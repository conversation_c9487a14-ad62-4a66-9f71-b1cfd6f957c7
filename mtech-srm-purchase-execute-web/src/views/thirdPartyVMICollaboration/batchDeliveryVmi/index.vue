<template>
  <!-- 批量创建送货单--第三方物流 vmi -->
  <div class="full-height vertical-flex-box">
    <div class="top-info flex-keep">
      <div class="header-box">
        <div class="middle-blank"></div>
        <!-- 右侧操作按钮 -->
        <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{
          $t('返回')
        }}</mt-button>
      </div>
      <div class="title">
        <span>{{ $t('批量创建送货单') }}</span>
      </div>
    </div>
    <!-- 列模板 -->
    <div class="flex-fit">
      <mt-template-page
        id="supply-plan-supplier-table-container"
        ref="templateRef"
        class="grid-wrap-not-paging"
        :template-config="componentConfig"
        @handleClickToolBar="handleClickToolBar"
        @cellEdit="cellEdit"
      >
      </mt-template-page>
    </div>
    <!-- 选择发货仓库弹框 -->
    <vmi-warehouse-dialog
      ref="vmiWarehouseDialog"
      @confirm="vmiWarehouseDialogConfirm"
    ></vmi-warehouse-dialog>
  </div>
</template>

<script>
import { gridDataSource } from './config/variable'
import { formatTableColumnData, verifyCreateMark } from './config/index'
import {
  Toolbar,
  ColumnDataDeliverySchedule,
  ColumnDataPurchaseOrder,
  ColumnDataJit
} from './config/constant'
import { TabIndex } from '../supplyPlanVmi/config/constant'
import vmiWarehouseDialog from '../supplyPlanVmi/components/vmiWarehouseDialog'
import { DialogActionType } from '../supplyPlanVmi/config/constant'
import { cloneDeep } from 'lodash'
export default {
  components: {
    vmiWarehouseDialog
  },
  data() {
    // 预创建订单数据
    const batchDeliveryVmiData = JSON.parse(localStorage.getItem('batchDeliveryVmiData')) || []
    // 预创建订单请求参数
    const batchDeliveryVmiParams = JSON.parse(localStorage.getItem('batchDeliveryVmiParams'))
    // 预创建订单 选择发货仓弹框 数据
    const batchDeliveryVmiFormData = JSON.parse(localStorage.getItem('batchDeliveryVmiFormData'))
    const lastTabIndex = JSON.parse(localStorage.getItem('lastTabIndex'))

    // 将预创建订单数据 设置到 表格中
    gridDataSource.length = 0
    batchDeliveryVmiData.forEach((item) => {
      gridDataSource.push(item)
    })

    const { type: pageType } = this.$route.query // 页面类型
    let column = []
    if (pageType == TabIndex.deliverySchedule) {
      // 交货计划
      column = ColumnDataDeliverySchedule
    } else if (pageType == TabIndex.purchaseOrder) {
      // 采购订单
      column = ColumnDataPurchaseOrder
    } else if (pageType == TabIndex.jit) {
      // jit
      column = ColumnDataJit
    }

    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      lastTabIndex, // 前一页面的 Tab index
      pageType, // 页面类型
      batchDeliveryVmiParams, // 预创建订单请求参数
      batchDeliveryVmiFormData, // 预创建订单 选择发货仓弹框 数据
      cacheGridData: gridDataSource,
      componentConfig: [
        {
          toolbar: Toolbar[pageType] || [],
          useToolTemplate: false, // 此项不使用预置的表格操作按钮
          useBaseConfig: false, // 此项不使用预置的表格操作按钮
          grid: {
            allowPaging: false, // 不分页
            lineSelection: 0, // 选项列
            lineIndex: 1, // 序号列
            frozenColumns: 1,
            columnData: formatTableColumnData({
              data: column
            }),
            dataSource: gridDataSource
          }
        }
      ]
    }
  },
  mounted() {},
  beforeDestroy() {
    // // 预创建订单数据
    // localStorage.removeItem("batchDeliveryVmiData");
    // // 预创建订单请求参数
    // localStorage.removeItem("batchDeliveryVmiParams");
    // // 预创建订单 选择发货仓弹框 数据
    // localStorage.removeItem("batchDeliveryVmiFormData");
    // localStorage.removeItem("lastTabIndex");
  },
  methods: {
    // ToolBar
    handleClickToolBar(args) {
      const { toolbar, grid } = args
      const selectRows = grid.getSelectedRecords()
      const selectRowIndexList = grid.getSelectedRowIndexes()
      console.log(selectRows, selectRowIndexList)
      const commonToolbar = [
        'TableImport',
        'Rematch',
        'Filter',
        'Refresh',
        'refreshDataByLocal',
        'filterDataByLocal',
        'resetDataByLocal',
        'Setting',
        'ReselectVmiWarehouse'
      ]
      if (selectRows.length === 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedId = []
      selectRows.forEach((item) => selectedId.push(item.id))

      if (toolbar.id === 'TableImport') {
        // 导入
        this.handleImport()
      } else if (toolbar.id === 'CreateDelivery') {
        // 提交创建送货单
        const { valid, createMark } = verifyCreateMark(selectRows)
        if (valid && createMark) {
          // 状态统一 && 都是可创建的状态
          this.handleCreateDelivery({ selectRows, selectRowIndexList })
        } else {
          this.$toast({
            content: this.$t('请选择可创建送货单的数据'),
            type: 'warning'
          })
        }
      } else if (toolbar.id === 'Rematch') {
        // 重新匹配
        this.handleRematch()
      } else if (toolbar.id === 'ReselectVmiWarehouse') {
        // 重新选择VMI仓
        this.reselectVmiWarehouse(selectRows[0])
      }
    },
    // 导入
    handleImport() {
      // TODO
      this.$toast({ content: this.$t('开发中'), type: 'warning' })
      if (this.pageType == TabIndex.purchaseOrder) {
        // 采购订单-创建送货单
        // this.TODO(params);
      } else if (this.pageType == TabIndex.deliverySchedule) {
        // 交货计划-创建送货单
        // this.TODO(params);
      } else if (this.pageType == TabIndex.jit) {
        // JIT-创建送货单
        // this.TODO(params);
      }
    },
    // 重新匹配
    handleRematch() {
      if (this.pageType === TabIndex.deliverySchedule) {
        // 供方收发货供货计划-vmi交货计划预创建送货单
        this.postVmiPlanPreCreate()
      } else if (this.pageType === TabIndex.jit) {
        // 供方收发货供货计划-JIT预创建送货单
        this.postVmiJitPreCreate()
      }
    },
    // 选择发货仓库 弹框 确定 数据校验通过时
    vmiWarehouseDialogConfirm(formData) {
      this.batchDeliveryVmiFormData = formData
      // 重新匹配
      this.handleRematch()
    },
    // 重新选择VMI仓
    reselectVmiWarehouse(selectRows) {
      this.$refs.vmiWarehouseDialog.dialogInit({
        title: this.$t('重新选择VMI仓库'),
        actionType: DialogActionType.Edit,
        selectData: this.batchDeliveryVmiFormData,
        data: selectRows
      })
    },
    // 提交创建送货单
    handleCreateDelivery(args) {
      const { selectRows, selectRowIndexList } = args
      const body = []
      selectRows.forEach((item) => {
        let _dataSourceItem = this.cacheGridData.find((data) => item.id === data.id)
        console.log(_dataSourceItem)
        body.push({
          id: item.id,
          num: item.presentDeliveryNum, // 此次发货数量
          supplierTenantId: item.supplierTenantId, // 供应商租户id
          remark: _dataSourceItem?.remark || '' //行备注
        })
      })

      if (this.pageType === TabIndex.purchaseOrder) {
        // 采购订单-创建送货单
        this.postVmiOrderCreate({
          body,
          selectRowIndexList
        })
      } else if (this.pageType === TabIndex.deliverySchedule) {
        // 交货计划-创建送货单
        this.postVmiPlanCreate({
          body,
          selectRowIndexList
        })
      } else if (this.pageType === TabIndex.jit) {
        // JIT-创建送货单
        this.postVmiJitCreate({
          body,
          selectRowIndexList
        })
      }
    },
    // 处理 提交创建送货单 后的逻辑
    handleCreateDeliveryAfter(args) {
      const { response, selectRowIndexList } = args
      const num = response.data || 0 // 创建的送货单数量
      this.$toast({
        content: this.$t(`成功创建${num}张送货单`),
        type: 'success'
      })
      // 移除行数据，更新 localStorage
      this.updateLocalStorageData({ selectRowIndexList })
      // 跳转到 送货单-列表-供方
      this.$router.push({
        path: '/purchase-execute/deliver-list-supplier-kt',
        query: {
          date: new Date().getTime()
        }
      })
    },
    // 移除行数据，更新 localStorage
    updateLocalStorageData(args) {
      const { selectRowIndexList } = args
      selectRowIndexList.forEach((itemIndex) => {
        gridDataSource.splice(itemIndex, 1)
      })
      localStorage.setItem('batchDeliveryVmiData', JSON.stringify(gridDataSource))
    },
    goBack() {
      localStorage.setItem('tabIndex', JSON.stringify(this.lastTabIndex))
      // 返回 供货计划-vmi
      this.$router.push({
        name: 'supply-plan-vmi'
      })
    },
    // 供方收发货供货计划-vmi订单创建送货单
    postVmiOrderCreate(args) {
      const { body, selectRowIndexList } = args
      this.apiStartLoading()
      const params = {
        query: {
          vmiWarehouseCode: this.batchDeliveryVmiFormData.vmiWarehouseCode,
          vmiWarehouseName: this.batchDeliveryVmiFormData.vmiWarehouseName
        },
        body
      }
      this.$API.thirdPartyVMICollaboration
        .postVmiOrderCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.handleCreateDeliveryAfter({
              response: res,
              selectRowIndexList
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-vmi交货计划创建送货单
    postVmiPlanCreate(args) {
      const { body, selectRowIndexList } = args
      this.apiStartLoading()
      const params = {
        // query: {
        vmiWarehouseCode: this.batchDeliveryVmiFormData?.vmiWarehouseCode,
        vmiWarehouseName: this.batchDeliveryVmiFormData?.vmiWarehouseName,
        // },
        deliveryList: body
      }
      this.$API.thirdPartyVMICollaboration
        .postVmiPlanCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.handleCreateDeliveryAfter({
              response: res,
              selectRowIndexList
            })
            this.$router.push({
              name: 'deliver-list-supplier-kt',
              query: {
                date: new Date().getTime()
              }
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-JIT创建送货单
    postVmiJitCreate(args) {
      const { body, selectRowIndexList } = args
      this.apiStartLoading()
      const params = {
        query: {
          vmiWarehouseCode: this.batchDeliveryVmiFormData?.vmiWarehouseCode,
          vmiWarehouseName: this.batchDeliveryVmiFormData?.vmiWarehouseName
        },
        body
      }
      this.$API.thirdPartyVMICollaboration
        .postVmiJitCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.handleCreateDeliveryAfter({
              response: res,
              selectRowIndexList
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-交货计划预创建送货单
    postVmiPlanPreCreate() {
      const params = {
        ...this.batchDeliveryVmiParams
      }
      this.apiStartLoading()
      this.$API.thirdPartyVMICollaboration
        .postVmiPlanPreCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const data = res?.data || []
            localStorage.setItem('batchDeliveryVmiData', JSON.stringify(data))
            // 将预创建订单数据 设置到 表格中
            gridDataSource.length = 0
            data.forEach((item) => {
              gridDataSource.push(item)
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-JIT预创建送货单
    postVmiJitPreCreate() {
      const params = {
        query: {
          vmiWarehouseCode: this.batchDeliveryVmiFormData.vmiWarehouseCode
        },
        body: this.batchDeliveryVmiParams.deliveryList
      }
      this.apiStartLoading()
      this.$API.thirdPartyVMICollaboration
        .postVmiJitPreCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const data = res?.data || []
            localStorage.setItem('batchDeliveryVmiData', JSON.stringify(data))
            // 将预创建订单数据 设置到 表格中
            gridDataSource.length = 0
            data.forEach((item) => {
              gridDataSource.push(item)
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 行编辑
    cellEdit(e) {
      // // 更新当前页 dataSource
      const _cacheGridData = cloneDeep(this.cacheGridData)
      this.cacheGridData = _cacheGridData?.map((item, index) => {
        console.log(e.index, index, e.value)
        if (e.index == index) {
          item.remark = e.value
        }
        return item
      })
      console.log(this.cacheGridData, '-----------------------------')
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.top-setting {
  padding: 20px 20px 20px 0;
  font-weight: 500;

  .accordion-title {
    font-size: 14px;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
    text-indent: 10px;
    border-left: 5px solid #00469c;
    margin-bottom: 20px;
    border-radius: 2px 0 0 2px;
  }
}

.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  // padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  .header-box {
    border-bottom: 1px solid #e6e9ed;
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
  }
}

/deep/ .grid-wrap-not-paging .e-control.e-grid .e-gridcontent {
  .e-frozenscrollbar.e-frozen-left-scrollbar {
    width: 50px !important; // 选择列宽度
  }
  .e-movablescrollbar .e-movablechild {
    width: 3730px !important; // 表格宽度 FIXME 注意 目前 订单供货计划 和 交货计划 的表格宽度都是相同的
  }
}
.title {
  margin: 24px 0px 8px 24px;
  padding-left: 8px;
  border-left: 4px solid #3369ac;
  font-weight: 600;
}
</style>
