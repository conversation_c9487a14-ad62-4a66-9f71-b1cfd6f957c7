import { i18n } from '@/main.js'

// 默认值、校验、级联
export const columnData = [
  {
    width: '150',
    field: 'rowNum',
    headerText: i18n.t('行号'),
    allowEditing: false, //此列不允许编辑
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return Number(+row.index + 1)
      }
    }
  },
  // {
  //   width: "150",
  //   field: "orderCode",
  //   headerText: i18n.t("关联采购订单号"),
  //   allowEditing: false,
  // },
  // {
  //   width: "150",
  //   field: "lineNo",
  //   headerText: i18n.t("关联采购订单行号"),
  //   allowEditing: false,
  // },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    allowEditing: false
  },
  // {
  //   width: "150",
  //   field: "insufficientDeliveryLimit",
  //   headerText: i18n.t("交货不足限定"),
  //   allowEditing: false,
  // },
  // {
  //   width: "150",
  //   field: "overDeliveryLimit",
  //   headerText: i18n.t("过量交货限定"),
  //   allowEditing: false,
  // },
  {
    width: '150',
    field: 'count',
    headerText: i18n.t('送货数量'),
    // editType: "numericedit", //默认编辑类型之number
    allowEditing: false
  },
  {
    width: '150',
    field: 'checkCount',
    headerText: i18n.t('实收数量'),
    // editType: "numericedit",
    edit: {
      params: {
        min: 0
      }
    }
  },
  {
    width: 0,
    field: 'itemUnit',
    headerText: i18n.t('单位'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'itemUnitDescription',
    headerText: i18n.t('单位'),
    allowEditing: false
  },
  // {
  //   width: "150",
  //   field: "batchCode",
  //   headerText: i18n.t("批次/卷号"),
  //   allowEditing: false,
  // },
  {
    width: '150',
    field: 'purchaseGroupName',
    headerText: i18n.t('采购组'),
    allowEditing: false
  },
  {
    width: 0,
    field: 'purchaseGroupCode',
    headerText: i18n.t('采购组'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('行备注'),
    allowEditing: false
  },
  // {
  //   width: "150",
  //   field: "takeNo",
  //   headerText: i18n.t("车号/船号"), //后端未返回
  //   allowEditing: false,
  // },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'confirmTime',
    headerText: i18n.t('接收日期'),
    allowEditing: false
  }
]
