// import Vue from "vue";
import { i18n } from '@/main.js'
import UTILS from '@/utils/utils.js'
import { MasterDataSelect } from '@/utils/constant'
import Vue from 'vue'
import { judgeFormatCodeName } from '@/utils/utils'

// 头视图
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'vmiOrderCode',
    headerText: i18n.t('VMI入库单号'),
    cellTools: [
      {
        id: 'confirmReceipt',
        title: i18n.t('确认收货'),
        icon: 'icon_table_batchacceptance',
        visibleCondition: (data) => {
          if (data.status == 1) return true
        }
      }
    ]
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '80',
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('已接收'), cssClass: 'col-active' },
        { value: 8, text: i18n.t('已完成'), cssClass: 'col-active' },
        { value: 9, text: i18n.t('已取消'), cssClass: 'col-active' }
      ]
    },
    searchOptions: {
      elementType: 'multi-select',
      operator: 'in',
      multiple: true,
      dataSource: [
        { label: i18n.t('新建'), value: 0 },
        { label: i18n.t('待确认'), value: 1 },
        { label: i18n.t('已接收'), value: 2 },
        { label: i18n.t('已完成'), value: 8 },
        { label: i18n.t('已取消'), value: 9 }
      ],
      fields: { text: 'label', value: 'value' }
    }
  },
  {
    width: '260',
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    searchOptions: MasterDataSelect.factorySupplierAddress,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.siteCode, data?.siteName)
    }
  },
  {
    width: '0',
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    ignore: true
  },
  {
    width: '260',
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    searchOptions: MasterDataSelect.supplierThird,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  },
  {
    width: '0',
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    ignore: true
  },
  {
    width: '260',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓'),
    searchOptions: MasterDataSelect.vmiThird,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.vmiWarehouseCode, data?.vmiWarehouseName)
    }
  },
  {
    width: '0',
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓名称'),
    ignore: true
  },
  {
    width: '150',
    field: 'vmiWarehouseType',
    headerText: i18n.t('VMI仓类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('SRM管理库存'),
        1: i18n.t('原厂'),
        2: i18n.t('WMS管理库存')
      }
    }
  },
  {
    width: '170',
    field: 'vmiWarehouseAddress',
    headerText: i18n.t('送货地址')
  },
  {
    width: '150',
    field: 'submitTime',
    headerText: i18n.t('制单日期'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    }
  },
  {
    width: '150',
    field: 'confirmTime',
    headerText: i18n.t('接收日期'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    }
  },
  {
    width: '80',
    field: 'createUserName',
    headerText: i18n.t('制单人')
  }
]
// 待审核
export const columnDataInfo2 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'vmiOrderCode',
    headerText: i18n.t('VMI入库单号'),
    cellTools: []
  },
  {
    width: '80',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('已接收'), cssClass: 'col-active' },
        { value: 8, text: i18n.t('已完成'), cssClass: 'col-active' },
        { value: 9, text: i18n.t('已取消'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '70',
    field: 'rowNum',
    headerText: i18n.t('行号')
  },
  {
    width: '115',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '395',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '85',
    field: 'purchaseGroupName',
    headerText: i18n.t('采购组'),
    searchOptions: {
      ...MasterDataSelect.businessCodeName,
      renameField: 'purchaseGroupCode'
    }
  },
  {
    width: '95',
    field: 'count',
    headerText: i18n.t('送货数量')
  },
  {
    width: '95',
    field: 'checkCount',
    headerText: i18n.t('收货数量'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `
        <div>
          <mt-input
          id="checkCount"
          v-model="data.checkCount"
          @change="change"
        ></mt-input>
        </div>
          `,
          data: function () {
            return {
              data: {}
            }
          },
          mounted() {},
          methods: {
            change(e) {
              this.data.checkCount = e
              this.$parent.$emit('cellEdit', {
                id: this.data.id,
                index: Number(this.data.index),
                key: 'checkCount',
                value: Number(this.data.checkCount)
              })
            }
          }
        })
      }
    }
  },
  {
    width: '65',
    field: 'itemUnitDescription',
    headerText: i18n.t('单位')
  },
  // {
  //   width: "105",
  //   field: "batchCode",
  //   headerText: i18n.t("批次/卷号"),
  // },
  {
    width: '65',
    field: 'remark',
    headerText: i18n.t('备注')
  },
  // {
  //   width: "140",
  //   field: "orderCode",
  //   headerText: i18n.t("关联采购订单号"),
  // },
  // {
  //   width: "150",
  //   field: "lineNo",
  //   headerText: i18n.t("关联采购订单行号"),
  // },
  {
    width: '95',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '230',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '115',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓编码')
  },

  {
    width: '120',
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓库名称')
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '115',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '170',
    field: 'vmiWarehouseAddress',
    headerText: i18n.t('送货地址')
  },
  {
    width: '100',
    field: 'qcReport',
    headerText: i18n.t('质检结果'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('不合格'), cssClass: '' },
        { value: 1, text: i18n.t('合格'), cssClass: '' },
        { value: null, text: '', cssClass: '' }
      ]
    }
  },
  {
    width: '110',
    field: 'unqualifiedTypes',
    headerText: i18n.t('不合格分类'),
    valueConverter: {
      type: 'map',
      map: [
        {
          text: i18n.t('包装不符'),
          value: '0',
          cssClass: ''
        },
        {
          text: i18n.t('其他不良'),
          value: '1',
          cssClass: ''
        },
        {
          text: i18n.t('外观不符'),
          value: '2',
          cssClass: ''
        },
        {
          text: i18n.t('内容不符'),
          value: '3',
          cssClass: ''
        },
        {
          text: i18n.t('材质不符'),
          value: '4',
          cssClass: ''
        },
        {
          text: i18n.t('标志不符'),
          value: '5',
          cssClass: ''
        },
        {
          text: i18n.t('尺寸不符'),
          value: '6',
          cssClass: ''
        },
        {
          text: i18n.t('认知不符'),
          value: '7',
          cssClass: ''
        },
        {
          text: i18n.t('性能不符'),
          value: '8',
          cssClass: ''
        },
        {
          text: i18n.t('错混料'),
          value: '9',
          cssClass: ''
        },
        {
          text: '',
          value: null,
          cssClass: ''
        }
      ]
    }
  },
  {
    width: '80',
    field: 'createUserName',
    headerText: i18n.t('制单人')
  },
  {
    width: '150',
    field: 'submitTime',
    headerText: i18n.t('制单时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    }
  },
  {
    width: '150',
    field: 'confirmTime',
    headerText: i18n.t('接收日期'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    }
  }
]
// 明细视图
export const columnDataInfo = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'vmiOrderCode',
    headerText: i18n.t('VMI入库单号'),
    cellTools: []
  },
  {
    width: '80',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('已接收'), cssClass: 'col-active' },
        { value: 8, text: i18n.t('已完成'), cssClass: 'col-active' },
        { value: 9, text: i18n.t('已取消'), cssClass: 'col-active' }
      ]
    },
    searchOptions: {
      elementType: 'multi-select',
      operator: 'in',
      multiple: true,
      dataSource: [
        { label: i18n.t('新建'), value: 0 },
        { label: i18n.t('待确认'), value: 1 },
        { label: i18n.t('已接收'), value: 2 },
        { label: i18n.t('已完成'), value: 8 },
        { label: i18n.t('已取消'), value: 9 }
      ],
      fields: { text: 'label', value: 'value' }
    }
  },
  {
    width: '70',
    field: 'rowNum',
    headerText: i18n.t('行号')
  },

  {
    width: '130',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '85',
    field: 'purchaseGroupCode',
    headerText: i18n.t('采购组'),
    searchOptions: {
      ...MasterDataSelect.businessCodeName
    },
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.purchaseGroupCode, data?.purchaseGroupName)
    }
  },
  {
    width: '0',
    field: 'purchaseGroupName',
    headerText: i18n.t('采购组名称'),
    ignore: true
  },
  {
    width: '95',
    field: 'count',
    headerText: i18n.t('送货数量')
  },
  {
    width: '95',
    field: 'checkCount',
    headerText: i18n.t('收货数量')
  },
  {
    width: '65',
    field: 'itemUnitDescription',
    headerText: i18n.t('单位')
  },
  // {
  //   width: "105",
  //   field: "batchCode",
  //   headerText: i18n.t("批次/卷号"),
  // },
  {
    width: '65',
    field: 'remark',
    headerText: i18n.t('备注')
  },
  // {
  //   width: "140",
  //   field: "orderCode",
  //   headerText: i18n.t("关联采购订单号"),
  // },
  // {
  //   width: "150",
  //   field: "lineNo",
  //   headerText: i18n.t("关联采购订单行号"),
  // },
  {
    width: '260',
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    searchOptions: MasterDataSelect.factorySupplierAddress,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.siteCode, data?.siteName)
    }
  },
  {
    width: '0',
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    ignore: true
  },
  {
    width: '260',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓'),
    searchOptions: MasterDataSelect.vmiThird,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.vmiWarehouseCode, data?.vmiWarehouseName)
    }
  },
  {
    width: '0',
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓名称'),
    ignore: true
  },
  {
    width: '260',
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    searchOptions: MasterDataSelect.supplierThird,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  },
  {
    width: '0',
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    ignore: true
  },
  {
    width: '170',
    field: 'vmiWarehouseAddress',
    headerText: i18n.t('送货地址')
  },
  {
    width: '100',
    field: 'qcReport',
    headerText: i18n.t('质检结果'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('不合格'), cssClass: '' },
        { value: 1, text: i18n.t('合格'), cssClass: '' },
        { value: null, text: '', cssClass: '' }
      ]
    }
  },
  {
    width: '110',
    field: 'unqualifiedTypes',
    headerText: i18n.t('不合格分类'),
    valueConverter: {
      type: 'map',
      map: [
        {
          text: i18n.t('包装不符'),
          value: '0',
          cssClass: ''
        },
        {
          text: i18n.t('其他不良'),
          value: '1',
          cssClass: ''
        },
        {
          text: i18n.t('外观不符'),
          value: '2',
          cssClass: ''
        },
        {
          text: i18n.t('内容不符'),
          value: '3',
          cssClass: ''
        },
        {
          text: i18n.t('材质不符'),
          value: '4',
          cssClass: ''
        },
        {
          text: i18n.t('标志不符'),
          value: '5',
          cssClass: ''
        },
        {
          text: i18n.t('尺寸不符'),
          value: '6',
          cssClass: ''
        },
        {
          text: i18n.t('认知不符'),
          value: '7',
          cssClass: ''
        },
        {
          text: i18n.t('性能不符'),
          value: '8',
          cssClass: ''
        },
        {
          text: i18n.t('错混料'),
          value: '9',
          cssClass: ''
        },
        {
          text: '',
          value: null,
          cssClass: ''
        }
      ]
    }
  },
  {
    width: '80',
    field: 'createUserName',
    headerText: i18n.t('制单人')
  },
  {
    width: '150',
    field: 'submitTime',
    headerText: i18n.t('制单时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    }
  },
  {
    width: '150',
    field: 'confirmTime',
    headerText: i18n.t('接收日期'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    }
  }
]
