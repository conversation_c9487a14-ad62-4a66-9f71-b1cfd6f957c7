<template>
  <!-- 第三方物流VWI协同-入库管理页-->
  <div class="full-height pt20">
    <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      v-show="tabIndex === 0"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <mt-template-page
      ref="templateRef1"
      :template-config="pageConfig1"
      v-show="tabIndex === 1"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @rowSelecting="rowSelecting"
      @cellEdit="cellEdit"
      @rowDeselected="rowDeselecting"
    ></mt-template-page>
    <mt-template-page
      ref="templateRef2"
      v-show="tabIndex === 2"
      :template-config="pageConfig2"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <!-- sourcing -->
  </div>
</template>

<script>
import { columnData, columnDataInfo, columnDataInfo2 } from './config/index.js'
import { download, getHeadersFileName } from '@/utils/utils'
import { cloneDeep } from 'lodash'

export default {
  components: {},
  data() {
    return {
      tabIndex: 0,
      tabSource: [
        {
          title: this.$t('头视图')
        },
        {
          title: this.$t('待接收')
        },
        {
          title: this.$t('明细视图')
        }
      ],
      pageConfig: [
        //配置tab
        //   头视图
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          useCombinationSelection: false,
          // title: this.$t("头视图"),
          toolbar: {
            //工具栏和右侧筛选
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'export1',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ], //新增  删除
              ['Filter', 'Refresh', 'Setting'] //筛选   刷新  设置
            ]
          },
          gridId: '6f88a69a-e3ab-4687-b63a-be0c4ff987db',
          grid: {
            columnData: columnData,
            dataSource: [
              {
                businessTypeName: 'w20211227001', //VMI领料单号
                status: 1, //状态
                plantCode: '630847', //工厂编号
                plantDescription: this.$t('TCL空调器有限公司生产工厂'), //工厂描述
                supplierCode: 'G89001', //原材料供应商编码
                supplierDescription: this.$t('广东克瑞达智能科技股份有限公司'), //供应商描述
                VMIWarehouseCode: '0100001', //VMI仓编码
                VMIWarehouseDescription: this.$t('VMI红兴物流芜湖威灵电机'), //VMI仓描述
                pickingSupplierCode: '2200101', //领料供应商编码
                pickingSupplierDescription: this.$t('广东新科科技股份有限公司'), //领料供应商描述
                shippingAddress: this.$t('深圳市福田区下梅林梅华路'), //送货地址
                preparationDate: '2021-12-28', //制单日期
                preparer: this.$t('许凯凯') //制单人
              },
              {
                businessTypeName: 'w20211227001', //VMI领料单号
                status: 0, //状态
                plantCode: '630847', //工厂编号
                plantDescription: this.$t('TCL空调器有限公司生产工厂'), //工厂描述
                supplierCode: 'G89001', //原材料供应商编码
                supplierDescription: this.$t('广东克瑞达智能科技股份有限公司'), //供应商描述
                VMIWarehouseCode: '0100001', //VMI仓编码
                VMIWarehouseDescription: this.$t('VMI红兴物流芜湖威灵电机'), //VMI仓描述
                pickingSupplierCode: '2200101', //领料供应商编码
                pickingSupplierDescription: this.$t('广东新科科技股份有限公司'), //领料供应商描述
                shippingAddress: this.$t('深圳市福田区下梅林梅华路'), //送货地址
                preparationDate: '2021-12-28', //制单日期
                preparer: this.$t('许凯凯') //制单人
              },
              {
                businessTypeName: 'w20211227001', //VMI领料单号
                status: '2', //状态
                plantCode: '630847', //工厂编号
                plantDescription: this.$t('TCL空调器有限公司生产工厂'), //工厂描述
                supplierCode: 'G89001', //原材料供应商编码
                supplierDescription: this.$t('广东克瑞达智能科技股份有限公司'), //供应商描述
                VMIWarehouseCode: '0100001', //VMI仓编码
                VMIWarehouseDescription: this.$t('VMI红兴物流芜湖威灵电机'), //VMI仓描述
                pickingSupplierCode: '2200101', //领料供应商编码
                pickingSupplierDescription: this.$t('广东新科科技股份有限公司'), //领料供应商描述
                shippingAddress: this.$t('深圳市福田区下梅林梅华路'), //送货地址
                preparationDate: '2021-12-28', //制单日期
                preparer: this.$t('许凯凯') //制单人
              },
              {
                businessTypeName: 'w20211227001', //VMI领料单号
                status: '3', //状态
                plantCode: '630847', //工厂编号
                plantDescription: this.$t('TCL空调器有限公司生产工厂'), //工厂描述
                supplierCode: 'G89001', //原材料供应商编码
                supplierDescription: this.$t('广东克瑞达智能科技股份有限公司'), //供应商描述
                VMIWarehouseCode: '0100001', //VMI仓编码
                VMIWarehouseDescription: this.$t('VMI红兴物流芜湖威灵电机'), //VMI仓描述
                pickingSupplierCode: '2200101', //领料供应商编码
                pickingSupplierDescription: this.$t('广东新科科技股份有限公司'), //领料供应商描述
                shippingAddress: this.$t('深圳市福田区下梅林梅华路'), //送货地址
                preparationDate: '2021-12-28', //制单日期
                preparer: this.$t('许凯凯') //制单人
              }
            ],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmi-receive-order/logistic-page-query',
              recordsPosition: 'data.records'
            },
            frozenColumns: 1
          }
        }
      ],
      pageConfig1: [
        // 待接收
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          useCombinationSelection: false,
          // title: this.$t("待接收"),
          toolbar: {
            //工具栏和右侧筛选
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'submit',
                  title: this.$t('确认')
                }
              ], //新增  删除
              ['Filter', 'Refresh', 'Setting'] //筛选   刷新  设置
            ]
          },
          grid: {
            columnData: columnDataInfo2,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmi-receive-order/logistic-item-page-query',
              recordsPosition: 'data.records',
              defaultRules: [
                {
                  field: 'status',
                  operator: 'equal',
                  value: 1
                }
              ],
              serializeList: (list) => {
                list.forEach((item) => {
                  item.checkCount = item.count
                })

                return list
              }
            },
            frozenColumns: 1
          }
        }
      ],
      pageConfig2: [
        // 明细视图

        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          useCombinationSelection: false,
          // title: this.$t("明细视图"),
          toolbar: {
            //工具栏和右侧筛选
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'export2',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ], //新增  删除
              ['Filter', 'Refresh', 'Setting'] //筛选   刷新  设置
            ]
          },
          gridId: '1e783de9-9c6f-46f1-9e23-9671c97dbbdc',
          grid: {
            columnData: columnDataInfo,
            dataSource: [
              {
                businessTypeName: 'w20211227001', //VMI退货单号
                status: 1, //状态
                lineNumber: '10', //行号
                materialCode: '630847', //物料编码
                materialDescription: '自攻螺钉 GB/T', //物料描述
                inventoryStatus: this.$t('合格库存'), //库存状态
                batch: 'SN0002', //批次/卷号
                returnQuantity: '500', //退货数量
                receivedQuantity: '500', //实收数量
                Company: this.$t('件'), //单位
                procurementGroup: '王国浩-塑料件', //采购组
                lineRemarks: this.$t('备注文本字段'), //行备注
                associatedPurchaseOrderNumber: 'D838940', //关联采购订单号
                associatedPurchaseOrderLineNumber: '10', //关联采购订单行号
                factoryNumber: '630847', //工厂编号
                plantDescription: this.$t('TCL空调器有限公司生产工厂'), //工厂描述
                supplierCode: '0100001', //供应商编码
                supplierDescription: this.$t('VMI红兴物流芜湖威灵电机'), //供应商描述
                VMIWarehouseCode: 'G89001', //VMI仓编码
                VMIWarehouseDescription: this.$t('广东惠利普智能科技股份有限公司'), //VMI仓描述
                shippingAddress: this.$t('深圳市福田区下梅林梅华路'), //送货地址
                preparationDate: '2021-12-27', //制单日期
                preparer: this.$t('许凯凯') //制单人
              }
            ],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmi-receive-order/logistic-item-page-query',
              recordsPosition: 'data.records'
            },
            frozenColumns: 1
          }
        }
      ],
      editData: [],
      addDialogShow: false,
      dialogData: null
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    },
    //编辑行内
    cellEdit(e) {
      const { id, index, key, value } = e
      if (id !== undefined) {
        // 如果是编辑行
        const editIndex = this.editData.findIndex((item) => item.id === id && item.key === key)
        if (editIndex >= 0) {
          // 更新编辑的数据
          this.editData[editIndex].value = value
        } else {
          // 保存编辑的数据，value 为空也存
          this.editData.push({
            id,
            key,
            index,
            value
          })
        }
      }
      console.log('cellEdit', this.editData)
    },
    //复选框事件
    rowSelecting(e) {
      if (e?.rowIndexes?.length < 1) return
      if (e.data instanceof Array) return
      // if (e?.data?.status != 2) {
      //   let mapArr = e.rowIndexes.filter((item) => {
      //     return item !== e.rowIndex;
      //   });
      //   if (!(e.data instanceof Array)) {
      //     console.log(e, mapArr);
      //     this.$nextTick(() => {
      //       this.$refs.templateRef
      //         .getCurrentUsefulRef()
      //         .ejsRef.selectRows(mapArr);
      //     });
      //   }
      //   return;
      // }
      //获取当前页所有的行
      let Obj = cloneDeep(
        this.$refs.templateRef1.getCurrentUsefulRef().ejsRef.getCurrentViewRecords()
      )
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.templateRef1.getCurrentUsefulRef().ejsRef.getSelectedRecords()
      )
      currentSelect.push(e.data)
      currentSelect = [...new Set(currentSelect)]
      if (Obj instanceof Array && Obj.length > 0) {
        let mapArr = []
        for (let i = 0; i < currentSelect.length; i++) {
          for (let j = 0; j < Obj.length; j++) {
            if (currentSelect[i]?.vmiOrderCode === Obj[j]?.vmiOrderCode) {
              mapArr.push(Obj[j])
            }
          }
        }
        mapArr = mapArr.map((item) => item.index)
        if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0 && mapArr.length > 0) {
          this.$refs.templateRef1.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
        }
        console.log(mapArr)
      }
    },
    //取消
    rowDeselecting(e) {
      if (e?.rowIndexes?.length < 1) return
      //获取当前页所有的行
      let Obj = cloneDeep(
        this.$refs.templateRef1.getCurrentUsefulRef().ejsRef.getCurrentViewRecords()
      )
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.templateRef1
          .getCurrentUsefulRef()
          .ejsRef.getSelectedRecords()
          .map((e) => e.id)
      )
      let mapArr = []
      Obj.filter((item, index) => {
        if (item.vmiOrderCode !== e.data.vmiOrderCode && currentSelect.includes(item.id)) {
          console.log(item)
          mapArr.push(index)
        }
      })
      console.log(e, mapArr)
      if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0) {
        this.$refs.templateRef1.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
      }
    },
    // 接口
    //   表格 toolbar 点击
    handleClickToolBar(e) {
      const { gridRef } = e

      if (e.toolbar.id === 'export1') {
        let obj = JSON.parse(
          sessionStorage.getItem('6f88a69a-e3ab-4687-b63a-be0c4ff987db')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnData.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        let rule =
          this.tabIndex === 0
            ? this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
            : this.tabIndex === 1
            ? this.$refs.templateRef1.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
            : this.tabIndex === 2
            ? this.$refs.templateRef2.getCurrentUsefulRef().pluginRef.queryBuilderRules
            : {}
        let params = {
          page: { current: 1, size: 1000 },
          rules: rule.rules || []
        }
        this.$store.commit('startLoading')
        this.$API.supplierCoordination.postLogisticOrderPrintNew(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
      if (e.toolbar.id === 'submit') {
        const _selectedData = gridRef.getMtechGridRecords()
        this.handleClickToolBarSubmit(_selectedData)
      }
      if (e.toolbar.id === 'export2') {
        let obj = JSON.parse(
          sessionStorage.getItem('1e783de9-9c6f-46f1-9e23-9671c97dbbdc')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnDataInfo.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        let rule =
          this.tabIndex === 0
            ? this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
            : this.tabIndex === 1
            ? this.$refs.templateRef1.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
            : this.tabIndex === 2
            ? this.$refs.templateRef2.getCurrentUsefulRef().pluginRef.queryBuilderRules
            : {}
        let params = {
          page: { current: 1, size: 1000 },
          rules: rule?.rules || []
        }
        this.$store.commit('startLoading')
        this.$API.supplierCoordination.postLogisticItemOrderPrintNew(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
    },
    handleClickToolBarSubmit(selectedData) {
      let _selectedData = cloneDeep(selectedData)
      if (_selectedData.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //没有编辑但是勾选的值
      let newarr = this.editData.map((item) => item.id)
      let incluArr = _selectedData.filter((item) => !newarr.includes(item.id))
      // incluArr.map((item) => {
      //   item.receiveQuantity = item.deliveryQuantity;
      // });
      //编辑的值替换勾选的值
      this.editData.forEach((item) => {
        let index = _selectedData.findIndex((e) => e.id == item.id)
        if (index > -1) {
          _selectedData[index] = item
        }
      })
      //勾选但未编辑的值 ---替换
      incluArr.forEach((item) => {
        let index = _selectedData.findIndex((e) => e.id == item.id)
        if (index > -1) {
          _selectedData[index] = item
        }
      })
      console.log(_selectedData)
      this.doSubmit(_selectedData)
    },
    doSubmit(e) {
      console.log(e)
      let params = {
        itemList: []
      }
      e.forEach((item) => {
        params.itemList.push({
          id: item.id,
          checkCount: item.value
        })
      })

      this.$API.supplierCoordination.postLogisticOrderConfirm(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功！'), type: 'success' })
          this.tabIndex === 0
            ? this.$refs.templateRef.refreshCurrentGridData()
            : this.tabIndex === 1
            ? this.$refs.templateRef1.refreshCurrentGridData()
            : this.tabIndex === 2
            ? this.$refs.templateRef2.refreshCurrentGridData()
            : {}
        } else {
          this.$toast({ content: res.msg, type: 'error' })
        }
      })
    },
    //单元格操作按钮点击
    handleClickCellTool(e) {
      let obj = {
        id: e.data.id,
        details: 0
      }
      if (e.tool.id === 'confirmReceipt') {
        this.redirectPage('purchase-execute/third-party-details', obj)
      }
    },
    // 跳转详情
    handleClickCellTitle(e) {
      let obj = {
        id: e.data.id,
        details: 1
      }
      if (e.field === 'vmiOrderCode' && e.tabIndex === 0) {
        this.redirectPage('purchase-execute/third-party-details', obj)
      }
    },
    // 路由跳转
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
