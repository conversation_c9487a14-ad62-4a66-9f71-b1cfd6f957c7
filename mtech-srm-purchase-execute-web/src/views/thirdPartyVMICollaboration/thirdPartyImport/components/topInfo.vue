<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="status mr20">
        {{ headerInfo.status | statusFormat }}
      </div>
      <div class="mr20">{{ $t('VMI入库单号：') }}{{ headerInfo.vmiOrderCode }}</div>
      <div class="infos mr20">{{ $t('制单人：') }}{{ headerInfo.createUserName }}</div>
      <div class="infos">
        {{ $t('制单时间：') }}
        <span v-if="headerInfo.submitTime && headerInfo.submitTime != 0"
          >{{ headerInfo.submitTime | dateFormat }} {{ headerInfo.submitTime | timeFormat }}</span
        >
        <span v-else>-</span>
      </div>
      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button
        css-class="e-flat"
        v-if="headerInfo.confirmStatus"
        :is-primary="true"
        @click="confirmBtn"
        >{{ $t('确认收货') }}</mt-button
      >

      <div class="sort-box" @click="doExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item :label="$t('工厂')">
          <mt-input
            v-model="headerInfo.siteName"
            :disabled="true"
            :placeholder="$t('工厂')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item :label="$t('供应商')">
          <mt-input v-model="headerInfo.supplierCode" :disabled="true" placeholder=""></mt-input>
        </mt-form-item>
        <!-- <mt-form-item :label="$t('供应商名称')">
          <mt-input
            v-model="headerInfo.supplierName"
            :disabled="true"
            :placeholder="$t('公司')"
          ></mt-input>
        </mt-form-item> -->

        <mt-form-item :label="$t('VMI仓')">
          <mt-input
            v-model="headerInfo.vmiWarehouseCode"
            :disabled="true"
            :placeholder="$t('VMI仓')"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item :label="$t('VMI仓名称')">
          <mt-input
            v-model="headerInfo.vmiWarehouseName"
            :disabled="true"
            :placeholder="$t('VMI仓名称')"
          ></mt-input>
        </mt-form-item> -->
        <mt-form-item :label="$t('送货地址')">
          <mt-input
            v-model="headerInfo.vmiWarehouseAddress"
            :disabled="true"
            :placeholder="$t('送货地址')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('VMI仓类型')">
          <mt-input v-model="vmiWarehouseTypeDisplay" :disabled="true"></mt-input>
        </mt-form-item>
        <!-- 供方备注 -->
        <mt-form-item class="full-width" :label="$t('备注')" :show-message="false">
          <mt-input
            v-model="headerInfo.remark"
            :disabled="true"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { StatusText, StatusCssClass } from '../config/constant'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isExpand: true,
      rules: {}
    }
  },
  computed: {
    vmiWarehouseTypeDisplay() {
      const _map = {
        0: this.$t('SRM管理库存'),
        1: this.$t('原厂'),
        2: this.$t('WMS管理库存')
      }
      return !_map[this.headerInfo.vmiWarehouseType]
        ? this.headerInfo.vmiWarehouseType
        : _map[this.headerInfo.vmiWarehouseType]
    }
  },
  mounted() {},
  filters: {
    dateFormat(value) {
      let str = ''
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }

      return str
    },
    timeFormat(value) {
      let str = ''
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'HH:MM:SS', value })
      } else {
        str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
      }

      return str
    },
    statusFormat(value) {
      if (!StatusText[value]) {
        return value
      } else {
        return StatusText[value]
      }
    }
  },
  methods: {
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 确认收货
    confirmBtn() {
      this.$emit('confirmBtn')
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    },
    // 状态 转对应的 css class
    statusToClass(value) {
      let cssClass = ''
      if (StatusCssClass[value]) {
        cssClass = StatusCssClass[value]
      }
      return cssClass
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: #6386c1;
      padding: 4px;
      background: #eef2f9;
      border-radius: 2px;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(33% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
