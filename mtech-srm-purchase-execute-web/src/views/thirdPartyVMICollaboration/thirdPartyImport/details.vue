// 供方-入库管理创建
<template>
  <div class="home">
    <top-info
      class="flex-keep"
      :header-info="headerInfo"
      @goBack="goBack"
      @confirmBtn="confirmBtn"
    ></top-info>
    <mt-tabs
      tab-id="hall-tab"
      :e-tab="false"
      :data-source="tabList"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <mt-data-grid
      v-show="tabIndex === 0"
      id="Grid1"
      class="pe-edit-grid edit-grid"
      :data-source="dataSource"
      :column-data="columnData"
      ref="dataGrid"
      :allow-paging="allowPaging"
      :edit-settings="editSettings"
      @actionBegin="actionBegin"
      :page-settings="pageSettings"
      @toolbarClick="toolbarClick"
      :toolbar="toolbar"
    ></mt-data-grid>
    <div v-show="tabIndex === 1">
      <logistics-info :logistics-data="logisticsData"></logistics-info>
    </div>
  </div>
</template>

<script>
import { columnData } from './config/details.js'
import $utils from '@/utils/utils'
export default {
  name: 'Home',
  components: {
    TopInfo: () => import('./components/topInfo.vue'),
    LogisticsInfo: () => import('./components/logisticsInfo.vue')
  },
  data() {
    return {
      dataSource: [
        {
          lineNumber: 1,
          normalNumber2: 44
        }
      ],
      columnData: columnData,
      allowPaging: true, // 产品要求：新增/编辑时，不分页；查看时要分页
      isEditStatus: false, // 正处于编辑状态
      actionFlag: '', // 点击按钮，可能是 保存草稿-save; 提交-submit;...
      headerInfo: {
        // status: 1,
        // createUserName: "jcs",
        // createTime: "2022-03-02",
        // factory: this.$t("京东工厂"),
        // supplierCode: "0001",
        // companyDescribe: this.$t("描述"),
        // warehouseCode: "0033333",
        // warehouseDescribe: this.$t("描述"),
        // remark: "以上备注信息全是bian",
        // confirmStatus: true, // 是否允许确认收货（头部有按钮、行内编辑可以修改实收数量）
      },
      pageSettings: {
        currentPage: 1,
        pageSize: 200,
        totalRecordsCount: 0,
        pageSizes: [10, 20, 50, 100, 200, 500, 1000]
      },
      tabList: [
        {
          title: this.$t('物料信息'),
          content: '0'
        },
        {
          title: this.$t('物流信息'),
          content: '1'
        }
      ],
      tabIndex: 0,
      // 物流信息数据
      logisticsData: {
        type: 1
      },
      toolbar: [], //编辑和取消、保存按钮
      editSettings: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      window.gridObj = this.$refs.dataGrid
    })
    this.getDetailData()
  },
  methods: {
    actionBegin(args) {
      if (
        this.$route.query.details == 'true' &&
        args.rowIndex === args.rowIndex &&
        args.rowIndex !== undefined
      ) {
        args.cancel = true //禁止行编辑
      }
      if (args.requestType == 'add' || args.requestType == 'beginEdit') {
        this.isEditStatus = true
      }
      if (args.requestType == 'refresh') {
        this.isEditStatus = false
      }
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    //保存
    confirmBtn() {
      const records = window.gridObj.ejsRef.getCurrentViewRecords()
      // 校验实收数量是否都大于零
      // const isGreaterThanZero = records.every((item) => item.checkCount > 0);
      const isTrue = records.every((item) => item.checkCount <= item.count)

      if (!isTrue) {
        // 数量为0的要提示
        this.$toast({
          content: this.$t('每行单据的实收数量不能大于送货数量，请检查'),
          type: 'error'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('收货'),
          message: this.$t('确认收货吗？')
        },
        success: () => {
          this.receiptConfirm(records)
        }
      })
    },
    // 表格上部操作
    toolbarClick(e) {
      // 自定义了一个行内新增按钮，为了给新增的这一行赋默认值
      if (e.item.id == 'Add') {
        // 新增
        this.$refs.dataGrid.ejsRef.addRecord()
      }
    },
    // tab切换
    handleSelectTab(index) {
      this.tabIndex = index
      // 切换时结束编辑
      this.$refs.dataGrid.ejsRef.endEdit()
    },
    getDetailData() {
      let obj = {
        id: this.$route.query.id
      }
      this.$API.purchaseCoordination.postBuyerWarehousingDetail(obj).then((res) => {
        if (res.code === 200) {
          this.headerInfo = {
            ...res.data,
            confirmStatus: false
          }
          console.log('获取到了详情了')
          // 如果是待确认状态，就可以编辑
          if (this.$route.query.details == '0' && res.data?.status == 1) {
            console.log(this.dataSource)

            this.headerInfo.confirmStatus = true
            this.toolbar = ['Cancel', 'Update']
            this.editSettings = {
              allowEditing: true, //是否允许编辑
              allowDeleting: true //是否允许删除
            }
          }
          console.log(this.dataSource)
          this.logisticsData =
            res.data.orderLogistic !== null
              ? res.data.orderLogistic[0]
              : {
                  type: 1
                }
          // this.logisticsData = res.data.orderLogistic[0];
          // this.logisticsData.type = res.data.orderLogistic[0].transportType
          // res.data.vmiOrderItemResponses.forEach((item) => {
          //   item.checkCount = item.count;
          // });
          console.log(this.dataSource)

          this.dataSource = res.data.vmiOrderItemResponses
          this.dataSource.forEach((item) => {
            if (item.confirmTime)
              item.confirmTime = $utils.formateTime(
                new Date(+res.data.confirmTime),
                'YYYY-mm-dd HH:MM:SS'
              )
            if (this.$route.query.details == '0' && res.data?.status == 1) {
              item.checkCount = item.count
            }
          })
          // code-name    头部以这种展示
          this.headerInfo.siteName = res.data.siteCode + '-' + res.data.siteName
          this.headerInfo.supplierCode = res.data.supplierCode + '-' + res.data.supplierName
          this.headerInfo.vmiWarehouseCode =
            res.data.vmiWarehouseCode + '-' + res.data.vmiWarehouseName
          console.log(this.dataSource)

          if (this.headerInfo.status === 1) {
            let obj = {
              orderId: this.$route.query.id,
              itemCodes: this.dataSource.map((item) => `${item.itemCode}`)
            }
            this.$API.purchaseCoordination.postPurchaseLimitQuery(obj).then((res) => {
              this.dataSource = this.dataSource.map((item) => {
                const { insufficientDeliveryLimit, overDeliveryLimit } = res.data.find(
                  (i) => item.itemCode === i.itemCode
                )

                return {
                  ...item,
                  insufficientDeliveryLimit,
                  overDeliveryLimit
                }
              })
            })
          }
          console.log(this.dataSource)
        }
      })
    },
    // 确认收货
    receiptConfirm(data) {
      let obj = {
        id: this.$route.query.id,
        itemList: [],
        operationType: 2
      }
      let objData = {}
      data.forEach((item) => {
        objData = {
          checkCount: '',
          id: ''
        }
        objData.checkCount = item.checkCount
        objData.id = item.id
        obj.itemList.push(objData)
      })

      this.$API.thirdPartyVMICollaboration.postBuyerWarehousingConfirm(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('收货成功'), type: 'success' })
          this.goBack()
        } else {
          this.$toast({
            content: this.$t('收货失败'),
            type: 'console.error();'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
$disabledBg: rgba(245, 245, 245, 1);
$requireddBg: rgba(237, 161, 51, 0.1);
.home {
  height: 100%;

  .pe-edit-grid {
    height: 80%;

    /deep/ .e-grid {
      height: 100%;
      .e-gridcontent {
        height: calc(100% - 90px);
        .e-content {
          height: 100% !important;
        }
      }
    }
  }

  .bgTransparent {
    background-color: transparent !important;
  }

  /deep/ .edit-grid {
    // 去掉 第一个单元格选中时的左边框样式，包括 列冻结后第一个单元格选中
    tr td:first-child::before {
      display: none;
    }
    .e-frozenheader,
    .e-frozenheader > .e-table,
    .e-frozencontent > .e-table {
      border-right: unset !important;
    }
    // 去掉 单元格的选中背景色
    td.e-active {
      @extend .bgTransparent;
    }
    // 去掉 行上悬浮时的单元格背景色
    tr:hover td {
      @extend .bgTransparent;
    }
    // 去掉冻结列 悬浮时，改变背景色问题
    &.e-gridhover .e-frozenhover,
    .e-detailcell,
    .e-detailindentcell,
    .e-detailrowcollapse,
    .e-detailrowexpand,
    .e-groupcaption,
    .e-indentcell,
    .e-recordpluscollapse,
    .e-recordplusexpand,
    .e-rowcell {
      @extend .bgTransparent;
    }

    // 编辑时
    .e-editedrow,
    .e-addedrow {
      .e-rowcell .e-control-wrapper {
        // 禁用的单元格背景色
        &.e-disabled,
        &.cell-disabled,
        .e-input[readonly]:not(.e-dropdownlist) {
          background: $disabledBg !important;
          cursor: not-allowed;
        }

        // 必填的单元格背景色
        &.isRequired .e-input {
          background: $requireddBg !important;
        }
      }
    }

    // 非编辑时
    tr td {
      // 禁用的单元格 样式
      &.e-rowcell.bg-grey,
      &.e-rowcell.bg-grey.e-updatedtd {
        background-color: $disabledBg !important;
        color: #9a9a9a !important;
        cursor: not-allowed;
        &.e-gridchkbox {
          @extend .bgTransparent;
          cursor: pointer;
        }
      }

      // 必填的单元格 样式
      &.e-rowcell.bg-red,
      &.e-rowcell.bg-red.e-updatedtd {
        background-color: $requireddBg !important;
      }
    }
  }
}
</style>
