<template>
  <div class="box">
    <!-- 下面的内容 -->
    <div class="main-box">
      <mt-form
        ref="ruleForm"
        :model="logisticsData"
        :rules="rules"
        :validate-on-rule-change="false"
      >
        <!-- 发货方式 -->
        <mt-form-item prop="transportType" :label="$t('发货方式')" style="width: 100%">
          <br />
          <mt-radio
            v-model.number="logisticsData.transportType"
            :data-source="ShippingTypeOptions"
            @change="onTransportTypeChange"
          ></mt-radio>
        </mt-form-item>
        <br />
        <!-- 快递-物流公司 -->
        <mt-form-item
          v-if="logisticsData.transportType == ShippingType.express"
          prop="thirdPartyLogisticsName"
          :label="$t('物流公司')"
        >
          <mt-select
            :data-source="companySelect"
            :disabled="!logisticsData.statusCreate"
            v-model="logisticsData.thirdPartyLogisticsName"
            :placeholder="$t('物流公司')"
          ></mt-select>
        </mt-form-item>
        <!-- 快递-物流单号 -->
        <mt-form-item
          v-if="logisticsData.transportType == ShippingType.express"
          prop="transportNum"
          :label="$t('物流单号')"
        >
          <mt-input
            v-model="logisticsData.transportNum"
            :disabled="!logisticsData.statusCreate"
            :placeholder="$t('物流单号')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-司机名称 -->
        <mt-form-item
          v-if="logisticsData.transportType == ShippingType.logistics"
          prop="driverName"
          :label="$t('司机名称')"
        >
          <mt-input
            v-model="logisticsData.driverName"
            :disabled="!logisticsData.statusCreate"
            :placeholder="$t('司机名称')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-司机联系方式 -->
        <mt-form-item
          v-if="logisticsData.transportType == ShippingType.logistics"
          prop="driverPhone"
          :label="$t('司机手机号')"
        >
          <mt-input
            v-model="logisticsData.driverPhone"
            :disabled="!logisticsData.statusCreate"
            :placeholder="$t('司机手机号')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-车牌 -->
        <mt-form-item
          v-if="logisticsData.transportType == ShippingType.logistics"
          prop="licensePlateNumber"
          :label="$t('车牌号')"
        >
          <mt-input
            v-model="logisticsData.licensePlateNumber"
            :disabled="!logisticsData.statusCreate"
            :placeholder="$t('车牌')"
          ></mt-input>
        </mt-form-item>
        <!-- 件数 -->
        <mt-form-item
          v-if="logisticsData.transportType == 1 || logisticsData.transportType == 2"
          prop="count"
          :label="$t('件数')"
        >
          <mt-input-number
            :min="0"
            v-model="logisticsData.count"
            :disabled="!logisticsData.statusCreate"
            :placeholder="$t('件数')"
          ></mt-input-number>
        </mt-form-item>

        <!-- 备注 -->
        <mt-form-item
          v-if="logisticsData.transportType == 1 || logisticsData.transportType == 2"
          class="full-width"
          prop="remark"
          :label="$t('备注')"
          :show-message="false"
        >
          <mt-input
            v-model="logisticsData.remark"
            :disabled="!logisticsData.statusCreate"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { ShippingTypeOptions, ShippingType } from '../config/constant'
import { RegExpMap } from '@/utils/constant'

export default {
  props: {
    logisticsData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const { phoneNumReg, integerReg, numberPlateReg } = RegExpMap
    // 快递-物流公司
    const thirdPartyLogisticsNameValidator = (rule, value, callback) => {
      if (this.logisticsData.transportType == ShippingType.express) {
        if (!value) {
          callback(new Error(this.$t('请选择物流公司')))
        } else {
          this.$refs.ruleForm.clearValidate(['thirdPartyLogisticsName'])
          callback()
        }
      } else {
        this.$refs.ruleForm.clearValidate(['thirdPartyLogisticsName'])
        callback()
      }
    }
    // 快递-物流单号
    const transportNumValidator = (rule, value, callback) => {
      if (this.logisticsData.transportType == ShippingType.express) {
        if (!value) {
          callback(new Error(this.$t('请输入物流单号')))
        } else {
          this.$refs.ruleForm.clearValidate(['transportNum'])
          callback()
        }
      } else {
        this.$refs.ruleForm.clearValidate(['transportNum'])
        callback()
      }
    }
    // 物流-司机姓名
    const driverNameValidator = (rule, value, callback) => {
      if (this.logisticsData.transportType == ShippingType.logistics) {
        if (!value) {
          callback(new Error(this.$t('请输入司机姓名')))
        } else {
          this.$refs.ruleForm.clearValidate(['driverName'])
          callback()
        }
      } else {
        this.$refs.ruleForm.clearValidate(['driverName'])
        callback()
      }
    }
    // 物流-司机联系方式
    const driverPhoneValidator = (rule, value, callback) => {
      if (this.logisticsData.transportType == ShippingType.logistics) {
        if (!value) {
          callback(new Error(this.$t('请输入司机手机号')))
        } else if (!phoneNumReg.test(value)) {
          callback(new Error(this.$t('请输入正确的手机号')))
        } else {
          this.$refs.ruleForm.clearValidate(['driverPhone'])
          callback()
        }
      } else {
        this.$refs.ruleForm.clearValidate(['driverPhone'])
        callback()
      }
    }
    // 物流-车牌
    const licensePlateNumberValidator = (rule, value, callback) => {
      if (this.logisticsData.transportType == ShippingType.logistics) {
        if (!value) {
          callback(new Error(this.$t('请输入车牌')))
        } else if (!numberPlateReg.test(value)) {
          callback(new Error(this.$t('请输入正确的车牌号')))
        } else {
          this.$refs.ruleForm.clearValidate(['licensePlateNumber'])
          callback()
        }
      } else {
        this.$refs.ruleForm.clearValidate(['licensePlateNumber'])
        callback()
      }
    }
    // 件数
    const countValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入件数')))
      } else if (!integerReg.test(value)) {
        callback(new Error(this.$t('请输入整数')))
      } else {
        this.$refs.ruleForm.clearValidate(['count'])
        callback()
      }
    }
    return {
      ShippingTypeOptions,
      ShippingType,
      isExpand: true,
      rules: {
        thirdPartyLogisticsName: [
          {
            required: true,
            validator: thirdPartyLogisticsNameValidator,
            trigger: 'blur'
          }
        ],
        transportNum: [
          {
            required: true,
            validator: transportNumValidator,
            trigger: 'blur'
          }
        ],
        driverName: [
          {
            required: true,
            validator: driverNameValidator,
            trigger: 'blur'
          }
        ],
        driverPhone: [
          {
            required: true,
            validator: driverPhoneValidator,
            trigger: 'blur'
          }
        ],
        licensePlateNumber: [
          {
            required: true,
            validator: licensePlateNumberValidator,
            trigger: 'blur'
          }
        ],
        count: [{ required: true, validator: countValidator, trigger: 'blur' }]
      },
      companySelect: []
    }
  },
  mounted() {
    this.getCompany()
  },
  filters: {},
  methods: {
    getCompany() {
      this.$API.supplierCoordination
        .postLogisticsCompany({ dictCode: 'logisticsCompany' })
        .then((res) => {
          if (res.data && res.data.length) {
            this.companySelect = res.data.map((item) => {
              return {
                text: item.itemName,
                value: item.itemCode,
                id: item.id
              }
            })
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.$nextTick(() => {
              this.logisticsData = {
                ...this.logisticsData,
                thirdPartyLogisticsName: this.logisticsData.thirdPartyLogisticsName // 这个实际上是物流公司的id
              }
            })
          }
        })
    },
    // 校验
    checkForm() {
      let validStatus = false
      this.$refs.ruleForm.validate((valid) => {
        validStatus = valid
      })
      return validStatus
    },
    // 发货方式 change
    onTransportTypeChange(value) {
      if (value == ShippingType.express) {
        // 快递配送
        this.logisticsData.driverName = null // 司机名称
        this.logisticsData.driverPhone = null // 司机联系方式
        this.logisticsData.licensePlateNumber = null // 车牌
        this.$refs.ruleForm.clearValidate(['driverName', 'driverPhone', 'licensePlateNumber'])
      } else if (value == ShippingType.logistics) {
        // 物流配送
        this.logisticsData.thirdPartyLogisticsName = null // 物流公司
        this.logisticsData.transportNum = null // 物流单号

        this.$refs.ruleForm.clearValidate(['thirdPartyLogisticsName', 'transportNum'])
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  padding: 20px 0 20px 20px;

  .main-box .mt-form-item {
    width: calc(20% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
  }
}
</style>
