<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="header-box-left" v-if="headerInfo.vmiOrderCode">
        <div :class="[statusToClass(headerInfo.status), 'mr20']">
          {{ headerInfo.status | statusFormat }}
        </div>
        <!-- 入库单号  -->
        <div class="mr20">{{ $t('VMI入库单号：') }}{{ headerInfo.vmiOrderCode }}</div>
        <!-- 制单人 -->
        <div class="infos mr20">{{ $t('制单人：') }}{{ headerInfo.createUserName }}</div>
        <!-- 制单时间 -->
        <div class="infos">
          {{ $t('制单时间：') }}{{ headerInfo.createTime | dateFormat }}
          {{ headerInfo.createTime | timeFormat }}
        </div>
      </div>

      <div class="middle-blank"></div>
      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button
        css-class="e-flat"
        v-if="headerInfo.preservationStatus && !headerInfo.isDisabled"
        :is-primary="true"
        @click="preservationBtn"
        >{{ $t('保存') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        v-if="headerInfo.cancelStatus && !headerInfo.isDisabled"
        :is-primary="true"
        @click="cancelBtn"
        >{{ $t('取消') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        v-if="headerInfo.submitStatus && !headerInfo.isDisabled"
        :is-primary="true"
        @click="submitBtn"
        >{{ $t('提交') }}</mt-button
      >
      <div class="sort-box" @click="doExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>
    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <!-- 工厂 -->
        <mt-form-item prop="siteCode" :label="$t('工厂')">
          <mt-input v-if="headerInfo.isDisabled" v-model="site" disabled></mt-input>

          <mt-select
            v-else
            v-model="headerInfo.siteCode"
            :disabled="headerInfo.isDisabled || headerInfo.hasDetailRow"
            :placeholder="$t('请选择工厂')"
            @change="factoryChange"
            :data-source="factorySelect"
            :allow-filtering="true"
            filter-type="Contains"
            @focus="focusSite('site')"
          ></mt-select>
        </mt-form-item>

        <!-- 供应商 -->
        <mt-form-item prop="supplierCode" :label="$t('供应商')">
          <mt-input v-if="headerInfo.isDisabled" v-model="supplier" disabled></mt-input>
          <mt-select
            v-else
            v-model="headerInfo.supplierCode"
            :disabled="headerInfo.isDisabled || headerInfo.hasDetailRow"
            :placeholder="$t('请选择供应商')"
            :data-source="supplierSelect"
            @change="supplierChange"
            :allow-filtering="true"
            filter-type="Contains"
            @focus="focusSupplier('supplier')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="vmiWarehouseCode" :label="$t('VMI仓')">
          <mt-input v-if="headerInfo.isDisabled" v-model="vmiWarehouse" disabled></mt-input>
          <mt-select
            v-else
            v-model="headerInfo.vmiWarehouseCode"
            :disabled="headerInfo.isDisabled || headerInfo.hasDetailRow"
            :placeholder="$t('VMI仓')"
            :data-source="warehouseSelect"
            :allow-filtering="true"
            filter-type="Contains"
            @focus="focusWarehouse('warehouse')"
            @change="warehouseChange"
          ></mt-select>
        </mt-form-item>
        <!-- 送货地址 -->
        <mt-form-item prop="vmiWarehouseAddress" :label="$t('送货地址')">
          <mt-input
            v-model="headerInfo.vmiWarehouseAddress"
            :disabled="headerInfo.isDisabled || headerInfo.hasDetailRow"
            :placeholder="$t('送货地址')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('VMI仓类型')">
          <mt-input v-model="vmiWarehouseTypeDisplay" :disabled="true"></mt-input>
        </mt-form-item>
        <!-- 备注 -->
        <mt-form-item class="full-width" prop="remark" :label="$t('备注')" :show-message="false">
          <mt-input
            v-model="headerInfo.remark"
            :disabled="headerInfo.isDisabled"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { StatusText, StatusCssClass, DeliveryTypeOptions } from '../config/constant'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { utils } from '@mtech-common/utils'
export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      DeliveryTypeOptions,
      isExpand: true,
      factorySelect: [], //工厂getSelect
      supplierSelect: [], //供应商
      warehouseSelect: [], //vmi仓接口
      requestUrl: {
        pre: 'thirdPartyVMICollaboration',
        url: 'postthirdPartyFactorySelectList'
      },
      parameterValue: '',
      dataLimit: 20,
      labelShowObj: {},
      rules: {}
    }
  },
  computed: {
    site() {
      return `${this.headerInfo?.siteCode}-${this.headerInfo?.siteName}`
    },

    supplier() {
      return `${this.headerInfo?.supplierCode}-${this.headerInfo?.supplierName}`
    },

    vmiWarehouse() {
      return `${this.headerInfo?.vmiWarehouseCode}-${this.headerInfo?.vmiWarehouseName}`
    },
    vmiWarehouseTypeDisplay() {
      const _map = {
        0: this.$t('SRM管理库存'),
        1: this.$t('原厂'),
        2: this.$t('WMS管理库存')
      }
      return !_map[this.headerInfo.vmiWarehouseType]
        ? this.headerInfo.vmiWarehouseType
        : _map[this.headerInfo.vmiWarehouseType]
    }
  },
  mounted() {
    this.getFactoryList() //工厂
    this.getSelect('supplier') //供应商
    this.getDatalist = utils.debounce(this.getDatalist, 300)
    if (!this.headerInfo.isDisabled) {
      this.rules = {
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        supplierName: [
          {
            required: true,
            message: this.$t('请选择原材料供应商'),
            trigger: 'blur'
          }
        ],
        vmiWarehouseAddress: [
          {
            required: true,
            message: this.$t('请输入送货地址'),
            trigger: 'blur'
          }
        ],
        vmiWarehouseCode: [
          {
            required: true,
            message: this.$t('请输入送货地址'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  filters: {
    dateFormat(value) {
      let str = ''
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }

      return str
    },
    timeFormat(value) {
      let str = ''
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'HH:MM:SS', value })
      } else {
        str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
      }

      return str
    },
    statusFormat(value) {
      if (!StatusText[value]) {
        return value
      } else {
        return StatusText[value]
      }
    }
  },
  methods: {
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // saveOk() {
    //   this.$emit("saveOk");
    // },
    // confirmOK() {
    //   this.$emit("confirmOK");
    // },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    },
    // 接受
    preservationBtn() {
      this.$emit('preservationBtn')
    },
    // 取消
    cancelBtn() {
      this.$emit('cancelBtn')
    },
    // 确认
    submitBtn() {
      this.$emit('submitBtn')
    },
    // 状态 转对应的 css class
    statusToClass(value) {
      let cssClass = ''
      if (StatusCssClass[value]) {
        cssClass = StatusCssClass[value]
      }
      return cssClass
    },
    // 切换工厂时
    factoryChange(val) {
      this.headerInfo.siteName = val.itemData.text.split('-')[0]
      this.$nextTick(() => {
        // this.getSelect("warehouse", val.itemData.value);
        this.getVmiWarehouse()
      })
    },
    // 切换供应商时
    supplierChange(val) {
      this.headerInfo.supplierName = val.itemData.label
      this.$nextTick(() => {
        this.getVmiWarehouse()
        // this.getSelect("warehouse", val.itemData.value);
      })
    },
    // 查询VMI仓接口 - 新
    getVmiWarehouse() {
      const params = {
        defaultRules: [
          {
            field: 'rel.site_code',
            operator: 'equal',
            value: this.headerInfo.siteCode
          },
          {
            field: 'rel.supplier_code',
            operator: 'equal',
            value: this.headerInfo.supplierCode
          },
          {
            field: 'base.vmiWarehouse_type',
            operator: 'in',
            value: [0, 2]
          }
        ]
      }
      this.$API.supplierCoordination.getVmiWarehouseThird(params).then((res) => {
        if (res.code === 200 && res.data.length > 0) {
          this.warehouseSelect = res.data.reduce((pre, now) => {
            pre.push({
              text: now.vmiWarehouseCode + '-' + now.vmiWarehouseName,
              value: now.vmiWarehouseCode,
              label: now.vmiWarehouseName,
              ...now
            })
            return pre
          }, [])
        }
      })
    },
    // 获取工厂接口
    getFactoryList() {
      const params = {
        defaultRules: [
          {
            field: 'base.vmiWarehouse_type',
            operator: 'in',
            value: [0, 2]
          }
        ]
      }
      this.$API.supplierCoordination.getVmiWarehouseThird(params).then((res) => {
        if (res.code === 200 && res.data.length > 0) {
          let newObj = {}
          this.factorySelect = res.data.reduce((pre, now) => {
            newObj[now.siteCode]
              ? ''
              : (newObj[now.siteCode] = pre.push({
                  text: now.siteCode + '-' + now.siteName,
                  value: now.siteCode,
                  label: now.siteName,
                  ...now
                }))
            return pre
          }, [])
        }
      })
    },
    // vmi仓切换时
    warehouseChange(val) {
      this.headerInfo.vmiWarehouseName = val.itemData.label
      this.headerInfo.vmiWarehouseType = val.itemData.vmiWarehouseType
      this.getWarehouseAddress(val.itemData.value)
    },
    // 根据vmi仓查出地址
    getWarehouseAddress(obj) {
      this.$API.thirdPartyVMICollaboration
        .postWarehouseAddressTenantId({ code: obj })
        .then((res) => {
          if (res.code === 200) {
            this.$set(this.headerInfo, 'vmiWarehouseAddress', res.data.vmiWarehouseAddress)
          }
        })
    },
    // 获取工厂列表接口
    getSelect(data) {
      // 工厂、供应商、VMI 都数据过滤 第三方物流
      let obj = {
        resultType: data,
        vmiWarehouseType: 0
      }
      // 如果是工厂/供应商改变，需要重新获取VMI。并且这俩都得有值
      if (data == 'warehouse') {
        if (this.headerInfo.siteCode && this.headerInfo.supplierCode) {
          obj.siteCode = this.headerInfo.siteCode
          obj.supplierCode = this.headerInfo.supplierCode
        } else {
          return
        }
      }
      this.$API.thirdPartyVMICollaboration.postthirdPartyFactorySelectList(obj).then((res) => {
        if (res.code === 200 && res.data.length > 0) {
          if (data === 'site') {
            // 工厂
            this.factorySelect = res.data.reduce((pre, now) => {
              pre.push({
                text: now.siteName + '-' + now.siteCode,
                value: now.siteCode,
                ...pre
              })
              return pre
            }, [])
          } else if (data === 'supplier') {
            let newObj = {}
            // 供应商
            this.supplierSelect = res.data.reduce((pre, now) => {
              newObj[now.supplierCode]
                ? ''
                : (newObj[now.supplierCode] = pre.push({
                    text: now.supplierCode + '-' + now.supplierName,
                    value: now.supplierCode,
                    label: now.supplierName,
                    ...now
                  }))
              return pre
            }, [])
          } else {
            // 仓库
            this.warehouseSelect = res.data.reduce((pre, now) => {
              pre.push({
                text: now.vmiWarehouseCode + '-' + now.vmiWarehouseName,
                value: now.vmiWarehouseCode,
                label: now.vmiWarehouseName,
                ...pre
              })
              return pre
            }, [])
          }
        }
      })
    },
    // 获取焦点时
    focusSite(e) {
      this.parameterValue = e
      this.labelShowObj = {
        code: 'siteCode',
        name: 'siteName'
      }
    },
    focusWarehouse(e) {
      this.parameterValue = e
    },
    focusSupplier(e) {
      this.parameterValue = e
    },
    // 搜索
    getDatalist(e = { text: '' }) {
      let than = this
      let params = {
        resultType: this.parameterValue,
        keyword: e.text
      }
      // 如果是工厂改变，过滤仓库类型为 第三方物流
      if (than.parameterValue == 'warehouse') {
        params.vmiWarehouseType = 0
      }
      // dataLimit 限制返回条数
      this.$API[this.requestUrl.pre][this.requestUrl.url](params).then((res) => {
        let dataList = []
        if (than.parameterValue === 'site') {
          dataList = res.data.reduce((pre, now) => {
            pre.push({
              text: now.siteName + '-' + now.siteCode,
              value: now.siteCode,
              ...pre
            })
            return pre
          }, [])
        } else if (than.parameterValue == 'supplier') {
          dataList = res.data.reduce((pre, now) => {
            pre.push({
              text: now.supplierName,
              value: now.supplierCode,
              ...pre
            })
            return pre
          }, [])
        } else if (than.parameterValue === 'warehouse') {
          dataList = res.data.reduce((pre, now) => {
            pre.push({
              text: now.vmiWarehouseCode + '-' + now.vmiWarehouseName,
              value: now.vmiWarehouseCode,
              ...pre
            })
            return pre
          }, [])
        }
        this.$nextTick(() => {
          if (e.updateData && typeof e.updateData == 'function') {
            e.updateData(dataList)
          }
        })

        if (res.total > this.dataLimit) {
          this.$toast({
            content: this.$t('搜索结果较多，请再输入更精确的查询条件'),
            type: 'warning'
          })
        }

        this.selectVal = this.initVal
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .header-box-left {
      height: 50px;
      display: flex;
      align-items: center;
    }
    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(33% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
