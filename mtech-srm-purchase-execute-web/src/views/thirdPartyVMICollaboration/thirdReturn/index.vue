<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { columnData, columnDataInfo } from './config/index.js'
export default {
  components: {
    // addDialog: require("./components/addDialog.vue").default,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  data() {
    return {
      downTemplateParams: {
        flag: 0
      }, // 下载模板参数
      requestUrls: {
        templateUrlPre: 'supplierCoordination',
        templateUrl: 'vmiReturnedOrderExcelNew',
        uploadUrl: 'vmiReturnedOrderImportNew'
      }, // 上传下载接口地址
      pageConfig: [
        //配置tab
        //   头视图
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          useCombinationSelection: false,
          title: this.$t('头视图'),
          toolbar: {
            //工具栏和右侧筛选
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'newlyBuild',
                  title: this.$t('新建VMI退货单'),
                  icon: 'icon_solid_Createorder',
                  permission: ['O_02_1109']
                },
                {
                  id: 'delete',
                  title: this.$t('删除'),
                  icon: 'icon_outline_Delete',
                  permission: ['O_02_1110']
                },
                {
                  id: 'upload',
                  icon: 'icon_solid_Import',
                  title: this.$t('导入')
                },
                {
                  id: 'synchronous',
                  icon: 'icon_table_restart',
                  title: this.$t('同步WMS')
                },
                {
                  id: 'print',
                  icon: 'icon_table_print',
                  title: this.$t('打印')
                }
              ], //新增  删除
              ['Filter', 'Refresh', 'Setting'] //筛选   刷新  设置
            ]
          },
          grid: {
            columnData: columnData,
            asyncConfig: {
              url: this.$API.thirdPartyVMICollaboration.postVMIReturnLogisticQuery, //第三方退货接口
              rules: [
                {
                  field: 'vmi_order_type',
                  // type:"string",
                  operator: 'equal',
                  value: 3
                }
              ]
            },
            frozenColumns: 1
          }
        },
        // 明细视图
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          useCombinationSelection: false,
          title: this.$t('明细视图'),
          toolbar: {
            //工具栏和右侧筛选
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'export1',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ], //新增  删除
              ['Filter', 'Refresh', 'Setting'] //筛选   刷新  设置
            ]
          },
          grid: {
            columnData: columnDataInfo,
            asyncConfig: {
              url: this.$API.thirdPartyVMICollaboration.postVMIReturnLogisticQueryDetail, //第三方明细视图接口,
              rules: [
                {
                  field: 'vmi_order_type',
                  // type:"string",
                  operator: 'equal',
                  value: 3
                }
              ]
            },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },
  methods: {
    //   表格 toolbar 点击
    handleClickToolBar(e) {
      if (e.toolbar.id == 'upload') {
        this.showUploadExcel(true)
      }
      if (e.tabIndex === 0 && e.toolbar.id === 'newlyBuild') {
        // 创建VMI退货单
        // this.redirectPage("purchase-execute/third-return-details", {});
        this.$router.push({
          path: `/purchase-execute/third-return-details`,
          query: {}
        })
      } else if (e.toolbar.id === 'delete') {
        // VMI领料单-批量删除
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('删除'),
            message: this.$t('确认删除吗？')
          },
          success: () => {
            let ids = _selectRows.map((item) => item.id)
            this.$API.thirdPartyVMICollaboration
              .postthirdReturnDelete({ ids })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('删除成功'),
                    type: 'success'
                  })
                  // 刷新当前 Grid
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
              .catch(() => {
                this.$toast({
                  content: this.$t('删除失败,只能是新建状态下可以删除'),
                  type: 'error'
                })
              })
          }
        })
      } else if (e.toolbar.id === 'synchronous') {
        let _selectRecords = e.grid.getSelectedRecords()
        if (_selectRecords.length <= 0 || _selectRecords.length > 1) {
          this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
          return
        }
        this.synchronousWms(_selectRecords[0])
      } else if (e.toolbar.id === 'refreshDataByLocal') {
        // 调用刷新方法
      } else if (e.toolbar.id === 'export1') {
        let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let obj = JSON.parse(
          sessionStorage.getItem('0000e9f9-be2e-4b9f-8d7d-7088e39e4737')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnDataInfo.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        let params = {
          page: { current: 1, size: 10000 },
          rules: rule.rules || []
        }
        params.rules = [
          ...params.rules,
          {
            field: 'vmi_order_type',
            // type:"string",
            operator: 'equal',
            value: 3
          }
        ]
        this.$store.commit('startLoading')
        this.$API.thirdPartyVMICollaboration.postthirdReturnExport(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
      } else if (e.toolbar.id === 'print') {
        let obj = {
          idList: []
        }

        e.gridRef.getMtechGridRecords().map((item) => {
          obj.idList.push(item.id)
        })
        let _selectRecords = e.grid.getSelectedRecords()
        if (_selectRecords.length <= 0 || _selectRecords.length > 1) {
          this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
          return
        }
        this.$API.purchaseCoordination.thirdVmiReturnedOrderPrint(obj).then((res) => {
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(
            new Blob([content], { type: 'text/html;charset=utf-8' })
          )
          // window.open(this.pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
          return
        })
      }
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = [] // 清空数据
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
      this.refreshColumns()
    },
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    //单元格操作按钮点击
    handleClickCellTool(e) {
      if (e.tool.id === 'Receive') {
        this.$dialog({
          data: {
            title: this.$t('提交'),
            message: this.$t('是否确认提交?')
          },
          success: () => {
            this.Receive(e.data.id)
          }
        })
      } else if (e.tool.id === 'Cancel') {
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消吗？')
          },
          success: () => {
            this.cancel(e.data.id)
          }
        })
      } else if (e.tool.id === 'Delete') {
        this.$dialog({
          data: {
            title: this.$t('删除'),
            message: this.$t('确认删除吗？')
          },
          success: () => {
            this.Delete(e.data.id)
          }
        })
      }
    },
    // 提交
    Receive(data) {
      let obj = {
        ids: [data]
      }
      this.$API.thirdPartyVMICollaboration.postthirdReturnSubmit(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          // 刷新当前 Grid
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('提交失败'), type: 'error' })
        }
      })
    },
    // 取消
    cancel(data) {
      let obj = {
        id: data
      }
      this.$API.thirdPartyVMICollaboration.postthirdReturnCancel(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('取消成功'), type: 'success' })
          // 刷新当前 Grid
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('取消失败'), type: 'error' })
        }
      })
    },
    // 删除
    Delete(data) {
      let obj = {
        ids: [data]
      }
      this.$API.thirdPartyVMICollaboration
        .postthirdReturnDelete(obj)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('删除成功'), type: 'success' })
            // 刷新当前 Grid
            this.$refs.templateRef.refreshCurrentGridData()
            // }else{
            //   this.$toast({ content: '删除失败', type: "error" });
          }
        })
        .catch(() => {
          this.$toast({ content: this.$t('删除失败'), type: 'error' })
        })
    },
    // 同步WMS
    synchronousWms(data) {
      if (data.wmsSyncStatus === 1) {
        this.$toast({
          content: this.$t('入库单已成功同步WMS，无需重复同步'),
          type: 'warning'
        })
        return
      }
      this.$API.purchaseCoordination.returnSynchronousWms({ id: data.id }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('同步成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('同步失败'), type: 'warning' })
        }
      })
    },
    // 点击单元格 标题
    handleClickCellTitle(e) {
      let obj = {
        tabIndex: '',
        status: '',
        id: ''
      }
      if (e.tabIndex === 0) {
        obj.tabIndex = 0
      } else {
        obj.tabIndex = 1
      }
      if (e.field === 'vmiOrderCode') {
        obj.status = e.data.status
        obj.id = e.data.id ?? e.data.vmiOrderId
        this.$router.push({
          path: `/purchase-execute/third-return-details`,
          query: {
            ...obj
          }
        })
        // this.redirectPage("purchase-execute/third-return-details", obj);
      }
    },
    // 路由跳转
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    }
  }
}
</script>

<style style="scss" scoped>
.full-height {
  height: 100%;
}
</style>
