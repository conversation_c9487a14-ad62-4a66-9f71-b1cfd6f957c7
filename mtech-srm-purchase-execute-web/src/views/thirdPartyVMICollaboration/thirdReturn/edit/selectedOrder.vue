<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="$t('输入订单号模糊查询')"
      :allow-filtering="true"
      :filtering="purOrderQueryOrder"
      @change="selectChange"
      :disabled="disabledStatus"
    >
    </mt-select>
  </div>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      dataSource: [],
      fields: { text: 'label', value: 'value' },
      disabledStatus: false
    }
  },
  mounted() {
    if (this.$route.query.create === 'false') this.disabledStatus = true
    this.$bus.$on('itemCodeChange', (itemCode) => {
      this.data.itemCode = itemCode
    })
  },
  methods: {
    purOrderQueryOrder(value) {
      // 判断没有选择物料编码不能选  关联采购单号
      if (!this.data.itemCode) {
        this.$toast({
          content: this.$t('请选择物料编码'),
          transportType: 'error'
        })
        return
      }
      console.log(value, '-=')
      let params = {
        condition: 'and',
        page: {
          current: 1,
          size: 50
        },
        defaultRules: [
          {
            label: this.$t('采购订单号'),
            field: 'orderCode',
            type: 'string',
            operator: 'contains',
            value: (value && value.text) || ''
          },
          {
            label: this.$t('工厂编码'),
            field: 'siteCode',
            type: 'string',
            operator: 'equal',
            value: this.data.siteCode
          },
          {
            label: this.$t('供应商编码'),
            field: 'supplierCode',
            type: 'string',
            operator: 'equal',
            value: this.data.supplierCode
          },
          {
            label: this.$t('vmi仓编码'),
            field: 'vmiWarehouseCode',
            type: 'string',
            operator: 'equal',
            value: this.data.vmiWarehouseCode
          },
          {
            label: this.$t('物料编码'),
            field: 'itemCode',
            type: 'string',
            operator: 'equal',
            value: this.data.itemCode
          }
        ]
      }
      this.$API.thirdPartyVMICollaboration
        .postthirdQueryThirdPartyLogisticsPO(params)
        .then((res) => {
          let orderOptions = res.data.records
            .map((item) => ({
              label: item,
              value: item
            }))
            .filter((item) => item.value) //过滤一下空值
          this.dataSource = orderOptions
        })
    },
    selectChange(value) {
      const code = value?.itemData?.value
      if (code) {
        //请求采购订单行
        this.$API.vmi.getByOrder({ code }).then((res) => {
          const orderLineData = res.data.reduce((pre, now) => {
            pre.push({
              label: now.itemNo,
              value: now.itemNo,
              ...now
            })
            return pre
          }, [])
          this.$bus.$emit('getOrderLineData', orderLineData)
        })
      }
    }
  }
}
</script>

<style scoped lang="scss"></style>
