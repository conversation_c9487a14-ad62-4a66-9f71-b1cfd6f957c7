<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      @change="fieldChange"
    >
    </mt-select>
  </div>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      dataSource: [
        {
          text: this.$t('合格库存'),
          value: 0
        },
        // {
        //   text: this.$t("待检库存"),
        //   value: 1,
        // },
        {
          text: this.$t('不合格库存'),
          value: 3
        }
      ],
      itemInfo: null // 当前选中行的物料信息
    }
  },
  mounted() {
    // console.log(this.data,"444455");
    // this.dataSources = this.data.materialList;
    this.fieldName = this.data.column.field

    this.itemInfo = this.data.itemInfo ? JSON.parse(this.data.itemInfo) : this.data.vmiStockResponse // 默认取当前行的数据，如果切换了物料就从物料数据中获取
    this.$bus.$on('itemInfoChange', (value) => {
      this.itemInfo = value ? JSON.parse(value) : null
    })
  },
  methods: {
    fieldChange(e) {
      let sourceStockType = e.itemData.value
      let _key =
        sourceStockType == 0
          ? 'count'
          : sourceStockType == 1
          ? 'qcCount'
          : 'ineffectivenessLockCount'
      if (this.itemInfo) {
        console.log('我改变了可退货数量--状态改变', this.itemInfo[_key])
        this.$bus.$emit(`lockCountChange`, this.itemInfo[_key])
      }
      this.$bus.$emit(`${this.data.column.field}Change`, e.itemData.value)
    }
  }
}
</script>

<style scoped lang="scss"></style>
