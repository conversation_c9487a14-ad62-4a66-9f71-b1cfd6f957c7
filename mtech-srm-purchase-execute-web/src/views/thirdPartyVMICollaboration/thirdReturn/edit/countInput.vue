<template>
  <div>
    <mt-input :id="data.column.field" v-model="data[data.column.field]" style="display: none" />
    <mt-input-number
      :id="data.column.field"
      v-model="data[data.column.field]"
      :min="0"
      :max="lockCountVal"
    />
  </div>
</template>

<script>
export default {
  props: {},
  data() {
    return { data: {}, value: '', lockCountVal: null }
  },
  mounted() {
    this.lockCountVal = this.data.lockCount
    this.fieldName = this.data.column.field
    this.$bus.$on(`lockCountChange`, (value) => {
      this.lockCountVal = value
    })
  },
  methods: {}
}
</script>

<style scoped lang="scss"></style>
