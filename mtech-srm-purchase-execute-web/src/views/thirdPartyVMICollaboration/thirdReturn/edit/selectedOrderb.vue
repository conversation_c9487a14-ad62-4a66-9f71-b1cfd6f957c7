<template>
  <div>
    <div class="in-cell">
      <mt-select
        :id="data.column.field"
        v-model="data[data.column.field]"
        :data-source="dataSource"
        :allow-filtering="true"
        filter-type="Contains"
        :fields="{
          text: 'codeAndName',
          value: 'itemCode'
        }"
        @change="fieldChange"
      >
      </mt-select>
      <mt-icon style="width: 20px" name="icon_input_search" @click.native="showDialog"></mt-icon>
    </div>
    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="title"
      :buttons="buttons"
      @close="handleClose"
    >
      <div class="full-height">
        <mt-template-page
          ref="templateRef"
          class="template-height has-page"
          :hidden-tabs="true"
          :template-config="pageConfig"
          @recordDoubleClick="recordDoubleClick"
        ></mt-template-page>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
export default {
  props: {},
  data() {
    return {
      dataSource: [],
      data: {},
      changedCellArr: [
        'itemName',
        'itemCode',
        'purchaseGroupCode',
        'purchaseGroupName',
        'itemUnitDescription',
        'itemUnit',
        'lineNo',
        'batchCode',
        'itemInfo'
      ],
      title: this.$t('请选择'),
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          toolbar: [],
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: 'aecb8dde-e57d-4d84-9ac7-1eab93ba5b25',
          grid: {
            // height: 352,
            // allowPaging: true,
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: [
              {
                width: '150',
                field: 'itemCode',
                headerText: this.$t('物料编号')
              },
              {
                width: '150',
                field: 'itemName',
                headerText: this.$t('物料名称')
              },
              {
                width: '150',
                field: 'itemUnitDescription',
                headerText: this.$t('单位')
              },
              {
                width: '150',
                field: 'purchaseGroupName',
                headerText: this.$t('采购组')
              }
              // {
              //   width: "150",
              //   field: "batchCode",
              //   headerText: this.$t("批次号"),
              // },
            ],
            asyncConfig: {},
            dataSource: []
          }
        }
      ],
      sourceStockType: null // 库存状态
    }
  },
  mounted() {
    this.dataSource = this.data.materialList.map((i) => {
      return {
        ...i,
        codeAndName: `${i.itemCode} - ${i.itemName}`
      }
    })
    this.$bus.$on('物料编码变化', (params) => {
      this.dataSource = params.map((i) => {
        return {
          ...i,
          codeAndName: `${i.itemCode} - ${i.itemName}`
        }
      })
    })

    this.sourceStockType = this.data.sourceStockType
    // 监听 移除库存状态的值
    this.$bus.$on('sourceStockTypeChange', (e) => {
      this.sourceStockType = e
    })
  },
  methods: {
    recordDoubleClick(args) {
      this.fieldChange({ itemData: args.rowData })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    confirm(e, records) {
      if (!records || records.length <= 0) {
        records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      }
      if (records.length <= 0) return
      this.fieldChange({ itemData: records[0] })
    },
    showDialog() {
      let headerInfo = this.data
      this.pageConfig[0].grid.asyncConfig = {
        url: `${BASE_TENANT}/vmi_stock/logistic-page-query`,
        recordsPosition: 'data.records',
        defaultRules: [
          {
            field: 'siteCode',
            operator: 'equal',
            value: headerInfo.siteCode
          },
          {
            field: 'supplierCode',
            operator: 'equal',
            value: headerInfo.supplierCode
          },
          {
            field: 'vmiWarehouseCode',
            operator: 'equal',
            value: headerInfo.vmiWarehouseCode
          }
        ]
        // params: obj,
      }
      this.$refs.dialog.ejsRef.show()
    },
    fieldChange(e) {
      this.data[this.data.column.field] = e.itemData?.itemCode
      let itemInfo = e.itemData
      this.changedCellArr.forEach((i) => {
        if (i == 'itemInfo') {
          this.$bus.$emit(`${i}Change`, JSON.stringify(itemInfo))
        } else {
          this.$bus.$emit(`${i}Change`, itemInfo?.[i])
        }
      })

      // 选择完物料后，如果有调出状态，才能 修改可调出数量
      if (this.sourceStockType || this.sourceStockType == 0) {
        let _key =
          this.sourceStockType == 0
            ? 'count'
            : this.sourceStockType == 1
            ? 'qcCount'
            : 'ineffectivenessLockCount'
        console.log('我改变了可退货数量-物料', itemInfo[_key])
        this.$bus.$emit('lockCountChange', itemInfo?.[_key])
      }
      this.handleClose()
      // console.log(this.data,"00000000022");
    }
  }
}
</script>

<style scoped lang="scss">
.in-cell {
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;

  /deep/ .mt-select {
    .e-input-group-icon,
    .e-ddl-icon,
    .e-search-icon {
      margin-right: 20px;
    }
  }
  > .mt-icons {
    margin-left: 5px;
    cursor: pointer;
    position: absolute;
    top: 8px;
    right: 0;
  }
}
</style>
