<template>
  <mt-input :id="data.column.field" v-model="data[data.column.field]" disabled />
</template>

<script>
export default {
  props: {},
  data() {
    return { data: {}, value: '' }
  },
  mounted() {
    const field = this.data.column.field
    this.$bus.$on(`${field}Change`, (value) => {
      this.$set(this.data, `${field}`, value)
    })
  },
  methods: {}
}
</script>

<style scoped lang="scss"></style>
