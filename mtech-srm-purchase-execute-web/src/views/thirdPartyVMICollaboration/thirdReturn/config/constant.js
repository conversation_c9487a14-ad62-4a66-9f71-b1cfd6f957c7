import { i18n } from '@/main.js'
import onlyShowInput from '../edit/onlyShowInput.vue'
import selectedOrdera from '../edit/selectedOrdera.vue' // 库存状态的下拉
import selectedOrderb from '../edit/selectedOrderb.vue' // 物料 sku

// tab
export const Tab = {
  list: 1, // 物料信息
  details: 2 // 物流信息
}

// 送货类型:1-关联采购订单,2-无采购订单
export const DeliveryType = {
  linked: 1, // 关联采购订单
  notLink: 2 // 无采购订单
}
// 送货类型 text
export const DeliveryTypeText = {
  [DeliveryType.linked]: i18n.t('关联采购订单'),
  [DeliveryType.notLink]: i18n.t('无采购订单')
}
// 送货类型 对应的 Options
export const DeliveryTypeOptions = [
  {
    // 关联采购订单
    value: DeliveryType.linked,
    text: DeliveryTypeText[DeliveryType.linked],
    cssClass: ''
  },
  {
    // 无采购订单
    value: DeliveryType.notLink,
    text: DeliveryTypeText[DeliveryType.notLink],
    cssClass: ''
  }
]

// 状态 状态:1-新建,2-发货中,3-已完成,4-已取消,5-已关闭   (之前mock数据)
//  status状态: 0:新建   1:待确认/已提交   2:已接收/待质检/已确认  8:已完成  9:已取消
export const Status = {
  new: 0, // 新建
  shipping: 1, // 待确认
  closed: 2, // 已接收
  completed: 8, // 已完成
  cancelled: 9 // 已取消
}
// 状态 text
export const StatusText = {
  [Status.new]: i18n.t('新建'),
  [Status.shipping]: i18n.t('待确认'),
  [Status.closed]: i18n.t('已接收'),
  [Status.completed]: i18n.t('已完成'),
  [Status.cancelled]: i18n.t('已取消')
}
// 状态 对应的 css class
export const StatusCssClass = {
  [Status.new]: 'col-active',
  [Status.shipping]: 'col-active',
  [Status.completed]: 'col-active',
  [Status.cancelled]: 'col-active',
  [Status.closed]: 'col-inactive'
}
// 状态 对应的 Options
export const StatusOptions = [
  {
    // 新建
    value: Status.new,
    text: StatusText[Status.new],
    cssClass: StatusCssClass[Status.new]
  },
  {
    // 发货中
    value: Status.shipping,
    text: StatusText[Status.shipping],
    cssClass: StatusCssClass[Status.shipping]
  },
  {
    // 已完成
    value: Status.completed,
    text: StatusText[Status.completed],
    cssClass: StatusCssClass[Status.completed]
  },
  {
    // 已取消
    value: Status.cancelled,
    text: StatusText[Status.cancelled],
    cssClass: StatusCssClass[Status.cancelled]
  },
  {
    // 已关闭
    value: Status.closed,
    text: StatusText[Status.closed],
    cssClass: StatusCssClass[Status.closed]
  }
]

// 发货方式:1-快递配送，2-物流配送
export const ShippingType = {
  express: 1, // 快递配送
  logistics: 2 // 物流配送
}
// 发货方式 text
export const ShippingTypeText = {
  [ShippingType.express]: i18n.t('快递配送'),
  [ShippingType.logistics]: i18n.t('物流配送')
}
// 发货方式 对应的 Options
export const ShippingTypeOptions = [
  {
    // 快递配送
    value: ShippingType.express,
    text: ShippingTypeText[ShippingType.express],
    label: ShippingTypeText[ShippingType.express],
    cssClass: ''
  },
  {
    // 物流配送
    value: ShippingType.logistics,
    text: ShippingTypeText[ShippingType.logistics],
    label: ShippingTypeText[ShippingType.logistics],
    cssClass: ''
  },
  {
    // 物流配送
    value: '',
    text: '无物流信息',
    label: '无物流信息',
    cssClass: ''
  }
]

// 物料信息 表格列数据
export const ColumnDataTab1 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: 0,
    field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    headerText: 'addId主键',
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。
    allowEditing: false
  },
  {
    width: '100',
    fieldCode: 'rowNum',
    fieldName: i18n.t('行号'),
    allowEditing: false,
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return Number(+row.index + 1)
      }
    }
  },
  // {
  //   fieldCode: "orderCode", // 关联采购订单号
  //   fieldName: i18n.t("关联采购订单号"),
  //   editTemplate: () => {
  //     return {
  //       template: selectedOrder,
  //     };
  //   },
  // },
  // {
  //   fieldCode: "lineNo", // 关联采购订单行号
  //   fieldName: i18n.t("关联采购订单行号"),
  //   ditTemplate: () => {
  //     return {
  //       template: selectedOrder,
  //     };
  //   },
  // },
  {
    fieldCode: 'itemCode', // 物料编码
    fieldName: i18n.t('物料编码'),
    editTemplate: () => {
      return {
        template: selectedOrderb
      }
    }
  },
  {
    fieldCode: 'itemName', //
    fieldName: i18n.t('物料名称'),
    allowEditing: false,
    editTemplate: () => {
      return { template: onlyShowInput }
    }
  },
  // {
  //   width: "0",
  //   fieldCode: "itemInfo", // 存入当前行选择的物料 stringify了
  //   allowEditing: false,
  //   editTemplate: () => {
  //     return { template: onlyShowInput };
  //   },
  // },
  {
    fieldCode: 'sourceStockType',
    fieldName: i18n.t('库存状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('合格库存'),
        // 1: i18n.t("待检库存"),
        // 2: i18n.t("出库冻结库存"),
        3: i18n.t('不合格库存')
      }
    },
    editTemplate: () => {
      return { template: selectedOrdera }
    }
  },
  {
    fieldCode: 'lockCount', // 可退货数量
    fieldName: i18n.t('可退货数量'), // 默认从接口拿，如果切换了物料、类型，则从物料的拿
    allowEditing: false,
    editTemplate: () => {
      return { template: onlyShowInput }
    }
  },
  {
    fieldCode: 'count', // 退货数量
    fieldName: i18n.t('退货数量'),
    editType: 'numericedit', //默认编辑类型之number
    edit: {
      params: {
        min: 0
      }
    }
  },
  {
    fieldCode: 'checkCount',
    fieldName: i18n.t('实退数量'),
    allowEditing: false
  },
  // {
  //   fieldCode: "batchCode", // 批次/卷号
  //   fieldName: i18n.t("批次/卷号"),
  //   allowEditing: false,
  //   editTemplate: () => {
  //     return { template: onlyShowInput };
  //   },
  // },
  {
    fieldCode: 'itemUnitDescription', // 单位 基本单位名称
    fieldName: i18n.t('单位名称'),
    allowEditing: false,
    editTemplate: () => {
      return { template: onlyShowInput }
    }
  },
  {
    width: 0,
    fieldCode: 'itemUnit',
    fieldName: i18n.t('单位编码'),
    editTemplate: () => {
      return { template: onlyShowInput }
    }
  },
  {
    fieldCode: 'purchaseGroupName', // 采购组
    fieldName: i18n.t('采购组名称'),
    allowEditing: false,
    editTemplate: () => {
      return { template: onlyShowInput }
    }
  },
  {
    width: 0,
    fieldCode: 'purchaseGroupCode', // 采购组
    fieldName: i18n.t('采购组编码'),
    allowEditing: false,
    editTemplate: () => {
      return { template: onlyShowInput }
    }
  },
  {
    fieldCode: 'remark', // 行备注
    fieldName: i18n.t('行备注')
  }
]
