<template>
  <!-- 送货单-详情-第三方 -->
  <div class="full-height pt20 vertical-flex-box" v-if="isShow">
    <!-- 头部信息 -->
    <top-info
      class="flex-keep"
      :header-info="headerInfo"
      @goBack="goBack"
      @preservationBtn="preservationBtn"
      @submitBtn="submitBtn"
    ></top-info>
    <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
    <!-- 列模板 -->
    <mt-template-page
      class="flex-fit"
      ref="dataGrid"
      :template-config="templateConfig"
      @handleClickToolBar="handleClickToolBar"
      v-show="tabIndex === 0"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    >
    </mt-template-page>
    <!-- 物流信息 Tab -->
    <div v-show="tabIndex === 1" slot="slot-1" class="full-height">
      <logistics-info ref="logisticsRef" :logistics-data="logisticsData"></logistics-info>
    </div>
  </div>
</template>

<script>
import { formatTableColumnData, checkData } from './config/details'
import { ColumnDataTab1 } from './config/constant'
// import { BASE_TENANT } from "@/utils/constant";

export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue'),
    LogisticsInfo: () => import('./components/logisticsInfo.vue')
  },
  watch: {
    'headerInfo.siteCode': async function () {
      const result = await this.getItemCode()
      this.$bus.$emit('物料编码变化', result)
    },
    'headerInfo.supplierCode': async function () {
      const result = await this.getItemCode()
      this.$bus.$emit('物料编码变化', result)
    },
    'headerInfo.vmiWarehouseCode': async function () {
      const result = await this.getItemCode()
      this.$bus.$emit('物料编码变化', result)
    }
  },
  data() {
    // const lastTabIndex = JSON.parse(localStorage.getItem("lastTabIndex"));
    // const deliverListData = JSON.parse(localStorage.getItem("deliverListData"));
    // const sendTime = deliverListData?.headerInfo?.sendTime; // 发货日期
    // const forecastArriveTime = deliverListData?.headerInfo?.forecastArriveTime; // 预计到货日期
    // const headerInfo = {
    //   ...deliverListData?.headerInfo,
    //   sendTime: sendTime ? new Date(Number(sendTime)) : "", // 发货日期,
    //   forecastArriveTime: sendTime ? new Date(Number(forecastArriveTime)) : "", // 预计到货日期,
    // };

    return {
      isShow: false,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      // 顶部的数据
      headerInfo: {
        cancelStatus: false, //取消
        preservationStatus: true, //保存
        submitStatus: true, //提交
        isDisabled: false, //false可编辑  true不可编辑
        hasDetailRow: this.$route.query.status == 0 // 是否新增状态 新建状态不可修改头部数据，因为通过头部数据获取物料
      },
      // 物流信息
      logisticsData: {
        transportType: '',
        statusCreate: true
        // transportNum:this
      },
      // lastTabIndex, // 前一页面的 Tab index
      templateConfig: [
        {
          // tab: { title: this.$t("物料信息") },
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 不使用组件中的toolbar配置
          toolbar: [
            [
              {
                id: 'newlyBuild',
                icon: 'icon_table_new',
                title: this.$t('新建')
              },
              { id: 'Delete', icon: 'Delete', title: this.$t('删除') }
              // { id: "Preservation", icon: "icon_table_save", title: this.$t("保存") },
            ],
            []
          ],
          grid: {
            allowEditing: true, //开启表格编辑操作
            allowPaging: false, // 不分页
            editSettings: {
              allowEditing: true, //是否允许编辑
              allowDeleting: true, //是否允许删除
              allowAdding: true, //是否允许新增
              showDeleteConfirmDialog: true, //删除是否需要确认
              newRowPosition: 'Top' //在上面新增一行还是下面新增一行
            },
            // lineIndex: 0, // 序号列
            columnData: formatTableColumnData({
              data: ColumnDataTab1
            }),
            dataSource: [],
            asyncConfig: {}
          }
        }
      ],
      tabSource: [
        {
          title: this.$t('物料信息')
        },
        {
          title: this.$t('物流信息')
        }
      ],
      tabIndex: 0,
      material: []
    }
  },
  created() {
    if (this.$route.query.id) {
      this.getDetails() //详情接口
      if (this.$route.query.status != 0) {
        this.logisticsData.statusCreate = false
        this.headerInfo.isDisabled = true
        this.templateConfig[0].grid.editSettings.allowEditing = false
        this.templateConfig[0].toolbar = []
      }
    }
    this.isShow = true
  },
  mounted() {
    // this.getDetails();  //详情接口
    // console.log( this.$refs.dataGrid.getCurrentTabRef(),"00000000000000");
  },
  beforeDestroy() {
    localStorage.removeItem('deliverListData')
    localStorage.removeItem('lastTabIndex')
  },
  methods: {
    // 物料编码
    getItemCode() {
      return new Promise((resolve) => {
        if (
          !this.headerInfo.siteCode ||
          !this.headerInfo.supplierCode ||
          !this.headerInfo.vmiWarehouseCode
        ) {
          return
        }
        this.$API.thirdPartyVMICollaboration
          .postthirdPartyLogisticQuer({
            page: {
              current: 1,
              size: 5000,
              orders: [
                {
                  column: 'item_code',
                  asc: true
                }
              ]
            },
            pageFlag: true,
            defaultRules: [
              {
                field: 'siteCode',
                operator: 'equal',
                value: this.headerInfo.siteCode
              },
              {
                field: 'supplierCode',
                operator: 'equal',
                value: this.headerInfo.supplierCode
              },
              {
                field: 'vmiWarehouseCode',
                operator: 'equal',
                value: this.headerInfo.vmiWarehouseCode
              }
            ]
          })
          .then((res) => {
            this.material = res.data.records
            resolve(this.material)
          })
      })
    },
    // 切换
    handleSelectTab(e) {
      this.tabIndex = e
      // 切换时结束编辑
      this.$refs.dataGrid.getCurrentUsefulRef().gridRef?.ejsRef.endEdit()
    },
    actionBegin(args) {
      if (args.requestType === 'add') {
        let obj = {
          // rowNum: this.templateConfig[0].grid.dataSource.length + 1, //行号
          materialList: this.material,
          siteCode: this.headerInfo.siteCode, //工厂编码
          supplierCode: this.headerInfo.supplierCode, //供应商编码
          vmiWarehouseCode: this.headerInfo.vmiWarehouseCode //vmi仓编码
        }
        for (const key of Object.keys(obj)) {
          args.data[key] = obj[key]
        }
        if (args.requestType == 'add') {
          args.data.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
        }
      } else if (args.requestType == 'beginEdit') {
        args.rowData.materialList = this.material
      }
    },
    actionComplete(args) {
      if (args.requestType == 'delete') {
        if (
          this.$refs.dataGrid?.getCurrentUsefulRef().gridRef?.ejsRef?.getCurrentViewRecords()
            ?.length === 0
        ) {
          this.headerInfo.hasDetailRow = false // 使头部数据可改变，因为通过头部数据获取物料，表格没有数据时，头部数据可修改
        }
      }
    },
    //   表格 toolbar 点击
    handleClickToolBar(e) {
      if (e.toolbar.id === 'newlyBuild') {
        // console.log(this.headerInfo);
        if (!this.headerInfo.siteCode) {
          this.$toast({
            content: this.$t('请选择工厂'),
            transportType: 'error'
          })
          return
        }
        if (!this.headerInfo.supplierCode) {
          this.$toast({
            content: this.$t('请选择供应商'),
            transportType: 'error'
          })
          return
        }
        this.$refs.dataGrid.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
        this.headerInfo.hasDetailRow = true // 使头部数据不可改变，因为通过头部数据获取物料，表格有数据时，头部数据不可修改
      } else if (e.toolbar.id === 'Delete') {
        // VMI领料单-批量删除
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.$refs.dataGrid.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
      }
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    // 提交
    submitBtn() {
      this.createInterface(2)
    },
    //保存
    preservationBtn() {
      this.createInterface(1)
    },
    // 创建接口
    createInterface(btnStatus) {
      // 校验  物流信息没有填写完整 就return
      if (!this.$refs.logisticsRef.checkForm()) {
        this.$toast({
          content: this.$t('物流信息没有填完整'),
          transportType: 'error'
        })
        return
      }
      let dataList = this.$refs.dataGrid
        .getCurrentUsefulRef()
        .gridRef?.ejsRef.getCurrentViewRecords()
      console.log(dataList, '8888888888')

      // 校验表格数据
      const { pass, msg } = checkData({ dataList })

      if (!pass) {
        this.$toast({
          content: msg,
          transportType: 'error'
        })
        return
      }

      let data = {
        id: this.$route.query.id || null,
        itemList: [],
        logisticsList: [], //物流信息
        operationType: btnStatus, //操作类型   1保存   提交
        remark: this.headerInfo.remark, //备注
        siteCode: this.headerInfo.siteCode, //工厂编码
        siteName: this.headerInfo.siteName, //工厂名称
        supplierCode: this.headerInfo.supplierCode, //供应商编码
        vmiWarehouseAddress: this.headerInfo.vmiWarehouseAddress, //送货地址
        vmiWarehouseCode: this.headerInfo.vmiWarehouseCode, //仓库编码
        vmiWarehouseName: this.headerInfo.vmiWarehouseName //仓库名称
      }
      dataList.forEach((item) => {
        item.lineNo = item.itemNo //行号
        item.orderCode = item.buyerOrder //订单号
        item.countLimit = item.quantity
        item.stockType = item.sourceStockType // 来源库存和目标库存是需要一致
      })
      data.itemList = dataList
      data.logisticsList.push(this.logisticsData)
      if (data.logisticsList[0].transportType === '') {
        data.logisticsList = []
      }
      this.$store.commit('startLoading')
      this.$API.thirdPartyVMICollaboration
        .postthirdPartyLogistic(data)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('创建成功'), type: 'success' })
            this.$router.go(-1)
          } else {
            this.$toast({
              content: this.$t('创建失败'),
              type: 'error'
            })
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },

    // 详情数据接口
    getDetails() {
      const obj = {
        id: this.$route.query.id
      }
      this.$API.purchaseCoordination.postBuyerWarehousingReturnDetail(obj).then((res) => {
        if (res.code === 200) {
          this.headerInfo = { ...this.headerInfo, ...res.data }
          const dataSource = res?.data?.vmiOrderItemResponses || []
          dataSource.forEach((i) => {
            i.addId = i.id
            let _key =
              i.sourceStockType == 0
                ? 'count'
                : i.sourceStockType == 1
                ? 'qcCount'
                : 'ineffectivenessLockCount'
            console.log('数据', _key, i)
            i.lockCount = i?.vmiStockResponse?.[_key]
          })
          this.templateConfig[0].grid.dataSource = dataSource
          this.logisticsData = {
            ...this.logisticsData,
            ...res?.data?.orderLogistic[0]
          }
        }
      })
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .flex-keep {
  margin-top: 10px;
}
</style>
