<!-- 供方-周计划排产 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('版本号')" prop="versionNo">
          <mt-date-picker
            v-model="searchFormModel.versionNo"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('状态')" prop="statusList">
          <mt-multi-select
            v-model="searchFormModel.statusList"
            :data-source="statusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('工厂')" prop="siteCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.siteCodeList"
            url="/masterDataManagement/auth/site/auth-fuzzy"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            params-key="fuzzyParam"
            records-position="data"
          />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')">
          <RemoteAutocomplete
            v-model="searchFormModel.itemCode"
            :url="$API.masterData.getItemUrl"
            :placeholder="$t('请选择')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :search-fields="['itemName', 'itemCode']"
          />
        </mt-form-item>
        <mt-form-item prop="buyerOrgCodeList" :label="$t('采购组')">
          <RemoteAutocomplete
            v-model="searchFormModel.buyerOrgCodeList"
            :url="$API.masterData.getBusinessGroupAuthFuzzyUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'groupName', value: 'groupCode' }"
            params-key="fuzzyParam"
            records-position="data"
          />
        </mt-form-item>
        <mt-form-item :label="$t('类型')" prop="typeList">
          <mt-multi-select
            v-model="searchFormModel.typeList"
            :data-source="typeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="cfe3da55-7452-49f8-9c19-b598b6655bdd"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :row-config="{ height: rowHeight }"
      :edit-config="{
        trigger: 'dblclick',
        mode: 'row',
        showStatus: true,
        activeMethod: this.activeRowMethod
      }"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
      @edit-disabled="editDisabledEvent"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #supplierItemCodeEdit="{ row }">
        <vxe-input v-model="row.supplierItemCode" :placeholder="$t('请输入')" clearable />
      </template>
      <template #supplierRemarkEdit="{ row }">
        <vxe-input v-model="row.supplierRemark" :placeholder="$t('请输入')" clearable />
      </template>
      <template #typeDefault="{ row }">
        <div v-for="(item, index) in row.schedulingDetailList" :key="index" class="vxe-cell-border">
          <span style="margin-left: 10px">{{ formatType(item.type) }}</span>
        </div>
      </template>
      <template #totalDefault="{ row }">
        <div v-for="(item, index) in row.schedulingDetailList" :key="index" class="vxe-cell-border">
          <span style="margin-left: 10px">{{ item.total }}</span>
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, statusOptions, typeOptions } from './config'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  components: { CollapseSearch, ScTable },
  data() {
    return {
      rowHeight: 128,
      searchFormModel: {
        versionNo: new Date(),
        typeList: ['P', 'C', 'GAP']
      },
      searchFormRules: {
        versionNo: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      toolbar: [
        { code: 'feedback', name: this.$t('反馈'), status: 'info', loading: false },
        { code: 'clearEdit', name: this.$t('取消编辑'), status: 'info', loading: false },
        { code: 'save', name: this.$t('保存'), status: 'info', loading: false },
        { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      statusOptions,
      typeOptions,

      typeList: [],

      editRows: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getHeader()
  },
  methods: {
    formatType(type) {
      let text = this.typeOptions.find((item) => item.value === type)?.text || ''
      return text
    },
    activeRowMethod({ row }) {
      if (row.status === 2) {
        return true
      }
      return false
    },
    editDisabledEvent() {
      this.$toast({
        content: this.$t('只能编辑已发布状态的数据'),
        type: 'warning'
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 清除编辑状态
        this.tableRef.clearEdit()
        let _index = -1
        if (this.editRows?.length) {
          _index = this.editRows.findIndex((item) => item.id === row.id)
        }
        if (_index !== -1) {
          this.editRows[_index] = row
        } else {
          this.editRows.push(row)
        }
        return
      }
    },
    getHeader() {
      let params = {
        versionNo: dayjs(this.searchFormModel.versionNo).format('YYYY-MM-DD')
      }
      this.$API.appConfig.headerSchedulingApi(params).then((res) => {
        if (res.code === 200) {
          this.handleColumns(res.data)
        }
      })
    },
    handleColumns(arr) {
      let currentColumn = columnData
      const dynamicColumn = []
      arr.forEach((item, i) => {
        const title = `srmField${i + 1}`
        dynamicColumn.push({
          title: item,
          field: title,
          width: 120,
          editRender: {},
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return row.schedulingDetailList.map((item, index) => (
                <div key={index} class='vxe-cell-border'>
                  <span style={{ 'margin-left': '10px' }} class={item[title] < 0 ? 'red' : ''}>
                    {item[title]}
                  </span>
                </div>
              ))
            },
            edit: ({ row }) => {
              return row.schedulingDetailList.map((item, index) => (
                <div key={index} class='vxe-cell-border'>
                  {['C'].includes(item.type) && row.status === 2 ? (
                    <vxe-input type='number' v-model={item[title]} />
                  ) : (
                    <span style={{ 'margin-left': '10px' }} class={item[title] < 0 ? 'red' : ''}>
                      {item[title]}
                    </span>
                  )}
                </div>
              ))
            }
          }
        })
      })
      this.columns = currentColumn.concat(dynamicColumn)
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          if (key === 'versionNo') {
            this.searchFormModel[key] = new Date()
          } else if (key === 'typeList') {
            this.searchFormModel[key] = ['P', 'C', 'GAP']
            this.typeList = ['P', 'C', 'GAP']
          } else {
            this.searchFormModel[key] = null
          }
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.editRows = []
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          if (
            !this.searchFormModel?.typeList ||
            this.searchFormModel?.typeList?.length === 0 ||
            this.searchFormModel?.typeList?.length === 3
          ) {
            this.typeList = ['P', 'C', 'GAP']
            this.rowHeight = 96
          } else if (this.searchFormModel?.typeList?.length === 2) {
            this.typeList = this.searchFormModel?.typeList
            this.rowHeight = 64
          } else if (this.searchFormModel?.typeList?.length === 1) {
            this.typeList = this.searchFormModel?.typeList
            this.rowHeight = 32
          }
          this.currentPage = 1
          this.getHeader()
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      params.versionNo = dayjs(params.versionNo).format('YYYYMMDD')
      this.loading = true
      const res = await this.$API.appConfig
        .pageWeeklyProductionScheduleSupApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = this.handleDataSource({ records })
      }
    },
    handleDataSource(args) {
      const { records } = args
      let dataSource = []
      dataSource = records.map((item) => {
        let schedulingDetailList = item.schedulingDetailList.filter((v) =>
          this.typeList.includes(v.type)
        )
        return {
          ...item,
          schedulingDetailList
        }
      })
      return dataSource
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['feedback']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'save':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认保存？')
            },
            success: () => {
              this.handleSave()
            }
          })
          break
        case 'feedback':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认反馈？')
            },
            success: () => {
              this.handleFeedBack(selectedRecords)
            }
          })
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleSave() {
      let params = []
      if (!this.editRows?.length) {
        this.$toast({ content: this.$t('请编辑数据'), type: 'success' })
        return
      }
      let detailList = []
      this.editRows.forEach((item) => {
        item.schedulingDetailList.forEach((v) => {
          if (v.type === 'C') {
            detailList.push(v)
          }
        })
        params.push({
          mainId: item.id,
          supplierItemCode: item.supplierItemCode,
          supplierRemark: item.supplierRemark,
          detailList
        })
      })

      this.$API.appConfig.saveWeeklyProductionScheduleSupApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('保存成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleFeedBack(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.appConfig.feedbackWeeklyProductionScheduleSupApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('反馈成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleImport() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      params.versionNo = dayjs(params.versionNo).format('YYYYMMDD')
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.appConfig.importWeeklyProductionScheduleSupApi,
          downloadTemplateApi: this.$API.appConfig.downloadWeeklyProductionScheduleSupApi,
          paramsKey: 'excel',
          downloadTemplateParams: {
            ...params
          }
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      params.versionNo = dayjs(params.versionNo).format('YYYYMMDD')
      this.$store.commit('startLoading')
      this.$API.appConfig
        .exportWeeklyProductionScheduleSupApi(params)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  .vxe-cell-border {
    &:first-child {
      border-top: none;
    }
    border-top: solid #e6e9ed 1px;
    height: 32px;
    line-height: 32px;
    box-sizing: border-box;
    .red {
      color: red;
    }
  }
}
</style>
