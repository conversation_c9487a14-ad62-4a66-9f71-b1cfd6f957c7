import { i18n } from '@/main.js'

export const statusOptions = [
  { text: i18n.t('已发布'), value: 2 },
  { text: i18n.t('反馈满足'), value: 3 },
  { text: i18n.t('反馈不满足'), value: 4 },
  { text: i18n.t('已确认'), value: 5 }
]

export const typeOptions = [
  { text: i18n.t('P（需求量）'), value: 'P' },
  { text: i18n.t('C（承诺量）'), value: 'C' },
  { text: i18n.t('Gap（差异）'), value: 'GAP' }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'versionNo',
    title: i18n.t('版本号')
  },
  {
    field: 'publishTime',
    title: i18n.t('发布时间'),
    minWidth: 140
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    minWidth: 80,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.siteName : ''
    }
  },
  {
    field: 'buyerOrgCode',
    title: i18n.t('采购组'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.buyerOrgName : ''
    }
  },
  {
    field: 'buyerAdvance',
    title: i18n.t('采方提前期')
  },
  {
    field: 'supplierAdvance',
    title: i18n.t('供方提前期')
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称')
  },
  {
    field: 'itemGroupCode',
    title: i18n.t('物料组'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.itemGroupName : ''
    }
  },
  {
    field: 'categoryCode',
    title: i18n.t('外部物料组'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.categoryName : ''
    }
  },
  {
    field: 'vmiInv',
    title: i18n.t('VMI库存')
  },
  {
    field: 'onWayQty',
    title: i18n.t('在途（直送本厂+VMI送本厂）')
  },
  {
    field: 'vmiOnWayQty',
    title: i18n.t('VMI在途（供方送VMI）')
  },
  {
    field: 'siteInv',
    title: i18n.t('原厂库存')
  },
  {
    field: 'unfilledOrderQty',
    title: i18n.t('未清PO')
  },
  {
    field: 'planOrderQty',
    title: i18n.t('计划订单')
  },
  {
    field: 'isSatisfiedName',
    title: i18n.t('是否满足')
  },
  {
    field: 'supplierItemCode',
    title: i18n.t('供方物料编码'),
    editRender: {},
    slots: {
      edit: 'supplierItemCodeEdit'
    },
    minWidth: 140
  },
  {
    field: 'platform',
    title: i18n.t('平台')
  },
  {
    field: 'supplierRemark',
    title: i18n.t('供方备注'),
    editRender: {},
    slots: {
      edit: 'supplierRemarkEdit'
    },
    minWidth: 140
  },
  {
    field: 'buyerRemark',
    title: i18n.t('采方备注')
  },
  {
    field: 'sapUpdateTime',
    title: i18n.t('SAP更新时间'),
    minWidth: 140
  },
  {
    field: 'type',
    title: i18n.t('类型'),
    className: 'vxe-table-multi-cell',
    minWidth: 90,
    slots: {
      default: 'typeDefault'
    }
  },
  {
    field: 'total',
    title: i18n.t('汇总'),
    className: 'vxe-table-multi-cell',
    minWidth: 90,
    slots: {
      default: 'totalDefault'
    }
  }
]
