<!-- 采方-周计划排产 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('版本号')" prop="versionNo">
          <mt-date-picker
            v-model="searchFormModel.versionNo"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('状态')" prop="statusList">
          <mt-multi-select
            v-model="searchFormModel.statusList"
            :data-source="statusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('工厂')" prop="siteCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.siteCodeList"
            url="/masterDataManagement/tenant/site/app/paged-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCodeList" :label="$t('供应商')">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCodeList"
            url="/masterDataManagement/tenant/supplier/paged-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')">
          <RemoteAutocomplete
            v-model="searchFormModel.itemCode"
            :url="$API.masterData.getItemUrl"
            :placeholder="$t('请选择')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :search-fields="['itemName', 'itemCode']"
          />
        </mt-form-item>
        <mt-form-item prop="buyerOrgCodeList" :label="$t('采购组')">
          <RemoteAutocomplete
            v-model="searchFormModel.buyerOrgCodeList"
            :url="$API.masterData.getBusinessGroupUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'groupName', value: 'groupCode' }"
            :search-fields="['groupName', 'groupCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('类型')" prop="typeList">
          <mt-multi-select
            v-model="searchFormModel.typeList"
            :data-source="typeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('展示日期')" prop="days">
          <mt-input
            type="number"
            v-model="searchFormModel.days"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
            @change="daysChange"
          />
        </mt-form-item>
        <mt-form-item prop="categoryCodeList" :label="$t('外部物料组')">
          <RemoteAutocomplete
            v-model="searchFormModel.categoryCodeList"
            url="/masterDataManagement/tenant/category/paged-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'categoryName', value: 'categoryCode' }"
            :search-fields="['categoryName', 'categoryCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('是否满足')" prop="isSatisfied">
          <mt-select
            v-model="searchFormModel.isSatisfied"
            :data-source="isSatisfiedOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="7a5ae246-cabd-4205-bccf-b900019a8db3"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :row-config="{ height: rowHeight }"
      :edit-config="{
        trigger: 'dblclick',
        mode: 'row',
        showStatus: true,
        activeMethod: this.activeRowMethod
      }"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
      @edit-disabled="editDisabledEvent"
      @edit-actived="editBegin"
      @edit-closed="editComplete"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #platformEdit="{ row }">
        <vxe-input v-model="row.platform" :placeholder="$t('请输入')" clearable />
      </template>
      <template #buyerRemarkEdit="{ row }">
        <vxe-input v-model="row.buyerRemark" :placeholder="$t('请输入')" clearable />
      </template>
      <template #typeDefault="{ row }">
        <div v-for="(item, index) in row.schedulingDetailList" :key="index" class="vxe-cell-border">
          <span style="margin-left: 10px">{{ formatType(item.type) }}</span>
        </div>
      </template>
      <template #totalDefault="{ row }">
        <div v-for="(item, index) in row.schedulingDetailList" :key="index" class="vxe-cell-border">
          <span style="margin-left: 10px">{{ item.total }}</span>
        </div>
      </template>
      <template #isSatisfiedDefault="{ row }">
        <div :style="row.isSatisfiedName === '否' ? { color: 'red' } : {}">
          <span style="margin-left: 10px">{{ row.isSatisfiedName }}</span>
        </div>
      </template>
      <template #supplierCodeEdit="{ row }">
        <vxe-pulldown ref="xDown" transfer>
          <template #default>
            <vxe-input
              :value="row.supplierCode"
              :placeholder="$t('请选择供应商')"
              readonly
              @click="focusSupplierCode"
            ></vxe-input>
          </template>
          <template #dropdown>
            <vxe-input
              prefix-icon="vxe-icon-search"
              :placeholder="$t('搜索')"
              @keyup="keyupSupplierCode"
              style="width: 100%"
            ></vxe-input>
            <vxe-list
              height="200"
              class="predict-vxe-dropdown"
              :data="supplierCodeOptions"
              auto-resize
            >
              <template #default="{ items }">
                <div
                  class="predict-vxe-list-item"
                  v-for="item in items"
                  :key="item.value"
                  @click="selectSupplierCode(item, row)"
                >
                  <span>{{ item.label }}</span>
                </div>
              </template>
            </vxe-list>
          </template>
        </vxe-pulldown>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, statusOptions, typeOptions, isSatisfiedOptions } from './config'
import { getHeadersFileName, download } from '@/utils/utils'
import { utils } from '@mtech-common/utils'

export default {
  components: { CollapseSearch, ScTable },
  data() {
    return {
      rowHeight: 128,
      searchFormModel: {
        versionNo: new Date(),
        typeList: ['D', 'P', 'C', 'GAP'],
        days: 31
      },
      searchFormRules: {
        versionNo: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      toolbar: [
        { code: 'publish', name: this.$t('发布'), status: 'info', loading: false },
        { code: 'unpublish', name: this.$t('取消发布'), status: 'info', loading: false },
        { code: 'clearEdit', name: this.$t('取消编辑'), status: 'info', loading: false },
        { code: 'save', name: this.$t('保存'), status: 'info', loading: false },
        { code: 'confirm', name: this.$t('确认'), status: 'info', loading: false },
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
        { code: 'calculate', name: this.$t('计算'), status: 'info', loading: false },
        { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
        { code: 'progress', name: this.$t('计算进度'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: [],
      loading: false,
      tableData: [],

      statusOptions,
      typeOptions,
      isSatisfiedOptions,
      typeList: [],
      editRows: [],

      supplierCodeOptions: [],
      getSupplierDataSource: () => {} // 供应商 下拉选项
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getHeader()
  },
  mounted() {
    this.getSupplierDataSource = utils.debounce(this.getSupplier, 1000)
  },
  methods: {
    focusSupplierCode() {
      this.$refs.xDown.showPanel()
    },
    keyupSupplierCode(e) {
      this.getSupplierDataSource({ ...e, type: 'supplierCode' })
    },
    selectSupplierCode(e, row) {
      row.supplierCode = e.supplierCode
      row.supplierId = e.id
      row.supplierName = e.supplierName
    },
    // 主数据 供应商
    getSupplier(args) {
      const { value, type } = args
      const params = {
        fuzzyNameOrCode: value ? value : ''
      }
      this.$API.masterData.getSupplier(params).then((res) => {
        if (res) {
          const list = res?.data || []
          const newData = list.map((i) => {
            return {
              ...i,
              label: `${i.supplierCode}-${i.supplierName}`,
              value: i.supplierCode
            }
          })
          if (type === 'supplierCode') {
            this.supplierCodeOptions = [...newData]
          }
        }
      })
    },
    daysChange(e) {
      if (Number(e) < 5) {
        this.searchFormModel.days = 5
      }
      if (Number(e) > 60) {
        this.searchFormModel.days = 60
      }
    },
    formatType(type) {
      let text = this.typeOptions.find((item) => item.value === type)?.text || ''
      return text
    },
    activeRowMethod({ row }) {
      if ([0, 1, 3, 4].includes(row.status)) {
        return true
      }
      return false
    },
    editDisabledEvent() {
      this.$toast({
        content: this.$t('只能编辑新建、已修改、反馈满足、反馈不满足状态的数据'),
        type: 'warning'
      })
    },
    editBegin(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        this.getSupplierDataSource({ value: row.supplierCode, type: 'supplierCode' })
      } else {
        this.getSupplierDataSource({ value: '', type: 'supplierCode' })
      }
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 清除编辑状态
        this.tableRef.clearEdit()
        let _index = -1
        if (this.editRows?.length) {
          _index = this.editRows.findIndex((item) => item.id === row.id)
        }
        if (_index !== -1) {
          this.editRows[_index] = row
        } else {
          this.editRows.push(row)
        }
        return
      }
    },
    getHeader() {
      let params = {
        versionNo: dayjs(this.searchFormModel.versionNo).format('YYYY-MM-DD')
      }
      this.$API.appConfig.headerSchedulingApi(params).then((res) => {
        if (res.code === 200) {
          let data = []
          let len = this.searchFormModel?.days || 31
          if (len === 5) {
            len = 6
          }
          if (len === 60) {
            len = 61
          }
          for (let i = 0; i < len; i++) {
            data.push(res.data[i])
          }
          this.handleColumns(data)
        }
      })
    },
    handleColumns(arr) {
      let currentColumn = columnData
      const dynamicColumn = []
      arr.forEach((item, i) => {
        const title = `srmField${i + 1}`
        dynamicColumn.push({
          title: item,
          field: title,
          width: 130,
          editRender: {},
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return row.schedulingDetailList.map((item, index) => (
                <div key={index} class='vxe-cell-border'>
                  <span style={{ 'margin-left': '10px' }} class={item[title] < 0 ? 'red' : ''}>
                    {item[title]}
                  </span>
                </div>
              ))
            },
            edit: ({ row }) => {
              return row.schedulingDetailList.map((item, index) => (
                <div key={index} class='vxe-cell-border'>
                  {['P', 'C'].includes(item.type) && [0, 1, 3, 4].includes(row.status) ? (
                    <vxe-input type='number' v-model={item[title]} />
                  ) : (
                    <span style={{ 'margin-left': '10px' }} class={item[title] < 0 ? 'red' : ''}>
                      {item[title]}
                    </span>
                  )}
                </div>
              ))
            }
          }
        })
      })
      this.columns = currentColumn.concat(dynamicColumn)
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          if (key === 'versionNo') {
            this.searchFormModel[key] = new Date()
          } else if (key === 'typeList') {
            this.searchFormModel[key] = ['D', 'P', 'C', 'GAP']
            this.typeList = ['D', 'P', 'C', 'GAP']
          } else if (key === 'days') {
            this.searchFormModel[key] = 31
          } else {
            this.searchFormModel[key] = null
          }
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.editRows = []
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          if (
            !this.searchFormModel?.typeList ||
            this.searchFormModel?.typeList?.length === 0 ||
            this.searchFormModel?.typeList?.length === 4
          ) {
            this.typeList = ['D', 'P', 'C', 'GAP']
            this.rowHeight = 128
          } else if (this.searchFormModel?.typeList?.length === 3) {
            this.typeList = this.searchFormModel?.typeList
            this.rowHeight = 96
          } else if (this.searchFormModel?.typeList?.length === 2) {
            this.typeList = this.searchFormModel?.typeList
            this.rowHeight = 64
          } else if (this.searchFormModel?.typeList?.length === 1) {
            this.typeList = this.searchFormModel?.typeList
            this.rowHeight = 32
          }
          this.currentPage = 1
          this.getHeader()
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      params.versionNo = dayjs(params.versionNo).format('YYYYMMDD')
      this.loading = true
      const res = await this.$API.appConfig
        .pageWeeklyProductionScheduleApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = this.handleDataSource({ records })
      }
    },
    handleDataSource(args) {
      const { records } = args
      let dataSource = []
      dataSource = records.map((item) => {
        let schedulingDetailList = item.schedulingDetailList.filter((v) =>
          this.typeList.includes(v.type)
        )
        return {
          ...item,
          schedulingDetailList
        }
      })
      return dataSource
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['publish', 'unpublish', 'confirm', 'delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'publish':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认发布？')
            },
            success: () => {
              this.handlePublish(selectedRecords)
            }
          })
          break
        case 'unpublish':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认取消发布？')
            },
            success: () => {
              this.handleUnpublish(selectedRecords)
            }
          })
          break
        case 'confirm':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('是否确认？')
            },
            success: () => {
              this.handleConfirm(selectedRecords)
            }
          })
          break
        case 'delete':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除？')
            },
            success: () => {
              this.handleDelete(selectedRecords)
            }
          })
          break
        case 'calculate':
          this.handleCalculate()
          break
        case 'save':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认保存？')
            },
            success: () => {
              this.handleSave()
            }
          })
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        case 'progress':
          this.hanldeProgress()
          break
        default:
          break
      }
    },
    handlePublish(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.appConfig.publishWeeklyProductionScheduleApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('发布成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleUnpublish(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.appConfig.unpublishWeeklyProductionScheduleApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('取消发布成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleConfirm(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.appConfig.confirmWeeklyProductionScheduleApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('确认成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleDelete(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.appConfig.deleteWeeklyProductionScheduleApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleCalculate() {
      this.$dialog({
        data: {
          title: this.$t('计算')
        },
        modal: () => import('./components/CalculateDialog.vue'),
        success: async (form) => {
          let params = {}
          if (form?.supplierCodeList) {
            params.supplierCodeList = form.supplierCodeList
          }
          const validRes = await this.$API.appConfig.validCalWeeklyProductionScheduleApi()
          if (validRes.code === 200) {
            this.$store.commit('startLoading')
            this.$API.appConfig.calWeeklyProductionScheduleApi(params).then((res) => {
              this.$store.commit('endLoading')
              if (res.code === 200) {
                this.$toast({ content: res.data, type: 'success' })
                this.handleSearch()
              }
            })
          } else {
            this.$toast({ content: validRes.data, type: 'warning' })
          }
        }
      })
    },
    async hanldeProgress() {
      const res = await this.$API.appConfig.validCalWeeklyProductionScheduleApi()
      this.$dialog({
        data: {
          title: this.$t('计算进度'),
          message: res.data
        },
        success: () => {}
      })
    },
    handleSave() {
      let params = []
      if (!this.editRows?.length) {
        this.$toast({ content: this.$t('请编辑数据'), type: 'success' })
        return
      }
      let detailList = []
      this.editRows.forEach((item) => {
        item.schedulingDetailList.forEach((v) => {
          if (['P', 'C'].includes(v.type)) {
            detailList.push(v)
          }
        })
        params.push({
          mainId: item.id,
          platform: item.platform,
          buyerRemark: item.buyerRemark,
          supplierCode: item.supplierCode,
          detailList
        })
      })

      this.$API.appConfig.saveWeeklyProductionScheduleApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('保存成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleImport() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      params.versionNo = dayjs(params.versionNo).format('YYYYMMDD')
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.appConfig.importWeeklyProductionScheduleApi,
          downloadTemplateApi: this.$API.appConfig.downloadWeeklyProductionScheduleApi,
          paramsKey: 'excel',
          downloadTemplateParams: {
            ...params
          }
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      params.versionNo = dayjs(params.versionNo).format('YYYYMMDD')
      this.$store.commit('startLoading')
      this.$API.appConfig
        .exportWeeklyProductionScheduleApi(params)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  .vxe-cell-border {
    &:first-child {
      border-top: none;
    }
    border-top: solid #e6e9ed 1px;
    height: 32px;
    line-height: 32px;
    box-sizing: border-box;
    .red {
      color: red;
    }
  }
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
