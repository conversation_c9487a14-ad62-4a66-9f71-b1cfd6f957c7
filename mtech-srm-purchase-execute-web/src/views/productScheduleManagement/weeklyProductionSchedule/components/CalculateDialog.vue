<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    :width="400"
    :height="400"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="dialog-mt-form" ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="type" :label="$t('计算类型')">
          <mt-select
            v-model="formObject.type"
            :data-source="typeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item v-if="formObject.type === 2" prop="supplierCodeList" :label="$t('供应商')">
          <RemoteAutocomplete
            v-model="formObject.supplierCodeList"
            url="/masterDataManagement/tenant/supplier/paged-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      //v-model获取的值
      formObject: {
        type: 1
      },
      //必填项
      formRules: {
        type: [
          {
            required: true,
            message: this.$t('请选择计算类型'),
            trigger: 'blur'
          }
        ],
        supplierCodeList: [
          {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          }
        ]
      },

      typeOptions: [
        { text: this.$t('全部计算'), value: 1 },
        { text: this.$t('部分计算'), value: 2 }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    //点击确认
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          this.$emit('confirm-function', this.formObject)
        }
      })
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    // padding: 0;
    .dialog-content {
      padding: 20px 0;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 100%;
        }
      }
    }
  }
}
</style>
