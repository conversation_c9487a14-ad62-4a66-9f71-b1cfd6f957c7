<!-- 排产管理配置 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('状态')" prop="status">
          <mt-select
            v-model="searchFormModel.status"
            :data-source="statusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('配置类型')" prop="configTypeList">
          <mt-multi-select
            v-model="searchFormModel.configTypeList"
            :data-source="configTypeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="companyCodeList" :label="$t('公司')">
          <RemoteAutocomplete
            v-model="searchFormModel.companyCodeList"
            url="/masterDataManagement/tenant/organization/specified-level-paged-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :params="{
              organizationLevelCodes: ['ORG01', 'ORG02']
            }"
          />
        </mt-form-item>
        <mt-form-item :label="$t('工厂编码')" prop="siteCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.siteCodeList"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCodeList" :label="$t('供应商')">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCodeList"
            url="/masterDataManagement/tenant/supplier/paged-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
        <mt-form-item prop="purOrgCodeList" :label="$t('采购组')">
          <RemoteAutocomplete
            v-model="searchFormModel.purOrgCodeList"
            :url="$API.masterData.getBusinessGroupUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'groupName', value: 'groupCode' }"
            :search-fields="['groupName', 'groupCode']"
          />
        </mt-form-item>
        <mt-form-item prop="planGroupCodeList" :label="$t('计划组')">
          <RemoteAutocomplete
            v-model="searchFormModel.planGroupCodeList"
            :url="$API.masterData.getBusinessGroupUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'groupName', value: 'groupCode' }"
            :search-fields="['groupName', 'groupCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('创建人')" prop="createUserName">
          <mt-input
            v-model="searchFormModel.createUserName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建日期')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            @change="(e) => dateTimeChange(e, 'createTime')"
            :placeholder="$t('请选择创建日期')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('更新人')" prop="updateUserName">
          <mt-input
            v-model="searchFormModel.updateUserName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="updateTime" :label="$t('更新日期')">
          <mt-date-range-picker
            v-model="searchFormModel.updateTime"
            @change="(e) => dateTimeChange(e, 'updateTime')"
            :placeholder="$t('请选择更新日期')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="b2dee2b6-ea33-4e61-81f2-13cb7fbeeaac"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />

    <AddOrEdit ref="addOrEditRef" @confirm="handleSearch" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, statusOptions, configTypeOptions } from './config'
import { getHeadersFileName, download } from '@/utils/utils'
import AddOrEdit from './components/AddOrEdit.vue'

export default {
  components: { CollapseSearch, ScTable, AddOrEdit },
  data() {
    return {
      searchFormModel: {},
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
        { code: 'edit', name: this.$t('编辑'), status: 'info', loading: false },
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
        { code: 'enable', name: this.$t('启用'), status: 'info', loading: false },
        { code: 'disable', name: this.$t('停用'), status: 'info', loading: false },
        { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      statusOptions,
      configTypeOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.appConfig
        .pageDeliveryScheduleMonitorConfigApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['edit', 'delete', 'enable', 'disable']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.code === 'edit' && selectedRecords.length !== 1) {
        this.$toast({ content: this.$t('只能选择一行数据进行编辑操作'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'edit':
          this.handleEdit(selectedRecords[0])
          break
        case 'delete':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除？')
            },
            success: () => {
              this.handleDelete(selectedRecords)
            }
          })
          break
        case 'enable':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认启用？')
            },
            success: () => {
              this.handleEnable(selectedRecords)
            }
          })
          break
        case 'disable':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认停用？')
            },
            success: () => {
              this.handleDisable(selectedRecords)
            }
          })
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleAdd() {
      this.$refs.addOrEditRef.dialogInit({
        title: this.$t('新增'),
        actionType: 'add'
      })
    },
    handleEdit(row) {
      this.$refs.addOrEditRef.dialogInit({
        title: this.$t('编辑'),
        actionType: 'edit',
        row
      })
    },
    handleDelete(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.appConfig.deleteDeliveryScheduleMonitorConfigApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleEnable(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.appConfig.enableDeliveryScheduleMonitorConfigApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('启用成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleDisable(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.appConfig.disableDeliveryScheduleMonitorConfigApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('停用成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.appConfig.importDeliveryScheduleMonitorConfigApi,
          downloadTemplateApi: this.$API.appConfig.downloadDeliveryScheduleMonitorConfigApi,
          paramsKey: 'excel'
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.appConfig
        .exportDeliveryScheduleMonitorConfigApi(params)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>
