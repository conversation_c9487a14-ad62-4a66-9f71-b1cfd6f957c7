<!-- 采方-排产管理配置-新增or编辑 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :height="800"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-form-item prop="configType" :label="$t('配置类型')">
        <mt-select
          v-model="formData.configType"
          :fields="{ text: 'text', value: 'value' }"
          :data-source="configTypeOptions"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
        />
      </mt-form-item>
      <mt-form-item prop="supplierCode" :label="$t('供应商')">
        <RemoteAutocomplete
          v-model="formData.supplierCode"
          url="/masterDataManagement/tenant/supplier/paged-query"
          :placeholder="$t('请选择')"
          :fields="{ text: 'supplierName', value: 'supplierCode' }"
          :search-fields="['supplierName', 'supplierCode']"
          @change="supplierChange"
        />
      </mt-form-item>
      <mt-form-item prop="siteCodeList" :label="$t('工厂')">
        <RemoteAutocomplete
          v-model="formData.siteCodeList"
          :url="$API.masterData.getSiteListUrl"
          multiple
          :placeholder="$t('请选择')"
          :fields="{ text: 'siteName', value: 'siteCode' }"
          :search-fields="['siteName', 'siteCode']"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[2, 4].includes(formData.configType)"
        prop="purOrgCode"
        :label="$t('采购组')"
      >
        <RemoteAutocomplete
          v-model="formData.purOrgCode"
          :url="$API.masterData.getBusinessGroupUrl"
          :placeholder="$t('请选择')"
          :fields="{ text: 'groupName', value: 'groupCode' }"
          :search-fields="['groupName', 'groupCode']"
          @change="purOrgChange"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[3].includes(formData.configType)"
        prop="planGroupCode"
        :label="$t('计划组')"
      >
        <RemoteAutocomplete
          v-model="formData.planGroupCode"
          :url="$API.masterData.getBusinessGroupCriteriaQueryUrl"
          :placeholder="$t('请选择')"
          :params="{
            groupTypeCode: 'BG001JH'
          }"
          @change="planGroupChange"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[4].includes(formData.configType)"
        prop="itemCode"
        :label="$t('物料编码')"
      >
        <RemoteAutocomplete
          v-model="formData.itemCode"
          :url="$API.masterData.getItemUrl"
          :placeholder="$t('请选择')"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          :search-fields="['itemName', 'itemCode']"
          @change="itemChange"
        />
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { configTypeOptions } from '../config/index'
import { cloneDeep } from 'lodash'
export default {
  data() {
    return {
      dialogTitle: '',
      actionType: 'add',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {},
      rules: {
        configType: [
          {
            required: true,
            message: this.$t('请选择配置类型'),
            trigger: 'blur'
          }
        ],
        siteCodeList: [
          {
            required: true,
            message: this.$t('请选择工厂'),
            trigger: 'blur'
          }
        ],
        purOrgCode: [
          {
            required: true,
            message: this.$t('请选择采购组'),
            trigger: 'blur'
          }
        ],
        planGroupCode: [
          {
            required: true,
            message: this.$t('请选择计划组'),
            trigger: 'blur'
          }
        ],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          }
        ],
        itemCode: [
          {
            required: true,
            message: this.$t('请选择物料编码'),
            trigger: 'blur'
          }
        ]
      },

      configTypeOptions
    }
  },
  methods: {
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, row } = args
      this.dialogTitle = title
      this.actionType = actionType
      if (actionType === 'edit') {
        this.formData = cloneDeep(row)
        this.formData.siteCodeList = [this.formData.siteCode]
      }
    },
    beforeOpen() {
      this.formData = {}
      this.$refs.ruleForm.clearValidate()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$refs.ruleForm2.validate((valid2) => {
            if (valid2) {
              this.handleSave()
            }
          })
        }
      })
    },
    handleSave() {
      let params = { ...this.formData }
      const api = this.$API.appConfig.saveDeliveryScheduleMonitorConfigApi
      api(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.actionType === 'add' ? this.$t('新增成功') : this.$t('更改成功'),
            type: 'success'
          })
          this.$emit('confirm')
          this.handleClose()
        }
      })
    },
    siteChange(e) {
      this.formData.siteName = e.itemData?.siteName
    },
    purOrgChange(e) {
      this.formData.purOrgName = e.itemData?.groupName
    },
    planGroupChange(e) {
      this.formData.planGroupName = e.itemData?.groupName
    },
    supplierChange(e) {
      this.formData.supplierName = e.itemData?.supplierName
    },
    categoryChange(e) {
      this.formData.categoryName = e.itemData?.categoryName
    },
    itemChange(e) {
      this.formData.itemName = e.itemData?.itemName
    }
  }
}
</script>
