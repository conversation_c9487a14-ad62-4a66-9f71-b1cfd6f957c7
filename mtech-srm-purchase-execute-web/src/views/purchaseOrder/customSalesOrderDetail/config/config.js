import Vue from 'vue'
import UTILS from '@/utils/utils'
import { i18n } from '@/main.js'

export const columnData1 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    field: 'orderCode',
    headerText: i18n.t('采购订单号'),
    width: '150',
    cellTools: []
  },
  {
    field: 'version',
    headerText: i18n.t('历史反馈'),
    width: '150',
    cellTools: [],
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return e - 1 === 0 ? '' : e - 1
      }
    }
  },
  {
    field: 'status',
    width: '120',
    headerText: i18n.t('订单形式'),
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = ''
              if (this.data.status === 4) {
                str = i18n.t('订单取消')
              }
              if (this.data.status === 2 && this.data.version === 1) {
                str = i18n.t('新订单')
              }
              if (this.data.status === 2 && this.data.version > 1) {
                str = i18n.t('订单变更')
              }
              return str
            }
          }
        })
      }
    }
  },
  {
    field: 'deliveryStatus',
    headerText: i18n.t('发货状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未发货'),
        1: i18n.t('部分发货'),
        2: i18n.t('全部发货')
      }
    }
  },
  {
    field: 'receiveStatus',
    headerText: i18n.t('收货状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未收货'),
        1: i18n.t('部分收货'),
        2: i18n.t('全部收货')
      }
    }
  },
  {
    field: 'createTime',
    headerText: i18n.t('订单日期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        e = Number(e)
        return UTILS.dateFormat(e)
      }
    }
  },
  {
    field: 'purTenantName',
    headerText: i18n.t('客户名称'),
    width: '150'
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('客户业务类型'),
    width: '150'
  },
  {
    field: 'requiredDeliveryDate',
    headerText: i18n.t('要求交期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        e = Number(e)
        return UTILS.dateFormat(e)
      }
    }
  },
  {
    field: 'buyerOrgName',
    headerText: i18n.t('采购组织名称'),
    width: '150'
  },
  {
    field: 'buyerGroupName',
    headerText: i18n.t('采购组名称'),
    width: '150'
  },
  {
    field: 'budgetQuantity',
    headerText: i18n.t('预测采购量'),
    editType: 'numericedit'
  },
  {
    field: 'startDate',
    headerText: i18n.t('预测采购周期起'),
    editType: 'datePickerEdit',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        e = Number(e)
        return UTILS.dateFormat(e)
      }
    }
  },
  {
    field: 'endDate',
    headerText: i18n.t('预测采购周期止'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        e = Number(e)
        return UTILS.dateFormat(e)
      }
    }
  },
  {
    field: 'confirmStatus',
    headerText: i18n.t('供应商确认状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('待确认'), 1: i18n.t('确认'), 2: i18n.t('拒绝') }
    }
  },
  {
    field: 'warehouseStatus',
    headerText: i18n.t('入库状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未入库'),
        1: i18n.t('部分入库'),
        2: i18n.t('全部入库')
      }
    }
  },
  {
    field: 'buyerUserName',
    headerText: i18n.t('采购员'),
    width: '150'
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    width: '150'
  },
  {
    field: 'paymentName',
    headerText: i18n.t('付款条件'),
    width: '150'
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    width: '150'
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    width: '150'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return e
        }
      }
    }
  }
]
export const lastColumn = [
  // {
  //   field: "file",
  //   width: "250",
  //   headerText: "附件",
  //   allowEditing: false,
  //   // 使用editTemplate时，显示的值不能是对象
  //   template: function () {
  //     return {
  //       template: cellUpload,
  //     };
  //   },
  // },
  {
    field: 'confirmRemark',
    width: '250',
    headerText: i18n.t('确认备注')
  }
]
