<template>
  <div class="apply-detail1 full-height">
    <top-info
      :entry-type="entryType"
      :top-info="topInfo"
      :entry-id="entryId"
      :version-options="versionOptions"
      @versionChange="versionChange"
    >
    </top-info>
    <div class="bottom-tables">
      <mt-template-page ref="templateRef1" :template-config="pageConfig">
        <div slot="slot-0">
          <!-- <mt-DataGrid
                ref="grid0"
                :data-source="dataSource1"
                :column-data="columnData"
                :allow-sorting="true"
                :allow-filtering="true"
                :filter-settings="filterOptions"
                :toolbar="toolbar"
                :page-settings="pageSettings"
                :allow-paging="true"
                @toolbarClick="handleClickToolBar"
                @currentChange="handleCurrentChange"
                @sizeChange="handleSizeChange"
              ></mt-DataGrid> -->
          <mt-template-page
            ref="grid0"
            :template-config="pageConfig1"
            @handleClickToolBar="handleClickToolBar"
          ></mt-template-page>
        </div>
        <div slot="slot-1">
          <relative-file
            ref="relativeFileRef"
            :doc-id="docId"
            :is-view="isView"
            :request-url-obj="requestUrlObj"
            :file-query-parms="fileQueryParms"
          ></relative-file>
        </div>
      </mt-template-page>
    </div>
    <accept-or-refuse-dialog
      ref="acceptOrRefuseDialog"
      @refreshColumns="refreshColumns"
    ></accept-or-refuse-dialog>
  </div>
</template>
<script>
import { BASE_TENANT } from '@/utils/constant'
import UTILS from '@/utils/utils'
import * as CONFIG from './config/config'
export default {
  components: {
    TopInfo: () => import('./components/topInfo'),
    AcceptOrRefuseDialog: () => import('./components/acceptOrRefuseDialog'),
    RelativeFile: () => import('@/components/businessComponents/relativeFile/index.vue')
  },
  data() {
    return {
      fileQueryParms: {
        docType: 'as_so'
      },
      docId: this.$route.query.id,
      isView: false,
      requestUrlObj: {
        preUrl: 'purchaseOrder', // 前缀
        listUrl: 'getFileNodeByDocId', // 获取左侧节点
        fileUrl: `${BASE_TENANT}/file/queryFileByDocId`, // 获取附件列表的，要完整的url
        saveUrl: 'saveRelativeFile' // 将附件文件url保存到列表
      },
      pageSettings: {
        pageSize: 1,
        pageCount: 8,
        totalRecordsCount: 0,
        enableQueryString: true,
        pageSizes: [1, 10, 20, 50, 100, 200]
      },
      toolbar: {
        useBaseConfig: false,
        tools: [
          { id: 'accept', icon: 'icon_table_accept1', title: this.$t('接受') },
          { id: 'reject', icon: 'icon_table_refuse1', title: this.$t('拒绝') }
        ]
      },
      dataSource1: [],
      columnData: [
        {
          width: '50',
          type: 'checkbox',
          showInColumnChooser: false
        }
      ],
      filterOptions: {
        type: 'Menu'
      },
      versionOptions: [],
      entryId: null,
      entryType: null, //1是编辑 3是详情 2是历史反馈
      topInfo: {},
      pageConfig: [
        { title: '', moduleId: '', moduleKey: '' },
        { title: '', moduleId: '', moduleKey: '' }
      ],
      pageConfig1: [
        {
          toolbar: [],
          grid: {
            columnData: [
              {
                width: '50',
                type: 'checkbox',
                showInColumnChooser: false
              }
            ],
            dataSource: [],
            frozenColumns: 1,
            asyncConfig: {
              // url: `${BASE_TENANT}/supOrder/module/data`,
              // recordsPosition: "data.supOrderDetailResponse.records",
              // queryBuilderWrap: "requestParams",
              // params: {
              //   docId: 1,
              //   // moduleId: 0,
              //   moduleKey: 2,
              // },
            }
          }
        }
      ],
      fields: [],
      moduleKey: ''
    }
  },
  mounted() {
    this.entryId = this.$route.query.id
    this.entryType = this.$route.query.type
    this.isView = this.$route.query.type == 1 ? false : true
    this.init()
  },
  methods: {
    refreshColumns() {
      this.$refs[`grid0`].refreshCurrentGridData()
    },
    async init() {
      //编辑或是详情
      if (this.entryType === '1' || this.entryType === '3') {
        this.$API.purchaseOrder.getSupplierSaleServiceOrder(this.entryId).then((res) => {
          this.topInfo = {
            ...res.data,
            type: res.data.type.toString(),
            requiredDeliveryDate: UTILS.dateFormat(Number(res.data.requiredDeliveryDate))
          }
          this.$set(this.topInfo.delivery, 'method', this.topInfo?.delivery?.method?.toString())
        })
        if (this.entryType === '3') {
          this.pageConfig1[0].toolbar = []
        }
        if (this.entryType === '1') {
          this.pageConfig1[0].toolbar = [
            {
              id: 'accept',
              icon: 'icon_table_accept1',
              title: this.$t('接受')
            },
            {
              id: 'reject',
              icon: 'icon_table_refuse1',
              title: this.$t('拒绝')
            }
          ]
        }
      }
      //历史反馈
      if (this.entryType === '2') {
        this.$API.purchaseOrder.getSupplierSaleServiceOrder(this.entryId).then((res) => {
          this.topInfo = {
            ...res.data,
            type: res.data.type.toString(),
            requiredDeliveryDate: UTILS.dateFormat(Number(res.data.requiredDeliveryDate))
          }
          this.$set(this.topInfo.delivery, 'method', this.topInfo?.delivery?.method?.toString())
        })
        this.pageConfig1[0].toolbar = []
        let params = {
          orderCode: this.$route.query.orderCode,
          purTenantId: this.$route.query.tenantId,
          version: 0
        }
        await this.$API.purchaseOrder
          .getSupSaleServiceVersion(params)
          // .getSupSaleServiceVersion(this.$route.query.orderCode)
          .then((res) => {
            res.data.forEach((item) => {
              this.versionOptions.push({ text: 'V' + item, value: item })
            })
          })
      }
      await this.getSupSaleServiceModuleConfig()
      this.getSupSaleServiceModuleData()
    },
    async getSupSaleServiceModuleConfig() {
      let params = { docId: this.entryId }
      await this.$API.purchaseOrder.getSupSaleServiceModuleConfig(params).then((res) => {
        this.pageConfig[0].title = res.data.moduleItems[0].moduleName
        this.pageConfig[0].moduleKey = res.data.moduleItems[0].moduleKey
        this.pageConfig[0].moduleId = res.data.moduleItems[0].moduleId
        this.pageConfig[1].title = res.data.moduleItems[1].moduleName
        this.pageConfig[1].moduleKey = res.data.moduleItems[1].moduleKey
        this.pageConfig[1].moduleId = res.data.moduleItems[1].moduleId
        res.data.moduleItems.some((item) => {
          if (item.moduleType === 11) {
            item.fieldDefines.forEach((item2) => {
              item2.field = item2.fieldCode
              item2.headerText = item2.fieldName
              item2.width = '150'
            })
            this.pageConfig1[0].grid.columnData = this.columnData.concat(
              item.fieldDefines.map((item1) => {
                let obj = {}
                let hasItem = false
                CONFIG.columnData1.some((item2) => {
                  if (item2.field === item1.fieldCode) {
                    obj = {
                      ...item2,
                      headerText: item1.fieldName,
                      hasItem: 1
                    }
                    hasItem = true
                  }
                })
                if (hasItem) {
                  return obj
                } else {
                  return {
                    ...item1,
                    headerText: item1.fieldName,
                    field: item1.fieldCode,
                    hasItem: 2
                  }
                }
              }),
              CONFIG.lastColumn
            )
            // .concat(CONFIG.lastColumn);
          }
        })
      })
    },
    getSupSaleServiceModuleData() {
      this.$set(this.pageConfig1[0].grid, 'asyncConfig', {
        url: `${BASE_TENANT}/supplierSaleServiceOrderDetail/module/data`,
        recordsPosition: 'data.records',
        queryBuilderWrap: 'requestParams',
        params: {
          moduleId: this.pageConfig[0].moduleId,
          moduleKey: this.pageConfig[0].moduleKey,
          orderId: this.entryId
        }
      })
    },
    versionChange(e) {
      let params = {
        orderCode: this.$route.query.orderCode,
        version: e.value,
        purTenantId: this.$route.query.tenantId
      }
      this.$API.purchaseOrder
        .getSupSaleServiceOrderHistory(params)
        // .getSupSaleServiceOrderHistory(this.$route.query.orderCode, e.value)
        .then((res) => {
          this.topInfo = res.data
        })
    },
    goBack() {
      this.$router.push({
        name: 'custom-order'
      })
    },
    handleClickToolBar(e) {
      console.log(e, e.grid.getSelectedRecords())
      if (e.grid.getSelectedRecords().length === 0 && e.toolbar.id !== 'Add') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = []
      e.grid.getSelectedRecords().map((item) => _id.push(item.id))
      if (e.toolbar.id == 'accept') {
        this.handleAccept(_id)
      } else if (e.toolbar.id == 'reject') {
        this.handleReject(_id)
      }
    },
    handleAccept(ids) {
      this.$refs.acceptOrRefuseDialog.acceptInfo({
        dialogTitle: this.$t('批量接受'),
        ids: ids,
        orderId: this.entryId
      })
    },
    handleReject(ids) {
      this.$refs.acceptOrRefuseDialog.acceptInfo({
        dialogTitle: this.$t('批量拒绝'),
        ids: ids,
        orderId: this.entryId
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.apply-detail1 {
  display: flex;
  flex-direction: column;

  .bottom-tables {
    flex: 1;
    display: flex;
    flex-direction: column;

    .mt-tabs,
    .mt-tabs /deep/ .mt-tabs-container {
      width: 100%;
    }
  }
}
/deep/.mt-tabs {
  width: 100%;
  background-color: #fff;
  .mt-tabs-container {
    background-color: #fff;
  }
}
</style>
