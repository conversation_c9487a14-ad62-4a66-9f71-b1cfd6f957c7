<template>
  <div
    :class="[
      'top-info',
      !isExpand && 'top-info-small',
      !isExpand && entryType === '2' && 'top-info-history-small'
    ]"
  >
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="search-con" v-if="entryType === '2'">
        <i class="mt-icons mt-icon-icon_input_search"></i>
        <mt-select
          :width="274"
          :data-source="versionOptions"
          :allow-filtering="true"
          :placeholder="$t('请选择版本')"
          @change="versionChange"
          v-model="selectVersion"
        >
        </mt-select>
      </div>
      <div class="infos mr20" v-if="entryType != '2'">
        {{ $t('单据编号：') }}{{ topInfo.orderCode }}
      </div>
      <div class="middle-blank"></div>
      <!-- 右侧各种操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" v-if="entryType === '1'" @click="save">{{
        $t('提交')
      }}</mt-button>
      <div v-if="entryType != '2'" class="sort-box" @click="expandChange">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>
    <!-- 下面的内容 -->
    <div class="main-bottom">
      <div class="header-box header-box-bottom" v-if="entryType === '2'">
        <div class="infos mr20">{{ $t('单据编号：') }}{{ topInfo.orderCode }}</div>
        <div class="middle-blank"></div>
        <!-- 右侧各种操作按钮 -->
        <div class="sort-box sort-box-bottom" @click="expandChange">
          <mt-icon
            v-for="index in 2"
            :key="index"
            :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
          ></mt-icon>
        </div>
      </div>
      <mt-form ref="ruleForm" :model="topInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="businessTypeName" :label="$t('业务类型')">
          <mt-input v-model="topInfo.businessTypeName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="supplierName" :label="$t('供应商')">
          <mt-input v-model="topInfo.supplierName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="type" :label="$t('售后类型')" :show-message="false">
          <mt-select v-model="topInfo.type" :data-source="typeOptions" :disabled="true"></mt-select>
        </mt-form-item>
        <mt-form-item prop="purOrderCode" :label="$t('关联采购订单')" :show-message="false">
          <mt-input v-model="topInfo.purOrderCode" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="paymentName" :label="$t('付款条件')" :show-message="false">
          <mt-input v-model="topInfo.paymentName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="buyerUserName" :label="$t('采购员')" :show-message="false">
          <mt-input v-model="topInfo.buyerUserName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="companyName" :label="$t('公司')" :show-message="false">
          <mt-input v-model="topInfo.companyName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="buyerOrgName" :label="$t('采购组织')" :show-message="false">
          <mt-input v-model="topInfo.buyerOrgName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="requiredDeliveryDate" :label="$t('要求完成时间')" :show-message="false">
          <mt-input v-model="topInfo.requiredDeliveryDate" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="taxTotal" :label="$t('含税总金额')" :show-message="false">
          <mt-input v-model="topInfo.taxTotal" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="freeTotal" :label="$t('未税总金额')" :show-message="false">
          <mt-input v-model="topInfo.freeTotal" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="currencyName" :label="$t('订单币种')" :show-message="false">
          <mt-input v-model="topInfo.currencyName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="settlementName" :label="$t('结算方')" :show-message="false">
          <mt-input v-model="topInfo.settlementName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="invoiceId" :label="$t('发票信息')" :show-message="false">
          <mt-input v-model="topInfo.invoiceId" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input v-model="topInfo.remark" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="deliveryTime" :label="$t('发货时间')" :show-message="false">
          <mt-input v-model="topInfo.deliveryTime" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="deliveryMethod"
          :label="$t('物流方式')"
          :show-message="false"
          v-if="topInfo.delivery"
        >
          <mt-select
            v-model="topInfo.delivery.method"
            :data-source="deliveryMethodOptions"
            :disabled="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          v-if="topInfo.delivery"
          prop="driverName"
          :label="$t('司机姓名')"
          :show-message="false"
        >
          <mt-input v-model="topInfo.delivery.driverName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item
          v-if="topInfo.delivery"
          prop="driverPhone"
          :label="$t('司机联系方式')"
          :show-message="false"
        >
          <mt-input v-model="topInfo.delivery.driverPhone" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item
          v-if="topInfo.delivery"
          prop="deliveryRemark"
          :label="$t('发货备注')"
          class="full-width"
          :show-message="false"
        >
          <mt-input v-model="topInfo.delivery.remark" :disabled="true"></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    entryType: {
      type: String,
      default: '1'
    },
    topInfo: {
      type: Object,
      default: () => {}
    },
    entryId: {
      type: String,
      default: ''
    },
    versionOptions: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    versionOptions: {
      handler(newVal) {
        // 默认选中第一个
        if (newVal.length) {
          this.selectVersion = newVal[0].value
        }
      }
    }
  },
  data() {
    return {
      selectVersion: 0,
      isExpand: false,
      rules: {},
      typeOptions: [
        {
          text: this.$t('退货订单'),
          value: '0'
        },
        {
          text: this.$t('换货订单'),
          value: '1'
        },
        {
          text: this.$t('维修订单'),
          value: '2'
        }
      ],
      deliveryMethodOptions: [
        {
          text: this.$t('快递配送'),
          value: '1'
        },
        {
          text: this.$t('物流配送'),
          value: '2'
        },
        {
          text: this.$t('供方自提'),
          value: '3'
        }
      ]
    }
  },
  methods: {
    expandChange() {
      this.isExpand = !this.isExpand
      this.$refs.ruleForm.clearValidate()
    },
    goBack() {
      this.$router.push({
        name: 'custom-order'
      })
    },
    save() {
      this.$API.purchaseOrder.getSupSaleServiceOrderFeedBack(this.entryId).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$router.push({
            name: 'custom-order'
          })
        }
      })
    },
    versionChange(e) {
      console.log('versionChange', e, e.value)
      this.$emit('versionChange', e)
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  &-history-small {
    height: 100px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-bottom {
      margin-right: 30px;
      border-top: 1px solid rgb(232, 232, 232);
    }

    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      &-bottom {
        margin-right: 0px !important;
      }
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
      .mt-form-item-topLabel .label {
        color: #9a9a9a;
      }
    }
    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
      padding: 20px 0;
    }
    /deep/ .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }
}
/deep/ .e-input-group .e-disabled {
  background-color: #fff !important;
}
/deep/.search-con {
  .mt-icon-icon_input_search {
    margin: 0 -20px 0 10px;
  }
  .select-container .e-input-group {
    font-size: 16px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(41, 41, 41, 1);
    .e-input {
      text-indent: 20px;
    }
  }
}
</style>
