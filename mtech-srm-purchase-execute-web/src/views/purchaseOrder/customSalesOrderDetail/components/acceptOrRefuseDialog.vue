<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <mt-form-item
        prop="refuseType"
        :label="$t('拒绝类型')"
        class="full-width"
        v-if="dialogTitle === $t('批量拒绝')"
      >
        <mt-select
          v-model="ruleForm.refuseType"
          :data-source="refuseTypeOptions"
          :show-clear-button="true"
          :placeholder="$t('请选择拒绝类型')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item
        prop="remark"
        :label="$t('意见备注')"
        class="full-width"
        :rules="[
          {
            message: this.$t('请输入意见备注'),
            trigger: 'blur',
            required: dialogTitle === this.$t('批量拒绝')
          }
        ]"
      >
        <mt-input
          v-model="ruleForm.remark"
          :show-clear-button="true"
          :placeholder="$t('请输入意见备注')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      orderId: '',
      entryIds: [],
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {
        refuseType: '',
        remark: ''
      },
      rules: {
        refuseType: [
          {
            required: true,
            message: this.$t('请选择拒绝类型'),
            trigger: 'blur'
          }
        ]
      },
      refuseTypeOptions: [
        { text: this.$t('时间异常'), value: 0 },
        { text: this.$t('数量异常'), value: 1 }
      ]
    }
  },
  methods: {
    acceptInfo(entryInfo) {
      this.dialogTitle = entryInfo.dialogTitle
      this.entryIds = entryInfo.ids
      this.orderId = entryInfo.orderId
      this.$refs.dialog.ejsRef.show()
      this.ruleForm = {
        refuseType: '',
        remark: ''
      }
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.dialogTitle === this.$t('批量接受')) {
            let params = {
              orderDetailIds: {},
              orderId: this.orderId
            }
            this.entryIds.forEach((item) => {
              params.orderDetailIds[item] = this.ruleForm.remark
            })
            this.$API.purchaseOrder.supSaleServiceOrderConfirm(params).then(() => {
              this.$toast({
                content: this.$t('批量接受操作成功'),
                type: 'success'
              })
              this.handleClose()
            })
          }
          if (this.dialogTitle === this.$t('批量拒绝')) {
            let params = {
              orderDetails: [],
              orderId: this.orderId
            }
            this.entryIds.forEach((item) => {
              params.orderDetails.push({
                orderDetailId: item,
                refuseType: this.ruleForm.refuseType,
                remark: this.ruleForm.remark
              })
            })
            this.$API.purchaseOrder.supSaleServiceOrderRefuse(params).then(() => {
              this.$toast({
                content: this.$t('批量拒绝操作成功'),
                type: 'success'
              })
              this.handleClose()
            })
          }
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
      this.$emit('refreshColumns')
    }
  }
}
</script>
