<template>
  <div :class="['detail-top-info', !isExpand && 'detail-top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="infos mr20">{{ $t('单据编号') }}：{{ headerInfo.orderCode }}</div>
      <div class="infos mr20">{{ $t('创建人') }}：{{ headerInfo.createUserName }}</div>
      <div class="infos">{{ $t('创建时间') }}：{{ headerInfo.createTime }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <span class="header-box-btn" v-waves type="info" @click="goBack">{{ $t('返回') }}</span>
      <div class="sort-box" @click="doExpand">
        <span>{{ isExpand ? $t('收起') : $t('展开') }}</span>
        <i
          class="mt-icons mt-icon-MT_DownArrow"
          :class="isExpand ? 'expendIcon' : 'unExpendIcon'"
        />
      </div>
    </div>

    <div class="main-bottom">
      <mt-form :model="headerInfo">
        <mt-form-item prop="businessTypeName" :label="$t('业务类型')">
          <mt-input v-model="headerInfo.businessTypeName" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="orderTypeName" :label="$t('订单类型')">
          <mt-input v-model="headerInfo.orderTypeName" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="company" :label="$t('公司')">
          <mt-input v-model="headerInfo.company" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="supplier" :label="$t('供应商')">
          <mt-input v-model="headerInfo.supplier" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="paymentName" :label="$t('付款条件')">
          <mt-input v-model="headerInfo.paymentName" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="buyerUserName" :label="$t('采购员')">
          <mt-input v-model="headerInfo.buyerUserName" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="buyerGroup" :label="$t('采购组织')">
          <mt-input v-model="headerInfo.buyerGroup" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="buyerOrg" :label="$t('采购组')">
          <mt-input v-model="headerInfo.buyerOrg" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="currency" :label="$t('订单币种')">
          <mt-input v-model="headerInfo.currency" :disabled="true" />
        </mt-form-item>
        <mt-form-item v-if="headerInfo.taxTotal === -1" prop="taxTotal" :label="$t('含税总金额')">
          <mt-input v-model="headerInfo.taxTotal" :disabled="true" />
        </mt-form-item>
        <mt-form-item v-if="headerInfo.taxTotal === -1" prop="freeTotal" :label="$t('未税总金额')">
          <mt-input v-model="headerInfo.freeTotal" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('采方备注')" class="half-width">
          <mt-input v-model="headerInfo.remark" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="supRemark" :label="$t('供方备注')" class="half-width">
          <mt-input v-model="headerInfo.supRemark" :disabled="true" />
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isExpand: true
    }
  },
  methods: {
    goBack() {
      this.$emit('goBack')
    },
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    },
    getCreateUserName() {},
    getCreateTime() {}
  }
}
</script>

<style lang="scss" scoped>
.main-bottom {
  /deep/ .half-width {
    width: calc(40% - 20px) !important;
  }
}
</style>
