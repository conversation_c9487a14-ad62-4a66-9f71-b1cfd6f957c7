<!-- 采购订单历史详情 -->
<template>
  <div class="full-height pt20 vertical-flex-box">
    <TopInfo class="flex-keep" :header-info="headerInfo" @goBack="goBack" @doExpand="doExpand" />

    <mt-template-page
      class="flex-fit"
      ref="templateRef"
      :hidden-tabs="false"
      :template-config="templateConfig"
    />
  </div>
</template>

<script>
import { columnData } from './config/index'
import TopInfo from './components/DetailTopInfo.vue'
export default {
  components: { TopInfo },
  data() {
    return {
      headerInfo: {},
      templateConfig: [
        {
          tab: { title: this.$t('订单明细') },
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [],
          grid: {
            allowPaging: false, // 分页
            columnData,
            dataSource: []
          }
        }
      ]
    }
  },
  mounted() {
    this.headerInfo = this.$route.query
    this.getTableData()
  },
  methods: {
    getTableData() {
      let id = this.$route.query.orderId
      this.$API.purchaseOrder.getPurchaseOrderHistoryDetail([id]).then((res) => {
        console.log(res)
        if (res.code === 200) {
          this.templateConfig[0].grid.dataSource = res.data
        }
      })
    },
    goBack() {
      this.$router.push({
        name: 'purchase-coordination-history'
      })
    },
    doExpand() {
      this.$refs.templateRef?.resetGridHeight && this.$refs.templateRef.resetGridHeight()
    }
  }
}
</script>
