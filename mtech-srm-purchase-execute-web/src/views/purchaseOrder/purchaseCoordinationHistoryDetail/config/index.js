import { i18n } from '@/main.js'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import Vue from 'vue'

const timeDate = (dataKey, hasTime) => {
  const template = () => {
    return {
      template: Vue.component('date', {
        template: `<div><div>{{data[dataKey] | dateFormat}}</div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div></div>`,
        data: function () {
          return { data: {}, dataKey, hasTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }
  return template
}

export const columnData = [
  {
    field: 'itemNo',
    headerText: i18n.t('行号')
  },
  {
    field: 'confirmStatus',
    headerText: i18n.t('供应商确认状态'),
    width: 160,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('待确认'),
        1: i18n.t('反馈异常'),
        2: i18n.t('反馈正常')
      }
    }
  },
  {
    field: 'urgentTime',
    headerText: i18n.t('加急时间'),
    template: timeDate('urgentTime', false)
  },
  {
    field: 'urgentStatus',
    headerText: i18n.t('加急状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('普通'),
        1: i18n.t('加急')
      }
    }
  },
  {
    field: 'closeStatus',
    headerText: i18n.t('关闭状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未关闭'),
        1: i18n.t('已关闭')
      }
    }
  },
  {
    field: 'warehouseStatus',
    headerText: i18n.t('入库状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未入库'),
        1: i18n.t('部分入库'),
        2: i18n.t('全部入库')
      }
    }
  },
  {
    field: 'deliveryStatus',
    headerText: i18n.t('发货状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未发货'),
        1: i18n.t('部分发货'),
        2: i18n.t('全部发货')
      }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料/品项编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料/品项名称')
  },
  {
    field: 'itemGroupCode',
    headerText: i18n.t('品项组编码')
  },
  {
    field: 'itemGroupName',
    headerText: i18n.t('品项组名称')
  },
  {
    field: 'outstandingNum',
    headerText: i18n.t('订单未清数量')
  },
  {
    field: 'projectRowText',
    headerText: i18n.t('行项目文本')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    template: () => {
      return {
        template: Vue.component('category', {
          template: `<div>{{data.categoryCode}}-{{data.categoryName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('地点/工厂'),
    template: () => {
      return {
        template: Vue.component('site', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerOrgName',
    headerText: i18n.t('采购组'),
    template: () => {
      return {
        template: Vue.component('buyerOrg', {
          template: `<div>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'unitName',
    headerText: i18n.t('基本单位'),
    template: () => {
      return {
        template: Vue.component('unit', {
          template: `<div>{{data.unitCode}}-{{data.unitName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'purUnitName',
    headerText: i18n.t('采购单位'),
    template: () => {
      return {
        template: Vue.component('purUnit', {
          template: `<div>{{data.purUnitCode}}-{{data.purUnitName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'quantity',
    headerText: i18n.t('订单数量')
  },
  {
    field: 'transitQty',
    headerText: i18n.t('在途数量')
  },
  {
    field: 'warehouseQty',
    headerText: i18n.t('已入库数量')
  },
  {
    field: 'deliveryQty',
    headerText: i18n.t('已发货数量')
  },
  {
    field: 'preDeliveryQty',
    headerText: i18n.t('待发货数量')
  },
  {
    field: 'receiveQty',
    headerText: i18n.t('已收货数量')
  },
  {
    field: 'requiredDeliveryDate',
    headerText: i18n.t('要求交期'),
    template: timeDate('requiredDeliveryDate', false)
  },
  {
    field: 'consignee',
    headerText: i18n.t('收货人')
  },
  {
    field: 'contact',
    headerText: i18n.t('联系方式')
  },
  {
    field: 'receiveAddress',
    headerText: i18n.t('收货地址')
  },
  {
    field: 'changeRemark',
    headerText: i18n.t('变更备注')
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  },
  {
    field: 'workOrderRel',
    headerText: i18n.t('关联工单')
  },
  {
    field: 'warehouse',
    headerText: i18n.t('库存地点')
  },
  {
    field: 'customerOrder',
    headerText: i18n.t('关联销售订单')
  },
  {
    field: 'customerOrderLineNo',
    headerText: i18n.t('关联销售订单行号')
  },
  {
    field: 'returnIdentification',
    headerText: i18n.t('退货标识'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('否'),
        1: i18n.t('是')
      }
    }
  },
  {
    field: 'supplierPromiseQty',
    headerText: i18n.t('承诺数量')
  },
  {
    field: 'timePromise',
    headerText: i18n.t('承诺日期'),
    template: timeDate('requiredDeliveryDate', false)
  },
  {
    field: 'supRemark',
    headerText: i18n.t('供方反馈备注')
  },
  {
    field: 'itemText',
    headerText: i18n.t('物料长文本')
  }
]
