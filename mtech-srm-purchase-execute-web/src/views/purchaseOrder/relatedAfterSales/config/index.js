import Vue from 'vue'
import cellUpload from '@/components/Upload/cellUpload'
import { i18n } from '@/main.js'

export const ConstantType = {
  Add: '1', // 新增
  Edit: '2', // 编辑
  Look: '3' // 查看
}

// 新增、编辑 售后数量
export const AfterSaleNumColumnDataAbleEdit = {
  width: '150',
  field: 'afterSaleNum',
  headerText: i18n.t('售后数量'),
  template: () => {
    return {
      template: Vue.component('actionInput', {
        template: `<mt-input-number v-model="data.afterSaleNum" cssClass="e-outline" :max="data.preAfterSaleQty" :show-clear-button="false" type="text" @change="handleChange"></mt-input-number>`,
        data: function () {
          return { data: {} }
        },
        mounted() {},
        methods: {
          handleChange(e) {
            if (
              this.data.preAfterSaleQty &&
              (isNaN(Number(e)) || Number(e) < 0 || e > this.data.preAfterSaleQty)
            ) {
              // 非数字、小于 0、大于 可售后数量
              this.data.afterSaleNum = this.data.preAfterSaleQty
            } else {
              this.data.afterSaleNum = e
            }
            this.$parent.$emit('cellEdit', {
              index: this.data.index,
              key: 'afterSaleNum',
              value: this.data.afterSaleNum
            })
          }
        }
      })
    }
  },
  visibleCondition: (data) => {
    return data.preAfterSaleQty && data.preAfterSaleQty > 0
  }
}

// 查看时 售后数量 不可编辑
export const AfterSaleNumColumnDataNotEditable = {
  width: '150',
  field: 'afterSaleNum',
  headerText: i18n.t('售后数量'),
  template: () => {
    return {
      template: Vue.component('actionInput', {
        template: `<div>{{data.afterSaleNum}}</div>`,
        data: function () {
          return { data: {} }
        },
        mounted() {}
      })
    }
  },
  visibleCondition: (data) => {
    return data.preAfterSaleQty && data.preAfterSaleQty > 0
  }
}

// 附件信息
export const OrderFilesColumnData = {
  width: '250',
  field: 'orderFiles',
  headerText: i18n.t('附件信息'),
  template: function () {
    return {
      template: cellUpload
    }
  }
}

// 格式化表格动态数据
export const formatTableDynamicData = (config) => {
  const { data, entryType } = config
  const colData = []
  if (data) {
    data.forEach((col) => {
      let columnCol = {
        field: col.fieldCode,
        headerText: col.fieldName,
        width: '150'
      }

      if (col.fieldCode == 'confirmStatus') {
        // 供应商确认状态
        columnCol.template = () => {
          return {
            template: Vue.component('date', {
              template: `<div>{{data.confirmStatus | formatData}}</div>`,
              data: function () {
                return { data: {} }
              },
              filters: {
                formatData(value) {
                  const map = {
                    0: i18n.t('待确认'),
                    1: i18n.t('确认'),
                    2: i18n.t('拒绝')
                  }
                  if (!map[value]) {
                    return value
                  } else {
                    return map[value]
                  }
                }
              }
            })
          }
        }
      } else if (col.fieldCode == 'deliveryStatus') {
        // 发货状态
        columnCol.template = () => {
          return {
            template: Vue.component('date', {
              template: `<div>{{data.deliveryStatus | formatData}}</div>`,
              data: function () {
                return { data: {} }
              },
              filters: {
                formatData(value) {
                  const map = {
                    0: i18n.t('未发货'),
                    1: i18n.t('部分发货'),
                    2: i18n.t('全部发货')
                  }
                  if (!map[value]) {
                    return value
                  } else {
                    return map[value]
                  }
                }
              }
            })
          }
        }
      } else if (col.fieldCode == 'receiveStatus') {
        // 收货状态
        columnCol.template = () => {
          return {
            template: Vue.component('date', {
              template: `<div>{{data.receiveStatus | formatData}}</div>`,
              data: function () {
                return { data: {} }
              },
              filters: {
                formatData(value) {
                  const map = {
                    0: i18n.t('未收货'),
                    1: i18n.t('部分收货'),
                    2: i18n.t('全部收货')
                  }
                  if (!map[value]) {
                    return value
                  } else {
                    return map[value]
                  }
                }
              }
            })
          }
        }
      } else if (col.fieldCode == 'warehouseStatus') {
        // 入库状态
        columnCol.template = () => {
          return {
            template: Vue.component('date', {
              template: `<div>{{data.warehouseStatus | formatData}}</div>`,
              data: function () {
                return { data: {} }
              },
              filters: {
                formatData(value) {
                  const map = {
                    0: i18n.t('未入库'),
                    1: i18n.t('部分入库'),
                    2: i18n.t('全部入库')
                  }
                  if (!map[value]) {
                    return value
                  } else {
                    return map[value]
                  }
                }
              }
            })
          }
        }
      } else if (col.fieldCode == 'afterSaleNum') {
        // 售后数量
        if (entryType == ConstantType.Add || entryType == ConstantType.Edit) {
          columnCol = AfterSaleNumColumnDataAbleEdit
        } else {
          // 不可编辑
          columnCol = AfterSaleNumColumnDataNotEditable
        }
      }
      colData.push(columnCol)
    })
    let _afterSaleNumColumnData = {}
    if (entryType == ConstantType.Add || entryType == ConstantType.Edit) {
      // 新增、编辑状态，售后数量可编辑
      _afterSaleNumColumnData = AfterSaleNumColumnDataAbleEdit
    } else {
      // 其他状态，售后数量不可编辑
      _afterSaleNumColumnData = AfterSaleNumColumnDataNotEditable
    }
    // 关联创建采购售后订单特有列：附件信息、可售后数量、已售后数量、售后数量
    colData.push(
      OrderFilesColumnData, // 附件信息
      {
        field: 'preAfterSaleQty',
        headerText: i18n.t('可售后数量'),
        width: '150'
      },
      {
        field: 'afterSaleQty',
        headerText: i18n.t('已售后数量'),
        width: '150'
      },
      _afterSaleNumColumnData // 售后数量
    )
  }

  return colData
}
