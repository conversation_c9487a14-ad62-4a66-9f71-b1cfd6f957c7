<template>
  <!-- 送货单详情头部的内容：收货 -->
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="infos mr20 bold">{{ $t('单据编号') }}：{{ headerInfo.orderCode }}</div>
      <div class="infos mr20">{{ $t('创建人') }}：{{ headerInfo.createUserName }}</div>
      <div class="infos">{{ $t('创建时间') }}：{{ headerInfo.createTime | dateFormat }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button
        v-if="entryType != ConstantType.Look"
        css-class="e-flat"
        :is-primary="true"
        @click="saveDraft"
        >{{ $t('保存草稿') }}</mt-button
      >
      <mt-button
        v-if="entryType == ConstantType.Add || entryType == ConstantType.Edit"
        css-class="e-flat"
        :is-primary="true"
        @click="doSubmit"
        >{{ $t('提交') }}</mt-button
      >

      <div class="sort-box" @click="isExpand = !isExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="businessTypeName" :label="$t('业务类型')">
          <mt-input
            v-model="headerInfo.businessTypeName"
            :disabled="true"
            :placeholder="$t('业务类型')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplierName" :label="$t('供应商')">
          <mt-input
            v-model="headerInfo.supplierName"
            :disabled="true"
            :placeholder="$t('供应商')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="type" :label="$t('售后类型')" :show-message="false">
          <mt-select
            :disabled="true"
            v-model="headerInfo.type"
            :data-source="typeOptions"
            :show-clear-button="false"
            :placeholder="$t('售后类型')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="reason" :label="$t('售后原因')" :show-message="false">
          <mt-select
            :disabled="true"
            v-model="headerInfo.reason"
            :data-source="reasonOptions"
            :show-clear-button="false"
            :placeholder="$t('售后原因')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="purOrderCode" :label="$t('关联采购订单')" :show-message="false">
          <mt-input
            v-model="headerInfo.purOrderCode"
            :disabled="true"
            :placeholder="$t('关联采购订单')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="paymentName" :label="$t('付款条件')" :show-message="false">
          <mt-input
            v-model="headerInfo.paymentName"
            :disabled="true"
            :placeholder="$t('付款条件')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="buyerUserName" :label="$t('采购员')" :show-message="false">
          <mt-input
            v-model="headerInfo.buyerUserName"
            :disabled="true"
            placeholder="$t('采购员')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="companyName" :label="$t('公司')" :show-message="false">
          <mt-input
            v-model="headerInfo.companyName"
            :disabled="true"
            placeholder="$t('公司')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="buyerOrgName" :label="$t('采购组织')" :show-message="false">
          <mt-input
            v-model="headerInfo.buyerOrgName"
            :disabled="true"
            :placeholder="$t('采购组织')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="dueDate" :label="$t('要求交期')" :show-message="false">
          <mt-date-time-picker
            :disabled="true"
            :show-clear-button="true"
            :allow-edit="false"
            v-model="headerInfo.dueDate"
            :placeholder="$t('要求交期')"
          ></mt-date-time-picker>
        </mt-form-item>

        <mt-form-item prop="currencyName" :label="$t('订单币种')" :show-message="false">
          <mt-input
            v-model="headerInfo.currencyName"
            :disabled="true"
            :placeholder="$t('订单币种')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="taxTotal" :label="$t('含税总金额')" :show-message="false">
          <mt-input
            v-model="headerInfo.taxTotal"
            :disabled="true"
            :placeholder="$t('含税总金额')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="freeTotal" :label="$t('未税总金额')" :show-message="false">
          <mt-input
            v-model="headerInfo.freeTotal"
            :disabled="true"
            :placeholder="$t('未税总金额')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="settlementName" :label="$t('结算方')" :show-message="false">
          <mt-input
            v-model="headerInfo.settlementName"
            :disabled="true"
            :placeholder="$t('结算方')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="settlementName" :label="$t('发票信息')" :show-message="false">
          <mt-input
            v-model="headerInfo.settlementName"
            :disabled="true"
            :placeholder="$t('发票信息')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input
            v-model="headerInfo.remark"
            :disabled="entryType == ConstantType.Look"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import utils from '@/utils/utils'
import { ConstantType } from '../config/index.js'

export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    },
    entryType: {
      type: String,
      default: () => '0' // 0: 新增、编辑 1: 查看
    }
  },
  data() {
    return {
      ConstantType,
      isExpand: true,
      rules: {},
      // 订单类型；0-退货订单；1-换货订单；2-维修订单
      typeOptions: [
        {
          value: 0,
          text: this.$t('退货')
        },
        {
          value: 1,
          text: this.$t('换货')
        },
        {
          value: 2,
          text: this.$t('维修')
        }
      ],
      // 售后原因；0-质量问题；1-包装问题；
      reasonOptions: [
        {
          value: 0,
          text: this.$t('质量问题')
        },
        {
          value: 1,
          text: this.$t('包装问题')
        }
      ]
    }
  },
  mounted() {},
  filters: {
    dateFormat(value) {
      const date = new Date(Number(value))
      if (isNaN(date.getTime())) {
        return value
      } else {
        return utils.formateTime(date, 'YYYY-mm-dd HH:MM:SS')
      }
    },
    statusFormat(value) {
      const status = {
        1: this.$t('待收货'),
        2: this.$t('部分收货'),
        3: this.$t('全部收货')
      }
      if (!status[value]) {
        return value
      } else {
        return status[value]
      }
    }
  },
  methods: {
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 保存草稿
    saveDraft() {
      this.$emit('saveDraft')
    },
    // 提交
    doSubmit() {
      this.$emit('doSubmit')
    },
    isHighlight(value) {
      let highlight = false
      // 1: "待收货",2: "部分收货",
      if (value == 1 || value == 2) {
        highlight = true
      }
      return highlight
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);

      &.bold {
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 600;
        color: rgba(41, 41, 41, 1);
      }
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
