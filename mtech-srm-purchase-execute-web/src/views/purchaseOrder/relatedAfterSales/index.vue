<template>
  <!-- 关联创建售后订单 -->
  <div class="full-height pt20">
    <!-- 头部信息 -->
    <top-info
      :header-info="headerInfo"
      :entry-type="entryType"
      @doSubmit="doSubmit"
      @saveDraft="saveDraft"
      @goBack="goBack"
    ></top-info>
    <div class="bottom-tables">
      <mt-tabs
        tab-id="related-tab"
        :e-tab="false"
        :data-source="componentConfig"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
      <!-- 售后明细 -->
      <div
        class="full-height"
        v-show="currentSelectTab.name == $t('售后明细') || currentSelectTab.index == 0"
      >
        <div class="tools" v-if="entryType !== ConstantType.Look">
          <div class="switch-wrap">
            <span>{{ $t('只显示可售后行') }}</span>
            <mt-switch
              v-model="onlyDisplayAvailableAfterSalesLines"
              @change="handleSwitch"
            ></mt-switch>
          </div>
        </div>
        <mt-data-grid
          v-if="columnData && columnData.length > 0"
          ref="dataGrid"
          @cellEdit="cellEdit"
          @confirm-function="handleUploadSuccess"
          @remove-file="handleRemoveFile"
          :data-source="dataSource"
          :column-data="columnData"
          :allow-sorting="true"
          :allow-filtering="true"
          :filter-settings="filterOptions"
          :allow-reordering="true"
          :allow-paging="false"
        ></mt-data-grid>
      </div>
      <!-- 相关附件 3: 查看，1：新增、编辑 -->
      <relative-file
        v-show="currentSelectTab.name == $t('相关附件') || currentSelectTab.index == 1"
        :entry-id="orderId"
        :entry-type="entryType == ConstantType.Look ? '3' : '1'"
        :module-file-list="moduleFileList"
        :entry-file-list="entryFileList"
        @updateFile="updateFile"
      ></relative-file>
    </div>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { formatTableDynamicData, ConstantType } from './config/index.js'
import { maxPageSize } from '@/utils/constant'

export default {
  components: {
    TopInfo: require('./components/topInfo.vue').default,
    RelativeFile: () => import('@/components/businessComponents/relativeFile1/index.vue')
  },
  data() {
    return {
      ConstantType,
      filterOptions: {
        type: 'Menu'
      },
      componentConfig: [], // tab
      dataSource: [],
      dataSourceCopy: [], // 原始数据，用于比较是否数据修改了
      filterData: [], // 保存数据过滤状态，提交数据时要填充被过滤的数据回去 dataSource
      columnData: [],
      tabList: [
        {
          title: this.$t('订单明细'),
          content: '0'
        },
        {
          title: this.$t('相关附件'),
          content: '1'
        }
      ],
      orderId: '', // 订单id
      orderCode: '', // 订单号
      businessTypeCode: '', // 业务类型编码
      afterSaleType: '', // 售后类型
      afterSaleReason: '', // 售后原因
      moduleKey: '', // 模块id
      moduleFileList: [], // 相关附件用到的 tree 信息
      entryFileList: [], // 编辑带入的附件
      fileList: [], // 所有的上传的文件
      onlyDisplayAvailableAfterSalesLines: false, // 只显示可售后行
      currentSelectTab: { index: 0, name: this.$t('售后明细') },
      entryType: ConstantType.Add,
      headerInfo: {},
      apiWaitingQuantity: 0, // 调用的api正在等待数
      modifyFileIndexList: [] // 修改了文件的明细行 index
    }
  },
  mounted() {
    /**
     * 页面入参
     * type "1": 新增、"2": 编辑、"3": 查看
     * orderId localStorage(type: 2/3)/$route.query(type: 1)
     * orderCode localStorage(type: 2/3)
     * businessTypeCode localStorage(type: 2/3)
     */

    this.entryType = this.$route.query.type
    // 获取数据
    this.init()
  },
  beforeDestroy() {
    localStorage.removeItem('salesCoordinationTopInfo')
  },
  methods: {
    // 获取数据
    async init() {
      if (this.entryType == ConstantType.Add) {
        const salesCoordinationTopInfo = JSON.parse(
          localStorage.getItem('salesCoordinationTopInfo')
        )
        this.orderId = salesCoordinationTopInfo.orderId // 订单id
        this.orderCode = salesCoordinationTopInfo.orderCode // 订单号
        this.businessTypeCode = salesCoordinationTopInfo.businessTypeCode // 业务类型编码
        this.afterSaleType = salesCoordinationTopInfo.afterSaleType // 售后类型
        this.afterSaleReason = salesCoordinationTopInfo.afterSaleReason // 售后原因
        // 顶部信息获取
        this.getPurOrderCode()
        // 表格动态列、tab 数据、相关附件左边
        this.getPebusinessConfig()
        // 表格数据
        this.postBuyerSaleServiceOrderDetailInitDetail()
      } else {
        this.orderId = this.$route.query.orderId
        // 顶部信息获取
        await this.getBuyerSaleServiceOrderId()
        // 表格动态列、tab 数据
        this.getBuyerSaleServiceOrderModuleConfig()
        // 相关附件左边
        this.getFileNodeByDocId()
        // 表格数据
        await this.postBuyerSaleServiceOrderDetailDetail()
        // 相关附件数据
        this.queryFileByDocId()
      }
    },
    // 只显示可售后行
    handleSwitch(e) {
      if (e) {
        this.filterData = [] // 清空过滤数据
        const dataSourceLength = this.dataSource.length // 数组长度
        this.dataSource = this.dataSource.filter((item, index) => {
          let isFilter = false
          if (item.preAfterSaleQty && item.preAfterSaleQty > 0) {
            isFilter = true
          }
          // 保存数据过滤状态
          this.filterData.push({
            dataSourceLength,
            isFilter,
            index,
            value: item
          })
          return isFilter
        })
      } else {
        this.dataSource = this.getDataBeforeFiltering()
      }
    },
    // 获取过滤前的数据
    getDataBeforeFiltering() {
      let data = this.dataSource
      if (this.filterData.length > 0) {
        const array = new Array(this.filterData[0].dataSourceLength)
        this.filterData.forEach((itemFilter) => {
          array.fill(itemFilter.value, itemFilter.index, itemFilter.index + 1)
        })
        data = array
      }

      return data
    },
    // 修改 tab
    handleSelectTab(index, name) {
      this.currentSelectTab = { index, name: name.title }
    },
    /**
     * 提交
     * 新增-提交/保存草稿，不放Modify，不传orderId
     * 编辑草稿-保存草稿，如果修改了草稿，修改的数据放Modify，固定传orderId
     * 编辑草稿-提交草稿，如果修改了草稿，修改的数据放Modify，固定传orderId
     */
    doSubmit() {
      let data = this.dataSource
      // 如果在过滤状态
      if (this.onlyDisplayAvailableAfterSalesLines) {
        data = this.getDataBeforeFiltering()
      }
      // 头部信息
      const headerData = {
        businessTypeCode: this.headerInfo.businessTypeCode, // 业务类型编码
        businessTypeId: this.headerInfo.businessTypeId, // 业务类型
        businessTypeName: this.headerInfo.businessTypeName, // 业务类型名称
        buyerOrgCode: this.headerInfo.buyerOrgCode, // 采购组织编码
        buyerOrgId: this.headerInfo.buyerOrgId, // 采购组织id
        buyerOrgName: this.headerInfo.buyerOrgName, // 采购组织名称
        buyerUserCode: this.headerInfo.buyerUserCode, // 采购员编码
        buyerUserId: this.headerInfo.buyerUserId, // 采购员id
        buyerUserName: this.headerInfo.buyerUserName, // 采购员姓名
        companyCode: this.headerInfo.companyCode, // 公司编码
        companyId: this.headerInfo.companyId, // 公司id
        companyName: this.headerInfo.companyName, // 公司名称
        currencyCode: this.headerInfo.currencyCode, // 币种编码
        currencyId: this.headerInfo.currencyId, // 币种id
        currencyName: this.headerInfo.currencyName, // 币种名称
        deliveryStatus: this.headerInfo.deliveryStatus, // 供方发货状态：0-未发货；1-部分发货；2-全部发货；3-无需发货
        dueDate: String(this.headerInfo.dueDate.valueOf()), // 要求完成日期
        feedbackStatus: this.headerInfo.feedbackStatus, // 供应商反馈状态：0-未反馈；1-供应商接受；2-供应商拒绝
        freeTotal: this.headerInfo.freeTotal, // 未税总价
        id: this.headerInfo.id, // 主键id
        invoiceId: this.headerInfo.invoiceId, // 发票id
        orderCode: this.headerInfo.orderCode, // 订单号
        paymentCode: this.headerInfo.paymentCode, // 付款条件编码
        paymentName: this.headerInfo.paymentName, // 付款条件名称
        publishStatus: this.headerInfo.publishStatus, // 发布状态：0-未发布；1-已发布
        purDeliveryStatus: this.headerInfo.purDeliveryStatus, // 采方发货状态：0-未发货；1-部分发货；2-全部发货
        purOrderCode: this.headerInfo.purOrderCode, // 采购订单订单号
        reason: this.headerInfo.reason, // 售后原因；0-质量问题；1-包装问题；
        receiveStatus: this.headerInfo.receiveStatus, // 收货状态：0-未收货；1-部分收货；2全部收货
        relateOrderCode: this.headerInfo.relateOrderCode, // 是否关联采购订单 0-不关联；1-关联
        remark: this.headerInfo.remark, // 备注
        settlementId: this.headerInfo.settlementId, // 结算方id
        settlementName: this.headerInfo.settlementName, // 结算方名称
        source: this.headerInfo.source, // 订单来源 0-采购订单转化；1-手动创建；
        status: 1, // 单据状态：0-草稿；1-待审批；2-审批通过；3-审批拒绝；4-关闭；5-完成
        supplierCode: this.headerInfo.supplierCode, // 供应商编码
        supplierId: this.headerInfo.supplierId, // 供应商id
        supplierName: this.headerInfo.supplierName, // 供应商名称
        taxTotal: this.headerInfo.taxTotal, // 含税总价
        // tenantId: this.headerInfo.tenantId, // 租户id
        // tenantName: this.headerInfo.tenantName, // 租户名称
        type: this.headerInfo.type, // 订单类型；0-退货订单；1-换货订单；2-维修订单
        warehouseStatus: this.headerInfo.warehouseStatus // 入库状态 0-未入库；1-部分入库；2-全部入库
      }
      // 整理明细数据
      const orderDetails = []
      const orderDetailsModify = []
      data.forEach((item, index) => {
        const orderDetail = {
          afterSaleNum: item.afterSaleNum, // 售后数量
          buyerDepCode: item.buyerDepCode, // 采购单位编码
          buyerDepId: item.buyerDepId, // 采购单位id
          buyerDepName: item.buyerDepName, // 采购单位名称
          buyerOrgCode: item.buyerOrgCode, // 采购组编码
          buyerOrgId: item.buyerOrgId, // 采购组id
          buyerOrgName: item.buyerOrgName, // 采购组名称
          categoryCode: item.categoryCode, // 品类编码
          categoryId: item.categoryId, // 品类id
          categoryName: item.categoryName, // 品类名称
          confirmStatus: item.confirmStatus, // 供应商确认状态：0-待确认；1-确认；2-拒绝
          consignee: item.consignee, // 收货人
          consigneeId: item.consigneeId, // 收货人id
          contact: item.contact, // 联系方式
          contactId: item.contactId, // 联系方式id
          currencyCode: item.currencyCode, // 币种编码
          currencyId: item.currencyId, // 币种id
          currencyName: item.currencyName, // 币种名称
          deliveryQty: item.deliveryQty, // 已发货数量
          deliveryStatus: item.deliveryStatus, // 发货状态：0-未发货；1-部分发货；2-全部发货
          fieldDataList: item.fieldDataList, // 单据模块扩展字段列表
          freePrice: item.freePrice, // 不含税单价
          freeTotal: item.freeTotal, // 不含税总价
          id: item.id, // 主键id
          itemCode: item.itemCode, // 品项编码
          itemId: item.itemId, // 品项id
          itemName: item.itemName, // 品项名称
          itemNo: item.itemNo, // 行号
          orderFiles: item.orderFiles, // 订单附件
          orderId: item.orderId, // 订单id（mt_order表id）
          packageDesc: item.packageDesc, // 包装说明
          packageMethod: item.packageMethod, // 包装方式
          packageSpec: item.packageSpec, // 包装规格
          postingAccount: item.postingAccount, // 入账科目
          preDeliveryQty: item.preDeliveryQty, // 待发货数量
          preReceiveQty: item.preReceiveQty, // 待收货数量
          productCode: item.productCode, // 涉及产品系列编码
          productName: item.productName, // 涉及产品系列名称
          purItemNo: item.purItemNo, // 采购行号
          purUnitCode: item.purUnitCode, // 采购单位编码
          purUnitId: item.purUnitId, // 采购单位id
          purUnitName: item.purUnitName, // 采购单位名称
          qualityExemptionMarkCode: item.qualityExemptionMarkCode, // 质量免检标识编码
          qualityExemptionMarkId: item.qualityExemptionMarkId, // 质量免检标识id
          qualityExemptionMarkName: item.qualityExemptionMarkName, // 质量免检标识名称
          quantity: item.quantity, // 订单数量
          receiveAddress: item.receiveAddress, // 收货地址
          receiveAddressId: item.receiveAddressId, // 收货地址id
          receiveQty: item.receiveQty, // 已收货数量
          receiveSiteCode: item.receiveSiteCode, // 收货工厂/地点编码
          receiveSiteId: item.receiveSiteId, // 收货工厂/地点id
          receiveSiteName: item.receiveSiteName, // 收货工厂/地点名称
          receiveStatus: item.receiveStatus, // 收货状态：0-未收货；1-部分收货；2-全部收货
          shippingMethodCode: item.shippingMethodCode, // 物流方式编码
          shippingMethodName: item.shippingMethodName, // 物流方式名称
          siteCode: item.siteCode, // 工厂编码
          siteId: item.siteId, // 工厂id
          siteName: item.siteName, // 工厂名称
          skuCode: item.skuCode, // sku编码
          skuId: item.skuId, // sku ID
          skuName: item.skuName, // sku名称
          skuPicUrl: item.skuPicUrl, // sku图片地址
          specification: item.specification, // 规格型号
          taxPrice: item.taxPrice, // 含税单价
          taxTotal: item.taxTotal, // 含税总价
          taxid: item.taxid, // 税率
          tenantId: item.tenantId, // 租户id
          tradeClauseCode: item.tradeClauseCode, // 贸易条款编码
          tradeClauseId: item.tradeClauseId, // 贸易条款id
          tradeClauseName: item.tradeClauseName, // 贸易条款名称
          unitCode: item.unitCode, // 基本单位编码
          unitId: item.unitId, // 基本单位id
          unitName: item.unitName, // 基本单位名称
          warehouse: item.warehouse, // 收货仓库
          warehouseId: item.warehouseId, // 收货仓库id
          warehouseStatus: item.warehouseStatus // 入库状态 0-未入库；1-部分入库；2-全部入库
        }
        if (
          this.entryType != ConstantType.Add &&
          this.diff({ index, key: 'afterSaleNum', value: item.afterSaleNum })
        ) {
          orderDetailsModify.push(orderDetail)
        } else {
          orderDetails.push(orderDetail)
        }
      })
      const parms = {
        moduleKey: this.moduleKey, // 模块id
        order: headerData, // 订单主单 头部信息
        orderDetails: this.entryType == ConstantType.Add ? orderDetails : [], // 订单明细
        orderDetailsModify: orderDetailsModify, // 订单明细 修改
        orderFiles: this.fileList, // 相关附件 上传的文件
        orderId: this.entryType != ConstantType.Add ? this.orderId : undefined, // 订单主单id
        source: 0 // 订单来源 0-采购订单转化；1-手动创建
      }
      // 保存售后订单
      this.apiStartLoading()
      this.$API.purchaseOrder
        .postBuyerSaleServiceOrderSave(parms)
        .then(() => {
          this.$router.push({
            name: 'sales-coordination',
            query: {}
          })
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    /**
     * 保存草稿
     * 新增-提交/保存草稿，不放Modify，不传orderId
     * 编辑草稿-保存草稿，如果修改了草稿，修改的数据放Modify，固定传orderId
     * 编辑草稿-提交草稿，如果修改了草稿，修改的数据放Modify，固定传orderId
     */
    saveDraft() {
      let data = this.dataSource
      // 如果在过滤状态
      if (this.onlyDisplayAvailableAfterSalesLines) {
        data = this.getDataBeforeFiltering()
      }
      // 头部信息
      const headerData = {
        businessTypeCode: this.headerInfo.businessTypeCode, // 业务类型编码
        businessTypeId: this.headerInfo.businessTypeId, // 业务类型
        businessTypeName: this.headerInfo.businessTypeName, // 业务类型名称
        buyerOrgCode: this.headerInfo.buyerOrgCode, // 采购组织编码
        buyerOrgId: this.headerInfo.buyerOrgId, // 采购组织id
        buyerOrgName: this.headerInfo.buyerOrgName, // 采购组织名称
        buyerUserCode: this.headerInfo.buyerUserCode, // 采购员编码
        buyerUserId: this.headerInfo.buyerUserId, // 采购员id
        buyerUserName: this.headerInfo.buyerUserName, // 采购员姓名
        companyCode: this.headerInfo.companyCode, // 公司编码
        companyId: this.headerInfo.companyId, // 公司id
        companyName: this.headerInfo.companyName, // 公司名称
        currencyCode: this.headerInfo.currencyCode, // 币种编码
        currencyId: this.headerInfo.currencyId, // 币种id
        currencyName: this.headerInfo.currencyName, // 币种名称
        deliveryStatus: this.headerInfo.deliveryStatus, // 供方发货状态：0-未发货；1-部分发货；2-全部发货；3-无需发货
        dueDate: String(this.headerInfo.dueDate.valueOf()), // 要求完成日期
        feedbackStatus: this.headerInfo.feedbackStatus, // 供应商反馈状态：0-未反馈；1-供应商接受；2-供应商拒绝
        freeTotal: this.headerInfo.freeTotal, // 未税总价
        id: this.headerInfo.id, // 主键id
        invoiceId: this.headerInfo.invoiceId, // 发票id
        orderCode: this.headerInfo.orderCode, // 订单号
        paymentCode: this.headerInfo.paymentCode, // 付款条件编码
        paymentName: this.headerInfo.paymentName, // 付款条件名称
        publishStatus: this.headerInfo.publishStatus, // 发布状态：0-未发布；1-已发布
        purDeliveryStatus: this.headerInfo.purDeliveryStatus, // 采方发货状态：0-未发货；1-部分发货；2-全部发货
        purOrderCode: this.headerInfo.purOrderCode, // 采购订单订单号
        reason: this.headerInfo.reason, // 售后原因；0-质量问题；1-包装问题；
        receiveStatus: this.headerInfo.receiveStatus, // 收货状态：0-未收货；1-部分收货；2全部收货
        relateOrderCode: this.headerInfo.relateOrderCode, // 是否关联采购订单 0-不关联；1-关联
        remark: this.headerInfo.remark, // 备注
        settlementId: this.headerInfo.settlementId, // 结算方id
        settlementName: this.headerInfo.settlementName, // 结算方名称
        source: this.headerInfo.source, // 订单来源 0-采购订单转化；1-手动创建；
        status: 0, // 单据状态：0-草稿；1-待审批；2-审批通过；3-审批拒绝；4-关闭；5-完成
        supplierCode: this.headerInfo.supplierCode, // 供应商编码
        supplierId: this.headerInfo.supplierId, // 供应商id
        supplierName: this.headerInfo.supplierName, // 供应商名称
        taxTotal: this.headerInfo.taxTotal, // 含税总价
        // tenantId: this.headerInfo.tenantId, // 租户id
        // tenantName: this.headerInfo.tenantName, // 租户名称
        type: this.headerInfo.type, // 订单类型；0-退货订单；1-换货订单；2-维修订单
        warehouseStatus: this.headerInfo.warehouseStatus // 入库状态 0-未入库；1-部分入库；2-全部入库
      }
      // 整理明细数据
      const orderDetails = []
      const orderDetailsModify = []
      data.forEach((item, index) => {
        const orderDetail = {
          afterSaleNum: item.afterSaleNum, // 售后数量
          buyerDepCode: item.buyerDepCode, // 采购单位编码
          buyerDepId: item.buyerDepId, // 采购单位id
          buyerDepName: item.buyerDepName, // 采购单位名称
          buyerOrgCode: item.buyerOrgCode, // 采购组编码
          buyerOrgId: item.buyerOrgId, // 采购组id
          buyerOrgName: item.buyerOrgName, // 采购组名称
          categoryCode: item.categoryCode, // 品类编码
          categoryId: item.categoryId, // 品类id
          categoryName: item.categoryName, // 品类名称
          confirmStatus: item.confirmStatus, // 供应商确认状态：0-待确认；1-确认；2-拒绝
          consignee: item.consignee, // 收货人
          consigneeId: item.consigneeId, // 收货人id
          contact: item.contact, // 联系方式
          contactId: item.contactId, // 联系方式id
          currencyCode: item.currencyCode, // 币种编码
          currencyId: item.currencyId, // 币种id
          currencyName: item.currencyName, // 币种名称
          deliveryQty: item.deliveryQty, // 已发货数量
          deliveryStatus: item.deliveryStatus, // 发货状态：0-未发货；1-部分发货；2-全部发货
          fieldDataList: item.fieldDataList, // 单据模块扩展字段列表
          freePrice: item.freePrice, // 不含税单价
          freeTotal: item.freeTotal, // 不含税总价
          id: item.id, // 主键id
          itemCode: item.itemCode, // 品项编码
          itemId: item.itemId, // 品项id
          itemName: item.itemName, // 品项名称
          itemNo: item.itemNo, // 行号
          orderFiles: item.orderFiles, // 订单附件
          orderId: item.orderId, // 订单id（mt_order表id）
          packageDesc: item.packageDesc, // 包装说明
          packageMethod: item.packageMethod, // 包装方式
          packageSpec: item.packageSpec, // 包装规格
          postingAccount: item.postingAccount, // 入账科目
          preDeliveryQty: item.preDeliveryQty, // 待发货数量
          preReceiveQty: item.preReceiveQty, // 待收货数量
          productCode: item.productCode, // 涉及产品系列编码
          productName: item.productName, // 涉及产品系列名称
          purItemNo: item.purItemNo, // 采购行号
          purUnitCode: item.purUnitCode, // 采购单位编码
          purUnitId: item.purUnitId, // 采购单位id
          purUnitName: item.purUnitName, // 采购单位名称
          qualityExemptionMarkCode: item.qualityExemptionMarkCode, // 质量免检标识编码
          qualityExemptionMarkId: item.qualityExemptionMarkId, // 质量免检标识id
          qualityExemptionMarkName: item.qualityExemptionMarkName, // 质量免检标识名称
          quantity: item.quantity, // 订单数量
          receiveAddress: item.receiveAddress, // 收货地址
          receiveAddressId: item.receiveAddressId, // 收货地址id
          receiveQty: item.receiveQty, // 已收货数量
          receiveSiteCode: item.receiveSiteCode, // 收货工厂/地点编码
          receiveSiteId: item.receiveSiteId, // 收货工厂/地点id
          receiveSiteName: item.receiveSiteName, // 收货工厂/地点名称
          receiveStatus: item.receiveStatus, // 收货状态：0-未收货；1-部分收货；2-全部收货
          shippingMethodCode: item.shippingMethodCode, // 物流方式编码
          shippingMethodName: item.shippingMethodName, // 物流方式名称
          siteCode: item.siteCode, // 工厂编码
          siteId: item.siteId, // 工厂id
          siteName: item.siteName, // 工厂名称
          skuCode: item.skuCode, // sku编码
          skuId: item.skuId, // sku ID
          skuName: item.skuName, // sku名称
          skuPicUrl: item.skuPicUrl, // sku图片地址
          specification: item.specification, // 规格型号
          taxPrice: item.taxPrice, // 含税单价
          taxTotal: item.taxTotal, // 含税总价
          taxid: item.taxid, // 税率
          tenantId: item.tenantId, // 租户id
          tradeClauseCode: item.tradeClauseCode, // 贸易条款编码
          tradeClauseId: item.tradeClauseId, // 贸易条款id
          tradeClauseName: item.tradeClauseName, // 贸易条款名称
          unitCode: item.unitCode, // 基本单位编码
          unitId: item.unitId, // 基本单位id
          unitName: item.unitName, // 基本单位名称
          warehouse: item.warehouse, // 收货仓库
          warehouseId: item.warehouseId, // 收货仓库id
          warehouseStatus: item.warehouseStatus // 入库状态 0-未入库；1-部分入库；2-全部入库
        }
        if (
          this.entryType != ConstantType.Add &&
          this.diff({ index, key: 'afterSaleNum', value: item.afterSaleNum })
        ) {
          orderDetailsModify.push(orderDetail)
        } else {
          orderDetails.push(orderDetail)
        }
      })
      const parms = {
        moduleKey: this.moduleKey, // 模块id
        order: headerData, // 订单主单 头部信息
        orderDetails: this.entryType == ConstantType.Add ? orderDetails : [], // 订单明细
        orderDetailsModify: orderDetailsModify, // 订单明细 修改
        orderFiles: this.fileList, // 相关附件 上传的文件
        orderId: this.entryType != ConstantType.Add ? this.orderId : undefined, // 订单主单id
        source: 0 // 订单来源 0-采购订单转化；1-手动创建
      }
      // 保存草稿售后订单
      this.apiStartLoading()
      this.$API.purchaseOrder
        .postBuyerSaleServiceOrderDraft(parms)
        .then(() => {
          this.$router.push({
            name: 'sales-coordination',
            query: {}
          })
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 返回
    goBack() {
      this.$router.push({
        name: 'sales-coordination',
        query: {}
      })
    },
    // 行编辑
    cellEdit(e) {
      // 更新当前页 dataSource
      let _dataSource = cloneDeep(this.dataSource)
      _dataSource[e.index][e.key] = e.value
      this.dataSource = _dataSource
    },
    // 表格数据, 新增, 售后订单明细列表-init
    postBuyerSaleServiceOrderDetailInitDetail() {
      // 新增 售后订单明细列表 init
      this.apiStartLoading()
      this.$API.purchaseOrder
        .postBuyerSaleServiceOrderDetailInitDetail({
          requestParams: { page: { current: 1, size: maxPageSize } },
          moduleId: 1,
          source: 0,
          orderId: this.orderId,
          purOrderCode: this.orderCode
        })
        .then((res) => {
          if (res?.data) {
            this.dataSource = res.data // 表格数据
            this.dataSourceCopy = cloneDeep(this.dataSource) // 保留初始数据
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 表格数据, 编辑、查看、编辑草稿, 售后订单明细列表
    postBuyerSaleServiceOrderDetailDetail() {
      this.apiStartLoading()
      return this.$API.purchaseOrder
        .postBuyerSaleServiceOrderDetailDetail({
          requestParams: { page: { current: 1, size: maxPageSize } },
          moduleId: 1,
          source: 0,
          orderId: this.orderId,
          purOrderCode: this.headerInfo.purOrderCode
        })
        .then((res) => {
          if (res?.data?.records) {
            this.dataSource = res.data.records // 表格数据
            this.dataSourceCopy = cloneDeep(this.dataSource) // 保留初始数据
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 顶部信息获取, 新增, 采方根据订单编号获取订单
    getPurOrderCode() {
      const currentBu = localStorage.getItem('currentBu') || 'KT'
      this.apiStartLoading()
      this.$API.purchaseOrder
        .getPurOrderCode(this.orderCode, currentBu)
        .then((res) => {
          if (res && res.data) {
            this.headerInfo = {
              ...res.data,
              relateOrderCode: 1, // 是否关联采购订单 0-不关联；1-关联
              purOrderCode: this.orderCode, // 关联采购订单
              type: this.afterSaleType, // 订单类型-售后类型
              reason: this.afterSaleReason, // 售后原因；0-质量问题；1-包装问题；
              dueDate: new Date(Number(res.data.requiredDeliveryDate)), // 采购获取的 "要求交期" 转换
              requiredDeliveryDate: new Date(Number(res.data.requiredDeliveryDate))
            }
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 顶部信息获取, 编辑、查看、编辑草稿, 采方根据订单id获取售后订单
    getBuyerSaleServiceOrderId() {
      this.apiStartLoading()
      return this.$API.purchaseOrder
        .getBuyerSaleServiceOrderId(this.orderId)
        .then((res) => {
          if (res && res.data) {
            this.headerInfo = {
              ...res.data,
              dueDate: new Date(Number(res.data.dueDate)) // 采购获取的 "要求交期" 转换
            }
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 表格动态列、tab 数据、相关附件左边, 新增, 业务配置详情
    getPebusinessConfig() {
      const params = {
        businessTypeCode: this.businessTypeCode,
        docType: 'as_po'
      }
      // 业务配置详情
      this.apiStartLoading()
      this.$API.purchaseOrder
        .getPebusinessConfig(params)
        .then((res) => {
          // 相关附件左边
          if (res?.data?.moduleFileList) {
            this.moduleFileList = res.data.moduleFileList.filter(
              (item) => item.nodeCode !== 'as_po_item_file'
            )
          }
          if (res?.data?.modules) {
            res.data.modules.forEach((item) => {
              // tab 数据
              this.componentConfig.push({
                title: item.moduleName,
                moduleType: item.moduleType,
                moduleKey: item.moduleKey,
                moduleId: item.moduleId
              })

              // 售后明细
              if (item.moduleType === 11) {
                this.columnData = formatTableDynamicData({
                  data: item.fields,
                  entryType: this.entryType
                }) // 表格动态列
                this.moduleKey = item.moduleKey // 明细模块id
              }
            })
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 表格列动态获取, tab 数据, 编辑、查看、编辑草稿, 获取采购订单单据模块定义
    getBuyerSaleServiceOrderModuleConfig() {
      this.apiStartLoading()
      this.$API.purchaseOrder
        .getBuyerSaleServiceOrderModuleConfig({
          docId: this.orderId
        })
        .then((res) => {
          if (res?.data?.moduleItems) {
            res?.data?.moduleItems.forEach((item) => {
              // tab 数据
              this.componentConfig.push({
                title: item.moduleName,
                moduleType: item.moduleType,
                moduleKey: item.moduleKey,
                moduleId: item.moduleId
              })
              // 售后明细
              if (item.moduleType === 11) {
                this.columnData = formatTableDynamicData({
                  data: item.fieldDefines,
                  entryType: this.entryType
                }) // 表格动态列
                this.moduleKey = item.moduleKey // 明细模块id
              }
            })
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 相关附件左边, 编辑、查看、编辑草稿场合, 根据申请主单查询所有文件节点信息
    getFileNodeByDocId() {
      let params = {
        docId: this.orderId,
        docType: 'as_po'
      }
      this.apiStartLoading()
      this.$API.purchaseOrder
        .getFileNodeByDocId(params)
        .then((res) => {
          if (res?.data) {
            this.moduleFileList = res.data.filter((item) => item.nodeCode !== 'as_po_item_file')
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 相关附件数据 根据docId-节点id查询所有文件信息
    queryFileByDocId() {
      const params = {
        docId: this.orderId,
        parentId: 0,
        docType: 'as_po'
      }
      this.apiStartLoading()
      this.$API.purchaseOrder
        .queryFileByDocId(params)
        .then((res) => {
          const list = []
          res.data.forEach((item) => {
            // 如果是文件
            if (item && item.nodeType == 1) {
              list.push(item)
            }
          })
          this.entryFileList = list
          this.dealWithRowItemFile(this.entryFileList)
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 处理 明细行附件数据
    dealWithRowItemFile(fileList) {
      if (fileList) {
        const _dataSource = cloneDeep(this.dataSource)
        const rowItemFile = []
        fileList.forEach((itemFile) => {
          // 订单明细附件
          if (itemFile.nodeCode === 'as_po_item_file') {
            for (let i = 0; i < _dataSource.length; i++) {
              if (itemFile.fileDetailId == _dataSource[i].id) {
                _dataSource[i].file = itemFile // 给组件显示用
                _dataSource[i].orderFiles = [itemFile] // api 参数用
                break
              }
            }
            rowItemFile.push(itemFile)
          }
        })
        this.dataSource = _dataSource
      }
    },
    // 明细行表格 文件上传成功
    handleUploadSuccess(params) {
      const { rowIndex, fileInfo, id } = JSON.parse(params)
      this.modifyFileIndexList.push(rowIndex)
      if (this.dataSource[rowIndex] && this.dataSource[rowIndex].id == id) {
        const _dataSource = cloneDeep(this.dataSource)
        // 明细行表格 文件数据
        _dataSource[rowIndex].orderFiles = [
          {
            docId: 0,
            docType: 'as_po',
            fileName: fileInfo.fileName,
            fileSize: fileInfo.fileSize,
            fileType: fileInfo.fileType,
            id: fileInfo.id,
            itemNo: 0,
            lineNo: 0,
            nodeCode: 'as_po_item_file',
            nodeName: this.$t('售后明细附件'),
            nodeType: 1,
            orderDetailId: id || 0,
            orderId: this.orderId || 0,
            parentId: 0,
            remark: '',
            remoteFileId: fileInfo.id,
            sysFileId: fileInfo.id,
            type: 0,
            url: fileInfo.url
          }
        ]
        // 明细行表格 文件显示
        _dataSource[rowIndex].file = _dataSource[rowIndex].orderFiles[0]
        this.dataSource = _dataSource
      }
    },
    // 明细行表格 文件删除
    handleRemoveFile(params) {
      const { rowIndex } = JSON.parse(params)
      this.modifyFileIndexList.push(rowIndex)
      if (this.dataSource[rowIndex]) {
        const _dataSource = cloneDeep(this.dataSource)
        // 明细行表格 文件数据
        _dataSource[rowIndex].orderFiles = []
        // 明细行表格 文件显示
        _dataSource[rowIndex].file = {}
        this.dataSource = _dataSource
      }
    },
    // 判断是否有修改数据
    diff(dataObj) {
      const { index, key, value } = dataObj
      let isDiff = false
      // 如果要比较的字段与原来的值不一样
      if (this.dataSourceCopy[index][key] !== value) {
        isDiff = true
      }
      // 如果上传了新的附件
      if (this.modifyFileIndexList.includes(index)) {
        isDiff = true
      }

      return isDiff
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    },
    updateFile(e) {
      this.fileList = e
    }
  }
}
</script>
<style lang="scss" scoped>
.bottom-tables {
  .mt-tabs,
  .mt-tabs /deep/ .mt-tabs-container {
    width: 100%;
  }
}

.tools {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background: var(--plugin-dg-bg-ff);
  padding: 20px 20px 10px 0;

  .switch-wrap {
    display: flex;
    justify-content: flex-end;

    span {
      margin-right: 14px;
    }
  }
}
</style>
