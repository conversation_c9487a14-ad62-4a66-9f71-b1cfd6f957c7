<template>
  <div class="table-area">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      max-height="610px"
      @reset="handleCustomReset"
      @search="handleCustomSearch('handle')"
    >
      <mt-form ref="searchFormRef">
        <template v-for="item in searchConfigs">
          <mt-form-item
            v-if="item.title && item.ignore !== true"
            :key="item.field"
            :prop="item.field"
            :label="item.title"
            label-style="top"
          >
            <mt-multi-select
              v-if="item.searchOptions && item.searchOptions.elementType === 'multi-select'"
              style="flex: 1"
              v-model="item.searchOptions.value"
              :fields="item.searchOptions.fields"
              :show-select-all="true"
              :allow-filtering="true"
              filter-type="Contains"
              :data-source="item.searchOptions.dataSource"
              :placeholder="$t('请选择')"
            ></mt-multi-select>
            <mt-select
              v-else-if="item.searchOptions && item.searchOptions.elementType === 'select'"
              style="flex: 1"
              v-model="item.searchOptions.value"
              :fields="item.searchOptions.fields"
              :allow-filtering="true"
              filter-type="Contains"
              :data-source="item.searchOptions.dataSource"
              :placeholder="$t('请选择')"
            ></mt-select>
            <RemoteAutocomplete
              v-else-if="
                item.searchOptions && item.searchOptions.elementType === 'remote-autocomplete'
              "
              v-model="item.searchOptions.value"
              :url="item.searchOptions.url"
              :placeholder="$t('请选择')"
              :fields="item.searchOptions.fields"
              :multiple="item.searchOptions.multiple"
              :params="item.searchOptions.params"
              :search-fields="item.searchOptions.searchFields"
              :records-position="item.searchOptions.recordsPosition"
            ></RemoteAutocomplete>
            <mt-date-range-picker
              v-else-if="item.searchOptions && item.searchOptions.elementType === 'date-range'"
              :open-on-focus="true"
              :show-clear-button="item.searchOptions.clearButton"
              :allow-edit="false"
              v-model="item.searchOptions.value"
              :placeholder="item.searchOptions.placeholder"
            ></mt-date-range-picker>
            <div
              v-else-if="item.searchOptions && item.searchOptions.elementType === 'number'"
              style="display: flex"
            >
              <div class="operator-list">
                <mt-select
                  v-model="item.searchOptions.operator"
                  :data-source="item.searchOptions.dataSource"
                  popup-width="50px"
                ></mt-select>
              </div>
              <div class="custom-input-number">
                <mt-input-number
                  v-model="item.searchOptions.value"
                  type="number"
                  :show-spin-button="false"
                  :show-clear-button="item.searchOptions.clearButton"
                  :placeholder="item.searchOptions.placeholder"
                />
              </div>
            </div>
            <mt-input
              v-else
              v-model="item.searchOptions.value"
              :placeholder="item.searchOptions.placeholder"
            />
          </mt-form-item>
        </template>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <ScTable
      ref="xTable"
      :row-config="{ height: 50 }"
      :columns="columns"
      :table-data="tableData"
      height="auto"
      header-align="left"
      grid-id="e390d1d3-2702-49ae-aded-087153c02145"
      align="left"
      show-overflow
      style="padding-top: unset"
      :scroll-x="{ gt: 0, oSize: 20 }"
      :scroll-y="{ gt: 0, oSize: 10 }"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          v-permission="item.permission"
          size="small"
          @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #acceptanceStatusDefault="{ row }">
        <div style="color: #2783fe">
          <div style="cursor: pointer">
            <span
              :class="
                acceptanceStatusList.filter((i) => i.value === row.acceptanceStatus)[0]['cssClass']
              "
              >{{
                acceptanceStatusList.filter((i) => i.value === row.acceptanceStatus)[0]['text']
              }}</span
            >
          </div>
        </div>
      </template>
      <template #fileDefault="{ row }">
        <cellFileView :row-data="row" />
      </template>
    </ScTable>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import cellFileView from '../components/cellFileView.vue' // 单元格附件查看
import ScTable from '@/components/ScTable/src/index'
import * as CONFIG from './../config/acceptancePlan.js'
import { cloneDeep } from 'lodash'
import { formatColumn, formatParams } from './../config/columnFormat'
export default {
  components: {
    ScTable,
    CollapseSearch,
    cellFileView
  },
  data() {
    return {
      acceptanceStatusList: CONFIG.acceptanceStatusList,
      searchConfigs: cloneDeep(formatColumn(CONFIG.columnData)),
      columns: formatColumn(CONFIG.columnData),
      tableData: [],
      forecastPageCurrent: 1,
      forecastPageSettings: {
        currentPage: 1,
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      toolbar: CONFIG.Toolbar
    }
  },
  methods: {
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectRecords = $grid.getCheckboxRecords() || []
      if (selectRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const ids = []
      selectRecords?.forEach((item) => {
        ids.push(item.id)
      })
      if (code === 'InvoiceAdvance') {
        this.handleInvoiceAdvance(ids, selectRecords)
      } else if (code === 'CancleInvoiceAdvance') {
        this.handleCancleInvoiceAdvance(ids, selectRecords)
      } else if (code === 'SharedPrepaid') {
        this.handleSharedPrepaid(ids, selectRecords)
      } else if (code === 'Reimbursement') {
        this.handleReimbursement(ids, selectRecords)
      }
    },
    handleReimbursement(ids, selectRows) {
      //挂账报销
      if (ids.length !== 1) {
        this.$toast({
          content: this.$t('请选择一行数据进行操作!'),
          type: 'warning'
        })
        return
      }
      if (
        selectRows[0].chargeStatus !== 1 ||
        selectRows[0].acceptanceStatus !== 1 ||
        selectRows[0].advanceInvoiceStatus !== 1
      ) {
        //预付标识是 且验收后
        this.$toast({
          content: this.$t('请选择是否挂账标识为是 且 提前开票且已验收的行!'),
          type: 'warning'
        })
        return
      }
      console.log(this.$t('挂账报销'), ids, selectRows)
      let params = { acceptanceId: ids[0] }
      this.$API.purchaseOrder.pushReimbursement(params).then(() => {
        this.$toast({ content: this.$t('挂账报销操作成功'), type: 'success' })
        this.handleCustomSearch()
      })
    },
    handleSharedPrepaid(ids, selectRows) {
      //共享预付
      if (ids.length > 50) {
        this.$toast({
          content: this.$t('请选择最多50行进行操作!'),
          type: 'warning'
        })
        return
      }
      let hasOne = selectRows.some((item) => {
        return item.advancePayStatus !== 1 || item.acceptanceStatus !== 1
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择预付标识为是且已验收的行进行操作!'),
          type: 'warning'
        })
        return
      }
      // if (
      //   selectRows[0].advancePayStatus !== 1 ||
      //   selectRows[0].acceptanceStatus !== 1
      // ) {
      //   //预付标识是 且验收后
      //   this.$toast({
      //     content: this.$t("请选择预付标识为是且已验收的行进行操作!"),
      //     type: "warning",
      //   });
      //   return;
      // }
      let params = { acceptanceIdList: ids }
      this.$API.purchaseOrder.pushSharedFinance(params).then(() => {
        this.$toast({ content: this.$t('共享预付操作成功'), type: 'success' })
        this.handleCustomSearch()
      })
    },
    handleCancleInvoiceAdvance(ids, selectRows) {
      //取消提前开票
      let hasOne = selectRows.some((item) => {
        return (
          item.acceptanceStatus !== 0 || item.chargeStatus !== 1 || item.advanceInvoiceStatus === 0
        )
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择未验收状态下，同时是否挂账：为是 且提前开票 的行操作!'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提前开票'),
          message: this.$t('确认提前开票选中的订单？')
        },
        success: () => {
          let params = {
            acceptanceIds: ids,
            files: []
          }
          this.$API.purchaseOrder.acceptanceUnAdvance(params).then(() => {
            this.$toast({
              content: this.$t('取消提前开票操作成功'),
              type: 'success'
            })
            this.handleCustomSearch()
          })
        }
      })
    },
    handleInvoiceAdvance(ids, selectRows) {
      //提前开票
      let hasOne = selectRows.some((item) => {
        return (
          item.acceptanceStatus !== 0 || item.chargeStatus !== 1 || item.advanceInvoiceStatus === 1
        )
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择未验收状态下，同时是否挂账：为是 且未提前开票的行操作!'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提前开票'),
          message: this.$t('确认提前开票选中的订单？')
        },
        success: () => {
          let params = {
            acceptanceIds: ids,
            files: []
          }
          this.$API.purchaseOrder.acceptanceAdvance(params).then(() => {
            this.$toast({
              content: this.$t('提前开票操作成功'),
              type: 'success'
            })
            this.handleCustomSearch()
          })
        }
      })
    },
    handleCustomReset() {
      this.searchConfigs.forEach((i) => {
        i.searchOptions = {
          ...i.searchOptions,
          value: null
        }
      })
      this.forecastPageCurrent = 1
      this.handleCustomSearch()
    },
    handleCustomSearch(handleType) {
      const rules = formatParams(this.searchConfigs)
      const params = {
        page: {
          current: handleType === 'handle' ? 1 : this.forecastPageCurrent,
          size: this.forecastPageSettings.pageSize
        },
        condition: 'and',
        rules
      }
      this.apiStartLoading()
      this.$API.purchaseCoordination
        .purAcceptanceQuery(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.currentPage = Number(res?.data?.current) || 1
            this.forecastPageSettings.totalRecordsCount = Number(total)
            this.tableData =
              res?.data?.records?.map((i) => {
                const item = {
                  ...i
                }
                item.files = item.files || []
                item.files.forEach((item1) => {
                  item1.remoteUrl = item1.url
                })
                item.file = JSON.stringify(item.files)
                let str = this.$t('未延期')
                if (
                  item.acceptanceStatus == 0 &&
                  new Date().getTime() > Number(item.preAcceptanceTime)
                ) {
                  str = this.$t('已延期')
                }
                if (
                  item.acceptanceStatus == 1 &&
                  Number(item.acceptanceTime) > Number(item.preAcceptanceTime)
                ) {
                  str = this.$t('已延期')
                }
                item.deliveryStatus1 = str
                return {
                  ...item
                }
              }) || [] // 表格数据
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .custom-input-number {
  width: 100%;
  .mt-input-number {
    width: 100%;
    margin-bottom: 0px;
    input {
      height: 23px;
      padding-right: 30px;
    }
  }
}
</style>
