<template>
  <div class="table-area">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      max-height="610px"
      @reset="handleCustomReset"
      @search="handleCustomSearch('handle')"
    >
      <mt-form ref="searchFormRef">
        <template v-for="item in searchConfigs">
          <mt-form-item
            v-if="item.title && item.ignore !== true"
            :key="item.field"
            :prop="item.field"
            :label="item.title"
            label-style="top"
          >
            <mt-multi-select
              v-if="item.searchOptions && item.searchOptions.elementType === 'multi-select'"
              style="flex: 1"
              v-model="item.searchOptions.value"
              :fields="item.searchOptions.fields"
              :show-select-all="true"
              :allow-filtering="true"
              filter-type="Contains"
              :data-source="item.searchOptions.dataSource"
              :placeholder="$t('请选择')"
            ></mt-multi-select>
            <mt-select
              v-else-if="item.searchOptions && item.searchOptions.elementType === 'select'"
              style="flex: 1"
              v-model="item.searchOptions.value"
              :fields="item.searchOptions.fields"
              :allow-filtering="true"
              filter-type="Contains"
              :data-source="item.searchOptions.dataSource"
              :placeholder="$t('请选择')"
            ></mt-select>
            <RemoteAutocomplete
              v-else-if="
                item.searchOptions && item.searchOptions.elementType === 'remote-autocomplete'
              "
              v-model="item.searchOptions.value"
              :url="item.searchOptions.url"
              :placeholder="$t('请选择')"
              :fields="item.searchOptions.fields"
              :multiple="item.searchOptions.multiple"
              :params="item.searchOptions.params"
              :search-fields="item.searchOptions.searchFields"
              :records-position="item.searchOptions.recordsPosition"
            ></RemoteAutocomplete>
            <mt-date-range-picker
              v-else-if="item.searchOptions && item.searchOptions.elementType === 'date-range'"
              :open-on-focus="true"
              :show-clear-button="item.searchOptions.clearButton"
              :allow-edit="false"
              v-model="item.searchOptions.value"
              :placeholder="item.searchOptions.placeholder"
            ></mt-date-range-picker>
            <div
              v-else-if="item.searchOptions && item.searchOptions.elementType === 'number'"
              style="display: flex"
            >
              <div class="operator-list">
                <mt-select
                  v-model="item.searchOptions.operator"
                  :data-source="item.searchOptions.dataSource"
                  popup-width="50px"
                ></mt-select>
              </div>
              <div class="custom-input-number">
                <mt-input-number
                  v-model="item.searchOptions.value"
                  type="number"
                  :show-spin-button="false"
                  :show-clear-button="item.searchOptions.clearButton"
                  :placeholder="item.searchOptions.placeholder"
                />
              </div>
            </div>
            <mt-input
              v-else
              v-model="item.searchOptions.value"
              :placeholder="item.searchOptions.placeholder"
            />
          </mt-form-item>
        </template>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <ScTable
      ref="xTable"
      :row-config="{ height: 50 }"
      :columns="columns"
      :table-data="tableData"
      grid-id="f61bb6b8-7583-484b-be40-03aa79076653"
      height="auto"
      header-align="left"
      align="left"
      show-overflow
      style="padding-top: unset"
      :scroll-x="{ gt: 0, oSize: 20 }"
      :scroll-y="{ gt: 0, oSize: 10 }"
    >
      <template #acceptanceStatusDefault="{ row }">
        <div style="color: #2783fe">
          <div style="cursor: pointer">
            <span
              :class="
                acceptanceStatusList.filter((i) => i.value === row.acceptanceStatus)[0]['cssClass']
              "
              >{{
                acceptanceStatusList.filter((i) => i.value === row.acceptanceStatus)[0]['text']
              }}</span
            >
          </div>
          <template v-for="item in myAcceptanceCellTools">
            <div
              v-if="item.visibleCondition(row)"
              style="cursor: pointer; display: inline-block; margin-right: 5px"
              v-permission="item.permission"
              :key="item.id"
            >
              <mt-icon :name="item.icon" />
              <span @click="handleClickCellTool({ tool: { id: item.id }, data: row })">{{
                item.title
              }}</span>
            </div>
          </template>
        </div>
      </template>
      <template #fileDefault="{ row }">
        <cellFileView :row-data="row" />
      </template>
    </ScTable>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />

    <acceptanceAccept
      @refreshColumns="handleCustomSearch"
      ref="acceptanceAccept"
    ></acceptanceAccept>
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import cellFileView from '../components/cellFileView.vue' // 单元格附件查看
import ScTable from '@/components/ScTable/src/index'
import * as CONFIG from './../config/acceptancePlan.js'
import { cloneDeep } from 'lodash'
import { formatColumn, formatParams } from './../config/columnFormat'
export default {
  components: {
    ScTable,
    CollapseSearch,
    cellFileView,
    acceptanceAccept: () => import('../components/acceptanceAccept')
  },
  data() {
    return {
      myAcceptanceCellTools: CONFIG.myAcceptanceCellTools,
      acceptanceStatusList: CONFIG.acceptanceStatusList,
      searchConfigs: cloneDeep(formatColumn(CONFIG.columnData)),
      columns: formatColumn(CONFIG.columnData),
      tableData: [],
      forecastPageCurrent: 1,
      forecastPageSettings: {
        currentPage: 1,
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      }
    }
  },
  methods: {
    //用来操作行按钮操作
    handleClickCellTool(e) {
      switch (e.tool.id) {
        case 'ys':
          this.handleYs(e.data)
          break
        case 'ch':
          this.handleCh(e.data)
          break
        case 'qr':
          this.handleQr(e.data)
          break
        case 'bh':
          this.handleBh(e.data)
          break
      }
    },
    // 确认
    handleQr(row) {
      this.$dialog({
        modal: () => import('../components/acceptanceConfirm.vue'),
        data: {
          title: this.$t('确认'),
          rowData: row
          // message: this.$t('确认驳回验收？')
        },
        success: (formInfo) => {
          let params = {
            ...formInfo,
            acceptanceIds: [row.id],
            acceptanceStatus: 1
          }
          this.$API.purchaseOrder.acceptanceUnConfirm(params).then(() => {
            this.$toast({
              content: this.$t('确认验收操作成功'),
              type: 'success'
            })
            this.handleCustomSearch()
          })
        }
      })
    },
    // 驳回
    handleBh(row) {
      this.$dialog({
        data: {
          title: this.$t('驳回验收'),
          message: this.$t('确认驳回验收？')
        },
        success: () => {
          let params = {
            acceptanceIds: [row.id],
            acceptanceStatus: 0
          }
          this.$API.purchaseOrder.acceptanceUnConfirm(params).then(() => {
            this.$toast({
              content: this.$t('驳回验收操作成功'),
              type: 'success'
            })
            this.handleCustomSearch()
          })
        }
      })
    },
    handleCh(row) {
      //撤销验收
      if (row.acceptanceCodeRel) {
        this.$toast({
          content: this.$t('有同步单据号，不允许撤回!'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('撤销验收'),
          message: this.$t('确认撤销验收？')
        },
        success: () => {
          let params = {
            acceptanceIds: [row.id],
            files: [],
            remark: ''
          }
          this.$API.purchaseOrder.acceptanceUnAccept(params).then(() => {
            this.$toast({
              content: this.$t('撤销验收操作成功'),
              type: 'success'
            })
            this.handleCustomSearch()
          })
        }
      })
    },
    handleYs(row) {
      //验收
      this.$refs.acceptanceAccept.dialogInit(row.id)
    },
    handleCustomReset() {
      this.searchConfigs.forEach((i) => {
        i.searchOptions = {
          ...i.searchOptions,
          value: null
        }
      })
      this.forecastPageCurrent = 1
      this.handleCustomSearch()
    },
    handleCustomSearch(handleType) {
      const rules = formatParams(this.searchConfigs)
      const params = {
        page: {
          current: handleType === 'handle' ? 1 : this.forecastPageCurrent,
          size: this.forecastPageSettings.pageSize
        },
        condition: 'and',
        rules
      }
      this.apiStartLoading()
      this.$API.purchaseCoordination
        .purAcceptanceQuery(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.currentPage = Number(res?.data?.current) || 1
            this.forecastPageSettings.totalRecordsCount = Number(total)
            this.tableData =
              res?.data?.records?.map((i) => {
                const item = {
                  ...i
                }
                item.files = item.files || []
                item.files.forEach((item1) => {
                  item1.remoteUrl = item1.url
                })
                item.file = JSON.stringify(item.files)
                let str = this.$t('未延期')
                if (
                  item.acceptanceStatus == 0 &&
                  new Date().getTime() > Number(item.preAcceptanceTime)
                ) {
                  str = this.$t('已延期')
                }
                if (
                  item.acceptanceStatus == 1 &&
                  Number(item.acceptanceTime) > Number(item.preAcceptanceTime)
                ) {
                  str = this.$t('已延期')
                }
                item.deliveryStatus1 = str
                return {
                  ...item
                }
              }) || [] // 表格数据
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .custom-input-number {
  width: 100%;
  .mt-input-number {
    width: 100%;
    margin-bottom: 0px;
    input {
      height: 23px;
      padding-right: 30px;
    }
  }
}
</style>
