<template>
  <div class="table-area">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      :show-archive="true"
      max-height="610px"
      @reset="handleCustomReset"
      @search="handleCustomSearch('handle')"
      @archiveChange="handleArchiveChange"
    >
      <mt-form ref="searchFormRef">
        <template v-for="item in searchConfigs">
          <mt-form-item
            v-if="item.title && item.ignore !== true"
            :key="item.field"
            :prop="item.field"
            :label="item.title"
            label-style="top"
          >
            <mt-multi-select
              v-if="item.searchOptions && item.searchOptions.elementType === 'multi-select'"
              style="flex: 1"
              v-model="item.searchOptions.value"
              :fields="item.searchOptions.fields"
              :show-select-all="true"
              :allow-filtering="true"
              filter-type="Contains"
              :data-source="item.searchOptions.dataSource"
              :placeholder="$t('请选择')"
            ></mt-multi-select>
            <RemoteAutocomplete
              v-else-if="
                item.searchOptions && item.searchOptions.elementType === 'remote-autocomplete'
              "
              v-model="item.searchOptions.value"
              :url="item.searchOptions.url"
              :placeholder="$t('请选择')"
              :fields="item.searchOptions.fields"
              :multiple="item.searchOptions.multiple"
              :params="item.searchOptions.params"
              :params-key="item.searchOptions.paramsKey"
              :search-fields="item.searchOptions.searchFields"
              :records-position="item.searchOptions.recordsPosition"
            ></RemoteAutocomplete>
            <mt-date-range-picker
              v-else-if="item.searchOptions && item.searchOptions.elementType === 'date-range'"
              :open-on-focus="true"
              :show-clear-button="item.searchOptions.clearButton"
              :allow-edit="false"
              v-model="item.searchOptions.value"
              :placeholder="item.searchOptions.placeholder"
            ></mt-date-range-picker>
            <div
              v-else-if="item.searchOptions && item.searchOptions.elementType === 'number'"
              style="display: flex"
            >
              <div class="operator-list">
                <mt-select
                  v-model="item.searchOptions.operator"
                  :data-source="item.searchOptions.dataSource"
                  popup-width="50px"
                ></mt-select>
              </div>
              <div class="custom-input-number">
                <mt-input-number
                  v-model="item.searchOptions.value"
                  type="number"
                  :show-spin-button="false"
                  :show-clear-button="item.searchOptions.clearButton"
                  :placeholder="item.searchOptions.placeholder"
                />
              </div>
            </div>
            <mt-input
              v-else
              v-model="item.searchOptions.value"
              :placeholder="item.searchOptions.placeholder"
            />
          </mt-form-item>
        </template>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <ScTable
      ref="xTable"
      :row-config="{ height: 50 }"
      :columns="columns"
      :table-data="tableData"
      grid-id="24374824-988f-45a7-a312-23128d2e667f"
      height="auto"
      header-align="left"
      align="left"
      show-overflow
      style="padding-top: unset"
      :scroll-x="{ gt: 0, oSize: 20 }"
      :scroll-y="{ gt: 0, oSize: 10 }"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          v-permission="item.permission"
          size="small"
          @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #orderCodeDefault="{ row }">
        <div style="color: #2783fe">
          <div style="cursor: pointer">
            <span @click="handleClickCellTitle({ field: 'orderCode', data: row })">{{
              row.orderCode
            }}</span>
          </div>
          <div v-if="visibleCondition(row)" style="cursor: pointer" v-permission="['O_02_0142']">
            <mt-icon name="icon_Editor" />
            <span @click="handleClickCellTool({ tool: { id: 'edit' }, data: row })">{{
              $t('编辑')
            }}</span>
          </div>
        </div>
      </template>
      <template #showStatusDefault="{ row }">
        <div style="color: #2783fe">
          <div style="cursor: pointer">
            <span
              :class="
                row.showStatus === '关闭'
                  ? 'col-inactive'
                  : row.showStatus === '反馈异常'
                  ? 'col-error'
                  : 'col-active'
              "
              >{{ $t(row.showStatus) }}</span
            >
          </div>
          <template v-for="item in columns.filter((i) => i.field === 'showStatus')[0]['cellTools']">
            <div
              v-if="item.visibleCondition(row)"
              style="cursor: pointer; display: inline-block; margin-right: 5px"
              v-permission="item.permission"
              :key="item.id"
            >
              <mt-icon :name="item.icon" />
              <span @click="handleClickCellTool({ tool: { id: item.id }, data: row })">{{
                item.title
              }}</span>
            </div>
          </template>
        </div>
      </template>
    </ScTable>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import * as CONFIG from './../config'
import { cloneDeep } from 'lodash'
import { formatColumn, formatParams } from './../config/columnFormat'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      searchConfigs: cloneDeep(formatColumn(CONFIG.columnData)),
      columns: formatColumn(CONFIG.columnData),
      tableData: [],
      forecastPageCurrent: 1,
      enableArchiveQuery: false,
      forecastPageSettings: {
        currentPage: 1,
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      toolbar: [
        {
          code: 'Add',
          icon: 'icon_solid_Createorder',
          name: this.$t('新增'),
          permission: ['O_02_0139']
        },
        {
          code: 'Publish',
          icon: 'icon_solid_pushorder',
          name: this.$t('发布'),
          permission: ['O_02_0140']
        },
        {
          code: 'Expedited',
          icon: 'icon_solid_Activateorder',
          name: this.$t('加急'),
          permission: ['O_02_0571']
        },
        {
          code: 'CancleExpedited',
          icon: 'icon_solid_Activateorder',
          name: this.$t('取消加急'),
          permission: ['O_02_0572']
        },
        {
          code: 'sync',
          name: this.$t('同步供方系统'),
          permission: ['O_02_1709']
        },
        {
          code: 'commitArchive',
          icon: 'icon_solid_Activateorder',
          name: this.$t('手动归档'),
          permission: ['O_02_1764']
        },
        {
          code: 'cancelArchive',
          icon: 'icon_solid_Activateorder',
          name: this.$t('取消归档'),
          permission: ['O_02_1765']
        }
      ]
    }
  },
  methods: {
    //用来操作行按钮操作
    handleClickCellTool(e) {
      switch (e.tool.id) {
        case 'btn1':
          this.handleBtn1()
          break
        case 'btn2':
          this.handleBtn2(e.data)
          break
        case 'btn3':
          this.handleBtn3(e.data)
          break
        case 'btn4':
          this.handleBtn4(e.data)
          break
        case 'edit':
          this.handleEdit(e.data)
          break
        case 'see':
          this.$router.push({
            name: 'purchase-coordination-detail',
            query: {
              orderid: e.data.id,
              type: '3'
            }
          })
          break
      }
    },
    //查看审批
    handleBtn1() {
      console.log('查看审批')
    },
    //售后
    handleBtn2() {
      // console.log("售后");
      // this.$refs.salesAddDialog.show(row);
    },
    // 关闭
    handleBtn3(row) {
      this.$dialog({
        data: {
          title: this.$t('关闭订单'),
          message: this.$t('确认关闭该订单？')
        },
        success: () => {
          // let params = {
          //   orderId: row.id,
          //   remark: row.remark,
          //   status: 4,
          // };
          let params = {
            itemNos: [],
            orderCode: row.orderCode,
            orderId: row.id
          }
          this.$API.purchaseOrder.poDetailClose(params).then(() => {
            this.$toast({ content: this.$t('关闭操作成功'), type: 'success' })
            this.handleCustomSearch()
          })
        }
      })
    },
    // 完成
    handleBtn4(row) {
      this.$dialog({
        data: {
          title: this.$t('完成订单'),
          message: this.$t('确认完成该订单？')
        },
        success: () => {
          let params = {
            orderId: row.id,
            remark: row.remark,
            status: 5
          }
          this.$API.purchaseOrder.purOrderDone(params).then(() => {
            this.$toast({ content: this.$t('完成操作成功'), type: 'success' })
            this.handleCustomSearch()
          })
        }
      })
    },
    //编辑
    handleEdit(row) {
      console.log(row, '编辑带入列表数据')
      this.$router.push({
        name: 'purchase-coordination-detail',
        query: {
          orderid: row.id,
          type: '2',
          source: row.source.toString(),
          draft: row.showStatus === this.$t('草稿') ? '1' : '2'
        }
      })
    },
    //用来跳转反馈异常
    handleClickCellTitle(e) {
      console.log('点击表格的数据事件', e)
      if (e.field === 'orderCode') {
        this.$router.push({
          name: 'purchase-coordination-detail',
          query: {
            orderid: e.data.id,
            type: '3'
          }
        })
      }
    },
    visibleCondition(data) {
      let isShow = false
      if (
        (data.showStatus === '草稿' ||
          data.showStatus === '待发布' ||
          data.showStatus === '审批不通过') &&
        data.showStatus !== '完成' &&
        data.showStatus !== '关闭' &&
        data.showStatus !== '待审批' &&
        data.source !== 3 // 3 来自SAP的数据
      ) {
        isShow = true
      }
      if (
        (data.feedbackStatus == 1 || data.feedbackStatus == 2) &&
        data.businessTypeIfEdit &&
        data.showStatus !== '完成' &&
        data.showStatus !== '关闭' &&
        data.showStatus !== '待审批' &&
        data.source !== 3 // 3 来自SAP的数据
      ) {
        // 对于供方接收/供方拒绝状态下 && 根据策略配置中的该业务类型决定是否允许编辑
        isShow = true
      }

      // 反馈正常的状态不能编辑，针对非采（业务类型为：固资新增，固资装修，费用类）的订单；
      if (
        data.showStatus === '反馈正常' &&
        (data.businessTypeCode === 'BTTCL001' ||
          data.businessTypeCode === 'BTTCL002' ||
          data.businessTypeCode === 'BTTCL003')
      ) {
        // 对于供方接收/供方拒绝状态下 && 根据策略配置中的该业务类型决定是否允许编辑
        isShow = false
      }

      return isShow
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectRecords = $grid.getCheckboxRecords() || []
      const ids = []
      const orderCodes = []
      selectRecords?.forEach((item) => {
        ids.push(item.id)
        orderCodes.push(item.orderCode)
      })
      if (!selectRecords.length && code !== 'Add') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (code) {
        case 'Add':
          this.handleAdd()
          break
        case 'Publish':
          this.handlePublish(ids, selectRecords)
          break
        case 'Expedited':
          this.handleExpedited(ids, selectRecords, orderCodes)
          break
        case 'CancleExpedited':
          this.handleCancleExpedited(ids, selectRecords, orderCodes)
          break
        case 'commitArchive':
          this.handleCommitArchive(ids)
          break
        case 'cancelArchive':
          this.handleCancelArchive(ids)
          break
        case 'sync':
          this.handleSync(selectRecords)
          break
      }
    },
    // 归档
    handleCommitArchive(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('是否确认归档?')
        },
        success: async () => {
          let params = {
            ids: ids
          }
          const res = await this.$API.purchaseOrder.orderCommitArchive(params)
          if (res.code === 200) {
            this.$toast({
              content: this.$t('归档成功'),
              type: 'success'
            })
          }
        }
      })
    },
    // 取消归档
    handleCancelArchive(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('是否确认取消归档?')
        },
        success: async () => {
          let params = {
            ids: ids
          }
          const res = await this.$API.purchaseOrder.orderCancelArchive(params)
          if (res.code === 200) {
            this.$toast({
              content: this.$t('取消归档成功'),
              type: 'success'
            })
          }
        }
      })
    },
    handleSync(selectRecords) {
      if (selectRecords.length === 1) {
        // 判断是否为同步失败的数据
        if (selectRecords[0].syncSupSysStatus !== 1) {
          let params = {
            ids: [selectRecords[0].id]
          }
          this.$API.purchaseOrder.syncSupplierSystemApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({
                content: this.$t('操作成功!'),
                type: 'success'
              })
              this.handleCustomSearch()
            }
          })
        } else {
          this.$toast({
            content: this.$t('只能选择同步供方系统状态为失败或为空的数据进行操作!'),
            type: 'warning'
          })
        }
      } else {
        this.$toast({
          content: this.$t('只能选择一行数据进行操作!'),
          type: 'warning'
        })
      }
    },
    handleCancleExpedited(ids, selectRows, orderCodes) {
      //取消加急订单
      let hasOne = selectRows.some((item) => {
        return !(item.urgentTime && item.urgentTime.length === 13)
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择已加急的的行操作!'),
          type: 'warning'
        })
        return
      }
      let params = {
        type: 0,
        urgentList: []
      }
      orderCodes.forEach((item) => {
        params.urgentList.push({
          ids: [],
          itemNos: [],
          orderCode: item
        })
      })
      this.$API.purchaseOrder.purOrderUrgentOrder(params).then((res) => {
        if (res.data.length) {
          let str = ''
          res.data.forEach((item) => {
            console.log(item)
            str = str + `${item.code} ${item.msg}`
          })
          this.$toast({ content: str, type: 'error' })
        } else {
          this.$toast({
            content: this.$t('取消加急订单操作成功'),
            type: 'success'
          })
          this.handleCustomSearch()
        }
      })
    },
    handleExpedited(ids, selectRows, orderCodes) {
      //加急订单
      let hasOne = selectRows.some((item) => {
        return item.urgentTime && item.urgentTime.length === 13
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择普通的的行操作!'),
          type: 'warning'
        })
        return
      }
      let params = {
        type: 1,
        urgentList: []
      }
      orderCodes.forEach((item) => {
        params.urgentList.push({
          ids: [],
          itemNos: [],
          orderCode: item
        })
      })
      this.$API.purchaseOrder.purOrderUrgentOrder(params).then((res) => {
        if (res.data.length) {
          let str = ''
          res.data.forEach((item) => {
            str = str + `${item.code} ${item.msg}`
          })
          this.$toast({ content: str, type: 'error' })
        } else {
          this.$toast({
            content: this.$t('加急订单操作成功'),
            type: 'success'
          })
          this.handleCustomSearch()
        }
      })
    },
    //发布
    handlePublish(ids, selectRows) {
      let hasOne = selectRows.some((item) => {
        return item.showStatus !== this.$t('待发布') && item.showStatus !== this.$t('反馈异常')
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择待发布或者反馈异常状态订单操作!'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('发布订单'),
          message: this.$t('确认发布选中的订单？')
        },
        success: () => {
          let params = {
            orderIds: ids,
            checkStatus: true
          }
          this.$API.purchaseOrder.purOrderPublish(params).then(() => {
            this.$toast({ content: this.$t('发布操作成功'), type: 'success' })
            this.handleCustomSearch()
          })
        }
      })
    },
    // 新增
    handleAdd() {
      // this.$refs.addDialog.show();
      this.$router.push({
        name: 'purchase-coordination-detail',
        query: {
          type: '1',
          source: '1'
        }
      })
    },
    handleCustomReset() {
      this.searchConfigs.forEach((i) => {
        i.searchOptions = {
          ...i.searchOptions,
          value: null
        }
      })
      this.forecastPageCurrent = 1
      this.handleCustomSearch()
    },
    handleCustomSearch(handleType) {
      const rules = formatParams(this.searchConfigs)
      const params = {
        enableArchiveQuery: this.enableArchiveQuery,
        page: {
          current: handleType === 'handle' ? 1 : this.forecastPageCurrent,
          size: this.forecastPageSettings.pageSize
        },
        condition: 'and',
        rules
      }
      this.apiStartLoading()
      this.$API.purchaseCoordination
        .purOrderQuery(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.currentPage = Number(res?.data?.current) || 1
            this.forecastPageSettings.totalRecordsCount = Number(total)
            this.tableData =
              res?.data?.records?.map((i) => {
                return {
                  ...i,
                  version1: i.version
                }
              }) || [] // 表格数据
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleArchiveChange(e) {
      this.enableArchiveQuery = !!e
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>
