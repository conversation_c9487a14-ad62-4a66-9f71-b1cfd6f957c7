<template>
  <div class="table-area">
    <div class="left-status">
      <span class="tit">{{ $t('业务类型：') }} </span>
      <status-check
        ref="statusCheckRef"
        :status-data="businessTypeList"
        @handleChose="handleChose"
      ></status-check>
    </div>
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      :show-archive="true"
      max-height="700px"
      @reset="handleCustomReset"
      @search="handleCustomSearch('handle')"
      @archiveChange="handleArchiveChange"
    >
      <mt-form ref="searchFormRef">
        <template v-for="item in searchConfigs">
          <!-- v-if="item.title && item.ignore !== true" -->
          <mt-form-item
            v-if="item.title"
            :key="item.field"
            :prop="item.field"
            :label="item.title"
            label-style="top"
          >
            <mt-multi-select
              v-if="item.searchOptions && item.searchOptions.elementType === 'multi-select'"
              style="flex: 1"
              v-model="item.searchOptions.value"
              :fields="item.searchOptions.fields"
              :show-select-all="true"
              :allow-filtering="true"
              filter-type="Contains"
              :data-source="item.searchOptions.dataSource"
              :placeholder="$t('请选择')"
            ></mt-multi-select>
            <mt-select
              v-else-if="item.searchOptions && item.searchOptions.elementType === 'select'"
              style="flex: 1"
              v-model="item.searchOptions.value"
              :fields="item.searchOptions.fields"
              :allow-filtering="true"
              filter-type="Contains"
              :data-source="item.searchOptions.dataSource"
              :placeholder="$t('请选择')"
            ></mt-select>
            <div
              v-else-if="
                item.searchOptions && item.searchOptions.elementType === 'remote-autocomplete'
              "
            >
              <RemoteAutocomplete
                v-if="isReload"
                v-model="item.searchOptions.value"
                :url="item.searchOptions.url"
                :placeholder="$t('请选择')"
                :fields="item.searchOptions.fields"
                :multiple="item.searchOptions.multiple"
                :params="item.searchOptions.params"
                :params-key="item.searchOptions.paramsKey"
                :search-fields="item.searchOptions.searchFields"
                :records-position="item.searchOptions.recordsPosition"
              ></RemoteAutocomplete>
            </div>
            <mt-date-range-picker
              v-else-if="item.searchOptions && item.searchOptions.elementType === 'date-range'"
              :open-on-focus="true"
              :show-clear-button="item.searchOptions.clearButton"
              :allow-edit="false"
              v-model="item.searchOptions.value"
              :placeholder="item.searchOptions.placeholder"
            ></mt-date-range-picker>
            <div
              v-else-if="item.searchOptions && item.searchOptions.elementType === 'number'"
              style="display: flex"
            >
              <div class="operator-list">
                <mt-select
                  v-model="item.searchOptions.operator"
                  :data-source="item.searchOptions.dataSource"
                  popup-width="50px"
                ></mt-select>
              </div>
              <div class="custom-input-number">
                <mt-input-number
                  v-model="item.searchOptions.value"
                  type="number"
                  :show-spin-button="false"
                  :show-clear-button="item.searchOptions.clearButton"
                  :placeholder="item.searchOptions.placeholder"
                />
              </div>
            </div>
            <mt-input
              v-else
              v-model="item.searchOptions.value"
              :placeholder="item.searchOptions.placeholder"
            />
          </mt-form-item>
        </template>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <ScTable
      ref="xTable"
      :row-config="{ height: 50 }"
      :columns="columns"
      :table-data="tableData"
      :grid-id="gridId"
      height="auto"
      header-align="left"
      align="left"
      show-overflow
      style="padding-top: unset"
      :scroll-x="{ gt: 0, oSize: 20 }"
      :scroll-y="{ gt: 0, oSize: 10 }"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          v-permission="item.permission"
          size="small"
          @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #printMaterialFileNameDefault="{ row }">
        <div style="cursor: pointer; color: #2783fe" @click="handleClick(row)">
          {{ row.printMaterialFileName }}
        </div>
      </template>
    </ScTable>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import * as UTILS from '@/utils/utils'
import ScTable from '@/components/ScTable/src/index'
import * as CONFIG from './../config'
import { cloneDeep } from 'lodash'
import { formatColumn, formatParams } from './../config/columnFormat'
export default {
  components: {
    ScTable,
    CollapseSearch,
    statusCheck: () => import('@/components/businessComponents/statusCheck.vue')
  },
  data() {
    return {
      enableArchiveQuery: false,
      isReload: true,
      businessTypeList: [],
      searchConfigs: cloneDeep(formatColumn(CONFIG.editColumnBefore)),
      columns: formatColumn(CONFIG.editColumnBefore),
      tableData: [],
      forecastPageCurrent: 1,
      forecastPageSettings: {
        currentPage: 1,
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      toolbar: [
        {
          code: 'Export1',
          icon: 'icon_solid_export',
          name: this.$t('导出'),
          permission: ['O_02_1070']
        }
      ],
      businessTypeId: '',
      businessTypeCode: '',
      gridId: ''
    }
  },
  async created() {
    await this.getBusinessConfig()
  },
  methods: {
    handleClick(row) {
      this.$API.drawingTogether
        .getFileUrlApi({
          id: row.printMaterialFileId
        })
        .then((res) => {
          if (res.code === 200) {
            window.open(res.data?.fileUrl)
          }
        })
    },
    // 设置gridId
    setGridId(code) {
      const gridId = 'f1a57f3b-3565-4887-b845-d1663726f303'
      // const replaceLength = code.length
      // const firstStr = gridId.substring(9, 12)
      // const centerStr = gridId.substring(14, 17)
      // const lastStr = gridId.slice(-3)
      // this.gridId = this.gridId.replace(firstStr, code.slice(-3).toLowerCase())
      // this.gridId = this.gridId.replace(centerStr, code.slice(-3).toLowerCase())
      // this.gridId = this.gridId.replace(lastStr, code.slice(-3).toLowerCase())
      this.gridId = this.$md5(`${gridId}${code}`)
    },
    // 顶部筛选 调整
    handleChose(e, item) {
      this.setGridId(item.itemCode)
      this.getColumnModule(item.itemCode, e)
    },
    //获取动态表头
    getColumnModule(code, e) {
      let params = {
        businessType: e,
        docType: 'po',
        moduleType: 6,
        tenantId: 0
      }
      this.$API.purchaseOrder.getbusinessModuleFields(params).then((res) => {
        let flag = false
        let columnData = []
        if (res.data && res.data.length) {
          let _columnData = cloneDeep(CONFIG.editColumnBefore)
          let _columnData1 = cloneDeep(CONFIG.editColumnEnd)
          columnData = _columnData
            .concat(
              res.data.map((item1) => {
                let obj = {}
                let hasItem = false
                CONFIG.columnData1.some((item2) => {
                  if (item2.field === item1.fieldCode) {
                    obj = {
                      ...item2,
                      headerText:
                        item1.fieldCode === 'itemCode' ? this.$t('物料编码') : item1.fieldName
                    }
                    if (
                      item1.fieldCode === 'taxTotal' ||
                      item1.fieldCode === 'taxPrice' ||
                      item1.fieldCode === 'freeTotal' ||
                      item1.fieldCode === 'freePrice' ||
                      item1.fieldCode === 'budgetTotalPrice' ||
                      item1.fieldCode === 'budgetUnitPrice'
                    ) {
                      obj.visible = false
                    }
                    hasItem = true
                  }
                })
                if (hasItem) {
                  return obj
                } else {
                  return {
                    width: '145',
                    visible:
                      item1.fieldCode === 'taxTotal' ||
                      item1.fieldCode === 'taxPrice' ||
                      item1.fieldCode === 'freeTotal' ||
                      item1.fieldCode === 'freePrice'
                        ? false
                        : true,
                    ...item1,
                    headerText:
                      item1.fieldCode === 'itemCode' ? this.$t('物料编码') : item1.fieldName,
                    field: item1.fieldCode
                  }
                }
              })
            )
            .concat(_columnData1)
          flag = true
          // columnData.forEach((item) => {
          //   if (item.field === 'closeStatus') {
          //     item.valueConverter = {
          //       type: 'map',
          //       map: {
          //         0: this.$t('未关闭'),
          //         1: this.$t('已关闭')
          //       }
          //     }
          //   }
          // })
        }
        if (!flag) {
          let _columnData = cloneDeep(CONFIG.editColumnBefore)
          let _columnData1 = cloneDeep(CONFIG.editColumnEnd)
          columnData = _columnData.concat(_columnData1)
        }
        this.isReload = !this.isReload
        if (code === 'BTTCL004') {
          const allColumns = formatColumn(columnData).map((i) => {
            const j = {
              ...i
            }
            if (j.field === 'siteName') {
              j.searchOptions = {
                elementType: 'remote-autocomplete',
                fields: { text: 'dimensionNameValue', value: 'dimensionCodeValue' },
                multiple: true,
                url: '/srm-purchase-execute/tenant/common/permission/querySiteList',
                recordsPosition: 'data',
                paramsKey: 'keyWord',
                renameField: 'siteCode',
                operator: 'in',
                searchFields: null,
                params: null,
                value: []
              }
            } else if (j.field === 'companyCode') {
              j.searchOptions = {
                elementType: 'remote-autocomplete',
                fields: { text: 'dimensionNameValue', value: 'dimensionCodeValue' },
                multiple: true,
                url: '/srm-purchase-execute/tenant/common/permission/queryCompanyList',
                recordsPosition: 'data',
                paramsKey: 'keyWord',
                operator: 'in',
                searchFields: null,
                params: null,
                value: []
              }
            }
            return j
          })
          const finalColumns = []
          CONFIG.columnDataSort.forEach((i) => {
            allColumns.forEach((j) => {
              if (j.field === i) {
                // const item = j
                // if (
                //   j.field === 'warehouse' ||
                //   j.field === 'urgentStatus' ||
                //   j.field === 'urgentTime' ||
                //   j.field === 'closeStatus' ||
                //   j.field === 'buyerOrgName' ||
                //   j.field === 'buyerGroupCode' ||
                //   j.field === 'unitName' ||
                //   j.field === 'customerOrderLineNo' ||
                //   j.field === 'receiveAddress' ||
                //   j.field === 'contact' ||
                //   j.field === 'consignee' ||
                //   j.field === 'source' ||
                //   j.field === 'businessTypeName' ||
                //   j.field === 'contractNo' ||
                //   j.field === 'projectName' ||
                //   j.field === 'prototypeMachineCode' ||
                //   j.field === 'itemText' ||
                //   j.field === 'projectRowText' ||
                //   j.field === 'remark' ||
                //   j.field === 'changeRemark' ||
                //   j.field === 'orderRemark' ||
                //   j.field === 'orderSupRemark' ||
                //   j.field === 'version' ||
                //   j.field === 'updateUserName' ||
                //   j.field === 'updateTime'
                // ) {
                //   item.ignore = true
                // }
                finalColumns.push(j)
              }
            })
          })
          this.columns = finalColumns
          this.searchConfigs = this.getSearchConfigsSort(cloneDeep(this.columns))
        } else {
          this.columns = formatColumn(columnData)
          this.searchConfigs = this.getSearchConfigsSort(cloneDeep(this.columns))
        }
        this.$nextTick(() => {
          this.isReload = !this.isReload
        })
        this.purOrderDetail(e, code)
      })
    },
    getSearchConfigsSort(columnData) {
      const currentBu = localStorage.getItem('currentBu')
      const searchSort = currentBu === 'TV' ? CONFIG.searchSortTV : CONFIG.searchSortPur
      const searchConfigs = []
      searchSort?.forEach((i) => {
        columnData.forEach((j) => {
          if (i === j.field) {
            searchConfigs.push(j)
          }
        })
      })
      return searchConfigs
    },
    //重新赋值asynConfig
    purOrderDetail(e, code) {
      this.businessTypeId = e
      this.businessTypeCode = code
      this.handleCustomSearch()
    },
    // 获取业务类型下拉
    async getBusinessConfig() {
      let userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      let params = {
        subjectId: userInfo.uid,
        dimensionCode: 'BUSINESS_TYPE',
        billTypeCode: 'po_item',
        tableName: 'mt_buyer_order'
      }
      await this.$API.purchaseOrder.getBusinessType(params).then((res) => {
        // res.data = res.data.concat(res.data);
        if (res.data && res.data.length) {
          this.businessTypeList = []
          res.data.map((item) => {
            this.businessTypeList.push({
              label: item.dimensionNameValue,
              value: item.dimensionIdValue,
              itemCode: item.dimensionCodeValue,
              text: item.dimensionNameValue
            })
          })
        }
      })
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectRecords = $grid.getCheckboxRecords() || []
      const ids = []
      const orderCodes = []
      selectRecords?.forEach((item) => {
        ids.push(item.id)
        orderCodes.push(item.orderCode)
      })
      if (code === 'Export1') {
        const { visibleColumn } = this.$refs.xTable.$refs.xGrid.getTableColumn()
        const headerMap = {}
        visibleColumn.forEach((i) => {
          if (i.field && i.title) {
            if (i.field !== 'customChecked' && i.field !== 'id') {
              headerMap[i.field] = i.title
            }
          }
        })
        const rules = formatParams(this.searchConfigs)
        let params = {
          condition: 'and',
          page: { current: 1, size: 5000 },
          pageFlag: true,
          rules: [
            ...rules,
            {
              condition: 'and',
              field: 'businessTypeCode',
              operator: 'equal',
              value: this.businessTypeCode
            },
            {
              condition: 'and',
              field: 'id',
              operator: 'in',
              value: ids
            }
          ],
          headerMap
        }
        this.apiStartLoading()
        this.$API.purchaseOrder
          .poDetailDownload(params)
          .then((res) => {
            this.apiEndLoading()
            const fileName = UTILS.getHeadersFileName(res)
            UTILS.download({ fileName: `${fileName}`, blob: res.data })
          })
          .catch(() => {
            this.apiEndLoading()
          })
      }
    },
    handleCustomReset() {
      this.searchConfigs.forEach((i) => {
        i.searchOptions = {
          ...i.searchOptions,
          value: null
        }
      })
      this.forecastPageCurrent = 1
      this.handleCustomSearch()
    },
    handleCustomSearch(handleType) {
      const rules = formatParams(this.searchConfigs)
      const params = {
        enableArchiveQuery: this.enableArchiveQuery,
        defaultRules: [
          {
            condition: 'and',
            field: 'business_type_id',
            label: '',
            operator: 'equal',
            value: this.businessTypeId
          }
        ],
        page: {
          current: handleType === 'handle' ? 1 : this.forecastPageCurrent,
          size: this.forecastPageSettings.pageSize
        },
        condition: 'and',
        rules
      }
      this.apiStartLoading()
      this.$API.purchaseCoordination
        .purOrderDetailQuery(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.currentPage = Number(res?.data?.current) || 1
            this.forecastPageSettings.totalRecordsCount = Number(total)
            this.tableData =
              res?.data?.records?.map((i) => {
                const item = {
                  ...i
                }
                item.fieldDataList = item.fieldDataList || []
                item.fieldDataList.forEach((item1) => {
                  //对应的动态字段 需求名称,需求描述,资产类别,资产编号,资产卡片,客户,关联客户订单
                  item[item1.fieldCode] = item1.fieldData
                })

                if (item.costs) {
                  item.orderCosts = `${item.costs[0].costCenterCode}-${item.costs[0].costCenterName}`
                }
                item.version1 = item.version
                if (item.taxTotal < 0) {
                  item.taxTotal = '*'
                }

                if (item.taxPrice < 0) {
                  item.taxPrice = '*'
                }
                if (item.freeTotal < 0) {
                  item.freeTotal = '*'
                }
                if (item.freePrice < 0) {
                  item.freePrice = '*'
                }
                if (item.unPrice < 0) {
                  item.unPrice = ''
                }
                if (item.unTotal < 0) {
                  item.unTotal = ''
                }
                if (item.approvedTotalPrice < 0) {
                  item.approvedTotalPrice = ''
                }
                if (item.budgetTotalPrice < 0) {
                  item.budgetTotalPrice = ''
                }
                if (item.budgetUnitPrice < 0) {
                  item.budgetUnitPrice = ''
                }
                if (item.taxedTotalPrice < 0) {
                  item.taxedTotalPrice = ''
                }
                if (item.taxedUnitPrice < 0) {
                  item.taxedUnitPrice = ''
                }
                if (item.subjectTotal < 0) {
                  item.subjectTotal = ''
                }
                // if (!item.printMaterialFileName) {
                //   item.printMaterialFileName = '1'
                // }
                return {
                  ...item,
                  version1: item.version
                }
              }) || [] // 表格数据
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleArchiveChange(e) {
      this.enableArchiveQuery = !!e
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.left-status {
  padding: 0 20px 10px;
}
::v-deep .custom-input-number {
  width: 100%;
  .mt-input-number {
    width: 100%;
    margin-bottom: 0px;
    input {
      height: 23px;
      padding-right: 30px;
    }
  }
}
</style>
