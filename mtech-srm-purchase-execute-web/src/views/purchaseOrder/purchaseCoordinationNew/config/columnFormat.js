import { i18n } from '@/main.js'
import UTILS from '../../../../utils/utils'

export const formatColumn = (column) => {
  const columnData = [
    {
      type: 'checkbox',
      width: 50,
      ignore: true,
      fixed: 'left'
    }
  ]
  if (!column) {
    return columnData
  }
  column.forEach((i) => {
    if (i.visible !== false && i.headerText) {
      // 将动态列配置与动态查询条件数据关联配置组装到一起
      const columnItem = {
        // ...i,
        minWidth: i.width,
        title: i.headerText,
        label: i.headerText,
        field: i.field,
        ignore: i.ignore,
        showOverflow: true,
        searchOptions: {
          value: null,
          type: 'string',
          operator: 'contains'
        }
      }
      if (i.searchOptions) {
        columnItem.searchOptions = {
          ...columnItem.searchOptions,
          ...i.searchOptions
        }
        if (i.searchOptions?.elementType === 'number') {
          columnItem.searchOptions.operator = 'equal'
          columnItem.searchOptions.dataSource = [
            { text: '=', value: 'equal' },
            { text: '≠', value: 'notequal' },
            { text: '>', value: 'greaterthan' },
            { text: '≥', value: 'greaterthanorequal' },
            { text: '<', value: 'lessthan' },
            { text: '≤', value: 'lessthanorequal' }
          ]
        }
      }
      if (i.valueConverter) {
        const dataSource = []
        for (const key in i.valueConverter.map) {
          dataSource.push({
            text: i.valueConverter.map[key],
            value: Number(key)
          })
        }
        columnItem.searchOptions = {
          ...columnItem.searchOptions,
          dataSource
        }
      }
      if (i.cellTools) {
        columnItem.cellTools = i.cellTools
        columnItem.slots = {
          // 使用插槽模板渲染
          default: i.field + 'Default'
        }
      }
      if (i.field === 'businessTypeName') {
        columnItem.formatter = ({ row }) => {
          return i18n.t(row.businessTypeName)
        }
      }
      if (i.field === 'companyCode' || i.field === 'companyName') {
        columnItem.formatter = ({ row }) => {
          return row.companyCode + '-' + row.companyName
        }
      }
      if (i.field === 'supplierCode' || i.field === 'supplierName') {
        columnItem.formatter = ({ row }) => {
          return row.supplierCode + '-' + row.supplierName
        }
      }
      if (i.field === 'buyerGroupCode' || i.field === 'buyerGroupName') {
        columnItem.formatter = ({ row }) => {
          return row.buyerGroupCode + '-' + row.buyerGroupName
        }
      }
      if (i.field === 'buyerOrgCode' || i.field === 'buyerOrgName') {
        columnItem.formatter = ({ row }) => {
          return row.buyerOrgCode + '-' + row.buyerOrgName
        }
      }
      if (i.field === 'currencyCode' || i.field === 'currencyName') {
        columnItem.formatter = ({ row }) => {
          return row.currencyCode + '-' + row.currencyName
        }
      } else if (i.field === 'version1') {
        columnItem.formatter = ({ cellValue }) => {
          return cellValue > 1 ? i18n.t('变更') : ''
        }
      } else if (i.field === 'showStatus') {
        // 订单状态
        columnItem.formatter = ({ cellValue }) => {
          let item = i.searchOptions?.dataSource?.find((item) => item.value === cellValue)
          return item ? item.text : ''
        }
      } else if (
        i.field === 'orderTime' ||
        i.field === 'orderDate' ||
        i.field === 'timePromise' ||
        i.field === 'requiredDeliveryDate' ||
        i.field === 'urgentTime' ||
        i.field === 'acceptDate'
      ) {
        // 订单日期、承诺日期、要求交期、加急日期
        columnItem.formatter = ({ cellValue }) => {
          if (cellValue && cellValue.length === 13) {
            const e = Number(cellValue)
            return UTILS.dateFormat(e, 'Y-m-d')
          } else {
            return ''
          }
        }
      } else if (i.field === 'warehouseCode') {
        // 币种
        columnItem.formatter = ({ cellValue, row }) => {
          return cellValue + '-' + row.warehouse
        }
      } else if (i.field === 'siteName') {
        // 地点/工厂
        columnItem.formatter = ({ cellValue, row }) => {
          return row.siteCode + '-' + cellValue
        }
      } else if (i.field === 'profitCenterName') {
        // 利润中心
        columnItem.formatter = ({ cellValue, row }) => {
          return row.profitCenterCode + '-' + cellValue
        }
      } else if (i.field === 'purUnitName') {
        // 采购单位
        columnItem.formatter = ({ cellValue, row }) => {
          return row.purUnitCode + '-' + cellValue
        }
      } else if (i.field === 'unitName') {
        // 基本单位
        columnItem.formatter = ({ cellValue, row }) => {
          return row.unitCode + '-' + cellValue
        }
      } else if (i.field === 'version') {
        columnItem.formatter = ({ cellValue }) => {
          return 'V' + cellValue
        }
      } else if (i.field === 'contractType') {
        // 合同协议类型
        columnItem.formatter = ({ cellValue }) => {
          let str = ''
          if (cellValue == '0') {
            str = i18n.t('单次采购')
          }
          if (cellValue == '1') {
            str = i18n.t('框架协议')
          }
          return str
        }
      } else if (
        i.field === 'updateTime' ||
        i.field === 'publishTime' ||
        i.field === 'feedbackTime' ||
        i.field === 'purchasingCycleStart' ||
        i.field === 'purchasingCycleEnd' ||
        i.field === 'preAcceptanceTime' ||
        i.field === 'acceptanceTime'
      ) {
        // 更新时间、发布时间、反馈时间
        columnItem.formatter = ({ cellValue }) => {
          if (cellValue && cellValue.length === 13) {
            const e = Number(cellValue)
            return UTILS.dateFormat(e)
          } else {
            return ''
          }
        }
      } else if (i.field === 'voucherDate') {
        // 凭证日期
        columnItem.formatter = ({ cellValue }) => {
          if (cellValue) {
            return UTILS.dateFormat(cellValue, 'Y-m-d')
          } else {
            return ''
          }
        }
      } else if (
        i.field === 'purchaseStrategy' ||
        i.field === 'provisionalEstimateStatus' ||
        i.field === 'returnIdentification' ||
        i.field === 'subjectType' ||
        i.field === 'deliveryStatus' ||
        i.field === 'receiveStatus' ||
        i.field === 'confirmStatus' ||
        i.field === 'closeStatus' ||
        i.field === 'warehouseStatus' ||
        i.field === 'requestOrderMethod' ||
        i.field === 'source' ||
        i.field === 'advanceInvoiceStatus' ||
        i.field === 'advancePayStatus' ||
        i.field === 'chargeStatus'
      ) {
        if (i.field === 'closeStatus') {
          i.valueConverter = {
            type: 'map',
            map: {
              0: i18n.t('未关闭'),
              1: i18n.t('已关闭')
            }
          }
        }
        // 采购策略、是否暂估价、退货标识、科目类型、发货状态、收货状态、供应商确认状态、关闭状态、入库状态、独立/集中、订单类型、提前开票、是否预付、是否挂账
        columnItem.formatter = ({ cellValue }) => {
          let text = ''
          if (cellValue || cellValue === 0) {
            text = i.valueConverter?.map[String(cellValue)]
          }
          return text
        }
      } else if (i.field === 'consignee' || i.field === 'techContactPerson') {
        // 收货人、技术对接人
        columnItem.formatter = ({ cellValue }) => {
          if (cellValue) {
            if (cellValue.split('-')[3]) {
              return cellValue.split('-')[3]
            } else {
              return cellValue
            }
          } else {
            return ''
          }
        }
      } else if (i.field === 'urgentStatus') {
        // 加急状态
        columnItem.type = 'html'
        columnItem.formatter = ({ cellValue }) => {
          const str = cellValue === 0 ? i18n.t('普通') : i18n.t('加急')
          const color = cellValue === 0 ? '' : 'color: red'
          return `<span style="${color}">${str}</span>`
        }
      } else if (i.field === 'deliveryStatus1') {
        // 延期状态
        columnItem.type = 'html'
        columnItem.formatter = ({ cellValue }) => {
          const color = cellValue === i18n.t('已延期') ? 'color: #ed5633' : 'color: #8acc40'
          return `<div style='${color}'><span style='
          display: inline-block;
          height: 8px;
          width: 8px;
          margin-right: 8px;
          height: 8px;
          background-${color};
          border-radius: 50%;'></span>${cellValue}</div>`
        }
      } else if (i.field === 'agreementCode') {
        columnItem.formatter = ({ row }) => {
          return row.contract
        }
      } else if (i.field === 'budgetQuantity') {
        columnItem.formatter = ({ row }) => {
          return row.forecastQty
        }
      } else if (i.field === 'orderUnitId') {
        columnItem.formatter = ({ row }) => {
          return row.purUnitId
        }
      } else if (i.field === 'file') {
        columnItem.slots = {
          // 使用插槽模板渲染
          default: i.field + 'Default'
        }
      } else if (i.field === 'domesticDemandFlag') {
        columnItem.formatter = ({ cellValue }) => {
          const str = cellValue == 'E' ? i18n.t('是') : i18n.t('否')
          return str
        }
      } else if (i.field === 'printMaterialFileName') {
        columnItem.slots = {
          // 使用插槽模板渲染
          default: i.field + 'Default'
        }
      }
      if (!columnData.some((i) => i.title === columnItem.title)) {
        columnData.push(columnItem)
      }
    }
  })
  return columnData
}

export const formatParams = (searchConfigs) => {
  const searchRules = []
  searchConfigs.forEach((item) => {
    if (
      ((item?.searchOptions?.value && item?.searchOptions?.value?.length) ||
        item?.searchOptions?.value === 0 ||
        (!isNaN(item?.searchOptions?.value) && item?.searchOptions?.value !== null)) &&
      item?.searchOptions?.value !== ''
    ) {
      let value = item?.searchOptions?.value
      // if (item?.searchOptions?.elementType === 'date-range') {
      // value = [
      //   Number(new Date(value[0].toString())),
      //   Number(new Date(value[1].toString())) + Number(86400000 - 1440000)
      // ]
      // }
      if (item?.searchOptions?.serializeValue) {
        value = item?.searchOptions?.serializeValue(value)
      }
      if (Array.isArray(value) && value.length === 0) {
        return false
      }
      let option = {
        label: item.label,
        field: item?.searchOptions?.renameField ? item?.searchOptions?.renameField : item.field,
        type: item?.searchOptions?.type,
        operator: item?.searchOptions?.operator,
        value
      }
      if (item?.searchOptions && item?.searchOptions?.customRule) {
        option = item?.searchOptions?.customRule(value)
      }
      searchRules.push(option)
    }
  })
  return searchRules
}
