import UTILS from '../../../../utils/utils'
// import cellFileView from '@/components/normalEdit/cellFileView' // 单元格附件查看
import { i18n } from '@/main.js'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant'

export const Toolbar = [
  {
    code: 'InvoiceAdvance',
    icon: 'a-icon_table_Invoicinginadvance',
    name: i18n.t('提前开票'),
    permission: ['O_02_0148']
  },
  {
    code: 'CancleInvoiceAdvance',
    icon: 'a-icon_table_Canceladvanceinvoicing',
    name: i18n.t('取消提前开票'),
    permission: ['O_02_0149']
  },
  {
    code: 'SharedPrepaid',
    icon: 'icon_table_SharedPrepayment',
    name: i18n.t('共享预付'),
    permission: ['O_02_0150']
  },
  {
    code: 'Reimbursement',
    icon: 'icon_table_AccountReimbursement',
    name: i18n.t('挂账报销'),
    permission: ['O_02_0151']
  }
]

export const myAcceptanceCellTools = [
  {
    id: 'ys',
    icon: 'icon_list_acceptance',
    title: i18n.t('验收'),
    permission: ['O_02_0145'],
    visibleCondition: (data) => {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      return (
        data.acceptanceStatus == 0 &&
        (data.acceptorId === userInfo.employeeId || data.acceptorId === userInfo.uid)
      )
    }
  },
  {
    id: 'ch',
    icon: 'icon_list_recall',
    title: i18n.t('撤回'),
    permission: ['O_02_0146'],
    visibleCondition: (data) => {
      return data.acceptanceStatus == 3 && data.acceptanceType !== '1527602264118325250'
    }
  },
  {
    id: 'qr',
    icon: 'icon_list_acceptance',
    title: i18n.t('确认'),
    permission: ['O_02_1388'],
    visibleCondition: (data) => {
      return data.acceptanceStatus == 3
    }
  },
  {
    id: 'bh',
    icon: 'icon_list_recall',
    title: i18n.t('驳回'),
    permission: ['O_02_1389'],
    visibleCondition: (data) => {
      return data.acceptanceStatus == 3 && data.acceptanceType !== '1527602264118325250'
    }
  }
]

export const acceptanceStatusList = [
  { value: 0, text: i18n.t('未验收'), cssClass: 'col-active' },
  { value: 2, text: i18n.t('验收中'), cssClass: 'col-inactive' },
  { value: 3, text: i18n.t('验收确认'), cssClass: 'col-inactive' },
  { value: 1, text: i18n.t('已验收'), cssClass: 'col-inactive' }
]

export const columnData = [
  //验收计划
  // {
  //   width: '50',
  //   type: 'checkbox',
  //   showInColumnChooser: false
  // },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    isPrimaryKey: true
  },
  {
    field: 'acceptanceStatus',
    headerText: i18n.t('验收状态'),
    width: '200',
    // valueConverter: {
    //   type: 'map',
    //   map: [
    //     { value: 0, text: i18n.t('未验收'), cssClass: 'col-active' },
    //     { value: 2, text: i18n.t('验收中'), cssClass: 'col-inactive' },
    //     { value: 3, text: i18n.t('验收确认'), cssClass: 'col-inactive' },
    //     { value: 1, text: i18n.t('已验收'), cssClass: 'col-inactive' }
    //   ]
    // },
    cellTools: [],
    searchOptions: {
      elementType: 'select',
      showSelectAll: true,
      operator: 'equal',
      dataSource: acceptanceStatusList
    }
  },
  {
    field: 'acceptanceCode',
    headerText: i18n.t('验收单号'),
    width: '150'
  },
  {
    field: 'acceptDate',
    headerText: i18n.t('验收日期'),
    ignore: true
  },
  {
    field: 'acceptanceCodeRel',
    headerText: i18n.t('同步关联单据号'),
    width: '150'
  },
  {
    field: 'advanceInvoiceStatus',
    headerText: i18n.t('提前开票'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    },
    searchOptions: {
      elementType: 'select',
      showSelectAll: true,
      operator: 'equal'
    }
  },
  {
    field: 'deliveryStatus1',
    headerText: i18n.t('延期状态'),
    width: '150',
    // valueConverter: {
    //   type: 'map',
    //   map: [
    //     {
    //       value: i18n.t('已延期'),
    //       text: i18n.t('已延期'),
    //       cssClass: 'col-synced1'
    //     },
    //     {
    //       value: i18n.t('未延期'),
    //       text: i18n.t('未延期'),
    //       cssClass: 'col-notSynced1'
    //     }
    //   ]
    // },
    searchOptions: {
      elementType: 'select',
      showSelectAll: true,
      operator: 'equal',
      dataSource: [
        { text: i18n.t('已延期'), value: i18n.t('已延期') },
        { text: i18n.t('未延期'), value: i18n.t('未延期') }
      ]
    }
  },
  {
    field: 'orderCode',
    headerText: i18n.t('采购订单号'),
    width: '150',
    searchOptions: {
      placeholder: i18n.t('多个订单号需要用英文逗号隔开')
    }
  },
  {
    field: 'source',
    headerText: i18n.t('订单来源'),
    width: '150',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('采购申请转化'),
        1: i18n.t('手动创建'),
        2: i18n.t('商城申请转化'),
        3: i18n.t('外部SAP'),
        4: i18n.t('合同转换')
      }
    }
  },
  {
    field: 'orderDate',
    headerText: i18n.t('订单日期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'projectCode',
    headerText: i18n.t('项目编号'),
    width: '150'
  },
  {
    field: 'projectName',
    headerText: i18n.t('项目名称'),
    width: '150'
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司'),
    width: '250',
    searchOptions: MasterDataSelect.businessCompany,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.companyCode}}-{{data.companyName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    width: '300',
    searchOptions: MasterDataSelect.supplier,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.supplierCode}}-{{data.supplierName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    width: '150'
    // searchOptions: {
    //   ...MasterDataSelect.material,
    //   renameField: "itemCode",
    // },
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: '150',
    ignore: true
  },
  {
    field: 'requireName',
    headerText: i18n.t('需求名称'),
    width: '160'
  },
  {
    field: 'demandCode',
    headerText: i18n.t('需求编码'),
    width: '160'
  },
  {
    field: 'requirementDescription',
    headerText: i18n.t('需求描述'),
    width: '150'
  },
  {
    field: 'skuCode',
    headerText: i18n.t('SKU编号'),
    width: '150',
    searchOptions: {
      ...MasterDataSelect.sku,
      renameField: 'skuCode'
    }
  },
  {
    field: 'skuName',
    headerText: i18n.t('SKU名称'),
    width: '150',
    ignore: true
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型'),
    width: '92'
  },
  {
    field: 'buyerGroupCode',
    headerText: i18n.t('采购组织'),
    searchOptions: MasterDataSelect.businessGroupUnit,
    width: '250',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerGroupCode}}-{{data.buyerGroupName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'buyerUserName',
    headerText: i18n.t('采购员'),
    width: '150',
    searchOptions: {
      ...MasterDataSelect.staff,
      renameField: 'buyerUserCode'
    }
  },
  {
    field: 'currencyCode',
    headerText: i18n.t('币种'),
    searchOptions: MasterDataSelect.money,
    width: '150',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.currencyCode}}-{{data.currencyName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'paymentName',
    headerText: i18n.t('付款条件'),
    width: '150'
  },
  {
    field: 'orderRemark',
    headerText: i18n.t('备注'),
    width: '155'
  },
  {
    field: 'itemNo',
    headerText: i18n.t('行号'),
    width: '150'
  },
  {
    field: 'acceptanceTypeName',
    headerText: i18n.t('验收类型'),
    width: '150'
  },
  {
    field: 'qaTimeLimitValue',
    headerText: i18n.t('质保期'),
    width: '150',
    ignore: true
  },
  {
    field: 'advancePayStatus',
    headerText: i18n.t('是否预付'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否') }
    },
    searchOptions: {
      elementType: 'select',
      showSelectAll: true,
      operator: 'equal'
    }
  },
  {
    field: 'chargeStatus',
    headerText: i18n.t('是否挂账'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否') }
    },
    searchOptions: {
      elementType: 'select',
      showSelectAll: true,
      operator: 'equal'
    }
  },
  {
    field: 'description',
    headerText: i18n.t('验收描述'),
    width: '150'
  },
  {
    field: 'percentage',
    headerText: i18n.t('付款比例'),
    width: '150'
  },
  {
    field: 'freeTotal',
    headerText: i18n.t('未税总价'),
    width: '150'
  },
  {
    field: 'taxTotal',
    headerText: i18n.t('含税总价'),
    width: '150'
  },
  {
    field: 'actualCheckUntaxedAmt',
    headerText: i18n.t('实际验收未税总价'),
    width: '150'
  },
  {
    field: 'actualCheckTaxedAmt',
    headerText: i18n.t('实际验收含税总价'),
    width: '150'
  },
  {
    field: 'payTypeName',
    headerText: i18n.t('付款类型'),
    width: '150'
  },
  {
    field: 'acceptor',
    headerText: i18n.t('验收人'),
    width: '150',
    valueAccessor: function (field, data) {
      if (data?.acceptor) {
        if (data.acceptor.split('-')[3]) {
          return data?.acceptor.split('-')[3]
        } else {
          return data?.acceptor
        }
      } else {
        return ''
      }
    }
  },
  {
    field: 'preAcceptanceTime',
    headerText: i18n.t('计划验收时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'acceptanceTime',
    headerText: i18n.t('实际验收时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'contractRel',
    headerText: i18n.t('价格记录编号'),
    width: '186'
  },
  {
    field: 'file',
    headerText: i18n.t('验收附件数量'),
    width: '150',
    ignore: true
    // template: function () {
    //   return {
    //     template: cellFileView
    //   }
    // }
  },
  {
    field: 'remark',
    headerText: i18n.t('验收备注'),
    width: '150'
  }
]
