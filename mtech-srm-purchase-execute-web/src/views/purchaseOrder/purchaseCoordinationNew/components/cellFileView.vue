<template>
  <div class="cell-upload" :id="'cell-upload-' + data.index">
    <!-- file:{{ data.file }} -->
    <mt-input id="file" style="display: none" :value="data.file"></mt-input>
    <div @click.self="showFileBaseInfo" class="cell-operable-title">
      <!-- {{ data.file | listNumFormat }} -->
      {{ fileNums }}
    </div>

    <!-- 需求附件弹窗 -->
    <uploader-dialog @change="fileChange" @confirm="setFile" ref="uploaderDialog"></uploader-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
// 草稿或审批拒绝状态才能再次上传
export default {
  props: {
    rowData: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    UploaderDialog: () => import('@/components/Upload/uploaderDialog')
  },
  data() {
    return {
      data: {
        // file: {},
      },
      uploadFileList: [], // 上传的附件(初始值赋值之前上传过的)
      fileNums: null // 显示的文案 附件数量 / “暂无附件” / “点击上传”
    }
  },
  filters: {
    byteToKB: (value) => Math.floor((value / 1024) * 100) / 100
  },
  watch: {
    'rowData.file': {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.fileNums = JSON.parse(newVal).length
          this.data = this.rowData
        } else {
          this.fileNums = this.$t('暂无附件')
        }
      },
      immediate: true
    }
  },

  mounted() {
    this.$nextTick(() => {
      if (this.data.file && this.data.file.length) {
        this.uploadFileList = JSON.parse(this.data.file)
      }
    })
  },
  methods: {
    showFileBaseInfo() {
      if (!this.uploadFileList.length) return
      const dialogParams = {
        fileData: cloneDeep(this.uploadFileList),
        isView: true, //是否可上传
        required: false, // 是否必须
        title: this.$t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },

    // 行附件弹窗内文件变动
    fileChange(data) {
      console.log('fileChange', data)
      // this.uploadFileList = data;
    },
    // 点击行附件上传的确认按钮
    setFile() {
      console.log('点击了确认')
    }
  }
}
</script>

<style scoped>
.cell-operable-title {
  display: inline-block;
  padding: 10px;
}
</style>
