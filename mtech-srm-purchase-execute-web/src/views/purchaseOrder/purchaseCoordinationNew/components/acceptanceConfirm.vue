<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="dialog-mt-form" :model="formInfo" ref="ruleForm" :rules="dialogRules">
        <mt-form-item prop="actualCheckUntaxedAmt" :label="$t('实际验收未税金额')">
          <mt-input-number v-model="formInfo.actualCheckUntaxedAmt" :min="0" />
        </mt-form-item>
        <mt-form-item prop="actualCheckTaxedAmt" :label="$t('实际验收含税金额')">
          <mt-input-number v-model="formInfo.actualCheckTaxedAmt" :min="0" />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formInfo: {},
      dialogRules: {
        actualCheckUntaxedAmt: [
          {
            required: true,
            message: this.$t('请输入实际验收未税金额'),
            trigger: 'blur'
          }
        ],
        actualCheckTaxedAmt: [
          {
            required: true,
            message: this.$t('请输入实际验收含税金额'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    rowData() {
      return this.modalData.rowData
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.$refs.ruleForm.clearValidate()
    this.formInfo = {
      actualCheckUntaxedAmt: this.rowData.freeTotal,
      actualCheckTaxedAmt: this.rowData.taxTotal
    }
  },
  methods: {
    //点击确认
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('confirm-function', this.formInfo)
        }
      })
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    padding: 0;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
        }
      }
    }
  }
}
</style>
