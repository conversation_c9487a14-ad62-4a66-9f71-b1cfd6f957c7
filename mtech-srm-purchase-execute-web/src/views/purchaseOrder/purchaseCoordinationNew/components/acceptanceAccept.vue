<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="$t('验收')"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <mt-form-item prop="uploadData" :label="$t('上传附件')" class="full-width">
        <upload-file ref="uploader" @change="fileChange"></upload-file>
      </mt-form-item>
      <mt-form-item class="formContainer" prop="acceptDate" :label="$t('验收日期')">
        <mt-date-picker
          v-model="ruleForm.acceptDate"
          :placeholder="$t('选择日期')"
        ></mt-date-picker>
        <p class="tips">{{ $t('注：请按验收单日期进行填写') }}</p>
      </mt-form-item>
      <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
        <mt-input v-model="ruleForm.remark" v-if="maxlength1" :maxlength="maxlength1"></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import dayjs from 'dayjs'
export default {
  components: {
    UploadFile: () => import('@/components/Upload/uploader')
  },
  data() {
    return {
      maxlength1: 500,
      uploadData: [],
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {
        acceptDate: null,
        remark: ''
      },
      rules: {
        acceptDate: [
          {
            required: true,
            message: this.$t('请选择验收日期'),
            trigger: 'blur'
          }
        ]
      },
      entryId: '' // 带入的验收计划id
    }
  },
  methods: {
    fileChange(data) {
      this.uploadData = data
    },
    dialogInit(entryId) {
      this.entryId = entryId
      this.$refs.uploader.init()
      this.uploadData = []
      this.ruleForm.remark = ''
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    confirm() {
      if (this.uploadData.length == 0) {
        this.$toast({ content: this.$t('请上传附件'), type: 'warning' })
        return
      }
      const uploadDataTmp = []
      this.uploadData.forEach((item) => {
        uploadDataTmp.push({
          docId: 0,
          docType: '',
          fileDetailId: 0,
          fileDetailInfo: '',
          fileName: item.fileName,
          fileSize: item.fileSize,
          fileType: item.fileType,
          lineNo: 0,
          nodeCode: '',
          nodeName: '',
          nodeType: 1,
          parentId: 0,
          supplierCode: '',
          supplierId: 0,
          supplierName: '',
          syncStatus: 0,
          sysFileId: item.id,
          url: item.url
        })
      })
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const params = {
            acceptanceIds: [this.entryId],
            files: uploadDataTmp,
            acceptDate: dayjs(dayjs(this.ruleForm.acceptDate).format('YYYY-MM-DD')).valueOf(),
            remark: this.ruleForm.remark
          }
          this.$API.purchaseOrder.acceptanceAccept(params).then(() => {
            this.$toast({ content: this.$t('验收操作成功'), type: 'success' })
            this.handleClose()
            this.$emit('refreshColumns')
          })
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
<style lang="scss">
.formContainer {
  position: relative;
  .tips {
    width: 200px;
    position: absolute;
    left: 405px;
    top: 24px;
    color: red;
  }
}
</style>
