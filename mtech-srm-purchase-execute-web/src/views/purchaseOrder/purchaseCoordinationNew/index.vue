<template>
  <div class="approval-config">
    <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
    <div class="grid-area">
      <orderList ref="orderList" class="flex-one" v-if="tabTitle === $t('单据视图')" />
      <detailList ref="detailList" class="flex-one" v-if="tabTitle === $t('明细视图')" />
      <acceptancePlan
        ref="acceptancePlan"
        class="flex-one"
        v-if="tabTitle === $t('验收计划汇总')"
      />
      <myAcceptance ref="myAcceptance" class="flex-one" v-if="tabTitle === $t('我的验收')" />
    </div>
  </div>
</template>

<script>
export default {
  components: {
    orderList: () => import('./pages/orderList.vue'),
    detailList: () => import('./pages/detailList.vue'),
    acceptancePlan: () => import('./pages/acceptancePlan.vue'),
    myAcceptance: () => import('./pages/myAcceptance.vue')
  },
  data() {
    return {
      tabTitle: this.$t('单据视图'),
      tabSource: []
    }
  },
  created() {
    const elementPermissionSet = window.elementPermissionSet
    if (elementPermissionSet.includes('T_02_0105')) {
      this.tabSource.unshift({
        title: this.$t('我的验收')
      })
      this.tabTitle = this.$t('我的验收')
    }
    if (elementPermissionSet.includes('T_02_0019')) {
      this.tabSource.unshift({
        title: this.$t('验收计划汇总')
      })
      this.tabTitle = this.$t('验收计划汇总')
    }
    if (elementPermissionSet.includes('T_02_0018')) {
      this.tabSource.unshift({
        title: this.$t('明细视图')
      })
      this.tabTitle = this.$t('明细视图')
    }
    if (elementPermissionSet.includes('T_02_0017')) {
      this.tabSource.unshift({
        title: this.$t('单据视图')
      })
      this.tabTitle = this.$t('单据视图')
    }
  },
  methods: {
    // 顶部筛选 调整
    handleSelectTab(e, args) {
      this.tabTitle = args.title
    }
  }
}
</script>

<style scoped lang="scss">
.approval-config {
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  .flex-one {
    flex: 1;
  }
  .grid-area {
    padding: 10px 8px;
  }
}
.full-height {
  /deep/ .supReconTypeTabs {
    flex: 1;
    display: flex;

    .mt-tabs-container {
      width: 100%;
    }
  }
}
</style>
