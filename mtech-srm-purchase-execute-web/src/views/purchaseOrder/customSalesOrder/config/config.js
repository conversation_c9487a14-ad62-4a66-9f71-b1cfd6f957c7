import Vue from 'vue'
import { i18n } from '@/main.js'
export const checkColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  }
]
export const orderStatusColumn = [
  {
    field: 'status',
    width: '120',
    headerText: i18n.t('订单形式'),
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = ''
              if (this.data.status === 4) {
                str = i18n.t('订单取消')
              }
              if (this.data.status === 2 && this.data.version === 1) {
                str = i18n.t('新增订单')
              }
              if (this.data.status === 2 && this.data.version > 1) {
                str = i18n.t('订单变更')
              }
              return str
            }
          }
        })
      }
    }
  }
]
export const columnData1 = [
  {
    field: 'orderCode',
    headerText: i18n.t('售后订单号'),
    width: '160',
    cellTools: []
  },
  {
    field: 'version',
    headerText: i18n.t('历史反馈'),
    width: '150',
    cellTools: [],
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return e - 1 === 0 ? '' : e - 1
      }
    }
  },
  {
    field: 'type',
    headerText: i18n.t('售后类型'),
    width: '150',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('退货订单'),
        1: i18n.t('换货订单'),
        2: i18n.t('维修订单')
      }
    }
  },
  {
    field: 'purStatus',
    headerText: i18n.t('采方发货状态'),
    width: '150',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('未发货'), 1: i18n.t('已发货') }
    }
  },
  {
    field: 'deliveryStatus',
    headerText: i18n.t('发货状态'),
    width: '150',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未发货'),
        1: i18n.t('部分发货'),
        2: i18n.t('全部发货')
      }
    }
  },
  {
    field: 'receiveStatus',
    headerText: i18n.t('收货状态'),
    width: '150',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未收货'),
        1: i18n.t('部分收货'),
        2: i18n.t('全部收货')
      }
    }
  }
]
export const deliverTypeColumn = [
  {
    field: 'deliveryType',
    headerText: i18n.t('订单物流方式'),
    width: '150',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('快递配送'),
        2: i18n.t('物流配送'),
        3: i18n.t('供方自提')
      }
    }
  }
]
export const lastColumn = [
  {
    field: 'createTime',
    headerText: i18n.t('订单日期'),
    width: '150'
  },
  {
    field: 'purTenantName',
    headerText: i18n.t('客户名称'),
    width: '150'
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('客户业务类型'),
    width: '150'
  },
  {
    field: 'requiredDeliveryDate',
    headerText: i18n.t('要求交期'),
    width: '150'
  },
  {
    field: 'buyerOrgName',
    headerText: i18n.t('采购组织名称'),
    width: '150'
  },
  {
    field: 'buyerGroupName',
    headerText: i18n.t('采购组名称'),
    width: '150'
  },
  {
    field: 'buyerUserName',
    headerText: i18n.t('采购员'),
    width: '150'
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    width: '150'
  },
  {
    field: 'paymentName',
    headerText: i18n.t('付款条件'),
    width: '150'
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    width: '150'
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    width: '150'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    width: '150'
  }
]
