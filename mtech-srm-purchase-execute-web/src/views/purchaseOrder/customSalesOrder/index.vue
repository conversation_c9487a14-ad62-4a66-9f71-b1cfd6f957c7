<template>
  <div class="approval-config full-height">
    <mt-template-page
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import {
  checkColumn,
  orderStatusColumn,
  columnData1,
  deliverTypeColumn,
  lastColumn
} from './config/config'
import { BASE_TENANT } from '@/utils/constant'
export default {
  data() {
    return {
      componentConfig: [
        {
          title: this.$t('待确认'),
          toolbar: [],
          grid: {
            allowEditing: true, //开启表格编辑操作
            columnData: checkColumn.concat(
              orderStatusColumn,
              columnData1,
              deliverTypeColumn,
              lastColumn
            ),
            asyncConfig: {
              url: `${BASE_TENANT}/supplierSaleServiceOrder/query`,
              defaultRules: [
                {
                  condition: 'and',
                  field: 'feedbackStatus',
                  label: '',
                  operator: 'equal',
                  value: 0
                }
              ]
            }
          }
        },
        {
          // useToolTemplate: false,
          title: this.$t('确认历史'),
          toolbar: [],
          grid: {
            columnData: checkColumn.concat(columnData1, lastColumn),
            // dataSource: CONFIG.dataSource2,
            asyncConfig: {
              url: `${BASE_TENANT}/supplierSaleServiceOrder/query`,
              defaultRules: [
                {
                  condition: 'and',
                  field: 'feedbackStatus',
                  label: '',
                  operator: 'notequal',
                  value: 0
                }
              ]
            }
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    //点击顶部的操作按钮
    handleClickToolBar(e) {
      console.log('方法1', e)
      console.log(e.grid.getSelectedRecords(), e.toolbar.id)
    },
    //点击表格的操作按钮
    handleClickCellTool(e) {
      console.log('方法2', e)
    },
    //点击表格数据的操作方法
    handleClickCellTitle(e) {
      console.log('方法3', e)
      //点击采购订单号跳转到详情
      if (e.field === 'orderCode') {
        this.$router.push({
          name: 'custom-order-detail',
          query: {
            id: e.data.id,
            type: e.tabIndex === 0 ? '1' : '3'
          }
        })
      }
      //历史反馈
      if (e.field === 'version') {
        this.$router.push({
          name: 'custom-order-detail',
          query: {
            id: e.data.id,
            type: '2',
            orderCode: e.data.orderCode
          }
        })
      }
    }
  }
}
</script>

<style></style>
