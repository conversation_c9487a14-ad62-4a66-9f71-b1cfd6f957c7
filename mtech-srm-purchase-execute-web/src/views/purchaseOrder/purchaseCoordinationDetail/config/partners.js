import { Query } from '@syncfusion/ej2-data'
import cellChanged from '@/components/normalEdit/cellChanged' // 单元格被改变（纯展示）
import partnerCode from '../components/partnerComponents/partnerCode.vue'
import { i18n } from '@/main.js'
export const editColumnBefore = [
  //订单明细
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false,
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    headerText: 'addId主键',
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。而且水平滚动条会回到最左侧。但现在看来好像并不会。。。
    allowEditing: false
  }
]
export const editColumn = [
  {
    width: '150',
    field: 'partnerType',
    headerText: i18n.t('合作类型'),
    editType: 'dropdownedit',
    edit: {
      params: {
        allowFiltering: true,
        dataSource: [
          { text: i18n.t('出票方'), value: '1' },
          { text: i18n.t('订货地址'), value: '2' },
          { text: i18n.t('供应商'), value: '3' }
        ],
        query: new Query(),
        fields: { value: 'value', text: 'text' },
        placeholder: i18n.t('请选择合作类型'),
        floatLabelType: 'Never',
        showClearButton: true
      }
    },
    valueAccessor: function (field, data, column) {
      let dataSource = column.edit.params.dataSource || []
      return dataSource.filter((i) => i.value == data[field])?.[0]?.text
    }
  },
  {
    width: '150',
    field: 'partnerCode',
    headerText: i18n.t('合作编码'),
    editTemplate: () => {
      return { template: partnerCode }
    }
  },
  {
    width: '150',
    field: 'partnerName',
    headerText: i18n.t('合作方名称'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'createTime1',
    headerText: i18n.t('创建时间'),
    allowEditing: false
  }
]
