import percentage from '../components/acceptcePlanComponents/percentage.vue' // 验收计划操作
import acceptanceQuantity from '../components/acceptcePlanComponents/acceptanceQuantity.vue' // 验收计划操作
import freeTotal1 from '../components/acceptcePlanComponents/freeTotal1.vue' // 验收计划操作
import taxTotal1 from '../components/acceptcePlanComponents/taxTotal1.vue' // 验收计划操作
import delayStatus from '../components/acceptcePlanComponents/delayStatus.vue' // 验收计划操作
import { Query } from '@syncfusion/ej2-data'
import consignee from '../components/consignee.vue' //验收人
import payTypeName from '../components/payTypeName.vue' //付款类型
// import { acceptorSelect } from "./columnComponent";
import { i18n } from '@/main.js'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant'
export const editColumnBefore1 = [
  //验收计划
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false,
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    headerText: 'addId主键',
    visible: false,
    allowEditing: false
  },
  {
    width: '150',
    field: 'selfId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    headerText: 'selfId主键',
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。而且水平滚动条会回到最左侧。但现在看来好像并不会。。。
    allowEditing: false
  },
  {
    width: '150',
    field: 'warehouseStatus',
    headerText: i18n.t('入库状态'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料/品相编码'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料/品相名称'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'projectName',
    headerText: i18n.t('项目名称'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'associateExtDocNo',
    headerText: i18n.t('共享单号'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'requireName',
    headerText: i18n.t('需求名称'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'demandCode',
    headerText: i18n.t('需求编码'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'requireDesc',
    headerText: i18n.t('需求描述'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'categoryName',
    headerText: i18n.t('品类名称'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('地点/工厂'),
    allowEditing: false
  },
  {
    width: '200',
    field: 'consignee',
    // allowEditing: false,
    headerText: i18n.t('收货人'),
    editTemplate: () => {
      return {
        template: consignee
      }
    },
    valueAccessor: function (field, data) {
      if (data?.consignee) {
        if (data.consignee.split('-')[3]) {
          return data?.consignee.split('-')[3]
        } else {
          return data?.consignee
        }
      } else {
        return ''
      }
    }
  },
  {
    width: '150',
    field: 'contact',
    headerText: i18n.t('联系方式'),
    allowEditing: false
  },
  {
    allowEditing: false,
    width: '150',
    field: 'receiveAddress',
    headerText: i18n.t('收货地址')
  },
  {
    allowEditing: false,
    width: '150',
    field: 'quantity',
    headerText: i18n.t('订单数量')
  },
  {
    allowEditing: false,
    width: '150',
    field: 'taxPrice',
    headerText: i18n.t('含税单价')
  },
  {
    allowEditing: false,
    width: '150',
    field: 'taxTotal',
    headerText: i18n.t('含税总价')
  },
  {
    allowEditing: false,
    width: '150',
    field: 'freePrice',
    headerText: i18n.t('未税单价')
  },
  {
    allowEditing: false,
    width: '150',
    field: 'freeTotal',
    headerText: i18n.t('未税总价')
  },
  {
    field: 'priceUnit',
    headerText: i18n.t('价格单位'),
    width: '150',
    allowEditing: false
  }
]
export const acceptancePlanColumn = (editColumnParams) => {
  var { that, acceptanceTypeOptions, businessTypeCode } = editColumnParams
  return [
    // {
    //   width: "150",
    //   field: "addId", // 隐藏的主键，获取到数据源时，需要把id赋值给它
    //   headerText: "addId主键",
    //   visible: false,
    //   isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    //   isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。而且水平滚动条会回到最左侧。但现在看来好像并不会。。。
    //   allowEditing: false,
    // },
    {
      width: 0,
      field: 'itemNo1',
      headerText: i18n.t('行号'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'acceptanceStatusName',
      headerText: i18n.t('验收状态'),
      // valueConverter: {
      //   type: "map",
      //   map: { 0: "未验收", 1: "已验收" },
      // },
      allowEditing: false,
      visible: !(
        businessTypeCode === 'BTTCL001' ||
        businessTypeCode === 'BTTCL002' ||
        businessTypeCode === 'BTTCL003'
      )
    },

    {
      width: 0,
      field: 'delayStatus',
      headerText: i18n.t('延期状态'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: delayStatus
        }
      },
      template: function () {
        return {
          template: delayStatus
        }
      }
    },
    {
      width: '150',
      field: 'acceptanceTypeName',
      headerText: i18n.t('验收类型'),
      editType: 'dropdownedit',
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('验收类型')}}</span>
                </div>
              `
          })
        }
      },
      edit: {
        params: {
          allowFiltering: true,
          dataSource: acceptanceTypeOptions,
          query: new Query(),
          placeholder: i18n.t('请选择验收类型'),
          floatLabelType: 'Never',
          fields: { value: 'text', text: 'text' },
          // showClearButton: true,
          actionComplete: () => false,
          change: (e) => {
            console.log('验收类型改变了', e)
            that.selectedChanged({
              fieldCode: 'acceptanceTypeName',
              itemInfo: e.itemData
            })
            if (e.itemData.acceptanceTypeName === '预付') {
              that.$bus.$emit('acceptancePlanSetAcceptor', that.topInfo)
            }
          }
        }
      }
    },
    {
      width: '150',
      field: 'advancePayStatus',
      headerText: i18n.t('是否预付'),
      editType: 'booleanedit',
      valueAccessor: function (field, data) {
        return data?.[field] ? i18n.t('是') : i18n.t('否')
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('是否预付')}}</span>
                </div>
              `
          })
        }
      }
    },
    {
      width: '150',
      field: 'chargeStatus',
      headerText: i18n.t('是否挂账'),
      editType: 'booleanedit',
      valueAccessor: function (field, data) {
        return data?.[field] ? i18n.t('是') : i18n.t('否')
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('是否挂账')}}</span>
                </div>
              `
          })
        }
      },
      edit: {
        params: {
          change: (e) => {
            if (e.checked === true) {
              that.$bus.$emit('acceptancePlanSetPayType')
            }
            // that.selectedChanged({
            //   fieldCode: 'acceptanceTypeName',
            //   itemInfo: e.itemData
            // })
            // if (e.itemData.acceptanceTypeName === '预付') {
            //   that.$bus.$emit('acceptancePlanSetAcceptor', that.topInfo)
            // }
          }
        }
      }
    },
    {
      width: 250,
      field: 'percentage',
      headerText: i18n.t('付款比例（%）'),
      // headerTemplate: () => {
      //   return {
      //     template: Vue.component("headers", {
      //       template: `
      //           <div class="headers">
      //             <span style="color: red">*</span>
      //             <span class="e-headertext">{{$t('付款比例（%）')}}</span>
      //           </div>
      //         `,
      //     }),
      //   };
      // },
      editTemplate: () => {
        return {
          template: percentage
        }
      }
    },
    {
      allowEditing: false,
      width: '150',
      field: 'acceptanceQuantity',
      headerText: i18n.t('验收数量'),
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('验收数量')}}</span>
                </div>
              `
          })
        }
      },
      editTemplate: () => {
        return {
          template: acceptanceQuantity
        }
      }
    },
    {
      width: '150',
      field: 'freeTotal1',
      headerText: i18n.t('未税金额'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: freeTotal1
        }
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('未税金额')}}</span>
                </div>
              `
          })
        }
      }
    },
    {
      width: 0,
      field: 'tax1',
      headerText: i18n.t('税率'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'taxTotal1',
      headerText: i18n.t('含税金额'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: taxTotal1
        }
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('含税金额')}}</span>
                </div>
              `
          })
        }
      }
    },
    {
      field: 'acceptor',
      headerText: i18n.t('验收人'),
      // editType: "dropdownedit",
      width: '450',
      editTemplate: () => {
        return {
          template: consignee
        }
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('验收人')}}</span>
                </div>
              `
          })
        }
      },
      valueAccessor: function (field, data) {
        if (data?.acceptor) {
          if (data.acceptor.split('-')[3]) {
            return data?.acceptor.split('-')[3]
          } else {
            return data?.acceptor
          }
        } else {
          return ''
        }
      }
      // template: acceptorSelect({ showClearBtn: true, that, type: "view" }),
      // editTemplate: acceptorSelect({ showClearBtn: true, that, type: "edit" }),
      // edit: {
      //   params: {
      //     allowFiltering: true,
      //     dataSource: acceptorIdOptions,
      //     query: new Query(),
      //     placeholder: "请选择验收人",
      //     fields: { value: "text", text: "text" },
      //     floatLabelType: "Never",
      //     showClearButton: true,
      //     actionComplete: () => false,
      //     change: (e) => {
      //       console.log("验收人改变了", e);
      //       that.selectedChanged({
      //         fieldCode: "acceptor",
      //         itemInfo: e.itemData,
      //       });
      //     },
      //   },
      // },
    },
    {
      field: 'preAcceptanceTime',
      headerText: i18n.t('计划验收时间'),
      editType: 'dateTimePickerEdit',
      type: 'dateTime',
      format: 'yyyy-MM-dd HH:mm',
      width: '180',
      edit: {
        params: {
          showClearButton: false,
          allowEdit: false
        }
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('计划验收时间')}}</span>
                </div>
              `
          })
        }
      },
      searchOptions: {
        ...MasterDataSelect.timeRange
      }
    },
    {
      width: '200',
      field: 'payTypeName',
      headerText: i18n.t('付款类型'),
      editTemplate: () => {
        return {
          template: payTypeName
        }
      }
      // edit: {
      //   params: {
      //     // allowFiltering: true,
      //     dataSource: payTypeOptions,
      //     query: new Query(),
      //     placeholder: i18n.t("请选择付款类型"),
      //     floatLabelType: "Never",
      //     fields: { text: "itemName", value: "itemName" },
      //     actionComplete: () => false,
      //     change: (e) => {
      //       console.log("付款类型改变了", e);
      //       that.selectedChanged({
      //         fieldCode: "payType",
      //         itemInfo: e.itemData,
      //       });
      //     },
      //   },
      // },
    },
    {
      width: '150',
      field: 'description',
      headerText: i18n.t('验收项描述')
    }
    // {
    //   width: '150',
    //   field: 'handle1',
    //   headerText: i18n.t('操作'),
    //   freeze: 'right',
    //   editTemplate: () => {
    //     return {
    //       template: handleView
    //     }
    //   },
    //   template: function () {
    //     return {
    //       template: handle
    //     }
    //   }
    // }
  ]
}
