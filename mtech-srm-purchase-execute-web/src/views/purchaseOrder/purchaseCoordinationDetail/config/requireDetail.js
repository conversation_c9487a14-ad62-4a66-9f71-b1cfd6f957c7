import Vue from 'vue'
import { Query } from '@syncfusion/ej2-data'
import cellUpload from '@/components/normalEdit/cellUpload' // 单元格上传
import cellFileView from '@/components/normalEdit/cellFileView' // 单元格附件查看
import cellChanged from '@/components/normalEdit/cellChanged' // 单元格被改变（纯展示）
import cellLinkage from '@/components/normalEdit/cellLinkage' // 单元格联动
import costs from '@/components/normalEdit/costs' // 下拉
import selectedItemCode from '@/components/normalEdit/selectedItemCode' // 物料、sku
import selectFactory from '@/components/normalEdit/selectFactory' // 工厂下拉
import selectPrice from '@/components/normalEdit/selectPrice' // 价格记录
import quantity from '@/components/normalEdit/quantity' // 订单数量
import budgetUnitPrice from '@/components/normalEdit/budgetUnitPrice' // 预算单价（未税)
import unPrice from '@/components/normalEdit/unPrice' //不加点单价
import taxedUnitPrice from '@/components/normalEdit/taxedUnitPrice' // 预算单价（含税)
import cellTwoForm from '@/components/normalEdit/cellTwoForm1' // 选不选择物料，是两种显示内容
import cellImgShow from '@/components/normalEdit/cellImgShow' // 单元格显示图片
import requireProfit from '@/components/normalEdit/requireProfit' // 利润中心
import consignee from '../components/consignee.vue' //收货人
import consigneeNew from '../components/consigneeNew.vue' //收货人
import warehouseCode from '../components/warehouseCode.vue' //库存地点
import orderRel from '../components/orderRel' //关联采购订单号
import { i18n } from '@/main.js'

// ===============start==============
// import siteName from "../components/dataGrid/siteName.vue"; // 工厂下拉
// import itemCode from "../components/dataGrid/itemCode.vue"; // 物料下拉

// ===============end================
// 品类、地点/工厂、采购组、基本单位、采购单位、质量免检标识
export const editColumnBefore = [
  //订单明细
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false,
    showInColumnChooser: false
  }
]
export const editColumnEnd = [
  {
    width: '150',
    field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    headerText: i18n.t('addId主键'),
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。而且水平滚动条会回到最左侧。但现在看来好像并不会。。。
    allowEditing: false
  },
  {
    width: 0,
    field: 'otherDataPew',
    headerText: i18n.t('其他数据'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  },
  {
    width: 0,
    field: 'warehouse',
    headerText: i18n.t('库存地点名称'),
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    width: 0,
    field: 'categoryCode',
    headerText: i18n.t('品类编码'),
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    width: 0,
    field: 'unitCode',
    headerText: i18n.t('基本单位'),
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    width: 0,
    field: 'purUnitNameCode',
    headerText: i18n.t('采购单位'),
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    field: 'costCenterName',
    width: 0,
    headerText: i18n.t('成本中心名称'),
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    width: 0,
    field: 'siteCode',
    headerText: i18n.t('工厂code'),
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    width: 0,
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组代码'),
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  }
  // {
  //   width: 0,
  //   field: "freePrice",
  //   headerText: i18n.t("未税单价"),
  //   editTemplate: () => {
  //     return {
  //       template: cellChanged,
  //     };
  //   },
  // },
  // {
  //   width: 0,
  //   field: "freeTotal",
  //   headerText: i18n.t("未税总价"),
  //   editTemplate: () => {
  //     return {
  //       template: cellChanged,
  //     };
  //   },
  // },
  // {
  //   width: 0,
  //   field: "taxPrice",
  //   headerText: i18n.t("含税单价"),
  //   editTemplate: () => {
  //     return {
  //       template: cellChanged,
  //     };
  //   },
  // },
  // {
  //   width: 0,
  //   field: "taxTotal",
  //   headerText: i18n.t("含税总价"),
  //   editTemplate: () => {
  //     return {
  //       template: cellChanged,
  //     };
  //   },
  // },
  // {
  //   width: 0,
  //   field: "unPrice",
  //   headerText: i18n.t("不加点单价"),
  //   editTemplate: () => {
  //     return {
  //       template: cellChanged,
  //     };
  //   },
  // },
  // {
  //   width: 0,
  //   field: "unTotal",
  //   headerText: i18n.t("不加点总价"),
  //   editTemplate: () => {
  //     return {
  //       template: cellChanged,
  //     };
  //   },
  // },
  // {
  //   width: 0,
  //   field: "approvedTotalPrice",
  //   headerText: i18n.t("批准预算总金额"),
  //   editTemplate: () => {
  //     return {
  //       template: cellChanged,
  //     };
  //   },
  // },
  // {
  //   width: 0,
  //   field: "budgetTotalPrice",
  //   headerText: i18n.t("预算总价（未税）"),
  //   editTemplate: () => {
  //     return {
  //       template: cellChanged,
  //     };
  //   },
  // },
  // {
  //   width: 0,
  //   field: "budgetUnitPrice",
  //   headerText: i18n.t("预算单价（未税）"),
  //   editTemplate: () => {
  //     return {
  //       template: cellChanged,
  //     };
  //   },
  // },
  // {
  //   width: 0,
  //   field: "subjectTotal",
  //   headerText: i18n.t("科目总额"),
  //   editTemplate: () => {
  //     return {
  //       template: cellChanged,
  //     };
  //   },
  // },
  // {
  //   width: 0,
  //   field: "taxedTotalPrice",
  //   headerText: i18n.t("预算总价（含税）"),
  //   editTemplate: () => {
  //     return {
  //       template: cellChanged,
  //     };
  //   },
  // },
  // {
  //   width: 0,
  //   field: "taxedUnitPrice",
  //   headerText: i18n.t("预算单价（含税）"),
  //   editTemplate: () => {
  //     return {
  //       template: cellChanged,
  //     };
  //   },
  // },
]
export const editColumn1 = [
  {
    width: '200',
    field: 'requestCode',
    headerText: i18n.t('采购申请编码'),
    allowEditing: false
  },
  {
    width: '200',
    field: 'itemNo1',
    headerText: i18n.t('采购申请行号'),
    allowEditing: false
  }
]
// export const editColumn2 = [
//   {
//     field: "timePromise",
//     headerText: i18n.t("承诺日期"),
//     width: "150",
//   },
//   // {
//   //   field: "countPromise",
//   //   headerText: i18n.t("承诺数量"),
//   //   width: "150",
//   // },
// ];
export const editColumn = (editColumnParams) => {
  var {
    that,
    currencyNameData,
    tradeClauseNameData,
    shippingMethodNameData
    // profitCenterNameData,
    // locationData,
    // entrySource
  } = editColumnParams
  return [
    {
      field: 'itemNo',
      headerText: i18n.t('行号'),
      allowEditing: false,
      valueAccessor: (field, data) => {
        return data[field] || 0
      }
    },
    // 此处四个接口：物料接口、SKU接口、根据物料ID获取工厂（地点/工厂）、根据工厂ID和物料ID获取采购组、基本单位、采购单位
    {
      width: '200',
      field: 'itemCode',
      headerText: i18n.t('物料/品项编码'),
      editTemplate: () => {
        return {
          template: selectedItemCode
          // template: itemCode,
        }
      }
    },
    {
      width: '200',
      field: 'orderRel',
      headerText: i18n.t('关联采购订单号'),
      editTemplate: () => {
        return {
          template: orderRel
        }
      }
    },
    {
      width: '100',
      field: 'closeStatus',
      headerText: i18n.t('行状态'),
      allowEditing: false,
      valueAccessor: function (field, data) {
        if (data?.[field] === 0) {
          return i18n.t('未关闭')
        }
        if (data?.[field] === 1) {
          return i18n.t('已关闭')
        }
      }
    },
    {
      width: '200',
      field: 'itemGroupName',
      headerText: i18n.t('品相组名称'),
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      width: '200',
      field: 'itemGroupCode',
      headerText: i18n.t('品相组编码'),
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料/品项名称'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'picUrl',
      headerText: i18n.t('图片'),
      // allowEditing: false,
      template: () => {
        return { template: cellImgShow }
      },
      editTemplate: () => {
        return { template: cellImgShow }
      }
    },
    {
      field: 'timePromise',
      headerText: i18n.t('承诺日期'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'supplierPromiseQty',
      headerText: i18n.t('承诺数量'),
      width: '100',
      allowEditing: false
    },
    {
      field: 'supRemark',
      headerText: i18n.t('供应商反馈备注'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'contract',
      headerText: i18n.t('推荐合同协议编号'),
      allowEditing: false
    },
    {
      field: 'requestOrderMethod',
      headerText: i18n.t('独立/集中'),
      allowEditing: false,
      valueAccessor: function (field, data) {
        if (data?.[field] === 1) {
          return i18n.t('独立')
        }
        if (data?.[field] === 2) {
          return i18n.t('集中')
        }
      }
    },
    {
      field: 'receiveStatus',
      headerText: i18n.t('收货状态'),
      width: '150',
      allowEditing: false,
      valueAccessor: function (field, data) {
        if (data?.[field] == 0) {
          return i18n.t('未收货')
        }
        if (data?.[field] == 1) {
          return i18n.t('部分收货')
        }
        if (data?.[field] == 2) {
          return i18n.t('全部收货')
        }
      }
      // valueConverter: {
      //   type: "map",
      //   map: {
      //     0: i18n.t("未收货"),
      //     1: i18n.t("部分收货"),
      //     2: i18n.t("全部收货"),
      //   },
      // },
    },
    {
      width: '300',
      field: 'contractRel',
      headerText: i18n.t('价格记录编号'),
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      width: '200',
      field: 'skuCode',
      headerText: i18n.t('SKU编码'), // 带出 物料编码、物料名称、SKU名称、规格型号、品类编码、品类名称
      editTemplate: () => {
        //修改了SKU后， 品类 工厂、采购组、基本单位、采购单位、质量免检标识要清空
        return {
          template: selectedItemCode
        }
      }
    },
    {
      field: 'skuName',
      headerText: i18n.t('SKU名称'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'specification',
      headerText: i18n.t('规格型号'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'deliveryStatus',
      headerText: i18n.t('发货状态'),
      allowEditing: false,
      editTemplate: function () {
        return {
          template: Vue.component('allowEditingFalse', {
            template: `<div>
            {{data.deliveryStatus}}
            </div>`,
            data() {
              return {
                data: { data: {}, rowIndex: -1 }
              }
            }
          })
        }
      }
    },
    {
      field: 'contractType',
      headerText: i18n.t('合同协议类型'),
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: [
            { label: i18n.t('单次采购'), value: '0' },
            { label: i18n.t('框架协议'), value: '1' }
          ],
          fields: { value: 'label', text: 'label' },
          query: new Query(),
          actionComplete: () => false,
          change: (e) => {
            that.selectedChanged({
              fieldCode: 'contractType',
              itemInfo: e.itemData
            })
          }
        }
      }
    },
    {
      field: 'confirmStatus',
      headerText: i18n.t('供应商确认状态'),
      allowEditing: false,
      editTemplate: function () {
        return {
          template: Vue.component('allowEditingFalse', {
            template: `<div>
            {{data.confirmStatus}}
            </div>`,
            data() {
              return {
                data: { data: {}, rowIndex: -1 }
              }
            }
          })
        }
      }
    },
    {
      field: 'warehouseStatus',
      headerText: i18n.t('入库状态'),
      allowEditing: false,
      editTemplate: function () {
        return {
          template: Vue.component('allowEditingFalse', {
            template: `<div>
            {{data.warehouseStatus}}
            </div>`,
            data() {
              return {
                data: { data: {}, rowIndex: -1 }
              }
            }
          })
        }
      }
    },
    {
      field: 'categoryName',
      headerText: i18n.t('品类'),
      // allowEditing: false,
      width: 300,
      editTemplate: () => {
        return {
          template: cellTwoForm
        }
      },
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span>{{data.categoryCode}}-{{data.categoryName}}</span>
                </div>
              `,
            data() {
              return {
                data: {}
              }
            }
          })
        }
      }
    },
    {
      field: 'unitName',
      headerText: i18n.t('基本单位'),
      // allowEditing: false,
      width: 300,
      editTemplate: () => {
        return {
          // template: cellChanged,
          template: cellTwoForm
        }
      },
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span>{{data.unitCode}}-{{data.unitName}}</span>
                </div>
              `,
            data() {
              return {
                data: {}
              }
            }
          })
        }
      }
    },
    {
      field: 'purUnitName',
      headerText: i18n.t('采购单位'),
      width: 300,
      editTemplate: () => {
        return {
          template: cellTwoForm
        }
      },
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span>{{data.purUnitNameCode}}-{{data.purUnitName}}</span>
                </div>
              `,
            data() {
              return {
                data: {}
              }
            }
          })
        }
      }
    },
    {
      width: 225,
      field: 'siteName',
      headerText: i18n.t('地点/工厂'), // 也得是个按钮，点击出弹窗
      editTemplate: () => {
        return {
          template: selectFactory
          // template: siteName,
        }
      },
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span>{{data.siteCode}}-{{data.siteName}}</span>
                </div>
              `,
            data() {
              return {
                data: {}
              }
            },
            mounted() {}
          })
        }
      }
    },
    {
      field: 'buyerOrgId',
      headerText: i18n.t('采购组'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'buyerOrgCode',
      headerText: i18n.t('采购组代码'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'buyerOrgName',
      headerText: i18n.t('采购组名称'),
      // allowEditing: false,
      width: 300,
      editTemplate: () => {
        return {
          // template: cellChanged,
          template: cellTwoForm
        }
      },
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</span>
                </div>
              `,
            data() {
              return {
                data: {}
              }
            },
            mounted() {}
          })
        }
      }
    },
    {
      field: 'requireName',
      headerText: i18n.t('需求名称')
    },

    {
      field: 'requireDesc',
      headerText: i18n.t('需求描述')
    },
    {
      field: 'assetType',
      headerText: i18n.t('资产类别')
    },
    {
      field: 'assetCode',
      headerText: i18n.t('资产编号')
    },
    {
      field: 'assetCard',
      headerText: i18n.t('资产卡片')
    },
    {
      field: 'customerName',
      headerText: i18n.t('客户')
    },
    {
      field: 'customerOrderLineNo',
      headerText: i18n.t('关联销售订单行号')
    },
    {
      field: 'customerOrder',
      headerText: i18n.t('关联客户订单')
    },
    {
      width: '300',
      field: 'profitCenterName',
      headerText: i18n.t('利润中心'),
      editTemplate: () => {
        return {
          template: requireProfit
        }
      }
      // editType: "dropdownedit",
      // edit: {
      //   params: {
      //     allowFiltering: true,
      //     dataSource: profitCenterNameData,
      //     fields: { value: "profitCenterName", text: "profitCenterName" },
      //     query: new Query(),
      //     placeholder: i18n.t("请选择利润中心"),
      //     floatLabelType: "Never",
      //     showClearButton: true,
      //     actionComplete: () => false,
      //     change: (e) => {
      //       that.selectedChanged({
      //         fieldCode: "profitCenterName",
      //         itemInfo: e.itemData,
      //       });
      //     },
      //   },
      // },
    },
    {
      width: 250,
      field: 'quantity',
      headerText: i18n.t('订单数量'),
      editType: 'numericedit',
      editTemplate: () => {
        return {
          template: quantity
        }
      }
    },
    {
      field: 'freePrice',
      headerText: i18n.t('未税单价'),
      editTemplate: () => {
        return {
          template: selectPrice
        }
      }
    },
    {
      field: 'freeTotal',
      headerText: i18n.t('未税总价'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'taxPrice',
      headerText: i18n.t('含税单价'),
      allowEditing: true,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'priceUnit',
      headerText: i18n.t('价格单位'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'taxTotal',
      headerText: i18n.t('含税总价'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'taxid',
      headerText: i18n.t('税率（%）'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'preReceiveQty',
      headerText: i18n.t('待收货数量'),
      editType: 'numericedit',
      allowEditing: false,
      editTemplate: function () {
        return {
          template: Vue.component('allowEditingFalse', {
            template: `<div>
            {{data.preReceiveQty}}
            </div>`,
            data() {
              return {
                data: { data: {}, rowIndex: -1 }
              }
            }
          })
        }
      }
    },
    {
      field: 'receiveQty',
      headerText: i18n.t('已收货数量'),
      editType: 'numericedit',
      allowEditing: false,
      editTemplate: function () {
        return {
          template: Vue.component('allowEditingFalse', {
            template: `<div>
            {{data.receiveQty}}
            </div>`,
            data() {
              return {
                data: { data: {}, rowIndex: -1 }
              }
            }
          })
        }
      }
    },
    {
      field: 'preWarehouseQty',
      headerText: i18n.t('待入库数量'),
      editType: 'numericedit',
      allowEditing: false,
      editTemplate: function () {
        return {
          template: Vue.component('allowEditingFalse', {
            template: `<div>
            {{data.preWarehouseQty}}
            </div>`,
            data() {
              return {
                data: { data: {}, rowIndex: -1 }
              }
            }
          })
        }
      }
    },
    {
      field: 'warehouseQty',
      headerText: i18n.t('已入库数量'),
      editType: 'numericedit',
      allowEditing: false,
      editTemplate: function () {
        return {
          template: Vue.component('allowEditingFalse', {
            template: `<div>
            {{data.warehouseQty}}
            </div>`,
            data() {
              return {
                data: { data: {}, rowIndex: -1 }
              }
            }
          })
        }
      }
    },
    {
      field: 'deliveryQty',
      headerText: i18n.t('已发货数量'),
      editType: 'numericedit',
      allowEditing: false,
      editTemplate: function () {
        return {
          template: Vue.component('allowEditingFalse', {
            template: `<div>
            {{data.deliveryQty}}
            </div>`,
            data() {
              return {
                data: { data: {}, rowIndex: -1 }
              }
            }
          })
        }
      }
    },
    {
      field: 'preDeliveryQty',
      headerText: i18n.t('待发货数量'),
      editType: 'numericedit',
      allowEditing: false,
      editTemplate: function () {
        return {
          template: Vue.component('allowEditingFalse', {
            template: `<div>
            {{data.preDeliveryQty}}
            </div>`,
            data() {
              return {
                data: { data: {}, rowIndex: -1 }
              }
            }
          })
        }
      }
    },
    {
      field: 'budgetQuantity',
      headerText: i18n.t('预测采购量'),
      editType: 'numericedit',
      edit: {
        params: {
          decimals: 3
        }
      }
    },
    {
      field: 'purchasingCycleStart',
      headerText: i18n.t('预测采购周期起'),
      editType: 'dateTimePickerEdit',
      type: 'dateTime',
      format: 'yyyy-MM-dd HH:mm',
      edit: {
        params: {
          showClearButton: false,
          min: new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000)
        }
      }
    },
    {
      field: 'purchasingCycleEnd',
      headerText: i18n.t('预测采购周期止'),
      editType: 'dateTimePickerEdit',
      type: 'dateTime',
      format: 'yyyy-MM-dd HH:mm',
      width: '180',
      edit: {
        params: {
          showClearButton: false,
          min: new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000)
        }
      }
    },
    {
      field: 'cycleType',
      headerText: i18n.t('周期类型'),
      editType: 'dropdownedit',
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span>{{ cycleTypeName }}</span>
                </div>
              `,
            data() {
              return {
                data: {},
                cycleTypeName: ''
              }
            },
            mounted() {
              switch (this.data.cycleType) {
                case 'yearly':
                  this.cycleTypeName = i18n.t('年')
                  break
                case 'weekly':
                  this.cycleTypeName = i18n.t('周')
                  break
                case 'monthly':
                  this.cycleTypeName = i18n.t('月')
                  break
                case 'daily':
                  this.cycleTypeName = i18n.t('日')
                  break

                default:
                  break
              }
            }
          })
        }
      },
      edit: {
        params: {
          allowFiltering: true,
          dataSource: [
            { label: i18n.t('年'), value: 'yearly' },
            { label: i18n.t('周'), value: 'weekly' },
            { label: i18n.t('月'), value: 'monthly' },
            { label: i18n.t('日'), value: 'daily' }
          ],
          fields: { value: 'value', text: 'label' },
          query: new Query(),
          actionComplete: () => false,
          change: (e) => {
            that.selectedChanged({
              fieldCode: 'cycleType',
              itemInfo: e.itemData
            })
          }
        }
      }
    },
    {
      field: 'deliveryDateCycle',
      headerText: i18n.t('交货周期'),
      editType: 'numericedit',
      edit: {
        params: {
          showClearButton: false,
          min: 0
        }
      }
    },
    {
      field: 'requiredDeliveryDate',
      headerText: i18n.t('要求交期'),
      editType: 'datePickerEdit',
      type: 'dateTime',
      format: 'yyyy-MM-dd',
      width: '180',
      edit: {
        params: {
          showClearButton: false,
          min: new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000)
        }
      }
    },
    {
      field: 'applyDate',
      headerText: i18n.t('申请日期'),
      editType: 'dateTimePickerEdit',
      type: 'dateTime',
      format: 'yyyy-MM-dd HH:mm',
      width: '180'
    },
    {
      field: 'budgetUnitPrice',
      headerText: i18n.t('预算单价（未税）'),
      width: 250,
      editTemplate: () => {
        return {
          template: budgetUnitPrice
        }
      }
    },
    {
      field: 'budgetTotalPrice',
      headerText: i18n.t('预算总价（未税）'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'subjectTotal',
      headerText: i18n.t('科目总额'),
      editType: 'numericedit',
      edit: {
        params: {
          decimals: 2
        }
      }
    },
    {
      field: 'taxedUnitPrice',
      headerText: i18n.t('预算单价（含税）'),
      width: 250,
      editTemplate: () => {
        return {
          template: taxedUnitPrice
        }
      }
    },
    {
      field: 'taxedTotalPrice',
      headerText: i18n.t('预算总价（含税）'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'approvedTotalPrice',
      headerText: i18n.t('批准预算总金额'),
      editType: 'numericedit',
      edit: {
        params: {
          decimals: 2
        }
      }
    },
    {
      field: 'currencyName',
      headerText: i18n.t('币种'), // 因为币种修改时要传值 id code，edit那种方式找不到this，所以改用editTemplate
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: currencyNameData,
          fields: { value: 'currencyName', text: 'currencyName' },
          query: new Query(),
          actionComplete: () => false,
          change: (e) => {
            that.selectedChanged({
              fieldCode: 'currencyName',
              itemInfo: {
                currencyCode: e.itemData.currencyCode,
                currencyName: e.itemData.currencyName
              }
            })
          }
        }
      }
    },
    {
      width: 300,
      field: 'orderCosts',
      headerText: i18n.t('成本中心'),
      editTemplate: () => {
        return {
          template: costs
        }
      },
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span>{{data.orderCosts}}-{{data.costCenterName}}</span>
                </div>
              `,
            data() {
              return {
                data: {}
              }
            },
            mounted() {}
          })
        }
      }
    },
    {
      field: 'tradeClauseName',
      headerText: i18n.t('贸易条款'),
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: tradeClauseNameData,
          fields: { value: 'itemName', text: 'itemName' },
          query: new Query(),
          placeholder: i18n.t('请选择贸易条款'),
          floatLabelType: 'Never',
          showClearButton: true,
          actionComplete: () => false,
          change: (e) => {
            that.selectedChanged({
              fieldCode: 'tradeClauseName',
              itemInfo: e.itemData
            })
          }
        }
      }
    },
    {
      width: '350',
      field: 'warehouseCode',
      headerText: i18n.t('库存地点编码'),
      editType: 'dropdownedit',
      editTemplate: () => {
        return {
          template: warehouseCode
        }
      },
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span>{{data.warehouseCode}}-{{data.warehouse}}</span>
                </div>
              `,
            data() {
              return {
                data: {}
              }
            },
            mounted() {}
          })
        }
      }
      // edit: {
      //   params: {
      //     allowFiltering: true,
      //     dataSource: locationData,
      //     fields: { value: "locationCode", text: "locationCode" },
      //     query: new Query(),
      //     placeholder: i18n.t("请选择库存地点"),
      //     floatLabelType: "Never",
      //     showClearButton: true,
      //     actionComplete: () => false,
      //     change: (e) => {
      //       that.$bus.$emit("warehouseChange", e.itemData?.locationName);
      //     },
      //   },
      // },
    },
    {
      field: 'warehouse',
      headerText: i18n.t('库存地点'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'shippingMethodName',
      headerText: i18n.t('物流方式'),
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: shippingMethodNameData,
          fields: { value: 'itemName', text: 'itemName' },
          query: new Query(),
          placeholder: i18n.t('请选择物流方式'),
          floatLabelType: 'Never',
          showClearButton: true,
          actionComplete: () => false,
          change: (e) => {
            that.selectedChanged({
              fieldCode: 'shippingMethodName',
              itemInfo: e.itemData
            })
          }
        }
      }
    },
    {
      field: 'consignee',
      headerText: i18n.t('收货人'),
      width: '350',
      editTemplate: () => {
        return {
          template: consigneeNew
        }
      },
      valueAccessor: function (field, data) {
        if (data?.consignee) {
          if (data.consignee.split('-')[3]) {
            return data?.consignee.split('-')[3]
          } else {
            return data?.consignee
          }
        } else {
          return ''
        }
      }
      // editType: "dropdownedit",
      // edit: {
      //   params: {
      //     allowFiltering: true,
      //     dataSource: receiveUserIdData,
      //     fields: { value: "employeeName", text: "employeeName" },
      //     query: new Query(),
      //     placeholder: i18n.t("请选择收货人"),
      //     floatLabelType: "Never",
      //     showClearButton: true,
      //     actionComplete: () => false,
      //     change: (e) => {
      //       that.selectedChanged({
      //         fieldCode: "consignee",
      //         itemInfo: e.itemData,
      //       });
      //     },
      //   },
      // },
    },
    {
      field: 'contact',
      headerText: i18n.t('联系方式'),
      editTemplate: () => {
        return {
          template: cellLinkage
        }
      }
    },
    {
      field: 'qualityExemptionMarkName',
      headerText: i18n.t('质量免检标识'),
      editTemplate: () => {
        return {
          // template: cellChanged,
          template: cellTwoForm
        }
      }
      // allowEditing: false,
    },
    {
      width: '150',
      field: 'supplierName',
      headerText: i18n.t('推荐供应商'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'urgentTime',
      headerText: i18n.t('加急时间'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'urgentStatus',
      headerText: i18n.t('加急时间'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'returnIdentification',
      headerText: '退货标识 ',
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: [
            { label: i18n.t('是'), value: '1' },
            { label: i18n.t('否'), value: '0' }
          ],
          query: new Query(),
          fields: { value: 'label', text: 'label' },
          actionComplete: () => false,
          change: (e) => {
            that.selectedChanged({
              fieldCode: 'returnIdentification',
              itemInfo: e.itemData
            })
          }
        }
      }
    },
    {
      field: 'printMaterialFileName',
      headerText: i18n.t('流水号附件'),
      width: '150',
      allowEditing: false,
      allowFiltering: false,
      ignore: true,
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div style="cursor: pointer; color: #2783fe" @click="handleClick">
                  <span>{{data.printMaterialFileName}}</span>
                </div>
              `,
            data() {
              return {
                data: {}
              }
            },
            mounted() {},
            methods: {
              handleClick() {
                this.$API.drawingTogether
                  .getFileUrlApi({
                    id: this.data.printMaterialFileId
                  })
                  .then((res) => {
                    if (res.code === 200) {
                      window.open(res.data?.fileUrl)
                    }
                  })
              }
            }
          })
        }
      }
    },
    {
      width: '150',
      field: 'provisionalEstimateStatus',
      headerText: i18n.t('是否暂估价'),
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: [
            { label: i18n.t('是'), value: '1' },
            { label: i18n.t('否'), value: '0' }
          ],
          query: new Query(),
          fields: { value: 'label', text: 'label' },
          actionComplete: () => false,
          change: (e) => {
            that.selectedChanged({
              fieldCode: 'provisionalEstimateStatus',
              itemInfo: e.itemData
            })
          }
        }
      }
    },
    {
      width: '150',
      field: 'purchaseStrategy',
      headerText: i18n.t('采购策略'),
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: [
            { label: i18n.t('标准'), value: '1' },
            { label: i18n.t('寄售'), value: '2' },
            { label: i18n.t('第三方'), value: '3' }
          ],
          query: new Query(),
          fields: { value: 'label', text: 'label' },
          actionComplete: () => false,
          change: (e) => {
            console.log(i18n.t('采购策略'), e)
            that.selectedChanged({
              fieldCode: 'purchaseStrategy',
              itemInfo: e.itemData
            })
          }
        }
      }
    },
    {
      width: '150',
      field: 'subjectType',
      headerText: i18n.t('科目类型'),
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: [
            { label: i18n.t('销售订单'), value: '1' },
            { label: i18n.t('生产工单'), value: '2' },
            { label: i18n.t('成本中心'), value: '0' },
            { label: i18n.t('项目'), value: '3' },
            { label: i18n.t('资产'), value: '4' },
            { label: i18n.t('其他'), value: '5' }
          ],
          query: new Query(),
          fields: { value: 'label', text: 'label' },
          actionComplete: () => false,
          change: (e) => {
            console.log(i18n.t('科目类型'), e)
            that.selectedChanged({
              fieldCode: 'subjectType',
              itemInfo: e.itemData
            })
          }
        }
      }
    },
    {
      width: '350',
      field: 'techContactPerson',
      headerText: i18n.t('技术对接人'),
      editTemplate: () => {
        return {
          template: consignee
        }
      },
      valueAccessor: function (field, data) {
        if (data?.techContactPerson) {
          if (data.techContactPerson.split('-')[3]) {
            return data?.techContactPerson.split('-')[3]
          } else {
            return data?.techContactPerson
          }
        } else {
          return ''
        }
      }
    },
    {
      width: '150',
      field: 'projectTextBatch',
      headerText: i18n.t('项目文本批次')
    },
    {
      width: '150',
      field: 'projectRowText',
      headerText: i18n.t('行项目文本')
    },
    {
      width: '150',
      field: 'artParam',
      headerText: i18n.t('美工变量')
    },
    {
      width: '150',
      field: 'transitQty',
      headerText: i18n.t('在途数量'),
      allowEditing: false
    },
    {
      field: 'unPrice',
      headerText: i18n.t('不加点单价'),
      width: 250,
      editTemplate: () => {
        return {
          template: unPrice
        }
      }
    },
    {
      width: '150',
      field: 'unTotal',
      headerText: i18n.t('不加点金额'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      width: '150',
      field: 'changeRemark',
      headerText: i18n.t('变更备注')
    }
  ]
}

export const lastColumn = [
  // {
  //   width: "150",
  //   field: "costSharingAccName",
  //   headerText: i18n.t("成本中心"),
  // },
  {
    field: 'file',
    width: '250',
    headerText: i18n.t('附件'), // 只有编辑状态能修改、上传，否则只能展示
    allowEditing: false,
    // 使用template时，新增一行且未保存时，获取不到index（编辑状态，正常的template不会的）
    // 使用editTemplate时，显示的值不能是对象
    template: function () {
      return {
        template: cellFileView
      }
    },
    editTemplate: () => {
      return {
        template: cellUpload
      }
    }
  }
]
