import { i18n } from '@/main.js'
export const costShareColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'index',
    headerText: i18n.t('序号')
  },
  {
    // width: "150",
    field: 'costCenterName',
    headerText: i18n.t('成本中心')
  },
  {
    // width: "150",
    field: 'percentage',
    headerText: i18n.t('分摊比例')
  },
  {
    // width: "150",
    field: 'remark',
    headerText: i18n.t('分摊说明')
  }
]
