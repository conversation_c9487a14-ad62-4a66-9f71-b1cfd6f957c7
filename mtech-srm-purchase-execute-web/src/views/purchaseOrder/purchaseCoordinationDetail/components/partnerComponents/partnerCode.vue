<template>
  <div>
    <!-- 合作方编码 -->
    <mt-select
      id="partnerCode"
      v-model="data.partnerCode"
      :data-source="dataSource"
      :fields="{ text: 'contactPartnerCode', value: 'contactPartnerCode' }"
      :placeholder="$t('请选择合作方编码')"
      @change="selectChange"
      v-if="allowEditing"
    ></mt-select>
    <span v-if="!allowEditing">{{ data.partnerCode }}</span>
  </div>
</template>
<script>
export default {
  data() {
    return {
      dataSource: [],
      allowEditing: true
    }
  },
  mounted() {
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    this.getOptions()
  },
  methods: {
    getOptions() {
      let supplierCode = sessionStorage.getItem('supplierCode')
      let params = {
        functionCode: 'BP_FUNCTION_SUPPLIER',
        // functionCode: "",
        orgCode: '', //采购组织code
        orgType: 1,
        partnerCode: supplierCode //供应商code
      }
      this.$API.masterData.businessPartnerQuery(params).then((res) => {
        this.dataSource = res.data || []
      })
    },
    selectChange(val) {
      // console.log("合作方编码改变", val);
      if (!val.itemData) return
      this.$bus.$emit('partnerNameChange', val.itemData.contactPartnerName)
      this.$parent.$emit('selectedChanged', {
        fieldCode: 'partnerCode',
        itemInfo: val.itemData
      })
    }
  }
}
</script>
