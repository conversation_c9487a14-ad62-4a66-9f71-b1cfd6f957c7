<template>
  <div>
    <div class="field-content" v-if="!allowEditing">
      <span>{{ data.orderRel }}</span>
    </div>
    <mt-select
      v-else
      id="orderRel"
      v-model="data.orderRel"
      allow-filtering="true"
      :filtering="purOrderQueryOrder"
      :data-source="orderRelOptions"
      :placeholder="$t('请选择关联采购订单号')"
    ></mt-select>
  </div>
</template>
<script>
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      allowEditing: true,
      orderRelOptions: [] //关联采购订单
    }
  },
  mounted() {
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    this.purOrderQueryOrder()
    this.purOrderQueryOrder = utils.debounce(this.purOrderQueryOrder, 1000)
  },
  methods: {
    purOrderQueryOrder(val) {
      //模糊查询订单号
      if (this.data.orderRel) {
        val = { text: this.data.orderRel }
      }
      let params = {
        condition: 'and',
        page: {
          current: 1,
          size: 50
        },
        defaultRules: [
          {
            label: this.$t('采购订单号'),
            field: 'orderCode',
            type: 'string',
            operator: 'contains',
            value: val && val.text ? val.text : ''
          }
        ]
      }
      this.$API.purchaseOrder.purOrderQueryOrder(params).then((res) => {
        let orderRelOptions = res.data.records.map((item) => {
          return {
            text: item,
            value: item
          }
        })
        orderRelOptions = orderRelOptions.filter((item) => {
          return item.text
        })
        // if (this.data.orderRel) {
        //   let hasItem = orderRelOptions.some((item) => {
        //     return this.data.orderRel === item.text;
        //   });
        //   if (!hasItem) {
        //     orderRelOptions.unshift({
        //       text: this.data.orderRel,
        //       value: this.data.orderRel,
        //     });
        //   }
        // }
        this.orderRelOptions = orderRelOptions
      })
    }
  }
}
</script>
