<template>
  <div :class="['pt20', 'grid-wrap', $route.query.type == '3' && 'grid-wrap-page']">
    <!-- <button @click="getData">获取数据</button> -->
    <mt-data-grid
      id="Grid2"
      class="custom-toolbar-grid"
      :data-source="dataSource"
      :column-data="columnData"
      ref="dataGrid"
      :allow-paging="allowPaging"
      :edit-settings="editSettings"
      :toolbar="toolbarOptions"
      :toolbar-click="toolbarClick"
      @handleSetSelectedInfo="handleSetSelectedInfo"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
      :enable-sticky-header="true"
      :query-cell-info="customiseCell"
      :allow-resizing="true"
      @selectedChanged="selectedChanged"
      @currentChange="handleCurrentChange"
      :page-settings="pageSettings"
      @sizeChange="handleSizeChange"
    ></mt-data-grid>
    <batch-edit-dialog
      v-if="batchEditShow"
      @handleAddDialogShow="handleBatchDialogShow"
      @confirmBatchSuccess="confirmBatchSuccess"
    ></batch-edit-dialog>
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-name="downTemplateName"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>
<script>
// Normal模式，没有绿色背景
// Batch模式，
//      要在 beforeBatchSave中根据args值对dataSource做改变；
//     且新增时，要换成行内新增
//     去掉保存时的dialog (设置editSetting)
//     一列根据另一列动态修改的
import Vue from 'vue'
import {
  editColumnBefore,
  editColumnEnd,
  editColumn,
  editColumn1,
  // editColumn2,
  lastColumn
} from '../config/requireDetail.js'
import * as UTILS from '../../../../utils/utils'
import { cloneDeep } from 'lodash'
import { RegExpMap } from '@/utils/constant'
var bigDecimal = require('js-big-decimal')

export default {
  props: {
    fields: {
      //表头数据
      type: Array,
      default: () => []
    },
    entryType: {
      //进入的类型
      type: String,
      default: '1'
    },
    costSwitch: {
      type: String,
      default: '1'
    },
    entryOrderDetailList: {
      type: Array,
      default: () => []
    },
    entryId: {
      type: String,
      default: '0'
    },
    entryFileList: {
      type: Array,
      default: () => []
    },
    entrySource: {
      type: String,
      default: '1'
    },
    topInfoIsSaved: {
      type: String,
      default: '0'
    },
    moduleKey: {
      type: String,
      default: ''
    },
    topInfo: {
      type: Object,
      default: () => {}
    },
    entryDraft: {
      type: String,
      default: ''
    },
    totalRecordsCount: {
      type: String,
      default: '0'
    }
  },
  components: {
    batchEditDialog: require('./batchEditDialog.vue').default,
    uploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    return {
      pageSettings: {
        currentPage: 1,
        pageSize: 50,
        totalRecordsCount: 0,
        pageSizes: [50, 100, 200]
      },
      allowPaging: false,
      addDialogShow: false,
      modalData: null,
      dataSource: [],
      columnData: editColumnBefore,
      toolbarOptions: [
        {
          text: this.$t('新增'),
          id: 'addinlinebtn',
          prefixIcon: 'e-add',
          fn: this.handleAdd
        },
        {
          text: this.$t('批量编辑'),
          id: 'batchEdit',
          prefixIcon: 'e-edit',
          fn: this.handleBatchEdit
        },
        // "Edit",
        'Delete'
      ],
      editSettings: {
        allowEditing: true,
        allowAdding: true,
        allowDeleting: true,
        mode: 'Normal', // Batch模式下editTemplate使用下拉的话，保存的args里获取不到，所以就回显不上。最终用重新赋值dataSource解决(币种和贸易条款的下拉)
        showConfirmDialog: false,
        showDeleteConfirmDialog: true,
        newRowPosition: 'Bottom'
      },
      editColumns: [],
      siteNameData: [], // 地点/工厂列表
      taxidData: [], // 税率列表
      currencyNameData: [], // 币种
      tradeClauseNameData: [], // 贸易条款
      shippingMethodNameData: [], // 物流方式
      receiveUserIdData: [], // 收货人
      supplierIdData: [], // 推荐供应商
      locationData: [], //库存地点
      profitCenterNameData: [], //利润中心
      nowRowItemId: null, // 当前编辑行的物料id（为了获取工厂）
      rowsSelectedInfo: {}, // 编辑行的下拉数据，包括对应的id和code  {0:{categoryId:"", categoryCode:"", categoryName:"", buyerOrgId:"",buyerOrgCode:""....}}
      deliveryStatusOptions: [
        //发货状态
        { label: this.$t('未发货'), value: '0' },
        { label: this.$t('部分发货'), value: '1' },
        { label: this.$t('全部发货'), value: '2' }
      ],
      confirmStatusOptions: [
        //供应商确认状态
        { label: this.$t('待确认'), value: '0' },
        { label: this.$t('反馈异常'), value: '1' },
        { label: this.$t('反馈正常'), value: '2' }
      ],
      warehouseStatusOptions: [
        //入库状态
        { label: this.$t('未入库'), value: '0' },
        { label: this.$t('部分入库'), value: '1' },
        { label: this.$t('全部入库'), value: '2' }
      ],
      purchaseStrategyOptions: [
        //采购策略
        { label: this.$t('标准'), value: '1' },
        { label: this.$t('寄售'), value: '2' },
        { label: this.$t('第三方'), value: '3' }
      ],
      subjectTypeOptions: [
        //科目类型
        { label: this.$t('销售订单'), value: '1' },
        { label: this.$t('生产工单'), value: '2' },
        { label: this.$t('成本中心'), value: '0' },
        { label: this.$t('项目'), value: '3' },
        { label: this.$t('资产'), value: '4' },
        { label: this.$t('其他'), value: '5' }
      ],
      provisionalEstimateStatusOptions: [
        //是否是暂估价
        { label: this.$t('是'), value: '1' },
        { label: this.$t('否'), value: '0' }
      ],
      returnIdentificationOptions: [
        //退货标识
        { label: this.$t('是'), value: '1' },
        { label: this.$t('否'), value: '0' }
      ],
      domesticDemandFlagOptions: [
        //退货标识
        { label: this.$t('是'), value: 'E' },
        { label: this.$t('否'), value: 'F' }
      ],
      batchEditShow: false, // 批量编辑的弹窗
      batchRowIndexs: [], // 批量编辑时的行序号
      addChangeRecords: [],
      delIds: [],
      addIdFileObj: {}, //
      selfAddId: '1',
      nowEditRowFlag: '', // 当前编辑行的id或addId，可以是编辑也可能是新增。。在actionBegin时记录，在actionComplete时清空
      updateCompanyLink: '2',
      isEdit: '2', //是否在编辑状态
      judgeList: [], //校验明细行的数据
      contractItemIdList: [], //合同明细ids
      downTemplateParams: {}, // 下载模板参数
      uploadParams: {}, // 明细行上传excel的
      requestUrls: {},
      downTemplateName: this.$t('采购订单明细模板'),
      requiredIdList: [], //校验明细行的数据 申请id list
      acceptanceTypeOptions: [], //验收类型下拉数据
      acceptorIdOptions: [], //验收人
      sendList: [], //封装好给后端的数据
      unMobileCompany: [] // 不用校验手机格式的公司
    }
  },
  watch: {
    totalRecordsCount: {
      handler(val) {
        this.pageSettings.totalRecordsCount = Number(val)
      },
      immediate: true
    },
    fields: {
      async handler() {
        await this.getDropDownData() // 要等到下拉数据获取后，才能组合列
        this.getMasterDropData()
        this.handleUnionColumns()
        this.updateGrid()
      },
      immediate: true
    },
    entryType: {
      handler() {
        this.init()
      },
      immediate: true
    },
    entrySource: {
      handler() {
        this.init()
      },
      immediate: true
    },
    costSwitch: {
      handler() {
        this.updateGrid()
      },
      immediate: true
    },
    entryFileList: {
      handler(val) {
        if (val.length) {
          // this.updataEntryList();
        }
      },
      immediate: true
    },
    entryOrderDetailList: {
      handler(val) {
        if (!val) {
          return
        }
        this.updataEntryList()
      },
      immediate: true
    }
  },
  mounted() {
    this.$bus.$on('openWhole1', (e) => {
      if (e) {
        // 清空成本列的内容，并且禁用
        this.$refs.dataGrid.ejsRef.hideColumns(this.$t('成本中心'))
      } else {
        // 解除成本列的禁用
        this.$refs.dataGrid.ejsRef.showColumns(this.$t('成本中心'))
      }
    })
    this.setSession()
    this.getUnMobileCompany()
  },
  methods: {
    getModuleData() {
      this.$emit('getModuleData1', this.pageSettings.currentPage, this.pageSettings.pageSize)
    },
    // 分页的两个方法
    handleCurrentChange(currentPage) {
      console.log(currentPage, '当前页')
      this.pageSettings.currentPage = currentPage
      this.getModuleData()
    },
    handleSizeChange(pageSize) {
      console.log(pageSize, '当前页数')
      this.pageSettings.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getModuleData()
    },
    //  无物料时，一些列是下拉。在此获取数据源，存到session
    getMasterDropData() {
      // 1. 品类
      if (!sessionStorage.getItem('categoryNameSession')) {
        // this.$API.masterData.getCategory({}).then((res) => {
        let sessionData = {
          dataSource: [],
          changeRowObj: {
            categoryId: 'id',
            categoryCode: 'categoryCode',
            categoryName: 'categoryName'
          },
          fields: { text: 'label', value: 'categoryName' },
          pld: this.$t('请选择品类')
        }
        sessionStorage.setItem('categoryNameSession', JSON.stringify(sessionData))
        // });
      }
      //todo 2. 采购组
      if (!sessionStorage.getItem('buyerOrgNameSession')) {
        // this.$API.masterData.getbussinessGroup({
        //   groupTypeCode: "BG001CG",
        // });
        // .then((res) => {
        // let list = res.data;
        // list.forEach((item) => {
        //   item.label = `${item.groupCode}-${item.groupName}`;
        // });
        let sessionData = {
          dataSource: [],
          changeFieldObj: {
            buyerOrgId: 'id',
            buyerOrgCode: 'groupCode',
            buyerOrgName: 'groupName'
          },
          fields: { text: 'label', value: 'groupName' },
          pld: this.$t('请选择采购组')
        }
        sessionStorage.setItem('buyerOrgNameSession', JSON.stringify(sessionData))
        // });
      }
      // 3. 基本单位/采购单位
      if (!sessionStorage.getItem('unitNameSession')) {
        // this.$API.masterData.getUnit({}).then((res) => {
        let sessionData1 = {
          dataSource: [],
          changeRowObj: {
            unitId: 'id',
            unitCode: 'unitCode',
            unitName: 'unitName'
          },
          fields: { text: 'label', value: 'unitName' },
          pld: this.$t('请选择基本单位')
        }
        let sessionData2 = {
          dataSource: [],
          changeRowObj: {
            orderUnitId: 'id',
            orderUnitCode: 'unitCode',
            orderUnitName: 'unitName'
          },
          fields: { text: 'label', value: 'purUnitName' },
          pld: this.$t('请选择采购单位')
        }
        sessionStorage.setItem('unitNameSession', JSON.stringify(sessionData1))
        sessionStorage.setItem('purUnitNameSession', JSON.stringify(sessionData2))
        // });
      }
      // 4. 质量免检标识
      if (!sessionStorage.getItem('qualityExemptionMarkNameSession')) {
        let sessionData = {
          dataSource: [
            {
              value: '0',
              label: this.$t('需检验')
            },
            {
              value: '1',
              label: this.$t('免检')
            }
          ],
          changeRowObj: {
            qualityExemptionMarkName: 'value'
          },
          fields: { text: 'label', value: 'label' },
          pld: this.$t('请选择质量免检标识')
        }
        sessionStorage.setItem('qualityExemptionMarkNameSession', JSON.stringify(sessionData))
      }
    },
    handleUpload() {
      //上传订单明细 校验头部信息供应商 公司 业务类型 校验表格是否处于编辑状态 是的话先停止表格编辑状态
      this.$emit('checkTopInfo')
      setTimeout(() => {
        if (this.topInfoIsSaved === '1') {
          if (this.isEdit === '1') {
            this.endEdit()
            setTimeout(() => {
              this.handleUploadNext()
            }, 300)
          } else {
            this.handleUploadNext()
          }
        }
      }, 500)
    },
    handleUploadNext() {
      // 设置上传参数
      this.downTemplateParams = {
        configId: this.topInfo.businessId
      }
      this.uploadParams = {
        configId: this.topInfo.businessId,
        moduleKey: this.moduleKey
      }
      this.requestUrls = {
        templateUrlPre: 'purchaseOrder',
        templateUrl: 'purOrderTemplateDownload',
        uploadUrl: 'purOrderUpload'
      }
      this.showUploadExcel(true)
    },
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 上传成功后，获取到的数据
    upExcelConfirm(res) {
      this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      let addRow = res.data || []
      this.addUploadDataSource(addRow)
    },
    purOrderDownload() {
      //下载订单明细showList
      let selectedrecords = this.$refs.dataGrid.ejsRef.getSelectedRecords()
      if (!selectedrecords.length) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let list = []
      selectedrecords.forEach((item) => {
        this.sendList.some((item1) => {
          if (item.addId === item1.addId) {
            list.push(item1)
          }
        })
      })
      let params = {
        businessTypeCode: this.topInfo.businessTypeCode,
        docType: 'po',
        orderDetails: list
      }
      this.$store.commit('startLoading')
      this.$API.purchaseOrder.purOrderDownload(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    addUploadDataSource(addRow) {
      //添加导入的数据
      let dataSource = cloneDeep(this.$refs.dataGrid.ejsRef.getCurrentViewRecords())
      addRow.forEach((item) => {
        item.addId = this.selfAddId++
        item.deliveryStatusValue = 0 //发货状态
        item.deliveryStatus = this.$t('未发货')
        item.confirmStatusValue = 0 //供应商确认状态
        item.confirmStatus = this.$t('待确认')
        item.warehouseStatusValue = 0 //入库状态
        item.warehouseStatus = this.$t('未入库')
        item.itemId = item.itemId || '1' //todo 后面去掉
        item.siteId = item.siteId || '1'
        item.itemNo = 0
        // item.categoryId = "23";
        // item.siteCode = "32";
        item.purUnitNameCode = item.purUnitCode
        if (item.purchasingCycleStart) {
          item.purchasingCycleStart = new Date(Number(item.purchasingCycleStart))
        }
        if (item.purchasingCycleEnd) {
          item.purchasingCycleEnd = new Date(Number(item.purchasingCycleEnd))
        }
        if (item.requiredDeliveryDate) {
          item.requiredDeliveryDate = new Date(Number(item.requiredDeliveryDate))
        }
        item.file = null
        item.preWarehouseQty = 0 //待入库数量
        item.warehouseQty = 0 //已入库数量
        item.deliveryQty = 0 //已发货数量
        item.preDeliveryQty = 0 ///待发货数量 (发货状态 入库状态 供应商确认状态  待入库数量 已入库数量 已发货数量 待发货数量 系统动态生成的不能改)
        item.freePrice = item.freePrice || 0 //未税单价
        item.freeTotal = item.freeTotal || 0 //未税总价
        item.taxPrice = item.taxPrice || 0 //含税单价
        item.taxTotal = item.taxTotal || 0 //含税总价 采购申请数据未返回默认0
        item.priceUnit = item.priceUnit || 0
        item.taxid = item.taxid || 0 //税率
        item.consignee = item.consignee || '' //收货人
        item.consigneeId = item.consigneeId || '1' //采购申请收货人id没有
        item.contact = item.contact || '' //联系方式
        if (item.qualityExemptionMarkName === this.$t('需检验')) {
          item.qualityExemptionMarkId = '0'
        }
        if (item.qualityExemptionMarkName === this.$t('免检')) {
          item.qualityExemptionMarkId = '1'
        }
      })
      dataSource = dataSource.concat(addRow)
      this.dataSource = dataSource
    },
    handleAdd1() {
      //添加展示合同列表
      if (this.isEdit === '1') {
        this.endEdit()
      }
      this.$refs.contract.show()
    },
    setSession() {
      let itemCodeData = {
        requestUrl: ['item/paged-query'], // 因为主数据的物料和sku的下拉和弹窗是两个接口给的，所以这里传两个值
        title: this.$t('请选择物料/品项编码'),
        changedFieldArr: [
          'itemName',
          'specification',
          'itemGroupCode',
          'itemGroupName',
          'unitName',
          'unitCode'
        ], // 物料编码 要改变的是物料名称、规格型号、基本单位
        changedRowArr: ['itemId', 'unitId', 'unitCode'], //要存入的是物料id
        clearFieldArr: [
          // 要清空的列
          'skuCode', //sku编码
          'skuName', //sku名称
          'categoryName', //品类名称
          'siteName', //工厂名称
          // "orderUnitName", //采购单位
          'qualityExemptionMarkName', //质量免检标识
          'buyerOrgId', //采购组
          'buyerOrgCode', //采购组
          'buyerOrgName', //采购组
          'purUnitName', //采购单位
          'taxid', //税率
          'contractRel', //关联合同协议编号
          'taxPrice', //含税单价
          'taxTotal', //含税总价
          'priceUnit',
          'freePrice', //未税单价
          'freeTotal', //未税总价
          'warehouseCode',
          'warehouse'
        ], // 改变物料 会清空 sku编码、sku名称、品类 、工厂、采购组、采购单位、质量免检标识
        clearRowArr: [
          // 要清空的存入
          'warehouse',
          'skuId',
          'siteId', // 地点/工厂
          'siteCode',
          'categoryId', // 品类
          'orderUnitId', // 采购单位
          'requestOrderMethod' //独立/集中
        ]
      }
      sessionStorage.setItem('itemCodeData', JSON.stringify(itemCodeData))

      let skuCodeData = {
        requestUrl: ['/sku/page-query-with-item'], // 因为主数据的物料和sku的下拉和弹窗是两个接口给的，所以这里传两个值
        title: this.$t('请选择sku编码'),
        changedFieldArr: [
          'itemCode',
          'itemName',
          'skuName',
          'specification',
          'itemGroupCode',
          'itemGroupName',
          'unitName',
          'unitCode'
        ],
        changedRowArr: ['itemId', 'skuId', 'unitId', 'unitCode'], //要存入的是物料id
        clearFieldArr: [
          'categoryName', //品类名称
          'siteName', //工厂名称
          // "orderUnitName", //采购单位
          'qualityExemptionMarkName', //质量免检标识
          'buyerOrgId', //采购组
          'buyerOrgCode', //采购组
          'buyerOrgName', //采购组
          'purUnitName', //采购单位
          'taxid', //税率
          'contractRel', //关联合同协议编号
          'taxPrice', //含税单价
          'taxTotal', //含税总价
          'priceUnit',
          'freePrice', //未税单价
          'freeTotal', //未税总价
          'warehouseCode',
          'warehouse'
        ], // 改变物料 会清空 品类 、工厂、采购组、采购单位、质量免检标识
        clearRowArr: [
          'siteId', // 地点/工厂
          'siteCode',
          'warehouse',
          'categoryId', // 品类
          'buyerOrgId', // 采购组
          'orderUnitId' // 采购单位
        ]
      }
      sessionStorage.setItem('skuCodeData', JSON.stringify(skuCodeData))

      let siteNameData = {
        sourceField: 'siteName', // 改变它数据源的字段，即参数
        title: this.$t('请选择地点/工厂'),
        requestUrl: 'getFactoryList',
        fields: { text: 'organizationName', value: 'organizationName' },
        // 因为物料+工厂变了，就直接改变，不需要清空了
        changedFieldArr: [
          // 改变工厂 会改变 品类 、工厂、采购组、基本单位、采购单位、质量免检标识 库存地点
          // "siteName",
          'categoryName',
          'categoryCode',
          'buyerOrgId',
          'buyerOrgCode',
          'buyerOrgName',
          'unitName',
          'unitCode',
          'orderUnitName',
          'purUnitName',
          'purUnitNameCode',
          'qualityExemptionMarkName',
          'warehouseCode',
          'warehouse'
        ],
        changedRowArr: [
          //TODO没用到
          // 一行数据里，还带的其他数据。也要被改变/清空
          'categoryId', // 品类
          'warehouse',
          // "buyerOrgId", // 采购组
          'unitId', // 基本单位
          'orderUnitId' // 采购单位
        ]
      }
      sessionStorage.setItem('siteNameData', JSON.stringify(siteNameData))
    },
    // 物料、sku、工厂的点击确认> 存入这一行的选择数据
    // params：普通下拉的：有field、value
    selectedChanged(params) {
      this.handleSetSelectedInfo(params)
    },
    // 不可修改的单元格，修改背景色
    customiseCell(args) {
      if (!args.column.allowEditing && !['itemCode'].includes(args.column.field)) {
        args.cell.classList.add('bg-grey')
      }
    },
    updataEntryList() {
      //更新文件和列表匹配
      if (!this.entryOrderDetailList.length) {
        this.dataSource = []
        return
      }
      console.log('我是带入的订单明细啊啊', this.entryOrderDetailList)
      this.entryOrderDetailList.forEach((item) => {
        item.addId = this.selfAddId++
        item.uniqueKey = item.addId
        item.closeStatus = item.closeStatus || 0
        item.deliveryStatusValue = item.deliveryStatus || 0 //发货状态
        item.deliveryStatus = this.deliveryStatusOptions.find((item1) => {
          return item1.value == item.deliveryStatusValue
        })?.label
        item.confirmStatusValue = item.confirmStatus || 0 //供应商确认状态
        item.confirmStatus = this.confirmStatusOptions.find((item1) => {
          return item1.value == item.confirmStatusValue
        })?.label
        item.warehouseStatusValue = item.warehouseStatus || 0 //入库状态
        item.warehouseStatus = this.warehouseStatusOptions.find((item1) => {
          return item1.value == item.warehouseStatusValue
        })?.label
        item.purchaseStrategyValue = item.purchaseStrategy || '1' //采购策略
        item.purchaseStrategy = this.purchaseStrategyOptions.find((item1) => {
          return item1.value == item.purchaseStrategyValue
        })?.label
        item.subjectTypeValue = item.subjectType || '0' //科目类型
        item.subjectType = this.subjectTypeOptions.find((item1) => {
          return item1.value == item.subjectTypeValue
        })?.label
        item.provisionalEstimateStatusValue = item.provisionalEstimateStatus || '0' //是否是暂估价
        item.provisionalEstimateStatus = this.provisionalEstimateStatusOptions.find((item1) => {
          return item1.value == item.provisionalEstimateStatusValue
        })?.label
        item.returnIdentificationValue = item.returnIdentification || '0'
        item.returnIdentification = this.returnIdentificationOptions.find((item1) => {
          return item1.value == item.returnIdentificationValue
        })?.label
        item.domesticDemandFlagValue = item.domesticDemandFlag
        item.domesticDemandFlag = this.domesticDemandFlagOptions.find((item1) => {
          return item1.value == item.domesticDemandFlagValue
        })?.label
        item.purUnitNameCode = item.purUnitCode || ''
        item.file = []
        item.timePromise1 = item.timePromise || '0'
        if (item.timePromise && item.timePromise.length === 13) {
          item.timePromise = UTILS.default.dateFormat(Number(item.timePromise), 'Y-m-d')
        } else {
          item.timePromise = ''
        }
        if (this.entryType === '2' || this.entryType === '3') {
          //采购新建数据 编进进入 采购申请编辑进入 详情
          item.entryFrom = '2'
          item.profitCenterName1 = item.profitCenterName
          item.profitCenterName = item?.profitCenterCode + ' - ' + item?.profitCenterName
          if (item.urgentTime === '0') {
            item.urgentTime = ''
            item.urgentStatus = '普通'
          } else if (item.urgentTime && item.urgentTime.length === 13) {
            item.urgentTime = UTILS.default.dateFormat(Number(item.urgentTime), 'Y-m-d')
            item.urgentStatus = '加急'
          }
          item.picUrl = item.skuPicUrl
          item.agreementCode = item.contract //推荐合同/协议编号
          item.contractTypeValue = item.contractType //合同协议类型
          if (item.contractTypeValue == '0') {
            item.contractType = this.$t('单次采购')
          }
          if (item.contractTypeValue == '1') {
            item.contractType = this.$t('框架协议')
          }
          item.fieldDataList = item.fieldDataList || []
          item.fieldDataList.forEach((item1) => {
            //对应的动态字段 需求名称,需求描述,资产类别,资产编号,资产卡片,客户,关联客户订单
            item[item1.fieldCode] = item1.fieldData
          })
          item.budgetQuantity = item.forecastQty //预测采购量
          item.preWarehouseQty = item.preWarehouseQty || 0 //待入库数量
          item.warehouseQty = item.warehouseQty || 0 //已入库数量
          if (item.purchasingCycleEnd && item.purchasingCycleEnd.length == 13) {
            item.purchasingCycleEnd = new Date(Number(item.purchasingCycleEnd))
          } else {
            item.purchasingCycleEnd = ''
          }
          if (item.requiredDeliveryDate && item.requiredDeliveryDate.length == 13) {
            item.requiredDeliveryDate = new Date(Number(item.requiredDeliveryDate))
          } else {
            item.requiredDeliveryDate = this.getToday()
          }
          if (item.purchasingCycleStart && item.purchasingCycleStart.length == 13) {
            item.purchasingCycleStart = new Date(Number(item.purchasingCycleStart))
          } else {
            item.purchasingCycleStart = ''
          }
          if (item.applyDate && item.applyDate.length == 13) {
            item.applyDate = new Date(Number(item.applyDate))
          } else {
            item.applyDate = ''
          }
          item.orderUnitId = item.purUnitId
          if (item.costs && this.entryDraft !== '1') {
            item.costs1 = item.costs
            // let str = "";
            // item.costs.forEach((item1) => {
            //   str += item1.costCenterCode + "-" + item1.percentage + "%;";
            // });
            // item.orderCosts = str;
            item.orderCosts = item.costs1[0].costCenterCode
            item.costCenterId = item.costs1[0].costCenterId
            item.costId = item.costs1[0].id
            item.costCenterCode = item.costs1[0].costCenterCode
            item.costCenterName = item.costs1[0].costCenterName
          }
          if (this.entryDraft === '1' && item.costs) {
            item.costs1 = item.costs
            item.orderCosts = item.costs1[0].costCenterCode
            item.costCenterId = item.costs1[0].costCenterId
            item.costId = item.costs1[0].id
            item.costCenterCode = item.costs1[0].costCenterCode
            item.costCenterName = item.costs1[0].costCenterName
          }
          if (item.taxTotal < 0) {
            item.taxTotal = '*'
          }
          if (item.taxPrice < 0) {
            item.taxPrice = '*'
          }
          if (item.freeTotal < 0) {
            item.freeTotal = '*'
          }
          if (item.freePrice < 0) {
            item.freePrice = '*'
          }
          if (item.unPrice < 0) {
            item.unPrice = ''
          }
          if (item.unTotal < 0) {
            item.unTotal = ''
          }
          if (item.approvedTotalPrice < 0) {
            item.approvedTotalPrice = ''
          }
          if (item.budgetTotalPrice < 0) {
            item.budgetTotalPrice = ''
          }
          if (item.budgetUnitPrice < 0) {
            item.budgetUnitPrice = ''
          }
          if (item.taxedTotalPrice < 0) {
            item.taxedTotalPrice = ''
          }
          if (item.taxedUnitPrice < 0) {
            item.taxedUnitPrice = ''
          }
          if (item.subjectTotal < 0) {
            item.subjectTotal = ''
          }
        }
        if (this.entrySource === '2' && this.entryType === '1') {
          item.preWarehouseQty = 0 //待入库数量
          item.warehouseQty = 0 //已入库数量
          item.deliveryQty = 0 //已发货数量
          item.preDeliveryQty = 0 ///待发货数量
          item.picUrl = item.skuUrl
          item.quantity = item.goodsNum
          // item.taxTotal = item.subtotal;
          // item.taxPrice = item.price;
          item.specification = item.salesoFproperty
          item.purchasingCycleStart = this.getToday()
          item.purchasingCycleEnd = this.getToday()
          item.requiredDeliveryDate = this.getToday()
        }
        if ((this.entrySource === '0' || this.entrySource === '4') && this.entryType === '1') {
          if (this.entrySource === '0') {
            item.itemNo1 = item.itemNo
            item.itemNo = 0
            item.contractRel = null
            item.taxCode = null
            item.freePrice = null //未税单价
            item.freeTotal = null //未税总价
            item.taxPrice = null //含税单价
            item.taxTotal = null //含税总价 采购申请数据未返回默认0
            item.priceUnit = null
            item.taxid = null //税率
          }
          if (this.entrySource === '0') {
            item.requiredId = item.id
          }
          if (this.entrySource === '4') {
            item.requiredId = item.requiredId || ''
          }
          item.urgentTime = ''
          item.urgentStatus = '普通'
          item.entryFrom = '0'
          item.itemCode = item.itemCode || ''
          item.categoryName = item.categoryName || ''
          item.forecastQty = item.budgetQuantity //预测采购量
          // item.categoryId = "23";
          // item.siteCode = "32";
          item.buyerOrgCode = item.buyerOrgCode || ''
          item.buyerOrgName = item.buyerOrgName || ''
          item.purUnitNameCode = item.orderUnitCode
          item.purUnitName = item.orderUnitName || ''
          if (item.costSharingResponses && item.costSharingResponses.length) {
            // let str = "";
            item.costSharingResponses.forEach((item1) => {
              if (item1.type === 1) {
                // str +=
                //   item1.costSharingAccCode + "-" + item1.costSharingAccName;
                item.orderCosts = item1.costSharingAccCode
                item.costCenterName = item1.costSharingAccName
              }
            })
            // console.log(str, "我是成本中心33");
            // item.orderCosts = str;
          }
          // item.profitCenterName = item.profitCenterName || "";
          item.profitCenterName1 = item.profitCenterName
          item.profitCenterName = item?.profitCenterCode + ' - ' + item?.profitCenterName
          //采购申请新建进入
          if (item.startDate) {
            item.purchasingCycleStart = new Date(item.startDate)
          } else {
            item.purchasingCycleStart = this.getToday()
          }
          if (item.endDate) {
            item.purchasingCycleEnd = new Date(item.endDate)
          } else {
            item.purchasingCycleEnd = this.getToday()
          }
          if (item.requiredDeliveryDate) {
            item.requiredDeliveryDate = new Date(item.requiredDeliveryDate)
          } else {
            item.requiredDeliveryDate = this.getToday()
          }
          if (item.applyDate) {
            item.applyDate = new Date(item.applyDate)
          }
          item.budgetSubject = item.budgetSubjectName
          item.specification = item.spec
          item.quantity = item.remainingQuantity //订单数量对应 剩余可创建数量
          item.preWarehouseQty = 0 //待入库数量
          item.warehouseQty = 0 //已入库数量
          item.deliveryQty = 0 //已发货数量
          item.preDeliveryQty = 0 ///待发货数量 (发货状态 入库状态 供应商确认状态  待入库数量 已入库数量 已发货数量 待发货数量 系统动态生成的不能改)
          item.consignee = item.receiveUserName || item.applyUserName //收货人
          item.consigneeId = item.receiveUserId || item.applyUserId //采购申请收货人id没有
          item.contact = item.linkWay || '' //联系方式
          if (item.budgetUnitPrice && item.quantity) {
            //预算总价未税
            item.budgetTotalPrice = item.budgetUnitPrice * item.quantity
          }
          if (item.taxedUnitPrice && item.quantity) {
            //预算总价含税
            item.taxedTotalPrice = item.taxedUnitPrice * item.quantity
          }
          if (item.qualityExemptionMark === 0) {
            item.qualityExemptionMarkName = this.$t('需检验')
          }
          if (item.qualityExemptionMark === 1) {
            item.qualityExemptionMarkName = this.$t('免检')
          }
        }
        if ((this.entrySource === '0' || this.entrySource === '4') && this.entryType === '2') {
          //申请或者合同编辑进入
          if (this.entrySource === '0') {
            //从采购申请进入
            if (item.requestRel) {
              item.itemNo1 = item.requestRel[0].requestItemNo
              item.requestCode = item.requestRel[0].requestCode
            }
          }
          if (this.entrySource === '4') {
            if (item.requestRel) {
              item.itemNo1 = item.requestRel[0].relItemNo
              item.requestCode = item.requestRel[0].relCode
            }
          }
        }
        if (this.entryFileList) {
          let entryfiles = []
          this.entryFileList.forEach((item1) => {
            if (item1.nodeCode === 'po_item_file') {
              //订单明细附件
              entryfiles.push(item1)
            }
          })
          if (!entryfiles) return
          entryfiles.forEach((item2) => {
            if (item2.fileDetailId === item.id) {
              item.file = item.file.concat(item2)
            }
          })
          item.file = JSON.stringify(item.file)
        }
      })
      this.dataSource = cloneDeep(this.entryOrderDetailList)
      // this.updateGrid();
    },
    init() {
      if (this.entryType === '1') {
        //新建
        if (this.entrySource === '1') {
          //手工新建
          this.toolbarOptions = [
            {
              text: this.$t('新增'),
              id: 'addinlinebtn',
              prefixIcon: 'e-add',
              fn: this.handleAdd
            },
            {
              text: this.$t('批量编辑'),
              id: 'batchEdit',
              prefixIcon: 'e-edit',
              fn: this.handleBatchEdit
            },
            'Delete',
            {
              text: this.$t('上传'),
              prefixIcon: 'e-upload-1',
              id: 'upload',
              fn: this.handleUpload
            },
            {
              text: this.$t('下载'),
              prefixIcon: 'e-expand',
              id: 'download',
              fn: this.handleDownload
            },
            {
              prefixIcon: 'icon_solid_Activateorder',
              id: 'Expedited',
              text: this.$t('加急')
            },
            {
              text: this.$t('取消加急'),
              id: 'CancleExpedited',
              prefixIcon: 'icon_solid_Activateorder'
            }
            // {
            //   text: this.$t("查看图纸"),
            //   id: "ViewDrawings",
            //   prefixIcon: "icon_solid_Activateorder",
            // },
          ]
        }
        if (this.entrySource === '4') {
          //从合同进入
          this.toolbarOptions = [
            // {
            //   text: this.$t("添加"),
            //   id: "addinlinebtn1",
            //   prefixIcon: "e-add",
            //   fn: this.handleAdd1,
            // },
            // "Delete",
            {
              text: this.$t('下载'),
              prefixIcon: 'e-expand',
              id: 'download',
              fn: this.handleDownload
            },
            {
              prefixIcon: 'icon_solid_Activateorder',
              id: 'Expedited',
              text: this.$t('加急')
            },
            {
              text: this.$t('取消加急'),
              id: 'CancleExpedited',
              prefixIcon: 'icon_solid_Activateorder'
            }
            // {
            //   text: this.$t("查看图纸"),
            //   id: "ViewDrawings",
            //   prefixIcon: "icon_solid_Activateorder",
            // },
          ]
        }
        if (this.entrySource === '0' || this.entrySource === '2') {
          // 采购申请新建
          this.toolbarOptions = [
            // "Delete",
            {
              text: this.$t('下载'),
              prefixIcon: 'e-expand',
              id: 'download',
              fn: this.handleDownload
            },
            {
              prefixIcon: 'icon_solid_Activateorder',
              id: 'Expedited',
              text: this.$t('加急')
            },
            {
              text: this.$t('取消加急'),
              id: 'CancleExpedited',
              prefixIcon: 'icon_solid_Activateorder'
            }
            // {
            //   text: this.$t("查看图纸"),
            //   id: "ViewDrawings",
            //   prefixIcon: "icon_solid_Activateorder",
            // },
          ]
        }
        this.allowPaging = false
      }
      if (this.entryType === '2') {
        //新建编辑 采购申请新建
        if (
          (this.entrySource === '1' || this.entrySource === '0' || this.entrySource === '2') &&
          this.entryDraft !== '1'
        ) {
          this.toolbarOptions = [
            {
              text: this.$t('下载'),
              prefixIcon: 'e-expand',
              id: 'download',
              fn: this.handleDownload
            },
            {
              prefixIcon: 'icon_solid_Activateorder',
              id: 'Close',
              text: this.$t('关闭')
            },
            {
              prefixIcon: 'icon_solid_Activateorder',
              id: 'Expedited',
              text: this.$t('加急')
            },
            {
              text: this.$t('取消加急'),
              id: 'CancleExpedited',
              prefixIcon: 'icon_solid_Activateorder'
            }
            // {
            //   text: this.$t("查看图纸"),
            //   id: "ViewDrawings",
            //   prefixIcon: "icon_solid_Activateorder",
            // },
          ]
        }
        if (this.entryDraft === '1') {
          this.toolbarOptions = [
            {
              text: this.$t('新增'),
              id: 'addinlinebtn',
              prefixIcon: 'e-add',
              fn: this.handleAdd
            },
            {
              text: this.$t('批量编辑'),
              id: 'batchEdit',
              prefixIcon: 'e-edit',
              fn: this.handleBatchEdit
            },
            'Delete',
            {
              text: this.$t('上传'),
              prefixIcon: 'e-upload-1',
              id: 'upload',
              fn: this.handleUpload
            },
            {
              text: this.$t('下载'),
              prefixIcon: 'e-expand',
              id: 'download',
              fn: this.handleDownload
            },
            {
              prefixIcon: 'icon_solid_Activateorder',
              id: 'Expedited',
              text: this.$t('加急')
            },
            {
              text: this.$t('取消加急'),
              id: 'CancleExpedited',
              prefixIcon: 'icon_solid_Activateorder'
            }
            // {
            //   text: this.$t("查看图纸"),
            //   id: "ViewDrawings",
            //   prefixIcon: "icon_solid_Activateorder",
            // },
          ]
        }
        if (this.entrySource === '4') {
          //和从合同进入编辑
          this.toolbarOptions = [
            // {
            //   text: this.$t("添加"),
            //   id: "addinlinebtn1",
            //   prefixIcon: "e-add",
            //   fn: this.handleAdd1,
            // },
            {
              prefixIcon: 'icon_solid_Activateorder',
              id: 'Close',
              text: this.$t('关闭')
            },
            {
              text: this.$t('下载'),
              prefixIcon: 'e-expand',
              id: 'download',
              fn: this.handleDownload
            },
            {
              prefixIcon: 'icon_solid_Activateorder',
              id: 'Expedited',
              text: this.$t('加急')
            },
            {
              text: this.$t('取消加急'),
              id: 'CancleExpedited',
              prefixIcon: 'icon_solid_Activateorder'
            }
            // {
            //   text: this.$t("查看图纸"),
            //   id: "ViewDrawings",
            //   prefixIcon: "icon_solid_Activateorder",
            // },
          ]
        }
        this.allowPaging = false
      }
      if (this.entryType === '3') {
        this.editSettings = {
          allowEditing: false
        }
        this.allowPaging = true
        this.toolbarOptions = [
          {
            text: this.$t('下载'),
            prefixIcon: 'e-expand',
            cssClass: 'invite-btn',
            id: 'download',
            fn: this.handleDownload
          },
          // {
          //   prefixIcon: "icon_solid_Activateorder",
          //   id: "Close",
          //   text: this.$t("关闭"),
          // },
          {
            prefixIcon: 'icon_solid_Activateorder',
            id: 'Expedited',
            text: this.$t('加急')
          },
          {
            text: this.$t('取消加急'),
            id: 'CancleExpedited',
            prefixIcon: 'icon_solid_Activateorder'
          },
          {
            text: this.$t('查看图纸'),
            id: 'ViewDrawings',
            prefixIcon: 'icon_solid_Activateorder'
          },
          {
            text: this.$t('印刷件份数查询'),
            id: 'search',
            prefixIcon: 'icon_solid_Activateorder'
          }
        ]
      }
    },
    // 根据fieldCode，设置下拉选中值。。(列表上会清空部分code、name，这里不重复修改)
    // 注意还要清空，修改了物料/SKU后，工厂、采购组、基本单位、采购单位要清空
    // 地点/工厂 能带出 品类、采购组、基本单位、采购单位
    //     品项  itemId itemCode  itemName  （弹框下拉）
    //     sku  skuId    skuCode  skuName （弹框下拉）
    //     品类  categoryId    categoryCode    categoryName  （带出）
    //     采购组 buyerOrgId  buyerOrgCode  buyerOrgName  （带出）
    //     币种 currencyCode  currencyName  （独立选择）
    //     基本单位  unitId     unitCode    unitName  （带出）
    //     订单单位  orderUnitId    orderUnitCode    orderUnitName  == 采购单位  （带出）
    //     工厂/地址  siteId   siteCode    siteName （弹框下拉）
    //     贸易条款   tradeClauseId    tradeClauseCode    tradeClauseName  （独立选择）
    handleSetSelectedInfo(params) {
      let { fieldCode, itemInfo } = params
      console.log('哈哈', fieldCode, itemInfo)
      let _nowRowSelectedInfo = {}, // 如果已有这一行数据
        _flag = this.nowEditRowFlag // 如果已有这一行数据
      if (Object.prototype.hasOwnProperty.call(this.rowsSelectedInfo, `row-${_flag}`)) {
        _nowRowSelectedInfo = this.rowsSelectedInfo[`row-${_flag}`]
      }

      if (fieldCode == 'itemCode') {
        //品项
        _nowRowSelectedInfo.itemId = itemInfo.itemId
        _nowRowSelectedInfo.skuId = null
        _nowRowSelectedInfo.siteId = null
        _nowRowSelectedInfo.siteCode = null // 清空sku 品类,工厂、采购组、基本单位、采购单位、质量免检标识要清空
        _nowRowSelectedInfo.buyerOrgId = null
        _nowRowSelectedInfo.buyerOrgCode = null
        _nowRowSelectedInfo.unitId = null
        _nowRowSelectedInfo.unitCode = null
        _nowRowSelectedInfo.orderUnitId = null
        _nowRowSelectedInfo.orderUnitCode = null
        _nowRowSelectedInfo.categoryId = null
        _nowRowSelectedInfo.categoryCode = null
      } else if (fieldCode == 'skuCode') {
        _nowRowSelectedInfo.skuId = itemInfo.skuId
        _nowRowSelectedInfo.itemId = itemInfo.itemId
        _nowRowSelectedInfo.siteId = null // 清空品类,工厂、采购组、基本单位、采购单位、质量免检标识要清空
        _nowRowSelectedInfo.siteCode = null
        _nowRowSelectedInfo.buyerOrgId = null
        _nowRowSelectedInfo.buyerOrgCode = null
        _nowRowSelectedInfo.unitId = null
        _nowRowSelectedInfo.unitCode = null
        _nowRowSelectedInfo.orderUnitId = null
        _nowRowSelectedInfo.orderUnitCode = null
        _nowRowSelectedInfo.categoryId = null
        _nowRowSelectedInfo.categoryCode = null
      } else if (fieldCode == 'siteName') {
        _nowRowSelectedInfo.siteId = itemInfo.siteId
        _nowRowSelectedInfo.siteCode = itemInfo.siteCode
        _nowRowSelectedInfo.categoryId = itemInfo?.categoryId
        _nowRowSelectedInfo.categoryCode = itemInfo?.categoryCode
        _nowRowSelectedInfo.buyerOrgId = itemInfo?.buyerOrgId
        _nowRowSelectedInfo.buyerOrgCode = itemInfo?.buyerOrgCode
        _nowRowSelectedInfo.unitId = itemInfo?.unitId
        _nowRowSelectedInfo.unitName = itemInfo?.unitName
        _nowRowSelectedInfo.unitCode = itemInfo?.unitCode
        _nowRowSelectedInfo.orderUnitId = itemInfo?.orderUnitId
        _nowRowSelectedInfo.orderUnitCode = itemInfo?.orderUnitCode
        _nowRowSelectedInfo.requestOrderMethod = itemInfo?.requestOrderMethod
      } else if (fieldCode == 'currencyName') {
        //币种
        _nowRowSelectedInfo.currencyCode = itemInfo.itemCode
        _nowRowSelectedInfo.currencyId = itemInfo.id
        _nowRowSelectedInfo.currencyName = itemInfo.currencyName
      } else if (fieldCode == 'tradeClauseName') {
        //贸易条款
        _nowRowSelectedInfo.tradeClauseName = itemInfo.itemName
        _nowRowSelectedInfo.tradeClauseId = itemInfo.id
        _nowRowSelectedInfo.tradeClauseCode = itemInfo.itemCode
      } else if (fieldCode == 'deliveryStatus') {
        //发货状态
        _nowRowSelectedInfo.deliveryStatus = itemInfo.label
        _nowRowSelectedInfo.deliveryStatusValue = itemInfo.value
      } else if (fieldCode == 'consignee') {
        //收货人
        _nowRowSelectedInfo.consignee = itemInfo.employeeName
        _nowRowSelectedInfo.consigneeId = itemInfo.userId
        _nowRowSelectedInfo.addId = itemInfo.addId
        this.$bus.$emit('contactChange', itemInfo.phoneNum)
      } else if (fieldCode == 'shippingMethodName') {
        //物流方式
        _nowRowSelectedInfo.shippingMethodName = itemInfo.itemName
        _nowRowSelectedInfo.shippingMethodCode = itemInfo.itemCode
      } else if (fieldCode == 'warehouseStatus') {
        //入库状态
        _nowRowSelectedInfo.warehouseStatus = itemInfo.label
        _nowRowSelectedInfo.warehouseStatusValue = itemInfo.value
      } else if (fieldCode == 'confirmStatus') {
        //供应商确认状态
        _nowRowSelectedInfo.confirmStatus = itemInfo.label
        _nowRowSelectedInfo.confirmStatusValue = itemInfo.value
      } else if (fieldCode == 'contractType') {
        //合同协议类型
        _nowRowSelectedInfo.contractType = itemInfo.label
        _nowRowSelectedInfo.contractTypeValue = itemInfo.value
      } else if (fieldCode === 'profitCenterName') {
        //利润中心
        _nowRowSelectedInfo.profitCenterId = itemInfo.profitCenterId
        _nowRowSelectedInfo.profitCenterCode = itemInfo.profitCenterCode
        _nowRowSelectedInfo.profitCenterName1 = itemInfo.profitCenterName
      } else if (fieldCode === 'orderCosts') {
        //成本中心
        _nowRowSelectedInfo.costId = itemInfo.costCenterId
        _nowRowSelectedInfo.costCenterId = itemInfo.costCenterId
        _nowRowSelectedInfo.costCenterName = itemInfo.costCenterName
        _nowRowSelectedInfo.orderCosts = itemInfo.costCenterCode
        _nowRowSelectedInfo.costCenterCode = itemInfo.costCenterCode
      } else if (fieldCode === 'contractRel') {
        //关联合同协议编号
        _nowRowSelectedInfo.contractRelId = itemInfo.contractRelId
        _nowRowSelectedInfo.contractRelCode = itemInfo.contractRelCode
      } else if (fieldCode === 'categoryName') {
        //品类 ,当是下拉的时候传过来的
        _nowRowSelectedInfo.categoryName = itemInfo.categoryName
        _nowRowSelectedInfo.categoryCode = itemInfo.categoryCode
        _nowRowSelectedInfo.categoryId = itemInfo.categoryId
      } else if (fieldCode === 'unitName') {
        //基本单位 ,下拉传过来
        _nowRowSelectedInfo.unitCode = itemInfo.unitCode
        _nowRowSelectedInfo.unitId = itemInfo.unitId
        _nowRowSelectedInfo.unitName = itemInfo.unitName
      } else if (fieldCode === 'purUnitName') {
        //采购单位 ,下拉传过来
        _nowRowSelectedInfo.purUnitNameCode = itemInfo.orderUnitCode
        _nowRowSelectedInfo.orderUnitId = itemInfo.orderUnitId
        _nowRowSelectedInfo.purUnitName = itemInfo.orderUnitName
      } else if (fieldCode === 'qualityExemptionMarkName') {
        _nowRowSelectedInfo.qualityExemptionMarkId = itemInfo.qualityExemptionMarkName
        _nowRowSelectedInfo.qualityExemptionMarkName =
          itemInfo.qualityExemptionMarkName === '1' ? this.$t('免检') : this.$t('需检验')
      } else if (fieldCode === 'purchaseStrategy') {
        //采购策略
        _nowRowSelectedInfo.purchaseStrategy = itemInfo.label
        _nowRowSelectedInfo.purchaseStrategyValue = itemInfo.value
      } else if (fieldCode === 'subjectType') {
        // 科目类型
        _nowRowSelectedInfo.subjectType = itemInfo.label
        _nowRowSelectedInfo.subjectTypeValue = itemInfo.value
      } else if (fieldCode === 'provisionalEstimateStatus') {
        //是否暂估价
        _nowRowSelectedInfo.provisionalEstimateStatus = itemInfo.label
        _nowRowSelectedInfo.provisionalEstimateStatusValue = itemInfo.value
      } else if (fieldCode === 'taxCode') {
        //税率code从价格协议带出来
        _nowRowSelectedInfo.taxCode = itemInfo.taxCode
      } else if (fieldCode === 'returnIdentification') {
        //退货标识
        _nowRowSelectedInfo.returnIdentification = itemInfo.label
        _nowRowSelectedInfo.returnIdentificationValue = itemInfo.value
      } else if (fieldCode === 'warehouseCode') {
        _nowRowSelectedInfo.warehouseCode = itemInfo.warehouseCode
        _nowRowSelectedInfo.warehouse = itemInfo.warehouseName
      } else if (fieldCode === 'domesticDemandFlag') {
        //是否内需跟单
        _nowRowSelectedInfo.domesticDemandFlag = itemInfo.label
        _nowRowSelectedInfo.domesticDemandFlagValue = itemInfo.value
      }
      this.rowsSelectedInfo[`row-${_flag}`] = { ..._nowRowSelectedInfo }
    },
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    },
    seeFigure() {},
    // 获取下拉数据
    async getDropDownData() {
      // 获取地点/工厂
      // await this.$API.masterData.getSite().then((res) => {
      //   this.siteNameData = res.data;
      // });
      // 获取税率
      // await this.$API.masterData.getTaxItem().then((res) => {
      //   this.taxidData = res.data.map(
      //     (item) => (item.taxRate = item.taxRate * 100)
      //   );
      // });
      // 获取币种
      // await this.$API.masterData.getCurrency().then((res) => {
      //   this.currencyNameData = res.data;
      // });
      // 贸易条款
      await this.$API.masterData.getDictCode({ dictCode: 'TradeClause' }).then((res) => {
        this.tradeClauseNameData = res.data || []
      })
      // 物流方式
      await this.$API.masterData.getDictCode({ dictCode: 'TransportMode' }).then((res) => {
        this.shippingMethodNameData = res.data || []
      })
      // 收货人
      // await this.$API.masterData.getEmploee().then((res) => {
      //   this.receiveUserIdData = res.data;
      // });
      // 推荐供应商
      // await this.$API.masterData.getSupplier().then((res) => {
      //   this.supplierIdData = res.data;
      // });
      // 利润中心
      // await this.$API.masterData.postProfitCenterCriteriaQuery().then((res) => {
      //   this.profitCenterNameData = res.data || [];
      // });
      //库存地点
      // await this.$API.masterData.getLocation().then((res) => {
      //   this.locationData = res.data || [];
      // });
      this.editColumns = editColumn({
        that: this,
        taxidData: this.taxidData, //税率
        currencyNameData: this.currencyNameData, //币种
        tradeClauseNameData: this.tradeClauseNameData, //贸易条款
        shippingMethodNameData: this.shippingMethodNameData, //物流方式
        receiveUserIdData: this.receiveUserIdData, //收货人
        supplierIdData: this.supplierIdData, //推荐供应商
        profitCenterNameData: this.profitCenterNameData, //利润中心
        locationData: this.locationData,
        entrySource: this.entrySource
      })
    },
    // 获取订单类型下拉
    getUnMobileCompany() {
      this.$API.masterData.getDictCode({ dictCode: 'UN_MOBILE_COMPANY' }).then((res) => {
        this.unMobileCompany.length = 0
        res.data.forEach((item) => {
          this.unMobileCompany.push(item)
        })
      })
    },
    // 组合列
    handleUnionColumns() {
      let _columnData = cloneDeep(editColumnBefore)
      if (this.entrySource === '0' || this.entrySource === '4') {
        _columnData = _columnData.concat(editColumn1)
      }
      // if (this.entryType === "3") {
      //   _columnData = _columnData.concat(editColumn2);
      // }
      if (this.fields && this.fields.length) {
        let fields = cloneDeep(this.fields)
        _columnData = _columnData.concat(editColumnEnd)
        _columnData = _columnData.concat(
          fields.map((item1) => {
            item1.allowResizing = true
            if (item1.required) {
              if (!['itemCode'].includes(item1.fieldCode)) {
                // _one.validationRules = { required: true }; // 用自带的校验会报错，因为要自动跳转到下一个，但校验报错，导致focus不了
              }
              item1.headerTemplate = () => {
                return {
                  template: Vue.component('headers', {
                    template: `
                        <div class="headers">
                          <span style="color: red">*</span>
                          <span class="e-headertext">{{fieldName}}</span>
                        </div>
                      `,
                    data() {
                      return {
                        data: {},
                        fieldName: ''
                      }
                    },
                    mounted() {
                      this.fieldName = item1.fieldName
                    }
                  })
                }
              }
            }
            if (this.entryType === '2' && this.entryDraft !== '1' && this.entrySource === '1') {
              //正常编辑进入可以修改的列
              let canEditFields = [
                'requiredDeliveryDate', //要求交期
                'tradeClauseName', //贸易条款
                'shippingMethodName', //物流方式
                'consignee', //收货人
                'contact', //联系方式
                'receiveAddress', //收货地址
                'packageMethod', //包装方式
                'packageSpec', //包装规格
                'packageDesc', //包装说明
                'quantity', //订单数量
                'warehouseCode',
                'warehouse'
                // "budgetUnitPrice", //预算单价（未税）
                // "taxedUnitPrice", //预算单价（含税）
                // "taxid", //税率
                // "freePrice", //未税单价
                // "taxPrice", //含税单价
              ]
              if (canEditFields.indexOf(item1.fieldCode) > -1) {
                item1.allowEditing = true
              } else {
                item1.allowEditing = false
              }
            }
            if (this.entrySource === '0' || this.entrySource === '4') {
              //采购申请进入改变可以修改的列
              let canEditFields = [
                'deliveryDateCycle', // 交货周期
                'cycleType', // 周期类型
                'requiredDeliveryDate', //要求交期
                'tradeClauseName', //贸易条款
                'shippingMethodName', //物流方式
                'consignee', //收货人
                'contact', //联系方式
                'receiveAddress', //收货地址
                'packageMethod', //包装方式
                'packageSpec', //包装规格
                'packageDesc', //包装说明
                'quantity', //订单数量
                'freePrice', //关联合同协议编号
                'checkRequest', //检验要求
                'profitCenterName', //利润中心
                'postingAccountName', //总账科目
                'contractType', //合同协议类型
                'purchaseStrategy', //采购策略
                'subjectType', //科目类型
                'projectName', //项目名称
                'projectCode', //项目编号
                'projectTextBatch', //项目文本批次
                'projectRowText', //行项目文本
                'artParam', //美工变量
                'changeRemark', //变更备注
                'warehouseCode'
              ]
              if (canEditFields.indexOf(item1.fieldCode) > -1) {
                item1.allowEditing = true
              } else {
                item1.allowEditing = false
              }
            }
            if (this.entrySource === '2') {
              //商城进入改变不可以修改的列
              let canNotEditFields = [
                'skuCode',
                'skuName',
                'itemCode',
                'itemName',
                'contact',
                'receiveAddress',
                'consignee'
              ]
              if (canNotEditFields.indexOf(item1.fieldCode) > -1) {
                item1.allowEditing = false
              } else {
                item1.allowEditing = true
              }
            }
            let obj = {}
            let hasItem = false
            this.editColumns.some((item2) => {
              if (item2.field === item1.fieldCode) {
                obj = {
                  ...item1,
                  hasItem: true,
                  ...item2,
                  width: item2.width || '150',
                  headerText: item1.fieldName
                }
                if (item1.required) {
                  obj.headerTemplate = item1.headerTemplate
                }
                hasItem = true
              }
            })
            if (hasItem) {
              return obj
            } else {
              obj = {
                ...item1,
                hasItem: false,
                headerText: item1.fieldName,
                field: item1.fieldCode,
                required: item1.required,
                width: '150'
              }
              if (item1.required) {
                obj.headerTemplate = item1.headerTemplate
              }
              return obj
            }
          })
        )
        // _columnData = _columnData.concat(lastColumn(this.supplierIdData));
        _columnData = _columnData.concat(lastColumn)
        this.columnData = _columnData
      }
      console.log('handleUnionColumns', this.columnData)
    },
    getToday() {
      var today = new Date()
      today.setHours(0)
      today.setMinutes(0)
      today.setSeconds(0)
      today.setMilliseconds(0)
      today = new Date(today.getTime() + 24 * 60 * 60 * 1000)
      return today
    },
    actionBegin(args) {
      console.log('actionBegin', args)
      if (args.requestType === 'add') {
        // 初始化数据
        this.columnData.forEach((item) => {
          if (item.editType) {
            if (item.editType.includes('dropdown')) {
              args.data[item.field] = ''
            } else if (item.editType.includes('date')) {
              args.data[item.field] = this.getToday()
            } else if (item.editType.includes('numer')) {
              args.data[item.field] = 0
            } else if (item.editType.includes('boolean')) {
              args.data[item.field] = true
            } else {
              args.data[item.field] = ''
            }
          } else {
            args.data[item.field] = ''
          }
          if (item.field === 'requiredDeliveryDate') {
            args.data.requiredDeliveryDate = this.getToday()
          }
          if (item.field === 'deliveryStatus') {
            args.data.deliveryStatusValue = 0
            args.data.deliveryStatus = this.$t('未发货')
          }
          if (item.field === 'confirmStatus') {
            args.data.confirmStatusValue = 0
            args.data.confirmStatus = this.$t('待确认')
          }
          if (item.field === 'warehouseStatus') {
            args.data.warehouseStatusValue = 0
            args.data.warehouseStatus = this.$t('未入库')
          }
          if (item.field === 'itemNo') {
            args.data.itemNo = 0
          }
          if (item.field === 'transitQty') {
            args.data.transitQty = 0
          }
          if (item.field === 'purchaseStrategy') {
            args.data.purchaseStrategy = this.$t('标准')
            args.data.purchaseStrategyValue = '1'
          }
          if (item.field === 'subjectType') {
            args.data.subjectType = this.$t('成本中心')
            args.data.subjectTypeValue = '0'
          }
          if (item.field === 'subjectTotal') {
            args.data.subjectTotal = ''
          }
          if (item.field === 'approvedTotalPrice') {
            args.data.approvedTotalPrice = ''
          }
          if (item.field == 'urgentStatus') {
            args.data.urgentStatus = '普通'
          }
          if (item.field == 'closeStatus') {
            args.data.closeStatus = '0'
          }
        })
        args.data.addId = String(this.selfAddId++) // 新增时是addId，后台获取过来的数据是id
        this.nowEditRowFlag = args.data.addId // 记录当前编辑行的唯一标识
      }
      if (args.action === 'add' && args.requestType === 'save') {
        const length = this.$refs.dataGrid.ejsRef.getCurrentViewRecords()?.length
        args.index = length
      }
      // 记录当前编辑行的唯一标识
      if (args.requestType == 'beginEdit') {
        if (args.rowData.closeStatus == 1) {
          this.$toast({
            content: this.$t('关闭的订单明细不可修改'),
            type: 'warning'
          })
          args.cancel = true
        }
        this.nowEditRowFlag = args.rowData.addId
        if (!this.rowsSelectedInfo[`row-${args.rowData.addId}`]) {
          this.rowsSelectedInfo[`row-${args.rowData.addId}`] = {
            consignee: args.rowData.consignee,
            consigneeId: args.rowData.consigneeId
          }
        }
      }
    },
    endEdit() {
      //让组件失去焦点
      this.$refs.dataGrid.ejsRef.endEdit()
    },
    actionComplete(args) {
      console.log('actionComplete', args)
      if (args.action == 'add' || args.action == 'edit') {
        this.nowEditRowFlag = ''
      }
      if (args.requestType === 'beginEdit' || args.requestType === 'add') {
        this.isEdit = '1'
      }
      if (
        args.requestType === 'refresh' ||
        args.requestType === 'save' ||
        args.requestType === 'delete'
      ) {
        this.$refs.dataGrid.ejsRef.clearSelection()
        this.isEdit = '2'
        this.updateGrid()
        if (args.action == 'edit') {
          const dataSource = this.dataSource.map((i) => {
            const obj = i
            if (this.rowsSelectedInfo[`row-${obj.addId}`]) {
              obj.consignee = this.rowsSelectedInfo[`row-${obj.addId}`].consignee
              obj.consigneeId = this.rowsSelectedInfo[`row-${obj.addId}`].consigneeId
            }
            return obj
          })
          this.dataSource = dataSource
        }
      }
    },
    updateCompanyLinkData() {
      //更新订单明细和公司关联的数据
      if (this.isEdit === '1') {
        this.endEdit()
        setTimeout(() => {
          this.updateCompanyLinkDataSource()
        }, 500)
      }
      if (this.isEdit === '2') {
        this.updateCompanyLinkDataSource()
      }
    },
    //更新和公司关联的数据
    updateCompanyLinkDataSource() {
      let dataSource = cloneDeep(this.$refs.dataGrid.ejsRef.getCurrentViewRecords())
      var that = this
      dataSource.forEach((item) => {
        item.siteName = null //工厂
        item.siteId = null //工厂
        item.siteCode = null //工厂
        item.categoryName = null //品类
        item.categoryCode = null //品类
        item.categoryId = null //品类
        item.buyerOrgCode = null //采购组
        item.buyerOrgId = null //采购组
        item.buyerOrgName = null //采购组
        item.unitId = null //基本单位
        item.unitName = null //基本单位
        item.orderUnitId = null //采购单位
        item.purUnitName = null //采购单位
        item.qualityExemptionMarkName = null //质量免检标识
        item.taxid = null //税率
        item.contractRel = null //关联合同协议编号
        item.taxPrice = null //含税单价
        item.taxTotal = null //含税总价
        item.priceUnit = null
        item.freePrice = null //未税单价
        item.freeTotal = null //未税总价
        if (this.entrySource === '1') {
          item.costCenterId = '' //成本中心
          item.orderCosts = '' //成本中心
          item.profitCenterName = '' //利润中心
          item.profitCenterCode = '' //利润中心
        }
        this.nowEditRowFlag = item.addId
        that.handleSetSelectedInfo({
          fieldCode: 'siteName',
          itemInfo: {
            siteId: null,
            siteCode: null,
            categoryId: null,
            categoryCode: null,
            buyerOrgId: null,
            buyerOrgCode: null,
            unitId: null,
            unitName: null,
            unitCode: null,
            orderUnitId: null,
            orderUnitCode: null
          }
        })
      })
      this.dataSource = dataSource
    },
    updatePurtenantLinkData(val) {
      //更新订单明细和供应商关联的数据
      if (this.isEdit === '1') {
        this.endEdit()
        setTimeout(() => {
          this.updatePurtenantLinkDataSource(val)
        }, 500)
      }
      if (this.isEdit === '2') {
        this.updatePurtenantLinkDataSource(val)
      }
    },
    //更新和供应商关联的数据
    updatePurtenantLinkDataSource(val) {
      let dataSource = cloneDeep(this.$refs.dataGrid.ejsRef.getCurrentViewRecords())
      dataSource.forEach((item) => {
        item.taxid = null //税率
        item.contractRel = null //关联合同协议编号
        item.taxPrice = null //含税单价
        item.taxTotal = null //含税总价
        item.priceUnit = null
        item.freePrice = null //未税单价
        item.freeTotal = null //未税总价
      })
      if (this.entrySource === '0' && this.entryType === '1') {
        this.getPriceList(dataSource, val)
        return
      }
      //手工新建
      this.dataSource = dataSource
    },
    getPriceList(dataSource, val) {
      let supplierCode = val
      //查询价格记录 bizCode 在申请新增或者合同新增 或者申请编辑 或者合同编辑进入的时候,如果没有物料信息 才会传
      let params = []
      dataSource.forEach((item) => {
        item._flag = true
        let bizCode = item.requestCode + '_' + item.itemNo1
        let obj = {
          bizCode: '',
          itemCode: item.itemCode || '',
          siteCode: item.siteCode,
          supplierCode: supplierCode,
          skuCode: item.skuCode || '',
          quantity: item.quantity,
          uniqueKey: item.uniqueKey
        }
        if (bizCode) {
          if (!obj.itemCode) {
            obj.bizCode = bizCode
          }
        }
        if (!obj.siteCode) {
          item._flag = false
          console.log('工厂code没有阿')
          return
        }
        if (!obj.supplierCode) {
          item._flag = false
          console.log('供应商code没有阿')
          return
        }
        if (item._flag) {
          params.push(obj)
        }
      })
      let params1 = {
        orderItemList: params
      }
      this.$API.purchaseOrder.queryAndCompute(params1).then((res) => {
        let orderItemList = res.data?.orderItemList
        dataSource.forEach((item1) => {
          orderItemList.some((item2) => {
            if (item1.uniqueKey == item2.uniqueKey) {
              if (item2.computeStatus == 1 && item2.matchPriceRecordCount >= 1) {
                item1.contractRel = item2.priceRecordCode //价格协议编号1
                item1.taxCode = item2.taxCode //税率code 1
                item1.taxid = bigDecimal.multiply(item2.taxRate, 100) //税率 1
                item1.taxPrice = item2.taxPrice //含税单价 1
                item1.priceUnit = item2.priceUnit //价格单位 1
                item1.freePrice = item2.freePrice //未税单价 1
                item1.taxTotal = bigDecimal.round(item2.taxTotal, 2) //含税总价 1
                item1.freeTotal = bigDecimal.round(item2.freeTotal, 2) //未税总价 1
                return
              }
            }
          })
        })
        this.dataSource = dataSource
      })
    },
    toolbarClick(args) {
      // 自定义了一个行内新增按钮，为了给新增的这一行赋默认值
      if (args.item.id == 'addinlinebtn') {
        this.$emit('checkTopInfo')
        setTimeout(() => {
          if (this.topInfoIsSaved === '1') {
            if (this.isEdit === '1') {
              this.endEdit()
              setTimeout(() => {
                this.handleAdd()
              }, 300)
            } else {
              this.handleAdd()
            }
          }
        }, 500)
      } else if (args.item.id == 'batchEdit') {
        this.handleBatchEdit()
      } else if (args.item.id === 'addinlinebtn1') {
        this.handleAdd1()
      } else if (args.item.id === 'upload') {
        this.handleUpload()
      } else if (args.item.id === 'download') {
        this.purOrderDownload()
      } else if (args.item.id === 'Expedited') {
        this.handleExpedited(args)
      } else if (args.item.id === 'CancleExpedited') {
        this.handleCancleExpedited(args)
      } else if (args.item.id === 'Close') {
        this.handleClose(args)
      } else if (args.item.id === 'ViewDrawings') {
        this.handleView()
      } else if (args.item.id === 'search') {
        this.handleShow()
      }
    },
    // // 查看图纸
    // handleView() {
    //   console.log("打开图纸");
    // },
    handleShow() {
      let selectRecords = this.$refs.dataGrid.ejsRef.getSelectedRecords()
      if (!selectRecords.length) {
        this.$toast({ content: this.$t('请至少选择一行'), type: 'warning' })
        return
      }
      if (selectRecords.length > 1) {
        this.$toast({ content: this.$t('只能查询单条数据'), type: 'warning' })
        return
      }
      this.$store.commit('startLoading')
      this.$API.drawingTogether
        .getItemParentDosageApi({ itemCode: selectRecords[0].itemCode })
        .then((res) => {
          if (res.code == 200) {
            this.$dialog({
              modal: () => import('./printSeachDialog.vue'),
              data: {
                tableData: res.data
              },
              success: () => {}
            })
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    handleClose(args) {
      //关闭订单明细
      let selectedrecords = this.$refs.dataGrid.ejsRef.getSelectedRecords()
      console.log(args, '关闭订单明细', selectedrecords)
      if (!selectedrecords.length) {
        this.$toast({
          content: this.$t('请选择需要关闭的订单明细行'),
          type: 'error'
        })
        return
      }
      let flag = '1'
      selectedrecords.some((item) => {
        if (item.closeStatus == 1) {
          flag = '2'
          return
        }
        if (item.itemNo == 0) {
          flag = '3'
          return
        }
      })
      if (flag === '2') {
        this.$toast({
          content: this.$t('请选择未关闭的订单明细行'),
          type: 'error'
        })
        return
      }
      if (flag === '3') {
        this.$toast({
          content: this.$t('请选择已生成行号的订单明细行'),
          type: 'error'
        })
        return
      }
      let itemNos = selectedrecords.map((item) => item.itemNo)
      this.handleClose1(itemNos, selectedrecords)
    },
    handleClose1(itemNos, selectedrecords) {
      let params = {
        itemNos: itemNos,
        orderCode: this.topInfo?.orderCode,
        orderId: this.entryId
      }
      this.$API.purchaseOrder.poDetailClose(params).then(() => {
        this.$toast({
          content: this.$t('关闭订单明细操作成功'),
          type: 'success'
        })
        let dataSource = cloneDeep(this.$refs.dataGrid.ejsRef.getCurrentViewRecords())
        selectedrecords.forEach((item) => {
          dataSource.some((item1) => {
            if (item.addId === item1.addId) {
              item1.closeStatus = 1
              return
            }
          })
        })
        this.dataSource = dataSource
      })
    },
    async handleCancleExpedited(args) {
      //取消加急
      let selectedrecords = this.$refs.dataGrid.ejsRef.getSelectedRecords()
      console.log(args, '取消加急', selectedrecords)
      if (!selectedrecords.length) {
        this.$toast({
          content: this.$t('请选择需要取消加急的订单明细行'),
          type: 'error'
        })
        return
      }
      let flag = '1'
      selectedrecords.some((item) => {
        if (!item.urgentTime) {
          flag = '2'
          return
        }
      })
      if (flag === '2') {
        this.$toast({
          content: this.$t('请选择已经加急的订单明细行'),
          type: 'error'
        })
        return
      }
      let ids = selectedrecords.map((item) => item.id)
      if (this.entryType === '2') {
        if (this.entryDraft === '1') {
          await this.setLinkurgentTime(selectedrecords, '2')
          setTimeout(() => {
            this.updateTopInfoUrgentime1('1')
            this.$toast({
              content: this.$t('取消加急订单明细操作成功'),
              type: 'success'
            })
          }, 1000)
        }
        if (
          (this.entryDraft === '2' && this.entrySource === '1') ||
          this.entrySource === '0' ||
          this.entrySource === '2' ||
          this.entrySource === '4'
        ) {
          await this.handleCancleExpedited1(ids, selectedrecords)
          setTimeout(() => {
            this.updateTopInfoUrgentime1('2')
          }, 500)
        }
      }
      if (this.entryType === '3') {
        await this.handleCancleExpedited1(ids, selectedrecords)
        setTimeout(() => {
          this.updateTopInfoUrgentime1('2')
        }, 500)
      }
      if (this.entryType === '1') {
        await this.setLinkurgentTime(selectedrecords, '2')
        setTimeout(() => {
          this.updateTopInfoUrgentime1('1')
        }, 500)
        this.$toast({
          content: this.$t('取消加急订单明细操作成功'),
          type: 'success'
        })
      }
    },
    async handleCancleExpedited1(ids, selectedrecords) {
      //取消加急订单明细
      // let params = {
      //   ids: ids,
      //   orderId: this.entryId,
      // };
      let params = {
        type: 0,
        urgentList: [
          {
            ids: ids,
            itemNos: [],
            orderCode: this.topInfo?.orderCode
          }
        ]
      }
      selectedrecords.forEach((item) => {
        params.urgentList[0].itemNos.push(item.itemNo)
      })
      await this.$API.purchaseOrder.purOrderUrgentOrder(params).then((res) => {
        if (res.data.length) {
          let str = ''
          res.data.forEach((item) => {
            str = str + `${item.code} ${item.msg}`
          })
          this.$toast({ content: str, type: 'error' })
        } else {
          this.setLinkurgentTime(selectedrecords, '2')
          this.$toast({
            content: this.$t('取消加急订单明细操作成功'),
            type: 'success'
          })
        }
      })
    },
    async handleExpedited(args) {
      //加急
      let selectedrecords = this.$refs.dataGrid.ejsRef.getSelectedRecords()
      console.log(args, '加急', selectedrecords)
      let ids = selectedrecords.map((item) => item.id)
      if (!selectedrecords.length) {
        this.$toast({
          content: this.$t('请选择需要加急的订单明细行'),
          type: 'error'
        })
        return
      }
      let flag = '1'
      selectedrecords.some((item) => {
        if (item.urgentTime) {
          flag = '2'
          return
        }
      })
      if (flag === '2') {
        this.$toast({
          content: this.$t('请选择普通的订单明细行'),
          type: 'error'
        })
        return
      }
      if (this.entryType === '2') {
        if (this.entryDraft === '1') {
          await this.setLinkurgentTime(selectedrecords, '1')
          this.updateTopInfoUrgentime('1')
          this.$toast({
            content: this.$t('加急订单明细操作成功'),
            type: 'success'
          })
        }
        if (
          (this.entryDraft === '2' && this.entrySource === '1') ||
          this.entrySource === '0' ||
          this.entrySource === '2' ||
          this.entrySource === '4'
        ) {
          await this.handleExpedited1(ids, selectedrecords)
          this.updateTopInfoUrgentime('1')
        }
      }
      if (this.entryType === '3') {
        await this.handleExpedited1(ids, selectedrecords)
        this.updateTopInfoUrgentime('2')
      }
      if (this.entryType === '1') {
        await this.setLinkurgentTime(selectedrecords, '1')
        this.updateTopInfoUrgentime('1')
        this.$toast({
          content: this.$t('加急订单明细操作成功'),
          type: 'success'
        })
      }
    },
    updateTopInfoUrgentime1(type) {
      if (type === '1') {
        setTimeout(() => {
          let dataSource = cloneDeep(this.$refs.dataGrid.ejsRef.getCurrentViewRecords())
          let hasUrgentTime = false
          dataSource.forEach((item) => {
            if (item.urgentTime) {
              hasUrgentTime = true
            }
          })
          if (!hasUrgentTime) {
            this.$emit('updateTopInfoUrgentime1', type)
          }
        }, 1000)
      }
      if (type === '2') {
        this.$emit('updateTopInfoUrgentime1', type)
      }
    },
    updateTopInfoUrgentime(type) {
      this.$emit('updateTopInfoUrgentime', type)
    },
    async handleExpedited1(ids, selectedrecords) {
      //加急订单明细
      // let params = {
      //   ids: ids,
      //   orderId: this.entryId,
      // };
      let params = {
        type: 1,
        urgentList: [
          {
            ids: ids,
            itemNos: [],
            orderCode: this.topInfo?.orderCode
          }
        ]
      }
      selectedrecords.forEach((item) => {
        params.urgentList[0].itemNos.push(item.itemNo)
      })
      await this.$API.purchaseOrder.purOrderUrgentOrder(params).then((res) => {
        if (res.data.length) {
          let str = ''
          res.data.forEach((item) => {
            str = str + `${item.code} ${item.msg}`
          })
          this.$toast({ content: str, type: 'error' })
        } else {
          this.setLinkurgentTime(selectedrecords, '1')
          this.$toast({
            content: this.$t('加急订单明细操作成功'),
            type: 'success'
          })
        }
      })
    },
    expeditedAllDetail() {
      let dataSource = cloneDeep(this.$refs.dataGrid.ejsRef.getCurrentViewRecords())
      // let nowTime = UTILS.default.formateTime(new Date(), "YYYY/mm/dd");
      let nowTime = this.getToday()
      nowTime = UTILS.default.formateTime(nowTime, 'YYYY/mm/dd')
      dataSource.forEach((item1) => {
        item1.urgentTime = nowTime
        item1.urgentStatus = '加急'
      })
      this.dataSource = dataSource
    },
    cancleExpeditedAllDetail() {
      let dataSource = cloneDeep(this.$refs.dataGrid.ejsRef.getCurrentViewRecords())
      dataSource.forEach((item1) => {
        item1.urgentTime = ''
        item1.urgentStatus = '普通'
      })
      this.dataSource = dataSource
    },
    setLinkurgentTime(selectedrecords, type) {
      //设置表格关联的加急时间 type 1 加急 2取消加急
      let dataSource = cloneDeep(this.$refs.dataGrid.ejsRef.getCurrentViewRecords())
      let nowTime = UTILS.default.formateTime(new Date(), 'YYYY/mm/dd')
      selectedrecords.forEach((item) => {
        dataSource.some((item1) => {
          if (item.addId === item1.addId) {
            if (type === '1') {
              item1.urgentTime = nowTime
              item1.urgentStatus = '加急'
            }
            if (type === '2') {
              item1.urgentTime = ''
              item1.urgentStatus = '普通'
            }
            return
          }
        })
      })
      this.dataSource = dataSource
    },
    // 批量编辑弹窗显示
    handleBatchEdit() {
      this.endEdit()
      setTimeout(() => {
        let selectedRecords = this.$refs.dataGrid.ejsRef.getSelectedRecords()
        if (!selectedRecords || !selectedRecords.length) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.batchRowIndexs = selectedRecords.map((item, index) => index)
        this.batchEditShow = true
      }, 500)
    },
    handleBatchDialogShow(code, flag) {
      this[code] = flag
    },
    // 批量编辑修改,回显到表格上
    confirmBatchSuccess(params) {
      if (Object.keys(params).length === 0) {
        return
      }
      let _dataSource = this.getData()
      for (let i = 0; i < this.batchRowIndexs.length; i++) {
        for (let key in params) {
          _dataSource[this.batchRowIndexs[i]][key] = params[key]
        }
      }
      console.log('回显完后：', _dataSource) // -------------差一个成本中心，传过来的是costSharingAccName 字符串格式的
      this.dataSource = cloneDeep(_dataSource)
    },
    //保存后更新最新的datasoruce
    updateGrid() {
      if (!(this.$refs.dataGrid && this.$refs.dataGrid.ejsRef)) {
        return
      }
      this.$refs.dataGrid.ejsRef.getCurrentViewRecords().forEach((item) => {
        Object.keys(this.rowsSelectedInfo).some((key) => {
          let _flag = item.addId
          if (key == 'row-' + _flag) {
            Object.assign(item, this.rowsSelectedInfo[key])
          }
        })
      })
      console.log('最新的表格数据', this.$refs.dataGrid.ejsRef.getCurrentViewRecords())
      let sendlist1 = cloneDeep(this.$refs.dataGrid.ejsRef.getCurrentViewRecords())
      this.$emit('updateShowDataGrid', sendlist1)
      let sendList = []
      sendlist1.forEach((item) => {
        if (Object.prototype.toString.call(item.file) === '[object String]' && item.file.length) {
          item.file = JSON.parse(item.file)
        }
        let obj = {
          customerName: item.customerName || '', //客户名称
          customerOrder: item.customerOrder, //关联销售订单
          customerOrderLineNo: item.customerOrderLineNo, //关联销售订单行号
          addId: item.addId,
          acceptances: [],
          itemNo: 0,
          assetCard: item.assetCard || '', //	关联资产卡片
          assetCode: item.assetCode || '', //关联资产编号
          assetType: item.assetType || '', //关联资产类别
          abolished: 0, //是否删除 0-未删除；1-已删除
          buyerDepCode: item.purUnitNameCode, //333 采购单位接口无code
          buyerDepId: item.orderUnitId, //采购单位
          buyerDepName: item.purUnitName, //采购单位
          buyerOrgCode: item.buyerOrgCode, //采购组
          buyerOrgId: item.buyerOrgId, //采购组
          buyerOrgName: item.buyerOrgName, //采购组
          categoryCode: item.categoryCode || '', //品类
          categoryId: item.categoryId, //品类
          categoryName: item.categoryName, //品类
          changeRemark: item.changeRemark, //变更备注
          checkRequest: item.checkRequest, //检验要求 0-否；1-是
          confirmStatus: item.confirmStatusValue, //供应商确认状态
          consignee: item.consignee, //收货人
          consigneeId: item.consigneeId || '1', //收货人
          contact: item.contact, //联系方式
          contactId: item.contactId || 1, //333 暂无接口联系方式 无id
          deliveryQty: item.deliveryQty, //已发货数量
          deliveryStatus: item.deliveryStatusValue, //发货状态：0-未发货；1-部分发货；2-全部发货
          fieldDataList: [], //	单据模块扩展字段列表
          freePrice: item.freePrice, //不含税单价
          freeTotal: item.freeTotal, //不含税总价
          itemCode: item.itemCode, //物料品项编码
          itemId: item.itemId, //物料品项id
          itemName: item.itemName, //物料品项名称
          itemGroupCode: item.itemGroupCode, //	品项组编码
          itemGroupName: item.itemGroupName, //	品项组名称
          id: 0,
          mall: {
            mallId: item.mallId || 0, //商城id
            provinceCode: item.provinceCode || '', //	省编码
            shopId: item.shopId || '', //	店铺id
            spuCode: item.spuCode || '' //	spu编码
          }, //商城信息
          orderCosts: [], //订单分摊信息
          orderFiles: [], //订单明细附件
          orderId: this.entryType === '1' ? 0 : this.entryId,
          orderRel: item.orderRel, //关联订单
          otherDataPew: item.otherDataPew, //额外信息
          packageDesc: item.packageDesc, //包装说明
          packageMethod: item.packageMethod, //包装方式
          packageSpec: item.packageSpec, //包装规格
          postingAccount: '', //4444暂无字段入账科目
          preDeliveryQty: item.preDeliveryQty, //待发货数量
          preReceiveQty: 0, //4444暂无待收货数量
          preWarehouseQty: item.preWarehouseQty, //待入库数量
          productCode: item.productNameCode || 1, //333暂无接口涉及产品代码
          productName: item.productName, //涉及产品
          projectCode: item.projectCode || '', //项目编号
          projectName: item.projectName || '', //项目名称
          projectRowText: item.projectRowText, //行项目文本
          projectTextBatch: item.projectTextBatch, //项目文本批次
          returnIdentification: item.returnIdentificationValue || '0', //退货标识
          domesticDemandFlag: item.domesticDemandFlagValue, //是否内需跟单
          provisionalEstimateStatus: item.provisionalEstimateStatusValue || '0', //是否暂估价
          purOrderDetailRequire: {
            //订单明细申请
            abolished: 0, //是否删除 0-未删除；1-已删除
            approvedTotalPrice: item.approvedTotalPrice, //批准预算总金额
            budgetCode: item.budgetCode, //4444暂无字段预算编码
            budgetSubject: item.budgetSubject, //预算科目
            budgetTotalPrice: item.budgetTotalPrice, //预算总价（未税）
            budgetUnitPrice: item.budgetUnitPrice, //预算单价（未税）
            contract: item.agreementCode, //推荐合同/协议编号
            contractRel: item.contractRel, //关联合同协议编号
            contractRelId: item.contractRelId, //关联合同协议id
            contractRelCode: item.contractRelCode, //关联合同协议code
            contractType: item.contractTypeValue, //合同协议类型
            forecastQty: item.budgetQuantity, //预测采购量
            id: item.purRequireId || 0,
            orderDetailId: item.id || 0,
            orderId: this.entryType === '1' ? 0 : this.entryId,
            postingAccountCode: item.postingAccountCode || 1, //333暂无接口总账科目编码
            postingAccountId: item.postingAccountId || 1, //333暂无接口总账科目id
            postingAccountName: item.postingAccountName, //总账科目名称
            profitCenterId: item.profitCenterId || 1, //利润中心id
            profitCenterName: item.profitCenterName1, //利润中心
            profitCenterCode: item.profitCenterCode, //利润中心code
            projectCode: item.projectCode || '', //项目编号
            projectName: item.projectName || '', //项目名称
            purchasingCycleEnd: item.purchasingCycleEnd ? item.purchasingCycleEnd.getTime() : '0', //截止预测采购周期
            purchasingCycleStart: item.purchasingCycleStart
              ? item.purchasingCycleStart.getTime()
              : '0', //起始预测采购周期
            cycleType: item.cycleType,
            deliveryDateCycle: item.deliveryDateCycle,
            requiredDeliveryDate: item.requiredDeliveryDate
              ? item.requiredDeliveryDate.getTime()
              : '0', //要求交期
            requirementDescription: item.requirementDescription || '', //需求描述,后端让传空
            subjectTotal: item.subjectTotal, //科目总额
            taxedTotalPrice: item.taxedTotalPrice, //预算总价（含税）
            taxedUnitPrice: item.taxedUnitPrice, //预算单价（含税）
            requireName: item.requireName, //需求名称
            artParam: item.artParam, //美工变量
            techContactPerson: item.techContactPerson //技术对接人
          },
          requiredDeliveryDate: item.requiredDeliveryDate
            ? item.requiredDeliveryDate.getTime()
            : '0', //要求交期
          purUnitCode: item.purUnitNameCode, //333 采购单位接口无code orderUnitName orderUnitCode
          purUnitId: item.orderUnitId, //采购单位 orderUnitId
          purUnitName: item.purUnitName, //采购单位
          purchaseStrategy: item.purchaseStrategyValue, //采购策略 1-标准；2-寄售；3-第三方
          qualityExemptionMarkCode: item.qualityExemptionMarkNameCode, //质量免检标识
          qualityExemptionMarkId: null,
          qualityExemptionMarkName: item.qualityExemptionMarkName, //质量免检标识
          quantity: item.quantity, //订单数量
          receiveAddress: item.receiveAddress, //收货地址
          receiveAddressId: item.receiveAddressId || 1, //333暂无接口收货地址
          receiveQty: 0, //4444暂无字段已收货数量
          receiveSiteCode: item.receiveSiteCode || '', //4444暂无字段收货工厂/地点编码
          receiveSiteId: item.receiveSiteId || 0, //4444暂无字段收货工厂/地点
          receiveSiteName: item.receiveSiteName || '', //4444暂无字段收货工厂/地点
          receiveStatus: 0, //4444暂无字段收货状态：0-未收货；1-部分收货；2-全部收货
          remark: item.remark, //备注
          requestOrderMethod: item.requestOrderMethod, //申请转化订单方式 1-独立；2-集中
          requestRels: [],
          shippingMethodCode: item.shippingMethodCode, //物流方式编码
          shippingMethodName: item.shippingMethodName, //物流方式名称
          siteCode: item.siteCode, //工厂
          siteId: item.siteId, //工厂
          siteName: item.siteName, //工厂
          skuCode: item.skuCode, //sku编码
          skuId: item.skuId || 0, //sku编码
          skuName: item.skuName || '', //sku编码
          skuPicUrl: item.picUrl, //sku图片地址
          specification: item.specification, //规格型号
          subjectType: item.subjectTypeValue, //科目类型：0、成本中心；1、销售订单；2、生产工单；3、项目 4资产 5其他
          taxCode: item.taxCode, //税率编码 从价格记录带出来
          taxPrice: item.taxPrice, //含税单价
          taxTotal: item.taxTotal, //含税总价
          priceUnit: item.priceUnit, //价格单位
          timePromise: item.timePromise1 || '0',
          supRemark: item.supRemark || '',
          taxid: item.taxid, //税率
          tradeClauseCode: item.tradeClauseCode, //贸易条款
          tradeClauseId: item.tradeClauseId, //贸易条款
          tradeClauseName: item.tradeClauseName, //贸易条款
          transitQty: item.transitQty, //在途数量
          unPrice: item.unPrice, //不加点单价
          unTotal: item.unTotal, //不加点金额
          unitCode: item.unitCode || '', //333 接口基本单位无code
          unitId: item.unitId, //基本单位
          unitName: item.unitName, //基本单位
          urgentTime: item.urgentTime ? new Date(item.urgentTime).getTime() : '', //加急时间
          urgentStatus: item.urgentTime ? 1 : 0,
          // "version": 0,//版本号
          requiredId: item.requiredId, //申请明细id
          contractItemId: item.contractItemId, //合同明细id
          entryFrom: item.entryFrom,
          warehouse: item.warehouse, //库存地点name
          warehouseCode: item.warehouseCode, //库存地点code
          warehouseId: '1', //4444暂无字段库存地点不需要
          warehouseQty: item.warehouseQty, //已入库数量
          warehouseStatus: item.warehouseStatusValue, //入库状态 0-未入库；1-部分入库；2-全部入库
          workOrderRel: item.workOrderRel || '', //关联工单号
          closeStatus: item.closeStatus || 0
        }
        if (item.qualityExemptionMarkName == this.$t('免检')) {
          obj.qualityExemptionMarkId = '1'
        }
        if (item.qualityExemptionMarkName == this.$t('需检验')) {
          obj.qualityExemptionMarkId = '0'
        }
        if (item.file) {
          item.file.forEach((item1) => {
            obj.orderFiles.push({
              docId: 0,
              docType: 'po',
              fileName: item1.fileName,
              fileSize: item1.fileSize,
              fileType: item1.fileType,
              id: item1.id,
              itemNo: item.itemNo || 0,
              lineNo: item.itemNo || 0,
              nodeCode: 'po_item_file',
              nodeName: this.$t('订单明细附件'),
              nodeType: 1,
              orderDetailId: item.id || 0,
              orderId: this.entryType === '1' ? 0 : this.entryId,
              parentId: 0,
              remark: '',
              remoteFileId: item1.id,
              sysFileId: item1.id,
              type: 0,
              remoteUrl: item1.url,
              url: item1.url,
              itemCode: item.itemCode, //物料编码
              itemName: item.itemName, //物料名称
              skuCode: item.skuCode, //sku编码
              skuName: item.skuName || '', //sku名称
              specification: item.specification, //规格型号
              createUserName: item1.createUserName || '', //创建人
              createTime: item1.createTime || '' //创建时间
            })
          })
        }
        this.columnData.forEach((item1) => {
          if (item1.tableField === 0) {
            obj.fieldDataList.push({
              fieldCode: item1.fieldCode,
              fieldData: item[item1.fieldCode] || '',
              fieldId: item1.fieldId,
              fieldKey: item1.fieldKey,
              fieldType: item1.fieldType,
              recordId: item.recordId || 0
            })
          }
        })
        if (this.entryType === '1' && (this.entrySource === '0' || this.entrySource === '4')) {
          //从采购申请新增过来
          obj.requestRels = [
            {
              itemNo: item.itemNo1,
              quantity: item.quantity,
              requestId: item.headerId,
              requestItemId: item.id,
              requestCode: item.requestCode,
              relCode: item.requestCode,
              relItemNo: item.itemNo1
            }
          ]
        }
        //成本中心 start
        if (this.entryType === '1') {
          if (this.entrySource === '1' || this.entrySource === '2') {
            //手工创建新增
            if (item.costCenterId) {
              obj.orderCosts = [
                {
                  id: item.costId ? item.costId : 0,
                  orderDetailId: item.id ? item.id : 0,
                  orderId: this.entryType === '1' ? 0 : this.entryId,
                  allocationMode: this.costSwitch,
                  costCenterCode: item.costCenterCode ? item.costCenterCode : '',
                  costCenterId: item.costCenterId ? item.costCenterId : '1',
                  costCenterName: item.costCenterName,
                  percentage: item.costPercentage ? item.costPercentage : 100
                }
              ]
            }
          }
          if (this.entrySource === '0' || this.entrySource === '4') {
            if (item.costSharingResponses) {
              let costs = []
              item.costSharingResponses.forEach((item1) => {
                if (item1.type === 1) {
                  costs.push({
                    id: '0',
                    orderDetailId: 0,
                    orderId: 0,
                    allocationMode: this.costSwitch,
                    costCenterCode: item1.costSharingAccCode,
                    costCenterId: item1.id,
                    costCenterName: item1.costSharingAccName,
                    percentage: item1.appProportion
                  })
                }
              })
              obj.orderCosts = costs
            }
          }
        }
        if (this.entryType === '2') {
          //编辑进入
          if ((this.entrySource === '1' || this.entrySource === '0') && this.entryDraft !== '1') {
            if (item.costs1) {
              let costs = []
              item.costs1.forEach((item1) => {
                costs.push({
                  id: item1.id,
                  orderDetailId: item.id ? item.id : 0,
                  orderId: this.entryType === '1' ? 0 : this.entryId,
                  allocationMode: this.costSwitch,
                  costCenterCode: item1.costCenterCode,
                  costCenterId: item1.costCenterId,
                  costCenterName: item1.costCenterName,
                  percentage: item1.percentage
                })
              })
              obj.orderCosts = costs
            }
          }
          if (this.entryDraft === '1') {
            if (item.costCenterId) {
              obj.orderCosts = [
                {
                  id: item.costId ? item.costId : 0,
                  orderDetailId: item.id ? item.id : 0,
                  orderId: this.entryType === '1' ? 0 : this.entryId,
                  allocationMode: this.costSwitch,
                  costCenterCode: item.costCenterCode ? item.costCenterCode : '',
                  costCenterId: item.costCenterId ? item.costCenterId : '1',
                  costCenterName: item.costCenterName,
                  percentage: item.costPercentage ? item.costPercentage : 100
                }
              ]
            }
          }
          if (this.entrySource === '4') {
            if (item.entryFrom === '4') {
              if (item.costSharingResponses) {
                let costs = []
                item.costSharingResponses.forEach((item1) => {
                  costs.push({
                    id: item1.id,
                    orderDetailId: 0,
                    orderId: this.entryId,
                    allocationMode: this.costSwitch,
                    costCenterCode: item1.costSharingAccCode,
                    costCenterId: item1.id,
                    costCenterName: item1.costSharingAccName,
                    percentage: item1.appProportion
                  })
                })
                obj.orderCosts = costs
              }
            } else {
              if (item.costs1) {
                let costs = []
                item.costs1.forEach((item1) => {
                  costs.push({
                    id: item1.id,
                    orderDetailId: item.id ? item.id : 0,
                    orderId: this.entryId,
                    allocationMode: this.costSwitch,
                    costCenterCode: item1.costCenterCode,
                    costCenterId: item1.costCenterId,
                    costCenterName: item1.costCenterName,
                    percentage: item1.percentage
                  })
                })
                obj.orderCosts = costs
              }
            }
          }
        }
        //成本中心 end
        if (this.entryType === '2' || this.entryType === '3') {
          if (item.entryFrom === '4') {
            obj.requestRels = [
              {
                itemNo: item.itemNo1,
                quantity: item.quantity,
                requestId: item.headerId,
                requestItemId: item.id,
                requestCode: item.requestCode,
                relCode: item.requestCode,
                relItemNo: item.itemNo1
              }
            ]
          } else {
            obj.id = item.id || 0
            obj.itemNo = item.itemNo || 0
            obj.requestRels = item.requestRel
              ? [
                  {
                    itemNo: item.requestRel[0].requestItemNo,
                    quantity: item.requestRel[0].requestCount,
                    requestId: item.requestRel[0].requestId,
                    requestItemId: item.requestRel[0].requestItemId,
                    requestCode: item.requestRel[0].requestCode,
                    relCode: item.requestRel[0].relCode,
                    relItemNo: item.requestRel[0].relItemNo
                  }
                ]
              : [] //采购申请明细与采购订单明细关联
          }
        }
        if (this.costSwitch === '0') {
          obj.orderCosts = []
        }
        sendList.push(obj)
      })
      console.log('最新的表格数据1', sendList)
      this.sendList = sendList
      //计算头部含税未税总价,最近的要求交期
      let orderTaxTotal = 0 //传给验收计划用
      let orderFreeTotal = 0 //传给验收计划的
      let requiredDateList = [] //获取最小的要求交期
      let entryFileList = [] //用于同步传给相关附件
      let ItemNoList = [] //用于获取最大的行号
      let judgeList = [] //用于校验是否可以并单校验
      let requiredIdList = [] //申请明细id
      let contractItemIdList = [] //合同明细list
      sendList.forEach((item) => {
        orderTaxTotal = bigDecimal.add(item.taxTotal, orderTaxTotal)
        orderFreeTotal = bigDecimal.add(item.freeTotal, orderFreeTotal)
        if (
          item.purOrderDetailRequire.requiredDeliveryDate &&
          item.purOrderDetailRequire.requiredDeliveryDate != '0'
        ) {
          requiredDateList.push(item.purOrderDetailRequire.requiredDeliveryDate)
        }
        if (item.orderFiles.length) {
          entryFileList = entryFileList.concat(item.orderFiles)
        }
        ItemNoList.push(item.itemNo)
        let obj = {
          address: item.receiveAddress, //收货地址
          businessTypeId: this.topInfo.businessId, //业务类型id
          buyerDepId: item.buyerOrgId, //采购组id
          company: item.siteId, //工厂
          companyId: this.topInfo.company, //公司
          range: item.purOrderDetailRequire.requiredDeliveryDate //需求日期
        }
        if (this.entryType === '1') {
          if (this.entrySource === '0' || this.entrySource === '4') {
            //新增从采购申请或者从采购合同
            requiredIdList.push(item.requiredId)
          }
          if (this.entrySource === '1' || this.entrySource === '2') {
            judgeList.push(obj)
          }
          if (this.entrySource === '4') {
            contractItemIdList.push(item.contractItemId)
          }
        }
        if (this.entryType === '2') {
          if (this.entrySource === '0') {
            //采购申请
            if (item.requestRels) {
              requiredIdList.push(Number(item.requestRels[0].requestItemId))
            }
          }
          if (this.entrySource === '4') {
            //合同编辑 带入添加的
            if (item.entryFrom === '4') {
              contractItemIdList.push(item.contractItemId)
              requiredIdList.push(item.requiredId)
            } else {
              judgeList.push(obj)
              if (item.requestRels) {
                contractItemIdList.push(item.requestRels[0].requestItemId)
              }
            }
          }
          if (this.entrySource === '1') {
            judgeList.push(obj)
          }
        }
      })
      this.contractItemIdList = contractItemIdList
      this.judgeList = judgeList
      this.requiredIdList = requiredIdList
      console.log('校验明细行的数据源', this.judgeList, this.requiredIdList)
      orderTaxTotal = bigDecimal.round(orderTaxTotal, 2)
      orderFreeTotal = bigDecimal.round(orderFreeTotal, 2)
      let orderRequiredDate = 0
      if (requiredDateList.length) {
        orderRequiredDate = Math.min(...requiredDateList)
      }
      let maxItemNo = Math.max(...ItemNoList)
      this.$emit('updateOrderInfo', orderTaxTotal, orderFreeTotal, orderRequiredDate)
      this.$emit('updateEntryFileList', entryFileList)
      this.$emit('updateGrid', sendList, maxItemNo)
    },
    validateJudgeList() {
      //校验明细行是否可以并单
      return new Promise((resolve) => {
        let judgeList = this.judgeList
        let params = {
          idList: {
            idList: this.requiredIdList
          },
          purchaseOrderMatchDTOList: judgeList
        }
        this.$store.commit('startLoading')
        this.$API.purchaseOrder.purchaseToOrderConsolidationMatch(params).then((res) => {
          this.$store.commit('endLoading')
          resolve(res.data.result)
          if (!res.data.result) {
            this.$toast({
              content: this.$t('并单校错误') + res.data.errorItems.join(','),
              type: 'error'
            })
          }
        })
      })
    },
    validateTable1() {
      let flag = true
      let strArr = []
      let isUnValidMobile = this.unMobileCompany.some(
        (item) => item.itemCode === this.topInfo.companyInfo.companyCode
      )
      this.columnData.some((item1) => {
        if (item1.field === 'contact') {
          this.dataSource.some((item2, index2) => {
            const { phoneNumReg } = RegExpMap
            // 校验5：联系方式  得是11位的数字
            if (item2[item1.field]) {
              if (item2.closeStatus != 1) {
                if (!phoneNumReg.test(item2[item1.field]) && !isUnValidMobile) {
                  strArr.push(`第${index2 + 1}行 ${item1.headerText}请输入正确的手机号`)
                  flag = false
                  return
                }
              }
            }
          })
        }
      })
      if (!flag) {
        this.$toast({ content: strArr[0], type: 'error' })
      }
      return flag
    },
    validateTable() {
      let flag = true
      let strArr = []
      this.columnData.forEach((item1) => {
        if (
          (item1.required ||
            item1.field === 'freePrice' ||
            item1.field === 'freeTotal' ||
            item1.field === 'taxPrice' ||
            item1.field === 'taxTotal' ||
            item1.field === 'unPrice' ||
            item1.field === 'unTotal' ||
            item1.field === 'approvedTotalPrice' ||
            item1.field === 'budgetTotalPrice' ||
            item1.field === 'budgetUnitPrice' ||
            item1.field === 'subjectTotal' ||
            item1.field === 'taxedTotalPrice' ||
            item1.field === 'taxedUnitPrice') &&
          item1.width != 0
        ) {
          this.dataSource.some((item2, index2) => {
            if (!item2.freePrice) {
              item2.freePrice = 0
            }
            if (item2[item1.field] === null || item2[item1.field] === '') {
              if (item2.closeStatus != 1) {
                strArr.push(`第${index2 + 1}行 ${item1.headerText}不能为空`)
                flag = false
                return
              }
            }
          })
        }
      })
      let isUnValidMobile = this.unMobileCompany.some(
        (item) => item.itemCode === this.topInfo.companyInfo.companyCode
      )
      this.columnData.some((item1) => {
        if (item1.field === 'contact') {
          this.dataSource.some((item2, index2) => {
            const { phoneNumReg } = RegExpMap
            // 校验5：联系方式  得是11位的数字
            if (item2[item1.field]) {
              if (item2.closeStatus != 1) {
                if (!phoneNumReg.test(item2[item1.field]) && !isUnValidMobile) {
                  console.log(this.topInfo)
                  console.log(this.unMobileCompany)
                  strArr.push(`第${index2 + 1}行 ${item1.headerText}请输入正确的手机号1`)
                  flag = false
                  return
                }
              }
            }
          })
        }
      })
      this.columnData.some((item1) => {
        if (item1.field === 'subjectType') {
          this.dataSource.some((item2, index2) => {
            if (item2.closeStatus != 1) {
              if (item2.subjectType === this.$t('成本中心')) {
                // if (!item2.orderCosts) {
                //   strArr.push(
                //     `第${index2 + 1}行科目类型为成本中心 成本中心不能为空`
                //   );
                //   flag = false;
                //   return;
                // }
              }
              if (item2.subjectType === this.$t('销售订单')) {
                if (!item2.customerOrder) {
                  strArr.push(`第${index2 + 1}行科目类型为销售订单 关联销售订单号不能为空`)
                  flag = false
                  return
                }
              }
              if (item2.subjectType === this.$t('生产工单')) {
                if (!item2.workOrderRel) {
                  strArr.push(`第${index2 + 1}行科目类型为生产工单 关联工单不能为空`)
                  flag = false
                  return
                }
              }
              if (item2.subjectType === this.$t('项目')) {
                if (!item2.projectCode) {
                  strArr.push(`第${index2 + 1}行科目类型为项目 项目编号不能为空`)
                  flag = false
                  return
                }
              }
            }
          })
        }
      })
      if (!flag) {
        this.$toast({ content: strArr[0], type: 'error' })
      }
      return flag
    },
    getData() {
      return this.$refs.dataGrid.ejsRef.getDataModule()?.dataManager?.dataSource?.json
    },
    handleAdd() {
      // 新增时，判断下，是否已新增过空行???未做
      this.$refs.dataGrid.ejsRef.addRecord()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  background: #fff;
}
.pt20 {
  padding-top: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  /deep/ #Grid2_toolbarItems {
    border-top: none;
  }
  // .mt-data-grid {
  //   flex: 1;
  //   /deep/ .e-grid {
  //     height: 100%;
  //     display: flex;
  //     flex-direction: column;

  //     .e-headerchkcelldiv {
  //       padding-left: 0;
  //     }

  //     .e-gridcontent {
  //       flex: 1;
  //       .e-content {
  //         // height: 100% !important;
  //         flex-direction: row !important;
  //       }
  //     }
  //   }
  // }
}
.toolbar-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;

  .one-bar {
    display: flex;
    align-items: center;
    margin-right: 20px;
    cursor: pointer;
    .mt-icons {
      font-size: 14px;
      color: #4f5b6d;
      margin-right: 5px;
    }
    span {
      word-break: keep-all;
      font-size: 14px;
      color: #4f5b6d;
      font-weight: normal;
    }
  }

  .flex1 {
    flex: 1;
  }
}
</style>
