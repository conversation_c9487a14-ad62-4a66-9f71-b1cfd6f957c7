<template>
  <div>
    <mt-select
      id="payTypeName"
      v-model="data.payTypeName"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="false"
      :disabled="isDisabled"
      :allow-filtering="true"
    ></mt-select>
  </div>
</template>
<script>
export default {
  data() {
    return {
      placeholder: this.$t('请选择付款类型'),
      fields: { text: 'itemName', value: 'itemName' },
      dataSource: [],
      isDisabled: false
    }
  },
  mounted() {
    this.getDataSource()
    // 2. 如果勾选有变化
    this.$bus.$on('acceptancePlanSetPayType', () => {
      setTimeout(() => {
        this.selectChange({
          itemData: {
            itemName: this.$t('挂账-不付款'),
            itemCode: '13'
          }
        })
        this.data.payTypeName = this.$t('挂账-不付款')
      }, 500)
    })
  },
  methods: {
    getDataSource() {
      this.$API.masterData.getDictCode({ dictCode: 'PAY_METHOD' }).then((res) => {
        this.dataSource = res.data || []
      })
    },
    startOpen() {
      if (!this.dataSource.length) {
        this.getDataSource()
      }
    },
    selectChange(val) {
      console.log(val)
      this.$parent.$emit('selectedChanged', {
        fieldCode: 'payType',
        itemInfo: val.itemData
      })
    }
  },

  beforeDestroy() {
    this.$bus.$off(`acceptancePlanSetPayType`)
  }
}
</script>
