<template>
  <div>
    <mt-select
      id="warehouseCode"
      v-model="data.warehouseCode"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="false"
      :disabled="isDisabled"
      :allow-filtering="true"
      :filtering="serchText"
    ></mt-select>
  </div>
</template>
<script>
import { utils } from '@mtech-common/utils'

export default {
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'locationCode' },
      dataSource: [],
      isDisabled: false,
      siteCode: '', //工厂code
      warehouseCode: '' //库存地点编号
    }
  },
  async mounted() {
    if (this.data.column.field === 'warehouseCode') {
      //库存地点
      this.siteCode = this.data.siteCode
      this.warehouseCode = this.data.warehouseCode
      this.$bus.$on('updateSiteCode1', (val) => {
        //监听工厂code变化
        this.siteCode = val
        if (this.data.warehouseCode) {
          console.log('已经选择了库存地点')
          return
        }
        this.getLocation()
      })
      this.getLocation()
      this.getLocation = utils.debounce(this.getLocation, 1000)
      this.setDisabled1()
      this.$bus.$on('warehouseCodeChange', () => {
        this.data.warehouseCode = null
        this.data.warehouse = null
      })
    }
  },
  methods: {
    serchText(val) {
      console.log('搜索值', val)
      if (this.data.column.field == 'warehouseCode') {
        this.getLocation(val && val.text ? val.text : '')
      }
    },
    setDisabled1() {
      this.isDisabled = this.data.column.allowEditing === false ? true : false
    },
    setDisabled() {
      if (this.data.isEntry === '1') {
        this.isDisabled = true
      }
      if (this.data.isEntry === '2') {
        this.isDisabled = false
      }
    },
    getLocation(val) {
      //查询库存地点
      let str = val || this.warehouseCode
      let params = {
        commonCode: this.siteCode, //工厂code
        dataLimit: 50,
        fuzzyParam: str //搜索参数
      }
      if (!params.commonCode) {
        console.log('工厂没有')
        return
      }
      this.$API.masterData.getLocationFuzzyQuery(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          if (item.externalCode) {
            item.locationCode = item.externalCode
            if (item.externalName) {
              item.locationName = item.externalName
            }
          }
          item.label = `${item.locationCode}-${item.locationName}`
        })
        this.dataSource = list
      })
    },
    startOpen() {
      console.log(this.data, '下拉打开时最新的行数据')
      if (this.data.column.field === 'warehouseCode') {
        if (!this.dataSource.length) {
          this.getLocation()
        }
      }
    },
    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      if (this.data.column.field === 'warehouseCode') {
        //库存地点
        this.$bus.$emit('warehouseChange', val.itemData.locationName)
        this.$parent.$emit('selectedChanged', {
          //传出额外数据库存地点
          fieldCode: 'warehouseCode',
          itemInfo: {
            warehouseCode: val.itemData.locationCode,
            warehouseName: val.itemData.locationName,
            warehouseId: val.itemData.id
          }
        })
      }
    }
  }
}
</script>
