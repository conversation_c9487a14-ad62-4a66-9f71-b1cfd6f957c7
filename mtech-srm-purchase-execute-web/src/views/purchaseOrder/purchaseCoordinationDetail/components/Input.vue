<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="isDisabled"
      v-if="maxlength"
      :maxlength="maxlength"
    ></mt-input>
  </div>
</template>
<script>
export default {
  data() {
    return { data: {}, isDisabled: false, addId: '', maxlength: 0 }
  },
  mounted() {
    this.maxlength = this.data.column.maxlength || 128
  }
}
</script>
