<template>
  <div>
    <mt-input
      id="acceptanceQuantity"
      style="display: none"
      :value="data.acceptanceQuantity"
    ></mt-input>
    <mt-inputNumber
      v-model="data.acceptanceQuantity"
      :min="0.01"
      :precision="3"
      :step="1"
      :show-clear-button="false"
      @input="handleChange"
      v-if="data.acceptanceStatus === 0"
    ></mt-inputNumber>
    <mt-input
      type="text"
      v-if="data.acceptanceStatus === 1"
      v-model="data.acceptanceQuantity"
      disabled
    />
  </div>
</template>
<script>
export default {
  data() {
    return {
      data: {}
    }
  },
  methods: {
    handleChange(e) {
      console.log(
        e,
        '我是百分比',
        this.data.lastPercentage,
        this.data.lastFreeTotal1,
        this.data.lastTaxTotal1
        // this.data.freeTotal,
        // this.data.taxTotal
      )
      this.$bus.$emit('acceptanceQuantityChange', e)
      // if (bigDecimal.compareTo(e, this.data.lastPercentage) === 0) {
      //   console.log("比例刚好等于100");
      //   this.$bus.$emit("updateFreeTotal1", this.data.lastFreeTotal1);
      //   this.$bus.$emit("updateTaxTotal1", this.data.lastTaxTotal1);
      //   return;
      // }
      // if (!e || e == "0.00") {
      //   this.$bus.$emit("updateFreeTotal1", "");
      //   this.$bus.$emit("updateTaxTotal1", "");
      //   return;
      // }
      // //在这里判断下输入的比例是否刚好达到了100%
      // if (this.data.freeTotal) {
      //   let freeTotal1 = "";
      //   let acceptanceQuantity = bigDecimal.divide(e, 100);
      //   freeTotal1 = bigDecimal.multiply(acceptanceQuantity, this.data.freeTotal);
      //   freeTotal1 = bigDecimal.round(freeTotal1, 2);
      //   this.$bus.$emit("updateFreeTotal1", freeTotal1);
      // }
      // if (this.data.taxTotal) {
      //   let taxTotal1 = "";
      //   let acceptanceQuantity = bigDecimal.divide(e, 100);
      //   taxTotal1 = bigDecimal.multiply(acceptanceQuantity, this.data.taxTotal);
      //   taxTotal1 = bigDecimal.round(taxTotal1, 2);
      //   this.$bus.$emit("updateTaxTotal1", taxTotal1);
      // }
    }
  }
}
</script>
