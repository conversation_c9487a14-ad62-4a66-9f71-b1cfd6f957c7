<template>
  <div>
    <mt-input
      :id="data.column.field"
      style="display: none"
      :value="data[data.column.field]"
    ></mt-input>
    <mt-input v-model="data[data.column.field]" disabled></mt-input>
  </div>
</template>
<script>
export default {
  name: 'FreeTotalTaxTotalView',
  data() {
    return {
      data: {
        freeTotal1: '',
        taxTotal1: ''
      }
    }
  },
  mounted() {
    // this.$bus.$on("updateFreeTotal1", (e) => {
    //   this.data.freeTotal1 = e;
    // });
    // this.$bus.$on("updateTaxTotal1", (e) => {
    //   this.data.taxTotal1 = e;
    // });
  },
  methods: {}
}
</script>
