<template>
  <div>
    <mt-input id="percentage" style="display: none" :value="data.percentage"></mt-input>
    <mt-inputNumber
      v-model="data.percentage"
      :min="0.01"
      :precision="2"
      :step="1"
      :show-clear-button="false"
      @input="handleChange"
      v-if="data.acceptanceStatus === 0"
    ></mt-inputNumber>
    <mt-input type="text" v-if="data.acceptanceStatus === 1" v-model="data.percentage" disabled />
  </div>
</template>
<script>
import bigDecimal from 'js-big-decimal'
export default {
  data() {
    return {
      data: {
        acceptanceQuantity: '', //验收数量
        taxPrice: '', //含税单价
        freePrice: '', //未税单价
        percentage: '', //比例
        priceUnit: '' //价格单位
      }
    }
  },
  mounted() {
    this.acceptanceQuantity = this.data.acceptanceQuantity
    this.taxPrice = this.data.taxPrice
    this.freePrice = this.data.freePrice
    this.percentage = this.data.percentage
    this.priceUnit = this.data.priceUnit || 1
    this.$bus.$on('acceptanceQuantityChange', (val) => {
      this.acceptanceQuantity = val
      this.updateFreeTotal1()
      this.updateTaxTotal1()
    })
  },
  methods: {
    handleChange(e) {
      console.log(
        e,
        '我是百分比',
        this.data.lastPercentage,
        this.data.lastFreeTotal1,
        this.data.lastTaxTotal1
        // this.data.freeTotal,
        // this.data.taxTotal
      )
      if (!e || e == '0.00') {
        this.$bus.$emit('updateFreeTotal1', '')
        this.$bus.$emit('updateTaxTotal1', '')
        return
      }
      this.percentage = e
      this.updateFreeTotal1()
      this.updateTaxTotal1()
      //在这里判断下输入的比例是否刚好达到了100%
    },
    updateTaxTotal1() {
      if (this.acceptanceQuantity) {
        //如果有验收数量
        let taxTotal1 = ''
        taxTotal1 = bigDecimal.multiply(this.taxPrice, this.acceptanceQuantity)
        let percentage = bigDecimal.divide(this.percentage, 100)
        taxTotal1 = bigDecimal.multiply(taxTotal1, percentage)
        taxTotal1 = bigDecimal.divide(taxTotal1, this.priceUnit)
        taxTotal1 = bigDecimal.round(taxTotal1, 2, bigDecimal.RoundingModes.HALF_UP)
        this.$bus.$emit('updateTaxTotal1', taxTotal1)
      }
    },
    updateFreeTotal1() {
      if (this.acceptanceQuantity) {
        //如果有验收数量
        let freeTotal1 = ''
        freeTotal1 = bigDecimal.multiply(this.freePrice, this.acceptanceQuantity)
        let percentage = bigDecimal.divide(this.percentage, 100)
        freeTotal1 = bigDecimal.multiply(freeTotal1, percentage)
        freeTotal1 = bigDecimal.divide(freeTotal1, this.priceUnit)
        freeTotal1 = bigDecimal.round(freeTotal1, 2, bigDecimal.RoundingModes.HALF_UP)
        this.$bus.$emit('updateFreeTotal1', freeTotal1)
      }
    }
  }
}
</script>
