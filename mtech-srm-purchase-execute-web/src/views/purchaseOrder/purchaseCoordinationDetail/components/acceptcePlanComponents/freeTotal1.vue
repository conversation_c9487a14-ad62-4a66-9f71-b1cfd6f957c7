<template>
  <div>
    <mt-input
      :id="data.column.field"
      style="display: none"
      :value="data[data.column.field]"
    ></mt-input>
    <!-- <mt-input v-model="data[data.column.field]"></mt-input> -->
    <mt-inputNumber
      v-model="data[data.column.field]"
      :min="0.01"
      :precision="2"
      :step="1"
      :show-clear-button="false"
    ></mt-inputNumber>
  </div>
</template>
<script>
export default {
  data() {
    return {
      data: {
        freeTotal1: ''
      }
    }
  },
  mounted() {
    this.$bus.$on('updateFreeTotal1', (e) => {
      this.data.freeTotal1 = e
    })
  },
  methods: {}
}
</script>
