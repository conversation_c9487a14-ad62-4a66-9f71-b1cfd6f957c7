<template>
  <div>
    <mt-input id="handle1" style="display: none" :value="data.handle1"></mt-input>
    <div
      name="icon_solid_Createorder"
      class="template-svg"
      v-if="isOrder === '1' && closeStatus != '1'"
      @click="addRow"
    >
      <i class="mt-icons mt-icon-icon_solid_Createorder show"></i>
      <span class="icon-title handle">{{ $t('新增行') }}</span>
    </div>
    <div
      name="icon_solid_Delete"
      class="template-svg"
      v-if="isOrder === '2' && data.acceptanceStatus === 0 && closeStatus != '1'"
      @click="delRow"
    >
      <i class="mt-icons mt-icon-icon_solid_Delete show"></i>
      <span class="icon-title handle">{{ $t('删除') }}</span>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      isOrder: '1',
      data: {},
      closeStatus: '0'
    }
  },
  mounted() {
    this.isOrder = this.data.isOrder
    this.closeStatus = this.data.closeStatus
  },
  methods: {
    addRow() {
      console.log('新增行')
      this.$parent.$emit('addRow', this.data)
    },
    delRow() {
      console.log('删除行')
      this.$parent.$emit('delRow', this.data)
    }
  }
}
</script>
<style lang="scss" scoped>
.handle {
  margin-left: 3px;
  color: #00469c;
  font-size: 14px;
  cursor: pointer;
}
.show {
  color: #00469c;
  font-size: 14px;
  cursor: pointer;
}
</style>
