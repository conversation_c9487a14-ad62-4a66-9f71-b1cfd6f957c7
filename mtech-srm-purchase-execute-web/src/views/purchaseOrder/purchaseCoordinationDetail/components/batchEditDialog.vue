<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="$t('批量编辑')"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="requiredDeliveryDate" :label="$t('要求交期')">
        <mt-date-picker
          v-model="addForm.requiredDeliveryDate"
          :show-clear-button="true"
          :placeholder="$t('请选择要求交期')"
        ></mt-date-picker>
      </mt-form-item>
      <mt-form-item prop="budgetCode" :label="$t('预算编号')">
        <mt-input
          v-model="addForm.budgetCode"
          :show-clear-button="true"
          :placeholder="$t('请输入预算编号')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="postingAccountName" :label="$t('总账科目')">
        <mt-input
          v-model="addForm.postingAccountName"
          :show-clear-button="true"
          :placeholder="$t('请输入总账科目')"
        ></mt-input>
      </mt-form-item>
      <!-- <mt-form-item prop="consigneeId" :label="$t('收货人')">
        <mt-select
          ref="consigneeIdRef"
          v-model="addForm.consigneeId"
          :data-source="receiveUserIdData"
          :show-clear-button="true"
          :fields="{ value: 'id', text: 'employeeName' }"
          :placeholder="$t('请选择收货人')"
        ></mt-select>
      </mt-form-item> -->
      <mt-form-item prop="contact" :label="$t('联系方式')">
        <mt-inputNumber
          :width="400"
          :show-spin-button="false"
          :min="1"
          v-model="addForm.contact"
          :placeholder="$t('请输入联系方式')"
        ></mt-inputNumber>
      </mt-form-item>
      <mt-form-item prop="receiveAddress" :label="$t('收货地址')">
        <mt-input
          v-model="addForm.receiveAddress"
          :show-clear-button="true"
          :placeholder="$t('请输入收货地址')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="packageMethod" :label="$t('包装方式')">
        <mt-input
          v-model="addForm.packageMethod"
          :show-clear-button="true"
          :placeholder="$t('请输入包装方式')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="packageSpec" :label="$t('包装规格')">
        <mt-input
          v-model="addForm.packageSpec"
          :show-clear-button="true"
          :placeholder="$t('请输入包装规格')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="packageDesc" :label="$t('包装说明')">
        <mt-input
          v-model="addForm.packageDesc"
          :show-clear-button="true"
          :placeholder="$t('请输入包装说明')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="seriesName" :label="$t('关联产品系列名称')">
        <mt-input
          v-model="addForm.seriesName"
          :show-clear-button="true"
          :placeholder="$t('请输入关联产品系列名称')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="orderCosts" :label="$t('成本中心')">
        <mt-select
          id="orderCosts"
          v-model="addForm.orderCosts"
          :data-source="dataSource"
          :fields="{ text: 'label', value: 'costCenterCode' }"
          @open="startOpen"
          @change="handleSelect"
          :placeholder="$t('请选择成本中心')"
          :open-dispatch-change="false"
          :allow-filtering="true"
          :filtering="serchText"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
        <mt-input
          v-model="addForm.remark"
          :show-clear-button="true"
          :multiline="true"
          :placeholder="$t('请输入备注')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
// import common from "@/utils/constant";
// import { formatDate, formatRules } from "@/utils/util";
import { utils } from '@mtech-common/utils'
import { RegExpMap } from '@/utils/constant'
export default {
  data() {
    const { phoneNumReg } = RegExpMap
    const driverPhoneValidator = (rule, value, callback) => {
      if (!value) {
        this.$refs.ruleForm.clearValidate(['contact'])
        callback()
      } else if (!phoneNumReg.test(value)) {
        callback(new Error(this.$t('请输入正确的手机号')))
      } else {
        this.$refs.ruleForm.clearValidate(['contact'])
        callback()
      }
    }
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        orderCosts: '',
        costCenterName: '',
        costCenterCode: '',
        costCenterId: '',
        requiredDeliveryDate: null,
        budgetCode: null,
        taxid: null,
        postingAccountName: null,
        consigneeId: null, // 收货人
        consignee: null,
        contact: null,
        receiveAddress: null,
        packageMethod: null,
        packageSpec: null,
        packageDesc: null,
        seriesName: null,
        supplierId: null,
        costSharingAccName: null,
        remark: null
      },
      rules: {
        costSharingAccCode: [
          {
            required: true,
            message: this.$t('请选择成本中心'),
            trigger: 'blur'
          }
        ],
        contact: [
          {
            required: false,
            trigger: 'blur',
            validator: driverPhoneValidator
          }
        ]
      },
      dataSource: [] //成本中心
    }
  },
  computed: {
    companyCode() {
      let companyCode = this.$store.state.companyCode
      return companyCode
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    this.postCostCenterCriteriaQuery = utils.debounce(this.postCostCenterCriteriaQuery, 1000)
    this.postCostCenterCriteriaQuery()
  },
  methods: {
    serchText(val) {
      this.postCostCenterCriteriaQuery(val && val.text ? val.text : '')
    },
    startOpen() {
      if (!this.dataSource.length) {
        this.postCostCenterCriteriaQuery()
      }
    },
    postCostCenterCriteriaQuery(val) {
      let params = {
        companyCode: this.companyCode,
        costData: val,
        dataLimit: 20
      }
      this.$API.masterData.postCostCenterCriteriaQuery(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.costCenterCode}-${item.costCenterName}`
        })
        this.dataSource = list
      })
    },
    handleSelect(e) {
      this.addForm.costCenterId = e.itemData.id
      this.addForm.costCenterCode = e.itemData.costCenterCode
      this.addForm.costCenterName = e.itemData.costCenterName
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    confirm() {
      console.log('表单数据', this.addForm)
      let params = {}
      for (var i in this.addForm) {
        if (this.addForm[i]) {
          params[i] = this.addForm[i]
        }
      }
      if (this.addForm.consigneeId && this.$refs.consigneeIdRef) {
        let _data = this.$refs.consigneeIdRef.ejsRef.getDataByValue(this.addForm.consigneeId)
        console.log('收货人_data', _data)
        params.consigneeId = _data.id
        params.consignee = _data.employeeName
      }
      this.$emit('handleAddDialogShow', 'batchEditShow', false)
      this.$emit('confirmBatchSuccess', params)
      console.log('最终的数据', params)
    },
    handleClose() {
      this.$emit('handleAddDialogShow', 'batchEditShow', false)
    }
  }
}
</script>

<style></style>
