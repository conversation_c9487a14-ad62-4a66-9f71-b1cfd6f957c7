<template>
  <div>
    <mt-dialog
      ref="dialog"
      css-class="create-proj-dialog"
      :header="dialogTitle"
      :buttons="buttons"
      @close="handleCloseDialog"
    >
      <mt-template-page ref="template-0" :template-config="componentConfig"> </mt-template-page>
    </mt-dialog>
  </div>
</template>
<script>
import UTILS from '../../../../utils/utils'
export default {
  props: {
    contractItemIdList: {
      type: Array,
      default: () => []
    },
    topInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: this.$t('添加'),
      buttons: [
        {
          click: this.handleCloseDialog,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      componentConfig: [
        {
          toolbar: [],
          grid: {
            height: 'auto',
            columnData: [
              {
                width: '50',
                type: 'checkbox',
                showInColumnChooser: false
              },
              {
                field: 'contractContractCode',
                headerText: this.$t('合同编码'),
                width: '150'
              },
              {
                field: 'contractContractName',
                headerText: this.$t('合同名称'),
                width: '150'
              },
              {
                field: 'contractContractIdRelation',
                headerText: this.$t('关联合同编号'),
                width: '150'
              },
              {
                field: 'companyPurCompanyCode',
                headerText: this.$t('公司编号'),
                width: '150'
              },
              {
                field: 'companyPurCompanyName',
                headerText: this.$t('公司名称'),
                width: '150'
              },
              {
                field: 'contractPurOrgName',
                headerText: this.$t('采购组织'),
                width: '150'
              },
              {
                field: 'contractPurGroupName',
                headerText: this.$t('采购组'),
                width: '150'
              },
              {
                field: 'contractPurName',
                headerText: this.$t('采购员'),
                width: '150'
              },
              {
                field: 'itemLineNo',
                headerText: this.$t('行号'),
                width: '150'
              },
              {
                field: 'contractSupplierCode',
                headerText: this.$t('供应商代码'),
                width: '150'
              },
              {
                field: 'contractSupplierName',
                headerText: this.$t('供应商名称'),
                width: '150'
              },
              {
                field: 'itemItemCode',
                headerText: this.$t('物料/品项编码'),
                width: '150'
              },
              {
                field: 'itemItemName',
                headerText: this.$t('物料/品项名称'),
                width: '150'
              },
              {
                field: 'pendingPurRequirementName',
                headerText: this.$t('需求名称'),
                width: '150'
              },
              {
                field: 'pendingPurRequirementDesc',
                headerText: this.$t('需求说明'),
                width: '150'
              },
              {
                field: 'pendingPurRequirementNum',
                headerText: this.$t('需求数量'),
                width: '150'
              },
              {
                field: 'itemUnitName',
                headerText: this.$t('基本单位'),
                width: '150'
              },
              {
                field: 'itemUnitId',
                headerText: this.$t('税率'),
                width: '150'
              },
              {
                field: 'itemUntaxedUnitPrice',
                headerText: this.$t('未税单价'),
                width: '150'
              },
              {
                field: 'itemUntaxedTotalPrice',
                headerText: this.$t('未税总价'),
                width: '150'
              },
              {
                field: 'itemTaxedUnitPrice',
                headerText: this.$t('含税单价'),
                width: '150'
              },
              {
                field: 'itemTaxedTotalPrice',
                headerText: this.$t('含税总价'),
                width: '150'
              },
              {
                field: 'contractEffectiveBeginTime',
                headerText: this.$t('有效时间起'),
                width: '150',
                valueConverter: {
                  type: 'function',
                  filter: (e) => {
                    if (e) {
                      e = new Date(e).getTime()
                      return UTILS.dateFormat(e)
                    } else {
                      return e
                    }
                  }
                }
              },
              {
                field: 'contractEffectiveEndTime',
                headerText: this.$t('终止时间'),
                width: '150',
                valueConverter: {
                  type: 'function',
                  filter: (e) => {
                    if (e) {
                      e = new Date(e).getTime()
                      return UTILS.dateFormat(e)
                    } else {
                      return e
                    }
                  }
                }
              },
              {
                field: 'companySiteName',
                headerText: this.$t('地点/工厂'),
                width: '150'
              }
            ]
            // asyncConfig: {
            //   url: "api/contract/contract-item",
            // },
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    handleCloseDialog() {
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      let getCurrentTabRef = this.$refs['template-0'].getCurrentTabRef()
      let getSelectedRecords = getCurrentTabRef.grid.getSelectedRecords()
      let ids = []
      let newSelectList = []
      getSelectedRecords.forEach((item) => {
        let hasItem = this.contractItemIdList.some((item1) => {
          return item1 == item.id
        })
        if (!hasItem) {
          newSelectList.push(item)
        }
      })
      newSelectList.map((item) =>
        ids.push({
          lineNo: item.pendingPurchaseRequestNo,
          requestCode: item.pendingPurRequestCode
        })
      )
      if (getSelectedRecords && ids.length === 0) {
        this.$toast({
          content: this.$t('您选择的合同明细已添加至订单,请重新选择'),
          type: 'warning'
        })
        return
      }
      if (ids.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      this.getRequestList(ids, newSelectList)
    },
    getRequestList(ids, newSelectList) {
      let params1 = {
        paramList: ids
      }
      this.$API.purchaseOrder.requestItemBatchQuery(params1).then((res) => {
        //查询采购申请明细行数据
        this.handleCloseDialog()
        if (!res.data.length) return
        res.data.forEach((item) => {
          newSelectList.some((item1) => {
            if (
              item1.pendingPurRequestCode == item.requestCode &&
              item1.pendingPurchaseRequestNo == item.itemNo
            ) {
              item.headerId = item1.contractId
              item.requiredId = item.id
              item.id = item1.id
              item.contractItemId = item1.id
              item.itemNo1 = item.itemNo
              item.itemNo = item1.lineNo
              item.remainingQuantity = item1.purRequirementRemainNum || 0
            }
          })
        })
        this.$emit('addContractDataSource', res.data)
      })
    },
    show() {
      this.$set(this.componentConfig[0].grid, 'asyncConfig', {
        url: '/contract/contract-item/queryToPurOrder',
        params: {
          ids: this.contractItemIdList,
          purCompanyCode: this.topInfo.companyInfo.companyCode,
          // businessId: this.topInfo.businessId,
          businessTypeCode: this.topInfo.businessTypeCode,
          // businessTypeName: this.topInfo.businessTypeName,
          supplierCode: this.topInfo.purtenant,
          purGroupCode: this.topInfo.buyerGroup
        }
        // serializeList: (list) => {
        //   list = [
        //     {
        //       id: "1",
        //       pendingPurchaseRequestNo: "10",
        //       pendingPurRequestCode: "PR2022030100001",
        //     },
        //   ];
        //   return list;
        // },
      })
      this.$refs.dialog.ejsRef.show()
    }
  }
}
</script>
