<template>
  <div class="full-height">
    <mt-template-page
      v-if="entryId !== '0' && entryId"
      ref="templateRef"
      :template-config="pageConfig"
    >
    </mt-template-page>
    <span v-else></span>
  </div>
</template>
<script>
import { operationLog } from '../config/operationLog.js'
import { BASE_TENANT } from '@/utils/constant'
export default {
  props: {
    entryId: {
      type: String,
      default: '0'
    }
  },
  data() {
    const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))

    return {
      userInfo, // 用户信息
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          grid: {
            columnData: operationLog,
            dataSource: [],
            // frozenColumns: 1,
            allowPaging: true,
            asyncConfig: {
              //   url: "/srm-purchase-execute/tenant/log/operationLog",
              //   params: {
              //     logOperationType: "BuyerOrder",
              //     objectId: this.entryId,
              //   },
            }
          }
        }
      ]
    }
  },
  watch: {
    entryId: {
      handler(val) {
        if (val !== '0' && val) {
          this.$set(this.pageConfig[0].grid, 'asyncConfig', {
            url: `${BASE_TENANT}/log/operationLog`,
            params: {
              logOperationType: 'BuyerOrder',
              objectId: val,
              tenantId: this.userInfo.tenantId
            }
          })
        }
      },
      immediate: true
    }
  },
  mounted() {},
  methods: {}
}
</script>
