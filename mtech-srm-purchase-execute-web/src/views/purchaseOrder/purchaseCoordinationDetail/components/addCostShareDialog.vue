<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="costCenterName" :label="$t('成本中心')">
        <mt-input
          v-model="addForm.costCenterName"
          :show-clear-button="true"
          :placeholder="$t('请输入成本中心')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="percentage" :label="$t('分摊比例')">
        <mt-input-number
          v-model="addForm.percentage"
          :show-clear-button="true"
          :placeholder="$t('请输入分摊比例')"
        ></mt-input-number>
      </mt-form-item>

      <mt-form-item prop="remark" :label="$t('分摊说明')" class="full-width">
        <mt-input
          v-model="addForm.remark"
          :show-clear-button="true"
          :multiline="true"
          :placeholder="$t('请输入分摊说明')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
// import common from "@/utils/constant";
// import { formatDate, formatRules } from "@/utils/util";
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        costCenterName: '',
        percentage: null,
        remark: ''
      },
      rules: {
        costCenterName: [
          {
            required: true,
            message: this.$t('请输入成本中心'),
            trigger: 'blur'
          }
        ],
        percentage: [
          {
            required: true,
            message: this.$t('请输入分摊比例'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      let _addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
      this.addForm = _addForm
    }
    if (this.dialogData?.dialogType == 'add') {
      this.dialogTitle = this.$t('新增费用分摊')
    } else {
      this.dialogTitle = this.$t('编辑费用分摊')
    }
    // this.getRules();
  },
  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // this.$toast({ content: "操作成功", type: "success" });
          this.$emit('handleAddDialogShow', false)
          this.$emit('confirmSuccess', this.addForm)
        }
      })
    },
    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },
    getRules() {
      if (this.dialogData?.dialogType == 'add') {
        // this.$API.baseMainData.getAddRulesExchangeRate().then((res) => {
        //   if (res.code == 200) this.rules = formatRules(res.data);
        //   this.rules.effectiveTime = [
        //     { required: true, message: "请选择有效时间", trigger: "blur" },
        //   ];
        // });
      } else {
        // this.$API.baseMainData.getUpdateRulesExchangeRate().then((res) => {
        //   if (res.code == 200) this.rules = formatRules(res.data);
        //   this.rules.effectiveTime = [
        //     { required: true, message: "请选择有效时间", trigger: "blur" },
        //   ];
        // });
      }
    }
  }
}
</script>

<style></style>
