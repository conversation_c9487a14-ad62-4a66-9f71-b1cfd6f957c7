<template>
  <div :class="['pt20', 'grid-wrap']">
    <div
      v-if="(entryType === '1' && entrySource !== '0') || (entryType === '2' && entryDraft == '1')"
      class="switch-wrap"
    >
      <span>{{ $t('整单分摊') }} </span>
      <mt-switch v-model="openSwitch" @change="handleSwitch"></mt-switch>
    </div>
    <div v-if="entrySource == '0' || entrySource == '4'" class="tip">
      {{ $t('提示：从采购申请转化的采购订单无法进行整单分摊') }}
    </div>
    <div
      v-if="
        !openSwitch &&
        (entryType === '3' || (entryType === '2' && entryDraft != '1')) &&
        (entrySource !== '0' || entrySource !== '4')
      "
      class="tip"
    >
      {{ $t('提示：未启用整单分摊') }}
    </div>
    <mt-template-page
      v-show="openSwitch"
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    >
    </mt-template-page>
    <add-cost-share-dialog
      v-if="addDialogShow"
      :dialog-data="dialogData"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></add-cost-share-dialog>
  </div>
</template>

<script>
import { costShareColumn } from '../config/costShare.js'
export default {
  components: {
    addCostShareDialog: require('./addCostShareDialog.vue').default
  },
  props: {
    entryDraft: {
      type: String,
      default: '1'
    },
    moduleKey: {
      type: String,
      default: ''
    },
    entryId: {
      type: String,
      default: '0'
    },
    entryCosts: {
      type: Array,
      default: () => []
    },
    entryType: {
      type: String,
      default: '1'
    },
    entrySource: {
      type: String,
      default: '1'
    },
    entryCostSwitch: {
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      openSwitch: false,
      pageConfig: [
        {
          toolbar: ['Add', 'Delete'],
          grid: {
            columnData: costShareColumn,
            dataSource: [],
            allowPaging: false
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },
  watch: {
    entryCostSwitch: {
      handler(val) {
        //1 未开启 ,0开启
        if (val === '1') {
          this.openSwitch = false
        }
        if (val === '0') {
          this.openSwitch = true
        }
      },
      immediate: true
    },
    openSwitch: {
      handler() {
        this.updateCosts()
      },
      immediate: true
    },
    entryCosts: {
      handler(val) {
        this.pageConfig[0].grid.dataSource = val
      },
      immediate: true
    },
    entryType: {
      handler() {
        if (this.entryType === '1' || this.entryType === '2') {
          this.pageConfig[0].toolbar = ['Add', 'Delete']
        }
        if (this.entryType === '3') {
          this.pageConfig[0].toolbar = []
        }
      },
      immediate: true
    }
  },
  methods: {
    handleSwitch(e) {
      if (e) {
        this.$dialog({
          data: {
            title: this.$t('提醒'),
            message: this.$t('启用整单分摊后，将清空需求明细中维护的成本中心')
          },
          success: () => {
            this.$bus.$emit('openWhole1', true)
          },
          close: () => {
            this.openSwitch = false
            this.$bus.$emit('openWhole1', false)
          }
        })
      } else {
        this.$bus.$emit('openWhole1', false)
      }
    },
    handleClickToolBar(e) {
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        if (e.grid.getSelectedRecords().length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        } else {
          // 传index
          this.handleClose(e.grid.getSelectedRowIndexes())
        }
      }
    },
    handleAdd() {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'add'
      }
    },
    handleEdit(e) {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'edit',
        row: e.data
      }
    },
    handleClose(indexs) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('确认删除该成本中心？')
        },
        success: () => {
          indexs.forEach((item) => {
            this.pageConfig[0].grid.dataSource.splice(item, 1)
          })
          this.updateCosts()
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
        }
      })
    },
    updateCosts() {
      let sendList = []
      this.pageConfig[0].grid.dataSource.forEach((item) => {
        sendList.push({
          allocationMode: '0',
          abolished: 0,
          costCenterCode: item.costCenterCode || '1',
          costCenterId: item.costCenterId || '1',
          costCenterName: item.costCenterName,
          id: item.id || 0,
          orderDetailId: 0,
          orderId: this.entryId,
          percentage: item.percentage,
          tenantId: item.tenantId,
          version: item.version,
          remark: item.remark
        })
      })
      this.$emit('updateCosts', sendList, this.openSwitch)
    },
    confirmSuccess(row) {
      row.index = this.pageConfig[0].grid.dataSource.length + 1
      this.pageConfig[0].grid.dataSource.push(row)
      this.updateCosts()
      // this.$refs[`template-${this.currentTabIndex}`].refreshCurrentGridData();
    },
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    }
  }
}
</script>

<style lang="scss" scoped>
.pt20 {
  padding-top: 0;
  flex: 1;
  .switch-wrap {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background: #fff;
    padding: 20px 20px 10px 0;

    span {
      margin-right: 14px;
    }
  }
  .tip {
    box-sizing: border-box;
    padding: 20px;
  }
  display: flex;
  flex-direction: column;
  /deep/ #Grid2_toolbarItems {
    border-top: none;
  }
}
</style>
