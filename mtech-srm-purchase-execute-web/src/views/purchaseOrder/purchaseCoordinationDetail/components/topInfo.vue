<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="itemcon mr20">
        <span class="infos" style="margin-right: 10px">{{ $t('加急') }}</span>
        <mt-switch
          class="item"
          v-model="expedited"
          active-value="1"
          inactive-value="2"
          @change="expeditedChange"
        ></mt-switch>
      </div>

      <div class="infos mr20">
        {{ $t('单据编号：') }}{{ topInfo.orderCode ? topInfo.orderCode : '-' }}
      </div>
      <div class="infos mr20" style="color: #9a9a9a">
        {{ $t('创建人：') }}
        {{ topInfo.createUserName ? topInfo.createUserName : '-' }}
      </div>
      <div class="infos" style="color: #9a9a9a">{{ $t('创建时间：') }}{{ time() }}</div>
      <div class="middle-blank"></div>
      <!-- 右侧各种操作按钮 -->
      <mt-button
        css-class="e-flat invite-btn"
        :is-primary="true"
        @click="goBack"
        v-if="entryType === '1' || entryType === '2' || entryType === '3'"
        >{{ $t('返回') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-if="
          (entryType === '1' && entrySource === '1') ||
          (entryType === '2' && entrySource === '1' && entryDraft === '1')
        "
        @click="save"
        >{{ $t('保存草稿') }}</mt-button
      >
      <!-- <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-if="entryType === '1' && entrySource === '4' && isFC"
        @click="publish"
        >{{ $t('发布') }}</mt-button
      >
      <template v-else>
        <mt-button
          css-class="e-flat"
          :is-primary="true"
          v-if="entryType === '1' || entryType === '2'"
          @click="submit"
          >{{ $t('提交') }}</mt-button
        >
      </template> -->
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-if="entryType === '1' || entryType === '2'"
        @click="submit"
        >{{ $t('提交') }}</mt-button
      >
      <div class="sort-box" @click="expandChange">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>
    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="topInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="businessTypeName" :label="$t('业务类型')" label-style="top">
          <mt-input
            v-model="topInfo.businessTypeName"
            :disabled="true"
            v-if="entryType === '3'"
          ></mt-input>
          <mt-select
            :disabled="
              !(
                (entryType === '1' && entrySource === '1') ||
                (entryType === '1' && entrySource === '2') ||
                (entryType === '2' && entryDraft === '1')
              )
            "
            :data-source="businessTypeOptions"
            v-model="topInfo.businessTypeName"
            :show-clear-button="false"
            :placeholder="$t('请选择业务类型')"
            :open-dispatch-change="false"
            :allow-filtering="true"
            @change="businessTypeNameChange"
            v-if="entryType === '2' || entryType === '1'"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="orderTypeCode" :label="$t('订单类型')" label-style="top">
          <mt-select
            :disabled="(entryType === '2' && entryDraft !== '1') || entryType === '3'"
            v-model="topInfo.orderTypeCode"
            :data-source="orderTypeOptions"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
            v-if="entryType === '2' || entryType === '1'"
          ></mt-select>
          <mt-input
            v-model="topInfo.orderTypeName"
            :disabled="true"
            v-if="entryType === '3'"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="company" :label="$t('公司')" label-style="top">
          <mt-select
            :popup-width="350"
            :show-clear-button="false"
            ref="companyRef"
            v-model="topInfo.company"
            id="companyTree"
            :data-source="companyOptions"
            :fields="{ text: 'label', value: 'id' }"
            :allow-filtering="true"
            :filtering="getCompany"
            :open-dispatch-change="false"
            :placeholder="$t('请选择公司')"
            @change="handleCompanyChange"
            v-if="
              (entryType === '1' &&
                !(
                  entryType === '1' &&
                  (entrySource === '0' || entrySource === '4' || entrySource === '2')
                )) ||
              (entryType === '2' && entryDraft === '1')
            "
          ></mt-select>
          <mt-input
            disabled
            v-if="
              (entryType === '2' && entryDraft !== '1') ||
              entryType === '3' ||
              (entryType === '1' &&
                (entrySource === '0' || entrySource === '4' || entrySource === '2'))
            "
            v-model="topInfo.companyName1"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="purtenant" :label="$t('供应商')" label-style="top">
          <mt-select
            v-if="entryType === '2' || entryType === '1'"
            :disabled="
              (entryType === '2' && entryDraft !== '1') ||
              entryType === '3' ||
              (entryType === '1' && entrySource === '4') ||
              (entryType === '1' && entrySource === '2')
            "
            allow-filtering="true"
            v-model="topInfo.purtenant"
            :data-source="purtenantOptions"
            :fields="{ text: 'label', value: 'value' }"
            :show-clear-button="false"
            :placeholder="$t('请选择供应商')"
            :open-dispatch-change="false"
            @change="handlePurtenantChange"
            :filtering="supplierPagedQuery"
          ></mt-select>
          <mt-input
            v-model="topInfo.supplierName1"
            :disabled="true"
            v-if="entryType === '3'"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="payment" :label="$t('付款条件')" label-style="top">
          <mt-input
            v-model="topInfo.paymentName"
            :disabled="true"
            v-if="entryType === '3'"
          ></mt-input>
          <mt-select
            v-if="entryType === '2' || entryType === '1'"
            :allow-filtering="true"
            :disabled="(entryType === '2' && entryDraft !== '1') || entryType === '3'"
            v-model="topInfo.payment"
            :data-source="paymentOptions"
            :show-clear-button="false"
            :placeholder="$t('请选择付款条件')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="buyerUserName" :label="$t('采购员')" label-style="top">
          <!-- <debounce-filter-select
            :allow-filtering="true"
            :open-dispatch-change="false"
            ref="userRef"
            v-model="topInfo.buyerUser"
            :data-source="buyerUserOptions"
            :fields="{ text: 'text', value: 'value' }"
            :request="getCurrentTenantEmployees"
            :show-clear-button="false"
            :placeholder="$t('请选择采购员')"
            :disabled="true"
            @change="handleUserChange"
          ></debounce-filter-select> -->
          <mt-input v-model="topInfo.buyerUserName" :disabled="true"></mt-input>
          <!-- :disabled="
              (entryType === '2' && entryDraft !== '1') || entryType === '3'
            " -->
        </mt-form-item>
        <mt-form-item prop="buyerOrg" :label="$t('采购组织')" label-style="top">
          <mt-input
            v-model="topInfo.buyerOrgName2"
            :disabled="true"
            v-if="entryType === '3'"
          ></mt-input>
          <mt-select
            v-if="entryType === '2' || entryType === '1'"
            :show-clear-button="false"
            :disabled="(entryType === '2' && entryDraft !== '1') || entryType === '3'"
            ref="buyerOrgRef"
            v-model="topInfo.buyerOrg"
            id="organizationTree"
            :popup-height="400"
            :data-source="buyerOrgOptions"
            :allow-filtering="true"
            :placeholder="$t('请选择采购组织')"
            :filtering="getGroup"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="buyerGroup" :label="$t('采购组')" label-style="top">
          <mt-input
            v-model="topInfo.buyerGroupName1"
            :disabled="true"
            v-if="entryType === '3'"
          ></mt-input>
          <mt-select
            v-if="entryType === '2' || entryType === '1'"
            :allow-filtering="true"
            :disabled="
              (entryType === '2' && entryDraft !== '1') ||
              entryType === '3' ||
              (entryType === '1' && entrySource === '4')
            "
            :fields="{ text: 'label', value: 'value' }"
            v-model="topInfo.buyerGroup"
            :data-source="buyerGroupOptions"
            :show-clear-button="false"
            :placeholder="$t('请选择采购组')"
            @open="startOpen"
            :filtering="getbussinessGroup"
          ></mt-select>
        </mt-form-item>
        <!-- <mt-form-item
          prop="requiredDeliveryDate"
          :label="$t('要求交期')"
          v-if="entryType === '3'"
        >
          <mt-date-picker
            :disabled="true"
            :placeholder="$t('选择要求交期')"
            v-model="topInfo.requiredDeliveryDate"
          ></mt-date-picker>
        </mt-form-item> -->
        <mt-form-item
          v-if="entryType === '1' && entrySource === '4' && isFC"
          prop="currencyName1"
          :label="$t('订单币种')"
          label-style="top"
        >
          <mt-select
            :allow-filtering="true"
            :disabled="true"
            v-model="topInfo.currencyId"
            :data-source="currencyOptions"
            :fields="{ text: 'label', value: 'value' }"
            :show-clear-button="true"
            :filtering="getCurrency"
            :placeholder="$t('请选择订单币种')"
            @open="startOpen1"
          ></mt-select>
        </mt-form-item>
        <mt-form-item v-else prop="currency" :label="$t('订单币种')" label-style="top">
          <mt-input
            v-model="topInfo.currencyName1"
            :disabled="true"
            v-if="
              entryType === '3' || (entryType === '2' && entrySource === '4' && entryDraft === '2')
            "
          ></mt-input>
          <mt-select
            v-if="(entryType === '2' || entryType === '1') && entryDraft !== '2'"
            :allow-filtering="true"
            :disabled="(entryType === '2' && entryDraft !== '1') || entryType === '3'"
            v-model="topInfo.currency"
            :data-source="currencyOptions"
            :fields="{ text: 'label', value: 'value' }"
            :show-clear-button="true"
            :filtering="getCurrency"
            :placeholder="$t('请选择订单币种')"
            @open="startOpen1"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          prop="taxTotal"
          :label="$t('含税总金额')"
          v-if="entryType === '3' && topInfo.taxTotal === -1"
          label-style="top"
        >
          <mt-input v-model="topInfo.taxTotal" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="freeTotal"
          :label="$t('未税总金额')"
          v-if="entryType === '3' && topInfo.taxTotal === -1"
          label-style="top"
        >
          <mt-input v-model="topInfo.freeTotal" :disabled="true"></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="settlement" :label="$t('结算方')">
          <mt-select
            :disabled="
              (entryType === '2' && entryDraft !== '1') || entryType === '3'
            "
            v-model="topInfo.settlement"
            :data-source="settlementOptions"
            :show-clear-button="true"
            :placeholder="$t('请选择结算方')"
          ></mt-select>
        </mt-form-item> -->
        <!-- <mt-form-item prop="orderRel" :label="$t('关联采购订单')">
          <mt-select
            :disabled="
              (entryType === '2' && entryDraft !== '1') || entryType === '3'
            "
            v-model="topInfo.orderRel"
            allow-filtering="true"
            :filtering="purOrderQueryOrder"
            :data-source="orderRelOptions"
            :placeholder="$t('请选择关联采购订单号')"
          ></mt-select>
        </mt-form-item> -->
        <mt-form-item prop="urgentTime1" :label="$t('加急日期')" label-style="top">
          <mt-input v-model="topInfo.urgentTime1" disabled></mt-input>
        </mt-form-item>
        <mt-form-item prop="invoiceId" :label="$t('发票信息')" label-style="top">
          <mt-select
            :disabled="(entryType === '2' && entryDraft !== '1') || entryType === '3'"
            v-model="topInfo.invoiceId"
            :data-source="invoiceIdOptions"
            :show-clear-button="true"
            :placeholder="$t('请选择发票信息')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="supSalesOrderNo" :label="$t('供方销售订单号')" label-style="top">
          <mt-input v-model="topInfo.supSalesOrderNo" :disabled="entryType === '3'" />
        </mt-form-item>
        <mt-form-item prop="syncSupSysStatus" :label="$t('同步供方系统状态')" label-style="top">
          <mt-select
            v-model="topInfo.syncSupSysStatus"
            :data-source="syncSupSysStatusOptions"
            :show-clear-button="true"
            :disabled="entryType === '3'"
          />
        </mt-form-item>
        <mt-form-item
          prop="syncSupSysFailDesc"
          :label="$t('同步供方系统失败原因')"
          class="half-width"
          label-style="top"
        >
          <mt-input v-model="topInfo.syncSupSysFailDesc" :disabled="entryType === '3'" />
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('采方备注')" class="half-width" label-style="top">
          <mt-input
            v-model="topInfo.remark"
            :disabled="entryType === '3'"
            v-if="maxlength1"
            :maxlength="maxlength1"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="supRemark" :label="$t('供方备注')" label-style="top">
          <mt-input v-model="topInfo.supRemark" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="ihrez" :label="$t('您的参考')" label-style="top">
          <mt-input v-model="topInfo.ihrez" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="unsez" :label="$t('我们的参考')" label-style="top">
          <mt-input v-model="topInfo.unsez" :disabled="true"></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import UTILS from '@/utils/utils'
import { utils } from '@mtech-common/utils'
export default {
  components: {
    // DebounceFilterSelect: () => import("@/components/debounceFilterSelect"),
  },
  props: {
    fromPath: {
      type: String,
      default: ''
    },
    entryType: {
      type: String,
      default: '1'
    },
    topInfo: {
      type: Object,
      default: () => {}
    },
    entryId: {
      type: String,
      default: '0'
    },
    costSwitch: {
      type: String,
      default: '1'
    },
    entrySource: {
      type: String,
      default: '1'
    },
    acceptanceUse: {
      type: String,
      default: '0'
    },
    entryDraft: {
      type: String,
      default: '1'
    }
  },
  data() {
    const driverPhoneValidator = (rule, value, callback) => {
      if (!this.topInfo.purtenant) {
        callback(new Error(this.$t('请选择供应商')))
      } else {
        this.$refs.ruleForm.clearValidate(['purtenant'])
        callback()
      }
    }
    return {
      isFC: false, // 判断是否为非采
      maxlength1: 200,
      isExpand: true,
      rules: {
        orderTypeCode: [
          {
            required: true,
            message: this.$t('请选择订单类型'),
            trigger: 'blur'
          }
        ],
        businessTypeName: [
          {
            required: true,
            message: this.$t('请输入业务类型'),
            trigger: 'blur'
          }
        ],
        purtenant: [
          {
            required: true,
            validator: driverPhoneValidator,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          }
        ],
        payment: [
          {
            required: true,
            message: this.$t('请选择付款条件'),
            trigger: 'blur'
          }
        ],
        buyerUserName: [{ required: true, message: this.$t('请选择采购员'), trigger: 'blur' }],
        company: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        buyerOrg: [
          {
            required: true,
            message: this.$t('请选择采购组织'),
            trigger: 'blur'
          }
        ],
        buyerGroup: [{ required: true, message: this.$t('请选择采购组'), trigger: 'blur' }],
        requiredDeliveryDate: [
          { required: false, message: this.$t('请选择要求交期'), trigger: 'blur' }
        ],
        currency: [
          {
            required: true,
            message: this.$t('请选择订单币种'),
            trigger: 'blur'
          }
        ],
        taxTotal: [{ required: false, message: this.$t('请输入含税总金额'), trigger: 'blur' }],
        freeTotal: [{ required: false, message: this.$t('请输入未税总金额'), trigger: 'blur' }],
        // settlement: [
        //   { required: true, message: this.$t("请选择结算方"), trigger: "blur" },
        // ],
        invoiceId: [{ required: false, message: this.$t('请选择发票信息'), trigger: 'blur' }]
      },
      orderTypeOptions: [], //订单类型
      businessTypeOptions: [], //业务类型
      purtenantOptions: [], //供应商
      paymentOptions: [], //付款条件
      buyerUserOptions: [], //采购员
      companyOptions: [], //公司
      buyerOrgOptions: [], //采购组织
      currencyOptions: [], //订单币种
      buyerGroupOptions: [], //采购组
      settlementOptions: [], //结算方
      orderRelOptions: [], //关联采购订单
      invoiceIdOptions: [
        //发票
        { text: this.$t('增值'), value: '1' },
        { text: this.$t('专票'), value: '2' }
      ],
      expedited: '2', //是否加急
      entryFirst: '1',
      entryFirst2: '1',
      entryFirst3: '1',
      entryFirst4: '1',
      entryFirst5: '1',
      entryFirst6: '1',
      syncSupSysStatusOptions: [
        { text: this.$t('成功'), value: 1 },
        { text: this.$t('失败'), value: 2 }
      ]
    }
  },
  watch: {
    topInfo: {
      handler(val) {
        // if (this.entryType === "3") {
        //   return;
        // }
        if (
          this.entryType === '2' ||
          this.entryType === '3' ||
          (this.entryType === '1' &&
            (this.entrySource === '0' || this.entrySource === '4' || this.entrySource === '2'))
        ) {
          if (val.urgentTime && val.urgentTime !== '0') {
            if (this.entryFirst3 === '1') {
              this.expedited = '1'
              this.topInfo.urgentTime1 = UTILS.dateFormat(Number(val.urgentTime), 'Y-m-d')
              this.entryFirst3 = '2'
            }
          }
          if (val.company) {
            //根据公司获取结算方 采购组织
            this.getParentCompanyOrgIncludeself()
            this.getGroup({ text: val?.buyerGroupCode }, '1')
            if (this.entryFirst5 === '1') {
              this.getCompany({
                text: val?.companyCode || val.companyInfo?.companyCode
              })
              this.entryFirst5 = '2'
            }
            if (this.entryFirst === '1') {
              this.supplierPagedQuery(
                '',
                val?.companyCode || val.companyInfo?.companyCode,
                val?.supplierCode || val?.purtenant
              )
            }
          }
          if (val.buyerGroup) {
            if (this.entryFirst4 === '1') {
              this.getbussinessGroup({ text: val.buyerGroup })
              this.entryFirst4 = '2'
            }
          }
          if (val.currencyName) {
            if (this.entryFirst6 === '1') {
              this.getCurrency({ text: val.currencyName })
              this.entryFirst6 = '2'
            }
          }
          if (val.buyerUserId) {
            // this.$API.masterData
            //   .getOrgCompanysByEmpId({ employeeId: val.buyerUserId })
            //   .then((res) => {
            //     this.companyOptions = res.data;
            //   });
          }
        }
        // if (
        //   this.entryType === "1" &&
        //   (this.entrySource === "0" || this.entrySource === "4")
        // ) {
        //   if (val.company) {
        //     // this.getCurrentTenantEmployees("", "1");
        //   }
        // }
        // if (val.buyerUserName) {
        //   this.getCurrentTenantEmployees({ text: val.buyerUserName }, "1");
        // }
        if (this.entryType === '1' && (this.entrySource === '1' || this.entrySource === '2')) {
          //默认登录人
          if (val.employeeId) {
            // this.getCompany(val.employeeId);
          }
        }
        if (this.entryType === '1' && this.entrySource === '4') {
          if (
            val.businessTypeCode === 'BTTCL001' ||
            val.businessTypeCode === 'BTTCL002' ||
            val.businessTypeCode === 'BTTCL003'
          ) {
            this.isFC = true
          }
        }
      },
      immediate: true,
      deep: true
    },
    entryType: {
      handler(val) {
        if (val === '1') {
          this.getCurrentTenantEmployees({ text: '' }, '0')
          this.getCompany()
          this.getCurrency()
        }
        if (val !== '3') {
          this.getOptions()
          this.supplierPagedQuery = utils.debounce(this.supplierPagedQuery, 1000)
          this.getbussinessGroup = utils.debounce(this.getbussinessGroup, 1000)
          this.getCompany = utils.debounce(this.getCompany, 1000)
          this.getCurrency = utils.debounce(this.getCurrency, 1000)
          this.getGroup = utils.debounce(this.getGroup, 1000)
          if (this.entryType === '1' && !this.topInfo.buyerGroup) {
            this.getbussinessGroup()
          }
        }
      },
      immediate: true
    }
  },
  mounted() {
    // if (this.entryType !== "3") {
    //   this.getOptions();
    //   this.supplierPagedQuery = utils.debounce(this.supplierPagedQuery, 1000);
    //   this.getbussinessGroup = utils.debounce(this.getbussinessGroup, 1000);
    //   this.getCompany = utils.debounce(this.getCompany, 1000);
    //   this.getCurrency = utils.debounce(this.getCurrency, 1000);
    //   if (this.entryType === "1" && !this.topInfo.buyerGroup) {
    //     this.getbussinessGroup();
    //   }
    // }
  },
  methods: {
    startOpen() {
      if (!this.buyerGroupOptions.length) {
        this.getbussinessGroup()
      }
    },
    startOpen1() {
      if (!this.currencyOptions.length) {
        this.getCurrency()
      }
    },
    getTenantPurOrder() {
      //重新获取加急时间
      this.$API.purchaseOrder.getTenantPurOrder(this.entryId).then((res) => {
        let urgentTime = res.data.urgentTime
        this.topInfo.urgentTime = urgentTime
        if (urgentTime && urgentTime !== '0') {
          this.topInfo.urgentTime1 = UTILS.dateFormat(Number(urgentTime.urgentTime), 'Y-m-d')
          this.expedited = '1'
        } else {
          this.topInfo.urgentTime1 = ''
          this.expedited = '2'
        }
      })
    },
    expeditedAllDetail() {
      //加急所有明细行
      this.$emit('expeditedAllDetail')
    },
    cancleExpeditedAllDetail() {
      //取消加急所有明细行
      this.$emit('cancleExpeditedAllDetail')
    },
    updateTopInfoUrgentime1(type) {
      if (type === '1') {
        this.topInfo.urgentTime1 = ''
        this.expedited = '2'
      }
      if (type === '2') {
        this.getTenantPurOrder()
      }
    },
    updateTopInfoUrgentime(type) {
      if (type === '1') {
        this.topInfo.urgentTime1 = UTILS.dateFormat(this.getToday().getTime(), 'Y-m-d')
        this.expedited = '1'
      }
      if (type === '2') {
        this.getTenantPurOrder()
      }
    },
    getToday() {
      var today = new Date()
      today.setHours(0)
      today.setMinutes(0)
      today.setSeconds(0)
      today.setMilliseconds(0)
      today = new Date(today.getTime() + 24 * 60 * 60 * 1000)
      return today
    },
    expeditedChange(e) {
      //加急或者取消加急
      if (e === '1') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认加急该订单吗？')
          },
          success: () => {
            if (this.entryType === '1') {
              this.topInfo.urgentTime1 = UTILS.dateFormat(this.getToday().getTime(), 'Y-m-d')
              this.expeditedAllDetail()
            }
            if (this.entryType === '2' || this.entryType === '3') {
              // let params = {
              //   ids: [this.entryId],
              // };
              let params = {
                type: 1,
                urgentList: [
                  {
                    ids: [],
                    itemNos: [],
                    orderCode: this.topInfo.orderCode
                  }
                ]
              }
              this.$API.purchaseOrder.purOrderUrgentOrder(params).then((res) => {
                if (res.data.length) {
                  let str = ''
                  res.data.forEach((item) => {
                    str = str + `${item.code} ${item.msg}`
                  })
                  this.$toast({ content: str, type: 'error' })
                  this.expedited = '2'
                } else {
                  this.expeditedAllDetail()
                  this.getTenantPurOrder()
                  this.$toast({
                    content: this.$t('加急订单操作成功'),
                    type: 'success'
                  })
                }
              })
            }
          },
          close: () => {
            this.expedited = '2'
          }
        })
      }
      if (e === '2') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认取消加急该订单吗？')
          },
          success: () => {
            if (this.entryType === '1') {
              this.topInfo.urgentTime1 = ''
              this.cancleExpeditedAllDetail()
            }
            if (this.entryType === '2' || this.entryType === '3') {
              // let params = {
              //   ids: [this.entryId],
              // };
              let params = {
                type: 0,
                urgentList: [
                  {
                    ids: [],
                    itemNos: [],
                    orderCode: this.topInfo.orderCode
                  }
                ]
              }
              this.$API.purchaseOrder.purOrderUrgentOrder(params).then((res) => {
                if (res.data.length) {
                  let str = ''
                  res.data.forEach((item) => {
                    str = str + `${item.code} ${item.msg}`
                  })
                  this.$toast({ content: str, type: 'error' })
                  this.expedited = '1'
                } else {
                  this.cancleExpeditedAllDetail()
                  this.getTenantPurOrder()
                  this.$toast({
                    content: this.$t('取消加急订单操作成功'),
                    type: 'success'
                  })
                }
              })
            }
          },
          close: () => {
            this.expedited = '1'
          }
        })
      }
    },
    time() {
      // return this.topInfo.orderTime
      //   ? UTILS.dateFormat(Number(this.topInfo.orderTime), "Y-m-d")
      //   : "-";
      let str = '-'
      if (this.topInfo.orderTime && this.topInfo.orderTime.length === 13) {
        str = UTILS.dateFormat(Number(this.topInfo.orderTime), 'Y-m-d')
      }
      return str
    },
    // 用户改变
    async handleUserChange(e) {
      if (
        (e.itemData != null &&
          this.entryType === '1' &&
          this.entrySource !== '0' &&
          this.entrySource !== '4') ||
        (this.entryType === '2' && this.entryDraft === '1' && e.itemData != null)
      ) {
        // this.companyOptions = [];
        // this.topInfo.company = "";
        // this.buyerOrgOptions = [];
        // this.topInfo.buyerOrg = "";
        // this.settlementOptions = [];
        // this.topInfo.settlementId = "";
        // await this.getCompany(e.itemData.id);
      }
    },
    // 根据员工ID查询公司
    async getCompany(val) {
      await this.$API.masterData
        .OrgFindSpecifiedChildrenLevelOrgs({
          fuzzyParam: val?.text || '',
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true,
          organizationIds: []
        })
        .then((res) => {
          let list = res.data || []
          list.forEach((item) => {
            item.label = `${item.orgCode}-${item.orgName}`
          })
          this.companyOptions = res.data
          // if (this.companyOptions && this.companyOptions.length == 1) {
          //   this.topInfo.company = this.companyOptions[0].id;
          //   this.getGroup();
          //   this.getParentCompanyOrgIncludeself();
          // }
          if (val?.updateData) {
            this.$nextTick(() => {
              val.updateData(this.companyOptions)
            })
          }
        })
    },
    //采购组织
    async getGroup(val, type) {
      if (!this.topInfo.company) {
        return
      }
      if (type === '1' && this.entryFirst2 === '2') return
      let params = {
        fuzzyParam: val?.text || '',
        orgId: this.topInfo.company
      }
      await this.$API.masterData.getOrganizateByOrgId(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.text = `${item.organizationCode}-${item.organizationName}`
          item.value = item.id
        })
        this.buyerOrgOptions = list
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.buyerOrgOptions)
          })
        }
        if (type === '1') {
          this.entryFirst2 = '2'
        }
      })
    },
    // 获取 商城转申请 的业务类型
    queryMallBusinessType() {
      this.$API.purchaseRequest
        .queryMallBusinessType()
        .then((res) => {
          this.topInfo.businessTypeName =
            res?.data?.businessTypeName || this.businessTypeOptions[0].text
          this.topInfo.businessTypeCode =
            res?.data?.businessTypeCode || this.businessTypeOptions[0].code
          this.topInfo.businessId = res?.data?.businessTypeId || this.businessTypeOptions[0].id
          this.$emit('queryRequestModelPo')
        })
        .catch(() => {
          if (this.businessTypeOptions.length) {
            this.topInfo.businessTypeName = this.businessTypeOptions[0].text
            this.topInfo.businessTypeCode = this.businessTypeOptions[0].code
            this.topInfo.businessId = this.businessTypeOptions[0].id
            this.$emit('queryRequestModelPo')
          }
        })
    },
    purOrderQueryOrder(val) {
      //模糊查询订单号
      let params = {
        condition: 'and',
        page: {
          current: 1,
          size: 50
        },
        defaultRules: [
          {
            label: this.$t('采购订单号'),
            field: 'orderCode',
            type: 'string',
            operator: 'contains',
            value: val && val.text ? val.text : ''
          }
        ]
      }
      this.$API.purchaseOrder.purOrderQueryOrder(params).then((res) => {
        let orderRelOptions = res.data.records.map((item) => {
          return {
            text: item,
            value: item
          }
        })
        orderRelOptions = orderRelOptions.filter((item) => {
          return item.text
        })
        this.orderRelOptions = orderRelOptions
      })
    },
    getbussinessGroup(val) {
      let str = val?.text || ''
      let params = {
        fuzzyParam: str,
        groupTypeCode: 'BG001CG'
      }
      this.$API.masterData.getbussinessGroup(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.groupCode}-${item.groupName}`
          item.text = item.groupName
          item.value = item.groupCode
          item.text = item.groupName
        })
        this.buyerGroupOptions = list
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.buyerGroupOptions)
          })
        }
      })
    },
    async supplierPagedQuery(supplierName, code, supplierCode) {
      //供应商
      if (!code && !this.topInfo.companyCode && !this.topInfo.companyInfo?.companyCode) {
        return
      }
      let params = {
        organizationCode: code || this.topInfo.companyCode || this.topInfo.companyInfo?.companyCode,
        fuzzyNameOrCode: supplierName?.text || ''
      }
      if (this.entryFirst == '1') {
        this.entryFirst = '2'
        if (supplierCode) {
          params.supplierCode = supplierCode
        }
      }
      if (this.entrySource == '4') {
        if (!params.supplierCode) {
          return
        }
      }
      await this.$API.masterData.getSupplier(params).then((res) => {
        // if (res && res.data?.length) {
        //   this.purtenantOptions = res.data.map((item) => {
        //     return {
        //       text: item.supplierName,
        //       value: item.supplierCode,
        //       label: `${item.supplierCode}-${item.supplierName}`,
        //       id: item.id,
        //     };
        //   });
        // }
        let list = res.data || []
        list.forEach((item) => {
          item.text = item.supplierName
          item.value = item.supplierCode
          item.label = `${item.supplierCode}-${item.supplierName}`
        })
        this.purtenantOptions = list
      })
      if (supplierName.updateData) {
        supplierName.updateData(this.purtenantOptions)
      }
    },
    getOptions() {
      //查询公司
      //模糊查询订单号
      // this.purOrderQueryOrder();
      //订单类型下拉
      this.$API.masterData.getDictCode({ dictCode: 'OrderType' }).then((res) => {
        this.orderTypeOptions = res.data || []
      })
      // 获取业务类型下拉
      this.$API.masterData.getDictCode({ dictCode: 'businessType' }).then((res) => {
        if (res.data && res.data.length) {
          this.businessTypeList = []
          res.data.map((item) => {
            this.businessTypeOptions.push({
              value: item.itemName,
              text: item.itemName,
              code: item.itemCode,
              id: item.id
            })
          })
          if (this.entryType === '1' && this.entrySource === '2') {
            this.queryMallBusinessType()
          }
        }
      })
      //供应商
      // this.$API.masterData.getSupplier().then((res) => {
      //   if (res && res.data.length) {
      //     this.purtenantOptions = res.data.map((item) => {
      //       return {
      //         text: item.supplierName,
      //         value: item.supplierCode,
      //         id: item.id,
      //       };
      //     });
      //   }
      // });
      // 付款条件
      this.$API.masterData.paymentTerms().then((res) => {
        if (res.data && res.data.length) {
          this.paymentOptions = res.data.map((item) => {
            return {
              text: item.paymentTermsName,
              value: item.paymentTermsCode
            }
          })
        }
      })
      // if (this.entryType === "1") {
      //   this.getCurrentTenantEmployees({ text: "" }, "0");
      // }
    },
    getCurrentTenantEmployees(e, type) {
      //查询采购员
      let params = null
      if (type === '1') {
        params = {
          // organizationId: this.topInfo.company,
          fuzzyName: e.text
        }
        if (this.entryFirst1 === '2') return
      } else {
        params = {
          fuzzyName: e.text
        }
        if (this.entryType === '1' && this.entrySource === '4') {
          let buyerUserInfo = JSON.parse(sessionStorage.getItem('buyerUserInfo') || '{}')
          params = {
            // organizationId: this.topInfo.company,
            fuzzyName: buyerUserInfo.buyerUserName
          }
        }
      }
      this.$API.masterData.getCurrentTenantEmployees(params).then((res) => {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`,
            value: item.accountName,
            id: item.uid,
            name: item.employeeName
          })
        })
        if (type === '1') {
          this.entryFirst1 = '2'
        }
        if (tmp.length === 1) {
          this.topInfo.buyerUserName = tmp[0].name
          this.topInfo.buyerUserCode = tmp[0].value
          this.topInfo.buyerUserId = tmp[0].id
        }
        this.buyerUserOptions = tmp
      })
    },
    //供应商改变的时候
    handlePurtenantChange(e) {
      if (this.entryType === '1' || (this.entryType === '2' && this.entryDraft === '1')) {
        this.$emit('updatePurtenantLinkData', e.itemData.value)
      }
      sessionStorage.setItem('supplierId', e.itemData.id)
      sessionStorage.setItem('supplierCode', e.itemData.value)
    },
    //公司选择改变的时候
    handleCompanyChange(e) {
      if (
        (e.itemData != null && this.entryType === '1') ||
        (this.entryType === '2' && this.entryDraft === '1' && e.itemData != null)
      ) {
        this.topInfo.company = e.itemData.id
        this.topInfo.companyCode = e.itemData.orgCode
        this.topInfo.purtenant = ''
        this.purtenantOptions = []
        this.supplierPagedQuery('', e.itemData.orgCode)
        this.buyerOrgOptions = []
        this.topInfo.buyerOrg = ''
        this.settlementOptions = []
        this.topInfo.settlementId = ''
        this.getGroup()
        this.$emit('updateCompanyLinkData')
        this.getParentCompanyOrgIncludeself()
        this.getDefaultCurrency()
        this.$store.commit('updateCompanyId', e.itemData.id)
        this.$store.commit('updateCompanyCode', e.itemData.orgCode)
      }
    },
    getCurrency(val, currencyCode) {
      // 订单币种
      let params = { fuzzyParam: val?.text || '' }
      this.$API.masterData.getCurrencyByFilter(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.currencyCode}-${item.currencyName}`
          item.text = item.currencyName
          item.value = item.id
        })
        this.currencyOptions = list
        if (currencyCode) {
          this.topInfo.currency = currencyCode
        }
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.currencyOptions)
          })
        }
      })
    },
    getDefaultCurrency() {
      //获取默认的币种
      if (!this.topInfo.company) {
        return
      }
      let params = { orgId: this.topInfo.company }
      this.$API.masterData.getStandardCurrency(params).then((res) => {
        if (res.data?.currencyCode) {
          // this.topInfo.currency = res.data?.currencyCode;
          this.currencyOptions = []
          this.getCurrency({ text: res.data?.currencyCode }, res.data?.currencyCode)
        }
      })
    },
    getParentCompanyOrgIncludeself() {
      // //获取结算方数据
      // if (!this.topInfo.company) {
      //   this.settlementOptions = [];
      //   this.topInfo.settlement = "";
      //   return;
      // }
      // let params = { organizationId: this.topInfo.company };
      // this.$API.masterData
      //   .getParentCompanyOrgIncludeself(params)
      //   .then((res) => {
      //     this.settlementOptions = res.data.map((item) => {
      //       return {
      //         text: item.orgName,
      //         value: item.id,
      //       };
      //     });
      //   });
    },
    expandChange() {
      this.isExpand = !this.isExpand
      this.$refs.ruleForm.clearValidate()
    },
    goBack() {
      // console.log(this.fromPath, '我是进来的URL')
      // if (this.fromPath == '/' || this.fromPath.includes('login')) {
      //   this.$router.push({
      //     name: 'purchase-coordination'
      //   })
      // } else {
      //   this.$router.go(-1)
      // }
      this.$router.go(-1)
    },
    save() {
      // this.$refs.ruleForm.validate((valid) => {
      //   if (!valid) {
      //     this.$toast({ content: this.$t("请先保存订单信息"), type: "error" });
      //     return;
      //   }
      this.$emit('save', this.getParams())
      // });
    },
    submit() {
      this.$refs.ruleForm.validate(() => {
        for (const key in this.rules) {
          const item = this.rules[key]
          if (item[0]['required'] === true && !this.topInfo[key]) {
            if (!(this.entryType === '1' && this.entrySource === '4' && key === 'currency')) {
              this.$toast({ content: item[0]['message'], type: 'error' })
              return
            }
          }
        }
        // if (!valid) {
        //   this.$toast({ content: this.$t('请先保存订单信息'), type: 'error' })
        //   return
        // }
        this.$emit('submit', this.getParams())
      })
    },
    publish() {
      this.$refs.ruleForm.validate(() => {
        for (const key in this.rules) {
          const item = this.rules[key]
          if (item[0]['required'] === true && !this.topInfo[key]) {
            if (!(this.entryType === '1' && this.entrySource === '4' && key === 'currency')) {
              this.$toast({ content: item[0]['message'], type: 'error' })
              return
            }
          }
        }
        // if (!valid) {
        //   this.$toast({ content: this.$t('请先保存订单信息'), type: 'error' })
        //   return
        // }
        this.$emit('publish', this.getParams())
      })
    },
    checkTopInfo() {
      //明细新增的时候先校验
      return new Promise((resove) => {
        let validateList = []
        this.$refs['ruleForm'].validateField(
          ['businessTypeName', 'purtenant', 'company'],
          (message) => {
            validateList.push(message)
          }
        )
        if (validateList.every((item) => item === true)) {
          resove('1')
        } else {
          this.$toast({
            content: this.$t('请先保存业务类型 供应商 公司'),
            type: 'error'
          })
          resove('0')
        }
      })
    },
    businessTypeNameChange(e) {
      sessionStorage.setItem(
        'businessInfo',
        JSON.stringify({
          businessTypeCode: e.itemData.code || this.topInfo.businessTypeCode,
          businessId: e.itemData.id || this.topInfo.businessId,
          orderCode: this.topInfo.orderCode
        })
      )
      if (
        (this.entryType === '1' && this.entrySource === '1') ||
        (this.entryType === '1' && this.entrySource === '2') ||
        (this.entryType === '2' && this.entryDraft === '1')
      ) {
        if (e.previousItemData) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('更换业务类型将重新清空/获取行数据，请确定是否继续？')
            },
            success: () => {
              this.topInfo.businessTypeCode = e.itemData.code
              this.topInfo.businessId = e.itemData.id
              this.$emit('businessTypeNameChange')
            },
            close: () => {
              this.topInfo.businessTypeCode = e.previousItemData.code
              this.topInfo.businessId = e.previousItemData.id
              this.topInfo.businessTypeName = e.previousItemData.text
            }
          })
        } else if (!e.previousItemData && e.itemData) {
          //第一次进入
          this.topInfo.businessTypeCode = e.itemData.code
          this.topInfo.businessId = e.itemData.id
          this.$emit('businessTypeNameChange')
        }
      }
    },
    getParams() {
      let companyInfo = {}
      if (this.entryType === '2') {
        if (this.entryDraft === '1') {
          companyInfo = {
            companyId: this.topInfo.company,
            companyName: this.companyOptions.find((item) => {
              return item.id == this.topInfo.company
            })?.orgName,
            companyCode: this.companyOptions.find((item) => {
              return item.id == this.topInfo.company
            })?.orgCode
          }
        }
        if (this.entryDraft !== '1') {
          companyInfo = this.topInfo.companyInfo
        }
      }
      if (this.entryType === '1') {
        if (this.entrySource === '0' || this.entrySource === '4') {
          companyInfo = this.topInfo.companyInfo
        }
        if (this.entrySource === '1' || this.entrySource === '2') {
          companyInfo = {
            companyId: this.topInfo.company,
            companyName: this.companyOptions.find((item) => {
              return item.id == this.topInfo.company
            })?.orgName,
            companyCode: this.companyOptions.find((item) => {
              return item.id == this.topInfo.company
            })?.orgCode
          }
        }
      }
      let params = {
        orderTypeCode: this.topInfo.orderTypeCode, //订单类型
        orderTypeName: this.orderTypeOptions.find((item) => {
          return item.itemCode == this.topInfo.orderTypeCode
        })?.itemName, //订单类型
        businessTypeCode: this.topInfo.businessTypeCode, //业务类型
        businessTypeId: this.topInfo.businessId, //业务类型
        businessTypeName: this.topInfo.businessTypeName, //业务类型
        buyerOrgCode: this.topInfo.buyerGroup, //采购组
        buyerOrgId: this.buyerGroupOptions.find((item) => {
          //采购组
          return item.value == this.topInfo.buyerGroup
        })?.id,
        buyerOrgName: this.buyerGroupOptions.find((item) => {
          //采购组
          return item.value == this.topInfo.buyerGroup
        })?.text,
        buyerGroupId: this.topInfo?.buyerOrg, //采购组织
        buyerGroupCode: this.buyerOrgOptions.find((item) => {
          //采购组织
          return item.value == this.topInfo.buyerOrg
        })?.organizationCode,
        buyerGroupName: this.buyerOrgOptions.find((item) => {
          //采购组织
          return item.value == this.topInfo.buyerOrg
        })?.organizationName,
        buyerUserCode: this.topInfo.buyerUser || this.topInfo.buyerUserCode, //采购员
        buyerUserId: this.topInfo.buyerUserId, //采购员
        buyerUserName: this.topInfo.buyerUserName, //采购员
        companyCode: companyInfo.companyCode, //公司
        companyId: companyInfo.companyId, //公司
        companyName: companyInfo.companyName, //公司
        currencyId: this.topInfo.currencyId, //币种
        currencyCode: this.currencyOptions.find((item) => {
          //币种
          return item.value == this.topInfo.currencyId
        })?.currencyCode,
        currencyName: this.currencyOptions.find((item) => {
          //币种
          return item.value == this.topInfo.currencyId
        })?.text,
        deliveryStatus: this.topInfo.deliveryStatus || 0, //供方发货状态
        requiredDeliveryDate: this.topInfo.requiredDeliveryDate
          ? this.topInfo.requiredDeliveryDate.getTime()
          : '0',
        feedbackStatus: this.topInfo.feedbackStatus || 0, //供应商反馈状态
        freeTotal: this.topInfo.freeTotal, //未税总价
        invoiceId: this.topInfo.invoiceId, //发票id
        paymentCode: this.topInfo.payment, //付款条件
        paymentName: this.paymentOptions.find((item) => {
          //付款条件
          return item.value == this.topInfo.payment
        })?.text,
        publishStatus: this.topInfo.publishStatus || 0, //发布状态
        purDeliveryStatus: this.topInfo.purDeliveryStatus || 0, //采方发货状态
        reason: 0, //售后原因
        receiveStatus: this.topInfo.receiveStatus || 0, //收货状态
        relateOrderCode: this.topInfo.relateOrderCode || 0, //是否关联采购订单
        remark: this.topInfo.remark, //备注
        // settlementId: this.topInfo.settlement, //结算方
        // settlementName: this.settlementOptions.find((item) => {
        //   //结算方
        //   return item.value == this.topInfo.settlement;
        // })?.text,
        source: this.entrySource, //订单来源
        status: 0, //单据状态
        supplierCode: this.topInfo.purtenant, //供应商
        supplierId: this.purtenantOptions.find((item) => {
          //供应商
          return item.value == this.topInfo.purtenant
        })?.id,
        supplierName: this.purtenantOptions.find((item) => {
          //供应商
          return item.value == this.topInfo.purtenant
        })?.text,
        taxTotal: this.topInfo.taxTotal, //含税总价
        tenantId: 0, //租户
        tenantName: 0, //租户
        type: this.topInfo.type || 0, //订单类型
        allocationMode: this.costSwitch, //整单分摊
        orderCode: this.topInfo.orderCode, //订单code
        acceptanceUse: this.acceptanceUse, //是否开启验收计划 0关闭 1开启
        urgentTime: this.topInfo.urgentTime1 ? new Date(this.topInfo.urgentTime1).getTime() : '0', //加急时间
        urgentStatus: this.topInfo.urgentTime1 ? 1 : 0,
        publishTime: this.topInfo.publishTime || '0', //发布时间
        feedbackTime: this.topInfo.feedbackTime || '0', //反馈时间
        feedbackUserId: this.topInfo.feedbackUserId || '0', //反馈人id
        feedbackUser: this.topInfo.feedbackUser || '', //反馈人名称
        orderRel: this.topInfo.orderRel || '', //关联订单号
        supRemark: this.topInfo.supRemark || '', //供方备注
        orderTime: this.topInfo.orderTime || '0' //订单创建时间
      }
      if (this.entryType === '2') {
        params.id = this.entryId
      }
      return params
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .itemcon {
      display: flex;
      align-items: center;
    }
    .item {
      margin-right: 20px;
    }
    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      // &.more-width {
      //   // width: 450px;
      // }
      .mt-input {
        width: 100%;
      }
      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
    /deep/ .half-width {
      width: calc(40% - 20px) !important;
    }
  }
}
</style>
