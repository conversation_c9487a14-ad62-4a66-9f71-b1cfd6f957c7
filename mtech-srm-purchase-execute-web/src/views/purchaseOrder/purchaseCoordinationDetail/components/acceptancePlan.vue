<template>
  <div class="top-info pt20 grid-wrap">
    <!-- acceptancePlanColumn:{{ acceptancePlanColumn }} -->
    <div v-if="entryType === '1' || (entryType === '2' && entryDraft === '1')" class="switch-wrap">
      <span>{{ $t('启用') }}</span>
      <mt-switch
        active-value="1"
        inactive-value="0"
        v-model="acceptanceUse"
        @change="acceptanceUseChange"
      ></mt-switch>
    </div>
    <div
      v-if="
        ((entryType == '2' && entryDraft !== '1') || entryType == '3') && entryAcceptanceUse == '0'
      "
      class="tip"
    >
      {{ $t('提示：未启用验收计划') }}
    </div>
    <mt-data-grid
      id="Grid3"
      v-show="acceptanceUse === '1'"
      :data-source="dataSource"
      :column-data="acceptancePlanColumn"
      ref="dataGrid"
      :allow-paging="false"
      :edit-settings="editSettings"
      :toolbar="toolbarOptions"
      :toolbar-click="toolbarClick"
      :query-cell-info="customiseCell"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
      :enable-sticky-header="true"
      @addRow="addRow"
      @delRow="delRow"
      @selectedChanged="selectedChanged"
    ></mt-data-grid>
  </div>
</template>
<script>
import { cloneDeep } from 'lodash'
import UTILS from '../../../../utils/utils'
import bigDecimal from 'js-big-decimal'
import { editColumnBefore1, acceptancePlanColumn } from '../config/acceptancePlan'
export default {
  props: {
    fields: {
      //表头数据
      type: Array,
      default: () => []
    },
    entryType: {
      type: String,
      default: '1'
    },
    entryId: {
      type: String,
      default: '0'
    },
    entrySource: {
      type: String,
      default: '1'
    },
    entryDraft: {
      type: String,
      default: '1'
    },
    entryAcceptanceUse: {
      type: String,
      default: '0'
    },
    entryAcceptancePlanList: {
      type: Array,
      default: () => []
    },
    topInfoIsSaved: {
      type: String,
      default: '0'
    },
    showDataGrid: {
      type: Array,
      default: () => []
    },
    topInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      acceptancePlanColumn: [
        {
          width: '50',
          type: 'checkbox',
          allowEditing: false,
          showInColumnChooser: false
        }
      ],
      dataSource: [], //数据源
      acceptanceUse: '0',
      acceptanceTypeOptions: [], //验收类型
      acceptorIdOptions: [], // 验收人
      toolbarOptions: [],
      isEdit: '2', //是否在编辑状态
      editSettings: {
        allowEditing: true,
        allowAdding: true,
        allowDeleting: true,
        mode: 'Normal', // Batch模式下editTemplate使用下拉的话，保存的args里获取不到，所以就回显不上。最终用重新赋值dataSource解决(币种和贸易条款的下拉)
        showConfirmDialog: false,
        showDeleteConfirmDialog: true,
        newRowPosition: 'Bottom'
      },
      allIsPercentage: false, //判断是否达到了100%
      addId: 0, //前端id
      rowsSelectedInfo: {},
      nowEditRowFlag: '',
      currentRow: {}, //当前编辑或者要删除的行
      maxAcceptanceItemNoObj: {},
      firstEntry: true,
      payTypeOptions: []
    }
  },
  watch: {
    fields: {
      async handler() {
        await this.getDropDownData() // 要等到下拉数据获取后，才能组合列
        this.handleUnionColumns()
      },
      immediate: true
    },
    entryType: {
      handler() {},
      immediate: true
    },
    showDataGrid: {
      handler() {
        this.updateShowDataGrid()
      },
      immediate: true
    },
    acceptanceUse: {
      handler() {
        this.updateAcceptancePlan()
      },
      immediate: true
    },
    entryAcceptanceUse: {
      handler(val) {
        this.acceptanceUse = val
      },
      immediate: true
    },
    entryAcceptancePlanList: {
      async handler(val) {
        let _dataSource = []
        if (val.length && (this.entryType === '2' || this.entryType === '3')) {
          //如果有带入验收计划
          this.showDataGrid.forEach((item) => {
            this.firstEntry = false
            let hasAlreadyOne = false
            console.log('验收计划阿萨法撒------', item)
            val.forEach((item1) => {
              if (item1.orderItemId === item.id) {
                _dataSource.push({
                  ...item,
                  requiredDeliveryDate: item.requiredDeliveryDate
                    ? UTILS.formateTime(item.requiredDeliveryDate, 'YYYY/mm/dd HH:MM:SS')
                    : '',
                  purchasingCycleStart: item.purchasingCycleStart
                    ? UTILS.formateTime(item.purchasingCycleStart, 'YYYY/mm/dd HH:MM:SS')
                    : '',
                  purchasingCycleEnd: item.purchasingCycleEnd
                    ? UTILS.formateTime(item.purchasingCycleEnd, 'YYYY/mm/dd HH:MM:SS')
                    : '',
                  acceptId: item1.id,
                  itemNo1: item1.itemNo,
                  isOrder: hasAlreadyOne ? '2' : '1',
                  advancePayStatus: item1.advancePayStatus === 1 ? true : false,
                  chargeStatus: item1.chargeStatus === 1 ? true : false,
                  percentage: item1.percentage,
                  acceptanceStatusName:
                    item1.acceptanceStatus === 0
                      ? this.$t('未验收')
                      : item1.acceptanceStatus === 2
                      ? this.$t('验收中')
                      : this.$t('已验收'),
                  acceptanceStatus: item1.acceptanceStatus,
                  acceptanceTypeName: item1.acceptanceTypeName,
                  acceptanceType: item1.acceptanceType,
                  freeTotal1: item1.freeTotal,
                  taxTotal1: item1.taxTotal,
                  taxTotal2: item.taxTotal,
                  tax1: item1.tax,
                  selfId: this.addId++,
                  preAcceptanceTime: item1.preAcceptanceTime
                    ? new Date(Number(item1.preAcceptanceTime))
                    : '',
                  // payTypeValue: item1.payType,
                  // payType:
                  //   item1.payType == 1 ? this.$t("现汇") : this.$t("承兑"),
                  payTypeName: item1.payTypeName,
                  payTypeCode: item1.payTypeCode,
                  description: item1.description,
                  acceptor: item1.acceptor,
                  acceptorId: item1.acceptorId
                })
                hasAlreadyOne = true
              }
            })
          })
          //计算最大的验收计划行号start
          let addIds = []
          _dataSource.forEach((item) => {
            addIds.push(item.addId)
          })
          addIds = [...new Set(addIds)]
          let maxAcceptanceItemNoObj = {}
          addIds.forEach((item) => {
            let ItemNoList = []
            _dataSource.forEach((item1) => {
              if (item1.addId === item) {
                ItemNoList.push(item1.itemNo1)
              }
            })
            maxAcceptanceItemNoObj[`row${item}`] = Math.max(...ItemNoList)
          })
          this.maxAcceptanceItemNoObj = maxAcceptanceItemNoObj
          //计算最大的验收计划行号end
          this.dataSource = _dataSource
        }
      },
      immediate: true
    }
  },
  methods: {
    updateShowDataGrid() {
      console.log(this.showDataGrid, '我是最新的订单明细')
      let newList = cloneDeep(this.showDataGrid)
      if (newList.length && this.entryType === '2') {
        //编辑进入并且有数据
        if (this.firstEntry) return
      }
      //拿到数据之后和现有的数据进行比较 是否已经有相同的订单明细了? 如果有相同的订单明细,直接根据相同的订单明细数据
      //如果没有相同的订单明细数据新增一行这个订单明细,把订单明细中 验收计划selfId赋值
      let _dataSource = []
      this.dataSource.forEach((item) => {
        _dataSource.push({ ...item, column: '' })
      })
      newList.forEach((item) => {
        item.tax1 = item.taxid
        if (item.requiredDeliveryDate) {
          item.requiredDeliveryDate = UTILS.formateTime(
            item.requiredDeliveryDate,
            'YYYY/mm/dd HH:MM:SS'
          )
        }
        if (item.purchasingCycleStart) {
          item.purchasingCycleStart = UTILS.formateTime(
            item.purchasingCycleStart,
            'YYYY/mm/dd HH:MM:SS'
          )
        }
        if (item.purchasingCycleEnd) {
          item.purchasingCycleEnd = UTILS.formateTime(
            item.purchasingCycleEnd,
            'YYYY/mm/dd HH:MM:SS'
          )
        }
        if (item.applyDate) {
          item.applyDate = UTILS.formateTime(item.applyDate, 'YYYY/mm/dd HH:MM:SS')
        }
        let hasItem = false
        _dataSource.some((item1) => {
          if (item1.addId === item.addId) {
            hasItem = true //已经有这条订单明细了,去更新订单明细的数据
            Object.assign(item1, item)
            if (!(item1.acceptor && item1.acceptorId)) {
              if (item.consignee && item.consigneeId) {
                item1.acceptor = item.consignee
                item1.acceptorId = item.consigneeId
              }
            }
            if (item1.taxTotal2 !== item1.taxTotal) {
              console.log('价格有变动了', item1)
              item1.taxTotal2 = item1.taxTotal
              if (item1.acceptanceStatus === 1) {
                //已验收
                if (!item1.taxTotal) {
                  item1.percentage = ''
                  return
                }
                item1.percentage = bigDecimal.divide(item1.taxTotal1, item1.taxTotal)
                item1.percentage = bigDecimal.multiply(item1.percentage, 100)
                item1.percentage = bigDecimal.round(item1.percentage, 2)
              }
              if (item1.acceptanceStatus === 0) {
                item1.percentage = ''
                item1.freeTotal1 = ''
                item1.taxTotal1 = ''
              }
            }
          }
        })
        if (!hasItem) {
          let row = {
            ...item,
            taxTotal2: item.taxTotal,
            itemNo1: '0',
            isOrder: '1',
            advancePayStatus: false,
            chargeStatus: false,
            acceptanceStatusName: this.$t('未验收'),
            acceptanceStatus: 0,
            selfId: this.addId++,
            preAcceptanceTime: new Date()
          }
          if (item.consignee && item.consigneeId) {
            //验收人默认等于收货人
            row.acceptor = item.consignee
            row.acceptorId = item.consigneeId
          }
          _dataSource.push(row)
        }
        //如果带入了验收计划的话
      })
      //判断订单明细是否有删除订单明细start
      let _dataSource1 = []
      _dataSource.forEach((item) => {
        newList.some((item1) => {
          if (item.addId === item1.addId) {
            _dataSource1.push(item)
          }
        })
      })
      //判断订单明细是否有删除订单明细end
      this.dataSource = _dataSource1
    },
    delRow(row) {
      this.currentRow = row
      if (this.isEdit === '1') {
        this.endEdit()
        setTimeout(() => {
          this.handleDel1()
        }, 500)
      } else {
        this.handleDel1()
      }
    },
    handleDel1() {
      let _dataSource = []
      this.dataSource.forEach((item) => {
        _dataSource.push({ ...item, column: '' })
      })
      let index = 0
      _dataSource.some((item, index1) => {
        if (item.selfId === this.currentRow.selfId) {
          index = index1
        }
      })
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('确认删除该验收计划行？')
        },
        success: () => {
          _dataSource.splice(index, 1)
          this.dataSource = _dataSource
        }
      })
    },
    addRow(row) {
      this.currentRow = row
      if (this.isEdit === '1') {
        this.endEdit()
        setTimeout(() => {
          this.handleAdd1()
        }, 500)
      } else {
        this.handleAdd1()
      }
    },
    handleAdd1() {
      //todo 先校验下比例是否已经达到了100%
      let _dataSource = []
      this.dataSource.forEach((item) => {
        _dataSource.push({ ...item, column: '' })
      })
      let index = 0
      _dataSource.some((item, index1) => {
        if (item.selfId === this.currentRow.selfId) {
          index = index1 + 1
        }
      })
      let addRow = this.currentRow
      addRow.selfId = this.addId++
      addRow.isOrder = '2'
      addRow.itemNo1 = '0'
      addRow.acceptId = ''
      addRow.advancePayStatus = false
      addRow.chargeStatus = false
      addRow.acceptanceStatusName = this.$t('未验收')
      addRow.acceptanceType = ''
      addRow.freeTotal1 = ''
      addRow.taxTotal1 = ''
      addRow.acceptanceStatus = 0
      addRow.acceptanceTypeName = ''
      addRow.percentage = ''
      // addRow.payTypeValue = "";
      addRow.payTypeCode = ''
      addRow.payTypeName = ''
      // addRow.payType = "";
      addRow.description = ''
      // addRow.acceptor = "";
      // addRow.acceptorId = "";
      addRow.preAcceptanceTime = new Date()
      _dataSource.splice(index, 0, addRow)
      this.dataSource = _dataSource
    },
    // 组合列
    handleUnionColumns() {
      //设置验收计划的表头
      let _columnData1 = cloneDeep(editColumnBefore1)
      if (this.fields && this.fields.length) {
        // let fields = cloneDeep(this.fields);
        // fields = fields.filter((item) => {
        //   return (
        //     item.fieldCode === "warehouseStatus" ||
        //     item.fieldCode === "itemCode" ||
        //     item.fieldCode === "itemName" ||
        //     item.fieldCode === "requireName" ||
        //     item.fieldCode === "requirementDescription" ||
        //     item.fieldCode === "categoryName" ||
        //     item.fieldCode === "siteName" ||
        //     item.fieldCode === "taxPrice" ||
        //     item.fieldCode === "taxTotal" ||
        //     item.fieldCode === "consignee" ||
        //     item.fieldCode === "receiveAddress" ||
        //     item.fieldCode === "contact"
        //   );
        // });
        // _columnData1 = _columnData1.concat(
        //   fields.map((item) => {
        //     let row = {
        //       ...item,
        //       allowEditing: false,
        //       headerText: item.fieldName,
        //       field: item.fieldCode,
        //       width: item.fieldCode === "consignee" ? "200" : "150",
        //     };
        //     if (item.fieldCode === "consignee") {
        //       //收货人
        //       row.valueAccessor = function (field, data) {
        //         if (data?.consignee) {
        //           if (data.consignee.split("-")[3]) {
        //             return data?.consignee.split("-")[3];
        //           } else {
        //             return data?.consignee;
        //           }
        //         } else {
        //           return "";
        //         }
        //       };
        //     }
        //     return row;
        //   })
        // );
        let acceptancePlanColumn = cloneDeep(this.acceptancePlanColumn)
        if (this.entryType === '3') {
          acceptancePlanColumn.forEach((item) => {
            item.allowEditing = false
          })
          acceptancePlanColumn.pop()
        }
        acceptancePlanColumn = _columnData1.concat(acceptancePlanColumn)
        this.acceptancePlanColumn = acceptancePlanColumn
      }
    },
    // 获取下拉数据
    async getDropDownData() {
      //验收类型
      await this.$API.purchaseOrder.getOrderAcceptanceStrategy().then((res) => {
        res.data.forEach((item) => {
          item.text = item.acceptanceTypeName
          item.value = item.id
        })
        this.acceptanceTypeOptions = res.data
      })
      //付款类型 就是付款方式
      // this.$API.masterData
      //   .getDictCode({ dictCode: "PAY_METHOD" })
      //   .then((res) => {
      //     this.payTypeOptions = res.data || [];
      //   });
      // // 验收人
      // await this.$API.masterData.getCurrentTenantEmployees().then((res) => {
      //   const tmp = [];
      //   res.data.forEach((item) => {
      //     tmp.push({
      //       ...item,
      //       text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`,
      //       value: item.employeeId,
      //       name: item.employeeName,
      //     });
      //   });
      //   this.acceptorIdOptions = tmp;
      // });
      this.acceptancePlanColumn = acceptancePlanColumn({
        that: this,
        acceptanceTypeOptions: this.acceptanceTypeOptions,
        acceptorIdOptions: this.acceptorIdOptions,
        payTypeOptions: this.payTypeOptions
      })
    },
    // // 获取 验收人 列表
    // getUser(e) {
    //   const { text: fuzzyName } = e;
    //   this.acceptorIdOptions = [];
    //   this.$API.masterData
    //     .getCurrentTenantEmployees({ fuzzyName })
    //     .then((res) => {
    //       const tmp = [];
    //       res.data.forEach((item) => {
    //         tmp.push({
    //           ...item,
    //           text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`,
    //           value: item.employeeId,
    //           name: item.employeeName,
    //         });
    //       });
    //       this.acceptorIdOptions = tmp;
    //     });
    // },
    selectedChanged(e) {
      let itemInfo = {}
      if (e.fieldCode === 'acceptanceTypeName') {
        itemInfo.acceptanceType = e.itemInfo.value
      }
      // if (e.fieldCode === "acceptor") {
      //   itemInfo.acceptorId = e.itemInfo.value;
      // }
      if (e.fieldCode === 'payType') {
        itemInfo.payTypeName = e.itemInfo.itemName
        itemInfo.payTypeCode = e.itemInfo.itemCode
      }
      if (e.fieldCode === 'acceptor') {
        console.log('acceptor人信息-----', e.itemInfo)
        itemInfo.acceptorId = e.itemInfo.userId
      }
      let hasItem = false
      Object.keys(this.rowsSelectedInfo).some((item) => {
        if (item === `row-${this.nowEditRowFlag}`) {
          hasItem = true
          Object.assign(this.rowsSelectedInfo[item], itemInfo)
        }
      })
      if (!hasItem) {
        this.$set(this.rowsSelectedInfo, `row-${this.nowEditRowFlag}`, itemInfo)
      }
    },
    // 不可修改的单元格，修改背景色
    customiseCell(args) {
      if (!args.column.allowEditing) {
        args.cell.classList.add('bg-grey')
      }
    },
    toolbarClick(args) {
      // 自定义了一个行内新增按钮，为了给新增的这一行赋默认值
      if (args.item.id == 'addinlinebtn') {
        this.$emit('checkTopInfo')
        setTimeout(() => {
          if (this.topInfoIsSaved === '1') {
            if (this.isEdit === '1') {
              this.endEdit()
              setTimeout(() => {
                this.handleAdd()
              }, 300)
            } else {
              this.handleAdd()
            }
          }
        }, 500)
      }
    },
    endEdit() {
      //让组件失去焦点
      this.$refs.dataGrid.ejsRef.endEdit()
    },
    handleAdd() {
      this.$refs.dataGrid.ejsRef.addRecord()
    },
    actionBegin(args) {
      if (args.requestType === 'add') {
        // 初始化数据
        this.nowEditRowFlag = args.rowData.selfId
      }
      if (args.action === 'add' && args.requestType === 'save') {
        const length = this.$refs.dataGrid.ejsRef.getCurrentViewRecords()?.length
        args.index = length
        this.nowEditRowFlag = args.rowData.selfId
      }
      // 记录当前编辑行的唯一标识
      if (args.requestType == 'beginEdit') {
        this.nowEditRowFlag = args.rowData.selfId
        if (this.$route.query.type === '3') {
          args.cancel = true
          return
        }
      }
    },
    actionComplete(args) {
      if (args.action == 'add' || args.action == 'edit') {
        this.nowEditRowFlag = ''
      }
      if (args.requestType === 'beginEdit' || args.requestType === 'add') {
        this.isEdit = '1'
      }
      if (
        args.requestType === 'refresh' ||
        args.requestType === 'save' ||
        args.requestType === 'delete'
      ) {
        this.isEdit = '2'
        this.updateAcceptancePlan()
      }
    },
    acceptanceUseChange(val) {
      if (val === '1') {
        this.$dialog({
          data: {
            title: this.$t('提醒'),
            message: this.$t('确认启用验收计划吗?')
          },
          success: () => {
            if (this.dataSource.length === 0) {
              this.firstEntry = false
              this.updateShowDataGrid()
            }
          },
          close: () => {
            this.acceptanceUse = '0'
          }
        })
      }
    },
    validateAcceptance() {
      //用来校验验收计划数据是否有效 ,含税价格 未税价格 付款类型 验收人是否有数据等
    },
    updateAcceptancePlan() {
      // 更新传出去验收计划
      if (this.acceptanceUse === '0') {
        this.$emit('updateAcceptancePlan', '0', [], this.allIsPercentage)
      }
      if (this.acceptanceUse === '1') {
        if (!(this.$refs.dataGrid && this.$refs.dataGrid.ejsRef)) {
          return
        }
        //计算每行剩余的比例及 金额  用100 减去另外的验收计划行 start
        let addIds1 = []
        this.$refs.dataGrid.ejsRef.getCurrentViewRecords().forEach((item) => {
          Object.keys(this.rowsSelectedInfo).some((key) => {
            let _flag = item.selfId
            if (key == 'row-' + _flag) {
              Object.assign(item, this.rowsSelectedInfo[key])
            }
          })
          addIds1.push(item.addId)
        })
        addIds1 = [...new Set(addIds1)]
        let objInfo1 = {}
        addIds1.forEach((item) => {
          objInfo1[`rowz${item}`] = []
          this.$refs.dataGrid.ejsRef.getCurrentViewRecords().forEach((item1) => {
            if (item1.addId === item) {
              objInfo1[`rowz${item}`].push(item1)
            }
          })
        })
        Object.keys(objInfo1).forEach((item) => {
          objInfo1[item].forEach((item1) => {
            item1.lastPercentage = '0'
            item1.lastFreeTotal1 = '0'
            item1.lastTaxTotal1 = '0'
            let otherList = objInfo1[item].filter((item2) => {
              return item2.selfId !== item1.selfId
            })
            let otherAllPercentage = '0'
            let otherAllFreeTotal1 = '0'
            let otherAllTaxTotal1 = '0'
            otherList.forEach((item2) => {
              if (item2.percentage) {
                otherAllPercentage = bigDecimal.add(otherAllPercentage, item2.percentage)
              }
              if (item2.freeTotal1) {
                otherAllFreeTotal1 = bigDecimal.add(otherAllFreeTotal1, item2.freeTotal1)
              }
              if (item2.taxTotal1) {
                otherAllTaxTotal1 = bigDecimal.add(otherAllTaxTotal1, item2.taxTotal1)
              }
            })
            item1.lastPercentage = bigDecimal.subtract(100, otherAllPercentage)
            item1.lastPercentage = bigDecimal.round(item1.lastPercentage, 2)
            if (item1.freeTotal) {
              item1.lastFreeTotal1 = bigDecimal.subtract(item1.freeTotal, otherAllFreeTotal1)
              item1.lastFreeTotal1 = bigDecimal.round(item1.lastFreeTotal1, 2)
            }
            if (item1.taxTotal) {
              item1.lastTaxTotal1 = bigDecimal.subtract(item1.taxTotal, otherAllTaxTotal1)
              item1.lastTaxTotal1 = bigDecimal.round(item1.lastTaxTotal1, 2)
            }
            this.$refs.dataGrid.ejsRef.getCurrentViewRecords().some((item2) => {
              if (item2.selfId === item1.selfId) {
                Object.assign(item2, item1)
              }
            })
          })
        })
        //计算每行剩余的比例 用100 减去另外的验收计划行 end
        console.log('最新的表格数据', this.$refs.dataGrid.ejsRef.getCurrentViewRecords())
        let sendlist1 = []
        this.$refs.dataGrid.ejsRef.getCurrentViewRecords().forEach((item) => {
          sendlist1.push({ ...item, column: '' })
        })
        let addIds = []
        sendlist1.forEach((item) => {
          addIds.push(item.addId)
        })
        addIds = [...new Set(addIds)]
        let objInfo = {}
        addIds.forEach((item) => {
          objInfo[`row${item}`] = []
          sendlist1.forEach((item1) => {
            if (item1.addId === item) {
              let obj = {
                // addId: item1.addId,
                // selfId: item1.selfId,
                id: item1.acceptId || 0,
                orderId: this.entryId,
                orderCode: this.topInfo.orderCode || '', //订单编号
                orderItemId: item1.id || 0, //这个才是订单明细id
                acceptanceType: item1.acceptanceType, //验收类型id
                acceptanceTypeName: item1.acceptanceTypeName, //验收类型名称
                itemNo: item1.itemNo1, //验收计划行号
                acceptanceStatus: item1.acceptanceStatus, //验收状态
                // payType: item1.payTypeValue, //付款类型
                payType: '1',
                payTypeCode: item1.payTypeCode || '',
                payTypeName: item1.payTypeName || '',
                percentage: item1.percentage || '0', //比例
                percentage1: item1.percentage, //用来验证
                freeTotal: item1.freeTotal1, //未税金额
                taxTotal: item1.taxTotal1, //含税金额
                tax: item1.tax1, //税率
                advancePayStatus: item1.advancePayStatus ? 1 : 0, //是否预付
                chargeStatus: item1.chargeStatus ? 1 : 0, //是否挂账
                acceptorId: item1.acceptorId, //验收人id
                acceptor: item1.acceptor, //验收人
                preAcceptanceTime: item1.preAcceptanceTime
                  ? item1.preAcceptanceTime.getTime()
                  : '0', //计划验收时间
                acceptanceTime: 0, //没有实际验收时间
                description: item1.description, //验收描述
                remark: item1.remark, //验收备注
                advanceInvoiceStatus: 0, //是否提前开票 默认否
                maxAcceptanceItemNo: 0
              }
              Object.keys(this.maxAcceptanceItemNoObj).some((item2) => {
                if (`row${item}` === item2) {
                  obj.maxAcceptanceItemNo = this.maxAcceptanceItemNoObj[item2]
                  return
                }
              })
              objInfo[`row${item}`].push(obj)
            }
          })
        })
        let allIsPercentage = true
        let validatePercentage = true
        let validateAcceptanceType = true
        Object.keys(objInfo).forEach((item) => {
          let isPercentage = 0
          objInfo[item].forEach((item1) => {
            isPercentage = bigDecimal.add(item1.percentage, isPercentage)
            if (
              item1.percentage1 == null ||
              item1.percentage1 === undefined ||
              item1.percentage1 === '' ||
              item1.percentage1 === '0.00' ||
              item1.percentage1 === 0
            ) {
              validatePercentage = false
            }
            if (
              item1.acceptanceType === null ||
              item1.acceptanceType === undefined ||
              item1.acceptanceType === ''
            ) {
              validateAcceptanceType = false
            }
          })
          if (bigDecimal.compareTo(isPercentage, 100) !== 0) {
            allIsPercentage = false
          }
        })
        this.allIsPercentage = allIsPercentage
        console.log(objInfo, allIsPercentage, '我是保存的信息')
        //校验验收计划行比例加起来是否达到了100%
        this.$emit(
          'updateAcceptancePlan',
          '1',
          objInfo,
          this.allIsPercentage,
          validatePercentage,
          validateAcceptanceType
        )
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.top-info {
  flex: 1;
  margin: 0;
  .switch-wrap {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background: #fff;
    padding: 20px 20px 10px 0;

    span {
      margin-right: 14px;
    }
  }
  .tip {
    box-sizing: border-box;
    padding: 20px;
  }
  .my-table {
    max-width: 100vw;
    overflow-x: auto;
  }
}
</style>
<style lang="scss" scoped>
.full-height {
  background: #fff;
}
</style>

<style lang="scss">
.pc-selection-dialog {
  .mt-data-grid {
    height: 100%;
    > .e-grid {
      height: 100%;
      // display: flex;

      > .e-gridcontent {
        flex: 1;
        overflow: auto;
      }
    }

    .e-rowcell.e-active {
      background: #e0e0e0 !important;
    }
  }
}
</style>
<style lang="scss" scoped>
.full-height {
  background: #fff;
}
.pt20 {
  padding-top: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  /deep/ #Grid2_toolbarItems {
    border-top: none;
  }
  // .mt-data-grid {
  //   flex: 1;
  //   /deep/ .e-grid {
  //     height: 100%;
  //     display: flex;
  //     flex-direction: column;

  //     .e-headerchkcelldiv {
  //       padding-left: 0;
  //     }

  //     .e-gridcontent {
  //       flex: 1;
  //       .e-content {
  //         // height: 100% !important;
  //         flex-direction: row !important;
  //       }
  //     }
  //   }
  // }
}
.toolbar-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;

  .one-bar {
    display: flex;
    align-items: center;
    margin-right: 20px;
    cursor: pointer;
    .mt-icons {
      font-size: 14px;
      color: #4f5b6d;
      margin-right: 5px;
    }
    span {
      word-break: keep-all;
      font-size: 14px;
      color: #4f5b6d;
      font-weight: normal;
    }
  }

  .flex1 {
    flex: 1;
  }
}
</style>
