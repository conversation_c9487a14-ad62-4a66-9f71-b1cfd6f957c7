<template>
  <div :class="['pt20', 'grid-wrap']" :style="{ 'padding-top': entryType === '3' ? '20px' : '0' }">
    <!-- 合作方 -->
    <mt-data-grid
      id="Grid3"
      :data-source="dataSource"
      :column-data="partnersColumn"
      ref="dataGrid"
      :allow-paging="false"
      :edit-settings="editSettings"
      :toolbar="toolbarOptions"
      :query-cell-info="customiseCell"
      :toolbar-click="toolbarClick"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
      @selectedChanged="selectedChanged"
      :enable-sticky-header="true"
    ></mt-data-grid>
  </div>
</template>
<script>
import { editColumnBefore, editColumn } from '../config/partners.js'
import utils from '@/utils/utils'
import { cloneDeep } from 'lodash'
export default {
  props: {
    entryId: {
      type: String,
      default: '0'
    },
    topInfoIsSaved: {
      type: String,
      default: '0'
    },
    entryPartners: {
      type: Array,
      default: () => []
    },
    entryType: {
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      dataSource: [],
      partnersColumn: [
        {
          width: '50',
          type: 'checkbox',
          allowEditing: false,
          showInColumnChooser: false
        }
      ],
      editSettings: {
        allowEditing: true,
        allowAdding: true,
        allowDeleting: true,
        mode: 'Normal',
        showConfirmDialog: false,
        showDeleteConfirmDialog: true,
        newRowPosition: 'Bottom'
      },
      toolbarOptions: [
        {
          text: this.$t('新增'),
          id: 'addinlinebtn',
          prefixIcon: 'e-add'
        },
        'Delete'
      ],
      nowEditRowFlag: '',
      isEdit: '2', //是否在编辑状态
      addId: 0, //前端id
      rowsSelectedInfo: {}
    }
  },
  watch: {
    entryPartners: {
      handler(val) {
        if (!val) {
          return
        }
        this.updataEntryList()
      },
      immediate: true
    },
    entryType: {
      handler() {
        this.init()
        this.mergeColumn()
      },
      immediate: true
    }
  },
  methods: {
    init() {
      if (this.entryType === '3') {
        this.toolbarOptions = []
      }
    },
    selectedChanged(e) {
      let itemInfo = {}
      if (e.fieldCode === 'partnerCode') {
        itemInfo.partnerId = e.itemInfo?.id || '1'
      }
      let hasItem = false
      Object.keys(this.rowsSelectedInfo).some((item) => {
        if (item === `row-${this.nowEditRowFlag}`) {
          hasItem = true
          Object.assign(this.rowsSelectedInfo[item], itemInfo)
        }
      })
      if (!hasItem) {
        this.$set(this.rowsSelectedInfo, `row-${this.nowEditRowFlag}`, itemInfo)
      }
    },
    updataEntryList() {
      let entryList = cloneDeep(this.entryPartners)
      entryList.forEach((item) => {
        item.addId = this.addId++
        item.createTime1 = utils.dateFormat(Number(item.createTime))
      })
      this.dataSource = entryList
    },
    toolbarClick(args) {
      // 自定义了一个行内新增按钮，为了给新增的这一行赋默认值
      if (args.item.id == 'addinlinebtn') {
        this.$emit('checkTopInfo')
        setTimeout(() => {
          if (this.topInfoIsSaved === '1') {
            if (this.isEdit === '1') {
              this.endEdit()
              setTimeout(() => {
                this.handleAdd()
              }, 300)
            } else {
              this.handleAdd()
            }
          }
        }, 500)
      }
    },
    handleAdd() {
      if (this.$refs.dataGrid.ejsRef.getCurrentViewRecords()?.length >= 3) {
        this.$toast({
          content: this.$t('最多添加合作类型不同的数据为3条'),
          type: 'warning'
        })
        return
      }
      this.$refs.dataGrid.ejsRef.addRecord()
    },
    endEdit() {
      //让组件失去焦点
      this.$refs.dataGrid.ejsRef.endEdit()
    },
    mergeColumn() {
      //组合表格的表头信息
      if (this.entryType === '3') {
        let editColumn1 = cloneDeep(editColumn)
        editColumn1.forEach((item) => {
          item.allowEditing = false
        })
        this.partnersColumn = editColumnBefore.concat(editColumn1)
      }
      if (this.entryType === '1' || this.entryType === '2') {
        let editColumn1 = cloneDeep(editColumn)
        this.partnersColumn = editColumnBefore.concat(editColumn1)
      }
    },
    actionBegin(args) {
      if (args.requestType === 'add') {
        // 初始化数据
        args.data.addId = String(this.addId++)
        this.nowEditRowFlag = args.rowData.addId
      }
      if (args.action === 'add' && args.requestType === 'save') {
        const length = this.$refs.dataGrid.ejsRef.getCurrentViewRecords()?.length
        args.index = length
        this.nowEditRowFlag = args.rowData.addId
      }
      // 记录当前编辑行的唯一标识
      if (args.requestType == 'beginEdit') {
        this.nowEditRowFlag = args.rowData.addId
      }
    },
    actionComplete(args) {
      if (args.action == 'add' || args.action == 'edit') {
        this.nowEditRowFlag = ''
      }
      if (args.requestType === 'beginEdit' || args.requestType === 'add') {
        this.isEdit = '1'
      }
      if (
        args.requestType === 'refresh' ||
        args.requestType === 'save' ||
        args.requestType === 'delete'
      ) {
        this.isEdit = '2'
        this.updatePartners()
      }
    },
    updatePartners() {
      // 更新获取最新的数据
      if (!(this.$refs.dataGrid && this.$refs.dataGrid.ejsRef)) {
        return
      }
      this.$refs.dataGrid.ejsRef.getCurrentViewRecords().forEach((item) => {
        Object.keys(this.rowsSelectedInfo).some((key) => {
          let _flag = item.addId
          if (key == 'row-' + _flag) {
            Object.assign(item, this.rowsSelectedInfo[key])
          }
        })
      })
      let sendlist1 = cloneDeep(this.$refs.dataGrid?.ejsRef.getCurrentViewRecords() || [])
      console.log('最新的表格数据合作方', sendlist1)
      let sendlist = []
      let partnerTypeList = []
      let allValidate = true
      sendlist1.forEach((item) => {
        sendlist.push({
          orderId: this.entryId,
          partnerCode: item.partnerCode,
          partnerId: item.partnerId,
          partnerName: item.partnerName,
          partnerType: item.partnerType
        })
        partnerTypeList.push(item.partnerType)
        if (!item.partnerType || !item.partnerCode) {
          allValidate = false
        }
      })
      if (partnerTypeList.length > 3) {
        allValidate = false
      }
      let flag = this.isRepeat(partnerTypeList)
      if (flag) {
        allValidate = false
      }
      this.$emit('updatePartners', sendlist, allValidate)
    },
    isRepeat(arr) {
      // 验证重复元素，有重复返回true；否则返回false
      var hash = {}
      for (var i in arr) {
        if (hash[arr[i]]) {
          return true
        }
        // 不存在该元素，则赋值为true，可以赋任意值，相应的修改if判断条件即可
        hash[arr[i]] = true
      }
      return false
    },
    // 不可修改的单元格，修改背景色
    customiseCell(args) {
      if (!args.column.allowEditing) {
        args.cell.classList.add('bg-grey')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  background: #fff;
}
.top-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  /deep/ #Grid3_toolbarItems {
    border-top: none;
    min-height: 0;
  }
  .mt-data-grid {
    flex: 1;
    /deep/ .e-grid {
      height: 100%;
      display: flex;
      flex-direction: column;

      .e-headerchkcelldiv {
        padding-left: 0;
      }

      .e-gridcontent {
        flex: 1;
        .e-content {
          // height: 100% !important;
          flex-direction: row !important;
        }
      }
    }
  }
}
.toolbar-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;

  .one-bar {
    display: flex;
    align-items: center;
    margin-right: 20px;
    cursor: pointer;
    .mt-icons {
      font-size: 14px;
      color: #4f5b6d;
      margin-right: 5px;
    }
    span {
      word-break: keep-all;
      font-size: 14px;
      color: #4f5b6d;
      font-weight: normal;
    }
  }

  .flex1 {
    flex: 1;
  }
}
</style>
