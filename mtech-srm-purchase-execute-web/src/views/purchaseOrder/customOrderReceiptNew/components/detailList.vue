<template>
  <div>
    <!-- 客户选择列表 -->
    <div class="tabs-area">
      <mt-select
        class="pur-tenant-select"
        :width="200"
        :data-source="businessTypeList"
        v-model="currentTabId"
        :open-dispatch-change="false"
        :show-clear-button="false"
        @change="setTypeList"
        :fields="{ value: 'purTenantId', text: 'title' }"
        placeholder=""
      ></mt-select>
      <!-- 对账单类型 -->
      <mt-tabs
        :e-tab="false"
        :data-source="tabTypeList"
        id="supReconTypeTabs"
        tab-id="supR-tab"
        class="supReconTypeTabs"
        :selected-item="currentTypeTabIndex"
        @handleSelectTab="handleSelectTypeTab"
      ></mt-tabs>
    </div>
    <div class="table-area" style="height: calc(100vh - 300px)">
      <!-- 自定义查询条件 -->
      <collapse-search
        :is-grid-display="true"
        :show-archive="true"
        max-height="700px"
        @reset="handleCustomReset"
        @search="handleCustomSearch"
        @archiveChange="handleArchiveChange"
      >
        <mt-form ref="searchFormRef">
          <template v-for="item in searchConfigs">
            <mt-form-item
              v-if="item.title"
              :key="item.field"
              :prop="item.field"
              :label="item.title"
              label-style="top"
            >
              <mt-multi-select
                v-if="item.searchOptions && item.searchOptions.elementType === 'multi-select'"
                style="flex: 1"
                v-model="item.searchOptions.value"
                :fields="item.searchOptions.fields"
                :show-select-all="true"
                :allow-filtering="true"
                filter-type="Contains"
                :data-source="item.searchOptions.dataSource"
                :placeholder="$t('请选择')"
              ></mt-multi-select>
              <mt-select
                v-else-if="item.searchOptions && item.searchOptions.elementType === 'select'"
                style="flex: 1"
                v-model="item.searchOptions.value"
                :fields="item.searchOptions.fields"
                :allow-filtering="true"
                filter-type="Contains"
                :data-source="item.searchOptions.dataSource"
                :placeholder="$t('请选择')"
              ></mt-select>
              <RemoteAutocomplete
                v-else-if="
                  item.searchOptions && item.searchOptions.elementType === 'remote-autocomplete'
                "
                v-model="item.searchOptions.value"
                :url="item.searchOptions.url"
                :placeholder="$t('请选择')"
                :fields="item.searchOptions.fields"
                :multiple="item.searchOptions.multiple"
                :params="item.searchOptions.params"
                :params-key="item.searchOptions.paramsKey"
                :search-fields="item.searchOptions.searchFields"
                :records-position="item.searchOptions.recordsPosition"
              ></RemoteAutocomplete>
              <mt-date-range-picker
                v-else-if="item.searchOptions && item.searchOptions.elementType === 'date-range'"
                :open-on-focus="true"
                :show-clear-button="item.searchOptions.clearButton"
                :allow-edit="false"
                v-model="item.searchOptions.value"
                :placeholder="item.searchOptions.placeholder"
              ></mt-date-range-picker>
              <div
                v-else-if="item.searchOptions && item.searchOptions.elementType === 'number'"
                style="display: flex"
              >
                <div class="operator-list">
                  <mt-select
                    v-model="item.searchOptions.operator"
                    :data-source="item.searchOptions.dataSource"
                    popup-width="50px"
                  ></mt-select>
                </div>
                <div class="custom-input-number">
                  <mt-input-number
                    v-model="item.searchOptions.value"
                    type="number"
                    :show-spin-button="false"
                    :show-clear-button="item.searchOptions.clearButton"
                    :placeholder="item.searchOptions.placeholder"
                  />
                </div>
              </div>
              <mt-input v-else v-model="item.searchOptions.value" />
            </mt-form-item>
          </template>
        </mt-form>
      </collapse-search>
      <!-- 表格 -->
      <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
        <ScTable
          ref="xTable"
          :row-config="{ height: 50 }"
          :columns="columns"
          :table-data="tableData"
          :grid-id="gridId"
          height="auto"
          header-align="left"
          align="left"
          show-overflow
          style="padding-top: unset"
          :scroll-x="{ gt: 0, oSize: 20 }"
          :scroll-y="{ gt: 0, oSize: 10 }"
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              v-permission="item.permission"
              size="small"
              @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
              >{{ item.name }}</vxe-button
            >
          </template>
          <template #fileDefault="{ row }">
            <div style="cursor: pointer; color: #2783fe" @click="fileClick(row)">
              {{ row.printMaterialFileName }}
            </div>
          </template>
        </ScTable>
      </div>
      <mt-page
        class="flex-keep custom-page"
        :page-settings="forecastPageSettings"
        :total-pages="forecastPageSettings.totalPages"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
      />
    </div>
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import * as UTILS from '@/utils/utils'
import {
  editColumnBefore,
  editColumnEnd,
  columnData1,
  columnDataSort,
  searchSortSup
} from './../config'
import { cloneDeep } from 'lodash'
import { formatColumn, formatParams } from './../config/columnFormat'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  name: '',
  data() {
    return {
      enableArchiveQuery: false,
      searchConfigs: [],
      columns: [],
      tableData: [],
      forecastPageCurrent: 1,
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      currentTabId: '',
      currentTypeTabIndex: 0,
      toolbar: [
        {
          code: 'Export1',
          icon: 'icon_solid_export',
          name: this.$t('导出'),
          permission: ['O_02_1072']
        }
      ],
      businessTypeList: [],
      tabTypeList: [],
      gridId: ''
    }
  }, // 监听,当路由发生变化的时候执行
  watch: {
    $route(to, from) {
      if (from.name === 'custom-receipt-detail') {
        this.handleCustomSearch()
      }
    }
  },
  mounted() {
    this.getBusinessConfig()
  },
  methods: {
    fileClick(row) {
      this.$API.drawingTogether
        .getFileUrlApi({
          id: row.printMaterialFileId
        })
        .then((res) => {
          if (res.code === 200) {
            window.open(res.data?.fileUrl)
          }
        })
    },
    // 设置gridId
    setGridId(code) {
      const gridId = '7ab1e79d-040e-468e-8d67-67fc7fc3211f'
      // const replaceLength = code.length
      // const firstStr = gridId.substring(9, 12)
      // const centerStr = gridId.substring(14, 17)
      // const lastStr = gridId.slice(-3)
      // this.gridId = this.gridId.replace(firstStr, code.slice(-3).toLowerCase())
      // this.gridId = this.gridId.replace(centerStr, code.slice(-3).toLowerCase())
      // this.gridId = this.gridId.replace(lastStr, code.slice(-3).toLowerCase())
      this.gridId = this.$md5(`${gridId}${code}`)
    },
    handleCustomReset() {
      this.searchConfigs.forEach((i) => {
        i.searchOptions = {
          ...i.searchOptions,
          value: null
        }
      })
      this.forecastPageCurrent = 1
      this.handleCustomSearch()
    },
    handleCustomSearch() {
      const rules = formatParams(this.searchConfigs)
      const params = {
        enableArchiveQuery: this.enableArchiveQuery,
        page: {
          current: this.forecastPageCurrent,
          size: this.forecastPageSettings.pageSize
        },
        condition: 'and',
        rules,
        defaultRules: [
          {
            condition: 'and',
            field: 'businessTypeCode',
            operator: 'equal',
            value: this.nowbusinessTypeCode
          },
          {
            condition: 'and',
            field: 'purTenantId',
            operator: 'equal',
            value: this.nowpurTenantId
          }
        ]
      }
      this.$API.purchaseOrder.soDetailQuery(params).then((res) => {
        if (res?.code == 200) {
          const total = res?.data?.total || 0
          this.forecastPageSettings.totalPages = Math.ceil(
            Number(total) / this.forecastPageSettings.pageSize
          )
          this.forecastPageSettings.totalRecordsCount = Number(total)
          const records = res?.data?.records || [] // 表格数据
          // 处理表数据
          this.tableData = records?.map((i) => {
            const item = {
              ...i
            }

            item.fieldDataList = item.fieldDataList || []
            item.fieldDataList.forEach((item1) => {
              //对应的动态字段 需求名称,需求描述,资产类别,资产编号,资产卡片,客户,关联客户订单
              item[item1.fieldCode] = item1.fieldData
            })
            if (item.costs) {
              item.orderCosts = `${item.costs[0].costCenterCode}-${item.costs[0].costCenterName}`
            }
            item.version1 = item.version
            if (item.taxTotal < 0) {
              item.taxTotal = ''
            }
            if (item.taxPrice < 0) {
              item.taxPrice = ''
            }

            if (item.freeTotal < 0) {
              item.freeTotal = ''
            }
            if (item.freePrice < 0) {
              item.freePrice = ''
            }
            if (item.unPrice < 0) {
              item.unPrice = ''
            }
            if (item.unTotal < 0) {
              item.unTotal = ''
            }
            if (item.approvedTotalPrice < 0) {
              item.approvedTotalPrice = ''
            }
            if (item.budgetTotalPrice < 0) {
              item.budgetTotalPrice = ''
            }
            if (item.budgetUnitPrice < 0) {
              item.budgetUnitPrice = ''
            }
            if (item.taxedTotalPrice < 0) {
              item.taxedTotalPrice = ''
            }
            if (item.taxedUnitPrice < 0) {
              item.taxedUnitPrice = ''
            }
            if (item.subjectTotal < 0) {
              item.subjectTotal = ''
            }
            return item
          })
        }
      })
    },
    handleArchiveChange(e) {
      this.enableArchiveQuery = !!e
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 获取业务类型下拉
    getBusinessConfig() {
      this.$API.purchaseOrder.getSupOrderQueryTab().then((res) => {
        this.businessTypeList = []
        let list = res.data || []
        let nowList = []
        list.forEach((item) => {
          nowList.push({
            title: item.purTenantName,
            purTenantId: item.purTenantId,
            tabs: item.tabs
          })
        })
        this.businessTypeList = nowList
        if (this.businessTypeList.length) {
          this.setTypeList({ value: list[0].purTenantId })
        }
      })
    },
    setTypeList(val) {
      if (!val.value) return
      let purTenantId = val.value
      this.currentTabId = purTenantId
      this.currentTypeTabIndex = 0
      this.tabTypeList = []
      this.currentTabIndex = this.businessTypeList.findIndex(
        (i) => i.purTenantId == this.currentTabId
      )
      this.businessTypeList[this.currentTabIndex].tabs.forEach((item) => {
        this.tabTypeList.push({
          title: item.businessTypeName,
          code: item.businessTypeCode,
          businessTypeId: item.businessTypeId
        })
      })
      if (this.tabTypeList.length) {
        let code = this.tabTypeList[0].code
        let businessTypeId = this.tabTypeList[0].businessTypeId
        this.setGridId(code)
        this.getColumnModule(code, businessTypeId, businessTypeId, this.currentTabId)
      }
    },
    handleSelectTypeTab(e) {
      this.currentTypeTabIndex = e
      // this.tabTypeList.forEach();
      if (this.tabTypeList.length) {
        let code = this.tabTypeList[e].code
        let businessTypeId = this.tabTypeList[e].businessTypeId
        this.setGridId(code)
        this.getColumnModule(code, businessTypeId, businessTypeId, this.currentTabId)
      }
    },
    //获取动态表头
    getColumnModule(code, e, businessTypeId, purTenantId) {
      let params = {
        businessType: businessTypeId,
        docType: 'so',
        moduleType: 6,
        tenantId: purTenantId
      }
      this.$API.purchaseOrder.getbusinessModuleFields(params).then((res) => {
        let flag = false
        if (res.data && res.data.length) {
          let _columnData = cloneDeep(editColumnBefore)
          let _columnData1 = cloneDeep(editColumnEnd)
          let _columnData2 = cloneDeep(columnData1)
          const finalColumnData = _columnData
            .concat(
              res.data.map((item1) => {
                let obj = {}
                let hasItem = false
                _columnData2.some((item2) => {
                  if (item2.field === item1.fieldCode) {
                    obj = {
                      ...item2,
                      headerText: item1.fieldName
                    }
                    hasItem = true
                  }
                })
                if (hasItem) {
                  return obj
                } else {
                  return {
                    ...item1,
                    headerText: item1.fieldName,
                    field: item1.fieldCode
                  }
                }
              })
            )
            .concat(_columnData1)
          // 此处考虑到直接修改列配置里的数据可能是导致表格的不稳定，进而新增一个searchConfigs变量进行查询字段的接收
          const allColumns = formatColumn(finalColumnData)
          const columns1 = []
          const columns2 = []
          allColumns.forEach((i) => {
            if (i.field === 'ihrez' || i.field === 'unsez' || i.field === 'demandNo') {
              columns2.push(i)
            } else {
              columns1.push(i)
            }
          })
          let index = columns1.findIndex((i) => i.field === 'orderSupRemark')
          if (index !== -1) {
            // 使用splice方法插入新的对象到找到的对象后面
            columns1.splice(index + 1, 0, ...columns2)
          }
          const finalColumns = []
          columnDataSort.forEach((i) => {
            columns1.forEach((j) => {
              if (j.field === i) {
                finalColumns.push(j)
              }
            })
          })
          this.columns = finalColumns
          this.searchConfigs = this.getSearchConfigsSort(cloneDeep(finalColumns))
          flag = true
        }

        if (!flag) {
          let _columnData = cloneDeep(editColumnBefore)
          let _columnData1 = cloneDeep(editColumnEnd)
          const finalColumnData = _columnData.concat(_columnData1)
          const allColumns = formatColumn(finalColumnData)
          const columns1 = []
          const columns2 = []
          allColumns.forEach((i) => {
            if (i.field === 'ihrez' || i.field === 'unsez' || i.field === 'demandNo') {
              columns2.push(i)
            } else {
              columns1.push(i)
            }
          })
          let index = columns1.findIndex((i) => i.field === 'orderSupRemark')
          if (index !== -1) {
            // 使用splice方法插入新的对象到找到的对象后面
            columns1.splice(index + 1, 0, ...columns2)
          }
          const finalColumns = []
          columnDataSort.forEach((i) => {
            columns1.forEach((j) => {
              if (j.field === i) {
                finalColumns.push(j)
              }
            })
          })
          this.columns = finalColumns
          this.searchConfigs = this.getSearchConfigsSort(cloneDeep(finalColumns))
        }
        this.purOrderDetail(code, purTenantId, businessTypeId)
      })
    },
    getSearchConfigsSort(columnData) {
      const searchConfigs = []
      searchSortSup?.forEach((i) => {
        columnData.forEach((j) => {
          if (i === j.field) {
            searchConfigs.push(j)
          }
        })
      })
      return searchConfigs
    },
    //重新赋值asynConfig
    purOrderDetail(e, purTenantId) {
      this.nowbusinessTypeCode = e
      this.nowpurTenantId = purTenantId
      this.handleCustomSearch()
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      if (code === 'Export1') {
        const selectRecords = $grid.getCheckboxRecords() || []
        this.handleExport(selectRecords)
        return
      }
    },
    handleExport(selectRecords) {
      //导出
      const { visibleColumn } = this.$refs.xTable.$refs.xGrid.getTableColumn()
      const headerMap = {}
      visibleColumn.forEach((i) => {
        if (i.field && i.title) {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.title
          }
        }
      })
      const rules = formatParams(this.searchConfigs)
      const ids = []
      selectRecords.forEach((item) => {
        ids.push(item.id)
      })
      let params = {
        condition: 'and',
        page: { current: 1, size: 5000 },
        pageFlag: true,
        rules: [
          ...rules,
          {
            condition: 'and',
            field: 'businessTypeCode',
            operator: 'equal',
            value: this.nowbusinessTypeCode
          },
          {
            condition: 'and',
            field: 'purTenantId',
            operator: 'equal',
            value: this.nowpurTenantId
          },
          {
            condition: 'and',
            field: 'id',
            operator: 'in',
            value: ids
          }
        ],
        headerMap
      }
      this.$store.commit('startLoading')

      this.$API.purchaseOrder.soDetailDownload(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.tabs-area {
  display: flex;
  align-items: center;
  .pur-tenant-select {
    margin: 17px 25px 0 25px;
  }
  .supReconTypeTabs {
    flex: 1;
  }
}
.table-area {
  padding: 12px 8px;
  background: #fff;
}
::v-deep .operator-list {
  width: 50px !important;
  // margin-right: 5px;
}
::v-deep .custom-input-number {
  width: 100%;
  .mt-input-number {
    width: 100%;
    margin-bottom: 0px;
    input {
      height: 23px;
      padding-right: 30px;
    }
  }
}
::v-deep .table-tool-bar + div {
  height: calc(-370px + 100vh) !important;
}
</style>
