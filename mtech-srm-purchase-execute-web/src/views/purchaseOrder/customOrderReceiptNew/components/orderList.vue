<template>
  <mt-template-page
    ref="templateRef"
    :template-config="componentConfig"
    @handleClickToolBar="handleClickToolBar"
    @handleClickCellTool="handleClickCellTool"
    @handleClickCellTitle="handleClickCellTitle"
  >
  </mt-template-page>
</template>

<script>
import * as CONFIG from './../config/config'
export default {
  name: '',
  data() {
    return {
      componentConfig: [
        {
          dataPermission: 'a',
          permissionCode: 'T_02_0040',
          showArchive: true, // 是否显示归档查询
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          // title: this.$t("单据视图"),
          activatedRefresh: false,
          toolbar: [
            {
              id: 'Edit',
              icon: 'icon_table_batchacceptance',
              title: this.$t('批量确认'),
              permission: ['O_02_0676']
            }
          ],
          gridId: this.$tableUUID.purchaseOrder.customReceiptTab,
          grid: {
            virtualPageSize: 30,
            enableVirtualization: true,
            // frozenColumns: 1,
            allowEditing: true, //开启表格编辑操作
            columnData: CONFIG.columnData1,
            lineIndex: 0,
            selectionSettings: {
              persistSelection: true,
              type: 'Multiple',
              checkboxOnly: true
            },
            customSelection: true, // 使用自定义勾选列
            pageSettings: {
              currentPage: 1,
              pageSize: 200,
              pageSizes: [50, 100, 200, 1000],
              totalRecordsCount: 0
            },
            showSelected: false,
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/supOrder/query'
              // ignoreDefaultSearch: this.$route.query.from === 'mytodo' ? false : true

              // rules: [
              //   {
              //     field: 'feedbackStatus',

              //     operator: 'in',
              //     value: ['0']
              //   }
              // ]
            }
          }
        }
        // {
        //   dataPermission: "b",
        //   permissionCode: "T_02_0041",
        //   activatedRefresh: false,

        //   title: this.$t("明细视图"),
        // },
      ]
    }
  },
  mounted() {
    if (this.$route.query.from === 'mytodo') {
      this.componentConfig[0].grid.asyncConfig.defaultRules = JSON.parse(
        sessionStorage.getItem('todoDetail')
      ).defaultRules
    }
    // const a = Array.from(document.querySelectorAll('li'))
    // for (let i = 0; i < a.length; i++) {
    //   if (a[i].getAttribute('permissioncode')) {
    //     for (let j = 0; j < this.contentConfig.length; j++) {
    //       console.log('getTabsIndex', a[i].getAttribute('permissioncode'), this.contentConfig[0])
    //       if (a[i].getAttribute('permissioncode') == this.contentConfig[j].permissionCode) {
    //         console.log('getTabsIndex2', this.contentConfig)
    //         this.currentTabIndex = j
    //         return
    //       }
    //     }
    //   }
    // }
  },
  watch: {
    $route(to, from) {
      if (from.name === 'custom-receipt-detail') {
        this.$refs.templateRef.refreshCurrentGridData()
        setTimeout(() => {
          this.$refs.templateRef.$el.querySelector('.e-content').scrollTop = 10
        }, 1000)
      }
    }
  },
  methods: {
    //点击顶部的操作按钮
    handleClickToolBar(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id === 'Edit') {
        const selectRows = []
        gridRef.dataSource.forEach((item) => {
          if (item.customChecked) {
            selectRows.push(item)
          }
        })
        if (selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        let _id = []
        selectRows.map((item) => _id.push(item.id))
        this.$dialog({
          data: {
            title: this.$t('批量确认'),
            message: this.$t('确认批量确认选中的订单？')
          },
          success: () => {
            let params = _id
            this.$API.purchaseOrder.supOrderbatchFeedback(params).then((res) => {
              if (res.data) {
                this.$toast({
                  content: this.$t('批量确认操作成功'),
                  type: 'success'
                })
                this.$refs.templateRef.refreshCurrentGridData()
              } else {
                this.$toast({
                  content: this.$t('批量确认失败'),
                  type: 'error'
                })
              }
            })
          }
        })
      }
    },
    //点击表格的操作按钮
    handleClickCellTool(e) {
      console.log('方法2', e)
      // 点击编辑跳转详情（提交）
      if (e.tool.id === 'edit') {
        this.$router.push({
          name: 'custom-receipt-detail',
          query: {
            id: e.data.id,
            type: '1'
          }
        })
      } else if (e.tool.id === 'uploadOrder') {
        this.$router.push({
          name: 'custom-receipt-detail',
          query: {
            id: e.data.id,
            type: '3',
            orderType: '1' // 1上传送货单 其它没有上传送货单按钮
          }
        })
      }
    },
    //点击表格数据的操作方法
    handleClickCellTitle(e) {
      console.log('方法3', e)
      //点击采购订单号跳转到详情（仅查看）
      if (e.field === 'orderCode') {
        this.$router.push({
          name: 'custom-receipt-detail',
          query: {
            id: e.data.id,
            type: '3'
          }
        })
      }
      //历史反馈
      if (e.field === 'version') {
        this.$router.push({
          name: 'custom-receipt-detail',
          query: {
            id: e.data.id,
            type: '2',
            orderCode: e.data.orderCode,
            tenantId: e.data.purTenantId
          }
        })
      }
    }
  }
}
</script>
