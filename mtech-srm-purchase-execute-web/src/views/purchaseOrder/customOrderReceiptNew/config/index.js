import UTILS from '../../../../utils/utils'
import Vue from 'vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
export const editColumnBefore = [
  //订单明细
  // {
  //   width: '50',
  //   type: 'checkbox',
  //   showInColumnChooser: false
  // },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    isPrimaryKey: true
  },
  {
    field: 'orderCode',
    headerText: i18n.t('采购订单号'),
    searchOptions: {
      placeholder: i18n.t('多个订单号需要用空格隔开'),
      operator: 'in',
      serializeValue: (e) => {
        let obj = e.split(' ')
        //自定义搜索值，规则
        return obj
      },
      maxQueryValueLength: 10000
    },
    width: '150'
  },
  {
    field: 'showStatus',
    headerText: i18n.t('订单状态'),
    width: '150',
    searchOptions: {
      elementType: 'multi-select',
      operator: 'in',
      dataSource: [
        { text: i18n.t('草稿'), value: '草稿' },
        { text: i18n.t('待审批'), value: '待审批' },
        { text: i18n.t('审批通过'), value: '审批通过' },
        { text: i18n.t('审批不通过'), value: '审批不通过' },
        { text: i18n.t('关闭'), value: '关闭' },
        { text: i18n.t('完成'), value: '完成' },
        { text: i18n.t('取消'), value: '取消' },
        { text: i18n.t('发布待确认'), value: '发布待确认' },
        { text: i18n.t('反馈正常'), value: '反馈正常' },
        { text: i18n.t('反馈异常'), value: '反馈异常' }
      ]
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('客户公司'),
    width: '250',
    searchOptions: {
      ...MasterDataSelect.companySupplier
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.companyCode}}-{{data.companyName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  }
]
export const editColumnEnd = [
  {
    field: 'version1',
    headerText: i18n.t('变更情况'),
    width: '150',
    searchOptions: {
      customRule: (e) => {
        return {
          label: '版本',
          field: 'version',
          type: 'number',
          operator: 'contains',
          value: e
        }
      }
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e > 1) {
          return i18n.t('变更')
        } else {
          return ''
        }
      }
    }
  },
  // {
  //   field: "source",
  //   headerText: i18n.t("订单来源"),
  //   width: "150",
  //   valueConverter: {
  //     type: "map",
  //     map: {
  //       0: i18n.t("采购申请转化"),
  //       1: i18n.t("手动创建"),
  //       2: i18n.t("商城申请转化"),
  //       3: i18n.t("外部SAP"),
  //       4: i18n.t("合同转换"),
  //     },
  //   },
  // },
  {
    field: 'orderTime',
    headerText: i18n.t('订单日期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型'),
    width: '150',
    searchOptions: {
      renameField: 'businessTypeCode',
      operator: 'in',
      ...MasterDataSelect.businessType
    }
  },
  {
    field: 'orderTypeName',
    headerText: i18n.t('订单类型'),
    width: '150',
    searchOptions: {
      elementType: 'remote-autocomplete',
      multiple: true,
      fields: { text: 'itemName', value: 'itemCode' },
      url: '/masterDataManagement/tenant/dict-item/dict-code-fuzzy',
      params: {
        dictCode: 'OrderType' // 字典类型编码
      },
      operator: 'in',
      recordsPosition: 'data',
      renameField: 'orderTypeCode',
      paramsKey: 'nameLike'
    }
  },
  {
    field: 'buyerGroupName',
    headerText: i18n.t('采购组织'),
    width: '150'
  },
  {
    field: 'buyerUserName',
    headerText: i18n.t('采购员'),
    width: '150'
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    width: '150',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.currencyCode}}-{{data.currencyName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'paymentName',
    headerText: i18n.t('付款条件'),
    width: '150'
  },
  {
    field: 'orderRemark',
    headerText: i18n.t('采方备注'),
    width: '150'
  },
  {
    field: 'orderSupRemark',
    headerText: i18n.t('供方备注'),
    width: '150'
  },
  {
    field: 'version',
    headerText: i18n.t('版本'),
    width: '150',
    // cellTools: [],
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return 'V' + e
      }
    }
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    width: '150'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    },
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    field: 'publishTime',
    headerText: i18n.t('发布时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    },
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    field: 'feedbackTime',
    headerText: i18n.t('反馈时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    },
    searchOptions: { ...MasterDataSelect.timeRange }
  }
]
export const columnData1 = [
  //采购明细列表
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    isPrimaryKey: true
  },
  {
    width: '150',
    field: 'purchaseStrategy',
    headerText: i18n.t('采购策略'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('标准'),
        2: i18n.t('寄售'),
        3: i18n.t('第三方')
      }
    }
  },
  {
    field: 'consignee',
    headerText: i18n.t('收货人'),
    width: '150',
    valueAccessor: function (field, data) {
      if (data?.consignee) {
        if (data.consignee.split('-')[3]) {
          return data?.consignee.split('-')[3]
        } else {
          return data?.consignee
        }
      } else {
        return ''
      }
    }
  },
  {
    field: 'techContactPerson',
    headerText: i18n.t('技术对接人'),
    width: '150',
    valueAccessor: function (field, data) {
      if (data?.techContactPerson) {
        if (data.techContactPerson.split('-')[3]) {
          return data?.techContactPerson.split('-')[3]
        } else {
          return data?.techContactPerson
        }
      } else {
        return ''
      }
    }
  },
  {
    field: 'provisionalEstimateStatus',
    headerText: i18n.t('是否暂估价'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('否'),
        1: i18n.t('是')
      }
    }
  },
  {
    field: 'returnIdentification',
    headerText: i18n.t('退货标识'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('否'),
        1: i18n.t('是')
      }
    }
  },
  {
    field: 'subjectType',
    headerText: i18n.t('科目类型'),
    width: '150',

    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('生产工单'),
        1: i18n.t('销售订单'),
        2: i18n.t('生产工单'),
        3: i18n.t('项目'),
        4: i18n.t('资产'),
        5: i18n.t('其他')
      }
    }
  },
  {
    field: 'timePromise',
    headerText: i18n.t('承诺日期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'warehouseCode',
    width: '250px',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.warehouseCode}}-{{data.warehouse}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'profitCenterName',
    width: '250px',
    headerText: i18n.t('利润中心'),
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.profitCenterCode}}-{{data.profitCenterName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    width: 225,
    field: 'siteName',
    headerText: i18n.t('地点/工厂'), // 也得是个按钮，点击出弹窗
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.siteCode}}-{{data.siteName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'purUnitName',
    headerText: i18n.t('采购单位'),
    width: 250,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.purUnitCode}}-{{data.purUnitName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'unitName',
    headerText: i18n.t('基本单位'),
    width: 200,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.unitCode}}-{{data.unitName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组代码'),
    width: 250,
    searchOptions: {
      ...MasterDataSelect.businessCodeName,
      renameField: 'buyerOrgCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'buyerOrgName',
    headerText: i18n.t('采购组名称'),
    width: 250,
    searchOptions: { ...MasterDataSelect.businessCodeName, renameField: 'buyerOrgCode' },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'contractRel',
    width: '150px',
    headerText: i18n.t('关联合同协议比那好')
  },
  {
    field: 'contractType',
    width: '150px',
    headerText: i18n.t('合同协议类型'),
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = ''
              if (this.data.contractType == '0') {
                str = i18n.t('单次采购')
              }
              if (this.data.contractType == '1') {
                str = i18n.t('框架协议')
              }
              return str
            }
          }
        })
      }
    }
  },
  {
    field: 'deliveryStatus',
    headerText: i18n.t('发货状态'),
    width: '150',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未发货'),
        1: i18n.t('部分发货'),
        2: i18n.t('全部发货')
      }
    }
  },
  {
    field: 'receiveStatus',
    headerText: i18n.t('收货状态'),
    width: '150',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未收货'),
        1: i18n.t('部分收货'),
        2: i18n.t('全部收货')
      }
    }
  },
  {
    field: 'confirmStatus',
    headerText: i18n.t('供应商确认状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('待确认'),
        1: i18n.t('反馈异常'),
        2: i18n.t('反馈正常')
      }
    },
    searchOptions: {
      elementType: 'select',
      showSelectAll: true,
      operator: 'equal',
      dataSource: [
        { value: 0, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('反馈异常'), cssClass: 'col-inactive' },
        { value: 2, text: i18n.t('反馈正常'), cssClass: 'col-inactive' }
      ]
    }
  },
  {
    field: 'requiredDeliveryDate',
    headerText: i18n.t('要求交期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'urgentStatus',
    headerText: i18n.t('加急状态'),
    width: '150',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in',
      dataSource: [
        { label: '普通', value: 0 },
        { label: '加急', value: 1 }
      ],
      fields: { text: 'label', value: 'value' }
    },
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div>
              <span :style="urgentStatus === '加急' ? {color: 'red'} : {}">{{ urgentStatus }}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          },
          computed: {
            urgentStatus() {
              return this.data.urgentStatus === 0 ? i18n.t('普通') : i18n.t('加急')
            }
          }
        })
      }
    }
  },
  {
    field: 'closeStatus',
    headerText: i18n.t('关闭状态'),
    width: '98',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未关闭'),
        1: i18n.t('已关闭')
      }
    }
  },
  {
    field: 'urgentTime',
    headerText: i18n.t('加急日期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'warehouseStatus',
    headerText: i18n.t('入库状态'),
    width: '150',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未入库'),
        1: i18n.t('部分入库'),
        2: i18n.t('全部入库')
      }
    }
  },
  {
    field: 'requestOrderMethod',
    headerText: i18n.t('独立/集中'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('独立'), 2: i18n.t('集中') }
    }
  },
  {
    field: 'purchasingCycleStart',
    width: '150',
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = ''
              if (
                this.data.purchasingCycleStart &&
                this.data.purchasingCycleStart.length === '13'
              ) {
                str = UTILS.dateFormat(Number(this.data.purchasingCycleStart))
              }
              return str
            }
          }
        })
      }
    }
  },
  {
    field: 'purchasingCycleEnd',
    width: '150',
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = ''
              if (this.data.purchasingCycleEnd && this.data.purchasingCycleEnd.length === '13') {
                str = UTILS.dateFormat(Number(this.data.purchasingCycleEnd))
              }
              return str
            }
          }
        })
      }
    }
  },
  {
    field: 'agreementCode',
    width: '150',
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = this.data.contract
              return str
            }
          }
        })
      }
    }
  },
  {
    field: 'budgetQuantity',
    width: '150',
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = this.data.forecastQty
              return str
            }
          }
        })
      }
    }
  },
  {
    field: 'orderUnitId',
    width: '150',
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = this.data.purUnitId
              return str
            }
          }
        })
      }
    }
  },
  {
    field: 'preDeliveryQty',
    headerText: i18n.t('待发货数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    field: 'deliveryQty',
    headerText: i18n.t('已发货数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    field: 'supplierPromiseQty',
    headerText: i18n.t('承诺数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    field: 'warehouseQty',
    headerText: i18n.t('已入库数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    field: 'outstandingNum',
    headerText: i18n.t('订单未清数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    field: 'preWarehouseQty',
    headerText: i18n.t('待入库数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    field: 'transitQty',
    headerText: i18n.t('在途数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    field: 'quantity',
    headerText: i18n.t('订单数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    field: 'printMaterialFileName',
    headerText: i18n.t('流水号附件'),
    width: '150',
    editRender: {},
    slots: {
      default: 'fileDefault'
    }
  }
  // {
  //   field: "orderCode",
  //   headerText: "订单号码",
  //   width: "150",
  // },
  // {
  //   field: "id",
  //   headerText: "订单id",
  //   width: "150",
  // },
]

export const columnDataSort = [
  'orderCode',
  'itemNo',
  'showStatus',
  'siteName',
  'warehouseCode',
  'warehouse',
  'itemCode',
  'itemName',
  'quantity',
  'outstandingNum',
  'orderTime',
  'orderTypeName',
  'confirmStatus',
  'purUnitName',
  'priceUnit',
  'taxTotal',
  'taxPrice',
  'freeTotal',
  'freePrice',
  'unPrice',
  'preDeliveryQty',
  'deliveryQty',
  'transitQty',
  'receiveQty',
  'warehouseQty',
  'cumReceiveQty',
  'returnIdentification',
  'receiveStatus',
  'deliveryStatus',
  'warehouseStatus',
  'closeStatus',
  'buyerUserName',
  'buyerOrgCode',
  'buyerOrgName',
  'requiredDeliveryDate',
  'timePromise',
  'itemText',
  'unitName',
  'customerOrder',
  'customerOrderLineNo',
  'workOrderRel',
  'demandNo',
  'changeRemark',
  'consignee',
  'receiveAddress',
  'supRemark',
  'urgentStatus',
  'urgentTime',
  'buyerGroupCode',
  'categoryName',
  'itemGroupCode',
  'itemGroupName',
  'contact',
  'paymentName',
  'source',
  'businessTypeName',
  'contractNo',
  'projectName',
  'prototypeMachineCode',
  'projectRowText',
  'companyName',
  'currencyCode',
  'currencyName',
  'unsez',
  'ihrez',
  'supSalesOrderNo',
  'syncSupSysStatus',
  'syncSupSysFailDesc',
  'remark',
  'orderRemark',
  'orderSupRemark',
  'version',
  'publishTime',
  'feedbackTime',
  'updateUserName',
  'updateTime',
  'vrPn',
  'deliveryCompletedFlag',
  'domesticDemandCode',
  'printMaterialSerialNumber',
  'printMaterialFileName'
]

export const searchSortSup = [
  'siteName',
  'itemCode',
  'showStatus',
  'orderCode',
  'confirmStatus',
  'outstandingNum',
  'requiredDeliveryDate',
  'orderTime',
  'buyerUserName',
  'buyerOrgName',
  'consignee',
  'deliveryStatus',
  'receiveStatus',
  'closeStatus',
  'orderTypeName',
  'feedbackTime',
  'changeRemark',
  'itemText',
  'itemNo',
  'workOrderRel',
  'warehouseCode',
  'returnIdentification',
  'customerOrder',
  'demandNo',
  'urgentStatus'
]
