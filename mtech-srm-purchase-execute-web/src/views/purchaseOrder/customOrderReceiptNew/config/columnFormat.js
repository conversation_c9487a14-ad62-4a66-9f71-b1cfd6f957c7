import { i18n } from '@/main.js'
import UTILS from '../../../../utils/utils'

export const formatColumn = (column) => {
  const columnData = [
    {
      type: 'checkbox',
      width: 50,
      ignore: true,
      fixed: 'left'
    }
  ]
  if (!column) {
    return columnData
  }
  column.forEach((i) => {
    if (i.visible !== false && i.headerText) {
      // 将动态列配置与动态查询条件数据关联配置组装到一起
      const columnItem = {
        // ...i,
        width: i.width,
        title: i.headerText,
        label: i.headerText,
        field: i.field,
        searchOptions: {
          value: null,
          type: 'string',
          operator: 'contains'
        }
      }
      if (i.searchOptions) {
        columnItem.searchOptions = {
          ...columnItem.searchOptions,
          ...i.searchOptions
        }
        if (i.searchOptions?.elementType === 'number') {
          columnItem.searchOptions.operator = 'equal'
          columnItem.searchOptions.dataSource = [
            { text: '=', value: 'equal' },
            { text: '≠', value: 'notequal' },
            { text: '>', value: 'greaterthan' },
            { text: '≥', value: 'greaterthanorequal' },
            { text: '<', value: 'lessthan' },
            { text: '≤', value: 'lessthanorequal' }
          ]
        }
      }
      if (i.valueConverter) {
        const dataSource = []
        for (const key in i.valueConverter.map) {
          dataSource.push({
            text: i.valueConverter.map[key],
            value: Number(key)
          })
        }
        columnItem.searchOptions = {
          ...columnItem.searchOptions,
          dataSource
        }
      }
      if (i.field === 'companyName') {
        columnItem.formatter = ({ cellValue, row }) => {
          return row.companyCode + '-' + cellValue
        }
      } else if (i.field === 'version1') {
        columnItem.formatter = ({ cellValue }) => {
          return cellValue > 1 ? i18n.t('变更') : ''
        }
      } else if (i.field === 'showStatus') {
        // 订单状态
        columnItem.formatter = ({ cellValue }) => {
          let item = i.searchOptions?.dataSource?.find((item) => item.value === cellValue)
          return item ? item.text : ''
        }
      } else if (
        i.field === 'orderTime' ||
        i.field === 'timePromise' ||
        i.field === 'requiredDeliveryDate' ||
        i.field === 'urgentTime'
      ) {
        // 订单日期、承诺日期、要求交期、加急日期
        columnItem.formatter = ({ cellValue }) => {
          if (cellValue && cellValue.length === 13) {
            const e = Number(cellValue)
            return UTILS.dateFormat(e, 'Y-m-d')
          } else {
            return ''
          }
        }
      } else if (i.field === 'currencyName') {
        // 币种
        columnItem.formatter = ({ cellValue, row }) => {
          return row.currencyCode + '-' + cellValue
        }
      } else if (i.field === 'warehouseCode') {
        // 币种
        columnItem.formatter = ({ cellValue, row }) => {
          return cellValue + '-' + row.warehouse
        }
      } else if (i.field === 'siteName') {
        // 地点/工厂
        columnItem.formatter = ({ cellValue, row }) => {
          return row.siteCode + '-' + cellValue
        }
      } else if (i.field === 'profitCenterName') {
        // 利润中心
        columnItem.formatter = ({ cellValue, row }) => {
          return row.profitCenterCode + '-' + cellValue
        }
      } else if (i.field === 'purUnitName') {
        // 采购单位
        columnItem.formatter = ({ cellValue, row }) => {
          return row.purUnitCode + '-' + cellValue
        }
      } else if (i.field === 'unitName') {
        // 基本单位
        columnItem.formatter = ({ cellValue, row }) => {
          return row.unitCode + '-' + cellValue
        }
      } else if (i.field === 'buyerOrgName') {
        // 采购组名称
        columnItem.formatter = ({ cellValue, row }) => {
          return row.buyerOrgCode + '-' + cellValue
        }
      } else if (i.field === 'version') {
        columnItem.formatter = ({ cellValue }) => {
          return 'V' + cellValue
        }
      } else if (i.field === 'contractType') {
        // 合同协议类型
        columnItem.formatter = ({ cellValue }) => {
          let str = ''
          if (cellValue == '0') {
            str = i18n.t('单次采购')
          }
          if (cellValue == '1') {
            str = i18n.t('框架协议')
          }
          return str
        }
      } else if (
        i.field === 'updateTime' ||
        i.field === 'publishTime' ||
        i.field === 'feedbackTime' ||
        i.field === 'purchasingCycleStart' ||
        i.field === 'purchasingCycleEnd'
      ) {
        // 更新时间、发布时间、反馈时间
        columnItem.formatter = ({ cellValue }) => {
          if (cellValue && cellValue.length === 13) {
            const e = Number(cellValue)
            return UTILS.dateFormat(e)
          } else {
            return ''
          }
        }
      } else if (
        i.field === 'purchaseStrategy' ||
        i.field === 'provisionalEstimateStatus' ||
        i.field === 'returnIdentification' ||
        i.field === 'subjectType' ||
        i.field === 'deliveryStatus' ||
        i.field === 'receiveStatus' ||
        i.field === 'confirmStatus' ||
        i.field === 'closeStatus' ||
        i.field === 'warehouseStatus' ||
        i.field === 'requestOrderMethod'
      ) {
        // 采购策略、是否暂估价、退货标识、科目类型、发货状态、收货状态、供应商确认状态、关闭状态、入库状态、独立/集中
        columnItem.formatter = ({ cellValue }) => {
          let text = ''
          if (cellValue || cellValue === 0) {
            text = i.valueConverter?.map[String(cellValue)]
          }
          return text
        }
      } else if (i.field === 'consignee' || i.field === 'techContactPerson') {
        // 收货人、技术对接人
        columnItem.formatter = ({ cellValue }) => {
          if (cellValue) {
            if (cellValue.split('-')[3]) {
              return cellValue.split('-')[3]
            } else {
              return cellValue
            }
          } else {
            return ''
          }
        }
      } else if (i.field === 'urgentStatus') {
        // 加急状态
        columnItem.type = 'html'
        columnItem.formatter = ({ cellValue }) => {
          const str = cellValue === 0 ? i18n.t('普通') : i18n.t('加急')
          const color = cellValue === 0 ? '' : 'color: red'
          return `<span style="${color}">${str}</span>`
        }
      } else if (i.field === 'agreementCode') {
        columnItem.formatter = ({ row }) => {
          return row.contract
        }
      } else if (i.field === 'budgetQuantity') {
        columnItem.formatter = ({ row }) => {
          return row.forecastQty
        }
      } else if (i.field === 'orderUnitId') {
        columnItem.formatter = ({ row }) => {
          return row.purUnitId
        }
      } else if (i.field === 'printMaterialFileName') {
        columnItem.editRender = {}
        columnItem.slots = { default: 'fileDefault' }
      }
      if (!columnData.some((i) => i.title === columnItem.title)) {
        columnData.push(columnItem)
      }
    }
  })
  return columnData
}

export const formatParams = (searchConfigs) => {
  const searchRules = []
  searchConfigs.forEach((item) => {
    if (
      ((item?.searchOptions?.value && item?.searchOptions?.value?.length) ||
        item?.searchOptions?.value === 0 ||
        (!isNaN(item?.searchOptions?.value) && item?.searchOptions?.value !== null)) &&
      item?.searchOptions?.value !== ''
    ) {
      let value = item?.searchOptions?.value
      if (item?.searchOptions?.elementType === 'date-range') {
        value = [
          Number(new Date(value[0].toString())),
          Number(new Date(value[1].toString())) + Number(86400000 - 1440000)
        ]
      }
      if (Array.isArray(value) && value.length === 0) {
        return false
      }
      let option = {
        label: item.label,
        field: item?.searchOptions?.renameField ? item?.searchOptions?.renameField : item.field,
        type: item?.searchOptions?.type,
        operator: item?.searchOptions?.operator,
        value
      }
      if (item?.searchOptions && item?.searchOptions?.customRule) {
        option = item?.searchOptions?.customRule(value)
      }
      searchRules.push(option)
    }
  })
  return searchRules
}
