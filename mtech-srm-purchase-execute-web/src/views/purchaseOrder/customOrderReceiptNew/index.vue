<template>
  <div class="approval-config">
    <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
    <orderList ref="orderList" class="flex-one" v-show="tabTitle === $t('单据视图')" />
    <detailList ref="detailList" class="flex-one" v-show="tabTitle === $t('明细视图')" />
  </div>
</template>

<script>
export default {
  components: {
    orderList: () => import('./components/orderList.vue'),
    detailList: () => import('./components/detailList.vue')
  },
  data() {
    return {
      tabTitle: this.$t('单据视图'),
      tabSource: [
        // {
        //   title: this.$t('单据视图')
        // },
        // {
        //   title: this.$t('明细视图')
        // }
      ]
    }
  },
  created() {
    const elementPermissionSet = window.elementPermissionSet
    if (elementPermissionSet.includes('T_02_0040')) {
      this.tabSource.push({
        title: this.$t('单据视图')
      })
      this.tabTitle = this.$t('单据视图')
    }
    if (elementPermissionSet.includes('T_02_0041')) {
      this.tabSource.push({
        title: this.$t('明细视图')
      })
    }
  },
  methods: {
    // 顶部筛选 调整
    handleSelectTab(e, args) {
      this.tabTitle = args.title
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('todoDetail')
  }
}
</script>

<style scoped lang="scss">
.approval-config {
  height: 100%;
  display: flex;
  flex-direction: column;
  .flex-one {
    flex: 1;
  }
}
.full-height {
  /deep/ .supReconTypeTabs {
    flex: 1;
    display: flex;

    .mt-tabs-container {
      width: 100%;
    }
  }
}
</style>
