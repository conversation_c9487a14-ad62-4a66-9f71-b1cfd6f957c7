import { i18n } from '@/main.js'
// import Vue from "vue";
import { DropDownList } from '@syncfusion/ej2-dropdowns'
import { NumericTextBox } from '@syncfusion/ej2-inputs'
import { Query } from '@syncfusion/ej2-data'
export const dataSource = [
  {
    code: '482ROF',
    name: i18n.t('编码生成器'),
    account: 10,
    businessType: 'Victoria',
    businessName: '',
    createTime: '2021-09-21'
  },
  {
    code: '482ROF222',
    name: i18n.t('编码生成器2'),
    account: 20,
    businessType: 'Queensland',
    businessName: '',
    createTime: '2021-09-21'
  }
]

// 初始共计41列
var subjectTotalEle,
  subjectTotalObj,
  taxedUnitPriceObj,
  deductionElem,
  deductionObj,
  supplierIdElem,
  supplierIdObj
import Vue from 'vue'
import cellUpload from '@/components/Upload/cellUpload'

export const editData = [
  {
    item_no: 1,
    file: ''
  },
  {
    item_no: 2
  },
  {
    item_no: 2
  }
]

export const editColumnBefore = [
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false,
    showInColumnChooser: false
  },
  {
    width: '80',
    field: 'indexDefine',
    headerText: i18n.t('序号'),
    allowEditing: false
  }
]

import { getComponent } from '@syncfusion/ej2-base'
var bigDecimal = require('js-big-decimal')

var grid,
  GridId = 'Grid3',
  latestIpt = '' // 最后输入的字段 含税单价/未税单价;
export const editColumn = (
  taxidData,
  currencyNameData,
  tradeClauseNameData,
  shippingMethodNameData,
  receiveUserIdData,
  supplierIdData
  // qualityExemptionMarkIdData
) => {
  return [
    {
      field: 'itemNo',
      headerText: i18n.t('行号'),
      // isPrimaryKey: true,
      allowEditing: false,
      editType: 'numericedit'
      // validationRules: { required: true },
    },
    // 此处四个接口：物料接口、SKU接口、根据物料ID获取工厂（地点/工厂）、根据工厂ID和物料ID获取采购组、基本单位、采购单位
    {
      field: 'itemCode',
      headerText: i18n.t('物料/品项编码'), // 带出：物料编码、物料名称、规格型号，清空：SKU编码、sku名称
      editType: 'stringedit',
      // template能给到index，而editTemplate没给..但template在获取未保存的行的序号时，也不对
      editTemplate: function (args) {
        return {
          template: Vue.component('selectItemCode', {
            template: `<div @click="handleChooseItemCode">
              <span v-if="data.itemCode"> {{data.itemCode}} </span>
              <span v-else> 选择物料 </span>
            </div>`,
            data() {
              return { data: { data: {}, rowIndex: -1 } }
            },
            mounted() {
              grid = new getComponent(GridId, 'grid')
              this.rowIndex = grid.editModule.editModule.cellDetails.rowIndex
              this.$bus.$on('setItemCode1', (fieldCode, e, rowIndex) => {
                if (fieldCode != 'itemCode') return
                if (this.rowIndex != rowIndex) return
                console.log('监听到了setItemCode1,物料', fieldCode, e)
                grid.updateCell(this.rowIndex, 'itemCode', e.itemCode)
                grid.updateCell(this.rowIndex, 'itemName', e.itemName)
                grid.updateCell(this.rowIndex, 'specification', e.itemDescription)
                grid.updateCell(this.rowIndex, 'skuName', '')
                grid.updateCell(this.rowIndex, 'skuCode', '')
                grid.updateCell(this.rowIndex, 'siteName', '') // 修改了物料后，工厂、采购组、基本单位、采购单位、质量免检标识要清空
                grid.updateCell(this.rowIndex, 'buyerOrgId', '')
                grid.updateCell(this.rowIndex, 'buyerOrgCode', '')
                grid.updateCell(this.rowIndex, 'buyerOrgName', '')
                grid.updateCell(this.rowIndex, 'unitName', '')
                grid.updateCell(this.rowIndex, 'purUnitName', '')
              })
            },
            methods: {
              handleChooseItemCode() {
                console.log('handleChooseItemCode', this.data, args)
                let params = {
                  fieldCode: 'itemCode',
                  rowIndex: this.rowIndex,
                  title: '选择物料/品项编码',
                  requestUrl: 'getItemByKeyword',
                  addId: this.data.addId,
                  id: this.data.id
                }
                this.$parent.$emit('handleChooseItemCode', JSON.stringify(params))
              }
            }
          })
        }
      }
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料/品项名称'),
      allowEditing: false
    },
    {
      field: 'skuCode',
      headerText: i18n.t('SKU编码'), // 带出 物料编码、物料名称、SKU名称、规格型号、品类编码、品类名称
      editTemplate: function () {
        return {
          template: Vue.component('selectItemCode', {
            template: `<div @click="handleChooseItemCode">
              <span v-if="data.skuCode"> {{data.skuCode}} </span>
              <span v-else> 选择SKU </span>
            </div>`,
            data() {
              return { data: { data: {}, rowIndex: -1 } }
            },
            mounted() {
              grid = new getComponent(GridId, 'grid')
              this.rowIndex = grid.editModule.editModule.cellDetails.rowIndex
              this.$bus.$on('setItemCode1', (fieldCode, e, rowIndex) => {
                if (fieldCode != 'skuCode') return
                if (this.rowIndex != rowIndex) return
                console.log('监听到了setItemCode1,sku', fieldCode, e)
                grid.updateCell(this.rowIndex, 'skuCode', e.barCode)
                grid.updateCell(this.rowIndex, 'skuName', e.name)
                grid.updateCell(this.rowIndex, 'itemCode', e.itemCode)
                grid.updateCell(this.rowIndex, 'itemName', e.itemName)
                // // grid.updateCell(this.rowIndex, "categoryId", "10");
                // grid.updateCell(this.rowIndex, "categoryName", e.currencyId);
                grid.updateCell(this.rowIndex, 'specification', e.specificationModel)
                grid.updateCell(this.rowIndex, 'siteName', '') // 修改了SKU后，工厂、采购组、基本单位、采购单位、质量免检标识要清空
                grid.updateCell(this.rowIndex, 'buyerOrgId', '')
                grid.updateCell(this.rowIndex, 'buyerOrgCode', '')
                grid.updateCell(this.rowIndex, 'buyerOrgName', '')
                grid.updateCell(this.rowIndex, 'unitName', '')
                grid.updateCell(this.rowIndex, 'purUnitName', '')
                grid.updateCell(this.rowIndex, 'handleChooseItemCode', '')
              })
            },
            methods: {
              handleChooseItemCode() {
                console.log('handleChooseItemCode', this.data)
                let params = {
                  fieldCode: 'skuCode',
                  rowIndex: this.rowIndex,
                  title: '选择SKU编码',
                  requestUrl: 'getSKU',
                  addId: this.data.addId,
                  id: this.data.id
                }
                this.$parent.$emit('handleChooseItemCode', JSON.stringify(params))
              }
            }
          })
        }
      }
    },
    {
      field: 'skuName',
      headerText: i18n.t('SKU名称'),
      allowEditing: false
    },
    {
      field: 'specification',
      headerText: i18n.t('规格型号'),
      allowEditing: false
    },
    {
      field: 'deliveryStatus',
      headerText: i18n.t('发货状态'),
      editType: 'dropdownedit',
      editTemplate: function () {
        return {
          template: Vue.component('changeSelect', {
            template: `<div>
                <mt-select
                  ref="businessRef"
                  v-model="data.deliveryStatus"
                  :data-source="deliveryStatusOptions"
                  :show-clear-button="true"
                  :fields="{ value: 'value', text: 'label' }"
                  @change="handleChangeCurrency"
                  :placeholder="$t('请选择发货状态')"
                ></mt-select>
            </div>`,
            data() {
              return {
                deliveryStatusOptions: [
                  { label: i18n.t('未发货'), value: '0' },
                  { label: i18n.t('部分发货'), value: '1' },
                  { label: i18n.t('全部发货'), value: '2' }
                ],
                data: { data: {}, rowIndex: -1 }
              }
            },
            mounted() {
              grid = new getComponent(GridId, 'grid')
              this.rowIndex = grid.editModule.editModule.cellDetails.rowIndex
            },
            methods: {
              handleChangeCurrency(e) {
                console.log(i18n.t('选择了发货状态'), e, this.rowIndex)
                grid.updateCell(this.rowIndex, 'deliveryStatus', e.itemData.label)
                let params = {
                  fieldCode: 'deliveryStatus',
                  rowIndex: this.rowIndex,
                  itemInfo: e.itemData,
                  addId: this.data.addId,
                  id: this.data.id
                }
                this.$parent.$parent.$emit('handleSetSelectedInfo', JSON.stringify(params))
              }
            }
          })
        }
      }
    },
    {
      field: 'confirmStatus',
      headerText: i18n.t('供应商确认状态'),
      editType: 'dropdownedit',
      editTemplate: function () {
        return {
          template: Vue.component('changeSelect', {
            template: `<div>
                <mt-select
                  ref="businessRef"
                  v-model="data.confirmStatus"
                  :data-source="confirmStatusOptions"
                  :show-clear-button="true"
                  :fields="{ value: 'value', text: 'label' }"
                  @change="handleChangeCurrency"
                  :placeholder="$t('请选择供应商确认状态')"
                ></mt-select>
            </div>`,
            data() {
              return {
                confirmStatusOptions: [
                  { label: i18n.t('待确认'), value: '0' },
                  { label: i18n.t('确认'), value: '1' },
                  { label: i18n.t('拒绝'), value: '2' }
                ],
                data: { data: {}, rowIndex: -1 }
              }
            },
            mounted() {
              grid = new getComponent(GridId, 'grid')
              this.rowIndex = grid.editModule.editModule.cellDetails.rowIndex
            },
            methods: {
              handleChangeCurrency(e) {
                console.log(i18n.t('选择了供应商确认状态'), e, this.rowIndex)
                grid.updateCell(this.rowIndex, 'confirmStatus', e.itemData.label)
                let params = {
                  fieldCode: 'confirmStatus',
                  rowIndex: this.rowIndex,
                  itemInfo: e.itemData,
                  addId: this.data.addId,
                  id: this.data.id
                }
                this.$parent.$parent.$emit('handleSetSelectedInfo', JSON.stringify(params))
              }
            }
          })
        }
      }
    },
    {
      field: 'warehouseStatus',
      headerText: i18n.t('入库状态'),
      editType: 'dropdownedit',
      editTemplate: function () {
        return {
          template: Vue.component('changeSelect', {
            template: `<div>
                <mt-select
                  ref="businessRef"
                  v-model="data.warehouseStatus"
                  :data-source="warehouseStatusOptions"
                  :show-clear-button="true"
                  :fields="{ value: 'value', text: 'label' }"
                  @change="handleChangeCurrency"
                  :placeholder="$t('请选择入库状态')"
                ></mt-select>
            </div>`,
            data() {
              return {
                warehouseStatusOptions: [
                  { label: i18n.t('未入库'), value: '0' },
                  { label: i18n.t('部分入库'), value: '1' },
                  { label: i18n.t('全部入库'), value: '2' }
                ],
                data: { data: {}, rowIndex: -1 }
              }
            },
            mounted() {
              grid = new getComponent(GridId, 'grid')
              this.rowIndex = grid.editModule.editModule.cellDetails.rowIndex
            },
            methods: {
              handleChangeCurrency(e) {
                console.log(i18n.t('选择了入库状态'), e, this.rowIndex)
                grid.updateCell(this.rowIndex, 'warehouseStatus', e.itemData.label)
                let params = {
                  fieldCode: 'warehouseStatus',
                  rowIndex: this.rowIndex,
                  itemInfo: e.itemData,
                  addId: this.data.addId,
                  id: this.data.id
                }
                this.$parent.$parent.$emit('handleSetSelectedInfo', JSON.stringify(params))
              }
            }
          })
        }
      }
      // edit: {
      //   create: () => {
      //     taxIdEle = document.createElement("input");
      //     return taxIdEle;
      //   },
      //   read: () => {
      //     return warehouseStatusObj.text;
      //   },
      //   destroy: () => {
      //     warehouseStatusObj.destroy();
      //   },
      //   write: () => {
      //     warehouseStatusObj = new DropDownList({
      //       dataSource: [
      //         // { label: i18n.t("未入库"), id: "0" },
      //         // { label: i18n.t("部分入库"), id: "1" },
      //         // { label: i18n.t("全部入库"), id: "2" },
      //         { label: "2", id: "2" },
      //       ],
      //       fields: { value: "id", text: "label" },
      //       placeholder: i18n.t("请选择入库状态"),
      //       floatLabelType: "Never",
      //     });
      //     warehouseStatusObj.appendTo(taxIdEle);
      //   },
      // },
    },
    {
      field: 'categoryName',
      headerText: i18n.t('品类'),
      allowEditing: false
    },
    {
      field: 'siteName',
      headerText: i18n.t('地点/工厂'), // 也得是个按钮，点击出弹窗
      editTemplate: function () {
        return {
          template: Vue.component('selectItemCode', {
            template: `<div @click="handleChooseItemCode">
              <span v-if="data.skuCode && data.siteName"> {{data.siteName}} </span>
              <span v-else> 选择工厂 </span>
            </div>`,
            data() {
              return { data: { data: {}, rowIndex: -1 } }
            },
            mounted() {
              grid = new getComponent(GridId, 'grid')
              this.rowIndex = grid.editModule.editModule.cellDetails.rowIndex

              this.$bus.$on('setItemCode1', (fieldCode, e, rowIndex) => {
                if (fieldCode != 'siteName') return
                if (this.rowIndex != rowIndex) return
                console.log('监听到了setItemCode1,工厂', fieldCode, e)
                grid.updateCell(this.rowIndex, 'siteName', e.organizationName) // 根据工厂ID和物料ID获取采购组、基本单位、采购单位
                grid.updateCell(this.rowIndex, 'buyerOrgId', e.buyerOrgId) //采购组id
                grid.updateCell(this.rowIndex, 'buyerOrgCode', e.buyerOrgCode) //采购组code
                grid.updateCell(this.rowIndex, 'buyerOrgName', e.buyerOrgName) //采购组名称
                grid.updateCell(this.rowIndex, 'unitName', e.unitName) //基本单位名称
                grid.updateCell(this.rowIndex, 'purUnitName', e.orderUnitName) //采购单位名称
                grid.updateCell(this.rowIndex, 'categoryId', e.categoryId) //品类
                grid.updateCell(this.rowIndex, 'categoryName', e.categoryName) //品类
                grid.updateCell(
                  this.rowIndex,
                  'qualityExemptionMarkName',
                  e.qualityExemptionMarkName
                ) //质量免检标识
              })
            },
            methods: {
              handleChooseItemCode() {
                console.log('handleChooseItemCode', this.data)
                if (!(this.data.itemCode || this.data.skuCode)) {
                  this.$parent.$toast({ content: '请先选择物料编码或SKU编码' })
                  return
                }
                let params = {
                  fieldCode: 'siteName',
                  rowIndex: this.rowIndex,
                  title: i18n.t('选择工厂'),
                  requestUrl: 'getFactoryList',
                  itemId: this.data.itemId,
                  addId: this.data.addId,
                  id: this.data.id
                }
                this.$parent.$emit('handleChooseItemCode', JSON.stringify(params))
              }
            }
          })
        }
      }
    },
    {
      field: 'buyerOrgId',
      headerText: i18n.t('采购组'),
      allowEditing: false
    },
    {
      field: 'buyerOrgCode',
      headerText: i18n.t('采购组代码'),
      allowEditing: false
    },
    {
      field: 'buyerOrgName',
      headerText: i18n.t('采购组名称'),
      allowEditing: false
    },
    {
      field: 'unitName',
      headerText: i18n.t('基本单位'),
      allowEditing: false
    },
    {
      field: 'purUnitName',
      headerText: i18n.t('采购单位'),
      allowEditing: false
    },
    {
      field: 'quantity',
      headerText: i18n.t('订单数量'),
      editType: 'numericedit',
      edit: {
        params: {
          change: (e) => {
            grid = new getComponent(GridId, 'grid')
            var { rowIndex, rowData } = grid.editModule.editModule.cellDetails
            console.log(i18n.t('订单数量改变'), grid, e)
            // 如果清空了：要清空预算未税总价、未税总价、含税总价、某个单价
            if (!e.value) {
              grid.updateCell(rowIndex, 'budgetTotalPrice', 0)
              grid.updateCell(rowIndex, 'freeTotal', 0)
              grid.updateCell(rowIndex, 'taxTotal', 0)
              if (latestIpt == 'freePrice') {
                grid.updateCell(rowIndex, 'taxPrice', 0)
              } else if (latestIpt == 'taxPrice') {
                grid.updateCell(rowIndex, 'freePrice', 0)
              }
              return
            }
            // 如果有 预算单价（未税） 就能计算出 预算总价（未税）
            if (rowData.budgetUnitPrice) {
              grid.updateCell(
                rowIndex,
                'budgetTotalPrice',
                bigDecimal.multiply(rowData.budgetUnitPrice, e.value)
              )
            }

            // 如果有 预算单价（含税） 就能计算出 预算总价（含税）
            if (rowData.taxedUnitPrice) {
              grid.updateCell(
                rowIndex,
                'taxedTotalPrice',
                bigDecimal.multiply(rowData.taxedUnitPrice, e.value)
              )
            }

            if (latestIpt == 'taxPrice') {
              // 如果有 含税单价 就可计算出含税总价，再有税率，就能计算出 未税总价、未税单价
              let _taxTotal = bigDecimal.multiply(rowData.taxPrice, e.value) // 含税总价
              grid.updateCell(rowIndex, 'taxTotal', _taxTotal)
              if (rowData.taxid) {
                let _tax = bigDecimal.divide(rowData.taxid, 100)
                let _freeTotal = bigDecimal.divide(_taxTotal, bigDecimal.add(1, _tax))
                grid.updateCell(rowIndex, 'freeTotal', _freeTotal)
                grid.updateCell(rowIndex, 'freePrice', bigDecimal.divide(_freeTotal, e.value))
              }
            } else {
              // 如果有 未税单价，就可计算出未税总价，再有税率，就可计算出含税总价、含税单价
              let _freeTotal = bigDecimal.multiply(rowData.freePrice, e.value) // 未税总价
              grid.updateCell(rowIndex, 'freeTotal', _freeTotal)
              if (rowData.taxid) {
                let _tax = bigDecimal.divide(rowData.taxid, 100)
                let _taxTotal = bigDecimal.multiply(_freeTotal, bigDecimal.add(1, _tax))
                grid.updateCell(rowIndex, 'taxTotal', _taxTotal)
                grid.updateCell(rowIndex, 'taxPrice', bigDecimal.divide(_taxTotal, e.value))
              }
            }
          }
        }
      }
    },
    {
      field: 'freePrice',
      headerText: i18n.t('未税单价'),
      type: 'number',
      valueAccessor: function (field, data) {
        return data['freePrice'] ? parseInt(data['freePrice'] * 100) / 100 : 0
      },
      editType: 'numericedit',
      edit: {
        params: {
          change: (e) => {
            latestIpt = 'freePrice'
            grid = new getComponent(GridId, 'grid')
            var { rowIndex, rowData } = grid.editModule.editModule.cellDetails
            console.log(i18n.t('未税单价改变'), e, grid)
            // 如果清空了：要清空 未税总价、含税总价、含税单价
            if (!e.value || !rowData.quantity) {
              grid.updateCell(rowIndex, 'freeTotal', 0)
              grid.updateCell(rowIndex, 'taxTotal', 0)
              grid.updateCell(rowIndex, 'freePrice', 0)
              return
            }
            // 如果有 订单数量，就可计算出未税总价，再有税率，就可计算出含税总价、含税单价
            let _freeTotal = bigDecimal.multiply(rowData.quantity, e.value) // 未税总价
            grid.updateCell(rowIndex, 'freeTotal', _freeTotal)
            if (rowData.taxid) {
              let _tax = bigDecimal.divide(rowData.taxid, 100)
              let _taxTotal = bigDecimal.multiply(_freeTotal, bigDecimal.add(1, _tax))
              grid.updateCell(rowIndex, 'taxTotal', _taxTotal)
              grid.updateCell(rowIndex, 'taxPrice', bigDecimal.divide(_taxTotal, rowData.quantity))
            }
          }
        }
      }
    },
    {
      field: 'freeTotal',
      headerText: i18n.t('未税总价'),
      allowEditing: false,
      valueAccessor: function (field, data) {
        return data['freeTotal'] ? parseInt(data['freeTotal'] * 100) / 100 : 0
      }
    },
    {
      field: 'taxPrice',
      headerText: i18n.t('含税单价'),
      valueAccessor: function (field, data) {
        return data['taxPrice'] ? parseInt(data['taxPrice'] * 100) / 100 : 0
      },
      editType: 'numericedit',
      edit: {
        params: {
          change: (e) => {
            latestIpt = 'taxPrice'
            grid = new getComponent(GridId, 'grid')
            var { rowIndex, rowData } = grid.editModule.editModule.cellDetails
            console.log(i18n.t('含税单价改变'), e, grid)
            // 如果清空了：要清空 未税总价、含税总价、含税单价
            if (!e.value || !rowData.quantity) {
              grid.updateCell(rowIndex, 'freeTotal', 0)
              grid.updateCell(rowIndex, 'taxTotal', 0)
              grid.updateCell(rowIndex, 'freePrice', 0)
              return
            }
            // 如果有 订单数量，就可计算出含税总价，再有税率，就可计算出未税总价、未税单价
            let _taxTotal = bigDecimal.multiply(rowData.quantity, e.value) // 含税总价
            grid.updateCell(rowIndex, 'taxTotal', _taxTotal)
            if (rowData.taxid) {
              let _tax = bigDecimal.divide(rowData.taxid, 100)
              let _freeTotal = bigDecimal.multiply(_taxTotal, bigDecimal.add(1, _tax))
              grid.updateCell(rowIndex, 'freeTotal', _freeTotal)
              grid.updateCell(
                rowIndex,
                'freePrice',
                bigDecimal.multiply(_freeTotal, rowData.quantity)
              )
            }
          }
        }
      }
    },
    {
      field: 'taxTotal',
      headerText: i18n.t('含税总价'),
      valueAccessor: function (field, data) {
        return data['taxTotal'] ? parseInt(data['taxTotal'] * 100) / 100 : 0
      },
      allowEditing: false
    },
    {
      field: 'taxid',
      headerText: i18n.t('税率（%）'),
      editType: 'dropdownedit',
      edit: {
        params: {
          dataSource: taxidData,
          fields: { value: 'id', text: 'taxRate' },
          placeholder: i18n.t('请选择税率'),
          showClearButton: true,
          query: new Query(),
          change: (e) => {
            console.log(i18n.t('税率改变了'), e)
            grid = new getComponent(GridId, 'grid')
            var { rowIndex, rowData } = grid.editModule.editModule.cellDetails
            // 如果清空了：要清空 未税总价、含税总价、某个单价
            if (!e.value) {
              grid.updateCell(rowIndex, 'freeTotal', 0)
              grid.updateCell(rowIndex, 'taxTotal', 0)
              if (latestIpt == 'freePrice') {
                grid.updateCell(rowIndex, 'taxPrice', 0)
              } else if (latestIpt == 'taxPrice') {
                grid.updateCell(rowIndex, 'freePrice', 0)
              }
              return
            }

            if (latestIpt == 'taxPrice') {
              // 如果有 含税单价,含税总价不用变；再有税率，就能计算出 未税总价，再有数量，就能算未税单价
              let _taxTotal = rowData.taxTotal // 含税总价
              if (e.itemData.value) {
                let _tax = bigDecimal.divide(e.itemData.value, 100)
                let _freeTotal = bigDecimal.divide(_taxTotal, bigDecimal.add(1, _tax))
                grid.updateCell(rowIndex, 'freeTotal', _freeTotal)
                if (rowData.quantity) {
                  grid.updateCell(
                    rowIndex,
                    'freePrice',
                    bigDecimal.divide(_freeTotal, rowData.quantity)
                  )
                }
              }
            } else {
              // 如果有 未税单价,未税总价不用变；再有税率，就能计算出 含税总价，再有数量，就能算含税单价
              let _freeTotal = rowData.freeTotal // 含税总价
              if (e.itemData.value) {
                let _tax = bigDecimal.divide(e.itemData.value, 100)
                let _taxTotal = bigDecimal.multiply(_freeTotal, bigDecimal.add(1, _tax))
                grid.updateCell(rowIndex, 'taxTotal', _taxTotal)
                if (rowData.quantity) {
                  grid.updateCell(
                    rowIndex,
                    'taxPrice',
                    bigDecimal.divide(_taxTotal, rowData.quantity)
                  )
                }
              }
            }
          }
        }
      }
    },
    {
      field: 'budgetQuantity',
      headerText: i18n.t('预测采购量'),
      editType: 'numericedit'
    },
    {
      field: 'startDate',
      headerText: i18n.t('预测采购周期起'),
      editType: 'datePickerEdit'
    },
    {
      field: 'endDate',
      headerText: i18n.t('预测采购周期止'),
      editType: 'datePickerEdit'
    },
    {
      field: 'requiredDeliveryDate',
      headerText: i18n.t('要求交期'),
      editType: 'dateTimePickerEdit',
      type: 'dateTime',
      format: 'yyyy-MM-dd HH:mm',
      width: '180'
    },
    {
      field: 'budgetUnitPrice',
      headerText: i18n.t('预算单价（未税）'),
      valueAccessor: function (field, data) {
        return data['budgetUnitPrice'] ? parseInt(data['budgetUnitPrice'] * 100) / 100 : 0
      },
      editType: 'numericedit',
      edit: {
        params: {
          change: (e) => {
            grid = new getComponent(GridId, 'grid')
            var { rowIndex, rowData } = grid.editModule.editModule.cellDetails
            if (rowData.quantity) {
              grid.updateCell(
                rowIndex,
                'budgetTotalPrice',
                bigDecimal.multiply(rowData.quantity, e.value)
              )
            }
          }
        }
      }
    },
    {
      field: 'budgetTotalPrice',
      headerText: i18n.t('预算总价（未税）'),
      allowEditing: false,
      valueAccessor: function (field, data) {
        return data['budgetTotalPrice'] ? parseInt(data['budgetTotalPrice'] * 100) / 100 : 0
      }
    },
    {
      field: 'subjectTotal',
      headerText: i18n.t('科目总额'),
      editType: 'numericedit',
      edit: {
        create: () => {
          subjectTotalEle = document.createElement('input')
          return subjectTotalEle
        },
        read: () => {
          console.log(subjectTotalObj)
          return subjectTotalObj.value
        },
        destroy: () => {
          subjectTotalObj.destroy()
        },
        write: (args) => {
          var rowIndex = grid.getRowInfo(args.row).rowIndex
          subjectTotalObj = new NumericTextBox({
            placeholder: i18n.t('请输入科目总额'),
            // type: "number",
            value: subjectTotalObj?.value || null,
            change: (args) => {
              console.log('输入的科目总额是：', Number(args.value))
              if (taxedUnitPriceObj?.value) {
                let taxedTotalPriceVal = taxedUnitPriceObj.value * args.value
                grid.updateCell(rowIndex, 'taxedTotalPrice', taxedTotalPriceVal)
              }
            }
          })
          subjectTotalObj.appendTo(subjectTotalEle)
        }
      }
    },
    {
      field: 'taxedUnitPrice',
      headerText: i18n.t('预算单价（含税）'),
      valueAccessor: function (field, data) {
        return data['taxedUnitPrice'] ? parseInt(data['taxedUnitPrice'] * 100) / 100 : 0
      },
      editType: 'numericedit',
      edit: {
        params: {
          change: (e) => {
            grid = new getComponent(GridId, 'grid')
            var { rowIndex, rowData } = grid.editModule.editModule.cellDetails
            if (rowData.quantity) {
              grid.updateCell(
                rowIndex,
                'taxedTotalPrice',
                bigDecimal.multiply(rowData.quantity, e.value)
              )
            }
          }
        }
      }
    },
    {
      field: 'taxedTotalPrice',
      headerText: i18n.t('预算总价（含税）'),
      allowEditing: false,
      valueAccessor: function (field, data) {
        return data['taxedTotalPrice'] ? parseInt(data['taxedTotalPrice'] * 100) / 100 : 0
      }
    },
    {
      field: 'approvedTotalPrice',
      headerText: i18n.t('批准预算总金额'),
      editType: 'numericedit'
    },
    {
      field: 'currencyName',
      headerText: i18n.t('币种'), // 因为币种修改时要传值 id code，edit那种方式找不到this，所以改用editTemplate
      editType: 'dropdownedit',
      editTemplate: function () {
        return {
          template: Vue.component('changeSelect', {
            template: `<div>
                <mt-select
                  ref="businessRef"
                  v-model="data.currencyName"
                  :data-source="currencyNameData"
                  :show-clear-button="true"
                  :fields="{ value: 'currencyCode', text: 'currencyName' }"
                  @change="handleChangeCurrency"
                  :placeholder="$t('请选择币种')"
                ></mt-select>
            </div>`,
            data() {
              return { currencyNameData, data: { data: {}, rowIndex: -1 } }
            },
            mounted() {
              grid = new getComponent(GridId, 'grid')
              this.rowIndex = grid.editModule.editModule.cellDetails.rowIndex
            },
            methods: {
              handleChangeCurrency(e) {
                // console.log(i18n.t("选择了币种"), e, this.rowIndex);
                let _rowIndex = grid.editModule.editModule.cellDetails.rowIndex
                grid.updateCell(_rowIndex, 'currencyName', e.itemData.currencyName)
                let params = {
                  fieldCode: 'currencyName',
                  rowIndex: this.rowIndex,
                  itemInfo: e.itemData,
                  addId: this.data.addId,
                  id: this.data.id
                }
                this.$parent.$parent.$emit('handleSetSelectedInfo', JSON.stringify(params))
              }
            }
          })
        }
      }
    },
    {
      field: 'postingAccountName',
      headerText: i18n.t('成本中心') // 成本中心 总账科目一个字段
      // editType: "dropdownedit",
      // edit: {
      //   create: () => {
      //     postAccountNameEle = document.createElement("input");
      //     return postAccountNameEle;
      //   },
      //   read: () => {
      //     return postAccountNameObj.text;
      //   },
      //   destroy: () => {
      //     postAccountNameObj.destroy();
      //   },
      //   write: () => {
      //     postAccountNameObj = new DropDownList({
      //       dataSource: state,
      //       fields: { value: "stateId", text: "stateName" },
      //       placeholder: i18n.t("请选择入账科目"),
      //       floatLabelType: "Never",
      //     });
      //     postAccountNameObj.appendTo(postAccountNameEle);
      //   },
      // },
    },
    {
      field: 'deduction',
      headerText: i18n.t('是否抵扣'),
      editType: 'dropdownedit',
      edit: {
        create: () => {
          deductionElem = document.createElement('input')
          return deductionElem
        },
        read: () => {
          return deductionObj.text
        },
        destroy: () => {
          deductionObj.destroy()
        },
        write: () => {
          deductionObj = new DropDownList({
            dataSource: [
              {
                value: 1,
                text: i18n.t('是')
              },
              {
                value: 0,
                text: i18n.t('否')
              }
            ],
            placeholder: i18n.t('请选择是否抵扣'),
            floatLabelType: 'Never'
          })
          deductionObj.appendTo(deductionElem)
        }
      }
    },
    {
      field: 'tradeClauseName',
      headerText: i18n.t('贸易条款'),
      editType: 'dropdownedit',
      editTemplate: function () {
        return {
          template: Vue.component('changeSelect', {
            template: `<div>
                <mt-select
                  ref="businessRef"
                  v-model="data.tradeClauseName"
                  :data-source="tradeClauseNameData"
                  :show-clear-button="true"
                  :fields="{ value: 'id', text: 'itemName' }"
                  @change="handleChangeCurrency"
                  :placeholder="$t('请选择贸易条款')"
                ></mt-select>
            </div>`,
            data() {
              return { tradeClauseNameData, data: { data: {}, rowIndex: -1 } }
            },
            mounted() {
              grid = new getComponent(GridId, 'grid')
              this.rowIndex = grid.editModule.editModule.cellDetails.rowIndex
            },
            methods: {
              handleChangeCurrency(e) {
                console.log(i18n.t('选择了贸易条款'), e, this.rowIndex)
                grid.updateCell(this.rowIndex, 'tradeClauseName', e.itemData.itemName)
                let params = {
                  fieldCode: 'tradeClauseName',
                  rowIndex: this.rowIndex,
                  itemInfo: e.itemData,
                  addId: this.data.addId,
                  id: this.data.id
                }
                this.$parent.$parent.$emit('handleSetSelectedInfo', JSON.stringify(params))
              }
            }
          })
        }
      }
    },
    {
      field: 'shippingMethodName',
      headerText: i18n.t('物流方式'),
      editType: 'dropdownedit',
      editTemplate: function () {
        return {
          template: Vue.component('changeSelect', {
            template: `<div>
                <mt-select
                  ref="businessRef"
                  v-model="data.shippingMethodName"
                  :data-source="shippingMethodNameData"
                  :show-clear-button="true"
                  :fields="{ value: 'id', text: 'itemName' }"
                  @change="handleChangeCurrency"
                  :placeholder="$t('请选择物流方式')"
                ></mt-select>
            </div>`,
            data() {
              return {
                shippingMethodNameData,
                data: { data: {}, rowIndex: -1 }
              }
            },
            mounted() {
              grid = new getComponent(GridId, 'grid')
              this.rowIndex = grid.editModule.editModule.cellDetails.rowIndex
            },
            methods: {
              handleChangeCurrency(e) {
                console.log(i18n.t('选择了物流方式'), e, this.rowIndex)
                grid.updateCell(this.rowIndex, 'shippingMethodName', e.itemData.itemName)
                let params = {
                  fieldCode: 'shippingMethodName',
                  rowIndex: this.rowIndex,
                  itemInfo: e.itemData,
                  addId: this.data.addId,
                  id: this.data.id
                }
                this.$parent.$parent.$emit('handleSetSelectedInfo', JSON.stringify(params))
              }
            }
          })
        }
      }
    },
    {
      field: 'consignee',
      headerText: i18n.t('收货人'),
      editType: 'dropdownedit',
      editTemplate: function () {
        return {
          template: Vue.component('changeSelect', {
            template: `<div>
                <mt-select
                  ref="businessRef"
                  v-model="data.consignee"
                  :data-source="receiveUserIdData"
                  :show-clear-button="true"
                  :fields="{ value: 'id', text: 'employeeName' }"
                  @change="handleChangeCurrency"
                  :placeholder="$t('请选择收货人')"
                ></mt-select>
            </div>`,
            data() {
              return { receiveUserIdData, data: { data: {}, rowIndex: -1 } }
            },
            mounted() {
              grid = new getComponent(GridId, 'grid')
              this.rowIndex = grid.editModule.editModule.cellDetails.rowIndex
            },
            methods: {
              handleChangeCurrency(e) {
                console.log(i18n.t('选择了收货人'), e, this.rowIndex)
                grid.updateCell(this.rowIndex, 'consignee', e.itemData.employeeName)
                let params = {
                  fieldCode: 'consignee',
                  rowIndex: this.rowIndex,
                  itemInfo: e.itemData,
                  addId: this.data.addId,
                  id: this.data.id
                }
                this.$parent.$parent.$emit('handleSetSelectedInfo', JSON.stringify(params))
              }
            }
          })
        }
      }
    },
    {
      field: 'qualityExemptionMarkName',
      headerText: i18n.t('质量免检标识'),
      template: function () {
        return {
          template: Vue.component('datePicker', {
            template: `<span>{{data.qualityExemptionMarkName}}</span>`,
            data() {
              return { data: {} }
            }
          })
        }
      },
      allowEditing: false
    },
    {
      width: '150',
      field: 'supplierName',
      headerText: i18n.t('推荐供应商'),
      editType: 'dropdownedit',
      edit: {
        create: () => {
          supplierIdElem = document.createElement('input')
          return supplierIdElem
        },
        read: () => {
          return supplierIdObj.text
        },
        destroy: () => {
          supplierIdObj.destroy()
        },
        write: () => {
          supplierIdObj = new DropDownList({
            dataSource: supplierIdData,
            fields: { value: 'id', text: 'supplierName' },
            placeholder: i18n.t('请选择推荐供应商'),
            floatLabelType: 'Never'
          })
          supplierIdObj.appendTo(supplierIdElem)
        }
      }
    },
    {
      field: 'claimStatus',
      headerText: i18n.t('认领状态'), // 0-未认领，1-部分认领，2-全部认领
      allowEditing: false,
      template: function () {
        return {
          template: Vue.component('datePicker', {
            template: `<span>{{data.claimStatus == 0 ?i18n.t('未认领'):data.claimStatus == 1? i18n.t('部分认领'):i18n.t('全部认领')}}</span>`,
            data() {
              return { data: {} }
            }
          })
        }
      }
    }
  ]
}

export const lastColumn = [
  // {
  //   width: "150",
  //   field: "costSharingAccName",
  //   headerText: i18n.t("成本中心"),
  // },
  {
    field: 'file',
    width: '250',
    headerText: i18n.t('附件'), // 只有编辑状态能修改、上传，否则只能展示
    allowEditing: false,
    // 使用template时，新增一行且未保存时，获取不到index（编辑状态，正常的template不会的）
    // 使用editTemplate时，显示的值不能是对象
    template: function () {
      return {
        template: cellUpload
      }
    }
  },
  {
    field: 'afterSaleNum',
    width: '150',
    headerText: i18n.t('售后数量'), // 只有编辑状态能修改、上传，否则只能展示
    editType: 'numericedit'
  }
]
