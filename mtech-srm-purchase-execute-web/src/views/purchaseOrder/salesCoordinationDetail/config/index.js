import { i18n } from '@/main.js'
export const columnData0 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '50',
    field: 'id',
    headerText: 'id'
  },
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('图片')
  },
  {
    width: '150',
    field: 'version',
    headerText: i18n.t('物料/品项编码')
  },
  {
    width: '150',
    field: 'deliveryStatus',
    headerText: i18n.t('物料/品项名称')
  },
  {
    width: '150',
    field: 'purtenantName',
    headerText: i18n.t('SKU编码')
  },
  {
    width: '150',
    field: 'col4',
    headerText: i18n.t('SKU名称')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('规格型号')
  }
]
