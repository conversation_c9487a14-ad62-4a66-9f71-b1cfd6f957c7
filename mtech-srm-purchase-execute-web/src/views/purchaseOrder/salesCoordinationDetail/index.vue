<template>
  <div class="sales-coordin-detail full-height">
    <top-info
      :entry-id="entryId"
      :entry-type="entryType"
      :top-info="topInfo"
      @save="save"
      @submit="submit"
    ></top-info>
    <mt-tabs
      style="background: #fafafa; width: 100%"
      :e-tab="false"
      tab-id="sales-tab"
      :data-source="componentConfig"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <link-purchase-grid
      v-show="currentInfo.title == '售后明细' && (entryType === '1' || entryType === '3')"
      ref="linkPurchaseGrid"
    ></link-purchase-grid>
    <not-link-purchase-grid
      ref="notLinkPurchaseGrid"
      v-show="
        currentInfo.title == '售后明细' &&
        (entryType === '2' || entryType === '4' || entryType === '5')
      "
      :entry-id="entryId"
      :entry-type="entryType"
      :fields="fields"
      :entry-file-list="entryFileList"
      :entry-order-detail-list="entryOrderDetailList"
      @updateGrid="updateGrid"
      @updateEntryFileList="updateEntryFileList"
      @updateOrderInfo="updateOrderInfo"
    ></not-link-purchase-grid>
    <!-- <detail-grid v-if="entryType === '5'"></detail-grid> -->
    <relative-file
      v-show="currentInfo.title == '相关附件'"
      :entry-id="entryId"
      :entry-type="entryType"
      :module-file-list="moduleFileList"
      :new-file-list="newFileList"
      :entry-file-list="entryFileList"
      change-node-code="as_po_item_file"
      @updateFile="updateFile"
    ></relative-file>
  </div>
</template>
<script>
export default {
  components: {
    TopInfo: () => import('./components/topInfo'),
    RelativeFile: () => import('@/components/businessComponents/relativeFile1/index.vue'),
    linkPurchaseGrid: () => import('./components/linkPurchaseGrid.vue'),
    notLinkPurchaseGrid: () => import('./components/notLinkPurchaseGrid.vue')
    // detailGrid: () => import("./components/detailGrid.vue"),
  },
  data() {
    return {
      entryId: '',
      entryType: null, //1是新增关联创建售后订单 2是新增不关联创建售后订单 3是编辑关联采购订单的售后订单 4是编辑不关联的售后订单 5是反馈异常
      topInfo: {
        company: '',
        buyerOrg: [],
        taxTotal: '',
        freeTotal: '',
        requiredDeliveryDate: ''
      },
      componentConfig: [],
      currentInfo: {},
      fields: [], //订单明细表头
      entrySource: '', //0-采购订单转化；1-手动创建；
      entryFileList: [], //编辑带入的附件
      entryOrderDetailList: [], //编辑带入的订单明细
      moduleFileList: [], //相关附件用到的tree信息
      fileList: [], //所有的上传的文件
      addList: [],
      editList: [],
      delIds: [],
      newFileList: []
    }
  },
  mounted() {
    this.entryType = this.$route.query.type
    this.entrySource = this.$route.query.source || '1'
    this.init()
  },
  destroyed() {
    // localStorage.removeItem("salesCoordinationTopInfo");
  },
  methods: {
    updateEntryFileList(fileList) {
      //订单明细附件有修改之后同步给相关附件
      this.newFileList = fileList
    },
    updateGrid(addList, editList, delIds) {
      console.log(addList, editList, delIds, '我是订单详情传过来的')
      this.addList = addList
      this.editList = editList
      this.delIds = delIds
    },
    handleSelectTab(index, item) {
      console.log(item)
      this.currentInfo = item
    },
    updateFile(e) {
      this.fileList = e
    },
    async init() {
      if (this.entryType === '1') {
        this.getTopInfo()
        this.getPebusinessConfig()
      }
      if (this.entryType === '2') {
        //新增不关联进来
        //todo 这里的接口都不对要改 获取模块数据
        this.entryId = this.$route.query.orderid
        this.getTopInfo()
        this.getPebusinessConfig()
      }
      if (this.entryType === '4' || this.entryType === '5') {
        //编辑不关联
        console.log('编辑不关联进来的阿')
        this.entryId = this.$route.query.orderid
        await this.getbuyerSaleOrder()
        await this.getFileNodeByDocId()
        await this.getPurOrderModuleData2()
        await this.getbuyerSaleOrderConfig()
        this.buyerSaleOrderDetail()
      }
    },
    buyerSaleOrderDetail() {
      //查询售后订单明细
      let params = {
        businessType: this.topInfo.businessTypeId,
        orderId: this.entryId,
        orderType: this.topInfo.type,
        purOrderCode: this.topInfo.purOrderCode,
        moduleId: this.componentConfig[0].moduleId,
        moduleKey: this.componentConfig[0].moduleKey,
        source: this.topInfo.source,
        requestParams: {
          defaultRules: [],
          page: {
            current: 1,
            size: 1000000000
          }
        }
      }
      this.$API.purchaseOrder.buyerSaleOrderDetail(params).then((res) => {
        this.entryOrderDetailList = res.data.records
      })
    },
    async getPurOrderModuleData2() {
      //附件带入的信息
      let params = {
        docId: this.entryId,
        parentId: 0,
        docType: 'as_po'
      }
      await this.$API.purchaseOrder.queryFileByDocId(params).then((res) => {
        let entryFileList = []
        res.data.forEach((item) => {
          if (item.nodeType === 1) {
            entryFileList.push(item)
          }
        })
        this.entryFileList = entryFileList
      })
    },
    async getFileNodeByDocId() {
      //编辑进入获取左边节点
      let params = {
        docId: this.entryId,
        docType: 'as_po'
      }
      await this.$API.purchaseOrder.getFileNodeByDocId(params).then((res) => {
        this.moduleFileList = res.data
      })
    },
    async getbuyerSaleOrderConfig() {
      //编辑进入获取模块配置
      let params = {
        docId: this.entryId
      }
      await this.$API.purchaseOrder.getbuyerSaleOrderConfig(params).then((res) => {
        res.data.moduleItems.forEach((item) => {
          this.componentConfig.push({
            title: item.moduleName,
            moduleType: item.moduleType,
            moduleKey: item.moduleKey,
            moduleId: item.moduleId
          })
          if (item.moduleType === 11) {
            this.fields = item.fieldDefines
            this.moduleKey = item.moduleKey
          }
        })
        this.currentInfo = this.componentConfig[0]
      })
    },
    async getbuyerSaleOrder() {
      //编辑进来获取顶部信息
      await this.$API.purchaseOrder.getbuyerSaleOrder(this.entryId).then((res) => {
        this.topInfo = {
          ...res.data,
          purtenant: res.data.supplierCode,
          payment: res.data.paymentCode,
          buyerUser: res.data.buyerUserCode,
          companyName1: res.data.companyName,
          buyerOrgName1: res.data.buyerOrgName,
          settlement: res.data.settlementId,
          currency: res.data.currencyCode,
          requiredDeliveryDate:
            res.data.dueDate && res.data.dueDate.length == 13
              ? new Date(Number(res.data.dueDate))
              : '',
          company: [
            {
              orgCode: res.data.companyCode,
              id: res.data.companyId,
              name: res.data.companyName
            }
          ],
          buyerOrg: [
            {
              orgCode: res.data.buyerOrgCode,
              id: res.data.buyerOrgId,
              orgName: res.data.buyerOrgName
            }
          ],
          businessId: res.data.businessTypeId,
          afterSaleType: res.data.type
        }
      })
    },
    //获取顶部从内存中获取数据
    getTopInfo() {
      let topInfo = JSON.parse(localStorage.getItem('salesCoordinationTopInfo'))
      Object.assign(this.topInfo, topInfo)
    },
    getPebusinessConfig() {
      //获取模块配置新增
      let params = {
        businessTypeCode: this.topInfo.businessTypeCode,
        docType: 'as_po'
      }
      this.$API.purchaseOrder.getPebusinessConfig(params).then((res) => {
        res.data.modules.forEach((item) => {
          this.componentConfig.push({
            title: item.moduleName,
            moduleType: item.moduleType,
            moduleKey: item.moduleKey,
            moduleId: item.moduleId
          })
          if (item.moduleType === 11) {
            this.fields = item.fields
            this.moduleKey = item.moduleKey
          }
        })
        this.moduleFileList = res.data.moduleFileList
        this.currentInfo = this.componentConfig[0]
      })
    },
    updateOrderInfo(orderTaxTotal, orderFreeTotal, orderRequiredDate) {
      //更新要求交期 未税总金额 含税总金额
      this.topInfo.taxTotal = orderTaxTotal
      this.topInfo.freeTotal = orderFreeTotal
      if (orderRequiredDate) {
        this.topInfo.requiredDeliveryDate = new Date(Number(orderRequiredDate))
      } else {
        this.topInfo.requiredDeliveryDate = ''
      }
    },
    submit(order) {
      if (this.$refs.notLinkPurchaseGrid.getBatchChanges()) {
        this.$toast({ content: this.$t('请先保存行数据'), type: 'error' })
        return
      }
      // console.log("提交信息", this.topInfo);
      let params = this.getParams(order, 2)
      if (!params.orderDetails.length && this.entryType === '2') {
        this.$toast({ content: this.$t('提交订单至少添加一条订单明细'), type: 'error' })
        return
      }
      // if (!this.$refs.notLinkPurchaseGrid.validateTable()) return;
      console.log(params, '传的参数')
      this.$API.purchaseOrder.buyerSaleOrderSave(params).then(() => {
        this.$toast({ content: this.$t('提交订单操作成功'), type: 'success' })
        this.$router.push({
          name: 'sales-coordination'
        })
      })
    },
    save(order) {
      console.log(order, '提交信息')
      if (this.$refs.notLinkPurchaseGrid.getBatchChanges()) {
        this.$toast({ content: this.$t('请先保存行数据'), type: 'error' })
        return
      }
      // if (!this.$refs.notLinkPurchaseGrid.validateTable()) return;
      let params = this.getParams(order, 1)
      console.log('保存草稿', params)
      this.$API.purchaseOrder.buyerSaleOrderDraft(params).then(() => {
        this.$toast({ content: this.$t('保存草稿操作成功'), type: 'success' })
        this.$router.push({
          name: 'sales-coordination'
        })
      })
    },
    getParams(order, type) {
      let params = {
        moduleKey: this.moduleKey,
        order: {
          ...order,
          status:
            (type == 2 && this.entryType === '2') ||
            this.entryType === '4' ||
            (type == 2 && this.entryType === '1') ||
            this.entryType === '3'
              ? 1
              : 0 //新增提交或者编辑提交
        },
        orderDetails: this.addList,
        orderDetailsDelete: this.delIds,
        orderDetailsModify: this.editList,
        orderFiles: this.fileList,
        orderId: 0,
        source: this.entrySource
      }
      //编辑的时候
      if ((type === 2 && this.entryType === '4') || (type === 2 && this.entryType === '3')) {
        params.orderId = this.entryId
      }
      return params
    }
  }
}
</script>
<style lang="scss" scoped>
.sales-coordin-detail {
}
/deep/ .top-info {
  margin-top: 20px;
}
</style>
