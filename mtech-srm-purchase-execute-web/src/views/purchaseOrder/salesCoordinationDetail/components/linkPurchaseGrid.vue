<template>
  <div class="link-purchase-grid full-height">
    <mt-DataGrid
      ref="grid0"
      class="custom-toolbar-grid"
      :data-source="dataSource"
      :column-data="columnData"
      :allow-sorting="true"
      :allow-filtering="true"
      :filter-settings="filterOptions"
      :toolbar="toolbar"
      :page-settings="pageSettings"
      :allow-paging="true"
      @toolbarClick="handleClicktoolbar"
    ></mt-DataGrid>
  </div>
</template>
<script>
import * as CONFIG from '../config'
export default {
  data() {
    return {
      dataSource: [
        {
          id: '1',
          buyerDepName: this.$t('图片1'),
          buyerDepName1: this.$t('物料品相编码'),
          buyerDepName2: this.$t('物料品相名称'),
          col3: this.$t('SKU编码1'),
          col4: this.$t('SKU名称1'),
          col5: this.$t('规格型号143')
        }
      ],
      columnData: CONFIG.columnData0,
      filterOptions: {
        type: 'Menu'
      },
      toolbar: [
        {
          id: 'add',
          prefixIcon: 'e-add',
          text: this.$t('新增')
        },
        {
          id: 'edit',
          prefixIcon: 'e-add',
          text: this.$t('批量编辑')
        },
        {
          id: 'delete',
          prefixIcon: 'e-add',
          text: this.$t('删除')
        }
      ],
      pageSettings: {
        pageSize: 1,
        pageCount: 8,
        totalRecordsCount: 0,
        enableQueryString: true,
        pageSizes: [1, 10, 20, 50, 100, 200]
      }
    }
  },
  mounted() {
    console.log('关联订单表格a')
  },
  methods: {
    handleClicktoolbar(e) {
      console.log(e)
      if (this.$refs.grid0.ejsRef.getSelectedRecords().length <= 0 && e.item.id !== 'add') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = []
      this.$refs.grid0.ejsRef.getSelectedRecords().map((item) => _id.push(item.id))
      if (e.item.id == 'add') {
        this.handleAdd()
      } else if (e.item.id == 'edit') {
        this.handleEdit(_id)
      } else if (e.item.id == 'delete') {
        this.handleDelete(_id)
      }
    },
    handleAdd() {
      console.log('新增啊')
    },
    handleEdit(ids) {
      console.log('编辑啊', ids)
    },
    handleDelete(ids) {
      console.log('删除啊', ids)
    }
  }
}
</script>
<style lang="scss" scoped>
.link-purchase-grid {
}
</style>
