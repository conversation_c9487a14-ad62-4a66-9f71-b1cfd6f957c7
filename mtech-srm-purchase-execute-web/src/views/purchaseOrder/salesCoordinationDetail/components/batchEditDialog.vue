<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="$t('批量编辑')"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="taxid" :label="$t('税率')">
        <mt-select
          ref="taxidRef"
          v-model="addForm.taxid"
          :data-source="taxidData"
          :show-clear-button="true"
          :fields="{ value: 'id', text: 'taxRate' }"
          :placeholder="$t('请选择税率')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="consigneeId" :label="$t('收货人')">
        <mt-select
          ref="consigneeIdRef"
          v-model="addForm.consigneeId"
          :data-source="receiveUserIdData"
          :show-clear-button="true"
          :fields="{ value: 'id', text: 'employeeName' }"
          :placeholder="$t('请选择收货人')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="contact" :label="$t('联系方式')">
        <mt-input
          v-model="addForm.contact"
          :show-clear-button="true"
          :placeholder="$t('请输入联系方式')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="receiveAddress" :label="$t('收货地址')">
        <mt-input
          v-model="addForm.receiveAddress"
          :show-clear-button="true"
          :placeholder="$t('请输入收货地址')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="packageMethod" :label="$t('包装方式')">
        <mt-input
          v-model="addForm.packageMethod"
          :show-clear-button="true"
          :placeholder="$t('请输入包装方式')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="packageSpec" :label="$t('包装规格')">
        <mt-input
          v-model="addForm.packageSpec"
          :show-clear-button="true"
          :placeholder="$t('请输入包装规格')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="packageDesc" :label="$t('包装说明')">
        <mt-input
          v-model="addForm.packageDesc"
          :show-clear-button="true"
          :placeholder="$t('请输入包装说明')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="productName" :label="$t('涉及产品系列名称')">
        <mt-input
          v-model="addForm.productName"
          :show-clear-button="true"
          :placeholder="$t('请输入涉及产品系列名称')"
        ></mt-input>
      </mt-form-item>

      <!-- <mt-form-item prop="supplierId" :label="$t('推荐供应商')">
        <mt-select
          ref="supplierIdRef"
          v-model="addForm.supplierId"
          :data-source="supplierIdData"
          :show-clear-button="true"
          :fields="{ value: 'id', text: 'supplierName' }"
          :placeholder="$t('请选择推荐供应商')"
        ></mt-select>
      </mt-form-item> -->

      <mt-form-item prop="costSharingAccName" :label="$t('成本中心')">
        <mt-input
          v-model="addForm.costSharingAccName"
          :show-clear-button="true"
          :placeholder="$t('请输入成本中心')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
        <mt-input
          v-model="addForm.remark"
          :show-clear-button="true"
          :multiline="true"
          :placeholder="$t('请输入备注')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
// import common from "@/utils/constant";
// import { formatDate, formatRules } from "@/utils/util";
export default {
  props: {
    taxidData: {
      type: Array,
      default: () => {}
    },
    receiveUserIdData: {
      type: Array,
      default: () => {}
    },
    supplierIdData: {
      type: Array,
      default: () => {}
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        taxid: null,
        consigneeId: null, // 收货人
        consignee: null,
        contact: null,
        receiveAddress: null,
        packageMethod: null,
        packageSpec: null,
        packageDesc: null,
        productName: null,
        supplierId: null,
        costSharingAccName: null,
        remark: null
      },
      rules: {
        costSharingAccCode: [
          { required: true, message: this.$t('请选择成本中心'), trigger: 'blur' }
        ],
        appProportion: [{ required: true, message: this.$t('请输入分摊比例'), trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    confirm() {
      let params = {}
      for (var i in this.addForm) {
        if (this.addForm[i]) {
          params[i] = this.addForm[i]
        }
      }
      if (this.addForm.supplierId && this.$refs.supplierIdRef) {
        let _data = this.$refs.supplierIdRef.ejsRef.getDataByValue(this.addForm.supplierId)
        params.supplierName = _data.supplierName
        params.supplierCode = _data.supplierCode
      }

      if (this.addForm.consigneeId && this.$refs.consigneeIdRef) {
        let _data = this.$refs.consigneeIdRef.ejsRef.getDataByValue(this.addForm.consigneeId)
        params.consigneeId = _data.id
        params.consignee = _data.employeeName
      }

      this.$emit('handleAddDialogShow', 'batchEditShow', false)
      this.$emit('confirmBatchSuccess', params)
    },

    handleClose() {
      this.$emit('handleAddDialogShow', 'batchEditShow', false)
    }
  }
}
</script>

<style></style>
