<template>
  <div class="detail-grid full-height">
    <mt-template-page :template-config="componentConfig"></mt-template-page>
  </div>
</template>

<script>
import * as CONFIG from '../config'
export default {
  data() {
    return {
      componentConfig: [
        {
          toolbar: [],
          grid: {
            columnData: CONFIG.columnData0,
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/supOrder/query',
              defaultRules: [
                {
                  condition: 'and',
                  field: 'feedbackStatus',
                  label: '',
                  operator: 'equal',
                  value: 0
                }
              ]
            }
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {}
}
</script>

<style></style>
