<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="infos mr20">
        {{ $t('单据编号：') }}{{ topInfo.orderCode ? topInfo.orderCode : '-' }}
      </div>
      <div class="infos mr20">
        {{ $t('创建人：') }}{{ topInfo.createUserName ? topInfo.createUserName : '-' }}
      </div>
      <div class="infos">
        {{ $t('创建时间：') }}{{ topInfo.createTime ? topInfo.createTime : '-' }}
      </div>
      <div class="middle-blank"></div>
      <!-- 右侧各种操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack" v-if="true">{{
        $t('返回')
      }}</mt-button>
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-if="entryType === '1' || entryType === '2'"
        @click="save"
        >{{ $t('保存草稿') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-if="entryType === '1' || entryType === '2' || entryType === '3' || entryType === '4'"
        @click="submit"
        >{{ $t('提交') }}</mt-button
      >
      <div class="sort-box" @click="expandChange">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>
    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="topInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="businessTypeName" :label="$t('业务类型')">
          <mt-input v-model="topInfo.businessTypeName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="purtenant" :label="$t('供应商')">
          <mt-select
            allow-filtering="true"
            v-model="topInfo.purtenant"
            :data-source="purtenantOptions"
            :show-clear-button="true"
            :placeholder="$t('请选择供应商')"
            :disabled="
              entryType === '1' || entryType === '3' || entryType === '4' || entryType === '5'
            "
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="afterSaleType" :label="$t('售后类型')">
          <mt-select
            :disabled="
              entryType === '1' || entryType === '3' || entryType === '4' || entryType === '5'
            "
            allow-filtering="true"
            v-model="topInfo.afterSaleType"
            :data-source="afterSaleTypeOptions"
            :show-clear-button="true"
            :placeholder="$t('请选择售后类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          prop="purchaseOrder"
          :label="$t('关联采购订单')"
          v-if="
            entryType === '1' ||
            entryType === '3' ||
            (entryType === '5' && topInfo.relateOrderCode == '0')
          "
        >
          <mt-input v-model="topInfo.purchaseOrder" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="payment" :label="$t('付款条件')">
          <mt-select
            :disabled="
              entryType === '1' || entryType === '3' || entryType === '4' || entryType === '5'
            "
            v-model="topInfo.payment"
            :data-source="paymentOptions"
            :show-clear-button="true"
            :placeholder="$t('请选择付款条件')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="buyerUser" class="" :label="$t('采购员')">
          <debounce-filter-select
            :disabled="
              entryType === '1' || entryType === '3' || entryType === '4' || entryType === '5'
            "
            :open-dispatch-change="false"
            v-model="topInfo.buyerUser"
            :request="getUser"
            :data-source="buyerUserOptions"
            :fields="{ text: 'text', value: 'employeeId' }"
            :show-clear-button="true"
            :placeholder="$t('请选择采购员')"
          ></debounce-filter-select>
        </mt-form-item>
        <mt-form-item prop="company" :label="$t('公司')">
          <mt-select
            v-if="entryType === '2'"
            ref="companyRef"
            v-model="topInfo.company"
            id="companyTree"
            :data-source="companyOptions"
            :fields="{ text: 'orgName', value: 'id' }"
            :allow-filtering="true"
            :placeholder="$t('请选择公司')"
            @change="handleCompanyChange"
          ></mt-select>
          <mt-input
            v-if="entryType === '1' || entryType === '3' || entryType === '4' || entryType === '5'"
            v-model="topInfo.companyName1"
            :disabled="true"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="buyerOrg" :label="$t('采购组织')">
          <mt-select
            v-if="entryType === '2'"
            ref="buyerOrgRef"
            v-model="topInfo.buyerOrg"
            id="organizationTree"
            :popup-height="400"
            :data-source="buyerOrgOptions"
            :allow-filtering="true"
            :placeholder="$t('请选择采购组织')"
            @change="handleBuyerOrgChange"
          ></mt-select>
          <mt-input
            v-if="entryType === '1' || entryType === '3' || entryType === '4' || entryType === '5'"
            v-model="topInfo.buyerOrgName1"
            :disabled="true"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="requiredDeliveryDate" :label="$t('要求交期')">
          <!-- <mt-date-time-picker
            :disabled="
              entryType === '1' ||
              entryType === '3' ||
              entryType === '4' ||
              entryType === '5'
            "
            :placeholder="$t('选择要求交期')"
            v-model="topInfo.requiredDeliveryDate"
          ></mt-date-time-picker> -->
          <mt-date-time-picker
            :disabled="true"
            :placeholder="$t('选择要求交期')"
            v-model="topInfo.requiredDeliveryDate"
          ></mt-date-time-picker>
        </mt-form-item>
        <mt-form-item prop="currency" :label="$t('订单币种')">
          <mt-select
            :disabled="
              entryType === '1' || entryType === '3' || entryType === '4' || entryType === '5'
            "
            allow-filtering="true"
            v-model="topInfo.currency"
            :data-source="currencyOptions"
            :show-clear-button="true"
            :placeholder="$t('请选择订单币种')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="taxTotal" :label="$t('含税总金额')">
          <!-- <mt-inputNumber
            v-if="entryType === '2'"
            v-model="topInfo.taxTotal"
            :precision="2"
            :placeholder="$t('请输入')"
          ></mt-inputNumber>
          <mt-input
            v-if="
              entryType === '1' ||
              entryType === '3' ||
              entryType === '4' ||
              entryType === '5'
            "
            v-model="topInfo.taxTotal"
            :disabled="true"
          ></mt-input> -->
          <mt-input v-model="topInfo.taxTotal" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="freeTotal" :label="$t('未税总金额')">
          <!-- <mt-inputNumber
            v-if="entryType === '2'"
            v-model="topInfo.freeTotal"
            :precision="2"
            :placeholder="$t('请输入')"
          ></mt-inputNumber>
          <mt-input
            v-if="
              entryType === '1' ||
              entryType === '3' ||
              entryType === '4' ||
              entryType === '5'
            "
            v-model="topInfo.freeTotal"
            :disabled="true"
          ></mt-input> -->
          <mt-input v-model="topInfo.freeTotal" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="settlement" :label="$t('结算方')">
          <mt-select
            :disabled="
              entryType === '1' || entryType === '3' || entryType === '4' || entryType === '5'
            "
            v-model="topInfo.settlement"
            :data-source="settlementOptions"
            :show-clear-button="true"
            :placeholder="$t('请选择结算方')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="invoiceId" :label="$t('发票信息')">
          <mt-select
            v-model="topInfo.invoiceId"
            :data-source="invoiceIdOptions"
            :show-clear-button="true"
            :placeholder="$t('请选择发票信息')"
            :disabled="
              entryType === '1' || entryType === '3' || entryType === '4' || entryType === '5'
            "
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input :disabled="entryType === '5'" v-model="topInfo.remark"></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  props: {
    entryType: {
      type: String,
      default: '1'
    },
    topInfo: {
      type: Object,
      default: () => {}
    },
    entryId: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      isExpand: false,
      rules: {
        businessTypeName: [{ required: true, message: this.$t('请输入业务类型'), trigger: 'blur' }],
        purtenant: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }],
        payment: [{ required: true, message: this.$t('请选择付款条件'), trigger: 'blur' }],
        buyerUser: [{ required: true, message: this.$t('请选择采购员'), trigger: 'blur' }],
        company: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        buyerOrg: [{ required: true, message: this.$t('请选择采购组织'), trigger: 'blur' }],
        requiredDeliveryDate: [
          { required: false, message: this.$t('请选择要求交期'), trigger: 'blur' }
        ],
        currency: [{ required: true, message: this.$t('请选择订单币种'), trigger: 'blur' }],
        taxTotal: [{ required: false, message: this.$t('请输入含税总金额'), trigger: 'blur' }],
        freeTotal: [{ required: false, message: this.$t('请输入未税总金额'), trigger: 'blur' }],
        settlement: [{ required: true, message: this.$t('请选择结算方'), trigger: 'blur' }],
        invoiceId: [{ required: false, message: this.$t('请选择发票信息'), trigger: 'blur' }]
      },
      purtenantOptions: [], //供应商
      afterSaleTypeOptions: [], //售后类型
      paymentOptions: [], //付款条件
      buyerUserOptions: [], //采购员
      companyOptions: [], //公司
      buyerOrgOptions: [], //采购组织
      currencyOptions: [], //订单币种
      settlementOptions: [
        { text: this.$t('结算方1'), value: '1' },
        { text: this.$t('结算方2'), value: '2' }
      ], //结算方
      invoiceIdOptions: [
        { text: this.$t('发票1'), value: '1' },
        { text: this.$t('发票2'), value: '2' }
      ] //发票
    }
  },
  mounted() {
    this.getOptions()
  },
  methods: {
    // 用户改变
    // async handleUserChange(e) {
    //   if (e.itemData != null) {
    //     this.topInfo.buyerUser = e.itemData.value;
    //   }
    //   // await this.getCompany();
    // },
    // 根据员工ID查询公司
    async getCompany() {
      await this.$API.masterData
        .OrgFindSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true,
          organizationIds: []
        })
        .then((res) => {
          this.companyOptions = res.data
          if (this.companyOptions && this.companyOptions.length == 1) {
            this.topInfo.company = this.companyOptions[0].id
            this.getGroup()
          }
        })
    },
    //采购组织

    async getGroup() {
      if (!this.topInfo.company) {
        return
      }
      await this.$API.masterData
        .getOrganizateByOrgId({ orgId: this.topInfo.company })
        .then((res) => {
          this.buyerOrgOptions = res.data.map((item) => {
            return {
              text: item.organizationName,
              value: item.id
            }
          })
        })
    },
    getOptions() {
      //获取公司
      this.getCompany()
      //供应商
      this.$API.masterData.getSupplier().then((res) => {
        if (res && res.data.length) {
          this.purtenantOptions = res.data.map((item) => {
            return {
              text: item.supplierName,
              value: item.supplierCode,
              id: item.id
            }
          })
        }
      })
      this.$API.masterData //售后类型
        .getDictItemTree({
          dictCode: 'SHLX'
        })
        .then((res) => {
          if (res.data && res.data.length) {
            // this.afterSaleTypeOptions = res.data.map((item) => {
            //   return {
            //     text: item.name,
            //     value: item.itemCode,
            //   };
            // });
            this.afterSaleTypeOptions = [
              { text: this.$t('退货订单'), value: 0 },
              { text: this.$t('换货订单'), value: 1 },
              { text: this.$t('维修订单'), value: 2 }
            ]
          }
          // ；1-换货订单；2-维修订单
        })
      // 付款条件
      this.$API.masterData.getDictCode({ dictCode: 'PaymentType' }).then((res) => {
        if (res.data && res.data.length) {
          this.paymentOptions = res.data.map((item) => {
            return {
              text: item.itemName,
              value: item.itemCode
            }
          })
        }
      })

      // 订单币种
      this.$API.masterData.getCurrency().then((res) => {
        if (res && res.data.length) {
          this.currencyOptions = res.data.map((item) => {
            return {
              text: item.currencyName,
              value: item.currencyCode,
              id: item.id
            }
          })
          if (this.entryType === '2' && this.currencyOptions) {
            this.topInfo.currency = this.currencyOptions[0].value
          }
        }
      })
    },
    // 获取 用户列表
    getUser(e) {
      const { text: fuzzyName } = e
      this.buyerUserOptions = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data?.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`,
            code: item.employeeCode,
            name: item.employeeName
          })
        })
        this.buyerUserOptions = tmp
      })
    },
    //采购选择改变的时候
    handleBuyerOrgChange(e) {
      console.log(e, '采购组织')
      this.$refs.ruleForm.validateField('buyerOrg')
    },
    //公司选择改变的时候
    handleCompanyChange() {
      this.getGroup()
    },
    expandChange() {
      this.isExpand = !this.isExpand
      this.$refs.ruleForm.clearValidate()
    },
    goBack() {
      this.$router.push({
        name: 'sales-coordination'
      })
    },
    save() {
      this.$refs.ruleForm.validate((valid) => {
        if (!valid) {
          this.$toast({ content: this.$t('请先保存订单信息'), type: 'error' })
          return
        }
        this.$emit('save', this.getParams())
      })
    },
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (!valid) {
          this.$toast({ content: this.$t('请先保存订单信息'), type: 'error' })
          return
        }
        this.$emit('submit', this.getParams())
      })
    },
    getParams() {
      let companyInfo = {}
      if (this.entryType === '2') {
        //新增
        companyInfo = {
          companyId: this.topInfo.company,
          companyName: this.companyOptions.find((item) => {
            return item.id == this.topInfo.company
          })?.orgName,
          companyCode: this.companyOptions.find((item) => {
            return item.id == this.topInfo.company
          })?.orgCode
        }
      }
      if (this.entryType === '4') {
        //编辑状态下
        companyInfo = this.topInfo.company
      }
      let params = {
        businessTypeCode: this.topInfo.businessTypeCode, //业务类型
        businessTypeId: this.topInfo.businessId, //业务类型
        businessTypeName: this.topInfo.businessTypeName, //业务类型
        buyerOrgCode: this.buyerOrgOptions.find((item) => {
          //采购组织
          return item.value == this.topInfo.buyerOrg
        })?.organizationCode, //采购组织
        buyerOrgId: this.topInfo?.buyerOrg, //采购组织
        buyerOrgName: this.buyerOrgOptions.find((item) => {
          //采购组织
          return item.value == this.topInfo.buyerOrg
        })?.organizationName, //采购组织
        buyerUserId: this.topInfo.buyerUser, //采购员
        buyerUserCode: this.buyerUserOptions.find((item) => {
          //采购员
          return item.value == this.topInfo.buyerUser
        })?.code,
        buyerUserName: this.buyerUserOptions.find((item) => {
          //采购员
          return item.value == this.topInfo.buyerUser
        })?.name,
        companyCode: companyInfo.companyCode, //公司
        companyId: companyInfo.companyId, //公司
        companyName: companyInfo.companyName, //公司
        currencyCode: this.topInfo.currency, //币种
        currencyId: this.currencyOptions.find((item) => {
          //币种
          return item.value == this.topInfo.currency
        })?.id,
        currencyName: this.currencyOptions.find((item) => {
          //币种
          return item.value == this.topInfo.currency
        })?.text,
        deliveryStatus: this.topInfo.deliveryStatus || 0, //供方发货状态
        dueDate: this.topInfo.requiredDeliveryDate
          ? this.topInfo.requiredDeliveryDate.getTime()
          : '0',
        feedbackStatus: this.topInfo.feedbackStatus || 0, //供应商反馈状态
        freeTotal: this.topInfo.freeTotal, //未税总价
        orderCode: this.topInfo.orderCode,
        invoiceId: this.topInfo.invoiceId, //发票id
        paymentCode: this.topInfo.payment, //付款条件
        paymentName: this.paymentOptions.find((item) => {
          //付款条件
          return item.value == this.topInfo.payment
        })?.text,
        publishStatus: this.topInfo.publishStatus || 0, //发布状态
        purDeliveryStatus: this.topInfo.purDeliveryStatus || 0, //采方发货状态
        purOrderCode: this.topInfo.purOrderCode || '',
        reason: this.topInfo.afterSaleReason, //售后原因
        receiveStatus: this.topInfo.receiveStatus || 0, //收货状态
        remark: this.topInfo.remark, //备注
        settlementId: this.topInfo.settlement, //结算方
        settlementName: this.settlementOptions.find((item) => {
          //结算方
          return item.value == this.topInfo.settlement
        })?.text,
        source: 1, //todo订单来源 增加从顶部获取
        status: this.topInfo.status, //单据状态
        supplierCode: this.topInfo.purtenant, //供应商
        supplierId: this.purtenantOptions.find((item) => {
          //供应商
          return item.value == this.topInfo.purtenant
        })?.id,
        supplierName: this.purtenantOptions.find((item) => {
          //供应商
          return item.value == this.topInfo.purtenant
        })?.text,
        taxTotal: this.topInfo.taxTotal, //含税总价
        tenantId: 0, //租户
        tenantName: 0, //租户
        type: this.topInfo.type || 0, //订单类型
        warehouseStatus: this.topInfo.warehouseStatus || 0
      }
      if (this.entryType === '4' || this.entryType === '3') {
        params.orderId = this.entryId
      }
      if (this.entryType === '2' || this.entryType === '4') {
        params.relateOrderCode = 0 //关联采购订单
      }
      if (this.entryType === '1' || this.entryType === '3') {
        params.relateOrderCode = 1 //不关联采购订单
      }
      return params
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
