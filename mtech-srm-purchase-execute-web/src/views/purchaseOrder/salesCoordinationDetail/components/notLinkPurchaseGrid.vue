<template>
  <div class="pt20">
    <!-- <button @click="getData">获取数据</button> -->
    <mt-data-grid
      id="Grid3"
      class="custom-toolbar-grid"
      :data-source="dataSource"
      :column-data="columnData"
      ref="dataGrid"
      :allow-paging="false"
      :page-settings="pageSettings"
      :edit-settings="editSettings"
      :toolbar="toolbarOptions"
      :before-batch-save="beforeBatchSave"
      @actionComplete="actionComplete"
      @confirm-function="handleUploadSuccess"
      @remove-file="handleRemoveFile"
      :toolbar-click="toolbarClick"
      @handleChooseItemCode="handleChooseItemCode"
      @handleSetSelectedInfo="handleSetSelectedInfo"
    ></mt-data-grid>
    <choose-item-code
      v-if="addDialogShow"
      :modal-data="modalData"
      @confirmSuccess="confirmSuccess"
      @handleAddDialogShow="handleAddDialogShow"
    ></choose-item-code>

    <batch-edit-dialog
      v-if="batchEditShow"
      :taxid-data="taxidData"
      :receive-user-id-data="receiveUserIdData"
      :supplier-id-data="supplierIdData"
      @handleAddDialogShow="handleBatchDialogShow"
      @confirmBatchSuccess="confirmBatchSuccess"
    ></batch-edit-dialog>
  </div>
</template>
<script>
// Normal模式，没有绿色背景
// Batch模式，
//      要在 beforeBatchSave中根据args值对dataSource做改变；
//     且新增时，要换成行内新增
//     去掉保存时的dialog (设置editSetting)
//     一列根据另一列动态修改的
import Vue from 'vue'
import { editColumnBefore, editColumn, lastColumn } from '../config/requireDetail.js'
import { cloneDeep } from 'lodash'
var bigDecimal = require('js-big-decimal')

export default {
  props: {
    fields: {
      //表头数据
      type: Array,
      default: () => []
    },
    entryType: {
      //进入的类型
      type: String,
      default: '1'
    },
    costSwitch: {
      type: String,
      default: '1'
    },
    entryOrderDetailList: {
      type: Array,
      default: () => []
    },
    entryId: {
      type: String,
      default: '0'
    },
    entryFileList: {
      type: Array,
      default: () => []
    }
  },
  components: {
    chooseItemCode: () => import('./chooseItemCode'),
    batchEditDialog: require('./batchEditDialog.vue').default
  },
  data() {
    return {
      addDialogShow: false,
      modalData: null,
      dataSource: [],
      columnData: editColumnBefore,
      toolbarOptions: [
        {
          text: this.$t('新增'),
          id: 'addinlinebtn',
          prefixIcon: 'e-add',
          fn: this.handleAdd
        },
        {
          text: this.$t('批量编辑'),
          id: 'batchEdit',
          prefixIcon: 'e-edit',
          fn: this.handleBatchEdit
        },
        // "Edit",
        'Delete',
        'Update',
        'Cancel'
      ],
      editSettings: {
        allowEditing: true,
        allowAdding: true,
        allowDeleting: true,
        mode: 'Batch',
        showConfirmDialog: false,
        showDeleteConfirmDialog: true
      },
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        totalRecordsCount: 0
      },
      editColumns: [],
      siteNameData: [], // 地点/工厂列表
      taxidData: [], // 税率列表
      currencyNameData: [], // 币种
      tradeClauseNameData: [], // 贸易条款
      shippingMethodNameData: [], // 物流方式
      receiveUserIdData: [], // 收货人
      qualityExemptionMarkIdData: [], // 质量免检标识
      supplierIdData: [], // 推荐供应商
      nowRowItemId: null, // 当前编辑行的物料id（为了获取工厂）
      rowsSelectedInfo: {}, // 编辑行的下拉数据，包括对应的id和code  {0:{categoryId:"", categoryCode:"", categoryName:"", buyerOrgId:"",buyerOrgCode:""....}}
      deliveryStatusOptions: [
        //发货状态
        { label: this.$t('未发货'), value: '0' },
        { label: this.$t('部分发货'), value: '1' },
        { label: this.$t('全部发货'), value: '2' }
      ],
      confirmStatusOptions: [
        //供应商确认状态
        { label: this.$t('待确认'), value: '0' },
        { label: this.$t('确认'), value: '1' },
        { label: this.$t('拒绝'), value: '2' }
      ],
      warehouseStatusOptions: [
        //入库状态
        { label: this.$t('未入库'), value: '0' },
        { label: this.$t('部分入库'), value: '1' },
        { label: this.$t('全部入库'), value: '2' }
      ],

      batchEditShow: false, // 批量编辑的弹窗
      batchRowIndexs: [], // 批量编辑时的行序号

      addChangeRecords: [],
      delIds: [],
      addIdFileObj: {} //
    }
  },
  watch: {
    fields: {
      async handler() {
        console.log('中的双方都是 121')
        await this.getDropDownData() // 要等到下拉数据获取后，才能组合列
        this.handleUnionColumns()
      },
      immediate: true
    },
    entryType: {
      handler() {
        this.init()
      },
      immediate: true
    },
    costSwitch: {
      handler() {
        this.updateGrid()
      },
      immediate: true
    },
    entryFileList: {
      handler(val) {
        if (val.length) {
          // this.updataEntryList();
        }
      },
      immediate: true
    },
    entryOrderDetailList: {
      handler(val) {
        if (!val) {
          return
        }
        this.updataEntryList()
      },
      immediate: true
    }
  },
  mounted() {
    this.$bus.$on('openWhole1', (e) => {
      if (e) {
        // 清空成本列的内容，并且禁用
        this.$refs.dataGrid.ejsRef.hideColumns('成本中心')
      } else {
        // 解除成本列的禁用
        this.$refs.dataGrid.ejsRef.showColumns('成本中心')
      }
    })
  },
  methods: {
    updataEntryList() {
      //更新文件和列表匹配
      if (!this.entryOrderDetailList) return
      this.entryOrderDetailList.forEach((item, index) => {
        item.indexDefine = +index + 1
        item.deliveryStatusValue = item.deliveryStatus
        item.deliveryStatus = this.deliveryStatusOptions.find((item1) => {
          return item1.value == item.deliveryStatusValue
        })?.label
        item.confirmStatusValue = item.confirmStatus
        item.confirmStatus = this.confirmStatusOptions.find((item1) => {
          return item1.value == item.confirmStatusValue
        })?.label
        item.agreementCode = item.contract
        item.budgetQuantity = item.forecastQty
        if (item.purchasingCycleStart && item.purchasingCycleStart.length == 13) {
          item.startDate = new Date(Number(item.purchasingCycleStart))
        } else {
          item.startDate = ''
        }
        if (item.purchasingCycleEnd && item.purchasingCycleEnd.length == 13) {
          item.endDate = new Date(Number(item.purchasingCycleEnd))
        } else {
          item.endDate = ''
        }
        if (item.dueDate && item.dueDate.length == 13) {
          item.requiredDeliveryDate = new Date(Number(item.dueDate))
        } else {
          item.requiredDeliveryDate = ''
        }
        item.orderUnitId = item.purUnitId
        item.warehouseStatusValue = item.warehouseStatus
        item.warehouseStatus = this.warehouseStatusOptions.find((item1) => {
          return item1.value == item.warehouseStatusValue
        })?.label
        item.postingAccountName = item.costCenter
        if (this.entryFileList) {
          let entryfiles = []
          this.entryFileList.forEach((item1) => {
            if (item1.nodeCode === 'as_po_item_file') {
              //订单明细附件
              entryfiles.push(item1)
            }
          })
          if (!entryfiles) return
          entryfiles.some((item2) => {
            if (item2.fileDetailId === item.id) {
              item.file = item2
            }
          })
        }
      })
      this.dataSource = this.entryOrderDetailList
      console.log(this.dataSource, '带入的数据2332')
      this.updateGrid()
    },
    init() {
      if (this.entryType === '2' || this.entryType === '4') {
        this.toolbarOptions = [
          {
            text: this.$t('新增'),
            id: 'addinlinebtn',
            prefixIcon: 'e-add',
            fn: this.handleAdd
          },
          {
            text: this.$t('批量编辑'),
            id: 'batchEdit',
            prefixIcon: 'e-edit',
            fn: this.handleBatchEdit
          },
          'Delete',
          'Update',
          'Cancel'
        ]
      }
      if (this.entryType === '5') {
        this.editSettings = {
          allowEditing: false
        }
        this.toolbarOptions = []
      }
    },
    // 显示品项（物料）、SKU、工厂弹窗
    // 顺序：先物料/SKU后工厂，最后根据这俩获取
    // 修改了物料/SKU后，工厂、采购组、基本单位、采购单位要清空
    handleChooseItemCode(params) {
      let { fieldCode, rowIndex, title, requestUrl, itemId, addId, id } = JSON.parse(params)
      this.addDialogShow = true
      this.modalData = {
        fieldCode,
        rowIndex,
        title,
        requestUrl,
        itemId: itemId || this.nowRowItemId, // 获取物料id
        addId,
        id
      }
    },
    // 选择弹窗点击确定
    async confirmSuccess(itemInfo) {
      // 选择了物料或SKU后，能拿到物料id，供获取工厂列表使用
      if (itemInfo.fieldCode == 'itemCode' || itemInfo.fieldCode == 'skuCode') {
        this.nowRowItemId = itemInfo.fieldCode == 'itemCode' ? itemInfo?.id : itemInfo?.itemId
        // 下拉修改后，再去更新编辑行的数据
        this.$bus.$emit('setItemCode1', this.modalData.fieldCode, itemInfo, this.modalData.rowIndex)
        let params = {
          fieldCode: this.modalData.fieldCode,
          rowIndex: this.modalData.rowIndex,
          itemInfo: itemInfo,
          addId: this.modalData.addId,
          id: this.modalData.id
        }
        this.handleSetSelectedInfo(JSON.stringify(params)) // 处理选择的下拉
      } else if (itemInfo.fieldCode == 'siteName') {
        // 如果是选择工厂，那么需要再请求一个接口来获取后面的：采购组、基本单位、采购单位字段
        let _res = null
        await this.$API.masterData
          .getBasicByFacItem({
            organizationId: itemInfo.organizationId,
            itemId: this.nowRowItemId
          })
          .then((res) => {
            console.log(res, itemInfo, '啊啊啊')
            _res = {
              siteName: itemInfo.organizationName, // 工厂/地点 名称
              unitId: res.data?.itemInfo?.baseMeasureUnitId, // 基本计量单位名称
              unitName: res.data?.itemInfo?.baseMeasureUnitName, // 基本计量单位名称
              orderUnitId: res.data?.purchasingBasicInfo?.purchaseUnitId, //  采购单位id
              // orderUnitName: res.data?.purchasingBasicInfo?.purchaseUnitName, //TODO采购单位名称
              orderUnitName: this.$t('暂无数据阿'), //TODO采购单位名称
              // buyerOrgId: res.data?.purchasingInfo?.purchaseGroupId, //  TODO采购组id
              // buyerOrgName: res.data?.purchasingInfo?.purchaseGroupName, //  TODO采购组名称
              // buyerOrgCode: res.data?.purchasingInfo?.purchaseGroupCode, // TODO 采购组code
              buyerOrgId: '1', // TODO 采购组id
              buyerOrgName: this.$t('暂无数据阿'), //TODO  采购组名称
              buyerOrgCode: this.$t('暂无数据阿'), // TODO 采购组code
              categoryId: itemInfo?.categoryTypeInfo?.categoryId, // 品类id
              categoryCode: itemInfo?.categoryTypeInfo?.categoryCode, // 品类code
              categoryName: itemInfo?.categoryTypeInfo?.categoryName // 品类名称
            }
            // 处理选择的下拉
            let params = {
              fieldCode: this.modalData.fieldCode,
              rowIndex: this.modalData.rowIndex,
              itemInfo: {
                ...itemInfo,
                ..._res
              },
              addId: this.modalData.addId,
              id: this.modalData.id
            }
            this.handleSetSelectedInfo(JSON.stringify(params))
          })
        // 质量免检标识（要传参：物料+工厂）
        await this.$API.masterData
          .getQuantity({
            organizationId: itemInfo.organizationId,
            itemId: this.nowRowItemId
          })
          .then((res) => {
            // 下拉修改后，再去更新编辑行的数据
            this.$bus.$emit(
              'setItemCode1',
              this.modalData.fieldCode,
              {
                ...itemInfo,
                ..._res,
                qualityExemptionMarkName: res.skipQualityControl
                  ? this.$t('需免检') //0 需免检
                  : this.$t('免检') // 质量免检标识 0-否，1-是 免检
              },
              this.modalData.rowIndex
            )
          })
      }
    },
    // 根据fieldCode，设置下拉选中值。。(列表上会清空部分code、name，这里不重复修改)
    // 注意还要清空，修改了物料/SKU后，工厂、采购组、基本单位、采购单位要清空
    // 地点/工厂 能带出 品类、采购组、基本单位、采购单位
    //     品项  itemId itemCode  itemName  （弹框下拉）
    //     sku  skuId    skuCode  skuName （弹框下拉）
    //     品类  categoryId    categoryCode    categoryName  （带出）
    //     采购组 buyerOrgId  buyerOrgCode  buyerOrgName  （带出）
    //     币种 currencyCode  currencyName  （独立选择）
    //     基本单位  unitId     unitCode    unitName  （带出）
    //     订单单位  orderUnitId    orderUnitCode    orderUnitName  == 采购单位  （带出）
    //     工厂/地址  siteId   siteCode    siteName （弹框下拉）
    //     贸易条款   tradeClauseId    tradeClauseCode    tradeClauseName  （独立选择）
    handleSetSelectedInfo(params) {
      let { fieldCode, rowIndex, itemInfo, addId, id } = JSON.parse(params)
      console.log('哈哈', fieldCode, rowIndex, itemInfo)
      let _nowRowSelectedInfo = {}, // 如果已有这一行数据
        _flag = id || addId // 如果已有这一行数据
      if (Object.prototype.hasOwnProperty.call(this.rowsSelectedInfo, `row-${_flag}`)) {
        _nowRowSelectedInfo = this.rowsSelectedInfo[`row-${_flag}`]
      }

      if (fieldCode == 'itemCode') {
        //品项
        _nowRowSelectedInfo.itemId = itemInfo.id
        _nowRowSelectedInfo.skuId = null
        _nowRowSelectedInfo.siteId = null
        _nowRowSelectedInfo.siteCode = null // 清空工厂、采购组、基本单位、采购单位
        _nowRowSelectedInfo.buyerOrgId = null
        _nowRowSelectedInfo.buyerOrgCode = null
        _nowRowSelectedInfo.unitId = null
        _nowRowSelectedInfo.unitCode = null
        _nowRowSelectedInfo.orderUnitId = null
        _nowRowSelectedInfo.orderUnitCode = null
      } else if (fieldCode == 'skuCode') {
        _nowRowSelectedInfo.skuId = itemInfo.id
        _nowRowSelectedInfo.itemId = itemInfo.itemId
        _nowRowSelectedInfo.siteId = null // 清空工厂、采购组、基本单位、采购单位
        _nowRowSelectedInfo.siteCode = null
        _nowRowSelectedInfo.buyerOrgId = null
        _nowRowSelectedInfo.buyerOrgCode = null
        _nowRowSelectedInfo.unitId = null
        _nowRowSelectedInfo.unitCode = null
        _nowRowSelectedInfo.orderUnitId = null
        _nowRowSelectedInfo.orderUnitCode = null
      } else if (fieldCode == 'siteName') {
        _nowRowSelectedInfo.siteId = itemInfo.organizationId
        _nowRowSelectedInfo.siteCode = itemInfo.organizationCode
        _nowRowSelectedInfo.categoryId = itemInfo?.categoryId
        _nowRowSelectedInfo.categoryCode = itemInfo?.categoryCode
        _nowRowSelectedInfo.buyerOrgId = itemInfo?.buyerOrgId
        _nowRowSelectedInfo.buyerOrgCode = itemInfo?.buyerOrgCode
        _nowRowSelectedInfo.unitId = itemInfo?.unitId
        _nowRowSelectedInfo.unitName = itemInfo?.unitName
        _nowRowSelectedInfo.unitCode = itemInfo?.unitCode
        _nowRowSelectedInfo.orderUnitId = itemInfo?.orderUnitId
        _nowRowSelectedInfo.orderUnitCode = itemInfo?.orderUnitCode
      } else if (fieldCode == 'currencyName') {
        //币种
        _nowRowSelectedInfo.currencyCode = itemInfo.itemCode
        _nowRowSelectedInfo.currencyId = itemInfo.id
        _nowRowSelectedInfo.currencyName = itemInfo.currencyName
      } else if (fieldCode == 'tradeClauseName') {
        //贸易条款
        _nowRowSelectedInfo.tradeClauseName = itemInfo.itemName
        _nowRowSelectedInfo.tradeClauseId = itemInfo.id
        _nowRowSelectedInfo.tradeClauseCode = itemInfo.itemCode
      } else if (fieldCode == 'deliveryStatus') {
        //发货状态
        _nowRowSelectedInfo.deliveryStatus = itemInfo.label
        _nowRowSelectedInfo.deliveryStatusValue = itemInfo.value
      } else if (fieldCode == 'consignee') {
        //收货人
        _nowRowSelectedInfo.consignee = itemInfo.employeeName
        _nowRowSelectedInfo.consigneeId = itemInfo.userId
      } else if (fieldCode == 'shippingMethodName') {
        //物流方式
        _nowRowSelectedInfo.shippingMethodName = itemInfo.itemName
        _nowRowSelectedInfo.shippingMethodCode = itemInfo.itemCode
      } else if (fieldCode == 'warehouseStatus') {
        _nowRowSelectedInfo.warehouseStatus = itemInfo.label
        _nowRowSelectedInfo.warehouseStatusValue = itemInfo.value
      } else if (fieldCode == 'confirmStatus') {
        _nowRowSelectedInfo.confirmStatus = itemInfo.label
        _nowRowSelectedInfo.confirmStatusValue = itemInfo.value
      }
      this.rowsSelectedInfo[`row-${_flag}`] = _nowRowSelectedInfo
      console.log(
        '下拉选择完后获取到的id、code、name',
        rowIndex,
        _nowRowSelectedInfo,
        this.rowsSelectedInfo
      )
    },
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    },
    // 获取下拉数据
    async getDropDownData() {
      // 获取地点/工厂
      // await this.$API.masterData.getSite().then((res) => {
      //   this.siteNameData = res.data;
      // });
      // 获取税率
      await this.$API.masterData.getTaxItem().then((res) => {
        this.taxidData = res.data.map((item) => (item.taxRate = item.taxRate * 100))
      })
      // 获取币种
      await this.$API.masterData.getCurrency().then((res) => {
        this.currencyNameData = res.data
      })
      // 贸易条款
      await this.$API.masterData.getDictCode({ dictCode: 'TradeClause' }).then((res) => {
        this.tradeClauseNameData = res.data
      })
      // 物流方式
      await this.$API.masterData.getDictCode({ dictCode: 'TransportMode' }).then((res) => {
        this.shippingMethodNameData = res.data
      })
      // 收货人
      await this.$API.masterData.getEmploee().then((res) => {
        this.receiveUserIdData = res.data
      })
      // 推荐供应商
      await this.$API.masterData.getSupplier().then((res) => {
        this.supplierIdData = res.data
      })
      this.editColumns = editColumn(
        this.taxidData,
        this.currencyNameData,
        this.tradeClauseNameData,
        this.shippingMethodNameData,
        this.receiveUserIdData,
        this.supplierIdData
      )
    },
    // 组合列
    handleUnionColumns() {
      let _columnData = cloneDeep(editColumnBefore)
      if (this.fields && this.fields.length) {
        _columnData = _columnData.concat(
          this.fields.map((item1) => {
            if (item1.required) {
              if (!['itemCode'].includes(item1.fieldCode)) {
                // _one.validationRules = { required: true }; // 用自带的校验会报错，因为要自动跳转到下一个，但校验报错，导致focus不了
              }
              item1.headerTemplate = () => {
                return {
                  template: Vue.component('headers', {
                    template: `
                        <div class="headers">
                          <span style="color: red">*</span>
                          <span class="e-headertext">{{fieldName}}</span>
                        </div>
                      `,
                    data() {
                      return {
                        data: {},
                        fieldName: ''
                      }
                    },
                    mounted() {
                      this.fieldName = item1.fieldName
                    }
                  })
                }
              }
            }
            let obj = {}
            let hasItem = false
            this.editColumns.some((item2) => {
              if (item2.field === item1.fieldCode) {
                obj = {
                  ...item1,
                  hasItem: true,
                  ...item2,
                  width: '150',
                  headerText: item1.fieldName
                }
                if (item1.required) {
                  obj.headerTemplate = item1.headerTemplate
                }
                hasItem = true
              }
            })
            if (hasItem) {
              return obj
            } else {
              obj = {
                ...item1,
                hasItem: false,
                headerText: item1.fieldName,
                field: item1.fieldCode,
                required: item1.required,
                width: '150'
              }
              if (item1.required) {
                obj.headerTemplate = item1.headerTemplate
              }
              return obj
            }
          })
        )
        // _columnData = _columnData.concat(lastColumn(this.supplierIdData));
        _columnData = _columnData.concat(lastColumn)
        this.columnData = _columnData
        console.log('this.columnData', this.columnData)
      }
    },
    // 批量编辑时，只在最后保存后有用，且数据不准确
    actionComplete() {
      // console.log("actionComplete", args);
    },
    // 批量保存时触发
    beforeBatchSave(args) {
      console.log('beforeBatchSave', args)
      if (args.batchChanges.addedRecords.length > 0) {
        let _addChangeRecords = args.batchChanges.addedRecords
        // 为新增的数据匹配文件
        _addChangeRecords.forEach((ar) => {
          if (ar.addId && this.addIdFileObj[ar.addId]) {
            ar.file = this.addIdFileObj[ar.addId]
          }
        })

        this.dataSource = _addChangeRecords.concat(this.dataSource)
        this.addChangeRecords = this.addChangeRecords.concat(_addChangeRecords) // 新增时，增加记录
      }
      if (args.batchChanges.changedRecords.length > 0) {
        let dataTp = cloneDeep(this.dataSource)
        let _addChangeRecords = cloneDeep(this.addChangeRecords)
        args.batchChanges.changedRecords.forEach((item) => {
          // 为新增的数据匹配文件
          if (item.addId && this.addIdFileObj[item.addId]) {
            item.file = this.addIdFileObj[item.addId]
          }
          // 修改dataSource和提交记录addChangeRecords
          let { changeIndex, resIndex } = this.batchFormat(dataTp, _addChangeRecords, item)
          if (changeIndex >= 0) dataTp[changeIndex] = item
          // 如果修改记录里没有，就添加进去
          if (resIndex >= 0) {
            _addChangeRecords[resIndex] = item
          } else {
            _addChangeRecords.push(item)
          }
        })
        this.dataSource = dataTp
        this.addChangeRecords = _addChangeRecords
      }
      if (args.batchChanges.deletedRecords.length > 0) {
        let dataTp = cloneDeep(this.dataSource)
        let _addChangeRecords = cloneDeep(this.addChangeRecords)
        args.batchChanges.deletedRecords.forEach((item) => {
          let { changeIndex, resIndex } = this.batchFormat(dataTp, _addChangeRecords, item)
          if (changeIndex >= 0) dataTp.splice(changeIndex, 1)
          if (resIndex >= 0) _addChangeRecords.splice(resIndex, 1)
        })
        this.dataSource = dataTp
        this.addChangeRecords = _addChangeRecords
        // 修改被删除id的数组
        this.changeDelIds(args.batchChanges.deletedRecords)
      }
      this.addIdFileObj = {} // 清空临时文件对象列表
      console.log('最新的表格数据', this.dataSource, this.addChangeRecords)
      this.updateGrid()
    },
    // 调整被删除的id数组
    changeDelIds(deletedRecords) {
      deletedRecords.forEach((item) => {
        if (item.id) {
          // 说明是接口返回的
          this.delIds.push(item.id)
        }
      })
    },
    // 批量保存时，修改和删除 数据格式化
    batchFormat(dataTp, _addChangeRecords, item) {
      // 修改dataSource、修改最后的结果记录
      let changeIndex = null,
        resIndex = null
      if (item.id) {
        changeIndex = dataTp.findIndex((x) => x.id == item.id)
        resIndex = _addChangeRecords.findIndex((x) => x.id == item.id)
      } else if (item.addId) {
        changeIndex = dataTp.findIndex((x) => x.addId == item.addId)
        resIndex = _addChangeRecords.findIndex((x) => x.addId == item.addId)
      }
      return {
        changeIndex,
        resIndex
      }
    },
    toolbarClick(args) {
      console.log('toolbarClick', args)
      // 自定义了一个行内新增按钮，为了给新增的这一行赋默认值
      if (args.item.id == 'addinlinebtn') {
        this.handleAdd()
      } else if (args.item.id == 'batchEdit') {
        this.handleBatchEdit()
      } else if (typeof args.item.fn == 'function') {
        args.item.fn(this.selectRowData)
      }
    },
    // 批量编辑弹窗显示
    handleBatchEdit() {
      if (this.getBatchChanges()) {
        this.$toast({ content: this.$t('请先保存行数据'), type: 'error' })
        return
      }
      console.log(this.$refs.dataGrid.ejsRef.getSelectedRecords())
      let selectedRecords = this.$refs.dataGrid.ejsRef.getSelectedRecords()
      if (!selectedRecords || !selectedRecords.length) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      this.batchRowIndexs = selectedRecords.map((item, index) => index)
      this.batchEditShow = true
    },
    // 是否有修改未提交，true：有未提交的-----拦不住这个跳转
    getBatchChanges() {
      console.log('getBatchChanges', this.$refs.dataGrid.ejsRef.getBatchChanges())
      let changesObj = this.$refs.dataGrid.ejsRef.getBatchChanges()
      if (
        changesObj.addedRecords?.length ||
        changesObj.changedRecords?.length ||
        changesObj.deletedRecords?.length
      ) {
        return true
      }
      return false
    },

    handleBatchDialogShow(code, flag) {
      this[code] = flag
    },
    // 批量编辑修改,回显到表格上
    confirmBatchSuccess(params) {
      if (Object.keys(params).length === 0) {
        return
      }
      let _dataSource = this.getData()
      for (let i = 0; i < this.batchRowIndexs.length; i++) {
        for (let key in params) {
          _dataSource[this.batchRowIndexs[i]][key] = params[key]
        }
      }
      console.log('回显完后：', _dataSource) // -------------差一个成本中心，传过来的是costSharingAccName 字符串格式的
      this.dataSource = cloneDeep(_dataSource)
    },

    // 文件上传成功
    handleUploadSuccess(params) {
      var { rowIndex, fileInfo, addId } = JSON.parse(params)
      console.log('handleUploadConfirm==========', fileInfo, rowIndex, addId)
      let _dataSource = this.dataSource
      if (addId) {
        // 如果是手动新增 且 已经保存过了，那么dataSource中有这条记录了，可以直接更新
        let _findIndex = _dataSource.findIndex((item) => item.addId == addId)
        if (_findIndex >= 0) {
          _dataSource[_findIndex].file = fileInfo
          this.dataSource = cloneDeep(_dataSource)
        } else {
          this.addIdFileObj[addId] = fileInfo
        }
      } else {
        _dataSource[rowIndex].file = fileInfo
        this.dataSource = cloneDeep(_dataSource)
        // 上传成功后，修改addChangeRecords...从数据库获取的，且未修改过的数据，直接上传附件的情况
        let _id = _dataSource[rowIndex].id,
          _addChangeRecords = cloneDeep(this.addChangeRecords)
        let changeIndex = _addChangeRecords.findIndex((x) => x.id == _id)
        if (changeIndex >= 0) {
          _addChangeRecords[changeIndex].file = fileInfo
        } else {
          _addChangeRecords.push(_dataSource[rowIndex])
        }
        this.addChangeRecords = _addChangeRecords
      }
      this.updateGrid()
    },
    handleRemoveFile(params) {
      var { rowIndex, addId } = JSON.parse(params)
      console.log('handleRemoveFile----------', rowIndex, addId)
      let _dataSource = this.dataSource
      if (addId) {
        // 如果是手动新增 且 已经保存过了，那么dataSource中有这条记录了，可以直接更新
        let _findIndex = _dataSource.findIndex((item) => item.addId == addId)
        if (_findIndex >= 0) {
          _dataSource[_findIndex].file = null
          this.dataSource = cloneDeep(_dataSource)
        } else {
          delete this.addIdFileObj[addId]
        }
      } else {
        _dataSource[rowIndex].file = null
        this.dataSource = cloneDeep(_dataSource)
        // 上传成功后，修改addChangeRecords...从数据库获取的，且未修改过的数据，直接上传附件的情况
        let _id = _dataSource[rowIndex].id,
          _addChangeRecords = cloneDeep(this.addChangeRecords)
        let changeIndex = _addChangeRecords.findIndex((x) => x.id == _id)
        if (changeIndex >= 0) {
          _addChangeRecords[changeIndex].file = null
        } else {
          _addChangeRecords.push(_dataSource[rowIndex])
        }
        this.addChangeRecords = _addChangeRecords
      }
      this.updateGrid()
    },
    //保存后更新最新的datasoruce
    updateGrid() {
      this.addChangeRecords.forEach((item) => {
        Object.keys(this.rowsSelectedInfo).some((key) => {
          let _flag = item.id || item.addId
          if (key == 'row-' + _flag) {
            Object.assign(item, this.rowsSelectedInfo[key])
          }
        })
      })
      this.dataSource.forEach((item, index) => {
        item.indexDefine = +index + 1
        Object.keys(this.rowsSelectedInfo).some((key) => {
          let _flag = item.id || item.addId
          if (key == 'row-' + _flag) {
            Object.assign(item, this.rowsSelectedInfo[key])
          }
        })
      })
      let sendlist1 = cloneDeep(this.addChangeRecords)
      let sendList = []
      sendlist1.forEach((item) => {
        let obj = {
          afterSaleNum: item.afterSaleNum || 0, //todo 从这里开始
          itemNo: item.itemNo || 0,
          tenantId: item.tenantId || 0,
          buyerDepCode: item.purUnitNameCode, //333 采购单位接口无code
          buyerDepId: item.orderUnitId, //采购单位
          buyerDepName: item.purUnitName, //采购单位
          buyerOrgCode: item.buyerOrgCode, //采购组
          buyerOrgId: item.buyerOrgId, //采购组
          buyerOrgName: item.buyerOrgName, //采购组
          categoryCode: item.categoryCode, //品类
          categoryId: item.categoryId, //品类
          categoryName: item.categoryName, //品类
          confirmStatus: item.confirmStatusValue, //供应商确认状态
          consignee: item.consignee, //收货人
          consigneeId: item.consigneeId, //收货人
          contact: item.contact, //联系方式
          contactId: item.contactId || 1, //333 暂无接口联系方式 无id
          costCenter: item.postingAccountName, //成本中心
          currencyCode: item.currencyCode, //币种
          currencyId: item.currencyId, //币种
          currencyName: item.currencyName, //币种
          deliveryQty: item.deliveryQty, //已发货数量
          deliveryStatus: item.deliveryStatusValue, //发货状态：0-未发货；1-部分发货；2-全部发货
          fieldDataList: [], //	单据模块扩展字段列表
          freePrice: item.freePrice, //不含税单价
          freeTotal: item.freeTotal, //不含税总价
          itemCode: item.itemCode, //品项编码
          itemId: item.itemId, //品项id
          itemName: item.itemName, //品项名称
          id: item.id || 0,
          orderFiles: item.file
            ? [
                {
                  docId: 0,
                  docType: 'as_po',
                  fileName: item.file.fileName,
                  fileSize: item.file.fileSize,
                  fileType: item.file.fileType,
                  id: item.file.id,
                  itemNo: item.itemNo || 0,
                  lineNo: item.itemNo || 0,
                  nodeCode: 'as_po_item_file',
                  nodeName: this.$t('售后明细附件'),
                  nodeType: 1,
                  orderDetailId: item.id || 0,
                  orderId: item.orderId || 0,
                  parentId: 0,
                  remark: '',
                  remoteFileId: item.file.id,
                  sysFileId: item.file.id,
                  type: 0,
                  url: item.file.url
                }
              ]
            : [],
          orderId: item.orderId || 0,
          packageDesc: item.packageDesc, //包装说明
          packageMethod: item.packageMethod, //包装方式
          packageSpec: item.packageSpec, //包装规格
          postingAccount: '', //4444暂无字段入账科目
          preDeliveryQty: item.preDeliveryQty, //待发货数量
          preReceiveQty: 0, //4444暂无待收货数量
          preWarehouseQty: item.preWarehouseQty, //待入库数量
          productCode: item.productCode || 1, //333暂无接口涉及产品代码
          productName: item.productName, //涉及产品
          dueDate: item.requiredDeliveryDate ? item.requiredDeliveryDate.getTime() : '0',
          purItemNo: item.purItemNo || '0',
          purUnitCode: item.purUnitNameCode, //333 采购单位接口无code
          purUnitId: item.orderUnitId, //采购单位
          purUnitName: item.purUnitName, //采购单位
          qualityExemptionMarkCode: item.qualityExemptionMarkNameCode || 1, //质量免检标识
          qualityExemptionMarkId:
            item.qualityExemptionMarkId && item.qualityExemptionMarkId != '0'
              ? item.qualityExemptionMarkId
              : item.qualityExemptionMarkName == this.$t('免检')
              ? '1'
              : '2', //质量免检标识
          qualityExemptionMarkName: item.qualityExemptionMarkName, //质量免检标识
          quantity: item.quantity, //订单数量
          receiveAddress: item.receiveAddress, //收货地址
          receiveAddressId: item.receiveAddressId || 1, //333暂无接口收货地址
          receiveQty: 0, //4444暂无字段已收货数量
          receiveSiteCode: '', //4444暂无字段收货工厂/地点编码
          receiveSiteId: 0, //4444暂无字段收货工厂/地点
          receiveSiteName: '', //4444暂无字段收货工厂/地点
          receiveStatus: 0, //4444暂无字段收货状态：0-未收货；1-部分收货；2-全部收货
          remark: item.remark, //备注
          shippingMethodCode: item.shippingMethodCode, //物流方式编码
          shippingMethodName: item.shippingMethodName, //物流方式名称
          siteCode: item.siteCode, //工厂
          siteId: item.siteId, //工厂
          siteName: item.siteName, //工厂
          skuCode: item.skuCode, //sku编码
          skuId: item.skuId || 0, //sku编码
          skuName: item.skuName || 0, //sku编码
          skuPicUrl: '', //444暂无字段sku图片地址
          specification: item.specification, //规格型号
          taxPrice: item.taxPrice, //含税单价
          taxTotal: item.taxTotal, //含税总价
          taxid: item.taxid, //税率
          tradeClauseCode: item.tradeClauseCode, //贸易条款
          tradeClauseId: item.tradeClauseId, //贸易条款
          tradeClauseName: item.tradeClauseName, //贸易条款
          unitCode: item.unitCode || 1, //333 接口基本单位无code
          unitId: item.unitId, //基本单位
          unitName: item.unitName, //基本单位
          // "version": 0,//版本号
          warehouse: '', //4444暂无字段收货仓库
          warehouseId: 0, //4444暂无字段收货仓库
          warehouseQty: item.warehouseQty, //已入库数量
          warehouseStatus: item.warehouseStatusValue //入库状态 0-未入库；1-部分入库；2-全部入库
        }
        sendList.push(obj)
      })
      let addList = []
      let editList = []
      sendList.forEach((item) => {
        if (item.id) {
          editList.push(item)
        } else {
          addList.push(item)
        }
      })
      let entryFileList = []
      this.dataSource.forEach((item) => {
        item.orderFiles1 = item.file
          ? [
              {
                docId: 0,
                docType: 'as_po',
                fileName: item.file.fileName,
                fileSize: item.file.fileSize,
                fileType: item.file.fileType,
                id: item.file.id,
                itemNo: item.itemNo || 0,
                lineNo: item.itemNo || 0,
                nodeCode: 'as_po_item_file',
                nodeName: this.$t('售后明细附件'),
                nodeType: 1,
                orderDetailId: item.id || 0,
                orderId: item.orderId || 0,
                parentId: 0,
                remark: '',
                remoteFileId: item.file.id,
                sysFileId: item.file.id,
                type: 0,
                url: item.file.url
              }
            ]
          : []
        if (item.orderFiles1.length) {
          entryFileList.push(item.orderFiles1[0])
        }
      })
      //计算头部含税未税总价,最近的要求交期
      let orderTaxTotal = 0
      let orderFreeTotal = 0
      let requiredDateList = []
      sendList.forEach((item) => {
        orderTaxTotal = bigDecimal.add(item.taxTotal, orderTaxTotal)
        orderFreeTotal = bigDecimal.add(item.freeTotal, orderFreeTotal)
        if (item.dueDate && item.dueDate != '0') {
          requiredDateList.push(item.dueDate)
        }
      })
      orderTaxTotal = bigDecimal.round(orderTaxTotal, 2)
      orderFreeTotal = bigDecimal.round(orderFreeTotal, 2)
      let orderRequiredDate = 0
      if (requiredDateList.length) {
        orderRequiredDate = Math.min(...requiredDateList)
      }
      this.$emit('updateOrderInfo', orderTaxTotal, orderFreeTotal, orderRequiredDate)
      this.$emit('updateEntryFileList', entryFileList)
      this.$emit('updateGrid', addList, editList, this.delIds)
    },
    validateTable() {
      let flag = true
      let strArr = []
      this.columnData.forEach((item1) => {
        if (item1.required) {
          this.dataSource.some((item2, index2) => {
            if (item2[item1.field] == null || item2[item1.field] == '') {
              strArr.push(`第${index2 + 1}行 ${item1.headerText}不能为空`)
              flag = false
              return
            }
          })
        }
      })
      if (!flag) {
        this.$toast({ content: strArr[0], type: 'error' })
      }
      return flag
    },
    getData() {
      // console.log(this.$refs.dataGrid.ejsRef.dataSource);
      // console.log(
      //   this.$refs.dataGrid.ejsRef.getDataModule()?.dataManager?.dataSource
      //     ?.json
      // );
      // console.log("增加修改的数据", this.addChangeRecords, this.delIds);

      return this.$refs.dataGrid.ejsRef.getDataModule()?.dataManager?.dataSource?.json
    },
    handleAdd() {
      // 新增时，判断下，是否已新增过空行???未做
      this.$refs.dataGrid.ejsRef.addRecord(this.newRowData())
    },
    // 根据列新建行数据
    newRowData() {
      let row = {
        addId: 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
      }
      // 初始化数据
      this.columnData.forEach((item) => {
        // if (item.allowEditing) {
        if (item.editType) {
          if (item.editType.includes('dropdown')) {
            row[item.field] = ''
            // row[item.field] =
            //   item.selectData && item.selectData.length > 0
            //     ? item.selectData[0].value
            //     : "";
          } else if (item.editType.includes('date')) {
            row[item.field] = new Date()
            // row[item.field] = "";
          } else if (item.editType.includes('numer')) {
            row[item.field] = 0
          } else if (item.editType.includes('boolean')) {
            row[item.field] = true
          } else {
            row[item.field] = ''
          }
          if (item.field === 'deliveryStatus') {
            row.deliveryStatusValue = 0
            row.deliveryStatus = '未发货'
          }
          if (item.field === 'confirmStatus') {
            row.confirmStatusValue = 0
            row.confirmStatus = '待确认'
          }
          if (item.field === 'warehouseStatus') {
            row.warehouseStatusValue = 0
            row.warehouseStatus = '未入库'
          }
          if (item.field === 'itemNo') {
            row.itemNo = 0
          }
        } else {
          row[item.field] = ''
        }
        // }
      })
      return row
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  background: #fff;
}
.pt20 {
  flex: 1;
  display: flex;
  flex-direction: column;

  .mt-data-grid {
    flex: 1;
    /deep/ .e-grid {
      height: 100%;
      display: flex;
      flex-direction: column;

      .e-gridcontent {
        flex: 1;
        .e-content {
          height: 100% !important;
        }
      }
    }
  }
}
.toolbar-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;

  .one-bar {
    display: flex;
    align-items: center;
    margin-right: 20px;
    cursor: pointer;
    .mt-icons {
      font-size: 14px;
      color: #4f5b6d;
      margin-right: 5px;
    }
    span {
      word-break: keep-all;
      font-size: 14px;
      color: #4f5b6d;
      font-weight: normal;
    }
  }

  .flex1 {
    flex: 1;
  }
}
/deep/ .small-uploader {
}
</style>
