<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="modalData.title"
    :buttons="buttons"
    @close="handleClose"
  >
    <!-- 选择物料/品项编码、SKU、工厂（不过滤） -->
    <mt-select
      ref="itemRef"
      v-model="categoryItem"
      :popup-height="400"
      :fields="fields"
      :data-source="categoryItemData"
      :filtering="getList"
      :allow-filtering="modalData.fieldCode != 'siteName'"
      :placeholder="$t('请选择') + modalData.title"
    ></mt-select>
  </mt-dialog>
</template>

<script>
/**
 * 1.使用mt-select的filtering，在输入数据时，调用接口，重新赋值列表数据。此时，filtering，需要加debounce
 * 2.如果是'未匹配'到数据，使用用户输入数据，可以参考链接：
 *   https://ej2.syncfusion.com/vue/demos/#/material/multi-select/custom-value.html
 */

import { utils } from '@mtech-common/utils'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      categoryItem: '',
      categoryItemData: [], // 物料列表
      fields: {}
    }
  },
  mounted() {
    console.log('modalData', this.modalData)
    this.$nextTick(() => {
      this.$refs.dialog.ejsRef.show()

      this.getList = utils.debounce(this.getList, 1000)
    })

    if (this.modalData.fieldCode == 'skuCode') {
      this.fields = { text: 'name', value: 'id' }
    } else if (this.modalData.fieldCode == 'itemCode') {
      this.fields = { text: 'itemName', value: 'id' }
    } else if (this.modalData.fieldCode == 'siteName') {
      this.fields = { text: 'organizationName', value: 'organizationId' }
    }

    if (this.modalData.fieldCode == 'siteName') {
      // 根据物料ID查 工厂
      this.$API.masterData[this.modalData.requestUrl]({
        orgLevelTypeCode: 'ORG06',
        // itemId: this.modalData?.itemId,
        itemId: '333'
      }).then((res) => {
        console.log(res)
        let _categoryItemData = res.data
        // 带出品类数据 放到categoryTypeInfo里
        _categoryItemData.forEach((item) => {
          if (item?.itemRelResponseList?.length) {
            item.categoryTypeInfo = item?.itemRelResponseList.find(
              (c) => c?.categoryTypeCode == 'product'
            )
          } else {
            item.categoryTypeInfo = null
          }
        })
        this.categoryItemData = _categoryItemData
      })
    }
  },

  methods: {
    getList(e = { text: '' }) {
      if (this.modalData.fieldCode == 'skuCode') {
        // 分页查询
        this.$API.masterData[this.modalData.requestUrl]({
          // keyword: e.text,
          defaultRules: [
            {
              label: this.$t('SKU名称'),
              field: 'name',
              type: 'string',
              operator: 'contains',
              value: e.text
            }
          ],
          page: {
            size: 50,
            current: 1
          }
        }).then((res) => {
          console.log(res)
          this.categoryItemData = res.data.records
        })
      } else if (this.modalData.fieldCode == 'itemCode') {
        // 根据物料名称模糊查询
        this.$API.masterData[this.modalData.requestUrl]({
          keyword: e.text
        }).then((res) => {
          console.log(res)
          this.categoryItemData = res.data.records
        })
      }
    },

    confirm() {
      if (this.categoryItem) {
        let _idName = this.modalData.fieldCode != 'siteName' ? 'id' : 'organizationId' // 因为工厂的id是organizationId
        let _item = this.categoryItemData.find((item) => item[_idName] == this.categoryItem)
        this.$emit('handleAddDialogShow', false)
        this.$emit('confirmSuccess', {
          ..._item,
          fieldCode: this.modalData.fieldCode
        })
        this.handleClose()
      }
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
      this.$emit('handleAddDialogShow', false)
    }
  }
}
</script>

<style></style>
