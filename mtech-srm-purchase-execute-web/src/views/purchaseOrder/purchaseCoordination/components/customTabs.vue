<template>
  <div class="mt-tabs mt-tabs--height">
    <div class="tab-hori">
      <div id="tags-view-tabs" class="tab-wrap2" style="width: 100%">
        <ul class="tab-container">
          <li
            v-for="(item, index) in tabSource"
            :key="index"
            :class="['tab-item2', 'tab-item2--line', { active: index == currentIndex }]"
            @click="changeActiveTab(index, item)"
            :style="!item.title ? 'display: none' : ''"
          >
            {{ item.title }}
          </li>
          <!-- <li class="tab-item2 tab-item2--line active">456</li> -->
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currentIndex: null,
      tabSource: []
    }
  },
  props: {
    dataSource: {
      type: Array,
      default: () => []
    }
  },
  created() {
    this.dataSource.forEach((item, index) => {
      if (window.elementPermissionSet.some((i) => item.permissionCode === i)) {
        this.tabSource.push(item)
        if (this.currentIndex === null) {
          this.currentIndex = index
          this.changeActiveTab(index, item)
        }
      } else {
        this.tabSource.push({ ...item, title: '' })
      }
    })
  },
  methods: {
    changeActiveTab(index, item) {
      if (item.title) {
        this.currentIndex = index
        this.$emit('handleSelectTab', index, item)
      }
    }
  }
}
</script>

<style></style>
