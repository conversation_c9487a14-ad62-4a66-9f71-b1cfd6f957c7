<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="$t('新增')"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <mt-form-item prop="orderCode" :label="$t('采购订单号')">
        <mt-input
          v-model="ruleForm.orderCode"
          :show-clear-button="true"
          :placeholder="$t('请输入意见备注')"
          disabled
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="businessType" :label="$t('业务类型')">
        <mt-select
          v-model="ruleForm.businessType"
          :data-source="businessTypeOptions"
          :show-clear-button="true"
          :placeholder="$t('请选择业务类型')"
        ></mt-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>
<script>
export default {
  props: {
    businessTypeOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('关闭') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('下一步') }
        }
      ],
      ruleForm: {
        businessType: '',
        orderCode: this.$t('系统自动生成')
      },
      rules: {
        businessType: [{ required: true, message: this.$t('请选择业务类型'), trigger: 'blur' }]
      }
    }
  },
  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let label = ''
          let code = ''
          let id = ''
          this.businessTypeOptions.some((item) => {
            if (item.value === this.ruleForm.businessType) {
              label = item.label
              code = item.itemCode
              id = item.value
              return
            }
          })
          this.$router.push({
            name: 'purchase-coordination-detail',
            query: {
              label: label,
              code: code,
              id: id,
              type: '1',
              source: '1'
            }
          })
        }
      })
    },
    show() {
      this.$refs.dialog.ejsRef.show()
      this.$refs.ruleForm.resetFields()
      this.ruleForm = {
        businessType: '',
        orderCode: this.$t('系统自动生成')
      }
    }
  }
}
</script>
