<template>
  <div class="full-height">
    <!-- <customTabs :data-source="tabSource" @handleSelectTab="handleSelectTab"></customTabs> -->
    <!-- <mt-tabs
      :e-tab="false"
      :data-source="tabSource"
      v-permission="permissionObj"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs> -->
    <!-- <mt-template-page
      ref="template-0"
      :permission-obj="permissionObj"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
      <mt-template-page
        slot="slot-1"
        ref="templateRef2"
        :template-config="pageConfig1"
        @handleClickToolBar="handleClickToolBar2"
      >
        <div slot="slot-filter" class="top-filter1">
          <div class="left-status">
            <span class="tit">{{ $t("业务类型：") }} </span>
            <status-check
              ref="statusCheckRef"
              :status-data="businessTypeList"
              @handleChose="handleChose"
            ></status-check>
          </div>
        </div> </mt-template-page
    ></mt-template-page> -->
    <mt-template-page
      v-if="!isLoad"
      :template-config="pageConfig"
      :permission-obj="permissionObj"
      @handleSelectTab="handleSelectTab"
    >
      <mt-template-page
        slot="slot-0"
        ref="template-0"
        class="template-height"
        :template-config="componentConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
      >
      </mt-template-page>
      <mt-template-page
        slot="slot-1"
        ref="templateRef2"
        class="template-height"
        :template-config="pageConfig1"
        @handleClickToolBar="handleClickToolBar2"
      >
        <div slot="slot-filter" class="top-filter1">
          <div class="left-status">
            <span class="tit">{{ $t('业务类型：') }} </span>
            <status-check
              ref="statusCheckRef"
              :status-data="businessTypeList"
              @handleChose="handleChose"
            ></status-check>
          </div>
        </div>
      </mt-template-page>
      <mt-template-page
        slot="slot-2"
        ref="template-2"
        class="template-height"
        :template-config="componentConfig2"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
      >
      </mt-template-page>
      <mt-template-page
        slot="slot-3"
        ref="template-3"
        class="template-height"
        :template-config="componentConfig3"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
      >
      </mt-template-page>
    </mt-template-page>
    <add-dialog ref="addDialog" :business-type-options="businessTypeList"></add-dialog>
    <!-- <salesAddDialog
      ref="salesAddDialog"
      :business-type-options="businessTypeList"
    ></salesAddDialog> -->
    <acceptanceAccept @refreshColumns="refreshColumns" ref="acceptanceAccept"></acceptanceAccept>
  </div>
</template>

<script>
import * as CONFIG from './config'
// import Vue from 'vue'
import * as UTILS from '@/utils/utils'
import { editColumnBefore, editColumnEnd } from './config'
import { cloneDeep } from 'lodash'
export default {
  components: {
    statusCheck: () => import('@/components/businessComponents/statusCheck.vue'),
    AddDialog: () => import('./components/addDialog'),
    // salesAddDialog: () => import("../salesCoordination/components/addDialog"),
    acceptanceAccept: () => import('./components/acceptanceAccept')
    // customTabs: () => import('./components/customTabs')
  },
  data() {
    // let userInfo1 = JSON.parse(sessionStorage.getItem('userInfo'))
    // let employeeId = userInfo1.employeeId
    return {
      isLoad: false,
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0017' },
          { dataPermission: 'b', permissionCode: 'T_02_0018' },
          { dataPermission: 'c', permissionCode: 'T_02_0019' },
          { dataPermission: 'd', permissionCode: 'T_02_0105' }
        ]
      },
      tabIndex: 0,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      // tabSource: [
      //   {
      //     title: this.$t('单据视图'),
      //     permissionCode: 'T_02_0017'
      //   },
      //   {
      //     title: this.$t('明细视图'),
      //     permissionCode: 'T_02_0018'
      //   },
      //   {
      //     title: this.$t('验收计划汇总'),
      //     permissionCode: 'T_02_0019'
      //   },
      //   {
      //     title: this.$t('我的验收'),
      //     permissionCode: 'T_02_0105'
      //   }
      // ],
      pageConfig: [
        {
          title: this.$t('单据视图'),
          dataPermission: 'a',
          permissionCode: 'T_02_0017'
        },
        {
          title: this.$t('明细视图'),
          dataPermission: 'b',
          permissionCode: 'T_02_0018'
        },
        {
          title: this.$t('验收计划汇总'),
          dataPermission: 'c',
          permissionCode: 'T_02_0019'
        },
        {
          title: this.$t('我的验收'),
          dataPermission: 'd',
          permissionCode: 'T_02_0105'
        }
      ],

      businessTypeList: [],
      pageConfig1: [
        {
          dataPermission: 'b',
          permissionCode: 'T_02_0018', // 需要与permissionObj中的参数和权限code对应
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          activatedRefresh: false,

          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'Export1',
              icon: 'icon_solid_export',
              title: this.$t('导出'),
              permission: ['O_02_1070']
            }
          ],
          gridId: '',
          grid: {
            // height: "auto",
            columnData: [
              {
                width: '50',
                type: 'checkbox',
                showInColumnChooser: false
              }
            ],
            dataSource: [],
            virtualPageSize: 30,
            enableVirtualization: true,
            customSelection: true, // 使用自定义勾选列
            pageSettings: {
              currentPage: 1,
              pageSize: 100,
              pageSizes: [20, 50, 100, 200, 1000],
              totalRecordsCount: 0
            },
            showSelected: false,
            selectionSettings: {
              persistSelection: true, // 这个选项很重要，影响到虚拟滚动下能不能操作全选按钮
              type: 'Multiple',
              checkboxOnly: true
            }
          }
        }
      ],
      componentConfig: [
        {
          dataPermission: 'a',
          permissionCode: 'T_02_0017', // 需要与permissionObj中的参数和权限code对应
          // title: this.$t("单据视图"),
          activatedRefresh: false,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              title: this.$t('新增'),
              permission: ['O_02_0139']
            },
            {
              id: 'Publish',
              icon: 'icon_solid_pushorder',
              title: this.$t('发布'),
              permission: ['O_02_0140']
            },
            // {
            //   id: "Approve",
            //   icon: "icon_solid_Activateorder",
            //   title: "审批",
            // },
            // {
            //   id: "Synchronize",
            //   icon: "icon_table_synchronize",
            //   title: "同步",
            //   permission: ["O_02_0141"],
            // },
            {
              id: 'Expedited',
              icon: 'icon_solid_Activateorder',
              title: this.$t('加急'),
              permission: ['O_02_0571']
            },
            {
              id: 'CancleExpedited',
              icon: 'icon_solid_Activateorder',
              title: this.$t('取消加急'),
              permission: ['O_02_0572']
            },
            {
              id: 'sync',
              icon: 'icon_solid_Activateorder',
              title: this.$t('同步供方系统'),
              permission: ['O_02_1709']
            }
          ],

          gridId: this.$tableUUID.purchaseOrder.mainTab,
          grid: {
            columnData: CONFIG.columnData,
            virtualPageSize: 30,
            enableVirtualization: true,
            customSelection: true, // 使用自定义勾选列
            pageSettings: {
              currentPage: 1,
              pageSize: 100,
              pageSizes: [20, 50, 100, 200, 1000],
              totalRecordsCount: 0
            },
            showSelected: false,
            selectionSettings: {
              persistSelection: true, // 这个选项很重要，影响到虚拟滚动下能不能操作全选按钮
              type: 'Multiple',
              checkboxOnly: true
            },
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/purOrder/query',
              // ignoreDefaultSearch: true,

              serializeList: (list) => {
                list.forEach((item) => {
                  item.version1 = item.version
                })
                return list
              }
            }
          }
        }
      ],
      componentConfig2: [
        {
          dataPermission: 'c',
          permissionCode: 'T_02_0019', // 需要与permissionObj中的参数和权限code对应

          activatedRefresh: false,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'InvoiceAdvance',
              icon: 'a-icon_table_Invoicinginadvance',
              title: this.$t('提前开票'),
              permission: ['O_02_0148']
            },
            {
              id: 'CancleInvoiceAdvance',
              icon: 'a-icon_table_Canceladvanceinvoicing',
              title: this.$t('取消提前开票'),
              permission: ['O_02_0149']
            },
            {
              id: 'SharedPrepaid',
              icon: 'icon_table_SharedPrepayment',
              title: this.$t('共享预付'),
              permission: ['O_02_0150']
            },
            {
              id: 'Reimbursement',
              icon: 'icon_table_AccountReimbursement',
              title: this.$t('挂账报销'),
              permission: ['O_02_0151']
            }
          ],
          gridId: this.$tableUUID.purchaseOrder.acceptanceTab,
          grid: {
            virtualPageSize: 30,
            enableVirtualization: true,
            customSelection: true, // 使用自定义勾选列
            pageSettings: {
              currentPage: 1,
              pageSize: 100,
              pageSizes: [20, 50, 100, 200, 1000],
              totalRecordsCount: 0
            },
            showSelected: false,
            selectionSettings: {
              persistSelection: true, // 这个选项很重要，影响到虚拟滚动下能不能操作全选按钮
              type: 'Multiple',
              checkboxOnly: true
            },
            columnData: CONFIG.columnData3,
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/po/acceptance/query',
              serializeList: (list) => {
                list.forEach((item) => {
                  item.files = item.files || []
                  item.files.forEach((item1) => {
                    item1.remoteUrl = item1.url
                  })
                  item.file = JSON.stringify(item.files)
                  let str = this.$t('未延期')
                  if (
                    item.acceptanceStatus == 0 &&
                    new Date().getTime() > Number(item.preAcceptanceTime)
                  ) {
                    str = this.$t('已延期')
                  }
                  if (
                    item.acceptanceStatus == 1 &&
                    Number(item.acceptanceTime) > Number(item.preAcceptanceTime)
                  ) {
                    str = this.$t('已延期')
                  }
                  item.deliveryStatus1 = str
                })
                return list
              }
            }
          }
        }
      ],
      componentConfig3: [
        {
          dataPermission: 'd',
          permissionCode: 'T_02_0105', // 需要与permissionObj中的参数和权限code对应

          activatedRefresh: false,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          gridId: this.$tableUUID.purchaseOrder.acceptancemyTab,
          grid: {
            virtualPageSize: 30,
            enableVirtualization: true,
            customSelection: true, // 使用自定义勾选列
            pageSettings: {
              currentPage: 1,
              pageSize: 100,
              pageSizes: [20, 50, 100, 200, 1000],
              totalRecordsCount: 0
            },
            showSelected: false,
            selectionSettings: {
              persistSelection: true, // 这个选项很重要，影响到虚拟滚动下能不能操作全选按钮
              type: 'Multiple',
              checkboxOnly: true
            },
            columnData: CONFIG.columnData2,
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/po/acceptance/query',
              serializeList: (list) => {
                list.forEach((item) => {
                  item.files = item.files || []
                  item.files.forEach((item1) => {
                    item1.remoteUrl = item1.url
                  })
                  item.file = JSON.stringify(item.files)
                  let str = this.$t('未延期')
                  if (
                    item.acceptanceStatus == 0 &&
                    new Date().getTime() > Number(item.preAcceptanceTime)
                  ) {
                    str = this.$t('已延期')
                  }
                  if (
                    item.acceptanceStatus == 1 &&
                    Number(item.acceptanceTime) > Number(item.preAcceptanceTime)
                  ) {
                    str = this.$t('已延期')
                  }
                  item.deliveryStatus1 = str
                })
                return list
              }
            }
          }
        }
      ],
      businessTypeCode: ''
    }
  },
  async created() {
    this.isLoad = !this.isLoad
    // 下方的循环是为了解决tab因为配置了权限导致的表格内容不渲染
    this.pageConfig.forEach((i) => {
      const elementPermissionSet = window.elementPermissionSet
      if (!elementPermissionSet.includes(i.permissionCode)) {
        delete i.permissionCode
      }
    })
    await this.getBusinessConfig()
    // this.test()
    // const a = Array.from(document.querySelectorAll('li'))
    // for (let i = 0; i < a.length; i++) {
    //   if (a[i].getAttribute('permissioncode')) {
    //     for (let j = 0; j < this.contentConfig.length; j++) {
    //       console.log('getTabsIndex', a[i].getAttribute('permissioncode'), this.contentConfig[0])
    //       if (a[i].getAttribute('permissioncode') == this.contentConfig[j].permissionCode) {
    //         console.log('getTabsIndex2', this.contentConfig)
    //         this.currentTabIndex = j
    //         return
    //       }
    //     }
    //   }
    // }
  },
  mounted() {
    if (this.$route.query.from === 'mytodo0') {
      const _rules = JSON.parse(sessionStorage.getItem('todoDetail')) || {
        rules: [],
        defaultRules: []
      }
      this.componentConfig[0].grid.asyncConfig = {
        url: '/srm-purchase-execute/tenant/purOrder/query',
        serializeList: (list) => {
          list.forEach((item) => {
            item.version1 = item.version
          })
          return list
        },
        rules: _rules.rules,
        defaultRules: _rules.defaultRules
      }
    }
  },
  methods: {
    //点击顶部的操作按钮
    handleClickToolBar2(e) {
      console.log('方法1', e)
      console.log(e.gridRef.getMtechGridRecords(), e.toolbar.id)
      if (e.toolbar.id === 'Export1') {
        this.handleExport(e)
        return
      }
    },
    handleExport(e) {
      let obj = JSON.parse(sessionStorage.getItem(this.pageConfig1[0].gridId))?.visibleCols
      const headerMap = {}
      if (obj !== undefined && obj.length) {
        obj?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      } else {
        this.pageConfig1[0].grid.columnData?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      }
      //导出
      let rule = this.$refs.templateRef2.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const ids = []
      e.gridRef.dataSource.forEach((item) => {
        if (item.customChecked) {
          ids.push(item.id)
        }
      })
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 50000 },
        pageFlag: true,
        rules: [
          {
            condition: 'and',
            field: 'businessTypeCode',
            operator: 'equal',
            value: this.businessTypeCode
          },
          {
            condition: 'and',
            field: 'id',
            operator: 'in',
            value: ids
          }
        ]
      }
      if (rule?.rules?.length) {
        params.rules = params.rules.concat(rule.rules)
      }
      this.$store.commit('startLoading')
      this.$API.purchaseOrder.poDetailDownload(params, headerMap).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 顶部筛选 调整
    handleChose(e, item) {
      this.getColumnModule(item.itemCode, e)
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    //重新赋值asynConfig
    purOrderDetail(e, code) {
      this.businessTypeCode = code
      this.$set(this.pageConfig1[0].grid, 'asyncConfig', {
        url: '/srm-purchase-execute/tenant/po/detail/pageQuery',
        // ignoreDefaultSearch: true,
        defaultRules: [
          {
            condition: 'and',
            field: 'business_type_id',
            label: '',
            operator: 'equal',
            value: e
          }
        ],
        serializeList: (list) => {
          list.forEach((item) => {
            console.log(item.closeStatus)
            item.fieldDataList = item.fieldDataList || []
            item.fieldDataList.forEach((item1) => {
              //对应的动态字段 需求名称,需求描述,资产类别,资产编号,资产卡片,客户,关联客户订单
              item[item1.fieldCode] = item1.fieldData
            })

            if (item.costs) {
              item.orderCosts = `${item.costs[0].costCenterCode}-${item.costs[0].costCenterName}`
            }
            item.version1 = item.version
            if (item.taxTotal < 0) {
              item.taxTotal = '*'
            }

            if (item.taxPrice < 0) {
              item.taxPrice = '*'
            }
            if (item.freeTotal < 0) {
              item.freeTotal = '*'
            }
            if (item.freePrice < 0) {
              item.freePrice = '*'
            }
            if (item.unPrice < 0) {
              item.unPrice = ''
            }
            if (item.unTotal < 0) {
              item.unTotal = ''
            }
            if (item.approvedTotalPrice < 0) {
              item.approvedTotalPrice = ''
            }
            if (item.budgetTotalPrice < 0) {
              item.budgetTotalPrice = ''
            }
            if (item.budgetUnitPrice < 0) {
              item.budgetUnitPrice = ''
            }
            if (item.taxedTotalPrice < 0) {
              item.taxedTotalPrice = ''
            }
            if (item.taxedUnitPrice < 0) {
              item.taxedUnitPrice = ''
            }
            if (item.subjectTotal < 0) {
              item.subjectTotal = ''
            }
            // if (item.closeStatus === 0) {
            //   item.closeStatus = "未关闭";
            // }
            // if (item.closeStatus === 1) {
            //   item.closeStatus = "已关闭";
            // }
          })

          console.log(list, 'list')

          return list
        }
      })
      this.pageConfig1[0].gridId = this.$md5(this.$tableUUID.purchaseOrder.detailTab + e)
    },
    test() {
      //测试接口的后面删了 顶部详情 保存草稿订单 保存订单
      // this.$API.purchaseOrder
      //   .getTenantPurOrder("1442414296039448577")
      //   .then((res) => {
      //     console.log(res);
      //   });
    },
    //获取动态表头
    getColumnModule(code, e) {
      let params = {
        businessType: e,
        docType: 'po',
        moduleType: 6,
        tenantId: 0
      }
      this.$API.purchaseOrder.getbusinessModuleFields(params).then((res) => {
        let flag = false
        if (res.data && res.data.length) {
          let _columnData = cloneDeep(editColumnBefore)
          let _columnData1 = cloneDeep(editColumnEnd)
          this.pageConfig1[0].grid.columnData = _columnData
            .concat(
              res.data.map((item1) => {
                let obj = {}
                let hasItem = false
                CONFIG.columnData1.some((item2) => {
                  if (item2.field === item1.fieldCode) {
                    obj = {
                      ...item2,
                      headerText:
                        item1.fieldCode === 'itemCode' ? this.$t('物料编码') : item1.fieldName
                    }
                    if (
                      item1.fieldCode === 'taxTotal' ||
                      item1.fieldCode === 'taxPrice' ||
                      item1.fieldCode === 'freeTotal' ||
                      item1.fieldCode === 'freePrice' ||
                      item1.fieldCode === 'budgetTotalPrice' ||
                      item1.fieldCode === 'budgetUnitPrice'
                    ) {
                      obj.visible = false
                    }
                    hasItem = true
                  }
                })
                if (hasItem) {
                  return obj
                } else {
                  return {
                    width: '145',
                    visible:
                      item1.fieldCode === 'taxTotal' ||
                      item1.fieldCode === 'taxPrice' ||
                      item1.fieldCode === 'freeTotal' ||
                      item1.fieldCode === 'freePrice'
                        ? false
                        : true,
                    ...item1,
                    headerText:
                      item1.fieldCode === 'itemCode' ? this.$t('物料编码') : item1.fieldName,
                    field: item1.fieldCode
                  }
                }
              })
            )
            .concat(_columnData1)
          flag = true
          this.pageConfig1[0].grid.columnData.forEach((item) => {
            if (item.field === 'closeStatus') {
              item.valueConverter = {
                type: 'map',
                map: {
                  0: this.$t('未关闭'),
                  1: this.$t('已关闭')
                }
              }
            }
            if (code === 'BTTCL004') {
              if (item.field === 'siteName') {
                item.searchOptions = {
                  elementType: 'remote-autocomplete',
                  fields: { text: 'dimensionNameValue', value: 'dimensionCodeValue' },
                  multiple: true,
                  url: '/srm-purchase-execute/tenant/common/permission/querySiteList',
                  recordsPosition: 'data',
                  paramsKey: 'keyWord',
                  renameField: 'siteCode'
                }
              } else if (item.field === 'companyCode') {
                item.searchOptions = {
                  elementType: 'remote-autocomplete',
                  fields: { text: 'dimensionNameValue', value: 'dimensionCodeValue' },
                  multiple: true,
                  url: '/srm-purchase-execute/tenant/common/permission/queryCompanyList',
                  recordsPosition: 'data',
                  paramsKey: 'keyWord'
                }
              }
            }
          })
          console.log(this.pageConfig1[0].grid.columnData, '123')
        }
        if (!flag) {
          let _columnData = cloneDeep(editColumnBefore)
          let _columnData1 = cloneDeep(editColumnEnd)
          this.pageConfig1[0].grid.columnData = _columnData.concat(_columnData1)
          console.log(this.pageConfig1[0].grid.columnData, '123')
        }
        this.purOrderDetail(e, code)
      })
    },
    // 获取业务类型下拉
    async getBusinessConfig() {
      // this.$API.masterData
      //   .getDictCode({ dictCode: "businessType" })
      //   .then((res) => {
      //     if (res.data && res.data.length) {
      //       this.businessTypeList = [];
      //       res.data.map((item) => {
      //         this.businessTypeList.push({
      //           label: item.itemName,
      //           value: item.id,
      //           itemCode: item.itemCode,
      //           text: item.itemName,
      //         });
      //       });
      //     }
      //   });
      let userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      let params = {
        subjectId: userInfo.uid,
        dimensionCode: 'BUSINESS_TYPE',
        billTypeCode: 'po_item',
        tableName: 'mt_buyer_order'
      }
      await this.$API.purchaseOrder
        .getBusinessType(params)
        .then((res) => {
          // res.data = res.data.concat(res.data);
          if (res.data && res.data.length) {
            this.businessTypeList = []
            res.data.map((item) => {
              this.businessTypeList.push({
                label: item.dimensionNameValue,
                value: item.dimensionIdValue,
                itemCode: item.dimensionCodeValue,
                text: item.dimensionNameValue
              })
            })
          }
          this.isLoad = false
        })
        .catch(() => {
          this.isLoad = false
        })
    },
    handleClickToolBar(e) {
      // const selectRows = e.gridRef.getMtechGridRecords()
      let selectRows = []
      e.gridRef.dataSource.forEach((item) => {
        if (item.customChecked) {
          selectRows.push(item)
        }
      })
      const commonToolbar = [
        'Add',
        'Filter',
        'Refresh',
        'Setting',
        'Synchronize',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]
      if (selectRows.length === 0 && !commonToolbar.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let ids = []
      selectRows.map((item) => ids.push(item.id))
      let orderCodes = []
      selectRows.map((item) => orderCodes.push(item.orderCode))
      switch (e.toolbar.id) {
        case 'Add':
          this.handleAdd()
          break
        case 'Publish':
          this.handlePublish(ids, selectRows)
          break
        case 'Approve':
          this.handleApprove(ids)
          break
        case 'InvoiceAdvance':
          this.handleInvoiceAdvance(ids, selectRows)
          break
        case 'CancleInvoiceAdvance':
          this.handleCancleInvoiceAdvance(ids, selectRows)
          break
        case 'Synchronize':
          this.handleSynchronize()
          break
        case 'SharedPrepaid':
          this.handleSharedPrepaid(ids, selectRows)
          break
        case 'Reimbursement':
          this.handleReimbursement(ids, selectRows)
          break
        case 'Expedited':
          this.handleExpedited(ids, selectRows, orderCodes)
          break
        case 'CancleExpedited':
          this.handleCancleExpedited(ids, selectRows, orderCodes)
          break
        case 'sync':
          this.handleSync(ids, selectRows)
          break
      }
    },
    handleSync(ids, selectRows) {
      if (selectRows.length === 1) {
        // 判断是否为同步失败的数据
        if (selectRows[0].syncSupSysStatus !== 1) {
          this.apiStartLoading()
          let params = { ids }
          this.$API.purchaseOrder
            .syncSupplierSystemApi(params)
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: this.$t('操作成功!'),
                  type: 'success'
                })
                this.$refs[`template-0`].refreshCurrentGridData()
              }
            })
            .finally(() => {
              this.apiEndLoading()
            })
        } else {
          this.$toast({
            content: this.$t('只能选择同步供方系统状态为失败或未匹配的数据进行操作!'),
            type: 'warning'
          })
        }
      } else {
        this.$toast({
          content: this.$t('只能选择一行数据进行操作!'),
          type: 'warning'
        })
      }
    },
    handleCancleExpedited(ids, selectRows, orderCodes) {
      //取消加急订单
      let hasOne = selectRows.some((item) => {
        return !(item.urgentTime && item.urgentTime.length === 13)
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择已加急的的行操作!'),
          type: 'warning'
        })
        return
      }
      let params = {
        type: 0,
        urgentList: []
      }
      orderCodes.forEach((item) => {
        params.urgentList.push({
          ids: [],
          itemNos: [],
          orderCode: item
        })
      })
      this.$API.purchaseOrder.purOrderUrgentOrder(params).then((res) => {
        if (res.data.length) {
          let str = ''
          res.data.forEach((item) => {
            console.log(item)
            str = str + `${item.code} ${item.msg}`
          })
          this.$toast({ content: str, type: 'error' })
        } else {
          this.$toast({
            content: this.$t('取消加急订单操作成功'),
            type: 'success'
          })
          if (this.tabIndex === 0) {
            this.$refs[`template-0`].refreshCurrentGridData()
          } else if (this.tabIndex === 2) {
            this.$refs[`template-2`].refreshCurrentGridData()
          } else if (this.tabIndex === 3) {
            this.$refs[`template-3`].refreshCurrentGridData()
          }
        }
      })
    },
    handleExpedited(ids, selectRows, orderCodes) {
      //加急订单
      let hasOne = selectRows.some((item) => {
        return item.urgentTime && item.urgentTime.length === 13
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择普通的的行操作!'),
          type: 'warning'
        })
        return
      }
      let params = {
        type: 1,
        urgentList: []
      }
      orderCodes.forEach((item) => {
        params.urgentList.push({
          ids: [],
          itemNos: [],
          orderCode: item
        })
      })
      this.$API.purchaseOrder.purOrderUrgentOrder(params).then((res) => {
        if (res.data.length) {
          let str = ''
          res.data.forEach((item) => {
            str = str + `${item.code} ${item.msg}`
          })
          this.$toast({ content: str, type: 'error' })
        } else {
          this.$toast({
            content: this.$t('加急订单操作成功'),
            type: 'success'
          })
          if (this.tabIndex === 0) {
            this.$refs[`template-0`].refreshCurrentGridData()
          } else if (this.tabIndex === 2) {
            this.$refs[`template-2`].refreshCurrentGridData()
          } else if (this.tabIndex === 3) {
            this.$refs[`template-3`].refreshCurrentGridData()
          }
        }
      })
    },
    // 同步 处理
    handleSynchronize(params = {}) {
      this.apiStartLoading()
      this.$API.purchaseOrder
        .getPurOrderSync(params)
        .then((res) => {
          this.apiEndLoading()
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleReimbursement(ids, selectRows) {
      //挂账报销
      if (ids.length !== 1) {
        this.$toast({
          content: this.$t('请选择一行数据进行操作!'),
          type: 'warning'
        })
        return
      }
      if (
        selectRows[0].chargeStatus !== 1 ||
        selectRows[0].acceptanceStatus !== 1 ||
        selectRows[0].advanceInvoiceStatus !== 1
      ) {
        //预付标识是 且验收后
        this.$toast({
          content: this.$t('请选择是否挂账标识为是 且 提前开票且已验收的行!'),
          type: 'warning'
        })
        return
      }
      console.log(this.$t('挂账报销'), ids, selectRows)
      let params = { acceptanceId: ids[0] }
      this.$API.purchaseOrder.pushReimbursement(params).then(() => {
        this.$toast({ content: this.$t('挂账报销操作成功'), type: 'success' })
        if (this.tabIndex === 0) {
          this.$refs[`template-0`].refreshCurrentGridData()
        } else if (this.tabIndex === 2) {
          this.$refs[`template-2`].refreshCurrentGridData()
        } else if (this.tabIndex === 3) {
          this.$refs[`template-3`].refreshCurrentGridData()
        }
      })
    },
    handleSharedPrepaid(ids, selectRows) {
      //共享预付
      if (ids.length > 50) {
        this.$toast({
          content: this.$t('请选择最多50行进行操作!'),
          type: 'warning'
        })
        return
      }
      let hasOne = selectRows.some((item) => {
        return item.advancePayStatus !== 1 || item.acceptanceStatus !== 1
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择预付标识为是且已验收的行进行操作!'),
          type: 'warning'
        })
        return
      }
      // if (
      //   selectRows[0].advancePayStatus !== 1 ||
      //   selectRows[0].acceptanceStatus !== 1
      // ) {
      //   //预付标识是 且验收后
      //   this.$toast({
      //     content: this.$t("请选择预付标识为是且已验收的行进行操作!"),
      //     type: "warning",
      //   });
      //   return;
      // }
      console.log('共享预付', ids, selectRows)
      let params = { acceptanceIdList: ids }
      this.$API.purchaseOrder.pushSharedFinance(params).then(() => {
        this.$toast({ content: this.$t('共享预付操作成功'), type: 'success' })
        if (this.tabIndex === 0) {
          this.$refs[`template-0`].refreshCurrentGridData()
        } else if (this.tabIndex === 2) {
          this.$refs[`template-2`].refreshCurrentGridData()
        } else if (this.tabIndex === 3) {
          this.$refs[`template-3`].refreshCurrentGridData()
        }
      })
    },
    handleCancleInvoiceAdvance(ids, selectRows) {
      //取消提前开票
      let hasOne = selectRows.some((item) => {
        return (
          item.acceptanceStatus !== 0 || item.chargeStatus !== 1 || item.advanceInvoiceStatus === 0
        )
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择未验收状态下，同时是否挂账：为是 且提前开票 的行操作!'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提前开票'),
          message: this.$t('确认提前开票选中的订单？')
        },
        success: () => {
          let params = {
            acceptanceIds: ids,
            files: []
          }
          this.$API.purchaseOrder.acceptanceUnAdvance(params).then(() => {
            this.$toast({
              content: this.$t('取消提前开票操作成功'),
              type: 'success'
            })
            if (this.tabIndex === 0) {
              this.$refs[`template-0`].refreshCurrentGridData()
            } else if (this.tabIndex === 2) {
              this.$refs[`template-2`].refreshCurrentGridData()
            } else if (this.tabIndex === 3) {
              this.$refs[`template-3`].refreshCurrentGridData()
            }
          })
        }
      })
    },
    handleInvoiceAdvance(ids, selectRows) {
      //提前开票
      let hasOne = selectRows.some((item) => {
        return (
          item.acceptanceStatus !== 0 || item.chargeStatus !== 1 || item.advanceInvoiceStatus === 1
        )
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择未验收状态下，同时是否挂账：为是 且未提前开票的行操作!'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提前开票'),
          message: this.$t('确认提前开票选中的订单？')
        },
        success: () => {
          let params = {
            acceptanceIds: ids,
            files: []
          }
          this.$API.purchaseOrder.acceptanceAdvance(params).then(() => {
            this.$toast({
              content: this.$t('提前开票操作成功'),
              type: 'success'
            })
            if (this.tabIndex === 0) {
              this.$refs[`template-0`].refreshCurrentGridData()
            } else if (this.tabIndex === 2) {
              this.$refs[`template-2`].refreshCurrentGridData()
            } else if (this.tabIndex === 3) {
              this.$refs[`template-3`].refreshCurrentGridData()
            }
          })
        }
      })
    },
    handleApprove(ids) {
      this.$dialog({
        data: {
          title: this.$t('审批订单'),
          message: this.$t('确认审批选中的订单？')
        },
        success: () => {
          let params = {
            orderId: ids[0],
            remark: this.$t('审批同意'),
            status: 2
          }
          this.$API.purchaseOrder.purOrderApprove(params).then(() => {
            this.$toast({ content: this.$t('审批订单操作成功'), type: 'success' })
            if (this.tabIndex === 0) {
              this.$refs[`template-0`].refreshCurrentGridData()
            } else if (this.tabIndex === 2) {
              this.$refs[`template-2`].refreshCurrentGridData()
            } else if (this.tabIndex === 3) {
              this.$refs[`template-3`].refreshCurrentGridData()
            }
          })
        }
      })
    },
    //发布
    handlePublish(ids, selectRows) {
      let hasOne = selectRows.some((item) => {
        return item.showStatus !== this.$t('待发布') && item.showStatus !== this.$t('反馈异常')
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择待发布或者反馈异常状态订单操作!'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('发布订单'),
          message: this.$t('确认发布选中的订单？')
        },
        success: () => {
          let params = {
            orderIds: ids,
            checkStatus: true
          }
          this.$API.purchaseOrder.purOrderPublish(params).then(() => {
            this.$toast({ content: this.$t('发布操作成功'), type: 'success' })
            if (this.tabIndex === 0) {
              this.$refs[`template-0`].refreshCurrentGridData()
            } else if (this.tabIndex === 2) {
              this.$refs[`template-2`].refreshCurrentGridData()
            } else if (this.tabIndex === 3) {
              this.$refs[`template-3`].refreshCurrentGridData()
            }
          })
        }
      })
    },
    // 新增
    handleAdd() {
      // this.$refs.addDialog.show();
      this.$router.push({
        name: 'purchase-coordination-detail',
        query: {
          type: '1',
          source: '1'
        }
      })
    },
    //用来操作行按钮操作
    handleClickCellTool(e) {
      switch (e.tool.id) {
        case 'btn1':
          this.handleBtn1()
          break
        case 'btn2':
          this.handleBtn2(e.data)
          break
        case 'btn3':
          this.handleBtn3(e.data)
          break
        case 'btn4':
          this.handleBtn4(e.data)
          break
        case 'edit':
          this.handleEdit(e.data)
          break
        case 'see':
          this.$router.push({
            name: 'purchase-coordination-detail',
            query: {
              orderid: e.data.id,
              type: '3'
            }
          })
          break
        case 'ys':
          this.handleYs(e.data)
          break
        case 'ch':
          this.handleCh(e.data)
          break
        case 'qr':
          this.handleQr(e.data)
          break
        case 'bh':
          this.handleBh(e.data)
          break
      }
    },
    // 确认
    handleQr(row) {
      this.$dialog({
        modal: () => import('./components/acceptanceConfirm.vue'),
        data: {
          title: this.$t('确认'),
          rowData: row
          // message: this.$t('确认驳回验收？')
        },
        success: (formInfo) => {
          let params = {
            ...formInfo,
            acceptanceIds: [row.id],
            acceptanceStatus: 1
          }
          this.$API.purchaseOrder.acceptanceUnConfirm(params).then(() => {
            this.$toast({
              content: this.$t('确认验收操作成功'),
              type: 'success'
            })
            if (this.tabIndex === 0) {
              this.$refs[`template-0`].refreshCurrentGridData()
            } else if (this.tabIndex === 2) {
              this.$refs[`template-2`].refreshCurrentGridData()
            } else if (this.tabIndex === 3) {
              this.$refs[`template-3`].refreshCurrentGridData()
            }
          })
        }
      })
    },
    // 驳回
    handleBh(row) {
      this.$dialog({
        data: {
          title: this.$t('驳回验收'),
          message: this.$t('确认驳回验收？')
        },
        success: () => {
          let params = {
            acceptanceIds: [row.id],
            acceptanceStatus: 0
          }
          this.$API.purchaseOrder.acceptanceUnConfirm(params).then(() => {
            this.$toast({
              content: this.$t('驳回验收操作成功'),
              type: 'success'
            })
            if (this.tabIndex === 0) {
              this.$refs[`template-0`].refreshCurrentGridData()
            } else if (this.tabIndex === 2) {
              this.$refs[`template-2`].refreshCurrentGridData()
            } else if (this.tabIndex === 3) {
              this.$refs[`template-3`].refreshCurrentGridData()
            }
          })
        }
      })
    },
    handleCh(row) {
      //撤销验收
      if (row.acceptanceCodeRel) {
        this.$toast({
          content: this.$t('有同步单据号，不允许撤回!'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('撤销验收'),
          message: this.$t('确认撤销验收？')
        },
        success: () => {
          let params = {
            acceptanceIds: [row.id],
            files: [],
            remark: ''
          }
          this.$API.purchaseOrder.acceptanceUnAccept(params).then(() => {
            this.$toast({
              content: this.$t('撤销验收操作成功'),
              type: 'success'
            })
            if (this.tabIndex === 0) {
              this.$refs[`template-0`].refreshCurrentGridData()
            } else if (this.tabIndex === 2) {
              this.$refs[`template-2`].refreshCurrentGridData()
            } else if (this.tabIndex === 3) {
              this.$refs[`template-3`].refreshCurrentGridData()
            }
          })
        }
      })
    },
    refreshColumns() {
      //验收成功刷新列表
      if (this.tabIndex === 0) {
        this.$refs[`template-0`].refreshCurrentGridData()
      } else if (this.tabIndex === 2) {
        this.$refs[`template-2`].refreshCurrentGridData()
      } else if (this.tabIndex === 3) {
        this.$refs[`template-3`].refreshCurrentGridData()
      }
    },
    handleYs(row) {
      //验收
      this.$refs.acceptanceAccept.dialogInit(row.id)
    },
    //编辑
    handleEdit(row) {
      console.log(row, '编辑带入列表数据')
      this.$router.push({
        name: 'purchase-coordination-detail',
        query: {
          orderid: row.id,
          type: '2',
          source: row.source.toString(),
          draft: row.showStatus === this.$t('草稿') ? '1' : '2'
        }
      })
    },
    //查看审批
    handleBtn1() {
      console.log('查看审批')
    },
    //售后
    handleBtn2() {
      // console.log("售后");
      // this.$refs.salesAddDialog.show(row);
    },
    // 关闭
    handleBtn3(row) {
      this.$dialog({
        data: {
          title: this.$t('关闭订单'),
          message: this.$t('确认关闭该订单？')
        },
        success: () => {
          // let params = {
          //   orderId: row.id,
          //   remark: row.remark,
          //   status: 4,
          // };
          let params = {
            itemNos: [],
            orderCode: row.orderCode,
            orderId: row.id
          }
          this.$API.purchaseOrder.poDetailClose(params).then(() => {
            this.$toast({ content: this.$t('关闭操作成功'), type: 'success' })
            if (this.tabIndex === 0) {
              this.$refs[`template-0`].refreshCurrentGridData()
            } else if (this.tabIndex === 2) {
              this.$refs[`template-2`].refreshCurrentGridData()
            } else if (this.tabIndex === 3) {
              this.$refs[`template-3`].refreshCurrentGridData()
            }
          })
        }
      })
    },
    // 完成
    handleBtn4(row) {
      this.$dialog({
        data: {
          title: this.$t('完成订单'),
          message: this.$t('确认完成该订单？')
        },
        success: () => {
          let params = {
            orderId: row.id,
            remark: row.remark,
            status: 5
          }
          this.$API.purchaseOrder.purOrderDone(params).then(() => {
            this.$toast({ content: this.$t('完成操作成功'), type: 'success' })
            if (this.tabIndex === 0) {
              this.$refs[`template-0`].refreshCurrentGridData()
            } else if (this.tabIndex === 2) {
              this.$refs[`template-2`].refreshCurrentGridData()
            } else if (this.tabIndex === 3) {
              this.$refs[`template-3`].refreshCurrentGridData()
            }
          })
        }
      })
    },
    //用来跳转反馈异常
    handleClickCellTitle(e) {
      console.log('点击表格的数据事件', e)
      if (e.field === 'orderCode') {
        this.$router.push({
          name: 'purchase-coordination-detail',
          query: {
            orderid: e.data.id,
            type: '3'
          }
        })
      }
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style scoped lang="scss">
.full-height {
  /deep/ .common-template-page {
    height: calc(100% - 15px) !important;
  }
  /deep/ .top-filter1 {
    height: 40px;
    padding: 0 20px;
    display: flex;
    margin-bottom: 10px;
    .left-status {
      overflow: hidden;
      margin-top: 10px;
      display: flex;
      align-items: center;
      .tit {
        white-space: nowrap;
      }
    }
  }
}
</style>
