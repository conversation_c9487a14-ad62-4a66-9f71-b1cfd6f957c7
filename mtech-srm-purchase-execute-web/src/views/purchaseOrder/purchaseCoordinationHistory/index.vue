<template>
  <div class="full-height">
    <mt-template-page
      ref="template-0"
      class="template-height"
      :template-config="componentConfig"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import * as CONFIG from './config'
import dayjs from 'dayjs'
export default {
  components: {},
  data() {
    return {
      componentConfig: [
        {
          dataPermission: 'a',
          permissionCode: 'T_02_0017', // 需要与permissionObj中的参数和权限code对应
          activatedRefresh: false,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          gridId: this.$tableUUID.purchaseOrder.mainTab,
          grid: {
            columnData: CONFIG.columnData,
            virtualPageSize: 30,
            enableVirtualization: true,
            customSelection: true, // 使用自定义勾选列
            pageSettings: {
              currentPage: 1,
              pageSize: 100,
              pageSizes: [20, 50, 100, 200, 1000],
              totalRecordsCount: 0
            },
            showSelected: false,
            selectionSettings: {
              persistSelection: true, // 这个选项很重要，影响到虚拟滚动下能不能操作全选按钮
              type: 'Multiple',
              checkboxOnly: true
            },
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/HistoryOrder/query',
              serializeList: (list) => {
                list.forEach((item) => {
                  item.version1 = item.version
                })
                return list
              }
            }
          }
        }
      ]
    }
  },
  methods: {
    handleClickCellTitle(e) {
      if (e.field === 'orderCode') {
        this.$router.push({
          name: 'purchase-coordination-history-detail',
          query: {
            orderId: e.data.id,
            orderCode: e.data.orderCode,
            createUserName: e.data.createUserName,
            createTime: dayjs(Number(e.data.createTime)).format('YYYY-MM-DD'),
            businessTypeName: e.data.businessTypeName,
            orderTypeName: e.data.orderTypeName,
            company: e.data.companyCode + '-' + e.data.companyName,
            supplier: e.data.supplierCode + '-' + e.data.supplierName,
            paymentName: e.data.paymentName,
            buyerUserName: e.data.buyerUserName,
            buyerGroup: e.data.buyerGroupCode + '-' + e.data.buyerGroupName,
            buyerOrg: e.data.buyerOrgCode + '-' + e.data.buyerOrgName,
            currency: e.data.currencyCode + '-' + e.data.currencyName,
            taxTotal: e.data.taxTotal,
            freeTotal: e.data.freeTotal,
            remark: e.data.remark,
            supRemark: e.data.supRemark
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.full-height {
  /deep/ .common-template-page {
    height: calc(100% - 15px) !important;
  }
}
</style>
