import UTILS from '../../../../utils/utils'
import Vue from 'vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'

export const toolbar = [
  // {
  //   code: 'export',
  //   status: 'primary',
  //   name: i18n.t('导出')
  // }
]

export const columnData = [
  //采购订单列表
  // {
  //   width: '50',
  //   type: 'checkbox',
  //   showInColumnChooser: false
  // },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    isPrimaryKey: true
  },
  {
    field: 'contractNo',
    headerText: i18n.t('合同编号'),
    width: '150'
  },
  {
    field: 'orderCode',
    headerText: i18n.t('采购订单号'),
    width: '150',
    searchOptions: {
      placeholder: i18n.t('多个订单号需要用英文逗号隔开'),
      operator: 'in',
      serializeValue: (e) => {
        let obj = e.split(',')
        //自定义搜索值，规则
        return obj
      },
      maxQueryValueLength: 10000
    },
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        permission: ['O_02_0142'],
        visibleCondition: (data) => {
          let isShow = false
          if (
            (data.showStatus === '草稿' ||
              data.showStatus === '待发布' ||
              data.showStatus === '审批不通过') &&
            data.showStatus !== '完成' &&
            data.showStatus !== '关闭' &&
            data.showStatus !== '待审批' &&
            data.source !== 3 // 3 来自SAP的数据
          ) {
            isShow = true
          }
          if (
            (data.feedbackStatus == 1 || data.feedbackStatus == 2) &&
            data.businessTypeIfEdit &&
            data.showStatus !== '完成' &&
            data.showStatus !== '关闭' &&
            data.showStatus !== '待审批' &&
            data.source !== 3 // 3 来自SAP的数据
          ) {
            // 对于供方接收/供方拒绝状态下 && 根据策略配置中的该业务类型决定是否允许编辑
            isShow = true
          }

          // 反馈正常的状态不能编辑，针对非采（业务类型为：固资新增，固资装修，费用类）的订单；
          if (
            data.showStatus === '反馈正常' &&
            (data.businessTypeCode === 'BTTCL001' ||
              data.businessTypeCode === 'BTTCL002' ||
              data.businessTypeCode === 'BTTCL003')
          ) {
            // 对于供方接收/供方拒绝状态下 && 根据策略配置中的该业务类型决定是否允许编辑
            isShow = false
          }

          return isShow
        }
      }
    ]
  },
  {
    field: 'showStatus',
    headerText: i18n.t('订单状态'),
    width: '103',
    valueConverter: {
      type: 'map',
      map: [
        { value: '完成', text: i18n.t('完成'), cssClass: 'col-active' },
        {
          value: '关闭',
          text: i18n.t('关闭'),
          cssClass: 'col-inactive'
        }
      ]
    },
    cellTools: [
      {
        id: 'btn1',
        icon: 'a-icon_list_Approvalprogress',
        title: i18n.t('查看审批'),
        visibleCondition: () => {
          return false
        }
      },
      {
        id: 'btn2',
        icon: 'icon_Editor',
        title: i18n.t('售后'),
        permission: ['O_02_0144'],
        visibleCondition: (data) => {
          return (data.receiveStatus == 1 || data.receiveStatus == 2) && data.afterSaleFlag == 1 // 收货状态 = 1-部分收货 || 2-全部收货 && 是否售后状态 = 1-是
        }
      },
      {
        id: 'btn3',
        icon: 'icon_list_close',
        title: i18n.t('关闭'),
        permission: ['O_02_0143'],
        visibleCondition: (data) => {
          return (
            // (data.showStatus === "草稿" ||
            //   data.showStatus === "审批拒绝" ||
            //   data.feedbackStatus === 1 ||
            //   data.feedbackStatus === 2) &&
            data.receiveStatus !== 2 && // 收货状态不等于全部收货
            data.showStatus !== '完成' && // 订单状态
            data.showStatus !== '关闭' && // 订单状态
            data.source !== 3 && // 订单来源不等于外部SAP
            data.businessTypeCode !== 'BTTCL001' &&
            data.businessTypeCode !== 'BTTCL002' &&
            data.businessTypeCode !== 'BTTCL003'
          )
        }
      },
      {
        id: 'btn4',
        icon: 'icon_list_accept',
        title: i18n.t('完成'),
        permission: ['O_02_0147'],
        visibleCondition: (data) => {
          return (
            (data.feedbackStatus === 1 || data.feedbackStatus === 2) &&
            data.receiveStatus !== 2 &&
            data.showStatus !== '完成' &&
            data.source !== 3
          )
        }
      }
    ],
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    }
  },
  {
    field: 'version1',
    headerText: i18n.t('变更情况'),
    width: '95',
    ignore: true,
    searchOptions: {
      customRule: (e) => {
        return {
          label: '版本',
          field: 'version',
          type: 'number',
          operator: 'contains',
          value: e
        }
      }
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e > 1) {
          return i18n.t('变更')
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'urgentStatus',
    headerText: i18n.t('加急状态'),
    width: '96',
    // valueConverter: {
    //   type: 'map',
    //   map: {
    //     0: i18n.t('普通'),
    //     1: i18n.t('加急')
    //   }
    // },
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in',
      dataSource: [
        { label: '普通', value: 0 },
        { label: '加急', value: 1 }
      ],
      fields: { text: 'label', value: 'value' }
    },
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div>
              <span :style="urgentStatus === '加急' ? {color: 'red'} : {}">{{ urgentStatus }}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          },
          computed: {
            urgentStatus() {
              return this.data.urgentStatus === 0 ? i18n.t('普通') : i18n.t('加急')
            }
          }
        })
      }
    }
  },
  {
    field: 'urgentTime',
    headerText: i18n.t('加急日期'),
    width: '98',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'deliveryStatus',
    headerText: i18n.t('发货状态'),
    width: '98',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未发货'),
        1: i18n.t('部分发货'),
        2: i18n.t('全部发货')
      }
    },
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    }
  },
  {
    field: 'receiveStatus',
    headerText: i18n.t('收货状态'),
    width: '98',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未收货'),
        1: i18n.t('部分收货'),
        2: i18n.t('全部收货')
      }
    },
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    }
  },
  // {
  //   field: "orderRel",
  //   headerText: "关联单据号",
  //   width: "150",
  // },
  {
    field: 'source',
    headerText: i18n.t('订单来源'),
    width: '98',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('采购申请转化'),
        1: i18n.t('手动创建'),
        2: i18n.t('商城申请转化'),
        3: i18n.t('外部SAP'),
        4: i18n.t('合同转换')
      }
    }
  },
  {
    field: 'orderTime',
    headerText: i18n.t('订单日期'),
    width: '98',
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    // searchOptions: {
    //   customRule: (e) => {
    //     return {
    //       label: "版本",
    //       field: "version",
    //       type: "number",
    //       operator: "contains",
    //       value: "123+" + e,
    //     };
    //   },
    // },
    // type: "datetime",
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'projectName',
    headerText: i18n.t('项目名称'),
    width: '130'
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司'),
    width: '230',
    searchOptions: MasterDataSelect.businessCompany,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.companyCode}}-{{data.companyName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    width: '170',
    searchOptions: MasterDataSelect.supplier,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.supplierCode}}-{{data.supplierName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型'),
    width: '92'
  },
  {
    field: 'orderTypeName',
    headerText: i18n.t('订单类型'),
    width: '153'
  },
  // {
  //   field: "requiredDeliveryDate",
  //   headerText: i18n.t("要求交期"),
  //   width: "150",
  //   valueConverter: {
  //     type: "function",
  //     filter: (e) => {
  //       if (e && e.length === 13) {
  //         e = Number(e);
  //         return UTILS.dateFormat(e, "Y-m-d");
  //       } else {
  //         return "";
  //       }
  //     },
  //   },
  // },
  {
    field: 'buyerGroupCode',
    headerText: i18n.t('采购组织'),
    searchOptions: MasterDataSelect.businessGroupUnit,
    width: '180',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerGroupCode}}-{{data.buyerGroupName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'buyerUserName',
    headerText: i18n.t('采购员'),
    width: '82'
    // searchOptions: {
    //   ...MasterDataSelect.staff,
    //   fields: { text: 'title', value: 'externalCode' },
    //   renameField: 'buyerUserCode'
    // }
  },
  {
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组'),
    width: '92',
    searchOptions: MasterDataSelect.businessGroupIn,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'currencyCode',
    headerText: i18n.t('币种'),
    searchOptions: MasterDataSelect.money,
    width: '116',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.currencyCode}}-{{data.currencyName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'paymentName',
    headerText: i18n.t('付款条件'),
    width: '250'
  },
  // {
  //   field: "orderRel12",
  //   headerText: "供应商地点",
  //   width: "150",
  // },
  {
    field: 'remark',
    headerText: i18n.t('采方备注'),
    width: '95'
  },
  {
    field: 'supRemark',
    headerText: i18n.t('供方备注'),
    width: '95'
  },
  {
    field: 'version',
    headerText: i18n.t('版本'),
    width: '70',
    // cellTools: [],
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return 'V' + e
      }
    }
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    width: '83'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    },
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    field: 'publishTime',
    headerText: i18n.t('发布时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    },
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    field: 'feedbackTime',
    headerText: i18n.t('反馈时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    },
    searchOptions: { ...MasterDataSelect.timeRange }
  }
]
