<template>
  <!-- 采方-售后订单协同 -->
  <div class="full-height">
    <mt-template-page
      ref="template-0"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
      <mt-template-page slot="slot-1" ref="template-1" :template-config="pageConfig1">
        <div slot="slot-filter" class="top-filter">
          <div class="left-status">
            <span>{{ $t('业务类型：') }}</span>
            <status-check
              ref="statusCheckRef"
              :status-data="businessTypeList"
              @handleChose="handleChose"
            ></status-check>
          </div>
        </div> </mt-template-page
    ></mt-template-page>
    <add-dialog ref="addDialog" :business-type-options="businessTypeList"></add-dialog>
    <ship-dialog
      ref="shipDialog"
      @buyerSaleOrderDeliverySave="buyerSaleOrderDeliverySave"
    ></ship-dialog>
  </div>
</template>

<script>
import * as CONFIG from './config'
export default {
  components: {
    statusCheck: () => import('@/components/businessComponents/statusCheck.vue'),
    AddDialog: () => import('./components/addDialog'),
    ShipDialog: () => import('./components/shipDialog')
  },
  data() {
    return {
      businessTypeList: [],
      pageConfig1: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: [[], ['Filter', 'Refresh', 'Setting']],
          grid: {
            columnData: [
              {
                width: '50',
                type: 'checkbox',
                showInColumnChooser: false
              }
            ],
            dataSource: [],
            frozenColumns: 1
          }
        }
      ],
      componentConfig: [
        {
          title: this.$t('单据视图'),
          toolbar: [
            'Add',
            {
              id: 'Publish',
              icon: 'icon_solid_pushorder',
              title: this.$t('发布')
            }
            // {
            //   id: "Approve",
            //   icon: "icon_solid_Activateorder",
            //   title: "审批",
            // },
          ],
          grid: {
            columnData: CONFIG.columnData,
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/buyerSaleServiceOrder/query'
            }
          }
        },
        {
          title: this.$t('明细视图')
        }
      ],
      currentRow: {}
    }
  },
  mounted() {
    this.getBusinessConfig()
  },
  methods: {
    // 顶部筛选 调整
    async handleChose(e, item) {
      await this.getColumnModule(item.itemCode)
      this.purOrderDetail(e)
    },
    //重新赋值asynConfig
    purOrderDetail(e) {
      this.$set(this.pageConfig1[0].grid, 'asyncConfig', {
        url: '/srm-purchase-execute/tenant/buyerSaleServiceOrderDetail/detail/view',
        defaultRules: [
          {
            condition: 'and',
            field: 'business_type_id',
            label: '',
            operator: 'equal',
            value: e
          }
        ]
      })
    },
    //获取动态表头
    async getColumnModule(code) {
      let params = { businessTypeCode: code, docType: 'as_po' }
      await this.$API.purchaseOrder.getPeBuinessConfig(params).then((res) => {
        let flag = false
        if (res.data && res.data.modules.length) {
          res.data.modules.some((item) => {
            if (item.moduleType === 11) {
              if (item.fields && item.fields.length) {
                this.pageConfig1[0].grid.columnData = this.pageConfig1[0].grid.columnData.concat(
                  item.fields.map((item1) => {
                    let obj = {}
                    let hasItem = false
                    CONFIG.columnData1.some((item2) => {
                      if (item2.field === item1.fieldCode) {
                        obj = {
                          ...item2,
                          headerText: item1.fieldName
                        }
                        hasItem = true
                      }
                    })
                    if (hasItem) {
                      return obj
                    } else {
                      return {
                        ...item1,
                        headerText: item1.fieldName,
                        field: item1.fieldCode
                      }
                    }
                  })
                )
                flag = true
                return true
              }
            }
          })
        }
        if (!flag) {
          this.pageConfig1[0].grid.columnData = CONFIG.columnData1
        }
      })
    },
    // 获取业务类型下拉
    getBusinessConfig() {
      this.$API.masterData.getDictCode({ dictCode: 'businessType' }).then((res) => {
        if (res.data && res.data.length) {
          this.businessTypeList = []
          res.data.map((item) => {
            this.businessTypeList.push({
              label: item.itemName,
              value: item.id,
              itemCode: item.itemCode,
              text: item.itemName
            })
          })
        }
      })
    },
    handleClickToolBar(e) {
      if (e.gridRef.getMtechGridRecords().length === 0 && e.toolbar.id !== 'Add') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let ids = []
      e.gridRef.getMtechGridRecords().map((item) => ids.push(item.id))
      switch (e.toolbar.id) {
        case 'Add':
          this.handleAdd()
          break
        case 'Publish':
          this.handlePublish(ids)
          break
        case 'Approve':
          this.handleApprove(ids)
          break
      }
    },
    handleApprove(ids) {
      this.$dialog({
        data: {
          title: this.$t('审批订单'),
          message: this.$t('确认审批选中的订单？')
        },
        success: () => {
          let params = {
            orderId: ids[0],
            remark: this.$t('审批同意'),
            status: 2
          }
          this.$API.purchaseOrder.buyerSaleOrderApprove(params).then(() => {
            this.$toast({ content: this.$t('审批订单操作成功'), type: 'success' })
            this.$refs[`template-0`].refreshCurrentGridData()
          })
        }
      })
    },
    //发布
    handlePublish(ids) {
      this.$dialog({
        data: {
          title: this.$t('发布订单'),
          message: this.$t('确认发布选中的订单？')
        },
        success: () => {
          let params = {
            orderIds: ids
          }
          this.$API.purchaseOrder.buyerSaleOrderPublish(params).then(() => {
            this.$toast({ content: this.$t('发布操作成功'), type: 'success' })
            this.$refs[`template-0`].refreshCurrentGridData()
          })
        }
      })
    },
    // 新增
    handleAdd() {
      this.$refs.addDialog.show()
    },
    //用来操作行按钮操作
    handleClickCellTool(e) {
      switch (e.tool.id) {
        case 'btn1':
          this.handleBtn1(e.data)
          break
        case 'btn2':
          this.handleBtn2()
          break
        case 'btn3':
          this.handleBtn3(e.data)
          break
        case 'btn4':
          this.handleBtn4(e.data)
          break
        case 'btn5':
          this.handleBtn5(e.data)
          break
      }
    },
    //发货
    handleBtn1(row) {
      this.currentRow = row
      this.$refs.shipDialog.show()
    },
    //发货保存
    buyerSaleOrderDeliverySave(entryInfo) {
      let params = {
        addressCode: entryInfo.startPlace,
        addressName: entryInfo.addressName,
        consignee: entryInfo.deliveryPerson,
        courierNumber: entryInfo.logisticsNumber,
        deliveryTime: new Date(entryInfo.startDate).getTime(),
        driverName: entryInfo.driverName,
        expressCompany: entryInfo.expressCompany,
        expressCompanyId: entryInfo.expressCompanyId,
        id: this.currentRow.id,
        supplierId: this.currentRow.supplierId,
        method: entryInfo.deliveryType,
        orderCode: this.currentRow.orderCode,
        orderId: this.currentRow.id,
        receiveTime: new Date(entryInfo.endDate).getTime(),
        remark: entryInfo.remark,
        tenantId: entryInfo.tenantId
      }
      if (params.method == '2') {
        params.driverPhone = entryInfo.driverPhone
      }
      if (params.method == '3') {
        params.driverPhone = entryInfo.deliveryPhone
      }
      this.$API.purchaseOrder.buyerSaleOrderDeliverySave(params).then(() => {
        this.$toast({ content: this.$t('发货操作成功'), type: 'success' })
        this.$refs.shipDialog.handleClose()
        this.$refs[`template-0`].refreshCurrentGridData()
      })
    },
    //查看审批
    handleBtn2() {},
    // 关闭
    handleBtn3(row) {
      this.$dialog({
        data: {
          title: this.$t('关闭订单'),
          message: this.$t('确认关闭该订单？')
        },
        success: () => {
          let params = {
            orderId: row.id,
            remark: this.$t('关闭订单'),
            status: 4
          }
          this.$API.purchaseOrder.buyerSaleOrderClose(params).then(() => {
            this.$toast({ content: this.$t('关闭操作成功'), type: 'success' })
            this.$refs[`template-0`].refreshCurrentGridData()
          })
        }
      })
    },
    // 完成
    handleBtn4(row) {
      this.$dialog({
        data: {
          title: this.$t('完成订单'),
          message: this.$t('确认完成该订单？')
        },
        success: () => {
          let params = {
            orderId: row.id,
            remark: this.$t('完成订单'),
            status: 5
          }
          this.$API.purchaseOrder.buyerSaleOrderDone(params).then(() => {
            this.$toast({ content: this.$t('完成操作成功'), type: 'success' })
            this.$refs[`template-0`].refreshCurrentGridData()
          })
        }
      })
    },
    //编辑
    handleBtn5(row) {
      console.log(row, '编辑带入列表数据')
      if (row.relateOrderCode === '0') {
        this.$router.push({
          name: 'sales-coordination-detail',
          query: {
            orderid: row.id,
            type: '4' //不关联进入4 关联是3
          }
        })
      }
      if (row.relateOrderCode === '1') {
        this.$router.push({
          name: 'related-after-sales',
          query: {
            orderId: row.id,
            type: '2'
          }
        })
      }
    },
    //用来跳转反馈异常
    handleClickCellTitle(e) {
      console.log('handleClickCellTitle', e)
      if (e.field === 'orderCode') {
        if (e.data.relateOrderCode === '0') {
          //TODO暂时用状态跳转详情要改成订单号
          this.$router.push({
            name: 'sales-coordination-detail',
            query: {
              orderid: e.data.id,
              type: '5'
            }
          })
        }
        if (e.data.relateOrderCode === '1') {
          this.$router.push({
            name: 'related-after-sales',
            query: {
              orderId: e.data.id,
              type: '3'
            }
          })
        }
      }
    }
  }
}
</script>

<style></style>
