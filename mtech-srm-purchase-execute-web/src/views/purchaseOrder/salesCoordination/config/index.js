import UTILS from '../../../../utils/utils'
import Vue from 'vue'
import { i18n } from '@/main.js'
export const columnData1 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    field: 'deliveryStatus',
    headerText: i18n.t('发货状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未发货'),
        1: i18n.t('部分发货'),
        2: i18n.t('全部发货')
      }
    }
  },
  {
    field: 'confirmStatus',
    headerText: i18n.t('供应商确认状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('待确认'), 1: i18n.t('确认'), 2: i18n.t('拒绝') }
    }
  },
  {
    field: 'dueDate',
    headerText: i18n.t('要求交期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return e
        }
      }
    }
  },
  {
    field: 'warehouseStatus',
    headerText: i18n.t('入库状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未入库'),
        1: i18n.t('部分入库'),
        2: i18n.t('全部入库')
      }
    }
  },
  {
    field: 'startDate',
    width: '150',
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = ''
              if (this.data.purchasingCycleStart) {
                str = UTILS.dateFormat(Number(this.data.purchasingCycleStart))
              } else {
                str = this.data.purchasingCycleStart
              }
              return str
            }
          }
        })
      }
    }
  },
  {
    field: 'endDate',
    width: '150',
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = ''
              if (this.data.purchasingCycleEnd) {
                str = UTILS.dateFormat(Number(this.data.purchasingCycleEnd))
              } else {
                str = this.data.purchasingCycleEnd
              }
              return str
            }
          }
        })
      }
    }
  },
  {
    field: 'agreementCode',
    width: '150',
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = this.data.contract
              return str
            }
          }
        })
      }
    }
  },
  {
    field: 'budgetQuantity',
    width: '150',
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = this.data.forecastQty
              return str
            }
          }
        })
      }
    }
  },
  {
    field: 'orderUnitId',
    width: '150',
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = this.data.purUnitId
              return str
            }
          }
        })
      }
    }
  }
  // {
  //   field: "orderCode",
  //   headerText: "订单号码",
  //   width: "150",
  // },
  // {
  //   field: "id",
  //   headerText: "订单id",
  //   width: "150",
  // },
]
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    field: 'orderCode',
    headerText: i18n.t('售后订单号'),
    width: '150',
    cellTools: [
      {
        id: 'btn5',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return (
            (data.showStatus === i18n.t('草稿') ||
              data.feedbackStatus === 1 ||
              data.feedbackStatus === 2) &&
            data.showStatus !== i18n.t('完成') &&
            data.showStatus !== i18n.t('关闭')
          )
        }
      }
    ]
  },
  {
    field: 'showStatus',
    headerText: i18n.t('状态'),
    width: '250',
    valueConverter: {
      type: 'map',
      map: [
        { value: i18n.t('草稿'), text: i18n.t('草稿'), cssClass: 'col-active' },
        {
          value: i18n.t('待审批'),
          text: i18n.t('待审批'),
          cssClass: 'col-active'
        },
        {
          value: i18n.t('审批通过'),
          text: i18n.t('审批通过'),
          cssClass: 'col-abnormal'
        },
        {
          value: i18n.t('审批不通过'),
          text: i18n.t('审批不通过'),
          cssClass: 'col-abnormal'
        },
        {
          value: i18n.t('待发布'),
          text: i18n.t('待发布'),
          cssClass: 'col-published'
        },
        {
          value: i18n.t('发布待确认'),
          text: i18n.t('发布待确认'),
          cssClass: 'col-published'
        },
        { value: i18n.t('完成'), text: i18n.t('完成'), cssClass: 'col-normal' },
        {
          value: i18n.t('关闭'),
          text: i18n.t('关闭'),
          cssClass: 'col-inactive'
        },
        {
          value: i18n.t('供方接受'),
          text: i18n.t('供方接受'),
          cssClass: 'col-normal'
        },
        {
          value: i18n.t('供方拒绝'),
          text: i18n.t('供方拒绝'),
          cssClass: 'col-abnormal'
        }
      ]
    },
    cellTools: [
      {
        id: 'btn1',
        icon: 'icon_Editor',
        title: i18n.t('发货'),
        visibleCondition: (data) => {
          return (
            data.feedbackStatus === 1 &&
            data.showStatus !== i18n.t('完成') &&
            data.showStatus !== i18n.t('关闭') &&
            data.purDeliveryStatus != '1'
          )
        }
      },
      {
        id: 'btn2',
        icon: 'icon_Editor',
        title: i18n.t('查看审批'),
        visibleCondition: () => {
          return false
        }
      },
      {
        id: 'btn3',
        icon: 'icon_Editor',
        title: i18n.t('关闭'),
        visibleCondition: (data) => {
          return (
            (data.showStatus === i18n.t('草稿') ||
              data.showStatus === i18n.t('审批拒绝') ||
              data.feedbackStatus === 1 ||
              data.feedbackStatus === 2) &&
            data.receiveStatus !== 2 &&
            data.showStatus !== i18n.t('完成')
          )
        }
      },
      {
        id: 'btn4',
        icon: 'icon_Editor',
        title: i18n.t('完成'),
        visibleCondition: (data) => {
          return (
            (data.feedbackStatus === 1 || data.feedbackStatus === 2) &&
            data.receiveStatus !== 2 &&
            data.showStatus !== i18n.t('完成')
          )
        }
      }
    ]
  },
  {
    field: 'relateOrderCode',
    headerText: i18n.t('关联状态'),
    width: '100',
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('不关联'), 1: i18n.t('关联') }
    }
  },
  {
    field: 'purDeliveryStatus',
    headerText: i18n.t('采方发货状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('未发货'), 1: i18n.t('已发货') }
    }
  },
  {
    field: 'receiveStatus',
    headerText: i18n.t('收货状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未收货'),
        1: i18n.t('部分收货'),
        2: i18n.t('全部收货')
      }
    }
  },
  {
    field: 'deliveryStatus',
    headerText: i18n.t('供方发货状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未发货'),
        1: i18n.t('部分发货'),
        2: i18n.t('全部发货')
      }
    }
  },
  {
    field: 'createTime',
    headerText: i18n.t('订单日期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        e = Number(e)
        return UTILS.dateFormat(e)
      }
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称'),
    width: '150'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '150'
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型'),
    width: '150'
  },
  {
    field: 'type',
    headerText: i18n.t('订单类型'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('正常订单'),
        1: i18n.t('退货订单'),
        2: i18n.t('免费订单')
      }
    }
  },
  {
    field: 'requiredDeliveryDate',
    headerText: i18n.t('要求交期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        e = Number(e)
        return UTILS.dateFormat(e)
      }
    }
  },
  {
    field: 'buyerOrgName',
    headerText: i18n.t('采购组织名称'),
    width: '150'
  },
  {
    field: 'buyerUserName',
    headerText: i18n.t('采购员'),
    width: '150'
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    width: '150'
  },
  {
    field: 'paymentName',
    headerText: i18n.t('付款条件'),
    width: '150'
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    width: '150'
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    width: '150'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        e = Number(e)
        return UTILS.dateFormat(e)
      }
    }
  },
  {
    field: 'updateTime',
    headerText: i18n.t('采方发货方式'),
    width: '150'
  }
]
