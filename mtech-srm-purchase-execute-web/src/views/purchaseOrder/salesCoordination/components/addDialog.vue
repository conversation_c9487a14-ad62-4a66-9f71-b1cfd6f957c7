<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="$t('新增售后订单')"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <mt-form-item prop="orderCode" :header="$t('订单编号')">
        <mt-input
          v-model="ruleForm.orderCode"
          :show-clear-button="true"
          :placeholder="$t('请输入订单编号')"
          disabled
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="linkPurchase" :label="$t('关联采购订单')">
        <mt-radio
          v-model="ruleForm.linkPurchase"
          :data-source="linkPurchaseOptions"
          @change="linkPurchaseChage"
        ></mt-radio>
      </mt-form-item>
      <mt-form-item
        prop="purchaseOrder"
        :label="$t('关联采购订单')"
        v-if="ruleForm.linkPurchase === '1'"
      >
        <mt-select
          allow-filtering="true"
          :filtering="getPurOrderQuery"
          v-model="ruleForm.purchaseOrder"
          :data-source="purchaseOrderOptions"
          :show-clear-button="true"
          :placeholder="$t('请选择关联采购订单')"
          @change="getPurchaseOrderDetail"
        ></mt-select>
      </mt-form-item>
      <mt-form-item
        prop="afterSaleType"
        :label="$t('售后类型')"
        v-if="ruleForm.linkPurchase === '2' || ruleForm.linkPurchase === '1'"
      >
        <mt-select
          allow-filtering="true"
          v-model="ruleForm.afterSaleType"
          :data-source="afterSaleTypeOptions"
          :show-clear-button="true"
          :placeholder="$t('请选择售后类型')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="businessType" :label="$t('业务类型')">
        <mt-select
          v-model="ruleForm.businessType"
          :data-source="businessTypeOptions"
          :show-clear-button="true"
          :placeholder="$t('请选择业务类型')"
          :disabled="ruleForm.linkPurchase === '1'"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="purtenant" :label="$t('供应商')">
        <mt-select
          allow-filtering="true"
          v-model="ruleForm.purtenant"
          :data-source="purtenantOptions"
          :show-clear-button="true"
          :placeholder="$t('请选择供应商')"
          :disabled="ruleForm.linkPurchase === '1'"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="afterSaleReason" :label="$t('售后原因')">
        <mt-select
          allow-filtering="true"
          v-model="ruleForm.afterSaleReason"
          :data-source="afterSaleReasonOptions"
          :show-clear-button="true"
          :placeholder="$t('请选择售后原因')"
        ></mt-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>
<script>
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('关闭') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('下一步') }
        }
      ],
      ruleForm: {
        orderCode: this.$t('提交后系统自动生成'), //订单编号
        linkPurchase: '1', //是否关联采购订单
        purchaseOrder: null, //采购订单
        afterSaleType: null, //售后类型
        businessType: null, //业务类型
        purtenant: null, //供应商
        afterSaleReason: null //售后原因
      },
      rules: {
        linkPurchase: [
          {
            required: true,
            message: this.$t('请选择是否关联采购订单'),
            trigger: 'blur'
          }
        ],
        purchaseOrder: [{ required: true, message: this.$t('请选择采购订单'), trigger: 'blur' }],
        afterSaleType: [{ required: true, message: this.$t('请选择售后类型'), trigger: 'blur' }],
        businessType: [{ required: true, message: this.$t('请选择业务类型'), trigger: 'blur' }],
        purtenant: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }],
        afterSaleReason: [{ required: true, message: this.$t('请选择售后原因'), trigger: 'blur' }]
      },
      linkPurchaseOptions: [
        //关联采购订单
        {
          label: this.$t('是'),
          value: '1'
        },
        {
          label: this.$t('否'),
          value: '2'
        }
      ],
      purchaseOrderOptions: [], //采购订单
      afterSaleTypeOptions: [], //售后类型
      businessTypeOptions: [], //业务类型
      purtenantOptions: [], //供应商
      afterSaleReasonOptions: [] // 售后原因
    }
  },
  mounted() {
    this.getPurOrderQuery = utils.debounce(this.getPurOrderQuery, 1000)
    this.getPurOrderQuery()
  },
  methods: {
    getPurchaseOrderDetail(e) {
      if (!e.value) {
        this.ruleForm.businessType = null
        this.ruleForm.purtenant = null
        return
      }
      this.$API.purchaseOrder.getTenantPurOrderCode(e.value).then((res) => {
        this.ruleForm.businessType = res.data.businessTypeCode
        this.ruleForm.purtenant = res.data.supplierCode
        this.ruleForm.paymentName = res.data.paymentCode
        this.ruleForm.buyerUserName = res.data.buyerUserCode
        this.ruleForm.companyName = res.data.companyCode
        this.ruleForm.companyName1 = res.data.companyName
        this.ruleForm.buyerOrgName = res.data.buyerOrgCode
        this.ruleForm.buyerOrgName1 = res.data.buyerOrgName
        this.ruleForm.requiredDeliveryDate = res.data.requiredDeliveryDate
        this.ruleForm.currencyName = res.data.currencyCode
        this.ruleForm.taxTotal = res.data.taxTotal
        this.ruleForm.freeTotal = res.data.freeTotal
        this.ruleForm.settlementName = res.data.settlementId
        this.ruleForm.orderId = res.data.id
      })
    },
    getPurOrderQuery(e = { text: '' }) {
      this.$API.purchaseOrder.getPurOrderQuery({ orderId: e.text }).then((res) => {
        this.purchaseOrderOptions = res.data
      })
    },
    linkPurchaseChage() {
      this.ruleForm.purtenant = null
      this.ruleForm.businessType = null
      this.ruleForm.afterSaleType = null
      this.ruleForm.afterSaleReason = null
      this.$refs.ruleForm.clearValidate()
    },
    getOptions() {
      this.afterSaleTypeOptions = [
        { text: this.$t('退货订单'), value: 0 },
        { text: this.$t('换货订单'), value: 1 },
        { text: this.$t('维修订单'), value: 2 }
      ]
      this.$API.masterData //售后类型
        .getDictItemTree({
          dictCode: 'SHLX'
        })
        .then((res) => {
          if (res.data && res.data.length) {
            // this.afterSaleTypeOptions = res.data.map((item) => {
            //   // return {
            //   //   text: item.name,
            //   //   value: item.itemCode,
            //   // };
            // });
          }
        })
      this.$API.masterData //业务类型
        .getDictCode({ dictCode: 'businessType' })
        .then((res) => {
          if (res.data && res.data.length) {
            this.businessTypeOptions = res.data.map((item) => {
              return {
                text: item.itemName,
                value: item.itemCode,
                id: item.id
              }
            })
          }
        })
      //供应商
      this.$API.masterData.getSupplier().then((res) => {
        if (res && res.data.length) {
          this.purtenantOptions = res.data.map((item) => {
            return {
              text: item.supplierName,
              value: item.supplierCode
            }
          })
        }
      })
      this.afterSaleReasonOptions = [
        {
          text: this.$t('质量问题'),
          value: 0
        },
        {
          text: this.$t('包装问题'),
          value: 1
        }
      ]
      this.$API.masterData //售后原因
        .getDictCode({ dictCode: 'afterSaleReason' })
        .then((res) => {
          if (res.data && res.data.length) {
            // this.afterSaleReasonOptions = res.data.map((item) => {
            //   return {
            //     text: item.itemName,
            //     value: item.itemCode,
            //   };
            // });
          }
        })
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.setSendInfo()
          if (this.ruleForm.linkPurchase === '1') {
            this.$router.push({
              name: 'related-after-sales',
              query: {
                type: '1'
              }
            })
          }
          if (this.ruleForm.linkPurchase === '2') {
            this.$router.push({
              name: 'sales-coordination-detail',
              query: {
                type: '2'
              }
            })
          }
        }
      })
    },
    setSendInfo() {
      let salesCoordinationTopInfo = {
        linkPurchase: this.ruleForm.linkPurchase,
        afterSaleType: this.ruleForm.afterSaleType,
        afterSaleTypeName: this.afterSaleTypeOptions.find((item) => {
          return item.value === this.ruleForm.afterSaleType
        }).text,
        businessTypeCode: this.ruleForm.businessType,
        businessId: this.businessTypeOptions.find((item) => {
          return item.value === this.ruleForm.businessType
        }).id,
        businessTypeName: this.businessTypeOptions.find((item) => {
          return item.value === this.ruleForm.businessType
        }).text,
        purtenant: this.ruleForm.purtenant,
        purtenantName: this.purtenantOptions.find((item) => {
          return item.value === this.ruleForm.purtenant
        }).text,
        afterSaleReason: this.ruleForm.afterSaleReason,
        afterSaleReasonName: this.afterSaleReasonOptions.find((item) => {
          return item.value === this.ruleForm.afterSaleReason
        }).text
      }
      if (salesCoordinationTopInfo.linkPurchase === '1') {
        salesCoordinationTopInfo.orderCode = this.ruleForm.purchaseOrder
        // Object.assign(salesCoordinationTopInfo, this.ruleForm);
        salesCoordinationTopInfo.orderId = this.ruleForm.orderId
      }
      localStorage.setItem('salesCoordinationTopInfo', JSON.stringify(salesCoordinationTopInfo))
    },
    show(entryInfo) {
      this.$refs.dialog.ejsRef.show()
      if (entryInfo) {
        this.ruleForm = {
          orderCode: this.$t('提交后系统自动生成'), //订单编号
          linkPurchase: '1', //是否关联采购订单
          purchaseOrder: entryInfo.orderCode, //采购订单
          afterSaleType: null, //售后类型
          businessType: entryInfo.businessTypeCode, //业务类型
          purtenant: entryInfo.supplierCode, //供应商
          afterSaleReason: null, //售后原因
          orderId: entryInfo.id
        }
      } else {
        this.ruleForm = {
          orderCode: this.$t('提交后系统自动生成'), //订单编号
          linkPurchase: '1', //是否关联采购订单
          purchaseOrder: null, //采购订单
          afterSaleType: null, //售后类型
          businessType: null, //业务类型
          purtenant: null, //供应商
          afterSaleReason: null //售后原因
        }
      }
      this.getOptions()
    }
  }
}
</script>
