<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="$t('发货')"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <mt-form-item prop="startDate" :label="$t('发货日期')">
        <mt-date-picker
          v-model="ruleForm.startDate"
          :min="new Date()"
          :max="ruleForm.endDate ? new Date(ruleForm.endDate) : new Date(2099, 11, 31)"
        ></mt-date-picker>
      </mt-form-item>
      <mt-form-item prop="endDate" :label="$t('预计到货日期')">
        <mt-date-picker
          v-model="ruleForm.endDate"
          :min="
            ruleForm.startDate
              ? new Date(ruleForm.startDate).getTime() > new Date().getTime()
                ? new Date(ruleForm.startDate)
                : new Date()
              : new Date()
          "
          :max="new Date(2099, 11, 31)"
        ></mt-date-picker>
      </mt-form-item>
      <mt-form-item prop="startPlace" :label="$t('发货地址')">
        <mt-select
          allow-filtering="true"
          v-model="ruleForm.startPlace"
          :data-source="startPlaceOptions"
          :show-clear-button="true"
          :placeholder="$t('请选择发货地址')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="deliveryType" :label="$t('发货方式')">
        <mt-radio
          v-model="ruleForm.deliveryType"
          :data-source="deliveryTypeOptions"
          @change="deliveryTypeChage"
        ></mt-radio
      ></mt-form-item>
      <mt-form-item
        prop="logisticsCompany"
        :label="$t('物流公司')"
        v-if="ruleForm.deliveryType === '1'"
      >
        <mt-select
          allow-filtering="true"
          v-model="ruleForm.logisticsCompany"
          :data-source="logisticsCompanyOptions"
          :show-clear-button="true"
          :placeholder="$t('请选择物流公司')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item
        prop="logisticsNumber"
        :label="$t('物流单号')"
        v-if="ruleForm.deliveryType === '1'"
      >
        <mt-input v-model="ruleForm.logisticsNumber"></mt-input>
      </mt-form-item>
      <mt-form-item prop="driverName" :label="$t('司机姓名')" v-if="ruleForm.deliveryType === '2'">
        <mt-input v-model="ruleForm.driverName"></mt-input>
      </mt-form-item>
      <mt-form-item
        prop="driverPhone"
        :label="$t('司机联系方式')"
        v-if="ruleForm.deliveryType === '2'"
      >
        <mt-input v-model="ruleForm.driverPhone" maxlength="11"></mt-input>
      </mt-form-item>
      <mt-form-item
        prop="deliveryPerson"
        :label="$t('提货人姓名')"
        v-if="ruleForm.deliveryType === '3'"
      >
        <mt-input v-model="ruleForm.deliveryPerson"></mt-input>
      </mt-form-item>
      <mt-form-item
        prop="deliveryPhone"
        :label="$t('联系方式')"
        v-if="ruleForm.deliveryType === '3'"
      >
        <mt-input v-model="ruleForm.deliveryPhone" maxlength="11"></mt-input>
      </mt-form-item>
      <mt-form-item
        prop="remark"
        :label="$t('备注')"
        class="full-width"
        v-if="
          ruleForm.deliveryType === '1' ||
          ruleForm.deliveryType === '2' ||
          ruleForm.deliveryType === '3'
        "
      >
        <mt-input v-model="ruleForm.remark"></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>
<script>
import { RegExpMap } from '@/utils/constant'

export default {
  data() {
    const { phoneNumReg } = RegExpMap
    var validPhone = (rule, value, callback) => {
      console.log(rule)
      let str =
        rule.field === 'driverPhone'
          ? this.$t('请输入司机联系方式')
          : this.$t('请输入提货人联系方式')
      if (value === '') {
        console.log(1)
        callback(new Error(str))
      } else if (!phoneNumReg.test(value)) {
        console.log(2)
        callback(new Error(this.$t('请输入正确的手机号')))
      } else {
        callback()
      }
    }
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('关闭') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('下一步') }
        }
      ],
      ruleForm: {
        startDate: '',
        endDate: '',
        startPlace: '',
        deliveryType: '1',
        logisticsCompany: '',
        logisticsNumber: '',
        driverName: '',
        driverPhone: '',
        deliveryPerson: '',
        deliveryPhone: '',
        remark: ''
      },
      rules: {
        startDate: [
          {
            required: true,
            message: this.$t('请选择发货日期'),
            trigger: 'blur'
          }
        ],
        endDate: [
          {
            required: true,
            message: this.$t('请选择预计到货日期'),
            trigger: 'blur'
          }
        ],
        startPlace: [
          {
            required: true,
            message: this.$t('请选择发货地址'),
            trigger: 'blur'
          }
        ],
        deliveryType: [
          {
            required: true,
            message: this.$t('请选择发货方式'),
            trigger: 'blur'
          }
        ],
        logisticsCompany: [
          {
            required: true,
            message: this.$t('请选择物流公司'),
            trigger: 'blur'
          }
        ],
        logisticsNumber: [
          {
            required: true,
            message: this.$t('请选择物流单号'),
            trigger: 'blur'
          }
        ],
        driverName: [
          {
            required: true,
            message: this.$t('请输入司机姓名'),
            trigger: 'blur'
          }
        ],
        driverPhone: [
          {
            validator: validPhone,
            required: true,
            trigger: 'blur'
          }
        ],
        deliveryPerson: [
          {
            required: true,
            message: this.$t('请输入提货人姓名'),
            trigger: 'blur'
          }
        ],
        deliveryPhone: [
          {
            validator: validPhone,
            required: true,
            trigger: 'blur'
          }
        ]
      },
      startPlaceOptions: [], //发货地址
      deliveryTypeOptions: [
        //发货方式
        {
          label: this.$t('快递配送'),
          value: '1'
        },
        {
          label: this.$t('物流配送'),
          value: '2'
        },
        {
          label: this.$t('采方自提'),
          value: '3'
        }
      ],
      logisticsCompanyOptions: [] //物流公司
    }
  },
  methods: {
    getOptions() {
      this.$API.masterData //物流公司
        .getDictItemTree({
          dictCode: 'logisticsCompany'
        })
        .then((res) => {
          if (res.data && res.data.length) {
            this.logisticsCompanyOptions = res.data.map((item) => {
              return {
                text: item.name,
                value: item.itemCode,
                id: item.id
              }
            })
          }
        })
      this.$API.masterData //发货地址
        .getDictItemTree({
          dictCode: 'deliveryAddress'
        })
        .then((res) => {
          if (res.data && res.data.length) {
            this.startPlaceOptions = res.data.map((item) => {
              return {
                text: item.name,
                value: item.itemCode
              }
            })
          }
        })
    },
    deliveryTypeChage() {
      this.$refs.ruleForm.clearValidate()
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.resetFields()
      this.$refs.ruleForm.clearValidate()
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('buyerSaleOrderDeliverySave', {
            ...this.ruleForm,
            expressCompanyId: this.logisticsCompanyOptions.find((item) => {
              return item.value === this.ruleForm.logisticsCompany
            })?.id,
            expressCompany: this.logisticsCompanyOptions.find((item) => {
              return item.value === this.ruleForm.logisticsCompany
            })?.text,
            addressName: this.startPlaceOptions.find((item) => {
              return item.value === this.ruleForm.startPlace
            })?.text
          })
        }
      })
    },
    show() {
      this.$refs.dialog.ejsRef.show()
      this.ruleForm = {
        startDate: '',
        endDate: '',
        startPlace: null,
        deliveryType: '1',
        logisticsCompany: null,
        logisticsNumber: '',
        driverName: '',
        driverPhone: '',
        deliveryPerson: '',
        deliveryPhone: '',
        remark: ''
      }
      this.getOptions()
    }
  }
}
</script>
