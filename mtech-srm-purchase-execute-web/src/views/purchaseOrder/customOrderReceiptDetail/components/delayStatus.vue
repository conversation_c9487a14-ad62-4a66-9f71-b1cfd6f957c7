<template>
  <div>
    <mt-input id="delayStatus" style="display: none" :value="data.delayStatus"></mt-input>
    <!-- <mt-input v-model="data.delayStatus" disabled></mt-input> -->
    <span>{{ data.delayStatus }}</span>
  </div>
</template>
<script>
export default {
  data() {
    return {
      data: {
        delayStatus: ''
      }
    }
  },
  mounted() {
    if (this.data.preAcceptanceTime) {
      if (new Date().getTime() > new Date(this.data.preAcceptanceTime).getTime()) {
        this.data.delayStatus = this.$t('已延期')
      } else {
        this.data.delayStatus = this.$t('未延期')
      }
    }
  }
}
</script>
