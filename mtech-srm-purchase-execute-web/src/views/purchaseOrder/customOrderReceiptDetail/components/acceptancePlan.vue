<template>
  <div class="top-info pt20 grid-wrap">
    <mt-data-grid
      id="Grid3"
      :data-source="dataSource"
      :column-data="acceptancePlanColumn"
      ref="dataGrid"
      :allow-paging="false"
      :enable-sticky-header="true"
    ></mt-data-grid>
  </div>
</template>
<script>
import { acceptancePlanColumn } from '../config/acceptancePlan'
export default {
  props: {
    acceptancePlanDataSource: {
      //表头数据
      type: Array,
      default: () => []
    },
    topInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dataSource: [], //数据源
      acceptancePlanColumn: acceptancePlanColumn()
    }
  },
  watch: {
    acceptancePlanDataSource: {
      handler() {
        this.acceptancePlanColumn = acceptancePlanColumn(this.topInfo.businessTypeCode)
        this.dataSource = this.acceptancePlanDataSource.map((i) => {
          const obj = { ...i }
          if (i.preAcceptanceTime) {
            obj.preAcceptanceTime = Number(i.preAcceptanceTime)
          }
          return obj
        })
      },
      immediate: true
    }
  }
}
</script>
<style lang="scss" scoped>
.top-info {
  flex: 1;
  margin: 0;
  .switch-wrap {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background: #fff;
    padding: 20px 20px 10px 0;

    span {
      margin-right: 14px;
    }
  }
  .my-table {
    max-width: 100vw;
    overflow-x: auto;
  }
}
</style>
<style lang="scss" scoped>
.full-height {
  background: #fff;
}
</style>

<style lang="scss">
.pc-selection-dialog {
  .mt-data-grid {
    height: 100%;
    > .e-grid {
      height: 100%;
      // display: flex;

      > .e-gridcontent {
        flex: 1;
        overflow: auto;
      }
    }

    .e-rowcell.e-active {
      background: #e0e0e0 !important;
    }
  }
}
</style>
<style lang="scss" scoped>
.full-height {
  background: #fff;
}
.pt20 {
  padding-top: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  /deep/ #Grid2_toolbarItems {
    border-top: none;
  }
  // .mt-data-grid {
  //   flex: 1;
  //   /deep/ .e-grid {
  //     height: 100%;
  //     display: flex;
  //     flex-direction: column;

  //     .e-headerchkcelldiv {
  //       padding-left: 0;
  //     }

  //     .e-gridcontent {
  //       flex: 1;
  //       .e-content {
  //         // height: 100% !important;
  //         flex-direction: row !important;
  //       }
  //     }
  //   }
  // }
}
.toolbar-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;

  .one-bar {
    display: flex;
    align-items: center;
    margin-right: 20px;
    cursor: pointer;
    .mt-icons {
      font-size: 14px;
      color: #4f5b6d;
      margin-right: 5px;
    }
    span {
      word-break: keep-all;
      font-size: 14px;
      color: #4f5b6d;
      font-weight: normal;
    }
  }

  .flex1 {
    flex: 1;
  }
}
</style>
