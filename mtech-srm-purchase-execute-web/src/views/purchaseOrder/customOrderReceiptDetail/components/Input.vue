<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="isDisabled"
      @input="remarkChange"
      v-if="maxlength"
      :maxlength="maxlength"
    ></mt-input>
  </div>
</template>
<script>
export default {
  data() {
    return { data: {}, isDisabled: false, addId: '', maxlength: 0 }
  },
  mounted() {
    this.maxlength = 200
    this.addId = this.data.addId
    if (
      this.data.confirmStatus == 2 ||
      this.data.receiveStatus == 2 ||
      this.data.closeStatus == 1
    ) {
      this.isDisabled = true
    } else {
      this.isDisabled = false
    }
    this.$bus.$on('remarkEditChange', (val) => {
      if (val.addId === this.addId) {
        if (
          !(
            this.data.confirmStatus == 2 ||
            this.data.receiveStatus == 2 ||
            this.data.closeStatus == 1
          )
        ) {
          this.data[this.data.column.field] = val.supRemark
          this.remarkChange(val.supRemark)
        }
      }
    })
  },
  methods: {
    remarkChange(val) {
      console.log(val)
      this.$parent.$emit('remarkChange', { remark: val, addId: this.addId })
    }
  }
}
</script>
