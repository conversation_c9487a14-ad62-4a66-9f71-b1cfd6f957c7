import Vue from 'vue'
// import { utils } from "@mtech-common/utils";
import { i18n } from '@/main.js'

// 页面内容是否可编辑
export let editFlag = {
  isEditable: false
}

// 采购申请数据
export let purchaseDataSource = []

// 并单策略
export let orderConfigData = []

// 采购申请转订单/手工创建采购订单并单配置
export const purchaseColumnData = [
  {
    //   width: "50",
    field: 'serialNumber',
    headerText: i18n.t('序号')
  },
  {
    //   width: "100",
    field: 'businessType',
    headerText: i18n.t('业务类型')
  },
  {
    //   width: "100",
    field: 'rule1',
    headerText: i18n.t('规则1-公司'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-checkbox v-model="this.checkStatus" cssClass="checkbox-checkeditem"  :disabled="true"></mt-checkbox>`,
          data() {
            return { data: {} }
          },
          computed: {
            checkStatus() {
              // purchaseDataSource[this.data.index].rule1.ruleValue = 1;
              // orderConfigData[0].orderConsolidationConfigDTOList[
              //   this.data.index * 7
              // ].ruleValue = 1;
              return this.data.rule1.ruleValue ? true : false
            }
          }
        })
      }
    }
  },
  {
    //   width: "150",
    field: 'rule2',
    headerText: i18n.t('规则2-供应商'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule2.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.rule2 = e.checked ? 1 : 0;
              // purchaseDataSource[this.data.index].rule2.ruleValue = e.checked
              //   ? 1
              //   : 0;
              orderConfigData[0].orderConsolidationConfigDTOList[
                this.data.index * 7 + 1
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    //   width: "150",
    field: 'rule3',
    headerText: i18n.t('规则3-采购组织'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable"  :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule3.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.rule3 = e.checked;
              // purchaseDataSource[this.data.index].rule3.ruleValue = e.checked
              //   ? 1
              //   : 0;

              orderConfigData[0].orderConsolidationConfigDTOList[
                this.data.index * 7 + 2
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    //   width: "150",
    field: 'rule4',
    headerText: i18n.t('规则4-来源'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable"  :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule4.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              this.data.rule4 = e.checked
              // purchaseDataSource[this.data.index].rule4.ruleValue = e.checked
              //   ? 1
              //   : 0;
              orderConfigData[0].orderConsolidationConfigDTOList[
                this.data.index * 7 + 3
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    //   width: "150",
    field: 'rule5',
    headerText: i18n.t('规则5-申请编号'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable"  :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule5.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              this.data.rule5 = e.checked
              // purchaseDataSource[this.data.index].rule5.ruleValue = e.checked
              //   ? 1
              //   : 0;

              orderConfigData[0].orderConsolidationConfigDTOList[
                this.data.index * 7 + 4
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    //   width: "150",
    field: 'rule6',
    headerText: i18n.t('规则6-收货地址'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable"  :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule6.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              this.data.rule6 = e.checked
              // purchaseDataSource[this.data.index].rule6.ruleValue = e.checked
              //   ? 1
              //   : 0;
              orderConfigData[0].orderConsolidationConfigDTOList[
                this.data.index * 7 + 5
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    width: '250',
    field: 'rule7',
    headerText: i18n.t('规则7-需求时间范围'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable"  :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" :label="$t('间隔天数：')"></mt-checkbox>
      <mt-inputNumber ref="inputNumber" :min="0" :max="999" width="120" height="26"
      :disabled="!isEditable"  :class="[!isEditable && 'checkbox-checkeditem']"
      @change="handleInputNum" v-model="data.rule7.timeLimit"></mt-inputNumber>
    </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule7.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.rule7 = e.checked;
              // purchaseDataSource[this.data.index].rule7.ruleValue = e.checked
              //   ? 1
              //   : 0;
              orderConfigData[0].orderConsolidationConfigDTOList[
                this.data.index * 7 + 6
              ].ruleValue = e.checked ? 1 : 0
            },
            handleInputNum(e) {
              // purchaseDataSource[this.data.index].rule7.timeLimit = e;
              orderConfigData[0].orderConsolidationConfigDTOList[
                this.data.index * 7 + 6
              ].timeLimit = e
            }
          }
        })
      }
    }
  }
]
