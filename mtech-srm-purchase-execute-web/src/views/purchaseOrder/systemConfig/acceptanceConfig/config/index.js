import { i18n } from '@/main.js'

// 验收项配置 用于页面展示的数据
export let acceptanceDataSource = []

// 验收项配置 用于提交的数据
export let acceptanceData = []

// 验收项配置
export const acceptanceConfigColumn = [
  {
    width: '230',
    field: 'serialNumber',
    headerText: i18n.t('序号'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        permission: ['O_02_0600']
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        permission: ['O_02_0604']
      }
    ],
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return Number(+row.index + 1)
      }
    }
  },
  {
    width: '280',
    field: 'acceptanceCode',
    headerText: i18n.t('验收项编号')
  },
  {
    // width: "200",
    field: 'acceptanceTypeName',
    headerText: i18n.t('验收类型名称')
  }
  // {
  //   width: "200",
  //   field: "prepaidFlag",
  //   headerText: "是否预付",
  //   template: function () {
  //     return {
  //       template: Vue.component("actionOption", {
  //         template: `<div style="flex-direction: row; display: inline-flex;">
  //         <mt-checkbox v-model="data.strategyConfigMap.prepaidFlag  == 1 ? true : false" @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
  //         </div>`,
  //         data() {
  //           return { data: {} };
  //         },
  //         methods: {
  //           handleChangeCellCheckBox(e) {
  //             this.data.strategyConfigMap.prepaidFlag = e.checked ? 1 : 0;
  //             acceptanceData[this.data.index].strategyConfigMap.prepaidFlag =
  //               e.checked ? 1 : 0;
  //             acceptanceDataSource[this.data.index].prepaidFlag = e.checked
  //               ? 1
  //               : 0;
  //           },
  //         },
  //       }),
  //     };
  //   },
  // },
]
