// 验收项配置
<template>
  <div class="full-height">
    <!-- 右侧各种操作按钮 -->
    <!-- <div class="operateButton">
      <mt-button
        css-class="e-flat"
        v-show="editFlag"
        @click="changeEditFlag"
        :is-primary="true"
        v-permission="['O_02_0601']"
        >{{ $t("取消") }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        v-show="!editFlag"
        @click="changeEditFlag"
        :is-primary="true"
        v-permission="['O_02_0600']"
        >{{ $t("编辑") }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        v-show="editFlag"
        @click="saveConfig"
        :is-primary="true"
        v-permission="['O_02_0602']"
        >{{ $t("保存") }}</mt-button
      >
    </div> -->
    <mt-template-page
      ref="template5"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      class="ml20"
    >
    </mt-template-page>
    <!-- 验收项配置弹窗 -->
    <add-acceptance-config-dialog
      v-if="isAcceptanceDialogShow"
      @handleAcceptanceDialogShow="handleAcceptanceDialogShow"
      @confirmSuccess="confirmSuccess"
      :dialog-data="dialogData"
    ></add-acceptance-config-dialog>
  </div>
</template>

<script>
import * as config from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'
export default {
  components: {
    addAcceptanceConfigDialog: require('./components/addAcceptanceConfigDialog.vue').default
  },
  data() {
    return {
      editFlag: false, //是否可编辑
      dialogData: null,
      acceptanceDataOrigin: [], // 源数据（用于Tab切换前对比）
      isAcceptanceDialogShow: false, // 是否显示弹窗
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          toolbar: [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              title: this.$t('新增'),
              permission: ['O_02_0603']
            },
            {
              id: 'Delete',
              icon: 'icon_solid_Delete',
              title: this.$t('删除'),
              permission: ['O_02_0604']
            }
          ],
          gridId: this.$tableUUID.purchaseOrder.acceptanceConfigTab,
          grid: {
            allowPaging: false,
            lineSelection: true,
            // frozenColumns: 1,
            columnData: config.acceptanceConfigColumn,
            asyncConfig: {
              url: `${BASE_TENANT}/orderAcceptanceStrategy/query`,
              methods: 'get',
              recordsPosition: 'data'
            }
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    // 打开编辑弹窗
    handleEdit(data) {
      console.log(data)
      this.dialogData = {
        dialogType: 'edit',
        row: data
      }
      this.isAcceptanceDialogShow = true
    },

    // 打开新增弹窗
    handleAdd() {
      this.isAcceptanceDialogShow = true
      this.dialogData = {
        dialogType: 'add'
      }
    },

    // 新增/编辑成功
    confirmSuccess(params) {
      console.log('获取到的数据', params)

      this.$API.bgConfig.editOrderAcceptance(params).then((res) => {
        if (res.code == 200) {
          this.$refs['template5'].refreshCurrentGridData()
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
        }
      })
    },

    // 删除行
    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t('确定删除选中行?')
        },
        success: () => {
          this.$API.bgConfig.deleteOrderAcceptance({ idList: ids }).then((res) => {
            if (res.code == 200) {
              this.$refs['template5'].refreshCurrentGridData()
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
            }
          })
        }
      })
    },

    handleClickToolBar(e) {
      if (e.grid.getSelectedRecords().length <= 0 && e.toolbar.id == 'Delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      let ids = []
      e.grid.getSelectedRecords().map((item) => {
        ids.push(item.id)
      })
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(ids)
      }
    },

    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      } else if (e.tool.id == 'edit') {
        this.handleEdit(e.data)
      }
    },

    handleAcceptanceDialogShow(flag) {
      this.isAcceptanceDialogShow = flag
    }
  }
}
</script>

<style lang="scss" scoped>
.operateButton {
  height: 50px;
  line-height: 50px;
  text-align: right;
}
.top-filter {
  background: #fff;
  padding: 20px 20px 20px 20px;
  font-weight: 500;

  .accordion-main {
    margin-right: 20px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 25px;
  }

  .left-status {
    margin: 0px 20px 20px 0px;
    clear: both;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  &-small {
    height: 50px;
    overflow: hidden;
  }
}
.second-top-filter {
  background: #fff;
  padding-bottom: 5px;
  font-weight: 500;
  padding-left: 20px;
  .second-left-status {
    height: 20px;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  &-small {
    height: 50px;
    overflow: hidden;
  }
}
.default-setting {
  width: 240px;
  height: 70px;
  float: left;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: normal;
  margin-right: 20px;
  color: rgba(41, 41, 41, 1);
  &-small {
    height: 60px;
  }
  &-title {
    height: 14px;
    display: block;
    margin-bottom: 20px;
    &-large {
      display: block;
      margin-bottom: 20px;
      width: 320px;
    }
  }
  &-suppliertab {
    width: 500px;
    default-setting-title {
      display: block;
      margin-bottom: 20px;
    }
  }
  &:nth-child(6) {
    width: 163px;
  }
}
.accordion-main {
  margin-right: 20px;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 25px;
}

.left-status {
  margin: 0px 20px 20px 0px;
  clear: both;
}
.accordion-title {
  float: left;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  text-indent: 10px;
  border-left: 5px solid #00469c;
  margin-bottom: 20px;
  border-radius: 2px 0 0 2px;
}

.sort-box {
  position: relative;
  cursor: pointer;
  width: 5px;
  float: right;
  margin: 15px 30px 0px 0px;

  .mt-icons {
    font-size: 12px;
    transform: scale(0.5);
    color: rgba(0, 70, 156, 1);
    margin-top: -15px;
    position: absolute;
    top: 0;
    &:nth-child(2) {
      top: 6px;
    }
  }
}
.mt-radio /deep/ .e-radio-wrapper {
  margin-right: 40px;
}
</style>
