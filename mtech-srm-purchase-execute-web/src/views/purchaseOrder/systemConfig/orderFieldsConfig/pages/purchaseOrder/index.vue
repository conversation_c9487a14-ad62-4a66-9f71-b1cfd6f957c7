<template>
  <div class="purchase-order">
    <common-config
      doc-type="po"
      base-doc-type="pr"
      :relative-file-module-type="7"
      :base-module-type="0"
      :column-name="$t('采购订单')"
      :base-column-name="$t('采购申请')"
      :remove-memory-grids="removeMemoryGrids"
    ></common-config>
  </div>
</template>

<script>
export default {
  props: {
    businessTypeId: {
      type: String,
      default: ''
    }
  },
  components: {
    commonConfig: require('@/components/businessComponents/fieldsConfig/commonConfig/index.vue')
      .default
  },
  computed: {
    removeMemoryGrids() {
      let userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      return [
        this.$md5(this.$tableUUID.sourceApply.detailTab + this.businessTypeId),
        this.$md5(this.$tableUUID.demandPool.toOrder + this.businessTypeId),
        this.$md5(this.$tableUUID.demandPool.myOrder + this.businessTypeId),
        this.$md5(this.$tableUUID.demandPool.toSource + this.businessTypeId),
        this.$md5(this.$tableUUID.demandPool.mySource + this.businessTypeId),
        this.$md5(this.$tableUUID.purchaseOrder.detailTab + this.businessTypeId),
        this.$md5(
          this.$tableUUID.purchaseOrder.customReceiptDetailTab +
            this.businessTypeId +
            userInfo.tenantId
        )
      ]
    }
  }
}
</script>

<style></style>
