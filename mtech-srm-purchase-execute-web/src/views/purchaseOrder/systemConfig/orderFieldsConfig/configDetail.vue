<template>
  <div class="fields-config-page mt-flex-direction-column">
    <div class="top-info mt-flex-direction-column">
      <div class="detail-info">
        <div class="name-wrap">
          <div class="first-line">
            <span class="code">{{ configInfo.businessTypeCode }}</span>
            <span
              :class="['tags', `tags-${index + 1}`]"
              v-for="(item, index) in tagList"
              :key="index"
              >{{ item }}</span
            >
          </div>
          <div class="second-line">
            <div class="cai-name">{{ configInfo.businessTypeId }}</div>
          </div>
        </div>
        <div class="btns-wrap">
          <!-- <mt-button>保存</mt-button> -->
          <mt-button @click.native="backToBusinessConfig">{{ $t('返回') }}</mt-button>
        </div>
      </div>

      <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
    </div>
    <div class="config-container">
      <!-- 0. 采购订单配置 -->
      <purchase-order-config
        v-if="tabIndex == 0"
        :business-type-id="configInfo.businessTypeId"
      ></purchase-order-config>

      <!-- 1. 供应商订单接收配置 -->
      <supply-order-config
        v-if="tabIndex == 1"
        :business-type-id="configInfo.businessTypeId"
      ></supply-order-config>
    </div>
  </div>
</template>
<script>
import { i18n } from '@/main.js'
const mainTabList = [
  {
    title: i18n.t('采购订单配置')
  },
  {
    title: i18n.t('供应商接收订单配置')
  }
]
export default {
  components: {
    purchaseOrderConfig: require('./pages/purchaseOrder/index.vue').default,
    supplyOrderConfig: require('./pages/supplyOrder/index.vue').default
  },
  data() {
    return {
      tabIndex: 0,
      configId: '',
      businessTypeId: null,
      businessTypeCode: null,
      tagList: [this.$t('一般采购')],
      configInfo: {
        businessTypeId: '',
        businessTypeCode: '',
        businessTypeName: null,
        configId: null,
        version: null
      },
      tabSource: mainTabList
    }
  },
  mounted() {
    let sourceModuleConfigInfo = JSON.parse(localStorage.sourceModuleConfigInfo)
    this.configInfo.businessTypeId = sourceModuleConfigInfo.businessTypeId
    this.configInfo.businessTypeCode = sourceModuleConfigInfo.businessTypeCode
    this.configId = sourceModuleConfigInfo.configId
    this.tagList = [sourceModuleConfigInfo.businessTypeName]
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    },
    backToBusinessConfig() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/pages/_fieldsConfig.scss';
</style>
