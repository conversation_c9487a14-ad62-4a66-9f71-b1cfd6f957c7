import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'businessTypeCode',
    headerText: i18n.t('业务类型编码'),
    cssClass: '',
    cellTools: [
      // "delete",
      {
        permission: ['O_02_0579'],
        id: 'config',
        icon: 'icon_list_deploy',
        title: i18n.t('配置')
      }
    ]
  },
  {
    // width: "150",
    field: 'businessTypeName',
    headerText: i18n.t('业务类型名称')
  },
  {
    // width: "150",
    field: 'enableStatus',
    headerText: i18n.t('启用状态'),
    valueConverter: {
      type: 'map',
      // map: { 0: "草稿", 1: "启用", 2: "停用" }
      map: [
        { value: 0, text: i18n.t('草稿'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('启用'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('停用'), cssClass: 'col-inactive' }
      ]
    }
  },
  {
    // width: "150",
    field: 'updateUserName',
    headerText: i18n.t('修改人')
  },
  {
    field: 'updateTime',
    headerText: i18n.t('修改时间'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    // width: "150",
    field: 'remark',
    headerText: i18n.t('描述'),
    valueConverter: { type: 'placeholder', placeholder: i18n.t('无数据') }
  }
]
