<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config/index.js'
export default {
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              // ["Add", "Delete"],
              [],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: this.$tableUUID.purchaseOrder.orderFieldsConfigTab,
          grid: {
            columnData: columnData,
            lineIndex: 1,
            autoWidthColumns: columnData.length + 1,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/pe/business/configs',
              recordsPosition: 'data'
            }
            // frozenColumns: 1,
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    handleClickToolBar(e) {
      console.log(e.grid.getSelectedRecords(), e)
      if (
        e.grid.getSelectedRecords().length <= 0 &&
        !(
          e.toolbar.id == 'Add' ||
          e.toolbar.id == 'Filter' ||
          e.toolbar.id == 'Refresh' ||
          e.toolbar.id == 'Setting' ||
          e.toolbar.id == 'refreshDataByLocal' ||
          e.toolbar.id == 'filterDataByLocal'
        )
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = []
      e.grid.getSelectedRecords().map((item) => _id.push(item.id))
      if (e.toolbar.id == 'Refresh') {
        this.getTableData()
      }
    },

    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },

    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    },

    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      } else if (e.tool.id == 'edit') {
        let _row = e.data
        this.handleEdit(_row)
      } else if (e.tool.id == 'config') {
        let _query = e.data
        if (e.data.id) {
          _query.configId = e.data.id
        }
        localStorage.sourceModuleConfigInfo = JSON.stringify(_query)
        this.redirectPage('purchase-execute/order-fields-detail', { timeStamp: new Date() })
      }
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
