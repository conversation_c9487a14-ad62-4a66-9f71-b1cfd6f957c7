<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="sourceCurrencyId" :label="$t('业务类型编码')">
        <mt-input
          v-model="addForm.rateDescription"
          :show-clear-button="true"
          :maxlength="50"
          :placeholder="$t('请输入业务类型编码')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="targetCurrencyId" :label="$t('业务类型名称')">
        <mt-input
          v-model="addForm.rateDescription"
          :show-clear-button="true"
          :maxlength="50"
          :placeholder="$t('请输入业务类型名称')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="rate" :label="$t('启用状态')">
        <mt-switch v-model="addForm.defaultOn"></mt-switch>
      </mt-form-item>

      <mt-form-item prop="rateDescription" :label="$t('备注')" class="full-width">
        <mt-input
          v-model="addForm.rateDescription"
          :show-clear-button="true"
          :multiline="true"
          :maxlength="200"
          :placeholder="$t('请输入备注')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
// import common from "@/utils/constant";
// import { formatDate, formatRules } from "@/utils/util";
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        targetCurrencyId: '',
        sourceCurrencyId: '',
        rate: null,
        effectiveTime: null,
        rateDescription: ''
      },
      rules: {
        // unitName: [
        //   { required: true, message: "请输入中文名称", trigger: "blur" }
        // ]
      },
      currencyAll: [],
      currencyTarget: [],
      currencySource: []
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      let _addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
      if (_addForm.startTime && _addForm.endTime) {
        _addForm.effectiveTime = [_addForm.startTime, _addForm.endTime]
      }
      this.addForm = _addForm
    }
    // this.getAllCurrency();
    if (this.dialogData?.dialogType == 'add') {
      this.dialogTitle = this.$t('业务类型新增')
    } else {
      this.dialogTitle = this.$t('业务类型编辑')
    }
    // this.getRules();
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // console.log(this.addForm);
          // let _request = this.dialogData?.requestUrl;
          // console.log(this.dialogData, _request);
          // this.$API.baseMainData[_request](params).then((res) => {
          //   if (res.code == 200) {
          //     this.$toast({ content: "操作成功", type: "success" });
          //     this.$emit("handleAddDialogShow", false);
          //     this.$emit("confirmSuccess");
          //   }
          // });
        }
      })
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },

    getRules() {
      if (this.dialogData?.dialogType == 'add') {
        // this.$API.baseMainData.getAddRulesExchangeRate().then((res) => {
        //   if (res.code == 200) this.rules = formatRules(res.data);
        //   this.rules.effectiveTime = [
        //     { required: true, message: "请选择有效时间", trigger: "blur" },
        //   ];
        // });
      } else {
        // this.$API.baseMainData.getUpdateRulesExchangeRate().then((res) => {
        //   if (res.code == 200) this.rules = formatRules(res.data);
        //   this.rules.effectiveTime = [
        //     { required: true, message: "请选择有效时间", trigger: "blur" },
        //   ];
        // });
      }
    }
  }
}
</script>

<style></style>
