<template>
  <div class="left-nav-check">
    <div
      class="ext-tabs-container mt-flex"
      v-for="(item, index) in leftNavList"
      :key="'left-nav-' + index"
      v-show="currentTabObject.moduleKey == item.moduleKey"
    >
      <div class="left-nav-tabs">
        <ul class="nav-container mt-flex-direction-column">
          <li
            :class="['nav-item', { active: _index == activeNav }]"
            v-for="(nav, _index) in item.navList"
            :key="'nav-item-' + index + '-' + _index"
            @click="activeNav = _index"
          >
            <div class="svg-option-item">
              <mt-icon
                @click.native="handleClickNavIcon(nav)"
                class="config-checkbox"
                :name="nav.checked ? 'a-icon_MultipleChoice_on' : 'a-icon_MultipleChoice_off'"
              />
              <span @click="handleClickNavTitle(nav)">{{ nav.text }}</span>
            </div>

            <!-- <mt-icon class="config-arrow" name="a-icon_Packup" /> -->
          </li>
        </ul>
      </div>
      <div class="ext-content-container"></div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    leftNavList: {
      type: Array,
      default: () => []
    },
    currentTabObject: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      activeNav: 0 //左侧第三层Tab，Active序号
    }
  },

  methods: {
    //左侧Nav点击，checkbox切换
    handleClickNavIcon(e) {
      console.log('handleClickNavIcon', e)
      //当前，左侧Tab未设置关于fixed的要求
      // let _fixed = e.fixed;
      // if (_fixed > 0) {
      //   this.$toast({ content: `'${e.text}'为固定项.`, type: "warning" });
      //   return;
      // } else {
      //   e.checked = !e.checked;
      // }
      e.checked = !e.checked
    },
    //左侧Nav Title点击，Tab切换
    handleClickNavTitle(e) {
      console.log('handleClickNavTitle', e)
    }
  }
}
</script>

<style></style>
