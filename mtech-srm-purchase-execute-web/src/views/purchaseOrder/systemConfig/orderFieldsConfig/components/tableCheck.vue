<template>
  <div class="table-check">
    <div v-for="(item, index) in tableList" :key="'table-' + index">
      <mt-data-grid
        :allow-selection="false"
        v-show="item.moduleKey == currentTabObject.moduleKey"
        :column-data="item.grid.columnData"
        :data-source="item.grid.dataSource"
        @queryCellInfo="queryCellInfoEvent"
        @handleChangeCellCheckBox="handleChangeCellCheckBox(index, $event)"
      ></mt-data-grid>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    tableList: {
      type: Array,
      default: () => []
    },
    currentTabObject: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    //合并单元格，主要处理fieldGroup列
    queryCellInfoEvent(args) {
      if (args.column.field == 'fieldGroup') {
        let _data = args.data
        if (_data?.isFirstGroupField) {
          args.rowSpan = _data.groupCount
        }
      }
    },

    // 勾选
    handleChangeCellCheckBox(index, $event) {
      this.$emit('handleChangeCellCheckBox', index, $event)
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .action-boxs {
  text-align: left;
  padding-left: 10px;
}
// 头部预留 actions-box 的高度
.table-check {
  padding-top: 50px;
}

// 表格第一列的边框
/deep/ td:first-child[rowspan] + td,
/deep/ tr:first-child td:first-child + td,
/deep/ td:first-child {
  border-left: 1px solid #e0e0e0 !important;
}

// 表格第一列的 padding
/deep/ td:first-child[rowspan].e-rowcell:first-of-type,
/deep/ tr:first-child td:first-child.e-rowcell:first-of-type {
  padding: 0 10px 0 20px !important;
}

// 表格第二列的 padding
/deep/ .e-grid td.e-rowcell:first-of-type {
  padding: 0 10px !important;
}

// 堆叠表头的文字
/deep/ .e-grid .e-gridheader .e-stackedheadercelldiv {
  color: #292929;
  font-weight: 500;
  font-size: 14px;
}

// 表头的标题 padding
/deep/ .e-grid.e-default .e-gridheader th.e-firstcell {
  padding: 0 10px 0 20px !important;
}
</style>
