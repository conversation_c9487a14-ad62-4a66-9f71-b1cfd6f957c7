<template>
  <div class="file-config">
    <div
      class="ext-files-container mt-flex"
      v-for="(item, index) in fileConfigList"
      :key="'file-config-' + index"
      v-show="configId && currentTabObject.moduleKey == item.moduleKey"
    >
      <div class="tree-view--wrap">
        <!-- <div class="trew-node--add">
          <div class="node-title">层级</div>
          <mt-button
            icon-css="mt-icons mt-icon-icon_solid_Createorder"
            css-class="e-flat"
            icon-position="Right"
            @click.native="addNewRootNode"
            >增加目录</mt-button
          >
        </div> -->
        <mt-common-tree
          :checked-nodes="fileChekedNodes"
          v-if="treeViewData.dataSource.length"
          ref="treeView"
          class="tree-view--template"
          :allow-editing="true"
          :fields="treeViewData"
          :show-check-box="true"
          :un-button="true"
        ></mt-common-tree>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    configId: {
      type: String,
      default: ''
    },
    currentTabObject: {
      type: Object,
      default: () => {}
    },
    fileConfigList: {
      type: Array,
      default: () => []
    },
    treeViewData: {
      type: Object,
      default: () => {}
    },
    fileChekedNodes: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style></style>
