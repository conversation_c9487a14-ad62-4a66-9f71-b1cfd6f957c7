<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <!-- 任务类型 定时发布/定时反馈 -->
      <!-- <mt-form-item prop="operateType" :label="$t('定时发布/反馈')">
        <mt-select
          v-model="ruleForm.operateType"
          :data-source="operateTypeOptions"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item> -->
      <mt-form-item prop="configType1" :label="$t('配置方式')">
        <!-- <mt-multi-select
          v-model="ruleForm.configType"
          :data-source="configTypeOptions"
          placeholder="请选择"
        ></mt-multi-select> -->
        <mt-select
          :allow-filtering="true"
          v-model="ruleForm.configType1"
          :data-source="configTypeOptions1"
          :placeholder="$t('请选择')"
          @change="configTypeChange"
        ></mt-select>
      </mt-form-item>
      <mt-form-item
        v-if="ruleForm.configType.includes('1')"
        prop="businessTypeCode"
        :label="$t('业务类型')"
      >
        <mt-select
          :allow-filtering="true"
          v-model="ruleForm.businessTypeCode"
          :data-source="businessTypeOptions"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item
        v-if="ruleForm.configType.includes('2')"
        prop="orderType"
        :label="$t('订单类型')"
      >
        <mt-select
          :allow-filtering="true"
          v-model="ruleForm.orderType"
          :data-source="orderTypeOptions"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="companyId" :label="$t('公司')" v-if="ruleForm.configType.includes('4')">
        <mt-select
          :allow-filtering="true"
          v-model="ruleForm.companyId"
          :data-source="companyOptions"
          :fields="{ text: 'label', value: 'id' }"
          :placeholder="$t('请选择')"
          :filtering="getCompany"
        ></mt-select>
      </mt-form-item>
      <mt-form-item
        v-if="ruleForm.configType.includes('8')"
        prop="supplierCode"
        :label="$t('供应商')"
      >
        <mt-select
          v-model="ruleForm.supplierCode"
          :data-source="supplierOptions"
          :fields="{ text: 'label', value: 'supplierCode' }"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
          :filtering="serchText"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="dateRange" :label="$t('日期范围')">
        <mt-date-range-picker
          v-model="ruleForm.dateRange"
          :allow-edit="false"
          :placeholder="$t('请选择')"
        ></mt-date-range-picker>
      </mt-form-item>
      <mt-form-item prop="overTime" :label="$t('超时时间（H）')">
        <mt-inputNumber
          :show-spin-button="false"
          v-model="ruleForm.overTime"
          :min="0"
          :step="1"
          :precision="2"
          :show-clear-button="false"
          :placeholder="$t('请输入')"
        ></mt-inputNumber>
      </mt-form-item>
      <mt-form-item
        prop="buyerOrgCode"
        :label="$t('采购组')"
        v-if="ruleForm.configType.includes('16')"
      >
        <mt-select
          v-model="ruleForm.buyerOrgCode"
          :data-source="buyerOrgOptions"
          :fields="{ text: 'label', value: 'groupCode' }"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
          @change="buyerOrgChange"
        ></mt-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import bigDecimal from 'js-big-decimal'
import { utils } from '@mtech-common/utils'
export default {
  data() {
    // 日期范围
    const dateRangeValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请选择日期范围')))
      } else {
        this.$refs.ruleForm.clearValidate(['dateRange'])
        callback()
      }
    }
    return {
      businessTypeOptions: [], // 业务类型 下列选项
      orderTypeOptions: [], // 订单类型 下列选项
      buyerOrgOptions: [],
      companyOptions: [], // 公司 下列选项
      supplierOptions: [], // 供应商 下列选项
      operateTypeOptions: [
        // { text: "定时发布", value: "0" },
        { text: this.$t('定时反馈'), value: '1' }
      ], // 定时发布/反馈 下拉数据
      configTypeOptions: [
        { text: this.$t('业务类型'), value: '1' },
        { text: this.$t('订单类型'), value: '2' },
        { text: this.$t('公司'), value: '4' },
        { text: this.$t('供应商'), value: '8' }
      ], // 配置方式 下拉数据
      configTypeOptions1: [
        { text: this.$t('业务类型+订单类型'), value: '3' },
        { text: this.$t('业务类型+订单类型+公司+供应商'), value: '15' },
        { text: this.$t('业务类型+订单类型+采购组'), value: '19' },
        { text: this.$t('业务类型+订单类型+公司+供应商+采购组'), value: '31' }
      ],
      dialogTitle: '',
      rules: {
        // 任务类型
        operateType: [
          {
            required: true,
            message: this.$t('请选择任务类型'),
            trigger: 'blur'
          }
        ],
        // 配置方式
        configType1: [
          {
            required: true,
            message: this.$t('请选择配置方式'),
            trigger: 'blur'
          }
        ],
        // 采购组
        buyerOrgCode: [
          {
            required: true,
            message: this.$t('请选择采购组'),
            trigger: 'blur'
          }
        ],
        // 业务类型
        businessTypeCode: [
          {
            required: true,
            message: this.$t('请选择业务类型'),
            trigger: 'blur'
          }
        ],
        // 订单类型
        orderType: [
          {
            required: true,
            message: this.$t('请选择订单类型'),
            trigger: 'blur'
          }
        ],
        // 公司
        companyId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        // 供应商
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }],
        // 日期范围
        dateRange: [{ required: true, validator: dateRangeValidator, trigger: 'blur' }],
        // 超时时间
        overTime: [
          {
            required: true,
            message: this.$t('请选择超时时间'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {
        id: '',
        buyerOrgId: '',
        buyerOrgName: '',
        buyerOrgCode: '',
        operateType: '1', // 任务类型 定时发布/反馈
        configType: [], // 配置方式
        configType1: '',
        businessTypeCode: '', // 业务类型
        orderType: '', // 订单类型编码
        companyId: '', // 公司编码
        supplierCode: '', // 供应商
        dateRange: null, // 日期范围
        overTime: '' // 前端定义 超时时间 timeValue.hour
      },
      editInfo: {}
    }
  },
  async mounted() {
    await this.getOptions()
    this.getSupplier()
    this.getSupplier = utils.debounce(this.getSupplier, 1000)
    this.getCompany = utils.debounce(this.getCompany, 1000)
    this.$emit('setAsyncConfig', this.orderTypeOptions)
  },
  methods: {
    configTypeChange(val) {
      if (val.value === '3') {
        this.ruleForm.configType = ['1', '2']
        this.ruleForm.buyerOrgCode = ''

        this.ruleForm.buyerOrgName = ''
        this.ruleForm.buyerOrgId = ''
      }
      if (val.value === '15') {
        this.ruleForm.configType = ['1', '2', '4', '8']
        this.ruleForm.buyerOrgCode = ''

        this.ruleForm.buyerOrgName = ''
        this.ruleForm.buyerOrgId = ''
      }
      if (val.value === '19') {
        this.ruleForm.configType = ['1', '2', '16']
      }
      if (val.value === '31') {
        this.ruleForm.configType = ['1', '2', '4', '8', '16']
      }
    },
    buyerOrgChange(e) {
      this.ruleForm.buyerOrgCode = e.itemData.groupCode

      this.ruleForm.buyerOrgName = e.itemData.groupName
      this.ruleForm.buyerOrgId = e.itemData.id
    },
    async getOptions() {
      const params = {
        fuzzyParam: '',
        groupTypeCode: 'BG001CG'
      }
      //业务类型下拉
      this.$API.masterData.getDictCode({ dictCode: 'businessType' }).then((res) => {
        this.businessTypeOptions = res.data || []
      })
      //订单类型下拉
      await this.$API.masterData.getDictCode({ dictCode: 'OrderType' }).then((res) => {
        this.orderTypeOptions = res.data || []
      })
      await this.$API.masterData.getbussinessGroup(params).then((res) => {
        res.data.forEach((item) => {
          item.label = item.groupCode + '-' + item.groupName
        })
        this.buyerOrgOptions = res.data || []
      })
    },
    getCompany(val, entryFirst) {
      //公司下拉
      this.$API.masterData
        .OrgFindSpecifiedChildrenLevelOrgs({
          fuzzyParam: val?.text || '',
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true,
          organizationIds: []
        })
        .then((res) => {
          let list = res.data || []
          list.forEach((item) => {
            item.label = `${item.orgCode}-${item.orgName}`
          })
          this.companyOptions = res.data
          if (entryFirst === '1') {
            this.ruleForm.companyId = this.editInfo.companyId
          }
          if (val?.updateData) {
            this.$nextTick(() => {
              val.updateData(this.companyOptions)
            })
          }
        })
    },
    serchText(val) {
      console.log('搜索值', val)
      this.getSupplier(val && val.text ? val.text : '')
    },
    getSupplier(val, entryFirst) {
      //查询供应商的数据
      let str = val || this.supplierCode
      let params = {
        fuzzyNameOrCode: str || ''
      }
      this.$API.masterData.getSupplier(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.supplierCode}-${item.supplierName}`
        })
        this.supplierOptions = res.data || []
        if (entryFirst === '1') {
          this.ruleForm.supplierCode = this.editInfo.supplierCode
        }
      })
    },
    // 初始化
    dialogInit(entryInfo) {
      this.dialogTitle = entryInfo.title
      if (this.dialogTitle === this.$t('新增')) {
        this.ruleForm = {
          configType1: '',
          operateType: '1', // 任务类型 定时发布/反馈
          configType: [], // 配置方式
          businessTypeCode: '', // 业务类型
          buyerOrgCode: '',
          buyerOrgId: '',
          buyerOrgName: '',
          orderType: '', // 订单类型编码
          companyId: '', // 公司编码
          supplierCode: null, // 供应商
          supplierName: null,
          dateRange: null, // 日期范围
          overTime: '' // 前端定义 超时时间 timeValue.hour
        }
        this.getSupplier()
        this.getCompany()
      }
      if (this.dialogTitle === this.$t('编辑')) {
        this.editInfo = entryInfo.row
        this.ruleForm = {
          operateType: entryInfo.row.type.toString(), // 任务类型 定时发布/反馈
          configType: entryInfo.row.configType, // 配置方式
          configType1: entryInfo.row.configType1,
          buyerOrgCode: entryInfo.row.buyerOrgCode,
          buyerOrgId: entryInfo.row.buyerOrgId,
          buyerOrgName: entryInfo.row.buyerOrgName,
          businessTypeCode: entryInfo.row.businessTypeCode, // 业务类型
          orderType: entryInfo.row.orderTypeCode, // 订单类型编码
          companyId: null, // 公司编码
          supplierCode: null, // 供应商
          supplierName: null,
          dateRange: [
            new Date(Number(entryInfo.row.startDate)),
            new Date(Number(entryInfo.row.endDate))
          ], // 日期范围
          overTime: entryInfo.row.overTime, // 前端定义 超时时间 timeValue.hour
          status: entryInfo.row.status,
          id: entryInfo.row.id
        }
        this.getCompany({ text: entryInfo.row.companyCode }, '1')
        this.getSupplier(entryInfo.row.supplierCode, '1')
      }
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      console.log(this.ruleForm)
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            businessTypeCode: '',
            businessTypeId: 0,
            businessTypeName: '',
            orderTypeCode: '',
            orderTypeName: 0,
            companyId: 0,
            companyCode: '',
            companyName: '',
            supplierCode: '',
            supplierId: 0,
            supplierName: '',
            configType: this.calculateSum(this.ruleForm.configType),
            configTypeName: this.calculateSumName(this.ruleForm.configType),
            endDate: this.ruleForm.dateRange[1].getTime(),
            id: this.ruleForm.id,
            overTime: Number(this.ruleForm.overTime),
            startDate: this.ruleForm.dateRange[0].getTime(),
            status: this.dialogTitle === this.$t('编辑') ? this.ruleForm.status : 1,
            type: Number(this.ruleForm.operateType)
          }
          if (this.ruleForm.configType.includes('1')) {
            Object.assign(params, {
              businessTypeCode: this.ruleForm.businessTypeCode,
              businessTypeId: this.businessTypeOptions.find((item) => {
                return item.itemCode == this.ruleForm.businessTypeCode
              }).id,
              businessTypeName: this.businessTypeOptions.find((item) => {
                return item.itemCode == this.ruleForm.businessTypeCode
              }).itemName
            })
          }
          if (this.ruleForm.configType.includes('2')) {
            Object.assign(params, {
              orderTypeCode: this.ruleForm.orderType,
              orderTypeName: this.orderTypeOptions.find((item) => {
                return item.itemCode == this.ruleForm.orderType
              }).itemName
            })
          }
          if (this.ruleForm.configType.includes('4')) {
            Object.assign(params, {
              companyId: this.ruleForm.companyId,
              companyCode: this.companyOptions.find((item) => {
                return item.id == this.ruleForm.companyId
              }).orgCode,
              companyName: this.companyOptions.find((item) => {
                return item.id == this.ruleForm.companyId
              }).orgName
            })
          }
          if (this.ruleForm.configType.includes('8')) {
            Object.assign(params, {
              supplierCode: this.ruleForm.supplierCode,
              supplierId: this.supplierOptions.find((item) => {
                return item.supplierCode == this.ruleForm.supplierCode
              }).supplierTenantId,
              supplierName: this.supplierOptions.find((item) => {
                return item.supplierCode == this.ruleForm.supplierCode
              }).supplierName
            })
          }
          if (this.ruleForm.configType.includes('16')) {
            Object.assign(params, {
              buyerOrgCode: this.ruleForm.buyerOrgCode,
              buyerOrgName: this.ruleForm.buyerOrgName,
              buyerOrgId: this.ruleForm.buyerOrgId
            })
          }
          this.$API.purchaseOrder.poTimeConfigSave(params).then(() => {
            this.$refs.dialog.ejsRef.hide()
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$emit('updateList')
          })
        }
      })
    },
    calculateSumName(arr) {
      let names = []
      arr.forEach((item) => {
        if (item.includes('1')) {
          names.push(this.$t('业务类型'))
        }
        if (item.includes('2')) {
          names.push(this.$t('订单类型'))
        }
        if (item.includes('4')) {
          names.push(this.$t('公司'))
        }
        if (item.includes('8')) {
          names.push(this.$t('供应商'))
        }
        if (item.includes('16')) {
          names.push(this.$t('采购组'))
        }
      })
      let name = names.join(',')
      return name
    },
    calculateSum(arr) {
      let sum = 0
      arr.forEach((item) => {
        sum = bigDecimal.add(sum, item)
      })
      return Number(sum)
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
<style lang="scss" scoped></style>
