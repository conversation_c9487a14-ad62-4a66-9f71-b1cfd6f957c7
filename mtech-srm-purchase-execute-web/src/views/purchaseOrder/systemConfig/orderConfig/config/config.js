import UTILS from '@/utils/utils'
import { i18n } from '@/main.js'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant'

export const checkColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  }
]
export const lastColumn = [
  {
    field: 'selfIndex',
    headerText: i18n.t('序号'),
    width: '150',
    ignore: true,
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        permission: ['O_02_0573'],
        visibleCondition: (data) => {
          return data
        }
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        permission: ['O_02_0574'],
        visibleCondition: (data) => {
          return data
        }
      }
    ]
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('启用'), cssClass: 'col-active' },
        { value: 0, text: i18n.t('停用'), cssClass: 'col-inactive' }
      ]
    },
    cellTools: [
      {
        id: 'disable',
        icon: '',
        title: i18n.t('停用'),
        permission: ['O_02_0576'],
        visibleCondition: (data) => data.status === 1
      },
      {
        id: 'enable',
        icon: '',
        permission: ['O_02_0575'],
        title: i18n.t('启用'),
        visibleCondition: (data) => data.status === 0
      }
    ]
  },
  // {
  //   field: "type",
  //   headerText: i18n.t("定时发布/反馈"),
  //   width: "150",
  //   valueConverter: {
  //     type: "map",
  //     map: { 0: i18n.t("定时发布"), 1: i18n.t("定时反馈") },
  //   },
  // },
  {
    field: 'configTypeName',
    headerText: i18n.t('配置方式'),
    width: '250'
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型'),
    width: '150'
  },
  {
    field: 'orderTypeName',
    headerText: i18n.t('订单类型'),
    width: '150'
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司'),
    width: '250',
    searchOptions: MasterDataSelect.businessCompany,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.companyCode}}-{{data.companyName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    width: '300',
    searchOptions: MasterDataSelect.supplier,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.supplierCode}}-{{data.supplierName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'buyerOrgName',
    headerText: i18n.t('采购组'),
    width: '150'
  },
  {
    field: 'startDate',
    headerText: i18n.t('开始日期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e != '0') {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'endDate',
    headerText: i18n.t('结束日期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e != '0') {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'overTime',
    headerText: i18n.t('超时（h）'),
    width: '150'
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    width: '150'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    width: '150'
  }
]
