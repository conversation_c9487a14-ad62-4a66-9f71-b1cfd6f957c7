// 供应商预占库存
<template>
  <div class="full-height">
    <!-- 右侧各种操作按钮 -->
    <div class="operateButton">
      <mt-button
        css-class="e-flat"
        v-show="editFlag"
        @click="changeEditFlag"
        :is-primary="true"
        v-permission="['O_02_0590']"
        >{{ $t('取消') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        v-show="!editFlag"
        @click="changeEditFlag"
        :is-primary="true"
        v-permission="['O_02_0588']"
        >{{ $t('编辑') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        v-show="editFlag"
        @click="saveConfig"
        :is-primary="true"
        v-permission="['O_02_0591']"
        >{{ $t('保存') }}</mt-button
      >
    </div>
    <mt-template-page
      slot="slot-3"
      ref="template-3"
      :template-config="pageConfig3"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      class="ml20"
    >
      <div
        slot="slot-filter"
        :class="['top-filter', !isSupplierTopFilterExpand && 'top-filter-small']"
        class="top-filter"
      >
        <div>
          <div class="accordion-title">{{ $t('供应商预占库存配置') }}</div>

          <div class="sort-box" @click="isSupplierTopFilterExpand = !isSupplierTopFilterExpand">
            <mt-icon
              v-for="index in 2"
              :key="index"
              :name="isSupplierTopFilterExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
            ></mt-icon>
          </div>
        </div>
        <div class="accordion-main">
          <div class="left-status">
            <span class="titles">{{ $t('默认配置：') }}</span>
          </div>
          <div class="default-setting-suppliertab">
            <span class="default-setting-title">{{ $t('订单生成') }}</span>
            <mt-radio
              v-model="supplierRadioVal"
              :data-source="supplierRadioData"
              @input="onchangOrderStock"
            ></mt-radio>
          </div>
        </div>
      </div>

      <div slot="slot-filter" class="second-top-filter">
        <div class="second-left-status">
          <span>{{ $t('特殊配置：') }}</span>
        </div>
      </div>
    </mt-template-page>
    <!-- 预占库存弹窗 -->
    <add-order-stock-occupy-dialog
      v-if="addOrderStockDialogShow"
      :dialog-data="dialogData"
      @handleOrderStockAddDialogShow="handleOrderStockAddDialogShow"
    ></add-order-stock-occupy-dialog>
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-name="downTemplateName"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { cloneDeep } from 'lodash'
import * as config from '@/views/bgConfig/policyConfig/config/index.js'
export default {
  components: {
    addOrderStockOccupyDialog:
      require('@/views/bgConfig/policyConfig/components/addOrderStockOccupyDialog.vue').default,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  data() {
    return {
      dialogData: null,
      downTemplateName: null, // 下载模板文件名
      downTemplateParams: {}, // 下载模板参数
      requestUrls: {}, // 上传下载接口地址
      editFlag: false, //是否可编辑
      supplierRadioVal: null,
      supplierRadioData: config.supplierTabRadioData,
      isSupplierTopFilterExpand: true,
      orderStockOccupyOrigin: [], // 源数据（用于Tab切换前对比）
      addOrderStockDialogShow: false, // 是否显示弹窗
      pageConfig3: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [],
              [
                {
                  id: 'download',
                  icon: 'icon_solid_export',
                  title: this.$t('导出'),
                  visibleCondition: () => false,
                  permission: ['O_02_0594']
                },
                {
                  id: 'upload',
                  icon: 'icon_solid_Import',
                  title: this.$t('导入'),
                  visibleCondition: () => false,
                  permission: ['O_02_0593']
                }
              ]
            ],
            permission: {}
          },
          gridId: this.$tableUUID.purchaseOrder.supplyOccupyConfigTab,
          grid: {
            allowPaging: false,
            // frozenColumns: 1,
            lineSelection: true,
            autoWidthColumns: config.supplierTabColumn.length + 1,
            columnData: config.supplierTabColumn,
            dataSource: config.orderStockOccupyDataSource
          }
        }
      ]
    }
  },
  mounted() {
    this.getConfig()
  },
  methods: {
    changeEditFlag() {
      this.editFlag = this.editFlag ? false : true
      var flag = this.editFlag
      config.editFlag.isEditable = this.editFlag
      if (!flag) {
        this.formatOrderStockOccupyConfig(cloneDeep(this.orderStockOccupyOrigin))
      }
      this.editFlag = flag
      config.supplierTabRadioData.forEach((element) => {
        element.disabled = !flag
      })
      this.$set(this.pageConfig3[0].grid, 'dataSource', [])
      this.$set(
        this.pageConfig3[0].toolbar,
        'tools',
        flag ? [config.editToolbar, config.importExport] : [[], config.importExport]
      )
      this.$set(this.pageConfig3[0].grid, 'dataSource', config.orderStockOccupyDataSource)
    },
    onchangOrderStock(e) {
      config.orderStockOccupyDefaultConfig[0].stockOccupyFlag = Number(e)
    },
    handleClickToolBar(e) {
      if (e.grid.getSelectedRecords().length <= 0 && e.toolbar.id == 'Delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      let _serialNumber = []
      e.grid.getSelectedRecords().map((item) => {
        _serialNumber.push(item.serialNumber)
      })
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(_serialNumber)
      } else if (e.toolbar.id == 'upload') {
        console.log('this.handleUpload();')
        this.handleUpload()
      } else if (e.toolbar.id == 'download') {
        this.handleDownload()
      }
    },
    handleAdd() {
      this.addOrderStockDialogShow = true
      this.dialogData = {
        dialogType: 'add'
      }
    },

    handleDelete(serialNumber) {
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t('确定删除选中行?')
        },
        success: () => {
          serialNumber.forEach((e) => {
            config.orderStockOccupyDataSource.forEach((item) => {
              if (item.serialNumber == e) {
                config.orderStockOccupySpecConfig.splice(
                  config.orderStockOccupyDataSource.indexOf(item),
                  1
                )
                config.orderStockOccupyDataSource.splice(
                  config.orderStockOccupyDataSource.indexOf(item),
                  1
                )
              }
            })
          })
          config.orderStockOccupyDataSource.forEach((e, index) => {
            e.serialNumber = index + 1
          })
        }
      })
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.serialNumber])
      }
    },
    // 获取业务类型下拉
    async getBusinessConfig() {
      await this.$API.bgConfig.getLocalBusinessTypeList({}).then((res) => {
        config.businessTypeList.length = 0
        res.data.forEach((e) => {
          config.businessTypeList.push(e)
        })
      })
    },
    // 获取供应商下拉
    async getSupplier() {
      await this.$API.masterData.getSupplier().then((res) => {
        config.supplierTypeList.length = 0
        res.data.forEach((e) => {
          config.supplierTypeList.push({
            ...e,
            text: `${e.supplierCode}-${e.supplierName}`
          })
        })
      })
    },

    // 查询订单预占库存配置
    async getConfig() {
      await this.getBusinessConfig()
      await this.getSupplier()
      await this.$API.bgConfig.getOrderStockOccupyConfig().then((res) => {
        this.$store.commit('endLoading')
        this.orderStockOccupyOrigin = cloneDeep(res.data)
        this.formatOrderStockOccupyConfig(cloneDeep(res.data))
      })
    },

    // 格式化订单预占库存配置数据
    formatOrderStockOccupyConfig(data) {
      config.orderStockOccupyDataSource.length = 0
      config.orderStockOccupySpecConfig.length = 0
      let serialNumber = 0
      for (let i = 0; i < data.length; i++) {
        if (data[i].commonFlag == 1) {
          config.orderStockOccupyDefaultConfig.length = 0
          config.orderStockOccupyDefaultConfig.push(cloneDeep(data[i]))
          this.supplierRadioVal = config.orderStockOccupyDefaultConfig[0].stockOccupyFlag.toString()
        } else if (data[i].commonFlag == 0) {
          config.orderStockOccupySpecConfig.push(data[i])
          let tempMap = cloneDeep(data[i])
          tempMap.serialNumber = ++serialNumber
          config.orderStockOccupyDataSource.push(cloneDeep(tempMap))
        }
      }
    },

    handleOrderStockAddDialogShow(flag) {
      this.addOrderStockDialogShow = flag
    },

    // 上传（显示弹窗）
    handleUpload() {
      this.downTemplateParams = {
        flag: 0
      }
      this.requestUrls = {
        templateUrlPre: 'bgConfig',
        templateUrl: 'downloadOrderStockOccupy',
        uploadUrl: 'uploadOrderStockOccupy'
      }
      this.downTemplateName = this.$t('供应商预占库存配置模板')
      this.showUploadExcel(true)
    },

    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = [] // 清空数据
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },

    // 导出
    handleDownload() {
      this.$store.commit('startLoading')
      this.$API.bgConfig.downloadOrderStockOccupy({ flag: 1 }).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },

    // 上传成功后，获取到的数据
    upExcelConfirm(res) {
      let tempNum = config.orderStockOccupyDataSource.length
      res.data.forEach((record) => {
        // 供应商预占库存 用于提交的特殊配置
        config.orderStockOccupySpecConfig.push({
          ...record,
          stockOccupyFlag: Number(record.stockOccupyFlag)
        })
        // 供应商预占库存 用于页面展示的特殊配置
        config.orderStockOccupyDataSource.push({
          ...record,
          serialNumber: ++tempNum
        })
      })

      this.showUploadExcel(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
    },

    sameTypeConfigCheck(arr) {
      var tempSet = new Set(arr)
      return tempSet.size !== arr.length
    },

    saveConfig() {
      if (config.orderStockOccupySpecConfig.some((item) => item.supplierId == null)) {
        this.$toast({
          content: this.$t('供应商不能为空，请确认后再提交'),
          type: 'warning'
        })
        return
      }
      let tempArr = config.orderStockOccupySpecConfig.map(
        (ele) => `${ele.businessTypeId}!!!!${ele.supplierId}`
      )
      if (this.sameTypeConfigCheck(tempArr)) {
        this.$toast({
          content: this.$t('存在重复类型的特殊配置，请确认后再提交'),
          type: 'warning'
        })
        return
      }
      let orderStockOccupy = config.orderStockOccupyDefaultConfig.concat(
        config.orderStockOccupySpecConfig
      )
      this.$API.bgConfig.saveOrderStockOccupyConfig(orderStockOccupy).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$store.commit('startLoading')
          this.getConfig()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.top-filter {
  background: #fff;
  padding: 20px 20px 20px 20px;
  font-weight: 500;

  .accordion-main {
    margin-right: 20px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 25px;
  }

  .left-status {
    margin: 0px 20px 20px 0px;
    clear: both;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  &-small {
    height: 50px;
    overflow: hidden;
  }
}
.second-top-filter {
  background: #fff;
  padding-bottom: 5px;
  font-weight: 500;
  padding-left: 20px;

  .second-left-status {
    height: 20px;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  &-small {
    height: 50px;
    overflow: hidden;
  }
}
.default-setting {
  width: 240px;
  height: 70px;
  float: left;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: normal;
  margin-right: 20px;
  color: rgba(41, 41, 41, 1);
  &-small {
    height: 60px;
  }
  &-title {
    height: 14px;
    display: block;
    margin-bottom: 20px;
    &-large {
      display: block;
      margin-bottom: 20px;
      width: 320px;
    }
  }
  &-suppliertab {
    width: 500px;
    default-setting-title {
      display: block;
      margin-bottom: 20px;
    }
  }
  &:nth-child(6) {
    width: 163px;
  }
}
.accordion-main {
  margin-right: 20px;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 25px;
}

.left-status {
  margin: 0px 20px 20px 0px;
  clear: both;
}
.accordion-title {
  float: left;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  text-indent: 10px;
  border-left: 5px solid #00469c;
  margin-bottom: 20px;
  border-radius: 2px 0 0 2px;
}

.sort-box {
  position: relative;
  cursor: pointer;
  width: 5px;
  float: right;
  margin: 15px 30px 0px 0px;

  .mt-icons {
    font-size: 12px;
    transform: scale(0.5);
    color: rgba(0, 70, 156, 1);
    margin-top: -15px;
    position: absolute;
    top: 0;
    &:nth-child(2) {
      top: 6px;
    }
  }
}
.mt-radio /deep/ .e-radio-wrapper {
  margin-right: 40px;
}
.operateButton {
  height: 50px;
  line-height: 50px;
  text-align: right;
}
</style>
