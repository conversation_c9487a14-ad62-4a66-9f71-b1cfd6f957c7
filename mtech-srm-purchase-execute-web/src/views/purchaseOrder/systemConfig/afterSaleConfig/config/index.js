import { i18n } from '@/main.js'

// 售后原因维护 用于提交的数据
export let orderAfterSalesReasonData = []

// 售后原因维护 用于页面展示的数据
export let orderAfterSalesReasonDataSource = []

// 售后原因维护
export const aftermarketReasonColumn = [
  {
    // width: "210",
    field: 'serialNumber',
    headerText: i18n.t('序号'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        permission: ['O_02_0595']
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        permission: ['O_02_0599']
      }
    ],
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return Number(+row.index + 1)
      }
    }
  },
  {
    // width: "260",
    field: 'afterSalesCode',
    headerText: i18n.t('售后编码')
  },
  {
    // width: "350",
    field: 'afterSalesReason',
    headerText: i18n.t('售后原因')
  }
]
