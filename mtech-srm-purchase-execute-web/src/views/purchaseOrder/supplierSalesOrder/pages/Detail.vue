<!-- 明细视图 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('销售订单号')" prop="salesOrderNo">
          <mt-input
            v-model="searchFormModel.salesOrderNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('销售订单行号')" prop="salesOrderItemNo">
          <mt-input
            v-model="searchFormModel.salesOrderItemNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('采购订单号')" prop="purchaseOrderNo">
          <mt-input
            v-model="searchFormModel.purchaseOrderNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('采购订单行号')" prop="purchaseOrderItemNo">
          <mt-input
            v-model="searchFormModel.purchaseOrderItemNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('采购订单状态')" prop="purOrderStatus">
          <mt-multi-select
            v-model="searchFormModel.purOrderStatus"
            :data-source="purOrderStatusOptions"
            :show-select-all="true"
            :show-clear-button="false"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('销售订单行状态')" prop="status">
          <mt-multi-select
            v-model="searchFormModel.status"
            :data-source="statusOptions"
            :show-select-all="true"
            :show-clear-button="false"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('工厂')" prop="siteCode">
          <RemoteAutocomplete
            v-model="searchFormModel.siteCode"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供应商')" prop="supplierCode">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料编码')" prop="itemCode">
          <mt-input
            v-model="searchFormModel.itemCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料名称')" prop="itemName">
          <mt-input
            v-model="searchFormModel.itemName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供方物料编码')" prop="supplierItemCode">
          <mt-input
            v-model="searchFormModel.supplierItemCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供方物料名称')" prop="supplierItemName">
          <mt-input
            v-model="searchFormModel.supplierItemName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="buyerDemandDeliveryDate" :label="$t('采方要求交货日期')">
          <mt-date-range-picker
            v-model="searchFormModel.buyerDemandDeliveryDate"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'buyerDemandDeliveryDate')"
          />
        </mt-form-item>
        <mt-form-item prop="supEstimateSendDate" :label="$t('供方预计发货日期')">
          <mt-date-range-picker
            v-model="searchFormModel.supEstimateSendDate"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'supEstimateSendDate')"
          />
        </mt-form-item>
        <mt-form-item prop="supEstimateDeliveryDate" :label="$t('供方预计到货日期')">
          <mt-date-range-picker
            v-model="searchFormModel.supEstimateDeliveryDate"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'supEstimateDeliveryDate')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供方预计交货状态')" prop="supEstimateDeliveryStatus">
          <mt-multi-select
            v-model="searchFormModel.supEstimateDeliveryStatus"
            :data-source="supEstimateDeliveryStatusOptions"
            :show-select-all="true"
            :show-clear-button="false"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供应商确认状态')" prop="purOrderFeedbackStatus">
          <mt-multi-select
            v-model="searchFormModel.purOrderFeedbackStatus"
            :data-source="purOrderFeedbackStatusOptions"
            :show-select-all="true"
            :show-clear-button="false"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('入库状态')" prop="purOrderWarehouseStatus">
          <mt-multi-select
            v-model="searchFormModel.purOrderWarehouseStatus"
            :data-source="purOrderWarehouseStatusOptions"
            :show-select-all="true"
            :show-clear-button="false"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('发货状态')" prop="purOrderDeliveryStatus">
          <mt-multi-select
            v-model="searchFormModel.purOrderDeliveryStatus"
            :data-source="purOrderDeliveryStatusOptions"
            :show-select-all="true"
            :show-clear-button="false"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('关闭状态')" prop="purOrderCloseStatus">
          <mt-multi-select
            v-model="searchFormModel.purOrderCloseStatus"
            :data-source="purOrderCloseStatusOptions"
            :show-select-all="true"
            :show-clear-button="false"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('同步-code')" prop="returnCode">
          <mt-input
            v-model="searchFormModel.returnCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('同步-reason')" prop="reason">
          <mt-input
            v-model="searchFormModel.reason"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('同步-message')" prop="msg">
          <mt-input
            v-model="searchFormModel.msg"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="9760e3c0-2412-41de-94dd-596fc51f0e8c"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      keep-source
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #purchaseOrderNoDefault="{ row }">
        <div style="cursor: pointer; color: #2783fe" @click="handleJump(row)">
          {{ row.purchaseOrderNo }}
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
    <DetailOrderQuery ref="detailOrderQueryRef" @confirm="handleSearch" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import {
  columnData,
  soStatusOptions,
  statusOptions,
  poStatusOptions,
  supplierConfirmStatusOptions,
  supEstimateDeliveryStatusOptions,
  purOrderStatusOptions,
  purOrderFeedbackStatusOptions,
  purOrderWarehouseStatusOptions,
  purOrderDeliveryStatusOptions,
  purOrderCloseStatusOptions
} from '../config/detail'
import { getHeadersFileName, download } from '@/utils/utils'
import DetailOrderQuery from '../components/OrderQuery.vue'

export default {
  components: { CollapseSearch, ScTable, DetailOrderQuery },
  data() {
    return {
      searchFormModel: {},
      toolbar: [
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
        { code: 'manualGet', name: this.$t('手工获取'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      soStatusOptions,
      statusOptions,
      poStatusOptions,
      supplierConfirmStatusOptions,
      supEstimateDeliveryStatusOptions,
      purOrderStatusOptions,
      purOrderFeedbackStatusOptions,
      purOrderWarehouseStatusOptions,
      purOrderDeliveryStatusOptions,
      purOrderCloseStatusOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleJump(row) {
      this.$router.push({
        name: 'purchase-coordination-detail',
        query: {
          orderid: row.purchaseOrderId,
          type: '3'
        }
      })
    },
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.purchaseOrder
        .pageSupplierSalesOrderDetailApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        case 'manualGet':
          this.handleQuery()
          break
        default:
          break
      }
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$API.purchaseOrder
        .exportSupplierSalesOrderDetailApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    handleQuery() {
      this.$refs.detailOrderQueryRef.dialogInit({
        title: this.$t('获取供方订单')
      })
    }
  }
}
</script>
