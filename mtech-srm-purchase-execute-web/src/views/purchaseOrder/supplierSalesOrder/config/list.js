import { i18n } from '@/main.js'

export const soStatusOptions = [
  { text: i18n.t('已确认'), value: 'OPEN' },
  { text: i18n.t('已拒绝'), value: 'REJECTED' },
  { text: i18n.t('待定'), value: 'PENDING' },
  { text: i18n.t('正在处理'), value: 'BEING PROCESSED' },
  { text: i18n.t('已完成'), value: 'COMPLETED' }
]

export const poStatusOptions = [
  { text: i18n.t('草稿'), value: 0 },
  { text: i18n.t('待审批'), value: 1 },
  { text: i18n.t('审批通过'), value: 2 },
  { text: i18n.t('审批拒绝'), value: 3 },
  { text: i18n.t('关闭'), value: 4 },
  { text: i18n.t('完成'), value: 5 }
]

export const supplierConfirmStatusOptions = [
  { text: i18n.t('待确认'), value: 0 },
  { text: i18n.t('反馈异常'), value: 1 },
  { text: i18n.t('反馈正常'), value: 2 }
]

export const columnData = [
  // {
  //   type: 'checkbox',
  //   width: 50,
  //   fixed: 'left',
  //   align: 'center'
  // },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'salesOrderNo',
    title: i18n.t('销售订单号'),
    minWidth: 120
  },
  {
    field: 'purchaseOrderNo',
    title: i18n.t('采购订单号'),
    minWidth: 120,
    slots: {
      default: 'purchaseOrderNoDefault'
    }
  },
  {
    field: 'soDate',
    title: i18n.t('销售订单日期'),
    minWidth: 160
  },
  {
    field: 'poDate',
    title: i18n.t('采购订单日期'),
    minWidth: 160
  },
  {
    field: 'soStatus',
    title: i18n.t('销售订单状态'),
    minWidth: 140,
    formatter: ({ cellValue }) => {
      let item = soStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'poStatus',
    title: i18n.t('采购订单状态'),
    minWidth: 140,
    formatter: ({ cellValue }) => {
      let item = poStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'supplierConfirmStatus',
    title: i18n.t('供应商确认状态'),
    minWidth: 140,
    formatter: ({ cellValue }) => {
      let item = supplierConfirmStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 200,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    }
  },
  {
    field: 'companyCode',
    title: i18n.t('公司'),
    minWidth: 200,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.companyName : ''
    }
  },
  {
    field: 'purchaseGroupCode',
    title: i18n.t('采购组'),
    minWidth: 200,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.purchaseGroupName : ''
    }
  },
  {
    field: 'currencyCode',
    title: i18n.t('币种'),
    minWidth: 100
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    minWidth: 120
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    minWidth: 120
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  },
  {
    field: 'remark',
    title: i18n.t('备注'),
    minWidth: 120
  }
]
