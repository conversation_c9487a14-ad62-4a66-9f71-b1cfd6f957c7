import { i18n } from '@/main.js'

export const soStatusOptions = [
  { text: i18n.t('已确认'), value: 'OPEN' },
  { text: i18n.t('已拒绝'), value: 'REJECTED' },
  { text: i18n.t('待定'), value: 'PENDING' },
  { text: i18n.t('正在处理'), value: 'BEING PROCESSED' },
  { text: i18n.t('已完成'), value: 'COMPLETED' }
]

export const statusOptions = [
  { text: i18n.t('未交货'), value: 'Not Delivered' },
  { text: i18n.t('已部分交货'), value: 'Partially Delivered' },
  { text: i18n.t('已全部交货'), value: 'Fully Deivered' },
  { text: i18n.t('已取消'), value: 'Cancelled' },
  { text: i18n.t('已拒绝'), value: 'Rejected' },
  { text: i18n.t('待定'), value: 'Pending' }
]

export const purOrderStatusOptions = [
  { text: i18n.t('草稿'), value: 0 },
  { text: i18n.t('待审批'), value: 1 },
  { text: i18n.t('审批通过'), value: 2 },
  { text: i18n.t('审批拒绝'), value: 3 },
  { text: i18n.t('关闭'), value: 4 },
  { text: i18n.t('完成'), value: 5 }
]

export const poStatusOptions = [
  { text: i18n.t('草稿'), value: 0 },
  { text: i18n.t('待审批'), value: 1 },
  { text: i18n.t('审批通过'), value: 2 },
  { text: i18n.t('审批拒绝'), value: 3 },
  { text: i18n.t('关闭'), value: 4 },
  { text: i18n.t('完成'), value: 5 }
]

export const supplierConfirmStatusOptions = [
  { text: i18n.t('待确认'), value: 0 },
  { text: i18n.t('反馈异常'), value: 1 },
  { text: i18n.t('反馈正常'), value: 2 }
]

export const supEstimateDeliveryStatusOptions = [
  { text: i18n.t('有回复交期 '), value: 'Confirmed' },
  { text: i18n.t('未回复交期 '), value: 'NULL' }
]

export const purOrderFeedbackStatusOptions = [
  { text: i18n.t('未反馈'), value: 0 },
  { text: i18n.t('供应商接受'), value: 2 },
  { text: i18n.t('供应商拒绝'), value: 1 }
]

export const purOrderWarehouseStatusOptions = [
  { text: i18n.t('未入库'), value: 0 },
  { text: i18n.t('部分入库'), value: 1 },
  { text: i18n.t('全部入库'), value: 2 }
]

export const purOrderDeliveryStatusOptions = [
  { text: i18n.t('未发货'), value: 0 },
  { text: i18n.t('部分发货'), value: 1 },
  { text: i18n.t('全部发货'), value: 2 }
]

export const purOrderCloseStatusOptions = [
  { text: i18n.t('未关闭'), value: 0 },
  { text: i18n.t('已关闭'), value: 1 }
]

export const columnData = [
  // {
  //   type: 'checkbox',
  //   width: 50,
  //   fixed: 'left',
  //   align: 'center'
  // },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'salesOrderNo',
    title: i18n.t('销售订单号'),
    minWidth: 120
  },
  {
    field: 'salesOrderItemNo',
    title: i18n.t('销售订单行号'),
    minWidth: 120
  },
  {
    field: 'purchaseOrderNo',
    title: i18n.t('采购订单号'),
    minWidth: 120,
    slots: {
      default: 'purchaseOrderNoDefault'
    }
  },
  {
    field: 'purchaseOrderItemNo',
    title: i18n.t('采购订单行号'),
    minWidth: 120
  },
  {
    field: 'purOrderStatus',
    title: i18n.t('采购订单状态'),
    minWidth: 140,
    formatter: ({ cellValue }) => {
      let item = purOrderStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  // {
  //   field: 'soStatus',
  //   title: i18n.t('销售订单状态'),
  //   minWidth: 140,
  //   formatter: ({ cellValue }) => {
  //     let item = soStatusOptions.find((item) => item.value === cellValue)
  //     return item ? item.text : ''
  //   }
  // },
  {
    field: 'status',
    title: i18n.t('销售订单行状态'),
    minWidth: 140,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂'),
    minWidth: 200,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.siteName : ''
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 200,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    }
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码'),
    minWidth: 160
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称'),
    minWidth: 200
  },
  {
    field: 'buyerDemandDeliveryDate',
    title: i18n.t('采方要求交货日期'),
    minWidth: 160
  },
  {
    field: 'buyerDemandDeliveryQty',
    title: i18n.t('采方要求交货数量'),
    minWidth: 160
  },
  {
    field: 'planItemNo',
    title: i18n.t('供方交货计划行号'),
    minWidth: 160
  },
  {
    field: 'supEstimateSendDate',
    title: i18n.t('供方预计发货日期'),
    minWidth: 160
  },
  {
    field: 'supEstimateDeliveryDate',
    title: i18n.t('供方预计到货日期'),
    minWidth: 160
  },
  {
    field: 'supPlanSendQty',
    title: i18n.t('供方计划交货数量'),
    minWidth: 160
  },
  {
    field: 'supEstimateDeliveryStatus',
    title: i18n.t('供方预计交货状态'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      let item = supEstimateDeliveryStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'purOrderFeedbackStatus',
    title: i18n.t('供应商确认状态'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      let item = purOrderFeedbackStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'purOrderWarehouseStatus',
    title: i18n.t('入库状态'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      let item = purOrderWarehouseStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'purOrderDeliveryStatus',
    title: i18n.t('发货状态'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      let item = purOrderDeliveryStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'purOrderCloseStatus',
    title: i18n.t('关闭状态'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      let item = purOrderCloseStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'supDeliveryQty',
    title: i18n.t('供方已交货数量'),
    minWidth: 160
  },
  // {
  //   field: 'poStatus',
  //   title: i18n.t('采购订单状态'),
  //   minWidth: 160,
  //   formatter: ({ cellValue }) => {
  //     let item = poStatusOptions.find((item) => item.value === cellValue)
  //     return item ? item.text : ''
  //   }
  // },
  // {
  //   field: 'supplierConfirmStatus',
  //   title: i18n.t('供应商确认状态'),
  //   minWidth: 160,
  //   formatter: ({ cellValue }) => {
  //     let item = supplierConfirmStatusOptions.find((item) => item.value === cellValue)
  //     return item ? item.text : ''
  //   }
  // },
  // {
  //   field: 'soDate',
  //   title: i18n.t('销售订单日期'),
  //   minWidth: 160
  // },
  // {
  //   field: 'poDate',
  //   title: i18n.t('采购订单日期'),
  //   minWidth: 160
  // },
  // {
  //   field: 'companyCode',
  //   title: i18n.t('公司'),
  //   minWidth: 200,
  //   formatter: ({ cellValue, row }) => {
  //     return cellValue ? cellValue + '-' + row.companyName : ''
  //   }
  // },
  // {
  //   field: 'siteCode',
  //   title: i18n.t('工厂'),
  //   minWidth: 200,
  //   formatter: ({ cellValue, row }) => {
  //     return cellValue ? cellValue + '-' + row.siteName : ''
  //   }
  // },
  // {
  //   field: 'purchaseGroupCode',
  //   title: i18n.t('采购组'),
  //   minWidth: 200,
  //   formatter: ({ cellValue, row }) => {
  //     return cellValue ? cellValue + '-' + row.purchaseGroupName : ''
  //   }
  // },
  {
    field: 'supplierItemCode',
    title: i18n.t('供方物料编码'),
    minWidth: 200
  },
  {
    field: 'supplierItemName',
    title: i18n.t('供方物料名称'),
    minWidth: 200
  },
  // {
  //   field: 'unit',
  //   title: i18n.t('基本单位'),
  //   minWidth: 100
  // },
  // {
  //   field: 'purchasingUnit',
  //   title: i18n.t('采购单位'),
  //   minWidth: 100
  // },
  {
    field: 'quantity',
    title: i18n.t('订单总数量'),
    minWidth: 200
  },
  // {
  //   field: 'supUntaxedUnitPrice',
  //   title: i18n.t('未税单价'),
  //   minWidth: 100
  // },
  // {
  //   field: 'freeTotal', // 待后端字段
  //   title: i18n.t('未税总价'),
  //   minWidth: 100
  // },
  {
    field: 'currencyCode',
    title: i18n.t('币种')
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    minWidth: 120
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    minWidth: 120
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  },
  {
    field: 'returnCode',
    title: i18n.t('同步-code'),
    minWidth: 160
  },
  {
    field: 'reason',
    title: i18n.t('同步-reason'),
    minWidth: 160
  },
  {
    field: 'msg',
    title: i18n.t('同步-message'),
    minWidth: 160
  }
]
