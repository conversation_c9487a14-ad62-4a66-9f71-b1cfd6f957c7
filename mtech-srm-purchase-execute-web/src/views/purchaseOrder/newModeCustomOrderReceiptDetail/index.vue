<template>
  <div class="detail-fix-wrap full-height purchase-custom">
    <div class="header-box" v-if="entryType === '2'">
      <div class="search-con" style="padding-left: 20px">
        <mt-select
          :width="274"
          :data-source="versionOptions"
          :allow-filtering="true"
          :placeholder="$t('请选择版本')"
          @change="versionChange"
          v-model="selectVersion"
        >
        </mt-select>
      </div>
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
    </div>
    <top-info
      :entry-type="entryType"
      :top-info="topInfo"
      :entry-id="entryId"
      @submit="submit"
    ></top-info>
    <mt-template-page ref="templateRef1" :template-config="pageConfig">
      <div
        slot="slot-0"
        :class="[
          $route.query.type == '1' && 'grid-wrap',
          ($route.query.type == '3' || $route.query.type == '2') && 'grid-wrap-page'
        ]"
      >
        <mt-template-page
          ref="grid0"
          :template-config="pageConfig1"
          @handleClickToolBar="handleClickToolBar"
          @actionBegin="actionBegin"
          @actionComplete="actionComplete"
          @dateChange="dateChange"
          @remarkChange="remarkChange"
          @supplierPromiseQtyChange="supplierPromiseQtyChange"
        ></mt-template-page>
      </div>
      <div slot="slot-1" class="full-height">
        <relative-file
          :entry-id="entryId"
          :entry-type="entryType"
          :module-file-list="moduleFileList"
          :entry-file-list="entryFileList"
          :new-file-list="newFileList"
          change-node-code="po_item_file"
          @updateFile="updateFile"
        ></relative-file>
      </div>
    </mt-template-page>
    <accept-or-refuse-dialog
      ref="acceptOrRefuseDialog"
      @refreshColumns="refreshColumns"
    ></accept-or-refuse-dialog>
    <!-- </div>
    </div> -->
  </div>
</template>
<script>
import UTILS from '@/utils/utils'
import * as CONFIG from './config/config'
import InputNumber from './components/InputNumber.vue'
export default {
  components: {
    TopInfo: () => import('./components/topInfo'),
    AcceptOrRefuseDialog: () => import('./components/acceptOrRefuseDialog'),
    RelativeFile: () => import('@/components/businessComponents/relativeFile1/index.vue')
  },
  data() {
    return {
      pageSettings: {
        pageSize: 1,
        pageCount: 8,
        totalRecordsCount: 0,
        enableQueryString: true,
        pageSizes: [1, 10, 20, 50, 100, 200]
      },
      toolbar: [
        {
          id: 'accept',
          prefixIcon: 'e-add',
          text: this.$t('接受')
        },
        {
          id: 'reject',
          prefixIcon: 'e-add',
          text: this.$t('拒绝')
        }
      ],
      dataSource1: [],
      columnData: [
        {
          width: '50',
          type: 'checkbox',
          showInColumnChooser: false
        }
      ],
      filterOptions: {
        type: 'Menu'
      },
      selectVersion: '',
      versionOptions: [],
      entryId: null,
      entryType: null, //1是编辑 3是详情 2是历史反馈
      topInfo: {},
      pageConfig: [
        { title: this.$t('订单明细'), moduleId: '', moduleKey: '' },
        { title: '', moduleId: '', moduleKey: '' }
      ],
      pageConfig1: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [
            [
              {
                id: 'Edit',
                icon: 'icon_list_edit',
                title: this.$t('批量编辑')
              }
            ],
            ['Setting']
          ],
          grid: {
            allowPaging: false, // 不分页
            allowEditing: true, //开启表格编辑操作
            editSettings: {
              allowEditing: false,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            columnData: [
              {
                width: '50',
                type: 'checkbox',
                showInColumnChooser: false
              }
            ],
            // height: "auto",
            dataSource: [],
            // frozenColumns: 1,
            asyncConfig: {
              // url: "/srm-purchase-execute/tenant/supOrder/module/data",
              // recordsPosition: "data.supOrderDetailResponse.records",
              // queryBuilderWrap: "requestParams",
              // params: {
              //   docId: 1,
              //   // moduleId: 0,
              //   moduleKey: 2,
              // },
            }
          }
        }
      ],
      fields: [],
      moduleKey: '',
      entryFileList: [],
      moduleFileList: [],
      newFileList: [],
      addId: '1',
      isEdit: '1',
      itemsList: []
    }
  },
  mounted() {
    this.entryId = this.$route.query.id
    this.entryType = this.$route.query.type
    this.init()
  },
  methods: {
    supplierPromiseQtyChange(val) {
      this.itemsList.some((item) => {
        if (item.addId == val.addId) {
          item.supplierPromiseQty = val.supplierPromiseQty
          return
        }
      })
    },
    remarkChange(val) {
      this.itemsList.some((item) => {
        if (item.addId == val.addId) {
          item.remark = val.remark
          return
        }
      })
    },
    dateChange(val) {
      this.itemsList.some((item) => {
        if (item.addId == val.addId) {
          item.timePromise = new Date(val.date).getTime()
          return
        }
      })
    },
    submit(val) {
      //提交反馈
      // if (this.isEdit === "1") {
      //   this.endEdit();
      // }
      let params = val
      params.items = this.itemsList.map((item) => {
        return {
          timePromise: item.timePromise,
          itemNo: item.itemNo,
          remark: item.remark,
          supplierPromiseQty: item.supplierPromiseQty
        }
      })
      // console.log(params, "ahhfds 范德萨");
      this.$store.commit('startLoading')
      this.$API.purchaseOrder.supOrderFeedBack(params).then((res) => {
        console.log(res.data)
        this.$store.commit('endLoading')
        if (res.data) {
          this.$toast({ content: this.$t('反馈成功'), type: 'success' })
          this.$router.push({
            name: 'new-mode-custom-receipt'
          })
        } else {
          this.$toast({ content: this.$t('反馈失败'), type: 'error' })
        }
      })
    },
    getParams() {
      let list = this.getCurrentRecords()
      let params = []
      list.forEach((item) => {
        params.push({
          itemNo: item.itemNo,
          timePromise: new Date(item.timePromise).getTime(),
          remark: item.supRemark || ''
        })
      })
      return params
    },
    endEdit() {
      //让组件失去焦点
      this.$refs.grid0.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
    },
    getCurrentRecords() {
      let currentRecords = this.$refs.grid0
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      return currentRecords
    },
    updateFile(e) {
      this.fileList = e
    },
    refreshColumns() {
      // this.$refs[`grid0`].refreshCurrentGridData();
    },
    actionBegin(args) {
      console.log(args, '我是actionBegin')
      if (args.requestType == 'beginEdit') {
        this.nowEditRowFlag = args.rowData.addId
        if (args.rowData.confirmStatus == 2) {
          this.$toast({
            content: this.$t('供方已确认不可编辑'),
            type: 'warning'
          })
          args.cancel = true
        }
      }
    },
    actionComplete(args) {
      console.log(args, '我是actionComplete')
      if (args.requestType === 'beginEdit' || args.requestType === 'add') {
        this.isEdit = '1'
      }
      if (
        args.requestType === 'refresh' ||
        args.requestType === 'save' ||
        args.requestType === 'delete'
      ) {
        this.isEdit = '2'
      }
      if (args.requestType === 'save' && args.action === 'edit') {
        //编辑完成
        // let row = this.getRow();
        // this.editRow(row);
        this.getCurrentRecords()
      }
    },
    async init() {
      //编辑或是详情
      if (this.entryType === '1' || this.entryType === '3') {
        this.pageConfig1[0].grid.allowPaging = false
        await this.$API.purchaseOrder.getnewModeSupOrder(this.entryId).then((res) => {
          this.topInfo = {
            ...res.data,
            requiredDeliveryDate: UTILS.dateFormat(Number(res.data.requiredDeliveryDate), 'Y-m-d'),
            companyName1: `${res.data.companyCode}-${res.data.companyName}`,
            buyerGroupName1: `${res.data.buyerGroupCode}-${res.data.buyerGroupName}`,
            buyerOrgName1: `${res.data.buyerOrgCode}-${res.data.buyerOrgName}`,
            sourceCurrencyId: '1'
          }
        })
        console.log()
        await this.getModuleConfig()
        await this.getFileNodeByDocId()
        await this.getPurOrderModuleData2()
        this.getModuleData()
        if (this.entryType === '3') {
          this.pageConfig1[0].toolbar = [[], ['Setting']]
          this.pageConfig1[0].grid.allowPaging = true
        }
      }
      //历史反馈
      if (this.entryType === '2') {
        this.pageConfig1[0].toolbar = [[], ['Setting']]
        this.pageConfig1[0].grid.allowPaging = true
        let params = {
          orderCode: this.$route.query.orderCode,
          purTenantId: this.$route.query.tenantId,
          version: 0
        }
        await this.$API.purchaseOrder.getVersion(params).then((res) => {
          this.versionOptions = []
          let list = []
          res.data.forEach((item) => {
            list.push({ text: `V${item}`, value: item })
          })
          this.versionOptions = list
        })
        this.selectVersion = this.versionOptions[this.versionOptions.length - 1].value
        // this.versionChange({ value: this.selectVersion });
      }
    },
    async getFileNodeByDocId() {
      //编辑进入获取左边节点
      let params = {
        docId: this.entryId,
        docType: 'so'
      }
      await this.$API.purchaseOrder.getFileNodeByDocId(params).then((res) => {
        this.moduleFileList = res.data
      })
    },
    async getPurOrderModuleData2() {
      //附件带入的信息
      let params = {
        docId: this.entryId,
        parentId: 0,
        docType: 'so'
      }
      await this.$API.purchaseOrder.queryFileByDocId(params).then((res) => {
        let entryFileList = []
        let list = res.data || []
        list.forEach((item) => {
          if (item.nodeType === 1) {
            item.id1 = item.id
            item.id = item.sysFileId
            item.remoteUrl = item.url
            entryFileList.push(item)
          }
        })
        this.entryFileList = entryFileList
      })
    },
    async getModuleConfig() {
      console.log('123123123123')
      let params = { docId: this.entryId }
      await this.$API.purchaseOrder.getModuleConfig(params).then((res) => {
        // const _config = [
        //   { title: '', moduleId: '', moduleKey: '' },
        //   { title: '', moduleId: '', moduleKey: '' }
        // ]
        // this.$set(ths)
        res.data.moduleItems.some((item) => {
          if (item.moduleType === 6) {
            this.$set(this.pageConfig[0], 'title', item.moduleName)
            this.$set(this.pageConfig[0], 'moduleKey', item.moduleKey)
            this.$set(this.pageConfig[0], 'moduleId', item.moduleId)
            return
          }
        })
        let hasItem = false
        res.data.moduleItems.some((item) => {
          if (item.moduleType === 7) {
            hasItem = true
            this.$set(this.pageConfig[1], 'title', item.moduleName)
            this.$set(this.pageConfig[1], 'moduleKey', item.moduleKey)
            this.$set(this.pageConfig[1], 'moduleId', item.moduleId)
            return
          }
        })
        if (!hasItem) {
          this.pageConfig.pop()
        }
        res.data.moduleItems.some((item) => {
          if (item.moduleType === 6) {
            item.fieldDefines.forEach((item2) => {
              item2.field = item2.fieldCode
              item2.headerText = item2.fieldName
              item2.width = '150'
            })
            let list = this.columnData
              .concat(
                item.fieldDefines.map((item1) => {
                  let obj = {}
                  let hasItem = false
                  CONFIG.columnData1.some((item2) => {
                    if (item2.field === item1.fieldCode) {
                      obj = {
                        ...item2,
                        headerText: item1.fieldName,
                        hasItem: 1
                      }
                      hasItem = true
                    }
                  })
                  if (hasItem) {
                    return obj
                  } else {
                    return {
                      ...item1,
                      headerText: item1.fieldName,
                      field: item1.fieldCode,
                      hasItem: 2
                    }
                  }
                })
              )
              .concat(CONFIG.lastColumn)
            list.forEach((item) => {
              if (item.field === 'timePromise' || item.field === 'supRemark') {
                if (this.entryType === '2' || this.entryType === '3') {
                  delete item.template
                }
              }
              if (item.field === 'supplierPromiseQty') {
                item.template = () => {
                  return {
                    template: InputNumber
                  }
                }
              }
            })
            this.pageConfig1[0].grid.columnData = list
          }
        })
      })
    },
    getModuleData() {
      let params = {}
      if (this.entryType === '2' || this.entryType === '3') {
        params = {
          moduleId: this.pageConfig[0].moduleId,
          moduleKey: this.pageConfig[0].moduleKey,
          orderId: this.entryId,
          moduleType: 6
        }
      }
      if (this.entryType === '1') {
        params = {
          moduleId: this.pageConfig[0].moduleId,
          moduleKey: this.pageConfig[0].moduleKey,
          orderId: this.entryId,
          moduleType: 6,
          page: { current: 1, size: 1000000000 }
        }
      }
      this.$set(this.pageConfig1[0].grid, 'asyncConfig', {
        url: '/srm-purchase-execute/tenant/supOrder/module/data',
        recordsPosition: 'data.supOrderDetailResponse.records',
        queryBuilderWrap: 'requestParams',
        params: params,
        serializeList: (list) => {
          let itemsList = []
          list.forEach((item) => {
            item.addId = this.addId++
            item.budgetQuantity = item.forecastQty
            item.agreementCode = item.contract
            if (!item.requiredDeliveryDate?.length == 13) {
              item.timePromise = this.getToday()
            }

            if (
              item.requiredDeliveryDate &&
              item.requiredDeliveryDate.length == 13 &&
              !item.timePromise
            ) {
              item.timePromise = new Date(Number(item.requiredDeliveryDate))
            }
            if (item.timePromise && item.timePromise.length == 13) {
              item.timePromise = new Date(Number(item.timePromise))
            }
            if (this.entryType === '2' || this.entryType === '3') {
              item.timePromise = UTILS.formateTime(item.timePromise)
            }
            if (item.costs) {
              item.costs1 = item.costs
              let str = ''
              item.costs.forEach((item1) => {
                str += item1.costCenterName + '-' + item1.percentage
              })
              item.orderCosts = str
            }
            item.supplierPromiseQty = item?.quantity ?? 0
            item.fieldDataList = item.fieldDataList || []
            item.fieldDataList.forEach((item1) => {
              //对应的动态字段 需求名称,需求描述,资产类别,资产编号,资产卡片,客户,关联客户订单
              item[item1.fieldCode] = item1.fieldData
            })
            item.file = []
            if (this.entryFileList) {
              let entryfiles = []
              this.entryFileList.forEach((item1) => {
                if (item1.nodeCode === 'po_item_file') {
                  entryfiles.push(item1)
                }
              })
              if (!entryfiles) return
              entryfiles.forEach((item2) => {
                if (item2.fileDetailId === item.id) {
                  item.file = item.file.concat(item2)
                  item.file.forEach((item1) => {
                    item1.remoteUrl = item1.url
                  })
                  item.file1 = item2
                  this.newFileList.push({
                    docId: 0,
                    docType: 'po',
                    fileName: item.file1.fileName,
                    fileSize: item.file1.fileSize,
                    fileType: item.file1.fileType,
                    id: item.file1.id,
                    itemNo: item.itemNo || 0,
                    lineNo: item.itemNo || 0,
                    nodeCode: 'po_item_file',
                    nodeName: this.$t('订单明细附件'),
                    nodeType: 1,
                    orderDetailId: item.id || 0,
                    parentId: 0,
                    remark: '',
                    remoteFileId: item.file1.id,
                    sysFileId: item.file1.id,
                    type: 0,
                    url: item.file1.url,
                    itemCode: item.itemCode, //物料编码
                    itemName: item.itemName, //物料名称
                    skuCode: item.skuCode, //sku编码
                    skuName: item.skuName || '', //sku名称
                    specification: item.specification, //规格型号
                    createUserName: item.file1.createUserName || '', //创建人
                    createTime: item.file1.createTime || '' //创建时间
                  })
                }
              })
            }
            item.file = JSON.stringify(item.file)
            itemsList.push({
              addId: item.addId,
              itemNo: item.itemNo,
              timePromise: new Date(item.timePromise).getTime(),
              remark: item.supRemark || '',
              supplierPromiseQty: item.supplierPromiseQty
            })
          })
          this.itemsList = itemsList
          return list
        }
      })
    },
    getToday() {
      var today = new Date()
      today.setHours(0)
      today.setMinutes(0)
      today.setSeconds(0)
      today.setMilliseconds(0)
      today = new Date(today.getTime() + 24 * 60 * 60 * 1000)
      return today
    },
    async versionChange(e) {
      let params = {
        orderCode: this.$route.query.orderCode,
        version: e.value,
        purTenantId: this.$route.query.tenantId
      }
      await this.$API.purchaseOrder.getNewModeSupOrderHistory(params).then((res) => {
        this.topInfo = {
          ...res.data,
          requiredDeliveryDate: UTILS.dateFormat(Number(res.data.requiredDeliveryDate)),
          companyName1: `${res.data.companyCode}-${res.data.companyName}`,
          buyerGroupName1: `${res.data.buyerGroupCode}-${res.data.buyerGroupName}`,
          buyerOrgName1: `${res.data.buyerOrgCode}-${res.data.buyerOrgName}`,
          sourceCurrencyId: '1'
        }
        this.entryId = res.data.id
      })
      await this.getModuleConfig()
      await this.getFileNodeByDocId()
      await this.getPurOrderModuleData2()
      this.getModuleData()
    },
    goBack() {
      this.$router.push({
        name: 'new-mode-custom-receipt'
      })
    },
    handleClickToolBar(e) {
      console.log(e, e.grid.getSelectedRecords())
      if (
        e.grid.getSelectedRecords().length === 0 &&
        e.toolbar.id !== 'Add' &&
        e.toolbar.id != 'refreshDataByLocal'
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = []
      let addIds = []
      let timePromise = e.grid.getSelectedRecords()[0]?.timePromise
      e.grid.getSelectedRecords().map((item) => _id.push(item.id))
      e.grid.getSelectedRecords().map((item) => addIds.push(item.addId))
      if (e.toolbar.id == 'accept') {
        this.handleAccept(_id, e.grid.getSelectedRecords())
      } else if (e.toolbar.id == 'reject') {
        this.handleReject(_id, e.grid.getSelectedRecords())
      } else if (e.toolbar.id == 'Edit') {
        this.handleEdit(addIds, timePromise)
      }
    },
    handleEdit(addIds, timePromise) {
      // console.log(addIds, 1650211200000, "我是批量编辑");
      // this.pageConfig1[0].grid.dataSource = [{ a: "32" }];
      // addIds.forEach((item) => {
      //   this.$bus.$emit("dateEditChange", {
      //     addId: item,
      //     date: new Date(1650211200000),
      //   });
      // });
      this.handleAccept(addIds, timePromise)
    },
    handleAccept(addIds, timePromise) {
      this.$refs.acceptOrRefuseDialog.acceptInfo({
        dialogTitle: this.$t('批量编辑'),
        addIds: addIds,
        timePromise: timePromise
      })
    },
    handleReject(ids, selcetRows) {
      this.$refs.acceptOrRefuseDialog.acceptInfo({
        dialogTitle: this.$t('批量拒绝'),
        ids: ids,
        orderId: this.entryId,
        selcetRows: selcetRows
      })
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/.search-con {
  .mt-icon-icon_input_search {
    margin: 0 -15px 0 10px;
    line-height: 30px;
  }
  .select-container .e-input-group {
    font-size: 16px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(41, 41, 41, 1);
    .e-input {
      text-indent: 20px;
    }
  }
}
.header-box {
  height: 50px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.purchase-custom {
  .common-template-page {
    flex: 1;
    height: auto;
    overflow: hidden;

    /deep/ .repeat-template {
      height: 100%;
      overflow: auto;

      .template-wrap {
        height: 100%;
      }
    }
  }
  /deep/ .grid-wrap-page {
    height: 100%;

    > .common-template-page {
      flex: 1;
      overflow: auto;
      height: 100%;

      .grid-container {
        flex: 1;
        overflow: auto;
        height: auto;
      }
      .mt-data-grid {
        height: 100%;
        display: flex;
        flex-direction: column;
        > .mt-pagertemplate {
          height: 40px;
        }
        > .e-grid {
          width: auto;
          height: calc(100% - 40px);
          display: flex;
          flex-direction: column;

          .e-gridcontent {
            flex: 1;

            .e-content {
              height: 100% !important;
              position: relative;
            }
          }
        }
      }
    }
  }
  /deep/ .grid-wrap {
    height: 100%;

    > .common-template-page {
      flex: 1;
      overflow: auto;
      height: 100%;

      .grid-container {
        flex: 1;
        overflow: auto;
        height: auto;
      }
      .mt-data-grid {
        height: 100%;
        display: flex;
        flex-direction: column;
        > .mt-pagertemplate {
          height: 40px;
        }
        > .e-grid {
          width: auto;
          height: 100%;
          display: flex;
          flex-direction: column;

          .e-gridcontent {
            flex: 1;

            .e-content {
              height: 100% !important;
              position: relative;
            }
          }
        }
      }
    }
  }
}
</style>
