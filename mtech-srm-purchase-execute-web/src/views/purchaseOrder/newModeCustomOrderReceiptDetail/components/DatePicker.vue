<template>
  <div>
    <mt-date-picker
      :id="data.column.field"
      v-model="data[data.column.field]"
      :placeholder="$t('选择日期')"
      :show-clear-button="false"
      :allow-edit="false"
      @change="dateChange"
      :disabled="isDisabled"
    ></mt-date-picker>
  </div>
</template>
<script>
export default {
  data() {
    return {
      formateDate: '',
      data: {},
      addId: '',
      isDisabled: false
    }
  },
  mounted() {
    this.addId = this.data.addId
    if (
      this.data.confirmStatus == 2 ||
      this.data.receiveStatus == 2 ||
      this.data.closeStatus == 1
    ) {
      this.isDisabled = true
    } else {
      this.isDisabled = false
    }
    this.$bus.$on('dateEditChange', (val) => {
      if (val.addId === this.addId) {
        if (
          !(
            this.data.confirmStatus == 2 ||
            this.data.receiveStatus == 2 ||
            this.data.closeStatus == 1
          )
        ) {
          this.data[this.data.column.field] = val.date
        }
      }
    })
  },
  methods: {
    dateChange(val) {
      this.$parent.$emit('dateChange', { date: val, addId: this.addId })
    }
  }
}
</script>
