<template>
  <div id="cell-changed">
    <mt-input-number
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="isDisabled"
      @input="numberChange"
      v-if="maxlength"
      :precision="0"
      :maxlength="maxlength"
    ></mt-input-number>
  </div>
</template>
<script>
export default {
  data() {
    return { data: {}, isDisabled: false, addId: '', maxlength: 0 }
  },
  mounted() {
    this.maxlength = 200
    this.addId = this.data.addId
    if (
      this.data.confirmStatus == 2 ||
      this.data.receiveStatus == 2 ||
      this.data.closeStatus == 1
    ) {
      this.isDisabled = true
    } else {
      this.isDisabled = false
    }
    // this.$bus.$on("remarkEditChange", (val) => {
    //   if (val.addId === this.addId) {
    //     if (
    //       !(
    //         this.data.confirmStatus == 2 ||
    //         this.data.receiveStatus == 2 ||
    //         this.data.closeStatus == 1
    //       )
    //     ) {
    //       this.data[this.data.column.field] = val.supplierPromiseQty;
    //       this.remarkChange(val.supplierPromiseQty);
    //     }
    //   }
    // });
  },
  methods: {
    numberChange(val) {
      console.log(val)
      this.$parent.$emit('supplierPromiseQtyChange', { supplierPromiseQty: val, addId: this.addId })
    }
  }
}
</script>
