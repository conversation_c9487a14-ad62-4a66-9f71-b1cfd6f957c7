<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="infos mr20">
        {{ $t('单据编号：') }}{{ topInfo.orderCode ? 'XC' + topInfo.orderCode : '' }}
      </div>
      <div class="middle-blank"></div>
      <!-- 右侧各种操作按钮 -->
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        @click="handleClickDownload"
        v-if="entryType !== '2'"
        >{{ $t('打印') }}</mt-button
      >
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack" v-if="entryType !== '2'">{{
        $t('返回')
      }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" v-if="entryType === '1'" @click="save">{{
        $t('提交反馈')
      }}</mt-button>
      <div class="sort-box" @click="expandChange">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>
    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="topInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item required prop="sourceCurrencyId" :label="$t('业务类型')" label-style="left">
          <mt-input v-model="topInfo.businessTypeName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item required prop="orderTypeName" :label="$t('订单类型')" label-style="left">
          <mt-input v-model="topInfo.orderTypeName" :disabled="true"></mt-input>
        </mt-form-item>
        <!-- <mt-form-item required prop="sourceCurrencyId" label="合同/协议类型">
          <mt-input
            v-if="topInfo.contractType === 0"
            :value="'单次采购'"
            :disabled="true"
          ></mt-input>
          <mt-input
            v-if="topInfo.contractType === 1"
            :value="'框架协议'"
            :disabled="true"
          ></mt-input>
        </mt-form-item> -->
        <!-- <mt-form-item
          prop="purTenantName"
          :label="$t('客户名称')"
          required
          :show-message="false"
        >
          <mt-input v-model="topInfo.purTenantName" :disabled="true"></mt-input>
        </mt-form-item> -->
        <mt-form-item
          prop="sourceCurrencyId"
          :label="$t('付款条件')"
          required
          :show-message="false"
          label-style="left"
        >
          <mt-input v-model="topInfo.paymentName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="sourceCurrencyId"
          :label="$t('采购员')"
          required
          :show-message="false"
          label-style="left"
        >
          <mt-input v-model="topInfo.buyerUserName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="companyName"
          :label="$t('公司')"
          required
          :show-message="false"
          label-style="left"
        >
          <mt-input v-model="topInfo.companyName1" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="sourceCurrencyId"
          :label="$t('采购组织')"
          required
          :show-message="false"
          label-style="left"
        >
          <mt-input v-model="topInfo.buyerGroupName1" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="sourceCurrencyId"
          :label="$t('采购组')"
          required
          :show-message="false"
          label-style="left"
        >
          <mt-input v-model="topInfo.buyerOrgName1" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="sourceCurrencyId"
          :label="$t('供应商')"
          required
          :show-message="false"
          label-style="left"
        >
          <mt-input v-model="topInfo.supplierName" :disabled="true"></mt-input>
        </mt-form-item>
        <!-- <mt-form-item
          prop="sourceCurrencyId"
          :label="$t('要求交期')"
          :show-message="false"
        >
          <mt-input
            v-model="topInfo.requiredDeliveryDate"
            :disabled="true"
          ></mt-input>
        </mt-form-item> -->
        <mt-form-item
          prop="sourceCurrencyId"
          :label="$t('订单币种')"
          required
          :show-message="false"
          label-style="left"
        >
          <mt-input v-model="topInfo.currencyName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="sourceCurrencyId"
          :label="$t('含税总金额')"
          required
          :show-message="false"
          label-style="left"
        >
          <mt-input v-model="topInfo.taxTotal" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="sourceCurrencyId"
          :label="$t('未税总金额')"
          required
          :show-message="false"
          label-style="left"
        >
          <mt-input v-model="topInfo.freeTotal" :disabled="true"></mt-input>
        </mt-form-item>
        <!-- <mt-form-item
          prop="sourceCurrencyId"
          label="结算方"
          required
          :show-message="false"
        >
          <mt-input
            v-model="topInfo.settlementName"
            :disabled="true"
          ></mt-input>
        </mt-form-item> -->
        <mt-form-item
          :label="$t('发票信息')"
          prop="sourceCurrencyId"
          :show-message="false"
          label-style="left"
        >
          <mt-select
            :disabled="true"
            v-model="topInfo.invoiceId"
            :data-source="invoiceIdOptions"
            :show-clear-button="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('采方备注')" class="half-width" label-style="left">
          <mt-input v-model="topInfo.remark" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="supRemark"
          :label="$t('供方备注')"
          class="half-width"
          label-style="left"
        >
          <mt-input v-model="supRemark" :disabled="entryType === '3'"></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'

export default {
  props: {
    entryType: {
      type: String,
      default: '1'
    },
    topInfo: {
      type: Object,
      default: () => {}
    },
    entryId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isExpand: true,
      rules: {},
      invoiceIdOptions: [
        //发票
        { text: this.$t('增值'), value: '1' },
        { text: this.$t('专票'), value: '2' }
      ],
      supRemark: ''
    }
  },
  watch: {
    topInfo: {
      handler(val) {
        if (this.entryType === '3') {
          this.supRemark = val.supRemark
        }
      },
      immediate: true
    }
  },
  methods: {
    expandChange() {
      this.isExpand = !this.isExpand
      this.$refs.ruleForm.clearValidate()
    },
    // 打印
    handleClickDownload() {
      console.log('print')
      let ids = []
      ids.push(this.entryId)
      this.$API.contractPrint.supPrintOrder(ids).then((res) => {
        console.log(res, '供方合同打印')
        if (res?.data?.type === 'application/json') {
          const reader = new FileReader()
          reader.readAsText(res?.data, 'utf-8')
          reader.onload = function () {
            console.log('======', reader)
            const readerRes = reader.result
            const resObj = JSON.parse(readerRes)
            Vue.prototype.$toast({
              content: resObj.msg,
              type: 'error'
            })
          }

          return
        }
        const content = res.data
        let pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))

        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    goBack() {
      this.$router.push({
        name: 'new-mode-custom-receipt'
      })
    },
    save() {
      // let params = {
      //   id: this.entryId,
      //   supRemark: this.supRemark,
      // };
      // this.$API.purchaseOrder.supOrderFeedBack(params).then(() => {
      //   this.$toast({ content: this.$t("操作成功"), type: "success" });
      //   this.goBack();
      // });
      this.$emit('submit', {
        id: this.entryId,
        supRemark: this.supRemark
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  flex: 0 0 auto;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
    min-height: 50px;
    overflow: hidden;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;
      .mt-input {
        width: 100%;
      }
      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
    /deep/ .half-width {
      width: calc(30% - 20px) !important;
    }
  }
}
</style>
