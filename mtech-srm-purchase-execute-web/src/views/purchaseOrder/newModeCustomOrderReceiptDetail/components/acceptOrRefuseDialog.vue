<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <mt-form-item prop="timePromise" :label="$t('承诺日期')">
        <mt-date-picker
          :placeholder="$t('选择承诺日期')"
          v-model="ruleForm.timePromise"
          :show-clear-button="false"
          :allow-edit="false"
        ></mt-date-picker>
      </mt-form-item>
      <mt-form-item prop="supRemark" :label="$t('供方反馈备注')">
        <mt-input
          :placeholder="$t('请输入')"
          v-model="ruleForm.supRemark"
          v-if="maxlength"
          :maxlength="maxlength"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      orderId: '',
      entryIds: [],
      dialogTitle: '',
      selcetRows: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {
        timePromise: '',
        supRemark: ''
      },
      rules: {
        timePromise: [
          {
            required: true,
            message: this.$t('请选择承诺日期'),
            trigger: 'blur'
          }
        ],
        supRemark: [
          {
            required: false,
            message: this.$t('请输入备注'),
            trigger: 'blur'
          }
        ]
      },
      maxlength: 0
    }
  },
  mounted() {
    this.maxlength = 200
  },
  methods: {
    acceptInfo(entryInfo) {
      this.dialogTitle = entryInfo.dialogTitle
      this.entryIds = entryInfo.addIds
      this.ruleForm.timePromise = entryInfo.timePromise
      this.ruleForm.supRemark = ''
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          console.log(this.ruleForm.timePromise, '我是承诺首日封')
          this.entryIds.forEach((item) => {
            this.$bus.$emit('dateEditChange', {
              addId: item,
              date: this.ruleForm.timePromise
            })
            this.$bus.$emit('remarkEditChange', {
              addId: item,
              supRemark: this.ruleForm.supRemark
            })
          })
          this.handleClose()
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
      // this.$emit("refreshColumns");
    }
  }
}
</script>
