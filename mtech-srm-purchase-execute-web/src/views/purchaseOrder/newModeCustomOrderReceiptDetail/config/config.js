import Vue from 'vue'
import UTILS from '@/utils/utils'
// import cellUpload from "@/components/Upload/cellUpload";
import cellFileView from '@/components/normalEdit/cellFileView' // 单元格附件查看
import { i18n } from '@/main.js'
import DatePicker from '../components/DatePicker.vue'
import Input from '../components/Input.vue'
import InputNumber from '../components/Input.vue'

export const columnData1 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    field: 'orderCode',
    headerText: i18n.t('采购订单号'),
    width: '150',
    cellTools: []
  },
  {
    field: 'version',
    headerText: i18n.t('历史反馈'),
    width: '150',
    cellTools: [],
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return e - 1 === 0 ? '' : String(e - 1)
      }
    }
  },
  {
    field: 'status',
    width: '120',
    headerText: i18n.t('订单形式'),
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = ''
              if (this.data.status === 4) {
                str = i18n.t('订单关闭')
              }
              if (this.data.status === 2 && this.data.version === 1) {
                str = i18n.t('新订单')
              }
              if (this.data.status === 2 && this.data.version > 1) {
                str = i18n.t('订单变更')
              }
              return str
            }
          }
        })
      }
    }
  },
  {
    field: 'contractType',
    headerText: i18n.t('合同协议类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('单次采购'),
        1: i18n.t('框架协议')
      }
    }
  },
  {
    width: '150',
    field: 'purchaseStrategy',
    headerText: i18n.t('采购策略'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('标准'),
        2: i18n.t('寄售'),
        3: i18n.t('第三方')
      }
    }
  },
  {
    field: 'deliveryStatus',
    headerText: i18n.t('发货状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未发货'),
        1: i18n.t('部分发货'),
        2: i18n.t('全部发货')
      }
    }
  },
  {
    width: '150',
    field: 'closeStatus',
    headerText: i18n.t('关闭状态'),
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未关闭'),
        1: i18n.t('已关闭')
      }
    }
  },
  {
    field: 'requestOrderMethod',
    headerText: i18n.t('独立/集中'),
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('独立'),
        2: i18n.t('集中')
      }
    }
  },
  {
    field: 'receiveStatus',
    headerText: i18n.t('收货状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未收货'),
        1: i18n.t('部分收货'),
        2: i18n.t('全部收货')
      }
    }
  },
  {
    field: 'warehouseCode',
    width: '250px',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.warehouseCode}}-{{data.warehouse}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    width: '150',
    field: 'subjectType',
    headerText: i18n.t('科目类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('成本中心'),
        1: i18n.t('销售订单'),
        2: i18n.t('生产工单'),
        3: i18n.t('项目'),
        4: i18n.t('资产'),
        5: i18n.t('其他')
      }
    }
  },
  {
    field: 'orderTime',
    headerText: i18n.t('订单日期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'purTenantName',
    headerText: i18n.t('客户名称'),
    width: '150'
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('客户业务类型'),
    width: '150'
  },
  {
    field: 'purchasingCycleStart',
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d H:i')
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'purchasingCycleEnd',
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d H:i')
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'requiredDeliveryDate',
    headerText: i18n.t('要求交期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'buyerOrgName',
    headerText: i18n.t('采购组名称'),
    width: '250',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'buyerGroupName',
    headerText: i18n.t('采购组织名称'),
    width: '250',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerGroupCode}}-{{data.buyerGroupName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    width: 225,
    field: 'siteName',
    headerText: i18n.t('地点/工厂'), // 也得是个按钮，点击出弹窗
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.siteCode}}-{{data.siteName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类'),
    width: 250,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.categoryCode}}-{{data.categoryName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'unitName',
    headerText: i18n.t('基本单位'),
    width: 250,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.unitCode}}-{{data.unitName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'purUnitName',
    headerText: i18n.t('采购单位'),
    width: 250,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.purUnitCode}}-{{data.purUnitName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'consignee',
    headerText: i18n.t('收货人'),
    width: '150',
    valueAccessor: function (field, data) {
      if (data?.consignee) {
        if (data.consignee.split('-')[3]) {
          return data?.consignee.split('-')[3]
        } else {
          return data?.consignee
        }
      } else {
        return ''
      }
    }
  },
  {
    field: 'techContactPerson',
    headerText: i18n.t('技术对接人'),
    width: '150',
    valueAccessor: function (field, data) {
      if (data?.techContactPerson) {
        if (data.techContactPerson.split('-')[3]) {
          return data?.techContactPerson.split('-')[3]
        } else {
          return data?.techContactPerson
        }
      } else {
        return ''
      }
    }
  },
  {
    field: 'budgetQuantity',
    headerText: i18n.t('预测采购量'),
    editType: 'numericedit'
  },
  {
    field: 'startDate',
    headerText: i18n.t('预测采购周期起'),
    editType: 'datePickerEdit',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        e = Number(e)
        return UTILS.dateFormat(e)
      }
    }
  },
  {
    field: 'endDate',
    headerText: i18n.t('预测采购周期止'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        e = Number(e)
        return UTILS.dateFormat(e)
      }
    }
  },
  {
    field: 'confirmStatus',
    headerText: i18n.t('供应商确认状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('待确认'),
        1: i18n.t('反馈异常'),
        2: i18n.t('反馈正常')
      }
    }
  },
  {
    width: '150',
    field: 'provisionalEstimateStatus',
    headerText: i18n.t('是否暂估价'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('否'),
        1: i18n.t('是')
      }
    }
  },
  {
    width: '150',
    field: 'returnIdentification',
    headerText: '退货标识 ',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('否'),
        1: i18n.t('是')
      }
    }
  },
  {
    field: 'warehouseStatus',
    headerText: i18n.t('入库状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未入库'),
        1: i18n.t('部分入库'),
        2: i18n.t('全部入库')
      }
    }
  },
  {
    field: 'buyerUserName',
    headerText: i18n.t('采购员'),
    width: '150'
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    width: '150'
  },
  {
    field: 'paymentName',
    headerText: i18n.t('付款条件'),
    width: '150'
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    width: '150'
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    width: '150'
  },
  {
    field: 'urgentStatus',
    headerText: i18n.t('加急状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('普通'),
        1: i18n.t('加急')
      }
    }
  },
  {
    field: 'urgentTime',
    headerText: i18n.t('加急时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'timePromise',
    headerText: i18n.t('承诺日期'),
    width: '150',
    // editTemplate: () => {
    //   return { template: DatePicker };
    // },
    template: () => {
      return { template: DatePicker }
    }
  },
  {
    field: 'supplierPromiseQty',
    headerText: i18n.t('承诺数量'),
    width: '150',
    // editTemplate: () => {
    //   return { template: DatePicker };
    // },
    template: () => {
      return { template: InputNumber }
    }
  },
  {
    field: 'supRemark',
    headerText: i18n.t('供方备注'),
    width: '150',
    template: () => {
      return { template: Input }
    }
  }
]
export const lastColumn = [
  // {
  //   width: "150",
  //   field: "costSharingAccName",
  //   headerText: "成本中心",
  // },
  {
    field: 'file',
    width: '250',
    headerText: i18n.t('附件'), // 只有编辑状态能修改、上传，否则只能展示
    allowEditing: false,
    // 使用template时，新增一行且未保存时，获取不到index（编辑状态，正常的template不会的）
    // 使用editTemplate时，显示的值不能是对象
    template: function () {
      return {
        template: cellFileView
      }
    }
  }
]
