<template>
  <div class="approval-config full-height">
    <mt-tabs
      :e-tab="false"
      :data-source="tabSource"
      v-permission="permissionObj"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <mt-template-page
      ref="template-0"
      :permission-obj="permissionObj"
      :template-config="componentConfig"
      v-if="tabIndex === 0"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
    <mt-template-page
      slot="slot-1"
      ref="templateRef2"
      v-if="tabIndex === 1"
      :template-config="pageConfig1"
      @handleClickToolBar="handleClickToolBar2"
    >
      <div slot="slot-filter" class="slot-select">
        <!-- 客户选择列表 -->
        <mt-select
          class="pur-tenant-select"
          :width="200"
          :data-source="businessTypeList"
          v-model="currentTabId"
          :open-dispatch-change="false"
          :show-clear-button="false"
          @change="setTypeList"
          :fields="{ value: 'purTenantId', text: 'title' }"
          placeholder=""
        ></mt-select>
        <!-- 对账单类型 -->
        <mt-tabs
          :e-tab="false"
          :data-source="tabTypeList"
          id="supReconTypeTabs"
          tab-id="supR-tab"
          class="supReconTypeTabs"
          :selected-item="currentTypeTabIndex"
          @handleSelectTab="handleSelectTypeTab"
        ></mt-tabs>
      </div>
    </mt-template-page>
  </div>
</template>

<script>
import * as CONFIG from './config/config'
import * as UTILS from '@/utils/utils'
import { editColumnBefore, editColumnEnd, columnData1 } from './config'
import { cloneDeep } from 'lodash'
export default {
  // components: {
  //   statusCheck: () =>
  //     import("@/components/businessComponents/statusCheck.vue"),
  // },
  data() {
    return {
      currentTabId: '',
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0197' },
          { dataPermission: 'b', permissionCode: 'T_02_0198' }
        ]
      },
      tabIndex: 0,
      tabSource: [
        {
          title: this.$t('单据视图')
        },
        {
          title: this.$t('明细视图')
        }
      ],
      componentConfig: [
        {
          dataPermission: 'a',
          permissionCode: 'T_02_0197',
          showArchive: true, // 是否显示归档查询
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          // title: this.$t("单据视图"),
          activatedRefresh: false,
          toolbar: [
            // {
            //   id: "Edit",
            //   icon: "icon_table_batchacceptance",
            //   title: this.$t("批量确认"),
            //   permission: ["O_02_0676"],
            // },
          ],
          gridId: this.$tableUUID.purchaseOrder.customReceiptTab,
          grid: {
            // height: "auto",
            frozenColumns: 1,
            allowEditing: true, //开启表格编辑操作
            columnData: CONFIG.columnData1,
            lineIndex: 1,
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/supOrder/queryNewType',
              ignoreDefaultSearch: this.$route.query.from === 'mytodo' ? true : false,

              rules:
                this.$route.query.from === 'mytodo'
                  ? [
                      {
                        field: 'feedbackStatus',

                        operator: 'in',
                        value: ['0']
                      }
                    ]
                  : []
            }
          }
        }
        // {
        //   dataPermission: "b",
        //   permissionCode: "T_02_0198",
        //   activatedRefresh: false,

        //   title: this.$t("明细视图"),
        // },
      ],
      businessTypeList: [],
      pageConfig1: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          showArchive: true, // 是否显示归档查询
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'Export1',
              icon: 'icon_solid_export',
              title: this.$t('导出'),
              permission: ['O_02_1405']
            }
          ],
          gridId: '',
          grid: {
            columnData: [
              {
                width: '50',
                type: 'checkbox',
                showInColumnChooser: false
              }
            ],
            dataSource: [],
            frozenColumns: 1
          }
        }
      ],
      tabTypeList: [],
      currentTypeTabIndex: 0,
      nowbusinessTypeCode: '',
      nowpurTenantId: ''
    }
  },
  mounted() {
    this.getBusinessConfig()

    if (this.$route.query.from === 'mytodo') {
      this.componentConfig[0].grid.asyncConfig.defaultRules = JSON.parse(
        sessionStorage.getItem('todoDetail')
      ).defaultRules
    }
    const a = Array.from(document.querySelectorAll('li'))
    for (let i = 0; i < a.length; i++) {
      if (a[i].getAttribute('permissioncode')) {
        for (let j = 0; j < this.contentConfig.length; j++) {
          console.log('getTabsIndex', a[i].getAttribute('permissioncode'), this.contentConfig[0])
          if (a[i].getAttribute('permissioncode') == this.contentConfig[j].permissionCode) {
            console.log('getTabsIndex2', this.contentConfig)
            this.currentTabIndex = j
            return
          }
        }
      }
    }
  },
  methods: {
    // 顶部筛选 调整
    handleChose(e, item) {
      this.getColumnModule(item.itemCode, e, item.businessTypeId, item.purTenantId)
    },
    //重新赋值asynConfig
    purOrderDetail(e, purTenantId, businessTypeId) {
      console.log(this.pageConfig1[0].grid.columnData)
      console.log(e, '我执行了阿')
      // this.pageConfig1[0].grid.columnData.forEach((item) => {});
      this.nowbusinessTypeCode = e
      this.nowpurTenantId = purTenantId
      this.$set(this.pageConfig1[0].grid, 'asyncConfig', {
        url: '/srm-purchase-execute/tenant/so/detail/queryNewType',
        ignoreDefaultSearch: this.$route.query.from === 'mytodo' ? true : false,
        rules:
          this.$route.query.from === 'mytodo'
            ? [
                {
                  field: 'feedbackStatus',

                  operator: 'in',
                  value: ['0']
                }
              ]
            : [],
        defaultRules: [
          {
            condition: 'and',
            field: 'businessTypeCode',
            operator: 'equal',
            value: e
          },
          {
            condition: 'and',
            field: 'purTenantId',
            operator: 'equal',
            value: purTenantId
          }
        ],
        serializeList: (list) => {
          list.forEach((item) => {
            item.fieldDataList = item.fieldDataList || []
            item.fieldDataList.forEach((item1) => {
              //对应的动态字段 需求名称,需求描述,资产类别,资产编号,资产卡片,客户,关联客户订单
              item[item1.fieldCode] = item1.fieldData
            })
            if (item.costs) {
              item.orderCosts = `${item.costs[0].costCenterCode}-${item.costs[0].costCenterName}`
            }
            item.version1 = item.version
            if (item.taxTotal < 0) {
              item.taxTotal = ''
            }
            if (item.taxPrice < 0) {
              item.taxPrice = ''
            }

            if (item.freeTotal < 0) {
              item.freeTotal = ''
            }
            if (item.freePrice < 0) {
              item.freePrice = ''
            }
            if (item.unPrice < 0) {
              item.unPrice = ''
            }
            if (item.unTotal < 0) {
              item.unTotal = ''
            }
            if (item.approvedTotalPrice < 0) {
              item.approvedTotalPrice = ''
            }
            if (item.budgetTotalPrice < 0) {
              item.budgetTotalPrice = ''
            }
            if (item.budgetUnitPrice < 0) {
              item.budgetUnitPrice = ''
            }
            if (item.taxedTotalPrice < 0) {
              item.taxedTotalPrice = ''
            }
            if (item.taxedUnitPrice < 0) {
              item.taxedUnitPrice = ''
            }
            if (item.subjectTotal < 0) {
              item.subjectTotal = ''
            }
          })
          return list
        }
      })
      this.pageConfig1[0].gridId = this.$md5(
        this.$tableUUID.purchaseOrder.customReceiptDetailTab + businessTypeId + purTenantId
      )
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    //获取动态表头
    getColumnModule(code, e, businessTypeId, purTenantId) {
      let params = {
        businessType: businessTypeId,
        docType: 'so',
        moduleType: 6,
        tenantId: purTenantId
      }
      this.$API.purchaseOrder.getbusinessModuleFields(params).then((res) => {
        let flag = false
        if (res.data && res.data.length) {
          let _columnData = cloneDeep(editColumnBefore)
          let _columnData1 = cloneDeep(editColumnEnd)
          let _columnData2 = cloneDeep(columnData1)
          this.pageConfig1[0].grid.columnData = _columnData
            .concat(
              res.data.map((item1) => {
                let obj = {}
                let hasItem = false
                _columnData2.some((item2) => {
                  if (item2.field === item1.fieldCode) {
                    obj = {
                      ...item2,
                      headerText: item1.fieldName
                    }
                    hasItem = true
                  }
                })
                if (hasItem) {
                  return obj
                } else {
                  return {
                    ...item1,
                    headerText: item1.fieldName,
                    field: item1.fieldCode
                  }
                }
              })
            )
            .concat(_columnData1)
          flag = true
        }

        if (!flag) {
          let _columnData = cloneDeep(editColumnBefore)
          let _columnData1 = cloneDeep(editColumnEnd)
          this.pageConfig1[0].grid.columnData = _columnData.concat(_columnData1)
        }
        this.purOrderDetail(code, purTenantId, businessTypeId)
      })
    },
    // 获取业务类型下拉
    getBusinessConfig() {
      this.$API.purchaseOrder.getSupOrderQueryTab().then((res) => {
        this.businessTypeList = []
        let list = res.data || []
        let nowList = []
        list.forEach((item) => {
          nowList.push({
            title: item.purTenantName,
            purTenantId: item.purTenantId,
            tabs: item.tabs
          })
        })
        this.businessTypeList = nowList
        if (this.businessTypeList.length) {
          this.setTypeList({ value: list[0].purTenantId })
        }
      })
    },
    handleSelectTypeTab(e) {
      console.log(e, '选择的tab改变')
      this.currentTypeTabIndex = e
      // this.tabTypeList.forEach();
      if (this.tabTypeList.length) {
        let code = this.tabTypeList[e].code
        let businessTypeId = this.tabTypeList[e].businessTypeId
        this.getColumnModule(code, businessTypeId, businessTypeId, this.currentTabId)
      }
    },
    setTypeList(val) {
      console.log(val.value, '我是选择改变了')
      if (!val.value) return
      let purTenantId = val.value
      this.currentTabId = purTenantId
      this.currentTypeTabIndex = 0
      this.tabTypeList = []
      this.currentTabIndex = this.businessTypeList.findIndex(
        (i) => i.purTenantId == this.currentTabId
      )
      console.log(this.currentTabIndex, '我是第几个')
      this.businessTypeList[this.currentTabIndex].tabs.forEach((item) => {
        this.tabTypeList.push({
          title: item.businessTypeName,
          code: item.businessTypeCode,
          businessTypeId: item.businessTypeId
        })
      })
      if (this.tabTypeList.length) {
        let code = this.tabTypeList[0].code
        let businessTypeId = this.tabTypeList[0].businessTypeId
        this.getColumnModule(code, businessTypeId, businessTypeId, this.currentTabId)
      }
    },
    //点击顶部的操作按钮
    handleClickToolBar2(e) {
      console.log('方法1', e)
      console.log(e.gridRef.getMtechGridRecords(), e.toolbar.id)
      if (e.toolbar.id === 'Export1') {
        this.handleExport()
        return
      }
    },
    handleExport() {
      //导出
      let rule = this.$refs.templateRef2.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      console.log(rule)
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 5000 },
        pageFlag: true,
        rules: rule.rules
          ? [
              {
                condition: 'and',
                field: 'businessTypeCode',
                operator: 'equal',
                value: this.nowbusinessTypeCode
              },
              {
                condition: 'and',
                field: 'purTenantId',
                operator: 'equal',
                value: this.nowpurTenantId
              },
              ...rule?.rules
            ]
          : [
              {
                condition: 'and',
                field: 'businessTypeCode',
                operator: 'equal',
                value: this.nowbusinessTypeCode
              },
              {
                condition: 'and',
                field: 'purTenantId',
                operator: 'equal',
                value: this.nowpurTenantId
              }
            ]
      }

      if (rule?.rules?.length) {
        params.rules = params.rules || [].concat(rule.rules)
      }
      this.$store.commit('startLoading')

      this.$API.purchaseOrder.newModeSoDetailDownload(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    //点击顶部的操作按钮
    handleClickToolBar(e) {
      console.log('方法1', e)
      console.log(e.gridRef.getMtechGridRecords(), e.toolbar.id)

      if (e.toolbar.id === 'Edit') {
        let _selectRows = e.gridRef.getMtechGridRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        let _id = []
        _selectRows.map((item) => _id.push(item.id))
        console.log(_id)
        this.$dialog({
          data: {
            title: this.$t('批量确认'),
            message: this.$t('确认批量确认选中的订单？')
          },
          success: () => {
            let params = _id
            this.$API.purchaseOrder.supOrderbatchFeedback(params).then((res) => {
              if (res.data) {
                this.$toast({
                  content: this.$t('批量确认操作成功'),
                  type: 'success'
                })
                this.$refs[`template-0`].refreshCurrentGridData()
              } else {
                this.$toast({
                  content: this.$t('批量确认失败'),
                  type: 'error'
                })
              }
            })
          }
        })
      }
    },
    //点击表格的操作按钮
    handleClickCellTool(e) {
      console.log('方法2', e)
      // 点击编辑跳转详情（提交）
      if (e.tool.id === 'edit') {
        this.$router.push({
          name: 'new-mode-custom-receipt-detail',
          query: {
            id: e.data.id,
            type: '1'
          }
        })
      }
    },
    //点击表格数据的操作方法
    handleClickCellTitle(e) {
      console.log('方法3', e)
      //点击采购订单号跳转到详情（仅查看）
      if (e.field === 'orderCode') {
        this.$router.push({
          name: 'new-mode-custom-receipt-detail',
          query: {
            id: e.data.id,
            type: '3'
          }
        })
      }
      //历史反馈
      if (e.field === 'version') {
        this.$router.push({
          name: 'new-mode-custom-receipt-detail',
          query: {
            id: e.data.id,
            type: '2',
            orderCode: e.data.orderCode,
            tenantId: e.data.purTenantId
          }
        })
      }
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('todoDetail')
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  // /deep/ .top-filter {
  //   height: 40px;
  //   padding: 0 20px;
  //   display: flex;
  //   margin-bottom: 10px;
  //   .left-status {
  //     overflow: hidden;
  //     margin-top: 10px;
  //     display: flex;
  //     align-items: center;
  //     .tit {
  //       white-space: nowrap;
  //     }
  //   }
  // }
  /deep/ .slot-select {
    display: flex;
    position: relative;
    align-items: center;
  }
  .pur-tenant-select {
    margin: 25px;
    margin-bottom: 0;
    margin-top: 17px;
  }
  /deep/ .supReconTypeTabs {
    flex: 1;
    display: flex;
    margin-top: 17px;

    .mt-tabs-container {
      width: 100%;
    }
  }
}
</style>
