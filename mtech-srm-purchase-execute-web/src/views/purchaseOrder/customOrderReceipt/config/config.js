import Vue from 'vue'
import UTILS from '@/utils/utils'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'

export const columnData1 = [
  {
    field: 'id',
    headerText: 'id',
    isPrimaryKey: true,
    allowEditing: false,
    visible: false
  },
  {
    field: 'contractNo',
    width: '150',
    headerText: i18n.t('合同编号')
  },
  {
    field: 'orderCode',
    headerText: i18n.t('采购订单号'),
    width: '150',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('反馈'),
        permission: ['O_02_0138'],
        visibleCondition: (data) => {
          return data.feedbackStatus == 0 && data.status !== 4 && data.status !== 5
        }
      }
    ],
    searchOptions: {
      placeholder: i18n.t('多个订单号需要用英文逗号隔开'),
      operator: 'in',
      serializeValue: (e) => {
        let obj = e.split(',')
        //自定义搜索值，规则
        return obj
      },
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'projectName',
    headerText: i18n.t('项目名称'),
    width: '130'
  },
  {
    field: 'version',
    headerText: i18n.t('历史反馈'),
    width: '100',
    cellTools: [],
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return e - 1 === 0 ? '' : e - 1
      }
    }
  },
  {
    field: 'status',
    width: '105',
    headerText: i18n.t('订单形式'),
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = ''
              if (this.data.status === 4) {
                str = i18n.t('订单关闭')
              }
              if (this.data.status === 5) {
                str = i18n.t('订单完成')
              }
              if (this.data.status === 2 && this.data.purVersion === 1) {
                str = i18n.t('新订单')
              }
              if (this.data.status === 2 && this.data.purVersion > 1) {
                str = i18n.t('订单变更')
              }
              return str
            }
          }
        })
      }
    },
    searchOptions: {
      elementType: 'multi-select',
      operator: 'in',
      multiple: true,

      dataSource: [
        { label: '新订单', value: 1 },
        { label: '订单变更', value: 2 },
        { label: '订单完成', value: 3 },
        { label: '订单关闭', value: 4 }
      ],
      fields: { text: 'label', value: 'value' }
    }
  },
  {
    field: 'urgentStatus',
    headerText: i18n.t('加急状态'),
    width: '150',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in',
      dataSource: [
        { label: '普通', value: 0 },
        { label: '加急', value: 1 }
      ],
      fields: { text: 'label', value: 'value' }
    },
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div>
              <span :style="urgentStatus === '加急' ? {color: 'red'} : {}">{{ urgentStatus }}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          },
          computed: {
            urgentStatus() {
              return this.data.urgentStatus === 0 ? i18n.t('普通') : i18n.t('加急')
            }
          }
        })
      }
    }
  },

  {
    field: 'urgentTime',
    headerText: i18n.t('加急日期'),
    width: '150',

    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'feedbackStatus',
    headerText: i18n.t('供应商确认状态'),
    width: '150',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: [
        {
          value: 0,
          text: i18n.t('待确认'),
          cssClass: 'col-active'
        },
        {
          value: 2,
          text: i18n.t('反馈正常'),
          cssClass: 'col-active'
        },
        {
          value: 1,
          text: i18n.t('反馈异常'),
          cssClass: 'col-error'
        }
      ]
    }
  },
  {
    field: 'deliveryStatus',
    headerText: i18n.t('发货状态'),
    width: '100',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未发货'),
        1: i18n.t('部分发货'),
        2: i18n.t('全部发货')
      }
    }
  },
  {
    field: 'receiveStatus',
    headerText: i18n.t('收货状态'),
    width: '100',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未收货'),
        1: i18n.t('部分收货'),
        2: i18n.t('全部收货')
      }
    }
  },

  {
    field: 'orderTime',
    headerText: i18n.t('订单日期'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    width: '100',

    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    }
  },
  // {
  //   field: "purTenantName",
  //   headerText: i18n.t("客户名称"),
  //   width: "150",
  // },
  {
    field: 'companyCode',
    headerText: i18n.t('客户公司'),
    width: '230',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.companyCode}}-{{data.companyName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('客户业务类型'),
    width: '150'
  },
  // {
  //   field: "requiredDeliveryDate",
  //   headerText: i18n.t("要求交期"),
  //   width: "150",
  //   valueConverter: {
  //     type: "function",
  //     filter: (e) => {
  //       if (e && e.length === 13) {
  //         e = Number(e);
  //         return UTILS.dateFormat(e, "Y-m-d");
  //       } else {
  //         return "";
  //       }
  //     },
  //   },
  // },
  {
    field: 'buyerOrgName',
    headerText: i18n.t('采购组名称'),
    width: '180',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'buyerGroupName',
    headerText: i18n.t('采购组织名称'),
    width: '180',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerGroupCode}}-{{data.buyerGroupName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'buyerUserName',
    headerText: i18n.t('采购员'),
    width: '85'
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    width: '120'
  },
  {
    field: 'paymentName',
    headerText: i18n.t('付款条件'),
    width: '150'
  },
  {
    field: 'supSalesOrderNo',
    headerText: i18n.t('供方销售订单号'),
    width: '160'
  },
  {
    field: 'syncSupSysStatus',
    headerText: i18n.t('同步供方系统状态'),
    width: '160',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('成功'),
        2: i18n.t('失败')
      }
    }
  },
  {
    field: 'syncSupSysFailDesc',
    headerText: i18n.t('同步供方系统失败原因'),
    width: '160'
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    width: '62'
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    width: '52'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    width: '150',
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    }
  }
]
export const columnData2 = [
  {
    field: 'col1',
    headerText: i18n.t('采购订单号'),
    width: '150',
    cellTools: [],
    searchOptions: {
      placeholder: i18n.t('多个订单号需要用英文逗号隔开'),
      operator: 'in',
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'col16',
    headerText: i18n.t('历史反馈'),
    width: '100',
    cellTools: []
  },
  {
    field: 'orderType',
    headerText: i18n.t('订单形式'),
    width: '105',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('新订单'),
        2: i18n.t('订单变更'),
        3: i18n.t('订单取消')
      }
    }
  },

  {
    field: 'col2',
    headerText: '发货状态',
    width: '100'
  },
  {
    field: 'col3',
    headerText: '收货状态',
    width: '100'
  },
  {
    field: 'col4',
    headerText: '订单日期',
    width: '100'
  },
  {
    field: 'prototypeMachineCode',
    headerText: '原型机编码',
    width: '100'
  },
  {
    field: 'col5',
    headerText: '客户名称',
    width: '105'
  },
  {
    field: 'col6',
    headerText: '客户业务类型',
    width: '150'
  },
  {
    field: 'col7',
    headerText: '要求交期',
    width: "100'"
  },
  {
    field: 'col8',
    headerText: '采购组织名称',
    width: '180'
  },
  {
    field: 'col9',
    headerText: '采购组名称',
    width: '180'
  },
  {
    field: 'col10',
    headerText: '采购员',
    width: '85'
  },
  {
    field: 'col11',
    headerText: '币种',
    width: '120'
  },
  {
    field: 'col12',
    headerText: '付款条件',
    width: '150'
  },
  {
    field: 'col13',
    headerText: '备注',
    width: '100'
  },
  {
    field: 'col14',
    headerText: '更新人',
    width: '100'
  },
  {
    field: 'col15',
    headerText: '更新时间',
    width: '150'
  }
]
export const dataSource2 = [
  {
    id: '1',
    orderType: '1',
    orderTypeStr: '新订单',
    col1: '1234',
    col2: '未发货',
    col3: '未收货',
    col4: '2021.08.30',
    col5: '恒力液压有限公司',
    col6: '生产采购',
    col7: '2021.12.05',
    col8: '采购组织111',
    col9: '采购组1',
    col10: '王小二',
    col11: '人民币',
    col12: '90天后付款',
    col13: '备注',
    col14: '刘飞',
    col15: '2021.12.05',
    col16: ''
  },
  {
    id: '2',
    orderType: '2',
    orderTypeStr: '订单变更',
    col1: '123',
    col2: '部分发货',
    col3: '部分收货',
    col4: '2021.08.30',
    col5: '恒力液压有限公司',
    col6: '生产采购',
    col7: '2021.12.05',
    col8: '采购组织111',
    col9: '采购组1',
    col10: '王小二',
    col11: '人民币',
    col12: '90天后付款',
    col13: '备注2',
    col14: '刘飞1',
    col15: '2021.12.05',
    col16: '哈哈'
  },
  {
    id: '3',
    orderType: '3',
    orderTypeStr: '订单取消',
    col1: '123',
    col2: '未发货',
    col3: '未收货',
    col4: '2021.08.30',
    col5: '恒力液压有限公司',
    col6: '生产采购',
    col7: '2021.12.05',
    col8: '采购组织111',
    col9: '采购组1',
    col10: '王小二',
    col11: '人民币',
    col12: '90天后付款',
    col13: '备注',
    col14: '刘飞',
    col15: '2021.12.05',
    col16: '1'
  }
]
