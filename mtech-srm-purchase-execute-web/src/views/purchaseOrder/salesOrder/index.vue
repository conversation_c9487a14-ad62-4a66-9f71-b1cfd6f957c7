<!-- 供方-销售订单查询 -->
<template>
  <mt-template-page
    ref="templateRef"
    :template-config="pageConfig"
    :current-tab="currentTab"
    @handleSelectTab="handleSelectTab"
  >
    <List slot="slot-0" />
    <Detail slot="slot-1" />
  </mt-template-page>
</template>

<script>
export default {
  components: {
    List: () => import('./pages/List.vue'),
    Detail: () => import('./pages/Detail.vue')
  },
  data() {
    return {
      pageConfig: [{ title: this.$t('列表视图') }, { title: this.$t('明细视图') }],
      currentTab: 0
    }
  },
  methods: {
    handleSelectTab(e) {
      this.currentTab = e
    }
  }
}
</script>
