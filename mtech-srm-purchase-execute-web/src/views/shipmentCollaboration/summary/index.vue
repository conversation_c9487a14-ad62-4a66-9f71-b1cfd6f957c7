<template>
  <!-- 发货协同-列表 -->
  <div class="full-height pt20">
    <mt-template-page
      :template-config="pageConfig"
      ref="templateRef"
      @handleSelectTab="handleSelectTab"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    >
      <!-- 待拣货订单 筛选 -->
      <div slot="slot-filter" class="top-filter" v-show="isShowPendingOrder">
        <div class="left-status">
          <span>{{ $t('客户：') }}</span>
          <status-check
            v-if="purTenantOptions.length > 0"
            ref="tab0PurTenantCheckRef"
            :status-data="purTenantOptions"
            @handleChose="tab0PurTenantChose"
          ></status-check>
        </div>
      </div>
      <!-- 待拣货明细 筛选 -->
      <div slot="slot-1" class="top-filter">
        <div class="left-status">
          <span>{{ $t('客户：') }}</span>
          <status-check
            v-if="purTenantOptions.length > 0"
            ref="tab1PurTenantCheckRef"
            :status-data="purTenantOptions"
            @handleChose="tab1PurTenantChose"
          ></status-check>
        </div>
        <div class="left-status itme-status">
          <span>{{ $t('业务类型：') }}</span>
          <status-check
            ref="tab1BusinessTypeCheckRef"
            :status-data="businessTypeList"
            @handleChose="tab1BusinessTypeChose"
          ></status-check>
        </div>
      </div>
      <!-- 待拣货明细 表格 -->
      <div slot="slot-1" class="full-height">
        <!-- :frozen-columns="1" -->
        <mt-data-grid
          v-if="detailsToBePickedColumnData && detailsToBePickedColumnData.length > 0"
          @cellEdit="detailsToBePickedCellEdit"
          ref="dataGrid"
          class="custom-toolbar-grid"
          :data-source="detailsToBePickedDataSource"
          :column-data="detailsToBePickedColumnData"
          :toolbar="detailsToBePickedToolbar"
          :toolbar-click="toolbarClick"
          :allow-sorting="true"
          :allow-filtering="true"
          :filter-settings="filterOptions"
          :allow-reordering="true"
          :allow-paging="true"
          :page-settings="detailsToBePickedPageSettings"
          @currentChange="detailsToBePickedCurrentChange"
          @sizeChange="detailsToBePickedSizeChange"
        ></mt-data-grid>
      </div>
    </mt-template-page>
    <!-- 待妥投-妥投 弹框 -->
    <proper-vote-dialog
      ref="properVoteDialog"
      @refreshColumns="refreshColumns"
    ></proper-vote-dialog>
    <!-- 发货 弹框 -->
    <ship-dialog ref="shipDialog" @refreshColumns="refreshColumns"></ship-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import {
  PendingOrderCol,
  DetailsToBePickedCol1,
  DetailsToBePickedCol2,
  ToBeDeliveredCol,
  PendingCol,
  DeliveryRecordCol,
  ColumnDataMock,
  formatColumnToGrid
} from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'

export default {
  components: {
    // 妥投 弹框
    ProperVoteDialog: () => import('./components/properVoteDialog'),
    // 发货、创建送货单并发货 弹框
    ShipDialog: () => import('./components/shipDialog'),
    StatusCheck: require('@/components/businessComponents/statusCheck.vue').default
  },
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      tab0PurTenantChosen: '', // 待拣货订单 状态选择 客户
      tab1PurTenantChosen: '', // 待拣货明细 状态选择 客户
      tab1BusinessTypeChosen: '', // 待拣货明细 状态选择 业务类型
      isShowPendingOrder: true, // 初始 tab 在 待拣货订单
      purTenantOptions: [], // 供方发货客户 下拉选项
      businessTypeList: [], // 业务类型下拉
      detailsToBePickedDataSource: [], // 待拣货明细 数据
      detailsToBePickedColumnData: [], // 待拣货明细 表格列
      detailsToBePickedToolbar: [
        {
          text: this.$t('创建送货单'),
          tooltipText: this.$t('创建送货单'),
          prefixIcon: 'icon_table_delivery_note',
          id: 'CreatePickList'
        },
        {
          text: this.$t('创建送货单并发货'),
          tooltipText: this.$t('创建送货单并发货'),
          prefixIcon: 'icon_table_delivery',
          id: 'CreateDeliveryNote'
        }
      ],
      filterOptions: {
        type: 'Menu'
      },
      detailsToBePickedPageSettings: {
        pageSize: 10, // 当前每页数据量
        totalRecordsCount: 0, // 总条数
        pageSizes: [10, 20, 30]
      },
      detailsToBePickedCurrentPage: 1, // 待拣货明细 当前页码
      editData: [], // 行编辑过的数据
      pageConfig: [
        // 待拣货订单
        {
          tab: { title: this.$t('待拣货订单') },
          toolbar: [],
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: PendingOrderCol,
            dataSource: [],
            frozenColumns: 1
          }
        },
        // 待拣货明细
        {
          tab: { title: this.$t('待拣货明细') }
        },
        // 待发货
        {
          tab: { title: this.$t('待发货') },
          toolbar: [
            {
              id: 'ToBeDeliveredShip',
              icon: 'icon_table_delivery',
              title: this.$t('发货')
            }
          ],
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: ToBeDeliveredCol,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/so/delivery/query`,
              defaultRules: [
                {
                  field: 'status',
                  type: 'number',
                  operator: 'equal',
                  value: '0'
                }
              ]
            },
            frozenColumns: 1
          }
        },
        // 待妥投
        {
          tab: { title: this.$t('待妥投') },
          toolbar: [],
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: PendingCol,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/so/delivery/query`,
              defaultRules: [
                {
                  field: 'proper_status',
                  type: 'number',
                  operator: 'equal',
                  value: '0'
                },
                {
                  field: 'status',
                  type: 'number',
                  operator: 'equal',
                  value: '1'
                }
              ]
            },
            frozenColumns: 1
          }
        },
        // 发货记录
        {
          tab: { title: this.$t('发货记录') },
          toolbar: [
            {
              id: 'ReturnShipping',
              icon: 'icon_table_return',
              title: this.$t('退回发货')
            }
          ],
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: DeliveryRecordCol,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/so/delivery/query`,
              defaultRules: [
                {
                  field: 'status',
                  type: 'number',
                  operator: 'equal',
                  value: '1'
                }
              ]
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    // 获取筛选项并获取
    async init() {
      // 获取 供方发货客户 下拉选项
      await this.getPurTenantOptions()
      // 获取业务类型
      await this.getBusinessConfig()
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 点击 tab
    handleSelectTab(e) {
      this.editData = [] // 清空编辑数据池
      this.isShowPendingOrder = false
      // 0: 待拣货订单 1: 待拣货明细 2: 待发货 3: 待妥投 4: 发货记录
      if (e == 0) {
        this.isShowPendingOrder = true
        if (!this.purTenantOptions || this.purTenantOptions.length === 0) {
          this.getPurTenantOptions()
        }
      } else if (e == 1) {
        if (!this.businessTypeList || this.businessTypeList.length === 0) {
          this.getBusinessConfig()
        } else {
          // 获取 待拣货明细 表格数据
          this.getDetailsToBePickedTableData()
        }
      }
    },
    // 待拣货明细 行数据编辑
    detailsToBePickedCellEdit(e) {
      // 存行编辑的数据
      if (this.detailsToBePickedDataSource[e.index].id) {
        let currentEditIndex
        const editItem = this.editData.find((item, index) => {
          currentEditIndex = index
          return item.id == this.detailsToBePickedDataSource[e.index].id
        })
        if (editItem) {
          // 更新编辑的数据
          editItem[e.key] = e.value
          this.editData.splice(currentEditIndex, 1, editItem)
        } else {
          // 保存编辑的数据
          if (e.value) {
            this.editData.push({
              id: this.detailsToBePickedDataSource[e.index].id, // 行数据id
              [e.key]: e.value, // 编辑的字段和值
              key: e.key // 编辑的字段
            })
          }
        }
      }
      // 更新 detailsToBePickedDataSource
      let _dataSource = cloneDeep(this.detailsToBePickedDataSource)
      _dataSource[e.index][e.key] = e.value
      this.detailsToBePickedDataSource = _dataSource
    },
    toolbarClick(e) {
      const selectedRecords = this.$refs.dataGrid.ejsRef.getSelectedRecords()
      if (selectedRecords.length == 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.item.id == 'CreateDeliveryNote') {
        // 待拣货明细-创建送货单
        this.handleCreateDeliveryNote(selectedRecords)
      } else if (e.item.id == 'CreatePickList') {
        // 待拣货明细-创建拣货单
        this.handleCreatePickList(selectedRecords)
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = e.gridRef.getMtechGridRecords()
      const commonToolbar = [
        'Add',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]
      if (selectedRecords.length == 0 && !commonToolbar.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const tmepIdList = []
      selectedRecords.forEach((item) => {
        tmepIdList.push(item.id)
      })

      if (e.toolbar.id == 'ToBeDeliveredShip') {
        // 待发货-发货
        this.handleToBeDeliveredShip(tmepIdList)
      } else if (e.toolbar.id == 'ReturnShipping') {
        // 发货记录-退回发货
        this.handleReturnShipping(tmepIdList)
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'Picking') {
        // 待拣货订单-拣货
        this.$router.push({
          name: 'pending-order-row',
          query: {
            type: '0',
            planId: e.data.planId,
            orderId: e.data.orderId
          }
        })
      } else if (e.tool.id == 'PendingOrderShip') {
        // 待拣货订单-发货
        this.$router.push({
          name: 'pending-order-row',
          query: {
            type: '1',
            planId: e.data.planId,
            orderId: e.data.orderId
          }
        })
      } else if (e.tool.id == 'ToBeDeliveredShip') {
        // 待发货-发货
        this.handleToBeDeliveredShip([e.data.id])
      } else if (e.tool.id == 'ToBeDeliveredClose') {
        // 待发货-关闭
        this.handleToBeDeliveredClose([e.data.id])
      } else if (e.tool.id == 'ProperVote') {
        // 待妥投-妥投
        this.$refs.properVoteDialog.dialogInit({
          rowData: e.data
        })
      } else if (e.tool.id == 'ReturnShipping') {
        // 发货记录-退回发货
        this.handleReturnShipping([e.data.id])
      }
    },
    // 整理拣货信息
    picksData(selectedData) {
      const data = selectedData
      const tmp = []
      data.forEach((item) => {
        if (item.qty > 0) {
          tmp.push({
            orderItemNo: item.orderItemNo, // 供方订单明细行号
            planItemNo: item.itemNo, // 供货计划明细行号
            orderCode: item.orderCode, // 供方订单号
            planId: item.planId, // 供货计划主单id
            planItemId: item.id, // 供货计划明细id
            purTenantId: item.purTenantId, // 客户租户id
            qty: item.qty, // 发货数量
            supplierOrderDetailId: item.orderDetailId, // 供方订单明细id
            supplierOrderId: item.orderId // 供方订单id
          })
        }
      })

      return tmp
    },
    // 待拣货明细-创建送货单
    handleCreateDeliveryNote(selectedData) {
      const picks = this.picksData(selectedData)
      if (picks.length === 0) {
        this.$toast({ content: this.$t('未填写发货数量'), type: 'warning' })
        return
      }
      // picks 拣货信息
      this.$refs.shipDialog.dialogInit({
        dialogTitle: this.$t('创建送货单并发货'),
        selectData: {
          picks
        },
        actionType: 1
      })
    },
    // 待拣货明细-创建拣货单
    handleCreatePickList(selectedRecords) {
      // 供方拣货
      const picks = this.picksData(selectedRecords)
      if (picks.length === 0) {
        this.$toast({ content: this.$t('未填写发货数量'), type: 'warning' })
        return
      }
      const params = {
        picks // 拣货信息
      }
      // 供方拣货
      this.apiStartLoading()
      this.$API.shipmentCollaboration
        .postSoPlanPicking(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
          }
          // 重新获取 待拣货明细 表格数据
          this.getDetailsToBePickedTableData()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 待发货-发货
    handleToBeDeliveredShip(idList) {
      this.$refs.shipDialog.dialogInit({
        dialogTitle: this.$t('发货'),
        selectData: {
          ids: idList
        },
        actionType: 0
      })
    },
    // 待发货-关闭
    handleToBeDeliveredClose(idList) {
      this.apiStartLoading()
      this.$API.shipmentCollaboration
        .postSoDeliveryClose({ deliveryIds: idList })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 发货记录-退回发货
    handleReturnShipping(idList) {
      this.apiStartLoading()
      this.$API.shipmentCollaboration
        .postSoDeliveryWithdraw({ deliveryBillIds: idList })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 待拣货订单 顶部筛选 客户
    tab0PurTenantChose(e) {
      this.tab0PurTenantChosen = e
      const parms = {
        tabNum: 0,
        url: `${BASE_TENANT}/so/plan/query`, // 获取供应商待拣货主单列表
        defaultRules: [
          {
            field: 'pur_tenant_id',
            operator: 'equal',
            value: this.tab0PurTenantChosen
          }
        ]
      }
      this.setAsynConfig(parms)
    },
    // 待拣货明细 顶部筛选 客户
    tab1PurTenantChose(e) {
      this.tab1PurTenantChosen = e
      // 重新获取 待拣货明细 表格数据
      this.getDetailsToBePickedTableData()
    },
    // 待拣货明细 顶部筛选 业务类型
    tab1BusinessTypeChose(e) {
      this.tab1BusinessTypeChosen = e
      // 获取 待拣货明细-订单明细行字段 并设置到表格中
      this.getTab1DetailsColumn()
      // 重新获取 待拣货明细 表格数据
      this.getDetailsToBePickedTableData()
    },
    // 顶部筛选选择后，重新赋值 asynConfig
    setAsynConfig(parms) {
      const { tabNum, url, rules } = parms
      this.$set(this.pageConfig[tabNum].grid, 'asyncConfig', {
        url: url,
        defaultRules: rules
      })
    },
    // 获取业务类型 下拉
    getBusinessConfig() {
      this.apiStartLoading()
      return this.$API.masterData
        .getDictCode({ dictCode: 'businessType' })
        .then((res) => {
          if (res.data && res.data.length) {
            const tmp = []
            res.data.forEach((item) => {
              tmp.push({
                label: item.itemName,
                value: item.id
              })
            })
            // this.businessTypeList 有值，触发 tab1BusinessTypeChose 方法
            this.businessTypeList = tmp
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方发货客户 下拉
    getPurTenantOptions() {
      this.apiStartLoading()
      return this.$API.shipmentCollaboration
        .getSoPlanPurTenant()
        .then((res) => {
          if (res && res.data) {
            const tmp = []
            res.data.forEach((item) => {
              tmp.push({
                label: item.purTenantName, // 客户租户id
                value: item.purTenantId // 客户租户名称
              })
            })
            // this.purTenantOptions 有值，触发 tab0PurTenantChose 方法
            this.purTenantOptions = tmp
            this.apiEndLoading()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 获取 待拣货明细-订单明细行字段
    getTab1DetailsColumn() {
      // 动态列
      const dynamicColumn = formatColumnToGrid(ColumnDataMock)
      this.detailsToBePickedColumnData = [].concat(
        DetailsToBePickedCol1,
        dynamicColumn,
        DetailsToBePickedCol2
      )
    },
    // 整合并设置列
    handleUnionColumn(columnData, tabNum) {
      // 动态列
      const dynamicColumn = formatColumnToGrid(columnData)
      let column = []
      // tabNum 0: 待拣货订单 1: 待拣货明细 2: 待发货 3: 待妥投 4: 发货记录
      if (tabNum === 1) {
        column = column.concat(DetailsToBePickedCol1, dynamicColumn, DetailsToBePickedCol2)
      }
      if (column.length > 0 && this.pageConfig[tabNum]) {
        // 设置新的 表格列
        this.$set(this.pageConfig[tabNum].grid, 'columnData', column)
      }
    },
    // 将行编辑过的数据设置到表格中
    setEditedData(data) {
      this.editData.forEach((itemEdit) => {
        for (let i = 0; i < data.length; i++) {
          if (data[i].id) {
            data[i][itemEdit.key] = itemEdit.value
            break
          }
        }
      })
      return data
    },
    // 表格数据 待拣货明细
    getDetailsToBePickedTableData() {
      if (this.tab1PurTenantChosen && this.tab1BusinessTypeChosen) {
        // 获取供应商待拣货明细列表
        this.apiStartLoading()
        this.$API.shipmentCollaboration
          .postSoPlanItemQuery({
            page: {
              current: this.detailsToBePickedCurrentPage,
              size: this.detailsToBePickedPageSettings.pageSize
            },
            defaultRules: [
              {
                field: 'pur_tenant_id',
                operator: 'equal',
                value: this.tab1PurTenantChosen
              },
              {
                field: 'business_type_id',
                operator: 'equal',
                value: this.tab1BusinessTypeChosen
              }
            ]
          })
          .then((res) => {
            if (res?.data?.records) {
              this.detailsToBePickedPageSettings.totalRecordsCount = res.data.total // 总数
              // 将行编辑过的数据设置到表格中
              this.detailsToBePickedDataSource = this.setEditedData(res.data.records)
            }
            this.apiEndLoading()
          })
          .catch(() => {
            this.apiEndLoading()
          })
      }
    },
    // 待拣货明细 页码修改
    detailsToBePickedCurrentChange(pageIndex) {
      this.detailsToBePickedCurrentPage = pageIndex
      // 重新获取 待拣货明细 表格数据
      this.getDetailsToBePickedTableData()
    },
    // 待拣货明细 页数据量修改
    detailsToBePickedSizeChange(pageSize) {
      this.detailsToBePickedPageSettings.pageSize = pageSize
      // 重新获取 待拣货明细 表格数据
      this.getDetailsToBePickedTableData()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .e-grid .e-toolbar {
  border-top: none;
}
.top-filter {
  background: #fff;
  padding: 20px;
  border-bottom: 1px solid #e8e8e8;

  .left-status {
    margin-right: 20px;
  }
  .itme-status {
    margin-top: 10px;
  }
}
</style>
