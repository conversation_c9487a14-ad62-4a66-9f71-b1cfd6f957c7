import { i18n } from '@/main.js'
import Vue from 'vue'
import utils from '@/utils/utils'

// 发货方式
export const ConstDeliveryType = {
  ExpressDelivery: '1', // 快递配送
  Logistics: '2', // 物流配送
  SelfMention: '3' // 采方自提
}

// 待拣货明细 明细动态列 mock
export const ColumnDataMock = [
  {
    fieldCode: 'buyerDepCode',
    fieldName: i18n.t('采购单位编码')
  },
  {
    fieldCode: 'buyerDepName',
    fieldName: i18n.t('采购单位名称')
  },
  {
    fieldCode: 'buyerOrgCode',
    fieldName: i18n.t('采购组编码')
  },
  {
    fieldCode: 'buyerOrgName',
    fieldName: i18n.t('采购组名称')
  },
  {
    fieldCode: 'categoryCode',
    fieldName: i18n.t('品类编码')
  },
  {
    fieldCode: 'categoryName',
    fieldName: i18n.t('品类名称')
  },
  {
    fieldCode: 'checkRequest',
    fieldName: i18n.t('检验要求')
  },
  {
    fieldCode: 'consignee',
    fieldName: i18n.t('收货人')
  },
  {
    fieldCode: 'contact',
    fieldName: i18n.t('联系方式')
  },
  {
    fieldCode: 'currencyCode',
    fieldName: i18n.t('币种编码')
  },
  {
    fieldCode: 'deliveryStatus',
    fieldName: i18n.t('发货状态')
  },
  {
    fieldCode: 'freePrice',
    fieldName: i18n.t('不含税单价')
  },
  {
    fieldCode: 'freeTotal',
    fieldName: i18n.t('不含税总价')
  },
  {
    fieldCode: 'itemCode',
    fieldName: i18n.t('品项编码')
  },
  {
    fieldCode: 'itemName',
    fieldName: i18n.t('品项名称')
  },
  {
    fieldCode: 'itemNo',
    fieldName: i18n.t('行号')
  },
  {
    fieldCode: 'orderItemNo',
    fieldName: i18n.t('订单明细行号')
  },
  {
    fieldCode: 'packageDesc',
    fieldName: i18n.t('包装说明')
  },
  {
    fieldCode: 'packageMethod',
    fieldName: i18n.t('包装方式')
  },
  {
    fieldCode: 'packageSpec',
    fieldName: i18n.t('包装规格')
  },
  {
    fieldCode: 'postingAccount',
    fieldName: i18n.t('入账科目')
  },
  {
    fieldCode: 'preReceiveQty',
    fieldName: i18n.t('待收货数量')
  },
  {
    fieldCode: 'preWarehouseQty',
    fieldName: i18n.t('待入库数量')
  },
  {
    fieldCode: 'productCode',
    fieldName: i18n.t('涉及产品系列编码')
  },
  {
    fieldCode: 'productName',
    fieldName: i18n.t('涉及产品系列名称')
  },
  {
    fieldCode: 'purUnitCode',
    fieldName: i18n.t('采购单位编码')
  },
  {
    fieldCode: 'qualityExemptionMarkCode',
    fieldName: i18n.t('质量免检标识编码')
  },
  {
    fieldCode: 'qualityExemptionMarkName',
    fieldName: i18n.t('质量免检标识名称')
  },
  {
    fieldCode: 'quantity',
    fieldName: i18n.t('订单数量')
  },
  {
    fieldCode: 'receiveAddress',
    fieldName: i18n.t('收货地址')
  },
  {
    fieldCode: 'receiveSiteCode',
    fieldName: '收货工厂/地点编码	'
  },
  {
    fieldCode: 'receiveSiteName',
    fieldName: i18n.t('收货工厂/地点名称')
  },
  {
    fieldCode: 'receiveStatus',
    fieldName: i18n.t('收货状态')
  },
  {
    fieldCode: 'shippingMethodCode',
    fieldName: i18n.t('物流方式编码')
  },
  {
    fieldCode: 'shippingMethodName',
    fieldName: i18n.t('物流方式名称')
  },
  {
    fieldCode: 'siteName',
    fieldName: i18n.t('工厂名称')
  },
  {
    fieldCode: 'siteCode',
    fieldName: i18n.t('工厂编码')
  },
  {
    fieldCode: 'skuCode',
    fieldName: i18n.t('sku编码')
  },
  {
    fieldCode: 'skuName',
    fieldName: i18n.t('sku名称')
  },
  {
    fieldCode: 'specification',
    fieldName: i18n.t('规格型号')
  },
  {
    fieldCode: 'taxPrice',
    fieldName: i18n.t('含税单价')
  },
  {
    fieldCode: 'taxTotal',
    fieldName: i18n.t('含税总价')
  },
  {
    fieldCode: 'taxid',
    fieldName: i18n.t('税率')
  },
  {
    fieldCode: 'tradeClauseCode',
    fieldName: i18n.t('贸易条款编码')
  },
  {
    fieldCode: 'tradeClauseName',
    fieldName: i18n.t('贸易条款名称')
  },
  {
    fieldCode: 'unitCode',
    fieldName: i18n.t('基本单位编码')
  },
  {
    fieldCode: 'unitName',
    fieldName: i18n.t('基本单位名称')
  },
  {
    fieldCode: 'warehouse',
    fieldName: i18n.t('收货仓库')
  },
  {
    fieldCode: 'warehouseQty',
    fieldName: i18n.t('入库数量')
  },
  {
    fieldCode: 'warehouseStatus',
    fieldName: i18n.t('入库状态')
  }
]

export const stringToDate = (data) => {
  const { formatString, value } = data
  if (formatString) {
    const date = new Date(Number(value))
    if (isNaN(date.getTime())) {
      return value
    } else {
      return utils.formateTime(date, formatString)
    }
  } else {
    return value
  }
}

// 待拣货订单 Col
export const PendingOrderCol = [
  {
    width: 'auto',
    field: 'orderCode',
    headerText: i18n.t('采购订单号'),
    cssClass: '',
    cellTools: [
      {
        id: 'Picking',
        icon: 'icon_list_picking',
        title: i18n.t('拣货')
      },
      {
        id: 'PendingOrderShip',
        icon: 'icon_list_takeDelivery',
        title: i18n.t('发货')
      }
    ]
  },
  {
    width: 'auto',
    field: 'orderDate',
    headerText: i18n.t('订单日期'),
    template: () => {
      return {
        template: Vue.component('date', {
          template: `<div><div>{{data.orderDate | timeFormat}}</div><div>{{data.orderDate | dateFormat}}</div></div>`,
          data: function () {
            return { data: {} }
          },
          filters: {
            dateFormat(value) {
              return stringToDate({ formatString: 'YYYY-mm-dd', value })
            },
            timeFormat(value) {
              return stringToDate({ formatString: 'HH:MM:SS', value })
            }
          }
        })
      }
    }
  },
  {
    width: 'auto',
    field: 'businessTypeName',
    headerText: i18n.t('客户业务类型')
  },
  {
    width: 'auto',
    field: 'requiredDeliveryDate',
    headerText: i18n.t('要求交期'),
    template: () => {
      return {
        template: Vue.component('date', {
          template: `<div><div>{{data.requiredDeliveryDate | timeFormat}}</div><div>{{data.requiredDeliveryDate | dateFormat}}</div></div>`,
          data: function () {
            return { data: {} }
          },
          filters: {
            dateFormat(value) {
              return stringToDate({ formatString: 'YYYY-mm-dd', value })
            },
            timeFormat(value) {
              return stringToDate({ formatString: 'HH:MM:SS', value })
            }
          }
        })
      }
    }
  },
  {
    width: 'auto',
    field: 'buyerOrgName',
    headerText: i18n.t('采购组织名称')
  },
  {
    width: 'auto',
    field: 'buyerUserName',
    headerText: i18n.t('采购员')
  },
  {
    width: 'auto',
    field: 'currencyName',
    headerText: i18n.t('币种')
  },
  {
    width: 'auto',
    field: 'paymentName',
    headerText: i18n.t('付款条件')
  },
  {
    width: 'auto',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

// 待拣货明细 Col 第一段
export const DetailsToBePickedCol1 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('采购订单号')
  },
  {
    width: '150',
    field: 'orderDate',
    headerText: i18n.t('订单日期'),
    template: () => {
      return {
        template: Vue.component('date', {
          template: `<div><div>{{data.orderDate | timeFormat}}</div><div>{{data.orderDate | dateFormat}}</div></div>`,
          data: function () {
            return { data: {} }
          },
          filters: {
            dateFormat(value) {
              return stringToDate({ formatString: 'YYYY-mm-dd', value })
            },
            timeFormat(value) {
              return stringToDate({ formatString: 'HH:MM:SS', value })
            }
          }
        })
      }
    }
  }
]

// 待拣货明细 Col 第二段
export const DetailsToBePickedCol2 = [
  {
    width: '150',
    field: 'businessTypeName',
    headerText: i18n.t('客户业务类型')
  },
  {
    width: '150',
    field: 'requiredDeliveryDate',
    headerText: i18n.t('要求交期'),
    template: () => {
      return {
        template: Vue.component('date', {
          template: `<div><div>{{data.requiredDeliveryDate | timeFormat}}</div><div>{{data.requiredDeliveryDate | dateFormat}}</div></div>`,
          data: function () {
            return { data: {} }
          },
          filters: {
            dateFormat(value) {
              return stringToDate({ formatString: 'YYYY-mm-dd', value })
            },
            timeFormat(value) {
              return stringToDate({ formatString: 'HH:MM:SS', value })
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'buyerOrgName',
    headerText: i18n.t('采购组织名称')
  },
  {
    width: '150',
    field: 'buyerUserName',
    headerText: i18n.t('采购员')
  },
  {
    width: '150',
    field: 'currencyName',
    headerText: i18n.t('币种')
  },
  {
    width: '150',
    field: 'paymentName',
    headerText: i18n.t('付款条件')
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  },
  {
    width: '150',
    field: 'deliveryQty',
    headerText: i18n.t('已发货数量')
  },
  {
    width: '150',
    field: 'receiveQty',
    headerText: i18n.t('已收货数量')
  },
  {
    width: '150',
    field: 'preDeliveryQty',
    headerText: i18n.t('待发货数量')
  },
  {
    width: '150',
    field: 'qty',
    headerText: i18n.t('本次发货'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<mt-input-number v-model.trim="data.qty" :max="data.preDeliveryQty" cssClass="e-outline" :show-clear-button="false" type="text" @change="handleChange"></mt-input-number>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {
            handleChange(e) {
              if (
                this.data.preDeliveryQty &&
                (isNaN(Number(e)) || Number(e) < 0 || e > this.data.preDeliveryQty)
              ) {
                // 非数字、小于 0、大于 待发货数量
                this.data.qty = this.data.preDeliveryQty
              } else {
                this.data.qty = e
              }
              this.$parent.$emit('cellEdit', {
                index: this.data.index,
                key: 'qty',
                value: this.data.qty
              })
            }
          }
        })
      }
    }
  }
]

// 格式化获取的动态列
export const formatColumnToGrid = (columns) => {
  const tmp = []
  columns.forEach((item) => {
    const _col = {
      width: '150',
      field: item.fieldCode,
      headerText: item.fieldName
    }

    if (item.fieldCode == 'warehouseStatus') {
      // 入库状态
      _col.template = () => {
        return {
          template: Vue.component('date', {
            template: `<div>{{data.warehouseStatus | formatData}}</div>`,
            data: function () {
              return { data: {} }
            },
            filters: {
              formatData(value) {
                const map = {
                  0: i18n.t('未入库'),
                  1: i18n.t('部分入库'),
                  2: i18n.t('全部入库')
                }
                if (!map[value]) {
                  return value
                } else {
                  return map[value]
                }
              }
            }
          })
        }
      }
    } else if (item.fieldCode == 'receiveStatus') {
      // 收货状态
      _col.template = () => {
        return {
          template: Vue.component('date', {
            template: `<div>{{data.receiveStatus | formatData}}</div>`,
            data: function () {
              return { data: {} }
            },
            filters: {
              formatData(value) {
                const map = {
                  0: i18n.t('未收货'),
                  1: i18n.t('部分收货'),
                  2: i18n.t('全部收货')
                }
                if (!map[value]) {
                  return value
                } else {
                  return map[value]
                }
              }
            }
          })
        }
      }
    } else if (item.fieldCode == 'deliveryStatus') {
      // 发货状态
      _col.template = () => {
        return {
          template: Vue.component('date', {
            template: `<div>{{data.deliveryStatus | formatData}}</div>`,
            data: function () {
              return { data: {} }
            },
            filters: {
              formatData(value) {
                const map = {
                  0: i18n.t('未发货'),
                  1: i18n.t('部分发货'),
                  2: i18n.t('全部发货')
                }
                if (!map[value]) {
                  return value
                } else {
                  return map[value]
                }
              }
            }
          })
        }
      }
    } else if (item.fieldCode == 'checkRequest') {
      // 检验要求
      _col.template = () => {
        return {
          template: Vue.component('date', {
            template: `<div>{{data.checkRequest | formatData}}</div>`,
            data: function () {
              return { data: {} }
            },
            filters: {
              formatData(value) {
                const map = {
                  0: i18n.t('否'),
                  1: i18n.t('是')
                }
                if (!map[value]) {
                  return value
                } else {
                  return map[value]
                }
              }
            }
          })
        }
      }
    }

    tmp.push(_col)
  })
  return tmp
}

// 待发货 Col
export const ToBeDeliveredCol = [
  {
    width: 'auto',
    field: 'deliveryCode',
    headerText: i18n.t('送货单号'),
    cssClass: '',
    cellTools: [
      {
        id: 'ToBeDeliveredShip',
        icon: 'icon_list_delivery',
        title: i18n.t('发货')
      },
      {
        id: 'ToBeDeliveredClose',
        icon: 'icon_list_close',
        title: i18n.t('关闭')
      }
      // 关闭
    ]
  },
  {
    width: 'auto',
    field: 'createTime',
    headerText: i18n.t('订单日期'),
    template: () => {
      return {
        template: Vue.component('date', {
          template: `<div><div>{{data.createTime | timeFormat}}</div><div>{{data.createTime | dateFormat}}</div></div>`,
          data: function () {
            return { data: {} }
          },
          filters: {
            dateFormat(value) {
              return stringToDate({ formatString: 'YYYY-mm-dd', value })
            },
            timeFormat(value) {
              return stringToDate({ formatString: 'HH:MM:SS', value })
            }
          }
        })
      }
    }
  },
  {
    width: 'auto',
    field: 'purTenantName',
    headerText: i18n.t('客户名称')
  },
  {
    width: 'auto',
    field: 'businessTypeName',
    headerText: i18n.t('客户业务类型')
  },
  {
    width: 'auto',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

// 待妥投 Col
export const PendingCol = [
  {
    width: 'auto',
    field: 'deliveryCode',
    headerText: i18n.t('送货单号'),
    cssClass: '',
    cellTools: [
      {
        id: 'ProperVote',
        icon: 'icon_list_deliver',
        title: i18n.t('妥投')
      }
    ]
  },
  {
    width: 'auto',
    field: 'createTime',
    headerText: i18n.t('订单日期'),
    template: () => {
      return {
        template: Vue.component('date', {
          template: `<div><div>{{data.createTime | timeFormat}}</div><div>{{data.createTime | dateFormat}}</div></div>`,
          data: function () {
            return { data: {} }
          },
          filters: {
            dateFormat(value) {
              return stringToDate({ formatString: 'YYYY-mm-dd', value })
            },
            timeFormat(value) {
              return stringToDate({ formatString: 'HH:MM:SS', value })
            }
          }
        })
      }
    }
  },
  {
    width: 'auto',
    field: 'purTenantName',
    headerText: i18n.t('客户名称')
  },
  {
    width: 'auto',
    field: '',
    headerText: i18n.t('客户业务类型')
  },
  {
    width: 'auto',
    field: 'deliveryTime',
    headerText: i18n.t('发货日期'),
    template: () => {
      return {
        template: Vue.component('date', {
          template: `<div><div>{{data.deliveryTime | timeFormat}}</div><div>{{data.deliveryTime | dateFormat}}</div></div>`,
          data: function () {
            return { data: {} }
          },
          filters: {
            dateFormat(value) {
              return stringToDate({ formatString: 'YYYY-mm-dd', value })
            },
            timeFormat(value) {
              return stringToDate({ formatString: 'HH:MM:SS', value })
            }
          }
        })
      }
    }
  },
  {
    width: 'auto',
    field: 'estimatedArrivalTime',
    headerText: i18n.t('预计到日期'),
    template: () => {
      return {
        template: Vue.component('date', {
          template: `<div><div>{{data.estimatedArrivalTime | timeFormat}}</div><div>{{data.estimatedArrivalTime | dateFormat}}</div></div>`,
          data: function () {
            return { data: {} }
          },
          filters: {
            dateFormat(value) {
              return stringToDate({ formatString: 'YYYY-mm-dd', value })
            },
            timeFormat(value) {
              return stringToDate({ formatString: 'HH:MM:SS', value })
            }
          }
        })
      }
    }
  },
  {
    width: 'auto',
    field: 'deliveryType',
    headerText: i18n.t('发货方式'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('快递配送'),
        2: i18n.t('物流配送'),
        3: i18n.t('采方自提')
      }
    }
  },
  {
    width: 'auto',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

// 发货记录 Col
export const DeliveryRecordCol = [
  {
    width: 'auto',
    field: 'deliveryCode',
    headerText: i18n.t('送货单号'),
    cssClass: '',
    cellTools: [
      {
        id: 'ReturnShipping',
        icon: 'icon_list_return',
        title: i18n.t('退回发货')
      }
    ]
  },
  {
    width: 'auto',
    field: 'receiveStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未收货'),
        1: i18n.t('部分发货'),
        2: i18n.t('全部收货')
      }
    }
  },
  {
    width: 'auto',
    field: 'createTime',
    headerText: i18n.t('订单日期'),
    template: () => {
      return {
        template: Vue.component('date', {
          template: `<div><div>{{data.createTime | timeFormat}}</div><div>{{data.createTime | dateFormat}}</div></div>`,
          data: function () {
            return { data: {} }
          },
          filters: {
            dateFormat(value) {
              return stringToDate({ formatString: 'YYYY-mm-dd', value })
            },
            timeFormat(value) {
              return stringToDate({ formatString: 'HH:MM:SS', value })
            }
          }
        })
      }
    }
  },
  {
    width: 'auto',
    field: 'purTenantName',
    headerText: i18n.t('客户名称')
  },
  {
    width: 'auto',
    field: 'orderType',
    headerText: i18n.t('客户订单类型'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('退货订单'),
        '-2': i18n.t('换货订单'),
        '-3': i18n.t('维修订单'),
        1: i18n.t('正常订单')
      }
    }
  },
  {
    width: 'auto',
    field: 'deliveryTime',
    headerText: i18n.t('发货日期'),
    template: () => {
      return {
        template: Vue.component('date', {
          template: `<div><div>{{data.deliveryTime | timeFormat}}</div><div>{{data.deliveryTime | dateFormat}}</div></div>`,
          data: function () {
            return { data: {} }
          },
          filters: {
            dateFormat(value) {
              return stringToDate({ formatString: 'YYYY-mm-dd', value })
            },
            timeFormat(value) {
              return stringToDate({ formatString: 'HH:MM:SS', value })
            }
          }
        })
      }
    }
  },
  {
    width: 'auto',
    field: 'estimatedArrivalTime',
    headerText: i18n.t('预计到日期'),
    template: () => {
      return {
        template: Vue.component('date', {
          template: `<div><div>{{data.estimatedArrivalTime | timeFormat}}</div><div>{{data.estimatedArrivalTime | dateFormat}}</div></div>`,
          data: function () {
            return { data: {} }
          },
          filters: {
            dateFormat(value) {
              return stringToDate({ formatString: 'YYYY-mm-dd', value })
            },
            timeFormat(value) {
              return stringToDate({ formatString: 'HH:MM:SS', value })
            }
          }
        })
      }
    }
  },
  {
    width: 'auto',
    field: 'deliveryType',
    headerText: i18n.t('发货方式'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('快递配送'),
        2: i18n.t('物流配送'),
        3: i18n.t('采方自提')
      }
    }
  },
  {
    width: 'auto',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
