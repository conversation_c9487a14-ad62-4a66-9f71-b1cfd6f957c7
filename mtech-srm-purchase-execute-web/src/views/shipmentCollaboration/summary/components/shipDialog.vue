<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules">
      <mt-form-item prop="deliveryTime" :label="$t('发货时间')" class="">
        <mt-date-time-picker
          :width="300"
          :show-clear-button="true"
          :allow-edit="false"
          v-model="formData.deliveryTime"
          :placeholder="$t('选择发货时间')"
        ></mt-date-time-picker>
      </mt-form-item>
      <mt-form-item prop="estimatedArrivalTime" :label="$t('预计到货时间')" class="">
        <mt-date-time-picker
          :width="300"
          :show-clear-button="true"
          :allow-edit="false"
          v-model="formData.estimatedArrivalTime"
          :placeholder="$t('选择预计到货时间')"
        ></mt-date-time-picker>
      </mt-form-item>
      <!-- TODO 搜索匹配 -->
      <mt-form-item prop="shippingAddress" :label="$t('发货地址')" class="">
        <mt-input
          v-model="formData.shippingAddress"
          :show-clear-button="true"
          :placeholder="$t('请输入发货地址')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="deliveryType" :label="$t('发货方式')" class="">
        <mt-radio
          v-model="formData.deliveryType"
          :data-source="deliveryTypeRadio"
          @change="onchangDeliveryType"
        ></mt-radio>
      </mt-form-item>
      <!-- 发货方式 快递配送 -->
      <template v-if="formData.deliveryType == ConstDeliveryType.ExpressDelivery">
        <mt-form-item prop="expressCompany" :label="$t('物流公司')" class="">
          <!-- <mt-select
            :width="300"
            v-model="formData.expressCompanyId"
            :data-source="expressCompanyOptions"
            :show-clear-button="true"
            :placeholder="$t('请输入物流公司')"
          ></mt-select> -->
          <mt-input
            v-model="formData.expressCompany"
            :show-clear-button="true"
            :placeholder="$t('请输入物流公司')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="courierNumber" :label="$t('物流单号')" class="">
          <mt-input
            v-model="formData.courierNumber"
            :show-clear-button="true"
            :placeholder="$t('请输入物流单号')"
          ></mt-input>
        </mt-form-item>
      </template>
      <!-- 发货方式 物流配送 -->
      <template v-if="formData.deliveryType == ConstDeliveryType.Logistics">
        <mt-form-item prop="driverName" :label="$t('司机姓名')" class="">
          <mt-input
            v-model="formData.driverName"
            :show-clear-button="true"
            :placeholder="$t('请输入司机姓名')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="driverPhone" :label="$t('司机联系方式')" class="">
          <mt-input
            v-model="formData.driverPhone"
            :show-clear-button="true"
            :placeholder="$t('请输入联系方式')"
          ></mt-input>
        </mt-form-item>
      </template>
      <!-- 发货方式 采方自提 -->
      <template v-if="formData.deliveryType == ConstDeliveryType.SelfMention">
        <mt-form-item prop="consignee" :label="$t('提货人姓名')" class="">
          <mt-input
            v-model="formData.consignee"
            :show-clear-button="true"
            :placeholder="$t('请输入提货人姓名')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="driverPhone" :label="$t('提货人联系方式')" class="">
          <mt-input
            v-model="formData.driverPhone"
            :show-clear-button="true"
            :placeholder="$t('请输入联系方式')"
          ></mt-input>
        </mt-form-item>
      </template>
      <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
        <mt-input
          v-model="formData.remark"
          :show-clear-button="true"
          :multiline="true"
          :placeholder="$t('请输入备注内容')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { ConstDeliveryType } from '../config/index.js'

export default {
  data() {
    // 发货时间
    const deliveryTimeValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请选择发货时间'))
      } else if (this.formData.estimatedArrivalTime && value > this.formData.estimatedArrivalTime) {
        callback(new Error('发货时间不能晚于到货时间'))
      } else {
        this.$refs.ruleForm.clearValidate(['estimatedArrivalTime'])
        callback()
      }
    }
    // 预计到货时间
    const estimatedArrivalTimeValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请选择预计到货时间'))
      } else if (this.formData.deliveryTime && value < this.formData.deliveryTime) {
        callback(new Error('到货时间不能早于发货时间'))
      } else {
        this.$refs.ruleForm.clearValidate(['deliveryTime'])
        callback()
      }
    }

    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      ConstDeliveryType,
      deliveryBillIds: [], // 发货单id
      picks: [], // 拣货信息
      deliveryTypeRadio: [
        {
          label: this.$t('快递配送'),
          value: ConstDeliveryType.ExpressDelivery
        },
        {
          label: this.$t('物流配送'),
          value: ConstDeliveryType.Logistics
        },
        {
          label: this.$t('采方自提'),
          value: ConstDeliveryType.SelfMention
        }
      ],
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      // 快递公司名称下拉选项
      expressCompanyOptions: [
        // { text: "区域1", value: 0 },
        // { text: "区域2", value: 1 },
      ],
      dialogTitle: '',
      actionType: 0, // 0: 创建送货单并发货 1: 发货
      formData: {
        consignee: '', // 提货人
        courierNumber: '', // 快递单号
        deliveryTime: '', // 发货时间
        estimatedArrivalTime: '', // 预计到货时间
        driverName: '', // 司机姓名
        driverPhone: '', // 联系方式（司机/提货人）
        expressCompany: '', // 快递公司名称
        expressCompanyId: 0, // 快递公司id
        remark: '', // 备注
        shippingAddress: '', // 发货地址
        shippingAddressId: 0, // 发货地址id
        deliveryType: ConstDeliveryType.ExpressDelivery // 发货方式 默认值: 快递配送
      },
      rules: {
        deliveryTime: [{ required: true, validator: deliveryTimeValidator, trigger: 'blur' }],
        estimatedArrivalTime: [
          {
            required: true,
            validator: estimatedArrivalTimeValidator,
            trigger: 'blur'
          }
        ],
        shippingAddress: [
          {
            required: true,
            message: this.$t('请输入发货地址'),
            trigger: 'blur'
          }
        ],
        deliveryType: [
          {
            required: true,
            message: this.$t('请选择发货方式'),
            trigger: 'blur'
          }
        ],
        expressCompany: [
          {
            required: true,
            message: this.$t('请输入物流公司'),
            trigger: 'blur'
          }
        ],
        courierNumber: [
          {
            required: true,
            message: this.$t('请输入物流单号'),
            trigger: 'blur'
          }
        ],
        driverName: [
          {
            required: true,
            message: this.$t('请输入司机姓名'),
            trigger: 'blur'
          }
        ],
        consignee: [
          {
            required: true,
            message: this.$t('请输入提货人姓名'),
            trigger: 'blur'
          }
        ],
        driverPhone: [
          {
            required: true,
            message: this.$t('请输入联系方式'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  mounted() {
    // 获取 快递公司名称下拉选项 TODO 物流模块未开启
    // this.getExpressCompanyOptions();
  },
  methods: {
    // 初始化
    dialogInit(entryInfo) {
      const { dialogTitle, actionType, selectData } = entryInfo
      this.dialogTitle = dialogTitle // 弹框名称
      this.actionType = actionType // 弹框模式
      this.deliveryBillIds = selectData?.ids // 发货单id
      this.picks = selectData?.picks // 拣货信息
      this.initForm()
      this.$refs.dialog.ejsRef.show()
    },
    initForm() {
      this.$refs.ruleForm.clearValidate()
      this.formData.deliveryType = ConstDeliveryType.ExpressDelivery // 默认值 发货方式 0-快递配送；1-物流配送；2-采方自提
      this.formData.consignee = '' // 提货人
      this.formData.courierNumber = '' // 物流单号
      this.formData.deliveryTime = '' // 发货时间
      this.formData.estimatedArrivalTime = '' // 预计到货时间
      this.formData.driverName = '' // 司机姓名
      this.formData.driverPhone = '' // 联系方式（司机/提货人）
      this.formData.expressCompany = '' // 快递公司名称
      this.formData.expressCompanyId = 0 // 快递公司id
      this.formData.remark = '' // 备注
      this.formData.shippingAddress = '' // 发货地址
      this.formData.shippingAddressId = 0 // 发货地址id
    },
    onchangDeliveryType() {
      // 切换发货方式 清空变化项验证消息
      this.$refs.ruleForm.clearValidate([
        'expressCompany', // 物流公司
        'courierNumber', // 物流单号
        'driverName', // 司机姓名
        'consignee', // 提货人姓名
        'driverPhone' // 联系方式
      ])
      this.formData.expressCompany = '' // 快递公司名称
      this.formData.expressCompanyId = 0 // 快递公司id
      this.formData.courierNumber = '' // 物流单号
      this.formData.driverName = '' // 司机姓名
      this.formData.consignee = '' // 提货人
      this.formData.driverPhone = '' // 联系方式（司机/提货人）
    },
    onOpen(args) {
      args.preventFocus = true
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // TODO 物流公司选择 物流模块未开启
          // const expressCompanySelect = this.expressCompanyOptions.find(
          //   (item) => item.value === this.formData.expressCompanyId
          // );
          // if (expressCompanySelect) {
          //   this.formData.expressCompany = expressCompanySelect.text;
          // }
          const params = {
            deliveryBillIds: this.deliveryBillIds, // 发货单id
            method: {
              ...this.formData,
              deliveryTime: Date.parse(this.formData.deliveryTime), // 发货时间
              estimatedArrivalTime: Date.parse(this.formData.estimatedArrivalTime), // 预计到货时间
              deliveryType: Number(this.formData.deliveryType) // 发货方式 0-快递配送；1-物流配送；2-采方自提
            }
          }
          if (this.actionType === 0) {
            // 拣货后发货
            this.apiStartLoading()
            this.$API.shipmentCollaboration
              .postSoDeliverAfterPicking(params)
              .then(() => {
                this.apiEndLoading()
                this.$toast({
                  content: this.$t('发货操作成功'),
                  type: 'success'
                })
                this.$emit('refreshColumns')
                this.handleClose()
              })
              .catch(() => {
                this.apiEndLoading()
              })
          } else if (this.actionType === 1) {
            // 待拣货明细-创建送货单并发货
            const params = {
              method: {
                ...this.formData,
                deliveryTime: Date.parse(this.formData.deliveryTime), // 发货时间
                estimatedArrivalTime: Date.parse(this.formData.estimatedArrivalTime), // 预计到货时间
                deliveryType: Number(this.formData.deliveryType) // 发货方式 0-快递配送；1-物流配送；2-采方自提
              },
              picks: this.picks // 拣货信息
            }
            // 供方拣货发货
            this.apiStartLoading()
            this.$API.shipmentCollaboration
              .postSoPlanDelivery(params)
              .then(() => {
                this.apiEndLoading()
                this.$toast({
                  content: this.$t('发货操作成功'),
                  type: 'success'
                })
                this.handleClose()
                // 返回 发货协同-列表
                this.$router.push({
                  name: 'shipment-collaboration-summary',
                  query: {}
                })
              })
              .catch(() => {
                this.apiEndLoading()
              })
            this.$emit('refreshColumns')
            this.handleClose()
          }
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
    // 获取 快递公司名称下拉选项 TODO 物流模块未开启
    // getExpressCompanyOptions() {
    //   this.$API.masterData
    //     .getDictCode({ dictCode: "TransportMode" })
    //     .then((res) => {
    //       if (res.data && res.data.length) {
    //         this.expressCompanyOptions = [];
    //         res.data.forEach((item) => {
    //           this.expressCompanyOptions.push({
    //             text: item.itemName, // 名称
    //             value: item.id, // id
    //           });
    //         });
    //       }
    //     });
    // },
  }
}
</script>
