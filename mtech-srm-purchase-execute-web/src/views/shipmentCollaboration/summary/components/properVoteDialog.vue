<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="$t('妥投')"
    :buttons="buttons"
    :open="onOpen"
    :close="initForm"
  >
    <mt-form ref="ruleForm" :model="infoData" :rules="rules">
      <mt-form-item prop="deliveryType" :label="$t('发货方式')" class="full-width">
        <mt-radio
          v-model="infoData.deliveryType"
          :data-source="deliveryTypeRadio"
          :disabled="true"
        ></mt-radio>
      </mt-form-item>
      <!-- 发货方式 快递配送 -->
      <template v-if="infoData.deliveryType == ConstDeliveryType.ExpressDelivery">
        <mt-form-item prop="expressCompany" :label="$t('物流公司')" class="">
          <mt-input
            v-model="infoData.expressCompany"
            :show-clear-button="false"
            :disabled="true"
            placeholder=""
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="courierNumber" :label="$t('物流单号')" class="">
          <mt-input
            v-model="infoData.courierNumber"
            :show-clear-button="false"
            :disabled="true"
            placeholder=""
          ></mt-input>
        </mt-form-item>
      </template>
      <!-- 发货方式 物流配送 -->
      <template v-if="infoData.deliveryType == ConstDeliveryType.Logistics">
        <mt-form-item prop="driverName" :label="$t('司机姓名')" class="">
          <mt-input
            v-model="infoData.driverName"
            :show-clear-button="false"
            :disabled="true"
            placeholder=""
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="driverPhone" :label="$t('司机联系方式')" class="">
          <mt-input
            v-model="infoData.driverPhone"
            :show-clear-button="false"
            :disabled="true"
            placeholder=""
          ></mt-input>
        </mt-form-item>
      </template>
      <!-- 发货方式 采方自提 -->
      <template v-if="infoData.deliveryType == ConstDeliveryType.SelfMention">
        <mt-form-item prop="consignee" :label="$t('提货人姓名')" class="">
          <mt-input
            v-model="infoData.consignee"
            :show-clear-button="false"
            :disabled="true"
            placeholder=""
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="driverPhone" :label="$t('提货人联系方式')" class="">
          <mt-input
            v-model="infoData.driverPhone"
            :show-clear-button="false"
            :disabled="true"
            placeholder=""
          ></mt-input>
        </mt-form-item>
      </template>
      <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
        <mt-input
          v-model="infoData.remark"
          :show-clear-button="false"
          :multiline="true"
          :disabled="true"
          placeholder=""
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="uploadData" :label="$t('上传凭证附件')" class="full-width">
        <upload-file ref="uploader" @change="fileChange"></upload-file>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { ConstDeliveryType } from '../config/index.js'

export default {
  components: {
    UploadFile: () => import('@/components/Upload/uploader')
  },
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      ConstDeliveryType,
      uploadData: [],
      deliveryTypeRadio: [
        {
          label: this.$t('快递配送'),
          value: ConstDeliveryType.ExpressDelivery
        },
        {
          label: this.$t('物流配送'),
          value: ConstDeliveryType.Logistics
        },
        {
          label: this.$t('采方自提'),
          value: ConstDeliveryType.SelfMention
        }
      ],
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      infoData: {},
      rules: {}
    }
  },
  methods: {
    initForm() {
      this.$refs.uploader.init()
      this.infoData.id = '' // id
      this.infoData.deliveryType = '' // 发货方式
      this.infoData.expressCompany = '' // 物流公司
      this.infoData.courierNumber = '' // 物流单号
      this.infoData.driverName = '' // 司机姓名
      this.infoData.consignee = '' // 提货人姓名
      this.infoData.driverPhone = '' // 联系方式
      this.infoData.remark = '' // 备注
    },
    fileChange(data) {
      this.uploadData = data
    },
    dialogInit(entryInfo) {
      const { rowData } = entryInfo
      const infoData = {
        id: rowData?.id, // id
        deliveryType: String(rowData?.deliveryType), // 发货方式
        expressCompany: rowData?.expressCompany, // 物流公司
        courierNumber: rowData?.courierNumber, // 物流单号
        driverName: rowData?.driverName, // 司机姓名
        consignee: rowData?.consignee, // 提货人姓名
        driverPhone: rowData?.driverPhone, // 联系方式
        remark: rowData?.remark // 备注
      }
      this.infoData = infoData
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    confirm() {
      if (this.uploadData.length == 0) {
        this.$toast({ content: this.$t('请上传附件'), type: 'warning' })
        return
      }
      const uploadDataTmp = []
      this.uploadData.forEach((item) => {
        uploadDataTmp.push({
          docId: this.infoData.id, // 业务对象id
          docType: 'so_proper', // 业务对象类型（枚举）
          fileName: item.fileName, // 文件上传名称
          fileSize: item.fileSize, // 文件大小
          fileType: item.fileType, // 文件类型
          lineNo: 10, // 排序
          nodeCode: 'so_proper_file', // 节点编码
          nodeName: this.$t('供方妥投文件'), // 节点名称
          nodeType: 1, // 类型(0节点,1文件)
          parentId: '', // 父id
          sysFileId: item.id, // 文件ID(MT_WP_SYS_FILE表ID)
          url: item.url // 文件路径
        })
      })
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 供方妥投
          const params = {
            deliveryBillIds: [this.infoData.id], // 发货单id
            files: uploadDataTmp // 供方妥投附件
          }
          this.apiStartLoading()
          this.$API.shipmentCollaboration
            .postSoDeliveryProper(params)
            .then(() => {
              this.apiEndLoading()
              this.$toast({
                content: this.$t('妥投操作成功'),
                type: 'success'
              })
              this.handleClose()
            })
            .catch(() => {
              this.apiEndLoading()
            })
        }
      })
      this.$emit('refreshColumns')
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
