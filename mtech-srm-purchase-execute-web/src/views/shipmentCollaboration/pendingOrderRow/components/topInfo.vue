<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button
        v-if="entryType == '0'"
        css-class="e-flat"
        :is-primary="true"
        @click="createDeliveryNote"
        >{{ $t('创建送货单') }}</mt-button
      >
      <mt-button
        v-if="entryType == '1'"
        css-class="e-flat"
        :is-primary="true"
        @click="createDeliveryNoteAndShip"
        >{{ $t('创建送货单并发货') }}</mt-button
      >
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>

      <div class="sort-box" @click="isExpand = !isExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="orderCode" :label="$t('采购订单号')">
          <mt-input
            v-model="headerInfo.orderCode"
            :disabled="true"
            :placeholder="$t('采购订单号')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="businessTypeName" :label="$t('业务类型')">
          <mt-input
            v-model="headerInfo.businessTypeName"
            :disabled="true"
            :placeholder="$t('业务类型')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="contractType" :label="$t('合同/协议类型')" :show-message="false">
          <mt-select
            :disabled="true"
            v-model="headerInfo.contractType"
            :data-source="contractTypeOptions"
            :show-clear-button="false"
            :placeholder="$t('合同/协议类型')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="purTenantName" :label="$t('客户名称')" :show-message="false">
          <mt-input
            v-model="headerInfo.purTenantName"
            :disabled="true"
            :placeholder="$t('客户名称')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="type" :label="$t('订单类型')" :show-message="false">
          <mt-select
            :disabled="true"
            v-model="headerInfo.type"
            :data-source="typeOptions"
            :show-clear-button="false"
            :placeholder="$t('订单类型')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="paymentName" :label="$t('付款条件')" :show-message="false">
          <mt-input
            v-model="headerInfo.paymentName"
            :disabled="true"
            :placeholder="$t('付款条件')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="buyerUserName" :label="$t('采购员')" :show-message="false">
          <mt-input
            v-model="headerInfo.buyerUserName"
            :disabled="true"
            :placeholder="$t('采购员')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="companyName" :label="$t('公司')" :show-message="false">
          <mt-input
            v-model="headerInfo.companyName"
            :disabled="true"
            :placeholder="$t('公司')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="buyerOrgName" :label="$t('采购组织')" :show-message="false">
          <mt-input
            v-model="headerInfo.buyerOrgName"
            :disabled="true"
            :placeholder="$t('采购组织')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="requiredDeliveryDate" :label="$t('要求交期')" :show-message="false">
          <mt-date-time-picker
            :disabled="true"
            :show-clear-button="true"
            :allow-edit="false"
            v-model="headerInfo.requiredDeliveryDate"
            :placeholder="$t('要求交期')"
          ></mt-date-time-picker>
        </mt-form-item>

        <mt-form-item prop="currencyName" :label="$t('订单币种')" :show-message="false">
          <mt-input
            v-model="headerInfo.currencyName"
            :disabled="true"
            :placeholder="$t('订单币种')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="settlementName" :label="$t('结算方')" :show-message="false">
          <mt-input
            v-model="headerInfo.settlementName"
            :disabled="true"
            :placeholder="$t('结算方')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="invoiceId" :label="$t('发票信息')" :show-message="false">
          <!-- TODO 发票信息 -->
          <mt-input
            v-model="headerInfo.invoiceId"
            :disabled="true"
            :placeholder="$t('发票信息')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="taxTotal" :label="$t('含税总金额')" :show-message="false">
          <mt-input
            v-model="headerInfo.taxTotal"
            :disabled="true"
            :placeholder="$t('含税总金额')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="freeTotal" :label="$t('未税总金额')" :show-message="false">
          <mt-input
            v-model="headerInfo.freeTotal"
            :disabled="true"
            :placeholder="$t('未税总金额')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input
            v-model="headerInfo.remark"
            :disabled="true"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import utils from '@/utils/utils'
export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    },
    entryType: {
      type: String,
      default: () => '0'
    }
  },
  data() {
    return {
      isExpand: true,
      rules: {},
      // 合同/协议类型
      contractTypeOptions: [
        {
          value: 0,
          text: this.$t('单次采购')
        },
        {
          value: 1,
          text: this.$t('框架协议')
        }
      ],
      // 订单类型
      typeOptions: [
        {
          value: 0,
          text: this.$t('正常订单')
        },
        {
          value: 1,
          text: this.$t('退货订单')
        },
        {
          value: 2,
          text: this.$t('免费订单')
        }
      ]
    }
  },
  mounted() {},
  filters: {
    dateFormat(value) {
      const date = new Date(Number(value))
      if (isNaN(date.getTime())) {
        return value
      } else {
        return utils.formateTime(date, 'YYYY-mm-dd HH:MM:SS')
      }
    }
  },
  methods: {
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 创建送货单并发货
    createDeliveryNoteAndShip() {
      this.$emit('ship')
    },
    // 创建送货单
    createDeliveryNote() {
      this.$emit('createDeliveryNote')
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
