import { i18n } from '@/main.js'
import Vue from 'vue'

// 发货方式
export const ConstDeliveryType = {
  ExpressDelivery: '1', // 快递配送
  Logistics: '2', // 物流配送
  SelfMention: '3' // 采方自提
}

// 明细动态列 mock
export const ColumnDataMock = [
  {
    fieldCode: 'itemNo',
    fieldName: i18n.t('行号')
  },
  {
    fieldCode: 'buyerDepCode',
    fieldName: i18n.t('采购单位编码')
  },
  {
    fieldCode: 'buyerDepName',
    fieldName: i18n.t('采购单位名称')
  },
  {
    fieldCode: 'buyerOrgCode',
    fieldName: i18n.t('采购组编码')
  },
  {
    fieldCode: 'buyerOrgName',
    fieldName: i18n.t('采购组名称')
  },
  {
    fieldCode: 'categoryCode',
    fieldName: i18n.t('品类编码')
  },
  {
    fieldCode: 'categoryName',
    fieldName: i18n.t('品类名称')
  },
  {
    fieldCode: 'checkRequest',
    fieldName: i18n.t('检验要求')
  },
  {
    fieldCode: 'consignee',
    fieldName: i18n.t('收货人')
  },
  {
    fieldCode: 'contact',
    fieldName: i18n.t('联系方式')
  },
  {
    fieldCode: 'currencyCode',
    fieldName: i18n.t('币种编码')
  },
  {
    fieldCode: 'currencyName',
    fieldName: i18n.t('币种名称')
  },
  {
    fieldCode: 'deliveryStatus',
    fieldName: i18n.t('发货状态')
  },
  {
    fieldCode: 'freePrice',
    fieldName: i18n.t('不含税单价')
  },
  {
    fieldCode: 'freeTotal',
    fieldName: i18n.t('不含税总价')
  },
  {
    fieldCode: 'itemCode',
    fieldName: i18n.t('品项编码')
  },
  {
    fieldCode: 'itemName',
    fieldName: i18n.t('品项名称')
  },
  {
    fieldCode: 'orderItemNo',
    fieldName: i18n.t('订单明细行号')
  },
  {
    fieldCode: 'packageDesc',
    fieldName: i18n.t('包装说明')
  },
  {
    fieldCode: 'packageMethod',
    fieldName: i18n.t('包装方式')
  },
  {
    fieldCode: 'packageSpec',
    fieldName: i18n.t('包装规格')
  },
  {
    fieldCode: 'postingAccount',
    fieldName: i18n.t('入账科目')
  },
  {
    fieldCode: 'productCode',
    fieldName: i18n.t('涉及产品系列编码')
  },
  {
    fieldCode: 'productName',
    fieldName: i18n.t('涉及产品系列名称')
  },
  {
    fieldCode: 'purUnitCode',
    fieldName: i18n.t('采购单位编码')
  },
  {
    fieldCode: 'qualityExemptionMarkCode',
    fieldName: i18n.t('质量免检标识编码')
  },
  {
    fieldCode: 'qualityExemptionMarkName',
    fieldName: i18n.t('质量免检标识名称')
  },
  {
    fieldCode: 'receiveAddress',
    fieldName: i18n.t('收货地址')
  },
  {
    fieldCode: 'receiveSiteCode',
    fieldName: '收货工厂/地点编码	'
  },
  {
    fieldCode: 'receiveSiteName',
    fieldName: i18n.t('收货工厂/地点名称')
  },
  {
    fieldCode: 'receiveStatus',
    fieldName: i18n.t('收货状态')
  },
  {
    fieldCode: 'remark',
    fieldName: i18n.t('备注')
  },
  {
    fieldCode: 'requiredDeliveryDate',
    fieldName: i18n.t('要求交期')
  },
  {
    fieldCode: 'shippingMethodCode',
    fieldName: i18n.t('物流方式编码')
  },
  {
    fieldCode: 'shippingMethodName',
    fieldName: i18n.t('物流方式名称')
  },
  {
    fieldCode: 'siteName',
    fieldName: i18n.t('工厂名称')
  },
  {
    fieldCode: 'siteCode',
    fieldName: i18n.t('工厂编码')
  },
  {
    fieldCode: 'skuCode',
    fieldName: i18n.t('sku编码')
  },
  {
    fieldCode: 'skuName',
    fieldName: i18n.t('sku名称')
  },
  {
    fieldCode: 'specification',
    fieldName: i18n.t('规格型号')
  },
  {
    fieldCode: 'taxPrice',
    fieldName: i18n.t('含税单价')
  },
  {
    fieldCode: 'taxTotal',
    fieldName: i18n.t('含税总价')
  },
  {
    fieldCode: 'taxid',
    fieldName: i18n.t('税率')
  },
  {
    fieldCode: 'tradeClauseCode',
    fieldName: i18n.t('贸易条款编码')
  },
  {
    fieldCode: 'tradeClauseName',
    fieldName: i18n.t('贸易条款名称')
  },
  {
    fieldCode: 'unitCode',
    fieldName: i18n.t('基本单位编码')
  },
  {
    fieldCode: 'unitName',
    fieldName: i18n.t('基本单位名称')
  },
  {
    fieldCode: 'warehouse',
    fieldName: i18n.t('收货仓库')
  },
  {
    fieldCode: '',
    fieldName: i18n.t('入库状态')
  },
  {
    fieldCode: 'warehouseQty',
    fieldName: i18n.t('入库数量')
  },
  {
    fieldCode: 'preReceiveQty',
    fieldName: i18n.t('待收货数量')
  },
  {
    fieldCode: 'preWarehouseQty',
    fieldName: i18n.t('待入库数量')
  },
  {
    fieldCode: 'deliveryQty',
    fieldName: i18n.t('已发货数量')
  },
  {
    fieldCode: 'receiveQty',
    fieldName: i18n.t('已收货数量')
  },
  {
    fieldCode: 'quantity',
    fieldName: i18n.t('订单数量')
  },
  {
    fieldCode: 'preDeliveryQty',
    fieldName: i18n.t('待发货数量')
  }
]

export const ReceivedQuantityColumnData = {
  width: '150',
  field: 'qty',
  headerText: i18n.t('本次发货'),
  template: () => {
    return {
      template: Vue.component('actionInput', {
        template: `<mt-input-number v-model.trim="data.qty" :max="data.preDeliveryQty" cssClass="e-outline" :show-clear-button="false" type="text" @change="handleChange"></mt-input-number>`,
        data: function () {
          return { data: {} }
        },
        mounted() {},
        methods: {
          handleChange(e) {
            if (
              this.data.preDeliveryQty &&
              (isNaN(Number(e)) || Number(e) < 0 || e > this.data.preDeliveryQty)
            ) {
              // 非数字、小于 0、大于 待发货数量
              this.data.qty = this.data.preDeliveryQty
            } else {
              this.data.qty = e
            }
            this.$parent.$emit('cellEdit', {
              index: this.data.index,
              key: 'qty',
              value: this.data.qty
            })
          }
        }
      })
    }
  }
}
