<template>
  <!-- 发货协同-单据行 -->
  <div class="full-height pt20">
    <!-- 头部信息 -->

    <top-info
      :header-info="headerInfo"
      :entry-type="entryType"
      @ship="ship"
      @createDeliveryNote="createDeliveryNote"
      @goBack="goBack"
    ></top-info>
    <div class="bottom-tables">
      <mt-tabs
        tab-id="pending-tab"
        :e-tab="false"
        :data-source="tabList"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
      <!-- 需求明细 -->
      <div class="full-height" v-show="currentSelectTab.name == $t('需求明细')">
        <mt-data-grid
          v-if="columnData && columnData.length > 0"
          @cellEdit="cellEdit"
          ref="dataGrid"
          class="custom-toolbar-grid"
          :data-source="dataSource"
          :column-data="columnData"
          :toolbar="toolbar"
          :toolbar-click="toolbarClick"
          :allow-sorting="true"
          :allow-filtering="true"
          :filter-settings="filterOptions"
          :allow-reordering="true"
          :allow-paging="true"
          :page-settings="pageSettings"
          @currentChange="handleCurrentChange"
          @sizeChange="handleSizeChange"
        ></mt-data-grid>
      </div>
      <!-- 相关附件 -->
      <relative-file
        v-show="currentSelectTab.name == $t('相关附件')"
        ref="relativeFileRef"
        :doc-id="relativeFileData.docId"
        :request-url-obj="relativeFileData.requestUrlObj"
        :file-query-parms="relativeFileData.fileQueryParms"
        :is-view="relativeFileData.isView"
      ></relative-file>
    </div>
    <!-- 发货 弹框 -->
    <ship-dialog ref="shipDialog"></ship-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { ReceivedQuantityColumnData, ColumnDataMock } from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'

export default {
  components: {
    TopInfo: require('./components/topInfo.vue').default,
    RelativeFile: () => import('@/components/businessComponents/relativeFile/index.vue'),
    // 发货、创建送货单并发货 弹框
    ShipDialog: () => import('./components/shipDialog')
  },
  data() {
    return {
      relativeFileData: {
        docId: this.$route.query.orderId,
        requestUrlObj: {
          preUrl: 'purchaseOrder', // 前缀
          listUrl: 'getFileNodeByDocId', // 获取左侧节点 根据申请主单查询所有文件节点信息
          fileUrl: `${BASE_TENANT}/file/queryFileByDocId` // 获取附件列表 根据docId-节点id查询所有文件信息
        },
        fileQueryParms: {
          docType: 'so'
        },
        isView: true
      },
      pageSettings: {
        pageSize: 10, // 当前每页数据量
        totalRecordsCount: 0, // 总条数
        pageSizes: [10, 20, 30]
      },
      filterOptions: {
        type: 'Menu'
      },
      currentPage: 1, // 当前页码
      dataSource: [],
      columnData: [],
      toolbar: [
        {
          text: this.$t('清空数量'),
          tooltipText: this.$t('清空数量'),
          prefixIcon: 'mt-icons mt-icon-icon_table_empty',
          id: 'EmptyQuantity'
        },
        {
          text: this.$t('自动填充'),
          tooltipText: this.$t('自动填充'),
          prefixIcon: 'mt-icons mt-icon-icon_table_AutoFill',
          id: 'AutoFill'
        }
      ],
      tabList: [
        {
          title: this.$t('需求明细'),
          content: '0'
        },
        {
          title: this.$t('相关附件'),
          content: '1'
        }
      ],
      currentSelectTab: {
        index: '0',
        name: this.$t('需求明细')
      },
      entryType: '0',
      headerInfo: {},
      editData: [], // 行编辑过的数据
      apiWaitingQuantity: 0 // 调用的api正在等待数
    }
  },
  mounted() {
    this.entryType = this.$route.query.type
    // 顶部信息获取
    this.getHeaderInfo()
    // 表格列动态获取
    this.getTableCol()
    // 表格数据获取
    this.getTableData()
  },
  methods: {
    // 创建送货单并发货
    ship() {
      const picks = this.picksData()
      if (picks.length === 0) {
        this.$toast({ content: this.$t('未填写发货数量'), type: 'warning' })
        return
      }
      this.$refs.shipDialog.dialogInit({
        dialogTitle: this.$t('发货'),
        picksData: picks, // 拣货信息
        actionType: 0
      })
    },
    // 创建送货单-拣货
    createDeliveryNote() {
      const picks = this.picksData()
      if (picks.length === 0) {
        this.$toast({ content: this.$t('未填写发货数量'), type: 'warning' })
        return
      }
      const params = {
        picks // 拣货信息
      }
      // 供方拣货
      this.apiStartLoading()
      this.$API.shipmentCollaboration
        .postSoPlanPicking(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
          }
          // 返回 发货协同-列表
          this.$router.push({
            name: 'shipment-collaboration-summary',
            query: {}
          })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 整理拣货信息
    picksData() {
      const data = this.$refs.dataGrid.dataSource
      const tmp = []
      data.forEach((item) => {
        if (item.qty > 0) {
          tmp.push({
            orderItemNo: item.orderItemNo, // 供方订单明细行号
            planItemNo: item.itemNo, // 供货计划明细行号
            orderCode: this.headerInfo.orderCode, // 供方订单号
            planId: this.$route.query.planId, // 供货计划主单id
            planItemId: item.id, // 供货计划明细id
            purTenantId: this.headerInfo.purTenantId, // 客户租户id
            qty: item.qty, // 发货数量
            supplierOrderDetailId: item.orderDetailId, // 供方订单明细id
            supplierOrderId: item.orderId // 供方订单id
          })
        }
      })

      return tmp
    },
    // 修改 tab
    handleSelectTab(index, name) {
      this.currentSelectTab = { index, name: name.title }
    },
    // 返回
    goBack() {
      this.$router.push({
        name: 'shipment-collaboration-summary',
        query: {}
      })
    },
    toolbarClick(e) {
      if (e.item.id == 'EmptyQuantity') {
        // 清空数量
        const dataSourceCopy = cloneDeep(this.dataSource)
        dataSourceCopy.forEach((item) => {
          item.qty = ''
          // 更新行编辑数据 本次发货 qty
          this.updataEditDataById({
            id: item.id,
            data: { qty: '' }
          })
        })
        this.dataSource = dataSourceCopy
      } else if (e.item.id == 'AutoFill') {
        // 自动填充 待发货数量 preDeliveryQty
        const dataSourceCopy = cloneDeep(this.dataSource)
        dataSourceCopy.forEach((item) => {
          item.qty = item.preDeliveryQty
          // 更新行编辑数据
          this.updataEditDataById({
            id: item.id,
            data: { qty: item.preDeliveryQty }
          })
        })
        this.dataSource = dataSourceCopy
      }
    },
    // 通过 id 更新指定的行编辑数据
    updataEditDataById({ id, data }) {
      for (let i = 0; i < this.editData.length; i++) {
        if (this.editData[i][id]) {
          this.editData[i][data.key] = data.value
          break
        }
      }
    },
    // 行编辑
    cellEdit(e) {
      // 存行编辑的数据
      if (this.dataSource[e.index].id) {
        let currentEditIndex
        const editItem = this.editData.find((item, index) => {
          currentEditIndex = index
          return item.id == this.dataSource[e.index].id
        })
        if (editItem) {
          // 更新编辑的数据
          editItem[e.key] = e.value
          this.editData.splice(currentEditIndex, 1, editItem)
        } else {
          // 保存编辑的数据
          if (e.value) {
            this.editData.push({
              id: this.dataSource[e.index].id, // 行数据id
              [e.key]: e.value, // 编辑的字段和值
              key: e.key // 编辑的字段
            })
          }
        }
      }
      // 更新当前页 dataSource
      let _dataSource = cloneDeep(this.dataSource)
      _dataSource[e.index][e.key] = e.value
      this.dataSource = _dataSource
    },
    handleCurrentChange(pageIndex) {
      this.currentPage = pageIndex
      // 重新获取表格数据
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      // 重新获取表格数据
      this.getTableData()
    },
    // 表格数据 供方发货计划主单明细
    getTableData() {
      this.apiStartLoading()
      this.$API.shipmentCollaboration
        .postSoPlanItem({
          page: { current: this.currentPage, size: this.pageSettings.pageSize },
          defaultRules: [
            {
              field: 'plan_id',
              operator: 'equal',
              value: this.$route.query.planId
            }
          ]
        })
        .then((res) => {
          if (res?.data?.records) {
            this.pageSettings.totalRecordsCount = res.data.total // 总数
            // 将行编辑过的数据设置到表格中
            this.dataSource = this.setEditedData(res.data.records)
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 将行编辑过的数据设置到表格中
    setEditedData(data) {
      this.editData.forEach((itemEdit) => {
        for (let i = 0; i < data.length; i++) {
          if (data[i].id) {
            data[i][itemEdit.key] = itemEdit.value
            break
          }
        }
      })
      return data
    },
    // 顶部信息获取 供方发货计划主单
    getHeaderInfo() {
      this.apiStartLoading()
      this.$API.shipmentCollaboration
        .postSoPlanHeader({ planId: this.$route.query.planId })
        .then((res) => {
          if (res && res.data) {
            this.headerInfo = {
              ...res.data,
              requiredDeliveryDate: res.data.requiredDeliveryDate
                ? new Date(Number(res.data.requiredDeliveryDate))
                : '' // 要求交期
            }
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 表格列动态获取
    getTableCol() {
      this.columnData = this.formatTableDynamicData(ColumnDataMock)
    },
    // 格式化表格动态数据
    formatTableDynamicData(data) {
      const colData = []
      if (data) {
        data.forEach((col) => {
          const columnCol = {
            ...col,
            field: col.fieldCode,
            headerText: col.fieldName,
            width: '150'
          }

          if (col.fieldCode === 'warehouseStatus') {
            // 入库状态
            columnCol.valueConverter = {
              type: 'map',
              map: {
                0: this.$t('未入库'),
                1: this.$t('部分入库'),
                2: this.$t('全部入库')
              }
            }
          } else if (col.fieldCode === 'receiveStatus') {
            // 收货状态
            columnCol.valueConverter = {
              type: 'map',
              map: {
                0: this.$t('未入库'),
                1: this.$t('部分入库'),
                2: this.$t('全部入库')
              }
            }
          }

          colData.push(columnCol)
        })
        // 末尾 后跟 本次发货
        colData.push(ReceivedQuantityColumnData)
      }

      return colData
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.bottom-tables {
  .mt-tabs,
  .mt-tabs /deep/ .mt-tabs-container {
    width: 100%;
  }
}
</style>
