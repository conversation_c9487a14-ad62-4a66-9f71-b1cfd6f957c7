<template>
  <!-- 选择 物料 -->
  <mt-dialog
    ref="dialog"
    css-class="pc-item-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <div class="full-height">
      <mt-template-page ref="templateRef" :template-config="componentConfig" :hidden-tabs="true" />
    </div>
  </mt-dialog>
</template>

<script>
import Vue from 'vue'
export default {
  data() {
    return {
      dialogTitle: '',
      apiWaitingQuantity: 0, // 调用的api正在等待数
      siteCode: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      componentConfig: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  mounted() {
    const statusList = [
      { value: 0, text: this.$t('进行中'), cssClass: '' },
      { value: 1, text: this.$t('已完成'), cssClass: '' },
      { value: 2, text: this.$t('失败'), cssClass: '' }
    ]
    this.componentConfig = [
      {
        useToolTemplate: false, // 不使用预置(新增、编辑、删除)
        useBaseConfig: true, // 使用组件中的toolbar配置
        toolbar: {
          useBaseConfig: false,
          tools: [[], ['Refresh']]
        },
        grid: {
          allowPaging: false, // 分页
          // lineSelection: 0, // 选项列
          // lineIndex: 1, // 序号列
          height: 'auto',
          columnData: [
            {
              field: 'taskName',
              headerText: this.$t('任务名称')
            },
            {
              field: 'status',
              headerText: this.$t('状态'),
              valueConverter: {
                type: 'map',
                map: statusList
              }
            },
            {
              field: 'fileList',
              headerText: this.$t('文件数'),
              valueConverter: {
                type: 'function',
                filter: (data) => {
                  return data?.length ? `${data.length}` : `0`
                }
              },
              cellTools: [],
              template: function () {
                return {
                  template: Vue.component('exportButton', {
                    template: `<div><span>{{ data?.length ? data.length : 0 }}</span><span v-if="data.length" @click="downLoadFile">{{ $t('下载') }}</span></div>`,
                    data() {
                      return { data: { data: {} } }
                    },
                    methods: {
                      downLoadFile() {}
                    }
                  })
                }
              }
            },
            {
              field: 'progressPercent',
              headerText: this.$t('进度比例')
            }
          ],
          asyncConfig: {
            // 供方送货司机信息维护-获取司机列表
            url: `platform/tenant/task/queryBuilder`,
            defaultRules: [
              // {
              //   field: 'taskCode',
              //   operator: 'contains',
              //   value: this.modalData.taskCode
              // }
            ],
            rules: [
              {
                field: 'taskCode',
                operator: 'contains',
                value: this.modalData.taskCode
              }
            ],
            params: {
              page: { current: 1, size: 20 }
            }
          },
          dataSource: []
          // frozenColumns: 1,
        }
      }
    ]
    this.dialogTitle = this.modalData.title
    this.$refs.dialog.ejsRef.show()
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
    },

    confirm() {
      this.$emit('confirm')
      this.handleClose()
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-data-grid {
  height: 100%;
  > .e-grid {
    height: auto;
    // display: flex;

    > .e-gridcontent {
      flex: 1;
      overflow: auto;
    }
  }

  .e-rowcell.e-active {
    background: #e0e0e0 !important;
  }
}
</style>
