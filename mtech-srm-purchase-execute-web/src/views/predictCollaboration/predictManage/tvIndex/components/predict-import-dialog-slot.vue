<template>
  <div
    class="full-height vertical-flex-box"
    id="predict-import-dialog-slot"
    ref="dialogSlotContainer"
  >
    <div id="toolbar-container" class="flex-keep">
      <div class="tool-left">
        <div class="delete-btn" @click="handleDelete()">
          <span><mt-icon name="icon_table_delete"></mt-icon></span>
          <span>{{ $t('删除') }}</span>
        </div>
      </div>
      <div class="tool-right">
        <div class="data-switch">
          <mt-switch
            v-model="isShowCacheError"
            @change="isShowCacheErrorChange"
            :on-label="$t('仅显示错误数据')"
            :off-label="$t('所有数据')"
          ></mt-switch>
        </div>
      </div>
    </div>
    <div class="flex-fit">
      <mt-data-grid
        class="custom-toolbar-grid"
        :data-source="forecastDialogDataSource"
        :column-data="columnData"
        ref="dataGrid"
        :allow-paging="allowPaging"
        :edit-settings="EditSettings"
        :page-settings="forecastPageSettings"
        :query-cell-info="queryCellInfo"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
        @handleClickCellTool="handleClickCellTool"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
        @dataBound="handleDataBound"
      ></mt-data-grid>
    </div>
  </div>
</template>

<script>
import { getScrollbarWidth } from '@/utils/utils'
import {
  forecastDialogDataSource,
  forecastDialogColumnData,
  rowDataTemp,
  dynamicTitleDialog
} from '../config/variable'
import {
  EditSettings,
  RequestType,
  ActionType,
  ConstDynamicTitleStr,
  ForecastColumnData,
  ForecastDialogColumnData,
  PagerHeight,
  ToolbarHeight,
  TheadHeight,
  EmptyMsgHeight,
  ForecastDialogColumnDataDoNotShow
} from '../config/constant'
import {
  formatTableDialogColumnData,
  rowDataToForecastInfo,
  formatTableDataSource
} from '../config/index'
import { cloneDeep } from 'lodash'

export default {
  components: {},
  props: {},
  watch: {},
  data() {
    return {
      isShowCacheError: false, // 仅显示错误信息
      EditSettings, // 编辑设置
      forecastDialogDataSource, // 预测弹框 表格数据
      columnData: forecastDialogColumnData, // 表头
      allowPaging: true, // 显示分页角标
      forecastPageSettings: {
        pageSize: 10, // 当前每页数据量
        totalRecordsCount: 0, // 总条数
        pageSizes: [10, 20, 30]
      },
      forecastPageCurrent: 0, // 预测表格 当前页码
      isEditing: false, // 正在编辑数据
      apiWaitingQuantity: 0, // 调用的api正在等待数
      tableContainerClientHeight: 0
    }
  },
  mounted() {},
  methods: {
    // 初始化
    init(args) {
      const { forecastCacheCode } = args
      this.forecastCacheCode = forecastCacheCode
      this.isShowCacheError = false
      // 页面初始化，获取表格区域预留的高度
      this.tableContainerClientHeight = this.$refs.dialogSlotContainer.clientHeight || 0
      // 采方预测信息-查询导入数据
      this.getBuyerForecastCacheList()
      // 初始化表头数据
      this.handleColumns({ titleList: [] })
    },
    // 调整单元格的样式
    queryCellInfo() {
      // const { column, cell } = args;
      // if (
      //   dynamicTitleDialog.includes(`${ConstDynamicTitleStr}${column.field}`) ||
      //   "forecastDataType" === column.field
      // ) {
      //   cell.classList.add("forecast-td");
      // }
    },
    // actionBegin 表格编辑生命周期
    actionBegin(args) {
      const { requestType, action, rowData } = args
      // console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args);
      if (requestType === RequestType.save && action === ActionType.add) {
        // 即将保存新增时，将新增中编辑的数据付给组件
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
        // 将新增的数据添加在未新增前的index处
        args.index = this.$refs.dataGrid.ejsRef.getCurrentViewRecords()?.length
      } else if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === RequestType.beginEdit) {
        // 开始行编辑
        this.isEditing = true
        // 即将编辑行，赋值当前行的数据
        rowDataTemp.length = 0
        rowDataTemp.push(rowData)
      }
    },
    // actionComplete 表格编辑生命周期
    actionComplete(args) {
      const { requestType, action, rowIndex } = args
      // console.log(
      //   `Complete,\nrequest: ${requestType}\naction: ${action}`,
      //   args
      // );
      if (requestType === RequestType.save && action == ActionType.edit) {
        // 完成编辑行
        this.isEditing = false
        this.postBuyerForecastCacheList({
          data: rowDataTemp[rowDataTemp.length - 1],
          rowIndex
        })
      } else if (requestType === RequestType.sorting || requestType === RequestType.refresh) {
        // 丢弃编辑中状态
        this.isEditing = false
      }
    },
    // 校验数据
    isValidData(data) {
      const { planGroupId, buyerOrgId, itemCode, factoryCode, supplierCode } = data
      let valid = false
      if (!itemCode) {
        // 物料代码
        this.$toast({ content: this.$t('物料不可为空'), type: 'warning' })
      } else if (!factoryCode) {
        // 工厂代码
        this.$toast({ content: this.$t('工厂不可为空'), type: 'warning' })
      } else if (!supplierCode) {
        // 供应商代码
        this.$toast({ content: this.$t('供应商不可为空'), type: 'warning' })
      } else if (!planGroupId) {
        // 计划组
        this.$toast({ content: this.$t('计划组不可为空'), type: 'warning' })
      } else if (!buyerOrgId) {
        // 采购组
        this.$toast({ content: this.$t('采购组不可为空'), type: 'warning' })
      } else {
        valid = true
      }

      return valid
    },
    // CellTool
    handleClickCellTool(e) {
      if (this.isEditing) {
        // 结束编辑状态
        this.$refs.dataGrid.ejsRef.endEdit()
        return
      }

      if (e.tool.id === 'ForecastDelete') {
        // 删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            this.postBuyerForecastBatchCacheDelete([e.data.id])
          }
        })
      }
    },
    // 采方预测信息-查询导入数据
    getBuyerForecastCacheList() {
      const params = {
        limit: this.forecastPageSettings.pageSize,
        start: this.forecastPageCurrent,
        code: this.forecastCacheCode
      }
      this.apiStartLoading()
      this.$API.predictCollaboration
        .getBuyerForecastCacheList(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.forecastPageSettings.totalRecordsCount = res?.data?.total || 0
            const records = res?.data?.list || [] // 表格数据
            const titleList = res?.data?.titleList || [] // 动态表头数据
            // 处理表头数据
            this.handleColumns({ titleList })
            // 处理表数据
            this.handleDataSource({ records, titleList })
            this.$refs.dataGrid.refresh() // 更新表格数据
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方预测信息-查询导入错误数据
    getBuyerForecastCacheErrorList() {
      const params = {
        limit: this.forecastPageSettings.pageSize,
        start: this.forecastPageCurrent,
        code: this.forecastCacheCode
      }
      this.apiStartLoading()
      this.$API.predictCollaboration
        .getBuyerForecastCacheErrorList(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.forecastPageSettings.totalRecordsCount = res?.data?.total || 0
            const records = res?.data?.list || [] // 表格数据
            const titleList = res?.data?.titleList || [] // 动态表头数据
            // 处理表头数据
            this.handleColumns({ titleList })
            // 处理表数据
            this.handleDataSource({ records, titleList })
            this.$refs.dataGrid.refresh() // 更新表格数据
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方预测信息-修改导入数据
    postBuyerForecastCacheList(args) {
      const { data, rowIndex } = args
      if (!this.isValidData(data)) {
        // 当出现错误时，指定行进入编辑状态
        this.$refs.dataGrid.ejsRef.selectRow(rowIndex)
        this.$refs.dataGrid.ejsRef.startEdit()
        return
      }
      const forecastInfo = rowDataToForecastInfo({
        dynamicTitleList: dynamicTitleDialog,
        rowData: data
      })
      const params = {
        code: this.forecastCacheCode,
        forecastInfo, //	预测信息
        id: data.id, // id
        buyerOrgId: data.buyerOrgId, // 采购组id
        buyerOrgName: data.buyerOrgName, // 采购组名称
        companyId: data.companyId, // 公司id
        companyName: data.companyName, // 公司名称
        factoryCode: data.factoryCode, // 工厂code
        factoryId: data.factoryId, // 工厂id
        factoryName: data.factoryName, // 工厂名称
        itemCode: data.itemCode, // 物料编码
        itemId: data.itemId, // 物料id
        itemName: data.itemName, // 物料名称
        manufacturer: data.manufacturer, // 制造商
        mrpArea: data.mrpArea, // MRP区域
        packagingManufacturer: data.packagingManufacturer, // 包装厂商
        packagingOrigin: data.packagingOrigin, // 包装产地
        percentage: data.percentage, // 配额
        planGroup: data.planGroup, // 计划组
        planGroupId: data.planGroupId, // 计划组id
        productRel: data.productRel, // 关联产品（机型、机芯）
        rawMaterialManufacturer: data.rawMaterialManufacturer, // 关键原材厂商
        rawMaterialOrigin: data.rawMaterialOrigin, // 关键原材产地
        specialUse: data.specialUse, // 制造商专用状
        storageQty: data.storageQty, // 已入库数量
        supplierCode: data.supplierCode, // 供应商code
        supplierId: data.supplierId, // 供应商id
        supplierName: data.supplierName, // 供应商名称
        undeliveredOrderQty: data.undeliveredOrderQty, // 未交PO
        purRemark: data.purRemark // 采方备注
      }
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerForecastCacheList(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 更新表格数据
            this.updateTableData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.dataGrid.ejsRef.selectRow(rowIndex)
          this.$refs.dataGrid.ejsRef.startEdit()
        })
    },
    // 采方预测信息-删除导入数据
    postBuyerForecastBatchCacheDelete(idList) {
      const params = {
        code: this.forecastCacheCode,
        idList: idList
      }
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerForecastBatchCacheDelete(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 更新表格数据
            this.updateTableData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方预测信息-导入数据提交
    getBuyerForecastCacheSubmit() {
      const params = { code: this.forecastCacheCode }
      this.apiStartLoading()
      this.$API.predictCollaboration
        .getBuyerForecastCacheSubmit(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('submitted')
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 更新表格数据
    updateTableData() {
      if (this.isShowCacheError) {
        // 采方预测信息-查询导入错误数据
        this.getBuyerForecastCacheErrorList()
      } else {
        // 采方预测信息-查询导入数据
        this.getBuyerForecastCacheList()
      }
    },
    // 处理表头数据
    handleColumns(data) {
      const { titleList } = data
      forecastDialogColumnData.length = 0 // 清空表头数据
      dynamicTitleDialog.length = 0 // 清空 Template 使用的表头列表
      const titleListColumnData = []
      titleList.forEach((item, index) => {
        dynamicTitleDialog.push(`${ConstDynamicTitleStr}${titleList[index]}`)
        titleListColumnData.push({
          fieldCode: item,
          fieldName: item,
          isForecast: true, // 预测数据的标志
          colIndex: index // 列的 index
        })
      })
      // 固定的表头
      const forecastColumns = formatTableDialogColumnData(ForecastColumnData)
      // 动态的日期表头
      const titleListColumns = formatTableDialogColumnData(titleListColumnData)
      // 特殊的 是否异常、错误信息
      const forecastDialogColumns = formatTableDialogColumnData(ForecastDialogColumnData)
      // 合并表头数组，然后 push 到 forecastDialogColumnData 中
      const columns = []
        .concat(forecastColumns)
        .concat(titleListColumns)
        .concat(forecastDialogColumns)
      columns.forEach((item) => {
        if (!ForecastDialogColumnDataDoNotShow.includes(item.fieldCode)) {
          forecastDialogColumnData.push(item)
        }
      })
    },
    // 处理表数据
    handleDataSource(data) {
      const { records, titleList } = data
      forecastDialogDataSource.length = 0 // 清空表格数据

      const dataSource = cloneDeep(formatTableDataSource({ records, titleList }))
      dataSource.forEach((item) => {
        // 将每一项 push 到 forecastDialogDataSource 中
        forecastDialogDataSource.push(item)
      })
    },
    // 点击弹框的导入按钮 提交数据
    doImport() {
      // 采方预测信息-导入数据提交
      this.getBuyerForecastCacheSubmit()
    },
    // 删除按钮 批量删除
    handleDelete() {
      const selectedRecords = this.$refs.dataGrid.ejsRef.getSelectedRecords()

      if (selectedRecords.length == 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })
      this.postBuyerForecastBatchCacheDelete(idList)
    },
    // 是否仅显示错误数据切换
    isShowCacheErrorChange(e) {
      if (e) {
        // 采方预测信息-查询导入错误数据
        this.getBuyerForecastCacheErrorList()
      } else {
        // 采方预测信息-查询导入数据
        this.getBuyerForecastCacheList()
      }
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      // 更新表格数据
      this.updateTableData()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      // 更新表格数据
      this.updateTableData()
    },
    // 表格数据绑定完成
    handleDataBound() {
      this.resizeGridHeight()
    },
    // 自定义计算表格高度
    resizeGridHeight() {
      // 表格数据容器
      const tableContent = document.querySelector(
        '#predict-import-dialog-slot .e-gridcontent .e-content'
      )
      if (tableContent) {
        let height =
          this.tableContainerClientHeight -
          ToolbarHeight -
          TheadHeight -
          PagerHeight -
          getScrollbarWidth()
        // 没有数据时
        if (forecastDialogDataSource.length === 0) {
          height =
            this.tableContainerClientHeight -
            ToolbarHeight -
            TheadHeight -
            EmptyMsgHeight -
            PagerHeight -
            getScrollbarWidth()
        }
        setTimeout(() => {
          tableContent.style.cssText = `height: ${height}px;`
        }, 500)
      }
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
#toolbar-container {
  display: flex;
  justify-content: space-between;

  .delete-btn {
    color: #4f5b6d;
    height: 42px;
    line-height: 42px;
    width: 66px;
    text-align: center;
    cursor: pointer;
  }
  .data-switch {
    line-height: 42px;
  }
}

/deep/ .errorText {
  color: #ed5633;
}

/deep/ .custom-toolbar-grid {
  height: 100%;
  display: flex;
  flex-direction: column;

  .e-grid {
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
    height: 100%;
    overflow: auto;

    .e-gridheader {
      flex-shrink: 0;
    }
    .e-gridcontent {
      overflow: hidden;
      .e-content {
        overflow: scroll;
        height: 100%;
      }
    }
    // 固定首列
    // .e-table {
    //   & thead th:first-child {
    //     position: sticky;
    //     left: 0px;
    //     z-index: 1;
    //     border-right: 1px solid var(--plugin-dg-shadow-color);
    //   }

    //   & tbody td:first-child {
    //     position: sticky;
    //     left: 0px;
    //     z-index: 1;
    //     border-right: 1px solid var(--plugin-dg-shadow-color);
    //     background-color: #fff;
    //   }
    // }
  }
  .mt-pagertemplate {
    flex: 0 0 auto;
  }
}
</style>
