<template>
  <!-- 选择 物料 -->
  <mt-dialog
    ref="dialog"
    css-class="pc-item-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <div class="full-height">
      <mt-template-page
        ref="templateRef"
        :template-config="componentConfig"
        :hidden-tabs="true"
        @recordDoubleClick="recordDoubleClick"
      />
    </div>
  </mt-dialog>
</template>

<script>
import { materielTableColumnData } from '../config/index.js'
import { MaterielTableColumnData } from '../config/constant'
import { PROXY_MDM_TENANT } from '@/utils/constant'

export default {
  data() {
    return {
      dialogTitle: '',
      apiWaitingQuantity: 0, // 调用的api正在等待数
      siteCode: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      componentConfig: [
        // {
        //   useToolTemplate: false, // 不使用预置(新增、编辑、删除)
        //   useBaseConfig: true, // 使用组件中的toolbar配置
        //   toolbar: [],
        //   gridId: "1d129d28-e515-d77e-37fe-47f45e1d75dd",
        //   grid: {
        //     allowPaging: true, // 分页
        //     // lineSelection: 0, // 选项列
        //     // lineIndex: 1, // 序号列
        //     allowSelection: true,
        //     selectionSettings: {
        //       checkboxOnly: false,
        //     },
        //     columnData: materielTableColumnData({
        //       data: MaterielTableColumnData,
        //     }),
        //     asyncConfig: {
        //       // 供方送货司机信息维护-获取司机列表
        //       url: `${PROXY_MDM_TENANT}/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
        //       defaultRules: [
        //         {
        //           field: "organizationCode",
        //           operator: "equal",
        //           value: "",
        //         },
        //       ],
        //       rules: [],
        //     },
        //     dataSource: [],
        //     // frozenColumns: 1,
        //   },
        // },
      ]
    }
  },
  mounted() {
    this.$bus.$on(`siteCodeChangeItem`, (e) => {
      const { data } = e
      this.componentConfig = [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          gridId: '1d129d28-e515-d77e-37fe-47f45e1d75dd',
          grid: {
            allowPaging: true, // 分页
            // lineSelection: 0, // 选项列
            // lineIndex: 1, // 序号列
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: materielTableColumnData({
              data: MaterielTableColumnData
            }),
            asyncConfig: {
              // 供方送货司机信息维护-获取司机列表
              url: `${PROXY_MDM_TENANT}/item/paged-query?BU_CODE=${localStorage.getItem(
                'currentBu'
              )}`,
              defaultRules: [
                {
                  field: 'organizationCode',
                  operator: 'equal',
                  value: data
                }
              ],
              rules: []
            },
            dataSource: []
            // frozenColumns: 1,
          }
        }
      ]
    })
  },

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      const { title } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.refreshColumns()
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },

    confirm() {
      const selectedRowData = this.$refs.templateRef
        .getCurrentUsefulRef()
        .gridRef.getMtechGridRecords()
      this.$emit('confirm', { data: selectedRowData })
      this.handleClose()
    },
    // 双击物料行，也进行提交
    recordDoubleClick(args) {
      const { rowData } = args
      // const selectedRowData = this.$refs.templateRef
      //   .getCurrentUsefulRef()
      //   .gridRef.getMtechGridRecords();
      this.$emit('confirm', { data: [rowData] })
      this.handleClose()
    },

    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-data-grid {
  height: 100%;
  > .e-grid {
    height: auto;
    // display: flex;

    > .e-gridcontent {
      flex: 1;
      overflow: auto;
    }
  }

  .e-rowcell.e-active {
    background: #e0e0e0 !important;
  }
}
</style>
