<template>
  <!-- 预测管理-采方 -->
  <div class="full-height pt20 vertical-flex-box">
    <!-- 头部 -->
    <!-- <div class="flex-keep">
      <div class="forecast-template">
        <span>{{ $t('预测模版：') }}</span>
        <mt-select
          class="forecast-template-select"
          v-model="forecastTemplate"
          :data-source="ForecastTemplateOptions"
          :disabled="isForecastTemplateDisabled"
          @change="forecastTemplateChange"
          :placeholder="$t('请选择')"
        ></mt-select>
      </div>
    </div> -->
    <!-- 自定义查询条件 -->
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <mt-template-page
        ref="templateRef"
        class="frozenColumns"
        :template-config="componentConfig"
        :hidden-tabs="true"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
        @dataBound="handleDataBound"
        @handleSearch="handleSearch"
        @handleCustomReset="handleCustomReset"
      >
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="factoryCodes" :label="$t('工厂')" label-style="top">
                <RemoteAutocomplete
                  style="flex: 1"
                  v-model="searchFormModel.factoryCodes"
                  :url="$API.masterData.getSiteListUrl"
                  multiple
                  :placeholder="$t('请选择工厂')"
                  :fields="{ text: 'siteName', value: 'siteCode' }"
                  :search-fields="['siteName', 'siteCode']"
                ></RemoteAutocomplete>
              </mt-form-item>
              <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
                <mt-input v-model="searchFormModel.itemCode" />
              </mt-form-item>
              <!-- <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input v-model="searchFormModel.itemName" />
        </mt-form-item> -->
              <!-- <mt-form-item prop="planner" :label="$t('计划员')" label-style="top">
          <debounce-filter-select
            v-model="searchFormModel.planner"
            :request="getPlanGroupList"
            :data-source="planGroupOptions"
            :show-clear-button="true"
            :fields="{ text: 'theCodeName', value: 'groupCode' }"
            :value-template="planGroupValueTemplate"
            @change="planGroupCodeChange"
            :placeholder="$t('请选择')"
          ></debounce-filter-select>
        </mt-form-item> -->
              <mt-form-item prop="planner" :label="$t('计划员')" label-style="top">
                <mt-multi-select
                  style="flex: 1"
                  v-model="searchFormModel.planner"
                  :data-source="plannerListOptions"
                  :fields="{ text: 'codeAndName', value: 'userCode' }"
                  filter-type="Contains"
                  :placeholder="$t('请选择')"
                ></mt-multi-select>
              </mt-form-item>
              <mt-form-item prop="supplierCodes" :label="$t('供应商编码')" label-style="top">
                <RemoteAutocomplete
                  style="flex: 1"
                  v-model="searchFormModel.supplierCodes"
                  url="/masterDataManagement/tenant/supplier/paged-query"
                  multiple
                  :placeholder="$t('请选择供应商')"
                  :fields="{ text: 'supplierName', value: 'supplierCode' }"
                  :search-fields="['supplierName', 'supplierCode']"
                ></RemoteAutocomplete>
              </mt-form-item>
              <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
                <mt-input v-model="searchFormModel.supplierName"></mt-input>
              </mt-form-item>
              <mt-form-item prop="status" :label="$t('状态')" label-style="top">
                <mt-multi-select
                  v-model="searchFormModel.status"
                  :data-source="statusOptions"
                  :placeholder="$t('请选择')"
                ></mt-multi-select>
              </mt-form-item>
              <mt-form-item prop="mrpArea" :label="$t('MRP区域')" label-style="top">
                <mt-input v-model="searchFormModel.mrpArea"></mt-input>
              </mt-form-item>
              <!-- <mt-form-item prop="publishDate" :label="$t('发布日期')" label-style="top">
          <mt-date-picker
            v-model="searchFormModel.publishDate"
            :show-clear-button="true"
            :placeholder="$t('请选择发布日期')"
          ></mt-date-picker>
        </mt-form-item> -->
              <!-- <mt-form-item prop="syncVersion" :label="$t('版本')" label-style="top">
          <mt-input v-model="searchFormModel.syncVersion"></mt-input>
        </mt-form-item> -->
              <!-- <mt-form-item
          prop="forecastType"
          :label="$t('类型')"
          label-style="top"
        >
          <mt-multi-select
            :show-clear-button="true"
            :data-source="forecastTypeOptions"
            v-model="searchFormModel.forecastType"
          />
        </mt-form-item> -->
            </mt-form>
          </div>
        </template>
      </mt-template-page>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
    <!-- 预测数据导入弹框 -->
    <!-- <import-dialog
      :accept="['.xls', '.xlsx']"
      :from-data-key="fromDataKey"
      :save-url="saveUrl"
      :down-load="true"
      ref="importDialog"
      :request-urls="requestUrls"
      dialog-class="create-proj-dialog full-size-dialog"
      @import="importDialogImport"
      @uploadCompleted="uploadCompleted"
    >
      <predict-import-dialog-slot
        ref="predictImportDialogSlot"
        @submitted="predictImportDialogSlotSubmitted"
      ></predict-import-dialog-slot>
    </import-dialog> -->
    <!-- 物料选择 弹框 -->
    <materiel-select-dialog
      ref="materielSelectDialog"
      @confirm="materielSelectDialogConfirm"
    ></materiel-select-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { formatTableColumnData, formatTableDataSource } from './config/index'
import {
  forecastDataSource,
  forecastColumnData,
  dynamicTitle,
  rowDataTemp,
  editLinkDataParams
} from './config/variable'
import {
  ToolbarTv,
  EditSettings,
  ForecastTemplateOptions,
  ForecastColumnData,
  ConstDynamicTitleStr,
  RequestType,
  ActionType,
  NewRowData,
  DynamicItemInit,
  Status,
  ComponentChangeType
} from './config/constant'
import MaterielSelectDialog from './components/materielSelectDialog'
// import PredictImportDialogSlot from './components/predict-import-dialog-slot'
// import ImportDialog from '@/components/Upload/importDialog'
import { StatusSearchOptions, ForecastTypeDataSource } from './config/constant'
// import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
export default {
  components: {
    // ImportDialog,
    // PredictImportDialogSlot,
    MaterielSelectDialog
    // DebounceFilterSelect
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {
        // status: '',
        // factoryCode: [],
        // itemCode: '',
        // itemName: '',
        // supplierCode: [],
        // syncVersion: '',
        // buyerOrgCode: [],
        // companyCode: [],
        // forecastType: [],
        // planGroupCode: [],
        // publishDate: '',
        supplierName: '',
        itemCode: '',
        status: [],
        factoryCodes: [],
        supplierCodes: [],
        mrpArea: ''
      },
      syncVersion: '',
      titleList: [],
      plannerListOptions: [], // 计划员 下列选项
      planGroupOptions: [], // 计划组 下列选项
      planGroupValueTemplate: codeNameColumn({
        firstKey: 'groupCode',
        secondKey: 'groupName'
      }), // 计划组
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          toolbar: { tools: [ToolbarTv, ['Setting']] }, // 此页面为动态数据关联的动态表格，故不可设置字段显示隐藏
          gridId: '31993154-c875-4333-879f-d06249446b12',
          grid: {
            height: 566,
            virtualPageSize: 30,
            enableVirtualization: true,
            gridLines: 'Both',
            allowPaging: false, // 不分页
            allowReordering: false, // 不可拖动排序 因为预测数据是动态获取到的，表格记忆排序不能处理
            // frozenColumns: 1, // 冻结第一列 使用此设置表格数据将不可见，已使用 frozenFistColumns 实现
            columnData: formatTableColumnData(ForecastColumnData),
            dataSource: [],
            editSettings: EditSettings,
            defaultSearchItem: [
              {
                field: 'status', // 自动生成，不可编辑
                headerText: this.$t('状态')
              }
            ]
          }
        }
      ],
      requestUrls: {
        templateUrlPre: 'deliveryConfig',
        templateUrl: 'buyerJitInfoexport' // 下载模板接口方法名
      },
      fromDataKey: 'excel', // 上传组件的 name，此值为调用 api 请求参数的 key
      saveUrl: `/srm-purchase-execute/internal/forecast/buyer/v1/excel/import`, // 导入API
      apiWaitingQuantity: 0, // 调用的api正在等待数
      isForecastTemplateDisabled: false, // 预测模版可编辑
      ForecastTemplateOptions, // 预测模板 Options
      manage: 'tv',
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 10, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 50, 100, 200]
      },
      forecastPageCurrent: 1, // 预测表格 当前页码
      forecastRules: [], // 预测表格请求规则
      forecastCondition: 'and', // 过滤-规则-关系，默认为 "and"
      isEditing: false, // 正在编辑数据
      exportRules: [], //导出规则
      statusOptions: StatusSearchOptions,
      forecastTypeOptions: ForecastTypeDataSource,
      copySearchFormModel: null
    }
  },
  mounted() {
    this.getPlannerList()
    // this.$refs.templateRef.$refs.searchRef[0].$children[0].searchFormObject
    this.copySearchFormModel = { ...this.searchFormModel }
  },
  methods: {
    getPlannerList() {
      this.$API.predictCollaboration.getPlannerAllName().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.plannerListOptions = data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.userCode} - ${i.userName}`
            }
          })
        }
      })
    },
    // 计划组 change
    planGroupCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.forecastTemplate.planGroupId = itemData.id
        this.forecastTemplate.planGroupCode = itemData.groupCode
        this.forecastTemplate.planGroupName = itemData.groupName
      } else {
        this.forecastTemplate.planGroupId = ''
        this.forecastTemplate.planGroupCode = ''
        this.forecastTemplate.planGroupName = ''
      }
    },
    // 获取主数据-计划组
    getPlanGroupList(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        groupTypeCode: 'BG001JH'
      }
      this.$API.masterData
        .getbussinessGroup(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.planGroupOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.planGroupOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 重置
    handleCustomReset() {
      this.searchFormModel = { ...this.copySearchFormModel }
    },
    // 查询
    handleCustomSearch() {
      // let _params = this.getRules();
      this.postBuyerForecastQuery()
    },
    // 获取参数
    getRules() {
      const rules = []
      const keys = Object.keys(this.searchFormModel)
      const values = Object.values(this.searchFormModel)
      const inArr = [
        'orgId',
        'factoryCodes',
        'itemCode',
        'supplierCodes',
        'buyerOrgCode',
        'companyCode',
        'forecastType',
        'planGroupCode'
      ]
      for (let i = 0; i < values.length; i++) {
        const element = values[i]
        if ((!inArr.includes(keys[i]) && element) || element === 0 || element?.length > 0) {
          let obj = {
            field: keys[i],
            label: '',
            operator: keys[i] === 'status' ? 'in' : inArr.includes(keys[i]) ? 'in' : 'contains',
            type: 'string',
            value: element
          }
          rules.push(obj)
        }
      }
      return rules
    },
    // toolbar 按钮点击
    handleClickToolBar(args) {
      const { toolbar, grid, rules } = args

      const selectedRecords = grid.getSelectedRecords()
      const commonToolbar = [
        'ForecastUpdate',
        'ForecastAdd',
        'ForecastImport',
        'Filter',
        'ForecastDelete',
        'ForecastPublish',
        'ForecastConfirm',
        'Refresh',
        'refreshDataByLocal',
        'filterDataByLocal',
        'CloseEdit',
        'resetDataByLocal',
        'ForecastExport',
        'Setting'
      ]
      if (toolbar.id === 'CloseEdit') {
        this.postBuyerForecastQuery()
        return
      }
      if (this.isEditing && toolbar.id !== 'refreshDataByLocal') {
        // 结束编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }

      if (selectedRecords.length == 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })

      if (toolbar.id === 'ForecastAdd') {
        // 新增
        const currentViewRecords = this.$refs.templateRef
          .getCurrentUsefulRef()
          .gridRef.ejsRef.getCurrentViewRecords()
        if (!currentViewRecords.length) {
          this.$toast({
            content: this.$t('请先查询数据再进行新增操作'),
            type: 'warning'
          })
          return
        }
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
        // } else if (toolbar.id === 'ForecastCancelPublish') {
        //   // 取消发布
        //   this.$dialog({
        //     data: {
        //       title: this.$t('提示'),
        //       message: this.$t('确认取消发布选中的数据？')
        //     },
        //     success: () => {
        //       this.postBuyerForecastCancel(idList)
        //     }
        //   })
      } else if (toolbar.id === 'ForecastDelete') {
        // 删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: idList.length
              ? this.$t('确认删除选中的数据？')
              : this.$t('确认删除符合筛选条件的数据？')
          },
          success: () => {
            this.postBuyerForecastDelete(idList)
          }
        })
      } else if (toolbar.id === 'ForecastPublish') {
        // 发布
        this.postBuyerForecastPublish(idList)
      } else if (toolbar.id === 'ForecastConfirm') {
        // 确认
        this.postBuyerForecastConfirm(idList)
      } else if (toolbar.id === 'ForecastImport') {
        // // 导入
        // this.$refs.importDialog.init({
        //   title: this.$t('导入')
        // })
        this.$dialog({
          modal: () => import('@/components/uploadDialog'),
          data: {
            title: this.$t('导入'),
            importApi: this.$API.predictCollaboration.postBuyerForecastImportTv,
            downloadTemplateApi: this.$API.predictCollaboration.postBuyerForecastExportTemplateTv,
            paramsKey: 'excel'
            // saveButtonText: this.$t('下一步')
            // asyncParams: {
            //   // requestJson: JSON.stringify(parameter),
            // },
          },
          success: () => {
            // 导入之后刷新列表
            // this.$refs.templateRef.refreshCurrentGridData()
            this.postBuyerForecastQuery()
          }
        })
        return
      } else if (toolbar.id === 'ForecastExport') {
        // 导出
        // if (selectedRecords.length == 0) {
        //   this.$toast({ content: this.$t('请先选择需要导出的数据'), type: 'warning' })
        //   return
        // }
        this.postBuyerForecastExport(selectedRecords)
      } else if (toolbar.id === 'refreshDataByLocal') {
        // 刷新 采方-获取采方预测信息列表
        // this.postBuyerForecastQuery();
      } else if (toolbar.id === 'filterDataByLocal') {
        // 筛选-过滤

        const { condition, rules: ruleList } = rules
        this.forecastCondition = condition
        this.forecastRules = ruleList
        // 不点查询不调接口
        this.forecastPageCurrent = 1
        if (this.manage === 'tv') {
          if (this.forecastRules.length > 0) {
            this.postBuyerForecastQuery()
          }
        } else {
          this.postBuyerForecastQuery()
        }
        // 采方-获取采方预测信息列表
      } else if (toolbar.id === 'resetDataByLocal') {
        // 筛选重置
        this.forecastCondition = 'and'
        this.forecastRules = []
        // 采方-获取采方预测信息列表
        this.forecastPageCurrent = 1
        this.postBuyerForecastQuery()
      } else if (toolbar.id === 'ForecastUpdate') {
        // 更新-结束行编辑
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },
    // CellTool
    handleClickCellTool(e) {
      if (this.isEditing) {
        // 结束编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }

      if (e.tool.id === 'ForecastDelete') {
        // 删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            this.postBuyerForecastDelete([e.data.id])
          }
        })
      } else if (e.tool.id === 'ForecastPublish') {
        // 发布
        this.postBuyerForecastPublish([e.data.id])
      } else if (e.tool.id === 'ForecastCancelPublish') {
        // 取消发布
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认取消发布选中的数据？')
          },
          success: () => {
            this.postBuyerForecastCancel([e.data.id])
          }
        })
      } else if (e.tool.id === 'ForecastConfirm') {
        // 确认
        this.postBuyerForecastConfirm([e.data.id])
      }
    },
    // 预测数据导入编辑弹框 点击导入
    importDialogImport() {
      this.$refs.predictImportDialogSlot.doImport()
    },
    // 文件上传完成
    // uploadCompleted(response) {
    //   this.$refs.importDialog.showStepSecond()
    //   this.$nextTick(() => {
    //     this.$refs.predictImportDialogSlot.init({
    //       forecastCacheCode: response?.data || ''
    //     })
    //   })
    // },
    // 导入提交
    // predictImportDialogSlotSubmitted() {
    //   this.$refs.importDialog.handleClose()
    // },
    addNewRow() {
      const dynamicItem = {}
      dynamicTitle.forEach((itemTitle) => {
        const timeInfo = itemTitle.substring(ConstDynamicTitleStr.length)
        dynamicItem[itemTitle] = cloneDeep(DynamicItemInit)
        dynamicItem[itemTitle].timeInfo = timeInfo // 初始数据的timeInfo
      })
      return {
        ...dynamicItem,
        ...NewRowData
      }
    },
    // actionBegin 表格编辑生命周期
    actionBegin(args) {
      const { requestType, action, rowData } = args
      // console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args);
      if (requestType === RequestType.add) {
        // 开始行添加
        this.isEditing = true
        // 即将添加一行，赋值新增行的初始数据
        rowDataTemp.length = 0
        editLinkDataParams.itemCode = ''
        rowDataTemp.push(this.addNewRow())
        args.rowData = this.addNewRow()
        args.data = this.addNewRow()
      } else if (requestType === RequestType.save && action === ActionType.add) {
        // 即将保存新增时，将新增中编辑的数据付给组件
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === RequestType.beginEdit) {
        // 已确认的数据不可修改
        const disAbleEditStatus = [Status.confirmed]
        if (disAbleEditStatus.includes(rowData.status)) {
          args.cancel = true
          this.$toast({
            content: this.$t('此状态数据不可编辑'),
            type: 'warning'
          })
          return
        }
        // 开始行编辑
        this.isEditing = true
        // 即将编辑行，赋值当前行的数据
        rowDataTemp.length = 0
        rowDataTemp.push(rowData)
      }
    },
    // actionComplete 表格编辑生命周期
    actionComplete(args) {
      console.log('actionComplete', args)
      const { requestType, action, rowIndex, index } = args
      // console.log(
      //   `Complete,\nrequest: ${requestType}\naction: ${action}`,
      //   args
      // );
      if (requestType === RequestType.save && action == ActionType.edit) {
        // 完成编辑行
        this.isEditing = false
        // 通过 id 判断这条数据是否为新增请求后，报错，自动再次编辑的数据
        if (rowDataTemp[rowDataTemp.length - 1].id) {
          // 有 id，此数据为行编辑的数据
          this.postBuyerForecastSaveForecast({
            data: rowDataTemp[rowDataTemp.length - 1],
            rowIndex
          })
        } else {
          // 无 id，此数据为新增错误，再次编辑的数据
          this.postBuyerForecastBatchInsert({
            data: rowDataTemp[rowDataTemp.length - 1],
            rowIndex
          })
        }
      } else if (requestType === RequestType.save && action == ActionType.add) {
        // 完成新增行
        this.isEditing = false
        this.postBuyerForecastBatchInsert({
          data: rowDataTemp[rowDataTemp.length - 1],
          rowIndex: index
        })
      } else if (requestType === RequestType.sorting || requestType === RequestType.refresh) {
        // 丢弃编辑中状态
        this.isEditing = false
      }
    },
    // 采方-批量写入预测数据
    postBuyerForecastBatchInsert(args) {
      const { data, rowIndex } = args
      if (!this.isValidData(data)) {
        // 当出现错误时，指定行进入编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        return
      }
      const tvForecastRecord = []
      if (this.titleList && this.titleList.length) {
        this.titleList.forEach((title) => {
          const item = data[`title_${title}`]['buyerNum']
          if (item) {
            tvForecastRecord.push(String(item))
          } else {
            tvForecastRecord.push('0')
          }
        })
      }
      const params = {
        tvForecastInsertDtoList: [
          {
            syncVersion: this.syncVersion,
            tvForecastModel: {
              factoryCode: data.factoryCode,
              itemCode: data.itemCode,
              itemName: data.itemName,
              keyRawMaterialOrigin: data.keyRawMaterialOrigin,
              machineModel: data.machineModel,
              manufacturer: data.manufacturer,
              manufacturerDedicatedStatus: data.manufacturerDedicatedStatus,
              mrpArea: data.mrpArea,
              packageManufacturer: data.packageManufacturer,
              packageProdtPlace: data.packageProdtPlace,
              plannerCode: 'lv2.li',
              purchaseAdvanceDate: data.purchaseAdvanceDate,
              quota: data.quota,
              quotaFlag: data.quotaFlag,
              rawMaterialManufacturer: data.rawMaterialManufacturer,
              supplierCode: data.supplierCode,
              supplierName: data.supplierName,
              unpaidPoQty: data.unpaidPoQty
            },
            tvForecastRecord
          }
        ]
      }
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerForecastBatchInsertTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 采方-获取采方预测信息列表
            this.postBuyerForecastQuery()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    // 采方-修改预测信息
    postBuyerForecastSaveForecast(args) {
      const { data, rowIndex } = args
      if (!this.isValidData(data)) {
        // 当出现错误时，指定行进入编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认修改该数据？')
        },
        success: () => {
          const tvForecastRecord = []
          if (this.titleList && this.titleList.length) {
            this.titleList.forEach((title) => {
              const item = data[`title_${title}`]['buyerNum']
              if (item) {
                tvForecastRecord.push(String(item))
              } else {
                tvForecastRecord.push('0')
              }
            })
          }
          const params = {
            tvForecastUpdateDtoList: [
              {
                purchaserRemark: data.purchaserRemark,
                tvForecastId: data.id,
                tvForecastRecord: tvForecastRecord
              }
            ]
          }
          this.apiStartLoading()
          this.$API.predictCollaboration
            .postBuyerForecastSaveForecastTv(params)
            .then((res) => {
              this.apiEndLoading()
              if (res?.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                // 采方-获取采方预测信息列表
                this.postBuyerForecastQuery()
              }
            })
            .catch(() => {
              this.apiEndLoading()
              // 当出现错误时，指定行进入编辑状态
              this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
              this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
            })
        },
        close: () => {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        }
      })
    },
    // 校验数据
    isValidData(data) {
      const { itemCode, factoryCode, supplierCode } = data
      let valid = false
      if (!itemCode) {
        // 物料代码
        this.$toast({ content: this.$t('物料不可为空'), type: 'warning' })
      } else if (!factoryCode) {
        // 工厂代码
        this.$toast({ content: this.$t('工厂不可为空'), type: 'warning' })
      } else if (!supplierCode) {
        // 供应商代码
        this.$toast({ content: this.$t('供应商不可为空'), type: 'warning' })
      } else {
        valid = true
      }

      return valid
    },
    // 采方-获取采方预测信息列表
    postBuyerForecastQuery() {
      // 处理查询条件
      this.exportRules = cloneDeep(this.forecastRules)
      this.forecastRules = this.getRules()
      const params = {
        // page: {
        //   },
        pageSize: this.forecastPageSettings.pageSize,
        pageNum: this.forecastPageCurrent,
        ...this.searchFormModel
        // condition: this.forecastCondition,
        // rules: [...this.forecastRules],
        // forecastTemplate: this.forecastTemplate, // 预测模板
        // defaultRules: [
        //   {
        //     field: 'latestVersion',
        //     operator: 'equal',
        //     value: 1
        //   }
        // ]
      }
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerTvForecastQuery(params)
        .then((res) => {
          this.apiEndLoading()
          // this.$store.commit("startLoading"); // 因为表格渲染比较耗时，在handleDataBound中结束
          if (res?.code == 200) {
            const total = res?.data?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.totalRecordsCount = Number(total)
            // noQuotasFlag
            res?.data?.records.forEach((item) => {
              if (item.noQuotasFlag == '0') {
                item.noQuotasFlag = ''
              }
              if (item.noQuotasFlag == '1') {
                item.noQuotasFlag = 'x'
              }
              if (item.percentage == '0') {
                item.percentage = ''
              }
            })
            const records = res?.data?.records || [] // 表格数据
            const titleList = res?.data?.records[0]?.headers || [] // 动态表头数据
            this.titleList = res?.data?.records[0]?.headers || [] // 动态表头数据
            this.syncVersion = res?.data?.records[0]?.tvForecastIntegration?.syncVersion
            // 处理表头数据
            this.handleColumns({ titleList })
            // 处理表数据
            this.handleDataSource({ records, titleList })
            this.$set(this.componentConfig[0].grid, 'columnData', forecastColumnData)
            this.$set(this.componentConfig[0].grid, 'dataSource', forecastDataSource)
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 处理表头数据
    handleColumns(data) {
      const { titleList } = data
      forecastColumnData.length = 0 // 清空表头数据
      dynamicTitle.length = 0 // 清空 Template 使用的表头列表
      const titleListColumnData = []
      titleList.forEach((item, index) => {
        dynamicTitle.push(`${ConstDynamicTitleStr}${titleList[index]}`)
        titleListColumnData.push({
          width: 125,
          fieldCode: item,
          fieldName: item,
          isForecast: true, // 预测数据的标志
          colIndex: index, // 列的 index
          ignore: true // 不出现在筛选字段中
        })
      })
      // 固定的表头
      const forecastColumns = formatTableColumnData(ForecastColumnData)
      // 动态的日期表头
      const titleListColumns = formatTableColumnData(titleListColumnData)

      // 合并表头数组，然后 push 到 forecastColumnData 中
      const columns = [].concat(forecastColumns).concat(titleListColumns)
      // if (this.manage === 'tv') {
      //   let obj = [
      //     'manufacturer',
      //     'productRel',
      //     'rawMaterialManufacturer',
      //     'rawMaterialOrigin',
      //     'packagingManufacturer',
      //     'packagingOrigin',
      //     'specialUse'
      //   ]
      //   obj.forEach((ob) => {
      //     columns.splice(
      //       columns.findIndex((item) => {
      //         return item.fieldCode === ob
      //       }),
      //       1
      //     )
      //   })
      // }
      // if (this.manage === 'bd') {
      //   let obj = ['purchaseAheadTime', 'noQuotasFlag']
      //   obj.forEach((ob) => {
      //     columns.splice(
      //       columns.findIndex((item) => {
      //         return item.fieldCode === ob
      //       }),
      //       1
      //     )
      //   })
      // }
      columns.forEach((item) => {
        forecastColumnData.push(item)
      })
      console.log('forecastColumnData', forecastColumnData)
    },
    // 处理表数据
    handleDataSource(data) {
      const { records, titleList } = data
      forecastDataSource.length = 0 // 清空表格数据

      const dataSource = cloneDeep(formatTableDataSource({ records, titleList }))
      dataSource.forEach((item) => {
        // 将每一项 push 到 forecastDataSource 中
        forecastDataSource.push({ ...item, ...item.tvForecastIntegration })
      })
    },
    forecastTemplateChange(e) {
      // 获取预测数据
      this.forecastTemplate = e.value
      this.postBuyerForecastQuery()
    },
    // 采方删除预测信息
    postBuyerForecastDelete(tvForecastIds) {
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerForecastDeleteTv({
          tvForecastIds,
          tvForecastQueryReq: { ...this.searchFormModel }
        })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 采方-获取采方预测信息列表
            this.postBuyerForecastQuery()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方发布预测信息接口
    postBuyerForecastPublish(tvForecastIds) {
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerForecastPublishTv({
          tvForecastIds,
          tvForecastQueryReq: { ...this.searchFormModel }
        })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 采方-获取采方预测信息列表
            this.postBuyerForecastQuery()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方-导出
    postBuyerForecastExport(selectedRecords) {
      const params = {
        forecastIds: selectedRecords.map((i) => i.id),
        tvForecastQueryReq: {
          ...this.searchFormModel
        }
      }
      this.apiStartLoading()
      this.$API.predictCollaboration.postBuyerForecastExportTv(params).then((res) => {
        this.apiEndLoading()
        // const fileName = getHeadersFileName(res)
        // download({ fileName: `${fileName}`, blob: res.data })
        if (res.code === 200) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('导出任务添加成功，请到任务中心查看')
            },
            success: () => {
              this.$router.push(`/middlePlatform/task-center?taskCode=${res.data}`)
            }
          })
          // this.$dialog({
          //   modal: () => import('./components/exportDialog.vue'),
          //   data: {
          //     title: this.$t('提示'),
          //     taskCode: res.data
          //   },
          //   success: () => {
          //     // this.confirmSuccess();
          //   }
          // })
        }
      })
    },
    // 采方确认信息
    postBuyerForecastConfirm(tvForecastIds) {
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerForecastConfirmTv({
          tvForecastIds,
          tvForecastQueryReq: { ...this.searchFormModel }
        })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 采方-获取采方预测信息列表
            this.postBuyerForecastQuery()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方取消发布接口
    postBuyerForecastCancel(params) {
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerForecastCancel(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 采方-获取采方预测信息列表
            this.postBuyerForecastQuery()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.postBuyerForecastQuery()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageCurrent = 1
      this.forecastPageSettings.pageSize = pageSize
      this.postBuyerForecastQuery()
    },
    // 表格数据绑定完成
    handleDataBound() {
      this.$store.commit('endLoading')
    },
    // 行编辑 点击搜索按钮
    handleSearch(args) {
      const { data, dataKey } = args
      if (dataKey === 'itemCode') {
        // 物料 选择物料弹框
        this.$refs.materielSelectDialog.dialogInit({
          title: this.$t('选择物料'),
          data
        })
      }
    },
    // 选择 物料 弹框点击确认
    materielSelectDialogConfirm(args) {
      const { data } = args
      if (data?.length > 0) {
        this.$bus.$emit('predictManageComponentChange', {
          requestKey: 'itemCode', // 物料编码
          changeType: ComponentChangeType.code,
          data: {
            itemCode: data[0].itemCode, // 物料编码
            itemName: data[0].itemName, // 物料名称
            itemId: data[0].id // 物料id
          },
          modifiedKeys: [
            'itemCode', // 物料编码
            'itemName', // 物料名称
            'itemId' // 物料id
          ]
        })
        this.$bus.$emit('predictManageComponentChange', {
          requestKey: 'itemCode', // 物料编码
          changeType: ComponentChangeType.link,
          data: {
            itemCode: data[0].itemCode, // 物料编码
            itemName: data[0].itemName, // 物料名称
            itemId: data[0].id // 物料id
          },
          modifiedKeys: [
            'factoryCode', // 工厂编码
            'planGroupCode', // 计划组编码
            'buyerOrgCode' // 采购组Code
          ]
        })
      }
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .ant-select-selection {
  background: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.42) !important;
}

/deep/ .mt-form .mt-form-item .mt-form-item-label div:nth-child(2) {
  width: inherit;
}

.e-grid .e-gridcontent {
  td.e-rowcell:not(:first-of-type) {
    padding: 0 0px !important;
  }
}
/deep/ .e-grid .e-gridcontent {
  td.e-rowcell:not(:first-of-type) {
    padding: 0 0px !important;
  }
}
/deep/ .mt-data-grid {
  .e-gridcontent {
    .e-table {
      tbody {
        .e-editedrow {
          td {
            overflow: visible !important;
            form {
              .e-table {
                tbody {
                  tr:nth-child(2n-1) {
                    background: #f6f7fb;
                    // td:first-child {
                    //   position: sticky;
                    //   left: 0px;
                    //   z-index: 1;
                    //   border-right: 1px solid var(--plugin-dg-shadow-color);
                    //   background-color: #f6f7fb;
                    // }
                    // td:nth-child(2) {
                    //   position: sticky;
                    //   left: 50px;
                    //   z-index: 1;
                    //   border-right: 1px solid var(--plugin-dg-shadow-color);
                    //   background-color: #f6f7fb;
                    // }
                    // td:nth-child(3) {
                    //   position: sticky;
                    //   left: 120px;
                    //   z-index: 1;
                    //   border-right: 1px solid var(--plugin-dg-shadow-color);
                    //   background-color: #f6f7fb;
                    // }
                    // td:nth-child(4) {
                    //   position: sticky;
                    //   left: 250px;
                    //   z-index: 1;
                    //   border-right: 1px solid var(--plugin-dg-shadow-color);
                    //   background-color: #f6f7fb;
                    // }
                    // td:nth-child(5) {
                    //   position: sticky;
                    //   left: 350px;
                    //   z-index: 1;
                    //   border-right: 1px solid var(--plugin-dg-shadow-color);
                    //   background-color: #f6f7fb;
                    // }
                    // td:nth-child(6) {
                    //   position: sticky;
                    //   left: 450px;
                    //   z-index: 1;
                    //   border-right: 1px solid var(--plugin-dg-shadow-color);
                    //   background-color: #f6f7fb;
                    // }
                    // td:nth-child(7) {
                    //   position: sticky;
                    //   left: 550px;
                    //   z-index: 1;
                    //   border-right: 1px solid var(--plugin-dg-shadow-color);
                    //   background-color: #f6f7fb;
                    // }
                  }
                  // tr:nth-child(2n) {
                  //   background: #fff;
                  // }
                }
              }
            }
          }
        }
        tr:nth-child(2n) {
          background: #fff;
          // .e-rowcell:first-child {
          //   position: sticky;
          //   left: 0px;
          //   z-index: 1;
          //   border-right: 1px solid var(--plugin-dg-shadow-color);
          //   background-color: #fff;
          // }
          // .e-rowcell:nth-child(2) {
          //   position: sticky;
          //   left: 50px;
          //   z-index: 1;
          //   border-right: 1px solid var(--plugin-dg-shadow-color);
          //   background-color: #fff;
          // }
          // .e-rowcell:nth-child(3) {
          //   position: sticky;
          //   left: 120px;
          //   z-index: 1;
          //   border-right: 1px solid var(--plugin-dg-shadow-color);
          //   background-color: #fff;
          // }
          // .e-rowcell:nth-child(4) {
          //   position: sticky;
          //   left: 250px;
          //   z-index: 1;
          //   border-right: 1px solid var(--plugin-dg-shadow-color);
          //   background-color: #fff;
          // }
          // .e-rowcell:nth-child(5) {
          //   position: sticky;
          //   left: 350px;
          //   z-index: 1;
          //   border-right: 1px solid var(--plugin-dg-shadow-color);
          //   background-color: #fff;
          // }
          // .e-rowcell:nth-child(6) {
          //   position: sticky;
          //   left: 450px;
          //   z-index: 1;
          //   border-right: 1px solid var(--plugin-dg-shadow-color);
          //   background-color: #fff;
          // }
          // .e-rowcell:nth-child(7) {
          //   position: sticky;
          //   left: 550px;
          //   z-index: 1;
          //   border-right: 1px solid var(--plugin-dg-shadow-color);
          //   background-color: #fff;
          // }
        }
        tr:nth-child(2n-1) {
          background: #f6f7fb;
          // .e-rowcell:first-child {
          //   position: sticky;
          //   left: 0px;
          //   z-index: 1;
          //   border-right: 1px solid var(--plugin-dg-shadow-color);
          //   background-color: #f6f7fb;
          // }
          // .e-rowcell:nth-child(2) {
          //   position: sticky;
          //   left: 50px;
          //   z-index: 1;
          //   border-right: 1px solid var(--plugin-dg-shadow-color);
          //   background-color: #f6f7fb;
          // }
          // .e-rowcell:nth-child(3) {
          //   position: sticky;
          //   left: 120px;
          //   z-index: 1;
          //   border-right: 1px solid var(--plugin-dg-shadow-color);
          //   background-color: #f6f7fb;
          // }
          // .e-rowcell:nth-child(4) {
          //   position: sticky;
          //   left: 250px;
          //   z-index: 1;
          //   border-right: 1px solid var(--plugin-dg-shadow-color);
          //   background-color: #f6f7fb;
          // }
          // .e-rowcell:nth-child(5) {
          //   position: sticky;
          //   left: 350px;
          //   z-index: 1;
          //   border-right: 1px solid var(--plugin-dg-shadow-color);
          //   background-color: #f6f7fb;
          // }
          // .e-rowcell:nth-child(6) {
          //   position: sticky;
          //   left: 450px;
          //   z-index: 1;
          //   border-right: 1px solid var(--plugin-dg-shadow-color);
          //   background-color: #f6f7fb;
          // }
          // .e-rowcell:nth-child(7) {
          //   position: sticky;
          //   left: 550px;
          //   z-index: 1;
          //   border-right: 1px solid var(--plugin-dg-shadow-color);
          //   background-color: #f6f7fb;
          // }
        }
        td {
          p {
            margin-bottom: 0;
          }
        }
        // tr:nth-child(2n) {
        //   background: #fff;
        // }
      }
    }
  }
}
.forecast-template {
  width: 250px;
  padding-bottom: 8px;
  .forecast-template-select {
    width: calc(250px - 70px);
  }
}
/deep/ .column-tool {
  // margin-top: 8px;
}
/deep/ .template-svg {
  cursor: pointer;
  font-size: 12px;
  color: var(--plugin-ct-cell-icon-color);

  &:nth-child(n + 2) {
    margin-left: 10px;
  }
}

/deep/ .grid-edit-column {
  // padding: 12px 0;
}

// 预测数据行
/deep/ .forecast-item {
  // height: 40px;
  // padding: 7px;
  line-height: 26px;
  border: 1px solid #e8e8e8;
  min-height: 28px;
}
/deep/ .inputSy {
  border: 1px solid #e8e8e8;
  height: 30px;
}
// 预测数据高亮数据
/deep/ .forecast-highlight {
  color: #ed5836;
  background-color: #fdeeea;
  border: 1px solid #ed5836;
}

// 表格容器
#forecast-manage-table-container {
  position: relative;

  // 表格数据视图
  /deep/ .e-gridcontent > .e-content {
    height: 0px;
    transition: height 0.5s ease;
  }

  // 分页
  /deep/ .mt-pagertemplate {
    margin: 10px 0 10px !important;
  }
}

// 表格预测类型 fieldCode: "forecastDataType"
// /deep/ .forecast-type-box {
//   border-left: 1px solid #e8e8e8;
// }
// /deep/ .forecast-item:nth-child(-n + 6) {
//   border-bottom: 1px solid #e8e8e8;
// }

// 行编辑时包含按钮的项目
/deep/ .input-search-content {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .mt-input {
    flex: 1;
  }

  .mutliselect-container {
    width: 100%;
  }
  // 超过宽度显示省略号
  .text-ellipsis {
    width: 125px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .mt-icons {
    width: 20px;
    flex-shrink: 0;
    text-align: center;
    cursor: pointer;
  }
}
/deep/ .frozenColumns {
  .template-wrap {
    // .e-grid .e-table {
    //   & thead th:first-child {
    //     position: sticky;
    //     left: 0px;
    //     z-index: 1;
    //   }
    //   & thead th:nth-child(2) {
    //     position: sticky;
    //     left: 50px;
    //     z-index: 1;
    //   }
    //   & thead th:nth-child(3) {
    //     position: sticky;
    //     left: 120px;
    //     z-index: 1;
    //   }
    //   & thead th:nth-child(4) {
    //     position: sticky;
    //     left: 250px;
    //     z-index: 1;
    //   }
    //   & thead th:nth-child(5) {
    //     position: sticky;
    //     left: 350px;
    //     z-index: 1;
    //   }
    //   & thead th:nth-child(6) {
    //     position: sticky;
    //     left: 450px;
    //     z-index: 1;
    //   }
    //   & thead th:nth-child(7) {
    //     position: sticky;
    //     left: 550px;
    //     z-index: 1;
    //   }
    //   & tbody td:first-child {
    //     position: sticky;
    //     left: 0px;
    //     z-index: 1;
    //     border-right: 1px solid var(--plugin-dg-shadow-color);
    //     background-color: #fff;
    //   }
    //   & tbody td:nth-child(2) {
    //     position: sticky;
    //     left: 50px;
    //     z-index: 1;
    //     border-right: 1px solid var(--plugin-dg-shadow-color);
    //     background-color: #fff;
    //   }
    //   & tbody td:nth-child(3) {
    //     position: sticky;
    //     left: 120px;
    //     z-index: 1;
    //     border-right: 1px solid var(--plugin-dg-shadow-color);
    //     background-color: #fff;
    //   }
    //   & tbody td:nth-child(4) {
    //     position: sticky;
    //     left: 250px;
    //     z-index: 1;
    //     border-right: 1px solid var(--plugin-dg-shadow-color);
    //     background-color: #fff;
    //   }
    //   & tbody td:nth-child(5) {
    //     position: sticky;
    //     left: 350px;
    //     z-index: 1;
    //     border-right: 1px solid var(--plugin-dg-shadow-color);
    //     background-color: #fff;
    //   }
    //   & tbody td:nth-child(6) {
    //     position: sticky;
    //     left: 450px;
    //     z-index: 1;
    //     border-right: 1px solid var(--plugin-dg-shadow-color);
    //     background-color: #fff;
    //   }
    //   & tbody td:nth-child(7) {
    //     position: sticky;
    //     left: 550px;
    //     z-index: 1;
    //     border-right: 1px solid var(--plugin-dg-shadow-color);
    //     background-color: #fff;
    //   }
    // }
  }
}
</style>
