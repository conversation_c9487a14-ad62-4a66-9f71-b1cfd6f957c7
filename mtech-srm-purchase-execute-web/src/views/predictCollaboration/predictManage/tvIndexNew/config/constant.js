import { i18n } from '@/main.js'

// 业务组类型数字字典编码
export const BusinessGroupTypeDictCode = 'BUSINESSGROUP'

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 预测数据类型cell
export const ForecastDataTypeCell = [
  // {
  //   title: i18n.t("D1(预测)"),
  // },
  // {
  //   title: i18n.t("D2(订单)"),
  // },
  {
    title: i18n.t('D(原始需求)')
  },
  {
    title: i18n.t('P(需求量)')
  },
  {
    title: i18n.t('C(承诺量)')
  },
  {
    title: i18n.t('Gap(差异)')
  },
  {
    title: 'F'
  }
]

// 类型字段，快捷搜索，下拉选项
export const ForecastTypeDataSource = [
  // ForecastDataTypeCell[0]?.title, // D1(预测)
  // ForecastDataTypeCell[1]?.title, // D2(订单)
  ForecastDataTypeCell[0]?.title, // D(原始需求)
  ForecastDataTypeCell[1]?.title, // P(需求量)
  ForecastDataTypeCell[2]?.title, // C(承诺量)
  ForecastDataTypeCell[3]?.title, // Gap(差异)
  ForecastDataTypeCell[4]?.title // F
]

// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  status: 0, // 状态 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
  planner: '', // 计划员
  factoryId: '', // 工厂id
  factoryName: '', // 工厂name
  factoryCode: '', // 工厂代码
  mrpArea: '', // 计划区域
  itemName: '', // 物料名称
  itemCode: '', // 物料编码
  itemId: '', // 物料id
  supplierName: '', // 供应商名称
  supplierCode: null, // 供应商编码
  supplierId: '', // 供应商id
  purchaseAdvanceDate: '', // 采购提前期
  supplierInv: '', // 供方库存
  supplierRemark: '', // 供应商备注
  quota: '', // 配额
  quotaFlag: '', // 无配额标识
  machineModel: '', // 机型/机芯
  purchaserRemark: '', // 采方备注
  manufacturer: '', // 生产厂商名称
  rawMaterialManufacturer: '', // 关键原材厂商
  keyRawMaterialOrigin: '', // 关键原材产地
  packageManufacturer: '', // 包装厂商
  packageProdtPlace: '', // 包装产地
  manufacturerDedicatedStatus: '', // 制造商专用状况
  syncVersion: '', // 版本
  unpaidPoQty: '' // 未交PO
}
// toolbarTv 按钮配置
export const ToolbarTv = [
  {
    id: 'ForecastAdd',
    prefixIcon: 'mt-icons mt-icon-icon_solid_Createproject',
    text: i18n.t('新增'),
    tooltipText: i18n.t('新增'),
    icon: 'icon_solid_Createproject',
    // permission: ["O_02_1304"],
    title: i18n.t('新增')
  },
  {
    id: 'ForecastDelete',
    prefixIcon: 'mt-icons mt-icon-icon_table_delete',
    text: i18n.t('删除'),
    tooltipText: i18n.t('删除'),
    icon: 'icon_table_delete',
    // permission: ["O_02_1305"],
    title: i18n.t('删除')
  },
  {
    id: 'CloseEdit',
    text: i18n.t('取消编辑'),
    tooltipText: i18n.t('取消编辑'),
    icon: 'icon_table_delete',
    // permission: ["O_02_1305"],
    title: i18n.t('取消编辑')
  },
  // {
  //   id: "ForecastUpdate",
  //   prefixIcon: "mt-icons mt-icon-icon_table_save",
  //   text: i18n.t("更新"),
  //   tooltipText: i18n.t("更新"),
  //   icon: "icon_table_save",
  //   permission: ["O_02_1304"],
  //   title: i18n.t("更新"),
  // },
  {
    id: 'ForecastPublish',
    prefixIcon: 'mt-icons mt-icon-icon_solid_Release',
    text: i18n.t('发布'),
    tooltipText: i18n.t('发布'),
    icon: 'icon_solid_Release',
    // permission: ["O_02_1301"],
    title: i18n.t('发布')
  },
  // {
  //   id: "ForecastCancelPublish",
  //   icon: "", // icon_solid_Delete
  //   // permission: ["O_02_0695"],
  //   title: i18n.t("取消发布"),
  // },
  {
    id: 'ForecastConfirm',
    prefixIcon: 'mt-icons mt-icon-icon_table_batchacceptance',
    text: i18n.t('确认'),
    tooltipText: i18n.t('确认'),
    icon: 'icon_table_batchacceptance',
    // permission: ["O_02_1303"],
    title: i18n.t('确认')
  },
  {
    id: 'ForecastImport',
    prefixIcon: 'mt-icons mt-icon-icon_solid_Import',
    text: i18n.t('导入'),
    tooltipText: i18n.t('导入'),
    icon: 'icon_solid_Import',
    // permission: ["O_02_1307"],
    title: i18n.t('导入')
  },
  {
    id: 'ForecastExport',
    prefixIcon: 'mt-icons mt-icon-icon_solid_export',
    text: i18n.t('导出'),
    tooltipText: i18n.t('导出'),
    icon: 'icon_solid_export',
    // permission: ["O_02_1306"],
    title: i18n.t('导出')
  }
]

export const ToolBar = [
  { code: 'ForecastAdd', name: i18n.t('新增'), icon: 'vxe-icon-square-plus', status: 'info' },
  {
    code: 'ForecastDelete',
    name: i18n.t('删除'),
    icon: 'vxe-icon-delete',
    status: 'info'
  },
  {
    code: 'CloseEdit',
    name: i18n.t('取消编辑'),
    icon: 'vxe-icon-edit',
    transfer: true,
    status: 'info'
  },
  {
    code: 'ForecastPublish',
    name: i18n.t('发布'),
    icon: 'vxe-icon-folder-open',
    status: 'info'
  },
  { code: 'ForecastConfirm', name: i18n.t('确认'), icon: 'vxe-icon-file-txt', status: 'info' },
  {
    code: 'ForecastImport',
    name: i18n.t('导入'),
    icon: 'vxe-icon-cloud-upload',
    status: 'info'
  },
  {
    code: 'ForecastExport1',
    name: i18n.t('导出（可再导入）'),
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  },
  {
    code: 'ForecastExport',
    name: i18n.t('灵活导出（仅查看）'),
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  }
]

// 表格行按钮
export const CellTools = [
  {
    id: 'ForecastPublish',
    icon: '', // icon_solid_Release
    // permission: ['O_02_0384'],
    title: i18n.t('发布'),
    visibleCondition: (data) => {
      return data.status === Status.new || data.status === Status.modified // 状态：新建 || 已修改
    }
  },
  // {
  //   id: 'ForecastCancelPublish',
  //   icon: '', // icon_solid_Delete
  //   permission: ['O_02_0695'],
  //   title: i18n.t('取消发布'),
  //   visibleCondition: (data) => {
  //     return data.status === Status.pendingFeedback // 状态：待反馈
  //   }
  // },
  {
    id: 'ForecastDelete',
    icon: '', // icon_solid_Delete
    // permission: ['O_02_0383'],
    title: i18n.t('删除'),
    visibleCondition: (data) => {
      return data.status === Status.new || data.status === Status.modified // 状态：新建 || 已修改
    }
  },
  {
    id: 'ForecastConfirm',
    icon: '', // icon_solid_Delete
    // permission: ['O_02_0385'],
    title: i18n.t('确认'),
    visibleCondition: (data) => {
      return data.status === Status.feedbackAbnormal || data.status === Status.feedbackNormal // 状态：新建 || 已修改
    }
  }
]

// 状态 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
export const Status = {
  new: 0, // 新建
  modified: 1, // 已修改
  pendingFeedback: 2, // 已发布 不可编辑
  feedbackNormal: 3, // 反馈满足
  feedbackAbnormal: 4, // 反馈不满足
  confirmed: 5 // 已确认 不可编辑
  // deleted: 6, // 已删除 不可编辑
}
// 状态 text
export const StatusText = {
  [Status.new]: i18n.t('新建'),
  [Status.modified]: i18n.t('已修改'),
  [Status.pendingFeedback]: i18n.t('已发布'),
  [Status.feedbackNormal]: i18n.t('反馈满足'),
  [Status.feedbackAbnormal]: i18n.t('反馈不满足'),
  [Status.confirmed]: i18n.t('已确认')
}
// 状态 class
export const StatusClass = {
  [Status.new]: 'col-active',
  [Status.modified]: 'col-active',
  [Status.pendingFeedback]: 'col-active', // col-published
  [Status.feedbackNormal]: 'col-active', // col-normal
  [Status.feedbackAbnormal]: 'col-active', // col-abnormal
  [Status.confirmed]: 'col-active' // col-inactive
}
export const StatusSearchOptions = [
  { value: Status.new, text: StatusText[Status.new] },
  { value: Status.modified, text: StatusText[Status.modified] },
  { value: Status.pendingFeedback, text: StatusText[Status.pendingFeedback] },
  { value: Status.feedbackNormal, text: StatusText[Status.feedbackNormal] },
  { value: Status.feedbackAbnormal, text: StatusText[Status.feedbackAbnormal] },
  { value: Status.confirmed, text: StatusText[Status.confirmed] }
]
export const StatusSearchOptionsLabel = [
  { value: Status.new, label: StatusText[Status.new] },
  { value: Status.modified, label: StatusText[Status.modified] },
  { value: Status.pendingFeedback, label: StatusText[Status.pendingFeedback] },
  { value: Status.feedbackNormal, label: StatusText[Status.feedbackNormal] },
  { value: Status.feedbackAbnormal, label: StatusText[Status.feedbackAbnormal] },
  { value: Status.confirmed, label: StatusText[Status.confirmed] }
]

// 预测表格列数据
export const ForecastColumnData = [
  {
    type: 'checkbox',
    width: 50,
    ignore: true,
    fixed: 'left'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left'
  },
  {
    field: 'factoryCode',
    title: i18n.t('工厂'),
    showOverflow: true,
    width: 160,
    fixed: 'left',
    editRender: {},
    slots: {
      edit: 'factoryCodeEdit'
    }
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码'),
    width: 160,
    fixed: 'left',
    showOverflow: true,
    editRender: {},
    slots: {
      edit: 'itemCodeEdit'
    }
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称'),
    width: 115,
    fixed: 'left',
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    formatter: ({ row }) => {
      return `${row.supplierCode}-${row.supplierName}`
    },
    width: 160,
    fixed: 'left',
    showOverflow: true,
    editRender: {},
    slots: {
      edit: 'supplierCodeEdit'
    }
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = StatusSearchOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    },
    editRender: {
      name: 'select',
      options: StatusSearchOptionsLabel,
      props: { style: 'background: #fff' },
      attrs: { disabled: true }
    }
    // fixed: 'left'
  },
  {
    field: 'planner',
    title: i18n.t('计划员'),
    width: 135,
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'mrpArea',
    title: i18n.t('MRP区域'),
    showOverflow: true,
    width: 150,
    // editRender: { name: 'input', attrs: { placeholder: '请输入MRP区域' } }
    editRender: {},
    slots: {
      edit: 'mrpAreaEdit'
    }
  },
  {
    field: 'purchaseAdvanceDate',
    title: i18n.t('采购提前期'),
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'supplierInv',
    title: i18n.t('供方库存'),
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'supplierRemark',
    title: i18n.t('供应商备注'),
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'quota',
    title: i18n.t('配额'),
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'quotaFlag',
    title: i18n.t('无配额标识'),
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'machineModel',
    title: i18n.t('机型/机芯'),
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'purchaserRemark',
    title: i18n.t('采方备注'),
    showOverflow: true,
    width: 210,
    // editRender: { name: 'input', attrs: { placeholder: '请输入采方备注' } }
    editRender: {},
    slots: {
      edit: 'purchaserRemarkEdit'
    }
  },
  {
    field: 'manufacturer',
    title: i18n.t('生产厂商名称'),
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'rawMaterialManufacturer',
    title: i18n.t('关键原材厂商'),
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'keyRawMaterialOrigin',
    title: i18n.t('关键原材产地'),
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'packageManufacturer',
    title: i18n.t('包装厂商'),
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'packageProdtPlace',
    title: i18n.t('包装产地'),
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'manufacturerDedicatedStatus',
    title: i18n.t('制造商专用状况'),
    width: 130,
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'syncVersion',
    title: i18n.t('版本'),
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'unpaidPoQty',
    title: i18n.t('未交PO'),
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'structSeqNo',
    title: i18n.t('结构序号'),
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'forecastType',
    className: 'vxe-table-multi-cell',
    title: i18n.t('类型'),
    slots: {
      default: 'forecastTypeDefault'
      // 使用 JSX 渲染
      // default: ({ row }) => {
      //   return [
      //     <div>
      //       {ForecastTypeDataSource.map((item) => {
      //         return <span>{item}</span>
      //       })}
      //     </div>
      //   ]
      // }
    }
  }
]
