<template>
  <!-- 预测管理-采方 -->
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      @reset="handleCustomReset"
      @search="handleCustomSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="factoryCodes" :label="$t('工厂')" label-style="top">
          <!-- <RemoteAutocomplete
            v-model="searchFormModel.factoryCodes"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择工厂')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          ></RemoteAutocomplete> -->
          <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.factoryCodes"
            url="/srm-purchase-execute/tenant/common/permission/querySiteList"
            multiple
            :placeholder="$t('请选择工厂')"
            :fields="{ text: 'dimensionNameValue', value: 'dimensionCodeValue' }"
            records-position="data"
            params-key="keyWord"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="itemCodes" :label="$t('物料编码')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.itemCodes"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.itemName"
            :placeholder="$t('支持模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="planners" :label="$t('计划员')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.planners"
            :data-source="plannerListOptions"
            :fields="{ text: 'codeAndName', value: 'userCode' }"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="supplierCodes" :label="$t('供应商编码')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.supplierCodes"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.supplierName"
            :placeholder="$t('支持模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierEmptyFlag" :label="$t('供应商为空')" label-style="top">
          <mt-select
            v-model="searchFormModel.supplierEmptyFlag"
            :data-source="[
              { text: $t('否'), value: 0 },
              { text: $t('是'), value: 1 }
            ]"
            :show-clear-button="true"
            :placeholder="$t('请选择供应商是否为空')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('类型')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="forecastTypes"
            :data-source="forecastTypeOptions"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.status"
            :data-source="statusOptions"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="mrpArea" :label="$t('MRP区域')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.mrpArea"></mt-input>
        </mt-form-item>
        <mt-form-item prop="structSeqNo" :label="$t('结构序号')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.structSeqNo"></mt-input>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        :row-config="{ height: rowHeight }"
        :columns="columns"
        :table-data="tableData"
        show-overflow
        height="auto"
        header-align="left"
        align="left"
        style="padding-top: unset"
        grid="05d707e6-4741-45ca-95bf-41529e5d6171"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true,
          activeMethod: this.activeRowMethod
        }"
        :cell-style="cellStyle"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        @edit-closed="editComplete"
        @edit-actived="editBegin"
        @edit-disabled="editDisabledEvent"
      >
        <template slot="custom-tools">
          <!-- :icon="item.icon" -->
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #mrpAreaEdit="{ row }">
          <vxe-input v-model="row.mrpArea" :disabled="!!row.planner"></vxe-input>
        </template>
        <template #purchaserRemarkEdit="{ row }">
          <vxe-input v-model="row.purchaserRemark" :disabled="row.status > 1"></vxe-input>
        </template>
        <template #factoryCodeEdit="{ row }">
          <vxe-select
            v-model="row.factoryCode"
            :placeholder="$t('请选择工厂')"
            :options="siteOptions"
            transfer
            filterable
            :disabled="!!row.planner"
            @change="(value) => factoryChange(value, row)"
          ></vxe-select>
        </template>
        <template #itemCodeEdit="{ row }">
          <vxe-pulldown ref="xDownItem" transfer>
            <template #default>
              <vxe-input
                :value="row.itemCode"
                :placeholder="$t('请选择物料')"
                readonly
                :disabled="!!row.planner"
                @click="(e) => focusItemCode(e, row)"
              ></vxe-input>
            </template>
            <template #dropdown>
              <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                :disabled="!!row.planner"
                @keyup="(e) => keyupItemCode(e, row)"
                style="width: 100%"
              ></vxe-input>
              <vxe-list height="200" class="predict-vxe-dropdown" :data="itemOptions" auto-resize>
                <template #default="{ items }">
                  <div
                    v-show="itemOptions.length"
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.value"
                    @click="selectItemCode(item, row)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                  <div v-show="!itemOptions.length" class="predict-vxe-list-item">
                    <span>{{ $t('暂无数据') }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template>
        <template #supplierCodeEdit="{ row }">
          <vxe-pulldown ref="xDown" transfer>
            <template #default>
              <vxe-input
                :value="row.supplierCode"
                :placeholder="$t('请选择供应商')"
                readonly
                :disabled="!!row.planner"
                @click="focusSupplierCode"
              ></vxe-input>
            </template>
            <template #dropdown>
              <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                :disabled="!!row.planner"
                @keyup="keyupSupplierCode"
                style="width: 100%"
              ></vxe-input>
              <vxe-list
                height="200"
                class="predict-vxe-dropdown"
                :data="supplierOptions"
                auto-resize
              >
                <template #default="{ items }">
                  <div
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.value"
                    @click="selectSupplierCode(item, row)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template>
        <template #forecastTypeDefault="{ row }">
          <div style="height: 114px">
            <div
              v-for="(item, index) in forecastTypeOptions"
              :key="item"
              class="forecast-type-item"
            >
              <div
                v-if="
                  index === 0 &&
                  row.drow &&
                  row.drow.length &&
                  row.id &&
                  showForecastTypes.indexOf('D') !== -1
                "
                :class="item ? 'vxe-cell-border' : ''"
              >
                {{ item }}
              </div>
              <div
                v-if="
                  index === 1 &&
                  ((row.prow && row.prow.length && row.id) || !row.planner) &&
                  showForecastTypes.indexOf('P') !== -1
                "
                :class="item ? 'vxe-cell-border' : ''"
              >
                {{ item }}
              </div>
              <div
                v-if="
                  index === 2 &&
                  row.crow &&
                  row.crow.length &&
                  row.id &&
                  showForecastTypes.indexOf('C') !== -1
                "
                :class="item ? 'vxe-cell-border' : ''"
              >
                {{ item }}
              </div>
              <div
                v-if="
                  index === 3 &&
                  row.gap &&
                  row.gap.length &&
                  row.id &&
                  showForecastTypes.indexOf('Gap') !== -1
                "
                :class="item ? 'vxe-cell-border' : ''"
              >
                {{ item }}
              </div>
              <div
                v-if="index === 4 && row.id && showForecastTypes.indexOf('F') !== -1"
                :class="item ? 'vxe-cell-border' : ''"
              >
                {{ item }}
              </div>
            </div>
          </div>
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import {
  StatusSearchOptions,
  ForecastTypeDataSource,
  ForecastColumnData,
  NewRowData,
  ToolBar
} from './config/constant'
import { utils } from '@mtech-common/utils'
import Vue from 'vue'
import inputNumberDebounce from '@/components/inputNumberDebounce/index'
Vue.component('inputNumberDebounce', inputNumberDebounce)
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      supplierEmptyFlag: null,
      toolbar: ToolBar,
      siteOptions: [], // 工厂下拉选项
      supplierOptions: [],
      getSupplierDataSource: () => {}, // 供应商 下拉选项
      itemOptions: [],
      getItemDataSource: () => {},
      apiWaitingQuantity: 0, // 调用的api正在等待数
      searchFormModel: {
        supplierName: '',
        itemName: '',
        itemCodes: '',
        status: [],
        factoryCodes: null,
        supplierCodes: '',
        mrpArea: '',
        structSeqNo: ''
      },
      syncVersion: '',
      titleList: [],
      tableData: [],
      columns: ForecastColumnData,
      plannerListOptions: [], // 计划员 下列选项
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      isEditing: false, // 正在编辑数据
      exportRules: [], //导出规则
      statusOptions: StatusSearchOptions,
      forecastTypeOptions: ForecastTypeDataSource,
      copySearchFormModel: null,
      forecastTypes: [],
      showForecastTypes: ['D', 'P', 'C', 'Gap', 'F'],
      isEdit: false,
      warningContent: this.$t('此状态数据不可编辑'),
      rowHeight: 120
    }
  },
  mounted() {
    this.getPlannerList()
    this.getSite()
    this.getSupplierDataSource = utils.debounce(this.getSupplier, 1000)
    this.getItemDataSource = utils.debounce(this.getItem, 1000)
    this.getSupplierDataSource({ value: '' })
  },
  methods: {
    cellStyle({ row, column }) {
      if (column.field === 'status') {
        if (row.status == 4) {
          return {
            color: 'red'
          }
        }
      }
    },
    getPlannerList() {
      this.$API.predictCollaboration.getPlannerAllName().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.plannerListOptions = data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.userCode} - ${i.userName}`
            }
          })
        }
      })
    },
    focusSupplierCode() {
      this.$refs.xDown.showPanel()
    },
    keyupSupplierCode(e) {
      this.getSupplierDataSource(e)
    },
    selectSupplierCode(e, row) {
      row.supplierCode = e.supplierCode
      row.supplierName = e.supplierName
      row.supplierId = e.id
    },
    focusItemCode(e, row) {
      if (!row.factoryCode) {
        this.$toast({ content: this.$t('请先选择工厂'), type: 'warning' })
        return false
      }
      this.$refs.xDownItem.showPanel()
    },
    keyupItemCode(e, row) {
      this.getItemDataSource(e, row)
    },
    selectItemCode(e, row) {
      row.itemCode = e.itemCode
      row.itemName = e.itemName
      row.itemId = e.id
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      const commonToolbar = [
        'ForecastUpdate',
        'ForecastAdd',
        'ForecastImport',
        'Filter',
        'ForecastDelete',
        'ForecastPublish',
        'ForecastConfirm',
        'Refresh',
        'refreshDataByLocal',
        'filterDataByLocal',
        'CloseEdit',
        'resetDataByLocal',
        'ForecastExport',
        'ForecastExport1',
        'Setting'
      ]
      if (code === 'CloseEdit') {
        // 清除编辑状态
        this.$refs.xTable.$refs.xGrid.clearEdit()
        this.handleCustomSearch()
        return
      }
      if (this.isEdit) {
        // this.$toast({ content: this.$t('请先完成或结束编辑操作'), type: 'warning' })
        return
      }
      if (!this.tableData.length && code !== 'ForecastImport') {
        this.$toast({ content: this.$t('请先查询数据再进行操作'), type: 'warning' })
        return
      }

      if (selectedRecords.length == 0 && !commonToolbar.includes(code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })

      if (code === 'ForecastAdd') {
        // 新增
        const currentViewRecords = $grid.getTableData().visibleData
        if (!currentViewRecords.length) {
          this.$toast({
            content: this.$t('请先查询数据再进行新增操作'),
            type: 'warning'
          })
          return
        }
        this.titleList.forEach((itemTitle) => {
          const forecastItem = {
            buyerNum: null,
            gapNum: null,
            total: null,
            planGroupNum: null,
            supplierNum: null
          }
          NewRowData[`title_${itemTitle}`] = forecastItem
        })
        // 新增一行
        $grid.insert([NewRowData])
        this.$nextTick(() => {
          // 获取最新的表格视图数据
          const currentViewRecords = $grid.getTableData().visibleData
          // 将新增的那一条设置为编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow(currentViewRecords[0])
        })
      } else if (code === 'ForecastDelete') {
        // 删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: idList.length
              ? this.$t('确认删除选中的数据？')
              : this.$t('确认删除符合筛选条件的数据？')
          },
          success: () => {
            this.postBuyerForecastDelete(idList)
          }
        })
      } else if (code === 'ForecastPublish') {
        // 发布
        this.postBuyerForecastPublish(idList)
      } else if (code === 'ForecastConfirm') {
        // 确认
        this.postBuyerForecastConfirm(idList)
      } else if (code === 'ForecastImport') {
        // // 导入
        // this.$refs.importDialog.init({
        //   title: this.$t('导入')
        // })
        this.$dialog({
          modal: () => import('@/components/uploadDialog'),
          data: {
            title: this.$t('导入'),
            importApi: this.$API.predictCollaboration.postBuyerForecastImportTv,
            downloadTemplateApi: this.$API.predictCollaboration.postBuyerForecastExportTemplateTv,
            paramsKey: 'excel'
            // saveButtonText: this.$t('下一步')
            // asyncParams: {
            //   // requestJson: JSON.stringify(parameter),
            // },
          },
          success: () => {
            // 导入之后刷新列表
            // this.$refs.templateRef.refreshCurrentGridData()
            this.handleCustomSearch()
          }
        })
        return
      } else if (code === 'ForecastExport') {
        // 导出
        // if (selectedRecords.length == 0) {
        //   this.$toast({ content: this.$t('请先选择需要导出的数据'), type: 'warning' })
        //   return
        // }
        this.postBuyerForecastExport(selectedRecords)
      } else if (code === 'ForecastExport1') {
        // 导出
        // if (selectedRecords.length == 0) {
        //   this.$toast({ content: this.$t('请先选择需要导出的数据'), type: 'warning' })
        //   return
        // }
        this.postBuyerForecastExport(selectedRecords, 'isAllowImport')
      }
    },
    // 采方删除预测信息
    postBuyerForecastDelete(tvForecastIds) {
      this.apiStartLoading()
      const params = {
        tvForecastIds,
        tvForecastQueryReq: { ...this.searchFormModel }
      }
      // if (this.searchFormModel.factoryCodes) {
      //   params.tvForecastQueryReq.factoryCodes = this.searchFormModel.factoryCodes.split(' ')
      // } else {
      //   params.tvForecastQueryReq.factoryCodes = null
      // }
      if (this.searchFormModel.supplierCodes) {
        params.tvForecastQueryReq.supplierCodes = this.searchFormModel.supplierCodes.split(' ')
      } else {
        params.tvForecastQueryReq.supplierCodes = null
      }
      if (this.searchFormModel.itemCodes) {
        params.tvForecastQueryReq.itemCodes = this.searchFormModel.itemCodes.split(' ')
      } else {
        params.tvForecastQueryReq.itemCodes = null
      }
      this.$API.predictCollaboration
        .postBuyerForecastDeleteTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 采方-获取采方预测信息列表
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方发布预测信息接口
    postBuyerForecastPublish(tvForecastIds) {
      this.apiStartLoading()
      const params = {
        tvForecastIds,
        tvForecastQueryReq: { ...this.searchFormModel }
      }
      // if (this.searchFormModel.factoryCodes) {
      //   params.tvForecastQueryReq.factoryCodes = this.searchFormModel.factoryCodes.split(' ')
      // } else {
      //   params.tvForecastQueryReq.factoryCodes = null
      // }
      if (this.searchFormModel.supplierCodes) {
        params.tvForecastQueryReq.supplierCodes = this.searchFormModel.supplierCodes.split(' ')
      } else {
        params.tvForecastQueryReq.supplierCodes = null
      }
      if (this.searchFormModel.itemCodes) {
        params.tvForecastQueryReq.itemCodes = this.searchFormModel.itemCodes.split(' ')
      } else {
        params.tvForecastQueryReq.itemCodes = null
      }
      this.$API.predictCollaboration
        .postBuyerForecastPublishTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 采方-获取采方预测信息列表
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方-导出
    postBuyerForecastExport(selectedRecords, isAllowImport) {
      const { visibleColumn } = this.$refs.xTable.$refs.xGrid.getTableColumn()
      const fixHeaderMap = {}
      const dynamicHeaderMap = {}
      if (!isAllowImport) {
        visibleColumn.forEach((i, index) => {
          if (i.field && i.title) {
            if (i.field.includes('title_')) {
              // dynamicHeaderMap.push(i.title)
              dynamicHeaderMap[i.title] = index
            } else {
              // fixHeaderMap.push(i.field)
              if (i.field === 'forecastType') {
                fixHeaderMap['type'] = index
              } else {
                fixHeaderMap[i.field] = index
              }
            }
          }
        })
      }
      const params = {
        forecastIds: selectedRecords.map((i) => i.id),
        tvForecastQueryReq: {
          ...this.searchFormModel
        },
        fixHeaderMap,
        dynamicHeaderMap
      }

      // if (this.searchFormModel.factoryCodes) {
      //   params.tvForecastQueryReq.factoryCodes = this.searchFormModel.factoryCodes.split(' ')
      // } else {
      //   params.tvForecastQueryReq.factoryCodes = null
      // }
      if (this.searchFormModel.supplierCodes) {
        params.tvForecastQueryReq.supplierCodes = this.searchFormModel.supplierCodes.split(' ')
      } else {
        params.tvForecastQueryReq.supplierCodes = null
      }
      if (this.searchFormModel.itemCodes) {
        params.tvForecastQueryReq.itemCodes = this.searchFormModel.itemCodes.split(' ')
      } else {
        params.tvForecastQueryReq.itemCodes = null
      }
      this.apiStartLoading()
      this.$API.predictCollaboration.postBuyerForecastExportTv(params).then((res) => {
        this.apiEndLoading()
        if (res.code === 200) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('导出任务添加成功，请到任务中心查看')
            },
            success: () => {
              this.$router.push(`/middlePlatform/task-center?taskCode=${res.data}`)
            }
          })
        }
      })
    },
    // 采方确认信息
    postBuyerForecastConfirm(tvForecastIds) {
      this.apiStartLoading()
      const params = {
        tvForecastIds,
        tvForecastQueryReq: { ...this.searchFormModel }
      }
      // if (this.searchFormModel.factoryCodes) {
      //   params.tvForecastQueryReq.factoryCodes = this.searchFormModel.factoryCodes.split(' ')
      // } else {
      //   params.tvForecastQueryReq.factoryCodes = null
      // }
      if (this.searchFormModel.supplierCodes) {
        params.tvForecastQueryReq.supplierCodes = this.searchFormModel.supplierCodes.split(' ')
      } else {
        params.tvForecastQueryReq.supplierCodes = null
      }
      if (this.searchFormModel.itemCodes) {
        params.tvForecastQueryReq.itemCodes = this.searchFormModel.itemCodes.split(' ')
      } else {
        params.tvForecastQueryReq.itemCodes = null
      }
      this.$API.predictCollaboration
        .postBuyerForecastConfirmTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 采方-获取采方预测信息列表
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 主数据 供应商
    getSupplier(args) {
      const { value } = args
      const params = {
        fuzzyNameOrCode: value ? value : ''
      }
      this.$API.masterData.getSupplier(params).then((res) => {
        if (res) {
          const list = res?.data || []
          const newData = list.map((i) => {
            return {
              ...i,
              label: `${i.supplierCode}-${i.supplierName}`,
              value: i.supplierCode
            }
          })
          // this.supplierOptions = [...newData, ...this.supplierOptions]
          this.supplierOptions = [...newData]
        }
      })
    },
    // 主数据 物料
    getItem(args, row) {
      const { value } = args
      const params = {
        page: { current: 1, size: 100 },
        condition: 'and',
        rules: [
          {
            label: this.$t('物料编号'),
            field: 'itemCode',
            type: 'string',
            operator: 'contains',
            value
          }
          // {
          //   label: '物料名称',
          //   field: 'itemName',
          //   type: 'string',
          //   operator: 'contains',
          //   value
          // }
        ],
        defaultRules: [
          {
            field: 'organizationCode',
            operator: 'equal',
            value: row.factoryCode
          }
        ]
      }
      this.$API.masterData.getItemPage(params).then((res) => {
        if (res) {
          const list = res?.data?.records || []
          const newData = list.map((i) => {
            return {
              ...i,
              label: `${i.itemCode}-${i.itemName}`,
              value: i.itemCode
            }
          })
          // this.itemOptions = [...newData, ...this.itemOptions]
          this.itemOptions = [...newData]
        }
      })
    },
    factoryChange(val, row) {
      const { value } = val
      this.siteOptions.forEach((e) => {
        if (e.siteCode === value || e.organizationCode === value) {
          row.factoryId = e.id // id
          // row.factoryCode = e.organizationCode // code
          // row.factoryName = e.organizationName // name
          row.factoryCode = e.organizationCode ? e.organizationCode : e.siteCode // code
          row.factoryName = e.organizationName ? e.organizationName : e.siteName // name
          this.getItemDataSource({ value: '' }, row)
        }
      })
    },
    // 主数据 根据物料 查询关联的工厂列表
    getSite() {
      this.$API.masterData.getSiteFindByPermission().then((res) => {
        const list = res?.data || []
        this.siteOptions = list.map((i) => {
          return {
            ...i,
            label: `${i.siteCode}-${i.siteName}`,
            value: i.siteCode
          }
        })
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 校验点击的对象是否为取消编辑按钮
        if (args.$event.target.innerText === this.$t('取消编辑')) {
          // 清除编辑状态
          this.$refs.xTable.$refs.xGrid.clearEdit()
          this.handleCustomSearch()
          return
        }
        // 远程数据才有$event属性
        //1、 校验必填 没通过就是this.$refs.xTable.$refs.xGrid.setEditRow(row)
        if (!this.isValidData(row)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow(row)
          return
        }
        //2、 判断是否有row.planner调新增或者编辑接口
        if (row.planner) {
          this.postBuyerForecastSaveForecast({ data: row })
        } else {
          this.postBuyerForecastBatchInsert({ data: row })
        }
        //3、 接口调用成功调刷新接口
      }
    },
    editBegin(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        this.getItemDataSource({ value: row.itemCode }, row)
        this.getSupplierDataSource({ value: row.supplierCode })
      } else {
        this.getSupplierDataSource({ value: '' })
      }
      this.isEdit = true
    },
    // 采方-批量写入预测数据
    postBuyerForecastBatchInsert(args) {
      const { data } = args
      const tvForecastRecord = []
      if (this.titleList && this.titleList.length) {
        this.titleList.forEach((title) => {
          const item = data[`title_${title}`]['buyerNum']
          if (item) {
            tvForecastRecord.push(String(item))
          } else {
            tvForecastRecord.push('0')
          }
        })
      }
      const params = {
        tvForecastInsertDtoList: [
          {
            syncVersion: this.syncVersion,
            tvForecastModel: {
              factoryCode: data.factoryCode,
              itemCode: data.itemCode,
              itemName: data.itemName,
              keyRawMaterialOrigin: data.keyRawMaterialOrigin,
              machineModel: data.machineModel,
              manufacturer: data.manufacturer,
              manufacturerDedicatedStatus: data.manufacturerDedicatedStatus,
              mrpArea: data.mrpArea,
              packageManufacturer: data.packageManufacturer,
              packageProdtPlace: data.packageProdtPlace,
              plannerCode: 'lv2.li',
              purchaseAdvanceDate: data.purchaseAdvanceDate,
              quota: data.quota,
              quotaFlag: data.quotaFlag,
              rawMaterialManufacturer: data.rawMaterialManufacturer,
              supplierCode: data.supplierCode,
              supplierName: data.supplierName,
              unpaidPoQty: data.unpaidPoQty
            },
            tvForecastRecord
          }
        ]
      }
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerForecastBatchInsertTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 采方-获取采方预测信息列表
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow(data)
        })
    },
    // 采方-修改预测信息
    postBuyerForecastSaveForecast(args) {
      const { data } = args
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认修改该数据？')
        },
        success: () => {
          const tvForecastRecord = []
          if (this.titleList && this.titleList.length) {
            this.titleList.forEach((title) => {
              const item = data[`title_${title}`]['buyerNum']
              if (item) {
                tvForecastRecord.push(String(item))
              } else {
                tvForecastRecord.push('0')
              }
            })
          }
          const params = {
            tvForecastUpdateDtoList: [
              {
                purchaserRemark: data.purchaserRemark,
                tvForecastId: data.id,
                tvForecastRecord: tvForecastRecord
              }
            ]
          }
          this.apiStartLoading()
          this.$API.predictCollaboration
            .postBuyerForecastSaveForecastTv(params)
            .then((res) => {
              this.apiEndLoading()
              if (res?.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                // 采方-获取采方预测信息列表
                this.handleCustomSearch()
              }
            })
            .catch(() => {
              this.apiEndLoading()
              // 当出现错误时，指定行进入编辑状态
              this.$refs.xTable.$refs.xGrid.setEditRow(data)
            })
        },
        close: () => {
          this.$refs.xTable.$refs.xGrid.setEditRow(data)
        }
      })
    },
    activeRowMethod({ row }) {
      // 已确认的数据不可修改
      if (row.status === 5) {
        return false
      }
      if (!row.supplierCode && row.planner) {
        this.warningContent = this.$t(
          '该行数据供应商为空，不可编辑，需导出调整后导入，且该条数据做页面删除！'
        )
        return false
      }
      return true
    },
    editDisabledEvent() {
      this.$toast({
        content: this.warningContent,
        type: 'warning'
      })
      this.warningContent = this.$t('此状态数据不可编辑')
    },
    // 校验数据
    isValidData(data) {
      const { itemCode, factoryCode, supplierCode } = data
      let valid = false
      if (!factoryCode) {
        // 工厂代码
        this.$toast({ content: this.$t('工厂不可为空'), type: 'warning' })
      } else if (!itemCode) {
        // 物料代码
        this.$toast({ content: this.$t('物料不可为空'), type: 'warning' })
      } else if (!supplierCode) {
        // 供应商代码
        this.$toast({ content: this.$t('供应商不可为空'), type: 'warning' })
      } else {
        valid = true
      }

      return valid
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'bigVersionNo') {
            this.searchFormModel[key] = this.versionList[0]
          }
        }
      }
      this.forecastTypes = []
      this.handleCustomSearch()
    },
    // 采方-获取采方预测信息列表
    handleCustomSearch() {
      this.isEdit = false
      const params = {
        pageSize: this.forecastPageSettings.pageSize,
        pageNum: this.forecastPageCurrent,
        ...this.searchFormModel
      }
      if (this.searchFormModel.supplierCodes) {
        params.supplierCodes = this.searchFormModel.supplierCodes.split(' ')
      } else {
        params.supplierCodes = null
      }
      if (this.searchFormModel.itemCodes) {
        params.itemCodes = this.searchFormModel.itemCodes.split(' ')
      } else {
        params.itemCodes = null
      }
      if (this.searchFormModel.supplierEmptyFlag) {
        if (params.supplierCodes) {
          params.supplierCodes.push('')
        } else {
          params.supplierCodes = ['']
        }
      }
      this.showForecastTypes = []
      if (!this.forecastTypes.length) {
        this.showForecastTypes = ['D', 'P', 'C', 'Gap', 'F']
        this.rowHeight = 120
      } else {
        this.forecastTypes.forEach((i) => {
          switch (i) {
            case this.$t('D(原始需求)'):
              this.showForecastTypes.push('D')
              break
            case this.$t('P(需求量)'):
              this.showForecastTypes.push('P')
              break
            case this.$t('C(承诺量)'):
              this.showForecastTypes.push('C')
              break
            case this.$t('Gap(差异)'):
              this.showForecastTypes.push('Gap')
              break
            case 'F':
              this.showForecastTypes.push('F')
              break

            default:
              break
          }
        })
      }
      if (this.showForecastTypes.length && this.showForecastTypes.length === 1) {
        this.rowHeight = 22
      } else if (this.showForecastTypes.length && this.showForecastTypes.length === 2) {
        this.rowHeight = 46
      } else if (this.showForecastTypes.length && this.showForecastTypes.length === 3) {
        this.rowHeight = 68
      } else if (this.showForecastTypes.length && this.showForecastTypes.length === 4) {
        this.rowHeight = 92
      } else if (this.showForecastTypes.length && this.showForecastTypes.length === 5) {
        this.rowHeight = 120
      }
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerTvForecastQuery(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.totalRecordsCount = Number(total)
            const records = res?.data?.records || [] // 表格数据
            const titleList = res?.data?.records[0]?.headers || [] // 动态表头数据
            this.titleList = res?.data?.records[0]?.headers || [] // 动态表头数据
            this.syncVersion =
              res?.data?.records[0]?.tvForecastIntegration?.syncVersion.split('.')[0]
            // 处理表头数据
            this.columns = this.handleColumns({ titleList })
            // 处理表数据
            this.tableData = this.handleDataSource({ records, titleList })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleColumns(data) {
      const { titleList } = data
      // 固定的表头
      const forecastColumns = ForecastColumnData
      // 动态的日期表头
      const titleListColumnData = []
      titleList.forEach((item, index) => {
        const title = `title_${titleList[index]}`
        titleListColumnData.push({
          field: title,
          title: item,
          minWidth: 125,
          showOverflow: true,
          editRender: {},
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <div class='dynamic-column-item'>
                  <div
                    v-show={
                      ((row.drow && row.drow.length && row.id) || !row.planner) &&
                      this.showForecastTypes.indexOf('D') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['total']}
                  </div>
                  <div
                    v-show={
                      ((row.prow && row.prow.length && row.id) || !row.planner) &&
                      this.showForecastTypes.indexOf('P') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['buyerNum']}
                  </div>
                  <div
                    v-show={
                      ((row.crow && row.crow.length && row.id) || !row.planner) &&
                      this.showForecastTypes.indexOf('C') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['supplierNum']}
                  </div>
                  <div
                    v-show={
                      ((row.gap && row.gap.length && row.id) || !row.planner) &&
                      this.showForecastTypes.indexOf('Gap') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['gapNum']}
                  </div>
                  <div
                    v-show={(row.id || !row.planner) && this.showForecastTypes.indexOf('F') !== -1}
                    class='vxe-cell-border'
                  >
                    {row[title]['planGroupNum']}
                  </div>
                </div>
              ]
            },
            edit: ({ row }) => {
              return [
                <div class='dynamic-column-item'>
                  <div
                    v-show={
                      ((row.drow && row.drow.length && row.id) || !row.planner) &&
                      this.showForecastTypes.indexOf('D') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['total']}
                  </div>
                  <div
                    v-show={
                      ((row.prow && row.prow.length && row.id) || !row.planner) &&
                      this.showForecastTypes.indexOf('P') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    <input-number-debounce
                      show-spin-button={false}
                      show-clear-button={false}
                      v-model={row[title]['buyerNum']}
                      v-show={!title.includes('汇总') && !title.includes('已入库')}
                    />
                    <div v-show={title.includes('汇总') || title.includes('已入库')}>
                      {row[title]['buyerNum'] || 0}
                    </div>
                  </div>
                  <div
                    v-show={
                      ((row.crow && row.crow.length && row.id) || !row.planner) &&
                      this.showForecastTypes.indexOf('C') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['supplierNum']}
                  </div>
                  <div
                    v-show={
                      ((row.gap && row.gap.length && row.id) || !row.planner) &&
                      this.showForecastTypes.indexOf('Gap') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['gapNum']}
                  </div>
                  <div
                    v-show={
                      // ((row.frow && row.frow.length && row.id) || !row.planner) &&
                      (row.id || !row.planner) && this.showForecastTypes.indexOf('F') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['planGroupNum']}
                  </div>
                </div>
              ]
            }
          }
        })
      })

      // 合并表头数组，然后 push 到 forecastColumnData 中
      const columns = [].concat(forecastColumns).concat(titleListColumnData)
      return columns
    },
    handleDataSource(data) {
      const { records, titleList } = data
      const tableData = []
      records.forEach((recordsItem) => {
        const rowData = {
          ...recordsItem.tvForecastIntegration,
          crow: recordsItem.crow,
          drow: recordsItem.drow,
          prow: recordsItem.prow,
          frow: recordsItem.frow,
          gap: recordsItem.gap
        }
        if (rowData.syncVersion && rowData.syncVersion.length) {
          // rowData.syncVersion = rowData.syncVersion.substring(0, 8)
          rowData.syncVersion = rowData.syncVersion.slice(0, 8) + rowData.syncVersion.slice(14)
        }
        // recordsItem.thePrimaryKey = recordsItem.tvForecastIntegration.id // 主键的值为 API 数据的 id
        // recordsItem.status = recordsItem.tvForecastIntegration.status ?? 0 // 状态为 undefined/null 时，默认为新建状态（导入时）

        const crow = recordsItem?.crow
        const drow = recordsItem?.drow
        const frow = recordsItem?.frow
        const prow = recordsItem?.prow
        const gap = recordsItem?.gap
        titleList.forEach((itemTitle, index) => {
          crow.forEach(() => {
            // 将预测数据赋给表头对应的对象
            const forecastItem = {
              buyerNum: prow && prow[index] ? prow[index] : null,
              gapNum: gap && gap[index] ? gap[index] : null,
              total: drow && drow[index] ? drow[index] : null,
              planGroupNum: frow && frow[index] ? frow[index] : null,
              supplierNum: crow && crow[index] ? crow[index] : null
            }
            rowData[`title_${itemTitle}`] = forecastItem
          })
        })
        tableData.push(rowData)
      })

      return tableData
      // return records
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  },
  beforeRouteLeave(to, from, next) {
    document.querySelector('.vxe-table--body-wrapper').scrollTop = 0
    next()
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  .forecast-type-item {
    &:first-child {
      .vxe-cell-border {
        border-top: none;
      }
    }
    .vxe-cell-border {
      border-top: solid #e6e9ed 1px;
    }
  }
  .dynamic-column-item {
    height: 114px;
    .vxe-cell-border {
      border-top: solid #e6e9ed 1px;
      &:first-child {
        border-top: none;
      }
    }
  }
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
