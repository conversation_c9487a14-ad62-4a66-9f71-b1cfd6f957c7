import Vue from 'vue'
import { rowDataTemp, factoryNameOptions, businessGroupCode, editLinkDataParams } from './variable'
import {
  StatusClass,
  StatusText,
  Status,
  ForecastDataTypeCell,
  ComponentType,
  ComponentChangeType
} from './constant'
import { i18n } from '@/main.js'
import { negativeInputOnKeyDown, addCodeNameKeyInList } from '@/utils/utils'
import { cloneDeep } from 'lodash'
import { utils } from '@mtech-common/utils'
import bigDecimal from 'js-big-decimal'

// 预测管理 Component 集合
const ForecastColumnComponent = {
  // 文本 可编辑、可监听关联、可带搜索按钮、可格式化显示
  inputText: (args) => {
    const {
      dataKey,
      showClearBtn,
      disabled,
      maxlength,
      isListenChange,
      hasSearch,
      format,
      disableConverter
    } = args
    const template = () => {
      return {
        template: Vue.component('inputComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <div :class="[hasSearch && 'input-search-content']">
                <mt-input
                  v-model="componentData"
                  autocomplete="off"
                  :show-clear-button="showClearBtn"
                  :disabled="disabled"
                  :maxlength="maxlength"
                  @input="onInput"
                ></mt-input>
                <mt-icon
                  v-show="hasSearch"
                  name="icon_input_search"
                  @click.native="handleSearch"
                ></mt-icon>
              </div>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              disabled,
              showClearBtn,
              maxlength,
              hasSearch,
              componentData: null // 组件内部绑定的变量
            }
          },
          mounted() {
            if (isListenChange) {
              // 监听变化
              this.onComponentChange()
            }
            this.doFormat()
            this.doDisableConverter()
          },
          beforeDestroy() {
            if (isListenChange) {
              // 移除监听
              this.$bus.$off('predictManageComponentChange')
            }
          },
          methods: {
            onInput(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e
            },
            // 监听变化
            onComponentChange() {
              // 监听被变化
              this.$bus.$on(`predictManageComponentChange`, (e) => {
                const { modifiedKeys, data, changeType } = e
                if (
                  modifiedKeys.includes(this.dataKey) &&
                  changeType === ComponentChangeType.code
                ) {
                  // 发布事件的数据修改了，关联值修改
                  const newData = cloneDeep(data[this.dataKey])
                  this.data[dataKey] = newData
                  rowDataTemp[rowDataTemp.length - 1][dataKey] = newData

                  if (this.dataKey === 'itemCode') {
                    // 物料的行字段
                    this.data.itemName = data.itemName
                    this.data.itemId = data.itemId
                    rowDataTemp[rowDataTemp.length - 1]['itemName'] = data.itemName
                    rowDataTemp[rowDataTemp.length - 1]['itemId'] = data.itemId
                  }
                  this.doFormat()
                }
              })
            },
            // 点击搜索
            handleSearch() {
              this.$parent.$emit('handleSearch', {
                dataKey,
                data: rowDataTemp[rowDataTemp.length - 1]
              })
            },
            doFormat() {
              if (format && typeof format === 'function') {
                // 格式化显示
                this.componentData = format(this.data)
              } else {
                this.componentData = this.data[dataKey]
              }
            },
            doDisableConverter() {
              if (disableConverter && typeof disableConverter === 'function') {
                // 禁用转换
                if (hasSearch) {
                  const disabled = disableConverter(this.data)
                  this.hasSearch = !disabled
                } else {
                  this.disabled = disableConverter(this.data)
                }
              }
            }
          }
        })
      }
    }
    return template
  },
  // 工厂 code-name显示 根据物料选择带出数据源 不可模糊搜索（API不支持）
  siteCodeSelect: () => {
    return {
      template: Vue.component('siteCodeSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.factoryCode"
              :data-source="siteOptions"
              :fields="{ text: 'theCodeName', value: 'organizationCode' }"
              :show-clear-button="true"
              :allow-filtering="false"
              @change="siteCodeChange"
              :open-dispatch-change="false"
              :placeholder="$t('请选择')"
              :disabled="disabled"
            ></mt-select>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            siteOptions: [], // 工厂 下拉选项
            disabled: false
          }
        },
        mounted() {
          this.getSite()
          // 监听变化
          // this.onComponentChange();
          this.doDisableConverter()
        },
        beforeDestroy() {
          this.$bus.$off('predictManageComponentChange')
        },
        methods: {
          // 主数据 根据物料 查询关联的工厂列表
          getSite() {
            this.$API.masterData.getSiteFindByPermission().then((res) => {
              const list = res?.data || []
              this.siteOptions = addCodeNameKeyInList({
                firstKey: 'organizationCode',
                secondKey: 'organizationName',
                list
              })
            })

            // const { itemCode, updateData, setSelectData } = args;
            // const params = {
            //   itemCode,
            //   orgLevelTypeCode: "ORG06",
            // };
            // this.$API.masterData.getFactoryList(params).then((res) => {
            //   if (res) {
            //     const list = res?.data || [];
            //     this.siteOptions = addCodeNameKeyInList({
            //       firstKey: "organizationCode",
            //       secondKey: "organizationName",
            //       list,
            //     });
            //     if (updateData) {
            //       this.$nextTick(() => {
            //         updateData(this.siteOptions);
            //       });
            //     }
            //     if (setSelectData) {
            //       this.$nextTick(() => {
            //         setSelectData();
            //       });
            //     }
            //   }
            // });
          },
          // 初始化检索 工厂
          initGetSite() {
            const itemCode = this.data.itemCode
            if (itemCode) {
              this.getSite({
                itemCode: itemCode
              })
            }
          },
          // 工厂 change
          siteCodeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].factoryId = itemData.id // id
              rowDataTemp[rowDataTemp.length - 1].factoryCode = itemData.organizationCode // code
              rowDataTemp[rowDataTemp.length - 1].factoryName = itemData.organizationName // name
            } else {
              rowDataTemp[rowDataTemp.length - 1].factoryId = null // id
              rowDataTemp[rowDataTemp.length - 1].factoryCode = null // code
              rowDataTemp[rowDataTemp.length - 1].factoryName = null // name
            }
            // 工厂带物料
            this.$bus.$emit('siteCodeChangeItem', {
              data: rowDataTemp[rowDataTemp.length - 1].factoryCode
            })
            // 发布事件 公司 值更新
            this.$bus.$emit('predictManageComponentChange', {
              requestKey: 'factoryCode', // 工厂编码
              changeType: ComponentChangeType.code,
              data: {
                factoryCode: rowDataTemp[rowDataTemp.length - 1].factoryCode // 工厂编码
              },
              modifiedKeys: [
                'companyCode' // 公司编码
              ]
            })

            // // 发布事件 计划组、采购组 数据源更新
            // this.$bus.$emit("predictManageComponentChange", {
            //   requestKey: "factoryCode", // 工厂编码
            //   changeType: ComponentChangeType.link,
            //   data: {
            //     factoryCode: rowDataTemp[rowDataTemp.length - 1].factoryCode, // 工厂编码
            //   },
            //   modifiedKeys: [
            //     "planGroupCode", // 计划组代码
            //     "buyerOrgCode", // 采购组代码
            //   ],
            // });
          },
          // 监听变化
          // onComponentChange() {
          //   // 监听被变化
          //   this.$bus.$on(`predictManageComponentChange`, (e) => {
          //     const { modifiedKeys, data, changeType } = e;
          //     if (
          //       modifiedKeys.includes("factoryCode") &&
          //       changeType === ComponentChangeType.link
          //     ) {
          //       // 物料修改了，重新获取工厂
          //       this.getSite({
          //         itemCode: data.itemCode,
          //       });
          //       this.data.factoryId = null;
          //       this.data.factoryCode = null;
          //       this.data.factoryName = null;
          //       rowDataTemp[rowDataTemp.length - 1]["factoryId"] = null;
          //       rowDataTemp[rowDataTemp.length - 1]["factoryCode"] = null;
          //       rowDataTemp[rowDataTemp.length - 1]["factoryName"] = null;
          //     }
          //   });
          // },
          doDisableConverter() {
            if (this.data.status != Status.new) {
              // 新增状态下 能修改
              this.disabled = true
            }
          }
        }
      })
    }
  },
  // 供应商 code-name 下拉框选择 供应商主数据 模糊搜索
  supplierCodeSelect: () => {
    return {
      template: Vue.component('supplierCodeSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.supplierCode"
              :filtering="doGetDataSource"
              :data-source="supplierOptions"
              :fields="{ text: 'theCodeName', value: 'supplierCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              @change="supplierCodeChange"
              :open-dispatch-change="false"
              :placeholder="$t('请选择')"
              :disabled="disabled"
            ></mt-select>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            supplierOptions: [], // 供应商 下拉选项
            doGetDataSource: () => {},
            disabled: false
          }
        },
        mounted() {
          this.initGetSupplier()
          this.doGetDataSource = utils.debounce(this.getSupplier, 1000)
          // 监听变化
          // this.onComponentChange();
          this.doDisableConverter()
        },
        beforeDestroy() {
          this.$bus.$off('predictManageComponentChange')
        },
        methods: {
          // 主数据 供应商
          getSupplier(args) {
            const { text, updateData, setSelectData } = args
            const params = {
              fuzzyNameOrCode: text
            }
            this.$API.masterData.getSupplier(params).then((res) => {
              if (res) {
                const list = res?.data || []
                this.supplierOptions = addCodeNameKeyInList({
                  firstKey: 'supplierCode',
                  secondKey: 'supplierName',
                  list
                })
                if (updateData) {
                  this.$nextTick(() => {
                    updateData(this.supplierOptions)
                  })
                }
                if (setSelectData) {
                  this.$nextTick(() => {
                    setSelectData()
                  })
                }
              }
            })
          },
          // 初始化检索 供应商
          initGetSupplier() {
            const supplierCode = this.data.supplierCode
            this.getSupplier({
              text: supplierCode,
              setSelectData: () => {
                // api获取数据后重新赋值，防止没有赋上值得情况
                this.data.supplierCode = supplierCode
              }
            })
          },
          // 供应商 change
          supplierCodeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].supplierId = itemData.supplierId // id
              rowDataTemp[rowDataTemp.length - 1].supplierCode = itemData.supplierCode // code
              rowDataTemp[rowDataTemp.length - 1].supplierName = itemData.supplierName // name
            } else {
              rowDataTemp[rowDataTemp.length - 1].supplierId = null // id
              rowDataTemp[rowDataTemp.length - 1].supplierCode = null // code
              rowDataTemp[rowDataTemp.length - 1].supplierName = null // name
            }
          },
          // 监听变化
          onComponentChange() {
            // 监听被变化
            this.$bus.$on(`predictManageComponentChange`, () => {
              // const {
              //   requestKey,
              //   modifiedKeys,
              //   data,
              //   changeType
              // } = e;
            })
          },
          doDisableConverter() {
            if (this.data.status != Status.new) {
              // 新增状态下 能修改
              this.disabled = true
            }
          }
        }
      })
    }
  },
  // 公司 code-name 不可编辑 工厂带出来
  companyCodeInputText: () => {
    return {
      template: Vue.component('companyCodeInputText', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-input
              v-model="componentData"
              autocomplete="off"
              :show-clear-button="false"
              :disabled="true"
            ></mt-input>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            componentData: null // 组件内部绑定的变量
          }
        },
        mounted() {
          // 监听变化
          this.onComponentChange()
          this.componentData = this.format(this.data)
        },
        beforeDestroy() {
          this.$bus.$off('predictManageComponentChange')
        },
        methods: {
          // 通过工厂获取公司
          getCompany(args) {
            const { siteCode } = args
            const params = {
              fuzzyParam: siteCode
            }
            this.$API.masterData.postSiteFuzzyQuery(params).then((res) => {
              const list = res?.data || []
              const company = {
                companyId: list[0]?.parentId, // 公司ID
                companyCode: list[0]?.parentCode, // 公司编码
                companyName: list[0]?.parentName // 公司名称
              }
              rowDataTemp[rowDataTemp.length - 1].companyId = company.companyId // id
              rowDataTemp[rowDataTemp.length - 1].companyCode = company.companyCode // code
              rowDataTemp[rowDataTemp.length - 1].companyName = company.companyName // name
              this.componentData = this.format(company)
            })
          },
          // 监听变化
          onComponentChange() {
            // 监听被变化
            this.$bus.$on(`predictManageComponentChange`, (e) => {
              const { requestKey, modifiedKeys, data, changeType } = e
              if (
                requestKey === 'factoryCode' &&
                modifiedKeys.includes('companyCode') &&
                changeType === ComponentChangeType.code
              ) {
                // 工厂带出来公司
                const siteCode = data.factoryCode
                this.getCompany({ siteCode })
              }
            })
          },
          format(data) {
            let theColumnData = ''
            if (data['companyCode'] && data['companyName']) {
              // firstKey-secondKey
              theColumnData = `${data['companyCode']}-${data['companyName']}`
            } else if (data['companyCode']) {
              // firstKey
              theColumnData = data['companyCode']
            } else if (data['companyName']) {
              // secondKey
              theColumnData = data['companyName']
            }
            return theColumnData
          }
        }
      })
    }
  },
  // 计划组 code-name显示 根据物料选择带出数据源 不可模糊搜索（API不支持）
  planGroupCodeSelect: () => {
    return {
      template: Vue.component('planGroupCodeSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.planGroupCode"
              :data-source="planGroupOptions"
              :fields="{ text: 'theCodeName', value: 'groupCode' }"
              :show-clear-button="true"
              :allow-filtering="false"
              @change="planGroupCodeChange"
              :open-dispatch-change="false"
              :placeholder="$t('请选择')"
              :disabled="disabled"
            ></mt-select>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            planGroupOptions: [], // 计划组 下拉选项
            disabled: false
          }
        },
        mounted() {
          this.initGetPlanGroup()
          // 监听变化
          this.onComponentChange()
          this.doDisableConverter()
        },
        beforeDestroy() {
          this.$bus.$off('predictManageComponentChange')
        },
        methods: {
          // 主数据 计划组 根据物料、业务组类型获取业务组
          getPlanGroup(args) {
            const { itemCode, updateData, setSelectData } = args
            const params = {
              itemCode, // 物料code
              businessGroupTypeCode: 'BG001JH'
            }
            this.$API.masterData.getBusinessGroup(params).then((res) => {
              if (res) {
                const list = res?.data || []
                this.planGroupOptions = addCodeNameKeyInList({
                  firstKey: 'groupCode',
                  secondKey: 'groupName',
                  list
                })
                if (updateData) {
                  this.$nextTick(() => {
                    updateData(this.planGroupOptions)
                  })
                }
                if (setSelectData) {
                  this.$nextTick(() => {
                    setSelectData()
                  })
                }
              }
            })
          },
          // 初始化检索 计划组
          initGetPlanGroup() {
            const itemCode = this.data.itemCode
            if (itemCode) {
              this.getPlanGroup({
                itemCode
              })
            }
          },
          // 计划组 change
          planGroupCodeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].planGroupId = itemData.id // id
              rowDataTemp[rowDataTemp.length - 1].planGroupCode = itemData.groupCode // code
              rowDataTemp[rowDataTemp.length - 1].planGroup = itemData.groupName // name
            } else {
              rowDataTemp[rowDataTemp.length - 1].planGroupId = null // id
              rowDataTemp[rowDataTemp.length - 1].planGroupCode = null // code
              rowDataTemp[rowDataTemp.length - 1].planGroup = null // name
            }
          },
          // 监听变化
          onComponentChange() {
            // 监听被变化
            this.$bus.$on(`predictManageComponentChange`, (e) => {
              const { modifiedKeys, data, changeType } = e
              if (
                modifiedKeys.includes('planGroupCode') &&
                changeType === ComponentChangeType.link
              ) {
                // 物料修改了，重新获取计划组
                this.getPlanGroup({
                  itemCode: data.itemCode
                })
                this.data.planGroupCode = null
                this.data.planGroupId = null
                this.data.planGroup = null
                rowDataTemp[rowDataTemp.length - 1]['planGroupCode'] = null
                rowDataTemp[rowDataTemp.length - 1]['planGroupId'] = null
                rowDataTemp[rowDataTemp.length - 1]['planGroup'] = null
              }
            })
          },
          doDisableConverter() {
            if (this.data.status != Status.new) {
              // 新增状态下 能修改
              this.disabled = true
            }
          }
        }
      })
    }
  },
  // 采购组 code-name显示 根据物料选择带出数据源 不可模糊搜索（API不支持）
  buyerOrgCodeSelect: () => {
    return {
      template: Vue.component('buyerOrgCodeSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.buyerOrgCode"
              :data-source="buyerOrgOptions"
              :fields="{ text: 'theCodeName', value: 'groupCode' }"
              :show-clear-button="true"
              :allow-filtering="false"
              @change="buyerOrgCodeChange"
              :open-dispatch-change="false"
              :placeholder="$t('请选择')"
              :disabled="disabled"
            ></mt-select>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            buyerOrgOptions: [], // 采购组 下拉选项
            disabled: false
          }
        },
        mounted() {
          this.initGetBuyerOrg()
          // 监听变化
          this.onComponentChange()
          this.doDisableConverter()
        },
        beforeDestroy() {
          this.$bus.$off('predictManageComponentChange')
        },
        methods: {
          // 主数据 采购组 根据物料、业务组类型获取业务组
          getBuyerOrg(args) {
            const { itemCode, updateData, setSelectData } = args
            const params = {
              itemCode, // 物料code
              businessGroupTypeCode: 'BG001CG'
            }
            this.$API.masterData.getBusinessGroup(params).then((res) => {
              if (res) {
                const list = res?.data || []
                this.buyerOrgOptions = addCodeNameKeyInList({
                  firstKey: 'groupCode',
                  secondKey: 'groupName',
                  list
                })
                if (updateData) {
                  this.$nextTick(() => {
                    updateData(this.buyerOrgOptions)
                  })
                }
                if (setSelectData) {
                  this.$nextTick(() => {
                    setSelectData()
                  })
                }
              }
            })
          },
          // 初始化检索 采购组
          initGetBuyerOrg() {
            const itemCode = this.data.itemCode
            if (itemCode) {
              this.getBuyerOrg({
                itemCode
              })
            }
          },
          // 计划组 change
          buyerOrgCodeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].buyerOrgId = itemData.id // id
              rowDataTemp[rowDataTemp.length - 1].buyerOrgCode = itemData.groupCode // code
              rowDataTemp[rowDataTemp.length - 1].buyerOrgName = itemData.groupName // name
            } else {
              rowDataTemp[rowDataTemp.length - 1].buyerOrgId = null // id
              rowDataTemp[rowDataTemp.length - 1].buyerOrgCode = null // code
              rowDataTemp[rowDataTemp.length - 1].buyerOrgName = null // name
            }
          },
          // 监听变化
          onComponentChange() {
            // 监听被变化
            this.$bus.$on(`predictManageComponentChange`, (e) => {
              const { modifiedKeys, data, changeType } = e
              if (
                modifiedKeys.includes('buyerOrgCode') &&
                changeType === ComponentChangeType.link
              ) {
                // 物料修改了，重新获取采购组
                this.getBuyerOrg({
                  itemCode: data.itemCode
                })
                this.data.buyerOrgCode = null
                this.data.buyerOrgId = null
                this.data.buyerOrgName = null
                rowDataTemp[rowDataTemp.length - 1]['buyerOrgCode'] = null
                rowDataTemp[rowDataTemp.length - 1]['buyerOrgId'] = null
                rowDataTemp[rowDataTemp.length - 1]['buyerOrgName'] = null
              }
            })
          },
          doDisableConverter() {
            if (this.data.status != Status.new) {
              // 新增状态下 能修改
              this.disabled = true
            }
          }
        }
      })
    }
  },
  // 文本 新增行时带出的数据，不可编辑
  changedInput: (args) => {
    const { dataKey, type } = args
    const template = () => {
      return {
        template: Vue.component('changedInputComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content" v-if="data.id && type !== ComponentType.mustEdit"><span>{{data[dataKey]}}</span></div>
            <div class="field-content" v-else>
              <mt-input
                :value="data[dataKey]"
                disabled
              ></mt-input>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              ComponentChangeType,
              type,
              ComponentType
            }
          },
          beforeDestroy() {
            this.$bus.$off('predictManageComponentChange')
          },
          mounted() {
            // 监听变化
            this.onComponentChange()
          },
          methods: {
            // 监听变化
            onComponentChange() {
              // 监听被变化
              this.$bus.$on(`predictManageComponentChange`, (e) => {
                const { requestKey, modifiedKey, changeType, value } = e
                const requestKeyList = [
                  'itemName', // 物料名称
                  'factoryName', // 工厂
                  'supplierName' // 供应商名称
                ]
                if (
                  requestKeyList.includes(requestKey) &&
                  changeType === ComponentChangeType.code &&
                  modifiedKey === dataKey
                ) {
                  // 发布事件的数据修改了，关联的code修改
                  this.data[dataKey] = value
                  // 编辑行时改变rowDataTemp中当前的值
                  rowDataTemp[rowDataTemp.length - 1][dataKey] = value
                } else if (
                  (requestKey === 'itemName' || requestKey === 'factoryName') &&
                  changeType === ComponentChangeType.link &&
                  modifiedKey === dataKey &&
                  dataKey === 'planGroup'
                ) {
                  // 计划组收到变更请求
                  const factoryCode = rowDataTemp[rowDataTemp.length - 1].factoryCode // 工厂Code
                  const itemCode = rowDataTemp[rowDataTemp.length - 1].itemCode // 物料Code
                  if (factoryCode && itemCode && businessGroupCode.plan) {
                    // 选择了 工厂、物料，而且获得了计划组的业务组类型code
                    const params = {
                      orgCode: factoryCode, // 组织Code
                      categoryCode: itemCode, // 末级节点品类Code
                      groupTypeCode: businessGroupCode.plan // 业务员类型编码
                    }
                    this.postCriteriaQueryBusinessGroup({
                      params,
                      businessKey: 'plan'
                    })
                  }
                } else if (
                  (requestKey === 'itemName' || requestKey === 'factoryName') &&
                  changeType === ComponentChangeType.link &&
                  modifiedKey === dataKey &&
                  dataKey === 'buyerOrgName'
                ) {
                  // 采购组收到变更请求
                  const factoryCode = rowDataTemp[rowDataTemp.length - 1].factoryCode // 工厂Code
                  const itemCode = rowDataTemp[rowDataTemp.length - 1].itemCode // 物料Code
                  if (factoryCode && itemCode && businessGroupCode.purchase) {
                    // 选择了 工厂、物料，而且获得了采购组的业务组类型code
                    const params = {
                      orgCode: factoryCode, // 组织code
                      categoryCode: itemCode, // 末级节点品类code
                      groupTypeCode: businessGroupCode.purchase // 业务员类型编码
                    }
                    this.postCriteriaQueryBusinessGroup({
                      params,
                      businessKey: 'purchase'
                    })
                  }
                }
              })
            },
            // 查询采购组或计划组，并设置值
            postCriteriaQueryBusinessGroup(args) {
              const { params, businessKey } = args
              this.$API.masterData.postCriteriaQueryBusinessGroup(params).then((res) => {
                const list = res?.data || []
                if (businessKey === 'plan' && list.length > 0) {
                  // 计划组
                  const groupName = list[0]?.groupName // 业务组名称，产品说获取第一个
                  const groupCode = list[0]?.groupCode // 业务组Code，产品说获取第一个
                  const id = list[0]?.id // 业务组id，产品说获取第一个

                  this.data[dataKey] = groupName
                  rowDataTemp[rowDataTemp.length - 1][dataKey] = list[0]?.groupName
                  rowDataTemp[rowDataTemp.length - 1].planGroupId = id // 计划组id
                  rowDataTemp[rowDataTemp.length - 1].planGroupCode = groupCode // 计划组Code
                } else if (businessKey === 'purchase' && list.length > 0) {
                  // 采购组
                  const groupName = list[0]?.groupName // 业务组名称，产品说获取第一个
                  const groupCode = list[0]?.groupCode // 业务组Code，产品说获取第一个
                  const id = list[0]?.id // 业务组id，产品说获取第一个

                  this.data[dataKey] = groupName
                  rowDataTemp[rowDataTemp.length - 1][dataKey] = groupName
                  rowDataTemp[rowDataTemp.length - 1].buyerOrgId = id // 采购组id
                  rowDataTemp[rowDataTemp.length - 1].buyerOrgCode = groupCode // 采购组Code
                }
              })
            }
          }
        })
      }
    }
    return template
  },
  // 编辑行时的 文本数据
  input: (args) => {
    const { dataKey, showClearBtn, alwaysEdit, maxlength } = args
    const template = () => {
      return {
        template: Vue.component('inputComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content" v-if="notEdit"><span>{{data[dataKey]}}</span></div>
            <div class="field-content" v-else>
                <mt-input
                  v-model="data[dataKey]"
                  :show-clear-button="showClearBtn"
                  :disabled="isDisabledEdit()"
                  :maxlength="maxlength"
                  @input="onInput"
                ></mt-input>
              </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              showClearBtn,
              maxlength,
              notEdit: true // 初始不可编辑
            }
          },
          mounted() {
            const isRowAdd = !this.data.id // id没有时为增加行
            if (alwaysEdit) {
              // 总是可编辑
              this.notEdit = false
            } else if (isRowAdd) {
              // 增加行时可编辑
              this.notEdit = false
            }
          },
          methods: {
            onInput(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e
            },
            // 编辑的条件
            isDisabledEdit() {
              let disabled = false
              // 条件
              const condition = [
                Status.pendingFeedback // 待反馈
              ]
              disabled =
                condition.includes(this.data?.status) && dataKey === 'purRemark'
                  ? true
                  : condition.includes(this.data?.status) && dataKey === 'purchaseAheadTime'
                  ? true
                  : condition.includes(this.data?.status) && dataKey === 'noQuotasFlag'
                  ? true
                  : false
              if (condition.includes(this.data?.status) && dataKey === 'purRemark') {
                // 采方备注的不可编辑条件
                disabled = true
              }

              return disabled
            }
          }
        })
      }
    }
    return template
  },
  // 编辑行时的 文本数据
  number: (args) => {
    const { dataKey, showClearBtn, alwaysEdit, maxKey, maximumValue, min, precision } = args
    const template = () => {
      return {
        template: Vue.component('numberComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content" v-if="notEdit"><span>{{data[dataKey]}}</span></div>
            <div class="field-content" v-else>
              <mt-input-number
              style='padding: 0px 10px !important'
                @keydown.native="negativeInputOnKeyDown"
                v-model="data[dataKey]"
                :max="max"
                :min="min"
                :precision="precision"
                :show-spin-button="false"
                :show-clear-button="showClearBtn"
                @input="onInput"
              ></mt-input-number>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              showClearBtn,
              max: undefined,
              min,
              precision,
              negativeInputOnKeyDown,
              notEdit: true // 初始不可编辑
            }
          },
          mounted() {
            this.max = this.data[maxKey] || maximumValue
            const isRowAdd = !this.data.id // id没有时为增加行
            if (alwaysEdit) {
              // 总是可编辑
              this.notEdit = false
            } else if (isRowAdd) {
              // 增加行时可编辑
              this.notEdit = false
            }
          },
          methods: {
            onInput(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e
            }
          }
        })
      }
    }
    return template
  },
  // 下拉框 新增行时可编辑
  select: (args) => {
    const { selectOptions, dataKey, allowFiltering, showClearBtn, type } = args
    const template = () => {
      return {
        template: Vue.component('selectComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content" v-if="data.id && type !== ComponentType.mustEdit"><span>{{data[dataKey]}}</span></div>
            <div class="field-content" v-else>
              <mt-select
                @change="selectChange"
                v-model="data[dataKey]"
                :allow-filtering="allowFiltering"
                :data-source="options"
                :show-clear-button="showClearBtn"
                :placeholder="$t('请选择')"
              ></mt-select>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              selectOptions,
              options: [],
              dataKey,
              allowFiltering,
              showClearBtn,
              ComponentChangeType,
              type,
              ComponentType
            }
          },
          beforeDestroy() {
            this.$bus.$off('predictManageComponentChange')
          },
          mounted() {
            // 初始化页面加载时得到的值
            this.options = this.formatOptionsValueToText(selectOptions)
            // 监听变化
            this.onComponentChange()
          },
          methods: {
            // 监听变化
            onComponentChange() {
              this.$bus.$on(`predictManageComponentChange`, (e) => {
                const { requestKey, modifiedKey, changeType, value } = e
                const requestKeyList = [
                  'itemName' // 物料名称
                ]
                if (
                  requestKeyList.includes(requestKey) &&
                  changeType === ComponentChangeType.link &&
                  modifiedKey === dataKey &&
                  dataKey === 'factoryName'
                ) {
                  this.data[dataKey] = null
                  // 更新工厂的数据源
                  this.getFactoryList(value)
                  // 不在页面显示的值改变
                  this.updateNotTable(undefined)
                  // 触发改变code，清空工厂选择的值
                  this.triggerCodeChange(undefined)
                }
              })
            },
            // 修改中的值
            selectChange(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e.value
              const selectValue = this.textToValue({
                options: this.selectOptions,
                text: e.value
              })
              // 不在页面显示的值改变
              this.updateNotTable(selectValue)
              // 触发改变code
              this.triggerCodeChange(selectValue)
              // 触发改变相关的值
              this.triggerLinkChange(selectValue)
            },
            // 不在页面显示的值改变
            updateNotTable(selectValue) {
              // 编辑行时改变rowDataTemp中当前的值
              if (dataKey === 'companyName') {
                // 公司
                rowDataTemp[rowDataTemp.length - 1].companyId = selectValue
              } else if (dataKey === 'itemName') {
                if (!selectValue) {
                  rowDataTemp[rowDataTemp.length - 1].itemId = selectValue
                  return
                }
                // 物料
                for (let i = 0; i < this.options.length; i++) {
                  if (this.options[i].itemCode === selectValue) {
                    rowDataTemp[rowDataTemp.length - 1].itemId = this.options[i].id // 物料id
                    break
                  }
                }
              } else if (dataKey === 'factoryName') {
                if (!selectValue) {
                  rowDataTemp[rowDataTemp.length - 1].factoryId = selectValue
                  return
                }
                // 工厂
                for (let i = 0; i < this.options.length; i++) {
                  if (this.options[i].organizationCode === selectValue) {
                    rowDataTemp[rowDataTemp.length - 1].factoryId = this.options[i].id // 工厂id
                    break
                  }
                }
              }
            },
            // 触发改变相关的值
            triggerLinkChange(selectValue) {
              // 关联的key
              const linkKey = {
                itemName: ['factoryName'], // “物料名称”修改，计划组、采购组、工厂将改变，但是工厂还没被改变前，不应该改变计划组、采购组，因为改变物料，工厂将修改数据源
                factoryName: ['planGroup', 'buyerOrgName'] // “工厂”修改，计划组、采购组改变
              }
              if (linkKey[dataKey]) {
                linkKey[dataKey].forEach((itemLinkKey) => {
                  const args = {
                    requestKey: dataKey, // 触发请求的key
                    modifiedKey: itemLinkKey, // 要被修改的key
                    changeType: ComponentChangeType.link, // 修改类型
                    value: selectValue // 发出的值
                  }
                  this.$bus.$emit('predictManageComponentChange', args)
                })
              }
            },
            // 触发改变code
            triggerCodeChange(selectValue) {
              // 要被修改的code
              const changeCode = {
                itemName: 'itemCode', // “物料名称”，物料编码改变
                factoryName: 'factoryCode', // “工厂”，工厂代码改变
                supplierName: 'supplierCode' // “供应商名称”，供应商编码改变
              }
              if (changeCode[dataKey]) {
                const args = {
                  requestKey: dataKey, // 触发请求的key
                  modifiedKey: changeCode[dataKey], // 要被修改的key
                  changeType: ComponentChangeType.code, // 修改类型
                  value: selectValue // 发出的值
                }
                this.$bus.$emit('predictManageComponentChange', args)
              }
            },
            // 使下拉选择的值转为text
            formatOptionsValueToText(options) {
              const list = []
              if (options.length) {
                options.forEach((item) => {
                  list.push({ ...item, text: item.text, value: item.text })
                })
              }

              return list
            },
            // 将选择的text转为value
            textToValue(args) {
              const { options, text } = args
              let value = undefined
              if (options.length && options.length > 0) {
                for (let i = 0; i < options.length; i++) {
                  if (options[i] && options[i].text === text) {
                    value = options[i].value
                    break
                  }
                }
              }

              return value
            },
            // 获取并更新工厂的数据源
            getFactoryList(itemCode) {
              if (editLinkDataParams.itemCode === itemCode) {
                return
              }
              factoryNameOptions.length = 0
              this.selectOptions.length = 0
              const params = {
                itemCode // 	物料编码
              }
              editLinkDataParams.itemCode = itemCode
              this.$API.masterData.getFactoryList(params).then((res) => {
                const list = res?.data || []
                list.forEach((item) => {
                  factoryNameOptions.push({
                    ...item,
                    text: item.organizationName,
                    value: item.organizationCode
                  })
                })
                this.selectOptions = factoryNameOptions
                this.options = this.formatOptionsValueToText(factoryNameOptions)
              })
            }
          }
        })
      }
    }
    return template
  },
  // 编辑时的 checkBox
  empty: () => {
    return {
      template: Vue.component('emptyComponent', {
        template: `<div></div>`,
        data: function () {
          return {
            data: {}
          }
        }
      })
    }
  },
  /**
   * 预测数据
   *
   * timeInfo => 预测时间;
   * forecastNum => D1预测;
   * orderNum => D2订单;
   * total => D;
   * buyerNum => P;
   * supplierNum => C;
   * planGroupNum => F;
   */
  forecast: (args) => {
    const {
      colIndex,
      type,
      dynamicTitle,
      precision,
      manage,
      maximumValue,
      minimumValue,
      forecastTypeShow
    } = args
    const template = () => {
      return {
        template: Vue.component('forecastComponent', {
          template: `
          <div  class="grid-edit-column mt-flex-direction-column" v-if="data[dynamicTitle[colIndex]]">
            <!-- D1(预测)行 view -->
            <div
             class="forecast-item"
             v-if="(type === ComponentType.view || data.id) &&
              type !== ComponentType.mustEdit &&
              isShowForecast.D1 &&
              manage === 'bd'"
             >{{data[dynamicTitle[colIndex]].forecastNum}}</div>
            <!-- D1(预测)行 edit -->
            <div  class='inputSy' v-if="(type === ComponentType.edit && !data.id) || type === ComponentType.mustEdit && isShowForecast.D1&&manage === 'bd'">
              <mt-input-number
              style='padding: 0px 10px !important'
                @keydown.native="negativeInputOnKeyDown"
                :precision="precision"
                :max="maximumValue"
                :min="minimumValue"
                :show-clear-button="false"
                :show-spin-button="false"
                v-model="data[dynamicTitle[colIndex]].forecastNum"
                @input="changeForecastNum"
                placeholder="">
              </mt-input-number>
            </div>
            
            <!-- D2(订单)行 view -->
            <div
             class="forecast-item"
             v-if="(type === ComponentType.view || data.id) &&
              type !== ComponentType.mustEdit &&
              isShowForecast.D2 &&
              manage === 'bd'"
             >{{data[dynamicTitle[colIndex]].orderNum}}</div>
            <!-- D2(订单)行 edit -->
            <div  class='inputSy' v-if="(type === ComponentType.edit && !data.id) || type === ComponentType.mustEdit && isShowForecast.D2&&
            manage === 'bd'">
              <mt-input-number
              style='padding: 0px 10px !important'
                @keydown.native="negativeInputOnKeyDown"
                :precision="precision"
                :max="maximumValue"
                :min="minimumValue"
                :show-clear-button="false"
                :show-spin-button="false"
                v-model="data[dynamicTitle[colIndex]].orderNum"
                @input="changeOrderNum"
                placeholder="">
              </mt-input-number>
            </div>
            
            <!-- D行 view -->
            <div
             class="forecast-item"
             v-if="(type === ComponentType.view || data.id) &&
              type !== ComponentType.mustEdit &&
              isShowForecast.D"
             >{{data[dynamicTitle[colIndex]].total}}</div>
            <!-- D行 edit -->
            <div  class='inputSy' v-if="(type === ComponentType.edit && !data.id) || type === ComponentType.mustEdit && isShowForecast.D">
              <mt-input-number
              style='padding: 0px 10px !important'
                disabled
                :show-clear-button="false"
                :show-spin-button="false"
                v-model="data[dynamicTitle[colIndex]].total"
                @input="changeTotal"
                placeholder="">
              </mt-input-number>
            </div>
            
            <!-- P行 view -->
            <div
              :class="['forecast-item']"
              v-show="type === ComponentType.view &&
              isShowForecast.P"
              >{{data[dynamicTitle[colIndex]].buyerNum}}</div>
            <!-- P行 edit -->
            <div class='inputSy' v-if="type === ComponentType.edit || type === ComponentType.mustEdit && isShowForecast.P">
              <mt-input-number
              style='padding: 0px 10px !important'
                @keydown.native="negativeInputOnKeyDown"
                :precision="precision"
                :max="maximumValue"
                :min="minimumValue"
                :show-clear-button="false"
                :show-spin-button="false"
                :disabled="isDisabledBuyerNumEdit()"
                v-model="data[dynamicTitle[colIndex]].buyerNum"
                @input="changeBuyerNum"
                placeholder="">
              </mt-input-number>
            </div>
            
            <!-- C行 仅可查看 -->
            <div class="forecast-item" v-show="isShowForecast.C">{{numType(data[dynamicTitle[colIndex]].supplierNum,data)}}</div>
            
            <!-- Gap行 仅可查看 -->
            <div
             :class="['forecast-item', isBuyerNumHighlight() && 'forecast-highlight']"
             v-show="isShowForecast.Gap"
             >{{numType(gap,data)}}</div>
            <!-- F行 view -->
            
            <div
             class="forecast-item"
             v-show="type === ComponentType.view && isShowForecast.F&&
             manage === 'bd'"
             >{{data[dynamicTitle[colIndex]].planGroupNum}}</div>
            <!-- F行 edit -->
            <div class='inputSy' v-if="type === ComponentType.edit && isShowForecast.F&&
            manage === 'bd'">
              <mt-input-number
              style='padding: 0px 10px !important'
                @keydown.native="negativeInputOnKeyDown"
                :precision="precision"
                :max="maximumValue"
                :min="minimumValue"
                :show-clear-button="false"
                :show-spin-button="false"
                :disabled="isDisabledPlanGroupNumEdit()"
                v-model="data[dynamicTitle[colIndex]].planGroupNum"
                @input="changePlanGroupNum"
                placeholder="">
              </mt-input-number>
            </div>
            
          </div>
          <div class="grid-edit-column mt-flex-direction-column" v-else>
            <div v-for="(cellItem, index) in cellItems" :key="index" class="forecast-item"></div>
          </div>`,
          data: function () {
            return {
              data: {},
              colIndex,
              dynamicTitle,
              ComponentType,
              type,
              manage,
              gap: '',
              negativeInputOnKeyDown,
              cellItems: [],
              precision,
              maximumValue,
              minimumValue,
              forecastTypeShow,
              isShowForecast: {
                D1: true,
                D2: true,
                D: true,
                P: true,
                C: true,
                Gap: true,
                F: true
              }
            }
          },
          mounted() {
            // 计算 Gap 行
            this.calculateGap()
            // 设置行显示隐藏
            this.handleForecastTypeShow()
          },
          methods: {
            numType(value, e) {
              if (e.status === 0 || e.status === 1) {
                return ''
              } else {
                return value
              }
            },
            // 计算 Gap 行
            calculateGap() {
              const supplierNum = this.data[dynamicTitle[colIndex]]?.supplierNum || 0 // C行
              const buyerNum = this.data[dynamicTitle[colIndex]]?.buyerNum || 0 // P行
              // Gap 差异 = C行 - P行
              const gapStr = bigDecimal.subtract(String(supplierNum), String(buyerNum))
              this.gap = Number(gapStr) || 0
            },
            // change D1预测 forecastNum
            changeForecastNum(value) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].forecastNum =
                Number(value)
              // 关联D行
              const orderNum = Number(this.data[dynamicTitle[colIndex]].orderNum)
              const totalStr = bigDecimal.add(String(orderNum), String(value))
              const total = Number(totalStr)
              this.data[dynamicTitle[colIndex]].total = total
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].total = total
            },
            // change D2订单 orderNum
            changeOrderNum(value) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].orderNum = Number(value)
              // 关联D行
              const forecastNum = Number(this.data[dynamicTitle[colIndex]].forecastNum)
              const totalStr = bigDecimal.add(String(forecastNum), String(value))
              const total = Number(totalStr)
              this.data[dynamicTitle[colIndex]].total = total
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].total = total
            },
            // change D total
            changeTotal(value) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].total = Number(value)
              // 计算 Gap 行
              this.calculateGap()
            },
            // change P buyerNum
            changeBuyerNum(value) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].buyerNum = Number(value)
              // 计算 Gap 行
              this.calculateGap()
            },
            // P 行可编辑的条件
            isDisabledBuyerNumEdit() {
              let disabled = true
              // 条件
              const condition = [
                Status.feedbackAbnormal, // 反馈异常
                Status.feedbackNormal, // 反馈异常
                Status.modified, // 已修改
                Status.new // 新增
              ]
              if (condition.includes(this.data?.status)) {
                disabled = false
              }

              return disabled
            },
            // change F planGroupNum
            changePlanGroupNum(value) {
              if (value === '') {
                rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].planGroupNum = value
              } else {
                rowDataTemp[rowDataTemp.length - 1][dynamicTitle[colIndex]].planGroupNum =
                  Number(value)
              }
              // 编辑行时改变rowDataTemp中当前的值
            },
            // F 行可编辑的条件
            isDisabledPlanGroupNumEdit() {
              let disabled = true
              // 条件
              const condition = [
                Status.new, // 新建
                Status.modified, // 待反馈
                Status.pendingFeedback, // 待反馈
                Status.feedbackNormal, // 反馈正常
                Status.feedbackAbnormal, // 反馈异常
                Status.confirmed // 已确认
              ]
              if (condition.includes(this.data?.status)) {
                disabled = false
              }

              return disabled
            },
            // Gap(差异)不等于 0 时高亮
            isBuyerNumHighlight() {
              let isHighlight = false

              if (this.gap !== 0) {
                isHighlight = true
              }

              return isHighlight
            },
            // 设置行显示隐藏
            handleForecastTypeShow() {
              if (forecastTypeShow?.length > 0) {
                // 勾选快捷搜索项，仅显示勾选项
                this.isShowForecast = {
                  D1: false,
                  D2: false,
                  D: false,
                  P: false,
                  C: false,
                  Gap: false,
                  F: false
                }
                if (forecastTypeShow.includes(ForecastDataTypeCell[0].title)) {
                  this.isShowForecast.D1 = true
                }
                if (forecastTypeShow.includes(ForecastDataTypeCell[1].title)) {
                  this.isShowForecast.D2 = true
                }
                if (forecastTypeShow.includes(ForecastDataTypeCell[2].title)) {
                  this.isShowForecast.D = true
                }
                if (forecastTypeShow.includes(ForecastDataTypeCell[3].title)) {
                  this.isShowForecast.P = true
                }
                if (forecastTypeShow.includes(ForecastDataTypeCell[4].title)) {
                  this.isShowForecast.C = true
                }
                if (forecastTypeShow.includes(ForecastDataTypeCell[5].title)) {
                  this.isShowForecast.Gap = true
                }
                if (forecastTypeShow.includes(ForecastDataTypeCell[6].title)) {
                  this.isShowForecast.F = true
                }
                // 处理空的数据占位符
                ForecastDataTypeCell.forEach((itemType) => {
                  if (forecastTypeShow.includes(itemType.title)) {
                    this.cellItems.push(itemType)
                  }
                })
              } else {
                // 没有勾选快捷搜索项，默认显示所有字段
                this.isShowForecast = {
                  D1: true,
                  D2: true,
                  D: true,
                  P: true,
                  C: true,
                  Gap: true,
                  F: true
                }
                // 默认显示所有 处理空的数据占位符
                this.cellItems = ForecastDataTypeCell
              }
            }
          }
        })
      }
    }
    return template
  },
  // 不可编辑的类型 D1(预测) D2(订单) D P C F
  type: (args) => {
    const { forecastTypeShow } = args
    const template = () => {
      return {
        template: Vue.component('typeComponent', {
          template: `<div class="grid-edit-column mt-flex-direction-column forecast-type-box"><div class="forecast-item" v-for="(cellItem, index) in cellItems" :key="index">{{cellItem.title}}</div></div>`,
          data: function () {
            return {
              data: {},
              cellItems: [],
              forecastTypeShow,
              manage: sessionStorage.getItem('manage'),
              obj: []
            }
          },
          // watch: {
          //   ForecastDataTypeCell: {
          //     handler(newVal) {
          //       alert(1);
          //     },
          //     immediate: true,
          //   },
          // },
          mounted() {
            this.handleForecastTypeShow()
          },
          methods: {
            handleForecastTypeShow() {
              if (this.manage === 'kt') {
                this.obj = [
                  {
                    title: 'D(原始需求)'
                  },
                  {
                    title: 'P(需求量)'
                  },
                  {
                    title: 'C(承诺量)'
                  },
                  {
                    title: 'Gap(差异)'
                  }
                ]
              } else {
                this.obj = [
                  {
                    title: i18n.t('D1(预测)')
                  },
                  {
                    title: i18n.t('D2(订单)')
                  },
                  {
                    title: i18n.t('D(原始需求)')
                  },
                  {
                    title: i18n.t('P(需求量)')
                  },
                  {
                    title: i18n.t('C(承诺量)')
                  },
                  {
                    title: i18n.t('Gap(差异)')
                  },
                  {
                    title: 'F'
                  }
                ]
              }

              if (forecastTypeShow.length > 0) {
                this.obj.forEach((itemType) => {
                  if (forecastTypeShow.includes(itemType.title)) {
                    this.cellItems.push(itemType)
                  }
                })
              } else {
                // 默认显示所有
                this.cellItems = this.obj
              }
            }
          }
        })
      }
    }
    return template
  },
  // 不可编辑的文字
  text: (args) => {
    const { dataKey } = args
    const template = () => {
      return {
        template: Vue.component('textComponent', {
          template: `<div class="grid-edit-column mt-flex-direction-column"><div class="field-content"><span>{{data[dataKey]}}</span></div></div>`,
          data: function () {
            return { data: {}, dataKey }
          }
        })
      }
    }
    return template
  },
  // 不可编辑的错误文字
  errorText: (args) => {
    const { dataKey } = args
    const template = () => {
      return {
        template: Vue.component('textComponent', {
          template: `<div class="grid-edit-column mt-flex-direction-column"><div class="field-content"><span class="errorText">{{data[dataKey]}}</span></div></div>`,
          data: function () {
            return { data: {}, dataKey }
          }
        })
      }
    }
    return template
  },
  // 版本
  versionText: () => {
    return {
      template: Vue.component('textComponent', {
        template: `<div class="grid-edit-column mt-flex-direction-column"><div class="field-content"><span>{{version}}</span></div></div>`,
        data: function () {
          return { data: {}, version: '' }
        },
        mounted() {
          if (this.data.syncVersion && this.data.publishVersion >= 0) {
            this.version = `${this.data.syncVersion}.${this.data.publishVersion}`
          } else if (this.data.syncVersion) {
            this.version = this.data.syncVersion
          }
        }
      })
    }
  },
  // 状态
  status: (args) => {
    const { type, cellTools } = args
    const template = () => {
      return {
        // 按钮：发布，取消发布，删除，确认
        template: Vue.component('statusComponent', {
          template: `<div class="grid-edit-column mt-flex-direction-column"
          >
            <div class="field-content"><span :class="[StatusClass[data.status]]">{{data.status | dataFormat}}</span></div>
            <div class="column-tool mt-flex invite-btn" v-if="type === ComponentType.view">
              <div v-for="(cellTool, index) in cellTools"
                :key="index" :id="cellTool.id"
                class="template-svg"
                v-if="!cellTool.visibleCondition || cellTool.visibleCondition(data)"
                @click.stop="clickCellTool({tool:cellTool, data})"
                ><MtIcon :name="cellTool.icon" />&nbsp<span class="icon-title">{{cellTool.title}}</span>
              </div>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              type,
              StatusClass,
              StatusText,
              cellTools,
              ComponentType
            }
          },
          filters: {
            // 值转换
            dataFormat(value) {
              let result = value
              if (StatusText[value]) {
                result = StatusText[value]
              }
              return result
            }
          },
          methods: {
            clickCellTool(data) {
              this.$parent.$emit('handleClickCellTool', data)
            }
          }
        })
      }
    }
    return template
  },
  // 带红星的表头
  requiredHeader: (args) => {
    const { headerText } = args
    const template = () => {
      return {
        template: Vue.component('requiredHeaderComponent', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{ headerText }}</span>
              </div>
            `,
          data: function () {
            return {
              data: {},
              headerText
            }
          },
          beforeDestroy() {},
          mounted() {},
          methods: {}
        })
      }
    }
    return template
  }
}

export default ForecastColumnComponent
