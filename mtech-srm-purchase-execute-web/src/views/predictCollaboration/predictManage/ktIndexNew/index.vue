<template>
  <!-- 预测管理-采方 -->
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      @reset="handleCustomReset"
      @search="handleCustomSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="versionNo" :label="$t('大版本号')" label-style="top">
          <mt-date-picker
            :show-clear-button="true"
            :placeholder="$t('请选择大版本号')"
            v-model="searchFormModel.versionNo"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="siteCodeList" :label="$t('工厂')" label-style="top">
          <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.siteCodeList"
            url="/masterDataManagement/tenant/site/app/paged-query"
            multiple
            :placeholder="$t('请选择工厂')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.itemCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.itemName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="buyerGroupCodeList" :label="$t('采购组')">
          <RemoteAutocomplete
            v-model="searchFormModel.buyerGroupCodeList"
            url="/masterDataManagement/tenant/business-group/paged-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'groupName', value: 'groupCode' }"
            :search-fields="['groupName', 'groupCode']"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.supplierCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.supplierName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="supplierBlank" :label="$t('供应商为空')" label-style="top">
          <mt-select
            v-model="searchFormModel.supplierBlank"
            :data-source="[
              { text: $t('否'), value: 0 },
              { text: $t('是'), value: 1 }
            ]"
            :show-clear-button="true"
            :placeholder="$t('请选择供应商是否为空')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="typeList" :label="$t('类型')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.typeList"
            :data-source="typeList"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="statusList" :label="$t('状态')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.statusList"
            :data-source="statusOptions"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="platform" :label="$t('平台')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.platform"
            :placeholder="$t('支持模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="itemGroupCodeList" :label="$t('物料组')" label-style="top">
          <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.itemGroupCodeList"
            url="/masterDataManagement/tenant/item-group/criteria-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'name', value: 'code' }"
            records-position="data"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="categoryCodeList" :label="$t('外部物料组')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.categoryCodeList"
            url="/masterDataManagement/tenant/category/paged-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'categoryName', value: 'categoryCode' }"
            :search-fields="['categoryName', 'categoryCode']"
          />
        </mt-form-item>
        <mt-form-item prop="fieldTypeList" :label="$t('正式与预测显示切换')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.fieldTypeList"
            :data-source="[
              { text: $t('正式需求'), value: 1 },
              { text: $t('预测需求'), value: 2 },
              { text: $t('需求合计'), value: 3 }
            ]"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="publishTime" :label="$t('最后发布时间')" label-style="top">
          <mt-date-range-picker
            style="flex: 1"
            v-model="searchFormModel.publishTime"
            @change="(e) => handleDateTimeChange(e, 'publishTime')"
            :placeholder="$t('请选择最后发布时间')"
          />
        </mt-form-item>
        <mt-form-item prop="feedbackTime" :label="$t('最后反馈时间')" label-style="top">
          <mt-date-range-picker
            style="flex: 1"
            v-model="searchFormModel.feedbackTime"
            @change="(e) => handleDateTimeChange(e, 'feedbackTime')"
            :placeholder="$t('请选择最后反馈时间')"
          />
        </mt-form-item>
        <mt-form-item prop="isSatisfyList" :label="$t('正式需求是否满足')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.isSatisfyList"
            :data-source="[
              { text: $t('否'), value: 'N' },
              { text: $t('是'), value: 'Y' }
            ]"
            :show-clear-button="true"
            :placeholder="$t('请选择正式需求是否满足')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item
          prop="forecastIsSatisfyList"
          :label="$t('预测需求是否满足')"
          label-style="top"
        >
          <mt-multi-select
            v-model="searchFormModel.forecastIsSatisfyList"
            :data-source="[
              { text: $t('否'), value: 'N' },
              { text: $t('是'), value: 'Y' }
            ]"
            :show-clear-button="true"
            :placeholder="$t('请选择预测需求是否满足')"
          ></mt-multi-select>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        :row-config="{ height: rowHeight }"
        :columns="columns"
        :table-data="tableData"
        show-overflow
        height="auto"
        header-align="left"
        align="left"
        style="padding-top: unset"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true,
          activeMethod: this.activeRowMethod
        }"
        :cell-style="cellStyle"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        @edit-closed="editComplete"
        @edit-actived="editBegin"
        @edit-disabled="editDisabledEvent"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #purchaserRemarkEdit="{ row }">
          <vxe-input v-model="row.purchaserRemark" :disabled="row.status > 1"></vxe-input>
        </template>
        <template #siteCodeEdit="{ row }">
          <vxe-select
            v-model="row.siteCode"
            :placeholder="$t('请选择工厂')"
            :options="siteOptions"
            transfer
            filterable
            @change="(value) => selectSiteCode(value, row)"
          ></vxe-select>
        </template>
        <template #buyerGroupCodeEdit="{ row }">
          <vxe-pulldown ref="xDownBuyer" transfer>
            <template #default>
              <vxe-input
                :value="row.buyerGroupCode"
                :placeholder="$t('请选择采购组')"
                readonly
                :disabled="!!row.planner"
                @click="focusBuyerCode(item, row)"
              ></vxe-input>
            </template>
            <template #dropdown>
              <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                :disabled="!!row.planner"
                @keyup="(e) => keyupBuyerGroupCode(e, row)"
                style="width: 100%"
              ></vxe-input>
              <vxe-list
                height="200"
                class="predict-vxe-dropdown"
                :data="buyerGroupOptions"
                auto-resize
              >
                <template #default="{ items }">
                  <div
                    v-show="buyerGroupOptions.length"
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.value"
                    @click="selectBuyerGroupCode(item, row)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                  <div v-show="!buyerGroupOptions.length" class="predict-vxe-list-item">
                    <span>{{ $t('暂无数据') }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template>
        <template #itemCodeEdit="{ row }">
          <vxe-pulldown ref="xDownItem" transfer>
            <template #default>
              <vxe-input
                :value="row.itemCode"
                :placeholder="$t('请选择物料')"
                readonly
                :disabled="!!row.planner"
                @click="(e) => focusItemCode(row)"
              ></vxe-input>
            </template>
            <template #dropdown>
              <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                :disabled="!!row.planner"
                @keyup="(e) => keyupItemCode(e, row)"
                style="width: 100%"
              ></vxe-input>
              <vxe-list height="200" class="predict-vxe-dropdown" :data="itemOptions" auto-resize>
                <template #default="{ items }">
                  <div
                    v-show="itemOptions.length"
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.value"
                    @click="selectItemCode(item, row)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                  <div v-show="!itemOptions.length" class="predict-vxe-list-item">
                    <span>{{ $t('暂无数据') }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template>
        <template #supplierCodeEdit="{ row }">
          <vxe-pulldown ref="xDown" transfer>
            <template #default>
              <vxe-input
                :value="row.supplierCode"
                :placeholder="$t('请选择供应商')"
                readonly
                :disabled="!!row.planner"
                @click="focusSupplierCode(item, row)"
              ></vxe-input>
            </template>
            <template #dropdown>
              <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                :disabled="!!row.planner"
                @keyup="keyupSupplierCode"
                style="width: 100%"
              ></vxe-input>
              <vxe-list
                height="200"
                class="predict-vxe-dropdown"
                :data="supplierOptions"
                auto-resize
              >
                <template #default="{ items }">
                  <div
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.value"
                    @click="selectSupplierCode(item, row)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
    <mt-dialog ref="toast" :header="$t('提示')" :buttons="buttons">
      <div style="height: 100%; padding-top: 20px">
        <div class="tips-container">
          <p v-for="(item, index) in confirmTips" :key="index">
            {{ item }}
          </p>
        </div>

        <div style="color: red">{{ $t('是否统一确认？确认后C行将默认等于P行') }}</div>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { StatusSearchOptions, forecastColumnData, ToolBar } from './config/constant'
import { utils } from '@mtech-common/utils'
import dayjs from 'dayjs'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      toolbar: ToolBar,
      siteOptions: [], // 工厂下拉选项
      supplierOptions: [],
      buyerGroupOptions: [],
      itemOptions: [],
      getItemDataSource: null,
      getSupplierDataSource: null,
      getBuyerGroupSource: null,
      searchFormModel: {
        versionNo: new Date()
      },
      tableData: [],
      columns: forecastColumnData,
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      statusOptions: StatusSearchOptions,
      typeList: [
        { text: this.$t('D(原始需求)'), value: 'D' },
        { text: this.$t('P(需求量)'), value: 'P' },
        { text: this.$t('C(承诺量)'), value: 'C' },
        { text: this.$t('Gap'), value: 'GAP' },
        { text: this.$t('累计Gap'), value: 'GAP_TOTAL' },
        { text: this.$t('F(承诺预实冲减)'), value: 'F' }
      ],
      warningContent: this.$t('此状态数据不可编辑'),
      rowHeight: 120,
      fixPreColumns: [
        {
          fieldCode: 'type',
          fieldName: this.$t('类型')
        },
        {
          fieldCode: 'totalDayZs',
          fieldName: this.$t('31天汇总(正式)'),
          type: 1
        },
        {
          fieldCode: 'totalDayYc',
          fieldName: this.$t('31天汇总(预测)'),
          type: 2
        },
        {
          fieldCode: 'totalDay',
          fieldName: this.$t('31天汇总(合计)'),
          type: 3
        },
        {
          fieldCode: 'totalWeekZs',
          fieldName: this.$t('周汇总(正式)'),
          type: 1
        },
        {
          fieldCode: 'totalWeekYc',
          fieldName: this.$t('周汇总(预测)'),
          type: 2
        },
        {
          fieldCode: 'totalWeek',
          fieldName: this.$t('周汇总(合计)'),
          type: 3
        },
        {
          fieldCode: 'totalMonthZs',
          fieldName: this.$t('月汇总(正式)'),
          type: 1
        },
        {
          fieldCode: 'totalMonthYc',
          fieldName: this.$t('月汇总(预测)'),
          type: 2
        },
        {
          fieldCode: 'totalMonth',
          fieldName: this.$t('月汇总(合计)'),
          type: 3
        }
      ],
      fixRightColumns: [
        {
          fieldCode: 'totalOtherMonthZs',
          fieldName: this.$t('其它月汇总(正式)'),
          type: 1
        },
        {
          fieldCode: 'totalOtherMonthYc',
          fieldName: this.$t('其它月汇总(预测)'),
          type: 2
        },
        {
          fieldCode: 'totalOtherMonth',
          fieldName: this.$t('其它月汇总(合计)'),
          type: 3
        }
      ],
      defaultDynamicAddData: [], // 默认增加数据
      buttons: [
        {
          click: this.confirmNo,
          buttonModel: { content: this.$t('否') }
        },
        {
          click: this.confirmYes,
          buttonModel: { content: this.$t('是') }
        }
      ],
      confirmTips: [],
      confirmIds: []
    }
  },
  mounted() {
    // 供应商、物料、采购组防抖
    this.getSupplierDataSource = utils.debounce(this.getSupplier, 1000)
    this.getItemDataSource = utils.debounce(this.getItem, 1000)
    this.getBuyerGroupSource = utils.debounce(this.getBuyerGroup, 1000)
  },
  beforeRouteLeave(to, from, next) {
    document.querySelector('.vxe-table--body-wrapper').scrollTop = 0
    next()
  },
  methods: {
    // ---------------------------------------- 表格操作 start：--------------------------------------------------------
    // 表格 - 单元格样式
    cellStyle({ row, column }) {
      if (column.field === 'status') {
        if (row.status == 4) {
          return {
            color: 'red'
          }
        }
      }
    },
    editDisabledEvent() {
      this.$toast({
        content: this.warningContent,
        type: 'warning'
      })
      this.warningContent = this.$t('此状态数据不可编辑')
    },
    activeRowMethod({ row }) {
      // 已确认的数据不可修改
      if ([2, 5].includes(row.status)) {
        return false
      }
      return true
    },
    editBegin(args) {
      const { row } = args
      if (args.$event) {
        this.getSite()
        this.getSupplierDataSource({ value: row.supplierCode })
        this.getBuyerGroupSource({ value: row.buyerGroupCode })
      }
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 校验点击的对象是否为取消编辑按钮
        if (args.$event.target.innerText === this.$t('取消编辑')) {
          // 清除编辑状态
          this.$refs.xTable.$refs.xGrid.clearEdit()
          this.handleCustomSearch()
          return
        }
        // 数据校验
        if (!this.validData(row)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow(row)
          return
        }
        // 新增
        const isAdd = row?.id?.includes('row_')
        if (isAdd) {
          this.addForecastData(row)
        } else {
          this.updateForecastData(row)
        }
      }
    },
    // ---------------------------------------- 表格操作 end:---------------------------------------------------------------
    // ---------------------------------------- 表格下拉组件相关事件（后续封装）start：----------------------------------------
    focusSupplierCode() {
      this.$refs.xDown.showPanel()
    },
    focusBuyerCode() {
      this.$refs.xDownBuyer.showPanel()
    },
    focusItemCode(row) {
      if (!row.siteCode) {
        this.$toast({ content: this.$t('请先选择工厂'), type: 'warning' })
        return false
      }
      this.getItemDataSource({ value: row.itemCode }, row)
      this.$refs.xDownItem.showPanel()
    },
    keyupSupplierCode(e) {
      this.getSupplierDataSource(e)
    },
    keyupBuyerGroupCode(e, row) {
      this.getBuyerGroupSource(e, row)
    },
    keyupItemCode(e, row) {
      this.getItemDataSource(e, row)
    },
    selectSupplierCode(e, row) {
      row.supplierCode = e.supplierCode
      row.supplierName = e.supplierName
      row.supplierId = e.id
      this.$refs.xDown.hidePanel()
    },
    selectBuyerGroupCode(e, row) {
      row.buyerGroupCode = e.groupCode
      row.buyerGroupName = e.groupName
      this.$refs.xDownBuyer.hidePanel()
    },
    selectItemCode(e, row) {
      row.itemCode = e.itemCode
      row.itemName = e.itemName
      row.itemId = e.id
      this.$refs.xDownItem.hidePanel()
    },
    // 获取数据 - 供应商
    getSupplier(args) {
      const { value } = args
      const params = {
        fuzzyNameOrCode: value ? value : ''
      }
      this.$API.masterData.getSupplier(params).then((res) => {
        if (res) {
          const list = res?.data || []
          const newData = list.map((i) => {
            return {
              ...i,
              label: `${i.supplierCode}-${i.supplierName}`,
              value: i.supplierCode
            }
          })
          this.supplierOptions = [...newData]
        }
      })
    },
    // 获取数据 - 物料
    getItem(args, row) {
      const { value } = args
      const params = {
        page: { current: 1, size: 100 },
        condition: 'and',
        rules: [
          {
            label: this.$t('物料编号'),
            field: 'itemCode',
            type: 'string',
            operator: 'contains',
            value
          }
        ],
        defaultRules: [
          {
            field: 'organizationCode',
            operator: 'equal',
            value: row?.siteCode
          }
        ]
      }
      this.$API.masterData.getItemPage(params).then((res) => {
        if (res) {
          const list = res?.data?.records || []
          const newData = list.map((i) => {
            return {
              ...i,
              label: `${i.itemCode}-${i.itemName}`,
              value: i.itemCode
            }
          })
          this.itemOptions = [...newData]
        }
      })
    },
    // 获取数据 - 采购组
    getBuyerGroup(args) {
      const { value } = args
      const params = {
        condition: 'and',
        page: { current: 1, size: 20 }
      }
      if (value) {
        params.rules = [
          {
            condition: 'and',
            rules: [
              {
                condition: 'or',
                label: '',
                field: 'groupName',
                type: 'string',
                operator: 'contains',
                value
              },
              {
                condition: 'or',
                label: '',
                field: 'groupCode',
                type: 'string',
                operator: 'contains',
                value
              }
            ]
          }
        ]
      }
      this.$API.masterData.getGroup(params).then((res) => {
        if (res) {
          const list = res?.data?.records || []
          this.buyerGroupOptions = list.map((i) => {
            return {
              ...i,
              label: `${i.groupCode}-${i.groupName}`,
              value: i.groupCode
            }
          })
        }
      })
    },
    // 获取数据 - 工厂
    getSite() {
      const params = {
        page: { current: 1, size: 1000 }
      }
      this.$API.masterData.getSiteData(params).then((res) => {
        const list = res?.data?.records || []
        this.siteOptions = list.map((i) => {
          return {
            ...i,
            label: `${i.siteCode}-${i.siteName}`,
            value: i.siteCode
          }
        })
      })
    },
    selectSiteCode(val, row) {
      const { value } = val
      const _find = this.siteOptions.find((item) => item.siteCode === value)
      row.siteCode = value
      row.siteName = _find?.siteName
      // 清空itemCode
      row.itemCode = null
      row.itemName = null
      row.itemId = null
    },
    // ---------------------------------------- 表格下拉组件相关事件（后续封装）end：----------------------------------------
    // ---------------------------------------- 按钮操作 ：start -------------------------------------------------------------
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      if (!this.tableData.length && code !== 'ForecastImport') {
        this.$toast({ content: this.$t('请先查询数据再进行操作'), type: 'warning' })
        return
      }
      if (
        !selectedRecords?.length &&
        ['ForecastDelete', 'ForecastPublish', 'ForecastConfirm'].includes(code)
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })

      if (code === 'ForecastAdd') {
        // 新增
        this.handleAdd()
      } else if (code === 'ForecastDelete') {
        // 删除
        this.handleDelete(idList)
      } else if (code === 'ForecastPublish') {
        // 发布
        this.handlePublish(idList)
      } else if (code === 'ForecastConfirm') {
        // 确认
        this.handleConfirm(idList)
      } else if (code === 'ForecastImport') {
        // 导入
        this.handleImport()
      } else if (code === 'ForecastExportReImport') {
        // 导出（可导入）
        this.handleExport('re')
      } else if (code === 'ForecastExport') {
        // 导出
        this.handleExport()
      }
    },
    // 新增
    handleAdd() {
      const newData = {
        versionNo: dayjs(this.searchFormModel.versionNo).format('YYYYMMDD'),
        status: 0,
        statusName: this.$t('草稿'),
        forecastDetailResList: this.defaultDynamicAddData,
        isAdd: true // 新增标识：判断新增时候只能编辑p行
      }
      // 新增一行
      this.$refs.xTable.$refs.xGrid.insert([newData])

      this.$nextTick(() => {
        // 初始化请求数据
        this.getSite()
        // this.getItemDataSource({ value: row.itemCode }) // 物料通过选择工厂后再确认下拉内容
        this.getSupplierDataSource({ value: '' })
        this.getBuyerGroupSource({ value: '' })
        // 获取最新的表格视图数据
        const currentViewRecords = this.$refs.xTable.$refs.xGrid.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.$refs.xTable.$refs.xGrid.setEditRow(currentViewRecords[0])
      })
    },
    // 删除
    handleDelete(ids) {
      // 删除
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.apiStartLoading()
          this.$API.predictCollaboration
            .deleteForecast({ ids })
            .then((res) => {
              this.apiEndLoading()
              if (res?.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.handleCustomSearch()
              }
            })
            .catch(() => {
              this.apiEndLoading()
            })
        }
      })
    },
    // 发布
    handlePublish(ids) {
      this.apiStartLoading()
      this.$API.predictCollaboration
        .publishForecast({ ids })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 确认
    handleConfirm(ids) {
      this.apiStartLoading()
      this.confirmIds = []
      this.$API.predictCollaboration
        .confirmCheckForecast({ ids })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.confirmTips = res.data
            this.$refs.toast.ejsRef.show()
            this.confirmIds = [...ids]
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    confirmNo() {
      this.$API.predictCollaboration
        .confirmForecast({ ids: this.confirmIds })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    confirmYes() {
      this.$API.predictCollaboration
        .confirmV2Forecast({ ids: this.confirmIds })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方 -导入
    handleImport() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel,
        versionNo: dayjs(this.searchFormModel.versionNo).format('YYYYMMDD')
      }
      // 导入
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.predictCollaboration.importForecast,
          downloadTemplateApi: this.$API.predictCollaboration.exportReImportForecast,
          paramsKey: 'excel',
          downloadTemplateParams: {
            ...params
          }
        },
        success: () => {
          this.handleCustomSearch()
        }
      })
    },
    // 采方-导出
    handleExport(type) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel,
        versionNo: dayjs(this.searchFormModel.versionNo).format('YYYYMMDD')
      }
      this.apiStartLoading()
      this.$API.predictCollaboration[type === 're' ? 'exportReImportForecast' : 'exportForecast'](
        params
      ).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 查询
    async handleCustomSearch() {
      let params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel,
        versionNo: dayjs(this.searchFormModel.versionNo).format('YYYYMMDD')
      }
      this.rowHeight = 32 * (this.searchFormModel.typeList?.length || this.typeList.length)
      this.apiStartLoading()
      // 处理动态列
      await this.getDynamicColumn()
      await this.$API.predictCollaboration
        .queryBuyerKTForecast(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)

            const records = res?.data?.records || [] // 表格数据
            this.tableData = [...records]
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 重置查询条件
    handleCustomReset() {
      this.searchFormModel = {
        versionNo: new Date()
      }
      this.handleCustomSearch()
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // ---------------------------------------- 按钮操作 ：start -------------------------------------------------------------

    // ---------------------------------------- 数据处理 ：start -------------------------------------------------------------
    // 修改预测信息
    updateForecastData(row) {
      const params = []
      const obj = { ...row, mainId: row.id }
      delete obj.forecastDetailResList
      row.forecastDetailResList.forEach((v) => {
        if (['P', 'C'].includes(v.type)) {
          params.push({ ...obj, ...v })
        }
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认修改该数据？')
        },
        success: () => {
          this.apiStartLoading()
          this.$API.predictCollaboration
            .updateForecast(params)
            .then((res) => {
              this.apiEndLoading()
              if (res?.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.handleCustomSearch()
              }
            })
            .catch(() => {
              this.apiEndLoading()
              // 当出现错误时，指定行进入编辑状态
              this.$refs.xTable.$refs.xGrid.setEditRow([row])
            })
        },
        close: () => {
          this.$refs.xTable.$refs.xGrid.setEditRow([row])
        }
      })
    },
    // 新增预测信息
    addForecastData(row) {
      const params = []
      const obj = { ...row, id: null }
      delete obj.forecastDetailResList
      row.forecastDetailResList.forEach((v) => {
        if (['P'].includes(v.type)) {
          params.push({ ...obj, ...v })
        }
      })
      this.$API.predictCollaboration
        .addForecast(params)
        .then((res) => {
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow([row])
        })
    },
    // 校验数据
    validData(data) {
      const { itemCode, siteCode, supplierCode, buyerGroupCode } = data
      let valid = false
      if (!siteCode) {
        // 工厂代码
        this.$toast({ content: this.$t('工厂不可为空'), type: 'warning' })
      } else if (!buyerGroupCode) {
        // 采购组
        this.$toast({ content: this.$t('采购组不可为空'), type: 'warning' })
      } else if (!itemCode) {
        // 物料代码
        this.$toast({ content: this.$t('物料不可为空'), type: 'warning' })
      } else if (!supplierCode) {
        // 供应商代码
        this.$toast({ content: this.$t('供应商不可为空'), type: 'warning' })
      } else {
        valid = true
      }
      return valid
    },

    // 获取动态列
    async getDynamicColumn() {
      let params = {
        versionNo: dayjs(this.searchFormModel.versionNo).format('YYYYMMDD'),
        fieldTypeList: this.searchFormModel.fieldTypeList
      }
      await this.$API.predictCollaboration.queryBuyerKTForecastHeader(params).then((res) => {
        if (res.code === 200) {
          this.handleColumns(res.data)
        }
      })
    },
    handleColumns(data) {
      // 固定表头 - 汇总
      let preColumn = []
      let rightColumn = []
      let fixPreColumns = this.fixPreColumns.filter(
        (item) =>
          this.searchFormModel.fieldTypeList?.includes(item.type) || item.fieldCode === 'type'
      )
      if (!fixPreColumns?.length) fixPreColumns = [...this.fixPreColumns]
      fixPreColumns.forEach((item) => {
        preColumn.push({
          field: item.fieldCode,
          title: item.fieldName,
          minWidth: 160,
          showOverflow: true,
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return row.forecastDetailResList?.map((detail, index) => (
                <div key={index} class='vxe-cell-border'>
                  <span
                    style={{ 'margin-left': '10px' }}
                    class={detail[item.fieldCode] < 0 ? 'red' : ''}>
                    {item.fieldCode === 'type'
                      ? this.filterType(detail[item.fieldCode])
                      : detail[item.fieldCode]}
                  </span>
                </div>
              ))
            }
          }
        })
      })
      // 固定表头 - 汇总
      let fixRightColumns = this.fixRightColumns.filter((item) =>
        this.searchFormModel.fieldTypeList?.includes(item.type)
      )
      if (!fixRightColumns?.length) fixRightColumns = [...this.fixRightColumns]
      fixRightColumns.forEach((item) => {
        rightColumn.push({
          field: item.fieldCode,
          title: item.fieldName,
          minWidth: 160,
          showOverflow: true,
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return row.forecastDetailResList.map((detail, index) => (
                <div key={index} class='vxe-cell-border'>
                  <span
                    style={{ 'margin-left': '10px' }}
                    class={detail[item.fieldCode] < 0 ? 'red' : ''}>
                    {detail[item.fieldCode]}
                  </span>
                </div>
              ))
            }
          }
        })
      })
      // 动态的日期表头
      const dynamicColumn = []
      data.forEach((item) => {
        const field = item.fieldCode
        const title = item.fieldName
        dynamicColumn.push({
          field,
          title,
          minWidth: 200,
          showOverflow: true,
          editRender: {},
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return row.forecastDetailResList?.map((detail, index) => (
                <div key={index} class='vxe-cell-border'>
                  <span
                    style={{ 'margin-left': '10px' }}
                    class={detail[item.fieldCode] < 0 ? 'red' : ''}>
                    {detail[item.fieldCode]}
                  </span>
                </div>
              ))
            },
            edit: ({ row }) => {
              const typeArr = row?.isAdd ? ['P'] : ['P', 'C']
              return row.forecastDetailResList.map((detail, index) => (
                <div key={index} class='vxe-cell-border'>
                  {typeArr.includes(detail.type) && [0, 1, 3, 4].includes(row.status) ? (
                    <vxe-input type='number' v-model={detail[item.fieldCode]} />
                  ) : (
                    <span
                      style={{ 'margin-left': '10px' }}
                      class={detail[item.fieldCode] < 0 ? 'red' : ''}>
                      {detail[item.fieldCode]}
                    </span>
                  )}
                </div>
              ))
            }
          }
        })
      })
      this.defaultDynamicAddData = []
      const _columns = [...preColumn, ...dynamicColumn, ...rightColumn]
      this.typeList.forEach((item) => {
        let obj = {}
        _columns.forEach((column) => {
          obj[column.field] = 0
        })
        obj.type = item.value
        this.defaultDynamicAddData.push(obj)
      })
      this.columns = [...forecastColumnData, ...preColumn, ...dynamicColumn, ...rightColumn]
    },
    // ---------------------------------------- 数据处理 ：end -------------------------------------------------------------

    // ---------------------------------------- 工具函数 ：start -------------------------------------------------------------
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    },
    filterType(type) {
      const _find = this.typeList.find((item) => item.value === type)
      return _find?.text
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = new Date(e.startDate).getTime()
        this.searchFormModel[field + 'End'] = new Date(e.endDate).getTime()
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    }
    // ---------------------------------------- 工具函数 ：end -------------------------------------------------------------
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  .forecast-type-item {
    &:first-child {
      .vxe-cell-border {
        border-top: none;
      }
    }
    .vxe-cell-border {
      border-top: solid #e6e9ed 1px;
    }
  }
  .dynamic-column-item {
    height: 114px;
    .vxe-cell-border {
      border-top: solid #e6e9ed 1px;
      &:first-child {
        border-top: none;
      }
    }
  }
  .vxe-cell-border {
    &:first-child {
      border-top: none;
    }
    border-top: solid #e6e9ed 1px;
    height: 32px;
    line-height: 32px;
    box-sizing: border-box;
    .red {
      color: red;
    }
  }
}
.tips-container {
  height: 445px;
  overflow: scroll;
  p {
    font-size: 14px;
    line-height: 20px;
  }
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
