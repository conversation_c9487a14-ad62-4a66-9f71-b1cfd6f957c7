<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="factoryCode" :label="$t('工厂')" label-style="top">
              <mt-input
                v-model="searchFormModel.factoryCode"
                :show-clear-button="true"
                :placeholder="$t('请输入工厂')"
              />
            </mt-form-item>
            <mt-form-item prop="itemCode" :label="$t('物料')" label-style="top">
              <mt-input
                v-model="searchFormModel.itemCode"
                :show-clear-button="true"
                :placeholder="$t('请输入物料')"
              />
            </mt-form-item>
            <mt-form-item prop="plannerName" :label="$t('计划员')" label-style="top">
              <mt-input
                v-model="searchFormModel.plannerName"
                :show-clear-button="true"
                :placeholder="$t('请输入计划员')"
              />
            </mt-form-item>
            <mt-form-item prop="emailAddr" :label="$t('邮箱')" label-style="top">
              <mt-input
                v-model="searchFormModel.emailAddr"
                :show-clear-button="true"
                :placeholder="$t('请输入邮箱')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { templatePageConfig } from './config'

export default {
  components: {},
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      pageConfig: templatePageConfig
    }
  },
  computed: {},
  // created() {
  //   this.pageConfig = pageConfig(this)
  // },
  methods: {
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  /deep/ .template-wrap {
    overflow-y: auto;
  }
}
</style>
