//现场评审得分设置Tab
import { i18n } from '@/main.js'

const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'serialNumber', // 前端定义
    headerText: i18n.t('序号'),
    allowEditing: false
  },
  {
    field: 'factoryCode',
    headerText: i18n.t('工厂'),
    allowEditing: false
  },
  {
    field: 'outerMaterialGroupCode',
    headerText: i18n.t('物料组'),
    allowEditing: false
  },
  {
    field: 'plannerName',
    headerText: i18n.t('计划员'),
    allowEditing: false,
    valueAccessor: function (field, data) {
      return `${data.planner} - ${data.plannerName}`
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料'),
    allowEditing: false
  },
  {
    field: 'emailAddr',
    headerText: i18n.t('邮箱'),
    allowEditing: false
  },
  {
    field: 'grantDate',
    headerText: i18n.t('授予日期'),
    allowEditing: false
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    allowEditing: false
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    allowEditing: false
  },
  {
    field: 'characteristicCode',
    headerText: i18n.t('特性值'),
    allowEditing: false
  },
  {
    field: 'characteristicDesc',
    headerText: i18n.t('特性值描述'),
    allowEditing: false
  }
]

export const templatePageConfig = [
  {
    isUseCustomSearch: true,
    isCustomSearchRules: true,
    // isUseCustomEditor: true,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          // { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
          // { id: 'Save', icon: 'icon_solid_Save', title: i18n.t('保存') },
          // { id: 'Submit', icon: 'icon_solid_Submit', title: i18n.t('提交') },
          // { id: 'Print', icon: 'icon_list_print', title: i18n.t('打印') }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    useToolTemplate: false,
    grid: {
      // editSettings: {
      //   allowEditing: true,
      //   allowAdding: true,
      //   allowDeleting: true,
      //   mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
      //   showConfirmDialog: false,
      //   showDeleteConfirmDialog: false,
      //   newRowPosition: 'Top'
      // },
      dataSource: [],
      columnData: columnData,
      frozenColumns: 1,
      asyncConfig: {
        url: '/srm-purchase-execute/internal/planner/v1/distribution/query',
        serializeList: (list) => {
          if (list.length > 0) {
            let serialNumber = 1
            list.forEach((item) => {
              // 添加序号
              item.serialNumber = serialNumber++
              // if (item.startDate) {
              //   item.startDate = item.startDate.split(' ')[0]
              // }
              // if (item.endDate) {
              //   item.endDate = item.endDate.split(' ')[0]
              // }
            })
          }
          return list
        }
      }
    }
  }
]
