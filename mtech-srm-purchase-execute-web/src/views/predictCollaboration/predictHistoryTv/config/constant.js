import { i18n } from '@/main.js'
import Vue from 'vue'
export const ConstDynamicTitleStr = 'title_' // 动态表格数据的 key 标识

// 业务组类型数字字典编码
export const BusinessGroupTypeDictCode = 'BUSINESSGROUP'

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 预测数据类型cell
export const ForecastDataTypeCell = [
  // {
  //   title: i18n.t("D1(预测)"),
  // },
  // {
  //   title: i18n.t("D2(订单)"),
  // },
  {
    title: i18n.t('D(原始需求)')
  },
  {
    title: i18n.t('P(需求量)')
  },
  {
    title: i18n.t('C(承诺量)')
  },
  {
    title: i18n.t('Gap(差异)')
  },
  {
    title: 'F'
  }
]

// 类型字段，快捷搜索，下拉选项
export const ForecastTypeDataSource = [
  // ForecastDataTypeCell[0]?.title, // D1(预测)
  // ForecastDataTypeCell[1]?.title, // D2(订单)
  ForecastDataTypeCell[0]?.title, // D(原始需求)
  ForecastDataTypeCell[1]?.title, // P(需求量)
  ForecastDataTypeCell[2]?.title, // C(承诺量)
  ForecastDataTypeCell[3]?.title, // Gap(差异)
  ForecastDataTypeCell[4]?.title // F
]

// 表格编辑 的 RequestType
export const RequestType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  sorting: 'sorting',
  refresh: 'refresh'
}

// 表格编辑 的 action
export const ActionType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  edit: 'edit'
}

// 行编辑触发的 change 事件类型
export const ComponentChangeType = {
  code: 'code', // 例：下拉框选择“供应商”，供应商编码（code)要被修改
  link: 'link' // 例：计划组：根据物料+工厂+计划组的业务组类型 code 带出
}

// 预测类型对应后端在表头增加的标志
export const ForecastType = {
  day: 'D',
  week: 'W',
  month: 'M'
}

// 动态行初始化数据
export const DynamicItemInit = {
  timeInfo: '', // 预测时间
  forecastNum: '', // D1预测
  orderNum: '', // D2订单
  total: '', // D
  buyerNum: '', // P
  supplierNum: '', // C
  planGroupNum: '' // F
}

// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  status: 0, // 状态 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
  itemName: '', // 物料名称
  itemCode: '', // 物料编码
  itemId: '', // 物料id
  factoryId: '', // 工厂id
  factoryName: '', // 工厂name
  factoryCode: '', // 工厂代码
  planGroupId: '', // 计划组id
  planGroup: '', // 计划组name
  planGroupCode: '', // 计划组代码
  buyerOrgId: '', // 采购组id
  buyerOrgName: '', // 采购组name
  buyerOrgCode: '', // 采购组代码
  productRel: '', // 机型/机芯
  companyId: '', // 公司id
  companyName: '', // 公司name
  companyCode: '', // 公司代码
  mrpArea: '', // 计划区域
  percentage: '', // 配额
  supplierName: '', // 供应商名称
  supplierCode: '', // 供应商编码
  supplierId: '', // 供应商id
  manufacturer: '', // 制造商名称
  rawMaterialManufacturer: '', // 关键原材厂商
  rawMaterialOrigin: '', // 关键原材产地
  packagingManufacturer: '', // 包装厂商
  packagingOrigin: '', // 包装产地
  specialUse: '', // 制造商专用状况
  undeliveredOrderQty: '', // 未交PO
  storageQty: '', // 已入库数量
  purchaserRemark: '', // 采方备注
  total1: '',
  total2: '',
  total3: '',
  total4: '',
  total5: '',
  total6: '',
  total7: '',
  monthTotalNum: '',
  weekData: '',
  noQuotasFlag: '',
  purchaseAheadTime: ''
}

// toolbar 按钮配置
export const Toolbar = [
  {
    id: 'ForecastAdd',
    prefixIcon: 'mt-icons mt-icon-icon_solid_Createproject',
    text: i18n.t('新增'),
    tooltipText: i18n.t('新增'),
    icon: 'icon_solid_Createproject',
    // permission: ['O_02_0382'],
    title: i18n.t('新增')
  },
  {
    id: 'ForecastDelete',
    prefixIcon: 'mt-icons mt-icon-icon_table_delete',
    text: i18n.t('删除'),
    tooltipText: i18n.t('删除'),
    icon: 'icon_table_delete',
    // permission: ['O_02_0383'],
    title: i18n.t('删除')
  },
  {
    id: 'ForecastUpdate',
    prefixIcon: 'mt-icons mt-icon-icon_table_save',
    text: i18n.t('更新'),
    tooltipText: i18n.t('更新'),
    icon: 'icon_table_save',
    // permission: ['O_02_0382'],
    title: i18n.t('更新')
  },
  {
    id: 'ForecastPublish',
    prefixIcon: 'mt-icons mt-icon-icon_solid_Release',
    text: i18n.t('发布'),
    tooltipText: i18n.t('发布'),
    icon: 'icon_solid_Release',
    // permission: ['O_02_0384'],
    title: i18n.t('发布')
  },
  {
    id: 'ForecastConfirm',
    prefixIcon: 'mt-icons mt-icon-icon_table_batchacceptance',
    text: i18n.t('确认'),
    tooltipText: i18n.t('确认'),
    icon: 'icon_table_batchacceptance',
    // permission: ['O_02_0385'],
    title: i18n.t('确认')
  },
  {
    id: 'ForecastImport',
    prefixIcon: 'mt-icons mt-icon-icon_solid_Import',
    text: i18n.t('导入'),
    tooltipText: i18n.t('导入'),
    icon: 'icon_solid_Import',
    // permission: ['O_02_0386'],
    title: i18n.t('导入')
  },
  {
    id: 'ForecastExport',
    prefixIcon: 'mt-icons mt-icon-icon_solid_export',
    text: i18n.t('导出'),
    tooltipText: i18n.t('导出'),
    icon: 'icon_solid_export',
    // permission: ['O_02_0387'],
    title: i18n.t('导出')
  }
]
// toolbar空调 按钮配置
export const ToolbarTv = [
  {
    id: 'ForecastAdd',
    prefixIcon: 'mt-icons mt-icon-icon_solid_Createproject',
    text: i18n.t('新增'),
    tooltipText: i18n.t('新增'),
    icon: 'icon_solid_Createproject',
    // permission: ["O_02_1304"],
    title: i18n.t('新增')
  },
  {
    id: 'ForecastDelete',
    prefixIcon: 'mt-icons mt-icon-icon_table_delete',
    text: i18n.t('删除'),
    tooltipText: i18n.t('删除'),
    icon: 'icon_table_delete',
    // permission: ["O_02_1305"],
    title: i18n.t('删除')
  },
  {
    id: 'CloseEdit',
    text: i18n.t('取消编辑'),
    tooltipText: i18n.t('取消编辑'),
    icon: 'icon_table_delete',
    // permission: ["O_02_1305"],
    title: i18n.t('取消编辑')
  },
  // {
  //   id: "ForecastUpdate",
  //   prefixIcon: "mt-icons mt-icon-icon_table_save",
  //   text: i18n.t("更新"),
  //   tooltipText: i18n.t("更新"),
  //   icon: "icon_table_save",
  //   permission: ["O_02_1304"],
  //   title: i18n.t("更新"),
  // },
  {
    id: 'ForecastPublish',
    prefixIcon: 'mt-icons mt-icon-icon_solid_Release',
    text: i18n.t('发布'),
    tooltipText: i18n.t('发布'),
    icon: 'icon_solid_Release',
    // permission: ["O_02_1301"],
    title: i18n.t('发布')
  },
  // {
  //   id: "ForecastCancelPublish",
  //   icon: "", // icon_solid_Delete
  //   // permission: ["O_02_0695"],
  //   title: i18n.t("取消发布"),
  // },
  {
    id: 'ForecastConfirm',
    prefixIcon: 'mt-icons mt-icon-icon_table_batchacceptance',
    text: i18n.t('确认'),
    tooltipText: i18n.t('确认'),
    icon: 'icon_table_batchacceptance',
    // permission: ["O_02_1303"],
    title: i18n.t('确认')
  },
  {
    id: 'ForecastImport',
    prefixIcon: 'mt-icons mt-icon-icon_solid_Import',
    text: i18n.t('导入'),
    tooltipText: i18n.t('导入'),
    icon: 'icon_solid_Import',
    // permission: ["O_02_1307"],
    title: i18n.t('导入')
  },
  {
    id: 'ForecastExport',
    prefixIcon: 'mt-icons mt-icon-icon_solid_export',
    text: i18n.t('导出'),
    tooltipText: i18n.t('导出'),
    icon: 'icon_solid_export',
    // permission: ["O_02_1306"],
    title: i18n.t('导出')
  }
]
// 弹框表格行按钮
export const DialogCellTools = [
  {
    id: 'ForecastDelete',
    icon: '', // icon_solid_Delete
    title: i18n.t('删除'),
    visibleCondition: (data) => {
      return data.status === Status.new // 状态：新建
    }
  }
]

// 表格行按钮
export const CellTools = [
  {
    id: 'ForecastPublish',
    icon: '', // icon_solid_Release
    // permission: ['O_02_0384'],
    title: i18n.t('发布'),
    visibleCondition: (data) => {
      return data.status === Status.new || data.status === Status.modified // 状态：新建 || 已修改
    }
  },
  // {
  //   id: 'ForecastCancelPublish',
  //   icon: '', // icon_solid_Delete
  //   permission: ['O_02_0695'],
  //   title: i18n.t('取消发布'),
  //   visibleCondition: (data) => {
  //     return data.status === Status.pendingFeedback // 状态：待反馈
  //   }
  // },
  {
    id: 'ForecastDelete',
    icon: '', // icon_solid_Delete
    // permission: ['O_02_0383'],
    title: i18n.t('删除'),
    visibleCondition: (data) => {
      return data.status === Status.new || data.status === Status.modified // 状态：新建 || 已修改
    }
  },
  {
    id: 'ForecastConfirm',
    icon: '', // icon_solid_Delete
    // permission: ['O_02_0385'],
    title: i18n.t('确认'),
    visibleCondition: (data) => {
      return data.status === Status.feedbackAbnormal || data.status === Status.feedbackNormal // 状态：新建 || 已修改
    }
  }
]

// 预测表格/预测弹框表格 EditSettings
export const EditSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
} // 编辑设置

// 预测模板 2：短周期；1：中周期；0：长周期；
export const ForecastTemplate = {
  long: 0, // 长周期
  medium: 1, // 中周期
  short: 2 // 短周期
}
// 预测模板 text
export const ForecastTemplateText = {
  [ForecastTemplate.long]: i18n.t('长周期'),
  [ForecastTemplate.medium]: i18n.t('中周期'),
  [ForecastTemplate.short]: i18n.t('短周期')
}
// 预测模板 Options
export const ForecastTemplateOptions = [
  {
    text: ForecastTemplateText[ForecastTemplate.long],
    value: ForecastTemplate.long
  },
  {
    text: ForecastTemplateText[ForecastTemplate.medium],
    value: ForecastTemplate.medium
  },
  {
    text: ForecastTemplateText[ForecastTemplate.short],
    value: ForecastTemplate.short
  }
]

// 状态 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
export const Status = {
  new: 0, // 新建
  modified: 1, // 已修改
  pendingFeedback: 2, // 已发布 不可编辑
  feedbackNormal: 3, // 反馈满足
  feedbackAbnormal: 4, // 反馈不满足
  confirmed: 5 // 已确认 不可编辑
  // deleted: 6, // 已删除 不可编辑
}
// 状态 text
export const StatusText = {
  [Status.new]: i18n.t('新建'),
  [Status.modified]: i18n.t('已修改'),
  [Status.pendingFeedback]: i18n.t('已发布'),
  [Status.feedbackNormal]: i18n.t('反馈满足'),
  [Status.feedbackAbnormal]: i18n.t('反馈不满足'),
  [Status.confirmed]: i18n.t('已确认')
}
// 状态 class
export const StatusClass = {
  [Status.new]: 'col-active',
  [Status.modified]: 'col-active',
  [Status.pendingFeedback]: 'col-active', // col-published
  [Status.feedbackNormal]: 'col-active', // col-normal
  [Status.feedbackAbnormal]: 'col-active', // col-abnormal
  [Status.confirmed]: 'col-active' // col-inactive
}
export const StatusSearchOptions = [
  { value: Status.new, text: StatusText[Status.new] },
  { value: Status.modified, text: StatusText[Status.modified] },
  { value: Status.pendingFeedback, text: StatusText[Status.pendingFeedback] },
  { value: Status.feedbackNormal, text: StatusText[Status.feedbackNormal] },
  { value: Status.feedbackAbnormal, text: StatusText[Status.feedbackAbnormal] },
  { value: Status.confirmed, text: StatusText[Status.confirmed] }
]
export const StatusSearchSupOptions = [
  // { value: Status.new, text: StatusText[Status.new] },
  // { value: Status.modified, text: StatusText[Status.modified] },
  { value: Status.pendingFeedback, text: StatusText[Status.pendingFeedback] },
  { value: Status.feedbackNormal, text: StatusText[Status.feedbackNormal] },
  { value: Status.feedbackAbnormal, text: StatusText[Status.feedbackAbnormal] },
  { value: Status.confirmed, text: StatusText[Status.confirmed] }
]

// 预测表格列数据
export const ForecastColumnData = [
  {
    fieldCode: 'status', // 自动生成，不可编辑
    fieldName: i18n.t('状态'), // 包含0新建、1已修改、2已发布、3反馈满足，4反馈不满足、5已确认，6已删除。
    // sticky: true,
    allowResizing: false, //是否允许调整大小 列拖拽
    allowReordering: false
  },

  {
    fieldCode: 'planner', // 根据物料+工厂+计划组的业务组类型code带出，不可编辑，必须
    fieldName: i18n.t('计划员'),
    ignore: true,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<p style='white-space: normal'>{{data.plannerName}}</p>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
    // planGroup 计划组name
    // planGroupId 计划组id
  },
  {
    fieldCode: 'factoryCode', // 物料带出对应的主数据中的工厂，新增状态时可编辑，必须
    fieldName: i18n.t('工厂'),
    customAttributes: {
      class: 'sticky-col-0' // sticky-col-0不是固定写法，自定义命名即可
    },
    // sticky: true,
    allowReordering: false,
    allowResizing: false //是否允许调整大小 列拖拽
    // factoryName
    // factoryId
  },
  {
    fieldCode: 'mrpArea', // 预测报表行的计划区域，新增状态时可编辑，文本框
    fieldName: i18n.t('MRP区域'),
    // sticky: true,
    allowReordering: false,
    allowResizing: false, //是否允许调整大小 列拖拽
    ignore: true
  },
  {
    fieldCode: 'itemCode', // 主数据中的物料，新增状态时可编辑，必须
    fieldName: i18n.t('物料编码'),
    // sticky: true,
    allowResizing: false, //是否允许调整大小 列拖拽
    allowReordering: false,
    customAttributes: {
      class: 'sticky-col-1' // sticky-col-0不是固定写法，自定义命名即可
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<p style='white-space: normal'>{{data.itemCode}}</p>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
    // itemName
    // itemId
  },
  {
    fieldCode: 'itemName', // 主数据中的物料，新增状态时可编辑，必须
    fieldName: i18n.t('物料名称'),
    // sticky: true,
    allowResizing: false, //是否允许调整大小 列拖拽
    allowReordering: false,
    customAttributes: {
      class: 'sticky-col-2' // sticky-col-0不是固定写法，自定义命名即可
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<p style='white-space: normal'>{{data.itemName}}</p>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
    // itemName
    // itemId
  },
  {
    fieldCode: 'supplierCode', // 供应商：主数据中的供应商，新增状态时可编辑，下拉框模糊搜索，模糊搜索 必须
    fieldName: i18n.t('供应商'),
    // sticky: true,
    allowResizing: false, //是否允许调整大小 列拖拽
    allowReordering: false,
    customAttributes: {
      class: 'sticky-col-3' // sticky-col-0不是固定写法，自定义命名即可
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<p style='white-space: normal'>{{data.supplierCode}}{{data.supplierName}}</p>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
    // supplierName
    // supplierId
  },
  {
    fieldCode: 'purchaseAdvanceDate', // 预测数据对应供应商的配额，新增状态时可编辑，文本框
    fieldName: i18n.t('采购提前期'),
    ignore: true
  },
  // {
  //   fieldCode: 'serialNumber', // 前端定义,不可编辑
  //   fieldName: i18n.t('序号')
  // },
  {
    fieldCode: 'supplierInv',
    fieldName: i18n.t('供方库存'),
    ignore: true
  },
  {
    fieldCode: 'supplierRemark', // 不可编辑
    fieldName: i18n.t('供应商备注'),
    ignore: true,
    allowEditing: false
  },
  // {
  //   fieldCode: 'quota', // 预测数据对应供应商的配额，新增状态时可编辑，文本框
  //   fieldName: i18n.t('配额'),
  //   ignore: true
  // },
  // {
  //   fieldCode: 'quotaFlag', // 预测数据对应供应商的配额，新增状态时可编辑，文本框
  //   fieldName: i18n.t('无配额标识'),
  //   ignore: true
  // },
  {
    fieldCode: 'machineModel', // 物料所属的机型/机芯，新增状态时可编辑，文本框
    fieldName: i18n.t('机型/机芯'),
    ignore: true
  },
  // {
  //   fieldCode: 'companyCode', // 通过登录用户信息获取当前租户的公司列表，新增状态时可编辑，下拉框
  //   fieldName: i18n.t('公司')
  //   // companyId
  //   // companyName
  // },
  {
    fieldCode: 'purchaserRemark', // 计划组可填写备注，新增状态时可编辑，文本框
    fieldName: i18n.t('采方备注'),
    ignore: true
  },
  {
    fieldCode: 'manufacturer', // 新增状态时可编辑，文本框
    fieldName: i18n.t('生产厂商名称'),
    ignore: true
  },
  {
    fieldCode: 'rawMaterialManufacturer', // 新增状态时可编辑，文本框
    fieldName: i18n.t('关键原材厂商'),
    ignore: true
  },
  {
    fieldCode: 'keyRawMaterialOrigin', // 新增状态时可编辑，文本框
    fieldName: i18n.t('关键原材产地'),
    ignore: true
  },
  {
    fieldCode: 'packageManufacturer', // 新增状态时可编辑，文本框
    fieldName: i18n.t('包装厂商'),
    ignore: true
  },
  {
    fieldCode: 'packageProdtPlace', // 新增状态时可编辑，文本框
    fieldName: i18n.t('包装产地'),
    ignore: true
  },
  {
    fieldCode: 'manufacturerDedicatedStatus', // 新增状态时可编辑，文本框
    fieldName: i18n.t('制造商专用状况'),
    ignore: true
  },
  {
    width: 150,
    fieldCode: 'syncVersion', // 自动生成,不可编辑 大版本， publishVersion 小版本
    fieldName: i18n.t('版本')
  },
  {
    fieldCode: 'unpaidPoQty', // 新增状态时可编辑，数字
    fieldName: i18n.t('未交PO'),
    ignore: true
  },
  {
    fieldCode: 'structSeqNo', // 不可编辑
    fieldName: i18n.t('结构序号'),
    ignore: true,
    allowEditing: false
  },
  {
    fieldCode: 'forecastType', // 前端写死 D1(预测); D2(订单); D;C;P;F;
    fieldName: i18n.t('类型')
  },
  // {
  //   fieldCode: 'storageQty', // 新增状态时可编辑，数字
  //   fieldName: i18n.t('已入库数量'),
  //   ignore: true
  // },
  // {
  //   fieldCode: 'remainFeedbackTime', // 按照配置后，发布给供应商后显示，不可编辑
  //   fieldName: i18n.t('剩余反馈时间（H)'),
  //   ignore: true
  // },
  {
    fieldCode: 'thePrimaryKey'
  }
  // {
  //   fieldCode: "status", // 自动生成，不可编辑
  //   fieldName: i18n.t("状态"), // 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
  // },
  // {
  //   fieldCode: "buyerOrgCode", // 计划组 code-name显示 根据物料选择带出数据源 不可模糊搜索（API不支持），必须
  //   fieldName: i18n.t("采购组"),
  //   // ignore: true,
  //   // buyerOrgId 采购组id
  //   // buyerOrgName 采购组name
  // },

  // {
  //   fieldCode: "mrpArea", // 预测报表行的计划区域，新增状态时可编辑，文本框
  //   fieldName: i18n.t("计划区域"),
  //   ignore: true,
  // },
  // {
  //   fieldCode: "timeoutDemand", // 不可编辑
  //   fieldName: i18n.t("过期需求"),
  //   ignore: true,
  // },
  // {
  //   fieldCode: 'total1', // 前端写死 D1(预测); D2(订单); D;C;P;F;
  //   fieldName: i18n.t('N+1月汇总'),
  //   ignore: true
  // },
  // {
  //   fieldCode: 'total2', // 前端写死 D1(预测); D2(订单); D;C;P;F;
  //   fieldName: i18n.t('N+2月汇总'),
  //   ignore: true
  // },
  // {
  //   fieldCode: 'total3', // 前端写死 D1(预测); D2(订单); D;C;P;F;
  //   fieldName: i18n.t('N+3月汇总'),
  //   ignore: true
  // },
  // {
  //   fieldCode: 'total4', // 前端写死 D1(预测); D2(订单); D;C;P;F;
  //   fieldName: i18n.t('N+4月汇总'),
  //   ignore: true
  // },
  // {
  //   fieldCode: 'total5', // 前端写死 D1(预测); D2(订单); D;C;P;F;
  //   fieldName: i18n.t('N+5月汇总'),
  //   ignore: true
  // },
  // {
  //   fieldCode: 'total6', // 前端写死 D1(预测); D2(订单); D;C;P;F;
  //   fieldName: i18n.t('N+6月汇总'),
  //   ignore: true
  // },
  // {
  //   fieldCode: 'total7', // 前端写死 D1(预测); D2(订单); D;C;P;F;
  //   fieldName: i18n.t('N+7月汇总'),
  //   ignore: true
  // },
  // {
  //   fieldCode: 'total8', // 前端写死 D1(预测); D2(订单); D;C;P;F;
  //   fieldName: i18n.t('N+8月汇总'),
  //   ignore: true
  // },
  // {
  //   fieldCode: "monthTotalNum", // 前端写死 D1(预测); D2(订单); D;C;P;F;
  //   fieldName: i18n.t("月汇总"),
  //   ignore: true,
  // },
  // {
  //   fieldCode: "weekData", // 前端写死 D1(预测); D2(订单); D;C;P;F;
  //   fieldName: i18n.t("周预测"),
  //   ignore: true,
  // },
]

// 预测历史表格列数据
export const ForecastHistoryColumnData = [
  // {
  //   fieldCode: 'checkBox' // 不可编辑
  // },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  },
  {
    fieldCode: 'status', // 自动生成，不可编辑
    fieldName: i18n.t('状态'), // 包含0新建、1已修改、2已发布、3反馈满足，4反馈不满足、5已确认，6已删除。
    // sticky: true,
    allowResizing: false, //是否允许调整大小 列拖拽
    allowReordering: false
  },

  {
    fieldCode: 'planner', // 根据物料+工厂+计划组的业务组类型code带出，不可编辑，必须
    fieldName: i18n.t('计划员'),
    ignore: true,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<p style='white-space: normal'>{{data.plannerName}}</p>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
    // planGroup 计划组name
    // planGroupId 计划组id
  },
  {
    fieldCode: 'factoryCode', // 物料带出对应的主数据中的工厂，新增状态时可编辑，必须
    fieldName: i18n.t('工厂'),
    // sticky: true,
    allowReordering: false,
    allowResizing: false //是否允许调整大小 列拖拽
    // factoryName
    // factoryId
  },
  {
    fieldCode: 'mrpArea', // 预测报表行的计划区域，新增状态时可编辑，文本框
    fieldName: i18n.t('MRP区域'),
    // sticky: true,
    allowReordering: false,
    allowResizing: false, //是否允许调整大小 列拖拽
    ignore: true
  },
  {
    fieldCode: 'itemCode', // 主数据中的物料，新增状态时可编辑，必须
    fieldName: i18n.t('物料编码'),
    // sticky: true,
    allowResizing: false, //是否允许调整大小 列拖拽
    allowReordering: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<p style='white-space: normal'>{{data.itemCode}}</p>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
    // itemName
    // itemId
  },
  {
    fieldCode: 'itemName', // 主数据中的物料，新增状态时可编辑，必须
    fieldName: i18n.t('物料名称'),
    // sticky: true,
    allowResizing: false, //是否允许调整大小 列拖拽
    allowReordering: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<p style='white-space: normal'>{{data.itemName}}</p>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
    // itemName
    // itemId
  },
  {
    fieldCode: 'supplierCode', // 供应商：主数据中的供应商，新增状态时可编辑，下拉框模糊搜索，模糊搜索 必须
    fieldName: i18n.t('供应商'),
    // sticky: true,
    allowResizing: false, //是否允许调整大小 列拖拽
    allowReordering: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<p style='white-space: normal'>{{data.supplierCode}}{{data.supplierName}}</p>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
    // supplierName
    // supplierId
  },
  {
    fieldCode: 'purchaseAdvanceDate', // 预测数据对应供应商的配额，新增状态时可编辑，文本框
    fieldName: i18n.t('采购提前期'),
    ignore: true
  },
  // {
  //   fieldCode: 'serialNumber', // 前端定义,不可编辑
  //   fieldName: i18n.t('序号')
  // },
  {
    fieldCode: 'supplierInv',
    fieldName: i18n.t('供方库存'),
    ignore: true
  },
  {
    fieldCode: 'supplierRemark', // 不可编辑
    fieldName: i18n.t('供应商备注'),
    ignore: true,
    allowEditing: false
  },
  // {
  //   fieldCode: 'quota', // 预测数据对应供应商的配额，新增状态时可编辑，文本框
  //   fieldName: i18n.t('配额'),
  //   ignore: true
  // },
  // {
  //   fieldCode: 'quotaFlag', // 预测数据对应供应商的配额，新增状态时可编辑，文本框
  //   fieldName: i18n.t('无配额标识'),
  //   ignore: true
  // },
  {
    fieldCode: 'machineModel', // 物料所属的机型/机芯，新增状态时可编辑，文本框
    fieldName: i18n.t('机型/机芯'),
    ignore: true
  },
  // {
  //   fieldCode: 'companyCode', // 通过登录用户信息获取当前租户的公司列表，新增状态时可编辑，下拉框
  //   fieldName: i18n.t('公司')
  //   // companyId
  //   // companyName
  // },
  {
    fieldCode: 'purchaserRemark', // 计划组可填写备注，新增状态时可编辑，文本框
    fieldName: i18n.t('采方备注'),
    ignore: true
  },
  {
    fieldCode: 'manufacturer', // 新增状态时可编辑，文本框
    fieldName: i18n.t('生产厂商名称'),
    ignore: true
  },
  {
    fieldCode: 'rawMaterialManufacturer', // 新增状态时可编辑，文本框
    fieldName: i18n.t('关键原材厂商'),
    ignore: true
  },
  {
    fieldCode: 'keyRawMaterialOrigin', // 新增状态时可编辑，文本框
    fieldName: i18n.t('关键原材产地'),
    ignore: true
  },
  {
    fieldCode: 'packageManufacturer', // 新增状态时可编辑，文本框
    fieldName: i18n.t('包装厂商'),
    ignore: true
  },
  {
    fieldCode: 'packageProdtPlace', // 新增状态时可编辑，文本框
    fieldName: i18n.t('包装产地'),
    ignore: true
  },
  {
    fieldCode: 'manufacturerDedicatedStatus', // 新增状态时可编辑，文本框
    fieldName: i18n.t('制造商专用状况'),
    ignore: true
  },
  {
    fieldCode: 'unpaidPoQty', // 新增状态时可编辑，数字
    fieldName: i18n.t('未交PO'),
    ignore: true
  }
  // {
  //   width: 150,
  //   fieldCode: 'syncVersion', // 自动生成,不可编辑 大版本， publishVersion 小版本
  //   fieldName: i18n.t('版本')
  // },
  // {
  //   fieldCode: 'forecastType', // 前端写死 D1(预测); D2(订单); D;C;P;F;
  //   fieldName: i18n.t('类型')
  // }
]

// 预测弹框表格列数据
export const ForecastDialogColumnData = [
  // {
  //   fieldCode: "isError",
  //   fieldName: i18n.t("是否异常"),
  // },
  {
    fieldCode: 'errorContent',
    fieldName: i18n.t('错误信息')
  }
]

// 预测弹框表格列不显示的列数据
export const ForecastDialogColumnDataDoNotShow = [
  'syncVersion', // 版本
  'remainFeedbackTime', // 剩余反馈时间
  'supRemark' // 供应商备注
]

// 选择 物料 弹框 表格列数据
export const MaterielTableColumnData = [
  {
    fieldCode: 'itemCode', // 物料编号
    fieldName: i18n.t('物料编号')
  },
  {
    fieldCode: 'itemName', // 物料名称
    fieldName: i18n.t('物料名称')
  },
  {
    fieldCode: 'categoryResponse.categoryCode', // 品类
    fieldName: i18n.t('品类')
  },
  {
    fieldCode: 'itemDescription', // 规格型号
    fieldName: i18n.t('规格型号')
  },
  {
    fieldCode: 'oldItemCode', // 旧物料编号
    fieldName: i18n.t('旧物料编号')
  },
  {
    fieldCode: 'manufacturerName', // 制造商
    fieldName: i18n.t('制造商')
  }
]
