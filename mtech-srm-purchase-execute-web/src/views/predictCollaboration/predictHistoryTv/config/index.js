import { dynamicTitle, dynamicTitleDialog, forecastTypeShow } from './variable'
import {
  ConstDynamicTitleStr,
  ForecastType,
  ComponentType,
  CellTools,
  DialogCellTools,
  Status,
  StatusSearchOptions,
  ForecastTypeDataSource
} from './constant'
import Component from './columnComponent'
import { codeNameColumn } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'

// 格式化表格列数据
export const formatTableColumnData = (data, type) => {
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      width: '150',
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName
    }
    if (col.fieldCode === 'checkBox') {
      // checkBox
      defaultCol.type = 'checkbox'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.showInColumnChooser = false
      defaultCol.width = '50'
      // 编辑时显示
      defaultCol.editTemplate = Component.empty
    } else if (col.fieldCode === 'serialNumber') {
      // 序号
      defaultCol.width = '75'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.ignore = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'status') {
      // 状态
      defaultCol.width = '70'
      defaultCol.allowResizing = false
      defaultCol.allowEditing = false
      // defaultCol.sticky = true
      defaultCol.allowResizing = false
      defaultCol.allowReordering = false

      defaultCol.template = Component.status({
        type: ComponentType.view,
        cellTools: CellTools
      })
      defaultCol.editTemplate = Component.status({
        type: ComponentType.edit
      })
      defaultCol.searchOptions = {
        elementType: 'select',
        dataSource: StatusSearchOptions,
        fields: { text: 'text', value: 'value' }
      }
    } else if (col.fieldCode === 'syncVersion') {
      // 大版本， 版本 = 大版本(syncVersion).小版本(publishVersion)
      defaultCol.width = '150'
      defaultCol.allowEditing = false
      // defaultCol.ignore = true;
      if (type !== 'withoutVersionLink') {
        defaultCol.template = Component.versionText
        defaultCol.editTemplate = Component.versionText
      }
    } else if (col.fieldCode === 'forecastType') {
      // 类型
      defaultCol.allowEditing = false
      defaultCol.template = Component.type({
        forecastTypeShow
      })
      // defaultCol.sticky = true
      defaultCol.editTemplate = Component.type({
        forecastTypeShow
      })
      // 快捷搜索，下拉选项
      defaultCol.searchOptions = {
        elementType: 'multi-select',
        operator: 'in',
        dataSource: ForecastTypeDataSource
      }
    } else if (col.isForecast) {
      // 预测数据
      defaultCol.template = Component.forecast({
        forecastTypeShow,
        colIndex: col.colIndex,
        type: ComponentType.view,
        manage: sessionStorage.getItem('manage'),
        dynamicTitle: dynamicTitle
      })
      defaultCol.editTemplate = Component.forecast({
        forecastTypeShow,
        colIndex: col.colIndex,
        type: ComponentType.edit,
        manage: sessionStorage.getItem('manage'),

        dynamicTitle: dynamicTitle,
        maximumValue: 999999999999999.999,
        precision: 3
      })
    } else if (col.fieldCode === 'itemCode') {
      // 物料：主数据中的物料，新增状态时可编辑，弹框，必须
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      // defaultCol.template = codeNameColumn({
      //   firstKey: "itemCode",
      //   secondKey: "itemName",
      // });
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: false,
        maxlength: undefined,
        isListenChange: true,
        disabled: true,
        hasSearch: true,
        format: (data) => {
          let theColumnData = ''
          if (data['itemCode'] && data['itemName']) {
            // firstKey-secondKey
            theColumnData = `${data['itemCode']}-${data['itemName']}`
          } else if (data['itemCode']) {
            // firstKey
            theColumnData = data['itemCode']
          } else if (data['itemName']) {
            // secondKey
            theColumnData = data['itemName']
          }
          return theColumnData
        },
        disableConverter: (data) => {
          return data.status != Status.new
        }
      })
      defaultCol.width = '200'
      // 主数据选择器
      // defaultCol.searchOptions = {
      //   ...MasterDataSelect.material,
      // };
    } else if (col.fieldCode === 'itemName') {
      // 物料：主数据中的物料，新增状态时可编辑，弹框，必须

      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.editTemplate = Component.empty
      defaultCol.width = '100'
      // 主数据选择器
      // defaultCol.searchOptions = {
      //   ...MasterDataSelect.material,
      // };
    } else if (col.fieldCode === 'factoryCode') {
      // 工厂：code-name显示 根据物料选择带出数据源 主数据中的工厂，新增状态时可编辑，必须
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '130'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      // defaultCol.template = codeNameColumn({
      //   firstKey: 'factoryCode',
      //   secondKey: 'factoryName'
      // })
      defaultCol.editTemplate = Component.siteCodeSelect
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.factoryAddress
      }
    } else if (col.fieldCode === 'supplierCode') {
      // 供应商：code-name显示 主数据中的供应商，新增状态时可编辑，下拉框模糊搜索，模糊搜索，必须
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '170'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      // defaultCol.template = codeNameColumn({
      //   firstKey: "supplierCode",
      //   secondKey: "supplierName",
      // });
      defaultCol.editTemplate = Component.supplierCodeSelect
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.supplier
      }
    } else if (col.fieldCode === 'planGroupCode') {
      // 计划组：code-name显示 根据物料选择带出数据源 不可模糊搜索（API不支持）新增状态时可编辑，非必须
      defaultCol.width = '90'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'planGroupCode',
        secondKey: 'planGroup'
      })
      defaultCol.editTemplate = Component.planGroupCodeSelect
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessGroup
      }
    } else if (col.fieldCode === 'buyerOrgCode') {
      // 采购组：code-name显示 根据物料选择带出数据源 不可模糊搜索（API不支持）新增状态时可编辑，非必须
      defaultCol.width = '90'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'buyerOrgCode',
        secondKey: 'buyerOrgName'
      })
      defaultCol.editTemplate = Component.buyerOrgCodeSelect
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessGroup
      }
    } else if (col.fieldCode === 'planner') {
      // 计划组：code-name显示 根据物料选择带出数据源 不可模糊搜索（API不支持）新增状态时可编辑，必须
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = 100
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
    } else if (col.fieldCode === 'companyCode') {
      // 公司 code-name 不可编辑 工厂带出来
      defaultCol.allowFiltering = false
      defaultCol.width = '200'
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'companyCode',
        secondKey: 'companyName'
      })
      defaultCol.editTemplate = Component.companyCodeInputText
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessCompany
      }
    } else if (col.fieldCode === 'machineModel') {
      // 机型/机芯: 新增状态时可编辑，文本框
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
    } else if (col.fieldCode === 'purchaseAdvanceDate') {
      // 采购提前期
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        alwaysEdit: true
      })
    } else if (col.fieldCode === 'quotaFlag') {
      // 无配额
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        alwaysEdit: false,
        maxlength: 200
      })
    } else if (col.fieldCode === 'mrpArea') {
      // 计划区域: 新增状态时可编辑，文本框
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.width = '100'
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })

      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 50
      })
    } else if (col.fieldCode === 'quota') {
      // 配额: 新增状态时可编辑，文本框
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.width = '75'
      defaultCol.allowEditing = false
      // defaultCol.editTemplate = Component.number({
      //   dataKey: col.fieldCode,
      //   showClearBtn: true,
      //   min: 0,
      //   maximumValue: 999999999999999.999,
      //   precision: 3
      // })
    } else if (col.fieldCode === 'manufacturer') {
      // 制造商名称: 新增状态时可编辑，文本框
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      // defaultCol.editTemplate = Component.input({
      //   dataKey: col.fieldCode,
      //   showClearBtn: true,
      //   maxlength: 50
      // })
    } else if (col.fieldCode === 'rawMaterialManufacturer') {
      // 关键原材厂商: 新增状态时可编辑，文本框
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 50
      })
    } else if (col.fieldCode === 'keyRawMaterialOrigin') {
      // 关键原材产地: 新增状态时可编辑，文本框
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      // defaultCol.editTemplate = Component.input({
      //   dataKey: col.fieldCode,
      //   showClearBtn: true,
      //   maxlength: 50
      // })
    } else if (col.fieldCode === 'packageManufacturer') {
      // 包装厂商: 新增状态时可编辑，文本框
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 50
      })
    } else if (col.fieldCode === 'packageProdtPlace') {
      // 包装产地: 新增状态时可编辑，文本框
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 50
      })
    } else if (col.fieldCode === 'manufacturerDedicatedStatus') {
      // 制造商专用状况: 新增状态时可编辑，文本框
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 50
      })
    } else if (col.fieldCode === 'unpaidPoQty') {
      // 未交PO: 新增状态时可编辑，数字
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      defaultCol.width = '150'
      // defaultCol.editTemplate = Component.number({
      //   dataKey: col.fieldCode,
      //   showClearBtn: true,
      //   min: 0,
      //   maximumValue: 999999999999999.999,
      //   precision: 3
      // })
    } else if (col.fieldCode === 'timeoutDemand') {
      // 过期需求: 不可编辑
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.width = '100'
      defaultCol.editTemplate = Component.empty
    } else if (col.fieldCode === 'storageQty') {
      // 已入库数量: 新增状态时可编辑，数字
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      defaultCol.width = '110'
      defaultCol.editTemplate = Component.number({
        dataKey: col.fieldCode,
        showClearBtn: true,
        min: 0,
        maximumValue: 999999999999999.999,
        precision: 3
      })
    } else if (col.fieldCode === 'purchaserRemark') {
      // 采方备注: 新增状态时可编辑，input
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.width = '150'
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        alwaysEdit: true,
        maxlength: 200
      })
    } else if (col.fieldCode === 'supplierRemark') {
      // 供应商备注: 不可编辑
      defaultCol.width = '110'
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.empty
    } else if (col.fieldCode === 'supplierInv') {
      // 供应商库存: 不可编辑
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.empty
    } else if (
      col.fieldCode === 'total1' ||
      col.fieldCode === 'total3' ||
      col.fieldCode === 'total4' ||
      col.fieldCode === 'total5' ||
      col.fieldCode === 'total6' ||
      col.fieldCode === 'total7' ||
      col.fieldCode === 'total2' ||
      col.fieldCode === 'monthTotalNum' ||
      col.fieldCode === 'weekData'
    ) {
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        alwaysEdit: false,
        maxlength: 50
      })
    } else if (col.fieldCode === 'remainFeedbackTime') {
      // 剩余反馈时间（H）不可编辑
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.empty
    } else if (col.fieldCode === 'thePrimaryKey') {
      // 行主键
      defaultCol.visible = false
      defaultCol.isIdentity = true // 加上这个，新增一行的时候，主键不会变成可输入
      defaultCol.isPrimaryKey = true
      defaultCol.allowEditing = false
    }
    colData.push(defaultCol)
  })

  return colData
}

// 格式化弹框表格列数据
export const formatTableDialogColumnData = (data) => {
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150'
    }
    if (col.fieldCode === 'checkBox') {
      // checkBox
      defaultCol.type = 'checkbox'
      defaultCol.allowEditing = false
      defaultCol.showInColumnChooser = false
      defaultCol.width = '70'
      // 编辑时显示
      // defaultCol.editTemplate = Component.empty;
    } else if (col.fieldCode === 'serialNumber') {
      // 序号
      defaultCol.width = '70'
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'status') {
      // 状态
      defaultCol.width = '70'
      defaultCol.allowResizing = false
      // defaultCol.sticky = true
      defaultCol.allowResizing = false
      defaultCol.allowReordering = false
      defaultCol.allowEditing = false
      defaultCol.template = Component.status({
        type: ComponentType.view,
        cellTools: DialogCellTools
      })
      defaultCol.editTemplate = Component.status({
        type: ComponentType.edit
      })
    } else if (col.fieldCode === 'forecastType') {
      // 类型
      defaultCol.width = '95'
      // defaultCol.sticky = true
      defaultCol.allowEditing = false
      defaultCol.template = Component.type({ forecastTypeShow: [] })
      defaultCol.editTemplate = Component.type({ forecastTypeShow: [] })
    } else if (col.isForecast) {
      // 预测数据
      defaultCol.template = Component.forecast({
        colIndex: col.colIndex,
        manage: sessionStorage.getItem('manage'),

        type: ComponentType.view,
        dynamicTitle: dynamicTitleDialog
      })
      defaultCol.editTemplate = Component.forecast({
        colIndex: col.colIndex,
        type: ComponentType.mustEdit,
        dynamicTitle: dynamicTitleDialog,
        maximumValue: 999999999999999.999,
        manage: sessionStorage.getItem('manage'),

        minimumValue: -999999999999999.999,
        precision: 3
      })
    } else if (col.fieldCode === 'itemCode') {
      // 物料：主数据中的物料，新增状态时可编辑，弹框，必须
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'itemCode',
        secondKey: 'itemName'
      })
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: false,
        maxlength: undefined,
        isListenChange: true,
        disabled: true,
        hasSearch: true,
        format: (data) => {
          let theColumnData = ''
          if (data['itemCode'] && data['itemName']) {
            // firstKey-secondKey
            theColumnData = `${data['itemCode']}-${data['itemName']}`
          } else if (data['itemCode']) {
            // firstKey
            theColumnData = data['itemCode']
          } else if (data['itemName']) {
            // secondKey
            theColumnData = data['itemName']
          }
          return theColumnData
        },
        disableConverter: (data) => {
          return data.status != Status.new
        }
      })
      defaultCol.width = '200'
    } else if (col.fieldCode === 'factoryCode') {
      // 工厂：code-name显示 根据物料选择带出数据源 主数据中的工厂，新增状态时可编辑，必须
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '250'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      // defaultCol.template = codeNameColumn({
      //   firstKey: 'factoryCode',
      //   secondKey: 'factoryName'
      // })
      defaultCol.editTemplate = Component.siteCodeSelect
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.factoryAddress
      }
    } else if (col.fieldCode === 'supplierCode') {
      // 供应商：code-name显示 主数据中的供应商，新增状态时可编辑，下拉框模糊搜索，模糊搜索，必须
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '100'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      })
      defaultCol.editTemplate = Component.supplierCodeSelect
    } else if (col.fieldCode === 'planGroupCode') {
      // 计划组：code-name显示 根据物料选择带出数据源 不可模糊搜索（API不支持）新增状态时可编辑，必须
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '300'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'planGroupCode',
        secondKey: 'planGroup'
      })
      defaultCol.editTemplate = Component.planGroupCodeSelect
    } else if (col.fieldCode === 'planner') {
      // 计划组：code-name显示 根据物料选择带出数据源 不可模糊搜索（API不支持）新增状态时可编辑，必须
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '100'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      // defaultCol.template = codeNameColumn({
      //   firstKey: 'planGroupCode',
      //   secondKey: 'planGroup'
      // })
      // defaultCol.editTemplate = Component.planGroupCodeSelect
    } else if (col.fieldCode === 'buyerOrgCode') {
      // 采购组：code-name显示 根据物料选择带出数据源 不可模糊搜索（API不支持）新增状态时可编辑，必须
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '300'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'buyerOrgCode',
        secondKey: 'buyerOrgName'
      })
      defaultCol.editTemplate = Component.buyerOrgCodeSelect
    } else if (col.fieldCode === 'companyCode') {
      // 公司 code-name 不可编辑 工厂带出来
      defaultCol.allowFiltering = false
      defaultCol.width = '250'
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'companyCode',
        secondKey: 'companyName'
      })
      defaultCol.editTemplate = Component.companyCodeInputText
    } else if (col.fieldCode === 'purchaseAdvanceDate') {
      // 采购提前期
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        alwaysEdit: true,
        maxlength: 200
      })
    } else if (col.fieldCode === 'quotaFlag') {
      // 无配额标识
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        alwaysEdit: false,
        maxlength: 200
      })
    } else if (col.fieldCode === 'machineModel') {
      // 机型/机芯: 新增状态时可编辑，文本框
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      // defaultCol.editTemplate = Component.input({
      //   dataKey: col.fieldCode,
      //   showClearBtn: true,
      //   alwaysEdit: true,
      //   maxlength: 50
      // })
    } else if (col.fieldCode === 'mrpArea') {
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      // 计划区域: 新增状态时可编辑，文本框
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.width = '100'

      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        alwaysEdit: true,
        maxlength: 50
      })
    } else if (col.fieldCode === 'quota') {
      // 配额: 新增状态时可编辑，文本框
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      // defaultCol.editTemplate = Component.number({
      //   dataKey: col.fieldCode,
      //   showClearBtn: true,
      //   alwaysEdit: true,
      //   min: 0,
      //   maximumValue: 999999999999999.999,
      //   precision: 3
      // })
    } else if (col.fieldCode === 'manufacturer') {
      // 制造商名称: 新增状态时可编辑，文本框
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      // defaultCol.editTemplate = Component.input({
      //   dataKey: col.fieldCode,
      //   showClearBtn: true,
      //   alwaysEdit: true,
      //   maxlength: 50
      // })
    } else if (col.fieldCode === 'rawMaterialManufacturer') {
      // 关键原材厂商: 新增状态时可编辑，文本框
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        alwaysEdit: true,
        maxlength: 50
      })
    } else if (col.fieldCode === 'keyRawMaterialOrigin') {
      // 关键原材产地: 新增状态时可编辑，文本框
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      // defaultCol.editTemplate = Component.input({
      //   dataKey: col.fieldCode,
      //   showClearBtn: true,
      //   alwaysEdit: true
      // })
    } else if (col.fieldCode === 'packageManufacturer') {
      // 包装厂商: 新增状态时可编辑，文本框
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        alwaysEdit: true,
        maxlength: 50
      })
    } else if (col.fieldCode === 'packageProdtPlace') {
      // 包装产地: 新增状态时可编辑，文本框
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        alwaysEdit: true,
        maxlength: 50
      })
    } else if (col.fieldCode === 'manufacturerDedicatedStatus') {
      // 制造商专用状况: 新增状态时可编辑，文本框
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        alwaysEdit: true,
        maxlength: 50
      })
    } else if (col.fieldCode === 'unpaidPoQty') {
      // 未交PO: 新增状态时可编辑，数字
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      defaultCol.width = '150'
      // defaultCol.editTemplate = Component.number({
      //   dataKey: col.fieldCode,
      //   showClearBtn: true,
      //   alwaysEdit: true,
      //   min: 0,
      //   maximumValue: 999999999999999.999,
      //   precision: 3
      // })
    } else if (col.fieldCode === 'storageQty') {
      // 已入库数量: 新增状态时可编辑，数字
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.allowEditing = false
      defaultCol.editTemplate = Component.number({
        dataKey: col.fieldCode,
        showClearBtn: true,
        alwaysEdit: true,
        min: 0,
        maximumValue: 999999999999999.999,
        precision: 3
      })
    } else if (col.fieldCode === 'purchaserRemark') {
      // 采方备注: 新增状态时可编辑，input
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.width = '150'
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        alwaysEdit: true,
        maxlength: 200
      })
    } else if (col.fieldCode === 'supplierRemark') {
      // 供应商备注: 不可编辑
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.empty
    } else if (col.fieldCode === 'supplierInv') {
      // 供应商库存: 不可编辑
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.empty
    } else if (
      col.fieldCode === 'total1' ||
      col.fieldCode === 'total3' ||
      col.fieldCode === 'total4' ||
      col.fieldCode === 'total5' ||
      col.fieldCode === 'total6' ||
      col.fieldCode === 'total7' ||
      col.fieldCode === 'total2' ||
      col.fieldCode === 'monthTotalNum' ||
      col.fieldCode === 'weekData'
    ) {
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        alwaysEdit: true,
        maxlength: 50
      })
    } else if (col.fieldCode === 'errorContent') {
      // 错误信息: 不可编辑
      defaultCol.template = Component.errorText({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.errorText({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'thePrimaryKey') {
      // 行主键
      defaultCol.visible = false
      defaultCol.isIdentity = true // 加上这个，新增一行的时候，主键不会变成可输入
      defaultCol.isPrimaryKey = true
      defaultCol.allowEditing = false
    }
    colData.push(defaultCol)
  })

  return colData
}
// 格式化预测信息
// const formatForecastInfo = (data) => {
//   const { list, type } = data;
//   const forecastList = list || [];
//   forecastList.forEach((item) => {
//     const time = item.timeInfo || "";
//     if (type === ForecastType.day) {
//       // 天预测
//       item.timeInfo = `${ForecastType.day}${time}`;
//     } else if (type === ForecastType.week) {
//       // 周预测
//       item.timeInfo = `${ForecastType.week}${time}`;
//     } else if (type === ForecastType.month) {
//       // 月预测
//       item.timeInfo = `${ForecastType.month}${time}`;
//     }
//   });
//   return forecastList;
// };

// 格式化预测表格
export const formatTableDataSource = (data) => {
  const { records, titleList } = data
  records.forEach((recordsItem, index) => {
    recordsItem.serialNumber = index + 1 // 添加序号
    recordsItem.thePrimaryKey = recordsItem.tvForecastIntegration.id // 主键的值为 API 数据的 id
    recordsItem.status = recordsItem.tvForecastIntegration.status ?? Status.new // 状态为 undefined/null 时，默认为新建状态（导入时）
    if (recordsItem.syncVersion && recordsItem.syncVersion.length) {
      // rowData.syncVersion = rowData.syncVersion.substring(0, 8)
      recordsItem.syncVersion =
        recordsItem.syncVersion.slice(0, 8) + recordsItem.syncVersion.slice(14)
    }
    // 天预测
    // const dayData = recordsItem?.forecastInfo?.dayData || []
    // const dayData = formatForecastInfo({
    //   list: recordsItem?.forecastInfo?.dayData,
    //   type: ForecastType.day,
    // });
    // 周预测
    // const weekData = recordsItem?.forecastInfo?.weekData || []
    // const weekData = formatForecastInfo({
    //   list: recordsItem?.forecastInfo?.weekData,
    //   type: ForecastType.week,
    // });
    // 月预测
    // const monthData = recordsItem?.forecastInfo?.monthData || []
    // const monthData = formatForecastInfo({
    //   list: recordsItem?.forecastInfo?.monthData,
    //   type: ForecastType.month,
    // });

    // 合并所有类型的预测数据
    // const forecastList = [].concat(dayData).concat(weekData).concat(monthData)
    const crow = recordsItem?.crow
    const drow = recordsItem?.drow
    const frow = recordsItem?.frow
    const prow = recordsItem?.prow
    const gap = recordsItem?.gap
    titleList.forEach((itemTitle, index) => {
      crow.forEach(() => {
        // 将预测数据赋给表头对应的对象
        const forecastItem = {
          buyerNum: prow && prow[index] ? prow[index] : null,
          gapNum: gap && gap[index] ? gap[index] : null,
          total: drow && drow[index] ? drow[index] : null,
          planGroupNum: frow && frow[index] ? frow[index] : null,
          supplierNum: crow && crow[index] ? crow[index] : null
        }
        recordsItem[`${ConstDynamicTitleStr}${itemTitle}`] = forecastItem
      })
    })
  })

  return records
}

// 编辑后 行数据 转 API 请求数据
export const rowDataToForecastInfo = (args) => {
  const { dynamicTitleList, rowData } = args
  const forecastInfo = {
    dayData: [],
    monthData: [],
    weekData: []
  }
  dynamicTitleList.forEach((itemTitle) => {
    const timeInfo = rowData[itemTitle]?.timeInfo || ''
    if (timeInfo.indexOf(ForecastType.day) === 0) {
      // 天预测
      const dayDataItem = {
        ...rowData[itemTitle]
        // timeInfo: timeInfo.substring(ForecastType.day.length), // 去掉标识
      }
      forecastInfo.dayData.push(dayDataItem)
    } else if (timeInfo.indexOf(ForecastType.week) === 0) {
      // 周预测
      const weekDataItem = {
        ...rowData[itemTitle]
        // timeInfo: timeInfo.substring(ForecastType.week.length), // 去掉标识
      }
      forecastInfo.weekData.push(weekDataItem)
    } else if (timeInfo.indexOf(ForecastType.month) === 0) {
      // 月预测
      const monthDataItem = {
        ...rowData[itemTitle]
        // timeInfo: timeInfo.substring(ForecastType.month.length), // 去掉标识
      }
      forecastInfo.monthData.push(monthDataItem)
    }
  })

  return forecastInfo
}

// 选择 物料 机弹框 格式化表格列
export const materielTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: 'auto'
    }
    colData.push(defaultCol)
  })

  return colData
}
