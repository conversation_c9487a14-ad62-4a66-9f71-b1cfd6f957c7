<template>
  <div style="height: 100%">
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="middle-blank"></div>
      <!-- 右侧各种操作按钮 -->
      <mt-button css-class="e-flat invite-btn" :is-primary="true" @click="goBack">{{
        $t('返回')
      }}</mt-button>
    </div>
    <mt-data-grid
      style="height: calc(100% - 50px)"
      class="price-dialog"
      :data-source="sampleData"
      :column-data="sampleColumns"
      ref="dataGrid"
      locale="zh"
    ></mt-data-grid>
  </div>
</template>

<script>
import { formatTableColumnData } from '../config/index'
import { ForecastHistoryColumnData } from '../config/constant'
import Vue from 'vue'
export default {
  data() {
    return {
      sampleColumns: formatTableColumnData(ForecastHistoryColumnData, 'withoutVersionLink'),
      sampleData: [],
      isSup: false,
      predictHistoryRow: {}
    }
  },
  mounted() {
    const predictHistoryRow = sessionStorage.getItem('predictHistoryRow')
    if (predictHistoryRow) {
      this.predictHistoryRow = JSON.parse(predictHistoryRow)
    }
    if (this.$route.query.routeName === 'predict-history-supplier-tv') {
      this.isSup = true
    }
    this.getDetail()
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    getDetail() {
      this.getApi()({
        forecastId: this.predictHistoryRow.id,
        version: this.predictHistoryRow.syncVersion?.split('.')[0]
      }).then((res) => {
        const { code, data } = res
        if (code === 200) {
          // 固定的表头
          // const forecastColumns = formatTableColumnData(ForecastColumnData)
          // 动态的日期表头
          const dataRow = this.predictHistoryRow
          const dynamicData = data.record || []
          const titleList = ['versions', 'typeComponent', ...data.headers]
          titleList.forEach((item, index) => {
            this.sampleColumns.push({
              allowEditing: false,
              field: item,
              headerText:
                item === 'versions'
                  ? this.$t('版本')
                  : item === 'typeComponent'
                  ? this.$t('类型')
                  : item,
              width: '150',
              template: () => {
                return {
                  template: Vue.component('typeComponent', {
                    template: `<div class="grid-edit-column mt-flex-direction-column forecast-type-box"><div class="forecast-item" v-for="(cellItem, index) in data[data.column.field]" :key="index">{{cellItem}}</div></div>`
                  })
                }
              }
            })
            dataRow[item] = []
            dynamicData.forEach((itm) => {
              itm.forEach((it, ix) => {
                if (index === ix) {
                  dataRow[item].push(it)
                }
              })
            })
          })

          this.sampleData = [dataRow]
        }
      })
    },
    getApi() {
      if (this.isSup) {
        return this.$API.predictCollaboration.postSupForecastDataReceiveQueryHistoryDetailTv
      }
      return this.$API.predictCollaboration.postBuyerForecastDataReceiveQueryHistoryDetailTv
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('predictHistoryRow')
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
/deep/ .e-grid {
  height: 100%;
  .e-gridcontent {
    height: calc(100% - 50px);
    .e-content {
      height: 100% !important;
    }
  }
}
.header-box {
  height: 50px;
  padding-right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .itemcon {
    display: flex;
    align-items: center;
  }
  .item {
    margin-right: 20px;
  }
  .middle-blank {
    flex: 1;
  }
  .status {
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(99, 134, 193, 1);
    padding: 4px;
    background: rgba(238, 242, 249, 1);
    border-radius: 2px;
  }

  .infos {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
  }

  .sort-box {
    position: relative;
    cursor: pointer;
    margin-right: 20px;
    .mt-icons {
      font-size: 12px;
      transform: scale(0.5);
      color: rgba(0, 70, 156, 1);
      margin-top: -10px;
      position: absolute;
      top: 0;

      &:nth-child(2) {
        top: 6px;
      }
    }
  }
}
</style>
