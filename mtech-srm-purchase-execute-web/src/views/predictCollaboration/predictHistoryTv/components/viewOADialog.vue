<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="modalData.title"
  >
    <mt-data-grid
      style="height: 100%"
      class="price-dialog"
      :data-source="sampleData"
      :column-data="sampleColumns"
      ref="dataGrid"
      locale="zh"
    ></mt-data-grid>
  </mt-dialog>
</template>

<script>
import { formatTableColumnData } from '../config/index'
import { ForecastHistoryColumnData } from '../config/constant'
import Vue from 'vue'
export default {
  data() {
    return {
      sampleColumns: formatTableColumnData(ForecastHistoryColumnData, 'withoutVersionLink'),
      buttons: [
        // {
        //   click: this.cancel,
        //   buttonModel: { content: this.$t("取消") },
        // },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      sampleData: [],
      isSup: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  mounted() {
    if (this.modalData.routeInfo.name === 'predict-history-supplier-tv') {
      this.isSup = true
    }
    this.userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
    this.$refs['dialog'].ejsRef.show()
    this.getDetail()
  },
  methods: {
    getDetail() {
      if (this.isSup) {
        // 供方
        this.$API.predictCollaboration
          .postSupForecastDataReceiveQueryHistoryDetailTv({
            forecastId: this.modalData.row.id,
            version: this.modalData.row.syncVersion
          })
          .then((res) => {
            const { code, data } = res
            if (code === 200) {
              // 固定的表头
              // const forecastColumns = formatTableColumnData(ForecastColumnData)
              // 动态的日期表头
              const dataRow = this.modalData.row
              const dynamicData = data.record || []
              const titleList = ['versions', 'typeComponent', ...data.headers]
              titleList.forEach((item, index) => {
                this.sampleColumns.push({
                  allowEditing: false,
                  field: item,
                  headerText:
                    item === 'versions'
                      ? this.$t('版本')
                      : item === 'typeComponent'
                      ? this.$t('类型')
                      : item,
                  width: '150',
                  template: () => {
                    return {
                      template: Vue.component('typeComponent', {
                        template: `<div class="grid-edit-column mt-flex-direction-column forecast-type-box"><div class="forecast-item" v-for="(cellItem, index) in data[data.column.field]" :key="index">{{cellItem}}</div></div>`
                      })
                    }
                  }
                })
                dataRow[item] = []
                dynamicData.forEach((itm) => {
                  itm.forEach((it, ix) => {
                    if (index === ix) {
                      dataRow[item].push(it)
                    }
                  })
                })
              })

              this.sampleData = [dataRow]
            }
          })
      } else {
        // 采方
        this.$API.predictCollaboration
          .postBuyerForecastDataReceiveQueryHistoryDetailTv({
            forecastId: this.modalData.row.id,
            version: this.modalData.row.syncVersion
          })
          .then((res) => {
            const { code, data } = res
            if (code === 200) {
              // 固定的表头
              // const forecastColumns = formatTableColumnData(ForecastColumnData)
              // 动态的日期表头
              const dataRow = this.modalData.row
              const dynamicData = data.record || []
              const titleList = ['versions', 'typeComponent', ...data.headers]
              titleList.forEach((item, index) => {
                this.sampleColumns.push({
                  allowEditing: false,
                  field: item,
                  headerText:
                    item === 'versions'
                      ? this.$t('版本')
                      : item === 'typeComponent'
                      ? this.$t('类型')
                      : item,
                  width: '150',
                  template: () => {
                    return {
                      template: Vue.component('typeComponent', {
                        template: `<div class="grid-edit-column mt-flex-direction-column forecast-type-box"><div class="forecast-item" v-for="(cellItem, index) in data[data.column.field]" :key="index">{{cellItem}}</div></div>`
                      })
                    }
                  }
                })
                dataRow[item] = []
                dynamicData.forEach((itm) => {
                  itm.forEach((it, ix) => {
                    if (index === ix) {
                      dataRow[item].push(it)
                    }
                  })
                })
              })

              this.sampleData = [dataRow]
            }
          })
      }
    },
    confirm() {
      this.$emit('confirm-function')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
/deep/ .e-grid .e-content {
  min-height: 380px;
}
</style>
