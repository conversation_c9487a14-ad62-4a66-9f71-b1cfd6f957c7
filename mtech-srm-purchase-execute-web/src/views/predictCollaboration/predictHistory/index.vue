<template>
  <!-- 预测历史数据列表-采方 -->
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      :hidden-tabs="true"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { ColumnData, formatTableColumnData } from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'

export default {
  components: {},
  data() {
    return {
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          manage: sessionStorage.getItem('manage'),

          gridId: this.$tableUUID.predictCollaboration.predictHistory.list,
          grid: {
            lineIndex: 0,
            columnData: formatTableColumnData(ColumnData),
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/buyer/forecast/dataReceive/query/history`,
              defaultRules: []
            }
            // frozenColumns: 1,
          }
        }
      ]
    }
  },
  mounted() {
    if (this.$route.path.includes('kt')) {
      sessionStorage.setItem('manage', 'kt')
    }
    if (this.$route.path.includes('bd')) {
      sessionStorage.setItem('manage', 'bd')
    }
  },
  methods: {
    // CellTitle
    handleClickCellTitle(e) {
      if (e.field === 'batchCode') {
        // 版本号 click
        this.goToPredictView({
          template: e.data.forecastTemplate,
          version: e.data.batchCode
        })
      }
    },
    // 跳转到 预测历史数据详情-采方
    goToPredictView(args) {
      const { template, version } = args
      // 跳转到 预测历史数据详情-采方
      this.$router.push({
        name: 'predict-view',
        query: {
          template,
          version
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
