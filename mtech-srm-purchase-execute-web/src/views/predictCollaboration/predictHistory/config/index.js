import { i18n } from '@/main.js'
import { ColumnComponent as Component } from './columnComponent'

// 表格列数据
export const ColumnData = [
  {
    fieldCode: 'batchCode',
    fieldName: i18n.t('版本号')
  },
  {
    fieldCode: 'startTime',
    fieldName: i18n.t('同步时间')
  },
  {
    fieldCode: 'forecastTemplate',
    fieldName: i18n.t('预测模板')
  }
]

// 预测模板 2：短周期；1：中周期；0：长周期；
export const ForecastTemplate = {
  long: 0, // 长周期
  medium: 1, // 中周期
  short: 2 // 短周期
}
// 预测模板 text
export const ForecastTemplateText = {
  [ForecastTemplate.long]: i18n.t('长周期'),
  [ForecastTemplate.medium]: i18n.t('中周期'),
  [ForecastTemplate.short]: i18n.t('短周期')
}

// 格式化表格动态数据
export const formatTableColumnData = (data) => {
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: 'auto' // 自适应
    }
    if (col.fieldCode === 'batchCode') {
      // 版本号
      defaultCol.width = '102'
      defaultCol.cellTools = [] // 使其可点击查看
    } else if (col.fieldCode === 'startTime') {
      // 同步时间
      defaultCol.width = '150'
      defaultCol.template = Component.timeDate({
        dataKey: col.fieldCode,
        isDateTime: true,
        isDate: false,
        isTime: false
      })
    } else if (col.fieldCode === 'forecastTemplate') {
      // 任务类型 定时发布/定时反馈 任务类型:发布-1、反馈-2
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            // 长期
            value: ForecastTemplate.long,
            text: ForecastTemplateText[ForecastTemplate.long],
            cssClass: ''
          },
          {
            // 中期
            value: ForecastTemplate.medium,
            text: ForecastTemplateText[ForecastTemplate.medium],
            cssClass: ''
          },
          {
            // 短期
            value: ForecastTemplate.short,
            text: ForecastTemplateText[ForecastTemplate.short],
            cssClass: ''
          }
        ]
      }
    }
    colData.push(defaultCol)
  })

  return colData
}
