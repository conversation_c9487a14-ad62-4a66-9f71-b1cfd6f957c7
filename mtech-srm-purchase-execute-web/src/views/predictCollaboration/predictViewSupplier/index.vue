<template>
  <!-- 预测历史数据详情-供方 -->
  <div class="full-height pt20 vertical-flex-box">
    <!-- 头部 -->
    <div class="top-info flex-keep">
      <div class="header-box">
        <div class="forecast-template">
          <span>{{ $t('预测模版：') }}</span>
          <mt-select
            class="forecast-template-select"
            v-model="forecastTemplate"
            :data-source="ForecastTemplateOptions"
            :disabled="isForecastTemplateDisabled"
            @change="forecastTemplateChange"
            :placeholder="$t('请选择')"
          ></mt-select>
        </div>
        <div class="middle-blank"></div>
        <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{
          $t('返回')
        }}</mt-button>
      </div>
    </div>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <mt-template-page
        ref="templateRef"
        class="frozenColumns"
        :template-config="componentConfig"
        :hidden-tabs="true"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
        @dataBound="handleDataBound"
      />
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { formatTableColumnData, formatTableDataSource, rowDataToForecastInfo } from './config/index'
import {
  forecastDataSource,
  forecastColumnData,
  dynamicTitle,
  rowDataTemp,
  forecastTypeShow
} from './config/variable'
import {
  Toolbar,
  EditSettings,
  ForecastTemplateOptions,
  ForecastColumnData,
  ConstDynamicTitleStr,
  RequestType,
  ActionType,
  NewRowData,
  DynamicItemInit
} from './config/constant'

export default {
  components: {},
  data() {
    return {
      componentConfig: [],
      apiWaitingQuantity: 0, // 调用的api正在等待数
      forecastTemplate: '', // 预测模版 页面参数
      syncVersion: '', // 预测版本 页面参数
      isForecastTemplateDisabled: true, // 预测模版不可编辑
      Toolbar, // toolbar 按钮配置
      EditSettings, // 编辑设置
      forecastDataSource, // 预测表格数据
      allowPaging: true, // 显示分页角标
      columnData: forecastColumnData, // 表头
      ForecastTemplateOptions, // 预测模板 Options
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 10, // 当前每页数据量
        totalPages: 0, // 总页数
        pageSizes: [10, 50, 100, 200]
      },
      forecastPageCurrent: 1, // 预测表格 当前页码
      forecastRules: [], // 预测表格请求规则
      forecastCondition: 'and', // 过滤-规则-关系，默认为 "and"
      isEditing: false // 正在编辑数据
    }
  },
  mounted() {
    this.forecastTemplate = this.$route.query.template
    this.syncVersion = this.$route.query.version
    // 获取采方预测信息列表
    this.postSupForecastQuery()
    // 初始化表头数据
    this.handleColumns({ titleList: [] })
  },
  methods: {
    // 调整单元格的样式
    queryCellInfo() {
      // const { column, cell } = args;
      // if (
      //   dynamicTitle.includes(`${ConstDynamicTitleStr}${column.field}`) ||
      //   "forecastDataType" === column.field
      // ) {
      //   cell.classList.add("forecast-td");
      // }
    },
    // toolbar 按钮点击
    handleClickToolBar(args) {
      const { toolbar, grid, rules } = args
      const selectedRecords = grid.getSelectedRecords()
      const commonToolbar = [
        'ForecastAdd',
        'ForecastImport',
        'Filter',
        'Refresh',
        'refreshDataByLocal',
        'filterDataByLocal',
        'resetDataByLocal',
        'ForecastExport',
        'Setting'
      ]

      if (this.isEditing && toolbar.id !== 'refreshDataByLocal') {
        // 结束编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }

      if (selectedRecords.length == 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })

      if (toolbar.id === 'refreshDataByLocal') {
        // 刷新 供方-获取供方预测信息列表
        this.postSupForecastQuery()
      } else if (toolbar.id === 'filterDataByLocal') {
        // 筛选-过滤
        const { condition, rules: ruleList } = rules
        this.forecastCondition = condition
        this.forecastRules = ruleList
        // 供方-获取供方预测信息列表
        this.postSupForecastQuery()
      } else if (toolbar.id === 'resetDataByLocal') {
        // 筛选重置
        this.forecastCondition = 'and'
        this.forecastRules = []
        // 供方-获取供方预测信息列表
        this.postSupForecastQuery()
      }
    },
    // CellTool
    handleClickCellTool() {
      if (this.isEditing) {
        // 结束编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }
    },
    addNewRow() {
      const dynamicItem = {}
      dynamicTitle.forEach((itemTitle) => {
        const timeInfo = itemTitle.substring(ConstDynamicTitleStr.length)
        dynamicItem[itemTitle] = cloneDeep(DynamicItemInit)
        dynamicItem[itemTitle].timeInfo = timeInfo // 初始数据的timeInfo
      })
      return {
        ...dynamicItem,
        ...NewRowData
      }
    },
    // actionBegin 表格编辑生命周期
    actionBegin(args) {
      const { requestType, action, rowData } = args
      // console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args);
      if (requestType === RequestType.add) {
        // 开始行添加
        this.isEditing = true
        // 即将添加一行，赋值新增行的初始数据
        rowDataTemp.length = 0
        rowDataTemp.push(this.addNewRow())
        args.rowData = this.addNewRow()
        args.data = this.addNewRow()
      } else if (requestType === RequestType.save && action === ActionType.add) {
        // 即将保存新增时，将新增中编辑的数据付给组件
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === RequestType.beginEdit) {
        // 开始行编辑
      }
    },
    // actionComplete 表格编辑生命周期
    actionComplete(args) {
      const { requestType, action, rowIndex } = args
      // console.log(
      //   `Complete,\nrequest: ${requestType}\naction: ${action}`,
      //   args
      // );
      if (requestType === RequestType.save && action == ActionType.edit) {
        // 完成编辑行
        this.isEditing = false
        this.postSupForecastSaveForecast({
          data: rowDataTemp[rowDataTemp.length - 1],
          rowIndex
        })
      } else if (requestType === RequestType.save && action == ActionType.add) {
        // 完成新增行
        this.isEditing = false
      } else if (requestType === RequestType.sorting || requestType === RequestType.refresh) {
        // 丢弃编辑中状态
        this.isEditing = false
      }
    },
    // 供方-修改预测信息
    postSupForecastSaveForecast(args) {
      const { data, rowIndex } = args
      const forecastInfo = rowDataToForecastInfo({
        dynamicTitleList: dynamicTitle,
        rowData: data
      })
      const params = {
        id: data?.id,
        supRemark: data?.supRemark,
        forecastInfo
      }
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postSupForecastSaveForecast(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 供方-获取供方预测信息列表
            this.postSupForecastQuery()
          }
        })
        .catch(() => {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.apiEndLoading()
        })
    },
    // 供方-反馈
    postSupForecastFeedback(params) {
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postSupForecastFeedback(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 供方-获取供方预测信息列表
            this.postSupForecastQuery()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方-获取供方预测信息列表
    postSupForecastQuery() {
      this.componentConfig = []
      // 处理查询条件
      this.forecastRules = this.handleRulesFormat(this.forecastRules)
      const params = {
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        },
        condition: this.forecastCondition,
        forecastTemplate: this.forecastTemplate, // 预测模板
        rules: [...this.forecastRules],
        defaultRules: [
          {
            // 版本
            field: 'syncVersion',
            operator: 'equal',
            value: this.syncVersion
          }
        ]
      }
      this.apiStartLoading()
      if (this.manage === 'kt') {
        this.$API.predictCollaboration
          .postSupKtForecastQuery(params)
          .then((res) => {
            this.apiEndLoading()
            this.$store.commit('startLoading') // 因为表格渲染比较耗时，在handleDataBound中结束
            if (res?.code == 200) {
              const total = res?.data?.forecasts?.total || 0
              this.forecastPageSettings.totalPages = Math.ceil(
                Number(total) / this.forecastPageSettings.pageSize
              )
              const records = res?.data?.forecasts?.records || [] // 表格数据
              const titleList = res?.data?.titleList || [] // 动态表头数据
              // 处理表头数据
              this.handleColumns({ titleList })
              // 处理表数据
              this.handleDataSource({ records, titleList })
              const config = {
                useToolTemplate: false, // 不使用预置(新增、编辑、删除)
                useBaseConfig: false, // 使用组件中的toolbar配置
                toolbar: [Toolbar, ['Filter', 'Refresh', 'Setting']], // 此页面为动态数据关联的动态表格，故不可设置字段显示隐藏
                gridId: this.$tableUUID.predictCollaboration.predictViewSupplier.list,
                grid: {
                  gridLines: 'Both',

                  allowPaging: false, // 不分页
                  allowReordering: false, // 不可拖动排序 因为预测数据是动态获取到的，表格记忆排序不能处理
                  // frozenColumns: 1, // 冻结第一列 使用此设置表格数据将不可见，已使用 frozenFistColumns 实现
                  columnData: forecastColumnData,
                  dataSource: forecastDataSource,
                  editSettings: EditSettings
                }
              }
              this.componentConfig.push(config)
            }
          })
          .catch(() => {
            this.apiEndLoading()
          })
      } else {
        this.$API.predictCollaboration
          .postSupForecastQuery(params)
          .then((res) => {
            this.apiEndLoading()
            this.$store.commit('startLoading') // 因为表格渲染比较耗时，在handleDataBound中结束
            if (res?.code == 200) {
              const total = res?.data?.forecasts?.total || 0
              this.forecastPageSettings.totalPages = Math.ceil(
                Number(total) / this.forecastPageSettings.pageSize
              )
              const records = res?.data?.forecasts?.records || [] // 表格数据
              const titleList = res?.data?.titleList || [] // 动态表头数据
              // 处理表头数据
              this.handleColumns({ titleList })
              // 处理表数据
              this.handleDataSource({ records, titleList })
              const config = {
                useToolTemplate: false, // 不使用预置(新增、编辑、删除)
                useBaseConfig: false, // 使用组件中的toolbar配置
                toolbar: [Toolbar, ['Filter', 'Refresh', 'Setting']], // 此页面为动态数据关联的动态表格，故不可设置字段显示隐藏
                gridId: this.$tableUUID.predictCollaboration.predictViewSupplier.list,
                grid: {
                  gridLines: 'Both',

                  allowPaging: false, // 不分页
                  allowReordering: false, // 不可拖动排序 因为预测数据是动态获取到的，表格记忆排序不能处理
                  // frozenColumns: 1, // 冻结第一列 使用此设置表格数据将不可见，已使用 frozenFistColumns 实现
                  columnData: forecastColumnData,
                  dataSource: forecastDataSource,
                  editSettings: EditSettings
                }
              }
              this.componentConfig.push(config)
            }
          })
          .catch(() => {
            this.apiEndLoading()
          })
      }
    },
    // 处理查询条件 rules
    handleRulesFormat(data) {
      const rules = cloneDeep(data)
      forecastTypeShow.length = 0
      if (rules?.length) {
        rules.forEach((itemRule, index) => {
          if (itemRule.field === 'forecastType') {
            // 类型
            if (itemRule.value?.length > 0) {
              // 勾选了类型字段中的选项
              itemRule.value.forEach((item) => {
                forecastTypeShow.push(item)
              })
            } else {
              // 没有勾选，默认显示全部
              forecastTypeShow.length = 0
            }
            rules.splice(index, 1)
          }
        })
      }
      return rules
    },
    // 处理表头数据
    handleColumns(data) {
      const { titleList } = data
      forecastColumnData.length = 0 // 清空表头数据
      dynamicTitle.length = 0 // 清空 Template 使用的表头列表
      const titleListColumnData = []
      titleList.forEach((item, index) => {
        dynamicTitle.push(`${ConstDynamicTitleStr}${titleList[index]}`)
        titleListColumnData.push({
          fieldCode: item,
          fieldName: item,
          isForecast: true, // 预测数据的标志
          colIndex: index, // 列的 index
          ignore: true // 不出现在筛选字段中
        })
      })
      // 固定的表头
      const forecastColumns = formatTableColumnData(ForecastColumnData)
      // 动态的日期表头
      const titleListColumns = formatTableColumnData(titleListColumnData)

      // 合并表头数组，然后 push 到 forecastColumnData 中
      const columns = [].concat(forecastColumns).concat(titleListColumns)
      let manage = sessionStorage.getItem('manage')

      if (manage === 'kt') {
        let obj = [
          'manufacturer',
          'productRel',
          'rawMaterialManufacturer',
          'rawMaterialOrigin',
          'packagingManufacturer',
          'packagingOrigin',
          'specialUse'
        ]
        obj.forEach((ob) => {
          columns.splice(
            columns.findIndex((item) => {
              return item.fieldCode === ob
            }),
            1
          )
        })
        columns.splice(
          columns.findIndex((item) => {
            return item.fieldCode === 'productRel'
          }),
          1
        )
      }
      if (manage === 'bd') {
        let obj = ['purchaseAheadTime', 'noQuotasFlag']
        obj.forEach((ob) => {
          columns.splice(
            columns.findIndex((item) => {
              return item.fieldCode === ob
            }),
            1
          )
        })
        // columns.splice(
        //   columns.findIndex((item) => {
        //     return item.fieldCode === "productRel";
        //   }),
        //   1
        // );
      }
      columns.forEach((item) => {
        forecastColumnData.push(item)
      })
    },
    // 处理表数据
    handleDataSource(data) {
      const { records, titleList } = data
      forecastDataSource.length = 0 // 清空表格数据

      const dataSource = cloneDeep(formatTableDataSource({ records, titleList }))
      dataSource.forEach((item) => {
        // 将每一项 push 到 forecastDataSource 中
        forecastDataSource.push(item)
      })
    },
    forecastTemplateChange(e) {
      // 获取预测数据
      this.forecastTemplate = e.value
      this.postSupForecastQuery()
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.postSupForecastQuery()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.postSupForecastQuery()
    },
    // 表格数据绑定完成
    handleDataBound() {
      this.$store.commit('endLoading')
    },
    goBack() {
      // 返回 预测历史列表-供方
      this.$router.push({
        name: 'predict-history-supplier-kt'
      })
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  // padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  .header-box {
    // border-bottom: 1px solid #e6e9ed;
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
  }
}
.forecast-template {
  width: 250px;
  padding-bottom: 8px;
  .forecast-template-select {
    width: calc(250px - 70px);
  }
}
/deep/ .column-tool {
  margin-top: 8px;
}
/deep/ .template-svg {
  cursor: pointer;
  font-size: 12px;
  color: #6386c1;

  &:nth-child(n + 2) {
    margin-left: 10px;
  }
}

/deep/ .grid-edit-column {
  padding: 12px 0;
}

// 预测数据行
/deep/ .forecast-item {
  height: 40px;
  padding: 7px;
  line-height: 26px;
}

// 预测数据高亮数据
/deep/ .forecast-highlight {
  color: #ed5836;
  background-color: #fdeeea;
  border: 1px solid #ed5836;
}
// 预测数据行
/deep/ .forecast-item {
  height: 40px;
  padding: 7px;
  line-height: 26px;
  border: 1px solid #e8e8e8;
}
/deep/ .inputSy {
  border: 1px solid #e8e8e8;
}
// 表格容器
#forecast-manage-table-container {
  position: relative;

  // 表格数据视图
  /deep/ .e-gridcontent > .e-content {
    height: 0px;
    transition: height 0.5s ease;
  }

  // 分页
  /deep/ .mt-pagertemplate {
    margin: 10px 0 10px !important;
  }
}
/deep/ .mt-data-grid {
  .e-gridcontent {
    .e-table {
      tbody {
        tr:nth-child(2n-1) {
          background: #f6f7fb;
          td:first-child {
            position: sticky;
            left: 0px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #f6f7fb;
          }

          td:nth-child(2) {
            position: sticky;
            left: 80px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #f6f7fb;
          }
          td:nth-child(3) {
            position: sticky;
            left: 180px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #f6f7fb;
          }
          td:nth-child(4) {
            position: sticky;
            left: 330px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #f6f7fb;
          }
          td:nth-child(5) {
            position: sticky;
            left: 430px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #f6f7fb;
          }
          td:nth-child(6) {
            position: sticky;
            left: 530px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #f6f7fb;
          }
        }
        tr:nth-child(2n) {
          background: #fff;
          td:first-child {
            position: sticky;
            left: 0px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #fff;
          }
          td:nth-child(2) {
            position: sticky;
            left: 80px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #fff;
          }
          td:nth-child(3) {
            position: sticky;
            left: 180px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #fff;
          }
          td:nth-child(4) {
            position: sticky;
            left: 330px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #fff;
          }
          td:nth-child(5) {
            position: sticky;
            left: 430px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #fff;
          }
          td:nth-child(6) {
            position: sticky;
            left: 530px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #fff;
          }
        }
      }
    }
  }
}
/deep/ .frozenColumns {
  .template-wrap {
    .e-headercontent .e-table {
      & thead th:first-child {
        position: sticky;
        left: 0px;
        z-index: 1;
      }
      & thead th:nth-child(2) {
        position: sticky;
        left: 80px;
        z-index: 1;
      }
      & thead th:nth-child(3) {
        position: sticky;
        left: 180px;
        z-index: 1;
      }
      & thead th:nth-child(4) {
        position: sticky;
        left: 330px;
        z-index: 1;
      }
      & thead th:nth-child(5) {
        position: sticky;
        left: 430px;
        z-index: 1;
      }
      & thead th:nth-child(6) {
        position: sticky;
        left: 530px;
        z-index: 1;
      }
    }
  }
}
// 表格预测类型 fieldCode: "forecastDataType"
// /deep/ .forecast-type-box {
//   border-left: 1px solid #e8e8e8;
// }
// /deep/ .forecast-item:nth-child(-n + 6) {
//   border-bottom: 1px solid #e8e8e8;
// }
</style>
