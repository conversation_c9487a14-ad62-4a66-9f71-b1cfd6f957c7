<template>
  <!-- 预测管理-采方 -->
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      @reset="handleCustomReset"
      @search="handleCustomSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="versionNo" :label="$t('大版本号')" label-style="top">
          <mt-date-picker
            :show-clear-button="true"
            :placeholder="$t('请选择大版本号')"
            v-model="searchFormModel.versionNo"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="siteCodeList" :label="$t('工厂')" label-style="top">
          <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.siteCodeList"
            url="/masterDataManagement/tenant/site/app/paged-query"
            multiple
            :placeholder="$t('请选择工厂')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.itemCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.itemName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="buyerGroupCode" :label="$t('采购组')">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.buyerGroupCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="typeList" :label="$t('类型')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.typeList"
            :data-source="typeList"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="statusList" :label="$t('状态')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.statusList"
            :data-source="statusOptions"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="platform" :label="$t('平台')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.platform"
            :placeholder="$t('支持模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="itemGroupCode" :label="$t('物料组')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.buyerGroupCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="itemGroupCode" :label="$t('外部物料组')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.itemGroupCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="fieldTypeList" :label="$t('正式与预测显示切换')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.fieldTypeList"
            :data-source="[
              { text: $t('正式需求'), value: 1 },
              { text: $t('预测需求'), value: 2 },
              { text: $t('需求合计'), value: 3 }
            ]"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="publishTime" :label="$t('最后发布时间')" label-style="top">
          <mt-date-range-picker
            style="flex: 1"
            v-model="searchFormModel.publishTime"
            @change="(e) => handleDateTimeChange(e, 'publishTime')"
            :placeholder="$t('请选择最后发布时间')"
          />
        </mt-form-item>
        <mt-form-item prop="feedbackTime" :label="$t('最后反馈时间')" label-style="top">
          <mt-date-range-picker
            style="flex: 1"
            v-model="searchFormModel.feedbackTime"
            @change="(e) => handleDateTimeChange(e, 'feedbackTime')"
            :placeholder="$t('请选择最后反馈时间')"
          />
        </mt-form-item>
        <mt-form-item prop="isSatisfyList" :label="$t('正式需求是否满足')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.isSatisfyList"
            :data-source="[
              { text: $t('否'), value: 'N' },
              { text: $t('是'), value: 'Y' }
            ]"
            :show-clear-button="true"
            :placeholder="$t('请选择正式需求是否满足')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item
          prop="forecastIsSatisfyList"
          :label="$t('预测需求是否满足')"
          label-style="top"
        >
          <mt-multi-select
            v-model="searchFormModel.forecastIsSatisfyList"
            :data-source="[
              { text: $t('否'), value: 'N' },
              { text: $t('是'), value: 'Y' }
            ]"
            :show-clear-button="true"
            :placeholder="$t('请选择预测需求是否满足')"
          ></mt-multi-select>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        :row-config="{ height: rowHeight }"
        :columns="columns"
        :table-data="tableData"
        show-overflow
        height="auto"
        header-align="left"
        align="left"
        style="padding-top: unset"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true,
          activeMethod: this.activeRowMethod
        }"
        :cell-style="cellStyle"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        @edit-closed="editComplete"
        @edit-actived="editBegin"
        @edit-disabled="editDisabledEvent"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #supplierRemarksEdit="{ row }">
          <vxe-input v-model="row.supplierRemarks" :disabled="row.status != 2"></vxe-input>
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { StatusSearchOptions, forecastColumnData, ToolBar } from './config/constant'
import { utils } from '@mtech-common/utils'
import dayjs from 'dayjs'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      toolbar: ToolBar,
      searchFormModel: {
        versionNo: new Date()
      },
      tableData: [],
      columns: forecastColumnData,
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      statusOptions: StatusSearchOptions,
      typeList: [
        // { text: this.$t('D(原始需求)'), value: 'D' },
        { text: this.$t('P(需求量)'), value: 'P' },
        { text: this.$t('C(承诺量)'), value: 'C' },
        { text: this.$t('Gap'), value: 'GAP' },
        { text: this.$t('累计Gap'), value: 'GAP_TOTAL' }
      ],
      warningContent: this.$t('此状态数据不可编辑'),
      rowHeight: 120,
      fixPreColumns: [
        {
          fieldCode: 'type',
          fieldName: this.$t('类型')
        },
        {
          fieldCode: 'totalDayZs',
          fieldName: this.$t('31天汇总(正式)'),
          type: 1
        },
        {
          fieldCode: 'totalDayYc',
          fieldName: this.$t('31天汇总(预测)'),
          type: 2
        },
        {
          fieldCode: 'totalDay',
          fieldName: this.$t('31天汇总(合计)'),
          type: 3
        },
        {
          fieldCode: 'totalWeekZs',
          fieldName: this.$t('周汇总(正式)'),
          type: 1
        },
        {
          fieldCode: 'totalWeekYc',
          fieldName: this.$t('周汇总(预测)'),
          type: 2
        },
        {
          fieldCode: 'totalWeek',
          fieldName: this.$t('周汇总(合计)'),
          type: 3
        },
        {
          fieldCode: 'totalMonthZs',
          fieldName: this.$t('月汇总(正式)'),
          type: 1
        },
        {
          fieldCode: 'totalMonthYc',
          fieldName: this.$t('月汇总(预测)'),
          type: 2
        },
        {
          fieldCode: 'totalMonth',
          fieldName: this.$t('月汇总(合计)'),
          type: 3
        }
      ],
      fixRightColumns: [
        {
          fieldCode: 'totalOtherMonthZs',
          fieldName: this.$t('其它月汇总(正式)'),
          type: 1
        },
        {
          fieldCode: 'totalOtherMonthYc',
          fieldName: this.$t('其它月汇总(预测)'),
          type: 2
        },
        {
          fieldCode: 'totalOtherMonth',
          fieldName: this.$t('其它月汇总(合计)'),
          type: 3
        }
      ],
      defaultDynamicAddData: [] // 默认增加数据
    }
  },
  mounted() {
    // 供应商、物料、采购组防抖
    this.getSupplierDataSource = utils.debounce(this.getSupplier, 1000)
    this.getItemDataSource = utils.debounce(this.getItem, 1000)
    this.getBuyerGroupSource = utils.debounce(this.getBuyerGroup, 1000)
  },
  beforeRouteLeave(to, from, next) {
    document.querySelector('.vxe-table--body-wrapper').scrollTop = 0
    next()
  },
  methods: {
    // ---------------------------------------- 表格操作 start：--------------------------------------------------------
    cellStyle({ row, column }) {
      if (column.field === 'status') {
        if (row.status == 4) {
          return {
            color: 'red'
          }
        }
      }
    },
    activeRowMethod({ row }) {
      // 已发布的数据才能修改
      if (![2].includes(row.status)) {
        return false
      }
      return true
    },
    editDisabledEvent() {
      this.$toast({
        content: this.warningContent,
        type: 'warning'
      })
      this.warningContent = this.$t('此状态数据不可编辑')
    },
    editBegin() {},
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 校验点击的对象是否为取消编辑按钮
        if (args.$event.target.innerText === this.$t('取消编辑')) {
          // 清除编辑状态
          this.$refs.xTable.$refs.xGrid.clearEdit()
          this.handleCustomSearch()
          return
        }

        this.updateForecastData(row)
      }
    },
    // ---------------------------------------- 表格操作 end:---------------------------------------------------------------
    // ---------------------------------------- 按钮操作 ：start -------------------------------------------------------------
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()

      if (!this.tableData.length && code !== 'ForecastImport') {
        this.$toast({ content: this.$t('请先查询数据再进行操作'), type: 'warning' })
        return
      }

      if (!selectedRecords?.length && ['Feedback'].includes(code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })

      if (code === 'Feedback') {
        // 确认
        this.handleFeedBack(idList)
      } else if (code === 'ForecastImport') {
        this.handleImport()
      } else if (code === 'ForecastExportReImport') {
        this.handleExport('re')
      } else if (code === 'ForecastExport') {
        this.handleExport()
      }
    },
    // 反馈
    handleFeedBack(ids) {
      this.apiStartLoading()
      this.$API.predictCollaboration
        .feedBackSupplierForecast({ ids })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 导入
    handleImport() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel,
        versionNo: dayjs(this.searchFormModel.versionNo).format('YYYYMMDD')
      }
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.predictCollaboration.importSupplierForecast,
          downloadTemplateApi: this.$API.predictCollaboration.exportReImportSupplierForecast,
          paramsKey: 'excel',
          downloadTemplateParams: {
            ...params
          }
        },
        success: () => {
          // 导入之后刷新列表
          this.handleCustomSearch()
        }
      })
    },
    // 导出
    handleExport(type) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel,
        versionNo: dayjs(this.searchFormModel.versionNo).format('YYYYMMDD')
      }
      this.apiStartLoading()
      this.$API.predictCollaboration[
        type === 're' ? 'exportReImportSupplierForecast' : 'exportSupplierForecast'
      ](params).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 查询
    async handleCustomSearch() {
      let params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel,
        versionNo: dayjs(this.searchFormModel.versionNo).format('YYYYMMDD')
      }
      this.rowHeight = 32 * (this.searchFormModel.typeList?.length || this.typeList.length)
      this.apiStartLoading()
      // 处理动态列
      await this.getDynamicColumn()
      await this.$API.predictCollaboration
        .querySupplierForecast(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)

            const records = res?.data?.records || [] // 表格数据
            this.tableData = [...records]
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 重置查询条件
    handleCustomReset() {
      this.searchFormModel = {
        versionNo: new Date()
      }
      this.handleCustomSearch()
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // ---------------------------------------- 按钮操作 end -------------------------------------------------------------
    // ---------------------------------------- 数据处理 ：start ---------------------------------------------------------

    // 修改预测信息
    updateForecastData(row) {
      const params = []
      const obj = { ...row, mainId: row.id }
      delete obj.forecastDetailResList
      row.forecastDetailResList.forEach((v) => {
        if (['C'].includes(v.type)) {
          params.push({ ...obj, ...v })
        }
      })
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认修改该数据？')
        },
        success: () => {
          this.apiStartLoading()
          this.$API.predictCollaboration
            .updateSupplierForecast(params)
            .then((res) => {
              this.apiEndLoading()
              if (res?.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                // 采方-获取采方预测信息列表
                this.handleCustomSearch()
              }
            })
            .catch(() => {
              this.apiEndLoading()
              // 当出现错误时，指定行进入编辑状态
              this.$refs.xTable.$refs.xGrid.setEditRow([row])
            })
        },
        close: () => {
          this.$refs.xTable.$refs.xGrid.setEditRow([row])
        }
      })
    },
    // 新增预测信息
    addForecastData(row) {
      const params = []
      const obj = { ...row, id: null }
      delete obj.forecastDetailResList
      row.forecastDetailResList.forEach((v) => {
        if (['P'].includes(v.type)) {
          params.push({ ...obj, ...v })
        }
      })
      this.$API.predictCollaboration
        .addForecast(params)
        .then((res) => {
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow([row])
        })
    },
    // 获取动态列
    async getDynamicColumn() {
      let params = {
        versionNo: dayjs(this.searchFormModel.versionNo).format('YYYYMMDD'),
        fieldTypeList: this.searchFormModel.fieldTypeList
      }
      await this.$API.predictCollaboration.queryBuyerKTForecastHeader(params).then((res) => {
        if (res.code === 200) {
          this.handleColumns(res.data)
        }
      })
    },
    handleColumns(data) {
      // 固定表头 - 汇总
      let preColumn = []
      let rightColumn = []
      let fixPreColumns = this.fixPreColumns.filter(
        (item) =>
          this.searchFormModel.fieldTypeList?.includes(item.type) || item.fieldCode === 'type'
      )
      if (!fixPreColumns?.length) fixPreColumns = [...this.fixPreColumns]
      fixPreColumns.forEach((item) => {
        preColumn.push({
          field: item.fieldCode,
          title: item.fieldName,
          minWidth: 160,
          showOverflow: true,
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return row.forecastDetailResList.map((detail, index) => (
                <div key={index} class='vxe-cell-border'>
                  <span
                    style={{ 'margin-left': '10px' }}
                    class={detail[item.fieldCode] < 0 ? 'red' : ''}>
                    {item.fieldCode === 'type'
                      ? this.filterType(detail[item.fieldCode])
                      : detail[item.fieldCode]}
                  </span>
                </div>
              ))
            }
          }
        })
      })
      // 固定表头 - 汇总
      let fixRightColumns = this.fixRightColumns.filter((item) =>
        this.searchFormModel.fieldTypeList?.includes(item.type)
      )
      if (!fixRightColumns?.length) fixRightColumns = [...this.fixRightColumns]
      fixRightColumns.forEach((item) => {
        rightColumn.push({
          field: item.fieldCode,
          title: item.fieldName,
          minWidth: 160,
          showOverflow: true,
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return row.forecastDetailResList.map((detail, index) => (
                <div key={index} class='vxe-cell-border'>
                  <span
                    style={{ 'margin-left': '10px' }}
                    class={detail[item.fieldCode] < 0 ? 'red' : ''}>
                    {detail[item.fieldCode]}
                  </span>
                </div>
              ))
            }
          }
        })
      })
      // 动态的日期表头
      const dynamicColumn = []
      data.forEach((item) => {
        const field = item.fieldCode
        const title = item.fieldName
        dynamicColumn.push({
          field,
          title,
          minWidth: 200,
          showOverflow: true,
          editRender: {},
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return row.forecastDetailResList.map((detail, index) => (
                <div key={index} class='vxe-cell-border'>
                  <span
                    style={{ 'margin-left': '10px' }}
                    class={detail[item.fieldCode] < 0 ? 'red' : ''}>
                    {detail[item.fieldCode]}
                  </span>
                </div>
              ))
            },
            edit: ({ row }) => {
              return row.forecastDetailResList.map((detail, index) => (
                <div key={index} class='vxe-cell-border'>
                  {['C'].includes(detail.type) && [2].includes(row.status) ? (
                    <vxe-input type='number' v-model={detail[item.fieldCode]} />
                  ) : (
                    <span
                      style={{ 'margin-left': '10px' }}
                      class={detail[item.fieldCode] < 0 ? 'red' : ''}>
                      {detail[item.fieldCode]}
                    </span>
                  )}
                </div>
              ))
            }
          }
        })
      })
      this.defaultDynamicAddData = []
      const _columns = [...preColumn, ...dynamicColumn, ...rightColumn]
      this.typeList.forEach((item) => {
        let obj = {}
        _columns.forEach((column) => {
          obj[column.field] = 0
        })
        obj.type = item.value
        this.defaultDynamicAddData.push(obj)
      })
      this.columns = [...forecastColumnData, ...preColumn, ...dynamicColumn, ...rightColumn]
    },
    // ---------------------------------------- 数据处理 : end ---------------------------------------------------------

    // ---------------------------------------- 工具函数 ：start -----------------------------------------------------------
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    },
    filterType(type) {
      const _find = this.typeList.find((item) => item.value === type)
      return _find?.text
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = new Date(e.startDate).getTime()
        this.searchFormModel[field + 'End'] = new Date(e.endDate).getTime()
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    }
    // ---------------------------------------- 工具函数 ：end -------------------------------------------------------------
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  .forecast-type-item {
    &:first-child {
      .vxe-cell-border {
        border-top: none;
      }
    }
    .vxe-cell-border {
      border-top: solid #e6e9ed 1px;
    }
  }
  .dynamic-column-item {
    height: 114px;
    .vxe-cell-border {
      border-top: solid #e6e9ed 1px;
      &:first-child {
        border-top: none;
      }
    }
  }
  .vxe-cell-border {
    &:first-child {
      border-top: none;
    }
    border-top: solid #e6e9ed 1px;
    height: 32px;
    line-height: 32px;
    box-sizing: border-box;
    .red {
      color: red;
    }
  }
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
