import { i18n } from '@/main.js'

export const ToolBar = [
  {
    code: 'Feedback',
    name: i18n.t('反馈'),
    icon: 'vxe-icon-edit',
    transfer: true,
    status: 'info'
  },
  {
    code: 'CancleEdit',
    name: i18n.t('取消编辑'),
    icon: 'vxe-icon-edit',
    transfer: true,
    status: 'info'
  },
  {
    code: 'ForecastImport',
    name: i18n.t('导入'),
    icon: 'vxe-icon-cloud-upload',
    status: 'info'
  },
  {
    code: 'ForecastExportReImport',
    name: i18n.t('导出（可再导入）'),
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  },
  {
    code: 'ForecastExport',
    name: i18n.t('灵活导出（仅查看）'),
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  }
]

// 状态 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
export const Status = {
  new: 0, // 新建
  modified: 1, // 已修改
  pendingFeedback: 2, // 已发布 不可编辑
  feedbackNormal: 3, // 反馈满足
  feedbackAbnormal: 4, // 反馈不满足
  confirmed: 5 // 已确认 不可编辑
}
// 状态 text
export const StatusText = {
  [Status.new]: i18n.t('新建'),
  [Status.modified]: i18n.t('已修改'),
  [Status.pendingFeedback]: i18n.t('已发布'),
  [Status.feedbackNormal]: i18n.t('反馈满足'),
  [Status.feedbackAbnormal]: i18n.t('反馈不满足'),
  [Status.confirmed]: i18n.t('已确认')
}
// 状态 class
export const StatusClass = {
  [Status.new]: 'col-active',
  [Status.modified]: 'col-active',
  [Status.pendingFeedback]: 'col-active', // col-published
  [Status.feedbackNormal]: 'col-active', // col-normal
  [Status.feedbackAbnormal]: 'col-active', // col-abnormal
  [Status.confirmed]: 'col-active' // col-inactive
}
export const StatusSearchOptions = [
  { value: Status.new, text: StatusText[Status.new] },
  { value: Status.modified, text: StatusText[Status.modified] },
  { value: Status.pendingFeedback, text: StatusText[Status.pendingFeedback] },
  { value: Status.feedbackNormal, text: StatusText[Status.feedbackNormal] },
  { value: Status.feedbackAbnormal, text: StatusText[Status.feedbackAbnormal] },
  { value: Status.confirmed, text: StatusText[Status.confirmed] }
]
export const StatusSearchOptionsLabel = [
  { value: Status.new, label: StatusText[Status.new] },
  { value: Status.modified, label: StatusText[Status.modified] },
  { value: Status.pendingFeedback, label: StatusText[Status.pendingFeedback] },
  { value: Status.feedbackNormal, label: StatusText[Status.feedbackNormal] },
  { value: Status.feedbackAbnormal, label: StatusText[Status.feedbackAbnormal] },
  { value: Status.confirmed, label: StatusText[Status.confirmed] }
]

// 预测表格列数据
export const forecastColumnData = [
  {
    type: 'checkbox',
    width: 50,
    ignore: true,
    fixed: 'left'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50
  },
  {
    field: 'statusName',
    title: i18n.t('状态')
  },

  {
    title: i18n.t('大版本号'),
    field: 'versionNo',
    width: 120
  },
  {
    title: i18n.t('小版本号'),
    field: 'smallVersionNo',
    width: 100
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂编码'),
    showOverflow: true,
    width: 160
  },
  {
    field: 'siteName',
    title: i18n.t('工厂名称'),
    width: 115,
    showOverflow: true
  },
  {
    field: 'buyerGroupCode',
    title: i18n.t('采购组'),
    showOverflow: true,
    width: 160,
    formatter: ({ row }) => {
      return row.buyerGroupCode || row.buyerGroupName
        ? `${row.buyerGroupCode}-${row.buyerGroupName}`
        : ''
    }
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码'),
    width: 160,
    showOverflow: true
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称'),
    width: 115
  },
  {
    field: 'itemGroupCode',
    title: i18n.t('外部物料组'),
    width: 115,
    showOverflow: true,
    formatter: ({ row }) => {
      return row.itemGroupCode || row.itemGroupName
        ? `${row.itemGroupCode}-${row.itemGroupName}`
        : ''
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码'),
    formatter: ({ row }) => {
      return row.supplierCode || row.supplierName ? `${row.supplierCode}-${row.supplierName}` : ''
    },
    width: 160,
    showOverflow: true
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    width: 115,
    showOverflow: true
  },
  {
    field: 'platform',
    title: i18n.t('平台'),
    width: 135,
    showOverflow: true
  },
  {
    field: 'purchaseCycle',
    title: i18n.t('采购周期'),
    width: 135,
    showOverflow: true
  },
  {
    field: 'frontInfo',
    title: i18n.t('前置期'),
    width: 135,
    showOverflow: true
  },
  {
    field: 'planner',
    title: i18n.t('计划员'),
    width: 135,
    showOverflow: true
  },
  {
    field: 'unclearPoQty',
    title: i18n.t('正式未清PO'),
    width: 135,
    showOverflow: true
  },
  {
    field: 'forecastUnclearPoQty',
    title: i18n.t('预测未清PO'),
    width: 135,
    showOverflow: true
  },
  {
    field: 'planOrder',
    title: i18n.t('正式计划订单'),
    width: 135,
    showOverflow: true
  },
  {
    field: 'yearInstockQty',
    title: i18n.t('年入库量'),
    width: 135,
    showOverflow: true
  },
  {
    field: 'monthInstockQty',
    title: i18n.t('当月入库数量'),
    width: 135,
    showOverflow: true
  },
  {
    field: 'onWayQty',
    title: i18n.t('当前在途量'),
    width: 135,
    showOverflow: true
  },
  {
    field: 'vmiInv',
    title: i18n.t('VMI库存'),
    width: 135,
    showOverflow: true
  },
  {
    field: 'siteInv',
    title: i18n.t('供方本厂库存'),
    width: 135,
    showOverflow: true
  },
  {
    field: 'stockQty',
    title: i18n.t('建议备货'),
    width: 135,
    showOverflow: true
  },
  {
    field: 'purRemark',
    title: i18n.t('采方备注'),
    showOverflow: true,
    width: 210
  },
  {
    field: 'supplierRemarks',
    title: i18n.t('供方备注'),
    showOverflow: true,
    width: 210,
    editRender: {},
    slots: {
      edit: 'supplierRemarksEdit'
    }
  },

  {
    field: 'sapTime',
    title: i18n.t('SAP运算时间'),
    showOverflow: true
  },
  {
    field: 'syncScmTimestamp',
    title: i18n.t('SRM最后同步SCM时间'),
    showOverflow: true
  },
  {
    field: 'publishTime',
    title: i18n.t('最后发布时间'),
    showOverflow: true
  },
  {
    field: 'feedbackTime',
    title: i18n.t('最后反馈时间'),
    showOverflow: true
  },
  {
    field: 'isSatisfiedName',
    title: i18n.t('正式需求是否满足'),
    showOverflow: true
  },
  {
    field: 'forecastIsSatisfyName',
    title: i18n.t('预测需求是否满足'),
    width: 130,
    showOverflow: true
  }
]
