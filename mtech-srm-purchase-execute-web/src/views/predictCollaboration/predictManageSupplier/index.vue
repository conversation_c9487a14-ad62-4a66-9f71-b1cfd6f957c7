<template>
  <!-- 预测管理-供方 -->
  <div class="full-height pt20 vertical-flex-box">
    <!-- 头部 -->
    <div class="header-search-bar">
      <div class="forecast-template">
        <span>{{ $t('预测模版：') }}</span>
        <mt-select
          class="forecast-template-select"
          v-model="forecastTemplate"
          :data-source="ForecastTemplateOptions"
          :disabled="isForecastTemplateDisabled"
          @change="forecastTemplateChange"
          :placeholder="$t('请选择')"
        ></mt-select>
      </div>
      <div v-if="manage == 'bd'" class="forecast-template">
        <span>{{ $t('展示无需求物料：') }}</span>
        <mt-select
          class="forecast-flag-select"
          v-model="forecastFlag"
          :data-source="forecastFlagMap"
          @change="forecastFlagChange"
          :placeholder="$t('请选择')"
        ></mt-select>
      </div>
    </div>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <mt-template-page
        ref="templateRef"
        class="frozenColumns"
        :template-config="componentConfig"
        v-if="componentConfig.length > 0"
        :hidden-tabs="true"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
        @dataBound="handleDataBound"
      />
    </div>
    <!-- 预测数据导入弹框 -->
    <import-dialog
      :accept="['.xls', '.xlsx']"
      :from-data-key="fromDataKey"
      :save-url="saveUrl"
      :has-step="false"
      ref="importDialog"
      @import="importDialogImport"
      @uploadCompleted="uploadCompleted"
    >
      <!-- <predict-import-dialog-slot
        ref="predictImportDialogSlot"
        @submitted="predictImportDialogSlotSubmitted"
      ></predict-import-dialog-slot> -->
    </import-dialog>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { download, getHeadersFileName } from '@/utils/utils'

import { formatTableColumnData, formatTableDataSource, rowDataToForecastInfo } from './config/index'
import {
  forecastDataSource,
  forecastColumnData,
  dynamicTitle,
  rowDataTemp,
  forecastTypeShow
} from './config/variable'
import {
  Toolbar,
  ToolbarKt,
  EditSettings,
  ForecastTemplate,
  ForecastTemplateOptions,
  ForecastColumnData,
  ConstDynamicTitleStr,
  RequestType,
  ActionType,
  NewRowData,
  DynamicItemInit,
  Status,
  StatusText
} from './config/constant'
import { BASE_TENANT } from '@/utils/constant'
import ImportDialog from '@/components/Upload/importDialog'
export default {
  components: {
    ImportDialog
  },
  data() {
    return {
      componentConfig: [],
      forecastCacheCode: '',
      fromDataKey: 'excel', // 上传组件的 name，此值为调用 api 请求参数的 key
      saveUrl: ``, // 导入API
      apiWaitingQuantity: 0, // 调用的api正在等待数
      forecastTemplate: ForecastTemplate.long, // 预测模版 默认值：长周期
      forecastFlag: null,
      forecastFlagMap: [
        { text: this.$t('是'), value: 'Y' },
        { text: this.$t('否'), value: 'N' }
      ],
      isForecastTemplateDisabled: false, // 预测模版可编辑
      Toolbar, // toolbar 按钮配置
      EditSettings, // 编辑设置
      allowPaging: true, // 显示分页角标
      ForecastTemplateOptions, // 预测模板 Options
      manage: 'bd',
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 10, // 当前每页数据量
        totalRecordsCount: 0,
        totalPages: 0, // 总页数
        pageSizes: [10, 50, 100, 200]
      },
      forecastPageCurrent: 1, // 预测表格 当前页码
      forecastRules: [], // 预测表格请求规则
      forecastCondition: 'and', // 过滤-规则-关系，默认为 "and"
      isEditing: false, // 正在编辑数据
      defaultRules: []
    }
  },
  mounted() {
    if (this.$route.path.includes('kt')) {
      sessionStorage.setItem('manage', 'kt')
      this.saveUrl = `${BASE_TENANT}/sup/forecast/kt-import`
    }
    if (this.$route.path.includes('bd')) {
      sessionStorage.setItem('manage', 'bd')
      this.saveUrl = `${BASE_TENANT}/sup/forecast/import`
    }

    this.manage = sessionStorage.getItem('manage')

    console.log(this.$route.path)
    // 获取采方预测信息列表
    this.postSupForecastQuery()
    // 初始化表头数据
    // this.handleColumns({ titleList: [] });
  },
  beforeDestroy() {
    sessionStorage.removeItem('manage')
  },
  methods: {
    // 调整单元格的样式
    queryCellInfo() {
      // const { column, cell } = args;
      // if (
      //   dynamicTitle.includes(`${ConstDynamicTitleStr}${column.field}`) ||
      //   "forecastDataType" === column.field
      // ) {
      //   cell.classList.add("forecast-td");
      // }
    },
    // toolbar 按钮点击
    handleClickToolBar(args) {
      const { toolbar, grid, rules } = args
      const selectedRecords = grid.getSelectedRecords()
      const commonToolbar = [
        'ForecastAdd',
        'ForecastImport',
        'Filter',
        'Refresh',
        'refreshDataByLocal',
        'filterDataByLocal',
        'resetDataByLocal',
        'ForecastExport',
        'Setting'
      ]

      if (this.isEditing && toolbar.id !== 'refreshDataByLocal') {
        // 结束编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }

      if (selectedRecords.length == 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })

      if (toolbar.id === 'ForecastSubmit') {
        // 提交
        if (this.isValidForecastSubmit({ data: selectedRecords, idList })) {
          this.postSupForecastFeedback(idList)
        }
      } else if (toolbar.id === 'refreshDataByLocal') {
        // 刷新 供方-获取供方预测信息列表
        this.postSupForecastQuery()
      } else if (toolbar.id === 'filterDataByLocal') {
        // 筛选-过滤
        this.forecastPageCurrent = 1 // 点击“查询”时重置页码 -- lbj - 2023.05.05
        const { condition, rules: ruleList } = rules
        this.forecastCondition = condition
        this.forecastRules = ruleList
        // 供方-获取供方预测信息列表

        if (this.manage === 'kt') {
          if (this.forecastRules.length > 0) {
            this.postSupForecastQuery()
          }
        } else {
          this.postSupForecastQuery()
        }
      } else if (toolbar.id === 'ForecastExport') {
        // 导出
        this.postBuyerForecastExport()
      } else if (toolbar.id === 'ForecastImport') {
        // 导入
        this.$refs.importDialog.init({
          title: this.$t('导入')
        })
      } else if (toolbar.id === 'resetDataByLocal') {
        // 筛选重置
        this.forecastCondition = 'and'
        this.forecastRules = []
        // 供方-获取供方预测信息列表
        this.postSupForecastQuery()
      }
    },
    // 预测数据导入编辑弹框 点击导入
    importDialogImport() {
      const params = { code: this.forecastCacheCode }
      this.apiStartLoading()
      this.$API.predictCollaboration
        .getSupForecastCacheSubmit(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.importDialog.handleClose()
            this.postSupForecastQuery()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 导入提交
    predictImportDialogSlotSubmitted() {
      this.$refs.importDialog.handleClose()
      this.postSupForecastQuery()
    },
    // CellTool
    handleClickCellTool(e) {
      if (this.isEditing) {
        // 结束编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }

      if (e.tool.id === 'ForecastSubmit') {
        // 提交
        if (this.isValidForecastSubmit({ data: [e.data], idList: [e.data.id] })) {
          this.postSupForecastFeedback([e.data.id])
        }
      }
    },
    uploadCompleted(response) {
      //   this.$refs.importDialog.showStepSecond();
      this.forecastCacheCode = response?.data
      //   this.$nextTick(() => {
      //     this.$refs.predictImportDialogSlot.init({
      //       forecastCacheCode: response?.data || "",
      //     });
      //   });
    },
    // 校验多行选择提交的数据
    isValidForecastSubmit(args) {
      const { data, idList } = args
      let isValid = true
      const rowSerialNumberList = [] // 记录 C 行有 0 的行序号
      for (let i = 0; i < data.length; i++) {
        if (data[i].status != Status.pendingFeedback) {
          // 如果状态不是待反馈
          this.$toast({
            content: `${this.$t('请选择')}${StatusText[Status.pendingFeedback]}${this.$t(
              '状态的数据'
            )}`,
            type: 'warning'
          })
          return false
        } else {
          for (let j = 0; j < dynamicTitle.length; j++) {
            const colData = data[i][dynamicTitle[j]]
            if (!colData) {
              continue
            }
            const colSupplierNum = colData.supplierNum ?? '' // C 行值， null 或 undefined 时为 ""，"" 是新增行时的默认值
            if (colSupplierNum === '') {
              rowSerialNumberList.push(data[i].serialNumber)
              break
            }
          }
        }
      }
      // 选择的数据存在C行存在0的数据
      if (rowSerialNumberList.length > 0) {
        isValid = false
        let rowInfo = ''
        let rowMsg = ''
        if (rowSerialNumberList.length > 5) {
          // 显示5行
          const top5 = rowSerialNumberList.slice(0, 5)
          rowInfo = top5.join('/')
          rowMsg = `${rowInfo}...`
        } else {
          const rowInfo = rowSerialNumberList.join('/')
          rowMsg = rowInfo
        }
        if (data.length === 1) {
          // 只选择一条数据进行提交
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: `${this.$t('预测数据 C(承诺量) 有空值，是否需要以数量为 0 直接填入')}`
            },
            success: () => {
              this.postSupForecastFeedback(idList)
            }
          })
        } else {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: `${this.$t('第')}${rowMsg}${this.$t(
                '行，预测数据 C(承诺量) 有空值，是否需要以数量为 0 直接填入'
              )}`
            },
            success: () => {
              this.postSupForecastFeedback(idList)
            }
          })
        }
      }

      return isValid
    },
    addNewRow() {
      const dynamicItem = {}
      dynamicTitle.forEach((itemTitle) => {
        const timeInfo = itemTitle.substring(ConstDynamicTitleStr.length)
        dynamicItem[itemTitle] = cloneDeep(DynamicItemInit)
        dynamicItem[itemTitle].timeInfo = timeInfo // 初始数据的timeInfo
      })
      return {
        ...dynamicItem,
        ...NewRowData
      }
    },
    // actionBegin 表格编辑生命周期
    actionBegin(args) {
      const { requestType, action, rowData } = args
      // console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args);
      if (requestType === RequestType.add) {
        // 开始行添加
        this.isEditing = true
        // 即将添加一行，赋值新增行的初始数据
        rowDataTemp.length = 0
        rowDataTemp.push(this.addNewRow())
        args.rowData = this.addNewRow()
        args.data = this.addNewRow()
      } else if (requestType === RequestType.save && action === ActionType.add) {
        // 即将保存新增时，将新增中编辑的数据付给组件
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === RequestType.beginEdit) {
        // 待反馈的数据可以编辑
        const ableEditStatus = [Status.pendingFeedback]
        if (ableEditStatus.includes(rowData.status)) {
          // 开始行编辑
          this.isEditing = true
          // 即将编辑行，赋值当前行的数据
          rowDataTemp.length = 0
          rowDataTemp.push(rowData)
        } else {
          args.cancel = true
          this.$toast({
            content: this.$t('此状态数据不可编辑'),
            type: 'warning'
          })
          return
        }
      }
    },
    // 供方-导出
    postBuyerForecastExport() {
      const params = {
        page: {
          current: 1,
          size: 10000
        },
        condition: this.forecastCondition,
        rules: [...this.forecastRules],
        forecastTemplate: this.forecastTemplate, // 预测模板
        forecastFlag: this.forecastFlag,
        defaultRules: [
          {
            field: 'latestVersion',
            operator: 'equal',
            value: 1
          }
        ]
      }
      this.apiStartLoading()
      if (this.manage === 'kt') {
        this.$API.predictCollaboration.postSupKtForecastExport(params).then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
      } else {
        this.$API.predictCollaboration.postSupForecastExport(params).then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
      }
    },
    // actionComplete 表格编辑生命周期
    actionComplete(args) {
      const { requestType, action, rowIndex } = args
      // console.log(
      //   `Complete,\nrequest: ${requestType}\naction: ${action}`,
      //   args
      // );
      if (requestType === RequestType.save && action == ActionType.edit) {
        // 完成编辑行
        this.isEditing = false
        this.postSupForecastSaveForecast({
          data: rowDataTemp[rowDataTemp.length - 1],
          rowIndex
        })
      } else if (requestType === RequestType.save && action == ActionType.add) {
        // 完成新增行
        this.isEditing = false
      } else if (requestType === RequestType.sorting || requestType === RequestType.refresh) {
        // 丢弃编辑中状态
        this.isEditing = false
      }
    },
    // 供方-修改预测信息
    postSupForecastSaveForecast(args) {
      const { data, rowIndex } = args
      const forecastInfo = rowDataToForecastInfo({
        dynamicTitleList: dynamicTitle,
        rowData: data
      })
      const params = {
        id: data?.id,
        supRemark: data?.supRemark,
        forecastInfo
      }
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postSupForecastSaveForecast(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 供方-获取供方预测信息列表
            this.postSupForecastQuery()
          }
        })
        .catch(() => {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          this.apiEndLoading()
        })
    },
    // 供方-反馈
    postSupForecastFeedback(params) {
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postSupForecastFeedback(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 供方-获取供方预测信息列表
            this.postSupForecastQuery()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方-获取供方预测信息列表
    async postSupForecastQuery() {
      this.componentConfig = []
      // 处理查询条件
      this.forecastRules = this.handleRulesFormat(this.forecastRules)
      if (this.$route.query.from === 'mytodo') {
        this.defaultRules = JSON.parse(sessionStorage.getItem('todoDetail')).defaultRules.concat([
          {
            field: 'latestVersion',
            operator: 'equal',
            value: 1
          }
        ])
      } else {
        this.defaultRules = [
          {
            field: 'latestVersion',
            operator: 'equal',
            value: 1
          }
        ]
      }
      const params = {
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        },
        condition: this.forecastCondition,
        rules: [...this.forecastRules],
        forecastTemplate: this.forecastTemplate, // 预测模板
        forecastFlag: this.forecastFlag,
        defaultRules: this.defaultRules
      }
      this.apiStartLoading()

      await this.$API.predictCollaboration
        .postSupForecastQuery(params)
        .then((res) => {
          this.apiEndLoading()
          this.$store.commit('startLoading') // 因为表格渲染比较耗时，在handleDataBound中结束
          if (res?.code == 200) {
            const total = res?.data?.forecasts?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.totalRecordsCount = Number(total)
            res?.data?.forecasts?.records.forEach((item) => {
              if (item.noQuotasFlag == '0') {
                item.noQuotasFlag = ''
              }
              if (item.noQuotasFlag == '1') {
                item.noQuotasFlag = 'x'
              }
            })
            const records = res?.data?.forecasts?.records || [] // 表格数据
            const titleList = res?.data?.titleList || [] // 动态表头数据
            // 处理表头数据

            titleList.length > 0
              ? this.handleColumns({ titleList })
              : this.handleColumns({ titleList: [] })

            // 处理表数据
            this.handleDataSource({ records, titleList })
            const config = {
              useToolTemplate: false, // 不使用预置(新增、编辑、删除)
              useBaseConfig: false, // 使用组件中的toolbar配置
              toolbar: [
                this.manage === 'kt' ? ToolbarKt : Toolbar,
                ['Filter', 'Refresh', 'Setting']
              ], // 此页面为动态数据关联的动态表格，故不可设置字段显示隐藏
              gridId: this.$tableUUID.predictCollaboration.predictManageSupplier.list,
              grid: {
                gridLines: 'Both',

                allowPaging: false, // 不分页
                allowReordering: false, // 不可拖动排序 因为预测数据是动态获取到的，表格记忆排序不能处理
                // frozenColumns: 1, // 冻结第一列 使用此设置表格数据将不可见，已使用 frozenFistColumns 实现
                columnData: forecastColumnData,
                dataSource: forecastDataSource,
                editSettings: EditSettings
              }
            }
            this.componentConfig.push(config)
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 处理查询条件 rules
    handleRulesFormat(data) {
      const rules = cloneDeep(data)
      forecastTypeShow.length = 0
      if (rules?.length) {
        rules.forEach((itemRule, index) => {
          if (itemRule.field === 'forecastType') {
            // 类型
            if (itemRule.value?.length > 0) {
              // 勾选了类型字段中的选项
              itemRule.value.forEach((item) => {
                forecastTypeShow.push(item)
              })
            } else {
              // 没有勾选，默认显示全部
              forecastTypeShow.length = 0
            }
            rules.splice(index, 1)
          }
        })
      }
      return rules
    },
    // 处理表头数据
    handleColumns(data) {
      const { titleList } = data
      forecastColumnData.length = 0 // 清空表头数据
      dynamicTitle.length = 0 // 清空 Template 使用的表头列表
      const titleListColumnData = []
      titleList.forEach((item, index) => {
        dynamicTitle.push(`${ConstDynamicTitleStr}${titleList[index]}`)
        titleListColumnData.push({
          fieldCode: item,
          fieldName: item,
          isForecast: true, // 预测数据的标志
          colIndex: index, // 列的 index
          ignore: true // 不出现在筛选字段中
        })
      })
      // 固定的表头
      const forecastColumns = formatTableColumnData(ForecastColumnData)

      // 动态的日期表头
      const titleListColumns = formatTableColumnData(titleListColumnData)
      // 合并表头数组，然后 push 到 forecastColumnData 中
      const columns = [].concat(forecastColumns).concat(titleListColumns)
      let manage = sessionStorage.getItem('manage')

      if (manage === 'kt') {
        let obj = [
          'manufacturer',
          'productRel',
          'rawMaterialManufacturer',
          'rawMaterialOrigin',
          'packagingManufacturer',
          'packagingOrigin',
          'specialUse'
        ]
        obj.forEach((ob) => {
          columns.splice(
            columns.findIndex((item) => {
              return item.fieldCode === ob
            }),
            1
          )
        })
      }
      if (manage === 'bd') {
        let obj = ['purchaseAheadTime', 'noQuotasFlag']
        obj.forEach((ob) => {
          columns.splice(
            columns.findIndex((item) => {
              return item.fieldCode === ob
            }),
            1
          )
        })
        // columns.splice(
        //   columns.findIndex((item) => {
        //     return item.fieldCode === "productRel";
        //   }),
        //   1
        // );
      }
      columns.forEach((item) => {
        forecastColumnData.push(item)
      })
      // columns.forEach((item) => {
      //   forecastColumnData.push(item);
      // });
      console.log(forecastColumnData)
    },
    // 处理表数据
    handleDataSource(data) {
      const { records, titleList } = data
      forecastDataSource.length = 0 // 清空表格数据

      const dataSource = cloneDeep(formatTableDataSource({ records, titleList }))
      dataSource.forEach((item) => {
        // 将每一项 push 到 forecastDataSource 中
        forecastDataSource.push(item)
      })
    },
    forecastTemplateChange(e) {
      // 获取预测数据
      this.forecastPageCurrent = 1 // 重置页码 -- lbj - 2023.05.05
      this.forecastTemplate = e.value
      this.postSupForecastQuery()
    },
    forecastFlagChange(e) {
      this.forecastPageCurrent = 1 // 重置页码 -- lbj - 2023.05.05
      this.forecastFlag = e.value
      this.postBuyerForecastQuery()
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.postSupForecastQuery()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.postSupForecastQuery()
    },
    // 表格数据绑定完成
    handleDataBound() {
      // debugger;
      setTimeout(() => {
        this.$refs.templateRef.resetGridHeight()
      }, 1)
      this.$store.commit('endLoading')
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.e-grid .e-gridcontent {
  td.e-rowcell:not(:first-of-type) {
    padding: 0 0px !important;
  }
}
/deep/ .e-grid .e-gridcontent {
  td.e-rowcell:not(:first-of-type) {
    padding: 0 0px !important;
  }
}
.header-search-bar {
  display: flex;
}
.forecast-template {
  width: 250px;
  padding-bottom: 8px;
  display: flex;
  align-items: center;
  margin-right: 20px;
  span {
    word-break: keep-all !important;
  }
  .forecast-template-select {
    width: calc(250px - 70px);
  }
  .forecast-flag-select {
    width: calc(250px - 90px);
  }
}
/deep/ .column-tool {
  margin-top: 8px;
}
/deep/ .template-svg {
  cursor: pointer;
  font-size: 12px;
  color: #6386c1;

  &:nth-child(n + 2) {
    margin-left: 10px;
  }
}
/deep/ .mt-data-grid {
  .e-gridcontent {
    .e-table {
      tbody {
        .e-editedrow {
          .e-editcell {
            overflow: visible !important;
            form {
              .e-table {
                tbody {
                  tr:nth-child(2n-1) {
                    background: #f6f7fb;
                    td:first-child {
                      position: sticky;
                      left: 0px;
                      z-index: 1;
                      border-right: 1px solid var(--plugin-dg-shadow-color);
                      background-color: #f6f7fb;
                    }
                    td:nth-child(2) {
                      position: sticky;
                      left: 50px;
                      z-index: 1;
                      border-right: 1px solid var(--plugin-dg-shadow-color);
                      background-color: #f6f7fb;
                    }
                    td:nth-child(3) {
                      position: sticky;
                      left: 130px;
                      z-index: 1;
                      border-right: 1px solid var(--plugin-dg-shadow-color);
                      background-color: #f6f7fb;
                    }
                    td:nth-child(4) {
                      position: sticky;
                      left: 230px;
                      z-index: 1;
                      border-right: 1px solid var(--plugin-dg-shadow-color);
                      background-color: #f6f7fb;
                    }
                    td:nth-child(5) {
                      position: sticky;
                      left: 380px;
                      z-index: 1;
                      border-right: 1px solid var(--plugin-dg-shadow-color);
                      background-color: #f6f7fb;
                    }
                    td:nth-child(6) {
                      position: sticky;
                      left: 480px;
                      z-index: 1;
                      border-right: 1px solid var(--plugin-dg-shadow-color);
                      background-color: #f6f7fb;
                    }
                    // td:nth-child(7) {
                    //   position: sticky;
                    //   left: 630px;
                    //   z-index: 1;
                    //   border-right: 1px solid var(--plugin-dg-shadow-color);
                    //   background-color: #f6f7fb;
                    // }
                  }
                }
              }
            }
          }
        }
        tr:nth-child(2n-1) {
          background: #f6f7fb;
          td:first-child {
            position: sticky;
            left: 0px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #f6f7fb;
          }
          td:nth-child(2) {
            position: sticky;
            left: 50px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #f6f7fb;
          }
          td:nth-child(3) {
            position: sticky;
            left: 130px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #f6f7fb;
          }
          td:nth-child(4) {
            position: sticky;
            left: 230px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #f6f7fb;
          }
          td:nth-child(5) {
            position: sticky;
            left: 380px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #f6f7fb;
          }
          td:nth-child(6) {
            position: sticky;
            left: 480px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #f6f7fb;
          }
          // td:nth-child(7) {
          //   position: sticky;
          //   left: 550px;
          //   z-index: 1;
          //   border-right: 1px solid var(--plugin-dg-shadow-color);
          //   background-color: #f6f7fb;
          // }
        }
        tr:nth-child(2n) {
          background: #fff;
          td:first-child {
            position: sticky;
            left: 0px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #fff;
          }
          td:nth-child(2) {
            position: sticky;
            left: 50px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #fff;
          }
          td:nth-child(3) {
            position: sticky;
            left: 130px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #fff;
          }
          td:nth-child(4) {
            position: sticky;
            left: 230px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #fff;
          }
          td:nth-child(5) {
            position: sticky;
            left: 380px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #fff;
          }
          td:nth-child(6) {
            position: sticky;
            left: 480px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #fff;
          }
          // td:nth-child(7) {
          //   position: sticky;
          //   left: 550px;
          //   z-index: 1;
          //   border-right: 1px solid var(--plugin-dg-shadow-color);
          //   background-color: #fff;
          // }
        }
      }
    }
  }
}
/deep/ .grid-edit-column {
  padding: 12px 0;
}

// 预测数据行
/deep/ .forecast-item {
  height: 40px;
  padding: 7px;
  line-height: 26px;
  border: 1px solid #e8e8e8;
}
/deep/ .inputSy {
  border: 1px solid #e8e8e8;
}
// 预测数据高亮数据
/deep/ .forecast-highlight {
  color: #ed5836;
  background-color: #fdeeea;
  border: 1px solid #ed5836;
}

// 表格容器
#forecast-manage-table-container {
  position: relative;

  // 表格数据视图
  // /deep/ .e-gridcontent > .e-content {
  //   height: 0px;
  //   transition: height 0.5s ease;
  // }

  // 分页
  /deep/ .mt-pagertemplate {
    margin: 10px 0 10px !important;
  }
}
/deep/ .frozenColumns {
  .template-wrap {
    .e-headercontent .e-table {
      & thead th:first-child {
        position: sticky;
        left: 0px;
        z-index: 1;
      }
      & thead th:nth-child(2) {
        position: sticky;
        left: 50px;
        z-index: 1;
      }
      & thead th:nth-child(3) {
        position: sticky;
        left: 130px;
        z-index: 1;
      }
      & thead th:nth-child(4) {
        position: sticky;
        left: 230px;
        z-index: 1;
      }
      & thead th:nth-child(5) {
        position: sticky;
        left: 380px;
        z-index: 1;
      }
      & thead th:nth-child(6) {
        position: sticky;
        left: 480px;
        z-index: 1;
      }
      // & thead th:nth-child(7) {
      //   position: sticky;
      //   left: 550px;
      //   z-index: 1;
      // }
    }
  }
}
// 表格预测类型 fieldCode: "forecastDataType"
// /deep/ .forecast-type-box {
//   border-left: 1px solid #e8e8e8;
// }
// /deep/ .forecast-item:nth-child(-n + 6) {
//   border-bottom: 1px solid #e8e8e8;
// }
</style>
