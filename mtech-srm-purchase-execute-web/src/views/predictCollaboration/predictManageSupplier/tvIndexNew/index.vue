<template>
  <!-- 预测管理-采方 -->
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      @reset="handleCustomReset"
      @search="handleCustomSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="factoryCodes" :label="$t('工厂')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.factoryCodes"
            :url="$API.masterData.getSiteAuthFuzzyUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            params-key="fuzzyParam"
            records-position="data"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="itemCodes" :label="$t('物料编码')" label-style="top">
          <mt-input
            v-model="itemCodes"
            @change="itemCodesChange"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.itemName"
            :placeholder="$t('支持模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="planners" :label="$t('计划员')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.planners"
            :data-source="plannerListOptions"
            :fields="{ text: 'codeAndName', value: 'userCode' }"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.status"
            :data-source="statusOptions"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="mrpArea" :label="$t('MRP区域')" label-style="top">
          <mt-input v-model="searchFormModel.mrpArea"></mt-input>
        </mt-form-item>
        <mt-form-item prop="structSeqNo" :label="$t('结构序号')" label-style="top">
          <mt-input v-model="searchFormModel.structSeqNo"></mt-input>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        :row-config="{ height: 73 }"
        :columns="columns"
        :table-data="tableData"
        show-overflow
        height="auto"
        header-align="left"
        align="left"
        style="padding-top: unset"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true,
          activeMethod: this.activeRowMethod
        }"
        grid="becb679d-1d0d-4309-8d3f-0d06128fe26d"
        :cell-style="cellStyle"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        @edit-closed="editComplete"
        @edit-actived="editBegin"
        @edit-disabled="editDisabledEvent"
      >
        <template slot="custom-tools">
          <!-- :icon="item.icon" -->
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #supplierInvEdit="{ row }">
          <vxe-input v-model="row.supplierInv" :disabled="row.status > 4"></vxe-input>
        </template>
        <template #forecastTypeDefault="{ row }">
          <div>
            <div
              v-for="(item, index) in forecastTypeOptions"
              :key="item"
              class="forecast-type-item"
            >
              <div
                v-if="
                  index === 0 &&
                  row.prow &&
                  row.prow.length &&
                  row.id &&
                  showForecastTypes.indexOf('P') !== -1
                "
                :class="item ? 'vxe-cell-border' : ''"
              >
                {{ item }}
              </div>
              <div
                v-if="
                  index === 1 &&
                  ((row.crow && row.crow.length && row.id) || !row.planner) &&
                  showForecastTypes.indexOf('C') !== -1
                "
                :class="item ? 'vxe-cell-border' : ''"
              >
                {{ item }}
              </div>
              <div
                v-if="
                  index === 2 &&
                  row.gap &&
                  row.gap.length &&
                  row.id &&
                  showForecastTypes.indexOf('Gap') !== -1
                "
                :class="item ? 'vxe-cell-border' : ''"
              >
                {{ item }}
              </div>
            </div>
          </div>
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import {
  StatusSearchOptions,
  ForecastTypeDataSource,
  ForecastColumnData,
  ToolBar
} from './config/constant'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      itemCodes: '',
      supplierEmptyFlag: null,
      toolbar: ToolBar,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      searchFormModel: {
        supplierName: '',
        // itemName: '',
        itemCodes: '',
        status: [],
        factoryCodes: null,
        supplierCodes: '',
        mrpArea: ''
        // structSeqNo: ''
      },
      syncVersion: '',
      titleList: [],
      tableData: [],
      columns: ForecastColumnData,
      plannerListOptions: [], // 计划员 下列选项
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      isEditing: false, // 正在编辑数据
      exportRules: [], //导出规则
      statusOptions: StatusSearchOptions,
      forecastTypeOptions: ForecastTypeDataSource,
      copySearchFormModel: null,
      forecastTypes: [],
      showForecastTypes: ['P', 'C', 'Gap'],
      isEdit: false,
      warningContent: this.$t('此状态数据不可编辑')
    }
  },
  mounted() {
    this.getPlannerList()
  },
  methods: {
    cellStyle({ row, column }) {
      if (column.field === 'status') {
        if (row.status == 4) {
          return {
            color: 'red'
          }
        }
      }
    },
    itemCodesChange(e) {
      if (e) {
        this.searchFormModel.itemCodes = this.itemCodes.split(' ')
      } else {
        this.searchFormModel.itemCodes = null
      }
    },
    getPlannerList() {
      this.$API.predictCollaboration.getPlannerAllNameSup().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.plannerListOptions = data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.planner} - ${i.plannerName}`,
              userCode: i.planner
            }
          })
        }
      })
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      if (code === 'CloseEdit') {
        // 清除编辑状态
        this.$refs.xTable.$refs.xGrid.clearEdit()
        this.handleCustomSearch()
        return
      }
      if (this.isEdit) {
        // this.$toast({ content: this.$t('请先完成或结束编辑操作'), type: 'warning' })
        return
      }
      if (!this.tableData.length && code !== 'ForecastImport') {
        this.$toast({ content: this.$t('请先查询数据再进行操作'), type: 'warning' })
        return
      }

      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })

      if (code === 'Feedback') {
        // 反馈
        const params = {
          feedbacks: selectedRecords.map((i) => {
            const tvForecastRecord = []
            if (this.titleList && this.titleList.length) {
              this.titleList.forEach((title) => {
                const item = i[`title_${title}`]['supplierNum']
                if (item) {
                  tvForecastRecord.push(String(item))
                } else {
                  tvForecastRecord.push('0')
                }
              })
            }
            return {
              supplierRemark: i.supplierRemark,
              supplierInv: i.supplierInv,
              tvForecastId: i.id,
              tvForecastRecord: tvForecastRecord
            }
          })
        }
        this.postBuyerForecastFeedback(params)
      } else if (code === 'ForecastImport') {
        // // 导入
        // this.$refs.importDialog.init({
        //   title: this.$t('导入')
        // })
        this.$dialog({
          modal: () => import('@/components/uploadDialog'),
          data: {
            title: this.$t('导入'),
            importApi: this.$API.predictCollaboration.postSupForecastImportTv,
            downloadTemplateApi: this.$API.predictCollaboration.postSupForecastImportTempTv,
            paramsKey: 'excel'
            // saveButtonText: this.$t('下一步')
            // asyncParams: {
            //   // requestJson: JSON.stringify(parameter),
            // },
          },
          success: () => {
            // 导入之后刷新列表
            // this.$refs.templateRef.refreshCurrentGridData()
            this.handleCustomSearch()
          }
        })
        return
      } else if (code === 'ForecastExport') {
        // 导出
        // if (selectedRecords.length == 0) {
        //   this.$toast({ content: this.$t('请先选择需要导出的数据'), type: 'warning' })
        //   return
        // }
        this.postBuyerForecastExport(selectedRecords)
      } else if (code === 'ForecastExport1') {
        // 导出
        // if (selectedRecords.length == 0) {
        //   this.$toast({ content: this.$t('请先选择需要导出的数据'), type: 'warning' })
        //   return
        // }
        this.postBuyerForecastExport(selectedRecords, 'isAllowImport')
      }
    },
    postBuyerForecastFeedback(feedbacks, row) {
      const tvForecastQueryReq = {
        ...this.searchFormModel
      }
      // if (this.searchFormModel.factoryCodes) {
      //   tvForecastQueryReq.factoryCodes = this.searchFormModel.factoryCodes.split(' ')
      // } else {
      //   tvForecastQueryReq.factoryCodes = null
      // }
      if (this.searchFormModel.supplierCodes) {
        tvForecastQueryReq.supplierCodes = this.searchFormModel.supplierCodes.split(' ')
      } else {
        tvForecastQueryReq.supplierCodes = null
      }
      const params = {
        ...feedbacks,
        tvForecastQueryReq: tvForecastQueryReq
      }
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postSupForecastFeedbackTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 采方-获取采方预测信息列表
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          if (row) {
            this.$refs.xTable.$refs.xGrid.setEditRow(row)
          }
        })
    },
    // 采方-导出
    postBuyerForecastExport(selectedRecords, isAllowImport) {
      const { visibleColumn } = this.$refs.xTable.$refs.xGrid.getTableColumn()
      const fixHeaderMap = {}
      const dynamicHeaderMap = {}
      if (!isAllowImport) {
        visibleColumn.forEach((i, index) => {
          if (i.field && i.title) {
            if (i.field.includes('title_')) {
              // dynamicHeaderMap.push(i.title)
              dynamicHeaderMap[i.title] = index
            } else {
              // fixHeaderMap.push(i.field)
              if (i.field === 'forecastType') {
                fixHeaderMap['type'] = index
              } else {
                fixHeaderMap[i.field] = index
              }
            }
          }
        })
      }
      const tvForecastQueryReq = {
        ...this.searchFormModel
      }
      // if (this.searchFormModel.factoryCodes) {
      //   tvForecastQueryReq.factoryCodes = this.searchFormModel.factoryCodes.split(' ')
      // } else {
      //   tvForecastQueryReq.factoryCodes = null
      // }
      if (this.searchFormModel.supplierCodes) {
        tvForecastQueryReq.supplierCodes = this.searchFormModel.supplierCodes.split(' ')
      } else {
        tvForecastQueryReq.supplierCodes = null
      }
      const params = {
        forecastIds: selectedRecords.map((i) => i.id),
        tvForecastQueryReq: tvForecastQueryReq,
        fixHeaderMap,
        dynamicHeaderMap
      }
      // this.apiStartLoading()
      this.$API.predictCollaboration.postSupForecastExportTv(params).then((res) => {
        this.apiEndLoading()
        if (res.code === 200) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('导出任务添加成功，请到任务中心查看')
            },
            success: () => {
              this.$router.push(`/middlePlatform/task-center?taskCode=${res.data}`)
            }
          })
        }
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 校验点击的对象是否为取消编辑按钮
        if (args.$event.target.innerText === this.$t('取消编辑')) {
          // 清除编辑状态
          this.$refs.xTable.$refs.xGrid.clearEdit()
          this.handleCustomSearch()
          return
        }
        // 远程数据才有$event属性
        //1、 校验必填 没通过就是this.$refs.xTable.$refs.xGrid.setEditRow(row)
        if (!this.isValidData(row)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow(row)
          return
        }
        //2、 判断是否有row.planner调新增或者编辑接口
        if (row.planner) {
          this.postBuyerForecastSaveForecast({ data: row })
        }
        //3、 接口调用成功调刷新接口
      }
    },
    editBegin() {
      // const { row } = args
      // if (args.$event) {
      //   // 远程数据才有$event属性
      // } else {
      // }
      this.isEdit = true
    },
    // 采方-编辑预测数据
    postBuyerForecastSaveForecast(args) {
      const { data } = args
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认反馈该数据？')
        },
        success: () => {
          const tvForecastRecord = []
          if (this.titleList && this.titleList.length) {
            this.titleList.forEach((title) => {
              const item = data[`title_${title}`]['supplierNum']
              if (item) {
                tvForecastRecord.push(String(item))
              } else {
                tvForecastRecord.push('0')
              }
            })
          }
          const params = {
            feedbacks: [
              {
                supplierRemark: data.supplierRemark,
                supplierInv: data.supplierInv,
                tvForecastId: data.id,
                tvForecastRecord: tvForecastRecord
              }
            ]
          }
          this.postBuyerForecastFeedback(params, data)
        },
        close: () => {
          this.$refs.xTable.$refs.xGrid.setEditRow(data)
        }
      })
    },
    activeRowMethod({ row }) {
      // 已确认的数据不可修改
      if (row.status === 5) {
        return false
      }
      if (!row.supplierCode) {
        this.warningContent = this.$t(
          '该行数据供应商为空，不可编辑，需导出调整后导入，且该条数据做页面删除！'
        )
        return false
      }
      return true
    },
    editDisabledEvent() {
      this.$toast({
        content: this.warningContent,
        type: 'warning'
      })
      this.warningContent = this.$t('此状态数据不可编辑')
    },
    // 校验数据
    isValidData(data) {
      const { itemCode, factoryCode, supplierCode } = data
      let valid = false
      if (!factoryCode) {
        // 工厂代码
        this.$toast({ content: this.$t('工厂不可为空'), type: 'warning' })
      } else if (!itemCode) {
        // 物料代码
        this.$toast({ content: this.$t('物料不可为空'), type: 'warning' })
      } else if (!supplierCode) {
        // 供应商代码
        this.$toast({ content: this.$t('供应商不可为空'), type: 'warning' })
      } else {
        valid = true
      }

      return valid
    },
    // 重置查询条件
    handleCustomReset() {
      this.itemCodes = null
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'bigVersionNo') {
            this.searchFormModel[key] = this.versionList[0]
          }
        }
      }
      this.forecastTypes = []
      this.handleCustomSearch()
    },
    // 采方-获取采方预测信息列表
    handleCustomSearch() {
      this.isEdit = false
      const params = {
        pageSize: this.forecastPageSettings.pageSize,
        pageNum: this.forecastPageCurrent,
        ...this.searchFormModel
      }
      // if (this.searchFormModel.factoryCodes) {
      //   params.factoryCodes = this.searchFormModel.factoryCodes.split(' ')
      // } else {
      //   params.factoryCodes = null
      // }
      if (this.searchFormModel.supplierCodes) {
        params.supplierCodes = this.searchFormModel.supplierCodes.split(' ')
      } else {
        params.supplierCodes = null
      }
      if (this.searchFormModel.supplierEmptyFlag) {
        if (params.supplierCodes) {
          params.supplierCodes.push('')
        } else {
          params.supplierCodes = ['']
        }
      }
      this.showForecastTypes = []
      if (!this.forecastTypes.length) {
        this.showForecastTypes = ['D', 'P', 'C', 'Gap', 'F']
      } else {
        this.forecastTypes.forEach((i) => {
          switch (i) {
            case this.$t('D(原始需求)'):
              this.showForecastTypes.push('D')
              break
            case this.$t('P(需求量)'):
              this.showForecastTypes.push('P')
              break
            case this.$t('C(承诺量)'):
              this.showForecastTypes.push('C')
              break
            case this.$t('Gap(差异)'):
              this.showForecastTypes.push('Gap')
              break
            case 'F':
              this.showForecastTypes.push('F')
              break

            default:
              break
          }
        })
      }
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postSupTvForecastQuery(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.totalRecordsCount = Number(total)
            const records = res?.data?.records || [] // 表格数据
            const titleList = res?.data?.records[0]?.headers || [] // 动态表头数据
            this.titleList = res?.data?.records[0]?.headers || [] // 动态表头数据
            this.syncVersion =
              res?.data?.records[0]?.tvForecastIntegration?.syncVersion.split('.')[0]
            // 处理表头数据
            this.columns = this.handleColumns({ titleList })
            // 处理表数据
            this.tableData = this.handleDataSource({ records, titleList })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleColumns(data) {
      const { titleList } = data
      // 固定的表头
      const forecastColumns = ForecastColumnData
      // 动态的日期表头
      const titleListColumnData = []
      titleList.forEach((item, index) => {
        const title = `title_${titleList[index]}`
        titleListColumnData.push({
          field: title,
          title: item,
          minWidth: 125,
          showOverflow: true,
          editRender: {},
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <div class='dynamic-column-item'>
                  <div
                    v-show={
                      ((row.prow && row.prow.length && row.id) || !row.planner) &&
                      this.showForecastTypes.indexOf('P') !== -1
                    }
                    class='vxe-cell-border'>
                    {row[title]['buyerNum']}
                  </div>
                  <div
                    v-show={
                      ((row.crow && row.crow.length && row.id) || !row.planner) &&
                      this.showForecastTypes.indexOf('C') !== -1
                    }
                    class='vxe-cell-border'>
                    {row[title]['supplierNum']}
                  </div>
                  <div
                    v-show={
                      ((row.gap && row.gap.length && row.id) || !row.planner) &&
                      this.showForecastTypes.indexOf('Gap') !== -1
                    }
                    class='vxe-cell-border'>
                    {row[title]['gapNum']}
                  </div>
                </div>
              ]
            },
            edit: ({ row }) => {
              return [
                <div class='dynamic-column-item'>
                  <div
                    v-show={
                      ((row.prow && row.prow.length && row.id) || !row.planner) &&
                      this.showForecastTypes.indexOf('P') !== -1
                    }
                    class='vxe-cell-border'>
                    {row[title]['buyerNum']}
                  </div>
                  <div
                    v-show={
                      ((row.prow && row.prow.length && row.id) || !row.planner) &&
                      this.showForecastTypes.indexOf('C') !== -1
                    }
                    class='vxe-cell-border'>
                    <mt-input-number
                      show-spin-button={false}
                      show-clear-button={false}
                      v-model={row[title]['supplierNum']}
                      v-show={!title.includes('汇总') && !title.includes('已入库')}
                    />
                    <div v-show={title.includes('汇总') || title.includes('已入库')}>
                      {row[title]['supplierNum'] || 0}
                    </div>
                  </div>
                  <div
                    v-show={
                      ((row.gap && row.gap.length && row.id) || !row.planner) &&
                      this.showForecastTypes.indexOf('Gap') !== -1
                    }
                    class='vxe-cell-border'>
                    {row[title]['gapNum']}
                  </div>
                </div>
              ]
            }
          }
        })
      })

      // 合并表头数组，然后 push 到 forecastColumnData 中
      const columns = [].concat(forecastColumns).concat(titleListColumnData)
      return columns
    },
    handleDataSource(data) {
      const { records, titleList } = data
      const tableData = []
      records.forEach((recordsItem) => {
        const rowData = {
          ...recordsItem.tvForecastIntegration,
          crow: recordsItem.crow,
          prow: recordsItem.prow,
          gap: recordsItem.gap
        }
        if (rowData.syncVersion && rowData.syncVersion.length) {
          // rowData.syncVersion = rowData.syncVersion.substring(0, 8)
          rowData.syncVersion = rowData.syncVersion.slice(0, 8) + rowData.syncVersion.slice(14)
        }
        // recordsItem.thePrimaryKey = recordsItem.tvForecastIntegration.id // 主键的值为 API 数据的 id
        // recordsItem.status = recordsItem.tvForecastIntegration.status ?? 0 // 状态为 undefined/null 时，默认为新建状态（导入时）

        const crow = recordsItem?.crow
        const prow = recordsItem?.prow
        const gap = recordsItem?.gap
        titleList.forEach((itemTitle, index) => {
          crow.forEach(() => {
            // 将预测数据赋给表头对应的对象
            const forecastItem = {
              buyerNum: prow && prow[index] ? prow[index] : null,
              gapNum: gap && gap[index] ? gap[index] : null,
              supplierNum: crow && crow[index] ? crow[index] : null
            }
            rowData[`title_${itemTitle}`] = forecastItem
          })
        })
        tableData.push(rowData)
      })

      return tableData
      // return records
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  },
  beforeRouteLeave(to, from, next) {
    document.querySelector('.vxe-table--body-wrapper').scrollTop = 0
    next()
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  .forecast-type-item {
    &:first-child {
      .vxe-cell-border {
        border-top: none;
      }
    }
    .vxe-cell-border {
      border-top: solid #e6e9ed 1px;
    }
  }
  .dynamic-column-item {
    .vxe-cell-border {
      border-top: solid #e6e9ed 1px;
      &:first-child {
        border-top: none;
      }
    }
  }
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
