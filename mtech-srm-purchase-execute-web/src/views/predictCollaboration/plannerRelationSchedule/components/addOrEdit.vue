<template>
  <div>
    <mt-dialog
      ref="dialog"
      :header="handlerTitle"
      :open="onOpen"
      :buttons="buttonList"
      @close="closeDialog"
    >
      <mt-form ref="addOrEditRef" :model="addOrEditForm" :rules="rules">
        <mt-row :gutter="24">
          <mt-col :span="12">
            <!-- 计划组编码 -->
            <mt-form-item prop="planGroupCode" :label="$t('计划组编码')" label-style="top">
              <mt-input
                v-model="addOrEditForm.planGroupCode"
                :placeholder="$t('请输入计划组编码')"
                :show-clear-button="true"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="planGroupName" :label="$t('计划组名称')" label-style="top">
              <mt-input
                v-model="addOrEditForm.planGroupName"
                :placeholder="$t('请输入计划组名称')"
                :show-clear-button="true"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item prop="planGroupRemark" :label="$t('计划组备注')" label-style="top">
              <mt-input
                v-model="addOrEditForm.planGroupRemark"
                :placeholder="$t('请输入计划组备注')"
                :show-clear-button="true"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="roleLevel" :label="$t('角色级别')" label-style="top">
              <mt-select
                v-model="addOrEditForm.roleLevel"
                :placeholder="$t('请输入角色级别')"
                :show-clear-button="true"
                :data-source="roleLevelOptions"
                :fields="{ text: 'label', value: 'value' }"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item v-if="isEdit" prop="userCode" :label="$t('用户代码')" label-style="top">
              <!-- <mt-input
                v-model="addOrEditForm.userCode"
                :placeholder="$t('请输入用户代码')"
                :show-clear-button="true"
              /> -->

              <mt-select
                style="flex: 1"
                v-model="addOrEditForm.userCode"
                :fields="{ text: 'codeAndName', value: 'planner' }"
                :allow-filtering="true"
                filter-type="Contains"
                :data-source="plannerList"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item v-else prop="userCodes" :label="$t('用户代码')" label-style="top">
              <!-- <mt-input
                v-model="addOrEditForm.userCode"
                :placeholder="$t('请输入用户代码')"
                :show-clear-button="true"
              /> -->
              <mt-multi-select
                style="flex: 1"
                v-model="addOrEditForm.userCodes"
                :show-select-all="true"
                :fields="{ text: 'codeAndName', value: 'planner' }"
                :allow-filtering="true"
                filter-type="Contains"
                :data-source="plannerList"
                :placeholder="$t('请选择')"
              ></mt-multi-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item v-if="isEdit" prop="userName" :label="$t('用户名称')" label-style="top">
              <!-- <mt-input
                v-model="addOrEditForm.userName"
                :placeholder="$t('请输入用户名称')"
                :show-clear-button="true"
              /> -->

              <mt-select
                style="flex: 1"
                v-model="addOrEditForm.userCode"
                mode="Single"
                :fields="{ text: 'plannerName', value: 'planner' }"
                filter-type="Contains"
                :data-source="plannerList"
                disabled
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item v-else prop="userName" :label="$t('用户名称')" label-style="top">
              <!-- <mt-input
                v-model="addOrEditForm.userName"
                :placeholder="$t('请输入用户名称')"
                :show-clear-button="true"
              /> -->

              <mt-multi-select
                style="flex: 1"
                v-model="addOrEditForm.userCodes"
                :fields="{ text: 'plannerName', value: 'planner' }"
                :show-select-all="true"
                filter-type="Contains"
                :data-source="plannerList"
                disabled
                :placeholder="$t('请选择')"
              ></mt-multi-select>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item prop="tel" :label="$t('电话号码')" label-style="top">
              <mt-input
                v-model="addOrEditForm.tel"
                :placeholder="$t('请输入电话号码')"
                :show-clear-button="true"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="emailAddr" :label="$t('邮箱地址')" label-style="top">
              <mt-input
                v-model="addOrEditForm.emailAddr"
                :placeholder="$t('请输入邮件')"
                :show-clear-button="true"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </mt-dialog>
  </div>
</template>
<script>
/* eslint-disable prettier/prettier */
import * as rulesFun from './config.js'
export default {
  data() {
    return {
      isEdit: false,
      rowId: null,
      roleLevelOptions: [
        {
          label: this.$t('计划员'),
          value: '0'
        },
        {
          label: this.$t('计划组长'),
          value: '1'
        }
      ],
      buttonList: [
        {
          click: this.handleCancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.handleAddForm,
          buttonModel: { isPrimary: true, content: this.$t('确认') }
        }
      ],
      addOrEditForm: {
        planGroupCode: '',
        planGroupName: '',
        roleLevel: '',
        userCode: '',
        userCodes: [],
        userName: '',
        planGroupRemark: '',
        tel: '',
        emailAddr: ''
      },
      copyAddOrEditForm: {
        planGroupCode: '',
        planGroupName: '',
        roleLevel: '',
        userCode: '',
        userCodes: [],
        userName: '',
        planGroupRemark: '',
        tel: '',
        emailAddr: ''
      },
      rules: {
        planGroupCode: [
          {
            required: true,
            trigger: 'blur',
            validator: rulesFun.planGroupCodeRules.bind(this)
          }
        ],
        planGroupName: [
          {
            required: true,
            trigger: 'blur',
            validator: rulesFun.planGroupNameRules.bind(this)
          }
        ],
        roleLevel: [
          {
            required: true,
            trigger: 'blur',
            validator: rulesFun.roleLevelRules.bind(this)
          }
        ],
        userCodes: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请选择用户代码')
            // validator: rulesFun.userCodeRules.bind(this)
          }
        ],
        userCode: [
          {
            required: true,
            trigger: 'blur',
            validator: rulesFun.userCodeRules.bind(this)
          }
        ]
        // userName: [
        //   {
        //     required: true,
        //     trigger: 'blur',
        //     validator: rulesFun.userNameRules.bind(this)
        //   }
        // ]
      },
      openModelFlag: false,
      plannerList: []
    }
  },
  computed: {
    handlerTitle() {
      return this.isEdit ? this.$t('编辑') : this.$t('新增')
    }
  },
  created() {
    this.getPlannerList()
  },
  beforeDestory() {
    this.isEdit = false
  },
  methods: {
    getPlannerList() {
      this.$API.predictCollaboration.getPlannerRelationName().then((res) => {
        this.plannerList = res.data.map((i) => {
          return { ...i, codeAndName: `${i.planner} - ${i.plannerName}` }
        })
      })
    },
    handleCancel() {
      this.closeDialog()
    },
    handleAddForm() {
      console.log('flag')
      this.$refs.addOrEditRef.validate((valid) => {
        if (valid) {
          // 新增、编辑
          this.addInfo()
        }
      })
    },
    addInfo() {
      this.$store.commit('startLoading')
      const params = []
      if (this.isEdit) {
        const param = {
          ...this.addOrEditForm,
          userName: this.plannerList.filter((i) => i.planner === this.addOrEditForm.userCode)[0][
            'plannerName'
          ]
        }
        if (this.rowId) {
          param.id = this.rowId
        }
        param.roleLevel = Number(param.roleLevel)
        params.push(param)
      } else {
        this.addOrEditForm.userCodes.forEach((i) => {
          const param = {
            ...this.addOrEditForm,
            userCode: i,
            userName: this.plannerList.filter((j) => j.planner === i)[0]['plannerName']
          }
          if (this.rowId) {
            param.id = this.rowId
          }
          param.roleLevel = Number(param.roleLevel)
          params.push(param)
        })
      }
      const { addPlannerRelationSchedule, updatePlannerRelationSchedule } =
        this.$API.predictCollaboration
      let _API = this.isEdit ? updatePlannerRelationSchedule : addPlannerRelationSchedule
      _API(params)
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: '操作成功!'
            })
            this.$emit('updateData')
            this.$refs.dialog.ejsRef.hide()
          } else {
            this.$toast({
              type: 'error',
              content: this.$t('操作失败')
            })
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    closeDialog() {
      this.$refs.dialog.ejsRef.hide()
      this.addOrEditForm = { ...this.copyAddOrEditForm }
      this.isEdit = false
    },
    onOpen: function (args) {
      args.preventFocus = true
    },
    async clearValid() {
      this.$refs.addOrEditRef.clearValidate()
    },
    openModal(row) {
      console.log('flag')
      console.log(row)
      if (row) {
        this.isEdit = row.isEdit
        for (let key in this.addOrEditForm) {
          if (row[key]) {
            this.$set(this.addOrEditForm, key, row[key])
          }
        }
        this.rowId = row.id
        this.addOrEditForm.roleLevel = String(row.roleLevel)
      }
      this.$refs.dialog.ejsRef.show()
      this.clearValid()
    }
  }
}
</script>
