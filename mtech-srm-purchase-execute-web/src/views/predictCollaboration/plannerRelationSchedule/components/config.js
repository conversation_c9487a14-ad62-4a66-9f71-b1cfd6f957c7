export const planGroupCodeRules = function (rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入计划组编码'))
  }
  return callback()
}
export const planGroupNameRules = function (rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入计划组名称'))
  }
  return callback()
}
export const roleLevelRules = function (rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入角色级别'))
  }
  return callback()
}
export const userCodeRules = function (rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入用户代码'))
  }
  return callback()
}
export const userNameRules = function (rule, value, callback) {
  if (!value) {
    return callback(new Error('请输入用户名称'))
  }
  return callback()
}
