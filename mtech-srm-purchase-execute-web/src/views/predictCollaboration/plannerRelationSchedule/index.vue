<template>
  <div>
    <div class="full-height">
      <mt-template-page
        ref="templateRef"
        :template-config="templateConfig"
        :hidden-tabs="false"
        @handleClickToolBar="handleClickToolBar"
        @handleCustomReset="handleCustomReset"
      >
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="planGroupCode" :label="$t('计划组编码')" label-style="top">
                <mt-input
                  v-model="searchFormModel.planGroupCode"
                  :placeholder="$t('请输入计划组编码')"
                  :show-clear-button="true"
                />
              </mt-form-item>
              <mt-form-item prop="planGroupName" :label="$t('计划组名称')" label-style="top">
                <mt-input
                  v-model="searchFormModel.planGroupName"
                  :placeholder="$t('请输入计划组名称')"
                  :show-clear-button="true"
                />
              </mt-form-item>
              <mt-form-item prop="roleLevel" :label="$t('角色级别')" label-style="top">
                <!-- <mt-input
                  v-model="searchFormModel.planGroupCode"
                  :placeholder="$t('请输入角色等级')"
                  allow-clear
                /> -->
                <mt-select
                  v-model="searchFormModel.roleLevel"
                  :placeholder="$t('请选择角色级别')"
                  :show-clear-button="true"
                  :data-source="roleLevelOptions"
                  :fields="{ text: 'label', value: 'value' }"
                />
              </mt-form-item>
              <mt-form-item prop="userCode" :label="$t('用户代码')" label-style="top">
                <mt-input
                  v-model="searchFormModel.userCode"
                  :placeholder="$t('请输入用户代码')"
                  :show-clear-button="true"
                />
              </mt-form-item>
              <mt-form-item prop="userName" :label="$t('用户名称')" label-style="top">
                <mt-input
                  v-model="searchFormModel.userName"
                  :placeholder="$t('请输入用户名称')"
                  :show-clear-button="true"
                />
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </mt-template-page>
    </div>
    <AddOrEdit ref="addOrEdit" @updateData="handleSearchForm" />
  </div>
</template>
<script>
import { pageConfig } from './config'

export default {
  components: {
    AddOrEdit: () =>
      import(/* webpackChunkName: "./components/addOrEdit.vue" */ './components/addOrEdit.vue')
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      roleLevelOptions: [
        {
          label: this.$t('计划员'),
          value: '0'
        },
        {
          label: this.$t('计划组长'),
          value: '1'
        }
      ],
      templateConfig: pageConfig(),
      searchFormModel: {
        planGroupCode: '',
        planGroupName: '',
        userCode: '',
        userName: ''
      }
    }
  },
  mounted() {
    this.copySearchFormModel = this.searchFormModel
  },
  methods: {
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    handleClickToolBar(e) {
      // console.log()
      const { grid, toolbar } = e
      let rowList = grid.getSelectedRecords() //获取被勾选的数据
      if (toolbar.id === 'Add') {
        this.$refs.addOrEdit.openModal()
      } else if (toolbar.id === 'Save') {
        if (rowList && rowList.length === 0) {
          this.$toast({
            type: 'warning',
            content: this.$t('请选择一条数据')
          })
          return
        }
        if (rowList && rowList.length > 1) {
          this.$toast({
            type: 'warning',
            content: this.$t('单次只能操作一条数据')
          })
          return
        }
        this.$refs.addOrEdit.openModal({ ...rowList[0], isEdit: true })
      } else if (toolbar.id === 'delete') {
        if (rowList && rowList.length === 0) {
          this.$toast({
            type: 'warning',
            content: this.$t('请选择数据')
          })
          return
        }
        this.handleDelete(rowList.map((item) => item.id))
      }
    },
    handleDelete(list) {
      this.$store.commit('startLoading')
      this.$API.predictCollaboration
        .deletePlannerRelationSchedule(list)
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t('删除成功!')
            })
            this.handleSearchForm()
          }
        })
        .catch((err) => {
          this.$toast.error(err.msg)
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    handleSearchForm() {
      this.$refs.templateRef.refreshCurrentGridData()
    }
  }
}
</script>
