// 计划员关系表
import { i18n } from '@/main.js'
import Vue from 'vue'

export const columnData = [
  {
    field: 'planGroupCode',
    headerText: i18n.t('计划组编码'),
    allowEditing: false
  },
  {
    field: 'planGroupName',
    headerText: i18n.t('计划组名称'),
    allowEditing: false
  },
  {
    field: 'planGroupRemark',
    headerText: i18n.t('计划组备注'),
    allowEditing: false
  },
  {
    field: 'roleLevel',
    headerText: i18n.t('角色级别'),
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionTemplate', {
          template: `<span>{{ transFromValue(data.roleLevel) }}</span>`,
          data() {
            return {
              data: {}
            }
          },
          methods: {
            transFromValue(val) {
              if (val === 1) return this.$t('计划组长')
              return this.$t('计划员')
            }
          }
        })
      }
    }
  },
  {
    field: 'userCode',
    headerText: i18n.t('用户代码'),
    allowEditing: false
  },
  {
    field: 'userName',
    headerText: i18n.t('用户名称'),
    allowEditing: false
  },
  {
    field: 'tel',
    headerText: i18n.t('电话号码'),
    allowEditing: false
  },
  {
    field: 'emailAddr',
    headerText: i18n.t('邮箱地址'),
    allowEditing: false
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    allowEditing: false
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    allowEditing: false
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    allowEditing: false
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    allowEditing: false
  }
]

export const pageConfig = () => {
  const config = [
    {
      isUseCustomSearch: true,
      isCustomSearchRules: true,
      toolbar: {
        useBaseConfig: false,
        tools: [
          [
            { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
            { id: 'Save', icon: 'icon_solid_Save', title: i18n.t('编辑') },
            { id: 'delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
          ],
          []
        ]
      },
      useToolTemplate: false,
      grid: {
        // allowPaging: false,
        // useCombinationSelection: true,
        lineSelection: 0, // 选项列
        lineIndex: 1, // 序号列
        columnData: columnData,
        // frozenColumns: 1,
        dateSource: [],
        asyncConfig: {
          url: '/srm-purchase-execute/internal/planner/v1/relation/query',
          serializeList: (list) => {
            if (list.length > 0) {
              let serialNumber = 1
              list.forEach((item) => {
                // 添加序号
                item.serialNumber = serialNumber++
                // if (item.startDate) {
                //   item.startDate = item.startDate.split(' ')[0]
                // }
                // if (item.endDate) {
                //   item.endDate = item.endDate.split(' ')[0]
                // }
              })
            }
            return list
          }
        }
      }
    }
  ]
  return config
}
