<template>
  <!-- 预测历史数据详情-采方 -->
  <div class="full-height pt20 vertical-flex-box">
    <!-- 头部 -->
    <div class="top-info flex-keep">
      <div class="header-box">
        <div class="forecast-template">
          <span>{{ $t('预测模版：') }}</span>
          <mt-select
            class="forecast-template-select"
            v-model="forecastTemplate"
            :data-source="ForecastTemplateOptions"
            :disabled="isForecastTemplateDisabled"
            @change="forecastTemplateChange"
            :placeholder="$t('请选择')"
          ></mt-select>
        </div>
        <div class="middle-blank"></div>
        <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{
          $t('返回')
        }}</mt-button>
      </div>
    </div>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <mt-template-page
        ref="templateRef"
        class="frozenColumns"
        :template-config="componentConfig"
        :hidden-tabs="true"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
        @dataBound="handleDataBound"
      />
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { download, getHeadersFileName } from '@/utils/utils'
import { formatTableColumnData, formatTableDataSource, rowDataToForecastInfo } from './config/index'
import {
  forecastDataSource,
  forecastColumnData,
  dynamicTitle,
  rowDataTemp,
  forecastTypeShow
} from './config/variable'
import {
  Toolbar,
  EditSettings,
  ForecastTemplateOptions,
  ForecastColumnData,
  ConstDynamicTitleStr,
  RequestType,
  ActionType,
  NewRowData,
  DynamicItemInit,
  Status
} from './config/constant'

export default {
  components: {},
  data() {
    return {
      manage: sessionStorage.getItem('manage'),

      componentConfig: [],
      apiWaitingQuantity: 0, // 调用的api正在等待数
      forecastTemplate: '', // 预测模版 页面参数
      syncVersion: '', // 预测版本 页面参数
      isForecastTemplateDisabled: true, // 预测模版不可编辑
      ForecastTemplateOptions, // 预测模板 Options
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 10, // 当前每页数据量
        totalPages: 0, // 总页数
        pageSizes: [10, 50, 100, 200]
      },
      forecastPageCurrent: 1, // 预测表格 当前页码
      forecastRules: [], // 预测表格请求规则
      forecastCondition: 'and' // 过滤-规则-关系，默认为 "and"
    }
  },
  mounted() {
    this.forecastTemplate = this.$route.query.template
    this.syncVersion = this.$route.query.version
    // 获取采方预测信息列表
    this.postBuyerForecastQuery()
    // 初始化表头数据
    this.handleColumns({ titleList: [] })
  },
  beforeDestroy() {
    sessionStorage.removeItem('manage')
  },
  methods: {
    // 调整单元格的样式
    queryCellInfo() {
      // const { column, cell } = args;
      // if (
      //   dynamicTitle.includes(`${ConstDynamicTitleStr}${column.field}`) ||
      //   "forecastDataType" === column.field
      // ) {
      //   cell.classList.add("forecast-td");
      // }
    },
    // toolbar 按钮点击
    handleClickToolBar(args) {
      const { toolbar, grid, rules } = args
      const selectedRecords = grid.getSelectedRecords()
      const commonToolbar = [
        'ForecastAdd',
        'ForecastImport',
        'Filter',
        'Refresh',
        'refreshDataByLocal',
        'filterDataByLocal',
        'resetDataByLocal',
        'ForecastExport',
        'Setting'
      ]

      if (this.isEditing && toolbar.id !== 'refreshDataByLocal') {
        // 结束编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }

      if (selectedRecords.length == 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })

      if (toolbar.id === 'ForecastAdd') {
        // 新增
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id === 'ForecastDelete') {
        // 删除
        this.postBuyerForecastDelete(idList)
      } else if (toolbar.id === 'ForecastPublish') {
        // 发布
        this.postBuyerForecastPublish(idList)
      } else if (toolbar.id === 'ForecastConfirm') {
        // 确认
        this.postBuyerForecastConfirm(idList)
      } else if (toolbar.id === 'ForecastExport') {
        // 导出
        this.postBuyerForecastExport()
      } else if (toolbar.id === 'refreshDataByLocal') {
        // 刷新 采方-获取采方预测信息列表
        this.postBuyerForecastQuery()
      } else if (toolbar.id === 'filterDataByLocal') {
        // 筛选-过滤
        const { condition, rules: ruleList } = rules
        this.forecastCondition = condition
        this.forecastRules = ruleList
        // 采方-获取采方预测信息列表
        this.postBuyerForecastQuery()
      } else if (toolbar.id === 'resetDataByLocal') {
        // 筛选重置
        this.forecastCondition = 'and'
        this.forecastRules = []
        // 采方-获取采方预测信息列表
        this.postBuyerForecastQuery()
      }
    },
    // CellTool
    handleClickCellTool(e) {
      if (this.isEditing) {
        // 结束编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }

      if (e.tool.id === 'ForecastDelete') {
        // 删除
        this.postBuyerForecastDelete([e.data.id])
      } else if (e.tool.id === 'ForecastPublish') {
        // 发布
        this.postBuyerForecastPublish([e.data.id])
      } else if (e.tool.id === 'ForecastCancelPublish') {
        // 取消发布
        this.postBuyerForecastCancel([e.data.id])
      } else if (e.tool.id === 'ForecastConfirm') {
        // 确认
        this.postBuyerForecastConfirm([e.data.id])
      }
    },
    addNewRow() {
      const dynamicItem = {}
      dynamicTitle.forEach((itemTitle) => {
        const timeInfo = itemTitle.substring(ConstDynamicTitleStr.length)
        dynamicItem[itemTitle] = cloneDeep(DynamicItemInit)
        dynamicItem[itemTitle].timeInfo = timeInfo // 初始数据的timeInfo
      })
      return {
        ...dynamicItem,
        ...NewRowData
      }
    },
    // actionBegin 表格编辑生命周期
    actionBegin(args) {
      const { requestType, action, rowData } = args
      // console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args);
      if (requestType === RequestType.add) {
        // 开始行添加
        this.isEditing = true
        // 即将添加一行，赋值新增行的初始数据
        rowDataTemp.length = 0
        rowDataTemp.push(this.addNewRow())
        args.rowData = this.addNewRow()
        args.data = this.addNewRow()
      } else if (requestType === RequestType.save && action === ActionType.add) {
        // 即将保存新增时，将新增中编辑的数据付给组件
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === RequestType.beginEdit) {
        // 已确认的数据不可修改
        const disAbleEditStatus = [Status.confirmed]
        if (disAbleEditStatus.includes(rowData.status)) {
          args.cancel = true
          this.$toast({
            content: this.$t('此状态数据不可编辑'),
            type: 'warning'
          })
          return
        }
        // 开始行编辑
        this.isEditing = true
        // 即将编辑行，赋值当前行的数据
        rowDataTemp.length = 0
        rowDataTemp.push(rowData)
      }
    },
    // actionComplete 表格编辑生命周期
    actionComplete(args) {
      const { requestType, action, rowIndex, index } = args
      // console.log(
      //   `Complete,\nrequest: ${requestType}\naction: ${action}`,
      //   args
      // );
      if (requestType === RequestType.save && action == ActionType.edit) {
        // 完成编辑行
        this.isEditing = false
        // 通过 id 判断这条数据是否为新增请求后，报错，自动再次编辑的数据
        if (rowDataTemp[rowDataTemp.length - 1].id) {
          // 有 id，此数据为行编辑的数据
          this.postBuyerForecastSaveForecast({
            data: rowDataTemp[rowDataTemp.length - 1],
            rowIndex
          })
        } else {
          // 无 id，此数据为新增错误，再次编辑的数据
          this.postBuyerForecastBatchInsert({
            data: rowDataTemp[rowDataTemp.length - 1],
            rowIndex
          })
        }
      } else if (requestType === RequestType.save && action == ActionType.add) {
        // 完成新增行
        this.isEditing = false
        this.postBuyerForecastBatchInsert({
          data: rowDataTemp[rowDataTemp.length - 1],
          rowIndex: index
        })
      } else if (requestType === RequestType.sorting || requestType === RequestType.refresh) {
        // 丢弃编辑中状态
        this.isEditing = false
      }
    },
    // 采方-批量写入预测数据
    postBuyerForecastBatchInsert(args) {
      const { data, rowIndex } = args
      if (!this.isValidData(data)) {
        // 当出现错误时，指定行进入编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        return
      }
      const forecastInfo = rowDataToForecastInfo({
        dynamicTitleList: dynamicTitle,
        rowData: data
      })
      const params = {
        forecastInfo, //	预测信息
        buyerOrgId: data.buyerOrgId, // 采购组id
        buyerOrgName: data.buyerOrgName, // 采购组名称
        companyId: data.companyId, // 公司id
        companyName: data.companyName, // 公司名称
        factoryCode: data.factoryCode, // 工厂code
        factoryId: data.factoryId, // 工厂id
        factoryName: data.factoryName, // 工厂名称
        itemCode: data.itemCode, // 物料编码
        itemId: data.itemId, // 物料id
        itemName: data.itemName, // 物料名称
        manufacturer: data.manufacturer, // 制造商
        mrpArea: data.mrpArea, // MRP区域
        packagingManufacturer: data.packagingManufacturer, // 包装厂商
        packagingOrigin: data.packagingOrigin, // 包装产地
        percentage: data.percentage, // 配额
        planGroup: data.planGroup, // 计划组
        planGroupId: data.planGroupId, // 计划组id
        productRel: data.productRel, // 关联产品（机型、机芯）
        rawMaterialManufacturer: data.rawMaterialManufacturer, // 关键原材厂商
        rawMaterialOrigin: data.rawMaterialOrigin, // 关键原材产地
        specialUse: data.specialUse, // 制造商专用状
        storageQty: data.storageQty, // 已入库数量
        supplierCode: data.supplierCode, // 供应商code
        supplierId: data.supplierId, // 供应商id
        supplierName: data.supplierName, // 供应商名称
        // syncTime: data.syncTime, // 同步时间
        undeliveredOrderQty: data.undeliveredOrderQty, // 未交PO
        // version: data.version, // 版本号
        purRemark: data.purRemark // 采方备注
      }
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerForecastBatchInsert([params])
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 采方-获取采方预测信息列表
            this.postBuyerForecastQuery()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    // 采方-修改预测信息
    postBuyerForecastSaveForecast(args) {
      const { data, rowIndex } = args
      if (!this.isValidData(data)) {
        // 当出现错误时，指定行进入编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        return
      }
      const forecastInfoReq = rowDataToForecastInfo({
        dynamicTitleList: dynamicTitle,
        rowData: data
      })
      const params = {
        id: data?.id,
        purRemark: data?.purRemark,
        forecastInfoReq
      }
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerForecastSaveForecast(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 采方-获取采方预测信息列表
            this.postBuyerForecastQuery()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    // 校验数据
    isValidData(data) {
      const { planGroupId, buyerOrgId, itemCode, factoryCode, supplierCode } = data
      let valid = false
      if (!itemCode) {
        // 物料代码
        this.$toast({ content: this.$t('物料不可为空'), type: 'warning' })
      } else if (!factoryCode) {
        // 工厂代码
        this.$toast({ content: this.$t('工厂不可为空'), type: 'warning' })
      } else if (!supplierCode) {
        // 供应商代码
        this.$toast({ content: this.$t('供应商不可为空'), type: 'warning' })
      } else if (!planGroupId) {
        // 计划组
        this.$toast({ content: this.$t('计划组不可为空'), type: 'warning' })
      } else if (!buyerOrgId) {
        // 采购组
        this.$toast({ content: this.$t('采购组不可为空'), type: 'warning' })
      } else {
        valid = true
      }

      return valid
    },
    // 采方-获取采方预测信息列表
    postBuyerForecastQuery() {
      this.componentConfig = []
      // 处理查询条件
      this.forecastRules = this.handleRulesFormat(this.forecastRules)
      const params = {
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        },
        condition: this.forecastCondition,
        rules: [...this.forecastRules],
        forecastTemplate: this.forecastTemplate, // 预测模板
        defaultRules: [
          {
            // 版本
            field: 'syncVersion',
            operator: 'equal',
            value: this.syncVersion
          }
        ]
      }
      this.apiStartLoading()
      if (this.manage === 'kt') {
        this.$API.predictCollaboration
          .postBuyerKtForecastQuery(params)
          .then((res) => {
            this.apiEndLoading()
            this.$store.commit('startLoading') // 因为表格渲染比较耗时，在handleDataBound中结束
            if (res?.code == 200) {
              const total = res?.data?.forecasts?.total || 0
              this.forecastPageSettings.totalPages = Math.ceil(
                Number(total) / this.forecastPageSettings.pageSize
              )
              const records = res?.data?.forecasts?.records || [] // 表格数据
              const titleList = res?.data?.titleList || [] // 动态表头数据
              // 处理表头数据
              this.handleColumns({ titleList })
              // 处理表数据
              this.handleDataSource({ records, titleList })
              const config = {
                useToolTemplate: false, // 不使用预置(新增、编辑、删除)
                useBaseConfig: false, // 使用组件中的toolbar配置
                toolbar: [Toolbar, ['Filter', 'Refresh', 'Setting']], // 此页面为动态数据关联的动态表格，故不可设置字段显示隐藏
                gridId: this.$tableUUID.predictCollaboration.predictView.list,
                grid: {
                  gridLines: 'Both',

                  allowPaging: false, // 不分页
                  allowReordering: false, // 不可拖动排序 因为预测数据是动态获取到的，表格记忆排序不能处理
                  // frozenColumns: 1, // 冻结第一列 使用此设置表格数据将不可见，已使用 frozenFistColumns 实现
                  columnData: forecastColumnData,
                  dataSource: forecastDataSource,
                  editSettings: EditSettings
                }
              }
              this.componentConfig.push(config)
            }
          })
          .catch(() => {
            this.apiEndLoading()
          })
      } else {
        this.$API.predictCollaboration
          .postBuyerForecastQuery(params)
          .then((res) => {
            this.apiEndLoading()
            this.$store.commit('startLoading') // 因为表格渲染比较耗时，在handleDataBound中结束
            if (res?.code == 200) {
              const total = res?.data?.forecasts?.total || 0
              this.forecastPageSettings.totalPages = Math.ceil(
                Number(total) / this.forecastPageSettings.pageSize
              )
              const records = res?.data?.forecasts?.records || [] // 表格数据
              const titleList = res?.data?.titleList || [] // 动态表头数据
              // 处理表头数据
              this.handleColumns({ titleList })
              // 处理表数据
              this.handleDataSource({ records, titleList })
              const config = {
                useToolTemplate: false, // 不使用预置(新增、编辑、删除)
                useBaseConfig: false, // 使用组件中的toolbar配置
                toolbar: [Toolbar, ['Filter', 'Refresh', 'Setting']], // 此页面为动态数据关联的动态表格，故不可设置字段显示隐藏
                gridId: this.$tableUUID.predictCollaboration.predictView.list,
                grid: {
                  gridLines: 'Both',

                  allowPaging: false, // 不分页
                  allowReordering: false, // 不可拖动排序 因为预测数据是动态获取到的，表格记忆排序不能处理
                  // frozenColumns: 1, // 冻结第一列 使用此设置表格数据将不可见，已使用 frozenFistColumns 实现
                  columnData: forecastColumnData,
                  dataSource: forecastDataSource,
                  editSettings: EditSettings
                }
              }
              this.componentConfig.push(config)
            }
          })
          .catch(() => {
            this.apiEndLoading()
          })
      }
    },
    // 处理查询条件 rules
    handleRulesFormat(data) {
      const rules = cloneDeep(data)
      forecastTypeShow.length = 0
      if (rules?.length) {
        rules.forEach((itemRule, index) => {
          if (itemRule.field === 'forecastType') {
            // 类型
            if (itemRule.value?.length > 0) {
              // 勾选了类型字段中的选项
              itemRule.value.forEach((item) => {
                forecastTypeShow.push(item)
              })
            } else {
              // 没有勾选，默认显示全部
              forecastTypeShow.length = 0
            }
            rules.splice(index, 1)
          }
        })
      }
      return rules
    },
    // 处理表头数据
    handleColumns(data) {
      const { titleList } = data
      forecastColumnData.length = 0 // 清空表头数据
      dynamicTitle.length = 0 // 清空 Template 使用的表头列表
      const titleListColumnData = []
      titleList.forEach((item, index) => {
        dynamicTitle.push(`${ConstDynamicTitleStr}${titleList[index]}`)
        titleListColumnData.push({
          fieldCode: item,
          fieldName: item,
          isForecast: true, // 预测数据的标志
          colIndex: index, // 列的 index
          ignore: true // 不出现在筛选字段中
        })
      })
      // 固定的表头
      const forecastColumns = formatTableColumnData(ForecastColumnData)
      // 动态的日期表头
      const titleListColumns = formatTableColumnData(titleListColumnData)
      // 合并表头数组，然后 push 到 forecastColumnData 中
      const columns = [].concat(forecastColumns).concat(titleListColumns)
      let manage = sessionStorage.getItem('manage')
      if (manage === 'kt') {
        let obj = [
          'manufacturer',
          'productRel',
          'rawMaterialManufacturer',
          'rawMaterialOrigin',
          'packagingManufacturer',
          'packagingOrigin',
          'specialUse'
        ]
        obj.forEach((ob) => {
          columns.splice(
            columns.findIndex((item) => {
              return item.fieldCode === ob
            }),
            1
          )
        })
        columns.splice(
          columns.findIndex((item) => {
            return item.fieldCode === 'productRel'
          }),
          1
        )
      }
      if (manage === 'bd') {
        let obj = ['purchaseAheadTime', 'noQuotasFlag']
        obj.forEach((ob) => {
          columns.splice(
            columns.findIndex((item) => {
              return item.fieldCode === ob
            }),
            1
          )
        })
        // columns.splice(
        //   columns.findIndex((item) => {
        //     return item.fieldCode === "productRel";
        //   }),
        //   1
        // );
      }
      columns.forEach((item) => {
        forecastColumnData.push(item)
      })
    },
    // 处理表数据
    handleDataSource(data) {
      const { records, titleList } = data
      forecastDataSource.length = 0 // 清空表格数据

      const dataSource = cloneDeep(formatTableDataSource({ records, titleList }))
      dataSource.forEach((item) => {
        // 将每一项 push 到 forecastDataSource 中
        forecastDataSource.push(item)
      })
    },
    forecastTemplateChange(e) {
      // 获取预测数据
      this.forecastTemplate = e.value
      this.postBuyerForecastQuery()
    },
    // 采方删除预测信息
    postBuyerForecastDelete(params) {
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerForecastDelete(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 采方-获取采方预测信息列表
            this.postBuyerForecastQuery()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方发布预测信息接口
    postBuyerForecastPublish(params) {
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerForecastPublish(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 采方-获取采方预测信息列表
            this.postBuyerForecastQuery()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方-导出
    postBuyerForecastExport() {
      const params = {
        queryBuilderDTO: {
          page: {
            size: this.forecastPageSettings.pageSize,
            current: this.forecastPageCurrent
          }
        },
        condition: this.forecastCondition,
        rules: [...this.forecastRules],
        forecastTemplate: this.forecastTemplate, // 预测模板
        defaultRules: [
          {
            // 版本
            field: 'syncVersion',
            operator: 'contains',
            value: this.syncVersion
          }
        ]
      }
      this.apiStartLoading()
      this.$API.predictCollaboration.postBuyerForecastExport(params).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 采方确认信息
    postBuyerForecastConfirm(params) {
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerForecastConfirm(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 采方-获取采方预测信息列表
            this.postBuyerForecastQuery()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方取消发布接口
    postBuyerForecastCancel(params) {
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerForecastCancel(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 采方-获取采方预测信息列表
            this.postBuyerForecastQuery()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.postBuyerForecastQuery()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.postBuyerForecastQuery()
    },
    // 表格数据绑定完成
    handleDataBound() {
      this.$store.commit('endLoading')
    },
    goBack() {
      // 返回 预测历史列表-采方
      this.$router.push({
        name: 'predict-history'
      })
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  // padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  .header-box {
    // border-bottom: 1px solid #e6e9ed;
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
  }
}
.forecast-template {
  width: 250px;
  padding-bottom: 8px;
  .forecast-template-select {
    width: calc(250px - 70px);
  }
}
/deep/ .column-tool {
  margin-top: 8px;
}
/deep/ .template-svg {
  cursor: pointer;
  font-size: 12px;
  color: #6386c1;

  &:nth-child(n + 2) {
    margin-left: 10px;
  }
}

/deep/ .grid-edit-column {
  padding: 12px 0;
}

// 预测数据行
/deep/ .forecast-item {
  height: 40px;
  padding: 7px;
  line-height: 26px;
  border: 1px solid #e8e8e8;
}
/deep/ .inputSy {
  border: 1px solid #e8e8e8;
}
// 预测数据高亮数据
/deep/ .forecast-highlight {
  color: #ed5836;
  background-color: #fdeeea;
  border: 1px solid #ed5836;
}
/deep/ .frozenColumns {
  .template-wrap {
    .e-grid .e-table {
      & thead th:first-child {
        position: sticky;
        left: 0px;
        z-index: 1;
      }
      & thead th:nth-child(2) {
        position: sticky;
        left: 50px;
        z-index: 1;
      }
      & thead th:nth-child(3) {
        position: sticky;
        left: 120px;
        z-index: 1;
      }
      & thead th:nth-child(4) {
        position: sticky;
        left: 250px;
        z-index: 1;
      }
      & thead th:nth-child(5) {
        position: sticky;
        left: 350px;
        z-index: 1;
      }
      & thead th:nth-child(6) {
        position: sticky;
        left: 450px;
        z-index: 1;
      }
      & tbody td:first-child {
        position: sticky;
        left: 0px;
        z-index: 1;
        border-right: 1px solid var(--plugin-dg-shadow-color);
        background-color: #fff;
      }
      & tbody td:nth-child(2) {
        position: sticky;
        left: 50px;
        z-index: 1;
        border-right: 1px solid var(--plugin-dg-shadow-color);
        background-color: #fff;
      }
      & tbody td:nth-child(3) {
        position: sticky;
        left: 120px;
        z-index: 1;
        border-right: 1px solid var(--plugin-dg-shadow-color);
        background-color: #fff;
      }
      & tbody td:nth-child(4) {
        position: sticky;
        left: 250px;
        z-index: 1;
        border-right: 1px solid var(--plugin-dg-shadow-color);
        background-color: #fff;
      }
      & tbody td:nth-child(5) {
        position: sticky;
        left: 350px;
        z-index: 1;
        border-right: 1px solid var(--plugin-dg-shadow-color);
        background-color: #fff;
      }
      & tbody td:nth-child(6) {
        position: sticky;
        left: 450px;
        z-index: 1;
        border-right: 1px solid var(--plugin-dg-shadow-color);
        background-color: #fff;
      }
    }
  }
}
/deep/ .mt-data-grid {
  .e-gridcontent {
    .e-table {
      tbody {
        tr:nth-child(2n-1) {
          background: #f6f7fb;
          td:first-child {
            position: sticky;
            left: 0px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #f6f7fb;
          }
          td:nth-child(2) {
            position: sticky;
            left: 50px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #f6f7fb;
          }
          td:nth-child(3) {
            position: sticky;
            left: 120px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #f6f7fb;
          }
          td:nth-child(4) {
            position: sticky;
            left: 250px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #f6f7fb;
          }
          td:nth-child(5) {
            position: sticky;
            left: 350px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #f6f7fb;
          }
          td:nth-child(6) {
            position: sticky;
            left: 450px;
            z-index: 1;
            border-right: 1px solid var(--plugin-dg-shadow-color);
            background-color: #f6f7fb;
          }
        }
        // tr:nth-child(2n) {
        //   background: #fff;
        // }
      }
    }
  }
}
// 表格容器
#forecast-manage-table-container {
  position: relative;

  // 表格数据视图
  /deep/ .e-gridcontent > .e-content {
    height: 0px;
    transition: height 0.5s ease;
  }

  // 分页
  /deep/ .mt-pagertemplate {
    margin: 10px 0 10px !important;
  }
}
// 表格预测类型 fieldCode: "forecastDataType"
// /deep/ .forecast-type-box {
//   border-left: 1px solid #e8e8e8;
// }
// /deep/ .forecast-item:nth-child(-n + 6) {
//   border-bottom: 1px solid #e8e8e8;
// }
</style>
