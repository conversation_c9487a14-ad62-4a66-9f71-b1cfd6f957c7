import { i18n } from '@/main.js'

export const ConstDynamicTitleStr = 'title_' // 动态表格数据的 key 标识

// 业务组类型数字字典编码
export const BusinessGroupTypeDictCode = 'BUSINESSGROUP'

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 预测数据类型cell
export const ForecastDataTypeCell = [
  {
    title: i18n.t('D1(预测)')
  },
  {
    title: i18n.t('D2(订单)')
  },
  {
    title: i18n.t('D(原始需求)')
  },
  {
    title: i18n.t('P(需求量)')
  },
  {
    title: i18n.t('C(承诺量)')
  },
  {
    title: i18n.t('Gap(差异)')
  },
  {
    title: 'F'
  }
]

// 类型字段，快捷搜索，下拉选项
export const ForecastTypeDataSource = [
  ForecastDataTypeCell[0].title, // D1(预测)
  ForecastDataTypeCell[1].title, // D2(订单)
  ForecastDataTypeCell[2].title, // D(原始需求)
  ForecastDataTypeCell[3].title, // P(需求量)
  ForecastDataTypeCell[4].title, // C(承诺量)
  ForecastDataTypeCell[5].title, // Gap(差异)
  ForecastDataTypeCell[6].title // F
]

// 表格编辑 的 RequestType
export const RequestType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  sorting: 'sorting',
  refresh: 'refresh'
}

// 表格编辑 的 action
export const ActionType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  edit: 'edit'
}

// 行编辑触发的 change 事件类型
export const ComponentChangeType = {
  code: 'code', // 例：下拉框选择“供应商”，供应商编码（code）要被修改
  link: 'link' // 例：计划组：根据物料+工厂+计划组的业务组类型 code 带出
}

// 预测类型对应后端在表头增加的标志
export const ForecastType = {
  day: 'D',
  week: 'W',
  month: 'M'
}

// 动态行初始化数据
export const DynamicItemInit = {
  timeInfo: '', // 预测时间
  forecastNum: '', // D1预测
  orderNum: '', // D2订单
  total: '', // D
  buyerNum: '', // P
  supplierNum: '', // C
  planGroupNum: '0' // F
}

// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  status: 0, // 状态 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
  itemName: '', // 物料名称
  itemCode: '', // 物料编码
  factoryName: '', // 工厂
  factoryCode: '', // 工厂代码
  planGroup: '', // 计划组
  buyerOrgName: '', // 采购组
  productRel: '', // 机型/机芯
  companyName: '', // 公司
  mrpArea: '', // 计划区域
  percentage: '', // 配额
  supplierName: '', // 供应商名称
  supplierCode: '', // 供应商编码
  manufacturer: '', // 制造商名称
  rawMaterialManufacturer: '', // 关键原材厂商
  rawMaterialOrigin: '', // 关键原材产地
  packagingManufacturer: '', // 包装厂商
  packagingOrigin: '', // 包装产地
  specialUse: '', // 制造商专用状况
  undeliveredOrderQty: '', // 未交PO
  storageQty: '', // 已入库数量
  purRemark: '' // 采方备注
}

// toolbar 按钮配置
export const Toolbar = [
  {
    id: 'ForecastExport',
    prefixIcon: 'mt-icons mt-icon-icon_solid_export',
    text: i18n.t('导出'),
    tooltipText: i18n.t('导出'),
    icon: 'icon_solid_export',
    title: i18n.t('导出')
  }
]

// 表格行按钮
export const CellTools = []

// 预测表格 EditSettings
export const EditSettings = {
  allowEditing: false,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
} // 编辑设置

// 预测模板 2：短周期；1：中周期；0：长周期；
export const ForecastTemplate = {
  long: 0, // 长周期
  medium: 1, // 中周期
  short: 2 // 短周期
}
// 预测模板 text
export const ForecastTemplateText = {
  [ForecastTemplate.long]: i18n.t('长周期'),
  [ForecastTemplate.medium]: i18n.t('中周期'),
  [ForecastTemplate.short]: i18n.t('短周期')
}
// 预测模板 Options
export const ForecastTemplateOptions = [
  {
    text: ForecastTemplateText[ForecastTemplate.long],
    value: ForecastTemplate.long
  },
  {
    text: ForecastTemplateText[ForecastTemplate.medium],
    value: ForecastTemplate.medium
  },
  {
    text: ForecastTemplateText[ForecastTemplate.short],
    value: ForecastTemplate.short
  }
]

// 状态 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
export const Status = {
  new: 0, // 新建
  modified: 1, // 已修改
  pendingFeedback: 2, // 待反馈
  feedbackNormal: 3, // 反馈正常
  feedbackAbnormal: 4, // 反馈异常
  confirmed: 5 // 已确认
}
// 状态 text
export const StatusText = {
  [Status.new]: i18n.t('新建'),
  [Status.modified]: i18n.t('已修改'),
  [Status.pendingFeedback]: i18n.t('待反馈'),
  [Status.feedbackNormal]: i18n.t('反馈正常'),
  [Status.feedbackAbnormal]: i18n.t('反馈异常'),
  [Status.confirmed]: i18n.t('已确认')
}
// 状态 class
export const StatusClass = {
  [Status.new]: 'col-active',
  [Status.modified]: 'col-active',
  [Status.pendingFeedback]: 'col-active', // col-published
  [Status.feedbackNormal]: 'col-active', // col-normal
  [Status.feedbackAbnormal]: 'col-active', // col-abnormal
  [Status.confirmed]: 'col-active' // col-inactive
}

// 预测表格列数据
export const ForecastColumnData = [
  {
    fieldCode: 'checkBox' // 不可编辑
  },
  {
    fieldCode: 'status', // 自动生成，不可编辑
    fieldName: i18n.t('状态'), // 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
    sticky: true,
    allowResizing: false, //是否允许调整大小 列拖拽
    allowReordering: false
  },
  {
    fieldCode: 'factoryCode', // 物料带出对应的主数据中的工厂，新增状态时可编辑，必须
    fieldName: i18n.t('工厂'),
    sticky: true,
    allowResizing: false, //是否允许调整大小 列拖拽
    allowReordering: false
    // factoryName
    // factoryId
  },
  {
    fieldCode: 'mrpArea', // 预测报表行的计划区域，新增状态时可编辑，文本框
    fieldName: i18n.t('MRP区域'),
    sticky: true,
    allowResizing: false, //是否允许调整大小 列拖拽
    allowReordering: false
  },
  {
    fieldCode: 'itemCode', // 主数据中的物料，新增状态时可编辑，必须
    fieldName: i18n.t('物料'),
    sticky: true,
    allowResizing: false, //是否允许调整大小 列拖拽
    allowReordering: false
    // itemName
    // itemId
  },
  {
    fieldCode: 'supplierCode', // 供应商：主数据中的供应商，新增状态时可编辑，下拉框模糊搜索，模糊搜索 必须
    fieldName: i18n.t('供应商'),
    sticky: true,
    allowResizing: false, //是否允许调整大小 列拖拽
    allowReordering: false
    // supplierName
    // supplierId
  },
  {
    fieldCode: 'thePrimaryKey'
  },
  {
    fieldCode: 'serialNumber', // 前端定义,不可编辑
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'syncVersion', // 自动生成,不可编辑 大版本， publishVersion 小版本
    fieldName: i18n.t('版本')
  },

  {
    fieldCode: 'planGroupCode', // 根据物料+工厂+计划组的业务组类型code带出，不可编辑，必须
    fieldName: i18n.t('计划组')
    // planGroup 计划组name
    // planGroupId 计划组id
  },
  {
    fieldCode: 'buyerOrgCode', // 计划组 code-name显示 根据物料选择带出数据源 不可模糊搜索（API不支持），必须
    fieldName: i18n.t('采购组')
    // buyerOrgId 采购组id
    // buyerOrgName 采购组name
  },
  {
    fieldCode: 'companyCode', // 通过登录用户信息获取当前租户的公司列表，新增状态时可编辑，下拉框
    fieldName: i18n.t('公司')
    // companyId
    // companyName
  },
  {
    fieldCode: 'productRel', // 物料所属的机型/机芯，新增状态时可编辑，文本框
    fieldName: i18n.t('机型/机芯')
  },

  {
    fieldCode: 'percentage', // 预测数据对应供应商的配额，新增状态时可编辑，文本框
    fieldName: i18n.t('配额')
  },
  {
    fieldCode: 'purchaseAheadTime', // 预测数据对应供应商的配额，新增状态时可编辑，文本框
    fieldName: i18n.t('采购提前期')
  },
  {
    fieldCode: 'noQuotasFlag', // 预测数据对应供应商的配额，新增状态时可编辑，文本框
    fieldName: i18n.t('无配额标识')
  },
  {
    fieldCode: 'manufacturer', // 新增状态时可编辑，文本框
    fieldName: i18n.t('制造商名称')
  },
  {
    fieldCode: 'rawMaterialManufacturer', // 新增状态时可编辑，文本框
    fieldName: i18n.t('关键原材厂商')
  },
  {
    fieldCode: 'rawMaterialOrigin', // 新增状态时可编辑，文本框
    fieldName: i18n.t('关键原材产地')
  },
  {
    fieldCode: 'packagingManufacturer', // 新增状态时可编辑，文本框
    fieldName: i18n.t('包装厂商')
  },
  {
    fieldCode: 'packagingOrigin', // 新增状态时可编辑，文本框
    fieldName: i18n.t('包装产地')
  },
  {
    fieldCode: 'specialUse', // 新增状态时可编辑，文本框
    fieldName: i18n.t('制造商专用状况')
  },
  {
    fieldCode: 'undeliveredOrderQty', // 新增状态时可编辑，数字
    fieldName: i18n.t('未交PO')
  },
  {
    fieldCode: 'overtimeDemand', // 不可编辑
    fieldName: i18n.t('过期需求')
  },
  {
    fieldCode: 'storageQty', // 新增状态时可编辑，数字
    fieldName: i18n.t('已入库数量')
  },
  {
    fieldCode: 'purRemark', // 计划组可填写备注，新增状态时可编辑，文本框
    fieldName: i18n.t('采方备注')
  },
  {
    fieldCode: 'supRemark', // 不可编辑
    fieldName: i18n.t('供应商备注')
  },
  {
    fieldCode: 'remainFeedbackTime', // 按照配置后，发布给供应商后显示，不可编辑
    fieldName: i18n.t('剩余反馈时间（H）')
  },
  {
    fieldCode: 'total1', // 前端写死 D1(预测); D2(订单); D;C;P;F;
    fieldName: i18n.t('N+1月汇总')
  },
  {
    fieldCode: 'total2', // 前端写死 D1(预测); D2(订单); D;C;P;F;
    fieldName: i18n.t('N+2月汇总')
  },
  {
    fieldCode: 'total3', // 前端写死 D1(预测); D2(订单); D;C;P;F;
    fieldName: i18n.t('N+3月汇总')
  },
  {
    fieldCode: 'total4', // 前端写死 D1(预测); D2(订单); D;C;P;F;
    fieldName: i18n.t('N+4月汇总')
  },
  {
    fieldCode: 'total5', // 前端写死 D1(预测); D2(订单); D;C;P;F;
    fieldName: i18n.t('N+5月汇总')
  },
  {
    fieldCode: 'total6', // 前端写死 D1(预测); D2(订单); D;C;P;F;
    fieldName: i18n.t('N+6月汇总')
  },
  {
    fieldCode: 'total7', // 前端写死 D1(预测); D2(订单); D;C;P;F;
    fieldName: i18n.t('N+7月汇总')
  },
  {
    fieldCode: 'monthTotalNum', // 前端写死 D1(预测); D2(订单); D;C;P;F;
    fieldName: i18n.t('月汇总')
  },
  {
    fieldCode: 'weekData', // 前端写死 D1(预测); D2(订单); D;C;P;F;
    fieldName: i18n.t('周预测')
  },
  {
    fieldCode: 'forecastType', // 前端写死 D1(预测); D2(订单); D;C;P;F;
    fieldName: i18n.t('类型')
  }
]
