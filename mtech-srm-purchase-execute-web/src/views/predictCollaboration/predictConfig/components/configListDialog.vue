<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" autocomplete="off">
      <!-- 任务类型 定时发布/定时反馈 -->
      <mt-form-item prop="operateType" :label="$t('任务类型')" class="">
        <mt-select
          v-model="formData.operateType"
          :data-source="OperateTypeOptions"
          :show-clear-button="true"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <!-- startTime-endTime -->
      <mt-form-item prop="dateRange" :label="$t('日期范围')" class="">
        <mt-date-range-picker
          v-model="formData.dateRange"
          :show-clear-button="true"
          :allow-edit="false"
          :placeholder="$t('请选择')"
        ></mt-date-range-picker>
      </mt-form-item>
      <!-- 配置方式 计划组+工厂+供应商/采购组+工厂+供应商/工厂 -->
      <mt-form-item prop="configGroupType" :label="$t('配置方式')" class="">
        <mt-select
          v-model="formData.configGroupType"
          :data-source="ConfigGroupTypeOptions"
          :show-clear-button="true"
          @change="configGroupTypeChange"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <!-- 配置类型 固定时间/超时时间 -->
      <mt-form-item prop="configType" :label="$t('配置类型')" class="">
        <mt-select
          v-model="formData.configType"
          :data-source="ConfigTypeOptions"
          :show-clear-button="true"
          @change="configTypeChange"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <!-- 显示：配置方式=计划组+工厂+供应商 -->
      <mt-form-item
        v-if="formData.configGroupType === ConfigGroupType.plan"
        prop="planGroupCode"
        :label="$t('计划组')"
        class=""
      >
        <debounce-filter-select
          v-model="formData.planGroupCode"
          :request="getPlanGroupList"
          :data-source="planGroupOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'groupCode' }"
          :value-template="planGroupValueTemplate"
          @change="planGroupCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <!-- 显示：配置方式=采购组+工厂+供应商 -->
      <mt-form-item
        v-if="formData.configGroupType === ConfigGroupType.purchase"
        prop="buyerOrgCode"
        :label="$t('采购组')"
        class=""
      >
        <debounce-filter-select
          v-model="formData.buyerOrgCode"
          :request="getBuyerOrgList"
          :data-source="buyerOrgOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'groupCode' }"
          :value-template="buyerOrgValueTemplate"
          @change="buyerOrgCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="siteCode" :label="$t('工厂')" class="">
        <debounce-filter-select
          v-model="formData.siteCode"
          :request="postSiteFuzzyQuery"
          :data-source="siteOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'siteCode' }"
          :value-template="siteCodeValueTemplate"
          @change="siteCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <!-- 模糊搜索供应商编号-名称 显示：配置方式=采购组+工厂+供应商 || 配置方式=计划组+工厂+供应商 -->
      <mt-form-item
        v-if="
          formData.configGroupType === ConfigGroupType.purchase ||
          formData.configGroupType === ConfigGroupType.plan
        "
        prop="supplierCode"
        :label="$t('供应商')"
        class=""
      >
        <debounce-filter-select
          v-model="formData.supplierCode"
          :request="getSupplier"
          :data-source="supplierOptions"
          :fields="{ text: 'theCodeName', value: 'supplierCode' }"
          :value-template="supplierCodeValueTemplate"
          :show-clear-button="true"
          :allow-filtering="true"
          @change="supplierCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <!-- 前端定义 循环时间，按周，显示：配置类型=固定时间 -->
      <mt-form-item
        v-if="formData.configType === ConfigType.fixed"
        prop="circulationTime"
        :label="$t('循环时间')"
        class=""
      >
        <select-weeks-time
          v-model="formData.circulationTime"
          :week-options="WeekOptions"
        ></select-weeks-time>
      </mt-form-item>
      <!-- 前端定义 两位小数，显示：配置类型=超时时间 -->
      <mt-form-item
        prop="overtimeTime"
        v-if="formData.configType === ConfigType.exceed"
        :label="$t('超时时间（H）')"
        class=""
      >
        <mt-input-number
          :min="0"
          :precision="2"
          :show-clear-button="true"
          :show-spin-button="false"
          @keydown.native="numberInputOnKeyDown"
          v-model="formData.overtimeTime"
          :placeholder="$t('请输入')"
        ></mt-input-number>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import {
  DialogActionType,
  OperateTypeOptions,
  ConfigGroupTypeOptions,
  ConfigGroupType,
  ConfigTypeOptions,
  ConfigType,
  WeekOptions,
  Type
} from '../config/constant'
import {
  dateRangeBySelectData,
  circulationTimeBySelectData,
  overtimeTimeBySelectData,
  formatDateRange,
  formDataToTimeValue
} from '../config/index'
import SelectWeeksTime from '@/components/selectWeeksTime'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { numberInputOnKeyDown } from '@/utils/utils'

export default {
  components: {
    SelectWeeksTime,
    DebounceFilterSelect
  },
  data() {
    // 日期范围
    const dateRangeValidator = (rule, value, callback) => {
      if (!value.end && !value.start) {
        callback(new Error(this.$t('请选择日期范围')))
      } else {
        this.$refs.ruleForm.clearValidate(['dateRange'])
        callback()
      }
    }
    // 循环时间
    const circulationTimeValidator = (rule, value, callback) => {
      if (this.formData.configType == ConfigType.fixed) {
        // 配置类型 == 固定时间
        if ((!value.time && !value.week) || (!value.time && value?.week?.length == 0)) {
          callback(new Error(this.$t('请选择循环时间')))
        } else if (!value.time || !value.week || value?.week?.length == 0) {
          callback(new Error(this.$t('请选择完整的循环时间')))
        } else {
          this.$refs.ruleForm.clearValidate(['circulationTime'])
          callback()
        }
      } else {
        this.$refs.ruleForm.clearValidate(['circulationTime'])
        callback()
      }
    }
    // 超时时间
    const overtimeTimeValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入超时时间')))
      } else {
        this.$refs.ruleForm.clearValidate(['overtimeTime'])
        callback()
      }
    }

    return {
      numberInputOnKeyDown,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      planGroupOptions: [], // 计划组 下列选项
      planGroupValueTemplate: codeNameColumn({
        firstKey: 'groupCode',
        secondKey: 'groupName'
      }), // 计划组
      buyerOrgOptions: [], // 采购组 下列选项
      buyerOrgValueTemplate: codeNameColumn({
        firstKey: 'groupCode',
        secondKey: 'groupName'
      }), // 采购组
      siteOptions: [], // 工厂 下列选项
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂 下拉框模板
      siteCodeItemTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂 下拉框模板
      supplierOptions: [], // 供应商 下列选项
      supplierCodeValueTemplate: codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      }), // 供应商 下拉框模板
      supplierCodeItemTemplate: codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      }), // 供应商 下拉框模板
      OperateTypeOptions, // 任务类型 下列选项
      ConfigGroupType, // 配置方式
      ConfigGroupTypeOptions, // 配置方式 下列选项
      ConfigType, // 配置类型
      ConfigTypeOptions, // 配置类型 下列选项
      dialogTitle: '',
      selectData: null, // 当前编辑的数据
      WeekOptions,
      rules: {
        // 任务类型
        operateType: [
          {
            required: true,
            message: this.$t('请选择任务类型'),
            trigger: 'blur'
          }
        ],
        // 配置方式
        configGroupType: [
          {
            required: true,
            message: this.$t('请选择配置方式'),
            trigger: 'blur'
          }
        ],
        // 配置类型
        configType: [
          {
            required: true,
            message: this.$t('请选择配置类型'),
            trigger: 'blur'
          }
        ],
        // 计划组
        planGroupCode: [{ required: true, message: this.$t('请选择计划组'), trigger: 'blur' }],
        // 采购组
        buyerOrgCode: [{ required: true, message: this.$t('请选择采购组'), trigger: 'blur' }],
        // 工厂
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        // 供应商
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }],
        // 日期范围
        dateRange: [{ required: true, validator: dateRangeValidator, trigger: 'blur' }],
        // 循环时间
        circulationTime: [
          {
            required: true,
            validator: circulationTimeValidator,
            trigger: 'blur'
          }
        ],
        // 超时时间
        overtimeTime: [{ required: true, validator: overtimeTimeValidator, trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      actionType: DialogActionType.Add, // 默认类型：新增
      formData: {
        id: '',
        operateType: '', // 任务类型 定时发布/反馈
        configGroupType: '', // 配置方式
        configType: '', // 配置类型 配置类型:固定时间-1,超时时间-2

        planGroupName: '', // 计划组
        planGroupId: '', // 计划组id
        planGroupCode: '', // 计划组编码

        buyerOrgName: '', // 采购组
        buyerOrgId: '', // 采购组id
        buyerOrgCode: '', // 采购组编码

        siteName: '', // 工厂
        siteId: '', // 工厂id
        siteCode: '', // 工厂编码

        supplierName: '', // 供应商
        supplierId: '', // 供应商id
        supplierCode: '', // 供应商编码

        dateRange: {}, // 日期范围

        circulationTime: {}, // 前端定义 循环时间 timeValue.week timeValue.timeFormat
        overtimeTime: '' // 前端定义 超时时间 timeValue.hour
      }
    }
  },
  mounted() {
    // 获取主数据-采购组
    this.getBuyerOrgList({ text: undefined })
    // 获取主数据-计划组
    this.getPlanGroupList({ text: undefined })
    // 获取主数据-工厂
    this.postSiteFuzzyQuery({ text: undefined })
    // 主数据 获取供应商
    this.getSupplier({ text: undefined })
  },

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, selectData } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.actionType = actionType // 弹框模式
      this.selectData = selectData // 行数据
      this.formData = this.initForm(selectData)
      // 编辑时获取所选择的下拉数据源
      this.handleGetSelectOptionsDuringEdit(selectData)
    },
    initForm(selectData) {
      let formData = null
      this.$refs.ruleForm.clearValidate()
      if (this.actionType === DialogActionType.Add) {
        // 新增
        formData = {
          id: '',
          operateType: '', // 任务类型 定时发布/反馈
          configGroupType: '', // 配置方式
          configType: '', // 配置类型 配置类型:固定时间-1,超时时间-2

          planGroupName: '', // 计划组
          planGroupId: '', // 计划组id
          planGroupCode: '', // 计划组编码

          buyerOrgName: '', // 采购组
          buyerOrgId: '', // 采购组id
          buyerOrgCode: '', // 采购组编码

          siteName: '', // 工厂
          siteId: '', // 工厂id
          siteCode: '', // 工厂编码

          supplierName: '', // 供应商
          supplierId: '', // 供应商id
          supplierCode: '', // 供应商编码

          dateRange: {}, // 日期范围

          circulationTime: {}, // 前端定义 循环时间 timeValue.week timeValue.timeFormat
          overtimeTime: '' // 前端定义 超时时间 timeValue.hour
        }
      } else if (this.actionType === DialogActionType.Edit) {
        // 编辑
        const circulationTime = circulationTimeBySelectData({
          ...selectData
        })
        const overtimeTime = overtimeTimeBySelectData({
          ...selectData
        })
        const dateRange = dateRangeBySelectData({
          ...selectData
        })
        formData = {
          id: selectData.id,
          operateType: selectData.operateType, // 任务类型
          configGroupType: selectData.configGroupType, // 配置方式
          configType: selectData.configType, // 配置类型

          planGroupName: selectData.planGroupName, // 计划组
          planGroupId: selectData.planGroupId, // 计划组id
          planGroupCode: selectData.planGroupCode, // 计划组编码

          buyerOrgName: selectData.buyerOrgName, // 采购组
          buyerOrgId: selectData.buyerOrgId, // 采购组id
          buyerOrgCode: selectData.buyerOrgCode, // 采购组编码

          siteName: selectData.siteName, // 工厂
          siteId: selectData.siteId, // 工厂id
          siteCode: selectData.siteCode, // 工厂编码

          supplierName: selectData.supplierName, // 供应商
          supplierId: selectData.supplierId, // 供应商id
          supplierCode: selectData.supplierCode, // 供应商编码

          dateRange, // 日期范围 前端定义
          circulationTime, // 前端定义 循环时间 timeValue.week timeValue.timeFormat
          overtimeTime // 前端定义 超时时间 timeValue.hour
        }
      }
      return formData
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.actionType === DialogActionType.Add) {
            // 新增
            this.postBuyerForecastConfigureCreate()
          } else {
            // 编辑
            this.postBuyerForecastConfigureUpdate()
          }
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 配置方式 Change
    configGroupTypeChange(e) {
      if (e.value == ConfigGroupType.purchase) {
        // 配置方式 采购
        this.$refs.ruleForm.clearValidate(['planGroupCode']) // 计划组
        // 计划组
        this.formData.planGroupId = ''
        this.formData.planGroupCode = ''
        this.formData.planGroupName = ''
      } else if (e.value == ConfigGroupType.plan) {
        // 配置方式 计划
        this.$refs.ruleForm.clearValidate(['buyerOrgCode']) // 采购组
        // 采购组
        this.formData.buyerOrgId = ''
        this.formData.buyerOrgCode = ''
        this.formData.buyerOrgName = ''
      } else {
        // 采购组
        this.formData.buyerOrgId = ''
        this.formData.buyerOrgCode = ''
        this.formData.buyerOrgName = ''
        // 计划组
        this.formData.planGroupId = ''
        this.formData.planGroupCode = ''
        this.formData.planGroupName = ''
        // 供应商
        this.formData.supplierId = ''
        this.formData.supplierName = ''
        this.formData.supplierCode = ''
      }
    },
    // 配置类型 Change
    configTypeChange(e) {
      if (e.value == ConfigType.fixed) {
        // 配置类型 固定时间
        this.$refs.ruleForm.clearValidate(['overtimeTime']) // 超时时间
        // 配置类型 超时时间
        this.formData.overtimeTime = '' // 超时时间
      } else if (e.value == ConfigType.exceed) {
        this.formData.circulationTime = '' // 循环时间
      } else {
        this.formData.circulationTime = '' // 循环时间
        this.formData.overtimeTime = '' // 超时时间
      }
    },
    // 计划组 change
    planGroupCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.planGroupId = itemData.id
        this.formData.planGroupCode = itemData.groupCode
        this.formData.planGroupName = itemData.groupName
      } else {
        this.formData.planGroupId = ''
        this.formData.planGroupCode = ''
        this.formData.planGroupName = ''
      }
    },
    // 采购组 change
    buyerOrgCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.buyerOrgId = itemData.id
        this.formData.buyerOrgCode = itemData.groupCode
        this.formData.buyerOrgName = itemData.groupName
      } else {
        this.formData.buyerOrgId = ''
        this.formData.buyerOrgCode = ''
        this.formData.buyerOrgName = ''
      }
    },
    // 工厂 change
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.siteId = itemData.id
        this.formData.siteCode = itemData.siteCode
        this.formData.siteName = itemData.siteName
      } else {
        this.formData.siteId = ''
        this.formData.siteCode = ''
        this.formData.siteName = ''
      }
    },
    // 供应商 change
    supplierCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.supplierId = itemData.id
        this.formData.supplierCode = itemData.supplierCode
        this.formData.supplierName = itemData.supplierName
      } else {
        this.formData.supplierId = ''
        this.formData.supplierCode = ''
        this.formData.supplierName = ''
      }
    },
    // 采方预测配置修改
    postBuyerForecastConfigureUpdate() {
      // 日期范围 转 开始时间 结束时间
      const dateRange = formatDateRange(this.formData.dateRange)
      // 表单数据 转 TimeValue
      const timeValue = formDataToTimeValue({
        ...this.formData
      })
      const params = {
        id: this.formData.id,
        type: Type.forecast, // 配置类型:采购预测-1,jit-2,要货计划-3
        // 采购组
        buyerOrgCode: this.formData.buyerOrgCode,
        buyerOrgId: this.formData.buyerOrgId,
        buyerOrgName: this.formData.buyerOrgName,
        // 计划组
        planGroupCode: this.formData.planGroupCode,
        planGroupId: this.formData.planGroupId,
        planGroupName: this.formData.planGroupName,
        // 工厂
        siteCode: this.formData.siteCode,
        siteId: this.formData.siteId,
        siteName: this.formData.siteName,
        // 供应商
        supplierCode: this.formData.supplierCode,
        supplierId: this.formData.supplierId,
        supplierName: this.formData.supplierName,

        configGroupType: this.formData.configGroupType, // 配置方式
        configType: this.formData.configType, // 配置类型
        operateType: this.formData.operateType, // 任务类型
        startTime: dateRange.startTime, // 开始时间 dateRange
        endTime: dateRange.endTime, // 结束时间 dateRange
        // 循环时间 circulationTime 超时时间 overtimeTime
        timeValue
      }
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerForecastConfigureUpdate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm')
            this.handleClose()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方预测配置添加
    postBuyerForecastConfigureCreate() {
      // 日期范围 转 开始时间 结束时间
      const dateRange = formatDateRange(this.formData.dateRange)
      // 表单数据 转 TimeValue
      const timeValue = formDataToTimeValue({
        ...this.formData
      })
      const params = [
        {
          type: Type.forecast, // 配置类型:采购预测-1,jit-2,要货计划-3
          // 采购组
          buyerOrgCode: this.formData.buyerOrgCode,
          buyerOrgId: this.formData.buyerOrgId,
          buyerOrgName: this.formData.buyerOrgName,
          // 计划组
          planGroupCode: this.formData.planGroupCode,
          planGroupId: this.formData.planGroupId,
          planGroupName: this.formData.planGroupName,
          // 工厂
          siteCode: this.formData.siteCode,
          siteId: this.formData.siteId,
          siteName: this.formData.siteName,
          // 供应商
          supplierCode: this.formData.supplierCode,
          supplierId: this.formData.supplierId,
          supplierName: this.formData.supplierName,

          configGroupType: this.formData.configGroupType, // 配置方式
          configType: this.formData.configType, // 配置类型
          operateType: this.formData.operateType, // 任务类型
          startTime: dateRange.startTime, // 开始时间 dateRange
          endTime: dateRange.endTime, // 结束时间 dateRange
          // 循环时间 circulationTime 超时时间 overtimeTime
          timeValue
        }
      ]
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerForecastConfigureCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm')
            this.handleClose()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 获取主数据-采购组
    getBuyerOrgList(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        groupTypeCode: 'BG001CG'
      }
      this.$API.masterData
        .getbussinessGroup(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.buyerOrgOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.buyerOrgOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-计划组
    getPlanGroupList(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        groupTypeCode: 'BG001JH'
      }
      this.$API.masterData
        .getbussinessGroup(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.planGroupOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.planGroupOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 主数据 获取供应商
    getSupplier(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData
        .getSupplier(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.supplierOptions = addCodeNameKeyInList({
              firstKey: 'supplierCode',
              secondKey: 'supplierName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.supplierOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 编辑时获取所选择的下拉数据源
    handleGetSelectOptionsDuringEdit(selectData) {
      if (this.actionType === DialogActionType.Edit) {
        // siteCode 工厂
        this.postSiteFuzzyQuery({
          text: selectData.siteCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.siteCode = selectData.siteCode
          }
        })
        // supplierCode 供应商
        this.getSupplier({
          text: selectData.supplierCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.supplierCode = selectData.supplierCode
          }
        })
        // planGroupCode 计划组
        this.getPlanGroupList({
          text: selectData.planGroupCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.planGroupCode = selectData.planGroupCode
          }
        })
        // buyerOrgCode 采购组
        this.getBuyerOrgList({
          text: selectData.buyerOrgCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.buyerOrgCode = selectData.buyerOrgCode
          }
        })
      } else if (this.actionType === DialogActionType.Add) {
        // 获取主数据-获取工厂
        this.postSiteFuzzyQuery({ text: undefined })
        // 获取主数据-供应商
        this.getSupplier({ text: undefined })
        // 获取主数据-获取计划组
        this.getPlanGroupList({ text: undefined })
        // 获取主数据-获取采购组
        this.getBuyerOrgList({ text: undefined })
      }
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped></style>
