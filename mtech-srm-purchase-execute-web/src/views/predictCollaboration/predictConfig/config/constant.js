import { i18n } from '@/main.js'

// Toolbar 按钮
export const Toolbar = [
  {
    id: 'ConfigAdd',
    icon: 'icon_solid_Createproject',
    permission: ['O_02_0389'],
    title: i18n.t('新增')
  },
  {
    id: 'ConfigDelete',
    icon: 'icon_table_delete',
    permission: ['O_02_0391'],
    title: i18n.t('删除')
  },
  {
    id: 'ConfigActive',
    icon: 'icon_solid_Activateorder',
    permission: ['O_02_0392'],
    title: i18n.t('启用')
  },
  {
    id: 'ConfigInactive',
    icon: 'icon_solid_Pauseorder',
    permission: ['O_02_0393'],
    title: i18n.t('停用')
  },
  {
    id: 'ConfigImport',
    icon: 'icon_solid_Import',
    permission: ['O_02_0394'],
    title: i18n.t('导入')
  },
  {
    id: 'ConfigExport',
    icon: 'icon_solid_export',
    permission: ['O_02_0395'],
    title: i18n.t('导出')
  }
]

// 表格列数据
export const ColumnData = [
  {
    fieldCode: 'serialNumber', // 前端定义
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'status', // 停用/启用 启用状态:0-未启用,1-启用
    fieldName: i18n.t('状态')
  },
  {
    fieldCode: 'operateType', // 定时发布/定时反馈 任务类型:发布-1、反馈-2
    fieldName: i18n.t('任务类型')
  },
  {
    fieldCode: 'configGroupType', // 计划组+工厂+供应商/采购组+工厂+供应商 配置组合方式:采购组/工厂/供应商-1,计划组/工厂/供应商-2
    fieldName: i18n.t('配置方式')
  },
  {
    fieldCode: 'configType', // 固定时间/超时时间 配置类型:固定时间-1,超时时间-2
    fieldName: i18n.t('配置类型')
  },
  {
    fieldCode: 'planGroupCode', // 取自物料主数据的计划组清单 计划组名称 code-name
    fieldName: i18n.t('计划组')
    // planGroupId 计划组id
    // planGroupCode 计划组编码
    // lanGroupName
  },
  {
    fieldCode: 'buyerOrgCode', // 取自物料主数据的采购组清单 code-name
    fieldName: i18n.t('采购组')
    // buyerOrgId 采购组id
    // buyerOrgCode 采购组编码
    // buyerOrgName
  },
  {
    fieldCode: 'siteCode', // 工厂清单 code-name
    fieldName: i18n.t('工厂')
    // siteId 工厂id
    // siteCode 工厂编码
    // siteName
  },
  {
    fieldCode: 'supplierCode', // 供应商名称 模糊搜索供应商编号-名称 code-name
    fieldName: i18n.t('供应商')
    // supplierId 供应商id
    // supplierCode 供应商编码
    // supplierName
  },
  {
    fieldCode: 'startTime', // 开始时间
    fieldName: i18n.t('开始日期')
  },
  {
    fieldCode: 'endTime', // 结束时间
    fieldName: i18n.t('结束日期')
  },
  {
    fieldCode: 'circulationTime', // 前端定义 获取数据后转换 timeValue.week  timeValue.timeFormat
    fieldName: i18n.t('循环时间')
  },
  {
    fieldCode: 'overtimeTime', // 前端定义 获取数据后转换 timeValue.hour
    fieldName: i18n.t('超时时间（H）')
  },
  {
    fieldCode: 'updateUserName',
    fieldName: i18n.t('更新人')
  },
  {
    fieldCode: 'updateTime',
    fieldName: i18n.t('更新时间')
  }
]

// 弹出框类型
export const DialogActionType = {
  Add: 0, // 新增
  Edit: 1 // 编辑
}

// 周数据
export const WeekOptions = [
  { text: i18n.t('周一'), value: 1 },
  { text: i18n.t('周二'), value: 2 },
  { text: i18n.t('周三'), value: 3 },
  { text: i18n.t('周四'), value: 4 },
  { text: i18n.t('周五'), value: 5 },
  { text: i18n.t('周六'), value: 6 },
  { text: i18n.t('周日'), value: 7 }
]

// api 配置类型
export const Type = {
  forecast: 1 // 配置类型:采购预测-1,jit-2,要货计划-3
}

// 任务类型 定时发布/反馈 任务类型:发布-1、反馈-2
export const OperateType = {
  publish: 1, // 发布
  feedback: 2 // 反馈
}
// 任务类型 定时发布/反馈 text
export const OperateTypeText = {
  [OperateType.publish]: i18n.t('定时发布'),
  [OperateType.feedback]: i18n.t('定时反馈')
}
// 任务类型 定时发布/反馈 Options
export const OperateTypeOptions = [
  {
    text: OperateTypeText[OperateType.publish],
    value: OperateType.publish
  },
  {
    text: OperateTypeText[OperateType.feedback],
    value: OperateType.feedback
  }
]

// 配置方式 配置组合方式:采购组/工厂/供应商-1,计划组/工厂/供应商-2, 工厂-3
export const ConfigGroupType = {
  purchase: 1, // 采购组/工厂/供应商
  plan: 2, // 计划组/工厂/供应商
  factory: 3 // 工厂
}
// 配置方式 text
export const ConfigGroupTypeText = {
  [ConfigGroupType.purchase]: i18n.t('按采购组+工厂+供应商'),
  [ConfigGroupType.plan]: i18n.t('按计划组+工厂+供应商'),
  [ConfigGroupType.factory]: i18n.t('按工厂')
}
// 配置方式 Options
export const ConfigGroupTypeOptions = [
  {
    // 采购
    text: ConfigGroupTypeText[ConfigGroupType.purchase],
    value: ConfigGroupType.purchase,
    cssClass: ''
  },
  {
    // 计划
    text: ConfigGroupTypeText[ConfigGroupType.plan],
    value: ConfigGroupType.plan,
    cssClass: ''
  },
  {
    // 工厂
    text: ConfigGroupTypeText[ConfigGroupType.factory],
    value: ConfigGroupType.factory,
    cssClass: ''
  }
]

// 配置类型:固定时间-1,超时时间-2
export const ConfigType = {
  fixed: 1, // 固定时间
  exceed: 2 // 超时时间
}
// 配置类型 text
export const ConfigTypeText = {
  [ConfigType.fixed]: i18n.t('固定时间'),
  [ConfigType.exceed]: i18n.t('超时时间')
}
// 配置类型 Options
export const ConfigTypeOptions = [
  {
    // 固定时间
    text: ConfigTypeText[ConfigType.fixed],
    value: ConfigType.fixed
  },
  {
    // 超时时间
    text: ConfigTypeText[ConfigType.exceed],
    value: ConfigType.exceed
  }
]

// 配置状态 0:启用 1:停用
export const ConfigStatus = {
  active: 1, // 启用
  inactive: 0 // 停用
}
// 配置状态 0:启用 1:停用
export const ConfigStatusConst = {
  [ConfigStatus.active]: i18n.t('启用'),
  [ConfigStatus.inactive]: i18n.t('停用')
}
// 配置状态 对应的 css class
export const ConfigStatusCssClass = {
  [ConfigStatus.active]: 'col-active', // 启用
  [ConfigStatus.inactive]: 'col-inactive' // 停用
}

// 符号
export const SymbolConst = {
  comma: ',',
  slash: '/'
}
