import { i18n } from '@/main.js'
import {
  ConfigStatus,
  ConfigStatusConst,
  ConfigStatusCssClass,
  OperateType,
  OperateTypeText,
  ConfigGroupTypeOptions,
  ConfigType,
  ConfigTypeText,
  SymbolConst
} from './constant'
import { timeDate, circulationTime, overtimeTime, textByConfigGroupType } from './columnComponent'
import utils, { codeNameColumn } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'

// 格式化表格动态数据
export const formatTableColumnData = (data) => {
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150' // 自适应
    }
    if (col.fieldCode === 'serialNumber') {
      // 序号
      defaultCol.cssClass = '' // 序号不可点击
      defaultCol.allowFiltering = false
      defaultCol.ignore = true
      defaultCol.cellTools = [
        {
          id: 'ConfigEdit',
          icon: 'icon_list_edit',
          permission: ['O_02_0391'],
          title: i18n.t('编辑')
        },
        {
          id: 'ConfigDelete',
          icon: 'icon_solid_Delete',
          permission: ['O_02_0383'],
          title: i18n.t('删除')
        }
      ]
    } else if (col.fieldCode === 'operateType') {
      // 任务类型 定时发布/定时反馈 任务类型:发布-1、反馈-2
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            // 发布
            value: OperateType.publish,
            text: OperateTypeText[OperateType.publish],
            cssClass: ''
          },
          {
            // 反馈
            value: OperateType.feedback,
            text: OperateTypeText[OperateType.feedback],
            cssClass: ''
          }
        ]
      }
    } else if (col.fieldCode === 'configGroupType') {
      // 配置方式
      defaultCol.valueConverter = {
        type: 'map',
        map: ConfigGroupTypeOptions
      }
    } else if (col.fieldCode === 'configType') {
      // 配置类型:固定时间-1,超时时间-2
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            // 固定时间
            value: ConfigType.fixed,
            text: ConfigTypeText[ConfigType.fixed],
            cssClass: ''
          },
          {
            // 超时时间
            value: ConfigType.exceed,
            text: ConfigTypeText[ConfigType.exceed],
            cssClass: ''
          }
        ]
      }
    } else if (col.fieldCode === 'startTime') {
      // 开始日期
      defaultCol.template = timeDate({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'endTime') {
      // 结束日期
      defaultCol.template = timeDate({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'circulationTime') {
      // 循环时间
      defaultCol.width = '300'
      defaultCol.template = circulationTime({ dataKey: col.fieldCode })
    } else if (col.fieldCode === 'overtimeTime') {
      // 超时时间(H)
      defaultCol.template = overtimeTime({ dataKey: col.fieldCode })
    } else if (col.fieldCode === 'updateTime') {
      // 更新时间
      defaultCol.template = timeDate({
        dataKey: col.fieldCode,
        hasTime: true
      })
    } else if (col.fieldCode === 'planGroupCode') {
      // 计划组 code-name 形式
      defaultCol.width = '300'
      defaultCol.template = textByConfigGroupType({
        dataKey: col.fieldCode
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessGroup
      }
    } else if (col.fieldCode === 'buyerOrgCode') {
      // 采购组 code-name 形式
      defaultCol.width = '300'
      defaultCol.template = textByConfigGroupType({
        dataKey: col.fieldCode
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessGroup
      }
    } else if (col.fieldCode === 'siteCode') {
      // 工厂 code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.factoryAddress,
        placeholder: i18n.t('工厂')
      }
    } else if (col.fieldCode === 'supplierCode') {
      // 供应商 code-name 形式
      defaultCol.width = '300'
      defaultCol.template = textByConfigGroupType({
        dataKey: col.fieldCode
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.supplier,
        placeholder: i18n.t('供应商')
      }
    } else if (col.fieldCode === 'status') {
      // 配置状态
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            // 启用
            value: ConfigStatus.active,
            text: ConfigStatusConst[ConfigStatus.active],
            cssClass: ConfigStatusCssClass[ConfigStatus.active]
          },
          {
            // 停用
            value: ConfigStatus.inactive,
            text: ConfigStatusConst[ConfigStatus.inactive],
            cssClass: ConfigStatusCssClass[ConfigStatus.inactive]
          }
        ]
      }
      defaultCol.cellTools = [
        {
          id: 'ConfigInactive',
          icon: 'icon_list_disable',
          title: i18n.t('停用'),
          permission: ['O_02_0393'],
          visibleCondition: (data) => data.status == ConfigStatus.active // 启用
        },
        {
          id: 'ConfigActive',
          icon: 'icon_list_enable',
          title: i18n.t('启用'),
          permission: ['O_02_0392'],
          visibleCondition: (data) => data.status == ConfigStatus.inactive // 停用
        }
      ]
    }
    colData.push(defaultCol)
  })

  return colData
}

// 表格数据转换
export const serializeList = (list) => {
  if (list.length > 0) {
    let serialNumber = 1
    list.forEach((item) => {
      // 添加序号
      item.serialNumber = serialNumber++
    })
  }
  return list
}

// 日期范围 格式转换 by SelectData
export const dateRangeBySelectData = (args) => {
  const { startTime, endTime } = args
  let data = {}
  if (startTime && endTime) {
    const start = new Date(Number(startTime))
    const end = new Date(Number(endTime))
    if (!isNaN(start.getTime()) && !isNaN(end.getTime())) {
      data = {
        start,
        end
      }
    }
  }

  return data
}

// 循环时间 格式转换 by SelectData
export const circulationTimeBySelectData = (args) => {
  const { timeValue, configType } = args
  let data = {}
  const today = new Date()
  const dateStr = utils.formateTime(today, 'YYYY-mm-dd')
  const timeStr = timeValue?.timeFormat || ''
  const dateTimeStr = `${dateStr} ${timeStr}`
  let dateTime = new Date(dateTimeStr)
  if (isNaN(dateTime.getDate())) {
    dateTime = ''
  }

  if (configType == ConfigType.fixed) {
    // 配置类型 == 固定时间 && 循环方式 == 按周
    const weekStr = timeValue?.week
    let week = []
    if (weekStr?.length > 0) {
      week = weekStr?.split(SymbolConst.comma).map((item) => Number(item))
    }
    data = {
      time: dateTime,
      week: week
    }
  }

  return data
}

// 超时时间 格式转换 by SelectData
export const overtimeTimeBySelectData = (args) => {
  const { timeValue, configType } = args
  let data = ''
  if (configType == ConfigType.exceed) {
    const hourStr = timeValue?.hour || ''
    data = hourStr
  }

  return data
}

// 日期范围 转 开始时间 结束时间
export const formatDateRange = (dateRange) => {
  const data = {
    startTime: '',
    endTime: ''
  }

  if (!dateRange.time && !dateRange.week) {
    data.startTime = dateRange.start.valueOf()
    data.endTime = dateRange.end.valueOf()
  }

  return data
}

// 表单数据 转 TimeValue
export const formDataToTimeValue = (args) => {
  const {
    configType, // 配置类型
    circulationTime, // 循环时间
    overtimeTime // 超时时间
  } = args
  const data = {
    hour: undefined,
    timeFormat: undefined,
    week: undefined
  }
  if (configType == ConfigType.exceed) {
    // 配置类型 == 超时时间
    data.hour = overtimeTime
  } else if (configType == ConfigType.fixed) {
    // 配置类型 == 固定时间
    data.week = circulationTime.week.join(SymbolConst.comma)
    data.timeFormat = utils.formateTime(circulationTime.time, 'HH:MM')
  }

  return data
}
