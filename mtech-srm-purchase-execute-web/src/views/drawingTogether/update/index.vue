<template>
  <div class="full-height pt20">
    <mt-template-page
      slot="slot-0"
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickCellTool="handleClickCellTool"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config/index.js'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  mounted() {},
  components: {},
  data() {
    return {
      id: [],
      userInfo: null,
      addDialogShow: false,
      currentTabIndex: 0,
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'export',
                  icon: 'icon_solid_export',
                  title: this.$t('导出')
                },
                {
                  id: 'view_bom',
                  icon: 'icon_table_new',
                  title: this.$t('查看BOM'),
                  permission: ['O_02_1426']
                },
                {
                  id: 'view_drawing',
                  icon: 'icon_table_new',
                  title: this.$t('查看图纸'),
                  permission: ['O_02_1427']
                },
                {
                  id: 'view_dwg_drawing',
                  icon: 'icon_table_new',
                  title: this.$t('查看dwg图纸'),
                  permission: ['O_02_1445']
                },
                {
                  id: 'check',
                  title: this.$t('查看印刷品序列变量')
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          grid: {
            lineSelection: 0,
            columnData: columnData,
            autoWidthColumns: columnData.length + 1,
            asyncConfig: {
              url: `/sourcing/tenant/drawingsUpdate/buyer/queryBuilder`
            },
            frozenColumns: 1
          }
        }
      ],
      dialogData: null
    }
  },
  methods: {
    // 点击单元格 标题
    handleClickCellTitle(e) {
      if (e.field === 'itemCode') {
        // this.$API.drawingTogether.purQueryDrawing({ itemCode: e.data.itemCode }).then((res) => {
        //   if (res.code == 200) {
        //     window.open(res.data)
        //   }
        // })
        this.$router.push({
          name: 'drawings-check',
          query: {
            itemCode: e.data.itemCode,
            timeStamp: new Date().getTime()
          }
        })
      }
    },
    handleClickCellTool(e) {
      console.log(e)
    },
    handleClickToolBar(e) {
      console.log(e)
      let _records = e.grid.getSelectedRecords()
      if (e.toolbar.id == 'export') {
        // 导出
        this.exportExcel()
        return
      }
      if (
        ['view_bom', 'view_drawing', 'view_dwg_drawing', 'check'].includes(e.toolbar.id) &&
        _records?.length <= 0
      ) {
        this.$toast({
          type: 'warning',
          content: this.$t('请先选择一条数据')
        })
        return
      }
      if (
        ['view_bom', 'view_drawing', 'view_dwg_drawing'].includes(e.toolbar.id) &&
        _records.length > 1
      ) {
        this.$toast({
          type: 'warning',
          content: this.$t('只能查看单条数据BOM或图纸')
        })
        return
      }
      if (e.toolbar.id === 'view_drawing') {
        // this.handleClickViewDrawing(_records[0])
        this.$router.push({
          name: 'drawings-check',
          query: {
            itemCode: _records[0].itemCode,
            timeStamp: new Date().getTime()
          }
        })
      } else if (e.toolbar.id === 'view_dwg_drawing') {
        this.$router.push({
          name: 'drawings-dwg',
          query: {
            itemCode: _records[0].itemCode,
            timeStamp: new Date().getTime()
          }
        })
      } else if (e.toolbar.id === 'view_bom') {
        this.handleClickViewBom(_records[0])
      } else if (e.toolbar.id === 'check') {
        let itemCode = '',
          supplierCode = ''
        _records.forEach((item) => {
          itemCode = itemCode + ' ' + item.itemCode
          supplierCode = supplierCode + ' ' + item.supplierCode
        })
        this.$router.push({
          path: `variable-query`,
          query: {
            itemCode: itemCode.trim(),
            supplierCode: supplierCode.trim(),
            timeStamp: new Date().getTime()
          }
        })
      }
      let _status = [],
        _id = []
      _records.map((item) => {
        _id.push(item.id), _status.push(item.status)
      })
      this.id = _id
    },
    handleClickViewDrawing(row) {
      this.$API.drawingTogether.purQueryDrawing({ itemCode: row.itemCode }).then((res) => {
        if (res.code == 200) {
          window.open(res.data)
        }
      })
    },
    // 查看dwg图纸
    handleClickViewDwgDrawing(row) {
      this.$API.drawingTogether.purQueryDwgDrawing({ itemCode: row.itemCode }).then((res) => {
        if (res.code == 200) {
          window.open(res.data)
        }
      })
    },
    handleClickViewBom(row) {
      this.$dialog({
        modal: () => import('../release/components/viewBomDialog.vue'),
        data: {
          ...row
        },
        success: () => {}
      })
    },
    // Excel导出
    exportExcel() {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.drawingTogether.purExportDrawingUpdate(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/.optionBtn {
  color: #6386c1;
  cursor: pointer;
  margin-right: 10px;
}
</style>
