import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '80',
    field: 'index',
    headerText: i18n.t('序号')
  },
  {
    width: '150',
    field: 'drawingNo',
    headerText: i18n.t('图号')
  },
  {
    width: '150',
    field: 'drawingVersionNo',
    headerText: i18n.t('图纸版本')
  },
  {
    width: '200',
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    cellTools: []
  },
  {
    width: '430',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '350',
    field: 'materialNameLong',
    headerText: i18n.t('物料长描述')
  },
  {
    width: '200',
    field: 'origMachineModelCode',
    headerText: i18n.t('原机型编码')
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '150',
    field: 'oldProductsInvQty',
    headerText: i18n.t('旧制品库存数量')
  },
  {
    width: '150',
    field: 'viewStatus',
    headerText: i18n.t('查阅状态'),
    ignore: true,
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('未查阅'), cssClass: '' },
        { value: 1, text: i18n.t('已查阅'), cssClass: '' }
      ]
    }
  },
  {
    width: '150',
    field: 'viewTime',
    headerText: i18n.t('查阅时间')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间')
  }
]
