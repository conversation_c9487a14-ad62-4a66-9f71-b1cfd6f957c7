<!-- 供方-印刷品序列号变量查询 -->
<template>
  <div class="full-height vertical-flex-box">
    <collapse-search :is-grid-display="true" @reset="handleReset" @search="handleSearch">
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('物料编码')" prop="itemCode">
          <mt-input
            v-model="searchFormModel.itemCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('内需单号')" prop="domesticDemandCode">
          <mt-input
            v-model="searchFormModel.domesticDemandCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('销售订单号')" prop="saleOrderNo">
          <mt-input
            v-model="searchFormModel.saleOrderNo"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('销售订单行号')" prop="saleOrderItemNo">
          <mt-input
            v-model="searchFormModel.saleOrderItemNo"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('客户序列流水号/变量')" prop="serialNumber">
          <mt-input
            v-model="searchFormModel.serialNumber"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('是否已查阅最新')" prop="viewStatus">
          <mt-select
            v-model="searchFormModel.viewStatus"
            :data-source="viewStatusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料名称')" prop="itemName">
          <mt-input
            v-model="searchFormModel.itemName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="viewTime" :label="$t('最后查阅时间')">
          <mt-date-range-picker
            v-model="searchFormModel.viewTime"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'viewTime')"
          />
        </mt-form-item>
        <mt-form-item prop="pushTime" :label="$t('最后更新时间')">
          <mt-date-range-picker
            v-model="searchFormModel.pushTime"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'pushTime')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('分体机编码')" prop="fissionCode">
          <mt-input
            v-model="searchFormModel.fissionCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <div class="flex-fit">
      <sc-table
        ref="sctableRef"
        grid-id="5450db10-b518-461a-a499-db24baf673df"
        :loading="loading"
        :is-show-refresh-bth="true"
        :columns="columns"
        :table-data="tableData"
        keep-source
        @refresh="handleSearch"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :loading="item.loading"
            size="small"
            @click="handleClickToolBar(item)"
          >
            {{ item.name }}
          </vxe-button>
        </template>
        <template #fileDetault="{ row }">
          <span style="cursor: pointer; color: #2783fe" @click="handleDownload(row)">
            {{ row.fileName }}
          </span>
        </template>
        <template #operateDetault="{ row }">
          <div style="cursor: pointer; color: #2783fe" @click="handleJump(row)">
            {{ $t('操作/更新记录') }}
          </div>
        </template>
      </sc-table>
    </div>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
    <OperateLogDialog ref="operateLogRef" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, viewStatusOptions } from './config/index'
import { getHeadersFileName, download } from '@/utils/utils'
import OperateLogDialog from './components/OperateLogDialog.vue'

export default {
  components: { CollapseSearch, ScTable, OperateLogDialog },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      toolbar: [
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
        { code: 'search', name: this.$t('印刷件份数查询'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],
      viewStatusOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    if (this.$route.query?.itemCode) {
      this.searchFormModel.itemCode = this.$route.query?.itemCode
    }
    this.getTableData()
  },
  mounted() {},
  methods: {
    handleDownload(row) {
      this.$API.drawingTogether
        .getFileUrlApi({
          id: row.fileId
        })
        .then((res) => {
          if (res.code === 200) {
            window.open(res.data?.fileUrl)
          }
        })
    },
    handleJump(row) {
      this.$refs.operateLogRef.dialogInit({
        title: this.$t('操作/更新记录'),
        params: { id: row.id }
      })
    },
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.currentPage = 1
      this.pageSettings.totalRecordsCount = 0
      this.tableData = []
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.drawingTogether
        .pagePrintMaterialSerialNumberPushSupplierApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['search']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (selectedRecords.length > 1 && ['search'].includes(e.code)) {
        this.$toast({ content: this.$t('只能查询单条数据'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        case 'search':
          this.handleShow(selectedRecords)
          break
        default:
          break
      }
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$API.drawingTogether
        .exportPrintMaterialSerialNumberPushSupplierApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    handleShow(selectedRecords) {
      this.$store.commit('startLoading')
      this.$API.drawingTogether
        .getItemParentDosageApi({ itemCode: selectedRecords[0].itemCode })
        .then((res) => {
          if (res.code == 200) {
            this.$dialog({
              modal: () => import('./components/printSeachDialog.vue'),
              data: {
                tableData: res.data
              },
              success: () => {}
            })
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    }
  }
}
</script>
