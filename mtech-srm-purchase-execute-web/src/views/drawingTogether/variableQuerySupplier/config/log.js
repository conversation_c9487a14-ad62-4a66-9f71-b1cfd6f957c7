import { i18n } from '@/main.js'

export const columnData = [
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'operationType',
    title: i18n.t('操作方式')
  },
  {
    field: 'belong',
    title: i18n.t('操作方')
  },
  {
    field: 'operatorName',
    title: i18n.t('操作人')
  },
  {
    field: 'operateTime',
    title: i18n.t('操作时间'),
    minWidth: 160
  },
  {
    field: 'changeRecord',
    title: i18n.t('变更内容')
  },
  {
    field: 'changeBeforeValue',
    title: i18n.t('变更前')
  },
  {
    field: 'changeAfterValue',
    title: i18n.t('变更后')
  },
  {
    field: 'remark',
    title: i18n.t('备注')
  }
]
