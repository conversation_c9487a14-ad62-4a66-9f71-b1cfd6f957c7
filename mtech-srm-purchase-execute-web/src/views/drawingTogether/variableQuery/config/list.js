import { i18n } from '@/main.js'

export const viewStatusOptions = [
  { text: i18n.t('是'), value: 1 },
  { text: i18n.t('否'), value: 0 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称'),
    minWidth: 200
  },
  {
    field: 'fissionCode',
    title: i18n.t('分体机编码'),
    minWidth: 120
  },
  {
    field: 'domesticDemandCode',
    title: i18n.t('内需单号')
  },
  {
    field: 'saleOrderNo',
    title: i18n.t('销售订单号'),
    minWidth: 120
  },
  {
    field: 'saleOrderItemNo',
    title: i18n.t('销售订单行号'),
    minWidth: 120
  },
  {
    field: 'serialNumber',
    title: i18n.t('客户序列流水号/变量'),
    minWidth: 200
  },
  {
    field: 'file',
    title: i18n.t('流水号附件'),
    minWidth: 120,
    slots: {
      default: 'fileDetault'
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码'),
    minWidth: 120
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 200
  },
  {
    field: 'pushTime',
    title: i18n.t('最后更新时间'),
    minWidth: 160
  },
  {
    field: 'viewStatus',
    title: i18n.t('是否已查阅最新'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      let item = viewStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'viewTime',
    title: i18n.t('最后查阅时间'),
    minWidth: 160
  },
  {
    field: 'operate',
    title: i18n.t('操作'),
    minWidth: 120,
    slots: {
      default: 'operateDetault'
    }
  }
]
