<!-- 印刷品序列号变量查询 -->
<template>
  <div>
    <mt-tabs
      class="toggle-tab"
      :e-tab="false"
      :data-source="tabList"
      :selected-item="currentTab"
      @handleSelectTab="handleSelectTab"
    />
    <div class="toggle-content">
      <List v-show="currentTab === 0" />
      <Detail v-show="currentTab === 1" />
    </div>
  </div>
</template>

<script>
export default {
  components: {
    List: () => import('./pages/List.vue'),
    Detail: () => import('./pages/Detail.vue')
  },
  data() {
    return {
      tabList: [{ title: this.$t('已发布的序列号查询') }, { title: this.$t('PLM->SRM推送清单') }],
      currentTab: 0
    }
  },
  methods: {
    handleSelectTab(e) {
      this.currentTab = e
    }
  }
}
</script>
