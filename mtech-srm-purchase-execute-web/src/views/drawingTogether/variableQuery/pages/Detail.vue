<!-- PLM->SRM推送清单 -->
<template>
  <div class="full-height vertical-flex-box">
    <collapse-search :is-grid-display="true" @reset="handleReset" @search="handleSearch">
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('物料编码')" prop="itemCode">
          <mt-input
            v-model="searchFormModel.itemCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('内需单号')" prop="domesticDemandCode">
          <mt-input
            v-model="searchFormModel.domesticDemandCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('销售订单号')" prop="saleOrderNo">
          <mt-input
            v-model="searchFormModel.saleOrderNo"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('销售订单行号')" prop="saleOrderItemNo">
          <mt-input
            v-model="searchFormModel.saleOrderItemNo"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料名称')" prop="itemName">
          <mt-input
            v-model="searchFormModel.itemName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('分体机编码')" prop="fissionCode">
          <mt-input
            v-model="searchFormModel.fissionCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('客户序列流水号/变量')" prop="serialNumber">
          <mt-input
            v-model="searchFormModel.serialNumber"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="pushTime" :label="$t('推送时间')">
          <mt-date-range-picker
            v-model="searchFormModel.pushTime"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'pushTime')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <div class="flex-fit">
      <sc-table
        ref="sctableRef"
        grid-id="15b882c4-2069-4d73-98d4-cfd97534097d"
        :loading="loading"
        :is-show-refresh-bth="true"
        :columns="columns"
        :table-data="tableData"
        keep-source
        @refresh="handleSearch"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :loading="item.loading"
            size="small"
            @click="handleClickToolBar(item)"
          >
            {{ item.name }}
          </vxe-button>
        </template>
        <template #fileDetault="{ row }">
          <span style="cursor: pointer; color: #2783fe" @click="handleDownload(row)">
            {{ row.fileName }}
          </span>
        </template>
        <template #operateDetault="{ row }">
          <div style="cursor: pointer; color: #2783fe" @click="handleJump(row)">
            {{ $t('操作/更新记录') }}
          </div>
        </template>
      </sc-table>
    </div>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
    <CreateDialog ref="createRef" @confirm="handleSearch" />
    <OperateLogDialog ref="operateLogRef" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData } from '../config/detail'
import { getHeadersFileName, download } from '@/utils/utils'
import CreateDialog from '../components/CreateDialog.vue'
import OperateLogDialog from '../components/OperateLogDialog.vue'

export default {
  components: { CollapseSearch, ScTable, CreateDialog, OperateLogDialog },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      toolbar: [
        { code: 'create', name: this.$t('创建发布'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  mounted() {},
  methods: {
    handleDownload(row) {
      this.$API.drawingTogether
        .getFileUrlApi({
          id: row.fileId
        })
        .then((res) => {
          if (res.code === 200) {
            window.open(res.data?.fileUrl)
          }
        })
      // this.$API.fileService.fileDownload(row.fileId).then((res) => {
      //   let link = document.createElement('a')
      //   link.style.display = 'none'
      //   let blob = new Blob([res.data], { type: 'application/x-msdownload' })
      //   let url = window.URL.createObjectURL(blob)
      //   link.href = url
      //   link.setAttribute('download', `${row.attachFileName}.pdf`)
      //   link.click()
      //   window.URL.revokeObjectURL(url)
      // })
    },
    handleJump(row) {
      this.$refs.operateLogRef.dialogInit({
        title: this.$t('操作/更新记录'),
        params: { id: row.id }
      })
    },
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.currentPage = 1
      this.pageSettings.totalRecordsCount = 0
      this.tableData = []
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.drawingTogether
        .pagePrintMaterialSerialNumberApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const ids = []
      selectedRecords.forEach((item) => {
        ids.push(item.id)
      })
      const commonToolbar = ['create']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'create':
          this.handleCreate(ids)
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleCreate(ids) {
      this.$refs.createRef.dialogInit({
        ids
      })
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$API.drawingTogether
        .exportPrintMaterialSerialNumberApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>
