<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-deliver"
    :header="$t('创建发布')"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="handleClose"
  >
    <div class="title" style="margin-top: 10px">
      <span>{{ $t('供应商选择') }}</span>
    </div>
    <div style="height: calc(100% - 27px)">
      <mt-template-page ref="templateRef" :template-config="pageConfig"></mt-template-page>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          // useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [],
          gridId: '293c5bf6-c01c-486e-a2f3-4901e9122e7d',
          grid: {
            columnData: [
              { width: '50', type: 'checkbox', showInColumnChooser: false },
              { field: 'supplierCode', headerText: this.$t('供应商编码') },
              { field: 'supplierName', headerText: this.$t('供应商名称') }
            ],
            allowPaging: true,
            asyncConfig: {
              url: '/masterDataManagement/tenant/supplier/paged-query-distinct'
            }
          }
        }
      ],
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('发布') }
        }
      ],
      ids: []
    }
  },
  mounted() {},
  methods: {
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { ids } = args
      this.ids = ids
    },
    beforeOpen() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    confirm() {
      let _records = this.$refs.templateRef.getCurrentUsefulRef().gridRef.getMtechGridRecords()
      if (_records.length == 0) {
        this.$toast({
          content: this.$t('请选择供应商'),
          type: 'warning'
        })
        return
      } else {
        let supplierCodeList = []
        _records.forEach((item) => {
          supplierCodeList.push(item.supplierCode)
        })
        let params = {
          ids: this.ids,
          supplierCodeList
        }
        this.$API.drawingTogether.createPushPrintMaterialSerialNumberApi(params).then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('创建发布成功'), type: 'success' })
            this.$emit('confirm')
            this.handleClose()
          }
        })
      }
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.title {
  // margin: 24px 0px 24px 24px;
  padding-left: 8px;
  border-left: 4px solid #3369ac;
  font-weight: 600;
}
.demo-block .topBox {
  display: flex;
  justify-content: space-between;
  .box {
    font-size: 17px;
  }
}
/deep/ .box {
  margin-top: 5px;
  .mt-form-item-topLabel {
    display: flex;
    flex-wrap: wrap;
    .label {
      width: 100%;
    }
  }
}
</style>
<style lang="scss">
.dialog-deliver {
  .e-dlg-content {
    padding: 10px;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
        }
      }
    }
  }
}
</style>
