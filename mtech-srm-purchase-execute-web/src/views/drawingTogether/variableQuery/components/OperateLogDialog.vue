<template>
  <mt-dialog
    ref="logDialog"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    height="75%"
    width="80%"
    @beforeOpen="beforeOpen"
    @close="handleClose"
  >
    <div class="dialog-content" style="margin-top: 10px">
      <sc-table
        ref="sctableRef"
        grid-id="8c6c2865-3373-4297-9b25-7baaf8862561"
        :loading="loading"
        :is-show-refresh-bth="false"
        :columns="columns"
        :table-data="tableData"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :loading="item.loading"
            size="small"
            @click="handleClickToolBar(item)"
          >
            {{ item.name }}
          </vxe-button>
        </template>
      </sc-table>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { columnData } from '../config/log.js'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  components: { ScTable },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('关闭') }
        }
      ],
      params: null,
      columns: columnData,
      loading: false,
      tableData: [],
      toolbar: [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }]
    }
  },
  mounted() {},
  methods: {
    dialogInit(args) {
      this.$refs.logDialog.ejsRef.show()
      const { title, params } = args
      this.dialogTitle = title
      this.params = params
      this.getTableData()
    },
    async getTableData() {
      const params = this.params
      this.loading = true
      const res = await this.$API.drawingTogether
        .queryOperateLogApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const records = res?.data || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleExport(e) {
      const params = this.params
      this.$API.drawingTogether
        .exportOperateLogApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    beforeOpen() {
      this.tableData = []
    },
    onOpen(args) {
      args.preventFocus = true
    },
    handleClose() {
      this.$refs.logDialog.ejsRef.hide()
    }
  }
}
</script>
