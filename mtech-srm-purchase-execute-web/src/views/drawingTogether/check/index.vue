<!-- 图纸管理-查看图纸 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('物料编码')" prop="itemCode">
          <mt-input
            v-model="searchFormModel.itemCode"
            :placeholder="$t('单个精准查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      grid-id="bb8a9eef-9ad9-4d9e-9411-416d2958bce5"
      :loading="loading"
      :is-show-refresh-bth="false"
      :columns="columns"
      :table-data="tableData"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #fileDefault="{ row }">
        <div style="cursor: pointer; color: #2783fe" @click="handleClick(row)">
          {{ row.drawingName }}
        </div>
      </template>
      <template #statusDefault="{ row }">
        <div :class="{ 'status-red': row.status === '图纸缺失' }">{{ row.status }}</div>
      </template>
      <template #operateDefault="{ row }">
        <div style="cursor: pointer; color: #2783fe" @click="handleCheck(row)">
          {{ $t('查看历史版本') }}
        </div>
      </template>
    </sc-table>
    <HistoryDialog ref="historyRef" />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData } from './config/index'
import HistoryDialog from './components/HistoryDialog.vue'

export default {
  components: { CollapseSearch, ScTable, HistoryDialog },
  data() {
    return {
      searchFormModel: {},
      toolbar: [
        { code: 'download', name: this.$t('下载图纸'), status: 'info', loading: false },
        { code: 'back', name: this.$t('返回'), status: 'info', loading: false },
        { code: 'check', name: this.$t('查看更早版本'), status: 'info', loading: false }
      ],
      columns: columnData,
      loading: false,
      tableData: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    isSup() {
      return this.$route.name === 'drawings-check-sup'
    }
  },
  created() {
    this.getData()
  },
  methods: {
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.getData()
    },
    getData() {
      let params = {
        itemCode: this.searchFormModel?.itemCode || this.$route.query?.itemCode || null
      }
      this.$store.commit('startLoading')
      const api = this.isSup
        ? this.$API.drawingTogether.pageCheckSupApi
        : this.$API.drawingTogether.pageCheckApi
      api(params)
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.data
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['download']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'back':
          this.$router.go(-1)
          break
        case 'download':
          e.loading = true
          this.handleDownload(e, selectedRecords)
          break
        case 'check':
          this.$dialog({
            data: {
              title: this.$t('温馨提示'),
              message: this.$t(
                '在2024年8月30日，TCL空调事业部内部对图纸文档软件进行了升级，在此之前的图纸，若存在多个版本，只迁移了最新版本的图纸至新的图纸文档软件，查看更早的版本，请直接点击“确定”访问，给您带来不便敬请谅解！'
              )
            },
            success: () => {
              this.handleCheckMore()
            }
          })
          break
        default:
          break
      }
    },
    handleClick(item) {
      if (['正在工作', '正在审阅'].includes(item.status)) {
        this.$toast({
          content: this.$t(
            '该图纸版本正在流程中，不支持下载，建议流程完结（已发布）后再下载，如若下载上一版本，请查看历史版本。'
          ),
          type: 'warning'
        })
        return
      }
      window.open(item.url)
    },
    handleCheckMore() {
      let itemCode = this.searchFormModel?.itemCode || this.$route.query?.itemCode || null
      this.$API.drawingTogether.purQueryDrawing({ itemCode }).then((res) => {
        if (res.code == 200) {
          window.open(res.data)
        }
      })
    },
    handleCheck(row) {
      this.$refs.historyRef.dialogInit({
        title: this.$t('查看图纸历史版本'),
        params: {
          itemCode: row.itemCode,
          keyId: row.keyId,
          modelName: row.modelName
        }
      })
    },
    async handleDownload(e, selectedRecords) {
      let cantDown = selectedRecords.some((item) => ['正在工作', '正在审阅'].includes(item.status))
      if (cantDown) {
        this.$toast({
          content: this.$t(
            '该图纸版本正在流程中，不支持下载，建议流程完结（已发布）后再下载，如若下载上一版本，请查看历史版本。'
          ),
          type: 'warning'
        })
        e.loading = false
        return
      }

      const dpwnloadFile = ({ url, fileName }) => {
        let aLink = document.createElement('a')
        aLink.href = url
        aLink.download = fileName
        document.body.appendChild(aLink)
        aLink.click()
        document.body.removeChild(aLink)
      }
      selectedRecords.forEach((item, i) => {
        setTimeout(() => {
          dpwnloadFile({ url: item.url, fileName: item.drawingName })
        }, i * 500)
        // const url = item.url
        // let iframe = document.createElement('iframe')
        // iframe.id = item.id
        // iframe.style.display = 'none'
        // document.body.appendChild(iframe)
        // iframe.src = url
      })
      e.loading = false
      // const promises = selectedRecords.map((item) => fetch(item.url).then((res) => res.blob()))
      // Promise.all(promises)
      //   .then((result) => {
      //     result?.forEach((blob, i) => {
      //       const a = document.createElement('a')
      //       a.href = window.URL.createObjectURL(blob)
      //       a.style.display = 'none'
      //       a.download = selectedRecords[i].drawingName
      //       document.body.appendChild(a)
      //       a.click()
      //       document.body.removeChild(a)
      //     })
      //   })
      //   .catch(() => {
      //     this.$toast({ content: this.$t('下载失败'), type: 'warning' })
      //   })
      //   .finally(() => {
      //     e.loading = false
      //   })
    }
  }
}
</script>

<style lang="scss" scoped>
.status-red {
  color: red;
}
</style>
