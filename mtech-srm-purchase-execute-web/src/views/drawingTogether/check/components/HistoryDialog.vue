<template>
  <mt-dialog
    ref="dialog"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    height="75%"
    width="80%"
    @beforeOpen="beforeOpen"
    @close="handleClose"
  >
    <div class="dialog-content" style="margin-top: 10px">
      <sc-table
        ref="historyRef"
        grid-id="8720a8b3-458c-4553-8606-9bb8bcd8c781"
        :loading="loading"
        :is-show-refresh-bth="false"
        :columns="columns"
        :table-data="tableData"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :loading="item.loading"
            size="small"
            @click="handleClickToolBar(item)"
          >
            {{ item.name }}
          </vxe-button>
        </template>
        <template #fileDefault="{ row }">
          <div style="cursor: pointer; color: #2783fe" @click="handleClick(row)">
            {{ row.drawingName }}
          </div>
        </template>
      </sc-table>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { columnData } from '../config/history.js'
export default {
  components: { ScTable },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('关闭') }
        }
      ],
      params: null,
      columns: columnData,
      loading: false,
      tableData: [],
      toolbar: [
        { code: 'download', name: this.$t('下载图纸'), status: 'info', loading: false },
        { code: 'check', name: this.$t('查看更早版本'), status: 'info', loading: false }
      ]
    }
  },
  mounted() {},
  methods: {
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, params } = args
      this.dialogTitle = title
      this.params = params
      this.getTableData()
    },
    async getTableData() {
      const params = this.params
      this.loading = true
      const res = await this.$API.drawingTogether
        .pageHistoryCheckApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const records = res?.data || []
        this.tableData = records
      }
    },
    handleClick(item) {
      window.open(item.url)
    },
    handleClickToolBar(e) {
      const selectedRecords = this.$refs.historyRef.$refs.xGrid.getCheckboxRecords()
      const commonToolbar = ['download']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'download':
          e.loading = true
          this.handleDownload(e, selectedRecords)
          break
        case 'check':
          this.$dialog({
            data: {
              title: this.$t('温馨提示'),
              message: this.$t(
                '在2024年8月30日，TCL空调事业部内部对图纸文档软件进行了升级，在此之前的图纸，若存在多个版本，只迁移了最新版本的图纸至新的图纸文档软件，查看更早的版本，请直接点击“确定”访问，给您带来不便敬请谅解！'
              )
            },
            success: () => {
              this.handleCheck()
            }
          })
          break
        default:
          break
      }
    },
    handleCheck() {
      this.$API.drawingTogether.purQueryDrawing({ itemCode: this.params.itemCode }).then((res) => {
        if (res.code == 200) {
          window.open(res.data)
        }
      })
    },
    async handleDownload(e, selectedRecords) {
      const dpwnloadFile = ({ url, fileName }) => {
        let aLink = document.createElement('a')
        aLink.href = url
        aLink.download = fileName
        document.body.appendChild(aLink)
        aLink.click()
        document.body.removeChild(aLink)
      }
      selectedRecords.forEach((item, i) => {
        setTimeout(() => {
          dpwnloadFile({ url: item.url, fileName: item.drawingName })
        }, i * 500)
        // const url = item.url
        // let iframe = document.createElement('iframe')
        // iframe.id = item.id
        // iframe.style.display = 'none'
        // document.body.appendChild(iframe)
        // iframe.src = url
      })
      // const promises = selectedRecords.map((item) => fetch(item.url).then((res) => res.blob()))
      // Promise.all(promises)
      //   .then((result) => {
      //     result?.forEach((blob, i) => {
      //       const a = document.createElement('a')
      //       a.href = URL.createObjectURL(blob)
      //       a.style.display = 'none'
      //       a.download = selectedRecords[i].drawingName
      //       document.body.appendChild(a)
      //       a.click()
      //       document.body.removeChild(a)
      //     })
      //   })
      //   .catch(() => {
      //     this.$toast({ content: this.$t('下载失败'), type: 'warning' })
      //   })
      //   .finally(() => {
      //     e.loading = false
      //   })
    },
    beforeOpen() {
      this.tableData = []
    },
    onOpen(args) {
      args.preventFocus = true
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
