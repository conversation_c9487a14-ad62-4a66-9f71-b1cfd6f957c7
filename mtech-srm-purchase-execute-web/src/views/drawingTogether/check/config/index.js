import { i18n } from '@/main.js'

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码'),
    minWidth: 160
  },
  {
    field: 'itemLongDesc',
    title: i18n.t('物料描述'),
    minWidth: 200
  },
  {
    field: 'fileName',
    title: i18n.t('图纸名称'),
    minWidth: 200,
    slots: {
      default: 'fileDefault'
    }
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    minWidth: 120,
    slots: {
      default: 'statusDefault'
    }
  },
  {
    field: 'drawingNumOfVersions',
    title: i18n.t('图纸版本数量'),
    minWidth: 160
  },
  {
    field: 'drawingUpdateTime',
    title: i18n.t('图纸更新时间'),
    minWidth: 160
  },
  {
    field: 'matUpdateTime',
    title: i18n.t('物料更新时间'),
    minWidth: 160
  },
  {
    field: 'drawingUpdateUser',
    title: i18n.t('流程启动人'),
    minWidth: 160
  },
  {
    field: 'operate',
    title: i18n.t('操作'),
    minWidth: 120,
    slots: {
      default: 'operateDefault'
    }
  }
]
