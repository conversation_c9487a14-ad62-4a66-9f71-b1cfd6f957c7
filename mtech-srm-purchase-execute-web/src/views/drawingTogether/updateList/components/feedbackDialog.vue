<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-deliver"
    :header="$t('图纸查阅反馈')"
    :buttons="buttons"
    :dialog-data="dialogData"
    :height="400"
    :width="700"
    @close="handleClose"
    :open="onOpen"
  >
    <div class="full-height">
      <div class="text1">{{ $t('请立即按最新图纸生产，回收报废旧图纸。') }}</div>
      <mt-form ref="formInfo" :model="formInfo" :rules="rules">
        <mt-form-item
          :label="$t('旧制品库存数量反馈')"
          label-style="top"
          prop="oldProductsInvQty"
          width="400"
        >
          <mt-input
            v-model="formInfo.oldProductsInvQty"
            :multiline="false"
            maxlength="20"
            :placeholder="$t('请输入数量')"
            width="200"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      defaultOn: '',
      formInfo: {
        oldProductsInvQty: ''
      },
      rules: {},
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('关闭') }
        },
        {
          click: this.handleSubmit,
          buttonModel: { content: this.$t('保存') }
        }
      ]
    }
  },
  mounted() {
    console.log('dialogData', this.modalData)
    this.$refs.dialog.ejsRef.show()
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    handleClose() {
      this.$emit('cancel-function')
      this.$refs.dialog.ejsRef.hide()
    },
    handleSubmit() {
      this.$refs.dialog.ejsRef.hide()
      this.$API.drawingTogether
        .supViewFeedback({
          drawingUpdateFormId: this.modalData.drawingUpdateFormId,
          oldProductsInvQty: this.formInfo.oldProductsInvQty
        })
        .then((res) => {
          if (res.code == 200) {
            this.$emit('confirm-function')
            // this.$API.drawingTogether
            //   .purQueryDrawing({ itemCode: this.modalData.itemCode })
            //   .then((res) => {
            //     if (res.code == 200) {
            //       window.open(res.data)
            //     }
            //   })
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.demo-block .topBox {
  display: flex;
  justify-content: space-between;
  .box {
    font-size: 17px;
  }
}
/deep/ .box {
  margin-top: 5px;
  .mt-form-item-topLabel {
    display: flex;
    flex-wrap: wrap;
    .label {
      width: 100%;
    }
  }
}
/deep/ .mt-form-item {
  .mt-form-item-topLabel {
    flex: 1;
    display: flex;
    margin-top: 50px;
    justify-content: center;
    align-items: center;
  }
  .error-label-label-top {
    left: auto;
  }
}
</style>
<style lang="scss">
.dialog-deliver {
  .e-dlg-content {
    padding: 10px;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
        }
      }
    }
  }
}
.text1 {
  text-align: center;
  font-size: x-large;
  font-weight: 600;
}
</style>
