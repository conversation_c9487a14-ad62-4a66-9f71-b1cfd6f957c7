import { i18n } from '@/main.js'

export const ConstantType = {
  Add: '1', // 新增
  Edit: '2', // 编辑
  Look: '3' // 查看
}

// 对账单确认 页面类型
export const ConstantConfirmDetailType = {
  Confirm: '1', // 确认待反馈 编辑：附件、反馈意见，操作：接受、拒绝
  ConfirmClose: '2', // 确认关闭 不可编辑，操作：接受
  Edit: '3', // 编辑对账单 编辑：附件、备注，操作：提交
  Look: '4' // 查看
}

export const Tab = {
  pending: 0, // 待处理
  feedback: 1 // 已反馈
}

// 来源途径
export const ConstSourcePath = {
  purchaser: 0, // 采方
  supplier: 1 // 供方
}

// 待查阅
export const PendingColumnData = [
  {
    fieldCode: 'content', // 对账单编码
    fieldName: i18n.t('内容'),
    cellTools: []
  },
  {
    fieldCode: 'createTime',
    fieldName: i18n.t('创建时间')
  }
]

// 已查阅
export const FeedbackColumnData = [
  {
    fieldCode: 'content1', // 对账单编码
    fieldName: i18n.t('内容'),
    cellTools: [],
    allowEditing: false
  },
  {
    fieldCode: 'createTime',
    fieldName: i18n.t('创建时间'),
    allowEditing: false
  },
  {
    fieldCode: 'oldProductsInvQty',
    fieldName: i18n.t('旧制品数量反馈'),
    type: 'number'
  }
]
