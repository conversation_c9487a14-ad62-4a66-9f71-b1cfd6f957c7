import Vue from 'vue'
import utils from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'
import { Tab } from './constant'

// data: yyyy-mm-dd hh:mm:ss
export const timeToDate = (data) => {
  const { formatString, value } = data
  if (formatString && value) {
    const date = new Date(value)
    if (isNaN(date.getTime())) {
      return value
    } else {
      return utils.formateTime(date, formatString)
    }
  } else {
    return value
  }
}

export const formatTableColumnData = (config) => {
  const { tab, data } = config
  const colData = []
  if (tab === Tab.pending) {
    // 待处理 tab
    data.forEach((col) => {
      const defaultCol = {
        ...col,
        field: col.fieldCode,
        headerText: col.fieldName
      }
      if (col.fieldCode === 'content') {
        defaultCol.width = '500'
        // 对账单号
      } else if (col.fieldCode === 'createTime') {
        // 创建时间
        defaultCol.template = () => {
          return {
            template: Vue.component('createTime', {
              template: `<div><div>{{data.createTime | timeFormat}}</div><div>{{data.createTime | dateFormat}}</div></div>`,
              data: function () {
                return { data: {} }
              },
              mounted() {},
              methods: {},
              filters: {
                dateFormat(value) {
                  return timeToDate({ formatString: 'YYYY-mm-dd', value })
                },
                timeFormat(value) {
                  return timeToDate({ formatString: 'HH:MM:SS', value })
                }
              }
            })
          }
        }
        defaultCol.searchOptions = MasterDataSelect.dateRange
      }
      colData.push(defaultCol)
    })
  } else if (tab === Tab.feedback) {
    // 已反馈 tab
    data.forEach((col) => {
      const defaultCol = {
        ...col,
        field: col.fieldCode,
        headerText: col.fieldName,
        width: '150'
      }
      if (col.fieldCode === 'content1') {
        defaultCol.width = '500'
      } else if (col.fieldCode === 'createTime') {
        // 创建时间
        defaultCol.template = () => {
          return {
            template: Vue.component('createTime', {
              template: `<div><div>{{data.createTime | timeFormat}}</div><div>{{data.createTime | dateFormat}}</div></div>`,
              data: function () {
                return { data: {} }
              },
              mounted() {},
              methods: {},
              filters: {
                dateFormat(value) {
                  return timeToDate({ formatString: 'YYYY-mm-dd', value })
                },
                timeFormat(value) {
                  return timeToDate({ formatString: 'HH:MM:SS', value })
                }
              }
            })
          }
        }
        defaultCol.searchOptions = MasterDataSelect.dateRange
      }
      colData.push(defaultCol)
    })
  }
  return colData
}
