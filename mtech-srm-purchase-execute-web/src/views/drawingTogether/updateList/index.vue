<template>
  <!-- 客户对账协同（供方）-对账单清单列表 -->
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :current-tab="currentTab"
      :template-config="componentConfig"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    />
  </div>
</template>

<script>
import { PendingColumnData, FeedbackColumnData, Tab } from './config/constant'
import { formatTableColumnData } from './config/index'
// import FeedbackDialog from './components/feedbackDialog.vue'
export default {
  // components: { FeedbackDialog },
  data() {
    const currentTab = JSON.parse(localStorage.getItem('tabIndex')) ?? 0

    return {
      feedbackShow: false,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      currentTab, // 当前加载的 Tab 默认 0
      componentConfig: [
        {
          title: this.$t('待查阅'),
          toolbar: [
            [
              {
                id: 'view_bom',
                icon: 'icon_table_new',
                title: this.$t('查看BOM'),
                permission: ['O_02_1422']
              },
              {
                id: 'view_dwg_drawing',
                icon: 'icon_table_new',
                title: this.$t('查看dwg图纸'),
                permission: ['O_02_1443']
              },
              {
                id: 'check',
                title: this.$t('查看印刷品序列变量')
              }
            ]
          ],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          grid: {
            columnData: formatTableColumnData({
              tab: Tab.pending,
              data: PendingColumnData
            }),
            dataSource: [],
            lineSelection: 0,

            asyncConfig: {
              url: `/sourcing/tenant/drawingsUpdate/supplier/queryBuilder`, // queryBuilder查询-待处理对账信息
              defaultRules: [],
              rules: [
                {
                  field: 'viewStatus',
                  label: this.$t('查阅状态'),
                  operator: 'equal',
                  type: 'string',
                  value: '0'
                }
              ],
              serializeList: (list) => {
                list.forEach((item) => {
                  item.content =
                    '图号：' +
                    item.drawingNo +
                    '，版本：' +
                    item.drawingVersionNo +
                    '，物料：' +
                    item.itemCode +
                    '，图纸已更新，请及时查阅'
                })
                return list
              }
            }
          }
        },
        {
          title: this.$t('已查阅'),
          toolbar: [
            [
              {
                id: 'view_bom',
                icon: 'icon_table_new',
                title: this.$t('查看BOM'),
                permission: ['O_02_1422']
              },
              {
                id: 'view_drawing',
                icon: 'icon_table_new',
                title: this.$t('查看图纸'),
                permission: ['O_02_1423']
              },
              {
                id: 'view_dwg_drawing',
                icon: 'icon_table_new',
                title: this.$t('查看dwg图纸'),
                permission: ['O_02_1443']
              },
              {
                id: 'check',
                title: this.$t('查看印刷品序列变量')
              }
            ]
          ],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置

          grid: {
            columnData: formatTableColumnData({
              tab: Tab.feedback,
              data: FeedbackColumnData
            }),
            dataSource: [],
            allowEditing: true,
            editSettings: {
              allowEditing: true
            },
            lineSelection: 0,
            asyncConfig: {
              url: `/sourcing/tenant/drawingsUpdate/supplier/queryBuilder`, // queryBuilder查询-已反馈对账信息
              defaultRules: [],
              rules: [
                {
                  field: 'viewStatus',
                  label: this.$t('查阅状态'),
                  operator: 'equal',
                  type: 'string',
                  value: '1'
                }
              ],
              serializeList: (list) => {
                list.forEach((item) => {
                  item.content1 =
                    '图号：' +
                    item.drawingNo +
                    '，版本：' +
                    item.drawingVersionNo +
                    '，物料：' +
                    item.itemCode +
                    '，图纸已更新，请及时查阅'
                })
                return list
              }
            }
          }
        }
      ]
    }
  },
  mounted() {},
  beforeDestroy() {
    localStorage.removeItem('tabIndex')
  },
  methods: {
    handleAddDialogShow() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // CellTool
    handleClickCellTool(e) {
      console.log(e)
    },
    // CellTitle
    handleClickCellTitle(e) {
      if (e.field == 'content') {
        this.$dialog({
          modal: () => import('./components/feedbackDialog.vue'),
          data: {
            title: this.$t('图纸查阅反馈'),
            drawingUpdateFormId: e.data.drawingUpdateFormId,
            itemCode: e.data.itemCode
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
            this.$router.push({
              name: 'drawings-check-sup',
              query: {
                itemCode: e.data.itemCode,
                timeStamp: new Date().getTime()
              }
            })
          }
        })
      } else if (e.field == 'content1') {
        // this.$API.drawingTogether.purQueryDrawing({ itemCode: e.data.itemCode }).then((res) => {
        //   if (res.code == 200) {
        //     window.open(res.data)
        //   }
        // })
        this.$router.push({
          name: 'drawings-check-sup',
          query: {
            itemCode: e.data.itemCode,
            timeStamp: new Date().getTime()
          }
        })
      }
    },
    actionBegin(args) {
      const { rowData } = args
      if (rowData.oldProductsInvQty == null) {
        args.cancel = false
      } else {
        args.cancel = true
      }
    },
    actionComplete(args) {
      const { requestType, data } = args

      if (requestType == 'save') {
        this.apiStartLoading()
        this.saveQty(data)
        // 调保存接口
      }
    },
    saveQty(data) {
      this.$API.drawingTogether
        .supViewFeedback({
          drawingUpdateFormId: data.drawingUpdateFormId,
          oldProductsInvQty: data.oldProductsInvQty
        })
        .then((res) => {
          if (res.code == 200) {
            this.$refs.templateRef.refreshCurrentGridData()
            this.apiEndLoading()
          }
        })
    },
    // ToolBar
    handleClickToolBar(e) {
      console.log(e)
      let _records = e.grid.getSelectedRecords()
      if (
        ['view_bom', 'view_drawing', 'view_dwg_drawing', 'check'].includes(e.toolbar.id) &&
        _records?.length <= 0
      ) {
        this.$toast({
          type: 'warning',
          content: this.$t('请先选择一条数据')
        })
        return
      }
      if (
        ['view_bom', 'view_drawing', 'view_dwg_drawing'].includes(e.toolbar.id) &&
        _records.length > 1
      ) {
        this.$toast({
          type: 'warning',
          content: this.$t('只能查看单条数据BOM或图纸')
        })
        return
      }
      if (e.toolbar.id === 'view_drawing') {
        // this.handleClickViewDrawing(_records[0])
        this.$router.push({
          name: 'drawings-check-sup',
          query: {
            itemCode: _records[0].itemCode,
            timeStamp: new Date().getTime()
          }
        })
      } else if (e.toolbar.id === 'view_dwg_drawing') {
        this.$router.push({
          name: 'drawings-dwg',
          query: {
            itemCode: _records[0].itemCode,
            timeStamp: new Date().getTime()
          }
        })
      } else if (e.toolbar.id === 'view_bom') {
        this.handleClickViewBom(_records[0])
      } else if (e.toolbar.id === 'check') {
        let itemCode = ''
        _records.forEach((item) => {
          itemCode = itemCode + ' ' + item.itemCode
        })
        this.$router.push({
          path: `variable-query-supplier`,
          query: {
            itemCode: itemCode.trim(),
            timeStamp: new Date().getTime()
          }
        })
      }
    },
    handleClickViewDrawing(row) {
      this.$API.drawingTogether.purQueryDrawing({ itemCode: row.itemCode }).then((res) => {
        if (res.code == 200) {
          window.open(res.data)
        }
      })
    },
    // 查看dwg图纸
    handleClickViewDwgDrawing(row) {
      this.$API.drawingTogether.purQueryDwgDrawing({ itemCode: row.itemCode }).then((res) => {
        if (res.code == 200) {
          window.open(res.data)
        }
      })
    },
    handleClickViewBom(row) {
      this.$dialog({
        modal: () => import('../accept/components/viewBomDialog.vue'),
        data: {
          ...row
        },
        success: () => {}
      })
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
