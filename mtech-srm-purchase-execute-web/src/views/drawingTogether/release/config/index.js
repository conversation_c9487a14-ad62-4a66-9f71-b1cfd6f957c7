import Vue from 'vue'
import { i18n } from '@/main.js'
import dayjs from 'dayjs'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'feedbackStatus',
    headerText: i18n.t('反馈状态'),
    ignore: true,
    valueConverter: {
      type: 'map',
      map: [
        { value: null, text: i18n.t(''), cssClass: '' },
        { value: 0, text: i18n.t(''), cssClass: '' },
        { value: 1, text: i18n.t('无权查看'), cssClass: '' },
        { value: 2, text: i18n.t('已查看'), cssClass: '' },
        { value: 3, text: i18n.t('已反馈'), cssClass: '' }
      ]
    }
  },
  {
    width: '200',
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    searchOptions: { maxQueryValueLength: 10000 }
  },
  {
    width: '450',
    field: 'itemText',
    headerText: i18n.t('物料名称')
  },
  {
    width: '200',
    field: 'originalModelCode',
    headerText: i18n.t('原机型编码')
  },
  {
    width: '350',
    field: 'operator',
    headerText: i18n.t('操作'),
    ignore: true,
    template: function () {
      return {
        template: Vue.component('sortIpt', {
          template: `<div class="sortIptNumber">
                      <span class="optionBtn" style="cursor: pointer" @click="viewDetail">{{ $t('查看图纸') }}</span>
                      <span class="optionBtn" style="cursor: pointer" @click="handleCheck">{{ $t('查看印刷品序列变量') }}</span>
                      <span class="optionBtn" style="cursor: pointer" @click="viewSendRecord">{{ $t('发送记录') }}</span>
                      <span class="optionBtn" style="cursor: pointer" v-if="data.feedbackStatus==3" @click="feedbackInfo">{{ $t('反馈信息') }}</span>
                    </div>`,
          data() {
            return {}
          },
          mounted() {},
          methods: {
            viewDetail() {
              // this.$API.drawingTogether
              //   .purQueryDrawing({ itemCode: this.data.itemCode })
              //   .then((res) => {
              //     if (res.code == 200) {
              //       window.open(res.data)
              //     }
              //   })
              this.$router.push({
                name: 'drawings-check',
                query: {
                  itemCode: this.data.itemCode,
                  timeStamp: new Date().getTime()
                }
              })
            },
            viewSendRecord() {
              console.log('viewSendRecord')
              this.$dialog({
                modal: () => import('../components/sendDialog.vue'),
                data: {
                  dialogData: this.data
                },
                success: () => {}
              })
            },
            feedbackInfo() {
              console.log('feedbackInfo', this.data)
              this.$dialog({
                modal: () => import('../components/feedbackDialog.vue'),
                data: {
                  dialogData: this.data
                },
                success: () => {}
              })
            },
            handleCheck() {
              this.$router.push({
                path: `variable-query`,
                query: {
                  itemCode: this.data.itemCode,
                  supplierCode: this.data.supplierCode,
                  timeStamp: new Date().getTime()
                }
              })
            }
          }
        })
      }
    }
  }
  // {
  //   width: "150",
  //   field: "drawingsType",
  //   headerText: i18n.t("图纸类型"),
  //   valueConverter: {
  //     type: "map",
  //     map: [
  //       { value: 0, text: i18n.t("2D"), cssClass: "" },
  //       { value: 1, text: i18n.t("3D"), cssClass: "" },
  //       { value: 2, text: i18n.t("美工"), cssClass: "" },
  //     ],
  //   },
  // },

  // {
  //   width: "150",
  //   field: "drawingsName",
  //   headerText: i18n.t("图纸名称"),
  //   // editTemplate: () => {
  //   //   return {
  //   //     template: "1212",
  //   //   };
  //   // },
  //   // template: timeDate("sendTime", true),
  // },
  // {
  //   width: "150",
  //   field: "version",
  //   headerText: i18n.t("版本"),
  // },
  // {
  //   width: "150",
  //   field: "supplierReason",
  //   headerText: i18n.t("供应商异常备注"),
  // },
  // {
  //   width: "150",
  //   field: "feedbackTime",
  //   headerText: i18n.t("更新时间"),
  //   valueConverter: {
  //     type: "function",
  //     filter: (e) => {
  //       if (e) {
  //         e = Number(e);
  //         return UTILS.dateFormat(e);
  //       } else {
  //         return e;
  //       }
  //     },
  //   },
  // },
  // {
  //   width: "150",
  //   field: "supplierName",
  //   headerText: i18n.t("发布供应商"),
  // },
  // {
  //   width: "150",
  //   field: "publishTime",
  //   headerText: i18n.t("发送时间"),
  //   valueConverter: {
  //     type: "function",
  //     filter: (e) => {
  //       if (e) {
  //         e = Number(e);
  //         return UTILS.dateFormat(e);
  //       } else {
  //         return e;
  //       }
  //     },
  //   },
  // },
]
export const columnData2 = [
  { width: '50', type: 'checkbox', showInColumnChooser: false },
  { field: 'supplierCode', headerText: i18n.t('供应商编码') },
  { field: 'supplierName', headerText: i18n.t('供应商名称') }
  // { width: "150", field: "organizationName", headerText: i18n.t("采方公司") },
  // { width: "150", field: "statusDescription", headerText: i18n.t("状态") },
  // {
  //   width: "150",

  //   field: "drawingsType",
  //   headerText: i18n.t("图纸类型"),
  //   valueConverter: {
  //     type: "map",
  //     //0-不循环，1. 循环
  //     map: {
  //       0: i18n.t("2D"),
  //       1: i18n.t("3D"),
  //       2: i18n.t("美工"),
  //     },
  //   },
  // },
  // {
  //   width: "150",

  //   field: "drawingsName",
  //   headerText: i18n.t("图纸名称"),
  // },
  // {
  //   width: "150",

  //   field: "version",
  //   headerText: i18n.t("版本"),
  // },
  // {
  //   width: "150",

  //   field: "itemName",
  //   headerText: i18n.t("更新时间"),
  // },
]

export const viewBomColumns = [
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',

    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',

    field: 'unit',
    headerText: i18n.t('组件单位')
  },
  {
    width: '150',

    field: 'count',
    headerText: i18n.t('组件数量')
  },
  {
    width: '150',

    field: 'createTime',
    headerText: i18n.t('创建时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          return dayjs(e).format('YYYY-MM-DD HH:mm:ss')
        }
        return ''
      }
    }
  },
  {
    width: '150',
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          return dayjs(e).format('YYYY-MM-DD HH:mm:ss')
        }
        return ''
      }
    }
  }
]

export const sendColumns = [
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '150',

    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '150',

    field: 'publishTime',
    headerText: i18n.t('发送时间')
  },
  {
    width: '150',

    field: 'feedbackStatus',
    headerText: i18n.t('反馈状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t(''), cssClass: '' },
        { value: 1, text: i18n.t('无权查看'), cssClass: '' },
        { value: 2, text: i18n.t('已查看'), cssClass: '' },
        { value: 3, text: i18n.t('已反馈'), cssClass: '' }
      ]
    }
  }
]

export const feedbackColumns = [
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '150',

    field: 'supplierReason',
    headerText: i18n.t('反馈信息')
  },
  {
    width: '150',
    field: 'feedbackTime',
    headerText: i18n.t('反馈时间')
  }
]
