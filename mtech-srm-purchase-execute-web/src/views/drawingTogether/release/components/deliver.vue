<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-deliver"
    :header="$t('创建发布')"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <div class="title" style="margin-top: 10px">
      <span>{{ $t('供应商选择') }}</span>
    </div>
    <div style="height: calc(100% - 27px)">
      <mt-template-page ref="templateRef" :template-config="pageConfig"></mt-template-page>
    </div>
    <!-- <mt-form ref="rules" :model="addForm" :rules="rules">
      <mt-form-item
        prop="supplierTenantIdList"
        label="发布给供应商"
        class="full-width"
      >
        <mt-multi-select
          v-model="addForm.supplierTenantIdList"
          :data-source="supplierTypeList"
          @change="supplierClick"
          :allow-filtering="true"
          filter-type="Contains"
          :allow-multi-selection="true"
          :fields="{ text: 'supplierName', value: 'supplierCode' }"
          :show-clear-button="true"
        ></mt-multi-select>
      </mt-form-item>
      <mt-data-grid
        class="consolidation-dialog"
        :data-source="dataSource2"
        :column-data="columnData2"
        height="330"
        ref="dataGrid"
      ></mt-data-grid>
    </mt-form> -->
  </mt-dialog>
</template>

<script>
import { columnData2 } from '../config/index'
// import { formatDate, formatRules } from "@/utils/util";
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          // useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [],
          gridId: '3f92554a-aaaf-422f-a0a7-259acaedee43',
          grid: {
            columnData: columnData2,
            allowPaging: true,
            asyncConfig: {
              url: '/masterDataManagement/tenant/supplier/paged-query-distinct'
            }
          }
        }
      ],
      defaultOn: '',
      columnData2: columnData2,
      fields: { text: 'itemName', value: 'itemCode' },
      dataSource2: [],
      itemList: [],
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('发布') }
        }
      ],
      supplierTypeList: [],
      addForm: {
        buyerDrawingsReqList: [],
        supplierList: []
      },
      dataSource: [],
      rules: {
        rejectReason: [
          {
            required: true,
            message: this.$t('请输入退回原因'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  mounted() {
    console.log('mounted--', this.dialogData)
    this.$refs.dialog.ejsRef.show()
    // this.init();
    this.getSupplier()
  },

  methods: {
    // aaa() {
    //   this.$API.drawingTogether
    //     .drawingsListByItemCode(this.itemList)
    //     .then((res) => {
    //       // 测试数据
    //       res.data = [];
    //       this.itemList.forEach((item) => {
    //         res.data.push({
    //           abolished: 0,
    //           createTime: 0,
    //           customerCode: "",
    //           customerName: "",
    //           drawingsName: "",
    //           drawingsType: 0,
    //           drawingsUrl: "",
    //           errorStatus: 0,
    //           feedbackTime: 0,
    //           // id: "",
    //           itemCode: item,
    //           itemName: "ttttt",
    //           publishTime: 0,
    //           supplierCode: "",
    //           supplierName: "",
    //           supplierReason: "",
    //           supplierTenantId: 0,
    //           tenantId: 0,
    //           version: 0,
    //         });
    //       });

    //       this.dataSource2 = res.data || [];
    //       this.addForm.buyerDrawingsReqList = this.dataSource2;
    //     });
    // },
    // init(val) {
    //   let params = {
    //     keyword: val || "",
    //     pageSize: 50,
    //   };
    //   this.$API.masterData.getItemByKeyword(params).then((res) => {
    //     this.dataSource = res.data?.records || [];
    //   });
    // },
    // 获取供应商下拉
    getSupplier() {
      let obj = {
        fuzzyNameOrCode: ''
      }
      this.$API.masterData.getSupplier(obj).then((res) => {
        // this.supplierTypeList.length = 0;
        this.supplierTypeList = res.data

        // res.data.forEach((e) => {
        // });
      })
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    supplierClick(e) {
      let obj = []
      this.supplierTypeList.forEach((item) => {
        e.value.forEach((r) => {
          if (r === item.supplierCode) {
            obj.push(item)
          }
        })
      })
      let pms = []
      obj.forEach((item) => {
        pms.push({
          supplierCode: item.supplierCode,
          supplierName: item.supplierName,
          supplierTenantId: item.supplierTenantId
        })
      })
      this.addForm.supplierList = pms
      this.dataSource2 = pms
      console.log('pms---', pms)
      console.log(e)
    },
    confirm() {
      let _records = this.$refs.templateRef.getCurrentUsefulRef().gridRef.getMtechGridRecords()
      debugger
      if (_records.length == 0) {
        this.$toast({
          content: this.$t('请选择供应商'),
          type: 'warning'
        })
        return
      } else {
        let obj = []
        _records.forEach((item) => {
          obj.push({
            supplierCode: item.supplierCode,
            supplierName: item.supplierName,
            supplierTenantId: item.supplierTenantId
          })
        })
        let _param = {
          buyerDrawingsReqList: this.dialogData,
          supplierList: obj
        }
        this.$API.drawingTogether.createPublish(_param).then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: this.$t('发布成功'),
              type: 'success'
            })
            this.handleClose()
          }
        })
      }
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.title {
  // margin: 24px 0px 24px 24px;
  padding-left: 8px;
  border-left: 4px solid #3369ac;
  font-weight: 600;
}
.demo-block .topBox {
  display: flex;
  justify-content: space-between;
  .box {
    font-size: 17px;
  }
}
/deep/ .box {
  margin-top: 5px;
  .mt-form-item-topLabel {
    display: flex;
    flex-wrap: wrap;
    .label {
      width: 100%;
    }
  }
}
</style>
<style lang="scss">
.dialog-deliver {
  .e-dlg-content {
    padding: 10px;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
        }
      }
    }
  }
}
</style>
