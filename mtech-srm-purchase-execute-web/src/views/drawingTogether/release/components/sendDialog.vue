<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-deliver"
    :header="$t('发送记录')"
    :buttons="buttons"
    :dialog-data="dialogData"
    width="70%"
    @close="handleClose"
    :open="onOpen"
  >
    <div class="full-height">
      <mt-template-page :template-config="pageConfig" ref="dataGrid"></mt-template-page>
    </div>
  </mt-dialog>
</template>

<script>
import { sendColumns } from '../config/index'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: [
        {
          toolbar: [],
          useToolTemplate: false,
          grid: {
            lineSelection: true,
            columnData: sendColumns,
            asyncConfig: {
              url: `/sourcing/tenant/drawings/buyer/record/query`,
              params: {
                condition: 'and',
                defaultRules: [
                  {
                    label: this.$t('物料编码'),
                    field: 'itemCode',
                    type: 'string',
                    operator: 'equal',
                    value: this.modalData.dialogData.itemCode
                  }
                ]
              }
            }
          }
        }
      ],
      defaultOn: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('关闭') }
        }
      ]
    }
  },
  mounted() {
    console.log('dialogData', this.modalData)
    this.$refs.dialog.ejsRef.show()
    this.init()
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.demo-block .topBox {
  display: flex;
  justify-content: space-between;
  .box {
    font-size: 17px;
  }
}
/deep/ .box {
  margin-top: 5px;
  .mt-form-item-topLabel {
    display: flex;
    flex-wrap: wrap;
    .label {
      width: 100%;
    }
  }
}
</style>
<style lang="scss">
.dialog-deliver {
  .e-dlg-content {
    padding: 10px;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
        }
      }
    }
  }
}
</style>
