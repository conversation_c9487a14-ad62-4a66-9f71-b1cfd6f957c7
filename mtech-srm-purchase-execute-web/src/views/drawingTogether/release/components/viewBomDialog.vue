<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-deliver"
    :header="$t('查看BOM')"
    :buttons="buttons"
    :dialog-data="modalData"
    width="70%"
    height="80%"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="searchForm" :model="searchFormModel">
      <mt-row :gutter="48">
        <mt-col :span="6">
          <mt-form-item :label="$t('工厂')" label-style="top">
            <mt-select
              v-model="searchFormModel.siteCode"
              :data-source="siteOptions"
              :placeholder="$t('请选择工厂')"
              @change="siteCodeChange"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="6">
          <mt-form-item :label="$t('物料编码')" label-style="top">
            <mt-input v-model="searchFormModel.itemCode" :disabled="true" />
          </mt-form-item>
        </mt-col>
        <mt-col :span="10">
          <mt-form-item :label="$t('物料名称')" label-style="top">
            <mt-input v-model="searchFormModel.itemName" :disabled="true" />
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
    <div>
      <mt-template-page
        :template-config="pageConfig"
        ref="dataGrid"
        @handleClickToolBar="handleClickToolBar"
      ></mt-template-page>
    </div>
  </mt-dialog>
</template>

<script>
import { viewBomColumns } from '../config/index'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      searchFormModel: {
        siteCode: '5540',
        itemCode: '',
        itemName: ''
      },
      siteOptions: [
        { text: '5540-TCL空调(武汉)有限公司生产工厂', value: '5540' },
        { text: '5543-TCL空调(武汉)有限公司注塑工厂 ', value: '5543' }
      ],
      pageConfig: [
        {
          toolbar: {
            tools: [
              [
                {
                  id: 'export',
                  icon: 'icon_solid_export',
                  title: this.$t('导出')
                }
              ],
              []
            ]
          },
          useToolTemplate: false,
          grid: {
            height: 440,
            // lineSelection: true,
            lineIndex: 0,
            columnData: viewBomColumns,
            dataSource: [],
            allowPaging: false
            // asyncConfig: {
            //   url: `/purchase-execute/tenant/bom`,
            //   params: {
            //     condition: 'and',
            //     defaultRules: [
            //       {
            //         label: '物料编码',
            //         field: 'itemCode',
            //         type: 'string',
            //         operator: 'equal',
            //         value: this.modalData.itemCode
            //       },
            //       {
            //         label: '物料名称',
            //         field: 'itemCode',
            //         type: 'string',
            //         operator: 'equal',
            //         value: this.modalData.itemName
            //       }
            //     ]
            //   }
            // }
          }
        }
      ],
      defaultOn: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('关闭') }
        }
      ]
    }
  },
  mounted() {
    console.log('dialogData', this.modalData)
    this.$refs.dialog.ejsRef.show()
    this.searchFormModel.itemCode = this.modalData.itemCode
    this.searchFormModel.itemName = this.modalData.itemName
    // this.init();
    this.siteCodeChange({ itemData: { value: this.searchFormModel.siteCode } })
  },

  methods: {
    handleClickToolBar(e) {
      const { toolbar } = e
      if (toolbar.id === 'export') {
        this.handleExport({
          itemCode: this.modalData.itemCode,
          siteCode: this.searchFormModel.siteCode
        })
      }
    },
    handleExport(params) {
      this.$store.commit('startLoading')
      this.$API.drawingTogether
        .purQueryDrawingBomExport(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    siteCodeChange(e) {
      if (e.itemData) {
        this.$store.commit('startLoading')
        this.$API.drawingTogether
          .purQueryDrawingBom({
            itemCode: this.modalData.itemCode,
            siteCode: e.itemData?.value
          })
          .then((res) => {
            if (res && res.code === 200) {
              this.$set(this.pageConfig[0].grid, 'dataSource', res.data)
            }
          })
          .finally(() => {
            this.$store.commit('endLoading')
          })
      }
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.demo-block .topBox {
  display: flex;
  justify-content: space-between;
  .box {
    font-size: 17px;
  }
}
/deep/ .box {
  margin-top: 5px;
  .mt-form-item-topLabel {
    display: flex;
    flex-wrap: wrap;
    .label {
      width: 100%;
    }
  }
}
/deep/ .dialog-deliver {
  .e-dlg-content {
    padding: 1rem;
  }
}
</style>
<style lang="scss">
.dialog-deliver {
  .e-dlg-content {
    padding: 10px;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
        }
      }
    }
  }
}
</style>
