<!-- 图纸管理-dwg图纸 -->
<template>
  <div>
    <sc-table
      ref="sctableRef"
      grid-id="2921cbe3-33a6-4518-8372-f42e624b7f34"
      :loading="loading"
      :is-show-refresh-bth="false"
      :columns="columns"
      :table-data="tableData"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #fileDefault="{ row }">
        <div v-for="item in row.docList" :key="item.docId">
          <div style="cursor: pointer; color: #2783fe" @click="handleClick(item)">
            {{ item.cadName }}
          </div>
        </div>
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { columnData } from './config/index'

export default {
  components: { ScTable },
  data() {
    return {
      searchFormModel: {},
      toolbar: [{ code: 'back', name: this.$t('返回'), status: 'info', loading: false }],
      columns: columnData,
      loading: false,
      tableData: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getData()
  },
  methods: {
    getData() {
      let params = {
        itemCode: this.$route.query?.itemCode
      }
      this.$API.drawingTogether.queryPlmCadDocApi(params).then((res) => {
        if (res.code === 200) {
          this.tableData = [res.data]
        }
      })
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.$router.go(-1)
          break
        default:
          break
      }
    },
    handleClick(item) {
      window.open(item.url)
    }
  }
}
</script>
