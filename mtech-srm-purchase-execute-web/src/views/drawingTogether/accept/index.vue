<template>
  <div class="full-height pt20">
    <mt-template-page
      slot="slot-0"
      ref="templateRef"
      :template-config="pageConfig1"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
    <deliver
      @handleAddDialogShow="handleAddDialogShow"
      @resolveClick="resolveClick"
      ref="headerTop"
      v-if="deliveryShow"
      class="flex-keep"
    ></deliver>
  </div>
</template>

<script>
import { columnData } from './config/index.js'
// import deliveryDialog from "./components/deliveryDialog";
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  mounted() {},
  components: {
    deliver: require('./components/deliver.vue').default
  },
  data() {
    return {
      id: [],
      deliveryShow: false,
      pageConfig: [{ title: this.$t('头视图') }],
      userInfo: null,
      addDialogShow: false,
      currentTabIndex: 0,
      pageConfig1: [
        {
          useToolTemplate: false,
          buttonQuantity: 7,
          toolbar: {
            tools: [
              [
                // {
                //   id: "new",
                //   icon: "icon_solid_Download",
                //   title: this.$t("下载"),
                //   // permission: ["O_02_0122"],
                // },
                {
                  id: 'editsvg',
                  icon: 'icon_solid_editsvg',
                  title: this.$t('异常反馈')
                  // permission: ["O_02_0122"],
                },
                {
                  id: 'export',
                  icon: 'icon_solid_export',
                  title: this.$t('导出')
                  // permission: ["O_02_0122"],
                },
                {
                  id: 'view_bom',
                  icon: 'icon_table_new',
                  title: this.$t('查看BOM'),
                  permission: ['O_02_1420']
                },
                {
                  id: 'view_dwg_drawing',
                  icon: 'icon_table_new',
                  title: this.$t('查看dwg图纸'),
                  permission: ['O_02_1442']
                },
                {
                  id: 'check',
                  title: this.$t('查看印刷品序列变量')
                },
                {
                  id: 'search',
                  title: this.$t('印刷件份数查询')
                },
                {
                  id: 'tips', // 通过button添加tips
                  icon: '',
                  title: this.$t('“物料名称”为主数据信息，非工厂级别数据')
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          gridId: '6a4c0c33-0c81-43e8-bcff-07a88a7da34b',
          grid: {
            columnData: columnData,
            lineIndex: 1,
            autoWidthColumns: columnData.length + 1,
            asyncConfig: {
              url: `/sourcing/tenant/drawings/sup/query`
            },
            frozenColumns: 1
          }
        }
      ],

      dialogData: null
    }
  },
  methods: {
    resolveClick(a) {
      this.resolvechange(a)
      this.deliveryShow = false
    },
    resolvechange(a) {
      let obj = {
        errorStatus: 1,
        idList: this.id,
        supplierReason: a
      }
      this.$API.drawingTogether.supFeedback(obj).then(() => {
        this.deliveryShow = false
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },

    // 弹框
    handleDialogShow(flag) {
      this.deliveryShow = flag
    },
    handleSelectTab(e) {
      this.currentTabIndex = e
    },
    // 点击单元格 标题
    handleClickCellTitle(e) {
      // if (e.data.status == "1") {
      if (e.field === 'receiveOrderCode') {
        localStorage.setItem('purchaseListScope', JSON.stringify(e.data))
        this.$router.push(`purchase-detail-picking?receiveOrderCode=${e.data.receiveOrderCode}`)
      }

      // } else if (e.data.status == "0") {
      //   this.$router.push(`deliver-detail-supplier?id=${e.data.id}&type=no`);
      // }
    },
    // handleClickCellTool(e) {
    //   console.log(e);
    //   if (e.tool.title === this.$t("确认")) {
    //     this.certain(e.data.id);
    //   }
    //   if (e.tool.title === this.$t("退回")) {
    //     this.id.push(e.data.id);
    //     this.deliveryShow = true;
    //   }
    // },
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    handleClickToolBar(e) {
      console.log(e)
      let _records = e.grid.getSelectedRecords()
      if (
        ['editsvg', 'view_bom', 'view_dwg_drawing', 'check', 'search'].includes(e.toolbar.id) &&
        _records?.length <= 0
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (['view_dwg_drawing', 'view_bom'].includes(e.toolbar.id) && _records.length > 1) {
        this.$toast({
          type: 'warning',
          content: this.$t('只能查看单条数据的BOM或图纸')
        })
        return
      }
      if (['search'].includes(e.toolbar.id) && _records.length > 1) {
        this.$toast({
          type: 'warning',
          content: this.$t('只能查询单条数据')
        })
        return
      }
      let _id = []

      e.grid.getSelectedRecords().map((item) => {
        _id.push(item.id)

        // _sourceStatus.push(item.sourceStatus),
        // _remainingQuantity.push(item.remainingQuantity);
      })
      this.id = _id
      if (e.toolbar.id === 'editsvg') {
        this.deliveryShow = true
      } else if (e.toolbar.id == 'export') {
        // 导出
        this.exportExcel()
      } else if (e.toolbar.id === 'view_bom') {
        this.$dialog({
          modal: () => import('./components/viewBomDialog.vue'),
          data: {
            ..._records[0]
          },
          success: () => {}
        })
      } else if (e.toolbar.id === 'view_dwg_drawing') {
        this.$router.push({
          name: 'drawings-dwg',
          query: {
            itemCode: _records[0].itemCode,
            timeStamp: new Date().getTime()
          }
        })
      } else if (e.toolbar.id === 'check') {
        let itemCode = ''
        _records.forEach((item) => {
          itemCode = itemCode + ' ' + item.itemCode
        })
        this.$router.push({
          path: `variable-query-supplier`,
          query: {
            itemCode: itemCode.trim(),
            timeStamp: new Date().getTime()
          }
        })
      } else if (e.toolbar.id === 'search') {
        this.$store.commit('startLoading')
        this.$API.drawingTogether
          .getItemParentDosageApi({ itemCode: _records[0].itemCode })
          .then((res) => {
            if (res.code == 200) {
              this.$dialog({
                modal: () => import('./components/printSeachDialog.vue'),
                data: {
                  tableData: res.data
                },
                success: () => {}
              })
            }
          })
          .finally(() => {
            this.$store.commit('endLoading')
          })
      }
    },
    // 查看dwg图纸
    handleClickViewDwgDrawing(row) {
      this.$API.drawingTogether.purQueryDwgDrawing({ itemCode: row.itemCode }).then((res) => {
        if (res.code == 200) {
          window.open(res.data)
        }
      })
    },
    // Excel导出
    exportExcel() {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.drawingTogether.supExportDrawing(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
  // 消失
  // beforeDestroy() {
  //   localStorage.removeItem("deliverDetailSupplier");
  // },
}
</script>

<style lang="scss" scoped>
/deep/.optionBtn {
  color: #6386c1;
  cursor: pointer;
  margin-right: 10px;
}

// 特殊button处理
/deep/.toolbar-container .mt-flex:first-child .toolbar-item:last-child {
  cursor: text;
  background: none;
  box-shadow: unset;
  span {
    color: red;
    border: unset;
  }
}
</style>
