<template>
  <mt-dialog
    ref="dialog"
    :header="$t('印刷件份数查询')"
    :buttons="buttons"
    width="70%"
    @close="handleClose"
    :open="onOpen"
  >
    <div class="full-height">
      <sc-table
        ref="sctableRef"
        row-id="id"
        grid-id="e45a0b5d-af68-41c6-b2d7-8dccd82d0cf3"
        show-overflow
        keep-source
        :loading="loading"
        :columns="columns"
        :table-data="tableData"
        :fix-height="400"
        :is-show-right-btn="false"
        :is-show-refresh-bth="false"
      >
      </sc-table>
      <div style="margin-top: 10px">{{ $t('总条数') }}：{{ total }}</div>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: { ScTable },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('关闭') }
        }
      ],
      loading: false,
      tableData: []
    }
  },
  computed: {
    columns() {
      return [
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50
        },
        {
          field: 'parentItemCode',
          title: this.$t('产品编码')
        },
        {
          field: 'parentItemName',
          title: this.$t('产品名称')
        },
        {
          field: 'parentItemDesc',
          title: this.$t('产品描述')
        },
        {
          field: 'dosage',
          title: this.$t('份数')
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码')
        },
        {
          field: 'itemName',
          title: this.$t('物料名称')
        },
        {
          field: 'itemDesc',
          title: this.$t('物料描述')
        }
      ]
    },
    total() {
      return this.tableData.length || 0
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.tableData = this.modalData.tableData
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
