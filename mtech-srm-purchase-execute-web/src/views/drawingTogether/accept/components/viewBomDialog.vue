<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :header="$t('查看BOM')"
    :buttons="buttons"
    :dialog-data="modalData"
    width="70%"
    height="80%"
    @close="handleClose"
    :open="onOpen"
  >
    <div class="dialog-content">
      <mt-form ref="searchForm" :model="searchFormModel">
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('工厂')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.siteCode"
                url="/masterDataManagement/auth/site/auth-fuzzy"
                :multiple="false"
                :placeholder="$t('请选择')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                params-key="fuzzyParam"
                records-position="data"
                @change="siteCodeChange"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('物料编码')" label-style="top">
              <mt-input v-model="searchFormModel.itemCode" :disabled="true" />
            </mt-form-item>
          </mt-col>
          <mt-col :span="10">
            <mt-form-item :label="$t('物料名称')" label-style="top">
              <mt-input v-model="searchFormModel.itemName" :disabled="true" />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
      <div>
        <sc-table
          ref="sctableRef"
          grid-id="ef621939-e29e-4833-8a3a-ed3b0c113eca"
          :columns="columns"
          :table-data="tableData"
          :loading="loading"
          :tree-config="{}"
          :is-show-refresh-bth="true"
          @refresh="handleSearch"
        >
          <template slot="custom-tools">
            <vxe-button
              v-for="item in toolbar"
              :key="item.code"
              :status="item.status"
              :icon="item.icon"
              :loading="item.loading"
              size="small"
              @click="handleClickToolBar(item)"
            >
              {{ item.name }}
            </vxe-button>
          </template>
          <template #fileDefault="{ row }">
            <div style="cursor: pointer; color: #2783fe" @click="handleClick(row)">
              {{ row.drawingName }}
            </div>
          </template>
        </sc-table>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: { ScTable },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('关闭') }
        }
      ],
      searchFormModel: {
        siteCode: '5540',
        itemCode: '',
        itemName: ''
      },
      loading: false,
      tableData: [],
      toolbar: [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 100,
          fixed: 'left',
          align: 'left',
          treeNode: true
        },
        {
          field: 'itemCode',
          title: this.$t('物料编号')
        },
        {
          field: 'itemName',
          title: this.$t('物料名称')
        },
        {
          field: 'count',
          title: this.$t('数量')
        },
        {
          field: 'unit',
          title: this.$t('单位')
        },
        {
          field: 'parentItemCode',
          title: this.$t('父物料编号')
        },
        {
          field: 'parentItemName',
          title: this.$t('父物料名称')
        },
        {
          field: 'createTime',
          title: this.$t('有效起始日')
        }
      ]
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.searchFormModel.itemCode = this.modalData.itemCode
    this.searchFormModel.itemName = this.modalData.itemName
  },

  methods: {
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleExport(e) {
      let params = {
        itemCode: this.modalData.itemCode,
        siteCode: this.searchFormModel.siteCode
      }
      this.$API.drawingTogether
        .exportBomSupApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    siteCodeChange(e) {
      if (e.itemData) {
        this.handleSearch()
      }
    },
    handleSearch() {
      this.loading = true
      this.$API.drawingTogether
        .queryBomSupApi({
          itemCode: this.modalData.itemCode,
          siteCode: this.searchFormModel.siteCode
        })
        .then((res) => {
          if (res && res.code === 200) {
            this.tableData = res.data
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    .dialog-content {
      padding: 20px 0;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
        }
      }
    }
  }
}
</style>
