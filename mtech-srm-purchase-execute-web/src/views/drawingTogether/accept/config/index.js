import UTILS from '@/utils/utils'
import Vue from 'vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'feedbackStatus',
    headerText: i18n.t('反馈状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: '', cssClass: '' },
        { value: 1, text: i18n.t('无权查看'), cssClass: '' },
        { value: 2, text: i18n.t('已查看'), cssClass: '' },
        { value: 3, text: i18n.t('已反馈'), cssClass: '' }
      ]
    }
  },
  {
    width: '150',
    field: 'customerName',
    headerText: i18n.t('客户')
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    searchOptions: { maxQueryValueLength: 10000 }
  },
  {
    width: '150',

    field: 'itemText',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'originalModelCode',
    headerText: i18n.t('原机型编码')
  },
  // {
  //   width: "150",
  //   field: "drawingsType",
  //   headerText: i18n.t("图纸类型"),
  //   valueConverter: {
  //     type: "map",
  //     map: [
  //       { value: 0, text: i18n.t("2D"), cssClass: "" },
  //       { value: 1, text: i18n.t("3D"), cssClass: "" },
  //       { value: 2, text: i18n.t("美工"), cssClass: "" },
  //     ],
  //   },
  // },

  // {
  //   width: "150",
  //   field: "drawingsName",
  //   headerText: i18n.t("图纸名称"),
  //   // editTemplate: () => {
  //   //   return {
  //   //     template: "1212",
  //   //   };
  //   // },
  //   // template: timeDate("sendTime", true),
  // },
  {
    width: '150',
    field: 'supplierReason',
    headerText: i18n.t('反馈原因')
  },
  // {
  //   width: "150",
  //   field: "version",
  //   headerText: i18n.t("版本"),
  // },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('图纸接受时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !e.includes('-')) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return e
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  // {
  //   width: "150",
  //   field: "publishTime",
  //   headerText: i18n.t("更新时间"),
  //   valueConverter: {
  //     type: "function",
  //     filter: (e) => {
  //       if (e) {
  //         e = Number(e);
  //         return UTILS.dateFormat(e);
  //       } else {
  //         return e;
  //       }
  //     },
  //   },
  // },

  {
    width: '150',
    field: 'feedbackTime',
    headerText: i18n.t('反馈时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !e.includes('-')) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return e
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    width: '250',
    field: 'operator',
    headerText: i18n.t('操作'),
    ignore: true,
    template: function () {
      return {
        template: Vue.component('sortIpt', {
          template: `<div class="sortIptNumber">
                      <span class="optionBtn" style="cursor: pointer" @click="viewDetail">{{ $t('查看图纸') }}</span>
                      <span class="optionBtn" style="cursor: pointer" @click="handleCheck">{{ $t('查看印刷品序列变量') }}</span>
                    </div>`,
          data() {
            return {}
          },
          mounted() {},
          methods: {
            viewDetail() {
              // this.$API.drawingTogether.supQueryDrawing({ id: this.data.id }).then((res) => {
              //   if (res.code == 200) {
              //     window.open(res.data)
              //   }
              // })
              this.$router.push({
                name: 'drawings-check-sup',
                query: {
                  itemCode: this.data.itemCode,
                  timeStamp: new Date().getTime()
                }
              })
            },
            handleCheck() {
              this.$router.push({
                path: `variable-query-supplier`,
                query: {
                  itemCode: this.data.itemCode,
                  timeStamp: new Date().getTime()
                }
              })
            }
          }
        })
      }
    }
  }
]
