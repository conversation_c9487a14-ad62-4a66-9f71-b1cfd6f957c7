<!-- SAP库存查询 - 供方 -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
          <mt-form-item prop="siteCodeList" :label="$t('工厂')">
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.siteCodeList"
              :url="$API.masterData.getSiteAuthFuzzyUrl"
              multiple
              :placeholder="$t('请选择')"
              :fields="{ text: 'siteName', value: 'siteCode' }"
              params-key="fuzzyParam"
              records-position="data"
              @change="siteCodeChange"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="positionList" :label="$t('库位')">
            <mt-multi-select
              v-model="searchFormModel.positionList"
              type="multipleChoice"
              :placeholder="
                searchFormModel.siteCodeList.length === 0 ? $t('请选择工厂') : $t('请选择')
              "
              :show-clear-button="true"
              :show-select-all="true"
              :allow-filtering="true"
              :disabled="searchFormModel.siteCodeList.length === 0"
              :data-source="positionOptions"
              :fields="{ text: 'locationName', value: 'locationCode' }"
            />
          </mt-form-item>
          <mt-form-item prop="itemCodeList" :label="$t('物料编码')">
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.itemCodeList"
              :url="$API.masterData.getItemUrl"
              multiple
              :placeholder="$t('请选择')"
              :fields="{ text: 'itemName', value: 'itemCode' }"
              :search-fields="['itemName', 'itemCode']"
            ></RemoteAutocomplete>
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { componentConfig } from './config/index'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      componentConfig: componentConfig(this),
      searchFormModel: {
        siteCodeList: [],
        positionList: [],
        itemCodeList: []
      },
      searchFormRules: {
        siteCodeList: [
          {
            required: true,
            message: this.$t('请选择工厂'),
            trigger: 'change'
          }
        ],
        positionList: [
          {
            required: true,
            message: this.$t('请选择库位'),
            trigger: 'change'
          }
        ]
      },
      positionData: [],
      positionOptions: []
    }
  },
  created() {
    this.getPositionOptions()
  },
  methods: {
    getPositionOptions() {
      this.$API.receiptAndDelivery.getPositionOptionsApi().then((res) => {
        if (res.code === 200) {
          this.positionData = res.data
        }
      })
    },
    siteCodeChange() {
      this.positionOptions = []
      this.positionData.forEach((item) => {
        if (this.searchFormModel.siteCodeList.includes(item.siteCode)) {
          this.positionOptions.push(...item.locations)
        }
      })
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = []
        }
      }
    }
  }
}
</script>
