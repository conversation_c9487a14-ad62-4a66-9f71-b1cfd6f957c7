import { i18n } from '@/main.js'

const columnData = () => {
  const column = [
    {
      field: 'siteCode',
      headerText: i18n.t('工厂')
    },
    {
      field: 'position',
      headerText: i18n.t('库位')
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码')
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称')
    },
    {
      field: 'stockNum',
      headerText: i18n.t('库存数量')
    }
  ]
  return column
}

export const componentConfig = (that) => {
  const config = [
    {
      isUseCustomSearch: true, // 是否使用自定义查询
      isCustomSearchRules: true,
      useToolTemplate: false, // 此项不使用预置的表格操作按钮'新增、编辑、删除'
      useBaseConfig: false,
      toolbar: [],
      grid: {
        allowPaging: false, // 是否使用内置分页器
        lineIndex: 0,
        columnData: columnData(that),
        asyncConfig: {
          url: '/srm-purchase-execute/tenant/tvOutSouring/queryTvSapInventory',
          ignoreDefaultSearch: true,
          recordsPosition: 'data'
        }
      }
    }
  ]
  return config
}
