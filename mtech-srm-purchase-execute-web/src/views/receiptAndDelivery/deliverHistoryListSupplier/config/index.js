import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import Vue from 'vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, isDateTime, isDate, isTime } = args

  const template = () => {
    return {
      template: Vue.component('timeDateComponent', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <div v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</div>
            <div v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</div>
          </div>
        </div>`,
        data: function () {
          return { data: {}, dataKey, isDateTime, isDate, isTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
export const columnData2 = [
  {
    width: '200',
    field: 'deliveryCode',
    cellTools: [],
    headerText: i18n.t('送货单号')
  },
  {
    width: '150',
    field: 'deliveryLineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '90',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          return e.label
        }
        return ''
      }
    }
  },
  {
    width: '100',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '200',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    ignore: true
  },
  {
    width: '123',
    field: 'deliveryQuantity',
    headerText: i18n.t('本次送货数量')
  },
  {
    width: '98',
    field: 'receiveQuantity',
    headerText: i18n.t('收货数量')
  },
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('采购订单号')
  },
  {
    width: '130',
    field: 'lineNo',
    headerText: i18n.t('采购订单行号')
  },
  {
    width: '100',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '200',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '135',
    field: 'warehouseName',
    headerText: i18n.t('库存地点'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.warehouseCode}}-{{data.warehouseName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '100',
    field: 'sendTime',
    headerText: i18n.t('发货日期'),
    template: timeDate({ dataKey: 'sendTime', isDateTime: true })
  },
  {
    field: 'onWayStatus',
    headerText: i18n.t('在途状态'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          return e.label
        }
        return ''
      }
    }
  },
  {
    width: '75',
    field: 'takeNo',
    headerText: i18n.t('车牌号')
  },
  {
    field: 'vehicleLogistics',
    headerText: i18n.t('车辆物流'),
    template: () => {
      return {
        template: Vue.component('template-detail', {
          template: `
              <span v-if="data.takeNo" style="color: #6386c1; cursor: pointer;" @click="toLogistics(data)">查看物流</span>`,
          methods: {
            toLogistics(e) {
              const params = {
                ztpno: e?.deliveryCode?.toString(),
                busCode: e?.forecastCode,
                busNum: e.takeNo
              }
              this.$API.receiptAndDelivery.purQueryVehicleLogistics(params).then((res) => {
                if (res.code === 200) {
                  window.open(res.data.mapURL)
                }
              })
            }
          }
        })
      }
    }
  },
  {
    width: '100',
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    width: '200',
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    width: '85',
    field: 'jit',
    headerText: i18n.t('是否JIT'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },
  {
    width: '150',
    field: 'jitDeliveryNumber',
    headerText: i18n.t('JIT编号')
  },
  {
    width: '100',
    field: 'demandDate',
    headerText: i18n.t('需求日期')
  },
  {
    width: '100',
    field: 'rejectQuantity',
    headerText: i18n.t('拒绝数量')
  },
  {
    width: '100',
    field: 'rejectReason',
    headerText: i18n.t('拒绝原因')
  },
  {
    field: 'inputDate',
    headerText: i18n.t('凭证创建日期'),
    template: timeDate({ dataKey: 'inputDate', isDateTime: true })
  },
  {
    field: 'postingDate',
    headerText: i18n.t('过账日期'),
    template: timeDate({ dataKey: 'postingDate', isDate: true })
  },
  {
    width: '100',
    field: 'deliveryType',
    headerText: i18n.t('送货单类型'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          return e.label
        }
        return ''
      }
    }
  },
  {
    width: '100',
    field: 'sendAddressName',
    headerText: i18n.t('发货地点')
  },
  {
    width: '130',
    field: 'thirdTenantCode',
    headerText: i18n.t('第三方物流商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.thirdTenantCode}}-{{data.thirdTenantName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '110',
    field: 'receiveSupplierName',
    headerText: i18n.t('收货供应商')
  },
  {
    field: 'deliveryRemark',
    headerText: i18n.t('送货单备注'),
    width: '120'
  },
  {
    width: '90',
    field: 'transferPlanName',
    headerText: i18n.t('计划员')
  },
  {
    width: '175',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '100',
    field: 'workOrderNo',
    headerText: i18n.t('关联工单号')
  },
  {
    width: '140',
    field: 'saleOrderNo',
    headerText: i18n.t('关联销售订单号')
  },
  {
    width: '150',
    field: 'saleOrderLineNo',
    headerText: i18n.t('关联销售订单行号')
  },
  {
    width: '85',
    field: 'bomCode',
    headerText: i18n.t('BOM号')
  },
  {
    width: '120',
    field: 'productCode',
    headerText: i18n.t('关联产品代码')
  },
  {
    width: '100',
    field: 'workCenterName',
    headerText: i18n.t('工作中心')
  },
  {
    width: '100',
    field: 'processName',
    headerText: i18n.t('工序名称')
  },
  {
    width: '100',
    field: 'receiveAddressName',
    headerText: i18n.t('收货地址')
  },
  {
    width: '80',
    field: 'unitName',
    headerText: i18n.t('单位')
  },
  {
    width: '150',
    field: 'batchCode',
    headerText: i18n.t('批次号')
  },
  {
    width: '100',
    field: 'limitQuantity',
    headerText: i18n.t('限量数量')
  },
  {
    width: '86',
    field: 'warehouseClerkName',
    headerText: i18n.t('仓管员')
  },
  {
    width: '86',
    field: 'dispatcherName',
    headerText: i18n.t('调度员')
  },
  {
    field: 'wmsInstockQty',
    headerText: i18n.t('WMS入库数量'),
    width: '120'
  },
  {
    field: 'wmsInstockTime',
    headerText: i18n.t('WMS入库时间'),
    width: '120'
  },
  {
    width: '86',
    field: 'cancelPersonName',
    headerText: i18n.t('取消人'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '95',
    field: 'cancelTime',
    headerText: i18n.t('取消时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    template: timeDate({ dataKey: 'cancelTime', isDateTime: true })
  },
  {
    width: '86',
    field: 'closePersonName',
    headerText: i18n.t('关闭人'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '95',
    field: 'closeTime',
    headerText: i18n.t('关闭时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    template: timeDate({ dataKey: 'closeTime', isDateTime: true })
  },
  {
    width: '95',
    field: 'collectorMark',
    headerText: i18n.t('代收标识'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '86',
    field: 'collectorName',
    headerText: i18n.t('代收人'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '86',
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    width: '86',
    field: 'remark',
    headerText: i18n.t('行备注')
  },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  }
]
