<template>
  <div style="height: 100%">
    <mt-template-page
      ref="templateRef1"
      :permission-obj="permissionObj"
      :template-config="pageConfig1"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle1"
      @handleCustomReset="handleSearchReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="deliveryCode" :label="$t('送货单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.deliveryCode"
                :placeholder="$t('请输入送货单号')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建日期')">
              <mt-date-range-picker
                v-model="searchFormModel.createTime"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'createTime')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
import { columnData2 } from './config'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName, codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {
        createTimeS: this.getUnix(dayjs().subtract(1, 'year').format('YYYY-MM-DD 00:00:00')),
        createTimeE: this.getUnix(dayjs().format('YYYY-MM-DD 23:59:59'))
      },
      supplierOptions: [], // 供应商下拉选项
      siteOptions: [], // 工厂 下列选项
      supplierCodeValueTemplate: codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      }),
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      companyCodeValueTemplate: codeNameColumn({
        firstKey: 'orgCode',
        secondKey: 'orgName'
      }),
      itemCodeValueTemplate: codeNameColumn({
        firstKey: 'itemCode',
        secondKey: 'itemName'
      }),
      itemCodeOptions: [], // 物料下拉
      companyOptions: [], // 公司 下拉选项
      // 1-新建,2-发货中,3-已完成,4-已取消,5-已关闭
      statusOptions: [
        { text: this.$t('送货中'), value: 2 },
        { text: this.$t('已完成'), value: 3 },
        { text: this.$t('已取消'), value: 4 },
        { text: this.$t('已关闭'), value: 5 }
      ],
      onWayStatusOptions: [
        { text: i18n.t('未出发'), value: 0 },
        { text: i18n.t('已入园'), value: 1 },
        { text: i18n.t('已出发'), value: 2 },
        { text: i18n.t('已报到'), value: 3 },
        { text: i18n.t('已取消'), value: 4 },
        { text: i18n.t('已关闭'), value: 5 }
      ],

      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'List', permissionCode: 'T_02_0163' },
          { dataPermission: 'Details', permissionCode: 'T_02_0164' }
        ]
      },
      pageConfig1: [
        {
          dataPermission: 'Details',
          activatedRefresh: false,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                // {
                //   id: 'export',
                //   icon: 'icon_solid_Import',
                //   permission: ['O_02_1373'],
                //   title: this.$t('导出')
                // }
              ],
              ['Refresh', 'Setting']
            ]
          },
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          gridId: '9bd07d71-9b2f-42b4-8ce1-6befd8283478',
          grid: {
            virtualPageSize: 30,
            showSelected: false,
            selectionSettings: {
              persistSelection: true,
              type: 'Multiple',
              checkboxOnly: true
            },
            enableVirtualization: true,
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [10, 20, 50, 100, 200, 500, 1000],
              totalRecordsCount: 0
            },
            lineIndex: 0, // 序号列
            columnData: columnData2,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/delivery/item/archive/supplier/page` // 采方送货单-采方查询送货单明细分页
            }
          }
        }
      ]
    }
  },
  mounted() {
    if (this.$route.query.from === 'mytodo') {
      this.tabIndex = 1
      this.pageConfig1[0].grid.asyncConfig.url = `${BASE_TENANT}/delivery/item/archive/supplier/page?fromWorkCenter=1`
      this.pageConfig1[0].grid.asyncConfig.defaultRules = JSON.parse(
        sessionStorage.getItem('todoDetail')
      ).defaultRules
    }
  },
  methods: {
    dataTimeChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'S'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[flag + 'E'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[flag + 'S'] = null
        this.searchFormModel[flag + 'E'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).unix()
    },
    handleClickCellTitle1(e) {
      if (e.field === 'deliveryCode') {
        this.getDeliverDataById(e.data?.deliveryId)
      }
    },
    // 根据id查询主单数据 - 获取物料信息并跳转物料页面
    getDeliverDataById(id) {
      let params = { id }
      this.$API.receiptAndDelivery
        .getHistorySupplierDeliveyDataTV(params)
        .then((res) => {
          if (res?.code == 200) {
            // 跳转详情页

            localStorage.setItem('deliverHistoryDetailSupplier', JSON.stringify(res.data))
            this.$router.push(
              `deliver-history-detail-supplier?id=${res?.data?.id}&type=${
                res?.data?.deliveryType
              }&timeStamp=${new Date().getTime()}`
            )
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 获取主数据-获取指定组织下指定组织层级节点列表 物料信息
    getItemCode(args) {
      const { text, updateData, setSelectData } = args
      let params = {
        keyword: text || this.searchFormModel.itemCode || '',
        pageSize: 50
      }
      this.$API.masterData
        .getItemByKeyword(params)
        .then((res) => {
          if (res) {
            const list = res.data?.records || []
            this.itemCodeOptions = addCodeNameKeyInList({
              firstKey: 'itemCode',
              secondKey: 'itemName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.itemCodeOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-获取指定组织下指定组织层级节点列表 业务公司
    getCompany(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true,
        organizationIds: []
      }
      this.$API.masterData
        .OrgFindSpecifiedChildrenLevelOrgs(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.companyOptions = addCodeNameKeyInList({
              firstKey: 'orgCode',
              secondKey: 'orgName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.companyOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        page: {
          current: 1,
          pages: 0,
          size: 20
        }
      }
      this.$API.receiptAndDelivery
        .getFactoryInfo(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 公司 change
    companyCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.companyId = itemData.id
        this.searchFormModel.companyCode = itemData.orgCode
        this.searchFormModel.companyName = itemData.orgName
      } else {
        this.searchFormModel.companyId = ''
        this.searchFormModel.companyCode = ''
        this.searchFormModel.companyName = ''
      }
    },
    // 物料 change
    itemCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.itemCode = itemData.itemCode
        this.searchFormModel.itemName = itemData.itemName
      } else {
        this.searchFormModel.itemCode = ''
        this.searchFormModel.itemName = ''
      }
    },
    // 工厂 change
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        // this.searchFormModel.siteId = itemData.id;
        this.searchFormModel.siteCode = itemData.siteCode
        this.searchFormModel.siteName = itemData.siteName
      } else {
        // this.searchFormModel.siteId = "";
        this.searchFormModel.siteCode = ''
        this.searchFormModel.siteName = ''
      }
    },
    // ToolBar
    handleClickToolBar(args) {
      // if (args.toolbar.id === 'resetDataByLocal') return
      if (args.toolbar.id == 'export') {
        const queryBuilderRules =
          this.$refs.templateRef1.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        console.log(queryBuilderRules)
        const params = {
          page: { current: 1, size: 10000 },
          // ...queryBuilderRules
          ...this.searchFormModel
        } // 筛选条件
        this.$store.commit('startLoading')
        this.$API.receiptAndDelivery.supplierOrderDeliveryQueryExportNewTv(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
    },
    handleSearchReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'createTime') {
            this.searchFormModel.createTimeS = this.getUnix(
              dayjs().subtract(1, 'year').format('YYYY-MM-DD 00:00:00')
            )
            this.searchFormModel.createTimeE = this.getUnix(dayjs().format('YYYY-MM-DD 23:59:59'))
          }
        }
      }
    }
  }
}
</script>

<style></style>
