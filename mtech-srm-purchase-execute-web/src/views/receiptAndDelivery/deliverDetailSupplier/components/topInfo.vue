<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 测试用字段prSubmitFlag:{{ prSubmitFlag }} -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="status mr20">
        {{ typeInfo(headerInfo.status) }}
      </div>
      <div class="infos mr20">{{ $t('更新人：') }}{{ getUpdateUserName(headerInfo) }}</div>
      <div class="infos">{{ $t('更新日期：') }}{{ getUpdateTime(headerInfo) }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧各种操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <!-- 草稿和审批拒绝状态可以保存草稿 -->
      <!-- <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-if="type === 'put"
        @click="startToSave(1)" -->
      <!-- >{{ $t("保存草稿") }}</mt-button -->
      <!-- > -->
      <!-- 草稿和审批拒绝状态可以提交 -->
      <!-- <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-if="type === 'add"
        @click="startToSave(2)"
        >{{ $t("提交") }}</mt-button
      > -->
      <!-- <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-if="type === 'no"
        @click="startToSave(1)"
        >{{ $t("补订单") }}</mt-button -->
      <!-- > -->
      <div class="sort-box" @click="isExpand = !isExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="addForm" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="deliveryCode" :label="$t('送货单编号')">
          <mt-input v-model="headerInfo.deliveryCode" :disabled="true" :maxlength="50"></mt-input>
        </mt-form-item>
        <mt-form-item prop="receiverContactName" :label="$t('供应商')">
          <mt-input v-model="headerInfo.supplierName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="siteName" :label="$t('工厂')">
          <mt-input
            v-model="headerInfo.siteName"
            :show-clear-button="false"
            :disabled="true"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="warehouseName" :label="$t('交货库存地点')">
          <mt-input v-model="headerInfo.warehouseName" :disabled="true" :maxlength="50"></mt-input>
        </mt-form-item>
        <mt-form-item prop="sendTime" :label="$t('发货日期')">
          <mt-date-time-picker v-model="headerInfo.sendTime" :disabled="true"></mt-date-time-picker>
        </mt-form-item>
        <mt-form-item prop="forecastArriveTime" :label="$t('预计到货日期')">
          <mt-date-time-picker
            :disabled="true"
            v-model="headerInfo.forecastArriveTime"
          ></mt-date-time-picker>
        </mt-form-item>
        <mt-form-item prop="companyName" :label="$t('公司')">
          <mt-input v-model="headerInfo.companyName" :disabled="true"></mt-input>
        </mt-form-item>

        <mt-form-item prop="deliveryType" :label="$t('送货单类型')">
          <mt-input v-model="headerInfo.deliveryType" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="shipType" :label="$t('送货方式')">
          <mt-input ref="businessRef" v-model="headerInfo.shipType" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="sendAddressName" :label="$t('发货地点')">
          <mt-input
            v-model="headerInfo.sendAddressName"
            :disabled="true"
            :maxlength="50"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="receiverContactName" :label="$t('收货联系人')">
          <mt-input v-model="headerInfo.receiverContactName" :disabled="true"></mt-input>
        </mt-form-item>

        <mt-form-item prop="receiverContact" :label="$t('联系人方式')">
          <mt-input v-model="headerInfo.receiverContact" :disabled="true"></mt-input>
        </mt-form-item>

        <mt-form-item prop="receiverContactName" :label="$t('加工商')">
          <mt-input v-model="headerInfo.receiverName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="thirdTenantCode" :label="$t('第三方物流商')">
          <mt-input v-model="thirdTenant" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="vmiWarehouseCode" :label="$t('VMi仓')">
          <mt-input v-model="vmiWarehouse" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="outsourcedType" :label="$t('委外方式')">
          <mt-input
            ref="businessRef"
            v-model="headerInfo.outsourcedType"
            :disabled="true"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="vmiWarehouseTypeDisplay" :label="$t('VMI仓类型')">
          <mt-input v-model="vmiWarehouseTypeDisplay" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="outboundWarehouse" :label="$t('出货库位')">
          <mt-input v-model="outboundWarehouse" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input
            :multiline="true"
            v-model="headerInfo.remark"
            :maxlength="200"
            :disabled="true"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {
        return 1212
      }
    }
  },
  data() {
    return {
      isInit: true, // 是第一次赋值
      isExpand: true,
      type: '', // add view edit
      vmiWarehouse: '',
      businessTypeList: [], // 业务类型
      applyUserIdData: [], // 申请人列表
      organizationData: {},
      applyCompanyData: [], // 公司
      applyDepartData: [], // 部门
      addForm: {
        title: '', // 采购申请名称
        businessTypeId: '', // 业务类型
        // businessTypeCode: "",
        // businessTypeName: "",
        applyUserId: '', // 申请人
        // applyUserName: "",
        // applyUserCode: ""
        companyId: '', // 公司
        // companyName: "",
        // companyCode: ""
        applyDepId: '', // 申请部门
        // applyDepCode: "",
        // applyDepName: "",
        remark: '',
        projectDesc: '' // 项目名称的聚合
      },
      rules: {
        businessTypeId: [
          {
            required: true,
            message: this.$t('请选择业务类型'),
            trigger: 'blur'
          }
        ],
        applyUserId: [{ required: true, message: this.$t('请选择申请人'), trigger: 'blur' }],
        companyId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        applyDepId: [
          {
            required: true,
            message: this.$t('请选择申请部门'),
            trigger: 'blur'
          }
        ]
      },
      isClickCancel: false, //手动点击了取消按钮， 为了不再次触发change事件
      thirdTenant: '',
      outboundWarehouse: ''
    }
  },

  computed: {
    vmiWarehouseTypeDisplay() {
      const _map = {
        0: this.$t('SRM管理库存'),
        1: this.$t('原厂'),
        2: this.$t('WMS管理库存')
      }
      return !_map[this.headerInfo.vmiWarehouseType]
        ? this.headerInfo.vmiWarehouseType
        : _map[this.headerInfo.vmiWarehouseType]
    }
  },

  watch: {},

  mounted() {
    this.timeDate()
    this.type = this.$route.query.type
    this.headerInfo.shipType = this.headerInfo.shipType === 0 ? this.$t('直送') : this.$t('非直送')
    const _outsourcedType = {
      0: this.$t('标准委外'),
      1: this.$t('销售委外'),
      2: this.$t('非委外'),
      3: this.$t('工序委外')
    }
    this.headerInfo.outsourcedType = _outsourcedType[this.headerInfo.outsourcedType]
    this.thirdTenant = this.headerInfo.thirdTenantCode + this.headerInfo.thirdTenantName
    this.vmiWarehouse = this.headerInfo.vmiWarehouseCode + this.headerInfo.vmiWarehouseName
    this.outboundWarehouse = this.headerInfo.outboundWarehouseCode
      ? this.headerInfo.outboundWarehouseCode + '-' + this.headerInfo.outboundWarehouseName
      : ''
    // this.thirdTenant = this.getList();
  },

  methods: {
    // 时间戳转换
    timeDate() {
      let sendData = this.dateFormat(this.headerInfo.sendTime)
      let sendDataTime = this.timeFormat(this.headerInfo.sendTime)
      this.headerInfo.sendTime = sendData + '-' + sendDataTime
      let forecastData = this.dateFormat(this.headerInfo.forecastArriveTime)
      let forecastTime = this.timeFormat(this.headerInfo.forecastArriveTime)

      this.headerInfo.forecastArriveTime = forecastData + '-' + forecastTime
    },
    dateFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }
      return str
    },
    timeFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'HH:MM:SS', value })
      } else {
        str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
      }
      return str
    },
    // 获取当前状态
    typeInfo(e) {
      if (e === 1) {
        return this.$t('新建')
      } else if (e === 2) {
        return this.$t('发货中')
      } else if (e === 3) {
        return this.$t('已完成')
      } else if (e === 4) {
        return this.$t('已取消')
      } else if (e === 5) {
        return this.$t('已关闭')
      }
    },
    startToSave(flag) {
      this.$emit('startToSave', flag)
    },

    // 业务类型变化
    handleBusinessChange(e) {
      console.log(this.$t('业务类型下拉改变'), this.isClickCancel, e)
      // 手动点了取消，会改变值再次触发change事件，此时不需要再弹窗提示了；
      // 如果没有赋过值，即第一次下拉选择，也可以不用手工提示
      if (!this.isClickCancel && e.previousItemData) {
        // 已有过下拉数据，且手动点击的下拉时
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('更换业务类型将重新清空/获取行数据，请确定是否继续？')
          },
          success: () => {
            this.isClickCancel = false
            this.$bus.$emit('changedPRBusinessType', e.itemData, e.previousItemData)
            this.$store.commit('updatePcBusinessCode', e.itemData.itemCode)
          },
          close: () => {
            this.isClickCancel = true
            this.addForm.businessTypeId = e.previousItemData.id
          }
        })
      } else if (!this.isClickCancel && !e.previousItemData) {
        // 新增第一次进入时
        this.isClickCancel = false
        this.$bus.$emit('changedPRBusinessType', e.itemData, e.previousItemData)
        this.$store.commit('updatePcBusinessCode', e.itemData.itemCode)
      } else {
        // 编辑/草稿状态新增时，只改变数据，不提交改变事件
        this.isClickCancel = false
        this.$store.commit('updatePcBusinessCode', e.itemData.itemCode)
      }
    },

    async getList() {
      // 获取业务类型列表
      this.$API.masterData.getDictCode({ dictCode: 'businessType' }).then((res) => {
        if (res.data && res.data.length) {
          this.businessTypeList = res.data
          if (this.$route.query.id && this.headerInfo.businessTypeId) {
            this.addForm.businessTypeId = this.headerInfo.businessTypeId
          } else if (this.$route.query?.source == 2) {
            this.queryMallBusinessType()
          }
        }
      })

      // 获取 用户列表
      await this.$API.masterData.getCurrentTenantEmployees().then((res) => {
        console.log(res)
        res.data.forEach((item) => {
          this.applyUserIdData.push({
            ...item,
            text:
              item.companyOrgName +
              '-' +
              item.departmentOrgName +
              '-' +
              item.employeeCode +
              '-' +
              item.employeeName
          })
        })
        console.log(this.$t('获取到了用户列表'), this.applyUserIdData, this.headerInfo)
        if (this.headerInfo?.applyUserId) {
          this.addForm.applyUserId = this.headerInfo.applyUserId
        } else {
          this.addForm.applyUserId = this.applyUserIdData.find(
            (item) => item.employeeId == this.userInfo?.employeeId
          )?.employeeId
        }
      })
    },

    // 获取 商城转申请 的业务类型
    queryMallBusinessType() {
      this.$API.purchaseRequest
        .queryMallBusinessType()
        .then((res) => {
          this.addForm.businessTypeId = res?.data?.businessTypeId || this.businessTypeList[0].id
          this.isClickCancel = false
        })
        .catch(() => {
          this.addForm.businessTypeId = this.businessTypeList[0].id
          this.isClickCancel = false
        })
    },

    // 用户改变
    async handleUserChange(e) {
      if (e.itemData != null) {
        this.addForm.applyUserId = e.itemData.employeeId
      }
      await this.getCompany()
      // await this.getDepart();
    },

    // 获取当前用户下的公司
    getCompany() {
      setTimeout(() => {
        if (!this.addForm.applyUserId) {
          return
        }
        this.$API.masterData
          .getOrgCompanysByEmpId({ employeeId: this.addForm.applyUserId })
          .then((res) => {
            this.applyCompanyData = res.data
            console.log('applyCompanyData', this.applyCompanyData)

            if (this.headerInfo.companyId) {
              this.addForm.companyId = this.headerInfo.companyId
            } else if (this.applyCompanyData && this.applyCompanyData.length == 1) {
              this.addForm.companyId = this.applyCompanyData[0].id
            } else if (this.applyCompanyData.length > 1) {
              this.addForm.companyId = null
            }
          })
      }, 10)
    },

    // 公司改变
    handleCompanyChange() {
      this.$store.commit('updatePrSubmitFlag', 4)
      this.getDepart()
    },

    // 获取当前用户下公司 的 部门
    getDepart() {
      setTimeout(() => {
        if (!this.addForm.companyId) {
          return
        }
        this.$API.masterData
          .getDepartmentsByEmpId({
            employeeId: this.addForm.applyUserId,
            companyOrganizationId: this.addForm.companyId
          })
          .then((res) => {
            console.log('applyDepartData', this.applyDepartData)
            this.applyDepartData = res.data

            if (this.headerInfo.applyDepId) {
              this.addForm.applyDepId = this.headerInfo.applyDepId
            } else if (this.applyDepartData && this.applyDepartData.length == 1) {
              this.addForm.applyDepId = this.applyDepartData[0].id
            } else if (this.applyDepartData.length > 1) {
              this.addForm.applyDepId = null
            }
          })
      }, 10)
    },

    // 获取其他数据 code、name
    getOtherInfo(params) {
      // 业务类型、申请人、公司、申请部门、采购组织
      if (this.addForm.businessTypeId && this.$refs.businessRef) {
        let _data = this.$refs.businessRef.ejsRef.getDataByValue(this.addForm.businessTypeId)
        if (!_data) return
        // console.log("业务类型_data", _data);
        params.businessTypeCode = _data.itemCode
        params.businessTypeName = _data.itemName
      }
      if (this.addForm.applyUserId && this.$refs.userRef) {
        let _data = this.$refs.userRef.ejsRef.getDataByValue(this.addForm.applyUserId)
        if (!_data) return
        // console.log("申请人_data", _data);
        params.applyUserName = _data.employeeName
        params.applyUserCode = _data.employeeCode
      }
      if (this.addForm.companyId && this.$refs.companyRef) {
        let _data = this.$refs.companyRef.ejsRef.getDataByValue(this.addForm.companyId)
        if (!_data) return
        // console.log("公司_data", _data);
        params.companyName = _data.orgName
        params.companyCode = _data.orgCode
      }
      if (this.addForm.applyDepId && this.$refs.depRef) {
        let _data = this.$refs.depRef.ejsRef.getDataByValue(this.addForm.applyDepId)
        if (!_data) return
        // console.log("部门_data", _data);
        params.applyDepName = _data.orgName
        params.applyDepCode = _data.orgCode
      }
      // console.log("params", params);
      return params
    },

    async confirm() {
      let res = 0
      await this.$refs.ruleForm.validate((valid) => {
        console.log(this.$t('校验'), valid)
        console.log(this.addForm)
        if (valid) {
          let params = {
            ...this.addForm
          }
          params = this.getOtherInfo(params)

          // console.log(this.$t("头部的提交数据"), params);
          res = params
        }
      })
      return res
    },
    // 更新人
    getUpdateUserName(data) {
      let userName = ''
      if (data.updateUserName) {
        userName = data.updateUserName // 更新人
      } else {
        userName = data.createUserName // 创建人
      }
      return userName
    },
    // 更新日期
    getUpdateTime(data) {
      let dateStr = ''
      let timeStr = ''
      if (data.updateTime) {
        dateStr = this.dateFormat(data.updateTime) // 更新日期
        timeStr = this.timeFormat(data.updateTime) // 更新时间
      } else {
        dateStr = this.dateFormat(data.createTime) // 创建日期
        timeStr = this.timeFormat(data.createTime) // 创建时间
      }
      return `${dateStr} ${timeStr}`
    },

    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      &.more-width {
        width: 450px;
      }

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
