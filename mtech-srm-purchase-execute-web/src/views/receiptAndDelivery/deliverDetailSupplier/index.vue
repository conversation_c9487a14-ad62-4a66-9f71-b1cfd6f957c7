<template>
  <div class="full-height pt20 vertical-flex-box">
    <top-info
      v-if="headerInfo"
      ref="headerTop"
      :header-info="headerInfo"
      class="flex-keep"
    ></top-info>
    <mt-tabs
      tab-id="deliver-tab"
      :e-tab="false"
      :data-source="pageConfig"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <div class="flex-fit" v-show="currentInfo.title == $t('物料信息')">
      <mt-template-page
        :e-tab="false"
        ref="templateRef"
        :template-config="pageConfig1"
        :hidden-tabs="true"
      />
    </div>
    <bottomInfo
      class="flex-keep"
      ref="bottomInfo"
      v-show="currentInfo.title == $t('物流信息')"
    ></bottomInfo>
  </div>
</template>

<script>
import { columnData } from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'

export default {
  components: {
    topInfo: require('./components/topInfo.vue').default,
    bottomInfo: require('./components/bottominfo.vue').default
  },
  data() {
    const headerInfo = JSON.parse(localStorage.getItem('deliverDetailSupplier')) || {}

    // 送货类型
    if (headerInfo.deliveryType == 1) {
      headerInfo.deliveryType = this.$t('采购订单')
    } else if (headerInfo.deliveryType == 2) {
      headerInfo.deliveryType = this.$t('交货计划')
    } else if (headerInfo.deliveryType == 3) {
      headerInfo.deliveryType = this.$t('JIT')
    } else if (headerInfo.deliveryType == 4) {
      headerInfo.deliveryType = this.$t('无需求')
    } else if (headerInfo.deliveryType == 5) {
      headerInfo.deliveryType = this.$t('vmi')
    } else if (headerInfo.deliveryType == 6) {
      headerInfo.deliveryType = this.$t('钢材')
    }

    headerInfo.status === 1
      ? this.$t('新建')
      : headerInfo.status === 2
      ? this.$t('发货中')
      : headerInfo.status === 3
      ? this.$t('已完成')
      : headerInfo.status === 4
      ? this.$t('已取消')
      : headerInfo.status === 5
      ? this.$t('已关闭')
      : ''
    const { id, type } = this.$route.query

    return {
      id,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      pageConfig: [{ title: this.$t('物料信息') }, { title: this.$t('物流信息') }],
      userInfo: null,
      headerInfo,
      currentInfo: {
        title: this.$t('物料信息')
      },
      type,
      pageConfig1: [
        {
          tab: { title: this.$t('送货明细') },
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          gridId: this.$tableUUID.receiptAndDelivery.deliverDetailSupplier.materialInfo,
          grid: {
            columnData: columnData,
            // lineSelection: 0, // 选项列
            // lineIndex: 1,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/supplierOrderDeliveryItem/page`,
              defaultRules: [
                {
                  field: 'deliveryId',
                  operator: 'equal',
                  value: id
                }
              ]
            }
            // frozenColumns: 1,
          }
        }
      ],
      forecastRules: []
    }
  },
  mounted() {},
  methods: {
    handleSelectTab(index, item) {
      this.currentInfo = item
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  },
  // 消失
  beforeDestroy() {
    localStorage.removeItem('deliverDetailSupplier')
  }
}
</script>

<style lang="scss" scoped>
.repeat-template {
  .common-template-page {
    /deep/ .mt-tabs {
      display: none;
    }
  }
}
.e-content {
  height: 200px;
}

.mt-tabs {
  width: 100%;
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
</style>
