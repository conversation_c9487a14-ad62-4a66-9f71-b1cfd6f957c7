<template>
  <!-- eslint-disable prettier/prettier -->
  <!-- 无PO送货申请单-供方 - 泛智屏 -->
  <div class="full-height vertical-flex-box">
    <div style="height: calc(100% - 40px)">
      <mt-template-page
        class="templateRef"
        ref="templateRef"
        :template-config="pageConfig"
        :hidden-tabs="true"
        @handleClickToolBar="handleClickToolBar"
        @handleCustomReset="handleSearchReset"
        @handleCustomSearch="handleSearchForm"
        @handleClickCellTitle="handleClickCellTitle"
        @handleClickCellTool="handleClickCellTool"
      >
        <template v-slot:quick-search-form>
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.itemCode"
                :show-clear-button="true"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              />
            </mt-form-item>
            <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.itemName"
                :show-clear-button="true"
                :placeholder="$t('支持模糊搜索')"
              />
            </mt-form-item>
            <mt-form-item prop="applyCode" :label="$t('申请编码')">
              <mt-input
                v-model="searchFormModel.applyCode"
                :show-clear-button="true"
                :placeholder="$t('请输入申请编码')"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-multi-select
                style="width: 100%"
                v-model="searchFormModel.status"
                type="multipleChoice"
                :placeholder="$t('请选择状态')"
                :show-clear-button="true"
                :data-source="statusList"
                :fields="{ text: 'label', value: 'value' }"
              />
            </mt-form-item>
            <mt-form-item prop="siteCode" :label="$t('工厂编码')">
              <mt-select
                v-model="searchFormModel.siteCode"
                :placeholder="$t('请选择')"
                :data-source="siteOptions"
                :fields="{ text: 'theCodeName', value: 'value' }"
                :allow-filtering="true"
                :show-clear-button="true"
                @change="siteCodeChange"
              />
            </mt-form-item>
            <mt-form-item prop="deliverDate" :label="$t('送货日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.deliverDate"
                :placeholder="$t('请选择送货日期')"
                @change="(e) => deliverDateChange(e, 'deliverDate')"
              />
            </mt-form-item>
            <mt-form-item prop="saleOrderNo" :label="$t('销售订单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.saleOrderNo"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
            <mt-form-item prop="workCenterCode" :label="$t('工作中心')" label-style="top">
              <mt-input
                v-model="searchFormModel.workCenterCode"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
          </mt-form>
        </template>
      </mt-template-page>
    </div>
    <mt-page
      class="full-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totals"
      @currentChange="currentChange"
      @sizeChange="sizeChange"
    />
    <delivery-dialog
      v-if="deliveryShow"
      :ids="id"
      :company-codes="companyCodes"
      ref="child"
      @handleAddDialogShow="handleAddDialogShow"
      @handleDialogShow="handleDialogShow"
    >
    </delivery-dialog>
  </div>
</template>

<script>
/* eslint-disable prettier/prettier */
import { i18n } from '@/main.js'

import Vue from 'vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import { columnData, statusList } from './config.js'
import dayjs from 'dayjs'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  components: {
    DeliveryDialog: require('./components/deliveryDialog').default
  },
  mounted() {
    this.$bus.$on('nopoDeliveryRequestSupplierUpdate', () => {
      this.handleSearchForm()
    })
    this.getCompany({})
    this.getItemCodeSource({})
    this.getSiteOptions()
    setTimeout(() => {
      this.$set(this.pageSettings, 'pageSize', 20)
    }, 500)
  },
  data() {
    return {
      itemCodeOptions: [],
      statusList: statusList,
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 5,
        pageSizes: [10, 20, 50, 100, 200, 1000],
        totalRecordsCount: 0,
        totals: 0
      },
      siteOptions: [], // 工厂 下列选项
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      itemCodeValueTemplate: codeNameColumn({
        firstKey: 'itemCode',
        secondKey: 'itemName'
      }),
      companyCodeValueTemplate: codeNameColumn({
        firstKey: 'customerCode',
        secondKey: 'customerName'
      }),
      companyOptions: [], // 公司 下拉选项
      statusOptions: [
        { value: 2, text: i18n.t('发货中') },
        { value: 3, text: i18n.t('已完成') },
        { value: 4, text: i18n.t('已取消') },
        { value: 5, text: i18n.t('已关闭') },
        { value: 6, text: i18n.t('部分收货') }
      ],
      id: [],
      searchFormModelDetail: {
        deliveryCode: ''
      },
      searchFormModel: {
        deliveryCode: null,
        companyName: null,
        status: null,
        siteCode: null,
        deliverDate: null,
        itemCode: null,
        itemName: null
      },
      companyCodes: [],
      deliveryShow: false,
      index: 0,
      tabIndex: 0,

      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'List', permissionCode: 'T_02_0163' },
          { dataPermission: 'Details', permissionCode: 'T_02_0164' }
        ]
      },
      pageConfig: [
        {
          dataPermission: 'List',
          activatedRefresh: false,
          isUseCustomSearch: true,
          isCustomSearchHandle: true,
          // permissionCode: "T_02_0163",
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: {
            tools: [
              [
                {
                  id: 'new',
                  icon: 'icon_table_new',
                  permission: ['O_02_1646'],
                  title: this.$t('新建')
                },
                // {
                //   id: "detail",
                //   icon: "icon_table_AppointmentDelivery1",
                //   title: this.$t("查看详情"),
                // },
                {
                  id: 'delete',
                  icon: 'icon_table_delete',
                  permission: ['O_02_1648'],
                  title: this.$t('删除')
                },
                {
                  id: 'submit',
                  icon: 'icon_table_submit',
                  permission: ['O_02_1647'],
                  title: this.$t('提交')
                }
              ],
              ['Setting']
            ]
          },
          gridId: '3575620d-796e-40c2-9c5f-b63c84788f08',
          grid: {
            virtualPageSize: 30,
            showSelected: false,
            selectionSettings: {
              persistSelection: true,
              type: 'Multiple',
              checkboxOnly: true
            },
            enableVirtualization: true,
            allowPaging: false,
            useToolTemplate: false, // 不使用预置(新增、编辑、删除)
            useBaseConfig: true, // 使用组件中的toolbar配置
            columnData: columnData,
            customSelection: true, // 使用自定义勾选列
            lineIndex: 0, // 序号列
            dataSource: []
            // frozenColumns: 1,
            // frozenColumns: 4
          }
        }
      ],
      userInfo: null,
      addDialogShow: false,
      dialogData: null
    }
  },
  methods: {
    // 送货日期时间切换
    deliverDateChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'S'] = dayjs(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        ).valueOf()
        this.searchFormModel[flag + 'E'] = dayjs(
          dayjs(e.startDate).format('YYYY-MM-DD 23:59:59')
        ).valueOf()
      } else {
        this.searchFormModel[flag + 'S'] = null
        this.searchFormModel[flag + 'E'] = null
      }
    },
    // 获取物料信息
    getItemCodeSource(args = {}) {
      const { text } = args
      let obj = {
        keyword: text ?? ''
      }

      this.$API.masterData.getItemByKeyword(obj).then((res) => {
        const _list = addCodeNameKeyInList({
          firstKey: 'itemCode',
          secondKey: 'itemName',
          list: res.data.records
        })
        this.itemCodeOptions = _list
      })
    },
    // 分页器 - 切换分页
    currentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.searchForm()
    },
    // 分页器 - 切换页大小
    sizeChange(currentPageSize) {
      this.pageSettings.pageSize = currentPageSize
      this.pageSettings.currentPage = 1
      this.searchForm()
    },
    handleSearchReset() {
      for (let key in this.searchFormModel) {
        this.searchFormModel[key] = null
      }
    },
    handleSearchForm() {
      this.pageSettings.currentPage = 1
      this.searchForm()
    },
    searchForm() {
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
      this.$store.commit('startLoading')
      const param = {
        page: {
          current: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$API.receiptAndDelivery
        .getNopoDeliveryRequestListTV(param)
        .then((res) => {
          if (res.code === 200) {
            setTimeout(() => {
              document.querySelector('.e-content').scrollTop = 0
            }, 20)
            this.$set(this.pageConfig[0].grid, 'dataSource', res.data.records)
            this.pageSettings.totals = Math.ceil(
              Number(res.data.total) / this.pageSettings.pageSize
            )
            this.pageSettings.totalRecordsCount = res.data.total
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    getSiteOptions() {
      const _userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      let supplierCode = _userInfo.accountName
      this.$API.receiptAndDelivery
        .getFactoryByCode([supplierCode])
        .then((res) => {
          if (res.code === 200) {
            this.siteOptions = res.data.map((item) => {
              return {
                theCodeName: item.siteCode + '-' + item.siteName,
                label: item.siteName,
                value: item.siteCode
              }
            })
          }
        })
        .catch(() => {})
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        page: {
          current: 1,
          pages: 0,
          size: 20
        }
      }
      this.$API.receiptAndDelivery
        .getFactoryInfo(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 公司 change
    companyCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.companyId = itemData.id
        this.searchFormModel.companyCode = itemData.customerCode
        this.searchFormModel.companyName = itemData.customerName
      } else {
        this.searchFormModel.companyId = ''
        this.searchFormModel.companyCode = ''
        this.searchFormModel.companyName = ''
      }
    },
    // 工厂 change
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.siteCode = itemData.value
        this.searchFormModel.siteName = itemData.label
      } else {
        this.searchFormModel.siteCode = ''
        this.searchFormModel.siteName = ''
      }
    },
    // 物料 change
    itemCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.itemId = itemData.id
        this.searchFormModel.itemCode = itemData.itemCode
        this.searchFormModel.itemName = itemData.itemName
      } else {
        this.searchFormModel.itemId = ''
        this.searchFormModel.itemCode = ''
        this.searchFormModel.itemName = ''
      }
    },
    // 获取主数据-获取指定组织下指定组织层级节点列表 业务公司
    getCompany(args) {
      const { updateData, setSelectData } = args
      let obj = {
        fuzzyNameOrCode: ''
      }
      this.$API.masterData
        .getCustomer(obj)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.companyOptions = addCodeNameKeyInList({
              firstKey: 'customerCode',
              secondKey: 'customerName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.companyOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 弹框
    handleDialogShow(flag) {
      this.deliveryShow = flag
    },
    // 点击单元格 标题
    handleClickCellTitle(e) {
      if (e.field === 'deliveryCode') {
        localStorage.setItem('deliverDetailSupplierTV', JSON.stringify(e.data))
        this.$router.push(`deliver-detail-supplier-tv?id=${e.data.id}&type=` + e.data.deliveryType)
      } else if (e.field === 'applyCode') {
        const { data } = e
        const _flag = data.status && Number(data.status) > 1
        const _path = _flag
          ? 'detail-nopo-delivery-request-supplier'
          : 'create-nopo-delivery-request-supplier'
        this.$router.push({
          name: _path,
          query: {
            ...data,
            operationType: _flag ? 'detail' : 'edit'
          }
        })
      }
    },
    handleClickCellTitle1(e) {
      if (e.field === 'deliveryCode') {
        this.getDeliverDataById(e.data?.deliveryId)
      }
    },
    // 根据id查询主单数据 - 获取物料信息并跳转物料页面
    getDeliverDataById(id) {
      const params = {
        condition: 'and',
        page: { current: 1, size: 20 },
        rules: [
          {
            field: 'id',
            operator: 'equal',
            value: id
          }
        ]
      }
      this.$API.receiptAndDelivery
        .getSupplierDeliveyData(params)
        .then((res) => {
          if (res?.code == 200) {
            // 跳转详情页

            localStorage.setItem('deliverDetailSupplierTV', JSON.stringify(res?.data?.records[0]))
            this.$router.push(
              `deliver-detail-supplier-tv?id=${res?.data?.records[0].id}&type=` +
                res?.data?.records[0].deliveryType
            )
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleClickCellTool(e) {
      if (e.tool.title === this.$t('维护物流信息')) {
        this.$router.push({
          name: 'deliver-logistics-supplier',
          query: {
            id: e.data.id
          }
        })
        localStorage.setItem('deliverDetailSupplierTV', JSON.stringify(e.data))
      }
    },
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    handleClickToolBar(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id === 'resetDataByLocal') return
      if (toolbar.id == 'new') {
        this.$router.push(`create-nopo-delivery-request-supplier`)
        return
      }
      const selectRows = []
      gridRef.dataSource.forEach((item) => {
        if (item.customChecked) {
          selectRows.push(item)
        }
      })
      if (selectRows.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const _status = []
      const _id = []
      const _codes = []
      selectRows.forEach((item) => {
        _id.push(item.id)
        _codes.push(item.companyCode)
        _status.push(item.status)
      })
      this.id = _id
      this.companyCodes = _codes

      if (toolbar.id === 'delete') {
        this.$dialog({
          data: {
            title: this.$t('删除'),
            message: this.$t('确认删除所选数据?')
          },
          success: () => {
            for (let i of _status) {
              if (i > 1) {
                this.$toast({
                  content: this.$t('只有状态为草稿的数据才可以删除'),
                  type: 'error'
                })
                return
              }
            }
            this.handleDelete(_id)
          }
        })
      }

      if (toolbar.id == 'submit') {
        this.$dialog({
          data: {
            title: this.$t('提交'),
            message: this.$t('确认提交?')
          },
          success: () => {
            for (let i of _status) {
              if (i > 1) {
                this.$toast({
                  content: this.$t('只有状态为草稿的数据才可以提交'),
                  type: 'warning'
                })
                return
              }
            }
            this.handleSubmit(_id)
          }
        })
      }
    },
    print(_id, type) {
      if (type === 'kt') {
        this.$API.receiptAndDelivery.supplierOrderDeliveryPrintHtml(_id).then((res) => {
          if (res && res.code === 500) {
            this.$toast({ content: res.msg, type: 'error' })
            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(
            new Blob([content], { type: 'text/html;charset=utf-8' })
          )
          // window.open(this.pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
      } else if (type === 'bd') {
        this.$API.receiptAndDelivery.supplierOrderDeliveryPrint(_id).then((res) => {
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = function () {
              console.log('======', reader)
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }

            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
          // window.open(this.pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
      }
    },
    handleDelete(_id) {
      this.$store.commit('startLoading')

      this.$API.receiptAndDelivery
        .batchDeleteNoPo(_id)
        .then((res) => {
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleSearchForm()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.handleSearchForm()
        })
    },
    handleSubmit(_id) {
      this.$store.commit('startLoading')

      this.$API.receiptAndDelivery
        .batchConfirmNoPo(_id)
        .then((res) => {
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleSearchForm()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.handleSearchForm()
        })
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('todoDetail')
  }
}
</script>

<style lang="scss" scoped>
.forecast-template {
  width: 250px;
  padding-bottom: 8px;
  .forecast-template-select {
    width: calc(250px - 70px);
  }
}
/deep/ .column-tool {
  margin-top: 8px;
}
/deep/ .template-svg {
  cursor: pointer;
  font-size: 12px;
  color: var(--plugin-ct-cell-icon-color);

  &:nth-child(n + 2) {
    margin-left: 10px;
  }
}

/deep/ .grid-edit-column {
  // padding: 12px 0;
}

// 预测数据行
/deep/ .forecast-item {
  // height: 40px;
  // padding: 7px;
  line-height: 26px;
  border: 1px solid #e8e8e8;
}
/deep/ .inputSy {
  border: 1px solid #e8e8e8;
  height: 30px;
}
// 预测数据高亮数据
/deep/ .forecast-highlight {
  color: #ed5836;
  background-color: #fdeeea;
  border: 1px solid #ed5836;
}

// 表格容器
#forecast-manage-table-container {
  position: relative;

  // 表格数据视图
  /deep/ .e-gridcontent > .e-content {
    height: 0px;
    transition: height 0.5s ease;
  }

  // 分页
  /deep/ .mt-pagertemplate {
    margin: 10px 0 10px !important;
  }
}

// 行编辑时包含按钮的项目
/deep/ .input-search-content {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .mt-input {
    flex: 1;
  }

  .mutliselect-container {
    width: 100%;
  }
  // 超过宽度显示省略号
  .text-ellipsis {
    width: 125px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .mt-icons {
    width: 20px;
    flex-shrink: 0;
    text-align: center;
    cursor: pointer;
  }
}

/deep/ .templateRef {
  .template-wrap {
    .e-grid .e-table {
      & thead th:first-child {
        position: sticky;
        left: 0px;
        z-index: 1;
      }

      & tbody td:first-child {
        position: sticky;
        left: 0px;
        z-index: 1;
        border-right: 1px solid var(--plugin-dg-shadow-color);
        background-color: #fff;
      }

      & tbody .e-active {
        background-color: #e0e0e0 !important;
      }
    }
  }
}
</style>
