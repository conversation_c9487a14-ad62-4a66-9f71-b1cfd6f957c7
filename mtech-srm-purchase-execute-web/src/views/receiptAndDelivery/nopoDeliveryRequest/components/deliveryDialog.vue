<template>
  <div>
    <mt-form ref="addForm" :model="addForm" :rules="rules" :validate-on-rule-change="false">
      <mt-form-item prop="siteCode" :label="$t('工厂代码')">
        <debounce-filter-select
          v-model="addForm.siteCode"
          :request="postSiteFuzzyQuery"
          :data-source="siteOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'siteCode' }"
          :value-template="siteCodeValueTemplate"
          @change="siteCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="type" :label="$t('工厂名称')">
        <mt-input v-model="addForm.siteName" :show-clear-button="true" :placeholder="$t('')" />
      </mt-form-item>
      <mt-form-item prop="itemCode" :label="$t('物料编码')">
        <debounce-filter-select
          v-model="addForm.itemCode"
          :request="getWarehouseCodeOptions"
          :data-source="itemOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'itemCode' }"
          :value-template="itemCodeValueTemplate"
          @change="itemCodeChange"
          :placeholder="$t('请选择物料编码')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="itemName" :label="$t('物料描述')">
        <mt-input v-model="addForm.itemName" :show-clear-button="true" :placeholder="$t('')" />
      </mt-form-item>
      <mt-form-item v-if="addForm.type === 1" prop="logisticsNo" :label="$t('申请数量')">
        <mt-input-number
          v-model="addForm.applyQty"
          :precision="0"
          :min="0"
          :show-clear-button="true"
          :placeholder="$t('申请数量')"
        />
      </mt-form-item>
      <mt-form-item prop="unit" :label="$t('单位')">
        <debounce-filter-select
          v-model="addForm.itemCode"
          :request="postUnitFuzzyQuery"
          :data-source="unitOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'unitCode' }"
          :value-template="unitValueTemplate"
          @change="unitCodeChange"
          :placeholder="$t('请选择物料编码')"
        ></debounce-filter-select>
      </mt-form-item>

      <mt-form-item prop="deliverAddr" :label="$t('发货日期')">
        <mt-date-picker
          :placeholder="$t('选择发货日期')"
          v-model="addForm.deliverDate"
          :show-clear-button="false"
          :allow-edit="false"
          @change="(e) => dateToStamp(e)"
        ></mt-date-picker>
      </mt-form-item>
      <mt-form-item prop="deliverAddr" :label="$t('送货地址')">
        <debounce-filter-select
          v-model="addForm.deliverAddr"
          :request="getWarehouseCodeOptions"
          :data-source="addrOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'deliverAddr' }"
          :value-template="addrValueTemplate"
          @change="deliverAddrChange"
          :placeholder="$t('请选择送货地址')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="left">
        <mt-input v-model="addForm.supplierCode" :placeholder="$t('系统自动带出')" />
      </mt-form-item>
      <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="left">
        <mt-input v-model="addForm.supplierName" :placeholder="$t('系统自动带出')" />
      </mt-form-item>
      <mt-form-item prop="planner" :label="$t('计划员')">
        <debounce-filter-select
          v-model="addForm.planner"
          :request="plannerList"
          :data-source="plannerOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'userCode' }"
          :value-template="plannerTemplate"
          @change="plannerChange"
          :placeholder="$t('请选择计划员')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="status" :label="$t('单据状态')">
        <mt-select
          v-model="addForm.status"
          :placeholder="$t('')"
          :data-source="statusOptions"
          :fields="{ text: 'label', value: 'value' }"
        />
      </mt-form-item>
      <mt-row>
        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input
            v-model="addForm.remark"
            :placeholder="$t('请输入备注')"
            :maxlength="200"
          ></mt-input>
        </mt-form-item>
      </mt-row>
      <mt-row>
        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input
            v-model="addForm.remark"
            :placeholder="$t('请输入备注')"
            :maxlength="200"
          ></mt-input>
        </mt-form-item>
      </mt-row>
      <mt-row>
        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input
            v-model="addForm.remark"
            :placeholder="$t('请输入备注')"
            :maxlength="200"
          ></mt-input>
        </mt-form-item>
      </mt-row>
    </mt-form>
  </div>
</template>

<script>
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import dayjs from 'dayjs'
export default {
  props: {
    ids: {
      type: Array,
      default: () => {}
    },
    companyCodes: {
      type: Array,
      default: () => []
    }
  },
  components: {
    DebounceFilterSelect
  },
  data() {
    return {
      statusOptions: [
        { label: this.$t('草稿'), value: 1 },
        { label: this.$t('审批中'), value: 2 },
        { label: this.$t('审批通过'), value: 3 },
        { label: this.$t('审批拒绝'), value: 4 },
        { label: this.$t('已退回'), value: 5 },
        { label: this.$t('已关闭'), value: 6 }
      ],
      plannerOptions: [],
      itemOptions: [],
      unitOptions: [],
      addrOptions: [],
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      itemCodeValueTemplate: codeNameColumn({
        firstKey: 'itemCode',
        secondKey: 'itemName'
      }), // 物料
      unitValueTemplate: codeNameColumn({
        firstKey: 'unitCode',
        secondKey: 'unitName'
      }), // 单位
      addrValueTemplate: codeNameColumn({
        firstKey: '',
        secondKey: ''
      }), // 送货地址
      plannerTemplate: codeNameColumn({
        firstKey: 'userCode',
        secondKey: 'userName'
      }),
      siteOptions: [],
      dialogTitle: '',
      logisticsCompanyOptions: [], // 物流公司
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      deliveryTypeOptions: [
        {
          value: 1,
          text: this.$t('快递配送')
        },
        {
          value: 2,
          text: this.$t('物流配送')
        }
      ], // 业务类型
      addForm: {
        carNo: '',
        deliveryId: '1',
        driverName: '',
        driverNo: '',
        driverPhone: '',
        idList: [],
        logisticsCompanyCode: '',
        logisticsCompanyName: '',
        logisticsNo: '',
        number: '',
        remark: '',
        tenantId: '1',
        type: 0
      },
      rules: {
        siteCode: [
          {
            required: true,
            message: this.$t('请选择工厂代码'),
            trigger: 'blur'
          }
        ],
        logisticsCompanyCode: [
          {
            required: true,
            message: this.$t('请选择物流公司'),
            trigger: 'blur'
          }
        ],
        logisticsNo: [
          {
            required: true,
            message: this.$t('请输入物流单号'),
            trigger: 'blur'
          }
        ]
        // number: [
        //   { required: true, message: this.$t("请输入件数"), trigger: "blur" },
        // ],
      },
      dimensionFields: { text: 'message', value: 'code' },
      dimensionList: [],
      driverNameOptions: [], // 司机姓名下拉数据源
      idCardDisabled: false
    }
  },
  mounted() {
    this.init()
    this.addForm.idList = this.ids
    this.$refs.dialog.ejsRef.show()
    // 获取送货司机下拉数据源
    this.getDriverNameOptions()
    // this.$refs.ruleForm.resetFields();
  },

  methods: {
    // supplierChange(e) {
    //   if (e.itemData) {
    //     this.addForm.supplierCode = e.itemData.supplierCode
    //     this.addForm.supplierName = e.itemDat
    //   }
    // },
    siteCodeChange(e) {
      if (e.itemData) {
        this.addForm.siteCode = e.itemData.siteCode
        this.addForm.siteName = e.itemData.siteName
      } else {
        this.addForm.siteCode = null
        this.addForm.siteName = null
      }
    },
    itemCodeChange(e) {
      if (e.itemData) {
        this.addForm.itemCode = e.itemData.itemCode
        this.addForm.itemName = e.itemData.itemName
      } else {
        this.addForm.itemCode = null
        this.addForm.itemName = null
      }
    },
    unitCodeChange(e) {
      if (e.itemData) {
        this.addForm.unitCode = e.itemData.unitCode
        this.addForm.unitName = e.itemData.unitName
      } else {
        this.addForm.unitCode = null
        this.addForm.unitName = null
      }
    },
    plannerChange(e) {
      if (e.itemData) {
        this.addForm.planMemberCode = e.itemData.userCode
        this.addForm.planner = e.itemData.userName
      } else {
        this.addForm.planMemberCode = null
        this.addForm.planner = null
      }
    },
    deliverAddrChange(e) {
      if (e.itemData) {
        this.addForm.unitCode = e.itemData.unitCode
        this.addForm.unitName = e.itemData.unitName
      } else {
        this.addForm.unitCode = null
        this.addForm.unitName = null
      }
    },
    // 日期转时间戳
    dateToStamp(e) {
      if (e) {
        this.addForm.deliverDate = dayjs(e).unix()
      } else {
        this.addForm.deliverDate = null
      }
    },
    // 获取库存地点
    getWarehouseCodeOptions(args = { text: null }) {
      const { text } = args
      const params = {
        siteAddress: text, //库存地点code
        siteCode: this.siteCode, //工厂
        itemCode: this.itemCode
      }
      this.$API.deliverySchedule.siteTenantExtendqueryBySite(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.consigneeName}-${item.consigneePhone}-${item.consigneeAddress}`
          item.addressId = item.id
        })
      })
    },
    // 计划员
    plannerList(args = { text: null }) {
      const { text } = args
      const params = {
        userName: text,
        page: { current: 1, size: 100 }
      }
      this.$API.predictCollaboration
        .getPlannerRelationSchedule(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.unitOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
          }
        })
        .catch(() => {})
    },
    // 单位
    postUnitFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.unitOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.unitOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 切换方式
    selectChange(e) {
      console.log(e)
      this.$refs.addForm.resetFields()
      console.log(this.addForm)
    },
    init() {
      // 获取物流公司
      this.$API.masterData //物流公司
        .getCommonDictItemTreeTv({
          dictCode: 'logisticsCompany'
        })
        .then((res) => {
          if (res.data && res.data.length) {
            this.logisticsCompanyOptions = res.data.map((item) => {
              return {
                text: item.itemName,
                value: item.itemCode,
                id: item.id
              }
            })
          }
        })
      // 获取工厂数据
      this.postSiteFuzzyQuery({ text: '' })
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    getDimension() {
      this.$API.moduleConfig.getDimension().then((res) => {
        console.log(res)
        this.dimensionList = res.data || []
      })
    },
    // 司机姓名 change
    driverNameChange(e) {
      const { itemData } = e
      if (itemData) {
        this.addForm.driverPhone = itemData.contact // 司机联系方式
        this.addForm.carNo = itemData.license // 车牌号
        this.addForm.driverNo = itemData.idCard // 司机身份证号
      } else {
        this.addForm.driverPhone = '' // 司机联系方式
        this.addForm.carNo = '' // 车牌号
        this.addForm.driverNo = '' // 司机身份证号
      }
    },
    driverNameBlur() {
      const _arr = this.driverNameOptions.map((item) => item.name)
      this.idCardDisabled = _arr.includes(this.addForm.driverName)
    },
    // 获取司机姓名下拉数据源
    getDriverNameOptions() {
      const params = {
        page: { current: 1, size: 10000 },
        condition: 'and',
        defaultRules: [
          {
            label: this.$t('状态'),
            field: 'status',
            type: 'string',
            operator: 'equal',
            value: 1
          }
        ],
        companyCodeList: this.companyCodes
      }

      this.$API.receiptAndDelivery
        .postSupplierDriverQueryTv(params)
        .then((res) => {
          const data = res?.data?.records || []
          this.driverNameOptions = data
        })
        .catch(() => {})
    },

    confirm() {
      console.log(1)
      this.$refs.addForm.validate((valid) => {
        console.log(2)
        if (valid) {
          let params = this.addForm

          this.$API.receiptAndDelivery.supplierDeliveryLogisticsInfoSave(params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('handleAddDialogShow')
            }
          })
        }
      })
    },

    handleClose() {
      this.$emit('handleDialogShow', false)
    }
  }
}
</script>

<style></style>
