<template>
  <div class="detail-top-info" style="height: 100%">
    <div class="header-box">
      <div class="middle-blank"></div>
      <span class="header-box-btn" v-waves type="info" @click="cancel">{{ $t('取消') }}</span>
      <span class="header-box-btn" v-waves type="primary" @click="save">{{ $t('保存') }}</span>
      <span class="header-box-btn" v-waves type="primary" @click="confirm">{{ $t('提交') }}</span>
    </div>
    <div>
      <mt-form ref="addForm" :model="addForm" :rules="rules" :validate-on-rule-change="false">
        <mt-row v-if="isEdit" :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('申请编码：')" label-style="left">
              {{ addForm.applyCode }}
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="factoryCode" :label="$t('工厂代码')">
              <mt-select
                v-model="addForm.factoryCode"
                :placeholder="$t('请选择')"
                :data-source="siteOptions"
                :fields="{ text: 'theCodeName', value: 'value' }"
                :allow-filtering="true"
                @change="siteCodeChange"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="factoryName" :label="$t('工厂名称')">
              <mt-input
                v-model="addForm.factoryName"
                :show-clear-button="true"
                :disabled="true"
                :placeholder="$t('')"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="itemCode" :label="$t('物料编码')">
              <debounce-filter-select
                v-model="addForm.itemCode"
                :request="getItemList"
                :data-source="itemOptions"
                :show-clear-button="true"
                :fields="{ text: 'theCodeName', value: 'itemCode' }"
                :value-template="itemCodeValueTemplate"
                @change="itemCodeChange"
                :placeholder="$t('请选择物料编码')"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="itemName" :label="$t('物料描述')">
              <mt-input
                v-model="addForm.itemName"
                :show-clear-button="true"
                :disabled="true"
                :placeholder="$t('')"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="applyQty" :label="$t('申请数量')">
              <mt-input-number
                v-model="addForm.applyQty"
                :precision="0"
                :min="0"
                :show-clear-button="true"
                :placeholder="$t('申请数量')"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="unitName" :label="$t('单位')">
              <mt-input
                v-model="addForm.unitName"
                :placeholder="$t('由物料编码带出')"
                :disabled="true"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="deliverDateCode" :label="$t('交货日期')">
              <mt-date-picker
                :placeholder="$t('选择交货日期')"
                v-model="addForm.deliverDateCode"
                format="yyyy-MM-dd"
                :show-clear-button="false"
                :allow-edit="false"
                :min="newParam"
                @change="(e) => dateToStamp(e, 'deliverDate')"
              ></mt-date-picker>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="deliverAddrCode" :label="$t('送货地址')">
              <!-- <debounce-filter-select
                v-if="isdeliverAddrShow"
                v-model="addForm.deliverAddrCode"
                :request="getWarehouseCodeOptions"
                :data-source="addrOptions"
                :show-clear-button="true"
                :fields="{ text: 'theCodeName', value: 'consigneeAddressCode' }"
                :value-template="addrValueTemplate"
                @change="deliverAddrChange"
                :placeholder="$t('请选择送货地址')"
              /> -->
              <RemoteAutocomplete
                v-if="isdeliverAddrShow"
                v-model="addForm.deliverAddrCode"
                url="/srm-purchase-execute/tenant/siteTenantExtend/distinct/queryBySiteCode"
                :fields="{ text: 'consigneeAddress', value: 'consigneeAddressCode' }"
                :params="{ siteCode: addForm.factoryCode }"
                :popup-width="'280'"
                :data-limit="2000"
                params-key="consigneeAddress"
                records-position="data.records"
                :placeholder="$t('请输入关键字')"
                @change="deliverAddrChange"
              />
              <mt-input v-else v-model="addForm.deliverAddrCode" disabled />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="saleOrderNo" :label="$t('销售订单号')">
              <mt-input
                style="width: 100%"
                v-model="addForm.saleOrderNo"
                show-clear-button
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="workCenterCode" :label="$t('工作中心')">
              <RemoteAutocomplete
                v-model="addForm.workCenterCode"
                url="/srm-purchase-execute/tenant/siteTenantExtend/getWorkCenterCodeBySiteCodes"
                :fields="{ text: 'workCenterName', value: 'workCenterCode' }"
                :params="{ siteCodes: [addForm.factoryCode] }"
                records-position="data"
                :placeholder="$t('请输入关键字')"
                @change="workCenterChange"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="supplierCode" :label="$t('供应商编码')">
              <mt-input
                style="width: 100%"
                v-model="addForm.supplierCode"
                :placeholder="$t('系统自动带出')"
                :disabled="true"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="supplierName" :label="$t('供应商名称')">
              <mt-input
                style="width: 100%"
                v-model="addForm.supplierName"
                :placeholder="$t('系统自动带出')"
                :disabled="true"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="planMemberCode" :label="$t('计划员')">
              <mt-select
                v-model="addForm.planMemberCode"
                :placeholder="$t('请选择')"
                :data-source="plannerOptions"
                :fields="{ text: 'theCodeName', value: 'value' }"
                :allow-filtering="true"
                filter-type="Contains"
                @change="plannerChange"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="status" :label="$t('单据状态')">
              <mt-select
                v-model="addForm.status"
                :placeholder="$t('')"
                :data-source="statusOptions"
                :disabled="true"
                :fields="{ text: 'label', value: 'value' }"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row v-if="isEdit" :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="createUserName" :label="$t('创建人')">
              <mt-input v-model="addForm.createUserName" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="createTime" :label="$t('创建时间')">
              <mt-input v-model="addForm.createTime" disabled />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row>
          <mt-form-item prop="supplierRemark" :label="$t('供方备注')" class="full-width">
            <mt-input
              v-model="addForm.supplierRemark"
              :placeholder="$t('请输入供方备注（字数限制250个）')"
              :maxlength="250"
            ></mt-input>
          </mt-form-item>
        </mt-row>
        <mt-row v-if="isEdit">
          <mt-form-item prop="plannerRemark" :label="$t('计划员备注')" class="full-width">
            <mt-input v-model="addForm.plannerRemark" disabled />
          </mt-form-item>
        </mt-row>
        <mt-row>
          <mt-form-item prop="approveOpinion" :label="$t('审批意见')" class="full-width">
            <mt-input
              v-model="addForm.approveOpinion"
              :placeholder="$t('')"
              :disabled="true"
            ></mt-input>
          </mt-form-item>
        </mt-row>
      </mt-form>
    </div>
  </div>
</template>

<script>
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import dayjs from 'dayjs'
export default {
  props: {
    ids: {
      type: Array,
      default: () => {}
    },
    companyCodes: {
      type: Array,
      default: () => []
    }
  },
  components: {
    DebounceFilterSelect
  },
  data() {
    const _year = new Date().getFullYear()
    const _mon = new Date().getMonth()
    const _day = new Date().getDate()
    return {
      dayjs,
      newParam: new Date(_year, _mon, _day),
      statusOptions: [
        { label: this.$t('草稿'), value: 1 },
        { label: this.$t('审批中'), value: 2 },
        { label: this.$t('审批通过'), value: 3 },
        { label: this.$t('审批拒绝'), value: 4 },
        { label: this.$t('已退回'), value: 5 },
        { label: this.$t('已关闭'), value: 6 }
      ],
      plannerOptions: [],
      itemOptions: [],
      unitOptions: [],
      addrOptions: [],
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      itemCodeValueTemplate: codeNameColumn({
        firstKey: 'itemCode'
      }), // 物料
      unitValueTemplate: codeNameColumn({
        firstKey: 'unitCode',
        secondKey: 'unitName'
      }), // 单位
      addrValueTemplate: codeNameColumn({
        firstKey: 'consigneeAddressCode',
        secondKey: 'consigneeAddress'
      }), // 送货地址
      workCenterOptions: [],
      workCenterValueTemplate: codeNameColumn({
        firstKey: 'workCenterCode',
        secondKey: 'workCenterName'
      }),
      plannerTemplate: codeNameColumn({
        firstKey: 'userCode',
        secondKey: 'userName'
      }),
      siteOptions: [],
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        factoryCode: '',
        deliveryId: '1',
        driverName: '',
        number: '',
        remark: '',
        type: 0,
        itemCode: '',
        itemName: '',
        siteCode: '',
        siteName: '',
        supplierCode: '',
        supplierName: '',
        deliverAddrCode: null,
        deliverAddr: '',
        warehouseCode: '',
        warehouseName: '',
        planMemberCode: '',
        planner: '',
        supplierRemark: '',
        plannerRemark: '',
        approveOpinion: '',
        unitCode: '',
        unitName: '',
        deliverDate: ''
      },
      isEdit: false,
      editTimes: 0, // 大于1时，计划员可赋默认第一个
      rules: {
        factoryCode: [
          {
            required: true,
            message: this.$t('请选择工厂代码'),
            trigger: 'blur'
          }
        ],
        unitName: [
          {
            required: true,
            message: this.$t('由物料编码带出'),
            trigger: 'blur'
          }
        ],
        itemCode: [
          {
            required: true,
            message: this.$t('请选择物料编码'),
            trigger: 'blur'
          }
        ],
        applyQty: [
          {
            required: true,
            message: this.$t('请输入申请数量'),
            trigger: 'blur'
          }
        ],
        deliverDateCode: [
          {
            required: true,
            message: this.$t('请选择交货日期'),
            trigger: 'blur'
          }
        ],
        deliverAddrCode: [
          {
            required: true,
            message: this.$t('请选择送货地址'),
            trigger: 'blur'
          }
        ],
        planMemberCode: [
          {
            required: true,
            message: this.$t('请选择计划员'),
            trigger: 'blur'
          }
        ]
        // number: [
        //   { required: true, message: this.$t("请输入件数"), trigger: "blur" },
        // ],
      },
      dimensionFields: { text: 'message', value: 'code' },
      dimensionList: [],
      driverNameOptions: [], // 司机姓名下拉数据源
      idCardDisabled: false,
      isdeliverAddrShow: false
    }
  },
  activated() {
    this.$forceUpdate()
    this.init()
  },
  mounted() {
    this.init()
  },
  methods: {
    cancel() {
      for (let key in this.addForm) {
        this.addForm[key] = null
      }
      this.$router.push({
        name: 'nopo-delivery-request-supplier'
      })
      this.$bus.$emit('nopoDeliveryRequestSupplierUpdate')
    },

    siteCodeChange(e) {
      this.isdeliverAddrShow = false
      if (e.itemData) {
        this.addForm.factoryCode = e.itemData.value
        this.addForm.factoryName = e.itemData.label
        this.isdeliverAddrShow = true
      } else {
        this.addForm.factoryCode = null
        this.addForm.factoryName = null
        this.isdeliverAddrShow = false
      }
      this.getItemList()
      if (!this.addForm.factoryCode) return
      this.getWarehouseCodeOptions({
        text: null,
        siteCode: this.addForm.factoryCode
      })
      this.getPlannerByCode()
    },
    itemCodeChange(e) {
      if (e.itemData) {
        this.addForm.itemCode = e.itemData.itemCode
        this.addForm.itemName = e.itemData.itemName
        this.addForm.unitCode = e.itemData.baseMeasureUnitCode
        this.addForm.unitName = e.itemData.baseMeasureUnitName
      } else {
        this.addForm.itemCode = null
        this.addForm.itemName = null
        this.addForm.unitCode = null
        this.addForm.unitName = null
      }
    },
    unitCodeChange(e) {
      if (e.itemData) {
        this.addForm.unitCode = e.itemData.unitCode
        this.addForm.unitName = e.itemData.unitName
      } else {
        this.addForm.unitCode = null
        this.addForm.unitName = null
      }
    },
    plannerChange(e) {
      if (e.itemData) {
        this.addForm.planMemberCode = e.itemData.value
        this.addForm.planner = e.itemData.label
      } else {
        this.addForm.planMemberCode = null
        this.addForm.planner = null
      }
    },
    deliverAddrChange(e) {
      if (e.itemData) {
        this.addForm.deliverAddrId = e.itemData.id
        this.addForm.deliverAddrCode = e.itemData.consigneeAddressCode
        this.addForm.deliverAddr = e.itemData.consigneeAddress
        this.addForm.warehouseCode = e.itemData.siteAddress
        this.addForm.warehouseName = e.itemData.siteAddressName
      } else {
        this.addForm.deliverAddrId = null
        this.addForm.deliverAddrCode = null
        this.addForm.deliverAddr = null
        this.addForm.warehouseCode = null
        this.addForm.warehouseName = null
      }
    },
    // 日期转时间戳
    dateToStamp(e) {
      if (e) {
        this.addForm.deliverDate = dayjs(dayjs(e).format('YYYY-MM-DD')).valueOf()
      } else {
        this.addForm.deliverDate = null
      }
    },
    // 获取物料数据
    getItemList(args = { text: null }) {
      const { text } = args
      let params = {
        page: { current: 1, size: 20 },
        condition: 'and',
        rules: [
          {
            field: 'organizationCode',
            type: 'string',
            operator: 'contains',
            value: this.addForm.factoryCode
          },
          {
            field: 'itemCode',
            type: 'string',
            operator: 'contains',
            value: text
          }
        ]
      }
      this.$API.Inventory.getItemPageQuery({ ...params }).then((res) => {
        if (res.code === 200) {
          const _list = res?.data?.records || []
          this.itemOptions = addCodeNameKeyInList({
            firstKey: 'itemCode',
            secondKey: 'itemName',
            list: _list
          })
        }
      })
    },
    // 获取库存地点
    getWarehouseCodeOptions(args = {}) {
      const { text, siteCode, setSelectData } = args
      const _siteCode = siteCode ?? this.addForm.factoryCode
      const params = {
        siteCode: _siteCode,
        consigneeAddress: text ?? '',
        page: { current: 1, size: 20 }
      }
      this.$API.receiptAndDelivery.getSiteCodeAddr(params).then((res) => {
        let list = res.data.records || []
        // list.forEach((item) => {
        //   item.label = `${item.consigneeName}-${item.consigneePhone}-${item.consigneeAddress}`
        //   item.addressId = item.id
        // })
        this.addrOptions = addCodeNameKeyInList({
          firstKey: 'consigneeAddressCode',
          secondKey: 'consigneeAddress',
          list
        })
        if (setSelectData) {
          this.$nextTick(() => {
            setSelectData()
          })
        }
      })
    },
    // 根据工厂代码+物料编码带出计划员
    getPlannerByCode(text) {
      this.editTimes++
      const params = {
        factoryCode: this.addForm.factoryCode,
        user: text,
        page: { current: 1, size: 100 }
      }
      this.$API.predictCollaboration
        .getPlannerByCode(params)
        .then((res) => {
          if (res.code === 200) {
            this.plannerOptions = res.data.records.map((item) => {
              return {
                theCodeName: item.planner + '-' + item.plannerName,
                label: item.plannerName,
                value: item.planner
              }
            })
            if (!this.isEdit || this.editTimes > 1) {
              this.$nextTick(() => {
                this.addForm.planner = this.plannerOptions[0].label
                this.addForm.planMemberCode = this.plannerOptions[0].value
              })
            }
          }
        })
        .catch(() => {})
    },
    // 计划员
    plannerList(args = { text: null, setSelectData: null }) {
      const { text, setSelectData } = args
      const params = {
        user: text,
        page: { current: 1, size: 100 }
      }
      this.$API.predictCollaboration
        .getPlannerRelationSchedule(params)
        .then((res) => {
          if (res) {
            const list = res?.data?.records || []
            this.plannerOptions = addCodeNameKeyInList({
              firstKey: 'userCode',
              secondKey: 'userName',
              list
            })
            if (setSelectData) {
              setSelectData(this.plannerOptions)
            }
          }
        })
        .catch(() => {})
    },
    // 单位
    postUnitFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        unitCode: text ?? '',
        id: '',
        tenantId: '',
        typeId: '',
        typeCode: '',
        unitGroupId: ''
      }
      this.$API.receiptAndDelivery
        .getUnitList(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.unitOptions = addCodeNameKeyInList({
              firstKey: 'unitCode',
              secondKey: 'unitName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.unitOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    getSiteOptions() {
      this.$API.receiptAndDelivery
        .getFactoryByCode([this.addForm.supplierCode])
        .then((res) => {
          if (res.code === 200) {
            this.siteOptions = res.data.map((item) => {
              return {
                theCodeName: item.siteCode + '-' + item.siteName,
                label: item.siteName,
                value: item.siteCode
              }
            })
          }
        })
        .catch(() => {})
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        page: {
          current: 1,
          pages: 0,
          size: 20
        }
      }
      this.$API.receiptAndDelivery
        .getFactoryInfo(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    init() {
      this.$refs.addForm.clearValidate()
      const _userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      this.addForm.supplierCode = _userInfo.accountName
      this.addForm.supplierName = _userInfo.entityName
      this.getSiteOptions()
      if (this.$route.query?.id ?? false) {
        this.itemOptions = []
        this.addrOptions = []
        this.addForm = { ...this.$route.query }
        delete this.addForm.column
        let _date = this.$route.query.deliverDate
        this.$set(this.addForm, 'id', this.$route.query?.id)
        this.addForm.deliverAddrId = this.$route?.query?.deliverAddrCode
        this.isEdit = true
        if (_date && !isNaN(Number(_date))) {
          this.addForm.deliverDate = _date
          this.addForm.deliverDateCode = dayjs(Number(_date)).format('YYYY-MM-DD')
        }
      }
      this.$nextTick(() => {
        this.addForm.status = this.$route.query?.status ?? 1
      })
      if (this.$route.query?.planner) {
        this.addForm.planMemberCode = this.$route.query?.planMemberCode || ''
        this.addForm.planner = this.$route.query?.planner || ''
      } else {
        this.addForm.planMemberCode = ''
        this.addForm.planner = ''
      }
    },
    getWorkCenter(args) {
      const { text, siteCode, setSelectData } = args
      const _siteCode = siteCode ?? this.addForm.factoryCode
      const params = {
        siteCode: _siteCode,
        workCenterCode: text ?? ''
      }
      this.$API.receiptAndDelivery.getWorkCenterBySite(params).then((res) => {
        let list = res?.data || []
        this.workCenterOptions = addCodeNameKeyInList({
          firstKey: 'workCenterCode',
          secondKey: 'workCenterName',
          list
        })
        if (setSelectData) {
          this.$nextTick(() => {
            setSelectData(this.workCenterOptions)
          })
        }
      })
    },
    workCenterChange(e) {
      const { itemData } = e
      if (itemData) {
        this.addForm.workCenterCode = itemData.workCenterCode
        this.addForm.workCenterName = itemData.workCenterName
      } else {
        this.addForm.workCenterCode = null
        this.addForm.workCenterName = null
      }
    },
    getDimension() {
      this.$API.moduleConfig.getDimension().then((res) => {
        this.dimensionList = res.data || []
      })
    },
    save() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          const param = {
            ...this.addForm
          }
          delete param.deliverDateCode
          this.$store.commit('startLoading')
          this.$API.receiptAndDelivery
            .saveNoPoDeliveryRequestDocument(param)
            .then((res) => {
              if (res && res.code === 200) {
                this.$store.commit('endLoading')
                this.$toast({
                  content: this.$t('保存成功'),
                  type: 'success'
                })
                this.cancel()
              }
            })
            .catch((error) => {
              this.$store.commit('endLoading')
              this.$toast({
                content: this.$t(error.msg),
                type: 'error'
              })
            })
        }
      })
    },
    confirm() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          const params = { ...this.addForm }
          delete params.deliverDateCode
          this.$store.commit('startLoading')
          this.$API.receiptAndDelivery
            .createNoPoDeliveryRequestDocument(params)
            .then((res) => {
              if (res.code == 200) {
                this.$store.commit('endLoading')
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$router.push({
                  name: 'nopo-delivery-request-supplier'
                })
                this.$bus.$emit('nopoDeliveryRequestSupplierUpdate')
              }
            })
            .catch((error) => {
              this.$store.commit('endLoading')
              console.log(error)
            })
        }
      })
    },

    handleClose() {
      this.$emit('handleDialogShow', false)
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .nopo-style .mt-form {
  padding: 1.5rem;
}
/deep/ .buttons-style button {
  border-radius: 5px;
  background-color: var(--plugin-ct-content-color);
}
</style>
