<!-- 供方 - 报关要素资料 - new -->
<template>
  <div class="full-height">
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('物料编码')" prop="itemCode">
          <mt-input
            v-model="itemCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
            @change="(e) => onChange(e, 'itemCode')"
          />
          <!-- <RemoteAutocomplete
            v-model="searchFormModel.itemCode"
            url="/masterDataManagement/tenant/item/paged-auth?BU_CODE=${localStorage.getItem('currentBu')}"
            :placeholder="$t('请选择')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :search-fields="['itemName', 'itemCode']"
          /> -->
        </mt-form-item>
        <mt-form-item :label="$t('商品申报品名')" prop="goodsDeclareName">
          <mt-input
            v-model="searchFormModel.goodsDeclareName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('商品编码')" prop="goodsCode">
          <mt-input
            v-model="searchFormModel.goodsCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('状态')" prop="approveStatus">
          <mt-multi-select
            v-model="searchFormModel.approveStatus"
            :data-source="statusOptions"
            :fields="{ text: 'label', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <div style="padding: 10px; color: red">
      数据填写有疑问，请与关务人员（陈先生）联系！座机：13751842738 邮箱：<EMAIL>
    </div>
    <sc-table
      ref="sctableRef"
      grid-id="187a17b6-19c3-49f0-8314-c42271ec13b0"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      keep-source
      @edit-actived="editBegin"
      @edit-closed="editComplete"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #itemCodeEdit="{ row }">
        <vxe-pulldown ref="xDownItem" transfer>
          <template #default>
            <vxe-input
              :value="row.itemCode"
              :placeholder="$t('请选择物料')"
              readonly
              @click="focusItemCode"
            />
          </template>
          <template #dropdown>
            <vxe-input
              prefix-icon="vxe-icon-search"
              :placeholder="$t('搜索')"
              @keyup="keyupItemCode"
              style="width: 100%"
            />
            <vxe-list height="200" class="predict-vxe-dropdown" :data="itemOptions" auto-resize>
              <template #default="{ items }">
                <div
                  v-show="itemOptions.length"
                  class="predict-vxe-list-item"
                  v-for="item in items"
                  :key="item.value"
                  @click="selectItemCode(item, row)"
                >
                  <span>{{ item.label }}</span>
                </div>
                <div v-show="!itemOptions.length" class="predict-vxe-list-item">
                  <span>{{ $t('暂无数据') }}</span>
                </div>
              </template>
            </vxe-list>
          </template>
        </vxe-pulldown>
      </template>
      <template #itemNameEdit="{ row }">
        <vxe-input :value="row.itemName" disabled />
      </template>
      <template #netWeightEdit="{ row }">
        <vxe-input type="number" v-model="row.netWeight" :min="0" :placeholder="$t('请输入')" />
      </template>
      <template #imgSlots="{ row, column }">
        <vxe-button size="small" @click="handleUpload(row, column)">{{
          $t('查看附件')
        }}</vxe-button>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
    <uploader-dialog
      ref="uploaderDialog"
      @change="fileChange"
      @confirm="batchUpload"
    ></uploader-dialog>
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, statusOptions } from './config'
import { getHeadersFileName, download } from '@/utils/utils'
import { utils } from '@mtech-common/utils'

export default {
  components: {
    CollapseSearch,
    ScTable,
    UploaderDialog: () => import('./components/uploaderDialog')
  },
  data() {
    return {
      searchFormModel: {},
      itemCode: null,
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info' },
        // { code: 'closeEdit', name: this.$t('取消编辑'), status: 'info' },
        { code: 'save', name: this.$t('保存'), status: 'info' },
        { code: 'delete', name: this.$t('删除'), status: 'info' },
        { code: 'submit', name: this.$t('提交'), status: 'info' },
        { code: 'import', name: this.$t('导入'), status: 'info' },
        { code: 'export', name: this.$t('导出'), status: 'info' },
        {
          code: 'batchUpload',
          name: this.$t('图片批量上传'),
          status: 'info'
        }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      getItemDataSource: () => {},
      itemOptions: [],

      statusOptions,

      fileList: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    editConfig() {
      return {
        enabled: true,
        trigger: 'dblclick',
        mode: 'row',
        showStatus: true,
        beforeEditMethod: this.beforeEditMethod
      }
    }
  },
  created() {
    this.getItemCodeList()
    this.getItemDataSource = utils.debounce(this.getItemCodeList, 1000)
    this.getTableData()
  },
  methods: {
    beforeEditMethod({ row }) {
      if ([2].includes(row.approveStatus)) {
        this.$toast({ content: this.$t('待审批状态的数据不可编辑'), type: 'warning' })
        return false
      }
      return true
    },
    handleUpload(row, column) {
      let params = {
        docId: row.id,
        docType: null
      }
      switch (column.field) {
        case 'singleProductImageUrl':
          params.docType = '19'
          break
        case 'productInPackagingPicturesUrl':
          params.docType = '20'
          break
        case 'productOutPackagingPicturesUrl':
          params.docType = '21'
          break
        case 'brandAuthorizationCertificateUrl':
          params.docType = '22'
          break
        default:
          break
      }
      this.$dialog({
        modal: () => import('./components/FileManage.vue'),
        data: {
          title: this.$t('查看附件'),
          type: [2].includes(row.approveStatus) ? 'download' : 'upload',
          params
        }
      })
    },
    focusItemCode() {
      this.$refs.xDownItem.showPanel()
    },
    keyupItemCode(e) {
      this.getItemDataSource(e)
    },
    selectItemCode(e, row) {
      row.itemCode = e.itemCode
      row.itemName = e.itemName
    },
    getItemCodeList(e = { value: '' }) {
      const { value } = e
      //物料下拉
      let params = {
        keyword: value || '',
        pageSize: 50
      }
      this.$API.masterData.getItemByKeyword(params).then((res) => {
        const list = res.data?.records || []
        const newData = list.map((i) => {
          return {
            ...i,
            label: `${i.itemCode}-${i.itemName}`,
            value: i.itemCode
          }
        })
        this.itemOptions = [...newData]
      })
    },
    onChange(e, field) {
      if (e) {
        this.searchFormModel[`${field}`] = this[field].split(' ')
      } else {
        this.searchFormModel[`${field}`] = null
        this[field] = null
      }
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.itemCode = null
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.receiptAndDelivery
        .pageCustomsDeclarationElementsSupNewApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const list = res.data?.records
        this.tableData = list
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)
      }
    },
    editBegin(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        this.getItemDataSource({ value: row.itemCode }, row)
      }
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        if (args.$event.target.innerText === this.$t('取消编辑')) {
          this.tableRef.clearEdit()
          return
        }
        // 1、 校验必填
        if (!this.isValidData(row)) {
          this.tableRef.setEditRow(row)
          return
        }
        // 2、 调保存接口
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认保存？')
          },
          success: () => {
            this.handleSave(row)
          },
          close: () => {
            this.handleSearch()
          }
        })
      }
    },
    isValidData(row) {
      let valid = false
      if (!row.itemCode) {
        this.$toast({ content: this.$t('请选择物料编码'), type: 'warning' })
      } else if (!row.goodsCode) {
        this.$toast({ content: this.$t('请输入商品编码'), type: 'warning' })
      } else if (!row.goodsDeclareName) {
        this.$toast({ content: this.$t('请输入商品申报品名'), type: 'warning' })
      } else if (!row.declareElement) {
        this.$toast({ content: this.$t('请输入申报要素'), type: 'warning' })
      } else if (!row.netWeight) {
        this.$toast({ content: this.$t('请输入单个净重'), type: 'warning' })
      } else if (!row.materialBrand) {
        this.$toast({ content: this.$t('请输入品牌'), type: 'warning' })
      } else if (!row.model) {
        this.$toast({ content: this.$t('请输入型号'), type: 'warning' })
      } else if (!row.origProdtCountry) {
        this.$toast({ content: this.$t('请输入原产国'), type: 'warning' })
      } else if (!row.manufacturerName) {
        this.$toast({ content: this.$t('请输入生产商名称'), type: 'warning' })
      } else if (!row.perBoxQty) {
        this.$toast({ content: this.$t('请输入每箱数量'), type: 'warning' })
      } else if (!row.length) {
        this.$toast({ content: this.$t('请输入长'), type: 'warning' })
      } else if (!row.width) {
        this.$toast({ content: this.$t('请输入宽'), type: 'warning' })
      } else if (!row.height) {
        this.$toast({ content: this.$t('请输入高'), type: 'warning' })
      } else if (!row.invoiceTaxCategorizeCode) {
        this.$toast({ content: this.$t('请输入开票税收分类编码'), type: 'warning' })
      } else if (!row.invoiceGoodsName) {
        this.$toast({ content: this.$t('请输入开票品名'), type: 'warning' })
      } else if (!row.invoiceTransactionUnit) {
        this.$toast({ content: this.$t('请输入开票成交单位'), type: 'warning' })
      } else if (!row.fillingPerson) {
        this.$toast({ content: this.$t('请输入填制人'), type: 'warning' })
      } else if (!row.fillingPersonPhone) {
        this.$toast({ content: this.$t('请输入填制人电话'), type: 'warning' })
      } else {
        valid = true
      }
      return valid
    },
    handleSave(row) {
      let params = { ...row }
      let api = this.$API.receiptAndDelivery.editCustomsDeclarationElementsSupNewApi
      if (params.id.includes('row_')) {
        params.id = null
        api = this.$API.receiptAndDelivery.addCustomsDeclarationElementsSupNewApi
      }
      api(params)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.handleSearch()
          }
        })
        .catch(() => {
          // 当出现错误时，指定行进入编辑状态
          this.tableRef.setEditRow(row)
        })
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const ids = []
      selectedRecords.forEach((item) => {
        ids.push(item.id)
      })
      const commonToolbar = ['delete', 'submit', 'save']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.code === 'save' && selectedRecords.length > 1) {
        this.$toast({ content: this.$t('只能选择一行数据进行保存操作'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'closeEdit':
          this.tableRef.clearEdit()
          this.handleSearch()
          break
        case 'save':
          this.handleSave(selectedRecords[0])
          break
        case 'delete':
          this.handleDelete(ids)
          break
        case 'submit':
          this.handleSubmit(ids)
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          this.handleExport()
          break
        case 'batchUpload':
          this.handleBatchUpload()
          break
        default:
          break
      }
    },
    handleAdd() {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      const item = {
        supplierCode: userInfo.accountName,
        supplierName: userInfo.tenantName,
        itemCode: null,
        itemName: null,
        netWeightUnit: 'KG',
        filings: 1
      }
      this.tableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },
    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除？')
        },
        success: () => {
          this.$API.receiptAndDelivery
            .deleteCustomsDeclarationElementsSupNewApi({ ids })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({ content: this.$t('删除成功'), type: 'success' })
                this.handleSearch()
              }
            })
        }
      })
    },
    handleSubmit(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交？')
        },
        success: () => {
          this.$API.receiptAndDelivery
            .submitCustomsDeclarationElementsSupNewApi({ ids })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({ content: this.$t('提交成功'), type: 'success' })
                this.handleSearch()
              }
            })
        }
      })
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.receiptAndDelivery.importCustomsDeclarationElementsSupNewApi,
          downloadTemplateApi:
            this.$API.receiptAndDelivery.downloadCustomsDeclarationElementsSupNewApi,
          validApi: this.$API.receiptAndDelivery.importValidCustomsDeclarationElementsSupNewApi,
          paramsKey: 'excel'
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    handleExport() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery.exportCustomsDeclarationElementsSupNewApi(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleBatchUpload() {
      const dialogParams = {
        isView: false, //是否可上传
        required: false, // 是否必须
        uploadText: this.$t('请点击此处上传文件'),
        title: this.$t('批量上传'),
        saveUrl: `/file/user/file/publicUpload?useType=2`
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },
    fileChange(data) {
      this.fileList = data
    },
    async batchUpload() {
      const fileList = this.fileList.map((item) => {
        item.sysFileId = item.id
        return item
      })
      fileList.forEach((item) => {
        delete item.id
      })
      const res = await this.$API.receiptAndDelivery.saveBatchFileCustomsDeclarationElementsNewApi(
        fileList
      )
      if (res.code === 200) {
        this.$toast({
          content: this.$t('上传成功'),
          type: 'success'
        })
        this.handleSearch()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
