import { i18n } from '@/main.js'

export const statusOptions = [
  { label: i18n.t('新建'), value: 0 },
  { label: i18n.t('已修改'), value: 1 },
  { label: i18n.t('待审批'), value: 2 },
  { label: i18n.t('审批通过'), value: 3 },
  { label: i18n.t('审批驳回'), value: 4 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left'
  },
  {
    field: 'declareCustomsCode',
    title: i18n.t('识别码'),
    minWidth: 120
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'itemCodeEdit'
    }
  },
  {
    field: 'itemName',
    title: i18n.t('物料描述'),
    minWidth: 140,
    editRender: {},
    slots: {
      edit: 'itemNameEdit'
    }
  },
  {
    field: 'goodsCode',
    title: i18n.t('商品编码'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { type: 'number', placeholder: i18n.t('请输入') } }
  },
  {
    field: 'goodsDeclareName',
    title: i18n.t('商品申报品名'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'declareElement',
    title: i18n.t('申报要素'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'netWeight',
    title: i18n.t('单个净重'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'netWeightEdit'
    }
  },
  {
    field: 'netWeightUnit',
    title: i18n.t('净重单位'),
    minWidth: 120
  },
  {
    field: 'materialBrand',
    title: i18n.t('品牌'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'filings',
    title: i18n.t('是否品牌已备案'),
    minWidth: 120,
    editRender: {
      name: 'select',
      options: [
        { label: i18n.t('是'), value: 1 },
        { label: i18n.t('否'), value: 0 }
      ]
    }
  },
  {
    field: 'model',
    title: i18n.t('型号'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'origProdtCountry',
    title: i18n.t('原产国'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'manufacturerName',
    title: i18n.t('生产商名称'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'perBoxQty',
    title: i18n.t('每箱数量'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入'), type: 'number' } }
  },
  {
    field: 'length',
    title: i18n.t('长'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入'), type: 'number' } }
  },
  {
    field: 'width',
    title: i18n.t('宽'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入'), type: 'number' } }
  },
  {
    field: 'height',
    title: i18n.t('高'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入'), type: 'number' } }
  },
  {
    field: 'invoiceTaxCategorizeCode',
    title: i18n.t('开票税收分类编码（增值税发票）'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'invoiceGoodsName',
    title: i18n.t('开票品名（增值税发票）'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'invoiceTransactionUnit',
    title: i18n.t('开票成交单位（增值税发票）'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'singleProductImageUrl',
    title: i18n.t('单个产品图片'),
    minWidth: 120,
    editRender: {},
    slots: {
      default: 'imgSlots',
      edit: 'imgSlots'
    }
  },
  {
    field: 'productInPackagingPicturesUrl',
    title: i18n.t('产品内包装图片'),
    minWidth: 120,
    editRender: {},
    slots: {
      default: 'imgSlots',
      edit: 'imgSlots'
    }
  },
  {
    field: 'productOutPackagingPicturesUrl',
    title: i18n.t('产品外包装图片'),
    minWidth: 120,
    editRender: {},
    slots: {
      default: 'imgSlots',
      edit: 'imgSlots'
    }
  },
  {
    field: 'brandAuthorizationCertificateUrl',
    title: i18n.t('品牌授权证明'),
    minWidth: 120,
    editRender: {},
    slots: {
      default: 'imgSlots',
      edit: 'imgSlots'
    }
  },
  {
    field: 'fillingPerson',
    title: i18n.t('填制人'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'fillingPersonPhone',
    title: i18n.t('填制人电话'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'supplierRemarks',
    title: i18n.t('供方备注'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'purchaserRemark',
    title: i18n.t('采方备注'),
    minWidth: 120
  },
  {
    field: 'approveStatus',
    title: i18n.t('审批状态'),
    minWidth: 120,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.label : ''
    }
  }
]
