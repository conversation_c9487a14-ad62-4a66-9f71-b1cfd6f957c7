<template>
  <div class="detail-top-info" style="height: 100%">
    <div class="header-box">
      <div class="middle-blank"></div>
      <span class="header-box-btn" v-waves type="info" @click="cancel">{{ $t('取消') }}</span>
      <span class="header-box-btn" v-waves type="primary" @click="save">{{ $t('保存') }}</span>
      <span class="header-box-btn" v-waves type="primary" @click="confirm">{{ $t('提交') }}</span>
    </div>
    <div>
      <mt-form ref="addForm" :model="addForm" :rules="rules" :validate-on-rule-change="false">
        <mt-row v-if="isEdit" :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('申请编码：')" label-style="left">
              {{ addForm.applyCode }}
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="factoryCode" :label="$t('工厂代码')">
              <debounce-filter-select
                v-model="addForm.factoryCode"
                :request="postSiteFuzzyQuery"
                :data-source="siteOptions"
                :show-clear-button="true"
                :fields="{ text: 'theCodeName', value: 'siteCode' }"
                :value-template="siteCodeValueTemplate"
                :placeholder="$t('请选择工厂代码')"
                @change="siteCodeChange"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="factoryName" :label="$t('工厂名称')">
              <mt-input
                disabled
                v-model="addForm.factoryName"
                :show-clear-button="true"
                :placeholder="$t('由工厂代码带出')"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="itemCode" :label="$t('物料编码')">
              <debounce-filter-select
                v-model="addForm.itemCode"
                :request="getItemList"
                :data-source="itemOptions"
                :show-clear-button="true"
                :fields="{ text: 'theCodeName', value: 'itemCode' }"
                :value-template="itemCodeValueTemplate"
                :placeholder="addForm.factoryCode ? $t('请选择物料编码') : $t('请先选择工厂代码')"
                @change="itemCodeChange"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="itemName" :label="$t('物料描述')">
              <mt-input
                disabled
                v-model="addForm.itemName"
                :show-clear-button="true"
                :placeholder="$t('由物料编码带出')"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="applyQty" :label="$t('申请数量')">
              <mt-input-number
                v-model="addForm.applyQty"
                :precision="0"
                :min="0"
                :show-clear-button="true"
                :placeholder="$t('申请数量')"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="unitName" :label="$t('单位')">
              <mt-input
                v-model="addForm.unitName"
                :placeholder="$t('由物料编码带出')"
                :disabled="true"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="deliverDateCode" :label="$t('交货日期')">
              <mt-date-picker
                :placeholder="$t('请选择交货日期')"
                v-model="addForm.deliverDateCode"
                format="yyyy-MM-dd"
                :show-clear-button="false"
                :allow-edit="false"
                :min="minDate"
                @change="(e) => dateToStamp(e, 'deliverDate')"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="deliverAddrId" :label="$t('送货地址')">
              <debounce-filter-select
                v-model="addForm.deliverAddrId"
                :request="getWarehouseCodeOptions"
                :data-source="addrOptions"
                :show-clear-button="true"
                :fields="{ text: 'theCodeName', value: 'consigneeAddressCode' }"
                :value-template="addrValueTemplate"
                :placeholder="addForm.factoryCode ? $t('请选择送货地址') : $t('请先选择工厂代码')"
                @change="deliverAddrChange"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="saleOrderNo" :label="$t('销售订单号')">
              <mt-input
                style="width: 100%"
                v-model="addForm.saleOrderNo"
                show-clear-button
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="workCenterCode" :label="$t('工作中心')">
              <RemoteAutocomplete
                v-model="addForm.workCenterCode"
                url="/srm-purchase-execute/tenant/siteTenantExtend/getWorkCenterCodeBySiteCodes"
                :fields="{ text: 'workCenterName', value: 'workCenterCode' }"
                :params="{ siteCodes: [addForm.factoryCode] }"
                records-position="data"
                :placeholder="$t('请输入关键字')"
                @change="workCenterChange"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="supplierCode" :label="$t('供应商代码')" label-style="top">
              <debounce-filter-select
                v-model="addForm.supplierCode"
                :request="getSupplier"
                :data-source="supplierOptions"
                :fields="{ text: 'theCodeName', value: 'supplierCode' }"
                :value-template="supplierCodeValueTemplate"
                :show-clear-button="true"
                :allow-filtering="true"
                :placeholder="$t('请选择供应商代码')"
                @change="supplierCodeChange"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="supplierName" :label="$t('供应商名称')">
              <mt-input
                style="width: 100%"
                v-model="addForm.supplierName"
                :disabled="true"
                :placeholder="$t('由供应商编码带出')"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="planner" :label="$t('计划员')">
              <mt-input
                style="width: 100%"
                v-model="addForm.planner"
                :disabled="true"
                :placeholder="$t('')"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="status" :label="$t('单据状态')">
              <mt-select
                v-model="addForm.status"
                :placeholder="$t('')"
                :data-source="statusOptions"
                :disabled="true"
                :fields="{ text: 'label', value: 'value' }"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row v-if="isEdit" :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="createUserName" :label="$t('创建人')">
              <mt-input v-model="addForm.createUserName" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="createTime" :label="$t('创建时间')">
              <mt-input v-model="addForm.createTime" disabled />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row>
          <mt-form-item prop="plannerRemark" :label="$t('计划员备注')" class="full-width">
            <mt-input
              v-model="addForm.plannerRemark"
              :placeholder="$t('请输入计划员备注（字数限制250个）')"
              :maxlength="250"
            />
          </mt-form-item>
        </mt-row>
        <mt-row v-if="isEdit">
          <mt-form-item prop="supplierRemark" :label="$t('供方备注')" class="full-width">
            <mt-input v-model="addForm.supplierRemark" disabled />
          </mt-form-item>
        </mt-row>
        <mt-row>
          <mt-form-item prop="approveOpinion" :label="$t('审批意见')" class="full-width">
            <mt-input v-model="addForm.approveOpinion" :placeholder="$t('')" :disabled="true" />
          </mt-form-item>
        </mt-row>
      </mt-form>
    </div>
  </div>
</template>

<script>
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import dayjs from 'dayjs'
export default {
  components: {
    DebounceFilterSelect
  },
  data() {
    const _year = new Date().getFullYear()
    const _mon = new Date().getMonth()
    const _day = new Date().getDate()
    return {
      addForm: {
        factoryCode: '',
        factoryName: '',
        itemCode: '',
        itemName: '',
        applyQty: '',
        unitCode: '',
        unitName: '',
        deliverDateCode: '',
        deliverAddrId: null,
        deliverAddrCode: null,
        deliverAddr: null,
        warehouseCode: null,
        warehouseName: null,
        supplierCode: undefined,
        supplierName: '',
        planMemberCode: '',
        planner: '',
        supplierRemark: '',
        plannerRemark: '',
        approveOpinion: ''
      },
      rules: {
        factoryCode: [
          {
            required: true,
            message: this.$t('请选择工厂代码'),
            trigger: ['blur', 'change']
          }
        ],
        itemCode: [
          {
            required: true,
            message: this.$t('请选择物料编码'),
            trigger: ['blur', 'change']
          }
        ],
        applyQty: [
          {
            required: true,
            message: this.$t('请输入申请数量'),
            trigger: ['blur', 'change']
          }
        ],
        unitName: [
          {
            required: true,
            message: this.$t('由物料编码带出'),
            trigger: ['blur', 'change']
          }
        ],
        deliverDateCode: [
          {
            required: true,
            message: this.$t('请选择交货日期'),
            trigger: ['blur', 'change']
          }
        ],
        deliverAddrId: [
          {
            required: true,
            message: this.$t('请选择送货地址'),
            trigger: ['blur', 'change']
          }
        ],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择供应商代码'),
            trigger: ['blur', 'change']
          }
        ],
        planner: [
          {
            required: true,
            message: this.$t('请输入计划员'),
            trigger: ['blur', 'change']
          }
        ]
      },
      siteOptions: [],
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      itemOptions: [],
      itemCodeValueTemplate: codeNameColumn({
        firstKey: 'itemCode'
      }), // 物料
      addrOptions: [],
      addrValueTemplate: codeNameColumn({
        firstKey: 'consigneeAddressCode',
        secondKey: 'consigneeAddress'
      }), // 送货地址
      workCenterOptions: [],
      workCenterValueTemplate: codeNameColumn({
        firstKey: 'workCenterCode',
        secondKey: 'workCenterName'
      }),
      supplierOptions: [],
      supplierCodeValueTemplate: codeNameColumn({
        firstKey: 'supplierCode'
      }), // 供应商
      minDate: new Date(_year, _mon, _day),
      statusOptions: [
        { label: this.$t('草稿'), value: 1 },
        { label: this.$t('审批中'), value: 2 },
        { label: this.$t('审批通过'), value: 3 },
        { label: this.$t('审批拒绝'), value: 4 },
        { label: this.$t('已退回'), value: 5 },
        { label: this.$t('已关闭'), value: 6 }
      ],
      isEdit: false
    }
  },
  activated() {
    this.$forceUpdate()
    this.init()
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      if (this.$route.query?.id) {
        this.itemOptions = []
        this.addrOptions = []
        this.supplierOptions = []
        this.addForm = { ...this.$route.query }
        this.isEdit = true
        let _date = this.$route.query.deliverDate
        if (_date && !isNaN(Number(_date))) {
          this.addForm.deliverDate = _date
          this.addForm.deliverDateCode = dayjs(Number(_date)).format('YYYY-MM-DD')
        }
        this.postSiteFuzzyQuery({
          text: this.$route.query.factoryCode || '',
          setSelectData: () => {
            this.addForm.factoryCode = this.$route.query.factoryCode
            this.addForm.factoryName = this.$route.query.factoryName
          }
        })
        this.getSupplier({
          text: this.$route.query.supplierCode || '',
          setSelectData: () => {
            this.addForm.supplierCode = this.$route.query?.supplierCode || undefined
            this.addForm.supplierName = this.$route.query?.supplierName || ''
          }
        })
        this.addForm.planMemberCode = this.$route.query?.planMemberCode || ''
        this.addForm.planner = this.$route.query?.planner || ''
      } else {
        this.postSiteFuzzyQuery({ text: this.addForm.factoryCode || '' })
        this.getSupplier({
          text: this.addForm.supplierCode || '',
          setSelectData: () => {
            this.addForm.supplierCode = this.addForm.supplierCode || undefined
            this.addForm.supplierName = this.addForm.supplierName || ''
          }
        })
        const _userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
        this.plannerChange(_userInfo)
      }
      this.$nextTick(() => {
        this.addForm.status = this.$route.query?.status ?? 1
      })
      this.$refs.addForm.clearValidate()
    },
    getWorkCenter(args) {
      const { text, siteCode, setSelectData } = args
      const _siteCode = siteCode ?? this.addForm.factoryCode
      const params = {
        siteCode: _siteCode,
        workCenterCode: text ?? ''
      }
      this.$API.receiptAndDelivery.getWorkCenterBySite(params).then((res) => {
        let list = res?.data || []
        this.workCenterOptions = addCodeNameKeyInList({
          firstKey: 'workCenterCode',
          secondKey: 'workCenterName',
          list
        })
        if (setSelectData) {
          this.$nextTick(() => {
            setSelectData(this.workCenterOptions)
          })
        }
      })
    },
    workCenterChange(e) {
      const { itemData } = e
      if (itemData) {
        this.addForm.workCenterCode = itemData.workCenterCode
        this.addForm.workCenterName = itemData.workCenterName
      } else {
        this.addForm.workCenterCode = null
        this.addForm.workCenterName = null
      }
    },
    // 主数据 获取供应商
    getSupplier(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData
        .getSupplier(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.supplierOptions = addCodeNameKeyInList({
              firstKey: 'supplierCode',
              secondKey: 'supplierName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.supplierOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 供应商 change
    supplierCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.addForm.supplierCode = itemData.supplierCode
        this.addForm.supplierName = itemData.supplierName
      } else {
        this.addForm.supplierCode = ''
        this.addForm.supplierName = ''
      }
    },
    siteCodeChange(e) {
      if (e.itemData) {
        this.addForm.factoryCode = e.itemData.siteCode
        this.addForm.factoryName = e.itemData.siteName
      } else {
        this.addForm.factoryCode = null
        this.addForm.factoryName = null
      }
      if (!this.addForm.factoryCode) return
      const text = this.$route.query?.id ? this.$route.query?.itemCode : ''
      this.getItemList({ text })
      this.getWarehouseCodeOptions({
        text: null,
        siteCode: this.addForm.factoryCode
      })
    },
    itemCodeChange(e) {
      if (e.itemData) {
        this.addForm.itemCode = e.itemData.itemCode
        this.addForm.itemName = e.itemData.itemName
        this.addForm.unitCode = e.itemData.baseMeasureUnitCode
        this.addForm.unitName = e.itemData.baseMeasureUnitName
      } else {
        this.addForm.itemCode = null
        this.addForm.itemName = null
        this.addForm.unitCode = null
        this.addForm.unitName = null
      }
    },
    unitCodeChange(e) {
      if (e.itemData) {
        this.addForm.unitCode = e.itemData.unitCode
        this.addForm.unitName = e.itemData.unitName
      } else {
        this.addForm.unitCode = null
        this.addForm.unitName = null
      }
    },
    plannerChange(e) {
      if (e) {
        this.addForm.planMemberCode = e.externalCode
        this.addForm.planner = e.accountName
      } else {
        this.addForm.planMemberCode = null
        this.addForm.planner = null
      }
    },
    deliverAddrChange(e) {
      if (e.itemData) {
        this.addForm.deliverAddrId = e.itemData.id
        this.addForm.deliverAddrCode = e.itemData.consigneeAddressCode
        this.addForm.deliverAddr = e.itemData.consigneeAddress
        this.addForm.warehouseCode = e.itemData.siteAddress
        this.addForm.warehouseName = e.itemData.siteAddressName
      } else {
        this.addForm.deliverAddrId = null
        this.addForm.deliverAddrCode = null
        this.addForm.deliverAddr = null
        this.addForm.warehouseCode = null
        this.addForm.warehouseName = null
      }
    },
    // 日期转时间戳
    dateToStamp(e) {
      if (e) {
        this.addForm.deliverDate = dayjs(dayjs(e).format('YYYY-MM-DD')).valueOf()
      } else {
        this.addForm.deliverDate = null
      }
    },
    // 获取物料数据
    getItemList(args = { text: null }) {
      const { text } = args
      let params = {
        page: { current: 1, size: 20 },
        condition: 'and',
        rules: [
          {
            field: 'organizationCode',
            type: 'string',
            operator: 'contains',
            value: this.addForm.factoryCode
          },
          {
            field: 'itemCode',
            type: 'string',
            operator: 'contains',
            value: text
          }
        ]
      }
      this.$API.Inventory.getItemPageQuery({ ...params }).then((res) => {
        if (res.code === 200) {
          const _list = res?.data?.records || []
          this.itemOptions = addCodeNameKeyInList({
            firstKey: 'itemCode',
            secondKey: 'itemName',
            list: _list
          })
        }
      })
    },
    // 获取库存地点
    getWarehouseCodeOptions(args = {}) {
      const { text, siteCode, setSelectData } = args
      const _siteCode = siteCode ?? this.addForm.factoryCode
      const params = {
        siteCode: _siteCode,
        consigneeAddress: text ?? '',
        page: { current: 1, size: 20 }
      }
      this.$API.receiptAndDelivery.getSiteCodeAddr(params).then((res) => {
        let list = res.data.records || []
        this.addrOptions = addCodeNameKeyInList({
          firstKey: 'consigneeAddressCode',
          secondKey: 'consigneeAddress',
          list
        })
        if (setSelectData) {
          this.$nextTick(() => {
            setSelectData(this.addrOptions)
          })
        }
      })
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        page: {
          current: 1,
          pages: 0,
          size: 20
        }
      }
      this.$API.receiptAndDelivery
        .getFactoryInfo(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    save() {
      this.$store.commit('startLoading')
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          const param = {
            ...this.addForm
          }
          delete param.deliverDateCode
          this.$API.receiptAndDelivery
            .saveNoPoDeliveryRequestPurChaseDocument(param)
            .then((res) => {
              if (res && res.code === 200) {
                this.$toast({
                  content: this.$t('保存成功'),
                  type: 'success'
                })
                this.cancel()
              }
            })
            .catch((error) => {
              this.$toast({
                content: this.$t(error.msg),
                type: 'error'
              })
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        } else {
          this.$store.commit('endLoading')
        }
      })
    },
    confirm() {
      this.$store.commit('startLoading')
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          const params = { ...this.addForm }
          delete params.deliverDateCode
          this.$API.receiptAndDelivery
            .createNoPoDeliveryRequestPurChaseDocument(params)
            .then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$router.push({
                  name: 'nopo-delivery-request-purchase'
                })
                this.$bus.$emit('nopoDeliveryRequestPurChaseUpdate')
              }
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        } else {
          this.$store.commit('endLoading')
        }
      })
    },
    cancel() {
      for (let key in this.addForm) {
        this.addForm[key] = null
      }
      this.$router.push({
        name: 'nopo-delivery-request-purchase'
      })
      this.$bus.$emit('nopoDeliveryRequestPurChaseUpdate')
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .nopo-style .mt-form {
  padding: 1.5rem;
}
/deep/ .buttons-style button {
  border-radius: 5px;
  background-color: var(--plugin-ct-content-color);
}
</style>
