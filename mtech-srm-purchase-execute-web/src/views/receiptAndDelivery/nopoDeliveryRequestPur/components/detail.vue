<template>
  <div class="detail-top-info" style="height: 100%">
    <div class="header-box">
      <div class="middle-blank"></div>
      <span class="header-box-btn" v-waves type="info" @click="cancel">{{ $t('关闭') }}</span>
    </div>
    <div>
      <mt-form ref="addForm" :model="addForm" :rules="rules" :validate-on-rule-change="false">
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('申请编码：')" label-style="left">
              {{ addForm.applyCode }}
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="factoryCode" :label="$t('工厂代码')">
              <mt-input v-model="addForm.factoryCode" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="factoryName" :label="$t('工厂名称')">
              <mt-input v-model="addForm.factoryName" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="itemCode" :label="$t('物料编码')">
              <mt-input v-model="addForm.itemCode" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="itemName" :label="$t('物料描述')">
              <mt-input v-model="addForm.itemName" disabled />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="applyQty" :label="$t('申请数量')">
              <mt-input v-model="addForm.applyQty" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="unitName" :label="$t('单位')">
              <mt-input v-model="addForm.unitName" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="deliverDateCode" :label="$t('交货日期')">
              <mt-date-picker
                :placeholder="$t('选择交货日期')"
                v-model="addForm.deliverDateCode"
                format="yyyy-MM-dd"
                disabled
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="deliverAddrName" :label="$t('送货地址')">
              <mt-input v-model="addForm.deliverAddr" disabled />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="supplierCode" :label="$t('供应商代码')" label-style="top">
              <mt-input v-model="addForm.supplierCode" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="supplierName" :label="$t('供应商名称')">
              <mt-input v-model="addForm.supplierName" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="planner" :label="$t('计划员')">
              <mt-input v-model="addForm.planner" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="status" :label="$t('单据状态')">
              <mt-select
                v-model="addForm.status"
                :data-source="statusOptions"
                :fields="{ text: 'label', value: 'value' }"
                disabled
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="createUserName" :label="$t('创建人')">
              <mt-input v-model="addForm.createUserName" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="createTime" :label="$t('创建时间')">
              <mt-input v-model="addForm.createTime" disabled />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row>
          <mt-form-item prop="plannerRemark" :label="$t('计划员备注')" class="full-width">
            <mt-input v-model="addForm.plannerRemark" disabled />
          </mt-form-item>
        </mt-row>
        <mt-row>
          <mt-form-item prop="supplierRemark" :label="$t('供方备注')" class="full-width">
            <mt-input v-model="addForm.supplierRemark" disabled />
          </mt-form-item>
        </mt-row>
        <mt-row>
          <mt-form-item prop="approveOpinion" :label="$t('审批意见')" class="full-width">
            <mt-input v-model="addForm.approveOpinion" disabled></mt-input>
          </mt-form-item>
        </mt-row>
      </mt-form>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
export default {
  data() {
    return {
      addForm: {},
      rules: {
        factoryCode: [
          {
            required: true,
            message: this.$t('请选择工厂代码'),
            trigger: ['blur', 'change']
          }
        ],
        itemCode: [
          {
            required: true,
            message: this.$t('请选择物料编码'),
            trigger: ['blur', 'change']
          }
        ],
        applyQty: [
          {
            required: true,
            message: this.$t('请输入申请数量'),
            trigger: ['blur', 'change']
          }
        ],
        unitName: [
          {
            required: true,
            message: this.$t('由物料编码带出'),
            trigger: ['blur', 'change']
          }
        ],
        deliverDateCode: [
          {
            required: true,
            message: this.$t('请选择交货日期'),
            trigger: ['blur', 'change']
          }
        ],
        deliverAddrCode: [
          {
            required: true,
            message: this.$t('请选择送货地址'),
            trigger: ['blur', 'change']
          }
        ],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择供应商代码'),
            trigger: ['blur', 'change']
          }
        ],
        planner: [
          {
            required: true,
            message: this.$t('请输入计划员'),
            trigger: ['blur', 'change']
          }
        ]
      },
      statusOptions: [
        { label: this.$t('草稿'), value: 1 },
        { label: this.$t('审批中'), value: 2 },
        { label: this.$t('审批通过'), value: 3 },
        { label: this.$t('审批拒绝'), value: 4 },
        { label: this.$t('已退回'), value: 5 },
        { label: this.$t('已关闭'), value: 6 }
      ]
    }
  },
  activated() {
    this.$forceUpdate()
    this.init()
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      if (this.$route.query?.id) {
        this.addForm = { ...this.$route.query }
        let _date = this.$route.query.deliverDate
        if (_date && !isNaN(Number(_date))) {
          this.addForm.deliverDateCode = dayjs(Number(_date)).format('YYYY-MM-DD')
        }
      }
    },
    cancel() {
      for (let key in this.addForm) {
        this.addForm[key] = null
      }
      this.$router.push({
        name: 'nopo-delivery-request-purchase'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .nopo-style .mt-form {
  padding: 1.5rem;
}
/deep/ .buttons-style button {
  border-radius: 5px;
  background-color: var(--plugin-ct-content-color);
}
</style>
