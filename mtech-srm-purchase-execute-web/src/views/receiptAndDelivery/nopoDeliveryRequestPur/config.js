import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import Vue from 'vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
// import { judgeFormatCodeName } from '@/utils/utils'

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, isDateTime, isDate, isTime } = args

  const template = () => {
    return {
      template: Vue.component('timeDateComponent', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <div v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</div>
            <div v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</div>
          </div>
        </div>`,
        data: function () {
          return { data: {}, dataKey, isDateTime, isDate, isTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
export const statusList = [
  {
    label: i18n.t('草稿'),
    value: 1
  },
  {
    label: i18n.t('审批中'),
    value: 2
  },
  {
    label: i18n.t('审批通过'),
    value: 3
  },
  {
    label: i18n.t('审批拒绝'),
    value: 4
  },
  // {
  //   label: i18n.t('已退回'),
  //   value: 5
  // },
  {
    label: i18n.t('已关闭'),
    value: 6
  }
]

export const columnData = [
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  },
  {
    width: '200',
    field: 'applyCode',
    headerText: i18n.t('申请编号'),
    allowResizing: false,
    allowReordering: false,
    cellTools: []
    // template: function () {
    //   const template = {
    //     template: `<span style="color: #1890ff; cursor: pointer;" @click="toNoPoInfo">{{ data.applyCode }}</span>`,
    //     data() {
    //       return {
    //         data: {}
    //       }
    //     },
    //     methods: {
    //       toNoPoInfo() {
    //         const _flag = this.data.status && Number(this.data.status) > 1
    //         const _path = _flag
    //           ? 'detail-nopo-delivery-request-purchase'
    //           : 'create-nopo-delivery-request-purchase'
    //         this.$router.push({
    //           name: _path,
    //           query: {
    //             ...this.data,
    //             operationType: _flag ? 'detail' : 'edit'
    //           }
    //         })
    //       }
    //     }
    //   }
    //   return {
    //     template
    //   }
    // }
  },
  {
    width: '300',
    field: 'applyName',
    headerText: i18n.t('申请名称'),
    allowResizing: false,
    allowReordering: false
  },
  {
    width: '80',
    field: 'status',
    headerText: i18n.t('状态'),
    allowResizing: false,
    allowReordering: false,
    ValueConverter: {
      type: 'map',
      map: {
        1: i18n.t('草稿'),
        2: i18n.t('审批中'),
        3: i18n.t('审批通过'),
        4: i18n.t('审批拒绝'),
        5: i18n.t('已退回'),
        6: i18n.t('已关闭')
      }
    },
    template: () => {
      const _statusList = [
        {
          label: i18n.t('草稿'),
          value: 1
        },
        {
          label: i18n.t('审批中'),
          value: 2
        },
        {
          label: i18n.t('审批通过'),
          value: 3
        },
        {
          label: i18n.t('审批拒绝'),
          value: 4
        },
        {
          label: i18n.t('已退回'),
          value: 5
        },
        {
          label: i18n.t('已关闭'),
          value: 6
        }
      ]
      return {
        template: Vue.component('actionViews', {
          template: `<span>{{ data.status | transformValue }}</span>`,
          data() {
            return {
              data: {}
            }
          },
          filters: {
            transformValue(val) {
              return _statusList.filter((item) => item.value === val)[0]?.label ?? val
            }
          }
        })
      }
    }
  },
  {
    field: 'OANode',
    width: '140',
    headerText: i18n.t('OA当前审批节点'),
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <span v-if="data.oaUrl" style="cursor: pointer;color: #00469c;" @click="viewOA">查看</span>
              <span v-else style="cursor: not-allowed;color: #888;">查看</span>
            `,
          methods: {
            viewOA() {
              // this.$bus.$emit('entryApplicationViewOA', this.data)
              if (this.data.oaUrl) {
                window.open(this.data.oaUrl)
              }
            }
          }
        })
      }
    },
    editTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <span v-if="data.oaUrl" style="cursor: pointer;color: #00469c;" @click="viewOA">查看</span>
              <span v-else style="cursor: not-allowed;color: #888;">查看</span>
            `,
          methods: {
            viewOA() {
              // this.$bus.$emit('entryApplicationViewOA', this.data)
              if (this.data.oaUrl) {
                window.open(this.data.oaUrl)
              }
            }
          }
        })
      }
    }
  },
  {
    width: '120',
    field: 'factoryCode',
    headerText: i18n.t('工厂代码')
  },
  {
    width: '150',
    field: 'factoryName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '120',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '220',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '80',
    field: 'unitCode',
    headerText: i18n.t('单位'),
    template: function () {
      const template = {
        template: `<span>{{ data['unitCode'] + '-' + data['unitName'] }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        methods: {}
      }
      return {
        template
      }
    }
  },
  {
    width: '100',
    field: 'applyQty',
    headerText: i18n.t('申请数量')
  },
  {
    width: '100',
    field: 'deliveredQty',
    headerText: i18n.t('已发数量')
  },
  {
    width: '200',
    field: 'deliverAddr',
    headerText: i18n.t('送货地址')
  },
  {
    width: '200',
    field: 'saleOrderNo',
    headerText: i18n.t('销售订单号')
  },
  {
    width: '200',
    field: 'workCenterCode',
    headerText: i18n.t('工作中心'),
    template: function () {
      const template = {
        template: `<span>{{ data['workCenterCode'] ? data['workCenterCode'] + '-' + data['workCenterName'] : '' }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        methods: {}
      }
      return {
        template
      }
    }
  },
  {
    width: '150',
    field: 'deliverDate',
    headerText: i18n.t('送货日期'),
    template: timeDate({
      dataKey: 'deliverDate',
      isDateTime: true
    })
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '220',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '120',
    field: 'createUserName',
    headerText: i18n.t('创建人名称')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    width: '300',
    field: 'supplierRemark',
    headerText: i18n.t('供方备注')
  },
  {
    width: '120',
    field: 'planner',
    headerText: i18n.t('计划员')
  },
  {
    width: '120',
    field: 'plannerRemark',
    headerText: i18n.t('计划员备注')
  },
  {
    width: '120',
    field: 'approveOpinion',
    headerText: i18n.t('审批意见')
  }
]
