import { i18n } from '@/main.js'
import { timeNumberToDate } from '@/utils/utils'
import Vue from 'vue'
import debounce from 'lodash/debounce'

const columnData = () => {
  const column = [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false,
      allowEditing: false,
      allowResizing: false
    },
    {
      field: 'id',
      headerText: 'id',
      width: 0,
      visible: false,
      allowEditing: false,
      isPrimaryKey: true
    },
    {
      field: 'barcodeCode',
      headerText: i18n.t('条码ID'),
      width: '160',
      allowEditing: false
    },
    {
      field: 'printDate',
      headerText: i18n.t('打印时间'),
      width: '160',
      allowEditing: false
    },
    {
      field: 'associateCode',
      headerText: i18n.t('关联条码ID'),
      width: '200',
      selectOptions: [],
      template: () => {
        return {
          template: Vue.component('actionSelect', {
            template: `
              <mt-select
                v-model="data.associateCode"
                :filtering="doGetDataSource"
                :fields="{ text: 'text', value: 'value' }"
                :allowFiltering="true"
                :dataSource="options"
                :show-clear-button="true"
                @change="handleChange"
              />
            `,
            data: function () {
              return { options: [], data: {} }
            },

            mounted() {
              if (this.data.associateCode) {
                this.init(this.data.associateCode)
              } else {
                this.options = this.data.column.selectOptions.filter(
                  (item) => item !== this.data.barcodeCode
                )
              }
            },
            methods: {
              doGetDataSource: debounce(function (e) {
                this.init(e.text)
              }, 1000),
              init(e) {
                let params = {
                  condition: 'and',
                  page: {
                    current: 1,
                    size: 50
                  },
                  defaultRules: [
                    {
                      field: 'barcodeCode',
                      operator: 'contains',
                      value: e || ''
                    }
                  ]
                }
                this.$API.receiptAndDelivery.barcodePrintRecordtListCode(params).then((res) => {
                  if (res.code === 200) {
                    this.options = res.data.records
                      .filter((item) => item != this.data.barcodeCode)
                      .map((item) => {
                        return { text: item, value: item }
                      })
                  }
                })
              },
              handleChange(e) {
                console.log(e)
                this.data.associateCode = e.value

                if (e.e !== null) {
                  this.$parent.$emit('cellEdit', {
                    ...this.data
                  })
                }
              }
            }
          })
        }
      },
      clipMode: 'Ellipsis'
    },
    {
      field: 'barcodeLevel',
      headerText: i18n.t('条码层级'),
      width: '100',
      valueConverter: {
        type: 'map',
        map: {
          1: i18n.t('外箱'),
          2: i18n.t('内箱')
        }
      },
      allowEditing: false
    },
    {
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      width: '100',
      allowEditing: false
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      width: '200',
      allowEditing: false
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: '120',
      allowEditing: false
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: '200',
      allowEditing: false
    },
    {
      field: 'supplierEnName',
      headerText: i18n.t('供应商（英文）'),
      width: '200',
      allowEditing: false
    },
    {
      field: 'deliveryCode',
      headerText: i18n.t('送货单号'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'deliveryLineNo',
      headerText: i18n.t('送货单行号'),
      width: '100',
      allowEditing: false
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      width: '300',
      allowEditing: false
    },
    {
      field: 'itemEnName',
      headerText: i18n.t('物料名称（英文）'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'buyerOrgName',
      headerText: i18n.t('采购组'),
      width: '200',
      allowEditing: false
    },
    {
      field: 'deliveryTotalQuantity',
      headerText: i18n.t('送货总数量'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'noPrintQuantity',
      headerText: i18n.t('未打印数量'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'printQuantity',
      headerText: i18n.t('本次打印数量'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'packingQuantity',
      headerText: i18n.t('装箱数量'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'lastQuantity',
      headerText: i18n.t('尾数'),
      width: '100',
      allowEditing: false
    },
    {
      field: 'bin',
      headerText: 'BIN',
      width: '200',
      allowEditing: false
    },
    {
      field: 'deliveryDate',
      headerText: i18n.t('送货日期'),
      width: '100',
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e && !isNaN(e) && e.length == 13) {
            e = Number(e)
            return timeNumberToDate({
              formatString: 'YYYY-mm-dd',
              value: e
            })
          } else {
            return ''
          }
        }
      },
      allowEditing: false
    },
    {
      field: 'produceDate',
      headerText: i18n.t('生产日期'),
      width: '100',
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e && !isNaN(e) && e.length == 13) {
            e = Number(e)
            return timeNumberToDate({
              formatString: 'YYYY-mm-dd',
              value: e
            })
          } else {
            return ''
          }
        }
      },
      allowEditing: false
    },
    {
      field: 'netWeight',
      headerText: i18n.t('净重（KG）'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'grossWeight',
      headerText: i18n.t('毛重（KG）'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'length',
      headerText: i18n.t('长（CM）'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'width',
      headerText: i18n.t('宽（CM）'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'height',
      headerText: i18n.t('高（CM）'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'lastGrossWeight',
      headerText: i18n.t('尾箱毛重（KG）'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'lastNetWeight',
      headerText: i18n.t('尾箱净重（KG）'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'lineBody',
      headerText: i18n.t('线体'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'supplierBatchCode',
      headerText: i18n.t('供方批次'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'produceBatchCode',
      headerText: i18n.t('生产批次'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'supplierItemCode',
      headerText: i18n.t('供应商物料编码'),
      width: '200',
      allowEditing: false
    },
    {
      field: 'supplierItemName',
      headerText: i18n.t('供应商物料名称'),
      width: '200',
      allowEditing: false
    },
    {
      field: 'supplierLineBody',
      headerText: i18n.t('供应商线体'),
      width: '150',
      allowEditing: false
    },
    {
      field: 'remark',
      headerText: i18n.t('备注'),
      width: '150',
      allowEditing: false
    }
  ]
  return column
}

export const componentConfig = (that) => {
  const config = [
    {
      isUseCustomSearch: true, // 是否使用自定义查询
      isCustomSearchRules: true,
      useToolTemplate: false, // 此项不使用预置的表格操作按钮'新增、编辑、删除'
      useBaseConfig: false, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
      toolbar: {
        tools: [
          [
            {
              id: 'Submit',
              icon: 'icon_table_accept1',
              permission: ['O_02_1359'],
              title: i18n.t('提交')
            },
            {
              id: 'Import',
              icon: 'icon_solid_Import',
              permission: ['O_02_1673'],
              title: i18n.t('导入')
            }
          ],
          []
        ]
      },
      grid: {
        allowPaging: true, // 是否使用内置分页器
        allowEditing: true, //开启表格编辑操作
        // virtualPageSize: 20, // 虚拟滚动，暂不开启
        // selectionSettings: {
        //   persistSelection: true,
        //   type: 'Multiple',
        //   checkboxOnly: true
        // },
        // enableVirtualization: true,
        columnData: columnData(that),
        asyncConfig: {
          url: '/srm-purchase-execute/tenant/barcodePrintRecord/query-tv'
        }
      }
    }
  ]
  return config
}

const columnData2 = (that) => {
  const column = [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false,
      allowEditing: false,
      allowResizing: false
    },
    {
      field: 'id',
      headerText: 'id',
      width: 0,
      visible: false,
      allowEditing: false,
      isPrimaryKey: true
    },
    {
      field: 'barcodeCode',
      headerText: i18n.t('条码ID'),
      width: '160',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headerTemplate', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('条码ID') }}</span>
                </div>
              `
          })
        }
      }
    },
    {
      field: 'associateCode',
      headerText: i18n.t('关联条码ID'),
      width: '200',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      }
    },
    {
      field: 'companyCode',
      headerText: i18n.t('公司编码'),
      width: '100',
      headerTemplate: () => {
        return {
          template: Vue.component('headerTemplate', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('公司编码') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        if (scoped.companyCode) {
          that.getSite(scoped, scoped.companyCode)
        }
        return (
          <div>
            <mt-select
              v-model={scoped.companyCode}
              fields={{ text: 'text', value: 'value' }}
              dataSource={that.companyOptions}
              popup-width={250}
              allow-filtering={true}
              filter-type='Contains'
              placeholder={i18n.t('请选择公司')}
              onChange={(e) => {
                const { companyName } = e.itemData
                scoped.companyName = companyName
                scoped.siteCode = ''
                scoped.siteName = ''
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'companyName',
      headerText: i18n.t('公司名称'),
      width: '200',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      }
    },
    {
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      width: '100',
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('工厂编码') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        if (scoped.siteCode) {
          that.getItemOptions(scoped, {
            text: scoped.itemCode
          })
        }
        return (
          <div>
            <mt-select
              v-model={scoped.siteCode}
              fields={{ text: 'text', value: 'value' }}
              dataSource={scoped.siteOptions || []}
              allow-filtering={true}
              filter-type='Contains'
              popup-width={250}
              placeholder={i18n.t('请选择工厂')}
              onChange={(e) => {
                const { siteName } = e.itemData
                scoped.siteName = siteName
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      width: '200',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      }
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: '120',
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('供应商编码') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        that.getSupplier(scoped)
        return (
          <div>
            <mt-input v-model={scoped.supplierCode} disabled />
          </div>
        )
      }
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: '200',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      }
    },
    {
      field: 'quantity',
      headerText: i18n.t('数量'),
      width: '150',
      headerTemplate: () => {
        return {
          template: Vue.component('headerTemplate', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('数量') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        scoped.quantity = scoped.quantity ?? 0
        return (
          <div>
            <mt-input-number v-model={scoped.quantity} min={0} placeholder={i18n.t('请输入')} />
          </div>
        )
      }
    },
    {
      field: 'deliveryCode',
      headerText: i18n.t('送货单号'),
      width: '150',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headerTemplate', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('送货单号') }}</span>
                </div>
              `
          })
        }
      }
    },
    {
      field: 'deliveryLineNo',
      headerText: i18n.t('送货单行号'),
      width: '150',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      }
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      width: '150',
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('物料编码') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        return (
          <div>
            <mt-select
              v-model={scoped.itemCode}
              fields={{ text: 'text', value: 'value' }}
              dataSource={scoped.itemOptions}
              popup-width={300}
              allow-filtering={true}
              filter-type='Contains'
              filtering={(e) => that.itemCodeFiltering(scoped, e)}
              placeholder={i18n.t('请选择物料编码')}
              onChange={(e) => {
                const { itemId, itemName } = e.itemData
                scoped.itemId = itemId
                scoped.itemName = itemName
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      width: '300',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      }
    },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人'),
      width: '100',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      }
    },
    {
      field: 'createTime',
      headerText: i18n.t('创建时间'),
      width: '150',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      }
    },
    {
      field: 'syncStatus',
      headerText: i18n.t('同步状态'),
      width: '200',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      },
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('未同步'),
          1: i18n.t('已同步'),
          2: i18n.t('同步失败')
        }
      }
    }
  ]
  return column
}

export const componentConfig2 = (that) => {
  const config = [
    {
      isUseCustomSearch: true, // 是否使用自定义查询
      isCustomSearchRules: true,
      isUseCustomEditor: true,
      useToolTemplate: false, // 此项不使用预置的表格操作按钮'新增、编辑、删除'
      useBaseConfig: false, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
      toolbar: {
        tools: [
          [
            {
              id: 'Submit',
              icon: 'icon_table_accept1',
              title: i18n.t('提交')
            },
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              title: i18n.t('新增')
            },
            {
              id: 'Delete',
              icon: 'icon_solid_Delete',
              title: i18n.t('删除')
            },
            {
              id: 'Import',
              icon: 'icon_solid_Import',
              title: i18n.t('导入')
            },
            {
              id: 'export',
              icon: 'icon_solid_Import',
              permission: ['O_02_1355'],
              title: i18n.t('导出')
            }
          ],
          ['Refresh']
        ]
      },
      grid: {
        allowPaging: true, // 是否使用内置分页器
        allowEditing: true, //开启表格编辑操作
        // virtualPageSize: 20, // 虚拟滚动，暂不开启
        // selectionSettings: {
        //   persistSelection: true,
        //   type: 'Multiple',
        //   checkboxOnly: true
        // },
        // enableVirtualization: true,
        editSettings: {
          allowEditing: true,
          allowAdding: true,
          allowDeleting: true,
          mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
          showConfirmDialog: false,
          showDeleteConfirmDialog: false,
          newRowPosition: 'Top'
        },
        columnData: columnData2(that),
        asyncConfig: {
          url: '/srm-purchase-execute/tenant/barcodePrintExternal/query-tv'
        }
      }
    }
  ]
  return config
}
