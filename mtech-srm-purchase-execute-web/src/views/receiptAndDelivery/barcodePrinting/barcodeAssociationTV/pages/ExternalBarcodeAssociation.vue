<!-- 外部条码关联tab -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
          <mt-form-item prop="barcodeCode" :label="$t('条码ID')">
            <mt-input
              v-model="searchFormModel.barcodeCode"
              :show-clear-button="true"
              :placeholder="$t('请输入条码ID')"
            />
          </mt-form-item>
          <mt-form-item prop="companyCode" :label="$t('公司编码')">
            <mt-input
              v-model="searchFormModel.companyCode"
              :show-clear-button="true"
              :placeholder="$t('请输入公司编码')"
            />
          </mt-form-item>
          <mt-form-item prop="companyName" :label="$t('公司名称')">
            <mt-input
              v-model="searchFormModel.companyName"
              :show-clear-button="true"
              :placeholder="$t('请输入公司名称')"
            />
          </mt-form-item>
          <mt-form-item prop="siteCode" :label="$t('工厂编码')">
            <mt-input
              v-model="searchFormModel.siteCode"
              :show-clear-button="true"
              :placeholder="$t('请输入工厂编码')"
            />
          </mt-form-item>
          <mt-form-item prop="siteName" :label="$t('工厂名称')">
            <mt-input
              v-model="searchFormModel.siteName"
              :show-clear-button="true"
              :placeholder="$t('请输入工厂名称')"
            />
          </mt-form-item>
          <mt-form-item prop="itemCode" :label="$t('物料编码')">
            <mt-input
              v-model="searchFormModel.itemCode"
              :show-clear-button="true"
              :placeholder="$t('请输入物料编码')"
            />
          </mt-form-item>
          <mt-form-item prop="itemName" :label="$t('物料名称')">
            <mt-input
              v-model="searchFormModel.itemName"
              :show-clear-button="true"
              :placeholder="$t('请输入物料名称')"
            />
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { componentConfig2 } from '../config/index'
import { getHeadersFileName, download } from '@/utils/utils'
import debounce from 'lodash/debounce'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      componentConfig: componentConfig2(this),
      searchFormModel: {},
      searchFormRules: {},
      companyOptions: [],
      supplierEnName: ''
    }
  },
  created() {
    this.getCompany()
    this.getSupplierEn()
  },
  methods: {
    dateChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'F'] = dayjs(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        ).valueOf()
        this.searchFormModel[flag + 'T'] = dayjs(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        ).valueOf()
      } else {
        this.searchFormModel[flag + 'F'] = null
        this.searchFormModel[flag + 'T'] = null
      }
    },
    getCompany() {
      this.$API.masterData
        .getCompanyByTenantId()
        .then((res) => {
          if (res.code === 200) {
            this.companyOptions = res.data.map((item) => {
              return {
                text: item.organizationCode + '-' + item.organizationName,
                value: item.organizationCode,
                companyName: item.organizationName
              }
            })
          }
        })
        .catch(() => {})
    },
    getSite(scoped, val) {
      let params = {
        paramObj: val
      }
      this.$API.masterData.getSiteListSupplier(params).then((res) => {
        if (res.code === 200) {
          const options = res.data.map((item) => {
            return {
              text: item.siteCode + '-' + item.siteName,
              value: item.siteCode,
              siteName: item.siteName
            }
          })
          this.$set(scoped, 'siteOptions', options)
        }
      })
    },
    getSupplier(scoped) {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      this.$set(scoped, 'supplierCode', userInfo.accountName)
      this.$set(scoped, 'supplierName', userInfo.tenantName)
      this.$set(scoped, 'supplierEnName', this.supplierEnName)
    },
    // 获取供应商英文名称
    getSupplierEn() {
      this.$API.masterData.getEnterpriseInfo().then((res) => {
        this.supplierEnName = res.data.enterpriseEnglishName
      })
    },
    itemCodeFiltering: debounce(function (scoped, e) {
      this.getItemOptions(scoped, e)
    }, 500),
    getItemOptions(scoped, e) {
      const { siteCode } = scoped
      const { text, updateData, setSelectData } = e
      let params = {
        siteCode: siteCode,
        fuzzyNameOrCode: {
          fuzzyNameOrCode: text
        }
      }
      this.$API.masterData.getItemCodeApi(params).then((res) => {
        if (res.code === 200) {
          const options = res.data?.map((item) => {
            return {
              text: item.itemCode + '-' + item.itemName,
              value: item.itemCode,
              itemId: item.itemId,
              itemName: item.itemName,
              buyerOrgCode: item.purchaseGroupCode,
              buyerOrgName: item.purchaseGroupName
            }
          })
          this.$set(scoped, 'itemOptions', options)
          if (updateData) {
            this.$nextTick(() => {
              updateData(scoped.itemOptions)
            })
          }
          if (setSelectData) {
            this.$nextTick(() => {
              setSelectData()
            })
          }
        }
      })
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selectedRecords = grid.getSelectedRecords()
      let _id = selectedRecords.map((item) => {
        return item.id
      })
      if (['Submit'].includes(toolbar.id)) {
        if (selectedRecords.length === 0) {
          this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
          return
        }
      }
      switch (toolbar.id) {
        case 'Submit':
          this.handleSubmit(_id)
          break
        case 'Add':
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
          break
        case 'Delete':
          this.handleDelete(_id)
          break
        case 'Import':
          this.handleImport()
          break
        case 'export':
          this.handleExport(selectedRecords)
          break
      }
    },
    handleSubmit(ids) {
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery
        .barcodePrintExternalSync(ids)
        .then(() => {
          this.$toast({
            content: this.$t('提交成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的外部条码关联吗?')
        },
        success: () => {
          this.$API.receiptAndDelivery.barcodePrintExternalDelete(ids).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    handleImport() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "@/components/uploadDialog/index.vue" */ '@/components/uploadDialog/index.vue'
          ),
        data: {
          title: this.$t('导入'),
          paramsKey: 'excel',
          importApi: this.$API.receiptAndDelivery.barcodePrintExternalImport,
          downloadTemplateApi: this.$API.receiptAndDelivery.buyerGoodsDemandPlanInfoKtExport
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    handleExport(_list) {
      this.$store.commit('startLoading')
      const asyncParams = this.$refs.templateRef.getAsyncParams()
      let params = {
        page: {
          current: asyncParams.page.current,
          size: asyncParams.page.size
        },
        ...this.searchFormModel,
        ids: _list && _list.length > 0 ? _list.map((item) => item.id) : []
      }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery
        .barcodePrintExternalExportTv(params)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    actionBegin(args) {
      const { requestType, data } = args
      if (requestType === 'save') {
        const validateMap = {
          barcodeCode: {
            value: data.barcodeCode,
            msg: this.$t('请输入条码ID')
          },
          companyCode: {
            value: data.companyCode,
            msg: this.$t('请选择公司')
          },
          siteCode: {
            value: data.siteCode,
            msg: this.$t('请选择工厂编码')
          },
          quantity: {
            value: data.quantity,
            msg: this.$t('数量不能为0')
          },
          deliveryCode: {
            value: data.deliveryCode,
            msg: this.$t('请输入送货单号')
          },
          itemCode: {
            value: data.itemCode,
            msg: this.$t('请选择物料编码')
          }
        }
        for (const key in validateMap) {
          if (Object.hasOwnProperty.call(validateMap, key)) {
            const element = validateMap[key]
            if (!element.value) {
              this.$toast({ content: element.msg, type: 'warning' })
              args.cancel = true
              break
            }
          }
        }
      }
    },
    actionComplete(args) {
      const { requestType, data, rowIndex } = args
      if (requestType === 'save') {
        this.handleSave(data, rowIndex)
      }
    },
    handleSave(rowData, rowIndex = 0) {
      const params = rowData
      this.$API.receiptAndDelivery
        .barcodePrintExternalSave(params)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('保存成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          }
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
        .catch(() => {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    }
  }
}
</script>
