<!-- 内部条码关联tab -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      @cellEdit="cellEdit"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
          <mt-form-item prop="barcodeCode" :label="$t('条码ID')">
            <mt-input
              v-model="searchFormModel.barcodeCode"
              :show-clear-button="true"
              :placeholder="$t('请输入条码ID')"
            />
          </mt-form-item>
          <mt-form-item prop="barcodeLevel" :label="$t('条码层级')">
            <mt-multi-select
              v-model="searchFormModel.barcodeLevel"
              type="multipleChoice"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              :allow-filtering="true"
              :data-source="barcodeLevelOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="siteCode" :label="$t('工厂编码')">
            <mt-input
              v-model="searchFormModel.siteCode"
              :show-clear-button="true"
              :placeholder="$t('请输入工厂编码')"
            />
          </mt-form-item>
          <mt-form-item prop="siteName" :label="$t('工厂名称')">
            <mt-input
              v-model="searchFormModel.siteName"
              :show-clear-button="true"
              :placeholder="$t('请输入工厂名称')"
            />
          </mt-form-item>
          <mt-form-item prop="itemCode" :label="$t('物料编码')">
            <mt-input
              v-model="searchFormModel.itemCode"
              :show-clear-button="true"
              :placeholder="$t('请输入物料编码')"
            />
          </mt-form-item>
          <mt-form-item prop="itemName" :label="$t('物料名称')">
            <mt-input
              v-model="searchFormModel.itemName"
              :show-clear-button="true"
              :placeholder="$t('请输入物料名称')"
            />
          </mt-form-item>
          <mt-form-item prop="buyerOrgName" :label="$t('采购组')">
            <mt-input
              v-model="searchFormModel.buyerOrgName"
              :show-clear-button="true"
              :placeholder="$t('请输入采购组')"
            />
          </mt-form-item>
          <mt-form-item prop="produceDate" :label="$t('生产日期')">
            <mt-date-range-picker
              v-model="searchFormModel.produceDate"
              :placeholder="$t('请选择生产日期')"
              @change="(e) => dateChange(e, 'produceDate')"
            />
          </mt-form-item>
          <mt-form-item prop="deliveryDate" :label="$t('送货日期')">
            <mt-date-range-picker
              v-model="searchFormModel.deliveryDate"
              :placeholder="$t('请选择送货日期')"
              @change="(e) => dateChange(e, 'deliveryDate')"
            />
          </mt-form-item>
          <mt-form-item prop="printDates" :label="$t('打印日期')">
            <mt-date-range-picker
              v-model="printDates"
              :placeholder="$t('请输入打印日期')"
              @change="(e) => dateChange(e, 'printDate')"
            />
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { componentConfig } from '../config/index'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      componentConfig: componentConfig(this),
      searchFormModel: {},
      searchFormRules: {},
      barcodeLevelOptions: [
        {
          text: this.$t('外箱'),
          value: 1
        },
        {
          text: this.$t('内箱'),
          value: 2
        }
      ]
    }
  },
  created() {
    this.getAssociateCodeOptions()
  },
  methods: {
    getAssociateCodeOptions() {
      let params = {
        condition: 'and',
        page: {
          current: 1,
          size: 50
        },
        defaultRules: [
          {
            field: 'barcodeCode',
            operator: 'contains',
            value: ''
          }
        ]
      }
      this.$API.receiptAndDelivery.barcodePrintRecordtListCode(params).then((res) => {
        if (res.code === 200) {
          this.componentConfig[0].grid.columnData[4].selectOptions = res.data.records.map(
            (item) => {
              return { text: item, value: item }
            }
          )
        }
      })
    },
    dateChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'F'] = dayjs(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        ).valueOf()
        this.searchFormModel[flag + 'T'] = dayjs(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        ).valueOf()
      } else {
        this.searchFormModel[flag + 'F'] = null
        this.searchFormModel[flag + 'T'] = null
      }
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selectedRecords = grid.getSelectedRecords()
      let _id = selectedRecords.map((item) => {
        return item.id
      })
      if (['Submit'].includes(toolbar.id)) {
        if (selectedRecords.length === 0) {
          this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
          return
        }
      }
      switch (toolbar.id) {
        case 'Submit':
          this.handleSubmit(_id)
          break
        case 'Import':
          this.handleImport()
          break
      }
    },
    handleSubmit(ids) {
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery
        .barcodePrintRecordSync(ids)
        .then(() => {
          this.$toast({
            content: this.$t('提交成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    handleImport() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "@/components/uploadDialog/index.vue" */ '@/components/uploadDialog/index.vue'
          ),
        data: {
          title: this.$t('导入'),
          paramsKey: 'excel',
          importApi: this.$API.receiptAndDelivery.barcodePrintRecordImport,
          downloadTemplateApi: this.$API.receiptAndDelivery.barcodePrintRecordTemplateDownload
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    cellEdit(e) {
      let params = {
        id: e.id,
        associateCode: e.associateCode
      }
      this.$API.receiptAndDelivery.barcodePrintRecordSave(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.template.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>
