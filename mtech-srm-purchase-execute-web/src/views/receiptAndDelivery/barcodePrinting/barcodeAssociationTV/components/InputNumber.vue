<template>
  <div>
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      style="display: none"
    ></mt-input>
    <mt-inputNumber
      v-model="data[data.column.field]"
      :min="0"
      :precision="3"
      :step="1"
      :show-clear-button="false"
      @input="numberChange"
    ></mt-inputNumber>
  </div>
</template>
<script>
var bigDecimal = require('js-big-decimal')
export default {
  data() {
    return {
      data: {}
    }
  },
  mounted() {
    this.deliveryTotalQuantit = this.data.deliveryTotalQuantity //送货总数量
    this.$bus.$on('deliveryTotalQuantityChange', (val) => {
      this.deliveryTotalQuantity = val
      if (this.data.printQuantity) {
        //如果已经有了数据
        let noPrintQuantity = bigDecimal.subtract(val, this.data.printQuantity)
        this.$bus.$emit('noPrintQuantityChange', noPrintQuantity)
        return
      }
      if (this.data.column.field === 'quantity') {
        this.data.quantity = val
      }
      this.data.printQuantity = val
      this.$bus.$emit('printQuantityChange', val)
      this.$bus.$emit('noPrintQuantityChange', 0)
    }) //接受的本次打印数量默认等于送货总数量
  },
  methods: {
    numberChange(val) {
      if (this.data.column.field === 'deliveryTotalQuantity') {
        this.$bus.$emit('deliveryTotalQuantityChange', val) //传给本次打印数量
        // this.$bus.$emit("deliveryTotalQuantityChange1", val); //传给尾数
      }
      if (this.data.column.field === 'printQuantity') {
        this.$bus.$emit('printQuantityChange', val)
        this.$bus.$emit('noPrintQuantityChange', val)
        // if (this.deliveryTotalQuantity) {
        //   this.$bus.$emit(
        //     "noPrintQuantityChange",
        //     bigDecimal.subtract(this.deliveryTotalQuantity, val)
        //   );
        // }
      }
      if (this.data.column.field === 'packingQuantity') {
        this.$bus.$emit('packingQuantityChange', val)
      }
      if (this.data.column.field === 'grossWeight') {
        this.$bus.$emit('grossWeightChange', val)
      }
      if (this.data.column.field === 'netWeight') {
        this.$bus.$emit('netWeightChange', val)
      }
    }
  }
}
</script>
