<template>
  <div>
    <debounce-filter-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="{ text: 'theCodeName', value: 'value' }"
      :placeholder="placeholder"
      :request="postChange"
      :popup-width="400"
      :value-template="valueTemplate"
      @change="selectChange"
      :open-dispatch-change="true"
      :disabled="isDisabled"
      :allow-filtering="true"
    ></debounce-filter-select>
  </div>
</template>
<script>
// import { utils } from "@mtech-common/utils";
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      valueTemplate: null,

      purchase: '', //采购组code
      isDisabled: false,
      siteId: '',
      customerEnterpriseId: '',
      siteCode: ''
    }
  },
  mounted() {
    this.dataSource = this.data.column.selectOptions
    if (this.data.column.field === 'companyCode') {
      this.getCompany()
    }
    if (this.data.column.field === 'barcodeLevel') {
      this.getBarcodeLevel()
    }
    if (this.data.column.field === 'itemCode') {
      //物料下拉

      this.$bus.$on('customerEnterpriseId', (val) => {
        this.customerEnterpriseId = val
      })
      this.$bus.$on('siteCode', (val) => {
        this.siteCode = val
        this.getCategoryItem('', val)
      })
    }
    if (this.data.column.field === 'siteCode') {
      this.valueTemplate = codeNameColumn({
        firstKey: 'siteOrgCode'
      })
      this.$bus.$on('companyCodeChange', (val) => {
        this.findOrgSiteInfo(val)
      })
    }
    if (this.data.column.field === 'supplierCode') {
      //供应商下拉
      this.$bus.$on('companyCodeChange', (val) => {
        this.companyCode = val
      })
      this.$bus.$on('siteCodeChange', (val) => {
        this.siteCode = val
        if (this.data.supplierCode) {
          this.getQuerySuppliers(this.data.supplierCode, val)
        } else {
          this.getQuerySuppliers('', val)
        }
      })
    }
    if (this.data.column.field === 'deliveryCode') {
      //送货单号
      this.$bus.$on('siteName', (val) => {
        this.getDeliveryCode(val)
      })
    }
  },
  methods: {
    getCompany() {
      this.$API.masterData.getComListSupplier().then((res) => {
        res.data.forEach((item) => {
          item.theCodeName = item.itemCode // 	客户名称
          item.value = item.itemCode // 	客户名称
        })
        this.dataSource = res.data || []
        // console.log(this.dataSource);
        // console.log(this.data);
      })
      // let obj = {
      //   fuzzyNameOrCode: "",
      // };
      // this.$API.masterData.getCustomer(obj).then((res) => {
      //   res.data.forEach((item) => {
      //     item.theCodeName = item.customerName; // 	客户名称
      //     item.value = item.customerName; // 	客户名称
      //   });
      //   this.dataSource = res.data || [];
      //   console.log(this.data);
      // });
    },
    getDeliveryCode(val) {
      //获取送货单信息
      let params = {
        page: {
          current: 1,
          size: 50
        },
        defaultRules: [
          {
            field: 'supplierName',
            operator: 'equal',
            value: this.data.supplierName
          },
          {
            field: 'siteName',
            operator: 'equal',
            value: val
          },
          {
            field: 'status',
            operator: 'notequal',
            value: 4
          },
          {
            field: 'status',
            operator: 'notequal',
            value: 5
          }
        ]
      }
      this.$API.receiptAndDelivery
        .supplierOrderDeliveryItemPage(params) //  供方
        .then((res) => {
          res.data.records.forEach((item) => {
            item.theCodeName = item.deliveryCode // 	客户名称
            item.value = item.deliveryCode // 	客户名称
          })

          this.dataSource = res.data?.records || []
          // this.fields = { text: "deliveryCode", value: "deliveryCode" };
        })
    },
    getBarcodeLevel() {
      this.dataSource = [
        { theCodeName: this.$t('外箱'), value: 1 },
        { theCodeName: this.$t('内箱'), value: 2 }
      ]
    },
    // 供应商
    getQuerySuppliers(e) {
      let obj = {
        defaultRules: [
          {
            field: 'companyCode',
            operator: 'equal',
            value: 'this.companyCode'
          },
          {
            field: 'siteCode',
            operator: 'equal',
            value: 'this.siteCode'
          },
          {
            field: 'codeLike',
            operator: 'equal',
            value: e
          }
        ]
      }
      this.$API.vmi.getQuerySuppliers(obj).then((res) => {
        const list = res?.data || []
        list.forEach((item) => {
          item.theCodeName = item.supplierCode // 供应商编码
          item.value = item.supplierCode // 供应商编码
        })
        this.dataSource = list || []
      })
    },
    // 模糊
    postChange(e) {
      if (this.data.column.field === 'itemCode') {
        this.getCategoryItem(e.text, this.siteCode)
      }
      if (this.data.column.field === 'supplierCode') {
        this.getQuerySuppliers(e.text, this.supplierCode)
      }
      // if (this.data.column.field === "siteName") {
      //   this.findOrgSiteInfo(e);
      // }
    },
    getSupplier() {
      //查询供应商的数据
      this.$API.masterData.getSupplier().then((res) => {
        res.data.forEach((item) => {
          item.theCodeName = item.supplierCode // 	客户名称
          item.value = item.supplierCode //  供应商编码
        })
        this.dataSource = res.data || []
      })
    },

    findOrgSiteInfo(val) {
      let obj = {
        paramObj: val
      }
      this.$API.masterData.getSiteListSupplier(obj).then((res) => {
        const list = res?.data || []
        list.forEach((item) => {
          item.siteOrgCode = item.siteCode
          item.siteOrgName = item.siteName
          item.value = item.siteCode
        })
        // list.forEach((item) => {
        //   item.value = item.siteOrgCode;
        //   // item.theCodeName = item.siteName;
        //   // item.value = item.siteName;
        // });
        this.dataSource = addCodeNameKeyInList({
          firstKey: 'siteOrgCode',
          list
        })
      })
    },
    getCategoryItem(val, code) {
      //物料下拉
      let obj = {
        siteCode: code,

        fuzzyNameOrCode: {
          fuzzyNameOrCode: val
        }
      }
      this.$API.masterData.getSiteItemFuzzyQuerySupplier(obj).then((res) => {
        const list = res?.data || []
        list.forEach((item) => {
          item.theCodeName = item.itemCode
          item.value = item.itemCode
        })
        this.dataSource = list
        // if (updateData) {
        //   this.$nextTick(() => {
        //     updateData(this.dataSource);
        //   });
        // }
      })
      // this.fields = { text: "itemCode", value: "itemCode" };
    },
    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      if (this.data.column.field === 'companyCode') {
        this.$bus.$emit('customerEnterpriseId', val.itemData.customerEnterpriseId)
        this.$bus.$emit('companyCodeChange', val.itemData.itemCode)
        this.$bus.$emit('companyNameChange', val.itemData.itemName)
        this.$parent.$emit('selectedChanged', {
          //传出额外数据
          fieldCode: 'companyName',
          itemInfo: {
            companyName: val.itemData.itemName,
            // companyId: val.itemData.companyOrgId,
            // purTenantId: val.itemData.customerTenantId,
            companyCode: val.itemData.itemCode
            // customerEnterpriseId: val.itemData.customerEnterpriseId,
            // supplierName: res.data.supplierName,
            // supplierCode: res.data.supplierCode,
          }
        })
      }
      if (this.data.column.field === 'itemCode') {
        //物料下拉
        this.$bus.$emit('buyerOrgNameChange', val.itemData.purchaseGroupName) //传给采购组

        this.$bus.$emit('itemNameChange', val.itemData.itemName) //传给物料名称
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'itemCode',
          itemInfo: {
            itemCode: val.itemData.itemCode,
            itemName: val.itemData.itemName,
            itemId: val.itemData.itemId
          }
        })
        // this.findBusinessGroupByCategoryId(val.itemData.itemCode);
      }
      if (this.data.column.field === 'supplierCode') {
        this.$bus.$emit('supplierNameChange', val.itemData.supplierName)

        this.$parent.$emit('selectedChanged', {
          fieldCode: 'deliveryLineNo',
          itemInfo: {
            supplierName: val.itemData.supplierName,
            supplierCode: val.itemData.supplierCode
          }
        })
      }
      if (this.data.column.field === 'siteCode') {
        console.log(val.itemData)
        this.$bus.$emit('siteId', val.itemData.id)
        this.$bus.$emit('siteCode', val.itemData.siteOrgCode)
        this.$bus.$emit('siteCodeChange', val.itemData.siteOrgCode)
        this.$bus.$emit('siteNameChange', val.itemData.siteOrgName)

        console.log(val.itemData)
        this.$parent.$emit('selectedChanged', {
          //传出额外数据工厂
          fieldCode: 'siteName',
          itemInfo: {
            siteName: val.itemData.siteOrgName,
            siteCode: val.itemData.siteOrgCode,
            siteId: val.itemData.siteOrgId
          }
        })
      }
    }
  }
}
</script>
