<!-- 原厂物料标识选择框 -->
<template>
  <mt-dialog
    ref="dialog"
    class="display-block"
    width="60%"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <div style="padding-top: 1rem">
      <mt-template-page
        ref="templateRef"
        :template-config="componentConfig"
        @handleCustomReset="handleCustomReset"
        @handleCustomSearch="handleCustomSearch"
      >
        <template v-slot:quick-search-form>
          <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
            <mt-form-item prop="supplierCode" :label="$t('供应商编码')">
              <mt-input v-model="searchFormModel.supplierCode" disabled />
            </mt-form-item>
            <mt-form-item prop="itemCode" :label="$t('物料编码')">
              <mt-input v-model="searchFormModel.itemCode" disabled />
            </mt-form-item>
            <mt-form-item prop="materialIdentification" :label="$t('原厂物料标识')">
              <mt-input
                v-model="searchFormModel.materialIdentification"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
            <mt-form-item prop="place" :label="$t('原产地')">
              <mt-input
                v-model="searchFormModel.place"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
            <mt-form-item prop="manufacturerNameCn" :label="$t('制造商中文名')">
              <mt-input
                v-model="searchFormModel.manufacturerNameCn"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
            <mt-form-item prop="manufacturerNameEn" :label="$t('制造商英文名')">
              <mt-input
                v-model="searchFormModel.manufacturerNameEn"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
              />
            </mt-form-item>
          </mt-form>
        </template>
      </mt-template-page>
    </div>
  </mt-dialog>
</template>

<script>
import { dialogColumn } from '../config/index'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      componentConfig: [
        {
          isUseCustomSearch: true, // 是否使用自定义查询
          isCustomSearchRules: true,
          isCustomSearchHandle: true,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false,
          toolbar: {
            tools: [[], []]
          },
          grid: {
            allowPaging: false, // 分页
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: dialogColumn(),
            dataSource: []
          }
        }
      ],
      searchFormModel: {},
      searchFormRules: {},

      callback: null
    }
  },
  methods: {
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          if (key !== 'supplierCode' && key !== 'itemCode') {
            this.searchFormModel[key] = null
          }
        }
      }
    },
    handleCustomSearch() {
      this.getTableData()
    },
    // 初始化
    dialogInit(args) {
      const { title, scoped, callback } = args
      this.searchFormModel.supplierCode = scoped.supplierCode
      this.searchFormModel.itemCode = scoped.itemCode
      this.dialogTitle = title // 弹框名称
      this.callback = callback
      this.refreshColumns()
      this.getTableData()
      this.$refs.dialog.ejsRef.show()
    },
    getTableData() {
      let params = {
        ...this.searchFormModel
      }
      this.$API.receiptAndDelivery.getMaterialIdentificationListApi(params).then((res) => {
        if (res.code === 200) {
          this.componentConfig[0].grid.dataSource = res.data
        }
      })
    },
    onOpen(args) {
      args.preventFocus = true
    },
    confirm() {
      const selectedRowData = this.$refs.templateRef
        .getCurrentUsefulRef()
        .gridRef.getMtechGridRecords()
      const params = selectedRowData[0]
      if (!params.place || !params.manufacturerNameEn) {
        this.$toast({
          content: this.$t('原产地或制造商英文名为空，请联系MC或采购员补齐该数据信息！'),
          type: 'warning'
        })
        return
      }
      this.callback(params)
      this.handleClose()
    },
    handleClose() {
      this.handleCustomReset()
      this.componentConfig[0].grid.dataSource = []
      this.$refs.dialog.ejsRef.hide()
    },
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>
