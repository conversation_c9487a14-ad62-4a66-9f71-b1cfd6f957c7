<!-- 供方-条码设置-泛智屏 -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
          <mt-form-item prop="siteCode" :label="$t('工厂编码')">
            <mt-input
              v-model="searchFormModel.siteCode"
              :show-clear-button="true"
              :placeholder="$t('请输入工厂编码')"
            />
          </mt-form-item>
          <mt-form-item prop="siteName" :label="$t('工厂名称')">
            <mt-input
              v-model="searchFormModel.siteName"
              :show-clear-button="true"
              :placeholder="$t('请输入工厂名称')"
            />
          </mt-form-item>
          <mt-form-item prop="itemCode" :label="$t('物料编码')">
            <mt-input
              v-model="searchFormModel.itemCode"
              :show-clear-button="true"
              :placeholder="$t('请输入物料编码')"
            />
          </mt-form-item>
          <mt-form-item prop="itemName" :label="$t('物料名称')">
            <mt-input
              v-model="searchFormModel.itemName"
              :show-clear-button="true"
              :placeholder="$t('请输入物料名称')"
            />
          </mt-form-item>
          <mt-form-item prop="supplierCode" :label="$t('供应商编码')">
            <mt-input
              v-model="searchFormModel.supplierCode"
              :show-clear-button="true"
              :placeholder="$t('请输入供应商编码')"
            />
          </mt-form-item>
          <mt-form-item prop="supplierName" :label="$t('供应商名称')">
            <mt-input
              v-model="searchFormModel.supplierName"
              :show-clear-button="true"
              :placeholder="$t('请输入供应商名称')"
            />
          </mt-form-item>
          <mt-form-item prop="supplierItemCode" :label="$t('供应商物料编码')">
            <mt-input
              v-model="searchFormModel.supplierItemCode"
              :show-clear-button="true"
              :placeholder="$t('请输入供应商物料编码')"
            />
          </mt-form-item>
          <mt-form-item prop="supplierItemName" :label="$t('供应商物料名称')">
            <mt-input
              v-model="searchFormModel.supplierItemName"
              :show-clear-button="true"
              :placeholder="$t('请输入供应商物料名称')"
            />
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>

    <MaterialIdentificationDialog ref="materialIdentificationRef" />
  </div>
</template>

<script>
import { componentConfig } from './config/index'
import debounce from 'lodash/debounce'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  components: {
    MaterialIdentificationDialog: () => import('./components/MaterialIdentificationDialog')
  },
  data() {
    return {
      componentConfig: componentConfig(this),
      searchFormModel: {},
      searchFormRules: {},
      barcodeLevelOptions: [
        {
          text: this.$t('外箱'),
          value: 1
        },
        {
          text: this.$t('内箱'),
          value: 2
        }
      ],
      companyOptions: [],
      siteOptions: [],
      supplierEnName: ''
    }
  },
  created() {
    this.getCompany()
    this.getSupplierEn()
  },
  methods: {
    handleShow(scoped) {
      if (!scoped.itemCode) {
        this.$toast({ content: this.$t('请选择物料编码'), type: 'warning' })
        return
      }
      this.$refs.materialIdentificationRef.dialogInit({
        title: this.$t('原厂物料标识'),
        scoped,
        callback: (row) => {
          this.$set(scoped, 'materialIdentification', row.materialIdentification)
          this.$set(scoped, 'place', row.place)
          this.$set(scoped, 'manufacturerNameCn', row.manufacturerNameCn)
          this.$set(scoped, 'manufacturerNameEn', row.manufacturerNameEn)
        }
      })
    },
    getCompany() {
      this.$API.masterData
        .getCompanyByTenantId()
        .then((res) => {
          if (res.code === 200) {
            this.companyOptions = res.data.map((item) => {
              return {
                text: item.organizationCode + '-' + item.organizationName,
                value: item.organizationCode,
                companyName: item.organizationName
              }
            })
          }
        })
        .catch(() => {})
    },
    companyCodeChange(e) {
      this.searchFormModel.siteCode = ''
      if (e) {
        this.getSite(null, e.itemData.value)
      }
    },
    getSite(scoped, val) {
      let params = {
        paramObj: val
      }
      this.$API.masterData.getSiteListSupplier(params).then((res) => {
        if (res.code === 200) {
          const options = res.data.map((item) => {
            return {
              text: item.siteCode + '-' + item.siteName,
              value: item.siteCode,
              siteName: item.siteName
            }
          })
          scoped ? this.$set(scoped, 'siteOptions', options) : (this.siteOptions = options)
        }
      })
    },
    getSupplier(scoped) {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      this.$set(scoped, 'supplierCode', userInfo.accountName)
      this.$set(scoped, 'supplierName', userInfo.tenantName)
      this.$set(scoped, 'supplierEnName', this.supplierEnName)
    },
    // 获取供应商英文名称
    getSupplierEn() {
      this.$API.masterData.getEnterpriseInfo().then((res) => {
        this.supplierEnName = res.data.enterpriseEnglishName
      })
    },
    itemCodeFiltering: debounce(function (scoped, e) {
      this.getItemOptions(scoped, e)
    }, 500),
    getItemOptions(scoped, e) {
      const { siteCode } = scoped
      const { text, updateData, setSelectData } = e
      let params = {
        siteCode: siteCode,
        fuzzyNameOrCode: {
          fuzzyNameOrCode: text
        }
      }
      this.$API.masterData.getItemCodeApi(params).then((res) => {
        if (res.code === 200) {
          const options = res.data?.map((item) => {
            return {
              text: item.itemCode + '-' + item.itemName,
              value: item.itemCode,
              itemId: item.itemId,
              itemName: item.itemName,
              buyerOrgCode: item.purchaseGroupCode,
              buyerOrgName: item.purchaseGroupName
            }
          })
          this.$set(scoped, 'itemOptions', options)
          if (updateData) {
            this.$nextTick(() => {
              updateData(scoped.itemOptions)
            })
          }
          if (setSelectData) {
            this.$nextTick(() => {
              setSelectData()
            })
          }
        }
      })
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selectedRecords = grid.getSelectedRecords()
      let ids = selectedRecords.map((item) => item.id)

      if (['Delete'].includes(toolbar.id)) {
        if (selectedRecords.length === 0) {
          this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
          return
        }
      }
      switch (toolbar.id) {
        case 'Add':
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
          break
        case 'CloseEdit':
          this.$refs.templateRef.refreshCurrentGridData()
          break
        case 'Delete':
          this.handleDelete(selectedRecords)
          break
        case 'export':
          this.handleExport(ids)
          break
        case 'Import':
          this.handleImport()
          break
      }
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    actionBegin(args) {
      const { requestType, data } = args
      if (requestType === 'save') {
        const validateMap = {
          barcodeLevel: {
            value: data.barcodeLevel,
            msg: this.$t('请选择条码层级')
          },
          companyCode: {
            value: data.companyCode,
            msg: this.$t('请选择公司')
          },
          siteCode: {
            value: data.siteCode,
            msg: this.$t('请选择工厂编码')
          },
          itemCode: {
            value: data.itemCode,
            msg: this.$t('请选择物料编号')
          },
          packingQuantity: {
            value: data.packingQuantity,
            msg: this.$t('装箱数量不能为0')
          }
        }
        for (const key in validateMap) {
          if (Object.hasOwnProperty.call(validateMap, key)) {
            const element = validateMap[key]
            if (!element.value) {
              this.$toast({ content: element.msg, type: 'warning' })
              args.cancel = true
              break
            }
          }
        }
      }
    },
    actionComplete(args) {
      const { requestType, data, rowIndex } = args
      if (requestType === 'save') {
        this.handleSave(data, rowIndex)
      }
    },
    handleSave(rowData, rowIndex = 0) {
      const params = rowData
      this.$API.receiptAndDelivery
        .barcodeSetupTVSave(params)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('保存成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          }
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
        .catch(() => {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    handleDelete(row) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据?')
        },
        success: () => {
          let ids = row.map((item) => item.id)
          this.$API.receiptAndDelivery.barcodePrintConfigDelete(ids).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    handleExport(ids) {
      const asyncParams = this.$refs.templateRef.getAsyncParams()
      let params = {
        page: {
          current: asyncParams.page.current,
          size: asyncParams.page.size
        },
        ...this.searchFormModel,
        ids: ids
      }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery.barcodePrintConfigExportTv(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleImport() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "@/components/uploadDialog/index.vue" */ '@/components/uploadDialog/index.vue'
          ),
        data: {
          title: this.$t('导入'),
          paramsKey: 'excel',
          importApi: this.$API.receiptAndDelivery.barcodePrintConfigImport,
          downloadTemplateApi: this.$API.receiptAndDelivery.barcodeExportTemplate
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 行编辑时包含按钮的项目
/deep/ .input-search-content {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .mt-icons {
    width: 20px;
    flex-shrink: 0;
    text-align: center;
    cursor: pointer;
  }
}
</style>
