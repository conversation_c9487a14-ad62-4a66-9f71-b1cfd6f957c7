import { i18n } from '@/main.js'
import Vue from 'vue'

const columnData = (that) => {
  const column = [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false,
      allowEditing: false,
      allowResizing: false
    },
    {
      field: 'id',
      headerText: 'id',
      width: 0,
      visible: false,
      allowEditing: false,
      isPrimaryKey: true
    },
    {
      field: 'barcodeLevel',
      headerText: i18n.t('条码层级'),
      width: '100',
      valueConverter: {
        type: 'map',
        map: {
          1: i18n.t('外箱'),
          2: i18n.t('内箱')
        }
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headerTemplate', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('条码层级') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        return (
          <div>
            <mt-select
              v-model={scoped.barcodeLevel}
              fields={{ text: 'text', value: 'value' }}
              dataSource={that.barcodeLevelOptions}
              allow-filtering={true}
              filter-type='Contains'
              placeholder={i18n.t('请选择条码层级')}
            />
          </div>
        )
      }
    },
    {
      field: 'companyCode',
      headerText: i18n.t('公司'),
      width: '250',
      headerTemplate: () => {
        return {
          template: Vue.component('headerTemplate', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('公司') }}</span>
                </div>
              `
          })
        }
      },
      template: () => {
        return {
          template: Vue.component('company', {
            template: `<div>{{data.companyCode}}-{{data.companyName}}</div>`,
            data: function () {
              return { data: {} }
            }
          })
        }
      },
      editorRender(h, scoped) {
        if (scoped.companyCode) {
          that.getSite(scoped, scoped.companyCode)
        }
        return (
          <div>
            <mt-select
              v-model={scoped.companyCode}
              fields={{ text: 'text', value: 'value' }}
              dataSource={that.companyOptions}
              allow-filtering={true}
              filter-type='Contains'
              placeholder={i18n.t('请选择公司')}
              onChange={(e) => {
                const { companyName } = e.itemData
                scoped.companyName = companyName
                scoped.siteCode = ''
                scoped.siteName = ''
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      width: '100',
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('工厂编码') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        if (scoped.siteCode) {
          that.getItemOptions(scoped, {
            text: scoped.itemCode
          })
        }
        return (
          <div>
            <mt-select
              v-model={scoped.siteCode}
              fields={{ text: 'text', value: 'value' }}
              dataSource={scoped.siteOptions || []}
              allow-filtering={true}
              filter-type='Contains'
              popup-width={250}
              placeholder={i18n.t('请选择工厂')}
              onChange={(e) => {
                const { siteName } = e.itemData
                scoped.siteName = siteName
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      width: '200',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('工厂名称') }}</span>
                </div>
              `
          })
        }
      }
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: '120',
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('供应商编码') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        that.getSupplier(scoped)
        return (
          <div>
            <mt-input v-model={scoped.supplierCode} disabled />
          </div>
        )
      }
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: '200',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      }
    },
    {
      field: 'supplierEnName',
      headerText: i18n.t('供应商（英文）'),
      width: '200',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      }
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      width: '150',
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('物料编码') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        return (
          <div>
            <mt-select
              v-model={scoped.itemCode}
              fields={{ text: 'text', value: 'value' }}
              dataSource={scoped.itemOptions}
              popup-width={300}
              allow-filtering={true}
              filter-type='Contains'
              filtering={(e) => that.itemCodeFiltering(scoped, e)}
              placeholder={scoped.siteCode ? i18n.t('请选择物料编码') : i18n.t('请先选择工厂编码')}
              disabled={!scoped.siteCode}
              onChange={(e) => {
                const { value, itemId, itemName, buyerOrgCode, buyerOrgName } = e.itemData
                if (scoped.itemCode !== value) {
                  scoped.materialIdentification = ''
                  scoped.place = ''
                  scoped.manufacturerNameCn = ''
                  scoped.manufacturerNameEn = ''
                }
                scoped.itemId = itemId
                scoped.itemName = itemName
                scoped.buyerOrgName = buyerOrgCode
                scoped.buyerOrgName = buyerOrgName
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      width: '300',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      }
    },
    {
      field: 'itemEnName',
      headerText: i18n.t('物料名称（英文）'),
      width: '150',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      }
    },
    {
      field: 'buyerOrgName',
      headerText: i18n.t('采购组'),
      width: '200',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      }
    },
    {
      field: 'materialIdentification',
      headerText: i18n.t('原厂物料标识'),
      width: '200',
      editorRender(h, scoped) {
        return (
          <div class='input-search-content'>
            <mt-input v-model={scoped.materialIdentification} readonly />
            <mt-icon
              name='icon_input_search'
              nativeOnClick={() => {
                that.handleShow(scoped)
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'supplierItemCode',
      headerText: i18n.t('供应商物料编码'),
      width: '150',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      }
    },
    {
      field: 'supplierItemName',
      headerText: i18n.t('供应商物料名称'),
      width: '150',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      }
    },
    {
      field: 'supplierLineBody',
      headerText: i18n.t('供应商线体'),
      width: '150',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      }
    },
    {
      field: 'packingQuantity',
      headerText: i18n.t('装箱数量'),
      width: '150',
      allowReordering: false,
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('装箱数量') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        scoped.packingQuantity = scoped.packingQuantity ?? 0
        return (
          <div>
            <mt-input-number
              v-model={scoped.packingQuantity}
              min={0}
              placeholder={i18n.t('请输入')}
              onInput={(val) => {
                if (val && val !== 0) {
                  scoped.lastQuantity = scoped.printQuantity % val
                } else {
                  scoped.lastQuantity = 0
                }
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'netWeight',
      headerText: i18n.t('净重（KG）'),
      width: '150',
      allowReordering: false,
      editorRender(h, scoped) {
        scoped.netWeight = scoped.netWeight ?? 0
        return (
          <div>
            <mt-input-number
              v-model={scoped.netWeight}
              min={0}
              precision={2}
              placeholder={i18n.t('请输入')}
            />
          </div>
        )
      }
    },
    {
      field: 'grossWeight',
      headerText: i18n.t('毛重（KG）'),
      width: '150',
      allowReordering: false,
      editorRender(h, scoped) {
        scoped.grossWeight = scoped.grossWeight ?? 0
        return (
          <div>
            <mt-input-number
              v-model={scoped.grossWeight}
              min={0}
              precision={2}
              placeholder={i18n.t('请输入')}
            />
          </div>
        )
      }
    },
    {
      field: 'length1',
      headerText: i18n.t('长（CM）'),
      width: '150',
      editorRender(h, scoped) {
        scoped.length1 = scoped.length1 ?? 0
        return (
          <div>
            <mt-input-number
              v-model={scoped.length1}
              min={0}
              precision={3}
              placeholder={i18n.t('请输入')}
            />
          </div>
        )
      }
    },
    {
      field: 'width',
      headerText: i18n.t('宽（CM）'),
      width: '150',
      editorRender(h, scoped) {
        scoped.width = scoped.width ?? 0
        return (
          <div>
            <mt-input-number
              v-model={scoped.width}
              min={0}
              precision={3}
              placeholder={i18n.t('请输入')}
            />
          </div>
        )
      }
    },
    {
      field: 'height',
      headerText: i18n.t('高（CM）'),
      width: '150',
      editorRender(h, scoped) {
        scoped.height = scoped.height ?? 0
        return (
          <div>
            <mt-input-number
              v-model={scoped.height}
              min={0}
              precision={3}
              placeholder={i18n.t('请输入')}
            />
          </div>
        )
      }
    },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人'),
      width: '100',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      }
    },
    {
      field: 'createTime',
      headerText: i18n.t('创建时间'),
      width: '150',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      }
    }
  ]
  return column
}

export const componentConfig = (that) => {
  const config = [
    {
      isUseCustomSearch: true, // 是否使用自定义查询
      isCustomSearchRules: true,
      isUseCustomEditor: true,
      useToolTemplate: false, // 此项不使用预置的表格操作按钮'新增、编辑、删除'
      useBaseConfig: false, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
      toolbar: {
        tools: [
          [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              permission: ['O_02_1348'],
              title: i18n.t('新增')
            },
            {
              id: 'CloseEdit',
              icon: 'icon_table_delete',
              title: i18n.t('取消编辑')
            },
            {
              id: 'Delete',
              icon: 'icon_solid_Delete',
              permission: ['O_02_1349'],
              title: i18n.t('删除')
            },
            {
              id: 'export',
              icon: 'icon_solid_Import',
              permission: ['O_02_1350'],
              title: i18n.t('导出')
            },
            {
              id: 'Import',
              icon: 'icon_solid_Import',
              permission: ['O_02_1667'],
              title: i18n.t('导入')
            }
          ],
          ['Setting']
        ]
      },
      gridId: '446c3891-8948-4651-b522-c473c7876660',
      grid: {
        allowPaging: true, // 是否使用内置分页器
        allowEditing: true, //开启表格编辑操作
        editSettings: {
          allowEditing: true,
          allowAdding: true,
          allowDeleting: true,
          mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
          showConfirmDialog: false,
          showDeleteConfirmDialog: false,
          newRowPosition: 'Top'
        },
        columnData: columnData(that),
        asyncConfig: {
          url: '/srm-purchase-execute/tenant/barcodePrintConfig/query-tv',
          params: {
            printStatus: 0
          }
        }
      }
    }
  ]
  return config
}

export const dialogColumn = () => {
  const column = [
    {
      field: 'materialIdentification',
      headerText: i18n.t('原厂物料标识')
    },
    {
      field: 'place',
      headerText: i18n.t('原产地')
    },
    {
      field: 'manufacturerNameCn',
      headerText: i18n.t('制造商中文名')
    },
    {
      field: 'manufacturerNameEn',
      headerText: i18n.t('制造商英文名')
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码')
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码')
    }
  ]
  return column
}
