<template>
  <div>
    <debounce-filter-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="{ text: 'theCodeName', value: 'value' }"
      :placeholder="placeholder"
      :request="postChange"
      :value-template="valueTemplate"
      @change="selectChange"
      :open-dispatch-change="true"
      :disabled="isDisabled"
      :allow-filtering="true"
    ></debounce-filter-select>
  </div>
</template>
<script>
// import { utils } from "@mtech-common/utils";
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      valueTemplate: '',

      purchase: '', //采购组code
      isDisabled: false,
      siteId: '',
      customerEnterpriseId: '',
      siteCode: ''
    }
  },
  mounted() {
    this.dataSource = this.data.column.selectOptions
    if (this.data.column.field === 'companyName') {
      this.getCompany()
    }
    if (this.data.column.field === 'barcodeLevel') {
      this.getBarcodeLevel()
    }
    if (this.data.column.field === 'itemCode') {
      //物料下拉

      this.$bus.$on('customerEnterpriseId', (val) => {
        this.customerEnterpriseId = val
      })
      this.$bus.$on('siteCode', (val) => {
        this.siteCode = val
        this.getCategoryItem({})
      })
    }
    if (this.data.column.field === 'siteName') {
      this.$bus.$on('customerEnterpriseId', (val) => {
        this.customerEnterpriseId = val
        this.findOrgSiteInfo('')
      })
      this.valueTemplate = codeNameColumn({
        firstKey: 'siteOrgCode',
        secondKey: 'siteOrgName'
      })
    }
    if (this.data.column.field === 'supplierName') {
      //供应商下拉
      this.getSupplier()
    }
    if (this.data.column.field === 'deliveryCode') {
      //送货单号
      this.$bus.$on('siteName', (val) => {
        this.getDeliveryCode(val)
      })
    }
  },
  methods: {
    getCompany() {
      let obj = {
        fuzzyNameOrCode: ''
      }
      this.$API.masterData.getCustomer(obj).then((res) => {
        res.data.forEach((item) => {
          item.theCodeName = item.customerName // 	客户名称
          item.value = item.customerName // 	客户名称
        })
        this.dataSource = res.data || []
        console.log(this.data)
      })
    },
    getDeliveryCode(val) {
      //获取送货单信息
      let params = {
        page: {
          current: 1,
          size: 50
        },
        defaultRules: [
          {
            field: 'supplierName',
            operator: 'equal',
            value: this.data.supplierName
          },
          {
            field: 'siteName',
            operator: 'equal',
            value: val
          },
          {
            field: 'status',
            operator: 'notequal',
            value: 4
          },
          {
            field: 'status',
            operator: 'notequal',
            value: 5
          }
        ]
      }
      this.$API.receiptAndDelivery
        .supplierOrderDeliveryItemPage(params) //  供方
        .then((res) => {
          res.data.records.forEach((item) => {
            item.theCodeName = item.deliveryCode // 	客户名称
            item.value = item.deliveryCode // 	客户名称
          })

          this.dataSource = res.data?.records || []
          // this.fields = { text: "deliveryCode", value: "deliveryCode" };
        })
    },
    getBarcodeLevel() {
      this.dataSource = [
        { theCodeName: this.$t('一级'), value: '0' },
        { theCodeName: this.$t('二级'), value: '1' },
        { theCodeName: this.$t('三级'), value: '2' },
        { theCodeName: this.$t('四级'), value: '3' }
      ]
    },
    // 模糊
    postChange(e) {
      if (this.data.column.field === 'itemCode') {
        this.getCategoryItem(e)
      }
      if (this.data.column.field === 'siteName') {
        this.findOrgSiteInfo(e)
      }
    },
    getSupplier() {
      //查询供应商的数据
      this.$API.masterData.getSupplier().then((res) => {
        res.data.forEach((item) => {
          item.theCodeName = item.supplierName // 	客户名称
          item.value = item.supplierName // 	客户名称
        })
        this.dataSource = res.data || []
      })
    },

    findOrgSiteInfo(args) {
      const { text, updateData } = args
      //工厂下拉
      let obj = {
        buyerEnterpriseId: this.customerEnterpriseId,
        fuzzyParam: text
      }
      this.$API.masterData.getSourceAvailableView(obj).then((res) => {
        const list = res?.data || []
        list.forEach((item) => {
          item.value = item.siteOrgName
        })
        this.dataSource = addCodeNameKeyInList({
          firstKey: 'siteOrgCode',
          secondKey: 'siteOrgName',
          list
        })
        if (updateData) {
          this.$nextTick(() => {
            updateData(this.dataSource)
          })
        }
      })
    },
    getCategoryItem(args) {
      const { text, updateData } = args
      //物料下拉
      let obj = {
        buyerEnterpriseId: this.customerEnterpriseId,
        siteCode: this.siteCode,
        fuzzyParam: text,
        requireItemDetail: true
      }
      this.$API.masterData.postSourceItemnVendorava(obj).then((res) => {
        const list = res?.data || []
        list.forEach((item) => {
          item.theCodeName = item.itemCode
          item.value = item.itemCode
        })
        this.dataSource = list
        if (updateData) {
          this.$nextTick(() => {
            updateData(this.dataSource)
          })
        }
      })
    },
    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      if (this.data.column.field === 'companyName') {
        this.$bus.$emit('customerEnterpriseId', val.itemData.customerEnterpriseId)
        this.$API.masterData
          .supplierFindInBuyingByCustomerCode({
            customerCode: val.itemData.customerCode
          })
          .then((res) => {
            this.$bus.$emit('supplierNameChange', res.data.supplierName)
            this.$parent.$emit('selectedChanged', {
              //传出额外数据
              fieldCode: 'companyName',
              itemInfo: {
                companyName: val.itemData.customerName,
                // companyId: val.itemData.companyOrgId,
                companyCode: val.itemData.customerCode,
                customerEnterpriseId: val.itemData.customerEnterpriseId,
                supplierName: res.data.supplierName,
                supplierCode: res.data.supplierCode
              }
            })
          })
      }
      if (this.data.column.field === 'itemCode') {
        //物料下拉
        this.$bus.$emit('buyerOrgNameChange', val.itemData.purchaseGroupName) //传给采购组

        this.$bus.$emit('itemNameChange', val.itemData.itemName) //传给物料名称
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'itemCode',
          itemInfo: {
            itemCode: val.itemData.itemCode,
            itemName: val.itemData.itemName,
            itemId: val.itemData.itemId
          }
        })
        // this.findBusinessGroupByCategoryId(val.itemData.itemCode);
      }
      if (this.data.column.field === 'siteName') {
        console.log(val.itemData)
        this.$bus.$emit('companyNameChange', val.itemData.companyOrgName) //传给物料名称
        this.$bus.$emit('siteName', val.itemData.siteOrgName)
        this.$bus.$emit('tenantId', val.itemData.tenantId)
        this.$bus.$emit('siteCode', val.itemData.siteOrgCode)
        this.$parent.$emit('selectedChanged', {
          //传出额外数据工厂
          fieldCode: 'siteName',
          itemInfo: {
            siteName: val.itemData.siteOrgName,
            siteCode: val.itemData.siteOrgCode,
            siteId: val.itemData.siteOrgId,
            companyName: val.itemData.companyOrgName,
            companyId: val.itemData.companyOrgId,
            companyCode: val.itemData.companyOrgCode
          }
        })
      }
    }
  }
}
</script>
