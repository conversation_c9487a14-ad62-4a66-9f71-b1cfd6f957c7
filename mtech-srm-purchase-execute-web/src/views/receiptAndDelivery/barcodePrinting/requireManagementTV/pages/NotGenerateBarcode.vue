<!-- 待生成条码 -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
          <!-- <mt-form-item prop="companyCode" :label="$t('公司')">
            <mt-select
              v-model="searchFormModel.companyCode"
              :placeholder="$t('请选择公司')"
              :show-clear-button="true"
              :allow-filtering="true"
              :data-source="companyOptions"
              :fields="{ text: 'text', value: 'value' }"
              @change="companyCodeChange"
            />
          </mt-form-item> -->
          <mt-form-item prop="createTime" :label="$t('创建时间')">
            <mt-date-range-picker
              v-model="searchFormModel.createTime"
              :placeholder="$t('请选择创建时间')"
              @change="(e) => produceDateChange(e, 'createTime')"
            />
          </mt-form-item>
          <mt-form-item prop="siteCode" :label="$t('工厂')">
            <mt-select
              v-model="searchFormModel.siteCode"
              :placeholder="$t('请选择工厂')"
              :show-clear-button="true"
              :allow-filtering="true"
              :data-source="siteOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="barcodeLevel" :label="$t('条码层级')">
            <mt-multi-select
              v-model="searchFormModel.barcodeLevel"
              type="multipleChoice"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              :allow-filtering="true"
              :data-source="barcodeLevelOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="deliveryCode" :label="$t('送货单号')">
            <mt-input
              v-model="searchFormModel.deliveryCode"
              :show-clear-button="true"
              :placeholder="$t('请输入送货单号')"
            />
          </mt-form-item>
          <mt-form-item prop="itemCode" :label="$t('物料编码')">
            <mt-input
              v-model="searchFormModel.itemCode"
              :show-clear-button="true"
              :placeholder="$t('请输入物料编码')"
            />
          </mt-form-item>
          <mt-form-item prop="itemName" :label="$t('物料名称')">
            <mt-input
              v-model="searchFormModel.itemName"
              :show-clear-button="true"
              :placeholder="$t('请输入物料名称')"
            />
          </mt-form-item>
          <mt-form-item prop="deliveryDate" :label="$t('送货日期')">
            <mt-date-range-picker
              v-model="searchFormModel.deliveryDate"
              :placeholder="$t('请选择送货日期')"
              @change="(e) => deliveryDateChange(e, 'deliveryDate')"
            />
          </mt-form-item>
          <mt-form-item prop="produceDate" :label="$t('生产日期')">
            <mt-date-range-picker
              v-model="searchFormModel.produceDate"
              :placeholder="$t('请选择生产日期')"
              @change="(e) => produceDateChange(e, 'produceDate')"
            />
          </mt-form-item>
          <mt-form-item prop="supplierItemCode" :label="$t('供应商物料编码')">
            <mt-input
              v-model="searchFormModel.supplierItemCode"
              :show-clear-button="true"
              :placeholder="$t('请输入供应商编码')"
            />
          </mt-form-item>
          <mt-form-item prop="supplierItemName" :label="$t('供应商物料名称')">
            <mt-input
              v-model="searchFormModel.supplierItemName"
              :show-clear-button="true"
              :placeholder="$t('请输入供应商物料名称')"
            />
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>

    <MaterialIdentificationDialog ref="materialIdentificationRef" />
  </div>
</template>

<script>
import Vue from 'vue'
import dayjs from 'dayjs'
import { uniqBy } from 'lodash'
import { getHeadersFileName, download } from '@/utils/utils'
import { componentConfig } from '../config/index'
import debounce from 'lodash/debounce'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  components: {
    MaterialIdentificationDialog: () => import('../components/MaterialIdentificationDialog')
  },
  data() {
    return {
      componentConfig: componentConfig(this),
      searchFormModel: {
        createTime: [
          new Date(dayjs().subtract(15, 'day').format('YYYY-MM-DD 00:00:00')),
          new Date(dayjs().format('YYYY-MM-DD 23:59:59'))
        ],
        createTimeF: new Date(dayjs().subtract(15, 'day').format('YYYY-MM-DD 00:00:00')).getTime(),
        createTimeT: new Date(dayjs().format('YYYY-MM-DD 23:59:59')).getTime()
      },
      searchFormRules: {},
      barcodeLevelOptions: [
        {
          text: this.$t('外箱'),
          value: 1
        },
        {
          text: this.$t('内箱'),
          value: 2
        }
      ],
      companyOptions: [],
      siteOptions: [],
      // supplierEnName: '',
      orderTypeOptions: [
        {
          text: this.$t('送货单'),
          value: 0
        }
      ],
      supplierOptions: [],
      rowData: null
    }
  },
  created() {
    // this.getCompany()
    this.getSiteOptions()
    // this.getSupplierEn()
    this.getSupplierList()
  },
  methods: {
    getHistoryData: debounce(function (scoped) {
      let params = {
        barcodeLevel: scoped.barcodeLevel,
        siteCode: scoped.siteCode,
        itemCode: scoped.itemCode
      }
      this.$API.receiptAndDelivery.getConfigBarcodeTVApi(params).then((res) => {
        if (res.code === 200) {
          const row = res.data
          if (row) {
            this.rowData = row
            this.$set(scoped, 'netWeight', row?.netWeight)
            this.$set(scoped, 'grossWeight', row?.grossWeight)
            this.$set(scoped, 'length1', row?.length)
            this.$set(scoped, 'width', row?.width)
            this.$set(scoped, 'height', row?.height)
            this.$set(scoped, 'materialIdentification', row?.materialIdentification)
            this.$set(scoped, 'place', row?.place)
            this.$set(scoped, 'manufacturerNameCn', row?.manufacturerNameCn)
            this.$set(scoped, 'manufacturerNameEn', row?.manufacturerNameEn)
            this.$set(scoped, 'itemEnName', row?.itemEnName)
            this.$set(scoped, 'buyerOrgCode', row?.buyerOrgCode)
            this.$set(scoped, 'buyerOrgName', row?.buyerOrgName)
          } else {
            this.rowData = null
          }
        }
      })
    }, 500),
    handleShow(scoped) {
      if (!scoped.itemCode) {
        this.$toast({ content: this.$t('请选择物料编码'), type: 'warning' })
        return
      }
      this.$refs.materialIdentificationRef.dialogInit({
        title: this.$t('原厂物料标识'),
        scoped,
        callback: (row) => {
          this.$set(scoped, 'materialIdentification', row.materialIdentification)
          this.$set(scoped, 'place', row.place)
          this.$set(scoped, 'manufacturerNameCn', row.manufacturerNameCn)
          this.$set(scoped, 'manufacturerNameEn', row.manufacturerNameEn)
        }
      })
    },
    deliveryDateChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'F'] = dayjs(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        ).valueOf()
        this.searchFormModel[flag + 'T'] = dayjs(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        ).valueOf()
      } else {
        this.searchFormModel[flag + 'F'] = null
        this.searchFormModel[flag + 'T'] = null
      }
    },
    produceDateChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'F'] = dayjs(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        ).valueOf()
        this.searchFormModel[flag + 'T'] = dayjs(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        ).valueOf()
      } else {
        this.searchFormModel[flag + 'F'] = null
        this.searchFormModel[flag + 'T'] = null
      }
    },
    getCompany() {
      this.$API.masterData
        .getCompanyByTenantId()
        .then((res) => {
          if (res.code === 200) {
            this.companyOptions = res.data.map((item) => {
              return {
                text: item.organizationCode + '-' + item.organizationName,
                value: item.organizationCode,
                companyName: item.organizationName
              }
            })
          }
        })
        .catch(() => {})
    },
    companyCodeChange(e) {
      this.searchFormModel.siteCode = ''
      if (e) {
        this.getSite(null, e.itemData.value)
      }
    },
    getSite(scoped, val) {
      let params = {
        paramObj: val
      }
      this.$API.masterData.getSiteListSupplier(params).then((res) => {
        if (res.code === 200) {
          const options = res.data.map((item) => {
            return {
              text: item.siteCode + '-' + item.siteName,
              value: item.siteCode,
              siteName: item.siteName
            }
          })
          scoped ? this.$set(scoped, 'siteOptions', options) : (this.siteOptions = options)
        }
      })
    },
    getSiteOptions() {
      // let params = {
      //   paramObj: '1503'
      // }
      // this.$API.masterData.getSiteListSupplier(params).then((res) => {
      //   if (res.code === 200) {
      //     const options = res.data.map((item) => {
      //       return {
      //         text: item.siteCode + '-' + item.siteName,
      //         value: item.siteCode,
      //         siteName: item.siteName
      //       }
      //     })
      //     this.siteOptions = options
      //   }
      // })
      const params = {
        fuzzyParam: '',
        dataLimit: 9999
      }
      this.$API.receiptAndDelivery
        .getFactoryInfo(params)
        .then((res) => {
          if (res.code === 200) {
            const options = res.data.map((item) => {
              return {
                text: item.siteCode + '-' + item.siteName,
                value: item.siteCode,
                siteName: item.siteName
              }
            })
            this.siteOptions = options
          }
        })
        .catch(() => {})
    },
    getSupplierList() {
      this.$API.receiptAndDelivery.getSupplierList().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.supplierOptions = data?.map((i) => {
            return {
              ...i,
              codeAndName: `${i.supplierCode} - ${i.supplierName}`
            }
          })
        }
      })
    },
    getSupplier(scoped) {
      // const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      // this.$set(scoped, 'supplierCode', userInfo.accountName)
      // this.$set(scoped, 'supplierName', userInfo.tenantName)
      // this.$set(scoped, 'supplierEnName', this.supplierEnName)
      const supplierOption = this.supplierOptions.filter(
        (i) => i.supplierCode === scoped.supplierCode
      )
      if (supplierOption.length) {
        this.$set(scoped, 'supplierCode', supplierOption[0]['supplierCode'])
        this.$set(scoped, 'supplierName', supplierOption[0]['supplierName'])
        this.$set(scoped, 'supplierEnName', supplierOption[0]['supplierNameEn'])
      } else {
        if (this.supplierOptions.length === 1 && !scoped.supplierCode) {
          this.$set(scoped, 'supplierCode', this.supplierOptions[0]['supplierCode'])
          this.$set(scoped, 'supplierName', this.supplierOptions[0]['supplierName'])
          this.$set(scoped, 'supplierEnName', this.supplierOptions[0]['supplierNameEn'])
        }
      }
    },
    // 获取供应商英文名称
    // getSupplierEn() {
    //   this.$API.masterData.getEnterpriseInfo().then((res) => {
    //     this.supplierEnName = res.data.enterpriseEnglishName
    //   })
    // },
    deliveryCodeFiltering: debounce(function (scoped, e) {
      this.getDeliveryCode(scoped, e)
    }, 500),
    getDeliveryCode(scoped, e) {
      const { siteCode } = scoped
      const { text, updateData, setSelectData } = e
      let params = {
        page: {
          current: 1,
          size: 50
        },
        siteCode: siteCode,
        deliveryCode: text
      }
      this.$API.receiptAndDelivery.getDeliveryCodeBySite(params).then((res) => {
        if (res.code === 200) {
          const arr = res.data.records.map((item) => {
            return {
              ...item,
              text: item.deliveryCode,
              value: item.deliveryCode,
              deliveryLineNo: item.deliveryLineNo,
              itemCode: item.itemCode,
              itemName: item.itemName,
              buyerOrgCode: item.buyerOrgCode,
              buyerOrgName: item.buyerOrgName,
              saleOrderNo: item.saleOrderNo
            }
          })
          this.$set(scoped, 'preDeliveryCodeOptions', arr)
          const options = uniqBy(arr, (item) => item.value)
          this.$set(scoped, 'deliveryCodeOptions', options)

          if (updateData) {
            this.$nextTick(() => {
              updateData(scoped.deliveryCodeOptions)
            })
          }
          if (setSelectData) {
            this.$nextTick(() => {
              setSelectData()
            })
          }
        }
      })
    },
    getDeliveryLineNo(scoped) {
      const { deliveryCode } = scoped
      const arr = scoped.preDeliveryCodeOptions
        ?.filter((item) => item.value === deliveryCode)
        .sort((a, b) => a.deliveryLineNo - b.deliveryLineNo)
      const options = arr?.map((item) => {
        return {
          ...item,
          text: item.deliveryLineNo,
          value: item.deliveryLineNo,
          itemCode: item.itemCode,
          itemName: item.itemName,
          buyerOrgCode: item.buyerOrgCode,
          buyerOrgName: item.buyerOrgName,
          saleOrderNo: item.saleOrderNo
        }
      })
      this.$set(scoped, 'deliveryLineNoOptions', options)
    },
    itemCodeFiltering: debounce(function (scoped, e) {
      this.getItemOptions(scoped, e)
    }, 500),
    getItemOptions(scoped, e) {
      const { siteCode } = scoped
      const { text, updateData, setSelectData } = e
      let params = {
        siteCode: siteCode,
        fuzzyNameOrCode: {
          fuzzyNameOrCode: text
        }
      }
      this.$API.masterData.getItemCodeApi(params).then((res) => {
        if (res.code === 200) {
          const options = res.data?.map((item) => {
            return {
              text: item.itemCode + '-' + item.itemName,
              value: item.itemCode,
              itemName: item.itemName,
              buyerOrgCode: item.purchaseGroupCode,
              buyerOrgName: item.purchaseGroupName
            }
          })
          this.$set(scoped, 'itemOptions', options)
          if (updateData) {
            this.$nextTick(() => {
              updateData(scoped.itemOptions)
            })
          }
          if (setSelectData) {
            this.$nextTick(() => {
              setSelectData()
            })
          }
        }
      })
    },
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      const selectedRecords = []
      gridRef.dataSource.forEach((item) => {
        if (item.customChecked) {
          selectedRecords.push(item)
        }
      })
      let _id = selectedRecords.map((item) => {
        return item.id
      })
      let _printQuantity = selectedRecords.map((item) => {
        return item.printQuantity
      })
      let obj = []
      if (['Delete', 'Confirm', 'PrintA4', 'ConfirmImte'].includes(toolbar.id)) {
        if (selectedRecords.length === 0) {
          this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
          return
        }
      }
      if (toolbar.id === 'Import') {
        this.handleImport()
      }
      if (toolbar.id === 'Confirm' || toolbar.id === 'PrintA4' || toolbar.id === 'ConfirmImte') {
        let currentTimestamp = new Date().getTime()
        let canPrint = selectedRecords.every((item) => {
          return new Date(item.produceDate).getTime() <= currentTimestamp
        })
        if (!canPrint) {
          this.$toast({
            content: this.$t('只能打印生产日期小于等于今天日期的单！'),
            type: 'warning'
          })
          return
        }
        _id.forEach((item, index) => {
          obj.push({ id: item, printQuantity: _printQuantity[index] })
        })
      }
      let ticketsPrint = selectedRecords.every((item) => item.barcodeLevel === 1)
      let labelPrint = selectedRecords.every((item) => item.barcodeLevel === 2)
      switch (toolbar.id) {
        case 'Add':
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
          break
        case 'CloseEdit':
          this.$refs.templateRef.refreshCurrentGridData()
          break
        case 'Delete':
          this.handleDelete(selectedRecords)
          break
        case 'Confirm':
          if (ticketsPrint) {
            this.$dialog({
              modal: () => import('../components/printDialog.vue'),
              data: {
                title: this.$t('现品票打印纸张选择')
              },
              success: (modelForm) => {
                this.handleConfirm(modelForm, obj)
              }
            })
          } else {
            this.$toast({
              content: this.$t('只能选择外箱进行现品票打印！'),
              type: 'warning'
            })
          }
          break
        case 'PrintA4':
          if (ticketsPrint) {
            this.handleConfirmA4(4, obj)
          } else {
            this.$toast({
              content: this.$t('只能选择外箱进行机芯厂A4纸打印！'),
              type: 'warning'
            })
          }
          break
        case 'ConfirmImte':
          if (labelPrint) {
            this.$dialog({
              modal: () => import('../components/materialPrintDialog.vue'),
              data: {
                title: this.$t('物料标签打印')
              },
              success: (modelForm) => {
                this.handleConfirmItem(obj, modelForm)
              }
            })
            // this.handleConfirmItem(obj)
          } else {
            this.$toast({
              content: this.$t('只能选择内箱进行物料标签打印！'),
              type: 'warning'
            })
          }
          break
        case 'export':
          this.handleExport(selectedRecords)
          break
      }
    },
    handleImport() {
      //导入
      // this.$refs.importDialog.init({
      //   title: this.$t('导入')
      // })
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.receiptAndDelivery.barcodeRequestImportTv,
          downloadTemplateApi: this.$API.receiptAndDelivery.barcodeRequestExportTv,
          paramsKey: 'excel'
          // asyncParams: {
          //   // requestJson: JSON.stringify(parameter),
          // },
        },
        success: () => {
          // 导入之后刷新列表
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    actionBegin(args) {
      const { requestType, data } = args
      if (requestType === 'save') {
        const validateData = (data, validateMap) => {
          const hasEmptyValue = validateMap.some((item) => !item.value)
          if (hasEmptyValue) {
            const emptyItem = validateMap.find((item) => !item.value)
            this.$toast({ content: emptyItem.msg, type: 'warning' })
            args.cancel = true
          }
        }
        const validateMap = [
          {
            key: 'barcodeLevel',
            value: data.barcodeLevel,
            msg: this.$t('请选择条码层级')
          },
          // {
          //   key: 'companyCode',
          //   value: data.companyCode,
          //   msg: this.$t('请选择公司')
          // },
          {
            key: 'siteCode',
            value: data.siteCode,
            msg: this.$t('请选择工厂编码')
          },
          {
            key: 'itemCode',
            value: data.itemCode,
            msg: this.$t('请选择物料编号')
          },
          {
            key: 'deliveryTotalQuantity',
            value: data.deliveryTotalQuantity,
            msg: this.$t('送货总数量不能为0')
          },
          {
            key: 'printQuantity',
            value: data.printQuantity,
            msg: this.$t('本次打印数量不能为0')
          },
          {
            key: 'packingQuantity',
            value: data.packingQuantity,
            msg: this.$t('装箱数量不能为0')
          }
        ]
        validateData.call(this, data, validateMap)
        if (data.deliveryCode && !data.deliveryLineNo) {
          this.$toast({ content: this.$t('请选择主单行号'), type: 'warning' })
          args.cancel = true
        }
        if (data.packingQuantity && data.barcodeLevel === 2 && !data.supplierBatchCode) {
          this.$toast({ content: this.$t('条码层级为内箱，供方批次必填'), type: 'warning' })
          args.cancel = true
        }
        if (data.packingQuantity && data.barcodeLevel === 1) {
          const validateMap2 = [
            {
              key: 'netWeight',
              value: data.netWeight,
              msg: this.$t('净重不能为0')
            },
            {
              key: 'grossWeight',
              value: data.grossWeight,
              msg: this.$t('毛重不能为0')
            },
            {
              key: 'length1',
              value: data.length1,
              msg: this.$t('长不能为0')
            },
            {
              key: 'width',
              value: data.width,
              msg: this.$t('宽不能为0')
            },
            {
              key: 'height',
              value: data.height,
              msg: this.$t('高不能为0')
            }
          ]
          validateData.call(this, data, validateMap2)
        }
      }
    },
    actionComplete(args) {
      const { requestType, data, rowIndex } = args
      if (requestType === 'save') {
        this.handleSave(data, rowIndex)
      }
    },
    handleSave(rowData, rowIndex = 0) {
      const params = rowData
      params.deliveryDate = new Date(params.deliveryDate).getTime()
      params.produceDate = new Date(params.produceDate).getTime()
      this.$API.receiptAndDelivery
        .barcodePrintRequestSaveTV(params)
        .then((res) => {
          if (res.code === 200) {
            let data = {
              ...params
            }
            data.id = this.rowData?.id || null
            this.$API.receiptAndDelivery.barcodeSetupTVSave(data)
            this.$toast({
              content: this.$t('保存成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          }
          // this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          // this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
        .catch(() => {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    handleDelete(row) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据?')
        },
        success: () => {
          let ids = row.map((item) => item.id)
          this.$API.receiptAndDelivery.barcodePrintRequestDelete(ids).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    handleConfirm(modelForm, row) {
      let params = {
        ...modelForm,
        body: row
      }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery
        .barcodePrintRequestcurrentBarcodePrintTv(params)
        .then((res) => {
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = function () {
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }
            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
          // window.open(this.pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
        .finally(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef.refreshCurrentGridData()
        })
    },
    handleConfirmA4(type, row) {
      let params = {
        templateType: type,
        body: row
      }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery
        .barcodePrintRequestcurrentBarcodePrintA4Tv(params)
        .then((res) => {
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = function () {
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }
            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
          // window.open(this.pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    handleConfirmItem(body, modelForm) {
      // this.$dialog({
      //   data: {
      //     title: this.$t('提示'),
      //     message: this.$t('确认打印选中的数据吗?')
      //   },
      //   success: () => {
      //   }
      // })
      let params = {
        ...modelForm,
        body
      }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery
        .barcodePrintRequestitemBarcodePrintTv(params)
        .then((res) => {
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = function () {
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }
            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
          // window.open(this.pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
        .finally(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef.refreshCurrentGridData()
        })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        this.pdfLoading = false
      }, 100)
    },
    handleExport(_list) {
      let _currentTabIndex = this.$refs.templateRef.getCurrentTabRef().tabIndex
      const asyncParams = this.$refs.templateRef.getAsyncParams()
      let params = {
        page: {
          current: asyncParams.page.current,
          size: asyncParams.page.size
        },
        ...this.searchFormModel,
        printStatus: _currentTabIndex,
        ids: _list && _list.length > 0 ? _list.map((item) => item.id) : []
      }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery.barcodePrintRequestExportTv(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 行编辑时包含按钮的项目
/deep/ .input-search-content {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .mt-icons {
    width: 20px;
    flex-shrink: 0;
    text-align: center;
    cursor: pointer;
  }
}
</style>
