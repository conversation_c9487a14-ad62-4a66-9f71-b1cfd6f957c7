<!-- 已生成条码 -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
          <!-- <mt-form-item prop="companyCode" :label="$t('公司')">
            <mt-select
              v-model="searchFormModel.companyCode"
              :placeholder="$t('请选择公司')"
              :show-clear-button="true"
              :allow-filtering="true"
              :data-source="companyOptions"
              :fields="{ text: 'text', value: 'value' }"
              @change="companyCodeChange"
            />
          </mt-form-item> -->
          <mt-form-item prop="createTime" :label="$t('创建时间')">
            <mt-date-range-picker
              v-model="searchFormModel.createTime"
              :placeholder="$t('请选择创建时间')"
              @change="(e) => produceDateChange(e, 'createTime')"
            />
          </mt-form-item>
          <mt-form-item prop="siteCode" :label="$t('工厂')">
            <mt-select
              v-model="searchFormModel.siteCode"
              :placeholder="$t('请选择工厂')"
              :show-clear-button="true"
              :allow-filtering="true"
              :data-source="siteOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="barcodeLevel" :label="$t('条码层级')">
            <mt-multi-select
              v-model="searchFormModel.barcodeLevel"
              type="multipleChoice"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              :allow-filtering="true"
              :data-source="barcodeLevelOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="deliveryCode" :label="$t('送货单号')">
            <mt-input
              v-model="searchFormModel.deliveryCode"
              :show-clear-button="true"
              :placeholder="$t('请输入送货单号')"
            />
          </mt-form-item>
          <mt-form-item prop="itemCode" :label="$t('物料编码')">
            <mt-input
              v-model="searchFormModel.itemCode"
              :show-clear-button="true"
              :placeholder="$t('请输入物料编码')"
            />
          </mt-form-item>
          <mt-form-item prop="itemName" :label="$t('物料名称')">
            <mt-input
              v-model="searchFormModel.itemName"
              :show-clear-button="true"
              :placeholder="$t('请输入物料名称')"
            />
          </mt-form-item>
          <mt-form-item prop="deliveryDate" :label="$t('送货日期')">
            <mt-date-range-picker
              v-model="searchFormModel.deliveryDate"
              :placeholder="$t('请选择送货日期')"
              @change="(e) => deliveryDateChange(e, 'deliveryDate')"
            />
          </mt-form-item>
          <mt-form-item prop="produceDate" :label="$t('生产日期')">
            <mt-date-range-picker
              v-model="searchFormModel.produceDate"
              :placeholder="$t('请选择生产日期')"
              @change="(e) => produceDateChange(e, 'produceDate')"
            />
          </mt-form-item>
          <mt-form-item prop="supplierItemCode" :label="$t('供应商物料编码')">
            <mt-input
              v-model="searchFormModel.supplierItemCode"
              :show-clear-button="true"
              :placeholder="$t('请输入供应商编码')"
            />
          </mt-form-item>
          <mt-form-item prop="supplierItemName" :label="$t('供应商物料名称')">
            <mt-input
              v-model="searchFormModel.supplierItemName"
              :show-clear-button="true"
              :placeholder="$t('请输入供应商物料名称')"
            />
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import Vue from 'vue'
import dayjs from 'dayjs'
import { getHeadersFileName, download } from '@/utils/utils'
import { componentConfig2 } from '../config/index'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      componentConfig: componentConfig2(this),
      searchFormModel: {
        createTime: [
          new Date(dayjs().subtract(15, 'day').format('YYYY-MM-DD 00:00:00')),
          new Date(dayjs().format('YYYY-MM-DD 23:59:59'))
        ],
        createTimeF: new Date(dayjs().subtract(15, 'day').format('YYYY-MM-DD 00:00:00')).getTime(),
        createTimeT: new Date(dayjs().format('YYYY-MM-DD 23:59:59')).getTime()
      },
      searchFormRules: {},
      barcodeLevelOptions: [
        {
          text: this.$t('外箱'),
          value: 1
        },
        {
          text: this.$t('内箱'),
          value: 2
        }
      ],
      companyOptions: [],
      siteOptions: [],
      supplierEnName: '',
      orderTypeOptions: [
        {
          text: this.$t('送货单'),
          value: 0
        }
      ]
    }
  },
  created() {
    this.getCompany()
  },
  methods: {
    deliveryDateChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'F'] = dayjs(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        ).valueOf()
        this.searchFormModel[flag + 'T'] = dayjs(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        ).valueOf()
      } else {
        this.searchFormModel[flag + 'F'] = null
        this.searchFormModel[flag + 'T'] = null
      }
    },
    produceDateChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'F'] = dayjs(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        ).valueOf()
        this.searchFormModel[flag + 'T'] = dayjs(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        ).valueOf()
      } else {
        this.searchFormModel[flag + 'F'] = null
        this.searchFormModel[flag + 'T'] = null
      }
    },
    getCompany() {
      this.$API.masterData
        .getCompanyByTenantId()
        .then((res) => {
          if (res.code === 200) {
            this.companyOptions = res.data.map((item) => {
              return {
                text: item.organizationCode + '-' + item.organizationName,
                value: item.organizationCode,
                companyName: item.organizationName
              }
            })
          }
        })
        .catch(() => {})
    },
    companyCodeChange(e) {
      this.searchFormModel.siteCode = ''
      if (e) {
        this.getSite(null, e.itemData.value)
      }
    },
    getSite(scoped, val) {
      let params = {
        paramObj: val
      }
      this.$API.masterData.getSiteListSupplier(params).then((res) => {
        if (res.code === 200) {
          const options = res.data.map((item) => {
            return {
              text: item.siteCode + '-' + item.siteName,
              value: item.siteCode,
              siteName: item.siteName
            }
          })
          scoped ? this.$set(scoped, 'siteOptions', options) : (this.siteOptions = options)
        }
      })
    },
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      const selectedRecords = []
      gridRef.dataSource.forEach((item) => {
        if (item.customChecked) {
          selectedRecords.push(item)
        }
      })
      let _id = selectedRecords.map((item) => {
        return item.id
      })
      let _printQuantity = selectedRecords.map((item) => {
        return item.printQuantity
      })
      let obj = []
      if (['Delete', 'Confirm', 'PrintA4', 'ConfirmImte', 'Synchronous'].includes(toolbar.id)) {
        if (selectedRecords.length === 0) {
          this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
          return
        }
      }
      if (toolbar.id === 'Confirm' || toolbar.id === 'PrintA4' || toolbar.id === 'ConfirmImte') {
        let currentTimestamp = new Date().getTime()
        let canPrint = selectedRecords.every((item) => {
          return new Date(item.produceDate).getTime() <= currentTimestamp
        })
        if (!canPrint) {
          this.$toast({
            content: this.$t('只能打印生产日期小于等于今天日期的单！'),
            type: 'warning'
          })
          return
        }
        _id.forEach((item, index) => {
          obj.push({ id: item, printQuantity: _printQuantity[index] })
        })
      }
      let ticketsPrint = selectedRecords.every((item) => item.barcodeLevel === 1)
      let labelPrint = selectedRecords.every((item) => item.barcodeLevel === 2)
      switch (toolbar.id) {
        case 'Delete':
          this.handleDelete(selectedRecords)
          break
        case 'Confirm':
          if (ticketsPrint) {
            this.$dialog({
              modal: () => import('../components/printDialog.vue'),
              data: {
                title: this.$t('现品票打印纸张选择')
              },
              success: (modelForm) => {
                this.handleConfirm(modelForm, obj)
              }
            })
          } else {
            this.$toast({
              content: this.$t('只能选择外箱进行现品票打印！'),
              type: 'warning'
            })
          }
          break
        case 'PrintA4':
          if (ticketsPrint) {
            this.handleConfirmA4(4, obj)
          } else {
            this.$toast({
              content: this.$t('只能选择外箱进行机芯厂A4纸打印！'),
              type: 'warning'
            })
          }
          break
        case 'ConfirmImte':
          if (labelPrint) {
            this.$dialog({
              modal: () => import('../components/materialPrintDialog.vue'),
              data: {
                title: this.$t('物料标签打印')
              },
              success: (modelForm) => {
                this.handleConfirmItem(obj, modelForm)
              }
            })
            // this.handleConfirmItem(obj)
          } else {
            this.$toast({
              content: this.$t('只能选择内箱进行物料标签打印！'),
              type: 'warning'
            })
          }
          break
        case 'export':
          this.handleExport(selectedRecords)
          break
        case 'Synchronous':
          this.synchronousWms(_id)
          break
      }
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    handleDelete(row) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据?')
        },
        success: () => {
          let ids = row.map((item) => item.id)
          this.$API.receiptAndDelivery.barcodePrintRequestDelete(ids).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    handleConfirm(modelForm, row) {
      let params = {
        ...modelForm,
        body: row
      }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery
        .barcodePrintRequestcurrentBarcodePrintTv(params)
        .then((res) => {
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = function () {
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }
            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
          // window.open(this.pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    handleConfirmA4(type, row) {
      let params = {
        templateType: type,
        body: row
      }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery
        .barcodePrintRequestcurrentBarcodePrintA4Tv(params)
        .then((res) => {
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = function () {
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }
            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
          // window.open(this.pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    handleConfirmItem(body, modelForm) {
      // this.$dialog({
      //   data: {
      //     title: this.$t('提示'),
      //     message: this.$t('确认打印选中的数据吗?')
      //   },
      //   success: () => {
      //     }
      // })
      let params = {
        ...modelForm,
        body
      }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery
        .barcodePrintRequestitemBarcodePrintTv(params)
        .then((res) => {
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = function () {
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }
            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
          // window.open(this.pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        this.pdfLoading = false
      }, 100)
    },
    handleExport(_list) {
      let _currentTabIndex = this.$refs.templateRef.getCurrentTabRef().tabIndex
      const asyncParams = this.$refs.templateRef.getAsyncParams()
      let params = {
        page: {
          current: asyncParams.page.current,
          size: asyncParams.page.size
        },
        ...this.searchFormModel,
        printStatus: _currentTabIndex,
        ids: _list && _list.length > 0 ? _list.map((item) => item.id) : []
      }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery.barcodePrintRequestExportTv(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    synchronousWms(ids) {
      this.$API.receiptAndDelivery.barcodePrintRecordSync(ids).then((res) => {
        if (res && res?.code === 200) {
          this.$toast({ content: this.$t('同步成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('同步失败'), type: 'warning' })
        }
      })
    }
  }
}
</script>
