<!-- 供方-条码需求管理-泛智屏 -->
<template>
  <!-- <mt-template-page ref="templateRef" :template-config="pageConfig">
    <TabNotGenerateBarcode slot="slot-0" />
    <TabGenerateBarcode slot="slot-1" />
  </mt-template-page> -->
  <div class="fields-config-page mt-flex-direction-column">
    <div class="top-info mt-flex-direction-column">
      <mt-tabs
        :e-tab="false"
        :data-source="pageConfig"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
    </div>
    <div class="config-container">
      <TabNotGenerateBarcode v-show="tabTitle == $t('待生成条码')" />
      <TabGenerateBarcode v-show="tabTitle == $t('已生成条码')" />
    </div>
  </div>
</template>

<script>
export default {
  components: {
    // TabNotGenerateBarcode: () => import('./pages/NotGenerateBarcode.vue'),
    // TabGenerateBarcode: () => import('./pages/GenerateBarcode.vue')
    TabNotGenerateBarcode: require('./pages/NotGenerateBarcode.vue').default,
    TabGenerateBarcode: require('./pages/GenerateBarcode.vue').default
  },
  data() {
    return {
      tabIndex: 0,
      tabTitle: this.$t('待生成条码'),
      pageConfig: [{ title: this.$t('待生成条码') }, { title: this.$t('已生成条码') }]
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
      this.tabTitle = this.pageConfig[e]['title']
    }
  }
}
</script>
