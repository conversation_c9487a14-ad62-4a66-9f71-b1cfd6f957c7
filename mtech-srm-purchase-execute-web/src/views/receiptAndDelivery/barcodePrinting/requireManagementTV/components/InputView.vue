<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="disabled"
    ></mt-input>
  </div>
</template>
<script>
var bigDecimal = require('js-big-decimal')
export default {
  data() {
    return {
      data: {},
      disabled: true,
      deliveryTotalQuantity: '', //送货总数量
      packingQuantity: '', //装箱数量
      printQuantity: '', //本次打印数量
      grossWeight: '', //毛重
      lastQuantity: '', //尾数
      netWeight: '' //净重
    }
  },
  mounted() {
    this.deliveryTotalQuantity = this.data.deliveryTotalQuantity
    this.packingQuantity = this.data.packingQuantity
    this.printQuantity = this.data.printQuantity
    this.grossWeight = this.data.grossWeight
    this.lastQuantity = this.data.lastQuantity
    this.netWeight = this.data.netWeight
    this.$bus.$on('itemNameChange', (val) => {
      this.data.itemName = val
    }) //接受的物料名称
    this.$bus.$on('buyerOrgNameChange', (val) => {
      this.data.buyerOrgName = val
    }) //接受的采购组
    if (this.data.column.field === 'noPrintQuantity') {
      console.log(this.data)

      this.$bus.$emit('printQuantityChange', this.data.noPrintQuantity)
    }
    // this.$bus.$on("companyNameChange", (val) => {
    //   this.data.companyName = val;
    // }); //接受的公司
    // this.$bus.$on("supplierNameChange", (val) => {
    //   this.data.supplierName = val;
    // }); //接受的供应商
    if (this.data.column.field === 'companyCode') {
      this.$bus.$on('companyCodeChange', (val) => {
        this.data.companyCode = val
      }) //接受的公司编码
    }
    this.$bus.$on('companyNameChanged', (val) => {
      this.data.companyName = val
    }) //接受的公司名称
    this.$bus.$on('noPrintQuantityChange', (val) => {
      this.data.noPrintQuantity = val
    }) //未打印数量
    this.$bus.$on('warehouseNameChange', (val) => {
      this.data.warehouseName = val
    }) //接受的库存地点
    this.$bus.$on('deliveryTotalQuantityChange1', (val) => {
      this.deliveryTotalQuantity = val
      this.init()
    }) //送货总数量
    this.$bus.$on('printQuantityChange', (val) => {
      this.printQuantity = val
      this.init2()
    }) //本次打印数量
    this.$bus.$on('packingQuantityChange', (val) => {
      this.packingQuantity = val
      this.init()
      this.init1()
      this.init2()
    }) //装箱数量
    this.$bus.$on('grossWeightChange', (val) => {
      this.grossWeight = val
      this.init()
    }) //毛重
    this.$bus.$on('lastQuantityChange', (val) => {
      this.lastQuantity = val
      this.init1()
      this.init()
    }) //尾数
    this.$bus.$on('netWeightChange', (val) => {
      this.netWeight = val
      this.init1()
    }) //净重
    this.$bus.$on('siteNameChange', (val) => {
      this.data.siteName = val
    })
    if (
      this.data.column.field === 'supplierCode' &&
      !this.data['id'] &&
      !this.data['supplierCode']
    ) {
      let _currentInfo = JSON.parse(sessionStorage.userInfo)
      this.data['supplierCode'] = _currentInfo.accountName
      this.data['supplierName'] = _currentInfo.tenantName
    }
    if (
      this.data.column.field === 'supplierName' &&
      !this.data['id'] &&
      !this.data['supplierName']
    ) {
      let _currentInfo = JSON.parse(sessionStorage.userInfo)
      this.data['supplierName'] = _currentInfo.tenantName
    }
  },
  methods: {
    // lastGrossWeight 尾箱毛重 grossWeight 毛重 lastQuantity 尾数 packingQuantity 装箱数量
    // netWeight 净重 noPrintQuantity 未打印数量  deliveryTotalQuantity 送货总数量 lastNetWeight 尾箱净重
    // 尾箱毛重=毛重*尾数/装箱数量
    // 尾箱净重=净重*尾数/装箱数量
    // 尾数=本次打印数量 %装箱数量 余数
    init1() {
      if (this.data.column.field === 'lastNetWeight') {
        //尾箱净重
        if (this.netWeight && this.lastQuantity && this.packingQuantity) {
          let num = bigDecimal.multiply(this.netWeight, this.lastQuantity)
          let num1 = bigDecimal.divide(num, this.packingQuantity)
          num1 = bigDecimal.round(num1, 2)
          this.data.lastNetWeight = num1
          console.log('尾箱净重', this.netWeight, this.lastQuantity, this.packingQuantity)
        } else {
          this.data.lastNetWeight = ''
        }
      }
    },
    init2() {
      if (this.data.column.field === 'lastQuantity') {
        //尾数
        if (this.printQuantity && this.packingQuantity) {
          let i = Number(this.printQuantity) % Number(this.packingQuantity)
          this.data.lastQuantity = isNaN(i) ? 0 : i
          this.$bus.$emit('lastQuantityChange', Number(this.data.lastQuantity))
        }
      }
    },
    init() {
      if (this.data.column.field === 'lastGrossWeight') {
        //尾箱毛重
        if (this.grossWeight && this.lastQuantity && this.packingQuantity) {
          let num = bigDecimal.multiply(this.grossWeight, this.lastQuantity)
          let num1 = bigDecimal.divide(num, this.packingQuantity)
          num1 = bigDecimal.round(num1, 2)
          console.log('尾箱毛重', this.grossWeight, this.lastQuantity, this.packingQuantity)
          this.data.lastGrossWeight = num1
        } else {
          this.data.lastGrossWeight = ''
        }
      }
    }
  }
}
</script>
