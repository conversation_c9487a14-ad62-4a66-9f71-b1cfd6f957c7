<!-- 选择打印方式 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    width="550px"
    height="400px"
  >
    <div class="dialog-content" style="margin-top: 20px">
      <mt-form ref="modelForm" :model="modelForm" :rules="rules">
        <mt-form-item prop="templateType" :label="$t('打印方式')">
          <mt-radio v-model="modelForm.templateType" :data-source="typeOptions" />
        </mt-form-item>
        <mt-form-item v-if="modelForm.templateType === '3'" prop="fontSize" :label="$t('字号')">
          <mt-radio v-model="modelForm.fontSize" :data-source="fontSizeOptions" />
        </mt-form-item>
        <mt-form-item v-if="modelForm.templateType === '3'" prop="fontFamily" :label="$t('字体')">
          <mt-radio v-model="modelForm.fontFamily" :data-source="fontFamilyOptions" />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      modelForm: {
        templateType: '3',
        fontSize: '10',
        fontFamily: 'SimHei'
      },
      rules: {
        templateType: [
          {
            required: true,
            message: this.$t('请选择打印方式'),
            trigger: 'blur'
          }
        ],
        fontSize: [
          {
            required: true,
            message: this.$t('请选择字号'),
            trigger: 'blur'
          }
        ],
        fontFamily: [
          {
            required: true,
            message: this.$t('请选择字体'),
            trigger: 'blur'
          }
        ]
      },
      typeOptions: [
        {
          label: this.$t('标签纸'),
          value: '3'
        },
        {
          label: this.$t('A4纸'),
          value: '4'
        }
      ],
      fontSizeOptions: [
        {
          label: this.$t('8'),
          value: '8'
        },
        {
          label: this.$t('9'),
          value: '9'
        },
        {
          label: this.$t('10'),
          value: '10'
        },
        {
          label: this.$t('11'),
          value: '11'
        },
        {
          label: this.$t('12'),
          value: '12'
        }
      ],
      fontFamilyOptions: [
        {
          label: this.$t('黑体'),
          value: 'SimHei'
        },
        {
          label: this.$t('华文宋体'),
          value: 'STSong'
        },
        {
          label: this.$t('华文细黑'),
          value: 'STXihei'
        },
        {
          label: this.$t('华文中宋'),
          value: 'STZhongsong'
        },
        {
          label: this.$t('微软雅黑'),
          value: 'Microsoft YaHei Light'
        }
      ]
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    const barcodeRequireManagementTvPrintModel = localStorage.getItem(
      'barcodeRequireManagementTvPrintModel'
    )
    if (barcodeRequireManagementTvPrintModel) {
      const modelForm = JSON.parse(barcodeRequireManagementTvPrintModel)
      this.modelForm = { ...modelForm, templateType: '3' }
    }
  },
  methods: {
    confirm() {
      this.$refs.modelForm.validate((valid) => {
        if (valid) {
          localStorage.setItem(
            'barcodeRequireManagementTvPrintModel',
            JSON.stringify(this.modelForm)
          )
          this.$emit('confirm-function', this.modelForm)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style></style>
