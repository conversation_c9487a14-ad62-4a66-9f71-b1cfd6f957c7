import { i18n } from '@/main.js'
import Vue from 'vue'
import dayjs from 'dayjs'
import { MasterDataSelect } from '@/utils/constant'
const columnData = (that) => {
  const column = [
    {
      field: 'id',
      headerText: 'id',
      width: 0,
      visible: false,
      allowEditing: false,
      isPrimaryKey: true
    },
    {
      field: 'barcodeLevel',
      headerText: i18n.t('条码层级'),
      width: '100',
      valueConverter: {
        type: 'map',
        map: {
          1: i18n.t('外箱'),
          2: i18n.t('内箱')
        }
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headerTemplate', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('条码层级') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        return (
          <div>
            <mt-select
              v-model={scoped.barcodeLevel}
              fields={{ text: 'text', value: 'value' }}
              dataSource={that.barcodeLevelOptions}
              allow-filtering={true}
              filter-type='Contains'
              placeholder={i18n.t('请选择条码层级')}
              onChange={() => {
                that.$nextTick(() => {
                  if (scoped.barcodeLevel && scoped.siteCode && scoped.itemCode) {
                    that.getHistoryData(scoped)
                  }
                })
              }}
            />
          </div>
        )
      }
    },
    // {
    //   field: 'companyCode',
    //   headerText: i18n.t('公司'),
    //   width: '250',
    //   headerTemplate: () => {
    //     return {
    //       template: Vue.component('headerTemplate', {
    //         template: `
    //             <div class="headers">
    //               <span style="color: red">*</span>
    //               <span class="e-headertext">{{ $t('公司') }}</span>
    //             </div>
    //           `
    //       })
    //     }
    //   },
    //   template: () => {
    //     return {
    //       template: Vue.component('company', {
    //         template: `<div>{{data.companyCode}}-{{data.companyName}}</div>`,
    //         data: function () {
    //           return { data: {} }
    //         }
    //       })
    //     }
    //   },
    //   editorRender(h, scoped) {
    //     if (scoped.companyCode) {
    //       that.getSite(scoped, scoped.companyCode)
    //     }
    //     return (
    //       <div>
    //         <mt-select
    //           v-model={scoped.companyCode}
    //           fields={{ text: 'text', value: 'value' }}
    //           dataSource={that.companyOptions}
    //           allow-filtering={true}
    //           filter-type='Contains'
    //           placeholder='请选择公司'
    //           onChange={(e) => {
    //             const { companyName } = e.itemData
    //             scoped.companyName = companyName
    //             scoped.siteCode = ''
    //             scoped.siteName = ''
    //           }}
    //         />
    //       </div>
    //     )
    //   }
    // },
    {
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      width: '100',
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('工厂编码') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        if (scoped.siteCode) {
          that.getDeliveryCode(scoped, {
            text: scoped.deliveryCode
          })
          that.getItemOptions(scoped, {
            text: scoped.itemCode
          })
        }
        return (
          <div>
            <mt-select
              v-model={scoped.siteCode}
              fields={{ text: 'text', value: 'value' }}
              dataSource={that.siteOptions || []}
              allow-filtering={true}
              filter-type='Contains'
              popup-width={250}
              placeholder={i18n.t('请选择工厂')}
              onChange={(e) => {
                const { siteName } = e.itemData
                scoped.siteName = siteName
                if (!scoped.id) {
                  scoped.orderType = null
                  scoped.deliveryCode = null
                  scoped.deliveryLineNo = null
                  scoped.itemCode = null
                  scoped.itemName = null
                  scoped.buyerOrgCode = null
                  scoped.buyerOrgName = null
                }
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      width: '200',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('工厂名称') }}</span>
                </div>
              `
          })
        }
      }
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编号'),
      width: '150',
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('物料编号') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        return scoped.deliveryCode ? (
          <div>
            <mt-input v-model={scoped.itemCode} disabled />
          </div>
        ) : (
          <div>
            <mt-select
              v-model={scoped.itemCode}
              fields={{ text: 'text', value: 'value' }}
              dataSource={scoped.itemOptions}
              popup-width={300}
              allow-filtering={true}
              filter-type='Contains'
              filtering={(e) => that.itemCodeFiltering(scoped, e)}
              placeholder={scoped.siteCode ? i18n.t('请选择物料编号') : i18n.t('请先选择工厂编码')}
              disabled={!scoped.siteCode}
              onChange={(e) => {
                const { value, itemName, buyerOrgCode, buyerOrgName } = e.itemData
                if (scoped.itemCode !== value) {
                  scoped.materialIdentification = ''
                  scoped.place = ''
                  scoped.manufacturerNameCn = ''
                  scoped.manufacturerNameEn = ''
                }
                scoped.itemName = itemName
                scoped.buyerOrgCode = buyerOrgCode
                scoped.buyerOrgName = buyerOrgName
              }}
              onInput={() => {
                if (scoped.barcodeLevel && scoped.siteCode && scoped.itemCode) {
                  that.getHistoryData(scoped)
                }
              }}
            />
          </div>
        )
      },
      searchOptions: {
        operator: 'likeright',
        maxQueryValueLength: 20000
      }
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      width: '300',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      }
    },
    {
      field: 'deliveryTotalQuantity',
      headerText: i18n.t('送货总数量'),
      width: '150',
      allowReordering: false,
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('送货总数量') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        scoped.deliveryTotalQuantity = scoped.deliveryTotalQuantity ?? 0
        return (
          <div>
            <mt-input-number
              v-model={scoped.deliveryTotalQuantity}
              min={0}
              placeholder={i18n.t('请输入')}
              onInput={(val) => {
                scoped.noPrintQuantity = val
                scoped.printQuantity = val
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'noPrintQuantity',
      headerText: i18n.t('未打印数量'),
      width: '150',
      editorRender(h, scoped) {
        scoped.noPrintQuantity = scoped.noPrintQuantity ?? 0
        return (
          <div>
            <mt-input v-model={scoped.noPrintQuantity} disabled />
          </div>
        )
      }
    },
    {
      field: 'printQuantity',
      headerText: i18n.t('本次打印数量'),
      width: '150',
      editorRender(h, scoped) {
        scoped.printQuantity = scoped.printQuantity ?? 0
        return (
          <div>
            <mt-input-number
              v-model={scoped.printQuantity}
              min={0}
              max={scoped.deliveryTotalQuantity}
              placeholder={i18n.t('请输入')}
              onInput={(val) => {
                if (scoped.packingQuantity && scoped.packingQuantity !== 0) {
                  scoped.lastQuantity = val % scoped.packingQuantity
                } else {
                  scoped.lastQuantity = 0
                }
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'packingQuantity',
      headerText: i18n.t('装箱数量'),
      width: '150',
      allowReordering: false,
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('装箱数量') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        scoped.packingQuantity = scoped.packingQuantity ?? 0
        return (
          <div>
            <mt-input-number
              v-model={scoped.packingQuantity}
              min={0}
              max={scoped.deliveryTotalQuantity}
              placeholder={i18n.t('请输入')}
              onInput={(val) => {
                if (val && val !== 0) {
                  scoped.lastQuantity = scoped.printQuantity % val
                } else {
                  scoped.lastQuantity = 0
                }
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'lastQuantity',
      headerText: i18n.t('尾数'),
      width: '100',
      allowReordering: false,
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      }
    },
    {
      field: 'deliveryDate',
      headerText: i18n.t('送货日期'),
      width: '200',
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('送货日期') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        if (!scoped.deliveryDate) {
          scoped.deliveryDate = new Date()
        }
        return (
          <div>
            <mt-date-time-picker
              v-model={scoped.deliveryDate}
              placeholder={i18n.t('请选择')}
              show-clear-button={false}
              allow-edit={false}
            />
          </div>
        )
      }
    },
    {
      field: 'produceDate',
      headerText: i18n.t('生产日期'),
      width: '200',
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('生产日期') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        if (!scoped.produceDate) {
          scoped.produceDate = new Date()
        }
        return (
          <div>
            <mt-date-time-picker
              v-model={scoped.produceDate}
              placeholder={i18n.t('请选择')}
              show-clear-button={false}
              allow-edit={false}
            />
          </div>
        )
      }
    },
    {
      field: 'netWeight',
      headerText: i18n.t('净重（KG）'),
      width: '150',
      allowReordering: false,
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('净重（KG）') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        scoped.netWeight = scoped.netWeight ?? 0
        return (
          <div>
            <mt-input-number
              v-model={scoped.netWeight}
              min={0}
              precision={2}
              placeholder={i18n.t('请输入')}
            />
          </div>
        )
      }
    },
    {
      field: 'grossWeight',
      headerText: i18n.t('毛重（KG）'),
      width: '150',
      allowReordering: false,
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('毛重（KG）') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        scoped.grossWeight = scoped.grossWeight ?? 0
        return (
          <div>
            <mt-input-number
              v-model={scoped.grossWeight}
              min={0}
              precision={2}
              placeholder={i18n.t('请输入')}
            />
          </div>
        )
      }
    },
    {
      field: 'length1',
      headerText: i18n.t('长（CM）'),
      width: '150',
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('长（CM）') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        scoped.length1 = scoped.length1 ?? 0
        return (
          <div>
            <mt-input-number
              v-model={scoped.length1}
              min={0}
              precision={3}
              placeholder={i18n.t('请输入')}
            />
          </div>
        )
      }
    },
    {
      field: 'width',
      headerText: i18n.t('宽（CM）'),
      width: '150',
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('宽（CM）') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        scoped.width = scoped.width ?? 0
        return (
          <div>
            <mt-input-number
              v-model={scoped.width}
              min={0}
              precision={3}
              placeholder={i18n.t('请输入')}
            />
          </div>
        )
      }
    },
    {
      field: 'height',
      headerText: i18n.t('高（CM）'),
      width: '150',
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('高（CM）') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        scoped.height = scoped.height ?? 0
        return (
          <div>
            <mt-input-number
              v-model={scoped.height}
              min={0}
              precision={3}
              placeholder={i18n.t('请输入')}
            />
          </div>
        )
      }
    },
    {
      field: 'produceBatchCode',
      headerText: i18n.t('生产批次'),
      width: '150',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      }
    },
    {
      field: 'supplierBatchCode',
      headerText: i18n.t('供方批次'),
      width: '150',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      }
    },
    {
      field: 'materialIdentification',
      headerText: i18n.t('原厂物料标识'),
      width: '200',
      editorRender(h, scoped) {
        return (
          <div class='input-search-content'>
            <mt-input v-model={scoped.materialIdentification} readonly />
            <mt-icon
              name='icon_input_search'
              nativeOnClick={() => {
                that.handleShow(scoped)
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'itemEnName',
      headerText: i18n.t('物料名称（英文）'),
      width: '150',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      }
    },
    {
      field: 'buyerOrgName',
      headerText: i18n.t('采购组'),
      width: '200',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      }
    },
    {
      field: 'bin',
      headerText: 'BIN',
      width: '200',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      }
    },
    {
      field: 'lastGrossWeight',
      headerText: i18n.t('尾箱毛重（KG）'),
      width: '150',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      }
    },
    {
      field: 'lastNetWeight',
      headerText: i18n.t('尾箱净重（KG）'),
      width: '150',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      }
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: '120',
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('供应商编码') }}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        that.getSupplier(scoped)
        return (
          <div>
            {/* <mt-input v-model={scoped.supplierCode} disabled /> */}
            <mt-select
              v-model={scoped.supplierCode}
              fields={{ text: 'codeAndName', value: 'supplierCode' }}
              dataSource={that.supplierOptions}
              allow-filtering={true}
              popup-width={500}
              show-clear-button={true}
              filter-type='Contains'
              placeholder={i18n.t('请选择供应商编码')}
              onChange={(e) => {
                if (!e.itemData) {
                  scoped.supplierCode = null
                  scoped.supplierName = null
                  scoped.supplierEnName = null
                } else {
                  scoped.supplierCode = e.itemData.supplierCode
                  scoped.supplierName = e.itemData.supplierName
                  scoped.supplierEnName = e.itemData.supplierEnName
                }
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: '200',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      }
    },
    {
      field: 'supplierEnName',
      headerText: i18n.t('供应商（英文）'),
      width: '200',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: true
      }
    },
    {
      field: 'lineBody',
      headerText: i18n.t('线体'),
      width: '150',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      }
    },
    {
      field: 'supplierItemCode',
      headerText: i18n.t('供应商物料编码'),
      width: '200',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      }
    },
    {
      field: 'supplierItemName',
      headerText: i18n.t('供应商物料名称'),
      width: '200',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      }
    },
    {
      field: 'supplierLineBody',
      headerText: i18n.t('供应商线体'),
      width: '150',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      }
    },
    {
      field: 'remark',
      headerText: i18n.t('备注'),
      width: '150',
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      }
    },
    {
      field: 'orderType',
      headerText: i18n.t('主单类型'),
      width: '100',
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('送货单')
        }
      },
      editorRender(h, scoped) {
        return (
          <div>
            <span v-show={scoped.sourceType === 1}>
              {that.orderTypeOptions.find((i) => i.value == scoped.orderType)?.text}
            </span>
            <mt-select
              v-show={scoped.sourceType !== 1}
              v-model={scoped.orderType}
              fields={{ text: 'text', value: 'value' }}
              dataSource={that.orderTypeOptions}
              allow-filtering={true}
              show-clear-button={true}
              filter-type='Contains'
              placeholder={i18n.t('请选择主单类型')}
            />
          </div>
        )
      }
    },
    {
      field: 'deliveryCode',
      headerText: i18n.t('送货单号'),
      width: '150',
      editorRender(h, scoped) {
        if (scoped.deliveryCode) {
          setTimeout(() => {
            that.getDeliveryLineNo(scoped)
          }, 300)
        }
        return (
          <div>
            <span v-show={scoped.sourceType === 1}>{scoped.deliveryCode}</span>
            <mt-select
              v-show={scoped.sourceType !== 1}
              v-model={scoped.deliveryCode}
              fields={{ text: 'text', value: 'value' }}
              dataSource={scoped.deliveryCodeOptions}
              allow-filtering={true}
              show-clear-button={true}
              filtering={(e) => that.deliveryCodeFiltering(scoped, e)}
              popup-width={200}
              filter-type='Contains'
              placeholder={i18n.t('请选择')}
              onChange={(e) => {
                if (!e.itemData) {
                  scoped.deliveryLineNoOptions = []
                  scoped.orderType = null
                } else {
                  scoped.orderType = scoped.orderType ?? 0
                }
                if (!scoped.id) {
                  scoped.deliveryLineNo = null
                  scoped.itemCode = null
                  scoped.itemName = null
                  scoped.buyerOrgCode = null
                  scoped.buyerOrgName = null
                  scoped.deliveryTotalQuantity = null
                  scoped.noPrintQuantity = null
                  scoped.printQuantity = null
                }
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'deliveryLineNo',
      headerText: i18n.t('送货单行号'),
      width: '120',
      editorRender(h, scoped) {
        const deliveryLineNo = scoped.deliveryLineNo
        if (scoped.sourceType !== 1) {
          scoped.deliveryLineNo = scoped.deliveryLineNo ? Number(scoped.deliveryLineNo) : null
        }
        // let deliveryLineNo = scoped.deliveryLineNo?.split(',').map((i) => Number(i)) || []
        return (
          <div>
            {/* <mt-multi-select */}
            <span v-show={scoped.sourceType === 1}>{deliveryLineNo}</span>
            <mt-select
              v-show={scoped.sourceType !== 1}
              v-model={scoped.deliveryLineNo}
              fields={{ text: 'text', value: 'value' }}
              dataSource={scoped.deliveryLineNoOptions}
              allow-filtering={true}
              filter-type='Contains'
              placeholder={i18n.t('请选择')}
              onChange={(e) => {
                const {
                  itemCode,
                  itemName,
                  buyerOrgCode,
                  buyerOrgName,
                  saleOrderNo,
                  deliveryQuantity
                } = e.itemData
                if (scoped.itemCode !== itemCode) {
                  scoped.materialIdentification = ''
                  scoped.place = ''
                  scoped.manufacturerNameCn = ''
                  scoped.manufacturerNameEn = ''
                }
                scoped.itemCode = itemCode
                scoped.itemName = itemName
                scoped.buyerOrgCode = buyerOrgCode
                scoped.buyerOrgName = buyerOrgName
                scoped.produceBatchCode = saleOrderNo
                scoped.deliveryTotalQuantity = deliveryQuantity
                scoped.noPrintQuantity = deliveryQuantity
                scoped.printQuantity = deliveryQuantity
                // if (e.value) {
                //   e.value.forEach((i) => {
                //     scoped.deliveryLineNoOptions.forEach((j) => {
                //       if (i === j.value) {
                //         const {
                //           itemCode,
                //           itemName,
                //           buyerOrgCode,
                //           buyerOrgName,
                //           saleOrderNo,
                //           deliveryQuantity
                //         } = j
                //         if (scoped.itemCode !== itemCode) {
                //           scoped.materialIdentification = ''
                //           scoped.place = ''
                //           scoped.manufacturerNameCn = ''
                //           scoped.manufacturerNameEn = ''
                //         }
                //         scoped.itemCode = itemCode
                //         scoped.itemName = itemName
                //         scoped.buyerOrgCode = buyerOrgCode
                //         scoped.buyerOrgName = buyerOrgName
                //         scoped.produceBatchCode = saleOrderNo
                //         scoped.deliveryTotalQuantity = deliveryQuantity
                //         scoped.noPrintQuantity = deliveryQuantity
                //         scoped.printQuantity = deliveryQuantity
                //       }
                //     })
                //   })
                // }
                // scoped.deliveryLineNo = e.value.join(',')
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'createTime',
      headerText: i18n.t('创建时间'),
      width: '150',
      searchOptions: {
        ...MasterDataSelect.timeRange,
        default: [
          // new Date(dayjs().subtract(3, 'month').format('YYYY-MM-DD 00:00:00')),
          new Date(dayjs().subtract(15, 'day').format('YYYY-MM-DD 00:00:00')),
          new Date(dayjs().format('YYYY-MM-DD 23:59:59'))
        ]
      }
    }
  ]
  return column
}

export const componentConfig = (that) => {
  const config = [
    {
      activatedRefresh: false,
      isUseCustomSearch: true, // 是否使用自定义查询
      isCustomSearchRules: true,
      isUseCustomEditor: true,
      useToolTemplate: false, // 此项不使用预置的表格操作按钮'新增、编辑、删除'
      useBaseConfig: false, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
      toolbar: {
        tools: [
          [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              permission: ['O_02_1352'],
              title: i18n.t('新增')
            },
            {
              id: 'CloseEdit',
              icon: 'icon_table_delete',
              title: i18n.t('取消编辑')
            },
            {
              id: 'Confirm',
              icon: 'icon_solid_Pauseorder',
              permission: ['O_02_1668'],
              title: i18n.t('现品票打印')
            },
            {
              id: 'PrintA4',
              icon: 'icon_solid_Pauseorder',
              permission: ['O_02_1668'],
              title: i18n.t('机芯厂A4纸打印')
            },
            {
              id: 'ConfirmImte',
              icon: 'icon_solid_Pauseorder',
              permission: ['O_02_1669'],
              title: i18n.t('物料标签打印')
            },
            {
              id: 'Delete',
              icon: 'icon_solid_Delete',
              permission: ['O_02_1353'],
              title: i18n.t('删除')
            },
            {
              id: 'Import',
              icon: 'icon_solid_Import',
              permission: ['O_02_1670'],
              title: i18n.t('导入')
            },
            {
              id: 'export',
              icon: 'icon_solid_Import',
              permission: ['O_02_1355'],
              title: i18n.t('导出')
            }
          ],
          ['Setting']
        ]
      },
      buttonQuantity: 7,
      gridId: 'e9020b1f-cb9e-4e47-b88c-32d7360451bf',
      grid: {
        allowPaging: true, // 是否使用内置分页器
        allowEditing: true, //开启表格编辑操作
        virtualPageSize: 30,
        enableVirtualization: true,
        customSelection: true,
        editSettings: {
          allowEditing: true,
          allowAdding: true,
          allowDeleting: true,
          mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
          showConfirmDialog: false,
          showDeleteConfirmDialog: false,
          newRowPosition: 'Top'
        },
        columnData: columnData(that),
        asyncConfig: {
          url: '/srm-purchase-execute/tenant/barcode/request/tv/page',
          params: {
            printStatus: 0
          }
        }
      }
    }
  ]
  return config
}

export const componentConfig2 = (that) => {
  const config = [
    {
      isUseCustomSearch: true, // 是否使用自定义查询
      isCustomSearchRules: true,
      isUseCustomEditor: true,
      useToolTemplate: false, // 此项不使用预置的表格操作按钮'新增、编辑、删除'
      useBaseConfig: false, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
      toolbar: {
        tools: [
          [
            {
              id: 'Delete',
              icon: 'icon_solid_Delete',
              permission: ['O_02_1356'],
              title: i18n.t('删除')
            },
            {
              id: 'Confirm',
              icon: 'icon_solid_Pauseorder',
              permission: ['O_02_1671'],
              title: i18n.t('现品票打印')
            },
            {
              id: 'PrintA4',
              icon: 'icon_solid_Pauseorder',
              permission: ['O_02_1671'],
              title: i18n.t('机芯厂A4纸打印')
            },
            {
              id: 'ConfirmImte',
              icon: 'icon_solid_Pauseorder',
              permission: ['O_02_1672'],
              title: i18n.t('物料标签打印')
            },
            {
              id: 'export',
              icon: 'icon_solid_Import',
              permission: ['O_02_1358'],
              title: i18n.t('导出')
            },
            {
              id: 'Synchronous',
              icon: 'icon_table_restart',
              title: i18n.t('同步WMS')
            }
          ],
          ['Setting']
        ]
      },
      gridId: 'ba5f7dde-d910-400c-abc9-e8687cee80b3',
      grid: {
        allowPaging: true, // 是否使用内置分页器
        virtualPageSize: 30,
        enableVirtualization: true,
        customSelection: true,
        columnData: columnData(that),
        asyncConfig: {
          url: '/srm-purchase-execute/tenant/barcode/request/tv/page',
          params: {
            printStatus: 1
          }
        }
      }
    }
  ]
  return config
}

export const dialogColumn = () => {
  const column = [
    {
      field: 'materialIdentification',
      headerText: i18n.t('原厂物料标识')
    },
    {
      field: 'place',
      headerText: i18n.t('原产地')
    },
    {
      field: 'manufacturerNameCn',
      headerText: i18n.t('制造商中文名')
    },
    {
      field: 'manufacturerNameEn',
      headerText: i18n.t('制造商英文名')
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码')
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码')
    }
  ]
  return column
}
