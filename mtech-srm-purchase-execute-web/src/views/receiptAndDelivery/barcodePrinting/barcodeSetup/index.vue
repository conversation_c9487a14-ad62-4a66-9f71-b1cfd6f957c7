<template>
  <!-- 条码需求管理 -->
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
      @selectedChanged="selectedChanged"
    ></mt-template-page>
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :is-show-tips="true"
      :request-urls="requestUrls"
      @closeUploadExcel="handleImport(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>
<script>
import { checkColumn, lastColumn } from './config/index'
import { BASE_TENANT } from '@/utils/constant'
import { cloneDeep } from 'lodash'
import * as UTILS from '@/utils/utils'
export default {
  components: {
    UploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    return {
      downTemplateParams: {
        pageFlag: false
      }, // 通知配置导入下载模板参数
      lastColumn: lastColumn,
      uploadParams: {}, // 导入通知配置文件参数

      requestUrls: {
        templateUrlPre: 'receiptAndDelivery',
        templateUrl: 'barcodeExportTemplate', // 下载模板接口方法名
        uploadUrl: 'barcodePrintConfigImport' // 上传接口方法名
      },
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              permission: ['O_02_0657'],
              title: this.$t('新增')
            },
            {
              id: 'Delete',
              icon: 'icon_solid_Delete',
              permission: ['O_02_0658'],
              title: this.$t('删除')
            },
            // { id: "Import", icon: "icon_solid_Import", title: this.$t("导入") },
            {
              id: 'Export1',
              icon: 'icon_solid_Import',
              permission: ['O_02_0659'],
              title: this.$t('导出')
            },
            {
              id: 'import',
              icon: 'icon_solid_Import',
              // permission: ["O_02_0659"],
              title: this.$t('导入')
            }
          ],
          gridId: this.$tableUUID.receiptAndDelivery.barcodeSetup.list,
          grid: {
            allowEditing: true, //开启表格编辑操作
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            columnData: checkColumn.concat(lastColumn),
            asyncConfig: {
              url: `${BASE_TENANT}/barcodePrintConfig/query`,
              serializeList: (list) => {
                list.forEach((item) => {
                  item.addId = this.addId++
                  item.isEntry = '1' //是否是带入的数据
                })
                this.currentList = list
                return list
              }
            }
          }
        }
      ],
      currentList: [], //从接口获取到的最新的数据
      nowEditRowFlag: '', //当前编辑的行id
      addId: '1',
      isEdit: '1', //是否编辑 1是编辑 2不是编辑
      selectedOtherInfo: {},
      supplierInfo: {
        supplierName: '',
        supplierCode: '',
        supplierEnName: '',
        supplierId: ''
      }
    }
  },
  mounted() {
    console.log(this.$tableUUID)
    this.getSupplier()
  },
  methods: {
    //新增
    handleImport(flag) {
      //导入
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    getSupplier() {
      // 查询供应商的数据
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      console.log(userInfo)
      // this.supplierInfo.supplierName = userInfo.enterpriseName;
      // this.supplierInfo.supplierId = userInfo.enterpriseId;
      // this.supplierInfo.supplierCode = userInfo.enterpriseCode;
      this.$API.masterData.getEnterpriseInfo().then((res) => {
        this.supplierInfo.supplierEnName = res.data.enterpriseEnglishName
      })
      console.log(this.supplierInfo)
    },
    selectedChanged(val) {
      Object.assign(this.selectedOtherInfo, val.itemInfo)
    },
    endEdit() {
      //让组件失去焦点
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
    },
    // 上传成功后
    upExcelConfirm() {
      this.handleImport(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
      this.$refs.templateRef.refreshCurrentGridData()
    },
    actionBegin(args) {
      console.log(args)
      if (args.requestType === 'add') {
        console.log(this.lastColumn)

        let lastColumn = cloneDeep(this.lastColumn)
        lastColumn.forEach((item) => {
          args.data[item.field] = ''
          console.log(item)
          if (
            item.field === 'packingQuantity' ||
            item.field === 'netWeight' ||
            item.field === 'grossWeight' ||
            item.field === 'length' ||
            item.field === 'width' ||
            item.field === 'height'
          ) {
            args.data[item.field] = '0'
          }
          args.data.isEntry = '2'
        })
        args.data.addId = 'add' + Math.random().toString(36).substring(3, 8)
        args.data.supplierEnName = this.supplierInfo.supplierEnName
        args.data.supplierName = this.supplierInfo.supplierName
        args.data.supplierId = this.supplierInfo.supplierId
        args.data.supplierCode = this.supplierInfo.supplierCode

        this.nowEditRowFlag = args.data.addId
      }
      if (args.requestType == 'beginEdit') {
        this.nowEditRowFlag = args.rowData.addId
        args.rowData.printQuantity = args.rowData.noPrintQuantity
        //   item.printQuantity = item.noPrintQuantity;
        // if (!(args.rowData.isEntry == "2")) {
        //   this.$toast({
        //     content: this.$t("此状态不可编辑"),
        //     type: "warning",
        //   });
        //   args.cancel = true;
        // }
      }
    },
    actionComplete(args) {
      console.log(args)
      const { rowIndex } = args

      if (args.requestType === 'beginEdit' || args.requestType === 'add') {
        this.isEdit = '1'
      }
      if (
        args.requestType === 'refresh' ||
        args.requestType === 'save' ||
        args.requestType === 'delete'
      ) {
        this.isEdit = '2'
      }
      if (args.requestType === 'save' && args.action === 'edit') {
        //编辑完成
        if (
          Number(args.data.packingQuantity) === 0 ||
          Number(args.data.netWeight) === 0 ||
          Number(args.data.grossWeight) === 0 ||
          Number(args.data.length) === 0 ||
          Number(args.data.width) === 0 ||
          Number(args.data.height) === 0
        ) {
          this.$toast({
            content: this.$t('装箱数量,净重，毛重以及长宽高不能为0'),
            type: 'error'
          })
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          return
        }
        let row = this.getRow()

        if (row.isEntry === '2') {
          //新增错误重新编辑
          this.addRow(row)
        }
        if (row.isEntry === '1') {
          this.editRow(row)
        }
      }
      if (args.requestType === 'save' && args.action === 'add') {
        //新增完成
        if (
          Number(args.data.packingQuantity) === 0 ||
          Number(args.data.netWeight) === 0 ||
          Number(args.data.grossWeight) === 0 ||
          Number(args.data.length) === 0 ||
          Number(args.data.width) === 0 ||
          Number(args.data.height) === 0
        ) {
          this.$toast({
            content: this.$t('装箱数量不能为0'),
            type: 'error'
          })
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          return
        }
        let row = this.getRow()
        this.addRow(row)
      }
    },
    getRow() {
      //获取编辑或者新增的数据
      let currentRecords =
        this.$refs.templateRef?.getCurrentUsefulRef().gridRef?.ejsRef.getCurrentViewRecords() || []
      console.log(currentRecords)
      console.log(this.nowEditRowFlag)
      let row = cloneDeep(this.selectedOtherInfo)
      console.log(row)
      let info = {}
      currentRecords.some((item) => {
        if (item.addId === this.nowEditRowFlag) {
          Object.assign(item, row)
          info = item
        }
      })
      return info
    },
    getParams(row) {
      let params = {
        // id: row.id || 0,
        supplierEnName: row.supplierEnName,
        barcodeCode: '', //条码code
        barcodeLevel: row.barcodeLevel, //条码层级
        buyerOrgCode: row.buyerOrgCode, //采购组code
        buyerOrgId: row.buyerOrgId, //采购组id
        buyerOrgName: row.buyerOrgName, //采购组name
        companyCode: row.companyCode || '',
        companyId: row.companyId || '',
        companyName: row.companyName || '',
        grossWeight: row.grossWeight || 0, //毛重
        height: row.height || 0, //高
        // id: row.id || 0,
        itemCode: row.itemCode, //物料编码
        itemEnName: row.itemEnName, //物料英文名称
        itemId: row.itemId || 0, //物料id
        itemName: row.itemName, //物料名称

        length: row.length || 0, //长

        netWeight: row.netWeight || 0, //净重
        packingQuantity: row.packingQuantity || 0,
        siteCode: row.siteCode || '', //工厂code
        siteId: row.siteId || 0, //工厂id
        siteName: row.siteName || '', //工厂name
        supplierCode: row.supplierCode || '', //供应商code

        supplierId: row.supplierId || 0, //供应商id
        supplierItemCode: row.supplierItemCode || '', //供应商物料code
        supplierItemId: row.supplierItemId || 0, //供应商物料id
        supplierItemName: row.supplierItemName || '', //供应商物料name
        supplierLineBody: row.supplierLineBody, //供应商线体
        supplierName: row.supplierName || '', //供应商name
        tenantId: row.tenantId || '',
        width: row.width || 0 //宽
      }
      return params
    },
    addRow(row) {
      let params = this.getParams(row)
      console.log(params)
      console.log(params.siteName.length)
      if (params.siteName.length == 0 || params.itemCode.length == 0) {
        this.$toast({
          content: this.$t('带星号的全为必填字段'),
          type: 'error'
        })
      } else {
        this.$API.receiptAndDelivery
          .barcodePrintConfigSave(params)
          .then(() => {
            this.$toast({
              content: this.$t('新增操作成功'),
              type: 'success'
            })
            this.selectedOtherInfo = {}
            this.updateList()
          })
          .catch(() => {
            this.selectedOtherInfo = {}
            this.updateList()
          })
      }
    },
    editRow(row) {
      console.log(row, '编辑行数据')
      let params = this.getParams(row)
      params.id = row.id
      this.$API.receiptAndDelivery.barcodePrintConfigSave(params).then(() => {
        this.$toast({
          content: this.$t('编辑操作成功'),
          type: 'success'
        })
        this.selectedOtherInfo = {}

        this.updateList()
      })
    },
    handleDelete(row) {
      //删除
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认删除选中的条码配置吗?')
        },
        success: () => {
          let ids = row.map((item) => item.id)
          this.$API.receiptAndDelivery.barcodePrintConfigDelete(ids).then(() => {
            this.$toast({
              content: this.$t('删除条码配置操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    updateList() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    handleAdd() {
      //新增
      console.log(this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef)
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
    },

    handleExport() {
      //导出
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 5000 },
        pageFlag: true,
        rules: rule.rules || []
      }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery.barcodePrintConfigExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    //点击顶部的操作按钮
    handleClickToolBar(e) {
      if (e.toolbar.id === 'import') {
        this.handleImport(true)
        return
      }
      if (e.toolbar.id === 'Add') {
        this.handleAdd()
        return
      }

      if (e.toolbar.id === 'Export1') {
        this.handleExport()
        return
      }
      let selectRecords = e.grid.getSelectedRecords()

      if (e.toolbar.id === 'Delete') {
        if (!selectRecords.length) {
          this.$toast({ content: this.$t('请至少选择一行'), type: 'warning' })
          return
        }
        this.handleDelete(selectRecords)
      }
    }
  }
}
</script>
