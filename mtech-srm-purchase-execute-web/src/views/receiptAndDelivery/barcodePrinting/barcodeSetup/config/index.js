import { i18n } from '@/main.js'
// import UTILS from "../../../../../utils/utils";
import Input from '../components/Input.vue'
import InputView from '../components/InputView.vue'
import Select from '../components/Select.vue'
// import DatePicker from "../components/DatePicker.vue";
// import DatePickerView from "../components/DataPickerView.vue";
import InputNumber from '../components/InputNumber.vue'
// import InputNumberView from "../components/InputNumberView.vue";
import Vue from 'vue'
export const checkColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false,
    allowEditing: false
  }
]
export const importColumn = [
  {
    field: 'ImportType',
    headerText: i18n.t('导入状态'),
    width: '150',
    allowEditing: false
  }
]
export const lastColumn = [
  {
    visible: false,
    isPrimaryKey: true, // 主键
    field: 'addId',
    headerText: 'addId',

    allowEditing: false
  },
  {
    field: 'id',
    headerText: 'id',
    visible: false,

    width: 0,
    allowEditing: false
  },
  {
    field: 'barcodeLevel',
    headerText: i18n.t('条码层级'),
    // selectOptions: [
    //   { label: i18n.t("一级"), value: "0" },
    //   { label: i18n.t("二级"), value: "1" },
    //   { label: i18n.t("三级"), value: "2" },
    //   { label: i18n.t("四级"), value: "3" },
    // ],
    width: '100',
    editTemplate: () => {
      return { template: Select }
    },
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('一级'),
        2: i18n.t('二级'),
        3: i18n.t('三级'),
        4: i18n.t('四级')
      }
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    width: '200',
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.companyCode}}-{{data.companyName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    },
    selectOptions: [],
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '180',
    selectOptions: [],
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('工厂')}}</span>
              </div>
            `
        })
      }
    }
  },

  {
    field: 'supplierName',
    headerText: i18n.t('供应商'),
    allowEditing: false,
    selectOptions: [],
    width: '175',
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.supplierCode}}-{{data.supplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    },
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'supplierEnName',
    headerText: i18n.t('供应商（英文）'),
    width: '140',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    selectOptions: [],
    width: '120',
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料编号')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: '275',
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    allowEditing: false,
    field: 'itemEnName',
    headerText: i18n.t('物料名称（英文）'),
    width: '150',
    editTemplate: () => {
      return { template: Input }
    }
  },
  // {
  //   field: "buyerOrgName",
  //   headerText: i18n.t("采购组"),
  //   width: "200",
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
  {
    field: 'supplierItemCode',
    headerText: i18n.t('供应商物料编码'),
    width: '150',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'supplierItemName',
    headerText: i18n.t('供应商物料名称'),
    width: '265',
    allowEditing: false,
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'supplierLineBody',
    headerText: i18n.t('供应商线体'),
    width: '205',
    allowEditing: false,
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'packingQuantity',
    headerText: i18n.t('装箱数量'),
    selectOptions: [],
    width: '95',
    editTemplate: () => {
      return { template: InputNumber }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('装箱数量')}}</span>
              </div>
            `
        })
      }
    }
  },
  // {
  //   field: "deliveryTotalQuantity",
  //   headerText: i18n.t("送货总数量"),
  //   editTemplate: () => {
  //     return { template: InputNumber };
  //   },
  // },
  // {
  //   field: "noPrintQuantity",
  //   headerText: i18n.t("未打印数量"),
  //   width: "150",
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
  // {
  //   field: "printQuantity",
  //   headerText: i18n.t("本次打印数量"),
  //   width: "150",
  //   editTemplate: () => {
  //     return { template: InputNumber };
  //   },
  // },

  {
    field: 'netWeight',
    headerText: i18n.t('净重（KG）'),
    width: '100',
    editTemplate: () => {
      return { template: InputNumber }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('净重（KG）')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'grossWeight',
    headerText: i18n.t('毛重（KG）'),
    width: '100',
    editTemplate: () => {
      return { template: InputNumber }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('毛重（KG）')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'length',
    headerText: i18n.t('长（CM）'),
    width: '90',
    editTemplate: () => {
      return { template: InputNumber }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('长（CM）')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'width',
    headerText: i18n.t('宽（CM）'),
    selectOptions: [],
    width: '90',
    editTemplate: () => {
      return { template: InputNumber }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('宽（CM）')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'height',
    headerText: i18n.t('高（CM）'),
    width: '90  ',
    editTemplate: () => {
      return { template: InputNumber }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('高（CM）')}}</span>
              </div>
            `
        })
      }
    }
  }
]
