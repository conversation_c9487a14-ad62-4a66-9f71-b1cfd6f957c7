<template>
  <div>
    <debounce-filter-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="{ text: 'theCodeName', value: 'value' }"
      :placeholder="placeholder"
      :request="postChange"
      :popup-width="400"
      :value-template="valueTemplate"
      @change="selectChange"
      :open-dispatch-change="true"
      :disabled="isDisabled"
      :allow-filtering="true"
    ></debounce-filter-select>
  </div>
</template>
<script>
// import { utils } from "@mtech-common/utils";
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      valueTemplate: null,

      purchase: '', //采购组code
      isDisabled: false,
      siteId: '',
      customerEnterpriseId: '',
      supplierCode: '', //供应商code

      siteCode: ''
    }
  },
  mounted() {
    this.dataSource = this.data.column.selectOptions
    if (this.data.column.field === 'companyName') {
      this.getCompany()
    }
    if (this.data.column.field === 'barcodeLevel') {
      this.getBarcodeLevel()
    }
    if (this.data.column.field === 'itemCode') {
      this.$bus.$on('siteId', (val) => {
        this.siteId = val
      })
      this.$bus.$on('customerEnterpriseId', (val) => {
        this.customerEnterpriseId = val
      })
      this.$bus.$off('siteCode')
      this.$bus.$on('siteCode', (val) => {
        this.siteCode = val
        this.getCategoryItem({})
      })
      // this.$bus.$on("tenantId", (val) => {

      // });
      //物料下拉
      // this.getBusinessGroupTypeListByDictCode();
    }
    if (this.data.column.field === 'siteName') {
      //工厂下拉
      // this.$bus.$off("customerEnterpriseId");
      this.$bus.$on('customerEnterpriseId', (val) => {
        this.customerEnterpriseId = val
        this.findOrgSiteInfo('')
      })
      this.valueTemplate = codeNameColumn({
        firstKey: 'siteOrgCode',
        secondKey: 'siteOrgName'
      })
    }
    if (this.data.column.field === 'supplierName') {
      //供应商下拉
      this.getSupplier()
    }
    // if (this.data.column.field === "deliveryCode") {
    //   //送货单号
    //   this.getDeliveryCode();
    // }
  },
  methods: {
    getCompany() {
      let obj = {
        fuzzyNameOrCode: ''
      }
      this.$API.masterData.getCustomer(obj).then((res) => {
        res.data.forEach((item) => {
          item.theCodeName = item.customerName // 	客户名称
          item.value = item.customerName // 	客户名称
        })
        this.dataSource = res.data || []
      })
    },
    getBarcodeLevel() {
      this.dataSource = [
        { theCodeName: this.$t('一级'), value: 1 },
        { theCodeName: this.$t('二级'), value: 2 },
        { theCodeName: this.$t('三级'), value: 3 },
        { theCodeName: this.$t('四级'), value: 4 }
      ]
    },

    // 模糊
    postChange(e) {
      if (this.data.column.field === 'itemCode') {
        this.getCategoryItem(e)
      }
      if (this.data.column.field === 'siteName') {
        this.findOrgSiteInfo(e)
      }
    },
    getSupplier() {
      //查询供应商的数据
      this.$API.masterData.getSupplier().then((res) => {
        res.data.forEach((item) => {
          item.theCodeName = item.supplierName // 	客户名称
          item.value = item.supplierName // 	客户名称
        })
        this.dataSource = res.data || []
      })
    },
    findOrgSiteInfo(args) {
      const { text, updateData } = args
      //工厂下拉
      let obj = {
        fuzzyParam: text,
        buyerEnterpriseId: this.customerEnterpriseId
      }
      this.$API.masterData.getSourceAvailableView(obj).then((res) => {
        const list = res?.data || []
        list.forEach((item) => {
          item.value = item.siteOrgName
        })
        this.dataSource = addCodeNameKeyInList({
          firstKey: 'siteOrgCode',
          secondKey: 'siteOrgName',
          list
        })
        if (updateData) {
          this.$nextTick(() => {
            updateData(this.dataSource)
          })
        }
      })
    },
    getCategoryItem(args) {
      //物料下拉
      const { text, updateData } = args

      let obj = {
        buyerEnterpriseId: this.customerEnterpriseId,
        siteCode: this.siteCode,
        fuzzyParam: text
      }
      this.$API.masterData.postSourceItemsInVendor(obj).then((res) => {
        const list = res?.data || []
        list.forEach((item) => {
          item.theCodeName = item.itemCode
          item.value = item.itemCode
        })
        this.dataSource = list
        if (updateData) {
          this.$nextTick(() => {
            updateData(this.dataSource)
          })
        }
      })
      // this.fields = { text: "itemCode", value: "itemCode" };
    },
    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      if (this.data.column.field === 'companyName') {
        this.$bus.$emit('customerEnterpriseId', val.itemData.customerEnterpriseId)

        // this.$bus.$emit("tenantId", val.itemData.tenantId);
        this.$API.masterData
          .supplierFindInBuyingByCustomerCode({
            customerCode: val.itemData.customerCode
          })
          .then((res) => {
            this.$bus.$emit('supplierNameChange', res.data.supplierName)
            this.$parent.$emit('selectedChanged', {
              //传出额外数据
              fieldCode: 'companyName',
              itemInfo: {
                companyName: val.itemData.customerName,
                // companyId: val.itemData.companyOrgId,
                companyCode: val.itemData.customerCode,
                customerEnterpriseId: val.itemData.customerEnterpriseId,
                supplierName: res.data.supplierName,
                supplierCode: res.data.supplierCode
              }
            })
          })
      }
      if (this.data.column.field === 'barcodeLevel') {
        this.$parent.$emit('selectedChanged', {
          //传出额外数据
          fieldCode: 'barcodeLevel',
          itemInfo: {
            barcodeLevel: val.itemData.value
          }
        })
      }
      if (this.data.column.field === 'itemCode') {
        this.$bus.$on('siteId', (val) => {
          this.siteId = val
        })
        //物料下拉
        this.$bus.$emit('itemNameChange', val.itemData.itemName) //传给物料名称
        this.$parent.$emit('selectedChanged', {
          //传出额外数据工厂
          fieldCode: 'itemCode',
          itemInfo: {
            itemCode: val.itemData.itemCode,
            itemName: val.itemData.itemName,
            itemId: val.itemData.id
          }
        })
      }
      if (this.data.column.field === 'siteName') {
        this.$bus.$emit('siteId', val.itemData.id)
        this.$bus.$emit('siteCode', val.itemData.siteOrgCode)

        // this.$bus.$emit("companyNameChanged", val.itemData.companyOrgName);

        console.log(val.itemData)
        this.$parent.$emit('selectedChanged', {
          //传出额外数据工厂
          fieldCode: 'siteName',
          itemInfo: {
            siteName: val.itemData.siteOrgName,
            siteCode: val.itemData.siteOrgCode,
            siteId: val.itemData.siteOrgId
            // companyName: val.itemData.companyOrgName,
            // companyId: val.itemData.companyOrgId,
            // companyCode: val.itemData.companyOrgCode,
          }
        })
      }
      if (this.data.column.field === 'supplierName') {
        this.$parent.$emit('selectedChanged', {
          //传出额外数据供应商
          fieldCode: 'supplierCode',
          itemInfo: {
            supplierCode: val.itemData.supplierCode,
            supplierName: val.itemData.supplierName,
            supplierId: val.itemData.id
          }
        })
      }
    }
  },
  beforeDestroy() {
    this.$bus.$off('siteName')

    this.$bus.$off('customerEnterpriseId ')
  }
}
</script>
