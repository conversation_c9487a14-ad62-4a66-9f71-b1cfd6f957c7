<template>
  <div class="full-height">
    <top-info
      ref="topInfo"
      :common-flag-one="commonFlagOne"
      @switchChange="switchChange"
    ></top-info>
    <mt-template-page
      ref="pickGridRef"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    />
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { columnData } from './config'
export default {
  components: {
    TopInfo: () => import('./components/topInfo')
  },
  data() {
    return {
      commonFlag: true,
      id: '',
      commonFlagOne: {
        type: false,
        autoCreateVmiFlag: false
      },
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              permission: ['O_02_0673', 'O_02_1674', 'O_02_1676'],
              title: this.$t('新增')
            },
            {
              id: 'Delete',
              icon: 'icon_table_delete',
              permission: ['O_02_0674', 'O_02_1675', 'O_02_1677'],
              title: this.$t('删除')
            }
          ],
          gridId: this.$tableUUID.receiptAndDelivery.barcodeRequireSetup.ProcessedList,

          grid: {
            lineSelection: true,
            columnData: columnData,
            dataSource: [],
            frozenColumns: 1,

            asyncConfig: {
              url: `${BASE_TENANT}/barcodeAutoCreateConfig/query`,
              serializeList: (list) => {
                let obj = []
                list.forEach((item) => {
                  if (item.commonFlag === 0) {
                    obj.push(item)
                  }
                  if (item.commonFlag === 1) {
                    this.id = item.id
                    if (item.autoCreateFlag === 1) {
                      this.commonFlagOne.type = true
                    } else {
                      this.commonFlagOne.type = false
                    }
                    if (item.autoCreateVmiFlag === 1) {
                      this.commonFlagOne.autoCreateVmiFlag = true
                    } else {
                      this.commonFlagOne.autoCreateVmiFlag = false
                    }
                  }
                })
                return obj
              }
            }
          }
        }
      ],
      switchParams: {
        abolished: '',
        autoCreateFlag: 0,
        autoCreateVmiFlag: 0,
        commonFlag: 1,
        companyCode: '',
        companyId: '',
        companyName: '',
        id: this.id,
        siteCode: '',
        siteId: '',
        siteName: '',
        tenantId: ''
      }
    }
  },
  methods: {
    switchChange(e) {
      this.commonFlag = e
      if (e.type === 'autoCreateFlag') {
        this.switchParams.autoCreateFlag = e.value === true ? 1 : 0
      }

      if (e.type === 'autoCreateVmiFlag') {
        this.switchParams.autoCreateVmiFlag = e.value === true ? 1 : 0
      }

      this.$API.receiptAndDelivery.barcodeAutoCreateConfigSave(this.switchParams).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      })
    },
    handleClickToolBar(e) {
      console.log('点击了顶部的按钮', e)
      if (e.toolbar.id == 'Delete') {
        let selectRows = e.grid.getSelectedRecords()
        // if (!selectRows.length) {
        //   this.$toast({ content: this.$t("请先选择一行"), type: "warning" });
        //   return;
        // }
        // if (selectRows.find((i) => i.status == 1)) {
        //   this.$toast({
        //     content: this.$t("请选择已停用状态的数据进行删除操作"),
        //     type: "warning",
        //   });
        //   return;
        // }
        let ids = selectRows.map((item) => item.id)
        console.log(ids)
        this.handleDelete(ids)
      } else if (e.toolbar.id == 'Add') {
        this.addRow()
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'Edit') {
        this.handleRow(e.data)
      } else if (e.tool.id == 'Delete') {
        this.handleDelete([e.data.id])
      } else if (['active', 'inActive'].includes(e.tool.id)) {
        let _toStatus = e.data.status ? 0 : 1
        this.handleActive(e.data.id, _toStatus)
      }
    },

    handleRow(row) {
      this.$dialog({
        modal: () => import('./components/addDialog.vue'),
        data: {
          title: this.$t('编辑'),
          row: row || null,
          commonFlag: this.commonFlag === true ? 1 : 0
        },
        success: () => {
          this.refreshGrid()
        }
      })
    },
    addRow(row) {
      this.$dialog({
        modal: () => import('./components/addDialog.vue'),
        data: {
          title: this.$t('新增'),
          row: row || null,
          commonFlag: this.commonFlag === true ? 1 : 0
        },
        success: () => {
          this.refreshGrid()
        }
      })
    },
    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('确认删除选中的行？')
        },
        success: () => {
          this.$API.receiptAndDelivery.barcodeAutoCreateConfigDelete(ids).then(() => {
            this.refreshGrid()
          })
        }
      })
    },

    // 启用禁用
    handleActive(id, toStatus) {
      this.$API.outsourcing.activeStockSetting({ id: id, status: toStatus }).then(() => {
        this.refreshGrid()
      })
    },

    refreshGrid() {
      this.$toast({ content: this.$t('操作成功'), type: 'success' })
      this.$refs.pickGridRef.refreshCurrentGridData()
    }
  }
}
</script>

<style></style>
