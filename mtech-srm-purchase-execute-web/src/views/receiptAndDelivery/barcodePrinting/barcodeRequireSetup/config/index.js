import { i18n } from '@/main.js'
import Vue from 'vue'
export const columnData = [
  {
    width: '100',
    field: 'indexs',
    headerText: i18n.t('序号'),
    showInColumnChooser: false,
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return Number(+row.index + 1)
      }
    },
    cellTools: [
      {
        id: 'Edit',
        icon: 'icon_list_edit',
        permission: ['O_02_0675'],
        title: i18n.t('编辑')
        // visibleCondition: (e) => e.status == 0,
      },
      {
        id: 'Delete',
        icon: 'icon_solid_Delete',
        permission: ['O_02_0674'],
        title: i18n.t('删除')
        // visibleCondition: (e) => e.status == 0,
      }
    ]
  },
  {
    width: '110',
    field: 'companyName',
    headerText: i18n.t('客户'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.companyCode}}-{{data.companyName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '180',
    field: 'siteName',
    headerText: i18n.t('工厂'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '150',
    field: 'autoCreateFlag', // 1开启 0关闭
    headerText: i18n.t('送货自动生成需求'),
    valueConverter: {
      type: 'map',
      map: [
        {
          value: 0,
          text: i18n.t('关闭'),
          cssClass: 'col-inactive'
        },
        {
          value: 1,
          text: i18n.t('开启'),
          cssClass: 'col-active'
        }
      ]
    }
  },
  {
    width: '150',
    field: 'autoCreateVmiFlag', // 1开启 0关闭
    headerText: i18n.t('vmi自动生成需求'),
    valueConverter: {
      type: 'map',
      map: [
        {
          value: 0,
          text: i18n.t('关闭'),
          cssClass: 'col-inactive'
        },
        {
          value: 1,
          text: i18n.t('开启'),
          cssClass: 'col-active'
        }
      ]
    }
  },
  {
    width: '120',
    field: 'updateUserName',
    headerText: i18n.t('最后更新人'),
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return row.updateUserName || row.createUserName
      }
    }
  },
  {
    width: '150',
    field: 'updateTime',
    headerText: i18n.t('最后更新时间'),
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return row.updateTime || row.createTime
      }
    }
  }
]
