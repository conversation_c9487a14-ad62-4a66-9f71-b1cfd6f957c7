import { i18n } from '@/main.js'

// 序号 表格行 按钮
export const SerialNumberCellTools = [
  {
    id: 'ConfigEdit',
    icon: 'icon_list_edit',
    title: i18n.t('编辑')
  },
  {
    id: 'ConfigDelete',
    icon: 'icon_solid_Delete',
    title: i18n.t('删除')
  }
]

// Toolbar 按钮
export const Toolbar = [
  {
    id: 'ConfigAdd',
    icon: 'icon_solid_Createproject',
    title: i18n.t('新增')
  },
  {
    id: 'ConfigDelete',
    icon: 'icon_table_delete',
    title: i18n.t('删除')
  }
]

// 表格列数据
export const ColumnData = [
  {
    fieldCode: 'serialNumber', // 前端定义
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'status', // 停用/启用 启用状态:1-未启用,2-启用
    fieldName: i18n.t('客户')
  },
  {
    fieldCode: 'companyName', // 客户公司名称
    fieldName: i18n.t('工厂')
    // companyId 客户公司名称id
    // companyCode 客户公司编码
  },
  {
    fieldCode: 'name', // 司机姓名
    fieldName: i18n.t('司机姓名')
  },
  {
    fieldCode: 'idCard', // 司机身份证号
    fieldName: i18n.t('最后更新人')
  },
  {
    fieldCode: 'contact', // 联系方式
    fieldName: i18n.t('最后更新日期')
  }
]

// 弹出框类型
export const DialogActionType = {
  Add: 0, // 新增
  Edit: 1 // 编辑
}

// 配置状态 1:启用 2:停用
export const ConfigStatus = {
  active: 1, // 启用
  inactive: 2 // 停用
}
// 配置状态 1:启用 2:停用
export const ConfigStatusConst = {
  [ConfigStatus.active]: i18n.t('启用'),
  [ConfigStatus.inactive]: i18n.t('停用')
}
// 配置状态 对应的 css class
export const ConfigStatusCssClass = {
  [ConfigStatus.active]: 'col-active', // 启用
  [ConfigStatus.inactive]: 'col-inactive' // 停用
}
// 配置状态 Options
export const ConfigStatusOptions = [
  {
    // 启用
    text: ConfigStatusConst[ConfigStatus.active],
    value: ConfigStatus.active,
    cssClass: ConfigStatusCssClass[ConfigStatus.active]
  },
  {
    // 停用
    text: ConfigStatusConst[ConfigStatus.inactive],
    value: ConfigStatus.inactive,
    cssClass: ConfigStatusCssClass[ConfigStatus.inactive]
  }
]
