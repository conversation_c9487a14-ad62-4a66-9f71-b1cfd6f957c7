<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="middle-blank"></div>
      <!-- 右侧各种操作按钮 -->

      <!-- <mt-button css-class="e-flat" :is-primary="true" @click="submit">{{
        $t("保存")
      }}</mt-button> -->
      <!-- <mt-button css-class="e-flat" :is-primary="true" @click="resoveClick">{{
        $t("提交")
      }}</mt-button> -->
      <div class="sort-box" @click="expandChange">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="topInfo" :rules="rules" :validate-on-rule-change="false">
        <div class="accordion-main">
          <div class="left-status">
            <span class="titles">{{ $t('默认配置：') }}</span>
          </div>
          <mt-form-item class="bot-status" :label="$t('送货自动生成条码需求')">
            <mt-switch
              v-permission="['O_02_0672']"
              @change="switchChange"
              v-model="commonFlagOne.type"
            ></mt-switch>
          </mt-form-item>
          <mt-form-item class="bot-status" :label="$t('VMI入库单自动生成条码需求')">
            <mt-switch
              @change="autoSwitchChange"
              v-model="commonFlagOne.autoCreateVmiFlag"
            ></mt-switch>
          </mt-form-item>
        </div>
      </mt-form>
    </div>
  </div>
</template>

<script>
// import { Query } from "@syncfusion/ej2-data";
// import { uniqWith, isEqual, filter } from "lodash";

// import { maxPageSize } from "@/utils/constant";

export default {
  components: {},
  props: {
    commonFlagOne: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      radioData: [
        {
          label: this.$t('否'),
          value: '0'
        },
        {
          label: this.$t('是'),
          value: '1'
        }
      ],
      cancelData: [
        {
          label: this.$t('良品退货'),
          value: '0'
        },
        {
          label: this.$t('不良品退货'),
          value: '1'
        }
      ],
      cancelList: [],
      materialList: [],
      customerEnterpriseId: '',
      dataArr0: [],
      addForm: {},
      radioData1: [
        {
          label: this.$t('是'),
          value: '1'
        },
        {
          label: this.$t('否'),
          value: '0'
        }
      ],
      orderList: [],
      isExpand: true,
      rules: {
        siteCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        itemCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        orderCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        buyerEnterpriseId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        isOutDirect: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        isOutSale: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        warehouseCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        cancelType: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }]
      },
      expedited: '1', //待领料明细or手工创建
      dataArr1: [],
      dataArr2: [],
      userInfo: JSON.parse(sessionStorage.getItem('userInfo')),
      siteTenantList: []
    }
  },

  mounted() {
    // this.purOrderQueryOrder();
    // this.init();
    // this.topInfo.supplierCode = this.userInfo.enterpriseCode;
    // this.topInfo.supplierName = this.userInfo.enterpriseName;
    // this.topInfo.supplierId = this.userInfo.enterpriseId;
    // this.topInfo.supplierCode = this.userInfo.enterpriseCode;
    // this.topInfo.supplierName = this.userInfo.enterpriseName;
    // this.topInfo.buyerCompanyOrgCode = this.userInfo.enterpriseCode;
  },
  methods: {
    switchChange(e) {
      let obj = {
        value: e,
        type: 'autoCreateFlag'
      }
      this.$emit('switchChange', obj)
      if (e === true) {
        this.addForm.commonFlag === 1
      } else {
        this.addForm.commonFlag === 0
      }
    },
    autoSwitchChange(e) {
      let obj = {
        value: e,
        type: 'autoCreateVmiFlag'
      }
      this.$emit('switchChange', obj)
    },
    expandChange() {
      this.isExpand = !this.isExpand
      // this.$refs.ruleForm.clearValidate();
    },
    // init() {
    //   this.topInfo.remark = this.headerInfo.remark;
    // },
    // 退货库存地点

    formFun(rule, value, callback) {
      console.log(rule, value, callback)
    },

    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.bot-status {
  margin-top: 20px;
  /deep/ .mt-form-item-topLabel {
    display: flex;
    height: 30px;
    line-height: 30px;
  }
}

.accordion-main {
  margin-top: 20px;
}
.mr20 {
  margin: 20px 20px 20px 0;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .itemcon {
      display: flex;
      align-items: center;
    }
    .item {
      margin-right: 20px;
    }
    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      // &.more-width {
      //   // width: 450px;
      // }

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
