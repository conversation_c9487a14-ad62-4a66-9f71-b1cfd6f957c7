<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="headerText"
    :buttons="buttons"
    @close="handleClose"
  >
    <mt-form ref="addFormRef" :model="addForm" :rules="addRules">
      <mt-form-item prop="companyCode" :label="$t('公司')">
        <mt-select
          v-model="addForm.companyCode"
          :data-source="companyOptions"
          :show-clear-button="false"
          :fields="{ text: 'labelShow', value: 'orgCode' }"
          :allow-filtering="true"
          :filtering="getCompanyOptions"
          :placeholder="$t('请选择')"
          :popup-width="350"
          @change="companyCodeChange"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="siteCode" :label="$t('工厂')">
        <debounce-filter-select
          v-model="addForm.siteCode"
          :request="postSiteFuzzyQuery"
          :data-source="siteOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'siteCode' }"
          :value-template="siteCodeValueTemplate"
          @change="siteCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="stockStatus" :label="$t('送货自动生成条码需求')">
        <mt-switch v-model="addForm.autoCreateFlag"></mt-switch>
      </mt-form-item>
      <mt-form-item prop="stockStatus" :label="$t('VMI入库单自动生成条码需求')">
        <mt-switch v-model="addForm.autoCreateVmiFlag"></mt-switch>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import { utils } from '@mtech-common/utils'

export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      headerText: this.$t('新增'),
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      companyOptions: [],
      siteOptions: [],
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }),
      addForm: {
        abolished: '',
        autoCreateFlag: '',
        autoCreateVmiFlag: '',
        commonFlag: '',
        companyCode: '',
        companyId: '',
        companyName: '',
        id: '',
        siteCode: '',
        siteId: '',
        siteName: '',
        tenantId: ''
      },
      addRules: {
        companyCode: [
          {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          }
        ],
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.getCompanyOptions({ text: '' })
    this.postSiteFuzzyQuery({ text: '' })
    this.getCompanyOptions = utils.debounce(this.getCompanyOptions, 300)
    this.$nextTick(() => {
      this.$refs.addFormRef.resetFields()
      if (this.modalData) {
        this.addForm.commonFlag = 0
        this.headerText = this.modalData?.title
        if (this.modalData?.row) {
          this.addForm = {
            ...this.modalData.row,
            autoCreateFlag: this.modalData.row.autoCreateFlag ? true : false,
            autoCreateVmiFlag: this.modalData.row.autoCreateVmiFlag ? true : false
          }
        }
      }
    })
  },
  methods: {
    getCompanyOptions(e = { text: '' }) {
      this.$API.masterData
        .getCompanyBySup({
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true,
          organizationIds: [],
          fuzzyParam: e.text
        })
        .then((res) => {
          const companyOptions = res.data
          companyOptions.forEach((item) => {
            item.labelShow = item.orgCode + ' - ' + item.orgName
          })
          this.companyOptions = companyOptions

          this.$nextTick(() => {
            if (e.updateData && typeof e.updateData == 'function') {
              e.updateData(companyOptions)
            }
          })
        })
    },
    companyCodeChange(e) {
      this.addForm.companyCode = e.itemData.orgCode
      this.addForm.companyName = e.itemData.orgName
      this.addForm.companyId = e.itemData.id
    },
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        page: {
          current: 1,
          pages: 0,
          size: 20
        }
      }
      this.$API.receiptAndDelivery
        .getFactoryInfo(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    siteCodeChange(e) {
      this.addForm.siteCode = e.itemData.siteCode
      this.addForm.siteName = e.itemData.siteName
      this.addForm.siteId = e.itemData.id
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    cancel() {
      this.$emit('cancel-function')
    },
    confirm() {
      this.$refs.addFormRef.validate((valid) => {
        if (valid) {
          console.log(this.addForm)
          this.addForm.autoCreateVmiFlag = this.addForm.autoCreateVmiFlag === false ? 0 : 1
          this.addForm.autoCreateFlag = this.addForm.autoCreateFlag === false ? 0 : 1

          let params = {
            ...this.addForm
          }

          this.$API.receiptAndDelivery
            .barcodeAutoCreateConfigSave(params)
            .then(() => {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.$refs.dialog.ejsRef.hide()

              this.$emit('confirm-function')
            })
            .catch(() => {
              this.$refs.dialog.ejsRef.hide()
            })
        }
      })
    }
  }
}
</script>
