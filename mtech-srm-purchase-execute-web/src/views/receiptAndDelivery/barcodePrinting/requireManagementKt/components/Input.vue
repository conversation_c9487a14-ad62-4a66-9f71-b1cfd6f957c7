<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="isDisabled"
    ></mt-input>
  </div>
</template>
<script>
export default {
  data() {
    return { data: {}, isDisabled: false }
  },
  mounted() {
    if (
      this.data.column.field === 'scheduleArea' ||
      this.data.column.field === 'associatedNumber' ||
      this.data.column.field === 'bom' ||
      this.data.column.field === 'saleOrder' ||
      this.data.column.field === 'saleOrderRowCode' ||
      this.data.column.field === 'processName' ||
      this.data.column.field === 'productCode' ||
      this.data.column.field === 'buyerOrder' ||
      this.data.column.field === 'buyerOrderRowCode' ||
      this.data.column.field === 'projectTextBatch' ||
      this.data.column.field === 'warehouseKeeper'
    ) {
      //计划区域 关联工单号
      this.setDisabled()
    }
    if (this.data.column.field === 'supplierItemCode') {
      this.$bus.$on('supplierItemCodeChange', (val) => {
        this.data.supplierItemCode = val
      }) //供应商物料编码
    }
    if (this.data.column.field === 'supplierItemName') {
      this.$bus.$on('supplierItemNameChange', (val) => {
        this.data.supplierItemName = val
      }) //供应商物料名称
    }
    if (this.data.column.field === 'supplierLineBody') {
      this.$bus.$on('supplierLineBodyChange', (val) => {
        this.data.supplierLineBody = val
      }) //供应商线体
    }
    if (this.data.column.field === 'lineBody') {
      this.$bus.$on('deliveryCodeChange', (val) => {
        this.data.lineBody = val.productLine || ''
      })
    }
  },
  methods: {
    setDisabled() {
      if (this.data.isEntry === '1') {
        this.isDisabled = true
      }
      if (this.data.isEntry === '2') {
        this.isDisabled = false
      }
    }
  }
}
</script>
