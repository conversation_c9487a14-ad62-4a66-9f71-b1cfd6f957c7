<template>
  <div>
    <debounce-filter-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :request="postChange"
      v-if="type === '1'"
      :data-source="dataSource"
      :value-template="valueTemplate"
      :fields="{ text: 'theCodeName', value: 'value' }"
      :placeholder="placeholder"
      :popup-width="400"
      @change="selectChange"
      :open-dispatch-change="true"
      :disabled="isDisabled"
      :allow-filtering="true"
    ></debounce-filter-select>
    <mt-input
      v-else
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="true"
    ></mt-input>
  </div>
</template>
<script>
// import { utils } from "@mtech-common/utils";

import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import { uniqWith, isEqual, filter } from 'lodash'

export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      data: {},
      type: '1',
      placeholder: this.$t('请选择'),
      valueTemplate: null,
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      purchase: '', //采购组code
      isDisabled: false,
      itemCode: '', //物料code
      companyCode: '',
      siteCode: '', //工厂code
      barcodeLevel: '', //条码层级
      supplierCode: '', //供应商code
      tenantId: '', //采方租户id
      siteId: '',
      customerEnterpriseId: '',
      orderType: null
    }
  },
  mounted() {
    this.supplierCode = this.data.supplierCode
    this.itemCode = this.data.itemCode
    this.siteCode = this.data.siteCode
    this.orderType = this.data.orderType
    this.barcodeLevel = this.data.barcodeLevel
    this.dataSource = this.data.column.selectOptions
    if (this.data.column.field === 'companyName') {
      this.getCompany()
    }
    if (this.data.column.field === 'barcodeLevel') {
      this.getBarcodeLevel()
    }
    if (this.data.column.field === 'orderType') {
      this.getOrderType()
    }
    if (this.data.column.field === 'itemCode') {
      this.$bus.$on('itemCodeChange', (val, id) => {
        console.log(val, id)
        if (id === '2') {
          this.type = '2'
        }
        this.data.itemCode = val
      })
      this.$bus.$on('supplierCode', (val) => {
        this.supplierCode = val
      })
      // this.$bus.$on("siteId", (val) => {
      //   this.siteId = val;
      // });

      this.$bus.$on('customerEnterpriseId', (val) => {
        this.customerEnterpriseId = val
      })

      this.$bus.$on('siteCode', (val) => {
        this.siteCode = val
        if (this.data.itemCode) {
          this.getCategoryItem(this.data.itemCode, val)
        } else {
          this.getCategoryItem('', val)
        }
      })
      // this.getBusinessGroupTypeListByDictCode();
      this.$bus.$on('deliveryCodeChange', (val) => {
        if (val.itemCode) {
          this.data.itemCode = val.itemCode

          // this.isDisabled = true;
          this.$bus.$emit('itemCodeChange', val.itemCode)
          this.barcodePrintConfigQueryOne(val.itemCode, '1')
        }
      })
      if (this.data.deliveryCode) {
        //如果有送货单号
        // this.isDisabled = true;
      }
      this.$bus.$on('tenantIdChange', (val) => {
        //接受采方租户id 去获取采购物料信息
        this.tenantId = val
      })
    }
    if (this.data.column.field === 'siteName') {
      this.valueTemplate = codeNameColumn({
        firstKey: 'siteOrgCode',
        secondKey: 'siteOrgName'
      })
      //工厂下拉old
      // this.$bus.$on("customerEnterpriseId", (val) => {
      //   this.customerEnterpriseId = val;
      //   this.findOrgSiteInfo("");
      // });
      // new
      this.$bus.$on('companyCodeChange', (val) => {
        this.findOrgSiteInfo(val)
      })
    }
    if (this.data.column.field === 'supplierCode') {
      this.valueTemplate = null
      this.$bus.$on('companyCodeChange', (val) => {
        this.companyCode = val
      })
      this.$bus.$on('siteCodeChange', (val) => {
        this.siteCode = val
        if (this.data.supplierCode) {
          this.getQuerySuppliers(this.data.supplierCode, val)
        } else {
          this.getQuerySuppliers('', val)
        }
      })
    }

    if (this.data.column.field === 'deliveryCode') {
      //送货单号
      this.$bus.$on('supplierNameChange', (val) => {
        this.supplierCode = val
        // this.getDeliveryCode(val);
      })
      this.$bus.$on('siteName', (val) => {
        this.siteCode = val
        // this.getDeliveryCode(val);
      })
      this.$bus.$on('orderTypeChange', (val) => {
        this.data.orderType = val.orderType
        this.requestOrderList(val)
      })
    }

    if (this.data.column.field === 'deliveryLineNo') {
      //送货单行号
      this.$bus.$on('deliveryLineNoClick', (val) => {
        this.requestLineNo(val)
      })
    }
    if (this.data.column.field === 'domesticDemandFlag') {
      this.getDomesticDemandFlag()
    }
    this.$bus.$on('siteCodeChange', (val) => {
      this.siteCode = val
    })
    this.$bus.$on('itemCodeChange', (val) => {
      this.itemCode = val
    })
    this.$bus.$on('barcodeLevelChange', (val) => {
      this.barcodeLevel = val
    })
    this.$bus.$on('supplierNameChange', (val) => {
      this.supplierCode = val
    })
    this.$bus.$on('orderTypeChange', (val) => {
      this.orderType = val.orderType
    })
    this.$bus.$on('domesticDemandFlagChange', (val) => {
      this.data.domesticDemandFlag = val
    }) //是否内需跟单
  },

  methods: {
    getCompany() {
      // let obj = {
      //   fuzzyNameOrCode: "",
      // };
      this.$API.masterData.getComListSupplier().then((res) => {
        res.data.forEach((item) => {
          item.theCodeName = item.itemCode + `-` + item.itemName // 	客户名称
          item.value = item.itemName // 	客户名称
        })
        this.dataSource = res.data || []
        console.log(this.dataSource)
        console.log(this.data)
      })
      // this.data.companyName = this.data.customerCode;
      // let obj = {
      //   fuzzyNameOrCode: "",
      // };
      // console.log(this.data);

      // this.$API.masterData.getCustomer(obj).then((res) => {
      //   res.data.forEach((item) => {
      //     item.theCodeName = item.customerName; // 	客户名称
      //     item.value = item.customerName; // 	客户名称
      //   });
      //   this.dataSource = res.data || [];
      //   console.log(this.dataSource);
      //   console.log(this.data);
      // });
    },
    // 供应商
    getQuerySuppliers(e) {
      let obj = {
        defaultRules: [
          {
            field: 'companyCode',
            operator: 'equal',
            value: this.companyCode
          },
          {
            field: 'siteCode',
            operator: 'equal',
            value: this.siteCode
          },
          {
            field: 'codeLike',
            operator: 'equal',
            value: e
          }
        ]
        // codeLike: "123",
      }
      this.$API.vmi.getQuerySuppliers(obj).then((res) => {
        const list = res?.data || []
        if (res.data.length === 1) {
          this.isDisabled = true
          this.barcodePrintConfigQueryOne(res.data[0].supplierCode, '4')
          this.$bus.$emit('supplierNameChange', res.data[0].supplierCode)
          this.data.supplierCode = res.data[0].supplierCode
          this.$parent.$emit('selectedChanged', {
            fieldCode: 'deliveryLineNo',
            itemInfo: {
              supplierName: res.data[0].supplierName,
              supplierCode: res.data[0].supplierCode
            }
          })
        }
        list.forEach((item) => {
          // item.theCodeName = item.itemCode;
          // item.value = item.itemCode;
          item.theCodeName = item.supplierName //
          item.value = item.supplierCode //
        })
        // this.dataSource = list;
        // res.data.forEach((item) => {
        //   item.theCodeName = item.supplierName; //
        //   item.value = item.supplierCode; //
        // });

        this.dataSource = list || []
        console.log(this.dataSource)
        console.log('3232', this.data)
      })
    },
    // 模糊
    postChange(e) {
      if (this.data.column.field === 'itemCode') {
        this.getCategoryItem(e.text, this.siteCode)
      }
      if (this.data.column.field === 'supplierCode') {
        this.getQuerySuppliers(e.text, this.supplierCode)
      }
      // if (this.data.column.field === "siteName") {
      //   this.findOrgSiteInfo(e);
      // }
    },
    // 获取主单数据
    requestOrderList(val) {
      this.dataSource = []
      // 0: 送货单 1: 入库单 2: 物料调拨单
      switch (val.orderType) {
        case 0:
          this.getDeliveryCode()
          break
        case 1:
          this.getReceiveOrder()
          break
        case 2:
          this.getMaterialOrder()
          break
      }
    },
    getDeliveryCode() {
      //获取送货单信息
      let params = {
        page: {
          current: 1,
          size: 50
        },
        defaultRules: [
          {
            field: 'supplierCode',
            operator: 'equal',
            value: this.supplierCode
          },
          {
            field: 'siteCode',
            operator: 'equal',
            value: this.siteCode
          },
          {
            field: 'status',
            operator: 'notequal',
            value: 4
          },
          {
            field: 'status',
            operator: 'notequal',
            value: 5
          }
        ]
      }
      this.$API.receiptAndDelivery
        .supplierOrderDeliveryItemPage(params) //  供方
        .then((res) => {
          res.data.records = res.data.records.map((item) => {
            return {
              deliveryCode: item.deliveryCode,
              theCodeName: item.deliveryCode,
              value: item.deliveryCode,
              productLine: item.productLine,
              id: item.id
            }
          })
          console.log()
          res.data.records = uniqWith(
            filter(res.data.records, (item) => item.deliveryCode), // 过滤
            isEqual
          ) // 去重

          this.dataSource = res.data?.records
          // this.dataSource = [
          //   {
          //     name: "1",
          //     value: "1",
          //     deliveryCode: "1",
          //   },
          // ];
        })
    },
    // 获取物料替换单
    getMaterialOrder() {
      let params = {
        page: {
          current: 1,
          size: 50
        },
        defaultRules: [
          {
            field: 'supplierCode',
            operator: 'equal',
            value: this.supplierCode
          },
          {
            field: 'siteCode',
            operator: 'equal',
            value: this.siteCode
          },
          {
            field: 'vmiOrderType',
            operator: 'equal',
            value: '6'
          },
          {
            field: 'status',
            operator: 'notequal',
            value: '9'
          }
        ]
      }
      this.$API.receiptAndDelivery.getVmiAllocationOrder(params).then((res) => {
        res.data.records = res.data.records.map((item) => {
          return {
            deliveryCode: item.vmiOrderCode,
            theCodeName: item.vmiOrderCode,
            value: item.vmiOrderCode,
            id: item.id
          }
        })

        this.dataSource = res.data?.records
      })
    },
    // 获取入库单
    getReceiveOrder() {
      let params = {
        page: {
          current: 1,
          size: 50
        },
        defaultRules: [
          {
            field: 'supplierCode',
            operator: 'equal',
            value: this.supplierCode
          },
          {
            field: 'siteCode',
            operator: 'equal',
            value: this.siteCode
          },
          {
            field: 'status',
            operator: 'notequal',
            value: '9'
          }
        ]
      }
      this.$API.receiptAndDelivery.getReceiveOrder(params).then((res) => {
        res.data.records = res.data.records.map((item) => {
          return {
            deliveryCode: item.vmiOrderCode,
            theCodeName: item.vmiOrderCode,
            value: item.vmiOrderCode,
            id: item.id
          }
        })

        this.dataSource = res.data?.records
      })
    },
    requestLineNo(val) {
      this.dataSource = []
      // 0: 送货单 1: 入库单 2: 物料调拨单
      switch (this.orderType) {
        case 0:
          this.getDeliveryLine(val)
          break
        case 1:
          this.getReceiveLine(val.id)
          break
        case 2:
          this.getMaterialLine(val.id)
          break
      }
    },
    getDeliveryLine(val) {
      let params = {
        page: {
          current: 1,
          size: 50
        },
        defaultRules: [
          {
            field: 'deliveryCode',
            operator: 'equal',
            value: val.deliveryCode
          }
        ]
      }
      this.$API.receiptAndDelivery
        .supplierOrderDeliveryItemPage(params) //  供方
        .then((res) => {
          console.log(res.data.records.length)
          res.data.records.forEach((item) => {
            item.theCodeName = item.deliveryLineNo // 	行号
            item.value = item.deliveryLineNo // 	行号
          })
          this.dataSource = res.data?.records

          this.data.deliveryLineNo = Number(this.data.deliveryLineNo)
        })
    },
    getReceiveLine(val) {
      let params = {
        id: val
      }
      this.$API.receiptAndDelivery
        .getReceiveOrderDetail(params) //  供方
        .then((res) => {
          res.data.vmiOrderItemResponses.forEach((item) => {
            item.theCodeName = item.rowNum // 	行号
            item.value = item.rowNum // 	行号
          })
          this.dataSource = res.data?.vmiOrderItemResponses

          this.data.deliveryLineNo = Number(this.data.deliveryLineNo)
        })
    },
    getMaterialLine(val) {
      let params = {
        id: val
      }
      this.$API.receiptAndDelivery
        .getVmiAllocationOrderDetail(params) //  供方
        .then((res) => {
          res.data.vmiOrderItemResponses.forEach((item) => {
            item.theCodeName = item.rowNum // 	行号
            item.value = item.rowNum // 	行号
          })
          this.dataSource = res.data?.vmiOrderItemResponses

          this.data.deliveryLineNo = Number(this.data.deliveryLineNo)
        })
    },
    barcodePrintConfigQueryOne(val, type) {
      //根据物料+工厂+条码层级+供应商查找配置
      let params = {
        siteCode: type === '2' ? val : this.siteCode,
        // supplierCode: this.supplierCode,
        supplierCode: type === '4' ? val : this.supplierCode,

        itemCode: type === '1' ? val : this.itemCode,
        barcodeLevel: type === '3' ? val : this.barcodeLevel
      }
      console.log(params, '供应商配置')
      if (
        !(
          params.siteCode !== null &&
          params.siteCode !== undefined &&
          params.siteCode !== '' &&
          params.supplierCode !== null &&
          params.supplierCode !== undefined &&
          params.supplierCode !== '' &&
          params.itemCode &&
          params.barcodeLevel
        )
      ) {
        //四个条件都有
        return
      }
      this.$API.receiptAndDelivery.barcodePrintConfigQueryOne(params).then((res) => {
        if (res.data) {
          this.$bus.$emit('supplierItemCodeChange', res.data.supplierItemCode)
          this.$bus.$emit('supplierItemNameChange', res.data.supplierItemName)
          this.$bus.$emit('supplierLineBodyChange', res.data.supplierLineBody)
          this.$bus.$emit('packingQuantityChange', res.data.packingQuantity)
          this.$bus.$emit('packingQuantityChange1', res.data.packingQuantity)
          this.$bus.$emit('grossWeightChange', res.data.grossWeight)
          this.$bus.$emit('grossWeightChange1', res.data.grossWeight)
          this.$bus.$emit('netWeightChange', res.data.netWeight)
          this.$bus.$emit('netWeightChange1', res.data.netWeight)
          this.$bus.$emit('lengthChange', res.data.length)
          this.$bus.$emit('widthChange', res.data.width)
          this.$bus.$emit('heightChange', res.data.height)
        }
      })
    },

    getBarcodeLevel() {
      this.dataSource = [
        { theCodeName: this.$t('一级'), value: 1 },
        { theCodeName: this.$t('二级'), value: 2 },
        { theCodeName: this.$t('三级'), value: 3 },
        { theCodeName: this.$t('四级'), value: 4 }
      ]
    },
    getOrderType() {
      this.dataSource = [
        { theCodeName: this.$t('送货单'), value: 0 },
        { theCodeName: this.$t('入库单'), value: 1 },
        { theCodeName: this.$t('物料替换单'), value: 2 }
      ]
    },
    getDomesticDemandFlag() {
      this.dataSource = [
        { theCodeName: this.$t('是'), value: 'E' },
        { theCodeName: this.$t('否'), value: 'F' }
      ]
    },
    findOrgSiteInfo(val) {
      let obj = {
        paramObj: val
      }
      this.$API.masterData.getSiteListSupplier(obj).then((res) => {
        const list = res?.data || []
        list.forEach((item) => {
          ;(item.siteOrgCode = item.siteCode), (item.siteOrgName = item.siteName)
        })
        list.forEach((item) => {
          item.value = item.siteOrgName
          // item.theCodeName = item.siteName;
          // item.value = item.siteName;
        })
        this.dataSource = addCodeNameKeyInList({
          firstKey: 'siteOrgCode',
          secondKey: 'siteOrgName',
          list
        })
      })
    },
    getCategoryItem(val, code) {
      //物料下拉
      // const { text, updateData } = args;  old
      // let obj = {
      //   buyerEnterpriseId: this.customerEnterpriseId,
      //   siteCode: this.siteCode,
      //   fuzzyParam: text,
      //   requireItemDetail: true,
      // };
      // this.$API.masterData.postSourceItemnVendorava(obj).then((res) => {
      //   const list = res?.data || [];
      //   list.forEach((item) => {
      //     item.theCodeName = item.itemCode;
      //     item.value = item.itemCode;
      //   });
      //   this.dataSource = list;
      //   if (updateData) {
      //     this.$nextTick(() => {
      //       updateData(this.dataSource);
      //     });
      //   }
      // });
      let obj = {
        siteCode: code,

        fuzzyNameOrCode: {
          fuzzyNameOrCode: val
        }
      }
      this.$API.masterData.getSiteItemFuzzyQuerySupplier(obj).then((res) => {
        const list = res?.data || []
        list.forEach((item) => {
          item.theCodeName = item.itemCode
          item.value = item.itemCode
        })
        this.dataSource = list
        // if (updateData) {
        //   this.$nextTick(() => {
        //     updateData(this.dataSource);
        //   });
        // }
      })
    },
    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      console.log(this.data.column.field)
      if (this.data.column.field === 'companyName') {
        this.$bus.$emit('customerEnterpriseId', val.itemData.customerEnterpriseId)
        this.$bus.$emit('companyCodeChange', val.itemData.itemCode)

        // this.$API.masterData
        //   .supplierFindInBuyingByCustomerCode({
        //     customerCode: val.itemData.customerCode,
        //   })
        //   .then((res) => {
        // this.$bus.$emit("supplierNameChange", res.data.supplierName);
        // this.$bus.$emit("supplierCode", res.data.supplierCode);
        // sessionStorage.setItem(
        //   "customerEnterpriseId",
        //   val.itemData.customerEnterpriseId
        // );
        this.$parent.$emit('selectedChanged', {
          //传出额外数据
          fieldCode: 'companyName',
          itemInfo: {
            companyName: val.itemData.itemName,
            // companyId: val.itemData.companyOrgId,
            // purTenantId: val.itemData.customerTenantId,
            companyCode: val.itemData.itemCode
            // customerEnterpriseId: val.itemData.customerEnterpriseId,
            // supplierName: res.data.supplierName,
            // supplierCode: res.data.supplierCode,
          }
        })
        // });
      }
      if (this.data.column.field === 'itemCode') {
        //物料下拉

        this.$bus.$emit('itemNameChange', val.itemData.itemName) //传给物料名称
        this.$bus.$emit('buyerOrgNameChange', val.itemData.purchaseGroupName) //传给采购组

        this.$parent.$emit('selectedChanged', {
          //传出额外数据
          fieldCode: 'itemCode',
          itemInfo: {
            itemCode: val.itemData.itemCode,
            itemName: val.itemData.itemName,
            itemId: val.itemData.id
          }
        })
        this.$bus.$emit('itemCodeChange', val.itemData.itemCode)
        this.barcodePrintConfigQueryOne(val.itemData.itemCode, '1')
      }
      if (this.data.column.field === 'siteName') {
        // this.$bus.$emit("siteId", val.itemData.siteOrgId);
        this.$bus.$emit('siteName', val.itemData.siteOrgName)
        // this.$bus.$emit("companyNameChanged", val.itemData.companyOrgName); old
        this.$bus.$emit('companyNameChanged', val.itemData.parentName)

        this.$bus.$emit('siteCode', val.itemData.siteOrgCode)
        this.$parent.$emit('selectedChanged', {
          //传出额外数据工厂
          fieldCode: 'siteName',
          itemInfo: {
            siteName: val.itemData.siteOrgName,
            siteCode: val.itemData.siteOrgCode,

            siteId: val.itemData.id
          }
        })
        this.$bus.$emit('siteCodeChange', val.itemData.siteOrgCode)
        this.$bus.$emit('tenantIdChange', val.itemData.tenantId)
        console.log(this.data)
        console.log(val.itemData, val.companyOrgCode)

        // this.logisticQuerySuppliers(
        //   val.itemData.siteOrgCode,
        //   val.itemData.companyOrgCode
        // );
        this.barcodePrintConfigQueryOne(val.itemData.siteOrgCode, '2')
      }
      if (this.data.column.field === 'barcodeLevel') {
        //条码层级
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'barcodeLevel',
          itemInfo: {
            barcodeLevel: val.itemData.value
          }
        })
        this.$bus.$emit('barcodeLevelChange', val.itemData.value)
        this.barcodePrintConfigQueryOne(val.itemData.value, '3')
      }
      if (this.data.column.field === 'deliveryCode') {
        //送货单号
        this.$bus.$emit('deliveryLineNoClick', {
          deliveryCode: val.itemData.deliveryCode,
          productLine: val.itemData.productLine,
          orderType: val.itemData.orderType,
          id: val.itemData.id
        })
      }
      if (this.data.column.field === 'deliveryLineNo') {
        let _deliveryCode =
          this.orderType === 0 ? val.itemData.deliveryCode : val.itemData.vmiOrderCode
        let _deliveryQuantity =
          this.orderType === 0 ? val.itemData.deliveryQuantity : val.itemData.count
        let _deliveryLineNo =
          this.orderType === 0 ? val.itemData.deliveryLineNo : val.itemData.rowNum
        let _buyerOrgName =
          this.orderType === 0 ? val.itemData.buyerOrgName : val.itemData.purchaseGroupName

        this.$bus.$emit('deliveryCodeChange', {
          deliveryCode: _deliveryCode,
          productLine: val.itemData.productLine
        })

        this.$bus.$emit(
          //传给送货总数量
          'deliveryQuantityChange',
          _deliveryQuantity
        )
        this.$bus.$emit('buyerOrgNameChange', _buyerOrgName)
        this.$bus.$emit('itemCodeChange', val.itemData.itemCode, '2') //传给物料编码
        this.$bus.$emit('itemNameChange', val.itemData.itemName) //传给物料名称
        this.$bus.$emit('domesticDemandFlagChange', val.itemData.domesticDemandFlag) //传给是否内需跟单
        this.$bus.$emit('domesticDemandCodeChange', val.itemData.domesticDemandCode) //传给内需单号
        this.$bus.$emit('saleOrderNoChange', val.itemData.saleOrderNo) //传给销售订单号
        this.$bus.$emit('saleOrderLineNoChange', val.itemData.saleOrderLineNo) //传给销售订单行号

        this.$parent.$emit('selectedChanged', {
          fieldCode: 'deliveryLineNo',
          itemInfo: {
            deliveryLineNo: _deliveryLineNo,
            deliveryCode: _deliveryCode
          }
        })
      }
      if (this.data.column.field === 'supplierCode') {
        this.barcodePrintConfigQueryOne(val.itemData.supplierCode, '4')
        this.$bus.$emit('supplierNameChange', val.itemData.supplierCode)

        this.$parent.$emit('selectedChanged', {
          fieldCode: 'deliveryLineNo',
          itemInfo: {
            supplierName: val.itemData.supplierName,
            supplierCode: val.itemData.supplierCode
          }
        })
      }
      if (this.data.column.field === 'orderType') {
        this.$bus.$emit('orderTypeChange', {
          orderType: val.itemData.value
        })
      }
    }
    // logisticQuerySuppliers(siteCode, companyCode) {
    //   let params = {
    //     page: {
    //       current: 1,
    //       size: 50,
    //     },
    //     defaultRules: [
    //       {
    //         field: "companyCode",
    //         operator: "equal",
    //         value: companyCode,
    //       },
    //       {
    //         field: "siteCode",
    //         operator: "equal",
    //         value: siteCode,
    //       },
    //     ],
    //   };
    //   this.$API.receiptAndDelivery
    //     .supplierOrderDeliveryItemPage(params) //  供方
    //     .then((res) => {
    //       console.log(res.data.records.length);
    //       res.data.records = res.data.records.map((item) => {
    //         return {
    //           // deliveryCode: item.deliveryCode,
    //           theCodeName: item.supplierName,
    //           value: item.supplierCode,
    //         };
    //       });
    //       console.log();
    //       this.dataSource = res.data?.records;
    //     });
    // },
  },
  beforeDestroy() {
    this.$bus.$off('siteName')
    this.$bus.$off('siteCode')
    this.$bus.$off('companyCodeChange')
    this.$bus.$off('supplierNameChange')
    this.$bus.$off('orderTypeChange')
    this.$bus.$off('customerEnterpriseId')
    this.$bus.$off('packingQuantityChange1')

    this.$bus.$off('deliveryLineNoClick')
  }
}
</script>
