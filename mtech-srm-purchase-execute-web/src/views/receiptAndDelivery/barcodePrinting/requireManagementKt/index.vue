<template>
  <!-- 条码需求管理 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @actionBegin="actionBegin"
      @cellEdit="cellEdit"
      @actionComplete="actionComplete"
      @selectedChanged="selectedChanged"
    ></mt-template-page>
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :is-show-tips="true"
      :request-urls="requestUrls"
      @closeUploadExcel="handleImport(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
    <!-- :permission-obj="permissionObj" -->
  </div>
</template>
<script>
import Vue from 'vue'
import { checkColumn, lastColumn, lastColumn2 } from './config/index'
import { BASE_TENANT } from '@/utils/constant'
import { cloneDeep } from 'lodash'
import * as UTILS from '@/utils/utils'
import dayjs from 'dayjs'
export default {
  components: {
    UploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    return {
      downTemplateParams: {
        pageFlag: false
      }, // 通知配置导入下载模板参数
      uploadParams: {}, // 导入通知配置文件参数

      lastColumn: lastColumn,
      requestUrls: {
        templateUrlPre: 'receiptAndDelivery',
        templateUrl: 'barcodePrintRequestExportTemplate', // 下载模板接口方法名
        uploadUrl: 'barcodePrintRequestBarcodePrintRequestImport' // 上传接口方法名
      },
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'Pending', permissionCode: 'T_02_0014' },
          { dataPermission: 'Processed', permissionCode: 'T_02_0013' }
        ]
      },
      componentConfig: [
        {
          title: this.$t('待生成条码'),
          // dataPermission: "Pending",
          // permissionCode: "T_02_0014",
          toolbar: [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              // permission: ["O_02_0660"],
              title: this.$t('新增')
            },
            {
              id: 'closeEdit',
              icon: 'icon_table_delete',
              // permission: ["O_02_1141"],
              title: this.$t('取消编辑')
            },
            {
              id: 'Delete',
              icon: 'icon_solid_Delete',
              // permission: ["O_02_0661"],
              title: this.$t('删除')
            },
            {
              id: 'Confirm',
              icon: 'icon_solid_Pauseorder',
              // permission: ["O_02_0662"],
              title: this.$t('打印')
            },
            {
              id: 'ConfirmPrint',
              icon: 'icon_solid_Pauseorder',
              // permission: ["O_02_0662"],
              title: this.$t('打印（英）')
            },
            { id: 'Import', icon: 'icon_solid_Import', title: this.$t('导入') },
            {
              id: 'Export1',
              icon: 'icon_solid_Import',
              // permission: ["O_02_0663"],
              title: this.$t('导出')
            }
          ],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          // gridId: this.$tableUUID.receiptAndDelivery.requireManagement.list,

          grid: {
            allowEditing: true, //开启表格编辑操作
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            columnData: checkColumn.concat(lastColumn),
            asyncConfig: {
              url: `${BASE_TENANT}/barcodePrintRequest/query`,
              defaultRules: [
                {
                  // 类型
                  field: 'noPrintQuantity',
                  operator: 'notequal',
                  value: 0
                },
                {
                  field: 'createTime',
                  label: '创建时间',
                  operator: 'between',
                  type: 'string',
                  value: [
                    new Date(dayjs().subtract(15, 'day').format('YYYY-MM-DD 00:00:00')).getTime(),
                    new Date(dayjs().format('YYYY-MM-DD 23:59:59')).getTime()
                  ]
                }
              ],
              serializeList: (list) => {
                list.forEach((item) => {
                  console.log(item)
                  if (item.deliveryDate && item.deliveryDate.length == 13) {
                    item.deliveryDate = new Date(Number(item.deliveryDate))
                  }
                  // if (item.printQuantity !== item.noPrintQuantity) {
                  //   item.printQuantity = item.noPrintQuantity;
                  // }
                  if (item.produceDate && item.produceDate.length == 13) {
                    item.produceDate = new Date(Number(item.produceDate))
                  }
                  item.addId = this.addId++
                  item.isEntry = '1' //是否是带入的数据
                })
                this.currentList = list
                return list
              }
            },
            defaultSearchItem: [
              {
                field: 'createTime',
                headerText: this.$t('创建时间')
              }
            ]
          }
        },
        {
          title: this.$t('已生成条码'),
          // dataPermission: "Processed",
          // permissionCode: "T_02_0013",
          useToolTemplate: false, // 使用预置表格操作按钮(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置（打开筛选、刷新、设置）
          toolbar: [
            {
              id: 'Delete',
              icon: 'icon_solid_Delete',
              // permission: ["O_02_0664"],
              title: this.$t('删除')
            },
            {
              id: 'Confirm',
              icon: 'icon_solid_Pauseorder',
              // permission: ["O_02_0665"],
              title: this.$t('打印')
            },
            {
              id: 'ConfirmPrint',
              icon: 'icon_solid_Pauseorder',
              // permission: ["O_02_0662"],
              title: this.$t('打印（英）')
            },
            {
              id: 'Export1',
              icon: 'icon_solid_Import',
              // permission: ["O_02_0666"],
              title: this.$t('导出')
            },
            {
              id: 'synchronous',
              icon: 'icon_table_restart',
              title: this.$t('同步WMS')
            }
          ],
          gridId: this.$tableUUID.receiptAndDelivery.requireManagement.ProcessedList,
          grid: {
            columnData: checkColumn.concat(lastColumn2),
            asyncConfig: {
              url: `${BASE_TENANT}/barcodePrintRequest/query`,
              defaultRules: [
                {
                  // 类型
                  field: 'noPrintQuantity',
                  operator: 'equal',
                  value: 0
                },
                {
                  field: 'createTime',
                  label: '创建时间',
                  operator: 'between',
                  type: 'string',
                  value: [
                    new Date(dayjs().subtract(15, 'day').format('YYYY-MM-DD 00:00:00')).getTime(),
                    new Date(dayjs().format('YYYY-MM-DD 23:59:59')).getTime()
                  ]
                }
              ],
              serializeList: (list) => {
                list.forEach((item) => {
                  console.log(item)
                  if (item.deliveryDate && item.deliveryDate.length == 13) {
                    item.deliveryDate = new Date(Number(item.deliveryDate))
                  }
                  // if (item.printQuantity !== item.noPrintQuantity) {
                  //   item.printQuantity = item.noPrintQuantity;
                  // }
                  if (item.produceDate && item.produceDate.length == 13) {
                    item.produceDate = new Date(Number(item.produceDate))
                  }
                  item.addId = this.addId++
                  item.isEntry = '1' //是否是带入的数据
                })
                this.currentList = list
                return list
              }
            },
            defaultSearchItem: [
              {
                field: 'createTime',
                headerText: this.$t('创建时间')
              }
            ]
          }
        }
      ],
      currentList: [], //从接口获取到的最新的数据
      nowEditRowFlag: '', //当前编辑的行id
      addId: '1',
      isEdit: '1', //是否编辑 1是编辑 2不是编辑
      selectedOtherInfo: {},
      supplierInfo: {
        supplierName: '',
        supplierCode: '',
        supplierEnName: '',
        supplierId: ''
      } //供应商的信息
    }
  },
  mounted() {
    this.getSupplier()
    // this.supOrderQuery();
  },
  methods: {
    handleImport(flag) {
      //导入
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 上传成功后
    upExcelConfirm() {
      this.handleImport(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
      this.$refs.templateRef.refreshCurrentGridData()
    },
    getSupplier() {
      // 查询供应商的数据
      // const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
      // console.log(userInfo);
      // this.supplierInfo.supplierName = userInfo.enterpriseName;
      // this.supplierInfo.supplierId = userInfo.enterpriseId;
      // this.supplierInfo.supplierCode = userInfo.enterpriseCode;
      this.$API.masterData.getEnterpriseInfo().then((res) => {
        this.supplierInfo.supplierEnName = res.data.enterpriseEnglishName
      })
    },
    selectedChanged(val) {
      console.log(val.itemInfo)
      Object.assign(this.selectedOtherInfo, val.itemInfo)
      console.log(this.selectedOtherInfo, '最新的额外数据')
    },
    endEdit() {
      //让组件失去焦点
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
    },
    actionBegin(args) {
      console.log(args, '我是actionBegin')

      if (args.requestType === 'add') {
        let lastColumn = cloneDeep(this.lastColumn)
        lastColumn.forEach((item) => {
          args.data[item.field] = ''
          if (item.field === 'deliveryDate' || item.field === 'produceDate') {
            args.data[item.field] = new Date()
          }

          if (
            item.field === 'deliveryTotalQuantity' ||
            item.field === 'printQuantity' ||
            item.field === 'packingQuantity' ||
            item.field === 'BIN' ||
            item.field === 'netWeight' ||
            item.field === 'grossWeight' ||
            item.field === 'length' ||
            item.field === 'width' ||
            item.field === 'height'
          ) {
            args.data[item.field] = '0'
          }
          args.data.isEntry = '2'
        })
        // args.data.supplierName = this.supplierInfo.supplierName; //设置供应商信息
        // args.data.supplierCode = this.supplierInfo.supplierCode;
        // args.data.supplierId = this.supplierInfo.supplierId;
        args.data.supplierEnName = this.supplierInfo.supplierEnName

        args.data.addId = this.addId++
        this.nowEditRowFlag = args.data.addId
      }
      if (args.requestType == 'beginEdit') {
        if (args.rowData.sourceType !== undefined) {
          sessionStorage.setItem('sourceType', JSON.stringify(args.rowData.sourceType))
        } else {
          sessionStorage.setItem('sourceType', JSON.stringify(0))
        }

        this.nowEditRowFlag = args.rowData.addId
        args.rowData.printQuantity = args.rowData.noPrintQuantity
        //   item.printQuantity = item.noPrintQuantity;
        // if (!(args.rowData.isEntry == "2")) {
        //   this.$toast({
        //     content: this.$t("此状态不可编辑"),
        //     type: "warning",
        //   });
        //   args.cancel = true;
        // }
      }
      if (args.requestType == 'save') {
        if (Number(args.data.deliveryTotalQuantity) < Number(args.data.packingQuantity)) {
          args.data.packingQuantity = args.data.deliveryTotalQuantity
          args.data.lastQuantity =
            Number(args.data.printQuantity) % Number(args.data.packingQuantity)
          args.rowData.lastQuantity =
            Number(args.data.printQuantity) % Number(args.data.packingQuantity)

          args.rowData.packingQuantity = args.data.deliveryTotalQuantity
        }
      }
    },
    actionComplete(args) {
      console.log(args, '我是actionComplete')
      const { rowIndex } = args

      if (args.requestType === 'beginEdit' || args.requestType === 'add') {
        this.isEdit = '1'
      }
      if (
        args.requestType === 'refresh' ||
        args.requestType === 'save' ||
        args.requestType === 'delete'
      ) {
        this.isEdit = '2'
      }
      if (args.requestType === 'save' && args.action === 'edit') {
        Number(args.data.printQuantity) === 0 || Number(args.data.packingQuantity) === 0
          ? this.saveChange(rowIndex, 1)
          : Number(args.data.printQuantity) > Number(args.data.deliveryTotalQuantity)
          ? this.saveChange(rowIndex, 2)
          : this.saveReject()
        //编辑完成
      }
      if (args.requestType === 'save' && args.action === 'add') {
        // if (

        Number(args.data.printQuantity) === 0 || Number(args.data.packingQuantity) === 0
          ? this.saveChange(rowIndex, 1)
          : Number(args.data.printQuantity) > Number(args.data.deliveryTotalQuantity)
          ? this.saveChange(rowIndex, 2)
          : this.saveResolve()

        //新增完成
      }
    },
    saveResolve() {
      let row = this.getRow()
      this.addRow(row)
    },
    saveReject() {
      let row = this.getRow()
      if (row.isEntry === '2') {
        //新增错误重新编辑
        this.addRow(row)
      }
      if (row.isEntry === '1') {
        this.editRow(row)
      }
    },
    saveChange(rowIndex, id) {
      if (id === 1) {
        this.$toast({
          content: this.$t('本次打印数量和装箱数量不能为0'),
          type: 'error'
        })
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
      } else if (id === 2) {
        this.$toast({
          content: this.$t('本次打印数量不能大于送货总数量'),
          type: 'error'
        })
      }
    },
    getRow() {
      //获取编辑或者新增的数据
      let currentRecords =
        this.$refs.templateRef?.getCurrentUsefulRef().gridRef?.ejsRef.getCurrentViewRecords() || []
      let row = cloneDeep(this.selectedOtherInfo)
      let info = {}
      currentRecords.some((item) => {
        if (item.addId === this.nowEditRowFlag) {
          Object.assign(item, row)
          info = item
        }
      })
      return info
    },
    cellEdit(data) {
      console.log(data)
      data.column = null
      data.produceDate = Number(new Date(data.produceDate))
      data.updateTime = Number(new Date(data.updateTime))
      data.deliveryDate = Number(new Date(data.deliveryDate))
      data.createTime = Number(new Date(data.createTime))

      this.$API.receiptAndDelivery.barcodePrintRequestsave(data).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.updateList()

        // this.selectedOtherInfo = {};
        // this.updateList();
      })
      // .catch(() => {
      //   this.updateList();
      // });
    },
    getParams(row) {
      console.log(row)

      let params = {
        // id: row.id || 0,
        abolished: 0, //是否删除 0-未删除；1-已删除
        associateCode: '', //关联条码id
        barcodeCode: '', //条码code
        barcodeLevel: row.barcodeLevel, //条码层级
        bin: row.bin, //BIN
        supplierEnName: row.supplierEnName,
        buyerOrgCode: row.buyerOrgCode, //采购组code
        buyerOrgId: row.buyerOrgId, //采购组id
        buyerOrgName: row.buyerOrgName, //采购组name
        companyCode: row.companyCode,
        companyId: row.companyId,
        companyName: row.companyName,
        orderType: row.orderType,
        deliveryCode: row.deliveryCode, //送货单号 —> 改为主单号
        deliveryLineNo: row.deliveryLineNo, //送货单 -> 改为主单号

        deliveryDate: row.deliveryDate ? row.deliveryDate.getTime() : 0, //送货日期
        deliveryTotalQuantity: row.deliveryTotalQuantity || 0, //送货总数量
        grossWeight: row.grossWeight || 0, //毛重
        height: row.height || 0, //高
        itemCode: row.itemCode, //物料编码
        itemEnName: row.itemEnName, //物料英文名称
        itemId: row.itemId || 0, //物料id
        itemName: row.itemName, //物料名称
        lastGrossWeight: row.lastGrossWeight || 0, //尾箱毛重
        lastNetWeight: row.lastNetWeight || 0, //尾箱净重
        lastQuantity: row.lastQuantity || 0, //尾数
        length: row.length || 0, //长
        lineBody: row.lineBody || '', //线体
        netWeight: row.netWeight || 0, //净重
        noPrintQuantity: row.noPrintQuantity || 0, //未打印数量
        packingQuantity: row.packingQuantity || 0, //装箱数量
        printQuantity: row.printQuantity || 0, //本次打印数量
        produceBatchCode: row.produceBatchCode || '', //生产批次
        produceDate: row.produceDate ? row.produceDate.getTime() : 0, //生产日期
        remark: row.remark || '', //备注
        siteCode: row.siteCode || '', //工厂code
        siteId: row.siteId || 0, //工厂id
        customerEnterpriseId: row.customerEnterpriseId || '',
        siteName: row.siteName || '', //工厂name
        supplierBatchCode: row.supplierBatchCode, //供方批次
        supplierCode: row.supplierCode || '', //供应商code

        purTenantId: row.purTenantId || '',
        supplierId: row.supplierId || 0, //供应商id
        supplierItemCode: row.supplierItemCode || '', //供应商物料code
        supplierItemId: row.supplierItemId || 0, //供应商物料id
        supplierItemName: row.supplierItemName || '', //供应商物料name
        supplierLineBody: row.supplierLineBody, //供应商线体
        supplierName: row.supplierName || '', //供应商name
        width: row.width || 0, //宽

        domesticDemandFlag: row.domesticDemandFlag || '', //是否内需跟单
        domesticDemandCode: row.domesticDemandCode || '', //内需单号
        saleOrderNo: row.saleOrderNo || '', //销售订单号
        saleOrderLineNo: row.saleOrderLineNo || '' //销售订单行号
      }
      return params
    },
    addRow(row) {
      console.log(row, '新增行数据')
      let params = this.getParams(row)
      console.log(params)
      if (Number(params.printQuantity) === 0) {
        this.$toast({
          content: this.$t('本次打印数量不能为0'),
          type: 'error'
        })

        this.updateList()

        return
      } else if (Number(params.printQuantity) > Number(params.deliveryTotalQuantity)) {
        this.$toast({
          content: this.$t('本次打印数量不能大于送货总数量'),
          type: 'error'
        })
        this.updateList()

        return
      } else {
        // this.updateList();
      }
      // if (params.siteName.length == 0 || params.itemCode.length == 0) {
      //   this.$toast({
      //     content: this.$t("带星号的全为必填字段"),
      //     type: "error",
      //   });
      // } else {
      this.$API.receiptAndDelivery
        .barcodePrintRequestsave(params)
        .then(() => {
          this.$toast({
            content: this.$t('新增条码需求管理操作成功'),
            type: 'success'
          })
          this.selectedOtherInfo = {}
          this.updateList()
        })
        .catch(() => {
          this.updateList()
        })
      // }
    },
    editRow(row) {
      console.log(row, '编辑行数据')
      let params = this.getParams(row)
      params.id = row.id
      if (Number(params.printQuantity) === 0) {
        this.$toast({
          content: this.$t('本次打印数量不能为0'),
          type: 'error'
        })

        this.$refs.templateRef.refreshCurrentGridData()
        return
      } else if (Number(params.printQuantity) > Number(params.deliveryTotalQuantity)) {
        this.$toast({
          content: this.$t('本次打印数量不能大于送货总数量'),
          type: 'error'
        })
        this.$refs.templateRef.refreshCurrentGridData()

        return
      }
      this.$API.receiptAndDelivery
        .barcodePrintRequestsave(params)
        .then(() => {
          this.$toast({
            content: this.$t('编辑条码需求管理操作成功'),
            type: 'success'
          })
          this.selectedOtherInfo = {}

          this.updateList()
        })
        .catch(() => {
          this.updateList()
        })
    },
    handleDelete(row) {
      //删除
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认删除选中的条码需求管理吗?')
        },
        success: () => {
          let ids = row.map((item) => item.id)
          this.$API.receiptAndDelivery.barcodePrintRequestDelete(ids).then(() => {
            this.$toast({
              content: this.$t('删除条码需求管理操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    updateList() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    handleAdd() {
      sessionStorage.setItem('sourceType', JSON.stringify(0))

      //新增
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
    },

    handleExport() {
      console.log(this.$refs.templateRef.getCurrentTabRef())
      if (this.$refs.templateRef.getCurrentTabRef().tabIndex === 0) {
        let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let params = {
          condition: rule.condition || '',
          rules: rule.rules || [],
          page: { current: 1, size: 5000 },
          pageFlag: true,
          defaultRules: [
            {
              // 类型
              field: 'noPrintQuantity',
              operator: 'notequal',
              value: 0
            }
          ]
        }
        this.$store.commit('startLoading')
        this.$API.receiptAndDelivery.barcodePrintRequestExport(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = UTILS.getHeadersFileName(res)
          UTILS.download({ fileName: `${fileName}`, blob: res.data })
        })
      } else {
        let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let params = {
          condition: rule.condition || '',
          rules: rule.rules || [],
          page: { current: 1, size: 5000 },
          pageFlag: true,
          defaultRules: [
            {
              // 类型
              field: 'noPrintQuantity',
              operator: 'equal',
              value: 0
            }
          ]
        }
        this.$store.commit('startLoading')
        this.$API.receiptAndDelivery.barcodePrintRequestExport(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = UTILS.getHeadersFileName(res)
          UTILS.download({ fileName: `${fileName}`, blob: res.data })
        })
      }
      //导出
    },
    handleConfirm(row) {
      //打印排期列表
      console.log(row)
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认打印选中的条码需求吗?')
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.receiptAndDelivery
            .barcodePrintRequestPrint(row)
            .then((res) => {
              this.$store.commit('endLoading')
              if (res?.data?.type === 'application/json') {
                const reader = new FileReader()
                reader.readAsText(res?.data, 'utf-8')
                reader.onload = function () {
                  console.log('======', reader)
                  const readerRes = reader.result
                  const resObj = JSON.parse(readerRes)
                  Vue.prototype.$toast({
                    content: resObj.msg,
                    type: 'error'
                  })
                }

                return
              }
              const content = res.data
              this.pdfUrl = window.URL.createObjectURL(
                new Blob([content], { type: 'application/pdf' })
              )
              // window.open(this.pdfUrl);
              let date = new Date().getTime()
              let ifr = document.createElement('iframe')
              ifr.style.frameborder = 'no'
              ifr.style.display = 'none'
              ifr.style.pageBreakBefore = 'always'
              ifr.setAttribute('id', 'printPdf' + date)
              ifr.setAttribute('name', 'printPdf' + date)
              ifr.src = this.pdfUrl
              document.body.appendChild(ifr)
              this.doPrint('printPdf' + date)
              window.URL.revokeObjectURL(ifr.src)
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },
    handleConfirmEn(row) {
      //打印排期列表
      console.log(row)
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认打印选中的条码需求吗?')
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.receiptAndDelivery
            .barcodeEnPrintRequestPrint(row)
            .then((res) => {
              if (res?.data?.type === 'application/json') {
                this.$store.commit('endLoading')
                const reader = new FileReader()
                reader.readAsText(res?.data, 'utf-8')
                reader.onload = function () {
                  console.log('======', reader)
                  const readerRes = reader.result
                  const resObj = JSON.parse(readerRes)
                  Vue.prototype.$toast({
                    content: resObj.msg,
                    type: 'error'
                  })
                }

                return
              }
              const content = res.data
              this.pdfUrl = window.URL.createObjectURL(
                new Blob([content], { type: 'application/pdf' })
              )
              // window.open(this.pdfUrl);
              let date = new Date().getTime()
              let ifr = document.createElement('iframe')
              ifr.style.frameborder = 'no'
              ifr.style.display = 'none'
              ifr.style.pageBreakBefore = 'always'
              ifr.setAttribute('id', 'printPdf' + date)
              ifr.setAttribute('name', 'printPdf' + date)
              ifr.src = this.pdfUrl
              document.body.appendChild(ifr)
              this.doPrint('printPdf' + date)
              window.URL.revokeObjectURL(ifr.src)
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },

    // 获取dom打印方法 异步执行
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        this.pdfLoading = false
      }, 100)
    },
    //点击顶部的操作按钮
    handleClickToolBar(e) {
      console.log(e)
      console.log(e.grid.getSelectedRecords(), e.toolbar)
      if (e.toolbar.id === 'Add') {
        this.handleAdd()
        return
      }
      if (e.toolbar.id === 'closeEdit') {
        e.grid.closeEdit()
        this.updateList()
      }
      if (e.toolbar.id === 'Import') {
        this.handleImport(true)
        return
      }
      if (e.toolbar.id === 'Export1') {
        this.handleExport()
        return
      }
      let selectRecords = e.grid.getSelectedRecords()
      let _id = e.grid.getSelectedRecords().map((item) => {
        return item.id
      })
      let _printQuantity = e.grid.getSelectedRecords().map((item) => {
        return item.printQuantity
      })
      let obj = []

      console.log(obj)
      console.log(_id, _printQuantity)
      if (!selectRecords.length) {
        this.$toast({ content: this.$t('请至少选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Delete') {
        this.handleDelete(selectRecords)
      }
      if (e.toolbar.id === 'Confirm') {
        _id.forEach((item, index) => {
          console.log(item)
          obj.push({ id: item, printQuantity: _printQuantity[index] })
        })
        this.handleConfirm(obj)
      }
      if (e.toolbar.id === 'ConfirmPrint') {
        _id.forEach((item, index) => {
          console.log(item)
          obj.push({
            id: item,
            printQuantity: _printQuantity[index],
            nationalFlag: 1
          })
        })
        this.handleConfirmEn(obj)
      }
      if (e.toolbar.id === 'synchronous') {
        this.synchronousWms(_id)
      }
    },
    // 内部同步
    synchronousWms(ids) {
      this.$API.receiptAndDelivery.barcodePrintRecordSync(ids).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('同步成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('同步失败'), type: 'warning' })
        }
      })
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('sourceType')
  }
}
</script>
