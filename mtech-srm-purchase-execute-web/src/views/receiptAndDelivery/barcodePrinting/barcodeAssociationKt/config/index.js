import { i18n } from '@/main.js'
// import UTILS from "../../../../../utils/utils";
import Input from '../components/Input.vue'
import InputView from '../components/InputView.vue'
// import InputView2 from "../components2/InputView.vue";
import Vue from 'vue'
import Select from '../components/Select.vue'
// import Select2 from "../components2/Select.vue";
import DatePicker from '../components/DatePicker.vue'
// import DatePickerView from "../components/DataPickerView.vue";
import InputNumber from '../components/InputNumber.vue'
import { timeNumberToDate } from '@/utils/utils'

import { utils } from '@mtech-common/utils'

//开始改了啊啊
export const checkColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false,
    allowEditing: false
  }
]
export const importColumn = [
  {
    field: 'ImportType',
    headerText: i18n.t('导入状态'),
    width: '150',
    allowEditing: false
  }
]
export const lastColumn2 = [
  // {
  //   field: "addId",
  //   headerText: "addId",
  //   width: "200",
  //   allowEditing: false,
  // },

  {
    field: 'barcodeCode',
    headerText: i18n.t('条码ID'),
    width: '200',
    allowEditing: false,
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'associateCode',
    headerText: i18n.t('关联条码id'),
    width: '200',
    selectOptions: [],
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    allowEditing: false,
    width: '200',
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.companyCode}}-{{data.companyName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('公司')}}</span>
              </div>
            `
        })
      }
    }
  },

  {
    field: 'quantity',
    headerText: i18n.t('数量'),
    width: '200',
    allowEditing: false,
    editTemplate: () => {
      return { template: InputNumber }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '300',
    selectOptions: [],
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('工厂')}}</span>
              </div>
            `
        })
      }
    }
  },

  {
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    allowEditing: false,
    width: '200',
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.supplierCode}}-{{data.supplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    }
    // headerTemplate: () => {
    //   return {
    //     template: Vue.component("headers", {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('供应商')}}</span>
    //           </div>
    //         `,
    //     }),
    //   };
    // },
  },

  {
    field: 'deliveryLineNo',
    headerText: i18n.t('送货单行号'),
    allowEditing: false,
    width: '150',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'deliveryCode',
    headerText: i18n.t('送货单号'),
    selectOptions: [],
    width: '200',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    selectOptions: [],
    width: '200',
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料编号')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: '250',
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    width: '150',
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    width: '150',
    allowEditing: false,
    editTemplate: () => {
      return { template: DatePicker }
    }
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    width: '200',
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未同步'),
        1: i18n.t('已同步'),
        2: i18n.t('同步失败')
      }
    }
    // editTemplate: () => {
    //   return { template: InputNumber };
    // },
  }
]
export const lastColumn = [
  // {
  //   field: "addId",
  //   headerText: "addId",
  //   width: "200",
  //   allowEditing: false,
  // },
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false,
    allowEditing: false
  },

  {
    field: 'barcodeCode',
    headerText: i18n.t('条码ID'),
    width: '200',
    allowEditing: false
  },
  {
    field: 'printDate',
    headerText: i18n.t('打印时间'),
    width: '200'
  },
  {
    field: 'associateCode',
    headerText: i18n.t('关联条码id'),
    width: '300',
    selectOptions: [],
    template: () => {
      return {
        template: Vue.component('actionSelect', {
          template: `<mt-select :filtering="doGetDataSource" :fields="{ text: 'name', value: 'value' }" :allowFiltering="true" v-model="data.associateCode" :dataSource="dataArr" :show-clear-button="false" @change="handleChange"></mt-select>`,
          components: {
            // DebounceFilterSelect: () =>
            //   import("@/components/debounceFilterSelect"),
          },
          data: function () {
            return { dataArr: [], data: {}, doGetDataSource: () => {} }
          },

          mounted() {
            this.dataArr = this.data.column.selectOptions.filter(
              (item) => item != this.data.barcodeCode
            )
            console.log(this.data)
            this.doGetDataSource = utils.debounce(this.getUser, 1000)
          },
          methods: {
            getUser(e) {
              this.init(e.text)
            },
            init(e) {
              // this.dataArr = this.data.column.selectOptions.filter(
              //   (item) => item != this.data.barcodeCode
              // );

              let params = {
                condition: 'and',
                page: {
                  current: 1,
                  size: 50
                },
                defaultRules: [
                  {
                    field: 'barcodeCode',
                    operator: 'contains',
                    value: e || ''
                  }
                ]
              }
              this.$API.receiptAndDelivery.barcodePrintRecordtListCode(params).then((res) => {
                this.dataArr = res.data.records
                  .filter((item) => item != this.data.barcodeCode)
                  .map((item) => {
                    return { name: item, value: item }
                  })
                // this.dataArr = this.dataArr.map((item) => {
                //   return { name: item, value: item };
                // });
                console.log(this.dataArr)
              })
            },
            handleChange(e) {
              this.data.associateCode = e.value

              if (e.e !== null) {
                this.$parent.$emit('cellEdit', {
                  ...this.data
                })
              }
              // if (e.value === "") {}
            }
          }
        })
      }
    }
  },
  {
    field: 'barcodeLevel',
    headerText: i18n.t('条码层级'),
    selectOptions: [
      { label: i18n.t('一级'), value: 1 },
      { label: i18n.t('二级'), value: 2 },
      { label: i18n.t('三级'), value: 3 },
      { label: i18n.t('四级'), value: 4 }
    ],
    width: '200',

    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('一级'),
        2: i18n.t('二级'),
        3: i18n.t('三级'),
        4: i18n.t('四级')
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '300',
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.supplierCode}}-{{data.supplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    },
    width: '300'
  },
  {
    field: 'supplierEnName',
    headerText: i18n.t('供应商（英文）'),
    width: '150'
  },
  {
    field: 'deliveryCode',
    headerText: i18n.t('送货单号'),

    width: '200'
  },
  {
    field: 'deliveryLineNo',
    headerText: i18n.t('送货单行号'),
    width: '200',
    allowEditing: false
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号'),

    width: '200'
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: '250'
  },
  {
    field: 'itemEnName',
    headerText: i18n.t('物料名称（英文）'),
    width: '150'
  },
  // {
  //   field: "buyerOrgName",
  //   headerText: i18n.t("采购组"),
  //   width: "200",
  // },
  {
    field: 'deliveryTotalQuantity',
    headerText: i18n.t('送货总数量')
  },
  {
    field: 'noPrintQuantity',
    headerText: i18n.t('未打印数量'),
    width: '150'
  },
  {
    field: 'printQuantity',
    headerText: i18n.t('本次打印数量'),
    width: '150'
  },
  {
    field: 'packingQuantity',
    headerText: i18n.t('装箱数量'),
    selectOptions: [],
    width: '250'
  },
  {
    field: 'lastQuantity',
    headerText: i18n.t('尾数'),
    width: '200'
  },
  {
    field: 'bin',
    headerText: 'BIN',
    width: '200'
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    width: '200',
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未同步'),
        1: i18n.t('已同步'),
        2: i18n.t('同步失败')
      }
    }
    // editTemplate: () => {
    //   return { template: InputNumber };
    // },
  },
  {
    field: 'deliveryDate',
    headerText: i18n.t('送货日期'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'produceDate',
    headerText: i18n.t('生产日期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'netWeight',
    headerText: i18n.t('净重（KG）'),
    width: '150'
  },
  {
    field: 'grossWeight',
    headerText: i18n.t('毛重（KG）'),
    width: '150'
  },
  {
    field: 'length',
    headerText: i18n.t('长（CM）'),
    width: '150'
  },
  {
    field: 'width',
    headerText: i18n.t('宽（CM）'),
    selectOptions: [],
    width: '200'
  },
  {
    field: 'height',
    headerText: i18n.t('高（CM)'),
    width: '150'
  },
  {
    field: 'lastGrossWeight',
    headerText: i18n.t('尾箱毛重（KG）'),
    width: '150'
  },
  {
    field: 'lastNetWeight',
    headerText: i18n.t('尾箱净重（KG）'),
    selectOptions: [],
    width: '200'
  },
  {
    field: 'lineBody',
    headerText: i18n.t('线体'),
    width: '150'
  },
  {
    field: 'supplierBatchCode',
    headerText: i18n.t('供方批次'),
    width: '150'
  },
  {
    field: 'produceBatchCode',
    headerText: i18n.t('生产批次'),
    width: '150'
  },
  {
    field: 'supplierItemCode',
    headerText: i18n.t('供应商物料编码'),
    width: '150'
  },
  {
    field: 'supplierItemName',
    headerText: i18n.t('供应商物料名称'),
    width: '150'
  },
  {
    field: 'supplierLineBody',
    headerText: i18n.t('供应商线体'),
    width: '150'
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    width: '150'
  }
]
