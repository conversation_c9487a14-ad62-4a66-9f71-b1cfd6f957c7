<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="disabled"
    ></mt-input>
  </div>
</template>
<script>
// var bigDecimal = require("js-big-decimal");
export default {
  data() {
    return {
      data: {},
      disabled: true,

      packingQuantity: '', //装箱数量
      printQuantity: '', //本次打印数量
      grossWeight: '', //毛重

      netWeight: '' //净重
    }
  },
  mounted() {
    this.deliveryTotalQuantity = this.data.deliveryTotalQuantity
    this.packingQuantity = this.data.packingQuantity
    this.printQuantity = this.data.printQuantity
    this.grossWeight = this.data.grossWeight

    this.netWeight = this.data.netWeight
    const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))

    this.data.createUserName = userInfo.username

    this.$bus.$on('itemNameChange', (val) => {
      this.data.itemName = val
    }) //接受的物料名称

    this.$bus.$on('companyNameChange', (val) => {
      this.data.companyName = val
    }) //接受的物料名称
    this.$bus.$on('supplierNameChange', (val) => {
      this.data.supplierName = val
    }) //接受的供应商
    this.$bus.$on('createUserNameChanged', (val) => {
      this.data.createUserName = val
    }) //未打印数量
    this.$bus.$on('noPrintQuantityChange', (val) => {
      this.data.noPrintQuantity = val
    }) //未打印数量
    this.$bus.$on('warehouseNameChange', (val) => {
      this.data.warehouseName = val
    }) //接受的库存地点
    // this.$bus.$on("deliveryTotalQuantityChange1", (val) => {
    //   this.deliveryTotalQuantity = val;
    //   this.init();
    // }); //送货总数量
    this.$bus.$on('printQuantityChange', (val) => {
      this.printQuantity = val
      this.init2()
    }) //本次打印数量
    this.$bus.$on('packingQuantityChange', (val) => {
      this.packingQuantity = val
      this.init()
      this.init1()
      this.init2()
    }) //装箱数量
    this.$bus.$on('grossWeightChange', (val) => {
      this.grossWeight = val
      this.init()
    }) //毛重

    this.$bus.$on('netWeightChange', (val) => {
      this.netWeight = val
      this.init1()
    }) //净重
  },
  methods: {
    // lastGrossWeight 尾箱毛重 grossWeight 毛重 lastQuantity 尾数 packingQuantity 装箱数量
    // netWeight 净重 noPrintQuantity 未打印数量  deliveryTotalQuantity 送货总数量 lastNetWeight 尾箱净重
    // 尾箱毛重=毛重*尾数/装箱数量
    // 尾箱净重=净重*尾数/装箱数量
    // 尾数=本次打印数量 %装箱数量 余数
  }
}
</script>
