<template>
  <!-- 条码关联 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      @cellEdit="cellEdit"
      @handleClickToolBar="handleClickToolBar"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
      @selectedChanged="selectedChanged"
      @handleSelectTab="handleSelectTab"
    ></mt-template-page>
    <!-- :permission-obj="permissionObj" -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :is-show-tips="isShow"
      :request-urls="requestUrls"
      @closeUploadExcel="handleImport(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>
<script>
import { checkColumn, lastColumn, lastColumn2 } from './config/index'
import { BASE_TENANT } from '@/utils/constant'
import { cloneDeep } from 'lodash'
// import * as UTILS from "@/utils/utils";
import * as UTILS from '@/utils/utils'

export default {
  components: {
    UploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    return {
      // requestUrls: {
      //   templateUrlPre: "receiptAndDelivery",
      //   // templateUrl: "buyerGoodsDemandPlanInfoKtExport", // 下载模板接口方法名
      //   uploadUrl: "barcodePrintExternalImport", // 上传接口方法名
      // },
      isShow: true,
      requestUrls: {
        templateUrlPre: 'receiptAndDelivery',
        templateUrl: 'barcodePrintRecordTemplateDownload', // 下载模板接口方法名
        uploadUrl: 'barcodePrintRecordImport' // 上传接口方法名
      },
      uploadParams: {}, // 导入通知配置文件参数
      downTemplateParams: {
        pageFlag: false
      }, // 通知配置导入下载模板参数
      arr: '',
      lastColumn: lastColumn,
      lastColumn2: lastColumn2,
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'Internal', permissionCode: 'T_02_0052' },
          { dataPermission: 'External', permissionCode: 'T_02_0053' }
        ]
      },
      componentConfig: [
        {
          title: this.$t('内部条码关联'),
          // dataPermission: "Internal",
          // permissionCode: "T_02_0052",
          useToolTemplate: false, // 使用预置表格操作按钮(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置（打开筛选、刷新、设置）
          toolbar: [
            {
              id: 'accept1',
              icon: 'icon_table_accept1',
              // permission: ["O_02_0667"],
              title: this.$t('提交')
            },
            { id: 'Import', icon: 'icon_solid_Import', title: this.$t('导入') }
          ],
          // gridId: this.$tableUUID.receiptAndDelivery.barcodeAssociation.list,

          grid: {
            allowPaging: true, // 分页
            columnData: lastColumn,
            // columnData: [
            //   {
            //     field: "barcodeCode",
            //     headerText: this.$t("条码ID"),
            //     width: "200",
            //     allowEditing: false,
            //   },
            // ],
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/barcodePrintRecord/query`
            }
          }
        },
        {
          title: this.$t('外部条码关联'),
          // dataPermission: "External",
          // permissionCode: "T_02_0053",
          useCombinationSelection: false,
          toolbar: [
            {
              id: 'accept',
              icon: 'icon_table_accept1',
              // permission: ["O_02_0670"],
              title: this.$t('提交')
            },
            {
              id: 'Add2',
              icon: 'icon_solid_Createorder',
              // permission: ["O_02_0668"],
              title: this.$t('新增')
            },
            {
              id: 'Delete2',
              icon: 'icon_solid_Delete',
              // permission: ["O_02_0669"],
              title: this.$t('删除')
            },
            { id: 'Import', icon: 'icon_solid_Import', title: this.$t('导入') },
            {
              id: 'Export1',
              icon: 'icon_solid_Import',
              // permission: ["O_02_0671"],
              title: this.$t('导出')
            }
          ],
          grid: {
            height: '600',
            allowEditing: true, //开启表格编辑操作
            editSettings: {
              allowEditing: true, // 允许修改
              allowAdding: true, //允许新增
              allowDeleting: true, // 允许删除
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false, // 是否显示确认弹窗
              showDeleteConfirmDialog: false, // 是否显示弹窗
              newRowPosition: 'Top' // 新行所在方向
            },
            columnData: checkColumn.concat(lastColumn2),
            asyncConfig: {
              url: `${BASE_TENANT}/barcodePrintExternal/query`,
              serializeList: (list) => {
                list.forEach((item) => {
                  item.addId = this.addId2++
                  item.isEntry = '1' //是否是带入的数据
                })
                return list
              }
            }
          }
        }
      ],
      cellList: [],
      nowEditRowFlag: '', //当前编辑的行id
      nowEditRowFlag2: '', //当前编辑的行id
      addId: '1',
      isEdit: '1', //是否编辑 1是编辑 2不是编辑
      selectedOtherInfo: {},
      selectedOtherInfo2: {},
      addId2: '2',
      supplierInfo: {
        supplierName: '',
        supplierCode: '',
        supplierEnName: '',
        supplierId: ''
      },
      currentTab: 0 //0表示内部关联 1表示外部关联
    }
  },
  mounted() {
    this.getSupplier()
    this.init()
  },
  methods: {
    upExcelConfirm() {
      this.handleImport(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
      this.$refs.templateRef.refreshCurrentGridData()
    },

    init() {
      let params = {
        condition: 'and',
        page: {
          current: 1,
          size: 50
        },
        defaultRules: [
          {
            field: 'barcodeCode',
            operator: 'contains',
            value: ''
          }
        ]
      }
      this.$API.receiptAndDelivery.barcodePrintRecordtListCode(params).then((res) => {
        this.componentConfig[0].grid.columnData[3].selectOptions = res.data.records.map((item) => {
          return { name: item, value: item }
        })
        // this.dataArr = res.data.records
        //   .filter((item) => item != this.data.barcodeCode)
        //   .map((item) => {
        //     return { name: item, value: item };
        //   });
        // this.dataArr = this.dataArr.map((item) => {
        //   return { name: item, value: item };
        // });
        // console.log(this.dataArr);
      })
    },
    handleExport() {
      //导出
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 5000 },
        pageFlag: true,
        rules: rule.rules || []
      }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery.barcodePrintExternalExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 内部条码关联单条行内编辑
    cellEdit(e) {
      // console.log(e);
      // this.cellList.push(e);
      // console.log(this.cellList);
      let params = e
      let obj = {
        id: params.id,
        associateCode: params.associateCode
      }
      console.log(params)
      params.column = undefined
      this.$API.receiptAndDelivery.barcodePrintRecordSave(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.template.refreshCurrentGridData()
        }
      })
    },

    getSupplier() {
      // 查询条码id列表

      // 查询供应商的数据
      // const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
      // this.supplierInfo.supplierName = userInfo.enterpriseName;

      // this.supplierInfo.supplierId = userInfo.enterpriseId;
      // this.supplierInfo.supplierCode = userInfo.enterpriseCode;
      this.$API.masterData.getEnterpriseInfo().then((res) => {
        this.supplierInfo.supplierEnName = res.data.enterpriseEnglishName
      })
    },
    handleSelectTab(val) {
      this.currentTab = val

      if (val === 1) {
        this.isShow = false
        this.requestUrls = {
          templateUrlPre: 'receiptAndDelivery',
          // templateUrl: "buyerGoodsDemandPlanInfoKtExport", // 下载模板接口方法名
          uploadUrl: 'barcodePrintExternalImport' // 上传接口方法名
        }
      } else {
        this.isShow = true

        this.requestUrls = {
          templateUrlPre: 'receiptAndDelivery',
          templateUrl: 'barcodePrintRecordTemplateDownload', // 下载模板接口方法名
          uploadUrl: 'barcodePrintRecordImport' // 上传接口方法名
        }
      }
    },
    selectedChanged(val) {
      console.log(val)
      console.log(val, '最新的额外数据12')

      if (this.currentTab === 1) {
        Object.assign(this.selectedOtherInfo2, val.itemInfo)
      }
      console.log(this.selectedOtherInfo2, '最新的额外数据')
    },
    endEdit() {
      //让组件失去焦点
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
    },
    actionBegin(args) {
      console.log(args, '我是actionBegin')
      if (args.requestType === 'add') {
        if (this.currentTab === 1) {
          let lastColumn = cloneDeep(this.lastColumn2)
          lastColumn.forEach((item) => {
            args.data[item.field] = ''
            args.data.isEntry = '2'
          })
          args.data.addId = this.addId2++
          args.data.supplierEnName = this.supplierInfo.supplierEnName
          args.data.supplierName = this.supplierInfo.supplierName
          args.data.supplierId = this.supplierInfo.supplierId
          args.data.supplierItemCode = this.supplierInfo.supplierItemCode

          this.nowEditRowFlag2 = args.data.addId
        }
      }
      if (args.requestType == 'beginEdit') {
        if (this.currentTab === 1) {
          this.nowEditRowFlag2 = args.rowData.addId
        }
      }
    },
    actionComplete(args) {
      console.log(args, '我是actionComplete')
      if (args.requestType === 'beginEdit' || args.requestType === 'add') {
        this.isEdit = '1'
      }
      if (
        args.requestType === 'refresh' ||
        args.requestType === 'save' ||
        args.requestType === 'delete'
      ) {
        this.isEdit = '2'
      }
      if (args.requestType === 'save' && args.action === 'edit') {
        //编辑完成
        if (this.currentTab === 1) {
          let row = this.getRow2()
          if (row.isEntry === '2') {
            //新增错误重新编辑
            this.addRow2(row)
          }
          if (row.isEntry === '1') {
            this.editRow2(row)
          }
        }
      }
      if (args.requestType === 'save' && args.action === 'add') {
        //新增完成
        if (this.currentTab === 1) {
          let row = this.getRow2()
          this.addRow2(row)
        }
      }
    },
    getRow2() {
      let currentRecords =
        this.$refs.templateRef?.getCurrentUsefulRef().gridRef?.ejsRef.getCurrentViewRecords() || []
      let row = cloneDeep(this.selectedOtherInfo2)
      let info = {}
      currentRecords.some((item) => {
        if (item.addId === this.nowEditRowFlag2) {
          Object.assign(item, row)
          info = item
        }
      })
      return info
    },

    editRow2(row) {
      console.log(row, '编辑行数据外部条码')
      let params = this.getParams2(row)
      params.id = row.id
      this.$API.receiptAndDelivery.barcodePrintExternalSave(params).then(() => {
        this.$toast({
          content: this.$t('编辑外部条码关联操作成功'),
          type: 'success'
        })
        this.selectedOtherInfo = []
        this.selectedOtherInfo2 = []

        this.updateList()
      })
    },
    addRow2(row) {
      //新增外部条码关联
      let params = this.getParams2(row)
      console.log(row, '新增行数据外部条码关联')

      if (params.siteName.length == 0 || params.itemCode.length == 0) {
        this.$toast({
          content: this.$t('带星号的全为必填字段'),
          type: 'error'
        })
      } else {
        this.$API.receiptAndDelivery.barcodePrintExternalSave(params).then(() => {
          this.$toast({
            content: this.$t('新增外部条码关联操作成功'),
            type: 'success'
          })
          this.selectedOtherInfo = []
          this.selectedOtherInfo2 = []

          this.updateList()
        })
      }
    },
    getParams2(row) {
      let params = {
        abolished: 0,
        associateCode: row.associateCode, //关联条码id
        barcodeCode: row.barcodeCode || '', //条码code
        companyCode: row.companyCode || '', //公司code
        companyId: row.companyId || 0, //公司id
        createUserName: row.createUserName || '',
        companyName: row.companyName || '', //公司名称
        deliveryCode: row.deliveryCode || '', ///送货单号
        deliveryLineNo: row.deliveryLineNo || 0, ///送货行单号
        itemCode: row.itemCode || '', //物料编号
        itemEnName: row.itemEnName || '', //物料英文名称
        itemId: row.itemId || 0, //物料id
        itemName: row.itemName || '', //物料名称
        siteCode: row.siteCode || '', //工厂code
        siteId: row.siteId || 0, //工厂id
        siteName: row.siteName || '', //工厂name
        supplierCode: row.supplierCode || '', //供应商code
        quantity: row.quantity || '',
        supplierName: row.supplierName || '' //供应商name
      }
      return params
    },

    handleDelete2(row) {
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认删除选中的外部条码关联吗?')
        },
        success: () => {
          let ids = row.map((item) => item.id)
          this.$API.receiptAndDelivery.barcodePrintExternalDelete(ids).then(() => {
            this.$toast({
              content: this.$t('删除外部条码关联操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    updateList() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    //新增
    handleImport(flag) {
      //导入
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    handleAdd1() {
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
    },
    // 内部同步
    handleAccept1(ids) {
      this.$API.receiptAndDelivery.barcodePrintRecordSync(ids).then(() => {
        this.$toast({
          content: this.$t('同步成功'),
          type: 'success'
        })
        this.updateList()
      })
    },
    // 外部同步
    handleAccept(ids) {
      this.$API.receiptAndDelivery.barcodePrintExternalSync(ids).then(() => {
        this.$toast({
          content: this.$t('同步成功'),
          type: 'success'
        })
        this.updateList()
      })
    },
    //点击顶部的操作按钮
    handleClickToolBar(e) {
      console.log('方法1', e)
      if (e.toolbar.id === 'Add2') {
        this.handleAdd1()
        return
      }
      if (e.toolbar.id === 'Import') {
        this.handleImport(true)

        return
      }
      let _id = []
      let selectRecords = e.grid.getSelectedRecords()
      e.grid.getSelectedRecords().map((item) => {
        _id.push(item.id)

        // _sourceStatus.push(item.sourceStatus),
        // _remainingQuantity.push(item.remainingQuantity);
      })

      if (!selectRecords.length) {
        this.$toast({ content: this.$t('请至少选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Export1') {
        this.handleExport()
        return
      }

      if (e.toolbar.id === 'accept1') {
        this.handleAccept1(_id)
        return
      }
      if (e.toolbar.id === 'accept') {
        this.handleAccept(_id)
        return
      }
      if (e.toolbar.id === 'Delete2') {
        this.handleDelete2(selectRecords)
      }
    }
  },

  beforeDestroy() {
    localStorage.removeItem('barcodeAssociation')
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-tabs {
  flex-shrink: 0;
}
</style>
