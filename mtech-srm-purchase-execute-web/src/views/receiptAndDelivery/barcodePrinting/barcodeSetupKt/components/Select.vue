<template>
  <div>
    <debounce-filter-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="{ text: 'theCodeName', value: 'value' }"
      :placeholder="placeholder"
      :request="postChange"
      :value-template="valueTemplate"
      @change="selectChange"
      :open-dispatch-change="true"
      :disabled="isDisabled"
      :allow-filtering="true"
    ></debounce-filter-select>
  </div>
</template>
<script>
// import { utils } from "@mtech-common/utils";
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      valueTemplate: null,

      purchase: '', //采购组code
      isDisabled: false,
      siteId: '',
      customerEnterpriseId: '',
      supplierCode: '', //供应商code

      siteCode: ''
    }
  },
  mounted() {
    this.$bus.$off('customerEnterpriseIdTwo ')

    this.dataSource = this.data.column.selectOptions
    if (this.data.column.field === 'companyName') {
      this.getCompany()
    }
    if (this.data.column.field === 'mainType') {
      this.getMainType()
    }
    if (this.data.column.field === 'barcodeLevel') {
      this.getBarcodeLevel()
    }
    if (this.data.column.field === 'itemCode') {
      this.$bus.$on('siteId', (val) => {
        this.siteId = val
      })
      this.$bus.$on('customerEnterpriseIdTwo', (val) => {
        this.customerEnterpriseId = val
      })
      this.$bus.$off('siteCode')
      this.$bus.$on('siteCode', (val) => {
        this.siteCode = val
        this.data.itemCode
          ? this.getCategoryItem(this.data.itemCode, val)
          : this.getCategoryItem('', val)
        // this.getCategoryItem("", val);
      })
      // this.$bus.$on("tenantId", (val) => {

      // });
      //物料下拉
      // this.getBusinessGroupTypeListByDictCode();
    }
    if (this.data.column.field === 'siteName') {
      //工厂下拉
      // this.$bus.$off("customerEnterpriseId");
      // this.$bus.$on("customerEnterpriseId", (val) => {
      //   this.customerEnterpriseId = val;
      //   this.findOrgSiteInfo("");
      // });
      // this.valueTemplate = codeNameColumn({
      //   firstKey: "siteOrgCode",
      //   secondKey: "siteOrgName",
      // });
      this.valueTemplate = codeNameColumn({
        firstKey: 'siteOrgCode',
        secondKey: 'siteOrgName'
      })
      //工厂下拉old
      // this.$bus.$on("customerEnterpriseId", (val) => {
      //   this.customerEnterpriseId = val;
      //   this.findOrgSiteInfo("");
      // });
      // new
      this.$bus.$on('companyCodeChange', (val) => {
        this.findOrgSiteInfo(val)
      })
    }
    if (this.data.column.field === 'supplierCode') {
      this.$bus.$on('companyCodeChange', (val) => {
        this.companyCode = val
      })
      this.$bus.$on('siteCodeChange', (val) => {
        this.siteCode = val
        if (this.data.supplierCode) {
          this.getQuerySuppliers(this.data.supplierCode, val)
        } else {
          this.getQuerySuppliers('', val)
        }
      })
    }
    // if (this.data.column.field === "deliveryCode") {
    //   //送货单号
    //   this.getDeliveryCode();
    // }
  },
  methods: {
    getCompany() {
      // let obj = {
      //   fuzzyNameOrCode: "",
      // };
      // this.$API.masterData.getCustomer(obj).then((res) => {
      //   res.data.forEach((item) => {
      //     item.theCodeName = item.customerName; // 	客户名称
      //     item.value = item.customerName; // 	客户名称
      //   });
      //   this.dataSource = res.data || [];
      // });
      this.$API.masterData.getComListSupplier().then((res) => {
        res.data.forEach((item) => {
          item.theCodeName = item.itemCode + '-' + item.itemName // 	客户名称
          item.value = item.itemName // 	客户名称
        })
        this.dataSource = res.data || []
        console.log(this.dataSource)
        console.log(this.data)
      })
    },
    // 获取主单类型
    getMainType() {
      this.dataSource = [
        { label: this.$t('送货单'), value: 1 },
        { label: this.$t('入库单'), value: 2 },
        { label: this.$t('物料替换单'), value: 3 }
      ]
    },
    getBarcodeLevel() {
      this.dataSource = [
        { theCodeName: this.$t('一级'), value: 1 },
        { theCodeName: this.$t('二级'), value: 2 },
        { theCodeName: this.$t('三级'), value: 3 },
        { theCodeName: this.$t('四级'), value: 4 }
      ]
    },

    // 模糊
    postChange(e) {
      if (this.data.column.field === 'itemCode') {
        this.getCategoryItem(e.text, this.siteCode)
      }
      if (this.data.column.field === 'supplierCode') {
        this.getQuerySuppliers(e.text, this.supplierCode)
      }
      // if (this.data.column.field === "siteName") {
      //   this.findOrgSiteInfo(e);
      // }
    },
    // 供应商
    getQuerySuppliers(e) {
      let obj = {
        defaultRules: [
          {
            field: 'companyCode',
            operator: 'equal',
            value: this.companyCode
          },
          {
            field: 'siteCode',
            operator: 'equal',
            value: this.siteCode
          },
          {
            field: 'codeLike',
            operator: 'equal',
            value: e
          }
        ]
      }
      this.$API.vmi.getQuerySuppliers(obj).then((res) => {
        const list = res?.data || []
        if (res.data.length === 1) {
          this.isDisabled = true
          this.$bus.$emit('supplierNameChange', res.data[0].supplierCode)
          this.data.supplierCode = res.data[0].supplierCode
          this.$parent.$emit('selectedChanged', {
            fieldCode: 'deliveryLineNo',
            itemInfo: {
              supplierName: res.data[0].supplierName,
              supplierCode: res.data[0].supplierCode
            }
          })
        }
        list.forEach((item) => {
          item.theCodeName = item.supplierName //
          item.value = item.supplierCode //
        })
        this.dataSource = list || []
      })
    },
    findOrgSiteInfo(val) {
      let obj = {
        paramObj: val
      }
      this.$API.masterData.getSiteListSupplier(obj).then((res) => {
        const list = res?.data || []
        list.forEach((item) => {
          ;(item.siteOrgCode = item.siteCode), (item.siteOrgName = item.siteName)
        })
        list.forEach((item) => {
          item.value = item.siteOrgName
          // item.theCodeName = item.siteName;
          // item.value = item.siteName;
        })
        this.dataSource = addCodeNameKeyInList({
          firstKey: 'siteOrgCode',
          secondKey: 'siteOrgName',
          list
        })
      })
    },
    getCategoryItem(val, code) {
      //物料下拉
      let obj = {
        siteCode: code,

        fuzzyNameOrCode: {
          fuzzyNameOrCode: val
        }
      }
      this.$API.masterData.getSiteItemFuzzyQuerySupplier(obj).then((res) => {
        const list = res?.data || []
        list.forEach((item) => {
          item.theCodeName = item.itemCode
          item.value = item.itemCode
        })
        this.dataSource = list
        // if (updateData) {
        //   this.$nextTick(() => {
        //     updateData(this.dataSource);
        //   });
        // }
      })
      // this.fields = { text: "itemCode", value: "itemCode" };
    },
    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      if (this.data.column.field === 'companyName') {
        this.$bus.$emit('customerEnterpriseIdTwo', val.itemData.customerEnterpriseId)
        this.$bus.$emit('companyCodeChange', val.itemData.itemCode)

        // this.$API.masterData
        //   .supplierFindInBuyingByCustomerCode({
        //     customerCode: val.itemData.customerCode,
        //   })
        //   .then((res) => {
        // this.$bus.$emit("supplierNameChange", res.data.supplierName);
        // this.$bus.$emit("supplierCode", res.data.supplierCode);
        // sessionStorage.setItem(
        //   "customerEnterpriseId",
        //   val.itemData.customerEnterpriseId
        // );
        this.$parent.$emit('selectedChanged', {
          //传出额外数据
          fieldCode: 'companyName',
          itemInfo: {
            companyName: val.itemData.itemName,
            // companyId: val.itemData.companyOrgId,
            // purTenantId: val.itemData.customerTenantId,
            companyCode: val.itemData.itemCode
            // customerEnterpriseId: val.itemData.customerEnterpriseId,
            // supplierName: res.data.supplierName,
            // supplierCode: res.data.supplierCode,
          }
        })
      }
      if (this.data.column.field === 'barcodeLevel') {
        this.$parent.$emit('selectedChanged', {
          //传出额外数据
          fieldCode: 'barcodeLevel',
          itemInfo: {
            barcodeLevel: val.itemData.value
          }
        })
      }
      if (this.data.column.field === 'itemCode') {
        this.$bus.$on('siteId', (val) => {
          this.siteId = val
        })
        //物料下拉
        this.$bus.$emit('itemNameChange', val.itemData.itemName) //传给物料名称
        this.$parent.$emit('selectedChanged', {
          //传出额外数据工厂
          fieldCode: 'itemCode',
          itemInfo: {
            itemCode: val.itemData.itemCode,
            itemName: val.itemData.itemName,
            itemId: val.itemData.id
          }
        })
      }
      if (this.data.column.field === 'siteName') {
        this.$bus.$emit('siteId', val.itemData.id)
        this.$bus.$emit('siteCode', val.itemData.siteOrgCode)
        this.$bus.$emit('siteCodeChange', val.itemData.siteOrgCode)

        console.log(val.itemData)
        this.$parent.$emit('selectedChanged', {
          //传出额外数据工厂
          fieldCode: 'siteName',
          itemInfo: {
            siteName: val.itemData.siteOrgName,
            siteCode: val.itemData.siteOrgCode,
            siteId: val.itemData.siteOrgId
          }
        })
      }
      if (this.data.column.field === 'supplierCode') {
        this.$bus.$emit('supplierNameChange', val.itemData.supplierCode)

        this.$parent.$emit('selectedChanged', {
          fieldCode: 'deliveryLineNo',
          itemInfo: {
            supplierName: val.itemData.supplierName,
            supplierCode: val.itemData.supplierCode
          }
        })
      }
    }
  }
}
</script>
