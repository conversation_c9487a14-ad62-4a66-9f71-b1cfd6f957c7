import { i18n } from '@/main.js'
// import UTILS from "../../../../../utils/utils";
import Input from '../components/Input.vue'
import InputView from '../components/InputView.vue'
import Select from '../components/Select.vue'
import DatePicker from '../components/DatePicker.vue'
// import DatePickerView from "../components/DataPickerView.vue";
import InputNumber from '../components/InputNumber.vue'
import Vue from 'vue'
import dayjs from 'dayjs'
import { MasterDataSelect } from '@/utils/constant'
// import InputNumberView from "../components/InputNumberView.vue";
//开始改了啊啊

export const checkColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false,
    allowEditing: false
  }
]
export const importColumn = [
  {
    field: 'ImportType',
    headerText: i18n.t('导入状态'),
    width: '150',
    allowEditing: false
  }
]
export const lastColumn = [
  {
    field: 'addId',
    headerText: 'addId',
    width: 0,
    visible: false,
    allowEditing: false
  },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false
  },
  {
    field: 'barcodeLevel',
    headerText: i18n.t('条码层级'),

    width: '100',
    editTemplate: () => {
      return { template: Select }
    },
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('一级'),
        2: i18n.t('二级'),
        3: i18n.t('三级'),
        4: i18n.t('四级')
      }
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    allowEditing: false,
    width: '200',
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.companyCode}}-{{data.companyName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    },
    editTemplate: () => {
      let type = JSON.parse(sessionStorage.getItem('sourceType'))
      // let type = 0;

      // if (type === 0) {
      return { template: type === 0 ? Select : InputView }
      // }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('公司')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '180',
    selectOptions: [],
    editTemplate: () => {
      let type = JSON.parse(sessionStorage.getItem('sourceType'))
      // let type = 0;

      // if (type === 0) {
      return { template: type === 0 ? Select : InputView }
      // }
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('工厂')}}</span>
              </div>
            `
        })
      }
    }
  },

  {
    field: 'supplierName',
    headerText: i18n.t('供应商'),
    allowEditing: false,
    width: '175',
    editTemplate: () => {
      return { template: InputView }
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.supplierCode}}-{{data.supplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'supplierEnName',
    headerText: i18n.t('供应商（英文）'),
    width: '140',
    allowEditing: false,
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'deliveryCode',
    headerText: i18n.t('送货单号'),
    selectOptions: [],
    width: '120',
    editTemplate: () => {
      let type = JSON.parse(sessionStorage.getItem('sourceType'))
      // let type = 0;

      // if (type === 0) {
      return { template: type === 0 ? Select : InputView }
      // }
    }
  },
  {
    field: 'deliveryLineNo',
    headerText: i18n.t('送货单行号'),
    width: '105',
    editTemplate: () => {
      let type = JSON.parse(sessionStorage.getItem('sourceType'))
      // let type = 0;

      // if (type === 0) {
      return { template: type === 0 ? Select : InputView }
      // }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    selectOptions: [],
    searchOptions: {
      operator: 'likeright',
      maxQueryValueLength: 20000
    },
    width: '120',
    editTemplate: () => {
      let type = JSON.parse(sessionStorage.getItem('sourceType'))
      // let type = 0;

      // if (type === 0) {
      return { template: type === 0 ? Select : InputView }
      // }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料编号')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: '275',
    editTemplate: () => {
      return { template: InputView }
    }
  },

  {
    allowEditing: false,
    field: 'itemEnName',
    headerText: i18n.t('物料名称（英文）'),
    width: '150',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'buyerOrgName',
    headerText: i18n.t('采购组'),
    width: '90',
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'deliveryTotalQuantity',
    headerText: i18n.t('送货总数量'),
    width: '105',
    editTemplate: () => {
      return { template: InputNumber }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('送货总数量')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'noPrintQuantity',
    headerText: i18n.t('未打印数量'),
    width: '110',
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'printQuantity',
    headerText: i18n.t('本次打印数量'),
    width: '120',
    editTemplate: () => {
      return { template: InputNumber }
    }
  },
  {
    field: 'packingQuantity',
    headerText: i18n.t('装箱数量'),
    selectOptions: [],
    width: '95',
    editTemplate: () => {
      return { template: InputNumber }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('装箱数量')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'lastQuantity',
    headerText: i18n.t('尾数'),
    width: '70',
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'bin',
    headerText: 'BIN',
    width: '65',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'deliveryDate',
    headerText: i18n.t('送货日期'),
    width: '150',
    editTemplate: () => {
      return { template: DatePicker }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('送货日期')}}</span>
              </div>
            `
        })
      }
    }
    // template: () => {
    //   return { template: DatePickerView };
    // },
  },
  {
    field: 'produceDate',
    headerText: i18n.t('生产日期'),
    width: '150',
    editTemplate: () => {
      return { template: DatePicker }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('生产日期')}}</span>
              </div>
            `
        })
      }
    }
    // template: () => {
    //   return { template: DatePickerView };
    // },
  },
  {
    field: 'netWeight',
    headerText: i18n.t('净重（KG）'),
    width: '110',
    editTemplate: () => {
      return { template: InputNumber }
    }
    // headerTemplate: () => {
    //   return {
    //     template: Vue.component("headers", {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('净重（KG）')}}</span>
    //           </div>
    //         `,
    //     }),
    //   };
    // },
  },
  {
    field: 'grossWeight',
    headerText: i18n.t('毛重（KG）'),
    width: '110',
    editTemplate: () => {
      return { template: InputNumber }
    }
    // headerTemplate: () => {
    //   return {
    //     template: Vue.component("headers", {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('毛重（KG）')}}</span>
    //           </div>
    //         `,
    //     }),
    //   };
    // },
  },
  {
    field: 'length',
    headerText: i18n.t('长（CM）'),
    width: '100',
    editTemplate: () => {
      return { template: InputNumber }
    }
    // headerTemplate: () => {
    //   return {
    //     template: Vue.component("headers", {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('长（CM）')}}</span>
    //           </div>
    //         `,
    //     }),
    //   };
    // },
  },
  {
    field: 'width',
    headerText: i18n.t('宽（CM）'),
    selectOptions: [],
    width: '100',
    editTemplate: () => {
      return { template: InputNumber }
    }
    // headerTemplate: () => {
    //   return {
    //     template: Vue.component("headers", {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('宽（CM）')}}</span>
    //           </div>
    //         `,
    //     }),
    //   };
    // },
  },
  {
    field: 'height',
    headerText: i18n.t('高（CM）'),
    width: '100',
    editTemplate: () => {
      return { template: InputNumber }
    }
    // headerTemplate: () => {
    //   return {
    //     template: Vue.component("headers", {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('高（CM）')}}</span>
    //           </div>
    //         `,
    //     }),
    //   };
    // },
  },
  {
    field: 'lastGrossWeight',
    headerText: i18n.t('尾箱毛重（KG）'),
    width: '140',
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'lastNetWeight',
    headerText: i18n.t('尾箱净重（KG）'),
    selectOptions: [],
    width: '140',
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'lineBody',
    headerText: i18n.t('线体'),
    width: '70',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'supplierBatchCode',
    headerText: i18n.t('供方批次'),
    width: '100',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'produceBatchCode',
    headerText: i18n.t('生产批次'),
    width: '100',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'supplierItemCode',
    headerText: i18n.t('供应商物料编码'),
    width: '150',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'supplierItemName',
    headerText: i18n.t('供应商物料名称'),
    width: '265',
    allowEditing: false,
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'supplierLineBody',
    headerText: i18n.t('供应商线体'),
    width: '205',
    allowEditing: false,
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    width: '150',
    allowEditing: false,
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    width: '150',
    searchOptions: {
      ...MasterDataSelect.timeRange,
      default: [
        // new Date(dayjs().subtract(3, 'month').format('YYYY-MM-DD 00:00:00')),
        new Date(dayjs().subtract(15, 'day').format('YYYY-MM-DD 00:00:00')),
        new Date(dayjs().format('YYYY-MM-DD 23:59:59'))
      ]
    }
  }
]
export const lastColumn2 = [
  {
    field: 'addId',
    headerText: 'addId',
    width: 0,
    visible: false,
    allowEditing: false
  },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false
  },
  {
    field: 'barcodeLevel',
    headerText: i18n.t('条码层级'),

    width: '100',

    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('一级'),
        2: i18n.t('二级'),
        3: i18n.t('三级'),
        4: i18n.t('四级')
      }
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    allowEditing: false,
    width: '200',
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.companyCode}}-{{data.companyName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('公司')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '180',
    selectOptions: [],

    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('工厂')}}</span>
              </div>
            `
        })
      }
    }
  },

  {
    field: 'supplierName',
    headerText: i18n.t('供应商'),
    allowEditing: false,
    width: '175',

    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.supplierCode}}-{{data.supplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'supplierEnName',
    headerText: i18n.t('供应商（英文）'),
    width: '140',
    allowEditing: false
  },
  {
    field: 'deliveryCode',
    headerText: i18n.t('送货单号'),
    selectOptions: [],
    width: '120'
  },
  {
    field: 'deliveryLineNo',
    headerText: i18n.t('送货单行号'),
    width: '105'
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    selectOptions: [],
    searchOptions: {
      operator: 'likeright',
      maxQueryValueLength: 20000
    },
    width: '120',
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料编号')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: '275'
  },
  {
    field: 'printTimes',
    headerText: i18n.t('打印次数'),
    width: '100',
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    allowEditing: false,
    field: 'itemEnName',
    headerText: i18n.t('物料名称（英文）'),
    width: '150'
  },
  {
    field: 'buyerOrgName',
    headerText: i18n.t('采购组'),
    width: '90'
  },
  {
    field: 'deliveryTotalQuantity',
    headerText: i18n.t('送货总数量'),
    width: '105',
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('送货总数量')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'noPrintQuantity',
    headerText: i18n.t('未打印数量'),
    width: '110'
  },
  {
    field: 'printQuantity',
    headerText: i18n.t('本次打印数量'),
    width: '120',
    template: () => {
      return {
        template: Vue.component('actionSelect', {
          template: `<mt-inputNumber :precision="3" :step="1" v-model="data.printQuantity" @change="handleChange"></mt-inputNumber>`,
          components: {
            // DebounceFilterSelect: () =>
            //   import("@/components/debounceFilterSelect"),
          },
          data: function () {
            return { data: {} }
          },

          mounted() {},
          methods: {
            handleChange(e) {
              console.log(e)
              console.log(this.data)
              if (e > Number(this.data.deliveryTotalQuantity)) {
                this.$toast({
                  content: this.$t('本次打印数量不能大于送货总数量'),
                  type: 'error'
                })
                // this.data.printQuantity = Number(
                //   this.data.deliveryTotalQuantity
                // );
                return
              }

              let i = Number(this.data.printQuantity) % Number(this.data.packingQuantity)
              this.data.lastQuantity = isNaN(i) ? 0 : i
              this.$parent.$emit('cellEdit', {
                ...this.data
              })
              // this.$API.receiptAndDelivery
              //   .barcodePrintRequestsave(this.data)
              //   .then(() => {
              //     this.$toast({
              //       content: this.$t("操作成功"),
              //       type: "success",
              // });
              // this.selectedOtherInfo = {};
              // this.updateList();
              // });
              // if (e.value === "") {}
            }
          }
        })
      }
    }
  },
  {
    field: 'packingQuantity',
    headerText: i18n.t('装箱数量'),
    selectOptions: [],
    width: '95',

    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('装箱数量')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'lastQuantity',
    headerText: i18n.t('尾数'),
    width: '70'
  },
  {
    field: 'bin',
    headerText: 'BIN',
    width: '65'
  },
  {
    field: 'deliveryDate',
    headerText: i18n.t('送货日期'),
    width: '150',

    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('送货日期')}}</span>
              </div>
            `
        })
      }
    }
    // template: () => {
    //   return { template: DatePickerView };
    // },
  },
  {
    field: 'produceDate',
    headerText: i18n.t('生产日期'),
    width: '150',

    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('生产日期')}}</span>
              </div>
            `
        })
      }
    }
    // template: () => {
    //   return { template: DatePickerView };
    // },
  },
  {
    field: 'netWeight',
    headerText: i18n.t('净重（KG）'),
    width: '110'

    // headerTemplate: () => {
    //   return {
    //     template: Vue.component("headers", {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('净重（KG）')}}</span>
    //           </div>
    //         `,
    //     }),
    //   };
    // },
  },
  {
    field: 'grossWeight',
    headerText: i18n.t('毛重（KG）'),
    width: '110'

    // headerTemplate: () => {
    //   return {
    //     template: Vue.component("headers", {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('毛重（KG）')}}</span>
    //           </div>
    //         `,
    //     }),
    //   };
    // },
  },
  {
    field: 'length',
    headerText: i18n.t('长（CM）'),
    width: '100'

    // headerTemplate: () => {
    //   return {
    //     template: Vue.component("headers", {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('长（CM）')}}</span>
    //           </div>
    //         `,
    //     }),
    //   };
    // },
  },
  {
    field: 'width',
    headerText: i18n.t('宽（CM）'),
    selectOptions: [],
    width: '100'

    // headerTemplate: () => {
    //   return {
    //     template: Vue.component("headers", {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('宽（CM）')}}</span>
    //           </div>
    //         `,
    //     }),
    //   };
    // },
  },
  {
    field: 'height',
    headerText: i18n.t('高（CM）'),
    width: '100'

    // headerTemplate: () => {
    //   return {
    //     template: Vue.component("headers", {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('高（CM）')}}</span>
    //           </div>
    //         `,
    //     }),
    //   };
    // },
  },
  {
    field: 'lastGrossWeight',
    headerText: i18n.t('尾箱毛重（KG）'),
    width: '140'
  },
  {
    field: 'lastNetWeight',
    headerText: i18n.t('尾箱净重（KG）'),
    selectOptions: [],
    width: '140'
  },
  {
    field: 'lineBody',
    headerText: i18n.t('线体'),
    width: '70'
  },
  {
    field: 'supplierBatchCode',
    headerText: i18n.t('供方批次'),
    width: '100'
  },
  {
    field: 'produceBatchCode',
    headerText: i18n.t('生产批次'),
    width: '100'
  },
  {
    field: 'supplierItemCode',
    headerText: i18n.t('供应商物料编码'),
    width: '150'
  },
  {
    field: 'supplierItemName',
    headerText: i18n.t('供应商物料名称'),
    width: '265',
    allowEditing: false
  },
  {
    field: 'supplierLineBody',
    headerText: i18n.t('供应商线体'),
    width: '205',
    allowEditing: false
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    width: '150',
    allowEditing: false
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    width: '150',
    searchOptions: {
      ...MasterDataSelect.timeRange,
      default: [
        // new Date(dayjs().subtract(3, 'month').format('YYYY-MM-DD 00:00:00')),
        new Date(dayjs().subtract(15, 'day').format('YYYY-MM-DD 00:00:00')),
        new Date(dayjs().format('YYYY-MM-DD 23:59:59'))
      ]
    }
  }
]
