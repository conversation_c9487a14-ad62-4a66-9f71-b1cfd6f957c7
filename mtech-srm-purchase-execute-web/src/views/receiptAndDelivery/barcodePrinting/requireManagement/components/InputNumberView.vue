<template>
  <div>
    <mt-input id="self49" v-model="data.self49" style="display: none"></mt-input>
    <span :class="calssName">{{ data.self49 }}</span>
  </div>
</template>
<script>
export default {
  data() {
    return {
      calssName: '',
      data: {}
    }
  },
  mounted() {
    if (this.data.self48 === this.data.self49) {
      this.calssName = ''
    } else {
      this.calssName = 'red'
    }
  }
}
</script>
<style lang="scss" scoped>
.red {
  background: red;
}
</style>
