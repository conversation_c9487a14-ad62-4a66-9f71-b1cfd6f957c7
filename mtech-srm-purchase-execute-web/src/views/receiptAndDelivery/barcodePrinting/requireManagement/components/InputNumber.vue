<template>
  <div>
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      style="display: none"
    ></mt-input>
    <mt-inputNumber
      v-model="data[data.column.field]"
      :min="0"
      :precision="3"
      :step="1"
      :show-clear-button="false"
      @input="numberChange"
      :disabled="isDisabled"
    ></mt-inputNumber>
  </div>
</template>
<script>
var bigDecimal = require('js-big-decimal')
export default {
  data() {
    return {
      data: {},
      deliveryTotalQuantity: '', //送货总数量
      isDisabled: false
    }
  },
  mounted() {
    this.deliveryTotalQuantit = this.data.deliveryTotalQuantity //送货总数量
    this.$bus.$on('deliveryTotalQuantityChange', (val) => {
      this.deliveryTotalQuantity = val
      if (this.data.printQuantity) {
        //如果已经有了数据
        let noPrintQuantity = bigDecimal.subtract(val, this.data.printQuantity)
        this.$bus.$emit('noPrintQuantityChange', noPrintQuantity)
        return
      }
      this.data.printQuantity = val

      this.$bus.$emit('printQuantityChange', val)
      this.$bus.$emit('noPrintQuantityChange', 0)
    }) //接受的本次打印数量默认等于送货总数量
    if (this.data.column.field === 'deliveryTotalQuantity') {
      //送货总数量//如果有送货单号
      if (this.data.deliveryCode) {
        this.isDisabled = true
      }
      this.$bus.$on('deliveryQuantityChange', (val) => {
        //接受的选中的送货单明细中送货总数量
        this.data.deliveryTotalQuantity = val
        this.$bus.$emit('noPrintQuantityChange', val)

        this.isDisabled = true
      })
    }
    if (this.data.column.field === 'packingQuantity') {
      this.$bus.$on('packingQuantityChange1', (val) => {
        this.data.packingQuantity = val
      })
    }
    if (this.data.column.field === 'printQuantity') {
      this.$bus.$on('printQuantityChange', (val) => {
        console.log(val, '数据来了')
        this.data.printQuantity = val
        console.log(this.data.printQuantity)
      })
    }
    if (this.data.column.field === 'grossWeight') {
      this.$bus.$on('grossWeightChange1', (val) => {
        this.data.grossWeight = val
      })
    }
    if (this.data.column.field === 'netWeight') {
      this.$bus.$on('netWeightChange1', (val) => {
        this.data.netWeight = val
      })
    }
    if (this.data.column.field === 'length') {
      this.$bus.$on('lengthChange', (val) => {
        this.data.length = val
      })
    }
    if (this.data.column.field === 'width') {
      this.$bus.$on('widthChange', (val) => {
        this.data.width = val
      })
    }
    if (this.data.column.field === 'height') {
      this.$bus.$on('heightChange', (val) => {
        this.data.height = val
      })
    }
  },
  methods: {
    numberChange(val) {
      if (this.data.column.field === 'deliveryTotalQuantity') {
        this.$bus.$emit('deliveryTotalQuantityChange', val) //传给送货总数量
        this.$bus.$emit('noPrintQuantityChange', val)

        this.$bus.$emit('printQuantityChange', val)

        this.$bus.$emit('deliveryTotalQuantityChange1', val) //传给尾数
      }
      if (this.data.column.field === 'printQuantity') {
        this.$bus.$emit('printQuantityChange', val)

        // if (this.deliveryTotalQuntity) {
        //   this.$bus.$emit(
        //     "noPrintQuantityChange",
        //     bigDecimal.subtract(this.deliveryTotalQuantity, val)
        //   );
        // }
      }
      if (this.data.column.field === 'packingQuantity') {
        this.$bus.$emit('packingQuantityChange', val)
      }
      if (this.data.column.field === 'grossWeight') {
        this.$bus.$emit('grossWeightChange', val)
      }
      if (this.data.column.field === 'netWeight') {
        this.$bus.$emit('netWeightChange', val)
      }
    }
  }
}
</script>
