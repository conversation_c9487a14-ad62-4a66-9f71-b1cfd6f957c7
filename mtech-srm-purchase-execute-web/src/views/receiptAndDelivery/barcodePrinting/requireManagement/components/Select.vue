<template>
  <div>
    <debounce-filter-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :request="postChange"
      v-if="type === '1'"
      :data-source="dataSource"
      :value-template="valueTemplate"
      :fields="{ text: 'theCodeName', value: 'value' }"
      :placeholder="placeholder"
      @change="selectChange"
      :open-dispatch-change="true"
      :disabled="isDisabled"
      :allow-filtering="true"
    ></debounce-filter-select>
    <mt-input
      v-else
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="true"
    ></mt-input>
  </div>
</template>
<script>
// import { utils } from "@mtech-common/utils";

import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import { uniqWith, isEqual, filter } from 'lodash'

export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      data: {},
      type: '1',
      placeholder: this.$t('请选择'),
      valueTemplate: null,
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      purchase: '', //采购组code
      isDisabled: false,
      itemCode: '', //物料code
      siteCode: '', //工厂code
      barcodeLevel: '', //条码层级
      supplierCode: '', //供应商code
      tenantId: '', //采方租户id
      siteId: '',
      customerEnterpriseId: ''
    }
  },
  mounted() {
    this.supplierCode = this.data.supplierCode
    this.itemCode = this.data.itemCode
    this.siteCode = this.data.siteCode
    this.barcodeLevel = this.data.barcodeLevel
    this.dataSource = this.data.column.selectOptions
    if (this.data.column.field === 'companyName') {
      this.getCompany()
    }
    if (this.data.column.field === 'barcodeLevel') {
      this.getBarcodeLevel()
    }
    if (this.data.column.field === 'itemCode') {
      this.$bus.$on('itemCodeChange', (val, id) => {
        console.log(val, id)
        if (id === '2') {
          this.type = '2'
        }
        this.data.itemCode = val
      })
      this.$bus.$on('supplierCode', (val) => {
        this.supplierCode = val
      })
      this.$bus.$on('siteId', (val) => {
        this.siteId = val
      })
      // this.$bus.$on("tenantId", (val) => {
      //   this.getCategoryItem(val);
      // });
      this.$bus.$on('customerEnterpriseId', (val) => {
        this.customerEnterpriseId = val
      })

      this.$bus.$on('siteCode', (val) => {
        this.siteCode = val
        console.log(this.customerEnterpriseId)
        this.getCategoryItem('')
      })
      // this.getBusinessGroupTypeListByDictCode();
      this.$bus.$on('deliveryCodeChange', (val) => {
        if (val.itemCode) {
          this.data.itemCode = val.itemCode

          // this.isDisabled = true;
          this.$bus.$emit('itemCodeChange', val.itemCode)
          this.barcodePrintConfigQueryOne(val.itemCode, '1')
        }
      })
      if (this.data.deliveryCode) {
        //如果有送货单号
        // this.isDisabled = true;
      }
      this.$bus.$on('tenantIdChange', (val) => {
        //接受采方租户id 去获取采购物料信息
        this.tenantId = val
      })
    }
    if (this.data.column.field === 'siteName') {
      this.valueTemplate = codeNameColumn({
        firstKey: 'siteOrgCode',
        secondKey: 'siteOrgName'
      })
      //工厂下拉
      this.$bus.$on('customerEnterpriseId', (val) => {
        this.customerEnterpriseId = val
        this.findOrgSiteInfo('')
      })
    }
    // if (this.data.column.field === "supplierName") {
    //   //供应商下拉
    //   this.getSupplier();
    // }
    if (this.data.column.field === 'deliveryCode') {
      //送货单号
      this.$bus.$on('supplierCode', (val) => {
        this.supplierCode = val
      })
      this.$bus.$on('siteName', (val) => {
        this.getDeliveryCode(val)
      })
    }
    if (this.data.column.field === 'deliveryLineNo') {
      //送货单行号
      this.$bus.$on('deliveryLineNoClick', (val) => {
        this.getDeliveryLine(val)
      })
    }
    this.$bus.$on('siteCodeChange', (val) => {
      this.siteCode = val
    })
    this.$bus.$on('itemCodeChange', (val) => {
      this.itemCode = val
    })
    this.$bus.$on('barcodeLevelChange', (val) => {
      this.barcodeLevel = val
    })
  },

  methods: {
    getCompany() {
      let obj = {
        fuzzyNameOrCode: ''
      }
      console.log(this.data)

      this.$API.masterData.getCustomer(obj).then((res) => {
        res.data.forEach((item) => {
          item.theCodeName = item.customerName // 	客户名称
          item.value = item.customerName // 	客户名称
        })
        this.dataSource = res.data || []
        console.log(this.dataSource)
        console.log(this.data)
      })
      // this.data.companyName = this.data.customerCode;
    },
    // 模糊
    postChange(e) {
      if (this.data.column.field === 'itemCode') {
        this.getCategoryItem(e)
      }
      if (this.data.column.field === 'siteName') {
        this.findOrgSiteInfo(e)
      }
    },
    getDeliveryCode(val) {
      //获取送货单信息
      let params = {
        page: {
          current: 1,
          size: 50
        },
        defaultRules: [
          {
            field: 'supplierCode',
            operator: 'equal',
            value: this.supplierCode
          },
          {
            field: 'siteName',
            operator: 'equal',
            value: val
          },
          {
            field: 'status',
            operator: 'notequal',
            value: 4
          },
          {
            field: 'status',
            operator: 'notequal',
            value: 5
          }
        ]
      }
      this.$API.receiptAndDelivery
        .supplierOrderDeliveryItemPage(params) //  供方
        .then((res) => {
          console.log(res.data.records.length)
          // let tmp = res.data.records.map((item) => {
          //   return {
          //     itemName: item.itemName, // 物料名称
          //     itemCode: item.itemCode, // 物料编号
          //     purUnitName: item.unitName, // 单位名称
          //     purUnitCode: item.unitCode, // 单位代码
          //   };
          // });

          res.data.records = res.data.records.map((item) => {
            return {
              deliveryCode: item.deliveryCode,
              theCodeName: item.deliveryCode,
              value: item.deliveryCode
            }
          })
          console.log()
          res.data.records = uniqWith(
            filter(res.data.records, (item) => item.deliveryCode), // 过滤
            isEqual
          ) // 去重

          this.dataSource = res.data?.records
          // this.dataSource = [
          //   {
          //     name: "1",
          //     value: "1",
          //     deliveryCode: "1",
          //   },
          // ];
        })
    },
    getDeliveryLine(val) {
      let params = {
        page: {
          current: 1,
          size: 50
        },
        defaultRules: [
          {
            field: 'deliveryCode',
            operator: 'equal',
            value: val
          }
        ]
      }
      this.$API.receiptAndDelivery
        .supplierOrderDeliveryItemPage(params) //  供方
        .then((res) => {
          console.log(res.data.records.length)
          res.data.records.forEach((item) => {
            item.theCodeName = item.deliveryLineNo // 	行号
            item.value = item.deliveryLineNo // 	行号
          })
          this.dataSource = res.data?.records

          this.data.deliveryLineNo = Number(this.data.deliveryLineNo)
        })
    },
    barcodePrintConfigQueryOne(val, type) {
      //根据物料+工厂+条码层级+供应商查找配置
      let params = {
        siteCode: type === '2' ? val : this.siteCode,
        supplierCode: this.supplierCode,
        itemCode: type === '1' ? val : this.itemCode,
        barcodeLevel: type === '3' ? val : this.barcodeLevel
      }
      console.log(params, '供应商配置')
      if (!(params.siteCode && params.supplierCode && params.itemCode && params.barcodeLevel)) {
        //四个条件都有
        return
      }
      this.$API.receiptAndDelivery.barcodePrintConfigQueryOne(params).then((res) => {
        if (res.data) {
          this.$bus.$emit('supplierItemCodeChange', res.data.supplierItemCode)
          this.$bus.$emit('supplierItemNameChange', res.data.supplierItemName)
          this.$bus.$emit('supplierLineBodyChange', res.data.supplierLineBody)
          this.$bus.$emit('packingQuantityChange', res.data.packingQuantity)
          this.$bus.$emit('packingQuantityChange1', res.data.packingQuantity)
          this.$bus.$emit('grossWeightChange', res.data.grossWeight)
          this.$bus.$emit('grossWeightChange1', res.data.grossWeight)
          this.$bus.$emit('netWeightChange', res.data.netWeight)
          this.$bus.$emit('netWeightChange1', res.data.netWeight)
          this.$bus.$emit('lengthChange', res.data.length)
          this.$bus.$emit('widthChange', res.data.width)
          this.$bus.$emit('heightChange', res.data.height)
        }
      })
    },

    getBarcodeLevel() {
      this.dataSource = [
        { theCodeName: this.$t('一级'), value: 1 },
        { theCodeName: this.$t('二级'), value: 2 },
        { theCodeName: this.$t('三级'), value: 3 },
        { theCodeName: this.$t('四级'), value: 4 }
      ]
    },
    findOrgSiteInfo(args) {
      const { text, updateData } = args
      //工厂下拉
      let obj = {
        fuzzyParam: text,
        buyerEnterpriseId: this.customerEnterpriseId
      }
      this.$API.masterData.getSourceAvailableView(obj).then((res) => {
        const list = res?.data || []
        list.forEach((item) => {
          item.value = item.siteOrgName
        })
        this.dataSource = addCodeNameKeyInList({
          firstKey: 'siteOrgCode',
          secondKey: 'siteOrgName',
          list
        })
        if (updateData) {
          this.$nextTick(() => {
            updateData(this.dataSource)
          })
        }
      })
    },
    getCategoryItem(args) {
      //物料下拉
      const { text, updateData } = args
      let obj = {
        buyerEnterpriseId: this.customerEnterpriseId,
        siteCode: this.siteCode,
        fuzzyParam: text,
        requireItemDetail: true
      }
      this.$API.masterData.postSourceItemnVendorava(obj).then((res) => {
        const list = res?.data || []
        list.forEach((item) => {
          item.theCodeName = item.itemCode
          item.value = item.itemCode
        })
        this.dataSource = list
        if (updateData) {
          this.$nextTick(() => {
            updateData(this.dataSource)
          })
        }
      })
    },
    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      console.log(this.data.column.field)

      if (this.data.column.field === 'companyName') {
        this.$bus.$emit('customerEnterpriseId', val.itemData.customerEnterpriseId)

        this.$bus.$emit('tenantId', val.itemData.tenantId)
        this.$API.masterData
          .supplierFindInBuyingByCustomerCode({
            customerCode: val.itemData.customerCode
          })
          .then((res) => {
            this.$bus.$emit('supplierNameChange', res.data.supplierName)
            this.$bus.$emit('supplierCode', res.data.supplierCode)
            // sessionStorage.setItem(
            //   "customerEnterpriseId",
            //   val.itemData.customerEnterpriseId
            // );
            this.$parent.$emit('selectedChanged', {
              //传出额外数据
              fieldCode: 'companyName',
              itemInfo: {
                companyName: val.itemData.customerName,
                // companyId: val.itemData.companyOrgId,
                purTenantId: val.itemData.customerTenantId,
                companyCode: val.itemData.customerCode,
                customerEnterpriseId: val.itemData.customerEnterpriseId,
                supplierName: res.data.supplierName,
                supplierCode: res.data.supplierCode
              }
            })
          })
      }
      // if (this.data.column.field === "barcodeLevel") {
      //   this.$parent.$emit("selectedChanged", {
      //     //传出额外数据
      //     fieldCode: "barcodeLevel",
      //     itemInfo: {
      //       barcodeLevel: val.itemData.value,
      //     },
      //   });
      //         this.$parent.$emit("selectedChanged", {
      //   //传出额外数据工厂
      //   fieldCode: "itemCode",
      //   itemInfo: {
      //     itemCode: ,
      //   },
      // });
      // }
      if (this.data.column.field === 'itemCode') {
        //物料下拉
        this.$bus.$emit('itemNameChange', val.itemData.itemName) //传给物料名称
        this.$bus.$emit('buyerOrgNameChange', val.itemData.purchaseGroupName) //传给采购组

        this.$parent.$emit('selectedChanged', {
          //传出额外数据
          fieldCode: 'itemCode',
          itemInfo: {
            itemCode: val.itemData.itemCode,
            itemName: val.itemData.itemName,
            itemId: val.itemData.id
          }
        })
        this.$bus.$emit('itemCodeChange', val.itemData.itemCode)
        this.barcodePrintConfigQueryOne(val.itemData.itemCode, '1')
      }
      if (this.data.column.field === 'siteName') {
        this.$bus.$emit('siteId', val.itemData.siteOrgId)
        this.$bus.$emit('siteName', val.itemData.siteOrgName)
        this.$bus.$emit('companyNameChanged', val.itemData.companyOrgName)
        this.$bus.$emit('siteCode', val.itemData.siteOrgCode)
        this.$parent.$emit('selectedChanged', {
          //传出额外数据工厂
          fieldCode: 'siteName',
          itemInfo: {
            siteName: val.itemData.siteOrgName,
            siteCode: val.itemData.siteOrgCode,
            siteId: val.itemData.siteOrgId
          }
        })
        this.$bus.$emit('siteCodeChange', val.itemData.siteOrgCode)
        this.$bus.$emit('tenantIdChange', val.itemData.tenantId)

        this.barcodePrintConfigQueryOne(val.itemData.siteOrgCode, '2')
      }
      if (this.data.column.field === 'barcodeLevel') {
        //条码层级
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'barcodeLevel',
          itemInfo: {
            barcodeLevel: val.itemData.value
          }
        })
        this.$bus.$emit('barcodeLevelChange', val.itemData.value)
        this.barcodePrintConfigQueryOne(val.itemData.value, '3')
      }
      if (this.data.column.field === 'deliveryCode') {
        //送货单号
        this.$bus.$emit('buyerOrgNameChange', val.itemData.buyerOrgName)
        this.$bus.$emit('deliveryLineNoClick', val.itemData.deliveryCode)
      }
      if (this.data.column.field === 'deliveryLineNo') {
        this.$bus.$emit('deliveryCodeChange', {
          deliveryCode: val.itemData.deliveryCode
        })
        console.log(val)
        this.$bus.$emit(
          //传给送货总数量
          'deliveryQuantityChange',
          val.itemData.deliveryQuantity
        )
        console.log(val)
        this.$bus.$emit('itemCodeChange', val.itemData.itemCode, '2') //传给物料编码
        this.$bus.$emit('itemNameChange', val.itemData.itemName) //传给物料名称
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'deliveryLineNo',
          itemInfo: {
            deliveryLineNo: val.itemData.deliveryLineNo,
            deliveryCode: val.itemData.deliveryCode
          }
        })
      }
    }
  },
  beforeDestroy() {
    this.$bus.$off('siteName')
    this.$bus.$off('siteCode')
    this.$bus.$off('customerEnterpriseId')
    this.$bus.$off('deliveryLineNoClick')
    this.$bus.$off('customerEnterpriseId')
    this.$bus.$off('customerEnterpriseId')
    this.$bus.$off('customerEnterpriseId')
  }
}
</script>
