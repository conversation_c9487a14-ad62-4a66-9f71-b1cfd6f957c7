<template>
  <div>
    <mt-input :id="data.column.field" style="display: none"></mt-input>
    <span>{{ timeInfo }}</span>
  </div>
</template>
<script>
import UTILS from '../../../../../utils/utils'
export default {
  data() {
    return {
      timeInfo: '',
      data: {}
    }
  },
  mounted() {
    console.log(this.data)
    if (this.data[this.data.column.field]) {
      this.timeInfo = UTILS.formateTime(this.data.produceDate)
    }
  }
}
</script>
