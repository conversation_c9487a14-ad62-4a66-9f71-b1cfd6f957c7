<!-- 采方-送货单列表-泛智屏 -->
<template>
  <mt-template-page ref="templateRef" :template-config="pageConfig">
    <List slot="slot-0" />
    <Detail slot="slot-1" />
    <DetailNew slot="slot-2" />
    <ScreenIdlog slot="slot-3" />
  </mt-template-page>
</template>

<script>
export default {
  components: {
    List: () => import('./pages/List.vue'),
    Detail: () => import('./pages/Detail.vue'),
    DetailNew: () => import('./pages/DetailNew.vue'),
    ScreenIdlog: () => import('./pages/screenIdlog.vue')
  },
  data() {
    return {
      pageConfig: [
        { title: this.$t('送货单列表') },
        { title: this.$t('送货单明细') },
        { title: this.$t('送货单明细-新') }
      ]
    }
  },
  created() {
    const elementPermissionSet = window.elementPermissionSet
    if (elementPermissionSet.includes('T_02_0202')) {
      this.pageConfig.push({ title: this.$t('栈板屏ID外围系统日志') })
    }
  }
}
</script>
