<!-- 送货单明细tab -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      @handleClickCellTitle="handleClickCellTitle"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
          <mt-form-item prop="deliveryCode" :label="$t('送货单号')">
            <mt-input
              v-model="searchFormModel.deliveryCode"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="createTime" :label="$t('创建日期')">
            <mt-date-range-picker
              v-model="searchFormModel.createTime"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              @change="(e) => dataTimeChange(e, 'createTime')"
            />
          </mt-form-item>
          <mt-form-item prop="status" :label="$t('状态')">
            <mt-multi-select
              style="width: 100%"
              v-model="searchFormModel.status"
              :placeholder="$t('请选择状态')"
              :show-clear-button="true"
              :show-select-all="true"
              :data-source="statusOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="companyCode" :label="$t('公司编码')">
            <!-- <debounce-filter-select
              v-model="searchFormModel.companyCode"
              :request="getCompany"
              :data-source="companyOptions"
              :fields="{ text: 'theCodeName', value: 'orgCode' }"
              :value-template="companyCodeValueTemplate"
              :show-clear-button="true"
              :allow-filtering="true"
              @change="companyCodeChange"
              :placeholder="$t('请选择')"
            /> -->
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.companyCode"
              url="/srm-purchase-execute/tenant/common/permission/queryCompanyList"
              :placeholder="$t('请选择公司')"
              :fields="{ text: 'dimensionNameValue', value: 'dimensionCodeValue' }"
              records-position="data"
              params-key="keyWord"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="siteCode" :label="$t('工厂编码')">
            <!-- <debounce-filter-select
              v-model="searchFormModel.siteCode"
              :request="postSiteFuzzyQuery"
              :data-source="siteOptions"
              :show-clear-button="true"
              :fields="{ text: 'theCodeName', value: 'siteCode' }"
              :value-template="siteCodeValueTemplate"
              @change="siteCodeChange"
              :placeholder="$t('请选择')"
            /> -->
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.siteCode"
              url="/srm-purchase-execute/tenant/common/permission/querySiteList"
              :placeholder="$t('请选择工厂')"
              :fields="{ text: 'dimensionNameValue', value: 'dimensionCodeValue' }"
              records-position="data"
              params-key="keyWord"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="supplierCodes" :label="$t('供应商编码')">
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.supplierCodes"
              url="/masterDataManagement/tenant/supplier/paged-query"
              multiple
              :placeholder="$t('请选择供应商')"
              :fields="{ text: 'supplierName', value: 'supplierCode' }"
              :search-fields="['supplierName', 'supplierCode']"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="itemCode" :label="$t('物料编码')">
            <mt-input
              v-model="searchFormModel.itemCode"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="itemName" :label="$t('物料名称')">
            <mt-input
              v-model="searchFormModel.itemName"
              :placeholder="$t('支持模糊搜索')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="orderNo" :label="$t('采购订单号')">
            <mt-input
              v-model="searchFormModel.orderNo"
              :placeholder="$t('支持粘贴多个精准查询')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="lineNo" :label="$t('采购订单行号')" label-style="top">
            <mt-input
              v-model="searchFormModel.lineNo"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="onWayStatus" :label="$t('在途状态')">
            <mt-multi-select
              v-model="searchFormModel.onWayStatus"
              type="multipleChoice"
              :placeholder="$t('请选择')"
              :show-select-all="true"
              :show-clear-button="true"
              :data-source="onWayStatusOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="buyerOrgCode" :label="$t('采购组')">
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.buyerOrgCode"
              :url="$API.masterData.getBusinessGroupAuthFuzzyUrl"
              multiple
              :placeholder="$t('请选择采购组')"
              :fields="{ text: 'groupName', value: 'groupCode' }"
              records-position="data"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="workCenterCode" :label="$t('工作中心')">
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.workCenterCode"
              url="/masterDataManagement/tenant/work-center/paged-query"
              multiple
              :placeholder="$t('请选择')"
              :fields="{ text: 'workCenterName', value: 'workCenterCode' }"
              :search-fields="['workCenterName', 'workCenterCode']"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="saleOrderNo" :label="$t('销售订单号')" label-style="top">
            <mt-input
              v-model="searchFormModel.saleOrderNo"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="sendTime" :label="$t('发货日期')">
            <mt-date-range-picker
              v-model="searchFormModel.sendTime"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              @change="(e) => dataTimeChange(e, 'sendTime')"
            />
          </mt-form-item>
          <mt-form-item prop="demandDate" :label="$t('需求日期')">
            <mt-date-range-picker
              v-model="searchFormModel.demandDate"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              @change="(e) => dataTimeChange(e, 'demandDate')"
            />
          </mt-form-item>
          <mt-form-item prop="virtualSupplierCode" :label="$t('虚拟供应商代码')">
            <mt-input
              v-model="searchFormModel.virtualSupplierCode"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="associatedNo" :label="$t('关联单据编号')">
            <mt-input
              v-model="searchFormModel.associatedNo"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="associateOuterDocNo" :label="$t('来源单号')">
            <mt-input
              v-model="searchFormModel.associateOuterDocNo"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="doNo" :label="$t('来源单号2')">
            <mt-input
              v-model="searchFormModel.doNo"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="deliveryOrderType" :label="$t('送货单类型')">
            <mt-multi-select
              v-model="searchFormModel.deliveryOrderType"
              type="multipleChoice"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              :data-source="deliveryOrderTypeOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="postingDate" :label="$t('过账日期')">
            <mt-date-range-picker
              v-model="searchFormModel.postingDate"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              @change="(e) => dataTimeChange(e, 'postingDate')"
            />
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { componentConfig } from '../config/detail'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import { getHeadersFileName, download } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    const startDate = dayjs().subtract(1, 'month')
    return {
      componentConfig: componentConfig(this),
      searchFormModel: {
        status: [2],
        createTime: [new Date(startDate), new Date()],
        createTimeE: this.getUnix(new Date(dayjs(new Date()).format('YYYY-MM-DD 23:59:59'))),
        createTimeS: this.getUnix(
          new Date(dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00'))
        )
      },
      searchFormRules: {},
      deliveryOrderTypeOptions: [
        { text: this.$t('采购订单'), value: 1 },
        { text: this.$t('交货计划'), value: 2 },
        { text: this.$t('JIT'), value: 3 },
        { text: this.$t('无需求'), value: 4 },
        { text: this.$t('vmi'), value: 5 },
        { text: this.$t('钢材'), value: 6 },
        { text: this.$t('屏发货指导'), value: 7 },
        { text: this.$t('无PO送货'), value: 8 }
      ],
      statusOptions: [
        { text: this.$t('送货中'), value: 2 },
        { text: this.$t('已完成'), value: 3 },
        { text: this.$t('已取消'), value: 4 },
        { text: this.$t('已关闭'), value: 5 }
      ],
      onWayStatusOptions: [
        { text: this.$t('未出发'), value: 0 },
        { text: this.$t('已入园'), value: 1 },
        { text: this.$t('已出发'), value: 2 },
        { text: this.$t('已报到'), value: 3 },
        { text: this.$t('已取消'), value: 4 },
        { text: this.$t('已关闭'), value: 5 }
      ],
      companyOptions: [],
      companyCodeValueTemplate: codeNameColumn({
        firstKey: 'orgCode',
        secondKey: 'orgName'
      }),
      siteOptions: [],
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      })
    }
  },
  mounted() {
    this.getCompany({ text: '' })
    this.postSiteFuzzyQuery({ text: '' })
  },
  methods: {
    getCompany(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true,
        organizationIds: []
      }
      this.$API.masterData
        .OrgFindSpecifiedChildrenLevelOrgs(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.companyOptions = addCodeNameKeyInList({
              firstKey: 'orgCode',
              secondKey: 'orgName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.companyOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    companyCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.companyId = itemData.id
        this.searchFormModel.companyCode = itemData.orgCode
        this.searchFormModel.companyName = itemData.orgName
      } else {
        this.searchFormModel.companyId = ''
        this.searchFormModel.companyCode = ''
        this.searchFormModel.companyName = ''
      }
    },
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        page: {
          current: 1,
          pages: 0,
          size: 20
        }
      }
      this.$API.receiptAndDelivery
        .getFactoryInfo(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.siteCode = itemData.siteCode
        this.searchFormModel.siteName = itemData.siteName
      } else {
        this.searchFormModel.siteCode = ''
        this.searchFormModel.siteName = ''
      }
    },
    dataTimeChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'S'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[flag + 'E'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[flag + 'S'] = null
        this.searchFormModel[flag + 'E'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).unix()
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selectedRecords = grid.getSelectedRecords()
      switch (toolbar.id) {
        case 'export':
          this.handleExport()
          break
        case 'Copy':
          this.handleCopy(selectedRecords)
          break
      }
    },
    handleCopy(selectedRecords) {
      const deliveryCodeList = selectedRecords.map((i) => {
        return i.deliveryCode
      })
      const str = [...new Set(deliveryCodeList)].join(' ')
      this.copyToClipboard(str)
      this.$refs.templateRef.$parent.$parent.currentTabIndex = 0
      sessionStorage.setItem('copyDeliveryCodeList', str)
    },
    copyToClipboard(str) {
      const el = document.createElement('textarea')
      el.value = str
      el.setAttribute('readonly', '')
      el.style.position = 'absolute'
      el.style.left = '-9999px'
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)
    },
    handleExport() {
      let obj = JSON.parse(sessionStorage.getItem(this.componentConfig[0].gridId))?.visibleCols
      const headerMap = {}
      if (obj !== undefined && obj.length) {
        obj?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      } else {
        this.componentConfig[0].grid.columnData?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      }
      let params = {
        page: { current: 1, size: 10000 },
        ...this.searchFormModel,
        headerMap
      }
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .buyerOrderDeliveryQueryExportNewTv(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'status') {
            this.searchFormModel[key] = [2]
          }
        }
      }
    },
    handleClickCellTitle(args) {
      const { field, data } = args
      if (field === 'deliveryCode') {
        this.getDeliverDataById(data.deliveryId)
      }
    },
    getDeliverDataById(id) {
      let params = { id }
      this.$API.receiptAndDelivery.getDeliveyDataTV(params).then((res) => {
        if (res.code == 200) {
          this.goToDetail({
            headerInfo: res.data
          })
        }
      })
    },
    goToDetail(data) {
      localStorage.setItem('lastTabIndex', 0)
      const { headerInfo } = data
      const deliverListData = {
        headerInfo
      }
      localStorage.setItem('deliverListData', JSON.stringify(deliverListData))
      this.$router.push({
        name: 'deliver-detail-tv',
        query: {
          timeStamp: new Date().getTime()
        }
      })
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>
