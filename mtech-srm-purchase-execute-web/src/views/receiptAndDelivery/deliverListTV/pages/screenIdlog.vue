<!-- 送货单明细tab -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel">
          <mt-form-item prop="deliveryCode" :label="$t('送货单号')">
            <mt-input
              v-model="searchFormModel.deliveryCode"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="syncStatus" :label="$t('校验状态')">
            <mt-select
              style="width: 100%"
              v-model="searchFormModel.syncStatus"
              :placeholder="$t('请选择校验状态')"
              :show-clear-button="true"
              :data-source="statusOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="supplierCode" :label="$t('供应商编码')">
            <mt-input
              v-model="searchFormModel.supplierCode"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="supplierName" :label="$t('供应商名称')">
            <mt-input
              v-model="searchFormModel.supplierName"
              :placeholder="$t('支持单个模糊搜索')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="palletNo" :label="$t('栈板号')">
            <mt-input
              v-model="searchFormModel.palletNo"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="boxNo" :label="$t('箱号')">
            <mt-input
              v-model="searchFormModel.boxNo"
              :placeholder="$t('支持单个精准搜索')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="diaphragmNo" :label="$t('膜片号')">
            <mt-input
              v-model="searchFormModel.diaphragmNo"
              :placeholder="$t('支持单个精准搜索')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="itemCode" :label="$t('物料编码')">
            <mt-input
              v-model="searchFormModel.itemCode"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="produceDate" :label="$t('生产日期')">
            <mt-date-range-picker
              v-model="searchFormModel.produceDate"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              @change="(e) => dataTimeChange(e, 'produceDate')"
            />
          </mt-form-item>
          <mt-form-item prop="purchaseOrderNo" :label="$t('采购订单号')">
            <mt-input
              v-model="searchFormModel.purchaseOrderNo"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="doNo" :label="$t('DO单号')">
            <mt-input
              v-model="searchFormModel.doNo"
              :placeholder="$t('支持单个精准搜索')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="doNo2" :label="$t('DO单号2')">
            <mt-input
              v-model="searchFormModel.doNo2"
              :placeholder="$t('支持单个精准搜索')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="level" :label="$t('等级')">
            <mt-input
              v-model="searchFormModel.level"
              :placeholder="$t('支持单个精准搜索')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="versionNo" :label="$t('版本号')">
            <mt-input
              v-model="searchFormModel.versionNo"
              :placeholder="$t('支持单个精准搜索')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="idSyncFailDesc" :label="$t('校验失败原因')">
            <mt-input
              v-model="searchFormModel.idSyncFailDesc"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="createTime" :label="$t('创建日期')">
            <mt-date-range-picker
              v-model="searchFormModel.createTime"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              @change="(e) => dataTimeChange(e, 'createTime')"
            />
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { componentConfig } from '../config/screenIdlog'
import { getHeadersFileName, download } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    // const startDate = dayjs().subtract(10, 'day')
    return {
      componentConfig: componentConfig(this),
      searchFormModel: {
        // createTime: [new Date(startDate), new Date()],
        // createTimeE: this.getUnix(new Date(dayjs(new Date()).format('YYYY-MM-DD 23:59:59'))),
        // createTimeS: this.getUnix(
        //   new Date(dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00'))
        // )
      },
      statusOptions: [
        { text: this.$t('失败'), value: 1 },
        { text: this.$t('正常'), value: 0 }
      ]
    }
  },
  mounted() {},
  methods: {
    dataTimeChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'From'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[flag + 'To'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[flag + 'From'] = null
        this.searchFormModel[flag + 'To'] = null
      }
    },
    getUnix(val) {
      return new Date(val).getTime()
    },
    handleClickToolBar(e) {
      // const { toolbar, grid } = e
      // const selectedRecords = grid.getSelectedRecords()
      switch (e.toolbar.id) {
        case 'export':
          this.handleExport()
          break
      }
    },
    handleExport() {
      this.$dialog({
        modal: () => import('../components/exportDialog.vue'),
        data: {
          title: this.$t('导出分页信息')
        },
        success: (page) => {
          let obj = JSON.parse(sessionStorage.getItem(this.componentConfig[0].gridId))?.visibleCols
          const headerMap = {}
          if (obj !== undefined && obj.length) {
            obj?.forEach((i) => {
              if (i.field !== 'customChecked' && i.field !== 'id') {
                headerMap[i.field] = i.headerText
              }
            })
          } else {
            this.componentConfig[0].grid.columnData?.forEach((i) => {
              if (i.field !== 'customChecked' && i.field !== 'id') {
                headerMap[i.field] = i.headerText
              }
            })
          }
          let params = {
            page,
            ...this.searchFormModel,
            headerMap
          }
          this.apiStartLoading()
          this.$API.receiptAndDelivery
            .buyerscreenIdlogExportTv(params)
            .then((res) => {
              this.apiEndLoading()
              const fileName = getHeadersFileName(res)
              download({ fileName: `${fileName}`, blob: res.data })
            })
            .catch(() => {
              this.apiEndLoading()
            })
            .finally(() => {
              this.apiEndLoading()
            })
        }
      })
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>
