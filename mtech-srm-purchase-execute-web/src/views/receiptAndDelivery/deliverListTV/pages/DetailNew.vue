<!-- 送货单明细tab -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig1"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      @handleClickCellTitle="handleClickCellTitle"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
          <mt-form-item prop="deliveryCode" :label="$t('送货单号')">
            <mt-input
              v-model="searchFormModel.deliveryCode"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="createTime" :label="$t('创建日期')">
            <mt-date-range-picker
              v-model="searchFormModel.createTime"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              @change="(e) => dataTimeChange(e, 'createTime')"
            />
          </mt-form-item>
          <mt-form-item prop="status" :label="$t('状态')">
            <mt-multi-select
              style="width: 100%"
              v-model="searchFormModel.status"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              :show-select-all="true"
              :data-source="statusOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="companyCodes" :label="$t('公司编码')">
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.companyCodes"
              url="/srm-purchase-execute/tenant/common/permission/queryCompanyList"
              multiple
              :placeholder="$t('请选择公司')"
              :fields="{ text: 'dimensionNameValue', value: 'dimensionCodeValue' }"
              records-position="data"
              params-key="keyWord"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="siteCodes" :label="$t('工厂编码')">
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.siteCodes"
              url="/srm-purchase-execute/tenant/common/permission/querySiteList"
              multiple
              :placeholder="$t('请选择工厂')"
              :fields="{ text: 'dimensionNameValue', value: 'dimensionCodeValue' }"
              records-position="data"
              params-key="keyWord"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="supplierCodes" :label="$t('供应商编码')">
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.supplierCodes"
              url="/masterDataManagement/tenant/supplier/paged-query"
              multiple
              :placeholder="$t('请选择供应商')"
              :fields="{ text: 'supplierName', value: 'supplierCode' }"
              :search-fields="['supplierName', 'supplierCode']"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="itemCode" :label="$t('物料编码')">
            <mt-input
              v-model="searchFormModel.itemCode"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="itemName" :label="$t('物料名称')">
            <mt-input
              v-model="searchFormModel.itemName"
              :placeholder="$t('支持模糊搜索')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="orderCode" :label="$t('采购订单号')">
            <mt-input
              v-model="searchFormModel.orderCode"
              :placeholder="$t('支持粘贴多个精准查询')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="lineNo" :label="$t('采购订单行号')" label-style="top">
            <mt-input
              v-model="searchFormModel.lineNo"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="onWayStatus" :label="$t('在途状态')">
            <mt-multi-select
              v-model="searchFormModel.onWayStatus"
              :placeholder="$t('请选择')"
              :show-select-all="true"
              :show-clear-button="true"
              :data-source="onWayStatusOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="buyerOrgCodes" :label="$t('采购组')">
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.buyerOrgCodes"
              :url="$API.masterData.getBusinessGroupAuthFuzzyUrl"
              multiple
              :placeholder="$t('请选择采购组')"
              :fields="{ text: 'groupName', value: 'groupCode' }"
              records-position="data"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="workCenterCodes" :label="$t('工作中心')">
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.workCenterCodes"
              url="/masterDataManagement/tenant/work-center/paged-query"
              multiple
              :placeholder="$t('请选择')"
              :fields="{ text: 'workCenterName', value: 'workCenterCode' }"
              :search-fields="['workCenterName', 'workCenterCode']"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="saleOrderNo" :label="$t('销售订单号')" label-style="top">
            <mt-input
              v-model="searchFormModel.saleOrderNo"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="sendTime" :label="$t('发货日期')">
            <mt-date-range-picker
              v-model="searchFormModel.sendTime"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              @change="(e) => dataTimeChange(e, 'sendTime')"
            />
          </mt-form-item>
          <mt-form-item prop="demandDate" :label="$t('需求日期')">
            <mt-date-range-picker
              v-model="searchFormModel.demandDate"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              @change="(e) => dataTimeChange(e, 'demandDate')"
            />
          </mt-form-item>
          <mt-form-item prop="virtualSupplierCode" :label="$t('虚拟供应商代码')">
            <mt-input
              v-model="searchFormModel.virtualSupplierCode"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="associatedNo" :label="$t('关联单据编号')">
            <mt-input
              v-model="searchFormModel.associatedNo"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="associateOuterDocNo" :label="$t('来源单号')">
            <mt-input
              v-model="searchFormModel.associateOuterDocNo"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="doNo" :label="$t('来源单号2')">
            <mt-input
              v-model="searchFormModel.doNo"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="deliveryOrderType" :label="$t('送货单类型')">
            <mt-multi-select
              v-model="searchFormModel.deliveryOrderType"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              :show-select-all="true"
              :data-source="deliveryOrderTypeOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="postingDate" :label="$t('过账日期')">
            <mt-date-range-picker
              v-model="searchFormModel.postingDate"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              @change="(e) => dataTimeChange(e, 'postingDate')"
            />
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import {
  componentConfig1,
  StatusOptions,
  onWayStatusOptions,
  DeliveryTypeOptions
} from '../config/detail'
import { getHeadersFileName, download } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    const startDate = dayjs().subtract(1, 'month')
    return {
      componentConfig1: componentConfig1(this),
      searchFormModel: {
        // status: [2],
        createTime: [new Date(startDate), new Date()],
        createTimeTo: this.getUnix(new Date(dayjs(new Date()).format('YYYY-MM-DD 23:59:59'))),
        createTimeFrom: this.getUnix(
          new Date(dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00'))
        )
      },
      searchFormRules: {},
      deliveryOrderTypeOptions: DeliveryTypeOptions,
      statusOptions: StatusOptions,
      onWayStatusOptions
    }
  },
  methods: {
    dataTimeChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'From'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[flag + 'To'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[flag + 'From'] = null
        this.searchFormModel[flag + 'To'] = null
      }
    },
    getUnix(val) {
      return new Date(val).getTime()
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selectedRecords = grid.getSelectedRecords()
      switch (toolbar.id) {
        case 'export':
          this.handleExport()
          break
        case 'Copy':
          this.handleCopy(selectedRecords)
          break
      }
    },
    handleCopy(selectedRecords) {
      const deliveryCodeList = selectedRecords.map((i) => {
        return i.deliveryCode
      })
      const str = [...new Set(deliveryCodeList)].join(' ')
      this.copyToClipboard(str)
      this.$refs.templateRef.$parent.$parent.currentTabIndex = 0
      sessionStorage.setItem('copyDeliveryCodeList', str)
    },
    copyToClipboard(str) {
      const el = document.createElement('textarea')
      el.value = str
      el.setAttribute('readonly', '')
      el.style.position = 'absolute'
      el.style.left = '-9999px'
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)
    },
    handleExport() {
      let obj = JSON.parse(sessionStorage.getItem(this.componentConfig1[0].gridId))?.visibleCols
      const headerMap = {}
      if (obj !== undefined && obj.length) {
        obj?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      } else {
        this.componentConfig1[0].grid.columnData?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      }
      const params = {
        headerMap,
        page: { current: 1, size: 5000 },
        ...this.searchFormModel,
        sourceFrom: 3,
        tenantType: 1
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery.buyerOrderDeliveryQueryNewExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
      return
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'status') {
            this.searchFormModel[key] = [2]
          }
        }
      }
    },
    handleClickCellTitle(args) {
      const { field, data } = args
      if (field === 'deliveryCode') {
        this.getDeliverDataById(data.deliveryId)
      }
    },
    getDeliverDataById(id) {
      let params = { id }
      this.$API.receiptAndDelivery.getDeliveyDataTV(params).then((res) => {
        if (res.code == 200) {
          this.goToDetail({
            headerInfo: res.data
          })
        }
      })
    },
    goToDetail(data) {
      localStorage.setItem('lastTabIndex', 0)
      const { headerInfo } = data
      const deliverListData = {
        headerInfo
      }
      localStorage.setItem('deliverListData', JSON.stringify(deliverListData))
      this.$router.push({
        name: 'deliver-detail-tv',
        query: {
          timeStamp: new Date().getTime()
        }
      })
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>
