<!-- 送货单列表tab -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickCellTool="handleClickCellTool"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
          <mt-form-item prop="deliveryCode" :label="$t('送货单号')">
            <mt-input
              v-model="searchFormModel.deliveryCode"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="createTime" :label="$t('创建日期')">
            <mt-date-range-picker
              v-model="searchFormModel.createTime"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              @change="(e) => dataTimeChange(e, 'createTime')"
            />
          </mt-form-item>
          <mt-form-item prop="status" :label="$t('状态')">
            <mt-multi-select
              v-model="searchFormModel.status"
              type="multipleChoice"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              :show-select-all="true"
              :data-source="statusOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="siteCode" :label="$t('工厂')">
            <!-- <debounce-filter-select
              v-model="searchFormModel.siteCode"
              :request="postSiteFuzzyQuery"
              :data-source="siteOptions"
              :show-clear-button="true"
              :fields="{ text: 'theCodeName', value: 'siteCode' }"
              :value-template="siteCodeValueTemplate"
              @change="siteCodeChange"
              :placeholder="$t('请选择')"
            /> -->
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.siteCode"
              url="/srm-purchase-execute/tenant/common/permission/querySiteList"
              :placeholder="$t('请选择工厂')"
              :fields="{ text: 'dimensionNameValue', value: 'dimensionCodeValue' }"
              records-position="data"
              params-key="keyWord"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="onWayStatus" :label="$t('在途状态')">
            <mt-multi-select
              v-model="searchFormModel.onWayStatus"
              type="multipleChoice"
              :placeholder="$t('请选择')"
              :show-select-all="true"
              :show-clear-button="true"
              :data-source="onWayStatusOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="supplierCodes" :label="$t('供应商编码')">
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.supplierCodes"
              url="/masterDataManagement/tenant/supplier/paged-query"
              multiple
              :placeholder="$t('请选择供应商')"
              :fields="{ text: 'supplierName', value: 'supplierCode' }"
              :search-fields="['supplierName', 'supplierCode']"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="sendTime" :label="$t('发货日期')">
            <mt-date-range-picker
              v-model="searchFormModel.sendTime"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              @change="(e) => dataTimeChange(e, 'sendTime')"
            />
          </mt-form-item>
          <mt-form-item prop="receiveAddressName" :label="$t('收货地址')">
            <mt-input
              v-model="searchFormModel.receiveAddressName"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="carNo" :label="$t('车牌号')">
            <mt-input
              v-model="searchFormModel.carNo"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="deliveryOrderType" :label="$t('送货单类型')">
            <mt-multi-select
              v-model="searchFormModel.deliveryOrderType"
              type="multipleChoice"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              :data-source="deliveryOrderTypeOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="printFlag" :label="$t('是否已打印')">
            <mt-select
              v-model="searchFormModel.printFlag"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              :data-source="isPrintOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="forecastFlag" :label="$t('是否可预约')">
            <mt-select
              v-model="searchFormModel.forecastFlag"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              :data-source="canDateOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="companyCode" :label="$t('公司')">
            <!-- <debounce-filter-select
              v-model="searchFormModel.companyCode"
              :request="getCompany"
              :data-source="companyOptions"
              :fields="{ text: 'theCodeName', value: 'orgCode' }"
              :value-template="companyCodeValueTemplate"
              :show-clear-button="true"
              :allow-filtering="true"
              @change="companyCodeChange"
              :placeholder="$t('请选择')"
            /> -->
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.companyCode"
              url="/srm-purchase-execute/tenant/common/permission/queryCompanyList"
              :placeholder="$t('请选择公司')"
              :fields="{ text: 'dimensionNameValue', value: 'dimensionCodeValue' }"
              records-position="data"
              params-key="keyWord"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="buyerOrgCode" :label="$t('采购组')">
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.buyerOrgCode"
              :url="$API.masterData.getBusinessGroupAuthFuzzyUrl"
              multiple
              :placeholder="$t('请选择采购组')"
              :fields="{ text: 'groupName', value: 'groupCode' }"
              records-position="data"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="outsourcedType" :label="$t('委外方式')">
            <mt-multi-select
              v-model="searchFormModel.outsourcedType"
              type="multipleChoice"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              :show-select-all="true"
              :data-source="outsourcedTypeOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="syncWmsStatus" :label="$t('WMS同步状态')">
            <mt-multi-select
              v-model="searchFormModel.syncWmsStatus"
              type="multipleChoice"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              :show-select-all="true"
              :data-source="syncStatusOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="syncSapStatus" :label="$t('SAP同步状态')">
            <mt-multi-select
              v-model="searchFormModel.syncSapStatus"
              type="multipleChoice"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              :show-select-all="true"
              :data-source="syncStatusOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="syncQmsStatus" :label="$t('QMS同步状态')">
            <mt-multi-select
              v-model="searchFormModel.syncQmsStatus"
              type="multipleChoice"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              :show-select-all="true"
              :data-source="syncStatusOptions"
              :fields="{ text: 'text', value: 'value' }"
            />
          </mt-form-item>
          <mt-form-item prop="virtualSupplierCode" :label="$t('虚拟供应商代码')">
            <mt-input
              v-model="searchFormModel.virtualSupplierCode"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="associatedNo" :label="$t('关联单据编号')">
            <mt-input
              v-model="searchFormModel.associatedNo"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="associateOuterDocNo" :label="$t('来源单号')">
            <mt-input
              v-model="searchFormModel.associateOuterDocNo"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="doNo" :label="$t('来源单号2')">
            <mt-input
              v-model="searchFormModel.doNo"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { componentConfig } from '../config/list'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import { getHeadersFileName, download } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    const startDate = dayjs().subtract(1, 'month')
    const deliveryCodeList = sessionStorage.getItem('copyDeliveryCodeList')
    return {
      componentConfig: componentConfig(this),
      searchFormModel: {
        createTime: [new Date(startDate), new Date()],
        createTimeE: this.getUnix(new Date(dayjs(new Date()).format('YYYY-MM-DD 23:59:59'))),
        createTimeS: this.getUnix(
          new Date(dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00'))
        ),
        deliveryCode: deliveryCodeList ? deliveryCodeList : ''
      },
      searchFormRules: {},
      deliveryOrderTypeOptions: [
        { text: this.$t('采购订单'), value: 1 },
        { text: this.$t('交货计划'), value: 2 },
        { text: this.$t('JIT'), value: 3 },
        { text: this.$t('无需求'), value: 4 },
        { text: this.$t('vmi'), value: 5 },
        { text: this.$t('钢材'), value: 6 },
        { text: this.$t('屏发货指导'), value: 7 },
        { text: this.$t('无PO送货'), value: 8 }
      ],
      statusOptions: [
        { text: this.$t('送货中'), value: 2 },
        { text: this.$t('已完成'), value: 3 },
        { text: this.$t('已取消'), value: 4 },
        { text: this.$t('已关闭'), value: 5 }
      ],
      onWayStatusOptions: [
        { text: this.$t('未出发'), value: 0 },
        { text: this.$t('已入园'), value: 1 },
        { text: this.$t('已出发'), value: 2 },
        { text: this.$t('已报到'), value: 3 },
        { text: this.$t('已取消'), value: 4 },
        { text: this.$t('已关闭'), value: 5 }
      ],
      isPrintOptions: [
        { text: this.$t('未打印'), value: 0 },
        { text: this.$t('已打印'), value: 1 }
      ],
      canDateOptions: [
        { text: this.$t('可预约'), value: 0 },
        { text: this.$t('不可预约'), value: 1 }
      ],
      outsourcedTypeOptions: [
        { text: this.$t('标准委外'), value: '0' },
        { text: this.$t('销售委外'), value: '1' },
        { text: this.$t('非委外'), value: '2' },
        { text: this.$t('工序委外'), value: '3' }
      ],
      syncStatusOptions: [
        { text: this.$t('未同步'), value: 0 },
        { text: this.$t('同步中'), value: 1 },
        { text: this.$t('同步成功'), value: 2 },
        { text: this.$t('同步失败'), value: 3 }
      ],
      companyOptions: [],
      companyCodeValueTemplate: codeNameColumn({
        firstKey: 'orgCode',
        secondKey: 'orgName'
      }),
      siteOptions: [],
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }),
      buyerOrgOptions: [],
      buyerOrgValueTemplate: codeNameColumn({
        firstKey: 'groupCode',
        secondKey: 'groupName'
      })
    }
  },
  mounted() {
    this.getCompany({ text: '' })
    this.postSiteFuzzyQuery({ text: '' })
    this.getBuyerOrgList({ text: '' })
  },
  methods: {
    getCompany(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true,
        organizationIds: []
      }
      this.$API.masterData
        .OrgFindSpecifiedChildrenLevelOrgs(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.companyOptions = addCodeNameKeyInList({
              firstKey: 'orgCode',
              secondKey: 'orgName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.companyOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    companyCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.companyId = itemData.id
        this.searchFormModel.companyCode = itemData.orgCode
        this.searchFormModel.companyName = itemData.orgName
      } else {
        this.searchFormModel.companyId = ''
        this.searchFormModel.companyCode = ''
        this.searchFormModel.companyName = ''
      }
    },
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        page: {
          current: 1,
          pages: 0,
          size: 20
        }
      }
      this.$API.receiptAndDelivery
        .getFactoryInfo(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.siteCode = itemData.siteCode
        this.searchFormModel.siteName = itemData.siteName
      } else {
        this.searchFormModel.siteCode = ''
        this.searchFormModel.siteName = ''
      }
    },
    getBuyerOrgList(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        groupTypeCode: 'BG001CG'
      }
      this.$API.masterData
        .getbussinessGroup(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.buyerOrgOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.buyerOrgOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    buyerOrgCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.buyerOrgCode = itemData.groupCode
        this.searchFormModel.buyerOrgName = itemData.groupName
      } else {
        this.searchFormModel.buyerOrgCode = ''
        this.searchFormModel.buyerOrgName = ''
      }
    },
    dataTimeChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'S'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[flag + 'E'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[flag + 'S'] = null
        this.searchFormModel[flag + 'E'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).unix()
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selectedRecords = grid.getSelectedRecords()
      let _id = selectedRecords.map((item) => {
        return item.id
      })
      if (
        ['Colse', 'Print', 'Sync', 'BatchDownload', 'EditIHNumber'].includes(toolbar.id) &&
        selectedRecords.length === 0
      ) {
        this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
        return
      }
      switch (toolbar.id) {
        case 'Colse':
          if (selectedRecords.some((item) => item.status?.value != 2)) {
            this.$toast({
              content: this.$t('只能选择送货中的数据进行关闭操作！'),
              type: 'warning'
            })
          } else {
            this.handleClose(_id)
          }
          break
        case 'Print':
          if (selectedRecords.some((item) => item.status?.value == 4 || item.status?.value == 5)) {
            this.$toast({
              content: this.$t('不可以打印已取消和已关闭的单据！'),
              type: 'warning'
            })
          } else {
            this.$dialog({
              modal: () => import('../components/printDialog.vue'),
              data: {
                title: this.$t('送货单打印纸张选择')
              },
              success: (type) => {
                this.handlePrint(type, _id)
              }
            })
          }
          break
        case 'Sync':
          this.handleSync(selectedRecords)
          break
        case 'EditIHNumber':
          this.handleEditIH(selectedRecords)
          break
        case 'Export1':
          this.handleExport()
          break
        case 'BatchDownload':
          if (
            selectedRecords.some(
              (item) =>
                !item.screenIdCount &&
                !item.supInvoiceCount &&
                !item.hkInvoiceCount &&
                !item.billOfLadingCount &&
                !item.saleOrderCount &&
                !item.deliveryScanningCount
            )
          ) {
            this.$toast({
              content: this.$t('不可以选择所有附件上传数为0的单据！'),
              type: 'warning'
            })
          } else {
            this.$dialog({
              modal: () => import('../components/batchDownloadDialog.vue'),
              data: {
                title: this.$t('批量下载附件')
              },
              success: (modelForm) => {
                this.batchDownload(_id, modelForm)
              }
            })
          }
          break
      }
    },
    handleExport() {
      let obj = JSON.parse(sessionStorage.getItem(this.componentConfig[0].gridId))?.visibleCols
      const headerMap = {}
      if (obj !== undefined && obj.length) {
        obj?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      } else {
        this.componentConfig[0].grid.columnData?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      }
      const params = {
        page: {
          current: 1,
          size: 9999
        },
        ...this.searchFormModel,
        headerMap
      }
      this.apiStartLoading()
      this.$API.receiptAndDelivery.postBuyerOrderDeliveryExport(params).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleEditIH(selectedRecords) {
      this.$dialog({
        modal: () => import('../components/editIHNumberDialog.vue'),
        data: {
          title: this.$t('编辑IH号'),
          selectedRecords
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    batchDownload(ids, modelForm) {
      const params = {
        body: ids,
        ...modelForm
      }
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postBuyerOrderDeliveryBatchDownload(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    handleClose(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认关闭选中的数据？')
        },
        success: () => {
          this.$API.receiptAndDelivery.postBuyerOrderDeliveryCloseTV(ids).then((res) => {
            if (res?.code == 200) {
              this.$toast({ content: this.$t('关闭成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      })
    },
    handlePrint(type, ids) {
      let params = {
        templateType: type,
        ids
      }
      this.$API.receiptAndDelivery.deliveryBuyerPrintApi(params).then((res) => {
        if (res && res.code === 500) {
          this.$toast({ content: res.msg, type: 'error' })
          return
        }
        const content = res.data
        this.pdfUrl = window.URL.createObjectURL(
          new Blob([content], { type: 'text/html;charset=utf-8' })
        )
        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = this.pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
      }, 100)
    },
    handleSync(data) {
      let _ids = []
      let _flag = true
      data.forEach((item) => {
        if (item.wmsSyncStatus === 1) {
          _flag = false
        }
        _ids.push(item.deliveryCode)
      })
      if (!_flag) {
        this.$toast({
          content: this.$t('送货单已成功同步WMS，无需重复同步'),
          type: 'warning'
        })
        return
      }
      this.$API.purchaseCoordination.deliverySynchronousWms(_ids).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('同步成功'), type: 'success' })
        } else {
          this.$toast({ content: this.$t('同步失败'), type: 'warning' })
        }
        this.$refs.templateRef.refreshCurrentGridData()
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
      })
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    handleClickCellTitle(args) {
      const { field, data } = args
      if (field === 'deliveryCode') {
        this.goToDetail({ headerInfo: data })
      }
    },
    goToDetail(data) {
      localStorage.setItem('lastTabIndex', 0)
      const { headerInfo } = data
      const deliverListData = {
        headerInfo // 头部信息
      }
      localStorage.setItem('deliverListData', JSON.stringify(deliverListData))
      this.$router.push({
        name: 'deliver-detail-tv',
        query: {
          timeStamp: new Date().getTime()
        }
      })
    },
    handleClickCellTool(e) {
      if (e.tool.id === 'screenIdImport') {
        // 屏ID导入
        this.handleScreenIdImport(e.data.id)
        return
      }
      if (e.tool.id === 'search') {
        // 屏ID列表查看
        this.screenIdSearch(e.data)
        return
      }
      // 附件
      if (
        ['sup_invoice', 'hk_invoice', 'bill_of_lading', 'sale_order', 'delivery_scanning'].includes(
          e.tool.id
        )
      ) {
        this.handleFile(e.data, e.tool.id)
      }
    },
    handleScreenIdImport(id) {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.receiptAndDelivery.supDeliveryImport,
          downloadTemplateApi: this.$API.receiptAndDelivery.supDeliveryDownloadTemplate,
          paramsKey: 'excel',
          asyncParams: {
            id
          }
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    screenIdSearch(row) {
      this.$dialog({
        modal: () => import('../components/screenIdList.vue'),
        data: {
          title: this.$t('屏ID查看'),
          row
        }
      })
    },
    handleFile(row, type) {
      const typeMap = {
        sup_invoice: 5,
        hk_invoice: 6,
        bill_of_lading: 7,
        sale_order: 8,
        delivery_scanning: 9
      }
      this.$dialog({
        modal: () => import('../components/fileManage.vue'),
        data: {
          title: this.$t('附件上传'),
          id: row.id,
          type: typeMap[type],
          row
        },
        close: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('copyDeliveryCodeList')
  }
}
</script>
