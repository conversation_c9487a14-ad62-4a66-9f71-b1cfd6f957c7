import { i18n } from '@/main.js'
import Vue from 'vue'
import { codeNameColumn } from '@/utils/utils'
import { timeDate } from './columnComponent'

const columnData = () => {
  const column = [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false,
      allowEditing: false,
      allowResizing: false
    },
    {
      field: 'id',
      headerText: 'id',
      width: 0,
      visible: false,
      allowEditing: false,
      isPrimaryKey: true
    },
    {
      field: 'deliveryCode',
      headerText: i18n.t('送货单号'),
      width: '165',
      cellTools: []
    },
    {
      field: 'deliveryLineNo',
      headerText: i18n.t('行号'),
      width: '100'
    },
    {
      field: 'statusDesc',
      headerText: i18n.t('状态'),
      width: '80',
      cellTools: []
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      width: '120'
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      width: '200'
    },
    {
      field: 'deliveryQuantity',
      headerText: i18n.t('本次送货数量'),
      width: '120'
    },
    {
      field: 'receiveQuantity',
      headerText: i18n.t('收货数量'),
      width: '100'
    },
    {
      field: 'cumReceiveQty',
      headerText: i18n.t('wms实收数量'),
      width: '120'
    },
    {
      field: 'distributionReceiveQty',
      headerText: i18n.t('wms首次收货数量'),
      width: '120'
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: '110'
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: '200'
    },
    {
      field: 'orderCode',
      headerText: i18n.t('采购订单号'),
      width: '120'
    },
    {
      field: 'lineNo',
      headerText: i18n.t('采购订单行号'),
      width: '120'
    },
    {
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      width: '100'
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      width: '200'
    },
    {
      field: 'warehouseCode',
      headerText: i18n.t('库存地点'),
      width: '150',
      template: codeNameColumn({
        firstKey: 'warehouseCode',
        secondKey: 'warehouseName'
      })
    },
    {
      field: 'sendTime',
      headerText: i18n.t('发货日期'),
      width: '100',
      template: timeDate({
        dataKey: 'sendTime',
        isDate: true
      })
    },
    {
      field: 'onWayStatusDesc',
      headerText: i18n.t('在途状态'),
      width: '100'
    },
    {
      field: 'carNo',
      headerText: i18n.t('车牌号'),
      width: '100'
    },
    {
      field: 'vehicleLogistics',
      headerText: i18n.t('车辆物流'),
      width: '100',
      template: () => {
        return {
          template: Vue.component('vehicleLogistics', {
            template: `
                <span v-if="data.carNo" style="color: #6386c1; cursor: pointer;" @click="toLogistics(data)">{{ $t('查看物流') }}</span>`,
            methods: {
              toLogistics(e) {
                const params = {
                  ztpno: e.deliveryCode.toString(),
                  busCode: e.forecastCode,
                  busNum: e.carNo
                }
                this.$API.receiptAndDelivery.purQueryVehicleLogistics(params).then((res) => {
                  if (res.code === 200) {
                    window.open(res.data.mapURL)
                  }
                })
              }
            }
          })
        }
      }
    },
    {
      field: 'companyCode',
      headerText: i18n.t('公司编码'),
      width: '100'
    },
    {
      field: 'companyName',
      headerText: i18n.t('公司名称'),
      width: '200'
    },
    {
      field: 'associatedNo',
      headerText: i18n.t('关联单据编号')
    },
    {
      field: 'associateOuterDocNo',
      headerText: i18n.t('来源单号')
    },
    {
      field: 'doNo',
      headerText: i18n.t('来源单号2')
    },
    {
      field: 'virtualSupplierCode',
      headerText: i18n.t('虚拟供应商代码'),
      width: '140'
    },
    {
      field: 'jit',
      headerText: i18n.t('是否JIT'),
      width: '100',
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('否'),
          1: i18n.t('是')
        }
      }
    },
    {
      field: 'jitDeliveryNumber',
      headerText: i18n.t('JIT编号'),
      width: '100'
    },
    {
      field: 'demandDate',
      headerText: i18n.t('需求日期'),
      width: '100',
      template: timeDate({
        dataKey: 'demandDate',
        isDate: true
      })
    },
    {
      field: 'rejectQuantity',
      headerText: i18n.t('拒绝数量'),
      width: '100'
    },
    {
      field: 'rejectReason',
      headerText: i18n.t('拒绝原因'),
      width: '100'
    },
    {
      field: 'inputDate',
      headerText: i18n.t('凭证创建日期'),
      width: '120',
      template: timeDate({
        dataKey: 'inputDate',
        isDate: true
      })
    },
    {
      field: 'postingDate',
      headerText: i18n.t('过账日期'),
      width: '100',
      template: timeDate({
        dataKey: 'postingDate',
        isDate: true
      })
    },
    {
      field: 'deliveryTypeDesc',
      headerText: i18n.t('送货单类型'),
      width: '105'
    },
    {
      field: 'deliveryNumber',
      headerText: i18n.t('交货编号'),
      width: '100'
    },
    {
      field: 'sendAddressName',
      headerText: i18n.t('发货地点'),
      width: '100'
    },
    {
      field: 'thirdTenantCode',
      headerText: i18n.t('第三方物流商'),
      width: '150',
      template: codeNameColumn({
        firstKey: 'thirdTenantCode',
        secondKey: 'thirdTenantName'
      })
    },
    {
      field: 'receiveSupplierCode',
      headerText: i18n.t('收货供应商'),
      width: '150',
      template: codeNameColumn({
        firstKey: 'receiveSupplierCode',
        secondKey: 'receiveSupplierName'
      })
    },
    {
      field: 'deliveryRemark',
      headerText: i18n.t('送货单备注'),
      width: '120'
    },
    {
      field: 'transferPlanName',
      headerText: i18n.t('计划员'),
      width: '100'
    },
    {
      field: 'buyerOrgCode',
      headerText: i18n.t('采购组'),
      width: '200',
      template: codeNameColumn({
        firstKey: 'buyerOrgCode',
        secondKey: 'buyerOrgName'
      })
    },
    {
      field: 'workOrderNo',
      headerText: i18n.t('关联工单号'),
      width: '120'
    },
    {
      field: 'saleOrderNo',
      headerText: i18n.t('关联销售订单号'),
      width: '120'
    },
    {
      field: 'saleOrderLineNo',
      headerText: i18n.t('关联销售订单行号'),
      width: '120'
    },
    {
      field: 'bomCode',
      headerText: i18n.t('BOM号'),
      width: '100'
    },
    {
      field: 'productCode',
      headerText: i18n.t('关联产品代码'),
      width: '120'
    },
    {
      field: 'workCenterCode',
      headerText: i18n.t('工作中心'),
      width: '150',
      template: codeNameColumn({
        firstKey: 'workCenterCode',
        secondKey: 'workCenterName'
      })
    },
    {
      field: 'rbcCode',
      headerText: i18n.t('RBC编码'),
      width: '100'
    },
    {
      field: 'bgType',
      headerText: i18n.t('BG类型'),
      width: '100'
    },
    {
      field: 'processName',
      headerText: i18n.t('工序名称'),
      width: '100'
    },
    {
      field: 'receiveAddressName',
      headerText: i18n.t('收货地址'),
      width: '100'
    },
    {
      field: 'unitCode',
      headerText: i18n.t('单位'),
      width: '100',
      template: codeNameColumn({
        firstKey: 'unitCode',
        secondKey: 'unitName'
      })
    },
    {
      field: 'batchCode',
      headerText: i18n.t('批次号'),
      width: '100'
    },
    {
      field: 'limitQuantity',
      headerText: i18n.t('限量数量'),
      width: '100'
    },
    {
      field: 'warehouseClerkName',
      headerText: i18n.t('仓管员'),
      width: '100'
    },
    {
      field: 'dispatcherName',
      headerText: i18n.t('调度员'),
      width: '100'
    },
    {
      field: 'wmsInstockQty',
      headerText: i18n.t('WMS入库数量'),
      width: '120'
    },
    {
      field: 'wmsInstockTime',
      headerText: i18n.t('WMS入库时间'),
      width: '120',
      template: timeDate({
        dataKey: 'wmsInstockTime',
        isDateTime: true
      })
    },
    {
      field: 'cancelPersonName',
      headerText: i18n.t('取消人'),
      width: '100'
    },
    {
      field: 'cancelTime',
      headerText: i18n.t('取消时间'),
      width: '100',
      template: timeDate({
        dataKey: 'cancelTime',
        isDateTime: true
      })
    },
    {
      field: 'closePersonName',
      headerText: i18n.t('关闭人'),
      width: '100'
    },
    {
      field: 'closeTime',
      headerText: i18n.t('关闭时间'),
      width: '100',
      template: timeDate({
        dataKey: 'closeTime',
        isDateTime: true
      })
    },
    {
      field: 'collectorMark',
      headerText: i18n.t('代收标识'),
      width: '100'
    },
    {
      field: 'collectorName',
      headerText: i18n.t('代收人'),
      width: '100'
    },
    {
      field: 'headRemark',
      headerText: i18n.t('头备注'),
      width: '100'
    },
    {
      field: 'remark',
      headerText: i18n.t('行备注'),
      width: '100'
    },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人'),
      width: '100'
    },
    {
      field: 'createTime',
      headerText: i18n.t('创建时间'),
      width: '100',
      template: timeDate({
        dataKey: 'createTime',
        isDateTime: true
      })
    }
  ]
  return column
}

export const componentConfig = (that) => {
  const config = [
    {
      isUseCustomSearch: true, // 是否使用自定义查询
      showArchive: true, // 是否显示归档查询
      isCustomSearchRules: true,
      useToolTemplate: false, // 此项不使用预置的表格操作按钮'新增、编辑、删除'
      useBaseConfig: false, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
      toolbar: {
        tools: [
          [
            {
              id: 'export',
              icon: 'icon_solid_Import',
              permission: ['O_02_1633'],
              title: i18n.t('导出')
            },
            {
              id: 'Copy',
              icon: 'icon_solid_Import',
              title: i18n.t('复制送货单号')
            }
          ],
          ['Setting']
        ]
      },
      gridId: 'ee3a3890-1c2e-4b31-9a44-3cc2ca59e20a',
      grid: {
        lineIndex: 1,
        allowPaging: true, // 是否使用内置分页器
        // virtualPageSize: 20, // 虚拟滚动，暂不开启
        // selectionSettings: {
        //   persistSelection: true,
        //   type: 'Multiple',
        //   checkboxOnly: true
        // },
        // enableVirtualization: true,
        columnData: columnData(that),
        asyncConfig: {
          url: '/srm-purchase-execute/tenant/delivery/item/buyer/page'
        },
        pageSettings: {
          currentPage: 1,
          pageSize: 20,
          pageSizes: [10, 20, 50, 100, 200],
          totalRecordsCount: 0
        }
      }
    }
  ]
  return config
}

// 1-新建,2-发货中,3-已完成,4-已取消,5-已关闭
export const StatusOptions = [
  { text: i18n.t('送货中'), value: 2, cssClass: '' },
  { text: i18n.t('已完成'), value: 3, cssClass: '' },
  { text: i18n.t('已取消'), value: 4, cssClass: '' },
  { text: i18n.t('已关闭'), value: 5, cssClass: '' }
]

export const onWayStatusOptions = [
  { text: i18n.t('未出发'), value: 0, cssClass: '' },
  { text: i18n.t('已入园'), value: 1, cssClass: '' },
  { text: i18n.t('已出发'), value: 2, cssClass: '' },
  { text: i18n.t('已报到'), value: 3, cssClass: '' },
  { text: i18n.t('已取消'), value: 4, cssClass: '' },
  { text: i18n.t('已关闭'), value: 5, cssClass: '' }
]

export const DeliveryTypeOptions = [
  { value: 1, text: i18n.t('采购订单'), cssClass: '' },
  { value: 2, text: i18n.t('交货计划'), cssClass: '' },
  { value: 3, text: i18n.t('JIT'), cssClass: '' },
  { value: 4, text: i18n.t('无需求'), cssClass: '' },
  { value: 5, text: i18n.t('vmi'), cssClass: '' },
  { value: 6, text: i18n.t('钢材'), cssClass: '' }
]

const columnData1 = () => {
  const column = [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false,
      allowEditing: false,
      allowResizing: false
    },
    {
      field: 'id',
      headerText: 'id',
      width: 0,
      visible: false,
      allowEditing: false,
      isPrimaryKey: true
    },
    {
      field: 'deliveryCode',
      headerText: i18n.t('送货单号'),
      width: '165',
      cellTools: []
    },
    {
      field: 'deliveryLineNo',
      headerText: i18n.t('行号'),
      width: '100'
    },
    {
      field: 'status',
      headerText: i18n.t('状态'),
      width: '80',
      valueConverter: {
        type: 'map',
        map: StatusOptions
      }
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      width: '120'
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      width: '200'
    },
    {
      field: 'deliveryQuantity',
      headerText: i18n.t('本次送货数量'),
      width: '120'
    },
    {
      field: 'receiveQuantity',
      headerText: i18n.t('收货数量'),
      width: '100'
    },
    {
      field: 'cumReceiveQty',
      headerText: i18n.t('wms实收数量'),
      width: '120'
    },
    {
      field: 'distributionReceiveQty',
      headerText: i18n.t('wms首次收货数量'),
      width: '120'
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: '110'
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: '200'
    },
    {
      field: 'orderCode',
      headerText: i18n.t('采购订单号'),
      width: '120'
    },
    {
      field: 'lineNo',
      headerText: i18n.t('采购订单行号'),
      width: '120'
    },
    {
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      width: '100'
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      width: '200'
    },
    {
      field: 'warehouseCode',
      headerText: i18n.t('库存地点'),
      width: '150',
      template: codeNameColumn({
        firstKey: 'warehouseCode',
        secondKey: 'warehouseName'
      })
    },
    {
      field: 'sendTime',
      headerText: i18n.t('发货日期'),
      width: '100',
      template: timeDate({
        dataKey: 'sendTime',
        isDate: true
      })
    },
    {
      field: 'onWayStatus',
      headerText: i18n.t('在途状态'),
      width: '100',
      valueConverter: {
        type: 'map',
        map: onWayStatusOptions
      }
    },
    {
      field: 'carNo',
      headerText: i18n.t('车牌号'),
      width: '100'
    },
    {
      field: 'vehicleLogistics',
      headerText: i18n.t('车辆物流'),
      width: '100',
      template: () => {
        return {
          template: Vue.component('vehicleLogistics', {
            template: `
                <span v-if="data.carNo" style="color: #6386c1; cursor: pointer;" @click="toLogistics(data)">{{ $t('查看物流') }}</span>`,
            methods: {
              toLogistics(e) {
                const params = {
                  ztpno: e.deliveryCode.toString(),
                  busCode: e.forecastCode,
                  busNum: e.carNo
                }
                this.$API.receiptAndDelivery.purQueryVehicleLogistics(params).then((res) => {
                  if (res.code === 200) {
                    window.open(res.data.mapURL)
                  }
                })
              }
            }
          })
        }
      }
    },
    {
      field: 'companyCode',
      headerText: i18n.t('公司编码'),
      width: '100'
    },
    {
      field: 'companyName',
      headerText: i18n.t('公司名称'),
      width: '200'
    },
    {
      field: 'associatedNo',
      headerText: i18n.t('关联单据编号')
    },
    {
      field: 'associateOuterDocNo',
      headerText: i18n.t('来源单号')
    },
    {
      field: 'doNo',
      headerText: i18n.t('来源单号2')
    },
    {
      field: 'virtualSupplierCode',
      headerText: i18n.t('虚拟供应商代码'),
      width: '140'
    },
    {
      field: 'jit',
      headerText: i18n.t('是否JIT'),
      width: '100',
      valueConverter: {
        type: 'map',
        map: [
          { value: 0, text: i18n.t('否'), cssClass: '' },
          { value: 1, text: i18n.t('是'), cssClass: '' }
        ]
      }
    },
    {
      field: 'jitDeliveryNumber',
      headerText: i18n.t('JIT编号'),
      width: '100'
    },
    {
      field: 'demandDate',
      headerText: i18n.t('需求日期'),
      width: '100',
      template: timeDate({
        dataKey: 'demandDate',
        isDate: true
      })
    },
    {
      field: 'rejectQuantity',
      headerText: i18n.t('拒绝数量'),
      width: '100'
    },
    {
      field: 'rejectReason',
      headerText: i18n.t('拒绝原因'),
      width: '100'
    },
    {
      field: 'inputDate',
      headerText: i18n.t('凭证创建日期'),
      width: '120',
      template: timeDate({
        dataKey: 'inputDate',
        isDate: true
      })
    },
    {
      field: 'postingDate',
      headerText: i18n.t('过账日期'),
      width: '100',
      template: timeDate({
        dataKey: 'postingDate',
        isDate: true
      })
    },
    {
      field: 'deliveryType',
      headerText: i18n.t('送货单类型'),
      width: '105',
      valueConverter: {
        type: 'map',
        map: DeliveryTypeOptions
      }
    },
    {
      field: 'deliveryNumber',
      headerText: i18n.t('交货编号'),
      width: '100'
    },
    {
      field: 'sendAddressName',
      headerText: i18n.t('发货地点'),
      width: '100'
    },
    {
      field: 'thirdTenantCode',
      headerText: i18n.t('第三方物流商'),
      width: '150',
      template: codeNameColumn({
        firstKey: 'thirdTenantCode',
        secondKey: 'thirdTenantName'
      })
    },
    {
      field: 'receiveSupplierCode',
      headerText: i18n.t('收货供应商'),
      width: '150',
      template: codeNameColumn({
        firstKey: 'receiveSupplierCode',
        secondKey: 'receiveSupplierName'
      })
    },
    {
      field: 'deliveryRemark',
      headerText: i18n.t('送货单备注'),
      width: '120'
    },
    {
      field: 'transferPlanName',
      headerText: i18n.t('计划员'),
      width: '100'
    },
    {
      field: 'buyerOrgCode',
      headerText: i18n.t('采购组'),
      width: '200',
      template: codeNameColumn({
        firstKey: 'buyerOrgCode',
        secondKey: 'buyerOrgName'
      })
    },
    {
      field: 'workOrderNo',
      headerText: i18n.t('关联工单号'),
      width: '120'
    },
    {
      field: 'saleOrderNo',
      headerText: i18n.t('关联销售订单号'),
      width: '120'
    },
    {
      field: 'saleOrderLineNo',
      headerText: i18n.t('关联销售订单行号'),
      width: '120'
    },
    {
      field: 'bomCode',
      headerText: i18n.t('BOM号'),
      width: '100'
    },
    {
      field: 'productCode',
      headerText: i18n.t('关联产品代码'),
      width: '120'
    },
    {
      field: 'workCenterCode',
      headerText: i18n.t('工作中心'),
      width: '150',
      template: codeNameColumn({
        firstKey: 'workCenterCode',
        secondKey: 'workCenterName'
      })
    },
    {
      field: 'rbcCode',
      headerText: i18n.t('RBC编码'),
      width: '100'
    },
    {
      field: 'bgType',
      headerText: i18n.t('BG类型'),
      width: '100'
    },
    {
      field: 'processName',
      headerText: i18n.t('工序名称'),
      width: '100'
    },
    {
      field: 'receiveAddressName',
      headerText: i18n.t('收货地址'),
      width: '100'
    },
    {
      field: 'unitCode',
      headerText: i18n.t('单位'),
      width: '100',
      template: codeNameColumn({
        firstKey: 'unitCode',
        secondKey: 'unitName'
      })
    },
    {
      field: 'batchCode',
      headerText: i18n.t('批次号'),
      width: '100'
    },
    {
      field: 'limitQuantity',
      headerText: i18n.t('限量数量'),
      width: '100'
    },
    {
      field: 'warehouseClerkName',
      headerText: i18n.t('仓管员'),
      width: '100'
    },
    {
      field: 'dispatcherName',
      headerText: i18n.t('调度员'),
      width: '100'
    },
    {
      field: 'wmsInstockQty',
      headerText: i18n.t('WMS入库数量'),
      width: '120'
    },
    {
      field: 'wmsInstockTime',
      headerText: i18n.t('WMS入库时间'),
      width: '120'
    },
    {
      field: 'cancelPersonName',
      headerText: i18n.t('取消人'),
      width: '100'
    },
    {
      field: 'cancelTime',
      headerText: i18n.t('取消时间'),
      width: '100',
      template: timeDate({
        dataKey: 'cancelTime',
        isDateTime: true
      })
    },
    {
      field: 'closePersonName',
      headerText: i18n.t('关闭人'),
      width: '100'
    },
    {
      field: 'closeTime',
      headerText: i18n.t('关闭时间'),
      width: '100',
      template: timeDate({
        dataKey: 'closeTime',
        isDateTime: true
      })
    },
    {
      field: 'collectorMark',
      headerText: i18n.t('代收标识'),
      width: '100'
    },
    {
      field: 'collectorName',
      headerText: i18n.t('代收人'),
      width: '100'
    },
    {
      field: 'headRemark',
      headerText: i18n.t('头备注'),
      width: '100'
    },
    {
      field: 'remark',
      headerText: i18n.t('行备注'),
      width: '100'
    },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人'),
      width: '100'
    },
    {
      field: 'createTime',
      headerText: i18n.t('创建时间'),
      width: '100',
      template: timeDate({
        dataKey: 'createTime',
        isDateTime: true
      })
    }
  ]
  return column
}

export const componentConfig1 = () => {
  const config = [
    {
      isUseCustomSearch: true, // 是否使用自定义查询
      showArchive: true, // 是否显示归档查询
      isCustomSearchRules: true,
      useToolTemplate: false, // 此项不使用预置的表格操作按钮'新增、编辑、删除'
      useBaseConfig: false, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
      toolbar: {
        tools: [
          [
            {
              id: 'export',
              icon: 'icon_solid_Import',
              title: i18n.t('导出')
            }
          ],
          ['Setting']
        ]
      },
      gridId: '31bf43c8-d887-4b7e-9af5-366e291fdb02',
      grid: {
        lineIndex: 1,
        allowPaging: true, // 是否使用内置分页器
        // virtualPageSize: 20, // 虚拟滚动，暂不开启
        // selectionSettings: {
        //   persistSelection: true,
        //   type: 'Multiple',
        //   checkboxOnly: true
        // },
        // enableVirtualization: true,
        columnData: columnData1(),
        asyncConfig: {
          url: `/statistics/tenant/buyer/delivery/item/view/v1/page`, // 采方送货单-采方查询送货单明细分页
          params: {
            sourceFrom: 3,
            tenantType: 1
          },
          serializeList: (list) => {
            if (list.length > 0) {
              list.forEach((item) => {
                let inputDate = item.inputDate
                if (item.inputDate && item.inputTime) {
                  inputDate = `${inputDate.split(' ')[0]} ${item.inputTime.split(' ')[1]}`
                }
                item.inputDate = inputDate
              })
            }
            return list
          }
        },
        pageSettings: {
          currentPage: 1,
          pageSize: 20,
          pageSizes: [10, 20, 50, 100, 200],
          totalRecordsCount: 0
        }
      }
    }
  ]
  return config
}
