import { i18n } from '@/main.js'
import Vue from 'vue'
import { codeNameColumn } from '@/utils/utils'
import { timeDate } from './columnComponent'

const columnData = () => {
  const column = [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false,
      allowEditing: false,
      allowResizing: false
    },
    {
      field: 'id',
      headerText: 'id',
      width: 0,
      visible: false,
      allowEditing: false,
      isPrimaryKey: true
    },
    {
      field: 'deliveryCode',
      headerText: i18n.t('送货单号'),
      width: '165',
      cellTools: []
    },
    {
      field: 'statusDesc',
      headerText: i18n.t('状态'),
      width: '80',
      cellTools: []
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: '110'
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: '200'
    },
    {
      field: 'onWayStatusDesc',
      headerText: i18n.t('在途状态'),
      width: '100'
    },
    {
      field: 'carNo',
      headerText: i18n.t('车牌号'),
      width: '100'
    },
    {
      field: 'vehicleLogistics',
      headerText: i18n.t('车辆物流'),
      width: '100',
      template: () => {
        return {
          template: Vue.component('vehicleLogistics', {
            template: `
                <span v-if="data.carNo" style="color: #6386c1; cursor: pointer;" @click="toLogistics(data)">{{ $t('查看物流') }}</span>`,
            methods: {
              toLogistics(e) {
                const params = {
                  ztpno: e.deliveryCode.toString(),
                  busCode: e.forecastCode,
                  busNum: e.carNo
                }
                this.$API.receiptAndDelivery.purQueryVehicleLogistics(params).then((res) => {
                  if (res.code === 200) {
                    window.open(res.data.mapURL)
                  }
                })
              }
            }
          })
        }
      }
    },
    {
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      width: '100'
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      width: '200'
    },
    {
      field: 'warehouseCode',
      headerText: i18n.t('库存地点'),
      width: '150',
      template: codeNameColumn({
        firstKey: 'warehouseCode',
        secondKey: 'warehouseName'
      })
    },
    {
      field: 'deliveryTypeDesc',
      headerText: i18n.t('送货单类型'),
      width: '105'
    },
    {
      field: 'sendTime',
      headerText: i18n.t('发货日期'),
      width: '100',
      template: timeDate({
        dataKey: 'sendTime',
        isDateTime: true
      })
    },
    {
      field: 'companyCode',
      headerText: i18n.t('公司编码'),
      width: '100'
    },
    {
      field: 'companyName',
      headerText: i18n.t('公司名称'),
      width: '200'
    },
    {
      field: 'associatedNo',
      headerText: i18n.t('关联单据编号')
    },
    {
      field: 'associateOuterDocNo',
      headerText: i18n.t('来源单号')
    },
    {
      field: 'doNo',
      headerText: i18n.t('来源单号2')
    },
    {
      field: 'virtualSupplierCode',
      headerText: i18n.t('虚拟供应商代码'),
      width: '140'
    },
    {
      field: 'buyerOrgCode',
      headerText: i18n.t('采购组'),
      width: '200',
      template: codeNameColumn({
        firstKey: 'buyerOrgCode',
        secondKey: 'buyerOrgName'
      })
    },
    {
      field: 'outsourcedTypeDesc',
      headerText: i18n.t('委外方式'),
      width: '100'
    },
    {
      field: 'printTimes',
      headerText: i18n.t('打印次数'),
      width: '100'
    },
    {
      field: 'thirdTenantCode',
      headerText: i18n.t('第三方物流商'),
      width: '150',
      template: codeNameColumn({
        firstKey: 'thirdTenantCode',
        secondKey: 'thirdTenantName'
      })
    },
    {
      field: 'sendAddressName',
      headerText: i18n.t('发货地点'),
      width: '100'
    },
    {
      field: 'createTime',
      headerText: i18n.t('创建时间'),
      width: '100',
      template: timeDate({
        dataKey: 'createTime',
        isDateTime: true
      })
    },
    {
      width: '150',
      field: 'screenIdCount',
      headerText: i18n.t('屏ID'),
      cellTools: [
        {
          id: 'screenIdImport',
          title: i18n.t('上载屏ID'),
          visibleCondition: (data) => data.status.value != 3 // 已完成
        },
        {
          id: 'search',
          title: i18n.t('查看')
        }
      ],
      cssClass: 'field-content-file-manage',
      valueConverter: {
        type: 'function',
        filter: (e) => {
          let str = i18n.t('已上传数')
          if (e) {
            return `${str}：${e}`
          } else {
            return `${str}：0`
          }
        }
      }
    },
    {
      width: '150',
      field: 'supInvoiceCount',
      headerText: i18n.t('供应商发票/装箱单'),
      template: () => {
        return {
          template: Vue.component('supInvoiceCount', {
            template: `
                <div class="headers">
                  <p style="color: #2783fe; cursor: pointer;" @click.stop="handleClickCellTool">
                    <span>{{ $t('已上传数') }}</span>:
                    <span>{{data.supInvoiceCount || 0}}</span>
                  </p>
                  <span class="e-headertext">{{ data.supInvoiceName }}</span>
                </div>
              `,
            methods: {
              handleClickCellTool() {
                const params = {
                  data: this.data,
                  tool: {
                    id: 'sup_invoice'
                  }
                }
                this.$parent.$emit('handleClickCellTool', params)
              }
            }
          })
        }
      }
      // cellTools: [
      //   {
      //     id: 'sup_invoice',
      //     title: i18n.t('附件')
      //   }
      // ],
      // cssClass: 'field-content-file-manage',
      // valueConverter: {
      //   type: 'function',
      //   filter: (e) => {
      //     let str = i18n.t('已上传数')
      //     if (e) {
      //       return `${str}：${e}`
      //     } else {
      //       return `${str}：0`
      //     }
      //   }
      // }
    },
    {
      width: '150',
      field: 'hkInvoiceCount',
      headerText: i18n.t('香港电子发票/装箱单'),
      // cellTools: [
      //   {
      //     id: 'hk_invoice',
      //     title: i18n.t('附件')
      //   }
      // ],
      // cssClass: 'field-content-file-manage',
      // valueConverter: {
      //   type: 'function',
      //   filter: (e) => {
      //     let str = i18n.t('已上传数')
      //     if (e) {
      //       return `${str}：${e}`
      //     } else {
      //       return `${str}：0`
      //     }
      //   }
      // },
      template: () => {
        return {
          template: Vue.component('hkInvoiceCount', {
            template: `
                <div class="headers">
                  <p style="color: #2783fe; cursor: pointer;" @click.stop="handleClickCellTool">
                    <span>{{ $t('已上传数') }}</span>:
                    <span>{{data.hkInvoiceCount || 0}}</span>
                  </p>
                  <span class="e-headertext">{{ data.hkInvoiceName }}</span>
                </div>
              `,
            methods: {
              handleClickCellTool() {
                const params = {
                  data: this.data,
                  tool: {
                    id: 'hk_invoice'
                  }
                }
                this.$parent.$emit('handleClickCellTool', params)
              }
            }
          })
        }
      }
    },
    {
      width: '120',
      field: 'ihNumber',
      headerText: i18n.t('IH号')
    },
    {
      width: '100',
      field: 'billOfLadingCount',
      headerText: i18n.t('提单'),
      // cellTools: [
      //   {
      //     id: 'bill_of_lading',
      //     title: i18n.t('附件')
      //   }
      // ],
      // cssClass: 'field-content-file-manage',
      // valueConverter: {
      //   type: 'function',
      //   filter: (e) => {
      //     let str = i18n.t('已上传数')
      //     if (e) {
      //       return `${str}：${e}`
      //     } else {
      //       return `${str}：0`
      //     }
      //   }
      // }
      template: () => {
        return {
          template: Vue.component('billOfLadingCount', {
            template: `
                <div class="headers">
                  <p style="color: #2783fe; cursor: pointer;" @click.stop="handleClickCellTool">
                    <span>{{ $t('已上传数') }}</span>:
                    <span>{{data.billOfLadingCount || 0}}</span>
                  </p>
                  <span class="e-headertext">{{ data.billOfLadingName }}</span>
                </div>
              `,
            methods: {
              handleClickCellTool() {
                const params = {
                  data: this.data,
                  tool: {
                    id: 'bill_of_lading'
                  }
                }
                this.$parent.$emit('handleClickCellTool', params)
              }
            }
          })
        }
      }
    },
    {
      width: '120',
      field: 'saleOrderCount',
      headerText: i18n.t('销售订单'),
      // cellTools: [
      //   {
      //     id: 'sale_order',
      //     title: i18n.t('附件')
      //   }
      // ],
      // cssClass: 'field-content-file-manage',
      // valueConverter: {
      //   type: 'function',
      //   filter: (e) => {
      //     let str = i18n.t('已上传数')
      //     if (e) {
      //       return `${str}：${e}`
      //     } else {
      //       return `${str}：0`
      //     }
      //   }
      // }
      template: () => {
        return {
          template: Vue.component('saleOrderCount', {
            template: `
                <div class="headers">
                  <p style="color: #2783fe; cursor: pointer;" @click.stop="handleClickCellTool">
                    <span>{{ $t('已上传数') }}</span>:
                    <span>{{data.saleOrderCount || 0}}</span>
                  </p>
                  <span class="e-headertext">{{ data.saleOrderName }}</span>
                </div>
              `,
            methods: {
              handleClickCellTool() {
                const params = {
                  data: this.data,
                  tool: {
                    id: 'sale_order'
                  }
                }
                this.$parent.$emit('handleClickCellTool', params)
              }
            }
          })
        }
      }
    },
    {
      width: '150',
      field: 'deliveryScanningCount',
      headerText: i18n.t('送货单扫描件'),
      // cellTools: [
      //   {
      //     id: 'delivery_scanning',
      //     title: i18n.t('附件')
      //   }
      // ],
      // cssClass: 'field-content-file-manage',
      // valueConverter: {
      //   type: 'function',
      //   filter: (e) => {
      //     let str = i18n.t('已上传数')
      //     if (e) {
      //       return `${str}：${e}`
      //     } else {
      //       return `${str}：0`
      //     }
      //   }
      // }
      template: () => {
        return {
          template: Vue.component('deliveryScanningCount', {
            template: `
                <div class="headers">
                  <p style="color: #2783fe; cursor: pointer;" @click.stop="handleClickCellTool">
                    <span>{{ $t('已上传数') }}</span>:
                    <span>{{data.deliveryScanningCount || 0}}</span>
                  </p>
                  <span class="e-headertext">{{ data.deliveryScanningName }}</span>
                </div>
              `,
            methods: {
              handleClickCellTool() {
                const params = {
                  data: this.data,
                  tool: {
                    id: 'delivery_scanning'
                  }
                }
                this.$parent.$emit('handleClickCellTool', params)
              }
            }
          })
        }
      }
    },
    {
      field: 'remark',
      headerText: i18n.t('备注'),
      width: '100'
    },
    {
      field: 'syncWmsStatusDesc',
      headerText: i18n.t('WMS同步状态'),
      width: '150'
    },
    {
      field: 'syncWmsDesc',
      headerText: i18n.t('WMS同步信息'),
      width: '150'
    },
    {
      field: 'syncSapStatusDesc',
      headerText: i18n.t('SAP同步状态'),
      width: '150'
    },
    {
      field: 'syncSapDesc',
      headerText: i18n.t('SAP同步信息'),
      width: '150'
    },
    {
      field: 'syncQmsStatusDesc',
      headerText: i18n.t('QMS同步状态'),
      width: '150'
    },
    {
      field: 'syncQmsDesc',
      headerText: i18n.t('QMS同步信息'),
      width: '150'
    }
  ]
  return column
}

export const componentConfig = (that) => {
  const config = [
    {
      isUseCustomSearch: true, // 是否使用自定义查询
      showArchive: true, // 是否显示归档查询
      isCustomSearchRules: true,
      useToolTemplate: false, // 此项不使用预置的表格操作按钮'新增、编辑、删除'
      useBaseConfig: false, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
      toolbar: {
        tools: [
          [
            {
              id: 'Colse',
              icon: 'icon_solid_Closeorder',
              permission: ['O_02_1377'],
              title: i18n.t('关闭')
            },
            {
              id: 'Print',
              icon: 'icon_table_print',
              permission: ['O_02_1382'],
              title: i18n.t('打印送货单')
            },
            {
              id: 'Sync',
              icon: 'icon_table_restart',
              permission: ['O_02_1383'],
              title: i18n.t('同步')
            },
            {
              id: 'EditIHNumber',
              icon: 'icon_table_restart',
              title: i18n.t('编辑IH号')
            },
            {
              id: 'BatchDownload',
              icon: 'icon_table_restart',
              title: i18n.t('批量下载附件')
            },
            {
              id: 'Export1',
              icon: 'icon_table_restart',
              title: i18n.t('导出')
            }
          ],
          ['Setting']
        ]
      },
      gridId: '90f33f48-b113-48d2-9ba5-4eedf96dac53',
      grid: {
        lineIndex: 1,
        allowPaging: true, // 是否使用内置分页器
        // virtualPageSize: 20, // 虚拟滚动，暂不开启
        // selectionSettings: {
        //   persistSelection: true,
        //   type: 'Multiple',
        //   checkboxOnly: true
        // },
        // enableVirtualization: true,
        columnData: columnData(that),
        asyncConfig: {
          url: '/srm-purchase-execute/tenant/delivery/header/buyer/list/page'
        },
        pageSettings: {
          currentPage: 1,
          pageSize: 50,
          pageSizes: [10, 20, 50, 100, 200],
          totalRecordsCount: 0
        }
      }
    }
  ]
  return config
}
