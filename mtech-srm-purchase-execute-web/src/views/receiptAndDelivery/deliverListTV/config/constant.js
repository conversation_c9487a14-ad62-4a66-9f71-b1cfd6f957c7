import { i18n } from '@/main.js'
import Vue from 'vue'

// tab
export const Tab = {
  list: 1, // 送货单列表
  details: 2 // 送货明细
}

// Toolbar 按钮
export const Toolbar = {
  // 送货单列表
  0: [
    {
      id: 'DeliveryNoteClose',
      icon: 'icon_solid_Closeorder',
      permission: ['O_02_0494'],
      title: i18n.t('关闭')
    },
    // {
    //   id: 'DeliveryNoteCancel',
    //   icon: 'icon_table_cancel',
    //   permission: ['O_02_0495'],
    //   title: i18n.t('取消')
    // },
    {
      id: 'print',
      icon: 'icon_table_print',
      title: i18n.t('打印送货单')
      // permission: ["O_02_0638"],
    },
    // {
    //   id: "printBd",
    //   icon: "icon_table_print",
    //   title: i18n.t("打印送货单（白电）"),
    //   // permission: ["O_02_0638"],
    // },
    {
      id: 'synchronous',
      icon: 'icon_table_restart',
      title: i18n.t('同步')
    }
  ],
  // 送货单明细
  1: []
}

// 送货单类型
export const DeliveryType = {
  associatedOrder: 1, //
  noOrder: 2, //
  jit: 3, //
  noNeed: 4, //
  vmi: 5, //
  gc: 6 //
}
// 送货单类型 text
export const DeliveryTypeText = {
  [DeliveryType.associatedOrder]: i18n.t('采购订单'),
  [DeliveryType.noOrder]: i18n.t('交货计划'),
  [DeliveryType.jit]: i18n.t('JIT'),
  [DeliveryType.noNeed]: i18n.t('无需求'),
  [DeliveryType.vmi]: i18n.t('vmi'),
  [DeliveryType.gc]: i18n.t('钢材')
}
// 送货单类型 对应的 Options
export const DeliveryTypeOptions = [
  {
    // 关联采购订单
    value: DeliveryType.associatedOrder,
    text: DeliveryTypeText[DeliveryType.associatedOrder],
    cssClass: ''
  },
  {
    // 无采购订单
    value: DeliveryType.noOrder,
    text: DeliveryTypeText[DeliveryType.noOrder],
    cssClass: ''
  },
  {
    // 关联采购订单
    value: DeliveryType.jit,
    text: DeliveryTypeText[DeliveryType.jit],
    cssClass: ''
  },
  {
    // 无采购订单
    value: DeliveryType.noNeed,
    text: DeliveryTypeText[DeliveryType.noNeed],
    cssClass: ''
  },
  {
    // 关联采购订单
    value: DeliveryType.vmi,
    text: DeliveryTypeText[DeliveryType.vmi],
    cssClass: ''
  },
  {
    // 无采购订单
    value: DeliveryType.gc,
    text: DeliveryTypeText[DeliveryType.gc],
    cssClass: ''
  }
]

// 交货方式:1-采购订单,2-交货计划,3-JIT,4-无需求
export const TheDeliveryType = {
  purchase: 1, // 采购订单
  plan: 2, // 交货计划
  jit: 3, // jit
  noNeed: 4 // 无需求
}
// 交货方式 text
export const TheDeliveryTypeText = {
  [TheDeliveryType.purchase]: i18n.t('采购订单'),
  [TheDeliveryType.plan]: i18n.t('交货计划'),
  [TheDeliveryType.jit]: i18n.t('JIT'),
  [TheDeliveryType.noNeed]: i18n.t('无需求')
}
// 交货方式 对应的 Options
export const TheDeliveryTypeOptions = [
  {
    // 采购订单
    value: TheDeliveryType.purchase,
    text: TheDeliveryTypeText[TheDeliveryType.purchase],
    cssClass: ''
  },
  {
    // 交货计划
    value: TheDeliveryType.plan,
    text: TheDeliveryTypeText[TheDeliveryType.plan],
    cssClass: ''
  },
  {
    // JIT
    value: TheDeliveryType.jit,
    text: TheDeliveryTypeText[TheDeliveryType.jit],
    cssClass: ''
  },
  {
    // 无需求
    value: TheDeliveryType.noNeed,
    text: TheDeliveryTypeText[TheDeliveryType.noNeed],
    cssClass: ''
  },
  {
    value: 5,
    text: 'vmi',
    cssClass: ''
  },
  {
    value: 6,
    text: i18n.t('钢材'),
    cssClass: ''
  }
]

// 状态 状态:1-新建,2-送货中,3-已完成,4-已取消,5-已关闭
export const Status = {
  new: 1, //新建
  shipping: 2, //送货中
  completed: 3, // 已完成
  cancelled: 4, // 已取消
  closed: 5 // 已关闭
}

// 状态 text
export const StatusText = {
  [Status.new]: i18n.t('新建'),
  [Status.shipping]: i18n.t('送货中'),
  [Status.completed]: i18n.t('已完成'),
  [Status.cancelled]: i18n.t('已取消'),
  [Status.closed]: i18n.t('已关闭')
}

// 状态 对应的 css class
export const StatusCssClass = {
  [Status.new]: 'col-active',
  [Status.shipping]: 'col-active',
  [Status.completed]: 'col-active',
  [Status.cancelled]: 'col-active',
  [Status.closed]: 'col-inactive'
}

// 状态 对应的 Options
export const StatusOptions = [
  {
    // 新建
    value: Status.new,
    text: StatusText[Status.new],
    cssClass: StatusCssClass[Status.new]
  },
  {
    // 送货中
    value: Status.shipping,
    text: StatusText[Status.shipping],
    cssClass: StatusCssClass[Status.shipping]
  },
  {
    // 已完成
    value: Status.completed,
    text: StatusText[Status.completed],
    cssClass: StatusCssClass[Status.completed]
  },
  {
    // 已取消
    value: Status.cancelled,
    text: StatusText[Status.cancelled],
    cssClass: StatusCssClass[Status.cancelled]
  },
  {
    // 已关闭
    value: Status.closed,
    text: StatusText[Status.closed],
    cssClass: StatusCssClass[Status.closed]
  }
]

// jit
export const JitOptions = [
  { value: 0, text: i18n.t('否'), cssClass: '' },
  { value: 1, text: i18n.t('是'), cssClass: '' }
]

// 委外方式
export const OutsourcedTypeOptions = [
  { value: '0', text: i18n.t('标准委外'), cssClass: '' },
  { value: '1', text: i18n.t('销售委外'), cssClass: '' },
  { value: '2', text: i18n.t('非委外'), cssClass: '' },
  { value: '3', text: i18n.t('工序委外'), cssClass: '' }
]

// 单元格操作按钮
export const CellTools = [
  {
    id: 'DeliveryNoteClose',
    icon: '',
    title: i18n.t('关闭'),
    // permission: [""],
    visibleCondition: (data) => data.status == Status.shipping // 送货中
  },
  {
    id: 'DeliveryNoteCancel',
    icon: '',
    title: i18n.t('取消'),
    // permission: [""],
    visibleCondition: (data) => data.status == Status.shipping // 送货中
  }
]

// tab1 送货单列表 表格列数据
export const ColumnDataTab1 = [
  {
    field: 'id',
    headerText: i18n.t('id'),
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  },
  {
    fieldCode: 'deliveryCode', // 送货单号
    fieldName: i18n.t('送货单号')
  },
  {
    fieldCode: 'deliveryType', // 类型 送货单类型:1-关联采购订单,2-无采购订单
    fieldName: i18n.t('送货单类型')
  },
  {
    fieldCode: 'status', // 状态 状态:1-新建,2-送货中,3-已完成,4-已取消,5-已关闭
    fieldName: i18n.t('状态')
  },
  {
    fieldCode: 'onWayStatus',
    fieldName: i18n.t('在途状态')
  },
  // {
  //   fieldCode: 'driverName',
  //   fieldName: i18n.t('司机名称')
  // },
  // {
  //   fieldCode: 'driverNo',
  //   fieldName: i18n.t('司机身份证')
  // },
  // {
  //   fieldCode: 'driverPhone',
  //   fieldName: i18n.t('司机联系方式')
  // },
  {
    fieldCode: 'carNo',
    fieldName: i18n.t('车牌号')
  },
  // {
  //   fieldCode: 'number',
  //   fieldName: i18n.t('件数')
  // },
  {
    fieldCode: 'vehicleLogistics', // 车辆物流
    fieldName: i18n.t('车辆物流'),
    template: () => {
      return {
        template: Vue.component('template-detail', {
          template: `
              <span v-if="data.carNo" style="color: #6386c1; cursor: pointer;" @click="toLogistics(data)">{{ $t('查看物流') }}</span>`,
          methods: {
            toLogistics(e) {
              const params = {
                ztpno: e.deliveryCode.toString(),
                busCode: e.forecastCode,
                busNum: e.carNo
              }
              this.$API.receiptAndDelivery.purQueryVehicleLogistics(params).then((res) => {
                if (res.code === 200) {
                  window.open(res.data.mapURL)
                }
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'siteCode',
    fieldName: i18n.t('工厂编码'),
    width: '100'
  },
  {
    fieldCode: 'siteName',
    fieldName: i18n.t('工厂名称'),
    width: '200'
  },
  {
    fieldCode: 'companyCode',
    fieldName: i18n.t('公司编码'),
    width: '100'
  },
  {
    fieldCode: 'companyName',
    fieldName: i18n.t('公司名称'),
    width: '200'
  },
  {
    fieldCode: 'outsourcedType',
    fieldName: i18n.t('委外方式')
  },
  {
    fieldCode: 'printTimes', //
    fieldName: i18n.t('打印次数')
  },
  {
    fieldCode: 'buyerOrgCode', // 采购组 buyerOrgName code-name
    fieldName: i18n.t('采购组')
  },
  {
    fieldCode: 'supplierCode',
    fieldName: i18n.t('供应商编码')
  },
  {
    fieldCode: 'supplierName',
    fieldName: i18n.t('供应商名称')
  },
  {
    fieldCode: 'thirdTenantCode', // thirdTenantName code-name
    fieldName: i18n.t('第三方物流商')
  },
  // {
  //   fieldCode: 'vmiWarehouseCode', // VMI仓编号
  //   fieldName: i18n.t('VMI仓编码')
  // },
  // {
  //   fieldCode: 'vmiWarehouseName', // VMI仓名称
  //   fieldName: i18n.t('VMI仓名称')
  // },
  {
    fieldCode: 'sendAddressName', // 发货地点
    fieldName: i18n.t('发货地点')
  },
  {
    fieldCode: 'createTime', // 创建时间
    fieldName: i18n.t('创建时间')
  },
  {
    fieldCode: 'warehouseCode', // 库存地点 warehouseName code-name
    fieldName: i18n.t('库存地点')
  },
  {
    fieldCode: 'sendTime', // 发货日期
    fieldName: i18n.t('发货日期')
  },
  {
    width: '150',
    fieldCode: 'screenId',
    fieldName: i18n.t('屏ID'),
    cellTools: [
      {
        id: 'search',
        title: i18n.t('查看')
      }
    ]
  },
  {
    width: '150',
    fieldCode: 'supplierFile',
    fieldName: i18n.t('供应商发票/装箱单'),
    cellTools: [
      {
        id: 'sup_invoice',
        title: i18n.t('附件')
      }
    ]
  },
  {
    width: '150',
    fieldCode: 'HKFile',
    fieldName: i18n.t('香港电子发票/装箱单'),
    cellTools: [
      {
        id: 'hk_invoice',
        title: i18n.t('附件')
      }
    ]
  },
  {
    width: '120',
    fieldCode: 'ihNumber',
    fieldName: i18n.t('IH号')
  },
  {
    width: '100',
    fieldCode: 'billFile',
    fieldName: i18n.t('提单'),
    cellTools: [
      {
        id: 'bill_of_lading',
        title: i18n.t('附件')
      }
    ]
  },
  {
    width: '120',
    fieldCode: 'saleOrderFile',
    fieldName: i18n.t('销售订单'),
    cellTools: [
      {
        id: 'sale_order',
        title: i18n.t('附件')
      }
    ]
  },
  {
    width: '150',
    fieldCode: 'canFile',
    fieldName: i18n.t('送货单扫描件'),
    cellTools: [
      {
        id: 'delivery_scanning',
        title: i18n.t('附件')
      }
    ]
  },
  {
    fieldCode: 'remark', // 备注
    fieldName: i18n.t('备注')
  },
  {
    fieldCode: 'syncWmsStatus',
    fieldName: i18n.t('WMS同步状态')
  },
  {
    fieldCode: 'syncWmsDesc',
    fieldName: i18n.t('WMS同步信息')
  },
  {
    fieldCode: 'syncSapStatus',
    fieldName: i18n.t('SAP同步状态')
  },
  {
    fieldCode: 'syncSapDesc',
    fieldName: i18n.t('SAP同步信息')
  },
  {
    fieldCode: 'syncQmsStatus',
    fieldName: i18n.t('QMS同步状态')
  },
  {
    fieldCode: 'syncQmsDesc',
    fieldName: i18n.t('QMS同步信息')
  }
]

// tab2 送货单明细 表格列数据
export const ColumnDataTab2 = [
  {
    field: 'id',
    headerText: i18n.t('id'),
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  },
  {
    fieldCode: 'deliveryCode', // 送货单号
    fieldName: i18n.t('送货单号')
  },
  {
    fieldCode: 'inputDate', //
    fieldName: i18n.t('凭证创建日期')
  },
  // {
  //   fieldCode: "inputTime", //
  //   fieldName: i18n.t("凭证创建时间"),
  //   ignore: true,
  // },
  {
    fieldCode: 'postingDate', //
    fieldName: i18n.t('过账日期')
  },
  {
    fieldCode: 'siteCode',
    fieldName: i18n.t('工厂编码'),
    width: '100'
  },
  {
    fieldCode: 'siteName',
    fieldName: i18n.t('工厂名称'),
    width: '200'
  },
  {
    fieldCode: 'companyCode',
    fieldName: i18n.t('公司编码'),
    width: '100'
  },
  {
    fieldCode: 'companyName',
    fieldName: i18n.t('公司名称'),
    width: '200'
  },
  {
    fieldCode: 'subSiteCode',
    fieldName: i18n.t('分厂')
  },
  {
    fieldCode: 'jit',
    fieldName: i18n.t('是否JIT')
  },
  {
    width: '150',
    fieldCode: 'takeNo',
    fieldName: i18n.t('车牌号')
  },
  {
    fieldCode: 'vehicleLogistics', // 车辆物流
    fieldName: i18n.t('车辆物流'),
    template: () => {
      return {
        template: Vue.component('template-detail', {
          template: `
              <span v-if="data.takeNo" style="color: #6386c1; cursor: pointer;" @click="toLogistics(data)">{{ $t('查看物流') }}</span>`,
          methods: {
            toLogistics(e) {
              const params = {
                ztpno: e?.deliveryCode?.toString(),
                busCode: e?.forecastCode,
                busNum: e.takeNo
              }
              this.$API.receiptAndDelivery.purQueryVehicleLogistics(params).then((res) => {
                if (res.code === 200) {
                  window.open(res.data.mapURL)
                }
              })
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    fieldCode: 'jitDeliveryNumber',
    fieldName: i18n.t('JIT编号'),
    searchOptions: {
      renameField: 'deliveryNumber'
    }
  },
  {
    fieldCode: 'supplierCode',
    fieldName: i18n.t('供应商编码')
  },
  {
    fieldCode: 'supplierName',
    fieldName: i18n.t('供应商名称')
  },
  {
    fieldCode: 'warehouseCode', // 库存地点 warehouseName code-name
    fieldName: i18n.t('库存地点')
  },
  {
    fieldCode: 'thirdTenantCode', // thirdTenantName code-name
    fieldName: i18n.t('第三方物流商')
  },
  // {
  //   fieldCode: 'vmiWarehouseCode', // vmiWarehouseName code-name
  //   fieldName: i18n.t('VMi仓')
  // },
  {
    fieldCode: 'deliveryLineNo', // 行号
    fieldName: i18n.t('行号')
  },
  {
    fieldCode: 'status', // 状态 状态:1-新建,2-送货中,3-已完成,4-已取消,5-已关闭
    fieldName: i18n.t('状态')
  },
  {
    fieldCode: 'onWayStatus',
    fieldName: i18n.t('在途状态')
  },
  {
    fieldCode: 'itemCode', // 物料 itemName code-name
    fieldName: i18n.t('物料')
  },
  {
    fieldCode: 'orderCode', // 采购订单号
    fieldName: i18n.t('采购订单号')
  },
  {
    fieldCode: 'lineNo', // 采购订单行号
    fieldName: i18n.t('采购订单行号')
  },
  {
    fieldCode: 'demandDate', // 需求日期
    fieldName: i18n.t('需求日期')
  },
  // {
  //   fieldCode: "demandTime", // 需求时间
  //   fieldName: i18n.t("需求时间"),
  // },
  {
    fieldCode: 'deliveryQuantity', // 本次送货数量
    fieldName: i18n.t('本次送货数量'),
    type: 'number'
  },
  {
    fieldCode: 'receiveQuantity', // 收货数量
    fieldName: i18n.t('收货数量'),
    type: 'number'
  },
  {
    fieldCode: 'rejectQuantity', // 拒绝数量
    fieldName: i18n.t('拒绝数量'),
    type: 'number'
  },
  {
    fieldCode: 'rejectReason', // 拒绝原因
    fieldName: i18n.t('拒绝原因')
  },
  {
    fieldCode: 'deliveryType', // 送货单类型 1-关联采购订单,2-无采购订单
    fieldName: i18n.t('送货单类型')
  },
  {
    fieldCode: 'deliveryType', // 交货方式:1-采购订单,2-交货计划,3-JIT,4-无需求
    fieldName: i18n.t('交货方式')
  },
  {
    fieldCode: 'transferPlanName', // 计划员
    fieldName: i18n.t('计划员')
  },
  {
    fieldCode: 'receiveSupplierCode', // 收货供应商 receiveSupplierName code-name
    fieldName: i18n.t('收货供应商')
  },
  {
    fieldCode: 'sendAddressName', // 发货地点
    fieldName: i18n.t('发货地点')
  },
  {
    fieldCode: 'sendTime', // 发货日期
    fieldName: i18n.t('发货日期')
  },

  {
    fieldCode: 'deliveryRemark', // 送货单备注
    fieldName: i18n.t('送货单备注')
  },
  {
    fieldCode: 'deliveryNumber', // 交货编号
    fieldName: i18n.t('交货编号')
  },
  {
    fieldCode: 'buyerOrgCode', // 采购组 buyerOrgName code-name
    fieldName: i18n.t('采购组')
  },
  {
    fieldCode: 'unitCode', // 单位 基本单位名称 unitName code-name
    fieldName: i18n.t('单位')
  },
  {
    fieldCode: 'workOrderNo', // 关联工单号
    fieldName: i18n.t('关联工单号')
  },
  {
    fieldCode: 'saleOrderNo', // 关联销售订单号
    fieldName: i18n.t('关联销售订单号')
  },
  {
    fieldCode: 'saleOrderLineNo', // 关联销售订单行号
    fieldName: i18n.t('关联销售订单行号')
  },
  {
    fieldCode: 'bomCode', // BOM号
    fieldName: i18n.t('BOM号')
  },
  {
    fieldCode: 'productCode', // 关联产品代码
    fieldName: i18n.t('关联产品代码')
  },
  {
    fieldCode: 'workCenterCode', // 工作中心 workCenterName code-name
    fieldName: i18n.t('工作中心')
  },
  {
    fieldCode: 'processName', // 工序名称
    fieldName: i18n.t('工序名称')
  },
  // {
  //   fieldCode: "TODO", // 送货方式 TODO API 没有这个字段
  //   fieldName: i18n.t("送货方式"),
  // },
  // {
  //   fieldCode: "receiverName", // 收货方名称
  //   fieldName: i18n.t("收货方名称"),
  // },
  // {
  //   fieldCode: "receiverCode", // 收货方编号
  //   fieldName: i18n.t("收货方编号"),
  // },
  {
    fieldCode: 'sendAddressName', // 送货地址
    fieldName: i18n.t('收货地址')
  },
  {
    fieldCode: 'batchCode', // 卷号
    fieldName: i18n.t('卷号')
  },
  {
    fieldCode: 'takeNo', // 卷号
    fieldName: i18n.t('车号')
  },
  {
    fieldCode: 'limitQuantity', // 限量数量
    fieldName: i18n.t('限量数量')
  },
  {
    fieldCode: 'warehouseClerkName', // 仓管员
    fieldName: i18n.t('仓管员')
  },
  {
    fieldCode: 'dispatcherName', // 调度员
    fieldName: i18n.t('调度员')
  },
  {
    fieldCode: 'wmsInstockQty', // 入库数量
    fieldName: i18n.t('WMS入库数量')
  },
  {
    fieldCode: 'wmsInstockTime', // 入库日期
    fieldName: i18n.t('WMS入库时间')
  },
  {
    fieldCode: 'cancelPersonName', // 取消人
    fieldName: i18n.t('取消人')
  },
  {
    fieldCode: 'cancelTime', // 取消时间
    fieldName: i18n.t('取消时间')
  },
  {
    fieldCode: 'closeTime', // 取消时间
    fieldName: i18n.t('关闭时间')
  },
  {
    fieldCode: 'closePersonName', // 取消时间
    fieldName: i18n.t('关闭人')
  },
  {
    fieldCode: 'collectorMark', // 代收标识
    fieldName: i18n.t('代收标识')
  },
  {
    fieldCode: 'collectorName', // 代收人
    fieldName: i18n.t('代收人')
  },
  {
    fieldCode: 'createUserName', // 创建人
    fieldName: i18n.t('创建人')
  },
  {
    fieldCode: 'createTime', // 创建时间
    fieldName: i18n.t('创建时间')
  },
  {
    fieldCode: 'remark', // 行备注
    fieldName: i18n.t('行备注')
  }
]
