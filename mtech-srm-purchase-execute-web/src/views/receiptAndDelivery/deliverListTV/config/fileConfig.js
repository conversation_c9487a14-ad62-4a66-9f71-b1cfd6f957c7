import { i18n } from '@/main.js'

const isAllowEdit = (modalData) => {
  const status = Number(modalData.row.status?.value || 1)
  // 扫描件不需要编辑
  // if (modalData.type === 9 && modalData.pageType !== 'receiptListProcess') {
  //   return false
  // }
  //销售订单附件只是送货中状态能编辑
  if (modalData.type === 8) {
    // if (status === 2) return true
    if (status === 2 || status === 3) return true
  } else {
    if ([1, 2, 3, 6].includes(status)) {
      return true
    }
  }
  return false
}
const scoreFileCols = (modalData) => [
  {
    field: 'fileName',
    headerText: i18n.t('文件名称'),
    cssClass: 'field-content'
  },
  {
    field: 'opration',
    headerText: i18n.t('操作'),
    cellTools: [
      {
        id: 'delete',
        title: i18n.t('删除'),
        visibleCondition: () => {
          return isAllowEdit(modalData)
        }
      },
      {
        id: 'download',
        title: i18n.t('下载')
      }
    ]
  }
]
const toolbar = [[{ id: 'Add', icon: 'icon_solid_upload', title: i18n.t('上传附件') }], []]
export const scoreFileConfig = (url, modalData) => {
  let tempTool = isAllowEdit(modalData) ? toolbar : []
  return [
    {
      useToolTemplate: false,
      useBaseConfig: false,
      toolbar: tempTool,
      grid: {
        lineIndex: true,
        columnData: scoreFileCols(modalData),
        height: 'auto',
        allowPaging: false,
        dataSource: [],
        asyncConfig: {
          loading: false,
          url: url,
          methods: 'get',
          recordsPosition: 'data',
          params: {
            docId: modalData.row.id,
            docType: modalData.type
          }
        }
      }
    }
  ]
}
