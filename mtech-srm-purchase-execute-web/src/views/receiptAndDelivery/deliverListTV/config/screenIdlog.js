import { i18n } from '@/main.js'
import { timeDate } from './columnComponent'

const columnData = () => {
  const column = [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false,
      allowEditing: false,
      allowResizing: false
    },
    {
      field: 'id',
      headerText: 'id',
      width: 0,
      visible: false,
      allowEditing: false,
      isPrimaryKey: true
    },
    {
      field: 'deliveryCode',
      headerText: i18n.t('送货单号'),
      width: '165'
      // cellTools: []
    },
    {
      field: 'syncStatus',
      headerText: i18n.t('校验状态'),
      width: '100',
      cellTools: [],
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('正常'),
          1: i18n.t('失败')
        }
      }
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: '110'
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: '200'
    },
    {
      field: 'palletNo',
      headerText: i18n.t('栈板号'),
      width: '100'
    },
    {
      field: 'boxNo',
      headerText: i18n.t('箱号'),
      width: '100'
    },
    {
      field: 'diaphragmNo',
      headerText: i18n.t('膜片号'),
      width: '100'
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      width: '120'
    },
    // {
    //   field: 'itemName',
    //   headerText: i18n.t('物料名称'),
    //   width: '200'
    // },
    {
      field: 'produceDate',
      headerText: i18n.t('生产日期'),
      width: '100',
      template: timeDate({
        dataKey: 'produceDate',
        isDate: true
      })
    },
    {
      field: 'purchaseOrderNo',
      headerText: i18n.t('采购订单号'),
      width: '120'
    },
    {
      field: 'doNo',
      headerText: i18n.t('DO单号'),
      width: '100'
    },
    {
      field: 'doNo2',
      headerText: i18n.t('DO单号2'),
      width: '100'
    },
    {
      field: 'level',
      headerText: i18n.t('等级'),
      width: '100'
    },
    {
      field: 'versionNo',
      headerText: i18n.t('版本号'),
      width: '100'
    },
    {
      field: 'idSyncFailDesc',
      headerText: i18n.t('校验失败原因'),
      width: '120'
    },
    {
      field: 'createTime',
      headerText: i18n.t('创建时间'),
      width: '100',
      template: timeDate({
        dataKey: 'createTime',
        isDateTime: true
      })
    }
  ]
  return column
}

export const componentConfig = () => {
  const config = [
    {
      isUseCustomSearch: true, // 是否使用自定义查询
      isCustomSearchRules: true,
      useToolTemplate: false, // 此项不使用预置的表格操作按钮'新增、编辑、删除'
      useBaseConfig: false, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
      toolbar: {
        tools: [
          [
            {
              id: 'export',
              icon: 'icon_solid_Import',
              title: i18n.t('导出')
            }
          ],
          ['Setting']
        ]
      },
      gridId: 'e1a961ce-fdeb-4cfe-bc5b-46a593def60b',
      grid: {
        lineIndex: 1,
        allowPaging: true, // 是否使用内置分页器
        // virtualPageSize: 20, // 虚拟滚动，暂不开启
        // selectionSettings: {
        //   persistSelection: true,
        //   type: 'Multiple',
        //   checkboxOnly: true
        // },
        // enableVirtualization: true,
        columnData: columnData(),
        asyncConfig: {
          url: '/srm-purchase-execute/tenant/delivery/screen/logList'
        },
        pageSettings: {
          currentPage: 1,
          pageSize: 20,
          pageSizes: [10, 20, 50, 100, 200],
          totalRecordsCount: 0
        }
      }
    }
  ]
  return config
}
