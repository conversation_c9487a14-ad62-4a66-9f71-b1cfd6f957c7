<!-- 选择打印方式 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    width="550px"
    height="400px"
  >
    <div class="dialog-content" style="margin-top: 20px">
      <mt-form ref="modelForm" :model="modelForm" :rules="dialogRules">
        <mt-form-item prop="ihNumber" :label="$t('IH号')">
          <mt-input
            v-model="modelForm.ihNumber"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      dialogRules: {
        ihNumber: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }]
      },
      modelForm: {
        ihNumber: ''
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      this.$refs.modelForm.validate((valid) => {
        if (valid) {
          const params = {
            id: this.modalData.selectedRecords[0]['id'],
            ...this.modelForm
          }
          this.apiStartLoading()
          this.$API.receiptAndDelivery
            .postUpdateIhNumber(params)
            .then((res) => {
              if (res.code === 200) {
                this.apiEndLoading()
                this.$toast({
                  content: this.$t('更新成功'),
                  type: 'success'
                })
                this.$emit('confirm-function')
              }
            })
            .catch(() => {
              this.apiEndLoading()
            })
            .finally(() => {
              this.apiEndLoading()
            })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>
