<!-- 选择打印方式 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    width="550px"
    height="300px"
  >
    <div class="dialog-content" style="margin-top: 20px">
      <mt-form ref="page" :model="page" :rules="rules">
        <mt-form-item prop="current" :label="$t('页码')">
          <!-- <mt-radio v-model="page.printType" :data-source="typeOptions" /> -->
          <mt-input-number v-model="page.current" :show-clear-button="false" :min="1" :step="1" />
        </mt-form-item>
        <mt-form-item prop="size" :label="$t('每页条数')">
          <!-- <mt-radio v-model="page.printType" :data-source="typeOptions" /> -->
          <mt-select v-model="page.size" :data-source="typeOptions" />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { RegExpMap } from '@/utils/constant'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    // 件数
    const numberValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入页码')))
      } else if (!RegExpMap.integerReg.test(value)) {
        callback(new Error(this.$t('请输入整数')))
      } else {
        this.$refs.page.clearValidate(['current'])
        callback()
      }
    }
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('导出') }
        }
      ],
      page: {
        current: 1,
        size: 10000
      },
      rules: {
        current: [
          {
            required: true,
            // message: this.$t('请输入页码'),
            validator: numberValidator,
            trigger: 'blur'
          }
        ],
        size: [
          {
            required: true,
            message: this.$t('请选择每页条数'),
            trigger: 'blur'
          }
        ]
      },
      typeOptions: [
        // {
        //   label: 5000,
        //   value: 5000
        // },
        // {
        //   label: 10000,
        //   value: 10000
        // },
        5000, 10000, 20000, 40000, 60000
      ]
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      this.$refs.page.validate((valid) => {
        if (valid) {
          this.$emit('confirm-function', this.page)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style></style>
