<template>
  <div style="height: 100%">
    <mt-template-page
      ref="templateRef2"
      :permission-obj="permissionObj"
      :template-config="templateConfig2"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleSearchReset"
      @handleClickCellTitle="clickCellTitle"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item :label="$t('供应商编码')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.supplierCodes"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择供应商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-multi-select
                style="width: 100%"
                v-model="searchFormModel.status"
                :placeholder="$t('请选择状态')"
                :show-clear-button="true"
                :show-select-all="true"
                :data-source="statusOptions"
                :fields="{ text: 'text', value: 'value' }"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('公司代码')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.companyCode"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                :placeholder="$t('请选择公司')"
                :fields="{ text: 'orgName', value: 'orgCode' }"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="deliveryCode" :label="$t('送货单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.deliveryCode"
                :placeholder="$t('请输入送货单号')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="siteCode" :label="$t('工厂代码')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.siteCode"
                :url="$API.masterData.getSiteListUrl"
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                :search-fields="['siteName', 'siteCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.itemCode"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.itemName"
                :placeholder="$t('支持模糊搜索')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="orderNo" :label="$t('采购订单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.orderNo"
                :placeholder="$t('支持粘贴多个精准查询')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="onWayStatus" :label="$t('在途状态')" label-style="top">
              <mt-multi-select
                v-model="searchFormModel.onWayStatus"
                type="multipleChoice"
                :placeholder="$t('请选择')"
                :show-select-all="true"
                :show-clear-button="true"
                :data-source="onWayStatusOptions"
                :fields="{ text: 'text', value: 'value' }"
              />
            </mt-form-item>
            <mt-form-item prop="buyerOrgCode" :label="$t('采购组')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.buyerOrgCode"
                :url="$API.masterData.getBusinessGroupAuthFuzzyUrl"
                multiple
                :placeholder="$t('请选择采购组')"
                :fields="{ text: 'groupName', value: 'groupCode' }"
                records-position="data"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="workCenterCode" :label="$t('工作中心')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.workCenterCode"
                url="/masterDataManagement/tenant/work-center/paged-query"
                multiple
                :placeholder="$t('请选择')"
                :fields="{ text: 'workCenterName', value: 'workCenterCode' }"
                :search-fields="['workCenterName', 'workCenterCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
import { formatTableColumnData } from '../config/index'
import { ColumnDataTab2, Tab } from '../config/constant'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName, codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      itemCodeValueTemplate: codeNameColumn({
        firstKey: 'itemCode',
        secondKey: 'itemName'
      }),
      itemCodeOptions: [], // 物料下拉
      searchFormModel: {
        status: [2]
      },
      // 1-新建,2-发货中,3-已完成,4-已取消,5-已关闭
      statusOptions: [
        { text: this.$t('新建'), value: 1 },
        { text: this.$t('送货中'), value: 2 },
        { text: this.$t('已完成'), value: 3 },
        { text: this.$t('已取消'), value: 4 },
        { text: this.$t('已关闭'), value: 5 }
      ],
      onWayStatusOptions: [
        { text: i18n.t('未出发'), value: 0 },
        { text: i18n.t('已入园'), value: 1 },
        { text: i18n.t('已出发'), value: 2 },
        { text: i18n.t('已报到'), value: 3 },
        { text: i18n.t('已取消'), value: 4 },
        { text: i18n.t('已关闭'), value: 5 }
      ],
      workCenterOptions: [],
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'List', permissionCode: 'T_02_0024' },
          { dataPermission: 'Details', permissionCode: 'T_02_0025' }
        ]
      },
      templateConfig2: [
        {
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                {
                  id: 'export',
                  icon: 'icon_solid_Import',
                  permission: ['O_02_1081'],
                  title: this.$t('导出')
                }
              ],
              ['Refresh', 'Setting']
            ]
          },
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          grid: {
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [10, 20, 50, 100, 200, 500, 1000],
              totalRecordsCount: 0
            },
            enableVirtualization: true,
            virtualPageSize: 30,
            lineIndex: 0, // 序号列
            columnData: formatTableColumnData({
              data: ColumnDataTab2,
              tab: Tab.details
            }),
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/delivery/item/buyer/page` // 采方送货单-采方查询送货单明细分页
            }
          }
        }
      ]
    }
  },
  mounted() {
    this.getWorkCenterOptions()
  },
  methods: {
    getWorkCenterOptions() {
      let params = {
        page: {
          current: 1,
          size: 50
        }
      }
      this.$API.masterData.getWorkCenterBy(params).then((res) => {
        if (res.code === 200) {
          this.workCenterOptions = res.data.records.map((item) => {
            return {
              text: item.workCenterCode + '-' + item.workCenterName,
              value: item.workCenterCode
            }
          })
        }
      })
    },
    // 标题点击
    clickCellTitle(args) {
      this.$emit('clickCellTitle', args)
    },
    // 物料 change
    itemCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.itemCode = itemData.itemCode
        this.searchFormModel.itemName = itemData.itemName
      } else {
        this.searchFormModel.itemCode = ''
        this.searchFormModel.itemName = ''
      }
    },
    // 获取主数据-获取指定组织下指定组织层级节点列表 物料信息
    getItemCode(args) {
      const { text, updateData, setSelectData } = args
      let params = {
        keyword: text || this.searchFormModel.itemCode || '',
        pageSize: 50
      }
      this.$API.masterData
        .getItemByKeyword(params)
        .then((res) => {
          if (res) {
            const list = res.data?.records || []
            this.itemCodeOptions = addCodeNameKeyInList({
              firstKey: 'itemCode',
              secondKey: 'itemName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.itemCodeOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // ToolBar
    handleClickToolBar(args) {
      // if (args.toolbar.id === 'resetDataByLocal') return
      if (args.toolbar.id == 'export') {
        const queryBuilderRules =
          this.$refs.templateRef2.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        console.log(queryBuilderRules)
        const params = {
          page: { current: 1, size: 10000 },
          // ...queryBuilderRules
          ...this.searchFormModel
        } // 筛选条件
        this.$store.commit('startLoading')
        this.$API.receiptAndDelivery.buyerOrderDeliveryQueryExportNewTv(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
    },
    handleSearchReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'status') {
            this.searchFormModel[key] = [2]
          }
        }
      }
    }
  }
}
</script>

<style></style>
