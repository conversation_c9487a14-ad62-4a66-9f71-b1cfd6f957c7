<!-- 选择打印方式 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    width="550px"
    height="400px"
  >
    <div class="dialog-content" style="margin-top: 20px">
      <mt-form ref="modelForm" :model="modelForm">
        <mt-form-item prop="batchDownloadFlag">
          <mt-checkbox
            v-model="modelForm.batchDownloadFlag"
            @change="(e) => handleChange(e, 'batchDownloadFlag')"
            :label="$t('全部下载')"
          />
        </mt-form-item>
        <mt-form-item prop="screenIdFlag" class="half-width">
          <mt-checkbox
            v-model="modelForm.screenIdFlag"
            @change="(e) => handleChange(e, 'screenIdFlag')"
            :label="$t('屏ID')"
          />
        </mt-form-item>
        <mt-form-item prop="supInvoiceFlag" class="half-width">
          <mt-checkbox
            v-model="modelForm.supInvoiceFlag"
            @change="(e) => handleChange(e, 'supInvoiceFlag')"
            :label="$t('供应商发票/装箱单')"
          />
        </mt-form-item>
        <mt-form-item prop="hkInvoiceFlag" class="half-width">
          <mt-checkbox
            v-model="modelForm.hkInvoiceFlag"
            @change="(e) => handleChange(e, 'hkInvoiceFlag')"
            :label="$t('香港点子发票/装箱单')"
          />
        </mt-form-item>
        <mt-form-item prop="billOfLadingFlag" class="half-width">
          <mt-checkbox
            v-model="modelForm.billOfLadingFlag"
            @change="(e) => handleChange(e, 'billOfLadingFlag')"
            :label="$t('提单')"
          />
        </mt-form-item>
        <mt-form-item prop="saleOrderFlag" class="half-width">
          <mt-checkbox
            v-model="modelForm.saleOrderFlag"
            @change="(e) => handleChange(e, 'saleOrderFlag')"
            :label="$t('销售订单')"
          />
        </mt-form-item>
        <mt-form-item prop="deliveryScanningFlag" class="half-width">
          <mt-checkbox
            v-model="modelForm.deliveryScanningFlag"
            @change="(e) => handleChange(e, 'deliveryScanningFlag')"
            :label="$t('送货单扫描件')"
          />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      modelForm: {
        batchDownloadFlag: true,
        screenIdFlag: false,
        supInvoiceFlag: false,
        hkInvoiceFlag: false,
        billOfLadingFlag: false,
        saleOrderFlag: false,
        deliveryScanningFlag: false
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    handleChange(e, labelName) {
      this.modelForm[labelName] = e.checked
    },
    confirm() {
      if (Object.values(this.modelForm).every((value) => value === false)) {
        this.$toast({ content: this.$t('请选择需要下载的附件'), type: 'warning' })
        return
      }
      this.$refs.modelForm.validate((valid) => {
        if (valid) {
          const modelFormKeys = Object.keys(this.modelForm)
          const modelForm = {}
          if (this.modelForm.batchDownloadFlag) {
            modelFormKeys.forEach((key) => {
              modelForm[key] = 1
            })
          } else {
            modelFormKeys.forEach((key) => {
              modelForm[key] = this.modelForm[key] ? 1 : 0
            })
          }
          this.$emit('confirm-function', modelForm)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .half-width {
  width: calc(37% - 20px) !important;
  display: inline-block;
}
</style>
