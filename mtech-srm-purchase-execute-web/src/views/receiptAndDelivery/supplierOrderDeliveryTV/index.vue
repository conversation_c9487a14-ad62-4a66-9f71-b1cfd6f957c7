<!-- 供方 - 送货单维护订单 -->
<template>
  <div class="full-height vertical-flex-box">
    <div style="height: calc(100% - 40px)">
      <mt-template-page
        :template-config="pageConfig"
        :hidden-tabs="true"
        ref="templateRef"
        @handleCustomReset="handleSearchReset"
        @handleCustomSearch="handleSearchForm"
        @handleClickToolBar="handleClickToolBar"
      >
        <template v-slot:quick-search-form>
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="deliveryCode" :label="$t('送货单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.deliveryCode"
                :placeholder="$t('请输入送货单号')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="siteCode" :label="$t('工厂编码')" class="">
              <debounce-filter-select
                v-model="searchFormModel.siteCode"
                :request="postSiteFuzzyQuery"
                :data-source="siteOptions"
                :show-clear-button="true"
                :fields="{ text: 'theCodeName', value: 'siteCode' }"
                :value-template="siteCodeValueTemplate"
                @change="siteCodeChange"
                :placeholder="$t('请选择')"
              ></debounce-filter-select>
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('公司编码')" label-style="top">
              <mt-select
                v-model="searchFormModel.companyCode"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                :allow-filtering="true"
                :data-source="companyOptions"
                :fields="{ text: 'text', value: 'value' }"
                @change="companyCodeChange"
              />
            </mt-form-item>
            <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.itemCode"
                :show-clear-button="true"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              />
            </mt-form-item>
            <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.itemName"
                :show-clear-button="true"
                :placeholder="$t('支持模糊搜索')"
              />
            </mt-form-item>
            <mt-form-item :label="$t('采购订单号是否为空')" label-style="top">
              <mt-select
                v-model="searchFormModel.orderEmpty"
                :data-source="[
                  { value: 'Y', text: $t('是') },
                  { value: 'N', text: $t('否') }
                ]"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :allow-filtering="true"
                :placeholder="$t('请选择')"
              />
            </mt-form-item>
          </mt-form>
        </template>
      </mt-template-page>
    </div>
    <mt-page
      class="full-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totals"
      @currentChange="currentChange"
      @sizeChange="sizeChange"
    />
    <autoMaticDialog
      v-if="showAutomatic"
      :selected-rows="selectedRows"
      @handleClose="handleClose"
    />
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import { columnData } from './config/index.js'
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import autoMaticDialog from './components/autoMaticDialog/index.vue'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  components: {
    DebounceFilterSelect,
    autoMaticDialog
  },
  data() {
    return {
      showAutomatic: false,
      selectedRows: [],
      searchFormModel: {
        orderEmpty: 'Y'
      },
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 5,
        pageSizes: [10, 50, 100, 200],
        totalRecordsCount: 0,
        totals: 0
      },
      siteOptions: [],
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }),
      companyOptions: [],
      pageConfig: [
        {
          dataPermission: 'Details',
          activatedRefresh: false,
          isUseCustomSearch: true,
          isCustomSearchHandle: true,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: {
            tools: [
              [
                {
                  id: 'export',
                  icon: 'icon_solid_Import',
                  permission: ['O_02_1643'],
                  title: this.$t('导出')
                },
                // {
                //   id: 'copy',
                //   icon: 'icon_solid_Createorder',
                //   permission: ['O_02_1644'],
                //   title: this.$t('复制')
                // },
                // {
                //   id: 'save',
                //   icon: 'icon_solid_Save',
                //   permission: ['O_02_1645'],
                //   title: this.$t('保存')
                // },
                {
                  id: 'automatic',
                  icon: 'icon_solid_Save',
                  // permission: ['O_02_1645'],
                  title: this.$t('自动补单')
                }
              ],
              ['Setting']
            ]
          },
          gridId: 'c86326d3-55ce-400b-bb5a-7ce39a06f054',
          grid: {
            allowPaging: false,
            columnData: columnData,
            lineSelection: 0, // 选项列
            lineIndex: 1, // 序列号显示位置
            dataSource: []
          }
        }
      ],

      copyData: []
    }
  },
  mounted() {
    this.getCompany()
    this.postSiteFuzzyQuery({})
  },
  methods: {
    handleClose() {
      this.showAutomatic = false
      this.searchForm()
    },
    // 分页器 - 切换分页
    currentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.searchForm()
    },
    // 分页器 - 切换页大小
    sizeChange(currentPageSize) {
      this.pageSettings.pageSize = currentPageSize
      this.pageSettings.currentPage = 1
      this.searchForm()
    },
    handleSearchReset() {
      for (let key in this.searchFormModel) {
        this.searchFormModel[key] = null
      }
      this.searchFormModel['orderEmpty'] = 'Y'
    },
    handleSearchForm() {
      this.copyData = []
      this.pageSettings.currentPage = 1
      this.searchForm()
    },
    searchForm() {
      this.$store.commit('startLoading')
      const param = {
        page: {
          current: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$API.receiptAndDelivery
        .getSupplierOrderDelivery(param)
        .then((res) => {
          if (res.code === 200) {
            this.$set(this.pageConfig[0].grid, 'dataSource', res.data.records)
            this.pageSettings.totals = Math.ceil(
              Number(res.data.total) / this.pageSettings.pageSize
            )
            this.pageSettings.totalRecordsCount = res.data.total
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        page: {
          current: 1,
          pages: 0,
          size: 20
        }
      }
      this.$API.receiptAndDelivery
        .getFactoryInfo(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 工厂 change
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.siteId = itemData.id
        this.searchFormModel.siteCode = itemData.siteCode
        this.searchFormModel.siteName = itemData.siteName
      } else {
        this.searchFormModel.siteId = ''
        this.searchFormModel.siteCode = ''
        this.searchFormModel.siteName = ''
      }
    },
    // 获取主数据-获取指定组织下指定组织层级节点列表 业务公司
    getCompany() {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      this.$API.masterData
        .getCompanyByCode([userInfo.accountName])
        .then((res) => {
          if (res.code === 200) {
            this.companyOptions = res.data.map((item) => {
              return {
                text: item.organizationCode + '-' + item.organizationName,
                value: item.organizationCode
              }
            })
          }
        })
        .catch(() => {})
    },
    // 公司 change
    companyCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.companyId = itemData.id
        this.searchFormModel.companyCode = itemData.customerCode
        this.searchFormModel.companyName = itemData.customerName
      } else {
        this.searchFormModel.companyId = ''
        this.searchFormModel.companyCode = ''
        this.searchFormModel.companyName = ''
      }
    },
    handleClickToolBar(args) {
      const { toolbar, grid } = args
      const selectedRecords = grid.getSelectedRecords()
      const _status = []
      const _saveList = []
      selectedRecords.forEach((item) => {
        _status.push(item.status)
        _saveList.push({
          deliveryCode: item.deliveryCode,
          deliveryLineNo: item.deliveryLineNo,
          id: item.id,
          lineNo: item.lineNo,
          newLineNo: item.newLineNo,
          orderCode: item.orderCode
        })
      })

      const commonToolbar = ['copy', 'save', 'automatic']

      if (selectedRecords.length === 0 && commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id === 'export') {
        this.handleExport()
      }
      if (toolbar.id === 'copy') {
        this.copyData = this.copyData.length !== 0 ? this.copyData : selectedRecords

        if (this.copyData.length > 1) {
          this.$toast({ content: this.$t('请选择一行进行复制！'), type: 'warning' })
          return
        }
        this.handleCopy(grid.dataSource, this.copyData)
      }
      if (toolbar.id === 'save') {
        if (_status.some((i) => i !== 2 && i !== 3)) {
          this.$toast({
            content: this.$t('只有发货中或已完成的数据才能保存!'),
            type: 'warning'
          })
          return
        }
        this.handleSave(_saveList)
      }
      if (toolbar.id === 'automatic') {
        console.log('asdas打啥打', selectedRecords)
        if (selectedRecords.some((i) => i.orderCode || (i.status !== 2 && i.status !== 3))) {
          this.$toast({
            content: this.$t('请选择采购订单号为空且WMS收货状态为发货中或已完成的送货单进行补单！'),
            type: 'warning'
          })
          return
        }
        this.showAutomatic = true
        this.selectedRows = selectedRecords
      }
    },
    handleExport() {
      let obj = JSON.parse(sessionStorage.getItem(this.pageConfig[0].gridId))?.visibleCols
      const headerMap = {}
      if (obj !== undefined && obj.length) {
        obj?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      } else {
        this.pageConfig[0].grid.columnData?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      }
      const params = {
        page: { current: 1, size: 10000 },
        ...this.searchFormModel,
        headerMap
      }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery.exportSupplierOrderDelivery(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleCopy(dataSource, _list) {
      const _this = this
      this.$set(this.pageConfig[0].grid, 'dataSource', _list.concat(dataSource))
      setTimeout(() => {
        _list.map((item) => {
          _this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(item.id)
        })
      }, 500)
    },
    handleSave(_saveList) {
      this.$store.commit('startLoading')

      this.$API.receiptAndDelivery
        .saveSupplierOrderDelivery(_saveList)
        .then((res) => {
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleSearchForm()
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    }
  }
}
</script>
