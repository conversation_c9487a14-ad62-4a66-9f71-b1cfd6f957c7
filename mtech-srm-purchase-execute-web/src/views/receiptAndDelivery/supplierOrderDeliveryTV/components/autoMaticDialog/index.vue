<template>
  <mt-side-bar
    position="Right"
    ref="sidebar"
    :show-backdrop="true"
    :close-on-document-click="false"
    class="side-detail"
    @close="handleClose"
  >
    <div class="right-btn">
      <div class="left-title">{{ $t('无PO送货单自动补PO') }}</div>
      <mt-icon name="icon_Close_1" @click.native="handleClose"></mt-icon>
    </div>
    <ScTable
      ref="xTable"
      :columns="columnData"
      :table-data="tableData"
      :is-show-right-btn="false"
      show-overflow
      height="auto"
      header-align="left"
      align="left"
      style="padding-top: unset"
      :scroll-x="{ gt: 0, oSize: 20 }"
      :scroll-y="{ gt: 0, oSize: 10 }"
    >
      <template slot="custom-tools">
        <!-- :icon="item.icon" -->
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          size="small"
          @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
          >{{ item.name }}</vxe-button
        >
      </template>
    </ScTable>
  </mt-side-bar>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { columnData } from './config/index'
export default {
  components: { ScTable },
  props: {
    // showAutomatic: {
    //   type: Boolean,
    //   default: false
    // },
    selectedRows: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      toolbar: [
        {
          code: 'Refresh',
          name: this.$t('刷新'),
          icon: 'vxe-icon-square-plus',
          status: 'info'
        },
        {
          code: 'Confirm',
          name: this.$t('确定'),
          icon: 'vxe-icon-edit',
          status: 'info'
        }
      ],
      columnData,
      tableData: []
    }
  },
  created() {
    this.preReplenishOrder()
  },
  // watch: {
  //   showAutomatic(newVal) {
  //     if (newVal) {
  //       this.$refs.sidebar.show()
  //     } else {
  //       this.$refs.sidebar.hide()
  //     }
  //   }
  // },
  methods: {
    preReplenishOrder() {
      // 根据selectedRows调接口查询
      const idList = this.selectedRows.map((i) => i.id)
      this.$API.receiptAndDelivery.preReplenishOrder(idList).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.tableData = data
        }
      })
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      if (code === 'Refresh') {
        // 刷新
        this.preReplenishOrder()
      } else if (code === 'Confirm') {
        // 确定 勾选 满足补单条件的  然后关闭弹窗
        const _saveList = []
        selectedRecords.forEach((item) => {
          _saveList.push({
            deliveryCode: item.deliveryCode,
            deliveryLineNo: item.deliveryLineNo,
            id: item.id && item.id.includes('row_') ? null : item.id,
            lineNo: item.lineNo,
            newLineNo: item.deliveryLineNo,
            orderCode: item.orderCode
          })
        })
        if (!_saveList.length) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }

        this.$API.receiptAndDelivery.saveSupplierOrderDelivery(_saveList).then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleClose()
          }
        })
      }
    },
    handleClose() {
      this.$emit('handleClose')
    }
  }
}
</script>

<style lang="scss" scoped>
.side-detail {
  width: 60% !important;
  // top: 60px;
  // height: calc(100% - 60px);
  overflow: auto;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px 0 0 0;

  .right-btn {
    width: 100%;
    font-size: 16px;
    line-height: 1;
    padding: 14px 20px;
    background: rgba(243, 243, 243, 1);
    color: #292929;
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      cursor: pointer;
    }
    .mt-icons {
      font-size: 14px;
      cursor: pointer;
    }
  }

  .info {
    width: 100%;
    padding: 0 40px;
    font-size: 15px;
    ul {
      display: flex;
      li {
        flex: 1;
        display: flex;
        .left-label {
          font-weight: bold;
        }
      }
    }
  }

  .set-country {
    flex: 1;
    .common-template-page {
      background: transparent;

      .page-grid-container {
        box-shadow: unset;
      }

      .e-grid {
        flex: 1;
        height: auto !important;
      }
    }
  }

  .blue-title {
    font-size: 14px;
    padding-left: 8px;
    margin: 20px;
    position: relative;
    color: #2f353c;
    &::before {
      content: '';
      width: 3px;
      height: 70%;
      position: absolute;
      left: 0;
      top: 15%;
      background: rgba(0, 70, 156, 1);
      border-radius: 5px 0 0 5px;
    }

    &.mt0 {
      margin-bottom: 0;
    }
  }
}
</style>
