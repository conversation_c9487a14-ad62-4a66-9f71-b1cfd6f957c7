import { i18n } from '@/main.js'
import UTILS from '@/utils/utils'
export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    ignore: true,
    fixed: 'left'
  },
  {
    minWidth: '200',
    field: 'deliveryCode',
    title: i18n.t('送货单号')
  },
  {
    minWidth: '200',
    field: 'validResult',
    title: i18n.t('补单校验结果')
  },
  {
    minWidth: '90',
    field: 'originalLineNo',
    title: i18n.t('原行号')
  },
  {
    minWidth: '90',
    field: 'deliveryLineNo',
    title: i18n.t('新行号')
  },
  {
    minWidth: '150',
    field: 'orderCode',
    title: i18n.t('采购订单号')
  },
  {
    minWidth: '130',
    field: 'lineNo',
    title: i18n.t('采购订单行号')
  },
  {
    minWidth: '123',
    field: 'deliveryQuantity',
    title: i18n.t('本次送货数量')
  },
  {
    minWidth: '98',
    field: 'receiveQuantity',
    title: i18n.t('收货数量')
  },
  {
    minWidth: '96',
    field: 'rejectQuantity',
    title: i18n.t('拒绝数量')
  },
  {
    minWidth: '200',
    field: 'siteName',
    title: i18n.t('工厂'),
    formatter: ({ cellValue, row }) => {
      return row.siteCode + '-' + cellValue
    }
  },
  {
    minWidth: '135',
    field: 'warehouseName',
    title: i18n.t('交货库存地点'),
    formatter: ({ cellValue, row }) => {
      return row.warehouseCode + '-' + cellValue
    }
  },
  {
    minWidth: '150',
    field: 'itemCode',
    title: i18n.t('物料编码')
  },
  {
    minWidth: '220',
    field: 'itemName',
    title: i18n.t('物料名称')
  },
  {
    minWidth: '200',
    field: 'associateOuterDocNo',
    title: i18n.t('无PO送货申请单编码')
  },
  {
    minWidth: '120',
    field: 'status',
    title: i18n.t('WMS收货状态'),
    formatter: ({ cellValue }) => {
      const StatusSearchOptions = [
        { value: 2, text: i18n.t('发货中'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('已完成'), cssClass: 'col-inactive' },
        { value: 4, text: i18n.t('已取消'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('已关闭'), cssClass: 'col-inactive' },
        { value: 6, text: i18n.t('部分收货'), cssClass: 'col-active' }
      ]
      let item = StatusSearchOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    minWidth: '135',
    field: 'receivingAddress',
    title: i18n.t('收货地址'),
    formatter: ({ cellValue, row }) => {
      return row.warehouseCode + '-' + cellValue
    }
  },
  {
    minWidth: '235',
    field: 'companyName',
    title: i18n.t('公司'),
    formatter: ({ cellValue, row }) => {
      return row.companyCode + '-' + cellValue
    }
  },
  {
    minWidth: '150',
    field: 'supplierCode',
    title: i18n.t('供应商编码')
  },
  {
    minWidth: '220',
    field: 'supplierName',
    title: i18n.t('供应商名称')
  },
  {
    minWidth: '175',
    field: 'buyerOrgCode',
    title: i18n.t('采购组'),
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.buyerOrgName
    }
  },
  {
    minWidth: '66',
    field: 'unitName',
    title: i18n.t('单位')
  },
  {
    minWidth: '97',
    field: 'deliveryType',
    title: i18n.t('送货单类型'),
    formatter: ({ cellValue }) => {
      const StatusSearchOptions = [
        { value: 1, text: i18n.t('采购订单'), cssClass: '' },
        { value: 2, text: i18n.t('交货计划'), cssClass: '' },
        { value: 3, text: i18n.t('JIT'), cssClass: '' },
        { value: 4, text: i18n.t('无需求'), cssClass: '' },
        { value: 5, text: i18n.t('vmi'), cssClass: '' },
        { value: 6, text: i18n.t('钢材'), cssClass: '' }
      ]
      let item = StatusSearchOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    minWidth: '150',
    field: 'syncWmsStatus',
    title: i18n.t('WMS同步状态'),
    formatter: ({ cellValue }) => {
      const StatusSearchOptions = [
        { value: '0', text: i18n.t('未同步'), cssClass: '' },
        { value: '1', text: i18n.t('同步中'), cssClass: '' },
        { value: '2', text: i18n.t('同步成功'), cssClass: '' },
        { value: '3', text: i18n.t('同步失败'), cssClass: '' }
      ]
      let item = StatusSearchOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    minWidth: '150',
    field: 'syncWmsDesc',
    title: i18n.t('WMS同步信息')
  },
  {
    minWidth: '150',
    field: 'syncQmsStatus',
    title: i18n.t('QMS同步状态'),
    formatter: ({ cellValue }) => {
      const StatusSearchOptions = [
        { value: '0', text: i18n.t('未同步'), cssClass: '' },
        { value: '1', text: i18n.t('同步中'), cssClass: '' },
        { value: '2', text: i18n.t('同步成功'), cssClass: '' },
        { value: '3', text: i18n.t('同步失败'), cssClass: '' }
      ]
      let item = StatusSearchOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    minWidth: '150',
    field: 'syncQmsDesc',
    title: i18n.t('QMS同步信息')
  },
  {
    minWidth: '150',
    field: 'syncSapStatus',
    title: i18n.t('SAP同步状态'),
    formatter: ({ cellValue }) => {
      const StatusSearchOptions = [
        { value: '0', text: i18n.t('未同步'), cssClass: '' },
        { value: '1', text: i18n.t('同步中'), cssClass: '' },
        { value: '2', text: i18n.t('同步成功'), cssClass: '' },
        { value: '3', text: i18n.t('同步失败'), cssClass: '' }
      ]
      let item = StatusSearchOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    minWidth: '150',
    field: 'syncSapDesc',
    title: i18n.t('SAP同步信息')
  },
  {
    minWidth: '150',
    field: 'batchCode',
    title: i18n.t('卷号')
  },
  {
    minWidth: '150',
    field: 'takeNo',
    title: i18n.t('车号')
  },
  {
    minWidth: '150',
    field: 'subSiteName',
    title: i18n.t('分厂'),
    formatter: ({ cellValue, row }) => {
      return row.subSiteCode + '-' + cellValue
    }
  },
  {
    field: 'inputDate', //
    title: i18n.t('凭证创建日期'),
    formatter: ({ cellValue }) => {
      if (cellValue && cellValue.length === 13) {
        const e = Number(cellValue)
        return UTILS.dateFormat(e)
      } else {
        return ''
      }
    }
  },
  // {
  //   field: "inputTime", //
  //   title: i18n.t("凭证创建时间"),
  //   template: timeDate({ dataKey: "inputTime", isTime: true }),
  //   searchOptions: {
  //     ...MasterDataSelect.timeRange,
  //   },
  // },
  {
    field: 'postingDate', //
    title: i18n.t('过账日期'),
    formatter: ({ cellValue }) => {
      if (cellValue) {
        return UTILS.dateFormat(cellValue, 'Y-m-d')
      } else {
        return ''
      }
    }
  },
  {
    minWidth: '130',
    field: 'thirdTenantCode',
    title: i18n.t('第三方物流商'),
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.thirdTenantName
    }
  },
  {
    minWidth: '150',
    field: 'vmiWarehouseCode',
    title: i18n.t('VMI仓'),
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.vmiWarehouseName
    }
  },
  {
    minWidth: '75',
    field: 'takeNo',
    title: i18n.t('车牌号')
  },
  {
    minWidth: '150',
    field: 'deliveryNumber',
    title: i18n.t('交货编号')
  },
  {
    minWidth: '150',
    field: 'jitDeliveryNumber',
    title: i18n.t('JIT编号')
  },
  {
    minWidth: '85',
    field: 'jit',
    title: i18n.t('是否JIT'),
    formatter: ({ cellValue }) => {
      const StatusSearchOptions = [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
      let item = StatusSearchOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    minWidth: '100',
    field: 'demandDate',
    title: i18n.t('需求日期')
  },
  {
    minWidth: '100',
    field: 'rejectReason',
    title: i18n.t('拒绝原因')
  },
  {
    minWidth: '90',
    field: 'transferPlanName',
    title: i18n.t('计划员')
  },
  {
    minWidth: '107',
    field: 'workOrderNo',
    title: i18n.t('关联工单号')
  },
  {
    minWidth: '140',
    field: 'saleOrderNo',
    title: i18n.t('关联销售订单号')
  },
  {
    minWidth: '154',
    field: 'saleOrderLineNo',
    title: i18n.t('关联销售订单行号')
  },
  {
    minWidth: '85',
    field: 'bomCode',
    title: i18n.t('BOM号')
  },
  {
    minWidth: '124',
    field: 'productCode',
    title: i18n.t('关联产品代码')
  },
  {
    minWidth: '91',
    field: 'workCenterName',
    title: i18n.t('工作中心')
  },
  {
    minWidth: '91',
    field: 'processName',
    title: i18n.t('工序名称')
  },
  {
    minWidth: '91',
    field: 'receiveAddressName',
    title: i18n.t('送货地址')
  },
  {
    minWidth: '91',
    field: '2',
    title: i18n.t('批次管理')
  },
  {
    minWidth: '68',
    field: '2',
    title: i18n.t('下达')
  },
  {
    minWidth: '91',
    field: 'limitQuantity',
    type: 'number',

    title: i18n.t('限量数量')
  },
  {
    minWidth: '86',
    field: 'warehouseClerkName',
    title: i18n.t('仓管员')
  },
  {
    minWidth: '86',
    field: 'dispatcherName',
    title: i18n.t('调度员')
  },
  {
    minWidth: '107',
    field: 'receiveSupplierName',
    title: i18n.t('收货供应商')
  },
  {
    minWidth: '86',
    field: 'cancelPersonName',
    title: i18n.t('取消人')
  },
  {
    minWidth: '95',
    field: 'cancelTime',
    title: i18n.t('取消时间'),
    formatter: ({ cellValue }) => {
      if (cellValue && cellValue.length === 13) {
        const e = Number(cellValue)
        return UTILS.dateFormat(e)
      } else {
        return ''
      }
    }
  },
  {
    minWidth: '86',
    field: 'closePersonName',
    title: i18n.t('关闭人')
  },
  {
    minWidth: '95',
    field: 'closeTime',
    title: i18n.t('关闭时间'),
    formatter: ({ cellValue }) => {
      if (cellValue && cellValue.length === 13) {
        const e = Number(cellValue)
        return UTILS.dateFormat(e)
      } else {
        return ''
      }
    }
  },
  {
    minWidth: '95',
    field: 'collectorMark',
    title: i18n.t('代收标识')
  },
  {
    minWidth: '86',
    field: 'collectorName',
    title: i18n.t('代收人')
  },
  {
    minWidth: '86',
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    minWidth: '150',
    field: 'createTime',
    title: i18n.t('创建时间')
  },
  {
    minWidth: '86',
    field: 'remark',
    title: i18n.t('行备注')
  }
]
