import { i18n } from '@/main.js'
import Vue from 'vue'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, isDateTime, isDate, isTime } = args

  const template = () => {
    return {
      template: Vue.component('timeDateComponent', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <div v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</div>
            <div v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</div>
          </div>
        </div>`,
        data: function () {
          return { data: {}, dataKey, isDateTime, isDate, isTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
// tab 的 index
export const TabIndex = {
  deliverySchedule: 0, // 交货计划
  purchaseOrder: 1, // 采购订单
  jit: 2 // JIT
}

// Toolbar 按钮
export const Toolbar = {
  // 采购订单
  [TabIndex.purchaseOrder]: [
    {
      id: 'TableExport',
      icon: 'icon_solid_export',
      permission: ['O_02_0635'],
      title: i18n.t('导出')
    }
  ],
  // 交货计划
  [TabIndex.deliverySchedule]: [
    {
      id: 'TableExport',
      icon: 'icon_solid_export',
      permission: ['O_02_0633'],
      title: i18n.t('导出')
    }
  ],
  // JIT供货计划
  [TabIndex.jit]: [
    {
      id: 'TableExport',
      icon: 'icon_solid_export',
      permission: ['O_02_1161'],
      title: i18n.t('导出')
    }
  ]
}

// tab1 交货计划 表格列数据
export const ColumnDataTab1 = [
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  },
  {
    field: 'statusDesc',
    headerText: i18n.t('状态'),
    width: 80
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    width: 130
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂编码'),
    width: 100
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: 110
  },
  {
    field: 'requiredDeliveryDate',
    headerText: i18n.t('需求日期'),
    width: 100
  },
  {
    field: 'numC',
    headerText: i18n.t('需求数量'),
    width: 100
  },
  {
    field: 'supplierNum',
    headerText: i18n.t('本次发货数量'),
    width: 150,
    ignore: true
  },
  {
    field: 'remainCreateQty',
    headerText: i18n.t('剩余可创建数量'),
    width: 150
  },
  {
    field: 'deliveryQty',
    headerText: i18n.t('已发货数量'),
    width: 150
  },
  {
    field: 'transitQty',
    headerText: i18n.t('在途数量'),
    width: 150
  },
  {
    field: 'receivedQuantity',
    headerText: i18n.t('已收货数量'),
    width: 150
  },
  {
    field: 'unClearQuantity',
    headerText: i18n.t('订单未清数量'),
    width: 150
  },
  {
    field: 'bigVersionNo',
    headerText: i18n.t('版本号'),
    width: 100
  },
  {
    field: 'jitFlagDesc',
    headerText: i18n.t('是否JIT'),
    width: 100
  },
  {
    field: 'workCenter',
    headerText: i18n.t('工作中心'),
    width: 150
  },
  {
    field: 'saleOrderNo',
    headerText: i18n.t('销售订单号'),
    width: 150
  },
  {
    field: 'batchFlagDesc',
    headerText: i18n.t('是否按批次'),
    width: 150
  },
  {
    field: 'serialNo',
    headerText: i18n.t('序列号'),
    width: 200
  },
  {
    field: 'rbcCode',
    headerText: i18n.t('RBC编码'),
    width: 120
  },
  {
    field: 'bgType',
    headerText: i18n.t('BG类型'),
    width: 120
  },
  {
    field: 'etaLocationCode',
    headerText: i18n.t('交货地库位'),
    width: 120
  },
  {
    field: 'deliveryAddrName',
    headerText: i18n.t('交货地点'),
    width: 120
  },
  {
    field: 'deliverAddr',
    headerText: i18n.t('发货地点'),
    width: 120
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: 220
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    width: 200
  },
  {
    field: 'mrpArea',
    headerText: i18n.t('计划区域'),
    width: 120
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: 180
  },
  {
    field: 'lineBody',
    headerText: i18n.t('线体'),
    width: 80
  },
  {
    field: 'flag',
    headerText: i18n.t('标识'),
    width: 80
  },
  {
    field: 'projectTextBatch',
    headerText: i18n.t('项目文本批次'),
    width: 150
  },
  {
    field: 'deliveryMethodDesc',
    headerText: i18n.t('配送方式'),
    width: 150
  },
  {
    field: 'outsourcedTypeDesc',
    headerText: i18n.t('委外方式'),
    width: 150
  },
  {
    field: 'processorCode',
    headerText: i18n.t('加工商编号'),
    width: 150
  },
  {
    field: 'processorName',
    headerText: i18n.t('加工商名称'),
    width: 200
  },
  {
    field: 'orderCode',
    headerText: i18n.t('采购订单号'),
    width: 150
  },
  {
    field: 'orderLineNo',
    headerText: i18n.t('采购订单行号'),
    width: 150
  },
  {
    field: 'deliverTypeDesc',
    headerText: i18n.t('送货类型'),
    width: 100
  },
  {
    field: 'releaseFlag',
    headerText: i18n.t('下达'),
    width: 65
  },
  {
    field: 'stockQty',
    headerText: i18n.t('库存数量'),
    width: 150
  },
  {
    field: 'limitQty',
    headerText: i18n.t('限量数量'),
    width: 150
  },
  {
    field: 'plannerName',
    headerText: i18n.t('计划员'),
    width: 80
  },
  {
    field: 'saleOrderRowCode',
    headerText: i18n.t('销售订单行号'),
    width: 150
  },
  {
    field: 'bomNo',
    headerText: i18n.t('BOM号'),
    width: 150
  },
  {
    field: 'checkDate',
    headerText: i18n.t('确认日期'),
    width: 150
  },
  {
    field: 'supplierRemark',
    headerText: i18n.t('供应商备注'),
    width: 150
  },
  {
    field: 'buyerRemark',
    headerText: i18n.t('采购方备注'),
    width: 150
  },
  {
    field: 'wmsInBoundTime',
    headerText: i18n.t('WMS入库时间'),
    width: 150
  },
  {
    field: 'wmsInBoundNum',
    headerText: i18n.t('WMS入库数量'),
    width: 150
  }
]

// tab2 采购计划 表格列数据
export const ColumnDataTab2 = [
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  },
  {
    field: 'orderCode',
    headerText: i18n.t('采购订单号'),
    width: 150
  },
  {
    field: 'itemNo',
    headerText: i18n.t('采购订单行号'),
    width: 150
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂编码'),
    width: 100
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    width: 120
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: 110
  },
  {
    field: 'requiredDeliveryDate',
    headerText: i18n.t('需求日期'),
    width: 100
  },
  {
    field: 'quantity',
    headerText: i18n.t('订单数量')
  },
  {
    field: 'supplierNum',
    headerText: i18n.t('本次发货数量'),
    width: 150
  },
  {
    field: 'unClearQuantity',
    headerText: i18n.t('未清数量')
  },
  {
    field: 'deliveryQty',
    headerText: i18n.t('已发货数量')
  },
  {
    field: 'transitQty',
    headerText: i18n.t('在途数量')
  },
  {
    field: 'receiveQty',
    headerText: i18n.t('已入库数量')
  },
  {
    field: 'receiveAddress',
    headerText: i18n.t('收货地址'),
    width: 300
  },
  {
    field: 'createUserName',
    headerText: i18n.t('收货联系人'),
    width: 120
  },
  {
    field: '',
    headerText: i18n.t('收货联系方式')
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码'),
    width: 100
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: 250
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: 180
  },
  {
    field: 'saleOrderNo',
    headerText: i18n.t('关联销售订单'),
    width: 200
  },
  {
    field: 'saleOrderRowCode',
    headerText: i18n.t('关联销售订单行号'),
    width: 200
  },
  {
    field: 'batchFlagDesc',
    headerText: i18n.t('是否按批次'),
    width: 150
  },
  {
    field: 'projectTextBatch',
    headerText: i18n.t('项目文本批次')
  },
  {
    field: 'buyerRemark',
    headerText: i18n.t('采购方备注')
  },
  {
    field: 'supplierRemark',
    headerText: i18n.t('供应商备注')
  }
]

// tab3 JIT 表格列数据
export const ColumnDataTab3 = [
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  },
  {
    field: 'statusDesc',
    headerText: i18n.t('状态'),
    width: 80
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    width: 130
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂编码'),
    width: 100
  },
  {
    field: 'workCenterCode',
    headerText: i18n.t('工作中心编码'),
    width: 150
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: 110
  },
  {
    field: 'requiredDeliveryDate',
    headerText: i18n.t('需求日期'),
    width: 150
  },
  {
    field: 'requiredDeliveryTime',
    headerText: i18n.t('需求时间'),
    width: 100
  },
  {
    field: 'supplierNum',
    headerText: i18n.t('本次发货数量'),
    width: 150,
    ignore: true
  },
  {
    field: 'remainCreateQty',
    headerText: i18n.t('剩余可创建数量'),
    width: 150
  },
  {
    field: 'deliveryQty',
    headerText: i18n.t('已发货数量'),
    width: 150
  },
  {
    field: 'transitQty',
    headerText: i18n.t('在途数量'),
    width: 150
  },
  {
    field: 'receivedQuantity',
    headerText: i18n.t('已收货数量'),
    width: 150
  },
  {
    field: 'unClearQuantity',
    headerText: i18n.t('订单未清数量'),
    width: 150
  },
  {
    field: 'saleOrderNo',
    headerText: i18n.t('销售订单号'),
    width: 150
  },
  {
    field: 'batchFlagDesc',
    headerText: i18n.t('是否按批次'),
    width: 150
  },
  {
    field: 'serialNo',
    headerText: i18n.t('序列号'),
    width: 200
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: 220
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    width: 200
  },
  {
    field: 'workCenter',
    headerText: i18n.t('工作中心名称'),
    width: 150
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: 180
  },
  {
    field: 'senderAddress',
    headerText: i18n.t('收货地址'),
    width: 200
  },
  {
    field: 'receiverContactName',
    headerText: i18n.t('收货联系人'),
    width: 120
  },
  {
    field: 'receiverContact',
    headerText: i18n.t('收货联系方式'),
    width: 120
  },
  {
    field: 'plannerName',
    headerText: i18n.t('调度员'),
    width: 80
  },
  {
    field: 'deliverTypeDesc',
    headerText: i18n.t('送货类型'),
    width: 100
  },
  {
    field: 'jitFlagDesc',
    headerText: i18n.t('是否JIT'),
    width: 100
  },
  {
    field: 'projectTextBatch',
    headerText: i18n.t('项目文本批次'),
    width: 150
  },
  {
    field: 'supplierRemark',
    headerText: i18n.t('供应商备注'),
    width: 150
  },
  {
    field: 'buyerRemark',
    headerText: i18n.t('采购方备注'),
    width: 150
  },
  {
    field: 'wmsInBoundTime',
    headerText: i18n.t('WMS入库时间'),
    width: 150
  },
  {
    field: 'wmsInBoundNum',
    headerText: i18n.t('WMS入库数量'),
    width: 150
  }
]

// tab4 屏发货指导 表格列数据
export const ColumnDataTab4 = [
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  },
  {
    field: 'statusDesc',
    headerText: i18n.t('状态'),
    width: 80
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    width: 130
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂编码'),
    width: 100
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: 110
  },
  {
    field: 'requiredDeliveryDate',
    headerText: i18n.t('需求日期'),
    width: 100
  },
  {
    field: 'numC',
    headerText: i18n.t('需求数量'),
    width: 100
  },
  {
    field: 'supplierNum',
    headerText: i18n.t('本次发货数量'),
    width: 150,
    ignore: true
  },
  {
    field: 'purchaseOrder',
    headerText: i18n.t('采购订单号'),
    width: 150
  },
  {
    field: 'etaLocationCode',
    headerText: i18n.t('交货地库位'),
    width: 120
  },
  {
    field: 'deliveryAddrName',
    headerText: i18n.t('交货地点'),
    width: 120
  },
  {
    field: 'deliverAddr',
    headerText: i18n.t('发货地点'),
    width: 120
  },
  {
    field: 'remainCreateQty',
    headerText: i18n.t('剩余可创建数量'),
    width: 150
  },
  {
    field: 'deliveryQty',
    headerText: i18n.t('已发货数量'),
    width: 150
  },
  {
    field: 'transitQty',
    headerText: i18n.t('在途数量'),
    width: 150
  },
  {
    field: 'receivedQuantity',
    headerText: i18n.t('已收货数量'),
    width: 150
  },
  {
    field: 'unClearQuantity',
    headerText: i18n.t('订单未清数量'),
    width: 150
  },
  {
    field: 'bigVersionNo',
    headerText: i18n.t('版本号'),
    width: 100
  },
  {
    field: 'jitFlagDesc',
    headerText: i18n.t('是否JIT'),
    width: 100
  },
  {
    field: 'workCenter',
    headerText: i18n.t('工作中心'),
    width: 150
  },
  {
    field: 'saleOrderNo',
    headerText: i18n.t('销售订单号'),
    width: 150
  },
  {
    field: 'batchFlagDesc',
    headerText: i18n.t('是否按批次'),
    width: 150
  },
  {
    field: 'serialNo',
    headerText: i18n.t('序列号'),
    width: 200
  },
  {
    field: 'rbcCode',
    headerText: i18n.t('RBC编码'),
    width: 120
  },
  {
    field: 'bgType',
    headerText: i18n.t('BG类型'),
    width: 120
  },
  {
    field: 'virtualSupplierCode',
    headerText: i18n.t('虚拟供应商代码'),
    width: 140
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: 220
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    width: 200
  },
  {
    field: 'mrpArea',
    headerText: i18n.t('计划区域'),
    width: 120
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: 180
  },
  {
    field: 'lineBody',
    headerText: i18n.t('线体'),
    width: 80
  },
  {
    field: 'flag',
    headerText: i18n.t('标识'),
    width: 80
  },
  {
    field: 'projectTextBatch',
    headerText: i18n.t('项目文本批次'),
    width: 150
  },
  {
    field: 'deliveryMethodDesc',
    headerText: i18n.t('配送方式'),
    width: 150
  },
  {
    field: 'outsourcedTypeDesc',
    headerText: i18n.t('委外方式'),
    width: 150
  },
  {
    field: 'processorCode',
    headerText: i18n.t('加工商编号'),
    width: 150
  },
  {
    field: 'processorName',
    headerText: i18n.t('加工商名称'),
    width: 200
  },
  {
    field: 'orderLineNo',
    headerText: i18n.t('采购订单行号'),
    width: 150
  },
  {
    field: 'deliverTypeDesc',
    headerText: i18n.t('送货类型'),
    width: 100
  },
  {
    field: 'releaseFlag',
    headerText: i18n.t('下达'),
    width: 65
  },
  {
    field: 'stockQty',
    headerText: i18n.t('库存数量'),
    width: 150
  },
  {
    field: 'limitQty',
    headerText: i18n.t('限量数量'),
    width: 150
  },
  {
    field: 'plannerName',
    headerText: i18n.t('计划员'),
    width: 80
  },
  {
    field: 'saleOrderRowCode',
    headerText: i18n.t('销售订单行号'),
    width: 150
  },
  {
    field: 'bomNo',
    headerText: i18n.t('BOM号'),
    width: 150
  },
  {
    field: 'checkDate',
    headerText: i18n.t('确认日期'),
    width: 150
  },
  {
    field: 'supplierRemark',
    headerText: i18n.t('供应商备注'),
    width: 150
  },
  {
    field: 'buyerRemark',
    headerText: i18n.t('采购方备注'),
    width: 150
  },
  {
    field: 'wmsInBoundTime',
    headerText: i18n.t('WMS入库时间'),
    width: 150
  },
  {
    field: 'wmsInBoundNum',
    headerText: i18n.t('WMS入库数量'),
    width: 150
  }
]
