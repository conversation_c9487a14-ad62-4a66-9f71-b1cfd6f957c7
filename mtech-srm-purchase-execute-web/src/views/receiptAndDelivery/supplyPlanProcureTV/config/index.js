/* eslint-disable */
import { shipmentsNum, timeDate } from './columnComponent'
import { i18n } from '@/main.js'
import { codeNameColumn } from '@/utils/utils'

// 格式化表格动态数据
export const formatTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: col.width ? col.width : '150'
    }
    colData.push(defaultCol)
  })

  return colData
}
