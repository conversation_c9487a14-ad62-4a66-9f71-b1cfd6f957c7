<template>
  <!-- 要货排期列表 -->
  <div class="orderConfig full-height vertical-flex-box">
    <mt-template-page
      ref="templateRef1"
      :current-tab="currentTab"
      :template-config="pageConfig"
      :permission-obj="permissionObj"
    >
      <div slot="slot-0" class="full-height">
        <plan></plan>
      </div>
      <div slot="slot-1" class="full-height">
        <order :is-detail="'2'" version="2"></order>
      </div>
      <div slot="slot-2" class="full-height">
        <JIT :is-detail="'2'" version="2"></JIT>
      </div>
      <div slot="slot-3" class="full-height">
        <Guide></Guide>
      </div>
    </mt-template-page>
  </div>
</template>
<script>
export default {
  components: {
    plan: () => import('./pages/plan.vue'),
    order: () => import('./pages/order.vue'),
    JIT: () => import('./pages/JIT.vue'),
    Guide: () => import('./pages/guide.vue')
  },
  data() {
    return {
      currentTab: 0,
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'aaaaa', permissionCode: 'T_02_0023' },
          { dataPermission: 'bbbbb', permissionCode: 'T_02_0022' },
          { dataPermission: 'ccccc', permissionCode: 'T_02_0115' },
          { dataPermission: 'ddddd', permissionCode: 'T_02_0199' }
        ]
      },
      pageConfig: [
        {
          title: this.$t('交货计划'),
          dataPermission: 'aaaaa',
          permissionCode: 'T_02_0023'
        },
        {
          title: this.$t('采购订单'),
          dataPermission: 'bbbbb',
          permissionCode: 'T_02_0022'
        },
        {
          title: this.$t('JIT'),
          dataPermission: 'ccccc',
          permissionCode: 'T_02_0115'
        },
        {
          title: this.$t('屏发货指导'),
          dataPermission: 'ddddd',
          permissionCode: 'T_02_0199'
        }
      ]
    }
  },
  created() {
    if (this.$route.query.from === 'mytodo') {
      if (JSON.parse(sessionStorage.getItem('todoDetail')).defaultRules === null) {
        this.currentTab = 0
      } else {
        this.currentTab = 2

        this.componentConfig[2].grid.asyncConfig.defaultRules = JSON.parse(
          sessionStorage.getItem('todoDetail')
        ).defaultRules
      }
    } else {
      // 下方的循环是为了解决tab因为配置了权限导致的表格内容不渲染
      this.pageConfig.forEach((item, index) => {
        const elementPermissionSet = window.elementPermissionSet
        if (elementPermissionSet.includes(item.permissionCode)) {
          this.currentTab = index
        }
      })
    }
  }
}
</script>
