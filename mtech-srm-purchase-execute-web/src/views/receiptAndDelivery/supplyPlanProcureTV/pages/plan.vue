<!-- 采方 - 供货计划-泛智屏 - 交货计划 -->
<template>
  <div>
    <div class="full-height" ref="tableContainer">
      <!-- 列模板 -->
      <mt-template-page
        ref="templateRef"
        :hidden-tabs="true"
        :basic-expand="false"
        :template-config="componentConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleCustomReset="handleCustomReset"
        @handleCustomSearch="handleCustomSearch('handle')"
        @cellEdit="cellEdit"
      >
        <template v-slot:quick-search-form>
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item :label="$t('序列号')" label-style="top">
              <mt-input
                v-model="searchFormModel.serialNumber"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <!-- <mt-form-item prop="companyCode" :label="$t('公司编码')" label-style="top">
              <mt-input v-model="searchFormModel.companyCode" />
            </mt-form-item> -->
            <mt-form-item prop="siteCode" :label="$t('工厂编码')" label-style="top">
              <!-- <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.siteCodeList"
                :url="$API.masterData.getSiteAuthFuzzyUrl"
                multiple
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                params-key="fuzzyParam"
                records-position="data"
              ></RemoteAutocomplete> -->
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.siteCodeList"
                url="/srm-purchase-execute/tenant/common/permission/querySiteList"
                multiple
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'dimensionNameValue', value: 'dimensionCodeValue' }"
                records-position="data"
                params-key="keyWord"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.itemCode"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                :show-clear-button="true"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.itemName"
                :placeholder="$t('支持模糊搜索')"
                :show-clear-button="true"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.supplierCode"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                :show-clear-button="true"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.supplierName"
                :placeholder="$t('支持模糊搜索')"
                :show-clear-button="true"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="demandDate" :label="$t('需求日期')" label-style="top">
              <mt-date-range-picker
                style="flex: 1"
                v-model="searchFormModel.demandDate"
                :show-clear-button="true"
                :placeholder="$t('需求日期')"
                @change="(e) => demandDateChange(e, 'demandDate')"
              />
            </mt-form-item>
            <mt-form-item prop="jitFlag" :label="$t('是否JIT')" label-style="top">
              <mt-select
                v-model="searchFormModel.jitFlag"
                css-class="rule-element"
                :data-source="[
                  { value: 0, text: $t('否'), cssClass: '' },
                  { value: 1, text: $t('是'), cssClass: '' }
                ]"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择是否JIT')"
              />
            </mt-form-item>
            <mt-form-item prop="saleOrderNo" :label="$t('销售订单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.saleOrderNo"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="workCenterCode" :label="$t('工作中心')" label-style="top">
              <mt-input
                v-model="searchFormModel.workCenterCode"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="plannerName" :label="$t('计划员')" label-style="top">
              <mt-input
                v-model="searchFormModel.plannerName"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="bigVersionNo" :label="$t('版本号')" label-style="top">
              <mt-select
                v-model="searchFormModel.bigVersionNo"
                :data-source="versionList"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="processorCode" :label="$t('加工商编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.processorCode"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="processorName" :label="$t('加工商名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.processorName"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
          </mt-form>
        </template>
      </mt-template-page>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totals"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>
<script>
import { ColumnDataTab1, Toolbar, TabIndex } from '../config/constant'
import { download, getHeadersFileName } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      pageSettings: {
        currentPage: 1,
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totals: 0, // 页数
        pageSizes: [10, 50, 100, 200, 1000]
      },
      searchFormModel: {
        serialNumber: null,
        itemCode: null,
        itemName: null,
        orderCode: null,
        demandDate: [],
        demandDateStart: null,
        demandDateEnd: null
        // demandDate: [new Date(), new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0)],
        // demandDateStart: dayjs(dayjs().startOf('date').format('YYYY-MM-DD HH:mm:ss')).valueOf(),
        // demandDateEnd: dayjs(dayjs().endOf('month').format('YYYY-MM-DD HH:mm:ss')).valueOf()
      },
      versionList: [],
      apiWaitingQuantity: 0, // 调用的api正在等待数
      initialSort: {
        columns: [{ field: 'remainingQty', direction: 'Descending' }]
      },
      editData: [], // 行编辑过的数据 { id, index, key, value }
      zeroFilterSwitch: 1, // 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      zeroFilterPlan: 1, // 交货计划 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      zeroFilterOrder: 1, // 采购订单 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      zeroFilterJit: 1, // JIT 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      componentConfig: [
        {
          title: this.$t('交货计划'),
          dataPermission: 'Plan',
          permissionCode: 'T_02_0023',
          activatedRefresh: false,
          isUseCustomSearch: true,
          isCustomSearchHandle: true,
          toolbar: { tools: [Toolbar[TabIndex.deliverySchedule], ['Setting']] },
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId: 'dd53b2ca-9790-4493-903a-fce9db90ee62',
          grid: {
            enableVirtualization: true,
            virtualPageSize: 30,
            selectionSettings: {
              persistSelection: true, // 这个选项很重要，影响到虚拟滚动下能不能操作全选按钮
              type: 'Multiple',
              checkboxOnly: true
            },
            allowPaging: false,
            customSelection: true, // 使用自定义勾选列
            lineIndex: 0, // 序号列
            columnData: ColumnDataTab1,
            dataSource: []
          }
        }
      ]
    }
  },
  mounted() {
    // setTimeout(() => {
    //   this.$set(this.pageSettings, 'pageSize', 50)
    // }, 500)
    this.buyerGoodsDemandPlanInfoQueryVersionTv()
  },
  methods: {
    // 获取版本下拉列表
    buyerGoodsDemandPlanInfoQueryVersionTv() {
      this.$API.deliverySchedule.buyerGoodsDemandPlanInfoQueryVersionTv().then((res) => {
        const { data, code } = res
        if (code === 200) {
          this.versionList = data
          // this.searchFormModel.bigVersionNo = data[0]
        }
      })
    },
    demandDateChange(e, prefix) {
      if (e.startDate) {
        this.searchFormModel[prefix + 'Start'] = dayjs(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        ).valueOf()
        this.searchFormModel[prefix + 'End'] = dayjs(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        ).valueOf()
      } else {
        this.searchFormModel[prefix + 'Start'] = null
        this.searchFormModel[prefix + 'End'] = null
      }
    },
    // 查询条件重置
    handleCustomReset() {
      this.pageSettings.currentPage = 1
      for (let key in this.searchFormModel) {
        if (key) {
          this.searchFormModel[key] = null
          // if (key === 'bigVersionNo') {
          //   this.searchFormModel[key] = this.versionList[0]
          // }
        }
      }
    },
    // 列表查询
    handleCustomSearch(type) {
      this.$store.commit('startLoading')
      const param = {
        pageNum: type === 'handle' ? 1 : this.pageSettings.currentPage,
        pageSize: this.pageSettings.pageSize,
        ...this.searchFormModel
      }
      const _Api = this.$API.receiptAndDelivery.getplanByDeliverProcureListTv
      _Api(param)
        .then((res) => {
          if (res && res.code === 200) {
            setTimeout(() => {
              document.querySelector('.e-content').scrollTop = 0
            }, 20)
            const _list = res.data?.records.map((item) => {
              if (item['remainCreateQty']) {
                item['supplierNum'] = Number(item['remainCreateQty'].replace(/,/g, ''))
                return item
              }
            })
            this.$set(this.componentConfig[0].grid, 'dataSource', _list)
            this.pageSettings.totals = Math.ceil(
              Number(res.data.total) / this.pageSettings.pageSize
            )
            this.pageSettings.currentPage = Number(res.data.current)
            this.pageSettings.totalRecordsCount = res.data.total
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    // 切换页码
    handleCurrentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.handleCustomSearch()
    },
    // 更换页大小
    handleSizeChange(pageSize) {
      this.pageSettings.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // ToolBar
    handleClickToolBar(args) {
      const { toolbar, gridRef } = args
      // const selectRowIndexs = gridRef.customSelectedRowIndexs
      if (toolbar.id === 'TableExport') {
        let _selectRow = []
        gridRef.dataSource.forEach((item) => {
          if (item.customChecked) {
            _selectRow.push({
              id: item.id,
              requiredDeliveryDate: item.requiredDeliveryDate
            })
          }
        })
        // 导出
        this.handleExport(_selectRow)
      }
    },
    // CellTool
    handleClickCellTool() {},
    // 导出
    handleExport(list) {
      // 交货计划-导出
      this.handlePlanExport(list)
    },
    // 交货计划-导出
    handlePlanExport(list) {
      let obj = JSON.parse(sessionStorage.getItem(this.componentConfig[0].gridId))?.visibleCols
      const headerMap = {}
      if (obj !== undefined && obj.length) {
        obj?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'serialNumber') {
            headerMap[i.field] = i.headerText
          }
        })
      } else {
        ColumnDataTab1?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'serialNumber') {
            headerMap[i.field] = i.headerText
          }
        })
      }
      const param = {
        ...this.searchFormModel,
        exportList: [...list],
        headerMap,
        pageNum: this.pageSettings.currentPage,
        pageSize: this.pageSettings.pageSize
      }
      this.apiStartLoading()
      this.$API.receiptAndDelivery.exportPlanByDeliverProcureTv(param).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 采购订单-导出
    handleOrderExport(list) {
      const params = {
        ...this.searchFormModel,
        exportList: [...list],
        pageNum: this.pageSettings.currentPage,
        pageSize: this.pageSettings.pageSize
      }
      params.zeroFilter = this.zeroFilter
      this.apiStartLoading()
      this.$API.receiptAndDelivery.postSupplierDeliverySupplyOrderExportTv(params).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // Jit-导出
    handleJitExport(list) {
      const params = {
        ...this.searchFormModel,
        exportList: [...list],
        pageNum: this.pageSettings.currentPage,
        pageSize: this.pageSettings.pageSize
      }
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyJitExportTv(params, {
          query: this.zeroFilterPlan
        })
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
    },
    // 批量创建送货单
    handleBatchPreCreateDelivery(selectRows) {
      console.log('flag')
      const selectRowsData = this.handleMatchEditDataById(selectRows, this.editData)
      console.log(this.editData)
      console.log(selectRows)

      // 校验 发货数量
      if (!this.checkTheNumberOfShipments(selectRowsData)) {
        // 校验失败
        return
      }

      const params = []
      selectRowsData.forEach((item) => {
        if (item.supplierNum > 0) {
          params.push({
            id: item.id,
            supplierNum: item.supplierNum, // 发货数量 前端定义
            requiredDeliveryDate: item.requiredDeliveryDate,
            supplierTenantId: item.supplierTenantId // 供应商租户id
          })
        }
      })

      if (params.length === 0) {
        this.$toast({ content: this.$t('未填写发货数量'), type: 'warning' })
        return
      }

      // 交货计划-预创建送货单
      this.postSupplierDeliverySupplyPlanPlanPreCreate(params)
    },
    // 创建无采购订单送货单
    handleCreateNoOrder(selectRows) {
      const selectRowsData = this.handleMatchEditDataById(selectRows, this.editData)
      const currentTabIndex = this.$refs.templateRef.currentTabIndex

      const params = []
      selectRowsData.forEach((item) => {
        if (item.supplierNum > 0) {
          params.push({
            id: item.id,
            num: item.supplierNum, // 发货数量 前端定义
            supplierTenantId: item.supplierTenantId ?? null // 供应商租户id
          })
        }
      })

      if (params.length === 0) {
        this.$toast({ content: this.$t('未填写发货数量'), type: 'warning' })
        return
      }

      if (currentTabIndex === TabIndex.jit) {
        // 供方收发货供货计划-JIT创建无订单送货单
        this.postSupplierDeliverySupplyPlanJitCreateNoOrder(params)
      } else if (currentTabIndex === TabIndex.deliverySchedule) {
        // 供方收发货供货计划-交货计划创建无订单送货单
        this.postSupplierDeliverySupplyPlanPlanCreateNoOrder(params)
      }
    },
    // 校验 发货数量
    checkTheNumberOfShipments(rowsData) {
      let isValid = true
      let isCheckPreDeliveryQty = false // 校验 采购订单tab 待发货数量
      let isCheckRemainingQty = false // 校验 交货计划tab、JIT tab 剩余可创建数量
      rowsData.forEach((itemRowData) => {
        if (itemRowData.preDeliveryQty !== undefined && !isNaN(itemRowData.preDeliveryQty)) {
          isCheckPreDeliveryQty = true
          // 采购订单tab 校验 待发货数量
          if (itemRowData.supplierNum > itemRowData.numC) {
            isValid = false
          }
        } else if (itemRowData.remainingQty !== undefined && !isNaN(itemRowData.remainingQty)) {
          isCheckRemainingQty = true
          // 交货计划tab、JIT tab 剩余可创建数量
          if (itemRowData.supplierNum > itemRowData.remainingQty) {
            isValid = false
          }
        }
      })
      if (isCheckPreDeliveryQty && !isValid) {
        this.$toast({
          content: this.$t('发货数量不可大于待发货数量'),
          type: 'warning'
        })
      } else if (isCheckRemainingQty && !isValid) {
        this.$toast({
          content: this.$t('发货数量不可大于剩余可创建数量'),
          type: 'warning'
        })
      }
      return isValid
    },
    // selectRows 与 editData 匹配 id，返回正确的数据，因为表格中 template 的数据与 dataSource 的数据不一致
    handleMatchEditDataById(selectRows, editData) {
      console.log(1324123413243)
      const result = []
      selectRows.forEach((item) => {
        editData.forEach((editItem) => {
          if (item.id === editItem.id) {
            item[editItem.key] = editItem.value
          }
        })
        result.push(item)
      })
      return result
    },
    // 行编辑
    cellEdit(e) {
      const { id, index, key, value } = e
      if (id !== undefined) {
        // 如果是编辑行
        const editIndex = this.editData.findIndex((item) => item.id === id && item.key === key)
        if (editIndex >= 0) {
          // 更新编辑的数据
          this.editData[editIndex].value = value
        } else {
          // 保存编辑的数据，value 为空也存
          this.editData.push({
            id,
            key,
            index,
            value
          })
        }
      }
    },
    // 跳转到批量创建送货单
    goToBatchDeliverySupplier(args) {
      const { data, params } = args
      // 预创建订单数据 保存到 localStorage
      localStorage.setItem('batchDeliverySupplierDataTv', JSON.stringify(data))
      // 预创建订单请求参数 保存到 localStorage
      localStorage.setItem('batchDeliverySupplierParamsTv', JSON.stringify(params))
      // 将 lastTabIndex 放到 localStorage
      localStorage.setItem('lastTabIndex', JSON.stringify(0))
      // 页面跳转
      this.editData = []
      this.$router.push({
        name: 'batch-delivery-supplier-tv',
        query: {
          type: 0, // 页面类型
          timeStamp: new Date().getTime()
        }
      })
    },
    // 供方收发货供货计划-订单预创建送货单
    postSupplierDeliverySupplyPlanOrderPreCreate(params) {
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanOrderPreCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res && res?.code == 200) {
            // 跳转-批量创建送货单
            this.goToBatchDeliverySupplier({ data: res.data || [], params })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-交货计划预创建送货单
    postSupplierDeliverySupplyPlanPlanPreCreate(params) {
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanPlanPreCreateTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 跳转-批量创建送货单
            this.goToBatchDeliverySupplier({ data: res.data || [], params })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-JIT预创建送货单
    postSupplierDeliverySupplyPlanJitPreCreate(params) {
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanJitPreCreateTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res && res?.code == 200) {
            // this.$toast({
            //   content: this.$t("操作成功"),
            //   type: "success",
            // });
            // 跳转-批量创建送货单
            this.goToBatchDeliverySupplier({ data: res.data || [], params })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-JIT创建无订单送货单
    postSupplierDeliverySupplyPlanJitCreateNoOrder(params) {
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanJitCreateNoOrder(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            // 刷新-当前表格数据
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-交货计划创建无订单送货单
    postSupplierDeliverySupplyPlanPlanCreateNoOrder(params) {
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanPlanCreateNoOrder(params)
        .then((res) => {
          this.apiEndLoading()
          if (res && res?.code == 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            // 刷新-当前表格数据
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 是否显示不可创建送货单数据
    handleChangeZeroFilterSwitch(value) {
      console.log('value', value)
      // 保存当前 tab 选择的 zeroFilterSwitch 状态
      // 并修改列模板请求参数，调用api获取数据
      const currentTabIndex = this.$refs.templateRef.currentTabIndex
      if (currentTabIndex == TabIndex.deliverySchedule) {
        this.zeroFilterPlan = value
        // 往列表中添加属性
        // this.$set(
        //   // this.componentConfig[currentTabIndex].grid.asyncConfig.query,
        //   "zeroFilter",
        //   value
        // );
      } else if (currentTabIndex == TabIndex.purchaseOrder) {
        this.zeroFilterOrder = value
        this.$set(this.componentConfig[currentTabIndex].grid.asyncConfig.query, 'zeroFilter', value)
      } else if (currentTabIndex == TabIndex.jit) {
        this.zeroFilterJit = value
        // this.$set(
        //   // this.componentConfig[currentTabIndex].grid.asyncConfig.query,
        //   "zeroFilter",
        //   value
        // );
      }
    },
    // 切换tab
    handleSelectTab(index) {
      console.log(111111)
      console.log(index)
      // 将各自 tab 保存的 zeroFilterSwitch 赋值给 zeroFilterSwitch
      // 并修改列模板请求参数，调用api获取数据
      if (index == TabIndex.deliverySchedule) {
        this.zeroFilterSwitch = this.zeroFilterPlan
        // this.$set(
        //   this.componentConfig[index].grid.asyncConfig.query,
        //   "zeroFilter",
        //   this.zeroFilterPlan
        // );
      } else if (index == TabIndex.purchaseOrder) {
        this.zeroFilterSwitch = this.zeroFilterOrder
        // this.$set(
        //   this.componentConfig[index].grid.asyncConfig.query,
        //   "zeroFilter",
        //   this.zeroFilterOrder
        // );
      } else if (index == TabIndex.jit) {
        this.zeroFilterSwitch = this.zeroFilterJit
        // this.$set(
        //   this.componentConfig[index].grid.asyncConfig.query,
        //   "zeroFilter",
        //   this.zeroFilterJit
        // );
      }
      this.handleCustomReset()
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('todoDetail')
  }
}
</script>

<style lang="scss" scoped>
// 表格容器
#table-container {
  position: relative;

  // 表格数据视图
  /deep/ .e-gridcontent > .e-content {
    height: 0px;
    transition: height 0.5s ease;
  }

  // 分页
  /deep/ .mt-pagertemplate {
    margin: 10px 0 10px !important;
  }
}
// 显示不可创建送货单数据
.zero-filter-switch {
  text-align: right;
}
</style>
