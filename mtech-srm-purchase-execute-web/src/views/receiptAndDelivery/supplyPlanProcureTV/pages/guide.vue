<!-- eslint-disable prettier/prettier -->
<template>
  <div>
    <!-- 供货计划 - 泛智屏 -->
    <div class="full-height" ref="tableContainer">
      <!-- 列模板 -->
      <mt-template-page
        ref="templateRef"
        :hidden-tabs="true"
        :basic-expand="false"
        :template-config="componentConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleCustomReset="handleCustomReset"
        @handleCustomSearch="handleCustomSearch('handle')"
      >
        <template v-slot:quick-search-form>
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <!-- <mt-form-item :label="$t('序列号')" label-style="top">
            <mt-input v-model="searchFormModel.serialNumber"></mt-input>
          </mt-form-item> -->
            <mt-form-item prop="companyCode" :label="$t('公司编码')" label-style="top">
              <mt-input v-model="searchFormModel.companyCode" />
            </mt-form-item>
            <mt-form-item prop="siteCode" :label="$t('工厂编码')" label-style="top">
              <!-- <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.siteCodeList"
                :url="$API.masterData.getSiteAuthFuzzyUrl"
                multiple
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                params-key="fuzzyParam"
                records-position="data"
              ></RemoteAutocomplete> -->
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.siteCodeList"
                url="/srm-purchase-execute/tenant/common/permission/querySiteList"
                multiple
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'dimensionNameValue', value: 'dimensionCodeValue' }"
                records-position="data"
                params-key="keyWord"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
              <mt-input v-model="searchFormModel.itemCode"></mt-input>
            </mt-form-item>
            <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
              <mt-input v-model="searchFormModel.itemName"></mt-input>
            </mt-form-item>
            <mt-form-item prop="demandDate" :label="$t('需求日期')" label-style="top">
              <mt-date-range-picker
                style="flex: 1"
                v-model="searchFormModel.demandDate"
                :show-clear-button="true"
                :placeholder="$t('需求日期')"
                @change="(e) => demandDateChange(e, 'demandDate')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.supplierCode"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择供应商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item
              prop="virtualSupplierCodeList"
              :label="$t('虚拟供应商代码')"
              label-style="top"
            >
              <mt-multi-select
                style="flex: 1"
                :show-clear-button="true"
                v-model="searchFormModel.virtualSupplierCodeList"
                :data-source="virtualSupplierCodeList"
                :placeholder="$t('请选择')"
                :allow-filtering="true"
                :fields="{ text: 'codeAndName', value: 'groupSupplierCode' }"
                filter-type="Contains"
              ></mt-multi-select>
            </mt-form-item>
            <mt-form-item prop="bigVersionNo" :label="$t('版本号')" label-style="top">
              <mt-input v-model="searchFormModel.bigVersionNo"></mt-input>
            </mt-form-item>
          </mt-form>
        </template>
      </mt-template-page>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totals"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>
<!-- eslint-disable prettier/prettier -->
<!-- eslint-disable prettier/prettier -->
<!-- eslint-disable prettier/prettier -->
<script>
import { ColumnDataTab4 } from '../config/constant'
// import { BASE_TENANT } from "@/utils/constant";
import { download, getHeadersFileName } from '@/utils/utils'
import dayjs from 'dayjs'
// import collapseSearch from '@/components/collapseSearch'

export default {
  components: {},
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      pageSettings: {
        currentPage: 1,
        pageCount: 5,
        pageSize: 10, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totals: 0, // 页数
        pageSizes: [10, 20, 50, 100, 200, 1000]
      },
      searchFormModel: {
        serialNumber: null,
        companyCode: null,
        siteCode: null,
        itemCode: null,
        itemName: null,
        orderCode: null,
        demandDate: [],
        demandDateStart: null,
        demandDateEnd: null
      },
      apiWaitingQuantity: 0, // 调用的api正在等待数
      initialSort: {
        columns: [{ field: 'remainingQty', direction: 'Descending' }]
      },
      editData: [], // 行编辑过的数据 { id, index, key, value }
      zeroFilterSwitch: 1, // 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      zeroFilterPlan: 1, // 交货计划 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      zeroFilterOrder: 1, // 采购订单 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      zeroFilterJit: 1, // JIT 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      componentConfig: [
        {
          activatedRefresh: false,
          isUseCustomSearch: true,
          isCustomSearchHandle: true,
          toolbar: [
            [
              {
                id: 'TableExport',
                icon: 'icon_solid_export',
                permission: ['O_02_1689'],
                title: this.$t('导出')
              }
            ],
            ['Setting']
          ],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 不使用组件中的toolbar配置
          gridId: '2c5371c8-3409-4cfc-974c-aecdfcec62c3',
          grid: {
            virtualPageSize: 30,
            showSelected: false,
            selectionSettings: {
              persistSelection: true,
              type: 'Multiple',
              checkboxOnly: true
            },
            enableVirtualization: true,
            allowPaging: false,
            lineSelection: 0, // 选项列 显示勾选
            lineIndex: 1, // 序号列 显示序列号
            columnData: ColumnDataTab4,
            dataSource: []
            // frozenColumns: 2
          }
        }
      ],
      virtualSupplierCodeList: []
    }
  },
  mounted() {
    this.getvirtualSupplierCodeList()
    setTimeout(() => {
      this.$set(this.pageSettings, 'pageSize', 20)
    }, 500)
  },
  methods: {
    getvirtualSupplierCodeList() {
      this.$API.receiptAndDelivery
        .getPingcaiGroupList({ page: { current: 1, size: 10000 } })
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.virtualSupplierCodeList = Array.from(
              new Set(data.records.map((item) => item.groupSupplierCode))
            )
              .map((code) => {
                const item = data.records.find((a) => a.groupSupplierCode === code)
                return {
                  ...item,
                  codeAndName: `${item.groupSupplierCode} - ${item.groupSupplierName}`
                }
              })
              .filter((i) => i.groupSupplierCode && i.groupSupplierName)
          }
        })
    },
    // 创建日期选择
    demandDateChange(e, prefix) {
      if (e.startDate) {
        this.searchFormModel[prefix + 'Start'] = dayjs(e.startDate).valueOf()
        this.searchFormModel[prefix + 'End'] = dayjs(e.endDate).valueOf()
      } else {
        this.searchFormModel[prefix + 'Start'] = null
        this.searchFormModel[prefix + 'End'] = null
      }
    },
    // 查询条件重置
    handleCustomReset() {
      this.pageSettings.currentPage = 1
      for (let key in this.searchFormModel) {
        if (key === 'demandDate') {
          // this.searchFormModel[key] = null
          this.$set(this.searchFormModel, key, null)
          this.searchFormModel['demandDateStart'] = null
          this.searchFormModel['demandDateEnd'] = null
        } else {
          this.searchFormModel[key] = null
        }
      }
    },
    // 列表查询
    handleCustomSearch(type) {
      this.$store.commit('startLoading')
      const param = {
        pageNum: type === 'handle' ? 1 : this.pageSettings.currentPage,
        pageSize: this.pageSettings.pageSize,
        ...this.searchFormModel
      }
      if (param.supplierCode && param.supplierCode.length) {
        param.supplierCode = param.supplierCode.join(',')
      }
      const _Api = this.$API.receiptAndDelivery.getPlanPurScreenList
      _Api(param)
        .then((res) => {
          if (res && res.code === 200) {
            setTimeout(() => {
              document.querySelector('.e-content').scrollTop = 0
            }, 20)
            const _list = res.data?.records.map((item) => {
              if (item['remainCreateQty']) {
                item['supplierNum'] = Number(item['remainCreateQty'].replace(/,/g, ''))
                return item
              }
            })
            this.$set(this.componentConfig[0].grid, 'dataSource', _list)
            this.pageSettings.totals = Math.ceil(
              Number(res.data.total) / this.pageSettings.pageSize
            )
            this.pageSettings.currentPage = Number(res.data.current)
            this.pageSettings.totalRecordsCount = res.data.total
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    // 切换页码
    handleCurrentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.handleCustomSearch()
    },
    // 更换页大小
    handleSizeChange(pageSize) {
      this.pageSettings.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // ToolBar
    handleClickToolBar(args) {
      const { toolbar, gridRef } = args
      const selectRows = gridRef.getMtechGridRecords()
      const selectedId = []
      selectRows.forEach((item) => selectedId.push(item.id))
      if (toolbar.id === 'TableExport') {
        // let _selectRow = selectRows.map((item) => ({
        //   id: item.id,
        //   requiredDeliveryDate: item.requiredDeliveryDate
        // }))
        // 导出
        this.handleExport()
      }
    },
    // CellTool
    handleClickCellTool() {},
    // 导出
    handleExport() {
      let obj = JSON.parse(sessionStorage.getItem(this.componentConfig[0].gridId))?.visibleCols
      const headerMap = {}
      if (obj !== undefined && obj.length) {
        obj?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'serialNumber') {
            headerMap[i.field] = i.headerText
          }
        })
      } else {
        ColumnDataTab4?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'serialNumber') {
            headerMap[i.field] = i.headerText
          }
        })
      }
      const param = {
        ...this.searchFormModel,
        headerMap
      }
      this.apiStartLoading()
      this.$API.receiptAndDelivery.exportScreenPurData(param).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('todoDetail')
  }
}
</script>

<style lang="scss" scoped>
// 表格容器
#table-container {
  position: relative;

  // 表格数据视图
  /deep/ .e-gridcontent > .e-content {
    height: 0px;
    transition: height 0.5s ease;
  }

  // 分页
  /deep/ .mt-pagertemplate {
    margin: 10px 0 10px !important;
  }
}
// 显示不可创建送货单数据
.zero-filter-switch {
  text-align: right;
}
</style>
