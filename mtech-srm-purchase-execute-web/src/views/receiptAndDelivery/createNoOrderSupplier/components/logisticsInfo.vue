<template>
  <div class="box">
    <!-- 下面的内容 -->
    <div class="main-box">
      <mt-form
        ref="ruleForm"
        :model="logisticsData"
        :rules="rules"
        :validate-on-rule-change="false"
      >
        <!-- 发货方式 -->
        <mt-form-item prop="type" :label="$t('发货方式')">
          <mt-select
            v-model="logisticsData.type"
            :disabled="false"
            :show-clear-button="true"
            :data-source="ShippingTypeOptions"
            :placeholder="$t('发货方式')"
            @change="typeChange"
          ></mt-select>
        </mt-form-item>
        <!-- 快递-物流公司 -->
        <mt-form-item
          v-show="logisticsData.type == ShippingType.express"
          prop="logisticsCompanyCode"
          :label="$t('物流公司')"
        >
          <mt-select
            v-model="logisticsData.logisticsCompanyCode"
            :fields="{
              text: 'itemName',
              value: 'itemCode'
            }"
            :disabled="false"
            :show-clear-button="true"
            :allow-filtering="true"
            :data-source="logisticsCompanyOptions"
            :placeholder="$t('物流公司')"
            @change="logisticsCompanyCodeChange"
          ></mt-select>
        </mt-form-item>
        <!-- 快递-物流单号 -->
        <mt-form-item
          v-show="logisticsData.type == ShippingType.express"
          prop="logisticsNo"
          :label="$t('物流单号')"
        >
          <mt-input
            v-model="logisticsData.logisticsNo"
            :disabled="false"
            :show-clear-button="true"
            :placeholder="$t('物流单号')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-司机姓名 -->
        <mt-form-item
          v-show="logisticsData.type == ShippingType.logistics"
          prop="driverName"
          :label="$t('司机姓名')"
        >
          <mt-input
            v-model="logisticsData.driverName"
            :disabled="false"
            :show-clear-button="true"
            :placeholder="$t('司机姓名')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-司机身份证号 -->
        <mt-form-item
          v-show="logisticsData.type == ShippingType.logistics"
          prop="driverNo"
          :label="$t('司机身份证号')"
        >
          <mt-input
            v-model="logisticsData.driverNo"
            :disabled="false"
            :show-clear-button="true"
            :placeholder="$t('司机身份证号')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-司机联系方式 -->
        <mt-form-item
          v-show="logisticsData.type == ShippingType.logistics"
          prop="driverPhone"
          :label="$t('司机联系方式')"
        >
          <mt-input
            v-model="logisticsData.driverPhone"
            :disabled="false"
            :show-clear-button="true"
            :placeholder="$t('司机联系方式')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-车牌 -->
        <mt-form-item
          v-show="logisticsData.type == ShippingType.logistics"
          prop="carNo"
          :label="$t('车牌')"
        >
          <mt-input
            v-model="logisticsData.carNo"
            :disabled="false"
            :show-clear-button="true"
            :placeholder="$t('车牌')"
          ></mt-input>
        </mt-form-item>
        <!-- 件数 -->
        <mt-form-item prop="number" :label="$t('件数')">
          <mt-input-number
            v-model="logisticsData.number"
            :min="0"
            :show-spin-button="false"
            :disabled="false"
            :show-clear-button="true"
            :placeholder="$t('件数')"
          ></mt-input-number>
        </mt-form-item>

        <!-- 备注 -->
        <mt-form-item class="full-width" prop="remark" :label="$t('备注')" :show-message="false">
          <mt-input
            v-model="logisticsData.remark"
            :disabled="false"
            maxlength="200"
            :show-clear-button="true"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { ShippingTypeOptions, ShippingType } from '../config/constant'
import { RegExpMap } from '@/utils/constant'

export default {
  props: {
    logisticsData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const { idCardReg, phoneNumReg, integerReg, numberPlateReg } = RegExpMap
    // 快递-物流公司
    const logisticsCompanyCodeValidator = (rule, value, callback) => {
      if (this.logisticsData.type == ShippingType.express) {
        if (!value) {
          callback(new Error(this.$t('请选择物流公司')))
        } else {
          this.$refs.ruleForm.clearValidate(['logisticsCompanyCode'])
          callback()
        }
      } else {
        this.$refs.ruleForm.clearValidate(['logisticsCompanyCode'])
        callback()
      }
    }
    // 快递-物流单号
    const logisticsNoValidator = (rule, value, callback) => {
      if (this.logisticsData.type == ShippingType.express) {
        if (!value) {
          callback(new Error(this.$t('请输入物流单号')))
        } else {
          this.$refs.ruleForm.clearValidate(['logisticsNo'])
          callback()
        }
      } else {
        this.$refs.ruleForm.clearValidate(['logisticsNo'])
        callback()
      }
    }
    // 物流-司机姓名
    const driverNameValidator = (rule, value, callback) => {
      if (this.logisticsData.type == ShippingType.logistics) {
        if (!value) {
          callback(new Error(this.$t('请输入司机姓名')))
        } else {
          this.$refs.ruleForm.clearValidate(['driverName'])
          callback()
        }
      } else {
        this.$refs.ruleForm.clearValidate(['driverName'])
        callback()
      }
    }
    // 物流-司机身份证号
    const driverNoValidator = (rule, value, callback) => {
      if (this.logisticsData.type == ShippingType.logistics) {
        if (!value) {
          callback(new Error(this.$t('请输入司机身份证号')))
        } else if (!idCardReg.test(value)) {
          callback(new Error(this.$t('身份证号格式错误')))
        } else {
          this.$refs.ruleForm.clearValidate(['idCard'])
          callback()
        }
      } else {
        this.$refs.ruleForm.clearValidate(['idCard'])
        callback()
      }
    }
    // 物流-司机联系方式
    const driverPhoneValidator = (rule, value, callback) => {
      if (this.logisticsData.type == ShippingType.logistics) {
        if (!value) {
          callback(new Error(this.$t('请输入司机手机号')))
        } else if (!phoneNumReg.test(value)) {
          callback(new Error(this.$t('请输入正确的手机号')))
        } else {
          this.$refs.ruleForm.clearValidate(['contact'])
          callback()
        }
      } else {
        this.$refs.ruleForm.clearValidate(['contact'])
        callback()
      }
    }
    // 物流-车牌
    const carNoValidator = (rule, value, callback) => {
      if (this.logisticsData.type == ShippingType.logistics) {
        if (!value) {
          callback(new Error(this.$t('请输入车牌')))
        } else if (!numberPlateReg.test(value)) {
          callback(new Error(this.$t('请输入正确的车牌号')))
        } else {
          this.$refs.ruleForm.clearValidate(['carNo'])
          callback()
        }
      } else {
        this.$refs.ruleForm.clearValidate(['carNo'])
        callback()
      }
    }
    // 件数
    const numberValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入件数')))
      } else if (!integerReg.test(value)) {
        callback(new Error(this.$t('请输入整数')))
      } else {
        this.$refs.ruleForm.clearValidate(['hour'])
        callback()
      }
    }
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      ShippingTypeOptions,
      ShippingType,
      isExpand: true,
      logisticsCompanyOptions: [], // 物流公司下拉数据源
      rules: {
        // 发货方式
        type: [
          {
            required: true,
            message: this.$t('请选择发货方式'),
            trigger: 'blur'
          }
        ],
        // 物流公司
        logisticsCompanyCode: [
          {
            required: true,
            validator: logisticsCompanyCodeValidator,
            trigger: 'blur'
          }
        ],
        // 物流单号
        logisticsNo: [
          {
            required: true,
            validator: logisticsNoValidator,
            trigger: 'blur'
          }
        ],
        // 司机姓名
        driverName: [
          {
            required: true,
            validator: driverNameValidator,
            trigger: 'blur'
          }
        ],
        // 司机身份证号
        driverNo: [
          {
            required: true,
            validator: driverNoValidator,
            trigger: 'blur'
          }
        ],
        // 司机手机号
        driverPhone: [
          {
            required: true,
            validator: driverPhoneValidator,
            trigger: 'blur'
          }
        ],
        // 车牌号
        carNo: [
          {
            required: true,
            validator: carNoValidator,
            trigger: 'blur'
          }
        ],
        // 件数
        number: [
          {
            required: true,
            validator: numberValidator,
            trigger: 'blur'
          }
        ]
      }
    }
  },
  mounted() {
    // 物流公司 主数据获取数据源
    this.getLogisticsCompany()
  },
  filters: {},
  methods: {
    // 物流公司 change
    logisticsCompanyCodeChange(e) {
      // 物流公司
      this.logisticsData.logisticsCompanyCode = null // 物流公司code
      this.logisticsData.logisticsCompanyName = null // 物流公司
      if (e.value !== undefined && e.value !== null) {
        for (let i = 0; i < this.logisticsCompanyOptions.length; i++) {
          const item = this.logisticsCompanyOptions[i]
          if (item.itemCode === e.value) {
            // 物流公司
            this.logisticsData.logisticsCompanyCode = item.itemCode // 物流公司code
            this.logisticsData.logisticsCompanyName = item.itemName // 物流公司
            break
          }
        }
      }
    },
    // 主数据-获取物流公司数据源
    getLogisticsCompany() {
      this.apiStartLoading()
      this.$API.masterData
        .getCommonDictItemTree({ dictCode: 'logisticsCompany' })
        .then((res) => {
          if (res?.data) {
            this.logisticsCompanyOptions = res.data || []
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 发货方式 change
    typeChange(e) {
      if (e.value == ShippingType.logistics) {
        // 快递配送
        this.logisticsData.logisticsCompanyCode = null // 物流公司code
        this.logisticsData.logisticsCompanyName = null // 物流公司
        this.logisticsData.logisticsNo = null // 物流单号

        this.$refs.ruleForm.clearValidate(['logisticsNo', 'logisticsCompanyCode'])
      } else if (e.value == ShippingType.express) {
        // 物流配送
        this.logisticsData.driverName = null // 司机姓名
        this.logisticsData.driverNo = null // 司机身份证号
        this.logisticsData.driverPhone = null // 司机联系方式
        this.logisticsData.carNo = null // 车牌

        this.$refs.ruleForm.clearValidate(['driverName', 'driverNo', 'driverPhone', 'carNo'])
      }
    },
    doValidate() {
      // 验证数据
      let isValid = false
      this.$refs.ruleForm.validate((valid) => {
        console.log('+++++++', valid)
        isValid = valid
      })

      return isValid
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  padding: 20px 0 20px 20px;

  .main-box .mt-form-item {
    width: calc(20% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
  }
}
</style>
