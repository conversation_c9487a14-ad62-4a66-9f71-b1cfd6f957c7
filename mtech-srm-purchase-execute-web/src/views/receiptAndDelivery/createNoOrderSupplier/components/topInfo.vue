<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" @click="doSubmit">{{
        $t('提交')
      }}</mt-button>

      <div class="sort-box" @click="doExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <!-- 公司 -->
        <mt-form-item prop="companyCode" :label="$t('公司')">
          <mt-select
            v-model="headerInfo.companyCode"
            :allow-filtering="true"
            :fields="{ text: 'customerName', value: 'customerCode' }"
            :disabled="false"
            :show-clear-button="true"
            :data-source="companyOptions"
            :placeholder="$t('公司')"
            @change="companyCodeChange"
          ></mt-select>
        </mt-form-item>

        <!-- 工厂 根据公司 获取数据源 -->
        <mt-form-item prop="siteCode" :label="$t('工厂')">
          <mt-select
            v-model="headerInfo.siteCode"
            :allow-filtering="true"
            :fields="{ text: 'siteOrgName', value: 'siteOrgCode' }"
            :disabled="false"
            :show-clear-button="true"
            :data-source="siteOptions"
            :placeholder="$t('工厂')"
            @change="siteCodeChange"
          ></mt-select>
        </mt-form-item>

        <!-- 交货库存地点 根据工厂 获取数据源 -->
        <mt-form-item prop="warehouseCode" :label="$t('交货库存地点')">
          <mt-select
            v-model="headerInfo.warehouseCode"
            :allow-filtering="true"
            :fields="{ text: 'siteAddressName', value: 'siteAddress' }"
            :disabled="false"
            :show-clear-button="true"
            :data-source="warehouseOptions"
            :placeholder="$t('交货库存地点')"
            @change="warehouseCodeChange"
          ></mt-select>
        </mt-form-item>
        <!-- 送货单类型 固定 无关联采购订单送货 -->
        <mt-form-item prop="deliveryType" :label="$t('送货单类型')">
          <mt-select
            v-model="headerInfo.deliveryType"
            :disabled="true"
            :data-source="DeliveryTypeOptions"
            :placeholder="$t('送货单类型')"
          ></mt-select>
        </mt-form-item>

        <!-- 收货方编号 使用选择工厂所带出的公司编号 -->
        <!-- <mt-form-item prop="receiverCode" :label="$t('收货方编号')">
          <mt-input
            v-model="headerInfo.receiverCode"
            :disabled="true"
            :placeholder="$t('收货方编号')"
          ></mt-input>
        </mt-form-item> -->

        <!-- 收货方编号 使用选择工厂所带出的公司名称 -->
        <!-- <mt-form-item prop="receiverName" :label="$t('收货方名称')">
          <mt-input
            v-model="headerInfo.receiverName"
            :disabled="true"
            :placeholder="$t('收货方名称')"
          ></mt-input>
        </mt-form-item> -->

        <!-- 送货方式 TODO 固定 无需求送货 -->
        <!-- <mt-form-item prop="TODO" :label="$t('送货方式')">
          <mt-select
            v-model="headerInfo.TODO"
            :disabled="true"
            :data-source="DeliveryTypeOptions"
            :placeholder="$t('送货方式')"
          ></mt-select>
        </mt-form-item> -->

        <!-- 发货地点 手工输入 -->
        <mt-form-item prop="sendAddressName" :label="$t('发货地点')">
          <mt-input
            v-model="headerInfo.sendAddressName"
            maxlength="200"
            :disabled="false"
            :show-clear-button="true"
            :placeholder="$t('发货地点')"
          ></mt-input>
        </mt-form-item>

        <!-- 送货地址 通过选择的交货库存地点 获取数据源 -->
        <mt-form-item prop="receiveAddressName" :label="$t('送货地址')">
          <mt-select
            v-model="headerInfo.receiveAddressName"
            :disabled="false"
            :allow-filtering="true"
            :show-clear-button="true"
            :fields="{
              text: 'consigneeAddress',
              value: 'consigneeAddress'
            }"
            :data-source="receiveAddressOptions"
            :placeholder="$t('送货地址')"
          ></mt-select>
        </mt-form-item>

        <!-- 发货日期 选择日期 -->
        <mt-form-item prop="sendTime" :label="$t('发货日期')">
          <mt-date-time-picker
            v-model="headerInfo.sendTime"
            :min="new Date()"
            :disabled="false"
            :show-clear-button="true"
            :allow-edit="false"
            placeholder=""
          ></mt-date-time-picker>
        </mt-form-item>

        <!-- 预计到货日期 选择日期 -->
        <mt-form-item prop="forecastArriveTime" :label="$t('预计到货日期')">
          <mt-date-time-picker
            v-model="headerInfo.forecastArriveTime"
            :min="new Date()"
            :disabled="false"
            :show-clear-button="true"
            :allow-edit="false"
            placeholder=""
          ></mt-date-time-picker>
        </mt-form-item>

        <!-- 供方备注 手工输入 -->
        <mt-form-item class="full-width" prop="remark" :label="$t('备注')" :show-message="false">
          <mt-input
            v-model="headerInfo.remark"
            maxlength="200"
            :disabled="false"
            :show-clear-button="true"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { DeliveryTypeOptions, BusinessGroupTypeDictCode } from '../config/constant'
import { materialInfoDataSource, businessGroupCode } from '../config/variable'
import { maxPageSize } from '@/utils/constant'

export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))

    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      isCancelSetSiteCode: false, // 正在取消设置工厂code
      isCancelSetCompanyCode: false, // 正在取消设置公司code
      userInfo,
      companyOptions: [], // 公司下拉数据源
      siteOptions: [], // 工厂下拉数据源
      warehouseOptions: [], // 交货库存地点下拉数据源
      receiveAddressOptions: [], // 送货地址下拉数据源
      DeliveryTypeOptions,
      isExpand: true,
      siteTenantExtendRules: [], // 获取采方配置的交货排期送货地址配置，规则
      rules: {
        // 工厂
        siteCode: [
          {
            required: true,
            message: this.$t('请选择工厂'),
            trigger: 'blur'
          }
        ],
        // 公司
        companyCode: [
          {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          }
        ],
        // 交货库存地点
        warehouseCode: [
          {
            required: true,
            message: this.$t('请选择交货库存地点'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  mounted() {
    // 租户级-客户接口-模糊查询 获取采方公司列表
    this.getCustomer()
  },
  filters: {},
  methods: {
    // 公司 change
    companyCodeChange(e) {
      const { previousItemData, itemData, value } = e
      if (!this.isCancelSetCompanyCode) {
        if (this.$parent.isEditing) {
          // 结束行编辑
          this.$parent.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
          setTimeout(() => {
            // 值回退
            this.isCancelSetCompanyCode = true // 正在进行公司取消的值回退
            this.handleCompanyCodeChange({ itemData: previousItemData })
          }, 500)
        } else {
          if (previousItemData && materialInfoDataSource.length > 0) {
            // 公司数据被修改 && 物料信息表格有数据
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('切换公司将清空物料信息数据，请确定是否继续？')
              },
              success: () => {
                this.isCancelSetCompanyCode = false
                this.$emit('companyCodeChange', {
                  itemData,
                  value,
                  supplierTenantId: this.userInfo.tenantId
                })
                this.handleCompanyCodeChange({ itemData })
              },
              close: () => {
                // 值回退
                this.isCancelSetCompanyCode = true // 正在进行公司取消的值回退
                this.handleCompanyCodeChange({ itemData: previousItemData })
              }
            })
          } else {
            this.$emit('companyCodeChange')
            this.handleCompanyCodeChange({ itemData })
          }
        }
      }
      this.isCancelSetCompanyCode = false
    },
    // 公司 change 执行
    handleCompanyCodeChange(e) {
      const { itemData } = e
      // 公司
      this.headerInfo.companyId = itemData?.id
      this.headerInfo.companyCode = itemData?.customerCode
      this.headerInfo.companyName = itemData?.customerName
      businessGroupCode.customerCode = itemData?.customerCode
      // 采方租户id
      // this.headerInfo.purTenantId = itemData?.tenantId;
      this.headerInfo.customerEnterpriseId = itemData?.customerEnterpriseId
      if (this.headerInfo.customerEnterpriseId && !this.isCancelSetCompanyCode) {
        // 重置 工厂
        this.siteOptions = []
        this.headerInfo.siteCode = null
        this.headerInfo.siteName = null
        // 根据公司 获取 工厂 数据源 siteOptions
        // 主数据-供方视角-获取可用货源关系工厂
        this.getSourceAvailableView()
      }
      if (businessGroupCode.customerCode) {
        // 通过 客户编码、业务组字典编码 获取业务组列表，通过 业务组列表获取 采购组业务组类型编码
        this.getAuthFindDictItemByCustomerCodeAndDictCode({
          customerCode: businessGroupCode.customerCode
        })
      }
    },
    // 工厂 change
    siteCodeChange(e) {
      const { previousItemData, itemData, value } = e
      if (!this.isCancelSetSiteCode) {
        if (this.$parent.isEditing) {
          // 结束行编辑
          this.$parent.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
          setTimeout(() => {
            // 值回退
            this.isCancelSetSiteCode = true // 正在进行工厂取消的值回退
            this.handleSiteCodeChange({ itemData: previousItemData })
          }, 500)
        } else {
          if (previousItemData && materialInfoDataSource.length > 0) {
            // 工厂数据被修改 && 物料信息表格有数据
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('切换工厂将清空物料信息数据，请确定是否继续？')
              },
              success: () => {
                this.isCancelSetSiteCode = false
                this.$emit('siteCodeChange', {
                  itemData,
                  value,
                  supplierTenantId: this.userInfo.tenantId
                })
                this.handleSiteCodeChange({ itemData })
              },
              close: () => {
                // 值回退
                this.isCancelSetSiteCode = true // 正在进行工厂取消的值回退
                this.handleSiteCodeChange({ itemData: previousItemData })
              }
            })
          } else {
            this.$emit('siteCodeChange', {
              itemData,
              value,
              supplierTenantId: this.userInfo.tenantId
            })
            this.handleSiteCodeChange({ itemData })
          }
        }
      }

      this.isCancelSetSiteCode = false
    },
    // 工厂 change 执行
    handleSiteCodeChange(e) {
      const { itemData } = e
      // 工厂
      this.headerInfo.siteCode = itemData?.siteOrgCode
      this.headerInfo.siteName = itemData?.siteOrgName

      if (
        this.headerInfo.customerEnterpriseId &&
        this.headerInfo.siteCode &&
        !this.isCancelSetSiteCode
      ) {
        // 重置 交货库存地点
        this.warehouseOptions = []
        this.headerInfo.warehouseName = null
        this.headerInfo.warehouseCode = null
        // 设置请求规则
        this.siteTenantExtendRules = [
          {
            // 工厂
            field: 'siteCode',
            operator: 'equal',
            value: this.headerInfo.siteCode
          }
        ]
        // 根据工厂 获取 交货库存地点 数据源 warehouseOptions
        this.postSiteTenantExtendQueryByEnterpriseId().then((res) => {
          if (res?.code == 200) {
            this.warehouseOptions = res?.data?.records || []
          }
        })
      }
    },
    // 交货库存地点 change
    warehouseCodeChange(e) {
      const { itemData } = e
      // 交货库存地点
      this.headerInfo.warehouseCode = itemData?.siteAddress
      this.headerInfo.warehouseName = itemData?.siteAddressName
      // 重置 送货地址
      this.receiveAddressOptions = []
      this.headerInfo.receiveAddressName = null

      if (
        this.headerInfo.customerEnterpriseId &&
        this.headerInfo.siteCode &&
        this.headerInfo.warehouseCode
      ) {
        // 设置请求规则
        this.siteTenantExtendRules = [
          {
            // 工厂
            field: 'siteCode',
            operator: 'equal',
            value: this.headerInfo.siteCode
          },
          {
            // 交货库存地点
            field: 'siteAddress',
            operator: 'equal',
            value: this.headerInfo.warehouseCode
          }
        ]
        // 根据交货库存地点 获取 送货地址 数据源 receiveAddressOptions
        this.postSiteTenantExtendQueryByEnterpriseId().then((res) => {
          if (res?.code == 200) {
            this.receiveAddressOptions = res?.data?.records || []
          }
        })
      }
    },
    // 获取采方配置的交货排期送货地址配置
    postSiteTenantExtendQueryByEnterpriseId() {
      const data = {
        enterpriseId: this.headerInfo.customerEnterpriseId,
        params: {
          page: {
            size: maxPageSize,
            current: 1
          },
          condition: 'and',
          defaultRules: [...this.siteTenantExtendRules]
        }
      }
      return this.$API.receiptAndDelivery
        .postSiteTenantExtendQueryByEnterpriseId(data)
        .then((res) => {
          return res
        })
        .catch(() => {})
    },
    // 主数据-供方视角-获取可用货源关系工厂
    getSourceAvailableView() {
      const params = {
        buyerEnterpriseId: this.headerInfo.customerEnterpriseId // 采方企业id
      }
      this.$API.masterData
        .getSourceAvailableView(params)
        .then((res) => {
          this.siteOptions = res?.data || []
        })
        .catch(() => {})
    },
    // 租户级-客户接口-模糊查询 获取采方公司列表
    getCustomer() {
      this.$API.masterData
        .getCustomer({ fuzzyNameOrCode: '' })
        .then((res) => {
          this.companyOptions = res?.data || []
        })
        .catch(() => {})
    },
    // 跨租户级-业务组物料接口-根据物料编码和业务组类型获取客户业务组
    getAuthFindDictItemByCustomerCodeAndDictCode(args) {
      const { customerCode } = args
      this.$API.masterData
        .getAuthFindDictItemByCustomerCodeAndDictCode({
          customerCode,
          dictCode: BusinessGroupTypeDictCode
        })
        .then((res) => {
          const businessGroupTypeList = res?.data || []
          for (let i = 0; i < businessGroupTypeList.length; i++) {
            if (businessGroupTypeList[i].itemName === '采购组') {
              businessGroupCode.purchase = businessGroupTypeList[i].itemCode
            }
          }
        })
    },
    doValidate() {
      // 验证数据
      let isValid = false
      this.$refs.ruleForm.validate((valid) => {
        isValid = valid
      })

      return isValid
    },
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 提交
    doSubmit() {
      this.$emit('doSubmit')
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
