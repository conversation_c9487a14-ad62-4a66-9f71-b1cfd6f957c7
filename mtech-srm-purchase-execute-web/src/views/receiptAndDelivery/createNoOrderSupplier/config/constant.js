import { i18n } from '@/main.js'

// 表格编辑 的 RequestType
export const RequestType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  sorting: 'sorting',
  refresh: 'refresh'
}

// 表格编辑 的 action
export const ActionType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  edit: 'edit'
}

// 业务组类型数字字典编码
export const BusinessGroupTypeDictCode = 'BUSINESSGROUP'

export const TabCode = {
  materialInfo: 'materialInfo',
  logisticsInfo: 'logisticsInfo'
}

export const TabList = [
  {
    title: i18n.t('物料信息'),
    code: TabCode.materialInfo
  },
  {
    title: i18n.t('物流信息'),
    code: TabCode.logisticsInfo
  }
]

// 表格 EditSettings
export const EditSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
} // 编辑设置

// tab
export const Tab = {
  list: 1, // 物料信息
  details: 2 // 物流信息
}

// 送货类型:1-关联采购订单,2-无采购订单
export const DeliveryType = {
  linked: 1, // 关联采购订单
  notLink: 2 // 无采购订单
}
// 送货类型 text
export const DeliveryTypeText = {
  [DeliveryType.linked]: i18n.t('关联采购订单'),
  [DeliveryType.notLink]: i18n.t('无采购订单')
}
// 送货类型 对应的 Options
export const DeliveryTypeOptions = [
  {
    // 关联采购订单
    value: DeliveryType.linked,
    text: DeliveryTypeText[DeliveryType.linked],
    cssClass: ''
  },
  {
    // 无采购订单
    value: DeliveryType.notLink,
    text: DeliveryTypeText[DeliveryType.notLink],
    cssClass: ''
  }
]

// 发货方式:1-快递配送，2-物流配送
export const ShippingType = {
  express: 1, // 快递配送
  logistics: 2 // 物流配送
}
// 发货方式 text
export const ShippingTypeText = {
  [ShippingType.express]: i18n.t('快递配送'),
  [ShippingType.logistics]: i18n.t('物流配送')
}
// 发货方式 对应的 Options
export const ShippingTypeOptions = [
  {
    // 快递配送
    value: ShippingType.express,
    text: ShippingTypeText[ShippingType.express],
    cssClass: ''
  },
  {
    // 物流配送
    value: ShippingType.logistics,
    text: ShippingTypeText[ShippingType.logistics],
    cssClass: ''
  }
]

// 物料信息 Toolbar
export const MaterialInfoToolbar = [
  [
    {
      id: 'MaterialInfoAdd',
      icon: 'icon_table_new',
      title: i18n.t('新增')
    },
    {
      id: 'MaterialInfoDel',
      icon: 'icon_table_remove',
      title: i18n.t('移除')
    },
    {
      id: 'MaterialInfoUpdate',
      icon: 'icon_table_save',
      title: i18n.t('更新')
    }
  ]
]

// 行编辑触发的 change 事件类型
export const ComponentChangeType = {
  code: 'code', // 例：下拉框选择“供应商”，供应商编码（code)要被修改
  link: 'link' // 例：计划组：根据物料+工厂+计划组的业务组类型 code 带出
}

// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  itemName: null, // 物料名称
  itemCode: null, // 物料编号
  buyerOrgName: null, // 采购组
  buyerOrgCode: null, // 采购组编码
  purUnitName: null, // 单位
  purUnitCode: null, // 采购单位编码
  projectTextBatch: null, // 项目文本批次
  // workCenterName: null, // 工作中心
  // workCenterCode: null, // 工作中心Code
  // TODO: null, // 工序名称
  // sendAddress: null, // 收货地址 根据头部信息显示，不可编辑
  // limitQuantity: null, // 限量数量
  // warehouseClerkName: null, // 仓管员
  // warehouseClerkCode: null, // 仓管员Code
  // dispatcherName: null, // 调度员
  // dispatcherCode: null, // 调度员Code
  demandDate: null, // 需求日期
  demandTime: null, // 需求时间
  thisDeliveryQuantity: null, // 本次送货数量
  remark: null // 备注
}

// 表格列数据
export const ColumnData = [
  {
    fieldCode: 'checkBox' // 不可编辑
  },
  {
    fieldCode: 'thePrimaryKey'
  },
  {
    fieldCode: 'serialNumber', // 前端定义,不可编辑
    fieldName: i18n.t('序号')
  },
  // 物料名称 下拉选择
  {
    fieldCode: 'itemName',
    fieldName: i18n.t('物料名称')
  },
  // 物料编号 物料带出
  {
    fieldCode: 'itemCode',
    fieldName: i18n.t('物料编号')
  },
  // 采购组 物料后调用主数据接口带出
  {
    fieldCode: 'buyerOrgName',
    fieldName: i18n.t('采购组')
    // buyerOrgCode	采购组编码
  },
  {
    fieldCode: 'buyerOrgCode',
    fieldName: i18n.t('采购组编码')
    // buyerOrgCode	采购组编码
  },
  // 单位 物料带出
  {
    fieldCode: 'purUnitName',
    fieldName: i18n.t('单位')
    // purUnitCode 采购单位编码
  },
  // 项目文本批次 手工输入
  {
    fieldCode: 'projectTextBatch',
    fieldName: i18n.t('项目文本批次')
  },
  // // 工作中心 主数据下拉选择
  // {
  //   fieldCode: "workCenterName",
  //   fieldName: i18n.t("工作中心"),
  //   // workCenterCode 工作中心Code
  // },
  // // 工序名称 物料带出
  // {
  //   fieldCode: "TODO",
  //   fieldName: i18n.t("工序名称"),
  // },
  // // 收货地址 根据头部信息显示，不可编辑
  // {
  //   fieldCode: "sendAddress",
  //   fieldName: i18n.t("收货地址"),
  // },
  // // 限量数量 物料带出
  // {
  //   fieldCode: "limitQuantity",
  //   fieldName: i18n.t("限量数量"),
  // },
  // // 仓管员 物料带出
  // {
  //   fieldCode: "warehouseClerkName",
  //   fieldName: i18n.t("仓管员"),
  //   // warehouseClerkCode 仓管员Code
  // },
  // // 调度员 物料带出
  // {
  //   fieldCode: "dispatcherName",
  //   fieldName: i18n.t("调度员"),
  //   // dispatcherCode 调度员Code
  // },
  // 需求日期 选择日期
  {
    fieldCode: 'demandDate',
    fieldName: i18n.t('需求日期')
  },
  // 需求时间 选择时间
  {
    fieldCode: 'demandTime',
    fieldName: i18n.t('需求时间')
  },
  // 本次送货数量 手工输入
  {
    fieldCode: 'thisDeliveryQuantity',
    fieldName: i18n.t('本次送货数量')
  },
  // 备注 手工输入
  {
    fieldCode: 'remark',
    fieldName: i18n.t('备注')
  }
]
