import Vue from 'vue'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { ComponentChangeType } from './constant'
import { rowDataTemp, businessGroupCode } from './variable'
import { numberInputOnKeyDown } from '@/utils/utils'

// 时间日期显示
export const ColumnComponent = {
  // 文本 下拉选择时，带出的数据
  changedText: (args) => {
    const { dataKey } = args
    const template = () => {
      return {
        template: Vue.component('changedInputComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content"><span>{{data[dataKey]}}</span></div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              ComponentChangeType
            }
          },
          beforeDestroy() {
            this.$bus.$off('createNoOrderSupplierComponentChange')
          },
          mounted() {
            // 监听变化
            this.onComponentChange()
          },
          methods: {
            // 监听变化
            onComponentChange() {
              // 监听被变化
              this.$bus.$on(`createNoOrderSupplierComponentChange`, (e) => {
                const { modifiedKeys, changeType, data } = e
                if (
                  modifiedKeys.includes(this.dataKey) &&
                  changeType === ComponentChangeType.code
                ) {
                  // 发布事件的数据修改了，关联值修改
                  this.data[dataKey] = data[this.dataKey]
                }
              })
            }
          }
        })
      }
    }
    return template
  },
  // 文本 编辑
  input: (args) => {
    const { dataKey, showClearBtn, maxlength } = args
    const template = () => {
      return {
        template: Vue.component('inputComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
                <mt-input
                  v-model="data[dataKey]"
                  :show-clear-button="showClearBtn"
                  :disabled="false"
                  :maxlength="maxlength"
                  @input="onInput"
                ></mt-input>
              </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              showClearBtn,
              maxlength
            }
          },
          mounted() {},
          methods: {
            onInput(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e
            }
          }
        })
      }
    }
    return template
  },
  // 数字 编辑
  number: (args) => {
    const { dataKey, showClearBtn, precision, maxValue, minValue } = args
    const template = () => {
      return {
        template: Vue.component('numberComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-input-number
                v-model="data[dataKey]"
                :max="maxValue"
                :min="minValue"
                :precision="precision"
                :show-spin-button="false"
                :show-clear-button="showClearBtn"
                @input="onChange"
                @keydown.native="numberInputOnKeyDown"
              ></mt-input-number>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              maxValue,
              minValue,
              precision,
              showClearBtn,
              numberInputOnKeyDown
            }
          },
          mounted() {},
          methods: {
            onChange(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e
            }
          }
        })
      }
    }
    return template
  },
  // 下拉框 编辑
  select: (args) => {
    const { dataKey, selectOptions, fields, allowFiltering, showClearBtn, modifiedKeys } = args
    const template = () => {
      return {
        template: Vue.component('selectComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-select
                @change="selectChange"
                v-model="data[dataKey]"
                :allow-filtering="allowFiltering"
                :data-source="selectOptions"
                :show-clear-button="showClearBtn"
                :placeholder="$t('请选择')"
                :fields="fields"
              ></mt-select>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              selectOptions,
              fields,
              dataKey,
              allowFiltering,
              showClearBtn,
              ComponentChangeType,
              modifiedKeys: modifiedKeys || []
            }
          },
          mounted() {},
          methods: {
            // 修改中的值
            selectChange(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e.value
              // 触发改变的值
              this.triggerCodeChange(e.itemData)
              if (dataKey === 'itemName') {
                // 通过 客户编码、采购组业务组类型编码、物料编码，获取采购组名称、编码
                this.triggerPurchase(e.itemData)
              }
            },
            // 触发改变的值
            triggerCodeChange(selectData) {
              const args = {
                requestKey: dataKey, // 触发请求的key
                modifiedKeys: modifiedKeys, // 要被修改的key列表
                changeType: ComponentChangeType.code, // 修改类型
                data: selectData // 发出的值
              }
              // 修改rowDataTemp中要改变的值
              modifiedKeys.forEach((modifiedKey) => {
                rowDataTemp[rowDataTemp.length - 1][modifiedKey] = selectData[modifiedKey]
              })
              this.$bus.$emit('createNoOrderSupplierComponentChange', args)
            },
            // 触发改变采购组
            triggerPurchase(selectData) {
              if (selectData.itemCode && businessGroupCode.purchase) {
                // 选择了 工厂、物料，而且获得了采购组的业务组类型code
                const params = {
                  customerCode: businessGroupCode.customerCode, // 客户编码
                  businessGroupTypeCode: businessGroupCode.purchase, // 采购组业务组类型编码
                  itemCode: selectData.itemCode // 物料编码
                }
                this.getAuthCustomerBusinessGroup({
                  params,
                  businessKey: 'purchase'
                })
              }
            },
            // 查询采购组，并设置值
            getAuthCustomerBusinessGroup(args) {
              const { params, businessKey } = args
              this.$API.masterData.getAuthCustomerBusinessGroup(params).then((res) => {
                const list = res?.data || []
                if (businessKey === 'purchase' && list.length > 0) {
                  // 采购组
                  const groupName = list[0]?.groupName // 业务组名称，产品说获取第一个
                  const groupCode = list[0]?.groupCode // 业务组Code，产品说获取第一个
                  const purchaseObj = {
                    buyerOrgName: groupName,
                    buyerOrgCode: groupCode
                  }
                  const purchaseModifiedKeys = [
                    'buyerOrgName', // 采购组
                    'buyerOrgCode' // 采购组编码
                  ]
                  const args = {
                    requestKey: dataKey, // 触发请求的key
                    modifiedKeys: purchaseModifiedKeys, // 要被修改的key列表
                    changeType: ComponentChangeType.code, // 修改类型
                    data: purchaseObj // 发出的值
                  }
                  // 修改rowDataTemp中要改变的值
                  purchaseModifiedKeys.forEach((modifiedKey) => {
                    rowDataTemp[rowDataTemp.length - 1][modifiedKey] = purchaseObj[modifiedKey]
                  })
                  this.$bus.$emit('createNoOrderSupplierComponentChange', args)
                }
              })
            }
          }
        })
      }
    }
    return template
  },
  // 空的显示
  empty: () => {
    return {
      template: Vue.component('emptyComponent', {
        template: `<div></div>`,
        data: function () {
          return {
            data: {}
          }
        }
      })
    }
  },
  // 不可编辑的文字显示
  text: (args) => {
    const { dataKey } = args
    const template = () => {
      return {
        template: Vue.component('textComponent', {
          template: `<div class="grid-edit-column mt-flex-direction-column"><div class="field-content"><span>{{data[dataKey]}}</span></div></div>`,
          data: function () {
            return { data: {}, dataKey }
          }
        })
      }
    }
    return template
  },
  // 时间显示
  timeDate: (args) => {
    const { dataKey, isDateTime, isDate, isTime } = args

    const template = () => {
      return {
        template: Vue.component('timeDateComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <div v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</div>
              <div v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</div>
            </div>
          </div>`,
          data: function () {
            return { data: {}, dataKey, isDateTime, isDate, isTime }
          },
          filters: {
            dateFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
              } else {
                str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
              }

              return str
            },
            timeFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({ formatString: 'HH:MM:SS', value })
              } else {
                str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
              }

              return str
            }
          }
        })
      }
    }

    return template
  },
  // 时间输入
  timeInput: (args) => {
    const { dataKey, disabled, showClearBtn, allowEdit, isDate, isTime, isDateTime } = args

    const template = () => {
      return {
        template: Vue.component('timeInputComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <mt-time-picker
              v-if="isTime"
              v-model="data[dataKey]"
              :disabled="disabled"
              :show-clear-button="showClearBtn"
              :allow-edit="allowEdit"
              @change="onChange"
              placeholder=""
            ></mt-time-picker>
            <mt-date-picker
              v-if="isDate"
              v-model="data[dataKey]"
              :disabled="disabled"
              :show-clear-button="showClearBtn"
              :allow-edit="allowEdit"
              @change="onChange"
              placeholder=""
            ></mt-date-picker>
            <mt-date-time-picker
              v-if="isDateTime"
              v-model="data[dataKey]"
              :disabled="disabled"
              :show-clear-button="showClearBtn"
              :allow-edit="allowEdit"
              @change="onChange"
              placeholder=""
            ></mt-date-time-picker>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              disabled,
              showClearBtn,
              allowEdit,
              isDate,
              isTime,
              isDateTime
            }
          },
          filters: {},
          methods: {
            onChange(e) {
              let data = null
              if (isDate) {
                data = e
              } else if (isTime && e.isInteracted) {
                data = e.value
              }

              this.data[dataKey] = data
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = data
            }
          }
        })
      }
    }

    return template
  },
  // 带红星的表头
  requiredHeader: (args) => {
    const { headerText } = args
    const template = () => {
      return {
        template: Vue.component('requiredHeaderComponent', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{ headerText }}</span>
              </div>
            `,
          data: function () {
            return {
              data: {},
              headerText
            }
          },
          beforeDestroy() {},
          mounted() {},
          methods: {}
        })
      }
    }
    return template
  }
}
