import { ColumnComponent as Component } from './columnComponent'
import { itemOptions, workCenter } from './variable'

// 格式化表格动态数据
export const formatTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: data.length > 10 ? '150' : 'auto'
    }
    if (col.fieldCode === 'thePrimaryKey') {
      // 行主键
      defaultCol.visible = false
      defaultCol.isIdentity = true // 加上这个，新增一行的时候，主键不会变成可输入
      defaultCol.isPrimaryKey = true
      defaultCol.allowEditing = false
    } else if (col.fieldCode === 'checkBox') {
      // checkBox
      defaultCol.type = 'checkbox'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.showInColumnChooser = false
      defaultCol.width = '50'
      // 编辑时显示
      defaultCol.editTemplate = Component.empty
    } else if (col.fieldCode === 'serialNumber') {
      // 序号
      defaultCol.width = '90'
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'itemName') {
      // 物料名称
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.select({
        dataKey: col.fieldCode,
        fields: { text: 'itemName', value: 'itemName' },
        selectOptions: itemOptions,
        allowFiltering: true,
        showClearBtn: true,
        modifiedKeys: [
          'itemCode', // 物料编号
          'purUnitName', // 单位
          'purUnitCode', // 采购单位编码
          // "TODO", // 工序名称
          'limitQuantity', // 限量数量
          'warehouseClerkName', // 仓管员
          'warehouseClerkCode', // 仓管员Code
          'dispatcherName', // 调度员
          'dispatcherCode' // 调度员Code
        ]
      })
    } else if (col.fieldCode === 'itemCode') {
      // 物料编号
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.changedText({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'buyerOrgName') {
      // 采购组
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.changedText({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'buyerOrgCode') {
      // 采购组编码
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.changedText({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'purUnitName') {
      // 单位
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.changedText({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'projectTextBatch') {
      // 项目文本批次
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 200
      })
    } else if (col.fieldCode === 'workCenterName') {
      // 工作中心
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.select({
        dataKey: col.fieldCode,
        fields: { text: 'workCenterName', value: 'workCenterName' },
        selectOptions: workCenter,
        allowFiltering: true,
        showClearBtn: true,
        modifiedKeys: [
          'workCenterCode' // 工作中心Code
        ]
      })
    } else if (col.fieldCode === 'TODO') {
      // 工序名称
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.changedText({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'limitQuantity') {
      // 限量数量
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.changedText({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'warehouseClerkName') {
      // 仓管员
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.changedText({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'dispatcherName') {
      // 调度员
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.changedText({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'demandDate') {
      // 需求日期
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.template = Component.timeDate({
        dataKey: col.fieldCode,
        isDate: true
      })
      defaultCol.editTemplate = Component.timeInput({
        dataKey: col.fieldCode,
        disabled: false,
        showClearBtn: true,
        allowEdit: false,
        isDate: true
      })
    } else if (col.fieldCode === 'demandTime') {
      // 需求时间
      defaultCol.template = Component.timeDate({
        dataKey: col.fieldCode,
        isTime: true
      })
      defaultCol.editTemplate = Component.timeInput({
        dataKey: col.fieldCode,
        disabled: false,
        showClearBtn: true,
        allowEdit: false,
        isTime: true
      })
    } else if (col.fieldCode === 'thisDeliveryQuantity') {
      // 本次送货数量
      defaultCol.width = '250'
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.number({
        dataKey: col.fieldCode,
        precision: 3,
        maxValue: 999999999999999.999,
        minValue: 0,
        showClearBtn: true
      })
    } else if (col.fieldCode === 'remark') {
      // 备注
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 200
      })
    }
    colData.push(defaultCol)
  })

  return colData
}
