<!-- 采方 - 报关要素资料 - new -->
<template>
  <div class="full-height">
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('供应商编码')" prop="supplierCode">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料编码')" prop="itemCode">
          <mt-input
            v-model="itemCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
            @change="(e) => onChange(e, 'itemCode')"
          />
          <!-- <RemoteAutocomplete
            v-model="searchFormModel.itemCode"
            :url="$API.masterData.getItemUrl"
            :placeholder="$t('请选择')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :search-fields="['itemName', 'itemCode']"
          /> -->
        </mt-form-item>
        <mt-form-item :label="$t('商品申报品名')" prop="goodsDeclareName">
          <mt-input
            v-model="searchFormModel.goodsDeclareName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('商品编码')" prop="goodsCode">
          <mt-input
            v-model="searchFormModel.goodsCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('状态')" prop="approveStatus">
          <mt-multi-select
            v-model="searchFormModel.approveStatus"
            :data-source="statusOptions"
            :fields="{ text: 'label', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('品牌')" prop="materialBrand">
          <mt-input
            v-model="searchFormModel.materialBrand"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="af27136a-7876-480e-a154-60a366fc2246"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :tooltip-config="tooltipConfig"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          v-permission="item.permission"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #imgSlots="{ row, column }">
        <vxe-button size="small" @click="handleUpload(row, column)">{{
          $t('查看附件')
        }}</vxe-button>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, statusOptions } from './config'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  components: { CollapseSearch, ScTable },
  data() {
    return {
      searchFormModel: {},
      supplierCode: null,
      itemCode: null,
      toolbar: [
        { code: 'pass', name: this.$t('通过'), status: 'info', permission: ['O_02_1729'] },
        { code: 'reject', name: this.$t('驳回'), status: 'info', permission: ['O_02_1730'] },
        { code: 'export', name: this.$t('导出'), status: 'info', permission: ['O_02_1731'] }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 100,
        pageSizes: [50, 100, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],
      tooltipConfig: {
        showAll: true,
        enterable: true
      },

      statusOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    onChange(e, field) {
      if (e) {
        this.searchFormModel[`${field}`] = this[field].split(' ')
      } else {
        this.searchFormModel[`${field}`] = null
        this[field] = null
      }
    },
    handleUpload(row, column) {
      let params = {
        docId: row.id,
        docType: null
      }
      switch (column.field) {
        case 'singleProductImageUrl':
          params.docType = '19'
          break
        case 'productInPackagingPicturesUrl':
          params.docType = '20'
          break
        case 'productOutPackagingPicturesUrl':
          params.docType = '21'
          break
        case 'brandAuthorizationCertificateUrl':
          params.docType = '22'
          break
        default:
          break
      }
      this.$dialog({
        modal: () => import('./components/FileManage.vue'),
        data: {
          title: this.$t('查看附件'),
          type: 'download',
          params
        }
      })
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.itemCode = null
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.receiptAndDelivery
        .pageCustomsDeclarationElementsNewApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const list = res.data?.records
        this.tableData = list
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const ids = []
      selectedRecords.forEach((item) => {
        ids.push(item.id)
      })
      const commonToolbar = ['pass', 'reject']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'pass':
          this.handlePass(ids)
          break
        case 'reject':
          this.handleReject(ids)
          break
        case 'export':
          this.handleExport(ids)
          break
        default:
          break
      }
    },
    handlePass(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认通过？')
        },
        success: () => {
          this.$API.receiptAndDelivery.passCustomsDeclarationElementsNewApi({ ids }).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.handleSearch()
            }
          })
        }
      })
    },
    handleReject(ids) {
      this.$dialog({
        data: {
          title: this.$t('驳回')
        },
        modal: () => import('./components/RejectDialog.vue'),
        success: async (purchaserRemark) => {
          let params = {
            ids,
            purchaserRemark
          }
          this.$store.commit('startLoading')
          let api = this.$API.receiptAndDelivery.rejectCustomsDeclarationElementsNewApi
          const res = await api(params).finally(() => {
            this.$store.commit('endLoading')
          })
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleSearch()
          }
        }
      })
    },
    handleExport(ids) {
      const params = {
        ids,
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery.exportCustomsDeclarationElementsNewApi(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
