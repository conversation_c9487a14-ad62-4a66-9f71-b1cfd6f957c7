<template>
  <!-- 手工新建无PO送货单-供方 -->
  <div class="full-height pt20 vertical-flex-box">
    <!-- 头部信息 -->
    <top-info
      ref="topInfoRef"
      class="flex-keep"
      :header-info="headerInfo"
      @goBack="goBack"
      @doSubmit="doSubmit"
      @doExpand="doExpand"
      @companyCodeChange="companyCodeChange"
      @siteCodeChange="siteCodeChange"
    ></top-info>
    <mt-tabs :e-tab="false" :data-source="tabList" @handleSelectTab="handleSelectTab"></mt-tabs>
    <!-- 列模板 -->
    <mt-template-page
      class="flex-fit frozenFistColumns"
      ref="templateRef"
      v-show="currentTabInfo.code === TabCode.materialInfo"
      :hidden-tabs="false"
      :template-config="templateConfig"
      @handleClickToolBar="handleClickToolBar"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    >
    </mt-template-page>
    <!-- 物流信息 Tab -->
    <logistics-info
      class="flex-fit"
      ref="logisticsInfoRef"
      v-show="currentTabInfo.code === TabCode.logisticsInfo"
      :logistics-data="logisticsData"
    ></logistics-info>
  </div>
</template>

<script>
import {
  ColumnData,
  MaterialInfoToolbar,
  DeliveryType,
  EditSettings,
  RequestType,
  NewRowData,
  ActionType,
  TabList,
  TabCode
} from './config/constant'
import { rowDataTemp, materialInfoDataSource, itemOptions } from './config/variable'
import { formatTableColumnData } from './config/index'
import { cloneDeep } from 'lodash'
import { maxPageSize } from '@/utils/constant'
import { timeNumberToDate } from '@/utils/utils'
import { uniqWith, isEqual, filter } from 'lodash'

export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue'),
    LogisticsInfo: () => import('./components/logisticsInfo.vue')
  },
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      // 顶部的数据
      headerInfo: {
        siteName: null, // 工厂
        siteCode: null, // 工厂code
        companyName: null, // 公司
        companyCode: null, // 公司code
        warehouseName: null, // 交货库存地点
        warehouseCode: null, // 交货库存地点code
        deliveryType: DeliveryType.notLink, // 送货单类型 固定 无关联采购订单送货（用于显示）
        // sendName: null, // 送货方名称 (收货方名称，值即为公司)
        // sendCode: null, // 送货方名称编号 (收货方名称编号，值即为公司code)
        // 送货方式 固定 无需求送货
        sendAddressName: null, // 发货地点
        receiveAddressName: null, // 送货地址
        sendTime: null, // 发货日期
        forecastArriveTime: null, // 预计到货日期
        remark: null, // 供方备注
        customerEnterpriseId: null // 客户企业id
      },
      // 物流信息
      logisticsData: {
        carNo: null, // 公司
        deliveryId: null, // 加工商租户id
        driverName: null, // 司机名称
        driverNo: null, // 司机身份证
        driverPhone: null, // 司机联系方式
        logisticsCompanyCode: null, // 物流公司code
        logisticsCompanyName: null, // 物流公司
        logisticsNo: null, // 物流单号
        number: null, // 数量
        remark: null, // 备注
        type: null // 发货方式:1-快递配送，2-物流配送
      },
      templateConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 不使用组件中的toolbar配置
          toolbar: MaterialInfoToolbar,
          grid: {
            editSettings: EditSettings,
            allowPaging: false, // 不分页
            columnData: formatTableColumnData({
              data: ColumnData
            }),
            dataSource: materialInfoDataSource,
            asyncConfig: {}
          }
        }
      ],
      isEditing: false, // 正在编辑状态
      TabCode,
      tabList: TabList,
      currentTabInfo: {
        code: TabCode.materialInfo
      } // 当前tab的数据 默认：物料信息
    }
  },
  mounted() {},
  methods: {
    // 点击 tab
    handleSelectTab(e) {
      this.currentTabInfo = this.tabList[e]
    },
    // actionBegin 表格编辑生命周期
    actionBegin(args) {
      const { requestType, action, rowData } = args
      // console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args);
      if (requestType === RequestType.add) {
        // 开始行添加
        this.isEditing = true
        // 即将添加一行，赋值新增行的初始数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        const newRowData = cloneDeep(NewRowData)
        rowDataTemp.push(newRowData)
        args.rowData = newRowData
        args.data = newRowData
      } else if (requestType === RequestType.save && action === ActionType.add) {
        // 即将保存新增时，将新增中编辑的数据付给组件
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === RequestType.beginEdit) {
        // 开始行编辑
        this.isEditing = true
        // 即将编辑行，赋值当前行的数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },
    // actionComplete 表格编辑生命周期
    actionComplete(args) {
      const { requestType, action, rowIndex, index } = args
      const rowData = rowDataTemp[rowDataTemp.length - 1]
      // console.log(
      //   `Complete,\nrequest: ${requestType}\naction: ${action}`,
      //   args
      // );
      if (requestType === RequestType.save && action == ActionType.edit) {
        // 完成编辑行
        this.isEditing = false
        // rowData, rowIndex
        if (!this.isValidData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          materialInfoDataSource.splice(rowIndex, 1, rowData)
        }
      } else if (requestType === RequestType.save && action == ActionType.add) {
        // 完成新增行
        this.isEditing = false
        // rowData, index
        if (!this.isValidData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          materialInfoDataSource.unshift(rowData)
          materialInfoDataSource.forEach((item, index) => {
            item.serialNumber = index + 1
          })
        }
      } else if (requestType === RequestType.sorting || requestType === RequestType.refresh) {
        // 丢弃编辑中状态
        this.isEditing = false
      }
    },
    // 校验数据
    isValidData(data) {
      const { itemCode, demandDate, thisDeliveryQuantity, buyerOrgCode } = data
      let valid = false
      if (!itemCode) {
        // 物料代码
        this.$toast({ content: this.$t('物料不可为空'), type: 'warning' })
      } else if (!buyerOrgCode) {
        // 采购组编码
        this.$toast({
          content: this.$t('采购组不可为空'),
          type: 'warning'
        })
      } else if (!demandDate) {
        // 需求日期
        this.$toast({ content: this.$t('需求日期不可为空'), type: 'warning' })
      } else if (!thisDeliveryQuantity || thisDeliveryQuantity == 0) {
        // 本次送货数量
        this.$toast({
          content: this.$t('本次送货数量不可为空'),
          type: 'warning'
        })
      } else {
        valid = true
      }

      return valid
    },
    // toolbar 按钮点击
    handleClickToolBar(args) {
      const { toolbar, grid } = args
      const selectedRecords = grid.getSelectedRecords()
      const commonToolbar = [
        'MaterialInfoUpdate',
        'MaterialInfoAdd',
        'Filter',
        'Refresh',
        'refreshDataByLocal',
        'filterDataByLocal',
        'resetDataByLocal',
        'ForecastExport',
        'Setting'
      ]

      if (selectedRecords.length == 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const primaryKeyList = []
      selectedRecords.forEach((item) => {
        primaryKeyList.push(item.thePrimaryKey)
      })

      if (toolbar.id === 'MaterialInfoAdd') {
        // 新增
        if (!this.headerInfo.siteCode) {
          this.$toast({ content: this.$t('请先选择工厂'), type: 'warning' })
        } else if (itemOptions.length === 0) {
          this.$toast({
            content: this.$t('该工厂没有关联的物料信息'),
            type: 'warning'
          })
        } else {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
        }
      } else if (toolbar.id === 'MaterialInfoDel') {
        // 移除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认移除选中的数据？')
          },
          success: () => {
            // 移除行
            this.handleMaterialInfoDel(primaryKeyList)
          }
        })
      } else if (toolbar.id === 'MaterialInfoUpdate') {
        // 更新-结束行编辑
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    },
    // 移除行
    handleMaterialInfoDel(primaryKeyList) {
      primaryKeyList.forEach((itemPrimaryKey) => {
        for (let i = 0; i < materialInfoDataSource.length; i++) {
          if (materialInfoDataSource[i].thePrimaryKey === itemPrimaryKey) {
            materialInfoDataSource.splice(i, 1)
            break
          }
        }
      })
      materialInfoDataSource.forEach((item, index) => {
        item.serialNumber = index + 1
      })
    },
    goBack() {
      // 返回 送货单-列表-供方
      this.$router.push({
        name: 'deliver-list-supplier-tv'
      })
    },
    doSubmit() {
      const topInfoValidate = this.$refs.topInfoRef.doValidate()
      let logisticsInfoValidate = true // 物流信息非必填，默认为验证通过

      if (this.isEditing) {
        // 结束行编辑
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }

      if (this.logisticsData.type) {
        // 如果选择了物流方式（填写了物流信息时，校验物流信息填写是否正确）
        logisticsInfoValidate = this.$refs.logisticsInfoRef.doValidate()
      }

      if (this.currentTabInfo.code === TabCode.materialInfo && !logisticsInfoValidate) {
        // tab 不在物流信息tab && 物流信息校验失败
        this.$toast({ content: this.$t('物流信息校验失败'), type: 'warning' })
      }

      if (logisticsInfoValidate && topInfoValidate && materialInfoDataSource.length > 0) {
        // 物流信息正确 && 头部信息正确 && 有物料信息
        const list = materialInfoDataSource.map((item) => {
          let demandDate = undefined // 需求日期
          let demandTime = undefined // 需求时间
          if (item.demandDate) {
            demandDate = timeNumberToDate({
              formatString: 'YYYY-mm-dd',
              value: item.demandDate
            })
          }
          if (item.demandTime) {
            demandTime = timeNumberToDate({
              formatString: 'YYYY-mm-dd',
              value: item.demandTime
            })
          }
          return {
            ...item,
            warehouseName: this.headerInfo.warehouseName, // 交货库存地点
            warehouseCode: this.headerInfo.warehouseCode, // 交货库存地点code
            siteCode: this.headerInfo.siteCode, // 工厂code
            siteName: this.headerInfo.siteName, // 工厂
            thePrimaryKey: undefined,
            demandDate, // 需求日期
            demandTime // 需求时间
          }
        })
        let sendTime = undefined // 发货日期
        let forecastArriveTime = undefined // 预计到货日期
        if (this.headerInfo.sendTime) {
          sendTime = Number(this.headerInfo.sendTime)
        }
        if (this.headerInfo.forecastArriveTime) {
          forecastArriveTime = Number(this.headerInfo.forecastArriveTime)
        }
        const params = {
          ...this.headerInfo,
          sendTime, // 发货日期
          forecastArriveTime, // 预计到货日期
          list,
          logisticsInfo: this.logisticsData
        }
        // 供方送货单-主单-送货单创建无订单送货单
        this.postSupplierOrderDeliveryCreateNoOrder(params)
      }
    },
    doExpand() {
      this.$refs.templateRef?.resetGridHeight && this.$refs.templateRef.resetGridHeight()
    },
    // 头部公司修改
    companyCodeChange() {
      // 清空物料信息表格数据
      materialInfoDataSource.splice(0, materialInfoDataSource.length)
    },
    // 头部工厂修改
    siteCodeChange(args) {
      const { itemData } = args
      // 清空物料信息表格数据
      materialInfoDataSource.splice(0, materialInfoDataSource.length)
      // 通过工厂获取物料
      this.postSupplierQueryItemDetail({ siteOrgCode: itemData.siteOrgCode })
    },
    // 供应商价格记录用户接口-分页查询物料价格记录详情
    postSupplierQueryItemDetail(args) {
      const { siteOrgCode } = args
      const page = { page: { current: 1, size: maxPageSize } }
      const rules = {
        condition: 'and',
        defaultRules: [
          {
            field: 'siteCode', // key
            operator: 'equal', // 等于
            value: siteOrgCode // value
          }
        ]
      }
      itemOptions.splice(0, itemOptions.length) // 清空物料数组数据
      this.$API.receiptAndDelivery
        .postSupplierQueryItemDetail({
          ...page,
          ...rules
        })
        .then((res) => {
          if (res?.code == 200) {
            const records = res?.data?.records || []
            let tmp = records.map((item) => {
              return {
                itemName: item.itemName, // 物料名称
                itemCode: item.itemCode, // 物料编号
                purUnitName: item.unitName, // 单位名称
                purUnitCode: item.unitCode // 单位代码
              }
            })
            tmp = uniqWith(
              filter(tmp, (item) => item.itemCode), // 过滤出有 物料编号 的数据
              isEqual
            ) // 去重
            tmp.forEach((item) => {
              itemOptions.push({
                ...item
              })
            })
          }
        })
        .catch(() => {})
    },
    // 供方送货单-主单-送货单创建无订单送货单
    postSupplierOrderDeliveryCreateNoOrder(params) {
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierOrderDeliveryCreateNoOrder(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({
              content: this.$t(`操作成功`),
              type: 'success'
            })
            // 返回 送货单-列表-供方
            this.$router.push({
              name: 'deliver-list-supplier-tv'
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .flex-keep {
  margin-top: 10px;
}

.mt-tabs {
  width: 100%;
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
</style>
