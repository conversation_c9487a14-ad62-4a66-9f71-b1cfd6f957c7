import { i18n } from '@/main.js'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import inputView from '../components/inputView.vue'

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, isDateTime, isDate, isTime } = args

  const template = () => {
    return {
      template: Vue.component('timeDateComponent', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <div v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</div>
            <div v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</div>
          </div>
        </div>`,
        data: function () {
          return { data: {}, dataKey, isDateTime, isDate, isTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
// tab 的 index
export const TabIndex = {
  deliverySchedule: 0, // 交货计划
  purchaseOrder: 1, // 采购订单
  jit: 2 // JIT
}

// Toolbar 按钮
export const Toolbar = {
  // 订单供货计划
  [TabIndex.purchaseOrder]: [
    {
      id: 'BatchPreCreateDelivery',
      icon: 'icon_solid_Createproject',
      permission: ['O_02_0634'],
      title: i18n.t('批量创建送货单')
    },
    {
      id: 'TableExport',
      icon: 'icon_solid_export',
      permission: ['O_02_0635'],
      title: i18n.t('导出')
    }
  ],
  // 交货计划
  [TabIndex.deliverySchedule]: [
    {
      id: 'BatchPreCreateDelivery',
      icon: 'icon_solid_Createproject',
      permission: ['O_02_0631'],
      title: i18n.t('批量创建送货单')
    },
    {
      id: 'CreateNoOrder',
      icon: 'icon_table_delivery_note',
      permission: ['O_02_0632'],
      title: i18n.t('创建无采购订单送货单')
    },
    {
      id: 'TableExport',
      icon: 'icon_solid_export',
      permission: ['O_02_0633'],
      title: i18n.t('导出')
    }
  ],
  // JIT供货计划
  [TabIndex.jit]: [
    {
      id: 'BatchPreCreateDelivery',
      icon: 'icon_solid_Createproject',
      permission: ['O_02_1162'],
      title: i18n.t('批量创建送货单')
    },
    // 不需要这个按钮，只有供方-收发货-交货计划tab才有
    // {
    //   id: "CreateNoOrder",
    //   icon: "icon_table_delivery_note",
    //   // permission: [""],
    //   title: i18n.t("创建无采购订单送货单"),
    // },
    {
      id: 'TableExport',
      icon: 'icon_solid_export',
      permission: ['O_02_1161'],
      title: i18n.t('导出')
    }
  ]
}

// tab1 采购订单 表格列数据
export const ColumnDataTab1 = [
  {
    fieldCode: 'companyName', // 公司
    fieldName: i18n.t('公司'),
    searchOptions: { operator: 'likeright' }
  },
  {
    fieldCode: 'companyCode', // 公司编码
    fieldName: i18n.t('公司编码'),
    searchOptions: { operator: 'equal' }
  },
  {
    fieldCode: 'siteName', // 工厂
    fieldName: i18n.t('工厂'),
    searchOptions: { operator: 'likeright' }
  },
  {
    fieldCode: 'siteCode', // 工厂编码
    fieldName: i18n.t('工厂编码'),
    searchOptions: { operator: 'equal' }
  },
  {
    fieldCode: 'itemCode', // 物料号 品项编码
    fieldName: i18n.t('物料号'),
    searchOptions: { operator: 'likeright' }
  },
  {
    fieldCode: 'itemName', // 物料名称
    fieldName: i18n.t('物料名称'),
    searchOptions: { operator: 'likeright' }
  },
  {
    fieldCode: 'orderCode', // 关联采购订单 订单号
    fieldName: i18n.t('关联采购订单'),
    searchOptions: { operator: 'equal' }
  },
  {
    fieldCode: 'requiredDeliveryDate', // 需求日期 要求交期
    fieldName: i18n.t('需求日期'),
    template: timeDate({ dataKey: 'requiredDeliveryDate', isDate: true }),

    allowFiltering: false,
    // ignore: true,
    searchOptions: {
      ...MasterDataSelect.timeRange,
      renameField: 'timeInfoTimestamp'
    }
  },
  {
    fieldCode: 'quantity', // 订单数量
    fieldName: i18n.t('订单数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'preDeliveryQty', // 待发货数量
    fieldName: i18n.t('待发货数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'theNumberOfShipments', // 发货数量 前端定义
    fieldName: i18n.t('发货数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'deliveryQty', // 已发货数量
    fieldName: i18n.t('已发货数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'receiveQty', // 已入库数量
    fieldName: i18n.t('已入库数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'warehouse', // 交货库存地点 库存地点 warehouseCode
    fieldName: i18n.t('库存地点编号+库存地点名称'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'supplierCode',
    fieldName: i18n.t('供应商编码'),
    searchOptions: { operator: 'equal' }
  },
  {
    fieldCode: 'supplierName',
    fieldName: i18n.t('供应商名称'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'buyerOrgName', // 采购组
    fieldName: i18n.t('采购组'),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.businessCodeName,
      renameField: 'buyerOrgCode'
    }
    // ignore: true,
  },
  {
    fieldCode: 'saleOrderNo', // 关联销售订单号
    fieldName: i18n.t('关联销售订单号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'projectTextBatch', // 项目文本批次
    fieldName: i18n.t('项目文本批次'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'itemNo', // 关联采购订单行号 行号
    fieldName: i18n.t('关联采购订单行号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'buyerRemark', // 采购方备注
    fieldName: i18n.t('采购方备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'supRemark', // 供应商备注
    fieldName: i18n.t('供应商备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'consignee', // 联系人 收货人
    fieldName: i18n.t('联系人'),
    allowFiltering: false,
    ignore: true,
    template: () => ({ template: inputView })
  },
  {
    fieldCode: 'contact', // 联系电话
    fieldName: i18n.t('联系电话'),
    allowFiltering: false,
    ignore: true,
    template: () => ({ template: inputView })
  },
  {
    fieldCode: 'receiveAddress', // 送货地址
    fieldName: i18n.t('送货地址')
  }
]

// tab2 交货计划 表格列数据
export const ColumnDataTab2 = [
  {
    fieldCode: 'companyName', // 公司
    fieldName: i18n.t('公司'),
    ignore: true
  },
  {
    fieldCode: 'companyCode', // 公司编码
    fieldName: i18n.t('公司编码'),
    searchOptions: { ...MasterDataSelect.companySupplier }
  },
  {
    fieldCode: 'siteName', // 工厂
    fieldName: i18n.t('工厂'),
    ignore: true
  },
  {
    fieldCode: 'siteCode', // 工厂编码
    fieldName: i18n.t('工厂编码'),
    searchOptions: { ...MasterDataSelect.factorySupplierAddress }
  },
  {
    fieldCode: 'itemCode',
    fieldName: i18n.t('物料编码'),
    searchOptions: { operator: 'likeright' }
  },
  {
    fieldCode: 'itemName',
    fieldName: i18n.t('物料名称'),
    searchOptions: { operator: 'likeright' }
  },
  {
    fieldCode: 'stockAdmin',
    fieldName: i18n.t('仓管员')
  },
  {
    fieldCode: 'orderCode', // 关联采购订单 采购订单号
    fieldName: i18n.t('关联采购订单'),
    searchOptions: { operator: 'equal' }
  },
  {
    fieldCode: 'requiredDeliveryDate', // 需求日期
    fieldName: i18n.t('需求日期'),
    // template: () => {
    //   return {
    //     template: Vue.component("actionInput", {
    //       template: `<div>{{data.requiredDeliveryDate}}-{{data.requiredDeliveryTime}}</div>`,
    //       data: function () {
    //         return { data: {} };
    //       },
    //       mounted() {},
    //       methods: {},
    //     }),
    //   };
    // },
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.timeRange,
      renameField: 'timeInfoTimestamp'
    }
    // ignore: true,
  },
  {
    fieldCode: 'quantity', // 需求数量
    fieldName: i18n.t('需求数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'remainingQty', // 剩余可创建数量
    fieldName: i18n.t('剩余可创建数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'theNumberOfShipments', // 发货数量 前端定义
    fieldName: i18n.t('发货数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'receiveQty', // 收货数量
    fieldName: i18n.t('收货数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'deliveryQty', // 在途数量
    fieldName: i18n.t('在途数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'warehouseCode', // 交货库存地点 库存地点名称 warehouseCode
    fieldName: i18n.t('库存地点编号'),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.stockSupplierAddressName,
      operator: 'in',
      renameField: 'warehouseCode'
    }
  },
  {
    fieldCode: 'warehouseName', // 交货库存地点 库存地点名称 warehouseCode
    fieldName: i18n.t('库存地点名称'),
    allowFiltering: false,
    searchOptions: { operator: 'likeright' }
  },
  {
    fieldCode: 'stockQty', // 库存量
    fieldName: i18n.t('库存量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'transferPlanName', // 计划员
    fieldName: i18n.t('计划员'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'supplierCode',
    fieldName: i18n.t('供应商编码'),
    searchOptions: { operator: 'equal' }
  },
  {
    fieldCode: 'supplierName',
    fieldName: i18n.t('供应商名称'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'planGroupName', // 计划组
    fieldName: i18n.t('计划组'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'buyerOrgName', // 采购组
    fieldName: i18n.t('采购组'),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.businessCodeName,
      renameField: 'buyerOrgCode'
    }
    // ignore: true,
  },
  {
    fieldCode: 'projectTextBatch', // 项目文本批次
    fieldName: i18n.t('项目文本批次'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'outsourcedType', // 委外方式
    fieldName: i18n.t('委外方式')
  },
  {
    fieldCode: 'deliveryMethod', // 是否直送 配送方式 0直送1非直送
    fieldName: i18n.t('是否直送'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'associatedNumber', // 关联工单号
    fieldName: i18n.t('关联工单号'),
    allowFiltering: false,
    searchOptions: {
      maxQueryValueLength: 100000,
      placeholder: i18n.t('支持粘贴多个精准及单个右模糊查询')
    }
  },
  {
    fieldCode: 'domesticDemandFlag',
    fieldName: i18n.t('是否内需跟单'),
    width: '160',
    valueConverter: {
      type: 'map',
      map: { E: i18n.t('是'), F: i18n.t('否') }
    }
  },
  {
    fieldCode: 'saleOrder',
    fieldName: i18n.t('销售订单号'),
    allowFiltering: false
  },
  {
    fieldCode: 'saleOrderRowCode',
    fieldName: i18n.t('销售订单行号'),
    width: '150'
  },
  {
    fieldCode: 'domesticDemandCode',
    fieldName: i18n.t('内需单号'),
    width: '150'
  },
  {
    fieldCode: 'serialNumber', // 序列号
    fieldName: i18n.t('序列号')
  },
  {
    fieldCode: 'bom', // BOM号
    fieldName: i18n.t('BOM号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'processName', // 工序名称
    fieldName: i18n.t('工序名称'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'productCode', // 工序产品代码
    fieldName: i18n.t('工序产品代码'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'picNo', // 图号
    fieldName: i18n.t('图号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'orderLineNo', // 关联采购订单行号 行号
    fieldName: i18n.t('关联采购订单行号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'processorName', // 加工商
    fieldName: i18n.t('加工商'),
    searchOptions: { operator: 'likeright' }
  },
  {
    fieldCode: 'processorCode', // 加工商编码
    fieldName: i18n.t('加工商编码'),
    searchOptions: { operator: 'equal' }
  },
  {
    fieldCode: 'systemRemark', // 系统备注
    fieldName: i18n.t('系统备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'buyerRemark', // 采购方备注
    fieldName: i18n.t('采购方备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'createUserName', // 创建人
    fieldName: i18n.t('创建人'),
    allowFiltering: false
    // ignore: true,
  },
  {
    fieldCode: 'supplierRemark', // 供应商备注
    fieldName: i18n.t('供应商备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'isJit', // 是否JIT 	是否jit 0否1是
    fieldName: i18n.t('是否JIT'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'isItemBatch', // 是否批次来料 0否1是
    fieldName: i18n.t('是否批次来料'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'supplierCheckUser', // 供应商确认人
    fieldName: i18n.t('供应商确认人'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'checkDate', // 确认日期
    fieldName: i18n.t('确认日期'),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.timeRange,
      renameField: 'timeInfoTimestamp'
    }
    // ignore: true,
  },
  {
    fieldCode: 'productLine', // 生产线
    fieldName: i18n.t('生产线'),
    allowFiltering: false
  },
  {
    fieldCode: 'batch', // 批量
    fieldName: i18n.t('批量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'senderPhone', // 送货联系电话 senderAddress
    fieldName: i18n.t('送货联系电话+送货地址'),
    allowFiltering: false,
    ignore: true
  }
  // {
  //   fieldCode: "senderAddress", // 送货地址
  //   fieldName: i18n.t("送货地址"),
  // },
]

// tab3 JIT 表格列数据
export const ColumnDataTab3 = [
  {
    fieldCode: 'companyName', // 公司
    fieldName: i18n.t('公司'),
    ignore: true
  },
  {
    fieldCode: 'companyCode', // 公司编码
    fieldName: i18n.t('公司编码'),
    searchOptions: { ...MasterDataSelect.companySupplier }
  },
  {
    fieldCode: 'siteName', // 工厂
    fieldName: i18n.t('工厂'),
    searchOptions: { ...MasterDataSelect.factorySupplierAddress, renameField: 'siteCode' }
  },
  {
    fieldCode: 'siteCode', // 工厂编码
    fieldName: i18n.t('工厂编码'),
    ignore: true
  },
  {
    fieldCode: 'subSiteCode', //
    fieldName: i18n.t('分厂'),
    searchOptions: {
      ...MasterDataSelect.subSiteCodeSupplier,
      url: '/srm-purchase-execute/tenant/supplierJitInfo/condition?conditionType=subSiteCode'
    }
  },
  {
    fieldCode: 'subSiteAddressCode', //
    fieldName: i18n.t('分厂库存地点'),
    searchOptions: {
      ...MasterDataSelect.subSiteAddressSupplier,
      url: '/srm-purchase-execute/tenant/supplierJitInfo/condition?conditionType=subSiteAddressCode'
    }
  },
  {
    fieldCode: 'itemCode',
    fieldName: i18n.t('物料编码'),
    searchOptions: { operator: 'likeright' }
  },
  {
    fieldCode: 'itemName',
    fieldName: i18n.t('物料名称'),
    ignore: true
  },
  {
    fieldCode: 'workOrder',
    fieldName: i18n.t('生产工单'),
    searchOptions: { operator: 'equal' }
  },
  {
    fieldCode: 'rowCode', //
    fieldName: i18n.t('JIT编号')
  },
  {
    fieldCode: 'orderCode', // 关联采购订单
    fieldName: i18n.t('关联采购订单'),
    searchOptions: {
      ...MasterDataSelect.orderCodeSupplier,
      url: '/srm-purchase-execute/tenant/supplierJitInfo/condition?conditionType=orderCode'
    }
  },
  // {
  //   fieldCode: "", // 关联采购订单行号 API 没有这个字段
  //   fieldName: i18n.t("关联采购订单行号"),
  // },
  {
    fieldCode: 'requiredDeliveryDate', // 需求日期
    fieldName: i18n.t('需求日期'),
    allowFiltering: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.requiredDeliveryDate}}-{{data.requiredDeliveryTime}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange,
      renameField: 'deliveryDate'
    }
    // ignore: true,
  },
  {
    fieldCode: 'quantity', // 需求数量
    fieldName: i18n.t('需求数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'remainingQty', // 剩余可创建数量
    fieldName: i18n.t('剩余可创建数量'),
    allowFiltering: false,
    type: 'number'
    // ignore: true,
  },
  {
    fieldCode: 'theNumberOfShipments', // 发货数量 前端定义
    fieldName: i18n.t('发货数量')
  },
  {
    fieldCode: 'receiveQty', // 收货数量
    fieldName: i18n.t('收货数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'deliveryQty', // 在途数量
    fieldName: i18n.t('在途数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'warehouseName', // 交货库存地点 库存地点名称 warehouseCode
    fieldName: i18n.t('库存地点编号+库存地点名称'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'transferPlanName', // 计划员
    fieldName: i18n.t('计划员')
  },
  {
    fieldCode: 'storemanName',
    fieldName: i18n.t('仓管员'),
    allowEditing: false
  },
  {
    fieldCode: 'supplierCode',
    fieldName: i18n.t('供应商编码'),
    searchOptions: { operator: 'equal' }
  },
  {
    fieldCode: 'supplierName',
    fieldName: i18n.t('供应商名称'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'planGroupName', // 计划组
    fieldName: i18n.t('计划组'),
    allowFiltering: false,
    ignore: true
    // planGroupCode
  },
  {
    fieldCode: 'buyerOrgName', // 采购组
    fieldName: i18n.t('采购组'),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.businessCodeName,
      renameField: 'buyerOrgCode'
    }
    // ignore: true,
    // buyerOrgCode
  },
  {
    fieldCode: 'deliveryMethod', // 是否直送 配送方式 0直送1非直送
    fieldName: i18n.t('是否直送'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'associatedNumber', // 关联工单号
    fieldName: i18n.t('关联工单号'),
    allowFiltering: false,
    ignore: true,
    searchOptions: {
      maxQueryValueLength: 100000,
      placeholder: i18n.t('支持粘贴多个精准及单个右模糊查询')
    }
  },
  {
    fieldCode: 'bom', // BOM号
    fieldName: i18n.t('BOM号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'saleOrderRowCode', // 关联销售订单行号
    fieldName: i18n.t('关联销售订单行号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'saleOrder', // 关联销售订单
    fieldName: i18n.t('关联销售订单'),
    allowFiltering: false,
    ignore: true
  },
  // {
  //   fieldCode: "", // 工作中心 API 没有这个字段
  //   fieldName: i18n.t("工作中心"),
  // },
  {
    fieldCode: 'processName', // 工序名称
    fieldName: i18n.t('工序名称'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'productCode', // 产品代码
    fieldName: i18n.t('产品代码'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'picNo', // 图号
    fieldName: i18n.t('图号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'systemRemark', // 系统备注
    fieldName: i18n.t('系统备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'dispatcherRemark', // 采购方备注
    fieldName: i18n.t('叫料员备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'batchCode',
    fieldName: i18n.t('版次号'),
    allowFiltering: false
  },
  {
    fieldCode: 'createUserName', // 创建人
    fieldName: i18n.t('创建人'),
    allowFiltering: false
    // ignore: true,
  },
  {
    fieldCode: 'createTime',
    fieldName: i18n.t('创建时间'),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    fieldCode: 'renewTime',
    fieldName: i18n.t('JIT更新时间'),
    allowFiltering: false,
    template: timeDate({ dataKey: 'renewTime', isDateTime: true }),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    fieldCode: 'supplierRemark', // 供应商备注
    fieldName: i18n.t('供应商备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'isJit', // 是否JIT 0否1是
    fieldName: i18n.t('是否JIT'),
    allowFiltering: false
  },
  {
    fieldCode: 'isItemBatch', // 是否批次来料 0否1是
    fieldName: i18n.t('是否批次来料'),
    allowFiltering: false
  },
  // {
  //   fieldCode: 'uniqueCode',
  //   fieldName: i18n.t('大数据平台唯一编号'),
  //   width: 200
  // },
  {
    fieldCode: 'versionNo',
    fieldName: i18n.t('版本号')
  },
  // {
  //   fieldCode: 'remarkExplain',
  //   fieldName: i18n.t('大数据平台备注')
  // },
  // {
  //   fieldCode: 'origSupplierCode',
  //   fieldName: i18n.t('上版供应商编号')
  // },
  // {
  //   fieldCode: 'origSupplierName',
  //   fieldName: i18n.t('上版供应商名称')
  // },
  {
    fieldCode: 'planDeliveryDate',
    fieldName: i18n.t('上版交货日期'),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    fieldCode: 'planDeliveryTime',
    fieldName: i18n.t('上版交货时间'),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    fieldCode: 'callMaterialQty',
    fieldName: i18n.t('上版叫料数量')
  },
  {
    fieldCode: 'supplierCheckUser', // 供应商确认人
    fieldName: i18n.t('供应商确认人'),
    allowFiltering: false,
    ignore: true
  },
  // {
  //   fieldCode: "", // 确认日期 API 没有这个字段
  //   fieldName: i18n.t("确认日期"),
  // },
  {
    fieldCode: 'productLine', // 生产线
    fieldName: i18n.t('生产线'),
    allowFiltering: false,
    searchOptions: {
      placeholder: i18n.t('多个生产线需要用空格隔开'),
      operator: 'in',
      serializeValue: (e) => {
        let obj = e.split(' ')
        //自定义搜索值，规则
        return obj
      },
      maxQueryValueLength: 10000
    }
  },
  {
    fieldCode: 'batch', // 批量
    fieldName: i18n.t('批量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'senderPhone', // senderPhone+senderAddress
    fieldName: i18n.t('送货联系电话+送货地址'),
    allowFiltering: false,
    ignore: true
  }
  // {
  //   fieldCode: "senderAddress", // 送货地址
  //   fieldName: i18n.t("送货地址"),
  // },
  // {
  //   fieldCode: "", // 库存量 API 没有这个字段
  //   fieldName: i18n.t("库存量"),
  // },
]
