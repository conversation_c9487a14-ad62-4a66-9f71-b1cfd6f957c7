import Vue from 'vue'
import { timeNumberToDate, timeStringToDate, numberInputOnKeyDown } from '@/utils/utils'

// 发货数量
export const shipmentsNum = () => {
  const template = () => {
    return {
      template: Vue.component('actionInput', {
        template: `<mt-input-number
        :min="0"
        v-model="data.theNumberOfShipments"
        cssClass="e-outline"
        :show-clear-button="false"
        :precision="0"
        @input="handleInput"
        @keydown.native="numberInputOnKeyDown"></mt-input-number>`,
        data: function () {
          return { data: {}, numberInputOnKeyDown }
        },
        mounted() {},
        methods: {
          handleInput(e) {
            // if (e <= 0) {
            //   this.$toast({
            //     type: 'warning',
            //     content: this.$t('发货数量不能小于或者等于0')
            //   })
            //   this.data.theNumberOfShipments = 0.001
            //   return
            // }
            // 校验数量
            if (this.data.preDeliveryQty !== undefined && !isNaN(this.data.preDeliveryQty)) {
              // 采购订单tab 校验 待发货数量
              if (e > this.data.preDeliveryQty) {
                this.$toast({
                  content: this.$t('发货数量不可大于待发货数量'),
                  type: 'warning'
                })
              }
            } else if (this.data.remainingQty !== undefined && !isNaN(this.data.remainingQty)) {
              // 交货计划tab 剩余可创建数量
              if (e > this.data.remainingQty) {
                this.$toast({
                  content: this.$t('发货数量不可大于剩余可创建数量'),
                  type: 'warning'
                })
              }
            }
            this.data.theNumberOfShipments = e
            this.$parent.$emit('cellEdit', {
              id: this.data.id,
              index: Number(this.data.index),
              key: 'theNumberOfShipments',
              value: Number(this.data.theNumberOfShipments)
            })
          }
        }
      })
    }
  }

  return template
}

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, hasTime } = args

  const template = () => {
    return {
      template: Vue.component('date', {
        template: `<div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div><div>{{data[dataKey] | dateFormat}}</div></div>`,
        data: function () {
          return { data: {}, dataKey, hasTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
