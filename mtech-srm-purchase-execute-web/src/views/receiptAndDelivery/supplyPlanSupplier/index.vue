<template>
  <!-- 供货计划-供方 -->
  <div class="full-height" ref="tableContainer">
    <!-- 列模板 -->
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="false"
      :current-tab="currentTab"
      :permission-obj="permissionObj"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleSelectTab="handleSelectTab"
      @cellEdit="cellEdit"
    >
      <!-- v-permission="['O_02_1124']" -->
      <div slot="slot-filter" class="zero-filter-switch">
        <mt-switch
          v-model="zeroFilterSwitch"
          :active-value="1"
          :inactive-value="0"
          :on-label="$t('隐藏不可创建送货单数据')"
          :off-label="$t('显示不可创建送货单数据')"
          @change="handleChangeZeroFilterSwitch"
        ></mt-switch>
      </div>
    </mt-template-page>
  </div>
</template>

<script>
import {
  ColumnDataTab1,
  ColumnDataTab2,
  ColumnDataTab3,
  Toolbar,
  TabIndex
} from './config/constant'
import { formatTableColumnData } from './config/index'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {},
  data() {
    const currentTab = JSON.parse(localStorage.getItem('tabIndex')) ?? 0
    localStorage.removeItem('tabIndex')

    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      initialSort: {
        columns: [{ field: 'remainingQty', direction: 'Descending' }]
      },
      currentTab, // 当前列模板显示的 Tab
      editData: [], // 行编辑过的数据 { id, index, key, value }
      zeroFilterSwitch: 1, // 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      zeroFilterPlan: 1, // 交货计划 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      zeroFilterOrder: 1, // 采购订单 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      zeroFilterJit: 1, // JIT 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'Plan', permissionCode: 'T_02_0045' },
          { dataPermission: 'Order', permissionCode: 'T_02_0044' },
          { dataPermission: 'Jit', permissionCode: 'T_02_0114' }
        ]
      },
      componentConfig: [
        {
          title: this.$t('交货计划'),
          dataPermission: 'Plan',
          permissionCode: 'T_02_0045',
          activatedRefresh: false,

          toolbar: Toolbar[TabIndex.deliverySchedule],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId: this.$tableUUID.receiptAndDelivery.supplyPlanSupplier.deliverySchedule,
          grid: {
            // sortSettings: {
            //   columns: [
            //     { field: "requiredDeliveryDate", direction: "Ascending" },
            //   ],
            // },
            allowPaging: true, // 分页
            lineSelection: 0, // 选项列
            lineIndex: 1, // 序号列
            columnData: formatTableColumnData({
              data: ColumnDataTab2
            }),
            dataSource: [],
            asyncConfig: {
              // 供方收发货供货计划-交货计划供货计划列表
              url: `${BASE_TENANT}/supplierDeliverySupplyPlan/plan/query`,
              // ignoreDefaultSearch: true,
              defaultRules: [],
              query: {
                zeroFilter: 1 // 默认 1: 过滤掉0的数据，其他：不过滤
              },
              // 序列化
              serializeList: this.serializeList
            }
            // frozenColumns: 1
          }
        },
        {
          title: this.$t('采购订单'),
          dataPermission: 'Order',
          permissionCode: 'T_02_0044',
          toolbar: Toolbar[TabIndex.purchaseOrder],
          activatedRefresh: false,

          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId: this.$tableUUID.receiptAndDelivery.supplyPlanSupplier.purchaseOrder,
          grid: {
            allowPaging: true, // 分页
            lineSelection: 0, // 选项列
            lineIndex: 1, // 序号列
            columnData: formatTableColumnData({
              data: ColumnDataTab1
            }),
            dataSource: [],
            asyncConfig: {
              // 供方收发货供货计划-订单供货计划列表
              url: `${BASE_TENANT}/supplierDeliverySupplyPlan/order/query`,
              defaultRules: [],
              query: {
                zeroFilter: 1 // 默认 1: 过滤掉0的数据，其他：不过滤
              },
              // 序列化
              serializeList: this.serializeList
            }
            // frozenColumns: 1
          }
        },
        {
          tab: { title: this.$t('叫料计划') },
          dataPermission: 'Jit',
          activatedRefresh: false,
          gridId: '3B39995E-2F82-FB50-D49B-822D2A591D58',
          permissionCode: 'T_02_0114',
          toolbar: Toolbar[TabIndex.jit],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          grid: {
            sortSettings: {
              columns: [{ field: 'requiredDeliveryDate', direction: 'Ascending' }]
            },
            allowPaging: true, // 分页
            lineSelection: 0, // 选项列
            lineIndex: 1, // 序号列
            columnData: formatTableColumnData({
              data: ColumnDataTab3
            }),
            dataSource: [],
            asyncConfig: {
              // 供方收发货供货计划-JIT供货计划列表
              url: `${BASE_TENANT}/supplierDeliverySupplyPlan/jit/query`,
              rules: [],
              defaultRules: [],
              query: {
                zeroFilter: 1 // 默认 1: 过滤掉0的数据，其他：不过滤
              },
              // 序列化
              serializeList: this.serializeList
            }
            // frozenColumns: 1
          }
        }
      ]
    }
  },
  mounted() {
    if (location.href.includes('kt')) {
      this.$bus.$on('createDeliveryTodo', () => {
        this.$refs.templateRef.refreshCurrentGridData()
      })
    }
    if (this.$route.query.from === 'mytodo') {
      if (JSON.parse(sessionStorage.getItem('todoDetail')).defaultRules === null) {
        this.currentTab = 0
      } else {
        this.currentTab = 2

        this.componentConfig[2].grid.asyncConfig.defaultRules = JSON.parse(
          sessionStorage.getItem('todoDetail')
        ).defaultRules
      }
    }
  },
  methods: {
    // ToolBar
    handleClickToolBar(args) {
      const { toolbar, gridRef } = args
      const selectRows = gridRef.getMtechGridRecords()
      // const selectRows = grid.getSelectedRecords()
      const commonToolbar = [
        'TableExport',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]
      if (selectRows.length === 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedId = []
      selectRows.forEach((item) => selectedId.push(item.id))

      if (toolbar.id === 'TableExport') {
        // 导出
        this.handleExport()
      } else if (toolbar.id === 'BatchPreCreateDelivery') {
        // 批量创建送货单
        this.handleBatchPreCreateDelivery(selectRows)
      } else if (toolbar.id === 'CreateNoOrder') {
        // 创建无采购订单送货单
        this.handleCreateNoOrder(selectRows)
      }
    },
    // CellTool
    handleClickCellTool() {},
    // 导出
    handleExport() {
      const currentTabIndex = this.$refs.templateRef.currentTabIndex
      if (currentTabIndex === TabIndex.deliverySchedule) {
        // 交货计划-导出
        this.handlePlanExport()
      } else if (currentTabIndex === TabIndex.purchaseOrder) {
        // 采购订单-导出
        this.handleOrderExport()
      } else if (currentTabIndex === TabIndex.jit) {
        // JIT-导出
        this.handleJitExport()
      }
    },
    // 交货计划-导出
    handlePlanExport() {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.apiStartLoading()
      this.$API.receiptAndDelivery.postSupplierDeliverySupplyPlanExport(params).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // // 采购订单-导出
    // handleOrderExport() {
    //   const queryBuilderRules =
    //     this.$refs.templateRef.getCurrentUsefulRef().pluginRef
    //       .queryBuilderRules || {};
    //   const params = {
    //     page: { current: 1, size: 10000 },
    //     ...queryBuilderRules,
    //   }; // 筛选条件
    //   this.apiStartLoading();
    //   this.$API.receiptAndDelivery
    //     .postSupplierDeliverySupplyPlanExportQuery(params)
    //     .then((res) => {
    //       this.apiEndLoading();
    //       const fileName = getHeadersFileName(res);
    //       download({ fileName: `${fileName}`, blob: res.data });
    //     });
    // },
    // Jit-导出
    handleJitExport() {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyJitExport(params, {
          query: this.zeroFilterPlan
        })
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
    },
    // 批量创建送货单
    handleBatchPreCreateDelivery(selectRows) {
      const selectRowsData = this.handleMatchEditDataById(selectRows, this.editData)
      console.log(this.editData)
      console.log(selectRows)

      // 校验 发货数量
      if (!this.checkTheNumberOfShipments(selectRowsData)) {
        // 校验失败
        return
      }
      const currentTabIndex = this.$refs.templateRef.currentTabIndex
      const params = []
      let isNull = false
      selectRowsData.forEach((item) => {
        if (item.theNumberOfShipments > 0) {
          params.push({
            id: item.id,
            num: item.theNumberOfShipments, // 发货数量 前端定义
            supplierTenantId: item.supplierTenantId // 供应商租户id
          })
        } else {
          isNull = true
        }
      })

      if (isNull) {
        this.$toast({
          type: 'warning',
          content: this.$t('发货数量不允许小于等于0')
        })
        return
      }

      if (params.length === 0) {
        this.$toast({ content: this.$t('未填写发货数量'), type: 'warning' })
        return
      }

      if (currentTabIndex === TabIndex.deliverySchedule) {
        // 交货计划-预创建送货单
        this.postSupplierDeliverySupplyPlanPlanPreCreate(params)
      } else if (currentTabIndex === TabIndex.purchaseOrder) {
        // 采购订单-预创建送货单
        this.postSupplierDeliverySupplyPlanOrderPreCreate(params)
      } else if (currentTabIndex === TabIndex.jit) {
        // JIT-预创建送货单
        this.postSupplierDeliverySupplyPlanJitPreCreate(params)
      }
    },
    // 创建无采购订单送货单
    handleCreateNoOrder(selectRows) {
      const selectRowsData = this.handleMatchEditDataById(selectRows, this.editData)
      const currentTabIndex = this.$refs.templateRef.currentTabIndex

      const params = []
      selectRowsData.forEach((item) => {
        if (item.theNumberOfShipments > 0) {
          params.push({
            id: item.id,
            num: item.theNumberOfShipments, // 发货数量 前端定义
            supplierTenantId: item.supplierTenantId // 供应商租户id
          })
        }
      })

      if (params.length === 0) {
        this.$toast({ content: this.$t('未填写发货数量'), type: 'warning' })
        return
      }

      if (currentTabIndex === TabIndex.jit) {
        // 供方收发货供货计划-JIT创建无订单送货单
        this.postSupplierDeliverySupplyPlanJitCreateNoOrder(params)
      } else if (currentTabIndex === TabIndex.deliverySchedule) {
        // 供方收发货供货计划-交货计划创建无订单送货单
        this.postSupplierDeliverySupplyPlanPlanCreateNoOrder(params)
      }
    },
    // 校验 发货数量
    checkTheNumberOfShipments(rowsData) {
      let isValid = true
      let isCheckPreDeliveryQty = false // 校验 采购订单tab 待发货数量
      let isCheckRemainingQty = false // 校验 交货计划tab、JIT tab 剩余可创建数量
      rowsData.forEach((itemRowData) => {
        if (itemRowData.preDeliveryQty !== undefined && !isNaN(itemRowData.preDeliveryQty)) {
          isCheckPreDeliveryQty = true
          // 采购订单tab 校验 待发货数量
          if (itemRowData.theNumberOfShipments > itemRowData.preDeliveryQty) {
            isValid = false
          }
        } else if (itemRowData.remainingQty !== undefined && !isNaN(itemRowData.remainingQty)) {
          isCheckRemainingQty = true
          // 交货计划tab、JIT tab 剩余可创建数量
          console.log(itemRowData.theNumberOfShipments, itemRowData.remainingQty)
          if (itemRowData.theNumberOfShipments > itemRowData.remainingQty) {
            isValid = false
          }
        }
      })
      if (isCheckPreDeliveryQty && !isValid) {
        this.$toast({
          content: this.$t('发货数量不可大于待发货数量'),
          type: 'warning'
        })
      } else if (isCheckRemainingQty && !isValid) {
        this.$toast({
          content: this.$t('发货数量不可大于剩余可创建数量'),
          type: 'warning'
        })
      }
      return isValid
    },
    // selectRows 与 editData 匹配 id，返回正确的数据，因为表格中 template 的数据与 dataSource 的数据不一致
    handleMatchEditDataById(selectRows, editData) {
      const result = []
      selectRows.forEach((item) => {
        editData.forEach((editItem) => {
          if (item.id === editItem.id) {
            item[editItem.key] = editItem.value
          }
        })
        result.push(item)
      })
      return result
    },
    // 序列化表格数据
    serializeList(list) {
      if (list.length > 0) {
        list.forEach((item) => {
          // 发货数量(前端定义) 默认等于 待发货数量 || 剩余可创建数量
          item.theNumberOfShipments = item.remainingQty ?? item.preDeliveryQty
          // 将编辑过的数据设置到表格中
          // const editData = this.editData.find(
          //   (itemEdit) => itemEdit.id === item.id
          // );
          // if (editData !== undefined) {
          //   item[editData.key] = editData.value;
          // }
        })
      }
      return list
    },
    // 行编辑
    cellEdit(e) {
      const { id, index, key, value } = e
      if (id !== undefined) {
        // 如果是编辑行
        const editIndex = this.editData.findIndex((item) => item.id === id && item.key === key)
        if (editIndex >= 0) {
          // 更新编辑的数据
          this.editData[editIndex].value = value
        } else {
          // 保存编辑的数据，value 为空也存
          this.editData.push({
            id,
            key,
            index,
            value
          })
        }
      }
    },
    // 跳转到批量创建送货单
    goToBatchDeliverySupplier(args) {
      const { data, params } = args
      const currentTabIndex = this.$refs.templateRef.currentTabIndex
      // 预创建订单数据 保存到 localStorage
      localStorage.setItem('batchDeliverySupplierData', JSON.stringify(data))
      // 预创建订单请求参数 保存到 localStorage
      localStorage.setItem('batchDeliverySupplierParams', JSON.stringify(params))
      // 将 lastTabIndex 放到 localStorage
      localStorage.setItem('lastTabIndex', JSON.stringify(currentTabIndex))
      // 页面跳转
      this.editData = []
      this.$router.push({
        name: 'batch-delivery-supplier' + (location.href.includes('kt') ? '-kt' : ''),
        query: {
          type: currentTabIndex, // 页面类型,
          date: new Date().getTime()
        }
      })
    },
    // 供方收发货供货计划-订单预创建送货单
    postSupplierDeliverySupplyPlanOrderPreCreate(params) {
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanOrderPreCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 跳转-批量创建送货单
            this.goToBatchDeliverySupplier({ data: res.data || [], params })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-交货计划预创建送货单
    postSupplierDeliverySupplyPlanPlanPreCreate(params) {
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanPlanPreCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 跳转-批量创建送货单
            this.goToBatchDeliverySupplier({ data: res.data || [], params })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-JIT预创建送货单
    postSupplierDeliverySupplyPlanJitPreCreate(params) {
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanJitPreCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // this.$toast({
            //   content: this.$t("操作成功"),
            //   type: "success",
            // });
            // 跳转-批量创建送货单
            this.goToBatchDeliverySupplier({ data: res.data || [], params })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-JIT创建无订单送货单
    postSupplierDeliverySupplyPlanJitCreateNoOrder(params) {
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanJitCreateNoOrder(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            // 刷新-当前表格数据
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-交货计划创建无订单送货单
    postSupplierDeliverySupplyPlanPlanCreateNoOrder(params) {
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanPlanCreateNoOrder(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            // 刷新-当前表格数据
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 是否显示不可创建送货单数据
    handleChangeZeroFilterSwitch(value) {
      console.log('value', value)
      // 保存当前 tab 选择的 zeroFilterSwitch 状态
      // 并修改列模板请求参数，调用api获取数据
      const currentTabIndex = this.$refs.templateRef.currentTabIndex
      if (currentTabIndex == TabIndex.deliverySchedule) {
        this.zeroFilterPlan = value
        this.$set(this.componentConfig[currentTabIndex].grid.asyncConfig.query, 'zeroFilter', value)
      } else if (currentTabIndex == TabIndex.purchaseOrder) {
        this.zeroFilterOrder = value
        this.$set(this.componentConfig[currentTabIndex].grid.asyncConfig.query, 'zeroFilter', value)
      } else if (currentTabIndex == TabIndex.jit) {
        this.zeroFilterJit = value
        this.$set(this.componentConfig[currentTabIndex].grid.asyncConfig.query, 'zeroFilter', value)
      }
    },
    // 切换tab
    handleSelectTab(index) {
      // 将各自 tab 保存的 zeroFilterSwitch 赋值给 zeroFilterSwitch
      // 并修改列模板请求参数，调用api获取数据
      if (index == TabIndex.deliverySchedule) {
        this.zeroFilterSwitch = this.zeroFilterPlan
        this.$set(
          this.componentConfig[index].grid.asyncConfig.query,
          'zeroFilter',
          this.zeroFilterPlan
        )
      } else if (index == TabIndex.purchaseOrder) {
        this.zeroFilterSwitch = this.zeroFilterOrder
        this.$set(
          this.componentConfig[index].grid.asyncConfig.query,
          'zeroFilter',
          this.zeroFilterOrder
        )
      } else if (index == TabIndex.jit) {
        this.zeroFilterSwitch = this.zeroFilterJit
        this.$set(
          this.componentConfig[index].grid.asyncConfig.query,
          'zeroFilter',
          this.zeroFilterJit
        )
      }
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('todoDetail')
  }
}
</script>

<style lang="scss" scoped>
// 表格容器
#table-container {
  position: relative;

  // 表格数据视图
  /deep/ .e-gridcontent > .e-content {
    height: 0px;
    transition: height 0.5s ease;
  }

  // 分页
  /deep/ .mt-pagertemplate {
    margin: 10px 0 10px !important;
  }
}
// 显示不可创建送货单数据
.zero-filter-switch {
  text-align: right;
}
</style>
