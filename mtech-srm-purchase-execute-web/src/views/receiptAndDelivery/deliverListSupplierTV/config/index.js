import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import Vue from 'vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, isDateTime, isDate, isTime } = args

  const template = () => {
    return {
      template: Vue.component('timeDateComponent', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <div v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</div>
            <div v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</div>
          </div>
        </div>`,
        data: function () {
          return { data: {}, dataKey, isDateTime, isDate, isTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
export const columnData = [
  {
    width: '200',
    field: 'deliveryCode',
    // allowResizing: false,
    // allowReordering: false,
    headerText: i18n.t('送货单号'),
    cellTools: []
  },
  {
    width: '82',
    field: 'statusDesc',
    headerText: i18n.t('状态'),
    // allowResizing: false,
    // allowReordering: false,
    cssClass: '',
    cellTools: [
      {
        id: 'active',
        icon: '',
        title: i18n.t('维护物流信息'),
        permission: ['O_02_0642'],
        visibleCondition: (data) => data['status'] === 2
      },
      {
        id: 'inactive',
        icon: '',
        title: i18n.t('取消'),
        permission: ['O_02_0643'],
        visibleCondition: (data) => data['status'] === 2
      }
    ]
  },
  {
    width: '100',
    field: 'carNo',
    headerText: i18n.t('车牌号')
    // allowResizing: false,
    // allowReordering: false
  },
  {
    width: '100',
    field: 'vehicleLogistics', // 车辆物流
    headerText: i18n.t('车辆物流'),
    // allowResizing: false,
    // allowReordering: false,
    template: () => {
      return {
        template: Vue.component('template-detail', {
          template: `
              <span v-if="data.carNo" style="color: #6386c1; cursor: pointer;" @click="toLogistics(data)">{{ $t('查看物流') }}</span>`,
          methods: {
            toLogistics(e) {
              const params = {
                ztpno: e?.deliveryCode?.toString(),
                busCode: e?.forecastCode,
                busNum: e.carNo
              }
              this.$API.receiptAndDelivery.purQueryVehicleLogistics(params).then((res) => {
                if (res.code === 200) {
                  window.open(res.data.mapURL)
                }
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'onWayStatusDesc', // 在途状态
    headerText: i18n.t('在途状态')
  },
  {
    width: '100',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '200',
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    searchOptions: {
      operator: 'likeright',
      ...MasterDataSelect.factorySupplierAddress,

      renameField: 'siteCode',
      ...MasterDataSelect.factorySupplierAddress
    }
  },
  {
    width: '135',
    field: 'warehouseName',
    headerText: i18n.t('库存地点'),
    searchOptions: {
      operator: 'likeright',
      ...MasterDataSelect.stockSupplierAddressName,
      renameField: 'warehouseCode'
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.warehouseCode}}-{{data.warehouseName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '120',
    field: 'deliveryTypeDesc',
    headerText: i18n.t('送货单类型'),
    allowResizing: false,
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '120',
    field: 'forecastDelivery',
    headerText: i18n.t('是否可预约'),
    cssClass: '',
    ignore: true,
    allowFiltering: false,
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if ([1, '1'].includes(e)) return i18n.t('否')
        return i18n.t('是')
      }
    }
  },
  {
    width: '97',
    field: 'sendTime',
    headerText: i18n.t('发货日期'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    template: timeDate({ dataKey: 'sendTime', isDateTime: true })
  },
  {
    width: '100',
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    width: '200',
    field: 'companyName',
    headerText: i18n.t('公司名称'),
    searchOptions: {
      operator: 'likeright',
      ...MasterDataSelect.companySupplier,

      renameField: 'companyCode',
      ...MasterDataSelect.companySupplier
    }
  },
  {
    field: 'associatedNo',
    headerText: i18n.t('关联单据编号')
  },
  {
    field: 'associateOuterDocNo',
    headerText: i18n.t('来源单号')
  },
  {
    field: 'doNo',
    headerText: i18n.t('来源单号2')
  },
  {
    field: 'virtualSupplierCode',
    headerText: i18n.t('虚拟供应商代码'),
    width: '140'
  },
  {
    width: '175',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '97',
    field: 'outsourcedTypeDesc',
    headerText: i18n.t('委外方式'),
    allowResizing: false,
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    field: 'printTimes',
    headerText: i18n.t('打印次数'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '130',
    field: 'thirdTenantCode',
    headerText: i18n.t('第三方物流商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.thirdTenantCode}}-{{data.thirdTenantName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '100',
    field: 'receiveAddressName',
    headerText: i18n.t('收货地址')
  },
  // {
  //   width: '100',
  //   field: 'sendAddressName',
  //   headerText: i18n.t('发货地点')
  // },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    width: '150',
    field: 'syncWmsStatusDesc',
    headerText: i18n.t('WMS同步状态'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'syncWmsDesc',
    headerText: i18n.t('WMS同步信息'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'syncQmsStatusDesc',
    headerText: i18n.t('QMS同步状态'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'syncQmsDesc',
    headerText: i18n.t('QMS同步信息'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'syncSapStatusDesc',
    headerText: i18n.t('SAP同步状态'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'syncSapDesc',
    headerText: i18n.t('SAP同步信息'),
    allowEditing: false
  },
  {
    width: '130',
    field: 'forecastArriveTime',
    headerText: i18n.t('预计到货日期'),
    template: timeDate({ dataKey: 'forecastArriveTime', isDateTime: true })
  },
  {
    width: '150',
    field: 'screenIdCount',
    headerText: i18n.t('屏ID'),
    cellTools: [
      {
        id: 'screenIdImport',
        title: i18n.t('上载屏ID'),
        // visibleCondition: (data) => data['status']?.value == 2,
        visibleCondition: (data) => data.status.value != 3 // 已完成
      },
      {
        id: 'search',
        title: i18n.t('查看')
      }
    ],
    cssClass: 'field-content-file-manage',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        let str = i18n.t('已上传数')
        if (e) {
          return `${str}：${e}`
        } else {
          return `${str}：0`
        }
      }
    }
  },
  {
    width: '150',
    field: 'supInvoiceCount',
    headerText: i18n.t('供应商发票/装箱单'),
    // cellTools: [
    //   {
    //     id: 'sup_invoice',
    //     title: i18n.t('附件')
    //   }
    // ],
    // cssClass: 'field-content-file-manage',
    // valueConverter: {
    //   type: 'function',
    //   filter: (e) => {
    //     let str = i18n.t('已上传数')
    //     if (e) {
    //       return `${str}：${e}`
    //     } else {
    //       return `${str}：0`
    //     }
    //   }
    // }
    template: () => {
      return {
        template: Vue.component('supInvoiceCount', {
          template: `
                <div class="headers">
                  <p style="color: #2783fe; cursor: pointer;" @click.stop="handleClickCellTool">
                    <span>{{ $t('已上传数') }}</span>:
                    <span>{{data.supInvoiceCount || 0}}</span>
                  </p>
                  <span class="e-headertext">{{ data.supInvoiceName }}</span>
                </div>
              `,
          methods: {
            handleClickCellTool() {
              const params = {
                data: this.data,
                tool: {
                  id: 'sup_invoice'
                }
              }
              this.$parent.$emit('handleClickCellTool', params)
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'hkInvoiceCount',
    headerText: i18n.t('香港电子发票/装箱单'),
    // cellTools: [
    //   {
    //     id: 'hk_invoice',
    //     title: i18n.t('附件')
    //   }
    // ],
    // cssClass: 'field-content-file-manage',
    // valueConverter: {
    //   type: 'function',
    //   filter: (e) => {
    //     let str = i18n.t('已上传数')
    //     if (e) {
    //       return `${str}：${e}`
    //     } else {
    //       return `${str}：0`
    //     }
    //   }
    // }
    template: () => {
      return {
        template: Vue.component('hkInvoiceCount', {
          template: `
                <div class="headers">
                  <p style="color: #2783fe; cursor: pointer;" @click.stop="handleClickCellTool">
                    <span>{{ $t('已上传数') }}</span>:
                    <span>{{data.hkInvoiceCount || 0}}</span>
                  </p>
                  <span class="e-headertext">{{ data.hkInvoiceName }}</span>
                </div>
              `,
          methods: {
            handleClickCellTool() {
              const params = {
                data: this.data,
                tool: {
                  id: 'hk_invoice'
                }
              }
              this.$parent.$emit('handleClickCellTool', params)
            }
          }
        })
      }
    }
  },
  {
    width: '120',
    field: 'ihNumber',
    headerText: i18n.t('IH号')
  },
  {
    width: '100',
    field: 'billOfLadingCount',
    headerText: i18n.t('提单'),
    // cellTools: [
    //   {
    //     id: 'bill_of_lading',
    //     title: i18n.t('附件')
    //   }
    // ],
    // cssClass: 'field-content-file-manage',
    // valueConverter: {
    //   type: 'function',
    //   filter: (e) => {
    //     let str = i18n.t('已上传数')
    //     if (e) {
    //       return `${str}：${e}`
    //     } else {
    //       return `${str}：0`
    //     }
    //   }
    // }
    template: () => {
      return {
        template: Vue.component('billOfLadingCount', {
          template: `
                <div class="headers">
                  <p style="color: #2783fe; cursor: pointer;" @click.stop="handleClickCellTool">
                    <span>{{ $t('已上传数') }}</span>:
                    <span>{{data.billOfLadingCount || 0}}</span>
                  </p>
                  <span class="e-headertext">{{ data.billOfLadingName }}</span>
                </div>
              `,
          methods: {
            handleClickCellTool() {
              const params = {
                data: this.data,
                tool: {
                  id: 'bill_of_lading'
                }
              }
              this.$parent.$emit('handleClickCellTool', params)
            }
          }
        })
      }
    }
  },
  {
    width: '120',
    field: 'saleOrderCount',
    headerText: i18n.t('销售订单'),
    // cellTools: [
    //   {
    //     id: 'sale_order',
    //     title: i18n.t('附件')
    //   }
    // ],
    // cssClass: 'field-content-file-manage',
    // valueConverter: {
    //   type: 'function',
    //   filter: (e) => {
    //     let str = i18n.t('已上传数')
    //     if (e) {
    //       return `${str}：${e}`
    //     } else {
    //       return `${str}：0`
    //     }
    //   }
    // }
    template: () => {
      return {
        template: Vue.component('saleOrderCount', {
          template: `
                <div class="headers">
                  <p style="color: #2783fe; cursor: pointer;" @click.stop="handleClickCellTool">
                    <span>{{ $t('已上传数') }}</span>:
                    <span>{{data.saleOrderCount || 0}}</span>
                  </p>
                  <span class="e-headertext">{{ data.saleOrderName }}</span>
                </div>
              `,
          methods: {
            handleClickCellTool() {
              const params = {
                data: this.data,
                tool: {
                  id: 'sale_order'
                }
              }
              this.$parent.$emit('handleClickCellTool', params)
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'deliveryScanningCount',
    headerText: i18n.t('送货单扫描件'),
    // cellTools: [
    //   {
    //     id: 'delivery_scanning',
    //     title: i18n.t('附件')
    //   }
    // ],
    // cssClass: 'field-content-file-manage',
    // valueConverter: {
    //   type: 'function',
    //   filter: (e) => {
    //     let str = i18n.t('已上传数')
    //     if (e) {
    //       return `${str}：${e}`
    //     } else {
    //       return `${str}：0`
    //     }
    //   }
    // }
    template: () => {
      return {
        template: Vue.component('deliveryScanningCount', {
          template: `
                <div class="headers">
                  <p style="color: #2783fe; cursor: pointer;" @click.stop="handleClickCellTool">
                    <span>{{ $t('已上传数') }}</span>:
                    <span>{{data.deliveryScanningCount || 0}}</span>
                  </p>
                  <span class="e-headertext">{{ data.deliveryScanningName }}</span>
                </div>
              `,
          methods: {
            handleClickCellTool() {
              const params = {
                data: this.data,
                tool: {
                  id: 'delivery_scanning'
                }
              }
              this.$parent.$emit('handleClickCellTool', params)
            }
          }
        })
      }
    }
  },
  {
    width: '300',
    field: 'remark',
    headerText: i18n.t('备注'),
    ignore: true
  },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  }
]
export const columnData2 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false,
    allowEditing: false,
    allowResizing: false
  },
  {
    width: '200',
    field: 'deliveryCode',
    cellTools: [],
    headerText: i18n.t('送货单号')
  },
  {
    width: '150',
    field: 'deliveryLineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '90',
    field: 'statusDesc',
    headerText: i18n.t('状态')
  },
  {
    width: '100',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '200',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    ignore: true
  },
  {
    width: '123',
    field: 'deliveryQuantity',
    headerText: i18n.t('本次送货数量')
  },
  {
    width: '98',
    field: 'receiveQuantity',
    headerText: i18n.t('收货数量')
  },
  {
    field: 'cumReceiveQty',
    headerText: i18n.t('wms实收数量'),
    width: '120'
  },
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('采购订单号')
  },
  {
    width: '130',
    field: 'lineNo',
    headerText: i18n.t('采购订单行号')
  },
  {
    width: '100',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '200',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '135',
    field: 'warehouseName',
    headerText: i18n.t('库存地点'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.warehouseCode}}-{{data.warehouseName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '100',
    field: 'sendTime',
    headerText: i18n.t('发货日期'),
    template: timeDate({ dataKey: 'sendTime', isDateTime: true })
  },
  {
    field: 'onWayStatusDesc',
    headerText: i18n.t('在途状态')
  },
  {
    width: '75',
    field: 'takeNo',
    headerText: i18n.t('车牌号')
  },
  {
    field: 'vehicleLogistics',
    headerText: i18n.t('车辆物流'),
    template: () => {
      return {
        template: Vue.component('template-detail', {
          template: `
              <span v-if="data.takeNo" style="color: #6386c1; cursor: pointer;" @click="toLogistics(data)">{{ $t('查看物流') }}</span>`,
          methods: {
            toLogistics(e) {
              const params = {
                ztpno: e?.deliveryCode?.toString(),
                busCode: e?.forecastCode,
                busNum: e.takeNo
              }
              this.$API.receiptAndDelivery.purQueryVehicleLogistics(params).then((res) => {
                if (res.code === 200) {
                  window.open(res.data.mapURL)
                }
              })
            }
          }
        })
      }
    }
  },
  {
    width: '100',
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    width: '200',
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'associatedNo',
    headerText: i18n.t('关联单据编号')
  },
  {
    field: 'associateOuterDocNo',
    headerText: i18n.t('来源单号')
  },
  {
    field: 'doNo',
    headerText: i18n.t('来源单号2')
  },
  {
    field: 'virtualSupplierCode',
    headerText: i18n.t('虚拟供应商代码'),
    width: '140'
  },
  {
    width: '85',
    field: 'jit',
    headerText: i18n.t('是否JIT'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },
  {
    width: '150',
    field: 'jitDeliveryNumber',
    headerText: i18n.t('JIT编号')
  },
  {
    width: '100',
    field: 'demandDate',
    headerText: i18n.t('需求日期')
  },
  {
    width: '100',
    field: 'rejectQuantity',
    headerText: i18n.t('拒绝数量')
  },
  {
    width: '100',
    field: 'rejectReason',
    headerText: i18n.t('拒绝原因')
  },
  {
    field: 'inputDate',
    headerText: i18n.t('凭证创建日期'),
    template: timeDate({ dataKey: 'inputDate', isDateTime: true })
  },
  {
    field: 'postingDate',
    headerText: i18n.t('过账日期'),
    template: timeDate({ dataKey: 'postingDate', isDate: true })
  },
  {
    width: '100',
    field: 'deliveryTypeDesc',
    headerText: i18n.t('送货单类型')
  },
  {
    width: '100',
    field: 'sendAddressName',
    headerText: i18n.t('发货地点')
  },
  {
    width: '130',
    field: 'thirdTenantCode',
    headerText: i18n.t('第三方物流商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.thirdTenantCode}}-{{data.thirdTenantName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '110',
    field: 'receiveSupplierName',
    headerText: i18n.t('收货供应商')
  },
  {
    field: 'deliveryRemark',
    headerText: i18n.t('送货单备注'),
    width: '120'
  },
  {
    width: '90',
    field: 'transferPlanName',
    headerText: i18n.t('计划员')
  },
  {
    width: '175',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '100',
    field: 'workOrderNo',
    headerText: i18n.t('关联工单号')
  },
  {
    width: '140',
    field: 'saleOrderNo',
    headerText: i18n.t('关联销售订单号')
  },
  {
    width: '150',
    field: 'saleOrderLineNo',
    headerText: i18n.t('关联销售订单行号')
  },
  {
    width: '85',
    field: 'bomCode',
    headerText: i18n.t('BOM号')
  },
  {
    width: '120',
    field: 'productCode',
    headerText: i18n.t('关联产品代码')
  },
  {
    width: '100',
    field: 'workCenterName',
    headerText: i18n.t('工作中心')
  },
  {
    field: 'rbcCode',
    headerText: i18n.t('RBC编码'),
    width: '100'
  },
  {
    field: 'bgType',
    headerText: i18n.t('BG类型'),
    width: '100'
  },
  {
    width: '100',
    field: 'processName',
    headerText: i18n.t('工序名称')
  },
  {
    width: '100',
    field: 'receiveAddressName',
    headerText: i18n.t('收货地址')
  },
  {
    width: '80',
    field: 'unitName',
    headerText: i18n.t('单位')
  },
  {
    width: '150',
    field: 'batchCode',
    headerText: i18n.t('批次号')
  },
  {
    width: '100',
    field: 'limitQuantity',
    headerText: i18n.t('限量数量')
  },
  {
    width: '86',
    field: 'warehouseClerkName',
    headerText: i18n.t('仓管员')
  },
  {
    width: '86',
    field: 'dispatcherName',
    headerText: i18n.t('调度员')
  },
  {
    field: 'wmsInstockQty',
    headerText: i18n.t('WMS入库数量'),
    width: '120'
  },
  {
    field: 'wmsInstockTime',
    headerText: i18n.t('WMS入库时间'),
    width: '120'
  },
  {
    width: '86',
    field: 'cancelPersonName',
    headerText: i18n.t('取消人')
  },
  {
    width: '95',
    field: 'cancelTime',
    headerText: i18n.t('取消时间'),
    template: timeDate({ dataKey: 'cancelTime', isDateTime: true })
  },
  {
    width: '86',
    field: 'closePersonName',
    headerText: i18n.t('关闭人')
  },
  {
    width: '95',
    field: 'closeTime',
    headerText: i18n.t('关闭时间'),
    template: timeDate({ dataKey: 'closeTime', isDateTime: true })
  },
  {
    width: '95',
    field: 'collectorMark',
    headerText: i18n.t('代收标识')
  },
  {
    width: '86',
    field: 'collectorName',
    headerText: i18n.t('代收人')
  },
  {
    width: '86',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'headRemark',
    headerText: i18n.t('头备注'),
    width: '100'
  },
  {
    width: '86',
    field: 'remark',
    headerText: i18n.t('行备注')
  },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  }
]

// 1-新建,2-发货中,3-已完成,4-已取消,5-已关闭
export const StatusOptions = [
  { text: i18n.t('送货中'), value: 2, cssClass: '' },
  { text: i18n.t('已完成'), value: 3, cssClass: '' },
  { text: i18n.t('已取消'), value: 4, cssClass: '' },
  { text: i18n.t('已关闭'), value: 5, cssClass: '' }
]

export const onWayStatusOptions = [
  { text: i18n.t('未出发'), value: 0, cssClass: '' },
  { text: i18n.t('已入园'), value: 1, cssClass: '' },
  { text: i18n.t('已出发'), value: 2, cssClass: '' },
  { text: i18n.t('已报到'), value: 3, cssClass: '' },
  { text: i18n.t('已取消'), value: 4, cssClass: '' },
  { text: i18n.t('已关闭'), value: 5, cssClass: '' }
]

export const DeliveryTypeOptions = [
  { value: 1, text: i18n.t('采购订单'), cssClass: '' },
  { value: 2, text: i18n.t('交货计划'), cssClass: '' },
  { value: 3, text: i18n.t('JIT'), cssClass: '' },
  { value: 4, text: i18n.t('无需求'), cssClass: '' },
  { value: 5, text: i18n.t('vmi'), cssClass: '' },
  { value: 6, text: i18n.t('钢材'), cssClass: '' }
]
export const columnData3 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false,
    allowEditing: false,
    allowResizing: false
  },
  {
    width: '200',
    field: 'deliveryCode',
    cellTools: [],
    headerText: i18n.t('送货单号')
  },
  {
    width: '150',
    field: 'deliveryLineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '90',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: StatusOptions
    }
  },
  {
    width: '100',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '200',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    ignore: true
  },
  {
    width: '123',
    field: 'deliveryQuantity',
    headerText: i18n.t('本次送货数量')
  },
  {
    width: '98',
    field: 'receiveQuantity',
    headerText: i18n.t('收货数量')
  },
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('采购订单号')
  },
  {
    width: '130',
    field: 'lineNo',
    headerText: i18n.t('采购订单行号')
  },
  {
    width: '100',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '200',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '135',
    field: 'warehouseName',
    headerText: i18n.t('库存地点'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.warehouseCode}}-{{data.warehouseName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '100',
    field: 'sendTime',
    headerText: i18n.t('发货日期'),
    template: timeDate({ dataKey: 'sendTime', isDateTime: true })
  },
  {
    field: 'onWayStatus',
    headerText: i18n.t('在途状态'),
    valueConverter: {
      type: 'map',
      map: onWayStatusOptions
    }
  },
  {
    width: '75',
    field: 'carNo',
    headerText: i18n.t('车牌号')
  },
  {
    field: 'vehicleLogistics',
    headerText: i18n.t('车辆物流'),
    template: () => {
      return {
        template: Vue.component('template-detail', {
          template: `
              <span v-if="data.carNo" style="color: #6386c1; cursor: pointer;" @click="toLogistics(data)">{{ $t('查看物流') }}</span>`,
          methods: {
            toLogistics(e) {
              const params = {
                ztpno: e?.deliveryCode?.toString(),
                busCode: e?.forecastCode,
                busNum: e.carNo
              }
              this.$API.receiptAndDelivery.purQueryVehicleLogistics(params).then((res) => {
                if (res.code === 200) {
                  window.open(res.data.mapURL)
                }
              })
            }
          }
        })
      }
    }
  },
  {
    width: '100',
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    width: '200',
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'associatedNo',
    headerText: i18n.t('关联单据编号')
  },
  {
    field: 'associateOuterDocNo',
    headerText: i18n.t('来源单号')
  },
  {
    field: 'doNo',
    headerText: i18n.t('来源单号2')
  },
  {
    field: 'virtualSupplierCode',
    headerText: i18n.t('虚拟供应商代码'),
    width: '140'
  },
  {
    width: '85',
    field: 'jit',
    headerText: i18n.t('是否JIT'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },
  {
    width: '150',
    field: 'jitDeliveryNumber',
    headerText: i18n.t('JIT编号')
  },
  {
    width: '100',
    field: 'demandDate',
    headerText: i18n.t('需求日期')
  },
  {
    width: '100',
    field: 'rejectQuantity',
    headerText: i18n.t('拒绝数量')
  },
  {
    width: '100',
    field: 'rejectReason',
    headerText: i18n.t('拒绝原因')
  },
  {
    field: 'inputDate',
    headerText: i18n.t('凭证创建日期'),
    template: timeDate({ dataKey: 'inputDate', isDateTime: true })
  },
  {
    field: 'postingDate',
    headerText: i18n.t('过账日期'),
    template: timeDate({ dataKey: 'postingDate', isDate: true })
  },
  {
    width: '100',
    field: 'deliveryType',
    headerText: i18n.t('送货单类型'),
    valueConverter: {
      type: 'map',
      map: DeliveryTypeOptions
    }
  },
  {
    width: '100',
    field: 'sendAddressName',
    headerText: i18n.t('发货地点')
  },
  {
    width: '130',
    field: 'thirdTenantCode',
    headerText: i18n.t('第三方物流商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.thirdTenantCode}}-{{data.thirdTenantName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '110',
    field: 'receiveSupplierName',
    headerText: i18n.t('收货供应商')
  },
  {
    field: 'deliveryRemark',
    headerText: i18n.t('送货单备注'),
    width: '120'
  },
  {
    width: '90',
    field: 'transferPlanName',
    headerText: i18n.t('计划员')
  },
  {
    width: '175',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '100',
    field: 'workOrderNo',
    headerText: i18n.t('关联工单号')
  },
  {
    width: '140',
    field: 'saleOrderNo',
    headerText: i18n.t('关联销售订单号')
  },
  {
    width: '150',
    field: 'saleOrderLineNo',
    headerText: i18n.t('关联销售订单行号')
  },
  {
    width: '85',
    field: 'bomCode',
    headerText: i18n.t('BOM号')
  },
  {
    width: '120',
    field: 'productCode',
    headerText: i18n.t('关联产品代码')
  },
  {
    width: '100',
    field: 'workCenterName',
    headerText: i18n.t('工作中心')
  },
  {
    field: 'rbcCode',
    headerText: i18n.t('RBC编码'),
    width: '100'
  },
  {
    field: 'bgType',
    headerText: i18n.t('BG类型'),
    width: '100'
  },
  {
    width: '100',
    field: 'processName',
    headerText: i18n.t('工序名称')
  },
  {
    width: '100',
    field: 'receiveAddressName',
    headerText: i18n.t('收货地址')
  },
  {
    width: '80',
    field: 'unitName',
    headerText: i18n.t('单位')
  },
  {
    width: '150',
    field: 'batchCode',
    headerText: i18n.t('批次号')
  },
  {
    width: '100',
    field: 'limitQuantity',
    headerText: i18n.t('限量数量')
  },
  {
    width: '86',
    field: 'warehouseClerkName',
    headerText: i18n.t('仓管员')
  },
  {
    width: '86',
    field: 'dispatcherName',
    headerText: i18n.t('调度员')
  },
  {
    field: 'wmsInstockQty',
    headerText: i18n.t('WMS入库数量'),
    width: '120'
  },
  {
    field: 'wmsInstockTime',
    headerText: i18n.t('WMS入库时间'),
    width: '120'
  },
  {
    width: '86',
    field: 'cancelPersonName',
    headerText: i18n.t('取消人')
  },
  {
    width: '95',
    field: 'cancelTime',
    headerText: i18n.t('取消时间'),
    template: timeDate({ dataKey: 'cancelTime', isDateTime: true })
  },
  {
    width: '86',
    field: 'closePersonName',
    headerText: i18n.t('关闭人')
  },
  {
    width: '95',
    field: 'closeTime',
    headerText: i18n.t('关闭时间'),
    template: timeDate({ dataKey: 'closeTime', isDateTime: true })
  },
  {
    width: '95',
    field: 'collectorMark',
    headerText: i18n.t('代收标识')
  },
  {
    width: '86',
    field: 'collectorName',
    headerText: i18n.t('代收人')
  },
  {
    width: '86',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'headRemark',
    headerText: i18n.t('头备注'),
    width: '100'
  },
  {
    width: '86',
    field: 'remark',
    headerText: i18n.t('行备注')
  },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  }
]
