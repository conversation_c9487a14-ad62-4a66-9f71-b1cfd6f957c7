<template>
  <div style="height: 100%">
    <mt-template-page
      ref="templateRef1"
      :permission-obj="permissionObj"
      :template-config="pageConfig1"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle1"
      @handleCustomReset="handleSearchReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="createTime" :label="$t('创建日期')">
              <mt-date-range-picker
                v-model="searchFormModel.createTime"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'createTime')"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-multi-select
                v-model="searchFormModel.status"
                type="multipleChoice"
                :placeholder="$t('请选择状态')"
                :show-select-all="true"
                :show-clear-button="true"
                :data-source="statusOptions"
                :fields="{ text: 'text', value: 'value' }"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('公司代码')" label-style="top">
              <debounce-filter-select
                v-model="searchFormModel.companyCode"
                :request="getCompany"
                :data-source="companyOptions"
                :fields="{ text: 'theCodeName', value: 'orgCode' }"
                :value-template="companyCodeValueTemplate"
                :show-clear-button="true"
                :allow-filtering="true"
                @change="companyCodeChange"
                :placeholder="$t('请选择')"
              ></debounce-filter-select>
            </mt-form-item>
            <mt-form-item prop="deliveryCode" :label="$t('送货单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.deliveryCode"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="siteCode" :label="$t('工厂代码')" label-style="top">
              <debounce-filter-select
                v-model="searchFormModel.siteCode"
                :request="postSiteFuzzyQuery"
                :data-source="siteOptions"
                :show-clear-button="true"
                :fields="{ text: 'theCodeName', value: 'siteCode' }"
                :value-template="siteCodeValueTemplate"
                @change="siteCodeChange"
                :placeholder="$t('请选择')"
              ></debounce-filter-select>
            </mt-form-item>
            <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.itemCode"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.itemName"
                :placeholder="$t('支持模糊搜索')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="orderNo" :label="$t('采购订单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.orderNo"
                :placeholder="$t('支持粘贴多个精准查询')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="lineNo" :label="$t('采购订单行号')" label-style="top">
              <mt-input
                v-model="searchFormModel.lineNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="onWayStatus" :label="$t('在途状态')" label-style="top">
              <mt-multi-select
                v-model="searchFormModel.onWayStatus"
                type="multipleChoice"
                :placeholder="$t('请选择')"
                :show-select-all="true"
                :show-clear-button="true"
                :data-source="onWayStatusOptions"
                :fields="{ text: 'text', value: 'value' }"
              />
            </mt-form-item>
            <mt-form-item prop="buyerOrgCode" :label="$t('采购组')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.buyerOrgCode"
                :url="$API.masterData.getBusinessGroupAuthFuzzyUrl"
                multiple
                :placeholder="$t('请选择采购组')"
                :fields="{ text: 'groupName', value: 'groupCode' }"
                records-position="data"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="workCenterName" :label="$t('工作中心')" label-style="top">
              <mt-input
                v-model="searchFormModel.workCenterName"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="saleOrderNo" :label="$t('销售订单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.saleOrderNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="sendTime" :label="$t('发货日期')">
              <mt-date-range-picker
                v-model="searchFormModel.sendTime"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'sendTime')"
              />
            </mt-form-item>
            <mt-form-item prop="demandDate" :label="$t('需求日期')">
              <mt-date-range-picker
                v-model="searchFormModel.demandDate"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'demandDate')"
              />
            </mt-form-item>
            <mt-form-item prop="associatedNo" :label="$t('关联单据编号')">
              <mt-input
                v-model="searchFormModel.associatedNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="associateOuterDocNo" :label="$t('来源单号')">
              <mt-input
                v-model="searchFormModel.associateOuterDocNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="doNo" :label="$t('来源单号2')">
              <mt-input
                v-model="searchFormModel.doNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="postingDate" :label="$t('过账日期')">
              <mt-date-range-picker
                v-model="searchFormModel.postingDate"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'postingDate')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
import { columnData2 } from '../config/index'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName, codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  components: {
    debounceFilterSelect: () => import('@/components/debounceFilterSelect/index.vue')
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    const startDate = dayjs().subtract(3, 'month')
    return {
      searchFormModel: {
        status: [],
        createTime: [new Date(startDate), new Date()],
        createTimeE: this.getUnix(new Date(dayjs(new Date()).format('YYYY-MM-DD 23:59:59'))),
        createTimeS: this.getUnix(
          new Date(dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00'))
        )
      },
      supplierOptions: [], // 供应商下拉选项
      siteOptions: [], // 工厂 下列选项
      supplierCodeValueTemplate: codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      }),
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      companyCodeValueTemplate: codeNameColumn({
        firstKey: 'orgCode',
        secondKey: 'orgName'
      }),
      itemCodeValueTemplate: codeNameColumn({
        firstKey: 'itemCode',
        secondKey: 'itemName'
      }),
      itemCodeOptions: [], // 物料下拉
      companyOptions: [], // 公司 下拉选项
      // 1-新建,2-发货中,3-已完成,4-已取消,5-已关闭
      statusOptions: [
        { text: this.$t('送货中'), value: 2 },
        { text: this.$t('已完成'), value: 3 },
        { text: this.$t('已取消'), value: 4 },
        { text: this.$t('已关闭'), value: 5 }
      ],
      onWayStatusOptions: [
        { text: i18n.t('未出发'), value: 0 },
        { text: i18n.t('已入园'), value: 1 },
        { text: i18n.t('已出发'), value: 2 },
        { text: i18n.t('已报到'), value: 3 },
        { text: i18n.t('已取消'), value: 4 },
        { text: i18n.t('已关闭'), value: 5 }
      ],

      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'List', permissionCode: 'T_02_0163' },
          { dataPermission: 'Details', permissionCode: 'T_02_0164' }
        ]
      },
      pageConfig1: [
        {
          dataPermission: 'Details',
          activatedRefresh: false,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          showArchive: true,
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                {
                  id: 'export',
                  icon: 'icon_solid_Import',
                  permission: ['O_02_1373'],
                  title: this.$t('导出')
                },
                {
                  id: 'Copy',
                  icon: 'icon_solid_Import',
                  title: i18n.t('复制送货单号')
                }
              ],
              ['Refresh', 'Setting']
            ]
          },
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          gridId: '138844d5-9c34-452c-a2c6-0c6c160252f3',
          grid: {
            // virtualPageSize: 30,
            // showSelected: true,
            // selectionSettings: {
            //   persistSelection: true,
            //   type: 'Multiple',
            //   checkboxOnly: true
            // },
            // enableVirtualization: true,
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [10, 20, 50, 100, 200, 500, 1000],
              totalRecordsCount: 0
            },
            lineIndex: 1, // 序号列
            columnData: columnData2,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/delivery/item/supplier/page` // 采方送货单-采方查询送货单明细分页
            }
          }
        }
      ]
    }
  },
  mounted() {
    if (this.$route.query.from === 'mytodo') {
      this.tabIndex = 1
      this.pageConfig1[0].grid.asyncConfig.url = `${BASE_TENANT}/delivery/item/supplier/page?fromWorkCenter=1`
      this.pageConfig1[0].grid.asyncConfig.defaultRules = JSON.parse(
        sessionStorage.getItem('todoDetail')
      ).defaultRules
    }
  },
  methods: {
    dataTimeChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'S'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[flag + 'E'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[flag + 'S'] = null
        this.searchFormModel[flag + 'E'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).unix()
    },
    handleClickCellTitle1(e) {
      if (e.field === 'deliveryCode') {
        this.getDeliverDataById(e.data?.deliveryId)
      }
    },
    // 根据id查询主单数据 - 获取物料信息并跳转物料页面
    getDeliverDataById(id) {
      let params = { id }
      this.$API.receiptAndDelivery
        .getSupplierDeliveyDataTV(params)
        .then((res) => {
          if (res?.code == 200) {
            // 跳转详情页

            localStorage.setItem('deliverDetailSupplierTV', JSON.stringify(res.data))
            this.$router.push(
              `deliver-detail-supplier-tv?id=${res?.data?.id}&type=${
                res?.data?.deliveryType
              }&timeStamp=${new Date().getTime()}`
            )
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 获取主数据-获取指定组织下指定组织层级节点列表 物料信息
    getItemCode(args) {
      const { text, updateData, setSelectData } = args
      let params = {
        keyword: text || this.searchFormModel.itemCode || '',
        pageSize: 50
      }
      this.$API.masterData
        .getItemByKeyword(params)
        .then((res) => {
          if (res) {
            const list = res.data?.records || []
            this.itemCodeOptions = addCodeNameKeyInList({
              firstKey: 'itemCode',
              secondKey: 'itemName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.itemCodeOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-获取指定组织下指定组织层级节点列表 业务公司
    getCompany(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true,
        organizationIds: []
      }
      this.$API.masterData
        .OrgFindSpecifiedChildrenLevelOrgs(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.companyOptions = addCodeNameKeyInList({
              firstKey: 'orgCode',
              secondKey: 'orgName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.companyOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        page: {
          current: 1,
          pages: 0,
          size: 20
        }
      }
      this.$API.receiptAndDelivery
        .getFactoryInfo(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 公司 change
    companyCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.companyId = itemData.id
        this.searchFormModel.companyCode = itemData.orgCode
        this.searchFormModel.companyName = itemData.orgName
      } else {
        this.searchFormModel.companyId = ''
        this.searchFormModel.companyCode = ''
        this.searchFormModel.companyName = ''
      }
    },
    // 物料 change
    itemCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.itemCode = itemData.itemCode
        this.searchFormModel.itemName = itemData.itemName
      } else {
        this.searchFormModel.itemCode = ''
        this.searchFormModel.itemName = ''
      }
    },
    // 工厂 change
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        // this.searchFormModel.siteId = itemData.id;
        this.searchFormModel.siteCode = itemData.siteCode
        this.searchFormModel.siteName = itemData.siteName
      } else {
        // this.searchFormModel.siteId = "";
        this.searchFormModel.siteCode = ''
        this.searchFormModel.siteName = ''
      }
    },
    // ToolBar
    handleClickToolBar(args) {
      // if (args.toolbar.id === 'resetDataByLocal') return
      const { toolbar, grid } = args
      const selectedRecords = grid.getSelectedRecords()
      if (toolbar.id == 'export') {
        let obj = JSON.parse(sessionStorage.getItem(this.pageConfig1[0].gridId))?.visibleCols
        const headerMap = {}
        if (obj !== undefined && obj.length) {
          obj?.forEach((i) => {
            if (i.field !== 'customChecked' && i.field !== 'id') {
              headerMap[i.field] = i.headerText
            }
          })
        } else {
          this.pageConfig1[0].grid.columnData?.forEach((i) => {
            if (i.field !== 'customChecked' && i.field !== 'id') {
              headerMap[i.field] = i.headerText
            }
          })
        }
        const params = {
          page: { current: 1, size: 10000 },
          ...this.searchFormModel,
          headerMap
        } // 筛选条件
        this.apiStartLoading()
        this.$API.receiptAndDelivery
          .supplierOrderDeliveryQueryExportNewTv(params)
          .then((res) => {
            this.apiEndLoading()
            const fileName = getHeadersFileName(res)
            download({ fileName: `${fileName}`, blob: res.data })
          })
          .catch(() => {
            this.apiEndLoading()
          })
          .finally(() => {
            this.apiEndLoading()
          })
        return
      } else if (toolbar.id == 'Copy') {
        this.handleCopy(selectedRecords)
      }
    },
    handleCopy(selectedRecords) {
      const deliveryCodeList = selectedRecords.map((i) => {
        return i.deliveryCode
      })
      const str = [...new Set(deliveryCodeList)].join(' ')
      this.copyToClipboard(str)
      // this.$refs.templateRef1.$parent.$parent.currentTabIndex = 0
      // sessionStorage.setItem('copyDeliveryCodeList', str)
      this.$refs.templateRef1.$parent.$parent.searchFormModel.deliveryCode = str
      this.$refs.templateRef1.$parent.$parent.handleSelectTab(0)
      // this.$refs.templateRef1.$parent.$parent.tabIndex = 0
    },
    copyToClipboard(str) {
      const el = document.createElement('textarea')
      el.value = str
      el.setAttribute('readonly', '')
      el.style.position = 'absolute'
      el.style.left = '-9999px'
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)
    },
    handleSearchReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'status') {
            this.searchFormModel[key] = []
          }
        }
      }
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style></style>
