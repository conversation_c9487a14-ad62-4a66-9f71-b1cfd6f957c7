<template>
  <mt-dialog ref="dialog" height="900" :header="header" @beforeClose="cancel">
    <div class="dialog-content">
      <mt-template-page
        ref="templatePage"
        :template-config="scoreFileConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleScoreCellTitle"
        @handleClickCellTool="handleScoreCellTool"
      />
      <!-- <mt-common-uploader
        ref="uploader"
        class="common-uploader"
        :save-url="saveUrl"
        :is-single-file="false"
        type="line"
        v-model="fileList"
        @confirm="addFile"
      ></mt-common-uploader> -->
      <uploader-dialog
        @change="fileChange"
        @confirm="setFile"
        ref="uploaderDialog"
      ></uploader-dialog>
    </div>
  </mt-dialog>
</template>
<script>
// import Vue from 'vue'
import { scoreFileConfig } from '../config/fileConfig'
import { download } from '@/utils/utils'

export default {
  components: {
    UploaderDialog: () => import('./uploaderDialog')
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    // const seft = this
    return {
      scoreFileConfig: scoreFileConfig(
        '/srm-purchase-execute/tenant/tv/files/query/list',
        this.modalData
      ),
      fileList: [],
      saveUrl: `/file/user/file/publicUpload?useType=2&businessCode=${this.modalData.type}`, // 文件上传路径待
      downloadUrl: '/api/file/user/file/downloadPublicFile' //文件下载
    }
  },
  watch: {
    'modalData.id'(val) {
      if (val) {
        this.scoreFileConfig = scoreFileConfig(
          '/srm-purchase-execute/tenant/tv/files/query/list',
          this.modalData
        )
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    async addFile() {
      const fileList = this.fileList.map((item) => {
        item.sysFileId = item.id
        return item
      })
      fileList.forEach((item) => {
        item.docId = this.modalData.row.id
        item.docType = this.modalData.type
        item.supplierCode = this.modalData.row.supplierCode
        item.supplierName = this.modalData.row.supplierName
        delete item.id
      })
      const res = await this.$API.receiptAndDelivery.supDeliveryFileSave(fileList)
      if (res.code === 200) {
        this.$refs.templatePage.refreshCurrentGridData()
      }
    },
    handleClickToolBar(e) {
      if (e.toolbar.id === 'Add') {
        // this.$refs.uploader.$children[1].showFileBaseInfo()
        const dialogParams = {
          isView: false, //是否可上传
          required: false, // 是否必须
          uploadText: this.$t('请点击此处上传文件'),
          title: this.$t('附件'),
          saveUrl: this.saveUrl
        }
        this.$refs.uploaderDialog.dialogInit(dialogParams)
      }
    },
    // 行附件弹窗内文件变动
    fileChange(data) {
      console.log('fileChange', data)
      this.fileList = data
    },
    // 点击行附件上传的确认按钮
    setFile() {
      console.log('点击了确认')
      this.addFile()
    },
    handleScoreCellTitle(e) {
      if (e.field == 'fileName') {
        let params = {
          id: e?.data?.sysFileId || e?.data?.id,
          useType: 2
        }
        this.$API.fileService.getMtPreview(params).then((res) => {
          window.open(res.data)
        })
      }
    },
    handleScoreCellTool(e) {
      if (e.tool.id === 'delete') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除该数据？')
          },
          success: async () => {
            const params = {
              idList: [e.data.id]
            }
            const res = await this.$API.receiptAndDelivery.supDeliveryFileDelete(params)
            if (res.code === 200) {
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              this.$refs.templatePage.refreshCurrentGridData()
            }
          }
        })
      } else if (e.tool.id === 'download') {
        this.$store.commit('startLoading')
        this.$API.fileService.downloadPrivateFile({ id: e.data.sysFileId }).then((res) => {
          this.$store.commit('endLoading')
          download({
            fileName: e.data.fileName,
            blob: new Blob([res.data])
          })
        })
      }
    },
    confirm() {
      this.$emit('confirm-function', this.fileList)
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  margin: 16px 0;
  ::v-deep .toolbar-container {
    padding: 0 !important;
  }
  .common-uploader {
    display: none;
  }
}
</style>
