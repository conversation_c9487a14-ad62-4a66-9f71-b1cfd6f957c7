<template>
  <mt-dialog
    ref="dialog"
    width="80%"
    height="900"
    :header="header"
    :buttons="buttons"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-template-page
        ref="templatePage"
        :template-config="pageConfig"
        @handleCustomReset="handleCustomReset"
      >
        <template v-slot:quick-search-form>
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="diaphragmNo" :label="$t('膜片号')">
              <mt-input
                v-model="searchFormModel.diaphragmNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="boxNo" :label="$t('箱号')">
              <mt-input
                v-model="searchFormModel.boxNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="palletNo" :label="$t('栈板号')">
              <mt-input
                v-model="searchFormModel.palletNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="itemCode" :label="$t('物料编码')">
              <mt-input
                v-model="searchFormModel.itemCode"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="produceDate" :label="$t('生产日期')">
              <mt-input
                v-model="searchFormModel.produceDate"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商代码')">
              <mt-input
                v-model="searchFormModel.supplierCode"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="purchaseOrderNo" :label="$t('采购订单号')">
              <mt-input
                v-model="searchFormModel.purchaseOrderNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="doNo" :label="$t('DO单号')">
              <mt-input
                v-model="searchFormModel.doNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="doNo2" :label="$t('DO单号2')">
              <mt-input
                v-model="searchFormModel.doNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
          </mt-form>
        </template>
      </mt-template-page>
    </div>
  </mt-dialog>
</template>
<script>
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    // const seft = this
    return {
      searchFormModel: {},
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          isUseCustomSearch: true, // 是否使用自定义查询
          isCustomSearchRules: true,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: {
            tools: [[], ['Refresh', 'Setting']]
          },
          activatedRefresh: false,
          useCombinationSelection: false,
          grid: {
            columnData: [
              {
                field: 'deliveryCode',
                headerText: this.$t('送货单号')
              },
              {
                field: 'diaphragmNo',
                headerText: this.$t('膜片号')
              },
              {
                field: 'boxNo',
                headerText: this.$t('箱号')
              },
              {
                field: 'palletNo',
                headerText: this.$t('栈板号')
              },
              {
                field: 'itemCode',
                headerText: this.$t('物料编码')
              },
              {
                field: 'produceDate',
                headerText: this.$t('生产日期')
              },
              {
                field: 'supplierCode',
                headerText: this.$t('供应商代码')
              },
              {
                field: 'purchaseOrderNo',
                headerText: this.$t('采购订单号')
              },
              {
                field: 'doNo',
                headerText: this.$t('DO单号')
              },
              {
                field: 'doNo2',
                headerText: this.$t('DO单号2')
              },
              {
                field: 'level',
                headerText: this.$t('等级')
              },
              {
                field: 'versionNo',
                headerText: this.$t('版本号')
              }
            ],
            gridLines: 'Both',
            allowReordering: false,
            // allowPaging: false, // 不分页
            allowEditing: true, //开启表格编辑操作
            // height: 300,
            // virtualPageSize: 20,
            // enableVirtualization: true,
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/delivery/screen/list',
              // recordsPosition: 'data',
              params: {
                id: this.modalData.row.id
              }
            }
          }
        }
      ]
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    confirm() {
      this.$emit('confirm-function')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  margin: 16px 0;
  ::v-deep .toolbar-container {
    padding: 0 !important;
  }
}
</style>
