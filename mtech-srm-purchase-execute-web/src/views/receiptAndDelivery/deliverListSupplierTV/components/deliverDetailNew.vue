<template>
  <div style="height: 100%">
    <mt-template-page
      ref="templateRef1"
      :template-config="pageConfig1"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle1"
      @handleCustomReset="handleSearchReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="createTime" :label="$t('创建日期')">
              <mt-date-range-picker
                v-model="searchFormModel.createTime"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'createTime')"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-multi-select
                v-model="searchFormModel.status"
                type="multipleChoice"
                :placeholder="$t('请选择')"
                :show-select-all="true"
                :show-clear-button="true"
                :data-source="statusOptions"
                :fields="{ text: 'text', value: 'value' }"
              />
            </mt-form-item>
            <mt-form-item prop="companyCodes" :label="$t('公司')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.companyCodes"
                url="/masterDataManagement/auth/company/auth-fuzzy"
                multiple
                :placeholder="$t('请选择公司')"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                records-position="data"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="deliveryCode" :label="$t('送货单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.deliveryCode"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="siteCodes" :label="$t('工厂')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.siteCodes"
                :url="$API.masterData.getSiteAuthFuzzyUrl"
                multiple
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                params-key="fuzzyParam"
                records-position="data"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.itemCode"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.itemName"
                :placeholder="$t('支持模糊搜索')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="orderCode" :label="$t('采购订单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.orderCode"
                :placeholder="$t('支持粘贴多个精准查询')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="lineNo" :label="$t('采购订单行号')" label-style="top">
              <mt-input
                v-model="searchFormModel.lineNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="onWayStatus" :label="$t('在途状态')" label-style="top">
              <mt-multi-select
                v-model="searchFormModel.onWayStatus"
                :placeholder="$t('请选择')"
                :show-select-all="true"
                :show-clear-button="true"
                :data-source="onWayStatusOptions"
                :fields="{ text: 'text', value: 'value' }"
              />
            </mt-form-item>
            <mt-form-item prop="buyerOrgCodes" :label="$t('采购组')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.buyerOrgCodes"
                url="masterDataManagement/auth/business-group/auth-fuzzy"
                multiple
                :placeholder="$t('请选择采购组')"
                :fields="{ text: 'groupName', value: 'groupCode' }"
                params-key="fuzzyParam"
                records-position="data"
              ></RemoteAutocomplete>
            </mt-form-item>
            <!-- <mt-form-item prop="workCenterName" :label="$t('工作中心')" label-style="top">
              <mt-input
                v-model="searchFormModel.workCenterName"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item> -->
            <mt-form-item prop="saleOrderNo" :label="$t('销售订单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.saleOrderNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="sendTime" :label="$t('发货日期')">
              <mt-date-range-picker
                v-model="searchFormModel.sendTime"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'sendTime')"
              />
            </mt-form-item>
            <!-- <mt-form-item prop="demandDate" :label="$t('需求日期')">
              <mt-date-range-picker
                v-model="searchFormModel.demandDate"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'demandDate')"
              />
            </mt-form-item> -->
            <mt-form-item prop="associatedNo" :label="$t('关联单据编号')">
              <mt-input
                v-model="searchFormModel.associatedNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="associateOuterDocNo" :label="$t('来源单号')">
              <mt-input
                v-model="searchFormModel.associateOuterDocNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="doNo" :label="$t('来源单号2')">
              <mt-input
                v-model="searchFormModel.doNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="postingDate" :label="$t('过账日期')">
              <mt-date-range-picker
                v-model="searchFormModel.postingDate"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'postingDate')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { columnData3, StatusOptions, onWayStatusOptions } from '../config/index'
import { download, getHeadersFileName } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    const startDate = dayjs().subtract(3, 'month')
    return {
      statusOptions: StatusOptions,
      searchFormModel: {
        createTime: [new Date(startDate), new Date()],
        createTimeTo: this.getUnix(new Date(dayjs(new Date()).format('YYYY-MM-DD 23:59:59'))),
        createTimeFrom: this.getUnix(
          new Date(dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00'))
        )
      },
      onWayStatusOptions,
      pageConfig1: [
        {
          dataPermission: 'Details',
          showArchive: true, // 是否显示归档查询
          activatedRefresh: false,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                {
                  id: 'export1',
                  icon: 'icon_solid_Import',
                  // permission: ['O_02_1373'],
                  title: this.$t('导出')
                }
                // {
                //   id: 'Copy',
                //   icon: 'icon_solid_Import',
                //   title: i18n.t('复制送货单号')
                // }
              ],
              ['Refresh', 'Setting']
            ]
          },
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          gridId: 'd7e48602-1795-49d1-a617-cf0a81ded0dc',
          grid: {
            // virtualPageSize: 30,
            // showSelected: true,
            // selectionSettings: {
            //   persistSelection: true,
            //   type: 'Multiple',
            //   checkboxOnly: true
            // },
            // enableVirtualization: true,
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [10, 20, 50, 100, 200, 500, 1000],
              totalRecordsCount: 0
            },
            lineIndex: 1, // 序号列
            columnData: columnData3,
            dataSource: [],
            asyncConfig: {
              url: `/statistics/tenant/supplier/delivery/item/view/v1/page`, // 采方送货单-采方查询送货单明细分页
              params: {
                sourceFrom: 3,
                tenantType: 2
              },
              serializeList: (list) => {
                if (list.length > 0) {
                  list.forEach((item) => {
                    let inputDate = item.inputDate
                    if (item.inputDate && item.inputTime) {
                      inputDate = `${inputDate.split(' ')[0]} ${item.inputTime.split(' ')[1]}`
                    }
                    item.inputDate = inputDate
                  })
                }
                return list
              }
            }
          }
        }
      ]
    }
  },
  methods: {
    dataTimeChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'From'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[flag + 'To'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[flag + 'From'] = null
        this.searchFormModel[flag + 'To'] = null
      }
    },
    getUnix(val) {
      return new Date(val).getTime()
    },
    handleClickCellTitle1(e) {
      if (e.field === 'deliveryCode') {
        this.getDeliverDataById(e.data?.deliveryId)
      }
    },
    // 根据id查询主单数据 - 获取物料信息并跳转物料页面
    getDeliverDataById(id) {
      let params = { id }
      this.$API.receiptAndDelivery
        .getSupplierDeliveyDataTV(params)
        .then((res) => {
          if (res?.code == 200) {
            // 跳转详情页

            localStorage.setItem('deliverDetailSupplierTV', JSON.stringify(res.data))
            this.$router.push(
              `deliver-detail-supplier-tv?id=${res?.data?.id}&type=${
                res?.data?.deliveryType
              }&timeStamp=${new Date().getTime()}`
            )
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // ToolBar
    handleClickToolBar(args) {
      // if (args.toolbar.id === 'resetDataByLocal') return
      const { toolbar, grid } = args
      const selectedRecords = grid.getSelectedRecords()
      if (toolbar.id == 'export') {
        let obj = JSON.parse(sessionStorage.getItem(this.pageConfig1[0].gridId))?.visibleCols
        const headerMap = {}
        if (obj !== undefined && obj.length) {
          obj?.forEach((i) => {
            if (i.field !== 'customChecked' && i.field !== 'id') {
              headerMap[i.field] = i.headerText
            }
          })
        } else {
          this.pageConfig1[0].grid.columnData?.forEach((i) => {
            if (i.field !== 'customChecked' && i.field !== 'id') {
              headerMap[i.field] = i.headerText
            }
          })
        }
        const params = {
          page: { current: 1, size: 10000 },
          ...this.searchFormModel,
          headerMap
        } // 筛选条件
        this.apiStartLoading()
        this.$API.receiptAndDelivery
          .supplierOrderDeliveryQueryExportNewTv(params)
          .then((res) => {
            this.apiEndLoading()
            const fileName = getHeadersFileName(res)
            download({ fileName: `${fileName}`, blob: res.data })
          })
          .catch(() => {
            this.apiEndLoading()
          })
          .finally(() => {
            this.apiEndLoading()
          })
        return
      } else if (toolbar.id == 'export1') {
        let obj = JSON.parse(sessionStorage.getItem(this.pageConfig1[0].gridId))?.visibleCols
        const headerMap = {}
        if (obj !== undefined && obj.length) {
          obj?.forEach((i) => {
            if (i.field !== 'customChecked' && i.field !== 'id') {
              headerMap[i.field] = i.headerText
            }
          })
        } else {
          this.pageConfig1[0].grid.columnData?.forEach((i) => {
            if (i.field !== 'customChecked' && i.field !== 'id') {
              headerMap[i.field] = i.headerText
            }
          })
        }
        const params = {
          headerMap,
          page: { current: 1, size: 5000 },
          ...this.searchFormModel,
          sourceFrom: 3,
          tenantType: 2
        } // 筛选条件
        this.$store.commit('startLoading')
        this.$API.receiptAndDelivery.buyerOrderDeliveryQueryNewExport(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      } else if (toolbar.id == 'Copy') {
        this.handleCopy(selectedRecords)
      }
    },
    handleCopy(selectedRecords) {
      const deliveryCodeList = selectedRecords.map((i) => {
        return i.deliveryCode
      })
      const str = [...new Set(deliveryCodeList)].join(' ')
      this.copyToClipboard(str)
      // this.$refs.templateRef1.$parent.$parent.currentTabIndex = 0
      // sessionStorage.setItem('copyDeliveryCodeList', str)
      this.$refs.templateRef1.$parent.$parent.searchFormModel.deliveryCode = str
      this.$refs.templateRef1.$parent.$parent.handleSelectTab(0)
      // this.$refs.templateRef1.$parent.$parent.tabIndex = 0
    },
    copyToClipboard(str) {
      const el = document.createElement('textarea')
      el.value = str
      el.setAttribute('readonly', '')
      el.style.position = 'absolute'
      el.style.left = '-9999px'
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)
    },
    handleSearchReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'status') {
            this.searchFormModel[key] = []
          }
        }
      }
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style></style>
