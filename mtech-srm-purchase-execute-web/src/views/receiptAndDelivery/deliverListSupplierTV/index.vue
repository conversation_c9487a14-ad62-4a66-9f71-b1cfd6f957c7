<template>
  <!-- eslint-disable prettier/prettier -->
  <!-- 送货单-列表-供方 - 泛智屏 -->
  <div class="full-height vertical-flex-box">
    <mt-tabs
      :e-tab="false"
      :selected-item="activeTab"
      :data-source="tabSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <div style="height: calc(100% - 40px)">
      <mt-template-page
        class="templateRef"
        ref="templateRef"
        :template-config="pageConfig"
        :permission-obj="permissionObj"
        v-show="tabIndex === 0"
        @handleClickToolBar="handleClickToolBar"
        @handleCustomReset="handleSearchReset"
        @handleCustomSearch="handleSearchForm"
        @handleClickCellTitle="handleClickCellTitle"
        @handleClickCellTool="handleClickCellTool"
      >
        <template v-slot:quick-search-form>
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="deliveryCode" :label="$t('送货单号')">
              <mt-input
                v-model="searchFormModel.deliveryCode"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建日期')">
              <mt-date-range-picker
                v-model="searchFormModel.createTime"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'createTime')"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')">
              <mt-multi-select
                v-model="searchFormModel.status"
                type="multipleChoice"
                :placeholder="$t('请选择')"
                :show-select-all="true"
                :show-clear-button="true"
                :data-source="statusOptions"
                :fields="{ text: 'text', value: 'value' }"
              />
            </mt-form-item>
            <mt-form-item prop="siteCode" :label="$t('工厂')">
              <debounce-filter-select
                v-model="searchFormModel.siteCode"
                :request="postSiteFuzzyQuery"
                :data-source="siteOptions"
                :show-clear-button="true"
                :fields="{ text: 'theCodeName', value: 'siteCode' }"
                :value-template="siteCodeValueTemplate"
                @change="siteCodeChange"
                :placeholder="$t('请选择')"
              ></debounce-filter-select>
            </mt-form-item>
            <mt-form-item prop="printFlag" :label="$t('是否已打印')">
              <mt-select
                v-model="searchFormModel.printFlag"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                :data-source="isPrintOptions"
                :fields="{ text: 'text', value: 'value' }"
              />
            </mt-form-item>
            <mt-form-item prop="forecastFlag" :label="$t('是否可预约')">
              <mt-select
                v-model="searchFormModel.forecastFlag"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                :data-source="canDateOptions"
                :fields="{ text: 'text', value: 'value' }"
              />
            </mt-form-item>
            <mt-form-item prop="sendTime" :label="$t('发货日期')">
              <mt-date-range-picker
                v-model="searchFormModel.sendTime"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'sendTime')"
              />
            </mt-form-item>
            <mt-form-item prop="receiveAddressName" :label="$t('收货地址')">
              <mt-input
                v-model="searchFormModel.receiveAddressName"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="onWayStatus" :label="$t('在途状态')">
              <mt-multi-select
                v-model="searchFormModel.onWayStatus"
                type="multipleChoice"
                :placeholder="$t('请选择')"
                :show-select-all="true"
                :show-clear-button="true"
                :data-source="onWayStatusOptions"
                :fields="{ text: 'text', value: 'value' }"
              />
            </mt-form-item>
            <mt-form-item prop="carNo" :label="$t('车牌号')">
              <mt-input
                v-model="searchFormModel.carNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
              <mt-select
                v-model="searchFormModel.companyCode"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                :allow-filtering="true"
                :data-source="companyOptions"
                :fields="{ text: 'text', value: 'value' }"
                @change="companyCodeChange"
              />
            </mt-form-item>
            <mt-form-item :label="$t('采购组')">
              <debounce-filter-select
                v-model="searchFormModel.buyerOrgCode"
                :request="getBuyerOrgList"
                :data-source="buyerOrgOptions"
                :show-clear-button="true"
                :fields="{ text: 'theCodeName', value: 'groupCode' }"
                :value-template="buyerOrgValueTemplate"
                @change="buyerOrgCodeChange"
                :placeholder="$t('请选择')"
              />
            </mt-form-item>
            <mt-form-item prop="deliveryOrderType" :label="$t('送货单类型')">
              <mt-multi-select
                v-model="searchFormModel.deliveryOrderType"
                type="multipleChoice"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                :data-source="deliveryOrderTypeOptions"
                :fields="{ text: 'text', value: 'value' }"
              />
            </mt-form-item>
            <mt-form-item prop="outsourcedType" :label="$t('委外方式')">
              <mt-multi-select
                v-model="searchFormModel.outsourcedType"
                type="multipleChoice"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                :show-select-all="true"
                :data-source="outsourcedTypeOptions"
                :fields="{ text: 'text', value: 'value' }"
              />
            </mt-form-item>
            <mt-form-item prop="syncWmsStatus" :label="$t('WMS同步状态')">
              <mt-multi-select
                v-model="searchFormModel.syncWmsStatus"
                type="multipleChoice"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                :show-select-all="true"
                :data-source="syncStatusOptions"
                :fields="{ text: 'text', value: 'value' }"
              />
            </mt-form-item>
            <mt-form-item prop="syncSapStatus" :label="$t('SAP同步状态')">
              <mt-multi-select
                v-model="searchFormModel.syncSapStatus"
                type="multipleChoice"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                :show-select-all="true"
                :data-source="syncStatusOptions"
                :fields="{ text: 'text', value: 'value' }"
              />
            </mt-form-item>
            <mt-form-item prop="syncQmsStatus" :label="$t('QMS同步状态')">
              <mt-multi-select
                v-model="searchFormModel.syncQmsStatus"
                type="multipleChoice"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                :show-select-all="true"
                :data-source="syncStatusOptions"
                :fields="{ text: 'text', value: 'value' }"
              />
            </mt-form-item>
            <mt-form-item prop="associatedNo" :label="$t('关联单据编号')">
              <mt-input
                v-model="searchFormModel.associatedNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="associateOuterDocNo" :label="$t('来源单号')">
              <mt-input
                v-model="searchFormModel.associateOuterDocNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="doNo" :label="$t('来源单号2')">
              <mt-input
                v-model="searchFormModel.doNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
          </mt-form>
        </template>
      </mt-template-page>
      <!-- <mt-template-page
        :template-config="pageConfig1"
        :permission-obj="permissionObj"
        v-show="tabIndex === 1"
        ref="templateRef1"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle1"
        @handleClickCellTool="handleClickCellTool"
      /> -->
      <deliverDetail v-show="tabIndex === 1" />
      <deliverDetailNew v-show="tabIndex === 2" />
    </div>
    <!-- <mt-page
      v-if="tabIndex === 0"
      class="full-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totals"
      @currentChange="currentChange"
      @sizeChange="sizeChange"
    /> -->
    <delivery-dialog
      v-if="deliveryShow"
      :ids="id"
      :company-codes="companyCodes"
      ref="child"
      @handleAddDialogShow="handleAddDialogShow"
      @handleDialogShow="handleDialogShow"
    >
    </delivery-dialog>
  </div>
</template>

<script>
/* eslint-disable prettier/prettier */
import { i18n } from '@/main.js'

const mainTabList = [
  {
    title: i18n.t('送货单列表')
  },
  {
    title: i18n.t('送货单明细')
  },
  {
    title: i18n.t('送货单明细-新')
  }
]
import Vue from 'vue'
import { download, getHeadersFileName } from '@/utils/utils'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import { columnData } from './config/index.js'
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import dayjs from 'dayjs'

import { BASE_TENANT } from '@/utils/constant'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  components: {
    // deliveryDialog,
    deliverDetail: () => import('./components/deliverDetail.vue'),
    deliverDetailNew: () => import('./components/deliverDetailNew.vue'),
    DeliveryDialog: require('./components/deliveryDialog').default,
    DebounceFilterSelect
  },
  mounted() {
    this.getCompany()
    this.postSiteFuzzyQuery({})
    this.getBuyerOrgList({ text: '' })
    setTimeout(() => {
      this.$set(this.pageSettings, 'pageSize', 20)
    }, 500)
  },
  data() {
    const startDate = dayjs().subtract(1, 'month')
    return {
      activeTab: 0,
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 5,
        pageSizes: [10, 20, 50, 100, 200, 1000],
        totalRecordsCount: 0,
        totals: 0
      },
      siteOptions: [], // 工厂 下列选项
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      companyCodeValueTemplate: codeNameColumn({
        firstKey: 'customerCode',
        secondKey: 'customerName'
      }),
      companyOptions: [], // 公司 下拉选项
      buyerOrgOptions: [],
      buyerOrgValueTemplate: codeNameColumn({
        firstKey: 'groupCode',
        secondKey: 'groupName'
      }),
      statusOptions: [
        { value: 2, text: i18n.t('送货中') },
        { value: 3, text: i18n.t('已完成') },
        { value: 4, text: i18n.t('已取消') },
        { value: 5, text: i18n.t('已关闭') },
        { value: 6, text: i18n.t('部分收货') }
      ],
      onWayStatusOptions: [
        { text: i18n.t('未出发'), value: 0 },
        { text: i18n.t('已入园'), value: 1 },
        { text: i18n.t('已出发'), value: 2 },
        { text: i18n.t('已报到'), value: 3 },
        { text: i18n.t('已取消'), value: 4 },
        { text: i18n.t('已关闭'), value: 5 }
      ],
      isPrintOptions: [
        { text: this.$t('未打印'), value: 0 },
        { text: this.$t('已打印'), value: 1 }
      ],
      canDateOptions: [
        { text: this.$t('可预约'), value: 0 },
        { text: this.$t('不可预约'), value: 1 }
      ],
      deliveryOrderTypeOptions: [
        { text: this.$t('采购订单'), value: 1 },
        { text: this.$t('交货计划'), value: 2 },
        { text: this.$t('JIT'), value: 3 },
        { text: this.$t('无需求'), value: 4 },
        { text: this.$t('vmi'), value: 5 },
        { text: this.$t('钢材'), value: 6 },
        { text: this.$t('屏发货指导'), value: 7 },
        { text: this.$t('无PO送货'), value: 8 }
      ],
      outsourcedTypeOptions: [
        { text: this.$t('标准委外'), value: '0' },
        { text: this.$t('销售委外'), value: '1' },
        { text: this.$t('非委外'), value: '2' },
        { text: this.$t('工序委外'), value: '3' }
      ],
      syncStatusOptions: [
        { text: this.$t('未同步'), value: 0 },
        { text: this.$t('同步中'), value: 1 },
        { text: this.$t('同步成功'), value: 2 },
        { text: this.$t('同步失败'), value: 3 }
      ],
      id: [],
      searchFormModelDetail: {
        deliveryCode: ''
      },
      searchFormModel: {
        deliveryCode: null,
        companyName: null,
        status: null,
        siteCode: null,
        createTime: [new Date(startDate), new Date()],
        createTimeE: this.getUnix(new Date(dayjs(new Date()).format('YYYY-MM-DD 23:59:59'))),
        createTimeS: this.getUnix(
          new Date(dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00'))
        )
      },
      companyCodes: [],
      deliveryShow: false,
      index: 0,
      tabIndex: 0,

      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'List', permissionCode: 'T_02_0163' },
          { dataPermission: 'Details', permissionCode: 'T_02_0164' }
        ]
      },
      tabSource: mainTabList,
      pageConfig: [
        {
          dataPermission: 'List',
          showArchive: true, // 是否显示归档查询
          activatedRefresh: false,
          isUseCustomSearch: true,
          isCustomSearchHandle: true,
          isCustomSearchRules: true,
          // permissionCode: "T_02_0163",
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: false, // 不使用组件中的toolbar配置
          toolbar: {
            tools: [
              [
                {
                  id: 'MaintainIogisticsInformation',
                  icon: 'icon_table_MaintainIogisticsInformation',
                  title: this.$t('维护物流信息'),
                  permission: ['O_02_1380']
                },
                {
                  id: 'AppointmentDelivery1',
                  icon: 'icon_table_AppointmentDelivery1',
                  title: this.$t('预约送货'),
                  permission: ['O_02_1378']
                },
                {
                  id: 'print',
                  icon: 'icon_table_print',
                  permission: ['O_02_1374'],
                  title: this.$t('打印送货单')
                },
                {
                  id: 'synchronous',
                  icon: 'icon_table_restart',
                  permission: ['O_02_1641'],
                  title: this.$t('同步')
                },
                {
                  id: 'cancel',
                  icon: 'icon_table_cancel',
                  permission: ['O_02_1375'],
                  title: this.$t('取消')
                },
                // {
                //   id: 'new',
                //   icon: 'icon_table_new',
                //   title: this.$t('创建无订单送货单')
                //   // permission: ["O_02_1381"],
                // },
                // {
                //   id: 'loseeffectiveness',
                //   icon: 'icon_solid_loseeffectiveness',
                //   title: this.$t('补订单')
                //   // permission: ["O_02_1379"],
                // },
                {
                  id: 'import',
                  icon: 'icon_solid_Import',
                  permission: ['O_02_1640'],
                  title: this.$t('导入')
                },
                {
                  id: 'export',
                  icon: 'icon_solid_Import',
                  title: this.$t('导出')
                  // permission: ["O_02_1379"],
                },
                {
                  id: 'EditIHNumber',
                  icon: 'icon_table_restart',
                  title: i18n.t('编辑IH号')
                },
                {
                  id: 'BatchDownload',
                  icon: 'icon_table_restart',
                  title: i18n.t('批量下载附件')
                }
              ],
              ['Setting']
            ]
          },
          buttonQuantity: 8,
          gridId: '5a4860a4-3202-405c-8a09-dccc6f4eeec6',
          grid: {
            allowSorting: true,
            virtualPageSize: 30,
            showSelected: false,
            selectionSettings: {
              persistSelection: true,
              type: 'Multiple',
              checkboxOnly: true
            },
            asyncConfig: {
              url: `${BASE_TENANT}/delivery/header/supplier/list/page`
            },
            enableVirtualization: true,
            allowPaging: true,
            useToolTemplate: false, // 不使用预置(新增、编辑、删除)
            useBaseConfig: true, // 使用组件中的toolbar配置
            columnData: columnData,
            customSelection: true, // 使用自定义勾选列
            lineIndex: 0, // 序号列
            // dataSource: []
            // frozenColumns: 1,
            frozenColumns: 6
          }
        }
      ],
      userInfo: null,
      addDialogShow: false,
      dialogData: null
    }
  },
  activated() {
    this.handleSearchForm()
  },
  watch: {
    '$route.query.type': {
      handler() {
        if (this.$route.query?.type === '0') {
          this.tabIndex = 0
          this.activeTab = 0
        }
      },
      deep: true
    }
  },
  methods: {
    getBuyerOrgList(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        groupTypeCode: 'BG001CG'
      }
      this.$API.masterData
        .getbussinessGroup(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.buyerOrgOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.buyerOrgOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    buyerOrgCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.buyerOrgCode = itemData.groupCode
        this.searchFormModel.buyerOrgName = itemData.groupName
      } else {
        this.searchFormModel.buyerOrgCode = ''
        this.searchFormModel.buyerOrgName = ''
      }
    },
    dataTimeChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'S'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[flag + 'E'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[flag + 'S'] = null
        this.searchFormModel[flag + 'E'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).unix()
    },
    // 分页器 - 切换分页
    currentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.searchForm()
    },
    // 分页器 - 切换页大小
    sizeChange(currentPageSize) {
      this.pageSettings.pageSize = currentPageSize
      this.pageSettings.currentPage = 1
      this.searchForm()
    },
    handleSearchReset() {
      for (let key in this.searchFormModel) {
        this.searchFormModel[key] = null
      }
      // this.currentChange(1)
    },
    handleSearchForm() {
      this.$refs.templateRef.refreshCurrentGridData()
      // this.pageSettings.currentPage = 1
      // this.searchForm()
    },
    searchForm() {
      this.$store.commit('startLoading')
      const param = {
        page: {
          current: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$API.receiptAndDelivery
        .getDeliverListSupplierTableDataTV(param)
        .then((res) => {
          if (res.code === 200) {
            setTimeout(() => {
              document.querySelector('.e-content').scrollTop = 0
            }, 20)
            this.$set(this.pageConfig[0].grid, 'dataSource', res.data.records)
            this.pageSettings.totals = Math.ceil(
              Number(res.data.total) / this.pageSettings.pageSize
            )
            this.pageSettings.totalRecordsCount = res.data.total
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        page: {
          current: 1,
          pages: 0,
          size: 20
        }
      }
      this.$API.receiptAndDelivery
        .getFactoryInfo(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 公司 change
    companyCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.companyId = itemData.id
        this.searchFormModel.companyCode = itemData.customerCode
        this.searchFormModel.companyName = itemData.customerName
      } else {
        this.searchFormModel.companyId = ''
        this.searchFormModel.companyCode = ''
        this.searchFormModel.companyName = ''
      }
    },
    // 工厂 change
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.siteId = itemData.id
        this.searchFormModel.siteCode = itemData.siteCode
        this.searchFormModel.siteName = itemData.siteName
      } else {
        this.searchFormModel.siteId = ''
        this.searchFormModel.siteCode = ''
        this.searchFormModel.siteName = ''
      }
    },
    // 获取主数据-获取指定组织下指定组织层级节点列表 业务公司
    getCompany() {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      console.log(userInfo)
      this.$API.masterData
        .getCompanyByCode([userInfo.accountName])
        .then((res) => {
          if (res.code === 200) {
            this.companyOptions = res.data.map((item) => {
              return {
                text: item.organizationCode + '-' + item.organizationName,
                value: item.organizationCode
              }
            })
          }
        })
        .catch(() => {})
    },
    // 弹框
    handleDialogShow(flag) {
      this.deliveryShow = flag
    },
    handleSelectTab(e) {
      this.tabIndex = e
      this.activeTab = e
    },
    // 点击单元格 标题
    handleClickCellTitle(e) {
      if (e.field === 'deliveryCode') {
        localStorage.setItem('deliverDetailSupplierTV', JSON.stringify(e.data))
        this.$router.push(
          `deliver-detail-supplier-tv?id=${e.data.id}&type=${
            e.data.deliveryType
          }&timeStamp=${new Date().getTime()}`
        )
      }
    },
    handleClickCellTitle1(e) {
      if (e.field === 'deliveryCode') {
        this.getDeliverDataById(e.data?.deliveryId)
      }
    },
    // 根据id查询主单数据 - 获取物料信息并跳转物料页面
    getDeliverDataById(id) {
      const params = {
        condition: 'and',
        page: { current: 1, size: 20 },
        rules: [
          {
            field: 'id',
            operator: 'equal',
            value: id
          }
        ]
      }
      this.$API.receiptAndDelivery
        .getSupplierDeliveyData(params)
        .then((res) => {
          if (res?.code == 200) {
            // 跳转详情页

            localStorage.setItem('deliverDetailSupplierTV', JSON.stringify(res?.data?.records[0]))
            this.$router.push(
              `deliver-detail-supplier-tv?id=${res?.data?.records[0].id}&type=${
                res?.data?.records[0].deliveryType
              }&timeStamp=${new Date().getTime()}`
            )
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleClickCellTool(e) {
      if (e.tool.title === this.$t('维护物流信息')) {
        this.$router.push({
          name: 'deliver-logistics-supplier',
          query: {
            id: e.data.id
          }
        })
        localStorage.setItem('deliverDetailSupplier', JSON.stringify(e.data))
        return
      }
      if (e.tool.title === this.$t('取消')) {
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消？')
          },
          success: () => {
            this.$store.commit('startLoading')
            this.$API.receiptAndDelivery
              .supplierOrderDeliveryCancelTV([e.data.id])
              .then((res) => {
                this.$store.commit('endLoading')
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('操作成功'),
                    type: 'success'
                  })
                  this.$refs.templateRef.refreshCurrentGridData()
                  // this.confirmSuccess();
                }
              })
              .catch(() => {
                this.$refs.templateRef.refreshCurrentGridData()

                this.$store.commit('endLoading')
              })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        return
      }
      if (e.tool.id === 'screenIdImport') {
        // 屏ID导入
        this.handleScreenIdImport(e.data.id)
        return
      }
      if (e.tool.id === 'search') {
        // 屏ID列表查看
        this.screenIdSearch(e.data)
        return
      }
      // 附件
      if (
        ['sup_invoice', 'hk_invoice', 'bill_of_lading', 'sale_order', 'delivery_scanning'].includes(
          e.tool.id
        )
      ) {
        this.handleFile(e.data, e.tool.id)
      }
    },
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    handleClickToolBar(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id === 'resetDataByLocal') return
      if (toolbar.id == 'new') {
        this.$router.push(`create-noOrder-supplier-tv`)
        return
      }
      if (toolbar.id === 'import') {
        this.handleImport()
        return
      }
      if (toolbar.id === 'export') {
        // 导出
        this.handleExport()
        return
      }
      const selectRows = []
      gridRef.dataSource.forEach((item) => {
        if (item.customChecked) {
          selectRows.push(item)
        }
      })
      if (selectRows?.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const _status = []
      const _forecastDelivery = []
      const _id = []
      const _codes = []
      selectRows.forEach((item) => {
        _id.push(item.id)
        _codes.push(item.companyCode)
        _status.push(item.status)
        _forecastDelivery.push(item.forecastDelivery)
      })
      this.id = _id
      this.companyCodes = _codes
      if (toolbar.id == 'MaintainIogisticsInformation') {
        for (let i of _status) {
          if (i.value != 2) {
            this.$toast({
              content: this.$t('只有状态在送货中才可以维护'),
              type: 'warning'
            })
            return
          }
        }
        this.deliveryShow = true
        return
      }

      if (toolbar.id === 'AppointmentDelivery1') {
        for (let i of _status) {
          if (i.value != 2) {
            this.$toast({
              content: this.$t('只有状态在送货中才可以预约'),
              type: 'warning'
            })
            return
          }
        }
        for (let i of _forecastDelivery) {
          if (i == 1) {
            this.$toast({
              content: this.$t('请勾选可预约的数据'),
              type: 'warning'
            })
            return
          }
        }
        // 预约送货
        const deliveryCodeList = []
        let deliveryStatusList = []
        selectRows.forEach((item) => {
          // 送货单号
          deliveryCodeList.push(item.deliveryCode)
          // 交货方式
          deliveryStatusList.push(item.deliveryType.value) // 交货方式:1-采购订单,2-交货计划,3-JIT,4-无需求,5-vmi
        })
        const purTenantId = selectRows[0].purTenantId // 采方租户id

        // 交货方式 去空、去重
        deliveryStatusList = [...new Set(deliveryStatusList.filter((item) => item))]
        // 存 localStorage 预约送货 页面读
        sessionStorage.setItem(
          'toReservationDeliverData',
          JSON.stringify({
            selectedRowData: selectRows,
            deliveryCodeList,
            deliveryStatusList,
            purTenantId // 采方租户id
          })
        )
        // 预约送货跳转
        this.$router.push({
          name: 'reservation-deliver-supplier-tv',
          query: {
            type: 'new'
          },
          params: {
            sourceType: 0 //1:入库单 0:送货单
          }
        })
        return
      }

      if (toolbar.id === 'print') {
        if (_status.some((v) => v.value == 4 || v.value == 5)) {
          return this.$toast({
            content: this.$t('不可以打印已取消或已关闭的单据'),
            type: 'warning'
          })
        }
        this.$dialog({
          modal: () => import('./components/printDialog.vue'),
          data: {
            title: this.$t('送货单打印纸张选择')
          },
          success: (type) => {
            this.handlePrint(type, _id)
          }
        })
      }
      if (toolbar.id === 'printBd') {
        if (_status.some((v) => v.value == 4 || v.value == 5)) {
          return this.$toast({
            content: this.$t('不可以打印已取消或已关闭的单据'),
            type: 'warning'
          })
        }
        this.print(_id, 'bd')
      }
      if (toolbar.id == 'cancel') {
        if (_status.some((v) => v.value != 2)) {
          return this.$toast({
            content: this.$t('只有状态在送货中才可以取消'),
            type: 'warning'
          })
        }
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消？')
          },
          success: () => {
            this.handleClaim(_id)
          }
        })
      }

      if (toolbar.id == 'loseeffectiveness') {
        this.$API.receiptAndDelivery
          .supplierOrderDeliveryRepair(_id)
          .then((res) => {
            this.$store.commit('startLoading')
            if (res.code == 200) {
              this.$store.commit('endLoading')

              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
              this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
            }
          })
          .catch(() => {
            this.$store.commit('endLoading')
            this.$refs.templateRef.refreshCurrentGridData()
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
          })
      }
      if (toolbar.id === 'synchronous') {
        this.synchronousWms(selectRows)
      }
      if (toolbar.id === 'EditIHNumber') {
        this.handleEditIH(selectRows)
      }
      if (toolbar.id === 'BatchDownload') {
        if (
          selectRows.some(
            (item) =>
              !item.screenIdCount &&
              !item.supInvoiceCount &&
              !item.hkInvoiceCount &&
              !item.billOfLadingCount &&
              !item.saleOrderCount &&
              !item.deliveryScanningCount
          )
        ) {
          this.$toast({
            content: this.$t('不可以选择所有附件上传数为0的单据！'),
            type: 'warning'
          })
        } else {
          this.$dialog({
            modal: () => import('./components/batchDownloadDialog.vue'),
            data: {
              title: this.$t('批量下载附件')
            },
            success: (modelForm) => {
              this.batchDownload(_id, modelForm)
            }
          })
        }
      }
    },
    handleEditIH(selectedRecords) {
      this.$dialog({
        modal: () => import('../deliverListTV/components/editIHNumberDialog.vue'),
        data: {
          title: this.$t('编辑IH号'),
          selectedRecords
        },
        success: () => {
          this.handleSearchForm()
        }
      })
    },
    batchDownload(ids, modelForm) {
      const params = {
        body: ids,
        ...modelForm
      }
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postBuyerOrderDeliveryBatchDownload(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.receiptAndDelivery.SupplierOrderDeliveryImport,
          downloadTemplateApi: this.$API.receiptAndDelivery.SupplierOrderDeliveryDownloadTemplate,
          paramsKey: 'excel'
        },
        success: () => {
          this.handleSearchForm()
        }
      })
    },
    handleScreenIdImport(id) {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.receiptAndDelivery.supDeliveryImport,
          downloadTemplateApi: this.$API.receiptAndDelivery.supDeliveryDownloadTemplate,
          paramsKey: 'excel',
          asyncParams: {
            id
          }
        },
        success: () => {
          this.handleSearchForm()
        }
      })
    },
    screenIdSearch(row) {
      this.$dialog({
        modal: () => import('./components/screenIdList.vue'),
        data: {
          title: this.$t('屏ID查看'),
          row
        }
      })
    },
    handleExport() {
      let obj = JSON.parse(sessionStorage.getItem(this.pageConfig[0].gridId))?.visibleCols
      const headerMap = {}
      if (obj !== undefined && obj.length) {
        obj?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      } else {
        this.pageConfig[0].grid.columnData?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      }
      const params = {
        page: {
          current: 1,
          size: 9999
        },
        ...this.searchFormModel,
        headerMap
      }
      this.apiStartLoading()
      this.$API.receiptAndDelivery.postSupplierOrderDeliveryExport(params).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleFile(row, type) {
      const typeMap = {
        sup_invoice: 5,
        hk_invoice: 6,
        bill_of_lading: 7,
        sale_order: 8,
        delivery_scanning: 9
      }
      this.$dialog({
        modal: () => import('./components/fileManage.vue'),
        data: {
          title: this.$t('附件上传'),
          id: row.id,
          type: typeMap[type],
          row
        },
        close: () => {
          this.handleSearchForm()
        }
      })
    },
    handlePrint(type, ids) {
      let params = {
        templateType: type,
        ids
      }
      this.$API.receiptAndDelivery.deliverySupplierPrintApi(params).then((res) => {
        if (res && res.code === 500) {
          this.$toast({ content: res.msg, type: 'error' })
          return
        }
        const content = res.data
        this.pdfUrl = window.URL.createObjectURL(
          new Blob([content], { type: 'text/html;charset=utf-8' })
        )
        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = this.pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    print(_id, type) {
      if (type === 'kt') {
        this.$API.receiptAndDelivery.supplierOrderDeliveryPrintHtml(_id).then((res) => {
          if (res && res.code === 500) {
            this.$toast({ content: res.msg, type: 'error' })
            return
          }
          // if (res?.data?.type === "application/json") {
          //   const reader = new FileReader();
          //   reader.readAsText(res?.data, "utf-8");
          //   reader.onload = function () {
          //     console.log("======", reader);
          //     const readerRes = reader.result;
          //     const resObj = JSON.parse(readerRes);
          //     Vue.prototype.$toast({
          //       content: resObj.msg,
          //       type: "error",
          //     });
          //   };

          //   return;
          // }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(
            new Blob([content], { type: 'text/html;charset=utf-8' })
          )
          // window.open(this.pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
      } else if (type === 'bd') {
        this.$API.receiptAndDelivery.supplierOrderDeliveryPrint(_id).then((res) => {
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = function () {
              console.log('======', reader)
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }

            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
          // window.open(this.pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
      }
    },
    // 同步WMS
    synchronousWms(data) {
      let _ids = []
      let _flag = true
      data.forEach((item) => {
        if (item.wmsSyncStatus === 1) {
          _flag = false
        }
        _ids.push(item.deliveryCode)
      })
      if (!_flag) {
        this.$toast({
          content: this.$t('送货单已成功同步WMS，无需重复同步'),
          type: 'warning'
        })
        return
      }
      this.$API.purchaseCoordination.deliverySynchronousWms(_ids).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('同步成功'), type: 'success' })
          this.handleSearchForm()
        } else {
          this.$toast({ content: this.$t('同步失败'), type: 'warning' })
        }
        this.$refs.templateRef.refreshCurrentGridData()
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    handleClaim(_id) {
      this.$store.commit('startLoading')

      this.$API.receiptAndDelivery
        .supplierOrderDeliveryCancelTV(_id)
        .then((res) => {
          if (res.code == 200) {
            this.$store.commit('endLoading')
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
            this.handleSearchForm()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef.refreshCurrentGridData()
        })
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('todoDetail')
  }
}
</script>

<style lang="scss" scoped>
.forecast-template {
  width: 250px;
  padding-bottom: 8px;
  .forecast-template-select {
    width: calc(250px - 70px);
  }
}
/deep/ .column-tool {
  margin-top: 8px;
}
/deep/ .template-svg {
  cursor: pointer;
  font-size: 12px;
  color: var(--plugin-ct-cell-icon-color);

  &:nth-child(n + 2) {
    margin-left: 10px;
  }
}

/deep/ .grid-edit-column {
  // padding: 12px 0;
}

// 预测数据行
/deep/ .forecast-item {
  // height: 40px;
  // padding: 7px;
  line-height: 26px;
  border: 1px solid #e8e8e8;
}
/deep/ .inputSy {
  border: 1px solid #e8e8e8;
  height: 30px;
}
// 预测数据高亮数据
/deep/ .forecast-highlight {
  color: #ed5836;
  background-color: #fdeeea;
  border: 1px solid #ed5836;
}

// 表格容器
#forecast-manage-table-container {
  position: relative;

  // 表格数据视图
  /deep/ .e-gridcontent > .e-content {
    height: 0px;
    transition: height 0.5s ease;
  }

  // 分页
  /deep/ .mt-pagertemplate {
    margin: 10px 0 10px !important;
  }
}

// 行编辑时包含按钮的项目
/deep/ .input-search-content {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .mt-input {
    flex: 1;
  }

  .mutliselect-container {
    width: 100%;
  }
  // 超过宽度显示省略号
  .text-ellipsis {
    width: 125px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .mt-icons {
    width: 20px;
    flex-shrink: 0;
    text-align: center;
    cursor: pointer;
  }
}
/deep/ .templateRef {
  height: 100%;
}
// /deep/ .templateRef {
//   .template-wrap {
//     .e-grid .e-table {
//       & thead th:first-child {
//         position: sticky;
//         left: 0px;
//         z-index: 1;
//       }
//       & thead th:nth-child(2) {
//         position: sticky;
//         left: 50px;
//         z-index: 1;
//       }
//       & thead th:nth-child(3) {
//         position: sticky;
//         left: 115px;
//         z-index: 1;
//       }
//       & thead th:nth-child(4) {
//         position: sticky;
//         left: 315px;
//         z-index: 1;
//       }
//       & thead th:nth-child(5) {
//         position: sticky;
//         left: 397px;
//         z-index: 1;
//       }
//       & thead th:nth-child(6) {
//         position: sticky;
//         left: 497px;
//         z-index: 1;
//       }

//       & tbody td:first-child {
//         position: sticky;
//         left: 0px;
//         z-index: 1;
//         border-right: 1px solid var(--plugin-dg-shadow-color);
//         background-color: #fff;
//       }
//       & tbody td:nth-child(2) {
//         position: sticky;
//         left: 50px;
//         z-index: 1;
//         border-right: 1px solid var(--plugin-dg-shadow-color);
//         background-color: #fff;
//       }
//       & tbody td:nth-child(3) {
//         position: sticky;
//         left: 115px;
//         z-index: 1;
//         border-right: 1px solid var(--plugin-dg-shadow-color);
//         background-color: #fff;
//       }
//       & tbody td:nth-child(4) {
//         position: sticky;
//         left: 315px;
//         z-index: 1;
//         border-right: 1px solid var(--plugin-dg-shadow-color);
//         background-color: #fff;
//       }
//       & tbody td:nth-child(5) {
//         position: sticky;
//         left: 397px;
//         z-index: 1;
//         border-right: 1px solid var(--plugin-dg-shadow-color);
//         background-color: #fff;
//       }
//       & tbody td:nth-child(6) {
//         position: sticky;
//         left: 497px;
//         z-index: 1;
//         border-right: 1px solid var(--plugin-dg-shadow-color);
//         background-color: #fff;
//       }

//       & tbody .e-active {
//         background-color: #e0e0e0 !important;
//       }
//     }
//   }
// }
</style>
