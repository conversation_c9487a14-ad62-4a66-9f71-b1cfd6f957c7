<template>
  <div id="cell-changed">
    <mt-input-number
      :id="data.column.field"
      v-model="data[data.column.field]"
      :show-spin-button="false"
      :min="0"
      :disabled="data['addOrderStatus'] === 1"
      @input="inputValue"
    />
  </div>
</template>
<script>
export default {
  data() {
    return { data: {} }
  },
  mounted() {},
  methods: {
    inputValue(e) {
      if (e) {
        const _dataSource = this.$parent?.$parent?.$parent?.$refs.gridRef.dataSource ?? []
        _dataSource.map((item, index) => {
          if (index === Number(this.data.index)) {
            item[this.data.column.field] = e
          }
        })
      }
    }
  }
}
</script>
