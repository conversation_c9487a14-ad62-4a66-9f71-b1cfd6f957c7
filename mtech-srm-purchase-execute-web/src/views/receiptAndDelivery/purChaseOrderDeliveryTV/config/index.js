import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import Vue from 'vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName } from '@/utils/utils'

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, isDateTime, isDate, isTime } = args

  const template = () => {
    return {
      template: Vue.component('timeDateComponent', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <div v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</div>
            <div v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</div>
          </div>
        </div>`,
        data: function () {
          return { data: {}, dataKey, isDateTime, isDate, isTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
export const columnData = [
  {
    width: '200',
    field: 'orderCode',
    headerText: i18n.t('采购订单号'),
    // searchOptions: {
    //   operator: 'likeright'
    // }
    // template: () => {
    //   return { template: Input }
    // },
    clipMode: 'Ellipsis'
  },
  {
    width: '130',
    field: 'lineNo',
    headerText: i18n.t('采购订单行号'),
    // searchOptions: {
    //   operator: 'likeright'
    // }
    // template: () => {
    //   return { template: Input }
    // },
    clipMode: 'Ellipsis'
  },
  // {
  //   width: '200',
  //   field: 'newLineNo',
  //   headerText: i18n.t('新送货单号行号'),
  //   template: () => {
  //     return { template: Input }
  //   },
  //   clipMode: 'Ellipsis'
  // },
  {
    width: '200',
    field: 'deliveryCode',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('送货单号')
  },
  {
    width: '90',
    field: 'deliveryLineNo',
    headerText: i18n.t('行号'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '120',
    field: 'status',
    headerText: i18n.t('WMS收货状态'),
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: [
        { value: 2, text: i18n.t('发货中'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('已完成'), cssClass: 'col-inactive' },
        { value: 4, text: i18n.t('已取消'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('已关闭'), cssClass: 'col-inactive' },
        { value: 6, text: i18n.t('部分收货'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '235',
    field: 'companyName',
    searchOptions: {
      operator: 'likeright',
      renameField: 'companyCode',
      ...MasterDataSelect.companySupplier
    },
    headerText: i18n.t('公司'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.companyCode}}-{{data.companyName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '200',
    field: 'siteName',
    searchOptions: {
      operator: 'likeright',
      renameField: 'siteCode',
      ...MasterDataSelect.factorySupplierAddress
    },
    headerText: i18n.t('工厂'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '220',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    searchOptions: {
      ...MasterDataSelect.itemSupplier,
      renameField: 'itemCode'
    }
  },
  {
    width: '220',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    ignore: true
  },
  {
    width: '123',
    field: 'deliveryQuantity',
    headerText: i18n.t('本次送货数量'),
    type: 'number',
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '98',
    field: 'receiveQuantity',
    type: 'number',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('收货数量')
  },
  {
    width: '96',
    field: 'rejectQuantity',
    searchOptions: {
      operator: 'likeright'
    },
    type: 'number',
    headerText: i18n.t('拒绝数量')
  },
  {
    width: '175',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组'),
    searchOptions: {
      operator: 'likeright',
      ...MasterDataSelect.businessCodeName,
      renameField: 'buyerOrgCode'
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '66',
    field: 'unitName',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('单位')
  },
  {
    width: '97',
    field: 'deliveryType',
    headerText: i18n.t('送货单类型'),
    searchOptions: {
      operator: 'likeright'
    },
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('采购订单'), cssClass: '' },
        { value: 2, text: i18n.t('交货计划'), cssClass: '' },
        { value: 3, text: i18n.t('JIT'), cssClass: '' },
        { value: 4, text: i18n.t('无需求'), cssClass: '' },
        { value: 5, text: i18n.t('vmi'), cssClass: '' },
        { value: 6, text: i18n.t('钢材'), cssClass: '' }
      ]
    }
  },
  {
    width: '150',
    field: 'syncWmsStatus',
    headerText: i18n.t('WMS同步状态'),
    searchOptions: {
      operator: 'likeright'
    },
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未同步'),
        1: i18n.t('同步中'),
        2: i18n.t('同步成功'),
        3: i18n.t('同步失败')
      }
    }
  },
  {
    width: '150',
    field: 'syncWmsDesc',
    headerText: i18n.t('WMS同步信息'),
    searchOptions: {
      operator: 'likeright'
    },
    allowEditing: false
  },
  {
    width: '150',
    field: 'syncQmsStatus',
    headerText: i18n.t('QMS同步状态'),
    searchOptions: {
      operator: 'likeright'
    },
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未同步'),
        1: i18n.t('同步中'),
        2: i18n.t('同步成功'),
        3: i18n.t('同步失败')
      }
    }
  },
  {
    width: '150',
    field: 'syncQmsDesc',
    headerText: i18n.t('QMS同步信息'),
    searchOptions: {
      operator: 'likeright'
    },
    allowEditing: false
  },
  {
    width: '150',
    field: 'syncSapStatus',
    headerText: i18n.t('SAP同步状态'),
    searchOptions: {
      operator: 'likeright'
    },
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未同步'),
        1: i18n.t('同步成功'),
        2: i18n.t('同步成功'),
        3: i18n.t('同步失败')
      }
    }
  },
  {
    width: '150',
    field: 'syncSapDesc',
    headerText: i18n.t('SAP同步信息'),
    searchOptions: {
      operator: 'likeright'
    },
    allowEditing: false
  },
  {
    width: '150',
    field: 'batchCode',
    headerText: i18n.t('卷号')
  },
  {
    width: '150',
    field: 'takeNo',
    headerText: i18n.t('车号')
  },
  {
    width: '150',
    field: 'subSiteName',
    headerText: i18n.t('分厂'),
    searchOptions: {
      operator: 'likeright',
      ...MasterDataSelect.subSiteCodeSupplier,
      renameField: 'subSiteCode'
    },
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.subSiteCode, data?.subSiteName)
    }
  },
  {
    width: '135',
    field: 'warehouseName',
    headerText: i18n.t('交货库存地点'),
    searchOptions: {
      operator: 'likeright',
      ...MasterDataSelect.stockSupplierAddressName,
      renameField: 'warehouseCode'
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.warehouseCode}}-{{data.warehouseName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'inputDate', //
    headerText: i18n.t('凭证创建日期'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    template: timeDate({ dataKey: 'inputDate', isDateTime: true })
  },
  // {
  //   field: "inputTime", //
  //   headerText: i18n.t("凭证创建时间"),
  //   template: timeDate({ dataKey: "inputTime", isTime: true }),
  //   searchOptions: {
  //     ...MasterDataSelect.timeRange,
  //   },
  // },
  {
    field: 'postingDate', //
    headerText: i18n.t('过账日期'),
    template: timeDate({ dataKey: 'postingDate', isDate: true }),

    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    width: '130',
    field: 'thirdTenantCode',
    headerText: i18n.t('第三方物流商'),
    searchOptions: {
      operator: 'likeright',
      ...MasterDataSelect.deliverSupplierThirdTenantCode
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.thirdTenantCode}}-{{data.thirdTenantName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '150',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓'),
    searchOptions: {
      operator: 'likeright',
      ...MasterDataSelect.deliverSupplierVmiWarehouseCode
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.vmiWarehouseCode}}-{{data.vmiWarehouseName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '75',
    field: 'takeNo',
    headerText: i18n.t('车牌号'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '150',
    field: 'deliveryNumber',
    headerText: i18n.t('交货编号'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '150',
    field: 'jitDeliveryNumber',
    headerText: i18n.t('JIT编号'),
    ignore: true,
    searchOptions: {
      renameField: 'deliveryNumber'
    }
  },
  {
    width: '85',
    field: 'jit',
    headerText: i18n.t('是否JIT'),
    searchOptions: {
      operator: 'likeright'
    },
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },
  {
    width: '100',
    field: 'demandDate',
    headerText: i18n.t('需求日期'),
    // template: timeDate({ dataKey: "demandDate", isDateTime: true }),

    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  // {
  //   width: "100",
  //   field: "demandTime",
  //   headerText: i18n.t("需求时间"),
  //   searchOptions: {
  //     operator: "likeright",
  //   },
  // },
  {
    width: '100',
    field: 'rejectReason',
    ignore: true,
    headerText: i18n.t('拒绝原因')
  },
  {
    width: '90',
    field: 'transferPlanName',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('计划员')
  },
  {
    width: '107',
    field: 'workOrderNo',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('关联工单号')
  },
  {
    width: '140',
    field: 'saleOrderNo',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('关联销售订单号')
  },
  {
    width: '154',
    field: 'saleOrderLineNo',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('关联销售订单行号')
  },
  {
    width: '85',
    field: 'bomCode',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('BOM号')
  },
  {
    width: '124',
    field: 'productCode',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('关联产品代码')
  },
  {
    width: '91',
    field: 'workCenterName',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('工作中心')
  },
  {
    width: '91',
    field: 'processName',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('工序名称')
  },
  // {
  //   width: "150",
  //   field: "receiveSupplierName",
  //   headerText: i18n.t("送货方式"), 暂无
  // },
  // {
  //   width: "150",
  //   field: "receiveSupplierName",
  //   headerText: i18n.t("收货方名称"),
  // },
  // {
  //   width: "150",
  //   field: "receiveSupplierCode",
  //   headerText: i18n.t("收货方编号"),
  // },
  {
    width: '91',
    field: 'receiveAddressName',
    headerText: i18n.t('送货地址'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  // {
  //   width: '91',
  //   field: '2',
  //   headerText: i18n.t('批次管理')
  // },
  // {
  //   width: '68',
  //   field: '2',
  //   headerText: i18n.t('下达')
  // },
  {
    width: '91',
    field: 'limitQuantity',
    type: 'number',

    headerText: i18n.t('限量数量'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '86',
    field: 'warehouseClerkName',
    headerText: i18n.t('仓管员'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '86',
    field: 'dispatcherName',
    headerText: i18n.t('调度员'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  // FIXME: 暂时不展示
  // {
  //   width: "150",
  //   field: "inventoryQuantity",
  //   headerText: i18n.t("入库数量"),
  // },
  // {
  //   width: "150",
  //   field: "inventoryTime",
  //   headerText: i18n.t("入库日期"),
  //   template: timeDate("inventoryTime", true),
  // },
  {
    width: '107',
    field: 'receiveSupplierName',
    headerText: i18n.t('收货供应商'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '86',
    field: 'cancelPersonName',
    headerText: i18n.t('取消人'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '95',
    field: 'cancelTime',
    headerText: i18n.t('取消时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    template: timeDate({ dataKey: 'cancelTime', isDateTime: true })
  },
  {
    width: '86',
    field: 'closePersonName',
    headerText: i18n.t('关闭人'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '95',
    field: 'closeTime',
    headerText: i18n.t('关闭时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    template: timeDate({ dataKey: 'closeTime', isDateTime: true })
  },
  {
    width: '95',
    field: 'collectorMark',
    headerText: i18n.t('代收标识'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '86',
    field: 'collectorName',
    headerText: i18n.t('代收人'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '86',
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    width: '86',
    field: 'remark',
    headerText: i18n.t('行备注')
  }
]
