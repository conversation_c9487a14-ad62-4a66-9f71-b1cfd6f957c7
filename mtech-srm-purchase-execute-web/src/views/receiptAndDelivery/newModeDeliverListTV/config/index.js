import { Tab, CellTools, JitOptions, OutsourcedTypeOptions } from './constant'
import { timeDate } from './columnComponent'
import { MasterDataSelect } from '@/utils/constant'
import { codeNameColumn } from '@/utils/utils'

// 格式化表格动态数据
export const formatTableColumnData = (args) => {
  const { data, tab } = args
  const colData = []
  if (tab === Tab.list) {
    // 送货单列表
    data.forEach((col) => {
      const defaultCol = {
        ...col,
        field: col.fieldCode,
        headerText: col.fieldName,
        width: '150'
      }
      if (col.fieldCode === 'deliveryCode') {
        // 送货单号
        defaultCol.cellTools = [] // 使其可点击查看
        defaultCol.searchOptions = {
          operator: 'likeright'
        }
      } else if (col.fieldCode === 'deliveryOrderType') {
        // 送货单列表-送货类型
        defaultCol.width = '105'
        defaultCol.searchOptions = {
          operator: 'likeright'
        }
        defaultCol.valueConverter = {
          // type: "map",
          // map: DeliveryTypeOptions,
          type: 'function',
          filter: (e) => {
            if (e) {
              return e.label
            }
            return ''
          }
        }
      } else if (col.fieldCode === 'status') {
        // 送货单列表-状态
        defaultCol.width = '82'
        defaultCol.searchOptions = {
          elementType: 'multi-select',
          showSelectAll: true,
          operator: 'in'
        }
        defaultCol.valueConverter = {
          // type: "map",
          // map: StatusOptions,
          type: 'function',
          filter: (e) => {
            if (e) {
              console.log('e', e)
              return e.label
            }
            return ''
          }
        }
        defaultCol.cellTools = CellTools // 表格行按钮
      } else if (
        col.fieldCode === 'syncWmsStatus' ||
        col.fieldCode === 'syncSapStatus' ||
        col.fieldCode === 'syncQmsStatus'
      ) {
        // 送货单列表-状态
        defaultCol.width = '120'
        defaultCol.searchOptions = {
          operator: 'likeright'
        }
        defaultCol.valueConverter = {
          // type: "map",
          // map: {
          //   0: "未同步",
          //   1: "同步中",
          //   2: "同步成功",
          //   3: "同步失败",
          // },
          type: 'function',
          filter: (e) => {
            if (e) {
              return e.label
            }
            return ''
          }
        }
      } else if (
        col.fieldCode === 'syncWmsDesc' ||
        col.fieldCode === 'syncQmsDesc' ||
        col.fieldCode === 'syncSapDesc'
      ) {
        // 送货单列表-关联销售订单号
        defaultCol.searchOptions = {
          operator: 'likeright'
        }
        defaultCol.width = '250'
      } else if (col.fieldCode === 'saleOrderNo') {
        defaultCol.searchOptions = {
          operator: 'likeright'
        }
        // 送货单列表-关联销售订单号
        defaultCol.width = '250'
      } else if (col.fieldCode === 'saleOrderLineNo') {
        defaultCol.searchOptions = {
          operator: 'likeright'
        }
        // 送货单列表-关联销售订单行号
        defaultCol.width = '250'
      } else if (col.fieldCode === 'inventoryTime') {
        defaultCol.searchOptions = {
          ...MasterDataSelect.timeRange
        }
        // cancelTime 取消时间 integer
        // sendTime 发货日期 integer
        // forecastArriveTime 预计到货日期 integer
        // inventoryTime 入库日期 integer
        defaultCol.template = timeDate({
          dataKey: col.fieldCode,
          isTime: true
        })
      } else if (col.fieldCode === 'forecastArriveTime') {
        // demandDate 需求日期 string
        defaultCol.template = timeDate({
          dataKey: col.fieldCode,
          isDate: true
        })
        defaultCol.searchOptions = {
          ...MasterDataSelect.timeRange
        }
      } else if (col.fieldCode === 'demandTime' || col.fieldCode === 'receiveTime') {
        // demandTime 需求时间 string
        // receiveTime 收货时间 integer
        defaultCol.template = timeDate({
          dataKey: col.fieldCode,
          isTime: true
        })
        defaultCol.searchOptions = {
          ...MasterDataSelect.timeRange
        }
      } else if (
        col.fieldCode === 'demandDate' ||
        col.fieldCode === 'createTime' ||
        col.fieldCode === 'sendTime'
      ) {
        // demandTime 需求时间 string
        // receiveTime 收货时间 integer
        defaultCol.template = timeDate({
          dataKey: col.fieldCode,
          isDateTime: true
        })
        defaultCol.searchOptions = {
          ...MasterDataSelect.timeRange
        }
      } else if (col.fieldCode === 'outsourcedType') {
        // 委外方式
        defaultCol.width = '95'
        defaultCol.valueConverter = {
          type: 'map',
          map: OutsourcedTypeOptions
        }
      } else if (col.fieldCode === 'siteCode') {
        // 工厂
        // code-name 形式
        defaultCol.width = '150'
        defaultCol.template = codeNameColumn({
          firstKey: 'siteCode',
          secondKey: 'siteName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        // 主数据选择器
        defaultCol.searchOptions = {
          ...MasterDataSelect.factoryAddress
        }
      } else if (col.fieldCode === 'companyCode') {
        // 公司
        // code-name 形式
        defaultCol.width = '235'
        defaultCol.template = codeNameColumn({
          firstKey: 'companyCode',
          secondKey: 'companyName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        // 主数据选择器
        defaultCol.searchOptions = {
          ...MasterDataSelect.businessCompany
        }
      } else if (col.fieldCode === 'buyerOrgCode') {
        // 采购组
        // code-name 形式
        defaultCol.width = '300'
        defaultCol.template = codeNameColumn({
          firstKey: 'buyerOrgCode',
          secondKey: 'buyerOrgName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        defaultCol.searchOptions = {
          ...MasterDataSelect.businessGroupIn
        }
      } else if (col.fieldCode === 'supplierCode') {
        defaultCol.width = '150'
        defaultCol.template = codeNameColumn({
          firstKey: 'supplierCode'
        })
      } else if (col.fieldCode === 'supplierName') {
        defaultCol.width = '220'
        defaultCol.template = codeNameColumn({
          firstKey: 'supplierName'
        })
      } else if (col.fieldCode === 'thirdTenantCode') {
        // 第三方物流商 即：供应商
        // code-name 形式
        defaultCol.width = '130'
        defaultCol.template = codeNameColumn({
          firstKey: 'thirdTenantCode',
          secondKey: 'thirdTenantName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        defaultCol.searchOptions = {
          ...MasterDataSelect.supplier
        }
      } else if (col.fieldCode === 'warehouseCode') {
        // 交货库存地点
        // code-name 形式
        defaultCol.width = '135'
        defaultCol.template = codeNameColumn({
          firstKey: 'warehouseCode',
          secondKey: 'warehouseName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        defaultCol.searchOptions = {
          ...MasterDataSelect.stockAddressName,
          renameField: 'warehouseName'
        }
      }
      colData.push(defaultCol)
    })
  } else if (tab === Tab.details) {
    // 送货明细
    data.forEach((col) => {
      const defaultCol = {
        ...col,
        field: col.fieldCode,
        headerText: col.fieldName,
        width: '150'
      }
      if (col.fieldCode === 'deliveryCode') {
        // 送货单号
        defaultCol.cellTools = [] // 使其可点击查看
      } else if (col.fieldCode === 'deliveryType') {
        // 送货明细-交货方式
        defaultCol.valueConverter = {
          type: 'function',
          filter: (e) => {
            if (e) {
              console.log('e', e)
              return e.label
            }
            return ''
          }
        }
      } else if (col.fieldCode === 'warehouseCode') {
        // 交货库存地点
        // code-name 形式
        defaultCol.width = '135'
        defaultCol.template = codeNameColumn({
          firstKey: 'warehouseCode',
          secondKey: 'warehouseName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        defaultCol.searchOptions = {
          ...MasterDataSelect.stockAddressName,
          renameField: 'warehouseName'
        }
      } else if (col.fieldCode === 'vmiWarehouseCode') {
        defaultCol.searchOptions = {
          ...MasterDataSelect.deliverNew
        }
      } else if (col.fieldCode === 'status') {
        // 送货明细-状态
        defaultCol.width = '75'
        defaultCol.valueConverter = {
          type: 'function',
          filter: (e) => {
            if (e) {
              console.log('e', e)
              return e.label
            }
            return ''
          }
        }
        defaultCol.searchOptions = {
          elementType: 'multi-select',
          showSelectAll: true,
          operator: 'in'
        }
      } else if (col.fieldCode === 'saleOrderNo') {
        // 送货明细-关联销售订单号
        defaultCol.width = '250'
      } else if (col.fieldCode === 'saleOrderLineNo') {
        // 送货明细-关联销售订单行号
        defaultCol.width = '250'
      } else if (
        col.fieldCode === 'forecastArriveTime' ||
        col.fieldCode === 'inventoryTime' ||
        col.fieldCode === 'inputTime'
      ) {
        // cancelTime 取消时间 integer
        // sendTime 发货日期 integer
        // inventoryTime 入库日期 integer
        defaultCol.template = timeDate({
          dataKey: col.fieldCode,
          isTime: true
        })
        defaultCol.searchOptions = {
          ...MasterDataSelect.timeRange
        }
      } else if (col.fieldCode === 'postingDate') {
        // forecastArriveTime 预计到货日期 integer
        // demandDate 需求日期 string
        defaultCol.width = '100'
        defaultCol.template = timeDate({
          dataKey: col.fieldCode,
          isDate: true
        })
        defaultCol.searchOptions = {
          ...MasterDataSelect.dateRange
        }
      } else if (
        col.fieldCode === 'createTime' ||
        col.fieldCode === 'cancelTime' ||
        col.fieldCode === 'inputDate' ||
        col.fieldCode === 'closeTime'
      ) {
        defaultCol.width = '100'
        defaultCol.template = timeDate({
          dataKey: col.fieldCode,
          isDateTime: true
        })
        defaultCol.searchOptions = {
          ...MasterDataSelect.timeRange
        }
      } else if (col.fieldCode === 'demandTime' || col.fieldCode === 'receiveTime') {
        // demandTime 需求时间 string
        // receiveTime 收货时间 integer
        defaultCol.width = '100'
        defaultCol.template = timeDate({
          dataKey: col.fieldCode,
          isTime: true
        })
        defaultCol.searchOptions = {
          ...MasterDataSelect.timeRange
        }
      } else if (
        col.fieldCode === 'createTime' ||
        col.fieldCode === 'cancelTime' ||
        col.fieldCode === 'inputDate' ||
        col.fieldCode === 'sendTime' ||
        col.fieldCode === 'demandDate' ||
        col.fieldCode === 'closeTime'
      ) {
        defaultCol.width = '100'
        defaultCol.template = timeDate({
          dataKey: col.fieldCode,
          isDateTime: true
        })
        defaultCol.searchOptions = {
          ...MasterDataSelect.timeRange
        }
      } else if (
        col.fieldCode === 'createUserName' ||
        col.fieldCode === 'transferPlanName' ||
        col.fieldCode === 'collectorName' ||
        col.fieldCode === 'cancelPersonName' ||
        col.fieldCode === 'dispatcherName' ||
        col.fieldCode === 'warehouseClerkName'
      ) {
        // 创建人
        // 计划员
        // 代收人
        // 取消人
        // 调度员
        // 仓管员
        // 主数据选择器
        defaultCol.searchOptions = {
          ...MasterDataSelect.staff,
          fields: { text: 'title', value: 'employeeName' }
        }
      } else if (col.fieldCode === 'jit') {
        // 是否JIT
        defaultCol.width = '86'
        defaultCol.valueConverter = {
          type: 'map',
          map: JitOptions
        }
      } else if (col.fieldCode === 'companyCode') {
        // 公司
        // code-name 形式
        defaultCol.width = '236'
        defaultCol.template = codeNameColumn({
          firstKey: 'companyCode',
          secondKey: 'companyName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        // 主数据选择器
        defaultCol.searchOptions = {
          ...MasterDataSelect.businessCompany
        }
      } else if (col.fieldCode === 'siteCode') {
        // 工厂
        // code-name 形式
        defaultCol.width = '151'
        defaultCol.template = codeNameColumn({
          firstKey: 'siteCode',
          secondKey: 'siteName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        // 主数据选择器
        defaultCol.searchOptions = {
          ...MasterDataSelect.factoryAddress
        }
      } else if (col.fieldCode === 'subSiteCode') {
        // 分厂
        // code-name 形式
        defaultCol.width = '151'
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        // 主数据选择器
        defaultCol.template = codeNameColumn({
          firstKey: 'subSiteCode',
          secondKey: 'subSiteName'
        })
        defaultCol.searchOptions = {
          operator: 'likeright',
          ...MasterDataSelect.subSiteCodeBuyer
        }
      } else if (col.fieldCode === 'supplierCode') {
        defaultCol.width = '150'
        defaultCol.template = codeNameColumn({
          firstKey: 'supplierCode'
        })
      } else if (col.fieldCode === 'supplierName') {
        defaultCol.width = '220'
        defaultCol.template = codeNameColumn({
          firstKey: 'supplierName'
        })
      } else if (col.fieldCode === 'thirdTenantCode') {
        // 第三方物流商 即：供应商
        // code-name 形式
        defaultCol.width = '126'
        defaultCol.template = codeNameColumn({
          firstKey: 'thirdTenantCode',
          secondKey: 'thirdTenantName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        defaultCol.searchOptions = {
          ...MasterDataSelect.supplier
        }
      } else if (col.fieldCode === 'receiveSupplierCode') {
        // 收货供应商 即：供应商
        // code-name 形式
        defaultCol.width = '300'
        defaultCol.template = codeNameColumn({
          firstKey: 'receiveSupplierCode',
          secondKey: 'receiveSupplierName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        defaultCol.searchOptions = {
          ...MasterDataSelect.supplier
        }
      } else if (col.fieldCode === 'itemCode') {
        defaultCol.width = '150'
        defaultCol.template = codeNameColumn({
          firstKey: 'itemCode'
        })
      } else if (col.fieldCode === 'itemName') {
        defaultCol.width = '220'
        defaultCol.template = codeNameColumn({
          firstKey: 'itemName'
        })
      } else if (col.fieldCode === 'buyerOrgCode') {
        // 采购组
        // code-name 形式
        defaultCol.width = '300'
        defaultCol.template = codeNameColumn({
          firstKey: 'buyerOrgCode',
          secondKey: 'buyerOrgName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        defaultCol.searchOptions = {
          ...MasterDataSelect.businessGroupIn
        }
      } else if (col.fieldCode === 'unitCode') {
        // 单位
        // code-name 形式
        defaultCol.width = '300'
        defaultCol.template = codeNameColumn({
          firstKey: 'unitCode',
          secondKey: 'unitName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        defaultCol.searchOptions = {
          ...MasterDataSelect.unit
        }
      } else if (col.fieldCode === 'workCenterCode') {
        // 工作中心
        // code-name 形式
        defaultCol.width = '300'
        defaultCol.template = codeNameColumn({
          firstKey: 'workCenterCode',
          secondKey: 'workCenterName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        defaultCol.searchOptions = {
          ...MasterDataSelect.workCenter
        }
      }
      colData.push(defaultCol)
    })
  }

  return colData
}

// 验证 状态是否统一
export const verifyStatus = (data) => {
  let valid = true
  let status = ''
  for (let i = 0; i < data.length; i++) {
    status = data[i].status
    if (data[i] && data[i - 1] && data[i].status.label !== data[i - 1].status.label) {
      valid = false
      break
    }
  }

  return { valid, status }
}
