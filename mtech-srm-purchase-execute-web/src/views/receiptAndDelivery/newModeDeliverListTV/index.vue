<!-- 采方-新模式送货单 -->
<template>
  <div class="full-height vertical-flex-box">
    <!-- 列模板 -->
    <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
    <div style="height: calc(100% - 70px)">
      <mt-template-page
        ref="templateRef"
        :current-tab="currentTab"
        :hidden-tabs="false"
        :permission-obj="permissionObj"
        v-show="tabIndex === 0"
        :template-config="templateConfig1"
        @handleCustomReset="handleSearchReset"
        @handleCustomSearch="handleCustomSearch"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
      >
        <template v-slot:quick-search-form>
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="deliveryCode" :label="$t('送货单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.deliveryCode"
                :placeholder="$t('请输入送货单号')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="sourceDeliveryCode" :label="$t('源送货单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.sourceDeliveryCode"
                :placeholder="$t('请输入源送货单号')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('物料编码')" label-style="top">
              <debounce-filter-select
                v-model="searchFormModel.itemCode"
                :request="getItemCode"
                :data-source="itemOptions"
                :fields="{ text: 'theCodeName', value: 'customerCode' }"
                :value-template="itemCodeValueTemplate"
                :show-clear-button="true"
                :allow-filtering="true"
                @change="itemCodeChange"
                :placeholder="$t('请选择')"
              ></debounce-filter-select>
            </mt-form-item>
            <mt-form-item prop="siteCode" :label="$t('工厂代码')" label-style="top">
              <!-- <debounce-filter-select
                v-model="searchFormModel.siteCode"
                :request="postSiteFuzzyQuery"
                :data-source="siteOptions"
                :show-clear-button="true"
                :fields="{ text: 'theCodeName', value: 'siteCode' }"
                :value-template="siteCodeValueTemplate"
                @change="siteCodeChange"
                :placeholder="$t('请选择')"
              ></debounce-filter-select> -->
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.siteCode"
                url="/srm-purchase-execute/tenant/common/permission/querySiteList"
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'dimensionNameValue', value: 'dimensionCodeValue' }"
                records-position="data"
                params-key="keyWord"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item :label="$t('供应商代码')" label-style="top">
              <debounce-filter-select
                v-model="searchFormModel.supplierCode"
                :request="getSupplier"
                :data-source="supplierOptions"
                :fields="{ text: 'theCodeName', value: 'supplierCode' }"
                :value-template="supplierCodeValueTemplate"
                :show-clear-button="true"
                :allow-filtering="true"
                @change="supplierCodeChange"
                :placeholder="$t('请选择')"
              ></debounce-filter-select>
            </mt-form-item>
            <mt-form-item prop="sendTime" :label="$t('发货日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.sendTime"
                :placeholder="$t('请选择发货日期')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'sendTime')"
              />
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createTime"
                :placeholder="$t('请选择创建时间')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'createTime')"
              />
            </mt-form-item>
          </mt-form>
        </template>
      </mt-template-page>
      <mt-template-page
        ref="templateRef2"
        :current-tab="currentTab"
        :hidden-tabs="false"
        :permission-obj="permissionObj"
        v-show="tabIndex === 1"
        :template-config="templateConfig2"
        @handleCustomReset="handleSearchReset"
        @handleCustomSearch="handleCustomSearch"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle1"
      >
        <template v-slot:quick-search-form>
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="deliveryCode" :label="$t('送货单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.deliveryCode"
                :placeholder="$t('请输入送货单号')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="sourceDeliveryCode" :label="$t('源送货单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.sourceDeliveryCode"
                :placeholder="$t('请输入源送货单号')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('物料编码')" label-style="top">
              <debounce-filter-select
                v-model="searchFormModel.itemCode"
                :request="getItemCode"
                :data-source="itemOptions"
                :fields="{ text: 'theCodeName', value: 'customerCode' }"
                :value-template="itemCodeValueTemplate"
                :show-clear-button="true"
                :allow-filtering="true"
                @change="itemCodeChange"
                :placeholder="$t('请选择')"
              ></debounce-filter-select>
            </mt-form-item>
            <mt-form-item prop="siteCode" :label="$t('工厂代码')" label-style="top">
              <!-- <debounce-filter-select
                v-model="searchFormModel.siteCode"
                :request="postSiteFuzzyQuery"
                :data-source="siteOptions"
                :show-clear-button="true"
                :fields="{ text: 'theCodeName', value: 'siteCode' }"
                :value-template="siteCodeValueTemplate"
                @change="siteCodeChange"
                :placeholder="$t('请选择')"
              ></debounce-filter-select> -->
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.siteCode"
                url="/srm-purchase-execute/tenant/common/permission/querySiteList"
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'dimensionNameValue', value: 'dimensionCodeValue' }"
                records-position="data"
                params-key="keyWord"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item :label="$t('供应商代码')" label-style="top">
              <debounce-filter-select
                v-model="searchFormModel.supplierCode"
                :request="getSupplier"
                :data-source="supplierOptions"
                :fields="{ text: 'theCodeName', value: 'supplierCode' }"
                :value-template="supplierCodeValueTemplate"
                :show-clear-button="true"
                :allow-filtering="true"
                @change="supplierCodeChange"
                :placeholder="$t('请选择')"
              ></debounce-filter-select>
            </mt-form-item>
            <mt-form-item prop="sendTime" :label="$t('发货日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.sendTime"
                :placeholder="$t('请选择发货日期')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'sendTime')"
              />
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createTime"
                :placeholder="$t('请选择创建时间')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'createTime')"
              />
            </mt-form-item>
          </mt-form>
        </template>
      </mt-template-page>
    </div>
    <mt-page
      class="full-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totals"
      @currentChange="currentChange"
      @sizeChange="sizeChange"
    />
  </div>
</template>

<script>
/* eslint-disable prettier/prettier */
import { i18n } from '@/main.js'

const mainTabList = [
  {
    title: i18n.t('送货单列表')
  },
  {
    title: i18n.t('送货单明细')
  }
]
import { formatTableColumnData } from './config/index'
import {
  ColumnDataTab1,
  ColumnDataTab2,
  Toolbar,
  Tab,
  DeliveryTypeOptions,
  OutsourcedTypeOptions
} from './config/constant'
import { download, getHeadersFileName } from '@/utils/utils'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import dayjs from 'dayjs'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  components: {
    debounceFilterSelect: () => import('@/components/debounceFilterSelect/index.vue')
  },
  data() {
    const currentTab = JSON.parse(localStorage.getItem('tabIndex')) ?? 0

    return {
      buyerOrgValueTemplate: codeNameColumn({
        firstKey: 'groupCode',
        secondKey: 'groupName'
      }), // 采购组
      buyerOrgOptions: [], // 采购组下拉选项
      supplierOptions: [], // 供应商下拉选项
      siteOptions: [], // 工厂 下列选项
      supplierCodeValueTemplate: codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      }),
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      itemCodeValueTemplate: codeNameColumn({
        firstKey: 'customerCode',
        secondKey: 'customerName'
      }),
      itemOptions: [], // 物料 下拉选项
      // 1-新建,2-发货中,3-已完成,4-已取消,5-已关闭
      statusOptions: [
        {
          text: this.$t('新建'),
          value: 1
        },
        {
          text: this.$t('送货中'),
          value: 2
        },
        {
          text: this.$t('已完成'),
          value: 3
        },
        {
          text: this.$t('已取消'),
          value: 4
        },
        {
          text: this.$t('已关闭'),
          value: 5
        }
      ],
      OutsourcedTypeOptions: OutsourcedTypeOptions,
      deliveryOrderTypeOptions: DeliveryTypeOptions,
      searchFormModel: {},
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 5,
        pageSizes: [10, 50, 100, 200, 500, 1000],
        totalRecordsCount: 0,
        totals: 0
      },
      tabIndex: 0,
      tabSource: mainTabList,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      currentTab, // 当前加载的 Tab 默认 0
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'List', permissionCode: 'T_02_0024' },
          { dataPermission: 'Details', permissionCode: 'T_02_0025' }
        ]
      },
      templateConfig1: [
        {
          activatedRefresh: false,
          isUseCustomSearch: true,
          isCustomSearchHandle: true,
          toolbar: { tools: [Toolbar[0], []] },
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId: '78eed870-f203-4b9e-9080-05f53c2243f3',
          grid: {
            enableVirtualization: true,
            customSelection: true, // 使用自定义勾选列
            virtualPageSize: 30,
            selectionSettings: {
              persistSelection: true, // 这个选项很重要，影响到虚拟滚动下能不能操作全选按钮
              type: 'Multiple',
              checkboxOnly: true
            },
            allowPaging: false, // 分页
            // lineSelection: 0, // 选项列
            lineIndex: 0, // 序号列
            frozenColumns: 6, // 冻结第一列
            columnData: formatTableColumnData({
              data: ColumnDataTab1,
              tab: Tab.list,
              type: 'title'
            }),
            dataSource: []
          }
        }
      ],
      templateConfig2: [
        {
          activatedRefresh: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'export',
                  icon: 'icon_solid_Import',
                  permission: ['O_02_1081'],
                  title: this.$t('导出')
                }
              ],
              ['Setting']
            ]
          },
          isUseCustomSearch: true,
          isCustomSearchHandle: true,
          useToolTemplate: false, // 使用预置表格操作按钮(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId: 'a1d3c119-c2b0-4ecd-abf9-bd2ca89691fc',
          grid: {
            enableVirtualization: true,
            customSelection: true, // 使用自定义勾选列
            virtualPageSize: 30,
            selectionSettings: {
              persistSelection: true, // 这个选项很重要，影响到虚拟滚动下能不能操作全选按钮
              type: 'Multiple',
              checkboxOnly: true
            },
            allowPaging: false,
            lineIndex: 0, // 序号列
            columnData: formatTableColumnData({
              data: ColumnDataTab2,
              tab: Tab.details
            }),
            dataSource: []
            // asyncConfig: {
            //   ignoreDefaultSearch: true,
            //   url: `${BASE_TENANT}/buyerOrderDelivery/query/item/page` // 采方送货单-采方查询送货单明细分页
            // }
          }
        }
      ]
    }
  },
  mounted() {
    this.getItemCode({ text: '' })
    setTimeout(() => {
      this.$set(this.pageSettings, 'pageSize', 100)
    }, 500)
  },
  beforeDestroy() {
    localStorage.removeItem('tabIndex')
  },
  methods: {
    // 分页器 - 切换分页
    currentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.searchForm()
    },
    // 分页器 - 切换页大小
    sizeChange(currentPageSize) {
      this.pageSettings.pageSize = currentPageSize
      this.pageSettings.currentPage = 1
      this.searchForm()
    },
    dataTimeChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'S'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[flag + 'E'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[flag + 'S'] = null
        this.searchFormModel[flag + 'E'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleCustomSearch() {
      this.pageSettings.currentPage = 1
      this.searchForm()
    },
    searchForm() {
      this.apiStartLoading()
      const param = {
        ...this.searchFormModel,
        page: {
          current: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        }
      }
      const { newModeDeliverListTAbleDataTV, newModeDeliverListTAbleDetailDataTV } =
        this.$API.receiptAndDelivery
      ;(this.tabIndex === 0 ? newModeDeliverListTAbleDataTV : newModeDeliverListTAbleDetailDataTV)(
        param
      )
        .then((res) => {
          if (res && res.code === 200) {
            setTimeout(() => {
              document.querySelector('.e-content').scrollTop = 0
            }, 20)
            this.$set(
              this['templateConfig' + (this.tabIndex + 1)][0].grid,
              'dataSource',
              res.data?.records ?? []
            )
            this.pageSettings.totals = Math.ceil(
              Number(res.data.total) / this.pageSettings.pageSize
            )
            this.pageSettings.totalRecordsCount = res.data.total
          }
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    handleSearchReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    // 获取主数据-采购组
    getBuyerOrgList(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        groupTypeCode: 'BG001CG'
      }
      this.$API.masterData
        .getbussinessGroup(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.buyerOrgOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.buyerOrgOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 主数据 获取供应商
    getSupplier(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData
        .getSupplier(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.supplierOptions = addCodeNameKeyInList({
              firstKey: 'supplierCode',
              secondKey: 'supplierName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.supplierOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        page: {
          current: 1,
          pages: 0,
          size: 20
        }
      }
      this.$API.receiptAndDelivery
        .getFactoryInfo(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    itemCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.customerId = itemData.id
        this.searchFormModel.customerCode = itemData.customerCode
        this.searchFormModel.customerName = itemData.customerName
      } else {
        this.searchFormModel.customerId = ''
        this.searchFormModel.customerCode = ''
        this.searchFormModel.customerName = ''
      }
    },
    // 工厂 change
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        // this.searchFormModel.siteId = itemData.id;
        this.searchFormModel.siteCode = itemData.siteCode
        this.searchFormModel.siteName = itemData.siteName
      } else {
        // this.searchFormModel.siteId = "";
        this.searchFormModel.siteCode = ''
        this.searchFormModel.siteName = ''
      }
    },
    // 供应商 change
    supplierCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        // this.searchFormModel.supplierId = itemData.id;
        this.searchFormModel.supplierCode = itemData.supplierCode
        this.searchFormModel.supplierName = itemData.supplierName
      } else {
        // this.searchFormModel.supplierId = "";
        this.searchFormModel.supplierCode = ''
        this.searchFormModel.supplierName = ''
      }
    },
    // 采购组 change
    buyerOrgCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        // this.formData.buyerOrgId = itemData.id;
        this.searchFormModel.buyerOrgCode = itemData.groupCode
        this.searchFormModel.buyerOrgName = itemData.groupName
      } else {
        // this.searchFormModel.buyerOrgId = "";
        this.searchFormModel.buyerOrgCode = ''
        this.searchFormModel.buyerOrgName = ''
      }
    },
    // 获取主数据-获取指定组织下指定组织层级节点列表 业务公司
    getItemCode(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text
      }
      this.$API.masterData
        .getCustomer(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.itemOptions = addCodeNameKeyInList({
              firstKey: 'customerCode',
              secondKey: 'customerName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.itemOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // ToolBar
    handleClickToolBar(args) {
      if (args.toolbar.id === 'resetDataByLocal') return
      if (args.toolbar.id == 'export') {
        this.handleExport()
        return
      }
      const { toolbar, gridRef } = args
      // grid.getSelectedRecords() 用于获取当前页面被勾选的数据 ,gridRef.getMtechGridRecords()用于获取被勾选的数据(包括刷新页面之前的勾选)
      // const selectRows = gridRef.getMtechGridRecords()
      let selectRows = []
      gridRef.dataSource.forEach((item) => {
        if (item.customChecked) {
          selectRows.push(item)
        }
      })
      const commonToolbar = [
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]
      if (selectRows.length === 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedId = []
      selectRows.forEach((item) => selectedId.push(item.id))
      const _id = []
      selectRows.map((item) => {
        _id.push(item.id)
      })

      if (toolbar.id === 'print') {
        for (let item of selectRows) {
          if (item.status.label === this.$t('已取消') || item.status.label === this.$t('已关闭')) {
            this.$toast({
              content: this.$t('不可以打印已取消和已关闭的单据'),
              type: 'warning'
            })
            return
          }
        }
        this.$dialog({
          modal: () => import('./components/printDialog.vue'),
          data: {
            title: this.$t('送货单打印纸张选择')
          },
          success: (type) => {
            this.handlePrint(type, _id)
          }
        })
      }
      if (args.toolbar.id === 'synchronous') {
        if (selectRows.length <= 0) {
          this.$toast({ content: this.$t('请选择数据'), type: 'warning' })
          return
        } else {
          this.synchronousWms(selectRows)
        }
      }
    },
    handleExport() {
      let obj = JSON.parse(sessionStorage.getItem(this.templateConfig2[0].gridId))?.visibleCols
      const headerMap = {}
      if (obj !== undefined && obj.length) {
        obj?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      } else {
        this.templateConfig2[0].grid.columnData?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      }
      const params = {
        page: { current: 1, size: 5000 },
        ...this.searchFormModel,
        headerMap
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery.buyerOrderDeliveryQueryExportTv(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handlePrint(type, ids) {
      let params = {
        templateType: type,
        ids
      }
      this.$API.receiptAndDelivery.buyerOrderDeliveryPrintHtmlTv(params).then((res) => {
        if (res && res.code === 500) {
          this.$toast({ content: res.msg, type: 'error' })
          return
        }
        const content = res.data
        this.pdfUrl = window.URL.createObjectURL(
          new Blob([content], { type: 'text/html;charset=utf-8' })
        )
        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = this.pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    print(_id) {
      this.$API.receiptAndDelivery.buyerOrderDeliveryPrintHtmlTv(_id).then((res) => {
        if (res && res.code === 500) {
          this.$toast({ content: res.msg, type: 'error' })
          return
        }
        const content = res.data
        this.pdfUrl = window.URL.createObjectURL(
          new Blob([content], { type: 'text/html;charset=utf-8' })
        )
        // window.open(this.pdfUrl);
        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = this.pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
        return
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    // CellTool
    handleClickCellTool(args) {
      const { tool, data } = args
      if (tool.id === 'DeliveryNoteClose') {
        // 送货单列表-关闭
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认关闭选中的数据？')
          },
          success: () => {
            // 采方送货单-采方关闭送货单
            this.postBuyerOrderDeliveryClose({
              selectedId: [data.id]
            })
          }
        })
      } else if (tool.id === 'DeliveryNoteCancel') {
        // 送货单列表-取消
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认取消选中的数据？')
          },
          success: () => {
            // 采方送货单-采方取消送货单
            this.postBuyerOrderDeliveryCancel({
              selectedId: [data.id]
            })
          }
        })
      }
    },
    // CellTitle
    handleClickCellTitle(args) {
      const { field, data } = args
      if (field === 'deliveryCode') {
        // 送货单号 click
        this.goToDetail({
          headerInfo: data
        })
      }
    },
    // CellTitle
    handleClickCellTitle1(args) {
      const { field, data } = args
      if (field === 'deliveryCode') {
        this.getDeliverDataById(data.deliveryId)
      }
    },
    // 根据id查询主单数据
    getDeliverDataById(id) {
      const params = {
        id: id
      }
      this.$API.receiptAndDelivery
        .getnewModeDeliveyData(params)
        .then((res) => {
          if (res?.code == 200) {
            // 跳转详情页
            this.goToDetail({
              headerInfo: res?.data
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 跳转到 送货单-详情-采方
    goToDetail(data) {
      const tabIndex = this.tabIndex
      // 将 lastTabIndex 放到 localStorage
      localStorage.setItem('lastTabIndex', JSON.stringify(tabIndex))
      const { headerInfo } = data
      const deliverListData = {
        headerInfo // 头部信息
      }
      // 将信息放到 localStorage 详情页 读
      localStorage.setItem('deliverListData', JSON.stringify(deliverListData))
      // 跳转 送货单-详情-采方
      this.$router.push({
        name: 'new-mode-deliver-detail-tv',
        query: {
          timeStamp: new Date().getTime()
        }
      })
    },
    // 采方送货单-采方关闭送货单
    postBuyerOrderDeliveryClose(args) {
      const { selectedId } = args
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postBuyerOrderDeliveryClose(selectedId)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方送货单-采方取消送货单
    postBuyerOrderDeliveryCancel(args) {
      const { selectedId } = args
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postBuyerOrderDeliveryCancel(selectedId)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 刷新当前 Grid
    refreshColumns() {
      // this.$refs.templateRef.refreshCurrentGridData();
      this.handleCustomSearch()
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    },
    // 同步WMS
    synchronousWms(data) {
      let _ids = []
      let _flag = true
      data.forEach((item) => {
        if (item.wmsSyncStatus === 1) {
          _flag = false
        }
        _ids.push(item.id)
      })
      if (!_flag) {
        this.$toast({
          content: this.$t('送货单已成功同步WMS，无需重复同步'),
          type: 'warning'
        })
        return
      }
      this.$API.purchaseCoordination.deliveryPurSynchronousWms(_ids).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('同步成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('同步失败'), type: 'warning' })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
