import { i18n } from '@/main.js'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'
import Vue from 'vue'
export const timeDate = (dataKey, hasTime) => {
  const template = () => {
    return {
      template: Vue.component('date', {
        template: `<div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div><div>{{data[dataKey] | dateFormat}}</div></div>`,
        data: function () {
          return { data: {}, dataKey, hasTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
export const columnData = [
  {
    width: '100',
    field: 'deliveryLineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '120',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          return e.label
        }
        return ''
      }
    }
  },
  {
    width: '110',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '200',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'deliveryQuantity',
    headerText: i18n.t('本次送货数量')
  },
  {
    width: '150',
    field: 'receiveQuantity',
    headerText: i18n.t('收货数量')
  },
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('采购订单号')
  },
  {
    width: '150',
    field: 'lineNo',
    headerText: i18n.t('采购订单行号')
  },
  {
    width: '100',
    field: 'siteCode',
    headerText: i18n.t('工厂')
  },
  {
    field: 'warehouseCode',
    headerText: i18n.t('库存地点'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.warehouseCode}}-{{data.warehouseName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    },
    width: '150'
  },
  {
    width: '150',
    field: 'workCenterName',
    headerText: i18n.t('工作中心'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.workCenterCode}}-{{data.workCenterName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '150',
    field: 'sendAddress',
    headerText: i18n.t('送货地址')
  },
  {
    field: 'unitCode',
    headerText: i18n.t('单位'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.unitCode}}-{{data.unitName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    },
    width: '100'
  },
  {
    width: '100',
    field: 'jit',
    headerText: i18n.t('是否JIT'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },
  {
    width: '150',
    field: 'deliveryNumber',
    headerText: i18n.t('来源单号')
  },
  {
    width: '150',
    field: 'jitDeliveryNumber',
    headerText: i18n.t('JIT编号'),
    searchOptions: {
      renameField: 'deliveryNumber'
    }
  },
  {
    width: '150',
    field: 'demandDate',
    headerText: i18n.t('需求日期'),
    template: timeDate('demandDate', false)
  },
  {
    width: '150',
    field: 'demandTime',
    headerText: i18n.t('需求时间')
  },
  {
    width: '150',
    field: 'receiveTime',
    headerText: i18n.t('收货时间')
  },
  {
    field: 'receiveSupplierCode',
    headerText: i18n.t('收货供应商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.receiveSupplierCode}}-{{data.receiveSupplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    },
    width: '150'
  },
  {
    width: '150',
    field: 'rejectQuantity',
    headerText: i18n.t('拒绝数量')
  },
  {
    width: '150',
    field: 'rejectReason',
    headerText: i18n.t('拒绝原因')
  },
  {
    field: 'transferPlanName',
    headerText: i18n.t('计划员')
  },
  {
    width: '150',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '150',
    field: 'deliveryType',
    headerText: i18n.t('送货单类型'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          return e.label
        }
        return ''
      }
    }
  },
  {
    width: '150',
    field: 'workOrderNo',
    headerText: i18n.t('关联工单号')
  },
  {
    width: '200',
    field: 'saleOrderNo',
    headerText: i18n.t('关联销售订单号')
  },
  {
    width: '200',
    field: 'saleOrderLineNo',
    headerText: i18n.t('关联销售订单行号')
  },
  {
    width: '150',
    field: 'bomCode',
    headerText: i18n.t('BOM号')
  },
  {
    width: '150',
    field: 'productCode',
    headerText: i18n.t('关联产品代码')
  },
  {
    width: '150',
    field: 'processName',
    headerText: i18n.t('工序名称')
  },
  {
    width: '150',
    field: 'batchCode',
    headerText: i18n.t('批次号')
  },
  {
    width: '150',
    field: 'limitQuantity',
    headerText: i18n.t('限量数量')
  },
  {
    width: '150',
    field: 'warehouseClerkName',
    headerText: i18n.t('仓管员')
  },
  {
    width: '150',
    field: 'dispatcherName',
    headerText: i18n.t('调度员')
  },
  {
    width: '90',
    field: 'cancelPersonName',
    headerText: i18n.t('取消人'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '150',
    field: 'cancelTime',
    headerText: i18n.t('取消时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    template: timeDate('cancelTime', true)
  },
  {
    width: '90',
    field: 'closePersonName',
    headerText: i18n.t('关闭人'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '150',
    field: 'closeTime',
    headerText: i18n.t('关闭时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    template: timeDate('closeTime', true)
  },
  {
    width: '150',
    field: 'collectorMark',
    headerText: i18n.t('代收标识')
  },
  {
    width: '150',
    field: 'collectorName',
    headerText: i18n.t('代收人')
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人') // 暂无
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('行备注')
  }
]
