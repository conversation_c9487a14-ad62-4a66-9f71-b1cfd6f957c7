<template>
  <!-- 出入库记录 -->
  <div class="full-height pt20">
    <!-- 列模板 -->
    <mt-template-page
      ref="templateRef"
      @handleClickToolBar="handleClickToolBar"
      :template-config="templateConfig"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {},
  data() {
    return {
      templateConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          activatedRefresh: false,

          toolbar: [
            {
              id: 'listExport',
              icon: 'icon_solid_export',

              title: this.$t('导出')
            }
          ],
          gridId: this.$tableUUID.receiptAndDelivery.inoutRecord.list,
          grid: {
            virtualPageSize: 30,
            showSelected: false,
            selectionSettings: {
              persistSelection: true,
              type: 'Multiple',
              checkboxOnly: true
            },
            enableVirtualization: true,
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [10, 20, 50, 100, 200, 500, 1000],
              totalRecordsCount: 0
            },
            allowPaging: true, // 分页
            lineIndex: 0, // 序号列
            columnData: columnData,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/po/in_out_record/query`, // 订单库存出入库接口 - 获取采方订单主单列表
              ignoreDefaultSearch: true
            }
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id === 'listExport') {
        // 配置-导出
        this.listExport()
      }
    },
    listExport() {
      let obj = JSON.parse(sessionStorage.getItem(this.templateConfig[0].gridId))?.visibleCols
      const headerMap = {}
      if (obj !== undefined && obj.length) {
        obj?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      } else {
        this.templateConfig[0].grid.columnData?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      }
      let queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.deliveryConfig.poExport(params, headerMap).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
        this.$store.commit('endLoading')
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
