import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
import { codeNameColumn } from '@/utils/utils'
import dayjs from 'dayjs'
import Vue from 'vue'

import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, isDateTime, isDate, isTime } = args

  const template = () => {
    return {
      template: Vue.component('timeDateComponent', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <div v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</div>
            <div v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</div>
          </div>
        </div>`,
        data: function () {
          return { data: {}, dataKey, isDateTime, isDate, isTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}

export const columnData = [
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  },
  {
    width: '121',
    field: 'itemVoucherYear',
    headerText: i18n.t('物料凭证年度'),
    searchOptions: {
      operator: 'equal',
      default: dayjs().format('YYYY-MM-DD').substring(0, 4)
    }
  },
  {
    width: '111',
    field: 'receiveCode',
    headerText: i18n.t('物料凭证号'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '125',
    field: 'receiveItemNo',
    headerText: i18n.t('物料凭证行号')
  },
  {
    width: '135',
    field: 'receiveCodeRef',
    headerText: i18n.t('参考物料凭证号')
  },
  {
    width: '120',
    field: 'itemVoucherDate',
    headerText: i18n.t('物料凭证日期'),
    searchOptions: {
      ...MasterDataSelect.timeRange,
      default: [
        new Date(dayjs().subtract(7, 'day').format('YYYY-MM-DD 00:00:00')),
        new Date(dayjs().format('YYYY-MM-DD 23:59:59'))
      ]
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    }
  },
  {
    width: '96',
    field: 'transType',
    headerText: i18n.t('事务类型')
  },
  {
    width: '106',
    field: 'orderCode',
    headerText: i18n.t('采购订单号'),
    searchOptions: {
      operator: 'equal'
    }
  },
  {
    width: '125',
    field: 'lineNo',
    headerText: i18n.t('采购订单行号')
  },
  {
    width: '150',
    field: 'orderTypeCode', // orderTypeName
    template: codeNameColumn({
      firstKey: 'orderTypeCode',
      secondKey: 'orderTypeName'
    }),
    allowFiltering: false,
    headerText: i18n.t('订单类型'),
    searchOptions: {
      ...MasterDataSelect.dictOrderType,
      placeholder: i18n.t('请选择订单类型')
    }
  },
  {
    width: '105',
    field: 'projectName',
    headerText: i18n.t('项目名称')
  },
  {
    width: '105',
    field: 'projectCode',
    headerText: i18n.t('项目编号')
  },
  {
    width: '105',
    field: 'contractCode',
    headerText: i18n.t('合同编号')
  },
  {
    width: '260',
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    template: codeNameColumn({
      firstKey: 'supplierCode',
      secondKey: 'supplierName'
    }),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.supplier,
      operator: 'in'
    }
  },
  {
    width: '0',
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    ignore: true
  },
  {
    width: '431',
    field: 'itemCode',
    headerText: i18n.t('物料'),
    template: codeNameColumn({
      firstKey: 'itemCode',
      secondKey: 'itemName'
    }),
    allowFiltering: false
    // searchOptions: {
    //   ...MasterDataSelect.material,
    // },
  },
  {
    width: '0',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    ignore: true
  },
  {
    width: '100',
    field: 'spec',
    headerText: i18n.t('规格型号')
  },
  {
    width: '100',
    field: 'categoryName',
    headerText: i18n.t('品类')
  },
  {
    width: '70',
    field: 'customName',
    headerText: i18n.t('客户')
  },
  {
    width: '115',
    field: 'relationCustomOrder',
    headerText: i18n.t('销售订单号')
  },
  {
    width: '75',
    field: 'quantity',
    headerText: i18n.t('数量')
  },
  {
    width: '65',
    field: 'unitName',
    headerText: i18n.t('单位'),
    searchOptions: {
      ...MasterDataSelect.unit,
      fields: { text: 'title', value: 'unitName' }
    }
  },
  {
    width: '238',
    field: 'companyCode',
    headerText: i18n.t('公司编号'),
    template: codeNameColumn({
      firstKey: 'companyCode',
      secondKey: 'companyName'
    }),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.businessCompanyPermission
    }
  },
  {
    width: '135',
    field: 'siteCode',
    headerText: i18n.t('地点/工厂'),
    template: codeNameColumn({
      firstKey: 'siteCode',
      secondKey: 'siteName'
    }),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.factoryAddressPermission
    }
  },
  {
    width: '98',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组'),
    searchOptions: {
      ...MasterDataSelect.businessGroup,
      operator: 'equal'
    },

    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },

  {
    width: '98',
    field: 'stockSite',
    headerText: i18n.t('库存地点'),
    searchOptions: {
      ...MasterDataSelect.stockAddress,
      fields: { text: 'title', value: 'locationName' }
    }
  },
  {
    width: '140',
    field: 'receiveCodeRel',
    headerText: i18n.t('关联交货单编号')
  },
  {
    width: '95',
    field: 'costCenter',
    headerText: i18n.t('成本中心'),
    searchOptions: {
      ...MasterDataSelect.costCenter,
      fields: { text: 'title', value: 'costCenterName' }
    }
  },
  {
    width: '95',
    field: 'profitCenter',
    headerText: i18n.t('利润中心'),
    searchOptions: {
      ...MasterDataSelect.profitCenter,
      fields: { text: 'title', value: 'profitCenterName' }
    }
  },
  {
    width: '95',
    field: 'paymentMode',
    headerText: i18n.t('付款方式'),
    searchOptions: {
      ...MasterDataSelect.dictPaymentMode,
      placeholder: i18n.t('请选择付款方式'),
      fields: { text: 'title', value: 'itemName' }
    }
  },
  {
    width: '95',
    field: 'remark',
    headerText: i18n.t('备注说明')
  },
  {
    width: '85',
    field: 'receiveUserName',
    headerText: i18n.t('收货人'),
    searchOptions: {
      ...MasterDataSelect.staff,
      fields: { text: 'title', value: 'employeeName' }
    }
  },
  {
    width: '100',
    field: 'receiveTime',
    headerText: i18n.t('过账日期'),
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return e.map((x) => Number(new Date(x.toString())))
      },
      renameField: 'receiveTime'
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    }
  },
  {
    width: '120',
    field: 'inputDate',
    headerText: i18n.t('凭证创建日期'),
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        //自定义搜索值，规则
        return e.map((x) => Number(new Date(x.toString())))
      },
      renameField: 'inputDate'
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    }
  },
  {
    width: '120',
    field: 'inputTime',
    headerText: i18n.t('凭证创建时间'),
    template: timeDate({ dataKey: 'inputTime', isTime: true })
  },
  {
    width: '95',
    field: 'orderUnitCode',
    headerText: i18n.t('订单单位编码')
  },
  {
    width: '95',
    field: 'orderUnitName',
    headerText: i18n.t('订单单位名称')
  },
  {
    width: '95',
    field: 'unitQuantity',
    headerText: i18n.t('订单单位数量')
  },
  {
    width: '95',
    field: 'deliveryCode',
    headerText: i18n.t('送货单号'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '95',
    field: 'deliveryLineNo',
    headerText: i18n.t('送货单行号')
  }
]
