<!-- eslint-disable -->
<template>
  <div class="cell-changed">
    <mt-input-number
      :id="data.column.field"
      v-model="data[data.column.field]"
      :max="max"
      :min="1"
      :step="1"
      precision="0"
      @input="emitChange"
    />
  </div>
</template>
<!-- eslint-disable -->
<script>
export default {
  data() {
    return {
      data: {}
    }
  },
  computed: {
    max() {
      return (
        Number(this.data?.['numC']?.replace(/,/g, '')) ||
        Number(this.data?.['unClearQuantity']?.replace(/,/g, ''))
      )
    }
  },
  mounted() {},
  methods: {
    emitChange() {
      let _id = this.data['id']
      let _key = this.data.column.field
      let _dataSource = this.$parent?.$parent?.dataSource || []
      _dataSource &&
        _dataSource.map((item) => {
          if (item.id === _id) {
            item[_key] = this.data[_key]
          }
        })
    }
  }
}
</script>
