<!-- eslint-disable prettier/prettier -->
<template>
  <div>
    <!-- 供货计划 - 泛智屏 -->
    <div class="full-height" ref="tableContainer">
      <!-- 列模板 -->
      <mt-template-page
        ref="templateRef"
        :hidden-tabs="true"
        :basic-expand="false"
        :template-config="componentConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleCustomReset="handleCustomReset"
        @handleCustomSearch="handleCustomSearch"
        @cellEdit="cellEdit"
      >
        <template v-slot:quick-search-form>
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <!-- <mt-form-item :label="$t('序列号')" label-style="top">
            <mt-input v-model="searchFormModel.serialNumber"></mt-input>
          </mt-form-item> -->
            <!-- <mt-form-item prop="companyCode" :label="$t('公司编码')" label-style="top">
              <mt-input v-model="searchFormModel.companyCode" />
            </mt-form-item> -->
            <mt-form-item prop="siteCode" :label="$t('工厂编码')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.siteCodeList"
                :url="$API.masterData.getSiteAuthFuzzyUrl"
                multiple
                :placeholder="$t('请选择')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                params-key="fuzzyParam"
                records-position="data"
              />
            </mt-form-item>
            <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
              <mt-input v-model="searchFormModel.itemCode"></mt-input>
            </mt-form-item>
            <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
              <mt-input v-model="searchFormModel.itemName"></mt-input>
            </mt-form-item>
            <mt-form-item prop="demandDate" :label="$t('需求日期')" label-style="top">
              <mt-date-range-picker
                style="flex: 1"
                v-model="searchFormModel.demandDate"
                :show-clear-button="true"
                :placeholder="$t('需求日期')"
                @change="(e) => demandDateChange(e, 'demandDate')"
              />
            </mt-form-item>
            <!-- <mt-form-item
              prop="virtualSupplierCodeList"
              :label="$t('虚拟供应商代码')"
              label-style="top"
            >
              <mt-multi-select
                style="flex: 1"
                :show-clear-button="true"
                v-model="searchFormModel.virtualSupplierCodeList"
                :data-source="virtualSupplierCodeList"
                :placeholder="$t('请选择')"
                :allow-filtering="true"
                :fields="{ text: 'codeAndName', value: 'groupSupplierCode' }"
                filter-type="Contains"
              ></mt-multi-select>
            </mt-form-item> -->
            <mt-form-item prop="bigVersionNo" :label="$t('版本号')" label-style="top">
              <mt-input v-model="searchFormModel.bigVersionNo"></mt-input>
            </mt-form-item>
          </mt-form>
        </template>
      </mt-template-page>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totals"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>
<!-- eslint-disable prettier/prettier -->
<!-- eslint-disable prettier/prettier -->
<!-- eslint-disable prettier/prettier -->
<script>
import { ColumnDataTab4 } from '../config/constant'
// import { BASE_TENANT } from "@/utils/constant";
import { download, getHeadersFileName } from '@/utils/utils'
import dayjs from 'dayjs'
// import collapseSearch from '@/components/collapseSearch'

export default {
  components: {},
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  props: {
    zeroFilter: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      pageSettings: {
        currentPage: 1,
        pageCount: 5,
        pageSize: 10, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totals: 0, // 页数
        pageSizes: [10, 20, 50, 100, 200, 1000]
      },
      searchFormModel: {
        serialNumber: null,
        companyCode: null,
        siteCode: null,
        itemCode: null,
        itemName: null,
        orderCode: null,
        demandDate: [new Date(), new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0)],
        demandDateStart: dayjs(dayjs().startOf('date').format('YYYY-MM-DD HH:mm:ss')).valueOf(),
        demandDateEnd: dayjs(dayjs().endOf('month').format('YYYY-MM-DD HH:mm:ss')).valueOf()
      },
      apiWaitingQuantity: 0, // 调用的api正在等待数
      initialSort: {
        columns: [{ field: 'remainingQty', direction: 'Descending' }]
      },
      editData: [], // 行编辑过的数据 { id, index, key, value }
      zeroFilterSwitch: 1, // 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      zeroFilterPlan: 1, // 交货计划 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      zeroFilterOrder: 1, // 采购订单 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      zeroFilterJit: 1, // JIT 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      componentConfig: [
        {
          title: this.$t('交货计划'),
          dataPermission: 'Plan',
          permissionCode: 'T_02_0156',
          activatedRefresh: false,
          isUseCustomSearch: true,
          isCustomSearchHandle: true,
          toolbar: [
            [
              {
                id: 'TableExport',
                icon: 'icon_solid_export',
                // permission: ['O_02_0635'],
                title: this.$t('导出')
              },
              {
                id: 'PreCreate',
                icon: 'icon_solid_createProject',
                // permission: ['O_02_0635'],
                title: this.$t('批量预创建发货单')
              }
            ],
            ['Setting']
          ],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          gridId: '1601716d-ef22-439c-8d52-24312b5166e7',
          grid: {
            virtualPageSize: 30,
            showSelected: false,
            selectionSettings: {
              persistSelection: true,
              type: 'Multiple',
              checkboxOnly: true
            },
            enableVirtualization: true,
            allowPaging: false,
            lineSelection: 0, // 选项列 显示勾选
            lineIndex: 1, // 序号列 显示序列号
            columnData: ColumnDataTab4,
            dataSource: []
            // frozenColumns: 2
          }
        }
      ],
      virtualSupplierCodeList: []
    }
  },
  mounted() {
    this.getvirtualSupplierCodeList()
    setTimeout(() => {
      this.$set(this.pageSettings, 'pageSize', 20)
    }, 500)
  },
  watch: {
    zeroFilter() {
      this.handleCustomSearch()
    }
  },
  methods: {
    getvirtualSupplierCodeList() {
      this.$API.receiptAndDelivery
        .getPingcaiGroupList({ page: { current: 1, size: 10000 } })
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.virtualSupplierCodeList = Array.from(
              new Set(data.records.map((item) => item.groupSupplierCode))
            )
              .map((code) => {
                const item = data.records.find((a) => a.groupSupplierCode === code)
                return {
                  ...item,
                  codeAndName: `${item.groupSupplierCode} - ${item.groupSupplierName}`
                }
              })
              .filter((i) => i.groupSupplierCode && i.groupSupplierName)
          }
        })
    },
    // 创建日期选择
    demandDateChange(e, prefix) {
      if (e.startDate) {
        this.searchFormModel[prefix + 'Start'] = dayjs(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        ).valueOf()
        this.searchFormModel[prefix + 'End'] = dayjs(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        ).valueOf()
      } else {
        this.searchFormModel[prefix + 'Start'] = null
        this.searchFormModel[prefix + 'End'] = null
      }
    },
    // 查询条件重置
    handleCustomReset() {
      for (let key in this.searchFormModel) {
        if (key === 'demandDate') {
          this.$set(this.searchFormModel, key, null)
          this.searchFormModel['demandDateStart'] = null
          this.searchFormModel['demandDateEnd'] = null
        } else {
          this.searchFormModel[key] = null
        }
      }
    },
    // 列表查询
    handleCustomSearch() {
      this.$store.commit('startLoading')
      const param = {
        pageNum: this.pageSettings.currentPage,
        pageSize: this.pageSettings.pageSize,
        ...this.searchFormModel
      }
      param.zeroFilter = this.zeroFilter
      const _Api = this.$API.receiptAndDelivery.getPlanSupScreenList
      _Api(param)
        .then((res) => {
          if (res && res.code === 200) {
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
            this.$refs.templateRef.refreshCurrentGridData()
            setTimeout(() => {
              document.querySelector('.e-content').scrollTop = 0
            }, 20)
            const _list = res.data?.records.map((item) => {
              if (item['remainCreateQty']) {
                item['supplierNum'] = Number(item['remainCreateQty'].replace(/,/g, ''))
              }
              if (!item.outsourcedTypeDesc && item.processorCode) {
                item['outsourcedTypeDesc'] = this.$t('标准委外')
              }
              return item
            })
            this.$set(this.componentConfig[0].grid, 'columnData', [])
            this.$nextTick(() => {
              this.$set(this.componentConfig[0].grid, 'columnData', ColumnDataTab4)
            })
            this.$set(this.componentConfig[0].grid, 'dataSource', _list)
            this.pageSettings.totals = Math.ceil(
              Number(res.data.total) / this.pageSettings.pageSize
            )
            this.pageSettings.totalRecordsCount = res.data.total
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    // 切换页码
    handleCurrentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.handleCustomSearch()
    },
    // 更换页大小
    handleSizeChange(pageSize) {
      this.pageSettings.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // ToolBar
    handleClickToolBar(args) {
      const { toolbar, gridRef } = args
      const selectRows = gridRef.getMtechGridRecords()
      const selectedId = []
      selectRows.forEach((item) => selectedId.push(item.id))
      if (toolbar.id === 'TableExport') {
        // 导出
        this.handleExport()
      } else if (toolbar.id === 'PreCreate') {
        // 批量创建送货单
        this.handleBatchPreCreateDelivery(selectRows)
      }
    },
    // CellTool
    handleClickCellTool() {},
    // 导出
    handleExport() {
      let obj = JSON.parse(sessionStorage.getItem(this.componentConfig[0].gridId))?.visibleCols
      const headerMap = {}
      if (obj !== undefined && obj.length) {
        obj?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'serialNumber' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      } else {
        ColumnDataTab4?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'serialNumber' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      }
      const param = {
        ...this.searchFormModel,
        headerMap
      }
      this.apiStartLoading()
      this.$API.receiptAndDelivery.exportScreenSupData(param).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 批量创建送货单
    handleBatchPreCreateDelivery(selectRows) {
      const selectRowsData = this.handleMatchEditDataById(selectRows, this.editData)

      // 校验 发货数量
      if (!this.checkTheNumberOfShipments(selectRowsData)) {
        // 校验失败
        return
      }

      const params = []
      selectRowsData.forEach((item) => {
        if (item.supplierNum > 0) {
          params.push({
            id: item.id,
            supplierNum: item.supplierNum, // 发货数量 前端定义
            requiredDeliveryDate: item.requiredDeliveryDate,
            supplierTenantId: item.supplierTenantId // 供应商租户id
          })
        }
      })

      if (params.length === 0) {
        this.$toast({ content: this.$t('未填写发货数量'), type: 'warning' })
        return
      }

      // 交货计划-预创建送货单
      this.postSupDeliveryPreCreate(params)
    },
    // 校验 发货数量
    checkTheNumberOfShipments(rowsData) {
      let isValid = true
      let isCheckPreDeliveryQty = false // 校验 采购订单tab 待发货数量
      let isCheckRemainingQty = false // 校验 交货计划tab、JIT tab 剩余可创建数量
      rowsData.forEach((itemRowData) => {
        if (itemRowData.preDeliveryQty !== undefined && !isNaN(itemRowData.preDeliveryQty)) {
          isCheckPreDeliveryQty = true
          // 采购订单tab 校验 待发货数量
          if (itemRowData.supplierNum > itemRowData.numC) {
            isValid = false
          }
        } else if (itemRowData.remainingQty !== undefined && !isNaN(itemRowData.remainingQty)) {
          isCheckRemainingQty = true
          // 交货计划tab、JIT tab 剩余可创建数量
          if (itemRowData.supplierNum > itemRowData.remainingQty) {
            isValid = false
          }
        }
      })
      if (isCheckPreDeliveryQty && !isValid) {
        this.$toast({
          content: this.$t('发货数量不可大于待发货数量'),
          type: 'warning'
        })
      } else if (isCheckRemainingQty && !isValid) {
        this.$toast({
          content: this.$t('发货数量不可大于剩余可创建数量'),
          type: 'warning'
        })
      }
      return isValid
    },
    // selectRows 与 editData 匹配 id，返回正确的数据，因为表格中 template 的数据与 dataSource 的数据不一致
    handleMatchEditDataById(selectRows, editData) {
      const result = []
      selectRows.forEach((item) => {
        editData.forEach((editItem) => {
          if (item.id === editItem.id) {
            item[editItem.key] = editItem.value
          }
        })
        result.push(item)
      })
      return result
    },
    // 行编辑
    cellEdit(e) {
      const { id, index, key, value } = e
      if (id !== undefined) {
        // 如果是编辑行
        const editIndex = this.editData.findIndex((item) => item.id === id && item.key === key)
        if (editIndex >= 0) {
          // 更新编辑的数据
          this.editData[editIndex].value = value
        } else {
          // 保存编辑的数据，value 为空也存
          this.editData.push({
            id,
            key,
            index,
            value
          })
        }
      }
    },
    // 跳转到批量创建送货单
    goToBatchDeliverySupplier(args) {
      const { data, params } = args
      // 预创建订单数据 保存到 localStorage
      localStorage.setItem('batchDeliverySupplierDataTv', JSON.stringify(data))
      // 预创建订单请求参数 保存到 localStorage
      localStorage.setItem('batchDeliverySupplierParamsTv', JSON.stringify(params))
      // 将 lastTabIndex 放到 localStorage
      localStorage.setItem('lastTabIndex', JSON.stringify(0))
      this.$store.commit('setPreCreateParams', params)
      // 页面跳转
      this.editData = []
      this.$router.push({
        name: 'batch-delivery-supplier-screen-tv',
        query: {
          type: 3, // 页面类型
          timeStamp: new Date().getTime()
        }
      })
    },
    // 供方收发货供货计划-订单预创建送货单
    postSupplierDeliverySupplyPlanOrderPreCreate(params) {
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanOrderPreCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res && res?.code == 200) {
            // 跳转-批量创建送货单
            this.goToBatchDeliverySupplier({ data: res.data || [], params })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-交货计划预创建送货单
    postSupDeliveryPreCreate(params) {
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupDeliveryPreCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 跳转-批量创建送货单
            this.goToBatchDeliverySupplier({ data: res.data || [], params })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-JIT预创建送货单
    postSupplierDeliverySupplyPlanJitPreCreate(params) {
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanJitPreCreateTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res && res?.code == 200) {
            // this.$toast({
            //   content: this.$t("操作成功"),
            //   type: "success",
            // });
            // 跳转-批量创建送货单
            this.goToBatchDeliverySupplier({ data: res.data || [], params })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-JIT创建无订单送货单
    postSupplierDeliverySupplyPlanJitCreateNoOrder(params) {
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanJitCreateNoOrder(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            // 刷新-当前表格数据
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-交货计划创建无订单送货单
    postSupplierDeliverySupplyPlanPlanCreateNoOrder(params) {
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanPlanCreateNoOrder(params)
        .then((res) => {
          this.apiEndLoading()
          if (res && res?.code == 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            // 刷新-当前表格数据
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('todoDetail')
  }
}
</script>

<style lang="scss" scoped>
// 表格容器
#table-container {
  position: relative;

  // 表格数据视图
  /deep/ .e-gridcontent > .e-content {
    height: 0px;
    transition: height 0.5s ease;
  }

  // 分页
  /deep/ .mt-pagertemplate {
    margin: 10px 0 10px !important;
  }
}
// 显示不可创建送货单数据
.zero-filter-switch {
  text-align: right;
}
</style>
