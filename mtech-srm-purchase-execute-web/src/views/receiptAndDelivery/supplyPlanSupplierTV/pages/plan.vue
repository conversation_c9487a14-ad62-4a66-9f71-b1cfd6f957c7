<!-- 采方 - 供货计划-泛智屏 - 交货计划 -->
<template>
  <div class="full-height">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleCustomReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('序列号')" label-style="top">
          <mt-input
            v-model="searchFormModel.serialNumber"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="siteCode" :label="$t('工厂编码')" label-style="top">
          <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.siteCodeList"
            :url="$API.masterData.getSiteAuthFuzzyUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            params-key="fuzzyParam"
            records-position="data"
          />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemName"
            :placeholder="$t('支持模糊搜索')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="demandDate" :label="$t('需求日期')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.demandDate"
            :show-clear-button="true"
            :open-on-focus="true"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            @change="(e) => demandDateChange(e, 'demandDate')"
          />
        </mt-form-item>
        <mt-form-item prop="jitFlag" :label="$t('是否JIT')" label-style="top">
          <mt-select
            v-model="searchFormModel.jitFlag"
            :data-source="[
              { value: 0, text: $t('否'), cssClass: '' },
              { value: 1, text: $t('是'), cssClass: '' }
            ]"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="saleOrderNo" :label="$t('销售订单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.saleOrderNo"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="workCenterCode" :label="$t('工作中心')" label-style="top">
          <mt-input
            v-model="searchFormModel.workCenterCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="plannerName" :label="$t('计划员')" label-style="top">
          <mt-input
            v-model="searchFormModel.plannerName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="bigVersionNo" :label="$t('版本号')" label-style="top">
          <mt-select
            v-model="searchFormModel.bigVersionNo"
            :data-source="versionList"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="processorCode" :label="$t('加工商编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.processorCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="processorName" :label="$t('加工商名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.processorName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="02d65257-24b7-4996-a094-ca216f7677cc"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>
<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import pagingMixin from '@/mixins/paging.js'

export default {
  components: { ScTable, CollapseSearch },
  mixins: [mixin, pagingMixin],
  props: {
    zeroFilter: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      type: 'plan',
      isInitQuery: false,
      searchFormModel: {
        bigVersionNo: ''
      },
      versionList: []
    }
  },
  mounted() {
    this.buyerGoodsDemandPlanInfoQueryVersionTv()
  },
  methods: {
    // 获取table数据
    async getTableData() {
      const params = {
        pageNum: this.pageInfo.current,
        pageSize: this.pageInfo.size,
        zeroFilter: this.zeroFilter,
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.receiptAndDelivery
        .getplanByDeliverList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const list = res.data?.records?.map((item) => {
          if (item.remainCreateQty || item.remainCreateQty === 0) {
            item.supplierNum = Number(item.remainCreateQty.replace(/,/g, ''))
          }
          return item
        })
        this.tableData = list
        this.total = res.data.total
      }
    },
    buyerGoodsDemandPlanInfoQueryVersionTv() {
      this.$API.deliverySchedule.supplierGoodsDemandPlanInfoQueryVersionTv().then((res) => {
        const { data, code } = res
        if (code === 200) {
          this.versionList = data
          // this.searchFormModel.bigVersionNo = data[0]
        }
      })
    },
    // 查询条件重置
    handleCustomReset() {
      for (let key in this.searchFormModel) {
        if (key) {
          this.searchFormModel[key] = null
          // if (key === 'bigVersionNo') {
          //   this.searchFormModel[key] = this.versionList[0]
          // }
        }
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      switch (e.code) {
        case 'batchCreateDelivery':
          this.handleBatchCreateDelivery(0, selectedRecords)
          break
        case 'batchImportDelivery':
          this.handleBatchImportDelivery()
          break
        case 'export':
          this.handleExport('plan', selectedRecords)
          break
        default:
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
