import { download, getHeadersFileName } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  data() {
    return {
      companyList: [],
      toolbar: [
        { code: 'batchCreateDelivery', name: this.$t('批量创建送货单'), status: 'info' },
        { code: 'batchImportDelivery', name: this.$t('批量导入送货单'), status: 'info' },
        { code: 'export', name: this.$t('导出'), status: 'info' }
      ],
      searchFormModel: {},
      editConfig: {
        trigger: 'click',
        mode: 'row',
        showStatus: true
      },
      tableData: [],
      loading: false
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      switch (this.type) {
        case 'plan':
          return [
            {
              minWidth: 50,
              type: 'checkbox',
              fixed: 'left'
            },
            {
              minWidth: 60,
              type: 'seq',
              title: this.$t('序号'),
              fixed: 'left'
            },
            {
              field: 'statusDesc',
              title: this.$t('状态'),
              minWidth: 80
            },
            {
              field: 'itemCode',
              title: this.$t('物料编码'),
              minWidth: 130
            },
            {
              field: 'siteCode',
              title: this.$t('工厂编码'),
              minWidth: 100
            },
            {
              field: 'requiredDeliveryDate',
              title: this.$t('需求日期'),
              minWidth: 100
            },
            {
              field: 'numC',
              title: this.$t('需求数量'),
              minWidth: 100
            },
            {
              field: 'supplierNum',
              title: this.$t('本次发货数量'),
              minWidth: 200,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  const max = Number(row.remainCreateQty?.replace(/,/g, ''))
                  return [
                    <vxe-input
                      v-model={row.supplierNum}
                      type='integer'
                      clearable
                      min={0}
                      max={max}
                    />
                  ]
                }
              }
            },
            {
              field: 'remainCreateQty',
              title: this.$t('剩余可创建数量'),
              minWidth: 150
            },
            {
              field: 'deliveryQty',
              title: this.$t('已发货数量'),
              minWidth: 150
            },
            {
              field: 'transitQty',
              title: this.$t('在途数量'),
              minWidth: 150
            },
            {
              field: 'receivedQuantity',
              title: this.$t('已收货数量'),
              minWidth: 150
            },
            {
              field: 'unClearQuantity',
              title: this.$t('订单未清数量'),
              minWidth: 150
            },
            {
              field: 'bigVersionNo',
              title: this.$t('版本号'),
              minWidth: 100
            },
            {
              field: 'jitFlagDesc',
              title: this.$t('是否JIT'),
              minWidth: 100
            },
            {
              field: 'workCenter',
              title: this.$t('工作中心'),
              minWidth: 150
            },
            {
              field: 'saleOrderNo',
              title: this.$t('销售订单号'),
              minWidth: 150
            },
            {
              field: 'batchFlagDesc',
              title: this.$t('是否按批次'),
              minWidth: 150
            },
            {
              field: 'serialNo',
              title: this.$t('序列号'),
              minWidth: 200
            },
            {
              field: 'itemName',
              title: this.$t('物料名称'),
              minWidth: 220
            },
            {
              field: 'siteName',
              title: this.$t('工厂名称'),
              minWidth: 200
            },
            {
              field: 'mrpArea',
              title: this.$t('计划区域'),
              minWidth: 120
            },
            {
              field: 'lineBody',
              title: this.$t('线体'),
              minWidth: 80
            },
            {
              field: 'flag',
              title: this.$t('标识'),
              minWidth: 80
            },
            {
              field: 'projectTextBatch',
              title: this.$t('项目文本批次'),
              minWidth: 150
            },
            {
              field: 'deliveryMethodDesc',
              title: this.$t('配送方式'),
              minWidth: 150
            },
            {
              field: 'outsourcedTypeDesc',
              title: this.$t('委外方式'),
              minWidth: 150
            },
            {
              field: 'processorCode',
              title: this.$t('加工商编号'),
              minWidth: 150
            },
            {
              field: 'processorName',
              title: this.$t('加工商名称'),
              minWidth: 200
            },
            {
              field: 'orderCode',
              title: this.$t('采购订单号'),
              minWidth: 150
            },
            {
              field: 'orderLineNo',
              title: this.$t('采购订单行号'),
              minWidth: 150
            },
            {
              field: 'deliverTypeDesc',
              title: this.$t('送货类型'),
              minWidth: 100
            },
            {
              field: 'releaseFlag',
              title: this.$t('下达'),
              minWidth: 65
            },
            {
              field: 'stockQty',
              title: this.$t('库存数量'),
              minWidth: 150
            },
            {
              field: 'limitQty',
              title: this.$t('限量数量'),
              minWidth: 150
            },
            {
              field: 'plannerName',
              title: this.$t('计划员'),
              minWidth: 80
            },
            {
              field: 'saleOrderRowCode',
              title: this.$t('销售订单行号'),
              minWidth: 150
            },
            {
              field: 'bomNo',
              title: this.$t('BOM号'),
              minWidth: 150
            },
            {
              field: 'checkDate',
              title: this.$t('确认日期'),
              minWidth: 150
            },
            {
              field: 'supplierRemark',
              title: this.$t('供应商备注'),
              minWidth: 150
            },
            {
              field: 'buyerRemark',
              title: this.$t('采购方备注'),
              minWidth: 150
            },
            {
              field: 'wmsInBoundTime',
              title: this.$t('WMS入库时间'),
              minWidth: 150
            }
            // {
            //   field: 'wmsInBoundNum',
            //   title: this.$t('WMS入库数量'),
            //   minWidth: 150
            // }
          ]
        case 'order':
          return [
            {
              minWidth: 50,
              type: 'checkbox',
              fixed: 'left'
            },
            {
              minWidth: 60,
              type: 'seq',
              title: this.$t('序号'),
              fixed: 'left'
            },
            {
              field: 'orderCode',
              title: this.$t('采购订单号'),
              minWidth: 150
            },
            {
              field: 'itemNo',
              title: this.$t('采购订单行号'),
              minWidth: 150
            },
            {
              field: 'itemCode',
              title: this.$t('物料编码'),
              minWidth: 120
            },
            {
              field: 'siteCode',
              title: this.$t('工厂编码')
            },
            {
              field: 'requiredDeliveryDate',
              title: this.$t('需求日期'),
              minWidth: 100
            },
            {
              field: 'quantity',
              title: this.$t('订单数量')
            },
            {
              field: 'supplierNum',
              title: this.$t('本次发货数量'),
              minWidth: 200,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  const max = Number(row.unClearQuantity?.replace(/,/g, ''))
                  return [
                    <vxe-input
                      v-model={row.supplierNum}
                      type='integer'
                      clearable
                      min={0}
                      max={max}
                    />
                  ]
                }
              }
            },
            {
              field: 'unClearQuantity',
              title: this.$t('未清数量')
            },
            {
              field: 'deliveryQty',
              title: this.$t('已发货数量')
            },
            {
              field: 'transitQty',
              title: this.$t('在途数量')
            },
            {
              field: 'receiveQty',
              title: this.$t('已入库数量')
            },
            {
              field: 'receiveAddress',
              title: this.$t('收货地址'),
              minWidth: 300
            },
            {
              field: 'receiverContactName',
              title: this.$t('收货联系人'),
              minWidth: 120
            },
            {
              field: 'receiverContact',
              title: this.$t('收货联系方式'),
              minWidth: 120
            },
            {
              field: 'companyCode',
              title: this.$t('公司编码'),
              minWidth: 100
            },
            {
              field: 'companyName',
              title: this.$t('公司名称')
            },
            {
              field: 'siteName',
              title: this.$t('工厂名称')
            },
            {
              field: 'itemName',
              title: this.$t('物料名称'),
              minWidth: 250
            },
            {
              field: 'warehouseCode',
              title: this.$t('库存地点')
            },
            {
              field: 'saleOrderNo',
              title: this.$t('关联销售订单'),
              minWidth: 200
            },
            {
              field: 'saleOrderRowCode',
              title: this.$t('关联销售订单行号'),
              minWidth: 150
            },
            {
              field: 'batchFlagDesc',
              title: this.$t('是否按批次'),
              minWidth: 150
            },
            {
              field: 'projectTextBatch',
              title: this.$t('项目文本批次')
            },
            {
              field: 'projectRowText',
              title: this.$t('行项目文本')
            },
            {
              field: 'buyerRemark',
              title: this.$t('采购方备注')
            },
            {
              field: 'supplierRemark',
              title: this.$t('供应商备注')
            }
          ]
        case 'JIT':
          return [
            {
              minWidth: 50,
              type: 'checkbox',
              fixed: 'left'
            },
            {
              minWidth: 60,
              type: 'seq',
              title: this.$t('序号'),
              fixed: 'left'
            },
            {
              field: 'statusDesc',
              title: this.$t('状态'),
              minWidth: 80
            },
            {
              field: 'itemCode',
              title: this.$t('物料编码'),
              minWidth: 130
            },
            {
              field: 'siteCode',
              title: this.$t('工厂编码'),
              minWidth: 100
            },
            {
              field: 'workCenterCode',
              title: this.$t('工作中心编码'),
              minWidth: 150
            },

            {
              field: 'requiredDeliveryDate',
              title: this.$t('需求日期'),
              minWidth: 150
            },
            {
              field: 'requiredDeliveryTime',
              title: this.$t('需求时间'),
              minWidth: 100
            },
            {
              field: 'supplierNum',
              title: this.$t('本次发货数量'),
              minWidth: 200,
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  const max = Number(row.remainCreateQty?.replace(/,/g, ''))
                  return [
                    <vxe-input
                      v-model={row.supplierNum}
                      type='integer'
                      clearable
                      min={0}
                      max={max}
                    />
                  ]
                }
              }
            },
            {
              field: 'remainCreateQty',
              title: this.$t('剩余可创建数量'),
              minWidth: 150
            },
            {
              field: 'deliveryQty',
              title: this.$t('已发货数量'),
              minWidth: 150
            },
            {
              field: 'transitQty',
              title: this.$t('在途数量'),
              minWidth: 150
            },
            {
              field: 'receivedQuantity',
              title: this.$t('已收货数量'),
              minWidth: 150
            },
            {
              field: 'unClearQuantity',
              title: this.$t('订单未清数量'),
              minWidth: 150
            },
            {
              field: 'saleOrderNo',
              title: this.$t('销售订单号'),
              minWidth: 150
            },
            {
              field: 'batchFlagDesc',
              title: this.$t('是否按批次'),
              minWidth: 150
            },
            {
              field: 'serialNo',
              title: this.$t('序列号'),
              minWidth: 200
            },
            {
              field: 'itemName',
              title: this.$t('物料名称'),
              minWidth: 220
            },
            {
              field: 'siteName',
              title: this.$t('工厂名称'),
              minWidth: 200
            },
            {
              field: 'workCenter',
              title: this.$t('工作中心名称'),
              minWidth: 150
            },
            {
              field: 'senderAddress',
              title: this.$t('收货地址'),
              minWidth: 200
            },
            {
              field: 'receiverContactName',
              title: this.$t('收货联系人'),
              minWidth: 120
            },
            {
              field: 'receiverContact',
              title: this.$t('收货联系方式'),
              minWidth: 120
            },
            {
              field: 'plannerName',
              title: this.$t('调度员'),
              minWidth: 80
            },
            {
              field: 'deliverTypeDesc',
              title: this.$t('送货类型'),
              minWidth: 100
            },
            {
              field: 'jitFlagDesc',
              title: this.$t('是否JIT'),
              minWidth: 100
            },
            {
              field: 'projectTextBatch',
              title: this.$t('项目文本批次'),
              minWidth: 150
            },
            {
              field: 'supplierRemark',
              title: this.$t('供应商备注'),
              minWidth: 150
            },
            {
              field: 'buyerRemark',
              title: this.$t('采购方备注'),
              minWidth: 150
            },
            {
              field: 'wmsInBoundTime',
              title: this.$t('WMS入库时间'),
              minWidth: 150
            }
            // {
            //   field: 'wmsInBoundNum',
            //   title: this.$t('WMS入库数量'),
            //   minWidth: 150
            // }
          ]
        default:
          return []
      }
    }
  },
  watch: {
    zeroFilter() {
      this.handleSearch()
    }
  },
  mounted() {
    this.type === 'order' && this.getCompanyList()
  },
  methods: {
    // 获取公司下拉列表
    async getCompanyList() {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      const res = await this.$API.masterData.getCompanyByCode([userInfo.accountName])
      if (res.code === 200) {
        res.data.forEach((item) => {
          item.text = item.organizationCode + '-' + item.organizationName
        })
        this.companyList = res.data
      }
    },
    // 创建日期选择
    demandDateChange(e, prefix) {
      if (e.startDate) {
        this.searchFormModel[prefix + 'Start'] = dayjs(e.startDate).valueOf()
        this.searchFormModel[prefix + 'End'] = dayjs(e.endDate).valueOf()
      } else {
        this.searchFormModel[prefix + 'Start'] = null
        this.searchFormModel[prefix + 'End'] = null
      }
    },
    // 批量创建送货单
    handleBatchCreateDelivery(type, list) {
      if (!list.length) {
        this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
        return
      }
      const params = []
      list.forEach((item) => {
        const { id, supplierNum, requiredDeliveryDate, supplierTenantId } = item
        if (supplierNum > 0) {
          params.push({
            id,
            supplierNum,
            requiredDeliveryDate,
            supplierTenantId
          })
        }
      })
      if (params.length < list.length) {
        this.$toast({ content: this.$t('本次发货数量须大于0，请检查！'), type: 'warning' })
        return
      }

      this.$store.commit('setPreCreateParams', params)
      this.$router.push({
        name: 'batch-delivery-supplier-tv',
        query: { type, timeStamp: Date.now() }
      })
    },
    // 批量导入送货单
    handleBatchImportDelivery() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('批量导入送货单'),
          importApi: this.$API.receiptAndDelivery.SupplierOrderDeliveryImport,
          downloadTemplateApi: this.$API.receiptAndDelivery.SupplierOrderDeliveryDownloadTemplate,
          paramsKey: 'excel'
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    // 导出
    async handleExport(type, list) {
      const methodMap = {
        plan: 'postSupplierDeliverySupplyPlanExportTv',
        order: 'postSupplierDeliverySupplyOrderExportTv',
        JIT: 'postSupplierDeliverySupplyJitExportTv'
      }
      const { visibleColumn } = this.$refs.sctableRef.$refs.xGrid.getTableColumn()
      const headerMap = {}
      visibleColumn.forEach((i) => {
        if (i.field && i.title) {
          headerMap[i.field] = i.title
        }
      })
      const params = {
        pageNum: 1,
        pageSize: 100000,
        exportList: [...list],
        headerMap,
        zeroFilter: this.zeroFilter,
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      const res = await this.$API.receiptAndDelivery[methodMap[type]](params)
      if (res.data) {
        this.$store.commit('endLoading')
        this.$toast({ type: 'success', content: this.$t('正在导出，请稍后！') })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    }
  }
}
