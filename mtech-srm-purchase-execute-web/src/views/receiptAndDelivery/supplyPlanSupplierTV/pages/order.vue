<template>
  <div class="full-height">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="orderCode" :label="$t('订单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.orderCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="saleOrderNo" :label="$t('销售订单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.saleOrderNo"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="companyCode" :label="$t('公司编码')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.companyCodeList"
            type="multipleChoice"
            :data-source="companyList"
            :fields="{ text: 'text', value: 'organizationCode' }"
            :placeholder="$t('请选择')"
            :show-select-all="true"
            :show-clear-button="true"
            :allow-filtering="true"
          />
        </mt-form-item>
        <mt-form-item prop="siteCode" :label="$t('工厂编码')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.siteCodeList"
            :url="$API.masterData.getSiteAuthFuzzyUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            params-key="fuzzyParam"
            records-position="data"
          />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemName"
            :placeholder="$t('支持模糊搜索')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="warehouseCode" :label="$t('库存地点')" label-style="top">
          <mt-input
            v-model="searchFormModel.warehouseCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="projectTextBatch" :label="$t('项目文本批次')" label-style="top">
          <mt-input
            v-model="searchFormModel.projectTextBatch"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="5f7a828e-7625-4f17-abde-66630ec04aa8"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import pagingMixin from '@/mixins/paging.js'

export default {
  components: { ScTable, CollapseSearch },
  mixins: [mixin, pagingMixin],
  props: {
    zeroFilter: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      type: 'order',
      isInitQuery: false
    }
  },
  methods: {
    // 获取table数据
    async getTableData() {
      const params = {
        pageNum: this.pageInfo.current,
        pageSize: this.pageInfo.size,
        zeroFilter: this.zeroFilter,
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.receiptAndDelivery
        .getOrderByDeliverList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const list = res.data?.records?.map((item) => {
          if (item.unClearQuantity || item.unClearQuantity === 0) {
            item.supplierNum = Number(item.unClearQuantity.replace(/,/g, ''))
          }
          return item
        })
        this.tableData = list
        this.total = res.data.total
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      switch (e.code) {
        case 'batchCreateDelivery':
          this.handleBatchCreateDelivery(1, selectedRecords)
          break
        case 'batchImportDelivery':
          this.handleBatchImportDelivery()
          break
        case 'export':
          this.handleExport('order', selectedRecords)
          break
        default:
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
