<template>
  <!-- 要货排期列表 -->
  <div class="orderConfig full-height vertical-flex-box">
    <mt-template-page
      ref="templateRef"
      :current-tab="currentTab"
      :template-config="pageConfig"
      :permission-obj="permissionObj"
      @handleSelectTab="handleSelectTab"
    >
      <div slot="slot-filter" class="zero-filter-switch">
        <mt-switch
          v-if="[0, 1, 2].includes(currentTab)"
          v-model="zeroFilterSwitch"
          :active-value="1"
          :inactive-value="0"
          :on-label="$t('隐藏不可创建送货单数据')"
          :off-label="$t('显示不可创建送货单数据')"
          @change="handleChangeZeroFilterSwitch"
        />
      </div>
      <div slot="slot-0" class="full-height">
        <Plan ref="planRef" :zero-filter="zeroFilterPlan" />
      </div>
      <div slot="slot-1" class="full-height">
        <Order ref="orderRef" :zero-filter="zeroFilterOrder" />
      </div>
      <div slot="slot-2" class="full-height">
        <JIT ref="jitRef" :zero-filter="zeroFilterJit" />
      </div>
      <div slot="slot-3" class="full-height">
        <Guide></Guide>
      </div>
    </mt-template-page>
  </div>
</template>
<script>
import { TabIndex } from './config/constant'
export default {
  components: {
    Plan: () => import('./pages/plan.vue'),
    Order: () => import('./pages/order.vue'),
    JIT: () => import('./pages/JIT.vue'),
    Guide: () => import('./pages/guide.vue')
  },
  data() {
    return {
      currentTab: 0,
      zeroFilterSwitch: 1, // 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      zeroFilterPlan: 1, // 交货计划
      zeroFilterOrder: 1, // 采购订单
      zeroFilterJit: 1, // JIT
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0156' },
          { dataPermission: 'b', permissionCode: 'T_02_0155' },
          { dataPermission: 'b', permissionCode: 'T_02_0157' }
        ]
      },
      pageConfig: [
        {
          title: this.$t('交货计划'),
          dataPermission: 'a',
          permissionCode: 'T_02_0156'
        },
        {
          title: this.$t('采购订单'),
          dataPermission: 'b',
          permissionCode: 'T_02_0155'
        },
        {
          title: this.$t('JIT'),
          dataPermission: 'c',
          permissionCode: 'T_02_0157'
        },
        {
          title: this.$t('屏发货指导'),
          permissionCode: ''
        }
      ]
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.name === 'batch-delivery-supplier-tv') {
        switch (vm.currentTab) {
          case TabIndex.deliverySchedule:
            vm.$refs['planRef'].getTableData()
            break
          case TabIndex.purchaseOrder:
            vm.$refs['orderRef'].getTableData()
            break
          case TabIndex.jit:
            vm.$refs['jitRef'].getTableData()
            break
        }
      }
    })
  },
  methods: {
    handleSelectTab(index) {
      this.currentTab = index
      switch (index) {
        case TabIndex.deliverySchedule:
          this.zeroFilterSwitch = this.zeroFilterPlan
          break
        case TabIndex.purchaseOrder:
          this.zeroFilterSwitch = this.zeroFilterOrder
          break
        case TabIndex.jit:
          this.zeroFilterSwitch = this.zeroFilterJit
          break
      }
    },
    handleChangeZeroFilterSwitch(value) {
      switch (this.currentTab) {
        case TabIndex.deliverySchedule:
          this.zeroFilterPlan = value
          break
        case TabIndex.purchaseOrder:
          this.zeroFilterOrder = value
          break
        case TabIndex.jit:
          this.zeroFilterJit = value
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.zero-filter-switch {
  text-align: right;
}
</style>
