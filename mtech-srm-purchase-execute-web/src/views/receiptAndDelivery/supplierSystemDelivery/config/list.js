import { i18n } from '@/main.js'
import dayjs from 'dayjs'

export const statusOptions = [
  { text: i18n.t('成功'), value: 1 },
  { text: i18n.t('失败'), value: 2 }
]

export const buyerDeliveryStatusOptions = [
  { text: i18n.t('新建'), value: 1 },
  { text: i18n.t('发货中'), value: 2 },
  { text: i18n.t('已完成'), value: 3 },
  { text: i18n.t('已取消'), value: 4 },
  { text: i18n.t('已关闭'), value: 5 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'statusId',
    title: i18n.t('创建SRM送货单状态'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'buyerDeliveryStatus',
    title: i18n.t('SRM送货单状态'),
    minWidth: 140,
    formatter: ({ cellValue }) => {
      let item = buyerDeliveryStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'supDeliveryCode',
    title: i18n.t('供方送货单号'),
    minWidth: 120
  },
  {
    field: 'buyerDeliveryCode',
    title: i18n.t('采方送货单号'),
    minWidth: 120,
    slots: {
      default: 'buyerDeliveryCodeDetault'
    }
  },
  {
    field: 'companyCode',
    title: i18n.t('公司'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.companyName : ''
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    }
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.siteName : ''
    }
  },
  {
    field: 'supEstimateSendDate',
    title: i18n.t('预计到货日期'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'deliverDate',
    title: i18n.t('发货日期'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'supCreateDeliverDate',
    title: i18n.t('供方送货单创建时间'),
    minWidth: 200,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'buyerCreateDeliverDate',
    title: i18n.t('采方送货单创建时间'),
    minWidth: 200,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'failReason',
    title: i18n.t('创建SRM送货单失败原因'),
    minWidth: 200
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    minWidth: 120
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    minWidth: 120
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'deliveryAttach',
    title: i18n.t('送货单附件'),
    minWidth: 120,
    slots: {
      default: 'deliveryAttachDetault'
    }
  },
  {
    field: 'invoiceAttach',
    title: i18n.t('发票附件'),
    minWidth: 120,
    slots: {
      default: 'invoiceAttachDetault'
    }
  }
]
