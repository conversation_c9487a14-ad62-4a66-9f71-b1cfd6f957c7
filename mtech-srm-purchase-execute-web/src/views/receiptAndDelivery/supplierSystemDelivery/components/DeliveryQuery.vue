<!-- 手工获取送货单 -->
<template>
  <mt-dialog
    ref="dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
    width="650px"
    height="400px"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-row :gutter="24">
        <mt-col :span="24">
          <mt-form-item prop="purOrderNo" :label="$t('采购订单号')">
            <mt-input
              v-model="formData.purOrderNo"
              :placeholder="$t('请输入采购订单号，多个请用英文“,”分割开')"
              :show-clear-button="true"
            />
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {},
      rules: {
        purOrderNo: [
          {
            required: true,
            message: this.$t('请输入采购订单号'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title } = args
      this.dialogTitle = title
    },
    beforeOpen() {
      this.formData = {}
      this.$refs.ruleForm.clearValidate()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.handleSave()
        }
      })
    },
    handleSave() {
      let params = { ...this.formData }
      const api = this.$API.receiptAndDelivery.pullOrderDeliveryTiApi
      this.$loading()
      this.handleClose()
      api(params)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$emit('confirm')
          }
        })
        .finally(() => {
          this.$hloading()
        })
    }
  }
}
</script>
