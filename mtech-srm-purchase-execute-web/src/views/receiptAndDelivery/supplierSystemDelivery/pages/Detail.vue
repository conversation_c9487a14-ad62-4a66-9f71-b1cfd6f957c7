<!-- 明细视图 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('供应商')" prop="supplierCode">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('创建SRM送货单状态')" prop="statusId">
          <mt-multi-select
            v-model="searchFormModel.statusId"
            :data-source="statusOptions"
            :show-select-all="true"
            :show-clear-button="false"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('SRM送货单状态')" prop="buyerDeliveryStatus">
          <mt-multi-select
            v-model="searchFormModel.buyerDeliveryStatus"
            :data-source="buyerDeliveryStatusOptions"
            :show-select-all="true"
            :show-clear-button="false"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('工厂')" prop="siteCode">
          <RemoteAutocomplete
            v-model="searchFormModel.siteCode"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供方送货单号')" prop="supDeliveryCode">
          <mt-input
            v-model="searchFormModel.supDeliveryCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('采方送货单号')" prop="buyerDeliveryCode">
          <mt-input
            v-model="searchFormModel.buyerDeliveryCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料编码')" prop="itemCode">
          <mt-input
            v-model="searchFormModel.itemCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料名称')" prop="itemName">
          <mt-input
            v-model="searchFormModel.itemName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供方物料编码')" prop="supplierItemCode">
          <mt-input
            v-model="searchFormModel.supplierItemCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供方物料名称')" prop="supplierItemName">
          <mt-input
            v-model="searchFormModel.supplierItemName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('采购订单号')" prop="purOrderNo">
          <mt-input
            v-model="searchFormModel.purOrderNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('采购订单行号')" prop="purOrderItemNo">
          <mt-input
            v-model="searchFormModel.purOrderItemNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('销售订单号')" prop="salesOrderNo">
          <mt-input
            v-model="searchFormModel.salesOrderNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('销售订单行号')" prop="salesOrderItemNo">
          <mt-input
            v-model="searchFormModel.salesOrderItemNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="orderDate" :label="$t('订单日期')">
          <mt-date-range-picker
            v-model="searchFormModel.orderDate"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'orderDate')"
          />
        </mt-form-item>
        <mt-form-item prop="supEstimateSendDate" :label="$t('预计到货日期')">
          <mt-date-range-picker
            v-model="searchFormModel.supEstimateSendDate"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'supEstimateSendDate')"
          />
        </mt-form-item>
        <mt-form-item prop="deliverDate" :label="$t('发货日期')">
          <mt-date-range-picker
            v-model="searchFormModel.deliverDate"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'deliverDate')"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'createTime')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供方包裹单号')" prop="packageNo">
          <mt-input
            v-model="searchFormModel.packageNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('创建SRM送货单失败原因')" prop="failReason">
          <mt-input
            v-model="searchFormModel.failReason"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="632480d8-af9d-41ec-b7a2-18885396f7ba"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      keep-source
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #buyerDeliveryCodeDetault="{ row }">
        <span style="cursor: pointer; color: #2783fe" @click="buyerDeliveryCodeClick(row)">{{
          row.buyerDeliveryCode
        }}</span>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
    <DetailDeliveryQuery ref="detailDeleveryQueryRef" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, statusOptions, buyerDeliveryStatusOptions } from '../config/detail'
import { getHeadersFileName, download } from '@/utils/utils'
import DetailDeliveryQuery from '../components/DeliveryQuery.vue'

export default {
  components: { CollapseSearch, ScTable, DetailDeliveryQuery },
  data() {
    return {
      searchFormModel: {},
      toolbar: [
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
        { code: 'manualGet', name: this.$t('手工获取'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      statusOptions,
      buyerDeliveryStatusOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    buyerDeliveryCodeClick(row) {
      const params = {
        condition: 'and',
        page: { current: 1, size: 20 },
        rules: [
          {
            field: 'id',
            operator: 'equal',
            value: row.buyerDeliveryId
          }
        ]
      }
      this.$API.receiptAndDelivery.getDeliveyData(params).then((res) => {
        if (res?.code == 200) {
          const deliverListData = {
            headerInfo: res?.data?.records[0] // 头部信息
          }
          // 将信息放到 localStorage 详情页 读
          localStorage.setItem('deliverListData', JSON.stringify(deliverListData))
          // 跳转详情页
          this.$router.push({
            name: 'deliver-detail',
            query: {
              timeStamp: new Date().getTime()
            }
          })
        }
      })
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.receiptAndDelivery
        .pageOrderDeliveryTiDetailApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        case 'manualGet':
          this.handleQuery()
          break
        default:
          break
      }
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$API.receiptAndDelivery
        .exportOrderDeliveryTiDetailApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    handleQuery() {
      this.$refs.detailDeleveryQueryRef.dialogInit({
        title: this.$t('获取供方送货单')
      })
    }
  }
}
</script>
