<!-- 列表视图 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('供应商')" prop="supplierCode">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('创建SRM送货单状态')" prop="statusId">
          <mt-multi-select
            v-model="searchFormModel.statusId"
            :data-source="statusOptions"
            :show-select-all="true"
            :show-clear-button="false"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('SRM送货单状态')" prop="buyerDeliveryStatus">
          <mt-multi-select
            v-model="searchFormModel.buyerDeliveryStatus"
            :data-source="buyerDeliveryStatusOptions"
            :show-select-all="true"
            :show-clear-button="false"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('工厂')" prop="siteCode">
          <RemoteAutocomplete
            v-model="searchFormModel.siteCode"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供方送货单号')" prop="supDeliveryCode">
          <mt-input
            v-model="searchFormModel.supDeliveryCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('采方送货单号')" prop="buyerDeliveryCode">
          <mt-input
            v-model="searchFormModel.buyerDeliveryCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('公司')" prop="companyCode">
          <RemoteAutocomplete
            v-model="searchFormModel.companyCode"
            url="/masterDataManagement/auth/company/auth-fuzzy"
            :params="{
              organizationLevelCodes: ['ORG02', 'ORG01'],
              orgType: 'ORG001PRO',
              includeItself: true
            }"
            :placeholder="$t('请选择')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            records-position="data"
          />
        </mt-form-item>
        <mt-form-item prop="supEstimateSendDate" :label="$t('预计到货日期')">
          <mt-date-range-picker
            v-model="searchFormModel.supEstimateSendDate"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'supEstimateSendDate')"
          />
        </mt-form-item>
        <mt-form-item prop="deliverDate" :label="$t('发货日期')">
          <mt-date-range-picker
            v-model="searchFormModel.deliverDate"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'deliverDate')"
          />
        </mt-form-item>
        <mt-form-item prop="supCreateDeliverDate" :label="$t('供方送货单创建时间')">
          <mt-date-range-picker
            v-model="searchFormModel.supCreateDeliverDate"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'supCreateDeliverDate')"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'createTime')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="f02434de-6a4c-4850-9c22-5b0889770b48"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      keep-source
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #buyerDeliveryCodeDetault="{ row }">
        <span style="cursor: pointer; color: #2783fe" @click="buyerDeliveryCodeClick(row)">{{
          row.buyerDeliveryCode
        }}</span>
      </template>
      <template #deliveryAttachDetault="{ row }">
        <span style="cursor: pointer; color: #2783fe" @click="deliveryAttachDownload(row)">{{
          row.deliveryAttachFileName
        }}</span>
      </template>
      <template #invoiceAttachDetault="{ row }">
        <span style="cursor: pointer; color: #2783fe" @click="invoiceAttachDownload(row)">{{
          row.invoiceAttachFileName
        }}</span>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
    <DeliveryQuery ref="deliveryQueryRef" @confirm="handleSearch" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, statusOptions, buyerDeliveryStatusOptions } from '../config/list'
import { getHeadersFileName, download } from '@/utils/utils'
import DeliveryQuery from '../components/DeliveryQuery.vue'

export default {
  components: { CollapseSearch, ScTable, DeliveryQuery },
  data() {
    return {
      searchFormModel: {},
      toolbar: [
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
        { code: 'manualGet', name: this.$t('手工获取'), status: 'info', loading: false },
        { code: 'create', name: this.$t('生成SRM送货单'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      statusOptions,
      buyerDeliveryStatusOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    buyerDeliveryCodeClick(row) {
      const params = {
        condition: 'and',
        page: { current: 1, size: 20 },
        rules: [
          {
            field: 'id',
            operator: 'equal',
            value: row.buyerDeliveryId
          }
        ]
      }
      this.$API.receiptAndDelivery.getDeliveyData(params).then((res) => {
        if (res?.code == 200) {
          const deliverListData = {
            headerInfo: res?.data?.records[0] // 头部信息
          }
          // 将信息放到 localStorage 详情页 读
          localStorage.setItem('deliverListData', JSON.stringify(deliverListData))
          // 跳转详情页
          this.$router.push({
            name: 'deliver-detail',
            query: {
              timeStamp: new Date().getTime()
            }
          })
        }
      })
    },
    deliveryAttachDownload(row) {
      let params = {
        attachId: row.deliveryAttachId,
        attachFileName: row.deliveryAttachFileName
      }
      this.handleDownload(params)
    },
    invoiceAttachDownload(row) {
      let params = {
        attachId: row.invoiceAttachId,
        attachFileName: row.invoiceAttachFileName
      }
      this.handleDownload(params)
    },
    handleDownload(row) {
      this.$API.fileService.fileDownload(row.attachId).then((res) => {
        let link = document.createElement('a')
        link.style.display = 'none'
        let blob = new Blob([res.data], { type: 'application/x-msdownload' })
        let url = window.URL.createObjectURL(blob)
        link.href = url
        link.setAttribute('download', `${row.attachFileName}.pdf`)
        link.click()
        window.URL.revokeObjectURL(url)
      })
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.receiptAndDelivery
        .pageOrderDeliveryTiApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const ids = []
      selectedRecords.forEach((item) => {
        ids.push(item.id)
      })
      const commonToolbar = ['create']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        case 'manualGet':
          this.handleQuery()
          break
        case 'create':
          if (selectedRecords.length > 1) {
            this.$toast({
              content: this.$t('仅能选择一条创建SRM送货单状态为失败或为空的单据操作！'),
              type: 'warning'
            })
          } else if (selectedRecords[0].statusId === 1) {
            this.$toast({
              content: this.$t('请选择一条创建SRM送货单状态为失败或为空的单据操作！'),
              type: 'warning'
            })
          } else {
            this.handleCreate(ids)
          }
          break
        default:
          break
      }
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$API.receiptAndDelivery
        .exportOrderDeliveryTiApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    handleQuery() {
      this.$refs.deliveryQueryRef.dialogInit({
        title: this.$t('获取供方送货单')
      })
    },
    handleCreate(ids) {
      let params = { ids }
      this.$API.receiptAndDelivery.createOrderDeliveryTiApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
        }
      })
    }
  }
}
</script>
