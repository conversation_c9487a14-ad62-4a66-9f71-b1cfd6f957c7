import { i18n } from '@/main.js'
import { ColumnComponent as Component } from './columnComponent'
import {
  ComponentType,
  CellTools,
  StatusClass,
  StatusText,
  onWayStatusText,
  onWayStatusStatusClass,
  IntoStatusClass,
  IntoStatusText,
  DeliveryTypeOptions,
  OrderDeliveryTypeOptions,
  DeliveryStatusText,
  CarAbilityOptions,
  SyncStatusClass,
  SyncStatusText,
  forecastTypeOptions
} from './constant'
import { strByCharacterToArray } from '@/utils/utils'

// 格式化表格动态数据
export const formatTableColumnData = (args) => {
  const { data, sourceType } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: col.width || '150'
    }
    if (col.fieldCode === 'thePrimaryKey') {
      // 行主键
      defaultCol.visible = false
      defaultCol.isIdentity = true // 加上这个，新增一行的时候，主键不会变成可输入
      defaultCol.isPrimaryKey = true
      defaultCol.allowEditing = false
    } else if (col.fieldCode === 'checkBox') {
      // checkBox
      defaultCol.type = 'checkbox'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.showInColumnChooser = false
      defaultCol.width = '50'
      // 编辑时显示
      defaultCol.editTemplate = Component.empty
    } else if (col.fieldCode === 'serialNumber') {
      // 序号
      defaultCol.width = '80'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.ignore = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'forecastCode') {
      // 预约号
      defaultCol.width = '84'
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        dataKey: col.fieldCode,
        cellTools: CellTools
      })
      defaultCol.editTemplate = Component.text({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'forecastType') {
      // 预约类型
      defaultCol.width = '150'
      // defaultCol.allowEditing = false;
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.valueConverter = {
        type: 'map',
        map: {
          0: i18n.t('送货单预约'),
          1: i18n.t('入库单预约')
        }
      }
      defaultCol.editTemplate = Component.select({
        dataKey: defaultCol.field,
        fields: { text: 'text', value: 'value' },
        selectOptions: forecastTypeOptions,
        allowFiltering: false,
        showClearBtn: true,
        defaultValue: sourceType ?? 0
      })
    } else if (col.fieldCode === 'deliveryCode') {
      // 关联ASN
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.allowEditing = true
      defaultCol.width = '170'
      defaultCol.template = Component.listInfoSearch({
        dataKey: col.fieldCode,
        isShowLength: false,
        delimiter: '； ',
        type: ComponentType.view
      })
      defaultCol.editTemplate = Component.multiSelectSearch({
        dataKey: col.fieldCode,
        showSearchBtn: true,
        showClearBtn: true,
        disabled: false
      })
    } else if (col.fieldCode === 'status') {
      // 状态
      defaultCol.allowEditing = false
      defaultCol.template = Component.status({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        statusClass: StatusClass,
        statusText: StatusText,
        cellTools: []
      })
      defaultCol.editTemplate = Component.status({
        type: ComponentType.edit,
        dataKey: col.fieldCode,
        statusClass: StatusClass,
        statusText: StatusText
      })
    } else if (col.fieldCode === 'onWayStatus') {
      // 状态
      defaultCol.allowEditing = false
      defaultCol.template = Component.onWayStatus({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        statusClass: onWayStatusStatusClass,
        statusText: onWayStatusText,
        cellTools: []
      })
      defaultCol.editTemplate = Component.onWayStatus({
        type: ComponentType.edit,
        dataKey: col.fieldCode,
        statusClass: onWayStatusStatusClass,
        statusText: onWayStatusText
      })
    } else if (col.fieldCode === 'companyName') {
      // 公司名称 code+name
      defaultCol.width = '235'
      defaultCol.template = Component.codeNameColumn({
        firstKey: 'companyCode',
        secondKey: 'companyName',
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.codeNameColumn({
        firstKey: 'companyCode',
        secondKey: 'companyName',
        type: ComponentType.edit,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'warehouseCode') {
      defaultCol.width = '235'
      defaultCol.template = Component.codeNameColumn({
        firstKey: 'warehouseCode',
        secondKey: 'warehouse',
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.codeNameColumn({
        firstKey: 'warehouseCode',
        secondKey: 'warehouse',
        type: ComponentType.edit,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'sendAddressCode') {
      defaultCol.width = '235'
      defaultCol.template = Component.codeNameColumn({
        firstKey: 'sendAddressCode',
        secondKey: 'sendAddress',
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.codeNameColumn({
        firstKey: 'sendAddressCode',
        secondKey: 'sendAddress',
        type: ComponentType.edit,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'supplierCode') {
      defaultCol.width = '150'
      defaultCol.template = Component.codeNameColumn({
        firstKey: 'supplierCode',
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.codeNameColumn({
        firstKey: 'supplierCode',
        type: ComponentType.edit,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'supplierName') {
      defaultCol.width = '220'
      defaultCol.template = Component.codeNameColumn({
        firstKey: 'supplierName',
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.codeNameColumn({
        firstKey: 'supplierName',
        type: ComponentType.edit,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'buyerOrgName') {
      // 采购组名称 code+name
      defaultCol.width = '175'
      defaultCol.template = Component.codeNameColumn({
        firstKey: 'buyerOrgCode',
        secondKey: 'buyerOrgName',
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.codeNameColumn({
        firstKey: 'buyerOrgCode',
        secondKey: 'buyerOrgName',
        type: ComponentType.edit,
        dataKey: col.fieldCode
      })
    } else if (defaultCol.field === 'syncStatus') {
      // 同步状态 状态:0-未同步，1-同步失败，2同步成功
      defaultCol.allowEditing = false
      defaultCol.width = '100'
      defaultCol.template = Component.status({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        statusClass: SyncStatusClass,
        statusText: SyncStatusText,
        cellTools: []
      })
      defaultCol.editTemplate = Component.status({
        type: ComponentType.edit,
        dataKey: col.fieldCode,
        statusClass: SyncStatusClass,
        statusText: SyncStatusText
      })
    } else if (col.fieldCode === 'intoStatus') {
      // 入园状态
      defaultCol.allowEditing = false
      defaultCol.width = '95'
      defaultCol.template = Component.status({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        statusClass: IntoStatusClass,
        statusText: IntoStatusText,
        cellTools: []
      })
      defaultCol.editTemplate = Component.status({
        type: ComponentType.edit,
        dataKey: col.fieldCode,
        statusClass: IntoStatusClass,
        statusText: IntoStatusText
      })
    } else if (col.fieldCode === 'deliveryStatus') {
      // 交货方式
      defaultCol.allowEditing = false
      defaultCol.width = '97'
      defaultCol.template = Component.listInfoSearch({
        dataKey: 'deliveryStatus',
        isShowLength: false,
        infoKey: 'text',
        delimiter: '； ',
        type: ComponentType.view
      })
      defaultCol.editTemplate = Component.listInfoSearch({
        dataKey: 'deliveryStatus',
        isShowLength: false,
        infoKey: 'text',
        delimiter: '； ',
        type: ComponentType.edit
      })
    } else if (col.fieldCode === 'driverName') {
      // 司机姓名
      defaultCol.allowEditing = true
      defaultCol.width = '95'
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.inputSearch({
        dataKey: col.fieldCode,
        showClearBtn: true,
        disabled: false,
        maxlength: 100
      })
    } else if (col.fieldCode === 'driverIdNo') {
      // 司机身份证号
      defaultCol.width = '200'
      defaultCol.allowEditing = true
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.inputChange({
        dataKey: col.fieldCode,
        showClearBtn: true
      })
    } else if (col.fieldCode === 'driverPhone') {
      // 司机手机号
      defaultCol.allowEditing = true
      defaultCol.width = '130'
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.inputChange({
        dataKey: col.fieldCode,
        showClearBtn: true
      })
    } else if (col.fieldCode === 'carNo') {
      // 车牌号
      defaultCol.allowEditing = true
      defaultCol.width = '103'
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.inputChange({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 100
      })
    } else if (col.fieldCode === 'quantity') {
      // 件数 数量
      defaultCol.width = '100'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.inputChange({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 999999999
      })
    } else if (col.fieldCode === 'forecastTime') {
      // 预约时间
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.allowEditing = true
      defaultCol.width = '180'
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.textSearch({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'remark') {
      // 备注
      defaultCol.allowEditing = true
      defaultCol.width = '90'
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.inputChange({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 100
      })
    } else if (col.fieldCode === 'accompanyInfo') {
      // 随车信息
      defaultCol.allowEditing = true
      defaultCol.width = '100'
      defaultCol.template = Component.listInfoSearch({
        dataKey: 'accompanyList',
        isShowLength: false,
        infoKey: 'name',
        delimiter: '； ',
        isShowBtn: false,
        type: ComponentType.view
      })
      defaultCol.editTemplate = Component.listInfoSearch({
        dataKey: 'accompanyList',
        requestKey: col.fieldCode,
        isShowLength: false,
        infoKey: 'name',
        delimiter: '； ',
        isShowBtn: true,
        type: ComponentType.edit
      })
    } else if (col.fieldCode === 'accompanyNum') {
      // 随车人数
      defaultCol.allowEditing = false
      defaultCol.template = Component.listInfoSearch({
        dataKey: 'accompanyList',
        isShowLength: true,
        type: ComponentType.view
      })
      defaultCol.editTemplate = Component.listInfoSearch({
        dataKey: 'accompanyList',
        isShowLength: true,
        type: ComponentType.edit
      })
    }
    colData.push(defaultCol)
  })

  return colData
}

// 预约送货-供方-勾选关联ASN 格式化表格列
export const asnTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150'
    }
    if (col.fieldCode === 'deliveryType') {
      // 交货方式:交货方式:1-采购订单,2-交货计划,3-JIT,4-无需求
      defaultCol.valueConverter = {
        type: 'map',
        map: DeliveryTypeOptions
      }
    } else if (col.fieldCode === 'orderDeliveryType') {
      // 送货类型:1-关联采购订单,2-无采购订单
      defaultCol.valueConverter = {
        type: 'map',
        map: OrderDeliveryTypeOptions
      }
    } else if (col.fieldCode === 'tmsWarehouseName') {
      // TMS分厂卸货点
      defaultCol.width = '200'
    } else if (col.fieldCode === 'companyName') {
      // 公司名称 code+name
      defaultCol.width = '300'
      defaultCol.template = Component.codeNameColumn({
        firstKey: 'companyCode',
        secondKey: 'companyName'
      })
    } else if (col.fieldCode === 'supplierName') {
      // 供应商名称 code+name
      defaultCol.width = '300'
      defaultCol.template = Component.codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      })
    } else if (col.fieldCode === 'buyerOrgName') {
      // 采购组名称 code+name
      defaultCol.width = '300'
      defaultCol.template = Component.codeNameColumn({
        firstKey: 'buyerOrgCode',
        secondKey: 'buyerOrgName'
      })
    }
    colData.push(defaultCol)
  })

  return colData
}

// 预约送货-供方-选择送货司机弹框 格式化表格列
export const driverInfoTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: 'auto'
    }
    colData.push(defaultCol)
  })

  return colData
}

// 预约送货-供方-选择预约时间弹框 格式化表格列
export const forecastTimeTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: 'auto'
    }
    if (col.fieldCode === 'isHave') {
      // 装车能力 1-有,2-无
      defaultCol.valueConverter = {
        type: 'map',
        map: CarAbilityOptions
      }
    }
    colData.push(defaultCol)
  })

  return colData
}

// 表格数据序列化
export const serializeList = (list) => {
  if (list?.length > 0) {
    list.forEach((item, index) => {
      // 添加序号
      item.serialNumber = index + 1
      // 关联ASN 字符串转数组
      item.deliveryCode = strByCharacterToArray({
        str: item.deliveryCode || '',
        character: ','
      })
      if (item.deliveryStatus) {
        // 交货方式 字符串转数组 数组转对象数组
        const deliveryStatusList = strByCharacterToArray({
          str: item.deliveryStatus || '',
          character: ','
        })
        const tmp = []
        deliveryStatusList.forEach((item) => {
          tmp.push({ text: DeliveryStatusText[item] || item, value: item })
        })
        item.deliveryStatus = tmp
      }
    })
  }

  return list
}
