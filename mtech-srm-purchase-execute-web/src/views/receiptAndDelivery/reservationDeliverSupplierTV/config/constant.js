import { i18n } from '@/main.js'
import Vue from 'vue'

// toolBar 的高度
export const ToolbarHeight = 50
// 表头的高度
export const TheadHeight = 42

// 表格编辑 的 RequestType
export const RequestType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  sorting: 'sorting',
  refresh: 'refresh'
}

// 表格编辑 的 action
export const ActionType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  edit: 'edit'
}

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 表格 EditSettings
export const EditSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
} // 编辑设置

// 状态 1-未预约,2-审批中,3-预约成功,4-预约拒绝,5-已取消
export const Status = {
  notYet: 1, // 未预约
  inProgress: 2, // 审批中
  success: 3, // 预约成功
  reject: 4, // 预约拒绝
  cancel: 5 // 已取消
}
// 状态 text
export const StatusText = {
  [Status.notYet]: i18n.t('未预约'),
  [Status.inProgress]: i18n.t('审批中'),
  [Status.success]: i18n.t('预约成功'),
  [Status.reject]: i18n.t('预约拒绝'),
  [Status.cancel]: i18n.t('已取消')
}
// 状态 class
export const StatusClass = {
  [Status.notYet]: 'col-active',
  [Status.inProgress]: 'col-active',
  [Status.success]: 'col-active',
  [Status.reject]: 'col-active',
  [Status.cancel]: 'col-inactive'
}
export const onWayStatusText = {
  0: i18n.t('未出发'),
  1: i18n.t('已入园'),
  2: i18n.t('已出发'),
  3: i18n.t('已报到'),
  4: i18n.t('已取消'),
  5: i18n.t('已关闭')
}
export const onWayStatusStatusClass = {
  0: 'col-active',
  1: 'col-active',
  2: 'col-active',
  3: 'col-active',
  4: 'col-active',
  5: 'col-active'
}
// 状态 对应的 Options
export const StatusOptions = [
  {
    // 未预约
    value: Status.notYet,
    text: StatusText[Status.notYet],
    cssClass: 'col-active'
  },
  {
    // 审批中
    value: Status.inProgress,
    text: StatusText[Status.inProgress],
    cssClass: 'col-active'
  },
  {
    // 预约成功
    value: Status.success,
    text: StatusText[Status.success],
    cssClass: 'col-active'
  },
  {
    // 预约拒绝
    value: Status.reject,
    text: StatusText[Status.reject],
    cssClass: 'col-active'
  },
  {
    // 已取消
    value: Status.cancel,
    text: StatusText[Status.cancel],
    cssClass: 'col-active'
  }
]

// 入园状态 入园装填:1-已入园,2-未入园
export const IntoStatus = {
  already: 1, // 已入园
  notYet: 2 // 未入园
}
// 入园状态 text
export const IntoStatusText = {
  [IntoStatus.already]: i18n.t('已入园'),
  [IntoStatus.notYet]: i18n.t('未入园')
}
// 入园状态 class
export const IntoStatusClass = {
  [IntoStatus.already]: '',
  [IntoStatus.notYet]: ''
}
// 入园状态 对应的 Options
export const IntoStatusOptions = [
  {
    // 已入园
    value: IntoStatus.already,
    text: IntoStatusText[IntoStatus.already],
    cssClass: ''
  },
  {
    // 未入园
    value: IntoStatus.notYet,
    text: IntoStatusText[IntoStatus.notYet],
    cssClass: ''
  }
]

// 交货方式 交货方式:1-采购订单,2-交货计划,3-JIT,4-无需求,5-vmi
export const DeliveryStatus = {
  purchase: 1, // 采购订单
  plan: 2, // 交货计划
  jit: 3, // JIT
  noNeed: 4, // 无需求
  vmi: 5 // VMI
}
// 交货方式 text
export const DeliveryStatusText = {
  [DeliveryStatus.purchase]: i18n.t('采购订单'),
  [DeliveryStatus.plan]: i18n.t('交货计划'),
  [DeliveryStatus.jit]: i18n.t('JIT'),
  [DeliveryStatus.noNeed]: i18n.t('无需求'),
  [DeliveryStatus.vmi]: i18n.t('VMI')
}
// 交货方式 Class
export const DeliveryStatusClass = {
  [DeliveryStatus.purchase]: '',
  [DeliveryStatus.plan]: '',
  [DeliveryStatus.jit]: '',
  [DeliveryStatus.noNeed]: '',
  [DeliveryStatus.vmi]: ''
}
// 交货方式 对应的 Options
export const DeliveryStatusOptions = [
  {
    // 采购订单
    value: DeliveryStatus.purchase,
    text: DeliveryStatusText[DeliveryStatus.purchase],
    cssClass: ''
  },
  {
    // 交货计划
    value: DeliveryStatus.plan,
    text: DeliveryStatusText[DeliveryStatus.plan],
    cssClass: ''
  },
  {
    // JIT
    value: DeliveryStatus.jit,
    text: DeliveryStatusText[DeliveryStatus.jit],
    cssClass: ''
  },
  {
    // 无需求
    value: DeliveryStatus.noNeed,
    text: DeliveryStatusText[DeliveryStatus.noNeed],
    cssClass: ''
  },
  {
    // VMI
    value: DeliveryStatus.vmi,
    text: DeliveryStatusText[DeliveryStatus.vmi],
    cssClass: ''
  }
]

// 预约类型
export const forecastType = {
  deliver: 0, // 送货单
  warehouse: 1 // 入库单
}
// 交货方式 text
export const forecastTypeText = {
  [forecastType.deliver]: i18n.t('送货单预约'),
  [forecastType.warehouse]: i18n.t('入库单预约')
}
// 交货方式 Class
export const forecastTypeClass = {
  [forecastType.deliver]: '',
  [forecastType.warehouse]: ''
}
// 交货方式 对应的 Options
export const forecastTypeOptions = [
  {
    // 送货单
    value: forecastType.deliver,
    text: forecastTypeText[forecastType.deliver],
    cssClass: ''
  },
  {
    // 入库单
    value: forecastType.warehouse,
    text: forecastTypeText[forecastType.warehouse],
    cssClass: ''
  }
]

// 选择送货单-弹框 交货方式 交货方式:1-采购订单,2-交货计划,3-JIT,4-无需求
export const DeliveryType = {
  purchase: 1, // 采购订单
  plan: 2, // 交货计划
  jit: 3, // JIT
  noNeed: 4 // 无需求
}
// 交货方式 text
export const DeliveryTypeText = {
  [DeliveryType.purchase]: i18n.t('采购订单'),
  [DeliveryType.plan]: i18n.t('交货计划'),
  [DeliveryType.jit]: i18n.t('JIT'),
  [DeliveryType.noNeed]: i18n.t('无需求')
}
// 交货方式 对应的 Options
export const DeliveryTypeOptions = [
  {
    // 采购订单
    value: DeliveryType.purchase,
    text: DeliveryTypeText[DeliveryType.purchase],
    cssClass: ''
  },
  {
    // 交货计划
    value: DeliveryType.plan,
    text: DeliveryTypeText[DeliveryType.plan],
    cssClass: ''
  },
  {
    // JIT
    value: DeliveryType.jit,
    text: DeliveryTypeText[DeliveryType.jit],
    cssClass: ''
  },
  {
    // 无需求
    value: DeliveryType.noNeed,
    text: DeliveryTypeText[DeliveryType.noNeed],
    cssClass: ''
  }
]

// 装车能力 1 有 2 无
export const CarAbility = {
  have: 1,
  not: 2
}
// 装车能力 text
export const CarAbilityText = {
  [CarAbility.have]: i18n.t('有'),
  [CarAbility.not]: i18n.t('无')
}
// 装车能力 text
export const CarAbilityOptions = [
  {
    // 有
    value: CarAbility.have,
    text: CarAbilityText[CarAbility.have],
    cssClass: ''
  },
  {
    // 有
    value: CarAbility.not,
    text: CarAbilityText[CarAbility.not],
    cssClass: ''
  }
]

// 选择送货单-弹框 送货类型:1-关联采购订单,2-无采购订单
export const OrderDeliveryType = {
  linked: 1, // 关联采购订单
  noOrder: 2 // 无采购订单
}
// 送货类型 text
export const OrderDeliveryTypeText = {
  [OrderDeliveryType.linked]: i18n.t('关联采购订单'),
  [OrderDeliveryType.noOrder]: i18n.t('无采购订单')
}
// 送货类型 对应的 Options
export const OrderDeliveryTypeOptions = [
  {
    // 关联采购订单
    value: OrderDeliveryType.linked,
    text: OrderDeliveryTypeText[OrderDeliveryType.linked],
    cssClass: ''
  },
  {
    // 无采购订单
    value: OrderDeliveryType.noOrder,
    text: OrderDeliveryTypeText[OrderDeliveryType.noOrder],
    cssClass: ''
  },
  {
    value: 3,
    text: 'jit',
    cssClass: ''
  },
  {
    value: 4,
    text: i18n.t('无需求'),
    cssClass: ''
  },
  {
    value: 5,
    text: 'VMI',
    cssClass: ''
  },
  {
    value: 6,
    text: i18n.t('钢材'),
    cssClass: ''
  }
]

// 预约表格 Toolbar
export const ReservationToolbar = [
  {
    id: 'ReservationAdd',
    icon: 'icon_table_new',
    permission: ['O_02_1387'],
    title: i18n.t('新增')
  },
  {
    id: 'endEdit',
    icon: 'icon_table_cancel',
    title: i18n.t('取消编辑')
  },
  {
    id: 'ReservationSubmit',
    icon: 'icon_table_submit',
    permission: ['O_02_1386'],
    title: i18n.t('提交')
  },
  // {
  //   id: "ReservationUpgrade",
  //   icon: "icon_table_UpgradeAppointment",
  //   title: i18n.t("升级预约"),
  // },
  {
    id: 'ReservationCancel',
    icon: 'icon_table_cancel',
    permission: ['O_02_1384'],
    title: i18n.t('取消')
  },
  {
    id: 'Import',
    icon: 'icon_solid_Import',
    permission: ['O_02_1642'],
    title: i18n.t('导入')
  }
  // {
  //   id: "ReservationUpdate",
  //   icon: "icon_table_save",
  //   permission: ["O_02_1387"],
  //   title: i18n.t("更新"),
  // },
]
// 单元格操作按钮
export const CellTools = [
  {
    id: 'ReservationCancel',
    icon: '',
    permission: ['O_02_1384'],
    title: i18n.t('取消'),
    // permission: [""],
    visibleCondition: (data) =>
      (data.status == Status.notYet ||
        data.status == Status.inProgress ||
        data.status == Status.reject ||
        data.status == Status.success) &&
      data.intoStatus == IntoStatus.notYet // (未预约 || 审批中 || 预约拒绝 || 预约成功) && 未入园
  },
  {
    id: 'ReservationSubmit',
    icon: '',
    permission: ['O_02_1386'],
    title: i18n.t('提交'),
    // permission: [""],
    visibleCondition: (data) => data.status == Status.notYet || data.status == Status.reject // 未预约 || 预约拒绝
  },
  {
    id: 'ReservationUpgrade',
    icon: '',
    permission: ['O_02_1385'],
    title: i18n.t('升级预约'),
    // permission: [""],
    visibleCondition: (data) =>
      data.status == Status.success && data.intoStatus == IntoStatus.notYet // 预约成功 && 未入园
  }
]

// 行编辑触发的 change 事件类型
export const ComponentChangeType = {
  code: 'code', // 例：下拉框选择“供应商”，供应商编码（code)要被修改
  link: 'link' // 例：计划组：根据物料+工厂+计划组的业务组类型 code 带出
}

// 同步状态 状态:0-未同步，1-同步失败，2同步成功
export const SyncStatus = {
  notSynced: 0, // 否
  fail: 1, // 失败
  synced: 2 // 是
}
// 交货方式 text
export const SyncStatusText = {
  [SyncStatus.notSynced]: i18n.t('未同步'),
  [SyncStatus.fail]: i18n.t('同步失败'),
  [SyncStatus.synced]: i18n.t('已同步')
}
// 交货方式 Class
export const SyncStatusClass = {
  [SyncStatus.notSynced]: 'col-notSynced',
  [SyncStatus.fail]: 'col-notSynced',
  [SyncStatus.synced]: 'col-synced'
}
// 交货方式 对应的 Options
export const SyncStatusOptions = [
  {
    value: SyncStatus.notSynced,
    text: SyncStatusText[SyncStatus.notSynced],
    cssClass: SyncStatusClass[SyncStatus.notSynced]
  },
  {
    value: SyncStatus.fail,
    text: SyncStatusText[SyncStatus.fail],
    cssClass: SyncStatusClass[SyncStatus.fail]
  },
  {
    value: SyncStatus.synced,
    text: SyncStatusText[SyncStatus.synced],
    cssClass: SyncStatusClass[SyncStatus.synced]
  }
]

// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  accompanyList: [], // 随车人员 { idNo: i18n.t("身份证号"), name: i18n.t("姓名") }
  carAbility: null, // 装车能力:1-有,2-无
  carNo: null, // 车牌号
  deliveryCode: [], // 送货单号 [ "单号1", "单号2" ]
  deliveryStatus: [], // 交货方式:1-采购订单,2-交货计划,3-JIT,4-无需求 [ { value: "1", text: i18n.t("采购订单") } ]
  driverIdNo: null, // 司机身份证
  driverName: null, // 司机名称
  driverPhone: null, // 司机手机号
  forecastCode: null, // 预约送货单号
  forecastTime: null, // 预约时间 "2020-12-11 12:00-13:00"
  intoStatus: 2, // 入园装填:1-已入园,2-未入园
  proTenantId: null, // 加工商租户id
  purTenantId: null, // 采方租户id
  quantity: null, // 数量
  remark: null, // 备注
  status: 1, // 状态:1-未预约,2-审批中,3-预约成功,4-预约拒绝
  syncStatus: 0, // 同步状态 状态:0-未同步，1-同步失败，2同步成功
  tenantId: null, // 租户id
  companyName: null, // 公司名称
  companyCode: null, // 公司编码
  supplierName: null, // 供应商名称
  supplierCode: null, // 供应商编码
  buyerOrgName: null, // 采购组名称
  buyerOrgCode: null // 采购组编码
}

// 预约表格列数据
export const ReservationColumnData = [
  {
    fieldCode: 'checkBox' // 不可编辑
  },
  {
    fieldCode: 'thePrimaryKey'
  },
  {
    fieldCode: 'serialNumber', // 前端定义,不可编辑
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'forecastCode', // 预约号 预约送货单号
    fieldName: i18n.t('预约号')
  },
  {
    fieldCode: 'forecastType', // 预约类型
    fieldName: i18n.t('预约类型')
  },
  {
    fieldCode: 'deliveryCode', // 关联ASN 可编辑
    fieldName: i18n.t('关联ASN/VMI入库单')
  },
  {
    fieldCode: 'companyName', // 公司名称 不可编辑 code+name
    fieldName: i18n.t('公司')
  },
  // {
  //   fieldCode: "companyCode", // 公司编码 不可编辑
  //   fieldName: i18n.t("公司编码"),
  // },
  {
    fieldCode: 'supplierCode',
    fieldName: i18n.t('供应商编码')
  },
  {
    fieldCode: 'supplierName',
    fieldName: i18n.t('供应商名称')
  },
  {
    fieldCode: 'buyerOrgName', // 采购组名称 不编辑 code+name
    fieldName: i18n.t('采购组')
  },
  // {
  //   fieldCode: "buyerOrgCode", // 采购组编码 不编辑
  //   fieldName: i18n.t("采购组编码"),
  // },
  {
    fieldCode: 'status', // 预约状态
    fieldName: i18n.t('预约状态')
  },
  {
    fieldCode: 'onWayStatus', // 在途状态
    fieldName: i18n.t('在途状态'),
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: [
        { text: i18n.t('未出发'), value: 0, cssClass: 'col-active' },
        { text: i18n.t('已入园'), value: 1, cssClass: 'col-active' },
        { text: i18n.t('已出发'), value: 2, cssClass: 'col-active' },
        { text: i18n.t('已报到'), value: 3, cssClass: 'col-active' },
        { text: i18n.t('已取消'), value: 4, cssClass: 'col-active' },
        { text: i18n.t('已关闭'), value: 5, cssClass: 'col-active' }
      ]
    }
  },
  {
    fieldCode: 'syncStatus', // 同步状态 状态:0-未同步，1-同步失败，2同步成功
    fieldName: i18n.t('同步状态')
  },
  {
    fieldCode: 'intoStatus', // 入园状态
    fieldName: i18n.t('入园状态')
  },
  {
    fieldCode: 'deliveryStatus', // 交货方式
    fieldName: i18n.t('交货方式')
  },
  {
    fieldCode: 'driverName', // 司机姓名 可编辑
    fieldName: i18n.t('司机姓名')
  },
  {
    fieldCode: 'driverIdNo', // 司机身份证号 可编辑
    fieldName: i18n.t('司机身份证号')
  },
  {
    fieldCode: 'driverPhone', // 司机手机号 可编辑
    fieldName: i18n.t('司机手机号')
  },
  {
    fieldCode: 'carNo', // 车牌号
    fieldName: i18n.t('车牌号')
  },
  {
    fieldCode: 'quantity', // 件数 可编辑
    fieldName: i18n.t('件数')
  },
  {
    fieldCode: 'vehicleLogistics', // 车辆物流
    fieldName: i18n.t('车辆物流'),
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('template-detail', {
          template: `
              <span v-if="data.carNo" style="color: #6386c1; cursor: pointer;" @click="toLogistics(data)">{{ $t('查看物流') }}</span>`,
          methods: {
            toLogistics(e) {
              const params = {
                ztpno: e?.deliveryCode?.toString(),
                busCode: e?.forecastCode,
                busNum: e.carNo
              }
              this.$API.receiptAndDelivery.purQueryVehicleLogistics(params).then((res) => {
                if (res.code === 200) {
                  window.open(res.data.mapURL)
                }
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'forecastTime', // 预约时间 可编辑
    fieldName: i18n.t('预约时间'),
    width: 200
  },
  {
    fieldCode: 'warehouseCode',
    width: 200,
    fieldName: i18n.t('库存地点')
  },
  {
    fieldCode: 'sendAddressCode',
    width: 200,
    fieldName: i18n.t('送货地址')
  },
  {
    fieldCode: 'remark', // 备注 可编辑
    fieldName: i18n.t('备注')
  },
  {
    fieldCode: 'accompanyInfo', // 随车信息 accompanyList 前端定义 key 可编辑
    fieldName: i18n.t('随车信息')
  },
  {
    fieldCode: 'accompanyNum', // 随车人数 accompanyList 前端定义 key
    fieldName: i18n.t('随车人数')
  }
]

// 预约送货-供方-勾选关联ASN 表格列数据
export const AsnTableColumnData = [
  {
    fieldCode: 'deliveryCode',
    fieldName: i18n.t('送货单号')
  },
  {
    fieldCode: 'siteCode',
    fieldName: i18n.t('工厂')
  },
  {
    fieldCode: 'warehouseCode',
    fieldName: i18n.t('库存地点')
  },
  {
    fieldCode: 'receiveAddressName',
    fieldName: i18n.t('收货地址')
  },
  {
    fieldCode: 'receiverContact',
    fieldName: i18n.t('收货联系方式')
  },
  {
    fieldCode: 'receiverContactName',
    fieldName: i18n.t('收货人')
  },
  {
    fieldCode: 'deliveryType',
    fieldName: i18n.t('送货单类型')
  }
]

// 入库单-勾选关联ASN 表格列数据
export const AsnWarehouseTableColumnData = [
  {
    fieldCode: 'vmiOrderCode', // 入库单
    fieldName: i18n.t('入库单号')
  },
  {
    fieldCode: 'siteName', // SRM工厂
    fieldName: i18n.t('SRM工厂')
  },
  {
    fieldCode: 'vmiWarehouseName', // SRM库位
    fieldName: i18n.t('SRM库位')
  },
  {
    fieldCode: 'tmsSiteName', // TMS分厂
    fieldName: i18n.t('TMS分厂')
  },
  {
    fieldCode: 'tmsWarehouseName', // TMS分厂卸货点
    fieldName: i18n.t('TMS分厂卸货点')
  },
  {
    fieldCode: 'vmiOrderTypeDesc', // 入库单类型
    fieldName: i18n.t('入库单类型')
  },
  {
    fieldCode: 'companyName', // 公司名称 不可编辑 code+name
    fieldName: i18n.t('公司')
  },
  {
    fieldCode: 'supplierName', // 供应商名称 不可编辑 code+name
    fieldName: i18n.t('供应商')
  }
]

// 预约送货-供方-选择送货司机弹框 表格列数据
export const DriverInfoTableColumnData = [
  {
    fieldCode: 'name', // 司机名称
    fieldName: i18n.t('司机名称')
  },
  {
    fieldCode: 'idCard', // 司机身份证号
    fieldName: i18n.t('司机身份证号')
  },
  {
    fieldCode: 'contact', // 司机手机号
    fieldName: i18n.t('司机手机号')
  },
  {
    fieldCode: 'license', // 车牌号
    fieldName: i18n.t('车牌号')
  }
]

// 预约送货-供方-选择预约时间弹框 表格列数据
export const ForecastTimeTableColumnData = [
  {
    fieldCode: 'appointmentTime', // 预约时间段 09:00-10:00
    fieldName: i18n.t('时间段')
  },
  {
    fieldCode: 'isHave', // 装车能力 1-有,2-无
    fieldName: i18n.t('装车能力')
  },
  {
    fieldCode: 'warehouseCode',
    width: 200,
    fieldName: i18n.t('库存地点'),
    template: () => {
      const template = {
        template: `<div>{{data.warehouseCode}}-{{data.warehouse}}</div>`,
        data: function () {
          return { data: {} }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    }
  },
  {
    fieldCode: 'sendAddressCode',
    width: 200,
    fieldName: i18n.t('送货地址'),
    template: () => {
      const template = {
        template: `<div>{{data.sendAddressCode}}-{{data.sendAddress}}</div>`,
        data: function () {
          return { data: {} }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    }
  }
]
