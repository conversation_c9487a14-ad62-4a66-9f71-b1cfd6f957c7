<template>
  <!-- 预约送货-供方-勾选关联ASN -->
  <mt-dialog
    ref="dialog"
    class="asn-warehose-dialog"
    width="60%"
    height="80%"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <AsnWarehouseTableForm :api-type="1" ref="asnFormRef" />
  </mt-dialog>
</template>

<script>
/* eslint-disable prettier/prettier */
import AsnWarehouseTableForm from './asnWarehouseTableForm.vue'
// import { BASE_TENANT } from "@/utils/constant";

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  components: {
    AsnWarehouseTableForm
  },
  data() {
    return {
      dialogTitle: '',
      apiWaitingQuantity: 0, // 调用的api正在等待数
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  methods: {
    // 初始化
    dialogInit(entryInfo) {
      const { title } = entryInfo
      this.dialogTitle = title // 弹框名称
      // this.refreshColumns();
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    confirm() {
      let selectedRowData = this.$refs.asnFormRef.getSelectedRecords()

      selectedRowData.forEach((item) => {
        item.deliveryCode = item.vmiOrderCode
      })
      this.$emit('confirm', { data: selectedRowData })
      this.handleClose()
    },

    handleClose() {
      this.$refs.asnFormRef.handleCustomReset()
      this.$refs.asnFormRef.clearSelection()
      this.$refs.dialog.ejsRef.hide()
    },
    // 刷新当前 Grid
    // refreshColumns() {
    //   this.$refs.templateRef.refreshCurrentGridData();
    // },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped>
.asn-warehose-dialog {
  /deep/ .e-dlg-content {
    padding-top: 0 !important;
  }
}
::v-deep .toolbar-container {
  height: 16px;
}
::v-deep .top-filter {
  padding-top: 0;
}
::v-deep .e-dialog {
  min-width: 600px;
  .e-dlg-header-content {
    padding: 8px 12px;
  }
}
</style>
