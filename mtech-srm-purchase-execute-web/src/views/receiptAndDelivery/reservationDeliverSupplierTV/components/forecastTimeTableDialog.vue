<template>
  <!-- 预约送货-供方-选预约时间 -->
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <div class="full-height vertical-flex-box">
      <!-- 头部 -->
      <div class="flex-keep">
        <div class="forecast-time-date">
          <span>{{ $t('选择日期') }}</span>
          <mt-date-picker
            class="forecast-time-date-picker"
            v-model="forecastTimeDate"
            :placeholder="$t('选择日期')"
            :min="new Date()"
            :allow-edit="false"
            :show-clear-button="false"
            @change="forecastTimeDateChange"
          ></mt-date-picker>
        </div>
      </div>
      <!-- 表格 -->
      <div class="flex-fit">
        <mt-template-page
          ref="templateRef"
          :template-config="componentConfig"
          :hidden-tabs="true"
        />
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { forecastTimeTableColumnData } from '../config/index.js'
import { ForecastTimeTableColumnData } from '../config/constant'
import { timeStringToDate } from '@/utils/utils'

export default {
  data() {
    return {
      dialogTitle: '',
      apiWaitingQuantity: 0, // 调用的api正在等待数
      forecastTimeDate: new Date(), // 预约日期
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 不使用组件中的toolbar配置
          grid: {
            allowPaging: false, // 不分页
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: forecastTimeTableColumnData({
              data: ForecastTimeTableColumnData
            }),
            dataSource: []
          }
        }
      ]
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      const { title, data } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.rowData = data
      this.$set(this.componentConfig[0].grid, 'dataSource', [])
      this.getSupplierForecastDeliveryForecastTime()
      this.$refs.dialog.ejsRef.show()
    },
    // 供方预约送货-预约时间段
    getSupplierForecastDeliveryForecastTime() {
      const params = {
        timeInfo: timeStringToDate({
          formatString: 'YYYY-mm-dd',
          value: this.forecastTimeDate
        }) // 预约日期
      }
      const deliveryCode = (this.rowData?.deliveryCode || '').join(',')
      if (this.rowData?.forecastType === 1) {
        params.vmiOrderCode = deliveryCode
      } else {
        params.deliveryCode = deliveryCode
      }
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .getSupplierForecastDeliveryForecastTime1(params)
        .then((res) => {
          this.apiEndLoading()
          if (res.code == 200) {
            // forecastTimeTableDataSource.splice(
            //   0,
            //   forecastTimeTableDataSource.length
            // );
            const dataList = res.data || []
            // dataList.forEach((item) => {
            //   forecastTimeTableDataSource.push(item);
            // });
            this.$set(this.componentConfig[0].grid, 'dataSource', dataList)
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 预约日期 change
    forecastTimeDateChange(e) {
      this.forecastTimeDate = e
      if (this.forecastTimeDate) {
        this.getSupplierForecastDeliveryForecastTime()
      } else {
        this.$toast({ content: this.$t('请选择日期'), type: 'warning' })
      }
    },
    onOpen(args) {
      args.preventFocus = true
    },

    confirm() {
      let selectedRowData = this.$refs.templateRef.getCurrentTabRef().grid.getSelectedRecords()
      if (selectedRowData[0]) {
        const appointmentTime = selectedRowData[0]?.appointmentTime || '' // 预约时间段
        const carAbility = selectedRowData[0].isHave // 装车能力
        const { warehouseCode, warehouse, sendAddressCode, sendAddress } = selectedRowData[0]
        const forecastTimeDateStr = timeStringToDate({
          formatString: 'YYYY-mm-dd',
          value: this.forecastTimeDate
        }) // 预约日期
        const forecastTime = `${forecastTimeDateStr} ${appointmentTime}` // 预约时间
        this.$emit('confirm', {
          data: {
            id: this.rowData.id,
            carAbility, // 装车能力
            forecastTime, // 预约时间
            warehouseCode,
            warehouse,
            sendAddressCode,
            sendAddress
          }
        })
      }
      this.handleClose()
    },

    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped>
.forecast-time-date {
  width: 250px;
  padding-bottom: 8px;
  .forecast-time-date-picker {
    width: calc(250px - 70px);
  }
}
</style>
