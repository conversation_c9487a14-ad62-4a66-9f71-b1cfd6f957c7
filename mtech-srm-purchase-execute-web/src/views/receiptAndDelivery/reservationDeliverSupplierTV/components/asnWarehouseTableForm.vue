<template>
  <div class="full-height">
    <div>
      <mt-template-page
        ref="templateRef"
        :template-config="componentConfig"
        @handleCustomReset="handleCustomReset"
        @handleCustomSearch="handleCustomSearch"
        :hidden-tabs="true"
      >
        <template v-if="apiType === 1" v-slot:quick-search-form>
          <mt-form ref="searchFormRef" :model="searchFormModel" :rules="rules">
            <mt-form-item prop="vmiOrderCode" :label="$t('单据号')" label-style="top">
              <mt-input
                v-model="searchFormModel.vmiOrderCode"
                :show-clear-button="true"
                :placeholder="$t('请输入单据号')"
              />
            </mt-form-item>

            <mt-form-item prop="companyName" :label="$t('公司名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.companyName"
                :show-clear-button="true"
                :placeholder="$t('请输入公司名称')"
              />
            </mt-form-item>

            <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.supplierName"
                :show-clear-button="true"
                :placeholder="$t('请输入供应商名称')"
              />
            </mt-form-item>
          </mt-form>
        </template>
        <template v-else v-slot:quick-search-form>
          <mt-form ref="searchFormRef" :model="searchFormModel" :rules="rules">
            <mt-form-item prop="deliveryCode" :label="$t('送货单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.deliveryCode"
                :show-clear-button="true"
                :placeholder="$t('请输入送货单号')"
              />
            </mt-form-item>

            <mt-form-item prop="companyCode" :label="$t('公司代码')" label-style="top">
              <mt-input
                v-model="searchFormModel.companyCode"
                :show-clear-button="true"
                :placeholder="$t('请输入公司代码')"
              />
            </mt-form-item>

            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-multi-select
                v-model="searchFormModel.status"
                type="multipleChoice"
                :placeholder="$t('请选择状态')"
                :show-clear-button="true"
                :data-source="statusOptions"
                :fields="{ text: 'text', value: 'value' }"
              />
            </mt-form-item>
            <mt-form-item prop="siteCode" :label="$t('工厂代码')" label-style="top">
              <mt-input
                v-model="searchFormModel.siteCode"
                :show-clear-button="true"
                :placeholder="$t('请输入工厂代码')"
              />
            </mt-form-item>
          </mt-form>
        </template>
      </mt-template-page>
    </div>

    <div>
      <mt-page
        class="full-keep custom-page"
        :page-settings="pageSettings"
        :total-pages="pageSettings.totals"
        @currentChange="currentPageChange"
        @sizeChange="currentSizeChange"
      />
    </div>
  </div>
</template>
<script>
/* eslint-disable prettier/prettier */
import { asnTableColumnData } from '../config/index.js'
import { AsnTableColumnData, AsnWarehouseTableColumnData } from '../config/constant'
// import { BASE_TENANT } from "@/utils/constant";

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  props: {
    apiType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      statusOptions: [
        { value: 2, text: this.$t('发货中') },
        { value: 3, text: this.$t('已完成') },
        { value: 4, text: this.$t('已取消') },
        { value: 5, text: this.$t('已关闭') },
        { value: 6, text: this.$t('部分收货') }
      ],
      rules: {},
      searchFormModel: {
        vmiOrderCode: '', // 单据号
        companyName: '', // 公司名称
        supplierName: '', // 供应商名称
        companyCode: '',
        status: null,
        deliveryCode: '',
        siteCode: ''
      },
      copySearchFormMode: {
        vmiOrderCode: '', // 单据号
        companyName: '', // 公司名称
        supplierName: '', // 供应商名称
        companyCode: '',
        status: null,
        deliveryCode: '',
        siteCode: ''
      },
      // 分页参数
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 5,
        pageSizes: [10, 20, 50, 100, 200],
        totals: 1,
        totalRecordsCount: 0
      },
      dialogTitle: '',
      apiWaitingQuantity: 0, // 调用的api正在等待数
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      componentConfig: [
        {
          activatedRefresh: false,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          isUseCustomSearch: true, // 使用自定义查询参数
          isCustomSearchHandle: true, // 使用自定义查询
          toolbar: { tools: [[], []] },
          grid: {
            allowPaging: false, // 分页
            lineSelection: 0, // 选项列
            lineIndex: 1, // 序号列
            columnData: asnTableColumnData({
              data: AsnWarehouseTableColumnData
            }),
            // asyncConfig: {
            //   url: `${BASE_TENANT}/vmi-receive-order/pageForForecast`,
            // },
            dataSource: []
          }
        }
      ]
    }
  },
  mounted() {
    this.componentConfig[0].grid.columnData = asnTableColumnData({
      data: this.apiType === 0 ? AsnTableColumnData : AsnWarehouseTableColumnData
    })
    this.handleCustomSearch()
  },
  methods: {
    getSelectedRecords() {
      let selectedRowData = this.$refs.templateRef
        .getCurrentUsefulRef()
        .gridRef.getMtechGridRecords()
      return selectedRowData
    },
    handleCustomReset() {
      for (let key in this.searchFormModel) {
        this.searchFormModel[key] = null
      }
    },
    handleCustomSearch() {
      this.pageSettings.currentPage = 1
      this.handleSearchForm()
    },
    currentPageChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.handleSearchForm()
    },
    currentSizeChange(currentPageSize) {
      this.pageSettings.pageSize = currentPageSize
      this.pageSettings.currentPage = 1
      this.handleSearchForm()
    },
    handleSearchForm() {
      this.$store.commit('startLoading')
      const param = {
        ...this.searchFormModel,
        page: {
          current: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        }
      }
      const { getASNAndVMIListInfo, getASNListInfo } = this.$API.receiptAndDelivery
      const _API = this.apiType === 1 ? getASNAndVMIListInfo : getASNListInfo
      _API(param)
        .then((res) => {
          if (res && res.code === 200) {
            this.$set(this.componentConfig[0].grid, 'dataSource', res.data.records)
            this.pageSettings.totals = Math.ceil(
              Number(res.data.total) / this.pageSettings.pageSize
            )
            this.pageSettings.totalRecordsCount = res.data.total
          } else {
            Promise.reject(res)
          }
        })
        .catch((err) => {
          this.$toast({
            type: 'error',
            message: err.msg
          })
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    // 初始化
    dialogInit() {
      this.handleCustomSearch()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    clearSelection() {
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
      this.$refs.templateRef.refreshCurrentGridData()
    },
    confirm() {
      let selectedRowData = this.$refs.templateRef
        .getCurrentUsefulRef()
        .gridRef.getMtechGridRecords()

      selectedRowData.forEach((item) => {
        item.deliveryCode = item.vmiOrderCode
      })
      this.$emit('confirm', { data: selectedRowData })
      this.handleClose()
    },

    handleClose() {
      // this.$refs.dialog.ejsRef.hide();
      this.$parent.dialog.ejsRef.hide()
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
