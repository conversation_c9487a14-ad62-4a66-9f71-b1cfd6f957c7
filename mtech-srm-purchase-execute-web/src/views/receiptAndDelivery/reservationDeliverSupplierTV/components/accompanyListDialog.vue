<template>
  <!-- 预约送货-供方-编辑随车人信息 -->
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <div>
      <div
        class="item-form-box normal-border-color"
        v-for="(formItem, index) in formList"
        :key="formItem.id"
      >
        <mt-form :ref="`ruleForm${index}`" :model="formItem" :rules="rules[index]">
          <mt-form-item prop="name" :label="$t('随车人姓名')" class="">
            <mt-input
              v-model="formItem.name"
              maxlength="100"
              :show-clear-button="true"
              :disabled="false"
              :placeholder="$t('请输入')"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="idNo" :label="$t('随车人身份证号')" class="">
            <mt-input
              v-model="formItem.idNo"
              :show-clear-button="true"
              :disabled="false"
              :placeholder="$t('请输入')"
            ></mt-input>
          </mt-form-item>
          <div class="del-btn" @click="deleteRowItem(formItem.id)">
            <mt-icon name="icon_card_minus" />
          </div>
        </mt-form>
      </div>
      <div class="add-btn-row">
        <span class="add-btn" @click="addRowItem">
          <mt-icon class="btn-icon" name="icon_card_plus" />
          <span class="btn-title">{{ $t('添加随车人') }}</span></span
        >
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { RegExpMap } from '@/utils/constant'

export default {
  components: {},
  data() {
    const itemRule = {
      // 随车人姓名
      name: [
        {
          required: true,
          message: this.$t('请输入随车人姓名'),
          trigger: 'blur'
        }
      ],
      // 随车人身份证号
      idNo: [
        {
          required: true,
          validator: this.identityNumberValidator,
          trigger: 'blur'
        }
      ]
    }

    return {
      dialogTitle: '',
      formList: [{ id: Math.random().toString(36).substr(3, 8), name: null, idNo: null }],
      rules: [],
      itemRule,
      defaultRules: [itemRule],
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      const { title, data } = entryInfo
      // 赋值 进行编辑
      if (data?.length > 0) {
        const tmpForm = []
        const tmpRule = []
        data.forEach((itemData) => {
          tmpForm.push({
            id: Math.random().toString(36).substr(3, 8),
            ...itemData
          })
          tmpRule.push(this.itemRule)
        })
        this.formList = tmpForm
        this.rules = tmpRule
      }
      this.dialogTitle = title // 弹框名称
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 身份证校验
    identityNumberValidator(rule, value, callback) {
      const { idCardReg } = RegExpMap
      if (!value) {
        callback(new Error(this.$t('请输入随车人身份证号')))
      } else if (!idCardReg.test(value)) {
        callback(new Error(this.$t('身份证号格式错误')))
      } else {
        callback()
      }
    },

    confirm() {
      // 校验
      const invalid = this.validateForm()
      if (!invalid) {
        this.$emit('confirm', {
          data: this.formList.map((item) => {
            return { idNo: item.idNo, name: item.name }
          })
        })
        this.handleClose()
      }
    },

    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 添加一行
    addRowItem() {
      // 校验
      const invalid = this.validateForm()
      if (!invalid) {
        this.formList.push({
          id: Math.random().toString(36).substr(3, 8),
          name: null,
          idNo: null
        })
        this.rules.push(this.itemRule)
      }
    },
    // 删除一行
    deleteRowItem(id) {
      for (let i = 0; i < this.formList.length; i++) {
        const item = this.formList[i]
        if (item.id === id) {
          this.formList.splice(i, 1)
          break
        }
      }
    },
    // 校验
    validateForm() {
      let invalid = false
      this.formList.forEach((itemForm, index) => {
        this.$refs[`ruleForm${index}`][0].validate((valid) => {
          if (!valid) {
            invalid = true
          }
        })
      })

      return invalid
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .item-form-box .mt-form .mt-form-item {
  width: 370px !important;
}
.item-form-box {
  padding: 20px;
  margin-bottom: 18px;
  border: 1px solid;
  position: relative;
  border-radius: 10px;
  // 删除按钮
  .del-btn {
    position: absolute;
    top: 0;
    right: 0;
    margin: 16px;
    font-size: 18px;
    color: #ee5e3d;
    cursor: pointer;
  }
}
.normal-border-color {
  border-color: #e8e8e8;
}
// 添加按钮
.add-btn-row {
  color: #6386c1;
  margin-top: 8px;
  .add-btn {
    cursor: pointer;
    .btn-icon {
      font-size: 18px;
    }
    .btn-title {
      vertical-align: top;
      margin-left: 8px;
    }
  }
}
</style>
