<template>
  <!-- 预约送货-供方-勾选关联ASN -->
  <mt-dialog
    ref="dialog"
    class="display-block"
    width="60%"
    height="80%"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <AsnWarehouseTableForm :api-type="0" ref="asnFormRef" />
  </mt-dialog>
</template>

<script>
import AsnWarehouseTableForm from './asnWarehouseTableForm.vue'
// import { BASE_TENANT } from "@/utils/constant";

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  components: {
    AsnWarehouseTableForm
  },
  data() {
    return {
      rules: {},
      searchFormModel: {
        vmiOrderCode: '', // 单据号
        companyName: '', // 公司名称
        supplierName: '' // 供应商名称
      },
      copySearchFormMode: {
        vmiOrderCode: '', // 单据号
        companyName: '', // 公司名称
        supplierName: '' // 供应商名称
      },
      // 分页参数
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        pageSizes: [10, 20, 50, 100, 200],
        totals: 1,
        totalRecordsCount: 0
      },
      dialogTitle: '',
      forecastType: '',
      apiWaitingQuantity: 0, // 调用的api正在等待数
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  methods: {
    // 初始化
    dialogInit(entryInfo) {
      const { title } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.forecastType = entryInfo?.data.forecastType // 预约类型

      // this.refreshColumns()
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    confirm() {
      let selectedRowData = this.$refs.asnFormRef.getSelectedRecords()
      this.$emit('confirm', { data: selectedRowData })
      this.handleClose()
    },

    handleClose() {
      this.$refs.asnFormRef.handleCustomReset()
      this.$refs.asnFormRef.clearSelection()
      this.$refs.dialog.ejsRef.hide()
    },
    // 刷新当前 Grid
    refreshColumns() {
      // console.log(this.$refs.asnFormRef)
      // console.log(this.$refs)
      // this.$refs.asnFormRef.$refs.templateRef.refreshCurrentGridData();
      this.handleSearchForm()
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .toolbar-container {
  height: 16px;
}
::v-deep .top-filter {
  padding-top: 0;
}
::v-deep .e-dialog {
  min-width: 600px;
  .e-dlg-header-content {
    padding: 8px 12px;
  }
}
</style>
