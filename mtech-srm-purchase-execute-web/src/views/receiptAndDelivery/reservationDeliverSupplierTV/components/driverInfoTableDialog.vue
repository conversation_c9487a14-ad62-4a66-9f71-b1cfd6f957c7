<template>
  <!-- 预约送货-供方-选择送货司机 -->
  <mt-dialog
    ref="dialog"
    class="display-block"
    width="60%"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <div style="padding-top: 1rem">
      <mt-template-page ref="templateRef" :template-config="componentConfig" :hidden-tabs="true" />
    </div>
  </mt-dialog>
</template>

<script>
import { driverInfoTableColumnData } from '../config/index.js'
import { DriverInfoTableColumnData } from '../config/constant'
import { BASE_TENANT } from '@/utils/constant'

export default {
  data() {
    return {
      dialogTitle: '',
      apiWaitingQuantity: 0, // 调用的api正在等待数
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          grid: {
            allowPaging: true, // 分页
            // lineSelection: 0, // 选项列
            // lineIndex: 1, // 序号列
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: driverInfoTableColumnData({
              data: DriverInfoTableColumnData
            }),
            asyncConfig: {
              // 供方送货司机信息维护-获取司机列表
              url: `${BASE_TENANT}/supplierDriver/query`,
              defaultRules: [
                {
                  field: 'status',
                  operator: 'equal',
                  value: 1 // 启用
                }
              ]
            },
            dataSource: []
            // frozenColumns: 1,
          }
        }
      ]
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      const { title } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.refreshColumns()
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },

    confirm() {
      const selectedRowData = this.$refs.templateRef
        .getCurrentUsefulRef()
        .gridRef.getMtechGridRecords()
      this.$emit('confirm', { data: selectedRowData })
      this.handleClose()
    },

    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
