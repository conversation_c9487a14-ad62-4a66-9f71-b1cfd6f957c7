<template>
  <div class="box">
    <!-- 下面的内容 -->
    <div class="main-box">
      <mt-form
        ref="ruleForm"
        :model="logisticsData"
        :rules="rules"
        :validate-on-rule-change="false"
      >
        <!-- 发货方式 -->
        <mt-form-item prop="type" :label="$t('发货方式')">
          <mt-select
            v-model="logisticsData.type"
            :disabled="true"
            :data-source="ShippingTypeOptions"
            placeholder=""
          ></mt-select>
        </mt-form-item>
        <!-- 快递-物流公司 -->
        <mt-form-item
          v-show="logisticsData.type == ShippingType.express"
          prop="logisticsCompanyName"
          :label="$t('物流公司')"
        >
          <mt-input
            v-model="logisticsData.logisticsCompanyName"
            :disabled="true"
            :placeholder="$t('物流公司')"
          ></mt-input>
        </mt-form-item>
        <!-- 快递-物流单号 -->
        <mt-form-item
          v-show="logisticsData.type == ShippingType.express"
          prop="logisticsNo"
          :label="$t('物流单号')"
        >
          <mt-input
            v-model="logisticsData.logisticsNo"
            :disabled="true"
            :placeholder="$t('物流单号')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-司机名称 -->
        <mt-form-item
          v-show="logisticsData.type == ShippingType.logistics"
          prop="driverName"
          :label="$t('司机名称')"
        >
          <mt-input
            v-model="logisticsData.driverName"
            :disabled="true"
            :placeholder="$t('司机名称')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-司机身份证号 -->
        <mt-form-item
          v-show="logisticsData.type == ShippingType.logistics"
          prop="driverNo"
          :label="$t('司机身份证号')"
        >
          <mt-input
            v-model="logisticsData.driverNo"
            :disabled="true"
            :placeholder="$t('司机身份证号')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-司机联系方式 -->
        <mt-form-item
          v-show="logisticsData.type == ShippingType.logistics"
          prop="driverPhone"
          :label="$t('司机联系方式')"
        >
          <mt-input
            v-model="logisticsData.driverPhone"
            :disabled="true"
            :placeholder="$t('司机联系方式')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-车牌 -->
        <mt-form-item
          v-show="logisticsData.type == ShippingType.logistics"
          prop="carNo"
          :label="$t('车牌')"
        >
          <mt-input
            v-model="logisticsData.carNo"
            :disabled="true"
            :placeholder="$t('车牌')"
          ></mt-input>
        </mt-form-item>
        <!-- 件数 -->
        <mt-form-item prop="number" :label="$t('件数')">
          <mt-input
            v-model="logisticsData.number"
            :disabled="true"
            :placeholder="$t('件数')"
          ></mt-input>
        </mt-form-item>

        <!-- 备注 -->
        <mt-form-item class="full-width" prop="remark" :label="$t('备注')" :show-message="false">
          <mt-input
            v-model="logisticsData.remark"
            :disabled="true"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { ShippingTypeOptions, ShippingType } from '../config/constant'

export default {
  props: {
    logisticsData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      ShippingTypeOptions,
      ShippingType,
      isExpand: true,
      rules: {}
    }
  },
  mounted() {},
  filters: {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.box {
  padding: 20px 0 20px 20px;

  .main-box .mt-form-item {
    width: calc(20% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
  }
}
</style>
