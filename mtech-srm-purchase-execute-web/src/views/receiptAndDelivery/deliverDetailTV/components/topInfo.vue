<template>
  <div :class="['detail-top-info', !isExpand && 'detail-top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div :class="[statusToClass(headerInfo.status.value), 'mr20']">
        {{ headerInfo.status.value | statusFormat }}
      </div>
      <div class="infos mr20">{{ $t('送货单号') }}：{{ headerInfo.deliveryCode }}</div>
      <div class="infos mr20">{{ $t('更新人') }}：{{ getUpdateUserName(headerInfo) }}</div>
      <div class="infos">{{ $t('更新日期') }}：{{ getUpdateTime(headerInfo) }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <span class="header-box-btn" v-waves type="info" @click="goBack">{{ $t('返回') }}</span>
      <div class="sort-box" @click="doExpand">
        <span>{{ isExpand ? $t('收起') : $t('展开') }}</span>
        <i
          class="mt-icons mt-icon-MT_DownArrow"
          :class="isExpand ? 'expendIcon' : 'unExpendIcon'"
        />
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="supplier" :label="$t('供应商')">
          <mt-input v-model="headerInfo.supplier" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="site" :label="$t('工厂')">
          <mt-input v-model="headerInfo.site" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="warehouse" :label="$t('库存地点')">
          <mt-input v-model="headerInfo.warehouse" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="sendTime" :label="$t('发货日期')">
          <mt-date-time-picker
            v-model="headerInfo.sendTime"
            :disabled="true"
            :show-clear-button="true"
            :allow-edit="false"
          />
        </mt-form-item>
        <mt-form-item prop="forecastArriveTime" :label="$t('预计到货日期')">
          <mt-date-time-picker
            v-model="headerInfo.forecastArriveTime"
            :disabled="true"
            :show-clear-button="true"
            :allow-edit="false"
          />
        </mt-form-item>
        <mt-form-item prop="company" :label="$t('公司')">
          <mt-input v-model="headerInfo.company" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="deliveryType" :label="$t('送货单类型')">
          <mt-input v-model="headerInfo.deliveryType" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="shipType" :label="$t('送货方式')">
          <mt-input v-model="headerInfo.shipType" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="receiveAddress" :label="$t('收货地址')">
          <mt-input v-model="headerInfo.receiveAddress" :disabled="true" />
        </mt-form-item>

        <mt-form-item prop="receiverContactName" :label="$t('收货联系人')">
          <mt-input v-model="headerInfo.receiverContactName" :disabled="true" />
        </mt-form-item>

        <mt-form-item prop="receiverContact" :label="$t('联系人方式')">
          <mt-input v-model="headerInfo.receiverContact" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="receiver" :label="$t('加工商')">
          <mt-input v-model="headerInfo.receiver" :disabled="true" />
        </mt-form-item>

        <mt-form-item prop="thirdTenant" :label="$t('第三方物流商')">
          <mt-input v-model="headerInfo.thirdTenant" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="outsourcedType" :label="$t('委外方式')">
          <mt-input v-model="headerInfo.outsourcedType" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="associateOuterDocNo" :label="$t('来源单号')">
          <mt-input v-model="headerInfo.associateOuterDocNo" disabled />
        </mt-form-item>
        <mt-form-item prop="doNo" :label="$t('来源单号2')">
          <mt-input v-model="headerInfo.doNo" disabled />
        </mt-form-item>
        <mt-form-item prop="associatedNo" :label="$t('关联单据号')">
          <mt-input v-model="headerInfo.associatedNo" disabled />
        </mt-form-item>
        <!-- 供方备注 -->
        <mt-form-item prop="remark" :label="$t('备注')" style="width: calc(80% - 20px) !important">
          <mt-input v-model="headerInfo.remark" :disabled="true" />
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { StatusText, StatusCssClass, ShipTypeOptions } from '../config/constant'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      ShipTypeOptions,
      isExpand: true,
      rules: {}
    }
  },
  filters: {
    statusFormat(value) {
      if (!StatusText[value]) {
        return value
      } else {
        return StatusText[value]
      }
    }
  },
  methods: {
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    },
    // 状态 转对应的 css class
    statusToClass(value) {
      let cssClass = ''
      if (StatusCssClass[value]) {
        cssClass = StatusCssClass[value]
      }
      return cssClass
    },
    dateFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }

      return str
    },
    timeFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'HH:MM:SS', value })
      } else {
        str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
      }

      return str
    },
    // 更新人
    getUpdateUserName(data) {
      let userName = ''
      if (data.updateUserName) {
        userName = data.updateUserName // 更新人
      } else {
        userName = data.createUserName // 创建人
      }
      return userName
    },
    // 更新日期
    getUpdateTime(data) {
      let dateStr = ''
      let timeStr = ''
      if (data.updateTime) {
        dateStr = this.dateFormat(data.updateTime) // 更新日期
        timeStr = this.timeFormat(data.updateTime) // 更新时间
      } else {
        dateStr = this.dateFormat(data.createTime) // 创建日期
        timeStr = this.timeFormat(data.createTime) // 创建时间
      }
      return `${dateStr} ${timeStr}`
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
