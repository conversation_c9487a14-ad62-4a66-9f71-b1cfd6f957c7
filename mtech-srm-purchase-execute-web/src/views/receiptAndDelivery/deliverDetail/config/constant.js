import { i18n } from '@/main.js'

// tab
export const Tab = {
  list: 1, // 物料信息
  details: 2 // 物流信息
}

// 送货单类型 1-关联采购订单,2-无采购订单
export const DeliveryType = {
  associatedOrder: 1, //
  noOrder: 2, //
  jit: 3, //
  noNeed: 4, //
  vmi: 5, //
  gc: 6 //
}
// 送货单类型 text
export const DeliveryTypeText = {
  [DeliveryType.associatedOrder]: i18n.t('采购订单'),
  [DeliveryType.noOrder]: i18n.t('交货计划'),
  [DeliveryType.jit]: i18n.t('JIT'),
  [DeliveryType.noNeed]: i18n.t('无需求'),
  [DeliveryType.vmi]: i18n.t('vmi'),
  [DeliveryType.gc]: i18n.t('钢材')
}
// 送货单类型 对应的 Options
export const deliveryOrderType = [
  {
    // 关联采购订单
    value: DeliveryType.associatedOrder,
    text: DeliveryTypeText[DeliveryType.associatedOrder],
    cssClass: ''
  },
  {
    // 无采购订单
    value: DeliveryType.noOrder,
    text: DeliveryTypeText[DeliveryType.noOrder],
    cssClass: ''
  },
  {
    // 关联采购订单
    value: DeliveryType.jit,
    text: DeliveryTypeText[DeliveryType.jit],
    cssClass: ''
  },
  {
    // 无采购订单
    value: DeliveryType.noNeed,
    text: DeliveryTypeText[DeliveryType.noNeed],
    cssClass: ''
  },
  {
    // 关联采购订单
    value: DeliveryType.vmi,
    text: DeliveryTypeText[DeliveryType.vmi],
    cssClass: ''
  },
  {
    // 无采购订单
    value: DeliveryType.gc,
    text: DeliveryTypeText[DeliveryType.gc],
    cssClass: ''
  }
]

// 送货方式 0-直送,1-非直送
export const ShipType = {
  direct: 0, // 直送
  nonDirect: 1 // 非直送
}
// 送货单类型 text
export const ShipTypeText = {
  [ShipType.direct]: i18n.t('直送'),
  [ShipType.nonDirect]: i18n.t('非直送')
}
// 送货单类型 对应的 Options
export const ShipTypeOptions = [
  {
    // 直送
    value: ShipType.direct,
    text: ShipTypeText[ShipType.direct],
    cssClass: ''
  },
  {
    // 非直送
    value: ShipType.nonDirect,
    text: ShipTypeText[ShipType.nonDirect],
    cssClass: ''
  }
]

// 交货方式:1-采购订单,2-交货计划,3-JIT,4-无需求
export const TheDeliveryType = {
  purchase: 1, // 采购订单
  plan: 2, // 交货计划
  jit: 3, // jit
  noNeed: 4 // 无需求
}
// 交货方式 text
export const TheDeliveryTypeText = {
  [TheDeliveryType.purchase]: i18n.t('采购订单'),
  [TheDeliveryType.plan]: i18n.t('交货计划'),
  [TheDeliveryType.jit]: i18n.t('JIT'),
  [TheDeliveryType.noNeed]: i18n.t('无需求')
}
// 交货方式 对应的 Options
export const ThedeliveryOrderType = [
  {
    // 采购订单
    value: TheDeliveryType.purchase,
    text: TheDeliveryTypeText[TheDeliveryType.purchase],
    cssClass: ''
  },
  {
    // 交货计划
    value: TheDeliveryType.plan,
    text: TheDeliveryTypeText[TheDeliveryType.plan],
    cssClass: ''
  },
  {
    // JIT
    value: TheDeliveryType.jit,
    text: TheDeliveryTypeText[TheDeliveryType.jit],
    cssClass: ''
  },
  {
    // 无需求
    value: TheDeliveryType.noNeed,
    text: TheDeliveryTypeText[TheDeliveryType.noNeed],
    cssClass: ''
  },
  {
    value: 5,
    text: 'vmi',
    cssClass: ''
  },
  {
    value: 6,
    text: i18n.t('钢材'),
    cssClass: ''
  }
]

// 状态 状态:1-新建,2-发货中,3-已完成,4-已取消,5-已关闭
export const Status = {
  new: 1, // 新建
  shipping: 2, // 发货中
  completed: 3, // 已完成
  cancelled: 4, // 已取消
  closed: 5 // 已关闭
}
// 状态 text
export const StatusText = {
  [Status.new]: i18n.t('新建'),
  [Status.shipping]: i18n.t('发货中'),
  [Status.completed]: i18n.t('已完成'),
  [Status.cancelled]: i18n.t('已取消'),
  [Status.closed]: i18n.t('已关闭')
}
// 状态 对应的 css class
export const StatusCssClass = {
  [Status.new]: 'col-active',
  [Status.shipping]: 'col-active',
  [Status.completed]: 'col-active',
  [Status.cancelled]: 'col-active',
  [Status.closed]: 'col-inactive'
}
// 状态 对应的 Options
export const StatusOptions = [
  {
    // 新建
    value: Status.new,
    text: StatusText[Status.new],
    cssClass: StatusCssClass[Status.new]
  },
  {
    // 发货中
    value: Status.shipping,
    text: StatusText[Status.shipping],
    cssClass: StatusCssClass[Status.shipping]
  },
  {
    // 已完成
    value: Status.completed,
    text: StatusText[Status.completed],
    cssClass: StatusCssClass[Status.completed]
  },
  {
    // 已取消
    value: Status.cancelled,
    text: StatusText[Status.cancelled],
    cssClass: StatusCssClass[Status.cancelled]
  },
  {
    // 已关闭
    value: Status.closed,
    text: StatusText[Status.closed],
    cssClass: StatusCssClass[Status.closed]
  }
]

// 发货方式:1-快递配送，2-物流配送
export const ShippingType = {
  express: 1, // 快递配送
  logistics: 2 // 物流配送
}
// 发货方式 text
export const ShippingTypeText = {
  [ShippingType.express]: i18n.t('快递配送'),
  [ShippingType.logistics]: i18n.t('物流配送')
}
// 发货方式 对应的 Options
export const ShippingTypeOptions = [
  {
    // 快递配送
    value: ShippingType.express,
    text: ShippingTypeText[ShippingType.express],
    cssClass: ''
  },
  {
    // 物流配送
    value: ShippingType.logistics,
    text: ShippingTypeText[ShippingType.logistics],
    cssClass: ''
  }
]

// jit
export const JitOptions = [
  { value: 0, text: i18n.t('否'), cssClass: '' },
  { value: 1, text: i18n.t('是'), cssClass: '' }
]

// 物料信息 表格列数据
export const ColumnDataTab1 = [
  {
    fieldCode: 'deliveryLineNo', // 行号
    fieldName: i18n.t('行号')
  },
  {
    fieldCode: 'status', // 状态 状态:1-新建,2-发货中,3-已完成,4-已取消,5-已关闭
    fieldName: i18n.t('状态')
  },
  {
    width: '120',
    fieldCode: 'onWayStatus',
    fieldName: i18n.t('在途状态'),
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { text: i18n.t('未出发'), value: 0, cssClass: 'col-active' },
        { text: i18n.t('已入园'), value: 1, cssClass: 'col-active' },
        { text: i18n.t('已出发'), value: 2, cssClass: 'col-active' },
        { text: i18n.t('已报到'), value: 3, cssClass: 'col-active' },
        { text: i18n.t('已取消'), value: 4, cssClass: 'col-active' },
        { text: i18n.t('已关闭'), value: 5, cssClass: 'col-active' }
      ]
    }
  },
  {
    fieldCode: 'itemCode', // 物料 itemName code-name
    fieldName: i18n.t('物料')
  },
  {
    fieldCode: 'jit',
    fieldName: i18n.t('是否JIT')
  },
  {
    fieldCode: 'orderCode', // 采购订单号
    fieldName: i18n.t('采购订单号')
  },
  {
    width: '150',
    field: 'jitDeliveryNumber',
    headerText: i18n.t('JIT编号'),
    searchOptions: {
      renameField: 'deliveryNumber'
    }
  },
  {
    field: 'inputDate', //
    headerText: i18n.t('凭证创建日期')
  },

  {
    field: 'postingDate', //
    headerText: i18n.t('过账日期')
  },
  {
    fieldCode: 'lineNo', // 采购订单行号
    fieldName: i18n.t('采购订单行号')
  },
  {
    fieldCode: 'demandDate', // 需求日期
    fieldName: i18n.t('需求日期')
  },
  {
    fieldCode: 'demandTime', // 需求时间
    fieldName: i18n.t('需求时间')
  },
  {
    fieldCode: 'deliveryQuantity', // 本次送货数量
    fieldName: i18n.t('本次送货数量')
  },
  {
    fieldCode: 'receiveQuantity', // 收货数量
    fieldName: i18n.t('收货数量')
  },
  {
    fieldCode: 'rejectQuantity', // 拒绝数量
    fieldName: i18n.t('拒绝数量')
  },
  {
    fieldCode: 'rejectReason', // 拒绝原因
    fieldName: i18n.t('拒绝原因')
  },
  {
    fieldCode: 'transferPlanName', // 计划员
    fieldName: i18n.t('计划员')
  },

  {
    fieldCode: 'deliveryRemark', // 送货单备注
    fieldName: i18n.t('送货单备注')
  },
  {
    fieldCode: 'deliveryNumber', // 交货编号
    fieldName: i18n.t('交货编号')
  },
  {
    width: '150',
    field: 'jitDeliveryNumber',
    headerText: i18n.t('JIT编号'),
    searchOptions: {
      renameField: 'deliveryNumber'
    }
  },
  {
    fieldCode: 'buyerOrgCode', // 采购组 buyerOrgName code-name
    fieldName: i18n.t('采购组')
  },
  {
    width: '150',
    field: 'takeNo',
    headerText: i18n.t('车牌号')
  },
  {
    fieldCode: 'unitCode', // 单位 基本单位名称 unitName code-name
    fieldName: i18n.t('单位')
  },
  {
    fieldCode: 'deliveryType', // 送货单类型 1-关联采购订单,2-无采购订单
    fieldName: i18n.t('送货单类型')
  },
  // {
  //   fieldCode: "deliveryType", // 交货方式:1-采购订单,2-交货计划,3-JIT,4-无需求
  //   fieldName: i18n.t("交货方式"),
  // },
  {
    fieldCode: 'workOrderNo', // 关联工单号
    fieldName: i18n.t('关联工单号')
  },
  {
    fieldCode: 'domesticDemandFlag',
    fieldName: i18n.t('是否内需跟单'),
    width: '160',
    valueConverter: {
      type: 'map',
      map: { E: i18n.t('是'), F: i18n.t('否') }
    }
  },
  {
    fieldCode: 'saleOrderNo', // 关联销售订单号
    fieldName: i18n.t('销售订单号')
  },
  {
    fieldCode: 'saleOrderLineNo', // 关联销售订单行号
    fieldName: i18n.t('销售订单行号')
  },
  {
    fieldCode: 'domesticDemandCode',
    fieldName: i18n.t('内需单号'),
    width: '150'
  },
  {
    fieldCode: 'bomCode', // BOM号
    fieldName: i18n.t('BOM号')
  },
  {
    fieldCode: 'productCode', // 关联产品代码
    fieldName: i18n.t('关联产品代码')
  },
  {
    fieldCode: 'workCenterCode', // 工作中心 workCenterName code-name
    fieldName: i18n.t('工作中心')
  },
  {
    fieldCode: 'processName', // 工序名称
    fieldName: i18n.t('工序名称')
  },
  {
    fieldCode: 'sendAddress', // 送货地址
    fieldName: i18n.t('送货地址')
  },
  {
    fieldCode: 'batchCode', // 卷号
    fieldName: i18n.t('卷号')
  },
  {
    fieldCode: 'limitQuantity', // 限量数量
    fieldName: i18n.t('限量数量')
  },
  {
    fieldCode: 'warehouseClerkName', // 仓管员
    fieldName: i18n.t('仓管员')
  },
  {
    fieldCode: 'dispatcherName', // 调度员
    fieldName: i18n.t('调度员')
  },
  {
    fieldCode: 'receiveTime', // 收货时间 到货时间
    fieldName: i18n.t('收货时间')
  },
  // FIXME: 暂时不展示
  // {
  //   fieldCode: "inventoryQuantity", // 入库数量
  //   fieldName: i18n.t("入库数量"),
  // },
  // {
  //   fieldCode: "inventoryTime", // 入库日期
  //   fieldName: i18n.t("入库日期"),
  // },
  {
    fieldCode: 'receiveSupplierCode', // 收货供应商 receiveSupplierName code-name
    fieldName: i18n.t('收货供应商')
  },
  {
    fieldCode: 'productLine',
    fieldName: i18n.t('生产线')
  },
  {
    fieldCode: 'cancelPersonName', // 取消人
    fieldName: i18n.t('取消人')
  },
  {
    fieldCode: 'cancelTime', // 取消时间
    fieldName: i18n.t('取消时间')
  },
  {
    fieldCode: 'closeTime', // 取消时间
    fieldName: i18n.t('关闭时间')
  },
  {
    fieldCode: 'closePersonName', // 取消时间
    fieldName: i18n.t('关闭人')
  },
  {
    fieldCode: 'collectorMark', // 代收标识
    fieldName: i18n.t('代收标识')
  },
  {
    fieldCode: 'collectorName', // 代收人
    fieldName: i18n.t('代收人')
  },
  {
    fieldCode: 'createUserName', // 创建人
    fieldName: i18n.t('创建人')
  },
  {
    fieldCode: 'createTime', // 创建时间
    fieldName: i18n.t('创建时间')
  },
  {
    fieldCode: 'remark', // 行备注
    fieldName: i18n.t('行备注')
  }
]
