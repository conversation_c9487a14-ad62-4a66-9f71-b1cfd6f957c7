import { deliveryOrderType, ThedeliveryOrderType, StatusOptions, JitOptions } from './constant'
import { timeDate } from './columnComponent'
import { MasterDataSelect } from '@/utils/constant'
import { codeNameColumn } from '@/utils/utils'

// 格式化表格动态数据
export const formatTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150'
    }
    if (col.fieldCode === 'orderDeliveryType') {
      // 送货明细-送货类型
      defaultCol.valueConverter = {
        type: 'map',
        map: deliveryOrderType
      }
    } else if (col.fieldCode === 'deliveryType') {
      // 送货明细-交货方式
      defaultCol.valueConverter = {
        type: 'map',
        map: ThedeliveryOrderType
      }
    } else if (col.fieldCode === 'status') {
      // 状态
      defaultCol.valueConverter = {
        type: 'map',
        map: StatusOptions
      }
    } else if (col.fieldCode === 'saleOrderNo') {
      // 关联销售订单号
      defaultCol.width = '250'
    } else if (col.fieldCode === 'saleOrderLineNo') {
      // 关联销售订单行号
      defaultCol.width = '250'
    } else if (
      col.fieldCode === 'cancelTime' ||
      col.fieldCode === 'sendTime' ||
      col.fieldCode === 'forecastArriveTime' ||
      col.fieldCode === 'inventoryTime' ||
      col.fieldCode === 'inputDate' ||
      col.fieldCode === 'closeTime'
    ) {
      // cancelTime 取消时间 integer
      // sendTime 发货日期 integer
      // forecastArriveTime 预计到货日期 integer
      // inventoryTime 入库日期 integer
      defaultCol.template = timeDate({
        dataKey: col.fieldCode,
        isDateTime: true
      })
    } else if (col.fieldCode === 'demandDate') {
      // demandDate 需求日期 string
      defaultCol.template = timeDate({
        dataKey: col.fieldCode,
        isDate: true
      })
    } else if (col.fieldCode === 'demandTime' || col.fieldCode === 'receiveTime') {
      // demandTime 需求时间 string
      // receiveTime 收货时间 integer
      defaultCol.template = timeDate({
        dataKey: col.fieldCode,
        isTime: true
      })
    } else if (
      col.fieldCode === 'createUserName' ||
      col.fieldCode === 'transferPlanName' ||
      col.fieldCode === 'collectorName' ||
      col.fieldCode === 'cancelPersonName' ||
      col.fieldCode === 'dispatcherName' ||
      col.fieldCode === 'warehouseClerkName'
    ) {
      // 创建人
      // 计划员
      // 代收人
      // 取消人
      // 调度员
      // 仓管员
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.staff,
        fields: { text: 'title', value: 'employeeName' }
      }
    } else if (col.fieldCode === 'jit') {
      // 是否JIT
      defaultCol.valueConverter = {
        type: 'map',
        map: JitOptions
      }
    } else if (col.fieldCode === 'companyCode') {
      // 公司
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'companyCode',
        secondKey: 'companyName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessCompany
      }
    } else if (col.fieldCode === 'siteCode') {
      // 工厂
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.factoryAddress
      }
    } else if (col.fieldCode === 'supplierCode') {
      // 供应商
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.supplier
      }
    } else if (col.fieldCode === 'thirdTenantCode') {
      // 第三方物流商 即：供应商
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'thirdTenantCode',
        secondKey: 'thirdTenantName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.supplier
      }
    } else if (col.fieldCode === 'receiveSupplierCode') {
      // 收货供应商 即：供应商
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'receiveSupplierCode',
        secondKey: 'receiveSupplierName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.supplier
      }
    } else if (col.fieldCode === 'itemCode') {
      // 物料
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'itemCode',
        secondKey: 'itemName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // defaultCol.searchOptions = {
      //   ...MasterDataSelect.material,
      // };
    } else if (col.fieldCode === 'buyerOrgCode') {
      // 采购组
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'buyerOrgCode',
        secondKey: 'buyerOrgName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessGroup
      }
    } else if (col.fieldCode === 'unitCode') {
      // 单位
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'unitCode',
        secondKey: 'unitName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.unit
      }
    } else if (col.fieldCode === 'workCenterCode') {
      // 工作中心
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'workCenterCode',
        secondKey: 'workCenterName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.workCenter
      }
    }
    colData.push(defaultCol)
  })

  return colData
}
