import { i18n } from '@/main.js'
import { TabIndex } from '../../supplyPlanSupplierTV/config/constant.js'
import Vue from 'vue'
import select from '../components/select.vue'
import { RegExpMap } from '@/utils/constant'
import dayjs from 'dayjs'
import { utils } from '@mtech-common/utils'
import { addCodeNameKeyInList } from '@/utils/utils'
// Toolbar 按钮
export const Toolbar = {
  // 交货计划 跳转过来时
  [TabIndex.deliverySchedule]: [
    // 左
    [
      {
        id: 'CreateDelivery',
        icon: 'icon_table_submit',
        title: i18n.t('提交创建送货单')
      },
      {
        id: 'Rematch',
        icon: 'icon_table_matching',
        title: i18n.t('重新匹配')
      }
      // {
      //   id: "TableImport",
      //   icon: "icon_solid_Import",
      //   title: i18n.t("导入"),
      // },
    ],
    // 右
    []
  ],
  // 采购订单 跳转过来时
  [TabIndex.purchaseOrder]: [
    // 左
    [
      {
        id: 'CreateDelivery',
        icon: 'icon_table_submit',
        title: i18n.t('提交创建送货单')
      }
      // {
      //   id: "TableImport",
      //   icon: "icon_solid_Import",
      //   title: i18n.t("导入"),
      // },
    ],
    // 右
    []
  ],
  // JIT供货计划 跳转过来时
  [TabIndex.jit]: [
    // 左
    [
      {
        id: 'CreateDelivery',
        icon: 'icon_table_submit',
        title: i18n.t('提交创建送货单')
      },
      {
        id: 'Rematch',
        icon: 'icon_table_matching',
        title: i18n.t('重新匹配')
      }
      // {
      //   id: "TableImport",
      //   icon: "icon_solid_Import",
      //   title: i18n.t("导入"),
      // },
    ],
    // 右
    []
  ],
  // 屏发货指导
  [TabIndex.guide]: [
    // 左
    [
      {
        id: 'CreateDelivery',
        icon: 'icon_table_submit',
        title: i18n.t('提交创建送货单')
      },
      {
        id: 'Rematch',
        icon: 'icon_table_matching',
        title: i18n.t('重新匹配')
      }
    ],
    // 右
    []
  ]
}

// 可创建送货单
export const CreateMark = {
  true: true, // 可创建
  false: false // 不可创建
}
// 可创建送货单
export const CreateMarkText = {
  [CreateMark.true]: i18n.t('可创建'),
  [CreateMark.false]: i18n.t('不可创建')
}
// 可创建送货单 对应的 css class
export const CreateMarkCssClass = {
  [CreateMark.true]: 'col-active', // 可创建
  [CreateMark.false]: 'col-inactive' // 不可创建
}

// 交货计划
export const ColumnDataDeliverySchedule = [
  {
    fieldCode: 'createMark',
    fieldName: i18n.t('是否可创建')
  },
  {
    fieldCode: 'denyReason',
    fieldName: i18n.t('无法创建原因')
  },
  {
    fieldCode: 'demandDate',
    fieldName: i18n.t('需求日期'),
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `<span>{{ getTimeFmt() }}</span>`,
          methods: {
            getTimeFmt() {
              if (this.data[this.data.column.field]) {
                const date = Number(this.data[this.data.column.field])
                return dayjs(date).format('YYYY-MM-DD')
              }
              return ''
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'itemCode',
    fieldName: i18n.t('物料编码')
  },
  {
    fieldCode: 'itemName',
    fieldName: i18n.t('物料名称')
  },
  {
    fieldCode: 'siteCode',
    fieldName: i18n.t('工厂'),
    width: 100
  },
  {
    fieldCode: 'warehouseCode',
    fieldName: i18n.t('库存地点'),
    width: 100
  },
  {
    fieldCode: 'supplierNum',
    fieldName: i18n.t('需求数量')
  },
  {
    fieldCode: 'presentDeliveryNum',
    fieldName: i18n.t('本次送货数量')
  },
  {
    fieldCode: 'remark', // 供应商备注
    fieldName: i18n.t('供应商备注'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<mt-input v-model="data.remark"  @blur="notChange"></mt-input>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {
            notChange() {
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'remark',
                value: this.data.remark
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'receiveAddress',
    fieldName: i18n.t('收货地址'),
    width: 250,
    template: () => ({ template: select })
  },
  {
    fieldCode: 'isBatch',
    fieldName: i18n.t('是否按批次'),
    template: function () {
      const template = {
        template: `<span>{{ data['isBatch'] === 'Y' ? $t('是') : $t('否') }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        methods: {}
      }
      return {
        template
      }
    }
  },
  {
    fieldCode: 'saleOrder',
    fieldName: i18n.t('销售订单号'),
    clipMode: 'Ellipsis',
    template: () => {
      return {
        template: Vue.component('actionInput1', {
          template: `<mt-input v-model="data.saleOrder"  @blur="batchCodeChange"></mt-input>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {
            batchCodeChange() {
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'saleOrder',
                value: this.data.saleOrder
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'workCenterCode',
    fieldName: i18n.t('工作中心'),
    width: 250,
    clipMode: 'Ellipsis',
    template: () => {
      return {
        template: Vue.component('workCenterCodeSelect', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-select
                v-model="data.workCenterCode"
                :filtering="doGetDataSource"
                :data-source="workCenterOptions"
                :fields="{ text: 'theCodeName', value: 'workCenterCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                @change="workCenterCodeChange"
                :placeholder="$t('请选择')"
                :open-dispatch-change="false"
                :disabled="disabled"
              ></mt-select>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              workCenterOptions: [], // 工作中心 下拉选项
              doGetDataSource: () => {},
              disabled: false
            }
          },
          mounted() {
            this.initGetWorkCenter()
            this.doGetDataSource = utils.debounce(this.getWorkCenter, 1000)
          },
          methods: {
            // 主数据 工作中心
            getWorkCenter(args) {
              const { text, updateData, setSelectData } = args
              const params = {
                siteCode: this.data.siteCode,
                workCenterCode: text
              }
              this.$API.receiptAndDelivery.getWorkCenterBySite(params).then((res) => {
                if (res) {
                  const list = res?.data || []
                  this.workCenterOptions = addCodeNameKeyInList({
                    firstKey: 'workCenterCode',
                    secondKey: 'workCenterName',
                    list
                  })
                  if (updateData) {
                    this.$nextTick(() => {
                      updateData(this.workCenterOptions)
                    })
                  }
                  if (setSelectData) {
                    this.$nextTick(() => {
                      setSelectData()
                    })
                  }
                }
              })
            },
            // 初始化检索 工作中心
            initGetWorkCenter() {
              const workCenterCode = this.data.workCenterCode
              this.getWorkCenter({
                text: workCenterCode,
                setSelectData: () => {
                  // api获取数据后重新赋值，防止没有赋上值得情况
                  this.data.workCenterCode = workCenterCode
                }
              })
            },
            // 工作中心 change
            workCenterCodeChange(args) {
              const { itemData } = args
              if (itemData) {
                // rowDataTemp[rowDataTemp.length - 1].workCenterId = itemData.id; // id
                // rowDataTemp[rowDataTemp.length - 1].workCenterCode =
                //   itemData.workCenterCode; // code
                // rowDataTemp[rowDataTemp.length - 1].workCenterCode =
                //   itemData.workCenterName; // name
                this.$parent.$emit('cellEdit', {
                  index: Number(this.data.index),
                  key: 'workCenterCode',
                  value: itemData.workCenterCode
                })
              } else {
                this.$parent.$emit('cellEdit', {
                  index: Number(this.data.index),
                  key: 'workCenterCode',
                  value: null
                })
              }
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'consignee',
    fieldName: i18n.t('收货联系人'),
    clipMode: 'Ellipsis',
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<mt-input v-model="data.consignee"  @blur="notChange"></mt-input>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {
            notChange() {
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'consignee',
                value: this.data.consignee
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'contact',
    fieldName: i18n.t('收货联系方式'),
    clipMode: 'Ellipsis',
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<mt-input v-model="data.contact"  @blur="notChange"></mt-input>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {
            notChange() {
              const { phoneNumRegCN, phoneNumRegVN } = RegExpMap
              if (this.data.contact) {
                const arr = this.data.contact.split('/')
                if (arr.some((item) => !(phoneNumRegCN.test(item) || phoneNumRegVN.test(item)))) {
                  this.$toast({ content: this.$t('请输入正确的手机号'), type: 'warning' })
                  return
                }
              }
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'contact',
                value: this.data.contact
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'orderCode',
    fieldName: i18n.t('关联采购订单'),
    width: 100
  },
  {
    fieldCode: 'itemNo',
    fieldName: i18n.t('关联采购订单行号'),
    width: 120
  },
  {
    fieldCode: 'deliveryQty',
    fieldName: i18n.t('已发货数量')
  },
  {
    fieldCode: 'receiveQty',
    fieldName: i18n.t('收货数量')
  },
  {
    fieldCode: 'processorCode',
    fieldName: i18n.t('加工商编号'),
    width: 150
  },
  {
    fieldCode: 'processorName',
    fieldName: i18n.t('加工商名称'),
    width: 200
  },
  {
    fieldCode: 'companyName',
    fieldName: i18n.t('公司名称'),
    width: 250
  },
  {
    fieldCode: 'buyerRemark',
    fieldName: i18n.t('采购方备注')
  }
]
// 采购订单
export const ColumnDataPurchaseOrder = [
  {
    fieldCode: 'createMark',
    fieldName: i18n.t('是否可创建')
  },
  {
    fieldCode: 'denyReason',
    fieldName: i18n.t('无法创建原因')
  },
  {
    fieldCode: 'demandDate',
    fieldName: i18n.t('需求日期'),
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `<span>{{ getTimeFmt() }}</span>`,
          methods: {
            getTimeFmt() {
              if (this.data[this.data.column.field]) {
                const date = Number(this.data[this.data.column.field])
                return dayjs(date).format('YYYY-MM-DD')
              }
              return ''
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'itemCode',
    fieldName: i18n.t('物料编码')
  },
  {
    fieldCode: 'itemName',
    fieldName: i18n.t('物料名称')
  },
  {
    fieldCode: 'siteCode',
    fieldName: i18n.t('工厂'),
    width: 100
  },
  {
    fieldCode: 'warehouseCode',
    fieldName: i18n.t('库存地点'),
    width: 100
  },
  {
    fieldCode: 'supplierNum',
    fieldName: i18n.t('需求数量')
  },
  {
    fieldCode: 'presentDeliveryNum',
    fieldName: i18n.t('本次送货数量')
  },
  {
    fieldCode: 'remark', // 供应商备注
    fieldName: i18n.t('供应商备注'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<mt-input v-model="data.remark"  @blur="notChange"></mt-input>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {
            notChange() {
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'remark',
                value: this.data.remark
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'receiveAddress',
    fieldName: i18n.t('收货地址'),
    width: 250,
    template: () => ({ template: select })
  },
  {
    fieldCode: 'isBatch',
    fieldName: i18n.t('是否按批次'),
    template: function () {
      const template = {
        template: `<span>{{ data['isBatch'] === 'Y' ? $t('是') : $t('否') }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        methods: {}
      }
      return {
        template
      }
    }
  },
  {
    fieldCode: 'saleOrder',
    fieldName: i18n.t('销售订单号'),
    clipMode: 'Ellipsis',
    template: () => {
      return {
        template: Vue.component('actionInput1', {
          template: `<mt-input v-model="data.saleOrder"  @blur="batchCodeChange"></mt-input>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {
            batchCodeChange() {
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'saleOrder',
                value: this.data.saleOrder
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'workCenterCode',
    fieldName: i18n.t('工作中心'),
    clipMode: 'Ellipsis',
    template: () => {
      return {
        template: Vue.component('workCenterCodeSelect', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-select
                v-model="data.workCenterCode"
                :filtering="doGetDataSource"
                :data-source="workCenterOptions"
                :fields="{ text: 'theCodeName', value: 'workCenterCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                @change="workCenterCodeChange"
                :placeholder="$t('请选择')"
                :open-dispatch-change="false"
                :disabled="disabled"
              ></mt-select>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              workCenterOptions: [], // 工作中心 下拉选项
              doGetDataSource: () => {},
              disabled: false
            }
          },
          mounted() {
            this.initGetWorkCenter()
            this.doGetDataSource = utils.debounce(this.getWorkCenter, 1000)
          },
          methods: {
            // 主数据 工作中心
            getWorkCenter(args) {
              const { text, updateData, setSelectData } = args
              const params = {
                siteCode: this.data.siteCode,
                workCenterCode: text
              }
              this.$API.receiptAndDelivery.getWorkCenterBySite(params).then((res) => {
                if (res) {
                  const list = res?.data || []
                  this.workCenterOptions = addCodeNameKeyInList({
                    firstKey: 'workCenterCode',
                    secondKey: 'workCenterName',
                    list
                  })
                  if (updateData) {
                    this.$nextTick(() => {
                      updateData(this.workCenterOptions)
                    })
                  }
                  if (setSelectData) {
                    this.$nextTick(() => {
                      setSelectData()
                    })
                  }
                }
              })
            },
            // 初始化检索 工作中心
            initGetWorkCenter() {
              const workCenterCode = this.data.workCenterCode
              this.getWorkCenter({
                text: workCenterCode,
                setSelectData: () => {
                  // api获取数据后重新赋值，防止没有赋上值得情况
                  this.data.workCenterCode = workCenterCode
                }
              })
            },
            // 工作中心 change
            workCenterCodeChange(args) {
              const { itemData } = args
              if (itemData) {
                // rowDataTemp[rowDataTemp.length - 1].workCenterId = itemData.id; // id
                // rowDataTemp[rowDataTemp.length - 1].workCenterCode =
                //   itemData.workCenterCode; // code
                // rowDataTemp[rowDataTemp.length - 1].workCenterCode =
                //   itemData.workCenterName; // name
                this.$parent.$emit('cellEdit', {
                  index: Number(this.data.index),
                  key: 'workCenterCode',
                  value: itemData.workCenterCode
                })
              } else {
                this.$parent.$emit('cellEdit', {
                  index: Number(this.data.index),
                  key: 'workCenterCode',
                  value: null
                })
              }
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'consignee',
    fieldName: i18n.t('收货联系人'),
    clipMode: 'Ellipsis',
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<mt-input v-model="data.consignee"  @blur="notChange"></mt-input>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {
            notChange() {
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'consignee',
                value: this.data.consignee
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'contact',
    fieldName: i18n.t('收货联系方式'),
    clipMode: 'Ellipsis',
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<mt-input v-model="data.contact"  @blur="notChange"></mt-input>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {
            notChange() {
              const { phoneNumRegCN, phoneNumRegVN } = RegExpMap
              if (this.data.contact) {
                const arr = this.data.contact.split('/')
                if (arr.some((item) => !(phoneNumRegCN.test(item) || phoneNumRegVN.test(item)))) {
                  this.$toast({ content: this.$t('请输入正确的手机号'), type: 'warning' })
                  return
                }
              }
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'contact',
                value: this.data.contact
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'orderCode',
    fieldName: i18n.t('关联采购订单'),
    width: 100
  },
  {
    fieldCode: 'itemNo',
    fieldName: i18n.t('关联采购订单行号'),
    width: 120
  },
  {
    fieldCode: 'deliveryQty',
    fieldName: i18n.t('已发货数量')
  },
  {
    fieldCode: 'receiveQty',
    fieldName: i18n.t('收货数量')
  },
  {
    fieldCode: 'processorCode',
    fieldName: i18n.t('加工商编号'),
    width: 150
  },
  {
    fieldCode: 'processorName',
    fieldName: i18n.t('加工商名称'),
    width: 200
  },
  {
    fieldCode: 'companyName',
    fieldName: i18n.t('公司名称'),
    width: 250
  },
  {
    fieldCode: 'buyerRemark',
    fieldName: i18n.t('采购方备注')
  }
]
// JIT供货计划
export const ColumnDataJit = [
  {
    fieldCode: 'createMark',
    fieldName: i18n.t('是否可创建')
  },
  {
    fieldCode: 'denyReason',
    fieldName: i18n.t('无法创建原因')
  },
  {
    fieldCode: 'demandDate',
    fieldName: i18n.t('需求日期'),
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `<span>{{ getTimeFmt() }}</span>`,
          methods: {
            getTimeFmt() {
              if (this.data[this.data.column.field]) {
                const date = Number(this.data[this.data.column.field])
                return dayjs(date).format('YYYY-MM-DD')
              }
              return ''
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'itemCode',
    fieldName: i18n.t('物料编码')
  },
  {
    fieldCode: 'itemName',
    fieldName: i18n.t('物料名称')
  },
  {
    fieldCode: 'siteCode',
    fieldName: i18n.t('工厂'),
    width: 100
  },
  {
    fieldCode: 'warehouseCode',
    fieldName: i18n.t('库存地点'),
    width: 100
  },
  {
    fieldCode: 'supplierNum',
    fieldName: i18n.t('需求数量')
  },
  {
    fieldCode: 'presentDeliveryNum',
    fieldName: i18n.t('本次送货数量')
  },
  {
    fieldCode: 'remark', // 供应商备注
    fieldName: i18n.t('供应商备注'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<mt-input v-model="data.remark"  @blur="notChange"></mt-input>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {
            notChange() {
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'remark',
                value: this.data.remark
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'receiveAddress',
    fieldName: i18n.t('收货地址'),
    width: 250,
    template: () => ({ template: select })
  },
  {
    fieldCode: 'isBatch',
    fieldName: i18n.t('是否按批次'),
    template: function () {
      const template = {
        template: `<span>{{ data['isBatch'] === 'Y' ? $t('是') : $t('否') }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        methods: {}
      }
      return {
        template
      }
    }
  },
  {
    fieldCode: 'saleOrder',
    fieldName: i18n.t('销售订单号'),
    clipMode: 'Ellipsis',
    template: () => {
      return {
        template: Vue.component('actionInput1', {
          template: `<mt-input v-model="data.saleOrder"  @blur="batchCodeChange"></mt-input>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {
            batchCodeChange() {
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'saleOrder',
                value: this.data.saleOrder
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'workCenterCode',
    fieldName: i18n.t('工作中心'),
    clipMode: 'Ellipsis',
    template: () => {
      return {
        template: Vue.component('workCenterCodeSelect', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-select
                v-model="data.workCenterCode"
                :filtering="doGetDataSource"
                :data-source="workCenterOptions"
                :fields="{ text: 'theCodeName', value: 'workCenterCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                @change="workCenterCodeChange"
                :placeholder="$t('请选择')"
                :open-dispatch-change="false"
                :disabled="disabled"
              ></mt-select>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              workCenterOptions: [], // 工作中心 下拉选项
              doGetDataSource: () => {},
              disabled: false
            }
          },
          mounted() {
            this.initGetWorkCenter()
            this.doGetDataSource = utils.debounce(this.getWorkCenter, 1000)
          },
          methods: {
            // 主数据 工作中心
            getWorkCenter(args) {
              const { text, updateData, setSelectData } = args
              const params = {
                siteCode: this.data.siteCode,
                workCenterCode: text
              }
              this.$API.receiptAndDelivery.getWorkCenterBySite(params).then((res) => {
                if (res) {
                  const list = res?.data || []
                  this.workCenterOptions = addCodeNameKeyInList({
                    firstKey: 'workCenterCode',
                    secondKey: 'workCenterName',
                    list
                  })
                  if (updateData) {
                    this.$nextTick(() => {
                      updateData(this.workCenterOptions)
                    })
                  }
                  if (setSelectData) {
                    this.$nextTick(() => {
                      setSelectData()
                    })
                  }
                }
              })
            },
            // 初始化检索 工作中心
            initGetWorkCenter() {
              const workCenterCode = this.data.workCenterCode
              this.getWorkCenter({
                text: workCenterCode,
                setSelectData: () => {
                  // api获取数据后重新赋值，防止没有赋上值得情况
                  this.data.workCenterCode = workCenterCode
                }
              })
            },
            // 工作中心 change
            workCenterCodeChange(args) {
              const { itemData } = args
              if (itemData) {
                // rowDataTemp[rowDataTemp.length - 1].workCenterId = itemData.id; // id
                // rowDataTemp[rowDataTemp.length - 1].workCenterCode =
                //   itemData.workCenterCode; // code
                // rowDataTemp[rowDataTemp.length - 1].workCenterCode =
                //   itemData.workCenterName; // name
                this.$parent.$emit('cellEdit', {
                  index: Number(this.data.index),
                  key: 'workCenterCode',
                  value: itemData.workCenterCode
                })
              } else {
                this.$parent.$emit('cellEdit', {
                  index: Number(this.data.index),
                  key: 'workCenterCode',
                  value: null
                })
              }
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'consignee',
    fieldName: i18n.t('收货联系人'),
    clipMode: 'Ellipsis',
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<mt-input v-model="data.consignee"  @blur="notChange"></mt-input>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {
            notChange() {
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'consignee',
                value: this.data.consignee
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'contact',
    fieldName: i18n.t('收货联系方式'),
    clipMode: 'Ellipsis',
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<mt-input v-model="data.contact"  @blur="notChange"></mt-input>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {
            notChange() {
              const { phoneNumRegCN, phoneNumRegVN } = RegExpMap
              if (this.data.contact) {
                const arr = this.data.contact.split('/')
                if (arr.some((item) => !(phoneNumRegCN.test(item) || phoneNumRegVN.test(item)))) {
                  this.$toast({ content: this.$t('请输入正确的手机号'), type: 'warning' })
                  return
                }
              }
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'contact',
                value: this.data.contact
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'orderCode',
    fieldName: i18n.t('关联采购订单'),
    width: 100
  },
  {
    fieldCode: 'itemNo',
    fieldName: i18n.t('关联采购订单行号'),
    width: 120
  },
  {
    fieldCode: 'deliveryQty',
    fieldName: i18n.t('已发货数量')
  },
  {
    fieldCode: 'receiveQty',
    fieldName: i18n.t('收货数量')
  },
  {
    fieldCode: 'processorCode',
    fieldName: i18n.t('加工商编号'),
    width: 150
  },
  {
    fieldCode: 'processorName',
    fieldName: i18n.t('加工商名称'),
    width: 200
  },
  {
    fieldCode: 'companyName',
    fieldName: i18n.t('公司名称'),
    width: 250
  },
  {
    fieldCode: 'buyerRemark',
    fieldName: i18n.t('采购方备注')
  }
]
