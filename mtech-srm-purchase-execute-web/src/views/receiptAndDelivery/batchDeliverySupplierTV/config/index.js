import { i18n } from '@/main.js'
import dayjs from 'dayjs'

export const PageType = {
  deliverySchedule: 0, // 交货计划
  purchaseOrder: 1, // 采购订单
  jit: 2, // JIT
  guide: 3 // 屏发货指导
}

export const ToolBar = {
  [PageType.deliverySchedule]: [
    {
      code: 'CreateDelivery',
      name: i18n.t('创建送货单'),
      status: 'info'
    },
    {
      code: 'Rematch',
      name: i18n.t('重新匹配'),
      status: 'info'
    },
    {
      code: 'BatchUpdate',
      name: i18n.t('批量修改'),
      status: 'info'
    }
  ],
  [PageType.purchaseOrder]: [
    {
      code: 'CreateDelivery',
      name: i18n.t('创建送货单'),
      status: 'info'
    },
    {
      code: 'Rematch',
      name: i18n.t('重新匹配'),
      status: 'info'
    },
    {
      code: 'BatchUpdate',
      name: i18n.t('批量修改'),
      status: 'info'
    }
  ],
  [PageType.jit]: [
    {
      code: 'CreateDelivery',
      name: i18n.t('创建送货单'),
      status: 'info'
    },
    {
      code: 'Rematch',
      name: i18n.t('重新匹配'),
      status: 'info'
    },
    {
      code: 'BatchUpdate',
      name: i18n.t('批量修改'),
      status: 'info'
    }
  ],
  [PageType.guide]: [
    {
      code: 'CreateDelivery',
      name: i18n.t('提交创建送货单'),
      status: 'info'
    },
    {
      code: 'Rematch',
      name: i18n.t('重新匹配'),
      status: 'info'
    },
    {
      code: 'BatchUpdate',
      name: i18n.t('批量修改'),
      status: 'info'
    }
  ]
}

export const ColumnData = {
  [PageType.deliverySchedule]: [
    {
      type: 'checkbox',
      width: 50,
      ignore: true,
      fixed: 'left'
    },
    {
      type: 'seq',
      title: i18n.t('序号'),
      width: 50,
      fixed: 'left',
      showOverflow: true
    },
    {
      field: 'createMark',
      title: i18n.t('是否可创建'),
      width: 100,
      showOverflow: true,
      formatter: ({ cellValue }) => {
        return cellValue ? i18n.t('可创建') : i18n.t('不可创建')
      }
    },
    {
      field: 'denyReason',
      title: i18n.t('无法创建原因'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'demandDate',
      title: i18n.t('需求日期'),
      width: 150,
      showOverflow: true,
      formatter: ({ cellValue }) => {
        return dayjs(Number(cellValue)).format('YYYY-MM-DD')
      }
    },
    {
      field: 'itemCode',
      title: i18n.t('物料编码'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'itemName',
      title: i18n.t('物料名称'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'siteCode',
      title: i18n.t('工厂'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'warehouseCode',
      title: i18n.t('库存地点'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'supplierNum',
      title: i18n.t('需求数量'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'presentDeliveryNum',
      title: i18n.t('本次送货数量'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'remark',
      title: i18n.t('供应商备注'),
      width: 250,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'remarkDefault'
      }
    },
    {
      field: 'receiveAddress',
      title: i18n.t('收货地址'),
      width: 250,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'receiveAddressDefault'
      }
    },
    {
      field: 'isBatch',
      title: i18n.t('是否按批次'),
      width: 100,
      showOverflow: true,
      formatter: ({ cellValue }) => {
        return cellValue === 'Y' ? i18n.t('是') : i18n.t('否')
      }
    },
    {
      field: 'saleOrderNo',
      title: i18n.t('销售订单号'),
      width: 150,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'saleOrderDefault'
      }
    },
    {
      field: 'workCenterCode',
      title: i18n.t('工作中心'),
      width: 250,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'workCenterDefault'
      }
    },
    {
      field: 'sendTime',
      title: i18n.t('发货日期'),
      width: 250,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'sendTimeDefault'
      }
    },
    {
      field: 'consignee',
      title: i18n.t('收货联系人'),
      width: 150,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'consigneeDefault'
      }
    },
    {
      field: 'contact',
      title: i18n.t('收货联系方式'),
      width: 150,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'contactDefault'
      }
    },
    {
      field: 'total',
      title: i18n.t('合计'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'orderCode',
      title: i18n.t('关联采购订单'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'itemNo',
      title: i18n.t('关联采购订单行号'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'deliveryQty',
      title: i18n.t('已发货数量'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'receiveQty',
      title: i18n.t('收货数量'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'processorCode',
      title: i18n.t('加工商编号'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'processorName',
      title: i18n.t('加工商名称'),
      width: 200,
      showOverflow: true
    },
    {
      field: 'companyName',
      title: i18n.t('公司名称'),
      width: 250,
      showOverflow: true
    },
    {
      field: 'buyerRemark',
      title: i18n.t('采购方备注'),
      showOverflow: true
    }
  ],
  [PageType.purchaseOrder]: [
    {
      type: 'checkbox',
      width: 50,
      ignore: true,
      fixed: 'left'
    },
    {
      type: 'seq',
      title: i18n.t('序号'),
      width: 50,
      fixed: 'left',
      showOverflow: true
    },
    {
      field: 'createMark',
      title: i18n.t('是否可创建'),
      width: 100,
      showOverflow: true,
      formatter: ({ cellValue }) => {
        return cellValue ? i18n.t('可创建') : i18n.t('不可创建')
      }
    },
    {
      field: 'denyReason',
      title: i18n.t('无法创建原因'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'demandDate',
      title: i18n.t('需求日期'),
      width: 150,
      showOverflow: true,
      formatter: ({ cellValue }) => {
        return dayjs(Number(cellValue)).format('YYYY-MM-DD')
      }
    },
    {
      field: 'itemCode',
      title: i18n.t('物料编码'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'itemName',
      title: i18n.t('物料名称'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'siteCode',
      title: i18n.t('工厂'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'warehouseCode',
      title: i18n.t('库存地点'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'supplierNum',
      title: i18n.t('需求数量'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'presentDeliveryNum',
      title: i18n.t('本次送货数量'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'remark',
      title: i18n.t('供应商备注'),
      width: 250,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'remarkDefault'
      }
    },
    {
      field: 'receiveAddress',
      title: i18n.t('收货地址'),
      width: 250,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'receiveAddressDefault'
      }
    },
    {
      field: 'isBatch',
      title: i18n.t('是否按批次'),
      width: 100,
      showOverflow: true,
      formatter: ({ cellValue }) => {
        return cellValue === 'Y' ? i18n.t('是') : i18n.t('否')
      }
    },
    {
      field: 'saleOrderNo',
      title: i18n.t('销售订单号'),
      width: 150,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'saleOrderDefault'
      }
    },
    {
      field: 'workCenterCode',
      title: i18n.t('工作中心'),
      width: 250,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'workCenterDefault'
      }
    },
    {
      field: 'sendTime',
      title: i18n.t('发货日期'),
      width: 250,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'sendTimeDefault'
      }
    },
    {
      field: 'consignee',
      title: i18n.t('收货联系人'),
      width: 150,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'consigneeDefault'
      }
    },
    {
      field: 'contact',
      title: i18n.t('收货联系方式'),
      width: 150,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'contactDefault'
      }
    },
    {
      field: 'total',
      title: i18n.t('合计'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'orderCode',
      title: i18n.t('关联采购订单'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'itemNo',
      title: i18n.t('关联采购订单行号'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'deliveryQty',
      title: i18n.t('已发货数量'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'receiveQty',
      title: i18n.t('收货数量'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'processorCode',
      title: i18n.t('加工商编号'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'processorName',
      title: i18n.t('加工商名称'),
      width: 200,
      showOverflow: true
    },
    {
      field: 'companyName',
      title: i18n.t('公司名称'),
      width: 250,
      showOverflow: true
    },
    {
      field: 'buyerRemark',
      title: i18n.t('采购方备注'),
      showOverflow: true
    }
  ],
  [PageType.jit]: [
    {
      type: 'checkbox',
      width: 50,
      ignore: true,
      fixed: 'left'
    },
    {
      type: 'seq',
      title: i18n.t('序号'),
      width: 50,
      fixed: 'left',
      showOverflow: true
    },
    {
      field: 'createMark',
      title: i18n.t('是否可创建'),
      width: 100,
      showOverflow: true,
      formatter: ({ cellValue }) => {
        return cellValue ? i18n.t('可创建') : i18n.t('不可创建')
      }
    },
    {
      field: 'denyReason',
      title: i18n.t('无法创建原因'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'demandDate',
      title: i18n.t('需求日期'),
      width: 150,
      showOverflow: true,
      formatter: ({ cellValue }) => {
        return dayjs(Number(cellValue)).format('YYYY-MM-DD')
      }
    },
    {
      field: 'itemCode',
      title: i18n.t('物料编码'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'itemName',
      title: i18n.t('物料名称'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'siteCode',
      title: i18n.t('工厂'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'warehouseCode',
      title: i18n.t('库存地点'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'supplierNum',
      title: i18n.t('需求数量'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'presentDeliveryNum',
      title: i18n.t('本次送货数量'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'remark',
      title: i18n.t('供应商备注'),
      width: 250,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'remarkDefault'
      }
    },
    {
      field: 'receiveAddress',
      title: i18n.t('收货地址'),
      width: 250,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'receiveAddressDefault'
      }
    },
    {
      field: 'isBatch',
      title: i18n.t('是否按批次'),
      width: 100,
      showOverflow: true,
      formatter: ({ cellValue }) => {
        return cellValue === 'Y' ? i18n.t('是') : i18n.t('否')
      }
    },
    {
      field: 'saleOrderNo',
      title: i18n.t('销售订单号'),
      width: 150,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'saleOrderDefault'
      }
    },
    {
      field: 'workCenterCode',
      title: i18n.t('工作中心'),
      width: 250,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'workCenterDefault'
      }
    },
    {
      field: 'sendTime',
      title: i18n.t('发货日期'),
      width: 250,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'sendTimeDefault'
      }
    },
    {
      field: 'consignee',
      title: i18n.t('收货联系人'),
      width: 150,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'consigneeDefault'
      }
    },
    {
      field: 'contact',
      title: i18n.t('收货联系方式'),
      width: 150,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'contactDefault'
      }
    },
    {
      field: 'total',
      title: i18n.t('合计'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'orderCode',
      title: i18n.t('关联采购订单'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'itemNo',
      title: i18n.t('关联采购订单行号'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'deliveryQty',
      title: i18n.t('已发货数量'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'receiveQty',
      title: i18n.t('收货数量'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'processorCode',
      title: i18n.t('加工商编号'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'processorName',
      title: i18n.t('加工商名称'),
      width: 200,
      showOverflow: true
    },
    {
      field: 'companyName',
      title: i18n.t('公司名称'),
      width: 250,
      showOverflow: true
    },
    {
      field: 'buyerRemark',
      title: i18n.t('采购方备注'),
      showOverflow: true
    }
  ],
  [PageType.guide]: [
    {
      type: 'checkbox',
      width: 50,
      ignore: true,
      fixed: 'left'
    },
    {
      type: 'seq',
      title: i18n.t('序号'),
      width: 50,
      fixed: 'left',
      showOverflow: true
    },
    {
      field: 'createMark',
      title: i18n.t('是否可创建'),
      width: 100,
      showOverflow: true,
      formatter: ({ cellValue }) => {
        return cellValue ? i18n.t('可创建') : i18n.t('不可创建')
      }
    },
    {
      field: 'denyReason',
      title: i18n.t('无法创建原因'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'demandDate',
      title: i18n.t('需求日期'),
      width: 150,
      showOverflow: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(Number(cellValue)).format('YYYY-MM-DD') : ''
      }
    },
    {
      field: 'itemCode',
      title: i18n.t('物料编码'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'itemName',
      title: i18n.t('物料名称'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'siteCode',
      title: i18n.t('工厂'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'warehouseCode',
      title: i18n.t('库存地点'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'supplierNum',
      title: i18n.t('需求数量'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'presentDeliveryNum',
      title: i18n.t('本次送货数量'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'remark',
      title: i18n.t('供应商备注'),
      width: 250,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'remarkDefault'
      }
    },
    {
      field: 'receiveAddress',
      title: i18n.t('收货地址'),
      width: 250,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'receiveAddressDefault'
      }
    },
    {
      field: 'isBatch',
      title: i18n.t('是否按批次'),
      width: 100,
      showOverflow: true,
      formatter: ({ cellValue }) => {
        return cellValue === 'Y' ? i18n.t('是') : i18n.t('否')
      }
    },
    {
      field: 'saleOrder',
      title: i18n.t('销售订单号'),
      width: 150,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'saleOrderDefault'
      }
    },
    // {
    //   field: 'workCenterCode',
    //   title: i18n.t('工作中心'),
    //   width: 250,
    //   showOverflow: true,
    //   editRender: {},
    //   slots: {
    //     default: 'workCenterDefault'
    //   }
    // },
    {
      field: 'sendTime',
      title: i18n.t('发货日期'),
      width: 250,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'sendTimeDefault'
      }
    },
    {
      field: 'consignee',
      title: i18n.t('收货联系人'),
      width: 150,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'consigneeDefault'
      }
    },
    {
      field: 'contact',
      title: i18n.t('收货联系方式'),
      width: 150,
      showOverflow: true,
      editRender: {},
      slots: {
        default: 'contactDefault'
      }
    },
    {
      field: 'total',
      title: i18n.t('合计'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'orderCode',
      title: i18n.t('关联采购订单'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'itemNo',
      title: i18n.t('关联采购订单行号'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'deliveryQty',
      title: i18n.t('已发货数量'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'receiveQty',
      title: i18n.t('收货数量'),
      width: 100,
      showOverflow: true
    },
    {
      field: 'virtualSupplierCode',
      title: i18n.t('虚拟供应商编码'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'processorCode',
      title: i18n.t('加工商编号'),
      width: 150,
      showOverflow: true
    },
    {
      field: 'processorName',
      title: i18n.t('加工商名称'),
      width: 200,
      showOverflow: true
    },
    {
      field: 'companyName',
      title: i18n.t('公司名称'),
      width: 250,
      showOverflow: true
    },
    {
      field: 'buyerRemark',
      title: i18n.t('采购方备注'),
      showOverflow: true
    }
  ]
}

export const verifyCreateMark = (data) => {
  let valid = true
  let createMark = ''
  for (let i = 0; i < data.length; i++) {
    createMark = data[i].createMark
    if (data[i] && data[i - 1] && data[i].createMark !== data[i - 1].createMark) {
      valid = false
      break
    }
  }

  return { valid, createMark }
}
