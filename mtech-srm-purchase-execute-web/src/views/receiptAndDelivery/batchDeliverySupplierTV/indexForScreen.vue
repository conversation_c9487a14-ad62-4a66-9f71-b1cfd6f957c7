<!-- 供方-供货计划TV-批量创建送货单 -->
<template>
  <div class="full-height vertical-flex-box">
    <div class="top-info flex-keep">
      <div class="header-box">
        <div class="middle-blank"></div>
        <!-- 右侧操作按钮 -->
        <mt-button css-class="e-flat" :is-primary="true" @click="goBack">
          {{ $t('返回') }}
        </mt-button>
      </div>
      <div class="title">
        <span>{{ $t('批量创建送货单') }}</span>
      </div>
    </div>
    <div class="flex-fit">
      <ScTable
        ref="xTable"
        :columns="columns"
        :table-data="tableData"
        height="auto"
        header-align="left"
        align="left"
        show-overflow
        :is-show-right-btn="false"
        :row-config="{ height: 36 }"
        :seq-config="{
          seqMethod({ rowIndex }) {
            return rowIndex + 1
          }
        }"
        :checkbox-config="{
          checkField: 'checked'
        }"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 20, oSize: 10 }"
        style="padding-top: unset"
        @checkbox-change="checkboxChange"
        @checkbox-all="checkboxAll"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #remarkDefault="{ row }">
          <vxe-input v-model="row.remark" :placeholder="$t('请输入')" clearable />
        </template>
        <template #receiveAddressDefault="{ row }">
          <!-- <div @click="clickReceiveAddress(row)"> -->
          <!-- <div>
            <vxe-select
              v-model="row.receiveAddress"
              :options="newReceiveAddressOptions"
              :placeholder="$t('请选择')"
              clearable
              filterable
              transfer
              @change="(e) => receiveAddressChange(e, row)"
            />
          </div> -->
          <div @click="clickReceiveAddress(row)">
            <vxe-select
              v-model="row.receiveAddress"
              :options="row.receiveAddressOptions"
              :placeholder="$t('请选择')"
              clearable
              filterable
              transfer
              @change="(e) => receiveAddressChange(e, row)"
            />
          </div>
        </template>
        <template #saleOrderDefault="{ row }">
          <vxe-input
            v-model="row.saleOrderNo"
            :placeholder="$t('请输入')"
            :disabled="true"
            clearable
            @blur="saleOrderBlur"
          />
        </template>
        <template #workCenterDefault="{ row }">
          <div @click="clickWorkCenter(row)">
            <vxe-select
              v-model="row.workCenterCode"
              :options="row.workCenterOptions"
              :placeholder="$t('请选择')"
              :disabled="row.workCenterDisable"
              clearable
              filterable
              transfer
            />
          </div>
        </template>
        <template #sendTimeDefault="{ row }">
          <mt-date-picker
            v-model="row.sendTime"
            :min="new Date()"
            :open-on-focus="true"
            :allow-edit="false"
            :show-clear-button="false"
            :placeholder="$t('请选择')"
          />
        </template>
        <template #consigneeDefault="{ row }">
          <vxe-input v-model="row.consignee" :placeholder="$t('请输入')" clearable />
        </template>
        <template #contactDefault="{ row }">
          <vxe-input
            v-model="row.contact"
            :placeholder="$t('请输入')"
            clearable
            @blur="contactChange"
          />
        </template>
      </ScTable>
    </div>
    <BatchUpdateDialog ref="batchUpdateDialogRef" @confirm="batchUpdateConfirm" />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { PageType, ColumnData, ToolBar, verifyCreateMark } from './config'
import { RegExpMap } from '@/utils/constant'

export default {
  components: {
    ScTable,
    BatchUpdateDialog: () => import('./components/BatchUpdateScreenDialog')
  },
  data() {
    const { type: pageType } = this.$route.query
    return {
      pageType,

      columns: ColumnData[pageType],
      tableData: [],
      toolbar: ToolBar[pageType],

      receiveAddressObj: {},
      workCenterObj: {},

      selectedRecordsIndexList: [],
      newReceiveAddressOptions: []
    }
  },
  computed: {
    currentParams() {
      return this.$store.state.preCreateParams
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    initOptions() {
      const arr = this.tableData
      const siteCodeArr = this.uniqueArray(arr, 'siteCode')
      siteCodeArr.forEach((item) => {
        this.getReceiveAddressOptions(item.siteCode, item.warehouseCode)
        let flag = false
        if (['T9', 'T91', 'T92'].includes(item.warehouseCode)) {
          flag = true
        }
        this.getWorkCenterOptions(item.siteCode, flag)
      })
    },
    getReceiveAddressOptions(siteCode, warehouseCode) {
      let params = {
        siteCode,
        siteAddress: warehouseCode
      }
      this.$API.deliverySchedule.siteAndAddressForScreen(params).then((res) => {
        if (res.code === 200) {
          const list = this.uniqueArray(res.data, 'receiveAddrDesc')
          const options = list.map((item) => {
            return {
              label: item.receiveAddrDesc,
              value: item.receiveAddrDesc,
              addressConfigId: item.id,
              isDirectDeliver: item.isDirectDeliver || null
            }
          })
          this.receiveAddressObj[siteCode] = options
        }
      })
    },
    uniqueArray(arr, field) {
      let map = new Map()
      for (let item of arr) {
        if (!map.has(item[field])) {
          map.set(item[field], item)
        }
      }
      return [...map.values()]
    },
    clickReceiveAddress(row) {
      if (row.receiveAddressOptions.length === 0) {
        this.setReceiveAddressOptions(row)
      }
    },
    setReceiveAddressOptions(row) {
      this.$set(row, 'receiveAddressOptions', this.receiveAddressObj[row.siteCode])
    },
    receiveAddressChange(e, row) {
      const selectedData = row.receiveAddressOptions.filter((item) => item.value === e.value)
      row.addressConfigId = selectedData[0]?.addressConfigId
      row.isDirectDeliver = selectedData[0]?.isDirectDeliver
    },
    saleOrderBlur(e) {
      if (e.value.length > 10) {
        this.$toast({ content: this.$t('销售订单号不能超过10位！'), type: 'warning' })
        return
      }
    },
    getWorkCenterOptions(siteCode, flag = false) {
      let params = {
        siteCodes: flag ? ['1515', '1517', '1521', '1523', siteCode] : [siteCode]
      }
      this.$API.receiptAndDelivery.getWorkCenterBySiteCodesApi(params).then((res) => {
        if (res.code === 200) {
          const options = res.data.map((item) => {
            return {
              label: item.workCenterCode + '-' + item.workCenterName,
              value: item.workCenterCode
            }
          })
          this.workCenterObj[flag ? `${siteCode}T` : siteCode] = options
        }
      })
    },
    clickWorkCenter(row) {
      if (row.workCenterOptions.length === 0) {
        this.setWorkCenterOptions(row)
      }
    },
    setWorkCenterOptions(row) {
      let key = ['T9', 'T91', 'T92'].includes(row.warehouseCode) ? `${row.siteCode}T` : row.siteCode
      this.$set(row, 'workCenterOptions', this.workCenterObj[key])
    },
    contactChange(e) {
      const { value } = e
      const { phoneNumRegCN, phoneNumRegVN } = RegExpMap
      if (value) {
        const arr = value.split('/')
        if (arr.some((item) => !(phoneNumRegCN.test(item) || phoneNumRegVN.test(item)))) {
          this.$toast({ content: this.$t('请输入正确的手机号'), type: 'warning' })
          return
        }
      }
    },
    getTableData() {
      let params = this.currentParams
      const api =
        this.pageType === 0
          ? this.$API.receiptAndDelivery.postSupplierDeliverySupplyPlanPlanPreCreateTv
          : this.pageType === 1
          ? this.$API.receiptAndDelivery.postSupplierDeliverySupplyPlanOrderPreCreateTv
          : this.pageType === 2
          ? this.$API.receiptAndDelivery.postSupplierDeliverySupplyPlanJitPreCreateTv
          : this.$API.receiptAndDelivery.postSupDeliveryPreCreate
      this.apiStartLoading()
      api(params)
        .then((res) => {
          this.apiEndLoading()
          if (res.code === 200) {
            this.tableData = res.data.map((i) => {
              const item = {
                ...i,
                receiveAddressOptions: [],
                workCenterOptions: [],
                saleOrderNo: i.saleOrderNo || null,
                saleOrderDisable: i.saleOrderNo ? true : false,
                workCenterDisable: i.workCenterCode ? true : false,
                checked: false,
                // sendTime: i.sendTime || new Date()
                sendTime: i.sendTime || null
              }
              // this.$set(item, 'receiveAddressOptions', [])
              // this.$set(item, 'workCenterOptions', [])
              // this.$set(item, 'saleOrderNo', item.saleOrderNo || null)
              // this.$set(item, 'saleOrderDisable', item.saleOrderNo ? true : false)
              // this.$set(item, 'workCenterDisable', item.workCenterCode ? true : false)
              // this.$set(item, 'checked', false)
              // this.$set(item, 'sendTime', item.sendTime || new Date())
              return item
            })
            if (this.tableData.length > 0) {
              this.initOptions()
            }
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    checkboxChange(args) {
      const { rowIndex } = args
      if (this.selectedRecordsIndexList.includes(rowIndex)) {
        this.selectedRecordsIndexList = this.selectedRecordsIndexList.filter(
          (itemIndex) => itemIndex !== rowIndex
        )
      } else {
        this.selectedRecordsIndexList.push(rowIndex)
      }
    },
    checkboxAll(args) {
      const { checked } = args
      this.selectedRecordsIndexList = []
      if (checked) {
        this.tableData.forEach((item, i) => {
          this.selectedRecordsIndexList.push(i)
        })
      }
    },
    goBack() {
      this.$router.push({
        name: 'supply-plan-supplier-tv'
      })
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      const selectedRecordsIndexList = this.selectedRecordsIndexList.sort((a, b) => a - b)
      const commonToolbar = ['CreateDelivery', 'BatchUpdate']
      if (selectedRecords.length === 0 && commonToolbar.includes(code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (code === 'CreateDelivery') {
        const { valid, createMark } = verifyCreateMark(selectedRecords)
        if (valid && createMark) {
          this.handleCreateDelivery({ selectedRecords, selectedRecordsIndexList })
        } else {
          this.$toast({
            content: this.$t('请选择可创建送货单的数据'),
            type: 'warning'
          })
        }
      }
      if (code === 'Rematch') {
        this.handleRematch()
      }
      if (code === 'BatchUpdate') {
        this.handleBatchUpdate({ selectedRecords })
        selectedRecords.forEach((e) => {
          this.tableData.forEach((item) => {
            if (e.id === item.id) {
              this.$set(item, 'receiveAddressOptions', this.receiveAddressObj[e.siteCode])
              let key = ['T9', 'T91', 'T92'].includes(e.warehouseCode)
                ? `${e.siteCode}T`
                : e.siteCode
              this.$set(item, 'workCenterOptions', this.workCenterObj[key])
            }
          })
        })
      }
    },
    handleCreateDelivery(args) {
      const { selectedRecords, selectedRecordsIndexList } = args
      const params = []
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        if (!item.addressConfigId) {
          this.$toast({
            content: this.$t(`第${selectedRecordsIndexList[i] + 1}行收货地址不能为空！`),
            type: 'warning'
          })
          return
        }
        if (!item.sendTime) {
          this.$toast({
            content: this.$t(
              `第${
                selectedRecordsIndexList[i] + 1
              }行发货日期不能为空！请通过勾选批量修改进行编辑调整`
            ),
            type: 'warning'
          })
          return
        }
        // 屏发货指导取消销售订单号和工作中心校验
        // if (item.isDirectDeliver === 1) {
        //   if (!item.saleOrderNo || !item.workCenterCode) {
        //     this.$toast({
        //       content: this.$t(
        //         `第${
        //           selectedRecordsIndexList[i] + 1
        //         }行该送货地址属直送，销售订单号和工作中心不能为空！`
        //       ),
        //       type: 'warning'
        //     })
        //     return
        //   }
        // }
        // if (!item.saleOrderDisable && item.saleOrderNo?.length > 10) {
        //   this.$toast({
        //     content: this.$t(`第${selectedRecordsIndexList[i] + 1}行销售订单号不能超过10位！`),
        //     type: 'warning'
        //   })
        //   return
        // }
        params.push({
          id: item.id,
          supplierNum: item.presentDeliveryNum,
          supplierTenantId: item.supplierTenantId,
          requiredDeliveryDate: item?.requiredDeliveryDate,
          remark: item.remark,
          addressConfigId: item.addressConfigId,
          saleOrderNo: item.saleOrderNo || null,
          workCenterCode: item.workCenterCode,
          workCenterName: item.workCenterName || null,
          sendTime: item.sendTime ? Date.parse(item.sendTime) : null,
          receiverContactName: item.consignee,
          receiverContact: item.contact,
          headerRemark: item.headerRemark || null
        })
      }
      if (this.pageType === PageType['deliverySchedule']) {
        // 交货计划-创建送货单
        this.postSupplierDeliverySupplyPlanPlanCreate({
          params,
          selectedRecordsIndexList
        })
      }
      if (this.pageType === PageType['purchaseOrder']) {
        // 采购订单-创建送货单
        this.postSupplierDeliverySupplyPlanOrderCreate({
          params,
          selectedRecordsIndexList
        })
      }
      if (this.pageType === PageType['jit']) {
        // JIT-创建送货单
        const { phoneNumRegCN, phoneNumRegVN } = RegExpMap

        if (params.some((item) => !item.receiverContactName)) {
          this.$toast({ content: this.$t('请输入收货联系人'), type: 'warning' })
          return
        }

        if (
          params.some((item) => {
            const arr = item.receiverContact.split('/')
            return arr.some((item) => !(phoneNumRegCN.test(item) || phoneNumRegVN.test(item)))
          })
        ) {
          this.$toast({ content: this.$t('请输入正确的手机号'), type: 'warning' })
          return
        }
        this.postSupplierDeliverySupplyPlanJitCreate({
          params,
          selectedRecordsIndexList
        })
      }
      if (this.pageType === PageType['guide']) {
        // 屏发货指导-创建送货单
        this.postSupplierDeliverySupplyPlanGuideCreate({
          params,
          selectedRecordsIndexList
        })
      }
    },
    postSupplierDeliverySupplyPlanPlanCreate(args) {
      const { params, selectedRecordsIndexList } = args
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanPlanCreateTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res.code == 200) {
            this.handleCreateDeliveryAfter({
              response: res,
              selectedRecordsIndexList
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    postSupplierDeliverySupplyPlanOrderCreate(args) {
      const { params, selectedRecordsIndexList } = args
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanOrderCreateTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res.code == 200) {
            this.handleCreateDeliveryAfter({
              response: res,
              selectedRecordsIndexList
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    postSupplierDeliverySupplyPlanJitCreate(args) {
      const { params, selectedRecordsIndexList } = args
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyJitPlanCreateTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.handleCreateDeliveryAfter({
              response: res,
              selectedRecordsIndexList
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    postSupplierDeliverySupplyPlanGuideCreate(args) {
      const { params, selectedRecordsIndexList } = args
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanGuideCreateTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.handleCreateDeliveryAfter({
              response: res,
              selectedRecordsIndexList
            })
          }
        })
    },
    handleCreateDeliveryAfter(args) {
      const { response, selectedRecordsIndexList } = args
      this.$toast({
        content: response.data,
        type: 'success'
      })
      this.updateCurrentTableData({ selectedRecordsIndexList })
      // setTimeout(() => {
      //   this.$router.push({
      //     path: '/purchase-execute/deliver-list-supplier-tv',
      //     query: {
      //       type: 0
      //     }
      //   })
      // }, 200)
    },
    updateCurrentTableData(args) {
      const { selectedRecordsIndexList } = args
      selectedRecordsIndexList
        .sort((a, b) => b - a)
        .forEach((itemIndex) => {
          this.tableData.splice(itemIndex, 1)
        })
      this.selectedRecordsIndexList = []
      if (this.tableData.length === 0) {
        this.goBack()
      }
    },
    handleRematch() {
      this.getTableData()
    },
    handleBatchUpdate(args) {
      const { selectedRecords } = args
      this.$refs.batchUpdateDialogRef.dialogInit({
        title: this.$t('批量修改'),
        selectedRecords
      })
    },
    batchUpdateConfirm(form) {
      const arr = this.selectedRecordsIndexList.sort((a, b) => a - b)
      for (let i = 0; i < arr.length; i++) {
        const itemIndex = arr[i]
        const item = this.tableData[itemIndex]
        if (
          form.receiveAddress &&
          !item.receiveAddressOptions.some((e) => e.value === form.receiveAddress)
        ) {
          this.$toast({
            content: this.$t(
              `第${arr[i] + 1}行，工厂${item['siteCode']}没有配置收货地址${
                form.receiveAddress
              }，请重新选择！`
            ),
            type: 'warning'
          })
          return
        }
        if (
          form.workCenterCode &&
          !item.workCenterOptions.some((e) => e.value === form.workCenterCode)
        ) {
          this.$toast({
            content: this.$t(
              `第${arr[i] + 1}行，工厂${item['siteCode']}没有配置工作中心${
                form.workCenterCode
              }，请重新选择！`
            ),
            type: 'warning'
          })
          return
        }
      }
      this.selectedRecordsIndexList.forEach((itemIndex) => {
        this.tableData[itemIndex].receiveAddress =
          form.receiveAddress || this.tableData[itemIndex].receiveAddress
        this.tableData[itemIndex].addressConfigId =
          form.addressConfigId || this.tableData[itemIndex].addressConfigId
        this.tableData[itemIndex].isDirectDeliver =
          form.isDirectDeliver || this.tableData[itemIndex].isDirectDeliver
        if (!this.tableData[itemIndex].saleOrderDisable) {
          this.tableData[itemIndex].saleOrderNo =
            form.saleOrderNo || this.tableData[itemIndex].saleOrderNo
        }
        if (!this.tableData[itemIndex].workCenterDisable) {
          this.tableData[itemIndex].workCenterCode =
            form.workCenterCode || this.tableData[itemIndex].workCenterCode
          this.tableData[itemIndex].workCenterName =
            form.workCenterName || this.tableData[itemIndex].workCenterName
        }
        this.tableData[itemIndex].sendTime = form.sendTime || this.tableData[itemIndex].sendTime
        this.tableData[itemIndex].consignee = form.consignee || this.tableData[itemIndex].consignee
        this.tableData[itemIndex].contact = form.contact || this.tableData[itemIndex].contact
        this.tableData[itemIndex].headerRemark =
          form.headerRemark || this.tableData[itemIndex].headerRemark
      })
    },
    apiStartLoading() {
      this.$store.commit('startLoading')
    },
    apiEndLoading() {
      this.$store.commit('endLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  // padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  .header-box {
    border-bottom: 1px solid #e6e9ed;
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
  }
  .title {
    margin: 24px 0px 8px 24px;
    padding-left: 8px;
    border-left: 4px solid #3369ac;
    font-weight: 600;
  }
}
</style>

<style>
.delivery-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.delivery-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.delivery-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
