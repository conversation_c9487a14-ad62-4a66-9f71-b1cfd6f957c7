<!-- 批量修改弹框 -->
<template>
  <mt-dialog
    ref="dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    width="650px"
    height="400px"
  >
    <div style="padding-top: 1rem">
      <mt-form ref="modelForm" :model="modelForm" :rules="rules">
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item prop="receiveAddress" :label="$t('收货地址')">
              <mt-select
                v-model="modelForm.receiveAddress"
                :data-source="receiveAddressOptions"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                @change="receiveAddressChange"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="sendTime" :label="$t('发货日期')">
              <mt-date-picker
                v-model="modelForm.sendTime"
                :min="new Date()"
                :open-on-focus="true"
                :allow-edit="false"
                :placeholder="$t('请选择')"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <!-- <mt-col :span="12">
            <mt-form-item prop="saleOrderNo" :label="$t('销售订单号')">
              <mt-input v-model="modelForm.saleOrderNo" show-clear-button />
            </mt-form-item>
          </mt-col> -->
          <!-- <mt-col :span="12">
            <mt-form-item prop="workCenterCode" :label="$t('工作中心')">
              <mt-select
                v-model="modelForm.workCenterCode"
                :data-source="workCenterOptions"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                @change="workCenterChange"
              />
            </mt-form-item>
          </mt-col> -->
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item prop="consignee" :label="$t('收货联系人')">
              <mt-input v-model="modelForm.consignee" show-clear-button />
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="contact" :label="$t('收货联系方式')">
              <mt-input v-model="modelForm.contact" show-clear-button />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="24">
            <mt-form-item prop="headerRemark" :label="$t('送货单头备注')">
              <mt-input v-model="modelForm.headerRemark" show-clear-button />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { RegExpMap } from '@/utils/constant'

export default {
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      modelForm: {},
      rules: {
        sendTime: [{ required: true, message: this.$t('请选择发货日期'), trigger: 'blur' }]
      },

      receiveAddressOptions: [],
      workCenterOptions: []
    }
  },
  methods: {
    dialogInit(args) {
      const { title, selectedRecords } = args
      const siteCodes = []
      let flag = false
      selectedRecords.forEach((item) => {
        if (!siteCodes.includes(item.siteCode)) {
          siteCodes.push(item.siteCode)
          this.getAddressOptions(item.siteCode, item.warehouseCode)
        }
        flag = ['T9', 'T91', 'T92'].includes(item.warehouseCode)
      })
      // this.getAddressOptions(siteCodes)
      this.getWorkCenterOptions(siteCodes, flag)
      this.dialogTitle = title
      this.$refs.dialog.ejsRef.show()
    },
    getAddressOptions(siteCode, warehouseCode) {
      // let params = {
      //   siteCode,
      //   page: {
      //     current: 1,
      //     size: 9999
      //   }
      // }
      // this.$API.deliverySchedule.queryScreenAddr(params).then((res) => {
      //   if (res.code == 200) {
      //     const receiveAddressOptions = []
      //     res.data?.screenWarehouseAddrDtoPage?.records?.forEach((item) => {
      //       receiveAddressOptions.push({
      //         text: item.receiveAddrDesc,
      //         value: item.receiveAddrDesc,
      //         addressConfigId: item.id,
      //         isDirectDeliver: item.isDirectDeliver || null
      //       })
      //     })
      //     this.receiveAddressOptions = this.uniqueArray(receiveAddressOptions, 'value')
      //   }
      // })
      let params = {
        siteCode,
        siteAddress: warehouseCode
      }
      this.$API.deliverySchedule.siteAndAddressForScreen(params).then((res) => {
        if (res.code == 200) {
          const receiveAddressOptions = [...this.receiveAddressOptions]
          res.data.forEach((item) => {
            receiveAddressOptions.push({
              text: item.receiveAddrDesc,
              value: item.receiveAddrDesc,
              addressConfigId: item.id,
              isDirectDeliver: item.isDirectDeliver || null
            })
          })
          this.receiveAddressOptions = this.uniqueArray(receiveAddressOptions, 'value')
        }
      })
    },
    uniqueArray(arr, field) {
      let map = new Map()
      for (let item of arr) {
        if (!map.has(item[field])) {
          map.set(item[field], item)
        }
      }
      return [...map.values()]
    },
    // getAddressOptions(siteCodes) {
    //   let params = { siteCodes }
    //   this.$API.receiptAndDelivery.getAddressBySiteCodesApi(params).then((res) => {
    //     if (res.code == 200) {
    //       this.receiveAddressOptions = res.data.map((item) => {
    //         return {
    //           text: item.consigneeAddress,
    //           value: item.consigneeAddress,
    //           addressConfigId: item.id,
    //           isDirectDeliver: item.isDirectDeliver
    //         }
    //       })
    //     }
    //   })
    // },
    receiveAddressChange(e) {
      const { itemData } = e
      this.modelForm.addressConfigId = itemData?.addressConfigId || null
      this.modelForm.isDirectDeliver = itemData?.isDirectDeliver || null
    },
    getWorkCenterOptions(siteCodes, flag = false) {
      let params = {
        siteCodes: flag ? ['1515', '1517', '1521', '1523', ...siteCodes] : siteCodes
      }
      this.$API.receiptAndDelivery.getWorkCenterBySiteCodesApi(params).then((res) => {
        if (res.code == 200) {
          this.workCenterOptions = res.data.map((item) => {
            return {
              text: item.workCenterCode + '-' + item.workCenterName,
              value: item.workCenterCode,
              workCenterName: item.workCenterName
            }
          })
        }
      })
    },
    workCenterChange(e) {
      const { itemData } = e
      this.modelForm.workCenterName = itemData?.workCenterName || null
    },
    onOpen(args) {
      args.preventFocus = true
    },
    confirm() {
      if (this.modelForm.saleOrderNo && this.modelForm.saleOrderNo.length > 10) {
        this.$toast({
          content: this.$t('销售订单号不能超过10位！'),
          type: 'warning'
        })
        return
      }
      if (this.modelForm.contact) {
        const { phoneNumRegCN, phoneNumRegVN, phoneNumRegMX } = RegExpMap
        const arr = this.modelForm.contact.split('/')
        if (
          arr.some(
            (item) =>
              !(phoneNumRegCN.test(item) || phoneNumRegVN.test(item) || phoneNumRegMX.test(item))
          )
        ) {
          this.$toast({ content: this.$t('请输入正确的手机号'), type: 'warning' })
          return
        }
      }
      this.$emit('confirm', this.modelForm)
      this.handleClose()
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>
