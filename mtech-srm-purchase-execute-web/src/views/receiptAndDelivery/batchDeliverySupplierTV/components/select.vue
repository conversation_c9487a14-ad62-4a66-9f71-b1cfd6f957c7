<template>
  <div class="grid-edit-column mt-flex-direction-column">
    <div class="field-content">
      <mt-select
        v-model="data[data.column.field]"
        :id="data.column.field"
        :data-source="warehouseOptions"
        :fields="fields"
        :show-clear-button="true"
        :allow-filtering="true"
        @change="warehouseCodeChange"
        filter-type="Contains"
        :placeholder="$t('请选择')"
        :disabled="disabled"
      ></mt-select>
    </div>
  </div>
</template>
<script>
export default {
  data: function () {
    return {
      data: {},
      warehouseOptions: [], // 库存地点 下拉选项
      disabled: false
    }
  },
  computed: {
    pageType() {
      return this.$route.query.type
    },
    fields() {
      return this.pageType === 3
        ? { text: 'receiveAddrDesc', value: 'receiveAddrDesc' }
        : { text: 'consigneeAddress', value: 'consigneeAddress' }
    }
  },
  mounted() {
    this.initGetWarehouse()
  },
  methods: {
    // 主数据 获取库存地点
    getWarehouse(args) {
      const { updateData, setSelectData } = args
      let currentSiteCode = this.data.siteCode
      const params = {
        siteCode: currentSiteCode || undefined // 工厂code
        // siteAddress: text
      }
      // 屏发货指导有其特定的请求地址
      const method =
        this.pageType === 3
          ? this.$API.deliverySchedule.siteAndAddressForScreen
          : this.$API.deliverySchedule.queryBySiteAndAddress
      method(params).then((res) => {
        if (res) {
          const list = res?.data || []
          this.warehouseOptions = list.filter(
            (item) => !!item.consigneeAddress || !!item.receiveAddrDesc
          )
          if (updateData) {
            this.$nextTick(() => {
              updateData(this.warehouseOptions)
            })
          }
          if (setSelectData) {
            this.$nextTick(() => {
              setSelectData()
            })
          }
        }
      })
    },
    // 初始化检索 根据 工厂 获取 库存地点
    initGetWarehouse() {
      const warehouseName = this.data[this.data.column.field] ?? ''
      this.getWarehouse({
        text: this.data['warehouseCode'],
        setSelectData: () => {
          // api获取数据后重新赋值，防止没有赋上值得情况
          this.data[this.data.column.field] = warehouseName
        }
      })
    },
    // 库存地点 change
    warehouseCodeChange(args) {
      const { itemData } = args
      this.data.addressConfigId = itemData?.id || ''
      this.$parent.$emit('cellEdit', {
        index: Number(this.data.index),
        key: 'receiveAddress',
        value: {
          addressConfigId: itemData.id,
          receiveAddress:
            this.pageType === 3 ? itemData.receiveAddrDesc : itemData.consigneeAddress,
          isDirectDeliver: itemData.isDirectDeliver
        }
      })
      // if (this.data.column.field === "warehouseCode") {
      //   this.data.warehouse = itemData.siteAddressName;
      // } else if (this.data.column.field === "receiveAddress") {
      //   this.data.receiveAddress = itemData.siteAddressName;
      // }
    }
  }
}
</script>
