import { ColumnComponent as Component } from './columnComponent'
import {
  StatusOptions,
  IntoStatusOptions,
  DeliveryStatusOptions,
  CellTools,
  ComponentType,
  SyncStatusOptions
} from './constant'
import { MasterDataSelect } from '@/utils/constant'
import { codeNameColumn } from '@/utils/utils'

// 格式化表格动态数据
export const formatTableColumnData = (args) => {
  const { data } = args
  const colData = []
  // 送货单列表
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: col.width ? col.width : '150'
    }
    if (col.fieldCode === 'forecastCode') {
      // 预约号
      defaultCol.cssClass = '' // 不可点击
      defaultCol.cellTools = CellTools // 表格行按钮
    } else if (col.fieldCode === 'deliveryCode') {
      // 关联ASN
      defaultCol.width = '300'
      defaultCol.template = Component.listInfoSearch({
        dataKey: col.fieldCode,
        isShowLength: false,
        delimiter: '； ',
        type: ComponentType.view
      })
    } else if (col.fieldCode === 'forecastTime') {
      // 预约时间
      defaultCol.width = '190'
    } else if (col.fieldCode === 'subSiteCode') {
      // 分厂 code+name
      defaultCol.template = codeNameColumn({
        firstKey: 'subSiteCode',
        secondKey: 'subSiteName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
    } else if (col.fieldCode === 'companyCode') {
      // 公司名称 code+name
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'companyCode',
        secondKey: 'companyName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        // ...MasterDataSelect.businessCompany,
        ...MasterDataSelect.businessCompanyPermission
      }
    } else if (col.fieldCode === 'supplierCode') {
      // 供应商名称 code+name
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.supplier
      }
    } else if (col.fieldCode === 'buyerOrgCode') {
      // 采购组名称 code+name
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'buyerOrgCode',
        secondKey: 'buyerOrgName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessGroup
      }
    } else if (col.fieldCode === 'status') {
      // 状态
      defaultCol.valueConverter = {
        type: 'map',
        map: StatusOptions
      }
    } else if (defaultCol.field === 'syncStatus') {
      // 同步状态 状态:0-未同步，1-同步失败，2同步成功
      defaultCol.valueConverter = {
        type: 'map',
        map: SyncStatusOptions
      }
    } else if (col.fieldCode === 'intoStatus') {
      // 入园状态
      defaultCol.valueConverter = {
        type: 'map',
        map: IntoStatusOptions
      }
    } else if (col.fieldCode === 'deliveryStatus') {
      // 交货方式
      defaultCol.valueConverter = {
        type: 'map',
        map: DeliveryStatusOptions
      }
    } else if (col.fieldCode === 'accompanyInfo') {
      // 随车信息
      defaultCol.width = '170'
      defaultCol.template = Component.listInfoSearch({
        dataKey: 'accompanyList',
        isShowLength: false,
        infoKey: 'name',
        delimiter: '； ',
        isShowBtn: false,
        type: ComponentType.view
      })
    } else if (col.fieldCode === 'accompanyNum') {
      // 随车人数
      defaultCol.template = Component.listInfoSearch({
        dataKey: 'accompanyList',
        isShowLength: true,
        type: ComponentType.view
      })
    }
    colData.push(defaultCol)
  })

  return colData
}

// 验证 状态是否统一
export const verifyStatus = (data) => {
  let valid = true
  let status = ''
  for (let i = 0; i < data.length; i++) {
    status = data[i].status
    if (data[i] && data[i - 1] && data[i].status !== data[i - 1].status) {
      valid = false
      break
    }
  }

  return { valid, status }
}
