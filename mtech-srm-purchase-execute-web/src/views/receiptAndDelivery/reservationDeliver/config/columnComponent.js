import Vue from 'vue'
import { ComponentType } from './constant'

// 时间日期显示
export const ColumnComponent = {
  // list 信息 显示 点击出弹框编辑
  listInfoSearch: (args) => {
    const { dataKey, isShowLength, infoKey, delimiter, type, isShowBtn } = args

    const template = () => {
      return {
        template: Vue.component('date', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <div class="input-search-content">
                <span v-if="isShowLength">{{data[dataKey] && data[dataKey].length || '-' }}</span>
                <div v-else :class="[isShowBtn && type === ComponentType.edit && 'text-ellipsis']"><span v-for="(item, index) in data[dataKey]" :key="index">{{infoKey ? item[infoKey] : item}}{{data[dataKey].length != index + 1 ? delimiter : ''}}</span></div>
              </div>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              isShowLength,
              infoKey,
              delimiter,
              isShowBtn,
              type,
              ComponentType
            }
          },
          mounted() {},
          beforeDestroy() {},
          filters: {},
          methods: {}
        })
      }
    }

    return template
  }
}
