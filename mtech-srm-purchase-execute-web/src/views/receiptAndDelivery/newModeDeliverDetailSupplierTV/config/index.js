import { i18n } from '@/main.js'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import Vue from 'vue'
export const timeDate = (dataKey, hasTime) => {
  const template = () => {
    return {
      template: Vue.component('date', {
        template: `<div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div><div>{{data[dataKey] | dateFormat}}</div></div>`,
        data: function () {
          return { data: {}, dataKey, hasTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
export const columnData = [
  {
    width: '100',
    field: 'deliveryLineNo',
    headerText: i18n.t('行号'),
    cssClass: ''
  },
  {
    width: '120',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      // type: "map",
      // map: [
      //   { value: 1, text: i18n.t("新建"), cssClass: "col-active" },
      //   { value: 2, text: i18n.t("发货中"), cssClass: "col-active" },
      //   { value: 3, text: i18n.t("已完成"), cssClass: "col-active" },
      //   { value: 4, text: i18n.t("已取消"), cssClass: "col-active" },
      //   { value: 5, text: i18n.t("已关闭"), cssClass: "col-active" },
      //   { value: 6, text: i18n.t("部分收货"), cssClass: "col-active" },
      // ],
      type: 'function',
      filter: (e) => {
        return e?.label ?? ''
      }
    }
  },
  {
    width: '150',
    field: 'jit',
    headerText: i18n.t('是否JIT'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },
  {
    width: '150',
    field: 'jitDeliveryNumber',
    headerText: i18n.t('JIT编号'),
    searchOptions: {
      renameField: 'deliveryNumber'
    }
  },
  {
    field: 'inputDate', //
    headerText: i18n.t('凭证创建日期'),
    template: timeDate('inputDate', true)
  },

  {
    field: 'postingDate', //
    headerText: i18n.t('过账日期'),
    template: timeDate('postingDate', false)
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('采购订单号')
  },
  {
    width: '150',
    field: 'lineNo',
    headerText: i18n.t('采购订单行号')
  },
  {
    width: '150',
    field: 'demandDate',
    headerText: i18n.t('需求日期'),
    template: timeDate('demandDate', false)
  },
  {
    width: '150',
    field: 'demandTime',
    headerText: i18n.t('需求时间')
  },
  {
    width: '150',
    field: 'takeNo',
    headerText: i18n.t('车牌号')
  },
  {
    width: '150',
    field: 'deliveryQuantity',
    headerText: i18n.t('本次送货数量')
  },
  {
    width: '150',
    field: 'receiveQuantity',
    headerText: i18n.t('收货数量')
  },
  {
    width: '150',
    field: 'rejectQuantity',
    headerText: i18n.t('拒绝数量')
  },
  {
    width: '150',
    field: 'rejectReason',
    headerText: i18n.t('拒绝原因')
  },
  {
    field: 'transferPlanName',
    headerText: i18n.t('计划员')
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    width: '150',
    field: 'sendTime',
    headerText: i18n.t('发货日期'),
    template: timeDate('sendTime', true)
  },
  {
    width: '150',
    field: 'forecastArriveTime',
    headerText: i18n.t('预计到货日期'),
    template: timeDate('forecastArriveTime', true)
  },
  {
    width: '150',
    field: 'deliveryRemark',
    headerText: i18n.t('送货单备注')
  },
  {
    width: '150',
    field: 'deliveryNumber',
    headerText: i18n.t('来源单号')
  },
  {
    width: '150',
    field: 'buyerOrgName',
    headerText: i18n.t('采购组')
  },
  {
    width: '150',
    field: 'deliveryType', // 送货类型:1-关联采购订单,2-无采购订单
    headerText: i18n.t('送货类型'),
    valueConverter: {
      // type: "map",
      // map: [
      //   { value: 1, text: i18n.t("采购订单"), cssClass: "" },
      //   { value: 2, text: i18n.t("交货计划"), cssClass: "" },
      //   { value: 3, text: i18n.t("JIT"), cssClass: "" },
      //   { value: 4, text: i18n.t("无需求"), cssClass: "" },
      //   { value: 5, text: i18n.t("vmi"), cssClass: "" },
      //   { value: 6, text: i18n.t("钢材"), cssClass: "" },
      // ],
      type: 'function',
      filter: (e) => {
        return e?.label ?? ''
      }
    }
  },
  // {
  //   width: "150",
  //   field: "deliveryType",
  //   headerText: i18n.t("交货方式"),
  //   valueConverter: {
  //     type: "map",
  //     map: [
  //       { value: 1, text: i18n.t("采购订单"), cssClass: "" },
  //       { value: 2, text: i18n.t("交货计划"), cssClass: "" },
  //       { value: 3, text: i18n.t("JIT"), cssClass: "" },
  //       { value: 4, text: i18n.t("无需求"), cssClass: "" },
  //     ],
  //   },
  // },
  {
    width: '150',
    field: 'workOrderNo',
    headerText: i18n.t('关联工单号')
  },
  {
    width: '200',
    field: 'saleOrderNo',
    headerText: i18n.t('关联销售订单号')
  },
  {
    width: '200',
    field: 'saleOrderLineNo',
    headerText: i18n.t('关联销售订单行号')
  },
  {
    width: '150',
    field: 'bomCode',
    headerText: i18n.t('BOM号')
  },
  {
    width: '150',
    field: 'productCode',
    headerText: i18n.t('关联产品代码')
  },
  {
    width: '150',
    field: 'workCenterName',
    headerText: i18n.t('工作中心')
  },
  {
    width: '150',
    field: 'processName',
    headerText: i18n.t('工序名称')
  },
  {
    width: '150',
    field: 'sendAddress',
    headerText: i18n.t('送货地址')
  },
  {
    width: '150',
    field: 'batchCode',
    headerText: i18n.t('卷号')
  },
  {
    width: '150',
    field: 'limitQuantity',
    headerText: i18n.t('限量数量')
  },
  {
    width: '150',
    field: 'warehouseClerkName',
    headerText: i18n.t('仓管员')
  },
  {
    width: '150',
    field: 'dispatcherName',
    headerText: i18n.t('调度员')
  },
  {
    width: '150',
    field: 'collectorMark',
    headerText: i18n.t('代收标识')
  },
  {
    width: '150',
    field: 'collectorName',
    headerText: i18n.t('代收人')
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人') // 暂无
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('行备注')
  }
]
