<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 下面的内容 -->
    <div class="main-bottom" style="padding-top: 10px">
      <mt-form ref="ruleForm" :model="addForm" :validate-on-rule-change="false">
        <mt-form-item prop="businessTypeId" :label="$t('选择发货方式')">
          <mt-input
            v-model="addForm.typeName"
            :disabled="true"
            :placeholder="$t('请输入发货方式')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item v-if="addForm.type === 1" prop="businessTypeId" :label="$t('物流公司')">
          <mt-input
            ref="businessRef"
            v-model="addForm.logisticsCompanyName"
            :disabled="true"
            :placeholder="$t('请输入物流公司')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item v-if="addForm.type === 1" prop="title" :label="$t('物流单号')">
          <mt-input
            v-model="addForm.logisticsNo"
            :disabled="true"
            :maxlength="50"
            :placeholder="$t('请输入物流单号')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item v-if="addForm.type === 1" prop="title" :label="$t('件数')">
          <mt-input
            v-model="addForm.number"
            :disabled="true"
            :maxlength="50"
            :placeholder="$t('件数')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item v-if="addForm.type === 2" prop="driverName" :label="$t('司机姓名')">
          <mt-input
            v-model="addForm.driverName"
            :disabled="true"
            :maxlength="10"
            :placeholder="$t('请输入司机姓名')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item v-if="addForm.type === 2" prop="driverNo" :label="$t('司机身份证号')">
          <mt-input
            v-model="addForm.driverNo"
            :disabled="true"
            :maxlength="50"
            :placeholder="$t('请输入司机身份证号')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item v-if="addForm.type === 2" prop="driverNo" :label="$t('车牌号')">
          <mt-input
            v-model="addForm.carNo"
            :disabled="true"
            :maxlength="50"
            :placeholder="$t('请输入车牌号')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="driverPhone" :label="$t('司机联系方式')" v-if="addForm.type === 2">
          <mt-input :disabled="true" v-model="addForm.driverPhone" maxlength="11"></mt-input>
        </mt-form-item>
        <mt-form-item v-if="addForm.type === 2" :disabled="true" prop="remark" :label="$t('件数')">
          <mt-input
            :disabled="true"
            v-model="addForm.remark"
            :placeholder="$t('请输入件数')"
            :maxlength="200"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input :multiline="true" :placeholder="$t('备注')" v-model="addForm.remark" />
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      isInit: true, // 是第一次赋值
      isExpand: true,
      type: '', // add view edit
      id: '',
      applyUserIdData: [], // 申请人列表
      organizationData: {},
      applyCompanyData: [], // 公司
      applyDepartData: [], // 部门
      addForm: {
        carNo: '',
        deliveryId: '',
        driverName: '',
        driverNo: '',
        driverPhone: '',
        idList: [],
        logisticsCompanyCode: '',
        logisticsCompanyName: '',
        logisticsNo: '',
        number: '',
        remark: '',
        tenantId: '',
        type: 0
      },
      isClickCancel: false //手动点击了取消按钮， 为了不再次触发change事件
    }
  },

  computed: {
    // 采购申请 保存flag 0：默认不处理；1：保存草稿；2：提交
    prSubmitFlag() {
      return this.$store.state.prSubmitFlag
    },

    // 0-草稿，1. 待审批，2. 审批通过，3. 审批拒绝，4. 关闭
    // !查看状态 && !商城转申请 && (状态为0或3)
    canSave() {
      return (
        this.$route.query.type != 'view' &&
        this.$route.query.source != 2 &&
        this.headerInfo &&
        (this.headerInfo.status == 0 || this.headerInfo.status == 3)
      )
    },
    // !查看状态 && (状态为0或3 || 是 补充信息操作)
    canSubmit() {
      return (
        this.$route.query.type != 'view' &&
        ((this.headerInfo && (this.headerInfo.status == 0 || this.headerInfo.status == 3)) ||
          this.type == 'replanish')
      )
    }
  },

  watch: {
    // headerInfo(newVal) {
    //   if (!this.isInit) return;
    //   this.addForm = cloneDeep(newVal);
    //   this.isInit = false;
    // },
  },

  mounted() {
    this.id = this.$route.query.id
    this.getList()
    const params = {
      deliveryId: this.id
    }
    // this.apiStartLoading();
    this.$API.receiptAndDelivery
      .getBuyerOrderDeliveryQueryDeliveryLogisticsInfo(params)
      .then((res) => {
        // this.apiEndLoading();
        if (res?.code == 200) {
          this.addForm = res.data
          this.addForm.typeName = res.data.type === 1 ? this.$t('快递配送') : this.$t('物流配送')
          console.log(this.addForm)
        }
      })
      .catch(() => {
        // this.apiEndLoading();
      })
  },

  methods: {
    startToSave(flag) {
      this.$emit('startToSave', flag)
    },

    getList() {
      // 初始化数据
      // this.$API.receiptAndDelivery
      //   .tenantSupplierDeliveryLogisticsInfo(this.id)
      //   .then((res) => {
      //     this.addForm = res.data;
      //     console.log(res.data);
      //     this.addForm.typeName =
      //       res.data.type === 1 ? this.$t("快递配送") : this.$t("物流配送");
      //     // 测试
      //   });
    },

    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}

.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      &.more-width {
        width: 450px;
      }

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
