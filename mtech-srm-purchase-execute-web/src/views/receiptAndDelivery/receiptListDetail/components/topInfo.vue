<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="status mr20">
        {{ typeInfo(headerInfo.status) }}
      </div>
      <div class="infos mr20">{{ $t('送货单编号：') }}{{ headerInfo.deliveryCode }}</div>
      <div class="infos mr20">{{ $t('更新人：') }}{{ getUpdateUserName(headerInfo) }}</div>
      <div class="infos">{{ $t('更新日期：') }}{{ getUpdateTime(headerInfo) }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧各种操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" @click="doSubmit">{{
        $t('提交')
      }}</mt-button>
      <div class="sort-box" @click="isExpand = !isExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="addForm" :validate-on-rule-change="false">
        <mt-form-item prop="supplier" :label="$t('供应商')">
          <mt-input v-model="headerInfo.supplier" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="site" :label="$t('工厂')">
          <mt-input v-model="headerInfo.site" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="warehouse" :label="$t('库存地点')">
          <mt-input v-model="headerInfo.warehouse" :disabled="true" :maxlength="50" />
        </mt-form-item>
        <mt-form-item prop="sendTime" :label="$t('发货日期')">
          <mt-date-time-picker v-model="headerInfo.sendTime" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="forecastArriveTime" :label="$t('预计到货日期')">
          <mt-date-time-picker v-model="headerInfo.forecastArriveTime" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="company" :label="$t('公司')">
          <mt-input v-model="headerInfo.company" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="deliveryType" :label="$t('送货单类型')">
          <mt-select
            v-model="headerInfo.deliveryType"
            :data-source="DeliveryTypeOptions"
            :disabled="true"
          />
        </mt-form-item>
        <mt-form-item prop="shipType" :label="$t('送货方式')">
          <mt-select
            v-model="headerInfo.shipType"
            :data-source="ShipTypeOptions"
            :disabled="true"
          />
        </mt-form-item>
        <mt-form-item prop="receiveAddressName" :label="$t('收货地址')">
          <mt-input v-model="headerInfo.receiveAddressName" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="receiverContactName" :label="$t('收货联系人')">
          <mt-input v-model="headerInfo.receiverContactName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="receiverContact" :label="$t('联系人方式')">
          <mt-input v-model="headerInfo.receiverContact" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="receiver" :label="$t('加工商')">
          <mt-input v-model="headerInfo.receiver" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="thirdTenant" :label="$t('第三方物流商')">
          <mt-input v-model="headerInfo.thirdTenant" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="outsourcedType" :label="$t('委外方式')">
          <mt-select
            v-model="headerInfo.outsourcedType"
            :data-source="OutsourcedTypeOptions"
            :disabled="true"
          />
        </mt-form-item>
        <mt-form-item prop="vmiWarehouse" :label="$t('VMi仓')">
          <mt-input v-model="vmiWarehouse" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input
            :multiline="true"
            v-model="headerInfo.remark"
            :maxlength="200"
            :disabled="true"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {
        return 1212
      }
    }
  },
  data() {
    return {
      ShipTypeOptions: [
        {
          value: 0,
          text: this.$t('直送')
        },
        {
          value: 1,
          text: this.$t('非直送')
        }
      ],
      OutsourcedTypeOptions: [
        {
          value: 0,
          text: this.$t('标准委外')
        },
        {
          value: 1,
          text: this.$t('销售委外')
        },
        {
          value: 2,
          text: this.$t('非委外')
        }
      ],
      DeliveryTypeOptions: [
        { value: 1, text: this.$t('采购订单'), cssClass: '' },
        { value: 2, text: this.$t('交货计划'), cssClass: '' },
        { value: 3, text: this.$t('JIT'), cssClass: '' },
        { value: 4, text: this.$t('无需求'), cssClass: '' },
        { value: 5, text: this.$t('vmi'), cssClass: '' },
        { value: 6, text: this.$t('钢材'), cssClass: '' }
      ],
      isInit: true, // 是第一次赋值
      isExpand: true,
      type: '', // add view edit

      businessTypeList: [], // 业务类型
      organizationData: {},
      thirdTenant: '',
      vmiWarehouse: '',
      applyCompanyData: [], // 公司
      applyDepartData: [], // 部门
      addForm: {
        title: '', // 采购申请名称
        businessTypeId: '', // 业务类型
        // businessTypeCode: "",
        // businessTypeName: "",
        applyUserId: '', // 申请人
        // applyUserName: "",
        // applyUserCode: ""
        companyId: '', // 公司
        // companyName: "",
        // companyCode: ""
        applyDepId: '', // 申请部门
        // applyDepCode: "",
        // applyDepName: "",
        remark: '',
        projectDesc: '' // 项目名称的聚合
      },
      isClickCancel: false //手动点击了取消按钮， 为了不再次触发change事件
    }
  },

  computed: {
    // 采购申请 保存flag 0：默认不处理；1：保存草稿；2：提交
    prSubmitFlag() {
      return this.$store.state.prSubmitFlag
    },

    // 0-草稿，1. 待审批，2. 审批通过，3. 审批拒绝，4. 关闭
    // !查看状态 && !商城转申请 && (状态为0或3)
    canSave() {
      return (
        this.$route.query.type != 'view' &&
        this.$route.query.source != 2 &&
        this.headerInfo &&
        (this.headerInfo.status == 0 || this.headerInfo.status == 3)
      )
    },
    // !查看状态 && (状态为0或3 || 是 补充信息操作)
    canSubmit() {
      return (
        this.$route.query.type != 'view' &&
        ((this.headerInfo && (this.headerInfo.status == 0 || this.headerInfo.status == 3)) ||
          this.type == 'replanish')
      )
    }
  },

  mounted() {
    this.timeDate()

    this.type = this.$route.query.type
    this.getList()
    this.thirdTenant = this.headerInfo.thirdTenantCode + this.headerInfo.thirdTenantName
    this.vmiWarehouse = this.headerInfo.vmiWarehouseCode + this.headerInfo.vmiWarehouseName
  },

  methods: {
    // 提交
    doSubmit() {
      this.$emit('doSubmit')
    },
    // 时间戳转换
    timeDate() {
      let sendData = this.dateFormat(this.headerInfo.sendTime)
      let sendDataTime = this.timeFormat(this.headerInfo.sendTime)
      this.headerInfo.sendTime = sendData + '-' + sendDataTime
      let forecastData = this.dateFormat(this.headerInfo.forecastArriveTime)
      let forecastTime = this.timeFormat(this.headerInfo.forecastArriveTime)

      this.headerInfo.forecastArriveTime = forecastData + '-' + forecastTime
    },
    dateFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }
      return str
    },
    timeFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'HH:MM:SS', value })
      } else {
        str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
      }
      return str
    },
    // 更新人
    getUpdateUserName(data) {
      let userName = ''
      if (data.updateUserName) {
        userName = data.updateUserName // 更新人
      } else {
        userName = data.createUserName // 创建人
      }
      return userName
    },
    // 更新日期
    getUpdateTime(data) {
      let dateStr = ''
      let timeStr = ''
      if (data.updateTime) {
        dateStr = this.dateFormat(data.updateTime) // 更新日期
        timeStr = this.timeFormat(data.updateTime) // 更新时间
      } else {
        dateStr = this.dateFormat(data.createTime) // 创建日期
        timeStr = this.timeFormat(data.createTime) // 创建时间
      }
      return `${dateStr} ${timeStr}`
    },
    // 获取当前状态
    typeInfo(e) {
      if (e === 1) {
        return this.$t('新建')
      } else if (e === 2) {
        return this.$t('发货中')
      } else if (e === 3) {
        return this.$t('已完成')
      } else if (e === 4) {
        return this.$t('已取消')
      } else if (e === 5) {
        return this.$t('已关闭')
      }
    },

    async getList() {
      // 获取业务类型列表
      this.$API.masterData.getDictCode({ dictCode: 'businessType' }).then((res) => {
        if (res.data && res.data.length) {
          this.businessTypeList = res.data
          if (this.$route.query.id && this.headerInfo.businessTypeId) {
            this.addForm.businessTypeId = this.headerInfo.businessTypeId
          } else if (this.$route.query?.source == 2) {
            this.queryMallBusinessType()
          }
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      &.more-width {
        width: 450px;
      }

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
