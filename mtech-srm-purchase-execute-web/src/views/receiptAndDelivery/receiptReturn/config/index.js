import { TypeOfReturnOptions, DeliveryTypeOptions } from './constant'
import { timeDate } from './columnComponent'
import { i18n } from '@/main.js'
import { codeNameColumn } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'

// 格式化表格动态数据
export const formatTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150'
    }
    if (col.fieldCode === 'type') {
      // 收退货类型
      defaultCol.valueConverter = {
        type: 'map',
        map: TypeOfReturnOptions
      }
    } else if (col.fieldCode === 'deliveryCreateTime') {
      // deliveryCreateTime 创建时间 integer
      defaultCol.template = timeDate({
        dataKey: col.fieldCode,
        hasTime: true
      })
    } else if (col.fieldCode === 'deliveryType') {
      // 送货方式
      defaultCol.valueConverter = {
        type: 'map',
        map: DeliveryTypeOptions
      }
    } else if (col.fieldCode === 'supplierCode') {
      // 供应商
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.supplier
      }
    } else if (col.fieldCode === 'siteCode') {
      // 工厂
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.factoryAddress,
        placeholder: i18n.t('工厂')
      }
    } else if (col.fieldCode === 'itemCode') {
      // 物料
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'itemCode',
        secondKey: 'itemName'
      })
      defaultCol.allowFiltering = false // 不可使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      // defaultCol.searchOptions = {
      //   ...MasterDataSelect.material,
      // };
    } else if (col.fieldCode === 'buyerOrgCode') {
      // 采购组
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'buyerOrgCode',
        secondKey: 'buyerOrgName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessGroup
      }
    } else if (col.fieldCode === 'unitCode') {
      // 单位
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'unitCode',
        secondKey: 'unitName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.unit
      }
    } else if (col.fieldCode === 'warehouseCode') {
      // 交货库存地点
      // code-name 形式
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'warehouseCode',
        secondKey: 'warehouseName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.searchOptions = {
        ...MasterDataSelect.stockAddress
      }
    } else if (col.fieldCode === 'deliveryCreateUserCode') {
      // 创建人
      defaultCol.width = '300'
      defaultCol.template = codeNameColumn({
        firstKey: 'deliveryCreateUserCode',
        secondKey: 'deliveryCreateUser'
      })
      defaultCol.allowFiltering = false // 不可使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.staff
      }
    }
    colData.push(defaultCol)
  })

  return colData
}
