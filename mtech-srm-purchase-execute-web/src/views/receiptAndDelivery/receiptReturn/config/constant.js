import { i18n } from '@/main.js'

// 收退货类型 1-收货，2-退货
export const TypeOfReturn = {
  receive: '1', // 收货
  return: '2' // 退货
}
// 收退货类型 text
export const TypeOfReturnText = {
  [TypeOfReturn.receive]: i18n.t('收货'),
  [TypeOfReturn.return]: i18n.t('退货')
}
// 收退货类型 对应的 Options
export const TypeOfReturnOptions = [
  {
    // 收货
    value: TypeOfReturn.receive,
    text: TypeOfReturnText[TypeOfReturn.receive],
    cssClass: ''
  },
  {
    // 退货
    value: TypeOfReturn.return,
    text: TypeOfReturnText[TypeOfReturn.return],
    cssClass: ''
  }
]

// 送货方式 Options
export const DeliveryTypeOptions = [
  { value: '0', text: i18n.t('PO'), cssClass: '' },
  { value: '1', text: i18n.t('无PO'), cssClass: '' },
  { value: '2', text: i18n.t('VMI'), cssClass: '' },
  { value: '3', text: i18n.t('JIT'), cssClass: '' },
  { value: '4', text: i18n.t('排期'), cssClass: '' }
]

// 表格列数据
export const ColumnData = [
  // 收退货类型
  {
    fieldCode: 'type',
    fieldName: i18n.t('收退货类型')
  },
  // 来源采购订单
  {
    fieldCode: 'orderCode',
    fieldName: i18n.t('来源采购订单')
  },
  // 采购订单行号
  {
    fieldCode: 'orderLineNo',
    fieldName: i18n.t('采购订单行号')
  },
  // 关联送货单
  {
    fieldCode: 'deliveryCode',
    fieldName: i18n.t('关联送货单')
  },
  // 关联送货单行号
  {
    fieldCode: 'deliveryLineNo',
    fieldName: i18n.t('关联送货单行号')
  },
  // 供应商
  {
    fieldCode: 'supplierCode', // supplierName code-name
    fieldName: i18n.t('供应商')
  },
  // 工厂
  {
    fieldCode: 'siteCode', // siteName code-name
    fieldName: i18n.t('工厂')
  },
  // 物料
  {
    fieldCode: 'itemCode', // itemName code-name
    fieldName: i18n.t('物料')
  },
  // 采购组
  {
    fieldCode: 'buyerOrgCode', // buyerOrgName code-name
    fieldName: i18n.t('采购组')
  },
  // 单位
  {
    fieldCode: 'unitCode', // unitName code-name
    fieldName: i18n.t('单位')
  },
  // 交货库存地点
  {
    fieldCode: 'warehouseCode', // warehouseName code-name
    fieldName: i18n.t('交货库存地点')
  },
  // 送货地址
  {
    fieldCode: 'sendAddress',
    fieldName: i18n.t('送货地址')
  },
  // 关联工单号
  {
    fieldCode: 'associatedNumber',
    fieldName: i18n.t('关联工单号')
  },
  // 关联销售订单号
  {
    fieldCode: 'saleOrderNo',
    fieldName: i18n.t('关联销售订单号')
  },
  // 关联销售订单行号
  {
    fieldCode: 'saleOrderLineNo',
    fieldName: i18n.t('关联销售订单行号')
  },
  // 数量
  {
    fieldCode: 'quantity',
    fieldName: i18n.t('数量')
  },
  // 送货方式
  {
    fieldCode: 'deliveryType',
    fieldName: i18n.t('送货方式')
  },
  // // 送货方名称
  // {
  //   fieldCode: "senderName",
  //   fieldName: i18n.t("送货方名称"),
  // },
  // // 送货方编号
  // {
  //   fieldCode: "senderCode",
  //   fieldName: i18n.t("送货方编号"),
  // },
  // 创建人
  {
    fieldCode: 'deliveryCreateUserCode', // deliveryCreateUser code-name
    fieldName: i18n.t('创建人')
  },
  // 创建时间
  {
    fieldCode: 'deliveryCreateTime',
    fieldName: i18n.t('创建时间')
  }
]
