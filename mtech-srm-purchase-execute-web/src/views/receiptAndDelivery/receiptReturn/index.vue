<template>
  <!-- 收退货记录-采方 -->
  <div class="full-height pt20">
    <!-- 列模板 -->
    <mt-template-page ref="templateRef" :template-config="templateConfig"> </mt-template-page>
  </div>
</template>

<script>
import { formatTableColumnData } from './config/index'
import { ColumnData } from './config/constant'
import { BASE_TENANT } from '@/utils/constant'

export default {
  components: {},
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      templateConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          gridId: this.$tableUUID.receiptAndDelivery.receiptReturn.list,
          grid: {
            allowPaging: true, // 分页
            lineIndex: 0, // 序号列
            columnData: formatTableColumnData({
              data: ColumnData
            }),
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/buyerReceiptRecords/query` // 采方收退货记录-采方收退货列表
            }
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped></style>
