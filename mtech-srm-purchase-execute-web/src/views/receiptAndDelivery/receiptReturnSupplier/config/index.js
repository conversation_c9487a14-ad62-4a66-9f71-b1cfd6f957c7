import { i18n } from '@/main.js'
import { timeDate } from './columnComponent'

export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'strategyName',
    headerText: i18n.t('事务类型')
  },
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('来源采购订单')
  },
  {
    width: '150',
    field: 'orderLineNo',
    headerText: i18n.t('采购订单行号')
  },
  {
    width: '150',
    field: 'deliveryCode',
    headerText: i18n.t('关联送货单')
  },
  {
    width: '150',
    field: 'deliveryLineNo',
    headerText: i18n.t('关联送货单行号')
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('工厂')
  },

  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
    // valueConverter: {
    //   type: "map",
    //   map: [
    //     { value: 1, text: i18n.t("启用"), cssClass: "col-active" },
    //     { value: 0, text: i18n.t("停用"), cssClass: "col-inactive" },
    //   ],
    // },
    // cellTools: [
    //   {
    //     id: "active",
    //     icon: "icon_Editor",
    //     title: i18n.t("关闭"),
    //     permission: ["O_02_0125"],
    //     // visibleCondition: (data) => data["status"] === '发货中',
    //   },
    //   {
    //     id: "inactive",
    //     icon: "icon_Editor",
    //     title: i18n.t("取消"),
    //     permission: ["O_02_0126"],
    //     // visibleCondition: (data) => data["status"] === '发货中',

    //   },
    // ],
  },
  {
    width: '150',
    field: 'buyerOrgName',
    headerText: i18n.t('采购组')
  },
  {
    width: '150',
    field: 'unitName',
    headerText: i18n.t('单位')
  },
  {
    width: '150',
    field: 'warehouseName',
    headerText: i18n.t('交货库存地点')
  },
  {
    width: '150',
    field: 'sendAddress',
    headerText: i18n.t('送货地址')
  },
  {
    width: '150',
    field: 'associatedNumber',
    headerText: i18n.t('关联工单号')
  },
  {
    width: '150',
    field: 'saleOrderNo',
    headerText: i18n.t('关联销售订单号')
  },
  {
    width: '150',
    field: 'saleOrderLineNo',
    headerText: i18n.t('关联销售订单行号')
  },
  {
    width: '150',
    field: 'quantity',
    headerText: i18n.t('数量')
  },
  {
    width: '150',
    field: 'deliveryType',
    headerText: i18n.t('送货方式'), // TODO 显示转换 API 没有提供转换规则
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('PO'), cssClass: '' },
        { value: 1, text: i18n.t('无PO'), cssClass: '' },
        { value: 2, text: i18n.t('VMI'), cssClass: '' },
        { value: 3, text: i18n.t('JIT'), cssClass: '' },
        { value: 4, text: i18n.t('排期'), cssClass: '' }
      ]
    }
  },
  // {
  //   width: "150",
  //   field: "senderName",
  //   headerText: i18n.t("送货方名称"),
  // },
  // {
  //   width: "150",
  //   field: "senderCode",
  //   headerText: i18n.t("送货方编号"),
  // },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    template: timeDate({
      dataKey: 'createTime',
      hasTime: true
    })
  }
]
