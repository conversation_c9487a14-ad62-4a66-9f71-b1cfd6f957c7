<template>
  <!-- 收退货记录-供方 -->
  <div class="full-height pt20">
    <mt-template-page ref="template-0" :template-config="pageConfig1"> </mt-template-page>
  </div>
</template>

<script>
// import { BASE_TENANT } from "@/utils/constant";
import { columnData } from './config/index.js'

export default {
  mounted() {
    // let userInfo = sessionStorage.getItem("userInfo");
    // if (userInfo) {
    //   this.userInfo = JSON.parse(userInfo);
    //   this.$set(this.pageConfig0[0].grid, "asyncConfig", {
    //     url: `${BASE_TENANT}/supplierReceiptRecords/query`,
    //     defaultRules: [
    //       {
    //         label: this.$t("创建人"),
    //         field: "createUserId",
    //         type: "string",
    //         operator: "equal",
    //         value: this.userInfo?.uid,
    //       },
    //     ],
    //   });
    // }
  },
  data() {
    return {
      userInfo: null,
      currentTabIndex: 0,
      pageConfig1: [
        {
          gridId: this.$tableUUID.receiptAndDelivery.receiptReturnSupplier.list,
          grid: {
            columnData: columnData,
            lineIndex: 1,
            autoWidthColumns: columnData.length + 1,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/supplierReceiptRecords/query'
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  methods: {}
}
</script>

<style></style>
