import { i18n } from '@/main.js'
import { timeNumberToDate } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'
import { codeNameColumn } from '@/utils/utils'
import Vue from 'vue'

export const columnData = [
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  },
  {
    width: '121',
    field: 'itemVoucherYear',
    headerText: i18n.t('物料凭证年度')
  },
  {
    width: '111',
    field: 'receiveCode',
    headerText: i18n.t('物料凭证号'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '125',
    field: 'receiveItemNo',
    headerText: i18n.t('物料凭证行号')
  },
  {
    width: '135',
    field: 'receiveCodeRef',
    headerText: i18n.t('参考物料凭证号')
  },
  {
    width: '120',
    field: 'itemVoucherDate',
    headerText: i18n.t('物料凭证日期'),
    searchOptions: {
      ...MasterDataSelect.timeRange,
      default: [
        new Date(
          new Date().getFullYear() - 1,
          new Date().getMonth(),
          new Date().getDate(),
          0,
          0,
          0
        ),
        new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate(), 23, 59, 59)
      ]
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    }
  },
  {
    width: '96',
    field: 'transType',
    headerText: i18n.t('事务类型')
  },
  {
    width: '106',
    field: 'orderCode',
    headerText: i18n.t('采购订单号'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '125',
    field: 'lineNo',
    headerText: i18n.t('采购订单行号')
  },
  {
    width: '150',
    field: 'orderTypeCode', // orderTypeName
    template: codeNameColumn({
      firstKey: 'orderTypeCode',
      secondKey: 'orderTypeName'
    }),
    allowFiltering: false,
    headerText: i18n.t('订单类型'),
    searchOptions: {
      ...MasterDataSelect.dictOrderSupplierType,
      placeholder: i18n.t('请选择订单类型')
    }
  },
  {
    width: '105',
    field: 'projectName',
    headerText: i18n.t('项目名称')
  },
  {
    width: '105',
    field: 'projectCode',
    headerText: i18n.t('项目编号')
  },
  {
    width: '105',
    field: 'contractCode',
    headerText: i18n.t('合同编号')
  },
  {
    width: '98',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组'),
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    width: '100',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.supplier
    }
  },
  {
    width: '250',
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    ignore: true
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    allowFiltering: false
  },
  {
    width: '300',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    ignore: true
  },
  {
    width: '100',
    field: 'spec',
    headerText: i18n.t('规格型号')
  },
  {
    width: '100',
    field: 'categoryName',
    headerText: i18n.t('品类')
  },
  {
    width: '70',
    field: 'customName',
    headerText: i18n.t('客户')
  },
  {
    width: '115',
    field: 'relationCustomOrder',
    headerText: i18n.t('销售订单号')
  },
  {
    width: '75',
    field: 'quantity',
    headerText: i18n.t('数量')
  },
  {
    width: '65',
    field: 'unitName',
    headerText: i18n.t('单位'),
    searchOptions: {
      ...MasterDataSelect.unit,
      fields: { text: 'title', value: 'unitName' }
    }
  },
  {
    width: '238',
    field: 'companyCode',
    headerText: i18n.t('公司编号'),
    template: codeNameColumn({
      firstKey: 'companyCode',
      secondKey: 'companyName'
    }),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.companySupplier
    }
  },
  {
    width: '135',
    field: 'siteCode',
    headerText: i18n.t('地点/工厂'),
    template: codeNameColumn({
      firstKey: 'siteCode',
      secondKey: 'siteName'
    }),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.factoryAddress
    }
  },
  {
    width: '98',
    field: 'stockSite',
    headerText: i18n.t('库存地点'),
    searchOptions: {
      ...MasterDataSelect.stockAddress,
      fields: { text: 'title', value: 'locationName' }
    }
  },
  {
    width: '140',
    field: 'receiveCodeRel',
    headerText: i18n.t('关联交货单编号')
  },
  {
    width: '95',
    field: 'costCenter',
    headerText: i18n.t('成本中心'),
    searchOptions: {
      ...MasterDataSelect.costCenter,
      fields: { text: 'title', value: 'costCenterName' }
    }
  },
  {
    width: '95',
    field: 'profitCenter',
    headerText: i18n.t('利润中心'),
    searchOptions: {
      ...MasterDataSelect.profitCenter,
      fields: { text: 'title', value: 'profitCenterName' }
    }
  },
  {
    width: '95',
    field: 'paymentMode',
    headerText: i18n.t('付款方式'),
    searchOptions: {
      ...MasterDataSelect.dictPaymentMode,
      placeholder: i18n.t('请选择付款方式'),
      fields: { text: 'title', value: 'itemName' }
    }
  },
  {
    width: '95',
    field: 'remark',
    headerText: i18n.t('备注说明')
  },
  {
    width: '85',
    field: 'receiveUserName',
    headerText: i18n.t('收货人'),
    searchOptions: {
      ...MasterDataSelect.staff,
      fields: { text: 'title', value: 'employeeName' }
    }
  },
  {
    width: '100',
    field: 'receiveTime',
    headerText: i18n.t('过账日期'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    }
  },
  {
    width: '120',
    field: 'inputDate',
    headerText: i18n.t('凭证创建日期'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    }
  },
  {
    width: '120',
    field: 'inputTime',
    headerText: i18n.t('凭证创建时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    }
  },
  {
    width: '95',
    field: 'orderUnitCode',
    headerText: i18n.t('订单单位编码')
  },
  {
    width: '95',
    field: 'orderUnitName',
    headerText: i18n.t('订单单位名称')
  },
  {
    width: '95',
    field: 'unitQuantity',
    headerText: i18n.t('订单单位数量')
  },
  {
    width: '95',
    field: 'deliveryCode',
    headerText: i18n.t('送货单号'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '95',
    field: 'deliveryLineNo',
    headerText: i18n.t('送货单行号')
  }
]
