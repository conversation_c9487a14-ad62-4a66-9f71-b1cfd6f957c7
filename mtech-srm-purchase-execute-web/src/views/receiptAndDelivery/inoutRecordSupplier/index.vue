<template>
  <!-- 出入库记录 -->
  <div class="full-height pt20">
    <!-- 列模板 -->
    <mt-template-page
      ref="templateRef"
      @handleClickToolBar="handleClickToolBar"
      :template-config="templateConfig"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {},
  data() {
    return {
      templateConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'listExport',
              icon: 'icon_solid_export',
              permission: ['O_02_1664', 'O_02_1665', 'O_02_1666'],
              title: this.$t('导出')
            }
          ],
          activatedRefresh: false,

          gridId: '868D4C60-1FA9-E321-2CC6-074101BEE468',
          grid: {
            virtualPageSize: 30,
            showSelected: false,
            selectionSettings: {
              persistSelection: true,
              type: 'Multiple',
              checkboxOnly: true
            },
            enableVirtualization: true,
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [10, 20, 50, 100, 200, 500, 1000],
              totalRecordsCount: 0
            },
            allowPaging: true, // 分页
            lineIndex: 0, // 序号列
            columnData: columnData,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/po/in_out_record/supplier-query?BU_CODE=${
                this.isGf ? 'GF' : ''
              }`, // 订单库存出入库接口 -
              ignoreDefaultSearch: true
            }
          }
        }
      ]
    }
  },
  computed: {
    isGf() {
      return this.$route.name === 'inout-record-supplier-gf'
    }
  },
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id === 'listExport') {
        // 配置-导出
        this.listExport()
      }
    },
    listExport() {
      let obj = JSON.parse(sessionStorage.getItem(this.templateConfig[0].gridId))?.visibleCols
      const headerMap = {}
      if (obj !== undefined && obj.length) {
        obj?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      } else {
        this.templateConfig[0].grid.columnData?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      }
      let queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const rulesValue = [
        new Date(
          new Date().getFullYear() - 1,
          new Date().getMonth(),
          new Date().getDate(),
          0,
          0,
          0
        ).getTime(),
        new Date(
          new Date().getFullYear(),
          new Date().getMonth(),
          new Date().getDate(),
          23,
          59,
          59
        ).getTime()
      ]
      if (!queryBuilderRules.rules) {
        queryBuilderRules = {
          condition: 'and',
          rules: [
            {
              label: this.$t('物料凭证日期'),
              field: 'itemVoucherDate',
              type: 'string',
              operator: 'between',
              value: rulesValue
            }
          ]
        }
      } else {
        if (!queryBuilderRules.rules.some((i) => i.field === 'itemVoucherDate')) {
          queryBuilderRules.rules.push({
            label: this.$t('物料凭证日期'),
            field: 'itemVoucherDate',
            type: 'string',
            operator: 'between',
            value: rulesValue
          })
        }
      }
      const params = {
        currentBu: this.isGf ? 'GF' : '',
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.deliveryConfig.poSupplierExport(params, headerMap).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
        this.$store.commit('endLoading')
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
