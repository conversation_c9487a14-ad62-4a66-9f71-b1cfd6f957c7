import { i18n } from '@/main.js'
import { timeNumberToDate } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'
import { codeNameColumn } from '@/utils/utils'

export const columnData = [
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('采购订单号')
  },
  {
    width: '120',
    field: 'lineNo',
    headerText: i18n.t('采购订单行号')
  },
  {
    width: '150',
    field: 'itemVoucherDate',
    headerText: i18n.t('物料凭证日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '250',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '180',
    field: 'prototypeCode',
    headerText: i18n.t('原机型编号')
  },
  {
    width: '200',
    field: 'buyerOrgName',
    headerText: i18n.t('采购组'),
    ignore: true
  },
  {
    width: '150',
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    width: '120',
    field: 'unitName',
    headerText: i18n.t('单位'),
    searchOptions: {
      ...MasterDataSelect.unit,
      fields: { text: 'title', value: 'unitName' }
    }
  },
  {
    width: '120',
    field: 'isNewItem',
    headerText: i18n.t('是否新品'),
    ignore: true,
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    width: '120',
    field: 'quantity',
    headerText: i18n.t('入库数量')
  },
  {
    width: '150',
    field: 'taxedUnitPrice',
    headerText: i18n.t('入库含税单价')
  },
  {
    width: '150',
    field: 'untaxedUnitPrice',
    headerText: i18n.t('入库未税单价')
  },
  {
    width: '250',
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    template: codeNameColumn({
      firstKey: 'supplierCode',
      secondKey: 'supplierName'
    }),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.supplier
    }
  },
  {
    width: '120',
    field: 'currencyName',
    headerText: i18n.t('币种')
  },
  {
    width: '200',
    field: 'orderTypeCode', // orderTypeName
    template: codeNameColumn({
      firstKey: 'orderTypeCode',
      secondKey: 'orderTypeName'
    }),
    allowFiltering: false,
    headerText: i18n.t('订单类型'),
    searchOptions: {
      ...MasterDataSelect.dictOrderType,
      placeholder: i18n.t('请选择订单类型')
    }
  },
  {
    width: '300',
    field: 'companyCode',
    headerText: i18n.t('公司'),
    template: codeNameColumn({
      firstKey: 'companyCode',
      secondKey: 'companyName'
    }),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.businessCompany
    }
  },
  {
    width: '300',
    field: 'siteCode',
    headerText: i18n.t('地点/工厂'),
    template: codeNameColumn({
      firstKey: 'siteCode',
      secondKey: 'siteName'
    }),
    allowFiltering: false,
    searchOptions: {
      ...MasterDataSelect.factoryAddress
    }
  },
  {
    width: '150',
    field: 'stockSite',
    headerText: i18n.t('库存地点'),
    searchOptions: {
      ...MasterDataSelect.stockAddress,
      fields: { text: 'title', value: 'locationName' }
    }
  },
  {
    width: '150',
    field: 'transType',
    headerText: i18n.t('事务类型')
  },
  {
    width: '150',
    field: 'receiveCode',
    headerText: i18n.t('物料凭证号')
  },
  {
    width: '150',
    field: 'receiveItemNo',
    headerText: i18n.t('物料凭证行号')
  },
  {
    width: '200',
    field: 'deliveryCode',
    headerText: i18n.t('送货单号')
  },
  {
    width: '200',
    field: 'deliveryLineNo',
    headerText: i18n.t('送货单行号')
  },
  {
    width: '150',
    field: 'receiveTime',
    headerText: i18n.t('收货时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  }
]
