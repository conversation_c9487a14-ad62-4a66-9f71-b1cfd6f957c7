<template>
  <!-- 入库未定价 -->
  <div class="full-height">
    <!-- 列模板 -->
    <mt-template-page
      ref="templateRef"
      :template-config="templateConfig"
      @handleClickToolBar="handleClickToolBar"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  components: {},
  data() {
    return {
      templateConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          toolbar: {
            tools: [
              [
                {
                  id: 'export',
                  icon: 'icon_solid_export',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: 'd95cbf25-c5df-4eb0-88b4-61d101073317',
          grid: {
            allowPaging: true, // 分页
            allowTextWrap: true,
            lineSelection: true,
            columnData: columnData,
            asyncConfig: {
              url: `${BASE_TENANT}/po/in_out_record/queryNotPriced` // 订单库存出入库接口 - 获取采方订单主单列表
            }
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(args) {
      const { toolbar } = args
      // const selectRows = gridRef.getMtechGridRecords();
      // if (selectRows.length === 0 && toolbar.id == "export") {
      //   this.$toast({
      //     content: this.$t("请先选择需要导出的数据"),
      //     type: "warning",
      //   });
      //   return;
      // }
      // let _ids = selectRows.map((e) => e.id);
      if (toolbar.id == 'export') {
        const queryBuilderRules =
          this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        const params = {
          condition: 'and',
          page: { current: 1, size: 10000 },
          ...queryBuilderRules
          // rules: [
          //   {
          //     field: "id",
          //     type: "string",
          //     operator: "in",
          //     value: _ids,
          //   },
          // ],
        } // 筛选条件
        this.$store.commit('startLoading')
        this.$API.receiptAndDelivery.exportNotPriced(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
