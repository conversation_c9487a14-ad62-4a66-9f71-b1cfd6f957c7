<template>
  <!-- 供货计划-采方 -->
  <div class="full-height">
    <!-- 列模板 -->
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="false"
      :current-tab="currentTab"
      :permission-obj="permissionObj"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleSelectTab="handleSelectTab"
    >
      <div slot="slot-filter" class="zero-filter-switch">
        <mt-switch
          v-model="zeroFilterSwitch"
          :active-value="1"
          :inactive-value="0"
          :on-label="$t('隐藏不可创建送货单数据')"
          :off-label="$t('显示不可创建送货单数据')"
          v-permission="['O_02_1115']"
          @change="handleChangeZeroFilterSwitch"
        ></mt-switch>
      </div>
    </mt-template-page>
  </div>
</template>

<script>
import {
  ColumnDataTab1,
  ColumnDataTab2,
  ColumnDataTab3,
  ToolbarOrder,
  ToolbarPlan,
  ToolbarJIT,
  TabIndex
} from './config/constant'
import { formatTableColumnData } from './config/index'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {},
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      currentTab: 0, // 当前列模板显示的 Tab
      zeroFilterSwitch: 1, // 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      zeroFilterPlan: 1, // 交货计划 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      zeroFilterOrder: 1, // 采购订单 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      zeroFilterJit: 1, // JIT 是否显示不可创建送货单数据 默认 1: 过滤掉0的数据，其他：不过滤
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'Plan', permissionCode: 'T_02_0023' },
          { dataPermission: 'Order', permissionCode: 'T_02_0022' },
          { dataPermission: 'Jit', permissionCode: 'T_02_0115' }
        ]
      },
      componentConfig: [
        {
          title: this.$t('采购订单'),
          dataPermission: 'Order',
          permissionCode: 'T_02_0022',
          toolbar: ToolbarOrder,
          activatedRefresh: false,

          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId: this.$tableUUID.receiptAndDelivery.supplyPlan.purchaseOrder,
          grid: {
            lineSelection: 0, // 选项列
            lineIndex: 1, // 序号列
            frozenColumns: 1, // 冻结第一列
            columnData: formatTableColumnData({
              data: ColumnDataTab1
            }),
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/buyerDeliverySupplyPlan/order/query`, // 采方收发货供货计划-订单供货计划列表

              query: {
                zeroFilter: 1 // 默认 1: 过滤掉0的数据，其他：不过滤
              }
            }
          }
        },
        {
          title: this.$t('交货计划'),
          dataPermission: 'Plan',
          activatedRefresh: false,

          permissionCode: 'T_02_0023',
          toolbar: ToolbarPlan,
          useToolTemplate: false, // 使用预置表格操作按钮(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId: this.$tableUUID.receiptAndDelivery.supplyPlan.deliverySchedule,
          grid: {
            lineSelection: 0, // 选项列
            lineIndex: 1, // 序号列
            frozenColumns: 1, // 冻结第一列
            columnData: formatTableColumnData({
              data: ColumnDataTab2
            }),
            dataSource: [],
            asyncConfig: {
              // ignoreDefaultSearch: true,
              defaultRules: [
                {
                  field: 'isItemBatch',
                  operator: 'equal',
                  value: 0
                },
                {
                  field: 'jit',
                  operator: 'equal',
                  value: 0
                }
              ],
              url: `${BASE_TENANT}/buyerDeliverySupplyPlan/plan/query`, // 采方收发货供货计划-交货计划供货计划列表
              query: {
                zeroFilter: 1 // 默认 1: 过滤掉0的数据，其他：不过滤
              }
            }
          }
        },
        {
          tab: { title: this.$t('叫料计划') },
          dataPermission: 'Jit',
          permissionCode: 'T_02_0115',
          activatedRefresh: false,

          toolbar: ToolbarJIT,
          useToolTemplate: false, // 使用预置表格操作按钮(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          grid: {
            lineSelection: 0, // 选项列
            lineIndex: 1, // 序号列
            frozenColumns: 1, // 冻结第一列
            columnData: formatTableColumnData({
              data: ColumnDataTab3
            }),
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/buyerDeliverySupplyPlan/jit/query`, // 采方收发货供货计划-JIT供货计划列表
              query: {
                zeroFilter: 1 // 默认 1: 过滤掉0的数据，其他：不过滤
              }
            }
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    // ToolBar
    handleClickToolBar(args) {
      const { toolbar, gridRef } = args
      const selectRows = gridRef.getMtechGridRecords()
      const commonToolbar = [
        'Add',
        'Filter',
        'Refresh',
        'Setting',
        'InvoiceAdd',
        'GetInvoice',
        'SupplyPlanOrderExport',
        'SupplyPlanPlanExport',
        'SupplyPlanJitExport',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]
      if (selectRows.length === 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedId = []
      selectRows.forEach((item) => selectedId.push(item.id))

      if (toolbar.id === 'SupplyPlanOrderExport') {
        // 采购订单-导出
        this.handleOrderExport()
      } else if (toolbar.id === 'SupplyPlanPlanExport') {
        // 交货计划-导出
        this.handlePlanExport()
      } else if (toolbar.id === 'SupplyPlanJitExport') {
        // JIT-导出
        this.handleJitExport()
      }
    },
    // CellTool
    handleClickCellTool() {},
    // 是否显示不可创建送货单数据
    handleChangeZeroFilterSwitch(value) {
      console.log('value', value)
      // 保存当前 tab 选择的 zeroFilterSwitch 状态
      // 并修改列模板请求参数，调用api获取数据
      const currentTabIndex = this.$refs.templateRef.currentTabIndex
      if (currentTabIndex == TabIndex.deliverySchedule) {
        this.zeroFilterPlan = value
        this.$set(this.componentConfig[currentTabIndex].grid.asyncConfig.query, 'zeroFilter', value)
      }
      // else if (currentTabIndex == TabIndex.purchaseOrder) {
      //   this.zeroFilterOrder = value;
      //   this.$set(
      //     this.componentConfig[currentTabIndex].grid.asyncConfig.query,
      //     "zeroFilter",
      //     value
      //   );
      // }
      else if (currentTabIndex == TabIndex.jit) {
        this.zeroFilterJit = value
        this.$set(this.componentConfig[currentTabIndex].grid.asyncConfig.query, 'zeroFilter', value)
      }
    },
    // 交货计划-导出
    handlePlanExport() {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.apiStartLoading()
      this.$API.receiptAndDelivery.postBuyerDeliveryPlanPlanExport(params).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data, width: '195' })
      })
    },
    // 采购订单-导出
    handleOrderExport() {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.apiStartLoading()
      this.$API.receiptAndDelivery.postBuyerDeliveryPlanOrderExport(params).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // JIT-导出
    handleJitExport() {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.apiStartLoading()
      this.$API.receiptAndDelivery.postBuyerDeliveryPlanJitExport(params).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 切换tab
    handleSelectTab(index) {
      // 将各自 tab 保存的 zeroFilterSwitch 赋值给 zeroFilterSwitch
      // 并修改列模板请求参数，调用api获取数据
      if (index == TabIndex.deliverySchedule) {
        this.zeroFilterSwitch = this.zeroFilterPlan
        this.$set(
          this.componentConfig[index].grid.asyncConfig.query,
          'zeroFilter',
          this.zeroFilterPlan
        )
      } else if (index == TabIndex.purchaseOrder) {
        this.zeroFilterSwitch = this.zeroFilterOrder
        this.$set(
          this.componentConfig[index].grid.asyncConfig.query,
          'zeroFilter',
          this.zeroFilterOrder
        )
      } else if (index == TabIndex.jit) {
        this.zeroFilterSwitch = this.zeroFilterJit
        this.$set(
          this.componentConfig[index].grid.asyncConfig.query,
          'zeroFilter',
          this.zeroFilterJit
        )
      }
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
// 显示不可创建送货单数据
.zero-filter-switch {
  text-align: right;
}
</style>
