import { i18n } from '@/main.js'
import { timeDate } from './columnComponent'
import { codeNameColumn } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'

// 格式化表格动态数据
export const formatTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150'
    }
    if (col.fieldCode === 'saleOrderNo') {
      // 关联销售订单号
      defaultCol.width = '140'
    } else if (col.fieldCode === 'itemNo') {
      // 关联采购订单行号 行号
      defaultCol.width = '153'
    } else if (col.fieldCode === 'saleOrderRowCode') {
      // 关联销售订单行号
      defaultCol.width = '200'
    } else if (col.fieldCode === 'requiredDeliveryDate' || col.fieldCode === 'checkDate') {
      // 需求日期 requiredDeliveryDate string
      // 确认日期 checkDate
      defaultCol.width = '93'
      defaultCol.template = timeDate({
        dataKey: col.fieldCode,
        hasTime: false
      })
    } else if (col.fieldCode === 'subSiteCode') {
      // 交货库存地点 code+name 采购订单tab
      defaultCol.width = '219'
      defaultCol.template = codeNameColumn({
        firstKey: 'subSiteCode',
        secondKey: 'subSiteName'
      })
    } else if (col.fieldCode === 'subSiteAddressCode') {
      // 交货库存地点 code+name 采购订单tab
      defaultCol.width = '219'
      defaultCol.template = codeNameColumn({
        firstKey: 'subSiteAddressCode',
        secondKey: 'subSiteAddress'
      })
    } else if (col.fieldCode === 'deliveryMethod') {
      // 是否直送
      defaultCol.valueConverter = {
        type: 'map',
        map: { 0: i18n.t('直送'), 1: i18n.t('非直送') }
      }
    } else if (col.fieldCode === 'outsourcedType') {
      // 委外方式
      defaultCol.valueConverter = {
        type: 'map',
        map: {
          0: i18n.t('标准委外'),
          1: i18n.t('销售委外'),
          2: i18n.t('非委外')
        }
      }
    } else if (['jit', 'isJit'].includes(col.fieldCode)) {
      // 委外方式
      defaultCol.valueConverter = {
        type: 'map',
        map: {
          0: i18n.t('否'),
          1: i18n.t('是')
        }
      }
    } else if (col.fieldCode === 'orderLineNo') {
      // 关联采购订单行号
      defaultCol.width = '153'
    } else if (col.fieldCode === 'warehouse') {
      // 交货库存地点 code+name 采购订单tab
      defaultCol.width = '219'
      defaultCol.template = codeNameColumn({
        firstKey: 'warehouseCode',
        secondKey: 'warehouse'
      })
    } else if (col.fieldCode === 'warehouseName') {
      // 交货库存地点 code+name 交货计划tab
      defaultCol.width = '219'
      defaultCol.template = codeNameColumn({
        firstKey: 'warehouseCode',
        secondKey: 'warehouseName'
      })
    } else if (col.fieldCode === 'senderPhone') {
      // 送货联系电话 code+name 交货计划tab
      defaultCol.width = '300'
      // defaultCol.template = codeNameColumn({
      //   firstKey: 'senderPhone',
      //   secondKey: 'senderAddress'
      // })
    } else if (col.fieldCode === 'companyCode') {
      // 公司
      // code-name 形式
      defaultCol.width = '235'
      defaultCol.template = codeNameColumn({
        firstKey: 'companyCode',
        secondKey: 'companyName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessCompany,
        placeholder: i18n.t('公司')
      }
    } else if (col.fieldCode === 'siteCode') {
      // 工厂
      // code-name 形式
      defaultCol.width = '260'
      defaultCol.template = codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.factoryAddress,
        placeholder: i18n.t('工厂')
      }
    } else if (col.fieldCode === 'itemCode') {
      // 物料
      defaultCol.allowFiltering = false // 不可使用过滤搜索，因为是 code-name 形式
      defaultCol.width = '540'
      defaultCol.template = codeNameColumn({
        firstKey: 'itemCode',
        secondKey: 'itemName'
      })
      // 主数据选择器
    } else if (col.fieldCode === 'supplierCode') {
      // 供应商code
      // code-name 形式
      defaultCol.width = '255'
      defaultCol.template = codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      })
      defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.supplier,
        placeholder: i18n.t('供应商')
      }
    } else if (col.fieldCode === 'createUserName') {
      // 创建人
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.staff,
        fields: { text: 'title', value: 'employeeName' }
      }
    }
    colData.push(defaultCol)
  })

  return colData
}
