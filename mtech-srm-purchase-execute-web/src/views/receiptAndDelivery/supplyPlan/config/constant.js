import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
import inputView from '../components/inputView.vue'

// tab 的 index
export const TabIndex = {
  purchaseOrder: 0, // 采购订单
  deliverySchedule: 1, // 交货计划
  jit: 2 // JIT
}

// Toolbar 按钮 采购订单
export const ToolbarOrder = [
  {
    id: 'SupplyPlanOrderExport',
    icon: 'icon_solid_export',
    title: i18n.t('导出'),
    permission: ['O_02_0492']
  }
]

// Toolbar 按钮 交货计划
export const ToolbarPlan = [
  {
    id: 'SupplyPlanPlanExport',
    icon: 'icon_solid_export',
    title: i18n.t('导出'),
    permission: ['O_02_0493']
  }
]

// Toolbar 按钮 JIT
export const ToolbarJIT = [
  {
    id: 'SupplyPlanJitExport',
    icon: 'icon_solid_export',
    title: i18n.t('导出'),
    permission: ['O_02_1164']
  }
]

// tab1 采购订单 表格列数据
export const ColumnDataTab1 = [
  {
    fieldCode: 'companyCode', // 公司 companyName code-name
    fieldName: i18n.t('公司')
  },
  {
    fieldCode: 'siteCode', // 工厂 siteName code-name
    fieldName: i18n.t('工厂')
  },
  {
    fieldCode: 'itemCode', // 物料号 品项编码 itemName code-name
    fieldName: i18n.t('物料')
  },
  {
    fieldCode: 'orderCode', // 关联采购订单 订单号
    fieldName: i18n.t('关联采购订单')
  },
  {
    fieldCode: 'requiredDeliveryDate', // 需求日期 要求交期
    fieldName: i18n.t('需求日期'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'quantity', // 订单数量
    fieldName: i18n.t('订单数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'preDeliveryQty', // 待发货数量
    fieldName: i18n.t('待发货数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'deliveryQty', // 已发货数量
    fieldName: i18n.t('已发货数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'receiveQty', // 已入库数量
    fieldName: i18n.t('已入库数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'warehouse', // 交货库存地点 库存地点 warehouseCode
    fieldName: i18n.t('库存地点编号+库存地点名称'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'supplierCode', // 供应商编码 supplierName code+name
    fieldName: i18n.t('供应商')
  },
  {
    fieldCode: 'buyerOrgName', // 采购组
    fieldName: i18n.t('采购组'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'saleOrderNo', // 关联销售订单号
    fieldName: i18n.t('关联销售订单号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'projectTextBatch', // 项目文本批次
    fieldName: i18n.t('项目文本批次'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'itemNo', // 关联采购订单行号 行号
    fieldName: i18n.t('关联采购订单行号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'buyerRemark', // 采购方备注
    fieldName: i18n.t('采购方备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'supRemark', // 供应商备注
    fieldName: i18n.t('供应商备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'consignee', // 联系人 收货人
    fieldName: i18n.t('联系人'),
    allowFiltering: false,
    ignore: true,
    template: () => ({ template: inputView })
  },
  {
    fieldCode: 'contact', // 联系电话
    fieldName: i18n.t('联系电话'),
    allowFiltering: false,
    ignore: true,
    template: () => ({ template: inputView })
  },
  {
    fieldCode: 'receiveAddress', // 送货地址
    fieldName: i18n.t('送货地址')
  }
]

// tab2 交货计划 表格列数据
export const ColumnDataTab2 = [
  {
    fieldCode: 'companyCode', // 公司 companyName code-name
    fieldName: i18n.t('公司')
  },
  {
    fieldCode: 'siteCode', // 工厂 siteName code-name
    fieldName: i18n.t('工厂')
  },
  {
    fieldCode: 'itemCode', // 物料号 品项编码 itemName code-name
    fieldName: i18n.t('物料')
  },
  {
    fieldCode: 'stockAdmin', // 物料号 品项编码 itemName code-name
    fieldName: i18n.t('仓管员')
  },
  {
    fieldCode: 'orderCode', // 关联采购订单 采购订单号
    fieldName: i18n.t('关联采购订单')
  },
  {
    fieldCode: 'requiredDeliveryDate', // 需求日期
    fieldName: i18n.t('需求日期'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'quantity', // 需求数量
    fieldName: i18n.t('需求数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'remainingQty', // 剩余可创建数量
    fieldName: i18n.t('剩余可创建数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'receiveQty', // 收货数量
    fieldName: i18n.t('收货数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'deliveryQty', // 在途数量
    fieldName: i18n.t('在途数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'warehouseName', // 交货库存地点 库存地点名称 warehouseCode
    fieldName: i18n.t('库存地点编号+库存地点名称'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'stockQty', // 库存量
    fieldName: i18n.t('库存量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'transferPlanName', // 计划员
    fieldName: i18n.t('计划员'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'supplierCode', // 供应商编码供应商编码 supplierName code+name
    fieldName: i18n.t('供应商')
  },
  {
    fieldCode: 'planGroupName', // 计划组
    fieldName: i18n.t('计划组'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'buyerOrgName', // 采购组
    fieldName: i18n.t('采购组'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'projectTextBatch', // 项目文本批次
    fieldName: i18n.t('项目文本批次'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'outsourcedType', // 委外方式
    fieldName: i18n.t('委外方式')
  },
  {
    fieldCode: 'deliveryMethod', // 是否直送 配送方式 0直送1非直送
    fieldName: i18n.t('是否直送'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'associatedNumber', // 关联工单号
    fieldName: i18n.t('关联工单号'),
    allowFiltering: false
  },
  {
    fieldCode: 'domesticDemandFlag',
    fieldName: i18n.t('是否内需跟单'),
    width: '160',
    valueConverter: {
      type: 'map',
      map: { E: i18n.t('是'), F: i18n.t('否') }
    }
  },
  {
    fieldCode: 'saleOrder',
    fieldName: i18n.t('销售订单号'),
    width: '150'
  },
  {
    fieldCode: 'saleOrderRowCode',
    fieldName: i18n.t('销售订单行号'),
    width: '150'
  },
  {
    fieldCode: 'domesticDemandCode',
    fieldName: i18n.t('内需单号'),
    width: '150'
  },
  {
    fieldCode: 'serialNumber', // 序列号
    fieldName: i18n.t('序列号')
  },
  {
    fieldCode: 'bom', // BOM号
    fieldName: i18n.t('BOM号'),
    allowFiltering: false,
    ignore: true
  },
  // {
  //   fieldCode: 'saleOrderNo', // 关联销售订单号
  //   fieldName: i18n.t('关联销售订单号'),
  //   allowFiltering: false,
  //   ignore: true
  // },
  {
    fieldCode: 'processName', // 工序名称
    fieldName: i18n.t('工序名称'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'productCode', // 工序产品代码
    fieldName: i18n.t('工序产品代码'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'picNo', // 图号
    fieldName: i18n.t('图号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'orderLineNo', // 关联采购订单行号 行号
    fieldName: i18n.t('关联采购订单行号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'processorName', // 加工商
    fieldName: i18n.t('加工商')
  },
  {
    fieldCode: 'processorCode', // 加工商编码
    fieldName: i18n.t('加工商编码')
  },
  {
    fieldCode: 'systemRemark', // 系统备注
    fieldName: i18n.t('系统备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'buyerRemark', // 采购方备注
    fieldName: i18n.t('采购方备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'createUserName', // 创建人
    fieldName: i18n.t('创建人')
  },
  {
    fieldCode: 'supplierRemark', // 供应商备注
    fieldName: i18n.t('供应商备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'jit', // 是否JIT 	是否jit 0否1是
    fieldName: i18n.t('是否JIT'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'uniqueCode',
    fieldName: i18n.t('大数据平台唯一编号'),
    width: 200
  },
  {
    fieldCode: 'versionNo',
    fieldName: i18n.t('版本号')
  },
  {
    fieldCode: 'remarkExplain',
    fieldName: i18n.t('大数据平台备注')
  },
  {
    fieldCode: 'origSupplierCode',
    fieldName: i18n.t('上版供应商编号')
  },
  {
    fieldCode: 'origSupplierName',
    fieldName: i18n.t('上版供应商名称')
  },
  {
    fieldCode: 'planDeliveryDate',
    fieldName: i18n.t('上版交货日期'),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    fieldCode: 'planDeliveryTime',
    fieldName: i18n.t('上版交货时间'),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    fieldCode: 'callMaterialQty',
    fieldName: i18n.t('上版叫料数量'),
    allowFiltering: false
  },
  {
    fieldCode: 'supplierCheckUser', // 供应商确认人
    fieldName: i18n.t('供应商确认人'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'checkDate', // 确认日期
    fieldName: i18n.t('确认日期'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'productLine', // 生产线
    fieldName: i18n.t('生产线'),
    allowFiltering: false
  },
  {
    fieldCode: 'batch', // 批量
    fieldName: i18n.t('批量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'senderPhone', // senderPhone+senderAddress
    fieldName: i18n.t('送货联系电话+送货地址'),
    allowFiltering: false,
    ignore: true,
    template: () => ({ template: inputView })
  }
  // {
  //   fieldCode: "senderAddress", // 送货地址
  //   fieldName: i18n.t("送货地址"),
  // },
]

// tab3 JIT 表格列数据
export const ColumnDataTab3 = [
  {
    fieldCode: 'companyCode', // 公司 companyName code-name
    fieldName: i18n.t('公司')
  },
  {
    fieldCode: 'siteCode', // 工厂 siteName code-name
    fieldName: i18n.t('工厂')
  },
  {
    fieldCode: 'itemCode', // 物料号 品项编码 itemName code-name
    fieldName: i18n.t('物料')
  },
  {
    fieldCode: 'rowCode', // 物料号 品项编码 itemName code-name
    fieldName: i18n.t('JIT编号')
  },
  {
    fieldCode: 'orderCode', // 关联采购订单
    fieldName: i18n.t('关联采购订单')
  },
  // {
  //   fieldCode: "", // 关联采购订单行号 API 没有这个字段
  //   fieldName: i18n.t("关联采购订单行号"),
  // },
  {
    fieldCode: 'requiredDeliveryDate', // 需求日期
    fieldName: i18n.t('需求日期'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'quantity', // 需求数量
    fieldName: i18n.t('需求数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'remainingQty', // 剩余可创建数量
    fieldName: i18n.t('剩余可创建数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'receiveQty', // 收货数量
    fieldName: i18n.t('收货数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'deliveryQty', // 在途数量
    fieldName: i18n.t('在途数量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'warehouseName', // 交货库存地点 库存地点名称 warehouseCode
    fieldName: i18n.t('库存地点编号+库存地点名称'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'transferPlanName', // 计划员
    fieldName: i18n.t('计划员'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'supplierCode', // 供应商编码 supplierName code+name
    fieldName: i18n.t('供应商')
  },
  {
    fieldCode: 'planGroupName', // 计划组
    fieldName: i18n.t('计划组'),
    allowFiltering: false,
    ignore: true
    // planGroupCode
  },
  {
    fieldCode: 'buyerOrgName', // 采购组
    fieldName: i18n.t('采购组'),
    allowFiltering: false,
    ignore: true
    // buyerOrgCode
  },
  {
    fieldCode: 'deliveryMethod', // 是否直送 配送方式 0直送1非直送
    fieldName: i18n.t('是否直送'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'associatedNumber', // 关联工单号
    fieldName: i18n.t('关联工单号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'bom', // BOM号
    fieldName: i18n.t('BOM号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'saleOrderRowCode', // 关联销售订单行号
    fieldName: i18n.t('关联销售订单行号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'saleOrder', // 关联销售订单
    fieldName: i18n.t('关联销售订单'),
    allowFiltering: false,
    ignore: true
  },
  // {
  //   fieldCode: "", // 工作中心 API 没有这个字段
  //   fieldName: i18n.t("工作中心"),
  // },
  {
    fieldCode: 'processName', // 工序名称
    fieldName: i18n.t('工序名称'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'productCode', // 产品代码
    fieldName: i18n.t('产品代码'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'picNo', // 图号
    fieldName: i18n.t('图号'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'systemRemark', // 系统备注
    fieldName: i18n.t('系统备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'buyerRemark', // 采购方备注
    fieldName: i18n.t('采购方备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'batchCode',
    fieldName: i18n.t('版次号'),
    allowFiltering: false
  },
  {
    fieldCode: 'createUserName', // 创建人
    fieldName: i18n.t('创建人')
  },
  {
    fieldCode: 'supplierRemark', // 供应商备注
    fieldName: i18n.t('供应商备注'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'isJit', // 是否JIT 	是否jit 0否1是
    fieldName: i18n.t('是否JIT'),
    allowFiltering: false
  },
  {
    fieldCode: 'supplierCheckUser', // 供应商确认人
    fieldName: i18n.t('供应商确认人'),
    allowFiltering: false,
    ignore: true
  },
  // {
  //   fieldCode: "", // 确认日期 API 没有这个字段
  //   fieldName: i18n.t("确认日期"),
  // },
  {
    fieldCode: 'productLine', // 生产线
    fieldName: i18n.t('生产线'),
    allowFiltering: false,
    searchOptions: {
      placeholder: i18n.t('多个生产线需要用空格隔开'),
      operator: 'in',
      serializeValue: (e) => {
        let obj = e.split(' ')
        //自定义搜索值，规则
        return obj
      },
      maxQueryValueLength: 10000
    }
  },
  {
    fieldCode: 'batch', // 批量
    fieldName: i18n.t('批量'),
    allowFiltering: false,
    ignore: true
  },
  {
    fieldCode: 'senderPhone', // senderPhone+senderAddress
    fieldName: i18n.t('送货联系电话+送货地址'),
    allowFiltering: false,
    ignore: true,
    template: () => ({ template: inputView })
  }
  // {
  //   fieldCode: "senderAddress", // 送货地址
  //   fieldName: i18n.t("送货地址"),
  // },
  // {
  //   fieldCode: "", // 库存量 API 没有这个字段
  //   fieldName: i18n.t("库存量"),
  // },
]
