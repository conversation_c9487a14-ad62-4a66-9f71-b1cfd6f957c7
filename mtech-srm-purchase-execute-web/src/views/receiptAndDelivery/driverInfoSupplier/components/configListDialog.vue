<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules">
      <mt-form-item prop="companyCode" :label="$t('客户公司名称')" class="">
        <debounce-filter-select
          v-model="formData.companyCode"
          :request="postCustomerPagedQuery"
          :data-source="companyOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'customerCode' }"
          :value-template="companyCodeValueTemplate"
          @change="companyCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="name" :label="$t('司机姓名')" class="">
        <mt-input
          v-model="formData.name"
          :show-clear-button="true"
          :disabled="false"
          :placeholder="$t('请输入')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="idCard" :label="$t('司机身份证号')" class="">
        <mt-input
          v-model="formData.idCard"
          :show-clear-button="true"
          :disabled="false"
          :placeholder="$t('请输入')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="contact" :label="$t('联系方式')" class="">
        <mt-input
          v-model="formData.contact"
          :show-clear-button="true"
          :disabled="false"
          :placeholder="$t('请输入')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="license" :label="$t('车牌号')" class="">
        <mt-input
          v-model="formData.license"
          :show-clear-button="true"
          :disabled="false"
          :placeholder="$t('请输入')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { DialogActionType, ConfigStatus } from '../config/constant'
import { RegExpMap } from '@/utils/constant'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'

export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    const { idCardReg, phoneNumReg, numberPlateReg } = RegExpMap
    const identityNumberValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入司机身份证号')))
      } else if (!idCardReg.test(value)) {
        callback(new Error(this.$t('身份证号格式错误')))
      } else {
        this.$refs.ruleForm.clearValidate(['idCard'])
        callback()
      }
    }
    const phoneNumberValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入司机手机号')))
      } else if (!phoneNumReg.test(value)) {
        callback(new Error(this.$t('请输入正确的手机号')))
      } else {
        this.$refs.ruleForm.clearValidate(['contact'])
        callback()
      }
    }
    const licenseValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入车牌')))
      } else if (!numberPlateReg.test(value)) {
        callback(new Error(this.$t('请输入正确的车牌号')))
      } else {
        this.$refs.ruleForm.clearValidate(['license'])
        callback()
      }
    }
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      companyOptions: [], // 公司 下列选项
      companyCodeValueTemplate: codeNameColumn({
        firstKey: 'customerCode',
        secondKey: 'customerName'
      }), // 公司
      dialogTitle: '',
      selectData: null, // 当前编辑的数据
      rules: {
        // 公司
        companyCode: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        // 司机姓名
        name: [
          {
            required: true,
            message: this.$t('请输入司机姓名'),
            trigger: 'blur'
          }
        ],
        // 司机身份证号
        idCard: [
          {
            required: true,
            validator: identityNumberValidator,
            trigger: 'blur'
          }
        ],
        // 车牌号
        license: [{ required: true, validator: licenseValidator, trigger: 'blur' }],
        // 司机手机号
        contact: [{ required: true, validator: phoneNumberValidator, trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      actionType: DialogActionType.Add, // 默认类型：新增
      formData: {
        id: '',

        companyName: '', // 公司
        companyId: '', // 公司id
        companyCode: '', // 公司编码

        status: '', // 配置状态
        name: '', // 司机姓名
        idCard: '', // 司机身份证号
        license: '', // 车牌号
        contact: '', // 司机手机号
        tenantId: '' // 租户id
      }
    }
  },
  mounted() {
    // 获取主数据-当前租户的公司列表
    this.postCustomerPagedQuery({})
  },

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, selectData } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.actionType = actionType // 弹框模式
      this.selectData = selectData // 行数据
      this.formData = this.initForm(selectData)
    },
    initForm(selectData) {
      let formData = null
      this.$refs.ruleForm.clearValidate()
      const { tenantId } = JSON.parse(sessionStorage.getItem('userInfo')) // 从 sessionStorage 获取当前用户信息
      if (this.actionType === DialogActionType.Add) {
        // 新增
        formData = {
          id: '',

          companyName: '', // 公司
          companyId: '', // 公司id
          companyCode: '', // 公司编码

          status: ConfigStatus.inactive, // 配置状态 默认 停用
          name: '', // 司机姓名
          idCard: '', // 司机身份证号
          license: '', // 车牌号
          contact: '', // 司机手机号
          tenantId // 租户id
        }
      } else if (this.actionType === DialogActionType.Edit) {
        // 编辑
        formData = {
          id: selectData.id,

          companyName: selectData.companyName, // 公司
          companyId: selectData.companyId, // 公司id
          companyCode: selectData.companyCode, // 公司编码

          status: selectData.status, // 配置状态
          name: selectData.name, // 司机姓名
          idCard: selectData.idCard, // 司机身份证号
          license: selectData.license, // 车牌号
          contact: selectData.contact, // 司机手机号
          tenantId // 租户id
        }
      }
      return formData
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 新增、编辑
          this.postSupplierDriverSave()
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 公司 change
    companyCodeChange(e) {
      this.formData.companyId = ''
      this.formData.companyCode = ''
      this.formData.companyName = ''
      if (e.value !== undefined && e.value !== null) {
        for (let i = 0; i < this.companyOptions.length; i++) {
          if (this.companyOptions[i].customerCode === e.value) {
            this.formData.companyId = this.companyOptions[i].id
            this.formData.companyCode = this.companyOptions[i].customerCode
            this.formData.companyName = this.companyOptions[i].customerName
            break
          }
        }
      }
    },
    // 保存司机
    postSupplierDriverSave() {
      const params = {
        id: this.formData.id || undefined, // 不传 id 就是新增
        // 公司
        companyCode: this.formData.companyCode,
        companyId: this.formData.companyId,
        companyName: this.formData.companyName,

        status: this.formData.status, // 配置状态
        name: this.formData.name, // 司机姓名
        idCard: this.formData.idCard, // 司机身份证号
        license: this.formData.license, // 车牌号
        contact: this.formData.contact, // 司机手机号
        tenantId: this.formData.tenantId // 租户id
      }
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDriverSave(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm')
            this.handleClose()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 获取主数据-客户列表
    postCustomerPagedQuery(e) {
      const { text, updateData } = e
      // const page = { page: { current: 1, size: maxPageSize } }
      // let rules = {}
      // if (text) {
      //   rules = {
      //     condition: 'or',
      //     defaultRules: [
      //       {
      //         field: 'customerName', // key
      //         operator: 'contains', // 包含
      //         value: text // value
      //       },
      //       {
      //         field: 'customerCode', // key 客户代码
      //         operator: 'contains', // 包含
      //         value: text // value
      //       }
      //     ]
      //   }
      // }
      this.$API.masterData
        .postCustomerNewPagedQuery({
          dictCode: 'KTComCode',
          tenantId: '10000',
          fuzzyName: text
        })
        .then((res) => {
          const list = res?.data || []
          list.forEach((item) => {
            item.customerCode = item.orgCode
            item.customerName = item.orgName
          })
          this.companyOptions = addCodeNameKeyInList({
            firstKey: 'customerCode',
            secondKey: 'customerName',
            list
          })
          this.$nextTick(() => {
            if (updateData && typeof updateData === 'function') {
              updateData(this.companyOptions)
            }
          })
        })
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped></style>
