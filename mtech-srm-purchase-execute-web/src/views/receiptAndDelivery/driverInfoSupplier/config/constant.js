import { i18n } from '@/main.js'

// 序号 表格行 按钮
export const SerialNumberCellTools = [
  {
    id: 'ConfigEdit',
    icon: 'icon_list_edit',
    permission: ['O_02_0656', 'O_02_1656', 'O_02_1663'],
    title: i18n.t('编辑'),
    visibleCondition: (data) => data.status == ConfigStatus.inactive // 停用
  },
  {
    id: 'ConfigDelete',
    icon: 'icon_solid_Delete',
    permission: ['O_02_0651', 'O_02_1651', 'O_02_1658'],
    title: i18n.t('删除'),
    visibleCondition: (data) => data.status == ConfigStatus.inactive // 停用
  }
]

// 状态 表格行 按钮
export const StatusCellTools = [
  {
    id: 'ConfigInactive',
    icon: 'icon_list_disable',
    permission: ['O_02_0653', 'O_02_1653', 'O_02_1660'],
    title: i18n.t('停用'),
    visibleCondition: (data) => data.status == ConfigStatus.active // 启用
  },
  {
    id: 'ConfigActive',
    icon: 'icon_list_enable',
    permission: ['O_02_0652', 'O_02_1652', 'O_02_1659'],
    title: i18n.t('启用'),
    visibleCondition: (data) => data.status == ConfigStatus.inactive // 停用
  }
]

// Toolbar 按钮
export const Toolbar = [
  {
    id: 'ConfigAdd',
    icon: 'icon_solid_Createproject',
    permission: ['O_02_0650', 'O_02_1650', 'O_02_1657'],
    title: i18n.t('新增')
  },
  {
    id: 'ConfigDelete',
    icon: 'icon_table_delete',
    permission: ['O_02_0651', 'O_02_1651', 'O_02_1658'],
    title: i18n.t('删除')
  },
  {
    id: 'ConfigActive',
    icon: 'icon_solid_Activateorder',
    permission: ['O_02_0652', 'O_02_1652', 'O_02_1659'],
    title: i18n.t('启用')
  },
  {
    id: 'ConfigInactive',
    icon: 'icon_solid_Pauseorder',
    permission: ['O_02_0653', 'O_02_1653', 'O_02_1660'],
    title: i18n.t('停用')
  },
  {
    id: 'ConfigImport',
    icon: 'icon_solid_Import',
    permission: ['O_02_0654', 'O_02_1654', 'O_02_1661'],
    title: i18n.t('导入')
  },
  {
    id: 'ConfigExport',
    icon: 'icon_solid_export',
    permission: ['O_02_0655', 'O_02_1655', 'O_02_1662'],
    title: i18n.t('导出')
  }
]

// 表格列数据
export const ColumnData = [
  {
    fieldCode: 'serialNumber', // 前端定义
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'status', // 停用/启用 启用状态:1-未启用,2-启用
    fieldName: i18n.t('状态')
  },
  {
    fieldCode: 'companyName', // 客户公司名称
    fieldName: i18n.t('客户公司名称')
    // companyId 客户公司名称id
    // companyCode 客户公司编码
  },
  {
    fieldCode: 'name', // 司机姓名
    fieldName: i18n.t('司机姓名')
  },
  {
    fieldCode: 'idCard', // 司机身份证号
    fieldName: i18n.t('司机身份证号')
  },
  {
    fieldCode: 'contact', // 联系方式
    fieldName: i18n.t('联系方式')
  },
  {
    fieldCode: 'license', // 车牌号
    fieldName: i18n.t('车牌号')
  }
]

// 弹出框类型
export const DialogActionType = {
  Add: 0, // 新增
  Edit: 1 // 编辑
}

// 配置状态 1:启用 2:停用
export const ConfigStatus = {
  active: 1, // 启用
  inactive: 2 // 停用
}
// 配置状态 1:启用 2:停用
export const ConfigStatusConst = {
  [ConfigStatus.active]: i18n.t('启用'),
  [ConfigStatus.inactive]: i18n.t('停用')
}
// 配置状态 对应的 css class
export const ConfigStatusCssClass = {
  [ConfigStatus.active]: 'col-active', // 启用
  [ConfigStatus.inactive]: 'col-inactive' // 停用
}
// 配置状态 Options
export const ConfigStatusOptions = [
  {
    // 启用
    text: ConfigStatusConst[ConfigStatus.active],
    value: ConfigStatus.active,
    cssClass: ConfigStatusCssClass[ConfigStatus.active]
  },
  {
    // 停用
    text: ConfigStatusConst[ConfigStatus.inactive],
    value: ConfigStatus.inactive,
    cssClass: ConfigStatusCssClass[ConfigStatus.inactive]
  }
]
