import { ConfigStatusOptions, SerialNumberCellTools, StatusCellTools } from './constant'

// 格式化表格动态数据
export const formatTableColumnData = (data) => {
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: 'auto' // 自适应
    }
    if (col.fieldCode === 'serialNumber') {
      // 序号
      defaultCol.cssClass = '' // 序号不可点击
      defaultCol.cellTools = SerialNumberCellTools
      defaultCol.ignore = true
    } else if (col.fieldCode === 'status') {
      // 配置状态
      defaultCol.valueConverter = {
        type: 'map',
        map: ConfigStatusOptions
      }
      defaultCol.cellTools = StatusCellTools
    }
    colData.push(defaultCol)
  })

  return colData
}

// 表格数据转换
export const serializeList = (list) => {
  if (list.length > 0) {
    let serialNumber = 1
    list.forEach((item) => {
      // 添加序号
      item.serialNumber = serialNumber++
    })
  }
  return list
}
