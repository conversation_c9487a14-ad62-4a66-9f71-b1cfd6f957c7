import { i18n } from '@/main.js'
import { TabIndex } from '../../supplyPlanSupplier/config/constant.js'
import Vue from 'vue'

// Toolbar 按钮
export const Toolbar = {
  // 订单供货计划 跳转过来时
  [TabIndex.purchaseOrder]: [
    // 左
    [
      {
        id: 'CreateDelivery',
        icon: 'icon_table_submit',
        title: i18n.t('提交创建送货单')
      }
      // {
      //   id: "TableImport",
      //   icon: "icon_solid_Import",
      //   title: i18n.t("导入"),
      // },
    ],
    // 右
    []
  ],
  // 交货计划 跳转过来时
  [TabIndex.deliverySchedule]: [
    // 左
    [
      {
        id: 'CreateDelivery',
        icon: 'icon_table_submit',
        title: i18n.t('提交创建送货单')
      },
      {
        id: 'Rematch',
        icon: 'icon_table_matching',
        title: i18n.t('重新匹配')
      },
      {
        id: 'BatchUpdate',
        title: i18n.t('批量修改')
      }
      // {
      //   id: "TableImport",
      //   icon: "icon_solid_Import",
      //   title: i18n.t("导入"),
      // },
    ],
    // 右
    []
  ],
  // JIT供货计划 跳转过来时
  [TabIndex.jit]: [
    // 左
    [
      {
        id: 'CreateDelivery',
        icon: 'icon_table_submit',
        title: i18n.t('提交创建送货单')
      },
      {
        id: 'Rematch',
        icon: 'icon_table_matching',
        title: i18n.t('重新匹配')
      },
      {
        id: 'BatchUpdate',
        title: i18n.t('批量修改')
      }
      // {
      //   id: "TableImport",
      //   icon: "icon_solid_Import",
      //   title: i18n.t("导入"),
      // },
    ],
    // 右
    []
  ]
}

// 可创建送货单
export const CreateMark = {
  true: true, // 可创建
  false: false // 不可创建
}
// 可创建送货单
export const CreateMarkText = {
  [CreateMark.true]: i18n.t('可创建'),
  [CreateMark.false]: i18n.t('不可创建')
}
// 可创建送货单 对应的 css class
export const CreateMarkCssClass = {
  [CreateMark.true]: 'col-active', // 可创建
  [CreateMark.false]: 'col-inactive' // 不可创建
}

// 订单供货计划
export const ColumnDataPurchaseOrder = [
  {
    fieldCode: 'createMark', // 可创建送货单
    fieldName: i18n.t('可创建送货单')
  },
  {
    fieldCode: 'denyReason', // 无法创建原因
    fieldName: i18n.t('无法创建原因')
  },
  {
    fieldCode: 'orderCode', // 关联采购订单 订单号
    fieldName: i18n.t('关联采购订单')
  },
  // {
  //   fieldCode: "itemNo", // 关联采购订单行号 行号
  //   fieldName: i18n.t("关联采购订单行号"),
  // },
  {
    fieldCode: 'siteName', // 工厂
    fieldName: i18n.t('工厂')
  },
  {
    fieldCode: 'itemName', // 物料名称 品项名称
    fieldName: i18n.t('物料名称')
  },
  {
    fieldCode: 'itemCode', // 物料号 品项编码
    fieldName: i18n.t('物料号')
  },
  {
    fieldCode: 'companyName', // 公司
    fieldName: i18n.t('公司')
  },
  {
    fieldCode: 'supplierName', // 供应商
    fieldName: i18n.t('供应商')
  },
  {
    fieldCode: 'buyerOrgName', // 采购组
    fieldName: i18n.t('采购组')
  },
  // {
  //   fieldCode: "", // 关联销售订单号 API没有这个字段
  //   fieldName: i18n.t("关联销售订单号"),
  // },
  // {
  //   fieldCode: "", // 关联销售订单行号 API没有这个字段
  //   fieldName: i18n.t("关联销售订单行号"),
  // },

  {
    fieldCode: 'consignee', // 收货人 (原型没有，API返回的字段)
    fieldName: i18n.t('收货人')
  },
  {
    fieldCode: 'contact', // 联系电话 联系方式
    fieldName: i18n.t('联系电话')
  },

  {
    fieldCode: 'receiveSiteName', // 收货工厂/地点名称 (原型没有，API返回的字段)
    fieldName: i18n.t('收货工厂')
  },
  {
    fieldCode: 'receiveAddress', // 收货地址 (原型没有，API返回的字段)
    fieldName: i18n.t('收货地址')
  },
  {
    fieldCode: 'buyerRemark', // 采购方备注
    fieldName: i18n.t('采购方备注')
  },
  {
    fieldCode: 'supRemark', // 供应商备注
    fieldName: i18n.t('供应商备注')
  }
]
// 交货计划
export const ColumnDataDeliverySchedule = [
  {
    fieldCode: 'buyerOrgName', // 采购组
    fieldName: i18n.t('采购组')
  },
  {
    fieldCode: 'denyReason', // 无法创建原因
    fieldName: i18n.t('无法创建原因')
  },
  {
    fieldCode: 'itemCode', // 物料号 品项编码
    fieldName: i18n.t('物料号')
  },
  {
    fieldCode: 'itemName', // 物料名称 品项名称
    fieldName: i18n.t('物料名称')
  },
  {
    fieldCode: 'supplierNum', // 需求数量 订单数量
    fieldName: i18n.t('需求数量')
  },
  {
    fieldCode: 'presentDeliveryNum',
    fieldName: i18n.t('本次送货数量')
  },
  {
    fieldCode: 'deliveryQty', // 已发货数量 (原型没有，API返回的字段)
    fieldName: i18n.t('已发货数量')
  },
  {
    fieldCode: 'receiveQty', // 收货数量 已入库数量
    fieldName: i18n.t('收货数量')
  },
  {
    fieldCode: 'preDeliveryQty', // 待发货数量 (原型没有，API返回的字段)
    fieldName: i18n.t('待发货数量')
  },
  {
    fieldCode: 'supplierName', // 供应商
    fieldName: i18n.t('供应商')
  },
  {
    fieldCode: 'createMark', // 可创建送货单
    fieldName: i18n.t('可创建送货单')
  },
  {
    fieldCode: 'totalPresentDeliveryNum', // 可创建送货单
    fieldName: i18n.t('合计')
  },
  {
    fieldCode: 'orderCode', // 关联采购订单 订单号
    fieldName: i18n.t('关联采购订单')
  },
  // {
  //   fieldCode: "itemNo", // 关联采购订单行号 行号
  //   fieldName: i18n.t("关联采购订单行号"),
  // },
  {
    fieldCode: 'siteName', // 工厂
    fieldName: i18n.t('工厂')
  },
  {
    fieldCode: 'itemName', // 物料名称
    fieldName: i18n.t('物料名称')
  },
  {
    fieldCode: 'itemCode', // 物料号 品项编码
    fieldName: i18n.t('物料号')
  },
  {
    fieldCode: 'companyName', // 公司
    fieldName: i18n.t('公司')
  },
  {
    fieldCode: 'requiredDeliveryDate', // 需求日期 要求交期
    fieldName: i18n.t('需求日期')
  },
  {
    fieldCode: 'warehouse', // 交货库存地点 库存地点
    fieldName: i18n.t('交货库存地点')
  },
  {
    fieldCode: 'domesticDemandFlag',
    fieldName: i18n.t('是否内需跟单'),
    width: '160',
    valueConverter: {
      type: 'map',
      map: { E: i18n.t('是'), F: i18n.t('否') }
    }
  },
  {
    fieldCode: 'saleOrder',
    fieldName: i18n.t('销售订单号')
  },
  {
    fieldCode: 'saleOrderRowCode',
    fieldName: i18n.t('销售订单行号'),
    width: '150'
  },
  {
    fieldCode: 'domesticDemandCode',
    fieldName: i18n.t('内需单号'),
    width: '150'
  },
  {
    fieldCode: 'outboundWarehouseId',
    fieldName: i18n.t('出库货位'),
    template: () => {
      return {
        template: Vue.component('outboundWarehouseSelect', {
          template: `
            <mt-select
              v-model="data.outboundWarehouseId"
              :data-source="outboundWarehouseOptions"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
              :fields="{ text: 'theCodeName', value: 'value' }"
              @change="handleChange"
            />
          `,
          data: function () {
            return { data: {}, outboundWarehouseOptions: [] }
          },
          mounted() {
            this.getOptions()
          },
          methods: {
            getOptions() {
              this.$API.masterData.getOutboundWarehouseListApi().then((res) => {
                if (res.code === 200) {
                  this.$parent.$emit('cellEdit', {
                    key: 'isRequired',
                    value: res.data.required
                  })
                  this.outboundWarehouseOptions = res.data.itemList?.map((item) => {
                    return {
                      theCodeName: item.itemCode + '-' + item.itemName,
                      text: item.itemName,
                      value: item.id,
                      outboundWarehouseCode: item.itemCode,
                      id: item.id
                    }
                  })
                }
              })
            },
            handleChange(e) {
              const { itemData } = e
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'outboundWarehouseCode',
                item: {
                  text: itemData?.text || null,
                  value: itemData?.outboundWarehouseCode || null,
                  id: itemData?.value || null
                }
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'buyerRemark', // 采购方备注
    fieldName: i18n.t('采购方备注')
  },
  {
    fieldCode: 'supRemark', // 供应商备注
    fieldName: i18n.t('供应商备注')
  },
  {
    fieldCode: 'consignee', // 收货人 (原型没有，API返回的字段)
    fieldName: i18n.t('收货人')
  },
  {
    fieldCode: 'contact', // 联系电话 联系方式
    fieldName: i18n.t('联系电话')
  },
  {
    fieldCode: 'receiveAddress', // 收货地址 (原型没有，API返回的字段)
    fieldName: i18n.t('收货地址')
  },
  {
    fieldCode: 'receiveSiteName', // 收货工厂/地点名称 (原型没有，API返回的字段)
    fieldName: i18n.t('收货工厂')
  },
  {
    fieldCode: 'remark',

    fieldName: i18n.t('备注'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<mt-input v-model="data.remark"  @blur="notChange"></mt-input>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {
            notChange(e) {
              console.log(e)

              // if (this.data.rejectReason !== e) {}
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'remark',
                value: this.data.remark
              })
            }
          }
        })
      }
    }
  }
]
// JIT供货计划
export const ColumnDataJit = [
  {
    fieldCode: 'siteName', // 工厂
    fieldName: i18n.t('工厂')
  },
  {
    fieldCode: 'denyReason', // 无法创建原因
    fieldName: i18n.t('无法创建原因')
  },
  {
    fieldCode: 'buyerOrgName', // 采购组
    fieldName: i18n.t('采购组')
  },
  {
    fieldCode: 'itemCode', // 物料号 品项编码
    fieldName: i18n.t('物料号')
  },
  {
    fieldCode: 'itemName', // 物料名称 品项名称
    fieldName: i18n.t('物料名称')
  },
  {
    fieldCode: 'supplierNum', // 需求数量 订单数量
    fieldName: i18n.t('需求数量')
  },
  {
    fieldCode: 'presentDeliveryNum',
    fieldName: i18n.t('本次送货数量')
  },
  {
    fieldCode: 'totalPresentDeliveryNum', // 可创建送货单
    fieldName: i18n.t('合计')
  },
  {
    fieldCode: 'deliveryQty', // 已发货数量 (原型没有，API返回的字段)
    fieldName: i18n.t('已发货数量')
  },
  {
    fieldCode: 'receiveQty', // 收货数量 已入库数量
    fieldName: i18n.t('收货数量')
  },
  {
    fieldCode: 'preDeliveryQty', // 待发货数量 (原型没有，API返回的字段)
    fieldName: i18n.t('待发货数量')
  },
  {
    fieldCode: 'supplierName', // 供应商
    fieldName: i18n.t('供应商')
  },
  {
    fieldCode: 'createMark', // 可创建送货单
    fieldName: i18n.t('可创建送货单')
  },

  {
    fieldCode: 'orderCode', // 关联采购订单 订单号
    fieldName: i18n.t('关联采购订单')
  },
  // {
  //   fieldCode: "itemNo", // 关联采购订单行号 行号
  //   fieldName: i18n.t("关联采购订单行号"),
  // },
  {
    fieldCode: 'siteName', // 工厂
    fieldName: i18n.t('工厂')
  },
  {
    fieldCode: 'itemName', // 物料名称
    fieldName: i18n.t('物料名称')
  },
  {
    fieldCode: 'itemCode', // 物料号 品项编码
    fieldName: i18n.t('物料号')
  },
  {
    fieldCode: 'companyName', // 公司
    fieldName: i18n.t('公司')
  },
  {
    fieldCode: 'supplierName', // 供应商
    fieldName: i18n.t('供应商')
  },
  {
    fieldCode: 'buyerOrgName', // 采购组
    fieldName: i18n.t('采购组')
  },
  {
    fieldCode: 'requiredDeliveryDate', // 需求日期 要求交期
    fieldName: i18n.t('需求日期')
  },
  {
    fieldCode: 'warehouse', // 交货库存地点 库存地点
    fieldName: i18n.t('交货库存地点')
  },
  {
    fieldCode: 'outboundWarehouseId',
    fieldName: i18n.t('出库货位'),
    template: () => {
      return {
        template: Vue.component('outboundWarehouseSelect', {
          template: `
            <mt-select
              v-model="data.outboundWarehouseId"
              :data-source="outboundWarehouseOptions"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
              :fields="{ text: 'theCodeName', value: 'value' }"
              @change="handleChange"
            />
          `,
          data: function () {
            return { data: {}, outboundWarehouseOptions: [] }
          },
          mounted() {
            this.getOptions()
          },
          methods: {
            getOptions() {
              this.$API.masterData.getOutboundWarehouseListApi().then((res) => {
                if (res.code === 200) {
                  this.$parent.$emit('cellEdit', {
                    key: 'isRequired',
                    value: res.data.required
                  })
                  this.outboundWarehouseOptions = res.data.itemList?.map((item) => {
                    return {
                      theCodeName: item.itemCode + '-' + item.itemName,
                      text: item.itemName,
                      value: item.id,
                      outboundWarehouseCode: item.itemCode,
                      id: item.id
                    }
                  })
                }
              })
            },
            handleChange(e) {
              const { itemData } = e
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'outboundWarehouseCode',
                item: {
                  text: itemData?.text || null,
                  value: itemData?.outboundWarehouseCode || null,
                  id: itemData?.value || null
                }
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'buyerRemark', // 采购方备注
    fieldName: i18n.t('采购方备注')
  },
  {
    fieldCode: 'supRemark', // 供应商备注
    fieldName: i18n.t('供应商备注')
  },
  {
    fieldCode: 'consignee', // 收货人 (原型没有，API返回的字段)
    fieldName: i18n.t('收货人')
  },
  {
    fieldCode: 'contact', // 联系电话 联系方式
    fieldName: i18n.t('联系电话')
  },
  {
    fieldCode: 'receiveAddress', // 收货地址 (原型没有，API返回的字段)
    fieldName: i18n.t('收货地址')
  },
  {
    fieldCode: 'receiveSiteName', // 收货工厂/地点名称 (原型没有，API返回的字段)
    fieldName: i18n.t('收货工厂')
  },

  {
    fieldCode: 'buyerRemark', // 采购方备注
    fieldName: i18n.t('采购方备注')
  },
  {
    fieldCode: 'supRemark', // 供应商备注
    fieldName: i18n.t('供应商备注')
  },
  {
    fieldCode: 'remark',
    fieldName: i18n.t('备注'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<mt-input v-model="data.remark"  @blur="notChange"></mt-input>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {
            notChange(e) {
              console.log(e)

              // if (this.data.rejectReason !== e) {}
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'remark',
                value: this.data.remark
              })
            }
          }
        })
      }
    }
  }
]
