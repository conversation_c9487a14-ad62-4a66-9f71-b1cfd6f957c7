<!-- 批量修改弹框 -->
<template>
  <mt-dialog
    ref="dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    width="650px"
    height="400px"
  >
    <div style="padding-top: 1rem">
      <mt-form ref="modelForm" :model="modelForm" :rules="rules">
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item prop="outboundWarehouseId" :label="$t('出库货位')">
              <mt-select
                v-model="modelForm.outboundWarehouseId"
                :data-source="outboundWarehouseOptions"
                :fields="{ text: 'theCodeName', value: 'value' }"
                :placeholder="$t('请选择，空代表不修改')"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                @change="outboundWarehouseChange"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="24">
            <mt-form-item prop="remark" :label="$t('送货单行备注')">
              <mt-input
                v-model="modelForm.remark"
                show-clear-button
                :placeholder="$t('请输入，空代表不修改')"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      modelForm: {},
      rules: {},

      outboundWarehouseOptions: []
    }
  },
  methods: {
    dialogInit(args) {
      const { title } = args
      this.getOutboundWarehouseList()
      this.dialogTitle = title
      this.$refs.dialog.ejsRef.show()
    },
    getOutboundWarehouseList() {
      this.$API.masterData.getOutboundWarehouseListApi().then((res) => {
        if (res.code === 200) {
          this.outboundWarehouseOptions = res.data.itemList?.map((item) => {
            return {
              theCodeName: item.itemCode + '-' + item.itemName,
              text: item.itemName,
              value: item.id,
              outboundWarehouseCode: item.itemCode,
              id: item.id
            }
          })
        }
      })
    },
    outboundWarehouseChange(e) {
      const { itemData } = e
      this.modelForm.outboundWarehouseName = itemData?.text || null
      this.modelForm.outboundWarehouseCode = itemData?.outboundWarehouseCode || null
      this.modelForm.outboundWarehouseId = itemData?.id || null
    },
    onOpen(args) {
      args.preventFocus = true
    },
    confirm() {
      this.$emit('confirm', this.modelForm)
      this.handleClose()
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>
