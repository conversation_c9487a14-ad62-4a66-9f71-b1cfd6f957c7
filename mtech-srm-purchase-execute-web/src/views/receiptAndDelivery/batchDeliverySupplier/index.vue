<template>
  <!-- 批量创建送货单-供方 -->
  <div class="full-height vertical-flex-box">
    <div class="top-info flex-keep">
      <div class="header-box">
        <div class="middle-blank"></div>
        <!-- 右侧操作按钮 -->
        <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{
          $t('返回')
        }}</mt-button>
      </div>
      <div class="title">
        <span>{{ $t('批量创建送货单') }}</span>
      </div>
    </div>
    <!-- 列模板 -->
    <div class="flex-fit">
      <mt-template-page
        id="supply-plan-supplier-table-container"
        ref="templateRef"
        class="grid-wrap-not-paging"
        :template-config="componentConfig"
        @handleClickToolBar="handleClickToolBar"
        @cellEdit="cellEdit"
      >
      </mt-template-page>
    </div>
    <BatchUpdateDialog ref="batchUpdateDialogRef" @confirm="batchUpdateConfirm" />
  </div>
</template>

<script>
import { gridDataSource } from './config/variable'
import { formatTableColumnData, verifyCreateMark } from './config/index'
import {
  Toolbar,
  ColumnDataDeliverySchedule,
  ColumnDataPurchaseOrder,
  ColumnDataJit
} from './config/constant'
import { TabIndex } from '../supplyPlanSupplier/config/constant'

export default {
  components: {
    BatchUpdateDialog: () => import('./components/BatchUpdateDialog')
  },
  data() {
    // 预创建订单数据
    const batchDeliverySupplierData =
      JSON.parse(localStorage.getItem('batchDeliverySupplierData')) || []
    // 预创建订单请求参数
    const batchDeliverySupplierParams = JSON.parse(
      localStorage.getItem('batchDeliverySupplierParams')
    )
    const lastTabIndex = JSON.parse(localStorage.getItem('lastTabIndex'))

    // 将预创建订单数据 设置到 表格中
    gridDataSource.length = 0
    batchDeliverySupplierData.forEach((item) => {
      item.outboundWarehouseName = ''
      item.outboundWarehouseCode = ''
      item.remark = ''
      gridDataSource.push(item)
    })

    const { type: pageType } = this.$route.query // 页面类型
    let column = []
    if (pageType == TabIndex.deliverySchedule) {
      // 交货计划
      column = ColumnDataDeliverySchedule
    } else if (pageType == TabIndex.purchaseOrder) {
      // 采购订单
      column = ColumnDataPurchaseOrder
    } else if (pageType == TabIndex.jit) {
      // jit
      column = ColumnDataJit
    }

    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      lastTabIndex, // 前一页面的 Tab index
      pageType, // 页面类型
      batchDeliverySupplierParams, // 预创建订单请求参数
      componentConfig: [
        {
          toolbar: Toolbar[pageType] || [],
          useToolTemplate: false, // 此项不使用预置的表格操作按钮
          useBaseConfig: false, // 此项不使用预置的表格操作按钮
          grid: {
            allowPaging: false, // 不分页
            lineSelection: 0, // 选项列
            lineIndex: 1, // 序号列
            frozenColumns: 1,
            columnData: formatTableColumnData({
              data: column
            }),
            dataSource: gridDataSource
          }
        }
      ],
      isRequired: false,
      selectRowIndexList: []
    }
  },
  mounted() {},
  beforeDestroy() {
    // // 预创建订单数据
    // localStorage.removeItem("batchDeliverySupplierData");
    // // 预创建订单请求参数
    // localStorage.removeItem("batchDeliverySupplierParams");
    // localStorage.removeItem("lastTabIndex");
  },
  methods: {
    // 行编辑
    cellEdit(e) {
      const { key } = e
      if (key === 'isRequired') {
        this.isRequired = e.value
      }
      if (key === 'outboundWarehouseCode') {
        gridDataSource[e.index].outboundWarehouseName = e.item?.text
        gridDataSource[e.index].outboundWarehouseCode = e.item?.value
        gridDataSource[e.index].outboundWarehouseId = e.item?.id
      }
      if (key === 'remark') {
        gridDataSource[e.index].remark = e.value
      }
    },
    // ToolBar
    handleClickToolBar(args) {
      const { toolbar, grid } = args
      const selectRows = grid.getSelectedRecords()
      const selectRowIndexList = grid.getSelectedRowIndexes()
      this.selectRowIndexList = selectRowIndexList
      const commonToolbar = [
        'TableImport',
        'Rematch',
        'Filter',
        'Refresh',
        'refreshDataByLocal',
        'filterDataByLocal',
        'resetDataByLocal',
        'Setting'
      ]
      if (selectRows.length === 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedId = []
      selectRows.forEach((item) => selectedId.push(item.id))

      if (toolbar.id === 'TableImport') {
        // 导入
        this.handleImport()
      } else if (toolbar.id === 'CreateDelivery') {
        // 提交创建送货单
        if (this.isRequired && selectRows.some((v) => !v.outboundWarehouseCode)) {
          this.$toast({
            content: this.$t('请选择出库货位'),
            type: 'warning'
          })
          return
        }
        const { valid, createMark } = verifyCreateMark(selectRows)
        if (valid && createMark) {
          // 状态统一 && 都是可创建的状态
          this.handleCreateDelivery({ selectRows, selectRowIndexList })
        } else {
          this.$toast({
            content: this.$t('请选择可创建送货单的数据'),
            type: 'warning'
          })
        }
      } else if (toolbar.id === 'Rematch') {
        // 重新匹配
        this.handleRematch()
      } else if (toolbar.id === 'BatchUpdate') {
        this.handleBatchUpdate()
      }
    },
    // 导入
    handleImport() {
      // TODO
      this.$toast({ content: this.$t('开发中'), type: 'warning' })
      if (this.pageType == TabIndex.purchaseOrder) {
        // 采购订单-创建送货单
        // this.TODO(params);
      } else if (this.pageType == TabIndex.deliverySchedule) {
        // 交货计划-创建送货单
        // this.TODO(params);
      } else if (this.pageType == TabIndex.jit) {
        // JIT-创建送货单
        // this.TODO(params);
      }
    },
    // 重新匹配
    handleRematch() {
      if (this.pageType == TabIndex.deliverySchedule) {
        // 供方收发货供货计划-交货计划预创建送货单
        this.postSupplierDeliverySupplyPlanPlanPreCreate({
          params: this.batchDeliverySupplierParams
        })
      } else if (this.pageType == TabIndex.jit) {
        // 供方收发货供货计划-JIT预创建送货单
        this.postSupplierDeliverySupplyPlanJitPreCreate({
          params: this.batchDeliverySupplierParams
        })
      }
    },
    handleBatchUpdate() {
      this.$refs.batchUpdateDialogRef.dialogInit({
        title: this.$t('批量修改')
      })
    },
    batchUpdateConfirm(form) {
      this.selectRowIndexList.forEach((i) => {
        gridDataSource[i].outboundWarehouseName =
          form?.outboundWarehouseName || gridDataSource[i].outboundWarehouseName
        gridDataSource[i].outboundWarehouseCode =
          form?.outboundWarehouseCode || gridDataSource[i].outboundWarehouseCode
        gridDataSource[i].outboundWarehouseId =
          form?.outboundWarehouseId || gridDataSource[i].outboundWarehouseId
        gridDataSource[i].remark = form?.remark || gridDataSource[i].remark
      })
    },
    // 提交创建送货单
    handleCreateDelivery(args) {
      const { selectRows, selectRowIndexList } = args
      const params = []
      selectRows.forEach((item) => {
        params.push({
          id: item.id,
          num: item.presentDeliveryNum, // 此次发货数量
          supplierTenantId: item.supplierTenantId, // 供应商租户id
          remark: item.remark,
          outboundWarehouseName: item.outboundWarehouseName,
          outboundWarehouseCode: item.outboundWarehouseCode
        })
      })
      if (this.pageType == TabIndex.purchaseOrder) {
        // 采购订单-创建送货单
        this.postSupplierDeliverySupplyPlanOrderCreate({
          params,
          selectRowIndexList
        })
      } else if (this.pageType == TabIndex.deliverySchedule) {
        // 交货计划-创建送货单
        this.postSupplierDeliverySupplyPlanPlanCreate({
          params,
          selectRowIndexList
        })
      } else if (this.pageType == TabIndex.jit) {
        // JIT-创建送货单
        this.postSupplierDeliverySupplyPlanJitCreate({
          params,
          selectRowIndexList
        })
      }
    },
    // 处理 提交创建送货单 后的逻辑
    handleCreateDeliveryAfter(args) {
      const { response, selectRowIndexList } = args
      const num = response.data || 0 // 创建的送货单数量
      this.$toast({
        content: this.$t(`成功创建${num}张送货单`),
        type: 'success'
      })
      this.isRequired = false

      // 移除行数据，更新 localStorage
      this.updateLocalStorageData({ selectRowIndexList })
      // setTimeout(() => {
      this.$router.push({
        path:
          '/purchase-execute/deliver-list-supplier' + (location.href.includes('-kt') ? '-kt' : ''),
        query: {
          date: new Date().getTime()
        }
      })
      // }, 10);

      // 跳转到 送货单-列表-供方
    },
    // 移除行数据，更新 localStorage
    updateLocalStorageData(args) {
      const { selectRowIndexList } = args
      selectRowIndexList.forEach((itemIndex) => {
        gridDataSource.splice(itemIndex, 1)
      })
      localStorage.setItem('batchDeliverySupplierData', JSON.stringify(gridDataSource))
    },
    goBack() {
      localStorage.setItem('tabIndex', JSON.stringify(this.lastTabIndex))
      // 返回 供货计划-供方
      this.$router.push({
        name: 'supply-plan-supplier' + (location.href.includes('kt') ? '-kt' : '')
      })
      if (location.href.includes('kt')) {
        this.$bus.$emit('createDeliveryTodo')
      }
    },
    // 供方收发货供货计划-订单创建送货单
    postSupplierDeliverySupplyPlanOrderCreate(args) {
      const { params, selectRowIndexList } = args
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanOrderCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.handleCreateDeliveryAfter({
              response: res,
              selectRowIndexList
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-交货计划创建送货单
    postSupplierDeliverySupplyPlanPlanCreate(args) {
      const { params, selectRowIndexList } = args
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanPlanCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.handleCreateDeliveryAfter({
              response: res,
              selectRowIndexList
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-JIT创建送货单
    postSupplierDeliverySupplyPlanJitCreate(args) {
      const { params, selectRowIndexList } = args
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanJitCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.handleCreateDeliveryAfter({
              response: res,
              selectRowIndexList
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-交货计划预创建送货单
    postSupplierDeliverySupplyPlanPlanPreCreate(args) {
      const { params } = args
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanPlanPreCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const data = res?.data || []
            localStorage.setItem('batchDeliverySupplierData', JSON.stringify(data))
            // 将预创建订单数据 设置到 表格中
            gridDataSource.length = 0
            data.forEach((item) => {
              gridDataSource.push(item)
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方收发货供货计划-JIT预创建送货单
    postSupplierDeliverySupplyPlanJitPreCreate(args) {
      const { params } = args
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierDeliverySupplyPlanJitPreCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const data = res?.data || []
            localStorage.setItem('batchDeliverySupplierData', JSON.stringify(data))
            // 将预创建订单数据 设置到 表格中
            gridDataSource.length = 0
            data.forEach((item) => {
              gridDataSource.push(item)
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.top-setting {
  padding: 20px 20px 20px 0;
  font-weight: 500;

  .accordion-title {
    font-size: 14px;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
    text-indent: 10px;
    border-left: 5px solid #00469c;
    margin-bottom: 20px;
    border-radius: 2px 0 0 2px;
  }
}

.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  // padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  .header-box {
    border-bottom: 1px solid #e6e9ed;
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
  }
}

/deep/ .grid-wrap-not-paging .e-control.e-grid .e-gridcontent {
  .e-frozenscrollbar.e-frozen-left-scrollbar {
    width: 50px !important; // 选择列宽度
  }
  // .e-movablescrollbar .e-movablechild {
  //   width: 3730px !important; // 表格宽度 FIXME 注意 目前 订单供货计划 和 交货计划 的表格宽度都是相同的
  // }
}
.title {
  margin: 24px 0px 8px 24px;
  padding-left: 8px;
  border-left: 4px solid #3369ac;
  font-weight: 600;
}
</style>
