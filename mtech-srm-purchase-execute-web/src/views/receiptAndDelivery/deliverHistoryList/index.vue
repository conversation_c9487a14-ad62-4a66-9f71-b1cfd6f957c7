<!-- 送货单明细tab -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      @handleClickCellTitle="handleClickCellTitle"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
          <mt-form-item prop="deliveryCode" :label="$t('送货单号')">
            <mt-input
              v-model="searchFormModel.deliveryCode"
              :placeholder="$t('请输入送货单号')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="supplierCodes" :label="$t('供应商编码')">
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.supplierCodes"
              url="/masterDataManagement/tenant/supplier/paged-query"
              multiple
              :placeholder="$t('请选择供应商')"
              :fields="{ text: 'supplierName', value: 'supplierCode' }"
              :search-fields="['supplierName', 'supplierCode']"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="createTime" :label="$t('创建日期')">
            <mt-date-range-picker
              v-model="searchFormModel.createTime"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              @change="(e) => dataTimeChange(e, 'createTime')"
            />
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { componentConfig } from './config'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import { getHeadersFileName, download } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      componentConfig: componentConfig(this),
      searchFormModel: {
        createTimeS: this.getUnix(dayjs().subtract(1, 'year').format('YYYY-MM-DD 00:00:00')),
        createTimeE: this.getUnix(dayjs().format('YYYY-MM-DD 23:59:59'))
      },
      searchFormRules: {},
      statusOptions: [
        { text: this.$t('送货中'), value: 2 },
        { text: this.$t('已完成'), value: 3 },
        { text: this.$t('已取消'), value: 4 },
        { text: this.$t('已关闭'), value: 5 }
      ],
      onWayStatusOptions: [
        { text: this.$t('未出发'), value: 0 },
        { text: this.$t('已入园'), value: 1 },
        { text: this.$t('已出发'), value: 2 },
        { text: this.$t('已报到'), value: 3 },
        { text: this.$t('已取消'), value: 4 },
        { text: this.$t('已关闭'), value: 5 }
      ],
      companyOptions: [],
      companyCodeValueTemplate: codeNameColumn({
        firstKey: 'orgCode',
        secondKey: 'orgName'
      }),
      siteOptions: [],
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      })
    }
  },
  mounted() {
    this.getCompany({ text: '' })
    this.postSiteFuzzyQuery({ text: '' })
  },
  methods: {
    getCompany(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true,
        organizationIds: []
      }
      this.$API.masterData
        .OrgFindSpecifiedChildrenLevelOrgs(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.companyOptions = addCodeNameKeyInList({
              firstKey: 'orgCode',
              secondKey: 'orgName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.companyOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    companyCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.companyId = itemData.id
        this.searchFormModel.companyCode = itemData.orgCode
        this.searchFormModel.companyName = itemData.orgName
      } else {
        this.searchFormModel.companyId = ''
        this.searchFormModel.companyCode = ''
        this.searchFormModel.companyName = ''
      }
    },
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        page: {
          current: 1,
          pages: 0,
          size: 20
        }
      }
      this.$API.receiptAndDelivery
        .getFactoryInfo(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.siteCode = itemData.siteCode
        this.searchFormModel.siteName = itemData.siteName
      } else {
        this.searchFormModel.siteCode = ''
        this.searchFormModel.siteName = ''
      }
    },
    dataTimeChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'S'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[flag + 'E'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[flag + 'S'] = null
        this.searchFormModel[flag + 'E'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).unix()
    },
    handleClickToolBar(e) {
      const { toolbar } = e
      switch (toolbar.id) {
        case 'export':
          this.handleExport()
          break
      }
    },
    handleExport() {
      let params = {
        page: { current: 1, size: 10000 },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery.buyerOrderDeliveryQueryExportNewTv(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'createTime') {
            this.searchFormModel.createTimeS = this.getUnix(
              dayjs().subtract(1, 'year').format('YYYY-MM-DD 00:00:00')
            )

            this.searchFormModel.createTimeE = this.getUnix(dayjs().format('YYYY-MM-DD 23:59:59'))
          }
        }
      }
    },
    handleClickCellTitle(args) {
      const { field, data } = args
      if (field === 'deliveryCode') {
        this.getDeliverDataById(data.deliveryId)
      }
    },
    getDeliverDataById(id) {
      let params = { id }
      this.$API.receiptAndDelivery.getHistoryDeliveyDataTV(params).then((res) => {
        if (res.code == 200) {
          this.goToDetail({
            headerInfo: res.data
          })
        }
      })
    },
    goToDetail(data) {
      localStorage.setItem('lastTabIndex', 0)
      const { headerInfo } = data
      const deliverListData = {
        headerInfo
      }
      localStorage.setItem('deliverHistoryListData', JSON.stringify(deliverListData))
      this.$router.push({
        name: 'deliver-history-detail',
        query: {
          timeStamp: new Date().getTime()
        }
      })
    }
  }
}
</script>
