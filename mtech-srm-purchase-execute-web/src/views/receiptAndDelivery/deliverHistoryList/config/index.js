import { i18n } from '@/main.js'
import Vue from 'vue'
import { codeNameColumn } from '@/utils/utils'
import { timeDate } from './columnComponent'

const columnData = () => {
  const column = [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false,
      allowEditing: false,
      allowResizing: false
    },
    {
      field: 'id',
      headerText: 'id',
      width: 0,
      visible: false,
      allowEditing: false,
      isPrimaryKey: true
    },
    {
      field: 'deliveryCode',
      headerText: i18n.t('送货单号'),
      width: '165',
      cellTools: []
    },
    {
      field: 'deliveryLineNo',
      headerText: i18n.t('行号'),
      width: '100'
    },
    {
      field: 'status',
      headerText: i18n.t('状态'),
      width: '80',
      cellTools: [],
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e) {
            return e.label
          }
          return ''
        }
      }
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      width: '120'
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      width: '200'
    },
    {
      field: 'deliveryQuantity',
      headerText: i18n.t('本次送货数量'),
      width: '120'
    },
    {
      field: 'receiveQuantity',
      headerText: i18n.t('收货数量'),
      width: '100'
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: '110'
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: '200'
    },
    {
      field: 'orderCode',
      headerText: i18n.t('采购订单号'),
      width: '120'
    },
    {
      field: 'lineNo',
      headerText: i18n.t('采购订单行号'),
      width: '120'
    },
    {
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      width: '100'
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      width: '200'
    },
    {
      field: 'warehouseCode',
      headerText: i18n.t('库存地点'),
      width: '150',
      template: codeNameColumn({
        firstKey: 'warehouseCode',
        secondKey: 'warehouseName'
      })
    },
    {
      field: 'sendTime',
      headerText: i18n.t('发货日期'),
      width: '100',
      template: timeDate({
        dataKey: 'sendTime',
        isDate: true
      })
    },
    {
      field: 'onWayStatus',
      headerText: i18n.t('在途状态'),
      width: '100',
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e) {
            return e.label
          }
          return ''
        }
      }
    },
    {
      field: 'carNo',
      headerText: i18n.t('车牌号'),
      width: '100'
    },
    {
      field: 'vehicleLogistics',
      headerText: i18n.t('车辆物流'),
      width: '100',
      template: () => {
        return {
          template: Vue.component('vehicleLogistics', {
            template: `
                <span v-if="data.carNo" style="color: #6386c1; cursor: pointer;" @click="toLogistics(data)">查看物流</span>`,
            methods: {
              toLogistics(e) {
                const params = {
                  ztpno: e.deliveryCode.toString(),
                  busCode: e.forecastCode,
                  busNum: e.carNo
                }
                this.$API.receiptAndDelivery.purQueryVehicleLogistics(params).then((res) => {
                  if (res.code === 200) {
                    window.open(res.data.mapURL)
                  }
                })
              }
            }
          })
        }
      }
    },
    {
      field: 'companyCode',
      headerText: i18n.t('公司编码'),
      width: '100'
    },
    {
      field: 'companyName',
      headerText: i18n.t('公司名称'),
      width: '200'
    },
    {
      field: 'jit',
      headerText: i18n.t('是否JIT'),
      width: '100',
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('否'),
          1: i18n.t('是')
        }
      }
    },
    {
      field: 'jitDeliveryNumber',
      headerText: i18n.t('JIT编号'),
      width: '100'
    },
    {
      field: 'demandDate',
      headerText: i18n.t('需求日期'),
      width: '100',
      template: timeDate({
        dataKey: 'demandDate',
        isDate: true
      })
    },
    {
      field: 'rejectQuantity',
      headerText: i18n.t('拒绝数量'),
      width: '100'
    },
    {
      field: 'rejectReason',
      headerText: i18n.t('拒绝原因'),
      width: '100'
    },
    {
      field: 'inputDate',
      headerText: i18n.t('凭证创建日期'),
      width: '120',
      template: timeDate({
        dataKey: 'inputDate',
        isDate: true
      })
    },
    {
      field: 'postingDate',
      headerText: i18n.t('过账日期'),
      width: '100',
      template: timeDate({
        dataKey: 'postingDate',
        isDate: true
      })
    },
    {
      field: 'deliveryType',
      headerText: i18n.t('送货单类型'),
      width: '105',
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e) {
            return e.label
          }
          return ''
        }
      }
    },
    {
      field: 'deliveryNumber',
      headerText: i18n.t('交货编号'),
      width: '100'
    },
    {
      field: 'sendAddressName',
      headerText: i18n.t('发货地点'),
      width: '100'
    },
    {
      field: 'thirdTenantCode',
      headerText: i18n.t('第三方物流商'),
      width: '150',
      template: codeNameColumn({
        firstKey: 'thirdTenantCode',
        secondKey: 'thirdTenantName'
      })
    },
    {
      field: 'receiveSupplierCode',
      headerText: i18n.t('收货供应商'),
      width: '150',
      template: codeNameColumn({
        firstKey: 'receiveSupplierCode',
        secondKey: 'receiveSupplierName'
      })
    },
    {
      field: 'deliveryRemark',
      headerText: i18n.t('送货单备注'),
      width: '120'
    },
    {
      field: 'transferPlanName',
      headerText: i18n.t('计划员'),
      width: '100'
    },
    {
      field: 'buyerOrgCode',
      headerText: i18n.t('采购组'),
      width: '200',
      template: codeNameColumn({
        firstKey: 'buyerOrgCode',
        secondKey: 'buyerOrgName'
      })
    },
    {
      field: 'workOrderNo',
      headerText: i18n.t('关联工单号'),
      width: '120'
    },
    {
      field: 'saleOrderNo',
      headerText: i18n.t('关联销售订单号'),
      width: '120'
    },
    {
      field: 'saleOrderLineNo',
      headerText: i18n.t('关联销售订单行号'),
      width: '120'
    },
    {
      field: 'bomCode',
      headerText: i18n.t('BOM号'),
      width: '100'
    },
    {
      field: 'productCode',
      headerText: i18n.t('关联产品代码'),
      width: '120'
    },
    {
      field: 'workCenterCode',
      headerText: i18n.t('工作中心'),
      width: '150',
      template: codeNameColumn({
        firstKey: 'workCenterCode',
        secondKey: 'workCenterName'
      })
    },
    {
      field: 'processName',
      headerText: i18n.t('工序名称'),
      width: '100'
    },
    {
      field: 'sendAddressName',
      headerText: i18n.t('收货地址'),
      width: '100'
    },
    {
      field: 'unitCode',
      headerText: i18n.t('单位'),
      width: '100',
      template: codeNameColumn({
        firstKey: 'unitCode',
        secondKey: 'unitName'
      })
    },
    {
      field: 'batchCode',
      headerText: i18n.t('批次号'),
      width: '100'
    },
    {
      field: 'limitQuantity',
      headerText: i18n.t('限量数量'),
      width: '100'
    },
    {
      field: 'warehouseClerkName',
      headerText: i18n.t('仓管员'),
      width: '100'
    },
    {
      field: 'dispatcherName',
      headerText: i18n.t('调度员'),
      width: '100'
    },
    {
      field: 'wmsInstockQty',
      headerText: i18n.t('WMS入库数量'),
      width: '120'
    },
    {
      field: 'wmsInstockTime',
      headerText: i18n.t('WMS入库时间'),
      width: '120'
    },
    {
      field: 'cancelPersonName',
      headerText: i18n.t('取消人'),
      width: '100'
    },
    {
      field: 'cancelTime',
      headerText: i18n.t('取消时间'),
      width: '100',
      template: timeDate({
        dataKey: 'cancelTime',
        isDateTime: true
      })
    },
    {
      field: 'closePersonName',
      headerText: i18n.t('关闭人'),
      width: '100'
    },
    {
      field: 'closeTime',
      headerText: i18n.t('关闭时间'),
      width: '100',
      template: timeDate({
        dataKey: 'closeTime',
        isDateTime: true
      })
    },
    {
      field: 'collectorMark',
      headerText: i18n.t('代收标识'),
      width: '100'
    },
    {
      field: 'collectorName',
      headerText: i18n.t('代收人'),
      width: '100'
    },
    {
      field: 'remark',
      headerText: i18n.t('行备注'),
      width: '100'
    },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人'),
      width: '100'
    },
    {
      field: 'createTime',
      headerText: i18n.t('创建时间'),
      width: '100',
      template: timeDate({
        dataKey: 'createTime',
        isDateTime: true
      })
    }
  ]
  return column
}

export const componentConfig = (that) => {
  const config = [
    {
      isUseCustomSearch: true, // 是否使用自定义查询
      isCustomSearchRules: true,
      useToolTemplate: false, // 此项不使用预置的表格操作按钮'新增、编辑、删除'
      useBaseConfig: false, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
      toolbar: {
        tools: [
          [
            // {
            //   id: 'export',
            //   icon: 'icon_solid_Import',
            //   permission: ['O_02_1633'],
            //   title: i18n.t('导出')
            // }
          ],
          ['Setting']
        ]
      },
      gridId: '0ceca379-f0c3-4166-b6f0-8c464c32bf55',
      grid: {
        lineIndex: 1,
        allowPaging: true, // 是否使用内置分页器
        // virtualPageSize: 20, // 虚拟滚动，暂不开启
        // selectionSettings: {
        //   persistSelection: true,
        //   type: 'Multiple',
        //   checkboxOnly: true
        // },
        // enableVirtualization: true,
        columnData: columnData(that),
        asyncConfig: {
          url: '/srm-purchase-execute/tenant/delivery/item/archive/buyer/page'
        },
        pageSettings: {
          currentPage: 1,
          pageSize: 20,
          pageSizes: [10, 20, 50, 100, 200],
          totalRecordsCount: 0
        }
      }
    }
  ]
  return config
}
