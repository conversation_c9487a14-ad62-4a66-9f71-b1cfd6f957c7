import { i18n } from '@/main.js'
import dayjs from 'dayjs'

export const statusOptions = [
  { label: i18n.t('未同步'), value: 0 },
  { label: i18n.t('同步中'), value: 1 },
  { label: i18n.t('同步成功'), value: 2 },
  { label: i18n.t('同步失败'), value: 3 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left'
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码'),
    minWidth: 120
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 140
  },
  {
    field: 'materialCode',
    title: i18n.t('物料编码'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'itemCodeEdit'
    }
  },
  {
    field: 'materialDesc',
    title: i18n.t('物料描述'),
    minWidth: 140,
    editRender: {},
    slots: {
      edit: 'itemNameEdit'
    }
  },
  {
    field: 'goodsCode',
    title: i18n.t('商品编号'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'goodsDeclareName',
    title: i18n.t('商品申报品名'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'declareElement',
    title: i18n.t('申报要素'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'dealMeasureUnitName',
    title: i18n.t('成交计量单位中文'),
    minWidth: 140,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'legalFirstUnitName',
    title: i18n.t('法定第一计量单位名称'),
    minWidth: 140,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'materialOrigProdtCountry',
    title: i18n.t('物料原产国（地区）'),
    minWidth: 140,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'finalDest',
    title: i18n.t('最终目的国（地区）'),
    minWidth: 140,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'domesticSourceAddr',
    title: i18n.t('境内货源地'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'taxExemptionMethod',
    title: i18n.t('征免方式'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'materialUnitNetWeight',
    title: i18n.t('物料单位净重'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入'), type: 'number' } }
  },
  {
    field: 'materialBrand',
    title: i18n.t('物料品牌'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'materialMode',
    title: i18n.t('物料型号'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'domesticConsignor',
    title: i18n.t('境内收发货人'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'domesticConsignorName',
    title: i18n.t('境外收发货人名称（外文）'),
    minWidth: 140,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'domesticConsignorCode',
    title: i18n.t('境内收发货人十位海关代码'),
    minWidth: 140,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'productSalesUnit',
    title: i18n.t('生产销售单位'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'productSalesUnitCode',
    title: i18n.t('生产销售单位十位海关代码'),
    minWidth: 140,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'superviseType',
    title: i18n.t('监管方式'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'dutyExemptionMethod',
    title: i18n.t('征免性质'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'tradeCountry',
    title: i18n.t('贸易国（地区）'),
    minWidth: 140,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'destinationCountry',
    title: i18n.t('运抵国（地区）'),
    minWidth: 140,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'packageType',
    title: i18n.t('包装种类'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'specialRelationshipType',
    title: i18n.t('特殊关系确认'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'priceImpactConfirm',
    title: i18n.t('价格影响确认'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'expensePaymentConfirm',
    title: i18n.t('与货物有关的特许权使用'),
    minWidth: 140,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'formulaPriceConfirm',
    title: i18n.t('公式定价确认'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'provisionalPriceConfirm',
    title: i18n.t('暂定价格确认'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'taxRefund',
    title: i18n.t('是否退税'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'filingNumber',
    title: i18n.t('备案序号'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'recordProductNumber',
    title: i18n.t('备案商品料号'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'recordSpecificModel',
    title: i18n.t('备案规格型号'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'processProductConsumeVersion',
    title: i18n.t('加工成品单耗版本号'),
    minWidth: 140,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'legalSecondUnit',
    title: i18n.t('法定第二计量单位'),
    minWidth: 140,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'materialSize',
    title: i18n.t('物料尺寸'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'materialVoltage',
    title: i18n.t('物料电压'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'materialPower',
    title: i18n.t('物料功率'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'materialUsage',
    title: i18n.t('物料用途'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'freightFee',
    title: i18n.t('运费'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'premiumFee',
    title: i18n.t('保费'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'miscellaneousFee',
    title: i18n.t('杂费'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'manualFilingNumber',
    title: i18n.t('手(账)册编号/备案号'),
    minWidth: 130,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'operateUnitCode',
    title: i18n.t('经营单位编码'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'operateUnitName',
    title: i18n.t('经营单位名称'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'processUnitCode',
    title: i18n.t('加工单位编码'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'processUnitName',
    title: i18n.t('加工单位名称'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'internalCode',
    title: i18n.t('企业内部编码'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'supervisingCustoms',
    title: i18n.t('主管海关'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'matFgMark',
    title: i18n.t('料件、成品标识'),
    minWidth: 130,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'declarationUnitType',
    title: i18n.t('申报单位类型'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'accountBookType',
    title: i18n.t('账册类型'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'quantityPerBox',
    title: i18n.t('每箱物料数量'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入'), type: 'number' } }
  },
  {
    field: 'length',
    title: i18n.t('长'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入'), type: 'number' } }
  },
  {
    field: 'width',
    title: i18n.t('宽'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入'), type: 'number' } }
  },
  {
    field: 'height',
    title: i18n.t('高'),
    minWidth: 120,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入'), type: 'number' } }
  },
  {
    field: 'version',
    title: i18n.t('版本号'),
    minWidth: 120
  },
  {
    field: 'retryCnt',
    title: i18n.t('重试'),
    minWidth: 120
  },
  {
    field: 'syncTmsStatus',
    title: i18n.t('同步tms状态'),
    minWidth: 120,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.label : ''
    }
  },
  {
    field: 'syncTmsDesc',
    title: i18n.t('同步tms描述'),
    minWidth: 120
  },
  {
    field: 'syncTmsTimestamp',
    title: i18n.t('同步tms时间'),
    minWidth: 140,
    formatter: ({ cellValue }) => {
      return cellValue ? dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss') : ''
    }
  }
]
