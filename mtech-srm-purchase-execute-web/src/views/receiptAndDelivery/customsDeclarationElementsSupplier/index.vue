<!-- 供方 - 报关要素资料 -->
<template>
  <div class="full-height">
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('物料编码')" prop="itemCode">
          <mt-input
            v-model="itemCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
            @change="(e) => onChange(e, 'itemCode')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('商品申报品名')" prop="goodsDeclareName">
          <mt-input
            v-model="searchFormModel.goodsDeclareName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="4edebf06-4f96-469d-a14e-04435e004229"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :tooltip-config="tooltipConfig"
      @edit-actived="editBegin"
      @edit-closed="editComplete"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #itemCodeEdit="{ row }">
        <vxe-pulldown ref="xDownItem" transfer>
          <template #default>
            <vxe-input
              :value="row.materialCode"
              :placeholder="$t('请选择物料')"
              readonly
              @click="focusItemCode"
            />
          </template>
          <template #dropdown>
            <vxe-input
              prefix-icon="vxe-icon-search"
              :placeholder="$t('搜索')"
              @keyup="keyupItemCode"
              style="width: 100%"
            />
            <vxe-list height="200" class="predict-vxe-dropdown" :data="itemOptions" auto-resize>
              <template #default="{ items }">
                <div
                  v-show="itemOptions.length"
                  class="predict-vxe-list-item"
                  v-for="item in items"
                  :key="item.value"
                  @click="selectItemCode(item, row)"
                >
                  <span>{{ item.label }}</span>
                </div>
                <div v-show="!itemOptions.length" class="predict-vxe-list-item">
                  <span>{{ $t('暂无数据') }}</span>
                </div>
              </template>
            </vxe-list>
          </template>
        </vxe-pulldown>
      </template>
      <template #itemNameEdit="{ row }">
        <vxe-input :value="row.materialDesc" disabled />
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData } from './config'
import { getHeadersFileName, download } from '@/utils/utils'
import { utils } from '@mtech-common/utils'

export default {
  components: { CollapseSearch, ScTable },
  data() {
    return {
      searchFormModel: {},
      itemCode: null,
      toolbar: [
        // { code: 'add', name: this.$t('新增'), status: 'info', permission: ['O_02_1614'] },
        // { code: 'closeEdit', name: this.$t('取消编辑'), status: 'info' },
        // { code: 'delete', name: this.$t('删除'), status: 'info', permission: ['O_02_1615'] },
        // { code: 'syncTms', name: this.$t('同步TMS'), status: 'info' },
        // { code: 'import', name: this.$t('导入'), status: 'info', permission: ['O_02_1616'] },
        { code: 'export', name: this.$t('导出'), status: 'info' }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],
      tooltipConfig: {
        showAll: true,
        enterable: true,
        contentMethod: ({ type, column }) => {
          const { field } = column
          if (
            field === 'goodsCode' ||
            field === 'goodsDeclareName' ||
            field === 'declareElement' ||
            field === 'dealMeasureUnit' ||
            field === 'dealMeasureUnitName' ||
            field === 'legalFirstUnitCode' ||
            field === 'legalFirstUnitName'
          ) {
            if (type === 'header') {
              return column.title ? this.$t('集散模式') + '：' + this.$t('必填') : ''
            }
          }
          if (field === 'materialOrigProdtCountry') {
            if (type === 'header') {
              return column.title
                ? this.$t('直送') +
                    '+' +
                    this.$t('集散模式') +
                    '：' +
                    this.$t('必填') +
                    '\n' +
                    this.$t('只能输入英文')
                : ''
            }
          }
          if (field === 'materialBrand' || field === 'materialMode') {
            if (type === 'header') {
              return column.title
                ? this.$t('直送') + '+' + this.$t('集散模式') + '：' + this.$t('必填')
                : ''
            }
          }
          if (
            field === 'finalDest' ||
            field === 'domesticSourceAddr' ||
            field === 'taxExemptionMethod'
          ) {
            if (type === 'header') {
              return column.title
                ? this.$t('集散模式') + '：' + this.$t('必填') + '\n' + this.$t('只能填写海关代码')
                : ''
            }
          }
          if (field === 'materialUnitNetWeight') {
            if (type === 'header') {
              return column.title
                ? this.$t('直送') +
                    '+' +
                    this.$t('集散模式') +
                    '：' +
                    this.$t('必填') +
                    '\n' +
                    this.$t('越南进口清关使用，请使用英文') +
                    '\n' +
                    this.$t('单颗物料的净重，单位KG')
                : ''
            }
          }
          if (
            field === 'domesticConsignorName' ||
            field === 'domesticConsignor' ||
            field === 'domesticConsignorCode' ||
            field === 'productSalesUnit' ||
            field === 'productSalesUnitCode' ||
            field === 'superviseType' ||
            field === 'dutyExemptionMethod' ||
            field === 'tradeCountry' ||
            field === 'destinationCountry' ||
            field === 'packageType' ||
            field === 'specialRelationshipType' ||
            field === 'priceImpactConfirm' ||
            field === 'expensePaymentConfirm' ||
            field === 'formulaPriceConfirm' ||
            field === 'provisionalPriceConfirm'
          ) {
            if (type === 'header') {
              return column.title
                ? this.$t('集散模式') + '：' + this.$t('必填') + '\n' + this.$t('报关单表头数据')
                : ''
            }
          }
          if (field === 'taxRefund') {
            if (type === 'header') {
              return column.title
                ? this.$t('集散模式') + '：' + this.$t('必填') + '\n' + this.$t('舱单申报需要填制')
                : ''
            }
          }
          if (
            field === 'filingNumber' ||
            field === 'recordProductNumber' ||
            field === 'recordSpecificModel' ||
            field === 'processProductConsumeVersion'
          ) {
            if (type === 'header') {
              return column.title
                ? this.$t('集散模式') +
                    '：' +
                    this.$t('选填') +
                    '\n' +
                    this.$t('企业根据自己的监管方式选填')
                : ''
            }
          }
          if (field === 'legalSecondUnit') {
            if (type === 'header') {
              return column.title ? this.$t('集散模式') + '：' + this.$t('选填') : ''
            }
          }
          if (field === 'materialSize') {
            if (type === 'header') {
              return column.title
                ? this.$t('直送') +
                    '+' +
                    this.$t('集散模式') +
                    '：' +
                    this.$t('如有必填，如无免填') +
                    '\n' +
                    this.$t('越南进口清关使用，请使用英文') +
                    '\n' +
                    this.$t('填制单颗物料长宽高厚度等等')
                : ''
            }
          }
          if (field === 'materialVoltage' || field === 'materialPower') {
            if (type === 'header') {
              return column.title
                ? this.$t('直送') +
                    '+' +
                    this.$t('集散模式') +
                    '：' +
                    this.$t('如有必填，如无免填') +
                    '\n' +
                    this.$t('越南进口清关使用，请使用英文') +
                    '\n' +
                    this.$t('该物料需通电才能运作的要提供电压，功率')
                : ''
            }
          }
          if (field === 'materialUsage') {
            if (type === 'header') {
              return column.title
                ? this.$t('直送') +
                    '+' +
                    this.$t('集散模式') +
                    '：' +
                    this.$t('如有必填，如无免填') +
                    '\n' +
                    this.$t('越南进口清关使用，请使用英文')
                : ''
            }
          }
          if (field === 'freightFee' || field === 'premiumFee' || field === 'miscellaneousFee') {
            if (type === 'header') {
              return column.title
                ? this.$t('集散模式') +
                    '：' +
                    this.$t('选填') +
                    '\n' +
                    this.$t('报关单表头数据') +
                    '\n' +
                    this.$t('根据实际成交方式选填')
                : ''
            }
          }
          if (
            field === 'manualFilingNumber' ||
            field === 'operateUnitCode' ||
            field === 'operateUnitName' ||
            field === 'processUnitCode' ||
            field === 'processUnitName' ||
            field === 'internalCode' ||
            field === 'supervisingCustoms' ||
            field === 'matFgMark' ||
            field === 'declarationUnitType' ||
            field === 'accountBookType'
          ) {
            if (type === 'header') {
              return column.title
                ? this.$t('集散模式') +
                    '：' +
                    this.$t('选填') +
                    '\n' +
                    this.$t('报关单/核注表头数据') +
                    '\n' +
                    this.$t('根据实际监管方式选填核注清单表头')
                : ''
            }
          }
          // 其余的单元格默认显示
          return null
        }
      },

      getItemDataSource: () => {},
      itemOptions: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getItemCodeList()
    this.getItemDataSource = utils.debounce(this.getItemCodeList, 1000)
    this.getTableData()
  },
  methods: {
    focusItemCode() {
      this.$refs.xDownItem.showPanel()
    },
    keyupItemCode(e) {
      this.getItemDataSource(e)
    },
    selectItemCode(e, row) {
      row.materialCode = e.itemCode
      row.materialDesc = e.itemName
    },
    getItemCodeList(e = { value: '' }) {
      const { value } = e
      //物料下拉
      let params = {
        keyword: value || '',
        pageSize: 50
      }
      this.$API.masterData.getItemByKeyword(params).then((res) => {
        console.log(res)
        const list = res.data?.records || []
        const newData = list.map((i) => {
          return {
            ...i,
            label: `${i.itemCode}-${i.itemName}`,
            value: i.itemCode
          }
        })
        this.itemOptions = [...newData]
      })
    },
    onChange(e, field) {
      if (e) {
        this.searchFormModel[`${field}s`] = this[field].split(' ')
      } else {
        this.searchFormModel[`${field}s`] = null
        this[field] = null
      }
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.itemCode = null
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.receiptAndDelivery
        .pageSupplierCustomsDeclarationApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const list = res.data?.records
        this.tableData = list
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)
      }
    },
    editBegin(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        this.getItemDataSource({ value: row.materialCode }, row)
      }
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        if (args.$event.target.innerText === this.$t('取消编辑')) {
          this.tableRef.clearEdit()
          return
        }
        // 1、 校验必填
        if (!this.isValidData(row)) {
          this.tableRef.setEditRow(row)
          return
        }
        // 2、 调保存接口
        this.handleSave(row)
      }
    },
    isValidData(row) {
      let valid = false
      if (!row.materialCode) {
        this.$toast({ content: this.$t('请选择物料编码'), type: 'warning' })
      } else if (!row.goodsCode) {
        this.$toast({ content: this.$t('请输入商品编号'), type: 'warning' })
      } else if (!row.goodsDeclareName) {
        this.$toast({ content: this.$t('请输入商品申报品名'), type: 'warning' })
      } else if (!row.declareElement) {
        this.$toast({ content: this.$t('请输入申报要素'), type: 'warning' })
      } else if (!row.dealMeasureUnitName) {
        this.$toast({ content: this.$t('请输入成交计量单位中文'), type: 'warning' })
      } else if (!row.legalFirstUnitName) {
        this.$toast({ content: this.$t('请输入法定第一计量单位名称'), type: 'warning' })
      } else if (!row.materialOrigProdtCountry) {
        this.$toast({ content: this.$t('请输入物料原产国（地区）'), type: 'warning' })
      } else if (!row.finalDest) {
        this.$toast({ content: this.$t('请输入最终目的国（地区）'), type: 'warning' })
      } else if (!row.domesticSourceAddr) {
        this.$toast({ content: this.$t('请输入境内货源地'), type: 'warning' })
      } else if (!row.taxExemptionMethod) {
        this.$toast({ content: this.$t('请输入征免方式'), type: 'warning' })
      } else if (!row.materialUnitNetWeight) {
        this.$toast({ content: this.$t('请输入物料单位净重'), type: 'warning' })
      } else if (!row.materialBrand) {
        this.$toast({ content: this.$t('请输入物料品牌'), type: 'warning' })
      } else if (!row.materialMode) {
        this.$toast({ content: this.$t('请输入物料型号'), type: 'warning' })
      } else if (!row.domesticConsignorName) {
        this.$toast({ content: this.$t('请输入境内收发货人'), type: 'warning' })
      } else if (!row.domesticConsignor) {
        this.$toast({ content: this.$t('请输入境外收发货人名称（外文）'), type: 'warning' })
      } else if (!row.domesticConsignorCode) {
        this.$toast({ content: this.$t('请输入境内收发货人十位海关代码'), type: 'warning' })
      } else if (!row.productSalesUnit) {
        this.$toast({ content: this.$t('请输入生产销售单位'), type: 'warning' })
      } else if (!row.productSalesUnitCode) {
        this.$toast({ content: this.$t('请输入生产销售单位十位海关代码'), type: 'warning' })
      } else if (!row.superviseType) {
        this.$toast({ content: this.$t('请输入监管方式'), type: 'warning' })
      } else if (!row.dutyExemptionMethod) {
        this.$toast({ content: this.$t('请输入征免性质'), type: 'warning' })
      } else if (!row.tradeCountry) {
        this.$toast({ content: this.$t('请输入贸易国（地区）'), type: 'warning' })
      } else if (!row.destinationCountry) {
        this.$toast({ content: this.$t('请输入运抵国（地区）'), type: 'warning' })
      } else if (!row.packageType) {
        this.$toast({ content: this.$t('请输入包装种类'), type: 'warning' })
      } else if (!row.specialRelationshipType) {
        this.$toast({ content: this.$t('请输入特殊关系确认'), type: 'warning' })
      } else if (!row.priceImpactConfirm) {
        this.$toast({ content: this.$t('请输入价格影响确认'), type: 'warning' })
      } else if (!row.expensePaymentConfirm) {
        this.$toast({ content: this.$t('请输入与货物有关的特许权使用'), type: 'warning' })
      } else if (!row.formulaPriceConfirm) {
        this.$toast({ content: this.$t('请输入公式定价确认'), type: 'warning' })
      } else if (!row.provisionalPriceConfirm) {
        this.$toast({ content: this.$t('请输入暂定价格确认'), type: 'warning' })
      } else if (!row.taxRefund) {
        this.$toast({ content: this.$t('请输入是否退税'), type: 'warning' })
      } else if (!row.quantityPerBox) {
        this.$toast({ content: this.$t('请输入每箱物料数量'), type: 'warning' })
      } else if (!row.length) {
        this.$toast({ content: this.$t('请输入长'), type: 'warning' })
      } else if (!row.width) {
        this.$toast({ content: this.$t('请输入宽'), type: 'warning' })
      } else if (!row.height) {
        this.$toast({ content: this.$t('请输入高'), type: 'warning' })
      } else {
        valid = true
      }
      return valid
    },
    handleSave(row) {
      let params = { ...row }
      if (params.id.includes('row_')) {
        params.id = null
      }
      this.$API.receiptAndDelivery
        .saveSupplierCustomsDeclarationApi(params)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleSearch()
          }
        })
        .catch(() => {
          // 当出现错误时，指定行进入编辑状态
          this.tableRef.setEditRow(row)
        })
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const ids = []
      selectedRecords.forEach((item) => {
        ids.push(item.id)
      })
      const commonToolbar = ['delete', 'syncTms']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'closeEdit':
          this.tableRef.clearEdit()
          this.handleSearch()
          break
        case 'delete':
          this.handleDelete(ids)
          break
        case 'syncTms':
          this.handleSyncTms(ids)
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    handleAdd() {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      const item = {
        supplierCode: userInfo.accountName,
        supplierName: userInfo.tenantName,
        materialCode: null,
        materialDesc: null
      }
      this.tableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },
    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除？')
        },
        success: () => {
          this.$API.receiptAndDelivery.deleteSupplierCustomsDeclarationApi({ ids }).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.handleSearch()
            }
          })
        }
      })
    },
    handleSyncTms(ids) {
      const params = {
        idsRequest: { ids },
        operationType: 'add'
      }
      this.$API.receiptAndDelivery.syncTmsSupplierCustomsDeclarationApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.receiptAndDelivery.importSupplierCustomsDeclarationApi,
          downloadTemplateApi: this.$API.receiptAndDelivery.tempSupplierCustomsDeclarationApi,
          paramsKey: 'excel'
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    handleExport() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery.exportSupplierCustomsDeclarationApi(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
