<template>
  <!-- 送货单-详情-采方 -->
  <div class="full-height pt20 vertical-flex-box">
    <!-- 头部信息 -->
    <top-info
      class="flex-keep"
      :header-info="headerInfo"
      @goBack="goBack"
      @doExpand="doExpand"
    ></top-info>
    <!-- 列模板 -->
    <mt-template-page
      class="flex-fit"
      ref="templateRef"
      :hidden-tabs="false"
      :template-config="templateConfig"
    >
      <!-- 物流信息 Tab -->
      <div slot="slot-1" class="full-height">
        <logistics-info :logistics-data="logisticsData"></logistics-info>
      </div>
    </mt-template-page>
  </div>
</template>

<script>
import { formatTableColumnData } from './config/index'
import { ColumnDataTab1 } from './config/constant'
import { BASE_TENANT } from '@/utils/constant'

export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue'),
    LogisticsInfo: () => import('./components/logisticsInfo.vue')
  },
  data() {
    const lastTabIndex = JSON.parse(localStorage.getItem('lastTabIndex'))
    const deliverListData = JSON.parse(localStorage.getItem('deliverListData'))
    const sendTime = deliverListData?.headerInfo?.sendTime // 发货日期
    const forecastArriveTime = deliverListData?.headerInfo?.forecastArriveTime // 预计到货日期
    const headerInfo = {
      ...deliverListData?.headerInfo,
      sendTime: sendTime && sendTime != 0 ? new Date(Number(sendTime)) : '', // 发货日期,
      forecastArriveTime:
        forecastArriveTime && forecastArriveTime != 0 ? new Date(Number(forecastArriveTime)) : '' // 预计到货日期,
    }
    if (!headerInfo.status) headerInfo.status = {}
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      headerInfo, // 顶部的数据
      logisticsData: {}, // 物流信息
      lastTabIndex, // 前一页面的 Tab index
      templateConfig: [
        {
          tab: { title: this.$t('物料信息') },
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: { tools: [[], []] },
          gridId: this.$tableUUID.receiptAndDelivery.deliverDetail.materialInfo,
          grid: {
            allowPaging: true, // 分页
            // lineIndex: 0, // 序号列
            columnData: formatTableColumnData({
              data: ColumnDataTab1
            }),
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/newMode/delivery/item/buyer/sort/query`,
              params: {
                deliveryId: headerInfo.id
              }
            }
          }
        },
        {
          tab: { title: this.$t('物流信息') }
        }
      ]
    }
  },
  mounted() {
    // 获取物流信息
    this.getBuyerOrderDeliveryQueryDeliveryLogisticsInfo()
  },
  beforeDestroy() {
    // localStorage.removeItem("deliverListData");
    // localStorage.removeItem("lastTabIndex");
  },
  methods: {
    // 采方送货单-采方查询送货单物流信息
    getBuyerOrderDeliveryQueryDeliveryLogisticsInfo() {
      const params = {
        deliveryId: this.headerInfo?.id
      }
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .getBuyerOrderDeliveryQueryDeliveryLogisticsInfo(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.logisticsData = res.data || {}
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    goBack() {
      // 将 tabIndex 放到 localStorage 送货单-列表-采方 读
      localStorage.setItem('tabIndex', JSON.stringify(this.lastTabIndex))
      // 返回 送货单-列表-采方 TabIndex
      this.$router.push({
        name: 'new-mode-deliver-list-tv'
      })
    },
    doExpand() {
      this.$refs.templateRef?.resetGridHeight && this.$refs.templateRef.resetGridHeight()
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .flex-keep {
  margin-top: 10px;
  height: 40px;
}
</style>
