import { i18n } from '@/main.js'

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left'
  },
  {
    field: 'period',
    title: i18n.t('装柜年月'),
    minWidth: 120
  },
  {
    field: 'vendorCode',
    title: i18n.t('供应商编码'),
    minWidth: 120
  },
  {
    field: 'vendorName',
    title: i18n.t('供应商名称'),
    minWidth: 140
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码'),
    minWidth: 120
  },
  {
    field: 'deliveryCode',
    title: i18n.t('送货单'),
    minWidth: 140
  },
  {
    field: 'deliveryLineNo',
    title: i18n.t('送货单行号'),
    minWidth: 140
  },
  {
    field: 'poNo',
    title: i18n.t('PO'),
    minWidth: 120
  },
  {
    field: 'poLineNo',
    title: i18n.t('PO行号'),
    minWidth: 120
  },
  {
    field: 'quantity',
    title: i18n.t('送货单数量'),
    minWidth: 120
  },
  {
    field: 'receiveQuantity',
    title: i18n.t('WMS实收数量'),
    minWidth: 120
  },
  {
    field: 'loadingQuantity',
    title: i18n.t('装柜数量'),
    minWidth: 120
  },
  {
    field: 'leaveQuantity',
    title: i18n.t('余留库存数量'),
    minWidth: 120
  }
]
