<!-- 采方-蕴鑫收货报表 -->
<template>
  <div class="full-height">
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('装柜年月')" prop="supplierCode">
          <mt-date-picker
            v-model="searchFormModel.period"
            :show-clear-button="true"
            :allow-edit="false"
            start="Year"
            depth="Year"
            format="yyyyMM"
            :placeholder="$t('请选择')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item :label="$t('供应商编码')" prop="vendorCode">
          <RemoteAutocomplete
            v-model="searchFormModel.vendorCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料编码')" prop="itemCodeList">
          <mt-input
            v-model="itemCodeList"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
            @change="(e) => onChange(e, 'itemCodeList')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('采购订单号')" prop="poNo">
          <mt-input
            v-model="searchFormModel.poNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('送货单')" prop="deliveryCode">
          <mt-input
            v-model="searchFormModel.deliveryCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="db932976-9fb0-4b0d-b6d7-12ee16c66271"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :tooltip-config="tooltipConfig"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          v-permission="item.permission"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData } from './config'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  components: { CollapseSearch, ScTable },
  data() {
    return {
      searchFormModel: {},
      itemCodeList: null,
      toolbar: [{ code: 'export', name: this.$t('导出'), status: 'info' }],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 100,
        pageSizes: [50, 100, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],
      tooltipConfig: {
        showAll: true,
        enterable: true
      }
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    onChange(e, field) {
      if (e) {
        this.searchFormModel[`${field}`] = this[field].split(' ')
      } else {
        this.searchFormModel[`${field}`] = null
        this[field] = null
      }
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.itemCodeList = null
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      params.period = params.period ? dayjs(params.period).format('YYYYMM') : null
      this.loading = true
      const res = await this.$API.receiptAndDelivery
        .pageReceiptAndDeliveryApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const list = res.data?.records
        this.tableData = list
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const ids = selectedRecords.map((v) => v.id)
      switch (e.code) {
        case 'export':
          this.handleExport(ids)
          break
        default:
          break
      }
    },
    handleExport(ids) {
      const params = {
        ids,
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      params.period = params.period ? dayjs(params.period).format('YYYYMM') : null
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery.exportReceiptAndDeliveryApi(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
