import { i18n } from '@/main.js'
import Vue from 'vue'
import inputView from '../components/inputView.vue'

// tab
export const Tab = {
  list: 1, // 送货单列表
  details: 2 // 送货明细
}

// Toolbar 按钮
export const Toolbar = {
  // 送货单列表
  0: [
    {
      id: 'DeliveryNoteClose',
      icon: 'icon_solid_Closeorder',
      permission: ['O_02_0494'],
      title: i18n.t('关闭')
    },
    {
      id: 'DeliveryNoteCancel',
      icon: 'icon_table_cancel',
      permission: ['O_02_0495'],
      title: i18n.t('取消')
    },
    {
      id: 'copy',
      icon: '',
      title: i18n.t('复制送货单号')
    },
    {
      id: 'print',
      icon: 'icon_table_print',
      title: i18n.t('打印送货单（空调）')
      // permission: ["O_02_0638"],
    },
    {
      id: 'printBd',
      icon: 'icon_table_print',
      title: i18n.t('打印送货单（白电）')
      // permission: ["O_02_0638"],
    },
    {
      id: 'synchronous',
      icon: 'icon_table_restart',
      title: i18n.t('同步')
    },
    {
      id: 'createSapdeliveryNode',
      icon: 'icon_table_restart',
      title: i18n.t('创建SAP交货单'),
      permission: ['O_02_1611']
    },
    {
      id: 'exportList',
      icon: 'icon_table_export',
      title: i18n.t('导出')
    },
    {
      id: 'unlock',
      icon: '',
      title: i18n.t('预约解锁'),
      permission: ['O_02_1844']
    }
  ],
  // 送货单明细
  1: []
}

// 送货单类型
export const DeliveryType = {
  associatedOrder: 1, //
  noOrder: 2, //
  jit: 3, //
  noNeed: 4, //
  vmi: 5, //
  gc: 6 //
}
// 送货单类型 text
export const DeliveryTypeText = {
  [DeliveryType.associatedOrder]: i18n.t('采购订单'),
  [DeliveryType.noOrder]: i18n.t('交货计划'),
  [DeliveryType.jit]: i18n.t('JIT'),
  [DeliveryType.noNeed]: i18n.t('无需求'),
  [DeliveryType.vmi]: i18n.t('vmi'),
  [DeliveryType.gc]: i18n.t('钢材')
}
// 送货单类型 对应的 Options
export const DeliveryTypeOptions = [
  {
    // 关联采购订单
    value: DeliveryType.associatedOrder,
    text: DeliveryTypeText[DeliveryType.associatedOrder],
    cssClass: ''
  },
  {
    // 无采购订单
    value: DeliveryType.noOrder,
    text: DeliveryTypeText[DeliveryType.noOrder],
    cssClass: ''
  },
  {
    // 关联采购订单
    value: DeliveryType.jit,
    text: DeliveryTypeText[DeliveryType.jit],
    cssClass: ''
  },
  {
    // 无采购订单
    value: DeliveryType.noNeed,
    text: DeliveryTypeText[DeliveryType.noNeed],
    cssClass: ''
  },
  {
    // 关联采购订单
    value: DeliveryType.vmi,
    text: DeliveryTypeText[DeliveryType.vmi],
    cssClass: ''
  },
  {
    // 无采购订单
    value: DeliveryType.gc,
    text: DeliveryTypeText[DeliveryType.gc],
    cssClass: ''
  }
]

// 交货方式:1-采购订单,2-交货计划,3-JIT,4-无需求
export const TheDeliveryType = {
  purchase: 1, // 采购订单
  plan: 2, // 交货计划
  jit: 3, // jit
  noNeed: 4 // 无需求
}
// 交货方式 text
export const TheDeliveryTypeText = {
  [TheDeliveryType.purchase]: i18n.t('采购订单'),
  [TheDeliveryType.plan]: i18n.t('交货计划'),
  [TheDeliveryType.jit]: i18n.t('JIT'),
  [TheDeliveryType.noNeed]: i18n.t('无需求')
}
// 交货方式 对应的 Options
export const TheDeliveryTypeOptions = [
  {
    // 采购订单
    value: TheDeliveryType.purchase,
    text: TheDeliveryTypeText[TheDeliveryType.purchase],
    cssClass: ''
  },
  {
    // 交货计划
    value: TheDeliveryType.plan,
    text: TheDeliveryTypeText[TheDeliveryType.plan],
    cssClass: ''
  },
  {
    // JIT
    value: TheDeliveryType.jit,
    text: TheDeliveryTypeText[TheDeliveryType.jit],
    cssClass: ''
  },
  {
    // 无需求
    value: TheDeliveryType.noNeed,
    text: TheDeliveryTypeText[TheDeliveryType.noNeed],
    cssClass: ''
  },
  {
    value: 5,
    text: 'vmi',
    cssClass: ''
  },
  {
    value: 6,
    text: i18n.t('钢材'),
    cssClass: ''
  }
]

// 状态 状态:1-新建,2-发货中,3-已完成,4-已取消,5-已关闭
export const Status = {
  new: 1, //新建
  shipping: 2, // 发货中
  completed: 3, // 已完成
  cancelled: 4, // 已取消
  closed: 5 // 已关闭
}

// 状态 text
export const StatusText = {
  [Status.new]: i18n.t('新建'),
  [Status.shipping]: i18n.t('发货中'),
  [Status.completed]: i18n.t('已完成'),
  [Status.cancelled]: i18n.t('已取消'),
  [Status.closed]: i18n.t('已关闭')
}

// 状态 对应的 css class
export const StatusCssClass = {
  [Status.new]: 'col-active',
  [Status.shipping]: 'col-active',
  [Status.completed]: 'col-active',
  [Status.cancelled]: 'col-active',
  [Status.closed]: 'col-inactive'
}

// 状态 对应的 Options
export const StatusOptions = [
  {
    // 新建
    value: Status.new,
    text: StatusText[Status.new],
    cssClass: StatusCssClass[Status.new]
  },
  {
    // 发货中
    value: Status.shipping,
    text: StatusText[Status.shipping],
    cssClass: StatusCssClass[Status.shipping]
  },
  {
    // 已完成
    value: Status.completed,
    text: StatusText[Status.completed],
    cssClass: StatusCssClass[Status.completed]
  },
  {
    // 已取消
    value: Status.cancelled,
    text: StatusText[Status.cancelled],
    cssClass: StatusCssClass[Status.cancelled]
  },
  {
    // 已关闭
    value: Status.closed,
    text: StatusText[Status.closed],
    cssClass: StatusCssClass[Status.closed]
  }
]

export const SyncDeliveryNoStatusOptions = [
  { text: i18n.t('未同步'), value: 0, cssClass: 'col-active' },
  { text: i18n.t('同步中'), value: 1, cssClass: 'col-active' },
  { text: i18n.t('同步成功'), value: 2, cssClass: 'col-active' },
  { text: i18n.t('同步失败'), value: 3, cssClass: 'col-active' }
]

export const onWayStatusOptions = [
  { text: i18n.t('未出发'), value: 0, cssClass: 'col-active' },
  { text: i18n.t('已入园'), value: 1, cssClass: 'col-active' },
  { text: i18n.t('已出发'), value: 2, cssClass: 'col-active' },
  { text: i18n.t('已报到'), value: 3, cssClass: 'col-active' },
  { text: i18n.t('保安门岗确认'), value: 11, cssClass: 'col-active' },
  { text: i18n.t('已取消'), value: 4, cssClass: 'col-active' },
  { text: i18n.t('已关闭'), value: 5, cssClass: 'col-inactive' },
  { text: i18n.t('已到厂待卸货'), value: 7, cssClass: 'col-active' },
  { text: i18n.t('已卸货待报检'), value: 8, cssClass: 'col-active' },
  { text: i18n.t('已离园'), value: 12, cssClass: 'col-active' }
]

export const operatorOptions = [
  { text: '=', value: 'equal' },
  { text: '≠', value: 'notequal' },
  { text: '>', value: 'greaterthan' },
  { text: '≥', value: 'greaterthanorequal' },
  { text: '<', value: 'lessthan' },
  { text: '≤', value: 'lessthanorequal' }
]

// jit
export const JitOptions = [
  { value: 0, text: i18n.t('否'), cssClass: '' },
  { value: 1, text: i18n.t('是'), cssClass: '' }
]

// 委外方式
export const OutsourcedTypeOptions = [
  { value: '0', text: i18n.t('标准委外'), cssClass: '' },
  { value: '1', text: i18n.t('销售委外'), cssClass: '' },
  { value: '2', text: i18n.t('非委外'), cssClass: '' },
  { value: '3', text: i18n.t('工序委外'), cssClass: '' }
]

// 单元格操作按钮
export const CellTools = [
  {
    id: 'DeliveryNoteClose',
    icon: '',
    title: i18n.t('关闭'),
    // permission: [""],
    visibleCondition: (data) => data.status == Status.shipping // 发货中
  },
  {
    id: 'DeliveryNoteCancel',
    icon: '',
    title: i18n.t('取消'),
    // permission: [""],
    visibleCondition: (data) => data.status == Status.shipping // 发货中
  }
]

// tab1 送货单列表 表格列数据
export const ColumnDataTab1 = [
  {
    fieldCode: 'deliveryCode', // 送货单号
    fieldName: i18n.t('送货单号')
  },

  {
    fieldCode: 'deliveryOrderType', // 类型 送货单类型:1-关联采购订单,2-无采购订单
    fieldName: i18n.t('送货单类型')
  },
  {
    fieldCode: 'outsourcedType',
    fieldName: i18n.t('委外方式')
  },
  {
    fieldCode: 'status', // 状态 状态:1-新建,2-发货中,3-已完成,4-已取消,5-已关闭
    fieldName: i18n.t('状态')
  },
  {
    width: '120',
    fieldCode: 'onWayStatus',
    fieldName: i18n.t('在途状态'),
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: onWayStatusOptions
    }
  },
  {
    fieldCode: 'driverName',
    fieldName: i18n.t('司机名称')
  },
  {
    fieldCode: 'driverNo',
    fieldName: i18n.t('司机身份证'),
    template: () => ({ template: inputView }),
    width: 180
  },
  {
    fieldCode: 'driverPhone',
    fieldName: i18n.t('司机联系方式'),
    template: () => ({ template: inputView }),
    width: 180
  },
  {
    fieldCode: 'carNo',
    fieldName: i18n.t('车牌')
  },
  {
    fieldCode: 'number',
    fieldName: i18n.t('件数')
  },
  {
    fieldCode: 'vehicleLogistics', // 车辆物流
    fieldName: i18n.t('车辆物流'),
    template: () => {
      return {
        template: Vue.component('template-detail', {
          template: `
              <span v-if="data.carNo" style="color: #6386c1; cursor: pointer;" @click="toLogistics(data)">{{ $t('查看物流') }}</span>`,
          methods: {
            toLogistics(e) {
              const params = {
                ztpno: e.deliveryCode.toString(),
                busCode: e.forecastCode,
                busNum: e.carNo
              }
              this.$API.receiptAndDelivery.purQueryVehicleLogistics(params).then((res) => {
                if (res.code === 200) {
                  window.open(res.data.mapURL)
                }
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'syncWmsStatus',
    fieldName: i18n.t('Wms同步状态')
  },
  {
    fieldCode: 'syncWmsDesc',
    fieldName: i18n.t('Wms同步信息')
  },
  {
    fieldCode: 'syncSapStatus',
    fieldName: i18n.t('Sap同步状态')
  },
  {
    fieldCode: 'syncSapDesc',
    fieldName: i18n.t('Sap同步信息')
  },
  {
    fieldCode: 'syncQmsStatus',
    fieldName: i18n.t('Qms同步状态')
  },
  {
    fieldCode: 'syncQmsDesc',
    fieldName: i18n.t('Qms同步信息')
  },
  {
    fieldCode: 'siteCode', // 工厂 siteName code-name
    fieldName: i18n.t('工厂')
  },
  {
    fieldCode: 'printTimes', //
    fieldName: i18n.t('打印次数')
  },
  {
    fieldCode: 'companyCode', // 公司 companyName code-name
    fieldName: i18n.t('公司')
  },
  {
    fieldCode: 'buyerOrgCode', // 采购组 buyerOrgName code-name
    fieldName: i18n.t('采购组')
  },
  {
    fieldCode: 'supplierCode', // 供应商 supplierName code-name
    fieldName: i18n.t('供应商')
  },
  {
    fieldCode: 'thirdTenantCode', // thirdTenantName code-name
    fieldName: i18n.t('第三方物流商')
  },
  {
    fieldCode: 'vmiWarehouseCode', // VMI仓编号
    fieldName: i18n.t('VMI仓编码')
  },
  {
    fieldCode: 'vmiWarehouseName', // VMI仓名称
    fieldName: i18n.t('VMI仓名称')
  },
  {
    fieldCode: 'sendAddressName', // 发货地点
    fieldName: i18n.t('发货地点')
  },
  {
    fieldCode: 'createUserName', // 创建人
    fieldName: i18n.t('创建人')
  },
  {
    fieldCode: 'createTime', // 创建时间
    fieldName: i18n.t('创建时间')
  },
  {
    fieldCode: 'warehouseCode', // 交货库存地点 warehouseName code-name
    fieldName: i18n.t('交货库存地点')
  },
  {
    fieldCode: 'sendTime', // 发货日期
    fieldName: i18n.t('发货日期')
  },
  {
    fieldCode: 'syncDeliveryNoStatus',
    fieldName: i18n.t('创建交货单是否成功')
  },
  {
    fieldCode: 'syncDeliveryNoDesc',
    fieldName: i18n.t('创建交货单失败信息')
  },
  {
    fieldCode: 'outboundWarehouseCode',
    fieldName: i18n.t('出库货位')
  },
  {
    fieldCode: 'remark', // 备注
    fieldName: i18n.t('备注')
  }
]

// tab2 送货单明细 表格列数据
export const ColumnDataTab2 = [
  {
    fieldCode: 'deliveryCode', // 送货单号
    fieldName: i18n.t('送货单号')
  },
  {
    fieldCode: 'deliveryLineNo', // 行号
    fieldName: i18n.t('行号')
  },
  {
    fieldCode: 'status', // 状态 状态:1-新建,2-发货中,3-已完成,4-已取消,5-已关闭
    fieldName: i18n.t('状态')
  },
  {
    fieldCode: 'deliveryType', // 送货单类型 1-关联采购订单,2-无采购订单
    fieldName: i18n.t('送货单类型')
  },

  {
    fieldCode: 'siteCode', // 工厂 siteName code-name
    fieldName: i18n.t('工厂')
  },
  {
    fieldCode: 'supplierCode', // 供应商 supplierName code-name
    fieldName: i18n.t('供应商')
  },
  {
    fieldCode: 'itemCode', // 物料 itemName code-name
    fieldName: i18n.t('物料')
  },
  {
    fieldCode: 'unitCode', // 单位 基本单位名称 unitName code-name
    fieldName: i18n.t('单位')
  },
  {
    fieldCode: 'orderCode', // 采购订单号
    fieldName: i18n.t('采购订单号'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    fieldCode: 'lineNo', // 采购订单行号
    fieldName: i18n.t('采购订单行号'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    fieldCode: 'demandDate', // 需求日期
    fieldName: i18n.t('需求日期')
  },
  // {
  //   fieldCode: 'demandTime', // 需求时间
  //   fieldName: i18n.t('需求时间')
  // },
  {
    fieldCode: 'deliveryQuantity', // 本次送货数量
    fieldName: i18n.t('本次送货数量'),
    type: 'number'
  },
  {
    fieldCode: 'receiveQuantity', // 收货数量
    fieldName: i18n.t('收货数量'),
    type: 'number'
  },
  {
    fieldCode: 'cumReceiveQty',
    fieldName: i18n.t('wms实收数量')
  },
  {
    fieldCode: 'distributionReceiveQty',
    fieldName: i18n.t('wms首次收货数量')
  },
  {
    fieldCode: 'rejectQuantity', // 拒绝数量
    fieldName: i18n.t('拒绝数量'),
    type: 'number'
  },
  {
    fieldCode: 'rejectReason', // 拒绝原因
    fieldName: i18n.t('拒绝原因')
  },
  {
    fieldCode: 'subSiteCode',
    fieldName: i18n.t('分厂')
  },
  {
    fieldCode: 'warehouseCode', // 交货库存地点 warehouseName code-name
    fieldName: i18n.t('交货库存地点')
  },
  {
    fieldCode: 'thirdTenantCode', // thirdTenantName code-name
    fieldName: i18n.t('第三方物流商')
  },
  {
    fieldCode: 'vmiWarehouseCode', // vmiWarehouseName code-name
    fieldName: i18n.t('VMi仓')
  },
  {
    fieldCode: 'receiveAddressName', // 送货地址
    fieldName: i18n.t('送货地址')
  },
  {
    fieldCode: 'deliveryNumber', // 交货编号
    fieldName: i18n.t('交货编号')
  },
  {
    fieldCode: 'buyerOrgCode', // 采购组 buyerOrgName code-name
    fieldName: i18n.t('采购组')
  },
  {
    fieldCode: 'transferPlanName', // 计划员
    fieldName: i18n.t('计划员')
  },
  {
    width: '150',
    fieldCode: 'carNo',
    fieldName: i18n.t('车牌号')
  },
  {
    fieldCode: 'jit',
    fieldName: i18n.t('是否JIT')
  },
  {
    width: '150',
    fieldCode: 'jitDeliveryNumber',
    fieldName: i18n.t('JIT编号'),
    searchOptions: {
      renameField: 'deliveryNumber',
      operator: 'likeright'
    }
  },
  {
    fieldCode: 'batchCode', // 卷号
    fieldName: i18n.t('卷号')
  },
  {
    fieldCode: 'takeNo',
    fieldName: i18n.t('车号')
  },
  {
    width: '120',
    fieldCode: 'onWayStatus',
    fieldName: i18n.t('在途状态'),
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { text: i18n.t('未出发'), value: 0, cssClass: 'col-active' },
        { text: i18n.t('已入园'), value: 1, cssClass: 'col-active' },
        { text: i18n.t('已出发'), value: 2, cssClass: 'col-active' },
        { text: i18n.t('已报到'), value: 3, cssClass: 'col-active' },
        { text: i18n.t('保安门岗确认'), value: 11, cssClass: 'col-active' },
        { text: i18n.t('已取消'), value: 4, cssClass: 'col-active' },
        { text: i18n.t('已关闭'), value: 5, cssClass: 'col-active' },
        { text: i18n.t('已到厂待卸货'), value: 7, cssClass: 'col-active' },
        { text: i18n.t('已卸货待报检'), value: 8, cssClass: 'col-active' },
        { text: i18n.t('已离园'), value: 12, cssClass: 'col-active' }
      ]
    }
  },
  {
    fieldCode: 'driverName',
    fieldName: i18n.t('司机名称')
  },
  {
    fieldCode: 'driverNo',
    fieldName: i18n.t('司机身份证'),
    template: () => ({ template: inputView })
  },
  {
    fieldCode: 'driverPhone',
    fieldName: i18n.t('司机联系方式'),
    template: () => ({ template: inputView })
  },
  {
    fieldCode: 'number',
    fieldName: i18n.t('件数')
  },
  {
    fieldCode: 'vehicleLogistics', // 车辆物流
    fieldName: i18n.t('车辆物流'),
    template: () => {
      return {
        template: Vue.component('template-detail', {
          template: `
              <span v-if="data.takeNo" style="color: #6386c1; cursor: pointer;" @click="toLogistics(data)">{{ $t('查看物流') }}</span>`,
          methods: {
            toLogistics(e) {
              const params = {
                ztpno: e?.deliveryCode.toString(),
                busCode: e?.forecastCode,
                busNum: e.takeNo
              }
              this.$API.receiptAndDelivery.purQueryVehicleLogistics(params).then((res) => {
                if (res.code === 200) {
                  window.open(res.data.mapURL)
                }
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'receiveSupplierCode', // 收货供应商 receiveSupplierName code-name
    fieldName: i18n.t('收货供应商')
  },
  {
    fieldCode: 'productLine',
    fieldName: i18n.t('生产线')
  },
  {
    fieldCode: 'sendAddressName', // 发货地点
    fieldName: i18n.t('发货地点')
  },
  {
    fieldCode: 'sendTime', // 发货日期
    fieldName: i18n.t('发货日期')
  },
  {
    fieldCode: 'cancelPersonName', // 取消人
    fieldName: i18n.t('取消人')
  },
  {
    fieldCode: 'cancelTime', // 取消时间
    fieldName: i18n.t('取消时间')
  },
  {
    fieldCode: 'closeTime', // 取消时间
    fieldName: i18n.t('关闭时间')
  },
  {
    fieldCode: 'closePersonName', // 取消时间
    fieldName: i18n.t('关闭人')
  },
  {
    fieldCode: 'workOrderNo', // 关联工单号
    fieldName: i18n.t('关联工单号')
  },
  {
    fieldCode: 'domesticDemandFlag',
    fieldName: i18n.t('是否内需跟单'),
    width: '160',
    valueConverter: {
      type: 'map',
      map: { E: i18n.t('是'), F: i18n.t('否') }
    }
  },
  {
    fieldCode: 'saleOrderNo', // 关联销售订单号
    fieldName: i18n.t('销售订单号')
  },
  {
    fieldCode: 'saleOrderLineNo', // 关联销售订单行号
    fieldName: i18n.t('销售订单行号')
  },
  {
    fieldCode: 'domesticDemandCode',
    fieldName: i18n.t('内需单号'),
    width: '150'
  },
  {
    fieldCode: 'bomCode', // BOM号
    fieldName: i18n.t('BOM号')
  },
  {
    fieldCode: 'productCode', // 关联产品代码
    fieldName: i18n.t('关联产品代码')
  },
  {
    fieldCode: 'workCenterCode', // 工作中心 workCenterName code-name
    fieldName: i18n.t('工作中心')
  },
  {
    fieldCode: 'processName', // 工序名称
    fieldName: i18n.t('工序名称')
  },
  {
    fieldCode: 'limitQuantity', // 限量数量
    fieldName: i18n.t('限量数量')
  },
  {
    fieldCode: 'warehouseClerkName', // 仓管员
    fieldName: i18n.t('仓管员')
  },
  {
    fieldCode: 'dispatcherName', // 调度员
    fieldName: i18n.t('调度员')
  },
  {
    fieldCode: 'collectorMark', // 代收标识
    fieldName: i18n.t('代收标识')
  },
  {
    fieldCode: 'collectorName', // 代收人
    fieldName: i18n.t('代收人')
  },
  {
    fieldCode: 'syncDeliveryNoStatus',
    fieldName: i18n.t('创建交货单是否成功')
  },
  {
    fieldCode: 'syncDeliveryNoDesc',
    fieldName: i18n.t('创建交货单失败信息')
  },
  {
    fieldCode: 'deliveryNo', // 交货单号
    fieldName: i18n.t('交货单号')
  },
  {
    fieldCode: 'deliveryItemNo', // 交货单号行号
    fieldName: i18n.t('交货单行号')
  },
  {
    fieldCode: 'inputDate',
    fieldName: i18n.t('凭证创建日期')
  },
  // {
  //   fieldCode: 'inputTime',
  //   fieldName: i18n.t('凭证创建时间'),
  //   ignore: true
  // },
  {
    fieldCode: 'postingDate', //
    fieldName: i18n.t('过账日期')
  },
  {
    fieldCode: 'companyCode', // 公司 companyName code-name
    fieldName: i18n.t('公司')
  },
  {
    fieldCode: 'createUserName', // 创建人
    fieldName: i18n.t('创建人')
  },
  {
    fieldCode: 'createTime', // 创建时间
    fieldName: i18n.t('创建时间')
  },
  {
    fieldCode: 'outboundWarehouseCode',
    fieldName: i18n.t('出库货位')
  },
  {
    fieldCode: 'deliveryRemark', // 送货单备注
    fieldName: i18n.t('送货单备注')
  },
  {
    fieldCode: 'remark', // 行备注
    fieldName: i18n.t('行备注')
  },
  {
    fieldCode: 'parkTime', // 已入园时间
    fieldName: i18n.t('已入园时间')
  },
  {
    fieldCode: 'outTime', // 已出发时间
    fieldName: i18n.t('已出发时间')
  },
  {
    fieldCode: 'reportedTime', // 已报到时间
    fieldName: i18n.t('已报到时间')
  },
  {
    fieldCode: 'unloadTime', // 已到厂待卸货时间
    fieldName: i18n.t('已到厂待卸货时间')
  },
  {
    fieldCode: 'inspectionTime', // 已卸货待报检时间
    fieldName: i18n.t('已卸货待报检时间')
  },
  {
    fieldCode: 'goAwayTime', // 已离园时间
    fieldName: i18n.t('已离园时间')
  }
]
