<template>
  <!-- 送货单-列表-采方 -->
  <div class="full-height vertical-flex-box">
    <!-- 列模板 -->
    <mt-tabs
      :e-tab="false"
      :selected-item="tabIndex"
      :data-source="tabSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <div style="height: calc(100% - 40px)">
      <mt-template-page
        ref="templateRef"
        :current-tab="currentTab"
        :hidden-tabs="false"
        :permission-obj="permissionObj"
        v-show="tabIndex === 0"
        :template-config="templateConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
      >
      </mt-template-page>
      <mt-template-page
        ref="templateRef2"
        :current-tab="currentTab"
        :hidden-tabs="false"
        :permission-obj="permissionObj"
        v-show="tabIndex === 1"
        :template-config="templateConfig2"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle1"
      >
      </mt-template-page>
      <mt-template-page
        ref="templateRef3"
        :current-tab="currentTab"
        :hidden-tabs="false"
        :permission-obj="permissionObj"
        v-show="tabIndex === 2"
        :template-config="templateConfig3"
        @handleCustomReset="handleCustomReset"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle1"
      >
        <template v-slot:quick-search-form>
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item :label="$t('送货单号')" prop="deliveryCode">
              <mt-input
                v-model="searchFormModel.deliveryCode"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('状态')" prop="status">
              <mt-multi-select
                v-model="searchFormModel.status"
                :data-source="StatusOptions"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                :show-select-all="true"
                style="flex: 1"
              />
            </mt-form-item>
            <mt-form-item :label="$t('创建时间')" prop="createTime">
              <mt-date-range-picker
                v-model="searchFormModel.createTime"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'createTime')"
              />
            </mt-form-item>
            <mt-form-item :label="$t('物料编码')" prop="itemCode">
              <mt-input
                v-model="searchFormModel.itemCode"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('物料名称')" prop="itemName">
              <mt-input
                v-model="searchFormModel.itemName"
                :placeholder="$t('支持模糊搜索')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('采购订单号')" prop="orderCode">
              <mt-input
                v-model="searchFormModel.orderCode"
                :placeholder="$t('支持粘贴多个精准查询')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('采购订单行号')" prop="lineNo">
              <mt-input
                v-model="searchFormModel.lineNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('供应商')" prop="supplierCodes">
              <RemoteAutocomplete
                v-model="searchFormModel.supplierCodes"
                url="/masterDataManagement/tenant/supplier/paged-query"
                :multiple="true"
                :placeholder="$t('请选择')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('公司')" prop="companyCodes">
              <RemoteAutocomplete
                v-model="searchFormModel.companyCodes"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01']
                }"
                multiple
                :placeholder="$t('请选择公司')"
                :fields="{ text: 'orgName', value: 'orgCode' }"
              />
            </mt-form-item>
            <mt-form-item :label="$t('工厂')" prop="siteCodes">
              <RemoteAutocomplete
                v-model="searchFormModel.siteCodes"
                :url="$API.masterData.getSiteListUrl"
                :multiple="true"
                :placeholder="$t('请选择')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                :search-fields="['siteName', 'siteCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('分厂')" prop="subSiteCodes">
              <RemoteAutocomplete
                v-model="searchFormModel.subSiteCodes"
                url="/srm-purchase-execute/tenant/buyerJitInfo/condition?conditionType=subSiteCode"
                :multiple="true"
                :placeholder="$t('请选择')"
                :fields="{ text: 'subSiteName', value: 'subSiteCode' }"
                :search-fields="['subSiteName', 'subSiteCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('交货库存地点')" prop="warehouseCodes">
              <RemoteAutocomplete
                v-model="searchFormModel.warehouseCodes"
                url="/masterDataManagement/tenant/location/paged-query"
                :multiple="true"
                :placeholder="$t('请选择')"
                :fields="{ text: 'locationName', value: 'locationCode' }"
                :search-fields="['locationName', 'locationCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('VMi仓')" prop="vmiWarehouseCodes">
              <RemoteAutocomplete
                v-model="searchFormModel.vmiWarehouseCodes"
                url="/srm-purchase-execute/tenant/buyerOrderDelivery/query/vmi/warehouse/list"
                :multiple="true"
                :placeholder="$t('请选择')"
                :fields="{ text: 'vmiWarehouseName', value: 'vmiWarehouseCode' }"
                :params-key="'vmiWarehouseCode'"
                records-position="data"
              />
            </mt-form-item>
            <mt-form-item :label="$t('采购组')" prop="buyerOrgCodes">
              <RemoteAutocomplete
                v-model="searchFormModel.buyerOrgCodes"
                :url="$API.masterData.getBusinessGroupUrl"
                :multiple="true"
                :placeholder="$t('请选择')"
                :fields="{ text: 'groupName', value: 'groupCode' }"
                :search-fields="['groupName', 'groupCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('计划员')" prop="transferPlanCode">
              <RemoteAutocomplete
                v-model="searchFormModel.transferPlanCode"
                url="/masterDataManagement/tenant/employee/paged-query"
                :placeholder="$t('请选择')"
                :fields="{ text: 'employeeName', value: 'employeeCode' }"
                :search-fields="['employeeName', 'employeeCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('送货单类型')" prop="deliveryType">
              <mt-multi-select
                style="flex: 1"
                v-model="searchFormModel.deliveryType"
                :data-source="DeliveryTypeOptions"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                :show-select-all="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('交货单号')" prop="deliveryNo">
              <mt-input
                v-model="searchFormModel.deliveryNo"
                :placeholder="$t('支持粘贴多个精准查询')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('过账日期')" prop="postingDate">
              <mt-date-range-picker
                v-model="searchFormModel.postingDate"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'postingDate')"
              />
            </mt-form-item>
            <mt-form-item :label="$t('是否jit')" prop="jit">
              <mt-select
                style="flex: 1"
                v-model="searchFormModel.jit"
                :data-source="isJitOptions"
                :fields="{ text: 'label', value: 'value' }"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('在途状态')" prop="onWayStatus">
              <mt-multi-select
                style="flex: 1"
                v-model="searchFormModel.onWayStatus"
                :data-source="onWayStatusOptions"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                :show-select-all="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('司机联系方式')" prop="driverPhone">
              <mt-input
                v-model="searchFormModel.driverPhone"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('本次送货数量')" prop="deliveryQuantity">
              <div style="display: flex">
                <div class="operator-list">
                  <mt-select
                    v-model="searchFormModel.deliveryQuantity.operator"
                    :data-source="operatorOptions"
                    popup-width="50px"
                  ></mt-select>
                </div>
                <div class="custom-input-number">
                  <mt-input-number
                    v-model="searchFormModel.deliveryQuantity.number"
                    type="number"
                    :show-spin-button="false"
                    :show-clear-button="true"
                    :placeholder="$t('请输入')"
                  />
                </div>
              </div>
            </mt-form-item>
            <mt-form-item :label="$t('发货日期')" prop="sendTime">
              <mt-date-range-picker
                v-model="searchFormModel.sendTime"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'sendTime')"
              />
            </mt-form-item>
            <mt-form-item :label="$t('收货供应商')" prop="receiveSupplierCodes">
              <RemoteAutocomplete
                v-model="searchFormModel.receiveSupplierCodes"
                url="/masterDataManagement/tenant/supplier/paged-query"
                :multiple="true"
                :placeholder="$t('请选择')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('送货地址')" prop="receiveAddressName">
              <mt-input
                v-model="searchFormModel.receiveAddressName"
                :placeholder="$t('支持模糊搜索')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('取消人')" prop="cancelPersonCode">
              <RemoteAutocomplete
                v-model="searchFormModel.cancelPersonCode"
                url="/masterDataManagement/tenant/employee/paged-query"
                :placeholder="$t('请选择')"
                :fields="{ text: 'employeeName', value: 'employeeCode' }"
                :search-fields="['employeeName', 'employeeCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('取消时间')" prop="cancelTime">
              <mt-date-range-picker
                v-model="searchFormModel.cancelTime"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'cancelTime')"
              />
            </mt-form-item>
            <mt-form-item :label="$t('拒绝原因')" prop="rejectReason">
              <mt-input
                v-model="searchFormModel.rejectReason"
                :placeholder="$t('支持模糊搜索')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('代收标识')" prop="collectorMark">
              <mt-input
                v-model="searchFormModel.collectorMark"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('代收人')" prop="collectorName">
              <RemoteAutocomplete
                v-model="searchFormModel.collectorName"
                url="/masterDataManagement/tenant/employee/paged-query"
                :placeholder="$t('请选择')"
                :fields="{ text: 'employeeCode', value: 'employeeName' }"
                :search-fields="['employeeName', 'employeeCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('卷号')" prop="batchCode">
              <mt-input
                v-model="searchFormModel.batchCode"
                :placeholder="$t('支持粘贴多个精准查询')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="takeNo" :label="$t('车号')">
              <mt-input
                v-model="searchFormModel.takeNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('第三方物流商')" prop="thirdTenantCodes">
              <RemoteAutocomplete
                v-model="searchFormModel.thirdTenantCodes"
                url="/masterDataManagement/tenant/supplier/paged-query"
                :multiple="true"
                :placeholder="$t('请选择')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('创建交货单是否成功')" prop="syncDeliveryNoStatus">
              <mt-select
                v-model="searchFormModel.syncDeliveryNoStatus"
                :data-source="SyncDeliveryNoStatusOptions"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                style="flex: 1"
              />
            </mt-form-item>
            <mt-form-item :label="$t('创建交货单失败信息')" prop="syncDeliveryNoDesc">
              <mt-input
                v-model="searchFormModel.syncDeliveryNoDesc"
                :placeholder="$t('支持模糊搜索')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('是否内需跟单')" prop="domesticDemandFlag">
              <mt-select
                v-model="searchFormModel.domesticDemandFlag"
                :data-source="[
                  { text: $t('是'), value: 'E' },
                  { text: $t('否'), value: 'F' }
                ]"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                style="flex: 1"
              />
            </mt-form-item>
            <mt-form-item :label="$t('销售订单号')" prop="saleOrderNo">
              <mt-input
                v-model="searchFormModel.saleOrderNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('销售订单行号')" prop="saleOrderLineNo">
              <mt-input
                v-model="searchFormModel.saleOrderLineNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('内需单号')" prop="domesticDemandCode">
              <mt-input
                v-model="searchFormModel.domesticDemandCode"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>

            <!-- <mt-form-item prop="carNo" :label="$t('车牌号')">
              <mt-input
                v-model="searchFormModel.carNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="deliveryNumber" :label="$t('JIT编号')">
              <mt-input
                v-model="searchFormModel.deliveryNumber"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="deliveryLineNo" :label="$t('行号')">
              <mt-input
                v-model="searchFormModel.deliveryLineNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="driverName" :label="$t('司机名称')">
              <mt-input
                v-model="searchFormModel.driverName"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="driverNo" :label="$t('司机身份证')">
              <mt-input
                v-model="searchFormModel.driverNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="number" :label="$t('件数')">
              <div style="display: flex">
                <div class="operator-list">
                  <mt-select
                    v-model="searchFormModel.number.operator"
                    :data-source="operatorOptions"
                    popup-width="50px"
                  ></mt-select>
                </div>
                <div class="custom-input-number">
                  <mt-input-number
                    v-model="searchFormModel.number.number"
                    type="number"
                    :show-spin-button="false"
                    :show-clear-button="true"
                    :placeholder="$t('请输入')"
                  />
                </div>
              </div>
            </mt-form-item>
            <mt-form-item prop="receiveQuantity" :label="$t('收货数量')">
              <div style="display: flex">
                <div class="operator-list">
                  <mt-select
                    v-model="searchFormModel.receiveQuantity.operator"
                    :data-source="operatorOptions"
                    popup-width="50px"
                  ></mt-select>
                </div>
                <div class="custom-input-number">
                  <mt-input-number
                    v-model="searchFormModel.receiveQuantity.number"
                    type="number"
                    :show-spin-button="false"
                    :show-clear-button="true"
                    :placeholder="$t('请输入')"
                  />
                </div>
              </div>
            </mt-form-item>
            <mt-form-item prop="rejectQuantity" :label="$t('拒绝数量')">
              <div style="display: flex">
                <div class="operator-list">
                  <mt-select
                    v-model="searchFormModel.rejectQuantity.operator"
                    :data-source="operatorOptions"
                    popup-width="50px"
                  ></mt-select>
                </div>
                <div class="custom-input-number">
                  <mt-input-number
                    v-model="searchFormModel.rejectQuantity.number"
                    type="number"
                    :show-spin-button="false"
                    :show-clear-button="true"
                    :placeholder="$t('请输入')"
                  />
                </div>
              </div>
            </mt-form-item>
            <mt-form-item prop="productLine" :label="$t('生产线')">
              <mt-input
                v-model="searchFormModel.productLine"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="deliveryRemark" :label="$t('送货单备注')">
              <mt-input
                v-model="searchFormModel.deliveryRemark"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="deliveryNumber" :label="$t('交货编号')">
              <mt-input
                v-model="searchFormModel.deliveryNumber"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('单位')" prop="unitCode">
              <RemoteAutocomplete
                v-model="searchFormModel.unitCode"
                :url="$API.masterData.getUnitUrl"
                :placeholder="$t('请选择')"
                :fields="{ text: 'unitName', value: 'unitCode' }"
                :search-fields="['unitName', 'unitCode']"
              />
            </mt-form-item>
            <mt-form-item prop="workOrderNo" :label="$t('关联工单号')">
              <mt-input
                v-model="searchFormModel.workOrderNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="saleOrderNo" :label="$t('关联销售订单号')">
              <mt-input
                v-model="searchFormModel.saleOrderNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="saleOrderLineNo" :label="$t('关联销售订单行号')">
              <mt-input
                v-model="searchFormModel.saleOrderLineNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="bomCode" :label="$t('BOM号')">
              <mt-input
                v-model="searchFormModel.bomCode"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="productCode" :label="$t('关联产品代码')">
              <mt-input
                v-model="searchFormModel.productCode"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="workCenterCodes" :label="$t('工作中心')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.workCenterCodes"
                url="/masterDataManagement/tenant/work-center/paged-query"
                multiple
                :placeholder="$t('请选择工作中心')"
                :fields="{ text: 'workCenterName', value: 'workCenterCode' }"
                :search-fields="['workCenterName', 'workCenterCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="processName" :label="$t('工序名称')">
              <mt-input
                v-model="searchFormModel.processName"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="limitQuantity" :label="$t('限量数量')">
              <div style="display: flex">
                <div class="operator-list">
                  <mt-select
                    v-model="searchFormModel.limitQuantity.operator"
                    :data-source="operatorOptions"
                    popup-width="50px"
                  ></mt-select>
                </div>
                <div class="custom-input-number">
                  <mt-input-number
                    v-model="searchFormModel.limitQuantity.number"
                    type="number"
                    :show-spin-button="false"
                    :show-clear-button="true"
                    :placeholder="$t('请输入')"
                  />
                </div>
              </div>
            </mt-form-item>
            <mt-form-item :label="$t('仓管员')" prop="warehouseClerkCodes">
              <RemoteAutocomplete
                v-model="searchFormModel.warehouseClerkCodes"
                url="/masterDataManagement/tenant/employee/paged-query"
                multiple
                :placeholder="$t('请选择')"
                :fields="{ text: 'employeeName', value: 'employeeCode' }"
                :search-fields="['employeeName', 'employeeCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('调度员')" prop="dispatcherCode">
              <RemoteAutocomplete
                v-model="searchFormModel.dispatcherCode"
                url="/masterDataManagement/tenant/employee/paged-query"
                :placeholder="$t('请选择')"
                :fields="{ text: 'employeeName', value: 'employeeCode' }"
                :search-fields="['employeeName', 'employeeCode']"
              />
            </mt-form-item>
            <mt-form-item prop="closeTime" :label="$t('关闭时间')">
              <mt-date-range-picker
                v-model="searchFormModel.closeTime"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'closeTime')"
              />
            </mt-form-item>
            <mt-form-item :label="$t('创建人')" prop="createUserName">
              <RemoteAutocomplete
                v-model="searchFormModel.createUserName"
                url="/masterDataManagement/tenant/employee/paged-query"
                :placeholder="$t('请选择')"
                :fields="{ text: 'employeeCode', value: 'employeeName' }"
                :search-fields="['employeeName', 'employeeCode']"
              />
            </mt-form-item> -->
          </mt-form>
        </template>
      </mt-template-page>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main.js'

const mainTabList = [
  {
    title: i18n.t('送货单列表')
  },
  {
    title: i18n.t('送货单明细')
  },
  {
    title: i18n.t('送货单明细-新')
  }
]
import { formatTableColumnData, verifyStatus } from './config/index'
import {
  ColumnDataTab1,
  ColumnDataTab2,
  Toolbar,
  Tab,
  Status,
  StatusOptions,
  onWayStatusOptions,
  operatorOptions,
  DeliveryTypeOptions,
  SyncDeliveryNoStatusOptions
} from './config/constant'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'
import dayjs from 'dayjs'
import Vue from 'vue'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  components: {},
  data() {
    const currentTab = JSON.parse(localStorage.getItem('tabIndex')) ?? 0
    const startDate = dayjs().subtract(1, 'month')

    return {
      StatusOptions,
      onWayStatusOptions,
      operatorOptions,
      DeliveryTypeOptions,
      SyncDeliveryNoStatusOptions,
      isJitOptions: [
        { label: this.$t('否'), value: 0 },
        { label: this.$t('是'), value: 1 }
      ],
      searchFormModel: {
        sourceFrom: 0, // 来源事业部,0-白电,空调,3-泛智屏
        tenantType: 1, // 租户类型 1 采方 2 供方
        createTime: [new Date(startDate), new Date()],
        createTimeTo: this.getUnix(new Date(dayjs(new Date()).format('YYYY-MM-DD 23:59:59'))),
        createTimeFrom: this.getUnix(
          new Date(dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00'))
        ),
        number: {
          number: null,
          operator: null
        },
        deliveryQuantity: {
          number: null,
          operator: null
        },
        receiveQuantity: {
          number: null,
          operator: null
        },
        rejectQuantity: {
          number: null,
          operator: null
        },
        limitQuantity: {
          number: null,
          operator: null
        }
      },
      tabIndex: 0,
      tabSource: mainTabList,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      currentTab, // 当前加载的 Tab 默认 0
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'List', permissionCode: 'T_02_0024' },
          { dataPermission: 'Details', permissionCode: 'T_02_0025' },
          { dataPermission: 'Details', permissionCode: 'T_02_0025' }
        ]
      },
      templateConfig: [
        {
          // title: this.$t("送货单列表"),
          // dataPermission: "List",
          activatedRefresh: false,
          showArchive: true, // 是否显示归档查询
          // alwaysShow: true,
          // permissionCode: "T_02_0024",
          toolbar: Toolbar[0],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId: this.$tableUUID.receiptAndDelivery.deliverList.list,
          grid: {
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [10, 20, 50, 100, 200, 500, 1000],
              totalRecordsCount: 0
            },
            allowPaging: true, // 分页
            lineSelection: 0, // 选项列
            lineIndex: 1, // 序号列
            frozenColumns: 1, // 冻结第一列
            columnData: formatTableColumnData({
              data: ColumnDataTab1,
              tab: Tab.list,
              type: 'title'
            }),
            dataSource: [],
            asyncConfig: {
              ignoreDefaultSearch: true,
              url: `${BASE_TENANT}/buyerOrderDelivery/query` // 采方送货单-采方查询供方送货单
            }
          }
        }
      ],
      templateConfig2: [
        {
          // title: this.$t("送货明细"),
          // dataPermission: "Details",
          // permissionCode: "T_02_0025",
          activatedRefresh: false,
          showArchive: true, // 是否显示归档查询
          // alwaysShow: true,
          toolbar: [
            {
              id: 'export',
              icon: 'icon_solid_Import',
              permission: ['O_02_1081'],
              title: this.$t('导出')
            },
            {
              id: 'copy',
              icon: '',
              title: this.$t('复制送货单号')
            }
          ],
          useToolTemplate: false, // 使用预置表格操作按钮(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId: this.$tableUUID.receiptAndDelivery.deliverList.details,
          grid: {
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [10, 20, 50, 100, 200, 500, 1000],
              totalRecordsCount: 0
            },
            allowPaging: true, // 分页
            lineSelection: 0, // 选项列
            lineIndex: 1, // 序号列
            frozenColumns: 1, // 冻结第一列
            columnData: formatTableColumnData({
              data: ColumnDataTab2,
              tab: Tab.details
            }),
            dataSource: [],
            asyncConfig: {
              ignoreDefaultSearch: true,
              url: `${BASE_TENANT}/buyerOrderDelivery/query/item/page` // 采方送货单-采方查询送货单明细分页
            }
          }
        }
      ],
      templateConfig3: [
        {
          isUseCustomSearch: true, // 是否使用自定义查询
          isCustomSearchRules: true,
          showArchive: true, // 是否显示归档查询
          activatedRefresh: false,
          // alwaysShow: true,
          toolbar: [
            {
              id: 'export1',
              icon: 'icon_solid_Import',
              permission: ['O_02_1081'],
              title: this.$t('导出')
            },
            {
              id: 'copy',
              icon: '',
              title: this.$t('复制送货单号')
            }
          ],
          useToolTemplate: false, // 使用预置表格操作按钮(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId: 'd5ebe633-0e76-4139-94e7-956dee9ac8b8',
          grid: {
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [10, 20, 50, 100, 200, 500, 1000],
              totalRecordsCount: 0
            },
            allowPaging: true, // 分页
            lineSelection: 0, // 选项列
            lineIndex: 1, // 序号列
            frozenColumns: 1, // 冻结第一列
            columnData: formatTableColumnData({
              data: ColumnDataTab2,
              tab: Tab.details
            }),
            dataSource: [],
            asyncConfig: {
              url: `/statistics/tenant/buyer/delivery/item/view/v1/page`, // 采方送货单-采方查询送货单明细分页
              serializeList: (list) => {
                if (list.length > 0) {
                  list.forEach((item) => {
                    let inputDate = item.inputDate
                    if (item.inputDate && item.inputTime) {
                      inputDate = `${inputDate.split(' ')[0]} ${item.inputTime.split(' ')[1]}`
                    }
                    item.inputDate = inputDate
                  })
                }
                return list
              }
            }
          }
        }
      ]
    }
  },
  mounted() {
    if (this.$route.query.from === 'mytodo2') {
      this.tabIndex = 1
      const { rules, defaultRules } = JSON.parse(sessionStorage.getItem('todoDetail')) || {
        rules: [],
        defaultRules: []
      }
      this.templateConfig2[0].grid.asyncConfig = {
        ignoreDefaultSearch: false,
        url: `${BASE_TENANT}/buyerOrderDelivery/query/item/page`,
        rules: rules || [],
        defaultRules: defaultRules
      }
    }
  },
  beforeDestroy() {
    localStorage.removeItem('tabIndex')
  },
  methods: {
    dataTimeChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'From'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[flag + 'To'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[flag + 'From'] = null
        this.searchFormModel[flag + 'To'] = null
      }
    },
    getUnix(val) {
      return new Date(val).getTime()
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'tenantType') {
            this.searchFormModel[key] = 1
          }
          if (key === 'sourceFrom') {
            this.searchFormModel[key] = 0
          }
          if (
            key === 'number' ||
            key === 'deliveryQuantity' ||
            key === 'receiveQuantity' ||
            key === 'rejectQuantity' ||
            key === 'limitQuantity'
          ) {
            this.searchFormModel[key] = {
              number: null,
              operator: null
            }
          }
        }
      }
    },
    handleSelectTab(e) {
      this.tabIndex = e
      if (e == 1) {
        setTimeout(() => {
          // 新版本template-page组件已经不再使用 resizeGridHeight 方法，为了兼容到空调白电已经上线内容所以作此判断 -- lbj-2023.05.23
          if (this.$refs.templateRef2.getCurrentUsefulRef().pluginRef.resizeGridHeight) {
            this.$refs.templateRef2.getCurrentUsefulRef().pluginRef.resizeGridHeight()
          }
        }, 0)
      }
    },
    // ToolBar
    handleClickToolBar(args) {
      if (args.toolbar.id == 'exportList') {
        const asyncParams = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.asyncParams || {}
        const params = {
          ...asyncParams
        } // 筛选条件
        if (params?.page) {
          this.$store.commit('startLoading')
          this.$API.receiptAndDelivery.buyerOrderDeliveryPurExportDelivery(params).then((res) => {
            this.$store.commit('endLoading')
            const fileName = getHeadersFileName(res)
            download({ fileName: `${fileName}`, blob: res.data })
          })
        } else {
          this.$toast({
            type: 'warning',
            content: this.$t('请先查询数据')
          })
        }
        return
      }
      if (args.toolbar.id == 'export') {
        const queryBuilderRules =
          this.$refs.templateRef2.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        console.log(queryBuilderRules)
        const params = {
          page: { current: 1, size: 5000 },
          ...queryBuilderRules
        } // 筛选条件
        this.$store.commit('startLoading')
        this.$API.receiptAndDelivery.buyerOrderDeliveryQueryExport(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
      if (args.toolbar.id == 'export1') {
        let obj = JSON.parse(sessionStorage.getItem(this.templateConfig3[0].gridId))?.visibleCols
        const headerMap = {}
        if (obj !== undefined && obj.length) {
          obj?.forEach((i) => {
            if (i.field !== 'customChecked' && i.field !== 'id') {
              headerMap[i.field] = i.headerText
            }
            if (i.field === 'itemCode') {
              headerMap['itemName'] = this.$t('物料名称')
            }
          })
        } else {
          this.templateConfig3[0].grid.columnData?.forEach((i) => {
            if (i.field !== 'customChecked' && i.field !== 'id') {
              headerMap[i.field] = i.headerText
            }
            if (i.field === 'itemCode') {
              headerMap['itemName'] = this.$t('物料名称')
            }
          })
        }
        const params = {
          headerMap,
          page: { current: 1, size: 5000 },
          ...this.searchFormModel
        } // 筛选条件
        this.$store.commit('startLoading')
        this.$API.receiptAndDelivery.buyerOrderDeliveryQueryNewExport(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
      const { toolbar, gridRef } = args
      const selectRows = gridRef.getMtechGridRecords()
      const commonToolbar = [
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]
      if (selectRows.length === 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedId = []
      selectRows.forEach((item) => selectedId.push(item.id))

      if (toolbar.id === 'unlock') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认解锁选中的记录吗？')
          },
          success: () => {
            this.handleUnlock(selectRows)
          }
        })
      }

      if (toolbar.id === 'copy') {
        this.handleCopy(selectRows)
      }

      if (toolbar.id === 'DeliveryNoteClose') {
        // 送货单列表-关闭
        this.handleDeliveryNoteClose({ data: selectRows, selectedId })
      } else if (toolbar.id === 'DeliveryNoteCancel') {
        // 送货单列表-取消
        this.handleDeliveryNoteCancel({ data: selectRows, selectedId })
      }
      const _id = []
      gridRef.getMtechGridRecords().map((item) => {
        _id.push(item.id)

        // _sourceStatus.push(item.sourceStatus),
        // _remainingQuantity.push(item.remainingQuantity);
      })

      if (toolbar.id === 'print') {
        for (let item of selectRows) {
          if (item.status === 4 || item.status === 5) {
            this.$toast({
              content: this.$t('不可以打印已取消和已关闭的单据'),
              type: 'warning'
            })
            return
          }
        }
        this.print(_id, 'kt')
      }

      if (toolbar.id === 'printBd') {
        for (let item of selectRows) {
          if (item.status === 4 || item.status === 5) {
            this.$toast({
              content: this.$t('不可以打印已取消和已关闭的单据'),
              type: 'warning'
            })
            return
          }
        }
        this.print(_id, 'bd')
      }
      if (args.toolbar.id === 'synchronous') {
        if (selectRows.length <= 0) {
          this.$toast({ content: this.$t('请选择数据'), type: 'warning' })
          return
        } else {
          this.synchronousWms(selectRows)
        }
      } else if (args.toolbar.id === 'createSapdeliveryNode') {
        if (selectRows.length <= 0) {
          this.$toast({ content: this.$t('请选择数据'), type: 'warning' })
          return
        } else {
          this.createSapdelivery(selectRows)
        }
      }
    },
    handleUnlock(selectRows) {
      this.$API.receiptAndDelivery
        .buyerOrderDeliveryUnlock(selectRows.map((i) => i.deliveryCode))
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('解锁成功'), type: 'success' })
            this.refreshColumns()
          }
        })
    },
    handleCopy(selectRows) {
      const deliveryCodeList = selectRows.map((i) => {
        return i.deliveryCode
      })
      const str = [...new Set(deliveryCodeList)].join(' ')
      this.copyToClipboard(str)
    },
    copyToClipboard(str) {
      const el = document.createElement('textarea')
      el.value = str
      el.setAttribute('readonly', '')
      el.style.position = 'absolute'
      el.style.left = '-9999px'
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)
    },
    print(_id, type) {
      if (type === 'kt') {
        this.$API.receiptAndDelivery.buyerOrderDeliveryPrintHtml(_id).then((res) => {
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(
            new Blob([content], { type: 'text/html;charset=utf-8' })
          )
          // window.open(this.pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
          return
        })
      } else if (type === 'bd') {
        this.$API.receiptAndDelivery.buyerOrderDeliveryPrint(_id).then((res) => {
          console.log(res.data)

          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = function () {
              console.log('======', reader)
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }

            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
          // window.open(this.pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
      }
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    // CellTool
    handleClickCellTool(args) {
      const { tool, data } = args
      if (tool.id === 'DeliveryNoteClose') {
        // 送货单列表-关闭
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认关闭选中的数据？')
          },
          success: () => {
            // 采方送货单-采方关闭送货单
            this.postBuyerOrderDeliveryClose({
              selectedId: [data.id]
            })
          }
        })
      } else if (tool.id === 'DeliveryNoteCancel') {
        // 送货单列表-取消
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认取消选中的数据？')
          },
          success: () => {
            // 采方送货单-采方取消送货单
            this.postBuyerOrderDeliveryCancel({
              selectedId: [data.id]
            })
          }
        })
      }
    },
    // 送货单列表-关闭
    handleDeliveryNoteClose(args) {
      const { data, selectedId } = args
      const { valid, status: dataStatus } = verifyStatus(data)
      if (!valid) {
        this.$toast({
          content: this.$t('请选择相同状态的数据'),
          type: 'warning'
        })
      } else if (valid && dataStatus != Status.shipping) {
        this.$toast({
          content: this.$t('请选择发货中状态的数据'),
          type: 'warning'
        })
      } else if (valid && dataStatus == Status.shipping) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认关闭选中的数据？')
          },
          success: () => {
            // 采方送货单-采方关闭送货单
            this.postBuyerOrderDeliveryClose({
              selectedId
            })
          }
        })
      }
    },
    // 送货单列表-取消
    handleDeliveryNoteCancel(args) {
      const { data, selectedId } = args
      const { valid, status: dataStatus } = verifyStatus(data)
      if (!valid) {
        this.$toast({
          content: this.$t('请选择相同状态的数据'),
          type: 'warning'
        })
      } else if (valid && dataStatus != Status.shipping) {
        this.$toast({
          content: this.$t('请选择发货中状态的数据'),
          type: 'warning'
        })
      } else if (valid && dataStatus == Status.shipping) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认取消选中的数据？')
          },
          success: () => {
            // 采方送货单-采方取消送货单
            this.postBuyerOrderDeliveryCancel({
              selectedId
            })
          }
        })
      }
    },
    // CellTitle
    handleClickCellTitle(args) {
      const { field, data } = args
      if (field === 'deliveryCode') {
        // 送货单号 click
        this.goToDetail({
          headerInfo: data
        })
      }
    },
    // CellTitle
    handleClickCellTitle1(args) {
      const { field, data } = args
      if (field === 'deliveryCode') {
        this.getDeliverDataById(data.deliveryId)
      }
    },
    // 根据id查询主单数据
    getDeliverDataById(id) {
      const params = {
        condition: 'and',
        page: { current: 1, size: 20 },
        rules: [
          {
            field: 'id',
            operator: 'equal',
            value: id
          }
        ]
      }
      this.$API.receiptAndDelivery
        .getDeliveyData(params)
        .then((res) => {
          if (res?.code == 200) {
            // 跳转详情页
            this.goToDetail({
              headerInfo: res?.data?.records[0]
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 跳转到 送货单-详情-采方
    goToDetail(data) {
      const tabIndex = this.$refs.templateRef.getCurrentTabRef().tabIndex
      // 将 lastTabIndex 放到 localStorage
      localStorage.setItem('lastTabIndex', JSON.stringify(tabIndex))
      const { headerInfo } = data
      const deliverListData = {
        headerInfo // 头部信息
      }
      // 将信息放到 localStorage 详情页 读
      localStorage.setItem('deliverListData', JSON.stringify(deliverListData))
      // 跳转 送货单-详情-采方
      this.$router.push({
        name: 'deliver-detail',
        query: {}
      })
    },
    // 采方送货单-采方关闭送货单
    postBuyerOrderDeliveryClose(args) {
      const { selectedId } = args
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postBuyerOrderDeliveryClose(selectedId)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方送货单-采方取消送货单
    postBuyerOrderDeliveryCancel(args) {
      const { selectedId } = args
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postBuyerOrderDeliveryCancel(selectedId)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    },
    // 同步WMS
    synchronousWms(data) {
      let _ids = []
      let _flag = true
      data.forEach((item) => {
        if (item.wmsSyncStatus === 1) {
          _flag = false
        }
        _ids.push(item.id)
      })
      if (!_flag) {
        this.$toast({
          content: this.$t('送货单已成功同步WMS，无需重复同步'),
          type: 'warning'
        })
        return
      }
      this.$API.purchaseCoordination.deliveryPurSynchronousWms(_ids).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('同步成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('同步失败'), type: 'warning' })
        }
      })
    },
    // 创建SAP交货单
    createSapdelivery(data) {
      let _ids = []
      data.forEach((item) => {
        _ids.push(item.id)
      })
      this.$store.commit('startLoading')
      this.$API.purchaseCoordination
        .createSapdeliveryNote(_ids)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t(res.msg || '创建成功'), type: 'success' })
            this.refreshColumns()
          } else {
            this.$toast({ content: this.$t(res.msg || '创建失败'), type: 'warning' })
          }
        })
        .catch((err) => {
          this.$toast({
            type: 'error',
            content: this.$t(err.msg || '创建失败')
          })
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    }
  }
}
</script>

<style lang="scss" scoped></style>
