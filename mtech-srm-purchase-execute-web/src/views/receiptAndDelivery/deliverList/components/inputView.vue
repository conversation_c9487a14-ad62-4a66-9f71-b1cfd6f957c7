<template>
  <div>
    <span>{{ data[data.column.field] }}</span>
    <vxe-Icon
      v-if="data.encryptMap && data.encryptMap[data.column.field]"
      :name="data[data.column.field].includes('**') ? 'eye-fill-close' : 'eye-fill'"
      class="eyeStyle"
      @click="checkInfo"
    ></vxe-Icon>
  </div>
</template>
<script>
import { Icon } from 'vxe-table'
export default {
  components: {
    vxeIcon: Icon
  },
  data() {
    return {
      data: {},
      oldValue: null
    }
  },
  mounted() {
    console.log(this.data, 'data')
    this.oldValue = this.data[this.data.column.field]
  },
  methods: {
    checkInfo() {
      let val = this.data[this.data.column.field]
      let _val = this.data.encryptMap[this.data.column.field]
      if (val && !val?.includes('**')) {
        this.data[this.data.column.field] = this.oldValue
        return
      }
      this.$API.deliveryConfig.checkDeliveryConfigInfo({ key: _val || '' }).then((res) => {
        if (res && res.code === 200) {
          this.data[this.data.column.field] = res.data || ''
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.eyeStyle {
  cursor: pointer;
  margin-left: 5px;
}
</style>
