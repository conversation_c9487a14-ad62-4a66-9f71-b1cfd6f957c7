<!-- 供方-新模式送货单 -->
<template>
  <!-- eslint-disable prettier/prettier -->
  <div class="full-height vertical-flex-box">
    <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
    <div style="height: calc(100% - 40px)">
      <mt-template-page
        class="templateRef"
        ref="templateRef"
        :template-config="pageConfig1"
        :permission-obj="permissionObj"
        v-if="tabIndex === 0"
        @handleClickToolBar="handleClickToolBar"
        @handleCustomReset="handleSearchReset"
        @handleCustomSearch="handleSearchForm"
        @handleClickCellTitle="handleClickCellTitle"
        @handleClickCellTool="handleClickCellTool"
      >
        <template v-slot:quick-search-form>
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="deliveryCode" :label="$t('送货单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.deliveryCode"
                :placeholder="$t('请输入送货单号')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="sourceDeliveryCode" :label="$t('源送货单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.sourceDeliveryCode"
                :placeholder="$t('请输入源送货单号')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('物料编码')" label-style="top">
              <debounce-filter-select
                v-model="searchFormModel.itemCode"
                :request="getItemCode"
                :data-source="itemOptions"
                :fields="{ text: 'theCodeName', value: 'customerCode' }"
                :value-template="itemCodeValueTemplate"
                :show-clear-button="true"
                :allow-filtering="true"
                @change="itemCodeChange"
                :placeholder="$t('请选择')"
              ></debounce-filter-select>
            </mt-form-item>
            <mt-form-item prop="siteCode" :label="$t('工厂代码')" label-style="top">
              <debounce-filter-select
                v-model="searchFormModel.siteCode"
                :request="postSiteFuzzyQuery"
                :data-source="siteOptions"
                :show-clear-button="true"
                :fields="{ text: 'theCodeName', value: 'siteCode' }"
                :value-template="siteCodeValueTemplate"
                @change="siteCodeChange"
                :placeholder="$t('请选择')"
              ></debounce-filter-select>
            </mt-form-item>
          </mt-form>
        </template>
      </mt-template-page>
      <mt-template-page
        :template-config="pageConfig2"
        :permission-obj="permissionObj"
        v-if="tabIndex === 1"
        ref="templateRefDetail"
        @handleClickToolBar="handleClickToolBar"
        @handleCustomReset="handleSearchReset"
        @handleCustomSearch="handleSearchForm"
        @handleClickCellTitle="handleClickCellTitle1"
        @handleClickCellTool="handleClickCellTool"
      >
        <template v-slot:quick-search-form>
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="deliveryCode" :label="$t('送货单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.deliveryCode"
                :placeholder="$t('请输入送货单号')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="sourceDeliveryCode" :label="$t('源送货单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.sourceDeliveryCode"
                :placeholder="$t('请输入源送货单号')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('物料编码')" label-style="top">
              <debounce-filter-select
                v-model="searchFormModel.itemCode"
                :request="getItemCode"
                :data-source="itemOptions"
                :fields="{ text: 'theCodeName', value: 'customerCode' }"
                :value-template="itemCodeValueTemplate"
                :show-clear-button="true"
                :allow-filtering="true"
                @change="itemCodeChange"
                :placeholder="$t('请选择')"
              ></debounce-filter-select>
            </mt-form-item>
            <mt-form-item prop="siteCode" :label="$t('工厂代码')" label-style="top">
              <debounce-filter-select
                v-model="searchFormModel.siteCode"
                :request="postSiteFuzzyQuery"
                :data-source="siteOptions"
                :show-clear-button="true"
                :fields="{ text: 'theCodeName', value: 'siteCode' }"
                :value-template="siteCodeValueTemplate"
                @change="siteCodeChange"
                :placeholder="$t('请选择')"
              ></debounce-filter-select>
            </mt-form-item>
          </mt-form>
        </template>
      </mt-template-page>
    </div>
    <mt-page
      class="full-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totals"
      @currentChange="currentChange"
      @sizeChange="sizeChange"
    />
    <delivery-dialog
      v-if="deliveryShow"
      :ids="id"
      :company-codes="companyCodes"
      ref="child"
      @handleAddDialogShow="handleAddDialogShow"
      @handleDialogShow="handleDialogShow"
    >
    </delivery-dialog>
  </div>
</template>

<script>
/* eslint-disable prettier/prettier */
import { i18n } from '@/main.js'

const mainTabList = [
  {
    title: i18n.t('送货单列表')
  },
  {
    title: i18n.t('送货单明细')
  }
]
import { download, getHeadersFileName } from '@/utils/utils'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import { columnData, columnData2 } from './config/index.js'
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  components: {
    // deliveryDialog,
    DeliveryDialog: require('./components/deliveryDialog').default,
    DebounceFilterSelect
  },
  mounted() {
    this.getCompany({})
    this.postSiteFuzzyQuery({})
    setTimeout(() => {
      this.$set(this.pageSettings, 'pageSize', 20)
    }, 500)
  },
  data() {
    return {
      itemOptions: [], // 物料 下拉选项
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 5,
        pageSizes: [10, 20, 50, 100, 200, 1000],
        totalRecordsCount: 0,
        totals: 0
      },
      siteOptions: [], // 工厂 下列选项
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      companyCodeValueTemplate: codeNameColumn({
        firstKey: 'customerCode',
        secondKey: 'customerName'
      }),
      itemCodeValueTemplate: codeNameColumn({
        firstKey: 'customerCode',
        secondKey: 'customerName'
      }),
      companyOptions: [], // 公司 下拉选项
      statusOptions: [
        { value: 2, text: i18n.t('送货中') },
        { value: 3, text: i18n.t('已完成') },
        { value: 4, text: i18n.t('已取消') },
        { value: 5, text: i18n.t('已关闭') },
        { value: 6, text: i18n.t('部分收货') }
      ],
      id: [],
      searchFormModelDetail: {
        deliveryCode: ''
      },
      searchFormModel: {
        deliveryCode: null,
        companyName: null,
        status: null,
        siteCode: null
      },
      companyCodes: [],
      deliveryShow: false,
      index: 0,
      tabIndex: 0,

      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'List', permissionCode: 'T_02_0163' },
          { dataPermission: 'Details', permissionCode: 'T_02_0164' }
        ]
      },
      tabSource: mainTabList,
      pageConfig1: [
        {
          dataPermission: 'List',
          activatedRefresh: false,
          isUseCustomSearch: true,
          isCustomSearchHandle: true,
          // permissionCode: "T_02_0163",
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: {
            tools: [
              [
                {
                  id: 'print',
                  icon: 'icon_table_print',
                  permission: ['O_02_1649'],
                  title: this.$t('打印送货单')
                }
              ],
              ['Setting']
            ]
          },
          gridId: '4ee87790-9b09-4ddb-80b0-14156ea936eb',
          grid: {
            virtualPageSize: 30,
            showSelected: false,
            selectionSettings: {
              persistSelection: true,
              type: 'Multiple',
              checkboxOnly: true
            },
            enableVirtualization: true,
            allowPaging: false,
            useToolTemplate: false, // 不使用预置(新增、编辑、删除)
            useBaseConfig: true, // 使用组件中的toolbar配置
            columnData: columnData,
            customSelection: true, // 使用自定义勾选列
            lineIndex: 0, // 序号列
            dataSource: []
            // frozenColumns: 1,
            // frozenColumns: 4
          }
        }
      ],
      pageConfig2: [
        {
          dataPermission: 'Details',
          activatedRefresh: false,
          isUseCustomSearch: true,
          isCustomSearchHandle: true,
          // permissionCode: "T_02_0164",
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: {
            tools: [
              [
                {
                  id: 'export',
                  icon: 'icon_solid_Import',
                  // permission: ["O_02_1078"],
                  title: this.$t('导出')
                }
              ],
              ['Setting']
            ]
          },
          gridId: '7295f436-cf2e-4ce7-a0fd-95b3ae590918',
          grid: {
            allowPaging: false,
            virtualPageSize: 30,
            showSelected: false,
            selectionSettings: {
              persistSelection: true,
              type: 'Multiple',
              checkboxOnly: true
            },
            enableVirtualization: true,
            columnData: columnData2,
            // lineSelection: 0, // 选项列
            lineIndex: 0,
            dataSource: []
            // asyncConfig: {
            //   url: `${BASE_TENANT}/supplierOrderDeliveryItem/page`,
            //   defaultRules: []
            //   // ignoreDefaultSearch: true,
            // }
            // frozenColumns: 1,
          }
        }
      ],
      userInfo: null,
      addDialogShow: false,
      dialogData: null
    }
  },
  methods: {
    itemCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.customerId = itemData.id
        this.searchFormModel.customerCode = itemData.customerCode
        this.searchFormModel.customerName = itemData.customerName
      } else {
        this.searchFormModel.customerId = ''
        this.searchFormModel.customerCode = ''
        this.searchFormModel.customerName = ''
      }
    },
    // 获取主数据-获取指定组织下指定组织层级节点列表 业务公司
    getItemCode(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text
      }
      this.$API.masterData
        .getCustomer(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.itemOptions = addCodeNameKeyInList({
              firstKey: 'customerCode',
              secondKey: 'customerName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.itemOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 分页器 - 切换分页
    currentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.searchForm()
    },
    // 分页器 - 切换页大小
    sizeChange(currentPageSize) {
      this.pageSettings.pageSize = currentPageSize
      this.pageSettings.currentPage = 1
      this.searchForm()
    },
    handleSearchReset() {
      for (let key in this.searchFormModel) {
        this.searchFormModel[key] = null
      }
    },
    handleSearchForm() {
      this.pageSettings.currentPage = 1
      this.searchForm()
    },
    searchForm() {
      this.$store.commit('startLoading')
      const param = {
        page: {
          current: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      const {
        getnewModeDeliverListSupplierTableData,
        getnewModeDeliverListSupplierTableDetailData
      } = this.$API.receiptAndDelivery
      ;(this.tabIndex === 0
        ? getnewModeDeliverListSupplierTableData
        : getnewModeDeliverListSupplierTableDetailData)(param)
        .then((res) => {
          if (res && res.code === 200) {
            setTimeout(() => {
              document.querySelector('.e-content').scrollTop = 0
            }, 20)
            this.$set(
              this['pageConfig' + (this.tabIndex + 1)][0].grid,
              'dataSource',
              res.data?.records ?? []
            )
            this.pageSettings.totals = Math.ceil(
              Number(res.data.total) / this.pageSettings.pageSize
            )
            this.pageSettings.totalRecordsCount = res.data.total
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        page: {
          current: 1,
          pages: 0,
          size: 20
        }
      }
      this.$API.receiptAndDelivery
        .getFactoryInfo(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 公司 change
    companyCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.companyId = itemData.id
        this.searchFormModel.companyCode = itemData.customerCode
        this.searchFormModel.companyName = itemData.customerName
      } else {
        this.searchFormModel.companyId = ''
        this.searchFormModel.companyCode = ''
        this.searchFormModel.companyName = ''
      }
    },
    // 工厂 change
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.siteId = itemData.id
        this.searchFormModel.siteCode = itemData.siteCode
        this.searchFormModel.siteName = itemData.siteName
      } else {
        this.searchFormModel.siteId = ''
        this.searchFormModel.siteCode = ''
        this.searchFormModel.siteName = ''
      }
    },
    // 获取主数据-获取指定组织下指定组织层级节点列表 业务公司
    getCompany(args) {
      const { updateData, setSelectData } = args
      let obj = {
        fuzzyNameOrCode: ''
      }
      this.$API.masterData
        .getCustomer(obj)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.companyOptions = addCodeNameKeyInList({
              firstKey: 'customerCode',
              secondKey: 'customerName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.companyOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 弹框
    handleDialogShow(flag) {
      this.deliveryShow = flag
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    // 点击单元格 标题
    handleClickCellTitle(e) {
      if (e.field === 'deliveryCode') {
        localStorage.setItem('deliverDetailSupplierTV', JSON.stringify(e.data))
        this.$router.push(
          `new-mode-deliver-detail-supplier-tv?id=${e.data.sourceId}&type=${
            e.data.deliveryType
          }&timeStamp=${new Date().getTime()}`
        )
      }
    },
    handleClickCellTitle1(e) {
      if (e.field === 'deliveryCode') {
        this.getDeliverDataById(e.data?.deliveryId)
      }
    },
    // 根据id查询主单数据 - 获取物料信息并跳转物料页面
    getDeliverDataById(id) {
      this.$store.commit('startLoading')
      const params = {
        id: id
      }
      this.$API.receiptAndDelivery
        .getnewModeSupplierDeliveyData(params)
        .then((res) => {
          if (res?.code == 200) {
            // 跳转详情页

            localStorage.setItem('deliverDetailSupplierTV', JSON.stringify(res?.data))
            this.$router.push(
              `new-mode-deliver-detail-supplier-tv?id=${res?.data?.id}&type=${
                res.data?.deliveryType
              }&timeStamp=${new Date().getTime()}`
            )
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    handleClickCellTool(e) {
      if (e.tool.title === this.$t('维护物流信息')) {
        this.$router.push({
          name: 'deliver-logistics-supplier',
          query: {
            id: e.data.id
          }
        })
        localStorage.setItem('deliverDetailSupplier', JSON.stringify(e.data))
      }
      if (e.tool.title === this.$t('取消')) {
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消？')
          },
          success: () => {
            this.$store.commit('startLoading')
            this.$API.receiptAndDelivery
              .supplierOrderDeliveryCancelTV([e.data.id])
              .then((res) => {
                this.$store.commit('endLoading')
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('操作成功'),
                    type: 'success'
                  })
                  this.$refs.templateRef.refreshCurrentGridData()
                  // this.confirmSuccess();
                }
              })
              .catch(() => {
                this.$refs.templateRef.refreshCurrentGridData()

                this.$store.commit('endLoading')
              })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    handleClickToolBar(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id === 'resetDataByLocal') return
      if (toolbar.id == 'new') {
        this.$router.push(`create-noOrder-supplier-tv`)
        return
      }
      if (toolbar.id == 'export') {
        this.handleExport()
        return
      }
      const selectRows = []
      gridRef.dataSource.forEach((item) => {
        if (item.customChecked) {
          selectRows.push(item)
        }
      })
      if (selectRows.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const _status = []
      const _forecastDelivery = []
      const _id = []
      const _codes = []
      selectRows.forEach((item) => {
        _id.push(item.id)
        _codes.push(item.companyCode)
        _status.push(item.status)
        _forecastDelivery.push(item.forecastDelivery)
      })
      this.id = _id
      this.companyCodes = _codes

      if (toolbar.id === 'print') {
        if (_status.some((v) => v.label === this.$t('已取消') || v.label === this.$t('已关闭'))) {
          return this.$toast({
            content: this.$t('不可以打印已取消或已关闭的单据'),
            type: 'warning'
          })
        }
        this.$dialog({
          modal: () => import('./components/printDialog.vue'),
          data: {
            title: this.$t('送货单打印纸张选择')
          },
          success: (type) => {
            this.handlePrint(type, _id)
          }
        })
      }
    },
    handleExport() {
      let obj = JSON.parse(sessionStorage.getItem(this.pageConfig2[0].gridId))?.visibleCols
      const headerMap = {}
      if (obj !== undefined && obj.length) {
        obj?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      } else {
        this.pageConfig2[0].grid.columnData?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'id') {
            headerMap[i.field] = i.headerText
          }
        })
      }
      const params = {
        page: { current: 1, size: 5000 },
        ...this.searchFormModel,
        headerMap
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery.newModeSupplierOrderDeliveryItemExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handlePrint(type, ids) {
      let params = {
        templateType: type,
        ids
      }
      this.$API.receiptAndDelivery.newModeSupplierOrderDeliveryPrintHtml(params).then((res) => {
        if (res && res.code === 500) {
          this.$toast({ content: res.msg, type: 'error' })
          return
        }
        const content = res.data
        this.pdfUrl = window.URL.createObjectURL(
          new Blob([content], { type: 'text/html;charset=utf-8' })
        )
        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = this.pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    print(_id, type) {
      if (type === 'kt') {
        this.$API.receiptAndDelivery.newModeSupplierOrderDeliveryPrintHtml(_id).then((res) => {
          if (res && res.code === 500) {
            this.$toast({ content: res.msg, type: 'error' })
            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(
            new Blob([content], { type: 'text/html;charset=utf-8' })
          )
          // window.open(this.pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
      }
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    handleClaim(_id) {
      this.$store.commit('startLoading')

      this.$API.receiptAndDelivery
        .supplierOrderDeliveryCancelTV(_id)
        .then((res) => {
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef.refreshCurrentGridData()
        })
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('todoDetail')
  }
}
</script>

<style lang="scss" scoped>
.forecast-template {
  width: 250px;
  padding-bottom: 8px;
  .forecast-template-select {
    width: calc(250px - 70px);
  }
}
/deep/ .column-tool {
  margin-top: 8px;
}
/deep/ .template-svg {
  cursor: pointer;
  font-size: 12px;
  color: var(--plugin-ct-cell-icon-color);

  &:nth-child(n + 2) {
    margin-left: 10px;
  }
}

/deep/ .grid-edit-column {
  // padding: 12px 0;
}

// 预测数据行
/deep/ .forecast-item {
  // height: 40px;
  // padding: 7px;
  line-height: 26px;
  border: 1px solid #e8e8e8;
}
/deep/ .inputSy {
  border: 1px solid #e8e8e8;
  height: 30px;
}
// 预测数据高亮数据
/deep/ .forecast-highlight {
  color: #ed5836;
  background-color: #fdeeea;
  border: 1px solid #ed5836;
}

// 表格容器
#forecast-manage-table-container {
  position: relative;

  // 表格数据视图
  /deep/ .e-gridcontent > .e-content {
    height: 0px;
    transition: height 0.5s ease;
  }

  // 分页
  /deep/ .mt-pagertemplate {
    margin: 10px 0 10px !important;
  }
}

// 行编辑时包含按钮的项目
/deep/ .input-search-content {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .mt-input {
    flex: 1;
  }

  .mutliselect-container {
    width: 100%;
  }
  // 超过宽度显示省略号
  .text-ellipsis {
    width: 125px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .mt-icons {
    width: 20px;
    flex-shrink: 0;
    text-align: center;
    cursor: pointer;
  }
}
/deep/ .templateRef {
  .template-wrap {
    .e-grid .e-table {
      & thead th:first-child {
        position: sticky;
        left: 0px;
        z-index: 1;
      }
      & thead th:nth-child(2) {
        position: sticky;
        left: 50px;
        z-index: 1;
      }
      & thead th:nth-child(3) {
        position: sticky;
        left: 115px;
        z-index: 1;
      }
      & thead th:nth-child(4) {
        position: sticky;
        left: 315px;
        z-index: 1;
      }
      & thead th:nth-child(5) {
        position: sticky;
        left: 515px;
        z-index: 1;
      }
      & thead th:nth-child(6) {
        position: sticky;
        left: 597px;
        z-index: 1;
      }

      & tbody td:first-child {
        position: sticky;
        left: 0px;
        z-index: 1;
        border-right: 1px solid var(--plugin-dg-shadow-color);
        background-color: #fff;
      }
      & tbody td:nth-child(2) {
        position: sticky;
        left: 50px;
        z-index: 1;
        border-right: 1px solid var(--plugin-dg-shadow-color);
        background-color: #fff;
      }
      & tbody td:nth-child(3) {
        position: sticky;
        left: 115px;
        z-index: 1;
        border-right: 1px solid var(--plugin-dg-shadow-color);
        background-color: #fff;
      }
      & tbody td:nth-child(4) {
        position: sticky;
        left: 315px;
        z-index: 1;
        border-right: 1px solid var(--plugin-dg-shadow-color);
        background-color: #fff;
      }
      & tbody td:nth-child(5) {
        position: sticky;
        left: 515px;
        z-index: 1;
        border-right: 1px solid var(--plugin-dg-shadow-color);
        background-color: #fff;
      }
      & tbody td:nth-child(6) {
        position: sticky;
        left: 597px;
        z-index: 1;
        border-right: 1px solid var(--plugin-dg-shadow-color);
        background-color: #fff;
      }
    }
  }
}
</style>
