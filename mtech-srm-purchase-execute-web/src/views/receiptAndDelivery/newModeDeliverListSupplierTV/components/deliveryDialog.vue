<template>
  <div>
    <mt-dialog
      ref="dialog"
      css-class="create-proj-dialog"
      :header="$t('维护物流信息')"
      :buttons="buttons"
      @close="handleClose"
      :open="onOpen"
    >
      <mt-form ref="addForm" :model="addForm" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="type" :label="$t('发货方式')">
          <mt-select
            ref="businessRef"
            v-model="addForm.type"
            :data-source="deliveryTypeOptions"
            @change="selectChange"
            :show-clear-button="false"
            :placeholder="$t('请选择发货方式')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item v-if="addForm.type === 1" prop="logisticsCompanyCode" :label="$t('物流公司')">
          <mt-select
            ref="logisticsCompanyCode"
            v-model="addForm.logisticsCompanyCode"
            :data-source="logisticsCompanyOptions"
            :show-clear-button="false"
            :placeholder="$t('请选择物流公司')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item v-if="addForm.type === 1" prop="logisticsNo" :label="$t('物流单号')">
          <mt-input v-model="addForm.logisticsNo" :placeholder="$t('请输入物流单号')"></mt-input>
        </mt-form-item>
        <mt-form-item v-if="addForm.type === 1" prop="number" :label="$t('件数')">
          <mt-input
            v-model="addForm.number"
            :maxlength="8"
            :placeholder="$t('请输入件数')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item v-if="addForm.type === 2" prop="driverName" :label="$t('司机姓名')">
          <combobox
            v-model="addForm.driverName"
            :fields="{ text: 'name', value: 'name' }"
            :data-source="driverNameOptions"
            :placeholder="$t('请选择')"
            @change="driverNameChange"
            @blur="driverNameBlur"
          />
        </mt-form-item>
        <mt-form-item v-if="addForm.type === 2" prop="driverNo" :label="$t('司机身份证号')">
          <mt-input
            v-model="addForm.driverNo"
            :placeholder="$t('请输入司机身份证号')"
            :disabled="idCardDisabled"
          ></mt-input>
        </mt-form-item>
        <mt-form-item v-if="addForm.type === 2" prop="carNo" :label="$t('车牌号')">
          <mt-input v-model="addForm.carNo" :placeholder="$t('请输入车牌号')"></mt-input>
        </mt-form-item>
        <mt-form-item prop="driverPhone" :label="$t('司机联系方式')" v-if="addForm.type === 2">
          <mt-input
            :placeholder="$t('请输入司机联系方式')"
            v-model="addForm.driverPhone"
            maxlength="11"
          ></mt-input>
        </mt-form-item>
        <mt-form-item v-if="addForm.type === 2" prop="number" :label="$t('件数')">
          <mt-input
            v-model="addForm.number"
            :maxlength="8"
            :placeholder="$t('请输入件数')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input
            v-model="addForm.remark"
            :placeholder="$t('请输入备注')"
            :maxlength="200"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { RegExpMap } from '@/utils/constant'
import Combobox from '@/components/combobox'
export default {
  props: {
    ids: {
      type: Array,
      default: () => {}
    },
    companyCodes: {
      type: Array,
      default: () => []
    }
  },
  components: {
    Combobox
  },
  data() {
    const { idCardReg, phoneNumRegCN, phoneNumRegVN } = RegExpMap
    const identityNumberValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入司机身份证号')))
      } else if (!idCardReg.test(value)) {
        callback(new Error(this.$t('身份证号格式错误')))
      } else {
        this.$refs.addForm.clearValidate(['driverNo'])
        callback()
      }
    }
    const phoneNumberValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入司机手机号')))
      } else if (!phoneNumRegCN.test(value) && !phoneNumRegVN.test(value)) {
        callback(new Error(this.$t('请输入正确的手机号')))
      } else {
        this.$refs.addForm.clearValidate(['driverPhone'])
        callback()
      }
    }
    return {
      dialogTitle: '',
      logisticsCompanyOptions: [], // 物流公司
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      deliveryTypeOptions: [
        {
          value: 1,
          text: this.$t('快递配送')
        },
        {
          value: 2,
          text: this.$t('物流配送')
        }
      ], // 业务类型
      addForm: {
        carNo: '',
        deliveryId: '1',
        driverName: '',
        driverNo: '',
        driverPhone: '',
        idList: [],
        logisticsCompanyCode: '',
        logisticsCompanyName: '',
        logisticsNo: '',
        number: '',
        remark: '',
        tenantId: '1',
        type: 0
      },
      rules: {
        type: [
          {
            required: true,
            message: this.$t('请选择发货方式'),
            trigger: 'blur'
          }
        ],
        logisticsCompanyCode: [
          {
            required: true,
            message: this.$t('请选择物流公司'),
            trigger: 'blur'
          }
        ],
        logisticsNo: [
          {
            required: true,
            message: this.$t('请输入物流单号'),
            trigger: 'blur'
          }
        ],
        // number: [
        //   { required: true, message: this.$t("请输入件数"), trigger: "blur" },
        // ],
        driverName: [
          {
            required: true,
            message: this.$t('请输入司机姓名'),
            trigger: 'blur'
          }
        ],
        driverNo: [
          {
            required: true,
            validator: identityNumberValidator,

            trigger: 'blur'
          }
        ],
        driverPhone: [
          {
            required: true,
            validator: phoneNumberValidator,
            trigger: 'blur'
          }
        ],
        carNo: [
          {
            required: true,
            message: this.$t('请输入车牌号'),

            trigger: 'blur'
          }
        ]
      },
      dimensionFields: { text: 'message', value: 'code' },
      dimensionList: [],
      driverNameOptions: [], // 司机姓名下拉数据源
      idCardDisabled: false
    }
  },
  mounted() {
    this.init()
    this.addForm.idList = this.ids
    this.$refs.dialog.ejsRef.show()
    // 获取送货司机下拉数据源
    this.getDriverNameOptions()
    // this.$refs.ruleForm.resetFields();
  },

  methods: {
    // 切换方式
    selectChange(e) {
      console.log(e)
      this.$refs.addForm.resetFields()
      console.log(this.addForm)
    },
    init() {
      // 获取物流公司
      this.$API.masterData //物流公司
        .getCommonDictItemTreeTv({
          dictCode: 'logisticsCompany'
        })
        .then((res) => {
          if (res.data && res.data.length) {
            this.logisticsCompanyOptions = res.data.map((item) => {
              return {
                text: item.itemName,
                value: item.itemCode,
                id: item.id
              }
            })
          }
        })
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    getDimension() {
      this.$API.moduleConfig.getDimension().then((res) => {
        console.log(res)
        this.dimensionList = res.data || []
      })
    },
    // 司机姓名 change
    driverNameChange(e) {
      const { itemData } = e
      if (itemData) {
        this.addForm.driverPhone = itemData.contact // 司机联系方式
        this.addForm.carNo = itemData.license // 车牌号
        this.addForm.driverNo = itemData.idCard // 司机身份证号
      } else {
        this.addForm.driverPhone = '' // 司机联系方式
        this.addForm.carNo = '' // 车牌号
        this.addForm.driverNo = '' // 司机身份证号
      }
    },
    driverNameBlur() {
      const _arr = this.driverNameOptions.map((item) => item.name)
      this.idCardDisabled = _arr.includes(this.addForm.driverName)
    },
    // 获取司机姓名下拉数据源
    getDriverNameOptions() {
      const params = {
        page: { current: 1, size: 10000 },
        condition: 'and',
        defaultRules: [
          {
            label: this.$t('状态'),
            field: 'status',
            type: 'string',
            operator: 'equal',
            value: 1
          }
        ],
        companyCodeList: this.companyCodes
      }

      this.$API.receiptAndDelivery
        .postSupplierDriverQueryTv(params)
        .then((res) => {
          const data = res?.data?.records || []
          this.driverNameOptions = data
        })
        .catch(() => {})
    },

    confirm() {
      console.log(1)
      this.$refs.addForm.validate((valid) => {
        console.log(2)
        if (valid) {
          let params = this.addForm

          this.$API.receiptAndDelivery.supplierDeliveryLogisticsInfoSave(params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('handleAddDialogShow')
            }
          })
        }
      })
    },

    handleClose() {
      this.$emit('handleDialogShow', false)
    }
  }
}
</script>

<style></style>
