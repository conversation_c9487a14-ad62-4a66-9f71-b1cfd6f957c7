<!-- 选择打印方式 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    width="550px"
    height="250px"
  >
    <div class="dialog-content" style="margin-top: 20px">
      <mt-form ref="modelForm" :model="modelForm" :rules="rules">
        <mt-form-item prop="printType" :label="$t('打印方式')">
          <mt-radio v-model="modelForm.printType" :data-source="typeOptions" />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      modelForm: {
        printType: '2'
      },
      rules: {
        printType: [
          {
            required: true,
            message: this.$t('请选择打印方式'),
            trigger: 'blur'
          }
        ]
      },
      typeOptions: [
        {
          label: this.$t('一半Letter纸(21.59*13.97)'),
          value: '2'
        },
        {
          label: this.$t('Letter纸(21.59*27.94)'),
          value: '1'
        },
        {
          label: this.$t('A4纸(21.0*29.7)'),
          value: '0'
        }
      ]
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    confirm() {
      this.$refs.modelForm.validate((valid) => {
        if (valid) {
          this.$emit('confirm-function', this.modelForm.printType)
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style></style>
