import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import Vue from 'vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName } from '@/utils/utils'

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, isDateTime, isDate, isTime } = args

  const template = () => {
    return {
      template: Vue.component('timeDateComponent', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <div v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</div>
            <div v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</div>
          </div>
        </div>`,
        data: function () {
          return { data: {}, dataKey, isDateTime, isDate, isTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
export const columnData = [
  {
    width: '200',
    field: 'deliveryCode',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('送货单号'),
    cellTools: []
  },
  {
    width: '200',
    field: 'sourceDeliveryCode',
    headerText: i18n.t('源送货单号')
  },
  {
    width: '82',
    field: 'status',
    headerText: i18n.t('状态'),
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    cssClass: '',
    // valueConverter: {
    //   type: 'map',
    //   map: [
    //     { value: 2, text: i18n.t('发货中'), cssClass: 'col-active' },
    //     { value: 3, text: i18n.t('已完成'), cssClass: 'col-inactive' },
    //     { value: 4, text: i18n.t('已取消'), cssClass: 'col-inactive' },
    //     { value: 5, text: i18n.t('已关闭'), cssClass: 'col-inactive' }
    //   ]
    // },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          return e.label
        }
        return ''
      }
    },
    cellTools: []
  },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  },
  {
    width: '97',
    field: 'outsourcedType',
    headerText: i18n.t('委外方式'),
    searchOptions: {
      operator: 'likeright'
    },
    valueConverter: {
      type: 'map',
      map: [
        { value: '0', text: i18n.t('标准委外'), cssClass: '' },
        { value: '1', text: i18n.t('销售委外'), cssClass: '' },
        { value: '2', text: i18n.t('非委外'), cssClass: '' },
        { value: '3', text: i18n.t('工序委外'), cssClass: '' }
      ]
    }
  },
  {
    width: '120',
    field: 'deliveryOrderType',
    headerText: i18n.t('送货单类型'),
    searchOptions: {
      operator: 'likeright'
    },
    // valueConverter: {
    //   type: 'map',
    //   map: [
    //     { value: 1, text: i18n.t('采购订单'), cssClass: '' },
    //     { value: 2, text: i18n.t('交货计划'), cssClass: '' },
    //     { value: 3, text: i18n.t('JIT'), cssClass: '' },
    //     { value: 4, text: i18n.t('无需求'), cssClass: '' },
    //     { value: 5, text: i18n.t('vmi'), cssClass: '' },
    //     { value: 6, text: i18n.t('钢材'), cssClass: '' }
    //   ]
    // }
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          return e.label
        }
        return ''
      }
    }
  },
  {
    width: '120',
    field: 'forecastDelivery',
    headerText: i18n.t('是否可预约'),
    searchOptions: {
      operator: 'likeright'
    },
    cssClass: '',
    ignore: true,
    allowFiltering: false,
    // valueConverter: {
    //   type: "function",
    //   filter: (data, rowData) => {
    //     if (data == 1) {
    //       return i18n.t("否");
    //     } else if (rowData.status != 2) {
    //       // 不是发货中
    //       return i18n.t("否");
    //     } else {
    //       return i18n.t("是");
    //     }
    //   },
    // },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if ([1, '1'].includes(e)) return i18n.t('否')
        return i18n.t('是')
      }
    }
  },
  {
    width: '250',
    field: 'siteName',
    headerText: i18n.t('工厂'),
    searchOptions: {
      operator: 'likeright',
      ...MasterDataSelect.factorySupplierAddress,

      renameField: 'siteCode',
      ...MasterDataSelect.factorySupplierAddress
    }
  },
  {
    width: '235',
    field: 'companyName',
    headerText: i18n.t('公司'),
    searchOptions: {
      operator: 'likeright',
      ...MasterDataSelect.companySupplier,

      renameField: 'companyCode',
      ...MasterDataSelect.companySupplier
    }
  },
  {
    field: 'printTimes', //
    headerText: i18n.t('打印次数'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '250',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组'),
    searchOptions: {
      operator: 'likeright',
      ...MasterDataSelect.businessCodeName,
      renameField: 'buyerOrgCode'
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '200',
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '180',
    field: 'thirdTenantCode',
    headerText: i18n.t('第三方物流商'),
    searchOptions: {
      operator: 'likeright'
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.thirdTenantCode}}-{{data.thirdTenantName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '150',
    field: 'vmiWarehouseCode',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('VMi仓'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.vmiWarehouseCode}}-{{data.vmiWarehouseName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '180',
    field: 'sendAddressName',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('送货地点')
  },
  {
    width: '120',
    field: 'vmiWarehouseType',
    headerText: i18n.t('VMI仓类型'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '180',
    field: 'warehouseName',
    headerText: i18n.t('交货库存地点'),
    searchOptions: {
      operator: 'likeright',
      ...MasterDataSelect.stockSupplierAddressName,
      renameField: 'warehouseCode'
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.warehouseCode}}-{{data.warehouseName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '97',
    field: 'sendTime',
    headerText: i18n.t('制单日期'),
    // editTemplate: () => {
    //   return {
    //     template: "1212",
    //   };
    // },
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    template: timeDate({ dataKey: 'sendTime', isDateTime: true })
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    width: '130',
    field: 'forecastArriveTime',
    headerText: i18n.t('预计到货日期'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    template: timeDate({ dataKey: 'forecastArriveTime', isDateTime: true })
  },
  {
    width: '300',
    field: 'remark',
    headerText: i18n.t('备注'),
    ignore: true
  },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  }
]
export const columnData2 = [
  {
    width: '200',
    field: 'deliveryCode',
    searchOptions: {
      operator: 'likeright'
    },
    cellTools: [],
    headerText: i18n.t('送货单号')
  },
  {
    width: '200',
    field: 'sourceDeliveryCode',
    headerText: i18n.t('源送货单号')
  },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  },
  {
    width: '235',
    field: 'companyName',
    searchOptions: {
      operator: 'likeright',
      renameField: 'companyCode',
      ...MasterDataSelect.companySupplier
    },
    headerText: i18n.t('公司')
  },
  {
    width: '150',
    field: 'batchCode',
    headerText: i18n.t('卷号')
  },
  {
    width: '150',
    field: 'takeNo',
    headerText: i18n.t('车号')
  },
  {
    width: '250',
    field: 'siteName',
    searchOptions: {
      operator: 'likeright',
      renameField: 'siteCode',
      ...MasterDataSelect.factorySupplierAddress
    },
    headerText: i18n.t('工厂')
  },
  {
    width: '250',
    field: 'subSiteName',
    headerText: i18n.t('分厂'),
    searchOptions: {
      operator: 'likeright',
      ...MasterDataSelect.subSiteCodeSupplier,
      renameField: 'subSiteCode'
    },
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.subSiteCode, data?.subSiteName)
    }
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '200',
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '250',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组'),
    searchOptions: {
      operator: 'likeright',
      ...MasterDataSelect.businessCodeName,
      renameField: 'buyerOrgCode'
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '250',
    field: 'warehouseName',
    headerText: i18n.t('交货库存地点'),
    searchOptions: {
      operator: 'likeright',
      ...MasterDataSelect.stockSupplierAddressName,
      renameField: 'warehouseCode'
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.warehouseCode}}-{{data.warehouseName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'inputDate', //
    headerText: i18n.t('凭证创建日期'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    template: timeDate({ dataKey: 'inputDate', isDateTime: true })
  },
  // {
  //   field: "inputTime", //
  //   headerText: i18n.t("凭证创建时间"),
  //   template: timeDate({ dataKey: "inputTime", isTime: true }),
  //   searchOptions: {
  //     ...MasterDataSelect.timeRange,
  //   },
  // },
  {
    field: 'postingDate', //
    headerText: i18n.t('过账日期'),
    template: timeDate({ dataKey: 'postingDate', isDate: true }),

    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    width: '200',
    field: 'thirdTenantCode',
    headerText: i18n.t('第三方物流商'),
    searchOptions: {
      operator: 'likeright',
      ...MasterDataSelect.deliverSupplierThirdTenantCode
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.thirdTenantCode}}-{{data.thirdTenantName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '150',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMi仓'),
    searchOptions: {
      operator: 'likeright',
      ...MasterDataSelect.deliverSupplierVmiWarehouseCode
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.vmiWarehouseCode}}-{{data.vmiWarehouseName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '150',
    field: 'takeNo',
    headerText: i18n.t('车牌号'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '180',
    field: 'deliveryNumber',
    headerText: i18n.t('交货编号'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '180',
    field: 'jitDeliveryNumber',
    headerText: i18n.t('JIT编号'),
    ignore: true,
    searchOptions: {
      renameField: 'deliveryNumber'
    }
  },
  {
    width: '150',
    field: 'deliveryLineNo',
    headerText: i18n.t('行号'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '100',
    field: 'status',
    headerText: i18n.t('状态'),
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      // type: "map",
      // map: [
      //   { value: 2, text: i18n.t("发货中"), cssClass: "col-active" },
      //   { value: 3, text: i18n.t("已完成"), cssClass: "col-inactive" },
      //   { value: 4, text: i18n.t("已取消"), cssClass: "col-inactive" },
      //   { value: 5, text: i18n.t("已关闭"), cssClass: "col-inactive" },
      //   { value: 6, text: i18n.t("部分收货"), cssClass: "col-active" },
      // ],
      type: 'function',
      filter: (e) => {
        if (e) {
          return e.label
        }
        return ''
      }
    }
  },
  {
    width: '90',
    field: 'jit',
    headerText: i18n.t('是否JIT'),
    searchOptions: {
      operator: 'likeright'
    },
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },

  {
    width: '97',
    field: 'deliveryType',
    headerText: i18n.t('送货单类型'),
    searchOptions: {
      operator: 'likeright'
    },
    valueConverter: {
      // type: "map",
      // map: [
      //   { value: 1, text: i18n.t("采购订单"), cssClass: "" },
      //   { value: 2, text: i18n.t("交货计划"), cssClass: "" },
      //   { value: 3, text: i18n.t("JIT"), cssClass: "" },
      //   { value: 4, text: i18n.t("无需求"), cssClass: "" },
      //   { value: 5, text: i18n.t("vmi"), cssClass: "" },
      //   { value: 6, text: i18n.t("钢材"), cssClass: "" },
      // ],
      type: 'function',
      filter: (e) => {
        if (e) return e.label
        return ''
      }
    }
  },
  {
    width: '200',
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    searchOptions: {
      ...MasterDataSelect.itemSupplier,
      renameField: 'itemCode'
    }
  },
  {
    width: '250',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    ignore: true
  },
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('采购订单号'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '130',
    field: 'lineNo',
    headerText: i18n.t('采购订单行号'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '180',
    field: 'demandDate',
    headerText: i18n.t('需求日期'),
    // template: timeDate({ dataKey: "demandDate", isDateTime: true }),

    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  // {
  //   width: "100",
  //   field: "demandTime",
  //   headerText: i18n.t("需求时间"),
  //   searchOptions: {
  //     operator: "likeright",
  //   },
  // },
  {
    width: '150',
    field: 'deliveryQuantity',
    headerText: i18n.t('本次送货数量'),
    type: 'number',

    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '98',
    field: 'receiveQuantity',
    type: 'number',

    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('收货数量')
  },
  {
    width: '96',
    field: 'rejectQuantity',
    searchOptions: {
      operator: 'likeright'
    },
    type: 'number',

    headerText: i18n.t('拒绝数量')
  },
  {
    width: '180',
    field: 'rejectReason',
    ignore: true,
    headerText: i18n.t('拒绝原因')
  },
  {
    width: '90',
    field: 'transferPlanName',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('计划员')
  },
  {
    width: '66',
    field: 'unitName',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('单位')
  },
  {
    width: '107',
    field: 'workOrderNo',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('关联工单号')
  },
  {
    width: '140',
    field: 'saleOrderNo',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('关联销售订单号')
  },
  {
    width: '154',
    field: 'saleOrderLineNo',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('关联销售订单行号')
  },
  {
    width: '85',
    field: 'bomCode',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('BOM号')
  },
  {
    width: '124',
    field: 'productCode',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('关联产品代码')
  },
  {
    width: '120',
    field: 'workCenterName',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('工作中心')
  },
  {
    width: '150',
    field: 'processName',
    searchOptions: {
      operator: 'likeright'
    },
    headerText: i18n.t('工序名称')
  },
  // {
  //   width: "150",
  //   field: "receiveSupplierName",
  //   headerText: i18n.t("送货方式"), 暂无
  // },
  // {
  //   width: "150",
  //   field: "receiveSupplierName",
  //   headerText: i18n.t("收货方名称"),
  // },
  // {
  //   width: "150",
  //   field: "receiveSupplierCode",
  //   headerText: i18n.t("收货方编号"),
  // },
  {
    width: '200',
    field: 'receiveAddressName',
    headerText: i18n.t('送货地址'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  // {
  //   width: '91',
  //   field: '2',
  //   headerText: i18n.t('批次管理')
  // },
  // {
  //   width: '68',
  //   field: '2',
  //   headerText: i18n.t('下达')
  // },
  {
    width: '91',
    field: 'limitQuantity',
    type: 'number',

    headerText: i18n.t('限量数量'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '86',
    field: 'warehouseClerkName',
    headerText: i18n.t('仓管员'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '86',
    field: 'dispatcherName',
    headerText: i18n.t('调度员'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  // FIXME: 暂时不展示
  // {
  //   width: "150",
  //   field: "inventoryQuantity",
  //   headerText: i18n.t("入库数量"),
  // },
  // {
  //   width: "150",
  //   field: "inventoryTime",
  //   headerText: i18n.t("入库日期"),
  //   template: timeDate("inventoryTime", true),
  // },
  {
    width: '180',
    field: 'receiveSupplierName',
    headerText: i18n.t('收货供应商'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '90',
    field: 'cancelPersonName',
    headerText: i18n.t('取消人'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '150',
    field: 'cancelTime',
    headerText: i18n.t('取消时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    template: timeDate({ dataKey: 'cancelTime', isDateTime: true })
  },
  {
    width: '86',
    field: 'closePersonName',
    headerText: i18n.t('关闭人'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '95',
    field: 'closeTime',
    headerText: i18n.t('关闭时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    template: timeDate({ dataKey: 'closeTime', isDateTime: true })
  },
  {
    width: '95',
    field: 'collectorMark',
    headerText: i18n.t('代收标识'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '86',
    field: 'collectorName',
    headerText: i18n.t('代收人'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '86',
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    width: '86',
    field: 'remark',
    headerText: i18n.t('行备注')
  },
  {
    width: '150',
    field: 'syncWmsStatus',
    headerText: i18n.t('WMS同步状态'),
    searchOptions: {
      operator: 'likeright'
    },
    allowEditing: false,
    // valueConverter: {
    //   type: 'map',
    //   map: {
    //     0: i18n.t('未同步'),
    //     1: i18n.t('同步中'),
    //     2: i18n.t('同步成功'),
    //     3: i18n.t('同步失败')
    //   }
    // }
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          return e.label
        }
        return ''
      }
    }
  },
  {
    width: '150',
    field: 'syncWmsDesc',
    headerText: i18n.t('WMS同步信息'),
    searchOptions: {
      operator: 'likeright'
    },
    allowEditing: false
  },
  {
    width: '150',
    field: 'syncQmsStatus',
    headerText: i18n.t('QMS同步状态'),
    searchOptions: {
      operator: 'likeright'
    },
    allowEditing: false,
    valueConverter: {
      // type: 'map',
      // map: {
      //   0: i18n.t('未同步'),
      //   1: i18n.t('同步中'),
      //   2: i18n.t('同步成功'),
      //   3: i18n.t('同步失败')
      // }
      type: 'function',
      filter: (e) => {
        if (e) {
          return e.label
        }
        return ''
      }
    }
  },
  {
    width: '150',
    field: 'syncQmsDesc',
    headerText: i18n.t('QMS同步信息'),
    searchOptions: {
      operator: 'likeright'
    },
    allowEditing: false
  },
  {
    width: '150',
    field: 'syncSapStatus',
    headerText: i18n.t('SAP同步状态'),
    searchOptions: {
      operator: 'likeright'
    },
    allowEditing: false,
    // valueConverter: {
    //   type: 'map',
    //   map: {
    //     0: i18n.t('未同步'),
    //     1: i18n.t('同步成功'),
    //     2: i18n.t('同步成功'),
    //     3: i18n.t('同步失败')
    //   }
    // }
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          return e.label
        }
        return ''
      }
    }
  },
  {
    width: '150',
    field: 'syncSapDesc',
    headerText: i18n.t('SAP同步信息'),
    searchOptions: {
      operator: 'likeright'
    },
    allowEditing: false
  }
]
