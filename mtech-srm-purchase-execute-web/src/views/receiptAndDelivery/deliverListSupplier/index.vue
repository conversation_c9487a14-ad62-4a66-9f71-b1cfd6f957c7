<template>
  <!-- 送货单-列表-供方 -->
  <div class="full-height vertical-flex-box">
    <mt-tabs
      :e-tab="false"
      :selected-item="tabIndex"
      :data-source="tabSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <div style="height: calc(100% - 40px)">
      <mt-template-page
        :template-config="pageConfig"
        :permission-obj="permissionObj"
        v-show="tabIndex === 0"
        ref="templateRef"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
        @handleClickCellTool="handleClickCellTool"
      >
      </mt-template-page>
      <mt-template-page
        :template-config="pageConfig1"
        :permission-obj="permissionObj"
        v-show="tabIndex === 1"
        ref="templateRef1"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle1"
        @handleClickCellTool="handleClickCellTool"
      >
      </mt-template-page>
      <mt-template-page
        ref="templateRef3"
        :hidden-tabs="false"
        :permission-obj="permissionObj"
        v-show="tabIndex === 2"
        :template-config="templateConfig3"
        @handleCustomReset="handleCustomReset"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle1"
      >
        <template v-slot:quick-search-form>
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item :label="$t('送货单号')" prop="deliveryCode">
              <mt-input
                v-model="searchFormModel.deliveryCode"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('状态')" prop="status">
              <mt-multi-select
                v-model="searchFormModel.status"
                :data-source="StatusOptions"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                :show-select-all="true"
                style="flex: 1"
              />
            </mt-form-item>
            <mt-form-item :label="$t('创建时间')" prop="createTime">
              <mt-date-range-picker
                v-model="searchFormModel.createTime"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'createTime')"
              />
            </mt-form-item>
            <mt-form-item :label="$t('物料编码')" prop="itemCode">
              <mt-input
                v-model="searchFormModel.itemCode"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('物料名称')" prop="itemName">
              <mt-input
                v-model="searchFormModel.itemName"
                :placeholder="$t('支持模糊搜索')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('采购订单号')" prop="orderCode">
              <mt-input
                v-model="searchFormModel.orderCode"
                :placeholder="$t('支持粘贴多个精准查询')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('采购订单行号')" prop="lineNo">
              <mt-input
                v-model="searchFormModel.lineNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('公司')" prop="companyCodes">
              <RemoteAutocomplete
                v-model="searchFormModel.companyCodes"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01']
                }"
                multiple
                :placeholder="$t('请选择公司')"
                :fields="{ text: 'orgName', value: 'orgCode' }"
              />
            </mt-form-item>
            <mt-form-item :label="$t('工厂')" prop="siteCodes">
              <RemoteAutocomplete
                v-model="searchFormModel.siteCodes"
                :url="$API.masterData.getSiteListUrl"
                :multiple="true"
                :placeholder="$t('请选择')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                :search-fields="['siteName', 'siteCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('分厂')" prop="subSiteCodes">
              <RemoteAutocomplete
                v-model="searchFormModel.subSiteCodes"
                url="/srm-purchase-execute/tenant/buyerJitInfo/condition?conditionType=subSiteCode"
                :multiple="true"
                :placeholder="$t('请选择')"
                :fields="{ text: 'subSiteName', value: 'subSiteCode' }"
                :search-fields="['subSiteName', 'subSiteCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('交货库存地点')" prop="warehouseCodes">
              <RemoteAutocomplete
                v-model="searchFormModel.warehouseCodes"
                url="/masterDataManagement/tenant/location/paged-query"
                :multiple="true"
                :placeholder="$t('请选择')"
                :fields="{ text: 'locationName', value: 'locationCode' }"
                :search-fields="['locationName', 'locationCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('VMi仓')" prop="vmiWarehouseCodes">
              <RemoteAutocomplete
                v-model="searchFormModel.vmiWarehouseCodes"
                url="/srm-purchase-execute/tenant/buyerOrderDelivery/query/vmi/warehouse/list"
                :multiple="true"
                :placeholder="$t('请选择')"
                :fields="{ text: 'vmiWarehouseName', value: 'vmiWarehouseCode' }"
                :params-key="'vmiWarehouseCode'"
                records-position="data"
              />
            </mt-form-item>
            <mt-form-item :label="$t('采购组')" prop="buyerOrgCodes">
              <RemoteAutocomplete
                v-model="searchFormModel.buyerOrgCodes"
                :url="$API.masterData.getBusinessGroupUrl"
                :multiple="true"
                :placeholder="$t('请选择')"
                :fields="{ text: 'groupName', value: 'groupCode' }"
                :search-fields="['groupName', 'groupCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('计划员')" prop="transferPlanCode">
              <RemoteAutocomplete
                v-model="searchFormModel.transferPlanCode"
                url="/masterDataManagement/tenant/employee/paged-query"
                :placeholder="$t('请选择')"
                :fields="{ text: 'employeeName', value: 'employeeCode' }"
                :search-fields="['employeeName', 'employeeCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('送货单类型')" prop="deliveryType">
              <mt-multi-select
                style="flex: 1"
                v-model="searchFormModel.deliveryType"
                :data-source="DeliveryTypeOptions"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                :show-select-all="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('交货单号')" prop="deliveryNo">
              <mt-input
                v-model="searchFormModel.deliveryNo"
                :placeholder="$t('支持粘贴多个精准查询')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('过账日期')" prop="postingDate">
              <mt-date-range-picker
                v-model="searchFormModel.postingDate"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'postingDate')"
              />
            </mt-form-item>
            <mt-form-item :label="$t('是否jit')" prop="jit">
              <mt-select
                style="flex: 1"
                v-model="searchFormModel.jit"
                :data-source="isJitOptions"
                :fields="{ text: 'label', value: 'value' }"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('在途状态')" prop="onWayStatus">
              <mt-multi-select
                style="flex: 1"
                v-model="searchFormModel.onWayStatus"
                :data-source="onWayStatusOptions"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                :show-select-all="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('司机联系方式')" prop="driverPhone">
              <mt-input
                v-model="searchFormModel.driverPhone"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('本次送货数量')" prop="deliveryQuantity">
              <div style="display: flex">
                <div class="operator-list">
                  <mt-select
                    v-model="searchFormModel.deliveryQuantity.operator"
                    :data-source="operatorOptions"
                    popup-width="50px"
                  ></mt-select>
                </div>
                <div class="custom-input-number">
                  <mt-input-number
                    v-model="searchFormModel.deliveryQuantity.number"
                    type="number"
                    :show-spin-button="false"
                    :show-clear-button="true"
                    :placeholder="$t('请输入')"
                  />
                </div>
              </div>
            </mt-form-item>
            <mt-form-item :label="$t('发货日期')" prop="sendTime">
              <mt-date-range-picker
                v-model="searchFormModel.sendTime"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'sendTime')"
              />
            </mt-form-item>
            <mt-form-item :label="$t('收货供应商')" prop="receiveSupplierCodes">
              <RemoteAutocomplete
                v-model="searchFormModel.receiveSupplierCodes"
                url="/masterDataManagement/tenant/supplier/paged-query"
                :multiple="true"
                :placeholder="$t('请选择')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('送货地址')" prop="receiveAddressName">
              <mt-input
                v-model="searchFormModel.receiveAddressName"
                :placeholder="$t('支持模糊搜索')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('取消人')" prop="cancelPersonCode">
              <RemoteAutocomplete
                v-model="searchFormModel.cancelPersonCode"
                url="/masterDataManagement/tenant/employee/paged-query"
                :placeholder="$t('请选择')"
                :fields="{ text: 'employeeName', value: 'employeeCode' }"
                :search-fields="['employeeName', 'employeeCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('取消时间')" prop="cancelTime">
              <mt-date-range-picker
                v-model="searchFormModel.cancelTime"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'cancelTime')"
              />
            </mt-form-item>
            <mt-form-item :label="$t('拒绝原因')" prop="rejectReason">
              <mt-input
                v-model="searchFormModel.rejectReason"
                :placeholder="$t('支持模糊搜索')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('代收标识')" prop="collectorMark">
              <mt-input
                v-model="searchFormModel.collectorMark"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('代收人')" prop="collectorName">
              <RemoteAutocomplete
                v-model="searchFormModel.collectorName"
                url="/masterDataManagement/tenant/employee/paged-query"
                :placeholder="$t('请选择')"
                :fields="{ text: 'employeeCode', value: 'employeeName' }"
                :search-fields="['employeeName', 'employeeCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('卷号')" prop="batchCode">
              <mt-input
                v-model="searchFormModel.batchCode"
                :placeholder="$t('支持粘贴多个精准查询')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="takeNo" :label="$t('车号')">
              <mt-input
                v-model="searchFormModel.takeNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('第三方物流商')" prop="thirdTenantCodes">
              <RemoteAutocomplete
                v-model="searchFormModel.thirdTenantCodes"
                url="/masterDataManagement/tenant/supplier/paged-query"
                :multiple="true"
                :placeholder="$t('请选择')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('创建交货单是否成功')" prop="syncDeliveryNoStatus">
              <mt-select
                v-model="searchFormModel.syncDeliveryNoStatus"
                :data-source="SyncDeliveryNoStatusOptions"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                style="flex: 1"
              />
            </mt-form-item>
            <mt-form-item :label="$t('创建交货单失败信息')" prop="syncDeliveryNoDesc">
              <mt-input
                v-model="searchFormModel.syncDeliveryNoDesc"
                :placeholder="$t('支持模糊搜索')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('是否内需跟单')" prop="domesticDemandFlag">
              <mt-select
                v-model="searchFormModel.domesticDemandFlag"
                :data-source="domesticDemandFlagOptions"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                style="flex: 1"
              />
            </mt-form-item>
            <mt-form-item :label="$t('销售订单号')" prop="saleOrderNo">
              <mt-input
                v-model="searchFormModel.saleOrderNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('销售订单行号')" prop="saleOrderLineNo">
              <mt-input
                v-model="searchFormModel.saleOrderLineNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('内需单号')" prop="domesticDemandCode">
              <mt-input
                v-model="searchFormModel.domesticDemandCode"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
          </mt-form>
        </template>
      </mt-template-page>
    </div>
    <delivery-dialog
      v-if="deliveryShow"
      :ids="id"
      :company-codes="companyCodes"
      ref="child"
      @handleAddDialogShow="handleAddDialogShow"
      @handleDialogShow="handleDialogShow"
    >
    </delivery-dialog>
  </div>
</template>

<script>
import { i18n } from '@/main.js'

const mainTabList = [
  {
    title: i18n.t('送货单列表')
  },
  {
    title: i18n.t('送货单明细')
  },
  {
    title: i18n.t('送货单明细-新')
  }
]
import { BASE_TENANT } from '@/utils/constant'
import dayjs from 'dayjs'
import Vue from 'vue'
import { download, getHeadersFileName } from '@/utils/utils'

import {
  columnData,
  columnData2,
  StatusOptions,
  onWayStatusOptions,
  DeliveryTypeOptions,
  operatorOptions,
  SyncDeliveryNoStatusOptions
} from './config/index.js'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  components: {
    // deliveryDialog,
    DeliveryDialog: require('./components/deliveryDialog').default
  },
  mounted() {
    if (this.$route.query.from === 'mytodo') {
      this.tabIndex = 1
      this.pageConfig1[0].grid.asyncConfig.url = `${BASE_TENANT}/supplierOrderDeliveryItem/page?fromWorkCenter=1`
      this.pageConfig1[0].grid.asyncConfig.defaultRules = JSON.parse(
        sessionStorage.getItem('todoDetail')
      ).defaultRules
    }
  },
  data() {
    const startDate = dayjs().subtract(1, 'month')
    return {
      StatusOptions,
      onWayStatusOptions,
      DeliveryTypeOptions,
      operatorOptions,
      SyncDeliveryNoStatusOptions,
      isJitOptions: [
        { label: this.$t('否'), value: 0 },
        { label: this.$t('是'), value: 1 }
      ],
      searchFormModel: {
        tenantType: 2, // 租户类型 1 采方 2 供方
        sourceFrom: 0, // 来源事业部,0-白电,空调,3-泛智屏
        createTime: [new Date(startDate), new Date()],
        createTimeTo: this.getUnix(new Date(dayjs(new Date()).format('YYYY-MM-DD 23:59:59'))),
        createTimeFrom: this.getUnix(
          new Date(dayjs(new Date(startDate)).format('YYYY-MM-DD 00:00:00'))
        ),
        number: {
          number: null,
          operator: null
        },
        deliveryQuantity: {
          number: null,
          operator: null
        },
        receiveQuantity: {
          number: null,
          operator: null
        },
        rejectQuantity: {
          number: null,
          operator: null
        },
        limitQuantity: {
          number: null,
          operator: null
        }
      },
      id: [],
      companyCodes: [],
      deliveryShow: false,
      index: 0,
      tabIndex: 0,

      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'List', permissionCode: 'T_02_0046' },
          { dataPermission: 'Details', permissionCode: 'T_02_0047' },
          { dataPermission: 'Details', permissionCode: 'T_02_0047' }
        ]
      },
      tabSource: mainTabList,
      pageConfig: [
        {
          dataPermission: 'List',
          activatedRefresh: false,
          showArchive: true,
          permissionCode: 'T_02_0046',
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'MaintainIogisticsInformation',
              icon: 'icon_table_MaintainIogisticsInformation',
              title: this.$t('维护物流信息'),
              permission: ['O_02_0642']
            },
            {
              id: 'AppointmentDelivery1',
              icon: 'icon_table_AppointmentDelivery1',
              title: this.$t('预约送货'),
              permission: ['O_02_0637']
            },
            {
              id: 'copy',
              title: this.$t('复制送货单号')
            },
            // {
            //   id: "print",
            //   icon: "icon_table_print",
            //   title: this.$t("打印条码"),
            //   // permission: ["O_02_0122"],
            // },
            {
              id: 'printBd',
              icon: 'icon_table_print',
              title: this.$t('打印送货单（白电）'),
              permission: ['O_02_1446']
            },
            {
              id: 'print',
              icon: 'icon_table_print',
              title: this.$t('打印送货单（空调）'),
              permission: ['O_02_0638']
            },
            {
              id: 'synchronous',
              icon: 'icon_table_restart',
              title: this.$t('同步')
            },
            {
              id: 'cancel',
              icon: 'icon_table_cancel',
              title: this.$t('取消'),
              permission: ['O_02_0643']
            },
            {
              id: 'new',
              icon: 'icon_table_new',
              title: this.$t('创建无订单送货单'),
              permission: ['O_02_0640']
            },
            {
              id: 'loseeffectiveness',
              icon: 'icon_solid_loseeffectiveness',
              title: this.$t('补订单'),
              permission: ['O_02_0641']
            },
            {
              id: 'createSapdeliveryNode',
              icon: 'icon_table_restart',
              title: i18n.t('创建SAP交货单'),
              permission: ['O_02_1612']
            },
            {
              id: 'listExport',
              icon: 'icon_table_export',
              title: this.$t('导出')
            }
          ],
          buttonQuantity: 6,
          gridId: this.$tableUUID.receiptAndDelivery.deliverListSupplier.list,
          grid: {
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [10, 20, 50, 100, 200, 500, 1000],
              totalRecordsCount: 0
            },
            useToolTemplate: false, // 不使用预置(新增、编辑、删除)
            useBaseConfig: true, // 使用组件中的toolbar配置
            columnData: columnData,
            lineSelection: 0, // 选项列
            lineIndex: 1, // 序号列
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/supplierOrderDelivery/query`
              // ignoreDefaultSearch: true,
            }
            // frozenColumns: 1
          }
        }
      ],
      pageConfig1: [
        {
          dataPermission: 'Details',
          activatedRefresh: false,
          showArchive: true,
          permissionCode: 'T_02_0047',
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'export',
              icon: 'icon_solid_Import',
              permission: ['O_02_1078'],
              title: this.$t('导出')
            },
            {
              id: 'copy',
              title: this.$t('复制送货单号')
            }
          ],
          gridId: this.$tableUUID.receiptAndDelivery.deliverListSupplier.details,
          grid: {
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [10, 20, 50, 100, 200, 500, 1000],
              totalRecordsCount: 0
            },
            columnData: columnData2,
            // lineSelection: 0, // 选项列
            lineIndex: 0,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/supplierOrderDeliveryItem/page`,
              defaultRules: []
              // ignoreDefaultSearch: true,
            }
            // frozenColumns: 1,
          }
        }
      ],
      templateConfig3: [
        {
          dataPermission: 'Details',
          showArchive: true, // 是否显示归档查询
          permissionCode: 'T_02_0047',
          isUseCustomSearch: true, // 是否使用自定义查询
          isCustomSearchRules: true,
          activatedRefresh: false,
          // alwaysShow: true,
          toolbar: [
            {
              id: 'export1',
              icon: 'icon_solid_Import',
              permission: ['O_02_1078'],
              title: this.$t('导出')
            },
            {
              id: 'copy',
              title: this.$t('复制送货单号')
            }
          ],
          useToolTemplate: false, // 使用预置表格操作按钮(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId: 'f9e12710-9eb9-47e1-a311-e3a3272e9000',
          grid: {
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [10, 20, 50, 100, 200, 500, 1000],
              totalRecordsCount: 0
            },
            allowPaging: true, // 分页
            lineIndex: 0, // 序号列
            columnData: columnData2,
            dataSource: [],
            asyncConfig: {
              url: `/statistics/tenant/supplier/delivery/item/view/v1/page`, // 采方送货单-采方查询送货单明细分页
              serializeList: (list) => {
                if (list.length > 0) {
                  list.forEach((item) => {
                    let inputDate = item.inputDate
                    if (item.inputDate && item.inputTime) {
                      inputDate = `${inputDate.split(' ')[0]} ${item.inputTime.split(' ')[1]}`
                    }
                    item.inputDate = inputDate
                  })
                }
                return list
              }
            }
          }
        }
      ],
      userInfo: null,
      addDialogShow: false,
      dialogData: null,
      domesticDemandFlagOptions: [
        { value: 'E', text: this.$t('是') },
        { value: 'F', text: this.$t('否') }
      ]
    }
  },
  methods: {
    dataTimeChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'From'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[flag + 'To'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[flag + 'From'] = null
        this.searchFormModel[flag + 'To'] = null
      }
    },
    getUnix(val) {
      return new Date(val).getTime()
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'tenantType') {
            this.searchFormModel[key] = 2
          }
          if (key === 'sourceFrom') {
            this.searchFormModel[key] = 0
          }
          if (
            key === 'number' ||
            key === 'deliveryQuantity' ||
            key === 'receiveQuantity' ||
            key === 'rejectQuantity' ||
            key === 'limitQuantity'
          ) {
            this.searchFormModel[key] = {
              number: null,
              operator: null
            }
          }
        }
      }
    },
    // 弹框
    handleDialogShow(flag) {
      this.deliveryShow = flag
    },
    handleSelectTab(e) {
      this.tabIndex = e
      if (e == 1) {
        setTimeout(() => {
          if (this.$refs.templateRef1.getCurrentUsefulRef().pluginRef.resizeGridHeight) {
            this.$refs.templateRef1.getCurrentUsefulRef().pluginRef.resizeGridHeight()
          }
        }, 0)
      }
    },
    // 点击单元格 标题
    handleClickCellTitle(e) {
      if (e.field === 'deliveryCode') {
        localStorage.setItem('deliverDetailSupplier', JSON.stringify(e.data))
        this.$router.push(`deliver-detail-supplier?id=${e.data.id}&type=` + e.data.deliveryType)
      }
    },
    handleClickCellTitle1(e) {
      if (e.field === 'deliveryCode') {
        this.getDeliverDataById(e.data?.deliveryId)
      }
    },
    // 根据id查询主单数据
    getDeliverDataById(id) {
      const params = {
        condition: 'and',
        page: { current: 1, size: 20 },
        rules: [
          {
            field: 'id',
            operator: 'equal',
            value: id
          }
        ]
      }
      this.$API.receiptAndDelivery
        .getSupplierDeliveyData(params)
        .then((res) => {
          if (res?.code == 200) {
            // 跳转详情页

            localStorage.setItem('deliverDetailSupplier', JSON.stringify(res?.data?.records[0]))
            this.$router.push(
              `deliver-detail-supplier?id=${res?.data?.records[0].id}&type=` +
                res?.data?.records[0].deliveryType
            )
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleClickCellTool(e) {
      if (e.tool.title === this.$t('维护物流信息')) {
        this.$router.push({
          name: 'deliver-logistics-supplier',
          query: {
            id: e.data.id
          }
        })
        localStorage.setItem('deliverDetailSupplier', JSON.stringify(e.data))
      }
      if (e.tool.title === this.$t('取消')) {
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消？')
          },
          success: () => {
            this.$store.commit('startLoading')
            this.$API.receiptAndDelivery
              .supplierOrderDeliveryCancel([e.data.id])
              .then((res) => {
                this.$store.commit('endLoading')
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('操作成功'),
                    type: 'success'
                  })
                  this.$refs.templateRef.refreshCurrentGridData()
                  // this.confirmSuccess();
                }
              })
              .catch(() => {
                this.$refs.templateRef.refreshCurrentGridData()

                this.$store.commit('endLoading')
              })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    handleClickToolBar(e) {
      console.log(e)

      if (e.toolbar.id == 'new') {
        this.$router.push(`create-noOrder-supplier`)
        return
      }
      if (e.toolbar.id == 'listExport') {
        const asyncParams = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.asyncParams || {}
        const params = {
          ...asyncParams
        }
        this.$store.commit('startLoading')
        this.$API.receiptAndDelivery.supExportDeliveryApi(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
      if (e.toolbar.id == 'export') {
        const asyncParams =
          this.$refs.templateRef1.getCurrentUsefulRef().pluginRef.asyncParams || {}
        const params = {
          ...asyncParams
        }
        this.$store.commit('startLoading')
        this.$API.receiptAndDelivery.supplierOrderDeliveryItemExport(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
      if (e.toolbar.id == 'export1') {
        let obj = JSON.parse(sessionStorage.getItem(this.templateConfig3[0].gridId))?.visibleCols
        const headerMap = {}
        if (obj !== undefined && obj.length) {
          obj?.forEach((i) => {
            if (i.field !== 'customChecked' && i.field !== 'id') {
              headerMap[i.field] = i.headerText
            }
          })
        } else {
          this.templateConfig3[0].grid.columnData?.forEach((i) => {
            if (i.field !== 'customChecked' && i.field !== 'id') {
              headerMap[i.field] = i.headerText
            }
          })
        }
        const params = {
          headerMap,
          page: { current: 1, size: 5000 },
          ...this.searchFormModel
        } // 筛选条件
        this.$store.commit('startLoading')
        this.$API.receiptAndDelivery.buyerOrderDeliveryQueryNewExport(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
      if (e.gridRef.getMtechGridRecords()?.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const _status = []
      const _forecastDelivery = []
      const _id = []
      const _codes = []
      e.gridRef.getMtechGridRecords().map((item) => {
        _id.push(item.id)
        _codes.push(item.companyCode)
        _status.push(item.status)
        _forecastDelivery.push(item.forecastDelivery)
        // _sourceStatus.push(item.sourceStatus),
        // _remainingQuantity.push(item.remainingQuantity);
      })
      this.id = _id
      this.companyCodes = _codes
      if (e.toolbar.id == 'MaintainIogisticsInformation') {
        for (let i of _status) {
          if (i !== 2) {
            this.$toast({
              content: this.$t('只有状态在发货中才可以维护'),
              type: 'warning'
            })
            return
          }
        }
        this.deliveryShow = true
        return
      }

      if (e.toolbar.id === 'copy') {
        this.handleCopy(e.gridRef.getMtechGridRecords())
      }

      if (e.toolbar.id === 'AppointmentDelivery1') {
        for (let i of _status) {
          if (i !== 2) {
            this.$toast({
              content: this.$t('只有状态在发货中才可以预约'),
              type: 'warning'
            })
            return
          }
        }
        for (let i of _forecastDelivery) {
          if (i == 1) {
            this.$toast({
              content: this.$t('请勾选可预约的数据'),
              type: 'warning'
            })
            return
          }
        }
        // 预约送货
        const deliveryCodeList = []
        let deliveryStatusList = []
        const data = e.gridRef.getMtechGridRecords()
        data.forEach((item) => {
          // 送货单号
          deliveryCodeList.push(item.deliveryCode)
          // 交货方式
          deliveryStatusList.push(item.itemDeliveryType) // 交货方式:1-采购订单,2-交货计划,3-JIT,4-无需求,5-vmi
        })
        const purTenantId = data[0].purTenantId // 采方租户id

        // 交货方式 去空、去重
        deliveryStatusList = [...new Set(deliveryStatusList.filter((item) => item))]
        // 存 localStorage 预约送货 页面读
        localStorage.setItem(
          'toReservationDeliverData',
          JSON.stringify({
            selectedRowData: data,
            deliveryCodeList,
            deliveryStatusList,
            purTenantId // 采方租户id
          })
        )
        // 预约送货跳转
        this.$router.push({
          name: 'reservation-deliver-supplier',
          query: {
            type: 'new'
          },
          params: {
            sourceType: 0 //1:入库单 0:送货单
          }
        })
        return
      }

      if (e.toolbar.id === 'print') {
        for (let item of e.gridRef.getMtechGridRecords()) {
          if (item.status === 4 || item.status === 5) {
            this.$toast({
              content: this.$t('不可以打印已取消和已关闭的单据'),
              type: 'warning'
            })
            return
          }
        }
        this.print(_id, 'kt')
      }
      if (e.toolbar.id === 'printBd') {
        for (let item of e.gridRef.getMtechGridRecords()) {
          if (item.status === 4 || item.status === 5) {
            this.$toast({
              content: this.$t('不可以打印已取消和已关闭的单据'),
              type: 'warning'
            })
            return
          }
          if (item.companyCode !== '0530') {
            this.$toast({
              content: this.$t(
                '勾选打印的送货单不属于【0530-TCL家用电器（合肥）有限公司】不可打印。'
              ),
              type: 'warning'
            })
            return
          }
        }
        this.print(_id, 'bd')
      }
      if (e.toolbar.id == 'cancel') {
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消？')
          },
          success: () => {
            for (let i of _status) {
              if (i !== 2) {
                this.$toast({
                  content: this.$t('只有状态在发货中才可以关闭'),
                  type: 'warning'
                })
                return
              }
            }
            this.handleClaim(_id)
          }
        })
      }

      if (e.toolbar.id == 'loseeffectiveness') {
        this.$API.receiptAndDelivery
          .supplierOrderDeliveryRepair(_id)
          .then((res) => {
            this.$store.commit('startLoading')
            if (res.code == 200) {
              this.$store.commit('endLoading')

              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
          .catch(() => {
            this.$store.commit('endLoading')
            this.$refs.templateRef.refreshCurrentGridData()
          })
      }
      if (e.toolbar.id === 'synchronous') {
        if (e.gridRef.getMtechGridRecords().length <= 0) {
          this.$toast({ content: this.$t('请选择数据'), type: 'warning' })
          return
        } else {
          this.synchronousWms(e.gridRef.getMtechGridRecords())
        }
      } else if (e.toolbar.id === 'createSapdeliveryNode') {
        if (e.gridRef.getMtechGridRecords().length <= 0) {
          this.$toast({ content: this.$t('请选择数据'), type: 'warning' })
          return
        } else {
          this.createSapdeliveryNote(e.gridRef.getMtechGridRecords())
        }
      }
    },
    print(_id, type) {
      if (type === 'kt') {
        this.$API.receiptAndDelivery.supplierOrderDeliveryPrintHtmlCheck(_id).then((res) => {
          console.log(res)
          if (res.code === 200) {
            this.$API.receiptAndDelivery.supplierOrderDeliveryPrintHtml(_id).then((res) => {
              const content = res.data
              this.pdfUrl = window.URL.createObjectURL(
                new Blob([content], { type: 'text/html;charset=utf-8' })
              )
              // window.open(this.pdfUrl);
              let date = new Date().getTime()
              let ifr = document.createElement('iframe')
              ifr.style.frameborder = 'no'
              ifr.style.display = 'none'
              ifr.style.pageBreakBefore = 'always'
              ifr.setAttribute('id', 'printPdf' + date)
              ifr.setAttribute('name', 'printPdf' + date)
              ifr.src = this.pdfUrl
              document.body.appendChild(ifr)
              this.doPrint('printPdf' + date)
              window.URL.revokeObjectURL(ifr.src)
            })
          } else {
            this.$toast({ content: res.msg, type: 'warning' })
          }
        })
      } else if (type === 'bd') {
        this.$API.receiptAndDelivery.supplierOrderDeliveryPrint(_id).then((res) => {
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = function () {
              console.log('======', reader)
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }

            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
          // window.open(this.pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
      }
    },
    handleCopy(selectRows) {
      const deliveryCodeList = selectRows.map((i) => {
        return i.deliveryCode
      })
      const str = [...new Set(deliveryCodeList)].join(' ')
      this.copyToClipboard(str)
    },
    copyToClipboard(str) {
      const el = document.createElement('textarea')
      el.value = str
      el.setAttribute('readonly', '')
      el.style.position = 'absolute'
      el.style.left = '-9999px'
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)
    },
    // 同步WMS
    synchronousWms(data) {
      let _ids = []
      let _flag = true
      data.forEach((item) => {
        if (item.wmsSyncStatus === 1) {
          _flag = false
        }
        _ids.push(item.id)
      })
      if (!_flag) {
        this.$toast({
          content: this.$t('送货单已成功同步WMS，无需重复同步'),
          type: 'warning'
        })
        return
      }
      this.$API.purchaseCoordination.deliverySupSynchronousWms(_ids).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('同步成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('同步失败'), type: 'warning' })
        }
      })
    },
    // 创建SAP交货单
    createSapdeliveryNote(data) {
      let _ids = []
      data.forEach((item) => {
        _ids.push(item.id)
      })
      this.$store.commit('startLoading')
      this.$API.purchaseCoordination
        .createSapdeliveryNote(_ids)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t(res.msg || '创建成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          } else {
            this.$toast({ content: this.$t(res.msg || '创建失败'), type: 'warning' })
          }
        })
        .catch((err) => {
          this.$toast({
            type: 'error',
            content: this.$t(err.msg || '创建失败')
          })
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    handleClaim(_id) {
      this.$store.commit('startLoading')

      this.$API.receiptAndDelivery
        .supplierOrderDeliveryCancel(_id)
        .then((res) => {
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef.refreshCurrentGridData()
        })
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('todoDetail')
  }
}
</script>

<style></style>
