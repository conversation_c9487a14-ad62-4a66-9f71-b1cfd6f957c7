<template>
  <div class="full-height pt20 vertical-flex-box">
    <top-info
      v-if="headerInfo"
      ref="headerTop"
      @bottom="bottom"
      :header-info="headerInfo"
      class="flex-keep"
    ></top-info>
    <mt-tabs
      :e-tab="false"
      tab-id="deliver-tab"
      :selected-item="1"
      :data-source="componentConfig"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <div class="flex-fit" v-show="currentInfo.title == $t('物料信息') && this.type !== 'add'">
      <mt-template-page ref="templateRef" :template-config="pageConfig1"> </mt-template-page>
    </div>
    <div class="flex-fit" v-show="currentInfo.title == $t('物料信息') && this.type === 'add'">
      <mt-template-page ref="templateRef" :template-config="pageConfig1" :hidden-tabs="true" />
    </div>
    <bottom-info
      class="flex-keep"
      ref="bottomInfo"
      @bottom="bottom"
      v-show="currentInfo.title == $t('物流信息')"
      :bottom-info="headerInfo"
    ></bottom-info>
  </div>
</template>

<script>
import { columnData } from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'

export default {
  components: {
    topInfo: require('./components/topInfo').default,
    bottomInfo: require('./components/bottomInfo').default
  },
  data() {
    const headerInfo = JSON.parse(localStorage.getItem('deliverDetailSupplier')) || {}
    // 送货类型
    // if (headerInfo.deliveryType == 1) {
    //   headerInfo.deliveryType = this.$t("关联采购订单");
    // } else if (headerInfo.deliveryType == 2) {
    //   headerInfo.deliveryType = this.$t("无采购订单");
    // } else {
    //   headerInfo.deliveryType = "";
    // }

    headerInfo.deliveryType =
      headerInfo.deliveryType === 1
        ? this.$t('采购订单')
        : headerInfo.deliveryType === 2
        ? this.$t('交货计划')
        : headerInfo.deliveryType === 3
        ? this.$t('JIT')
        : headerInfo.deliveryType === 4
        ? this.$t('无需求')
        : headerInfo.deliveryType === 5
        ? this.$t('vmi')
        : headerInfo.deliveryType === 6
        ? this.$t('钢材')
        : ''
    headerInfo.status === 1
      ? this.$t('新建')
      : headerInfo.status === 2
      ? this.$t('发货中')
      : headerInfo.status === 3
      ? this.$t('已完成')
      : headerInfo.status === 4
      ? this.$t('已取消')
      : headerInfo.status === 5
      ? this.$t('已关闭')
      : ''
    const { id } = this.$route.query
    return {
      params: {},
      pageConfig: [{ title: this.$t('物料信息') }, { title: this.$t('物流信息') }],
      userInfo: null,
      headerInfo,
      currentInfo: {
        title: this.$t('物流信息')
      },
      type: '',
      pageConfig1: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          gridId: this.$tableUUID.receiptAndDelivery.deliverLogisticsSupplier.materialInfo,
          grid: {
            columnData: columnData,
            // lineIndex: 0,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/supplierOrderDeliveryItem/page`,
              defaultRules: [
                {
                  field: 'deliveryId',
                  operator: 'equal',
                  value: id
                }
              ]
            }
            // frozenColumns: 1,
          }
        }
      ],
      componentConfig: [
        {
          title: this.$t('物料信息')
        },
        {
          title: this.$t('物流信息')
        }
      ],
      currentTabIndex: 0,
      forecastRules: []
    }
  },
  mounted() {},
  methods: {
    bottom(value) {
      let a = false
      a = this.$refs.bottomInfo.validateCick()
      if (a === false) {
        return
      } else {
        if (value) {
          this.params = value
        } else {
          // 修改

          this.$store.commit('startLoading')
          this.$API.receiptAndDelivery
            .supplierDeliveryLogisticsInfoSave(this.params)
            .then((res) => {
              if (res.code == 200) {
                this.$store.commit('endLoading') //loading
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$router.go(-1)
              }
            })
            .catch(() => {
              this.$toast({ content: this.$t('操作失败'), type: 'error' })
              this.$store.commit('endLoading') //loading
            })
        }
      }
    },
    handleSelectTab(index, item) {
      this.currentInfo = item
    }
  },
  // 消失
  beforeDestroy() {
    localStorage.removeItem('deliverDetailSupplier')
  }
}
</script>

<style lang="scss" scoped>
.repeat-template {
  .common-template-page {
    /deep/ .mt-tabs {
      display: none;
    }
  }
}
.e-content {
  height: 200px;
}

.mt-tabs {
  width: 100%;
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
</style>
