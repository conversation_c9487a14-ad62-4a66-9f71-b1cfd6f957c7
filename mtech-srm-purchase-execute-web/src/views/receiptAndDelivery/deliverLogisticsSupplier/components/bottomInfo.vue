<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 下面的内容 -->
    <div class="main-bottom" style="padding-top: 10px">
      <mt-form ref="addForm" :model="addForm" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="type" :label="$t('发货方式')">
          <mt-select
            v-model="addForm.type"
            :data-source="deliveryTypeOptions"
            @change="selectChange"
            :show-clear-button="false"
            :placeholder="$t('请选择发货方式')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item v-if="addForm.type === 1" prop="logisticsCompanyCode" :label="$t('物流公司')">
          <mt-select
            v-if="addForm.type === 1"
            ref="businessRef"
            v-model="addForm.logisticsCompanyCode"
            :data-source="logisticsCompanyOptions"
            :show-clear-button="false"
            :placeholder="$t('请选择物流公司')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item v-if="addForm.type === 1" prop="logisticsNo" :label="$t('物流单号')">
          <mt-input
            v-if="addForm.type === 1"
            v-model="addForm.logisticsNo"
            :maxlength="50"
            :placeholder="$t('请输入物流单号')"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item v-if="addForm.type === 1" prop="number" :label="$t('件数')">
          <mt-input-number
            v-model="addForm.number"
            :maxlength="8"
            :min="0"
            :placeholder="$t('请输入件数')"
          ></mt-input-number>
        </mt-form-item> -->

        <mt-form-item v-if="addForm.type === 2" prop="driverName" :label="$t('司机姓名')">
          <combobox
            v-model="addForm.driverName"
            :fields="{ text: 'name', value: 'name' }"
            :data-source="driverNameOptions"
            :placeholder="$t('请选择')"
            @change="driverNameChange"
            @blur="driverNameBlur"
          />
        </mt-form-item>
        <mt-form-item v-if="addForm.type === 2" prop="driverNo" :label="$t('司机身份证号')">
          <mt-input
            v-if="addForm.type === 2"
            v-model="addForm.driverNo"
            :maxlength="18"
            :placeholder="$t('请输入司机身份证号')"
            :disabled="idCardDisabled"
          ></mt-input>
        </mt-form-item>
        <mt-form-item v-if="addForm.type === 2" prop="carNo" :label="$t('车牌号')">
          <mt-input
            v-if="addForm.type === 2"
            v-model="addForm.carNo"
            :maxlength="30"
            :placeholder="$t('请输入车牌号')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="driverPhone"
          :label="$t('司机联系方式')"
          :placeholder="$t('请输入联系方式')"
          v-if="addForm.type === 2"
        >
          <mt-input
            v-if="addForm.type === 2"
            v-model="addForm.driverPhone"
            maxlength="11"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="number" :label="$t('件数')">
          <mt-input-number
            v-model="addForm.number"
            :min="0"
            :placeholder="$t('请输入件数')"
            :maxlength="8"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input
            v-model="addForm.remark"
            :maxlength="500"
            :placeholder="$t('请输入备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { RegExpMap } from '@/utils/constant'
import Combobox from '@/components/combobox'
export default {
  props: {
    userInfo: {
      type: Object,
      default: () => {}
    },
    bottomInfo: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    Combobox
  },
  data() {
    const { idCardReg, phoneNumRegCN, phoneNumRegVN } = RegExpMap

    const identityNumberValidator = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('请输入司机身份证号')))
      } else if (!idCardReg.test(value)) {
        return callback(new Error(this.$t('身份证号格式错误')))
      } else {
        this.$refs.addForm.clearValidate(['driverNo'])
        return callback()
      }
    }
    const phoneNumberValidator = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('请输入司机手机号')))
      } else if (!phoneNumRegCN.test(value) && !phoneNumRegVN.test(value)) {
        return callback(new Error(this.$t('请输入正确的手机号')))
      } else {
        this.$refs.addForm.clearValidate(['driverPhone'])
        return callback()
      }
    }
    return {
      detailList: [],
      id: '',
      isInit: true, // 是第一次赋值
      isExpand: true,
      type: null, // add view edit

      deliveryTypeOptions: [
        {
          value: 1,
          text: this.$t('快递配送')
        },
        {
          value: 2,
          text: this.$t('物流配送')
        }
      ], // 业务类型
      applyUserIdData: [], // 申请人列表
      logisticsCompanyOptions: [], // 物流公司
      organizationData: {},
      applyCompanyData: [], // 公司
      applyDepartData: [], // 部门
      addForm: {
        carNo: '',
        deliveryId: '',
        driverName: '',
        driverNo: '',
        driverPhone: '',
        idList: [],
        logisticsCompanyCode: '',
        logisticsCompanyName: '',
        logisticsNo: '',
        number: '',
        remark: '',
        tenantId: '',
        type: 0
      },
      rules: {
        type: [
          {
            required: true,
            message: this.$t('请选择发货方式'),
            trigger: 'blur'
          }
        ],
        logisticsCompanyCode: [
          {
            required: true,
            message: this.$t('请选择物流公司'),
            trigger: 'blur'
          }
        ],
        logisticsNo: [
          {
            required: true,
            message: this.$t('请输入物流单号'),
            trigger: 'blur'
          }
        ],
        // number: [
        //   { required: true, message: this.$t("请输入件数"), trigger: "blur" },
        // ],
        driverName: [
          {
            required: true,
            message: this.$t('请输入司机姓名'),
            trigger: 'blur'
          }
        ],
        driverNo: [
          {
            required: true,
            validator: identityNumberValidator,

            trigger: 'blur'
          }
        ],
        driverPhone: [
          {
            required: true,
            validator: phoneNumberValidator,
            trigger: 'blur'
          }
        ],
        carNo: [
          {
            required: true,
            message: this.$t('请输入车牌号'),
            trigger: 'blur'
          }
        ]
      },
      isClickCancel: false, //手动点击了取消按钮， 为了不再次触发change事件
      driverNameOptions: [], // 司机姓名下拉数据源
      idCardDisabled: false,
      showNumber: false
    }
  },

  computed: {},

  watch: {
    addForm: {
      handler(newValue) {
        this.$emit('bottom', newValue)
      },
      deep: true
    }
  },

  mounted() {
    this.id = this.$route.query.id

    this.getList()
    // 获取送货司机下拉数据源
    this.getDriverNameOptions()
  },

  methods: {
    validateCick() {
      let flag = false
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          flag = true
        } else {
          flag = false
        }
      })
      return flag
    },
    selectChange(e) {
      console.log(e)
      console.log(this.type)
      this.addForm.type = e.value
      if (e.e !== null) {
        if (this.type == e.value) {
          console.log(this.detailList)

          this.addForm = this.detailList

          console.log(this.addForm)
        } else {
          this.addForm.logisticsCompanyCode = ''
          this.addForm.logisticsNo = ''
          this.addForm.driverPhone = ''
          this.addForm.carNo = ''
          this.addForm.driverNo = ''
          this.addForm.driverName = ''
        }
        if (this.type == null) {
          // 物流公司
          this.addForm.logisticsCompanyCode = ''
          // 物流单号
          this.addForm.logisticsNo = ''
          // 司机手机号
          this.addForm.driverPhone = ''
          // 车牌号
          this.addForm.carNo = ''
          // 司机身份证号码
          this.addForm.driverNo = ''
          // 司机姓名
          this.addForm.driverName = ''
        }
        // if (this.type === 1) {
        //   this.addForm.driverPhone = "";
        //   this.addForm.carNo = "";
        //   this.addForm.driverNo = "";
        //   this.addForm.driverName = "";
        // }

        // this.$refs.addForm.resetFields()
      }
      this.$nextTick(() => {
        this.validateCick()
      })
    },
    // 司机姓名 change
    driverNameChange(e) {
      const { itemData } = e
      if (itemData) {
        this.addForm.driverPhone = itemData.contact // 司机联系方式
        this.addForm.carNo = itemData.license // 车牌号
        this.addForm.driverNo = itemData.idCard // 司机身份证号
      } else {
        this.addForm.driverPhone = '' // 司机联系方式
        this.addForm.carNo = '' // 车牌号
        this.addForm.driverNo = '' // 司机身份证号
      }
    },
    driverNameBlur() {
      const _arr = this.driverNameOptions.map((item) => item.name)
      this.idCardDisabled = _arr.includes(this.addForm.driverName)
    },
    // 获取司机姓名下拉数据源
    getDriverNameOptions() {
      const params = {
        page: { current: 1, size: 10000 },
        condition: 'and',
        defaultRules: [
          {
            label: this.$t('状态'),
            field: 'status',
            type: 'string',
            operator: 'equal',
            value: 1
          }
        ],
        companyCodeList: [this.bottomInfo?.companyCode]
      }

      this.$API.receiptAndDelivery
        .postSupplierDriverQuery(params)
        .then((res) => {
          const data = res?.data?.records || []
          this.driverNameOptions = data
        })
        .catch(() => {})
    },

    getList() {
      // 初始化数据
      this.$API.receiptAndDelivery.tenantSupplierDeliveryLogisticsInfo(this.id).then((res) => {
        this.addForm = res.data
        this.type = res.data.type
        this.addForm.idList = []
        this.addForm.idList.push(this.$route.query.id)

        this.detailList = res.data
      })
      // 获取物流公司
      this.$API.masterData //物流公司
        .getCommonDictItemTree({
          dictCode: 'logisticsCompany'
        })
        .then((res) => {
          if (res.data && res.data.length) {
            this.logisticsCompanyOptions = res.data.map((item) => {
              return {
                text: item.itemName,
                value: item.itemCode,
                id: item.id
              }
            })
          }
        })
    },

    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}

.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      &.more-width {
        width: 450px;
      }

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
