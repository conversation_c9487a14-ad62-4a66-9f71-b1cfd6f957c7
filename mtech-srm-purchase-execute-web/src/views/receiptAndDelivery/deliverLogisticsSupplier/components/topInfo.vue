<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 测试用字段prSubmitFlag:{{ prSubmitFlag }} -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="infos mr20">{{ $t('更新人：') }}{{ getUpdateUserName(headerInfo) }}</div>
      <div class="infos">{{ $t('更新日期：') }}{{ getUpdateTime(headerInfo) }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧各种操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <!-- 草稿和审批拒绝状态可以保存草稿 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="startToSave()">{{
        $t('保存')
      }}</mt-button>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="addForm" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="businessTypeId" :label="$t('工厂')">
          <mt-input
            v-model="headerInfo.siteName"
            :show-clear-button="false"
            :disabled="true"
            :placeholder="$t('请选择工厂')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="businessTypeId" :label="$t('公司')">
          <mt-input
            v-model="headerInfo.companyName"
            :disabled="true"
            :placeholder="$t('请选择公司')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="title" :label="$t('交货库存地点')">
          <mt-input
            v-model="headerInfo.warehouseName"
            :disabled="true"
            :maxlength="50"
            :placeholder="$t('交货库存地点')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="deliveryType" :label="$t('送货类型')">
          <mt-input
            v-model="headerInfo.deliveryType"
            :disabled="true"
            :placeholder="$t('送货类型')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="outsourcedType" :label="$t('委外方式')">
          <mt-input
            ref="businessRef"
            v-model="headerInfo.outsourcedType"
            :disabled="true"
            :placeholder="$t('委外方式')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="title" :label="$t('送货单编号')">
          <mt-input
            v-model="headerInfo.deliveryCode"
            :disabled="true"
            :maxlength="50"
            :placeholder="$t('送货单编号')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="businessTypeId" :label="$t('送货方式')">
          <mt-input
            ref="businessRef"
            v-model="headerInfo.shipType"
            :disabled="true"
            :placeholder="$t('请输入送货方式')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="title" :label="$t('发货地点')">
          <mt-input
            v-model="headerInfo.sendAddressName"
            :disabled="true"
            :maxlength="50"
            :placeholder="$t('请输入发货地点')"
          ></mt-input>
        </mt-form-item>
        <!-- 暂无 -->
        <!-- <mt-form-item prop="title" :label="$t('送货地址')">
          <mt-input
            v-model="headerInfo.title"
            :disabled="true"
            :maxlength="50"
            :placeholder="$t('送货地址')"
          ></mt-input>
        </mt-form-item> -->
        <mt-form-item prop="title" :label="$t('发货日期')">
          <mt-date-time-picker
            v-model="headerInfo.sendTime"
            :disabled="true"
            :placeholder="$t('选择日期和时间')"
          ></mt-date-time-picker>
        </mt-form-item>
        <mt-form-item prop="title" :label="$t('预计到货日期')">
          <mt-date-time-picker
            :disabled="true"
            v-model="headerInfo.forecastArriveTime"
            :placeholder="$t('选择日期和时间')"
          ></mt-date-time-picker>
        </mt-form-item>

        <mt-form-item prop="receiverContactName" :label="$t('收货联系人')">
          <mt-input
            v-model="headerInfo.receiverContactName"
            :disabled="true"
            :placeholder="$t('收货联系人')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="receiverContact" :label="$t('联系人方式')">
          <mt-input
            v-model="headerInfo.receiverContact"
            :disabled="true"
            :placeholder="$t('联系人方式')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input
            :multiline="true"
            v-model="headerInfo.remark"
            :placeholder="$t('备注')"
            :maxlength="200"
            :disabled="true"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
// import { cloneDeep } from "lodash";
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isInit: true, // 是第一次赋值
      isExpand: true,
      type: '', // add view edit

      organizationData: {},
      applyCompanyData: [], // 公司
      applyDepartData: [], // 部门
      addForm: {
        title: '', // 采购申请名称
        businessTypeId: '', // 业务类型
        // businessTypeCode: "",
        // businessTypeName: "",
        applyUserId: '', // 申请人
        // applyUserName: "",
        // applyUserCode: ""
        companyId: '', // 公司
        // companyName: "",
        // companyCode: ""
        applyDepId: '', // 申请部门
        // applyDepCode: "",
        // applyDepName: "",
        remark: '',
        projectDesc: '' // 项目名称的聚合
      },
      rules: {
        businessTypeId: [
          {
            required: true,
            message: this.$t('请选择业务类型'),
            trigger: 'blur'
          }
        ],
        applyUserId: [{ required: true, message: this.$t('请选择申请人'), trigger: 'blur' }],
        companyId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        applyDepId: [
          {
            required: true,
            message: this.$t('请选择申请部门'),
            trigger: 'blur'
          }
        ]
      },
      isClickCancel: false //手动点击了取消按钮， 为了不再次触发change事件
    }
  },

  computed: {
    // 采购申请 保存flag 0：默认不处理；1：保存草稿；2：提交
    prSubmitFlag() {
      return this.$store.state.prSubmitFlag
    },

    // 0-草稿，1. 待审批，2. 审批通过，3. 审批拒绝，4. 关闭
    // !查看状态 && !商城转申请 && (状态为0或3)
    canSave() {
      return (
        this.$route.query.type != 'view' &&
        this.$route.query.source != 2 &&
        this.headerInfo &&
        (this.headerInfo.status == 0 || this.headerInfo.status == 3)
      )
    },
    // !查看状态 && (状态为0或3 || 是 补充信息操作)
    canSubmit() {
      return (
        this.$route.query.type != 'view' &&
        ((this.headerInfo && (this.headerInfo.status == 0 || this.headerInfo.status == 3)) ||
          this.type == 'replanish')
      )
    }
  },

  watch: {},

  mounted() {
    this.type = this.$route.query.type
    this.headerInfo.shipType = this.headerInfo.shipType === 0 ? this.$t('直送') : this.$t('非直送')
    this.headerInfo.outsourcedType =
      this.headerInfo.outsourcedType === '0'
        ? this.$t('标准委外')
        : this.headerInfo.outsourcedType === '1'
        ? this.$t('销售委外')
        : this.headerInfo.outsourcedType === '2'
        ? this.$t('非委外')
        : ''
    this.timeDate()
  },

  methods: {
    // 时间转换
    timeDate() {
      let sendData = this.dateFormat(this.headerInfo.sendTime)
      let sendDataTime = this.timeFormat(this.headerInfo.sendTime)
      this.headerInfo.sendTime = sendData + '-' + sendDataTime
      console.log(this.headerInfo.sendTime)
      let forecastData = this.dateFormat(this.headerInfo.forecastArriveTime)
      let forecastTime = this.timeFormat(this.headerInfo.forecastArriveTime)

      this.headerInfo.forecastArriveTime = forecastData + '-' + forecastTime
    },
    dateFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }
      return str
    },
    timeFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'HH:MM:SS', value })
      } else {
        str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
      }
      return str
    },
    // 更新人
    getUpdateUserName(data) {
      let userName = ''
      if (data.updateUserName) {
        userName = data.updateUserName // 更新人
      } else {
        userName = data.createUserName // 创建人
      }
      return userName
    },
    // 更新日期
    getUpdateTime(data) {
      let dateStr = ''
      let timeStr = ''
      if (data.updateTime) {
        dateStr = this.dateFormat(data.updateTime) // 更新日期
        timeStr = this.timeFormat(data.updateTime) // 更新时间
      } else {
        dateStr = this.dateFormat(data.createTime) // 创建日期
        timeStr = this.timeFormat(data.createTime) // 创建时间
      }
      return `${dateStr} ${timeStr}`
    },
    startToSave() {
      this.$emit('bottom')
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      &.more-width {
        width: 450px;
      }

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
