import { i18n } from '@/main.js'
// 头视图
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('VMI退货单号'),
    cssClass: 'field-content'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('待确认'),
        2: i18n.t('已取消'),
        3: i18n.t('已接收'),
        4: i18n.t('已退回')
      }
    },
    cellTools: [
      {
        id: 'Receive',
        icon: 'icon_solid_pushorder',
        title: i18n.t('接收'),
        visibleCondition: (data) => {
          return data.status == 0
        }
      },
      {
        id: 'Return',
        icon: 'icon_solid_pushorder',
        title: i18n.t('退回'),
        visibleCondition: (data) => {
          return data.status == 0
        }
      }
    ]
  },
  {
    field: 'plantCode',
    headerText: i18n.t('工厂编号')
  },
  {
    field: 'plantDescription',
    headerText: i18n.t('工厂描述')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierDescription',
    headerText: i18n.t('供应商描述')
  },
  {
    field: 'VMIWarehouseCode',
    headerText: i18n.t('VMI仓编码')
  },
  {
    field: 'VMIWarehouseDescription',
    headerText: i18n.t('VMI仓描述')
  },
  {
    field: 'shippingAddress',
    headerText: i18n.t('送货地址')
  },
  {
    field: 'preparationDate',
    headerText: i18n.t('制单日期')
  },
  {
    field: 'preparer',
    headerText: i18n.t('制单人')
  }
]
// 明细视图
export const columnDataInfo = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('VMI退货单号'),
    cssClass: 'field-content' //自定义Title显示样式
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    cssClass: 'field-content', //自定义Title显示样式
    valueConverter: {
      type: 'map',
      map: {
        5: i18n.t('新建')
      }
    }
  },
  {
    field: 'lineNumber',
    headerText: i18n.t('行号')
  },
  {
    field: 'materialCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'materialDescription',
    headerText: i18n.t('物料描述')
  },
  {
    field: 'inventoryStatus',
    headerText: i18n.t('库存状态')
  },
  {
    field: 'batch',
    headerText: '批次/卷号'
  },
  {
    field: 'returnQuantity',
    headerText: i18n.t('退货数量')
  },
  {
    field: 'receivedQuantity',
    headerText: i18n.t('实收数量')
  },
  {
    field: 'Company',
    headerText: i18n.t('单位')
  },
  {
    field: 'procurementGroup',
    headerText: i18n.t('采购组')
  },
  {
    field: 'lineRemarks',
    headerText: i18n.t('行备注')
  },
  {
    field: 'associatedPurchaseOrderNumber',
    headerText: i18n.t('关联采购订单号')
  },
  {
    field: 'associatedPurchaseOrderLineNumber',
    headerText: i18n.t('关联采购订单行号')
  },
  {
    field: 'factoryNumber',
    headerText: i18n.t('工厂编号')
  },
  {
    field: 'plantDescription',
    headerText: i18n.t('工厂描述')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierDescription',
    headerText: i18n.t('供应商描述')
  },
  {
    field: 'VMIWarehouseCode',
    headerText: i18n.t('VMI仓编码')
  },
  {
    field: 'VMIWarehouseDescription',
    headerText: i18n.t('VMI仓描述')
  },
  {
    field: 'shippingAddress',
    headerText: i18n.t('送货地址')
  },
  {
    field: 'preparationDate',
    headerText: i18n.t('制单日期')
  },
  {
    field: 'preparer',
    headerText: i18n.t('制单人')
  }
]
