<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <!-- sourcing -->
  </div>
</template>

<script>
import { columnData, columnDataInfo } from './config/index.js'
export default {
  components: {},
  data() {
    return {
      pageConfig: [
        //配置tab
        //   头视图
        {
          //   useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          title: this.$t('头视图'),
          toolbar: {
            //工具栏和右侧筛选
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'acceptanceBtn',
                  title: this.$t('供方接收'),
                  icon: 'icon_solid_export'
                },
                {
                  id: 'returnBtn',
                  title: this.$t('供方退回'),
                  icon: 'icon_solid_export'
                }
              ], //新增  删除
              ['Filter', 'Refresh', 'Setting'] //筛选   刷新  设置
            ]
          },
          grid: {
            columnData: columnData,
            dataSource: [
              {
                id: '10001',
                businessTypeName: 'w20211227001', //VMI领料单号
                status: 1, //状态
                plantCode: '630847', //工厂编号
                plantDescription: this.$t('TCL空调器有限公司生产工厂1'), //工厂描述
                supplierCode: 'G89001', //供应商编码
                supplierDescription: this.$t('广东克瑞达智能科技股份有限公司'), //供应商描述
                VMIWarehouseCode: '0100001', //VMI仓编码
                VMIWarehouseDescription: this.$t('VMI红兴物流芜湖威灵电机'), //VMI仓描述
                pickingSupplierCode: '2200101', //领料供应商编码
                pickingSupplierDescription: this.$t('广东新科科技股份有限公司'), //领料供应商描述
                shippingAddress: this.$t('深圳市福田区下梅林梅华路'), //送货地址
                preparationDate: '2021-12-28', //制单日期
                preparer: this.$t('许凯凯1'), //制单人
                createTime: '1646216299053'
              },
              {
                id: '10002',
                businessTypeName: 'w20211227001', //VMI领料单号
                status: 2, //状态
                plantCode: '630847', //工厂编号
                plantDescription: this.$t('TCL空调器有限公司生产工厂2'), //工厂描述
                supplierCode: 'G89001', //原材料供应商编码
                supplierDescription: this.$t('广东克瑞达智能科技股份有限公司'), //供应商描述
                VMIWarehouseCode: '0100002', //VMI仓编码
                VMIWarehouseDescription: this.$t('VMI红兴物流芜湖威灵电机'), //VMI仓描述
                pickingSupplierCode: '2200101', //领料供应商编码
                pickingSupplierDescription: this.$t('广东新科科技股份有限公司'), //领料供应商描述
                shippingAddress: this.$t('深圳市福田区下梅林梅华路'), //送货地址
                preparationDate: '2021-12-28', //制单日期
                preparer: this.$t('许凯凯2'), //制单人
                createTime: '1646216299253'
              },
              {
                id: '10003',
                businessTypeName: 'w20211227001', //VMI领料单号
                status: 3, //状态
                plantCode: '630847', //工厂编号
                plantDescription: this.$t('TCL空调器有限公司生产工厂3'), //工厂描述
                supplierCode: 'G89001', //原材料供应商编码
                supplierDescription: this.$t('广东克瑞达智能科技股份有限公司'), //供应商描述
                VMIWarehouseCode: '0100003', //VMI仓编码
                VMIWarehouseDescription: this.$t('VMI红兴物流芜湖威灵电机'), //VMI仓描述
                pickingSupplierCode: '2200101', //领料供应商编码
                pickingSupplierDescription: this.$t('广东新科科技股份有限公司'), //领料供应商描述
                shippingAddress: this.$t('深圳市福田区下梅林梅华路'), //送货地址
                preparationDate: '1646216299023', //制单日期
                preparer: this.$t('许凯凯3'), //制单人
                createTime: '1646216299023'
              },
              {
                id: '10004',
                businessTypeName: 'w20211227001', //VMI领料单号
                status: 4, //状态
                plantCode: '630847', //工厂编号
                plantDescription: this.$t('TCL空调器有限公司生产工厂4'), //工厂描述
                supplierCode: 'G89001', //原材料供应商编码
                supplierDescription: this.$t('广东克瑞达智能科技股份有限公司'), //供应商描述
                VMIWarehouseCode: '0100004', //VMI仓编码
                VMIWarehouseDescription: this.$t('VMI红兴物流芜湖威灵电机'), //VMI仓描述
                pickingSupplierCode: '2200101', //领料供应商编码
                pickingSupplierDescription: this.$t('广东新科科技股份有限公司'), //领料供应商描述
                shippingAddress: this.$t('深圳市福田区下梅林梅华路'), //送货地址
                preparationDate: '2021-12-28', //制单日期
                preparer: this.$t('许凯凯4'), //制单人
                createTime: '1646216290053'
              }
            ],
            // asyncConfig: {
            //   url: "/srm-purchase-execute/tenant/process/configs",
            // },
            frozenColumns: 1
          }
        },
        // 明细视图
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          title: this.$t('明细视图'),
          toolbar: {
            //工具栏和右侧筛选
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [], //新增  删除
              ['Filter', 'Refresh', 'Setting'] //筛选   刷新  设置
            ]
          },
          grid: {
            columnData: columnDataInfo,
            dataSource: [
              {
                id: '10001',
                businessTypeName: 'w20211227001', //VMI退货单号
                status: 5, //状态
                lineNumber: '10', //行号
                materialCode: '630847', //物料编码
                materialDescription: '自攻螺钉 GB/T', //物料描述
                inventoryStatus: this.$t('合格库存'), //库存状态
                batch: 'SN0002', //批次/卷号
                returnQuantity: '500', //退货数量
                receivedQuantity: '500', //实收数量
                Company: this.$t('件'), //单位
                procurementGroup: '王国浩-塑料件', //采购组
                lineRemarks: this.$t('备注文本字段'), //行备注
                associatedPurchaseOrderNumber: 'D838940', //关联采购订单号
                associatedPurchaseOrderLineNumber: '10', //关联采购订单行号
                factoryNumber: '630847', //工厂编号
                plantDescription: this.$t('TCL空调器有限公司生产工厂'), //工厂描述
                supplierCode: '0100001', //供应商编码
                supplierDescription: this.$t('VMI红兴物流芜湖威灵电机'), //供应商描述
                VMIWarehouseCode: 'G89001', //VMI仓编码
                VMIWarehouseDescription: this.$t('广东惠利普智能科技股份有限公司'), //VMI仓描述
                shippingAddress: this.$t('深圳市福田区下梅林梅华路'), //送货地址
                preparationDate: '2021-12-27', //制单日期
                preparer: this.$t('许凯凯') //制单人
              }
            ],
            // asyncConfig: {
            //   url: "/srm-purchase-execute/tenant/process/configs",
            // },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },
  methods: {
    //单元格点击事件
    handleClickCellTitle(args) {
      //   console.log(e.field, "44444");
      const { field, data } = args
      if (field == 'businessTypeName') {
        // 送货单号 click
        this.goToDetail({
          headerInfo: data
        })
      }
    },
    // 跳转到 送货单-详情-供方
    goToDetail(data) {
      const tabIndex = this.$refs.templateRef.getCurrentTabRef().tabIndex
      // 将 lastTabIndex 放到 localStorage
      localStorage.setItem('lastTabIndex', JSON.stringify(tabIndex))
      const { headerInfo } = data
      const deliverListData = {
        headerInfo // 头部信息
      }
      // 将信息放到 localStorage 详情页 读
      localStorage.setItem('deliverListData', JSON.stringify(deliverListData))
      // 跳转 送货单-详情-供方
      this.$router.push({
        name: 'supplier-return-details',
        query: {}
      })
    },
    //   表格 toolbar 点击
    handleClickToolBar(e) {
      console.log(e, 'eee')
    },
    //单元格操作按钮点击
    handleClickCellTool(e) {
      console.log('方法2', e)
    }
  }
}
</script>

<style style="scss" scoped>
.full-height {
  height: 100%;
}
</style>
