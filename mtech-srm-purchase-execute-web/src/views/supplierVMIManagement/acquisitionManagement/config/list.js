import UTILS from '../../../../utils/utils'
import { MasterDataSelect } from '@/utils/constant'
export const columnData = [
  {
    width: '50', //第一行checkbox
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    field: 'orderCode',
    headerText: '采购订单号1',
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        console.log(e)
        return e + '---11'
      }
    },
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: '编辑',
        visibleCondition: (data) => {
          console.log(data, 'data')
          let isShow = false
          if (Math.random() > 0.5) {
            isShow = true
          }
          return isShow
        }
      }
    ]
  },
  {
    field: 'showStatus',
    headerText: '状态',
    width: '250',
    valueConverter: {
      type: 'map',
      map: [
        {
          value: '待审批',
          text: '待审批',
          cssClass: 'col-active'
        },
        {
          value: '待发布',
          text: '待发布',
          cssClass: 'col-published'
        },
        {
          value: '完成',
          text: '完成',
          cssClass: 'col-normal'
        }
      ]
    }
  },
  {
    field: 'createTime',
    headerText: '加急日期',
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e != '0') {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'type',
    headerText: '订单类型',
    width: '150',
    valueConverter: {
      type: 'map',
      map: { 0: '正常订单', 1: '退货订单', 2: '免费订单' }
    }
  },
  {
    field: 'version',
    headerText: '版本',
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return 'V' + e
      }
    }
  }
]
