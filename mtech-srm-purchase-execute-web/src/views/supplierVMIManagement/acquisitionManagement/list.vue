<template>
  <div :style="{ padding: '50px' }">
    <div @click="getData">{{ $t('获取数据') }}</div>
    <br />
    <mt-button
      icon-css="mt-icons mt-icon-icon_solid_Createorder"
      type="text"
      @click="handleClick"
      >{{ $t('自定义事件开启编辑') }}</mt-button
    >
    <mt-DataGrid
      id="gridcomp"
      ref="dataGrid"
      :data-source="dataSource1"
      :column-data="columnData"
      :edit-settings="editing"
      :toolbar="toolbar"
      @addRecord="addRecord"
      @actionBegin="handlerToolbarClick1"
      @actionComplete="handlerToolbarClick11"
    ></mt-DataGrid>
    <h2 :style="{ fontSize: '24px', color: 'red' }">
      {{ dataSource1[0] }}
    </h2>
  </div>
</template>

<script>
import Vue from 'vue'
import { DropDownList } from '@syncfusion/ej2-dropdowns'
import { TextBox } from '@syncfusion/ej2-inputs'
let countryElem, stateElem, countryObj, stateObj, inputValueElem, inputValueObj
let state = [
  { stateName: this.$t('东城'), countryId: '1', stateId: '101' },
  { stateName: this.$t('西城'), countryId: '1', stateId: '102' },
  { stateName: this.$t('郑州'), countryId: '2', stateId: '103' },
  { stateName: this.$t('南阳'), countryId: '2', stateId: '104' }
]
export default {
  name: 'DataGrid',
  data() {
    return {
      toolbar: [
        'Add',
        'Edit',
        'Delete',
        'Update',
        'Cancel',
        {
          id: 'test',
          icon: 'icon_solid_Activateorder',
          title: this.$t('测试')
        }
      ],
      dataSource1: [
        {
          orderCode: 11,
          showStatus: this.$t('待审批'),
          time: '2020/3/18',
          buer: 'buer1',
          inputValue: 'true',
          id: '1-1',
          number: 1,
          result: 'true',
          test: 'test',
          city: this.$t('北京'),
          country: this.$t('东城')
        },
        {
          orderCode: 22,
          showStatus: this.$t('待发布'),
          time: '2022/3/8',
          buer: 'buer2',
          inputValue: 'false',
          id: '2-2',
          number: 2,
          result: 'false',
          test: 'test',
          city: this.$t('河南'),
          country: this.$t('内乡')
        }
      ],
      columnData: [
        {
          width: '50',
          type: 'checkbox',
          showInColumnChooser: false //隐藏在列选择器中的过滤
        },
        {
          field: 'city',
          headerText: this.$t('省份'),
          edit: {
            create: () => {
              //每次都要创建一个input元素
              countryElem = document.createElement('input')
              return countryElem
            },
            read: () => {
              return countryObj.text
            },
            destroy: () => {
              countryObj.destroy()
            },
            write: () => {
              countryObj = new DropDownList({
                dataSource: [
                  { countryName: this.$t('北京'), countryId: '1' },
                  { countryName: this.$t('河南'), countryId: '2' }
                ],
                fields: { value: 'countryId', text: 'countryName' },
                placeholder: this.$t('选择城市'),
                change: (e) => {
                  console.log(e, '-=-=-=')
                  stateObj.enabled = true //城镇的编写打开
                  stateObj.dataSource = state.filter((item) => item.countryId === countryObj.value)
                  stateObj.text = null
                  // stateObj.dataBind();
                }
                // floatLabelType: "Never",
              })
              countryObj.appendTo(countryElem)
            }
          }
        },
        {
          field: 'country',
          headerText: this.$t('地区'),
          edit: {
            create: () => {
              stateElem = document.createElement('input')
              return stateElem
            },
            read: () => {
              return stateObj.text
            },
            destroy: () => {
              stateObj.destroy()
            },
            write: () => {
              stateObj = new DropDownList({
                dataSource: state,
                fields: { value: 'stateId', text: 'stateName' },
                placeholder: this.$t('选择地区'),
                enabled: false,
                change: (e) => {
                  console.log(e, '-=-=-=')
                }
              })
              stateObj.appendTo(stateElem)
            }
          }
        },
        {
          field: 'inputValue',
          headerText: 'inputValue',
          edit: {
            create: () => {
              inputValueElem = document.createElement('input')
              return inputValueElem
            },
            read: () => {
              return inputValueObj.value
            },
            destroy: () => {
              inputValueObj.destroy()
            },
            write: () => {
              inputValueObj = new TextBox({
                placeholder: this.$t('请输入'),
                input: (e) => {
                  console.log(e)
                  inputValueObj.value = e.value
                }
              })
              console.log(inputValueObj, '0-0-0-')
              inputValueObj.appendTo(inputValueElem)
            }
          }
        },
        {
          field: 'buer',
          headerText: this.$t('自定义组件') //buerElem, buerObj

          // edit: {
          //   create: () => {
          //     buerElem = document.createElement("input");
          //     return buerElem;
          //   },
          //   read: () => {
          //     return buerObj.data.vaue;
          //   },
          //   destroy: () => {
          //     buerObj.destroy();
          //   },
          //   write: () => {
          //     buerObj = new DropDownList({
          //       dataSource: state,
          //       fields: { value: "stateId", text: "stateName" },
          //       placeholder: "选择地区",
          //       enabled: false,
          //       change: (e) => {
          //         console.log(e, "-=-=-=");
          //       },
          //     });
          //     buerObj.appendTo(buerElem);
          //   },
          // },
        },
        {
          field: 'result',
          headerText: 'result',
          editTemplate: function () {
            return {
              template: Vue.component('addressTemplate', {
                template: `<mt-radio id="result" v-model="data.result" :dataSource="[{ label: $t('是'), value: 'true'},{
          label: $t('否'), value: 'false'}]"  @input="onchang"></mt-radio>`,
                data() {
                  return { data: {} }
                },
                methods: {
                  onchang(e) {
                    // console.log(e);
                    // console.log(inputValueObj, "inputValueObj");
                    inputValueObj.value = e //改变别的值
                    this.$bus.$emit('CustomerID', e)
                  }
                }
              })
            }
          }
        },
        // {
        //   allowEditing: true, //是否允许编辑
        //   allowFiltering: true, //是否允许过滤
        //   allowReordering: true, //是否重新排序
        //   allowResizing: true, //是否允许调整大小 列拖拽
        //   clipMode: "Ellipsis", //剪辑模式 Clip 内容溢出其区域时截断 Ellipsis显示省略号 EllipsisWithTooltip时显示省略号，当悬停在省略号应用的单元格上时，它也会显示工具提示
        // },

        {
          field: 'showStatus',
          headerText: this.$t('状态'),
          width: '250',
          editType: 'dropdownedit',
          valueConverter: {
            type: 'map',
            map: [
              {
                value: '待审批',
                text: this.$t('待审批'),
                cssClass: 'col-active'
              },
              {
                value: '待发布',
                text: this.$t('待发布'),
                cssClass: 'col-published'
              },
              {
                value: '完成',
                text: this.$t('完成'),
                cssClass: 'col-normal'
              }
            ]
          },
          edit: {
            params: {
              allowFiltering: true,
              change: (e) => {
                console.log('我是下拉的change事件', e)
              }
            }
          }
        }
      ],
      editing: {
        allowDeleting: true,
        allowEditing: true,
        allowAdding: true
      },
      filterOptions: {
        type: 'Menu'
      }
    }
  },
  methods: {
    handleClick() {
      this.$refs.dataGrid.ejsRef.selectRow(1)
      this.$refs.dataGrid.ejsRef.startEdit()
      // console.log(this.$refs.dataGrid.ejsRef.updateCell, "事件");
      // this.$refs.dataGrid.ejsRef.addRecord({
      //   buer:'2-2-2-'
      // });
      // this.$refs.dataGrid.ejsRef.endEdit();
      // console.log(this.$refs.dataGrid.ejsRef.getBatchChanges());
      // console.log(this.$refs.dataGrid.ejsRef.getCurrentViewRecords());//获取编辑后的数据
    },
    addRecord() {
      console.log('addRecord', arguments)
    },
    getData() {
      console.log(this.dataSource1, '-=-=-')
    },
    handlerToolbarClick() {
      // console.log("toolbarClick", arguments);
    },

    handlerToolbarClick2() {
      console.log('toolbarClick22', arguments)
    },
    handlerToolbarClick3() {
      console.log('toolbarClick33', arguments)
    },
    handlerToolbarClick4() {
      console.log('toolbarClick44', arguments)
    },
    handlerToolbarClick5() {
      console.log('toolbarClick55', arguments)
    },
    handlerToolbarClick1(args) {
      console.log(args, '-=-=-=')
      if (args.requestType == 'beginEdit') {
        this.$bus.$on('CustomerID', this.getTemplateValue)
      }
      if (args.requestType == 'save') {
        args.data.result = this.customerID // Changed the CustomerID value by using the global variable
      }

      // if (args.requestType === "beginEdit") {
      //   if (args.rowIndex == 0) {
      //     args.cancel = true;//取消首行可编辑
      //   }
      // }
    },
    getTemplateValue: function (e) {
      console.log(e, '被监听的e')
      this.customerID = e // collected the dropdownlist selected value and stored it in global Vue variable
    },
    handlerToolbarClick11() {
      // console.log(args, "-=args.requestType");
    },
    handlerToolbarClick6() {
      // let { action = "" } = arg;
      // console.log(action, "-=-==");
      // if (action === "edit") {
      //   this.dataSource1 = JSON.parse(JSON.stringify(this.dataSource1));
      // }
    },
    changeOnoffItem(a, b, c) {
      console.log(a, b, c, 'item,,,,,,,')
      //值已经改了 但是视图
      this.dataSource1[b][c] = a
      console.log(this.dataSource1[b], 9999999)
    }
  }
}

/* 
export default {
  props: {},
  data() {
    return {
      tabSource: [
        {
          title: this.$t("采购申请配置"),
        },
        {
          title: this.$t("采购订单配置"),
        },
        {
          title: this.$t("供应商接收订单配置"),
        },
        {
          title: this.$t("售后订单配置"),
        },
        {
          title: this.$t("供应商接收售后订单配置"),
        },
      ],
      tabConfig: {
        eTab: false, //是否使用自适应
        titleCanChange: true, //自适应中的按钮
        showCloseButton: true,
      },
      componentConfig: [
        {
          title: this.$t("Tab名称一"), //title的设置是向下兼容，仅用于只有Tab标题显示的场景
          toolbar: [
            {
              id: "test",
              icon: "icon_solid_Activateorder",
              title: this.$t("测试"),
              visibleCondition: () => {
                return false;
              },
            },
          ],
          grid: {
            height: "auto",
            columnData: CONFIG.columnData,
            // ignoreFields:["orderCode"],
            asyncConfig: {
              url: "/srm-purchase-execute/tenant/purOrder/query",
              afterAsyncData: this.updateTabTitle,
            },
            allowResizing: false,
            // dataSource: [
            //   {
            //     orderCode: 888,
            //   },
            // ],
          },
        },
        {
          title: this.$t("Tab名称二"),
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: [
            [
              {
                id: "Export",
                icon: "icon_solid_edit",
                title: this.$t("导出"),
              },
            ],
            ["Filter", "Export"],
          ],
          grid: {
            // height: "auto",
            columnData: CONFIG.columnData,
            dataSource: [
              {
                orderCode: 888,
              },
            ],
          },
        },
      ],
    };
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    checkDetailStatus() {
      return true;
    },
    updateTabTitle(res) {
      console.log(res, 999);
      this.$set(
        this.componentConfig[0],
        "title",
        `需求明细(${res.data.total})`
      );
    },
  },
  components: {},
};
 */
</script>

<style scoped lang="scss"></style>
