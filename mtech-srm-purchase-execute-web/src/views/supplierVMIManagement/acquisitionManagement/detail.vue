<template>
  <div :style="{ padding: '50px' }">
    <!-- <hr />
    <mt-tabs :e-tab="false" :data-source="tabSource"></mt-tabs>
    <div>
      <mt-template-page
        :template-config="componentConfig"
        :tab-config="tabConfig"
      >
      </mt-template-page>
    </div> -->
    <div @click="getData">{{ $t('获取数据') }}</div>
    <br />
    <mt-button
      icon-css="mt-icons mt-icon-icon_solid_Createorder"
      type="text"
      :disabled="isEditStatus"
      @click="handleClick"
      >{{ $t('自定义事件开启编辑') }}</mt-button
    >
    <mt-DataGrid
      id="gridcomp"
      ref="dataGrid"
      :data-source="dataSource1"
      :column-data="columnData"
      :edit-settings="editing"
      :toolbar="toolbar"
      @addRecord="addRecord"
      @actionBegin="handlerToolbarClick1Old"
      @actionComplete="handlerToolbarClick11"
    ></mt-DataGrid>
    <!-- <mt-DataGrid
      id="gridcomp"
      :data-source="dataSource1"
      :column-data="columnData"
      :edit-settings="editing"
      :toolbar="toolbar"
      @toolbarClick="handlerToolbarClick"
      @actionBegin="handlerToolbarClick1"
      @actionComplete="handlerToolbarClick6"
      @beforeChange="handlerToolbarClick2"
      @ruleChange="handlerToolbarClick3"
      @resuzing="handlerToolbarClick4"
      @dataBound="handlerToolbarClick5"
    ></mt-DataGrid> -->
    <h2 :style="{ fontSize: '24px', color: 'red' }">
      {{ dataSource1[0] }}
    </h2>
  </div>
</template>

<script>
// import * as CONFIG from "./config/list";
// import cellChanged from "./cellChanged.vue";
import Vue from 'vue'
export default {
  name: 'DataGrid',
  data() {
    const _self = this
    return {
      toolbar: [
        'Add',
        'Edit',
        'Delete',
        'Update',
        'Cancel',
        {
          id: 'test',
          icon: 'icon_solid_Activateorder',
          title: this.$t('测试')
        }
      ],
      dataSource1: [
        {
          orderCode: 11,
          showStatus: this.$t('待审批'),
          time: '2020/3/18',
          buer: 'buer1',
          inputValue: true,
          id: '1-1',
          number: 1,
          result: 'true',
          test: 'test'
        },
        {
          orderCode: 22,
          showStatus: this.$t('待发布'),
          time: '2022/3/8',
          buer: 'buer2',
          inputValue: false,
          id: '2-2',
          number: 2,
          result: 'false',
          test: 'test'
        }
      ],
      columnData: [
        {
          width: '50',
          type: 'checkbox',
          showInColumnChooser: false //隐藏在列选择器中的过滤
        },
        {
          field: 'time',
          headerText: this.$t('时间'),
          valueConverter: { type: 'date', format: 'YYYY-MM-DD' }
        },
        {
          field: 'test',
          headerText: 'test',
          editTemplate: function () {
            return {
              template: Vue.component('addressTemplate', {
                template: `<mt-input id="test" v-model="data.test"/>`,
                data() {
                  return {}
                }
              })
            }
          }
        },
        {
          field: 'result',
          headerText: 'result',
          editTemplate: function () {
            return {
              template: Vue.component('addressTemplate', {
                template: `<mt-radio id="result" v-model="data.result" :dataSource="[{ label: $t('是'), value: 'true'},{
          label: $t('否'), value: 'false'}]"  @input="onchang"></mt-radio>`,
                data() {
                  return { data: {} }
                },
                methods: {
                  onchang(e) {
                    console.log(e)
                  }
                }
              })
            }
          }
        },
        // {
        //   allowEditing: true, //是否允许编辑
        //   allowFiltering: true, //是否允许过滤
        //   allowReordering: true, //是否重新排序
        //   allowResizing: true, //是否允许调整大小 列拖拽
        //   clipMode: "Ellipsis", //剪辑模式 Clip 内容溢出其区域时截断 Ellipsis显示省略号 EllipsisWithTooltip时显示省略号，当悬停在省略号应用的单元格上时，它也会显示工具提示
        // },
        {
          field: 'number',
          headerText: this.$t('数字'),
          editType: 'numericedit',
          format: 'n2', // n2-默认；p2-百分比；c2-金额$
          edit: {
            params: {
              min: 0,
              format: 'C2'
            }
          }
        },
        {
          field: 'id',
          headerText: 'ID'
          // formatter: (column, data) => {
          //   if (data[column.field]) {
          //     return `${data[column.field]}^^^`;
          //   }
          // },
        },
        {
          field: 'time',
          headerText: this.$t('时间'),
          editType: 'datepickeredit',
          format: 'yMd'
        },

        {
          field: 'inputValue',
          headerText: 'inputValue',
          editType: 'booleanedit',
          editTemplate: function () {
            return {
              template: Vue.component('addressTemplate', {
                template: `<mt-switch
                      @change="change"
                      :on-label="$t('显示')"
                      :off-label="$t('关闭')"
                       :value="data[data.column.field]"
                    ></mt-switch>`,
                data() {
                  return {
                    data: { field: '', data: {}, index: null }
                  }
                },
                mounted() {
                  this.field = this.data.column.field
                  this.index = this.data.index
                  // console.log(this.data, this.field, "-=-==");
                },
                methods: {
                  change(onOff) {
                    console.log(onOff)
                    this.data['inputValue'] = onOff
                    _self.changeOnoffItem(onOff, this.index, this.field)
                  }
                }
              })
            }
          }
        },
        // {
        //   field: "result",
        //   width: "150",
        //   headerText: "质检结果",
        //   valueAccessor: function (field, data) {
        //     return data[field] ? "合格" : "不合格";
        //   },
        //   editTemplate: function () {
        //     return {
        //       template: Vue.component("result", {
        //         template: `<div>
        //         <input type="radio" default name="resultRadio" @click="changeTrue" :checked="data[data.column.field]?'checked':'' " />合格
        //         <input type="radio" name="resultRadio" @click="changeFalse" :checked="data[data.column.field]?'':'checked' " />不合格
        //         </div> `,
        //         data() {
        //           return {
        //             data: {
        //               field: "",
        //               data: {},
        //               index: null,
        //             },
        //           };
        //         },
        //         mounted() {
        //           this.field = this.data.column.field;
        //           this.index = this.data.index;
        //         },
        //         methods: {
        //           changeFalse() {
        //             this.data["result"] = false;
        //             _self.changeOnoffItem(false, this.index, this.field);
        //           },
        //           changeTrue() {
        //             this.data["result"] = true;
        //             _self.changeOnoffItem(true, this.index, this.field);
        //           },
        //         },
        //       }),
        //     };
        //   },
        // },
        {
          field: 'buer',
          headerText: this.$t('自定义'),
          editTemplate: function () {
            return {
              template: Vue.component('addressTemplate', {
                template: `<mt-input
                      @input="change"
                       :value="data[data.column.field]"
                    ></mt-input>`,
                data() {
                  return {
                    data: { field: '', data: {}, index: null }
                  }
                },
                mounted() {
                  this.field = this.data.column.field
                  this.index = this.data.index
                  // console.log(this.data, this.field, "-=-==");
                },
                methods: {
                  change(e) {
                    console.log(e, '修改后的值')
                    this.data['buer'] = e
                    _self.dataSource1[this.index][this.field] = e
                    console.log(_self.dataSource1[this.index], '-=-=-=-=-')
                    // _self.changeOnoffItem(e, this.index, this.field);
                  }
                }
              })
            }
          }
        },
        {
          field: 'showStatus',
          headerText: this.$t('状态'),
          width: '250',
          editType: 'dropdownedit',
          valueConverter: {
            type: 'map',
            map: [
              {
                value: '待审批',
                text: this.$t('待审批'),
                cssClass: 'col-active'
              },
              {
                value: '待发布',
                text: this.$t('待发布'),
                cssClass: 'col-published'
              },
              {
                value: '完成',
                text: this.$t('完成'),
                cssClass: 'col-normal'
              }
            ]
          },
          edit: {
            params: {
              allowFiltering: true,
              change: (e) => {
                console.log('我是下拉的change事件', e)
              }
            }
          }
        }
      ],
      editing: {
        allowDeleting: true,
        allowEditing: true,
        allowAdding: true
      },

      filterOptions: {
        type: 'Menu'
      }
    }
  },
  methods: {
    handleClick() {
      this.$refs.dataGrid.ejsRef.startEdit()
      // this.$refs.dataGrid.ejsRef.endEdit();
      //  console.log(this.$refs.dataGrid.ejsRef.dataSource,6666);

      // console.log(this.$refs.dataGrid.ejsRef.updateCell, "事件");
      // this.$refs.dataGrid.ejsRef.addRecord({
      //   buer:'2-2-2-'
      // });

      // console.log(this.$refs.dataGrid.ejsRef.getBatchChanges());
      // console.log(this.$refs.dataGrid.ejsRef.getCurrentViewRecords());//获取编辑后的数据
      // console.log(this.$refs.dataGrid.ejsRef.updateCell(1, "buer", "666"));
    },
    addRecord() {
      console.log('addRecord', arguments)
    },
    getData() {
      console.log(this.dataSource1, '-=-=-')
    },
    handlerToolbarClick() {
      // console.log("toolbarClick", arguments);
    },

    handlerToolbarClick2() {
      console.log('toolbarClick22', arguments)
    },
    handlerToolbarClick3() {
      console.log('toolbarClick33', arguments)
    },
    handlerToolbarClick4() {
      console.log('toolbarClick44', arguments)
    },
    handlerToolbarClick5() {
      console.log('toolbarClick55', arguments)
    },
    /* 
    打开编辑的时候  data和previousData是没有东西的 只有rowData
    关闭编辑的时候  视图展示的是data里的值

    打开编辑的时候  data和previousData是没有东西的被清空了  但是
   rowData的值是会渲染内容的    
   继续关闭编辑   视图展示data并且把data的值赋给rowData  
   
    */
    handlerToolbarClick1() {
      // if (args.requestType === "beginEdit") {
      //   if (args.rowIndex == 0) {
      //     args.cancel = true;
      //   }
      // }
    },
    handlerToolbarClick11() {
      // console.log(args, "-=args.requestType");
      // if (args.requestType === "beginEdit") {
      //   if (args.rowIndex == 1) {
      //     args.cancel = true;
      //   }
      // }
    },
    handlerToolbarClick1Old(arg) {
      //渲染机制是用
      console.log(this.dataSource1, 'this.dataSource1')
      let { data = {}, previousData = {}, rowData = {}, rowIndex } = arg

      // if (arg.requestType == "save") {
      //   arg.data.inputValue = this.dataSource1[rowIndex].inputValue;
      // }
      // console.log("actionBegin", arguments);
      //编辑完点击保存的时候----开始的时候吧rowData赋值成正确的
      arg.data = { ...this.dataSource1[rowIndex] }
      // data.inputValue  = this.dataSource1[rowIndex].inputValue;
      console.log(
        '开始data--previousData--rowData',
        data.inputValue,
        previousData.buer,
        rowData.buer
      )
      // data.id = "data" + Math.random();
      // previousData.id = "previousData";
      //rowData.id = "rowData" + index;
    },
    handlerToolbarClick6(arg) {
      let { action = '' } = arg
      console.log(action, '-=-==')
      if (action === 'edit') {
        this.dataSource1 = JSON.parse(JSON.stringify(this.dataSource1))
      }
    },
    changeOnoffItem(a, b, c) {
      console.log(a, b, c, 'item,,,,,,,')
      //值已经改了 但是视图
      this.dataSource1[b][c] = a
      console.log(this.dataSource1[b], 9999999)
      // this.$set(this.dataSource1[b], "number", 444);

      // this.dataSource1 = JSON.parse(JSON.stringify(this.dataSource1));
      // this.$set(this.dataSource1[b], c, a);
      // let bb = JSON.parse(JSON.stringify(this.dataSource1[b]));
      // this.$set(this.dataSource1, b, bb);
      // console.log(this.dataSource1[b], 9090);
    }
  }
}

/* 
export default {
  props: {},
  data() {
    return {
      tabSource: [
        {
          title: this.$t("采购申请配置"),
        },
        {
          title: this.$t("采购订单配置"),
        },
        {
          title: this.$t("供应商接收订单配置"),
        },
        {
          title: this.$t("售后订单配置"),
        },
        {
          title: this.$t("供应商接收售后订单配置"),
        },
      ],
      tabConfig: {
        eTab: false, //是否使用自适应
        titleCanChange: true, //自适应中的按钮
        showCloseButton: true,
      },
      componentConfig: [
        {
          title: this.$t("Tab名称一"), //title的设置是向下兼容，仅用于只有Tab标题显示的场景
          toolbar: [
            {
              id: "test",
              icon: "icon_solid_Activateorder",
              title: this.$t("测试"),
              visibleCondition: () => {
                return false;
              },
            },
          ],
          grid: {
            height: "auto",
            columnData: CONFIG.columnData,
            // ignoreFields:["orderCode"],
            asyncConfig: {
              url: "/srm-purchase-execute/tenant/purOrder/query",
              afterAsyncData: this.updateTabTitle,
            },
            allowResizing: false,
            // dataSource: [
            //   {
            //     orderCode: 888,
            //   },
            // ],
          },
        },
        {
          title: this.$t("Tab名称二"),
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: [
            [
              {
                id: "Export",
                icon: "icon_solid_edit",
                title: this.$t("导出"),
              },
            ],
            ["Filter", "Export"],
          ],
          grid: {
            // height: "auto",
            columnData: CONFIG.columnData,
            dataSource: [
              {
                orderCode: 888,
              },
            ],
          },
        },
      ],
    };
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    checkDetailStatus() {
      return true;
    },
    updateTabTitle(res) {
      console.log(res, 999);
      this.$set(
        this.componentConfig[0],
        "title",
        `需求明细(${res.data.total})`
      );
    },
  },
  components: {},
};
 */
</script>

<style scoped lang="scss"></style>
