<template>
  <div class="full-height pt20 vertical-flex-box">
    <div>demo1</div>
    <mt-button
      icon-css="mt-icons mt-icon-icon_solid_Createorder"
      type="text"
      @click="handleClick"
      >{{ $t('获取数据') }}</mt-button
    >
    <hr />
    <mt-template-page :template-config="componentConfig">
      <div slot="slot-0" :style="{ padding: '30px' }">
        <br />
        <mt-data-grid
          ref="dataGrid"
          :data-source="dataSource"
          :column-data="columnData"
          :edit-settings="editing"
          :toolbar="toolbar"
        ></mt-data-grid>
      </div>
    </mt-template-page>
  </div>
</template>

<script>
import selectedwuliaoCode from './components/wuliao.vue' // 物料、sku
import onlyShowInput from './components/onlyShowInput.vue' // 物料、sku
export default {
  props: {},
  data() {
    return {
      componentConfig: [
        {
          tab: { title: this.$t('物料信息') },
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false // 使用组件中的toolbar配置
        },
        {
          tab: { title: this.$t('物流信息') }
        }
      ],
      dataSource: [
        {
          orderCode: '1-1-1',
          itemName: '物料1 ',
          outStatus: this.$t('合格库存')
        },
        {
          orderCode: '2-2-2345',
          itemName: '物料2 ',
          outStatus: this.$t('限制库存')
        }
      ],
      columnData: [
        {
          width: '50',
          type: 'checkbox',
          showInColumnChooser: false //隐藏在列选择器中的过滤
        },
        {
          field: 'orderCode',
          headerText: this.$t('物料编码'),
          editTemplate: () => {
            return {
              template: selectedwuliaoCode
            }
          }
        },
        {
          field: 'itemName',
          headerText: this.$t('物料名称'),
          editTemplate: () => {
            return { template: onlyShowInput }
          }
        },
        {
          field: 'remark',
          headerText: this.$t('订单备注')
        }
      ],
      toolbar: ['Add', 'Edit', 'Cancel', 'Update'],
      editing: {
        allowEditing: true, //是否允许编辑
        allowDeleting: true, //是否允许删除
        allowAdding: true //是否允许新增
      }
    }
  },
  methods: {
    handleClick() {
      //获取编辑后的整体数据
      console.log(this.$refs.dataGrid.ejsRef.getCurrentViewRecords(), '-=-=-=')
    }
  }
}
</script>

<style scoped lang="scss"></style>
