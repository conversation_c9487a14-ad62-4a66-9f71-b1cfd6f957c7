<template>
  <div class="full-height pt20 vertical-flex-box">
    <div>
      demo3 {{ $t('下拉模糊查询联动') }} {{ $t('订单号带出') }}-{{ $t('订单行') }}-{{
        $t('订单行带出物料')
      }}
    </div>
    <br />
    <hr />
    <mt-button
      icon-css="mt-icons mt-icon-icon_solid_Createorder"
      type="text"
      @click="handleClick"
      >{{ $t('获取数据') }}</mt-button
    >
    <mt-template-page ref="templateRef" :template-config="componentConfig"> </mt-template-page>
  </div>
</template>

<script>
import onlyShowInput from './components/onlyShowInput2.vue' // 物料、sku
import selectedOrder from './components/selectedOrder.vue' // 物料、sku
import selectedOrderLine from './components/selectedOrderLine.vue' // 物料、sku
export default {
  props: {},
  data() {
    return {
      componentConfig: [
        {
          toolbar: [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              title: this.$t('新增')
            },
            { id: 'Delete', icon: 'icon_solid_Delete', title: this.$t('删除') },
            'Edit'
          ],
          grid: {
            dataSource: [
              {
                orderCode: '1-1-1',
                buyerOrder: 'PO2022011900345',
                itemName: '物料1 ',
                outStatus: this.$t('合格库存')
              },
              {
                orderCode: '2-2-2345',
                itemName: '物料2 ',
                outStatus: this.$t('限制库存'),
                buyerOrder: 'PO2022011900345'
              }
            ],
            allowEditing: true, //开启表格编辑操作
            allowPaging: false, // 不分页
            editSettings: {
              allowEditOnDblClick: false,
              allowEditing: true, //是否允许编辑
              allowDeleting: true, //是否允许删除
              allowAdding: true, //是否允许新增
              showDeleteConfirmDialog: false, //删除是否需要确认
              newRowPosition: 'Top' //在上面新增一行还是下面新增一行
            },
            columnData: [
              {
                width: '50',
                type: 'checkbox',
                showInColumnChooser: false //隐藏在列选择器中的过滤
              },
              {
                field: 'buyerOrder',
                headerText: this.$t('采购订单'),
                editTemplate: () => {
                  return {
                    template: selectedOrder
                  }
                }
              },
              {
                field: 'buyerOrderLineCode',
                headerText: this.$t('采购订单行'),
                editTemplate: () => {
                  return {
                    template: selectedOrderLine
                  }
                }
              },
              {
                field: 'orderCode',
                headerText: this.$t('物料编码'),
                editTemplate: () => {
                  return {
                    template: onlyShowInput
                  }
                }
              },
              {
                field: 'itemName',
                headerText: this.$t('物料名称'),
                editTemplate: () => {
                  return { template: onlyShowInput }
                }
              },
              {
                field: 'remark',
                headerText: this.$t('订单备注')
              }
            ]
          }
        }
      ]
    }
  },
  methods: {
    handleClick() {
      //获取编辑后的整体数据
      console.log(
        this.$refs.templateRef.getCurrentUsefulRef().gridRef?.ejsRef.getCurrentViewRecords(),
        '-=-=-='
      )
    }
  }
}
</script>

<style scoped lang="scss"></style>
