<template>
  <div class="pc-select">
    <div class="in-cell">
      <mt-input :id="fieldName" disabled v-model="value" :width="130" :placeholder="title" />
      <mt-icon style="width: 20px" name="icon_list_refuse" @click.native="handleClear"></mt-icon>
      <mt-icon style="width: 20px" name="icon_input_search" @click.native="showDialog"></mt-icon>
    </div>
    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="title"
      :buttons="buttons"
      @close="handleClose"
    >
      <mt-template-page
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="pageConfig"
        @recordDoubleClick="recordDoubleClick"
      ></mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
import { PROXY_MDM_TENANT } from '@/utils/constant'

export default {
  props: {},
  data() {
    return {
      dialogShow: false,
      title: this.$t('请选择'),
      value: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          toolbar: [],
          grid: {
            height: 352,
            allowPaging: true,
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: [
              {
                width: '150',
                field: 'itemCode',
                headerText: this.$t('物料编号')
              },
              { width: '150', field: 'itemName', headerText: this.$t('物料名称') },
              {
                width: '150',
                field: 'categoryResponse.categoryCode',
                headerText: this.$t('品类')
              },
              {
                width: '150',
                field: 'itemDescription',
                headerText: this.$t('规格型号')
              },
              { width: '150', field: 'oldItemCode', headerText: this.$t('旧物料编号') },
              { width: '150', field: 'manufacturerName', headerText: this.$t('制造商') }
            ],
            asyncConfig: {
              url: `${PROXY_MDM_TENANT}/item/paged-query?BU_CODE=${localStorage.getItem(
                'currentBu'
              )}`
            }
          }
        }
      ]
    }
  },
  mounted() {
    this.fieldName = this.data.column.field
    this.value = this.data[this.fieldName]
  },
  methods: {
    recordDoubleClick(args) {
      this.confirm(null, [args.rowData])
    },
    confirm(e, records) {
      if (!records || records.length <= 0) {
        records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      }
      if (records.length <= 0) return
      console.log(records, '-=-=')
      //   this.value = records[0]?.itemName;
      this.value = records[0]?.itemCode
      //联动改变物料描述
      this.$bus.$emit('itemNameChange', records[0]?.itemName)
      // 关闭弹窗
      this.handleClose()
    },
    handleClear() {
      this.value = null
      //联动改变物料描述
      this.$bus.$emit('itemNameChange', null)
    },
    handleClose() {
      this.dialogShow = false
      this.$refs.dialog.ejsRef.hide()
    },
    showDialog() {
      this.dialogShow = true
      this.$refs.dialog.ejsRef.show()
    }
  }
}
</script>

<style scoped lang="scss">
.in-cell {
  width: 100%;
  display: flex;
  > .mt-icons {
    margin-left: 5px;
    cursor: pointer;
  }
}
</style>
