<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="$t('请选择采购订单行')"
      @change="selectChange"
    >
    </mt-select>
  </div>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      dataSource: [],
      fields: { text: 'label', value: 'value' }
    }
  },
  mounted() {
    //监听数据
    this.$bus.$on('getOrderLineData', (data) => {
      if (data && data.length > 0) {
        this.dataSource = data
      }
    })
  },
  methods: {
    selectChange(value) {
      const data = value?.itemData
      if (data) {
        this.$bus.$emit('orderCodeChange', data.itemCode)
        this.$bus.$emit('itemNameChange', data.itemName)
      }
    }
  }
}
</script>
