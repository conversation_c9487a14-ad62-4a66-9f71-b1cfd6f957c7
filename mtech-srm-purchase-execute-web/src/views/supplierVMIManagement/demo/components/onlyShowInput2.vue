<template>
  <mt-input :id="data.column.field" v-model="data[data.column.field]" disabled />
</template>

<script>
export default {
  props: {},
  data() {
    return { data: {}, value: '' }
  },
  mounted() {
    const that = this
    this.fieldName = this.data.column.field
    this.$bus.$on(`${this.fieldName}Change`, (value) => {
      console.log(value, '监听到事件变化了')
      that.$set(that.data, this.fieldName, value)
    })
  },
  methods: {}
}
</script>

<style scoped lang="scss"></style>
