<template>
  <div class="full-height pt20 vertical-flex-box">
    <div>{{ $t('头部区域占位') }}</div>
    <mt-button
      icon-css="mt-icons mt-icon-icon_solid_Createorder"
      type="text"
      @click="handleClick"
      >{{ $t('获取数据') }}</mt-button
    >
    <hr />
    <mt-template-page :template-config="componentConfig">
      <div slot="slot-0" :style="{ padding: '30px' }">
        <br />
        <mt-data-grid
          ref="dataGrid"
          :data-source="dataSource"
          :column-data="columnData"
          :edit-settings="editing"
          :toolbar="toolbar"
          @actionBegin="actionBegin"
        ></mt-data-grid>
      </div>
    </mt-template-page>
  </div>
</template>

<script>
import Vue from 'vue'
import { DropDownList } from '@syncfusion/ej2-dropdowns'

let sortsObj, sortsElem

export default {
  props: {},
  data() {
    const _this = this
    return {
      //   sortsRules: { require: true, minLength: 3 },
      componentConfig: [
        {
          tab: { title: this.$t('物料信息') },
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false // 使用组件中的toolbar配置
        },
        {
          tab: { title: this.$t('物流信息') }
        }
      ],
      dataSource: [
        {
          orderCode: '1-1-12345',
          des: this.$t('物料1描述'),
          number: 1000,
          result: this.$t('合格'),
          sorts: this.$t('包装损坏'),
          boolean: true,
          remark: this.$t('我是备注'),
          time: '2020/1/1'
        },
        {
          orderCode: '2-2-2345',
          des: this.$t('物料2描述'),
          number: 999,
          result: this.$t('不合格'),
          sorts: this.$t('残次品'),
          boolean: false,
          remark: this.$t('我是备注'),
          time: '2020/1/2'
        },
        {
          orderCode: '3-2-2345',
          des: this.$t('这行是不允许编辑的'),
          number: 888,
          result: this.$t('不合格'),
          sorts: this.$t('残次品'),
          boolean: false,
          remark: this.$t('不允许编辑的行'),
          time: '2020/1/1'
        }
      ],
      columnData: [
        {
          width: '50',
          type: 'checkbox',
          showInColumnChooser: false //隐藏在列选择器中的过滤
        },
        {
          field: 'orderCode',
          headerText: this.$t('物料编码'),
          allowEditing: false //此列不允许编辑
        },
        {
          field: 'des',
          headerText: this.$t('物料描述')
        },
        {
          field: 'number',
          headerText: this.$t('发货数量'),
          editType: 'numericedit', //默认编辑类型之number
          format: 'n2', // n2-默认；p2-百分比；c2-金额$
          edit: {
            params: {
              min: 0
            }
          }
        },
        {
          field: 'result',
          headerText: this.$t('质检结果'),
          editTemplate: function () {
            return {
              template: Vue.component('addressTemplate', {
                template: `<mt-radio id="result" v-model="data.result" :dataSource="[{ label: $t('合格'), value: $t('合格')},{
          label: $t('不合格'), value: $t('不合格')}]"  @input="onchange"></mt-radio>`,
                data() {
                  return { data: {} }
                },
                methods: {
                  onchange(value) {
                    console.log(this.data, '选择的值')
                    //两种方式  1 通过$bus传值
                    // this.$bus.$emit("result", value);
                    //另一种直接改变dataSource的值 主意：这种方式只改变了值  并不会改变编辑结束后的视图   改变视图在事件里
                    const index = this.data.index
                    const field = this.data.column.field
                    _this.dataSource[index][field] = value
                    console.log(_this.dataSource[index], '-=-=')
                    //选择合格的时候  要把选择的不合格的分类清空 并且置灰
                    if (value === this.$t('合格')) {
                      sortsObj.enabled = false
                      sortsObj.text = null
                    } else {
                      sortsObj.enabled = true
                    }
                    console.log(_this.$refs.dataGrid.ejsInstances, '-=-=')
                    _this.$refs.dataGrid.ejsInstances.editSettings.newRowPosition = 'Bottom'
                    //  this.$refs.grid.ej2Instances.editSettings.newRowPosition = args.value;
                  }
                }
              })
            }
          }
        },
        {
          field: 'sorts',
          headerText: this.$t('不合格分类'), // sortsObj,sortsElem;
          //   validationRules: sortsRules,
          //   validationRules: {
          //     required: Math.random() > 0.5 ? true : false,
          //     minLength: 3,
          //   },
          edit: {
            create: () => {
              sortsElem = document.createElement('input')
              return sortsElem
            },
            read: () => {
              return sortsObj.text
            },
            destroy: () => {
              sortsObj.destroy()
            },
            write: (arg) => {
              //   console.log(arg, "-=-=");
              sortsObj = new DropDownList({
                dataSource: [
                  {
                    label: this.$t('包装损坏'),
                    value: 'sunhuai'
                  },
                  {
                    label: this.$t('残次品'),
                    value: 'caici'
                  }
                ],
                fields: { value: 'value', text: 'label' },
                placeholder: this.$t('选择不合格的原因'),
                enabled: arg.rowData.result === this.$t('合格') ? false : true,
                change: (e) => {
                  console.log(e)
                }
              })
              //   console.log(sortsObj, 1111);
              sortsObj.appendTo(sortsElem)
            }
          }
        },

        {
          field: 'remark',
          headerText: this.$t('订单备注')
        },
        {
          field: 'time',
          headerText: this.$t('时间'),
          editType: 'datepickeredit', //默认行内编辑类型之时间日期
          format: 'ymd'
        }
      ],
      toolbar: ['Add', 'Edit', 'Cancel', 'Update'], //编辑和取消、保存按钮
      editing: {
        allowEditing: true, //是否允许编辑
        allowDeleting: true, //是否允许删除
        allowAdding: true //是否允许新增
        //   allowFiltering: true, //是否允许过滤
        //   allowReordering: true, //是否重新排序
        //   allowResizing: true, //是否允许调整大小 列拖拽
        //   clipMode: "Ellipsis", //剪辑模式 Clip 内容溢出其区域时截断 Ellipsis显示省略号 EllipsisWithTooltip时显示省略号，当悬停在省略号应用的单元格上时，它也会显示工具提示
      }
    }
  },
  methods: {
    handleClick() {
      //获取编辑后的整体数据
      console.log(this.$refs.dataGrid.ejsRef.getCurrentViewRecords(), '-=-=-=')
    },
    actionBegin(args) {
      if (args.requestType === 'beginEdit') {
        if (args.rowIndex == 2) {
          args.cancel = true //禁止行编辑
        }
      }
      if (args.requestType == 'save') {
        console.log(args, 3333)
        //更新质检编辑完毕后的视图
        args.data.result = this.dataSource[args.rowIndex].result
        //如果必填项没有填写  提示并打开这一行的编辑

        if (args.data.result === this.$t('不合格') && !args.data.sorts) {
          this.$toast({
            content: '质检结果不合格，不合格原因必选,请完善哦~',
            type: 'warning'
          })
          setTimeout(() => {
            this.$refs.dataGrid.ejsRef.selectRow(0)
            this.$refs.dataGrid.ejsRef.startEdit()
          })
        }
      }
    }
  }
}
</script>

<style scoped lang="scss"></style>
