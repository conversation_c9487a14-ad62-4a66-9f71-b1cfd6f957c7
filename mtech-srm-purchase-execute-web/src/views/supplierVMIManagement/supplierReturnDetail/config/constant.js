import { i18n } from '@/main.js'

// tab
export const Tab = {
  list: 1, // 物料信息
  details: 2 // 物流信息
}

// 送货类型:1-关联采购订单,2-无采购订单
export const DeliveryType = {
  linked: 1, // 关联采购订单
  notLink: 2 // 无采购订单
}
// 送货类型 text
export const DeliveryTypeText = {
  [DeliveryType.linked]: i18n.t('关联采购订单'),
  [DeliveryType.notLink]: i18n.t('无采购订单')
}
// 送货类型 对应的 Options
export const DeliveryTypeOptions = [
  {
    // 关联采购订单
    value: DeliveryType.linked,
    text: DeliveryTypeText[DeliveryType.linked],
    cssClass: ''
  },
  {
    // 无采购订单
    value: DeliveryType.notLink,
    text: DeliveryTypeText[DeliveryType.notLink],
    cssClass: ''
  }
]

// 状态 状态:1-新建,2-发货中,3-已完成,4-已取消,5-已关闭
export const Status = {
  beConfirmed: 1, // 待确认
  cancelled: 2, // 已取消
  returned: 3, // 已退回
  received: 4, // 已接收
  new: 5 // 已关闭
}
// 状态 text
export const StatusText = {
  [Status.beConfirmed]: i18n.t('待确认'),
  [Status.cancelled]: i18n.t('已取消'),
  [Status.returned]: i18n.t('已退回'),
  [Status.received]: i18n.t('已接收'),
  [Status.new]: i18n.t('新建')
}
// 状态 对应的 css class
export const StatusCssClass = {
  [Status.beConfirmed]: 'col-active',
  [Status.cancelled]: 'col-active',
  [Status.returned]: 'col-active',
  [Status.received]: 'col-active',
  [Status.new]: 'col-inactive'
}
// 状态 对应的 Options
export const StatusOptions = [
  {
    // 新建
    value: Status.new,
    text: StatusText[Status.new],
    cssClass: StatusCssClass[Status.new]
  },
  {
    // 发货中
    value: Status.shipping,
    text: StatusText[Status.shipping],
    cssClass: StatusCssClass[Status.shipping]
  },
  {
    // 已完成
    value: Status.completed,
    text: StatusText[Status.completed],
    cssClass: StatusCssClass[Status.completed]
  },
  {
    // 已取消
    value: Status.cancelled,
    text: StatusText[Status.cancelled],
    cssClass: StatusCssClass[Status.cancelled]
  },
  {
    // 已关闭
    value: Status.closed,
    text: StatusText[Status.closed],
    cssClass: StatusCssClass[Status.closed]
  }
]

// 发货方式:1-快递配送，2-物流配送
export const ShippingType = {
  express: 1, // 快递配送
  logistics: 2 // 物流配送
}
// 发货方式 text
export const ShippingTypeText = {
  [ShippingType.express]: i18n.t('快递配送'),
  [ShippingType.logistics]: i18n.t('物流配送')
}
// 发货方式 对应的 Options
export const ShippingTypeOptions = [
  {
    // 快递配送
    value: ShippingType.express,
    text: ShippingTypeText[ShippingType.express],
    label: ShippingTypeText[ShippingType.express],
    cssClass: ''
  },
  {
    // 物流配送
    value: ShippingType.logistics,
    text: ShippingTypeText[ShippingType.logistics],
    label: ShippingTypeText[ShippingType.logistics],
    cssClass: ''
  }
]

// 物料信息 表格列数据
export const ColumnDataTab1 = [
  {
    fieldCode: 'status', // 行号
    fieldName: i18n.t('行号')
  },
  {
    fieldCode: 'transferPlanName', // 物料编码
    fieldName: i18n.t('物料编码')
  },
  {
    fieldCode: 'siteName', // 物料描述
    fieldName: i18n.t('物料描述')
  },
  {
    fieldCode: 'companyName', // 库存状态
    fieldName: i18n.t('库存状态')
  },
  {
    fieldCode: 'sendTime', // 批次/卷号
    fieldName: i18n.t('批次/卷号')
  },
  {
    fieldCode: 'forecastArriveTime', // 退货数量
    fieldName: i18n.t('退货数量')
  },
  {
    fieldCode: 'deliveryRemark', // 实收数量
    fieldName: i18n.t('实收数量')
  },
  {
    fieldCode: 'unitName', // 单位
    fieldName: i18n.t('单位')
  },
  {
    fieldCode: 'buyerOrgName', // 采购组
    fieldName: i18n.t('采购组')
  },
  {
    fieldCode: 'itemName', // 行备注
    fieldName: i18n.t('行备注')
  },
  {
    fieldCode: 'orderCode', // 关联采购订单号
    fieldName: i18n.t('关联采购订单号')
  },
  {
    fieldCode: 'lineNo', // 关联采购订单行号
    fieldName: i18n.t('关联采购订单行号')
  }

  // {
  //   fieldCode: "deliveryType", // 交货方式:1-采购订单,2-交货计划,3-JIT
  //   fieldName: i18n.t("交货方式"),
  // },
  // {
  //   fieldCode: "orderCode", // 采购订单号
  //   fieldName: i18n.t("采购订单号"),
  // },
  // {
  //   fieldCode: "lineNo", // 采购订单行号
  //   fieldName: i18n.t("采购订单行号"),
  // },
  // {
  //   fieldCode: "workOrderNo", // 关联工单号
  //   fieldName: i18n.t("关联工单号"),
  // },
  // {
  //   fieldCode: "saleOrderNo", // 关联销售订单号
  //   fieldName: i18n.t("关联销售订单号"),
  // },
  // {
  //   fieldCode: "saleOrderLineNo", // 关联销售订单行号
  //   fieldName: i18n.t("关联销售订单行号"),
  // },
  // {
  //   fieldCode: "bomCode", // BOM号
  //   fieldName: i18n.t("BOM号"),
  // },
  // {
  //   fieldCode: "productCode", // 关联产品代码
  //   fieldName: i18n.t("关联产品代码"),
  // },
  // {
  //   fieldCode: "workCenterName", // 工作中心
  //   fieldName: i18n.t("工作中心"),
  // },
  // {
  //   fieldCode: "processName", // 工序名称
  //   fieldName: i18n.t("工序名称"),
  // },
  // {
  //   fieldCode: "sendAddress", // 送货地址
  //   fieldName: i18n.t("送货地址"),
  // },
  // {
  //   fieldCode: "batchCode", // 批次号
  //   fieldName: i18n.t("批次号"),
  // },
  // {
  //   fieldCode: "limitQuantity", // 限量数量
  //   fieldName: i18n.t("限量数量"),
  // },
  // {
  //   fieldCode: "warehouseClerkName", // 仓管员
  //   fieldName: i18n.t("仓管员"),
  // },
  // {
  //   fieldCode: "dispatcherName", // 调度员
  //   fieldName: i18n.t("调度员"),
  // },
  // {
  //   fieldCode: "demandDate", // 需求日期
  //   fieldName: i18n.t("需求日期"),
  // },
  // {
  //   fieldCode: "demandTime", // 需求时间
  //   fieldName: i18n.t("需求时间"),
  // },
  // {
  //   fieldCode: "deliveryQuantity", // 本次送货数量
  //   fieldName: i18n.t("本次送货数量"),
  // },
  // {
  //   fieldCode: "receiveQuantity", // 收货数量
  //   fieldName: i18n.t("收货数量"),
  // },
  // {
  //   fieldCode: "rejectQuantity", // 拒绝数量
  //   fieldName: i18n.t("拒绝数量"),
  // },
  // {
  //   fieldCode: "rejectReason", // 拒绝原因
  //   fieldName: i18n.t("拒绝原因"),
  // },
  // {
  //   fieldCode: "receiveTime", // 收货时间 到货时间
  //   fieldName: i18n.t("收货时间"),
  // },
  // {
  //   fieldCode: "inventoryQuantity", // 入库数量
  //   fieldName: i18n.t("入库数量"),
  // },
  // {
  //   fieldCode: "inventoryTime", // 入库日期
  //   fieldName: i18n.t("入库日期"),
  // },
  // {
  //   fieldCode: "receiveSupplierName", // 收货供应商
  //   fieldName: i18n.t("收货供应商"),
  // },
  // {
  //   fieldCode: "cancelPersonName", // 取消人
  //   fieldName: i18n.t("取消人"),
  // },
  // {
  //   fieldCode: "cancelTime", // 取消时间
  //   fieldName: i18n.t("取消时间"),
  // },
  // {
  //   fieldCode: "collectorMark", // 代收标识
  //   fieldName: i18n.t("代收标识"),
  // },
  // {
  //   fieldCode: "collectorName", // 代收人
  //   fieldName: i18n.t("代收人"),
  // },
  // {
  //   fieldCode: "createUserName", // 创建人
  //   fieldName: i18n.t("创建人"),
  // },
  // {
  //   fieldCode: "createTime", // 创建时间
  //   fieldName: i18n.t("创建时间"),
  // },
  // {
  //   fieldCode: "remark", // 行备注
  //   fieldName: i18n.t("行备注"),
  // },
]
