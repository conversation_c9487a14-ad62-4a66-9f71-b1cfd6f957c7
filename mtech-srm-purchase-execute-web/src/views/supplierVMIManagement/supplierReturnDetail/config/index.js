import { DeliveryTypeOptions, StatusOptions } from './constant'
import { timeDate } from './columnComponent'

// 格式化表格动态数据
export const formatTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150'
    }
    if (col.fieldCode === 'deliveryType') {
      // 送货单列表-类型
      defaultCol.valueConverter = {
        type: 'map',
        map: DeliveryTypeOptions
      }
    } else if (col.fieldCode === 'status') {
      // 送货单列表-状态
      defaultCol.valueConverter = {
        type: 'map',
        map: StatusOptions
      }
    } else if (col.fieldCode === 'saleOrderNo') {
      // 送货单列表-关联销售订单号
      defaultCol.width = '250'
    } else if (col.fieldCode === 'saleOrderLineNo') {
      // 送货单列表-关联销售订单行号
      defaultCol.width = '250'
    } else if (
      col.fieldCode === 'sendTime' ||
      col.fieldCode === 'forecastArriveTime' ||
      col.fieldCode === 'demandDate' ||
      col.fieldCode === 'demandTime' ||
      col.fieldCode === 'inventoryTime' ||
      col.fieldCode === 'cancelTime' ||
      col.fieldCode === 'receiveTime'
    ) {
      // sendTime 发货日期 integer
      // forecastArriveTime 预计到货日期 integer
      // demandDate 需求日期 string
      // demandTime 需求时间 string
      // inventoryTime 入库日期 integer
      // cancelTime 取消时间 integer
      // receiveTime 收货时间 integer
      defaultCol.template = timeDate({
        dataKey: col.fieldCode,
        hasTime: true
      })
    }
    colData.push(defaultCol)
  })

  return colData
}
