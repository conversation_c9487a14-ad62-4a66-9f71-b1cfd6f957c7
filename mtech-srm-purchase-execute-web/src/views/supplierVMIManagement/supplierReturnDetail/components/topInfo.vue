<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- {{headerInfo}} -->
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div :class="[statusToClass(headerInfo.status), 'mr20']">
        {{ headerInfo.status | statusFormat }}
      </div>
      <div class="infos mr20">{{ $t('创建人：') }}{{ headerInfo.preparer }}</div>
      <div class="infos">
        {{ $t('创建时间：') }}{{ headerInfo.createTime | dateFormat }}
        {{ headerInfo.createTime | timeFormat }}
      </div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>

      <div class="sort-box" @click="doExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="siteName" :label="$t('工厂')">
          <mt-input
            v-model="headerInfo.plantDescription"
            :disabled="true"
            :placeholder="$t('工厂')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="companyName" :label="$t('供应商编码')">
          <mt-input
            v-model="headerInfo.supplierCode"
            :disabled="true"
            :placeholder="$t('供应商编码')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="warehouseName" :label="$t('供应商描述')">
          <mt-input
            v-model="headerInfo.supplierDescription"
            :disabled="true"
            :placeholder="$t('供应商描述')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="deliveryType" :label="$t('VMI仓编码')">
          <mt-input
            v-model="headerInfo.VMIWarehouseCode"
            :disabled="true"
            :placeholder="$t('VMI仓编码')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="receiverCode" :label="$t('VMI仓描述')">
          <mt-input
            v-model="headerInfo.VMIWarehouseDescription"
            :disabled="true"
            :placeholder="$t('VMI仓描述')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="receiveAddressName" :label="$t('送货地址')">
          <mt-input
            v-model="headerInfo.shippingAddress"
            :disabled="true"
            :placeholder="$t('送货地址')"
          ></mt-input>
        </mt-form-item>

        <!-- <mt-form-item prop="sendTime" :label="$t('发货日期')">
          <mt-date-time-picker
            v-model="headerInfo.sendTime"
            :disabled="true"
            :show-clear-button="true"
            :allow-edit="false"
            placeholder=""
          ></mt-date-time-picker>
        </mt-form-item> -->

        <!-- 供方备注 -->
        <mt-form-item class="full-width" prop="remark" :label="$t('备注')" :show-message="false">
          <mt-input
            v-model="headerInfo.remark"
            :disabled="true"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { StatusText, StatusCssClass, DeliveryTypeOptions } from '../config/constant'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      DeliveryTypeOptions,
      isExpand: true,
      rules: {}
    }
  },
  mounted() {},
  filters: {
    dateFormat(value) {
      let str = ''
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }

      return str
    },
    timeFormat(value) {
      let str = ''
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'HH:MM:SS', value })
      } else {
        str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
      }

      return str
    },
    statusFormat(value) {
      if (!StatusText[value]) {
        return value
      } else {
        return StatusText[value]
      }
    }
  },
  methods: {
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    },
    // 状态 转对应的 css class
    statusToClass(value) {
      let cssClass = ''
      if (StatusCssClass[value]) {
        cssClass = StatusCssClass[value]
      }
      return cssClass
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
