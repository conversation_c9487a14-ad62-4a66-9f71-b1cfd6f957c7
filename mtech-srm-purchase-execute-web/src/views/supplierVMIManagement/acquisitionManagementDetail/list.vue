<template>
  <div>
    <mt-template-page
      ref="template-0"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
  </div>
</template>

<script>
import * as CONFIG from './config/list'
export default {
  props: {},
  data() {
    return {
      componentConfig: [
        {
          title: this.$t('单据视图'),

          toolbar: [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              title: this.$t('新增')
            },
            {
              id: 'Publish',
              icon: 'icon_solid_pushorder',
              title: this.$t('发布')
            },
            {
              id: 'Expedited',
              icon: 'icon_solid_Activateorder',
              title: this.$t('测试')
            }
          ],
          grid: {
            height: 'auto',
            columnData: CONFIG.columnData,
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/purOrder/query'
            }
          }
        }
      ]
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    handleClickToolBar(e) {
      const selectRows = e.grid.getSelectedRecords()
      console.log(e, 1111)
      // console.log(selectRows, 1111);
      if (selectRows.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let ids = []
      selectRows.map((item) => ids.push(item.id))
      switch (e.toolbar.id) {
        case 'Expedited':
          this.handleCancleExpedited(ids, selectRows)
          break
      }
    },
    handleCancleExpedited(ids, selectRows) {
      let params = {
        ids: ids
      }
      this.$API.purchaseOrder.purOrderCancelUrgent(params).tnen((res) => {
        console.log(res, 222)
        console.log(selectRows, 222)
      })
    }
  },
  components: {}
}
</script>

<style scoped lang="scss"></style>
