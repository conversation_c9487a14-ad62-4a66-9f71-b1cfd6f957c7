<template>
  <div class="full-height pt20 vertical-flex-box">
    <top-info
      class="flex-keep"
      :header-info="headerInfo"
      @goBack="goBack"
      @submitBtn="submitBtn"
      @preservationBtn="preservationBtn"
      ref="infoRules"
    ></top-info>
    <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
    <mt-template-page
      v-if="forType"
      v-show="tabIndex === 0"
      ref="templateRef"
      @cellEdit="cellEdit"
      :template-config="componentConfig"
      @handleClickCellTool="handleClickCellTool"
      @actionBegin="actionBegin"
    >
    </mt-template-page>
    <div v-show="tabIndex === 1" :style="{ padding: '30px' }">
      <logistics-info ref="logisticsRef" :logistics-data="logisticsData"></logistics-info>
    </div>
  </div>
</template>
<script>
import { columnSelect } from './config/create.js'
import { cloneDeep } from 'lodash'
import { dataSource } from './config/create.js'
export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue'),
    LogisticsInfo: () => import('./components/logisticsInfo.vue')
  },
  provide() {
    return {
      wuliaoList: this.headerInfo
    }
  },
  props: {},
  data() {
    return {
      componentConfig: [
        {
          // toolbar: [[]],
          grid: {
            dataSource: [],
            // useToolTemplate: false, // 使用预置表格操作按钮(新增、编辑、删除)
            // useBaseConfig: false, // 使用组件中的toolbar配置（打开筛选、刷新、设置）
            lineIndex: 1,
            height: 'auto',
            allowPaging: false, // 不分页
            columnData: columnSelect
          }
        }
      ],
      forType: false,
      cellData: [],
      createDateHeader: [],
      headerInfo: {},
      createDate: JSON.parse(sessionStorage.getItem('createDate')),

      // 物流信息数据
      logisticsData: {
        transportType: 1,
        statusCreate: true
      },
      tabSource: [
        {
          title: this.$t('物料信息')
        }
        // {
        //   title: this.$t("物流信息"),
        // },
      ],
      tabIndex: 0
    }
  },
  mounted() {
    // create === 1  编辑
    // create === 0  详情

    // this.componentConfig[0].grid.dataSource = this.createDate;?
    // if (this.$route.query.create == 1 && this.$route.query.status == 0) {
    // }

    if (this.$route.query.create == 1) {
      dataSource.length = 0
      this.createDateHeader = cloneDeep(this.createDate)
      if (this.$route.query.status == 0) {
        this.getDetailData()
      } else {
        this.createDate = JSON.parse(sessionStorage.getItem('createDate'))[0]
        JSON.parse(sessionStorage.getItem('createDate')).forEach((item) => {
          item.rawDataLine = 1
          dataSource.push(item)
        })
        this.createDate[0] = cloneDeep(dataSource[0])
      }

      this.headerInfo.statusCreate = false
      this.logisticsData.statusCreate = false
      this.forType = true
      this.componentConfig[0].grid.dataSource = cloneDeep(dataSource)
      this.headerInfo = this.createDateHeader[0]
    } else {
      this.getDetailData()
      // this.componentConfig[0].toolbar = [];
    }
    // 新建状态下可修改
  },
  watch: {
    'headerInfo.identificationStatus': function () {
      // if (newVal == "1") {
      this.componentConfig[0].grid.columnData = columnSelect
      // }
    }
  },
  beforeDestroy() {
    this.componentConfig[0].grid.dataSource = []
    dataSource.length = 0
  },
  methods: {
    handleClickCellTool(e) {
      if (e.tool.id === 'submit') {
        // 参数值需要修改
        dataSource.push({
          ...e.data,
          id: '',
          rawDataLine: 0,
          takeNo: '',
          batchCode: '',
          count: ''
        })
        this.componentConfig[0].grid.dataSource = cloneDeep(dataSource)
        // console.log(this.componentConfig[0].grid.dataSource);
        // console.log(dataSource);
      }

      if (e.tool.id === 'delete') {
        dataSource.splice(e.componentData.index, 1)
        this.componentConfig[0].grid.dataSource = cloneDeep(dataSource)
      }
    },
    cellEdit(e) {
      console.log(e)
      // this.cellData = cloneDeep(this.componentConfig[0].grid.dataSource);
      // this.cellData.splice(e.index, 1, {
      //   ...this.componentConfig[0].grid.dataSource[e.index],
      //   [e.key]: e.value,
      // });
      // 更新当前页 dataSource
      // this.componentConfig[0].grid.dataSource.splice(e.index, 1, {
      //   ...this.componentConfig[0].grid.dataSource[e.index],
      //   [e.key]: e.value,
      // });
    },
    // 切换
    handleSelectTab(e) {
      this.tabIndex = e
      // 切换时结束编辑
      this.$refs.templateRef.getCurrentUsefulRef().gridRef?.ejsRef.endEdit()
    },
    actionBegin(args) {
      if (
        this.$route.query.create == 0 &&
        args.rowIndex === args.rowIndex &&
        args.rowIndex !== undefined
      ) {
        args.cancel = true //禁止行编辑
      }
    },

    // 返回
    goBack() {
      this.$router.go(-1)
    },
    // 提交
    submitBtn(e) {
      this.createDate = e

      //获取编辑后的整体数据
      // console.log(
      //   this.$refs.templateRef
      //     .getCurrentUsefulRef()
      //     .gridRef?.ejsRef.getCurrentViewRecords(),
      //   "-=-=-="
      // );
      this.createInterface(2)
    },
    //保存
    preservationBtn(e) {
      this.createDate = e
      this.createInterface(1)
    },
    // 获取详情接口
    getDetailData() {
      let obj = {
        id: this.$route.query.id
      }
      this.$API.supplierCoordination.postReceiveDetail(obj).then((res) => {
        if (res.code === 200) {
          this.headerInfo = res.data
          this.headerInfo.statusCreate = true
          this.logisticsData.statusCreate = true
          if (res.data.orderLogistic.length === 0) {
            this.logisticsData = {
              transportType: 1,
              statusCreate: true
            }
          } else {
            this.logisticsData = res.data.orderLogistic[0]
          }
          // this.$set(this.componentConfig[0].grid,'dataSource',res.data.vmiOrderItemResponses)
          res.data.vmiOrderItemResponses.forEach((item) => {
            item.unitCode = item.itemUnit
            item.unitName = item.itemUnitDescription

            item.status = res.data.status
            item.buyerOrgCode = item.purchaseGroupCode
            item.buyerOrgName = item.purchaseGroupName
          })
          console.log(res.data)
          dataSource.length = 0
          dataSource.push(...res.data.vmiOrderItemResponses)
          this.createDate = res.data

          this.componentConfig[0].grid.dataSource = cloneDeep(res.data.vmiOrderItemResponses)

          this.forType = true
        }
      })
    },
    // 创建接口
    createInterface(btnStatus) {
      // 校验  物流信息没有填写完整 就return
      // this.componentConfig[0].grid.dataSource = cloneDeep(dataSource);
      console.log(this.componentConfig[0].grid.dataSource)
      // let dataList = this.$refs.templateRef
      //   .getCurrentUsefulRef()
      //   .gridRef?.ejsRef.getCurrentViewRecords();
      let dataList = dataSource
      for (let item of dataList) {
        if (item.takeNo === '' || item.batchCode === '' || item.count === '') {
          this.$toast({
            content: this.$t('请检查车号/卷号/卷重是否正常填写'),
            type: 'error'
          })

          return
        }
      }
      console.log(dataList)
      let data = {
        itemList: [],
        logisticsList: [],
        operationType: btnStatus,
        remark: this.createDate?.remark,
        siteCode: this.createDate.siteCode,
        supplierCode: this.createDate.supplierCode,
        vmiWarehouseAddress: this.createDate.vmiWarehouseAddress,
        vmiWarehouseCode: this.createDate.vmiWarehouseCode
      }
      if (!this.$route.query.id) {
        dataList.forEach((item) => {
          item.id = ''
        })
      }
      if (this.$route.query.id) {
        data.id = this.$route.query.id
      }
      dataList.forEach((item) => {
        // item.lineNo = item.lineNo;
        // item.orderCode = item.orderCode;
        item.itemUnit = item.unitCode
        item.purchaseGroupName = item.buyerOrgName
        item.purchaseGroupCode = item.buyerOrgCode

        // item.countLimit = item.countLimit;
        // item.count = item.count;
        item.purchaseGroupCode = item.buyerOrgCode
      })
      data.itemList = dataList
      data.logisticsList.push(this.logisticsData)
      if (!this.$refs.logisticsRef.checkForm()) {
        // this.$toast({ content: this.$t("物流信息没有填完整"), type: "error" });
        // return;
        data.logisticsList = null
      }
      console.log(data)
      this.$API.supplierCoordination.postBuyerWarehousingSave(data).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('创建成功'), type: 'success' })
          this.$router.go(-1)
        } else {
          this.$toast({
            content: this.$t('创建失败'),
            type: 'console.error();'
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss"></style>
