<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="$t('请选择采购订单行')"
      @change="selectChange"
      :disabled="disabledStatus"
    >
    </mt-select>
  </div>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      dataSource: [],
      fields: { text: 'label', value: 'value' },
      disabledStatus: false
    }
  },
  mounted() {
    if (this.$route.query.create === 'false') this.disabledStatus = true
    //监听数据
    this.$bus.$on('getOrderLineData', (data) => {
      if (data && data.length > 0) {
        this.dataSource = data
      }
    })
  },
  methods: {
    selectChange(value) {
      const data = value?.itemData
      if (data) {
        this.$bus.$emit('itemCodeChange', data.itemCode)
        this.$bus.$emit('itemNameChange', data.itemName)
        this.$bus.$emit('quantityChange', data.quantity)
        this.$bus.$emit('unitNameChange', data.unitName)
        this.$bus.$emit('buyerOrgNameChange', data.buyerOrgName)
        this.$bus.$emit('buyerOrgCodeChange', data.buyerOrgCode)
        this.$bus.$emit('unitCodeChange', data.unitCode)
      }
      let obj = {
        lineNo: data.itemNo,
        orderCode: data.orderCode,
        orderCount: data.quantity
      }
      this.getReceiveLimitQuery(obj).then((res) => {
        this.$bus.$emit('countLimitChange', res.data.countLimit)
      })
    },
    // 根据订单编号及行号查询该订单可发货数量
    getReceiveLimitQuery(data) {
      return this.$API.supplierCoordination.postReceiveLimitQuery(data)
    }
  }
}
</script>

<style scoped lang="scss"></style>
