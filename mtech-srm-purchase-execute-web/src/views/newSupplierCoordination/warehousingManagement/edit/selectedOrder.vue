<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="$t('输入订单号模糊查询')"
      :allow-filtering="true"
      :filtering="purOrderQueryOrder"
      @change="selectChange"
      :disabled="disabledStatus"
    >
    </mt-select>
  </div>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      dataSource: [],
      fields: { text: 'label', value: 'value' },
      disabledStatus: false
    }
  },
  mounted() {
    if (this.$route.query.create === 'false') this.disabledStatus = true
    this.purOrderQueryOrder({ value: '' })
  },
  methods: {
    purOrderQueryOrder(value) {
      console.log(value, '-=')
      let params = {
        condition: 'and',
        page: {
          current: 1,
          size: 50
        },
        defaultRules: [
          {
            label: this.$t('采购订单号'),
            field: 'orderCode',
            type: 'string',
            operator: 'contains',
            value: (value && value.text) || ''
          }
        ]
      }
      this.$API.supplierCoordination.purOrderQueryOrder(params).then((res) => {
        let orderOptions = res.data.records
          .map((item) => ({
            label: item,
            value: item
          }))
          .filter((item) => item.value) //过滤一下空值
        this.dataSource = orderOptions
      })
    },
    selectChange(value) {
      const code = value?.itemData?.value
      if (code) {
        //请求采购订单行
        this.$API.vmi.getByOrder({ code }).then((res) => {
          //   const data=res.data;//这里返回的是真实数据   数据不太多  自己造下面的
          // const data = [
          //   {
          //     orderCode: "PO2022011900345", //采购订单号
          //     itemNo: 10, //行号
          //     itemCode: "", //物料编码
          //     itemName: this.$t("医用手术刀"),
          //     quantity: 5.04, //数量
          //     buyerOrgCode: "11111", //采购组编码
          //     buyerOrgName: "采购组11", //采购组名称
          //     unitCode: "1", //单位编码
          //     unitName: this.$t("长度单位"), //长度单位
          //   },
          //   {
          //     orderCode: "PO20220119003451", //采购订单号
          //     itemNo: 11, //行号
          //     itemCode: "", //物料编码
          //     itemName: "11的物资",
          //     quantity: 6.04, //数量
          //     buyerOrgCode: "22222", //采购组编码
          //     buyerOrgName: "采购组22", //采购组名称
          //     unitCode: "1", //单位编码
          //     unitName: this.$t("长度单位"), //长度单位
          //   },
          // ];
          const orderLineData = res.data.reduce((pre, now) => {
            pre.push({
              label: now.itemNo,
              value: now.itemNo,
              ...now
            })
            return pre
          }, [])
          this.$bus.$emit('getOrderLineData', orderLineData)
        })
      }
    }
  }
}
</script>

<style scoped lang="scss"></style>
