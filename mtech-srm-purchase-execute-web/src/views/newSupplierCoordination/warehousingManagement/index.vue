// VMI入库管理
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      current-tab="0"
      :permission-obj="permissionObj"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @rowSelecting="rowSelecting"
      @rowDeselected="rowDeselecting"
    ></mt-template-page>
    <create-dialog
      v-if="deliveryShow"
      ref="child"
      @handleAddDialogShow="handleAddDialogShow"
      @handleDialogShow="handleDialogShow"
    ></create-dialog>
    <!-- 导入弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { columnObj } from './config/index.js'
import Vue from 'vue'
import utils from '@/utils/utils'
import { cloneDeep } from 'lodash'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    // deliveryDialog,
    createDialog: require('./components/createDialog').default,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  data() {
    return {
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0122' },
          { dataPermission: 'b', permissionCode: 'T_02_0123' }
        ]
      },
      requestUrls: {
        templateUrlPre: 'supplierCoordination',
        templateUrl: 'vmiSteelExportExcel',
        uploadUrl: 'vmiSteelImportExcel'
      }, // 导入下载接口地址
      downTemplateParams: {
        flag: 0
      }, // 下载模板参数
      pageConfig: [
        {
          tab: { title: this.$t('头视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          dataPermission: 'a',
          permissionCode: 'T_02_0122',
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'add',
                  // icon: "icon_solid_Createorder",
                  permission: ['O_02_1260'],
                  title: this.$t('创建VMI入库单')
                },
                {
                  id: 'submit',
                  // icon: "icon_solid_Createorder",
                  // permission: ["O_02_1260"],
                  title: this.$t('提交')
                },
                {
                  id: 'receive',
                  // icon: "icon_solid_Delete",
                  permission: ['O_02_1261'],
                  title: this.$t('接收')
                },
                {
                  id: 'delete',
                  // icon: "icon_solid_Delete",
                  permission: ['O_02_1262'],
                  title: this.$t('删除')
                },
                {
                  id: 'upload',
                  // icon: "icon_solid_Delete",
                  // permission: ["O_02_1262"],
                  title: this.$t('导入')
                },
                {
                  id: 'export1',
                  title: this.$t('导出')
                }
                // {
                //   id: "print",
                //   // icon: "icon_solid_Delete",
                //   permission: ["O_02_1281"],
                //   title: this.$t("打印"),
                // },
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: '0798a0b1-5117-4e1e-b595-5407a5bd623d',
          grid: {
            columnData: columnObj.headColumn,
            dataSource: [
              {
                vmiOrderCode: 'w20211227001',
                status: 1,
                factoryCode: '630847',
                factoryDescribe: this.$t('TCL空调器有限公司生产工厂'),
                warehouseCode: '0100001',
                warehouseDescribe: this.$t('VMI红物流芜湖威灵电机'),
                supplierCode: 'G 89001',
                materialDescribe: this.$t('广东惠利普智能科技股份有限公司'),
                goodsAddress: this.$t('深圳市福田区下梅林梅华路'),
                createTime: '2021-12-27 13:50：33',
                receiveTime: '2021-12-30'
              }
            ],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiSteel/vmi-receive-order/supplier-page-query',
              recordsPosition: 'data.records',
              serializeList: this.serialize
            },
            frozenColumns: 1
          }
        },
        {
          tab: { title: this.$t('入库接收') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          dataPermission: 'b',
          permissionCode: 'T_02_0123',
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'receive1',
                  // icon: "icon_solid_Delete",
                  // permission: ["O_02_1261"],
                  title: this.$t('接收')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            allowPaging: false, // 不分页

            columnData: columnObj.detailedColumn,
            dataSource: [
              {
                warehousingCode: 'w20211227001',
                status: 1,
                rowNumber: 10,
                materialCode: '630847',
                materialName: this.$t('自攻特攻GB/T'),
                procurementGroup: this.$t('采购组01'),
                goodsNumber: 500,
                receivingNumber: 100,
                company: this.$t('件'),
                remarks: this.$t('备注文本字段'),
                qualityResult: this.$t('不合格'),
                unqualifiedClassification: this.$t('不良价值'),
                qualityRemarks: this.$t('质检备注内容'),
                relationCode: 'D848940',
                relationRowCode: 10,
                factoryCode: '630847',
                factoryDescribe: this.$t('TCL空调器有限公司生产工厂'),
                warehouseCode: '0100001',
                warehouseDescribe: this.$t('VMI红物流芜湖威灵电机'),
                supplierCode: 'G 89001',
                materialDescribe: this.$t('广东惠利普智能科技股份有限公司'),
                goodsAddress: this.$t('深圳市福田区下梅林梅华路'),
                createTime: '2021-12-27 13:50：33',
                receiveTime: '2021-12-30'
              }
            ],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiSteel/vmi-receive-order/supplier-item-page-query',
              page: {
                current: 1,
                size: 10000
              },
              rules: [
                {
                  field: 'status',
                  operator: 'equal',
                  value: 1
                }
              ],
              recordsPosition: 'data.records',
              serializeList: this.serialize
            },
            frozenColumns: 1
          }
        },
        {
          tab: { title: this.$t('明细视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          dataPermission: 'b',
          permissionCode: 'T_02_0123',
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'export2',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: '9bd6a477-7fbd-496b-9677-2441b0986b9d',
          grid: {
            columnData: columnObj.detailedColumn,
            dataSource: [
              {
                warehousingCode: 'w20211227001',
                status: 1,
                rowNumber: 10,
                materialCode: '630847',
                materialName: this.$t('自攻特攻GB/T'),
                procurementGroup: this.$t('采购组01'),
                goodsNumber: 500,
                receivingNumber: 100,
                company: this.$t('件'),
                remarks: this.$t('备注文本字段'),
                qualityResult: this.$t('不合格'),
                unqualifiedClassification: this.$t('不良价值'),
                qualityRemarks: this.$t('质检备注内容'),
                relationCode: 'D848940',
                relationRowCode: 10,
                factoryCode: '630847',
                factoryDescribe: this.$t('TCL空调器有限公司生产工厂'),
                warehouseCode: '0100001',
                warehouseDescribe: this.$t('VMI红物流芜湖威灵电机'),
                supplierCode: 'G 89001',
                materialDescribe: this.$t('广东惠利普智能科技股份有限公司'),
                goodsAddress: this.$t('深圳市福田区下梅林梅华路'),
                createTime: '2021-12-27 13:50：33',
                receiveTime: '2021-12-30'
              }
            ],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiSteel/vmi-receive-order/supplier-item-page-query',

              recordsPosition: 'data.records',
              serializeList: this.serialize
            },
            frozenColumns: 1
          }
        }
      ],
      deliveryShow: false,

      addDialogShow: false,
      dialogData: null
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('createDate')
  },
  methods: {
    // 弹框
    handleDialogShow(flag) {
      this.deliveryShow = flag
    },
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    serialize(data) {
      data.forEach((item) => {
        item.submitTime = Number(item.submitTime)
        item.confirmTime = Number(item.confirmTime)
      })
      return data
    },
    // create === 1  编辑
    // create === 0  详情
    // 跳转详情
    handleClickCellTitle(e) {
      if (e.field === 'vmiOrderCode' && e.tabIndex === 0) {
        let obj = {
          create: 0,
          id: e.data.id,
          key: utils.randomString()
        }
        // e.data.status === 0 新建状态可修改 传值status
        if (e.data.status === 0) obj.create = 1
        obj.status = e.data.status
        this.redirectPage('purchase-execute/new-supplier-warehousing-create', obj)
      }
    },
    // 头部操作
    handleClickToolBar(e) {
      let _id = e.grid.getSelectedRecords().map((item) => {
        return item.id
      })
      if (e.toolbar.id === 'add') {
        // let obj = {
        //   create: 1,
        // };
        // 跳转到新建
        // this.redirectPage("purchase-execute/supplier-warehousing-create", obj);
        this.deliveryShow = true
      } else if (e.toolbar.id === 'export1' || e.toolbar.id === 'export2') {
        let obj = JSON.parse(
          sessionStorage.getItem('0798a0b1-5117-4e1e-b595-5407a5bd623d')
        )?.visibleCols
        if (e.toolbar.id === 'export2') {
          obj = JSON.parse(
            sessionStorage.getItem('9bd6a477-7fbd-496b-9677-2441b0986b9d')
          )?.visibleCols
        }
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          const columnDataList =
            e.toolbar.id === 'export2' ? columnObj.detailedColumn : columnObj.headColumn
          columnDataList.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        const queryBuilderRules =
          this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        const params = {
          page: { current: 1, size: 10000 },
          ...queryBuilderRules
        } // 筛选条件
        this.$store.commit('startLoading')
        this.$API.supplierCoordination.vmiReceiveOrderExport(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
      } else if (e.toolbar.id === 'delete' && e.grid.getSelectedRecords().length > 0) {
        // 删除弹窗
        this.$dialog({
          data: {
            title: this.$t('删除'),
            message: this.$t('确认删除吗？')
          },
          success: () => {
            this.delete(e.grid.getSelectedRecords())
          }
        })
      } else if (e.toolbar.id === 'submit' && e.grid.getSelectedRecords().length > 0) {
        this.summitClick(e.grid.getSelectedRecords())
      } else if (e.toolbar.id == 'upload') {
        this.showUploadExcel(true)
      } else if (e.toolbar.id === 'receive' && e.grid.getSelectedRecords().length > 0) {
        let ids = e.grid.getSelectedRecords().map((item) => item.id)
        this.receive(ids)
      } else if (e.toolbar.id === 'receive1' && e.grid.getSelectedRecords().length > 0) {
        let ids = e.grid.getSelectedRecords().map((item) => item.vmiOrderId)
        // let obj = new Set([ids]);
        for (var i = 0; i < ids.length; i++) {
          for (var j = i + 1; j < ids.length; j++) {
            if (ids[i] == ids[j]) {
              ids.splice(j, 1)
            }
          }
        }

        this.receive(ids)
      } else if (e.toolbar.id === 'print') {
        let obj = { idList: _id }

        this.handleConfirm(obj)
        e.grid.getSelectedRecords()
      } else if (e.toolbar.id === 'refreshDataByLocal') {
        // 调用刷新
        debugger
      } else if (e.grid.getSelectedRecords().length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
    },
    handleConfirm(row) {
      //打印排期列表
      console.log(row)
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认打印选中的条码需求吗?')
        },
        success: () => {
          this.$API.supplierCoordination.postOrderPrint(row).then((res) => {
            if (res?.data?.type === 'application/json') {
              const reader = new FileReader()
              reader.readAsText(res?.data, 'utf-8')
              reader.onload = function () {
                console.log('======', reader)
                const readerRes = reader.result
                const resObj = JSON.parse(readerRes)
                Vue.prototype.$toast({
                  content: resObj.msg,
                  type: 'error'
                })
              }

              return
            }
            const content = res.data
            this.pdfUrl = window.URL.createObjectURL(
              new Blob([content], { type: 'application/pdf' })
            )
            // window.open(this.pdfUrl);
            let date = new Date().getTime()
            let ifr = document.createElement('iframe')
            ifr.style.frameborder = 'no'
            ifr.style.display = 'none'
            ifr.style.pageBreakBefore = 'always'
            ifr.setAttribute('id', 'printPdf' + date)
            ifr.setAttribute('name', 'printPdf' + date)
            ifr.src = this.pdfUrl
            document.body.appendChild(ifr)
            this.doPrint('printPdf' + date)
            window.URL.revokeObjectURL(ifr.src)
          })
        }
      })
    },
    //复选框事件
    rowSelecting(e) {
      if (e?.rowIndexes?.length < 1) return
      if (e.data instanceof Array) return
      // if (e?.data?.status != 2) {
      //   let mapArr = e.rowIndexes.filter((item) => {
      //     return item !== e.rowIndex;
      //   });
      //   if (!(e.data instanceof Array)) {
      //     console.log(e, mapArr);
      //     this.$nextTick(() => {
      //       this.$refs.templateRef
      //         .getCurrentUsefulRef()
      //         .ejsRef.selectRows(mapArr);
      //     });
      //   }
      //   return;
      // }
      //获取当前页所有的行
      let Obj = cloneDeep(
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getCurrentViewRecords()
      )
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getSelectedRecords()
      )
      currentSelect.push(e.data)
      currentSelect = [...new Set(currentSelect)]
      if (Obj instanceof Array && Obj.length > 0) {
        let mapArr = []
        for (let i = 0; i < currentSelect.length; i++) {
          for (let j = 0; j < Obj.length; j++) {
            if (currentSelect[i]?.vmiOrderCode === Obj[j]?.vmiOrderCode) {
              mapArr.push(Obj[j])
            }
          }
        }
        mapArr = mapArr.map((item) => item.index)
        if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0 && mapArr.length > 0) {
          this.$refs.templateRef.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
        }
        console.log(mapArr)
      }
    },
    //取消
    rowDeselecting(e) {
      if (e?.rowIndexes?.length < 1) return
      //获取当前页所有的行
      let Obj = cloneDeep(
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getCurrentViewRecords()
      )
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.templateRef
          .getCurrentUsefulRef()
          .ejsRef.getSelectedRecords()
          .map((e) => e.id)
      )
      let mapArr = []
      Obj.filter((item, index) => {
        if (item.vmiOrderCode !== e.data.vmiOrderCode && currentSelect.includes(item.id)) {
          console.log(item)
          mapArr.push(index)
        }
      })
      console.log(e, mapArr)
      if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0) {
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
      }
    },
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 获取dom打印方法 异步执行
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        this.pdfLoading = false
      }, 100)
    },
    handleClickCellTool(e) {
      let idOBj = {
        id: e.data.id
      }
      // 提交
      if (e.tool.id === 'submit') {
        // 参数值需要修改
        this.$dialog({
          data: {
            title: this.$t('提交'),
            message: this.$t('确认提交吗？')
          },
          success: () => {
            this.submit(idOBj)
          }
        })
      } else if (e.tool.id === 'cancel') {
        //取消
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消吗？')
          },
          success: () => {
            this.cancel(idOBj)
          }
        })
      }
    },
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },
    // 接收
    receive(id) {
      this.$store.commit('startLoading')
      let obj = {
        idList: id
      }
      this.$API.supplierCoordination
        .postReceiveConfirm(obj)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.refreshColumns()
          } else {
            this.$toast({
              content: this.$t('操作失败'),
              type: 'console.error();'
            })
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    // 导入成功后，获取到的数据
    upExcelConfirm() {
      // this.$refs["uploadExcelRef"].refreshCurrentGridData();
      this.showUploadExcel(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
    },
    // 删除
    delete(data) {
      let obj = {
        ids: data.map((item) => item.id)
      }
      this.$API.supplierCoordination.postReceiveDelete(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({
            content: this.$t('删除失败'),
            type: 'console.error();'
          })
        }
      })
    },
    summitClick(data) {
      let obj = {
        ids: data.map((item) => item.id)
      }
      this.$API.supplierCoordination.postReceiveSubmitEvery(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({
            content: this.$t('提交失败'),
            type: 'console.error();'
          })
        }
      })
    },
    // 提交 用的与创建同一个接口
    submit(obj) {
      this.$API.supplierCoordination.postReceiveSubmit(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({
            content: this.$t('提交失败'),
            type: 'console.error();'
          })
        }
      })
    },
    // 取消
    cancel(obj) {
      this.$API.supplierCoordination.postReceiveCancel(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('取消成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({
            content: this.$t('取消失败'),
            type: 'console.error();'
          })
        }
      })
    },
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
