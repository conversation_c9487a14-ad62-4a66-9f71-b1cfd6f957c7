import { i18n } from '@/main.js'
import Vue from 'vue'

// import onlyShowInput from "../edit/onlyShowInput.vue"; // 物料、sku
// import selectedOrder from "../edit/selectedOrder.vue"; // 物料、sku
// import selectedOrderLine from "../edit/selectedOrderLine.vue"; // 物料、sku
// import wuliao from "../edit/wuliao.vue"; // 物料、sku
// vmi仓，goodsReceiptType === 1选择采购订单号，并且调用/tenant/vmi-receive-order/receive-limit-query拿出  VMI可送货数量
// goodsReceiptType 不等于1的时候  选择采购订单号不可以选择    订单数量，VMI可送货数量不展示 物料编码弹窗选择
export let dataSource = []
export const columnSelect = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false, //隐藏在列选择器中的过滤
    allowEditing: false
  },

  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('关联采购订单号'),
    cellTools: [
      {
        id: 'submit',
        // icon: "icon_list_enable",
        title: i18n.t('拆分'),
        // permission: ["O_02_0125"],
        visibleCondition: (data) =>
          data['rawDataLine'] === 1 &&
          data['status'] !== 1 &&
          data['status'] !== 2 &&
          data['status'] !== 8 &&
          data['status'] !== 9
      },
      {
        id: 'delete',
        // icon: "icon_list_enable",
        title: i18n.t('删除'),
        // permission: ["O_02_0125"],
        visibleCondition: (data) =>
          data['rawDataLine'] === 0 &&
          data['status'] !== 1 &&
          data['status'] !== 2 &&
          data['status'] !== 8 &&
          data['status'] !== 9
      }
    ]
  },
  {
    width: '150',
    field: 'lineNo',
    headerText: i18n.t('采购订单行号')
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'quantity',
    headerText: i18n.t('订单数量')
  },
  {
    width: '150',
    field: 'availableQuantity',
    headerText: i18n.t('剩余最大可创建数量'),
    type: 'number'
  },
  {
    width: '150',
    field: 'takeNo',
    headerText: i18n.t('车号'),
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('车号')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `
          <div v-if='type'>{{data.takeNo}}</div>
          <mt-input
          v-else
          v-model="data.takeNo"


          @change="handleInput"

          ></mt-input>`,
          data: function () {
            return {
              data: {},
              type: window.location.href.includes('status=8')
                ? true
                : window.location.href.includes('status=9')
                ? true
                : window.location.href.includes('status=1')
                ? true
                : window.location.href.includes('status=2')
                ? true
                : false
            }
          },
          mounted() {},
          methods: {
            handleInput(e) {
              console.log(e)
              console.log(this.data)

              this.$set(dataSource[this.data.index], 'takeNo', e)
              // this.data.takeNo = e;
              // this.$parent.$emit("cellEdit", {
              //   id: this.data.id,
              //   index: Number(this.data.index),
              //   key: "takeNo",
              //   value: Number(this.data.takeNo),
              // });
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'batchCode',
    headerText: i18n.t('卷号'),
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('卷号')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `
          <div v-if='type'>{{data.batchCode}}</div>
          <mt-input
          v-else
          v-model="data.batchCode"
          @change="handleInput2"
          ></mt-input>`,
          data: function () {
            return {
              data: {},
              type: window.location.href.includes('status=8')
                ? true
                : window.location.href.includes('status=9')
                ? true
                : window.location.href.includes('status=1')
                ? true
                : window.location.href.includes('status=2')
                ? true
                : false
            }
          },
          mounted() {},
          methods: {
            handleInput2(e) {
              console.log(e)
              this.data.batchCode = e
              this.$set(dataSource[this.data.index], 'batchCode', e)
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'count',
    headerText: i18n.t('卷重'),
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('卷重')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `
          <div v-if='type'>{{data.count}}</div>

          <mt-input-number
          :precision="3"
          v-else
          v-model="data.count"
          @change="handleInput2"
          ></mt-input-number>`,
          data: function () {
            return {
              data: {},
              type: window.location.href.includes('status=8')
                ? true
                : window.location.href.includes('status=9')
                ? true
                : window.location.href.includes('status=1')
                ? true
                : window.location.href.includes('status=2')
                ? true
                : false
            }
          },
          mounted() {},
          methods: {
            handleInput2(e) {
              console.log(e)

              this.$set(dataSource[this.data.index], 'count', e)
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'unitCode',
    headerText: i18n.t('单位'),
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `
          <div >{{data.unitCode}}-{{data.unitName}}</div>
          `,
          data: function () {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'buyerOrgName',
    headerText: i18n.t('采购组'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组编码'),
    allowEditing: false
    // visible: false,
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('行备注'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `
          <div v-if='type'>{{data.remark}}</div>
          <mt-input
          v-else
          v-model="data.remark"


          @change="handleInput"

          ></mt-input>`,
          data: function () {
            return {
              data: {},
              type: window.location.href.includes('status=8')
                ? true
                : window.location.href.includes('status=9')
                ? true
                : window.location.href.includes('status=1')
                ? true
                : window.location.href.includes('status=2')
                ? true
                : false
            }
          },
          mounted() {},
          methods: {
            handleInput(e) {
              this.$set(dataSource[this.data.index], 'remark', e)
            }
          }
        })
      }
    }
    // allowEditing: this.$route.query.create ? true : false,
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
    // allowEditing: this.$route.query.create ? true : false,
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间')
    // allowEditing: this.$route.query.create ? true : false,
  }
]
// export const columnPopup = [
//   {
//     width: "50",
//     type: "checkbox",
//     showInColumnChooser: false, //隐藏在列选择器中的过滤
//     allowEditing: false,
//   },
//   {
//     width: "200",
//     field: "itemCode",
//     headerText: i18n.t("物料编码"),
//     headerTemplate: () => {
//       return {
//         template: Vue.component("requiredCell", {
//           template: `
//             <div class="headers">
//               <span style="color: red">*</span>
//               <span class="e-headertext">物料编码</span>
//             </div>
//           `,
//         }),
//       };
//     },
//     editTemplate: () => {
//       return {
//         template: wuliao,
//       };
//     },
//   },
//   {
//     width: "150",
//     field: "itemName",
//     headerText: i18n.t("物料名称"),
//     editTemplate: () => {
//       return { template: onlyShowInput };
//     },
//   },
//   {
//     width: "150",
//     field: "count",
//     headerText: i18n.t("送货数量"),
//     headerTemplate: () => {
//       return {
//         template: Vue.component("requiredCell", {
//           template: `
//             <div class="headers">
//               <span style="color: red">*</span>
//               <span class="e-headertext">送货数量</span>
//             </div>
//           `,
//         }),
//       };
//     },
//     editType: "numericedit", //默认编辑类型之number
//     edit: {
//       params: {
//         min: 1,
//       },
//     },
//   },
//   {
//     width: "150",
//     field: "unitName",
//     headerText: i18n.t("单位"),
//     allowEditing: false,
//     editTemplate: () => {
//       return {
//         template: onlyShowInput,
//       };
//     },
//   },
//   {
//     width: "150",
//     field: "unitCode",
//     headerText: i18n.t("单位编码"),
//     allowEditing: false,
//     editTemplate: () => {
//       return {
//         template: onlyShowInput,
//       };
//     },
//   },
//   {
//     width: "150",
//     field: "buyerOrgName",
//     headerText: i18n.t("采购组"),
//     allowEditing: false,
//     editTemplate: () => {
//       return {
//         template: onlyShowInput,
//       };
//     },
//   },
//   {
//     width: "150",
//     field: "buyerOrgCode",
//     headerText: i18n.t("采购组编码"),
//     allowEditing: false,
//     editTemplate: () => {
//       return {
//         template: onlyShowInput,
//       };
//     },
//     // visible: false,
//   },
//   {
//     width: "150",
//     field: "remark",
//     headerText: i18n.t("行备注"),
//     // allowEditing: this.$route.query.create ? true : false,
//   },
//   {
//     width: "150",
//     field: "batchCode",
//     headerText: i18n.t("卷号"),
//   },
//   {
//     width: "150",
//     field: "takeNo",
//     headerText: i18n.t("车号/船号"),
//   },
// ];
