import { i18n } from '@/main.js'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName } from '@/utils/utils'

export const timeDate = (dataKey, hasTime) => {
  const template = () => {
    return {
      template: Vue.component('date', {
        template: `<div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div><div>{{data[dataKey] | dateFormat}}</div></div>`,
        data: function () {
          return { data: {}, dataKey, hasTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
export const columnObj = {
  headColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '200',
      field: 'vmiOrderCode',
      headerText: i18n.t('VMI入库单号'),
      cellTools: []
    },
    {
      width: '80',
      field: 'status',
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map',
        map: [
          { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
          { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
          { value: 2, text: i18n.t('已接收'), cssClass: 'col-active' },
          { value: 8, text: i18n.t('已完成'), cssClass: 'col-active' },
          { value: 9, text: i18n.t('已取消'), cssClass: 'col-active' }
        ]
      },
      searchOptions: {
        elementType: 'multi-select',
        operator: 'in',
        multiple: true,
        dataSource: [
          { label: i18n.t('新建'), value: 0 },
          { label: i18n.t('待确认'), value: 1 },
          { label: i18n.t('已接收'), value: 2 },
          { label: i18n.t('已完成'), value: 8 },
          { label: i18n.t('已取消'), value: 9 }
        ],
        fields: { text: 'label', value: 'value' }
      },
      cellTools: [
        //   {
        //     id: "submit",
        //     icon: "icon_list_enable",
        //     title: i18n.t("提交"),
        //     // permission: ["O_02_0125"],
        //     visibleCondition: (data) => data["status"] == "0",
        //   },
        {
          id: 'cancel',
          icon: 'icon_list_disable',
          title: i18n.t('取消'),
          // permission: ["O_02_0126"],
          visibleCondition: (data) => data['status'] == 1
        }
      ]
    },
    {
      width: '150',
      field: 'takeNo',
      headerText: i18n.t('车号')
    },
    {
      field: 'vehicleLogistics', // 车辆物流
      headerText: i18n.t('车辆物流'),
      template: () => {
        return {
          template: Vue.component('template-detail', {
            template: `
                <span v-if="data.takeNo" style="color: #6386c1; cursor: pointer;" @click="toLogistics(data)">{{ $t('查看物流') }}</span>`,
            methods: {
              toLogistics(e) {
                const params = {
                  ztpno: e?.deliveryCode?.toString(),
                  busCode: e?.forecastCode,
                  busNum: e.takeNo
                }
                this.$API.receiptAndDelivery.purQueryVehicleLogistics(params).then((res) => {
                  if (res.code === 200) {
                    window.open(res.data.mapURL)
                  }
                })
              }
            }
          })
        }
      }
    },
    {
      width: '260',
      field: 'siteCode',
      headerText: i18n.t('工厂'),
      searchOptions: MasterDataSelect.factorySupplierAddress,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.siteCode, data?.siteName)
      }
    },
    {
      width: '0',
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'vmiWarehouseCode',
      headerText: i18n.t('VMI仓'),
      searchOptions: MasterDataSelect.vmiWarehouseSteel,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.vmiWarehouseCode, data?.vmiWarehouseName)
      }
    },
    {
      width: '0',
      field: 'vmiWarehouseName',
      headerText: i18n.t('VMI仓名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'supplierCode',
      headerText: i18n.t('供应商'),
      searchOptions: MasterDataSelect.supplierSu,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
      }
    },
    {
      width: '0',
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      ignore: true
    },
    {
      width: '170',
      field: 'vmiWarehouseAddress',
      headerText: i18n.t('送货地址')
    },
    {
      width: '80',
      field: 'createUserName',
      headerText: i18n.t('制单人')
    },
    {
      width: '150',
      field: 'createTime',
      headerText: i18n.t('制单时间'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      },
      template: timeDate('createTime', false)
    },
    {
      width: '150',
      field: 'confirmTime',
      headerText: i18n.t('接收日期'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      },
      type: 'date',
      template: timeDate('confirmTime', false)
    }
  ],
  detailedColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '200',
      field: 'vmiOrderCode',
      headerText: i18n.t('VMI入库单号')
    },
    {
      width: '80',
      field: 'status',
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map',
        map: [
          { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
          { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
          { value: 2, text: i18n.t('已接收'), cssClass: 'col-active' },
          { value: 8, text: i18n.t('已完成'), cssClass: 'col-active' },
          { value: 9, text: i18n.t('已取消'), cssClass: 'col-active' }
        ]
      },
      searchOptions: {
        elementType: 'multi-select',
        operator: 'in',
        multiple: true,
        dataSource: [
          { label: i18n.t('新建'), value: 0 },
          { label: i18n.t('待确认'), value: 1 },
          { label: i18n.t('已接收'), value: 2 },
          { label: i18n.t('已完成'), value: 8 },
          { label: i18n.t('已取消'), value: 9 }
        ],
        fields: { text: 'label', value: 'value' }
      }
    },
    {
      width: '70',
      field: 'rowNum',
      headerText: i18n.t('行号')
    },
    {
      width: '130',
      field: 'itemCode', //supplierCode
      headerText: i18n.t('物料编码')
    },
    {
      width: '130',
      field: 'itemName',
      headerText: i18n.t('物料名称')
    },
    {
      width: '85',
      field: 'purchaseGroupName',
      headerText: i18n.t('采购组')
    },
    {
      width: '65',
      field: 'count',
      headerText: i18n.t('卷重')
    },
    {
      width: '150',
      field: 'takeNo',
      headerText: i18n.t('车号')
    },
    {
      field: 'vehicleLogistics', // 车辆物流
      headerText: i18n.t('车辆物流'),
      template: () => {
        return {
          template: Vue.component('template-detail', {
            template: `
                <span v-if="data.takeNo" style="color: #6386c1; cursor: pointer;" @click="toLogistics(data)">{{ $t('查看物流') }}</span>`,
            methods: {
              toLogistics(e) {
                const params = {
                  ztpno: e?.deliveryCode?.toString(),
                  busCode: e?.forecastCode,
                  busNum: e.takeNo
                }
                this.$API.receiptAndDelivery.purQueryVehicleLogistics(params).then((res) => {
                  if (res.code === 200) {
                    window.open(res.data.mapURL)
                  }
                })
              }
            }
          })
        }
      }
    },
    {
      width: '150',
      field: 'batchCode',
      headerText: i18n.t('卷号')
    },
    {
      width: '65',
      field: 'itemUnit',
      headerText: i18n.t('单位')
    },
    {
      width: '65',
      field: 'remarks',
      headerText: i18n.t('备注')
    },
    {
      width: '140',
      field: 'orderCode',
      headerText: i18n.t('关联采购订单号')
    },
    {
      width: '150',
      field: 'lineNo',
      headerText: i18n.t('关联采购订单行号')
    },
    {
      width: '260',
      field: 'siteCode',
      headerText: i18n.t('工厂'),
      searchOptions: MasterDataSelect.factorySupplierAddress,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.siteCode, data?.siteName)
      }
    },
    {
      width: '0',
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'vmiWarehouseCode',
      headerText: i18n.t('VMI仓'),
      searchOptions: MasterDataSelect.vmiWarehouseSteel,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.vmiWarehouseCode, data?.vmiWarehouseName)
      }
    },
    {
      width: '0',
      field: 'vmiWarehouseName',
      headerText: i18n.t('VMI仓名称'),
      ignore: true
    },
    {
      width: '110',
      field: 'batchCode',
      headerText: i18n.t('卷号')
    },
    {
      width: '260',
      field: 'supplierCode',
      headerText: i18n.t('供应商'),
      searchOptions: MasterDataSelect.supplierSu,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
      }
    },
    {
      width: '0',
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      ignore: true
    },
    {
      width: '170',
      field: 'vmiWarehouseAddress',
      headerText: i18n.t('送货地址')
    },
    {
      width: '95',
      field: 'createUserName',
      headerText: i18n.t('制单人')
    },
    {
      width: '150',
      field: 'submitTime',
      headerText: i18n.t('制单时间'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      },
      type: 'date',
      format: 'YYYY-mm-dd HH:MM:SS'
    },
    {
      width: '150',
      field: 'confirmTime',
      headerText: i18n.t('接收日期'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      },
      type: 'date',
      format: 'YYYY-mm-dd HH:MM:SS'
    }
  ],
  createColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '150',
      field: 'orderCode',
      headerText: i18n.t('PO'),
      searchOptions: {
        renameField: 'detail.orderCode'
      }
    },
    {
      width: '150',
      field: 'lineNo',
      headerText: i18n.t('PO行'),
      searchOptions: {
        renameField: 'detail.lineNo'
      }
    },
    {
      width: '260',
      field: 'siteCode',
      headerText: i18n.t('工厂'),
      searchOptions: MasterDataSelect.factorySupplierAddress,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.siteCode, data?.siteName)
      }
    },
    {
      width: '0',
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'supplierCode',
      headerText: i18n.t('供应商'),
      searchOptions: MasterDataSelect.supplierSu,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
      }
    },
    {
      width: '0',
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      ignore: true
    },
    {
      width: '130',
      field: 'itemCode', //supplierCode
      headerText: i18n.t('物料编码')
    },
    {
      width: '130',
      field: 'itemName',
      headerText: i18n.t('物料名称')
    },
    {
      width: '150',
      field: 'varianceRate',
      headerText: i18n.t('容差比例'),
      searchOptions: {
        renameField: 'detail.varianceRate'
      }
    },
    {
      width: '150',
      field: 'quantity',
      headerText: i18n.t('订单行数量'),
      searchOptions: {
        renameField: 'detail.quantity'
      }
    },
    {
      width: '150',
      field: 'availableQuantity',
      headerText: i18n.t('剩余最大可创建数量'),
      type: 'number',
      searchOptions: {
        renameField: 'detail.availableQuantity'
      }
    },
    {
      width: '150',
      field: 'unitName',
      headerText: i18n.t('单位'),
      searchOptions: {
        renameField: 'detail.unitName'
      }
    },
    {
      width: '150',
      field: 'remark',
      headerText: i18n.t('备注')
    }
  ]
}
