// VMI领料管理-供方
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :current-tab="currentTab"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnObj } from './config/index.js'
import { download, getHeadersFileName } from '@/utils/utils'

// import Vue from "vue";
export default {
  components: {
    // addDialog: require("./components/addDialog.vue").default,
  },
  data() {
    return {
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0124' },
          { dataPermission: 'b', permissionCode: 'T_02_0125' }
        ]
      },
      printObj: {
        orderId: '',
        logisticCompanyId: ''
      },
      currentTab: 0, //代表当前默认加载显示的Tab索引
      supplierThirdOptions: [],

      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.print,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          tab: { title: this.$t('头视图') },
          useBaseConfig: false, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          // dataPermission: "a",
          // permissionCode: "T_02_0124",
          toolbar: {
            tools: [[], ['Filter', 'Refresh', 'Setting']]
          },
          grid: {
            columnData: columnObj.headColumn,
            // dataSource: [],
            asyncConfig: {
              // url: "/srm-purchase-execute/tenant/vmi-pickup-order/supplier-page-query",
              url: '/srm-purchase-execute/tenant/vmiSteel/vmi-pickup-order/processor-page-query'
              // defaultRules: [

              // ],
              // recordsPosition: "data",
            },
            frozenColumns: 1
          }
        },
        {
          tab: { title: this.$t('明细视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          // dataPermission: "a",
          // permissionCode: "T_02_0125",
          toolbar: {
            tools: [
              [
                {
                  id: 'download',
                  icon: 'icon_solid_export',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columnObj.detailedColumn,
            // dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiSteel/vmi-pickup-order/processor-item-page-query'
              // recordsPosition: "data",
            },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },
  mounted() {
    if (this.$route.query.from === 'mytodo') {
      // this.currentTab = 1;
      this.pageConfig[0].grid.asyncConfig.defaultRules = JSON.parse(
        sessionStorage.getItem('todoDetail')
      ).defaultRules
    }
    let obj = {
      page: {
        current: 1,
        size: 100
      }
    }
    this.$API.supplierCoordination.purNewOrderSave(obj).then((res) => {
      // let data = res.data.filter((item) => item.supplierName);
      this.supplierThirdOptions = res.data.records

      // this.$nextTick(() => {
      //   if (e.updateData && typeof e.updateData == "function") {
      //     e.updateData(data);
      //   }
      // });
    })
  },

  methods: {
    hide() {
      this.$refs.toast.ejsRef.hide()
    },
    onOpen: function (args) {
      args.preventFocus = true
    },
    // 跳转详情
    handleClickCellTitle(e) {
      let obj = {
        id: ''
      }
      if (e.field === 'vmiOrderCode') {
        obj.id = e.data.vmiOrderId ?? e.data.id
        this.redirectPage('purchase-execute/new-supplier-picking-details', obj)
      }
    },

    // 头部跳转
    handleClickToolBar(e) {
      console.log(e)
      let _selectRows = e.grid.getSelectedRecords()
      // let ids = _selectRows.map((item) => item.id);
      if (e.toolbar.id === 'download') {
        // 导出
        this.downLoadExport()
      }
      if (e.toolbar.id === 'confirm') {
        // VMI领料单-供方-批量确认
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('确认'),
            message: this.$t('确定确认吗？')
          },
          success: () => {
            // TODO: 领料-供方-确认（列表-批量确认）
            // debugger
            let ids = _selectRows.map((item) => item.id)
            // 只有在待确认的情况下才可选择
            let switAccept = true
            _selectRows.map((item) => {
              if (item.status != 1) {
                switAccept = false
              }
            })
            if (switAccept == false) {
              this.$toast({
                content: this.$t('请选择待确认状态'),
                type: 'warning'
              })
              return
            }
            this.$API.supplierCoordination
              .purNewOrderQueryBatchConfirm({ ids: ids })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('确认成功'),
                    type: 'success'
                  })
                  // 刷新当前 Grid
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
          }
        })
      } else if (e.toolbar.id == 'supplierRrturn') {
        // VMI领料单-供方-批量退回
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('退回'),
            message: this.$t('确定退回吗？')
          },
          success: () => {
            // TODO: 领料-供方-退回（列表-批量退回）
            // debugger
            let ids = _selectRows.map((item) => item.id)
            // 只有在待确认的情况下才可选择
            let switAccept = true
            _selectRows.map((item) => {
              if (item.status != 1) {
                switAccept = false
              }
            })
            if (switAccept == false) {
              this.$toast({
                content: this.$t('请选择待确认状态'),
                type: 'warning'
              })
              return
            }
            this.$API.supplierCoordination.purNewOrderQueryBatchReject({ ids: ids }).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('退回成功'),
                  type: 'success'
                })
                // 刷新当前 Grid
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          }
        })
      } else if (e.toolbar.id === 'establish') {
        // 创建送货单
        // this.redirectPage("purchase-execute/VMIpickingListCreate", obj);
        this.submitAsn()
      } else if (e.toolbar.id === 'print') {
        // this.printObj.orderId = ids;
        this.$refs.toast.ejsRef.show()
      } else {
        // 调用刷新方法
      }
    },

    handleClickCellTool(e) {
      if (e.tool.id == 'confirmss') {
        this.$dialog({
          data: {
            title: this.$t('确认'),
            message: this.$t('确认确认吗？')
          },
          success: () => {
            // TODO: 领料-供方-确认（列表-确认）
            this.$API.supplierCoordination
              .purNewOrderQueryConfirm({ id: e.data.id })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('提交成功'),
                    type: 'success'
                  })
                  // 刷新当前 Grid
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
          }
        })
      } else if (e.tool.id == 'cancel') {
        this.$dialog({
          data: {
            title: this.$t('退回'),
            message: this.$t('确认退回吗？')
          },
          success: () => {
            // TODO: 领料-供方-退回（列表-退回）
            // debugger
            this.$API.supplierCoordination
              .purNewOrderQueryBatchReject({ ids: [e.data.id] })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('退回成功'),
                    type: 'success'
                  })
                  // 刷新当前 Grid
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
          }
        })
      }
    },
    downLoadExport() {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      console.log(queryBuilderRules)
      const params = {
        page: { current: 1, size: 1000 },
        ...queryBuilderRules
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.thirdPartyVMICollaboration.postVmiSteelPickupExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },
    submitAsn() {
      const selectedRowData = this.$refs.templateRef.getCurrentTabRef().grid.getSelectedRecords()
      const data = {
        itemList: []
      }
      let obj = null
      selectedRowData.forEach((item) => {
        obj = {}
        obj.count = item.checkCount
        obj.id = item.id
        obj.lineNo = item.lineNo
        obj.orderCode = item.orderCode
        data.itemList.push(obj)
      })
      this.$API.thirdPartyVMICollaboration.createNewAsn(data).then((res) => {
        if (res.code === 200) {
          localStorage.setItem('asnList', JSON.stringify(res.data))
          this.redirectPage('purchase-execute/VMIpickingListNewCreate')
        }
      })
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('todoDetail')
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
