// 领料管理，创建领料单详情页 // 供方 VMI供应商确认
<template>
  <div class="full-height pt20">
    <top-info
      class="flex-keep"
      :header-info="formObject"
      @goBack="goBack"
      @acceptBtn="acceptBtn"
      @endpointBtn="endpointBtn"
    ></top-info>
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
    <mt-dialog ref="toast" :header="$t('提示')" :buttons="buttons" :open="onOpen" size="small">
      <mt-form ref="ruleForm" :model="printObj" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="logisticCompanyId" :label="$t('物流公司')">
          <mt-select
            v-model="printObj.logisticCompanyId"
            :data-source="supplierThirdOptions"
            :show-clear-button="false"
            :allow-filtering="true"
            @change="thirdPartyLogisticsCodeChange"
            :filtering="getThirdParty"
            :fields="{ text: 'logisticsCompanyName', value: 'id' }"
            :placeholder="$t('请选择物流公司')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { columnData } from './config/pickingDetails.js'
import TopInfo from './components/topInfo.vue'
import Vue from 'vue'
export default {
  components: {
    TopInfo
  },
  data() {
    return {
      // headerInfo: {
      //   status: 1,
      //   createUserName: "jcs",
      //   createTime: "2022-03-02",
      //   factory: this.$t("京东工厂"),
      //   supplierCode: "0001",
      //   companyDescribe: this.$t("描述"),
      //   warehouseCode: "0033333",
      //   warehouseDescribe: this.$t("描述"),
      //   remark: this.$t("以上备注信息全是bian"),
      //   accept: true,
      //   endpoint: true,
      // },
      printObj: {
        orderId: '',
        logisticCompanyId: '',
        orderItemIdList: []
      },
      rules: {
        logisticCompanyId: [
          {
            required: true,
            message: this.$t('请选择物料公司'),
            trigger: 'blur'
          }
        ]
      },
      formObject: {
        status: 1, // status状态：0新建（待提交） 1已提交/待确认 2已接收/待质检 8已完成 9已取消
        vmiOrderCode: 'w20211227001', // VMI领料单号
        createUserName: 'jcs', // 制单人
        createTime: '2022-03-02', // 制单时间
        siteCode: '', // 工厂编码
        siteName: '',
        supplierCode: '', // 原材料供应商编码
        vmiWarehouseCode: '', // VMI仓编码
        vmiWarehouseName: '',
        processorCode: '', // 领料供应商编码（加工商）
        processorName: '', // 领料供应商名称（加工商）
        vmiWarehouseAddress: '', // 送货地址
        remark: '', // 备注
        itemList: [], // 创建VMI领料单请求明细参数
        statusCreate: false //判断是不是编辑页面
      },
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.handleConfirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      supplierThirdOptions: [],
      pageConfig: [
        {
          title: this.$t('汇总列表'),
          useToolTemplate: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'print',
                  icon: 'icon_table_print',
                  title: this.$t('打印')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columnData,
            // lineIndex: 1,
            // autoWidthColumns: columnData.length + 1,
            dataSource: [],
            // asyncConfig: {
            //   url: "/srm-purchase-execute/tenant/pe/business/configs",
            //   recordsPosition: "data",
            // },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },
  created() {
    if (this.$route.query.details === 'true') {
      this.headerInfo.accept = false
      this.headerInfo.endpoint = false
    }
  },
  mounted() {
    this.getFormDetail(this.$route.query.id)
    this.printObj.orderId = this.$route.query.id
    let obj = {
      page: {
        current: 1,
        size: 100
      }
    }
    this.$API.supplierCoordination.purNewOrderSave(obj).then((res) => {
      // let data = res.data.filter((item) => item.supplierName);
      this.supplierThirdOptions = res.data.records

      // this.$nextTick(() => {
      //   if (e.updateData && typeof e.updateData == "function") {
      //     e.updateData(data);
      //   }
      // });
    })
  },
  methods: {
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    //详情==接口
    getFormDetail(id) {
      this.$API.purchaseCoordination
        .VMINewPickupOrderDetailQuery({
          id: id
        })
        .then((res) => {
          this.formObject = { ...res.data }
          this.formObject.itemList = JSON.parse(
            JSON.stringify(this.formObject.vmiOrderItemResponses)
          )
          delete this.formObject.vmiOrderItemResponses
          this.pageConfig[0].grid.dataSource = res.data.vmiOrderItemResponses
          this.isInfo = true
          // status状态：0新建（待提交） 1已提交/待确认 2已接收/待质检 8已完成 9已取消
          if (this.formObject.status == 0) {
            // 采方-状态是"新建（待提交）"
            // 渲染顶部和操作按钮toolbar
            this.renderUI(this.formObject.status)
          } else if (this.formObject.status == 2) {
            // this.pageConfig = pickingDetailsPageConfig(this.formObject.itemList)
          } else {
            // 渲染顶部和操作按钮toolbar
            this.renderUI(this.formObject.status)
          }
        })
        .catch(() => {
          // 渲染顶部和操作按钮toolbar
          // this.renderUI(0);
        })
    },
    hide() {
      this.$refs.toast.ejsRef.hide()
    },
    onOpen: function (args) {
      args.preventFocus = true
    },
    // 供方确认
    acceptBtn() {
      this.$dialog({
        data: {
          title: this.$t('供方确认'),
          message: this.$t('是否确认？')
        },
        success: () => {
          let params = {
            id: this.$route.query.id
          }
          this.$API.supplierCoordination.purOrderNewQueryConfirm(params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('确认成功'), type: 'success' })
              this.$router.go(-1)
              // 刷新当前 Grid
              // this.$refs.templateRef.refreshCurrentGridData();
            }
          })
          // this.redirectPage("purchase-execute/supplier-picking-print",{});
        }
      })
    },
    // 供方退回
    endpointBtn() {
      this.$dialog({
        data: {
          title: this.$t('供方退回'),
          message: this.$t('是否确认退回？')
        },
        success: () => {
          let params = {
            ids: [this.$route.query.id]
          }
          this.$API.supplierCoordination.purOrderNewQueryBatchReject(params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('退回成功'), type: 'success' })
              this.$router.go(-1)
              // 刷新当前 Grid
              // this.$refs.templateRef.refreshCurrentGridData();
            }
          })
          // this.redirectPage("purchase-execute/supplier-picking-print",{});
        }
      })
    },

    handleConfirm() {
      this.$refs.toast.ejsRef.hide()

      //打印排期列表
      let row = this.printObj
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认打印?')
        },
        success: () => {
          this.$API.supplierCoordination.postReceivePrint(row).then((res) => {
            if (res?.data?.type === 'application/json') {
              const reader = new FileReader()
              reader.readAsText(res?.data, 'utf-8')
              reader.onload = function () {
                console.log('======', reader)
                const readerRes = reader.result
                const resObj = JSON.parse(readerRes)
                Vue.prototype.$toast({
                  content: resObj.msg,
                  type: 'error'
                })
              }
              return
            }
            const content = res.data
            this.pdfUrl = window.URL.createObjectURL(
              new Blob([content], { type: 'application/pdf' })
            )
            // window.open(this.pdfUrl);
            let date = new Date().getTime()
            let ifr = document.createElement('iframe')
            ifr.style.frameborder = 'no'
            ifr.style.display = 'none'
            ifr.style.pageBreakBefore = 'always'
            ifr.setAttribute('id', 'printPdf' + date)
            ifr.setAttribute('name', 'printPdf' + date)
            ifr.src = this.pdfUrl
            document.body.appendChild(ifr)
            this.doPrint('printPdf' + date)
            window.URL.revokeObjectURL(ifr.src)
          })
        }
      })
    },
    // 获取dom打印方法 异步执行
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        this.pdfLoading = false
      }, 100)
    },
    // 头部操作
    handleClickToolBar(e) {
      let _id = []
      e.gridRef.getMtechGridRecords().forEach((item) => {
        _id.push(item.id)
      })
      console.log(_id)
      this.printObj.orderItemIdList = _id
      // let _selectRows = e.grid.getSelectedRecords();
      if (e.toolbar.id === 'print') {
        // this.printObj.orderId = ids;
        this.$refs.toast.ejsRef.show()
      }
      //   if (e.grid.getSelectedRecords().length <= 0) {
      //     this.$toast({ content: this.$t("请先选择一行"), type: "warning" });
      //     return;
      //   } else {
      //     // 调用导出方法·
      //     let _selectRows = e.grid.getSelectedRecords();
      //     console.log(_selectRows, 7777);
      //     debugger;
      //   }
    },
    // 跳转方法
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
.titleColor {
  color: #00469c;
}
</style>
