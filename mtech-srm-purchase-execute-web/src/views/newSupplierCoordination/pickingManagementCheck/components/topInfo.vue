<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- {{headerInfo.status}} -->
    <!-- {{businessTypeList}} -->
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div :class="[statusToClass(headerInfo.status), 'mr20']">
        {{ headerInfo.status | statusFormat }}
      </div>
      <div class="infos mr20">{{ $t('创建人：') }}{{ headerInfo.createUserName }}</div>
      <div class="infos">
        {{ $t('创建时间：') }}{{ headerInfo.createTime | dateFormat }}
        {{ headerInfo.createTime | timeFormat }}
      </div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>

      <mt-button
        css-class="e-flat"
        v-show="headerInfo.status == 1"
        :is-primary="true"
        v-permission="['O_02_1267']"
        @click="acceptBtn"
        >{{ $t('供方确认') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        v-if="headerInfo.status == 1"
        v-permission="['O_02_1268']"
        :is-primary="true"
        @click="endpointBtn"
        >{{ $t('供方退回') }}</mt-button
      >
      <div class="sort-box" @click="doExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom mt20">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item class="two-thirds-full-width" prop="siteCode" :label="$t('工厂')">
          <mt-input
            v-model="headerInfo.siteCode"
            :disabled="true"
            :placeholder="$t('工厂')"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="siteCode" :label="$t('工厂')">
          <mt-select
            ref="factoryRef"
            v-model="headerInfo.siteCode"
            :dataSource="factoryOptions"
            :disabled="true"
            :showClearButton="true"
            :allowFiltering="true"
            :filtering="getFactoryOptions"
            :fields="{ text: 'labelShow', value: 'siteCode' }"
            :placeholder="$t('工厂')"
            @change="handleFactoryChange"
          ></mt-select>
        </mt-form-item> -->
        <mt-form-item class="two-thirds-full-width" prop="supplierCode" :label="$t('原材料供应商')">
          <mt-input
            v-model="headerInfo.supplierCode"
            :disabled="true"
            :placeholder="$t('原材料供应商')"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="supplierCode" :label="$t('原材料供应商')">
          <mt-select
            ref="supplierCodeRef"
            v-model="headerInfo.supplierCode"
            :dataSource="supplierOptions"
            :disabled="true"
            :showClearButton="true"
            :allowFiltering="true"
            :filtering="getSupplierOptions"
            :fields="{ text: 'labelShow', value: 'supplierCode' }"
            :placeholder="$t('请选择')"
            @change="handleSupplierChange"
          ></mt-select>
        </mt-form-item> -->
        <mt-form-item class="two-thirds-full-width" prop="vmiWarehouseCode" :label="$t('VMI仓')">
          <mt-input
            v-model="headerInfo.vmiWarehouseCode"
            :disabled="true"
            :placeholder="$t('VMI仓')"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="vmiWarehouseCode" :label="$t('VMI仓')">
          <mt-select
            ref="vmiWarehouseCodeRef"
            v-model="headerInfo.vmiWarehouseCode"
            :dataSource="warehouseOptions"
            :disabled="true"
            :showClearButton="true"
            :allowFiltering="true"
            :filtering="getVMIOptions"
            :fields="{ text: 'labelShow', value: 'vmiWarehouseCode' }"
            :placeholder="$t('请选择')"
            @open="handleWarehouseOpen"
            @change="handleWarehouseChange"
          ></mt-select>
        </mt-form-item> -->
        <mt-form-item class="two-thirds-full-width" prop="processorCode" :label="$t('领料供应商')">
          <mt-input
            v-model="headerInfo.processorCode"
            :disabled="true"
            :placeholder="$t('领料供应商')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('送货地址')">
          <mt-input v-model="headerInfo.vmiWarehouseAddress" disabled></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="processorCode" :label="$t('领料供应商')">
          <mt-select
            v-model="headerInfo.processorCode"
            :dataSource="processorOptions"
            :disabled="true"
            :showClearButton="true"
            :allowFiltering="true"
            :filtering="getProcessorOptions"
            :fields="{ text: 'labelShow', value: 'processorCode' }"
            :placeholder="$t('请选择')"
            @change="handleProcessorChange"
          ></mt-select>
        </mt-form-item> -->
        <!-- <mt-form-item class="two-thirds-full-width" prop="vmiWarehouseAddress" :label="$t('送货地址')">
          <mt-input
            v-model="headerInfo.vmiWarehouseAddress"
            :disabled="true"
            :placeholder="$t('请输入')"
          ></mt-input>
        </mt-form-item> -->
        <mt-form-item class="full-width" prop="remark" :label="$t('备注')" :show-message="false">
          <mt-input
            v-model="headerInfo.remark"
            :disabled="true"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { StatusText, StatusCssClass, DeliveryTypeOptions } from '../config/constant.js'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      DeliveryTypeOptions,
      isExpand: true,
      rules: {
        shippingAddress: [
          {
            required: true,
            message: this.$t('请选择送货地址'),
            trigger: 'blur'
          }
        ]
      },
      businessTypeList: [],
      rawList: [],
      warehouseList: [],
      addressList: []
    }
  },
  mounted() {},
  filters: {
    dateFormat(value) {
      let str = ''
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }

      return str
    },
    timeFormat(value) {
      let str = ''
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'HH:MM:SS', value })
      } else {
        str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
      }
      return str
    },
    statusFormat(value) {
      if (!StatusText[value]) {
        return value
      } else {
        return StatusText[value]
      }
    }
  },
  methods: {
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    },
    // 供方确认
    acceptBtn() {
      this.$emit('acceptBtn')
    },
    // 供方退回
    endpointBtn() {
      this.$emit('endpointBtn')
    },
    // 状态 转对应的 css class
    statusToClass(value) {
      let cssClass = ''
      if (StatusCssClass[value]) {
        cssClass = StatusCssClass[value]
      }
      return cssClass
    },
    handleBusinessChange() {
      debugger
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;
      .full-width {
        width: calc(100% - 10px) !important;
      }
    }
  }
}
</style>
