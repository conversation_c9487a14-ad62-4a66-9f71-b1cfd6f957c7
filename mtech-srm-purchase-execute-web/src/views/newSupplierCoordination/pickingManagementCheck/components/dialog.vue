<template>
  <mt-dialog
    size="small"
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <mt-form-item :label="$t('物流公司')" prop="company">
        <mt-select
          v-model="ruleForm.company"
          :data-source="companyList"
          :show-clear-button="false"
          :fields="{ text: 'itemName', value: 'id' }"
          :placeholder="$t('请选择物流公司名称')"
          @change="handleBusinessChange"
        ></mt-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  props: {
    ruleForm: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: this.$t('VMI领料单'),
      rules: {
        // 公司
        company: [{ required: true, message: this.$t('请输入公司'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      companyList: [
        {
          itemName: this.$t('京东'),
          id: 1
        },
        {
          itemName: this.$t('阿里'),
          id: 2
        }
      ]
    }
  },
  async mounted() {},
  methods: {
    initDialog() {
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      let _this = this
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          _this.$emit('dilogData', this.ruleForm)
          this.$refs.dialog.ejsRef.hide()
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    handleBusinessChange(e) {
      console.log(e)
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .display-block {
  width: 200px;
  height: 200px;
}
/deep/ .create-proj-dialog {
  width: 200px;
  height: 200px;
}
</style>
