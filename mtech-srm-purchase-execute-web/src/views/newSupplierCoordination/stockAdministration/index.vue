// VMI库存调整管理
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config/index.js'
export default {
  components: {},
  data() {
    return {
      pageConfig: [
        {
          title: this.$t('汇总列表'),
          useToolTemplate: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'create',
                  icon: 'icon_solid_Createorder',
                  permission: ['O_02_1094'],
                  title: this.$t('供应商库存导入')
                }
              ],
              [
                'Filter',
                {
                  id: 'export',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                },
                'Refresh',
                'Setting'
              ]
            ]
          },
          grid: {
            columnData: columnData,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiAllocationOrder/supplier-page-query',
              recordsPosition: 'data.records'
            },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    // 跳转详情
    handleClickCellTitle(e) {
      if (e.field === 'vmiOrderCode') {
        let obj = {
          details: '',
          orderStatus: e.data.vmiOrderType,
          id: e.data.id
        }
        if (e.data.status == 0 || e.data.status == 1) {
          obj.details = 0 //非详情
        } else {
          obj.details = 1 //详情
        }
        // 根据类型判断   类型字段暂时没有返回   先用状态代替（自定义代替）
        if (e.data.vmiOrderType === 4) {
          obj.details = 1
          // 跳入导入
          this.redirectPage('purchase-execute/supplier-stock-build', {})
        } else if (e.data.vmiOrderType === 6) {
          // 跳入物料替换
          this.redirectPage('purchase-execute/supplier-stock-replace', obj)
        } else if (e.data.vmiOrderType === 5) {
          // 跳入物料调拨
          this.redirectPage('purchase-execute/supplier-stock-allocation', obj)
        }
      }
    },
    // 头部操作
    handleClickToolBar(e) {
      if (e.toolbar.id === 'export') {
        // 供应商导出
        let obj = {
          page: { current: 1, size: 0 }
        }
        this.$API.supplierCoordination.supplierStockExport(obj).then(() => {})
      } else if (e.toolbar.id === 'create') {
        let obj = {
          orderStatus: 4,
          details: 0
        }
        // 供应商导入
        this.redirectPage('purchase-execute/supplier-stock-build', obj)
      } else if (e.toolbar.id === 'refreshDataByLocal') {
        // 刷新
      } else if (e.grid.getSelectedRecords().length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
    },
    // 行内操作
    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.$dialog({
          data: {
            title: this.$t('删除'),
            message: this.$t('确认删除吗？')
          },
          success: () => {
            this.delete(e.data.id)
          }
        })
      } else if (e.tool.id == 'accept') {
        this.$dialog({
          data: {
            title: this.$t('接受'),
            message: this.$t('确认接受吗？')
          },
          success: () => {
            this.accept(e.data.id, e.data.vmiOrderType)
          }
        })
      } else if (e.tool.id == 'cancel') {
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消吗？')
          },
          success: () => {
            this.cancel(e.data.id, e.data.vmiOrderType)
          }
        })
      } else if (e.tool.id == 'submit') {
        this.$dialog({
          data: {
            title: this.$t('提交'),
            message: this.$t('确认提交吗？')
          },
          success: () => {
            this.submit(e.data.id, e.data.vmiOrderType)
          }
        })
      }
    },
    // 刷新页面
    // redirectPage(route, param) {
    //   this.$router.push({ path: `/${route}`, query: param })
    // },
    // 接收 采方，供方公用一个接口
    accept(data, valType) {
      let apiInterface = ''
      if (valType == 5) {
        apiInterface = 'purchaseAllocationReceive'
      } else if (valType == 6) {
        apiInterface = 'purchaseReplaceReceive'
      }
      let obj = {
        ids: []
      }
      obj.ids.push(data)
      this.$API.purchaseCoordination[apiInterface](obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('提交失败'), type: 'error' })
        }
      })
    },
    // 取消 三方共用一个接口
    cancel(data, valType) {
      let apiInterface = ''
      if (valType == 5) {
        apiInterface = 'purchaseAllocationBackOff'
      } else if (valType == 6) {
        apiInterface = 'purchaseReplaceBackOff'
      }
      let obj = {
        ids: []
      }
      obj.ids.push(data)
      this.$API.purchaseCoordination[apiInterface](obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('取消成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('取消失败'), type: 'error' })
        }
      })
    },
    // 删除
    delete(data) {
      let obj = {
        ids: []
      }
      obj.ids.push(data)
      this.$API.supplierCoordination.postSupplierStockImportDelete(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('删除失败'), type: 'error' })
        }
      })
    },
    // 提交
    submit(data) {
      let obj = {
        ids: []
      }
      obj.ids.push(data)
      this.$API.supplierCoordination.postSupplierStockImportSubmit(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('提交失败'), type: 'error' })
        }
      })
    },
    // 跳转方法
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },
    // 刷新页面
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
