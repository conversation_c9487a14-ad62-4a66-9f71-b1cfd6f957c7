<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div
        :class="[statusToClass(headerInfo.status), 'mr20']"
        v-if="isDetails || $route.query.orderStatus != 4"
      >
        {{ headerInfo.status | statusFormat }}
      </div>
      <div class="infos mr20" v-if="isDetails || $route.query.orderStatus != 4">
        {{ $t('创建人：') }}{{ headerInfo.createUserName }}
      </div>
      <div class="infos" v-if="isDetails || $route.query.orderStatus != 4">
        {{ $t('创建时间：') }}{{ headerInfo.createTime | dateFormat }}
        {{ headerInfo.createTime | timeFormat }}
      </div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button
        css-class="e-flat"
        v-if="!isDetails && !isBtnType"
        :is-primary="true"
        @click="acceptBtn"
        >{{ $t('供方接收') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        v-if="!isDetails && !isBtnType"
        :is-primary="true"
        @click="backBtn"
        >{{ $t('采方退回') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        v-if="isBtnType && !isDetails"
        :is-primary="true"
        @click="preservationBtn"
        >{{ $t('保存') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        v-if="isBtnType && !isDetails"
        :is-primary="true"
        @click="submitBtn"
        >{{ $t('提交') }}</mt-button
      >
      <div class="sort-box" @click="doExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="siteCode" :label="$t('工厂')">
          <mt-select
            v-model="headerInfo.siteCode"
            :disabled="headerInfo.isDisabled"
            :placeholder="$t('请选择工厂')"
            :data-source="siteSelect"
            @change="siteChange"
            :allow-filtering="true"
            :filtering="
              $route.path === '/purchase-execute/supplier-stock-build' ? getBuildSite : getDatalist
            "
            @focus="focusSite('site')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('供应商')">
          <mt-input
            v-model="headerInfo.supplierCode"
            :disabled="true"
            :placeholder="$t('请选择供应商')"
            @change="supplierChange"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="vmiWarehouseCode" :label="$t('VMI仓')">
          <mt-select
            v-model="headerInfo.vmiWarehouseCode"
            :disabled="headerInfo.isDisabled"
            :placeholder="$t('请选择VMI仓')"
            :data-source="warehouseSelect"
            :allow-filtering="true"
            :filtering="
              $route.path === '/purchase-execute/supplier-stock-build' ? getBuildWare : getDatalist
            "
            @change="warehouseChange"
            @focus="focusWarehouse('warehouse')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="full-width" prop="remark" :label="$t('备注')" :show-message="false">
          <mt-input
            v-model="headerInfo.remark"
            :disabled="headerInfo.isDisabled"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { StatusText, StatusCssClass, DeliveryTypeOptions } from '../config/constant.js'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { utils } from '@mtech-common/utils'
export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    headerInfo: function (newVal) {
      if (newVal.isDisabled) {
        const { siteName, siteCode } = newVal
        this.siteSelect = [
          {
            text: siteCode,
            value: siteCode,
            label: siteName
          }
        ]
        console.log(this.siteSelect, 'qqw')
      }
    }
  },
  data() {
    return {
      DeliveryTypeOptions,
      isExpand: true,
      rules: {
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        vmiWarehouseCode: [{ required: true, message: this.$t('请选择VMI仓'), trigger: 'blur' }]
      },
      siteSelect: [],
      warehouseSelect: [],
      supplierSelect: [],
      isDetails: null,
      isBtnType: null,
      requestUrl: {
        pre: 'supplierCoordination',
        url: 'postWarehouse'
      },
      parameterValue: '',
      dataLimit: 20,
      labelShowObj: {},
      customerEnterpriseId: ''
    }
  },
  created() {
    if (this.$route.query.details == 0) {
      this.isDetails = false
      if (this.$route.path === '/purchase-execute/supplier-stock-build') {
        this.getBuildSelect()
      } else {
        this.getSelect('site', this.headerInfo.supplierCode.split('-')[0], 2)
      }

      this.headerInfo.isDisabled = false
    } else {
      this.getSelect('site', false, 1)
      this.isDetails = true
      if (this.$route.query.orderStatus != 4) this.headerInfo.isDisabled = true
    }
    if (this.$route.query.orderStatus == 4) {
      this.isBtnType = true
    } else {
      this.isBtnType = false
    }

    // this.getSelect("supplier", false);
    this.getDatalist = utils.debounce(this.getDatalist, 300)
  },
  filters: {
    dateFormat(value) {
      let str = ''
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }

      return str
    },
    timeFormat(value) {
      let str = ''
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'HH:MM:SS', value })
      } else {
        str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
      }

      return str
    },
    statusFormat(value) {
      if (!StatusText[value]) {
        return value
      } else {
        return StatusText[value]
      }
    }
  },
  methods: {
    // 校验
    checkForm() {
      let validStatus = false
      this.$refs.ruleForm.validate((valid) => {
        validStatus = valid
      })
      return validStatus
    },
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    },
    // 接受
    acceptBtn() {
      this.$emit('acceptBtn')
    },
    // 确认
    backBtn() {
      this.$emit('backBtn')
    },
    // 提交
    submitBtn() {
      this.$emit('submitBtn')
    },
    // 保存
    preservationBtn() {
      this.$emit('preservationBtn')
    },
    // 状态 转对应的 css class
    statusToClass(value) {
      let cssClass = ''
      if (StatusCssClass[value]) {
        cssClass = StatusCssClass[value]
      }
      return cssClass
    },
    // 工厂切换时
    siteChange(val) {
      // this.headerInfo.siteCode = val.itemData.text;
      if (this.$route.path !== '/purchase-execute/supplier-stock-build') {
        this.getSelect('warehouse', val.itemData.value.split('-')[0], 3)
      }
    },
    // 供应商切换
    supplierChange(val) {
      this.headerInfo.supplierCode = val
    },
    // vmi仓切换 以companyCode调用客户接口   以客户接口中的 organizationId  组织id去查询物料编码
    warehouseChange(val) {
      this.getCustomer(val.itemData)
    },
    getBuildSelect() {
      this.$API.masterData.postBuyerCriteriaQuery().then((res) => {
        this.customerEnterpriseId = res.data[0].customerEnterpriseId
        this.getBuildSite()
      })

      this.getBuildWare()
    },
    getBuildSite(e) {
      this.$API.masterData
        .getSourceAvailableView({
          buyerEnterpriseId: this.customerEnterpriseId,
          fuzzyParam: e?.text ?? ''
        })
        .then((res) => {
          this.siteSelect = res.data.reduce((pre, now) => {
            pre.push({
              text: now.siteOrgCode + '-' + now.siteOrgName,
              value: now.siteOrgCode + '-' + now.siteOrgName,
              label: now.siteOrgName
            })
            return pre
          }, [])
        })
    },
    getBuildWare(e) {
      this.$API.supplierCoordination
        .postSupplierPageQuery({
          page: {
            current: 1,
            size: 100
          },
          defaultRules: [
            {
              field: 'vmiWarehouseName',
              operator: 'contains',
              value: e?.text ?? ''
            }
          ]
        })
        .then((res) => {
          this.warehouseSelect = res.data.records.reduce((pre, now) => {
            pre.push({
              text: now.vmiWarehouseCode + '-' + now.vmiWarehouseName,
              value: now.vmiWarehouseCode + '-' + now.vmiWarehouseName,
              label: now.vmiWarehouseName,
              companyCode: now.companyCode,
              buyerTenantId: now.buyerTenantId
            })
            return pre
          }, [])

          const obj = res.data.records[0]

          if (obj) {
            const { companyCode, companyName } = obj
            this.headerInfo.supplierCode = `${companyCode}-${companyName}`
          }
        })
    },
    // 收货单头上的工厂等那些信息的筛选按照林健说的逻辑稍微改下，接口还是之前的接口，我加了个参数：resultType，可选值为：site(工厂)/warehouse(vmi仓库)/supplier(供应商);你需要查工厂的时候就传site，需要查仓库就传warehouse，需要查供应商就传supplier；查VMI仓要根据工厂过滤就将选择的工厂编码作为查询VMI仓的条件一起传进去
    // 查询出vmi仓接口
    getSelect(data, val, valNumber) {
      console.log(this.headerInfo, val, 'zaq')
      let obj = {
        resultType: data
      }
      if (val !== false && valNumber == 3) {
        obj.siteCode = val
        obj.supplierCode = this.headerInfo.supplierCode.split('-')[0]
      } else if (val !== false && valNumber == 2) {
        obj.supplierCode = val
      }
      this.$API.supplierCoordination.postWarehouse(obj).then((res) => {
        if (res.code === 200 && res.data.length > 0) {
          if (data === 'site') {
            // 工厂
            this.siteSelect = res.data.reduce((pre, now) => {
              pre.push({
                text: now.siteCode + '-' + now.siteName,
                value: now.siteCode + '-' + now.siteName,
                label: now.siteName
              })
              return pre
            }, [])

            console.log(this.siteSelect, 'wers')
          } else if (data === 'supplier' && !val) {
            // 供应商
            this.supplierSelect = res.data.reduce((pre, now) => {
              pre.push({
                text: now.supplierCode + '-' + now.supplierName,
                value: now.supplierCode,
                label: now.supplierName
              })
              return pre
            }, [])
          } else {
            // 仓库
            this.warehouseSelect = res.data.reduce((pre, now) => {
              pre.push({
                text: now.vmiWarehouseCode + '-' + now.vmiWarehouseName,
                value: now.vmiWarehouseCode + '-' + now.vmiWarehouseName,
                label: now.vmiWarehouseName,
                companyCode: now.companyCode,
                buyerTenantId: now.buyerTenantId
              })
              return pre
            }, [])
          }
        }
      })
    },
    // 获取焦点时
    focusSite(e) {
      this.parameterValue = e
      this.labelShowObj = {
        code: 'siteCode',
        name: 'siteName'
      }
    },
    focusWarehouse(e) {
      this.parameterValue = e
    },
    // 工厂，供应商，vmi仓，搜索    jcs
    getDatalist(e = { text: '' }) {
      let than = this
      // dataLimit 限制返回条数
      this.$API[this.requestUrl.pre]
        [this.requestUrl.url]({
          resultType: this.parameterValue,
          keyword: e.text
        })
        .then((res) => {
          let dataList = []
          if (than.parameterValue === 'site') {
            dataList = res.data.reduce((pre, now) => {
              pre.push({
                text: now.siteCode + '-' + now.siteName,
                value: now.siteCode + '-' + now.siteName,
                ...pre
              })
              return pre
            }, [])
          } else if (than.parameterValue === 'warehouse') {
            dataList = res.data.reduce((pre, now) => {
              pre.push({
                text: now.vmiWarehouseCode + '-' + now.vmiWarehouseName,
                value: now.vmiWarehouseCode + '-' + now.vmiWarehouseName,
                ...pre
              })
              return pre
            }, [])
          }
          this.$nextTick(() => {
            if (e.updateData && typeof e.updateData == 'function') {
              e.updateData(dataList)
            }
          })

          if (res.total > this.dataLimit) {
            this.$toast({
              content: this.$t('搜索结果较多，请再输入更精确的查询条件'),
              type: 'warning'
            })
          }

          this.selectVal = this.initVal
        })
    },
    // 调用客户接口以客户接口中的 customerEnterpriseId  客户企业id去查询物料编码
    getCustomer(param) {
      let obj = {
        tenantId: param.buyerTenantId,
        siteCode: this.headerInfo.siteCode.split('-')[0]
      }
      this.$API.masterData.getEnterpriseId(obj).then((res) => {
        if (res.code === 200) {
          this.headerInfo.customerEnterpriseId = res.data
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(33% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;
      .full-width {
        width: calc(100% - 10px) !important;
      }
    }
  }
}
</style>
