// 供方-库存调整新建
<template>
  <div class="home">
    <top-info
      class="flex-keep"
      :header-info="headerInfo"
      @goBack="goBack"
      @submitBtn="submitBtn"
      @preservationBtn="preservationBtn"
      ref="infoRules"
    ></top-info>
    <mt-template-page :template-config="componentConfig">
      <div slot="slot-0" :style="{ padding: '30px' }">
        <mt-data-grid
          id="Grid1"
          class="pe-edit-grid edit-grid"
          :data-source="dataSource"
          :column-data="columnData"
          ref="dataGrid"
          :allow-paging="allowPaging"
          :edit-settings="editSettings"
          @actionBegin="actionBegin"
          @actionComplete="actionComplete"
          :page-settings="pageSettings"
          :toolbar="toolbar"
          @toolbarClick="toolbarClick"
        ></mt-data-grid>
      </div>
    </mt-template-page>
  </div>
</template>

<script>
import selectedwuliaoCode from './edit/wuliao.vue' // 物料、sku
import onlyShowInput from './edit/onlyShowInput.vue' // 物料、sku
export default {
  name: 'Home',
  components: {
    TopInfo: () => import('./components/topInfo.vue')
  },
  provide() {
    return {
      wuliaoList: this.headerInfo
    }
  },
  data() {
    return {
      componentConfig: [
        {
          tab: { title: this.$t('物料信息') },
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false // 使用组件中的toolbar配置
        }
      ],
      dataSource: [],
      columnData: [
        {
          width: '50',
          type: 'checkbox',
          showInColumnChooser: false //隐藏在列选择器中的过滤
        },
        {
          field: 'rowNum',
          headerText: this.$t('行号'),
          allowEditing: false
        },
        {
          field: 'itemCode',
          headerText: this.$t('物料编码'),
          editTemplate: () => {
            return {
              template: selectedwuliaoCode
            }
          }
        },
        {
          field: 'itemName',
          headerText: this.$t('物料名称'),
          editTemplate: () => {
            return { template: onlyShowInput }
          }
        },
        {
          field: 'count',
          headerText: this.$t('库存数量'),
          editType: 'numericedit', //默认编辑类型之number
          edit: {
            params: {
              min: 1
            }
          }
        },
        {
          field: 'baseMeasureUnitCode',
          headerText: this.$t('单位'),
          editTemplate: () => {
            return { template: onlyShowInput }
          }
        },
        {
          field: 'purchaseGroupCode',
          headerText: this.$t('采购组'),
          allowEditing: false,
          editTemplate: () => {
            return { template: onlyShowInput }
          }
        }
      ],
      editSettings: {
        allowEditing: true, //是否允许编辑
        allowDeleting: true, //是否允许删除
        allowAdding: true //是否允许新增
      },
      toolbar: ['Add', 'Edit', 'Cancel', 'Update'],
      allowPaging: true, // 产品要求：新增/编辑时，不分页；查看时要分页
      isEditStatus: false, // 正处于编辑状态
      actionFlag: '', // 点击按钮，可能是 保存草稿-save; 提交-submit;...
      headerInfo: {
        status: 1,
        createUserName: 'jcs',
        createTime: '2022-03-02',
        factory: this.$t('京东工厂'),
        supplierCode: '',
        warehouseCode: '0033333',
        remark: '',
        accept: false,
        confirm: false,
        isSubmit: true,
        isDisabled: false, //true不可编辑  false可编辑
        isDetails: false
      },
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        totalRecordsCount: 0,
        pageSizes: [10, 20, 50, 100, 200]
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      window.gridObj = this.$refs.dataGrid
    })
    if (this.$route.query.details == 1) {
      this.getDetails()
      this.toolbar = []
    }
  },
  methods: {
    actionBegin(args) {
      if (
        this.$route.query.details == 1 &&
        args.rowIndex === args.rowIndex &&
        args.rowIndex !== undefined
      ) {
        args.cancel = true //禁止行编辑
      }
      if (!this.$refs.infoRules.checkForm()) {
        return
      }
      if (args.requestType == 'add' || args.requestType == 'beginEdit') {
        this.isEditStatus = true
      }
      if (args.requestType == 'refresh') {
        this.isEditStatus = false
      }
    },

    actionComplete(args) {
      if (args.requestType == 'save') {
        this.isEditStatus = false
        if (this.actionFlag == 'submit') {
          // this.handleSubmit();
        }
      }
    },
    handleSubmit() {
      console.log(this.$t('点击了提交'), window.gridObj.ejsRef.getCurrentViewRecords())
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    // 提交: 如果处于编辑状态，需要先结束掉，再获取数据
    submitBtn() {
      // this.headerInfo.supplierCode =
      if (this.isEditStatus) {
        window.gridObj.ejsRef.endEdit()
      }
      if (!this.$refs.infoRules.checkForm()) return
      this.create(2)
    },
    // 保存
    preservationBtn() {
      if (this.isEditStatus) {
        window.gridObj.ejsRef.endEdit()
      }
      if (!this.$refs.infoRules.checkForm()) return
      this.create(1)
    },
    // 表格上部操作
    toolbarClick(e) {
      // 自定义了一个行内新增按钮，为了给新增的这一行赋默认值
      if (e.item.id == 'Grid1_add') {
        if (!this.$refs.infoRules.checkForm()) return
        // 新增
        this.$refs.dataGrid.ejsRef.addRecord(this.newRowData())
      } else if (e.item.id == 'Delete') {
        // 删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            this.$refs.dataGrid.ejsRef.deleteRecord()
          }
        })
      }
    },
    // 根据列新建行数据
    newRowData() {
      let row = {}
      // 初始化数据
      this.columnData.forEach((item) => {
        if (item.field === 'rowNum') {
          row[item.field] = this.$refs.dataGrid.ejsRef.getCurrentViewRecords().length
        }
      })
      return row
    },
    // 创建
    create(val) {
      let obj = {
        operationType: val,
        siteCode: this.headerInfo.siteCode.split('-')[0],
        siteName: this.headerInfo.siteCode.split('-')[1],
        supplierCode: this.headerInfo.supplierCode.split('-')[0],
        vmiWarehouseCode: this.headerInfo.vmiWarehouseCode.split('-')[0],
        vmiWarehouseName: this.headerInfo.vmiWarehouseCode.split('-')[1],
        itemList: []
      }
      let arrObj = {}
      window.gridObj.ejsRef.getCurrentViewRecords().forEach((item) => {
        arrObj.count = item.count
        arrObj.itemCode = item.itemCode
        arrObj.itemUnit = item.baseMeasureUnitCode
        arrObj.purchaseGroupCode = item.purchaseGroupCode
        obj.itemList.push(arrObj)
      })
      this.$API.supplierCoordination.postSupplierStockImportCreate(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('创建成功'), type: 'success' })
          this.goBack()
        } else {
          this.$toast({ content: this.$t('创建失败'), type: 'error' })
        }
      })
    },
    // 请求详情
    // 三方公用一个详情接口
    getDetails() {
      let _that = this
      let obj = {
        id: this.$route.query.id
      }
      this.$API.purchaseCoordination.postNewBuyerWarehousingReturnDetail(obj).then((res) => {
        if (res.code === 200) {
          _that.headerInfo = res.data
          _that.headerInfo.siteCode = res.data.siteCode + '-' + res.data.siteName
          _that.headerInfo.supplierCode = res.data.supplierCode + '-' + res.data.supplierName
          _that.headerInfo.vmiWarehouseCode =
            res.data.vmiWarehouseCode + '-' + res.data.vmiWarehouseName
          _that.headerInfo.isDisabled = true
          _that.dataSource = res.data.vmiOrderItemResponses
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
$disabledBg: rgba(245, 245, 245, 1);
$requireddBg: rgba(237, 161, 51, 0.1);
.home {
  height: 100%;

  .pe-edit-grid {
    height: 80%;

    /deep/ .e-grid {
      height: 100%;
      .e-gridcontent {
        height: calc(100% - 90px);
        .e-content {
          height: 100% !important;
        }
      }
    }
  }

  .bgTransparent {
    background-color: transparent !important;
  }

  /deep/ .edit-grid {
    // 去掉 第一个单元格选中时的左边框样式，包括 列冻结后第一个单元格选中
    tr td:first-child::before {
      display: none;
    }
    .e-frozenheader,
    .e-frozenheader > .e-table,
    .e-frozencontent > .e-table {
      border-right: unset !important;
    }
    // 去掉 单元格的选中背景色
    td.e-active {
      @extend .bgTransparent;
    }
    // 去掉 行上悬浮时的单元格背景色
    tr:hover td {
      @extend .bgTransparent;
    }
    // 去掉冻结列 悬浮时，改变背景色问题
    &.e-gridhover .e-frozenhover,
    .e-detailcell,
    .e-detailindentcell,
    .e-detailrowcollapse,
    .e-detailrowexpand,
    .e-groupcaption,
    .e-indentcell,
    .e-recordpluscollapse,
    .e-recordplusexpand,
    .e-rowcell {
      @extend .bgTransparent;
    }

    // 编辑时
    .e-editedrow,
    .e-addedrow {
      .e-rowcell .e-control-wrapper {
        // 禁用的单元格背景色
        &.e-disabled,
        &.cell-disabled,
        .e-input[readonly]:not(.e-dropdownlist) {
          background: $disabledBg !important;
          cursor: not-allowed;
        }

        // 必填的单元格背景色
        &.isRequired .e-input {
          background: $requireddBg !important;
        }
      }
    }

    // 非编辑时
    tr td {
      // 禁用的单元格 样式
      &.e-rowcell.bg-grey,
      &.e-rowcell.bg-grey.e-updatedtd {
        background-color: $disabledBg !important;
        color: #9a9a9a !important;
        cursor: not-allowed;
        &.e-gridchkbox {
          @extend .bgTransparent;
          cursor: pointer;
        }
      }

      // 必填的单元格 样式
      &.e-rowcell.bg-red,
      &.e-rowcell.bg-red.e-updatedtd {
        background-color: $requireddBg !important;
      }
    }
  }
}
</style>
