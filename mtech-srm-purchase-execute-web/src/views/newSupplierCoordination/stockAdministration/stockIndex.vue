//供方 VMI库存管理
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config/stockIndex.js'
export default {
  data() {
    return {
      pageConfig: [
        {
          title: this.$t('汇总列表'),
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          toolbar: {
            tools: [
              [],
              [
                'Filter',
                {
                  id: 'export',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                },
                'Refresh',
                'Setting'
              ]
            ]
          },
          grid: {
            columnData: columnData,
            // lineIndex: 1,
            // autoWidthColumns: columnData.length + 1,
            dataSource: [
              {
                factoryCode: '01',
                factoryName: this.$t('测试1'),
                supplierCode: '001',
                supplierName: this.$t('京东'),
                warehouseCode: this.$t('仓库编码'),
                warehouseName: this.$t('仓库名称'),
                materialCode: '0001',
                materialName: this.$t('物料名称'),
                purchaseGroup: this.$t('采购组'),
                stockStatus: this.$t('充足'),
                batchNumber: this.$t('第一卷号'),
                stockNumber: '99',
                founder: 'jcs'
              }
            ],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmi_stock/supplier-page-query',
              recordsPosition: 'data.records'
            },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    handleClickToolBar(e) {
      if (e.grid.getSelectedRecords().length <= 0 && e.toolbar.id === 'export') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      } else {
        // 调用导出方法·
        let _selectRows = e.grid.getSelectedRecords()
        console.log(_selectRows, 7777)
        debugger
      }
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
