import { i18n } from '@/main.js'
// 状态 状态:1-新建,2-发货中,3-已完成,4-已取消,5-已关闭
export const Status = {
  new: 0, // 新建
  shipping: 1, // 待确认
  completed: 2, // 已接收
  cancelled: 8, // 已完成
  closed: 9 // 已取消
}
// 状态 text
export const StatusText = {
  [Status.new]: i18n.t('新建'),
  [Status.shipping]: i18n.t('待确认'),
  [Status.completed]: i18n.t('已接收'),
  [Status.cancelled]: i18n.t('已完成'),
  [Status.closed]: i18n.t('已取消')
}
// 状态 对应的 css class
export const StatusCssClass = {
  [Status.new]: 'col-active',
  [Status.shipping]: 'col-active',
  [Status.completed]: 'col-active',
  [Status.cancelled]: 'col-active',
  [Status.closed]: 'col-inactive'
}
