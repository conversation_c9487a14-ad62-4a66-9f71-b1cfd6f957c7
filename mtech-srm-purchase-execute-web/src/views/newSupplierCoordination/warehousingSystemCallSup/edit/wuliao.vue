<template>
  <div class="pc-select">
    <div class="in-cell">
      <mt-input :id="fieldName" disabled v-model="value" :width="130" ::placeholder="$t('title')" />
      <mt-icon style="width: 20px" name="icon_list_refuse" @click.native="handleClear"></mt-icon>
      <mt-icon style="width: 20px" name="icon_input_search" @click.native="showDialog"></mt-icon>
    </div>
    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="title"
      :buttons="buttons"
      @close="handleClose"
    >
      <div class="full-height">
        <mt-template-page
          ref="templateRef"
          class="template-height has-page"
          :hidden-tabs="true"
          :template-config="pageConfig"
          @recordDoubleClick="recordDoubleClick"
        ></mt-template-page>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
export default {
  props: {},
  inject: {
    wuliaoList: {
      default: () => ({})
    }
  },
  data() {
    return {
      dialogShow: false,
      title: this.$t('请选择'),
      value: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[], ['Filter', 'Refresh', 'Setting']]
          },
          grid: {
            // height: 352,
            // allowPaging: true,
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: [
              {
                width: '150',
                field: 'itemCode',
                headerText: this.$t('物料编号')
              },
              { width: '150', field: 'itemName', headerText: this.$t('物料名称') },
              {
                width: '150',
                field: 'baseMeasureUnitName',
                headerText: this.$t('单位')
              },
              {
                width: '150',
                field: 'baseMeasureUnitCode',
                headerText: this.$t('单位编码')
              },
              {
                width: '150',
                field: 'purchaseGroupName',
                headerText: this.$t('采购组')
              },
              {
                width: '150',
                field: 'purchaseGroupCode',
                headerText: this.$t('采购组编码')
              }
            ],
            asyncConfig: {}
          }
        }
      ]
    }
  },
  mounted() {
    let obj = {
      buyerEnterpriseId: this.wuliaoList.customerEnterpriseId,
      siteCode: this.wuliaoList.siteCode.split('-')[0],
      requireItemDetail: true
    }
    this.pageConfig[0].grid.asyncConfig = {
      url: `/masterDataManagement/tenant/supply/source/page-available-source-items-in-supplier-view?buyerEnterpriseId=${obj.buyerEnterpriseId}&siteCode=${obj.siteCode}&requireItemDetail=true`,
      methods: 'post'
      // params: obj,
      // recordsPosition: "data",
    }
    this.fieldName = this.data.column.field
    this.value = this.data[this.fieldName]
  },
  methods: {
    recordDoubleClick(args) {
      this.confirm(null, [args.rowData])
    },
    confirm(e, records) {
      if (!records || records.length <= 0) {
        records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      }
      if (records.length <= 0) return
      console.log(records, '-=-=')
      //   this.value = records[0]?.itemName;
      this.value = records[0]?.itemCode
      //联动改变物料描述
      this.$bus.$emit('itemNameChange', records[0]?.itemName)
      this.$bus.$emit('unitCodeChange', records[0]?.baseMeasureUnitCode)
      this.$bus.$emit('buyerOrgCodeChange', records[0]?.purchaseGroupCode)
      this.$bus.$emit('buyerOrgNameChange', records[0]?.purchaseGroupName)
      this.$bus.$emit('unitNameChange', records[0]?.baseMeasureUnitName)
      // 关闭弹窗
      this.handleClose()
    },
    handleClose() {
      this.dialogShow = false
      this.$refs.dialog.ejsRef.hide()
    },
    handleClear() {
      this.value = null
      //联动改变物料描述
      this.$bus.$emit('itemNameChange', null)
      this.$bus.$emit('unitCodeChange', null)
      this.$bus.$emit('buyerOrgCodeChange', null)
      this.$bus.$emit('buyerOrgNameChange', null)
      this.$bus.$emit('unitNameChange', null)
    },
    showDialog() {
      this.dialogShow = true
      this.$refs.dialog.ejsRef.show()
    }
  }
}
</script>

<style scoped lang="scss">
.in-cell {
  width: 100%;
  display: flex;
  > .mt-icons {
    margin-left: 5px;
    cursor: pointer;
  }
}

.full-height {
  height: 100%;
}
/deep/.template-height {
  .mt-data-grid {
    height: 100%;

    > .e-grid {
      height: calc(100% - 40px);
      .e-content {
        height: 100% !important;
      }
    }
  }
}

/deep/.template-height.has-page {
  .repeat-template .mt-data-grid > .e-control {
    height: calc(100% - 40px) !important;
  }
}
</style>
