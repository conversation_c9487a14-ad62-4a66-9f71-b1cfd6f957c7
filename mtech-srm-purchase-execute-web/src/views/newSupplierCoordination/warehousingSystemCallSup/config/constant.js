import { i18n } from '@/main.js'
export const editSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Bottom'
}

export const Status = {
  new: 0, // 新建
  beConfirmed: 1, // 待确认
  cancelled: 2, // 已接收
  // returned: 3, // 已退回
  received: 8, // 已完成
  cancel: 9 // 已取消
}
// 状态 text
export const StatusText = {
  [Status.beConfirmed]: i18n.t('待确认'),
  [Status.cancelled]: i18n.t('已接收'),
  [Status.received]: i18n.t('已完成'),
  [Status.new]: i18n.t('新建'),
  [Status.cancel]: i18n.t('已取消')
}
// 状态 对应的 css class
export const StatusCssClass = {
  [Status.beConfirmed]: 'col-active',
  [Status.cancelled]: 'col-active',
  [Status.cancel]: 'col-active',
  [Status.received]: 'col-active',
  [Status.new]: 'col-inactive'
}

// 发货方式:1-快递配送，2-物流配送
export const ShippingType = {
  express: 1, // 快递配送
  logistics: 2 // 物流配送
}
// 发货方式 text
export const ShippingTypeText = {
  [ShippingType.express]: i18n.t('快递配送'),
  [ShippingType.logistics]: i18n.t('物流配送')
}
// 发货方式 对应的 Options
export const ShippingTypeOptions = [
  {
    // 快递配送
    value: ShippingType.express,
    text: ShippingTypeText[ShippingType.express],
    label: ShippingTypeText[ShippingType.express],
    cssClass: ''
  },
  {
    // 物流配送
    value: ShippingType.logistics,
    text: ShippingTypeText[ShippingType.logistics],
    label: ShippingTypeText[ShippingType.logistics],
    cssClass: ''
  }
]

// 接口状态
export const apiStatusOptions = [
  { text: i18n.t('成功'), label: i18n.t('成功'), value: i18n.t('成功') },
  { text: i18n.t('失败'), label: i18n.t('失败'), value: i18n.t('失败') }
]

// 行接口状态
export const itemStatusOptions = [
  { text: i18n.t('通过'), label: i18n.t('通过'), value: i18n.t('通过') },
  { text: i18n.t('不通过'), label: i18n.t('不通过'), value: i18n.t('不通过') }
]

// 集成类型
export const collectTypeOptions = [
  { text: i18n.t('拉取'), label: i18n.t('拉取'), value: i18n.t('拉取') },
  { text: i18n.t('推送'), label: i18n.t('推送'), value: i18n.t('推送') }
]

// 供方物料标识
export const ItemTagDescOptions = [
  { text: i18n.t('成品'), label: i18n.t('成品'), value: 'F' },
  { text: i18n.t('半成品'), label: i18n.t('半成品'), value: 'M' },
  { text: i18n.t('辅料'), label: i18n.t('辅料'), value: 'A' }
]
