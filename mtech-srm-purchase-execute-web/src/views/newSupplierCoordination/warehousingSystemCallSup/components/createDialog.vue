<template>
  <div>
    <mt-dialog
      ref="dialog"
      css-class="create-proj-dialog"
      :header="$t('新建')"
      :buttons="buttons"
      :width="1400"
      :height="900"
      @close="handleClose"
      :open="onOpen"
    >
      <mt-template-page
        ref="templateRef"
        :hidden-tabs="false"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
      ></mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
import { columnObj } from '../config/index.js'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  props: {
    ids: {
      type: Array,
      default: () => {}
    }
  },
  data() {
    return {
      pageConfig: [
        {
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'add',
                  // icon: "icon_solid_Createorder",
                  // permission: ["O_02_1092"],
                  title: this.$t('创建')
                },
                {
                  id: 'export1',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: `8AFF473B-770E-7254-08A2-D5D513C805FA`,

          grid: {
            columnData: columnObj.createColumn,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/so/detail/queryAvailable',
              recordsPosition: 'data.records',
              serializeList: this.serialize
            },
            frozenColumns: 1
          }
        }
      ],

      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        }
      ]
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()

    // this.$refs.ruleForm.resetFields();
  },

  methods: {
    // 切换方式
    selectChange(e) {
      console.log(e)
      this.$refs.addForm.resetFields()
      console.log(this.addForm)
    },
    handleClickToolBar(e) {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      if (e.toolbar.id === 'export1') {
        let obj = JSON.parse(
          sessionStorage.getItem('8AFF473B-770E-7254-08A2-D5D513C805FA')
        ).visibleCols
        let field = []
        if (obj !== undefined) {
          obj.forEach((item) => {
            field.push(item.field)
          })
        } else {
          columnObj.createColumn.forEach((item) => {
            field.push(item.field)
          })
        }
        let params = {
          page: { current: 1, size: 1000 },
          rules: rule.rules || [],
          sortedColumnStr: field.toString()
        }
        this.$store.commit('startLoading')

        this.$API.supplierCoordination.supplierSoOrderViewExport(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
      if (e.grid.getSelectedRecords().length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        if (e.toolbar.id === 'add') {
          console.log(e.grid.getSelectedRecords())
          for (let item of e.grid.getSelectedRecords()) {
            if (e.grid.getSelectedRecords()[0].siteCode !== item.siteCode) {
              this.$toast({
                content: this.$t('只可以创建同一工厂的po'),
                type: 'warning'
              })
              return
            } else if (item.vmiWarehouseCode === '' || item.vmiWarehouseCode === undefined) {
              this.$toast({
                content: this.$t(`工厂${item.siteName}没有vmi仓不可创建`),
                type: 'warning'
              })
              return
            }
          }
          sessionStorage.setItem('createDate', JSON.stringify(e.grid.getSelectedRecords()))

          this.redirectPage('purchase-execute/new-supplier-warehousing-create?create=1')
          this.handleClose()
        }
      }
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },

    handleClose() {
      this.$emit('handleDialogShow', false)
    }
  }
}
</script>

<style></style>
