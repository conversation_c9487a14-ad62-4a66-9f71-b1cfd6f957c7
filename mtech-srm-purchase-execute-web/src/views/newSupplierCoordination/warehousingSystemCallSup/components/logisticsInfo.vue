<template>
  <div class="box">
    <!-- 下面的内容 -->
    <div class="main-box">
      <mt-form
        ref="ruleForm"
        :model="logisticsData"
        :rules="rules"
        :validate-on-rule-change="false"
      >
        <!-- 发货方式 -->
        <mt-form-item prop="type" :label="$t('请选择发货方式:')">
          <br />
          <mt-radio
            v-model.number="logisticsData.transportType"
            :data-source="ShippingTypeOptions"
          ></mt-radio>
        </mt-form-item>
        <br />
        <!-- 快递-物流公司 -->
        <mt-form-item
          v-if="logisticsData.transportType == ShippingType.express"
          :label="$t('物流公司')"
          prop="thirdPartyLogisticsName"
        >
          <mt-select
            :data-source="companySelect"
            :disabled="logisticsData.statusCreate"
            v-model="logisticsData.thirdPartyLogisticsName"
            :placeholder="$t('物流公司')"
          ></mt-select>
        </mt-form-item>
        <!-- 快递-物流单号 -->
        <mt-form-item
          v-if="logisticsData.transportType == ShippingType.express"
          :label="$t('物流单号')"
          prop="transportNum"
        >
          <mt-input
            :disabled="logisticsData.statusCreate"
            v-model="logisticsData.transportNum"
            :placeholder="$t('物流单号')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-司机名称 -->
        <mt-form-item
          v-if="logisticsData.transportType == ShippingType.logistics"
          prop="driverName"
          :label="$t('司机名称')"
        >
          <mt-input
            :disabled="logisticsData.statusCreate"
            v-model="logisticsData.driverName"
            :placeholder="$t('司机名称')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-司机联系方式 -->
        <mt-form-item
          v-if="logisticsData.transportType == ShippingType.logistics"
          :label="$t('司机联系方式')"
          prop="driverPhone"
        >
          <mt-input
            :disabled="logisticsData.statusCreate"
            v-model="logisticsData.driverPhone"
            :placeholder="$t('司机联系方式')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-车牌 -->
        <mt-form-item
          v-if="logisticsData.transportType == ShippingType.logistics"
          :label="$t('车牌')"
          prop="licensePlateNumber"
        >
          <mt-input
            :disabled="logisticsData.statusCreate"
            v-model="logisticsData.licensePlateNumber"
            :placeholder="$t('车牌')"
          ></mt-input>
        </mt-form-item>
        <!-- 件数 -->
        <mt-form-item :label="$t('件数')" prop="count">
          <mt-input-number
            :disabled="logisticsData.statusCreate"
            v-model="logisticsData.count"
            :placeholder="$t('件数')"
          ></mt-input-number>
        </mt-form-item>

        <!-- 备注 -->
        <mt-form-item class="full-width" :label="$t('备注')" :show-message="false">
          <mt-input
            :disabled="logisticsData.statusCreate"
            v-model="logisticsData.remark"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { ShippingTypeOptions, ShippingType } from '../config/constant'

export default {
  props: {
    logisticsData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      ShippingTypeOptions,
      ShippingType,
      isExpand: true,
      rules: {
        thirdPartyLogisticsName: [
          {
            required: true,
            message: this.$t('请选择物流公司'),
            trigger: 'blur'
          }
        ],
        transportNum: [
          {
            required: true,
            message: this.$t('请输入物流单号'),
            trigger: 'blur'
          }
        ],
        driverName: [
          {
            required: true,
            message: this.$t('请输入司机名称'),
            trigger: 'blur'
          }
        ],
        driverPhone: [
          {
            required: true,
            message: this.$t('请输入司机联系方式'),
            trigger: 'blur'
          }
        ],
        licensePlateNumber: [{ required: true, message: this.$t('请输入车牌'), trigger: 'blur' }],
        count: [{ required: true, message: this.$t('请输入件数'), trigger: 'blur' }]
      },
      companySelect: [
        { text: this.$t('区域1'), value: 0 },
        { text: this.$t('区域2'), value: 1 }
      ]
    }
  },
  mounted() {
    this.getCompany()
  },
  filters: {},
  methods: {
    getCompany() {
      this.$API.supplierCoordination
        .postLogisticsCompany({ dictCode: 'logisticsCompany' })
        .then((res) => {
          if (res.data && res.data.length) {
            this.companySelect = res.data.map((item) => {
              return {
                text: item.itemName,
                value: item.itemCode,
                id: item.id
              }
            })
          }
        })
    },
    // 校验
    checkForm() {
      let validStatus = false
      this.$refs.ruleForm.validate((valid) => {
        validStatus = valid
      })
      return validStatus
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  padding: 20px 0 20px 20px;

  .main-box .mt-form-item {
    width: calc(20% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
  }
}
</style>
