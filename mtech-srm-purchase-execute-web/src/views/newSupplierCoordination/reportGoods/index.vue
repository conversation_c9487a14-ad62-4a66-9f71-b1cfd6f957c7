// 报表
<template>
  <div class="full-height pt20">
    <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
    <mt-template-page
      ref="templateRef"
      v-show="tabIndex === 0"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <mt-template-page
      ref="templateRef1"
      v-show="tabIndex === 1"
      :hidden-tabs="true"
      :template-config="pageConfig1"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <create-dialog
      v-if="deliveryShow"
      ref="child"
      @handleAddDialogShow="handleAddDialogShow"
      @handleDialogShow="handleDialogShow"
    ></create-dialog>
  </div>
</template>

<script>
import { columnObj } from './config/index.js'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  data() {
    return {
      tabIndex: 0,
      pageConfig: [
        {
          // tab: { title: this.$t("卷号维度") },
          toolbar: {
            tools: [
              [
                {
                  id: 'export1',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          useBaseConfig: true, // 使用组件中的toolbar配置
          // dataPermission: "a",
          // permissionCode: "T_02_0122",
          dataSource: [],
          useToolTemplate: false,
          useCombinationSelection: false,
          gridId: '3D801235-B8C1-C8DB-9EF9-C06F61EE6E9B',

          grid: {
            columnData: columnObj.headColumn,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiSteel/report/supplier/batchView'
            },
            rules: [
              {
                field: 'header.orderStatus',
                operator: 'equal',
                value: 0
              }
            ],
            frozenColumns: 1
          }
        }
      ],
      pageConfig1: [
        {
          // tab: { title: this.$t("订单维度") },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useCombinationSelection: false,
          // dataPermission: "b",
          // permissionCode: "T_02_0123",
          toolbar: {
            tools: [
              [
                {
                  id: 'export2',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          useToolTemplate: false,
          gridId: 'FA773957-A16F-3C0B-C9B1-7A126F59BAFD',
          grid: {
            columnData: columnObj.detailedColumn,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiSteel/report/supplier/orderView'
            },
            frozenColumns: 1
          }
        }
      ],
      tabSource: [
        {
          title: this.$t('卷号维度')
        },
        {
          title: this.$t('订单维度')
        }
      ],
      deliveryShow: false,

      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    },
    // 头部操作
    handleClickToolBar(args) {
      let rule =
        this.tabIndex === 0
          ? this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
          : this.$refs.templateRef1.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}

      if (args.toolbar.id === 'export1') {
        let obj = JSON.parse(
          sessionStorage.getItem('3D801235-B8C1-C8DB-9EF9-C06F61EE6E9B')
        ).visibleCols
        let field = []
        if (obj !== undefined) {
          obj.forEach((item) => {
            field.push(item.field)
          })
        } else {
          columnObj.headColumn.forEach((item) => {
            field.push(item.field)
          })
        }

        let params = {
          page: { current: 1, size: 10000 },
          rules: rule.rules || [],
          sortedColumnStr: field.toString()
        }
        this.$store.commit('startLoading')
        this.$API.supplierCoordination.supplierBatchViewExport(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
      }
      if (args.toolbar.id === 'export2') {
        let obj = JSON.parse(
          sessionStorage.getItem('FA773957-A16F-3C0B-C9B1-7A126F59BAFD')
        ).visibleCols
        let field = []
        if (obj !== undefined) {
          obj.forEach((item) => {
            field.push(item.field)
          })
        } else {
          columnObj.detailedColumn.forEach((item) => {
            field.push(item.field)
          })
        }

        let params = {
          page: { current: 1, size: 10000 },
          rules: rule.rules || [],
          sortedColumnStr: field.toString()
        }
        this.$store.commit('startLoading')
        this.$API.supplierCoordination.supplierOrderViewExport(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
      }
    },

    handleClickCellTool(e) {
      let idOBj = {
        id: e.data.id
      }
      // 提交
      if (e.tool.id === 'submit') {
        // 参数值需要修改
        this.$dialog({
          data: {
            title: this.$t('提交'),
            message: this.$t('确认提交吗？')
          },
          success: () => {
            this.submit(idOBj)
          }
        })
      } else if (e.tool.id === 'cancel') {
        //取消
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消吗？')
          },
          success: () => {
            this.cancel(idOBj)
          }
        })
      }
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
