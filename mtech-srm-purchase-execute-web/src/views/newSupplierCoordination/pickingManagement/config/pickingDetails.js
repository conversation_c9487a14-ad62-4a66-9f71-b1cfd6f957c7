import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'rowNum', //rowNum
    headerText: i18n.t('行号')
  },
  {
    width: '200',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },
  {
    width: '150',
    field: 'stockType', ////stockType 订单操作的库存属性分类 0合格库存 1待检库存 2不合格库存
    headerText: i18n.t('库存状况'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('合格库存'),
        1: i18n.t('待检库存'),
        3: i18n.t('不合格库存')
      }
    }
  },
  {
    width: '150',
    field: 'batchCode', //batchCode
    headerText: i18n.t('卷号')
  },
  // {
  //   width: "150",
  //   field: "countLimit",
  //   headerText: i18n.t("可领料数量"),
  // },
  {
    width: '150',
    field: 'checkCount',
    headerText: i18n.t('卷重')
  },
  {
    width: '150',
    field: 'itemUnitDescription', //itemUnit
    headerText: i18n.t('单位')
  },
  {
    width: '150',
    field: 'purchaseGroupName', //purchaseGroupName
    headerText: i18n.t('采购组')
  },
  {
    width: '150',
    field: 'stockCount', //purchaseGroupName
    headerText: i18n.t('库存数量')
  },
  {
    width: '150',
    field: 'takeNo', //
    headerText: i18n.t('车号')
  },
  {
    width: '150',
    field: 'remark', //remark
    headerText: i18n.t('行备注')
  },
  {
    width: '150',
    field: 'orderCode', //orderCode
    headerText: i18n.t('关联采购订单号')
  },
  {
    width: '150',
    field: 'lineNo', //  lineNo
    headerText: i18n.t('关联采购订单行号')
  }
]
