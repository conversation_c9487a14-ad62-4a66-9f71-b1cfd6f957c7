// VMI领料管理-供方
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :current-tab="tabIndex"
      :permission-obj="permissionObj"
      @rowDeselected="rowDeselecting"
      @rowSelecting="rowSelecting"
      :hidden-tabs="false"
      @handleSelectTab="handleSelectTab"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnObj } from './config/index.js'
import { download, getHeadersFileName } from '@/utils/utils'
import { cloneDeep } from 'lodash'

// import Vue from "vue";
export default {
  components: {
    // addDialog: require("./components/addDialog.vue").default,
  },
  data() {
    return {
      tabIndex: 0,
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0124' },
          { dataPermission: 'b', permissionCode: 'T_02_0125' }
        ]
      },
      printObj: {
        orderId: '',
        logisticCompanyId: ''
      },
      currentTab: 0, //代表当前默认加载显示的Tab索引
      supplierThirdOptions: [],

      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.print,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          tab: { title: this.$t('头视图') },
          useBaseConfig: false, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          dataPermission: 'a',
          permissionCode: 'T_02_0124',
          toolbar: {
            tools: [
              [
                {
                  id: 'confirm',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('供方确认'),
                  permission: ['O_02_1267']
                }
                // {
                //   id: "supplierRrturn",
                //   icon: "icon_list_recall",
                //   title: this.$t("供方退回"),
                //   permission: ["O_02_1268"],
                // },
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },

          grid: {
            columnData: columnObj.headColumn,
            // dataSource: [],
            asyncConfig: {
              // url: "/srm-purchase-execute/tenant/vmi-pickup-order/supplier-page-query",
              url: '/srm-purchase-execute/tenant/vmiSteel/vmi-pickup-order/supplier-page-query',
              defaultRules: []
              // recordsPosition: "data",
            },
            frozenColumns: 1
          }
        },
        {
          tab: { title: this.$t('明细视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          // dataPermission: "a",
          // permissionCode: "T_02_0125",
          toolbar: {
            tools: [
              [
                // {
                //   id: "establish",
                //   icon: "icon_table_batchacceptance",
                //   title: this.$t("创建送货单"),
                //   // permission: ["O_02_1276"],
                // },
                {
                  id: 'download',
                  icon: 'icon_solid_export',
                  title: this.$t('导出')
                }
                // {
                //   id: "supplierRrturn",
                //   icon: "icon_list_recall",
                //   title: this.$t("供方退回"),
                // },
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: 'CA9D848A-F143-3901-6E80-0A6AE6FE67A5',

          grid: {
            columnData: columnObj.detailedColumn,
            // dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiSteel/vmi-pickup-order/supplier-item-page-query'
              // recordsPosition: "data",
            },
            frozenColumns: 1
          }
        },
        {
          tab: { title: this.$t('待审核') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          // dataPermission: "a",
          // permissionCode: "T_02_0125",
          toolbar: {
            tools: [
              [
                {
                  id: 'detailConfirm',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('供方确认')
                  // permission: ["O_02_1267"],
                }
                // {
                //   id: "supplierRrturn",
                //   icon: "icon_list_recall",
                //   title: this.$t("供方退回"),
                // },
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          // gridId: "CA9D848A-F143-3901-6E80-0A6AE6FE67A5",

          grid: {
            columnData: columnObj.detailedColumn,
            // dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiSteel/vmi-pickup-order/supplier-item-page-query',
              // recordsPosition: "data",
              rules: [
                {
                  field: 'status',
                  operator: 'equal',
                  value: 1
                }
              ]
            },
            frozenColumns: 1
          }
        },
        {
          tab: { title: this.$t('送货单创建') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          // dataPermission: "a",
          // permissionCode: "T_02_0125",
          toolbar: {
            tools: [
              [
                {
                  id: 'detailEstablish',
                  icon: 'icon_table_batchacceptance',
                  title: this.$t('创建送货单')
                  // permission: ["O_02_1276"],
                }
                // {
                //   id: "supplierRrturn",
                //   icon: "icon_list_recall",
                //   title: this.$t("供方退回"),
                // },
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          // gridId: "CA9D848A-F143-3901-6E80-0A6AE6FE67A5",

          grid: {
            columnData: columnObj.detailedColumn,
            // dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiSteel/vmi-pickup-order/supplier-item-page-query',
              // recordsPosition: "data",
              rules: [
                {
                  field: 'status',
                  operator: 'equal',
                  value: 2
                }
              ]
            },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },
  mounted() {
    if (this.$route.query.from === 'mytodo') {
      // this.currentTab = 1;
      this.pageConfig[0].grid.asyncConfig.defaultRules = JSON.parse(
        sessionStorage.getItem('todoDetail')
      ).defaultRules
    }
    let obj = {
      page: {
        current: 1,
        size: 100
      }
    }
    this.$API.supplierCoordination.purNewOrderSave(obj).then((res) => {
      // let data = res.data.filter((item) => item.supplierName);
      this.supplierThirdOptions = res.data.records

      // this.$nextTick(() => {
      //   if (e.updateData && typeof e.updateData == "function") {
      //     e.updateData(data);
      //   }
      // });
    })
  },

  methods: {
    hide() {
      this.$refs.toast.ejsRef.hide()
    },
    onOpen: function (args) {
      args.preventFocus = true
    },
    // 切换tab
    handleSelectTab(e) {
      this.tabIndex = e
    },
    // 跳转详情
    handleClickCellTitle(e) {
      let obj = {
        id: ''
      }
      if (e.field === 'vmiOrderCode') {
        obj.id = e.data.vmiOrderId ?? e.data.id
        this.redirectPage('purchase-execute/new-supplier-picking-details', obj)
      }
    },
    //复选框事件
    rowSelecting(e) {
      if (e?.rowIndexes?.length < 1) return
      if (e.data instanceof Array) return
      if (this.tabIndex === 3 || this.tabIndex === 1) return

      // if (e?.data?.status != 2) {
      //   let mapArr = e.rowIndexes.filter((item) => {
      //     return item !== e.rowIndex;
      //   });
      //   if (!(e.data instanceof Array)) {
      //     console.log(e, mapArr);
      //     this.$nextTick(() => {
      //       this.$refs.templateRef
      //         .getCurrentUsefulRef()
      //         .ejsRef.selectRows(mapArr);
      //     });
      //   }
      //   return;
      // }
      //获取当前页所有的行
      let Obj = cloneDeep(
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getCurrentViewRecords()
      )
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getSelectedRecords()
      )
      currentSelect.push(e.data)
      currentSelect = [...new Set(currentSelect)]
      if (Obj instanceof Array && Obj.length > 0) {
        let mapArr = []
        for (let i = 0; i < currentSelect.length; i++) {
          for (let j = 0; j < Obj.length; j++) {
            if (currentSelect[i]?.vmiOrderCode === Obj[j]?.vmiOrderCode) {
              mapArr.push(Obj[j])
            }
          }
        }
        mapArr = mapArr.map((item) => item.index)
        if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0 && mapArr.length > 0) {
          this.$refs.templateRef.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
        }
        console.log(mapArr)
      }
    },
    //取消
    rowDeselecting(e) {
      if (e?.rowIndexes?.length < 1) return
      if (this.tabIndex === 3 || this.tabIndex === 1) return

      //获取当前页所有的行
      let Obj = cloneDeep(
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getCurrentViewRecords()
      )
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.templateRef
          .getCurrentUsefulRef()
          .ejsRef.getSelectedRecords()
          .map((e) => e.id)
      )
      let mapArr = []
      Obj.filter((item, index) => {
        if (item.vmiOrderCode !== e.data.vmiOrderCode && currentSelect.includes(item.id)) {
          console.log(item)
          mapArr.push(index)
        }
      })
      console.log(e, mapArr)
      if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0) {
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
      }
    },
    // 头部跳转
    handleClickToolBar(e) {
      console.log(e)
      let _selectRows = e.grid.getSelectedRecords()
      // let ids = _selectRows.map((item) => item.id)
      if (e.toolbar.id === 'download') {
        // 导出
        this.downLoadExport()
      }
      if (e.toolbar.id === 'confirm') {
        // VMI领料单-供方-批量确认
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('确认'),
            message: this.$t('确定确认吗？')
          },
          success: () => {
            // TODO: 领料-供方-确认（列表-批量确认）
            // debugger
            let ids = _selectRows.map((item) => item.id)
            // 只有在待确认的情况下才可选择
            let switAccept = true
            _selectRows.map((item) => {
              if (item.status != 1) {
                switAccept = false
              }
            })
            if (switAccept == false) {
              this.$toast({
                content: this.$t('请选择待确认状态'),
                type: 'warning'
              })
              return
            }
            this.$API.supplierCoordination
              .purNewOrderQueryBatchConfirm({ ids: ids })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('确认成功'),
                    type: 'success'
                  })
                  // 刷新当前 Grid
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
          }
        })
      } else if (e.toolbar.id == 'supplierRrturn') {
        // VMI领料单-供方-批量退回
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('退回'),
            message: this.$t('确定退回吗？')
          },
          success: () => {
            // TODO: 领料-供方-退回（列表-批量退回）
            // debugger
            let ids = _selectRows.map((item) => item.id)
            // 只有在待确认的情况下才可选择
            let switAccept = true
            _selectRows.map((item) => {
              if (item.status != 1) {
                switAccept = false
              }
            })
            if (switAccept == false) {
              this.$toast({
                content: this.$t('请选择待确认状态'),
                type: 'warning'
              })
              return
            }
            this.$API.supplierCoordination.purNewOrderQueryBatchReject({ ids: ids }).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('退回成功'),
                  type: 'success'
                })
                // 刷新当前 Grid
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          }
        })
      } else if (e.toolbar.id === 'establish') {
        // 创建送货单
        // this.redirectPage("purchase-execute/VMIpickingListCreate", obj);
        this.submitAsn()
      } else if (e.toolbar.id === 'detailEstablish') {
        this.submitAsn()
      } else if (e.toolbar.id === 'print') {
        // this.printObj.orderId = ids;
        this.$refs.toast.ejsRef.show()
      } else {
        // 调用刷新方法
      }
      if (e.toolbar.id === 'detailConfirm') {
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('确认'),
            message: this.$t('确定确认吗？')
          },
          success: () => {
            // TODO: 领料-供方-确认（列表-批量确认）
            // debugger
            let ids = _selectRows.map((item) => item.vmiOrderId)
            // 只有在待确认的情况下才可选择
            let switAccept = true
            _selectRows.map((item) => {
              if (item.status != 1) {
                switAccept = false
              }
            })
            if (switAccept == false) {
              this.$toast({
                content: this.$t('请选择待确认状态'),
                type: 'warning'
              })
              return
            }
            this.$API.supplierCoordination
              .purNewOrderQueryBatchConfirm({ ids: [...new Set(ids)] })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('确认成功'),
                    type: 'success'
                  })
                  // 刷新当前 Grid
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
          }
        })
      }
    },
    downLoadExport() {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let obj = JSON.parse(
        sessionStorage.getItem('CA9D848A-F143-3901-6E80-0A6AE6FE67A5')
      ).visibleCols
      let field = []
      if (obj !== undefined) {
        obj.forEach((item) => {
          field.push(item.field)
        })
      } else {
        columnObj.detailedColumn.forEach((item) => {
          field.push(item.field)
        })
      }
      const params = {
        page: { current: 1, size: 1000 },
        ...queryBuilderRules,
        sortedColumnStr: field.toString()
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.thirdPartyVMICollaboration.postVmiSteelPickupSupplierExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'confirmss') {
        this.$dialog({
          data: {
            title: this.$t('确认'),
            message: this.$t('确认确认吗？')
          },
          success: () => {
            // TODO: 领料-供方-确认（列表-确认）
            this.$API.supplierCoordination
              .purNewOrderQueryConfirm({ id: e.data.id })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('提交成功'),
                    type: 'success'
                  })
                  // 刷新当前 Grid
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
          }
        })
      } else if (e.tool.id == 'cancel') {
        this.$dialog({
          data: {
            title: this.$t('退回'),
            message: this.$t('确认退回吗？')
          },
          success: () => {
            // TODO: 领料-供方-退回（列表-退回）
            // debugger
            this.$API.supplierCoordination
              .purNewOrderQueryBatchReject({ ids: [e.data.id] })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('退回成功'),
                    type: 'success'
                  })
                  // 刷新当前 Grid
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
          }
        })
      }
    },
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },
    submitAsn() {
      const selectedRowData = this.$refs.templateRef.getCurrentTabRef().grid.getSelectedRecords()
      if (selectedRowData?.length > 10) {
        this.$toast({
          content: this.$t('创建的数据最多为10条'),
          type: 'warning'
        })
        return
      }
      const data = {
        itemList: []
      }
      let obj = null
      selectedRowData.forEach((item) => {
        obj = {}
        obj.count = item.checkCount
        obj.id = item.id
        obj.lineNo = item.lineNo
        obj.orderCode = item.orderCode
        data.itemList.push(obj)
      })
      this.$API.thirdPartyVMICollaboration.createNewAsn(data).then((res) => {
        if (res.code === 200) {
          localStorage.setItem('asnList', JSON.stringify(res.data))
          this.redirectPage('purchase-execute/VMIpickingListNewCreate')
        }
      })
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('todoDetail')
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
