<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <mt-form-item :label="$t('客户公司名称')" prop="companyCode">
        <mt-select
          v-model="ruleForm.companyCode"
          :data-source="companyList"
          :show-clear-button="false"
          :fields="{ text: 'customerName', value: 'customerCode' }"
          :placeholder="$t('请选择客户公司名称')"
          @change="handleBusinessChange"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="logisticsCompanyName" :label="$t('物流公司')">
        <mt-input
          v-model="ruleForm.logisticsCompanyName"
          :show-clear-button="true"
          :multiline="false"
          :placeholder="$t('请输入后续物流公司名称')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="contactPerson" :label="$t('联系人')">
        <mt-input
          v-model="ruleForm.contactPerson"
          :show-clear-button="true"
          :multiline="false"
          :placeholder="$t('请输入联系人')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="contactPhone" :label="$t('联系方式')">
        <mt-input
          v-model="ruleForm.contactPhone"
          :show-clear-button="true"
          :multiline="false"
          :placeholder="$t('请选择联系方式')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { RegExpMap } from '@/utils/constant'

export default {
  data() {
    const { phoneNumReg } = RegExpMap
    const phoneNumberValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入司机手机号')))
      } else if (!phoneNumReg.test(value)) {
        callback(new Error(this.$t('请输入正确的手机号')))
      } else {
        this.$refs.ruleForm.clearValidate(['contactPhone'])
        callback()
      }
    }
    return {
      dialogTitle: '',
      // 校验
      rules: {
        // 公司
        companyCode: [{ required: true, message: this.$t('请输入公司'), trigger: 'blur' }],
        logisticsCompanyName: [
          {
            required: true,
            message: this.$t('请输入物流公司'),
            trigger: 'blur'
          }
        ],
        contactPerson: [
          {
            required: true,
            message: this.$t('请输入智能手机'),
            trigger: 'blur'
          }
        ],
        contactPhone: [
          {
            required: true,
            validator: phoneNumberValidator,
            trigger: 'blur'
          }
        ]
      },
      // 确定 取消按钮
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      // 下拉数据
      companyList: [],
      ruleForm: {
        companyName: '', //客户公司名称
        logisticsCompanyName: '', //物流公司
        contactPerson: '', // 联系人
        contactPhone: '', //联系方式
        companyCode: '', //客户公司编码
        customerName: ''
      }
    }
  },
  mounted() {},
  methods: {
    onOpen(args) {
      args.preventFocus = true
    },
    //查询客户公司名称下拉接口
    postCriteriaQuery() {
      this.$API.supplierCoordination.postNewBuyerCriteriaQuery().then((res) => {
        // console.log(res,"222");
        this.companyList = res.data
      })
    },
    // 初始化
    dialogInit(entryInfo) {
      this.dialogTitle = entryInfo.title
      if (this.dialogTitle === this.$t('新增')) {
        this.ruleForm = {
          companyCode: '', //客户公司编码
          companyName: '', //客户公司名称
          customerName: '', //客户公司名称下拉的字段
          contactPerson: '', //联系人
          contactPhone: '', //联系电话
          logisticsCompanyName: '' //物流公司名称
        }
        this.postCriteriaQuery()
      }
      if (this.dialogTitle === this.$t('编辑')) {
        // console.log(entryInfo,"444");
        this.ruleForm = {
          id: entryInfo.row.id,
          companyCode: entryInfo.row.companyCode,
          companyName: entryInfo.row.companyName,
          contactPerson: entryInfo.row.contactPerson,
          contactPhone: entryInfo.row.contactPhone,
          logisticsCompanyName: entryInfo.row.logisticsCompanyName
        }
        this.postCriteriaQuery()
      }
      this.$refs.dialog.ejsRef.show()
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            companyCode: this.ruleForm.companyCode, //客户公司编码
            // companyName:this.ruleForm.customerName,  //客户公司名称
            companyName: this.ruleForm.companyName, //客户公司名称
            contactPerson: this.ruleForm.contactPerson, //联系人姓名
            contactPhone: this.ruleForm.contactPhone, //联系人电话
            logisticsCompanyName: this.ruleForm.logisticsCompanyName //物流公司名称
          }
          if (this.dialogTitle === this.$t('编辑')) {
            params.id = this.ruleForm.id
            this.$API.supplierCoordination.NewPurOrderSave(params).then(() => {
              this.$refs.dialog.ejsRef.hide()
              this.$toast({ content: this.$t('编辑成功'), type: 'success' })
              this.$emit('dilogData')
            })
          }
          if (this.dialogTitle === this.$t('新增')) {
            this.$API.supplierCoordination.NewPurOrderSave(params).then(() => {
              this.$refs.dialog.ejsRef.hide()
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$emit('dilogData')
            })
          }
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 下拉框事件
    handleBusinessChange(e) {
      // console.log(e,"2222333");
      this.ruleForm.companyName = e.itemData.customerName
    }
  }
}
</script>
<style lang="scss" scoped></style>
