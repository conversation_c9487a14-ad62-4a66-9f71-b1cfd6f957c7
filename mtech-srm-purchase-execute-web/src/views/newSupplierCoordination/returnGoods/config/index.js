import { i18n } from '@/main.js'
import $utils from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName } from '@/utils/utils'

export const columnObj = {
  // 供方头视图
  headColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '200',
      field: 'vmiOrderCode', //vmiOrderCode
      headerText: i18n.t('VMI退货单号'),
      cellTools: []
    },
    {
      width: '80',
      field: 'status',
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map',
        //  status状态: 0:新建   1:待确认/已提交   2:已接收/待质检/已确认  8:已完成  9:已取消
        map: [
          { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
          { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
          { value: 2, text: i18n.t('已确认'), cssClass: 'col-inactive' },
          { value: 8, text: i18n.t('已完成'), cssClass: 'col-inactive' },
          { value: 9, text: i18n.t('已取消'), cssClass: 'col-inactive' }
        ]
      },
      searchOptions: {
        elementType: 'multi-select',
        operator: 'in',
        multiple: true,
        dataSource: [
          { label: i18n.t('新建'), value: 0 },
          { label: i18n.t('待确认'), value: 1 },
          { label: i18n.t('已确认'), value: 2 },
          { label: i18n.t('已完成'), value: 8 },
          { label: i18n.t('已取消'), value: 9 }
        ],
        fields: { text: 'label', value: 'value' }
      }
      // cellTools: [
      //   {
      //     id: "accept",
      //     icon: "a-icon_MultipleChoice_on",
      //     title: i18n.t("接受"),
      //     visibleCondition: (data) => {
      //       let isShow = false;
      //       if (data.status == 1 ) {
      //         isShow = true;
      //       }
      //       return isShow;
      //     },
      //   },
      // {
      //   id: "cancel",
      //   icon: "icon_list_recall",
      //   title: i18n.t("退回"),
      //   visibleCondition: (data) => {
      //     let isShow = false;
      //     if (data.status == 1 ) {
      //       isShow = true;
      //     }
      //     return isShow;
      //   },
      // },
      // ],
    },
    {
      width: '260',
      field: 'siteCode',
      headerText: i18n.t('工厂'),
      searchOptions: MasterDataSelect.factorySupplierAddress,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.siteCode, data?.siteName)
      }
    },
    {
      width: '0',
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'supplierCode',
      headerText: i18n.t('供应商'),
      searchOptions: MasterDataSelect.supplierSu,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
      }
    },
    {
      width: '0',
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'vmiWarehouseCode',
      headerText: i18n.t('VMI仓'),
      searchOptions: MasterDataSelect.vmiWarehouseSteel,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.vmiWarehouseCode, data?.vmiWarehouseName)
      }
    },
    {
      width: '0',
      field: 'vmiWarehouseName',
      headerText: i18n.t('VMI仓名称'),
      ignore: true
    },
    {
      width: '170',
      field: 'vmiWarehouseAddress', //vmiWarehouseAddress
      headerText: i18n.t('货品地址')
    },
    {
      width: '150',
      field: 'createTime', //createTime
      headerText: i18n.t('制单日期'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      }
    },
    {
      width: '90',
      field: 'createUserName', //createUserName
      headerText: i18n.t('制单人')
    }
  ],
  // 供方明细视图
  detailedColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '200',
      field: 'vmiOrderCode', //vmiOrderCode
      headerText: i18n.t('VMI退货单号'),
      cellTools: []
    },
    {
      width: '80',
      field: 'status',
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map',
        //  status状态: 0:新建   1:待确认/已提交   2:已接收/待质检/已确认  8:已完成  9:已取消
        map: [
          { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
          { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
          { value: 2, text: i18n.t('已确认'), cssClass: 'col-inactive' },
          { value: 8, text: i18n.t('已完成'), cssClass: 'col-inactive' },
          { value: 9, text: i18n.t('已取消'), cssClass: 'col-inactive' }
        ]
      },
      searchOptions: {
        elementType: 'multi-select',
        operator: 'in',
        multiple: true,
        dataSource: [
          { label: i18n.t('新建'), value: 0 },
          { label: i18n.t('待确认'), value: 1 },
          { label: i18n.t('已确认'), value: 2 },
          { label: i18n.t('已完成'), value: 8 },
          { label: i18n.t('已取消'), value: 9 }
        ],
        fields: { text: 'label', value: 'value' }
      }
    },
    {
      width: '70',
      field: 'rowNum', //rowNum
      headerText: i18n.t('行号')
    },
    // {
    //   width: "115",
    //   field: "itemCode", //supplierCode
    //   headerText: i18n.t("物料编码"),
    // },
    // {
    //   width: "395",
    //   field: "itemName", //supplierName
    //   headerText: i18n.t("物料名称"),
    // },
    {
      width: '95',
      field: 'stockType', //stockType   订单操作的库存属性分类 0合格库存 1待检库存 2不合格库存
      headerText: i18n.t('库存状态'),
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('合格库存'),
          1: i18n.t('待检库存'),
          3: i18n.t('不合格库存')
        }
      }
    },
    {
      width: '100',
      field: 'batchCode', //batchCode
      headerText: i18n.t('卷号')
    },
    {
      width: '95',
      field: 'count', //count
      headerText: i18n.t('退货数量')
    },
    {
      width: '95',
      field: 'checkCount', //checkCount
      headerText: i18n.t('实收数量')
    },
    {
      width: '75',
      field: 'itemUnit', //itemUnit
      headerText: i18n.t('单位')
    },
    {
      width: '80',
      field: 'purchaseGroupName', //purchaseGroupName
      headerText: i18n.t('采购组')
    },
    {
      width: '80',
      field: 'remark', //remark
      headerText: i18n.t('行备注')
    },
    {
      width: '140',
      field: 'orderCode', //orderCode
      headerText: i18n.t('关联采购订单号')
    },
    {
      width: '150',
      field: 'lineNo', //lineNo
      headerText: i18n.t('关联采购订单行号')
    },
    {
      width: '260',
      field: 'siteCode',
      headerText: i18n.t('工厂'),
      searchOptions: MasterDataSelect.factorySupplierAddress,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.siteCode, data?.siteName)
      }
    },
    {
      width: '0',
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'supplierCode',
      headerText: i18n.t('供应商'),
      searchOptions: MasterDataSelect.supplierSu,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
      }
    },
    {
      width: '0',
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'vmiWarehouseCode',
      headerText: i18n.t('VMI仓'),
      searchOptions: MasterDataSelect.vmiWarehouseSteel,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.vmiWarehouseCode, data?.vmiWarehouseName)
      }
    },
    {
      width: '0',
      field: 'vmiWarehouseName',
      headerText: i18n.t('VMI仓名称'),
      ignore: true
    },
    {
      width: '170',
      field: 'vmiWarehouseAddress', //vmiWarehouseAddress
      headerText: i18n.t('送货地址')
    },
    {
      width: '150',
      field: 'createTime', //createTime
      headerText: i18n.t('制单日期'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      },
      valueConverter: {
        type: 'function',
        filter: (data) => {
          return $utils.formateTime(new Date(+data), 'YYYY-mm-dd HH:MM:SS')
        }
      }
    },
    {
      width: '95',
      field: 'createUserName', //createUserName
      headerText: i18n.t('制单人')
    }
  ]
}
export const createColumnObj = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('PO')
  },
  {
    width: '150',
    field: 'lineNo',
    headerText: i18n.t('PO行')
  },
  {
    width: '150',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  // {
  //   width: "150",
  //   field: "itemCode",
  //   headerText: i18n.t("物料编码"),
  // },
  // {
  //   width: "150",
  //   field: "itemName",
  //   headerText: i18n.t("物料名称"),
  // },
  {
    width: '150',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓编码')
  },
  {
    width: '150',
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓名称')
  },
  {
    width: '150',
    field: 'purchaseGroupName',
    headerText: i18n.t('采购组')
  },
  {
    width: '150',
    field: 'batchCode',
    headerText: i18n.t('卷号')
  },
  {
    width: '150',
    field: 'count',
    headerText: i18n.t('卷重')
  },
  {
    width: '150',
    field: 'itemUnit',
    headerText: i18n.t('单位')
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  }
]
