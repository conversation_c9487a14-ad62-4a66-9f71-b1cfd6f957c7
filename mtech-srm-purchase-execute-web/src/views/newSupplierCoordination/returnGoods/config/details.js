// VMI供方退货详情页
import { i18n } from '@/main.js'
const numberFields = ['normalNumber1', 'normalNumber2', 'resNumber']
var wholeObj = {}
numberFields.forEach((item) => {
  wholeObj[`${item}Ele`] = null
  wholeObj[`${item}Obj`] = null
})
import { TextBox, NumericTextBox } from '@syncfusion/ej2-inputs'
var commonEdit = (params) => {
  // type:单元格的类型 等于ej2包含的类型，默认字符串类型
  // field: 列的field，必填
  // pld：placeholder，可不填
  // canEdit：是否可以编辑，默认可以
  // required: 是否必填，默认非必填。。必填时给cssClass赋值isRequired
  // callback: 回调（按照业务逻辑来）
  // calc：计算参数，仅举例（按照业务逻辑来）
  let {
    type = 'stringedit',
    field,
    pld,
    canEdit = true,
    required = false,
    // callback,
    calc
  } = params
  return {
    create: () => {
      wholeObj[`${field}Ele`] = document.createElement('input')
      return wholeObj[`${field}Ele`]
    },
    read: () => {
      return wholeObj[`${field}Obj`].value
    },
    destroy: () => {
      wholeObj[`${field}Obj`].destroy()
    },
    write: (args) => {
      // console.log(args,77790000);
      switch (type) {
        case 'stringedit':
          wholeObj[`${field}Obj`] = new TextBox({
            enabled: canEdit,
            cssClass: required ? 'isRequired' : null,
            value: args.rowData[args.column.field],
            placeholder: pld,
            floatLabelType: 'Never'
            // change: (e) => {
            //   // console.log(i18n.t("我改变了"), calc, e, wholeObj);
            // },
          })
          break
        case 'numericedit':
          wholeObj[`${field}Obj`] = new NumericTextBox({
            enabled: canEdit,
            cssClass: required ? 'isRequired' : null,
            value: args.rowData[args.column.field],
            placeholder: pld,
            floatLabelType: 'Never',
            min: 0,
            change: (e) => {
              // console.log(i18n.t("我改变了"), calc, e, wholeObj);
              if (calc && calc.length) {
                // 以简单乘法计算为例
                if (wholeObj[`${calc[0]}Obj`] && wholeObj[`${calc[1]}Obj`]) {
                  wholeObj[`${calc[1]}Obj`].value = wholeObj[`${calc[0]}Obj`].value * e.value
                }
              }
            }
          })
          break
        default:
          break
      }
      wholeObj[`${field}Obj`].appendTo(wholeObj[`${field}Ele`])
    }
  }
}
export const headColumn = [
  {
    field: 'rowNum', // 行号  rowNum
    headerText: i18n.t('行号')
  },
  {
    field: 'itemCode', // 物料编码   itemCode
    headerText: i18n.t('物料编码'),
    allowEditing: false
  },
  {
    field: 'itemName', // 物料描述    itemName
    headerText: i18n.t('物料名称'),
    allowEditing: false
  },
  {
    field: 'stockType', // 库存状态   //stockType   0合格库存 1待检库存 2不合格库存
    headerText: i18n.t('库存状态'),
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('合格库存'),
        1: i18n.t('待检库存'),
        3: i18n.t('不合格库存')
      }
    }
  },
  {
    field: 'batchCode', // 卷号    batchCode
    headerText: i18n.t('卷号'),
    allowEditing: false
  },
  {
    field: 'count', // 退货数量   count
    headerText: i18n.t('退货数量'),
    allowEditing: false
  },
  {
    field: 'checkCount',
    headerText: i18n.t('实退数量'),
    edit: commonEdit({
      type: 'numericedit',
      field: 'normalNumber2',
      pld: i18n.t('请输入'),
      calc: ['normalNumber1', 'resNumber']
    })
  },
  {
    field: 'itemUnit', // 单位  itemUnit
    headerText: i18n.t('单位'),
    allowEditing: false
  },
  {
    field: 'purchaseGroupName', //   purchaseGroupName
    headerText: i18n.t('采购组'),
    allowEditing: false
  },
  {
    field: 'remark', // 行备注   remark
    headerText: i18n.t('行备注'),
    allowEditing: false
  },
  {
    field: 'orderCode', // 关联采购订单号   orderCode
    headerText: i18n.t('关联采购订单号'),
    allowEditing: false
  },
  {
    field: 'lineNo', // 关联采购订单行号   //lineNo
    headerText: i18n.t('关联采购订单行号'),
    allowEditing: false
  }
]
