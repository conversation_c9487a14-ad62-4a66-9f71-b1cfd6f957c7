// 供应商退货列表
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :permission-obj="permissionObj"
      :current-tab="0"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <create-dialog
      v-if="deliveryShow"
      ref="child"
      @handleAddDialogShow="handleAddDialogShow"
      @handleDialogShow="handleDialogShow"
    ></create-dialog>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { columnObj } from './config/index.js'
export default {
  components: {
    // deliveryDialog,
    createDialog: require('./components/createDialog').default
  },
  data() {
    return {
      pageConfig: [
        {
          tab: { title: this.$t('头视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          dataPermission: 'a',
          permissionCode: 'T_02_0126',
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'create',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('创建vmi冲销单'),
                  permission: ['O_02_1697']
                }
              ],
              ['Filter', 'Refresh', 'Setting'] //筛选  刷新   设置
            ]
          },
          grid: {
            columnData: columnObj.headColumn,
            asyncConfig: {
              url: this.$API.supplierCoordination.postNewVMIReturnQuery, //供方退货接口
              // recordsPosition: "data",
              rules: [
                {
                  field: 'vmi_order_type',
                  // type:"string",
                  operator: 'equal',
                  value: 3
                }
              ]
            },
            frozenColumns: 1
          }
        },
        {
          tab: { title: this.$t('明细视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          dataPermission: 'a',
          permissionCode: 'T_02_0127',
          toolbar: {
            tools: [
              [
                {
                  id: 'export1',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: 'dc4b4eb6-febd-4f96-8cfd-596cf6321f87',
          grid: {
            columnData: columnObj.detailedColumn,
            asyncConfig: {
              url: this.$API.supplierCoordination.postNewVMIReturnQueryDetail, //供方明细接口接口
              // recordsPosition: "data",
              rules: [
                {
                  field: 'vmi_order_type',
                  // type:"string",
                  operator: 'equal',
                  value: 3
                }
              ]
            },
            frozenColumns: 1
          }
        }
      ],
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0126' },
          { dataPermission: 'b', permissionCode: 'T_02_0127' }
        ]
      },
      deliveryShow: false,

      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    // 跳转详情
    handleClickCellTitle(e) {
      let obj = {
        tabIndex: '',
        id: '',
        status: ''
      }
      if (e.tabIndex === 0) {
        obj.tabIndex = 0
      } else {
        obj.tabIndex = 1
      }
      if (e.field === 'vmiOrderCode') {
        obj.id = e.data.vmiOrderId ?? e.data.id
        obj.status = e.data.status
        this.redirectPage('purchase-execute/new-supplier-out-details', obj)
      }
    },
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    handleDialogShow(flag) {
      this.deliveryShow = flag
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 表格上方操作
    handleClickToolBar(e) {
      if (e.toolbar.id === 'create') {
        this.deliveryShow = true
      }
      if (e.toolbar.id === 'accept') {
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('供方接收'),
            message: this.$t('确认接收吗？')
          },
          success: () => {
            let ids = _selectRows.map((item) => item.id)
            let switAccept = true
            _selectRows.map((item) => {
              if (item.status != 1) {
                switAccept = false
              }
            })
            if (switAccept == false) {
              this.$toast({
                content: this.$t('请选择待确认状态'),
                type: 'warning'
              })
              return
            }
            this.$API.purchaseCoordination
              .postBuyerWarehousingReturnConfirm({ ids })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('接收成功'),
                    type: 'success'
                  })
                  // 刷新当前 Grid
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
          }
        })
      } else if (e.toolbar.id === 'InvoiceImport') {
        // 供方退回
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('供方退回'),
            message: this.$t('确认退回吗？')
          },
          success: () => {
            let ids = _selectRows.map((item) => item.id)
            let switInvoiceImport = true
            _selectRows.map((item) => {
              if (item.status != 1) {
                switInvoiceImport = false
              }
            })
            if (switInvoiceImport == false) {
              this.$toast({
                content: this.$t('请选择待确认状态'),
                type: 'warning'
              })
              return
            }
            this.$API.purchaseCoordination
              .postBuyerWarehousingReturnRejected({ ids })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('退回成功'),
                    type: 'success'
                  })
                  // 刷新当前 Grid
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
          }
        })
      } else if (e.toolbar.id === 'refreshDataByLocal') {
        // 刷新
      } else if (e.toolbar.id === 'export1') {
        let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let obj = JSON.parse(
          sessionStorage.getItem('dc4b4eb6-febd-4f96-8cfd-596cf6321f87')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnObj.detailedColumn.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        let params = {
          page: { current: 1, size: 10000 },
          rules: rule.rules || []
        }
        params.rules = [
          ...params.rules,
          {
            field: 'vmi_order_type',
            // type:"string",
            operator: 'equal',
            value: 3
          }
        ]
        this.$store.commit('startLoading')
        this.$API.purchaseCoordination
          .postSupplierWarehousingReturnExport(params, field)
          .then((res) => {
            this.$store.commit('endLoading')
            const fileName = getHeadersFileName(res)

            download({ fileName: `${fileName}`, blob: res.data })
          })
      }
    },

    // 行内操作
    handleClickCellTool(e) {
      if (e.tool.id == 'accept') {
        this.$dialog({
          data: {
            title: this.$t('接受'),
            message: this.$t('确认接受吗？')
          },
          success: () => {
            this.confirm(e.data.id)
          }
        })
      } else if (e.tool.id == 'cancel') {
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认准确吗？')
          },
          success: () => {
            this.rejected(e.data.id)
          }
        })
      }
    },

    // 接收
    confirm(data) {
      let obj = {
        ids: [data]
      }
      this.$API.purchaseCoordination.postBuyerWarehousingReturnConfirm(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('接收成功'), type: 'success' })
          // 刷新当前 Grid
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('接收失败'), type: 'error' })
        }
      })
    },
    // 退回
    rejected(data) {
      let obj = {
        ids: [data]
      }
      this.$API.purchaseCoordination.postBuyerWarehousingReturnRejected(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('退回成功'), type: 'success' })
          // 刷新当前 Grid
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('退回失败'), type: 'error' })
        }
      })
    },

    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    }
  }
}
</script>

<style style="scss" scoped>
.full-height {
  height: 100%;
}
</style>
