// 供应商退货详情页
<template>
  <div class="full-height pt20">
    <!-- 头部信息 -->
    <top-info
      class="flex-keep"
      :header-info="headerInfo"
      @goBack="goBack"
      @doExpand="doExpand"
      @acceptBtn="acceptBtn"
      @rejiectBtn="rejiectBtn"
    ></top-info>
    <div slot="slot-0" class="full-height" v-show="isShowPendingOrder">
      <mt-template-page
        ref="templateRef"
        :current-tab="0"
        :hidden-tabs="false"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
      >
        <div slot="slot-1" class="full-height">
          <logistics-info :logistics-data="logisticsData"></logistics-info>
        </div>
      </mt-template-page>
    </div>
  </div>
</template>

<script>
import { headColumn } from './config/details.js'
export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue'),
    LogisticsInfo: () => import('./components/logisticsInfo.vue')
  },
  data() {
    return {
      isShowPendingOrder: true, //
      pageConfig: [
        {
          tab: { title: this.$t('物料信息') },
          useBaseConfig: false, // 使用组件中的toolbar配置
          useToolTemplate: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[], ['Filter', 'Refresh', 'Setting']]
          },
          grid: {
            columnData: headColumn,

            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true
            },
            dataSource: [],
            // asyncConfig: {
            //   url: "/srm-purchase-execute/tenant/pe/business/configs",
            //   recordsPosition: "data",
            // },
            frozenColumns: 1
          }
        }
        // {
        //   tab: { title: this.$t("物流信息") },
        // },
      ],
      // 物流信息数据
      logisticsData: {
        transportType: 1
      },
      headerInfo: {} //头部数据展示
    }
  },
  mounted() {
    this.getDetailData() //详情接口
  },
  methods: {
    // tab切换
    handleSelectTab(e) {
      if (e === 0) {
        this.isShowPendingOrder = true
      } else {
        this.isShowPendingOrder = false
      }
    },
    // 跳转详情
    handleClickCellTitle() {},
    // 表格头部操作
    handleClickToolBar(e) {
      if (e.toolbar.id === 'refreshDataByLocal') {
        // 刷新
      }
    },
    // 行内操作
    handleClickCellTool() {},
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    // 展开收起的hearder
    doExpand() {
      this.$refs.templateRef?.resetGridHeight && this.$refs.templateRef.resetGridHeight()
    },
    // 供方接收
    acceptBtn() {
      this.$dialog({
        data: {
          title: this.$t('供方接收'),
          message: this.$t('是否确认接收')
        },
        success: () => {
          let params = {
            ids: [this.$route.query.id]
          }
          this.$API.purchaseCoordination.postBuyerWarehousingReturnConfirm(params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('接收成功'), type: 'success' })
              this.$router.go(-1)
              // 刷新当前 Grid
              // this.$refs.templateRef.refreshCurrentGridData();
            }
          })
        }
      })
    },
    // 供方退回
    rejiectBtn() {
      this.$dialog({
        data: {
          title: this.$t('供方退回'),
          message: this.$t('是否确认退回？')
        },
        success: () => {
          let params = {
            ids: [this.$route.query.id]
          }
          this.$API.purchaseCoordination.postBuyerWarehousingReturnRejected(params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('退回成功'), type: 'success' })
              this.$router.go(-1)
              // 刷新当前 Grid
              // this.$refs.templateRef.refreshCurrentGridData();
            }
          })
        }
      })
    },
    // 详情数据接口
    getDetailData() {
      let obj = {
        id: this.$route.query.id
      }
      this.$API.purchaseCoordination.postNewBuyerWarehousingReturnDetail(obj).then((res) => {
        if (res.code === 200) {
          this.headerInfo = res.data
          this.pageConfig[0].grid.dataSource = res.data.vmiOrderItemResponses
          this.logisticsData = res.data.orderLogistic[0] //订单物流信息
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .flex-keep {
  margin-top: 10px;
}
.full-height {
  height: 100%;
}
</style>
