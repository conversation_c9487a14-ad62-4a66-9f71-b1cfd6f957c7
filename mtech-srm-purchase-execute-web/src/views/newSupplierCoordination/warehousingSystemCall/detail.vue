<template>
  <div class="detail-container">
    <div class="header">
      <div class="operate-bar">
        <span class="op-item mt-flex" @click="backToBusinessConfig">
          {{ $t('返回') }}
        </span>
      </div>
    </div>
    <div class="form-warp">
      <mt-form ref="formInstance" class="form-box" :model="formModel">
        <mt-row :gutter="24">
          <mt-col :span="4">
            <!-- 单号 -->
            <mt-form-item class="form-item" :label="$t('单号')" label-style="top" prop="id">
              <mt-input v-model="formModel.id" :disabled="true"></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="4">
            <!-- 接口状态 -->
            <mt-form-item
              class="form-item"
              :label="$t('接口状态')"
              label-style="top"
              prop="requestStatus"
            >
              <span :style="requestStatusStyle" :disabled="true">
                {{ formModel.requestStatus }}
              </span>
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <!-- 供应商 -->
            <mt-form-item
              class="form-item"
              :label="$t('供应商名称')"
              label-style="top"
              width="100%"
              style="width: 100%"
              prop="supplierName"
            >
              <mt-input v-model="supplierCodeName" style="width: 100%" :disabled="true"></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="4">
            <!-- 集成类型 -->
            <mt-form-item
              class="form-item"
              :label="$t('集成类型')"
              label-style="top"
              prop="collectType"
            >
              <mt-input v-model="formModel.collectType" :disabled="true"></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="4">
            <!-- 调用时间 -->
            <mt-form-item
              class="form-item"
              :label="$t('调用时间')"
              label-style="top"
              prop="createTime"
            >
              <mt-input v-model="formModel.createTime" :disabled="true"></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="23">
            <!-- 接口失败原因 -->
            <mt-form-item
              class="form-item"
              :label="$t('接口失败原因')"
              label-style="top"
              style="width: 100%"
              prop="failureReason"
            >
              <span :style="requestStatusStyle" :disabled="true">{{
                formModel.failureReason
              }}</span>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
    <div class="table-content">
      <div class="table-title">{{ $t('物料信息') }}</div>
      <mt-template-page
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="templateConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
      >
      </mt-template-page>
    </div>
  </div>
</template>
<script>
import { ColumnData } from './config'

export default {
  data() {
    return {
      supplierList: [],
      isEdit: false,
      disabled: false, //禁用
      //表单数据
      formModel: {
        id: '',
        batchId: '',
        requestStatus: 0,
        supplierCode: '',
        supplierName: '',
        submitTime: '',
        failureReason: '',
        rectificationCode: '',
        createTime: '',
        collectType: ''
      },
      templateConfig: [
        {
          // title: this.$t('明细信息'),
          useToolTemplate: false,
          toolbar: {
            tools: [[], []]
          },
          grid: {
            height: 500,
            allowPaging: true,
            columnData: ColumnData,
            lineIndex: 0,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/inventorySync/recordDetail',
              recordsPosition: 'data.records',
              page: { current: 1, size: 20 },
              defaultRules: [
                {
                  label: this.$t('批次ID'),
                  field: 'batchId',
                  type: 'string',
                  operator: 'equal',
                  value: this.$route?.query.id
                }
              ]
            }
          }
        }
      ],
      factoryData: []
    }
  },
  computed: {
    form() {
      return this.$route?.query
    },
    supplierCodeName() {
      return this.formModel.supplierCode + '-' + this.formModel.supplierName
    },
    requestStatusStyle() {
      let obj = {
        display: 'flex',
        alignItems: 'center',
        height: '100%',
        width: '100%',
        fontWeight: 800,
        background: 'linear-gradient(179deg, white, rgb(235, 235, 235))'
      }
      if (this.formModel.requestStatus === '成功') {
        return Object.assign({}, { color: 'green' }, obj)
      }
      return Object.assign({}, { color: 'red' }, obj)
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      let obj = JSON.parse(sessionStorage.getItem('warehouse-system-call'))
      console.log(obj)
      for (let key in obj) {
        if (this.formModel[key] !== undefined) {
          this.formModel[key] = obj[key]
        }
      }
    },
    handleClickToolBar(e) {
      console.log(e)
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
    },
    //返回列表页
    backToBusinessConfig() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/.mt-form-item {
  width: calc(33% - 30px);
  min-width: 200px;
  display: inline-flex;
  margin-left: 10px;
  margin-right: 20px;
  .mt-input {
    width: 100%;
  }
}
.detail-container {
  background: #fff;
  height: 100%;
  .header {
    height: 40px;
    display: flex;
    justify-content: end;
    margin-bottom: 16px;
    .title {
      height: 100%;
      line-height: 40px;
      font-size: 20px;
      font-weight: 600;
      color: #333;
      padding: 5px 10px;
    }
    .operate-bar {
      height: 100%;
      float: right;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: #4f5b6d;

      .op-item {
        cursor: pointer;
        align-items: center;
        margin-right: 20px;
        align-items: center;
        background-color: rgba(0, 0, 0, 0);
        border-color: rgba(0, 0, 0, 0);
        color: #00469c;
      }
    }
  }
  .table-content {
    padding: 10px;
    .table-title {
      font-size: 16px;
      font-weight: 600;
    }
  }
}
</style>
