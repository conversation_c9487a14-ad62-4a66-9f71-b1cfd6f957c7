import { i18n } from '@/main.js'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant'
import {
  apiStatusOptions,
  itemStatusOptions,
  collectTypeOptions,
  ItemTagDescOptions
} from './constant'

export const timeDate = (dataKey, hasTime) => {
  const template = () => {
    return {
      template: Vue.component('date', {
        template: `<div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div><div>{{data[dataKey] | dateFormat}}</div></div>`,
        data: function () {
          return { data: {}, dataKey, hasTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
export const columnObj = {
  headColumn: [
    {
      width: '120',
      field: 'requestStatus',
      headerText: i18n.t('接口状态'),
      searchOptions: {
        elementType: 'multi-select',
        showSelectAll: true,
        multiple: true,
        operator: 'in',
        dataSource: apiStatusOptions
      },
      template: () => {
        const template = {
          template: `<span :style="statusStyle">{{ data.requestStatus }}</span>`,
          data() {
            return {
              data: {}
            }
          },
          computed: {
            statusStyle() {
              if (this.data.requestStatus === '成功') {
                return { color: 'green', fontWeight: 800 }
              }
              return { color: 'red', fontWeight: 800 }
            }
          }
        }
        return { template }
      }
    },
    {
      width: '200',
      field: 'id',
      headerText: i18n.t('批次ID'),
      cellTools: []
    },
    {
      width: '200',
      field: 'createTime',
      headerText: i18n.t('调用时间'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      }
    },
    {
      width: '160',
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      searchOptions: MasterDataSelect.supplierAll
    },
    {
      width: '250',
      field: 'supplierName',
      headerText: i18n.t('供应商名称')
    },
    {
      width: '120',
      field: 'collectType',
      headerText: i18n.t('集成类型'),
      searchOptions: {
        elementType: 'multi-select',
        multiple: true,
        showSelectAll: true,
        operator: 'in',
        dataSource: collectTypeOptions
      }
    },
    // {
    //   width: '120',
    //   field: 'syncType',
    //   headerText: i18n.t('数据同步类型'),
    //   ignore: true
    // },
    {
      width: '300',
      field: 'failureReason',
      headerText: i18n.t('接口失败原因'),
      template: () => {
        const template = {
          template: `<span :style="failContentColor">{{ data.failureReason }}</span>`,
          data() {
            return {
              data: {}
            }
          },
          computed: {
            failContentColor() {
              if (this.data.requestStatus === '成功') {
                return { color: 'green', fontWeight: 800 }
              }
              return { color: 'red', fontWeight: 800 }
            }
          }
        }
        return { template }
      }
    },
    {
      width: 'auto',
      field: 'remark',
      headerText: i18n.t('备注')
    }
  ],
  detailedColumn: [
    {
      width: '120',
      field: 'requestStatus',
      headerText: i18n.t('接口状态'),
      searchOptions: {
        elementType: 'multi-select',
        showSelectAll: true,
        multiple: true,
        operator: 'in',
        dataSource: apiStatusOptions
      },
      template: () => {
        const template = {
          template: `<span :style="statusStyle">{{ data.requestStatus }}</span>`,
          data() {
            return {
              data: {}
            }
          },
          computed: {
            statusStyle() {
              if (this.data.requestStatus === '成功') {
                return { color: 'green', fontWeight: 800 }
              }
              return { color: 'red', fontWeight: 800 }
            }
          }
        }
        return { template }
      }
    },
    {
      width: '200',
      field: 'batchId',
      headerText: i18n.t('批次ID'),
      cellTools: []
    },
    {
      width: '120',
      field: 'itemStatus',
      headerText: i18n.t('行验证状态'),
      searchOptions: {
        elementType: 'select',
        operator: 'equal',
        dataSource: itemStatusOptions
      },
      template: () => {
        const template = {
          template: `<span :style="statusStyle">{{ data.itemStatus }}</span>`,
          data() {
            return {
              data: {}
            }
          },
          computed: {
            statusStyle() {
              if (this.data.itemStatus === '通过') {
                return { color: 'green', fontWeight: 800 }
              }
              return { color: 'red', fontWeight: 800 }
            }
          }
        }
        return { template }
      }
    },
    {
      width: '200',
      field: 'createTime',
      headerText: i18n.t('调用时间'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      },
      type: 'YYYY-mm-dd HH:MM:SS'
    },
    {
      width: '160',
      field: 'supplierCode', //supplierCode
      headerText: i18n.t('供应商编码'),
      searchOptions: {
        ...MasterDataSelect.supplierAll
      }
    },
    {
      width: '200',
      field: 'supplierName',
      headerText: i18n.t('供应商名称')
    },
    {
      width: '160',
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      searchOptions: {
        ...MasterDataSelect.factorySupplierAddress
      }
    },
    {
      width: '200',
      field: 'siteName',
      headerText: i18n.t('工厂名称')
    },
    {
      width: '150',
      field: 'itemCode',
      headerText: i18n.t('TCL物料编码')
    },
    {
      width: '200',
      field: 'itemName',
      headerText: i18n.t('TCL物料名称')
    },
    {
      width: '160',
      field: 'supplierItemCode',
      headerText: i18n.t('供方物料编码')
    },
    {
      width: '200',
      field: 'supplierItemName',
      headerText: i18n.t('供方物料名称')
    },
    {
      width: '160',
      field: 'supplierItemTagDesc',
      headerText: i18n.t('供方物料标识'),
      searchOptions: {
        elementType: 'multi-select',
        showSelectAll: true,
        operator: 'in',
        multiple: true,
        dataSource: ItemTagDescOptions
      }
    },
    {
      width: '120',
      field: 'inventoryQty',
      headerText: i18n.t('库存数量')
    },
    {
      width: '300',
      field: 'sourceId',
      headerText: i18n.t('供方系统数据ID')
    },
    // {
    //   width: '300',
    //   field: 'failureReason',
    //   headerText: i18n.t('失败原因'),
    //   template: () => {
    //     const template = {
    //       template: `<span :style="failContentColor">{{ data.failureReason }}</span>`,
    //       data() {
    //         return {
    //           data: {}
    //         }
    //       },
    //       computed: {
    //         failContentColor() {
    //           if (this.data.requestStatus === '成功') {
    //             return { color: 'green', fontWeight: 800 }
    //           }
    //           return { color: 'red', fontWeight: 800 }
    //         }
    //       }
    //     }
    //     return { template }
    //   }
    // }
    {
      width: '300',
      field: 'itemFailReason',
      headerText: i18n.t('行验证失败原因'),
      template: () => {
        const template = {
          template: `<span style="color: red">{{ data.itemFailReason }}</span>`,
          data() {
            return {
              data: {}
            }
          }
        }
        return { template }
      }
    }
  ]
}

export const ColumnData = [
  {
    width: '120',
    field: 'itemStatus',
    headerText: i18n.t('行验证状态'),
    searchOptions: {
      elementType: 'select',
      operator: 'equal',
      dataSource: itemStatusOptions
    },
    template: () => {
      const template = {
        template: `<span :style="statusStyle">{{ data.itemStatus }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        computed: {
          statusStyle() {
            if (this.data.itemStatus === '通过') {
              return { color: 'green', fontWeight: 800 }
            }
            return { color: 'red', fontWeight: 800 }
          }
        }
      }
      return { template }
    }
  },
  {
    width: '160',
    field: 'siteCode',
    headerText: i18n.t('工厂编码'),
    searchOptions: {
      ...MasterDataSelect.factorySupplierAddress
    }
  },
  {
    width: '200',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('TCL物料编码')
  },
  {
    width: '200',
    field: 'itemName',
    headerText: i18n.t('TCL物料名称')
  },
  {
    width: '160',
    field: 'supplierItemCode',
    headerText: i18n.t('供方物料编码')
  },
  {
    width: '200',
    field: 'supplierItemName',
    headerText: i18n.t('供方物料名称')
  },
  {
    width: '160',
    field: 'supplierItemTagDesc',
    headerText: i18n.t('供方物料标识'),
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in',
      multiple: true,
      dataSource: ItemTagDescOptions
    }
  },
  {
    width: '120',
    field: 'inventoryQty',
    headerText: i18n.t('库存数量')
  },
  {
    width: '300',
    field: 'sourceId',
    headerText: i18n.t('供方系统数据ID')
  },
  {
    width: '300',
    field: 'itemFailReason',
    headerText: i18n.t('行验证失败原因'),
    template: () => {
      const template = {
        template: `<span style="color: red">{{ data.itemFailReason }}</span>`,
        data() {
          return {
            data: {}
          }
        }
      }
      return { template }
    }
  },
  {
    width: '300',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
