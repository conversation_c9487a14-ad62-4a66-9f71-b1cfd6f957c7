<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      current-tab="0"
      :permission-obj="permissionObj"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @rowDeselected="rowDeselecting"
    ></mt-template-page>
    <create-dialog
      v-if="deliveryShow"
      ref="child"
      @handleAddDialogShow="handleAddDialogShow"
      @handleDialogShow="handleDialogShow"
    ></create-dialog>
    <!-- 导入弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { columnObj } from './config/index.js'
import Vue from 'vue'
import utils from '@/utils/utils'
import { cloneDeep } from 'lodash'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    // deliveryDialog,
    createDialog: require('./components/createDialog').default,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  data() {
    return {
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        }
      },
      requestUrls: {
        templateUrlPre: 'supplierCoordination',
        templateUrl: 'vmiSteelExportExcel',
        uploadUrl: 'vmiSteelImportExcel'
      }, // 导入下载接口地址
      downTemplateParams: {
        flag: 0
      }, // 下载模板参数
      pageConfig: [
        {
          tab: { title: this.$t('列表视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          dataPermission: 'a',
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'export',
                  icon: 'icon_solid_Export',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columnObj.headColumn,
            lineIndex: 0,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/inventorySync/record',
              recordsPosition: 'data.records'
            },
            frozenColumns: 1
          }
        },
        {
          tab: { title: this.$t('明细视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          dataPermission: 'b',
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  title: this.$t('导出'),
                  id: 'export_detail',
                  icon: 'icon_solid_Export'
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columnObj.detailedColumn,
            dataSource: [],
            lineIndex: 0,
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/inventorySync/recordDetail',

              recordsPosition: 'data.records'
            },
            frozenColumns: 1
          }
        }
      ],
      deliveryShow: false,

      addDialogShow: false,
      dialogData: null
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('createDate')
  },
  methods: {
    // 弹框
    handleDialogShow(flag) {
      this.deliveryShow = flag
    },
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    serialize(data) {
      data.forEach((item) => {
        item.submitTime = Number(item.submitTime)
        item.confirmTime = Number(item.confirmTime)
      })
      return data
    },
    // create === 1  编辑
    // create === 0  详情
    // 跳转详情
    handleClickCellTitle(e) {
      if (e.field === 'vmiOrderCode' && [0, '0'].includes(e.tabIndex)) {
        let obj = {
          create: 0,
          id: e.data.id,
          key: utils.randomString()
        }
        // e.data.status === 0 新建状态可修改 传值status
        if (e.data.status === 0) obj.create = 1
        obj.status = e.data.status
        this.redirectPage('purchase-execute/new-supplier-warehousing-create', obj)
      } else if (e.field === 'id' || e.field === 'batchId') {
        sessionStorage.setItem('warehouse-system-call', JSON.stringify(e.data))
        this.$router.push({
          path: 'warehouse-system-call-detail',
          query: {
            id: e.field === 'id' ? e.data.id : e.data.batchId,
            date: new Date().getTime()
          }
        })
      }
    },
    // 头部操作
    handleClickToolBar(e) {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      if (e.toolbar.id === 'export') {
        this.handleClickExport(params)
        return
      }
      if (e.toolbar.id === 'export_detail') {
        this.handleClickExportDetail(params)
        return
      }
    },
    handleClickExport(param) {
      this.$store.commit('startLoading')
      this.$API.purchaseCoordination
        .supWarechaseSystemCallMainExport(param)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    handleClickExportDetail(param) {
      this.$store.commit('startLoading')
      this.$API.purchaseCoordination
        .supWarechaseSystemCallDetailExport(param)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    handleConfirm(row) {
      //打印排期列表
      console.log(row)
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认打印选中的条码需求吗?')
        },
        success: () => {
          this.$API.supplierCoordination.postOrderPrint(row).then((res) => {
            if (res?.data?.type === 'application/json') {
              const reader = new FileReader()
              reader.readAsText(res?.data, 'utf-8')
              reader.onload = function () {
                console.log('======', reader)
                const readerRes = reader.result
                const resObj = JSON.parse(readerRes)
                Vue.prototype.$toast({
                  content: resObj.msg,
                  type: 'error'
                })
              }

              return
            }
            const content = res.data
            this.pdfUrl = window.URL.createObjectURL(
              new Blob([content], { type: 'application/pdf' })
            )
            // window.open(this.pdfUrl);
            let date = new Date().getTime()
            let ifr = document.createElement('iframe')
            ifr.style.frameborder = 'no'
            ifr.style.display = 'none'
            ifr.style.pageBreakBefore = 'always'
            ifr.setAttribute('id', 'printPdf' + date)
            ifr.setAttribute('name', 'printPdf' + date)
            ifr.src = this.pdfUrl
            document.body.appendChild(ifr)
            this.doPrint('printPdf' + date)
            window.URL.revokeObjectURL(ifr.src)
          })
        }
      })
    },
    //复选框事件
    // rowSelecting(e) {
    //   if (e?.rowIndexes?.length < 1) return
    //   if (e.data instanceof Array) return
    //   // if (e?.data?.status != 2) {
    //   //   let mapArr = e.rowIndexes.filter((item) => {
    //   //     return item !== e.rowIndex;
    //   //   });
    //   //   if (!(e.data instanceof Array)) {
    //   //     console.log(e, mapArr);
    //   //     this.$nextTick(() => {
    //   //       this.$refs.templateRef
    //   //         .getCurrentUsefulRef()
    //   //         .ejsRef.selectRows(mapArr);
    //   //     });
    //   //   }
    //   //   return;
    //   // }
    //   //获取当前页所有的行
    //   let Obj = cloneDeep(
    //     this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getCurrentViewRecords()
    //   )
    //   Obj.map((item, i) => {
    //     item.index = i
    //   })
    //   //获取当前页勾选的值
    //   let currentSelect = cloneDeep(
    //     this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getSelectedRecords()
    //   )
    //   currentSelect.push(e.data)
    //   currentSelect = [...new Set(currentSelect)]
    //   if (Obj instanceof Array && Obj.length > 0) {
    //     let mapArr = []
    //     for (let i = 0; i < currentSelect.length; i++) {
    //       for (let j = 0; j < Obj.length; j++) {
    //         if (currentSelect[i]?.vmiOrderCode === Obj[j]?.vmiOrderCode) {
    //           mapArr.push(Obj[j])
    //         }
    //       }
    //     }
    //     mapArr = mapArr.map((item) => item.index)
    //     if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0 && mapArr.length > 0) {
    //       this.$refs.templateRef.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
    //     }
    //     console.log(mapArr)
    //   }
    // },
    //取消
    rowDeselecting(e) {
      if (e?.rowIndexes?.length < 1) return
      //获取当前页所有的行
      let Obj = cloneDeep(
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getCurrentViewRecords()
      )
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.templateRef
          .getCurrentUsefulRef()
          .ejsRef.getSelectedRecords()
          .map((e) => e.id)
      )
      let mapArr = []
      Obj.filter((item, index) => {
        if (item.vmiOrderCode !== e.data.vmiOrderCode && currentSelect.includes(item.id)) {
          console.log(item)
          mapArr.push(index)
        }
      })
      console.log(e, mapArr)
      if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0) {
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
      }
    },
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 获取dom打印方法 异步执行
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        this.pdfLoading = false
      }, 100)
    },
    handleClickCellTool(e) {
      let idOBj = {
        id: e.data.id
      }
      // 提交
      if (e.tool.id === 'submit') {
        // 参数值需要修改
        this.$dialog({
          data: {
            title: this.$t('提交'),
            message: this.$t('确认提交吗？')
          },
          success: () => {
            this.submit(idOBj)
          }
        })
      } else if (e.tool.id === 'cancel') {
        //取消
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消吗？')
          },
          success: () => {
            this.cancel(idOBj)
          }
        })
      }
    },
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },
    // 接收
    receive(id) {
      // console.log(data);
      let obj = {
        idList: id
      }
      this.$API.supplierCoordination.postReceiveConfirm(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({
            content: this.$t('操作失败'),
            type: 'console.error();'
          })
        }
      })
    },
    // 导入成功后，获取到的数据
    upExcelConfirm() {
      // this.$refs["uploadExcelRef"].refreshCurrentGridData();
      this.showUploadExcel(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
    },
    // 删除
    delete(data) {
      let obj = {
        ids: data.map((item) => item.id)
      }
      this.$API.supplierCoordination.postReceiveDelete(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({
            content: this.$t('删除失败'),
            type: 'console.error();'
          })
        }
      })
    },
    summitClick(data) {
      let obj = {
        ids: data.map((item) => item.id)
      }
      this.$API.supplierCoordination.postReceiveSubmitEvery(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({
            content: this.$t('提交失败'),
            type: 'console.error();'
          })
        }
      })
    },
    // 提交 用的与创建同一个接口
    submit(obj) {
      this.$API.supplierCoordination.postReceiveSubmit(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({
            content: this.$t('提交失败'),
            type: 'console.error();'
          })
        }
      })
    },
    // 取消
    cancel(obj) {
      this.$API.supplierCoordination.postReceiveCancel(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('取消成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({
            content: this.$t('取消失败'),
            type: 'console.error();'
          })
        }
      })
    },
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
