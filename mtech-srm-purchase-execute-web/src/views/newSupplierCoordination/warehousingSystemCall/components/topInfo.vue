<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- {{headerInfo}} -->
    <!-- statusCreate判断是不是编辑页 -->
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div :class="[statusToClass(headerInfo.status), 'mr20']" v-if="headerInfo.statusCreate">
        {{ headerInfo.status | statusFormat }}
      </div>
      <div class="infos mr20" v-if="headerInfo.createUserName">
        {{ $t('创建人：') }}{{ headerInfo.createUserName }}
      </div>
      <div class="infos" v-if="headerInfo.createTime">
        {{ $t('创建时间：') }}{{ headerInfo.createTime | dateFormat }}
        {{ headerInfo.submitTime | timeFormat }}
      </div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-if="this.$route.query.create == 1"
        @click="preservationBtn"
        >{{ $t('保存') }}</mt-button
      >
      <mt-button
        v-if="this.$route.query.create == 1"
        css-class="e-flat"
        :is-primary="true"
        @click="submitBtn"
        >{{ $t('提交') }}</mt-button
      >
      <div class="sort-box" @click="doExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item :label="$t('工厂')">
          <!-- :disabled="headerInfo.statusCreate" -->
          <mt-input v-model="headerInfo.siteName" :disabled="true"></mt-input>
        </mt-form-item>

        <mt-form-item :label="$t('供应商')">
          <mt-input v-model="headerInfo.supplierName" :disabled="true"></mt-input>
        </mt-form-item>
        <!-- <mt-form-item :label="$t('供应商')">
          <mt-input
            v-model="headerInfo.supplierName"
            :disabled="true"
            :placeholder="$t('供应商')"
          ></mt-input>
        </mt-form-item> -->
        <mt-form-item :label="$t('VMI仓')">
          <mt-input v-model="headerInfo.vmiWarehouseName" :disabled="true"></mt-input>
        </mt-form-item>
        <!-- <mt-form-item :label="$t('VMI仓名称')">
          <mt-input
            v-model="headerInfo.vmiWarehouseName"
            :disabled="true"
            :placeholder="$t('VMI仓名称')"
          ></mt-input>
        </mt-form-item> -->
        <mt-form-item :label="$t('送货地址')">
          <mt-input
            :disabled="true"
            v-model="headerInfo.vmiWarehouseAddress"
            :placeholder="$t('送货地址')"
          ></mt-input>
        </mt-form-item>

        <!-- 供方备注 -->
        <mt-form-item class="full-width" prop="remark" :label="$t('备注')" :show-message="false">
          <mt-input
            v-model="headerInfo.remark"
            :disabled="headerInfo.statusCreate"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { StatusText, StatusCssClass, DeliveryTypeOptions } from '../config/constant'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { debounce } from 'lodash'
export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      DeliveryTypeOptions,
      isExpand: true,
      detailType: '',
      rules: {
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }],
        vmiWarehouseAddress: [
          {
            required: true,
            message: this.$t('请输入送货地址'),
            trigger: 'blur'
          }
        ],
        VMIWarehouseCode: [{ required: true, message: this.$t('请选择VMI仓'), trigger: 'blur' }]
      },
      warehouseSelect: [], //vmi仓接口
      siteSelect: [], //工厂
      supplierSelect: [], //供应商
      requestUrl: {
        pre: 'thirdPartyVMICollaboration',
        url: 'postthirdPartyFactorySelectList'
      },
      parameterValue: '',
      dataLimit: 20,
      labelShowObj: {}
    }
  },
  watch: {
    // headerInfo() {
    // 默认选中第一个
    // this.headerInfo.vmi =
    //   this.headerInfo.vmiWarehouseCode +
    //   "-" +
    //   this.headerInfo.vmiWarehouseName;
    // },
  },
  mounted() {
    // this.headerInfo.vmi =
    //   this.headerInfo.vmiWarehouseCode + "-" + this.headerInfo.vmiWarehouseName;
    // this.headerInfo.site =
    //   this.headerInfo.siteCode + "-" + this.headerInfo.siteName;
    // this.headerInfo.supplier =
    //   this.headerInfo.siteCode + "-" + this.headerInfo.siteName;

    this.detailType = this.$route.query.create
  },
  filters: {
    dateFormat(value) {
      let str = ''
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }

      return str
    },
    timeFormat(value) {
      let str = ''
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'HH:MM:SS', value })
      } else {
        str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
      }

      return str
    },
    statusFormat(value) {
      if (!StatusText[value]) {
        return value
      } else {
        return StatusText[value]
      }
    }
  },
  methods: {
    // 校验
    checkForm() {
      let validStatus = false
      this.$refs.ruleForm.validate((valid) => {
        validStatus = valid
      })
      return validStatus
    },
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 保存

    preservationBtn: debounce(function () {
      this.$emit('preservationBtn', this.headerInfo)
    }, 500),
    // 提交
    submitBtn: debounce(function () {
      this.$emit('submitBtn', this.headerInfo)
    }, 500),
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    },
    // 状态 转对应的 css class
    statusToClass(value) {
      let cssClass = ''
      if (StatusCssClass[value]) {
        cssClass = StatusCssClass[value]
      }
      return cssClass
    },
    // 收货单头上的工厂等那些信息的筛选按照林健说的逻辑稍微改下，接口还是之前的接口，我加了个参数：resultType，可选值为：site(工厂)/warehouse(vmi仓库)/supplier(供应商);你需要查工厂的时候就传site，需要查仓库就传warehouse，需要查供应商就传supplier；查VMI仓要根据工厂过滤就将选择的工厂编码作为查询VMI仓的条件一起传进去
    // 查询出vmi仓接口

    // 获取焦点时
    focusSite(e) {
      this.parameterValue = e
      this.labelShowObj = {
        code: 'siteCode',
        name: 'siteName'
      }
    },
    focusWarehouse(e) {
      this.parameterValue = e
    },
    focusSupplier(e) {
      this.parameterValue = e
    },
    // 工厂，供应商，vmi仓，搜索    jcs
    getDatalist(e = { text: '' }) {
      let than = this
      // dataLimit 限制返回条数
      this.$API[this.requestUrl.pre]
        [this.requestUrl.url]({
          resultType: this.parameterValue,
          keyword: e.text
        })
        .then((res) => {
          let dataList = []
          if (than.parameterValue === 'site') {
            dataList = res.data.reduce((pre, now) => {
              pre.push({
                text: now.siteName + '-' + now.siteCode,
                value: now.siteCode,
                ...pre
              })
              return pre
            }, [])
          } else if (than.parameterValue === 'supplier') {
            dataList = res.data.reduce((pre, now) => {
              pre.push({
                text: now.supplierCode + '-' + now.supplierName,
                value: now.supplierCode,
                ...pre
              })
              return pre
            }, [])
          } else if (than.parameterValue === 'warehouse') {
            dataList = res.data.reduce((pre, now) => {
              pre.push({
                text: now.vmiWarehouseCode + '-' + now.vmiWarehouseName,
                value: now.vmiWarehouseCode,
                ...pre
              })
              return pre
            }, [])
          }
          this.$nextTick(() => {
            if (e.updateData && typeof e.updateData == 'function') {
              e.updateData(dataList)
            }
          })
          if (res.total > this.dataLimit) {
            this.$toast({
              content: this.$t('搜索结果较多，请再输入更精确的查询条件'),
              type: 'warning'
            })
          }
          // this.selectVal = this.initVal;
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(33% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
