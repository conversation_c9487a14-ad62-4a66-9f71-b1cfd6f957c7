<template>
  <!-- // 新增评审模板详情 -->
  <div class="full-height pt20 vertical-flex-box">
    <div class="flex-keep">
      <div class="header-box">
        <div class="main-bottom">
          <mt-form ref="ruleForm" :model="forecastTemplate" :rules="rules">
            <mt-form-item prop="companyCode" :label="$t('公司')">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="forecastTemplate.companyCode"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                multiple
                :placeholder="$t('请选择公司')"
                :fields="{ text: 'orgName', value: 'orgCode' }"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商编码（多选）')">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="forecastTemplate.supplierCodeList"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择供应商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-button css-class="e-flat" @click="submitEvent" :is-primary="true">{{
              $t('查询')
            }}</mt-button>
            <mt-button css-class="e-flat" @click="clearEvent" :is-primary="true">{{
              $t('清除')
            }}</mt-button>
          </mt-form>
          <div class="detail-content">
            <vxe-toolbar>
              <template #buttons>
                <!-- icon="vxe-icon-download" -->
                <vxe-button @click="handleAddDimension" status="primary">{{
                  $t('导出')
                }}</vxe-button>
              </template>
            </vxe-toolbar>
            <ScTable
              show-footer
              ref="xTable"
              max-height="100%"
              :row-config="{ height: 48 }"
              :columns="columns"
              :table-data="tableData"
              :footer-method="footerMethod"
              header-align="center"
              align="center"
            >
            </ScTable>
            <vxe-pager
              align="right"
              :current-page.sync="page1.current"
              :page-size.sync="page1.size"
              :total="totalNum"
              @page-change="handlePageChange"
            >
            </vxe-pager>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main'
import ScTable from '@/components/ScTable/src/index'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    ScTable
  },
  data() {
    return {
      page1: {
        current: 1,
        size: 20
      },
      totalNum: 0,
      forecastTemplate: {
        supplierCode: '',
        companyCode: [],
        supplierCodeList: []
      },
      copyTemplate: null,
      formData: {},
      tableData: [],
      columns: [
        {
          type: 'seq',
          title: i18n.t('序号'),
          width: 70
        },
        {
          field: 'companyCode',
          title: i18n.t('公司')
        },
        {
          field: 'supplierCode',
          title: i18n.t('供应商代码')
        },
        {
          field: 'supplierName',
          title: i18n.t('供应商名称'),
          width: 200
        },
        {
          field: 'currencyName',
          title: i18n.t('币种'),
          width: 100
        },
        {
          field: 'totalAmounts',
          title: i18n.t('应付款合计'),
          width: 120
        },
        {
          field: 'billAmounts',
          title: i18n.t('对账单在途金额'),
          width: 150
        },
        {
          field: 'invoiceAmounts',
          title: i18n.t('已开票应付金额'),
          width: 150
        },
        {
          field: 'noInvoiceAmounts',
          title: i18n.t('未开票应付金额'),
          width: 150
        },
        {
          field: 'unPostingInvoiceAmounts',
          title: i18n.t('已开票未过账金额'),
          width: 150
        },
        {
          title: this.$t('未开票余额'),
          children: [
            {
              field: 'noInvoiceAmounts5',
              title: i18n.t('N-5(及以前)月'),
              width: 120
            },
            {
              field: 'noInvoiceAmounts4',
              title: i18n.t('N-4月')
            },
            {
              field: 'noInvoiceAmounts3',
              title: i18n.t('N-3月')
            },
            {
              field: 'noInvoiceAmounts2',
              title: i18n.t('N-2月')
            },
            {
              field: 'noInvoiceAmounts1',
              title: i18n.t('N-1月')
            },
            {
              field: 'noInvoiceAmounts0',
              title: i18n.t('N月')
            }
          ]
        }
      ],
      rules: {
        companyCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      extend: {
        mode: 'normal',
        placeholder: this.$t('请选择供应商'),
        method: 'post',
        url: 'masterDataManagement/tenant/supplier/paged-query',
        searchUrl: '',
        searchFields: ['id', 'supplierCode', 'supplierDescription', 'supplierName'],
        params: {},
        recordsPosition: 'data.records',
        input: (item) => `${item.supplierCode} ${item.supplierName}`,
        title: (item) => `${item.supplierCode}`,
        title2: (item) => `${item.supplierName}`, //TODO  搜索后无法显示数据
        rulesAbled: true
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    }
  },
  mounted() {
    this.copyTemplate = { ...this.forecastTemplate }
  },
  methods: {
    getFormDetail() {
      console.log(this.forecastTemplate)
      const params = {
        page: this.page1,
        companyCode: this.forecastTemplate.companyCode,
        supplierCode: '',
        supplierCodeList: this.forecastTemplate.supplierCodeList
      }
      this.$API.reconciliationReport.supplierAccountQuery(params).then((res) => {
        this.totalNum = res.data.total ? Number(res.data.total) : 0
        this.tableData = res.data.records
      })
    },
    handlePageChange({ currentPage, pageSize }) {
      this.page1.current = currentPage
      this.page1.size = pageSize
      this.getFormDetail()
    },
    // 求和
    sumNum(list, field) {
      let count = 0
      list.forEach((item) => {
        count += Number(item[field])
      })
      return count
    },
    footerMethod({ columns, data }) {
      const sums = []
      columns.forEach((column, columnIndex) => {
        if (columnIndex === 0) {
          sums.push('合计：')
        } else {
          let sumCell = null
          switch (column.property) {
            case 'totalAmounts':
            case 'billAmounts':
            case 'invoiceAmounts':
            case 'noInvoiceAmounts':
            case 'noInvoiceAmounts5':
            case 'noInvoiceAmounts4':
            case 'noInvoiceAmounts3':
            case 'noInvoiceAmounts2':
            case 'noInvoiceAmounts1':
            case 'noInvoiceAmounts0':
            case 'unPostingInvoiceAmounts':
              sumCell = this.sumNum(data, column.property)
              break
            case 'companyCode':
              break
            case 'supplierCode':
              break
            case 'supplierName':
              break
          }
          sums.push(sumCell)
        }
      })
      // 返回一个二维数组的表尾合计
      return [sums]
    },
    // 导出
    handleAddDimension() {
      console.log('导出')
      const params = {
        page: this.page1,
        companyCode: this.forecastTemplate.companyCode,
        supplierCode: '',
        supplierCodeList: this.forecastTemplate.supplierCodeList
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.reconciliationReport.supplierAccountExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 查询
    submitEvent() {
      this.getFormDetail()
    },
    // 清除
    clearEvent() {
      this.clearSearch()
      this.getFormDetail()
    },
    // 清除查询条件
    clearSearch() {
      this.forecastTemplate = { ...this.copyTemplate }
    }
  },
  watch: {}
}
</script>
<style lang="scss">
/* ant select 样式改写 */
.transparencySelect {
  .ant-select-selection {
    background-color: transparent !important;
  }
  .j-select.ant-select .ant-select-selection {
    border-color: rgba(0, 0, 0, 0.42) !important;
  }
}
</style>
<style lang="scss" scoped>
.forecast-template {
  width: 250px;
  padding-bottom: 8px;
  .forecast-template-select {
    width: calc(250px - 70px);
  }
}
.full-height .flex-keep {
  display: flex;
}
.main-bottom {
  width: 100%;
  /deep/ .mt-form-item {
    width: calc(30% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;

    // &.more-width {
    //   // width: 450px;
    // }

    .full-width {
      width: calc(100% - 20px) !important;
    }
  }
  /deep/ .e-flat {
    position: relative;
    top: 21px;
  }
}
.header-box {
  width: 100%;
  padding-right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .itemcon {
    display: flex;
    align-items: center;
  }
  .item {
    margin-right: 20px;
  }
  .middle-blank {
    flex: 1;
  }
  .status {
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(99, 134, 193, 1);
    padding: 4px;
    background: rgba(238, 242, 249, 1);
    border-radius: 2px;
  }

  .infos {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
  }

  .sort-box {
    position: relative;
    cursor: pointer;
    margin-right: 20px;
    .mt-icons {
      font-size: 12px;
      transform: scale(0.5);
      color: rgba(0, 70, 156, 1);
      margin-top: -10px;
      position: absolute;
      top: 0;

      &:nth-child(2) {
        top: 6px;
      }
    }
  }
}
</style>
