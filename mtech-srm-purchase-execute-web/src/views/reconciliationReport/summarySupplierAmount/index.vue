<template>
  <!-- // 新增评审模板详情 -->
  <div class="full-height pt20 vertical-flex-box">
    <div class="flex-keep">
      <div class="header-box">
        <div class="main-bottom">
          <mt-form ref="ruleForm" :model="forecastTemplate" :rules="rules">
            <mt-form-item prop="companyCode" :label="$t('公司')">
              <mt-multi-select
                :allow-filtering="true"
                :fields="{
                  text: 'label',
                  value: 'orgCode'
                }"
                filter-type="Contains"
                :show-clear-button="true"
                :data-source="companyCodeList"
                v-model="forecastTemplate.companyCode"
                :placeholder="$t('请选择公司')"
              >
              </mt-multi-select>
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商编码')">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="forecastTemplate.supplierCode"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择供应商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="voucherDateStart" :label="$t('凭证日期从')">
              <mt-date-picker
                v-model="forecastTemplate.voucherDateStart"
                :show-clear-button="true"
                :placeholder="$t('请选择凭证日期从')"
              ></mt-date-picker>
            </mt-form-item>
            <mt-form-item prop="voucherDateEnd" :label="$t('凭证日期至')">
              <mt-date-picker
                v-model="forecastTemplate.voucherDateEnd"
                :show-clear-button="true"
                :placeholder="$t('请选择凭证日期至')"
              ></mt-date-picker>
            </mt-form-item>
            <mt-button css-class="e-flat" @click="submitEvent" :is-primary="true">{{
              $t('查询')
            }}</mt-button>
            <mt-button css-class="e-flat" @click="clearEvent" :is-primary="true">{{
              $t('清除')
            }}</mt-button>
          </mt-form>
          <div class="detail-content">
            <vxe-toolbar>
              <template #buttons>
                <!-- icon="vxe-icon-download" -->
                <vxe-button @click="handleAddDimension" status="primary">{{
                  $t('导出')
                }}</vxe-button>
              </template>
            </vxe-toolbar>
            <ScTable
              show-footer
              ref="xTable"
              max-height="100%"
              :row-config="{ height: 48 }"
              :columns="columns"
              :table-data="tableData"
              header-align="center"
              align="center"
            >
            </ScTable>
            <vxe-pager
              align="right"
              :current-page.sync="page1.current"
              :page-size.sync="page1.size"
              :total="totalNum"
              @page-change="handlePageChange"
            >
            </vxe-pager>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main'
import ScTable from '@/components/ScTable/src/index'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    ScTable
  },
  data() {
    return {
      page1: {
        current: 1,
        size: 20
      },
      totalNum: 0,
      companyCodeList: null,
      forecastTemplate: {
        supplierCode: [],
        companyCode: [],
        // itemCode: "",
        voucherDateStart: '',
        voucherDateEnd: ''
      },
      copyTemplate: null,
      formData: {},
      tableData: [],
      columns: [
        {
          type: 'seq',
          title: i18n.t('序号'),
          width: 70
        },
        {
          field: 'companyCode',
          title: i18n.t('公司代码')
        },
        {
          field: 'companyName',
          title: i18n.t('公司名称'),
          width: 200
        },
        {
          field: 'supplierCode',
          title: i18n.t('供应商代码')
        },
        {
          field: 'supplierName',
          title: i18n.t('供应商名称'),
          width: 200
        },
        // {
        //   field: "itemCode",
        //   title: i18n.t("物料编码"),
        // },
        // {
        //   field: "itemName",
        //   title: i18n.t("物料名称"),
        //   width: 200,
        // },
        {
          field: 'inOutQuantity',
          title: i18n.t('入库数量'),
          width: 120
        },
        {
          field: 'inOutPrice',
          title: i18n.t('入库金额'),
          width: 150
        },
        {
          field: 'noSaleQuantity',
          title: i18n.t('销售数量（不开票）'),
          width: 150
        },
        {
          field: 'noSalePrice',
          title: i18n.t('销售金额（不开票）'),
          width: 150
        },
        {
          field: 'saleQuantity',
          title: i18n.t('销售数量（开票）'),
          width: 150
        },
        {
          field: 'salePrice',
          title: i18n.t('销售金额（开票）'),
          width: 150
        },
        {
          field: 'amounts',
          title: i18n.t('实际供货金额'),
          width: 150
        }
      ],
      rules: {
        companyCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      extend: {
        mode: 'normal',
        placeholder: this.$t('请选择供应商'),
        method: 'post',
        url: 'masterDataManagement/tenant/supplier/paged-query',
        searchUrl: '',
        searchFields: ['id', 'supplierCode', 'supplierDescription', 'supplierName'],
        params: {},
        recordsPosition: 'data.records',
        input: (item) => `${item.supplierCode} ${item.supplierName}`,
        title: (item) => `${item.supplierCode}`,
        title2: (item) => `${item.supplierName}`, //TODO  搜索后无法显示数据
        rulesAbled: true
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    }
  },
  mounted() {
    this.getCompanyCodeList()
    // this.getFormDetail();
    this.copyTemplate = { ...this.forecastTemplate }
  },
  methods: {
    getFormDetail() {
      console.log(this.forecastTemplate)
      const params = {
        page: this.page1,
        companyCode: this.forecastTemplate.companyCode,
        supplierCode: this.forecastTemplate.supplierCode,
        // itemCode: this.forecastTemplate.itemCode,
        voucherDateStart: this.forecastTemplate.voucherDateStart
          ? new Date(this.forecastTemplate.voucherDateStart).getTime()
          : '',
        voucherDateEnd: this.forecastTemplate.voucherDateEnd
          ? new Date(this.forecastTemplate.voucherDateEnd).getTime()
          : ''
      }
      this.$API.reconciliationReport.supplierSummaryQuery(params).then((res) => {
        this.totalNum = res.data.total ? Number(res.data.total) : 0
        this.tableData = res.data.records
      })
    },
    handlePageChange({ currentPage, pageSize }) {
      this.page1.current = currentPage
      this.page1.size = pageSize
      this.getFormDetail()
    },
    // 求和
    sumNum(list, field) {
      let count = 0
      list.forEach((item) => {
        count += Number(item[field])
      })
      return count
    },
    footerMethod({ columns, data }) {
      const sums = []
      columns.forEach((column, columnIndex) => {
        if (columnIndex === 0) {
          sums.push('合计：')
        } else {
          let sumCell = null
          switch (column.property) {
            case 'totalAmounts':
            case 'billAmounts':
            case 'invoiceAmounts':
            case 'noInvoiceAmounts':
            case 'noInvoiceAmounts5':
            case 'noInvoiceAmounts4':
            case 'noInvoiceAmounts3':
            case 'noInvoiceAmounts2':
            case 'noInvoiceAmounts1':
            case 'noInvoiceAmounts0':
              sumCell = this.sumNum(data, column.property)
              break
            case 'companyCode':
              break
            case 'supplierCode':
              break
            case 'supplierName':
              break
          }
          sums.push(sumCell)
        }
      })
      // 返回一个二维数组的表尾合计
      return [sums]
    },
    // 导出
    handleAddDimension() {
      console.log('导出')
      const params = {
        page: this.page1,
        companyCode: this.forecastTemplate.companyCode,
        // itemCode: this.forecastTemplate.itemCode,
        supplierCode: this.forecastTemplate.supplierCode,
        voucherDateStart: this.forecastTemplate.voucherDateStart
          ? new Date(this.forecastTemplate.voucherDateStart).getTime()
          : '',
        voucherDateEnd: this.forecastTemplate.voucherDateEnd
          ? new Date(this.forecastTemplate.voucherDateEnd).getTime()
          : ''
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.reconciliationReport.supplierSummaryExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 查询
    submitEvent() {
      this.$refs.ruleForm.validate((valid) => {
        if (!valid) {
          this.$toast({ content: this.$t('请选择公司'), type: 'warning' })
          return
        }
        this.getFormDetail()
      })
    },
    // 清除
    clearEvent() {
      this.clearSearch()
    },
    // 清除查询条件
    clearSearch() {
      this.forecastTemplate = { ...this.copyTemplate }
    },
    //获取公司列表数据
    getCompanyCodeList() {
      this.$API.reconciliationReport.getCompanyList({}).then((res) => {
        if (res.code === 200) {
          const _resData = res.data
          _resData.map((item) => {
            item.label = item.orgCode + ' - ' + item.orgName
          })
          this.companyCodeList = _resData
        }
      })
    }
  },
  watch: {}
}
</script>
<style lang="scss">
/* ant select 样式改写 */
.transparencySelect {
  .ant-select-selection {
    background-color: transparent !important;
  }
  .j-select.ant-select .ant-select-selection {
    border-color: rgba(0, 0, 0, 0.42) !important;
  }
}
</style>
<style lang="scss" scoped>
.forecast-template {
  width: 250px;
  padding-bottom: 8px;
  .forecast-template-select {
    width: calc(250px - 70px);
  }
}
.full-height .flex-keep {
  display: flex;
}
.main-bottom {
  width: 100%;
  /deep/ .mt-form-item {
    width: calc(30% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;

    // &.more-width {
    //   // width: 450px;
    // }

    .full-width {
      width: calc(100% - 20px) !important;
    }
  }
  /deep/ .e-flat {
    position: relative;
    top: 21px;
  }
}
.header-box {
  width: 100%;
  // padding-right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .itemcon {
    display: flex;
    align-items: center;
  }
  .item {
    margin-right: 20px;
  }
  .middle-blank {
    flex: 1;
  }
  .status {
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(99, 134, 193, 1);
    padding: 4px;
    background: rgba(238, 242, 249, 1);
    border-radius: 2px;
  }

  .infos {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
  }

  .sort-box {
    position: relative;
    cursor: pointer;
    margin-right: 20px;
    .mt-icons {
      font-size: 12px;
      transform: scale(0.5);
      color: rgba(0, 70, 156, 1);
      margin-top: -10px;
      position: absolute;
      top: 0;

      &:nth-child(2) {
        top: 6px;
      }
    }
  }
}
</style>
