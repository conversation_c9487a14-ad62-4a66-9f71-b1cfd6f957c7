<!--盘点单列表-->
<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
  </div>
</template>
<script>
import { pageConfig } from './config/index'
export default {
  data() {
    return {
      pageConfig: pageConfig()
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      const _selectRows = e.grid.getSelectedRecords()
      if (_selectRows.length <= 0 && (e.toolbar.id == 'confirm' || e.toolbar.id == 'refuse')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'confirm') {
        this.handleClickToolBerConfirm(_selectRows)
      }
      if (e.toolber.id == 'refuse') {
        this.handleClickToolBerRefuse(_selectRows)
      }
    },
    //确认
    handleClickToolBerConfirm(_selectRows) {
      let idList = _selectRows.map((item) => item.id)
      let parameter = {
        idList: idList,
        remark: ''
      }
      this.$API.Inventory.batchAccept(parameter).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
      })
    },
    //拒绝
    handleClickToolBerRefuse(_selectRows) {
      let idList = _selectRows.map((item) => item.id)
      let parameter = {
        idList: idList,
        remark: ''
      }
      this.$API.Inventory.batchReject(parameter).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
      })
    },
    handleClickCellTool(e) {
      let _selectRows = [e.data]
      if (e.tool.id == 'confirm') {
        this.handleClickToolBerConfirm(_selectRows)
      }
      if (e.tool.id == 'refuse') {
        this.handleClickToolBerRefuse(_selectRows)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  flex: 1;
}
</style>
