import { i18n } from '@/main.js'
const toolbar = [
  { id: 'confirm', icon: 'icon_solid_Createorder', title: i18n.t('确认') },
  { id: 'refuse', icon: 'icon_solid_Closeorder', title: i18n.t('拒绝') }
]

const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { status: 0, label: i18n.t('失效'), cssClass: 'title-#9baac1' },
        { status: 1, label: i18n.t('待确认'), cssClass: 'title-#6386c1' },
        { status: 2, label: i18n.t('已确认'), cssClass: 'title-#6386c1' },
        { status: 3, label: i18n.t('已拒绝'), cssClass: 'title-#9baac1' }
      ],
      fields: { text: 'label', value: 'status' }
    },
    cellTools: [
      {
        id: 'confirm',
        // icon: "icon_solid_Createorder",
        title: i18n.t('确认'),
        visibleCondition: (data) => {
          return data['status'] === 1
        }
      },
      {
        id: 'refuse',
        // icon: "icon_solid_Cancel",
        title: i18n.t('拒绝'),
        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
  },
  {
    field: 'companyName',
    headerText: i18n.t('客户公司')
  },
  {
    field: 'year',
    headerText: i18n.t('年份')
  },
  {
    field: 'month',
    headerText: i18n.t('月份')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',

    headerText: i18n.t('物料名称')
  },
  {
    field: 'unit',

    headerText: i18n.t('单位')
  },
  {
    field: 'inventoryQuantity',

    headerText: i18n.t('盘点数量')
  },
  {
    field: 'bookQuantity',

    headerText: i18n.t('账面数量')
  },
  {
    field: 'diversityQuantity',

    headerText: i18n.t('差异数量')
  }
]
export const pageConfig = () => [
  {
    toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData,
      asyncConfig: {
        url: `/srm-purchase-execute/tenant/inventory/queryContrastList`,
        params: {}
      }
    }
  }
]
