export default {
  data() {
    return {
      toolbar: [
        { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
      ],
      fixedColumn: [
        // {
        //   width: 50,
        //   type: 'checkbox'
        // },
        // {
        //   type: 'seq',
        //   title: this.$t('序号'),
        //   width: 50
        // },
        {
          field: 'itemCode',
          title: this.$t('物料编码'),
          fixed: 'left'
        },
        {
          field: 'itemName',
          title: this.$t('物料描述'),
          fixed: 'left'
        },
        {
          field: 'extMaterialGroup',
          title: this.$t('品类'),
          fixed: 'left'
        },
        {
          field: 'supplierItemCode',
          title: this.$t('供方物料编码'),
          minWidth: 120,
          fixed: 'left'
        },
        {
          field: 'countType',
          title: this.$t('统计类别'),
          className: 'vxe-table-multi-cell',
          minWidth: 90,
          fixed: 'left',
          slots: {
            default: 'typeDefault'
          }
        }
      ]
    }
  },
  computed: {},
  mounted() {},
  methods: {}
}
