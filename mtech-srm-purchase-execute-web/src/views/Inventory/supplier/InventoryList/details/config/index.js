import { i18n } from '@/main.js'
import Vue from 'vue'
import onlyShowInput from '../edit/onlyShowInput.vue'
import wuliao from '../edit/itemSearch.vue'
import Select from '../edit/select.vue'
export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'delete', icon: 'icon_solid_Closeorder', title: i18n.t('删除') },
  { id: 'cancel', icon: 'icon_table_cancel', title: i18n.t('取消编辑') }
]

export const toolbar1 = [
  {
    id: 'excelExport',
    icon: 'icon_solid_pushorder',
    title: i18n.t('导出')
  }
]
export const toolbar2 = [
  { id: 'excelImport', icon: 'icon_solid_Import', title: i18n.t('导入') },
  {
    id: 'excelExport',
    icon: 'icon_solid_pushorder',
    title: i18n.t('导出')
  }
]
export const enclosureToolbar = [
  {
    id: 'enclosureDelete',
    icon: 'icon_solid_Closeorder',
    title: i18n.t('删除')
  },
  { id: 'upload', icon: 'icon_solid_upload', title: i18n.t('上传') },
  { id: 'Download', icon: 'icon_solid_Download', title: i18n.t('下载') }
]

const diffToolbar = [
  {
    id: 'detailExcelExport',
    icon: 'icon_solid_pushorder',
    title: i18n.t('导出')
  }
]
// 盘点明细
export const columnDataDetails = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false, //隐藏在列选择器中的过滤
    allowEditing: false
  },
  {
    width: '200',
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    headerTemplate: () => {
      return {
        template: Vue.component('requiredCell', {
          template: `
            <div class="headers">
              <span style="color: red">*</span>
              <span class="e-headertext">{{ $t('物料编码') }}</span>
            </div>
          `
        })
      }
    },
    editTemplate: () => {
      return {
        template: wuliao
      }
    }
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    headerTemplate: () => {
      return {
        template: Vue.component('requiredCell', {
          template: `
            <div class="headers">
              <span style="color: red">*</span>
              <span class="e-headertext">{{ $t('物料名称') }}</span>
            </div>
          `
        })
      }
    },
    editTemplate: () => {
      return { template: onlyShowInput }
    },
    validationRules: { required: true }
  },
  {
    width: '150',
    field: 'inventoryQuantity',
    headerText: i18n.t('盘点数量'),
    headerTemplate: () => {
      return {
        template: Vue.component('requiredCell', {
          template: `
            <div class="headers">
              <span style="color: red">*</span>
              <span class="e-headertext">{{ $t('盘点数量') }}</span>
            </div>
          `
        })
      }
    },
    editType: 'numericedit', //默认编辑类型之number
    edit: {
      params: {
        min: 0
      }
    },
    validationRules: { required: true }
  },
  {
    field: 'endProductStatus',
    headerText: i18n.t('是否成品'),
    headerTemplate: () => {
      return {
        template: Vue.component('requiredCell', {
          template: `
            <div class="headers">
              <span style="color: red">*</span>
              <span class="e-headertext">{{ $t('是否成品') }}</span>
            </div>
          `
        })
      }
    },
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    },
    editTemplate: () => {
      return { template: Select }
    },
    validationRules: { required: true }
  },
  {
    width: '150',
    field: 'unit',
    headerText: i18n.t('单位'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: onlyShowInput
      }
    }
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
// 附件查询
const columnDataEnclosure = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'fileName',
    headerText: i18n.t('文件名称'),
    cellTools: [
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除')
      }
    ]
  },
  {
    field: 'fileSize',
    headerText: i18n.t('文件大小')
  },
  {
    field: 'fileType',
    headerText: i18n.t('文件类型')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  }
]
// 盘点差异
const columnDataDiff = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { status: 0, label: i18n.t('新建'), cssClass: 'title-#9baac1' },
        { status: 1, label: i18n.t('已提交'), cssClass: 'title-#6386c1' },
        { status: 2, label: i18n.t('已对比'), cssClass: 'title-#9baac1' },
        { status: 3, label: i18n.t('已取消'), cssClass: 'title-#6386c1' },
        { status: 4, label: i18n.t('已确认'), cssClass: 'title-#6386c1' },
        { status: 5, label: i18n.t('待确认'), cssClass: 'title-#6386c1' },
        { status: 6, label: i18n.t('已拒绝'), cssClass: 'title-#6386c1' },
        { status: 7, label: i18n.t('失效'), cssClass: 'title-#6386c1' },
        { status: 8, label: i18n.t('采方确认'), cssClass: 'title-#6386c1' }
      ],
      fields: { text: 'label', value: 'status' }
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商')
  },
  {
    field: 'year',
    headerText: i18n.t('年份')
  },
  {
    field: 'month',
    headerText: i18n.t('月份')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'unit',
    headerText: i18n.t('单位')
  },
  {
    field: 'inventoryQuantity',
    headerText: i18n.t('盘点数量')
  },
  {
    field: 'bookQuantity',
    headerText: i18n.t('账面数量')
  },
  {
    field: 'diversityQuantity',
    headerText: i18n.t('差异数量')
  }
]
export const pageConfig = [
  {
    title: i18n.t('盘点明细'),
    toolbar: {
      //工具栏和右侧筛选
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [toolbar, []]
    },
    grid: {
      useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
      columnData: columnDataDetails,
      dataSource: [],
      allowPaging: false, // 不分页
      editSettings: {
        allowEditing: true, //是否允许编辑
        allowDeleting: true, //是否允许删除
        allowAdding: true, //是否允许新增
        showDeleteConfirmDialog: false //删除是否需要确认
        // newRowPosition: "Top", //在上面新增一行还是下面新增一行
      }
    }
  },
  {
    title: i18n.t('相关附件'),
    toolbar: {
      //工具栏和右侧筛选
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [enclosureToolbar, []]
    },
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData: columnDataEnclosure,
      dataSource: []
      // asyncConfig: {
      //   url: `srm-purchase-execute/tenant/reconciliationHeader/queryFileByDocIdAndDocType?docId=${docId}&doctype="pd"`,
      //   methods: "get",
      // },
    }
  },
  {
    title: i18n.t('盘点差异'),
    toolbar: {
      //工具栏和右侧筛选
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [diffToolbar, []]
    },
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData: columnDataDiff,
      dataSource: []
    }
  }
]
