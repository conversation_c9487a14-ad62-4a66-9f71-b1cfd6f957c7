<template>
  <div class="top-info">
    <!-- 顶部信息 -->
    <nav>
      <div class="detail-info">
        <div class="name-wrap">
          <span class="codes">{{ $t(topDetails && topDetails.inventoryCode) }}</span>
          <span class="tags tags-0" v-if="topDetails.status == '0'">{{ $t('草稿') }}</span>
          <span class="tags tags-1" v-if="topDetails.status == '1'">{{ $t('已提交') }}</span>
          <span class="tags tags-2" v-if="topDetails.status == '2'">{{ $t('已对比') }}</span>
          <span class="tags tags-1" v-if="topDetails.status == '3'">{{ $t('已取消') }}</span>
          <span class="tags tags-1" v-if="topDetails.status == '4'">{{ $t('已确认') }}</span>
          <span class="tags tags-1" v-if="topDetails.status == '5'">{{ $t('待确认') }}</span>
          <span class="tags tags-1" v-if="topDetails.status == '6'">{{ $t('已拒绝') }}</span>
          <span class="tags tags-1" v-if="topDetails.status == '7'">{{ $t('失效') }}</span>
          <span class="tags tags-1" v-if="topDetails.status == '8'">{{ $t('采方已确认') }}</span>
          <span class="founder">{{ $t('创建人：') }}{{ $t(topDetails.createUserName) }}</span>
          <span class="date-created">{{ $t('创建日期：') }}{{ $t(topDetails.createTime) }} </span>
        </div>
        <div class="btns-wrap">
          <mt-button
            class="e-flat"
            @click="$router.push('/purchase-execute/supplier-inventory-list')"
            >{{ $t('返回') }}</mt-button
          >
          <mt-button
            class="e-flat"
            v-if="saveStatus != 3 && pageType != 'view'"
            @click="clickMtbuttonSave"
            >{{ $t('保存') }}</mt-button
          >
          <mt-button class="e-flat" v-if="pageType != 'view'" @click="clickMtbuttonSubmit">{{
            $t('提交')
          }}</mt-button>
          <mt-button
            class="e-flat"
            v-if="pageType === 'view' && topDetails.status === '8'"
            @click="clickMtbuttonConfirm"
            >{{ $t('确认') }}</mt-button
          >
          <mt-button
            class="e-flat"
            v-if="pageType === 'add' || pageType === 'edit'"
            @click="clickMtbuttonGetItem"
            >{{ $t('获取物料信息') }}</mt-button
          >
        </div>
      </div>
      <div class="formInput">
        <mt-form>
          <mt-form-item prop="companyCode" :label="$t('公司')">
            <mt-select
              v-if="!inventoryCode"
              v-model="formObject.companyCode"
              float-label-type="Never"
              :data-source="companySelect"
              :fields="{ text: 'codeAndName', value: 'customerCode' }"
              :disabled="weight <= 0"
              @change="companySelectChange"
              :placeholder="$t('请选择公司')"
            ></mt-select>
            <mt-input
              v-else
              v-model="formObject.companyName"
              float-label-type="Never"
              :disabled="true"
              placeholder=""
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="siteCode" :label="$t('工厂')">
            <mt-select
              v-if="!inventoryCode"
              v-model="formObject.siteCode"
              float-label-type="Never"
              :data-source="factorySelect"
              :fields="{ text: 'codeAndName', value: 'siteCode' }"
              :disabled="weight <= 1"
              @change="siteNameSelectChange"
              :placeholder="formObject.companyCode ? $t('请选择工厂') : $t('先选择公司')"
            ></mt-select>
            <mt-input
              v-else
              v-model="formObject.siteName"
              float-label-type="Never"
              :disabled="true"
              placeholder=""
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="data" :label="$t('年月')">
            <mt-date-picker
              start="Year"
              depth="Year"
              format="y MMMM"
              :allow-edit="false"
              v-model="formObject.date"
              :disabled="!!inventoryCode"
              :placeholder="$t('选择年月')"
            ></mt-date-picker>
          </mt-form-item>
          <mt-form-item prop="remark" :label="$t('备注')">
            <mt-input
              :disabled="pageType == 'view'"
              v-model="formObject.remark"
              float-label-type="Never"
              :placeholder="$t('请输入备注')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </nav>
    <mt-template-page
      ref="tepPage"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    />
  </div>
</template>
<script>
import { pageConfig, toolbar, toolbar1, toolbar2, enclosureToolbar } from './config/index'
import { utils } from '@mtech-common/utils'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  data() {
    return {
      pageConfig: pageConfig,
      formObject: {}, //头部表单
      topDetails: {}, //顶部详情
      detailsAnnex: [], //现有附件
      companySelect: [], //公司下拉数据
      factorySelect: [], //工厂下拉数据
      deleteItemId: [], //删除--盘点明细的id集合
      inventoryFiles: [],
      deleteFilesId: [], //删除--附件合集
      weight: 1,
      inventoryCode: null, //表单code
      listData: [], //列表信息
      saveStatus: 1,
      pageType: ''
    }
  },
  mounted() {
    this.initialCallInterface()
  },
  activated() {
    this.initialCallInterface()
  },

  methods: {
    actionBegin(args) {
      if (this.$route.query?.type === 'view' && args.requestType == 'beginEdit') {
        args.cancel = true //禁止行编辑
      }
    },
    actionComplete(args) {
      const { action } = args
      let _dataSource = utils.cloneDeep(this.pageConfig[0].grid['dataSource'])

      setTimeout(() => {
        if (['save', 'delete'].includes(args.requestType)) {
          this.isInEdit = false // 结束编辑状态

          // 如果新增时，有一行正在编辑。结束编辑后，再调用新增
          if (this.btnFlag == 'add') {
            this.handleAdd()
          }
          this.btnFlag = null
        }

        if (action === 'add') {
          _dataSource.unshift({ ...args.data, addId: new Date().getTime() })
          this.setItemData(_dataSource)
        }
        if (action === 'edit') {
          _dataSource = _dataSource.map((item) => {
            if (item.itemCode === args?.data.itemCode) item = args.data
            return item
          })
          this.setItemData(_dataSource)
        }
      }, 10)
    },

    //初始调用---------------------------------------表头
    initialCallInterface() {
      sessionStorage.removeItem('topData')
      sessionStorage.removeItem('itemData')
      this.inventoryCode = this.$route.query.inventoryCode
      this.pageType = this.$route.query.type
      if (this.inventoryCode) {
        this.queryDetails()
      } else {
        this.pageConfig[0].grid.dataSource = []
        this.pageConfig[1].grid.dataSource = []
        this.formObject = {
          companyName: null, //公司
          siteName: null, //工厂
          date: '', //年月
          remark: '' //备注
        } //头部表单
        this.topDetails = {
          inventoryCode: this.$t('盘点单号'),
          createUserName: '-', //创建人
          createTime: '-', //创建日期
          status: '0'
        } //顶部详情
        this.addcompanyDataSelect()
      }
      this.pageConfig[0].toolbar.tools = []
      this.pageConfig[1].toolbar.tools = []
      if (this.pageType === 'view') {
        this.$set(this.pageConfig[0].toolbar, 'tools', [[...toolbar1], []])
        this.$set(this.pageConfig[1].toolbar, 'tools', [[], []])
      } else {
        this.$set(this.pageConfig[0].toolbar, 'tools', [
          this.inventoryCode ? [...toolbar, ...toolbar2] : [...toolbar],
          []
        ])
        this.$set(this.pageConfig[1].toolbar, 'tools', [[...enclosureToolbar], []])
      }
    },
    //新增--公司--下拉
    addcompanyDataSelect() {
      let obj = {
        fuzzyNameOrCode: ''
      }
      this.$API.masterData.getCustomer(obj).then((res) => {
        res.data.forEach((item) => {
          item.theCodeName = item.customerName // 	客户名称
          item.value = item.customerName // 	客户名称
        })
        this.companySelect =
          res.data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.customerCode} - ${i.customerName}`
            }
          }) || []
      })
    },
    //code查询详情
    queryDetails() {
      const parameter = {
        inventoryCode: this.inventoryCode
      }
      this.$API.Inventory.queryInventoryItem(parameter).then(({ data }) => {
        let {
          id,
          companyName,
          companyCode,
          companyId,
          siteName,
          siteCode,
          remark,
          version,
          year,
          month,
          inventoryCode,
          createUserName,
          createTime,
          status
        } = data
        let formObject = {
          companyName,
          companyCode,
          companyId,
          siteName,
          siteCode,
          remark,
          version,
          year,
          month,
          status
        }
        this.inventoryId = id
        this.saveStatus = status
        formObject.date = new Date(year + '-' + month)
        this.$set(this, 'formObject', formObject)
        //----------头部信息赋值
        let topDetails = {
          inventoryCode,
          createUserName,
          createTime,
          status: String(status)
        }
        this.$set(this, 'topDetails', topDetails)
        let _data = utils.cloneDeep(data.outInventoryItemResponseList)
        _data = _data?.map((item) => {
          return {
            ...item,
            addId: new Date().getTime()
          }
        })
        this.setItemData(_data)
        this.weight = '3'
        sessionStorage.setItem('topData', JSON.stringify({ siteName, siteCode }))
      })
      this.detailsQueryFileByDocId(this.$route.query.id)
      // 查询盘点差异明细
      this.getInventoryVarianceItem()
    },
    //明细-查询附件
    detailsQueryFileByDocId(id) {
      this.$API.Inventory.queryFileByDocId(id).then((res) => {
        if (res.data && res.data.length > 0) {
          this.detailsAnnex = res.data
          this.$set(this.pageConfig[1].grid, 'dataSource', res.data)
        }
      })
    },
    //明细-盘点差异明细查询
    getInventoryVarianceItem() {
      this.$set(this.pageConfig[2].grid, 'dataSource', [])
      this.$API.Inventory.queryInventoryVarianceItem(this.inventoryCode).then((res) => {
        if (res.data && res.data.length > 0) {
          this.$set(this.pageConfig[2].grid, 'dataSource', res.data)
        }
      })
    },
    //change 公司
    companySelectChange(e) {
      this.formObject.companyCode = e.itemData.customerCode
      this.formObject.companyName = e.itemData.customerName
      this.formObject.companyId = e.itemData.customerEnterpriseId
      this.formObject.siteName = ''
      this.formObject.siteCode = ''
      this.weight = 2
      this.getSourceAvailable()
    },
    // 获取工厂数据
    getSourceAvailable() {
      let obj = {
        paramObj: this.formObject.companyCode
        // enterpriseId: this.topInfo.buyerEnterpriseId,
        // fuzzyParam: text,
        // dataLimit: 10000,
      }
      this.$API.masterData.getSiteListSupplier(obj).then((res) => {
        const list =
          res?.data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.siteCode} - ${i.siteName}`
            }
          }) || []
        this.factorySelect = [...list]
      })
    },
    //change 工厂
    siteNameSelectChange(e) {
      this.formObject.siteCode = e.itemData.siteCode
      this.formObject.siteName = e.itemData.siteName
      this.weight = 3
      sessionStorage.setItem('topData', JSON.stringify(this.formObject))
    },
    //--------------------------------------------------列表操作
    handleClickToolBar(e) {
      const _selectRows = e.grid.getSelectedRecords()
      if (
        _selectRows.length <= 0 &&
        (e.toolbar.id == 'delete' ||
          e.toolbar.id == 'enclosureDelete' ||
          e.toolbar.id == 'Download')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.handleClickToolBarAdd()
      }
      if (e.toolbar.id == 'delete') {
        this.handleClickToolBarDelete(_selectRows)
      }
      if (e.toolbar.id == 'enclosureDelete') {
        //附件-删除
        this.handleClickToolBarenclosureDelete(_selectRows)
      }
      if (e.toolbar.id == 'upload') {
        //附件-导入
        this.handleClickToolBarUpload()
      }
      if (e.toolbar.id == 'Download') {
        //附件-导出
        this.handleClickToolBarDownload(_selectRows)
      }
      if (e.toolbar.id === 'excelImport') {
        this.handleImport()
      }
      if (e.toolbar.id === 'excelExport') {
        this.handleExport()
      }
      if (e.toolbar.id === 'detailExcelExport') {
        this.handleExportVarianceItem()
      }
      if (e.toolbar.id === 'cancel') {
        e.grid.closeEdit()
      }
    },
    //明细--新增
    handleClickToolBarAdd() {
      if (this.weight == 3) {
        this.$refs.tepPage.getCurrentUsefulRef().gridRef?.ejsRef.addRecord()
      } else {
        this.$toast({ content: this.$t('请先选择顶部工厂'), type: 'warning' })
      }
    },
    //明细--删除
    handleClickToolBarDelete(_selectRows) {
      if (_selectRows.length == 0) {
        return this.$toast({
          content: this.$t('请先选择一行'),
          type: 'warning'
        })
      }
      for (var i = 0; i < _selectRows.length; i++) {
        if (_selectRows[i]?.dataSource === 'sap') {
          return this.$toast({
            content: this.$t('sap获取数据，禁止删除'),
            type: 'warning'
          })
        }
      }
      let _dataSource = utils.cloneDeep(this.pageConfig[0].grid['dataSource'])

      let itemCode = _selectRows.map((i) => {
        return i.itemCode
      })
      itemCode.forEach((i) => {
        //删除本地。
        _dataSource.forEach((e, eIdx) => {
          if (i == e.itemCode && e.id) {
            this.deleteItemId.push(e.id)
            _dataSource.splice(eIdx, 1)
          } else if (i == e.itemCode && !e.id) {
            _dataSource.splice(eIdx, 1)
          }
        })
      })
      this.setItemData(_dataSource)
    },
    //附件--删除
    handleClickToolBarenclosureDelete(_selectRows) {
      let _dataSource = utils.cloneDeep(this.pageConfig[1].grid['dataSource'])
      let id = _selectRows.map((i) => {
        return i.id
      })
      //删除本地
      id.forEach((i) => {
        _dataSource.forEach((e, eIdx) => {
          if (i == e.id) {
            _dataSource.splice(eIdx, 1)
          }
        })
      })
      id.forEach((i) => {
        this.detailsAnnex.forEach((e) => {
          if (i == e.id) {
            this.deleteFilesId.push(e.id)
          }
        })
      })
      this.$set(this.pageConfig[1].grid, 'dataSource', _dataSource)
    },
    //附件--行内--删除
    handleClickCellTool(e) {
      let _selectRows = [e.data]
      if (e.tool.id == 'delete') {
        this.handleClickToolBarenclosureDelete(_selectRows)
      }
    },
    //附件--上传
    handleClickToolBarUpload() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/Inventory/supplier/InventoryList/components/uploadDialog.vue" */ './components/uploadDialog.vue'
          ),
        data: {
          title: this.$t('附件导入')
        },
        success: (data) => {
          const formDatas = new FormData()
          formDatas.append('UploadFiles', data[0])
          this.$API.fileService.uploadPrivateFile(formDatas).then((res) => {
            if (res.code == 200) {
              let _dataSource = utils.cloneDeep(this.pageConfig[1].grid['dataSource'])
              let dataArr = res.data
              dataArr.createUserName = this.topDetails.createUserName
              _dataSource = [..._dataSource, dataArr]
              this.$set(this.pageConfig[1].grid, 'dataSource', _dataSource)
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
            }
          })
        }
      })
    },
    //附件--下载
    handleClickToolBarDownload(_selectRows) {
      _selectRows.forEach((item) => {
        this.handlerDownloadPrivateFile(item)
      })
    },
    // 下载文件
    handlerDownloadPrivateFile(data) {
      this.$store.commit('startLoading')
      this.$API.fileService
        .downloadPrivateFile({
          id: data.id || data.sysFileId
        })
        .then((res) => {
          this.$store.commit('endLoading')
          download({ fileName: data.fileName, blob: res.data })
        })
    },
    //确认
    clickMtbuttonConfirm() {
      this.$API.Inventory.supplierInventoryConfirm(this.inventoryCode).then(() => {
        this.$router.push({
          path: `purchase-inventory-list`
        })
      })
    },
    //保存----------------------------------------------保存
    clickMtbuttonSave() {
      const parameter = this.getParams('0')
      if (!parameter) return
      this.$API.Inventory.saveSubmit(parameter).then(() => {
        this.$router.push({
          path: `supplier-inventory-list`
        })
      })
    },
    //提交
    clickMtbuttonSubmit() {
      const parameter = this.getParams('1')
      if (!parameter) return
      this.$API.Inventory.saveSubmit(parameter).then(() => {
        this.$router.push({
          path: `supplier-inventory-list`
        })
      })
    },
    //判断--必填
    judgmentRequired(formObject, _dataSource) {
      if (!formObject.companyName) {
        this.$toast({
          content: this.$t('公司不能为空'),
          type: 'warning'
        })
        return
      }
      if (!formObject.siteCode) {
        this.$toast({
          content: this.$t('工厂不能为空'),
          type: 'warning'
        })
        return
      }
      if (!formObject.date) {
        this.$toast({
          content: this.$t('年月不能为空'),
          type: 'warning'
        })
        return
      }
      if (_dataSource.length == 0) {
        this.$toast({
          content: this.$t('盘点明细不能为空'),
          type: 'warning'
        })
        return
      }
      return true
    },
    //判断--新增附件合集
    inventoryFilesFunction() {
      let _dataSource = utils.cloneDeep(this.pageConfig[1].grid['dataSource'])
      let id = this.detailsAnnex.map((i) => {
        return i.id
      })
      id.forEach((i) => {
        _dataSource.forEach((e, eIdx) => {
          if (i == e.id) {
            _dataSource.splice(eIdx, 1)
          }
        })
      })
      return _dataSource
    },
    getParams(type) {
      let formObject = this.formObject //表头
      let _dataSource = this.pageConfig[0].grid.dataSource //表单
      let deleteItemId = this.deleteItemId //盘点明细删除的id
      let deleteFilesId = this.deleteFilesId //盘点明细删除的id
      if (type) {
        let judgmentRequired = this.judgmentRequired(formObject, _dataSource) //判断必填
        if (!judgmentRequired) return false
      }
      formObject.year = new Date(formObject.date).getFullYear()
      formObject.month = Number(new Date(formObject.date).getMonth()) + 1

      const mainObject =
        this.pageType === 'edit' ? { ...formObject, id: this.inventoryId } : { ...formObject }
      let inventoryFiles = this.inventoryFilesFunction()
      let parameter = {
        button: type, //保存
        deleteFilesId: deleteFilesId.length == 0 ? [] : [...deleteFilesId], //删除---附件
        deleteItemId: deleteItemId.length == 0 ? [] : [...deleteItemId], //删除---明细id
        inventoryFiles: inventoryFiles.length == 0 ? [] : [...inventoryFiles], //添加---附件
        inventoryItemRequestList: [..._dataSource], //盘点明细
        outInventoryHeaderRequest: { ...mainObject } //盘点主单
      }
      return parameter
    },
    //获取物料信息
    clickMtbuttonGetItem() {
      const parameter = this.getParams()
      const dataSource = this.pageConfig[0]?.grid?.dataSource ?? []
      this.$store.commit('startLoading')
      this.$API.Inventory.syncInventoryItem(parameter).then((res) => {
        this.$store.commit('endLoading')
        if (res.code === 200) {
          let _data = [...res.data]
          _data = _data.map((item) => {
            return { ...item, endProductStatus: 0 }
          })
          const _arr = this.arrSet([..._data, ...dataSource])
          this.setItemData(_arr)
        }
      })
    },
    setItemData(list) {
      this.$set(this.pageConfig[0].grid, 'dataSource', list)
      sessionStorage.setItem('itemData', JSON.stringify(list)) //设置dataSource缓存
    },
    // 去重
    arrSet(arr) {
      let obj = {}
      const res = arr.reduce((setArr, item) => {
        let _field = item.itemCode
        if (!obj[_field]) {
          obj[_field] = true
          setArr.push(item)
        } else {
          let _findIndex = setArr.findIndex((i) => i[_field] == _field)
          setArr[_findIndex] = {
            ...setArr[_findIndex],
            ...item
          }
        }
        return setArr
      }, [])
      return res
    },
    // 导入
    handleImport() {
      const parameter = this.getParams('0')
      if (!this.formObject.siteCode) {
        return this.$toast({
          content: this.$t('请先选择顶部工厂'),
          type: 'warning'
        })
      }
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('上传/导入'),
          importApi: this.$API.Inventory.excelImport,
          downloadTemplateApi: this.$API.Inventory.supplierExportOutInventoryItem,
          paramsKey: 'excel',
          asyncParams: {
            requestJson: JSON.stringify(parameter)
          }
        },
        success: () => {
          // 导入之后刷新列表
          this.queryDetails()
        }
      })
    },
    // 盘点明细导出
    handleExport() {
      this.$store.commit('startLoading')
      this.$API.Inventory.supplierExportOutInventoryItem(this.inventoryCode).then((res) => {
        this.$store.commit('endLoading')
        this.$toast({
          type: 'success',
          content: this.$t('导出成功，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      })
    },
    // 盘点差异明细导出
    handleExportVarianceItem() {
      this.$store.commit('startLoading')
      this.$API.Inventory.supExportInventoryVarianceItem(this.inventoryCode).then((res) => {
        this.$store.commit('endLoading')
        this.$toast({
          type: 'success',
          content: this.$t('导出成功，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.top-info {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  nav {
    flex-shrink: 0;
    //按钮
    .detail-info {
      padding: 20px 20px 3px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .name-wrap {
        span {
          margin-left: 30px;
        }
        span:nth-of-type(1) {
          margin-left: 0;
        }
        // 盘点单号
        .codes {
          font-size: 20px;
          font-family: DINAlternate;
          font-weight: bold;
          color: rgba(41, 41, 41, 1);
        }
        //状态
        .tags {
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: 500;
          padding: 4px;
          border-radius: 2px;
        }
        .tags-0 {
          color: rgb(155, 170, 193);
          background: rgba(155, 170, 193, 0.1);
        }
        .tags-1 {
          color: rgba(237, 161, 51, 1);
          background: rgba(237, 161, 51, 0.1);
        }
        .tags-2 {
          color: rgb(237, 86, 51);
          background: rgba(237, 86, 51, 0.1);
        }
        //创建人
        .founder {
        }
        //创建日期
        .date-created {
        }
      }
      .btns-wrap {
        /deep/ .mt-button {
          margin-right: 0;
          button {
            background: transparent;
            //border: 1px solid rgba(0, 70, 156, 0.1);
            border-radius: 4px;
            box-shadow: unset;
            padding: 6px 12px 4px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 500;
            color: rgba(0, 70, 156, 1);
          }
        }
        .pack-up {
          display: inline-block;
          position: relative;
          // .rotate {
          //   position: absolute;
          //   right: -5px;
          //   top: 5px;
          //   transform: rotate(180deg);
          //   .mt-icons {
          //     color: #c8d5e9;
          //   }
          // }
        }
      }
    }
    //表单
    .formInput {
      width: 100%;
      padding: 0 20px 0;
      box-sizing: border-box;
      .mt-form {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        .mt-form-item {
          width: 360px;
        }
        .mt-form-item:nth-of-type(1),
        .mt-form-item:nth-of-type(4) {
          margin-left: 0;
        }
        .mt-form-item:nth-of-type(4) {
          width: 100%;
        }
      }
    }
  }
  .mt-template-page {
    flex: 1;
    height: 100%;
  }
}
</style>
