export default {
  data() {
    return {
      toolbar: [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }]
    }
  },
  computed: {
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50
        },
        {
          field: 'invDate',
          title: this.$t('库存日期')
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商代码'),
          minWidth: 140
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          minWidth: 160
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码'),
          minWidth: 120
        },
        {
          field: 'itemName',
          title: this.$t('物料描述'),
          minWidth: 160
        },
        {
          field: 'categoryCode',
          title: this.$t('品类')
        },
        {
          field: 'supplierItemCode',
          title: this.$t('供方物料编码'),
          minWidth: 160
        },
        {
          field: 'producePlace',
          title: this.$t('生产地点'),
          minWidth: 160
        },
        {
          field: 'invPlace',
          title: this.$t('库存地点'),
          minWidth: 160
        },
        {
          field: 'invQty',
          title: this.$t('库存数量'),
          minWidth: 120
        },
        {
          field: 'invUnit',
          title: this.$t('单位'),
          minWidth: 80
        },
        {
          field: 'invCycle',
          title: this.$t('库存周期（天）'),
          minWidth: 160
        },
        {
          field: 'packageMethod',
          title: this.$t('包装方式')
        },
        {
          field: 'wbsOdf',
          title: this.$t('WBS/ODF'),
          minWidth: 160
        },
        {
          field: 'softwareVersion',
          title: this.$t('软件版本'),
          minWidth: 160
        },
        {
          field: 'pid',
          title: this.$t('PID'),
          minWidth: 160
        },
        {
          field: 'wb',
          title: this.$t('白平衡'),
          minWidth: 160
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          minWidth: 160
        }
      ]
    }
  },
  mounted() {},
  methods: {}
}
