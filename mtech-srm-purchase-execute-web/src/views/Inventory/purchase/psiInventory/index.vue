<!-- 采方-供方库存 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item prop="invDates" :label="$t('库存日期')">
          <mt-multi-select
            type="multipleChoice"
            v-model="searchFormModel.invDates"
            :data-source="invDatesOptions"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCodes" :label="$t('供应商编码')">
          <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.supplierCodes"
            url="/masterDataManagement/tenant/supplier/paged-query"
            multiple
            :placeholder="$t('请选择供应商')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="wbsOdf" :label="$t('WBS/ODF')">
          <mt-input
            v-model="searchFormModel.wbsOdf"
            :show-clear-button="true"
            :placeholder="$t('请输入WBS/ODF')"
          />
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类')">
          <mt-input
            v-model="searchFormModel.categoryCode"
            :show-clear-button="true"
            :placeholder="$t('请输入品类')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierItemCode" :label="$t('供方物料编码')">
          <mt-input
            v-model="searchFormModel.supplierItemCode"
            :show-clear-button="true"
            :placeholder="$t('请输入供方物料编码')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      row-id="id"
      grid-id="091cc93d-d2b7-4816-a661-acbd189c23e1"
      align="center"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :loading="item.loading"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import pagingMixin from '@/mixins/paging.js'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  components: {
    CollapseSearch,
    ScTable
  },
  mixins: [mixin, pagingMixin],
  data() {
    return {
      searchFormModel: {
        invDates: []
      },
      searchFormRules: {
        invDates: [{ required: true, message: this.$t('请选择库存日期'), trigger: 'blur' }]
      },
      tableData: [],
      loading: false,
      invDatesOptions: [],
      isInitQuery: false
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getInvDateOptions()
  },
  methods: {
    getInvDateOptions() {
      this.$API.screenDemand.getInvDateApi().then((res) => {
        if (res.code === 200) {
          this.invDatesOptions = res.data.map((item) => {
            return {
              text: item,
              value: item
            }
          })
          this.searchFormModel.invDates.push(this.invDatesOptions[0].value)
          this.handleSearch()
        }
      })
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.searchFormModel.invDates = []
      this.handleSearch()
    },
    handleSearch(type) {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          if (!type && this.$refs.pageRef) {
            this.pageInfo.current = 1
            this.$refs.pageRef.jumpNum = 1
            this.$refs.pageRef.currentPage = 1
          }
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    // 获取table数据
    async getTableData() {
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.Inventory.pagePsiInventoryApi(params).catch(
        () => (this.loading = false)
      )
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleExport(e) {
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      e.loading = true
      this.$API.Inventory.exportPsiInventoryApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>

<style></style>
