<!-- 采方-PSI日合格率 -->
<template>
  <div>
    <mt-tabs
      class="toggle-tab"
      :e-tab="false"
      :data-source="tabList"
      :selected-item="selectedItem"
      @handleSelectTab="handleSelectTab"
    />
    <div class="toggle-content">
      <List ref="listRef" v-show="tabIndex == 0" />
      <Chart ref="chartRef" v-if="tabIndex == 1" :params="params" :date-list="dateList" />
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
export default {
  components: {
    List: () => import('./pages/list/index.vue'),
    Chart: () => import('./pages/chart/index.vue')
  },
  data() {
    return {
      tabList: [{ title: this.$t('列表') }, { title: this.$t('趋势图') }],
      selectedItem: 0,
      tabIndex: 0,
      params: null,
      dateList: []
    }
  },
  methods: {
    handleSelectTab(e) {
      if (e === 1) {
        let params = {
          page: this.$refs.listRef.pageInfo,
          ...this.$refs.listRef.searchFormModel
        }
        params.dataDate = dayjs(params.dataDate).format('YYYYMMDD')
        this.params = params
        this.dateList = this.$refs.listRef.dateList
      }
      this.tabIndex = e
    }
  }
}
</script>
