<!-- 采方-PSI日合格率-趋势图 -->
<template>
  <div style="height: 800px; width: 100%">
    <div ref="lineChart" style="height: 100%; width: 100%"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
export default {
  props: {
    params: {
      type: Object,
      default: () => {
        return {}
      }
    },
    dateList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      lineChart: null
    }
  },
  mounted() {
    this.getData()
    this.lineChart = echarts.init(this.$refs.lineChart)
    window.addEventListener('resize', this.chartResize, true)
  },
  destroyed() {
    echarts.dispose(this.lineChart)
    this.lineChart = null
    window.removeEventListener('resize', this.chartResize, true)
  },
  methods: {
    chartResize() {
      this.lineChart && this.lineChart.resize()
    },
    getData() {
      this.$API.Inventory.chartPsiDailyPassApi(this.params).then((res) => {
        if (res.code === 200) {
          let seriesData = this.formatData(res.data)
          this.initChart(seriesData)
        }
      })
    },
    formatData(arr) {
      let seriesData = []
      arr.forEach((item) => {
        if (item.countType === 5) {
          let countType5Data = []
          this.dateList.forEach((v, i) => {
            let title = `srmField${i + 1}`
            if (this.params?.rollbackDays) {
              if (i < this.params?.rollbackDays - 1) {
                title = `rollbackField${i + 1}`
              } else {
                title = `srmField${i + 1 - this.params?.rollbackDays + 1}`
              }
            }
            countType5Data.push(item[title])
          })
          seriesData.push({
            name: item.supplierCode,
            type: 'line',
            smooth: true,
            data: countType5Data
          })
        }
        if (item.countType === 6) {
          let countType6Data = []
          this.dateList.forEach((v, i) => {
            let title = `srmField${i + 1}`
            if (this.params?.rollbackDays) {
              if (i < this.params?.rollbackDays - 1) {
                title = `rollbackField${i + 1}`
              } else {
                title = `srmField${i + 1 - this.params?.rollbackDays + 1}`
              }
            }
            countType6Data.push(item[title])
          })
          seriesData.push({
            name: item.supplierCode,
            type: 'line',
            smooth: true,
            data: countType6Data,
            xAxisIndex: 1,
            yAxisIndex: 1
          })
        }
      })
      return seriesData
    },
    initChart(seriesData) {
      let option = {
        color: ['#91cc75', '#ff9560', '#fac858', '#5470c6', '#ee6666'],
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          top: '45%'
        },
        grid: [
          {
            bottom: '60%'
          },
          {
            top: '60%'
          }
        ],
        title: [
          {
            left: 'center',
            text: this.$t('日合格率')
          },
          {
            top: '50%',
            left: 'center',
            text: this.$t('日达成率')
          }
        ],
        xAxis: [
          {
            data: this.dateList
          },
          {
            data: this.dateList,
            gridIndex: 1
          }
        ],
        yAxis: [
          {},
          {
            gridIndex: 1
          }
        ],
        series: seriesData
      }
      option && this.lineChart.setOption(option)
      this.lineChart && this.lineChart.resize()
    }
  }
}
</script>
