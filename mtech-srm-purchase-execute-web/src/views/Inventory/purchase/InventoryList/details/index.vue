<template>
  <div class="top-info">
    <!-- 顶部信息 -->
    <nav>
      <div class="detail-info">
        <div class="name-wrap">
          <span class="codes">{{ $t(topDetails && topDetails.inventoryCode) }}</span>
          <span class="tags tags-0" v-if="topDetails.status == '0'">{{ $t('草稿') }}</span>
          <span class="tags tags-1" v-if="topDetails.status == '1'">{{ $t('已提交') }}</span>
          <span class="tags tags-2" v-if="topDetails.status == '2'">{{ $t('已对比') }}</span>
          <span class="tags tags-1" v-if="topDetails.status == '3'">{{ $t('已取消') }}</span>
          <span class="tags tags-1" v-if="topDetails.status == '4'">{{ $t('已确认') }}</span>
          <span class="tags tags-1" v-if="topDetails.status == '5'">{{ $t('待确认') }}</span>
          <span class="tags tags-1" v-if="topDetails.status == '6'">{{ $t('已拒绝') }}</span>
          <span class="tags tags-1" v-if="topDetails.status == '7'">{{ $t('失效') }}</span>
          <span class="tags tags-1" v-if="topDetails.status == '8'">{{ $t('采方已确认') }}</span>
          <span class="founder">{{ $t('创建人：') }}{{ $t(topDetails.createUserName) }}</span>
          <span class="date-created">{{ $t('创建日期：') }}{{ $t(topDetails.createTime) }} </span>
        </div>
        <div class="btns-wrap">
          <mt-button
            class="e-flat"
            @click="$router.push('/purchase-execute/purchase-inventory-list')"
            >{{ $t('返回') }}</mt-button
          >
          <mt-button
            class="e-flat"
            v-if="pageType === 'add' || pageType === 'edit'"
            @click="clickMtbuttonSave"
            >{{ $t('保存') }}</mt-button
          >
          <!-- <mt-button
            class="e-flat"
            v-if="pageType === 'add' || pageType === 'edit'"
            @click="clickMtbuttonSubmit"
            >{{ $t("提交") }}</mt-button
          > -->
          <mt-button
            class="e-flat"
            v-if="pageType === 'view' && topDetails.status === '2'"
            @click="clickMtbuttonRefuse"
            >{{ $t('拒绝') }}</mt-button
          >
          <mt-button
            class="e-flat"
            v-if="pageType === 'view' && topDetails.status === '2'"
            @click="clickMtbuttonConfirm"
            >{{ $t('确认') }}</mt-button
          >
          <mt-button
            class="e-flat"
            v-if="pageType === 'add' || pageType === 'edit'"
            @click="clickMtbuttonGetItem"
            >{{ $t('获取物料信息') }}</mt-button
          >
        </div>
      </div>
      <div class="formInput">
        <mt-form>
          <mt-form-item prop="companyCode" :label="$t('公司')">
            <mt-select
              v-if="!inventoryCode"
              v-model="formObject.companyCode"
              float-label-type="Never"
              :data-source="companyOptions"
              :allow-filtering="true"
              filter-type="Contains"
              :fields="{
                text: 'codeAndName',
                value: 'dimensionCodeValue'
              }"
              @change="companySelectChange"
              :placeholder="$t('请选择公司')"
            ></mt-select>
            <mt-input
              v-else
              v-model="formObject.companyName"
              float-label-type="Never"
              :disabled="true"
              placeholder=""
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="siteCode" :label="$t('工厂')">
            <mt-select
              v-if="!inventoryCode"
              v-model="formObject.siteCode"
              float-label-type="Never"
              :data-source="siteOptions"
              :allow-filtering="true"
              filter-type="Contains"
              :fields="{
                text: 'codeAndName',
                value: 'siteCode'
              }"
              :disabled="!formObject.companyName"
              @change="siteNameSelectChange"
              :placeholder="formObject.companyName ? $t('请选择工厂') : $t('先选择公司')"
            ></mt-select>
            <mt-input
              v-else
              v-model="formObject.siteName"
              float-label-type="Never"
              :disabled="true"
              placeholder=""
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="supplierCode" :label="$t('供应商')">
            <mt-select
              v-if="!inventoryCode"
              v-model="formObject.supplierCode"
              float-label-type="Never"
              :allow-filtering="true"
              filter-type="Contains"
              :fields="{
                text: 'codeAndName',
                value: 'dimensionCodeValue'
              }"
              :data-source="supplierOptions"
              :placeholder="$t('请选择供应商')"
              @change="supplierSelectChange"
            ></mt-select>
            <mt-input
              v-else
              v-model="formObject.supplierName"
              float-label-type="Never"
              :disabled="true"
              placeholder=""
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="data" :label="$t('年月')">
            <mt-date-picker
              :disabled="inventoryCode"
              start="Year"
              depth="Year"
              format="y MMMM"
              :allow-edit="false"
              v-model="formObject.date"
              :placeholder="$t('选择年月')"
            ></mt-date-picker>
          </mt-form-item>
          <mt-form-item prop="remark" :label="$t('备注')">
            <mt-input
              :disabled="pageType === 'view'"
              v-model="formObject.remark"
              float-label-type="Never"
              :placeholder="$t('请输入备注')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </nav>
    <mt-template-page
      ref="tepPage"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    />
  </div>
</template>
<script>
import { pageConfig, detailsToolbar, detailsToolBar1, detailsToolbar2 } from './config/index'
import { utils } from '@mtech-common/utils'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  data() {
    return {
      pageConfig: pageConfig,
      formObject: {
        companyName: '',
        companyCode: '',
        companyId: '',
        siteName: '',
        siteCode: '',
        supplierName: '',
        supplierCode: null,
        remark: '',
        year: '',
        month: '',
        date: ''
      }, //头部表单
      topDetails: {
        inventoryCode: '盘点单号',
        createUserName: '-', //创建人
        createTime: '-', //创建日期
        status: '0'
      }, //顶部详情
      detailsAnnex: [], //现有附件
      companyOptions: [], //公司下拉数据
      siteOptions: [], //工厂下拉数据
      supplierOptions: [], //供应商下拉数据
      deleteItemId: [], //删除--盘点明细的id集合
      inventoryFiles: [],
      deleteFilesId: [], //删除--附件合集
      inventoryCode: null, //表单code
      listData: [], //列表信息
      inventoryId: null,
      pageType: ''
    }
  },
  mounted() {
    this.initialCallInterface()
  },
  activated() {
    this.initialCallInterface()
  },
  computed: {
    canAdd() {
      return this.formObject.siteCode
    }
  },
  methods: {
    actionBegin(args) {
      if (this.$route.query?.type === 'view' && args.requestType == 'beginEdit') {
        args.cancel = true //禁止行编辑
      }
    },
    actionComplete(args) {
      const { action } = args
      let _dataSource = utils.cloneDeep(this.pageConfig[0].grid['dataSource'])

      setTimeout(() => {
        if (['save', 'delete'].includes(args.requestType)) {
          this.isInEdit = false // 结束编辑状态

          // 如果新增时，有一行正在编辑。结束编辑后，再调用新增
          if (this.btnFlag == 'add') {
            this.handleAdd()
          }
          this.btnFlag = null
        }

        if (action === 'add') {
          _dataSource.unshift({ ...args.data, addId: new Date().getTime() })
          this.setItemData(_dataSource)
        }
        if (action === 'edit') {
          _dataSource = _dataSource.map((item) => {
            if (item.itemCode === args?.data.itemCode) item = args.data
            return item
          })
          this.setItemData(_dataSource)
        }
      }, 10)
    },
    // 初始化调用接口
    initialCallInterface() {
      sessionStorage.removeItem('topData')
      sessionStorage.removeItem('itemData')
      this.inventoryCode = this.$route.query.inventoryCode
      this.pageType = this.$route.query.type
      if (!this.inventoryCode) {
        this.getSiteCompanySupplier('company')
        // this.getSiteCompanySupplier("site");
        this.getSiteCompanySupplier('supplier')
      }

      this.resetPage()
      // 如果是编辑页或者详情页，请求数据
      if (this.pageType === 'edit' || this.pageType === 'view') {
        this.queryDetails()
      }

      if (this.pageType === 'view') {
        this.$set(this.pageConfig[0].toolbar, 'tools', [[...detailsToolBar1], []])
      } else if (this.pageType === 'add') {
        this.$set(this.pageConfig[0].toolbar, 'tools', [[...detailsToolbar], []])
      } else {
        this.$set(this.pageConfig[0].toolbar, 'tools', [
          [...detailsToolbar, ...detailsToolbar2],
          []
        ])
      }
    },
    // 重置页面数据
    resetPage() {
      this.$set(this.pageConfig[0].grid, 'dataSource', [])
      this.$set(this.pageConfig[1].grid, 'dataSource', [])
      this.pageConfig[0].toolbar.tools = []
      this.formObject = {
        companyName: '',
        companyCode: '',
        companyId: '',
        siteName: '',
        siteCode: '',
        supplierName: '',
        supplierCode: null,
        remark: '',
        year: '',
        month: '',
        date: ''
      } //头部表单
      this.topDetails = {
        inventoryCode: '盘点单号',
        createUserName: '-', //创建人
        createTime: '-', //创建日期
        status: '0'
      }
    },
    //--------------------------------------------------列表操作
    handleClickToolBar(e) {
      const _selectRows = e.grid.getSelectedRecords()
      if (
        _selectRows.length <= 0 &&
        (e.toolbar.id == 'delete' ||
          e.toolbar.id == 'enclosureDelete' ||
          e.toolbar.id == 'Download')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.handleClickToolBarAdd()
      }
      if (e.toolbar.id == 'delete') {
        this.handleClickToolBarDelete(_selectRows)
      }
      if (e.toolbar.id === 'excelImport') {
        this.handleImport()
      }
      if (e.toolbar.id === 'excelExport') {
        this.handleExport()
      }
      if (e.toolbar.id === 'detailExcelExport') {
        this.handleExportVarianceItem()
      }
      if (e.toolbar.id === 'cancel') {
        e.grid.closeEdit()
      }
    },
    //获取采方工厂，公司、供应商列表
    getSiteCompanySupplier(type) {
      this.$API.Inventory.getSiteCompanySupplier(type).then((res) => {
        if (res.code === 200) {
          this[`${type}Options`] = res.data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.dimensionCodeValue} - ${i.dimensionNameValue}`
            }
          })
        }
      })
    },
    //code查询详情
    queryDetails() {
      const parameter = {
        inventoryCode: this.inventoryCode
      }

      this.$API.Inventory.queryInventoryItem(parameter).then(({ data }) => {
        let {
          id,
          companyName,
          companyCode,
          companyId,
          siteName,
          siteCode,
          supplierName,
          supplierCode,
          remark,
          version,
          year,
          month,
          inventoryCode,
          createUserName,
          createTime,
          status
        } = data
        let formObject = {
          companyName,
          companyCode,
          companyId,
          siteName,
          siteCode,
          supplierName,
          supplierCode,
          remark,
          version,
          year,
          month
        }
        this.inventoryId = id
        formObject.date = new Date(year + '-' + month)
        this.$set(this, 'formObject', formObject)
        //----------头部信息赋值
        let topDetails = {
          inventoryCode,
          createUserName,
          createTime,
          status: String(status)
        }
        this.$set(this, 'topDetails', topDetails)
        let _data = utils.cloneDeep(data.outInventoryItemResponseList)
        _data = _data?.map((item) => {
          return {
            ...item,
            addId: new Date().getTime()
          }
        })
        this.setItemData(_data)
        sessionStorage.setItem('topData', JSON.stringify({ siteName, siteCode }))
        // 查询附件
        this.detailsQueryFileByDocId(this.inventoryId)
        // 查询盘点差异明细
        this.getInventoryVarianceItem()
      })
    },
    //明细-查询附件
    detailsQueryFileByDocId(id) {
      this.$API.Inventory.queryFileByDocId(id).then((res) => {
        if (res.data && res.data.length > 0) {
          this.detailsAnnex = res.data
          this.$set(this.pageConfig[1].grid, 'dataSource', res.data || [])
        }
      })
    },
    //明细-盘点差异明细查询
    getInventoryVarianceItem() {
      this.$set(this.pageConfig[2].grid, 'dataSource', [])
      this.$API.Inventory.queryInventoryVarianceItem(this.inventoryCode).then((res) => {
        if (res.data && res.data.length > 0) {
          this.$set(this.pageConfig[2].grid, 'dataSource', res.data)
        }
      })
    },
    //change 公司
    companySelectChange(e) {
      this.formObject.companyCode = e.itemData.dimensionCodeValue
      this.formObject.companyName = e.itemData.dimensionNameValue
      this.formObject.companyId = e.itemData.dimensionIdValue
      this.$API.Inventory.getSiteByCompany(this.formObject.companyCode).then((res) => {
        this.siteOptions = res.data.map((i) => {
          return {
            ...i,
            codeAndName: `${i.siteCode} - ${i.siteName}`
          }
        })
      })
    },
    //change 工厂
    siteNameSelectChange(e) {
      this.formObject.siteCode = e.itemData.siteCode
      this.formObject.siteName = e.itemData.siteName
      sessionStorage.setItem('topData', JSON.stringify(this.formObject))
    },
    supplierSelectChange(e) {
      this.formObject.supplierCode = e.itemData.dimensionCodeValue
      this.formObject.supplierName = e.itemData.dimensionNameValue
    },
    //盘点明细--新增
    handleClickToolBarAdd() {
      if (this.canAdd) {
        this.$refs.tepPage.getCurrentUsefulRef().gridRef?.ejsRef.addRecord()
      } else {
        this.$toast({ content: this.$t('请先选择顶部工厂'), type: 'warning' })
      }
    },
    //盘点明细--删除
    handleClickToolBarDelete(_selectRows) {
      if (_selectRows.length == 0) {
        return this.$toast({
          content: this.$t('请先选择一行'),
          type: 'warning'
        })
      }
      let _dataSource = utils.cloneDeep(this.pageConfig[0].grid['dataSource'])
      let itemCode = _selectRows.map((i) => {
        return i.itemCode
      })
      itemCode.forEach((i) => {
        //删除本地。
        _dataSource.forEach((e, eIdx) => {
          if (i == e.itemCode && e.id) {
            console.log(e)
            this.deleteItemId.push(e.id)
            _dataSource.splice(eIdx, 1)
          } else if (i == e.itemCode && !e.id) {
            _dataSource.splice(eIdx, 1)
          }
        })
      })
      this.setItemData(_dataSource)
    },

    //保存----------------------------------------------保存
    clickMtbuttonSave() {
      const parameter = this.getParams('0')
      if (!parameter) return
      this.$API.Inventory.buyerInventorySave(parameter).then(() => {
        this.$router.push({
          path: `purchase-inventory-list`
        })
      })
    },
    //提交
    clickMtbuttonSubmit() {
      const parameter = this.getParams('1')
      if (!parameter) return
      this.$API.Inventory.buyerInventorySave(parameter).then(() => {
        this.$router.push({
          path: `purchase-inventory-list`
        })
      })
    },
    getParams(type) {
      let formObject = this.formObject //表头
      let _dataSource = this.pageConfig[0].grid.dataSource //表单
      let deleteItemId = this.deleteItemId //盘点明细删除的id
      let deleteFilesId = this.deleteFilesId //盘点明细删除的id
      if (type) {
        let judgmentRequired = this.judgmentRequired(formObject, _dataSource) //判断必填
        if (!judgmentRequired) return false
      }
      formObject.year = new Date(formObject.date).getFullYear()
      formObject.month = Number(new Date(formObject.date).getMonth()) + 1
      const mainObject =
        this.pageType === 'edit' ? { ...formObject, id: this.inventoryId } : { ...formObject }
      let inventoryFiles = this.inventoryFilesFunction()
      let parameter = {
        button: type, //保存
        deleteFilesId: deleteFilesId.length == 0 ? [] : [...deleteFilesId], //删除---附件
        deleteItemId: deleteItemId.length == 0 ? [] : [...deleteItemId], //删除---明细id
        inventoryFiles: inventoryFiles.length == 0 ? [] : [...inventoryFiles], //添加---附件
        inventoryItemRequestList: [..._dataSource], //盘点明细
        outInventoryHeaderRequest: { ...mainObject } //盘点主单
      }
      return parameter
    },
    //获取物料信息
    clickMtbuttonGetItem() {
      const parameter = this.getParams()
      const dataSource = this.pageConfig[0]?.grid?.dataSource ?? []
      this.$store.commit('startLoading')
      this.$API.Inventory.syncInventoryItem(parameter).then((res) => {
        this.$store.commit('endLoading')
        if (res.code === 200) {
          let _data = [...res.data]
          _data = _data.map((item) => {
            return { ...item, endProductStatus: 0 }
          })
          const _arr = this.arrSet([..._data, ...dataSource])
          this.setItemData(_arr)
        }
      })
    },
    setItemData(list) {
      this.$set(this.pageConfig[0].grid, 'dataSource', list)
      sessionStorage.setItem('itemData', JSON.stringify(list)) //设置dataSource缓存
    },
    // 去重
    arrSet(arr) {
      let obj = {}
      const res = arr.reduce((setArr, item) => {
        let _field = item.itemCode
        if (!obj[_field]) {
          obj[_field] = true
          setArr.push(item)
        } else {
          let _findIndex = setArr.findIndex((i) => i[_field] == _field)
          setArr[_findIndex] = {
            ...setArr[_findIndex],
            ...item
          }
        }
        return setArr
      }, [])
      return res
    },
    //拒绝
    clickMtbuttonRefuse() {
      this.$API.Inventory.buyerInventoryRefuse(this.inventoryCode).then(() => {
        this.$router.push({
          path: `purchase-inventory-list`
        })
      })
    },
    //确认
    clickMtbuttonConfirm() {
      this.$API.Inventory.buyerInventoryConfirm(this.inventoryCode).then(() => {
        this.$router.push({
          path: `purchase-inventory-list`
        })
      })
    },
    //判断--必填
    judgmentRequired(formObject, _dataSource) {
      if (!formObject.companyCode) {
        this.$toast({
          content: this.$t('公司不能为空'),
          type: 'warning'
        })
        return
      }
      if (!formObject.siteCode) {
        this.$toast({
          content: this.$t('工厂不能为空'),
          type: 'warning'
        })
        return
      }
      if (!formObject.supplierCode) {
        this.$toast({
          content: this.$t('供应商不能为空'),
          type: 'warning'
        })
        return
      }
      if (!formObject.date) {
        this.$toast({
          content: this.$t('年月不能为空'),
          type: 'warning'
        })
        return
      }
      if (_dataSource.length == 0) {
        this.$toast({
          content: this.$t('盘点明细不能为空'),
          type: 'warning'
        })
        return
      }
      return true
    },
    //判断--新增附件合集
    inventoryFilesFunction() {
      let _dataSource = utils.cloneDeep(this.pageConfig[1].grid['dataSource'])
      let id = this.detailsAnnex.map((i) => {
        return i.id
      })
      id.forEach((i) => {
        _dataSource.forEach((e, eIdx) => {
          if (i == e.id) {
            _dataSource.splice(eIdx, 1)
          }
        })
      })
      return _dataSource
    },
    // 导入
    handleImport() {
      const parameter = this.getParams('0')
      if (!this.formObject.siteCode) {
        return this.$toast({
          content: this.$t('请先选择顶部工厂'),
          type: 'warning'
        })
      }
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('上传/导入'),
          importApi: this.$API.Inventory.buyerExcelImport,
          downloadTemplateApi: this.$API.Inventory.buyerExportOutInventoryItem,
          paramsKey: 'excel',
          asyncParams: {
            requestJson: JSON.stringify(parameter)
          }
        },
        success: () => {
          // 导入之后刷新列表
          this.queryDetails()
        }
      })
    },
    // 盘点明细导出
    handleExport() {
      this.$store.commit('startLoading')
      this.$API.Inventory.buyerExportOutInventoryItem(this.inventoryCode).then((res) => {
        this.$store.commit('endLoading')
        this.$toast({
          type: 'success',
          content: this.$t('导出成功，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      })
    },
    // 盘点差异明细导出
    handleExportVarianceItem() {
      this.$store.commit('startLoading')
      this.$API.Inventory.buyerExportInventoryVarianceItem(this.inventoryCode).then((res) => {
        this.$store.commit('endLoading')
        this.$toast({
          type: 'success',
          content: this.$t('导出成功，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.top-info {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  nav {
    flex-shrink: 0;
    //按钮
    .detail-info {
      padding: 20px 20px 3px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .name-wrap {
        span {
          margin-left: 30px;
        }
        span:nth-of-type(1) {
          margin-left: 0;
        }
        // 盘点单号
        .codes {
          font-size: 20px;
          font-family: DINAlternate;
          font-weight: bold;
          color: rgba(41, 41, 41, 1);
        }
        //状态
        .tags {
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: 500;
          padding: 4px;
          border-radius: 2px;
        }
        .tags-0 {
          color: rgb(155, 170, 193);
          background: rgba(155, 170, 193, 0.1);
        }
        .tags-1 {
          color: rgba(237, 161, 51, 1);
          background: rgba(237, 161, 51, 0.1);
        }
        .tags-2 {
          color: rgb(237, 86, 51);
          background: rgba(237, 86, 51, 0.1);
        }
      }
      .btns-wrap {
        /deep/ .mt-button {
          margin-right: 0;
          button {
            background: transparent;
            //border: 1px solid rgba(0, 70, 156, 0.1);
            border-radius: 4px;
            box-shadow: unset;
            padding: 6px 12px 4px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: 500;
            color: rgba(0, 70, 156, 1);
          }
        }
        .pack-up {
          display: inline-block;
          position: relative;
        }
      }
    }
    //表单
    .formInput {
      width: 100%;
      padding: 0 20px 0;
      box-sizing: border-box;
      .mt-form {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        .mt-form-item {
          width: 23%;
        }
        .mt-form-item:nth-of-type(1),
        .mt-form-item:nth-of-type(4) {
          margin-left: 0;
        }
        .mt-form-item:nth-of-type(5) {
          width: 100%;
        }
      }
    }
  }
  .mt-template-page {
    flex: 1;
    height: 100%;
  }
}
</style>
