<template>
  <div>
    <div class="upload-dialog">
      <div class="uipload-box">
        <div id="drop" class="droparea">
          <div class="click-upbox" id="browse">
            <div class="plus-icon"></div>
            <div class="">
              <div class="plus-txt">{{ $t('请拖拽文件或点击上传') }}</div>
              <div class="warn-text">
                <span
                  >{{ $t('注') }}：{{ $t('文件最大不可超过') }}50M，
                  {{ $t('文件格式仅支持') }}（.pdf .jpg .bmp .gif .ico .pcx .jpeg .tif .png .txt
                  .xls .xlsx .doc .docx .zip .7z .rar）</span
                >
              </div>
            </div>
            <input type="file" class="upload-input" @change="chooseFiles" />
          </div>
        </div>
      </div>
    </div>
    <div class="upload-info" v-for="(item, index) in fileList" :key="item.name">
      <p class="info-name">{{ item.name }}</p>
      <p class="info-size">{{ item.size }}</p>
      <i class="mt-icons mt-icon-icon_Close_1 delete" @click="deleted(index)"></i>
    </div>
  </div>
</template>

<script>
import Bus from './config/bus'
export default {
  data() {
    return {
      fileList: [],
      parentData: []
    }
  },
  props: {
    multiple: {
      // 单个上传还是多个上传
      type: Boolean,
      default: () => {
        return true
      }
    }
  },
  methods: {
    chooseFiles(event) {
      const _files = event.target.files
      const params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (_files.length < 1) {
        this.$toast({
          content: this.$t('您未选择需要上传的文件.')
        })
        return
      }
      const _data = new FormData()
      let isOutOfRange = false
      for (let i = 0; i < _files.length; i++) {
        const size = _files[i].size / 1024
        let sizeStr = ''
        if (size < 1024) sizeStr = size.toFixed(2) + 'KB'
        else sizeStr = (size / 1024).toFixed(2)
        const json = { name: _files[i].name, size: sizeStr }
        this.parentData.push(_files[i])
        if (this.multiple) {
          this.fileList.push(json)
        } else {
          this.fileList = [json]
        }

        _data.append('UploadFiles', _files[i])
        if (_files[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$toast({
          content: params.msg
        })
        return
      }
      console.log(this.parentData)
      Bus.$emit('getFileData', this.parentData)
    },
    deleted(index) {
      this.fileList.splice(index, 1)
      this.parentData.splice(index, 1)
      Bus.$emit('getFileData', this.parentData)
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .mt-upload-file {
  display: none;
}
/deep/ .e-dlg-content {
  padding: 10px;
  overflow: hidden;
}
.upload-dialog {
  .e-upload {
    float: none !important;
    border: none !important;
  }
  .e-file-select-wrap {
    display: none !important;
  }
  .e-upload-files {
    background: rgba(250, 250, 250, 1);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 4px;
    padding: 20px;
    margin-top: 20px;

    .e-upload-file-list {
      min-height: 16px !important;
      height: 16px !important;
      line-height: 16px !important;
      border-bottom: none !important;
    }

    .up-loadcontainer {
      .wrapper {
        display: flex;
        align-items: center;
      }
      .file-name {
        width: 80px !important;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-top: 0;
        padding-top: 0;
        color: #292929;
      }
      .upload-size {
        color: #9a9a9a !important;
        padding-right: 10px;
      }
      .upload-status {
        color: #0043a8 !important;
      }
      .upload-success {
        color: #6f982b !important;
      }
      .upload-failed {
        color: #ed5633 !important;
      }
    }
  }

  .uipload-box {
    width: 100%;
    .droparea {
      background: rgba(251, 252, 253, 1);
      border: 1px dashed rgba(232, 232, 232, 1);
      border-radius: 4px;
      margin: 0 auto;
      margin-top: 20px;
      .click-upbox {
        cursor: pointer;
        position: relative;
        display: flex;
        height: 80px;
        padding-left: 20px;
        align-items: center;
        .upload-input {
          height: 100%;
          width: 100%;
          position: absolute;
          z-index: 2;
          top: 0;
          background: transparent;
          opacity: 0;
        }
        .plus-icon {
          width: 40px;
          height: 40px;
          margin-right: 20px;
          position: relative;
          &::after {
            content: ' ';
            display: inline-block;
            width: 2px;
            height: 40px;
            background: rgba(232, 232, 232, 1);
            border-radius: 100px;
            position: absolute;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
          }
          &::before {
            content: ' ';
            display: inline-block;
            width: 40px;
            height: 2px;
            background: rgba(232, 232, 232, 1);
            border-radius: 100px;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }
        .plus-txt {
          width: 100%;
          text-align: left;
          font-size: 20px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(152, 170, 195, 1);
        }
      }
      .warn-text {
        width: 100%;
        text-align: center;
        margin: 0 auto;
        margin-top: 10px;
        font-size: 12px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(237, 86, 51, 1);
      }
    }
  }
}
.upload-info {
  width: 100%;
  height: 80px;
  margin-top: 10px;
  background: rgba(251, 252, 253, 1);
  border: 1px dashed rgba(232, 232, 232, 1);
  border-radius: 4px;
  padding-left: 40px;
  padding-top: 22px;
  position: relative;
  .info-name {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(0, 70, 156, 1);
    margin-bottom: 5px;
  }
  .info-size {
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(154, 154, 154, 1);
  }
  .delete {
    position: absolute;
    font-size: 18px;
    top: 50%;
    margin-top: -9px;
    right: 20px;
    cursor: pointer;
  }
}
</style>
