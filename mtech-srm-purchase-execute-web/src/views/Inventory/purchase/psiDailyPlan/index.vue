<!-- 采方-供方生产日计划 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('数据导入日期')" prop="dataDate">
          <mt-date-picker
            v-model="searchFormModel.dataDate"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供应商')" prop="supplierCodes">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCodes"
            url="/masterDataManagement/tenant/supplier/paged-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
        <mt-form-item prop="supplierItemCode" :label="$t('供应商物料编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierItemCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('统计类别')" prop="countTypes">
          <mt-multi-select
            v-model="searchFormModel.countTypes"
            :data-source="countTypeOptions"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('品类')" prop="extMaterialGroup">
          <RemoteAutocomplete
            v-model="searchFormModel.extMaterialGroup"
            url="/masterDataManagement/tenant/category/paged-query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'categoryName', value: 'categoryCode' }"
            :search-fields="['categoryName', 'categoryCode']"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      row-id="id"
      grid-id="08a306cd-272b-4849-b5cd-73c2f3dd10da"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :row-config="{ height: rowHeight }"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :loading="item.loading"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #typeDefault="{ row }">
        <div v-for="(item, index) in row.detailData" :key="index" class="vxe-cell-border">
          <span style="margin-left: 10px">{{ formatCountType(item.countType) }}</span>
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import pagingMixin from '@/mixins/paging.js'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  components: {
    CollapseSearch,
    ScTable
  },
  mixins: [mixin, pagingMixin],
  data() {
    return {
      searchFormModel: {
        dataDate: new Date()
      },
      searchFormRules: {
        dataDate: [{ required: true, message: this.$t('请选择数据导入日期'), trigger: 'blur' }]
      },
      tableData: [],
      loading: false,
      columns: [],
      rowHeight: 128,
      countTypeOptions: [
        { text: this.$t('日排产数'), value: 1 },
        { text: this.$t('实际日产出'), value: 2 },
        { text: this.$t('合格数'), value: 3 },
        { text: this.$t('不良品数'), value: 4 }
      ]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getHeader()
  },
  methods: {
    formatCountType(value) {
      let text = this.countTypeOptions.find((item) => item.value === value)?.text
      return text
    },
    getHeader() {
      let params = {
        dataDate: dayjs(this.searchFormModel.dataDate).format('YYYY-MM-DD')
      }
      this.$API.Inventory.headerPsiDailyPlanApi(params).then((res) => {
        if (res.code === 200) {
          this.handleColumns(res.data)
        }
      })
    },
    handleColumns(arr) {
      let currentColumn = this.fixedColumn
      const dynamicColumn = []
      arr.forEach((item, i) => {
        const title = `srmField${i + 1}`
        dynamicColumn.push({
          title: item,
          field: title,
          width: 120,
          editRender: {},
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return row.detailData.map((item, index) => (
                <div key={index} class='vxe-cell-border'>
                  <span style={{ 'margin-left': '10px' }}>{item[title]}</span>
                </div>
              ))
            }
          }
        })
      })
      this.columns = currentColumn.concat(dynamicColumn)
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          if (key === 'dataDate') {
            this.searchFormModel[key] = new Date()
          } else {
            this.searchFormModel[key] = null
          }
        }
      }
      this.handleSearch()
    },
    handleSearch(type) {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          if (!type && this.$refs.pageRef) {
            this.pageInfo.current = 1
            this.$refs.pageRef.jumpNum = 1
            this.$refs.pageRef.currentPage = 1
          }
          this.getHeader()
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    // 获取table数据
    async getTableData() {
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      params.dataDate = dayjs(params.dataDate).format('YYYYMMDD')
      this.loading = true
      const res = await this.$API.Inventory.pagePsiDailyPlanApi(params).catch(
        () => (this.loading = false)
      )
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleExport(e) {
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      params.dataDate = dayjs(params.dataDate).format('YYYYMMDD')
      e.loading = true
      this.$API.Inventory.exportPsiDailyPlanApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  .vxe-cell-border {
    &:first-child {
      border-top: none;
    }
    border-top: solid #e6e9ed 1px;
    height: 32px;
    line-height: 32px;
    box-sizing: border-box;
  }
}
</style>
