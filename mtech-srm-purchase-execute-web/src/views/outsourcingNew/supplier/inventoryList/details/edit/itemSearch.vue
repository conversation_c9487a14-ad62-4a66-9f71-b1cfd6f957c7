<template>
  <div class="pc-select">
    <div class="in-cell">
      <mt-select
        :id="fieldName"
        v-model="value"
        :allow-filtering="true"
        float-label-type="Never"
        :data-source="dataSourceList"
        :fields="{ text: 'codeAndName', value: 'itemCode' }"
        @change="handleSelectChange"
        :filtering="filteringList"
        :disabled="selectDiasbled"
      ></mt-select>
      <mt-icon
        style="width: 20px; right: 18px"
        name="icon_list_refuse"
        @click.native="handleClear"
      ></mt-icon>
      <mt-icon style="width: 20px" name="icon_input_search" @click.native="showDialog"></mt-icon>
    </div>
    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="title"
      :buttons="buttons"
      @close="handleClose"
    >
      <div class="full-height">
        <mt-template-page
          ref="templateRef"
          class="template-height has-page"
          :hidden-tabs="true"
          :template-config="pageConfig"
          @recordDoubleClick="recordDoubleClick"
        ></mt-template-page>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import { PROXY_MDM_AUTH } from '@/utils/constant'
import { throttle } from 'lodash'
import axios from 'axios'

export default {
  props: {},
  data() {
    return {
      data: {},
      dialogShow: false,
      title: this.$t('请选择'),
      value: '',
      topData: JSON.parse(sessionStorage.getItem('topData')),
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      fieldName: '',
      dataSourceList: [], // 物料下拉列表
      pageConfig: []
    }
  },
  computed: {
    selectDiasbled() {
      return this.data.addId || this.data.dataSource === 'sap' ? true : false
    }
  },
  mounted() {
    this.fieldName = this.data.column.field
    this.getBuildSelect()
    this.value = this.data[this.fieldName]
    const _data = {
      ...this.data,
      baseMeasureUnitCode: this.data.unit
    }
    this.confirm(null, [_data])
  },
  methods: {
    handleSelectChange(e) {
      // 下拉框选中事件
      let _data = e.itemData
      this.confirm(null, [_data])
    },
    getParams(text) {
      let params = {
        customerEnterpriseId: this.customerEnterpriseId,
        organizationCode: this.topData.siteCode,
        page: {
          current: 1,
          size: 9999
        }
      }
      const rules = []
      let columnData = [
        {
          width: '150',
          field: 'itemCode',
          headerText: this.$t('物料编号')
        },
        { width: '150', field: 'itemName', headerText: this.$t('物料名称') },
        {
          width: '150',
          field: 'baseMeasureUnitName',
          headerText: this.$t('单位')
        },
        {
          width: '150',
          field: 'baseMeasureUnitCode',
          headerText: this.$t('单位编码')
        },
        {
          width: '150',
          field: 'purchaseGroupName',
          headerText: this.$t('采购组')
        },
        {
          width: '150',
          field: 'purchaseGroupCode',
          headerText: this.$t('采购组编码')
        }
      ]
      for (let i = 0; i < columnData.length; i++) {
        if (i < 1) {
          const field = columnData[i]?.field
          let obj = {
            field,
            label: '',
            operator: field.includes('Code') ? 'equal' : 'contains',
            type: 'string',
            value: text
          }
          rules.push(obj)
        }
      }
      if (rules.length) {
        params.condition = 'and'
        params.rules = rules
      }
      const configParams = {
        customerEnterpriseId: this.customerEnterpriseId,
        organizationCode: this.topData.siteCode
      }
      if (configParams) {
        params = {
          ...params,
          ...configParams
        }
      }
      return params
    },
    filteringList: throttle(function (e) {
      let { text } = e
      if (text) {
        const params = this.getParams(text)
        axios
          .post(
            `/api${PROXY_MDM_AUTH}/item-org-rel/paged-query?BU_CODE=${localStorage.getItem(
              'currentBu'
            )}`,
            params
          )
          .then((res) => {
            const { code, data, msg } = res.data
            if (code === 200) {
              let records = data?.records || []
              this.dataSourceList = records.map((i) => {
                return {
                  ...i,
                  codeAndName: `${i.itemCode}-${i.itemName}`,
                  purchaseGroupCode:
                    i.purchasingBasicInOrgResponse?.purchasingInfo?.purchaseGroupCode,
                  purchaseGroupName:
                    i.purchasingBasicInOrgResponse?.purchasingInfo?.purchaseGroupName,
                  baseMeasureUnitName:
                    i.purchasingBasicInOrgResponse?.itemInfo?.baseMeasureUnitName,
                  baseMeasureUnitCode: i.purchasingBasicInOrgResponse?.itemInfo?.baseMeasureUnitCode
                }
              })
            } else {
              this.$toast({ content: msg, type: 'warning' })
            }
          })
          .catch(() => {
            this.$toast({ content: this.$t('系统异常，请稍后再试'), type: 'warning' })
          })
      }
    }, 300),
    recordDoubleClick(args) {
      this.confirm(null, [args.rowData])
    },
    confirm(e, records) {
      let _itemData = sessionStorage.getItem('itemData')
      _itemData = _itemData ? JSON.parse(_itemData) : []
      if (!records || records.length <= 0) {
        records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      }
      if (records.length <= 0) return
      if (records[0]?.itemCode) {
        // 校验物料是否重复
        const _find = _itemData.find((item) => item.itemCode === records[0]?.itemCode)

        if (_find && e?.type === 'click') {
          this.$toast({
            content: this.$t('物料编码重复'),
            type: 'warning'
          })
          return
        }
        this.dataSourceList = [
          {
            ...records[0],
            codeAndName: `${records[0]?.itemCode}-${records[0]?.itemName}`,
            itemCode: records[0]?.itemCode,
            itemName: records[0]?.itemName,
            purchasingBasicInOrgResponse: {
              purchasingInfo: {
                purchaseGroupCode: records[0]?.purchaseGroupCode,
                purchaseGroupName: records[0]?.purchaseGroupName
              },
              itemInfo: {
                itemUnitDescription: records[0]?.itemUnitDescription,
                baseMeasureUnitCode: records[0]?.baseMeasureUnitCode
              }
            }
          }
        ]
        this.value = records[0]?.itemCode
      }

      this.$set(this.data, this.fieldName, records[0]?.itemCode)

      //联动改变物料描述
      this.$bus.$emit('itemNameChange', records[0]?.itemName)
      this.$bus.$emit('unitChange', records[0]?.baseMeasureUnitCode)
      // 关闭弹窗
      this.handleClose()
    },
    handleClose() {
      this.dialogShow = false
      this.$refs.dialog.ejsRef.hide()
    },
    handleClear() {
      if (this.data.addId || this.data.dataSource === 'sap') return
      this.value = null
      this.$set(this.data, this.fieldName, null)

      //联动改变物料描述
      this.$bus.$emit('itemNameChange', null)
      this.$bus.$emit('unitChange', null)
    },
    getBuildSelect() {
      this.$API.masterData.postBuyerCriteriaQuery().then((res) => {
        this.customerEnterpriseId = res.data[0].customerEnterpriseId
        if (this.value) {
          this.filteringList({ text: this.value })
        }
      })
    },
    serializeList(data) {
      let obj = []
      data.forEach((item) => {
        obj.push({
          item,
          ...item.purchasingBasicInOrgResponse.itemInfo,
          ...item.purchasingBasicInOrgResponse.purchasingInfo
        })
      })
      return obj
    },
    showDialog() {
      if (this.data.addId || this.data.dataSource === 'sap') return
      this.dialogShow = true
      this.pageConfig = [
        {
          useToolTemplate: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[], ['Filter', 'Refresh', 'Setting']]
          },
          gridId: 'e5fd2329-fb52-4171-b3e3-8a3603897751',
          grid: {
            // height: 352,
            // allowPaging: true,
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: [
              {
                width: '150',
                field: 'itemCode',
                headerText: this.$t('物料编号')
              },
              { width: '150', field: 'itemName', headerText: this.$t('物料名称') },
              {
                width: '150',
                field: 'baseMeasureUnitName',
                headerText: this.$t('单位')
              },
              {
                width: '150',
                field: 'baseMeasureUnitCode',
                headerText: this.$t('单位编码')
              },
              {
                width: '150',
                field: 'purchaseGroupName',
                headerText: this.$t('采购组')
              },
              {
                width: '150',
                field: 'purchaseGroupCode',
                headerText: this.$t('采购组编码')
              }
            ],
            asyncConfig: {
              url: `${PROXY_MDM_AUTH}/item-org-rel/paged-query?BU_CODE=${localStorage.getItem(
                'currentBu'
              )}`,
              params: {
                customerEnterpriseId: this.customerEnterpriseId,
                organizationCode: this.topData.siteCode
              },
              serializeList: this.serializeList
            }
          }
        }
      ]
      this.$refs.dialog.ejsRef.show()
    }
  }
}
</script>

<style scoped lang="scss">
.in-cell {
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;

  /deep/ .mt-select {
    .e-input-group-icon,
    .e-ddl-icon,
    .e-search-icon {
      margin-right: 40px;
    }
  }
  > .mt-icons {
    margin-left: 5px;
    cursor: pointer;
    position: absolute;
    top: 8px;
    right: 0;
  }
}

.full-height {
  height: 100%;
}
/deep/.template-height {
  .mt-data-grid {
    height: 100%;

    > .e-grid {
      height: calc(100% - 40px);
      .e-content {
        height: 100% !important;
      }
    }
  }
}

/deep/.template-height.has-page {
  .repeat-template .mt-data-grid > .e-control {
    height: calc(100% - 40px) !important;
  }
}
</style>
