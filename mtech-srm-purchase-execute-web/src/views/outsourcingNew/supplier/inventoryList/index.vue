<!--盘点单列表-->
<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>
<script>
import { pageConfig } from './config/index'
import utils from '@/utils/utils'
export default {
  data() {
    return {
      pageConfig: pageConfig
    }
  },
  mounted() {},
  methods: {
    //表头点击
    handleClickToolBar(e) {
      const _selectRows = e.grid.getSelectedRecords()
      if (_selectRows.length <= 0 && (e.toolbar.id == 'submit' || e.toolbar.id == 'delete')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        //新增
        this.handleClickToolBarAdd()
      }
      if (e.toolbar.id == 'submit') {
        //提交
        this.handleClickToolBarSubmit(_selectRows)
      }
      if (e.toolbar.id == 'delete') {
        //删除
        this.handleClickToolBarDelete(_selectRows)
      }
    },
    //行内点击
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id == 'submit') {
        //提交
        this.handleClickCellToolSubmit(data)
      } else if (tool.id == 'editor') {
        //编辑
        this.handleClickCellToolEditor(data)
      } else if (tool.id == 'delete') {
        //删除
        this.handleClickCellToolDelete(data)
      }
    },
    // 点击单元格 标题
    handleClickCellTitle(e) {
      if (e.field === 'inventoryCode') {
        this.$router.push({
          path: `new-supplier-inventory-detail`,
          query: {
            // key: utils.randomString(),
            id: e.data.id,
            inventoryCode: e.data.inventoryCode,
            type: 'view'
          }
        })
      }
    },
    //头部--创建盘点单
    handleClickToolBarAdd() {
      this.$router.push({
        path: `new-supplier-inventory-detail`,
        key: utils.randomString(),
        query: { type: 'add' }
      })
    },
    //头部--提交
    handleClickToolBarSubmit(_selectRows) {
      let ids = _selectRows.map((item) => item.id)
      let parameter = {
        ids: ids
      }
      this.$API.Inventory.submit(parameter).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      })
    },
    //头部--删除
    handleClickToolBarDelete(_selectRows) {
      let idList = _selectRows.map((item) => item.id)
      let parameter = {
        idList: idList,
        remark: ''
      }
      this.$API.Inventory.inventoryDelete(parameter).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      })
    },
    //行内--提交
    handleClickCellToolSubmit(data) {
      let _selectRows = [data]
      this.handleClickToolBarSubmit(_selectRows)
    },
    //行内--编辑
    handleClickCellToolEditor(data) {
      this.$router.push({
        path: `new-supplier-inventory-detail`,
        query: {
          inventoryCode: data.inventoryCode,
          id: data.id,
          // key: utils.randomString(),
          type: 'edit'
        }
      })
    },
    //行内--删除
    handleClickCellToolDelete(data) {
      let _selectRows = [data]
      this.handleClickToolBarDelete(_selectRows)
    }
  }
}
</script>
<style lang="scss" scoped></style>
