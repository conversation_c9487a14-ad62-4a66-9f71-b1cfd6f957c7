export const inventoryList = {
  code: 200,
  msg: '执行成功',
  data: {
    records: [
      {
        tenantId: '17706479458443265',
        customerTenantId: '10000',
        inventoryCode: 'PD20220407201100013',
        companyCode: '123',
        companyName: '123',
        siteCode: '',
        siteName: '',
        year: 10,
        month: 0,
        remark: '',
        status: 0,
        syncStatus: 0,
        abolished: 0,
        version: 1,
        supplierCode: '',
        supplierName: '',
        contrastTime: '0'
      },
      {
        tenantId: '17706479458443266',
        customerTenantId: '10000',
        inventoryCode: 'PD20220407201100014',
        companyCode: '123',
        companyName: '123',
        siteCode: '',
        siteName: '',
        year: 10,
        month: 0,
        remark: '',
        status: 1,
        syncStatus: 0,
        abolished: 0,
        version: 1,
        supplierCode: '',
        supplierName: '',
        contrastTime: '0'
      },
      {
        tenantId: '17706479458443267',
        customerTenantId: '10000',
        inventoryCode: 'PD20220407201100015',
        companyCode: '123',
        companyName: '123',
        siteCode: '',
        siteName: '',
        year: 10,
        month: 0,
        remark: '',
        status: 2,
        syncStatus: 0,
        abolished: 0,
        version: 1,
        supplierCode: '',
        supplierName: '',
        contrastTime: '0'
      },
      {
        tenantId: '17706479458443268',
        customerTenantId: '10000',
        inventoryCode: 'PD20220407201100016',
        companyCode: '123',
        companyName: '123',
        siteCode: '',
        siteName: '',
        year: 10,
        month: 0,
        remark: '',
        status: 3,
        syncStatus: 0,
        abolished: 0,
        version: 1,
        supplierCode: '',
        supplierName: '',
        contrastTime: '0'
      }
    ],
    total: '1',
    size: '10',
    current: '1',
    orders: [
      {
        column: 'create_time',
        asc: false
      }
    ],
    optimizeCountSql: true,
    hitCount: false,
    countId: null,
    maxLimit: null,
    searchCount: true,
    pages: '1'
  },
  errorStackTrace: null
}
