import { i18n } from '@/main.js'
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('创建盘点单') },
  { id: 'submit', icon: 'icon_solid_Submit', title: i18n.t('提交') },
  { id: 'delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
  // { id: "submit", icon: "icon_solid_submit", title: "对比差异" },
]
const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'inventoryCode',
    headerText: i18n.t('盘点单号'),
    cellTools: []
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { status: 0, label: i18n.t('草稿'), cssClass: 'title-#9baac1' },
        { status: 1, label: i18n.t('已提交'), cssClass: 'title-#6386c1' },
        { status: 2, label: i18n.t('已对比'), cssClass: 'title-#9baac1' },
        { status: 3, label: i18n.t('已取消'), cssClass: 'title-#6386c1' },
        { status: 4, label: i18n.t('已确认'), cssClass: 'title-#6386c1' },
        { status: 5, label: i18n.t('待确认'), cssClass: 'title-#6386c1' },
        { status: 6, label: i18n.t('已拒绝'), cssClass: 'title-#6386c1' },
        { status: 7, label: i18n.t('失效'), cssClass: 'title-#6386c1' },
        { status: 8, label: i18n.t('采方已确认'), cssClass: 'title-#6386c1' }
      ],
      fields: { text: 'label', value: 'status' }
    },
    cellTools: [
      {
        id: 'submit',
        title: i18n.t('提交'),
        visibleCondition: (data) => {
          return data['status'] === 0
        }
      },
      {
        id: 'editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['status'] == 0 || data['status'] == 6
        }
      },
      {
        id: 'delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data['status'] == 0
        }
      }
    ]
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商')
  },
  {
    field: 'year',
    headerText: i18n.t('年份')
  },
  {
    field: 'month',
    headerText: i18n.t('月份')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    field: 'receiveTime',
    headerText: i18n.t('接收时间')
  },
  {
    field: 'contrastTime',
    headerText: i18n.t('对比时间')
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
export const pageConfig = [
  {
    toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    useCombinationSelection: false,
    grid: {
      columnData,
      asyncConfig: {
        url: '/srm-purchase-execute/tenant/inventoryStocktaking/querySupHeader',
        params: {}
      }
    }
  }
]
