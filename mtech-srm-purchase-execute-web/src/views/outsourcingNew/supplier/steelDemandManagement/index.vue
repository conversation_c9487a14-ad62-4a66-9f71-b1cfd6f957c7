<!-- 供方 - 钢材需求管理 -->
<template>
  <div>
    <mt-tabs
      :e-tab="false"
      :data-source="tabList"
      :selected-item="tabIndex"
      @handleSelectTab="handleSelectTab"
    />
    <div>
      <List v-if="tabIndex === 0" />
      <Detail v-if="tabIndex === 1" />
    </div>
  </div>
</template>

<script>
export default {
  components: {
    List: () => import('./components/list.vue'),
    Detail: () => import('./components/detail.vue')
  },
  data() {
    return {
      tabList: [{ title: this.$t('钢材需求单') }, { title: this.$t('钢材需求明细') }],
      tabIndex: 0
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    }
  }
}
</script>
