import { i18n } from '@/main.js'
import Vue from 'vue'
export const columnData = [
  {
    field: 'itemCode',
    width: '250',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },
  {
    field: 'dn', // new
    headerText: i18n.t('交货单号'),
    allowEditing: false
  },
  {
    field: 'dnItem', // new
    headerText: i18n.t('交货单行号'),
    allowEditing: false
  },
  {
    field: 'planGroupName',
    headerText: i18n.t('计划组'), // new
    allowEditing: false
    // editTemplate: () => {
    //   return { template: InputView };
    // },
  },
  {
    field: 'buyerGroupName',
    headerText: i18n.t('采购组'), // new
    allowEditing: false
    // editTemplate: () => {
    //   return { template: InputView };
    // },
  },
  {
    field: 'allocationQuantity',
    headerText: i18n.t('调出数量')
  },
  {
    field: 'batch',
    headerText: '批次/卷号'
  },
  {
    field: 'maxDemandQuantity',
    headerText: i18n.t('可调拨数量')
  },
  {
    field: 'packageMinQuantity', // new
    headerText: i18n.t('最小包装量')
  },
  {
    field: 'supplierStock',
    headerText: i18n.t('库存现有量')
  },

  {
    field: 'wmsAllocationQuantity',
    headerText: i18n.t('实收数量')
  },
  {
    field: 'basicUnitCode',
    headerText: i18n.t('基本单位'), // new
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.basicUnitCode}}-{{data.basicUnitName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'stockUnitCode',
    headerText: i18n.t('库存单位')
  },

  {
    field: 'remark',
    headerText: i18n.t('行备注')
  }
]
