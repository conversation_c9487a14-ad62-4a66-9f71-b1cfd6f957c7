<template>
  <!-- <div class="detail-fix-wrap full-height"> -->
  <div class="full-height pt20 vertical-flex-box">
    <top-info
      ref="headerTop"
      :header-info="headerInfo"
      v-if="this.headerInfo"
      @rejectTo="rejectTo"
      @startTo="startTo"
      class="flex-keep"
    ></top-info>
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig1"
      @cellEdit="cellEdit"
      :hidden-tabs="true"
    />
  </div>
  <!-- </div> -->
</template>

<script>
import { columnData } from './config/index.js'

export default {
  components: {
    topInfo: require('./components/topInfo.vue').default
  },
  data() {
    const headerInfo = JSON.parse(localStorage.getItem('purchaseListScope'))
    const id = this.$route.query.id
    const type = this.$route.query.type

    return {
      id,
      apiWaitingQuantity: 0, // 调用的api正在等待数

      userInfo: null,
      headerInfo,
      type,
      pageConfig1: [
        {
          grid: {
            columnData: columnData,
            lineIndex: 0,
            allowPaging: false, // 不分页
            // autoWidthColumns: columnData.length + 1,
            dataSource: []
            // asyncConfig: {
            //   url: "/srm-purchase-execute/tenant/_distribute_rule/list",
            // serializeList: serializeList,
            // },
            // frozenColumns: 1,
          }
        }
      ],
      currentTabIndex: 0,
      forecastRules: []
    }
  },
  mounted() {
    // this.currentInfo.title = "物料信息";
    this.init()
    console.log(this.headerInfo)
    // if (this.$route.query.type === "are") {
    //   this.pageConfig1[0].grid = this.grid1;
    // } else if (this.$route.query.type === "no") {
    //   this.pageConfig1[0].grid = this.grid2;
    // } else {
    //   this.pageConfig1[0].grid = {};
    // }
  },
  methods: {
    // 初始化
    init() {
      let obj = {
        allocationOrderCode: this.$route.query.allocationOrderCode
      }
      this.$API.outsourcing
        .outAllocationNewOrderGetOne(obj)
        .then((res) => {
          this.headerInfo = res.data
          this.pageConfig1[0].grid.dataSource = res.data.detailResponseList
        })
        .catch(() => {
          // this.apiEndLoading();
        })
    },

    handleSelectTab(index, item) {
      this.currentInfo = item
      console.log(item)
      // this.currentTabIndex = e;
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    },
    // 行编辑
    cellEdit(e) {
      let obj = e.value
      //
      this.pageConfig1[0].grid.dataSource.forEach((item) => {
        if (item.id === obj.id) {
          item.wmsAllocationQuantity = obj.wmsAllocationQuantity
        }
      })
    }
  },
  // 消失
  deactivated() {
    localStorage.removeItem('purchaseListScope')
  }
}
</script>

<style lang="scss" scoped>
.repeat-template {
  .common-template-page {
    /deep/ .mt-tabs {
      display: none;
    }
  }
}
.e-content {
  height: 200px;
}

.mt-tabs {
  width: 100%;
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
</style>
