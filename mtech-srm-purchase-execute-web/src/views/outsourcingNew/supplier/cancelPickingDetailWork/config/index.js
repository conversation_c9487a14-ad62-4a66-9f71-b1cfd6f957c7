import { i18n } from '@/main.js'
import Vue from 'vue'
export const columnData = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },
  {
    field: 'cancelQuantity',
    headerText: i18n.t('退货数量'),
    width: '150'
  },
  // {
  //   // width: "150",
  //   field: "maxCancelQuantity",
  //   headerText: i18n.t("可退货数量"),
  // },
  {
    // width: "150",
    field: 'maxCancelQuantity',
    headerText: i18n.t('可退货数量')
  },
  {
    width: '150',
    field: 'stockQuantity',
    headerText: i18n.t('库存现有量')
  },
  {
    field: 'warehouseName',
    headerText: i18n.t('库存地点'),
    width: 200,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.warehouseName}}{{data.warehouseCode}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  // {
  //   field: "stockUnitCode",
  //   headerText: i18n.t("库存单位"),
  // },
  {
    field: 'basicUnitCode',
    headerText: i18n.t('基本单位'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.basicUnitCode}}-{{data.basicUnitName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  // {
  //   width: "150",
  //   field: "buyerOrgName",
  //   headerText: i18n.t("采购组"),
  // },
  {
    field: 'buyerGroupName', // new
    headerText: i18n.t('采购组'),
    allowEditing: false
  },
  {
    field: 'dn', // new
    headerText: i18n.t('交货单号'),
    allowEditing: false
  },
  {
    field: 'dnItem', // new
    headerText: i18n.t('交货单行号'),
    allowEditing: false
  },
  {
    field: 'planGroupName', // new
    headerText: i18n.t('计划组'),
    allowEditing: false
  },
  {
    field: 'batch',
    headerText: '批次/卷号'
  },
  {
    width: '150',
    field: 'wmsCancelQuantity',
    headerText: i18n.t('实退数量')
  },
  {
    width: '150',
    field: 'wmsCancelStatus',
    headerText: i18n.t('退货状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('未退货'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('全部退货'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('部分退货'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('行备注')
  }
]
