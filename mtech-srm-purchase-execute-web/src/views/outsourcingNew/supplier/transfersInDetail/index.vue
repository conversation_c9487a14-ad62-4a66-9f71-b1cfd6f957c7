<template>
  <div class="full-height pt20 vertical-flex-box">
    <top-info
      ref="headerTop"
      :header-info="headerInfo"
      v-if="this.headerInfo"
      @rejectTo="rejectTo"
      @startTo="startTo"
      class="flex-keep"
    ></top-info>
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig1"
      @cellEdit="cellEdit"
      :hidden-tabs="true"
    />
    <deliver
      @handleAddDialogShow="handleAddDialogShow"
      @resolveClick="resolveClick"
      ref="headerTop"
      v-if="deliveryShow"
      class="flex-keep"
    ></deliver>
  </div>
</template>

<script>
import { columnData } from './config/index.js'

export default {
  components: {
    topInfo: require('./components/topInfo.vue').default,
    deliver: require('./components/deliver.vue').default
  },
  data() {
    const id = this.$route.query.id
    const type = this.$route.query.type

    return {
      id,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      deliveryShow: false,
      userInfo: null,
      headerInfo: {},
      type,
      pageConfig1: [
        {
          grid: {
            columnData: columnData,
            lineIndex: 0,
            allowPaging: false, // 不分页
            // autoWidthColumns: columnData.length + 1,
            dataSource: []
            // asyncConfig: {
            //   url: "/srm-purchase-execute/tenant/_distribute_rule/list",
            // serializeList: serializeList,
            // },
            // frozenColumns: 1,
          }
        }
      ],
      currentTabIndex: 0,
      forecastRules: []
    }
  },
  mounted() {
    // this.currentInfo.title = "物料信息";
    this.init()
    // if (this.$route.query.type === "are") {
    //   this.pageConfig1[0].grid = this.grid1;
    // } else if (this.$route.query.type === "no") {
    //   this.pageConfig1[0].grid = this.grid2;
    // } else {
    //   this.pageConfig1[0].grid = {};
    // }
  },
  methods: {
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    resolveClick(value) {
      let obj = {
        ids: this.headerInfo.id,
        allocationReason: value
      }
      this.$API.outsourcing
        .outNewAllocationOrderReject(obj)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$router.go(-1)
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef.refreshCurrentGridData()
        })
      this.deliveryShow = false
    },
    rejectTo() {
      this.deliveryShow = true
    },
    startTo() {
      this.certain(this.headerInfo.id)
    },
    cellEdit(e) {
      let obj = e.value
      //
      this.pageConfig1[0].grid.dataSource.forEach((item) => {
        if (item.id === obj.id) {
          item.wmsAllocationQuantity = obj.wmsAllocationQuantity
        }
      })
      // obj.column = "";
      // this.$API.outsourcing
      //   .buyerSaveDetail(JSON.parse(JSON.stringify(obj)))
      //   .then((res) => {
      //     if (res.code === 200) {
      //       this.$toast({ content: this.$t("操作成功"), type: "success" });
      //       this.init();
      //     }
      //   });
    },
    // 确认
    certain() {
      this.headerInfo.detailRequestList = this.pageConfig1[0].grid.dataSource
      this.headerInfo.isOutSale = this.headerInfo.isOutSale === '是' ? 1 : 0
      this.$API.outsourcing
        .outNewAllocationOrderConfirmAndUpdate(this.headerInfo)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$router.go(-1)
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    // 初始化
    init() {
      let obj = {
        allocationOrderCode: this.$route.query.allocationOrderCode
      }
      this.$API.outsourcing
        .outNewAllocationOrderInSupplierGetOne(obj)
        .then((res) => {
          this.headerInfo = res.data
          if (res.data.status === 1) {
            res.data.detailResponseList.forEach((item) => {
              item.wmsAllocationQuantity = item.allocationQuantity
            })
          }
          this.pageConfig1[0].grid.dataSource = res.data.detailResponseList
          this.pageConfig1[0].grid.dataSource.forEach((item) => {
            // if (item.wmsAllocationQuantity === null) {
            //   item.wmsAllocationQuantity = item.allocationQuantity;
            // }
            item.type = res.data.status
          })
        })
        .catch(() => {
          // this.apiEndLoading();
        })
    },
    handleSelectTab(index, item) {
      this.currentInfo = item
      console.log(item)
      // this.currentTabIndex = e;
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  },
  // 消失
  deactivated() {
    localStorage.removeItem('purchaseListScope')
  }
}
</script>

<style lang="scss" scoped>
.repeat-template {
  .common-template-page {
    /deep/ .mt-tabs {
      display: none;
    }
  }
}
.e-content {
  height: 200px;
}

.mt-tabs {
  width: 100%;
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
</style>
