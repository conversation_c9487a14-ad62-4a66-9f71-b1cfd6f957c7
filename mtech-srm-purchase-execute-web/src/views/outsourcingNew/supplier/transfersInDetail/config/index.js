import { i18n } from '@/main.js'
import Vue from 'vue'
export const columnData = [
  {
    field: 'itemCode',
    width: '250',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },
  {
    field: 'dn', // new
    headerText: i18n.t('交货单号'),
    allowEditing: false
  },
  {
    field: 'dnItem', // new
    headerText: i18n.t('交货单行号'),
    allowEditing: false
  },
  {
    field: 'batch',
    headerText: '批次/卷号'
  },
  {
    field: 'supplierStock',
    headerText: i18n.t('库存现有量')
  },
  {
    field: 'maxDemandQuantity',
    headerText: i18n.t('可调拨数量')
  },
  {
    field: 'allocationQuantity',
    headerText: i18n.t('调出数量')

    // validationRules: { required: true },
  },
  {
    field: 'wmsAllocationQuantity',
    headerText: i18n.t('实收数量'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div><mt-input-number precision="3" :min="0" v-if='data.status === 1' v-model="data.wmsAllocationQuantity"  cssClass="e-outline" :show-clear-button="false" @blur="handleChange"></mt-input-number>
          <div v-else>{{data.wmsAllocationQuantity}}</div>
          </div> `,
          data: function () {
            return {
              data: {},
              type: JSON.parse(localStorage.getItem('purchaseListScope'))
            }
          },
          mounted() {},
          methods: {
            handleChange() {
              console.log(this.data)
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'wmsAllocationQuantity',
                value: this.data
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'planGroupName',
    headerText: i18n.t('计划组'), // new
    allowEditing: false
    // editTemplate: () => {
    //   return { template: InputView };
    // },
  },
  {
    field: 'buyerGroupName',
    headerText: i18n.t('采购组'), // new
    allowEditing: false
    // editTemplate: () => {
    //   return { template: InputView };
    // },
  },
  {
    field: 'packageMinQuantity', // new
    headerText: i18n.t('最小包装量')
  },
  {
    field: 'basicUnitCode',
    headerText: i18n.t('基本单位'), // new
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.basicUnitCode}}-{{data.basicUnitName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'stockUnitCode',
    headerText: i18n.t('库存单位')
  },
  // {
  //   field: "", // 只是界面显示
  //   headerText: i18n.t("最小包装量"),
  // },

  {
    field: 'remark',
    headerText: i18n.t('行备注')
  }
]
