<template>
  <div>
    <div class="in-cell">
      <mt-select
        :id="data.column.field"
        v-model="data[data.column.field]"
        :data-source="dataSource"
        :fields="fields"
        :placeholder="placeholder"
        @change="selectChange"
        :open-dispatch-change="true"
        :disabled="isDisabled"
        :filtering="filtering"
        :allow-filtering="true"
      ></mt-select>
      <mt-icon style="width: 20px" name="icon_input_search" @click.native="showDialog"></mt-icon>
    </div>
    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="title"
      :buttons="buttons"
      @close="handleClose"
    >
      <div class="full-height">
        <mt-template-page
          ref="templateRef"
          class="template-height has-page"
          :hidden-tabs="true"
          :template-config="pageConfig"
          @recordDoubleClick="recordDoubleClick"
        ></mt-template-page>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import { PROXY_MDM_AUTH } from '@/utils/constant'

export default {
  data() {
    return {
      title: this.$t('请选择'),
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          toolbar: [],
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: '5508158f-d619-4f2c-afb6-962c407499f3',
          grid: {
            // height: 352,
            // allowPaging: true,
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: [
              {
                width: '150',
                field: 'itemCode',
                headerText: this.$t('物料编号')
              },
              {
                width: '150',
                field: 'itemName',
                headerText: this.$t('物料名称')
              }
              // {
              //   width: "150",
              //   field: "itemUnitDescription",
              //   headerText: this.$t("单位"),
              // },
              // {
              //   width: "150",
              //   field: "purchaseGroupName",
              //   headerText: this.$t("采购组"),
              // },
              // {
              //   width: "150",
              //   field: "batchCode",
              //   headerText: this.$t("批次号"),
              // },
            ],
            asyncConfig: {},
            dataSource: []
          }
        }
      ],
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'codeAndName', value: 'itemCode' },
      dataSource: [],
      codeArr: JSON.parse(sessionStorage.getItem('codeArr')),
      itemCode: '',
      itemId: '',
      purchase: '', //采购组code
      plan: '', //计划组code
      isDisabled: false,

      entryData: JSON.parse(sessionStorage.getItem('entryData'))
    }
  },

  mounted() {
    if (this.data.column.field === 'warehouseName') {
      this.$bus.$on('warehouseChange', (val) => {
        console.log(val)
        if (val.length > 0) {
          val.forEach((item) => {
            item.name = item.warehouseCode + item.warehouseName // 	客户名称
            item.value = item.warehouseName == null ? '' : item.warehouseName // 	客户名称
          })
          this.dataSource =
            val.map((i) => {
              return {
                ...i,
                codeAndName: `${i.itemCode} - ${i.itemName}`
              }
            }) || []
          console.log(this.dataSource)
          this.fields = { text: 'name', value: 'value' }
          this.data.warehouseName = val[0].warehouseName
          this.data.warehouseCode = val[0].warehouseCode

          this.$bus.$emit('batchChange', val[0])
        }
      })
    }
    if (this.data.column.field === 'itemCode') {
      this.data.itemCode === null || this.data.itemCode === undefined
        ? this.itemData('')
        : this.itemData(this.data.itemCode)
      console.log(this.data.itemCode)
      // this.itemData(this.data.itemCode);
    }
    this.dataSource = this.data.column.selectOptions.map((i) => {
      return {
        ...i,
        codeAndName: `${i.itemCode} - ${i.itemName}`
      }
    })
  },

  methods: {
    recordDoubleClick(args) {
      this.selectChange({ itemData: args.rowData })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    confirm(e, records) {
      if (!records || records.length <= 0) {
        records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      }
      if (records.length <= 0) return
      this.selectChange({ itemData: records[0] })
    },
    showDialog() {
      this.pageConfig[0].grid.asyncConfig = {
        url: `${PROXY_MDM_AUTH}/item-org-rel/paged-query?BU_CODE=${localStorage.getItem(
          'currentBu'
        )}`,
        recordsPosition: 'data.records',
        params: {
          customerEnterpriseId: this.codeArr.buyerEnterpriseId,
          organizationCode: this.codeArr.siteCode
        }
      }
      this.$refs.dialog.ejsRef.show()
    },
    filtering(e) {
      // var searchData = this.dataSource;
      if (this.data.column.field === 'itemCode') {
        this.itemData(e.text)
      }
    },

    // startOpen() {
    //   console.log(this.data, "下拉打开时最新的行数据");

    //   if (this.data.column.field === "siteName") {
    //     //工厂下拉

    //     if (!this.data.itemCode && !this.itemCode) {
    //       this.$toast({
    //         content: this.$t("请先选择物料信息"),

    //         type: "warning",
    //       });
    //     }
    //   }
    //   if (this.data.column.field === "itemCode") {
    //     this.itemData("");
    //   }
    // },

    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      console.log(this.data, '下拉数据的信息')
      if (this.data.column.field === 'itemCode') {
        this.data[this.data.column.field] = val.itemData?.itemCode
        let obj = {
          buyerEnterpriseId: this.codeArr.buyerEnterpriseId,
          createType: 2,
          itemCode: val.itemData.itemCode,
          siteCode: this.codeArr.siteCode,
          buyerOrgCode: this.codeArr.buyerOrgCode,
          orderCode: this.codeArr?.orderCode,
          orderItemNo: this.codeArr?.orderItemNo,
          isOutSale: this.codeArr.isOutSale,
          id: this.data?.id,
          supplierCode: this.codeArr.supplierCode
        }
        this.$API.outsourcing.QuerySapOutDemand(obj).then((res) => {
          this.$bus.$emit('itemNameChange', res.data[0].itemName) //传给物料名称
          this.$bus.$emit('planGroupNameChange', res.data[0].planGroupName) // 计划组

          this.$bus.$emit('orderCodeChange', res.data[0].orderCode) //
          this.$bus.$emit('orderItemNoChange', res.data[0].orderItemNo) //

          this.$bus.$emit('buyerOrgNameChange', {
            buyerGroupName: res.data[0].buyerGroupName,
            buyerGroupCode: res.data[0].buyerGroupCode
          }) // 采购组
          this.$bus.$emit('stockUnitCodeChange', res.data[0].stockUnitCode) //传给单位

          this.$bus.$emit('basicUnitCodeChange', {
            basicUnitCode: res.data[0].basicUnitCode,
            basicUnitName: res.data[0].basicUnitName
          }) //传给单位
          this.$bus.$emit(
            'maxDemandQuantityChange',
            this.codeArr.isOutSale === '1' ? 0 : res.data[0].maxReceiveQuantity
          ) //

          this.data.buyerGroupName = val.buyerGroupName
          this.data.buyerGroupCode = val.buyerGroupCode

          this.$parent.$emit('selectedChanged', {
            //传出额外数据
            fieldCode: 'buyerGroupName',
            itemInfo: {
              buyerGroupName: res.data[0].buyerGroupName,
              buyerGroupCode: res.data[0].buyerGroupCode,
              basicUnitCode: res.data[0].basicUnitCode,
              basicUnitName: res.data[0].basicUnitName
            }
          })

          this.$bus.$emit('supplierStockChange', res.data[0].maxDemandQuantity) //传给库存现有
          this.$bus.$emit('roundingOffChange', res.data[0].roundingOff) //
        })
        this.handleClose()
      }
    },
    itemData(e) {
      let params = {
        page: {
          current: 1,
          size: 20
        },
        customerEnterpriseId: this.codeArr.buyerEnterpriseId,
        organizationCode: this.codeArr.siteCode,
        rules: [
          {
            field: 'itemCode',
            operator: 'contains',
            value: e
          }
          // {
          //   field: "organizationCode",
          //   operator: "equal",
          //   value: this.codeArr.siteCode,
          // },
        ]
      }
      this.$API.masterData.getOrgRel(params).then((res) => {
        this.dataSource =
          res.data?.records.map((i) => {
            return {
              ...i,
              codeAndName: `${i.itemCode} - ${i.itemName}`
            }
          }) || []
      })
    }
  },
  deactivated() {
    this.$bus.$off('batchChange2')
    this.$bus.$off('batchChange')
    this.$bus.$off('maxDemandQuantityChange')
    this.$bus.$off('warehouseChange')
    this.$bus.$off('itemNameChange')
    this.$bus.$off('warehouseChange2')
  }
}
</script>

<style scoped lang="scss">
.in-cell {
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;

  /deep/ .mt-select {
    .e-input-group-icon,
    .e-ddl-icon,
    .e-search-icon {
      margin-right: 20px;
    }
  }
  > .mt-icons {
    margin-left: 5px;
    cursor: pointer;
    position: absolute;
    top: 8px;
    right: 0;
  }
}
</style>
