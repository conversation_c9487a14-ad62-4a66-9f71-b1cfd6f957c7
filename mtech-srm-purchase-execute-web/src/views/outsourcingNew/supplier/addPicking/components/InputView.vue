<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="disabled"
      @input="onInput"
    ></mt-input>
  </div>
</template>
<script>
export default {
  data() {
    return { data: {}, disabled: true }
  },
  mounted() {
    this.$bus.$on('itemNameChange', (val) => {
      this.data.itemName = val
    }) //接受的物料名称
    this.$bus.$on('buyerOrgNameChange', (val) => {
      this.data.buyerGroupName = val.buyerGroupName
      this.data.buyerGroupCode = val.buyerGroupCode

      this.$parent.$emit('selectedChanged', {
        //传出额外数据
        fieldCode: 'buyerGroupName',
        itemInfo: {
          ...this.data
        }
      })
    }) //接受的采购组
    this.$bus.$on('planGroupNameChange', (val) => {
      this.data.planGroupName = val
    }) //接受的计划组

    // this.$bus.$on("stockUnitChange", (val) => {
    //   this.data.stockUnit = val;
    // }); //接受的单位
    this.$bus.$on('stockUnitCodeChange', (val) => {
      this.data.stockUnitCode = val
    })
    this.$bus.$on('basicUnitCodeChange', (val) => {
      this.data.basicUnitCode = val.basicUnitCode
      this.data.basicUnitName = val.basicUnitName
      this.$parent.$emit('selectedChanged', {
        //传出额外数据
        fieldCode: 'basicUnitCode',
        itemInfo: {
          ...this.data
        }
      })
    }) //接受的单位

    this.$bus.$on('maxDemandQuantityChange', (val) => {
      this.data.maxReceiveQuantity = val
    }) //
    this.$bus.$on('supplierStockChange', (val) => {
      this.data.maxDemandQuantity = val
    })

    this.$bus.$on('orderCodeChange', (val) => {
      this.data.orderCode = val
    }) //
    this.$bus.$on('orderItemNoChange', (val) => {
      this.data.orderItemNo = val
    }) //
    this.$bus.$on('roundingOffChange', (val) => {
      this.data.roundingOff = val
    }) //
  },
  methods: {
    onInput(e) {
      console.log(e)
    }
  }
}
</script>
