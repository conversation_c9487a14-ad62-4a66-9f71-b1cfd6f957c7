<template>
  <!-- <div class="detail-fix-wrap full-height"> -->
  <div class="full-height pt20 vertical-flex-box">
    <top-info
      @deleteRe="deleteRe"
      :top-info-e="topInfo"
      :entry-type="entryType"
      :entry-new-rule-form="entryNewRuleForm"
      @submit="submit"
      @onchang="onchang"
      @save="save"
      @updateDataSource="updateDataSource"
      @newSave="newSave"
      @newSubmit="newSubmit"
      class="flex-keep"
      ref="topInfo"
    ></top-info>
    <!-- <mt-data-grid
      :column-data="todoListColumnData"
      :data-source="[]"
      :edit-settings="editSettings"
      :toolbar="toolbar"
    ></mt-data-grid> -->
    <mt-template-page
      ref="templateRef23"
      :template-config="pageConfig"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
      @selectedChanged="selectedChanged"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      v-show="topInfo.createType == '2' || entryType == 'readonly'"
    >
    </mt-template-page>
    <mt-template-page
      class="bottom-box"
      ref="templateRef2"
      :template-config="pageConfig2"
      @actionBegin="actionBegin2"
      @handleClickToolBar="handleClickToolBar2"
      v-show="topInfo.createType == '1' && (entryType == 'edit' || !entryType)"
    >
    </mt-template-page>
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :is-show-tips="true"
      :request-urls="requestUrls"
      @closeUploadExcel="handleImport(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
    <!-- </div> -->
  </div>
  <!-- </div> -->
</template>
<script>
import { cloneDeep } from 'lodash'
import {
  todoListColumnData2,
  todoListColumnData3,
  pageConfig,
  pageConfig2,
  pageConfig3,
  editSettings
} from './config/index.js'
import { utils } from '@mtech-common/utils'
export default {
  components: {
    TopInfo: () => import('./components/topInfo'),
    UploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    return {
      beginData: null,
      downTemplateParams: {
        pageFlag: false
      }, // 通知配置导入下载模板参数
      uploadParams: {}, // 导入通知配置文件参数

      requestUrls: {
        templateUrlPre: 'outsourcing',
        templateUrl: 'outReceiveOrderTemplate', // 下载模板接口方法名
        uploadUrl: 'outReceiveOrderImport' // 上传接口方法名
      },
      entryNewRuleForm: [],
      todoListColumnData2,
      saveList: null,
      toolbar: ['add', 'delete'],
      editSettings,
      pageConfig:
        this.$route.query.receiveOrderCode && this.$route.query.type === 'edit'
          ? pageConfig3(this.$route.query.receiveOrderCode)
          : this.$route.query.receiveOrderCode && this.$route.query.type === 'readonly'
          ? pageConfig(this.$route.query.receiveOrderCode)
          : pageConfig2(),
      entryId: '', //编辑带入的订单id
      // entryType: null, //1是新增 2是编辑 3是反馈异常
      entrySource: '', // 0采购申请 1手工创建 2商城进入 4合同进入
      entryDraft: '', //1是草稿 2不是草稿 草稿可以修改
      topInfo: {
        orderCode: '',
        siteCode: '',
        warehouseCode: '',
        createType: '2',
        customerEnterpriseId: ''
        // isOutSale: "0",
      },
      showpage: true,
      receiveOrderCode: '',

      orderDetailRequestList: [],
      id: '',
      entryType: this.$route.query.type,
      pageConfig2: [
        {
          title: this.$t('物料信息'),
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [
            [
              {
                id: 'Delete',
                icon: 'icon_solid_Delete',
                title: this.$t('删除')
              }
            ]
          ],

          grid: {
            allowPaging: false,
            editSettings: {
              allowEditing: true,
              allowAdding: false,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              // showDeleteConfirmDialog: true,
              newRowPosition: 'Top'
            },
            columnData: todoListColumnData3,
            dataSource: []
          }
        }
      ],
      addId: '1',
      newSaveId: ''
    }
  },
  created() {
    if (this.entryType == 'readonly') {
      this.pageConfig[0].toolbar = []
      this.pageConfig[0].grid.columnData = todoListColumnData2
      this.pageConfig[0].grid.editSettings = {}
      // this.codeChange({})
      // this.pageConfig[0].editSettings.allowEditing = false;
    }
  },
  watch: {
    showpage: {
      handler(newVal) {
        if (newVal) {
          this.$nextTick(() => {
            window.outGrid = this.$refs.templateRef23
          })
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.receiveOrderCode = this.$route.query.receiveOrderCode
    if (this.receiveOrderCode) {
      this.getOnePicking()
    }
    this.save = utils.debounce(this.save, 1000)
  },
  methods: {
    updateDataSource(e) {
      this.pageConfig2[0].grid.dataSource = e
    },

    handleClickToolBar2(e) {
      if (e.toolbar.id === 'Delete') {
        let selectRows = e.gridRef.getMtechGridRecords()
        let list = this.$refs.templateRef2
          .getCurrentUsefulRef()
          .gridRef.ejsRef.getCurrentViewRecords()
        let newList = []
        list.forEach((item) => {
          let hasItem = selectRows.some((item1) => {
            return item.addId == item1.addId
          })
          if (!hasItem) {
            newList.push(item)
          }
        })
        this.pageConfig2[0].grid.dataSource = newList
      }
    },
    onchang(e) {
      if (e === '1') {
        this.topInfo.createType = '1'
        // this.pageConfig = pageConfig2();
      } else {
        this.topInfo.createType = '2'
        // this.pageConfig = pageConfig(this.$route.query.receiveOrderCode);
      }
    },
    getDetailList(val) {
      let list = []
      val.forEach((item) => {
        let obj = {
          buyerOrgCode: item.buyerOrgCode,
          buyerOrgName: item.buyerOrgName,
          demandDate: item.demandDate || 0,
          itemCode: item.itemCode,
          itemName: item.itemName,
          orderCode: item.orderCode,
          orderItemNo: item.orderItemNo,
          packageMinQuantity: item.packageMinQuantity,
          receiveQuantity: item.receiveQuantity || 0,
          remark: item.remark,
          stockUnit: item.stockUnit || '',
          warehouseCode: item.warehouseCode || '',
          warehouseName: item.warehouseName || ''
        }
        if (item.id) {
          obj.id = item.id
        }
        list.push(obj)
      })
      return list
    },
    async newSave(newRuleForm, type) {
      console.log(newRuleForm)
      let params = {
        buyerCode: newRuleForm.customerCode,
        buyerEnterpriseId: newRuleForm.enterpriseId,
        buyerName: newRuleForm.customerName,
        createType: this.topInfo.createType,
        isOutSale: newRuleForm.isOutSale,
        itemCode: newRuleForm.itemCode,
        itemName: newRuleForm.itemName,
        orderCode: newRuleForm.orderCode,
        orderItemNo: newRuleForm.orderItemNo,
        receiveSiteCode: newRuleForm.pickingSiteCode,
        receiveSiteName: newRuleForm.pickingSiteName,
        remark: newRuleForm.remark,
        siteCode: newRuleForm.siteCode,
        siteName: newRuleForm.siteName,
        warehouseCode: newRuleForm.warehouseCode,
        warehouseName: newRuleForm.warehouseName,

        supplierCode: newRuleForm.supplierCode,
        supplierName: newRuleForm.supplierName,
        buyerOrgCode: newRuleForm.buyerOrgCode,
        buyerOrgName: newRuleForm.buyerOrgName,
        buyerOrgId: '1', //无此字段
        demandStartDate: newRuleForm.requireDate ? newRuleForm.requireDate[0].getTime() : '',
        demandEndDate: newRuleForm.requireDate ? newRuleForm.requireDate[1].getTime() : '',
        orderDetailRequestList: []
      }
      params.id = newRuleForm.id
      let orderDetailRequestList = this.$refs.templateRef2
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      params.buyerTenantId = newRuleForm.tenantId
      if (!orderDetailRequestList.length) {
        this.$toast({
          content: this.$t('请至少添加一条委外领料明细'),
          type: 'warning'
        })
        return
      }
      if (
        params.warehouseCode === '' ||
        params.warehouseCode === undefined ||
        params.warehouseCode === null
      ) {
        this.$toast({
          content: this.$t('库存地点必填'),
          type: 'warning'
        })
        return
      }
      // params.orderDetailRequestList = this.getDetailList(
      //   orderDetailRequestList
      // );
      params.orderDetailRequestList = orderDetailRequestList
      await this.$API.outsourcing.ktOutReceiveOrderSave(params).then((res) => {
        this.newSaveId = res.data
        this.$toast({ content: this.$t('操作成功！'), type: 'success' })
        if (type != '1') {
          this.$router.go(-1)
        }
      })
    },
    async newSubmit(newRuleForm) {
      await this.newSave(newRuleForm, '1')
      this.$API.outsourcing.supplierNewSubmit({ ids: this.newSaveId }).then(() => {
        this.$router.go(-1)
      })
    },
    submit(params) {
      if (params.status === 2) {
        let arr = this.$refs.templateRef23
          .getCurrentUsefulRef()
          .gridRef.ejsRef.getCurrentViewRecords()
        arr.forEach((ele1) => {
          ele1.orderItemNo = params.itemNo
          ele1.orderCode = params.orderCode
        })
        this.orderDetailRequestList = arr
        params.buyerTenantId = params.tenantId
        params.orderDetailRequestList = this.orderDetailRequestList
        params.id = this.id
        console.log('buyerTenantId', params)
        params.orderDetailRequestList.every((item) => {
          console.log(item)
        })
        if (
          params.warehouseCode === '' ||
          params.warehouseCode === undefined ||
          params.warehouseCode === null
        ) {
          this.$toast({
            content: this.$t('库存地点必填'),
            type: 'warning'
          })
          return
        }
        this.$API.outsourcing.addNewPicking(params).then((r) => {
          this.$API.outsourcing.supplierNewSubmit({ ids: r.data }).then(() => {
            this.$toast({ content: this.$t('操作成功！'), type: 'success' })
            this.$router.go(-1)
          })
        })
        return
      }
      if (this.id === '') {
        let arr = this.$refs.templateRef23
          .getCurrentUsefulRef()
          .gridRef.ejsRef.getCurrentViewRecords()
        arr.forEach((ele1) => {
          ele1.orderItemNo = params.itemNo
          ele1.orderCode = params.orderCode
        })
        this.orderDetailRequestList = arr
        params.buyerTenantId = params.tenantId
        params.orderDetailRequestList = this.orderDetailRequestList
        params.id = this.id
        console.log('buyerTenantId', params)
        params.orderDetailRequestList.every((item) => {
          console.log(item)
        })
        if (
          params.warehouseCode === '' ||
          params.warehouseCode === undefined ||
          params.warehouseCode === null
        ) {
          this.$toast({
            content: this.$t('库存地点必填'),
            type: 'warning'
          })
          return
        }
        this.$API.outsourcing.addNewPicking(params).then((r) => {
          this.$API.outsourcing.supplierNewSubmit({ ids: r.data }).then(() => {
            this.$toast({ content: this.$t('操作成功！'), type: 'success' })
            this.$router.go(-1)
          })
        })
      } else {
        this.$API.outsourcing.supplierNewSubmit({ ids: this.id }).then(() => {
          this.$toast({ content: this.$t('操作成功！'), type: 'success' })
          this.$router.go(-1)
        })
      }
    },
    save(params) {
      console.log(params)
      let arr = this.$refs.templateRef23
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      arr.forEach((ele1) => {
        ele1.orderItemNo = params.itemNo
        ele1.orderCode = params.orderCode
      })
      this.orderDetailRequestList = arr
      params.buyerTenantId = params.tenantId
      params.orderDetailRequestList = this.orderDetailRequestList
      params.id = this.id
      console.log('buyerTenantId', params)
      // params.orderDetailRequestList.every((item) => {
      //   console.log(item);
      // });
      // params.orderDetailRequestList.forEach((item) => {
      //   item.buyerGroupName = 50;
      //   item.buyerGroupCode = 50;
      //   item.basicUnitName = 12;
      // });
      if (
        params.warehouseCode === '' ||
        params.warehouseCode === undefined ||
        params.warehouseCode === null
      ) {
        this.$toast({
          content: this.$t('库存地点必填'),
          type: 'warning'
        })
        return
      }
      this.$API.outsourcing.addNewPicking(params).then((r) => {
        this.id = r.data
        this.$toast({ content: this.$t('操作成功！'), type: 'success' })
        this.$router.go(-1)
      })
    },
    getOnePicking() {
      this.$store.commit('startLoading')

      this.$API.outsourcing
        .getOneNewPicking({ receiveOrderCode: this.receiveOrderCode })
        .then((r) => {
          this.$store.commit('endLoading')

          if (r.data.createType == '1' && this.entryType == 'edit') {
            this.topInfo.createType = '1'
            this.entryNewRuleForm = r.data
            let entryList = r.data.outReceiveOrderDetailResponseList || []
            entryList.forEach((item) => {
              item.addId = this.addId++
              if (r.data.isOutSale == 1) {
                item.maxReceiveQuantity = 0
              }
            })
            this.pageConfig2[0].grid.dataSource = entryList
            this.$refs.topInfo.init()

            return
          }
          this.topInfo = cloneDeep(r.data)
          this.$refs.topInfo.init()
          let entryList = r.data.outReceiveOrderDetailResponseList || []
          entryList.forEach((item) => {
            item.addId = this.addId++
            if (r.data.isOutSale == 1) {
              item.maxReceiveQuantity = 0
            }
          })
          this.pageConfig[0].grid.dataSource = entryList

          this.topInfo.siteId = r.data.siteId
          this.topInfo.itemNo = r.data.orderItemNo
          this.topInfo.supplierCode = r.data.supplierCode
          this.topInfo.orderCode = r.data.orderCode
          this.topInfo.isOutSale = String(r.data.isOutSale)
          this.topInfo.remark = r.data.remark
          this.topInfo.itemCode = r.data.itemCode
          this.topInfo.itemName = r.data.itemName
          this.topInfo.customerEnterpriseId = r.data.buyerEnterpriseId
          // this.topInfo.outReceiveOrderDetailResponseList =
          //   r.data.outReceiveOrderDetailResponseList;
          // this.pageConfig[0].grid.dataSource =
          //   this.topInfo.outReceiveOrderDetailResponseList;
          // this.topInfo.siteId = r.data.siteCode;
          this.id = r.data.id
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    deleteRe() {
      // this.$dialog({
      //   data: {
      //     title: this.$t("确认"),
      //     message: this.$t(
      //       "更换公司和采购订单将重新清空/获取领料物料数据，请确定是否继续?"
      //     ),
      //   },
      // success: () => {
      let currentRecords =
        this.$refs.templateRef23?.getCurrentUsefulRef().gridRef?.ejsRef.getCurrentViewRecords() ||
        []
      if (currentRecords.length > 0) {
        let numList = []
        for (let i = 0; i < currentRecords.length; i++) {
          numList.push(i)
        }
        console.log(numList)
        this.$refs.templateRef23.getCurrentUsefulRef().gridRef.ejsRef.selectRows(numList)
        this.$refs.templateRef23.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
      }
      // },
      // });
    },

    selectedChanged(e) {
      this.saveList = e
      console.log(this.saveList)
    },

    handleClickToolBar(e) {
      console.log('handleClickToolBar', e)
      // console.log()
      let codeArr = JSON.parse(sessionStorage.getItem('codeArr'))

      if (e.toolbar.id === 'Add') {
        if (codeArr.supplierCode) {
          if (
            codeArr.isOutSale === undefined ||
            codeArr.isOutSale === null ||
            codeArr.isOutSale === ''
          ) {
            this.$toast({
              content: this.$t('是否销售委外必填'),
              type: 'warning'
            })
            return
          }
          this.$refs.templateRef23.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
          // this.$refs.templateRef23.$refs[
          //   "pageGridRef-0"
          // ][0].$refs.pluginGridRef.$refs.gridRef.ejsInstances.dataSource.push(
          //   {}
          // );

          // this.$refs.templateRef23.getCurrentTabRef()?.grid.refresh();

          // this.pageConfig[0].grid.dataSource.push({});
          this.$refs.topInfo.init()
        }
      } else if (e.toolbar.id === 'Import') {
        if (codeArr.supplierCode) {
          if (
            codeArr.isOutSale === undefined ||
            codeArr.isOutSale === null ||
            codeArr.isOutSale === ''
          ) {
            this.$toast({
              content: this.$t('是否销售委外必填'),
              type: 'warning'
            })
            return
          }
          let obj = {
            buyerEnterpriseId: codeArr.buyerEnterpriseId,
            createType: 2,
            siteCode: codeArr.siteCode,
            buyerOrgCode: codeArr.buyerOrgCode,
            isOutSale: codeArr.isOutSale,
            supplierCode: codeArr.supplierCode
          }
          this.uploadParams = obj
          this.handleImport(true)
        }
      } else if (e.toolbar.id === 'Delete') {
        this.$refs.templateRef23.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
        // let currentRecords = this.$refs.templateRef23
        //   ?.getCurrentUsefulRef()
        //   .gridRef?.ejsRef.getSelectedRecords();
        // console.log(currentRecords);
      }
    },
    handleClickCellTool() {},
    handleImport(flag) {
      //导入
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    handleClickCellTitle(e) {
      console.log('handleClickCellTitle', e)
    },
    upExcelConfirm(e) {
      let codeArr = JSON.parse(sessionStorage.getItem('codeArr'))

      this.handleImport(false)

      e.data.forEach((item) => {
        if (codeArr.isOutSale === '1' || codeArr.isOutSale === 1) {
          item.maxReceiveQuantity = 0
        }
        item.receiveQuantity = item.quantity
        item.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
      })
      // this.$set(this.pageConfig[0].grid, "dataSource", e.data);
      // this.pageConfig[0].grid.dataSource = e.data;
      this.$refs.templateRef23.$refs[
        'pageGridRef-0'
      ].$refs.pluginGridRef.$refs.gridRef.ejsInstances.dataSource.push(...e.data)

      this.$refs.templateRef23.getCurrentTabRef()?.grid.refresh()
      // this.pageConfig[0].grid.dataSource.push(e.data[0]);
      console.log(this.pageConfig[0].grid.dataSource)
    },
    actionBegin(args) {
      console.log('actionBegin', args)
      console.log(this.saveList)
      if (args.requestType === 'sorting') {
        // if (this.allowSubmit == false) {
        args.cancel = true
        // }
      }
      if (args.requestType === 'save') {
        console.log(this.beginData)
        if (args.data.itemCode === null && this.beginData !== null) {
          args.data.itemCode = this.beginData.itemCode
        }
        if (this.saveList !== null) {
          console.log(this.saveList)

          args.data.warehouseCode = this.saveList.itemInfo.warehouseCode
          args.data.buyerGroupName = this.saveList.itemInfo.buyerGroupName
          args.data.buyerGroupCode = this.saveList.itemInfo.buyerGroupCode
          args.data.basicUnitCode = this.saveList.itemInfo.basicUnitCode
          args.data.basicUnitName = this.saveList.itemInfo.basicUnitName

          console.log(args.data)
        }
      }
      if (args.requestType == 'add' || args.requestType == 'beginEdit') {
        this.beginData = args.rowData

        args.data.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
        // if (
        //   Number(args.data.receiveQuantity) >
        //   Number(args.data.maxDemandQuantity)
        // ) {
        //   this.$toast({
        //     content: "本次领料数量不可超过最大需求可创建数量!",
        //     type: "warning",
        //   });
        //   args.data.receiveQuantity = args.data.maxDemandQuantity;
        // }
        this.isEditStatus = true
      }
      if (args.requestType == 'refresh') {
        this.isEditStatus = false
      }
      // let arr = this.$refs.templateRef23
      //   .getCurrentUsefulRef()
      //   .gridRef.ejsRef.getCurrentViewRecords();
      // console.log(arr);
    },
    actionBegin2(args) {
      console.log('actionBegin', args)
      if (args.requestType === 'sorting') {
        // if (this.allowSubmit == false) {
        args.cancel = true
        // }
      }
      if (args.requestType == 'add' || args.requestType == 'beginEdit') {
        args.data.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id

        this.isEditStatus = true
      }
      if (args.requestType === 'save') {
        // if (this.saveList !== null) {
        // if (args.data.maxDemandQuantity > args.data.maxReceiveQuantity) {
        //   if (
        //     Number(args.data.receiveQuantity) >
        //     Number(args.data.maxReceiveQuantity)
        //   ) {
        //     this.$toast({
        //       content: "本次领料数量不可超过创建领料单最大数量!",
        //       type: "warning",
        //     });
        //     args.data.receiveQuantity = args.data.maxReceiveQuantity;
        //   }
        // } else {
        //   if (
        //     Number(args.data.receiveQuantity) >
        //     Number(args.data.maxDemandQuantity)
        //   ) {
        //     this.$toast({
        //       content: "本次领料数量不可超过最大需求可创建数量!",
        //       type: "warning",
        //     });
        //     args.data.receiveQuantity = args.data.maxDemandQuantity;
        //   }
        // }
        // }
      }
    },
    actionComplete(args) {
      //   // const { requestType, action, rowIndex, index } = args;

      console.log(args, '我是actionComplete')
    },
    addRow(row) {
      console.log('addRow', row)
    }
  },
  deactivated() {
    sessionStorage.removeItem('entryData')
    sessionStorage.removeItem('codeArr')
  }
}
</script>
<style lang="scss" scoped>
.repeat-template {
  .common-template-page {
    /deep/ .mt-tabs {
      display: none;
    }
  }
}
.e-content {
  height: 200px;
}

.mt-tabs {
  width: 100%;
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
// /deep/ .top-info {
// margin-top: 20px;
// }
// .bottom-tables {
//   height: 100%;
// }
// .mt-tabs {
//   width: 100%;
//   /deep/.mt-tabs-container {
//     width: 100%;
//   }
// }
// .addNewPicking {
//   background: #fff;
//   display: flex;
//   flex-direction: column;
//   .template-height {
//     height: auto;
//     flex: 1;
//     max-height: calc(100% - 270px);
//     /deep/ .e-gridcontent {
//       overflow-y: auto;
//       height: calc(100% - 44px);
//     }
//     /deep/ .e-gridcontent {
//       // height: 100%;
//       overflow-y: auto;
//       height: calc(100% - 44px);
//     }
//   }
// }
</style>
