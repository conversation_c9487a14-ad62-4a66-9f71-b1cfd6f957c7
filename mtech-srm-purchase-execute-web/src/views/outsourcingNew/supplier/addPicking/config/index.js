import { i18n } from '@/main.js'
import Select from '../components/Select.vue'
import InputView from '../components/InputView.vue'
import InputNumber from '../components/InputNumber.vue'
import Vue from 'vue'
import UTILS from '@/utils/utils'
const todoListToolBar = {
  useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
  tools: [
    // ["Add", "Delete"],
    [
      'add',
      'delete',
      {
        id: 'Import',
        icon: 'icon_solid_Import',
        title: i18n.t('导入')
      }
    ],
    []
  ]
}

export const todoListColumnData = [
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false
  },
  {
    field: 'lineNo',
    headerText: i18n.t('行号'),
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    width: 200,
    allowEditing: true,
    selectOptions: [],
    entryData: {},
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料编号')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'dn', // new
    headerText: i18n.t('交货单号'),
    allowEditing: false
  },
  {
    field: 'dnItem', // new
    headerText: i18n.t('交货单行号'),
    allowEditing: false
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料描述'),
    width: 200,
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料描述')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'planGroupName',
    headerText: i18n.t('计划组'),
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'buyerGroupName',
    headerText: i18n.t('采购组'),
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  // {
  //   field: "warehouseName",
  //   headerText: i18n.t("库存地点"),
  //   width: 300,
  //   allowEditing: false,
  //   // editTemplate: () => {
  //   //   return { template: InputView };
  //   // },
  //   editTemplate: () => {
  //     return { template: Select };
  //   },

  //   template: () => {
  //     return {
  //       template: Vue.component("actionInput", {
  //         template: `<div>{{data.warehouseName}}{{data.warehouseCode}}</div>`,
  //         data: function () {
  //           return { data: {} };
  //         },
  //         mounted() {},
  //         methods: {},
  //       }),
  //     };
  //   },
  // },
  // {
  //   field: "warehouseCode",
  //   headerText: i18n.t("库存地点编号"),
  //   width: 200,
  //   allowEditing: false,
  //   visible: false,
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
  {
    field: 'receiveQuantity',
    headerText: i18n.t('本次领料数量'),
    width: 200,
    editTemplate: () => {
      return { template: InputNumber }
    }
    // validationRules: { required: true },

    // allowEditing: true,
    // editTemplate: () => {
    //   return { template: InputView };
    // },
  },

  {
    field: 'maxReceiveQuantity',
    headerText: i18n.t('创建领料单最大数量'),
    width: 200,
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },

  {
    field: 'orderCode',
    headerText: i18n.t('关联采购订单号'),
    width: 200,
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'orderItemNo',
    headerText: i18n.t('关联采购订单号行号'),
    width: 200,
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'soAndDn',
    headerText: i18n.t('销售凭证&交货单'),
    width: 200,
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  // {
  //   field: "maxReceiveQuantity",
  //   headerText: i18n.t("采购采购订单行号"),
  //   width: 200,
  //   allowEditing: false,
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },

  {
    field: 'maxDemandQuantity',
    headerText: i18n.t('最大需求可创建数量'),
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'roundingOff', // new
    headerText: i18n.t('最小包装量'),
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'stockUnit',
    headerText: i18n.t('库存单位'),
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'basicUnitName',
    headerText: i18n.t('基本单位'), // new
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('行备注')
  },
  {
    width: '150',
    field: 'quantity',
    visible: false
  },
  {
    width: '150',
    field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    headerText: 'addId主键',
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。
    allowEditing: false
  }
]
export const todoListColumnData2 = [
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false
  },
  {
    field: 'lineNo',
    headerText: i18n.t('行号'),
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    width: 200,
    allowEditing: true,
    selectOptions: [],
    entryData: {},
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料描述'),
    width: 200,
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'planGroupName',
    headerText: i18n.t('计划组'),
    allowEditing: false
  },
  {
    field: 'buyerGroupName',
    headerText: i18n.t('采购组'),
    allowEditing: false
  },
  {
    field: 'receiveQuantity',
    headerText: i18n.t('本次领料数量'),
    width: 200,
    editTemplate: () => {
      return { template: InputNumber }
    },
    validationRules: { required: true }

    // allowEditing: true,
    // editTemplate: () => {
    //   return { template: InputView };
    // },
  },
  {
    field: 'orderCode',
    headerText: i18n.t('关联采购订单号'),
    width: 200,
    allowEditing: false
  },
  {
    field: 'orderItemNo',
    headerText: i18n.t('关联采购订单号行号'),
    width: 200,
    allowEditing: false
  },
  {
    field: 'soAndDn',
    headerText: i18n.t('销售凭证&交货单'),
    width: 200,
    allowEditing: false
  },
  // {
  //   field: "warehouseName",
  //   headerText: i18n.t("库存地点"),
  //   width: 200,
  //   allowEditing: false,
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  //   template: () => {
  //     return {
  //       template: Vue.component("actionInput", {
  //         template: `<div>{{data.warehouseName}}{{data.warehouseCode}}</div>`,
  //         data: function () {
  //           return { data: {} };
  //         },
  //         mounted() {},
  //         methods: {},
  //       }),
  //     };
  //   },
  // },
  {
    field: 'warehouseCode',
    headerText: i18n.t('发料仓位编号'),
    width: 200,
    allowEditing: false,
    // visible: false,
    // isIdentity: true,
    // isPrimaryKey: true,
    editTemplate: () => {
      return { template: InputView }
    }
  },

  {
    field: 'roundingOff',
    headerText: i18n.t('最小包装量'),
    width: 200,
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },

  {
    field: 'wmsReceiveQuantity',
    headerText: i18n.t('发料数量'),
    width: 200,
    allowEditing: false
  },
  {
    field: 'wmsReceiveStatus',
    headerText: i18n.t('发料状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('未发货'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('全部发货'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('部分发货'), cssClass: 'col-active' }
      ]
    }
  },
  {
    field: 'maxReceiveQuantity',
    headerText: i18n.t('创建领料单最大数量'),
    width: 200,
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'maxDemandQuantity',
    headerText: i18n.t('最大需求可创建数量'),
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },

  {
    field: 'stockUnit',
    headerText: i18n.t('库存单位'),
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'stockUnit',
    headerText: i18n.t('基本单位'),
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('行备注')
  },
  // {
  //   field: "wmsReceiveStatus",
  //   headerText: i18n.t("发料状态"),
  //   valueConverter: {
  //     type: "map",
  //     map: [
  //       { value: 0, text: i18n.t("未发货"), cssClass: "col-active" },
  //       { value: 1, text: i18n.t("全部发货"), cssClass: "col-active" },
  //       { value: 2, text: i18n.t("部分发货"), cssClass: "col-active" },
  //     ],
  //   },
  // },

  // {
  //   field: "wmsReceiveQuantity",
  //   headerText: i18n.t("实发数量"),
  // },
  {
    width: '150',
    field: 'quantity',
    visible: false
  },
  {
    width: '150',
    field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    headerText: 'addId主键',
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。
    allowEditing: false
  }
]
// const completedListColumnData = [
//   {
//     width: "50",
//     type: "checkbox",
//   },
//   {
//     field: "businessTypeName",
//     headerText: i18n.t("委外物料单号"),
//   },
//   {
//     field: "rfxCode",
//     headerText: i18n.t("状态"),
//   },
//   {
//     field: "skuCode",
//     headerText: i18n.t("行号"),
//   },
//   {
//     field: "skuName",
//     headerText: i18n.t("物料编码"),
//   },
//   {
//     field: "itemCode",
//     headerText: i18n.t("物料描述"),
//   },
//   {
//     field: "itemName",
//     headerText: i18n.t("数量"),
//   },
//   {
//     field: "categoryCode",
//     headerText: i18n.t("需求数量"),
//   },
//   {
//     field: "itemName",
//     headerText: i18n.t("单位"),
//   },
//   {
//     field: "companyName",
//     headerText: i18n.t("工厂编码"),
//   },
//   {
//     field: "purOrgName",
//     headerText: i18n.t("工厂描述"),
//   },
//   {
//     field: "purGroupName",
//     headerText: i18n.t("物料供应商编码"),
//   },
//   {
//     field: "purExecutorName",
//     headerText: i18n.t("领料供应商描述"),
//   },
//   {
//     field: "siteCode",
//     headerText: i18n.t("发料仓位编码"),
//   },
//   {
//     field: "siteName",
//     headerText: i18n.t("发料仓位描述"),
//   },
//   {
//     field: "supplierName",
//     headerText: i18n.t("采购组"),
//   },
// ];

//status  定点推荐主表状态 0待处理 3已处理 4已退回
export const pageConfig = () => [
  {
    title: i18n.t('物料信息1'),
    // useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    useToolTemplate: false, // 不使用预置(新增、编辑、删除)
    useBaseConfig: false, // 不使用组件中的toolbar配置
    toolbar: [],

    grid: {
      allowPaging: false,
      // height: "auto",
      frozenColumns: 0,
      editSettings: {
        allowEditing: true,
        allowAdding: true,
        allowDeleting: true,
        mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
        showConfirmDialog: false
        // showDeleteConfirmDialog: true,
        // newRowPosition: "Top",
      },
      columnData: todoListColumnData,
      dataSource: [],
      asyncConfig: {
        // url: `/srm-purchase-execute/tenant/kt/outReceiveOrder/supplier/getOne?receiveOrderCode=${code}`,
        // serializeList: (list) => {
        //   console.log(list, "list");
        //   if (list.isOutSale === 1) {
        //     list.outReceiveOrderDetailResponseList.forEach((item) => {
        //       item.maxReceiveQuantity = 0;
        //     });
        //     console.log(list, "list");
        //     return list.outReceiveOrderDetailResponseList;
        //   } else {
        //     return list;
        //   }
        // },
        // recordsPosition: `data.outReceiveOrderDetailResponseList`,
        // methods: "get",
      }
    }
  }
]
export const pageConfig2 = () => [
  {
    title: i18n.t('物料信息'),
    // useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    toolbar: todoListToolBar,

    grid: {
      // height: "auto",
      frozenColumns: 0,

      allowPaging: false,
      editSettings: {
        allowEditing: true,
        allowAdding: true,
        allowDeleting: true,
        mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
        showConfirmDialog: false,
        // showDeleteConfirmDialog: true,
        newRowPosition: 'Top'
      },
      columnData: todoListColumnData,
      dataSource: []
    }
  }
]
export const pageConfig3 = () => [
  {
    title: i18n.t('物料信息'),
    // useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'

    toolbar: todoListToolBar,

    grid: {
      allowPaging: false,
      // height: "auto",
      frozenColumns: 0,
      editSettings: {
        allowEditing: true,
        allowAdding: true,
        allowDeleting: true,
        mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
        showConfirmDialog: false
        // showDeleteConfirmDialog: true,
        // newRowPosition: "Top",
      },
      columnData: todoListColumnData,
      dataSource: [],
      asyncConfig: {
        // url: `/srm-purchase-execute/tenant/kt/outReceiveOrder/supplier/getOne?receiveOrderCode=${code}`,
        // serializeList: (list) => {
        //   console.log(list, "list");
        //   if (list.isOutSale === 1) {
        //     list.outReceiveOrderDetailResponseList.forEach((item) => {
        //       item.maxReceiveQuantity = 0;
        //     });
        //     console.log(list, "list");
        //     return list.outReceiveOrderDetailResponseList;
        //   } else {
        //     return list;
        //   }
        // },
        // recordsPosition: `data.outReceiveOrderDetailResponseList`,
        // methods: "get",
      }
    }
  }
]
export const editSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  // showDeleteConfirmDialog: true,
  newRowPosition: 'Bottom'
}
export const todoListColumnData3 = [
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false
  },
  {
    field: 'lineNo',
    headerText: i18n.t('行号'),
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    width: 200,
    allowEditing: false
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料描述'),
    width: 200,
    allowEditing: false
  },
  {
    field: 'planGroupName',
    headerText: i18n.t('计划组'),
    allowEditing: false
  },
  {
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组'),
    width: 200,
    allowEditing: false
  },
  {
    field: 'receiveQuantity',
    headerText: i18n.t('本次领料数量'),
    width: 200,
    editTemplate: () => {
      return { template: InputNumber }
    }
  },
  {
    field: 'maxReceiveQuantity',
    headerText: i18n.t('创建领料单最大数量'),
    width: 200,
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'maxDemandQuantity',
    headerText: i18n.t('最大需求可创建数量'),
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'orderCode',
    headerText: i18n.t('关联采购订单号'),
    width: 200,
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'orderItemNo',
    headerText: i18n.t('关联采购订单号行号'),
    width: 200,
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'soAndDn',
    headerText: i18n.t('销售凭证&交货单'),
    width: 200,
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  // {
  //   field: "stockQuantity",
  //   headerText: i18n.t("可用库存数量"),
  //   width: 200,
  //   allowEditing: false,
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
  {
    field: 'roundingOff',
    headerText: i18n.t('最小包装量'),
    width: 200,
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'basicUnitName',
    headerText: i18n.t('基本单位'),
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'stockUnitCode',
    headerText: i18n.t('库存单位'),
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'warehouseCode',
    headerText: i18n.t('发料仓位编码'),
    allowEditing: false
  },
  {
    field: 'demandDate',
    headerText: i18n.t('需求日期'),
    allowEditing: false,
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    }
  },
  // {
  //   field: "orderCode",
  //   headerText: i18n.t("关联采购订单号"),
  //   allowEditing: false,
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
  // {
  //   field: "orderItemNo",
  //   headerText: i18n.t("关联采购订单行号"),
  //   allowEditing: false,
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
  {
    field: 'remark',
    headerText: i18n.t('行备注')
  },
  {
    width: '150',
    field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    headerText: 'addId主键',
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。
    allowEditing: false
  }
]
