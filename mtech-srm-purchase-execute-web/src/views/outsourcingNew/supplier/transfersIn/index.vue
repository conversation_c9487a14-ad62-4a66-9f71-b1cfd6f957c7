<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @rowSelecting="rowSelecting"
      @cellEdit="cellEdit"
      @rowDeselected="rowDeselecting"
    >
    </mt-template-page>
    <deliver
      @handleAddDialogShow="handleAddDialogShow"
      @resolveClick="resolveClick"
      ref="headerTop"
      v-if="deliveryShow"
      class="flex-keep"
    ></deliver>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { pageConfig, completedListColumnData2 } from './config'
import { cloneDeep } from 'lodash'

export default {
  components: {
    deliver: require('./components/deliver.vue').default
  },
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0119' },
          { dataPermission: 'b', permissionCode: 'T_02_0118' }
        ]
      },
      editData: [],
      pageConfig: pageConfig(this.$route.path),
      deliveryShow: false,
      chooseIds: []
    }
  },
  created() {},
  mounted() {
    if (this.$route.query.from === 'mytodo') {
      // this.currentTab = 1;
      this.pageConfig[0].grid.asyncConfig.defaultRules = JSON.parse(
        sessionStorage.getItem('todoDetail')
      ).defaultRules
      this.pageConfig[0].grid.asyncConfig.defaultRules.push({
        field: 'orderType',
        operator: 'equal',
        value: this.$route.path.includes('gc') ? 1 : 0
      })
    }
  },
  methods: {
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    resolveClick(value) {
      this.materialReject(value)
      this.deliveryShow = false
    },
    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    queryRfxConfig() {},
    //表格按钮-点击事件
    handleClickToolBar(e) {
      if (e.toolbar.id === 'export1') {
        let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let obj = JSON.parse(
          sessionStorage.getItem('c30f3fcc-4634-4ed9-8c07-3d0845fe3598')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          completedListColumnData2.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        let params = {
          page: { current: 1, size: 10000 },
          rules: rule.rules || []
        }
        params.rules = [
          ...params.rules,
          {
            field: 'orderType',
            operator: 'equal',
            value: this.$route.path.includes('gc') ? 1 : 0
          }
        ]
        this.$store.commit('startLoading')
        this.$API.outsourcing.outNewAllocationOrderExport(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
      const { toolbar, gridRef } = e

      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (_selectGridRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _status = _selectGridRecords.map((ele) => {
        return ele.status
      })
      console.log(_status)

      if (_status.includes(2) || _status.includes(3)) {
        this.$toast({
          content: this.$t('该状态不可以确认或退回'),
          type: 'warning'
        })
        return
      }
      if (toolbar.id === 'detailAccept') {
        const _selectedData = gridRef.getMtechGridRecords()
        console.log(_selectedData)
        this.handleClickToolBarSubmit(_selectedData)
      }
      if (toolbar.id === 'detailBack') {
        this.chooseIds = _selectGridRecords.map((ele) => {
          return ele.allocationOrderId
        })
        this.deliveryShow = true
      }
      if (e.toolbar.id == 'create') {
        //议价

        let arr = _selectGridRecords.map((ele) => {
          return ele.id
        })
        console.log('eeee', arr)
        this.materialConfirm(arr)
      } else if (e.toolbar.id == 'back') {
        this.chooseIds = _selectGridRecords.map((ele) => {
          return ele.id
        })
        this.deliveryShow = true
        // this.handleAddDialogShow();
        // this.accept(_selectGridRecords);
      }
    },
    //编辑行内
    cellEdit(e) {
      console.log(e)
      let { status, data } = e
      let flag = false
      if (status) {
        if (this.editData && this.editData.length < 1) {
          this.editData.push(data)
        } else {
          for (let i = 0; i < this.editData.length; i++) {
            if (this.editData[i].id !== data.id) {
              flag = true
              break
            }
          }
          if (flag) {
            let arr = this.editData.map((item) => item.id)
            let indexOf = arr.indexOf(data.id)
            if (indexOf == -1) {
              this.editData.push(data)
            }
          }
        }
      } else {
        if (this.editData && this.editData.length < 1) return
        this.editData.map((item, i) => {
          if (item.id === data.id) {
            this.editData.splice(i, 1)
          }
        })
      }

      console.log(this.editData)
    },
    materialConfirm(e) {
      this.$API.outsourcing.outNewAllocationOrderConfirm({ ids: e.join(',') }).then(() => {
        this.$toast({ content: this.$t('操作成功！'), type: 'success' })
        this.confirmSuccess()
      })
    },
    handleClickToolBarSubmit(selectedData) {
      let _selectedData = cloneDeep(selectedData)
      if (_selectedData.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //没有编辑但是勾选的值
      let newarr = this.editData.map((item) => item.id)
      let incluArr = _selectedData.filter((item) => !newarr.includes(item.id))
      // incluArr.map((item) => {
      //   item.receiveQuantity = item.deliveryQuantity;
      // });
      //编辑的值替换勾选的值
      this.editData.forEach((item) => {
        let index = _selectedData.findIndex((e) => e.id == item.id)
        if (index > -1) {
          _selectedData[index] = item
        }
      })
      //勾选但未编辑的值 ---替换
      incluArr.forEach((item) => {
        let index = _selectedData.findIndex((e) => e.id == item.id)
        if (index > -1) {
          _selectedData[index] = item
        }
      })
      console.log(_selectedData)
      this.doSubmit(_selectedData)
    },
    doSubmit(e) {
      console.log(e)

      let obj = e.map((item) => {
        return {
          id: item.id,
          outOrderId: item.allocationOrderId,
          wmsQuantity: item.wmsAllocationQuantity
        }
      })
      console.log(obj)
      this.$API.outsourcing
        .OrderNewDetailInSupplierConfirmAndUpdateBatch(obj)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.template1.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.template1.refreshCurrentGridData()
        })
    },
    //复选框事件
    rowSelecting(e) {
      if (e?.rowIndexes?.length < 1) return
      if (e.data instanceof Array) return
      // if (e?.data?.status != 2) {
      //   let mapArr = e.rowIndexes.filter((item) => {
      //     return item !== e.rowIndex;
      //   });
      //   if (!(e.data instanceof Array)) {
      //     console.log(e, mapArr);
      //     this.$nextTick(() => {
      //       this.$refs.templateRef
      //         .getCurrentUsefulRef()
      //         .ejsRef.selectRows(mapArr);
      //     });
      //   }
      //   return;
      // }
      //获取当前页所有的行
      let Obj = cloneDeep(this.$refs.template1.getCurrentUsefulRef().ejsRef.getCurrentViewRecords())
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.template1.getCurrentUsefulRef().ejsRef.getSelectedRecords()
      )
      currentSelect.push(e.data)
      currentSelect = [...new Set(currentSelect)]
      if (Obj instanceof Array && Obj.length > 0) {
        let mapArr = []
        for (let i = 0; i < currentSelect.length; i++) {
          for (let j = 0; j < Obj.length; j++) {
            if (currentSelect[i]?.allocationOrderCode === Obj[j]?.allocationOrderCode) {
              mapArr.push(Obj[j])
            }
          }
        }
        mapArr = mapArr.map((item) => item.index)
        if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0 && mapArr.length > 0) {
          this.$refs.template1.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
        }
        console.log(mapArr)
      }
    },
    //取消
    rowDeselecting(e) {
      if (e?.rowIndexes?.length < 1) return
      //获取当前页所有的行
      let Obj = cloneDeep(this.$refs.template1.getCurrentUsefulRef().ejsRef.getCurrentViewRecords())
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.template1
          .getCurrentUsefulRef()
          .ejsRef.getSelectedRecords()
          .map((e) => e.id)
      )
      let mapArr = []
      Obj.filter((item, index) => {
        if (
          item.allocationOrderCode !== e.data.allocationOrderCode &&
          currentSelect.includes(item.id)
        ) {
          console.log(item)
          mapArr.push(index)
        }
      })
      console.log(e, mapArr)
      if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0) {
        this.$refs.template1.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
      }
    },
    materialReject(e) {
      this.$API.outsourcing
        .outNewAllocationOrderReject({
          ids: this.chooseIds.join(','),
          allocationReason: e
        })
        .then(() => {
          this.$toast({ content: this.$t('操作成功！'), type: 'success' })

          this.handleAddDialogShow()
          this.confirmSuccess()
        })
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      if (e.field === 'allocationOrderCode') {
        this.$router.push(
          `new-supplier-transfersIn-Detail?allocationOrderCode=${e.data.allocationOrderCode}`
        )
      }
    }
  }
}
</script>
<style lang="scss">
.form-design {
  padding-left: 10px;
  background: #fff;
}
.e-dlg-container > .right-wrapper {
  height: 100% !important;
  max-height: 100% !important;
  width: 95% !important;
  .dialog-panel {
    .panel-title {
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(41, 41, 41, 1);
      margin-bottom: 18px;
      &.price-title {
        margin-bottom: 0;
      }
    }
    .full-width {
      width: 100% !important;
    }
  }
}
</style>
<style lang="scss" scoped>
.full-height {
  /deep/.mt-tabs {
    background: #fafafa;
  }
  /deep/ .tab-container {
    background: #fafafa !important;
  }
}
</style>
