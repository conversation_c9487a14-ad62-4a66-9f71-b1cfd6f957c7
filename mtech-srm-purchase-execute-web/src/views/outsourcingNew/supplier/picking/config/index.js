import { i18n } from '@/main.js'
import { timeNumberToDate } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName } from '@/utils/utils'
import Vue from 'vue'
const todoListToolBar = [
  {
    id: 'create',
    icon: 'icon_solid_edit',
    title: i18n.t('创建委外领料单')
    // permission: ["O_02_0681"],
  },
  {
    id: 'print',
    icon: 'icon_table_print',
    title: i18n.t('打印领料单')
    // permission: ["O_02_1046"],
  },
  {
    id: 'submit',
    icon: 'icon_table_accept1',
    title: i18n.t('提交')
    // permission: ["O_02_1046"],
  }
]
const todoListToolBarDetail = [
  {
    id: 'export1',
    icon: 'icon_solid_Createorder',
    title: i18n.t('导出')
  }
  // {
  //   id: "create",
  //   icon: "icon_solid_edit",
  //   title: i18n.t("创建委外领料单"),
  //   permission: ["O_02_0681"],
  // },
  // {
  //   id: "print",
  //   icon: "icon_table_print",
  //   title: i18n.t("打印领料单"),
  //   permission: ["O_02_1046"],
  // },
]

const todoListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'receiveOrderCode',
    headerText: i18n.t('委外领料单号'),
    width: '155',
    cellTools: [
      // {
      //   id: "edit",
      //   icon: "icon_list_edit",
      //   title: i18n.t("编辑"),
      // },
      // {
      //   id: "delete",
      //   icon: "icon_Delete",
      //   title: i18n.t("删除"),
      // },
    ]
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '135',
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('采购退回'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('已确认'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('已完结'), cssClass: 'col-active' },
        { value: 5, text: i18n.t('已取消'), cssClass: 'col-active' }
      ]
    },
    searchOptions: {
      elementType: 'multi-select',
      operator: 'in',
      multiple: true,
      dataSource: [
        { label: i18n.t('新建'), value: 0 },
        { label: i18n.t('待确认'), value: 1 },
        { label: i18n.t('采购退回'), value: 2 },
        { label: i18n.t('已确认'), value: 3 },
        { label: i18n.t('已完结'), value: 4 },
        { label: i18n.t('已取消'), value: 5 }
      ],
      fields: { text: 'label', value: 'value' }
    },
    cellTools: [
      {
        id: 'push',
        title: i18n.t('提交'),
        visibleCondition: (data) => {
          return data['status'] == 0 || data['status'] == 2
        }
        // permission: ["O_02_1282"],
      },
      {
        id: 'delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => data['status'] == 0
        // permission: ["O_02_1282"],
      },
      {
        id: 'edit',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['status'] == 0 || data['status'] == 2
        }
        // permission: ["O_02_1282"],
      },
      {
        id: 'cancel',
        title: i18n.t('取消'),
        visibleCondition: (data) => data['status'] == 2
        // permission: ["O_02_1284"],
      },
      // {
      //   id: "cancel",
      //   title: i18n.t("取消"),
      //   visibleCondition: (data) => data["status"] == 3,
      //   // permission: ["O_02_1284"],
      // },
      {
        id: 'cancel',
        title: i18n.t('取消'),
        visibleCondition: (data) => data['status'] == 1
        // permission: ["O_02_1284"],
      }
    ]
  },
  {
    width: '260',
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    allowEditing: false,
    searchOptions: MasterDataSelect.factorySupplierAddress,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.siteCode, data?.siteName)
    }
  },
  {
    width: '0',
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    ignore: true
  },
  {
    width: '260',
    field: 'buyerOrgCode',
    headerText: i18n.t('公司'),
    allowEditing: false,
    searchOptions: MasterDataSelect.companySupplier,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.buyerOrgCode, data?.buyerOrgName)
    }
  },
  {
    width: '0',
    field: 'buyerOrgName',
    headerText: i18n.t('公司名称'),
    ignore: true
  },
  {
    width: '110',
    field: 'buyerApproveUserName',
    headerText: i18n.t('采方确认人')
  },
  {
    width: '125',
    field: 'buyerApproveDate',
    headerText: i18n.t('采方确认时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    width: '110',
    field: 'buyerCancelUserName',
    headerText: i18n.t('取消人')
  },
  {
    width: '125',
    field: 'buyerCancelDate',
    headerText: i18n.t('取消时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    width: '95',
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: '0', text: i18n.t('未同步'), cssClass: '' },
        { value: '1', text: i18n.t('同步成功'), cssClass: '' },
        { value: '2', text: i18n.t('同步失败'), cssClass: '' },
        { value: '3', text: i18n.t('同步中'), cssClass: '' }
      ]
    }
  },
  {
    field: 'syncMsg',
    headerText: i18n.t('同步消息')
  },
  {
    width: '260',
    field: 'supplierCode',
    headerText: i18n.t('领料供应商'),
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  },
  {
    width: '0',
    field: 'supplierName',
    headerText: i18n.t('领料供应商名称'),
    ignore: true
  },
  {
    field: 'createTime',
    headerText: i18n.t('制单日期'),
    width: '150'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('制单人'),
    width: '90'
  }
]

export const completedListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'receiveOrderCode',
    headerText: i18n.t('委外领料单号'),
    width: '155',
    cellTools: [
      // {
      //   id: "edit",
      //   icon: "icon_list_edit",
      //   title: i18n.t("编辑"),
      // },
      // {
      //   id: "delete",
      //   icon: "icon_Delete",
      //   title: i18n.t("删除"),
      // },
    ]
  },
  {
    width: '80',
    field: 'receiveOrderStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-inactive' },
        { value: 2, text: i18n.t('退回'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('已确认'), cssClass: 'col-inactive' },
        { value: 4, text: i18n.t('已完结'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('已取消'), cssClass: 'col-active' }
      ]
    },
    searchOptions: {
      elementType: 'multi-select',
      operator: 'in',
      multiple: true,
      dataSource: [
        { label: i18n.t('新建'), value: 0 },
        { label: i18n.t('待确认'), value: 1 },
        { label: i18n.t('采购退回'), value: 2 },
        { label: i18n.t('已确认'), value: 3 },
        { label: i18n.t('已完结'), value: 4 },
        { label: i18n.t('已取消'), value: 5 }
      ],
      fields: { text: 'label', value: 'value' }
    },
    cellTools: [
      // {
      //   id: "download",
      //   icon: "icon_solid_Download",
      //   title: i18n.t("下载"),
      //   visibleCondition: () => btnRequired.hasDownload,
      // },
      // {
      //   id: "delete",
      //   icon: "icon_solid_Delete",
      //   title: i18n.t("删除"),
      //   visibleCondition: () => btnRequired.hasDelete && !isView,
      // },
    ]
  },
  {
    width: '260',
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    allowEditing: false,
    searchOptions: MasterDataSelect.factorySupplierAddress,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.siteCode, data?.siteName)
    }
  },
  {
    width: '0',
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    ignore: true
  },
  {
    width: '260',
    field: 'buyerOrgCode',
    headerText: i18n.t('公司'),
    allowEditing: false,
    searchOptions: MasterDataSelect.companySupplier,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.buyerOrgCode, data?.buyerOrgName)
    }
  },
  {
    width: '0',
    field: 'buyerOrgName',
    headerText: i18n.t('公司名称'),
    ignore: true
  },
  {
    width: '70',
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '260',
    field: 'itemCode', //supplierCode
    headerText: i18n.t('物料'),
    searchOptions: MasterDataSelect.itemSupplier,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.itemCode, data?.itemName)
    }
  },
  {
    width: '0',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    ignore: true
  },
  {
    field: 'soAndDn',
    headerText: i18n.t('销售凭证&交货单'),
    width: '200'
  },
  {
    field: 'receiveQuantity',
    headerText: i18n.t('本次领料数量'),
    width: '125'
  },
  {
    field: 'wmsReceiveQuantity',
    headerText: i18n.t('实发数量'),
    width: '100'
  },
  {
    width: '65',
    field: 'basicUnitName',
    headerText: i18n.t('单位'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.basicUnitCode}}-{{data.basicUnitName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  // {
  //   field: "siteCode",
  //   headerText: i18n.t("工厂编码"),
  //   width: 200,
  //   template: () => {
  //     return {
  //       template: Vue.component("actionInput", {
  //         template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
  //         data: function () {
  //           return { data: {} };
  //         },
  //         mounted() {},
  //         methods: {},
  //       }),
  //     };
  //   },
  // },

  {
    width: '260',
    field: 'supplierCode',
    headerText: i18n.t('领料供应商'),
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  },
  {
    width: '0',
    field: 'supplierName',
    headerText: i18n.t('领料供应商名称'),
    ignore: true
  },
  {
    width: '260',
    field: 'warehouseCode',
    headerText: i18n.t('库存地点'),
    searchOptions: MasterDataSelect.stockSupplierAddressName,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.warehouseCode, data?.warehouseName)
    }
  },
  {
    width: '0',
    field: 'warehouseName',
    headerText: i18n.t('库存地点名称'),
    ignore: true
  },
  {
    field: 'createTime',
    headerText: i18n.t('制单日期'),
    width: '145'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('制单人'),
    width: '90'
  }
]

//status  定点推荐主表状态 0待处理 3已处理 4已退回
export const pageConfig = () => [
  {
    title: i18n.t('头视图'),
    // dataPermission: "a",
    // permissionCode: "T_02_0134",
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    useCombinationSelection: false,
    toolbar: todoListToolBar,
    // gridId: Vue.prototype.$tableUUID.outsourcing.piking.list,

    grid: {
      frozenColumns: 1,
      columnData: todoListColumnData,
      asyncConfig: {
        url: '/srm-purchase-execute/tenant/kt/outReceiveOrder/supplier/queryView',
        recordsPosition: 'data.records'
        // defaultRules: [
        //   {
        //     condition: "and",
        //     field: "rfx_item.rfxHeaderId",
        //     operator: "equal",
        //     rfx_bidding_item: "",
        //     rfx_item: "",
        //     value,
        //   },
        // ],
      }
    }
  },
  {
    title: i18n.t('明细视图'),
    // dataPermission: "b",
    // permissionCode: "T_02_0135",
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    useCombinationSelection: false,
    toolbar: todoListToolBarDetail,
    gridId: '26e634ed-5f02-4197-b32f-a1db02fd582b',

    grid: {
      columnData: completedListColumnData,
      frozenColumns: 1,

      asyncConfig: {
        url: '/srm-purchase-execute/tenant/kt/outReceiveOrder/supplier/queryDetailView',
        recordsPosition: 'data.records'
        // defaultRules: [
        //   {
        //     condition: "and",
        //     field: "rfx_item.rfxHeaderId",
        //     operator: "equal",
        //     value,
        //   },
        // ],
      }
    }
  }
]
