<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
    <!-- :permission-obj="permissionObj" -->

    <deliver
      @handleAddDialogShow="handleAddDialogShow"
      @resolveClick="resolveClick"
      ref="headerTop"
      v-if="deliveryShow"
      class="flex-keep"
    ></deliver>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { pageConfig, completedListColumnData } from './config'
export default {
  components: {
    deliver: require('./components/deliver.vue').default
  },
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      pageConfig: pageConfig(),
      deliveryShow: false,
      chooseIds: []
    }
  },
  created() {},
  mounted() {},
  methods: {
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    resolveClick(value) {
      this.materialReject(value)
      this.deliveryShow = false
    },
    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    queryRfxConfig() {},
    //表格按钮-点击事件
    handleClickToolBar(e) {
      if (e.toolbar.id == 'create') {
        this.$router.push('new-supplier-add-picking')
        return
      }
      if (e.toolbar.id === 'export1') {
        let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let obj = JSON.parse(
          sessionStorage.getItem('26e634ed-5f02-4197-b32f-a1db02fd582b')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          completedListColumnData.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        let params = {
          page: { current: 1, size: 10000 },
          rules: rule.rules || []
        }
        this.$store.commit('startLoading')
        this.$API.outsourcing.supplierNewExport(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
      const selectRows = e.gridRef.getMtechGridRecords()

      if (selectRows.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let obj = []
      let _ids = []
      let _status = []

      selectRows.forEach((item) => {
        obj.push(item.receiveOrderCode)
        _ids.push(item.id)
        _status.push(item.status)
      })
      if (e.toolbar.id === 'print') {
        for (let i of _status) {
          if (i !== 3 && i !== 4) {
            console.log(_status)
            this.$toast({
              content: this.$t('只有状态在采方确认才支持打印'),
              type: 'warning'
            })
            return
          }
        }
        this.$API.outsourcing.outNewReceiveOrderSupplierPrint(obj).then((res) => {
          if (res?.data?.type === 'application/json') {
            //   const reader = new FileReader();
            //   reader.readAsText(res?.data, "utf-8");
            //   reader.onload = function () {
            //     console.log("======", reader);
            //     const readerRes = reader.result;
            //     const resObj = JSON.parse(readerRes);
            //     Vue.prototype.$toast({
            //       content: resObj.msg,
            //       type: "error",
            //     });
            //   };
            //   return;
          }
          const content = res.data
          let pdfUrl = window.URL.createObjectURL(
            new Blob([content], { type: 'text/html;charset=utf-8' })
          )

          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
      }
      if (e.toolbar.id === 'submit') {
        this.$API.outsourcing.supplierNewSubmit({ ids: _ids.toString() }).then(() => {
          this.$toast({ content: this.$t('操作成功！'), type: 'success' })

          this.confirmSuccess()
        })
      }
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    supplierSubmit(e) {
      this.$API.outsourcing.supplierNewSubmit({ ids: e }).then(() => {
        this.confirmSuccess()
      })
    },
    supplierCancel(e) {
      this.$API.outsourcing.supplierNewCancel({ ids: e }).then(() => {
        this.$toast({ content: this.$t('操作成功！'), type: 'success' })

        this.confirmSuccess()
      })
    },
    supplierDelete(e) {
      this.$API.outsourcing.supplierNewDelete({ ids: e }).then(() => {
        this.$toast({ content: this.$t('操作成功！'), type: 'success' })

        this.confirmSuccess()
      })
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'push') {
        this.supplierSubmit(e.data.id)
      } else if (e.tool.id == 'cancel') {
        this.supplierCancel(e.data.id)
      } else if (e.tool.id == 'delete') {
        this.supplierDelete(e.data.id)
      } else if (e.tool.id == 'edit') {
        this.$router.push({
          path: 'new-supplier-add-picking',
          query: { receiveOrderCode: e.data.receiveOrderCode, type: 'edit' }
        })
      }
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
      this.$router.push({
        path: 'new-supplier-add-picking',
        query: { receiveOrderCode: e.data.receiveOrderCode, type: 'readonly' }
      })
    }
  }
}
</script>
<style lang="scss">
.form-design {
  padding-left: 10px;
  background: #fff;
}
.e-dlg-container > .right-wrapper {
  height: 100% !important;
  max-height: 100% !important;
  width: 95% !important;
  .dialog-panel {
    .panel-title {
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(41, 41, 41, 1);
      margin-bottom: 18px;
      &.price-title {
        margin-bottom: 0;
      }
    }
    .full-width {
      width: 100% !important;
    }
  }
}
</style>
<style lang="scss" scoped>
.full-height {
  /deep/.mt-tabs {
    background: #fafafa;
  }
  /deep/ .tab-container {
    background: #fafafa !important;
  }
}
</style>
