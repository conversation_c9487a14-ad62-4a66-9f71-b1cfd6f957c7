import { i18n } from '@/main.js'
import Vue from 'vue'
export const columnData = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },
  // {
  //   field: "stockUnitCode",
  //   headerText: i18n.t("库存单位"),
  // },
  {
    field: 'basicUnitCode',
    headerText: i18n.t('基本单位'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.basicUnitCode}}-{{data.basicUnitName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'warehouseCode', // new
    headerText: i18n.t('库存地点'),
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.warehouseName}}{{data.warehouseCode}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerGroupName', // new
    headerText: i18n.t('采购组'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'wmsCancelQuantity',
    headerText: i18n.t('实退数量'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div><mt-input-number precision="3" :min="0" v-if='data.status === 1 ' v-model="data.wmsCancelQuantity"  cssClass="e-outline" :show-clear-button="false" @blur="handleChange"></mt-input-number>
          <div v-else>{{data.wmsCancelQuantity}}</div>
          </div> `,
          data: function () {
            return {
              data: {}
            }
          },
          mounted() {
            // alert(this.data.receiveQuantity);
          },
          methods: {
            handleChange() {
              console.log(this.data)
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'wmsCancelQuantity',
                value: this.data
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'planGroupName', // new
    headerText: i18n.t('计划组'),
    allowEditing: false
  },
  {
    field: 'batch',
    headerText: '批次/卷号'
  },
  {
    field: 'dn', // new
    headerText: i18n.t('交货单号'),
    allowEditing: false
  },
  {
    field: 'dnItem', // new
    headerText: i18n.t('交货单行号'),
    allowEditing: false
  },
  {
    field: 'cancelQuantity',
    headerText: i18n.t('退货数量')
  },
  {
    // width: "150",
    field: 'maxCancelQuantity',
    headerText: i18n.t('可退货数量')
  },

  {
    width: '150',
    field: 'stockQuantity',
    headerText: i18n.t('库存现有量')
  },

  // {
  //   width: "150",
  //   field: "buyerOrgName",
  //   headerText: i18n.t("采购组"),
  // },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('行备注')
  }
]
