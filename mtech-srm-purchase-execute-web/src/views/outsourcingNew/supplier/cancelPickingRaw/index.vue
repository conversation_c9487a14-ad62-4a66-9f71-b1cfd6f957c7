<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @rowDeselected="rowDeselecting"
      @rowSelecting="rowSelecting"
      @cellEdit="cellEdit"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
    <deliver
      @handleAddDialogShow="handleAddDialogShow"
      @resolveClick="resolveClick"
      ref="headerTop"
      v-if="deliveryShow"
      class="flex-keep"
    ></deliver>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { pageConfig, completedListColumnData2 } from './config'
import { cloneDeep } from 'lodash'

export default {
  components: {
    deliver: require('./components/deliver.vue').default
  },
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      editData: [],
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0058' },
          { dataPermission: 'b', permissionCode: 'T_02_0059' }
        ]
      },
      pageConfig: [],
      deliveryShow: false,
      chooseIds: []
    }
  },
  created() {},
  mounted() {
    if (this.$route.query.from === 'mytodo') {
      this.pageConfig = pageConfig(this.$route.path)
      let obj = cloneDeep(pageConfig(this.$route.path))
      obj[0].grid.asyncConfig.defaultRules = JSON.parse(
        sessionStorage.getItem('todoDetail')
      ).defaultRules
      obj[0].grid.asyncConfig.defaultRules.push({
        field: 'orderType',
        operator: 'equal',
        value: this.$route.path.includes('gc') ? 1 : 0
      })
      obj[0].grid.asyncConfig.defaultRules.push({
        condition: 'and',
        rules: [
          {
            condition: 'or',
            field: 'status',
            type: 'string',
            operator: 'contains',
            value: 1
          }
        ]
      })
      this.pageConfig = obj
    } else {
      this.pageConfig = pageConfig(this.$route.path)
    }
  },
  methods: {
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    resolveClick(value) {
      this.materialReject(value)
      this.deliveryShow = false
    },
    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    queryRfxConfig() {},
    //表格按钮-点击事件
    handleClickToolBar(e) {
      if (e.toolbar.id === 'export1') {
        let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let obj = JSON.parse(
          sessionStorage.getItem('6298d626-ad01-45fe-9c61-27e1def38ea4')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          completedListColumnData2.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        let params = {
          page: { current: 1, size: 10000 },
          rules: rule.rules || []
        }
        params.rules = [
          ...params.rules,
          {
            field: 'orderType',
            operator: 'equal',
            value: this.$route.path.includes('gc') ? 1 : 0
          }
        ]
        this.$store.commit('startLoading')
        this.$API.outsourcing.materialNewDetailSupplierNewExport(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
      const { toolbar, gridRef } = e

      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (_selectGridRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _status = _selectGridRecords.map((ele) => {
        return ele.status
      })
      console.log(_status)

      if (_status.includes(2) || _status.includes(3)) {
        this.$toast({
          content: this.$t('该状态不可以确认或退回'),
          type: 'warning'
        })
        return
      }
      if (e.toolbar.id == 'create') {
        //议价

        let arr = _selectGridRecords.map((ele) => {
          return ele.id
        })
        console.log('eeee', arr)
        this.materialConfirm(arr)
      } else if (e.toolbar.id == 'back') {
        this.chooseIds = _selectGridRecords.map((ele) => {
          return ele.id
        })
        this.deliveryShow = true
        // this.handleAddDialogShow();
        // this.accept(_selectGridRecords);
      }
      if (toolbar.id === 'detailAccept') {
        const _selectedData = gridRef.getMtechGridRecords()
        console.log(_selectedData)
        this.handleClickToolBarSubmit(_selectedData)
      }
      if (toolbar.id === 'detailBack') {
        this.chooseIds = _selectGridRecords.map((ele) => {
          return ele.cancelOrderId
        })
        this.deliveryShow = true
      }
    },
    handleClickToolBarSubmit(selectedData) {
      let _selectedData = cloneDeep(selectedData)
      if (_selectedData.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //没有编辑但是勾选的值
      let newarr = this.editData.map((item) => item.id)
      let incluArr = _selectedData.filter((item) => !newarr.includes(item.id))
      // incluArr.map((item) => {
      //   item.receiveQuantity = item.deliveryQuantity;
      // });
      //编辑的值替换勾选的值
      this.editData.forEach((item) => {
        let index = _selectedData.findIndex((e) => e.id == item.id)
        if (index > -1) {
          _selectedData[index] = item
        }
      })
      //勾选但未编辑的值 ---替换
      incluArr.forEach((item) => {
        let index = _selectedData.findIndex((e) => e.id == item.id)
        if (index > -1) {
          _selectedData[index] = item
        }
      })
      console.log(_selectedData)
      this.doSubmit(_selectedData)
    },
    doSubmit(e) {
      console.log(e)

      let obj = e.map((item) => {
        return {
          id: item.id,
          outOrderId: item.cancelOrderId,
          wmsQuantity: item.wmsCancelQuantity
        }
      })
      console.log(obj)
      this.$API.outsourcing
        .materialNewDetailBuyerConfirm(obj)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.template1.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.template1.refreshCurrentGridData()
        })
    },
    //复选框事件
    rowSelecting(e) {
      if (e?.rowIndexes?.length < 1) return
      if (e.data instanceof Array) return
      // if (e?.data?.status != 2) {
      //   let mapArr = e.rowIndexes.filter((item) => {
      //     return item !== e.rowIndex;
      //   });
      //   if (!(e.data instanceof Array)) {
      //     console.log(e, mapArr);
      //     this.$nextTick(() => {
      //       this.$refs.templateRef
      //         .getCurrentUsefulRef()
      //         .ejsRef.selectRows(mapArr);
      //     });
      //   }
      //   return;
      // }
      //获取当前页所有的行
      let Obj = cloneDeep(this.$refs.template1.getCurrentUsefulRef().ejsRef.getCurrentViewRecords())
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.template1.getCurrentUsefulRef().ejsRef.getSelectedRecords()
      )
      currentSelect.push(e.data)
      currentSelect = [...new Set(currentSelect)]
      if (Obj instanceof Array && Obj.length > 0) {
        let mapArr = []
        for (let i = 0; i < currentSelect.length; i++) {
          for (let j = 0; j < Obj.length; j++) {
            if (currentSelect[i]?.cancelOrderCode === Obj[j]?.cancelOrderCode) {
              mapArr.push(Obj[j])
            }
          }
        }
        mapArr = mapArr.map((item) => item.index)
        if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0 && mapArr.length > 0) {
          this.$refs.template1.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
        }
        console.log(mapArr)
      }
    },
    //取消
    rowDeselecting(e) {
      if (e?.rowIndexes?.length < 1) return
      //获取当前页所有的行
      let Obj = cloneDeep(this.$refs.template1.getCurrentUsefulRef().ejsRef.getCurrentViewRecords())
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.template1
          .getCurrentUsefulRef()
          .ejsRef.getSelectedRecords()
          .map((e) => e.id)
      )
      let mapArr = []
      Obj.filter((item, index) => {
        if (item.cancelOrderCode !== e.data.cancelOrderCode && currentSelect.includes(item.id)) {
          console.log(item)
          mapArr.push(index)
        }
      })
      console.log(e, mapArr)
      if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0) {
        this.$refs.template1.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
      }
    },
    //编辑行内
    cellEdit(e) {
      console.log(e)
      let { status, data } = e
      let flag = false
      if (status) {
        if (this.editData && this.editData.length < 1) {
          this.editData.push(data)
        } else {
          for (let i = 0; i < this.editData.length; i++) {
            if (this.editData[i].id !== data.id) {
              flag = true
              break
            }
          }
          if (flag) {
            let arr = this.editData.map((item) => item.id)
            let indexOf = arr.indexOf(data.id)
            if (indexOf == -1) {
              this.editData.push(data)
            }
          }
          // this.editData.map((item) => {
          //   if (type === "text" && item.id === data.id) {
          //     item.rejectReason = data.rejectReason;
          //   } else if (type === "number" && item.id === data.id) {
          //     item.rejectQuantity = data.rejectQuantity;
          //     item.receiveQuantity = data.receiveQuantity;
          //   }
          // });
        }
      } else {
        if (this.editData && this.editData.length < 1) return
        this.editData.map((item, i) => {
          if (item.id === data.id) {
            this.editData.splice(i, 1)
          }
        })
      }

      console.log(this.editData)
    },
    materialConfirm(e) {
      this.$API.outsourcing.materialNewConfirm({ ids: e.join(',') }).then(() => {
        this.$toast({ content: this.$t('操作成功！'), type: 'success' })
        this.confirmSuccess()
      })
    },
    materialReject(e) {
      this.$API.outsourcing
        .materialNewReject({ ids: this.chooseIds.join(','), rejectReason: e })
        .then(() => {
          this.$toast({ content: this.$t('操作成功！'), type: 'success' })

          this.handleAddDialogShow()
          this.confirmSuccess()
        })
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
      console.log(e.data)
      if (e.field === 'cancelOrderCode') {
        localStorage.setItem('purchaseListScope', JSON.stringify(e.data))
        this.$router.push(
          `new-supplier-cancel-pickingDetail-raw?cancelOrderCode=${e.data.cancelOrderCode}`
        )
      }
    }
  }
}
</script>
<style lang="scss">
.form-design {
  padding-left: 10px;
  background: #fff;
}
.e-dlg-container > .right-wrapper {
  height: 100% !important;
  max-height: 100% !important;
  width: 95% !important;
  .dialog-panel {
    .panel-title {
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(41, 41, 41, 1);
      margin-bottom: 18px;
      &.price-title {
        margin-bottom: 0;
      }
    }
    .full-width {
      width: 100% !important;
    }
  }
}
</style>
<style lang="scss" scoped>
.full-height {
  /deep/.mt-tabs {
    background: #fafafa;
  }
  /deep/ .tab-container {
    background: #fafafa !important;
  }
}
</style>
