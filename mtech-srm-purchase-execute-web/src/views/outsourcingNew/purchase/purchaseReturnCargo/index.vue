<template>
  <div class="full-height pt20">
    <mt-template-page
      :current-tab="currentTabIndex"
      :template-config="pageConfig"
      @handleSelectTab="handleSelectTab"
    >
      <mt-template-page
        slot="slot-0"
        ref="templateRef"
        :template-config="pageConfig1"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
        @handleClickCellTool="handleClickCellTool"
      >
      </mt-template-page>

      <mt-template-page
        slot="slot-1"
        @rowSelecting="rowSelecting"
        @handleClickToolBar="handleClickToolBar2"
        @rowDeselected="rowDeselecting"
        @cellEdit="cellEdit"
        ref="template1"
        :template-config="pageConfig2"
      >
      </mt-template-page>
      <!-- 11.23新增 -->
      <mt-template-page
        slot="slot-2"
        ref="template2"
        @handleClickToolBar="handleClickToolBar3"
        @rowSelecting="rowSelecting2"
        @rowDeselected="rowDeselecting2"
        :template-config="pageConfig4"
      >
      </mt-template-page>

      <mt-template-page
        slot="slot-3"
        ref="template3"
        @handleClickToolBar="handleClickToolBar4"
        :template-config="pageConfig3"
      >
      </mt-template-page>
    </mt-template-page>
    <deliver
      @handleAddDialogShow="handleAddDialogShow"
      @resolveClick="resolveClick"
      ref="headerTop"
      v-if="deliveryShow"
      class="flex-keep"
    ></deliver>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { BASE_TENANT } from '@/utils/constant'
import { columnData, columnData2, columnData3, columnData4 } from './config/index.js'
// import deliveryDialog from "./components/deliveryDialog";
import { cloneDeep } from 'lodash'

export default {
  mounted() {
    if (this.$route.path.includes('gc')) {
      this.gcType = true
    }
  },
  components: {
    deliver: require('./components/deliver.vue').default
  },
  data() {
    let currentTabIndex = 0
    if (this.$route.query.from == 'mytodo1') {
      currentTabIndex = 1
    } else if (this.$route.query.from == 'mytodo2') {
      currentTabIndex = 2
    }
    return {
      id: [],
      editData: [],

      deliveryShow: false,
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0146' },
          { dataPermission: 'b', permissionCode: 'T_02_0147' }
        ]
      },
      pageConfig: [
        {
          title: this.$t('头视图')
          // dataPermission: "a",
          // permissionCode: "T_02_0146",
        },
        {
          title: this.$t('直退审核')
          // dataPermission: "b",
          // permissionCode: "T_02_0147",
        },
        {
          title: this.$t('退本厂审核')
          // dataPermission: "b",
          // permissionCode: "T_02_0147",
        },
        {
          title: this.$t('明细视图')
          // dataPermission: "b",
          // permissionCode: "T_02_0147",
        }
      ],
      userInfo: null,
      addDialogShow: false,
      currentTabIndex,
      pageConfig1: [
        {
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                // {
                //   id: "accept1",
                //   icon: "icon_table_accept1",
                //   title: this.$t("确认"),
                //   // permission: ["O_02_0122"],
                // },
                // {
                //   id: "recall",
                //   icon: "icon_table_recall",
                //   title: this.$t("退回"),
                //   // permission: ["O_02_0122"],
                // },
                {
                  id: 'print',
                  icon: 'icon_table_print',
                  title: this.$t('打印')
                  // permission: ["O_02_1046"],
                },
                {
                  id: 'restart',
                  icon: 'icon_table_restart',
                  title: this.$t('手工同步')
                  // permission: ["O_02_1298"],
                },
                {
                  id: 'accept1',
                  icon: 'icon_table_accept1',
                  title: this.$t('确认')
                  // permission: ["O_02_1297"],
                },
                {
                  id: 'recall',
                  icon: 'icon_table_recall',
                  title: this.$t('退回')
                  // permission: ["O_02_1297"],
                },
                {
                  id: 'createReturn',
                  icon: 'icon_table_recall',
                  title: this.$t('创建委外退货单')
                  // permission: ["O_02_0122"],
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          // gridId: this.$tableUUID.outsourcing.purchaseReturnCargo.list,

          grid: {
            columnData: columnData,
            lineIndex: 1,
            frozenColumns: 1,

            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/kt/outCancelOrder/buyer/queryView`,
              rules:
                this.$route.query.from === 'mytodo6'
                  ? JSON.parse(sessionStorage.getItem('todoDetail')).rules
                  : [
                      {
                        field: 'orderType',
                        operator: 'equal',
                        value: this.$route.path.includes('gc') ? 1 : 0
                      }
                    ],
              defaultRules:
                this.$route.query.from === 'mytodo6'
                  ? JSON.parse(sessionStorage.getItem('todoDetail')).defaultRules
                  : [
                      {
                        field: 'orderType',
                        operator: 'equal',
                        value: this.$route.path.includes('gc') ? 1 : 0
                      }
                    ]
            }
            // frozenColumns: 1,
          }
        }
      ],
      pageConfig2: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'detailAccept',
                  icon: 'icon_table_accept1',
                  title: this.$t('确认')
                  // permission: ["O_02_1295"],
                },
                {
                  id: 'recall',
                  icon: 'icon_table_recall',
                  title: this.$t('退回')
                  // permission: ["O_02_1297"],
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          // gridId: this.$tableUUID.outsourcing.purchaseReturnCargo.details,

          grid: {
            columnData: columnData2,
            lineIndex: 1,
            frozenColumns: 1,
            allowPaging: false, // 不分页
            dataSource: [],
            asyncConfig: {
              page:
                this.$route.query.from == 'mytodo1'
                  ? JSON.parse(sessionStorage.getItem('todoDetail')).page
                  : {
                      current: 1,
                      size: 10000
                    },
              defaultRules:
                this.$route.query.from == 'mytodo1'
                  ? JSON.parse(sessionStorage.getItem('todoDetail')).defaultRules
                  : [],
              url: `${BASE_TENANT}/kt/outCancelOrder/buyer/queryDetailView`,
              rules:
                this.$route.query.from == 'mytodo1'
                  ? JSON.parse(sessionStorage.getItem('todoDetail')).rules
                  : [
                      {
                        field: 'orderType',
                        operator: 'equal',
                        value: this.$route.path.includes('gc') ? 1 : 0
                      },
                      {
                        field: 'isOutDirect',
                        operator: 'equal',
                        value: 1
                      },
                      {
                        condition: 'and',
                        rules: [
                          {
                            condition: 'or',
                            field: 'status',
                            type: 'string',
                            operator: 'contains',
                            value: 1
                          },
                          {
                            condition: 'or',
                            field: 'status',
                            type: 'string',
                            operator: 'contains',
                            value: 2
                          },
                          {
                            condition: 'or',
                            field: 'status',
                            type: 'string',
                            operator: 'contains',
                            value: 3
                          }
                        ]
                      }
                    ]
            }
            // frozenColumns: 1,
          }
        }
      ],
      pageConfig3: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'export1',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          gridId: 'db80cac7-fe14-4b42-9cfd-37eca9e680a9',

          grid: {
            columnData: columnData3,
            lineIndex: 1,
            frozenColumns: 1,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/kt/outCancelOrder/buyer/queryDetailView`,
              rules: [
                // {
                //   field: 'orderType',
                //   operator: 'equal',
                //   value: this.$route.path.includes('gc') ? 1 : 0
                // },
                // {
                //   field: 'isOutDirect',
                //   operator: 'equal',
                //   value: 1
                // }
              ]
            }
            // frozenColumns: 1,
          }
        }
      ],
      pageConfig4: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'detailAccept',
                  icon: 'icon_table_accept1',
                  title: this.$t('确认')
                  // permission: ["O_02_1295"],
                },
                {
                  id: 'recall',
                  icon: 'icon_table_recall',
                  title: this.$t('退回')
                  // permission: ["O_02_1297"],
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          // gridId: this.$tableUUID.outsourcing.purchaseReturnCargo.details,

          grid: {
            columnData: columnData4,
            lineIndex: 1,
            frozenColumns: 1,
            allowPaging: false, // 不分页
            dataSource: [],
            asyncConfig: {
              page:
                this.$route.query.from == 'mytodo2'
                  ? JSON.parse(sessionStorage.getItem('todoDetail')).page
                  : {
                      current: 1,
                      size: 10000
                    },
              url: `${BASE_TENANT}/kt/outCancelOrder/buyer/queryDetailView`,
              defaultRules:
                this.$route.query.from == 'mytodo2'
                  ? JSON.parse(sessionStorage.getItem('todoDetail')).defaultRules
                  : [],
              rules:
                this.$route.query.from == 'mytodo2'
                  ? JSON.parse(sessionStorage.getItem('todoDetail')).rules
                  : [
                      {
                        field: 'orderType',
                        operator: 'equal',
                        value: this.$route.path.includes('gc') ? 1 : 0
                      },
                      {
                        field: 'isOutDirect',
                        operator: 'equal',
                        value: 0
                      },
                      {
                        condition: 'and',
                        rules: [
                          {
                            condition: 'or',
                            field: 'status',
                            type: 'string',
                            operator: 'contains',
                            value: 1
                          },
                          {
                            condition: 'or',
                            field: 'status',
                            type: 'string',
                            operator: 'contains',
                            value: 2
                          },
                          {
                            condition: 'or',
                            field: 'status',
                            type: 'string',
                            operator: 'contains',
                            value: 3
                          }
                        ]
                      }
                    ]
            }
            // frozenColumns: 1,
          }
        }
      ],
      dialogData: null
    }
  },
  methods: {
    resolveClick(a) {
      this.resolvechange(this.id, a)
      this.deliveryShow = false
    },
    //复选框事件
    rowSelecting(e) {
      if (e?.rowIndexes?.length < 1) return
      if (e.data instanceof Array) return
      // if (e?.data?.status != 2) {
      //   let mapArr = e.rowIndexes.filter((item) => {
      //     return item !== e.rowIndex;
      //   });
      //   if (!(e.data instanceof Array)) {
      //     console.log(e, mapArr);
      //     this.$nextTick(() => {
      //       this.$refs.templateRef
      //         .getCurrentUsefulRef()
      //         .ejsRef.selectRows(mapArr);
      //     });
      //   }
      //   return;
      // }
      //获取当前页所有的行
      let Obj = cloneDeep(this.$refs.template1.getCurrentUsefulRef().ejsRef.getCurrentViewRecords())
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.template1.getCurrentUsefulRef().ejsRef.getSelectedRecords()
      )
      currentSelect.push(e.data)
      currentSelect = [...new Set(currentSelect)]
      if (Obj instanceof Array && Obj.length > 0) {
        let mapArr = []
        for (let i = 0; i < currentSelect.length; i++) {
          for (let j = 0; j < Obj.length; j++) {
            if (currentSelect[i]?.cancelOrderCode === Obj[j]?.cancelOrderCode) {
              mapArr.push(Obj[j])
            }
          }
        }
        mapArr = mapArr.map((item) => item.index)
        if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0 && mapArr.length > 0) {
          this.$refs.template1.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
        }
        console.log(mapArr)
      }
    },
    //取消
    rowDeselecting(e) {
      if (e?.rowIndexes?.length < 1) return
      //获取当前页所有的行
      let Obj = cloneDeep(this.$refs.template1.getCurrentUsefulRef().ejsRef.getCurrentViewRecords())
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.template1
          .getCurrentUsefulRef()
          .ejsRef.getSelectedRecords()
          .map((e) => e.id)
      )
      let mapArr = []
      Obj.filter((item, index) => {
        if (item.cancelOrderCode !== e.data.cancelOrderCode && currentSelect.includes(item.id)) {
          console.log(item)
          mapArr.push(index)
        }
      })
      console.log(e, mapArr)
      if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0) {
        this.$refs.template1.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
      }
    },
    rowSelecting2(e) {
      if (e?.rowIndexes?.length < 1) return
      if (e.data instanceof Array) return
      // if (e?.data?.status != 2) {
      //   let mapArr = e.rowIndexes.filter((item) => {
      //     return item !== e.rowIndex;
      //   });
      //   if (!(e.data instanceof Array)) {
      //     console.log(e, mapArr);
      //     this.$nextTick(() => {
      //       this.$refs.templateRef
      //         .getCurrentUsefulRef()
      //         .ejsRef.selectRows(mapArr);
      //     });
      //   }
      //   return;
      // }
      //获取当前页所有的行
      let Obj = cloneDeep(this.$refs.template2.getCurrentUsefulRef().ejsRef.getCurrentViewRecords())
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.template2.getCurrentUsefulRef().ejsRef.getSelectedRecords()
      )
      currentSelect.push(e.data)
      currentSelect = [...new Set(currentSelect)]
      if (Obj instanceof Array && Obj.length > 0) {
        let mapArr = []
        for (let i = 0; i < currentSelect.length; i++) {
          for (let j = 0; j < Obj.length; j++) {
            if (currentSelect[i]?.cancelOrderCode === Obj[j]?.cancelOrderCode) {
              mapArr.push(Obj[j])
            }
          }
        }
        mapArr = mapArr.map((item) => item.index)
        if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0 && mapArr.length > 0) {
          this.$refs.template2.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
        }
        console.log(mapArr)
      }
    },
    //取消
    rowDeselecting2(e) {
      if (e?.rowIndexes?.length < 1) return
      //获取当前页所有的行
      let Obj = cloneDeep(this.$refs.template2.getCurrentUsefulRef().ejsRef.getCurrentViewRecords())
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.template2
          .getCurrentUsefulRef()
          .ejsRef.getSelectedRecords()
          .map((e) => e.id)
      )
      let mapArr = []
      Obj.filter((item, index) => {
        if (item.cancelOrderCode !== e.data.cancelOrderCode && currentSelect.includes(item.id)) {
          console.log(item)
          mapArr.push(index)
        }
      })
      console.log(e, mapArr)
      if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0) {
        this.$refs.template2.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
      }
    },
    //编辑行内
    cellEdit(e) {
      console.log(e)
      let { status, data } = e
      let flag = false
      if (status) {
        if (this.editData && this.editData.length < 1) {
          this.editData.push(data)
        } else {
          for (let i = 0; i < this.editData.length; i++) {
            if (this.editData[i].id !== data.id) {
              flag = true
              break
            }
          }
          if (flag) {
            let arr = this.editData.map((item) => item.id)
            let indexOf = arr.indexOf(data.id)
            if (indexOf == -1) {
              this.editData.push(data)
            }
          }
          // this.editData.map((item) => {
          //   if (type === "text" && item.id === data.id) {
          //     item.rejectReason = data.rejectReason;
          //   } else if (type === "number" && item.id === data.id) {
          //     item.rejectQuantity = data.rejectQuantity;
          //     item.receiveQuantity = data.receiveQuantity;
          //   }
          // });
        }
      } else {
        if (this.editData && this.editData.length < 1) return
        this.editData.map((item, i) => {
          if (item.id === data.id) {
            this.editData.splice(i, 1)
          }
        })
      }

      console.log(this.editData)
    },
    resolvechange(id, a) {
      let obj = {
        ids: id.toString(),
        rejectReason: a
      }
      this.$API.outsourcing
        .OrderNewBuyerReject(obj)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef?.refreshCurrentGridData()
            this.$refs.template1?.refreshCurrentGridData()
            this.$refs.template2?.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef?.refreshCurrentGridData()
          this.$refs.template1?.refreshCurrentGridData()
          this.$refs.template2?.refreshCurrentGridData()
        })
    },
    restartClick(id) {
      console.log(id)
      let pd = id.toString()
      let obj = { ids: pd }
      this.$API.outsourcing.cancelNewBuyerSyncWms(obj).then(() => {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    handleClickToolBar(e) {
      if (e.toolbar.id === 'createReturn') {
        this.$router.push('new-purchase-create-returnCargo')
        return
      }
      if (e.gridRef.getMtechGridRecords()?.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _status = [],
        _id = []
      let obj = []

      e.gridRef.getMtechGridRecords().map((item) => {
        _id.push(item.id), _status.push(item.status)
        // _sourceStatus.push(item.sourceStatus),
        // _remainingQuantity.push(item.remainingQuantity);
        obj.push(item.cancelOrderCode)
      })
      this.id = _id
      if (e.toolbar.id == 'accept1') {
        for (let i of _status) {
          if (i !== 1 && i !== 2 && i !== 3) {
            this.$toast({
              content: this.$t('当前状态不可操作'),
              type: 'warning'
            })
            return
          }
        }
        this.certain(_id)
        return
      } else if (e.toolbar.id == 'restart') {
        this.restartClick(_id)
      } else if (e.toolbar.id == 'recall') {
        for (let i of _status) {
          if (i !== 1 && i !== 2 && i !== 3) {
            console.log(_status)
            this.$toast({
              content: this.$t('当前状态不可操作'),
              type: 'warning'
            })
            return
          }
        }
        this.deliveryShow = true
      } else if (e.toolbar.id === 'print') {
        for (let i of _status) {
          if (i !== 5 && i !== 6) {
            this.$toast({
              content: this.$t('只有状态在采方已确认才可以打印'),
              type: 'warning'
            })
            return
          }
        }
        this.$API.outsourcing.outCancelOrderBuyerPrint(obj).then((res) => {
          const content = res.data
          let pdfUrl = window.URL.createObjectURL(
            new Blob([content], { type: 'text/html;charset=utf-8' })
          )

          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
      }
    },
    handleClickToolBar2(e) {
      console.log(e)
      // if (e.toolbar.id == "createReturn") {
      //   this.$router.push(`create-noOrder-supplier`);
      //   return;
      // }
      const { toolbar, gridRef } = e
      let _status = [],
        _id = []
      let obj = []

      e.gridRef.getMtechGridRecords().map((item) => {
        _id.push(item.cancelOrderId), _status.push(item.status)
        // _sourceStatus.push(item.sourceStatus),
        // _remainingQuantity.push(item.remainingQuantity);
        obj.push(item.cancelOrderCode)
      })
      this.id = _id

      if (e.gridRef.getMtechGridRecords()?.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id === 'detailAccept') {
        const _selectedData = gridRef.getMtechGridRecords()
        console.log(_selectedData)
        this.handleClickToolBarSubmit(_selectedData)
      } else if (e.toolbar.id == 'recall') {
        for (let i of _status) {
          if (i !== 1 && i !== 2 && i !== 3) {
            console.log(_status)
            this.$toast({
              content: this.$t('当前状态不可操作'),
              type: 'warning'
            })
            return
          }
        }
        this.deliveryShow = true
      }
    },
    handleClickToolBar3(e) {
      console.log(e)
      // if (e.toolbar.id == "new") {
      //   this.$router.push(`create-noOrder-supplier`);
      //   return;
      // }
      const { toolbar, gridRef } = e
      let _status = [],
        _id = []
      let obj = []

      e.gridRef.getMtechGridRecords().map((item) => {
        _id.push(item.cancelOrderId), _status.push(item.status)
        // _sourceStatus.push(item.sourceStatus),
        // _remainingQuantity.push(item.remainingQuantity);
        obj.push(item.cancelOrderCode)
      })
      this.id = _id

      if (e.gridRef.getMtechGridRecords()?.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id === 'detailAccept') {
        const _selectedData = gridRef.getMtechGridRecords()
        console.log(_selectedData)
        this.certain(_id)
      }

      if (e.toolbar.id == 'recall') {
        for (let i of _status) {
          if (i !== 1 && i !== 2 && i !== 3) {
            console.log(_status)
            this.$toast({
              content: this.$t('当前状态不可操作'),
              type: 'warning'
            })
            return
          }
        }
        this.deliveryShow = true
      }
    },
    handleClickToolBar4(e) {
      if (e.toolbar.id === 'export1') {
        let asyncParams = this.$refs.template3.getCurrentUsefulRef().pluginRef.asyncParams || {}
        let obj = JSON.parse(
          sessionStorage.getItem('db80cac7-fe14-4b42-9cfd-37eca9e680a9')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnData3.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        let params = { ...asyncParams }
        if (this.$route.path.includes('gc')) {
          params.rules.push({
            field: 'orderType',
            operator: 'equal',
            value: 1
          })
        }
        this.$store.commit('startLoading')
        this.$API.outsourcing.outCancelOrderBuyerExport(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
    },
    handleClickToolBarSubmit(selectedData) {
      let _selectedData = cloneDeep(selectedData)
      if (_selectedData.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //没有编辑但是勾选的值
      let newarr = this.editData.map((item) => item.id)
      let incluArr = _selectedData.filter((item) => !newarr.includes(item.id))
      // incluArr.map((item) => {
      //   item.receiveQuantity = item.deliveryQuantity;
      // });
      //编辑的值替换勾选的值
      this.editData.forEach((item) => {
        let index = _selectedData.findIndex((e) => e.id == item.id)
        if (index > -1) {
          _selectedData[index] = item
        }
      })
      //勾选但未编辑的值 ---替换
      incluArr.forEach((item) => {
        let index = _selectedData.findIndex((e) => e.id == item.id)
        if (index > -1) {
          _selectedData[index] = item
        }
      })
      console.log(_selectedData)
      this.doSubmit(_selectedData)
    },

    doSubmit(e) {
      console.log(e)

      let obj = e.map((item) => {
        return {
          id: item.id,
          outOrderId: item.cancelOrderId,
          wmsQuantity: item.wmsCancelQuantity
        }
      })
      console.log(obj)
      this.$API.outsourcing
        .OrderNewDetailBuyerConfirm(obj)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.template1.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.template1.refreshCurrentGridData()
        })
    },
    // 确认
    certain(id) {
      let obj = {
        ids: id.toString()
      }
      this.$API.outsourcing
        .OrderNewBuyerConfirm(obj)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef?.refreshCurrentGridData()

          this.$refs.template2?.refreshCurrentGridData()
        })
    },
    // 弹框
    handleDialogShow(flag) {
      this.deliveryShow = flag
    },
    handleSelectTab(e) {
      this.currentTabIndex = e
    },
    // 点击单元格 标题
    handleClickCellTitle(e) {
      console.log(e)
      if (e.field === 'cancelOrderCode') {
        localStorage.setItem('purchaseListScope', JSON.stringify(e.data))
        this.$router.push(
          `new-purchase-detail-returnCargo?cancelOrderCode=${e.data.cancelOrderCode}`
        )
      }
    },
    handleClickCellTool(e) {
      console.log(e)
      if (e.tool.title === this.$t('取消')) {
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消？')
          },
          success: () => {
            this.handleCancel(e.data.id)
          }
        })
      }

      if (e.tool.title === this.$t('手工同步')) {
        this.restartClick(e.data.id)
      }
    },
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    handleCancel(e) {
      this.$API.outsourcing
        .outNewAllocationOutCancelOrder({ ids: e, cancelReason: '' })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('取消成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          }
          // this.$store.commit("endLoading");

          // location.reload();
        })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    }
  }
  // 消失
  // deactivated() {
  //   localStorage.removeItem("deliverDetailSupplier");
  // },
}
</script>

<style></style>
