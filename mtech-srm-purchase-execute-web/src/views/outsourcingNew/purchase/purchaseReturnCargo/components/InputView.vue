<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="disabled"
    ></mt-input>
  </div>
</template>
<script>
// var bigDecimal = require("js-big-decimal");
export default {
  data() {
    return {
      data: {},
      disabled: true,

      packingQuantity: '', //装箱数量
      printQuantity: '', //本次打印数量
      grossWeight: '', //毛重

      netWeight: '' //净重
    }
  },
  mounted() {
    this.deliveryTotalQuantity = this.data.deliveryTotalQuantity
    this.packingQuantity = this.data.packingQuantity
    this.printQuantity = this.data.printQuantity
    this.grossWeight = this.data.grossWeight

    this.$bus.$on('maxDemandQuantityChange', (val) => {
      this.data.maxCancelQuantity = val
    }) //
    this.$bus.$on('supplierStockChange', (val) => {
      this.data.stockQuantity = val
    }) //
    this.netWeight = this.data.netWeight
    this.$bus.$on('itemNameChange', (val) => {
      this.data.itemName = val
    }) //接受的物料名称
    this.$bus.$on('buyerOrgNameChange', (val) => {
      this.data.buyerGroupName = val.buyerGroupName
      this.data.buyerGroupCode = val.buyerGroupCode

      this.$parent.$emit('selectedChanged', {
        //传出额外数据
        fieldCode: 'buyerGroupName',
        itemInfo: {
          ...this.data
        }
      })
    }) //接受的采购组
    this.$bus.$on('planGroupNameChange', (val) => {
      this.data.planGroupName = val
    }) //接受的计划组
    // this.$bus.$on("stockUnitChange", (val) => {
    //   this.data.stockUnit = val;
    // }); //接受的单位
    // this.$bus.$on("stockUnitCodeChange", (val) => {
    //   this.data.stockUnitCode = val;
    // });
    this.$bus.$on('basicUnitCodeChange', (val) => {
      this.data.basicUnitCode = val.basicUnitCode
      this.data.basicUnitName = val.basicUnitName

      this.$parent.$emit('selectedChanged', {
        //传出额外数据
        fieldCode: 'buyerGroupName',
        itemInfo: {
          ...this.data
        }
      })
    }) //接受的单位
  },
  methods: {}
}
</script>
