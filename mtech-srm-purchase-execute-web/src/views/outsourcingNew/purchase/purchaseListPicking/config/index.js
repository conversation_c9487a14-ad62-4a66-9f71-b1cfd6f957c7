import { timeNumberToDate } from '@/utils/utils'
import Vue from 'vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'

export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '155',
    field: 'receiveOrderCode',
    headerText: i18n.t('委外领料单号'),
    cellTools: []
  },
  {
    width: '135',
    field: 'status',
    headerText: i18n.t('状态'),
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('退回'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('已确认'), cssClass: 'col-inactive' },
        { value: 4, text: i18n.t('已完成'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('已取消'), cssClass: 'col-inactive' }
      ]
    },
    cellTools: [
      {
        id: 'Proper',
        icon: 'icon_table_accept1',
        title: i18n.t('确认'),
        // permission: ["O_02_0605"],
        visibleCondition: (data) => data['status'] === 1
      },
      {
        id: 'recall',
        icon: 'icon_table_recall',
        title: i18n.t('退回'),
        // permission: ["O_02_0606"],
        visibleCondition: (data) => data['status'] === 1
      },
      {
        id: 'restart',
        icon: 'icon_table_restart',
        title: i18n.t('手工同步'),
        // permission: ["O_02_0687"],
        visibleCondition: (data) => data['status'] === 3
      },
      {
        id: 'cancel',
        icon: 'icon_table_cancel',
        title: i18n.t('取消'),
        // permission: ["O_02_0606"],
        visibleCondition: (data) => data['status'] === 3 || data['status'] === 2
      }
    ]
  },
  {
    width: '120',
    field: 'isOutSale',
    headerText: i18n.t('是否销售委外'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('是'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '110',
    field: 'materialApproveUserName',
    headerText: i18n.t('供方确认人')
  },
  {
    width: '125',
    field: 'materialApproveDate',
    headerText: i18n.t('供方确认时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    width: '110',
    field: 'buyerCancelUserName',
    headerText: i18n.t('取消人')
  },
  {
    width: '125',
    field: 'buyerCancelDate',
    headerText: i18n.t('取消时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    width: '110',
    field: 'buyerApproveUserName',
    headerText: i18n.t('采方确认人')
  },
  {
    width: '125',
    field: 'buyerApproveDate',
    headerText: i18n.t('采方确认时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    width: '95',
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: '0', text: i18n.t('未同步'), cssClass: '' },
        { value: '1', text: i18n.t('同步成功'), cssClass: '' },
        { value: '2', text: i18n.t('同步失败'), cssClass: '' },
        { value: '3', text: i18n.t('同步中'), cssClass: '' }
      ]
    }
  },
  {
    field: 'syncMsg',
    headerText: i18n.t('同步消息')
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    width: '265',
    // allowEditing: false,
    searchOptions: MasterDataSelect.factoryAddress,
    // searchable: true,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerOrgCode',
    headerText: i18n.t('公司'),
    width: '230',
    searchOptions: MasterDataSelect.businessCompany,

    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  // {
  //   width: "150",
  //   field: "supplierCode",
  //   headerText: i18n.t("领料供应商编码"),
  // },
  {
    width: '250',
    field: 'supplierCode',
    headerText: i18n.t('领料供应商'),
    searchOptions: MasterDataSelect.supplier,

    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.supplierCode}}-{{data.supplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },

  {
    width: '145',
    field: 'createTime',
    headerText: i18n.t('制单日期')
    // editTemplate: () => {
    //   return {
    //     template: "1212",
    //   };
    // },
    // template: timeDate("sendTime", true),
  },
  {
    field: 'createUserName',
    headerText: i18n.t('制单人'),
    width: '90'
  }
]
export const columnData2 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '155',
    field: 'receiveOrderCode',
    headerText: i18n.t('委外领料单号')
  },
  {
    width: '80',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('退回'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('已确认'), cssClass: 'col-inactive' },
        { value: 4, text: i18n.t('已完成'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('已取消'), cssClass: 'col-inactive' }
      ]
    }
  },
  {
    width: '120',
    field: 'isOutSale',
    headerText: i18n.t('是否销售委外'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('是'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '70',
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '120',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '395',
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },
  {
    width: '200',
    field: 'soAndDn',
    headerText: i18n.t('销售凭证&交货单')
  },
  {
    width: '125',
    field: 'receiveQuantity',
    headerText: i18n.t('本次领料数量')
    // template: () => {
    //   return {
    //     template: Vue.component("actionInput", {
    //       template: `
    //     <div>
    //       <mt-input
    //       id="receiveQuantity"
    //       v-model="data.receiveQuantity"
    //       @change="change"
    //     ></mt-input>
    //     </div>
    //       `,
    //       data: function () {
    //         return {
    //           data: {},
    //         };
    //       },
    //       mounted() {
    //         // this.$bus.$off(`rejectReason${this.data.index}`);
    //         // this.$bus.$on(`rejectReason${this.data.index}`, (val) => {
    //         //   if (this.data.index === val.index && val.rejectQuantity != 0) {
    //         //     if (this.data.rejectQuantity == val.rejectQuantity) {
    //         //       this.dataObj.rejectReason = this.data.rejectReason;
    //         //     }
    //         //     this.rejectReasonFlag = false;
    //         //   } else if (
    //         //     this.data.index === val.index &&
    //         //     val.rejectQuantity == 0
    //         //   ) {
    //         //     this.rejectReasonFlag = true;
    //         //     this.dataObj.rejectReason = "";
    //         //   }
    //         // });
    //       },
    //       methods: {
    //         change(e) {
    //           this.data.receiveQuantity = e;
    //           this.$parent.$emit(`cellEdit`, {
    //             data: this.data,
    //             type: "text",
    //             status: true,
    //           });
    //         },
    //       },
    //     }),
    //   };
    // },
  },
  {
    width: '95',
    field: 'wmsReceiveQuantity',
    headerText: i18n.t('发料数量')
  },
  {
    width: '65',
    field: 'basicUnitName',
    headerText: i18n.t('单位'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.basicUnitCode}}-{{data.basicUnitName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    searchOptions: MasterDataSelect.factoryAddress,
    width: '260',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerOrgCode',
    headerText: i18n.t('公司'),
    searchOptions: MasterDataSelect.businessCompany,

    width: '235',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '250',
    field: 'supplierCode',
    headerText: i18n.t('领料供应商'),
    searchOptions: MasterDataSelect.supplier,

    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.supplierCode}}-{{data.supplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '100',
    field: 'warehouseCode',
    headerText: i18n.t('库存地点'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.warehouseCode}}-{{data.warehouseName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },

  // {
  //   width: "150",
  //   field: "buyerOrgName",
  //   headerText: i18n.t("采购组"),
  // },
  {
    width: '145',
    field: 'createTime',
    headerText: i18n.t('制单日期')
  },
  // {
  //   width: "150",
  //   field: "receiveSupplierName",
  //   headerText: i18n.t("送货方式"), 暂无
  // },
  // {
  //   width: "150",
  //   field: "receiveSupplierName",
  //   headerText: i18n.t("收货方名称"),
  // },
  // {
  //   width: "150",
  //   field: "receiveSupplierCode",
  //   headerText: i18n.t("收货方编号"),
  // },
  {
    width: '90',
    field: 'createUserName',
    headerText: i18n.t('制单人')
  }
]
export const todoColumnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '155',
    field: 'receiveOrderCode',
    headerText: i18n.t('委外领料单号')
  },
  {
    width: '80',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('退回'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('已确认'), cssClass: 'col-inactive' },
        { value: 4, text: i18n.t('已完成'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('已取消'), cssClass: 'col-inactive' }
      ]
    }
  },
  {
    width: '70',
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '120',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '395',
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },
  {
    width: '125',
    field: 'receiveQuantity',
    headerText: i18n.t('本次领料数量')
    // template: () => {
    //   return {
    //     template: Vue.component("actionInput", {
    //       template: `
    //     <div>
    //       <mt-input
    //       id="receiveQuantity"
    //       v-model="data.receiveQuantity"
    //       @change="change"
    //     ></mt-input>
    //     </div>
    //       `,
    //       data: function () {
    //         return {
    //           data: {},
    //         };
    //       },
    //       mounted() {
    //         // this.$bus.$off(`rejectReason${this.data.index}`);
    //         // this.$bus.$on(`rejectReason${this.data.index}`, (val) => {
    //         //   if (this.data.index === val.index && val.rejectQuantity != 0) {
    //         //     if (this.data.rejectQuantity == val.rejectQuantity) {
    //         //       this.dataObj.rejectReason = this.data.rejectReason;
    //         //     }
    //         //     this.rejectReasonFlag = false;
    //         //   } else if (
    //         //     this.data.index === val.index &&
    //         //     val.rejectQuantity == 0
    //         //   ) {
    //         //     this.rejectReasonFlag = true;
    //         //     this.dataObj.rejectReason = "";
    //         //   }
    //         // });
    //       },
    //       methods: {
    //         change(e) {
    //           this.data.receiveQuantity = e;
    //           this.$parent.$emit(`cellEdit`, {
    //             data: this.data,
    //             type: "text",
    //             status: true,
    //           });
    //         },
    //       },
    //     }),
    //   };
    // },
  },
  {
    width: '95',
    field: 'wmsReceiveQuantity',
    headerText: i18n.t('发料数量')
  },
  {
    width: '65',
    field: 'basicUnitName',
    headerText: i18n.t('单位'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.basicUnitCode}}-{{data.basicUnitName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    searchOptions: MasterDataSelect.factoryAddress,
    width: '260',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerOrgCode',
    headerText: i18n.t('公司'),
    searchOptions: MasterDataSelect.businessCompany,

    width: '235',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '250',
    field: 'supplierCode',
    headerText: i18n.t('领料供应商'),
    searchOptions: MasterDataSelect.supplier,

    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.supplierCode}}-{{data.supplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '100',
    field: 'warehouseCode',
    headerText: i18n.t('库存地点'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.warehouseCode}}-{{data.warehouseName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },

  // {
  //   width: "150",
  //   field: "buyerOrgName",
  //   headerText: i18n.t("采购组"),
  // },
  {
    width: '145',
    field: 'createTime',
    headerText: i18n.t('制单日期')
  },
  // {
  //   width: "150",
  //   field: "receiveSupplierName",
  //   headerText: i18n.t("送货方式"), 暂无
  // },
  // {
  //   width: "150",
  //   field: "receiveSupplierName",
  //   headerText: i18n.t("收货方名称"),
  // },
  // {
  //   width: "150",
  //   field: "receiveSupplierCode",
  //   headerText: i18n.t("收货方编号"),
  // },
  {
    width: '90',
    field: 'createUserName',
    headerText: i18n.t('制单人')
  }
]
