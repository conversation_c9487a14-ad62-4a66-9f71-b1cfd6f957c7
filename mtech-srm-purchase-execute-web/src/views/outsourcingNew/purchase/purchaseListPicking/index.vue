<template>
  <div class="full-height pt20">
    <!-- <mt-template-page
      :template-config="pageConfig"
      @handleSelectTab="handleSelectTab"
    > -->
    <mt-template-page
      slot="slot-0"
      ref="templateRef"
      :current-tab="currentTab"
      :template-config="pageConfig1"
      @rowSelecting="rowSelecting"
      @rowDeselected="rowDeselecting"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickCellTool="handleClickCellTool"
    >
    </mt-template-page>

    <!-- <mt-template-page
        slot="slot-1"
        ref="templateRef"
        @rowSelecting="rowSelecting"
        @handleClickToolBar="handleClickToolBar2"
        @rowDeselected="rowDeselecting"
        :template-config="pageConfig2"
        @cellEdit="cellEdit"
      >
      </mt-template-page> -->

    <!-- <mt-template-page
        slot="slot-2"
        ref="template2"
        @handleClickToolBar="handleClickToolBar3"
        :template-config="pageConfig3"
      >
      </mt-template-page> -->
    <!-- </mt-template-page> -->
    <deliver
      @handleAddDialogShow="handleAddDialogShow"
      @resolveClick="resolveClick"
      ref="headerTop"
      v-if="deliveryShow"
      class="flex-keep"
    ></deliver>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { BASE_TENANT } from '@/utils/constant'
import { columnData, columnData2 } from './config/index.js'
// import deliveryDialog from "./components/deliveryDialog";
import { cloneDeep } from 'lodash'

export default {
  mounted() {
    if (this.$route.query.from == 'mytodo4') {
      this.pageConfig1[0].grid.asyncConfig = {
        url: `${BASE_TENANT}/kt/outReceiveOrder/buyer/queryView`,
        defaultRules: [
          {
            condition: null,
            field: 'syncStatus',
            label: null,
            type: 'string',
            operator: 'equal',
            value: 2,
            rules: null
          },
          {
            condition: 'and',
            field: null,
            label: null,
            type: 'string',
            operator: 'none',
            value: null,
            rules: [
              {
                condition: 'or',
                field: 'status',
                label: null,
                type: 'string',
                operator: 'equal',
                value: 3,
                rules: null
              },
              {
                condition: 'or',
                field: 'status',
                label: null,
                type: 'string',
                operator: 'equal',
                value: 5,
                rules: null
              }
            ]
          }
        ]
      }
    } else if (this.$route.query.from === 'mytodo5') {
      const { rules, defaultRules } = JSON.parse(sessionStorage.getItem('todoDetail')) || {
        rules: [],
        defaultRules: []
      }
      this.pageConfig1[0].grid.asyncConfig = {
        url: `${BASE_TENANT}/kt/outReceiveOrder/buyer/queryView`,
        defaultRules: defaultRules,
        rules: rules
      }
    }
  },
  components: {
    deliver: require('./components/deliver.vue').default
  },
  data() {
    let currentTab = 0
    if (this.$route.query.from == 'mytodo0') {
      currentTab = 1
    } else if (this.$route.query.from == 'mytodo5') {
      currentTab = 0
    }
    return {
      id: [],
      editdata: [], //编辑维护的数组

      deliveryShow: false,
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0144' },
          { dataPermission: 'b', permissionCode: 'T_02_0145' }
        ]
      },

      userInfo: null,
      addDialogShow: false,
      currentTab,
      pageConfig1: [
        {
          title: this.$t('头视图'),

          // dataPermission: "a",
          // permissionCode: "T_02_0144",
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'accept1',
                  icon: 'icon_table_accept1',
                  title: this.$t('确认')
                  // permission: ["O_02_1295"],
                },
                {
                  id: 'recall',
                  icon: 'icon_table_recall',
                  title: this.$t('退回')
                  // permission: ["O_02_1295"],
                },
                {
                  id: 'restart',
                  icon: 'icon_table_restart',
                  title: this.$t('手工同步')
                  // permission: ["O_02_1296"],
                },
                {
                  id: 'print',
                  icon: 'icon_table_print',
                  title: this.$t('打印')
                  // permission: ["O_02_1046"],
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          // gridId: this.$tableUUID.outsourcing.purchasePicking.list,

          grid: {
            columnData: columnData,
            lineIndex: 1,
            autoWidthColumns: columnData.length + 1,
            dataSource: [],
            asyncConfig: {
              ignoreDefaultSearch: false,
              url: `${BASE_TENANT}/kt/outReceiveOrder/buyer/queryView`
              // 妥投 序列化 TODO 演示后删掉
            },
            frozenColumns: 1
          }
        },
        {
          title: this.$t('待审核'),

          // dataPermission: "b",
          // permissionCode: "T_02_0145",
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'todoAccept1',
                  icon: 'icon_table_accept1',
                  title: this.$t('确认')
                  // permission: ["O_02_1295"],
                },
                {
                  id: 'todoRecall',
                  icon: 'icon_table_recall',
                  title: this.$t('退回')
                  // permission: ["O_02_1295"],
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          // gridId: this.$tableUUID.outsourcing.purchaseReturnCargo.details,

          grid: {
            allowPaging: false, // 不分页

            columnData: columnData2,
            lineIndex: 1,
            autoWidthColumns: columnData2.length + 1,
            dataSource: [],
            asyncConfig: {
              defaultRules: [
                {
                  field: 'status',
                  type: 'string',
                  operator: 'contains',
                  value: 1
                }
              ],
              page: {
                current: 1,
                size: 10000
              },
              url: `${BASE_TENANT}/kt/outReceiveOrder/buyer/queryDetailView`
            },
            frozenColumns: 1
          }
        },
        {
          title: this.$t('明细视图'),

          // dataPermission: "b",
          // permissionCode: "T_02_0145",
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                // {
                //   id: "detailAccept",
                //   icon: "icon_table_accept1",
                //   title: this.$t("确认"),
                //   // permission: ["O_02_1295"],
                // },
                {
                  id: 'export1',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          // gridId: this.$tableUUID.outsourcing.purchaseReturnCargo.details,
          gridId: 'afc25d40-7eb0-4e67-91de-fd13e259b552',
          grid: {
            columnData: columnData2,
            lineIndex: 1,
            autoWidthColumns: columnData2.length + 1,
            dataSource: [],
            asyncConfig: {
              defaultRules: [],
              url: `${BASE_TENANT}/kt/outReceiveOrder/buyer/queryDetailView`
            },
            frozenColumns: 1
          }
        }
      ],
      // pageConfig2: [
      //   {
      //     // dataPermission: "b",
      //     // permissionCode: "T_02_0145",
      //     useToolTemplate: false, // 不使用预置(新增、编辑、删除)
      //     useBaseConfig: true, // 使用组件中的toolbar配置
      //     toolbar: {
      //       tools: [
      //         [
      //           // {
      //           //   id: "detailAccept",
      //           //   icon: "icon_table_accept1",
      //           //   title: this.$t("确认"),
      //           //   // permission: ["O_02_1295"],
      //           // },
      //         ],
      //         ["Filter", "refresh", "setting"],
      //       ],
      //     },
      //     // gridId: this.$tableUUID.outsourcing.purchaseReturnCargo.details,

      //     grid: {
      //       columnData: columnData2,
      //       lineIndex: 1,
      //       autoWidthColumns: columnData2.length + 1,
      //       dataSource: [],
      //       asyncConfig: {
      //         page: {
      //           current: 1,
      //           size: 10000,
      //         },
      //         defaultRules: [
      //           {
      //             field: "status", // 送货中
      //             type: "string",
      //             operator: "equal",
      //             value: 1,
      //           },
      //         ],
      //         url: `${BASE_TENANT}/kt/outReceiveOrder/buyer/queryDetailView`,
      //       },
      //       frozenColumns: 1,
      //     },
      //   },
      // ],

      dialogData: null
    }
  },
  methods: {
    resolveClick(a) {
      if (this.TypeKt === 'cancel') {
        this.cancelChange(this.id, a)
        this.deliveryShow = false
      } else if (this.TypeKt === 'recall') {
        this.resolvechange(this.id, a)
        this.deliveryShow = false
      }
    },
    resolvechange(id, a) {
      let obj = {
        ids: id.toString(),
        rejectReason: a
      }
      this.$API.outsourcing
        .buyerNewReject(obj)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef.refreshCurrentGridData()
        })
    },
    cancelChange(id, a) {
      let obj = {
        ids: id.toString(),
        rejectReason: a
      }
      this.$API.outsourcing
        .buyerCancel(obj)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef.refreshCurrentGridData()
        })
    },

    //复选框事件
    rowSelecting(e) {
      if (e?.rowIndexes?.length < 1) return
      if (e.data instanceof Array) return
      // if (e?.data?.status != 2) {
      //   let mapArr = e.rowIndexes.filter((item) => {
      //     return item !== e.rowIndex;
      //   });
      //   if (!(e.data instanceof Array)) {
      //     console.log(e, mapArr);
      //     this.$nextTick(() => {
      //       this.$refs.templateRef
      //         .getCurrentUsefulRef()
      //         .ejsRef.selectRows(mapArr);
      //     });
      //   }
      //   return;
      // }
      //获取当前页所有的行
      let Obj = cloneDeep(
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getCurrentViewRecords()
      )
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getSelectedRecords()
      )
      currentSelect.push(e.data)
      currentSelect = [...new Set(currentSelect)]
      if (Obj instanceof Array && Obj.length > 0) {
        let mapArr = []
        for (let i = 0; i < currentSelect.length; i++) {
          for (let j = 0; j < Obj.length; j++) {
            if (currentSelect[i]?.receiveOrderCode === Obj[j]?.receiveOrderCode) {
              mapArr.push(Obj[j])
            }
          }
        }
        mapArr = mapArr.map((item) => item.index)
        if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0 && mapArr.length > 0) {
          this.$refs.templateRef.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
        }
        console.log(mapArr)
      }
    },
    //取消
    rowDeselecting(e) {
      if (e?.rowIndexes?.length < 1) return
      //获取当前页所有的行
      let Obj = cloneDeep(
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getCurrentViewRecords()
      )
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.templateRef
          .getCurrentUsefulRef()
          .ejsRef.getSelectedRecords()
          .map((e) => e.id)
      )
      let mapArr = []
      Obj.filter((item, index) => {
        if (item.receiveOrderCode !== e.data.receiveOrderCode && currentSelect.includes(item.id)) {
          console.log(item)
          mapArr.push(index)
        }
      })
      console.log(e, mapArr)
      if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0) {
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
      }
    },
    // 确认
    certain(id) {
      let obj = {
        ids: id.toString()
      }
      console.log(obj)
      this.$API.outsourcing
        .buyerNewConfirm(obj)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef.refreshCurrentGridData()
        })
    },
    // 弹框
    handleDialogShow(flag) {
      this.deliveryShow = flag
    },
    handleSelectTab(e) {
      this.currentTabIndex = e
    },
    // 点击单元格 标题
    handleClickCellTitle(e) {
      // const { toolbar, gridRef } = e

      // if (e.data.status == "1") {
      if (e.field === 'receiveOrderCode') {
        localStorage.setItem('purchaseListScope', JSON.stringify(e.data))
        this.$router.push(`new-purchase-detail-picking?receiveOrderCode=${e.data.receiveOrderCode}`)
      }

      // } else if (e.data.status == "0") {
      //   this.$router.push(`deliver-detail-supplier?id=${e.data.id}&type=no`);
      // }
    },
    handleClickCellTool(e) {
      console.log(e)
      if (e.tool.title === this.$t('确认')) {
        this.certain(e.data.id)
      }

      if (e.tool.title === this.$t('退回')) {
        this.id = []
        this.TypeKt = 'recall'

        this.id.push(e.data.id)
        this.deliveryShow = true
      }

      if (e.tool.title === this.$t('手工同步')) {
        this.id = []
        this.restartClick(e.data.id)
      }
      if (e.tool.title === this.$t('取消')) {
        this.id = []
        this.TypeKt = 'cancel'
        this.id.push(e.data.id)
        this.deliveryShow = true
      }
    },
    restartClick(id) {
      console.log(id)
      let pd = id.toString()
      let obj = { ids: pd }
      this.$API.outsourcing.buyerNewSyncWms(obj).then(() => {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    handleClickToolBar(e) {
      console.log(e)
      if (e.toolbar.id === 'export1') {
        let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let obj = JSON.parse(
          sessionStorage.getItem('afc25d40-7eb0-4e67-91de-fd13e259b552')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnData2.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        let params = {
          page: { current: 1, size: 10000 },
          rules: rule.rules || []
        }
        this.$store.commit('startLoading')
        this.$API.outsourcing.purchaseNewExport(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
      console.log(e.gridRef.getMtechGridRecords())

      // if (e.toolbar.id == "new") {
      //   this.$router.push(`create-noOrder-supplier`);
      //   return;
      // }

      if (e.gridRef.getMtechGridRecords()?.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _status = [],
        _id = []
      let _syncStatus = []
      let obj = []
      let todoId = []
      e.gridRef.getMtechGridRecords().map((item) => {
        _id.push(item.id), _status.push(item.status)
        _syncStatus.push(item.syncStatus)
        obj.push(item.receiveOrderCode)
        if (item.receiveOrderId) {
          todoId.push(item.receiveOrderId)
        }
        // _sourceStatus.push(item.sourceStatus),
        // _remainingQuantity.push(item.remainingQuantity);
      })
      this.id = _id
      console.log(_status)
      if (e.toolbar.id == 'accept1') {
        this.certain(_id)
        return
      }

      if (e.toolbar.id == 'recall') {
        for (let i of _status) {
          if (i !== 1) {
            console.log(_status)
            this.$toast({
              content: this.$t('只有状态在待确认中才可以退回'),
              type: 'warning'
            })
            return
          }
        }
        this.TypeKt = 'recall'

        this.deliveryShow = true
      }
      if (e.toolbar.id == 'todoAccept1') {
        for (let i of _status) {
          if (i !== 1) {
            this.$toast({
              content: this.$t('只有状态在发货中才可以确认'),
              type: 'warning'
            })
            return
          }
        }
        this.certain(todoId)
        return
      }
      if (e.toolbar.id == 'todoRecall') {
        for (let i of _status) {
          if (i !== 1) {
            console.log(_status)
            this.$toast({
              content: this.$t('只有状态在待确认中才可以退回'),
              type: 'warning'
            })
            return
          }
        }
        this.TypeKt = 'recall'
        this.id = todoId

        this.deliveryShow = true
      }
      if (e.toolbar.id == 'cancel') {
        for (let i of _status) {
          if (i !== 3) {
            console.log(_status)
            this.$toast({
              content: this.$t('只有状态在已确认中才可以退回'),
              type: 'warning'
            })
            return
          }
        }
        this.TypeKt = 'cancel'
        this.deliveryShow = true
      }
      if (e.toolbar.id == 'restart') {
        console.log(_syncStatus)
        // for (let i of _syncStatus) {
        //   if (i === "1") {
        //     this.$toast({
        //       content: this.$t("该状态不可手工同步"),
        //       type: "warning",
        //     });
        //     return;
        //   }
        // }
        this.restartClick(_id)
      }
      if (e.toolbar.id === 'print') {
        for (let i of _status) {
          if (i !== 3 && i !== 4) {
            this.$toast({
              content: this.$t('只有状态在采方已确认才可以打印'),
              type: 'warning'
            })
            return
          }
        }
        this.$API.outsourcing.outReceiveOrderBuyerPrint(obj).then((res) => {
          // if (res?.data?.type === "application/json") {
          //   const reader = new FileReader();
          //   reader.readAsText(res?.data, "utf-8");
          //   reader.onload = function () {
          //     console.log("======", reader);
          //     const readerRes = reader.result;
          //     const resObj = JSON.parse(readerRes);
          //     Vue.prototype.$toast({
          //       content: resObj.msg,
          //       type: "error",
          //     });
          //   };

          //   return;
          // }

          const content = res.data
          let pdfUrl = window.URL.createObjectURL(
            new Blob([content], { type: 'text/html;charset=utf-8' })
          )

          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
      }
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    handleClickToolBarSubmit(selectedData) {
      let _selectedData = cloneDeep(selectedData)
      if (_selectedData.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //没有编辑但是勾选的值
      let newarr = this.editdata.map((item) => item.id)
      let incluArr = _selectedData.filter((item) => !newarr.includes(item.id))
      // incluArr.map((item) => {
      //   item.receiveQuantity = item.deliveryQuantity;
      // });
      //编辑的值替换勾选的值
      this.editdata.forEach((item) => {
        let index = _selectedData.findIndex((e) => e.id == item.id)
        if (index > -1) {
          _selectedData[index] = item
        }
      })
      //勾选但未编辑的值 ---替换
      incluArr.forEach((item) => {
        let index = _selectedData.findIndex((e) => e.id == item.id)
        if (index > -1) {
          _selectedData[index] = item
        }
      })
      console.log(_selectedData)
      this.doSubmit(_selectedData)
    },
    doSubmit(e) {
      console.log(e)
    },
    handleClickToolBar2(e) {
      console.log(e)
      // if (e.toolbar.id == "new") {
      //   this.$router.push(`create-noOrder-supplier`);
      //   return;
      // }
      const { toolbar, gridRef } = e

      if (e.gridRef.getMtechGridRecords()?.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id === 'detailAccept') {
        const _selectedData = gridRef.getMtechGridRecords()
        console.log(_selectedData)
        this.handleClickToolBarSubmit(_selectedData)
      }
    }
  }
  // 消失
  // deactivated() {
  //   localStorage.removeItem("deliverDetailSupplier");
  // },
}
</script>

<style></style>
