<template>
  <div class="full-height pt20 vertical-flex-box">
    <top-info
      @submitClick="submitClick"
      @codeChange="codeChange"
      @orderChange="orderChange"
      @resolve="resolve"
      @deleteRe="deleteRe"
      :header-info="headerInfo"
      ref="topInfo"
      class="flex-keep"
    ></top-info>
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :load="load"
      @actionBegin="actionBegin"
      @rowSelected="rowSelected"
      @actionComplete="actionComplete"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @selectedChanged="selectedChanged"
    >
    </mt-template-page>
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :is-show-tips="true"
      :request-urls="requestUrls"
      @closeUploadExcel="handleImport(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
    <!-- </div> -->
  </div>
</template>
<script>
import { pageConfig, editSettings } from './config/transferBillCfg.js'

export default {
  components: {
    TopInfo: () => import('./components/topInfo'),
    UploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    return {
      downTemplateParams: {
        pageFlag: false
      }, // 通知配置导入下载模板参数
      uploadParams: {}, // 导入通知配置文件参数

      requestUrls: {
        templateUrlPre: 'outsourcing',
        templateUrl: 'outReceiveOrderTemplate', // 下载模板接口方法名
        uploadUrl: 'outAllocationOrderImport' // 上传接口方法名
      },
      headerInfo: {
        siteCode: '',
        remark: ''
      },

      beginData: null,
      editSettings,
      pageConfig: pageConfig,
      rowList: [],
      topList: {},

      entryId: '', //编辑带入的订单id
      entryType: null, //1是新增 2是编辑 3是反馈异常
      entrySource: '', // 0采购申请 1手工创建 2商城进入 4合同进入
      entryDraft: '', //1是草稿 2不是草稿 草稿可以修改
      ids: '',
      saveList: null,
      topInfo: {
        orderCode: '',
        createType: '1',
        isOutSale: '0'
      },
      selectedOtherInfo: {},

      edit: ''
    }
  },
  mounted() {
    this.edit = this.$route.query.edit
    if (this.edit) {
      this.getOnePicking()
    }
    window.pickGrid = this.$refs.templateRef
  },
  methods: {
    handleImport(flag) {
      //导入
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    load(args) {
      console.log('load-----------', args)
      this.$refs.grid.$el.addEventListener('keydown', this.keyDownHandler)
    },
    keyPressed(args) {
      console.log('keyPressed', args)
    },
    keyDownHandler(e) {
      console.log('keyDownHandler', e)
    },
    submitClick(val) {
      let currentRecords =
        this.$refs.templateRef?.getCurrentUsefulRef().gridRef?.ejsRef.getCurrentViewRecords() || []
      this.rowList = currentRecords
      let obj = {
        detailRequestList: this.rowList,
        orderType: this.$route.path.includes('gc') ? 1 : 0,

        ...val
      }
      if (obj.inSupplierCode === obj.outSupplierCode) {
        this.$toast({
          content: this.$t('调入供应商和调出供应商不能一致'),
          type: 'warning'
        })
        return
      }
      obj.detailRequestList.forEach((item) => {
        return (item.orderCode = val.orderCode), (item.orderItemNo = val.orderItemNo)
      })
      if (this.ids.length > 0) {
        obj.id = this.ids
      }
      this.$API.outsourcing.createTransferBillSave(obj).then((r) => {
        if (r.code === 200) {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
          this.ids = r.data
        }
      })
    },
    resolve(val) {
      let currentRecords =
        this.$refs.templateRef?.getCurrentUsefulRef().gridRef?.ejsRef.getCurrentViewRecords() || []

      this.rowList = currentRecords
      console.log(this.ids.length)
      if (this.ids.length > 0) {
        let obj = [this.ids]
        // if (obj.inSupplierCode === obj.outSupplierCode) {
        //   this.$toast({
        //     content: this.$t('调入供应商和调出供应商不能一致'),
        //     type: 'warning'
        //   })
        //   return
        // }
        this.$API.outsourcing.createTransferBillSubmit(obj).then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('提交成功'),
              type: 'success'
            })
            this.$router.go(-1)
          }
        })
      } else {
        let obj = {
          detailRequestList: this.rowList,
          orderType: this.$route.path.includes('gc') ? 1 : 0,

          ...val
        }
        if (obj.inSupplierCode === obj.outSupplierCode) {
          this.$toast({
            content: this.$t('调入供应商和调出供应商不能一致'),
            type: 'warning'
          })
          return
        }
        obj.detailRequestList.forEach((item) => {
          return (item.orderCode = val.orderCode), (item.orderItemNo = val.orderItemNo)
        })
        if (this.ids.length > 0) {
          obj.id = this.ids
        } else {
          obj.id = null
        }
        this.$API.outsourcing.createTransferBillSave(obj).then((r) => {
          this.$API.outsourcing.createTransferBillSubmit([r.data]).then(() => {
            // if (res.code === 200) {
            this.$toast({
              content: this.$t('提交成功'),
              type: 'success'
            })
            this.$router.go(-1)
            // }
          })
        })
      }
    },
    rowSelected(e) {
      console.log(e)
      e.previousRowIndex = e.rowIndex
    },
    selectedChanged(val) {
      console.log(val)
      console.log(this.selectedOtherInfo)
      Object.assign(this.selectedOtherInfo, val.itemInfo)
      console.log(this.selectedOtherInfo, '最新的额外数据')
    },
    getOnePicking() {
      this.$store.commit('startLoading')

      this.$API.outsourcing
        .outAllocationNewOrderGetOne({ allocationOrderCode: this.edit })
        .then((r) => {
          // this.topInfo.siteId = r.data.siteId;
          // this.topInfo.supplierCode = r.data.supplierCode;
          // this.topInfo.orderCode = r.data.orderCode;
          // this.topInfo.isOutSale = r.data.isOutSale;
          // this.topInfo.remark = r.data.remark;
          // sessionStorage.setItem("order", JSON.stringify(r.data));

          this.headerInfo = r.data || {}
          this.$refs.topInfo.init()

          this.topList.siteCode = r.data.siteCode
          console.log(this.headerInfo)
          this.pageConfig[0].grid.dataSource = r.data.detailResponseList
          this.$store.commit('endLoading')
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    orderChange(e, obj) {
      console.log(e)
      this.topList.itemCode = e.itemCode
      this.$API.outsourcing.AllocationOrderSupplierOrderBomStock(obj).then((r) => {
        let obj = r.data.map((item) => {
          return {
            ...item,
            stockUnit: item.unitCode,
            maxAllocationQuantity:
              item.wmsStockResponseList[0].stockBatchResponseList[0].maxCreateQty,
            stockQuantity: item.wmsStockResponseList[0].stockBatchResponseList[0].qty,

            // itemName: "",
            // itemCode:item.itemCode
            ...item.wmsStockResponseList[0],
            ...item.wmsStockResponseList[0].stockBatchResponseList[0]
          }
        })
        console.log(obj)
        this.pageConfig[0].grid.dataSource = obj
        console.log(this.pageConfig[0].grid.dataSource)
      })
    },
    upExcelConfirm(e) {
      this.handleImport(false)
      console.log(e)
      e.data.forEach((item) => {
        item.allocationQuantity = item.quantity
        item.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
      })
      this.$refs.templateRef.$refs[
        'pageGridRef-0'
      ][0].$refs.pluginGridRef.$refs.gridRef.ejsInstances.dataSource.push(...e.data)

      this.$refs.templateRef.getCurrentTabRef()?.grid.refresh()
      // this.pageConfig[0].grid.dataSource = e.data;
    },
    codeChange(e) {
      this.topList.orderCode = e
    },
    deleteRe() {
      // this.$dialog({
      //   data: {
      //     title: this.$t("确认"),
      //     message: this.$t(
      //       "更换公司和采购订单将重新清空/获取领料物料数据，请确定是否继续?"
      //     ),
      //   },codeArr
      // success: () => {
      let currentRecords =
        this.$refs.templateRef?.getCurrentUsefulRef().gridRef?.ejsRef.getCurrentViewRecords() || []
      if (currentRecords.length > 0) {
        let numList = []
        for (let i = 0; i < currentRecords.length; i++) {
          numList.push(i)
        }
        console.log(numList)
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRows(numList)
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
      }
      //   },
      // });
    },
    handleClickToolBar(e) {
      console.log('handleClickToolBar', e)
      const codeArr = this.$refs.topInfo.getTopInfo()
      if (e.toolbar.id === 'Add') {
        if (
          codeArr.siteCode === undefined ||
          codeArr.siteCode === null ||
          codeArr.siteCode === '' ||
          codeArr.isOutSale === undefined ||
          codeArr.isOutSale === null ||
          codeArr.isOutSale === ''
        ) {
          this.$toast({
            content: this.$t('头部带星号的为必填项'),
            type: 'warning'
          })
        } else if (
          codeArr.inSupplierCode === undefined ||
          codeArr.inSupplierCode === null ||
          codeArr.inSupplierCode === ''
        ) {
          this.$toast({
            content: this.$t('调入供应商必填'),
            type: 'warning'
          })
        } else {
          console.log(this.topList)
          console.log('ref', this.$refs.templateRef.getCurrentUsefulRef())
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
          this.$refs.topInfo.init()

          // let obj = {
          //   buyerEnterpriseId: this.codeArr.buyerEnterpriseId,
          //   createType: 2,
          //   itemCode: "81102-009060",
          //   siteCode: 5500,
          //   supplierCode: 105541,
          // };
          // this.$API.outsourcing.OutQuerySapOutDemand(obj).then((res) => {
          //   res.data.forEach((item) => {
          //     item.allocationQuantity = 50;
          //   });
          //   this.pageConfig[0].grid.dataSource = res.data;
          // });
        }
      } else if (e.toolbar.id === 'Import') {
        if (
          codeArr.siteCode === undefined ||
          codeArr.siteCode === null ||
          codeArr.siteCode === '' ||
          codeArr.isOutSale === undefined ||
          codeArr.isOutSale === null ||
          codeArr.isOutSale === ''
        ) {
          this.$toast({
            content: this.$t('头部带星号的为必填项'),
            type: 'warning'
          })
        } else if (
          codeArr.inSupplierCode === undefined ||
          codeArr.inSupplierCode === null ||
          codeArr.inSupplierCode === ''
        ) {
          this.$toast({
            content: this.$t('调入供应商必填'),
            type: 'warning'
          })
        } else {
          let obj = {
            buyerEnterpriseId: codeArr.buyerEnterpriseId,
            createType: 2,
            siteCode: codeArr.siteCode,
            buyerOrgCode: codeArr.buyerOrgCode,
            isOutSale: codeArr.isOutSale,
            supplierCode: codeArr.supplierCode
          }
          this.uploadParams = obj
          this.handleImport(true)
          return
        }
      } else if (e.toolbar.id === 'Delete') {
        // console.log(
        //   this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef
        //     .deleteRecord
        // );
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
      }
    },

    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
    },
    handleClickCellTitle(e) {
      console.log('handleClickCellTitle', e)
    },
    actionComplete(args) {
      console.log('actionComplete', args)
    },
    actionBegin(args) {
      console.log('actionBegin', args)
      // const { index } = args;
      if (args.requestType === 'sorting') {
        // if (this.allowSubmit == false) {
        args.cancel = true
        // }
      }
      // args.index = args?.rowIndex;

      if (args.requestType === 'save') {
        if (args.data.itemCode === null && this.beginData !== null) {
          args.data.itemCode = this.beginData.itemCode
        }
        // if (this.saveList !== null) {
        // console.log(this.saveList);
        // args.data.warehouseCode = this.saveList.itemInfo.warehouseCode;
        args.data.buyerGroupName = this.selectedOtherInfo.buyerGroupName
        args.data.buyerGroupCode = this.selectedOtherInfo.buyerGroupCode
        args.data.basicUnitCode = this.selectedOtherInfo.basicUnitCode
        args.data.basicUnitName = this.selectedOtherInfo.basicUnitName

        console.log(args.data)
        // }
        // if (
        //   Number(args.data.allocationQuantity) > Number(args.data.stockQuantity)
        // ) {
        //   this.$toast({
        //     content: "调出数量不可超过库存现有量!",
        //     type: "warning",
        //   });
        // }
      }

      if (args.requestType == 'add' || args.requestType == 'beginEdit') {
        this.beginData = args.rowData

        args.data.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
        this.isEditStatus = true
        // this.nowEditRowFlag = args.data.addId;

        console.log(args.data)
      }
      if (args.requestType == 'refresh') {
        this.isEditStatus = false
      }
    },

    addRow(row) {
      console.log('addRow', row)
    }
  },
  deactivated() {
    this.pageConfig[0].grid.dataSource = []
    sessionStorage.removeItem('order')
  }
}
</script>
<style lang="scss" scoped>
// /deep/ .top-info {
//   margin-top: 20px;
// }
.bottom-tables {
  height: 100%;
}
// .addPicking {
//   background: #fff;
//   display: flex;
//   flex-direction: column;
//   .template-height {
//     height: auto;
//     flex: 1;
//     max-height: calc(100% - 270px);
//     /deep/ .e-gridcontent {
//       height: 100%;
//     }
//     /deep/ .e-gridcontent {
//       height: 100%;
//     }
//   }
// }
</style>
