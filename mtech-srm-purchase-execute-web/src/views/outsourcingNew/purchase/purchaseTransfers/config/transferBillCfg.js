import { i18n } from '@/main.js'
import Select from '../components/Select.vue'
import InputView from '../components/InputView'
import Input from '../components/Input'
import InputNumber from '../components/InputNumber'
import Vue from 'vue'
const todoListToolBar = {
  useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
  tools: [
    // ["Add", "Delete"],
    [
      'add',
      'delete',
      {
        id: 'Import',
        icon: 'icon_solid_Import',
        title: i18n.t('导入')
      }
    ],
    []
  ]
}

const todoListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'addId',
    headerText: 'addId',
    width: 0,
    visible: false,
    allowEditing: false
  },
  {
    field: 'itemCode',
    width: '250',
    headerText: i18n.t('物料编码'),

    allowEditing: true,
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料编号')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料描述'),
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料描述')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'planGroupName',
    headerText: i18n.t('计划组'), // new
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'buyerGroupName',
    headerText: i18n.t('采购组'), // new
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'allocationQuantity',
    headerText: i18n.t('调出数量'),
    editTemplate: () => {
      return { template: InputNumber }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('调出数量')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'batch',
    headerText: '批次/卷号',
    editTemplate: () => {
      return { template: Input }
    }
  },

  {
    field: 'maxAllocationQuantity',
    // maxAllocationQuantity
    headerText: i18n.t('可调拨数量'),
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'stockQuantity',
    //stockQuantity
    headerText: i18n.t('库存现有量'),
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'packageMinQuantity', // new
    headerText: i18n.t('最小包装量'),
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'basicUnitName',
    headerText: i18n.t('基本单位'), // new
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'stockUnitCode',
    headerText: i18n.t('库存单位'),
    editTemplate: () => {
      return { template: InputView }
    }
  },
  // {
  //   field: "", // 只是界面显示
  //   headerText: i18n.t("最小包装量"),
  // },

  {
    field: 'remark',
    headerText: i18n.t('行备注'),
    editTemplate: () => {
      return { template: Input }
    }
  }
]

//status  定点推荐主表状态 0待处理 3已处理 4已退回
export const pageConfig = [
  {
    title: i18n.t('物料信息'),
    // useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    toolbar: todoListToolBar,

    grid: {
      height: 400,
      allowPaging: false,
      frozenColumns: 0,
      editSettings: {
        allowEditing: true,
        allowAdding: true,
        allowDeleting: true,
        mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
        showConfirmDialog: false,
        // showDeleteConfirmDialog: true,
        newRowPosition: 'Bottom'
      },
      columnData: todoListColumnData,
      dataSource: []
    }
  }
]

export const editSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: true,
  newRowPosition: 'Bottom'
}
