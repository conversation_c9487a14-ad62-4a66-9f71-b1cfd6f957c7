<template>
  <div>
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      style="display: none"
    ></mt-input>
    <mt-inputNumber
      v-model="data[data.column.field]"
      :min="0"
      :step="1"
      precision="3"
      :show-clear-button="false"
      @input="numberChange"
    ></mt-inputNumber>
  </div>
</template>
<script>
// var bigDecimal = require("js-big-decimal");
export default {
  data() {
    return {
      data: {},
      stockQuantity: 0
    }
  },
  mounted() {
    this.deliveryTotalQuantit = this.data.deliveryTotalQuantity //送货总数量
    if (this.data.column.field === 'allocationQuantity') {
      // this.stockQuantity = this.data.stockQuantity;
      // this.$bus.$on("stockQuantityChange", (val) => {
      //   this.stockQuantity = val;
      //   this.stockQuantityType = false;
      //   console.log(this.stockQuantity);
      // }); //接受的单位
    }
  },
  methods: {
    numberChange() {}
  }
}
</script>
