<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="middle-blank">
        <div style="display: flex">
          <div class="infos mr20">{{ $t('新建') }}</div>
          <div class="infos mr20">{{ $t('制单人：') }}{{ getuserInfo() }}</div>
          <div class="infos mr20">{{ $t('制单日期：') }}{{ getCurrentTime() }}</div>
        </div>
      </div>
      <!-- 右侧各种操作按钮 -->
      <mt-button css-class="e-flat invite-btn" :is-primary="true" @click="goBack">{{
        $t('返回')
      }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" @click="submit">{{ $t('保存') }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" @click="resoveClick">{{
        $t('提交')
      }}</mt-button>
      <div class="sort-box" @click="expandChange">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="topInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="buyerEnterpriseId" :label="$t('公司')">
          <mt-select
            ref="businessRef"
            v-model="topInfo.buyerEnterpriseId"
            @change="companyClick"
            :data-source="companyList"
            :allow-filtering="true"
            :filtering="getSourceCompany1"
            :fields="{ text: 'name', value: 'code' }"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('工厂')" prop="siteCode">
          <debounce-filter-select
            v-model="topInfo.siteCode"
            :request="getSiteCodeList"
            :value-template="siteCodeValueTemplate"
            :data-source="siteOptions"
            :is-active="true"
            :fields="{ text: 'theCodeName', value: 'siteCode' }"
            @change="siteOrgClick"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
          ></debounce-filter-select>
        </mt-form-item>
        <mt-form-item :label="$t('调出供应商')">
          <debounce-filter-select
            v-model="topInfo.outSupplierCode"
            :request="getOutMaterialSupplier"
            :data-source="outMaterialList"
            :value-template="supplierCodeValueTemplate"
            :fields="{ text: 'theCodeName', value: 'supplierCode' }"
            @change="materialClick($event, 'out')"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
          ></debounce-filter-select>
        </mt-form-item>
        <mt-form-item :label="$t('调入供应商')" prop="inSupplierCode">
          <debounce-filter-select
            v-model="topInfo.inSupplierCode"
            :request="getInMaterialSupplier"
            :data-source="inMaterialList"
            :value-template="supplierCodeValueTemplate"
            :fields="{ text: 'theCodeName', value: 'supplierCode' }"
            @change="materialClick($event, 'in')"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
          ></debounce-filter-select>
        </mt-form-item>
        <mt-form-item :label="$t('送货地址')">
          <mt-select
            :show-clear-button="true"
            v-model="topInfo.inSupplierAddress"
            :open-dispatch-change="false"
            :allow-filtering="true"
            :filtering="getWarehouseCodeOptions"
            :data-source="cancelList"
            :fields="{
              text: 'consigneeAddress',
              value: 'consigneeAddress'
            }"
            :placeholder="$t('请选择')"
            @change="inSupplierAddressChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('是否销售委外')" prop="isOutSale">
          <mt-select
            v-model="topInfo.isOutSale"
            :data-source="radioData"
            @change="changeOutSaleType"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input v-model="topInfo.remark"></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { Query } from '@syncfusion/ej2-data'
import { maxPageSize } from '@/utils/constant'
import Vue from 'vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import { cloneDeep } from 'lodash'

const siteCodeSelectTemplate = () => {
  return {
    template: Vue.component('siteCodeSelectTemplate', {
      template: `
    <div>
      <div>{{data.siteCode}}-{{data.siteName}}</div>
    </div>`,
      data() {
        return { data: {} }
      }
    })
  }
}
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      supplierCodeValueTemplate: codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      }),
      isDisabled: this.$route.path.includes('gc') ? true : false,
      siteCodeValueTemplate: siteCodeSelectTemplate, // 工厂
      radioData: [
        {
          text: this.$t('否'),
          value: '0'
        },
        {
          text: this.$t('是'),
          value: '1'
        }
      ],
      cancelList: [],
      outMaterialList: [],
      inMaterialList: [],
      customerEnterpriseId: '',
      copyTopInfo: {},
      topInfo: {
        allocationOrderCode: '',
        buyerCode: '',
        buyerOrgId: '',
        allocationSiteCode: '',
        allocationSiteName: '',
        buyerEnterpriseId: '',
        buyerName: '',
        buyerOrgCode: '',
        buyerOrgName: '',

        quantity: '',
        itemName: '',
        itemCode: '',
        // buyerTenantId: 0,
        buyerTenantId: '',
        orderItemNo: '',
        orderCode: '',
        orderId: '',
        inSupplierAddress: '',
        enterpriseId: '',
        id: '',

        inSupplierCode: null,
        inSupplierId: '',
        inSupplierName: '',
        isOutSale: this.$route.path.includes('gc') ? '1' : '',
        remark: '',
        siteCode: '',
        siteId: '',
        siteName: '',
        outSupplierCode: null,
        outSupplierId: '',
        outSupplierName: ''
      },
      companyList: [],
      addForm: {},
      radioData1: [
        {
          label: this.$t('是'),
          value: '1'
        },
        {
          label: this.$t('否'),
          value: '0'
        }
      ],
      isDisabled2: false,
      site: '',
      isExpand: true,
      rules: {
        siteCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        itemCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        orderCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        buyerEnterpriseId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        isOutDirect: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        inSupplierCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        outSupplierCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],

        isOutSale: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        warehouseCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        allocationSiteCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }]
      },
      expedited: '1', //待领料明细or手工创建
      siteOptions: [],

      userInfo: JSON.parse(sessionStorage.getItem('userInfo')),
      siteTenantList: [],
      edit: ''
    }
  },
  mounted() {
    this.copyTopInfo = cloneDeep(this.topInfo)
    // 获取公司下拉数据
    this.getSourceCompany()

    // this.filterCompany = utils.debounce(this.filterCompany, 1000)
  },
  methods: {
    getTopInfo() {
      return this.topInfo
    },
    stroageTopInfo() {
      // 由于在物料选择弹窗中还要使用
      sessionStorage.setItem('codeArr', JSON.stringify(this.topInfo))
    },
    // 获取主数据-工厂
    getSiteCodeList(args, companyId) {
      const { text } = args
      const params = {
        organizationId: companyId || this.topInfo.id,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
          }
        })
        .catch(() => {})
    },
    // 获取送货地址
    getWarehouseCodeOptions(val) {
      let obj = {
        supplierCode: val ? val : this.topInfo.inSupplierCode
      }
      this.$API.outsourcingNew.InSupplierGetAddressList(obj).then((res) => {
        this.cancelList = res.data
      })
    },
    resoveClick() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('resolve', this.topInfo)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 供应商点击事件
    materialClick(info, type) {
      this.topInfo[type + 'SupplierCode'] = info.itemData?.supplierCode
      this.topInfo[type + 'SupplierId'] = info.itemData?.supplierTenantId
      this.topInfo[type + 'SupplierName'] = info.itemData?.supplierName
      if (type === 'in' && info.itemData?.supplierCode) {
        this.getWarehouseCodeOptions(info.itemData?.supplierCode)
      } else {
        this.topInfo.inSupplierAddress = null
      }
      this.stroageTopInfo()
    },
    getuserInfo() {
      if (this.userInfo?.userName) {
        return this.userInfo?.userName
      } else {
        return ''
      }
    },
    // 当前日期
    getCurrentTime() {
      let yy = new Date().getFullYear()
      let mm = new Date().getMonth() + 1
      let dd = new Date().getDate()
      let hh = new Date().getHours()
      let mf =
        new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes()
      let ss =
        new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds()
      return yy + '/' + mm + '/' + dd + ' ' + hh + ':' + mf + ':' + ss
    },
    // 工厂点击
    siteOrgClick(e) {
      console.log(e.itemData)
      this.topInfo.buyerOrgId = e.itemData?.organizationId
      this.topInfo.siteCode = e.itemData?.siteCode
      this.topInfo.siteName = e.itemData?.siteName
      this.stroageTopInfo()
    },
    warehouseClick(val) {
      console.log(val)

      this.topInfo.warehouseCode = val.itemData?.siteAddress

      this.cancelList = []

      let obj = {
        enterpriseId: this.customerEnterpriseId,
        params: {
          page: {
            size: maxPageSize,
            current: 1
          },
          condition: 'and',
          defaultRules: [
            {
              // 工厂
              field: 'siteCode',
              operator: 'equal',
              value: this.topInfo.siteCode
            },
            {
              // 交货库存地点
              field: 'siteAddress',
              operator: 'equal',
              value: this.topInfo.warehouseCode
            }
          ]
        }
      }
      this.$API.receiptAndDelivery.postSiteTenantExtendQueryByEnterpriseId(obj).then((res) => {
        this.cancelList = res.data.records
      })
    },

    // 获取 调入供应商
    getMaterialList(val, companyCode, type) {
      let obj = {
        commonCode: companyCode || this.topInfo.buyerCode,
        fuzzyParam: val
      }
      this.$API.outsourcingNew.companyCodeGetSupplierList(obj).then((res) => {
        const list = res?.data || []
        const arr = addCodeNameKeyInList({
          firstKey: 'supplierCode',
          secondKey: 'supplierName',
          list
        })
        this.topInfo[type + 'SupplierCode'] = null
        this.topInfo[type + 'SupplierId'] = null
        this.topInfo[type + 'SupplierName'] = null
        if (type === 'in') {
          this.inMaterialList = arr
        } else {
          this.outMaterialList = arr
        }
      })
    },
    getInMaterialSupplier(e) {
      this.getMaterialList(e.text, this.topInfo.buyerCode, 'in')
    },
    getOutMaterialSupplier(e) {
      this.getMaterialList(e.text, this.topInfo.buyerCode, 'out')
    },
    submit() {
      console.log(this.topInfo)
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('submitClick', this.topInfo)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    companyClick(info) {
      console.log(info)
      if (info.e !== null) {
        this.topInfo.quantity = ''
        this.topInfo.siteCode = ''
        this.topInfo.siteName = ''
        this.topInfo.buyerOrgCode = ''
      }
      this.stroageTopInfo()
      // this.$API.masterData.findInBuyingByCustomerCode({ customerCode: info.value }).then((res) => {
      //   this.topInfo.supplierId = res.data.supplierId
      //   this.supplier = res.data.supplierCode + res.data.supplierName
      //   this.topInfo.supplierCode = res.data.supplierCode
      //   this.topInfo.supplierName = res.data.supplierName
      //   this.topInfo.outSupplierCode = res.data.supplierCode
      //   this.topInfo.outSupplierName = res.data.supplierName
      // })
      this.customerEnterpriseId = info.itemData.enterpriseId
      this.topInfo.buyerOrgCode = info.itemData.orgCode
      this.topInfo.buyerOrgName = info.itemData.orgName
      this.topInfo.buyerCode = info.itemData.orgCode
      this.topInfo.buyerName = info.itemData.orgName
      this.topInfo.buyerTenantId = info.itemData.orgCode
      this.topInfo.enterpriseId = info.itemData.enterpriseId
      const companyCode = info.itemData.orgCode
      this.topInfo.id = info.itemData.id
      // this.getSourceSitesInVendor({})
      this.getSiteCodeList({ text: '' }, info.itemData.id)
      this.getMaterialList('', companyCode, 'in')
      this.getMaterialList('', companyCode, 'out')
    },
    getSourceCompany() {
      let obj = {
        fuzzyNameOrCode: ''
      }

      this.$API.outsourcingNew.queryCustomCompanyList(obj).then((res) => {
        res.data.forEach((item) => {
          item.name = item.orgCode + '-' + item.orgName
          item.code = item.enterpriseId
        })
        this.companyList = res.data
      })
    },
    getSourceCompany1(e) {
      var searchData = this.companyList
      // this.purOrderQueryOrder(e.text);

      // load overall data when search key empty.
      if (e.text == '') e.updateData(searchData)
      else {
        let query = new Query().select(['name', 'code', 'orgCode', 'orgName', 'enterpriseId', 'id'])
        // change the type of filtering
        query = e.text !== '' ? query.where('name', 'contains', e.text, true) : query
        console.log(query)
        e.updateData(searchData, query)
        console.log(searchData)
      }
    },
    formFun(rule, value, callback) {
      console.log(rule, value, callback)
    },

    whitch(e) {
      console.log(e)
      if (e === '0') {
        this.topInfo.materialSupplierCode = ''
        this.topInfo.materialSupplierName = ''
      }
    },
    inSupplierAddressChange(val) {
      this.topInfo.inSupplierAddressName = val.itemData.consigneeAddress
      this.stroageTopInfo()
    },
    // 获取主数据-工厂
    // getSourceSitesInVendor(args) {
    //   const { updateData } = args
    //   const params = {
    //     paramObj: this.topInfo.buyerCode
    //     // enterpriseId: this.customerEnterpriseId,
    //     // fuzzyParam: text,
    //     // dataLimit: 10000,
    //   }
    //   this.$API.masterData
    //     .getSiteListSupplier(params)
    //     .then((res) => {
    //       if (res) {
    //         const list = res?.data || []
    //         this.siteOptions = addCodeNameKeyInList({
    //           firstKey: 'siteCode',
    //           secondKey: 'siteName',
    //           list
    //         })
    //         if (updateData) {
    //           this.$nextTick(() => {
    //             updateData(this.siteOptions)
    //           })
    //         }
    //       }
    //     })
    //     .catch(() => {})
    // },
    goBack() {
      // this.$router.go(-1)
      this.$router.push({
        path: '/purchase-execute/new-purchase-transfers'
      })
    },
    changeOutSaleType(info) {
      this.topInfo.isOutSale = info.value
      this.stroageTopInfo()
    },
    expandChange() {
      this.isExpand = !this.isExpand
      // this.$refs.ruleForm.clearValidate();
    },
    onchang(e) {
      console.log('onchang', e, this.expedited)
    },

    // 获取其他数据 code、name
    getOtherInfo(params) {
      // 业务类型、申请人、公司、申请部门、采购组织
      if (this.topInfo.siteId && this.$refs.businessRef) {
        let _data = this.$refs.businessRef.ejsRef.getDataByValue(this.topInfo.siteId)
        // console.log("params", _data);
        for (let key in _data) {
          params[key] = _data[key]
        }
        if (!_data) return
      }
      console.log('params', params)
      return params
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin: 20px 20px 20px 0;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .itemcon {
      display: flex;
      align-items: center;
    }
    .item {
      margin-right: 20px;
    }
    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      // &.more-width {
      //   // width: 450px;
      // }

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
