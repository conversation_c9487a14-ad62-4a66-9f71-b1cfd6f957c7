<template>
  <div>
    <div class="in-cell">
      <debounce-filter-select
        :id="data.column.field"
        v-model="data[data.column.field]"
        :request="postChange"
        :data-source="dataSource"
        :fields="{ text: 'codeAndName', value: 'value' }"
        :placeholder="placeholder"
        @change="selectChange"
        :open-dispatch-change="true"
        :disabled="isDisabled"
        :allow-filtering="true"
      ></debounce-filter-select>
      <mt-icon style="width: 20px" name="icon_input_search" @click.native="showDialog"></mt-icon>
    </div>
    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="title"
      :buttons="buttons"
      @close="handleClose"
    >
      <div class="full-height">
        <mt-template-page
          ref="templateRef"
          class="template-height has-page"
          :hidden-tabs="true"
          :template-config="pageConfig"
          @recordDoubleClick="recordDoubleClick"
        ></mt-template-page>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
// import { utils } from "@mtech-common/utils";
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      title: this.$t('请选择'),
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          toolbar: [],
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: 'a2b7191a-eec1-4bec-83d3-9bbb4b5f8574',
          grid: {
            // height: 352,
            // allowPaging: true,
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: [
              {
                width: '150',
                field: 'itemCode',
                headerText: this.$t('物料编号')
              },
              {
                width: '150',
                field: 'itemName',
                headerText: this.$t('物料名称')
              }
              // {
              //   width: "150",
              //   field: "itemUnitDescription",
              //   headerText: this.$t("单位"),
              // },
              // {
              //   width: "150",
              //   field: "purchaseGroupName",
              //   headerText: this.$t("采购组"),
              // },
              // {
              //   width: "150",
              //   field: "batchCode",
              //   headerText: this.$t("批次号"),
              // },
            ],
            asyncConfig: {},
            dataSource: []
          }
        }
      ],
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      purchase: '', //采购组code
      isDisabled: false,
      codeArr: JSON.parse(sessionStorage.getItem('codeArr')) // 工厂下拉相关数据
    }
  },
  mounted() {
    if (this.data.column.field === 'itemCode') {
      //物料下拉
      this.data.itemCode === null
        ? this.getCategoryItem('')
        : this.getCategoryItem(this.data.itemCode)

      this.getCategoryItem(this.data.itemCode)
    }

    if (this.data.column.field === 'warehouseName') {
      this.$bus.$on('warehouseChange', (val) => {
        console.log(val)
        val.forEach((item) => {
          item.name = item.warehouseCode + item.warehouseName // 	客户名称
          item.value = item.warehouseName == null ? '' : item.warehouseName // 	客户名称
        })
        this.dataSource = val || []
        this.data.warehouseName = val[0].warehouseName
        // this.data.warehouseCode = val[0].warehouseCode;

        this.fields = { text: 'name', value: 'value' }
      })
    }
    if (this.data.column.field === 'siteName') {
      //工厂下拉
      this.findOrgSiteInfo()
    }
  },
  methods: {
    recordDoubleClick(args) {
      this.selectChange({ itemData: args.rowData })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    confirm(e, records) {
      if (!records || records.length <= 0) {
        records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      }
      if (records.length <= 0) return
      this.selectChange({ itemData: records[0] })
    },
    showDialog() {
      this.pageConfig[0].grid.asyncConfig = {
        url: `masterDataManagement/auth/item-org-rel/pur-paged-query`,
        recordsPosition: 'data.records',
        condition: 'and',
        page: { current: 1, size: 20 },
        params: {
          organizationCode: this.codeArr?.siteCode ?? ''
        }
      }
      this.$refs.dialog.ejsRef.show()
    },
    // 模糊
    postChange(e) {
      if (this.data.column.field === 'itemCode') {
        this.getCategoryItem(e.text)
      }
      //   let obj = {
      //     currentPage: 1,
      //     keyword: "",
      //     pageSize: 20,
      //   };
      //   obj.keyword = e.text;
      //   this.$API.masterData.getItemByKeyword(obj).then((res) => {
      //     res.data.records.forEach((item) => {
      //       item.name = item.itemCode; // 	客户名称
      //       item.value = item.itemCode; // 	客户名称
      //     });
      //     this.dataSource = res.data.records || [];
      //   });
    },
    findOrgSiteInfo() {
      //工厂下拉
      this.$API.masterData.findOrgSiteInfo().then((res) => {
        res.data.forEach((item) => {
          item.name = item.siteName // 	客户名称
          item.value = item.siteCode // 	客户名称
        })
        this.dataSource =
          res.data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.siteCode} - ${i.siteName}`
            }
          }) || []
        // this.fields = { text: "siteName", value: "siteCode" };
      })
    },
    getCategoryItem(e) {
      //物料下拉
      let obj = {
        page: {
          current: 1,
          size: 100
        },
        rules: [
          {
            field: 'itemCode',
            operator: 'likeright',
            value: e
          }
        ],
        organizationCode: this.codeArr?.siteCode ?? ''
      }
      this.$API.outsourcingNew.getitemCodeList(obj).then((res) => {
        res.data.records.forEach((item) => {
          item.name = item.itemCode // 	客户名称
          item.value = item.itemCode // 	客户名称
        })
        this.dataSource =
          res.data.records.map((i) => {
            return {
              ...i,
              codeAndName: `${i.itemCode} - ${i.itemName}`
            }
          }) || []
      })
    },
    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      if (this.data.column.field === 'itemCode') {
        this.data[this.data.column.field] = val.itemData?.itemCode
        this.postChange({ text: val.itemData?.itemCode })
        this.$bus.$emit('itemNameChange', val.itemData?.itemName) //传给物料名称
        let obj = {
          buyerEnterpriseId: this.codeArr.buyerEnterpriseId,
          createType: 2,
          itemCode: val.itemData.itemCode,
          id: this.data?.id,
          siteCode: this.codeArr.siteCode,
          buyerOrgCode: this.codeArr.buyerOrgCode,
          isOutSale: this.codeArr.isOutSale,
          supplierCode: this.codeArr.outSupplierCode
        }
        this.$API.outsourcingNew.OutQuerySapOutDemand(obj).then((res) => {
          this.$bus.$emit('planGroupNameChange', res.data[0].planGroupName) // 计划组
          this.$bus.$emit('buyerOrgNameChange', {
            buyerGroupName: res.data[0].buyerGroupName,
            buyerGroupCode: res.data[0].buyerGroupCode
          }) // 采购组
          this.$bus.$emit('stockUnitCodeChange', res.data[0].stockUnitCode) //传给单位
          this.$bus.$emit('roundingOffChange', res.data[0].roundingOff) //传给最小包装量
          this.$bus.$emit('basicUnitCodeChange', {
            basicUnitCode: res.data[0].basicUnitCode,
            basicUnitName: res.data[0].basicUnitName
          }) //传给单位
          this.$bus.$emit('maxDemandQuantityChange', res.data[0].maxReceiveQuantity) //传给可调拨数量
          this.$bus.$emit('supplierStockChange', res.data[0].supplierStock) //传给库存现有
        })

        // this.$bus.$emit("stockUnitChange", val.itemData.unitCode); //传给单位
        // this.$parent.$emit("selectedChanged", {
        //   fieldCode: "itemCode",
        //   itemInfo: {
        //     stockUnit: val.itemData.unitCode,
        //     stockUnitName: val.itemData.unitName,
        //     itemCode: val.itemData.itemCode,
        //     itemName: val.itemData.itemName,
        //   },
        // });
        // let params = {
        //   buyerEnterpriseId: "",
        //   itemCode: "",
        //   nature: "1",
        //   quantity: "",
        //   orderQuantity: "",
        //   siteCode: "",
        //   buyerOrgCode: this.order.buyerOrgCode,

        //   supplierCode: "",
        //   // warehouseCode: "",
        // };
        // params.itemCode = val.itemData.itemCode;
        // params.quantity = val.itemData.quantity;
        // params.orderQuantity = this.order.quantity;

        // params.supplierCode = this.order.outSupplierCode;
        // params.siteCode = this.order.siteCode;
        // params.buyerEnterpriseId = this.order.buyerEnterpriseId;

        // this.$API.outsourcing.supplierQueryStock(params).then((r) => {
        //   this.$bus.$emit("batchChange", r.data);
        // });
        // this.$API.outsourcing.queryWmsStock(params).then((r) => {
        //   if (r.data.length > 0) {
        //     this.$bus.$emit("warehouseChange", r.data);
        //   }
        // });
        this.handleClose()
      }
      // if (this.data.column.field === "batch") {
      //   this.$bus.$emit("stockQuantityChange", val.itemData.qty); //传给库存

      //   this.$bus.$emit(
      //     "maxAllocationQuantityChange",
      //     val.itemData.maxCreateQty
      //   ); //传给可退货数量

      //   this.$parent.$emit("selectedChanged", {
      //     fieldCode: "batch",
      //     itemInfo: {
      //       batch: val.itemData.batch,

      //       basicUnit: val.itemData.unitName,
      //       orderItemNo: val.itemData.lineNo,
      //       // itemId: val.itemData.id,
      //     },
      //   });
      // }
      // if (this.data.column.field === "warehouseName") {
      //   console.log(val);
      //   this.$bus.$emit("batchChange", val.itemData);
      // }
    }
  },
  deactivated() {
    this.$bus.$off('itemNameChange')
    this.$bus.$off('planGroupNameChange')
    this.$bus.$off('buyerOrgNameChange')
    this.$bus.$off('stockUnitCodeChange')
    this.$bus.$off('roundingOffChange')
    this.$bus.$off('basicUnitCodeChange')
  }
}
</script>

<style scoped lang="scss">
.in-cell {
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;

  /deep/ .mt-select {
    .e-input-group-icon,
    .e-ddl-icon,
    .e-search-icon {
      margin-right: 20px;
    }
  }
  > .mt-icons {
    margin-left: 5px;
    cursor: pointer;
    position: absolute;
    top: 8px;
    right: 0;
  }
}
</style>
