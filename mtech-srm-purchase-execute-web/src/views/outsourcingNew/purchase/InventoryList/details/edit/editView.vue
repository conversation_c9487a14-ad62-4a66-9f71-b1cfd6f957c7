<template>
  <div>
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      style="display: none"
    ></mt-input>
    <mt-input v-model="nowText" :disabled="true"></mt-input>
  </div>
</template>
<script>
export default {
  data() {
    return {
      options: [],
      nowText: ''
    }
  },
  mounted() {
    this.options = this.data.column.options
    this.nowText = this.options.find((item) => {
      return item.value == this.data[this.data.column.field]
    })?.text
  }
}
</script>
