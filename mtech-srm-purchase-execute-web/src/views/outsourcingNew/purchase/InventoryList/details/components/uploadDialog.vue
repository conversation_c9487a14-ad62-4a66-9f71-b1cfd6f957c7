<template>
  <div class="upload-wrap">
    <mt-dialog ref="dialog" css-class="create-proj-dialog" :buttons="buttons" :header="header">
      <upload :multiple="false"></upload>
    </mt-dialog>
  </div>
</template>
<script>
import upload from './upload.vue'
import Bus from './config/bus'
export default {
  components: {
    upload
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      signForm: {
        type: '2'
      },
      files: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    Bus.$on('getFileData', (d) => {
      this.getFileData(d)
    })
  },
  methods: {
    confirm() {
      this.save()
    },
    cancel() {
      this.$emit('cancel-function')
    },

    save() {
      if (this.files && this.files.length > 0) {
        this.$emit('confirm-function', this.files)
      } else {
        this.$toast({
          content: this.$t('请上传文件'),
          type: 'warning'
        })
      }
    },
    getFileData(_files) {
      this.files = _files
    }
  },
  created() {}
}
</script>
<style lang="scss" scoped></style>
