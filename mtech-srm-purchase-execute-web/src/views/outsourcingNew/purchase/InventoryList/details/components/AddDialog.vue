<template>
  <div>
    <mt-dialog
      ref="dialog"
      css-class="dialog-main"
      :buttons="buttons"
      :header="headers"
      @beforeClose="cancel"
    >
      <div class="dialog-content" v-show="!dialogShow">
        <mt-form class="dialog-mt-form" ref="dialogRef" :model="formObject" :rules="formRules">
          <mt-form-item prop="itemCode" :label="$t('物料编码')">
            <!-- <mt-input
              v-model="formObject.itemCode"
              float-label-type="Never"
              :disabled="true"
            ></mt-input> -->
            <mt-select
              v-model="formObject.itemCode"
              float-label-type="Never"
              :fields="{ text: 'itemCode', value: 'itemCode' }"
              :data-source="itemSource"
              @change="itemClick"
              :placeholder="$t('请选择物料编码')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="itemName" :label="$t('物料名称')">
            <mt-input
              :width="370"
              style="display: inline-block"
              v-model="formObject.itemName"
              float-label-type="Never"
              :disabled="true"
              :placeholder="$t('')"
            ></mt-input>
            <!-- <mt-icon
              class="allowed"
              style="width: 20px; margin-left: 10px"
              name="icon_input_search"
              @click.native="showDialog"
            ></mt-icon> -->
          </mt-form-item>
          <mt-form-item prop="inventoryQuantity" :label="$t('盘点数量')">
            <mt-input-number
              v-model="formObject.inventoryQuantity"
              :min="0"
              float-label-type="Never"
              :placeholder="$t('请输入物料名称')"
            ></mt-input-number>
          </mt-form-item>
          <mt-form-item prop="endProductStatus" :label="$t('是否成品')">
            <mt-select
              v-model="formObject.endProductStatus"
              float-label-type="Never"
              :data-source="endProductStatusData"
              :placeholder="$t('请选择是否成品')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="unit" :label="$t('单位')">
            <mt-select
              v-model="formObject.unit"
              float-label-type="Never"
              :data-source="unitSelect"
              :fields="{ text: 'unitName', value: 'unitCode' }"
              @change="changeUnitSelect"
              :placeholder="$t('请选择单位')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="remark" :label="$t('备注')">
            <mt-input
              v-model="formObject.remark"
              float-label-type="Never"
              :placeholder="$t('请输入备注')"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
      <mt-template-page
        v-show="dialogShow"
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="pageConfig"
        @recordDoubleClick="recordDoubleClick"
      >
      </mt-template-page>
    </mt-dialog>
  </div>
</template>
<script>
import { pageConfig } from './config/index'
export default {
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      itemSource: [],
      formObject: {
        itemCode: null, // 物料编码
        itemName: null, // 物料名称
        inventoryQuantity: null, //盘点数量
        endProductStatus: null, //是否成品
        unit: null, //单位
        remark: null //备注
      },
      //必填项
      formRules: {
        endProductStatus: [
          {
            required: true,
            message: this.$t('是否成品'),
            trigger: 'blur'
          }
        ],
        itemCode: [
          {
            required: true,
            message: this.$t('物料编码'),
            trigger: 'blur'
          }
        ],
        itemName: [
          {
            required: true,
            message: this.$t('物料名称'),
            trigger: 'blur'
          }
        ],
        inventoryQuantity: [
          {
            required: true,
            message: this.$t('盘点数量'),
            trigger: 'blur'
          }
        ],
        unit: [
          {
            required: true,
            message: this.$t('单位'),
            trigger: 'blur'
          }
        ]
      },
      endProductStatusData: [
        { text: this.$t('否'), value: 0 },
        { text: this.$t('是'), value: 1 }
      ],
      unitSelect: [], //单位
      weight: 1, //权重值判断那些下拉框可以编辑
      //--------------物料弹框
      headerTxt: this.$t('选择物料'),
      dialogShow: false,
      pageConfig: pageConfig,
      headers: ''
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    organizationId() {
      return this.modalData.organizationId
    },
    dataSource() {
      return this.modalData.dataSource
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.headers = this.header
    this.initialCallInterface()
  },
  methods: {
    //新增调用接口
    initialCallInterface() {
      this.getItemList()
      this.getUnitList()
    },
    itemClick(e) {
      this.formObject.itemName = e.itemData.itemName
      this.formObject.itemCode = e.itemData.itemCode
      // this.formObject.unit =
      //   e.itemData?.purchasingBasicInOrgResponse?.itemInfo?.baseMeasureUnitCode;
    },
    // 获取物料数据
    getItemList() {
      let params = {
        page: { current: 1, size: 20 },
        condition: 'and',
        rules: [
          {
            field: 'organizationCode',
            type: 'string',
            operator: 'contains',
            value: this.modalData.itemForm.siteCode
          }
        ]
      }
      this.$API.Inventory.getItemforSite({ ...params }).then((res) => {
        if (res.code === 200) {
          this.itemSource = res?.data?.records || []
        }
      })
    },
    // 获取单位列表数据
    getUnitList() {
      let pramas = {
        tenantId: '10000'
      }
      this.$API.Inventory.findByTenantId(pramas).then((res) => {
        this.unitSelect = res.data
      })
    },
    //单位change
    changeUnitSelect(e) {
      this.formObject.unitName = e.itemData.unitName
    },
    //编辑回选
    editCallInterface() {
      let JITdata = this.JITdata[0]
      this.itemSelect = [
        {
          itemName: JITdata.itemName,
          itemCode: JITdata.itemCode
        }
      ]
      this.formObject.id = JITdata.id
      this.formObject.organizationName = JITdata.organizationName
      this.formObject.organizationCode = JITdata.organizationCode
      this.formObject.siteName = JITdata.siteName
      this.formObject.siteCode = JITdata.siteCode
      this.formObject.itemCode = JITdata.itemCode
      this.formObject.itemName = JITdata.itemName
      this.formObject.itemIdentification = JITdata.itemIdentification
      this.weight = 0
      let parameter = {
        organizationId: JITdata.organizationId,
        itemQueryKeyword: JITdata.itemName
      }
      this.$API.material.itemfuzzyQuery(parameter).then((res) => {
        this.itemSelect = res.data
      })
    },
    //物料---弹框显示
    showDialog() {
      this.dialogShow = true
      this.headers = this.$t('请选择物料/品项编码')
      this.buttons = [
        {
          click: this.itemCancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.itemConfirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    },
    //物料--双击
    recordDoubleClick(args) {
      let itemArr = this.dataSource.filter((item) => {
        if (item.itemCode == args.rowData.itemCode) {
          return item
        }
      })
      if (itemArr.length == 0) {
        this.formObject.itemCode = args.rowData.itemCode
        this.formObject.itemName = args.rowData.itemName
        this.itemCancel()
      } else {
        this.$toast({
          content: this.$t('物料不能重复'),
          type: 'warning'
        })
        return
      }
    },
    //点击确认
    confirm() {
      let formObject = this.formObject
      let arr = this.dataSource.filter((item) => item.itemCode == formObject.itemCode)
      if (arr.length > 0) {
        this.$toast({
          content: this.$t('物料不能重复'),
          type: 'warning'
        })
        return
      }
      if (!formObject.itemCode || !formObject.itemName) {
        this.$toast({
          content: this.$t('物料不能为空'),
          type: 'warning'
        })
        return
      }
      if (!formObject.inventoryQuantity) {
        this.$toast({
          content: this.$t('盘点数量不能为空'),
          type: 'warning'
        })
        return
      }
      if (!formObject.inventoryQuantity) {
        this.$toast({
          content: this.$t('盘点数量不能为空'),
          type: 'warning'
        })
        return
      }
      if (![0, 1].includes(formObject.endProductStatus)) {
        this.$toast({
          content: this.$t('是否成品不能为空'),
          type: 'warning'
        })
        return
      }
      if (!formObject.unit) {
        this.$toast({
          content: this.$t('单位不能为空'),
          type: 'warning'
        })
        return
      }
      console.log(formObject)
      this.$emit('confirm-function', formObject)
    },
    //物料确认
    itemConfirm() {
      let _records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      let itemArr = this.dataSource.filter((item) => {
        if (item.itemCode == _records[0].itemCode) {
          return item
        }
      })
      if (itemArr.length == 0) {
        this.formObject.itemCode = _records[0].itemCode
        this.formObject.itemName = _records[0].itemName
        this.itemCancel()
      } else {
        this.$toast({
          content: this.$t('物料不能重复'),
          type: 'warning'
        })
        return
      }
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    },
    //物料取消
    itemCancel() {
      this.dialogShow = false
      this.newdialogheader()
    },
    newdialogheader() {
      this.headers = this.$t('新建')
      this.buttons = [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    padding: 0;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
          .mt-icons {
            cursor: pointer;
          }
        }
      }
    }
    .common-template-page {
      height: 100%;
      .e-gridcontent {
        max-height: 340px;
      }
      .mt-pagertemplate {
        overflow: inherit !important;
      }
    }
  }
}
</style>
