<!--盘点单列表-->
<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>
<script>
import { pageConfig } from './config/index'
import utils from '@/utils/utils'
export default {
  data() {
    return {
      pageConfig: pageConfig
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      const _selectRows = e.grid.getSelectedRecords()
      const _ids = _selectRows.map((item) => item.id)
      // 是否含有非草稿状态下
      const hasNoDraft = _selectRows.find((item) => item.status !== 0)
      if (_selectRows.length < 1 && (e.toolbar.id === 'submit' || e.toolbar.id === 'delete')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'submit') {
        this.handleClickToolBarSubmit(_selectRows)
      } else if (e.toolbar.id === 'Add') {
        // 创建盘点单
        this.handleClickToolBarAdd()
      } else if (e.toolbar.id === 'delete') {
        if (hasNoDraft && hasNoDraft.id) {
          this.$toast({
            content: this.$t('请选择草稿状态数据'),
            type: 'warning'
          })
          return
        }
        // 删除盘点单
        this.handleDelete(_ids)
      }
    },
    //行内点击
    handleClickCellTool(e) {
      const { data, tool } = e
      if (tool.id == 'submit') {
        //提交
        // this.handleClickCellToolSubmit(data);
      } else if (tool.id == 'editor') {
        //编辑
        this.handleEdit(data)
      } else if (tool.id === 'inactive') {
        //取消
        this.handleInactive(data)
      } else if (tool.id === 'delete') {
        this.handleDelete([data.id])
      }
    },

    handleClickToolBarSubmit(_selectRows) {
      if (_selectRows.length > 1) {
        this.$toast({ content: this.$t('只能选择一行'), type: 'warning' })
        return
      }
      let parameter = {
        inventoryCode: _selectRows[0].inventoryCode
      }
      this.$API.Inventory.contrast(parameter).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
      })
    },
    // 点击单元格 标题
    handleClickCellTitle(e) {
      if (e.field === 'inventoryCode') {
        this.$router.push({
          path: `new-purchase-inventory-detail`,
          key: utils.randomString(),
          query: { inventoryCode: e.data.inventoryCode, type: 'view' }
        })
      }
    },
    //头部--创建盘点单
    handleClickToolBarAdd() {
      this.$router.push({
        path: `new-purchase-inventory-detail`,
        key: utils.randomString(),
        query: { type: 'add' }
      })
    },
    // 取消操作
    handleInactive(data) {
      this.$API.Inventory.buyerInventoryInactive({ ids: [data.id] }).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      })
    },
    // 删除操作
    handleDelete(ids) {
      let params = {
        idList: ids,
        remark: ''
      }
      this.$API.Inventory.buyerInventoryDelete(params).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      })
    },
    // 编辑操作
    handleEdit(data) {
      this.$router.push(
        `new-purchase-inventory-detail?inventoryCode=${data.inventoryCode}&type=edit`
      )
    }
  }
}
</script>
<style lang="scss" scoped></style>
