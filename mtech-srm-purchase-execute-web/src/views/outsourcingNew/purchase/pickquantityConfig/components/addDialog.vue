<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="headerText"
    :buttons="buttons"
    @close="handleClose"
  >
    <mt-form ref="addFormRef" :model="addForm" :rules="addRules">
      <mt-form-item prop="settingType" :label="$t('类型')">
        <mt-select
          v-model="addForm.settingType"
          :data-source="typeList"
          :fields="{ text: 'text', value: 'value' }"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="siteCode" :label="$t('工厂')">
        <select-filter
          :width="390"
          :fields="{ text: 'labelShow', value: 'siteCode' }"
          :request-url="requestUrl"
          :request-key="requestKey"
          :init-val.sync="addForm.siteCode"
          :other-params="otherParams"
          :label-show-obj="labelShowObj"
          @handleChange="handleChange"
        ></select-filter>
      </mt-form-item>

      <mt-form-item
        prop="supplierCode"
        v-if="addForm.settingType === 1 || addForm.settingType === 2"
        :label="$t('加工商')"
      >
        <debounce-filter-select
          v-model="addForm.supplierCode"
          :request="getSupplier"
          :data-source="supplierOptions"
          :fields="{ text: 'theCodeName', value: 'supplierCode' }"
          :value-template="supplierCodeValueTemplate"
          :show-clear-button="true"
          :allow-filtering="true"
          @change="supplierCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="buyerOrgCode" v-if="addForm.settingType === 1" :label="$t('采购组')">
        <debounce-filter-select
          v-model="addForm.buyerOrgCode"
          :request="getBuyerOrgList"
          :data-source="buyerOrgOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'groupCode' }"
          :value-template="buyerOrgValueTemplate"
          @change="buyerOrgCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="stockStatus" :label="$t('可用库存数量限制')">
        <mt-switch v-model="addForm.stockStatus"></mt-switch>
      </mt-form-item>

      <mt-form-item prop="demandStatus" :label="$t('最大需求数量限制')">
        <mt-switch v-model="addForm.demandStatus"></mt-switch>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
export default {
  components: {
    DebounceFilterSelect
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      headerText: this.$t('新增'),
      supplierOptions: [],
      buyerOrgOptions: [],
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      supplierCodeValueTemplate: codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      }), // 供应商
      addForm: {
        buyerGroupCode: '',
        buyerGroupName: '',
        buyerOrgCode: '',
        buyerOrgName: '',
        demandStatus: 0,
        remark: '',
        settingType: '',
        siteCode: '',
        siteName: '',
        stockStatus: 0,
        supplierCode: '',
        supplierName: ''
      },
      typeList: [
        {
          text: this.$t('工厂+加工方+采购组（原材料）'),
          value: 1
        },
        {
          text: this.$t('工厂+加工方'),
          value: 2
        },
        {
          text: this.$t('工厂'),
          value: 3
        }
      ],
      buyerOrgValueTemplate: codeNameColumn({
        firstKey: 'groupCode',
        secondKey: 'groupName'
      }), // 采购组
      addRules: {
        siteCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        settingType: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        buyerOrgCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        stockStatus: [
          {
            required: true,
            message: this.$t('请选择是否限制可用库存数量'),
            trigger: 'blur'
          }
        ],
        demandStatus: [
          {
            required: true,
            message: this.$t('请选择是否限制最大需求数量'),
            trigger: 'blur'
          }
        ]
      },
      requestUrl: {
        pre: 'masterData',
        url: 'postSiteFuzzyQuery'
      },
      requestKey: 'fuzzyParam',
      otherParams: { dataLimit: 1000 },
      labelShowObj: { code: 'siteCode', name: 'siteName' }
    }
  },
  mounted() {
    console.log('我是弹窗')
    this.$refs.dialog.ejsRef.show()
    this.getBuyerOrgList({ text: undefined })
    this.getSupplier({ text: this.addForm.supplierCode })

    this.$nextTick(() => {
      this.$refs.addFormRef.resetFields()
      if (this.modalData) {
        this.headerText = this.modalData?.title
        if (this.modalData?.row) {
          this.addForm = {
            ...this.modalData.row,
            stockStatus: this.modalData.row.stockStatus ? true : false,
            demandStatus: this.modalData.row.demandStatus ? true : false
          }
          console.log(this.addForm)
        }
      }
    })
  },
  methods: {
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    cancel() {
      this.$emit('cancel-function')
    },
    supplierCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.addForm.supplierId = itemData.id
        this.addForm.supplierCode = itemData.supplierCode
        this.addForm.supplierName = itemData.supplierName
      } else {
        this.addForm.supplierId = ''
        this.addForm.supplierCode = ''
        this.addForm.supplierName = ''
      }
    },
    buyerOrgCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.addForm.buyerOrgId = itemData.id
        this.addForm.buyerOrgCode = itemData.groupCode
        this.addForm.buyerOrgName = itemData.groupName
      } else {
        this.addForm.buyerOrgId = ''
        this.addForm.buyerOrgCode = ''
        this.addForm.buyerOrgName = ''
      }
    },
    // 主数据 获取供应商
    getSupplier(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData
        .getSupplier(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.supplierOptions = addCodeNameKeyInList({
              firstKey: 'supplierCode',
              secondKey: 'supplierName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.supplierOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-采购组
    getBuyerOrgList(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        groupTypeCode: 'BG001CG'
      }
      this.$API.masterData
        .getbussinessGroup(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.buyerOrgOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.buyerOrgOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    confirm() {
      this.$refs.addFormRef.validate((valid) => {
        if (valid) {
          console.log(this.addForm)
          let params = {
            ...this.addForm,
            stockStatus: this.addForm.stockStatus ? 1 : 0, // 可用库存数量限制 1开启 0关闭
            demandStatus: this.addForm.demandStatus ? 1 : 0 // 最大需求数量限制 1开启 0关闭
          }
          this.$API.outsourcing.saveNewStockSetting(params).then(() => {
            this.$emit('confirm-function')
          })
        }
      })
    },

    handleChange(e) {
      this.addForm.siteId = e.itemData?.id
      this.addForm.siteName = e.itemData?.siteName
    }
  }
}
</script>

<style></style>
