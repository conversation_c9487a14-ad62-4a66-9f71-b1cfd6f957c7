<template>
  <!-- 送货单-列表-供方 -->
  <div class="full-height pt20">
    <mt-template-page
      :permission-obj="permissionObj"
      :template-config="pageConfig"
      @handleSelectTab="handleSelectTab"
    >
      <mt-template-page
        slot="slot-0"
        ref="templateRef"
        :template-config="pageConfig1"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
        @handleClickCellTool="handleClickCellTool"
      >
      </mt-template-page>

      <mt-template-page slot="slot-1" ref="template-1" :template-config="pageConfig2">
      </mt-template-page>
    </mt-template-page>
    <deliver
      @handleAddDialogShow="handleAddDialogShow"
      @resolveClick="resolveClick"
      ref="headerTop"
      v-if="deliveryShow"
      class="flex-keep"
    ></deliver>
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { columnData, columnData2 } from './config/index.js'
// import deliveryDialog from "./components/deliveryDialog";
export default {
  mounted() {},
  components: {
    deliver: require('./components/deliver.vue').default
  },
  data() {
    return {
      id: [],
      deliveryShow: false,
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0036' },
          { dataPermission: 'b', permissionCode: 'T_02_0037' }
        ]
      },
      pageConfig: [
        {
          title: this.$t('头视图'),
          dataPermission: 'a',
          permissionCode: 'T_02_0036' // 需要与permissionObj中的参数和权限code对应
        },
        {
          title: this.$t('明细视图'),
          dataPermission: 'b',
          permissionCode: 'T_02_0037' // 需要与permissionObj中的参数和权限code对应
        }
      ],
      userInfo: null,
      addDialogShow: false,
      currentTabIndex: 0,
      pageConfig1: [
        {
          useToolTemplate: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'accept1',
                  icon: 'icon_table_accept1',
                  title: this.$t('确认'),
                  permission: ['O_02_0605']
                },
                {
                  id: 'recall',
                  icon: 'icon_table_recall',
                  title: this.$t('退回'),
                  permission: ['O_02_0606']
                },
                {
                  id: 'restart',
                  icon: 'icon_table_restart',
                  title: this.$t('手工同步'),
                  permission: ['O_02_0687']
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          gridId: this.$tableUUID.outsourcing.purchasePicking.list,

          grid: {
            columnData: columnData,
            lineIndex: 1,
            autoWidthColumns: columnData.length + 1,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/outReceiveOrder/buyer/queryView`
              // 妥投 序列化 TODO 演示后删掉
            },
            frozenColumns: 1
          }
        }
      ],
      pageConfig2: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          gridId: this.$tableUUID.outsourcing.purchaseReturnCargo.details,

          grid: {
            columnData: columnData2,
            lineIndex: 1,
            autoWidthColumns: columnData2.length + 1,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/outReceiveOrder/buyer/queryDetailView`
            },
            frozenColumns: 1
          }
        }
      ],
      dialogData: null
    }
  },
  methods: {
    resolveClick(a) {
      this.resolvechange(this.id, a)
      this.deliveryShow = false
    },
    resolvechange(id, a) {
      let obj = {
        ids: id.toString(),
        rejectReason: a
      }
      this.$API.outsourcing
        .buyerNewReject(obj)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef.refreshCurrentGridData()
        })
    },
    // 确认
    certain(id) {
      let obj = {
        ids: id.toString()
      }
      console.log(obj)
      this.$API.outsourcing
        .buyerNewConfirm(obj)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef.refreshCurrentGridData()
        })
    },
    // 弹框
    handleDialogShow(flag) {
      this.deliveryShow = flag
    },
    handleSelectTab(e) {
      this.currentTabIndex = e
    },
    // 点击单元格 标题
    handleClickCellTitle(e) {
      // if (e.data.status == "1") {
      if (e.field === 'receiveOrderCode') {
        localStorage.setItem('purchaseListScope', JSON.stringify(e.data))
        this.$router.push(`purchase-detail-picking?receiveOrderCode=${e.data.receiveOrderCode}`)
      }

      // } else if (e.data.status == "0") {
      //   this.$router.push(`deliver-detail-supplier?id=${e.data.id}&type=no`);
      // }
    },
    handleClickCellTool(e) {
      console.log(e)
      if (e.tool.title === this.$t('确认')) {
        this.certain(e.data.id)
      }
      if (e.tool.title === this.$t('退回')) {
        this.id = []
        this.id.push(e.data.id)
        this.deliveryShow = true
      }
      if (e.tool.title === this.$t('手工同步')) {
        this.id = []
        this.restartClick(e.data.id)
      }
    },
    restartClick(id) {
      console.log(id)
      let pd = id.toString()
      let obj = { ids: pd }
      this.$API.outsourcing.buyerNewSyncWms(obj).then(() => {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    handleClickToolBar(e) {
      console.log(e)
      if (e.toolbar.id == 'new') {
        this.$router.push(`create-noOrder-supplier`)
        return
      }
      if (e.gridRef.getMtechGridRecords()?.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _status = [],
        _id = []
      let _syncStatus = []
      e.gridRef.getMtechGridRecords().map((item) => {
        _id.push(item.id), _status.push(item.status)
        _syncStatus.push(item.syncStatus)
        // _sourceStatus.push(item.sourceStatus),
        // _remainingQuantity.push(item.remainingQuantity);
      })
      this.id = _id
      console.log(_status)
      if (e.toolbar.id == 'accept1') {
        for (let i of _status) {
          if (i !== 1) {
            this.$toast({
              content: this.$t('只有状态在发货中才可以确认'),
              type: 'warning'
            })
            return
          }
        }
        this.certain(_id)
        return
      }

      if (e.toolbar.id == 'recall') {
        for (let i of _status) {
          if (i !== 1) {
            console.log(_status)
            this.$toast({
              content: this.$t('只有状态在待确认中才可以退回'),
              type: 'warning'
            })
            return
          }
        }
        this.deliveryShow = true
      }
      if (e.toolbar.id == 'restart') {
        console.log(_syncStatus)
        // for (let i of _syncStatus) {
        //   if (i === "1") {
        //     this.$toast({
        //       content: this.$t("该状态不可手工同步"),
        //       type: "warning",
        //     });

        //     return;
        //   }
        // }
        this.restartClick(_id)
      }
    }
  }
  // 消失
  // deactivated() {
  //   localStorage.removeItem("deliverDetailSupplier");
  // },
}
</script>

<style></style>
