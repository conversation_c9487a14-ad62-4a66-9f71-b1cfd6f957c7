import { timeNumberToDate } from '@/utils/utils'
import Vue from 'vue'
import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '280',
    field: 'receiveOrderCode',
    headerText: i18n.t('委外领料单号'),
    cellTools: []
  },
  // {
  //   width: "150",
  //   field: "deliveryType",
  //   headerText: i18n.t("状态"),
  //   valueConverter: {
  //     type: "map",
  //     map: [
  //       { value: 1, text: i18n.t("关联采购订单"), cssClass: "" },
  //       { value: 2, text: i18n.t("无采购订单"), cssClass: "" },
  //     ],
  //   },
  // },
  {
    width: '200',
    field: 'status',
    headerText: i18n.t('状态'),
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('退回'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('已确认'), cssClass: 'col-inactive' },
        { value: 4, text: i18n.t('已完成'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('已取消'), cssClass: 'col-inactive' }
      ]
    },
    cellTools: [
      {
        id: 'Proper',
        icon: 'icon_table_accept1',
        title: i18n.t('确认'),
        permission: ['O_02_0605'],
        visibleCondition: (data) => data['status'] === 1
      },
      {
        id: 'Proper',
        icon: 'icon_table_recall',
        title: i18n.t('退回'),
        permission: ['O_02_0606'],
        visibleCondition: (data) => data['status'] === 1
      },
      {
        id: 'restart',
        icon: 'icon_table_restart',
        title: i18n.t('手工同步'),
        permission: ['O_02_0687'],
        visibleCondition: (data) => data['status'] === 3
      }
    ]
  },
  {
    field: 'materialApproveUserName',
    headerText: i18n.t('供方确认人')
  },
  {
    field: 'materialApproveDate',
    headerText: i18n.t('供方确认时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'buyerApproveUserName',
    headerText: i18n.t('采方确认人')
  },
  {
    field: 'buyerApproveDate',
    headerText: i18n.t('采方确认时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: '0', text: i18n.t('未同步'), cssClass: '' },
        { value: '1', text: i18n.t('同步成功'), cssClass: '' },
        { value: '2', text: i18n.t('同步失败'), cssClass: '' },
        { value: '3', text: i18n.t('同步中'), cssClass: '' }
      ]
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: 300,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerName',
    headerText: i18n.t('公司'),
    width: 300,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerCode}}-{{data.buyerName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  // {
  //   width: "150",
  //   field: "supplierCode",
  //   headerText: i18n.t("领料供应商编码"),
  // },
  {
    width: '250',
    field: 'supplierCode',
    headerText: i18n.t('领料供应商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.supplierCode}}-{{data.supplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },

  {
    field: 'createTime',
    headerText: i18n.t('制单日期')
    // editTemplate: () => {
    //   return {
    //     template: "1212",
    //   };
    // },
    // template: timeDate("sendTime", true),
  },
  {
    field: 'createUserName',
    headerText: i18n.t('制单人')
  }
]
export const columnData2 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'receiveOrderCode',
    headerText: i18n.t('委外领料单号')
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('退回'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('已确认'), cssClass: 'col-inactive' },
        { value: 4, text: i18n.t('已完成'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('已取消'), cssClass: 'col-inactive' }
      ]
    }
  },
  {
    width: '150',
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },
  {
    width: '150',
    field: 'receiveQuantity',
    headerText: i18n.t('本次领料数量')
  },
  {
    width: '150',
    field: 'wmsReceiveQuantity',
    headerText: i18n.t('发料数量')
  },
  {
    width: '150',
    field: 'stockUnit',
    headerText: i18n.t('单位')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: 300,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerName',
    headerText: i18n.t('公司'),
    width: 300,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerCode}}-{{data.buyerName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '250',
    field: 'supplierCode',
    headerText: i18n.t('领料供应商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.supplierCode}}-{{data.supplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '250',
    field: 'warehouseCode',
    headerText: i18n.t('库存地点'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.warehouseCode}}-{{data.warehouseName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },

  // {
  //   width: "150",
  //   field: "buyerOrgName",
  //   headerText: i18n.t("采购组"),
  // },
  {
    field: 'createTime',
    headerText: i18n.t('制单日期')
  },
  // {
  //   width: "150",
  //   field: "receiveSupplierName",
  //   headerText: i18n.t("送货方式"), 暂无
  // },
  // {
  //   width: "150",
  //   field: "receiveSupplierName",
  //   headerText: i18n.t("收货方名称"),
  // },
  // {
  //   width: "150",
  //   field: "receiveSupplierCode",
  //   headerText: i18n.t("收货方编号"),
  // },
  {
    field: 'createUserName',
    headerText: i18n.t('制单人')
  }
]
