<template>
  <!-- 送货单-列表-供方 -->
  <div class="full-height pt20">
    <mt-template-page :template-config="pageConfig" @handleSelectTab="handleSelectTab">
      <mt-template-page
        slot="slot-0"
        ref="templateRef"
        :template-config="pageConfig1"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
        @handleClickCellTool="handleClickCellTool"
      >
      </mt-template-page>

      <mt-template-page slot="slot-1" ref="template-1" :template-config="pageConfig2">
      </mt-template-page>
    </mt-template-page>
  </div>
</template>

<script>
// import { BASE_TENANT } from "@/utils/constant";
import { columnData, columnData2 } from './config/index.js'
// import deliveryDialog from "./components/deliveryDialog";
export default {
  mounted() {},
  data() {
    return {
      id: [],
      deliveryShow: false,
      pageConfig: [{ title: this.$t('送货单列表') }, { title: this.$t('送货明细') }],
      userInfo: null,
      addDialogShow: false,
      currentTabIndex: 0,
      pageConfig1: [
        {
          useToolTemplate: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'MaintainIogisticsInformation',
                  icon: 'icon_table_MaintainIogisticsInformation',
                  title: this.$t('维护物流信息')
                  // permission: ["O_02_0122"],
                },
                {
                  id: 'AppointmentDelivery1',
                  icon: 'icon_table_AppointmentDelivery1',
                  title: this.$t('预约送货')
                  // permission: ["O_02_0122"],
                },
                {
                  id: 'print',
                  icon: 'icon_table_print',
                  title: this.$t('打印条码')
                  // permission: ["O_02_0122"],
                },
                {
                  id: 'cancel',
                  icon: 'icon_table_cancel',
                  title: this.$t('取消')
                  // permission: ["O_02_0122"],
                },

                {
                  id: 'new',
                  icon: 'icon_table_new',
                  title: this.$t('创建无订单送货单')
                  // permission: ["O_02_0122"],
                },
                {
                  id: 'loseeffectiveness',
                  icon: 'icon_solid_loseeffectiveness',
                  title: this.$t('补订单')
                  // permission: ["O_02_0122"],
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          grid: {
            columnData: columnData,
            lineIndex: 1,
            autoWidthColumns: columnData.length + 1,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/supplierOrderDelivery/query',
              // 妥投 序列化 TODO 演示后删掉
              serializeList: (list) => {
                if (list.length > 0) {
                  const isProperData = JSON.parse(localStorage.getItem('isProperData')) || []
                  list.forEach((item) => {
                    for (let i = 0; i < isProperData.length; i++) {
                      if (item.id == isProperData[i]) {
                        item.status = 99 // 妥投状态
                        break
                      }
                    }
                  })
                }
                return list
              }
            },
            frozenColumns: 1
          }
        }
      ],
      pageConfig2: [
        {
          grid: {
            columnData: columnData2,
            lineIndex: 1,
            autoWidthColumns: columnData2.length + 1,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/supplierOrderDeliveryItem/page'
            },
            frozenColumns: 1
          }
        }
      ],
      dialogData: null
    }
  },
  methods: {
    // 弹框
    handleDialogShow(flag) {
      this.deliveryShow = flag
    },
    handleSelectTab(e) {
      this.currentTabIndex = e
    },
    // 点击单元格 标题
    handleClickCellTitle(e) {
      console.log(e)
      // if (e.data.status == "1") {
      if (e.field === 'deliveryCode') {
        localStorage.setItem('deliverDetailSupplier', JSON.stringify(e.data))
        this.$router.push(`deliver-detail-supplier?id=${e.data.id}&type=` + e.data.deliveryType)
      }

      // } else if (e.data.status == "0") {
      //   this.$router.push(`deliver-detail-supplier?id=${e.data.id}&type=no`);
      // }
    },
    handleClickCellTool(e) {
      if (e.tool.title === this.$t('维护物流信息')) {
        this.$router.push({
          name: 'deliver-logistics-supplier',
          query: {
            id: e.data.id
          }
        })
        localStorage.setItem('deliverDetailSupplier', JSON.stringify(e.data))
      }
      if (e.tool.title === this.$t('取消')) {
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消？')
          },
          success: () => {
            this.$store.commit('startLoading')
            this.$API.receiptAndDelivery
              .supplierOrderDeliveryCancel([e.data.id])
              .then((res) => {
                this.$store.commit('endLoading')
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('操作成功'),
                    type: 'success'
                  })
                  this.$refs.templateRef.refreshCurrentGridData()
                  // this.confirmSuccess();
                }
              })
              .catch(() => {
                this.$refs.templateRef.refreshCurrentGridData()

                this.$store.commit('endLoading')
              })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
      // 妥投 TODO 演示使用 使用后要删掉
      if (e.tool.id === 'Proper') {
        this.$store.commit('startLoading')
        let isProperData = JSON.parse(localStorage.getItem('isProperData')) || []
        isProperData = isProperData.concat([e.data.id])
        localStorage.setItem('isProperData', JSON.stringify(isProperData))
        setTimeout(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef.refreshCurrentGridData()
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
        }, 500)
      }
    },
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    handleClickToolBar(e) {
      console.log(e)
      if (e.toolbar.id == 'new') {
        this.$router.push(`create-noOrder-supplier`)
        return
      }
      if (e.grid.getSelectedRecords()?.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _status = [],
        _id = []
      e.grid.getSelectedRecords().map((item) => {
        _id.push(item.id), _status.push(item.status)

        // _sourceStatus.push(item.sourceStatus),
        // _remainingQuantity.push(item.remainingQuantity);
      })
      this.id = _id
      if (e.toolbar.id == 'MaintainIogisticsInformation') {
        for (let i of _status) {
          if (i !== 2) {
            this.$toast({
              content: this.$t('只有状态在发货中才可以维护'),
              type: 'warning'
            })
            return
          }
        }
        this.deliveryShow = true
        return
      }

      if (e.toolbar.id == 'cancel') {
        for (let i of _status) {
          if (i !== 2) {
            this.$toast({
              content: this.$t('只有状态在发货中才可以关闭'),
              type: 'warning'
            })
            return
          }
        }
        this.handleClaim(_id)
      }
      // 妥投 TODO 演示使用 使用后要删掉
      if (e.toolbar.id == 'Proper') {
        console.log('==========', _id)
        let valid = true
        let status = ''
        for (let i = 0; i < _status.length; i++) {
          status = _status[i]
          if (_status[i] && _status[i - 1] && _status[i] !== _status[i - 1]) {
            valid = false
            break
          }
        }

        if (!valid || status != 2) {
          this.$toast({
            content: this.$t('非发货中状态的送货单不可妥投'),
            type: 'warning'
          })
        } else {
          this.$store.commit('startLoading')
          let isProperData = JSON.parse(localStorage.getItem('isProperData')) || []
          isProperData = isProperData.concat(_id)
          localStorage.setItem('isProperData', JSON.stringify(isProperData))
          setTimeout(() => {
            this.$store.commit('endLoading')
            this.$refs.templateRef.refreshCurrentGridData()
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
          }, 500)
        }
      }

      if (e.toolbar.id == 'loseeffectiveness') {
        // 不属于订单   补订单  暂时不搞
      }
    },
    handleClaim(_id) {
      this.$API.receiptAndDelivery
        .supplierOrderDeliveryCancel(_id)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef.refreshCurrentGridData()
        })
      // .catch(() => {
      //   this.$store.commit("endLoading");
      // });
    }
    // handleClaim(_id) {},
  }
  // 消失
  // deactivated() {
  //   localStorage.removeItem("deliverDetailSupplier");
  // },
}
</script>

<style></style>
