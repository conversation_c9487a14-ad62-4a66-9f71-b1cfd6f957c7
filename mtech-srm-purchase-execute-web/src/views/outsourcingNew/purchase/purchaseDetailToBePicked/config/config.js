import UTILS from '@/utils/utils'
import { i18n } from '@/main.js'
// import Vue from 'vue'
// import { MasterDataSelect } from '@/utils/constant'

export const checkColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  }
]
export const lastColumn = [
  {
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    width: '150',
    valueAccessor: (field, data) => {
      return data.siteCode + '-' + data.siteName
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    width: '250',
    valueAccessor: (field, data) => {
      return data.supplierCode + '-' + data.supplierName
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    width: '250'
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: '250'
  },
  {
    field: 'purchaseGroupCode',
    headerText: i18n.t('采购组'),
    width: '250',
    valueAccessor: (field, data) => {
      return data.purchaseGroupCode + '-' + data.purchaseGroupName
    }
  },
  {
    field: 'minPackNum',
    headerText: i18n.t('最小包装量'),
    width: '250'
  },
  {
    field: 'paramFile',
    headerText: i18n.t('舍入参数文件'),
    width: '250'
  },
  {
    field: 'demandNum',
    headerText: i18n.t('需求量'),
    width: '250'
  },
  {
    field: 'demandDate',
    headerText: i18n.t('组件需求日期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'orderCode',
    headerText: i18n.t('采购订单号'),
    width: '150'
  },
  {
    field: 'orderItemNo',
    headerText: i18n.t('采购订单行号'),
    width: '150'
  },
  {
    field: 'warehouseManagerCode',
    headerText: i18n.t('仓管员编码'),
    width: '150'
  },
  {
    field: 'warehouseManagerName',
    headerText: i18n.t('仓管员名称'),
    width: '150'
  },
  {
    field: 'warehouseCode',
    headerText: i18n.t('库存地点'),
    width: '150',
    valueAccessor: (field, data) => {
      return data.warehouseCode + '-' + data.warehouseName
    }
  },
  {
    field: 'stockUnit',
    headerText: i18n.t('库存单位'),
    width: '150'
  },
  {
    field: 'supplierStockNum',
    headerText: i18n.t('供应商库存（分包库存）'),
    width: '150'
  },
  {
    field: 'unlimitStockNum',
    headerText: i18n.t('合格库存'),
    width: '150'
  },
  {
    field: 'unqualifiedStockNum',
    headerText: i18n.t('待检不合格库存'),
    width: '150'
  },
  {
    field: 'qualitingStockNum',
    headerText: i18n.t('质量检验中库存'),
    width: '150'
  },
  {
    field: 'uncountDemandNum',
    headerText: i18n.t('未清需求数量'),
    width: '150'
  },
  {
    field: 'unReceiveNum',
    headerText: i18n.t('待领料数量'),
    width: '150'
  },
  {
    field: 'approvedNum',
    headerText: i18n.t('已审核未过账数量'),
    width: '150'
  },
  {
    field: 'disapproveNum',
    headerText: i18n.t('未审核未过账数量'),
    width: '150'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    }
  }
]
