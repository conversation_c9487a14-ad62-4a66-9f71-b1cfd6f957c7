<template>
  <div class="orderConfig full-height">
    <mt-template-page
      ref="template-0"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
  </div>
</template>
<script>
import { checkColumn, lastColumn } from './config/config'
import { BASE_TENANT } from '@/utils/constant'
import * as UTILS from '@/utils/utils'
export default {
  data() {
    return {
      requestUrls: {},
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              title: this.$t('获取待领料信息')
              // permission: ["O_02_0473"],
            },
            { id: 'Export1', icon: 'icon_solid_Import', title: this.$t('导出') }
          ],
          gridId: this.$tableUUID.outsourcing.purchaseDetailToBePicked.list,
          grid: {
            frozenColumns: 1, // 冻结第一列
            columnData: checkColumn.concat(lastColumn),
            asyncConfig: {
              url: `${BASE_TENANT}/outWaitReceiveOrder/buyer/query`,
              serializeList: (list) => {
                list.forEach((item, index) => {
                  item.selfIndex = index + 1
                })
                return list
              }
            }
          }
        }
      ]
    }
  },
  methods: {
    //点击顶部的操作按钮
    handleClickToolBar(e) {
      console.log('方法1', e)
      console.log(e.gridRef.getMtechGridRecords(), e.toolbar.id)
      if (e.toolbar.id === 'Export1') {
        this.handleExport()
        return
      }
    },
    handleExport() {
      //导出
      let rule = this.$refs[`template-0`].getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 10 },
        pageFlag: true,
        rules: rule.rules || []
      }
      this.$store.commit('startLoading')
      this.$API.purchaseOrder.poTimeConfigDownload(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.orderConfig {
}
</style>
