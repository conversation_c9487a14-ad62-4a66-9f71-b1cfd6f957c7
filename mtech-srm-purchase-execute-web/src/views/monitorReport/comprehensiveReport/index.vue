<!-- 综合报表 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item prop="demandDate" :label="$t('交货（需求）日期')">
          <mt-date-range-picker
            v-model="searchFormModel.demandDate"
            @change="(e) => dateTimeChange(e, 'demandDate')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('计划编号')" prop="jitDeliveryNumber">
          <mt-input
            v-model="searchFormModel.jitDeliveryNumber"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('送货单号')" prop="deliveryCode">
          <mt-input
            v-model="searchFormModel.deliveryCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('送货单状态')" prop="statusList">
          <mt-multi-select
            v-model="searchFormModel.statusList"
            :data-source="statusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('在途状态')" prop="onWayStatusList">
          <mt-multi-select
            v-model="searchFormModel.onWayStatusList"
            :data-source="onWayStatusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('是否JIT')" prop="jit">
          <mt-select
            v-model="searchFormModel.jit"
            :data-source="jitOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')">
          <RemoteAutocomplete
            v-model="searchFormModel.itemCode"
            :url="$API.masterData.getItemUrl"
            :placeholder="$t('请选择')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :search-fields="['itemName', 'itemCode']"
          />
        </mt-form-item>
        <mt-form-item prop="siteCode" :label="$t('工厂')">
          <RemoteAutocomplete
            v-model="searchFormModel.siteCode"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商')">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
        <mt-form-item prop="buyerOrgCode" :label="$t('采购组')">
          <RemoteAutocomplete
            v-model="searchFormModel.buyerOrgCode"
            :url="$API.masterData.getBusinessGroupUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'groupName', value: 'groupCode' }"
            :search-fields="['groupName', 'groupCode']"
          />
        </mt-form-item>
        <mt-form-item prop="subSiteCode" :label="$t('分厂')">
          <RemoteAutocomplete
            v-model="searchFormModel.subSiteCode"
            url="/contract/tenant/app/comprehensive/subSiteList"
            multiple
            method="get"
            :placeholder="$t('请选择')"
            :fields="{ text: 'subSiteName', value: 'subSiteCode' }"
            records-position="data"
          />
        </mt-form-item>
        <mt-form-item :label="$t('生产线')" prop="productline">
          <mt-input
            v-model="searchFormModel.productline"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('库存地点')" prop="warehouseCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.warehouseCodeList"
            url="/masterDataManagement/tenant/location/paged-query"
            :multiple="true"
            :placeholder="$t('请选择')"
            :fields="{ text: 'locationCode', value: 'locationName' }"
            :search-fields="['locationName', 'locationCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('内需单号')" prop="domesticDemandCode">
          <mt-input
            v-model="searchFormModel.domesticDemandCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('销售订单号')" prop="saleOrderNo">
          <mt-input
            v-model="searchFormModel.saleOrderNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('送货单创建时间')" prop="createTime">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            @change="(e) => createTimeChange(e, 'createTime')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('车牌')" prop="carNo">
          <mt-input
            v-model="searchFormModel.carNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('司机名称')" prop="driverName">
          <mt-input
            v-model="searchFormModel.driverName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('司机联系方式')" prop="driverPhone">
          <mt-input
            v-model="searchFormModel.driverPhone"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('采购订单号')" prop="orderCode">
          <mt-input
            v-model="searchFormModel.orderCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('第三方物流')" prop="thirdTenantCode">
          <mt-input
            v-model="searchFormModel.thirdTenantCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="a8aa8f70-88ce-4662-9a73-e314dbebf43b"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, statusOptions, onWayStatusOptions, jitOptions } from './config'
import { getHeadersFileName, download } from '@/utils/utils'

// const getMonthRange = () => {
//   var startDate = dayjs(new Date().setDate(1)) // 开始日期
//   var endDate = dayjs().endOf('month') // 结束日期
//   var formattedStartDate = startDate.toISOString().split('T')[0]
//   var formattedEndDate = endDate.toISOString().split('T')[0]

//   return [new Date(formattedStartDate), new Date(formattedEndDate)]
// }

export default {
  components: { CollapseSearch, ScTable },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {
        // demandDate: [
        //   { required: true, message: this.$t('请选择交货（需求）日期'), trigger: 'blur' }
        // ]
      },
      toolbar: [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      statusOptions,
      onWayStatusOptions,
      jitOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    // this.searchFormModel = {
    //   demandDate: getMonthRange()
    // }
    // this.searchFormModel['demandDateStart'] = dayjs(dayjs().startOf('month')).format('YYYY-MM-DD')
    // this.searchFormModel['demandDateEnd'] = dayjs(dayjs().endOf('month')).format('YYYY-MM-DD')
    // this.getTableData()
  },
  methods: {
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = dayjs(e.startDate).format('YYYY-MM-DD')
        this.searchFormModel[field + 'End'] = dayjs(e.endDate).format('YYYY-MM-DD')
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    createTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'S'] = dayjs(e.startDate).format('YYYY-MM-DD')
        this.searchFormModel[field + 'E'] = dayjs(e.endDate).format('YYYY-MM-DD')
      } else {
        this.searchFormModel[field + 'S'] = null
        this.searchFormModel[field + 'E'] = null
      }
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      // this.searchFormModel = {
      //   demandDate: getMonthRange()
      // }
      // this.searchFormModel['demandDateStart'] = dayjs(dayjs().startOf('month')).format('YYYY-MM-DD')
      // this.searchFormModel['demandDateEnd'] = dayjs(dayjs().endOf('month')).format('YYYY-MM-DD')
      this.handleSearch()
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.monitorReport
        .pageComprehensiveReportApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.monitorReport
        .exportComprehensiveReportApi(params)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>
