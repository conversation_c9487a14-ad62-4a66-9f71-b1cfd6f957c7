import { i18n } from '@/main.js'

export const statusOptions = [
  { text: i18n.t('新建'), value: 1 },
  { text: i18n.t('发货中'), value: 2 },
  { text: i18n.t('已完成'), value: 3 },
  { text: i18n.t('已取消'), value: 4 },
  { text: i18n.t('已关闭'), value: 5 }
]
export const onWayStatusOptions = [
  { text: i18n.t('未出发'), value: 0 },
  { text: i18n.t('已入园'), value: 1 },
  { text: i18n.t('已出发'), value: 2 },
  { text: i18n.t('已报到'), value: 3 },
  { text: i18n.t('已取消'), value: 4 },
  { text: i18n.t('已关闭'), value: 5 },
  { text: i18n.t('已完成'), value: 6 },
  { text: i18n.t('已到厂待卸货'), value: 7 },
  { text: i18n.t('已卸货待报检'), value: 8 },
  { text: i18n.t('保安门岗确认'), value: 11 },
  { text: i18n.t('已离园'), value: 12 }
]
export const isItemBatchOptions = [
  { text: i18n.t('是'), value: 1 },
  { text: i18n.t('否'), value: 0 }
]
export const jitOptions = [
  { text: i18n.t('是'), value: 1 },
  { text: i18n.t('否'), value: 0 }
]
export const stateCodeOptions = [
  { text: i18n.t('待质检'), value: 1 },
  { text: i18n.t('质检中'), value: 2 },
  { text: i18n.t('质检异常'), value: 3 },
  { text: i18n.t('质检正常'), value: 4 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'deliveryNumber',
    title: i18n.t('计划编号')
  },
  {
    field: 'deliveryCode',
    title: i18n.t('送货单号'),
    minWidth: 140
  },
  {
    field: 'forecastCode',
    title: i18n.t('预约单号'),
    minWidth: 140
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'onWayStatus',
    title: i18n.t('在途状态'),
    formatter: ({ cellValue }) => {
      let item = onWayStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'productLine',
    title: i18n.t('生产线')
  },
  {
    field: 'workOrderNo',
    title: i18n.t('生产工单')
  },
  {
    field: 'demandDate',
    title: i18n.t('交货（需求）日期'),
    minWidth: 120
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.siteName
    }
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码'),
    minWidth: 120
  },
  {
    field: 'itemName',
    title: i18n.t('物料描述'),
    minWidth: 160
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.supplierName
    }
  },
  {
    field: 'buyerOrgCode',
    title: i18n.t('采购组'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.buyerOrgName
    }
  },
  {
    field: 'total',
    title: i18n.t('原始需求数量（D）')
  },
  {
    field: 'bidNum',
    title: i18n.t('叫料（需求）数量')
  },
  {
    field: 'receiveQuantity',
    title: i18n.t('收货数量')
  },
  {
    field: 'deliveryNum',
    title: i18n.t('在途数量')
  },
  {
    field: 'remainingDeliveryNum',
    title: i18n.t('剩余送货数量')
  },
  {
    field: 'ext9',
    title: i18n.t('延误时间')
  },
  {
    field: 'subSiteCode',
    title: i18n.t('分厂'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.subSiteName
    }
  },
  {
    field: 'warehouseCode',
    title: i18n.t('库存地点'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.warehouseName
    }
  },
  {
    field: 'receiveAddressCode',
    title: i18n.t('送货地址'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.receiveAddressName
    }
  },
  {
    field: 'receiveInfo',
    title: i18n.t('收货信息')
  },
  {
    field: 'isItemBatch',
    title: i18n.t('是否批次来料'),
    formatter: ({ cellValue }) => {
      let item = isItemBatchOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'jit',
    title: i18n.t('是否JIT'),
    formatter: ({ cellValue }) => {
      let item = jitOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'planUpdateTimeText',
    title: i18n.t('计划更新时间')
  },
  {
    field: 'planCreateUserName',
    title: i18n.t('计划创建人')
  },
  {
    field: 'planCreateTimeText',
    title: i18n.t('计划创建时间')
  },
  {
    field: 'planCloseUserName',
    title: i18n.t('计划关闭人')
  },
  {
    field: 'planCloseTimeText',
    title: i18n.t('计划关闭时间')
  },
  {
    field: 'departTimeText',
    title: i18n.t('已出发时间')
  },
  {
    field: 'reportedTimeText',
    title: i18n.t('已报到时间')
  },
  {
    field: 'unloadTimeText',
    title: i18n.t('已到厂待卸货时间')
  },
  {
    field: 'inspectionTimeText',
    title: i18n.t('已卸货待报检时间')
  },
  {
    field: 'goawayTimeText',
    title: i18n.t('已离园时间')
  },
  {
    field: 'domesticDemandFlag',
    title: i18n.t('是否内需跟单')
  },
  {
    field: 'domesticDemandCode',
    title: i18n.t('内需单号')
  },
  {
    field: 'saleOrderNo',
    title: i18n.t('销售订单号')
  },
  {
    field: 'saleOrderLineNo',
    title: i18n.t('销售订单行号')
  },
  {
    field: 'ext31',
    title: i18n.t('业务员')
  },
  {
    field: 'createTime',
    title: i18n.t('送货单创建时间')
  },
  {
    field: 'carNo',
    title: i18n.t('车牌')
  },
  {
    field: 'driverName',
    title: i18n.t('司机名称')
  },
  {
    field: 'driverNo',
    title: i18n.t('司机身份证')
  },
  {
    field: 'driverPhone',
    title: i18n.t('司机联系方式')
  },
  {
    field: 'orderCode',
    title: i18n.t('采购订单号')
  },
  {
    field: 'lineNo',
    title: i18n.t('采购订单行号')
  },
  {
    field: 'inspDate',
    title: i18n.t('送检时间')
  },
  {
    field: 'inspectNo',
    title: i18n.t('报检编号')
  },
  {
    field: 'sendInspectQty',
    title: i18n.t('送检数量')
  },
  {
    field: 'stateCode',
    title: i18n.t('检验状态'),
    formatter: ({ cellValue }) => {
      let item = stateCodeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'ext43',
    title: i18n.t('检验类型')
  },
  {
    field: 'ext44',
    title: i18n.t('送检类型')
  },
  {
    field: 'inspectResults',
    title: i18n.t('检验结论')
  },
  {
    field: 'inspectorName',
    title: i18n.t('检验员')
  },
  {
    field: 'ext47',
    title: i18n.t('判检结果判定时间')
  },
  {
    field: 'unqltyReason',
    title: i18n.t('不合格原因')
  },
  {
    field: 'thirdTenantCode',
    title: i18n.t('第三方物流商'),
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.thirdTenantName
    }
  },
  {
    field: 'vmiWarehouseCode',
    title: i18n.t('VMI仓'),
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.vmiWarehouseName
    }
  },
  {
    field: 'deliveryNo',
    title: i18n.t('交货单号')
  },
  {
    field: 'deliveryItemNo',
    title: i18n.t('交货单号行号')
  }
]
