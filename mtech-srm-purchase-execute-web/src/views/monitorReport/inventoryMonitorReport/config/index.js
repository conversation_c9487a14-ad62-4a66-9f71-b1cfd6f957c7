import { i18n } from '@/main.js'

export const riskLevelOptions = [
  { text: i18n.t('满足'), value: 1 },
  { text: i18n.t('低风险'), value: 2 },
  { text: i18n.t('中风险'), value: 3 },
  { text: i18n.t('高风险'), value: 4 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称')
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.supplierName
    }
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.siteName
    }
  },
  {
    field: 'purOrgCode',
    title: i18n.t('采购组'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.purOrgName
    }
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.categoryName
    }
  },
  {
    field: 'platform',
    title: i18n.t('平台')
  },
  {
    field: 'maxDeliveryDate',
    title: i18n.t('最大交付日期')
  },
  {
    field: 'periodDeliveryQty',
    title: i18n.t('期间交付量')
  },
  {
    field: 'safeStockRate',
    title: i18n.t('安全库存配置比率')
  },
  {
    field: 'safeInvQty',
    title: i18n.t('安全库存量')
  },
  {
    field: 'stockSite',
    title: i18n.t('原厂库存')
  },
  {
    field: 'vmiInvQty',
    title: i18n.t('VMI库存')
  },
  {
    field: 'onwayQty',
    title: i18n.t('在途')
  },
  {
    field: 'vmiOnwayQty',
    title: i18n.t('VMI在途（供方送VMI）')
  },
  {
    field: 'stockQty',
    title: i18n.t('库存总量')
  },
  {
    field: 'diffQty',
    title: i18n.t('差异量')
  },
  {
    field: 'stockAchieveRate',
    title: i18n.t('达成率')
  },
  {
    field: 'updateDate',
    title: i18n.t('更新日期')
  },
  {
    field: 'dangerLevel',
    title: i18n.t('风险等级'),
    formatter: ({ cellValue }) => {
      let item = riskLevelOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'unclearPoQty',
    title: i18n.t('未清PO')
  },
  {
    field: 'planOrder',
    title: i18n.t('计划订单')
  }
]
