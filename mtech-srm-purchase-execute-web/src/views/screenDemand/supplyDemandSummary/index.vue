<template>
  <!-- 采方-对账协同-采购对账单列表 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
      class="template-height"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="bigVersionNo" :label="$t('大版本号')" label-style="top">
              <mt-input
                v-model="searchFormModel.bigVersionNo"
                :show-clear-button="true"
                :placeholder="$t('请输入大版本号')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          toolbar: [],
          grid: {
            columnData: [
              {
                field: 'id',
                headerText: 'id',
                width: 0,
                visible: false,
                isPrimaryKey: true
              },
              {
                width: '120',
                field: 'bigVersionNo',
                headerText: this.$t('大版本号'),
                cellTools: []
              },
              {
                // width: "150",
                field: 'dsmallVersionNo',
                headerText: this.$t('需求小版本号')
              },
              {
                width: '150',
                field: 'drowVersionDate',
                headerText: this.$t('需求版本日期')
              },
              {
                width: '120',
                field: 'fsmallVersionNo',
                headerText: this.$t('预测小版本号')
              },
              {
                width: '150',
                field: 'frowVersionDate',
                headerText: this.$t('预测版本日期')
              },
              {
                // width: '230',
                field: 'ssmallVersionNo',
                headerText: this.$t('供应小版本号')
              },
              {
                width: '150',
                field: 'srowVersionDate',
                headerText: this.$t('供应版本日期')
              }
            ],
            dataSource: [],
            pageSettings: {
              currentPage: 1,
              pageSize: 10,
              pageSizes: [10, 50, 100, 200],
              totalRecordsCount: 0
            },
            asyncConfig: {
              url: `/srm-purchase-execute/internal/screen/sync/record/v1/query`,
              recordsPosition: 'data.records',
              params: {}
            },
            lineSelection: 0,
            lineIndex: 1
            // frozenColumns: 1
          }
        }
      ]
    }
  },
  methods: {
    //单元格按钮，点击
    handleClickCellTitle(e) {
      if (e.field == 'bigVersionNo') {
        this.$router.push({
          path: `screen-supply-query-preview`,
          query: {
            timeStamp: new Date().getTime(),
            version: e.data.bigVersionNo,
            dsmallVersionNo: e.data.dsmallVersionNo,
            versionDate: e.data.drowVersionDate
          }
          // key: utils.randomString(),
          // query: { type: "add" },
        })
      }
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    }
  }
}
</script>

<style></style>
