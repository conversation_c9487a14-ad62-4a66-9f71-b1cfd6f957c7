import { i18n } from '@/main.js'

const columnData = () => {
  const column = [
    {
      field: 'invDate',
      headerText: i18n.t('库存日期'),
      width: '100'
    },
    {
      field: 'categoryCode',
      headerText: i18n.t('类别'),
      width: '100'
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: '150'
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: '200'
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      width: '200'
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料描述'),
      width: '200'
    },
    {
      field: 'factoryCode',
      headerText: i18n.t('工厂'),
      width: '100'
    },
    {
      field: 'invPlace',
      headerText: i18n.t('库存地点'),
      width: '200'
    },
    {
      field: 'wbsOdf',
      headerText: i18n.t('WBS/ODF'),
      width: '150'
    },
    {
      field: 'softwareVersion',
      headerText: i18n.t('软件版本'),
      width: '200'
    },
    {
      field: 'pid',
      headerText: i18n.t('PID'),
      width: '200'
    },
    {
      field: 'wb',
      headerText: i18n.t('白平衡'),
      width: '200'
    },
    {
      field: 'invQty',
      headerText: i18n.t('库存数量'),
      width: '150'
    },
    {
      field: 'invUnit',
      headerText: i18n.t('单位'),
      width: '80'
    },
    {
      field: 'invCycle',
      headerText: i18n.t('库存周期（天）'),
      width: '150'
    },
    {
      field: 'packageMethod',
      headerText: i18n.t('包装方式'),
      width: '100'
    }
  ]
  return column
}

export const componentConfig = (that) => {
  const config = [
    {
      isUseCustomSearch: true, // 是否使用自定义查询
      isCustomSearchRules: true,
      useToolTemplate: false, // 此项不使用预置的表格操作按钮'新增、编辑、删除'
      useBaseConfig: false,
      toolbar: [],
      grid: {
        allowPaging: true, // 是否使用内置分页器
        lineIndex: 0,
        columnData: columnData(that),
        asyncConfig: {
          url: '/srm-purchase-execute/internal/cvte/inv/v1/query'
        }
      }
    }
  ]
  return config
}
