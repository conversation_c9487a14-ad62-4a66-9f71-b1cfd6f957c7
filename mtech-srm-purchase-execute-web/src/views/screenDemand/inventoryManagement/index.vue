<!-- 视琨库存管理 - 采方 -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
          <mt-form-item prop="invDates" :label="$t('库存日期')">
            <mt-multi-select
              type="multipleChoice"
              v-model="searchFormModel.invDates"
              :data-source="invDatesOptions"
              :show-select-all="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
            />
          </mt-form-item>
          <mt-form-item prop="supplierCodes" :label="$t('供应商编码')">
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.supplierCodes"
              url="/masterDataManagement/tenant/supplier/paged-query"
              multiple
              :placeholder="$t('请选择供应商')"
              :fields="{ text: 'supplierName', value: 'supplierCode' }"
              :search-fields="['supplierName', 'supplierCode']"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="itemCode" :label="$t('物料编码')">
            <mt-input
              v-model="searchFormModel.itemCode"
              :show-clear-button="true"
              :placeholder="$t('请输入物料编码')"
            />
          </mt-form-item>
          <mt-form-item prop="wbsOdf" :label="$t('WBS/ODF')">
            <mt-input
              v-model="searchFormModel.wbsOdf"
              :show-clear-button="true"
              :placeholder="$t('请输入WBS/ODF')"
            />
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { componentConfig } from './config/index'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      componentConfig: componentConfig(this),
      searchFormModel: {},
      searchFormRules: {},
      invDatesOptions: []
    }
  },
  created() {
    this.getInvDateOptions()
  },
  methods: {
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    getInvDateOptions() {
      this.$API.screenDemand.getInvDateApi().then((res) => {
        if (res.code === 200) {
          this.invDatesOptions = res.data.map((item) => {
            return {
              text: item,
              value: item
            }
          })
        }
      })
    }
  }
}
</script>
