<template>
  <!-- 预测管理-采方 -->
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <!-- <div style="position: relative; z-index: 1; height: 30px">
      <span
        style="color: #2783fe; cursor: pointer; position: absolute; right: 0"
        @click="$router.go(-1)"
        >返回</span
      >
    </div> -->
    <collapse-search
      :is-grid-display="true"
      default-max-height="60"
      @reset="handleCustomReset"
      @search="handleCustomSearch"
    >
      <template slot="buttons-bar">
        <span type="info" @click="$router.go(-1)">{{ $t('返回') }}</span>
        <span type="info" @click="handleCustomReset()">{{ $t('重置') }}</span>
        <span type="primary" @click="handleCustomSearch()">{{ $t('查询') }}</span>
      </template>
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="supplierItemCodes" :label="$t('供应商物料编码')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.supplierItemCodes"
            :data-source="screenSupplierItemList"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="itemCodes" :label="$t('物料编码')" label-style="top">
          <mt-input
            v-model="itemCodes"
            @change="itemChange"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.itemName" />
        </mt-form-item>
        <mt-form-item prop="specCodes" :label="$t('规格')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.specCodes"
            :data-source="specList"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.categoryCode" />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('数据类型')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="forecastTypes"
            :data-source="ForecastDataTypeCell"
            :fields="{ text: 'title', value: 'code' }"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        :row-config="{ height: rowHeight }"
        :columns="columns"
        :table-data="tableData"
        show-overflow
        height="auto"
        header-align="left"
        align="left"
        style="padding-top: unset"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true
        }"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
      >
        <template slot="custom-tools">
          <!-- :icon="item.icon" -->
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
          <span style="margin-left: 10px"
            >{{ $t('大版本号') }}：{{ searchFormModel.bigVersionNo }}
          </span>
          <span style="margin-left: 10px"
            >{{ $t('小版本号') }}：{{ $route.query.dsmallVersionNo }}</span
          >
          <span style="margin-left: 10px"
            >{{ $t('版本日期') }}：{{ $route.query.versionDate }}</span
          >
        </template>
        <template #forecastTypeDefault="{ row }">
          <div v-for="(item, index) in forecastTypeOptions" :key="item">
            <div
              v-if="
                index === 0 && row.drow && row.drow.length && showForecastTypes.indexOf('D') !== -1
              "
              class="vxe-cell-border"
            >
              {{ item }}
            </div>
            <div
              v-if="
                index === 1 && row.frow && row.frow.length && showForecastTypes.indexOf('F') !== -1
              "
              class="vxe-cell-border"
            >
              {{ item }}
            </div>
            <div
              v-if="
                index === 2 && row.srow && row.srow.length && showForecastTypes.indexOf('S') !== -1
              "
              class="vxe-cell-border"
            >
              {{ item }}
            </div>
            <div
              v-if="
                index === 3 &&
                row.sdRow &&
                row.sdRow.length &&
                showForecastTypes.indexOf('SD') !== -1
              "
              class="vxe-cell-border"
            >
              {{ item }}
            </div>
            <div
              v-if="
                index === 4 &&
                row.sfRow &&
                row.sfRow.length &&
                showForecastTypes.indexOf('SF') !== -1
              "
              class="vxe-cell-border"
            >
              {{ item }}
            </div>
            <div
              v-if="
                index === 5 &&
                row.fdRow &&
                row.fdRow.length &&
                showForecastTypes.indexOf('FD') !== -1
              "
              class="vxe-cell-border"
            >
              {{ item }}
            </div>
          </div>
        </template>
      </ScTable>
    </div>
    <!-- <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    /> -->
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import {
  StatusSearchOptions,
  ForecastTypeDataSource,
  ForecastColumnData,
  ToolBar,
  ForecastDataTypeCell
} from './config/constant'
import * as UTILS from '@/utils/utils'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      forecastTypes: [],
      rowHeight: 148,
      ForecastDataTypeCell,
      itemCodes: '',
      toolbar: ToolBar,
      siteOptions: [], // 工厂下拉选项
      supplierOptions: [],
      getSupplierDataSource: () => {}, // 供应商 下拉选项
      itemOptions: [],
      getItemDataSource: () => {},
      apiWaitingQuantity: 0, // 调用的api正在等待数
      searchFormModel: {
        // supplierCode: null,
        // logicFactory: null,
        // specCode: null,
        // categoryCode: null,
        bigVersionNo: this.$route.query.version
      },
      titleList: [],
      tableData: [],
      columns: ForecastColumnData,
      plannerListOptions: [], // 计划员 下列选项
      forecastPageCurrent: 1,
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      isEditing: false, // 正在编辑数据
      exportRules: [], //导出规则
      statusOptions: StatusSearchOptions,
      forecastTypeOptions: ForecastTypeDataSource,
      copySearchFormModel: null,
      isEdit: false,
      screenSupplierList: [],
      specList: [],
      screenSupplierItemList: [],
      showForecastTypes: ['D', 'F', 'S', 'SD', 'SF', 'FD']
    }
  },
  mounted() {
    this.getSpecList()
    this.getScreenSupplierItem()
    this.getScreenSupplier()
    this.handleCustomSearch()
  },
  methods: {
    getScreenSupplierItem() {
      this.$API.screenDemand
        .getScreenSupplierItemCode({ bigVersionNo: this.$route.query.version })
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.screenSupplierItemList = []
            data.forEach((i) => {
              if (i.supplierItemCode && i.itemName) {
                this.screenSupplierItemList.push({
                  text: `${i.supplierItemCode}-${i.itemName}`,
                  value: i.supplierItemCode
                })
              }
            })
          }
        })
    },
    // 查询条件操作物料编码切割
    itemChange(e) {
      if (e) {
        this.searchFormModel.itemCodes = this.itemCodes.split(' ')
      } else {
        this.searchFormModel.itemCodes = null
      }
    },
    getSpecList() {
      this.$API.screenDemand
        .screenDemandSupplyBuyerQuerySpecAll({ bigVersionNo: this.$route.query.version })
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.specList = data
          }
        })
    },
    getScreenSupplier() {
      this.$API.screenDemand.getScreenSupplier().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.screenSupplierList = data.map((i) => {
            return {
              text: `${i.supplierCode}-${i.supplierName}`,
              value: i.supplierCode
            }
          })
        }
      })
    },
    handleClickToolBar(args) {
      const { code } = args
      if (code === 'ForecastExport') {
        // 导出
        // if (selectedRecords.length == 0) {
        //   this.$toast({ content: this.$t('请先选择需要导出的数据'), type: 'warning' })
        //   return
        // }
        this.postBuyerForecastExport()
      }
    },
    // 采方-导出
    postBuyerForecastExport() {
      const params = {
        page: {
          current: 1,
          size: 9999
        },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      // this.apiStartLoading()
      this.$API.screenDemand.screenDemandSummarySupExport(params).then((res) => {
        this.$store.commit('endLoading')
        // this.apiEndLoading()

        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 重置查询条件
    handleCustomReset() {
      this.forecastTypes = []
      this.itemCodes = null
      this.searchFormModel.itemCodes = null
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'bigVersionNo') {
            this.searchFormModel[key] = this.$route.query.version
          }
        }
      }
      this.handleCustomSearch()
    },
    // 采方-获取采方预测信息列表
    handleCustomSearch() {
      this.isEdit = false
      const params = {
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        },
        ...this.searchFormModel
      }
      if (
        !this.forecastTypes ||
        this.forecastTypes.length === 0 ||
        this.forecastTypes.length === 6
      ) {
        this.showForecastTypes = ['D', 'F', 'S', 'SD', 'SF', 'FD']
        this.rowHeight = 148
      } else if (this.forecastTypes.length === 5) {
        this.showForecastTypes = this.forecastTypes
        this.rowHeight = 123
      } else if (this.forecastTypes.length === 4) {
        this.showForecastTypes = this.forecastTypes
        this.rowHeight = 98
      } else if (this.forecastTypes.length === 3) {
        this.showForecastTypes = this.forecastTypes
        this.rowHeight = 73
      } else if (this.forecastTypes.length === 2) {
        this.showForecastTypes = this.forecastTypes
        this.rowHeight = 48
      } else if (this.forecastTypes.length === 1) {
        this.showForecastTypes = this.forecastTypes
        this.rowHeight = 24
      }
      // this.apiStartLoading()
      this.$API.screenDemand
        .screenDemandSummarySupQuery(params)
        .then((res) => {
          // this.apiEndLoading()
          if (res?.code == 200) {
            // const total = res?.data?.total || 0
            // this.forecastPageSettings.totalPages = Math.ceil(
            //   Number(total) / this.forecastPageSettings.pageSize
            // )
            // this.forecastPageSettings.totalRecordsCount = Number(total)
            const records = res?.data || [] // 表格数据
            const titleList = res?.data[0]?.headers || [] // 动态表头数据
            this.titleList = res?.data[0]?.headers || [] // 动态表头数据
            // 处理表头数据
            this.columns = this.handleColumns({ titleList })
            // 处理表数据
            this.tableData = this.handleDataSource({ records, titleList })
          }
        })
        .catch(() => {
          // this.apiEndLoading()
        })
    },
    handleColumns(data) {
      const { titleList } = data
      // 固定的表头
      const forecastColumns = ForecastColumnData
      // 动态的日期表头
      const titleListColumnData = []
      titleList.forEach((item, index) => {
        const title = `title_${titleList[index]}`
        titleListColumnData.push({
          field: title,
          title: item,
          // width: 143,
          // showOverflow: true,
          // editRender: {},
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <div>
                  <div
                    v-show={
                      row.drow && row.drow.length && this.showForecastTypes.indexOf('D') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['demand']}
                  </div>
                  <div
                    v-show={
                      row.frow && row.frow.length && this.showForecastTypes.indexOf('F') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['forecast']}
                  </div>
                  <div
                    v-show={
                      row.srow && row.srow.length && this.showForecastTypes.indexOf('S') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['supply']}
                  </div>
                  <div
                    v-show={
                      row.sdRow && row.sdRow.length && this.showForecastTypes.indexOf('SD') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['supplyDemand']}
                  </div>
                  <div
                    v-show={
                      row.sfRow && row.sfRow.length && this.showForecastTypes.indexOf('SF') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['supplyForecast']}
                  </div>
                  <div
                    v-show={
                      row.fdRow && row.fdRow.length && this.showForecastTypes.indexOf('FD') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['forecastDemand']}
                  </div>
                </div>
              ]
            },
            edit: ({ row }) => {
              return [
                <div>
                  <div
                    v-show={
                      row.drow && row.drow.length && this.showForecastTypes.indexOf('D') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['demand']}
                  </div>
                  <div
                    v-show={
                      row.frow && row.frow.length && this.showForecastTypes.indexOf('F') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['forecast']}
                  </div>
                  <div
                    v-show={
                      row.srow && row.srow.length && this.showForecastTypes.indexOf('S') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['supply']}
                  </div>
                  <div
                    v-show={
                      row.sdRow && row.sdRow.length && this.showForecastTypes.indexOf('SD') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['supplyDemand']}
                  </div>
                  <div
                    v-show={
                      row.sfRow && row.sfRow.length && this.showForecastTypes.indexOf('SF') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['supplyForecast']}
                  </div>
                  <div
                    v-show={
                      row.fdRow && row.fdRow.length && this.showForecastTypes.indexOf('FD') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['forecastDemand']}
                  </div>
                </div>
              ]
            }
          }
        })
      })
      // 合并表头数组，然后 push 到 forecastColumnData 中
      const columns = [].concat(forecastColumns).concat(titleListColumnData)
      return columns
    },
    handleDataSource(data) {
      const { records, titleList } = data
      const tableData = []
      records.forEach((recordsItem) => {
        const rowData = {
          ...recordsItem.tvScreenSupplyAggregation,
          drow: recordsItem.drow,
          frow: recordsItem.frow,
          srow: recordsItem.srow,
          sdRow: recordsItem.sdRow,
          sfRow: recordsItem.sfRow,
          fdRow: recordsItem.fdRow
        }
        // recordsItem.thePrimaryKey = recordsItem.tvForecastIntegration.id // 主键的值为 API 数据的 id
        // recordsItem.status = recordsItem.tvForecastIntegration.status ?? 0 // 状态为 undefined/null 时，默认为新建状态（导入时）

        const drow = recordsItem?.drow
        const frow = recordsItem?.frow
        const srow = recordsItem?.srow
        const sdRow = recordsItem?.sdRow
        const sfRow = recordsItem?.sfRow
        const fdRow = recordsItem?.fdRow
        titleList.forEach((itemTitle, index) => {
          srow.forEach(() => {
            // 将供应数据赋给表头对应的对象
            const forecastItem = {
              demand: drow && drow[index] ? drow[index] : 0,
              forecast: frow && frow[index] ? frow[index] : 0,
              supply: srow && srow[index] ? srow[index] : 0,
              supplyDemand: sdRow && sdRow[index] ? sdRow[index] : 0,
              supplyForecast: sfRow && sfRow[index] ? sfRow[index] : 0,
              forecastDemand: fdRow && fdRow[index] ? fdRow[index] : 0
            }
            rowData[`title_${itemTitle}`] = forecastItem
          })
        })
        tableData.push(rowData)
      })

      return tableData
      // return records
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  .vxe-cell-border {
    border: solid #e6e9ed 1px;
  }
}
/deep/ .col--seq,
/deep/ .col--checkbox {
  .c--tooltip {
    padding: 0 10px;
  }
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
