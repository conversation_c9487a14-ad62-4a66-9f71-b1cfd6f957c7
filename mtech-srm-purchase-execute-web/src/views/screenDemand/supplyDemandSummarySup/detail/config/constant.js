import { i18n } from '@/main.js'
export const ConstDynamicTitleStr = 'title_' // 动态表格数据的 key 标识

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

export const ToolBar = [
  {
    code: 'ForecastExport',
    name: i18n.t('导出'),
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  }
]

// 预测数据类型cell
export const ForecastDataTypeCell = [
  {
    title: i18n.t('D(需求)'),
    code: 'D'
  },
  {
    title: i18n.t('F(预测)'),
    code: 'F'
  },
  {
    title: i18n.t('S(供应)'),
    code: 'S'
  },
  {
    title: i18n.t('B(供应vs需求)'),
    code: 'SD'
  },
  {
    title: i18n.t('B(供应vs预测)'),
    code: 'SF'
  },
  {
    title: i18n.t('B(预测vs需求)'),
    code: 'FD'
  }
]

// 类型字段，快捷搜索，下拉选项
export const ForecastTypeDataSource = [
  ForecastDataTypeCell[0]?.title, // D(需求)
  ForecastDataTypeCell[1]?.title, // F(预测)
  ForecastDataTypeCell[2]?.title, // S(供应)
  ForecastDataTypeCell[3]?.title, // B(供应VS需求)
  ForecastDataTypeCell[4]?.title, // B(供应VS预测)
  ForecastDataTypeCell[5]?.title // B(预测VS需求)
]

// 状态 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
export const Status = {
  new: 0, // 新建
  modified: 1, // 已修改
  pendingFeedback: 2, // 已反馈
  confirmed: 3 // 已确认
}
// 状态 text
export const StatusText = {
  [Status.new]: i18n.t('新建'),
  [Status.modified]: i18n.t('已修改'),
  [Status.pendingFeedback]: i18n.t('已反馈'),
  [Status.confirmed]: i18n.t('已确认')
}
// 状态 class
export const StatusClass = {
  [Status.new]: 'col-active',
  [Status.modified]: 'col-active',
  [Status.pendingFeedback]: 'col-active', // col-published
  [Status.confirmed]: 'col-active' // col-inactive
}
export const StatusSearchOptions = [
  { value: Status.new, text: StatusText[Status.new] },
  { value: Status.modified, text: StatusText[Status.modified] },
  { value: Status.pendingFeedback, text: StatusText[Status.pendingFeedback] },
  { value: Status.confirmed, text: StatusText[Status.confirmed] }
]

// 预测表格列数据
export const ForecastColumnData = [
  // {
  //   type: 'checkbox',
  //   width: 70,
  //   ignore: true
  //   // fixed: 'left'
  // },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50
  },
  // {
  //   field: 'bigVersionNo',
  //   title: i18n.t('大版本号'),
  //   width: 135
  // },
  // {
  //   field: 'dsmallVersionNo',
  //   title: i18n.t('小版本号'),
  //   width: 135
  // },
  {
    field: 'itemCode',
    title: i18n.t('物料编码')
    // width: 210
  },
  {
    field: 'supplierItemCode',
    title: i18n.t('供应商物料编码'),
    width: 140
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称')
    // width: 70
  },
  {
    field: 'specCode',
    title: i18n.t('规格')
    // width: 135
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类')
    // width: 135
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码')
    // width: 60
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    width: 180
  },
  // {
  //   field: 'logicFactory',
  //   title: i18n.t('逻辑工厂'),
  //   width: 135
  // },
  // {
  //   field: 'bgCode',
  //   title: i18n.t('RBC'),
  //   width: 135
  // },
  {
    field: 'forecastType',
    className: 'vxe-table-multi-cell',
    title: i18n.t('数据类型'),
    slots: {
      default: 'forecastTypeDefault'
      // 使用 JSX 渲染
      // default: ({ row }) => {
      //   return [
      //     <div>
      //       {ForecastTypeDataSource.map((item) => {
      //         return <span>{item}</span>
      //       })}
      //     </div>
      //   ]
      // }
    }
  }
]

export const lastColumn = [
  {
    field: 'drowVersionDate',
    title: i18n.t('版本日期'),
    width: 135
  }
]
