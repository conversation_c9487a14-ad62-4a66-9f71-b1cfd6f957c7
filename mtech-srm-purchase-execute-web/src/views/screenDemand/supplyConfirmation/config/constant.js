import { i18n } from '@/main.js'
export const ConstDynamicTitleStr = 'title_' // 动态表格数据的 key 标识

// 业务组类型数字字典编码
export const BusinessGroupTypeDictCode = 'BUSINESSGROUP'

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 预测数据类型cell
export const ForecastDataTypeCell = [
  {
    title: i18n.t('F(预测)')
  },
  {
    title: i18n.t('S(供应)')
  },
  {
    title: i18n.t('B(累计差额)')
  }
]

// 类型字段，快捷搜索，下拉选项
export const ForecastTypeDataSource = [
  ForecastDataTypeCell[0]?.title, // F(预测)
  ForecastDataTypeCell[1]?.title, // S(供应)
  ForecastDataTypeCell[2]?.title, // S(供应上一版)
  ForecastDataTypeCell[3]?.title // B(累计差额)
]

// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  status: 0, // 状态 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
  planner: '', // 计划员
  factoryId: '', // 工厂id
  factoryName: '', // 工厂name
  factoryCode: '', // 工厂代码
  mrpArea: '', // 计划区域
  itemName: '', // 物料名称
  itemCode: '', // 物料编码
  itemId: '', // 物料id
  supplierName: '', // 供应商名称
  supplierCode: null, // 供应商编码
  supplierId: '', // 供应商id
  purchaseAdvanceDate: '', // 采购提前期
  supplierInv: '', // 供方库存
  supplierRemark: '', // 供应商备注
  quota: '', // 配额
  quotaFlag: '', // 无配额标识
  machineModel: '', // 机型/机芯
  purchaserRemark: '', // 采方备注
  manufacturer: '', // 生产厂商名称
  rawMaterialManufacturer: '', // 关键原材厂商
  keyRawMaterialOrigin: '', // 关键原材产地
  packageManufacturer: '', // 包装厂商
  packageProdtPlace: '', // 包装产地
  manufacturerDedicatedStatus: '', // 制造商专用状况
  syncVersion: '', // 版本
  unpaidPoQty: '' // 未交PO
}

export const ToolBar = [
  // { code: 'ForecastAdd', name: i18n.t('新增'), icon: 'vxe-icon-square-plus', status: 'info' },
  { code: 'ForecastConfirm', name: i18n.t('确认'), icon: 'vxe-icon-file-txt', status: 'info' },
  {
    code: 'ForecastImport',
    name: i18n.t('导入'),
    icon: 'vxe-icon-cloud-upload',
    status: 'info'
  },
  {
    code: 'ForecastExport',
    name: i18n.t('导出'),
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  }
]

// 状态 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
export const Status = {
  new: 0, // 新建
  modified: 1, // 已修改
  pendingFeedback: 2, // 已反馈
  confirmed: 3 // 已确认
}
// 状态 text
export const StatusText = {
  [Status.new]: i18n.t('新建'),
  [Status.modified]: i18n.t('已修改'),
  [Status.pendingFeedback]: i18n.t('已反馈'),
  [Status.confirmed]: i18n.t('已确认')
}
// 状态 class
export const StatusClass = {
  [Status.new]: 'col-active',
  [Status.modified]: 'col-active',
  [Status.pendingFeedback]: 'col-active', // col-published
  [Status.confirmed]: 'col-active' // col-inactive
}
export const StatusSearchOptions = [
  { value: Status.new, text: StatusText[Status.new] },
  { value: Status.modified, text: StatusText[Status.modified] },
  { value: Status.pendingFeedback, text: StatusText[Status.pendingFeedback] },
  { value: Status.confirmed, text: StatusText[Status.confirmed] }
]

// 预测表格列数据
export const ForecastColumnData = [
  // {
  //   type: 'checkbox',
  //   width: 70,
  //   // fixed: 'left'
  // },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50
  },
  {
    field: 'srowStatus',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = StatusSearchOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
    // fixed: 'left'
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码'),
    width: 210
    // showOverflow: true,
    // editRender: {},
    // slots: {
    //   edit: 'itemCodeEdit'
    // }
  },
  {
    field: 'supplierItemCode',
    title: i18n.t('供应商物料编码'),
    width: 210
    // showOverflow: true,
    // editRender: { name: 'input', attrs: { placeholder: '请输入规格' } }
    // editRender: {},
    // slots: {
    //   edit: 'itemSupplierCodeEdit'
    // }
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称'),
    width: 210
  },
  {
    field: 'specCode',
    title: i18n.t('规格'),
    width: 135
    // showOverflow: true,
    // editRender: { name: 'input', attrs: { placeholder: '请输入规格' } }
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类'),
    width: 135
    // showOverflow: true,
    // editRender: { name: 'input', attrs: { placeholder: '请输入品类' } }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    formatter: ({ row }) => {
      return `${row.supplierCode}-${row.supplierName}`
    },
    width: 210
    // showOverflow: true,
    // editRender: {},
    // slots: {
    //   edit: 'supplierCodeEdit'
    // }
  },
  {
    field: 'deliverAddr',
    title: i18n.t('出货地点'),
    width: 135,
    showOverflow: true,
    editRender: {},
    slots: {
      edit: 'deliverAddrEdit'
    }
  },
  // {
  //   field: 'bgCode',
  //   title: i18n.t('BG'),
  //   width: 135
  //   // showOverflow: true,
  //   // editRender: { name: 'input', attrs: { placeholder: '请输入发货地点' } }
  // },
  // {
  //   field: 'logicFactory',
  //   title: i18n.t('逻辑工厂'),
  //   showOverflow: true,
  //   width: 210,
  //   editRender: {},
  //   slots: {
  //     edit: 'factoryCodeEdit'
  //   }
  // },
  {
    field: 'forecastType',
    className: 'vxe-table-multi-cell',
    title: i18n.t('数据类型'),
    slots: {
      default: 'forecastTypeDefault'
      // 使用 JSX 渲染
      // default: ({ row }) => {
      //   return [
      //     <div>
      //       {ForecastTypeDataSource.map((item) => {
      //         return <span>{item}</span>
      //       })}
      //     </div>
      //   ]
      // }
    }
  }
]

export const lastColumn = [
  {
    field: 'drowVersionDate',
    title: i18n.t('版本日期'),
    width: 135,
    slots: {
      default: 'rowVersionDateDefault'
      // 使用 JSX 渲染
      // default: ({ row }) => {
      //   return [
      //     <div>
      //       {ForecastTypeDataSource.map((item) => {
      //         return <span>{item}</span>
      //       })}
      //     </div>
      //   ]
      // }
    }
  }
]
