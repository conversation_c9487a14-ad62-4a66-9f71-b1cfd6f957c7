<template>
  <!-- 预测管理-采方 -->
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      @reset="handleCustomReset"
      @search="handleCustomSearch"
    >
      <mt-form
        ref="searchFormRef"
        :model="searchFormModel"
        :rules="{
          supplierCode: [{ required: true, message: $t('请输入'), trigger: 'blur' }]
        }"
      >
        <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
          <!-- <mt-input v-model="searchFormModel.supplierCode"></mt-input> -->
          <mt-select
            style="flex: 1"
            v-model="searchFormModel.supplierCode"
            :data-source="screenSupplierList"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="logicFactory" :label="$t('逻辑工厂')" label-style="top">
          <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.logicFactory"
            :url="$API.masterData.getSiteListUrl"
            :placeholder="$t('请选择逻辑工厂')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.itemCode" />
        </mt-form-item>
        <!-- <mt-form-item prop="spec" :label="$t('规格')" label-style="top">
          <mt-input v-model="searchFormModel.spec"></mt-input>
        </mt-form-item> -->
        <mt-form-item prop="specCodes" :label="$t('规格')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.specCodes"
            :data-source="specList"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.status"
            :data-source="statusOptions"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.itemName" />
        </mt-form-item>
        <mt-form-item prop="supplierItemCodes" :label="$t('供应商物料编码')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.supplierItemCodes"
            :data-source="screenSupplierItemList"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.categoryCode" />
        </mt-form-item>
        <mt-form-item prop="bg" :label="$t('BG')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.bg" />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('类型')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="forecastTypes"
            :data-source="forecastTypeOptions"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        :row-config="{ height: rowHeight }"
        :columns="columns"
        :table-data="tableData"
        show-overflow
        height="auto"
        header-align="left"
        align="left"
        style="padding-top: unset"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true,
          activeMethod: this.activeRowMethod
        }"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        @edit-closed="editComplete"
        @edit-actived="editBegin"
        @edit-disabled="editDisabledEvent"
      >
        <template slot="custom-tools">
          <!-- :icon="item.icon" -->
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
          <span style="margin-left: 10px"
            >{{ $t('大版本号') }}：{{ versionInfo.bigVersionNo }}
          </span>
          <span style="margin-left: 10px"
            >{{ $t('小版本号') }}：{{ versionInfo.dsmallVersionNo }}
          </span>
          <span style="margin-left: 10px"
            >{{ $t('版本日期') }}：{{ versionInfo.drowVersionDate }}</span
          >
        </template>
        <template #forecastTypeDefault="{ row }">
          <div v-for="(item, index) in forecastTypeOptions" :key="item">
            <div
              v-if="
                index === 0 &&
                row.drow &&
                row.drow.length &&
                row.id &&
                showForecastTypes.indexOf('D') !== -1
              "
              class="vxe-cell-border"
            >
              {{ item }}
            </div>
            <div
              v-if="
                index === 1 &&
                row.frow &&
                row.frow.length &&
                row.id &&
                showForecastTypes.indexOf('F') !== -1
              "
              class="vxe-cell-border"
            >
              {{ item }}
            </div>
          </div>
        </template>
        <template #statusDefault="{ row }">
          <div
            v-if="row.drow && row.drow.length && row.id && showForecastTypes.indexOf('D') !== -1"
            class="vxe-cell-border"
          >
            {{ getStatusLabel(row.drowStatus) }}
          </div>
          <div
            v-if="row.frow && row.frow.length && row.id && showForecastTypes.indexOf('F') !== -1"
            class="vxe-cell-border"
          >
            {{ getStatusLabel(row.frowStatus) }}
          </div>
        </template>
        <template #rowVersionDateDefault="{ row }">
          <div
            v-if="row.drow && row.drow.length && row.id && showForecastTypes.indexOf('D') !== -1"
            class="vxe-cell-border"
          >
            {{ row.drowVersionDate || '-' }}
          </div>
          <div
            v-if="row.frow && row.frow.length && row.id && showForecastTypes.indexOf('F') !== -1"
            class="vxe-cell-border"
          >
            {{ row.frowVersionDate || '-' }}
          </div>
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import * as UTILS from '@/utils/utils'
import {
  StatusSearchOptions,
  ForecastTypeDataSource,
  ForecastColumnData,
  lastColumn,
  ToolBar
} from './config/constant'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      rowHeight: 50,
      toolbar: ToolBar,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      searchFormModel: {},
      titleList: [],
      tableData: [],
      columns: ForecastColumnData,
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      isEditing: false, // 正在编辑数据
      exportRules: [], //导出规则
      statusOptions: StatusSearchOptions,
      forecastTypeOptions: ForecastTypeDataSource,
      copySearchFormModel: null,
      forecastTypes: [],
      showForecastTypes: ['D', 'F'],
      isEdit: false,
      versionInfo: {
        bigVersionNo: '',
        frowVersionDate: '',
        fsmallVersionNo: ''
      },
      screenSupplierList: [],
      screenSupplierItemList: [],
      warningContent: this.$t('此状态数据不可编辑'),
      specList: []
    }
  },
  mounted() {
    this.getScreenSupplier()
    this.getSyncLatestVersion()
  },
  methods: {
    getScreenSupplierItem() {
      this.$API.screenDemand
        .getScreenSupplierItemCode({ bigVersionNo: this.versionInfo.bigVersionNo })
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.screenSupplierItemList = []
            data.forEach((i) => {
              if (i.supplierItemCode && i.itemName) {
                this.screenSupplierItemList.push({
                  text: `${i.supplierItemCode}-${i.itemName}`,
                  value: i.supplierItemCode
                })
              }
            })
          }
        })
    },
    getSyncLatestVersion() {
      this.$API.screenDemand.syncLatestVersion().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.versionInfo = data
          this.getSpecList({ bigVersionNo: data.bigVersionNo })
          this.getScreenSupplierItem()
        }
      })
    },
    getSpecList(params) {
      this.$API.screenDemand.screenDemandForecastManagerQuerySpecAll(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.specList = data
        }
      })
    },
    getScreenSupplier() {
      this.$API.screenDemand.getScreenSupplier().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.screenSupplierList = data.map((i) => {
            return {
              text: `${i.supplierCode}-${i.supplierName}`,
              value: i.supplierCode
            }
          })
        }
      })
    },
    getStatusLabel(status) {
      if ((!status && status !== 0) || status < 0) {
        return '-'
      }
      return StatusSearchOptions.find((i) => i.value === status)['text']
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      if (code === 'CloseEdit') {
        // 清除编辑状态
        this.$refs.xTable.$refs.xGrid.clearEdit()
        this.handleCustomSearch()
        return
      }
      if (this.isEdit) {
        // this.$toast({ content: this.$t('请先完成或结束编辑操作'), type: 'warning' })
        return
      }
      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })

      if (code === 'ForecastPublish') {
        // 发布
        this.postBuyerForecastPublish(idList)
      } else if (code === 'ForecastSync') {
        // 同步
        this.postBuyerForecastSync(idList)
      } else if (code === 'ForecastImport') {
        // // 导入
        // this.$refs.importDialog.init({
        //   title: this.$t('导入')
        // })
        this.$dialog({
          modal: () => import('@/components/uploadDialog'),
          data: {
            title: this.$t('导入'),
            importApi: this.$API.screenDemand.screenDemandForecastManagerImport,
            // downloadTemplateApi: this.$API.predictCollaboration.postBuyerForecastExportTemplateTv,
            paramsKey: 'excel'
            // saveButtonText: this.$t('下一步')
            // asyncParams: {
            //   // requestJson: JSON.stringify(parameter),
            // },
          },
          success: () => {
            // 导入之后刷新列表
            // this.$refs.templateRef.refreshCurrentGridData()
            this.handleCustomSearch()
          }
        })
        return
      } else if (code === 'ForecastExport') {
        // 导出
        // if (selectedRecords.length == 0) {
        //   this.$toast({ content: this.$t('请先选择需要导出的数据'), type: 'warning' })
        //   return
        // }
        this.postBuyerForecastExport(selectedRecords)
      }
    },
    // 采方发布预测信息接口
    postBuyerForecastPublish() {
      this.apiStartLoading()
      this.$API.screenDemand
        .screenDemandForecastManagerPublish({
          ...this.searchFormModel,
          page: {
            size: this.forecastPageSettings.pageSize,
            current: this.forecastPageCurrent
          }
        })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.getSyncLatestVersion()
            // 采方-获取采方预测信息列表
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方-导出
    postBuyerForecastExport() {
      if (!this.searchFormModel.supplierCode) {
        this.$toast({ content: this.$t('查询区域请先输入供应商编码'), type: 'warning' })
      }
      const params = {
        // forecastIds: selectedRecords.map((i) => i.id),
        // tvForecastQueryReq: {
        //   ...this.searchFormModel
        // },
        ...this.searchFormModel,
        page: {
          size: 9999,
          current: 1
        }
      }
      // this.apiStartLoading()
      this.$store.commit('startLoading')
      this.$API.screenDemand.screenDemandForecastManagerExport(params).then((res) => {
        this.$store.commit('endLoading')
        // this.apiEndLoading()

        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 同步需求
    postBuyerForecastSync() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('执行该操作会刷新现有版本需求，是否确认重新获取？')
        },
        success: () => {
          this.apiStartLoading()
          this.$API.screenDemand
            .syncScm()
            .then((res) => {
              this.apiEndLoading()
              if (res?.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.getSyncLatestVersion()
                // 采方-获取采方预测信息列表
                if (this.searchFormModel.supplierCode) {
                  this.handleCustomSearch()
                }
              }
            })
            .catch(() => {
              this.apiEndLoading()
            })
        }
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 校验点击的对象是否为取消编辑按钮
        if (args.$event.target.innerText === this.$t('取消编辑')) {
          // 清除编辑状态
          this.$refs.xTable.$refs.xGrid.clearEdit()
          this.handleCustomSearch()
          return
        }
        //2、 判断是否有row.planner调新增或者编辑接口
        this.postBuyerForecastSaveForecast({ data: row })
        //3、 接口调用成功调刷新接口
      }
    },
    editBegin(args) {
      // const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        // this.getItemDataSource({ value: row.itemCode }, row)
        // this.getSupplierDataSource({ value: row.supplierCode })
      } else {
        // this.getSupplierDataSource({ value: '' })
      }
      this.isEdit = true
    },
    // 采方-修改预测信息
    postBuyerForecastSaveForecast(args) {
      const { data } = args
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认修改该数据？')
        },
        success: () => {
          const forecastRecord = []
          if (this.titleList && this.titleList.length) {
            this.titleList.forEach((title) => {
              const item = data[`title_${title}`]['planGroupNum']
              if (item) {
                forecastRecord.push(String(item))
              } else {
                forecastRecord.push('0')
              }
            })
          }
          const params = {
            id: data.id,
            forecastRecord: forecastRecord
          }
          this.apiStartLoading()
          this.$API.screenDemand
            .screenDemandForecastManagerUpdate(params)
            .then((res) => {
              this.apiEndLoading()
              if (res?.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                // 采方-获取采方预测信息列表
                this.handleCustomSearch()
              }
            })
            .catch(() => {
              this.apiEndLoading()
              // 当出现错误时，指定行进入编辑状态
              this.$refs.xTable.$refs.xGrid.setEditRow(data)
            })
        },
        close: () => {
          this.$refs.xTable.$refs.xGrid.setEditRow(data)
        }
      })
    },
    activeRowMethod({ row }) {
      // 已确认的数据不可修改
      if (row.status === 5) {
        this.warningContent = this.$t('此状态数据不可编辑')
        return false
      }
      // 规格、品类为空不可编辑
      if (!row.specCode || !row.categoryCode) {
        this.warningContent = this.$t(
          '选中的数据规格或品类为空，请在相关系统维护基础数据后再操作！'
        )
        return false
      }
      return true
    },
    editDisabledEvent() {
      this.$toast({
        content: this.warningContent,
        type: 'warning'
      })
      this.warningContent = this.$t('此状态数据不可编辑')
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'bigVersionNo') {
            this.searchFormModel[key] = this.versionList[0]
          }
        }
      }
      this.forecastTypes = []
      this.tableData = []
      this.forecastPageCurrent = 1
      this.forecastPageSettings.totalPages = 0
      this.forecastPageSettings.totalRecordsCount = 0
      this.handleCustomSearch()
    },
    // 采方-获取采方预测信息列表
    handleCustomSearch() {
      if (!this.searchFormModel.supplierCode) {
        this.$toast({ content: this.$t('请选择供应商'), type: 'warning' })
      }
      this.isEdit = false
      const params = {
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        },
        ...this.searchFormModel
      }
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.showForecastTypes = []
          if (!this.forecastTypes.length) {
            this.showForecastTypes = ['D', 'F']
          } else {
            this.forecastTypes.forEach((i) => {
              switch (i) {
                case this.$t('D(原始需求)'):
                  this.showForecastTypes.push('D')
                  break
                case this.$t('F(预测)'):
                  this.showForecastTypes.push('F')
                  break

                default:
                  break
              }
            })
          }
          if (this.showForecastTypes.length === 1) {
            this.rowHeight = 24
          } else {
            this.rowHeight = 50
          }
          this.apiStartLoading()
          this.$API.screenDemand
            .screenDemandForecastManagerQuery(params)
            .then((res) => {
              this.apiEndLoading()
              if (res?.code == 200) {
                const total = res?.data?.total || 0
                this.forecastPageSettings.totalPages = Math.ceil(
                  Number(total) / this.forecastPageSettings.pageSize
                )
                this.forecastPageSettings.totalRecordsCount = Number(total)
                const records = res?.data?.records || [] // 表格数据
                const titleList = res?.data?.records[0]?.headers || [] // 动态表头数据
                this.titleList = res?.data?.records[0]?.headers || [] // 动态表头数据
                // 处理表头数据
                this.columns = this.handleColumns({ titleList })
                // 处理表数据
                this.tableData = this.handleDataSource({ records, titleList })
              }
            })
            .catch(() => {
              this.apiEndLoading()
            })
        }
      })
    },
    handleColumns(data) {
      const { titleList } = data
      // 固定的表头
      const forecastColumns = ForecastColumnData
      // 动态的日期表头
      const titleListColumnData = []
      titleList.forEach((item, index) => {
        const title = `title_${titleList[index]}`
        titleListColumnData.push({
          field: title,
          title: item,
          minWidth: 165,
          showOverflow: true,
          editRender: {},
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <div>
                  <div
                    v-show={
                      ((row.drow && row.drow.length && row.id) || !row.planner) &&
                      this.showForecastTypes.indexOf('D') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['total']}
                  </div>
                  <div
                    v-show={
                      ((row.frow && row.frow.length && row.id) || !row.planner) &&
                      this.showForecastTypes.indexOf('F') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['planGroupNum']}
                  </div>
                </div>
              ]
            },
            edit: ({ row }) => {
              return [
                <div>
                  <div
                    v-show={
                      ((row.drow && row.drow.length && row.id) || !row.planner) &&
                      this.showForecastTypes.indexOf('D') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    {row[title]['total']}
                  </div>
                  <div
                    v-show={
                      ((row.frow && row.frow.length && row.id) || !row.planner) &&
                      this.showForecastTypes.indexOf('F') !== -1
                    }
                    class='vxe-cell-border'
                  >
                    <mt-input-number
                      show-spin-button={false}
                      show-clear-button={false}
                      min={0}
                      precision={'0'}
                      v-model={row[title]['planGroupNum']}
                    />
                  </div>
                </div>
              ]
            }
          }
        })
      })

      // 合并表头数组，然后 push 到 forecastColumnData 中
      const columns = [].concat(forecastColumns).concat(titleListColumnData).concat(lastColumn)
      return columns
    },
    handleDataSource(data) {
      const { records, titleList } = data
      const tableData = []
      records.forEach((recordsItem) => {
        const rowData = {
          ...recordsItem.tvScreenForecastIntegration,
          drow: recordsItem.drow,
          frow: recordsItem.frow
        }
        // recordsItem.thePrimaryKey = recordsItem.tvForecastIntegration.id // 主键的值为 API 数据的 id
        // recordsItem.status = recordsItem.tvForecastIntegration.status ?? 0 // 状态为 undefined/null 时，默认为新建状态（导入时）

        const drow = recordsItem?.drow
        const frow = recordsItem?.frow
        titleList.forEach((itemTitle, index) => {
          drow.forEach(() => {
            // 将预测数据赋给表头对应的对象
            const forecastItem = {
              total: drow[index] || 0,
              planGroupNum: frow[index] || 0
            }
            rowData[`title_${itemTitle}`] = forecastItem
          })
        })
        tableData.push(rowData)
      })

      return tableData
      // return records
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  },
  activated() {
    this.getScreenSupplier()
    this.getSyncLatestVersion()
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  .vxe-cell-border {
    border: solid #e6e9ed 1px;
  }
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
