import { i18n } from '@/main.js'
export const ConstDynamicTitleStr = 'title_' // 动态表格数据的 key 标识

// 业务组类型数字字典编码
export const BusinessGroupTypeDictCode = 'BUSINESSGROUP'

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 预测数据类型cell
export const ForecastDataTypeCell = [
  {
    title: i18n.t('D(原始需求)')
  },
  {
    title: i18n.t('F(预测)')
  }
]

// 类型字段，快捷搜索，下拉选项
export const ForecastTypeDataSource = [
  // ForecastDataTypeCell[0]?.title, // D1(预测)
  // ForecastDataTypeCell[1]?.title, // D2(订单)
  ForecastDataTypeCell[0]?.title, // D(原始需求)
  ForecastDataTypeCell[1]?.title // F
]

export const ToolBar = [
  {
    code: 'CloseEdit',
    name: i18n.t('取消编辑'),
    icon: 'vxe-icon-edit',
    status: 'info',
    transfer: true
  },
  { code: 'ForecastSync', name: i18n.t('同步需求'), icon: 'vxe-icon-file-txt', status: 'info' },
  {
    code: 'ForecastPublish',
    name: i18n.t('发布'),
    icon: 'vxe-icon-folder-open',
    status: 'info'
  },
  {
    code: 'ForecastImport',
    name: i18n.t('导入'),
    icon: 'vxe-icon-cloud-upload',
    status: 'info'
  },
  {
    code: 'ForecastExport',
    name: i18n.t('导出'),
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  }
]

// 状态 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
export const Status = {
  new: 0, // 新建
  modified: 1, // 已修改
  pendingFeedback: 2 // 已发布 不可编辑
  // feedbackNormal: 3, // 反馈满足
  // feedbackAbnormal: 4, // 反馈不满足
  // confirmed: 5 // 已确认 不可编辑
  // deleted: 6, // 已删除 不可编辑
}
// 状态 text
export const StatusText = {
  [Status.new]: i18n.t('新建'),
  [Status.modified]: i18n.t('已修改'),
  [Status.pendingFeedback]: i18n.t('已发布')
}
// 状态 class
export const StatusClass = {
  [Status.new]: 'col-active',
  [Status.modified]: 'col-active',
  [Status.pendingFeedback]: 'col-active'
}
export const StatusSearchOptions = [
  { value: Status.new, text: StatusText[Status.new] },
  { value: Status.modified, text: StatusText[Status.modified] },
  { value: Status.pendingFeedback, text: StatusText[Status.pendingFeedback] }
]

// 预测表格列数据
export const ForecastColumnData = [
  // {
  //   type: 'checkbox',
  //   minWidth: 70,
  //   ignore: true
  //   // fixed: 'left'
  // },
  {
    type: 'seq',
    title: i18n.t('序号'),
    minWidth: 50
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码'),
    minWidth: 100
    // showOverflow: true,
    // editRender: {},
    // slots: {
    //   edit: 'itemCodeEdit'
    // }
  },
  {
    field: 'supplierItemCode',
    title: i18n.t('供应商物料编码'),
    minWidth: 130
    // showOverflow: true,
    // editRender: {},
    // slots: {
    //   edit: 'itemSupplierCodeEdit'
    // }
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称'),
    minWidth: 180
  },
  {
    field: 'specCode',
    title: i18n.t('规格'),
    minWidth: 80
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类'),
    minWidth: 100
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    formatter: ({ row }) => {
      return `${row.supplierCode}-${row.supplierName}`
    },
    minWidth: 210
    // showOverflow: true,
    // editRender: {},
    // slots: {
    //   edit: 'supplierCodeEdit'
    // }
  },
  {
    field: 'logicFactory',
    title: i18n.t('逻辑工厂'),
    showOverflow: true,
    minWidth: 210
    // editRender: {},
    // slots: {
    //   edit: 'factoryCodeEdit'
    // }
  },
  {
    field: 'bgCode',
    title: i18n.t('BG'),
    minWidth: 210
  },
  {
    field: 'forecastType',
    className: 'vxe-table-multi-cell',
    title: i18n.t('类型'),
    slots: {
      default: 'forecastTypeDefault'
      // 使用 JSX 渲染
      // default: ({ row }) => {
      //   return [
      //     <div>
      //       {ForecastTypeDataSource.map((item) => {
      //         return <span>{item}</span>
      //       })}
      //     </div>
      //   ]
      // }
    }
  }
]

export const lastColumn = [
  {
    field: 'status',
    title: i18n.t('状态'),
    // formatter: ({ cellValue }) => {
    //   let item = StatusSearchOptions.find((item) => item.value === cellValue)
    //   return item ? item.text : ''
    // },
    slots: {
      default: 'statusDefault'
      // 使用 JSX 渲染
      // default: ({ row }) => {
      //   return [
      //     <div>
      //       {ForecastTypeDataSource.map((item) => {
      //         return <span>{item}</span>
      //       })}
      //     </div>
      //   ]
      // }
    }
    // fixed: 'left'
  },
  {
    field: 'drowVersionDate',
    title: i18n.t('版本日期'),
    minWidth: 135,
    slots: {
      default: 'rowVersionDateDefault'
      // 使用 JSX 渲染
      // default: ({ row }) => {
      //   return [
      //     <div>
      //       {ForecastTypeDataSource.map((item) => {
      //         return <span>{item}</span>
      //       })}
      //     </div>
      //   ]
      // }
    }
  }
]
