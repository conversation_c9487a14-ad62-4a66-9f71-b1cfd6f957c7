<template>
  <!-- 屏采交货计划-采方 -->
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      @reset="handleCustomReset"
      @search="handleCustomSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="itemCodeList" :label="$t('物料编码')" label-style="top">
          <!-- <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.itemCodeList"
            multiple
            :url="$API.masterData.getItemUrl"
            :placeholder="$t('请选择物料')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :search-fields="['itemName', 'itemCode']"
          ></RemoteAutocomplete> -->
          <mt-input
            v-model="itemCodes"
            @change="itemChange"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="virtualSupplierCode" :label="$t('虚拟供应商编码')" label-style="top">
          <mt-input
            v-model="virtualSupplierCode"
            @change="
              () =>
                (searchFormModel.virtualSupplierCodeList = virtualSupplierCode
                  ? [virtualSupplierCode]
                  : null)
            "
          />
        </mt-form-item>
        <mt-form-item prop="deliverAddr" :label="$t('发货地')" label-style="top">
          <mt-input
            v-model="deliverAddr"
            @change="() => (searchFormModel.deliverAddrList = deliverAddr ? [deliverAddr] : null)"
          />
        </mt-form-item>
        <mt-form-item prop="siteCodeList" :label="$t('工厂编码')" label-style="top">
          <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.siteCodeList"
            :url="$API.masterData.getSiteAuthFuzzyUrl"
            multiple
            :placeholder="$t('请选择工厂')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            params-key="fuzzyParam"
            records-position="data"
          />
        </mt-form-item>
        <mt-form-item prop="deliveryAddrName" :label="$t('交货地点名称')" label-style="top">
          <mt-input
            v-model="deliveryAddrName"
            @change="
              () =>
                (searchFormModel.deliveryAddrNameList = deliveryAddrName
                  ? [deliveryAddrName]
                  : null)
            "
          />
        </mt-form-item>
        <mt-form-item prop="storageLocCodeList" :label="$t('库位编码')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.storageLocCodeList"
            :data-source="storageLocCodeOptions"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="bgTypeList" :label="$t('BG类型')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.bgTypeList"
            :data-source="bgTypeOptions"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="typeList" :label="$t('类型')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.typeList"
            :data-source="[
              { text: $t('D(原始需求)'), value: 'D' },
              { text: $t('P(需求量)'), value: 'P' },
              { text: $t('A'), value: 'A' }
            ]"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="statusList" :label="$t('状态')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.statusList"
            :data-source="statusOptions"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        :row-config="{ height: rowHeight }"
        :columns="columns"
        :table-data="tableData"
        show-overflow
        height="auto"
        header-align="left"
        align="left"
        style="padding-top: unset"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true
        }"
        :tooltip-config="tooltipConfig"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
      >
        <template slot="custom-tools">
          <!-- :icon="item.icon" -->
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #supplierListDefault="{ row }">
          <div v-for="item in row.supplierList" :key="item.supplierCode">
            {{ item.supplierCode }}
          </div>
        </template>
        <template #purchaseOrderDefault="{ row }">
          <div v-for="item in row.purchaseOrder" :key="item">
            {{ item }}
          </div>
        </template>
        <template #forecastTypeDefault="{}">
          <div v-for="(item, index) in forecastTypeOptions" :key="item">
            <div
              v-if="index === 0 && showForecastTypes.indexOf('D') !== -1"
              class="vxe-cell-border"
            >
              {{ item }}
            </div>
            <div
              v-if="index === 1 && showForecastTypes.indexOf('P') !== -1"
              class="vxe-cell-border"
            >
              {{ item }}
            </div>
            <div
              v-if="index === 2 && showForecastTypes.indexOf('A') !== -1"
              class="vxe-cell-border"
            >
              {{ item }}
            </div>
          </div>
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import {
  StatusSearchOptions,
  ForecastTypeDataSource,
  columnData,
  lastColumns,
  ToolBar
} from './config/constant'
import * as UTILS from '@/utils/utils'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      rowHeight: 70,
      itemCodes: '',
      virtualSupplierCode: null,
      deliverAddr: null,
      deliveryAddrName: null,
      addId: '1',
      toolbar: ToolBar,
      searchFormModel: {},
      titleList: [],
      tableData: [],
      tooltipConfig: {
        showAll: true,
        enterable: true,
        contentMethod: ({ column, row }) => {
          const { field } = column
          // 重写默认的提示内容
          if (field === 'purchaseOrder') {
            return row['purchaseOrderStr'] ? row['purchaseOrderStr'] : ''
          }
          // 其余的单元格使用默认行为
          return null
        }
      },
      columns: columnData,
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      forecastPageCurrent: 1,
      statusOptions: StatusSearchOptions,
      forecastTypeOptions: ForecastTypeDataSource,
      forecastTypes: [],
      showForecastTypes: ['D', 'P', 'A'],
      isEdit: false,
      storageLocCodeOptions: [],
      bgTypeOptions: []
    }
  },
  mounted() {
    this.getBgTypeAndStorageLocCode()
  },
  methods: {
    // 查询条件操作物料编码切割
    itemChange(e) {
      if (e) {
        this.searchFormModel.itemCodeList = this.itemCodes.split(' ')
      } else {
        this.searchFormModel.itemCodeList = null
      }
    },
    getBgTypeAndStorageLocCode() {
      this.$API.screenDemand.screenWarehouseList().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.storageLocCodeOptions = data.deliveryAddrCode
          this.bgTypeOptions = data.bgType
        }
      })
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      if (code === 'CloseEdit') {
        // 清除编辑状态
        this.$refs.xTable.$refs.xGrid.clearEdit()
        this.handleCustomSearch()
        return
      }
      if (this.isEdit) {
        // this.$toast({ content: this.$t('请先完成或结束编辑操作'), type: 'warning' })
        return
      }
      const idList = []
      selectedRecords.forEach((item) => {
        idList.push({
          id: item.id,
          status: item.status,
          smallVersionNo: item.smallVersionNo,
          supplierCodes: item.supplierCodes,
          processorCodes: item.processorCodes,
          processorTenantId: item.processorTenantId,
          processorName: item.processorName,
          virtualSupplierCode: item.virtualSupplierCode,
          virtualSupplierTenantId: item.virtualSupplierTenantId,
          virtualSupplierName: item.virtualSupplierName
        })
      })

      if (code === 'ForecastExport') {
        // 导出
        // if (selectedRecords.length == 0) {
        //   this.$toast({ content: this.$t('请先选择需要导出的数据'), type: 'warning' })
        //   return
        // }
        this.postDeliveryScheduleExport(idList)
      }
    },
    // 采方-导出
    postDeliveryScheduleExport(idList) {
      const params = {
        ids: idList.map((i) => i.id),
        ...this.searchFormModel,
        page: {
          size: 999999,
          current: 1
        }
      }
      this.$store.commit('startLoading')
      this.$API.screenDemand.screenScheduleeSupExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.itemCodes = null
      this.searchFormModel.itemCodeList = null
      this.virtualSupplierCode = null
      this.searchFormModel.virtualSupplierCodeList = null
      this.deliverAddr = null
      this.deliveryAddrName = null
      this.forecastTypes = []
      this.forecastPageCurrent = 1
      this.handleCustomSearch()
    },
    // 采方-获取采方信息列表
    handleCustomSearch() {
      this.isEdit = false
      const params = {
        ...this.searchFormModel,
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        }
      }
      if (
        !this.searchFormModel?.typeList ||
        this.searchFormModel?.typeList?.length === 0 ||
        this.searchFormModel?.typeList?.length === 3
      ) {
        this.showForecastTypes = ['D', 'P', 'A']
        this.rowHeight = 70
      } else if (this.searchFormModel?.typeList?.length === 2) {
        this.showForecastTypes = this.searchFormModel?.typeList
        this.rowHeight = 48
      } else if (this.searchFormModel?.typeList?.length === 1) {
        this.showForecastTypes = this.searchFormModel?.typeList
        this.rowHeight = 24
      }
      this.apiStartLoading()
      this.tableData = []
      this.$API.screenDemand
        .screenScheduleSupQuery(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.screenDemandPlanDtoPage?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.totalRecordsCount = Number(total)
            const records = res?.data?.screenDemandPlanDtoPage?.records || [] // 表格数据
            const titleList =
              res?.data?.titleList.map((item, index) => {
                if (index === 0) {
                  return 'W' + item
                } else {
                  return 'D' + item
                }
              }) || [] // 动态表头数据
            this.titleList = titleList // 动态表头数据
            // 处理表头数据
            this.columns = this.handleColumns({ titleList })
            // 处理表数据
            this.tableData = this.handleDataSource({ records, titleList })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleColumns(data) {
      const { titleList } = data
      // 固定的表头
      const constantColumns = columnData
      // 动态的日期表头
      const titleListColumnData = []
      titleList.forEach((item, index) => {
        const title = `title_${titleList[index]}`
        titleListColumnData.push({
          field: title,
          title: item,
          minWidth: 125,
          showOverflow: true,
          editRender: {},
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <div>
                  <div v-show={this.showForecastTypes.indexOf('D') !== -1} class='vxe-cell-border'>
                    {row[title]?.total}
                  </div>
                  <div v-show={this.showForecastTypes.indexOf('P') !== -1} class='vxe-cell-border'>
                    {row[title]?.buyerNum}
                  </div>
                  <div v-show={this.showForecastTypes.indexOf('A') !== -1} class='vxe-cell-border'>
                    {row[title]?.planGroupNum}
                  </div>
                </div>
              ]
            },
            edit: ({ row }) => {
              // <div v-show={this.showForecastTypes.indexOf('P') !== -1} class='vxe-cell-border'>
              //   <mt-input-number
              //     show-spin-button={false}
              //     show-clear-button={false}
              //     v-model={row[title]['buyerNum']}
              //     v-show={columnIndex >= 35}
              //   />
              //   <div v-show={columnIndex < 35}>{row[title]['buyerNum']}</div>
              // </div>
              return [
                <div>
                  <div v-show={this.showForecastTypes.indexOf('D') !== -1} class='vxe-cell-border'>
                    {row[title]?.total}
                  </div>
                  <div v-show={this.showForecastTypes.indexOf('P') !== -1} class='vxe-cell-border'>
                    <mt-input-number
                      show-spin-button={false}
                      show-clear-button={false}
                      v-model={row[title]['buyerNum']}
                      v-show={!title.includes('W')}
                    />
                    <span v-show={title.includes('W')}>{row[title]?.buyerNum}</span>
                  </div>
                  <div v-show={this.showForecastTypes.indexOf('A') !== -1} class='vxe-cell-border'>
                    {row[title]?.planGroupNum}
                  </div>
                </div>
              ]
            }
          }
        })
      })

      // 合并表头数组，然后 push 到 forecastColumnData 中
      const columns = [].concat(constantColumns).concat(titleListColumnData).concat(lastColumns)
      return columns
    },
    handleDataSource(args) {
      const { records, titleList } = args
      records.forEach((obj) => {
        obj.addId = this.addId++
        obj.purchaseOrderStr = ''
        obj.purchaseOrder?.forEach((i) => {
          obj.purchaseOrderStr = obj.purchaseOrderStr + i + '\n'
        })
        if (obj.detailDtoList.length > 0) {
          titleList.forEach((title, index) => {
            let total = ''
            let buyerNum = ''
            let planGroupNum = ''
            // let gapNum = ''
            // let countGapNum = ''
            obj.detailDtoList.forEach((itm) => {
              if (!itm) {
                total = ''
                buyerNum = ''
                planGroupNum = ''
              }
              if (itm.type === 'D') {
                if (index === 0) {
                  total = itm[`weeklySum`]
                } else {
                  total = itm[`planDemandQty${index}`]
                }
              }
              if (itm.type === 'P') {
                if (index === 0) {
                  buyerNum = itm[`weeklySum`]
                } else {
                  buyerNum = itm[`planDemandQty${index}`]
                }
              }
              if (itm.type === 'A') {
                if (index === 0) {
                  planGroupNum = itm[`weeklySum`]
                } else {
                  planGroupNum = itm[`planDemandQty${index}`]
                }
              }
            })
            obj[`title_${title}`] = {
              total,
              buyerNum,
              planGroupNum
            }
          })
        }
      })
      return records
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  div .vxe-cell-border {
    border: solid #e6e9ed 1px;
    height: 24px;
    box-sizing: border-box;
  }
}
/deep/ .col--seq,
/deep/ .col--checkbox {
  .c--tooltip {
    padding: 0 10px;
  }
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
