import { i18n } from '@/main.js'

// 业务组类型数字字典编码
export const BusinessGroupTypeDictCode = 'BUSINESSGROUP'

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 预测数据类型cell
export const ForecastDataTypeCell = [
  {
    title: i18n.t('D(原始需求)')
  },
  {
    title: i18n.t('P(需求量)')
  },
  {
    title: 'A'
  }
]

// 类型字段，快捷搜索，下拉选项
export const ForecastTypeDataSource = [
  ForecastDataTypeCell[0]?.title, // D(原始需求)
  ForecastDataTypeCell[1]?.title, // P(需求量)
  ForecastDataTypeCell[2]?.title // A
]

export const ToolBar = [
  {
    code: 'ForecastExport',
    name: i18n.t('导出'),
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  }
]

// 表格行按钮
export const CellTools = [
  {
    id: 'ForecastPublish',
    icon: '', // icon_solid_Release
    // permission: ['O_02_0384'],
    title: i18n.t('发布'),
    visibleCondition: (data) => {
      return data.status === Status.new || data.status === Status.modified // 状态：新建 || 已修改
    }
  },
  {
    id: 'ForecastDelete',
    icon: '', // icon_solid_Delete
    // permission: ['O_02_0383'],
    title: i18n.t('删除'),
    visibleCondition: (data) => {
      return data.status === Status.new || data.status === Status.modified // 状态：新建 || 已修改
    }
  }
]

// 状态 0 新建、1 已修改、2 待反馈、3 已反馈、4 已删除
export const Status = {
  new: 0, // 新建
  modified: 1, // 已修改
  pendingFeedback: 2, // 已发布 不可编辑
  endingFeedback: 3, // 已反馈 不可编辑
  deleted: 4 // 已删除 不可编辑
}
// 状态 text
export const StatusText = {
  [Status.new]: i18n.t('新建'),
  [Status.modified]: i18n.t('已修改'),
  [Status.pendingFeedback]: i18n.t('已发布'),
  [Status.endingFeedback]: i18n.t('已反馈'),
  [Status.deleted]: i18n.t('已删除')
}
// 状态 class
export const StatusClass = {
  [Status.new]: 'col-active',
  [Status.modified]: 'col-active',
  [Status.pendingFeedback]: 'col-active', // col-published
  [Status.endingFeedback]: 'col-active', // col-published
  [Status.deleted]: 'col-active' // col-inactive
}
export const StatusSearchOptions = [
  { value: Status.new, text: StatusText[Status.new] },
  { value: Status.modified, text: StatusText[Status.modified] },
  { value: Status.pendingFeedback, text: StatusText[Status.pendingFeedback] },
  { value: Status.endingFeedback, text: StatusText[Status.endingFeedback] },
  { value: Status.deleted, text: StatusText[Status.deleted] }
]

// 预测表格列数据
export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    ignore: true
    // fixed: 'left'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50
  },
  {
    field: 'bigVersionNo',
    title: i18n.t('大版本号')
  },
  {
    field: 'smallVersionNo',
    title: i18n.t('小版本号')
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = StatusSearchOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'virtualSupplierCode',
    title: i18n.t('虚拟供应商代码'),
    // formatter: ({ row }) => {
    //   return `${row.virtualSupplierCode}-${row.virtualSupplierName}`
    // },
    width: 160
  },
  {
    field: 'supplierList',
    title: i18n.t('供应商编码'),
    // formatter: ({ row }) => {
    //   return `${row.supplierList}`
    // },
    slots: {
      // 使用 JSX 渲染
      default: 'supplierListDefault'
    },
    width: 160,
    showOverflow: true
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂编码'),
    showOverflow: true,
    width: 100
  },
  {
    field: 'materialCode',
    title: i18n.t('物料编码'),
    width: 160
  },
  {
    field: 'supplierItemCode',
    title: i18n.t('供应商物料编码'),
    width: 160
  },
  {
    field: 'rbcCode',
    title: i18n.t('RBC编码')
  },
  {
    field: 'bgType',
    title: i18n.t('BG类型')
  },
  {
    field: 'etaLocationCode',
    title: i18n.t('交货地库位编码')
  },
  {
    field: 'deliveryAddrName',
    title: i18n.t('交货地点')
  },
  {
    field: 'deliverAddr',
    title: i18n.t('发货地点')
  },
  {
    field: 'processorCode',
    title: i18n.t('加工商编码')
  },
  {
    field: 'purchaseOrder',
    title: i18n.t('采购订单'),
    slots: {
      // 使用 JSX 渲染
      default: 'purchaseOrderDefault'
    }
    // width: 210
    // showOverflow: true,
    // editRender: { name: 'input', attrs: { placeholder: '请输入' } }
  },
  {
    field: 'forecastType',
    className: 'vxe-table-multi-cell',
    title: i18n.t('类型'),
    slots: {
      default: 'forecastTypeDefault'
      // 使用 JSX 渲染
      // default: ({ row }) => {
      //   return [
      //     <div>
      //       {ForecastTypeDataSource.map((item) => {
      //         return <span>{item}</span>
      //       })}
      //     </div>
      //   ]
      // }
    }
  }
]

export const lastColumns = [
  {
    field: 'remark',
    title: i18n.t('备注')
    // formatter: ({ row }) => {
    //   return `${row.virtualSupplierCode}-${row.virtualSupplierName}`
    // },
    // editRender: {},
    // slots: {
    //   edit: 'virtualSupplierCodeEdit'
    // }
  }
]
