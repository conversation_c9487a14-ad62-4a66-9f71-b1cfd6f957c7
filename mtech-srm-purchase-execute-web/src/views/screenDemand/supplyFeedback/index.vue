<template>
  <!-- 供应管理-供方 -->
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      default-max-height="60"
      @reset="handleCustomReset"
      @search="handleCustomSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.itemCode" />
        </mt-form-item>
        <mt-form-item prop="supplierItemCode" :label="$t('供应商物料编码')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.supplierItemCodes"
            :data-source="screenSupplierItemList"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <!-- <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="top">
          <mt-select
            style="flex: 1"
            v-model="searchFormModel.supplierCode"
            :data-source="screenSupplierList"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item> -->
        <mt-form-item prop="deliverAddr" :label="$t('出货地点')" label-style="top">
          <!-- <mt-input style="flex: 1" v-model="searchFormModel.sendAddressName" /> -->
          <mt-select
            style="flex: 1"
            v-model="searchFormModel.deliverAddr"
            :data-source="deliverAddrOptions"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.itemName" />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('类型')" label-style="top">
          <!-- <mt-multi-select
            style="flex: 1"
            v-model="forecastTypes"
            :data-source="forecastTypeOptions"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select> -->
          <div class="radio-checkbox-status">
            <mt-radio
              v-model="searchFormModel.srowType"
              :data-source="[
                {
                  label: this.$t('S(供应)'),
                  value: '0'
                },
                {
                  label: this.$t('S(供应)(上一版)'),
                  value: '1'
                }
              ]"
            ></mt-radio>
            <div class="radio-checkbox-status__checkbox">
              <mt-checkbox
                v-model="searchFormModel.frowFlag"
                @change="(e) => handleChange(e, 'frowFlag')"
                :label="$t('F(预测)')"
                style="vertical-align: sub; display: inline-block"
              />
              <mt-checkbox
                v-model="searchFormModel.browFlag"
                @change="(e) => handleChange(e, 'browFlag')"
                :label="$t('B(累计差额)')"
                style="vertical-align: sub; display: inline-block"
              />
            </div>
          </div>
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.categoryCode" />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        :row-config="{ height: 73 }"
        :columns="columns"
        :table-data="tableData"
        show-overflow
        height="auto"
        header-align="left"
        align="left"
        style="padding-top: unset"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true,
          activeMethod: this.activeRowMethod
        }"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        @edit-closed="editComplete"
        @edit-actived="editBegin"
        @edit-disabled="editDisabledEvent"
      >
        <template slot="custom-tools">
          <!-- :icon="item.icon" -->
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
          <span style="margin-left: 10px"
            >{{ $t('大版本号') }}：{{ versionInfo.bigVersionNo }}
          </span>
          <span style="margin-left: 10px"
            >{{ $t('小版本号') }}：{{ versionInfo.ssmallVersionNo }}
          </span>
          <span style="margin-left: 10px"
            >{{ $t('版本日期') }}：{{ versionInfo.srowVersionDate }}</span
          >
        </template>
        <template #factoryCodeEdit="{ row }">
          <vxe-select
            v-model="row.logicFactory"
            :placeholder="$t('请选择工厂')"
            :options="siteOptions"
            transfer
            filterable
            @change="(value) => factoryChange(value, row)"
          ></vxe-select>
        </template>
        <template #itemCodeEdit="{ row }">
          <vxe-pulldown ref="xDownItem" transfer>
            <template #default>
              <vxe-input
                :value="row.itemCode"
                :placeholder="$t('请选择物料')"
                readonly
                @click="(e) => focusItemCode(e, row)"
              ></vxe-input>
            </template>
            <template #dropdown>
              <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                @keyup="(e) => keyupItemCode(e, row)"
                style="width: 100%"
              ></vxe-input>
              <vxe-list height="200" class="predict-vxe-dropdown" :data="itemOptions" auto-resize>
                <template #default="{ items }">
                  <div
                    v-show="itemOptions.length"
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.value"
                    @click="selectItemCode(item, row)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                  <div v-show="!itemOptions.length" class="predict-vxe-list-item">
                    <span>{{ $t('暂无数据') }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template>
        <template #supplierCodeEdit="{ row }">
          <vxe-pulldown ref="xDown" transfer>
            <template #default>
              <vxe-input
                :value="row.supplierCode"
                :placeholder="$t('请选择供应商')"
                readonly
                @click="focusSupplierCode"
              ></vxe-input>
            </template>
            <template #dropdown>
              <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                @keyup="keyupSupplierCode"
                style="width: 100%"
              ></vxe-input>
              <vxe-list
                height="200"
                class="predict-vxe-dropdown"
                :data="supplierOptions"
                auto-resize
              >
                <template #default="{ items }">
                  <div
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.value"
                    @click="selectSupplierCode(item, row)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template>
        <template #deliverAddrEdit="{ row }">
          <vxe-select
            v-model="row.deliverAddr"
            :placeholder="$t('请选择出货地点')"
            :options="deliverAddrOptions"
            transfer
            filterable
          ></vxe-select>
        </template>
        <template #forecastTypeDefault="{ row }">
          <div v-for="(item, index) in forecastTypeOptions" :key="item">
            <div v-if="index === 0 && row.frow && row.frow.length" class="vxe-cell-border">
              {{ item }}
            </div>
            <!-- <div
              v-if="index === 1 && row.srow && row.srow.length && srowType === '0'"
              class="vxe-cell-border"
            >
              {{ item }}
            </div> -->
            <div v-if="index === 1 && row.srow && row.srow.length" class="vxe-cell-border">
              {{ item }}
            </div>
            <div v-if="index === 2 && row.brow && row.brow.length" class="vxe-cell-border">
              {{ item }}
            </div>
          </div>
        </template>
        <template #rowVersionDateDefault="{ row }">
          <div>
            <div v-if="row.frow && row.frow.length" class="vxe-cell-border">
              {{ row.frowVersionDate || '-' }}
            </div>
            <div v-if="row.srow && row.srow.length" class="vxe-cell-border">
              {{ row.srowVersionDate || '-' }}
            </div>
            <div v-if="row.brow && row.brow.length" class="vxe-cell-border">
              {{ row.browVersionDate || '-' }}
            </div>
          </div>
        </template>
      </ScTable>
    </div>
    <!-- <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    /> -->
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import {
  StatusSearchOptions,
  ForecastTypeDataSource,
  ForecastColumnData,
  lastColumn,
  ToolBar
} from './config/constant'
import * as UTILS from '@/utils/utils'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      toolbar: ToolBar,
      siteOptions: [], // 工厂下拉选项
      deliverAddrOptions: [], // 出货地点下拉选项
      supplierOptions: [],
      getSupplierDataSource: () => {}, // 供应商 下拉选项
      itemOptions: [],
      getItemDataSource: () => {},
      apiWaitingQuantity: 0, // 调用的api正在等待数
      searchFormModel: {
        srowType: '0',
        frowFlag: false,
        browFlag: false
      },
      titleList: [],
      tableData: [],
      columns: ForecastColumnData,
      isEditing: false, // 正在编辑数据
      exportRules: [], //导出规则
      statusOptions: StatusSearchOptions,
      forecastTypeOptions: ForecastTypeDataSource,
      copySearchFormModel: null,
      forecastTypes: [],
      isEdit: false,
      versionInfo: {
        bigVersionNo: '',
        srowVersionDate: '',
        ssmallVersionNo: ''
      },
      screenSupplierList: [],
      screenSupplierItemList: [],
      srowType: '0'
    }
  },
  mounted() {
    // this.getScreenSupplier()
    this.getSyncLatestVersion()
    this.getScreenAddrAll()
    // this.getSite()
    // this.getSupplierDataSource = utils.debounce(this.getSupplier, 1000)
    // this.getItemDataSource = utils.debounce(this.getItem, 1000)
    // this.getSupplierDataSource({ value: '' })
  },
  methods: {
    getScreenSupplierItem() {
      this.$API.screenDemand
        .getScreenSupplierItemCode({ bigVersionNo: this.versionInfo.bigVersionNo })
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.screenSupplierItemList = []
            data.forEach((i) => {
              if (i.supplierItemCode && i.itemName) {
                this.screenSupplierItemList.push({
                  text: `${i.supplierItemCode}-${i.itemName}`,
                  value: i.supplierItemCode
                })
              }
            })
          }
        })
    },
    getScreenAddrAll() {
      this.$API.screenDemand.getScreenAddrAll().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.deliverAddrOptions = data?.map((i) => {
            return {
              label: i,
              text: i,
              value: i
            }
          })
        }
      })
    },
    getSyncLatestVersion() {
      this.$API.screenDemand.syncLatestVersion().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.versionInfo = data
          this.getScreenSupplierItem()
        }
      })
    },
    getScreenSupplier() {
      this.$API.screenDemand.getScreenSupplySupplier().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.screenSupplierList = data.map((i) => {
            return {
              text: `${i.supplierCode}-${i.supplierName}`,
              value: i.supplierCode
            }
          })
        }
      })
    },
    handleChange(e, labelName) {
      this.searchFormModel[labelName] = e.checked
    },
    focusSupplierCode() {
      this.$refs.xDown.showPanel()
    },
    keyupSupplierCode(e) {
      this.getSupplierDataSource(e)
    },
    selectSupplierCode(e, row) {
      row.supplierCode = e.supplierCode
      row.supplierName = e.supplierName
      row.supplierId = e.id
    },
    focusItemCode(e, row) {
      if (!row.logicFactory) {
        this.$toast({ content: this.$t('请先选择工厂'), type: 'warning' })
        return false
      }
      this.$refs.xDownItem.showPanel()
    },
    keyupItemCode(e, row) {
      this.getItemDataSource(e, row)
    },
    selectItemCode(e, row) {
      row.itemCode = e.itemCode
      row.itemName = e.itemName
      row.itemId = e.id
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      // const commonToolbar = [
      //   'ForecastUpdate',
      //   'ForecastAdd',
      //   'ForecastImport',
      //   'Filter',
      //   'ForecastDelete',
      //   'ForecastPublish',
      //   'ForecastConfirm',
      //   'Refresh',
      //   'refreshDataByLocal',
      //   'filterDataByLocal',
      //   'CloseEdit',
      //   'resetDataByLocal',
      //   'ForecastExport',
      //   'Setting'
      // ]
      if (code === 'CloseEdit') {
        // 清除编辑状态
        this.$refs.xTable.$refs.xGrid.clearEdit()
        this.handleCustomSearch()
        return
      }
      if (this.isEdit) {
        // this.$toast({ content: this.$t('请先完成或结束编辑操作'), type: 'warning' })
        return
      }
      // if (!this.tableData.length && code !== 'ForecastImport') {
      //   this.$toast({ content: this.$t('请先查询数据再进行操作'), type: 'warning' })
      //   return
      // }

      // if (selectedRecords.length == 0 && !commonToolbar.includes(code)) {
      //   this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      //   return
      // }
      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })
      if (code === 'ForecastFeedback') {
        // 确认
        this.postBuyerForecastFeedback(idList)
      } else if (code === 'ForecastImport') {
        // // 导入
        // this.$refs.importDialog.init({
        //   title: this.$t('导入')
        // })
        this.$dialog({
          modal: () => import('@/components/uploadDialog'),
          data: {
            title: this.$t('导入'),
            importApi: this.$API.screenDemand.screenDemandSupplySupplierImport,
            downloadTemplateApi: this.$API.screenDemand.screenDemandSupplySupplierImportTemplate,
            paramsKey: 'excel'
            // saveButtonText: this.$t('下一步')
            // asyncParams: {
            //   // requestJson: JSON.stringify(parameter),
            // },
          },
          success: () => {
            // 导入之后刷新列表
            // this.$refs.templateRef.refreshCurrentGridData()
            this.handleCustomSearch()
          }
        })
        return
      } else if (code === 'ForecastExport') {
        // 导出
        // if (selectedRecords.length == 0) {
        //   this.$toast({ content: this.$t('请先选择需要导出的数据'), type: 'warning' })
        //   return
        // }
        this.postBuyerForecastExport(selectedRecords)
      }
    },
    // 供方删除供应信息
    postBuyerForecastDelete(tvForecastIds) {
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerForecastDeleteTv({
          tvForecastIds,
          tvForecastQueryReq: { ...this.searchFormModel }
        })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 供方-获取供方供应信息列表
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方发布供应信息接口
    postBuyerForecastPublish(tvForecastIds) {
      this.apiStartLoading()
      this.$API.predictCollaboration
        .postBuyerForecastPublishTv({
          tvForecastIds,
          tvForecastQueryReq: { ...this.searchFormModel }
        })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 供方-获取供方供应信息列表
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方-导出
    postBuyerForecastExport() {
      const params = {
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      // this.apiStartLoading()
      this.$API.screenDemand.screenDemandSupplySupplierExport(params).then((res) => {
        this.$store.commit('endLoading')
        // this.apiEndLoading()

        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 供方确认信息
    postBuyerForecastFeedback() {
      this.apiStartLoading()
      this.$API.screenDemand
        .screenDemandSupplySupplierFeedback({
          ...this.searchFormModel
        })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.getSyncLatestVersion()
            // 供方-获取供方供应信息列表
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 主数据 供应商
    getSupplier(args) {
      const { value } = args
      const params = {
        fuzzyNameOrCode: value ? value : ''
      }
      this.$API.masterData.getSupplier(params).then((res) => {
        if (res) {
          const list = res?.data || []
          const newData = list.map((i) => {
            return {
              ...i,
              label: `${i.supplierCode}-${i.supplierName}`,
              value: i.supplierCode
            }
          })
          // this.supplierOptions = [...newData, ...this.supplierOptions]
          this.supplierOptions = [...newData]
        }
      })
    },
    // 主数据 物料
    getItem(args, row) {
      const { value } = args
      const params = {
        page: { current: 1, size: 100 },
        condition: 'or',
        rules: [
          {
            label: this.$t('物料编号'),
            field: 'itemCode',
            type: 'string',
            operator: 'contains',
            value
          },
          {
            label: this.$t('物料名称'),
            field: 'itemName',
            type: 'string',
            operator: 'contains',
            value
          }
        ],
        defaultRules: [
          {
            field: 'organizationCode',
            operator: 'equal',
            value: row.logicFactory
          }
        ]
      }
      this.$API.masterData.getItemPage(params).then((res) => {
        if (res) {
          const list = res?.data?.records || []
          const newData = list.map((i) => {
            return {
              ...i,
              label: `${i.itemCode}-${i.itemName}`,
              value: i.itemCode
            }
          })
          // this.itemOptions = [...newData, ...this.itemOptions]
          this.itemOptions = [...newData]
        }
      })
    },
    factoryChange(val, row) {
      const { value } = val
      this.siteOptions.forEach((e) => {
        if (e.siteCode === value || e.organizationCode === value) {
          row.factoryId = e.id // id
          // row.logicFactory = e.organizationCode // code
          // row.factoryName = e.organizationName // name
          row.logicFactory = e.organizationCode ? e.organizationCode : e.siteCode // code
          row.factoryName = e.organizationName ? e.organizationName : e.siteName // name
          this.getItemDataSource({ value: '' }, row)
        }
      })
    },
    // 主数据 根据物料 查询关联的工厂列表
    getSite() {
      this.$API.masterData.getSiteFindByPermission().then((res) => {
        const list = res?.data || []
        this.siteOptions = list.map((i) => {
          return {
            ...i,
            label: `${i.siteCode}-${i.siteName}`,
            value: i.siteCode
          }
        })
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 校验点击的对象是否为取消编辑按钮
        if (args.$event.target.innerText === this.$t('取消编辑')) {
          // 清除编辑状态
          this.$refs.xTable.$refs.xGrid.clearEdit()
          this.handleCustomSearch()
          return
        }
        // 远程数据才有$event属性
        //1、 校验必填 没通过就是this.$refs.xTable.$refs.xGrid.setEditRow(row)
        // if (!this.isValidData(row)) {
        //   // 当出现错误时，指定行进入编辑状态
        //   this.$refs.xTable.$refs.xGrid.setEditRow(row)
        //   return
        // }
        //2、 判断是否有row.planner调新增或者编辑接口
        if (row.id && row.id.includes('row_')) {
          this.postBuyerForecastBatchInsert({ data: row })
        } else {
          this.postBuyerForecastSaveForecast({ data: row })
        }
        //3、 接口调用成功调刷新接口
      }
    },
    editBegin(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        this.getItemDataSource({ value: row.itemCode }, row)
        this.getSupplierDataSource({ value: row.supplierCode })
      } else {
        this.getSupplierDataSource({ value: '' })
      }
      this.isEdit = true
    },
    // 供方-无id即新增
    postBuyerForecastBatchInsert(args) {
      const { data } = args
      const tvForecastRecord = []
      if (this.titleList && this.titleList.length) {
        this.titleList.forEach((title) => {
          const item = data[`title_${title}`]['supplierNum']
          if (item) {
            tvForecastRecord.push(String(item))
          } else {
            tvForecastRecord.push('0')
          }
        })
      }
      const params = [
        {
          bgCode: data.bgCode,
          categoryCode: data.categoryCode,
          deliverAddr: data.deliverAddr,
          itemCode: data.itemCode,
          logicFactory: data.logicFactory,
          specCode: data.specCode,
          record: tvForecastRecord
        }
      ]
      this.apiStartLoading()
      this.$API.screenDemand
        .screenDemandSupplySupplierSave(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 供方-获取供方供应信息列表
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow(data)
        })
    },
    // 供方-修改供应信息
    postBuyerForecastSaveForecast(args) {
      const { data } = args
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认修改该数据？')
        },
        success: () => {
          const tvForecastRecord = []
          if (this.titleList && this.titleList.length) {
            this.titleList.forEach((title) => {
              const item = data[`title_${title}`]['supplierNum']
              if (item) {
                tvForecastRecord.push(String(item))
              } else {
                tvForecastRecord.push('0')
              }
            })
          }
          const params = [
            {
              id: data.id,
              deliverAddr: data.deliverAddr,
              record: tvForecastRecord
            }
          ]
          this.apiStartLoading()
          this.$API.screenDemand
            .screenDemandSupplySupplierUpdate(params)
            .then((res) => {
              this.apiEndLoading()
              if (res?.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                // 供方-获取供方供应信息列表
                this.handleCustomSearch()
              }
            })
            .catch(() => {
              this.apiEndLoading()
              // 当出现错误时，指定行进入编辑状态
              this.$refs.xTable.$refs.xGrid.setEditRow(data)
            })
        },
        close: () => {
          this.$refs.xTable.$refs.xGrid.clearEdit()
          this.handleCustomSearch()
        }
      })
    },
    activeRowMethod({ row }) {
      // 已确认的数据不可修改
      if (row.status === 3) {
        return false
      }
      return true
    },
    editDisabledEvent() {
      this.$toast({
        content: this.$t('此状态数据不可编辑'),
        type: 'warning'
      })
    },
    // 校验数据
    isValidData(data) {
      const { itemCode, logicFactory, supplierCode } = data
      let valid = false
      if (!logicFactory) {
        // 工厂代码
        this.$toast({ content: this.$t('工厂不可为空'), type: 'warning' })
      } else if (!itemCode) {
        // 物料代码
        this.$toast({ content: this.$t('物料不可为空'), type: 'warning' })
      } else if (!supplierCode) {
        // 供应商代码
        this.$toast({ content: this.$t('供应商不可为空'), type: 'warning' })
      } else {
        valid = true
      }

      return valid
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'srowType') {
            this.searchFormModel[key] = '0'
          }
          if (key === 'frowFlag') {
            this.searchFormModel[key] = false
          }
          if (key === 'browFlag') {
            this.searchFormModel[key] = false
          }
        }
      }
      this.forecastTypes = []
      this.handleCustomSearch()
    },
    // 供方-获取供方供应信息列表
    handleCustomSearch() {
      this.isEdit = false
      const params = {
        ...this.searchFormModel
      }
      // this.apiStartLoading()
      this.$API.screenDemand
        .screenDemandSupplySupplierQuery(params)
        .then((res) => {
          // this.apiEndLoading()
          if (res?.code == 200) {
            const records = res?.data || [] // 表格数据
            const titleList = res?.data[0]?.headers || [] // 动态表头数据
            const editableHeaders = res?.data[0]?.editableHeaders || [] // 动态表头数据
            this.titleList = res?.data[0]?.headers || [] // 动态表头数据
            // 处理表头数据
            this.srowType = this.searchFormModel.srowType
            this.columns = this.handleColumns({ editableHeaders, titleList })
            // 处理表数据
            this.tableData = this.handleDataSource({ records, titleList })
          }
        })
        .catch(() => {
          // this.apiEndLoading()
        })
    },
    handleColumns(data) {
      const { editableHeaders, titleList } = data
      // 固定的表头
      const forecastColumns = ForecastColumnData
      // 动态的日期表头
      const titleListColumnData = []
      titleList.forEach((item, index) => {
        const title = `title_${titleList[index]}`
        titleListColumnData.push({
          field: title,
          title: item,
          minWidth: 180,
          showOverflow: true,
          editRender: {},
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <div>
                  <div v-show={row.frow && row.frow.length} class='vxe-cell-border'>
                    {row[title]['planGroupNum']}
                  </div>
                  <div v-show={row.srow && row.srow.length} class='vxe-cell-border'>
                    {row[title]['supplierNum']}
                  </div>
                  <div v-show={row.brow && row.brow.length} class='vxe-cell-border'>
                    {row[title]['total']}
                  </div>
                </div>
              ]
            },
            edit: ({ row }) => {
              return [
                <div>
                  <div v-show={row.frow && row.frow.length} class='vxe-cell-border'>
                    {row[title]['planGroupNum']}
                  </div>
                  <div v-show={row.srow && row.srow.length} class='vxe-cell-border'>
                    <mt-input-number
                      v-show={editableHeaders.includes(item)}
                      min={0}
                      precision={'0'}
                      show-spin-button={false}
                      show-clear-button={false}
                      v-model={row[title]['supplierNum']}
                    />
                    <div v-show={!editableHeaders.includes(item)} class='vxe-cell-border'>
                      {row[title]['supplierNum']}
                    </div>
                  </div>
                  <div v-show={row.brow && row.brow.length} class='vxe-cell-border'>
                    {row[title]['total']}
                  </div>
                </div>
              ]
            }
          }
        })
      })

      // 合并表头数组，然后 push 到 forecastColumnData 中
      const columns = [].concat(forecastColumns).concat(titleListColumnData).concat(lastColumn)
      return columns
    },
    handleDataSource(data) {
      const { records, titleList } = data
      const tableData = []
      records.forEach((recordsItem) => {
        const rowData = {
          ...recordsItem.tvScreenSupplyAggregation,
          srow: recordsItem.srow,
          brow: recordsItem.brow,
          frow: recordsItem.frow
        }
        // recordsItem.thePrimaryKey = recordsItem.tvForecastIntegration.id // 主键的值为 API 数据的 id
        // recordsItem.status = recordsItem.tvForecastIntegration.status ?? 0 // 状态为 undefined/null 时，默认为新建状态（导入时）

        const srow = recordsItem?.srow
        const brow = recordsItem?.brow
        const frow = recordsItem?.frow
        titleList.forEach((itemTitle, index) => {
          srow.forEach(() => {
            // 将供应数据赋给表头对应的对象
            const forecastItem = {
              total: brow && brow[index] ? brow[index] : 0,
              planGroupNum: frow && frow[index] ? frow[index] : 0,
              supplierNum: srow && srow[index] ? srow[index] : 0
            }
            rowData[`title_${itemTitle}`] = forecastItem
          })
        })
        tableData.push(rowData)
      })

      return tableData
      // return records
    },
    // 分页 页面
    // handleCurrentChange(currentPage) {
    //   this.forecastPageCurrent = currentPage
    //   this.handleCustomSearch()
    // },
    // // 分页 页数据量
    // handleSizeChange(pageSize) {
    //   this.forecastPageSettings.pageSize = pageSize
    //   this.handleCustomSearch()
    // },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  },
  beforeRouteLeave(to, from, next) {
    this.$refs.xTable.$refs.xGrid.clearEdit()
    next()
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  div .vxe-cell-border {
    border: solid #e6e9ed 1px;
  }
}
/deep/ .mt-form {
  .radio-checkbox-status {
    display: flex;
    &__checkbox {
      display: flex;
      flex-direction: column;
    }
    .e-label {
      white-space: nowrap;
    }
  }
}
/deep/ .col--seq,
/deep/ .col--checkbox {
  .c--tooltip {
    padding: 0 10px;
  }
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
