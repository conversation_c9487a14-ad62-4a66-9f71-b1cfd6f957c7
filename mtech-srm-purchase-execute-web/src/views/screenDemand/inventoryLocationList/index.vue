<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="siteCode" :label="$t('工厂代码')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.siteCode"
                :url="$API.masterData.getSiteListUrl"
                multiple
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                :search-fields="['siteName', 'siteCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="deliveryAddrCode" :label="$t('交货地点代码')" label-style="top">
              <mt-input
                v-model="deliveryAddrCode"
                @change="() => (searchFormModel.deliveryAddrCode = [deliveryAddrCode])"
                :show-clear-button="true"
                :placeholder="$t('请输入交货地点代码')"
              />
            </mt-form-item>
            <mt-form-item prop="consigneeName" :label="$t('收货人')" label-style="top">
              <mt-input
                v-model="consigneeName"
                @change="() => (searchFormModel.consigneeName = [consigneeName])"
                :show-clear-button="true"
                :placeholder="$t('请输入收货人')"
              />
            </mt-form-item>
            <mt-form-item prop="processorCode" :label="$t('加工商')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.processorCode"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择加工商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="rbcCode" :label="$t('RBC编码')" label-style="top">
              <mt-input
                v-model="rbcCode"
                @change="() => (searchFormModel.rbcCode = [rbcCode])"
                :show-clear-button="true"
                :placeholder="$t('请输入RBC编码')"
              />
            </mt-form-item>
            <mt-form-item prop="bgType" :label="$t('BG类型')" label-style="top">
              <mt-input
                v-model="bgType"
                @change="() => (searchFormModel.bgType = [bgType])"
                :show-clear-button="true"
                :placeholder="$t('请输入BG类型')"
              />
            </mt-form-item>
            <!-- <mt-form-item prop="authorizeCertStatus" :label="$t('状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.authorizeCertStatus"
                css-class="rule-element"
                :data-source="[
                  { value: '0', text: '新增', cssClass: 'col-active' },
                  { value: '1', text: '审批中', cssClass: 'col-active' },
                  { value: '2', text: '审批通过', cssClass: 'col-active' },
                  { value: '3', text: '驳回', cssClass: 'col-inactive' }
                ]"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item> -->
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { pageConfig, columnData } from './config'
import * as UTILS from '@/utils/utils'

export default {
  components: {},
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      deliveryAddrCode: '',
      consigneeName: '',
      rbcCode: '',
      bgType: '',
      pageConfig: [],
      indicatorTypeList: [], // 自动发计算指标类型列表
      categoryList: [], // 类别列表
      templateIndexMap: [] // 指标名称列表
    }
  },
  computed: {},
  created() {
    this.pageConfig = pageConfig(this)
  },
  methods: {
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selectedRecords = grid.getSelectedRecords()

      if (toolbar.id === 'Add') {
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id === 'closeEdit') {
        this.$refs.templateRef.refreshCurrentGridData()
      } else if (toolbar.id === 'Delete') {
        this.handleDelete(selectedRecords)
      } else if (toolbar.id === 'Import') {
        this.$dialog({
          modal: () => import('@/components/uploadDialog'),
          data: {
            title: this.$t('导入'),
            importApi: this.$API.screenDemand.screenWarehouseImport,
            downloadTemplateApi: this.$API.screenDemand.screenWarehouseImportTemp,
            paramsKey: 'excel'
            // asyncParams: {
            //   // requestJson: JSON.stringify(parameter),
            // },
          },
          success: () => {
            // 导入之后刷新列表
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      } else if (toolbar.id === 'Export1') {
        this.handleExport(selectedRecords)
      }
    },
    handleDelete(selectedRecords) {
      const idList = selectedRecords.map((i) => i.id)
      if (idList.length) {
        this.$dialog({
          data: {
            title: this.$t('提醒'),
            message: this.$t('是否确定删除已选数据?')
          },
          success: () => {
            // 没勾选就不传参数全部删除
            this.deleteFunc(idList)
          }
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('是否确定全部删除?')
        },
        success: () => {
          // 没勾选就不传参数全部删除
          this.deleteFunc([])
        }
      })
    },
    deleteFunc(idList) {
      const params = {
        idList,
        ruleReq: {
          ...this.searchFormModel,
          page: {
            current: 1,
            size: 9999
          }
        }
      }
      this.$API.screenDemand.screenWarehouseDelete(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    handleExport(idList) {
      const params = {
        idList: idList.map((i) => i.id),
        ruleReq: {
          ...this.searchFormModel,
          page: {
            current: 1,
            size: 9999
          }
        }
      }
      this.$store.commit('startLoading')
      this.$API.screenDemand.screenWarehouseExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 重置查询条件
    handleCustomReset() {
      this.deliveryAddrCode = ''
      this.consigneeName = ''
      this.rbcCode = ''
      this.bgType = ''
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    actionBegin(args) {
      const { requestType, data } = args
      if (requestType === 'save') {
        for (let i = 0; i < columnData().length; i++) {
          const elem = columnData()[i]
          if (elem.headerTemplate && !data[elem.field]) {
            this.$toast({ content: this.$t(`请输入${elem.headerText}`), type: 'warning' })
            args.cancel = true
            return
          }
        }
      }
    },
    actionComplete(args) {
      const { requestType, data, rowIndex } = args
      if (requestType === 'save') {
        !args.cancel && this.handleSave(data, rowIndex)
      }
    },
    startEdit(index) {
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
    },
    // 保存
    handleSave(data, index) {
      const params = {
        ...data,
        siteCode: data.factoryCode,
        processorCode: data.plusBizCirclesCode,
        receiveAddrCode: data.warehouseCode,
        receiveAddrDesc: data.warehouseName
      }
      this.getSaveApi(data.id)(params)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          } else {
            this.startEdit(index)
          }
        })
        .catch(() => {
          this.startEdit(index)
        })
    },
    getSaveApi(id) {
      if (id) {
        return this.$API.screenDemand.screenWarehouseUpdate
      }
      return this.$API.screenDemand.screenWarehouseAdd
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  /deep/ .template-wrap {
    overflow-y: auto;
  }
}
</style>
