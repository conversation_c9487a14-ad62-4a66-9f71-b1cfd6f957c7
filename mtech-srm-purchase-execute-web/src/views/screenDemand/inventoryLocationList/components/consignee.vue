<template>
  <div>
    <div class="grid-edit-column mt-flex-direction-column">
      <mt-input
        :id="data.column.field"
        v-model="data[data.column.field]"
        style="display: none"
      ></mt-input>
      <div class="field-content" v-if="!allowEditing">
        <span>{{ data[data.column.field] }}</span>
      </div>
      <div class="field-content" v-else>
        <debounce-filter-select
          :open-dispatch-change="false"
          @change="selectChange"
          v-model="data[data.column.field]"
          :request="getUser"
          :popup-width="'350'"
          :data-source="acceptorOptions"
          :show-clear-button="false"
          :placeholder="$t('请选择')"
          :fields="{ text: 'text', value: 'text' }"
        ></debounce-filter-select>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      data: {},
      acceptorOptions: [],
      allowEditing: true
    }
  },
  mounted() {
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    if (!this.data[this.data.column.field]) {
      this.getUser({ text: '' })
    }
    if (this.data[this.data.column.field]) {
      let text1 = this.data[this.data.column.field].split('-')[3]
      this.getUser({ text: text1 })
    }
  },
  methods: {
    getUser(e) {
      const { text: fuzzyName } = e
      this.acceptorOptions = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.employeeName}`,
            value: item.uid,
            name: item.employeeName
          })
        })
        this.acceptorOptions = tmp
      })
    },
    selectChange(e) {
      // this.$parent.$emit('selectedChanged', {
      //   fieldCode: this.data.column.field,
      //   itemInfo: {
      //     employeeName: e.itemData.name,
      //     userId: e.itemData.value
      //   }
      // })
      this.$bus.$emit('changeConsignee', {
        consigneeId: e.itemData.uid,
        contactNo: e.itemData.phoneNum
      })
    }
  }
}
</script>
