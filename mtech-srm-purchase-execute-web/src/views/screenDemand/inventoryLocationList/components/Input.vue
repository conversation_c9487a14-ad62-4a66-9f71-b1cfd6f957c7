<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="isDisabled"
      v-if="maxlength"
      :maxlength="maxlength"
    ></mt-input>
  </div>
</template>
<script>
export default {
  data() {
    return { data: {}, isDisabled: false, maxlength: 0 }
  },
  mounted() {
    this.maxlength = this.data.column.maxlength || 128
    if (
      this.data.column.field === 'scheduleArea' ||
      this.data.column.field === 'saleOrder' ||
      this.data.column.field === 'saleOrderRowCode' ||
      this.data.column.field === 'processName' ||
      this.data.column.field === 'productCode' ||
      this.data.column.field === 'buyerOrder' ||
      this.data.column.field === 'buyerOrderRowCode' ||
      this.data.column.field === 'warehouseKeeper'
    ) {
      //计划区域 关联工单号
      this.setDisabled()
    }
    if (
      this.data.column.field === 'projectTextBatch' ||
      this.data.column.field === 'associatedNumber' ||
      this.data.column.field === 'bom'
    ) {
      //项目文本批次 关联工单号 bom号
      this.setDisabled1()
    }
    if (this.data.column.field === 'contactNo') {
      // this.isDisabled = true
      this.$bus.$on('changeConsignee', (e) => {
        this.data.contactNo = e.contactNo
        this.data.consigneeId = e.consigneeId
      })
    }
    if (this.data.column.field === 'warehouseName') {
      // this.isDisabled = true
      this.$bus.$on('warehouseNameChange', (e) => {
        this.data.warehouseName = e
        this.$set(this.data, 'warehouseName', e)
      })
    }
  },
  methods: {
    setDisabled1() {
      if (this.data.status === 4) {
        this.isDisabled = true
      } else {
        this.isDisabled = false
      }
    },
    setDisabled() {
      if (this.data.isEntry === '1') {
        this.isDisabled = true
      }
      if (this.data.isEntry === '2') {
        this.isDisabled = false
      }
    }
  }
}
</script>
