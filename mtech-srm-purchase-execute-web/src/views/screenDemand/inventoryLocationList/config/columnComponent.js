import Vue from 'vue'

import { timeNumberToDate, timeStringToDate, addCodeNameKeyInList } from '@/utils/utils'
import { rowDataTemp } from './variable'

import { cloneDeep } from 'lodash'
import {} from '@/utils/utils'
import { utils } from '@mtech-common/utils'

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 行编辑触发的 change 事件类型
export const ComponentChangeType = {
  code: 'code', // 例：下拉框选择“供应商”，供应商编码（code)要被修改
  link: 'link' // 例：计划组：根据物料+工厂+计划组的业务组类型 code 带出
}

export const ColumnComponent = {
  // 文本 可编辑、可监听关联、可带搜索按钮、可格式化显示
  inputText: (args) => {
    const {
      dataKey,
      showClearBtn,
      disabled,
      maxlength,
      isItem,
      isListenChange,
      hasSearch,
      format
    } = args
    const template = () => {
      return {
        template: Vue.component('inputComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <div :class="[hasSearch && 'input-search-content']">
              <div>
              <mt-select
              :popup-width="450"
              v-if="hasSearch===true"
              :data-source="itemList"
              :filtering="serchText"
              :disabled='disableConverter'
              @change="itemClick"
              :show-clear-button="true"
              :allow-filtering="true"
              @open="startOpen"
              :fields="{ text: 'label', value: 'itemCode' }"
              v-model="data.itemCode"
              width="100%"
            ></mt-select>
            <mt-input
            v-model="componentData"
            autocomplete="off"
            v-if='hasSearch === false'
            :show-clear-button="showClearBtn"
            :disabled="disableConverter"
            :maxlength="maxlength"
            @input="onInput"
          ></mt-input>
            <mt-icon
            v-show='!disableConverter'
            v-if="hasSearch===true"
            name="icon_input_search"
            @click.native="handleSearch"
          ></mt-icon>


              </div>
              </div>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              itemList: [],
              isItem,
              disableConverter: false,
              disabled,
              showClearBtn,
              maxlength,
              organizationCode: null,
              hasSearch,
              componentData: null // 组件内部绑定的变量
            }
          },
          //   <mt-icon
          //   v-if="hasSearch"
          //   name="icon_input_search"
          //   @click.native="handleSearch"
          // ></mt-icon>
          mounted() {
            if (disabled) {
              this.disableConverter = disabled
            }
            // if (this.data.status === Status.abnormal || this.data.status === Status.normal) {
            //   this.disableConverter = true
            // }
            if ((dataKey === 'batchCode' || dataKey === 'itemCode') && this.data.id) {
              this.disableConverter = true
            }

            if (isListenChange) {
              // 监听变化
              this.onComponentChange()
            }
            if (this.isItem === true) {
              this.$bus.$on('itemCodeUpdateBudgetUnitPrice', (e) => {
                // this.hasSearch = false
                let itemCode = ''
                if (e && e.data && e.data[0] && e.data[0].itemCode) {
                  itemCode = e.data[0].itemCode
                }
                this.init(itemCode)
              })
            }

            // this.init(this.data.itemCode);
            if (hasSearch) {
              this.$bus.$on('siteCodeChangeClick', (e) => {
                this.organizationCode = e.siteCode
                if (this.data.itemCode === null) {
                  this.init('')
                } else {
                  this.init(this.data.itemCode)
                }
              })
              // this.$bus.$on("itemCodeConfirm", (e) => {
              //   this.hasSearch = false;
              //   this.init(e.data.data.itemCode);
              // });
            }

            this.doFormat()
            // this.doDisableConverter();
          },
          beforeDestroy() {
            if (isListenChange) {
              // 移除监听
              this.$bus.$off('purchaseJitColumnChange')
            }
          },
          methods: {
            serchText(e) {
              this.init(e.text)
            },
            itemClick(e) {
              if (e && e.e !== null) {
                this.$bus.$emit('itemCodeUpdateBudgetUnitPrice', {
                  data: [e.itemData]
                })
              }

              // this.$parent.$emit("confirm", { data: [...e.itemData] });
            },
            startOpen() {
              if (this.organizationCode == null) {
                this.$toast({
                  content: this.$t('请先选择工厂'),
                  type: 'warning'
                })
              }
            },
            init(e) {
              let itemObj = {
                page: {
                  current: 1,
                  pages: 6,
                  size: 20
                },
                defaultRules: [
                  {
                    field: 'organizationCode',
                    operator: 'equal',
                    value: this.organizationCode
                  }
                  // {
                  //   field: 'jitFlag',
                  //   operator: 'equal',
                  //   value: 1
                  // }
                ],

                rules: [
                  {
                    field: 'itemCode',
                    operator: 'contains',
                    value: e
                  }
                ]
              }
              this.$API.masterData.getItemPage(itemObj).then((res) => {
                res.data.records.forEach((item) => {
                  item.label = item.itemCode + '-' + item.itemName
                })
                const list = res?.data.records || []
                this.itemList = [...list]
                this.hasSearch = true
              })
            },
            onInput(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e
            },
            // 监听变化
            onComponentChange() {
              // 监听被变化
              this.$bus.$on(`purchaseJitColumnChange`, (e) => {
                const { modifiedKeys, data, changeType } = e
                if (
                  modifiedKeys.includes(this.dataKey) &&
                  changeType === ComponentChangeType.code
                ) {
                  // 发布事件的数据修改了，关联值修改
                  const newData = cloneDeep(data[this.dataKey])
                  this.data[dataKey] = newData
                  rowDataTemp[rowDataTemp.length - 1][dataKey] = newData

                  if (this.dataKey === 'itemCode') {
                    // 物料的行字段
                    this.data.itemName = data.itemName
                    this.data.itemId = data.itemId

                    rowDataTemp[rowDataTemp.length - 1]['itemName'] = data.itemName
                    rowDataTemp[rowDataTemp.length - 1]['itemId'] = data.itemId
                  }
                  this.doFormat()
                }
              })
            },
            // 点击搜索
            handleSearch() {
              this.$parent.$emit('handleSearch', {
                dataKey,
                data: rowDataTemp[rowDataTemp.length - 1]
              })
            },
            doFormat() {
              if (format && typeof format === 'function') {
                // 格式化显示
                this.componentData = format(this.data)
              } else {
                this.componentData = this.data[dataKey]
              }
            }
            // doDisableConverter() {
            //   if (disableConverter && typeof disableConverter === "function") {
            //     // 禁用转换
            //     if (hasSearch) {
            //       this.hasSearch = !disableConverter(this.data);
            //     } else {
            //       this.disabled = disableConverter(this.data);
            //     }
            //   }
            // },
          }
        })
      }
    }
    return template
  },
  // 下拉框 编辑
  select: (args) => {
    const {
      dataKey,
      selectOptions,
      fields,
      allowFiltering,
      showClearBtn,
      modifiedKeys,
      modifiedRelation,
      disabled,
      disableConverter
    } = args
    const template = () => {
      return {
        template: Vue.component('selectComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-select
                @change="selectChange"
                v-model="data[dataKey]"
                :allow-filtering="allowFiltering"
                :data-source="selectOptions"
                :show-clear-button="showClearBtn"
                :placeholder="$t('请选择')"
                :open-dispatch-change="false"
                :fields="fields"
                :disabled="disabled"
              ></mt-select>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              selectOptions,
              fields,
              dataKey,
              allowFiltering,
              showClearBtn,
              ComponentChangeType,
              disabled
            }
          },
          mounted() {
            this.doDisableConverter()
          },
          methods: {
            // 修改中的值
            selectChange(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e.value
              if (modifiedKeys?.length > 0) {
                // 触发改变的值
                this.triggerCodeChange(e.itemData)
              }
            },
            // 触发改变的值
            triggerCodeChange(selectData) {
              const args = {
                requestKey: dataKey, // 触发请求的key
                modifiedKeys: modifiedKeys || [], // 要被修改的key列表
                changeType: ComponentChangeType.code, // 修改类型
                data: selectData, // 发出的值
                modifiedRelation // 对应数据源中的 key 关系
              }
              this.$bus.$emit('purchaseJitColumnChange', args)
            },
            doDisableConverter() {
              if (disableConverter && typeof disableConverter === 'function') {
                // 禁用转换
                this.disabled = disableConverter(this.data)
              }
            }
          }
        })
      }
    }
    return template
  },
  // 空的显示
  empty: () => {
    return {
      template: Vue.component('emptyComponent', {
        template: `<div></div>`,
        data: function () {
          return {
            data: {}
          }
        }
      })
    }
  },
  // 不可编辑的文字显示 带 cellTools
  text: (args) => {
    const { dataKey, type, cellTools, valueConverter } = args
    const template = () => {
      return {
        template: Vue.component('textComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content"><span :class=[cssClass]>{{data[dataKey] | format}}</span></div>
            <div class="column-tool mt-flex invite-btn" v-if="haveShowCellTool && type === ComponentType.view">
              <div v-for="(cellTool, index) in cellTools"
                :key="index" :id="cellTool.id"
                class="template-svg"
                v-if="!cellTool.visibleCondition || cellTool.visibleCondition(data)"
                v-permission="cellTool.permission"
                @click.stop="clickCellTool({tool:cellTool, data})"
                ><MtIcon :name="cellTool.icon" />&nbsp<span class="icon-title">{{cellTool.title}}</span>
              </div>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              type,
              ComponentType,
              cellTools,
              haveShowCellTool: false,
              cssClass: ''
            }
          },
          mounted() {
            // 判断是否显示cellTool
            if (cellTools?.length > 0) {
              for (let i = 0; i < cellTools.length; i++) {
                const cellTool = cellTools[i]
                if (!cellTool.visibleCondition || cellTool.visibleCondition(this.data)) {
                  this.haveShowCellTool = true
                  break
                }
              }
            }
            // 设置 cssClass
            if (valueConverter?.type === 'map' && valueConverter?.map?.length) {
              const mapList = valueConverter.map
              const findItem = mapList.find((item) => item.value === this.data[dataKey])
              this.cssClass = findItem?.cssClass
            }
          },
          filters: {
            format: (value) => {
              let data = value
              if (valueConverter?.type === 'map' && valueConverter?.map?.length) {
                // 转换
                const mapList = valueConverter.map
                const findItem = mapList.find((item) => String(item.value) === String(value))
                data = findItem?.text
              }
              return data
            }
          },
          methods: {
            clickCellTool(data) {
              this.$parent.$emit('handleClickCellTool', data)
            }
          }
        })
      }
    }
    return template
  },
  // 时间显示
  timeDate: (args) => {
    const { dataKey, isDateTime, isDate, isTime } = args

    const template = () => {
      return {
        template: Vue.component('timeDateComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <span v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</span>
              <span v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</span>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              isDateTime,
              isDate,
              isTime
            }
          },
          filters: {
            dateFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({
                  formatString: 'YYYY-mm-dd',
                  value
                })
              } else {
                str = timeNumberToDate({
                  formatString: 'YYYY-mm-dd',
                  value
                })
              }

              return str
            },
            timeFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({
                  formatString: 'HH:MM:SS',
                  value
                })
              } else {
                str = timeNumberToDate({
                  formatString: 'HH:MM:SS',
                  value
                })
              }

              return str
            }
          }
        })
      }
    }

    return template
  },
  // 带红星的表头
  requiredHeader: (args) => {
    const { headerText } = args
    const template = () => {
      return {
        template: Vue.component('requiredHeaderComponent', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{ headerText }}</span>
              </div>
            `,
          data: function () {
            return {
              data: {},
              headerText
            }
          },
          beforeDestroy() {},
          mounted() {},
          methods: {}
        })
      }
    }
    return template
  },
  // 收货信息 下拉框选择 数据来源 交货地址配置表 不可模糊搜索
  senderAddressSelect: () => {
    return {
      template: Vue.component('senderAddressSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.senderAddress"
              :data-source="senderAddressOptions"
              :fields="{ text: 'theCodeName', value: 'senderAddress' }"
              :show-clear-button="true"
              :allow-filtering="false"
              @change="senderAddressChange"
              :placeholder="$t('请选择')"
              :open-dispatch-change="false"
              :disabled="disabled"
            ></mt-select>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            senderAddressOptions: [], // 收货信息 下拉选项
            disabled: false
          }
        },
        mounted() {
          // 监听变化
          this.onComponentChange()
          this.doDisableConverter()
        },
        beforeDestroy() {
          this.$bus.$off('purchaseJitColumnChange')
        },
        methods: {
          // 送货地址 change
          senderAddressChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].senderAddress = itemData.senderAddress // 送货地址
              rowDataTemp[rowDataTemp.length - 1].senderName = itemData.senderName // 送货联系人
              rowDataTemp[rowDataTemp.length - 1].senderPhone = itemData.senderPhone // 送货联系电话
            } else {
              rowDataTemp[rowDataTemp.length - 1].senderAddress = null // 送货地址
              rowDataTemp[rowDataTemp.length - 1].senderName = null // 送货联系人
              rowDataTemp[rowDataTemp.length - 1].senderPhone = null // 送货联系电话
            }
          },
          // 监听变化
          onComponentChange() {
            // 监听被变化
            this.$bus.$on(`purchaseJitColumnChange`, (e) => {
              const { modifiedKeys, data, changeType } = e
              if (
                modifiedKeys.includes('senderAddress') &&
                changeType === ComponentChangeType.link
              ) {
                // 修改下拉数据源
                this.senderAddressOptions = addCodeNameKeyInList({
                  firstKey: 'senderName',
                  secondKey: 'senderPhone',
                  thirdKey: 'senderAddress',
                  list: data.senderInfoList
                })
                this.data.senderName = null
                this.data.senderPhone = null
                this.data.senderAddress = null
                rowDataTemp[rowDataTemp.length - 1]['senderName'] = null
                rowDataTemp[rowDataTemp.length - 1]['senderPhone'] = null
                rowDataTemp[rowDataTemp.length - 1]['senderAddress'] = null
              } else if (
                modifiedKeys.includes('senderAddress') &&
                changeType === ComponentChangeType.code
              ) {
                // 通过 配置行id 修改选中的数据
                const beSetData = this.senderAddressOptions.find(
                  (item) => item.configId === data.configId
                )
                if (beSetData) {
                  this.data.senderName = beSetData.senderName // 送货联系人
                  this.data.senderPhone = beSetData.senderPhone // 送货联系电话
                  this.data.senderAddress = beSetData.senderAddress // 送货地址
                  rowDataTemp[rowDataTemp.length - 1].senderName = beSetData.senderName // 送货联系人
                  rowDataTemp[rowDataTemp.length - 1].senderPhone = beSetData.senderPhone // 送货联系电话
                  rowDataTemp[rowDataTemp.length - 1].senderAddress = beSetData.senderAddress // 送货地址
                } else {
                  this.data.senderName = null
                  this.data.senderPhone = null
                  this.data.senderAddress = null
                  rowDataTemp[rowDataTemp.length - 1]['senderName'] = null
                  rowDataTemp[rowDataTemp.length - 1]['senderPhone'] = null
                  rowDataTemp[rowDataTemp.length - 1]['senderAddress'] = null
                }
              }
            })
          },
          doDisableConverter() {
            // if (this.data.status === Status.abnormal || this.data.status === Status.normal) {
            //   // 反馈异常状态下只能修改 叫料数量（需求数量）
            //   this.disabled = true
            // }
          }
        }
      })
    }
  },
  // 加工商 code-name 显示 主数据 非必填 模糊搜索编号/名称
  // 选择 工作中心 后，根据 工作中心-加工商配置表 自动选中加工商
  processorCodeSelect: () => {
    return {
      template: Vue.component('processorCodeSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.processorCode"
              :filtering="doGetDataSource"
              :data-source="processorOptions"
              :fields="{ text: 'theCodeName', value: 'supplierCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              @change="processorCodeChange"
              :placeholder="$t('请选择')"
              :open-dispatch-change="false"
              :disabled="disabled"
            ></mt-select>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            processorOptions: [], // 加工商 下拉选项
            doGetDataSource: () => {},
            disabled: false
          }
        },
        mounted() {
          this.initGetProcessor()
          this.doGetDataSource = utils.debounce(this.getSupplier, 1000)
          // 监听变化
          this.onComponentChange()
          this.doDisableConverter()
        },
        beforeDestroy() {
          this.$bus.$off('purchaseJitColumnChange')
        },
        methods: {
          // 主数据 加工商 = 供应商
          getSupplier(args) {
            const { text, updateData, setSelectData } = args
            const params = {
              fuzzyNameOrCode: text
            }
            this.$API.masterData.getSupplier(params).then((res) => {
              if (res) {
                const list = res?.data || []
                this.processorOptions = addCodeNameKeyInList({
                  firstKey: 'supplierCode',
                  secondKey: 'supplierName',
                  list
                })
                if (updateData) {
                  this.$nextTick(() => {
                    updateData(this.processorOptions)
                  })
                }
                if (setSelectData) {
                  this.$nextTick(() => {
                    setSelectData()
                  })
                }
              }
            })
          },
          // 初始化检索 加工商 = 供应商
          initGetProcessor() {
            const processorCode = this.data.processorCode
            this.getSupplier({
              text: processorCode,
              setSelectData: () => {
                // api获取数据后重新赋值，防止没有赋上值得情况
                this.data.processorCode = processorCode
              }
            })
          },
          // 加工商 change
          processorCodeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].processorCode = itemData.supplierCode // code
              rowDataTemp[rowDataTemp.length - 1].processorName = itemData.supplierName // name
            } else {
              rowDataTemp[rowDataTemp.length - 1].processorCode = null // code
              rowDataTemp[rowDataTemp.length - 1].processorName = null // name
            }
          },
          // 监听变化
          onComponentChange() {
            // 监听被变化
            this.$bus.$on(`purchaseJitColumnChange`, (e) => {
              const { requestKey, modifiedKeys, data, changeType } = e
              if (
                requestKey === 'workCenterCode' &&
                modifiedKeys.includes('processorCode') &&
                changeType === ComponentChangeType.code
              ) {
                // 选择 工作中心 后，根据 工作中心-加工商配置表 自动选中加工商
                const workCenterCode = data.workCenterCode
                this.getProcessorByWorkCenter({ workCenterCode })
              }
            })
          },
          // 根据 工作中心获取 工作中心-加工商配置表 自动选中加工商
          getProcessorByWorkCenter(args) {
            const { workCenterCode } = args
            const params = {
              page: {
                current: 1,
                size: 10
              },
              condition: 'and',
              rules: [
                {
                  label: '工作中心',
                  field: 'workCenter',
                  operator: 'contains',
                  value: workCenterCode
                }
              ]
            }
            this.$API.deliverySchedule.workCenterSupplierRelquery(params).then((res) => {
              const configData = res?.data?.records || []
              if (configData[0]) {
                this.getSupplier({
                  text: configData[0].supplierCode,
                  setSelectData: () => {
                    // api获取数据后重新赋值，防止没有赋上值得情况
                    this.data.processorCode = configData[0].supplierCode // 加工商编码
                    this.data.processorName = configData[0].supplierName // 加工商名称
                    rowDataTemp[rowDataTemp.length - 1].processorCode = configData[0].supplierCode // 加工商编码
                    rowDataTemp[rowDataTemp.length - 1].processorName = configData[0].supplierName // 加工商名称
                  }
                })
              }
            })
          },
          doDisableConverter() {
            // if (this.data.status === Status.abnormal || this.data.status === Status.normal) {
            //   // 反馈异常状态下只能修改 叫料数量（需求数量）
            //   this.disabled = true
            // }
          }
        }
      })
    }
  }
}
