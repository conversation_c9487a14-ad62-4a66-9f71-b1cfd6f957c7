//现场评审得分设置Tab
import { i18n } from '@/main.js'
import Vue from 'vue'
import Select from './../components/Select.vue'
import Input from './../components/Input.vue'
import { timeNumberToDate } from '@/utils/utils'

// 带红星的表头
export const requiredHeader = (args) => {
  const { headerText } = args
  const template = () => {
    return {
      template: Vue.component('requiredHeaderComponent', {
        template: `
            <div class="headers">
              <span style="color: red">*</span>
              <span class="e-headertext">{{ headerText }}</span>
            </div>
          `,
        data: function () {
          return {
            data: {},
            headerText
          }
        }
      })
    }
  }
  return template
}

export const columnData = () => {
  const column = [
    {
      type: 'checkbox',
      width: 50
    },
    // {
    //   field: 'serialNumber', // 前端定义
    //   headerText: i18n.t('序号'),
    //   allowEditing: false
    // },
    {
      field: 'factoryCode',
      headerText: i18n.t('工厂代码'),
      headerTemplate: requiredHeader({
        headerText: i18n.t('工厂代码')
      }),
      editTemplate: () => {
        return { template: Select }
      }
    },
    {
      field: 'deliveryAddrCode',
      headerText: i18n.t('交货地点代码'),
      headerTemplate: requiredHeader({
        headerText: i18n.t('交货地点代码')
      })
      // editTemplate: () => {
      //   return { template: Select }
      // }
    },
    {
      field: 'deliveryAddrName',
      headerText: i18n.t('交货地点描述'),
      headerTemplate: requiredHeader({
        headerText: i18n.t('交货地点描述')
      })
    },
    {
      field: 'rbcCode',
      headerText: i18n.t('RBC编码'),
      headerTemplate: requiredHeader({
        headerText: i18n.t('RBC编码')
      }),
      editTemplate: () => {
        return { template: Input }
      }
    },
    {
      field: 'bgType',
      headerText: i18n.t('BG类型'),
      headerTemplate: requiredHeader({
        headerText: i18n.t('BG类型')
      }),
      editTemplate: () => {
        return { template: Input }
      }
    },
    {
      field: 'plusBizCirclesCode',
      headerText: i18n.t('加工商'),
      editTemplate: () => {
        return { template: Select }
      }
    },
    {
      field: 'consigneeName',
      headerText: i18n.t('收货人'),
      width: 180,
      // headerTemplate: requiredHeader({
      //   headerText: i18n.t('收货人')
      // }),
      editTemplate: () => {
        // return { template: consignee }
        return { template: Input }
      }
    },
    {
      field: 'contactNo',
      headerText: i18n.t('联系电话'),
      // allowEditing: false,
      // headerTemplate: requiredHeader({
      //   headerText: i18n.t('联系电话')
      // }),
      editTemplate: () => {
        return { template: Input }
      }
    },
    {
      field: 'warehouseCode',
      headerText: i18n.t('收货地址编码'),
      allowEditing: false,
      // headerTemplate: requiredHeader({
      //   headerText: i18n.t('收货地址编码')
      // }),
      // template: () => {
      //   const template = {
      //     template: `<span>{{ data.warehouseCode + '-' + data.warehouseName }}</span>`,
      //     data() {
      //       return {
      //         data: {}
      //       }
      //     }
      //   }
      //   return { template }
      // },
      editTemplate: () => {
        // return { template: Select }
        return { template: Input }
      }
    },
    {
      field: 'warehouseName',
      headerText: i18n.t('收货地址名称'),
      // headerTemplate: requiredHeader({
      //   headerText: i18n.t('收货地址名称')
      // }),
      // allowEditing: false,
      // width: 0.5,
      editTemplate: () => {
        return { template: Input }
      }
    },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人'),
      allowEditing: false
    },
    {
      field: 'createTime',
      headerText: i18n.t('创建时间'),
      allowEditing: false,
      template: () => {
        const template = {
          template: `<span>{{ data.createTime | transformValue }}</span>`,
          data() {
            return {
              data: {}
            }
          },
          filters: {
            transformValue(val) {
              if (val && val.length === 13) {
                return timeNumberToDate({
                  formatString: 'YYYY-mm-dd HH:MM:SS',
                  value: val
                })
              }
              return '-'
            }
          }
        }
        return { template }
      },
      editTemplate: () => {
        const template = {
          template: `<span>{{ data.createTime | transformValue }}</span>`,
          data() {
            return {
              data: {}
            }
          },
          filters: {
            transformValue(val) {
              if (val && val.length === 13) {
                return timeNumberToDate({
                  formatString: 'YYYY-mm-dd HH:MM:SS',
                  value: val
                })
              }
              return '-'
            }
          }
        }
        return { template }
      }
    },
    {
      field: 'updateUserName',
      headerText: i18n.t('更新人'),
      allowEditing: false
    },
    {
      field: 'updateTime',
      headerText: i18n.t('更新时间'),
      allowEditing: false,
      template: () => {
        const template = {
          template: `<span>{{ data.updateTime | transformValue }}</span>`,
          data() {
            return {
              data: {}
            }
          },
          filters: {
            transformValue(val) {
              if (val && val.length === 13) {
                return timeNumberToDate({
                  formatString: 'YYYY-mm-dd HH:MM:SS',
                  value: val
                })
              }
              return '-'
            }
          }
        }
        return { template }
      },
      editTemplate: () => {
        const template = {
          template: `<span>{{ data.updateTime | transformValue }}</span>`,
          data() {
            return {
              data: {}
            }
          },
          filters: {
            transformValue(val) {
              if (val && val.length === 13) {
                return timeNumberToDate({
                  formatString: 'YYYY-mm-dd HH:MM:SS',
                  value: val
                })
              }
              return '-'
            }
          }
        }
        return { template }
      }
    }
  ]
  return column
}

export const pageConfig = (that) => {
  const config = [
    {
      gridId: 'f7b19598-7c5a-4ef5-b2fe-4500279fe363',
      isUseCustomSearch: true,
      isCustomSearchRules: true,
      isUseCustomEditor: true,
      toolbar: {
        useBaseConfig: false,
        tools: [
          [
            { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
            {
              id: 'closeEdit',
              icon: 'icon_table_delete',
              title: i18n.t('取消编辑')
            },
            {
              id: 'Delete',
              icon: 'icon_table_delete',
              title: i18n.t('删除')
            },
            {
              id: 'Import',
              icon: 'icon_solid_Import',
              title: i18n.t('导入')
            },
            { id: 'Export1', icon: 'icon_solid_export', title: i18n.t('导出') }
          ],
          ['Filter', 'Refresh', 'Setting']
        ]
      },
      useToolTemplate: false,
      grid: {
        editSettings: {
          allowEditing: true,
          allowAdding: true,
          allowDeleting: true,
          mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
          showConfirmDialog: false,
          showDeleteConfirmDialog: false,
          newRowPosition: 'Top'
        },
        columnData: columnData(that),
        frozenColumns: 1,
        asyncConfig: {
          url: '/srm-purchase-execute/tenant/screen/warehouse/addr/query',
          recordsPosition: 'data.screenWarehouseAddrDtoPage.records',
          serializeList: (list) => {
            if (list.length > 0) {
              let serialNumber = 1
              list.forEach((item) => {
                // 添加序号
                item.serialNumber = serialNumber++
                item.factoryCode = item.siteCode
                item.plusBizCirclesCode = item.processorCode
                item.warehouseCode = item.receiveAddrCode
                item.warehouseName = item.receiveAddrDesc
              })
            }
            return list
          }
        }
      }
    }
  ]
  return config
}
