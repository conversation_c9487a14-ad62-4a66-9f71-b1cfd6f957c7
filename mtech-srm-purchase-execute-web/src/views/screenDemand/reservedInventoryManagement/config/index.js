import { i18n } from '@/main.js'

const columnData = () => {
  const column = [
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      width: '150'
    },
    {
      field: 'supplierItemCode',
      headerText: i18n.t('供应商物料编码'),
      width: '200'
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料描述'),
      width: '200'
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: '150'
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: '200'
    },
    {
      field: 'location',
      headerText: i18n.t('库存地点'),
      width: '100'
    },
    {
      field: 'invQty',
      headerText: i18n.t('数量'),
      width: '100'
    },
    {
      field: 'invUnit',
      headerText: i18n.t('单位'),
      width: '100'
    },
    {
      field: 'createTime',
      headerText: i18n.t('更新时间'),
      width: '200'
    },
    {
      field: 'remark',
      headerText: i18n.t('备注'),
      width: '200'
    },
    {
      field: 'po',
      headerText: i18n.t('预留订单号'),
      width: '200'
    },
    {
      field: 'receivePlace',
      headerText: i18n.t('收货地点'),
      width: '200'
    },
    {
      field: 'factoryCode',
      headerText: i18n.t('工厂'),
      width: '200'
    }
  ]
  return column
}

export const componentConfig = (that) => {
  const config = [
    {
      isUseCustomSearch: true, // 是否使用自定义查询
      isCustomSearchRules: true,
      useToolTemplate: false, // 此项不使用预置的表格操作按钮'新增、编辑、删除'
      useBaseConfig: false,
      toolbar: [],
      grid: {
        allowPaging: true, // 是否使用内置分页器
        lineIndex: 0,
        columnData: columnData(that),
        asyncConfig: {
          url: '/srm-purchase-execute/internal/csot/inv/v1/query'
        }
      }
    }
  ]
  return config
}
