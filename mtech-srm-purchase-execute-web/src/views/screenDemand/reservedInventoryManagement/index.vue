<!-- 预留库存查询 - 采方 -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
          <mt-form-item prop="itemCode" :label="$t('物料编码')">
            <mt-input
              v-model="searchFormModel.itemCode"
              :show-clear-button="true"
              :placeholder="$t('请输入物料编码')"
              @change="itemCodeChange"
            />
          </mt-form-item>
          <mt-form-item prop="supplierItemCode" :label="$t('供应商物料编码')">
            <mt-input
              v-model="searchFormModel.supplierItemCode"
              :show-clear-button="true"
              :placeholder="$t('请输入供应商物料编码')"
              @change="supplierItemCodeChange"
            />
          </mt-form-item>
          <mt-form-item prop="location" :label="$t('库存地点')">
            <mt-input
              v-model="searchFormModel.location"
              :show-clear-button="true"
              :placeholder="$t('请输入库存地点')"
              @change="locationChange"
            />
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { componentConfig } from './config/index'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      componentConfig: componentConfig(this),
      searchFormModel: {},
      searchFormRules: {},
      invDatesOptions: []
    }
  },
  methods: {
    itemCodeChange() {
      this.searchFormModel.itemCodes = this.searchFormModel.itemCode.split(' ')
    },
    supplierItemCodeChange() {
      this.searchFormModel.supplierItemCodes = this.searchFormModel.supplierItemCode.split(' ')
    },
    locationChange() {
      this.searchFormModel.locations = this.searchFormModel.location.split(' ')
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    }
  }
}
</script>
