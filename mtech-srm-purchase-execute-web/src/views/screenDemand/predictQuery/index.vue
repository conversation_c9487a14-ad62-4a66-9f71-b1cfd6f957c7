<template>
  <!-- 预测管理-采方 -->
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      default-max-height="60"
      @reset="handleCustomReset"
      @search="handleCustomSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item v-if="!isSup" prop="supplierCodes" :label="$t('供应商')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.supplierCodes"
            :data-source="screenSupplierList"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <mt-input v-model="searchFormModel.itemCode"></mt-input>
        </mt-form-item>
        <mt-form-item prop="specCodes" :label="$t('规格')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.specCodes"
            :data-source="specList"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="queryType" :label="$t('查询类型')" label-style="top">
          <mt-select
            style="flex: 1"
            v-model="searchFormModel.queryType"
            :data-source="[
              {
                text: $t('汇总'),
                value: 1
              },
              {
                text: $t('明细'),
                value: 0
              }
            ]"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input v-model="searchFormModel.itemName"></mt-input>
        </mt-form-item>
        <mt-form-item prop="supplierItemCodes" :label="$t('供应商物料编码')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.supplierItemCodes"
            :data-source="screenSupplierItemList"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.status"
            :data-source="statusOptions"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
          <mt-input v-model="searchFormModel.categoryCode"></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="status" :label="$t('类型')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="forecastTypes"
            :data-source="forecastTypeOptions"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item> -->
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        :row-config="{ height: 30 }"
        :columns="columns"
        :table-data="tableData"
        show-overflow
        height="auto"
        header-align="left"
        align="left"
        style="padding-top: unset"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true,
          activeMethod: this.activeRowMethod
        }"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        @edit-closed="editComplete"
        @edit-actived="editBegin"
        @edit-disabled="editDisabledEvent"
      >
        <template slot="custom-tools">
          <!-- :icon="item.icon" -->
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
          <span style="margin-left: 10px"
            >{{ $t('大版本号') }}：{{ versionInfo.bigVersionNo }}
          </span>
          <span style="margin-left: 10px"
            >{{ $t('小版本号') }}：{{ versionInfo.fsmallVersionNo }}
          </span>
          <span style="margin-left: 10px"
            >{{ $t('版本日期') }}：{{ versionInfo.frowVersionDate }}</span
          >
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import {
  StatusSearchOptions,
  ForecastColumnData,
  ToolBar,
  lastColumn,
  middleColumn,
  dataTypeColumn
} from './config/constant'
import * as UTILS from '@/utils/utils'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      toolbar: ToolBar,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      searchFormModel: {
        queryType: 1
      },
      titleList: [],
      tableData: [],
      columns: ForecastColumnData,
      forecastPageCurrent: 1,
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      exportRules: [], //导出规则
      statusOptions: StatusSearchOptions,
      copySearchFormModel: null,
      isEdit: false,
      isSup: false,
      versionInfo: {
        bigVersionNo: '',
        frowVersionDate: '',
        fsmallVersionNo: '',
        srowVersionDate: '',
        ssmallVersionNo: ''
      },
      screenSupplierList: [],
      screenSupplierItemList: [],
      specList: []
    }
  },
  mounted() {
    if (this.$route.name === 'sup-screen-predict-query') {
      this.isSup = true
    } else {
      this.getScreenSupplier()
    }
    this.$API.screenDemand.syncLatestVersion().then((res) => {
      const { code, data } = res
      if (code === 200) {
        this.versionInfo = data
        this.getSpecList({ bigVersionNo: data.bigVersionNo })
        this.getScreenSupplierItem()
      }
    })
    this.handleCustomSearch()
  },
  activated() {
    if (this.$route.name === 'sup-screen-predict-query') {
      this.isSup = true
    } else {
      this.getScreenSupplier()
    }
    this.$API.screenDemand.syncLatestVersion().then((res) => {
      const { code, data } = res
      if (code === 200) {
        this.versionInfo = data
        this.getSpecList({ bigVersionNo: data.bigVersionNo })
        this.getScreenSupplierItem()
      }
    })
    this.handleCustomSearch()
  },
  methods: {
    getScreenSupplier() {
      this.$API.screenDemand.getScreenSupplier().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.screenSupplierList = data.map((i) => {
            return {
              text: `${i.supplierCode}-${i.supplierName}`,
              value: i.supplierCode
            }
          })
        }
      })
    },
    getSpecList(params) {
      this.$API.screenDemand.screenDemandForecastManagerQuerySpecAll(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.specList = data
        }
      })
    },
    getScreenSupplierItem() {
      this.$API.screenDemand
        .getScreenSupplierItemCode({ bigVersionNo: this.versionInfo.bigVersionNo })
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.screenSupplierItemList = []
            data.forEach((i) => {
              if (i.supplierItemCode && i.itemName) {
                this.screenSupplierItemList.push({
                  text: `${i.supplierItemCode}-${i.itemName}`,
                  value: i.supplierItemCode
                })
              }
            })
          }
        })
    },
    handleClickToolBar(args) {
      const { code } = args
      if (code === 'ForecastExport') {
        // 导出
        // if (selectedRecords.length == 0) {
        //   this.$toast({ content: this.$t('请先选择需要导出的数据'), type: 'warning' })
        //   return
        // }
        this.postBuyerForecastExport()
      }
    },
    // 采方-导出
    postBuyerForecastExport() {
      const params = {
        page: {
          current: 1,
          size: 9999
        },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      // this.apiStartLoading()
      this.getExportApi()(params).then((res) => {
        this.$store.commit('endLoading')
        // this.apiEndLoading()

        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    getExportApi() {
      if (this.isSup) {
        return this.$API.screenDemand.screenDemandForecastSupExport
      }
      return this.$API.screenDemand.screenDemandForecastExport
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 校验点击的对象是否为取消编辑按钮
        if (args.$event.target.innerText === this.$t('取消编辑')) {
          // 清除编辑状态
          this.$refs.xTable.$refs.xGrid.clearEdit()
          this.handleCustomSearch()
          return
        }
        //2、 判断是否有row.planner调新增或者编辑接口
        if (row.planner) {
          this.postBuyerForecastSaveForecast({ data: row })
        } else {
          this.postBuyerForecastBatchInsert({ data: row })
        }
        //3、 接口调用成功调刷新接口
      }
    },
    editBegin(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        this.getItemDataSource({ value: row.itemCode }, row)
        this.getSupplierDataSource({ value: row.supplierCode })
      } else {
        this.getSupplierDataSource({ value: '' })
      }
      this.isEdit = true
    },
    activeRowMethod({ row }) {
      // 已确认的数据不可修改
      if (row.status === 5) {
        return false
      }
      return true
    },
    editDisabledEvent() {
      this.$toast({
        content: this.$t('此状态数据不可编辑'),
        type: 'warning'
      })
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'queryType') {
            this.searchFormModel[key] = 1
          }
        }
      }
      this.handleCustomSearch()
    },
    // 采方-获取采方预测信息列表
    handleCustomSearch() {
      this.isEdit = false
      const params = {
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        },
        ...this.searchFormModel
      }
      this.apiStartLoading()
      this.getSearchApi()(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.totalRecordsCount = Number(total)
            const records = res?.data?.records || [] // 表格数据
            const titleList = res?.data?.records[0]?.headers || [] // 动态表头数据
            this.titleList = res?.data?.records[0]?.headers || [] // 动态表头数据
            // 处理表头数据
            this.columns = this.handleColumns({ titleList })
            // 处理表数据
            this.tableData = this.handleDataSource({ records, titleList })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    getSearchApi() {
      if (this.isSup) {
        return this.$API.screenDemand.screenDemandForecastSupQuery
      }
      return this.$API.screenDemand.screenDemandForecastQuery
    },
    handleColumns(data) {
      const { titleList } = data
      // 固定的表头
      const forecastColumns = ForecastColumnData
      // 动态的日期表头
      const titleListColumnData = []
      titleList.forEach((item, index) => {
        const title = `title_${titleList[index]}`
        titleListColumnData.push({
          field: title,
          title: item,
          minWidth: 160,
          // showOverflow: true,
          // editRender: {},
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <div>
                  <div class='vxe-cell-border'>{row[title]['supplierNum']}</div>
                </div>
              ]
            },
            edit: ({ row }) => {
              return [
                <div>
                  <div class='vxe-cell-border'>{row[title]['supplierNum']}</div>
                </div>
              ]
            }
          }
        })
      })
      // 合并表头数组，然后 push 到 forecastColumnData 中
      const columns = [].concat(forecastColumns)

      if (this.searchFormModel.queryType === 0) {
        columns.push(...middleColumn)
      }
      columns.push(...dataTypeColumn)
      columns.push(...titleListColumnData)
      columns.push(...lastColumn)
      return columns
    },
    handleDataSource(data) {
      const { records, titleList } = data
      const tableData = []
      records.forEach((recordsItem) => {
        const rowData = {
          ...recordsItem.tvScreenForecastIntegration
        }
        // recordsItem.thePrimaryKey = recordsItem.tvScreenForecastIntegration.id // 主键的值为 API 数据的 id
        // recordsItem.status = recordsItem.tvScreenForecastIntegration.status ?? 0 // 状态为 undefined/null 时，默认为新建状态（导入时）

        const frow = recordsItem?.frow
        titleList.forEach((itemTitle, index) => {
          frow.forEach(() => {
            // 将预测数据赋给表头对应的对象
            const forecastItem = {
              supplierNum: frow[index] || 0
            }
            rowData[`title_${itemTitle}`] = forecastItem
          })
        })
        tableData.push(rowData)
      })

      return tableData
      // return records
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  // div .vxe-cell-border {
  //   border: solid #e6e9ed 1px;
  // }
}
/deep/ .col--seq,
/deep/ .col--checkbox {
  .c--tooltip {
    padding: 0 10px;
  }
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
