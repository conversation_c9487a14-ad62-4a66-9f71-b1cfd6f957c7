import { i18n } from '@/main.js'
export const ConstDynamicTitleStr = 'title_' // 动态表格数据的 key 标识

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

export const ToolBar = [
  {
    code: 'ForecastExport',
    name: i18n.t('导出'),
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  }
]

// 状态 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
export const Status = {
  new: 0, // 新建
  modified: 1, // 已修改
  pendingFeedback: 2 // 已反馈
  // confirmed: 3 // 已确认
}
// 状态 text
export const StatusText = {
  [Status.new]: i18n.t('新建'),
  [Status.modified]: i18n.t('已修改'),
  [Status.pendingFeedback]: i18n.t('已发布')
  // [Status.confirmed]: i18n.t('已确认')
}
// 状态 class
export const StatusClass = {
  [Status.new]: 'col-active',
  [Status.modified]: 'col-active',
  [Status.pendingFeedback]: 'col-active' // col-published
  // [Status.confirmed]: 'col-active' // col-inactive
}
export const StatusSearchOptions = [
  { value: Status.new, text: StatusText[Status.new] },
  { value: Status.modified, text: StatusText[Status.modified] },
  { value: Status.pendingFeedback, text: StatusText[Status.pendingFeedback] }
  // { value: Status.confirmed, text: StatusText[Status.confirmed] }
]

// 预测表格列数据
export const ForecastColumnData = [
  // {
  //   type: 'checkbox',
  //   width: 70,
  //   ignore: true
  //   // fixed: 'left'
  // },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50
  },
  // {
  //   field: 'bigVersionNo',
  //   title: i18n.t('大版本号'),
  //   width: 135
  // },
  // {
  //   field: 'dsmallVersionNo',
  //   title: i18n.t('小版本号'),
  //   width: 135
  // },
  {
    field: 'itemCode',
    title: i18n.t('物料编码'),
    width: 210
  },
  {
    field: 'supplierItemCode',
    title: i18n.t('供应商物料编码'),
    width: 210
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称'),
    width: 210
  },
  {
    field: 'specCode',
    title: i18n.t('规格'),
    width: 135
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类'),
    width: 135
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码'),
    width: 135
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    width: 210
  }
]

export const middleColumn = [
  {
    field: 'logicFactory',
    title: i18n.t('逻辑工厂'),
    width: 135
  },
  {
    field: 'bgCode',
    title: i18n.t('BG'),
    width: 135
  }
]

export const dataTypeColumn = [
  {
    field: 'dataType',
    title: i18n.t('数据类型'),
    width: 135,
    formatter: () => {
      return 'F(预测)'
    }
  }
]

export const lastColumn = [
  {
    field: 'frowStatus',
    title: i18n.t('状态'),
    width: 100,
    formatter: ({ cellValue }) => {
      let item = StatusSearchOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'drowVersionDate',
    title: i18n.t('版本日期'),
    width: 135
  }
]
