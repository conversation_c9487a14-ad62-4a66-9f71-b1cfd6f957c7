<template>
  <!-- 采方-对账协同-采购对账单列表 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleCustomReset="handleCustomReset"
      @handleClickToolBar="handleClickToolBar"
      class="template-height"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.supplierCode"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择供应商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="deliveryPlace" :label="$t('交货地点')" label-style="top">
              <mt-input
                v-model="searchFormModel.deliveryPlace"
                :show-clear-button="true"
                :placeholder="$t('请输入交货地点')"
              />
            </mt-form-item>
            <mt-form-item prop="deliverPlace" :label="$t('发货地')" label-style="top">
              <mt-input
                v-model="searchFormModel.deliverPlace"
                :show-clear-button="true"
                :placeholder="$t('请输入发货地')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          toolbar: [
            {
              id: 'Delete',
              icon: 'icon_solid_Delete',
              title: this.$t('删除')
            },
            {
              id: 'excelExport', // 共用的
              icon: 'icon_solid_export',
              title: this.$t('导出')
            },
            { id: 'excelImport', icon: 'icon_solid_Import', title: this.$t('导入') }
          ],
          grid: {
            columnData: [
              {
                field: 'id',
                headerText: 'id',
                width: 0,
                visible: false,
                isPrimaryKey: true
              },
              {
                width: '100',
                field: 'supplierCode',
                headerText: this.$t('供应商编码')
              },
              {
                field: 'supplierName',
                headerText: this.$t('供应商名称')
              },
              {
                width: '120',
                field: 'deliveryPlace',
                headerText: this.$t('交货地点代码')
              },
              {
                width: '80',
                field: 'deliverPlace',
                headerText: this.$t('发货地')
              },
              {
                width: '100',
                field: 'transitDays',
                headerText: this.$t('在途天数')
              }
            ],
            dataSource: [],
            pageSettings: {
              currentPage: 1,
              pageSize: 50,
              pageSizes: [10, 50, 100, 200],
              totalRecordsCount: 0
            },
            asyncConfig: {
              url: `/srm-purchase-execute/tenant/srm-scm/screen/queryTransit`,
              recordsPosition: 'data.records',
              params: {}
            },
            lineSelection: 0
            // lineIndex: 1
            // frozenColumns: 1
          }
        }
      ]
    }
  },
  methods: {
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    handleClickToolBar(args) {
      const { grid, toolbar } = args
      const selectedRecords = grid.getSelectedRecords()
      if (selectedRecords.length == 0 && toolbar.id === 'Delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id === 'Delete') {
        const idList = selectedRecords.map((i) => i.id)
        this.handleDelete(idList)
      } else if (toolbar.id === 'excelExport') {
        this.handleExport()
      } else if (toolbar.id === 'excelImport') {
        this.handleImport()
      }
    },
    handleDelete(idList) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('是否确定删除已选数据?')
        },
        success: () => {
          this.$API.screenDemand.inTransitCycleDelete(idList).then((res) => {
            if (res.code === 200) {
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      })
    },
    handleExport() {
      const params = {
        page: {
          size: 9999,
          current: 1
        },
        ...this.searchFormModel
      }
      this.apiStartLoading()
      this.$API.screenDemand
        .inTransitCycleExport(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    handleImport() {
      //导入
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.screenDemand.inTransitCycleImport,
          downloadTemplateApi: this.$API.screenDemand.inTransitCycleImportTemp,
          paramsKey: 'excel'
        },
        success: () => {
          // 导入之后刷新列表
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style></style>
