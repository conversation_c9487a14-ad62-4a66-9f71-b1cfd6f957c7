import { i18n } from '@/main.js'
export const ConstDynamicTitleStr = 'title_' // 动态表格数据的 key 标识

// 业务组类型数字字典编码
export const BusinessGroupTypeDictCode = 'BUSINESSGROUP'

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 预测数据类型cell
export const ForecastDataTypeCell = [
  {
    title: i18n.t('D(原始需求)')
  },
  {
    title: i18n.t('P(需求量)')
  },
  {
    title: 'A'
  }
]

// 类型字段，快捷搜索，下拉选项
export const ForecastTypeDataSource = [
  ForecastDataTypeCell[0]?.title, // D(原始需求)
  ForecastDataTypeCell[1]?.title, // P(需求量)
  ForecastDataTypeCell[2]?.title // A
]

// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  status: 0, // 状态 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
  planner: '', // 计划员
  factoryId: '', // 工厂id
  factoryName: '', // 工厂name
  factoryCode: '', // 工厂代码
  mrpArea: '', // 计划区域
  itemName: '', // 物料名称
  itemCode: '', // 物料编码
  itemId: '', // 物料id
  supplierName: '', // 供应商名称
  supplierCode: null, // 供应商编码
  supplierId: '', // 供应商id
  purchaseAdvanceDate: '', // 采购提前期
  supplierInv: '', // 供方库存
  supplierRemark: '', // 供应商备注
  quota: '', // 配额
  quotaFlag: '', // 无配额标识
  machineModel: '', // 机型/机芯
  purchaserRemark: '', // 采方备注
  manufacturer: '', // 生产厂商名称
  rawMaterialManufacturer: '', // 关键原材厂商
  keyRawMaterialOrigin: '', // 关键原材产地
  packageManufacturer: '', // 包装厂商
  packageProdtPlace: '', // 包装产地
  manufacturerDedicatedStatus: '', // 制造商专用状况
  syncVersion: '', // 版本
  unpaidPoQty: '' // 未交PO
}

export const ToolBar = [
  { code: 'ForecastAdd', name: i18n.t('新增'), icon: 'vxe-icon-square-plus', status: 'info' },
  {
    code: 'ForecastDelete',
    name: i18n.t('删除'),
    icon: 'vxe-icon-delete',
    status: 'info'
  },
  {
    code: 'CloseEdit',
    name: i18n.t('取消编辑'),
    icon: 'vxe-icon-edit',
    status: 'info',
    transfer: true
  },
  {
    code: 'ForecastPublish',
    name: i18n.t('发布'),
    icon: 'vxe-icon-folder-open',
    status: 'info'
  },
  {
    code: 'ForecastImport',
    name: i18n.t('导入'),
    icon: 'vxe-icon-cloud-upload',
    status: 'info'
  },
  {
    code: 'ForecastExport',
    name: i18n.t('导出'),
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  }
]

// 表格行按钮
export const CellTools = [
  {
    id: 'ForecastPublish',
    icon: '', // icon_solid_Release
    // permission: ['O_02_0384'],
    title: i18n.t('发布'),
    visibleCondition: (data) => {
      return data.status === Status.new || data.status === Status.modified // 状态：新建 || 已修改
    }
  },
  {
    id: 'ForecastDelete',
    icon: '', // icon_solid_Delete
    // permission: ['O_02_0383'],
    title: i18n.t('删除'),
    visibleCondition: (data) => {
      return data.status === Status.new || data.status === Status.modified // 状态：新建 || 已修改
    }
  }
]

// 状态 0 新建、1 已修改、2 待反馈、3 已反馈、4 已删除
export const Status = {
  new: 0, // 新建
  modified: 1, // 已修改
  pendingFeedback: 2, // 已发布 不可编辑
  endingFeedback: 3, // 已反馈 不可编辑
  deleted: 4 // 已删除 不可编辑
}
// 状态 text
export const StatusText = {
  [Status.new]: i18n.t('新建'),
  [Status.modified]: i18n.t('已修改'),
  [Status.pendingFeedback]: i18n.t('已发布'),
  [Status.endingFeedback]: i18n.t('已反馈'),
  [Status.deleted]: i18n.t('已删除')
}
// 状态 class
export const StatusClass = {
  [Status.new]: 'col-active',
  [Status.modified]: 'col-active',
  [Status.pendingFeedback]: 'col-active', // col-published
  [Status.endingFeedback]: 'col-active', // col-published
  [Status.deleted]: 'col-active' // col-inactive
}
export const StatusSearchOptions = [
  { value: Status.new, text: StatusText[Status.new] },
  { value: Status.modified, text: StatusText[Status.modified] },
  { value: Status.pendingFeedback, text: StatusText[Status.pendingFeedback] },
  { value: Status.endingFeedback, text: StatusText[Status.endingFeedback] },
  { value: Status.deleted, text: StatusText[Status.deleted] }
]
export const StatusSearchOptionsLabel = [
  { value: Status.new, label: StatusText[Status.new] },
  { value: Status.modified, label: StatusText[Status.modified] },
  { value: Status.pendingFeedback, label: StatusText[Status.pendingFeedback] },
  { value: Status.endingFeedback, label: StatusText[Status.endingFeedback] },
  { value: Status.deleted, label: StatusText[Status.deleted] }
]
export const StatusSearchOptions1 = [
  { value: Status.new, text: StatusText[Status.new] },
  { value: Status.modified, text: StatusText[Status.modified] },
  { value: Status.pendingFeedback, text: StatusText[Status.pendingFeedback] }
  // { value: Status.endingFeedback, text: StatusText[Status.endingFeedback] },
  // { value: Status.deleted, text: StatusText[Status.deleted] }
]

// 预测表格列数据
export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    ignore: true
    // fixed: 'left'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'bigVersionNo',
    title: i18n.t('大版本号'),
    width: 120,
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'smallVersionNo',
    title: i18n.t('小版本号'),
    width: 120,
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = StatusSearchOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    },
    editRender: {
      name: 'select',
      options: StatusSearchOptionsLabel,
      props: { style: 'background: #fff' },
      attrs: { disabled: true }
    }
    // fixed: 'left'
  },
  {
    field: 'virtualSupplierCode',
    title: i18n.t('虚拟供应商代码'),
    // formatter: ({ row }) => {
    //   return `${row.virtualSupplierCode}-${row.virtualSupplierName}`
    // },
    width: 160,
    showOverflow: true,
    // editRender: { name: 'input', attrs: { placeholder: '请输入' } }
    editRender: {},
    slots: {
      edit: 'virtualSupplierCodeEdit'
    }
    // editRender: {},
    // slots: {
    //   edit: 'virtualSupplierCodeEdit'
    // }
  },
  {
    field: 'supplierList',
    title: i18n.t('供应商编码'),
    // formatter: ({ row }) => {
    //   let supplierList = ''
    //   if (row.supplierList && row.supplierList.length) {
    //     row.supplierList.forEach((item, index) => {
    //       if (index === 0) {
    //         supplierList += item.supplierCode
    //       } else {
    //         supplierList += `, \n${item.supplierCode}`
    //       }
    //     })
    //   }
    //   return supplierList
    // },
    slots: {
      // 使用 JSX 渲染
      default: 'supplierListDefault',
      edit: 'supplierListDefault'
    },
    width: 160,
    showOverflow: true,
    editRender: {}
    // editRender: {},
    // slots: {
    //   edit: 'supplierCodeEdit'
    // }
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂编码'),
    showOverflow: true,
    width: 170,
    editRender: {},
    slots: {
      edit: 'factoryCodeEdit'
    }
  },
  {
    field: 'materialCode',
    title: i18n.t('物料编码'),
    width: 160,
    showOverflow: true,
    editRender: {},
    slots: {
      edit: 'itemCodeEdit'
    }
  },
  {
    field: 'mokaItemCode',
    title: i18n.t('茂佳物料编码'),
    width: 160,
    showOverflow: true
  },
  {
    field: 'supplierItemCode',
    title: i18n.t('供应商物料编码'),
    width: 160,
    showOverflow: true,
    // editRender: {},
    // slots: {
    //   edit: 'itemCodeEdit'
    // }
    // editRender: { name: 'input', attrs: { placeholder: '请输入' } }
    editRender: {},
    slots: {
      edit: 'supplierItemCodeEdit'
    }
  },
  {
    field: 'rbcCode',
    title: i18n.t('RBC编码'),
    width: 120,
    showOverflow: true,
    // editRender: { name: 'input', attrs: { placeholder: '请输入' } }
    editRender: {},
    slots: {
      edit: 'rbcCodeEdit'
    }
  },
  {
    field: 'bgType',
    title: i18n.t('BG类型'),
    showOverflow: true,
    // editRender: { name: 'input', attrs: { placeholder: '请输入' } }
    editRender: {},
    slots: {
      edit: 'bgTypeEdit'
    }
  },
  {
    field: 'etaLocationCode',
    title: i18n.t('交货地库位编码'),
    width: 155,
    showOverflow: true,
    // editRender: { name: 'input', attrs: { placeholder: '请输入' } }
    editRender: {},
    slots: {
      edit: 'etaLocationCodeEdit'
    }
  },
  {
    field: 'deliveryAddrName',
    title: i18n.t('交货地点'),
    width: 120,
    showOverflow: true,
    // editRender: { name: 'input', attrs: { placeholder: '请输入' } }
    editRender: {},
    slots: {
      edit: 'deliveryAddrNameEdit'
    }
    // formatter: ({ row }) => {
    //   return `${row.deliveryAddrCode}-${row.deliveryAddrName}`
    // }
  },
  {
    field: 'deliverAddr',
    title: i18n.t('发货地点'),
    width: 120,
    showOverflow: true,
    // editRender: { name: 'input', attrs: { placeholder: '请输入' } }
    editRender: {},
    slots: {
      edit: 'deliverAddrEdit'
    }
  },
  {
    field: 'processorCode',
    title: i18n.t('加工商编码'),
    width: 160,
    showOverflow: true,
    editRender: {},
    slots: {
      edit: 'processorCodeEdit'
    }
  },
  {
    field: 'purchaseOrder',
    title: i18n.t('采购订单'),
    width: 120,
    slots: {
      // 使用 JSX 渲染
      default: 'purchaseOrderDefault',
      edit: 'purchaseOrderDefault'
    },
    showOverflow: true,
    editRender: {}
  },
  {
    field: 'forecastType',
    className: 'vxe-table-multi-cell',
    title: i18n.t('类型'),
    slots: {
      default: 'forecastTypeDefault'
      // 使用 JSX 渲染
      // default: ({ row }) => {
      //   return [
      //     <div>
      //       {ForecastTypeDataSource.map((item) => {
      //         return <span>{item}</span>
      //       })}
      //     </div>
      //   ]
      // }
    }
  }
]

export const lastColumns = [
  {
    field: 'remark',
    title: i18n.t('备注'),
    // formatter: ({ row }) => {
    //   return `${row.virtualSupplierCode}-${row.virtualSupplierName}`
    // },
    showOverflow: true,
    editRender: { name: 'input', attrs: { placeholder: '请输入' } }
    // editRender: {},
    // slots: {
    //   edit: 'virtualSupplierCodeEdit'
    // }
  }
]
