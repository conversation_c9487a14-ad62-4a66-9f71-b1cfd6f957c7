<template>
  <!-- 屏采交货计划-采方 -->
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      @reset="handleCustomReset"
      @search="handleCustomSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="itemCodeList" :label="$t('物料编码')" label-style="top">
          <!-- <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.itemCodeList"
            :url="$API.masterData.getItemUrl"
            multiple
            :placeholder="$t('请选择物料')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :search-fields="['itemName', 'itemCode']"
          ></RemoteAutocomplete> -->
          <mt-input
            v-model="itemCodes"
            @change="itemChange"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="virtualSupplierCode" :label="$t('虚拟供应商编码')" label-style="top">
          <mt-input
            v-model="virtualSupplierCode"
            @change="
              () =>
                (searchFormModel.virtualSupplierCodeList = virtualSupplierCode
                  ? [virtualSupplierCode]
                  : null)
            "
          />
        </mt-form-item>
        <mt-form-item prop="deliverAddr" :label="$t('发货地')" label-style="top">
          <mt-input
            v-model="deliverAddr"
            @change="() => (searchFormModel.deliverAddrList = deliverAddr ? [deliverAddr] : null)"
          />
        </mt-form-item>
        <mt-form-item prop="siteCodeList" :label="$t('工厂编码')" label-style="top">
          <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.siteCodeList"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择工厂')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="deliveryAddrName" :label="$t('交货地点名称')" label-style="top">
          <mt-input
            v-model="deliveryAddrName"
            @change="
              () =>
                (searchFormModel.deliveryAddrNameList = deliveryAddrName
                  ? [deliveryAddrName]
                  : null)
            "
          />
        </mt-form-item>
        <mt-form-item prop="storageLocCodeList" :label="$t('库位编码')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.storageLocCodeList"
            :data-source="storageLocCodeOptions"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="bgTypeList" :label="$t('BG类型')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.bgTypeList"
            :data-source="bgTypeOptions"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="typeList" :label="$t('类型')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.typeList"
            :data-source="[
              { text: $t('D(原始需求)'), value: 'D' },
              { text: $t('P(需求量)'), value: 'P' },
              { text: $t('A'), value: 'A' }
            ]"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="statusList" :label="$t('状态')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.statusList"
            :data-source="statusOptions"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        :row-config="{ height: rowHeight }"
        :columns="columns"
        :table-data="tableData"
        show-overflow
        height="auto"
        header-align="left"
        align="left"
        style="padding-top: unset"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true,
          activeMethod: this.activeRowMethod
        }"
        :tooltip-config="tooltipConfig"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        @edit-closed="editComplete"
        @edit-actived="editBegin"
        @edit-disabled="editDisabledEvent"
      >
        <template slot="custom-tools">
          <!-- :icon="item.icon" -->
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #virtualSupplierCodeEdit="{ row }">
          <vxe-input
            v-model="row.virtualSupplierCode"
            @blur="(e) => virtualSupplierChange(e, row)"
            :disabled="!!row.bigVersionNo"
          ></vxe-input>
        </template>
        <template #supplierItemCodeEdit="{ row }">
          <vxe-input v-model="row.supplierItemCode" :disabled="!!row.bigVersionNo"></vxe-input>
        </template>
        <template #rbcCodeEdit="{ row }">
          <vxe-input v-model="row.rbcCode" :disabled="!!row.bigVersionNo"></vxe-input>
        </template>
        <template #bgTypeEdit="{ row }">
          <vxe-input v-model="row.bgType" :disabled="!!row.bigVersionNo"></vxe-input>
        </template>
        <template #etaLocationCodeEdit="{ row }">
          <vxe-input
            v-model="row.etaLocationCode"
            @blur="(e) => etaLocationCodeChange(e, row)"
            :disabled="!!row.bigVersionNo"
          ></vxe-input>
        </template>
        <template #deliveryAddrNameEdit="{ row }">
          <vxe-input v-model="row.deliveryAddrName" :disabled="!!row.bigVersionNo"></vxe-input>
        </template>
        <template #deliverAddrEdit="{ row }">
          <vxe-input v-model="row.deliverAddr" :disabled="!!row.bigVersionNo"></vxe-input>
        </template>
        <template #supplierListDefault="{ row }">
          <div v-for="item in row.supplierList" :key="item.supplierCode">
            {{ item.supplierCode }}
          </div>
        </template>
        <template #purchaseOrderDefault="{ row }">
          <div v-for="item in row.purchaseOrder" :key="item">
            {{ item }}
          </div>
        </template>
        <template #factoryCodeEdit="{ row }">
          <vxe-select
            v-model="row.siteCode"
            :placeholder="$t('请选择工厂')"
            :options="siteOptions"
            transfer
            filterable
            :disabled="!!row.bigVersionNo"
            @change="(value) => factoryChange(value, row)"
          ></vxe-select>
        </template>
        <template #itemCodeEdit="{ row }">
          <vxe-pulldown ref="xDownItem" transfer>
            <template #default>
              <vxe-input
                :value="row.materialCode"
                :placeholder="$t('请选择物料')"
                readonly
                :disabled="!!row.bigVersionNo"
                @click="(e) => focusItemCode(e, row)"
              ></vxe-input>
            </template>
            <template #dropdown>
              <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                @keyup="(e) => keyupItemCode(e, row)"
                :disabled="!!row.bigVersionNo"
                style="width: 100%"
              ></vxe-input>
              <vxe-list height="200" class="predict-vxe-dropdown" :data="itemOptions" auto-resize>
                <template #default="{ items }">
                  <div
                    v-show="itemOptions.length"
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.id"
                    @click="selectItemCode(item, row)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                  <div v-show="!itemOptions.length" class="predict-vxe-list-item">
                    <span>{{ $t('暂无数据') }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template>
        <!-- <template #supplierCodeEdit="{ row }">
          <vxe-pulldown ref="xDown" transfer>
            <template #default>
              <vxe-input
                :value="row.supplierCodes"
                :placeholder="$t('请选择供应商')"
                readonly
                @click="focusSupplierCode"
              ></vxe-input>
            </template>
            <template #dropdown>
              <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                @keyup="keyupSupplierCode"
                style="width: 100%"
              ></vxe-input>
              <vxe-list
                height="200"
                class="predict-vxe-dropdown"
                :data="supplierCodeOptions"
                auto-resize
              >
                <template #default="{ items }">
                  <div
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.value"
                    @click="selectSupplierCode(item, row)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template> -->
        <!-- <template #virtualSupplierCodeEdit="{ row }">
          <vxe-pulldown ref="xDown2" transfer>
            <template #default>
              <vxe-input
                :value="row.virtualSupplierCode"
                :placeholder="$t('请选择虚拟供应商')"
                readonly
                @click="focusVirtualSupplierCode"
              ></vxe-input>
            </template>
            <template #dropdown>
              <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                @keyup="keyupVirtualSupplierCode"
                style="width: 100%"
              ></vxe-input>
              <vxe-list
                height="200"
                class="predict-vxe-dropdown"
                :data="virtualSupplierCodeOptions"
                auto-resize
              >
                <template #default="{ items }">
                  <div
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.value"
                    @click="selectVirtualSupplierCode(item, row)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template> -->
        <template #processorCodeEdit="{ row }">
          <vxe-pulldown ref="xDown1" transfer>
            <template #default>
              <vxe-input
                :value="row.processorCode"
                :placeholder="$t('请选择加工商')"
                readonly
                :disabled="row.status === 2"
                @click="focusProcessorCode"
              ></vxe-input>
            </template>
            <template #dropdown>
              <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                :disabled="row.status === 2"
                @keyup="keyupProcessorCode"
                style="width: 100%"
              ></vxe-input>
              <vxe-list
                height="200"
                class="predict-vxe-dropdown"
                :data="processorCodeOptions"
                auto-resize
              >
                <template #default="{ items }">
                  <div
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.value"
                    @click="selectProcessorCode(item, row)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template>
        <template #forecastTypeDefault="{ row }">
          <div v-for="(item, index) in forecastTypeOptions" :key="item">
            <div
              v-if="index === 0 && showForecastTypes.indexOf('D') !== -1 && row.flagField !== 2"
              class="vxe-cell-border"
            >
              {{ item }}
            </div>
            <div
              v-if="index === 1 && showForecastTypes.indexOf('P') !== -1"
              class="vxe-cell-border"
            >
              {{ item }}
            </div>
            <div
              v-if="index === 2 && showForecastTypes.indexOf('A') !== -1 && row.flagField !== 2"
              class="vxe-cell-border"
            >
              {{ item }}
            </div>
          </div>
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import {
  StatusSearchOptions1,
  ForecastTypeDataSource,
  columnData,
  lastColumns,
  NewRowData,
  ToolBar
} from './config/constant'
import { utils } from '@mtech-common/utils'
import * as UTILS from '@/utils/utils'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      rowHeight: 70,
      virtualSupplierCode: '',
      itemCodes: '',
      deliverAddr: null,
      deliveryAddrName: null,
      addId: '1',
      toolbar: ToolBar,
      siteOptions: [], // 工厂下拉选项
      supplierCodeOptions: [],
      processorCodeOptions: [],
      virtualSupplierCodeOptions: [],
      getSupplierDataSource: () => {}, // 供应商 下拉选项
      getVirtualSupplierDataSource: () => {}, // 供应商 下拉选项
      getProcessorDataSource: () => {}, // 供应商 下拉选项
      itemOptions: [],
      getItemDataSource: () => {},
      searchFormModel: {},
      titleList: [],
      tableData: [],
      tooltipConfig: {
        showAll: true,
        enterable: true,
        contentMethod: ({ column, row }) => {
          const { field } = column
          // 重写默认的提示内容
          if (field === 'purchaseOrder') {
            return row.purchaseOrderStr ? row.purchaseOrderStr : ''
          }
          // 其余的单元格使用默认行为
          return null
        }
      },
      columns: columnData,
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      forecastPageCurrent: 1,
      statusOptions: StatusSearchOptions1,
      forecastTypeOptions: ForecastTypeDataSource,
      showForecastTypes: ['D', 'P', 'A'],
      isEdit: false,
      storageLocCodeOptions: [],
      bgTypeOptions: []
    }
  },
  mounted() {
    this.getSite()
    this.getSupplierDataSource = utils.debounce(this.getSupplier, 1000)
    this.getVirtualSupplierDataSource = utils.debounce(this.getSupplier, 1000)
    this.getProcessorDataSource = utils.debounce(this.getSupplier, 1000)
    this.getItemDataSource = utils.debounce(this.getItem, 1000)
    // this.getSupplierDataSource({ value: '', type: 'supplierCode' })
    this.getVirtualSupplierDataSource({ value: '', type: 'virtualSupplierCode' })
    this.getProcessorDataSource({ value: '', type: 'processorCode' })
    this.getBgTypeAndStorageLocCode()
  },
  methods: {
    virtualSupplierChange(e, row) {
      this.setSupplierItem(e, row.materialCode, row)
    },
    setSupplierItem(virtualSupplierCode, materialCode, row) {
      if (virtualSupplierCode && materialCode) {
        this.$API.screenDemand
          .supplierMaterialQuery({ virtualSupplierCode, materialCode })
          .then((res) => {
            const { code, data } = res
            if (code === 200) {
              this.$set(row, 'supplierItemCode', data)
              // row.supplierItemCode = data
            }
          })
      }
    },
    etaLocationCodeChange(e, row) {
      this.setDeliveryAddrName(row.siteCode, e.value, row)
    },
    setDeliveryAddrName(siteCode, etaLocationCode, row) {
      if (siteCode && etaLocationCode) {
        this.$API.screenDemand.deliveryAddressQuery({ siteCode, etaLocationCode }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.$set(row, 'deliveryAddrName', data)
            // row.deliveryAddrName = data
          }
        })
      }
    },
    // 查询条件操作物料编码切割
    itemChange(e) {
      if (e) {
        this.searchFormModel.itemCodeList = this.itemCodes.split(' ')
      } else {
        this.searchFormModel.itemCodeList = null
      }
    },
    getBgTypeAndStorageLocCode() {
      this.$API.screenDemand.screenWarehouseList().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.storageLocCodeOptions = data.deliveryAddrCode
          this.bgTypeOptions = data.bgType
        }
      })
    },
    focusProcessorCode() {
      this.$refs.xDown1.showPanel()
    },
    keyupProcessorCode(e) {
      this.getProcessorDataSource({ ...e, type: 'processorCode' })
    },
    selectProcessorCode(e, row) {
      row.processorCode = e.supplierCode
      row.processorName = e.supplierName
      row.processorTenantId = e.supplierTenantId
      this.$refs.xDown1.hidePanel()
    },
    focusVirtualSupplierCode() {
      this.$refs.xDown2.showPanel()
    },
    keyupVirtualSupplierCode(e) {
      this.getVirtualSupplierDataSource({ ...e, type: 'virtualSupplierCode' })
    },
    selectVirtualSupplierCode(e, row) {
      row.virtualSupplierCode = e.supplierCode
      row.virtualSupplierName = e.supplierName
      row.virtualSupplierTenantId = e.supplierTenantId
    },
    focusSupplierCode() {
      this.$refs.xDown.showPanel()
    },
    keyupSupplierCode(e) {
      this.getSupplierDataSource({ ...e, type: 'supplierCode' })
    },
    selectSupplierCode(e, row) {
      row.supplierCodes = e.supplierCode
    },
    focusItemCode(e, row) {
      if (!row.siteCode) {
        this.$toast({ content: this.$t('请先选择工厂'), type: 'warning' })
        return false
      }
      this.$refs.xDownItem.showPanel()
    },
    keyupItemCode(e, row) {
      this.getItemDataSource(e, row)
    },
    selectItemCode(e, row) {
      row.materialCode = e.itemCode
      row.materialName = e.itemName
      row.materialId = e.id
      this.setSupplierItem(row.virtualSupplierCode, e.itemCode, row)
      this.$refs.xDownItem.hidePanel()
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      if (code === 'CloseEdit') {
        // 清除编辑状态
        this.$refs.xTable.$refs.xGrid.clearEdit()
        this.handleCustomSearch()
        return
      }
      if (this.isEdit) {
        // this.$toast({ content: this.$t('请先完成或结束编辑操作'), type: 'warning' })
        return
      }
      if (!this.tableData.length && code !== 'ForecastImport') {
        this.$toast({ content: this.$t('请先查询数据再进行操作'), type: 'warning' })
        return
      }

      // if (selectedRecords.length == 0 && !commonToolbar.includes(code)) {
      //   this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      //   return
      // }
      const idList = []
      selectedRecords.forEach((item) => {
        idList.push({
          // id: item.id,
          // status: item.status,
          // smallVersionNo: item.smallVersionNo,
          // supplierList: item.supplierList,
          // supplierCodes: item.supplierCodes,
          // processorCode: item.processorCode,
          // processorTenantId: item.processorTenantId,
          // processorName: item.processorName,
          // virtualSupplierCode: item.virtualSupplierCode,
          // virtualSupplierTenantId: item.virtualSupplierTenantId,
          // virtualSupplierName: item.virtualSupplierName,
          ...item,
          line: item.lineNo,
          supplierCodes: item.supplierList ? item.supplierList.map((i) => i.supplierCode) : null
        })
      })

      if (code === 'ForecastAdd') {
        // 新增
        const currentViewRecords = $grid.getTableData().visibleData
        if (!currentViewRecords.length) {
          this.$toast({
            content: this.$t('请先查询数据再进行新增操作'),
            type: 'warning'
          })
          return
        }
        this.titleList.forEach((itemTitle) => {
          const forecastItem = {
            buyerNum: null,
            total: null,
            planGroupNum: null
          }
          NewRowData[`title_${itemTitle}`] = forecastItem
        })
        // 新增一行
        $grid.insert([NewRowData])
        this.$nextTick(() => {
          // 获取最新的表格视图数据
          const currentViewRecords = $grid.getTableData().visibleData
          // 将新增的那一条设置为编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow(currentViewRecords[0])
        })
      } else if (code === 'ForecastDelete') {
        // 删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: idList.length
              ? this.$t('确认删除选中的数据？')
              : this.$t('确认删除符合筛选条件的数据？')
          },
          success: () => {
            this.postDeliveryScheduleDelete(idList)
          }
        })
      } else if (code === 'ForecastPublish') {
        // 发布
        this.postDeliverySchedulePublish(idList)
      } else if (code === 'ForecastImport') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('导入内容如果包含已发布数据，修改后会直接发布，是否继续导入？')
          },
          success: () => {
            this.$dialog({
              modal: () => import('@/components/uploadDialog'),
              data: {
                title: this.$t('导入'),
                importApi: this.$API.screenDemand.screenScheduleImport,
                downloadTemplateApi: this.$API.screenDemand.screenScheduleImportTemp,
                paramsKey: 'excel'
                // saveButtonText: this.$t('下一步')
                // asyncParams: {
                //   // requestJson: JSON.stringify(parameter),
                // },
              },
              success: () => {
                // 导入之后刷新列表
                // this.$refs.templateRef.refreshCurrentGridData()
                this.handleCustomSearch()
              }
            })
          }
        })

        return
      } else if (code === 'ForecastExport') {
        // 导出
        // if (selectedRecords.length == 0) {
        //   this.$toast({ content: this.$t('请先选择需要导出的数据'), type: 'warning' })
        //   return
        // }
        this.postDeliveryScheduleExport(idList)
      }
    },
    // 采方删除信息
    postDeliveryScheduleDelete(chooseDeleteReqList) {
      this.apiStartLoading()
      this.$API.screenDemand
        .screenScheduleDelete({
          chooseDeleteReqList,
          queryRulesReq: {
            ...this.searchFormModel,
            page: {
              size: 999999,
              current: 1
            }
          }
        })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 采方-获取采方信息列表
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方发布信息接口
    postDeliverySchedulePublish(choosePublishReqList) {
      this.apiStartLoading()
      this.$API.screenDemand
        .screenSchedulePublish({
          choosePublishReqList,
          queryRulesReq: {
            ...this.searchFormModel,
            page: {
              size: 999999,
              current: 1
            }
          }
        })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 采方-获取采方信息列表
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方-导出
    postDeliveryScheduleExport(idList) {
      const params = {
        ids: idList.map((i) => i.id),
        ...this.searchFormModel,
        page: {
          size: 999999,
          current: 1
        }
      }
      this.$store.commit('startLoading')
      this.$API.screenDemand.screenScheduleeExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 主数据 供应商
    getSupplier(args) {
      const { value, type } = args
      const params = {
        fuzzyNameOrCode: value ? value : ''
      }
      this.$API.masterData.getSupplier(params).then((res) => {
        if (res) {
          const list = res?.data || []
          const newData = list.map((i) => {
            return {
              ...i,
              label: `${i.supplierCode}-${i.supplierName}`,
              value: i.supplierCode
            }
          })
          if (type === 'supplierCode') {
            this.supplierCodeOptions = [...newData]
          } else if (type === 'virtualSupplierCode') {
            this.virtualSupplierCodeOptions = [...newData]
          } else if (type === 'processorCode') {
            this.processorCodeOptions = [...newData]
          }
        }
      })
    },
    // 主数据 物料
    getItem(args, row) {
      const { value } = args
      const params = {
        page: { current: 1, size: 100 },
        // condition: 'or',
        rules: [
          {
            label: this.$t('物料编号'),
            field: 'itemCode',
            type: 'string',
            operator: 'contains',
            value
          }
          // {
          //   label: this.$t('物料名称'),
          //   field: 'itemName',
          //   type: 'string',
          //   operator: 'contains',
          //   value
          // }
        ],
        defaultRules: [
          {
            field: 'organizationCode',
            operator: 'equal',
            value: row.siteCode
          }
        ]
      }
      this.$API.masterData.getItemPage(params).then((res) => {
        if (res) {
          const list = res?.data?.records || []
          const newData = list.map((i) => {
            return {
              ...i,
              label: `${i.itemCode}-${i.itemName}`,
              value: i.itemCode
            }
          })
          // this.itemOptions = [...newData, ...this.itemOptions]
          this.itemOptions = [...newData]
        }
      })
    },
    factoryChange(val, row) {
      const { value } = val
      this.siteOptions.forEach((e) => {
        if (e.siteCode === value || e.organizationCode === value) {
          row.siteId = e.id // id
          // row.siteCode = e.organizationCode // code
          // row.factoryName = e.organizationName // name
          row.siteCode = e.organizationCode ? e.organizationCode : e.siteCode // code
          row.siteName = e.organizationName ? e.organizationName : e.siteName // name
          this.getItemDataSource({ value: '' }, row)
          this.setDeliveryAddrName(row.siteCode, row.etaLocationCode, row)
        }
      })
    },
    // 主数据 根据物料 查询关联的工厂列表
    getSite() {
      this.$API.masterData.getSiteFindByPermission().then((res) => {
        const list = res?.data || []
        this.siteOptions = list.map((i) => {
          return {
            ...i,
            label: `${i.siteCode}-${i.siteName}`,
            value: i.siteCode
          }
        })
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 校验点击的对象是否为取消编辑按钮
        if (args.$event.target.innerText === this.$t('取消编辑')) {
          // 清除编辑状态
          this.$refs.xTable.$refs.xGrid.clearEdit()
          this.handleCustomSearch()
          return
        }
        // 远程数据才有$event属性
        //1、 校验必填 没通过就是this.$refs.xTable.$refs.xGrid.setEditRow(row)
        if (!this.isValidData(row)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow(row)
          return
        }
        //2、 判断是否有row.bigVersionNo调新增或者编辑接口
        if (row.bigVersionNo) {
          this.postDeliveryScheduleSaveForecast({ data: row })
        } else {
          this.postDeliveryScheduleBatchInsert({ data: row })
        }
        //3、 接口调用成功调刷新接口
      }
    },
    editBegin(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        this.getItemDataSource({ value: row.materialCode }, row)
        // this.getSupplierDataSource({ value: row.supplierCode, type: 'supplierCode' })
        this.getVirtualSupplierDataSource({
          value: row.virtualSupplierCode,
          type: 'virtualSupplierCode'
        })
        this.getProcessorDataSource({ value: row.processorCode, type: 'processorCode' })
      } else {
        // this.getSupplierDataSource({ value: '', type: 'supplierCode' })
        this.getVirtualSupplierDataSource({ value: '', type: 'virtualSupplierCode' })
        this.getProcessorDataSource({ value: '', type: 'processorCode' })
      }
      this.isEdit = true
    },
    // 采方-批量写入数据
    postDeliveryScheduleBatchInsert(args) {
      const { data } = args
      let tvScreenDemandPlanDetailInfo = {
        id: '',
        mainFormId: '',
        type: 'P',
        dateText: ''
      }
      if (data && data.detailDtoList && data.detailDtoList.length) {
        data.detailDtoList.forEach((item) => {
          if (item.type === 'P') {
            tvScreenDemandPlanDetailInfo = item
          }
        })
      }
      if (this.titleList && this.titleList.length) {
        this.titleList.forEach((title, index) => {
          if (index === 0) {
            tvScreenDemandPlanDetailInfo['weeklySum'] =
              data[`title_${this.titleList[0]}`]['buyerNum'] || 0
          } else {
            tvScreenDemandPlanDetailInfo[`planDemandQty${index}`] =
              data[`title_${this.titleList[index]}`]['buyerNum'] || 0
          }
        })
      }
      const params = {
        id: data.id || '',
        versionId: data.bigVersionNo || '',
        materialCode: data.materialCode || '',
        materialName: data.materialName || '',
        siteCode: data.siteCode || '',
        virtualSupplierCode: data.virtualSupplierCode || '',
        supplierItemCode: data.supplierItemCode || '',
        deliverPlaceDesc: data.deliverAddr || '',
        deliveryAddrName: data.deliveryAddrName || '',
        rbcCode: data.rbcCode || '',
        bgType: data.bgType || '',
        etaLocationCode: data.etaLocationCode || '',
        processorCode: data.processorCode || '',
        deliveryAddrCode: data.deliveryAddrCode || '',
        deliverAddr: data.deliverAddr || '',
        dateText: this.titleList.map((i) => i.substr(1)),
        tvScreenDemandPlanDetailInfo
      }
      this.apiStartLoading()
      this.$API.screenDemand
        .screenScheduleAdd(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 采方-获取采方信息列表
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow(data)
        })
    },
    // 采方-修改信息
    postDeliveryScheduleSaveForecast(args) {
      const { data } = args
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message:
            data.status === 2
              ? this.$t('该数据已发布,修改后会直接发布,是否修改该数据')
              : this.$t('确认修改该数据？')
        },
        success: () => {
          const { data } = args
          let tvScreenDemandPlanDetailInfo = {
            id: '',
            mainFormId: '',
            type: 'P',
            dateText: ''
          }
          if (data && data.detailDtoList && data.detailDtoList.length) {
            data.detailDtoList.forEach((item) => {
              if (item.type === 'P') {
                tvScreenDemandPlanDetailInfo = item
              }
            })
          }
          if (this.titleList && this.titleList.length) {
            this.titleList.forEach((title, index) => {
              if (index === 0) {
                tvScreenDemandPlanDetailInfo['weeklySum'] =
                  data[`title_${this.titleList[0]}`]['buyerNum'] || 0
              } else {
                tvScreenDemandPlanDetailInfo[`planDemandQty${index}`] =
                  data[`title_${this.titleList[index]}`]['buyerNum'] || 0
              }
            })
          }
          const params = {
            // versionId: data.bigVersionNo || '',
            // materialCode: data.materialCode || '',
            // siteCode: data.siteCode || '',
            // virtualSupplierCode: data.virtualSupplierCode || '',
            // supplierItemCode: data.supplierItemCode || '',
            // deliverPlaceDesc: data.deliverAddr || '',
            // deliveryAddrName: data.deliveryAddrName || '',
            // rbcCode: data.rbcCode || '',
            // bgType: data.bgType || '',
            // etaLocationCode: data.etaLocationCode || '',
            // processorCode: data.processorCode || '',
            // deliveryAddrCode: data.deliveryAddrCode || '',
            // deliverAddr: data.deliverAddr || '',
            // dateText: this.titleList.map((i) => i.substr(1)),
            ...tvScreenDemandPlanDetailInfo,
            // id: data.id,
            status: data.status,
            processorCode: data.processorCode,
            remark: data.remark
          }
          this.apiStartLoading()
          this.$API.screenDemand
            .screenScheduleUpdate(params)
            .then((res) => {
              this.apiEndLoading()
              if (res?.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                // 采方-获取采方信息列表
                this.handleCustomSearch()
              }
            })
            .catch(() => {
              this.apiEndLoading()
              // 当出现错误时，指定行进入编辑状态
              this.$refs.xTable.$refs.xGrid.setEditRow(data)
            })
        },
        close: () => {
          this.$refs.xTable.$refs.xGrid.setEditRow(data)
        }
      })
    },
    activeRowMethod({ row }) {
      // 已确认的数据不可修改
      if (row.status === 5) {
        return false
      }
      return true
    },
    editDisabledEvent() {
      this.$toast({
        content: this.$t('此状态数据不可编辑'),
        type: 'warning'
      })
    },
    // 校验数据
    isValidData(data) {
      const { materialCode, siteCode } = data
      let valid = false
      if (!siteCode) {
        // 工厂代码
        this.$toast({ content: this.$t('工厂不可为空'), type: 'warning' })
      } else if (!materialCode) {
        // 物料代码
        this.$toast({ content: this.$t('物料不可为空'), type: 'warning' })
        // } else if (!supplierCode) {
        //   // 供应商代码
        //   this.$toast({ content: this.$t('供应商不可为空'), type: 'warning' })
      } else {
        valid = true
      }

      return valid
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.itemCodes = null
      this.searchFormModel.itemCodeList = null
      this.virtualSupplierCode = null
      this.searchFormModel.virtualSupplierCodeList = null
      this.deliverAddr = null
      this.deliveryAddrName = null
      this.forecastPageCurrent = 1
      this.handleCustomSearch()
    },
    // 采方-获取采方信息列表
    handleCustomSearch() {
      this.isEdit = false
      const params = {
        ...this.searchFormModel,
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        }
      }
      if (
        !this.searchFormModel?.typeList ||
        this.searchFormModel?.typeList?.length === 0 ||
        this.searchFormModel?.typeList?.length === 3
      ) {
        this.showForecastTypes = ['D', 'P', 'A']
        this.rowHeight = 70
      } else if (this.searchFormModel?.typeList?.length === 2) {
        this.showForecastTypes = this.searchFormModel?.typeList
        this.rowHeight = 48
      } else if (this.searchFormModel?.typeList?.length === 1) {
        this.showForecastTypes = this.searchFormModel?.typeList
        this.rowHeight = 24
      }
      this.apiStartLoading()
      this.tableData = []
      this.$API.screenDemand
        .screenScheduleQuery(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.screenDemandPlanDtoPage?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.totalRecordsCount = Number(total)
            const records = res?.data?.screenDemandPlanDtoPage?.records || [] // 表格数据
            const titleList =
              res?.data?.titleList.map((item, index) => {
                if (index === 0) {
                  return 'W' + item
                } else {
                  return 'D' + item
                }
              }) || [] // 动态表头数据
            this.titleList = titleList // 动态表头数据
            // 处理表头数据
            this.columns = this.handleColumns({ titleList })
            // 处理表数据
            this.tableData = this.handleDataSource({ records, titleList })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleColumns(data) {
      const { titleList } = data
      // 固定的表头
      const constantColumns = columnData
      // 动态的日期表头
      const titleListColumnData = []
      titleList.forEach((item, index) => {
        const title = `title_${titleList[index]}`
        titleListColumnData.push({
          field: title,
          title: item,
          minWidth: 128,
          showOverflow: true,
          editRender: {},
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <div>
                  <div
                    v-show={this.showForecastTypes.indexOf('D') !== -1 && row.flagField !== 2}
                    class='vxe-cell-border'
                  >
                    {row[title]?.total}
                  </div>
                  <div v-show={this.showForecastTypes.indexOf('P') !== -1} class='vxe-cell-border'>
                    {row[title]?.buyerNum}
                  </div>
                  <div
                    v-show={this.showForecastTypes.indexOf('A') !== -1 && row.flagField !== 2}
                    class='vxe-cell-border'
                  >
                    {row[title]?.planGroupNum}
                  </div>
                </div>
              ]
            },
            edit: ({ row }) => {
              // <div v-show={this.showForecastTypes.indexOf('P') !== -1} class='vxe-cell-border'>
              //   <mt-input-number
              //     show-spin-button={false}
              //     show-clear-button={false}
              //     v-model={row[title]['buyerNum']}
              //     v-show={columnIndex >= 35}
              //   />
              //   <div v-show={columnIndex < 35}>{row[title]['buyerNum']}</div>
              // </div>
              return [
                <div>
                  <div
                    v-show={this.showForecastTypes.indexOf('D') !== -1 && row.flagField !== 2}
                    class='vxe-cell-border'
                  >
                    {row[title]?.total}
                  </div>
                  <div v-show={this.showForecastTypes.indexOf('P') !== -1} class='vxe-cell-border'>
                    <mt-input-number
                      show-spin-button={false}
                      show-clear-button={false}
                      v-model={row[title].buyerNum}
                      v-show={!title.includes('W')}
                    />
                    <span v-show={title.includes('W')}>{row[title]?.buyerNum}</span>
                  </div>
                  <div
                    v-show={this.showForecastTypes.indexOf('A') !== -1 && row.flagField !== 2}
                    class='vxe-cell-border'
                  >
                    {row[title]?.planGroupNum}
                  </div>
                </div>
              ]
            }
          }
        })
      })

      // 合并表头数组，然后 push 到 forecastColumnData 中
      const columns = [].concat(constantColumns).concat(titleListColumnData).concat(lastColumns)
      return columns
    },
    handleDataSource(args) {
      const { records, titleList } = args
      records.forEach((obj, idx) => {
        obj.addId = this.addId++
        obj.lineNo = idx + 1
        obj.purchaseOrderStr = ''
        obj.purchaseOrder?.forEach((i) => {
          obj.purchaseOrderStr = obj.purchaseOrderStr + i + '\n'
        })
        if (obj.detailDtoList && obj.detailDtoList.length > 0) {
          titleList.forEach((title, index) => {
            let total = ''
            let buyerNum = ''
            let planGroupNum = ''
            // let gapNum = ''
            // let countGapNum = ''
            obj.detailDtoList.forEach((itm) => {
              if (!itm) {
                total = ''
                buyerNum = ''
                planGroupNum = ''
              }
              if (itm.type === 'D') {
                if (index === 0) {
                  total = itm[`weeklySum`]
                } else {
                  total = itm[`planDemandQty${index}`]
                }
              }
              if (itm.type === 'P') {
                if (index === 0) {
                  buyerNum = itm[`weeklySum`]
                } else {
                  buyerNum = itm[`planDemandQty${index}`]
                }
              }
              if (itm.type === 'A') {
                if (index === 0) {
                  planGroupNum = itm[`weeklySum`]
                } else {
                  planGroupNum = itm[`planDemandQty${index}`]
                }
              }
            })
            obj[`title_${title}`] = {
              total,
              buyerNum,
              planGroupNum
            }
          })
        }
      })
      return records
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  div .vxe-cell-border {
    border: solid #e6e9ed 1px;
    height: 24px;
    box-sizing: border-box;
  }
}
/deep/ .col--seq,
/deep/ .col--checkbox {
  .c--tooltip {
    padding: 0 10px;
  }
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
