<template>
  <!-- 采方-对账协同-采购对账单列表 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
      class="template-height"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="bigVersionNo" :label="$t('大版本号')" label-style="top">
              <mt-input
                v-model="searchFormModel.bigVersionNo"
                :show-clear-button="true"
                :placeholder="$t('请输入大版本号')"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('拉取状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.status"
                :show-clear-button="true"
                :data-source="[
                  {
                    text: this.$t('成功'),
                    value: 0
                  },
                  {
                    text: this.$t('失败'),
                    value: 1
                  }
                ]"
                :placeholder="$t('请输入拉取状态')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {
        status: 0
      },
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          toolbar: [
            {
              id: 'syncScmDemand',
              icon: 'icon_table_restart',
              title: this.$t('拉取SCM需求')
            }
          ],
          grid: {
            columnData: [
              {
                field: 'id',
                headerText: 'id',
                width: 0,
                visible: false,
                isPrimaryKey: true
              },
              {
                width: '120',
                field: 'bigVersionNo',
                headerText: this.$t('大版本号'),
                cellTools: []
              },
              {
                width: '120',
                field: 'dsmallVersionNo',
                headerText: this.$t('小版本号')
              },
              {
                // width: "150",
                field: 'status',
                headerText: this.$t('拉取状态'),
                valueConverter: {
                  type: 'map',
                  map: {
                    0: this.$t('成功'),
                    1: this.$t('失败')
                  }
                }
              },
              {
                width: '120',
                field: 'qty',
                headerText: this.$t('数量行数')
              },
              {
                width: '120',
                field: 'errorQty',
                headerText: this.$t('日志错误信息')
              },
              {
                width: '150',
                field: 'createTime',
                headerText: this.$t('创建日期')
              },
              {
                // width: '230',
                field: 'createUserName',
                headerText: this.$t('创建人')
              },
              {
                width: '150',
                field: 'updateTime',
                headerText: this.$t('最后更新日期')
              },
              {
                width: '120',
                field: 'updateUserName',
                headerText: this.$t('更新人')
              }
            ],
            dataSource: [],
            pageSettings: {
              currentPage: 1,
              pageSize: 10,
              pageSizes: [10, 50, 100, 200],
              totalRecordsCount: 0
            },
            asyncConfig: {
              url: `/srm-purchase-execute/internal/screen/sync/record/v1/query`,
              recordsPosition: 'data.records',
              params: {}
            },
            // lineSelection: 0,
            lineIndex: 1
            // frozenColumns: 1
          }
        }
      ]
    }
  },
  methods: {
    //单元格按钮，点击
    handleClickCellTitle(e) {
      if (e.field == 'bigVersionNo') {
        this.$router.push({
          path: `screen-demand-query-preview`,
          query: {
            timeStamp: new Date().getTime(),
            version: e.data.bigVersionNo,
            dsmallVersionNo: e.data.dsmallVersionNo,
            versionDate: e.data.drowVersionDate
          }
          // key: utils.randomString(),
          // query: { type: "add" },
        })
      }
    },
    handleClickToolBar(e) {
      if (e.toolbar.id === 'syncScmDemand') {
        this.apiStartLoading()
        this.$API.screenDemand
          .syncScm()
          .then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('拉取SCM需求成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
          .finally(() => {
            this.apiEndLoading()
          })
      }
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'status') {
            this.searchFormModel[key] = 0
          }
        }
      }
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style></style>
