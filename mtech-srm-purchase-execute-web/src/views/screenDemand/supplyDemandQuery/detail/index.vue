<template>
  <!-- 预测管理-采方 -->
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <!-- <div style="position: relative; z-index: 1; height: 30px">
      <span
        style="color: #2783fe; cursor: pointer; position: absolute; right: 0"
        @click="$router.go(-1)"
        >返回</span
      >
    </div> -->
    <collapse-search
      :is-grid-display="true"
      default-max-height="60"
      @reset="handleCustomReset"
      @search="handleCustomSearch"
    >
      <template slot="buttons-bar">
        <span type="info" @click="$router.go(-1)">{{ $t('返回') }}</span>
        <span type="info" @click="handleCustomReset()">{{ $t('重置') }}</span>
        <span type="primary" @click="handleCustomSearch()">{{ $t('查询') }}</span>
      </template>
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="supplierCodes" :label="$t('供应商')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.supplierCodes"
            :data-source="screenSupplierList"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="logicFactory" :label="$t('逻辑工厂')" label-style="top">
          <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.logicFactory"
            :url="$API.masterData.getSiteListUrl"
            :placeholder="$t('请选择逻辑工厂')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <mt-input v-model="searchFormModel.itemCode"></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="specCode" :label="$t('规格')" label-style="top">
          <mt-input v-model="searchFormModel.specCode"></mt-input>
        </mt-form-item> -->
        <mt-form-item prop="specCodes" :label="$t('规格')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.specCodes"
            :data-source="specList"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.categoryCode" />
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.itemName" />
        </mt-form-item>
        <mt-form-item prop="supplierItemCodes" :label="$t('供应商物料编码')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.supplierItemCodes"
            :data-source="screenSupplierItemList"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <!-- <mt-form-item prop="status" :label="$t('类型')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="forecastTypes"
            :data-source="forecastTypeOptions"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item> -->
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        :row-config="{ height: 30 }"
        :columns="columns"
        :table-data="tableData"
        show-overflow
        height="auto"
        header-align="left"
        align="left"
        style="padding-top: unset"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true,
          activeMethod: this.activeRowMethod
        }"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        @edit-closed="editComplete"
        @edit-actived="editBegin"
        @edit-disabled="editDisabledEvent"
      >
        <template slot="custom-tools">
          <!-- :icon="item.icon" -->
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
          <span style="margin-left: 10px"
            >{{ $t('大版本号') }}：{{ searchFormModel.bigVersionNo }}
          </span>
          <span style="margin-left: 10px"
            >{{ $t('小版本号') }}：{{ $route.query.dsmallVersionNo }}
          </span>
          <span style="margin-left: 10px"
            >{{ $t('版本日期') }}：{{ $route.query.versionDate }}</span
          >
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import {
  StatusSearchOptions,
  ForecastTypeDataSource,
  ForecastColumnData,
  ToolBar,
  lastColumn
} from './config/constant'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      toolbar: ToolBar,
      siteOptions: [], // 工厂下拉选项
      supplierOptions: [],
      getSupplierDataSource: () => {}, // 供应商 下拉选项
      itemOptions: [],
      getItemDataSource: () => {},
      apiWaitingQuantity: 0, // 调用的api正在等待数
      searchFormModel: {
        // supplierCode: null,
        // logicFactory: null,
        // specCode: null,
        // categoryCode: null,
        bigVersionNo: this.$route.query.version
      },
      titleList: [],
      tableData: [],
      columns: ForecastColumnData,
      plannerListOptions: [], // 计划员 下列选项
      forecastPageCurrent: 1,
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      isEditing: false, // 正在编辑数据
      exportRules: [], //导出规则
      statusOptions: StatusSearchOptions,
      forecastTypeOptions: ForecastTypeDataSource,
      copySearchFormModel: null,
      isEdit: false,
      screenSupplierList: [],
      screenSupplierItemList: [],
      specList: []
    }
  },
  mounted() {
    this.getSpecList()
    this.getScreenSupplierItem()
    this.getScreenSupplier()
    this.handleCustomSearch()
  },
  methods: {
    getScreenSupplierItem() {
      this.$API.screenDemand
        .getScreenSupplierItemCode({ bigVersionNo: this.$route.query.version })
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.screenSupplierItemList = []
            data.forEach((i) => {
              if (i.supplierItemCode && i.itemName) {
                this.screenSupplierItemList.push({
                  text: `${i.supplierItemCode}-${i.itemName}`,
                  value: i.supplierItemCode
                })
              }
            })
          }
        })
    },
    getSpecList() {
      this.$API.screenDemand
        .screenDemandSupplyBuyerQuerySpecAll({ bigVersionNo: this.$route.query.version })
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.specList = data
          }
        })
    },
    getScreenSupplier() {
      this.$API.screenDemand.getScreenSupplier().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.screenSupplierList = data.map((i) => {
            return {
              text: `${i.supplierCode}-${i.supplierName}`,
              value: i.supplierCode
            }
          })
        }
      })
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      const commonToolbar = [
        'ForecastUpdate',
        'ForecastAdd',
        'ForecastImport',
        'Filter',
        'ForecastDelete',
        'ForecastPublish',
        'ForecastConfirm',
        'Refresh',
        'refreshDataByLocal',
        'filterDataByLocal',
        'CloseEdit',
        'resetDataByLocal',
        'ForecastExport',
        'Setting'
      ]
      if (code === 'CloseEdit') {
        // 清除编辑状态
        this.$refs.xTable.$refs.xGrid.clearEdit()
        this.handleCustomSearch()
        return
      }
      if (this.isEdit) {
        // this.$toast({ content: this.$t('请先完成或结束编辑操作'), type: 'warning' })
        return
      }

      if (selectedRecords.length == 0 && !commonToolbar.includes(code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })
      if (code === 'ForecastImport') {
        // // 导入
        // this.$refs.importDialog.init({
        //   title: this.$t('导入')
        // })
        this.$dialog({
          modal: () => import('@/components/uploadDialog'),
          data: {
            title: this.$t('导入'),
            importApi: this.$API.predictCollaboration.postBuyerForecastImportTv,
            downloadTemplateApi: this.$API.predictCollaboration.postBuyerForecastExportTemplateTv,
            paramsKey: 'excel'
            // saveButtonText: this.$t('下一步')
            // asyncParams: {
            //   // requestJson: JSON.stringify(parameter),
            // },
          },
          success: () => {
            // 导入之后刷新列表
            // this.$refs.templateRef.refreshCurrentGridData()
            this.handleCustomSearch()
          }
        })
        return
      } else if (code === 'ForecastExport') {
        // 导出
        // if (selectedRecords.length == 0) {
        //   this.$toast({ content: this.$t('请先选择需要导出的数据'), type: 'warning' })
        //   return
        // }
        this.postBuyerForecastExport(selectedRecords)
      }
    },
    // 采方-导出
    postBuyerForecastExport(selectedRecords) {
      const params = {
        forecastIds: selectedRecords.map((i) => i.id),
        tvForecastQueryReq: {
          ...this.searchFormModel
        }
      }
      this.apiStartLoading()
      this.$API.predictCollaboration.postBuyerForecastExportTv(params).then((res) => {
        this.apiEndLoading()
        if (res.code === 200) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('导出任务添加成功，请到任务中心查看')
            },
            success: () => {
              this.$router.push(`/middlePlatform/task-center?taskCode=${res.data}`)
            }
          })
        }
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 校验点击的对象是否为取消编辑按钮
        if (args.$event.target.innerText === this.$t('取消编辑')) {
          // 清除编辑状态
          this.$refs.xTable.$refs.xGrid.clearEdit()
          this.handleCustomSearch()
          return
        }
        // 远程数据才有$event属性
        //1、 校验必填 没通过就是this.$refs.xTable.$refs.xGrid.setEditRow(row)
        if (!this.isValidData(row)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow(row)
          return
        }
        //2、 判断是否有row.planner调新增或者编辑接口
        if (row.planner) {
          this.postBuyerForecastSaveForecast({ data: row })
        } else {
          this.postBuyerForecastBatchInsert({ data: row })
        }
        //3、 接口调用成功调刷新接口
      }
    },
    editBegin(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        this.getItemDataSource({ value: row.itemCode }, row)
        this.getSupplierDataSource({ value: row.supplierCode })
      } else {
        this.getSupplierDataSource({ value: '' })
      }
      this.isEdit = true
    },
    activeRowMethod({ row }) {
      // 已确认的数据不可修改
      if (row.status === 5) {
        return false
      }
      return true
    },
    editDisabledEvent() {
      this.$toast({
        content: this.$t('此状态数据不可编辑'),
        type: 'warning'
      })
    },
    // 校验数据
    isValidData(data) {
      const { itemCode, factoryCode, supplierCode } = data
      let valid = false
      if (!factoryCode) {
        // 工厂代码
        this.$toast({ content: this.$t('工厂不可为空'), type: 'warning' })
      } else if (!itemCode) {
        // 物料代码
        this.$toast({ content: this.$t('物料不可为空'), type: 'warning' })
      } else if (!supplierCode) {
        // 供应商代码
        this.$toast({ content: this.$t('供应商不可为空'), type: 'warning' })
      } else {
        valid = true
      }

      return valid
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'bigVersionNo') {
            this.searchFormModel[key] = this.$route.query.version
          }
        }
      }
      this.handleCustomSearch()
    },
    // 采方-获取采方预测信息列表
    handleCustomSearch() {
      // if (!this.searchFormModel.supplierCode) {
      //   this.$toast({ content: this.$t('请选择供应商'), type: 'warning' })
      // }
      this.isEdit = false
      const params = {
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        },
        ...this.searchFormModel
      }
      this.apiStartLoading()
      this.$API.screenDemand
        .screenDemandQuery(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.totalRecordsCount = Number(total)
            const records = res?.data?.records || [] // 表格数据
            const titleList = res?.data?.records[0]?.headers || [] // 动态表头数据
            this.titleList = res?.data?.records[0]?.headers || [] // 动态表头数据
            // 处理表头数据
            this.columns = this.handleColumns({ titleList })
            // 处理表数据
            this.tableData = this.handleDataSource({ records, titleList })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleColumns(data) {
      const { titleList } = data
      // 固定的表头
      const forecastColumns = ForecastColumnData
      // 动态的日期表头
      const titleListColumnData = []
      titleList.forEach((item, index) => {
        const title = `title_${titleList[index]}`
        titleListColumnData.push({
          field: title,
          title: item,
          minWidth: 143,
          // showOverflow: true,
          // editRender: {},
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <div>
                  <div class='vxe-cell-border'>{row[title]['supplierNum']}</div>
                </div>
              ]
            },
            edit: ({ row }) => {
              return [
                <div>
                  <div class='vxe-cell-border'>{row[title]['supplierNum']}</div>
                </div>
              ]
            }
          }
        })
      })
      // 合并表头数组，然后 push 到 forecastColumnData 中
      const columns = [].concat(forecastColumns).concat(titleListColumnData).concat(lastColumn)
      return columns
    },
    handleDataSource(data) {
      const { records, titleList } = data
      const tableData = []
      records.forEach((recordsItem) => {
        const rowData = {
          ...recordsItem.tvScreenForecastIntegration,
          crow: recordsItem.row
        }
        // recordsItem.thePrimaryKey = recordsItem.tvScreenForecastIntegration.id // 主键的值为 API 数据的 id
        // recordsItem.status = recordsItem.tvScreenForecastIntegration.status ?? 0 // 状态为 undefined/null 时，默认为新建状态（导入时）

        const row = recordsItem?.row
        titleList.forEach((itemTitle, index) => {
          row.forEach(() => {
            // 将预测数据赋给表头对应的对象
            const forecastItem = {
              supplierNum: row[index] || 0
            }
            rowData[`title_${itemTitle}`] = forecastItem
          })
        })
        tableData.push(rowData)
      })

      return tableData
      // return records
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  // div .vxe-cell-border {
  //   border: solid #e6e9ed 1px;
  // }
}
/deep/ .col--seq,
/deep/ .col--checkbox {
  .c--tooltip {
    padding: 0 10px;
  }
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
