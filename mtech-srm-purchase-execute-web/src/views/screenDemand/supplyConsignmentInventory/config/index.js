import { i18n } from '@/main.js'

const columnData = () => {
  const column = [
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      width: '120'
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      width: '200'
    },
    {
      field: 'warehouseFlag',
      headerText: i18n.t('库存标识'),
      width: '90'
    },
    {
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      width: '80'
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      width: '100'
    },
    {
      field: 'warehouseCode',
      headerText: i18n.t('库位'),
      width: '80'
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      width: '100'
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      width: '150'
    },
    {
      field: 'unitCode',
      headerText: i18n.t('单位'),
      width: '60'
    },
    {
      field: 'onHandQuantity',
      headerText: i18n.t('非限制使用库存数量'),
      width: '60'
    },
    {
      field: 'qualityQuantity',
      headerText: i18n.t('质检库存数量'),
      width: '60'
    }
  ]
  return column
}

export const componentConfig = (that) => {
  const config = [
    {
      isUseCustomSearch: true, // 是否使用自定义查询
      isCustomSearchRules: true,
      useToolTemplate: false, // 此项不使用预置的表格操作按钮'新增、编辑、删除'
      toolbar: {
        tools: [
          [
            {
              id: 'export',
              icon: 'icon_solid_Import',
              // permission: ['O_02_1355'],
              title: i18n.t('导出')
            }
          ],
          ['Setting']
        ]
      },
      grid: {
        allowPaging: true, // 是否使用内置分页器
        lineIndex: 0,
        columnData: columnData(that),
        asyncConfig: {
          ignoreDefaultSearch: true,
          ignoreSearchToast: true,
          params: {
            tenantType: 2
          },
          url: '/srm-purchase-execute/tenant/sap/consignment/inv/query',
          recordsPosition: 'data'
        }
      }
    }
  ]
  return config
}
