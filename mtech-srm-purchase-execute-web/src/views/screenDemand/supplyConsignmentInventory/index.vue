<!-- 寄售库存 - 供方 -->
<template>
  <div class="full-height inv-wrapper">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      @handleCustomReset="handleCustomReset"
      @handleClickToolBar="handleClickToolBar"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
          <mt-form-item prop="itemCodeStr" :label="$t('物料编码')">
            <mt-input
              v-model="searchFormModel.itemCodeStr"
              :show-clear-button="true"
              :placeholder="$t('请输入物料编码')"
            />
          </mt-form-item>
          <mt-form-item prop="siteCode" :label="$t('工厂')">
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.siteCode"
              :url="$API.masterData.getSiteAuthFuzzyUrl"
              :placeholder="$t('请选择工厂')"
              :multiple="true"
              :fields="{ text: 'siteName', value: 'siteCode' }"
              params-key="fuzzyParam"
              records-position="data"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="warehouseCode" :label="$t('库位')">
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.warehouseCode"
              :multiple="true"
              url="/masterDataManagement/auth/location/auth-sup-fuzzy"
              :placeholder="$t('请选择库位')"
              :fields="{ text: 'locationName', value: 'locationCode' }"
              records-position="data"
            />
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { componentConfig } from './config/index'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      componentConfig: componentConfig(this),
      searchFormModel: {},
      searchFormRules: {
        supplierCodeStr: [{ required: true, message: this.$t('请选择供应商编码'), trigger: 'blur' }]
      },
      invDatesOptions: []
    }
  },
  created() {},
  methods: {
    handleClickToolBar(args) {
      const { toolbar } = args
      if (toolbar.id === 'export') {
        this.handleExport()
      }
    },
    // 重置
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 导出
    handleExport() {
      const params = {
        page: {
          current: 1,
          size: 5000
        },
        tenantType: 2,
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.screenDemand.exportConsignmentInv(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>
<style>
.inv-wrapper .form-box {
  max-height: 65px !important;
}
</style>
