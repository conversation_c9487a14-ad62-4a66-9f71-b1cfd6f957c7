// VMI领料管理-供方
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :current-tab="currentTab"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnObj } from './config/index.js'
export default {
  components: {
    // addDialog: require("./components/addDialog.vue").default,
  },
  data() {
    return {
      currentTab: 0, //代表当前默认加载显示的Tab索引
      pageConfig: [
        {
          tab: { title: this.$t('头视图') },
          useBaseConfig: false, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'confirm',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('供方确认')
                },
                {
                  id: 'supplierRrturn',
                  icon: 'icon_list_recall',
                  title: this.$t('供方退回')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columnObj.headColumn,
            // dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmi-pickup-order/supplier-page-query'
              // recordsPosition: "data",
            },
            frozenColumns: 1
          }
        },
        {
          tab: { title: this.$t('明细视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'confirm',
                  icon: 'icon_solid_Pauseorder',
                  title: this.$t('供方确认')
                },
                {
                  id: 'supplierRrturn',
                  icon: 'icon_list_recall',
                  title: this.$t('供方退回')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columnObj.detailedColumn,
            // dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmi-pickup-order/supplier-item-page-query'
              // recordsPosition: "data",
            },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    // 跳转详情
    handleClickCellTitle(e) {
      let obj = {
        id: ''
      }
      if (e.field === 'vmiOrderCode') {
        obj.id = e.data.vmiOrderId ?? e.data.id
        this.redirectPage('purchase-execute/supplier-picking-details', obj)
      }
    },
    // 头部跳转
    handleClickToolBar(e) {
      if (e.toolbar.id === 'confirm') {
        // VMI领料单-供方-批量确认
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('确认'),
            message: this.$t('确定确认吗？')
          },
          success: () => {
            // TODO: 领料-供方-确认（列表-批量确认）
            // debugger
            let ids = _selectRows.map((item) => item.id)
            // 只有在待确认的情况下才可选择
            let switAccept = true
            _selectRows.map((item) => {
              if (item.status != 1) {
                switAccept = false
              }
            })
            if (switAccept == false) {
              this.$toast({
                content: this.$t('请选择待确认状态'),
                type: 'warning'
              })
              return
            }
            this.$API.supplierCoordination.purOrderQueryBatchConfirm({ ids: ids }).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('确认成功'),
                  type: 'success'
                })
                // 刷新当前 Grid
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          }
        })
      } else if (e.toolbar.id == 'supplierRrturn') {
        // VMI领料单-供方-批量退回
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('退回'),
            message: this.$t('确定退回吗？')
          },
          success: () => {
            // TODO: 领料-供方-退回（列表-批量退回）
            // debugger
            let ids = _selectRows.map((item) => item.id)
            // 只有在待确认的情况下才可选择
            let switAccept = true
            _selectRows.map((item) => {
              if (item.status != 1) {
                switAccept = false
              }
            })
            if (switAccept == false) {
              this.$toast({
                content: this.$t('请选择待确认状态'),
                type: 'warning'
              })
              return
            }
            this.$API.supplierCoordination.purOrderQueryBatchReject({ ids: ids }).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('退回成功'),
                  type: 'success'
                })
                // 刷新当前 Grid
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          }
        })
      } else {
        // 调用刷新方法
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'confirmss') {
        this.$dialog({
          data: {
            title: this.$t('确认'),
            message: this.$t('确认确认吗？')
          },
          success: () => {
            // TODO: 领料-供方-确认（列表-确认）
            this.$API.supplierCoordination.purOrderQueryConfirm({ id: e.data.id }).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('提交成功'),
                  type: 'success'
                })
                // 刷新当前 Grid
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          }
        })
      } else if (e.tool.id == 'cancel') {
        this.$dialog({
          data: {
            title: this.$t('退回'),
            message: this.$t('确认退回吗？')
          },
          success: () => {
            // TODO: 领料-供方-退回（列表-退回）
            // debugger
            this.$API.supplierCoordination
              .purOrderQueryBatchReject({ ids: [e.data.id] })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('退回成功'),
                    type: 'success'
                  })
                  // 刷新当前 Grid
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
          }
        })
      }
    },
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
