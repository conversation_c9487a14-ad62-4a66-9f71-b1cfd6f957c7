// 领料管理，创建领料单详情页 // 供方 VMI供应商确认
<template>
  <div class="full-height pt20">
    <top-info class="flex-keep" :header-info="headerInfo" @goBack="goBack"></top-info>
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
    <Dialog ref="configListDialog" @dilogData="dilogData" :rule-form="ruleForm"></Dialog>
  </div>
</template>

<script>
import { columnData } from './config/pickingDetails.js'
import TopInfo from './components/topInfo.vue'
import Dialog from './components/dialog.vue'
export default {
  components: {
    TopInfo,
    Dialog
  },
  data() {
    return {
      headerInfo: {
        status: 1,
        createUserName: 'jcs',
        createTime: '2022-03-02',
        factory: this.$t('京东工厂'),
        supplierCode: '0001',
        companyDescribe: this.$t('描述'),
        warehouseCode: '0033333',
        warehouseDescribe: this.$t('描述'),
        remark: '',
        accept: false,
        endpoint: false
      },
      pageConfig: [
        {
          title: this.$t('汇总列表'),
          //   useToolTemplate: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'print',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('打印')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columnData,
            // lineIndex: 1,
            // autoWidthColumns: columnData.length + 1,
            dataSource: [
              {
                lineNumber: 1,
                outScience: 'W039847',
                outMaterial: this.$t('自攻特攻GB/T'),
                outGroup: this.$t('王国浩-塑料件'),
                stockStatus: this.$t('合格库存'),
                encryptionNumber: 'SN0002',
                outNumber: 500,
                enterMaterial: 'W737636',
                enterDescribe: this.$t('自攻特攻GB/TTT'),
                company: this.$t('吨'),
                rowRemarks: this.$t('备注文本描述')
              }
            ],
            // asyncConfig: {
            //   url: "/srm-purchase-execute/tenant/pe/business/configs",
            //   recordsPosition: "data",
            // },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null,
      ruleForm: {
        company: ''
      }
    }
  },

  methods: {
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    handleClickToolBar(e) {
      if (e.toolbar.id === 'print') {
        this.$refs.configListDialog.initDialog()
      } else {
        // 调用刷新方法
      }
    },
    // 接收弹窗编辑后的值
    dilogData(data) {
      this.pageConfig[0].grid.dataSource.unshift(data)
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
.titleColor {
  color: #00469c;
}
</style>
