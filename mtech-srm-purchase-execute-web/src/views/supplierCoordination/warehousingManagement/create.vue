<template>
  <div class="full-height pt20 vertical-flex-box">
    <top-info
      class="flex-keep"
      @goBack="goBack"
      @submitBtn="submitBtn"
      @preservationBtn="preservationBtn"
      ref="infoRules"
    ></top-info>
    <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
    <mt-template-page
      v-show="tabIndex === 0"
      ref="templateRef"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    >
    </mt-template-page>
    <div v-show="tabIndex === 1" :style="{ padding: '30px' }">
      <logistics-info ref="logisticsRef" :logistics-data="logisticsData"></logistics-info>
    </div>
  </div>
</template>
<script>
import { columnPopup } from './config/create.js'
import { headerInfo } from './config/variable.js'
import TopInfo from './components/topInfo.vue'
import LogisticsInfo from './components/logisticsInfo.vue'
import { cloneDeep } from 'lodash'
export default {
  components: {
    TopInfo,
    LogisticsInfo
  },
  props: {},
  data() {
    return {
      topData: '',
      headerInfo,
      postId: '',
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [
            [
              {
                id: 'Add',
                icon: 'icon_solid_Createorder',
                title: this.$t('新增')
              },
              'Delete'
              // "Edit",
              // {
              //   id: "Preservation",
              //   icon: "icon_table_save",
              //   title: this.$t("保存1"),
              // },
            ],
            []
          ],
          grid: {
            dataSource: [],
            allowEditing: true, //开启表格编辑操作
            allowPaging: false, // 不分页
            editSettings: {
              allowEditing: true, //是否允许编辑
              allowDeleting: true, //是否允许删除
              allowAdding: true, //是否允许新增
              showDeleteConfirmDialog: true, //删除是否需要确认
              newRowPosition: 'Top' //在上面新增一行还是下面新增一行
            },
            columnData: columnPopup
          }
        }
      ],
      // 物流信息数据
      logisticsData: {
        transportType: '',
        statusCreate: true,
        thirdPartyLogisticsName: null,
        transportNum: null,
        driverName: null,
        driverPhone: null,
        licensePlateNumber: null,
        count: null,
        remark: null
      },
      tabSource: [
        {
          title: this.$t('物料信息')
        },
        {
          title: this.$t('物流信息')
        }
      ],
      tabIndex: 0,
      isRequesting: false // 请求的节流
    }
  },
  watch: {
    $route(to) {
      if (to.name == 'supplier-warehousing-index') {
        this.headerInfoInit()
      }
    },
    'headInfo.vmiWarehouseType'(v) {
      // WMS管理库存不允许编辑
      let _edit = v === 2 ? false : true
      let _cloumnData = cloneDeep(columnPopup)
      _cloumnData.map((item) => {
        if (item.field === 'count') item.allowEditing = _edit
      })
      this.$set(this.componentConfig[0].grid, 'columnData', _cloumnData)
    }
  },
  // deactivated() {
  //   this.headerInfoInit();
  // },
  mounted() {
    // create === 1  编辑
    // create === 0  详情
    if (this.$route.query.create == 1) {
      headerInfo.statusCreate = false
      headerInfo.submitStatus = true
      this.logisticsData.statusCreate = false
    } else {
      this.getDetailData()
      this.componentConfig[0].toolbar = []
      headerInfo.statusCreate = true
      this.logisticsData.statusCreate = true
    }
    // 新建状态下可修改
    if (this.$route.query.status == 0) this.getDetailData()
  },
  // watch: {
  //   "headerInfo.identificationStatus": function (newVal) {
  //     console.log("获取到了类型", newVal);
  //     if (newVal == "1") {
  //       this.componentConfig[0].grid.columnData = columnSelect;
  //     } else {
  //       this.componentConfig[0].grid.columnData = columnPopup;
  //     }
  //   },
  // },
  methods: {
    headerInfoInit() {
      headerInfo.status = ''
      headerInfo.createUserName = ''
      headerInfo.createTime = ''
      headerInfo.submitTime = ''
      headerInfo.siteCode = ''
      headerInfo.siteName = ''
      headerInfo.supplierCode = ''
      headerInfo.supplierName = ''
      headerInfo.vmiWarehouseAddress = ''
      headerInfo.vmiWarehouseCode = ''
      headerInfo.vmiWarehouseName = ''
      headerInfo.vmiWarehouseType = null
      headerInfo.remark = ''
      headerInfo.submitStatus = true
      headerInfo.statusCreate = true //判断是不是编辑页面 true不可编辑   false可编辑
      headerInfo.identificationStatus = null
      headerInfo.customerEnterpriseId = ''
      headerInfo.vmiOrderCode = ''
      headerInfo.hasDetailRow = false
    },
    // 切换
    handleSelectTab(e) {
      this.tabIndex = e
      // 切换时结束编辑
      this.$refs.templateRef.getCurrentUsefulRef().gridRef?.ejsRef.endEdit()
    },
    actionBegin(args) {
      if (
        this.$route.query.create == 0 &&
        args.rowIndex === args.rowIndex &&
        args.rowIndex !== undefined
      ) {
        args.cancel = true //禁止行编辑
      }
    },
    actionComplete(args) {
      console.log('actionComplete', args)
      if (args.requestType == 'delete') {
        if (
          this.$refs.templateRef.getCurrentUsefulRef()?.ejsRef?.getCurrentViewRecords()?.length ===
          0
        ) {
          headerInfo.hasDetailRow = false // 使头部数据可改变，因为通过头部数据获取物料，表格没有数据时，头部数据可修改
        }
      } else if (args.requestType == 'save') {
        let afterList =
          this.$refs.templateRef.getCurrentUsefulRef().gridRef?.ejsRef.getCurrentViewRecords() || []
        this.componentConfig[0].grid.dataSource = afterList
      }
    },
    // 清空表格数据
    emptyTableData() {
      this.componentConfig[0].grid.dataSource.length = 0
    },
    // 表格头部操作
    handleClickToolBar(e) {
      // if (e.toolbar.id === "Preservation") {
      //   // 结束编辑
      //   this.$refs.templateRef.getCurrentUsefulRef().gridRef?.ejsRef.endEdit();
      //   this.componentConfig[0].grid.dataSource = afterList;
      // } else
      if (e.toolbar.id === 'Add') {
        if (!this.$refs.infoRules.checkForm()) return

        // vmi仓，goodsReceiptType === 1选择采购订单号，并且调用/tenant/vmi-receive-order/receive-limit-query拿出  VMI可送货数量
        // goodsReceiptType 不等于1的时候  选择采购订单号不可以选择    订单数量，VMI可送货数量不展示 物料编码弹窗选择

        // this.componentConfig[0].grid.dataSource = afterList
        this.$refs.templateRef.getCurrentUsefulRef().gridRef?.ejsRef.addRecord()

        headerInfo.hasDetailRow = true
        // this.componentConfig[0].grid.dataSource.push(obj)
      } else if (e.toolbar.id === 'Delete' && e.grid.getSelectedRecords().length > 0) {
        this.$refs.templateRef.getCurrentUsefulRef().gridRef?.ejsRef.deleteRecord()
        // this.componentConfig[0].grid.dataSource = afterList
        // // 删除   找出与原数组的相同的，删除删掉
        // e.grid.getSelectedRecords().forEach(item =>{
        //   this.componentConfig[0].grid.dataSource.forEach((ele,index)=>{
        //     if(item.rowNmber === ele.rowNmber) this.componentConfig[0].grid.dataSource.splice(index,1)
        //   })
        // })
      } else if (e.toolbar.id === 'refreshDataByLocal') {
        // 调用刷新
      } else if (e.grid.getSelectedRecords().length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    // 提交
    submitBtn() {
      //获取编辑后的整体数据
      // console.log(
      //   this.$refs.templateRef
      //     .getCurrentUsefulRef()
      //     .gridRef?.ejsRef.getCurrentViewRecords(),
      //   "-=-=-="
      // );
      this.createInterface(2)
    },
    //保存
    preservationBtn() {
      this.createInterface(1)
    },
    // 获取详情接口
    getDetailData() {
      let obj = {
        id: this.$route.query.id
      }
      this.$API.purchaseCoordination.postBuyerWarehousingDetail(obj).then((res) => {
        if (res.code === 200) {
          if (res?.data) {
            const data = res?.data
            headerInfo.vmiOrderCode = data.vmiOrderCode
            headerInfo.status = data.status
            headerInfo.createUserName = data.createUserName
            headerInfo.createTime = data.createTime
            headerInfo.submitTime = data.submitTime
            headerInfo.siteCode = data.siteCode
            headerInfo.siteName = data.siteName
            headerInfo.supplierCode = data.supplierCode
            headerInfo.supplierName = data.supplierName
            headerInfo.vmiWarehouseAddress = data.vmiWarehouseAddress
            headerInfo.vmiWarehouseCode = data.vmiWarehouseCode
            headerInfo.vmiWarehouseName = data.vmiWarehouseName
            headerInfo.vmiWarehouseType = data.vmiWarehouseType
            headerInfo.submitStatus = data.submitStatus
            headerInfo.statusCreate = data.statusCreate
            headerInfo.identificationStatus = data.identificationStatus
            this.postId = data.id
          }
          this.logisticsData =
            res.data.orderLogistic === null
              ? {
                  transportType: '',
                  statusCreate: true,
                  thirdPartyLogisticsName: null,
                  transportNum: null,
                  driverName: null,
                  driverPhone: null,
                  licensePlateNumber: null,
                  count: null,
                  remark: null
                }
              : res.data.orderLogistic[0]
          // this.$set(this.componentConfig[0].grid,'dataSource',res.data.vmiOrderItemResponses)
          this.componentConfig[0].grid.dataSource = res.data.vmiOrderItemResponses
          this.componentConfig[0].grid.dataSource.forEach((item) => {
            item.buyerOrgName = item.purchaseGroupName
            item.buyerOrgCode = item.purchaseGroupCode
            item.unitName = item.itemUnitDescription
            item.unitCode = item.itemUnit
            item.quantity = item.count
            item.buyerOrder = item.orderCode
            item.itemNo = item.lineNo
          })
          if (this.$route.query.status != 0) {
            headerInfo.statusCreate = true
            this.logisticsData.statusCreate = true
          } else {
            // status == 0 新建状态下可修改
            headerInfo.submitStatus = true
          }
          headerInfo.vmiWarehouseCode = res.data.vmiWarehouseCode
        }
      })
    },
    // 创建接口
    createInterface(btnStatus) {
      // 校验  物流信息没有填写完整 就return
      if (!this.$refs.logisticsRef.checkForm()) {
        this.$toast({ content: this.$t('物流信息没有填完整'), type: 'error' })
        return
      }
      if (this.isRequesting) return

      let dataList = this.$refs.templateRef
        .getCurrentUsefulRef()
        .gridRef?.ejsRef.getCurrentViewRecords()
      let data = {
        itemList: [],
        logisticsList: [],
        operationType: btnStatus,
        remark: headerInfo.remark,
        siteCode: headerInfo.siteCode,
        supplierCode: headerInfo.supplierCode,
        vmiWarehouseAddress: headerInfo.vmiWarehouseAddress,
        vmiWarehouseCode: headerInfo.vmiWarehouseCode
      }
      if (this.postId) {
        data.id = this.postId
      }
      dataList.forEach((item) => {
        item.lineNo = item.itemNo
        item.orderCode = item.buyerOrder
        item.itemUnit = item.unitCode
        // item.countLimit = item.countLimit;
        // item.count = item.count;
        item.purchaseGroupCode = item.buyerOrgCode
        item.purchaseGroupName = item.buyerOrgName
      })
      data.itemList = dataList
      data.logisticsList.push(this.logisticsData)
      if (data.logisticsList[0].transportType === '') {
        data.logisticsList = []
      }
      this.isRequesting = true
      this.$API.supplierCoordination
        .postBuyerWarehousingCreate(data)
        .then((res) => {
          this.isRequesting = false
          if (res.code === 200) {
            this.$toast({ content: this.$t('创建成功'), type: 'success' })
            this.$router.go(-1)
          } else {
            this.$toast({
              content: this.$t('创建失败'),
              type: 'console.error();'
            })
          }
        })
        .catch(() => {
          this.isRequesting = false
        })
    }
  }
}
</script>

<style scoped lang="scss"></style>
