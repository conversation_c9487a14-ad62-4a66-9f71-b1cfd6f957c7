<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="$t('输入订单号模糊查询')"
      :allow-filtering="true"
      :filtering="purOrderQueryOrder"
      @change="selectChange"
      :disabled="disabledStatus"
    >
    </mt-select>
  </div>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      dataSource: [],
      fields: { text: 'label', value: 'value' },
      disabledStatus: false
    }
  },
  mounted() {
    if (this.$route.query.create === 'false') this.disabledStatus = true
    this.purOrderQueryOrder({ value: this.data[this.data.column.field] })
  },
  methods: {
    purOrderQueryOrder(value) {
      let params = {
        condition: 'and',
        page: {
          current: 1,
          size: 50
        },
        defaultRules: [
          {
            label: this.$t('采购订单号'),
            field: 'orderCode',
            type: 'string',
            operator: 'contains',
            value: (value && value.text) || ''
          }
        ]
      }
      this.$API.supplierCoordination.purOrderQueryOrder(params).then((res) => {
        let orderOptions = res.data.records
          .map((item) => ({
            label: item,
            value: item
          }))
          .filter((item) => item.value) //过滤一下空值
        this.dataSource = orderOptions
      })
    },
    selectChange(value) {
      const code = value?.itemData?.value
      if (code) {
        // 请求采购订单行
        this.$API.vmi.getByOrder({ code }).then((res) => {
          const orderLineData = res.data.reduce((pre, now) => {
            pre.push({
              label: now.itemNo,
              value: String(now.itemNo),
              ...now
            })
            return pre
          }, [])
          this.$bus.$emit('getOrderLineData', orderLineData)
        })
      }
    }
  }
}
</script>

<style scoped lang="scss"></style>
