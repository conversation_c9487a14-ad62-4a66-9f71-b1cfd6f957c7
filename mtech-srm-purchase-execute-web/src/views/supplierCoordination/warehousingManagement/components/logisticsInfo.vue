<template>
  <div class="box">
    <!-- 下面的内容 -->
    <div class="main-box">
      <mt-form
        ref="ruleForm"
        :model="logisticsData"
        :rules="rules"
        :validate-on-rule-change="false"
      >
        <!-- 发货方式 -->
        <mt-form-item style="width: 100%" prop="type" :label="$t('请选择发货方式:')">
          <br />
          <mt-radio
            v-model.number="logisticsData.transportType"
            :data-source="ShippingTypeOptions"
            :disabled="logisticsData.statusCreate"
            @change="handleChangeTransportType"
          ></mt-radio>
        </mt-form-item>
        <br />
        <!-- 快递-物流公司 -->
        <mt-form-item
          v-if="logisticsData.transportType == ShippingType.express"
          :label="$t('物流公司')"
          prop="thirdPartyLogisticsName"
        >
          <mt-select
            :data-source="companySelect"
            :disabled="logisticsData.statusCreate"
            v-model="logisticsData.thirdPartyLogisticsName"
            :placeholder="$t('物流公司')"
          ></mt-select>
        </mt-form-item>
        <!-- 快递-物流单号 -->
        <mt-form-item
          v-if="logisticsData.transportType == ShippingType.express"
          :label="$t('物流单号')"
          prop="transportNum"
        >
          <mt-input
            :disabled="logisticsData.statusCreate"
            v-model="logisticsData.transportNum"
            :placeholder="$t('物流单号')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-司机名称 -->
        <mt-form-item
          v-if="logisticsData.transportType == ShippingType.logistics"
          prop="driverName"
          :label="$t('司机名称')"
        >
          <mt-input
            :disabled="logisticsData.statusCreate"
            v-model="logisticsData.driverName"
            :placeholder="$t('司机名称')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-司机联系方式 -->
        <mt-form-item
          v-if="logisticsData.transportType == ShippingType.logistics"
          :label="$t('司机联系方式')"
          prop="driverPhone"
        >
          <mt-input
            :disabled="logisticsData.statusCreate"
            v-model="logisticsData.driverPhone"
            :placeholder="$t('司机联系方式')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-车牌 -->
        <mt-form-item
          v-if="logisticsData.transportType == ShippingType.logistics"
          :label="$t('车牌')"
          prop="licensePlateNumber"
        >
          <mt-input
            :disabled="logisticsData.statusCreate"
            v-model="logisticsData.licensePlateNumber"
            :placeholder="$t('车牌')"
          ></mt-input>
        </mt-form-item>
        <!-- 件数 -->
        <mt-form-item
          :label="$t('件数')"
          prop="count"
          v-if="logisticsData.transportType == 1 || logisticsData.transportType == 2"
        >
          <mt-input-number
            :disabled="logisticsData.statusCreate"
            v-model="logisticsData.count"
            :placeholder="$t('件数')"
          ></mt-input-number>
        </mt-form-item>

        <!-- 备注 -->
        <mt-form-item
          class="full-width"
          :label="$t('备注')"
          v-if="logisticsData.transportType == 1 || logisticsData.transportType == 2"
          :show-message="false"
        >
          <mt-input
            :disabled="logisticsData.statusCreate"
            v-model="logisticsData.remark"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { ShippingTypeOptions, ShippingType } from '../config/constant'
import { RegExpMap } from '@/utils/constant'

export default {
  props: {
    logisticsData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const transportNumValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入物流公司')))
      } else if (!RegExpMap.englishAndNumReg.test(value)) {
        callback(new Error(this.$t('只可输入字母/数字')))
      } else {
        callback()
      }
    }
    return {
      ShippingTypeOptions,
      ShippingType,
      isExpand: true,
      rules: {
        thirdPartyLogisticsName: [
          {
            required: true,
            message: this.$t('请选择物流公司'),
            trigger: 'blur'
          }
        ],
        transportNum: [
          {
            required: true,
            validator: transportNumValidator,
            trigger: 'blur'
          }
        ],
        driverName: [
          {
            required: true,
            message: this.$t('请输入司机名称'),
            trigger: 'blur'
          }
        ],
        driverPhone: [
          {
            required: true,
            message: this.$t('请输入司机联系方式'),
            trigger: 'blur'
          }
        ],
        licensePlateNumber: [{ required: true, message: this.$t('请输入车牌'), trigger: 'blur' }],
        count: [{ required: true, message: this.$t('请输入件数'), trigger: 'blur' }]
      },
      companySelect: []
    }
  },
  mounted() {
    this.getCompany()
  },
  filters: {},
  methods: {
    getCompany() {
      this.$API.supplierCoordination
        .postLogisticsCompany({ dictCode: 'logisticsCompany' })
        .then((res) => {
          if (res.data && res.data.length) {
            this.companySelect = res.data.map((item) => {
              return {
                text: item.itemName,
                value: item.itemCode,
                id: item.id
              }
            })
          }
        })
    },

    // 改变发货方式
    handleChangeTransportType() {
      this.$refs.ruleForm.resetFields()
    },

    /**
     * 校验逻辑：
     * 1、选择了快递配送时，若选择了物流公司，物流信息整单必填；没有选物流公司默认，物流信息整单非必填；
     * 2、选择了物流配送时，物流信息整单必填
     */
    checkForm() {
      let validStatus = false
      if (
        this.logisticsData.transportType == ShippingType.express &&
        this.logisticsData.thirdPartyLogisticsName
      ) {
        this.$refs.ruleForm.validate((valid) => {
          validStatus = valid
        })
      } else if (this.logisticsData.transportType == ShippingType.logistics) {
        this.$refs.ruleForm.validate((valid) => {
          validStatus = valid
        })
      } else {
        validStatus = true
      }

      return validStatus
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  padding: 20px 0 20px 20px;

  .main-box .mt-form-item {
    width: calc(20% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
  }
}
</style>
