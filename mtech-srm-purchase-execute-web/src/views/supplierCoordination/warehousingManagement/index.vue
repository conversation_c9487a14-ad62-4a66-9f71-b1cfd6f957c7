// VMI入库管理
<template>
  <div class="full-height pt20">
    <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      v-show="tabIndex === 0"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      v-show="tabIndex === 1"
      :template-config="pageConfig1"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { columnObj } from './config/index.js'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    // addDialog: require("./components/addDialog.vue").default,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  data() {
    return {
      downTemplateParams: {
        flag: 0
      }, // 下载模板参数
      tabSource: [
        {
          title: this.$t('头视图')
        },
        {
          title: this.$t('明细视图')
        }
      ],
      requestUrls: {
        templateUrlPre: 'supplierCoordination',
        templateUrl: 'vmiExportExcel',
        uploadUrl: 'vmiImportExcel'
      }, // 上传下载接口地址
      tabIndex: 0,
      pageConfig: [
        {
          tab: { title: this.$t('头视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'add',
                  icon: 'icon_solid_Createorder',
                  permission: ['O_02_1092'],
                  title: this.$t('创建VMI入库单')
                },
                {
                  id: 'synchronous',
                  icon: 'icon_table_restart',
                  title: this.$t('同步WMS')
                },
                {
                  id: 'AppointmentDelivery1',
                  icon: 'icon_table_AppointmentDelivery1',
                  title: this.$t('预约送货')
                },
                {
                  id: 'export1',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                },
                {
                  id: 'print',
                  icon: 'icon_table_print',
                  title: this.$t('打印')
                  // permission: ["O_02_1046"],
                },
                {
                  id: 'delete',
                  icon: 'icon_solid_Delete',
                  permission: ['O_02_1093'],
                  title: this.$t('删除')
                },
                {
                  id: 'upload',
                  icon: 'icon_solid_Import',
                  title: this.$t('导入')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: 'ed7b2449-24f7-498d-b7c3-f4f3b1d71e00',
          grid: {
            columnData: columnObj.headColumn,
            dataSource: [
              {
                vmiOrderCode: 'w20211227001',
                status: 1,
                factoryCode: '630847',
                factoryDescribe: this.$t('TCL空调器有限公司生产工厂'),
                warehouseCode: '0100001',
                warehouseDescribe: this.$t('VMI红物流芜湖威灵电机'),
                supplierCode: 'G 89001',
                materialDescribe: this.$t('广东惠利普智能科技股份有限公司'),
                goodsAddress: this.$t('深圳市福田区下梅林梅华路'),
                createTime: '2021-12-27 13:50：33',
                receiveTime: '2021-12-30'
              }
            ],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmi-receive-order/supplier-page-query',
              recordsPosition: 'data.records',
              serializeList: this.serialize
            },
            frozenColumns: 1
          }
        }
      ],
      pageConfig1: [
        {
          tab: { title: this.$t('明细视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'export2',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: '5e899ef7-7f8e-488b-a606-db779afec972',
          grid: {
            columnData: columnObj.detailedColumn,
            dataSource: [
              {
                warehousingCode: 'w20211227001',
                status: 1,
                rowNumber: 10,
                materialCode: '630847',
                materialName: this.$t('自攻特攻GB/T'),
                procurementGroup: this.$t('采购组01'),
                goodsNumber: 500,
                receivingNumber: 100,
                company: this.$t('件'),
                remarks: this.$t('备注文本字段'),
                qualityResult: this.$t('不合格'),
                unqualifiedClassification: this.$t('不良价值'),
                qualityRemarks: this.$t('质检备注内容'),
                relationCode: 'D848940',
                relationRowCode: 10,
                factoryCode: '630847',
                factoryDescribe: this.$t('TCL空调器有限公司生产工厂'),
                warehouseCode: '0100001',
                warehouseDescribe: this.$t('VMI红物流芜湖威灵电机'),
                supplierCode: 'G 89001',
                materialDescribe: this.$t('广东惠利普智能科技股份有限公司'),
                goodsAddress: this.$t('深圳市福田区下梅林梅华路'),
                createTime: '2021-12-27 13:50：33',
                receiveTime: '2021-12-30'
              }
            ],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmi-receive-order/supplier-item-page-query',
              recordsPosition: 'data.records',
              serializeList: this.serialize
            },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    serialize(data) {
      data.forEach((item) => {
        item.submitTime = Number(item.submitTime)
        item.confirmTime = Number(item.confirmTime)
      })
      return data
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    // create === 1  编辑
    // create === 0  详情
    // 跳转详情
    handleClickCellTitle(e) {
      if (e.field === 'vmiOrderCode' && e.tabIndex === 0) {
        let obj = {
          create: 0,
          id: e.data.id
        }
        // e.data.status === 0 新建状态可修改 传值status
        if (e.data.status === 0) obj.create = 1
        obj.status = e.data.status
        this.redirectPage('purchase-execute/supplier-warehousing-create', obj)
      }
    },
    // 头部操作
    handleClickToolBar(e) {
      const _records = e.grid.getSelectedRecords()
      const _status = []
      const _forecastDelivery = []
      const _id = []
      const _codes = []
      e.gridRef.getMtechGridRecords().map((item) => {
        _id.push(item.id)
        _codes.push(item.companyCode)
        _status.push(item.status)
        _forecastDelivery.push(item.forecastDelivery)
      })
      if (e.toolbar.id === 'export1') {
        let obj = JSON.parse(
          sessionStorage.getItem('ed7b2449-24f7-498d-b7c3-f4f3b1d71e00')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnObj.headColumn.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let params = {
          page: { current: 1, size: 1000 },
          rules: rule.rules || []
        }
        this.$store.commit('startLoading')
        this.$API.supplierCoordination.postSupplierOrderPrintNew(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
      if (e.toolbar.id === 'export2') {
        let obj = JSON.parse(
          sessionStorage.getItem('5e899ef7-7f8e-488b-a606-db779afec972')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnObj.detailedColumn.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let params = {
          page: { current: 1, size: 1000 },
          rules: rule.rules || []
        }
        this.$store.commit('startLoading')
        this.$API.supplierCoordination.postSupplierItemOrderPrintNew(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
      if (e.toolbar.id == 'upload') {
        this.showUploadExcel(true)
      } else if (e.toolbar.id == 'synchronous') {
        if (_records.length <= 0 || _records.length > 1) {
          this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
          return
        }
        this.synchronousWms(_records[0])
      } else if (e.toolbar.id === 'AppointmentDelivery1') {
        if (_records.length <= 0) {
          this.$toast({ content: this.$t('请选择数据'), type: 'warning' })
          return
        }
        for (let i of _status) {
          if (i === 8 || i === 9) {
            this.$toast({
              content: this.$t('已完成或已取消状态不可预约'),
              type: 'warning'
            })
            return
          }
        }
        // 预约送货
        const deliveryCodeList = []
        // let deliveryStatusList = []; 入库单没有交货方式
        _records.forEach((item) => {
          // 单号
          deliveryCodeList.push(item.vmiOrderCode)
        })
        const purTenantId = _records[0].buyerTenantId // 采方租户id

        // 存 localStorage 预约送货 页面读
        localStorage.setItem(
          'toReservationDeliverData',
          JSON.stringify({
            selectedRowData: _records,
            deliveryCodeList,
            purTenantId // 采方租户id
          })
        )
        // 预约送货跳转
        this.$router.push({
          name: 'reservation-deliver-supplier',
          query: {
            type: 'new'
          },
          params: {
            sourceType: 1 //1:入库单 0:送货单
          }
        })
        return
      } else if (e.toolbar.id === 'add') {
        let obj = {
          create: 1
        }
        // 跳转到新建
        this.redirectPage('purchase-execute/supplier-warehousing-create', obj)
      } else if (e.toolbar.id === 'delete' && e.grid.getSelectedRecords().length > 0) {
        // 删除弹窗
        this.$dialog({
          data: {
            title: this.$t('删除'),
            message: this.$t('确认删除吗？')
          },
          success: () => {
            this.delete(e.grid.getSelectedRecords())
          }
        })
      } else if (e.toolbar.id === 'print' && e.grid.getSelectedRecords().length > 0) {
        let obj = {
          idList: []
        }
        e.grid.getSelectedRecords().forEach((item) => {
          obj.idList.push(item.id)
        })
        this.$API.supplierCoordination.postOrderPrintHtml(obj).then((res) => {
          // if (res?.data?.type === "application/json") {
          //   const reader = new FileReader();
          //   reader.readAsText(res?.data, "utf-8");
          //   reader.onload = function () {
          //     console.log("======", reader);
          //     const readerRes = reader.result;
          //     const resObj = JSON.parse(readerRes);
          //     Vue.prototype.$toast({
          //       content: resObj.msg,
          //       type: "error",
          //     });
          //   };

          //   return;
          // }
          const content = res.data
          let pdfUrl = window.URL.createObjectURL(
            new Blob([content], { type: 'text/html;charset=utf-8' })
          )

          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
      } else if (e.toolbar.id === 'refreshDataByLocal') {
        // 调用刷新
        debugger
      } else if (e.grid.getSelectedRecords().length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    handleClickCellTool(e) {
      let idOBj = {
        id: e.data.id
      }
      // 提交
      if (e.tool.id === 'submit') {
        // 参数值需要修改
        this.$dialog({
          data: {
            title: this.$t('提交'),
            message: this.$t('确认提交吗？')
          },
          success: () => {
            this.submit(idOBj)
          }
        })
      } else if (e.tool.id === 'cancel') {
        //取消
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消吗？')
          },
          success: () => {
            this.cancel(idOBj)
          }
        })
      }
    },
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },
    // 删除
    delete(data) {
      let obj = {
        ids: data.map((item) => item.id)
      }
      this.$API.supplierCoordination.postBuyerWarehousingDelete(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({
            content: this.$t('删除失败'),
            type: 'console.error();'
          })
        }
      })
    },
    // 同步WMS
    synchronousWms(data) {
      if (data.wmsSyncStatus === 1) {
        this.$toast({
          content: this.$t('入库单已成功同步WMS，无需重复同步'),
          type: 'warning'
        })
        return
      }
      this.$API.purchaseCoordination.synchronousWms({ id: data.id }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('同步成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('同步失败'), type: 'warning' })
        }
      })
    },
    // 提交 用的与创建同一个接口
    submit(obj) {
      this.$API.supplierCoordination.postBuyerWarehousingSubmit(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({
            content: this.$t('提交失败'),
            type: 'console.error();'
          })
        }
      })
    },
    // 取消
    cancel(obj) {
      this.$API.supplierCoordination.postBuyerWarehousingCancel(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('取消成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({
            content: this.$t('取消失败'),
            type: 'console.error();'
          })
        }
      })
    },
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$toast({ content: this.$t('导入成功'), type: 'success' })

      this.refreshColumns()
    },
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = [] // 清空数据
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
