import { i18n } from '@/main.js'
import { timeNumberToDate } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName } from '@/utils/utils'
import Vue from 'vue'

export const columnObj = {
  headColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '200',
      field: 'vmiOrderCode',
      headerText: i18n.t('VMI入库单号'),
      cellTools: []
    },
    {
      width: '80',
      field: 'status',
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map',
        map: [
          { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
          { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
          { value: 2, text: i18n.t('已接收'), cssClass: 'col-active' },
          { value: 8, text: i18n.t('已完成'), cssClass: 'col-active' },
          { value: 9, text: i18n.t('已取消'), cssClass: 'col-active' }
        ]
      },
      searchOptions: {
        elementType: 'multi-select',
        operator: 'in',
        multiple: true,
        dataSource: [
          { label: i18n.t('新建'), value: 0 },
          { label: i18n.t('待确认'), value: 1 },
          { label: i18n.t('已接收'), value: 2 },
          { label: i18n.t('已完成'), value: 8 },
          { label: i18n.t('已取消'), value: 9 }
        ],
        fields: { text: 'label', value: 'value' }
      },
      cellTools: [
        {
          id: 'submit',
          icon: 'icon_list_enable',
          title: i18n.t('提交'),
          // permission: ["O_02_0125"],
          visibleCondition: (data) => data['status'] == 0
        },
        {
          id: 'cancel',
          icon: 'icon_list_disable',
          title: i18n.t('取消'),
          // permission: ["O_02_0126"],
          visibleCondition: (data) => data['status'] == 1
        }
      ]
    },
    {
      width: '120',
      field: 'onWayStatus',
      headerText: i18n.t('在途状态'),
      allowEditing: false,
      searchOptions: {
        elementType: 'multi-select',
        showSelectAll: true,
        operator: 'in'
      },
      cssClass: '',
      valueConverter: {
        type: 'map',
        map: [
          { text: i18n.t('未出发'), value: 0, cssClass: 'col-active' },
          { text: i18n.t('已入园'), value: 1, cssClass: 'col-active' },
          { text: i18n.t('已出发'), value: 2, cssClass: 'col-active' },
          { text: i18n.t('已报到'), value: 3, cssClass: 'col-active' },
          { text: i18n.t('已取消'), value: 4, cssClass: 'col-active' },
          { text: i18n.t('已关闭'), value: 5, cssClass: 'col-active' }
        ]
      }
    },
    {
      field: 'vehicleLogistics', // 车辆物流
      headerText: i18n.t('车辆物流'),
      template: () => {
        return {
          template: Vue.component('template-detail', {
            template: `
                <span v-if="data.carNo" style="color: #6386c1; cursor: pointer;" @click="toLogistics(data)">{{ $t('查看物流') }}</span>`,
            methods: {
              toLogistics(e) {
                const params = {
                  ztpno: e?.deliveryCode?.toString(),
                  busCode: e?.forecastCode,
                  busNum: e.carNo
                }
                this.$API.receiptAndDelivery.purQueryVehicleLogistics(params).then((res) => {
                  if (res.code === 200) {
                    window.open(res.data.mapURL)
                  }
                })
              }
            }
          })
        }
      }
    },
    {
      field: 'carNo', // 车牌号
      headerText: i18n.t('车牌号')
    },
    {
      width: '150',
      field: 'wmsSyncStatus',
      headerText: i18n.t('WMS同步状态'),
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('未同步'),
          1: i18n.t('同步成功'),
          2: i18n.t('同步失败')
        }
      }
    },
    {
      width: '150',
      field: 'wmsSyncInfo',
      headerText: i18n.t('WMS同步信息'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'vmiWarehouseType',
      headerText: i18n.t('VMI仓类型'),
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('SRM管理库存'),
          1: i18n.t('原厂'),
          2: i18n.t('WMS管理库存')
        }
      }
    },
    {
      width: '260',
      field: 'siteCode',
      headerText: i18n.t('工厂'),
      searchOptions: MasterDataSelect.factorySupplierAddress,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.siteCode, data?.siteName)
      }
    },
    {
      width: '0',
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'vmiWarehouseCode',
      headerText: i18n.t('VMI仓'),
      searchOptions: MasterDataSelect.vmiWarehouseSupplier,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.vmiWarehouseCode, data?.vmiWarehouseName)
      }
    },
    {
      width: '0',
      field: 'vmiWarehouseName',
      headerText: i18n.t('VMI仓名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'supplierCode',
      headerText: i18n.t('供应商'),
      searchOptions: MasterDataSelect.supplierSu,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
      }
    },
    {
      width: '0',
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      ignore: true
    },
    {
      width: '170',
      field: 'vmiWarehouseAddress',
      headerText: i18n.t('送货地址')
    },
    {
      field: 'driverName', // 司机姓名
      headerText: i18n.t('司机姓名')
    },
    {
      field: 'driverIdNo', // 司机身份证号
      headerText: i18n.t('司机身份证号')
    },
    {
      field: 'driverPhone', // 司机手机号
      headerText: i18n.t('司机手机号')
    },
    {
      width: '80',
      field: 'createUserName',
      headerText: i18n.t('制单人')
    },
    {
      width: '150',
      field: 'createTime',
      headerText: i18n.t('制单时间'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      }
    },
    {
      width: '150',
      field: 'confirmTime',
      headerText: i18n.t('接收日期'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      },
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e && !isNaN(e) && `${e}`.length == 13) {
            e = Number(e)
            return timeNumberToDate({
              formatString: 'YYYY-mm-dd HH:MM:SS',
              value: e
            })
          } else {
            return '-'
          }
        }
      }
    }
  ],
  detailedColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '200',
      field: 'vmiOrderCode',
      headerText: i18n.t('VMI入库单号')
    },
    {
      width: '80',
      field: 'status',
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map',
        map: [
          { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
          { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
          { value: 2, text: i18n.t('已接收'), cssClass: 'col-active' },
          { value: 8, text: i18n.t('已完成'), cssClass: 'col-active' },
          { value: 9, text: i18n.t('已取消'), cssClass: 'col-active' }
        ]
      },
      searchOptions: {
        elementType: 'multi-select',
        operator: 'in',
        multiple: true,
        dataSource: [
          { label: i18n.t('新建'), value: 0 },
          { label: i18n.t('待确认'), value: 1 },
          { label: i18n.t('已接收'), value: 2 },
          { label: i18n.t('已完成'), value: 8 },
          { label: i18n.t('已取消'), value: 9 }
        ],
        fields: { text: 'label', value: 'value' }
      }
    },
    {
      width: '150',
      field: 'carNo',
      headerText: i18n.t('车号')
    },
    {
      field: 'vehicleLogistics', // 车辆物流
      headerText: i18n.t('车辆物流'),
      template: () => {
        return {
          template: Vue.component('template-detail', {
            template: `
                <span v-if="data.carNo" style="color: #6386c1; cursor: pointer;" @click="toLogistics(data)">{{ $t('查看物流') }}</span>`,
            methods: {
              toLogistics(e) {
                const params = {
                  ztpno: e?.deliveryCode?.toString(),
                  busCode: e?.forecastCode,
                  busNum: e.carNo
                }
                this.$API.receiptAndDelivery.purQueryVehicleLogistics(params).then((res) => {
                  if (res.code === 200) {
                    window.open(res.data.mapURL)
                  }
                })
              }
            }
          })
        }
      }
    },
    {
      width: '120',
      field: 'onWayStatus',
      headerText: i18n.t('在途状态'),
      allowEditing: false,
      searchOptions: {
        elementType: 'multi-select',
        showSelectAll: true,
        operator: 'in'
      },
      cssClass: '',
      valueConverter: {
        type: 'map',
        map: [
          { text: i18n.t('未出发'), value: 0, cssClass: 'col-active' },
          { text: i18n.t('已入园'), value: 1, cssClass: 'col-active' },
          { text: i18n.t('已出发'), value: 2, cssClass: 'col-active' },
          { text: i18n.t('已报到'), value: 3, cssClass: 'col-active' },
          { text: i18n.t('已取消'), value: 4, cssClass: 'col-active' },
          { text: i18n.t('已关闭'), value: 5, cssClass: 'col-active' }
        ]
      }
    },
    {
      width: '70',
      field: 'rowNum',
      headerText: i18n.t('行号')
    },
    {
      width: '130',
      field: 'itemCode', //supplierCode
      headerText: i18n.t('物料编码')
    },
    {
      width: '130',
      field: 'itemName',
      headerText: i18n.t('物料名称')
    },
    {
      width: '85',
      field: 'purchaseGroupCode',
      headerText: i18n.t('采购组'),
      searchOptions: {
        ...MasterDataSelect.businessCodeName
      },
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.purchaseGroupCode, data?.purchaseGroupName)
      }
    },
    {
      width: '0',
      field: 'purchaseGroupName',
      headerText: i18n.t('采购组名称'),
      ignore: true
    },
    {
      width: '150',
      field: 'count',
      headerText: i18n.t('送货数量')
    },
    {
      width: '150',
      field: 'checkCount',
      headerText: i18n.t('收货数量')
    },
    {
      width: '65',
      field: 'itemUnitDescription',
      headerText: i18n.t('单位')
    },
    {
      width: '65',
      field: 'remarks',
      headerText: i18n.t('备注')
    },
    {
      width: '100',
      field: 'qcReport',
      headerText: i18n.t('质检结果'),
      valueConverter: {
        type: 'map',
        map: [
          { value: 0, text: i18n.t('不合格'), cssClass: '' },
          { value: 1, text: i18n.t('合格'), cssClass: '' },
          { value: null, text: '', cssClass: '' }
        ]
      }
    },
    {
      width: '110',
      field: 'unqualifiedTypes',
      headerText: i18n.t('不合格分类'),
      valueConverter: {
        type: 'map',
        map: [
          {
            text: i18n.t('包装不符'),
            value: '0',
            cssClass: ''
          },
          {
            text: i18n.t('其他不良'),
            value: '1',
            cssClass: ''
          },
          {
            text: i18n.t('外观不符'),
            value: '2',
            cssClass: ''
          },
          {
            text: i18n.t('内容不符'),
            value: '3',
            cssClass: ''
          },
          {
            text: i18n.t('材质不符'),
            value: '4',
            cssClass: ''
          },
          {
            text: i18n.t('标志不符'),
            value: '5',
            cssClass: ''
          },
          {
            text: i18n.t('尺寸不符'),
            value: '6',
            cssClass: ''
          },
          {
            text: i18n.t('认知不符'),
            value: '7',
            cssClass: ''
          },
          {
            text: i18n.t('性能不符'),
            value: '8',
            cssClass: ''
          },
          {
            text: i18n.t('错混料'),
            value: '9',
            cssClass: ''
          },
          {
            text: '',
            value: null,
            cssClass: ''
          }
        ]
      }
    },
    // {
    //   width: "140",
    //   field: "orderCode",
    //   headerText: i18n.t("关联采购订单号"),
    // },
    // {
    //   width: "150",
    //   field: "lineNo",
    //   headerText: i18n.t("关联采购订单行号"),
    // },
    {
      width: '260',
      field: 'siteCode',
      headerText: i18n.t('工厂'),
      searchOptions: MasterDataSelect.factorySupplierAddress,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.siteCode, data?.siteName)
      }
    },
    {
      width: '0',
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'vmiWarehouseCode',
      headerText: i18n.t('VMI仓'),
      searchOptions: MasterDataSelect.vmiWarehouseSupplier,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.vmiWarehouseCode, data?.vmiWarehouseName)
      }
    },
    {
      width: '0',
      field: 'vmiWarehouseName',
      headerText: i18n.t('VMI仓名称'),
      ignore: true
    },
    // {
    //   width: "120",
    //   field: "batchCode",
    //   headerText: i18n.t("批次/卷号"),
    // },
    {
      width: '260',
      field: 'supplierCode',
      headerText: i18n.t('供应商'),
      searchOptions: MasterDataSelect.supplierSu,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
      }
    },
    {
      width: '0',
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      ignore: true
    },
    {
      width: '175',
      field: 'vmiWarehouseAddress',
      headerText: i18n.t('送货地址')
    },
    {
      width: '95',
      field: 'createUserName',
      headerText: i18n.t('制单人')
    },
    {
      width: '150',
      field: 'createTime',
      headerText: i18n.t('制单时间'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      },
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e && !isNaN(e) && `${e}`.length == 13) {
            e = Number(e)
            return timeNumberToDate({
              formatString: 'YYYY-mm-dd HH:MM:SS',
              value: e
            })
          } else {
            return '-'
          }
        }
      }
    },
    {
      width: '150',
      field: 'confirmTime',
      headerText: i18n.t('接收日期'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      },
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e && !isNaN(e) && `${e}`.length == 13) {
            e = Number(e)
            return timeNumberToDate({
              formatString: 'YYYY-mm-dd HH:MM:SS',
              value: e
            })
          } else {
            return '-'
          }
        }
      }
    }
  ]
}
