import { i18n } from '@/main.js'
import Vue from 'vue'
import onlyShowInput from '../edit/onlyShowInput.vue' // 物料、sku
import wuliao from '../edit/wuliao.vue' // 物料、sku
// vmi仓，goodsReceiptType === 1选择采购订单号，并且调用/tenant/vmi-receive-order/receive-limit-query拿出  VMI可送货数量
// goodsReceiptType 不等于1的时候  选择采购订单号不可以选择    订单数量，VMI可送货数量不展示 物料编码弹窗选择
export const columnSelect = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false, //隐藏在列选择器中的过滤
    allowEditing: false
  },
  // {
  //   width: "150",
  //   field: "buyerOrder",
  //   headerText: i18n.t("关联采购订单号"),
  //   editTemplate: () => {
  //     return {
  //       template: selectedOrder,
  //     };
  //   },
  // },
  // {
  //   width: "150",
  //   field: "itemNo",
  //   headerText: i18n.t("采购订单行号"),
  //   editTemplate: () => {
  //     return {
  //       template: selectedOrderLine,
  //     };
  //   },
  // },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    editTemplate: () => {
      return {
        template: onlyShowInput
      }
    }
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    editTemplate: () => {
      return { template: onlyShowInput }
    }
  },
  {
    width: '150',
    field: 'quantity',
    headerText: i18n.t('订单数量'),
    editTemplate: () => {
      return { template: onlyShowInput }
    }
  },
  {
    width: '150',
    field: 'countLimit',
    headerText: i18n.t('VMI可送货数量'),
    editTemplate: () => {
      return { template: onlyShowInput }
    }
  },
  {
    width: '150',
    field: 'count',
    headerText: i18n.t('送货数量'),
    editType: 'numericedit', //默认编辑类型之number
    edit: {
      params: {
        min: 1
      }
    }
  },
  {
    width: '150',
    field: 'checkCount',
    headerText: i18n.t('收货数量'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'unitName',
    headerText: i18n.t('单位'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: onlyShowInput
      }
    }
  },
  {
    width: '150',
    field: 'unitCode',
    headerText: i18n.t('单位编码'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: onlyShowInput
      }
    }
  },
  {
    width: '150',
    field: 'buyerOrgName',
    headerText: i18n.t('采购组'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: onlyShowInput
      }
    }
  },
  {
    width: '150',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组编码'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: onlyShowInput
      }
    }
    // visible: false,
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('行备注')
    // allowEditing: this.$route.query.create ? true : false,
  }
  // {
  //   width: "150",
  //   field: "batchCode",
  //   headerText: i18n.t("批次/卷号"),
  // },
  // {
  //   width: "150",
  //   field: "takeNo",
  //   headerText: i18n.t("车号/船号"),
  // },
]
export const columnPopup = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false, //隐藏在列选择器中的过滤
    allowEditing: false
  },
  {
    width: '100',
    field: 'rowNum',
    headerText: i18n.t('行号'),
    allowEditing: false,
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return Number(+row.index + 1)
      }
    }
  },
  {
    width: '200',
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    headerTemplate: () => {
      return {
        template: Vue.component('requiredCell', {
          template: `
            <div class="headers">
              <span style="color: red">*</span>
              <span class="e-headertext">{{ $t('物料编码') }}</span>
            </div>
          `
        })
      }
    },
    editTemplate: () => {
      return {
        template: wuliao
      }
    }
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    editTemplate: () => {
      return { template: onlyShowInput }
    }
  },
  {
    width: '150',
    field: 'count',
    headerText: i18n.t('送货数量'),
    headerTemplate: () => {
      return {
        template: Vue.component('requiredCell', {
          template: `
            <div class="headers">
              <span style="color: red">*</span>
              <span class="e-headertext">{{ $t('送货数量') }}</span>
            </div>
          `
        })
      }
    },
    // editType: "numericedit", //默认编辑类型之number
    edit: {
      params: {
        min: 1
      }
    }
  },
  {
    width: '150',
    field: 'checkCount',
    headerText: i18n.t('收货数量'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'unitName',
    headerText: i18n.t('单位'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: onlyShowInput
      }
    }
  },
  {
    width: '150',
    field: 'unitCode',
    headerText: i18n.t('单位编码'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: onlyShowInput
      }
    }
  },
  {
    width: '150',
    field: 'buyerOrgName',
    headerText: i18n.t('采购组'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: onlyShowInput
      }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('采购组')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    width: '150',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组编码'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: onlyShowInput
      }
    }
    // visible: false,
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('行备注')
    // allowEditing: this.$route.query.create ? true : false,
  }
  // {
  //   width: "150",
  //   field: "batchCode",
  //   headerText: i18n.t("批次/卷号"),
  // },
  // {
  //   width: "150",
  //   field: "takeNo",
  //   headerText: i18n.t("车号/船号"),
  // },
]

// 原来写的列，暂没用
export const originColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false //隐藏在列选择器中的过滤
  },
  // {
  //   width: "200",
  //   field: "buyerOrder",
  //   headerText: i18n.t("关联采购订单号"),
  //   editTemplate: () => {
  //     return {
  //       template: selectedOrder,
  //     };
  //   },
  // },
  // {
  //   width: "150",
  //   field: "itemNo",
  //   headerText: i18n.t("采购订单行号"),
  //   editTemplate: () => {
  //     return {
  //       template: selectedOrderLine,
  //     };
  //   },
  // },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    headerTemplate: () => {
      return {
        template: Vue.component('requiredCell', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{ $t('物料编码') }}</span>
              </div>
            `,
          data() {
            return { data: {} }
          }
        })
      }
    },
    editTemplate: () => {
      return {
        template: onlyShowInput
      }
    }
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    editTemplate: () => {
      return { template: onlyShowInput }
    }
  },
  {
    width: '150',
    field: 'quantity',
    headerText: i18n.t('订单数量'),
    editTemplate: () => {
      return { template: onlyShowInput }
    }
  },
  {
    width: '150',
    field: 'count',
    headerText: i18n.t('送货数量'),
    editType: 'numericedit', //默认编辑类型之number
    edit: {
      params: {
        min: 1
      }
    }
  },
  {
    width: '150',
    field: 'checkCount',
    headerText: i18n.t('收货数量'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'unitName',
    headerText: i18n.t('单位'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: onlyShowInput
      }
    }
  },
  {
    width: '150',
    field: 'unitCode',
    headerText: i18n.t('单位编码'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: onlyShowInput
      }
    }
  },
  {
    width: '150',
    field: 'buyerOrgName',
    headerText: i18n.t('采购组'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: onlyShowInput
      }
    }
  },
  {
    width: '150',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组编码'),
    allowEditing: false,
    editTemplate: () => {
      return {
        template: onlyShowInput
      }
    }
    // visible: false,
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('行备注')
    // allowEditing: this.$route.query.create ? true : false,
  }
  // {
  //   width: "150",
  //   field: "batchCode",
  //   headerText: i18n.t("批次/卷号"),
  // },
  // {
  //   width: "150",
  //   field: "takeNo",
  //   headerText: i18n.t("车号/船号"),
  // },
]
