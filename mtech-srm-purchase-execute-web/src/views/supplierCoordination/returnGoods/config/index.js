import { i18n } from '@/main.js'
import $utils from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName } from '@/utils/utils'

export const columnObj = {
  // 供方头视图
  headColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '200',
      field: 'vmiOrderCode', //vmiOrderCode
      headerText: i18n.t('VMI退货单号'),
      cellTools: []
    },
    {
      width: '115',
      field: 'status',
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map',
        //  status状态: 0:新建   1:待确认/已提交   2:已接收/待质检/已确认  8:已完成  9:已取消
        map: [
          { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
          { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
          { value: 2, text: i18n.t('已接收'), cssClass: 'col-inactive' },
          { value: 3, text: i18n.t('待WMS拣货'), cssClass: 'col-inactive' },
          { value: 4, text: i18n.t('待确认收货'), cssClass: 'active' },
          { value: 5, text: i18n.t('待WMS出库'), cssClass: 'col-inactive' },
          { value: 8, text: i18n.t('已完成'), cssClass: 'col-inactive' },
          { value: 9, text: i18n.t('已取消'), cssClass: 'col-inactive' }
        ]
      },
      searchOptions: {
        elementType: 'multi-select',
        operator: 'in',
        multiple: true,
        dataSource: [
          { label: i18n.t('新建'), value: 0 },
          { label: i18n.t('待确认'), value: 1 },
          { label: i18n.t('已接收'), value: 2 },
          { label: i18n.t('待WMS拣货'), value: 3 },
          { label: i18n.t('待确认收货'), value: 4 },
          { label: i18n.t('待WMS出库'), value: 5 },
          { label: i18n.t('已完成'), value: 8 },
          { label: i18n.t('已取消'), value: 9 }
        ],
        fields: { text: 'label', value: 'value' }
      },
      cellTools: [
        {
          id: 'accept',
          icon: 'a-icon_MultipleChoice_on',
          title: i18n.t('接受'),
          visibleCondition: (data) => {
            let isShow = false
            if (data.status == 1) {
              isShow = true
            }
            return isShow
          }
        },
        {
          id: 'cancel',
          icon: 'icon_list_recall',
          title: i18n.t('退回'),
          visibleCondition: (data) => {
            let isShow = false
            if (data.status == 1) {
              isShow = true
            }
            return isShow
          }
        }
      ]
    },
    {
      width: '150',
      field: 'wmsSyncStatus',
      headerText: i18n.t('WMS同步状态'),
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('未同步'),
          1: i18n.t('同步成功'),
          2: i18n.t('同步失败')
        }
      }
    },
    {
      width: '150',
      field: 'wmsSyncInfo',
      headerText: i18n.t('WMS同步信息'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'vmiWarehouseType',
      headerText: i18n.t('VMI仓类型'),
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('SRM管理库存'),
          1: i18n.t('原厂'),
          2: i18n.t('WMS管理库存')
        }
      }
    },
    {
      width: '260',
      field: 'siteCode',
      headerText: i18n.t('工厂'),
      searchOptions: MasterDataSelect.factorySupplierAddress,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.siteCode, data?.siteName)
      }
    },
    {
      width: '0',
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'supplierCode',
      headerText: i18n.t('供应商'),
      searchOptions: MasterDataSelect.supplierSu,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
      }
    },
    {
      width: '0',
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'vmiWarehouseCode',
      headerText: i18n.t('VMI仓'),
      searchOptions: MasterDataSelect.vmiWarehouseSupplier,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.vmiWarehouseCode, data?.vmiWarehouseName)
      }
    },
    {
      width: '0',
      field: 'vmiWarehouseName',
      headerText: i18n.t('VMI仓名称'),
      ignore: true
    },
    {
      width: '175',
      field: 'vmiWarehouseAddress', //vmiWarehouseAddress
      headerText: i18n.t('货品地址')
    },
    {
      width: '150',
      field: 'createTime', //createTime
      headerText: i18n.t('制单日期')
    },
    {
      width: '95',
      field: 'createUserName', //createUserName
      headerText: i18n.t('制单人')
    }
  ],
  // 供方明细视图
  detailedColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '200',
      field: 'vmiOrderCode', //vmiOrderCode
      headerText: i18n.t('VMI退货单号'),
      cellTools: []
    },
    {
      width: '150',
      field: 'status',
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map',
        //  status状态: 0:新建   1:待确认/已提交   2:已接收/待质检/已确认  8:已完成  9:已取消
        map: [
          { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
          { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
          { value: 2, text: i18n.t('已接收'), cssClass: 'col-inactive' },
          { value: 3, text: i18n.t('待WMS拣货'), cssClass: 'col-inactive' },
          { value: 4, text: i18n.t('待确认收货'), cssClass: 'active' },
          { value: 8, text: i18n.t('已完成'), cssClass: 'col-inactive' },
          { value: 5, text: i18n.t('待WMS出库'), cssClass: 'col-inactive' },
          { value: 9, text: i18n.t('已取消'), cssClass: 'col-inactive' }
        ]
      },
      searchOptions: {
        elementType: 'multi-select',
        operator: 'in',
        multiple: true,
        dataSource: [
          { label: i18n.t('新建'), value: 0 },
          { label: i18n.t('待确认'), value: 1 },
          { label: i18n.t('已接收'), value: 2 },
          { label: i18n.t('待WMS拣货'), value: 3 },
          { label: i18n.t('待确认收货'), value: 4 },
          { label: i18n.t('待WMS出库'), value: 5 },
          { label: i18n.t('已完成'), value: 8 },
          { label: i18n.t('已取消'), value: 9 }
        ],
        fields: { text: 'label', value: 'value' }
      }
    },
    {
      width: '70',
      field: 'rowNum', //rowNum
      headerText: i18n.t('行号')
    },
    {
      width: '130',
      field: 'itemCode', //supplierCode
      headerText: i18n.t('物料编码')
    },
    {
      width: '130',
      field: 'itemName',
      headerText: i18n.t('物料名称')
    },
    {
      width: '95',
      field: 'stockType',
      headerText: i18n.t('库存状态'),
      valueConverter: {
        type: 'map',
        map: {
          0: i18n.t('合格库存'),
          1: i18n.t('待检库存'),
          // 2: i18n.t("出库冻结库存"),
          3: i18n.t('不合格库存')
        }
      }
    },
    // {
    //   width: "105",
    //   field: "batchCode", //batchCode
    //   headerText: i18n.t("批次/卷号"),
    // },
    {
      width: '95',
      field: 'count', //count
      headerText: i18n.t('退货数量')
    },
    {
      width: '95',
      field: 'checkCount', //checkCount
      headerText: i18n.t('实收数量')
    },
    {
      width: '65',
      field: 'itemUnit', //itemUnit
      headerText: i18n.t('单位')
    },
    {
      width: '85',
      field: 'purchaseGroupCode', //purchaseGroupName
      headerText: i18n.t('采购组'),
      searchOptions: MasterDataSelect.businessCodeName,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.purchaseGroupCode, data?.purchaseGroupName)
      }
    },
    {
      width: '0',
      field: 'purchaseGroupName',
      headerText: i18n.t('采购组名称'),
      ignore: true
    },
    {
      width: '95',
      field: 'remark', //remark
      headerText: i18n.t('行备注')
    },
    // {
    //   width: "140",
    //   field: "orderCode", //orderCode
    //   headerText: i18n.t("关联采购订单号"),
    // },
    // {
    //   width: "150",
    //   field: "lineNo", //lineNo
    //   headerText: i18n.t("关联采购订单行号"),
    // },
    {
      width: '260',
      field: 'siteCode',
      headerText: i18n.t('工厂'),
      searchOptions: MasterDataSelect.factorySupplierAddress,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.siteCode, data?.siteName)
      }
    },
    {
      width: '0',
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'supplierCode',
      headerText: i18n.t('供应商'),
      searchOptions: MasterDataSelect.supplierSu,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
      }
    },
    {
      width: '0',
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'vmiWarehouseCode',
      headerText: i18n.t('VMI仓'),
      searchOptions: MasterDataSelect.vmiWarehouseSupplier,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.vmiWarehouseCode, data?.vmiWarehouseName)
      }
    },
    {
      width: '0',
      field: 'vmiWarehouseName',
      headerText: i18n.t('VMI仓名称'),
      ignore: true
    },
    {
      width: '170',
      field: 'vmiWarehouseAddress', //vmiWarehouseAddress
      headerText: i18n.t('送货地址')
    },
    {
      width: '150',
      field: 'createTime', //createTime
      headerText: i18n.t('制单日期'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      },
      valueConverter: {
        type: 'function',
        filter: (data) => {
          return $utils.formateTime(new Date(+data), 'YYYY-mm-dd HH:MM:SS')
        }
      }
    },
    {
      width: '95',
      field: 'createUserName', //createUserName
      headerText: i18n.t('制单人')
    },
    {
      width: '150',
      field: 'confirmTime', //  confirmTime
      headerText: i18n.t('接收时间'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      },
      valueConverter: {
        type: 'function',
        filter: (data) => {
          return $utils.formateTime(new Date(+data), 'YYYY-mm-dd HH:MM:SS')
        }
      }
    }
  ]
}
