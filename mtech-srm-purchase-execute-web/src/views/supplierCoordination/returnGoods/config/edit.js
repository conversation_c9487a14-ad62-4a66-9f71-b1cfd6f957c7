import { i18n } from '@/main.js'

export const editing = {
  allowEditing: true, //是否允许编辑
  allowDeleting: true, //是否允许删除
  allowAdding: true //是否允许新增
  //   allowFiltering: true, //是否允许过滤
  //   allowReordering: true, //是否重新排序
  //   allowResizing: true, //是否允许调整大小 列拖拽
  //   clipMode: "Ellipsis", //剪辑模式 Clip 内容溢出其区域时截断 Ellipsis显示省略号 EllipsisWithTooltip时显示省略号，当悬停在省略号应用的单元格上时，它也会显示工具提示
}
// 传递this
// let _this = this
// export const _thisFunction = function (vueObject) {
//   _this = vueObject
// }

export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false //隐藏在列选择器中的过滤
  },
  {
    field: 'orderCode',
    headerText: i18n.t('行号'),
    allowEditing: false //此列不允许编辑
  },
  {
    field: 'orderCode',
    headerText: i18n.t('物料编码'),
    allowEditing: false //此列不允许编辑
  },
  {
    field: 'des',
    headerText: i18n.t('物料名称'),
    allowEditing: false
  },
  {
    field: 'des',
    headerText: i18n.t('库存状态'),
    allowEditing: false
  },
  // {
  //   field: "des",
  //   headerText: i18n.t("批次/卷号"),
  //   allowEditing: false,
  // },
  {
    field: 'des',
    headerText: i18n.t('退货数量'),
    allowEditing: false
  },
  {
    field: 'number',
    headerText: i18n.t('发货数量'),
    editType: 'numericedit' //默认编辑类型之number
    // editTemplate: function () {
    //   return {
    //     template: Vue.component("addressTemplate", {
    //       template: `<mt-input id="number" type="numericedit" v-model="data.number" @blur="onchange"></mt-input>`,
    //       data() {
    //         return { data: {} };
    //       },
    //       methods: {
    //         onchange(value) {
    //           _this.$bus.$emit("number", value);
    //           const index = this.data.index;
    //           const field = this.data.column.field;
    //           _this.dataSource[index][field] = value;
    //         },
    //       },
    //     }),
    //   };
    // },
  },

  {
    field: 'result',
    headerText: i18n.t('单位'),
    allowEditing: false
  },
  {
    field: 'sorts',
    headerText: i18n.t('采购组'), // sortsObj,sortsElem;
    allowEditing: false
  },

  {
    field: 'remark',
    headerText: i18n.t('行备注'),
    allowEditing: false
  }
  // {
  //   field: "remark",
  //   headerText: i18n.t("关联采购订单号"),
  //   allowEditing: false,
  // },
  // {
  //   field: "remark",
  //   headerText: i18n.t("关联采购订单行号"),
  //   allowEditing: false,
  // },
  // {
  //   field: "time",
  //   headerText: i18n.t("时间"),
  //   allowEditing: false,
  //   // editType: "datepickeredit", //默认行内编辑类型之时间日期
  //   // format: "ymd",
  // },
]
