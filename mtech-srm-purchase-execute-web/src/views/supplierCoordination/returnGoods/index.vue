// 供应商退货列表
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :current-tab="0"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-name="downTemplateName"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { columnObj } from './config/index.js'
import { download, getHeadersFileName } from '@/utils/utils'
import Vue from 'vue'

export default {
  components: {
    // addDialog: require("./components/addDialog.vue").default,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  data() {
    return {
      downTemplateName: null, // 下载模板文件名
      downTemplateParams: {}, // 下载模板参数
      requestUrls: {}, // 上传下载接口地址
      pageConfig: [],
      addDialogShow: false,
      dialogData: null
    }
  },
  mounted() {
    if (this.$route.query.from === 'mytodo') {
      this.pageConfig = [
        {
          tab: { title: this.$t('头视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'accept',
                  icon: 'icon_table_acquire',
                  title: this.$t('供方接收')
                },
                {
                  id: 'InvoiceImport',
                  icon: 'icon_solid_Import',
                  title: this.$t('供方退回')
                },
                {
                  id: 'synchronous',
                  icon: 'icon_table_restart',
                  title: this.$t('同步WMS')
                },
                // {
                //   id: "import",
                //   icon: "icon_solid_upload",
                //   title: this.$t("导入"),
                // },
                {
                  id: 'export',
                  icon: 'icon_solid_Download',
                  title: this.$t('导出')
                },
                {
                  id: 'print',
                  icon: 'icon_table_print',
                  title: this.$t('打印')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: 'a69cead1-e992-4fb0-98d7-71c36cb7da24',
          grid: {
            columnData: columnObj.headColumn,
            asyncConfig: {
              url: this.$API.supplierCoordination.postVMIReturnQuery, //供方退货接口
              // recordsPosition: "data",
              rules: [
                {
                  field: 'vmi_order_type',
                  // type:"string",
                  operator: 'equal',
                  value: 3
                }
              ],
              defaultRules: JSON.parse(sessionStorage.getItem('todoDetail')).defaultRules
            },
            frozenColumns: 1
          }
        },
        {
          tab: { title: this.$t('明细视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'export1',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: '5c70a436-8a9a-4276-951a-789d12fc3716',
          grid: {
            columnData: columnObj.detailedColumn,
            asyncConfig: {
              url: this.$API.supplierCoordination.postVMIReturnQueryDetail, //供方明细接口接口
              // recordsPosition: "data",
              rules: [
                {
                  field: 'vmi_order_type',
                  // type:"string",
                  operator: 'equal',
                  value: 3
                }
              ]
            },
            frozenColumns: 1
          }
        }
      ]
    } else {
      this.pageConfig = [
        {
          tab: { title: this.$t('头视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'accept',
                  icon: 'icon_table_acquire',
                  title: this.$t('供方接收')
                },
                {
                  id: 'InvoiceImport',
                  icon: 'icon_solid_Import',
                  title: this.$t('供方退回')
                },
                {
                  id: 'synchronous',
                  icon: 'icon_table_restart',
                  title: this.$t('同步WMS')
                },
                // {
                //   id: "import",
                //   icon: "icon_solid_upload",
                //   title: this.$t("导入"),
                // },
                {
                  id: 'export',
                  icon: 'icon_solid_Download',
                  title: this.$t('导出')
                },
                {
                  id: 'print',
                  icon: 'icon_table_print',
                  title: this.$t('打印')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: 'a69cead1-e992-4fb0-98d7-71c36cb7da24',
          grid: {
            columnData: columnObj.headColumn,
            asyncConfig: {
              url: this.$API.supplierCoordination.postVMIReturnQuery, //供方退货接口
              // recordsPosition: "data",
              rules: [
                {
                  field: 'vmi_order_type',
                  // type:"string",
                  operator: 'equal',
                  value: 3
                }
              ],
              defaultRules: []
            },
            frozenColumns: 1
          }
        },
        {
          tab: { title: this.$t('明细视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'export1',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: '5c70a436-8a9a-4276-951a-789d12fc3716',
          grid: {
            columnData: columnObj.detailedColumn,
            asyncConfig: {
              url: this.$API.supplierCoordination.postVMIReturnQueryDetail, //供方明细接口接口
              // recordsPosition: "data",
              rules: [
                {
                  field: 'vmi_order_type',
                  // type:"string",
                  operator: 'equal',
                  value: 3
                }
              ]
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  methods: {
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    },
    // 上传（显示弹窗）
    handleUpload() {
      this.downTemplateParams = {
        flag: 0
      }
      this.requestUrls = {
        templateUrlPre: 'supplierCoordination',
        templateUrl: 'supplierOutTemplateDownload',
        uploadUrl: 'supplierOutImportExcel'
      }
      this.downTemplateName = this.$t('退货单导入模板')
      this.showUploadExcel(true)
    },
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = [] // 清空数据
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    handleDownload() {
      let obj = JSON.parse(
        sessionStorage.getItem('a69cead1-e992-4fb0-98d7-71c36cb7da24')
      )?.visibleCols
      let field = []
      if (obj !== undefined && obj.length) {
        obj.forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      } else {
        columnObj.headColumn.forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      }
      this.$store.commit('startLoading')
      this.$API.supplierCoordination.supplierOutExportExcel({}, field).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 跳转详情
    handleClickCellTitle(e) {
      let obj = {
        tabIndex: '',
        id: '',
        status: ''
      }
      if (e.tabIndex === 0) {
        obj.tabIndex = 0
      } else {
        obj.tabIndex = 1
      }
      if (e.field === 'vmiOrderCode') {
        obj.id = e.data.vmiOrderId ?? e.data.id
        obj.status = e.data.status
        this.redirectPage('purchase-execute/supplier-out-details', obj)
      }
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    // 表格上方操作
    handleClickToolBar(e) {
      let obj = {
        idList: []
      }

      e.gridRef.getMtechGridRecords().map((item) => {
        obj.idList.push(item.id)
      })
      if (e.toolbar.id === 'print') {
        let _selectRecords = e.grid.getSelectedRecords()
        if (_selectRecords.length <= 0 || _selectRecords.length > 1) {
          this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
          return
        }
        this.$API.purchaseCoordination.vmiReturnedOrderPrint(obj).then((res) => {
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = function () {
              console.log('======', reader)
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }

            return
          }
          const content = res.data
          let pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))

          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
      }
      // 供方接收
      if (e.toolbar.id === 'accept') {
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('供方接收'),
            message: this.$t('确认接收吗？')
          },
          success: () => {
            let ids = _selectRows.map((item) => item.id)
            let switAccept = true
            _selectRows.map((item) => {
              if (item.status != 1) {
                switAccept = false
              }
            })
            if (switAccept == false) {
              this.$toast({
                content: this.$t('请选择待确认状态'),
                type: 'warning'
              })
              return
            }
            this.$API.purchaseCoordination
              .postBuyerWarehousingReturnConfirm({ ids })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('接收成功'),
                    type: 'success'
                  })
                  // 刷新当前 Grid
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
          }
        })
      } else if (e.toolbar.id === 'InvoiceImport') {
        // 供方退回
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('供方退回'),
            message: this.$t('确认退回吗？')
          },
          success: () => {
            let ids = _selectRows.map((item) => item.id)
            let switInvoiceImport = true
            _selectRows.map((item) => {
              if (item.status != 1) {
                switInvoiceImport = false
              }
            })
            if (switInvoiceImport == false) {
              this.$toast({
                content: this.$t('请选择待确认状态'),
                type: 'warning'
              })
              return
            }
            this.$API.purchaseCoordination
              .postBuyerWarehousingReturnRejected({ ids })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('退回成功'),
                    type: 'success'
                  })
                  // 刷新当前 Grid
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
          }
        })
      } else if (e.toolbar.id === 'refreshDataByLocal') {
        // 刷新
      } else if (e.toolbar.id === 'import') {
        this.handleUpload()
      } else if (e.toolbar.id === 'export') {
        // 供方往来明细-导出
        this.handleDownload()
      } else if (e.toolbar.id === 'synchronous') {
        let _selectRecords = e.grid.getSelectedRecords()
        if (_selectRecords.length <= 0 || _selectRecords.length > 1) {
          this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
          return
        }
        this.synchronousWms(_selectRecords[0])
      } else if (e.toolbar.id === 'export1') {
        let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let obj = JSON.parse(
          sessionStorage.getItem('5c70a436-8a9a-4276-951a-789d12fc3716')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnObj.detailedColumn.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        let params = {
          page: { current: 1, size: 10000 },
          rules: rule.rules || []
        }
        params.rules = [
          ...params.rules,
          {
            field: 'vmi_order_type',
            // type:"string",
            operator: 'equal',
            value: 3
          }
        ]
        this.$store.commit('startLoading')
        this.$API.supplierCoordination
          .postSupplierWarehousingReturnDetailExport(params, field)
          .then((res) => {
            this.$store.commit('endLoading')
            const fileName = getHeadersFileName(res)

            download({ fileName: `${fileName}`, blob: res.data })
          })
      }
    },
    // 行内操作
    handleClickCellTool(e) {
      if (e.tool.id == 'accept') {
        this.$dialog({
          data: {
            title: this.$t('接受'),
            message: this.$t('确认接受吗？')
          },
          success: () => {
            this.confirm(e.data.id)
          }
        })
      } else if (e.tool.id == 'cancel') {
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认退回吗？')
          },
          success: () => {
            this.rejected(e.data.id)
          }
        })
      }
    },

    // 接收
    confirm(data) {
      let obj = {
        ids: [data]
      }
      this.$API.purchaseCoordination.postBuyerWarehousingReturnConfirm(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('接收成功'), type: 'success' })
          // 刷新当前 Grid
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('接收失败'), type: 'error' })
        }
      })
    },
    // 退回
    rejected(data) {
      let obj = {
        ids: [data]
      }
      this.$API.purchaseCoordination.postBuyerWarehousingReturnRejected(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('退回成功'), type: 'success' })
          // 刷新当前 Grid
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('退回失败'), type: 'error' })
        }
      })
    },

    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },
    // 同步WMS
    synchronousWms(data) {
      if (data.wmsSyncStatus === 1) {
        this.$toast({
          content: this.$t('入库单已成功同步WMS，无需重复同步'),
          type: 'warning'
        })
        return
      }
      this.$API.purchaseCoordination.returnSynchronousWms({ id: data.id }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('同步成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('同步失败'), type: 'warning' })
        }
      })
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('todoDetail')
  }
}
</script>

<style style="scss" scoped>
.full-height {
  height: 100%;
}
</style>
