// 供方接收
<template>
  <div class="full-height pt20 vertical-flex-box">
    <div>
      <top-info
        class="flex-keep"
        :header-info="headerInfo"
        @goBack="goBack"
        @doExpand="doExpand"
        @acceptBtn="acceptBtn"
        @confirmBtn="confirmBtn"
      ></top-info>
    </div>
    <hr />
    <mt-template-page :template-config="componentConfig">
      <div slot="slot-0" :style="{ padding: '30px' }">
        <br />
        <mt-data-grid
          ref="dataGrid"
          :data-source="dataSource"
          :column-data="columnData"
          :edit-settings="editing"
          :toolbar="toolbar"
          @actionBegin="actionBegin"
        ></mt-data-grid>
      </div>
      <div slot="slot-1" :style="{ padding: '30px' }">
        <logistics-info :logistics-data="logisticsData"></logistics-info>
      </div>
    </mt-template-page>
  </div>
</template>

<script>
import { columnData, editing } from './config/edit'
export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue'),
    LogisticsInfo: () => import('./components/logisticsInfo.vue')
  },
  data() {
    return {
      componentConfig: [
        {
          tab: { title: this.$t('物料信息') },
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false // 使用组件中的toolbar配置
        },
        {
          tab: { title: this.$t('物流信息') }
        }
      ],
      dataSource: [
        {
          orderCode: '1-1-12345',
          des: this.$t('物料1描述'),
          number: 1000,
          result: this.$t('合格'),
          sorts: this.$t('包装损坏'),
          boolean: true,
          remark: this.$t('我是备注'),
          time: '2020/1/1'
        },
        {
          orderCode: '2-2-2345',
          des: this.$t('物料2描述'),
          number: 999,
          result: this.$t('不合格'),
          sorts: this.$t('残次品'),
          boolean: false,
          remark: this.$t('我是备注'),
          time: '2020/1/2'
        },
        {
          orderCode: '3-2-2345',
          des: this.$t('这行是不允许编辑的'),
          number: 888,
          result: this.$t('不合格'),
          sorts: this.$t('残次品'),
          boolean: false,
          remark: this.$t('不允许编辑的行'),
          time: '2020/1/1'
        }
      ],
      columnData: columnData,
      toolbar: ['Edit', 'Cancel', 'Update'], //编辑和取消、保存按钮
      editing: editing,
      headerInfo: {
        // status: 1,
        // preparer: "jcs",
        // createTime: "2020-01-24",
        // plantDescription: this.$t("京东"),
        accept: true,
        confirm: true
      }, //头部信息
      logisticsData: {
        type: 1
      } // 物流信息
    }
  },
  // mounted() {
  //   _thisFunction(this)
  //   // this.getDetailData();
  // },
  methods: {
    goBack() {
      this.$router.push({
        name: 'supplier-out-index'
      })
    },
    // 展开收起的hearder
    doExpand() {
      this.$refs.templateRef?.resetGridHeight && this.$refs.templateRef.resetGridHeight()
    },
    // 接收
    acceptBtn() {
      console.log(this.dataSource, 77777)
    },
    // 供方端点
    confirmBtn() {},
    actionBegin(args) {
      if (args.requestType === 'beginEdit') {
        if (args.rowIndex == 2) {
          args.cancel = true //禁止行编辑
        }
      }
      if (args.requestType == 'save') {
        // console.log(args,"555555555");
        //更新质检编辑完毕后的视图
        args.data.result = this.dataSource[args.rowIndex].result
      }
    }
    // 详情数据接口
    // getDetailData(){
    //   let obj = {
    //     id: this.$route.query.id
    //   }
    //   this.$API.purchaseCoordination.postBuyerWarehousingReturnDetail(obj).then(res =>{
    //     if(res.code === 200){
    //       this.headerInfo = res.data
    //       // if(this.$route.query.status === '0'){
    //       //   this.headerInfo.verificationStatus = true
    //       // }else if(this.$route.query.status === '1'){
    //       //   this.headerInfo.sentenceStatus = true
    //       // }
    //       this.dataSource = res.data.vmiOrderItemResponses
    //       this.logisticsData = res.data.orderLogistic[0]  //订单物流信息
    //     }
    //   })
    // },
  }
}
</script>

<style scoped lang="scss">
.full-height {
  height: 100%;
}
</style>
