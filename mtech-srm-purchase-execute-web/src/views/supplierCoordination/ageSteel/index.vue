// 报表
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      current-tab="0"
      :permission-obj="permissionObj"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <create-dialog
      v-if="deliveryShow"
      ref="child"
      @handleAddDialogShow="handleAddDialogShow"
      @handleDialogShow="handleDialogShow"
    ></create-dialog>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'

import { columnObj } from './config/index.js'

export default {
  data() {
    return {
      pageConfig: [
        {
          tab: { title: this.$t('库龄报表') },
          toolbar: {
            tools: [
              [
                {
                  id: 'export1',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          useBaseConfig: true, // 使用组件中的toolbar配置
          // dataPermission: "a",
          // permissionCode: "T_02_0122",
          dataSource: [],
          useToolTemplate: false,
          useCombinationSelection: false,
          // gridId: "E76F8812-B1F0-3298-EBB8-C3CB387A1B96",
          grid: {
            columnData: columnObj.headColumn,
            dataSource: [],
            asyncConfig: {
              url: '/statistics/tenant/vmiStock/supplier/stock-day-query'
            },
            frozenColumns: 1
          }
        }
      ],
      deliveryShow: false,

      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    // 头部操作
    handleClickToolBar(args) {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}

      if (args.toolbar.id === 'export1') {
        let params = {
          page: { current: 1, size: 1000 },
          rules: rule.rules || []
        }
        this.$store.commit('startLoading')
        this.$API.supplierCoordination.supplierStockExcelExport(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
      }
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
