import { i18n } from '@/main.js'

export const columnObj = {
  headColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '200',
      field: 'factoryCode',
      headerText: i18n.t('工厂编码')
    },
    {
      width: '200',
      field: 'factoryName',
      headerText: i18n.t('工厂名称')
    },
    {
      width: '150',
      field: 'supplierCode',
      headerText: i18n.t('供应商编码')
    },
    {
      width: '150',
      field: 'supplierName',
      headerText: i18n.t('供应商名称')
    },
    {
      width: '150',
      field: 'warehouseCode',
      headerText: i18n.t('仓库编码')
    },
    {
      width: '150',
      field: 'warehouseName',
      headerText: i18n.t('仓库名称')
    },
    {
      width: '150',
      field: 'stockStatus',
      headerText: i18n.t('库存状态')
    },
    {
      width: '150',
      field: 'materialCode', //supplierCode
      headerText: i18n.t('物料编码')
    },
    {
      width: '150',
      field: 'materialName', //supplierName
      headerText: i18n.t('物料名称')
    },
    {
      width: '150',
      field: 'purchaseGroupCode', //supplierName
      headerText: i18n.t('采购组编码')
    },
    {
      width: '150',
      field: 'purchaseGroupName', //supplierName
      headerText: i18n.t('采购组名称')
    },
    {
      width: '150',
      field: 'inTransitInventory',
      headerText: i18n.t('在途库存')
    },
    {
      width: '150',
      field: 'writeOffQty',
      headerText: i18n.t('已冲销库存')
    },
    {
      width: '150',
      field: 'deliversQuantity',
      headerText: i18n.t('已收货')
    },
    {
      width: '150',
      field: 'unconfirmedInstockQty',
      headerText: i18n.t('待确认入库数量')
    },
    {
      width: '150',
      field: 'invQty',
      headerText: i18n.t('当前库存数')
    },
    {
      width: '150',
      field: 'stockQty30',
      headerText: i18n.t('库龄小于30天库存数量')
    },
    {
      width: '150',
      field: 'stockQty3160',
      headerText: i18n.t('库龄31-60天库存数量')
    },
    {
      width: '150',
      field: 'stockQty61180',
      headerText: i18n.t('库龄61-180天库存数量')
    },
    {
      width: '150',
      field: 'stockQty180',
      headerText: i18n.t(' 库龄180天以上库存数量')
    }
  ]
}
