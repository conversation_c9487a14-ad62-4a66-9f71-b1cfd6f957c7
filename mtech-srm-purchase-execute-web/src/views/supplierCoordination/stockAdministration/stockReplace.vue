// 供方 物料替换VMI供应商确认
<template>
  <div class="full-height pt20">
    <top-info
      class="flex-keep"
      @goBack="goBack"
      @acceptBtn="acceptBtn"
      @backBtn="backBtn"
    ></top-info>
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config/stockConfirm.js'
import TopInfo from './components/topInfoNew.vue'
import { headerInfoInit, setHeaderInfo } from './config/variable'
export default {
  components: {
    TopInfo
  },
  data() {
    return {
      // headerInfo: {
      //   status: 1,
      //   createUserName: "jcs",
      //   createTime: "2022-03-02",
      //   siteCode: "",
      //   supplierCode: "0001",
      //   companyDescribe: this.$t("描述"),
      //   warehouseCode: "111",
      //   warehouseName: this.$t("VMI111号仓"),
      //   warehouseDescribe: this.$t("描述"),
      //   remark: "",
      //   // accept: true,
      //   // confirm: true,
      //   isDisabled: true,
      // },
      pageConfig: [
        {
          title: this.$t('汇总列表'),
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[], []]
          },
          grid: {
            columnData: columnData,
            dataSource: [],
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },
  watch: {
    $route(to) {
      // 如果是返回列表，那么就移除dom，防止下拉组件多次触发change事件
      if (to.name == 'supplier-stock-index') {
        headerInfoInit()
      }
    }
  },
  mounted() {
    this.getDetails()
  },
  methods: {
    switchNumber(data) {
      let statusType = ''
      switch (data) {
        case 0:
          statusType = this.$t('合格库存')
          break
        case 1:
          statusType = this.$t('待检库存')
          break
        case 3:
          statusType = this.$t('不合格库存')
          break
        default:
          break
      }
      return statusType
    },
    // 三方公用一个详情接口
    getDetails() {
      let _that = this
      let obj = {
        id: this.$route.query.id
      }
      this.$API.purchaseCoordination.postBuyerWarehousingReturnDetail(obj).then((res) => {
        if (res.code === 200) {
          setHeaderInfo(res.data)

          _that.pageConfig[0].grid.dataSource = res.data.vmiOrderItemResponses
          // _that.gridStatus = true
          _that.pageConfig[0].grid.dataSource.forEach((item) => {
            item.enterStatus = this.switchNumber(item.stockType)
          })
        } else {
          // _that.gridStatus = true
        }
      })
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    // 接受 采访，供方共用一个接口
    acceptBtn() {
      let apiInterface = ''
      if (this.$route.query.orderStatus == 5) {
        apiInterface = 'purchaseAllocationReceive'
      } else if (this.$route.query.orderStatus == 6) {
        apiInterface = 'purchaseReplaceReceive'
      }
      let obj = {
        ids: []
      }
      obj.ids.push(this.$route.query.id)
      this.$API.purchaseCoordination[apiInterface](obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('接受成功'), type: 'success' })
          this.goBack()
        } else {
          this.$toast({ content: this.$t('接受失败'), type: 'error' })
        }
      })
    },
    // 退回 采访，供方共用一个接口
    backBtn() {
      let apiInterface = ''
      if (this.$route.query.orderStatus == 5) {
        apiInterface = 'purchaseAllocationBackOff'
      } else if (this.$route.query.orderStatus == 6) {
        apiInterface = 'purchaseReplaceBackOff'
      }
      let obj = {
        ids: []
      }
      obj.ids.push(this.$route.query.id)
      this.$API.purchaseCoordination[apiInterface](obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('回退成功'), type: 'success' })
          this.goBack()
        } else {
          this.$toast({ content: this.$t('回退失败'), type: 'error' })
        }
      })
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
.titleColor {
  color: #00469c;
}
</style>
