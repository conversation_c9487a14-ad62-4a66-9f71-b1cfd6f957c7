// 供方-库存调整新建
<template>
  <div class="home">
    <top-info
      class="flex-keep"
      @goBack="goBack"
      @submitBtn="submitBtn"
      @preservationBtn="preservationBtn"
      ref="infoRules"
    ></top-info>
    <mt-template-page :template-config="componentConfig">
      <div slot="slot-0">
        <mt-data-grid
          id="Grid1"
          class="pe-edit-grid edit-grid"
          :data-source="dataSource"
          :column-data="columnData"
          ref="dataGrid"
          :allow-paging="allowPaging"
          :edit-settings="editSettings"
          @actionBegin="actionBegin"
          @actionComplete="actionComplete"
          :page-settings="pageSettings"
          :toolbar="toolbar"
          @toolbarClick="toolbarClick"
          @dataBound="dataBound"
        ></mt-data-grid>
      </div>
    </mt-template-page>
  </div>
</template>

<script>
/**
 * 路由上的参数：
 *  details add-新建，edit-编辑，beConfirm-待确认，detail-纯详情
 *  orderStatus 库存调整单的类型
 */
import { addSupplyStockCols } from './config'
import { headerInfo, headerInfoInit, setHeaderInfo } from './config/variable'
import { detailStatusType } from './config/constant'
export default {
  name: 'Home',
  components: {
    TopInfo: require('./components/topInfoNew.vue').default
  },
  data() {
    return {
      componentConfig: [
        {
          tab: { title: this.$t('物料信息') },
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false // 使用组件中的toolbar配置
        }
      ],
      dataSource: [],
      columnData: addSupplyStockCols,
      editSettings: {
        allowEditing: true, //是否允许编辑
        allowDeleting: true, //是否允许删除
        allowAdding: true, //是否允许新增
        showDeleteConfirmDialog: true
      },
      toolbar: ['Add', 'Edit', 'Cancel', 'Update', 'Delete'],
      allowPaging: false, // 产品要求：新增/编辑时，不分页；查看时要分页
      isEditStatus: false, // 正处于编辑状态
      actionFlag: '', // 点击按钮，可能是 保存草稿-save; 提交-submit;...
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        totalRecordsCount: 0,
        pageSizes: [10, 20, 50, 100, 200]
      }
    }
  },
  watch: {
    $route(to) {
      console.log('判断是否返回列表', to)
      if (to.name == 'supplier-stock-index') {
        headerInfoInit()
        localStorage.removeItem(`itemCode`)
      }
    }
  },
  mounted() {
    console.log('我是刚进入页面的')
    this.$nextTick(() => {
      window.gridObj = this.$refs.dataGrid
    })

    // 如果有id，那么就获取详情
    if (this.$route.query?.id) this.getDetails()

    // 如果不是新增和编辑，就不可修改
    if (![detailStatusType.add, detailStatusType.edit].includes(this.$route.query.details)) {
      this.toolbar = []
      this.allowPaging = true
      this.editSettings = null
    }
  },
  methods: {
    dataBound() {
      // console.log("dataBound", args);
      let dataRef = this.$refs.dataGrid.$refs.ejsRef
      let _dataSource = dataRef.getCurrentViewRecords()
      _dataSource.forEach((item, index) => {
        dataRef.setCellValue(item.addId, 'rowNum', Number(index + 1))
      })
    },
    actionBegin(args) {
      // if (
      //   this.$route.query.details == 1 &&
      //   args.rowIndex === args.rowIndex &&
      //   args.rowIndex !== undefined
      // ) {
      //   args.cancel = true; //禁止行编辑
      // }

      if (args.requestType == 'add' || args.requestType == 'beginEdit') {
        if (!this.$refs.infoRules.checkForm()) {
          return
        }
        this.isEditStatus = true
      }
      if (args.requestType == 'refresh') {
        this.isEditStatus = false
      }
      if (args.requestType == 'add') {
        args.data.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
      }
    },

    actionComplete(args) {
      if (args.requestType == 'save') {
        this.isEditStatus = false
        if (this.actionFlag == 'submit') {
          // this.handleSubmit();
        }
      } else if (args.requestType == 'delete') {
        if (this.$refs.dataGrid?.$refs?.ejsRef?.getCurrentViewRecords()?.length === 0) {
          headerInfo.hasDetailRow = false // 使头部数据可改变，因为通过头部数据获取物料，表格没有数据时，头部数据可修改
        }
      }
    },
    handleSubmit() {
      console.log(this.$t('点击了提交'), window.gridObj.ejsRef.getCurrentViewRecords())
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    // 提交: 如果处于编辑状态，需要先结束掉，再获取数据
    submitBtn() {
      if (this.isEditStatus) {
        window.gridObj.ejsRef.endEdit()
      }
      if (!this.$refs.infoRules.checkForm()) return
      this.create(2)
    },
    // 保存
    preservationBtn() {
      if (this.isEditStatus) {
        window.gridObj.ejsRef.endEdit()
      }
      if (!this.$refs.infoRules.checkForm()) return
      this.create(1)
    },
    // 表格上部操作
    toolbarClick(e) {
      // 自定义了一个行内新增按钮，为了给新增的这一行赋默认值
      if (e.item.id == 'Grid1_add') {
        if (!this.$refs.infoRules.checkForm()) return
        // 新增
        this.$refs.dataGrid.ejsRef.addRecord()
        headerInfo.hasDetailRow = true
      } else if (e.item.id == 'Delete') {
        // 删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            this.$refs.dataGrid.ejsRef.deleteRecord()
          }
        })
      }
    },
    // 根据列新建行数据
    // newRowData() {
    //   let row = {};
    //   // 初始化数据
    //   this.columnData.forEach((item) => {
    //     if (item.field === "rowNum") {
    //       row[item.field] =
    //         this.$refs.dataGrid.ejsRef.getCurrentViewRecords().length;
    //     }
    //   });
    //   return row;
    // },
    // 创建
    create(val) {
      let obj = {
        operationType: val,
        siteCode: headerInfo.siteCode,
        // siteName: headerInfo.siteCode.split("-")[1],
        supplierCode: headerInfo.supplierCode,
        vmiWarehouseCode: headerInfo.vmiWarehouseCode,
        vmiWarehouseName: null,
        remark: headerInfo.remark,
        itemList: window.gridObj?.ejsRef?.getCurrentViewRecords() || []
      }
      // window.gridObj.ejsRef.getCurrentViewRecords().forEach((item) => {
      //   let arrObj = {};
      //   arrObj.count = item.count;
      //   arrObj.itemCode = item.itemCode;
      //   arrObj.itemUnit = item.baseMeasureUnitCode;
      //   arrObj.purchaseGroupCode = item.purchaseGroupCode;
      //   obj.itemList.push(arrObj);
      // });
      this.$API.supplierCoordination.postSupplierStockImportCreate(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('创建成功'), type: 'success' })
          this.goBack()
        } else {
          this.$toast({ content: this.$t('创建失败'), type: 'error' })
        }
      })
    },
    // 请求详情
    // 三方公用一个详情接口
    getDetails() {
      let _that = this
      let obj = {
        id: this.$route.query.id
      }
      this.$API.purchaseCoordination.postBuyerWarehousingReturnDetail(obj).then((res) => {
        if (res.code === 200) {
          setHeaderInfo(res.data)
          let dataSource = res.data.vmiOrderItemResponses
          dataSource.forEach((i) => (i.addId = i.id))
          _that.dataSource = dataSource
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
$disabledBg: rgba(245, 245, 245, 1);
$requireddBg: rgba(237, 161, 51, 0.1);
.home {
  height: 100%;
  display: flex;
  flex-direction: column;

  .common-template-page {
    flex: 1;

    /deep/ .template-wrap > div {
      height: 100%;
    }
  }

  .pe-edit-grid {
    height: 80%;

    /deep/ .e-grid {
      height: 100%;
      .e-gridcontent {
        height: calc(100% - 90px);
        .e-content {
          height: 100% !important;
        }
      }
    }
  }

  .bgTransparent {
    background-color: transparent !important;
  }

  /deep/ .edit-grid {
    // 去掉 第一个单元格选中时的左边框样式，包括 列冻结后第一个单元格选中
    tr td:first-child::before {
      display: none;
    }
    .e-frozenheader,
    .e-frozenheader > .e-table,
    .e-frozencontent > .e-table {
      border-right: unset !important;
    }
    // 去掉 单元格的选中背景色
    td.e-active {
      @extend .bgTransparent;
    }
    // 去掉 行上悬浮时的单元格背景色
    tr:hover td {
      @extend .bgTransparent;
    }
    // 去掉冻结列 悬浮时，改变背景色问题
    &.e-gridhover .e-frozenhover,
    .e-detailcell,
    .e-detailindentcell,
    .e-detailrowcollapse,
    .e-detailrowexpand,
    .e-groupcaption,
    .e-indentcell,
    .e-recordpluscollapse,
    .e-recordplusexpand,
    .e-rowcell {
      @extend .bgTransparent;
    }

    // 编辑时
    .e-editedrow,
    .e-addedrow {
      .e-rowcell .e-control-wrapper {
        // 禁用的单元格背景色
        &.e-disabled,
        &.cell-disabled,
        .e-input[readonly]:not(.e-dropdownlist) {
          background: $disabledBg !important;
          cursor: not-allowed;
        }

        // 必填的单元格背景色
        &.isRequired .e-input {
          background: $requireddBg !important;
        }
      }
    }

    // 非编辑时
    tr td {
      // 禁用的单元格 样式
      &.e-rowcell.bg-grey,
      &.e-rowcell.bg-grey.e-updatedtd {
        background-color: $disabledBg !important;
        color: #9a9a9a !important;
        cursor: not-allowed;
        &.e-gridchkbox {
          @extend .bgTransparent;
          cursor: pointer;
        }
      }

      // 必填的单元格 样式
      &.e-rowcell.bg-red,
      &.e-rowcell.bg-red.e-updatedtd {
        background-color: $requireddBg !important;
      }
    }
  }
}
</style>
