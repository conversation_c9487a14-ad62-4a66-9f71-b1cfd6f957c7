<template>
  <div class="pc-select">
    <div class="in-cell">
      <mt-input :id="fieldName" disabled v-model="value" :width="130" />
      <mt-icon style="width: 20px" name="icon_list_refuse" @click.native="handleClear"></mt-icon>
      <mt-icon style="width: 20px" name="icon_input_search" @click.native="showDialog"></mt-icon>
    </div>
    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="title"
      :buttons="buttons"
      @close="handleClose"
    >
      <mt-template-page
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="pageConfig"
        @recordDoubleClick="recordDoubleClick"
      ></mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
import { PROXY_MDM_AUTH } from '@/utils/constant'
import { headerInfo } from '../config/variable.js'

export default {
  props: {},
  data() {
    return {
      fieldName: null,
      dialogShow: false,
      title: this.$t('请选择'),
      value: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [],
      changeArr: [
        'itemCode',
        'itemName',
        'itemUnit',
        'itemUnitDescription',
        'purchaseGroupName',
        'purchaseGroupCode'
      ]
    }
  },
  mounted() {
    // this.pageConfig[0].grid.asyncConfig = {
    //   url: `${PROXY_MDM_AUTH}/item-org-rel/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    //   params: {
    //     customerEnterpriseId: headerInfo.customerEnterpriseId,
    //     organizationCode: headerInfo.siteCode
    //   },
    //   methods: 'post',
    //   serializeList: this.serializeList
    // }
    this.fieldName = this.data.column.field
    this.value = this.data[this.fieldName]
  },
  methods: {
    serializeList(data) {
      let obj = []
      data.forEach((item) => {
        obj.push({
          item,
          ...item.purchasingBasicInOrgResponse.itemInfo,
          ...item.purchasingBasicInOrgResponse.purchasingInfo
        })
        // return {
        //   item,
        //   ...item.purchasingBasicInOrgResponse.itemInfo,
        //   ...item.purchasingBasicInOrgResponse.purchasingInfo,
        // };
      })
      obj.forEach((element) => {
        element.itemUnit = element.baseMeasureUnitCode
        element.itemUnitDescription = element.baseMeasureUnitName
      })
      return obj
    },
    recordDoubleClick(args) {
      this.confirm(null, [args.rowData])
    },
    confirm(e, records) {
      if (!records || records.length <= 0) {
        records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      }
      if (records.length <= 0) return
      console.log(records, '-=-=')
      //   this.value = records[0]?.itemName;
      this.value = records[0]?.itemCode
      //联动改变物料描述
      this.changeArr.forEach((i) => {
        this.$bus.$emit(`${i}Change`, records[0]?.[i])
      })

      // 关闭弹窗
      this.handleClose()
    },
    handleClear() {
      this.value = null
      //联动改变物料描述
      this.changeArr.forEach((i) => {
        this.$bus.$emit(`${i}Change`, null)
      })
    },
    handleClose() {
      this.dialogShow = false
      this.$refs.dialog.ejsRef.hide()
    },
    showDialog() {
      this.dialogShow = true
      this.pageConfig = [
        {
          toolbar: [],
          gridId: '92853ae7-e590-43bb-9e8a-1295db35bbac',
          grid: {
            // height: 352,
            allowPaging: true,
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: [
              {
                width: '150',
                field: 'itemCode',
                headerText: this.$t('物料编号')
              },
              { width: '150', field: 'itemName', headerText: this.$t('物料名称') },
              {
                width: '150',
                field: 'itemUnit',
                headerText: this.$t('单位')
              },
              {
                width: '150',
                field: 'itemUnitDescription',
                headerText: this.$t('单位编码')
              },
              {
                width: '150',
                field: 'purchaseGroupName',
                headerText: this.$t('采购组')
              },
              {
                width: '150',
                field: 'purchaseGroupCode',
                headerText: this.$t('采购组编码')
              }
            ],
            asyncConfig: {
              url: `${PROXY_MDM_AUTH}/item-org-rel/paged-query?BU_CODE=${localStorage.getItem(
                'currentBu'
              )}`,
              params: {
                customerEnterpriseId: headerInfo.customerEnterpriseId,
                organizationCode: headerInfo.siteCode
              },
              methods: 'post',
              serializeList: this.serializeList
            }
          }
        }
      ]
      this.$refs.dialog.ejsRef.show()
    }
  }
}
</script>

<style scoped lang="scss">
.in-cell {
  width: 100%;
  display: flex;
  align-items: center;
  min-width: 200px;
  i {
    width: 12px !important;
  }
  > .mt-icons {
    margin-left: 5px;
    cursor: pointer;
  }
}
</style>
