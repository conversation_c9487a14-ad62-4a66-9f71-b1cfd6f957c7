// 查看变更记录
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog dialog-payment"
    :header="dialogTitle"
    :buttons="buttons"
    width="80%"
    @close="handleClose"
  >
    <div class="full-height pt20">
      <!-- <div class="header-box">
        <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      </div> -->
      <mt-template-page
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
      ></mt-template-page>
    </div>
  </mt-dialog>
</template>

<script>
import { columnData } from './config/importStock.js'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: this.$t('关闭')
        }
      ],
      pageConfig: [
        {
          title: this.$t('变更记录'),
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                {
                  id: 'export',
                  icon: 'icon_solid_export',
                  title: this.$t('导出')
                }
              ],
              []
            ]
          },
          gridId: '4970af96-1de4-4511-ba55-f66eb0a5499c',
          grid: {
            height: 500,
            columnData: columnData,
            lineIndex: 0,
            // autoWidthColumns: columnData.length + 1,
            dataSource: [],
            // asyncConfig: {
            //   url: "/srm-purchase-execute/tenant/pe/business/configs",
            //   recordsPosition: "data",
            // },
            frozenColumns: 1,
            editSettings: {
              allowEditing: false, //是否允许编辑
              allowDeleting: false, //是否允许删除
              allowAdding: false //是否允许新增
            }
          }
        }
      ],
      addDialogShow: false,
      dialogData: null,
      formModel: {}
    }
  },
  computed: {
    dialogTitle() {
      return this.modalData.headerTitle
    }
  },
  created() {
    // this.getDetails()
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    const _query = JSON.parse(sessionStorage.getItem('checkEditHeaderInfo'))
    this.formModel = _query
    this.getDetails(_query)
  },
  methods: {
    handleClose() {
      // this.$refs.dialog.ejsRef.hidden()
      this.$emit('cancel-function')
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    handleClickToolBar(e) {
      if (e.toolbar.id === 'export') {
        // 调用导出方法·
        this.handleExport()
      }
    },
    handleExport() {
      const params = {
        vmiStockId: this.formModel.id,
        sourceType: [1, '1'].includes(this.formModel.vmiWarehouseType)
          ? 'VMI_INVENTORY'
          : this.formModel.vmiWarehouseType
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.purchaseCoordination.supSystemCallInfo(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 请求详情
    // 三方公用一个详情接口
    getDetails(param) {
      let obj = {
        vmiStockId: param.id,
        sourceType: [1, '1'].includes(param.vmiWarehouseType)
          ? 'VMI_INVENTORY'
          : param.vmiWarehouseType,
        pageNo: 1,
        pageSize: 1000
      }
      this.pageConfig[0].grid.dataSource = []
      this.$API.purchaseCoordination.checkEditInfo(obj).then((res) => {
        if (res.code === 200) {
          this.pageConfig[0].grid.dataSource = res.data
          // _that.$set(_that.pageConfig[0].grid, 'dataSource', res.data)
          sessionStorage.removeItem('checkEditHeaderInfo')
        }
      })
    }
  }
}
</script>

<!-- <style lang="scss" scoped>
.set-country {
  height: 100%;
}
.titleColor {
  color: #00469c;
}
/deep/ .header-box {
  text-align: right;
}
</style> -->
