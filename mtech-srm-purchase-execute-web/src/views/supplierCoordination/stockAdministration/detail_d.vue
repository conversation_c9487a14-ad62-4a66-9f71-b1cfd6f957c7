// VMI供应商导入库存详情
<template>
  <div class="full-height pt20">
    <top-info class="flex-keep" @goBack="goBack" @confirmBtn="confirmBtn"></top-info>
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config/importStock.js'
import TopInfo from './components/topInfoNew.vue'
import { headerInfo, headerInfoInit } from './config/variable'
export default {
  components: {
    TopInfo
  },
  data() {
    return {
      // headerInfo: {
      //   confirm: true,
      // },
      pageConfig: [
        {
          title: this.$t('汇总列表'),
          toolbar: [['Add', 'Edit', 'Cancel', 'Update', 'Delete'], []],
          gridId: '51fc30cf-267c-4a4b-800e-088cb89d2349',
          grid: {
            columnData: columnData,
            // lineIndex: 1,
            // autoWidthColumns: columnData.length + 1,
            dataSource: [],
            // asyncConfig: {
            //   url: "/srm-purchase-execute/tenant/pe/business/configs",
            //   recordsPosition: "data",
            // },
            frozenColumns: 1,
            editSettings: {
              allowEditing: true, //是否允许编辑
              allowDeleting: true, //是否允许删除
              allowAdding: true //是否允许新增
            }
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },
  created() {
    // 判断是不是详情页，如果是详情页，就不可以编辑
    if (this.$route.query.details) {
      headerInfo.confirm = false
      this.pageConfig[0].toolbar = [[], ['Filter', 'Refresh', 'Setting']]
      this.pageConfig[0].useToolTemplate = false
      this.pageConfig[0].grid.editSettings = null
    }
    this.getDetails()
  },
  methods: {
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    // 确认
    confirmBtn() {
      this.$dialog({
        data: {
          title: this.$t('接受'),
          message: this.$t('确认接受吗？')
        },
        success: () => {
          this.confirm()
        }
      })
    },
    handleClickToolBar(e) {
      if (e.grid.getSelectedRecords().length <= 0 && e.toolbar.id === 'export') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      } else {
        // 调用导出方法·
        let _selectRows = e.grid.getSelectedRecords()
        console.log(_selectRows, 7777)
      }
    },
    // 请求详情
    // 三方公用一个详情接口
    getDetails() {
      let _that = this
      let obj = {
        id: this.$route.query.id
      }
      this.$API.purchaseCoordination.postBuyerWarehousingReturnDetail(obj).then((res) => {
        if (res.code === 200) {
          headerInfo.siteCode = res.data.siteCode
          headerInfo.siteCode = res.data.siteCode
          headerInfo.supplierCode = res.data.supplierCode
          headerInfo.supplierName = res.data.supplierName
          headerInfo.supplier = res.data?.supplierCode + '-' + res.data?.supplierName
          headerInfo.vmiWarehouseCode = res.data.vmiWarehouseCode
          _that.pageConfig[0].grid.dataSource = res.data.vmiOrderItemResponses
        }
      })
    },
    // 确认
    confirm() {
      // let _that = this
      let obj = {
        ids: []
      }
      obj.ids.push(this.$route.query.id)
      this.$API.purchaseCoordination.postPurchaseStockImportConfirm(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('确认成功'), type: 'success' })
          this.goBack()
        } else {
          this.$toast({ content: this.$t('确认失败'), type: 'error' })
        }
      })
    }
  },

  deactivated() {
    headerInfoInit()
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
.titleColor {
  color: #00469c;
}
</style>
