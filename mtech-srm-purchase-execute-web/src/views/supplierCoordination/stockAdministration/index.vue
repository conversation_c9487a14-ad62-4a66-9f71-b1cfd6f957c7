// VMI库存调整管理
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-name="downTemplateName"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { columnData, columnData2 } from './config/index.js'
import { VmiOrderType, detailStatusType } from './config/constant'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  data() {
    return {
      downTemplateParams: {
        flag: 0
      }, // 下载模板参数
      requestUrls: {
        templateUrlPre: 'supplierCoordination',
        templateUrl: 'exportExcel',
        uploadUrl: 'importExcel'
      }, // 上传下载接口地址
      pageConfig: [],
      addDialogShow: false,
      dialogData: null,
      downTemplateName: null
    }
  },
  mounted() {
    if (this.$route.query.from === 'mytodo') {
      this.pageConfig = [
        {
          title: this.$t('头视图'),
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'create',
                  icon: 'icon_solid_Createorder',
                  permission: ['O_02_1094'],
                  title: this.$t('手工录入供应商库存')
                },
                {
                  id: 'upload',
                  icon: 'icon_solid_Import',
                  title: this.$t('供应商库存导入')
                },
                {
                  id: 'synchronous',
                  icon: 'icon_table_restart',
                  title: this.$t('同步WMS')
                }
              ],
              [
                'Filter',
                {
                  id: 'export',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                },
                'Refresh',
                'Setting'
              ]
            ]
          },
          gridId: 'f66e4798-3709-4ec9-a06d-cdefae3e55ae',
          grid: {
            columnData: columnData,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiAllocationOrder/supplier-page-query',
              recordsPosition: 'data.records',
              defaultRules: JSON.parse(sessionStorage.getItem('todoDetail')).defaultRules
            },
            frozenColumns: 1
          }
        },
        {
          useToolTemplate: false,
          useCombinationSelection: false,
          title: this.$t('明细列表'),
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [],
              [
                'Filter',
                {
                  id: 'export2',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                },
                'Refresh',
                'Setting'
              ]
            ]
          },
          // gridId: "e2ae7eab-f90b-43c7-9e16-713a0dd51ccd",
          gridId: '3a13fd61-769f-4b68-a1be-2bb7fcb67ffd',
          grid: {
            columnData: columnData2,
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiAllocationOrder/supplier-page-query-ext-detail',
              recordsPosition: 'data.records'
            },
            frozenColumns: 1
          }
        }
      ]
    } else {
      this.pageConfig = [
        {
          title: this.$t('头视图'),
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'create',
                  icon: 'icon_solid_Createorder',
                  permission: ['O_02_1094'],
                  title: this.$t('手工录入供应商库存')
                },
                {
                  id: 'upload',
                  icon: 'icon_solid_Import',
                  title: this.$t('供应商库存导入')
                },
                {
                  id: 'synchronous',
                  icon: 'icon_table_restart',
                  title: this.$t('同步WMS')
                }
              ],
              [
                'Filter',
                {
                  id: 'export',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                },
                'Refresh',
                'Setting'
              ]
            ]
          },
          gridId: 'f66e4798-3709-4ec9-a06d-cdefae3e55ae',
          grid: {
            columnData: columnData,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiAllocationOrder/supplier-page-query',
              recordsPosition: 'data.records',
              defaultRules: []
            },
            frozenColumns: 1
          }
        },
        {
          useToolTemplate: false,
          useCombinationSelection: false,
          title: this.$t('明细列表'),
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [],
              [
                'Filter',
                {
                  id: 'export2',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                },
                'Refresh',
                'Setting'
              ]
            ]
          },
          // gridId: "e2ae7eab-f90b-43c7-9e16-713a0dd51ccd",
          gridId: '3a13fd61-769f-4b68-a1be-2bb7fcb67ffd',
          grid: {
            columnData: columnData2,
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiAllocationOrder/supplier-page-query-ext-detail',
              recordsPosition: 'data.records'
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  methods: {
    // 跳转详情
    handleClickCellTitle(args) {
      const { field, data } = args
      if (field === 'vmiOrderCode') {
        // FIXME: 各个单据类型的页面未开发完 先不允许点击进入详情
        const { vmiOrderType, id, status, vmiOrderId } = data || {}
        // status: 0-新建，1-待确认，2-已接受，8-已完成，9-已取消
        // vmiOrderType： 单据类型，根据路由不同，可创建或编辑的不同
        // details: add-新建，edit-编辑，beConfirm-待确认，detail-纯详情
        const query = {
          details: '',
          orderStatus: vmiOrderType,
          id: id
          // detailsType: "",
        }

        if (status == 1) {
          // 待确认状态
          query.details = detailStatusType.beConfirm // 待确认
        } else {
          query.details = detailStatusType.detail // 详情,纯查看
        }

        // console.log("准备处理跳转了");

        // 根据类型判断   类型字段暂时没有返回   先用状态代替（自定义代替）
        if (vmiOrderType === VmiOrderType.import) {
          // 供应商库存导入
          if (status == 0) query.details = detailStatusType.edit // 编辑
          this.$router.push({ name: `supplier-stock-build`, query })
        } else if (vmiOrderType === VmiOrderType.replacement) {
          // VMI物料替换单--第三方物流创建、供/采确认（供方确认入口只有供方库存调整管理）
          this.$router.push({ name: `supplier-stock-replace`, query })
        } else if (vmiOrderType === VmiOrderType.inventory) {
          // VMI库存调拨单--第三方物流创建、供/采确认（供方确认入口只有供方库存调整管理）
          this.$router.push({ name: `supplier-stock-allocation`, query })
        } else if (vmiOrderType === VmiOrderType.delivery) {
          // VMI仓送货单
          query.status = status
          query.create = status == 0 ? 1 : 0 // create：1-编辑；0-详情
          this.$router.push({ name: `supplier-warehousing-create`, query })
        } else if (vmiOrderType === VmiOrderType.return) {
          // VMI退货单  第三方物流创建、供/采确认
          query.id = vmiOrderId ?? data.id
          query.status = status
          this.$router.push({ name: `supplier-out-details`, query })
        }
      }
    },
    // 头部操作
    handleClickToolBar(e) {
      if (e.toolbar.id == 'upload') {
        this.showUploadExcel(true)
      } else if (e.toolbar.id === 'export2') {
        let obj = JSON.parse(
          sessionStorage.getItem('3a13fd61-769f-4b68-a1be-2bb7fcb67ffd')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnData2.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        const queryBuilderRules =
          this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        const params = {
          page: { current: 1, size: 10000 },
          ...queryBuilderRules
        } // 筛选条件
        this.$store.commit('startLoading')
        this.$API.supplierCoordination.supplierExcelExport(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
      } else if (e.toolbar.id === 'export') {
        let obj = JSON.parse(
          sessionStorage.getItem('f66e4798-3709-4ec9-a06d-cdefae3e55ae')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnData.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        // 供应商导出
        let params = {
          page: { current: 1, size: 0 },
          sortedColumnStr: field.toString()
        }
        this.$store.commit('startLoading')
        this.$API.supplierCoordination.supplierHeaderStockExport(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
      } else if (e.toolbar.id === 'create') {
        let obj = {
          orderStatus: 4,
          details: detailStatusType.add
        }
        // 供应商导入
        this.$router.push({ name: `supplier-stock-build`, query: obj })
      } else if (e.toolbar.id == 'synchronous') {
        const _records = e.grid.getSelectedRecords()
        if (_records.length <= 0 || _records.length > 1) {
          this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
          return
        }
        this.synchronousWms(_records[0])
      } else if (e.toolbar.id === 'refreshDataByLocal') {
        // 刷新
      } else if (e.grid.getSelectedRecords().length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
    },
    // 行内操作
    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.$dialog({
          data: {
            title: this.$t('删除'),
            message: this.$t('确认删除吗？')
          },
          success: () => {
            // 全类型的删除已经联调完接口了   暂时先放出导入导出的 有需要后续全部放开
            // this.delete(e.data.id);
            this.delete(e.data.vmiOrderType, { ids: [e.data.id] })
          }
        })
      } else if (e.tool.id == 'accept') {
        this.$dialog({
          data: {
            title: this.$t('接受'),
            message: this.$t('确认接受吗？')
          },
          success: () => {
            this.accept(e.data.id, e.data.vmiOrderType)
          }
        })
      } else if (e.tool.id == 'cancel') {
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消吗？')
          },
          success: () => {
            this.cancel(e.data.id, e.data.vmiOrderType)
          }
        })
      } else if (e.tool.id == 'submit') {
        this.$dialog({
          data: {
            title: this.$t('提交'),
            message: this.$t('确认提交吗？')
          },
          success: () => {
            this.submit(e.data.id, e.data.vmiOrderType)
          }
        })
      }
    },
    // 删除
    delete(type, obj) {
      console.log(type, obj)

      this.$API.supplierCoordination[`postSupplierStockImportDelete${type}`](obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('删除失败'), type: 'error' })
        }
      })
    },
    // 同步WMS  3 : 退货  6: 替换
    synchronousWms(data) {
      if (data.wmsSyncStatus === 1) {
        this.$toast({
          content: this.$t('入库单已成功同步WMS，无需重复同步'),
          type: 'warning'
        })
        return
      }
      if (data.vmiOrderType === 3 || data.vmiOrderType === 6) {
        this.$API.purchaseCoordination[
          data.vmiOrderType === 3 ? 'returnSynchronousWms' : 'replaceSynchronousWms'
        ]({ id: data.id }).then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('同步成功'), type: 'success' })
            this.refreshColumns()
          } else {
            this.$toast({ content: this.$t('同步失败'), type: 'warning' })
          }
        })
      } else {
        this.$toast({
          content: this.$t('请选择退货单或物料替换单'),
          type: 'warning'
        })
      }
    },
    // 刷新页面
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },
    // 接收 采方，供方公用一个接口
    accept(data, valType) {
      let apiInterface = ''
      if (valType == 5) {
        apiInterface = 'purchaseAllocationReceive'
      } else if (valType == 6) {
        apiInterface = 'purchaseReplaceReceive'
      }
      let obj = {
        ids: []
      }
      obj.ids.push(data)
      this.$API.purchaseCoordination[apiInterface](obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('提交失败'), type: 'error' })
        }
      })
    },
    // 取消 三方共用一个接口
    cancel(data, valType) {
      let apiInterface = ''
      if (valType == 5) {
        apiInterface = 'purchaseAllocationBackOff'
      } else if (valType == 6) {
        apiInterface = 'purchaseReplaceBackOff'
      }
      let obj = {
        ids: []
      }
      obj.ids.push(data)
      this.$API.purchaseCoordination[apiInterface](obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('取消成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('取消失败'), type: 'error' })
        }
      })
    },

    // 提交
    submit(data) {
      let obj = {
        ids: []
      }
      obj.ids.push(data)
      this.$API.supplierCoordination.postSupplierStockImportSubmit(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('提交失败'), type: 'error' })
        }
      })
    },
    upExcelConfirm() {
      this.refreshColumns()
      this.showUploadExcel(false)
    },
    // 刷新页面
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = [] // 清空数据
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('todoDetail')
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
