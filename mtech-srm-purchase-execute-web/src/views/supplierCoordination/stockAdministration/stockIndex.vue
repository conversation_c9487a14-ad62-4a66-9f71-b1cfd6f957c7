//供方 VMI库存管理
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config/stockIndex.js'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  data() {
    return {
      pageConfig: [
        {
          title: this.$t('汇总列表'),
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          activatedRefresh: false,

          toolbar: {
            tools: [
              [
                {
                  id: 'checkEditInfo',
                  title: this.$t('查看变更记录'),
                  icon: 'icon_solid_Createorder'
                }
              ],
              [
                'Filter',
                {
                  id: 'export',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                },
                'Refresh',
                'Setting'
              ]
            ]
          },
          gridId: '5ef7517a-3f6d-41dc-8c11-5732852cdb6c',
          grid: {
            columnData: columnData,
            // lineIndex: 1,
            // autoWidthColumns: columnData.length + 1,

            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmi_stock/stock-supplier-page-query',
              recordsPosition: 'data.records',
              defaultRules: []
            },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      console.log('exexex', e)
      const { grid } = e
      if (e.toolbar.id === 'export') {
        this.downloadTemplate()
        return
      }
      const records = grid.getSelectedRecords()
      if (!records || records.length === 0) {
        this.$toast({
          type: 'warning',
          content: this.$t('请选择一条数据')
        })
        return
      }
      if (records.length > 1) {
        this.$toast({
          type: 'warning',
          content: this.$t('只能查看单条数据的变更记录')
        })
        return
      }
      // if (![1, '1'].includes(records[0].vmiWarehouseType)) {
      //   this.$toast({
      //     type: 'warning',
      //     content: this.$t('请选择一条VMI仓类型为原厂的数据！')
      //   })
      //   return
      // }
      if (e.toolbar.id === 'checkEditInfo') {
        sessionStorage.setItem('checkEditHeaderInfo', JSON.stringify(records[0]))
        this.$dialog({
          modal: () => import('@/views/supplierCoordination/stockAdministration/checkEditInfo.vue'),
          data: {
            headerTitle: this.$t('查看变更记录')
          },
          success: () => {}
        })
      }
    },
    downloadTemplate() {
      let obj = JSON.parse(
        sessionStorage.getItem('5ef7517a-3f6d-41dc-8c11-5732852cdb6c')
      )?.visibleCols
      let field = []
      if (obj !== undefined && obj.length) {
        obj.forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      } else {
        columnData.forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      }
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.purchaseCoordination.vmiSupStockExport(params, field).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
        this.$store.commit('endLoading')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.set-country {
  height: 100%;
}
/deep/ .e-grid td.e-active {
  z-index: auto;
}
</style>
