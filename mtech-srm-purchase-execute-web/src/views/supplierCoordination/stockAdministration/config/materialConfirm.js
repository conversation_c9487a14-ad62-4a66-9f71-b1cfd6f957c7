import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '100',
    field: 'rowNum',
    headerText: i18n.t('行号'),
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return Number(+row.index + 1)
      }
    }
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'sourceStockType',
    headerText: i18n.t('移出库存状态')
  },
  // {
  //   field: "batchCode",
  //   headerText: i18n.t("批次/卷号"),
  // },
  {
    field: 'count',
    headerText: i18n.t('调拨数量')
  },

  {
    field: 'enterStatus',
    headerText: i18n.t('移入库存状态')
  },
  {
    width: 0,
    field: 'itemUnit',
    headerText: i18n.t('单位')
  },
  {
    field: 'itemUnitDescription',
    headerText: i18n.t('单位')
  },
  {
    width: 0,
    field: 'purchaseGroupCode',
    headerText: i18n.t('采购组')
  },
  {
    field: 'purchaseGroupName',
    headerText: i18n.t('采购组')
  },
  {
    field: 'remark',
    headerText: i18n.t('行备注')
  }
]
