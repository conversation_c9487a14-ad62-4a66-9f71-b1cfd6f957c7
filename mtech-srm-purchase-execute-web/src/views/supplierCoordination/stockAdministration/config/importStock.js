import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '160',
    field: 'action',
    headerText: i18n.t('操作方式')
  },
  {
    width: '120',
    field: 'createUserName',
    headerText: i18n.t('操作人')
  },
  {
    width: '180',
    field: 'createTime',
    headerText: i18n.t('操作时间')
  },
  {
    width: '250',
    field: 'changeContent',
    headerText: i18n.t('变更内容')
  },
  {
    width: '120',
    field: 'preCount',
    headerText: i18n.t('变更前')
  },
  {
    width: '120',
    field: 'count',
    headerText: i18n.t('变更后')
  },
  {
    field: 'remark',
    headerText: i18n.t('备注'),
    width: 'auto'
  }
]
