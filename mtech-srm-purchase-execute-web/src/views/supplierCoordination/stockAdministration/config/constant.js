import { i18n } from '@/main.js'
// 状态 状态:1-新建,2-发货中,3-已完成,4-已取消,5-已关闭
export const Status = {
  new: 0, // 新建
  shipping: 1, // 待确认
  completed: 2, // 已接收
  cancelled: 8, // 已完成
  closed: 9 // 已取消
}
// 状态 text
export const StatusText = {
  [Status.new]: i18n.t('新建'),
  [Status.shipping]: i18n.t('待确认'),
  [Status.completed]: i18n.t('已接收'),
  [Status.cancelled]: i18n.t('已完成'),
  [Status.closed]: i18n.t('已取消')
}
// 状态 对应的 css class
export const StatusCssClass = {
  [Status.new]: 'col-active',
  [Status.shipping]: 'col-active',
  [Status.completed]: 'col-active',
  [Status.cancelled]: 'col-active',
  [Status.closed]: 'col-inactive'
}

// 详情页面的状态
export const detailStatusType = {
  add: '0', // 新建
  edit: '1', // 编辑
  beConfirm: '2', // 待确认
  detail: '3' // 详情
}

// 单据类型 1 VMI仓送货单 2 VMI领料单 3 VMI退货单 4 供应商库存导入 5 VMI库存调拨单 6 VMI物料替换单
export const VmiOrderType = {
  delivery: 1, // VMI仓送货单
  picking: 2, // VMI领料单
  return: 3, // VMI退货单
  import: 4, // 供应商库存导入
  inventory: 5, // VMI库存调拨单
  replacement: 6 // VMI物料替换单
}
// 字段类型 对应的 text
export const VmiOrderTypeText = {
  [VmiOrderType.delivery]: i18n.t('VMI仓送货单'), // 入库单 供方创建、采方/物流方确认
  [VmiOrderType.picking]: i18n.t('VMI领料单'), // 现在不用（采方创建，供方/物流方确认）
  [VmiOrderType.return]: i18n.t('VMI退货单'), // 第三方物流创建、供/采确认
  [VmiOrderType.import]: i18n.t('供应商库存导入'), // 供方创建，无需确认
  [VmiOrderType.inventory]: i18n.t('VMI库存调拨单'), // 第三方物流创建、供/采确认
  [VmiOrderType.replacement]: i18n.t('VMI物料替换单') // 第三方物流创建、供/采确认
}
// 字段类型 对应的 Options
export const VmiOrderTypeOptions = [
  {
    value: VmiOrderType.delivery,
    text: VmiOrderTypeText[VmiOrderType.delivery],
    cssClass: ''
  },
  {
    value: VmiOrderType.picking,
    text: VmiOrderTypeText[VmiOrderType.picking],
    cssClass: ''
  },
  {
    value: VmiOrderType.return,
    text: VmiOrderTypeText[VmiOrderType.return],
    cssClass: ''
  },
  {
    value: VmiOrderType.import,
    text: VmiOrderTypeText[VmiOrderType.import],
    cssClass: ''
  },
  {
    value: VmiOrderType.inventory,
    text: VmiOrderTypeText[VmiOrderType.inventory],
    cssClass: ''
  },
  {
    value: VmiOrderType.replacement,
    text: VmiOrderTypeText[VmiOrderType.replacement],
    cssClass: ''
  }
]
