export const headerInfo = {
  status: null,
  vmiOrderCode: null,
  createUserName: null,
  createTime: null,
  factory: null,
  siteCode: null, // 工厂
  siteName: null,
  supplier: null, // 供应商
  supplierCode: null,
  supplierName: null,
  vmiWarehouseCode: null,
  vmiWarehouseName: null,
  vmiWarehouseType: null,
  customerEnterpriseId: null,
  remark: null,
  accept: false,
  confirm: false,
  isSubmit: true,
  isDisabled: false, //true不可编辑  false可编辑
  isDetails: false,
  hasDetailRow: false // 是否有明细行数据，如果有，就不可修改头部数据
}

export function headerInfoInit() {
  headerInfo.status = null
  headerInfo.vmiOrderCode = null
  headerInfo.createUserName = null
  headerInfo.createTime = null
  headerInfo.factory = null
  headerInfo.siteCode = null
  headerInfo.siteName = null
  headerInfo.supplier = null
  headerInfo.supplierCode = null
  headerInfo.supplierName = null
  headerInfo.vmiWarehouseCode = null
  headerInfo.vmiWarehouseName = null
  headerInfo.customerEnterpriseId = null
  headerInfo.remark = null
  headerInfo.accept = false
  headerInfo.confirm = false
  headerInfo.isSubmit = true
  headerInfo.isDisabled = false //true不可编辑  false可编辑
  headerInfo.isDetails = false
  headerInfo.hasDetailRow = false
}

export function setHeaderInfo(data) {
  headerInfo.status = data?.status
  headerInfo.vmiOrderCode = data?.vmiOrderCode
  headerInfo.createUserName = data?.createUserName || null
  headerInfo.createTime = data?.createTime || null
  headerInfo.factory = data?.factory || null
  headerInfo.siteCode = data?.siteCode || null
  headerInfo.siteName = data?.siteName || null
  headerInfo.supplier = data?.supplierCode + '-' + data?.supplierName
  headerInfo.supplierCode = data?.supplierCode || null
  headerInfo.supplierName = data?.supplierName || null
  headerInfo.vmiWarehouseCode = data?.vmiWarehouseCode || null
  headerInfo.vmiWarehouseName = data?.vmiWarehouseName || null
  headerInfo.customerEnterpriseId = data?.customerEnterpriseId || null
  headerInfo.remark = data?.remark
  headerInfo.hasDetailRow = data?.vmiOrderItemResponses?.length ? true : false
  // headerInfo.accept = false;
  // headerInfo.confirm = false;
  // headerInfo.isSubmit = true;
  // headerInfo.isDisabled = false; //true不可编辑  false可编辑
  // headerInfo.isDetails = false;
}
