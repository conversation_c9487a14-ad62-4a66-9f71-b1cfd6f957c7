// import Vue from "vue";
import { i18n } from '@/main.js'
import { VmiOrderTypeOptions } from './constant'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName } from '@/utils/utils'

export const timeDate = (dataKey, hasTime) => {
  // const { dataKey, hasTime } = args;
  // console.log(args);
  const template = () => {
    return {
      template: Vue.component('date', {
        template: `<div><div>{{data[dataKey] | dateFormat}}</div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div></div>`,
        data: function () {
          return { data: {}, dataKey, hasTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}

// 库存调整管理
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'vmiOrderCode',
    headerText: i18n.t('单据号'),
    cellTools: []
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '120',
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('新建'),
        1: i18n.t('待确认'),
        2: i18n.t('已接收'),
        3: i18n.t('待WMS拣货'),
        4: i18n.t('待确认收货'),
        5: i18n.t('待WMS出库'),
        6: i18n.t('待确认替换'),
        7: i18n.t('待WMS替换'),
        8: i18n.t('已完成'),
        9: i18n.t('已取消')
      }
    },
    searchOptions: {
      elementType: 'multi-select',
      operator: 'in',
      multiple: true,

      dataSource: [
        { text: i18n.t('新建'), value: 0 },
        { text: i18n.t('待确认'), value: 1 },
        { text: i18n.t('已接收'), value: 2 },
        { text: i18n.t('待WMS拣货'), value: 3 },
        { text: i18n.t('待确认收货'), value: 4 },
        { text: i18n.t('待WMS出库'), value: 5 },
        { text: i18n.t('待确认替换'), value: 6 },
        { text: i18n.t('待WMS替换'), value: 7 },
        { text: i18n.t('已完成'), value: 8 },
        { text: i18n.t('已取消'), value: 9 }
      ]
    },
    cellTools: [
      {
        id: 'submit',
        // icon: "icon_solid_pushorder",
        title: i18n.t('提交'),
        visibleCondition: (data) => {
          if (data.status == 0 && data.vmiOrderType === 4) return true
        }
      },
      {
        id: 'delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          // if(data.status == 0 || data.status == 1) return true;//其他类型的删除接口已经对完了  后续需要可以放开
          if (data.vmiOrderType == 4 && (data.status == 0 || data.status == 1)) return true
        }
      },
      {
        id: 'accept',
        // icon: "icon_solid_pushorder",
        title: i18n.t('接收'), // VMI仓送货单和供应商导入类型的，不可做确认操作
        visibleCondition: (data) => {
          if ([5, 6].includes(data.vmiOrderType) && (data.status == 1 || data.status == 6))
            return true
        }
      },
      {
        id: 'cancel',
        // icon: "icon_solid_pushorder",
        title: i18n.t('取消'), // VMI仓送货单和供应商导入类型的，不可做确认操作
        visibleCondition: (data) => {
          if ([9].includes(data.vmiOrderType) && (data.status == 1 || data.status == 6)) return true
        }
      }
    ]
  },
  {
    width: '150',
    field: 'wmsSyncStatus',
    headerText: i18n.t('WMS同步状态'),
    allowEditing: false,
    searchOptions: {
      elementType: 'multi-select',
      operator: 'in',
      multiple: true,

      dataSource: [
        { text: i18n.t('未同步'), value: 0 },
        { text: i18n.t('同步成功'), value: 1 },
        { text: i18n.t('同步失败'), value: 2 }
      ]
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未同步'),
        1: i18n.t('同步成功'),
        2: i18n.t('同步失败')
      }
    }
  },
  {
    width: '150',
    field: 'wmsSyncInfo',
    headerText: i18n.t('WMS同步信息'),
    allowEditing: false
  },
  {
    width: '150', // 0-第三方物流，1-供方本厂
    field: 'vmiWarehouseType',
    headerText: i18n.t('VMI仓类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('SRM管理库存'),
        1: i18n.t('原厂'),
        2: i18n.t('WMS管理库存')
      }
    },
    searchOptions: {
      elementType: 'multi-select',
      operator: 'in',
      multiple: true,

      dataSource: [
        { text: i18n.t('SRM管理库存'), value: 0 },
        { text: i18n.t('原厂'), value: 1 },
        { text: i18n.t('WMS管理库存'), value: 2 }
      ]
    }
  },
  {
    width: '125',
    field: 'vmiOrderType',
    headerText: i18n.t('类型'),
    valueConverter: {
      type: 'map',
      map: VmiOrderTypeOptions
    },
    searchOptions: {
      elementType: 'multi-select',
      operator: 'in',
      multiple: true,

      dataSource: [
        { text: i18n.t('VMI入库单-待检'), value: 1 },
        { text: i18n.t('VMI入库单-合格'), value: 2 },
        { text: i18n.t('VMI入库单-不合格'), value: 3 },
        { text: i18n.t('VMI退货单'), value: 4 },
        { text: i18n.t('VMI库存调拨-调入'), value: 5 },
        { text: i18n.t('VMI库存调拨-调出'), value: 6 },
        { text: i18n.t('VMI物料替换-调入'), value: 7 },
        { text: i18n.t('VMI物料替换-调出'), value: 8 },
        { text: i18n.t('供应商库存导入'), value: 9 }
      ]
    }
  },
  {
    width: '260',
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    searchOptions: MasterDataSelect.factorySupplierAddress,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.siteCode, data?.siteName)
    }
  },
  {
    width: '0',
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    ignore: true
  },
  {
    width: '260',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓'),
    searchOptions: MasterDataSelect.vmiWarehouseSupplier,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.vmiWarehouseCode, data?.vmiWarehouseName)
    }
  },
  {
    width: '0',
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓名称'),
    ignore: true
  },
  {
    width: '260',
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    searchOptions: MasterDataSelect.supplierSu,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  },
  {
    width: '0',
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    ignore: true
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('制单日期'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    width: '150',
    field: 'confirmTime',
    headerText: i18n.t('单据接受日期'),
    template: timeDate('confirmTime', true),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    width: '110',
    field: 'createUserName',
    headerText: i18n.t('制单人')
  }
  // {
  //   width: "150",
  //   field: "VMIWarehouseCode",
  //   headerText: i18n.t("接收日期"),
  // },
]
export const columnData2 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'vmiOrderCode',
    headerText: i18n.t('单据号')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '90',
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('新建'),
        1: i18n.t('待确认'),
        2: i18n.t('已接收'),
        3: i18n.t('待WMS拣货'),
        4: i18n.t('待确认收货'),
        5: i18n.t('待WMS出库'),
        6: i18n.t('待确认替换'),
        7: i18n.t('待WMS替换'),
        8: i18n.t('已完成'),
        9: i18n.t('已取消')
      }
    },
    searchOptions: {
      elementType: 'multi-select',
      operator: 'in',
      multiple: true,

      dataSource: [
        { text: i18n.t('新建'), value: 0 },
        { text: i18n.t('待确认'), value: 1 },
        { text: i18n.t('已接收'), value: 2 },
        { text: i18n.t('待WMS拣货'), value: 3 },
        { text: i18n.t('待确认收货'), value: 4 },
        { text: i18n.t('待WMS出库'), value: 5 },
        { text: i18n.t('待确认替换'), value: 6 },
        { text: i18n.t('待WMS替换'), value: 7 },
        { text: i18n.t('已完成'), value: 8 },
        { text: i18n.t('已取消'), value: 9 }
      ]
    }
  },
  {
    width: '115',
    field: 'vmiOrderItemType',
    headerText: i18n.t('类型'),

    searchOptions: {
      elementType: 'multi-select',
      operator: 'in',
      multiple: true,

      dataSource: [
        { text: i18n.t('VMI入库单-待检'), value: 1 },
        { text: i18n.t('VMI入库单-合格'), value: 2 },
        { text: i18n.t('VMI入库单-不合格'), value: 3 },
        { text: i18n.t('VMI退货单'), value: 4 },
        { text: i18n.t('VMI库存调拨-调入'), value: 5 },
        { text: i18n.t('VMI库存调拨-调出'), value: 6 },
        { text: i18n.t('VMI物料替换-调入'), value: 7 },
        { text: i18n.t('VMI物料替换-调出'), value: 8 },
        { text: i18n.t('供应商库存导入'), value: 9 }
      ]
    },
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('VMI入库单-待检'), // 没有提交和取消
        2: i18n.t('VMI入库单-合格'),
        3: i18n.t('VMI入库单-不合格'),
        4: i18n.t('VMI退货单'),
        5: i18n.t('VMI库存调拨-调入'),
        6: i18n.t('VMI库存调拨-调出'),
        7: i18n.t('VMI物料替换-调入'),
        8: i18n.t('VMI物料替换-调出'),
        9: i18n.t('供应商库存导入')
      }
    }
  },
  {
    width: '260',
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    searchOptions: MasterDataSelect.factorySupplierAddress,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.siteCode, data?.siteName)
    }
  },
  {
    width: '0',
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    ignore: true
  },
  {
    width: '95',
    field: 'purchaseGroupCode',
    headerText: i18n.t('采购组'),
    searchOptions: {
      ...MasterDataSelect.businessCodeName
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.purchaseGroupCode}}-{{data.purchaseGroupName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '0',
    field: 'purchaseGroupName',
    headerText: i18n.t('采购组名称'),
    ignore: true
  },
  {
    width: '260',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓'),
    searchOptions: MasterDataSelect.vmiWarehouseSupplier,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.vmiWarehouseCode, data?.vmiWarehouseName)
    }
  },
  {
    width: '0',
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓名称'),
    ignore: true
  },
  {
    width: '260',
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    searchOptions: MasterDataSelect.supplierSU,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  },
  {
    width: '0',
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    ignore: true
  },
  {
    width: '80',
    field: 'createUserName',
    headerText: i18n.t('制单人')
  },
  {
    width: '130',
    field: 'itemCode', //supplierCode
    headerText: i18n.t('物料编码')
  },
  {
    width: '130',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '115',
    field: 'stockTypeDesc',
    headerText: i18n.t('库存类型')
    // valueConverter: {
    //   type: 'map',
    //   map: {
    //     1: i18n.t('VMI入库单-待检'), // 没有提交和取消
    //     2: i18n.t('VMI入库单-合格'),
    //     3: i18n.t('VMI入库单-不合格'),
    //     4: i18n.t('VMI退货单'),
    //     5: i18n.t('VMI库存调拨-调入'),
    //     6: i18n.t('VMI库存调拨-调出'),
    //     7: i18n.t('VMI物料替换-调入'),
    //     8: i18n.t('VMI物料替换-调出'),
    //     9: i18n.t('供应商库存导入')
    //   }
    // }
  },
  {
    width: '150',
    field: 'count',
    headerText: i18n.t('数量')
  },
  {
    width: '150',
    field: 'checkCount',
    headerText: i18n.t('实收数量')
  },
  {
    width: '150',
    field: 'itemUnit',
    headerText: i18n.t('单位')
  },
  {
    width: '150',
    field: 'vmiWarehouseAddress',
    headerText: i18n.t('送货地址')
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('制单人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('制单日期'),
    template: timeDate('createTime', true),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    width: '150',
    field: 'confirmTime',
    headerText: i18n.t('单据接受日期'),
    template: timeDate('confirmTime', true),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
// 库存调整管理 - 新建供应商库存
import selectedwuliaoCode from '../edit/wuliao.vue' // 物料、sku
import onlyShowInput from '../edit/onlyShowInput.vue' // 物料、sku
export const addSupplyStockCols = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false //隐藏在列选择器中的过滤
  },
  {
    width: 0,
    field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    headerText: i18n.t('addId主键'),
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。
    allowEditing: false
  },
  {
    field: 'rowNum',
    headerText: i18n.t('行号'),
    allowEditing: false
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    editTemplate: () => {
      return {
        template: selectedwuliaoCode
      }
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    editTemplate: () => {
      return { template: onlyShowInput }
    }
  },
  {
    field: 'count',
    headerText: i18n.t('库存数量'),
    editType: 'numericedit', //默认编辑类型之number
    edit: {
      params: {
        min: 0
      }
    }
  },
  {
    field: 'itemUnitDescription',
    headerText: i18n.t('单位'),
    editTemplate: () => {
      return { template: onlyShowInput }
    }
  },
  {
    width: 0,
    field: 'itemUnit',
    headerText: i18n.t('单位'),
    editTemplate: () => {
      return { template: onlyShowInput }
    }
  },
  {
    field: 'purchaseGroupName',
    headerText: i18n.t('采购组'),
    allowEditing: false,
    searchOptions: {
      ...MasterDataSelect.businessCodeName,
      renameField: 'purchaseGroupCode'
    },
    editTemplate: () => {
      return { template: onlyShowInput }
    }
  },
  {
    width: 0,
    field: 'purchaseGroupCode',
    headerText: i18n.t('采购组编码'),
    searchOptions: {
      ...MasterDataSelect.businessCodeName,
      renameField: 'purchaseGroupCode'
    },
    editTemplate: () => {
      return { template: onlyShowInput }
    }
  }
]
