// 供方 原材料调拨 VMI供应商确认
<template>
  <div class="full-height pt20">
    <top-info
      class="flex-keep"
      @goBack="goBack"
      @acceptBtn="acceptBtn"
      @backBtn="backBtn"
    ></top-info>
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config/materialConfirm.js'
import TopInfo from './components/topInfoNew.vue'
import { headerInfoInit, setHeaderInfo } from './config/variable'
export default {
  components: {
    TopInfo
  },
  data() {
    return {
      // 头部form信息
      // headerInfo: null,
      // headerInfo: {
      //   status: 1,
      //   createUserName: "jcs",
      //   createTime: "2022-03-02",
      //   factory: this.$t("京东工厂"),
      //   supplierCode: "0001",
      //   companyDescribe: this.$t("描述"),
      //   warehouseCode: "0033333",
      //   warehouseDescribe: this.$t("描述"),
      //   remark: "",
      //   accept: true,
      //   confirm: true,
      //   isDisabled: true,
      // },
      pageConfig: [
        {
          title: this.$t('汇总列表'),
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: {
            tools: [[], []]
          },
          grid: {
            columnData: columnData,
            dataSource: [],
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },
  watch: {
    $route(to) {
      // 如果是返回列表，那么就移除dom，防止下拉组件多次触发change事件
      if (to.name == 'supplier-stock-index') {
        headerInfoInit()
      }
    }
  },
  created() {
    this.getDetails()
  },
  methods: {
    switchNumber(data) {
      let statusType = ''
      switch (data) {
        case 1:
          statusType = this.$t('VMI仓送货单')
          break
        case 2:
          statusType = this.$t('VMI领料单')
          break
        case 3:
          statusType = this.$t('VMI退货单')
          break
        case 4:
          statusType = this.$t('供应商库存导入')
          break
        case 5:
          statusType = this.$t('VMI库存调拨单')
          break
        case 6:
          statusType = this.$t('VMI物料替换单')
          break
        default:
          break
      }
      return statusType
    },
    switchNumber2(data) {
      let statusType = ''
      switch (data) {
        case 0:
          statusType = this.$t('合格库存')
          break
        case 1:
          statusType = this.$t('待检库存')
          break
        case 3:
          statusType = this.$t('不合格库存')
          break
        default:
          break
      }
      return statusType
    },
    // 三方公用一个详情接口
    getDetails() {
      let _that = this
      let obj = {
        id: this.$route.query.id
      }
      this.$API.purchaseCoordination.postBuyerWarehousingReturnDetail(obj).then((res) => {
        if (res.code === 200) {
          setHeaderInfo(res.data)
          _that.pageConfig[0].grid.dataSource = res.data.vmiOrderItemResponses
          // _that.gridStatus = true
          _that.pageConfig[0].grid.dataSource.forEach((item) => {
            item.enterStatus = this.switchNumber2(item.stockType)
            item.sourceStockType = this.switchNumber2(item.sourceStockType)
          })
        } else {
          // _that.gridStatus = true
        }
      })
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    // 接受 采访，供方共用一个接口
    acceptBtn() {
      let apiInterface = ''
      if (this.$route.query.orderStatus == 5) {
        apiInterface = 'purchaseAllocationReceive'
      } else if (this.$route.query.orderStatus == 6) {
        apiInterface = 'purchaseReplaceReceive'
      }
      let obj = {
        ids: []
      }
      obj.ids.push(this.$route.query.id)
      this.$API.purchaseCoordination[apiInterface](obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('接受成功'), type: 'success' })
          this.goBack()
        } else {
          this.$toast({ content: this.$t('接受失败'), type: 'error' })
        }
      })
    },
    // 退回 采访，供方共用一个接口
    backBtn() {
      let apiInterface = ''
      if (this.$route.query.orderStatus == 5) {
        apiInterface = 'purchaseAllocationBackOff'
      } else if (this.$route.query.orderStatus == 6) {
        apiInterface = 'purchaseReplaceBackOff'
      }
      let obj = {
        ids: []
      }
      obj.ids.push(this.$route.query.id)
      this.$API.purchaseCoordination[apiInterface](obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('回退成功'), type: 'success' })
          this.goBack()
        } else {
          this.$toast({ content: this.$t('回退失败'), type: 'error' })
        }
      })
    },
    handleClickToolBar(e) {
      if (e.grid.getSelectedRecords().length <= 0 && e.toolbar.id === 'export') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      } else {
        // 调用导出方法·
        let _selectRows = e.grid.getSelectedRecords()
        console.log(_selectRows, 7777)
        // debugger;
      }
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
.titleColor {
  color: #00469c;
}
</style>
