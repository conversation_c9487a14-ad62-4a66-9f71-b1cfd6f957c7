// VMI 供方 领料管理
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :current-tab="0"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <Dialog ref="configListDialog" @dilogData="dilogData"></Dialog>
  </div>
</template>

<script>
import { columnObj } from './config/index.js'
import Dialog from './components/dialog.vue'
export default {
  components: {
    Dialog
  },
  data() {
    return {
      pageConfig: [
        {
          // useBaseConfig: true, // 使用组件中的toolbar配置
          // useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'add',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('新增'),
                  permission: ['O_02_1095']
                },
                {
                  id: 'delete',
                  icon: 'icon_solid_Delete',
                  title: this.$t('删除'),
                  permission: ['O_02_1096']
                },
                {
                  id: 'activate',
                  icon: 'icon_list_enable',
                  title: this.$t('启用'),
                  permission: ['O_02_1097']
                },
                {
                  id: 'prohibit',
                  icon: 'icon_list_disable',
                  title: this.$t('禁止'),
                  permission: ['O_02_1098']
                }
              ],
              [
                'Filter',
                // {
                //   id: "export",
                //   icon: "icon_solid_Createorder",
                //   title: this.$t("导出"),
                // },
                'Refresh',
                'Setting'
              ]
            ]
          },
          grid: {
            columnData: columnObj,
            dataSource: [],
            asyncConfig: {
              url: this.$API.supplierCoordination.postVMIPagedQuery //供方退货接口
              // recordsPosition: "data",
            },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    // 跳转详情
    handleClickCellTitle() {
      // let obj = {
      //   tabIndex: "",
      // };
      // if (e.tabIndex === 0) {
      //   obj.tabIndex = 0;
      // } else {
      //   obj.tabIndex = 1;
      // }
      // if (e.field === "orderNumber") {
      //   this.redirectPage("purchase-execute/supplier-picking-details", obj);
      // }
    },
    // 头部跳转
    handleClickToolBar(e) {
      const selectRows = e.grid.getSelectedRecords()
      if (e.toolbar.id === 'add') {
        this.handleAdd()
        return
      } else if (selectRows.length > 0 && e.toolbar.id === 'delete') {
        // 删除
        this.$dialog({
          data: {
            title: this.$t('删除'),
            message: this.$t('确认删除吗？')
          },
          success: () => {
            let ids = selectRows.map((item) => item.id)
            this.$API.supplierCoordination.postBuyerBatchDelete({ ids }).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('删除成功'),
                  type: 'success'
                })
                // 刷新当前 Grid
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          }
        })
      } else if (selectRows.length > 0 && e.toolbar.id === 'activate') {
        // 启用
        this.$dialog({
          data: {
            title: this.$t('启用'),
            message: this.$t('确认启用吗？')
          },
          success: () => {
            this.handleEnable(selectRows)
          }
        })
      } else if (selectRows.length > 0 && e.toolbar.id === 'prohibit') {
        this.$dialog({
          data: {
            title: this.$t('禁用'),
            message: this.$t('确认禁用吗？')
          },
          success: () => {
            this.handleDisable(selectRows)
          }
        })
        // } else if (selectRows.length > 0 && e.toolbar.id === "export") {
        // 导出
      } else if (e.toolbar.id === 'refreshDataByLocal') {
        // 调用刷新方法
      } else if (selectRows.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
    },
    // 行内的按钮
    handleClickCellTool(e) {
      // console.log(e.data,"999999999");
      if (e.tool.id == 'edit') {
        this.handleEdit(e.data)
      } else if (e.tool.id == 'delete') {
        // 删除
        this.$dialog({
          data: {
            title: this.$t('删除'),
            message: this.$t('确认删除吗？')
          },
          success: () => {
            this.delete(e.data.id)
          }
        })
        // 启用
      } else if (e.tool.id == 'activate') {
        this.$dialog({
          data: {
            title: this.$t('启用'),
            message: this.$t('确认启用吗？')
          },
          success: () => {
            this.handleEnable([e.data])
          }
        })
        // 禁用
      } else if (e.tool.id == 'prohibit') {
        this.$dialog({
          data: {
            title: this.$t('禁用'),
            message: this.$t('确认禁用吗？')
          },
          success: () => {
            this.handleDisable([e.data])
          }
        })
      }
    },
    // 路由跳转
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },
    // 接收弹窗编辑后的值
    dilogData() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    //新增
    handleAdd() {
      this.$refs.configListDialog.dialogInit({ title: this.$t('新增') })
    },
    //编辑
    handleEdit(row) {
      this.$refs.configListDialog.dialogInit({
        title: this.$t('编辑'),
        row: row
      })
    },
    //行内删除
    delete(data) {
      let obj = {
        ids: [data]
      }
      this.$API.supplierCoordination.postBuyerBatchDelete(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          // 刷新当前 Grid
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('删除失败'), type: 'error' })
        }
      })
    },
    //启用
    handleEnable(row) {
      let hasOne = row.some((item) => {
        return item.status === 1
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择禁用状态的行操作!'),
          type: 'warning'
        })
        return
      }
      let ids = row.map((item) => item.id)
      let params = {
        ids: ids,
        targetStatus: 1 //	目标状态:1-启用;0-禁用
      }
      this.$API.supplierCoordination.postBuyerBatchSwitchStatus(params).then(() => {
        this.$toast({
          content: this.$t('启用操作成功'),
          type: 'success'
        })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    // 禁用
    handleDisable(row) {
      let hasOne = row.some((item) => {
        return item.status === 0
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择已启用状态的行操作!'),
          type: 'warning'
        })
        return
      }
      let ids = row.map((item) => item.id)
      let params = {
        ids: ids,
        targetStatus: 0
      }
      this.$API.supplierCoordination.postBuyerBatchSwitchStatus(params).then(() => {
        this.$toast({
          content: this.$t('禁用操作成功'),
          type: 'success'
        })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    }
  }
}
</script>

<style style="scss" scoped>
.full-height {
  height: 100%;
}
</style>
