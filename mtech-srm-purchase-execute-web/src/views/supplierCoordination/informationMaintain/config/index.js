import { i18n } from '@/main.js'
export const columnObj = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'orderNumber',
    headerText: i18n.t('序号'),
    ignore: true,
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return Number(+row.index + 1)
      }
    },
    cellTools: [
      {
        id: 'edit',
        icon: 'a-icon_MultipleChoice_on',
        title: i18n.t('编辑'),
        permission: ['O_02_1099']
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        permission: ['O_02_1096']
      }
    ]
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('已禁用'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('已启用'), cssClass: 'col-active' }
      ]
    },
    cellTools: [
      {
        id: 'activate',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          let isShow = false
          if (data.status == 0) {
            isShow = true
          }
          return isShow
        }
      },
      {
        id: 'prohibit',
        icon: 'icon_list_disable',
        title: i18n.t('禁用'),
        visibleCondition: (data) => {
          let isShow = false
          if (data.status == 1) {
            isShow = true
          }
          return isShow
        }
      }
    ]
  },
  {
    width: '210',
    field: 'companyName', //companyName
    headerText: i18n.t('客户公司名称')
  },
  {
    width: '210',
    field: 'logisticsCompanyName', //logisticsCompanyName
    headerText: i18n.t('物流公司')
  },
  {
    width: '210',
    field: 'contactPerson', //contactPerson
    headerText: i18n.t('联系人')
  },
  {
    width: '210',
    field: 'contactPhone', //contactPhone
    headerText: i18n.t('联系方式')
  }
]
