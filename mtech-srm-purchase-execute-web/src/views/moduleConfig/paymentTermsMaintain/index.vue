<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>

    <add-dialog
      v-if="addDialogShow"
      :dialog-data="dialogData"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></add-dialog>
  </div>
</template>

<script>
import { columnData } from './config/index.js'
export default {
  components: {
    addDialog: require('./components/addDialog.vue').default
  },
  data() {
    return {
      pageConfig: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              ['Add', 'Delete'],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columnData,
            dataSource: [],
            asyncConfig: {
              url: '/masterDataManagement/tenant/user/paged-query'
            },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    handleClickToolBar(e) {
      console.log(e.grid.getSelectedRecords(), e)
      // if (
      //   e.grid.getSelectedRecords().length <= 0 &&
      //   !(
      //     e.toolbar.id == "Add" ||
      //     e.toolbar.id == "Filter" ||
      //     e.toolbar.id == "Refresh" ||
      //     e.toolbar.id == "Setting"
      //   )
      // ) {
      //   this.$toast({ content: this.$t("请先选择一行"), type: "warning" });
      //   return;
      // }
      let _id = []
      e.grid.getSelectedRecords().map((item) => _id.push(item.id))
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(_id)
      }
    },

    handleAdd() {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'add',
        requestUrl: 'addExchangeRateData'
      }
    },

    handleDelete() {
      this.$dialog({
        data: {
          title: this.$t('删除审批流'),
          message:
            '删除工作流将默认该节点工作流不启用，在相应节点则会默认审批自动通过。请慎重操作！'
        },
        success: () => {
          // this.$store.commit("startLoading");
          // this.$API.baseMainData
          //   .deleteExchangeRateData({ ids: ids })
          //   .then((res) => {
          //     this.$store.commit("endLoading");
          //     if (res.code == 200) {
          //       this.$toast({ content: this.$t("操作成功"), type: "success" });
          //       this.handleQueryReset();
          //     }
          //   })
          //   .catch(() => {
          //     this.$store.commit("endLoading");
          //   });
        }
      })
    },

    handleUpdateStatus(ids, flag, msg) {
      this.$dialog({
        data: {
          title: this.$t('操作'),
          message: msg
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.base.updateUserStatus({ ids: ids, statusId: flag }).then((res) => {
            this.$store.commit('endLoading')
            if (res.code == 200) {
              this.$refs.templateRef.refreshCurrentGridData()
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
            }
          })
        }
      })
    },

    handleClickCellTool(e) {
      if (e.tool.id == 'config') {
        this.$router.push('/pr-apply-detail?id=' + e.data.id)
      }
    },

    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    }
  }
}
</script>

<style lang="scss">
.dialog-payment .mt-form-item .mt-input-number {
  height: 30px;
  padding: 2px 10px;
}
</style>
