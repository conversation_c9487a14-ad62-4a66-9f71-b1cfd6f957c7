<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog dialog-payment"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="sourceCurrencyId" :label="$t('付款条件')">
        <mt-select
          v-model="addForm.projType"
          :data-source="projTypeList"
          :show-clear-button="true"
          :disabled="dialogData && dialogData.dialogType != 'add'"
          :placeholder="$t('请选择付款条件')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="rateDescription" :label="$t('排序')" class="full-width">
        <mt-input-number
          v-model="addForm.rateDescription"
          :show-clear-button="true"
          :multiline="true"
          :placeholder="$t('请输入排序')"
        ></mt-input-number>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
// import common from "@/utils/constant";
// import { formatDate, formatRules } from "@/utils/util";
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        targetCurrencyId: '',
        sourceCurrencyId: '',
        rate: null,
        effectiveTime: null,
        rateDescription: ''
      },
      rules: {
        // unitName: [
        //   { required: true, message: this.$t("请输入中文名称"), trigger: "blur" }
        // ]
      },
      projTypeList: [], // 业务类型
      fields: {
        dataSource: [
          {
            id: '01',
            name: 'Local Disk (C:)',
            expanded: true,
            subChild: [
              {
                id: '01-01',
                name: 'Program Files',
                subChild: [
                  { id: '01-01-01', name: 'Windows NT' },
                  { id: '01-01-02', name: 'Windows Mail' },
                  { id: '01-01-03', name: 'Windows Photo Viewer' }
                ]
              },
              {
                id: '01-02',
                name: 'Users',
                expanded: true,
                subChild: [
                  { id: '01-02-01', name: 'Smith' },
                  { id: '01-02-02', name: 'Public' },
                  { id: '01-02-03', name: 'Admin' }
                ]
              },
              {
                id: '01-03',
                name: 'Windows',
                subChild: [
                  { id: '01-03-01', name: 'Boot' },
                  { id: '01-03-02', name: 'FileManager' },
                  { id: '01-03-03', name: 'System32' }
                ]
              }
            ]
          }
        ]
      },

      currencyAll: [],
      currencyTarget: [],
      currencySource: []
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      let _addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
      if (_addForm.startTime && _addForm.endTime) {
        _addForm.effectiveTime = [_addForm.startTime, _addForm.endTime]
      }
      this.addForm = _addForm
    }
    // this.getAllCurrency();
    if (this.dialogData?.dialogType == 'add') {
      this.dialogTitle = this.$t('新增付款条件')
    } else {
      this.dialogTitle = this.$t('编辑付款条件')
    }
    // this.getRules();
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    vaulechange(value, text) {
      console.log('value :', value, 'text:', text)
    },

    getAllCurrency() {
      this.$API.baseMainData.getCurrencyDataAll().then((res) => {
        console.log(res)
        this.currencyAll = res.data
        this.currencyTarget = res.data
        this.currencySource = res.data
      })
    },

    handleCurrencyChange(val, keys) {
      // console.log(this.$t("改变后的值"), val, keys);
      let _currencyAll = JSON.parse(JSON.stringify(this.currencyAll))
      if (val?.value) {
        _currencyAll = _currencyAll.filter((item) => item.id != val.value)
        // console.log(_currencyAll);
      }
      this[keys] = _currencyAll
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // console.log(this.addForm);
          // let _request = this.dialogData?.requestUrl;
          // console.log(this.dialogData, _request);
          // this.$API.baseMainData[_request](params).then((res) => {
          //   if (res.code == 200) {
          //     this.$toast({ content: this.$t("操作成功"), type: "success" });
          //     this.$emit("handleAddDialogShow", false);
          //     this.$emit("confirmSuccess");
          //   }
          // });
        }
      })
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },

    getRules() {
      if (this.dialogData?.dialogType == 'add') {
        // this.$API.baseMainData.getAddRulesExchangeRate().then((res) => {
        //   if (res.code == 200) this.rules = formatRules(res.data);
        //   this.rules.effectiveTime = [
        //     { required: true, message: this.$t("请选择有效时间"), trigger: "blur" },
        //   ];
        // });
      } else {
        // this.$API.baseMainData.getUpdateRulesExchangeRate().then((res) => {
        //   if (res.code == 200) this.rules = formatRules(res.data);
        //   this.rules.effectiveTime = [
        //     { required: true, message: this.$t("请选择有效时间"), trigger: "blur" },
        //   ];
        // });
      }
    }
  }
}
</script>

<style></style>
