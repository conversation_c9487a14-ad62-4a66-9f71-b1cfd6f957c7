// 创建审批流弹窗
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <div class="dialog-content">
      <mt-form ref="ruleForm" :model="approvalObject" :rules="dialogRules">
        <mt-form-item prop="businessTypeId" :label="$t('业务类型')">
          <mt-select
            ref="businessTypeRef"
            v-model="approvalObject.businessTypeId"
            :data-source="businessTypeList"
            :allow-filtering="true"
            :placeholder="$t('请选择业务类型')"
            :fields="{ text: 'businessTypeName', value: 'id' }"
            :disabled="dialogData && dialogData.dialogType == 'edit'"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="enableStatus" :label="$t('是否启用审批：')">
          <mt-switch
            v-model="approvalObject.useApproval"
            :on-label="$t('启用')"
            :off-label="$t('停用')"
            ref="enableStatusRef"
          ></mt-switch>
        </mt-form-item>

        <mt-form-item prop="operationType" :label="$t('审批节点名称：')">
          <mt-select
            v-model="approvalObject.operationType"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="approvalList"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择审批节点名称')"
            :disabled="dialogData && dialogData.dialogType == 'edit'"
          ></mt-select>
        </mt-form-item>

        <mt-form-item
          prop="processName"
          :label="$t('关联工作流分类：')"
          v-if="flowGroupFields.dataSource.length"
        >
          <mt-input
            :readonly="true"
            :show-clear-button="false"
            v-model="approvalObject.processName"
            float-label-type="Never"
            @focus="focusGroupInput"
            :disabled="dialogData && dialogData.dialogType == 'edit'"
          ></mt-input>
          <mt-treeView
            v-show="showTreeView"
            class="tree-view-container"
            ref="innerTreeView"
            :fields="flowGroupFields"
            @nodeSelected="nodeSelected"
            @nodeClicked="nodeClicked"
            :disabled="dialogData && dialogData.dialogType == 'edit'"
          ></mt-treeView>
        </mt-form-item>

        <mt-form-item class="process-desc" prop="processDesc" :label="$t('审批节点描述：')">
          <mt-input
            :multiline="true"
            :rows="3"
            type="text"
            float-label-type="Never"
            v-model="approvalObject.processDesc"
            :placeholder="$t('请输入审批节点描述')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      dialogTitle: '',
      // useApproval: true,

      approvalObject: {
        useApproval: true,
        businessTypeId: '',
        configId: 0, //配置Id
        enableStatus: 1, //是否启用 1启用 2 停用
        operationType: '', //	操作类型
        processDesc: '', //审批描述
        processDefId: '', //流程定义Id
        processDefKey: '', //流程Key
        processGroup: '', //流程分组
        processName: '', //审批流程名称
        version: 0 //	版本号
      },
      dialogRules: {
        businessTypeId: [{ required: true, message: this.$t('请选择业务类型'), trigger: 'blur' }],
        operationType: [
          { required: true, message: this.$t('请选择审批流程节点'), trigger: 'blur' }
        ],
        processDefId: [{ required: true, message: this.$t('请选择工作流模型'), trigger: 'blur' }]
      },
      approvalList: [], //审批流节点类型列表
      statusList: [
        { text: this.$t('启用'), value: 1 },
        { text: this.$t('停用'), value: 2 }
      ],
      flowGroupFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'subChild'
      },
      selectedNodeSet: new Set(),
      currentTemplateNode: null,
      showTreeView: false,
      businessTypeList: [] // 业务类型list
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      let _addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
      _addForm.useApproval = _addForm.enableStatus == 1
      this.approvalObject = _addForm
      // console.log("_addForm", _addForm);
    }

    if (this.dialogData?.dialogType == 'add') {
      this.dialogTitle = this.$t('新增审批流')
    } else {
      this.dialogTitle = this.$t('编辑审批流')
    }
    this.getBusinessConfig()
    this.getFlowOperationTypes()
    this.getTenantCategoryTree()
  },
  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },

    focusGroupInput(e) {
      console.log('focusGroupInput-pinput', e)
      this.showTreeView = true
    },
    // 判断是否模板节点, true: 是
    checkTemplateNode(treeRef, id) {
      const treeNodes = treeRef.ejsInstances.getTreeData(id)
      return treeNodes.length && treeNodes[0].categoryName
    },
    // 先触发 nodeSelected , 然后触发nodeClicked 事件， 这里只处理叶子节点
    nodeClicked() {
      if (this.currentTemplateNode) {
        const { categoryName, id, key, name } = this.currentTemplateNode
        this.approvalObject.processGroup = categoryName //流程分组
        this.approvalObject.processDefId = id //流程定义Id
        this.approvalObject.processDefKey = key //流程Key
        this.approvalObject.processName = name //审批流程名称
        console.log('点击模型节点---', this.approvalObject)
        this.showTreeView = false
      }
    },
    // 模板树的选中事件，在其中调用接口，获取数据， 忽略叶子节点
    nodeSelected(e) {
      console.log('nodeSelected-pinput', e)
      const { nodeData } = e
      this.currentTemplateNode = undefined // 每次事件触发先，先将最近选中的模板置空
      this.approvalObject.processGroup = null //流程分组
      this.approvalObject.processDefId = null //流程定义Id
      this.approvalObject.processDefKey = null //流程Key
      this.approvalObject.processName = null //审批流程名称

      if (this.selectedNodeSet.has(nodeData.id)) {
        return
      }
      const treeRef = this.$refs.innerTreeView
      if (this.checkTemplateNode(treeRef, nodeData.id)) {
        this.currentTemplateNode = treeRef.ejsInstances.getTreeData(nodeData.id)[0]
      } else {
        this.getTemplateByCategory(nodeData, treeRef)
      }
    },
    // 获取业务类型下拉
    getBusinessConfig() {
      this.$API.bgConfig.getBusinessConfig().then((res) => {
        this.businessTypeList = res.data
      })
    },
    // 获取分类下的所有模板
    getTemplateByCategory(nodeData, treeRef) {
      let _params = {
        category: nodeData.id,
        current: 1,
        size: 100,
        type: 'ProcessModel',
        key: ''
      }
      this.$API.flow.getTemplatePage(_params).then((res) => {
        let _records = [...res.data.records]
        _records.forEach((e) => {
          e.categoryName = nodeData.text
        })
        treeRef.ejsInstances.addNodes([..._records], nodeData.id)
        this.selectedNodeSet.add(nodeData.id)
      })
    },

    //获取审批流结点类型列表
    getFlowOperationTypes() {
      this.$API.moduleConfig.getFlowOperationTypes().then((res) => {
        console.log('get type-list', res)
        this.approvalList = res.data
      })
    },
    //获取关联工作流-列表
    getTenantCategoryTree() {
      this.$API.flow.getTenantCategoryTree().then((res) => {
        console.log('get type-tree---', res)
        this.$set(this.flowGroupFields, 'dataSource', res.data)
      })
    },
    confirm() {
      console.log(this.$t('保存'), this.approvalObject)
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.approvalObject))
          params.enableStatus = this.useApproval ? 1 : 2
          delete params.useApproval
          if (this.dialogData?.dialogType == 'add') {
            delete params.configId
            delete params.version

            if (this.approvalObject.businessTypeId && this.$refs.businessTypeRef) {
              let _data = this.$refs.businessTypeRef.ejsRef.getDataByValue(
                this.approvalObject.businessTypeId
              )
              console.log('businessTypeId _data', _data)
              params.businessTypeCode = _data.businessTypeCode
              params.businessTypeName = _data.businessTypeName
            }
          }

          console.log('save-params--', params)
          this.$API.moduleConfig.saveApprovalConfig(params).then((res) => {
            console.log('保存审批流--', res)
            if (res.code == 200) {
              this.$emit('handleAddDialogShow', false)
              this.$emit('confirmSuccess')
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/span.e-input-group {
    padding: 0;
  }
  /deep/.process-desc {
    width: 820px !important;
  }
  /deep/.tree-view-container {
    box-shadow: inset 0 0 0 1px rgba(232, 232, 232, 1);
    width: 100%;
    position: absolute;
    left: 0;
    top: 50px;
    z-index: 2;
    background: #fff;
    height: 200px;
    overflow: auto;
  }
}
</style>
