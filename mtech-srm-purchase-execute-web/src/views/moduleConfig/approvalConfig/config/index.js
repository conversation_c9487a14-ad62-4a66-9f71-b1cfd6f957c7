import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    // width: "150",
    field: 'businessTypeName',
    headerText: i18n.t('业务类型'),
    cellTools: ['edit', 'delete']

    // cellTools: [
    //  "edit", "delete",
    //   {
    //     id: "active",
    //     icon: "back",
    //     title: i18n.t("启用"),
    //     visibleCondition: () => false,
    //   },
    //   {
    //     id: "inactive",
    //     icon: "back",
    //     title: i18n.t("停用"),
    //     visibleCondition: () => false,
    //   },
    // ],
  },
  {
    // width: "150",
    field: 'processName',
    headerText: i18n.t('审批节点名称')
  },
  {
    // width: "150",
    field: 'processDesc',
    headerText: i18n.t('审批节点描述')
  },
  {
    // width: "150",
    field: 'operationTypeName',
    headerText: i18n.t('关联审批流')
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间')
  },
  {
    // width: "150",
    field: 'updateUserName',
    headerText: i18n.t('更新人')
  }
]
