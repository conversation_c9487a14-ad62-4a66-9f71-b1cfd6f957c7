import { i18n } from '@/main.js'
import { timeNumberToDate } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'
export const cols = [
  {
    width: '150',
    field: 'code',
    headerText: i18n.t('对账单号'),
    valueConverter: {
      type: 'placeholder',
      placeholder: '--' //placeholder可不传，默认值为"未填"
    },
    cellTools: [
      {
        id: 'view',
        icon: 'icon_outline_Preview',
        title: i18n.t('查看')
      }
    ]
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: -1, text: i18n.t('关闭'), cssClass: 'col-inactive' },
        { value: 0, text: i18n.t('待发布'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('发布待反馈'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('反馈正常'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('反馈异常'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('已确认'), cssClass: 'col-active' }
      ]
    },
    cellTools: [
      {
        id: 'feedback',
        icon: 'icon_list_feedback',
        title: i18n.t('反馈'),
        visibleCondition: (data) => {
          return data['status'] == 1
        },
        permission: ['O_02_1321']
      }
    ]
  },
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('客户公司名称'),
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return row.companyCode + '-' + row.companyName
      }
    }
  },
  {
    width: '150',
    field: 'customerCode',
    headerText: i18n.t('客户编码')
  },
  {
    width: '150',
    field: 'untaxedTotalPrice',
    headerText: i18n.t('执行未税金额')
  },
  {
    width: '150',
    field: 'taxedTotalPrice',
    headerText: i18n.t('执行含税金额')
  },
  {
    width: '150',
    field: 'taxPrice',
    headerText: i18n.t('税额')
  },
  {
    width: '150',
    field: 'currencyName',
    headerText: i18n.t('货币')
  },
  {
    width: '150',
    field: 'publishTime',
    headerText: i18n.t('发布时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    width: '150',
    field: 'feedbackTime',
    headerText: i18n.t('确认时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    width: '150',
    field: 'purRemark',
    headerText: i18n.t('采方备注')
  },
  {
    width: '150',
    field: 'supRemark',
    headerText: i18n.t('供方备注')
  }
]
