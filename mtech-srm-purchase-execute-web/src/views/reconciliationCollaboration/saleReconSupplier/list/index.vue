<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      class="self-set-table"
      :template-config="componentConfig"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { cols } from './config'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  data() {
    return {
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          toolbar: [
            {
              id: 'excelExport',
              icon: 'icon_solid_export',
              title: this.$t('导出')
            }
          ],
          gridId: this.$tableUUID.reconciliationCollaboration.saleReconSupplier.list,

          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: cols,
            // 销售待对账明细接口
            asyncConfig: {
              url: `${BASE_TENANT}/sale/reconciliation/sup/query`,
              defaultRules: [
                {
                  field: 'status',
                  operator: 'notequal',
                  value: -1
                }
              ]
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id == 'excelExport') {
        this.handleExport()
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'feedback') {
        this.$router.push(
          `sale-detail-supplier?id=${e.data.id}&type=feedback&code=${e.data.code}&come=recon`
        )
      } else if (e.tool.id == 'view') {
        this.$router.push(
          `sale-detail-supplier?id=${e.data.id}&type=view&code=${e.data.code}&come=recon`
        )
      }
    },
    handleClickCellTitle(e) {
      if (e.field == 'code') {
        this.$router.push(
          `sale-detail-supplier?id=${e.data.id}&type=view&code=${e.data.code}&come=recon`
        )
      }
    },
    // 导出
    handleExport() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 10000 },
        pageFlag: true,
        rules: rule.rules || [],
        defaultRules: [
          {
            field: 'status',
            operator: 'notequal',
            value: -1
          }
        ]
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationCollaboration.reconciliationSaleExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>

<style></style>
