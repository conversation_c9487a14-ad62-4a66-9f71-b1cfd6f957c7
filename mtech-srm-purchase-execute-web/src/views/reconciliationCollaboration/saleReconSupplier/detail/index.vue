<template>
  <div class="full-height mt-flex-direction-column detail-fix-wrap">
    <!-- 头部信息 -->
    <top-info
      class="flex-keep"
      ref="topInfoRef"
      :header-info="headerInfo"
      @doSubmit="doSubmit"
    ></top-info>

    <div class="bottom-tables">
      <mt-tabs
        tab-id="reconci-tab"
        :e-tab="false"
        :data-source="tabList"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>

      <div class="bottom-box">
        <!-- 对账明细 -->
        <div class="grid-wrap">
          <recon-detail
            class="flex-fit"
            ref="saleReconRef"
            :item-info="itemInfo"
            v-show="tabIndex == 0"
          ></recon-detail>
        </div>

        <!-- 相关附件 -->
        <relative-file
          ref="relativeFileRef"
          v-show="tabIndex == 1"
          :module-file-list="moduleFileList"
        ></relative-file>

        <!-- 操作日志 -->
        <operation-log
          :entry-code="$route.query.code"
          log-operation-type="SaleReconciliationHeader"
          v-if="tabIndex == 2"
        ></operation-log>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * entryType: create - 创建；view - 查看
 */
import { nodeType } from '@/components/businessComponents/relativeFileNoDocId/config/relative.js'
export default {
  components: {
    topInfo: require('./components/topInfo.vue').default,
    reconDetail: require('./components/reconDetail.vue').default,
    relativeFile: require('@/components/businessComponents/relativeFileNoDocId/index.vue').default,
    operationLog: require('./components/operationLog/index.vue').default
  },
  data() {
    return {
      tabIndex: 0,
      tabList: [
        {
          title: this.$t('对账明细')
        },
        {
          title: this.$t('相关附件')
        },
        {
          title: this.$t('操作日志')
        }
      ],
      moduleFileList: [],
      beforeTabIndex: 0,
      headerInfo: null,
      itemInfo: null
    }
  },

  mounted() {
    // 根据id 获取详情
    this.getDetail()
  },

  methods: {
    getDetail() {
      let _id = this.$route.query.id
      this.$API.reconciliationCollaboration.getSaleDetailById(_id).then((res) => {
        this.headerInfo = res.data?.header
        this.itemInfo = res.data?.items || []
        this.formatFile(res.data?.purFiles, res.data?.supFiles)
      })
    },

    // 整合附件
    formatFile(purFiles, supFiles) {
      if (this.$route.query.type == 'feedback') {
        this.moduleFileList = [
          {
            id: '01',
            code: 'reconciliation_settled_header_file', // 清账整单附件code
            nodeName: '采方 - 整单附件',
            type: nodeType.mainViewData,
            dataSource: purFiles || []
          },
          {
            id: '02',
            code: 'reconciliation_settled_header_file', // 清账整单附件code
            nodeName: '供方 - 整单附件',
            type: nodeType.mainUpdate
          }
        ]
      } else {
        this.moduleFileList = [
          {
            id: '01',
            code: 'reconciliation_settled_header_file', // 清账整单附件code
            nodeName: '采方 - 整单附件',
            type: nodeType.mainViewData,
            dataSource: purFiles || []
          },
          {
            id: '02',
            code: 'reconciliation_gong',
            nodeName: '供方 - 整单附件',
            type: nodeType.mainViewData,
            dataSource: supFiles || []
          }
        ]
      }
    },

    doSubmit(flag) {
      console.log(this.$refs.topInfoRef, this.$refs.saleReconRef, this.$refs.relativeFileRef)
      let _dataSource =
        this.$refs.saleReconRef.$refs.detailTemplate
          .getCurrentUsefulRef()
          .gridRef?.ejsRef?.getCurrentViewRecords() || []

      let _waits = _dataSource.map((i) => {
        return {
          id: i.id,
          supRemark: i.supRemark
        }
      })

      let submitData = {
        header: {
          id: this.$route.query.id,
          supRemark: this.$refs.topInfoRef?.topInfo.supRemark
        },
        fileList: this.$refs.relativeFileRef.getUploadFlies(this.moduleFileList[1].id),
        items: _waits,
        status: flag
      }
      console.log('submitData', submitData)
      this.$API.reconciliationCollaboration.feedBackSaleRecon(submitData).then((res) => {
        if (res?.code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$router.replace(`sale-recon-supplier`)
        }
      })
    },

    handleSelectTab(e) {
      this.tabIndex = e
    }
  },

  beforeDestroy() {
    // sessionStorage.removeItem("saleCreateSessionData");
  }
}
</script>
<style lang="scss" scoped>
.bottom-tables {
  flex: 1;
  overflow: auto;

  /deep/ .repeat-template {
    .mt-data-grid,
    .mt-data-grid > .e-control {
      height: 100%;

      .e-gridcontent {
        height: calc(100% - 44px);

        .e-content {
          height: 100%;
        }
      }
    }
  }
  /deep/ .table-container {
    height: 100%;
    max-height: 100%;
  }
}
</style>
