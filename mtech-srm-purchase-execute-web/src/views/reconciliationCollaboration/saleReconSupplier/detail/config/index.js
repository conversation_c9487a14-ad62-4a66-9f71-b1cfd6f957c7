import { i18n } from '@/main.js'
import { timeNumberToDate } from '@/utils/utils'

export const checkCol = {
  width: '50',
  type: 'checkbox',
  showInColumnChooser: false,
  allowEditing: false
}

export const detailCols = [
  {
    width: '150',
    field: 'saleOrderCode',
    headerText: i18n.t('销售单号')
  },
  {
    width: '150',
    field: 'lineNo',
    headerText: i18n.t('销售单行号')
  },
  {
    width: '150',
    field: 'receiveCode',
    headerText: i18n.t('交货单号')
  },
  {
    width: '150',
    field: 'receiveItemNo',
    headerText: i18n.t('交货单行号')
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'unitName',
    headerText: i18n.t('单位')
  },
  {
    width: '150',
    field: 'quantity',
    headerText: i18n.t('数量')
  },
  {
    width: '150',
    field: 'receiveDate',
    headerText: i18n.t('事务日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    }
  },
  {
    width: '150',
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    width: '150',
    field: 'untaxedTotalPrice',
    headerText: i18n.t('未税总价')
  },
  {
    width: '150',
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    width: '150',
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价')
  },
  {
    width: '150',
    field: 'taxedTotalPrice',
    headerText: i18n.t('含税总价')
  },
  // {
  //   width: "150",
  //   field: "currencyName",
  //   headerText: i18n.t("币种"),
  // },
  {
    width: '150',
    field: 'purRemark',
    headerText: i18n.t('采方备注')
  },
  {
    width: '150',
    field: 'supRemark',
    headerText: i18n.t('供方备注')
  }
]
