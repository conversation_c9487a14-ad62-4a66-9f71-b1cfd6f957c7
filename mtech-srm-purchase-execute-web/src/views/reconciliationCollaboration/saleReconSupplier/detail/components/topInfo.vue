<template>
  <div :class="['detail-top-info', !isExpand && 'detail-top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box" v-if="topInfo">
      <div class="infos mr20">{{ statusTxt }}</div>
      <div class="infos mr20">{{ $t('销售对账单号：') }}{{ topInfo.code || '-' }}</div>
      <div class="infos mr20">{{ $t('创建人：') }}{{ topInfo.createUserName }}</div>
      <div class="infos">{{ $t('创建时间：') }}{{ topInfo.createTime | dateFormat }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-if="$route.query.type == 'feedback'"
        @click="doSubmit(2)"
        >{{ $t('接受') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-if="$route.query.type == 'feedback'"
        @click="doSubmit(3)"
        >{{ $t('拒绝') }}</mt-button
      >

      <div class="sort-box" @click="doExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom" v-if="topInfo">
      <mt-form ref="ruleForm" :model="topInfo">
        <mt-form-item prop="company" :label="$t('客户公司名称')">
          <mt-input
            v-model="topInfo.company"
            :disabled="true"
            :placeholder="$t('客户公司名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="currencyName" :label="$t('币种')">
          <mt-input
            v-model="topInfo.currencyName"
            :disabled="true"
            :placeholder="$t('币种')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="untaxedTotalPrice" :label="$t('执行未税总金额')">
          <mt-input
            v-model="topInfo.untaxedTotalPrice"
            :disabled="true"
            :placeholder="$t('执行未税总金额')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="taxedTotalPrice" :label="$t('执行含税总金额')">
          <mt-input
            v-model="topInfo.taxedTotalPrice"
            :disabled="true"
            :placeholder="$t('执行含税总金额')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="taxPrice" :label="$t('税额')">
          <mt-input
            v-model="topInfo.taxPrice"
            :disabled="true"
            :placeholder="$t('税额')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="purRemark" :label="$t('采方备注')" class="full-width">
          <mt-input
            v-model="topInfo.purRemark"
            :disabled="true"
            :placeholder="$t('采方备注')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="supRemark" :label="$t('供方备注')" class="full-width">
          <mt-input
            v-model="topInfo.supRemark"
            :disabled="$route.query.type == 'view'"
            :placeholder="$t('供方备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isExpand: true,
      topInfo: null,
      statusTxt: ''
    }
  },

  filters: {
    dateFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }

      return str
    }
  },

  watch: {
    headerInfo: {
      handler(newVal) {
        this.topInfo = {
          ...newVal,
          company: newVal?.companyCode + '-' + newVal?.companyName
        }
      },
      immediate: true,
      deep: true
    },
    'topInfo.status'(value) {
      let str = ''
      if (value == -1) {
        str = this.$t('关闭')
      } else if (value == 0) {
        str = this.$t('待发布')
      } else if (value == 1) {
        str = this.$t('发布待反馈')
      } else if (value == 2) {
        str = this.$t('反馈正常')
      } else if (value == 3) {
        str = this.$t('反馈异常')
      } else if (value == 4) {
        str = this.$t('已确认')
      }
      this.statusTxt = str
    }
  },

  methods: {
    // 返回
    goBack() {
      if (this.$route.query.come == 'recon') {
        this.$router.replace(`sale-recon-supplier`)
      } else if (this.$route.query.come == 'reconQuery') {
        this.$router.replace(`sale-recon-supplier-query`)
      }
    },
    // 提交
    doSubmit(flag) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: flag === 2 ? this.$t('确认接受吗？') : this.$t('确认拒绝吗？')
        },
        success: () => {
          this.$emit('doSubmit', flag)
        }
      })
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
    }
  }
}
</script>

<style></style>
