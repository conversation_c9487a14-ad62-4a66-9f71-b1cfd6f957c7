<template>
  <mt-template-page
    ref="detailTemplate"
    :template-config="componentConfig"
    @handleClickToolBar="handleClickToolBar"
  ></mt-template-page>
</template>

<script>
import { cloneDeep } from 'lodash'
import { checkCol, detailCols } from '../config'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  props: {
    itemInfo: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      componentConfig: [
        {
          useToolTemplate: false,
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: [
            [
              {
                id: 'excelExport',
                icon: 'icon_solid_pushorder',
                title: this.$t('导出')
              }
            ]
          ],
          gridId: this.$tableUUID.reconciliationCollaboration.saleDetailSupplier.reconDetail.list,
          grid: {
            height: 'auto',
            allowPaging: false,
            columnData: [checkCol],
            dataSource: []
          }
        }
      ]
    }
  },

  watch: {
    itemInfo: {
      handler(newVal) {
        // 设置dataSource
        if (newVal) this.$set(this.componentConfig[0].grid, 'dataSource', newVal)
      },
      immediate: true
    }
  },

  mounted() {
    this.setData()
  },
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id == 'excelExport') {
        this.handleExport()
      }
    },
    // 设置列、行、是否可编辑
    setData() {
      let type = this.$route.query.type

      // 设置列
      let columns = cloneDeep(detailCols)
      columns.forEach((i) => {
        i.allowEditing = i.field == 'supRemark'
      })
      this.$set(this.componentConfig[0].grid, 'columnData', [checkCol].concat(columns))

      // 设置是否表格可编辑
      let editSettings
      if (type == 'feedback') {
        editSettings = {
          allowEditing: true,
          allowAdding: true,
          allowDeleting: true,
          mode: 'Normal', // Batch模式下editTemplate使用下拉的话，保存的args里获取不到，所以就回显不上。最终用重新赋值dataSource解决(币种和贸易条款的下拉)
          showConfirmDialog: false,
          showDeleteConfirmDialog: false, // 因为切换业务类型的时候，只能通过deleteRecord来删除了，但这个方法会触发这个弹窗，所以只能把弹窗去掉
          newRowPosition: 'Bottom'
        }
      } else {
        editSettings = {
          allowEditing: false
        }
      }
      this.$set(this.componentConfig[0].grid, 'editSettings', editSettings)
    },
    handleExport() {
      let _id = this.$route.query?.id
      this.$store.commit('startLoading')
      this.$API.reconciliationCollaboration.reconciliationSaleListExport(_id).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>
