<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules">
      <mt-form-item prop="invoiceType" :label="$t('发票类型')" class="">
        <mt-select
          v-model="formData.invoiceType"
          :data-source="InvoiceTypeOptions"
          :show-clear-button="true"
          :placeholder="$t('发票类型')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="invoiceCode" :label="$t('发票代码')" class="">
        <mt-input
          maxlength="50"
          v-model="formData.invoiceCode"
          :disabled="isUploadEdit"
          :show-clear-button="true"
          :placeholder="$t('发票代码')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="invoiceNum" :label="$t('发票号')" class="">
        <mt-input
          maxlength="50"
          v-model="formData.invoiceNum"
          :disabled="isUploadEdit"
          :show-clear-button="true"
          :placeholder="$t('发票号')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="invoiceTime" :label="$t('开票日期')" class="">
        <mt-date-picker
          :show-clear-button="true"
          :allow-edit="false"
          :max="new Date()"
          v-model="formData.invoiceTime"
          :placeholder="$t('开票日期')"
        ></mt-date-picker>
      </mt-form-item>
      <mt-form-item prop="taxedAmount" :label="$t('发票含税金额')" class="">
        <mt-input-number
          max="999999999999999.99"
          :show-clear-button="true"
          :show-spin-button="false"
          :precision="2"
          :placeholder="$t('发票含税金额')"
          @input="handleTaxedAmount"
          v-model="formData.taxedAmount"
        ></mt-input-number>
      </mt-form-item>
      <mt-form-item prop="untaxedAmount" :label="$t('发票未税金额')" class="">
        <mt-input-number
          max="999999999999999.99"
          :show-clear-button="true"
          :show-spin-button="false"
          :precision="2"
          :placeholder="$t('发票未税金额')"
          @input="handleUntaxedAmount"
          v-model="formData.untaxedAmount"
        ></mt-input-number>
      </mt-form-item>
      <mt-form-item prop="taxAmount" :label="$t('税额')" class="">
        <mt-input
          :disabled="true"
          v-model="formData.taxAmount"
          :show-clear-button="true"
          :placeholder="$t('税额')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="remark" :label="$t('供应商备注')" class="">
        <mt-input
          maxlength="200"
          v-model="formData.remark"
          :show-clear-button="true"
          :placeholder="$t('供应商备注')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { DialogActionType, InvoiceTypeOptions } from '../config/constant'
import utils from '@/utils/utils'
import bigDecimal from 'js-big-decimal'

export default {
  data() {
    // 发票含税金额
    const taxedAmountValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入发票含税金额')))
      } else if (
        this.formData.untaxedAmount &&
        Number(value) < Number(this.formData.untaxedAmount)
      ) {
        callback(new Error(this.$t('含税金额不能小于未税金额')))
      } else {
        this.$refs.ruleForm.clearValidate(['untaxedAmount'])
        callback()
      }
    }
    // 发票未税金额
    const untaxedAmountValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入发票未税金额')))
      } else if (this.formData.taxedAmount && Number(value) > Number(this.formData.taxedAmount)) {
        callback(new Error(this.$t('未税金额不能大于含税金额')))
      } else {
        this.$refs.ruleForm.clearValidate(['taxedAmount'])
        callback()
      }
    }

    return {
      dialogTitle: '',
      selectData: null,
      isUploadEdit: false, // 是否正在编辑已上传的发票
      rules: {
        invoiceType: [
          {
            required: true,
            message: this.$t('请选择发票类型'),
            trigger: 'blur'
          }
        ],
        invoiceCode: [
          {
            required: true,
            message: this.$t('请输入发票代码'),
            trigger: 'blur'
          }
        ],
        invoiceNum: [{ required: true, message: this.$t('请输入发票号'), trigger: 'blur' }],
        invoiceTime: [
          {
            required: true,
            message: this.$t('请选择开票日期'),
            trigger: 'blur'
          }
        ],
        taxedAmount: [{ required: true, validator: taxedAmountValidator, trigger: 'blur' }],
        untaxedAmount: [
          {
            required: true,
            validator: untaxedAmountValidator,
            trigger: 'blur'
          }
        ]
      },
      InvoiceTypeOptions,
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      actionType: DialogActionType.Add, // 默认类型：新增
      formData: {
        invoiceType: null, // 发票类型
        invoiceCode: '', // 发票代码
        invoiceNum: '', // 发票号
        invoiceTime: '', // 开票日期
        untaxedAmount: '', // 发票未税金额
        taxAmount: '', // 税额
        taxedAmount: '', // 发票含税金额
        currencyName: '', //币种
        remark: '' // 供应商备注
      }
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      const { title, actionType, selectData } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.actionType = actionType // 弹框模式
      this.selectData = selectData // 行数据
      this.initForm()
      this.$refs.dialog.ejsRef.show()
    },
    initForm() {
      this.formData = {
        id: undefined, // 发票id，编辑已上传的发票时，发票代码和发票号不可编辑
        invoiceType: null, // 发票类型
        invoiceCode: '', // 发票代码
        invoiceNum: '', // 发票号
        invoiceTime: '', // 开票日期
        untaxedAmount: '', // 发票未税金额
        taxAmount: '', // 税额
        taxedAmount: '', // 发票含税金额
        currencyName: '', //币种
        remark: '' // 供应商备注
      }
      // 正在编辑已上传的发票 初始化
      this.isUploadEdit = false
      this.$refs.ruleForm.clearValidate()
      // 编辑
      if (this.actionType === DialogActionType.Edit) {
        let invoiceTime = ''
        // 日期转换
        if (this.selectData.invoiceTime) {
          const date = new Date(this.selectData.invoiceTime) // 日期类型格式 yyyy-mm-dd hh:mm:ss
          // 如果 invoiceTime 是一个日期，就使用它
          if (!isNaN(date.getTime())) {
            invoiceTime = date
          }
        }
        this.formData = {
          ...this.selectData,
          invoiceTime // 开票日期
        }
        if (this.selectData.id) {
          // 存在 id，正在编辑已上传的发票
          this.isUploadEdit = true
        }
      }
    },
    // 发票未税金额 改变
    handleUntaxedAmount(e) {
      const taxedAmount = this.formData.taxedAmount ?? 0 // 含税
      const untaxedAmount = e ?? 0 // 未税
      // 税额 = 含税 - 未税
      this.formData.taxAmount = bigDecimal.subtract(taxedAmount, untaxedAmount)
    },
    // 发票含税金额 改变
    handleTaxedAmount(e) {
      const taxedAmount = e ?? 0 // 含税
      const untaxedAmount = this.formData.untaxedAmount ?? 0 // 未税
      // 税额 = 含税 - 未税
      this.formData.taxAmount = bigDecimal.subtract(taxedAmount, untaxedAmount)
    },
    onOpen(args) {
      args.preventFocus = true
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 日期转换
          let invoiceTime = ''
          if (this.formData.invoiceTime) {
            const date = this.formData.invoiceTime
            // 如果 invoiceTime 是一个日期，就使用它
            if (!isNaN(date.getTime())) {
              invoiceTime = utils.dateFormat(date, 'Y-m-d H:i:s') // 将日期转为 api 需要的格式 yyyy-mm-dd hh:mm:ss
            }
          }
          this.$emit('confirm', {
            data: {
              ...this.formData,
              untaxedAmount: Number(this.formData.untaxedAmount), // 转换类型 用于比较
              taxedAmount: Number(this.formData.taxedAmount), // 转换类型 用于比较
              invoiceTime // 开票日期
            },
            actionType: this.actionType
          })
          this.handleClose()
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
