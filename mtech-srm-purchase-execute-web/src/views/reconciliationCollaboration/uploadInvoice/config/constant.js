import { i18n } from '@/main.js'

export const ConstantType = {
  Add: '1', // 新增
  Edit: '2', // 编辑
  Look: '3' // 查看
}

export const DialogActionType = {
  Add: 0, // 新增
  Edit: 1 // 编辑
}
// 发票类型 选项
export const InvoiceTypeOptions = [
  {
    text: i18n.t('增值税'),
    value: '0'
  },
  {
    text: i18n.t('普通发票'),
    value: '1'
  }
]

// tab
export const Tab = {
  reconciliationDetails: 1, // 对账明细
  invoiceList: 2 // 发票清单
}

// tab code
export const TabCode = {
  highLowInfo: 'highLowInfo', // 高低开信息
  operationLog: 'operationLog', // 操作日志
  reconciliationFile: 'reconciliationFile', // 相关附件
  reconciliationField: 'reconciliationField', // 对账明细
  invoiceList: 'invoiceList', // 发票清单
  purHeaderFile: 'purHeaderFile', // 采方-整单附件
  supHeaderFile: 'supHeaderFile' // 供方-整单附件
}

// 发票清单 表格列数据
export const InvoiceColumnData = [
  {
    fieldCode: 'invoiceType', // （0增值税，1普通发票）
    fieldName: i18n.t('发票类型')
  },
  {
    fieldCode: 'invoiceCode',
    fieldName: i18n.t('发票代码')
  },
  {
    fieldCode: 'invoiceNum',
    fieldName: i18n.t('发票号')
  },
  {
    fieldCode: 'invoiceTime',
    fieldName: i18n.t('开票日期')
  },
  {
    fieldCode: 'untaxedAmount',
    fieldName: i18n.t('发票未税金额')
  },
  {
    fieldCode: 'taxAmount',
    fieldName: i18n.t('税额')
  },
  {
    fieldCode: 'taxedAmount',
    fieldName: i18n.t('发票含税金额')
  },
  {
    fieldCode: 'remark',
    fieldName: i18n.t('供应商备注')
  }
]

// 税控平台 表格列数据
export const InvoiceTableColumnData = [
  {
    fieldCode: 'invoiceType', // （0增值税，1普通发票）
    fieldName: i18n.t('发票类型')
  },
  {
    fieldCode: 'invoiceCode',
    fieldName: i18n.t('发票代码')
  },
  {
    fieldCode: 'invoiceNum',
    fieldName: i18n.t('发票号')
  },
  {
    fieldCode: 'buyerName',
    fieldName: i18n.t('购方名称')
  },
  {
    fieldCode: 'buyerTaxno',
    fieldName: i18n.t('购方税号')
  },
  {
    fieldCode: 'sellerName',
    fieldName: i18n.t('销方名称')
  },
  {
    fieldCode: 'sellerTaxno',
    fieldName: i18n.t('销方税号')
  },
  {
    fieldCode: 'invoiceTime',
    fieldName: i18n.t('开票日期')
  },
  {
    fieldCode: 'untaxedAmount',
    fieldName: i18n.t('发票未税金额')
  },
  {
    fieldCode: 'taxAmount',
    fieldName: i18n.t('税额')
  },
  {
    fieldCode: 'taxedAmount',
    fieldName: i18n.t('发票含税金额')
  }
]

// 单据状态 -1:关闭 0:未发布 1:待反馈 2:反馈正常 3:反馈异常
export const ConstStatus = {
  close: -1,
  unpublished: 0,
  toBeConfirmed: 1,
  normal: 2,
  abnormal: 3
}

// 单据状态
export const Status = {
  [ConstStatus.close]: i18n.t('关闭'),
  [ConstStatus.unpublished]: i18n.t('未发布'),
  [ConstStatus.toBeConfirmed]: i18n.t('待反馈'),
  [ConstStatus.normal]: i18n.t('反馈正常'),
  [ConstStatus.abnormal]: i18n.t('反馈异常')
}

// 开票状态 0:未开票 1:已开票 2:采购已确认 -1:反馈异常 3:财务已确认
export const InvoiceStatus = {
  abnormal: -1, // 反馈异常
  notInvoiced: 0, // 未开票
  invoiced: 1, // 已开票
  confirmed: 2, // 采购已确认
  confirmedFinance: 3 // 财务已确认
}

// 开票状态 0:未开票 1:已开票 2:已确认 3:反馈异常
export const InvoiceStatusConst = {
  [InvoiceStatus.abnormal]: i18n.t('异常退回'),
  [InvoiceStatus.notInvoiced]: i18n.t('未开票'),
  [InvoiceStatus.invoiced]: i18n.t('已开票'),
  [InvoiceStatus.confirmed]: i18n.t('采购已确认'),
  [InvoiceStatus.confirmedFinance]: i18n.t('财务已确认')
}

// 开票状态 对应的 css class
export const InvoiceStatusCssClass = {
  [InvoiceStatus.abnormal]: 'col-abnormal',
  [InvoiceStatus.notInvoiced]: 'col-published',
  [InvoiceStatus.invoiced]: 'col-active',
  [InvoiceStatus.confirmed]: 'col-normal',
  [InvoiceStatus.confirmedFinance]: 'col-normal'
}

// 表格 checkbox
export const ColumnCheckbox = {
  width: '50',
  type: 'checkbox',
  showInColumnChooser: false,
  visible: false // 不显示，无需使用 checkbox
}
