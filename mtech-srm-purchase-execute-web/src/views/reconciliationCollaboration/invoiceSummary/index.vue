<template>
  <!-- 发票协同（供方）列表 -->
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      :hidden-tabs="true"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import { formatTableColumnData } from './config/index.js'
import { ColumnData, ConstantType, ConstStatus } from './config/constant'
import { BASE_TENANT } from '@/utils/constant'

export default {
  components: {},
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      componentConfig: [
        {
          toolbar: [],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: formatTableColumnData(ColumnData),
            dataSource: [],
            // queryBuilder查询-发票协同信息
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliationHeaderSupplier/queryBuilderInvoice`,
              defaultRules: [
                {
                  field: 'status',
                  operator: 'equal',
                  value: ConstStatus.normal
                }
              ]
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      const selectedRecords = e.gridRef.getMtechGridRecords()
      const commonToolbar = [
        'Add',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]
      if (selectedRecords.length == 0 && !commonToolbar.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })
    },
    // CellTool
    handleClickCellTool(e) {
      if (e.tool.id === 'UploadInvoice') {
        this.goToInvoiceCollaborationUpload({
          headerInfo: e.data,
          entryType: ConstantType.Add
        })
      }
    },
    // CellTitle
    handleClickCellTitle(e) {
      if (e.field === 'reconciliationCode') {
        // 对账单号 clik
        this.goToInvoiceCollaborationUpload({
          headerInfo: e.data,
          entryType: ConstantType.Look
        })
      }
    },
    // 跳转到上传发票页面
    goToInvoiceCollaborationUpload(data) {
      const { headerInfo, entryType } = data
      // 上传发票 页面参数
      const params = {
        reconciliationCode: headerInfo.reconciliationCode, // 对账单号
        headerInfo,
        entryType: entryType
      }
      localStorage.setItem('reconciliationData', JSON.stringify(params))
      // 跳转到上传发票页面
      this.$router.push({
        name: 'reconciliation-upload-invoice',
        query: {}
      })
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
