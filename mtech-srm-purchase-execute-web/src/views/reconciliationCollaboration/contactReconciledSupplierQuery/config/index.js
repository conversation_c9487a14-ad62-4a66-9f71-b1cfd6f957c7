import { ReconciledStatusOptions, CellTools } from './constant'
import { timeDate, theReconDiffTemplate } from './columnComponent'
import bigDecimal from 'js-big-decimal'

// 格式化表格动态数据
export const formatTableColumnData = (args) => {
  const { data } = args
  const colData = []
  // 往来对账单
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150'
    }
    if (col.fieldCode === 'code') {
      // 往来对账单 往来对账单号
      defaultCol.cellTools = [] // 使其可点击查看
    } else if (col.fieldCode === 'createTime') {
      // 往来对账单 创建时间
      defaultCol.template = timeDate({
        dataKey: col.fieldCode,
        hasTime: true
      })
    } else if (col.fieldCode === 'theReconDiff') {
      // 往来对账单 对账差异 前端定义、计算 = 采方期末-供方期末
      defaultCol.template = theReconDiffTemplate
      defaultCol.ignore = true
    } else if (col.fieldCode === 'status') {
      // 状态
      defaultCol.valueConverter = {
        type: 'map',
        map: ReconciledStatusOptions
      }
      defaultCol.cellTools = CellTools
    }
    colData.push(defaultCol)
  })

  return colData
}

// 添加 对账差异 字段到列表中
export const addTheReconDiffToList = (list) => {
  if (list.length > 0) {
    // 往来对账单 对账差异 前端定义、计算 = 采方期末+供方期末
    list.forEach((item) => {
      const closingBalance = item.closingBalance || 0 // 采方期末
      const supplierClosingBalance = item.supplierClosingBalance || 0 // 供方期末
      // 对账差异
      item.theReconDiff = bigDecimal.add(closingBalance, supplierClosingBalance)
    })
  }
  return list
}
