import Vue from 'vue'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
// import bigDecimal from "js-big-decimal";

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, hasTime } = args

  const template = () => {
    return {
      template: Vue.component('date', {
        template: `<div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div><div>{{data[dataKey] | dateFormat}}</div></div>`,
        data: function () {
          return { data: {}, dataKey, hasTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}

// 对账差异
export const theReconDiffTemplate = () => {
  return {
    template: Vue.component('theReconDiffComponent', {
      template: `<div :class="setClassNameByData()">{{data.theReconDiff}}</div>`,
      data: function () {
        return { data: {} }
      },
      methods: {
        setClassNameByData() {
          let cssClass = ''
          if (this.data.theReconDiff > 0) {
            // 对账差异 > 0 蓝色
            cssClass = 'the-recon-diff-blue'
          } else if (this.data.theReconDiff < 0) {
            // 对账差异 < 0 红色
            cssClass = 'the-recon-diff-red'
          }

          return cssClass
        }
      }
    })
  }
}
