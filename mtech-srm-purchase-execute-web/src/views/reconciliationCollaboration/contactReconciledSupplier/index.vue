<template>
  <!-- 往来对账单列表-供方 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      class="template-height"
      :template-config="componentConfig"
      :current-tab="currentTab"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickCellTool="handleClickCellTool"
      @handleClickToolBar="handleClickToolBar"
    />
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { formatTableColumnData, addTheReconDiffToList } from './config/index'
import { ReconciledColumnData } from './config/constant'
import { EntryType } from '../contactDetailSupplier/config/constant'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  components: {},
  data() {
    const currentTab = JSON.parse(localStorage.getItem('tabIndex')) ?? 0
    localStorage.removeItem('tabIndex')

    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      currentTab, // 当前加载的 Tab 默认 0
      actionType: {
        createStatement: 1 // 明细创建对账单
      },
      componentConfig: [
        {
          // tab: { title: this.$t("往来对账单列表") },
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'excelExport',
              icon: 'icon_solid_export',
              title: this.$t('导出')
            }
          ],
          gridId: this.$tableUUID.reconciliationCollaboration.contactReconciledSupplier.list,
          grid: {
            // lineSelection: 0,
            lineIndex: 0,
            columnData: formatTableColumnData({
              data: ReconciledColumnData
            }),
            dataSource: [],
            asyncConfig: {
              // 租户级-往来对账单供应商控制器-分页查询
              url: `${BASE_TENANT}/transaction_reconciliation_header_supplier/pageQuery`,
              defaultRules: [
                {
                  field: 'status',
                  operator: 'in',
                  value: [1, 6]
                }
              ],
              // 往来对账单列表 序列化
              serializeList: (list) => {
                let afterSerialization = addTheReconDiffToList(list)
                return afterSerialization
              }
            }
            // frozenColumns: 1,
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id == 'excelExport') {
        this.handleExport()
      }
    },
    // CellTool TODO
    handleClickCellTool(args) {
      const { tool, data, tabIndex } = args
      if (tool.id === 'FeedbackStatement' || tool.id === 'uploadAttach') {
        // 反馈
        // 将 lastTabIndex 放到 localStorage 往来对账单详情-供方 读
        localStorage.setItem('lastTabIndex', JSON.stringify(tabIndex))
        // 跳转 往来对账单详情-供方
        this.$router.push({
          name: 'contact-detail-supplier',
          query: {
            id: data.id,
            code: data.code,
            type: tool.id === 'uploadAttach' ? EntryType.upload : EntryType.edit // 上传附件
          }
        })
      }
    },
    // CellTitle
    handleClickCellTitle(args) {
      const { field, tabIndex, data } = args
      if (field === 'code') {
        // 点击对账单号
        const id = data.id
        const code = data.code
        // 将 lastTabIndex 放到 localStorage 往来对账单详情-供方 读
        localStorage.setItem('lastTabIndex', JSON.stringify(tabIndex))
        // 跳转 往来对账单详情-供方
        this.$router.push({
          name: 'contact-detail-supplier',
          query: {
            id,
            code,
            type: data.status === 1 ? EntryType.edit : EntryType.view
          }
        })
      }
    },
    // 导出
    handleExport() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 10000 },
        pageFlag: true,
        rules: rule.rules || [],
        defaultRules: [
          {
            field: 'status',
            operator: 'in',
            value: [1, 6]
          }
        ]
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationCollaboration.reconciliationTransactionExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
// 对账差异 theReconDiffTemplate
/deep/ .the-recon-diff-blue {
  color: #6386c1; // 这是 UI 给的颜色
}
/deep/ .the-recon-diff-red {
  color: #ed5633; // 这是 UI 给的颜色
}
</style>
