<template>
  <!-- 往来对账单详情-供方 -->
  <div class="full-height pt20 vertical-flex-box">
    <!-- 头部信息 -->
    <top-info
      class="flex-keep"
      ref="topInfoRef"
      :header-info="headerInfo"
      :entry-type="entryType"
      @doSubmit="doSubmit"
      @goBack="goBack"
      @doExpand="doExpand"
    ></top-info>

    <mt-tabs
      class="flex-keep"
      :e-tab="false"
      :data-source="tabList"
      :selected-item="selectedItem"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <!-- 金额汇总 -->
    <div class="flex-fit summary" v-show="currentTabInfo.code === Tab.summary">
      <div class="box">
        <div class="title">
          <span>{{ $t('对账科目') }}</span>
        </div>
        <div class="tips">
          <mt-icon class="icon" name="icon_card_rank_second" />
          <span
            >{{ $t('对账总差异（元）：') }}<span v-if="statementId">{{ totalDiff }}</span
            ><span v-else>{{ '-' }}</span></span
          >
        </div>
        <div class="row-box">
          <!-- 采方-金额汇总 -->
          <div class="item-box">
            <div class="top">
              <div class="item">
                <div>{{ $t('对账单位') }}</div>
                <mt-input
                  v-model="headerInfo.companyName"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
              <div class="item">
                <div>{{ $t('期初余额') }}</div>
                <mt-input
                  v-model="headerInfo.openingBalance"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
              <div class="item">
                <div>{{ $t('期末余额') }}</div>
                <mt-input
                  v-model="headerInfo.closingBalance"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
            </div>
            <!-- <div class="table-box">
              <mt-data-grid
                :data-source="buyerSummary"
                :column-data="PurchaseSummaryColumns"
              ></mt-data-grid>
            </div> -->
          </div>
          <div class="dividing-line"></div>
          <!-- 供方-金额汇总 -->
          <div class="item-box">
            <div class="top">
              <div class="item">
                <div>{{ $t('对账单位') }}</div>
                <mt-input
                  v-model="headerInfo.supplierName"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
              <div class="item">
                <div>{{ $t('期初余额') }}</div>
                <mt-input
                  v-model="headerInfo.supplierOpeningBalance"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
              <div class="item">
                <div>{{ $t('期末余额') }}</div>
                <mt-input
                  v-model="headerInfo.supplierClosingBalance"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
            </div>
            <div class="top">
              <div class="item">
                <div><span style="color: red">*</span>{{ $t('制表') }}</div>
                <mt-input
                  v-model="headerInfo.supplierContact"
                  :disabled="this.$route.query.type != 2"
                  :placeholder="$t('请输入制表人')"
                ></mt-input>
              </div>
              <div class="item">
                <div><span style="color: red">*</span>{{ $t('联系电话') }}</div>
                <mt-input
                  v-model="headerInfo.supplierPhone"
                  :disabled="this.$route.query.type != 2"
                  :placeholder="$t('请输入联系电话')"
                ></mt-input>
              </div>
              <div class="item">
                <div><span style="color: red">*</span>{{ $t('email') }}</div>
                <mt-input
                  v-model="headerInfo.supplierEmail"
                  :disabled="this.$route.query.type != 2"
                  :placeholder="$t('请输入email')"
                ></mt-input>
              </div>
            </div>
            <!-- <div class="table-box">
              <mt-data-grid
                :data-source="supplierSummary"
                :column-data="SupplierSummaryColumns"
              ></mt-data-grid>
            </div> -->
          </div>
        </div>
        <div class="title">
          <span>{{ $t('差异调整项') }}</span>
        </div>
        <div class="row-box">
          <!-- 采方差异调整项 -->
          <div class="item-box">
            <div class="table-box">
              <mt-template-page
                ref="purchaseAdjustRef"
                class="template-height"
                :template-config="purchaseAdjustConfig"
              />
            </div>
          </div>
          <div class="dividing-line"></div>
          <!-- 供方差异调整项 -->
          <div class="item-box" v-if="isShowSupAdj">
            <div class="table-box">
              <mt-template-page
                ref="supplierAdjustRef"
                class="template-height"
                :template-config="supplierAdjustConfig"
                @handleClickToolBar="supplierAdjustClickToolBar"
                @actionBegin="supplierAdjustActionBegin"
                @actionComplete="supplierAdjustActionComplete"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 采方-往来明细 -->
    <div class="flex-fit" v-show="currentTabInfo.code === Tab.purchaseDetails">
      <div style="background: white; display: flex; flex-direction: row">
        <div style="margin-left: 20px; padding-top: 20px">
          {{ $t('期初余额：') }}{{ headerInfo.openingBalance }}
        </div>
        <div style="margin-left: 20px; padding-top: 20px">
          {{ $t('期末余额：') }}{{ headerInfo.closingBalance }}
        </div>
      </div>
      <mt-template-page
        ref="purchaseDetailsRef"
        class="template-height"
        :template-config="purchaseComponentConfig"
        @handleClickToolBar="purchaseDetailClickToolBar"
      />
    </div>
    <!-- 供方-往来明细 -->
    <div class="flex-fit" v-show="currentTabInfo.code === Tab.supplierDetails">
      <div style="background: white; display: flex; flex-direction: row">
        <div style="margin-left: 20px; padding-top: 20px">
          {{ $t('期初余额：') }}{{ headerInfo.supplierOpeningBalance }}
        </div>
        <div style="margin-left: 20px; padding-top: 20px">
          {{ $t('期末余额：') }}{{ headerInfo.supplierClosingBalance }}
        </div>
      </div>
      <mt-template-page
        ref="supplierDetailsRef"
        class="template-height"
        :template-config="supplierComponentConfig"
        @handleClickToolBar="supplierDetailClickToolBar"
        @actionBegin="supplierDetailActionBegin"
        @actionComplete="supplierDetailActionComplete"
      />
    </div>
    <!-- 相关附件 -->
    <div class="flex-fit" v-show="currentTabInfo.code === Tab.file">
      <relative-file
        ref="relativeFileRef"
        :doc-id="relativeFileData.docId"
        :request-url-obj="relativeFileData.requestUrlObj"
        :module-file-list="moduleFileList"
        :is-view="relativeFileData.isView"
      ></relative-file>
    </div>
    <!-- 操作日志 -->
    <div class="flex-fit" v-show="currentTabInfo.code === Tab.log">
      <mt-template-page
        ref="logRef"
        class="template-height"
        :template-config="logComponentConfig"
      />
    </div>
    <!-- 供方-往来明细导入弹框 -->
    <upload-excel-dialog
      ref="supplierDetailsUploadExcelRef"
      :down-template-params="supplierDetailsDownTemplateParams"
      :upload-params="supplierDetailsUploadParams"
      :request-urls="supplierDetailsRequestUrls"
      @closeUploadExcel="showSupplierDetailsUploadExcel(false)"
      @upExcelConfirm="supplierDetailsUpExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import {
  Tab,
  TabList,
  DetailsColumnData,
  SupplierDetailsColumnData,
  EntryType,
  PurchaseSummaryColumns,
  SupplierSummaryColumns,
  LogColumnData,
  AdjustColumnData,
  EditSettings,
  RequestType,
  ActionType,
  SupplierAdjustNewRowData,
  SupplierDetailNewRowData
} from './config/constant'
import {
  formatDetailsTableColumnData,
  formatLogTableColumnData,
  formatAdjustTableColumnData
} from './config/index'
import { rowDataTemp } from './config/variable'
import bigDecimal from 'js-big-decimal'
import { BASE_TENANT, maxPageSize } from '@/utils/constant'
import { cloneDeep } from 'lodash'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue'),
    RelativeFile: () => import('@/components/businessComponents/relativeFile/index.vue'),
    UploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    const lastTabIndex = JSON.parse(localStorage.getItem('lastTabIndex')) ?? 0
    const entryType = this.$route?.query?.type // 从URL获取页面状态
    const statementId = this.$route?.query?.id // 从URL获取对账单id
    const code = this.$route?.query?.code // 从URL获取对账单code
    let supplierAdjustToolbar = [] // 供方-差异调整 操作按钮
    let supplierDetailToolbar = [
      {
        id: 'SupplierDetailExport',
        icon: 'icon_solid_Import',
        title: this.$t('导出')
      }
    ]
    let purchaseDetailToolbar = [
      {
        id: 'PurchaseDetailExport',
        icon: 'icon_solid_Import',
        title: this.$t('导出')
      }
    ]
    // 供方-往来明细 操作按钮
    if (entryType == EntryType.edit) {
      // 页面显示类型 = 编辑
      supplierAdjustToolbar = [
        {
          id: 'SupplierAdjustAdd',
          icon: 'icon_table_new',
          title: this.$t('新增')
        },
        {
          id: 'SupplierAdjustDelete',
          icon: 'icon_table_delete',
          title: this.$t('删除')
        },
        {
          id: 'SupplierAdjustUpdate',
          icon: 'icon_table_save',
          title: this.$t('更新')
        }
      ]
      supplierDetailToolbar = [
        {
          id: 'SupplierDetailSave',
          icon: 'icon_table_save',
          title: this.$t('保存')
        },
        {
          id: 'SupplierDetailAdd',
          icon: 'icon_table_new',
          title: this.$t('新增')
        },
        {
          id: 'SupplierDetailDelete',
          icon: 'icon_table_delete',
          title: this.$t('删除')
        },
        {
          id: 'SupplierDetailUpdate',
          icon: 'icon_table_save',
          title: this.$t('更新')
        },
        {
          id: 'SupplierDetailImport',
          icon: 'icon_solid_Import',
          title: this.$t('导入')
        },
        {
          id: 'SupplierDetailExport',
          icon: 'icon_solid_Import',
          title: this.$t('导出')
        }
      ]
    }

    return {
      Tab,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      headerInfo: {}, // 主单信息
      entryType: entryType || EntryType.view, // 页面状态，默认：查看
      selectedItem: entryType === 3 ? 3 : 0,
      statementId, // 对账单id
      totalDiff: 0, // 对账总差异
      lastTabIndex, // 上一页面的 tabIndex，返回时传递参数
      currentTabInfo: entryType === 3 ? TabList[3] : TabList[0], // 当前tab的数据，默认选中第一项
      tabList: TabList,
      buyerSummary: [], // 采方科目类别汇总
      supplierSummary: [], // 供方科目类别汇总
      hasChangeSupplierDetails: false, // 修改了供方往来明细，回到金额汇总tab时重新获取主单明细数据
      moduleFileList: [
        // {
        //   id: "trans_reconciliation_header_buyer", // 选中时传给api的 doctype 的值
        //   nodeName: this.$t("采方-整单附件"), // 侧边栏名称
        //   nodeCode: 0, // 即当前 index 用于选择左侧边栏时 获取当前项 配置
        //   btnRequired: {
        //     hasUpload: false,
        //     hasDownload: true,
        //     hasDelete: false,
        //   }, // 表格 toolbar 配置（可选） hasUpload: Boolean、hasDownload: Boolean、 hasDelete: Boolean
        //   hasItem: false, // 是否显示附件行数据（可选）
        // },
        {
          id: 'trans_reconciliation_header_supplier', // 选中时传给api的 doctype 的值
          nodeName: this.$t('供方-整单附件'), // 侧边栏名称
          nodeCode: 0, // 即当前 index 用于选择左侧边栏时 获取当前项 配置
          btnRequired: {
            hasUpload: true,
            hasDownload: true,
            hasDelete: true,
            hasPrint: true
          }, // 表格 toolbar 配置（可选） hasUpload: Boolean、hasDownload: Boolean、 hasDelete: Boolean
          hasItem: false, // 是否显示附件行数据（可选）
          deleteFileUrl: `${BASE_TENANT}/transaction_reconciliation/files/deleteSupplierFile`, // 删除文件使用的 API 根据ID删除
          deleteFileRequestMethod: 'delete' // 设置删除api的请求方法
        }
      ], // 相关附件 左侧菜单
      // 相关附件配置
      relativeFileData: {
        docId: statementId,
        requestUrlObj: {
          preUrl: 'reconciliationSettlement',
          saveUrl: 'postTransactionReconciliationFilesSupplierSave', // 将附件文件url保存到列表 保存文件信息
          fileUrl: `${BASE_TENANT}/transaction_reconciliation/files/queryList` // 获取附件列表的url 根据docType和docID查询所有文件信息
        },
        isView: entryType == EntryType.view || entryType == EntryType.edit // 查看的状态 || 待反馈状态，没有上传和删除
      },
      PurchaseSummaryColumns, // 金额汇总-采方对账科目 表格
      SupplierSummaryColumns, // 金额汇总-供方对账科目 表格
      supplierDetailsDownTemplateParams: {
        pageFlag: false,
        belong: 1, // 数据归属，0-采方,1-供方
        headerId: statementId,
        page: {}
      }, // 通知配置导入下载模板参数
      supplierDetailsUploadParams: {
        headerId: statementId
      }, // 导入通知配置文件参数
      // 通知配置导入请求接口配置
      supplierDetailsRequestUrls: {
        templateUrlPre: 'reconciliationCollaboration',
        templateUrl: 'postTransactionReconciliationItemSupplierDownload', // 下载模板接口方法名
        uploadUrl: 'postTransactionReconciliationItemSupplierUpload' // 上传接口方法名
      },
      // 采方-差异调整
      purchaseAdjustConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [], // 采方-差异调整 操作按钮
          grid: {
            height: '300', // 固定高度
            editSettings: {
              allowEditing: false // 不可编辑采方差异调整
            },
            allowPaging: false, // 不分页
            columnData: formatAdjustTableColumnData({
              data: AdjustColumnData.filter((item) => item.fieldCode !== 'checkBox')
            }),
            dataSource: [],
            // 租户级-往来对账单差异调整项接口-分页查询
            asyncConfig: {
              url: `${BASE_TENANT}/transaction-reconciliation-adjust/paged-query`,
              params: {
                headerId: statementId,
                page: {
                  current: 1,
                  size: maxPageSize
                }
              },
              defaultRules: [
                {
                  field: 'belong', // 数据归属，0-采方,1-供方
                  operator: 'equal',
                  value: 0
                }
              ],
              serializeList: this.serializeAdjustList
            }
          }
        }
      ],
      // 供方-差异调整
      supplierAdjustConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: supplierAdjustToolbar, // 供方-差异调整 操作按钮
          grid: {
            height: '300', // 固定高度
            editSettings: {
              ...EditSettings,
              allowEditing: entryType == EntryType.edit
            },
            allowPaging: false, // 不分页
            columnData: formatAdjustTableColumnData({
              data: AdjustColumnData
            }),
            dataSource: [],
            // 租户级-往来对账单差异调整项接口-分页查询
            asyncConfig: {
              url: `${BASE_TENANT}/transaction-reconciliation-adjust/paged-query`,
              params: {
                headerId: statementId,
                page: {
                  current: 1,
                  size: maxPageSize
                }
              },
              defaultRules: [
                {
                  field: 'belong', // 数据归属，0-采方,1-供方
                  operator: 'equal',
                  value: 1
                }
              ],
              serializeList: this.serializeAdjustList
            }
          }
        }
      ],
      // 采方-往来明细
      purchaseComponentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: purchaseDetailToolbar, // 供方-往来明细 操作按钮
          gridId:
            this.$tableUUID.reconciliationCollaboration.contactDetailSupplier.purchaserDetailsTab,
          grid: {
            editSettings: {
              allowEditing: false
            },
            allowPaging: true, // 分页
            // lineIndex: 0,
            columnData: formatDetailsTableColumnData({
              data: DetailsColumnData.filter((item) => item.fieldCode !== 'checkBox')
            }),
            dataSource: [],
            // 分页查询特定对账单的明细
            asyncConfig: {
              url: `${BASE_TENANT}/transaction_reconciliation_item_supplier/page-query`,
              params: {
                belong: 0, // 数据归属，0-采方,1-供方
                headerId: statementId
              },
              defaultRules: [],
              serializeList: this.serializeDetailList
            }
          }
        }
      ],
      // 供方-往来明细
      supplierComponentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: supplierDetailToolbar, // 供方-往来明细 操作按钮
          gridId:
            this.$tableUUID.reconciliationCollaboration.contactDetailSupplier.supplierDetailsTab,
          grid: {
            editSettings: {
              ...EditSettings,
              allowEditing: entryType == EntryType.edit
            },
            allowPaging: true, // 分页
            // lineIndex: 0,
            columnData: formatDetailsTableColumnData({
              data: SupplierDetailsColumnData
            }),
            dataSource: [],
            // 分页查询特定对账单的明细
            asyncConfig: {
              url: `${BASE_TENANT}/transaction_reconciliation_item_supplier/page-query`,
              params: {
                belong: 1, // 数据归属，0-采方,1-供方
                headerId: statementId
              },
              defaultRules: [],
              serializeList: this.serializeDetailList
            }
          }
        }
      ],
      // 操作日志
      logComponentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          gridId: this.$tableUUID.reconciliationCollaboration.contactDetailSupplier.operationLog,
          grid: {
            editSettings: {
              allowEditing: false
            },
            allowPaging: true, // 分页
            lineIndex: 0,
            columnData: formatLogTableColumnData({
              data: LogColumnData
            }),
            dataSource: [],
            // 租户级-往来对账单操作日志接口-分页查询
            asyncConfig: {
              url: `${BASE_TENANT}/log/queryOperationLog`,
              params: {
                reconciliationCode: code,
                reconciliationType: 4
              },
              defaultRules: []
            }
          }
        }
      ],
      companyCode: '',
      companyCodeList: []
    }
  },
  computed: {
    isShowSupAdj() {
      return !this.companyCode.includes(this.companyCodeList)
    }
  },
  mounted() {
    // 获取对账单详情数据
    this.getDetailsData()
  },
  beforeDestroy() {
    localStorage.removeItem('lastTabIndex')
  },
  methods: {
    // 获取对账单详情数据
    getDetailsData() {
      if (this.statementId) {
        // 如果URL存在对账单ID
        this.apiStartLoading()
        this.$API.reconciliationCollaboration
          .postTransactionReconciliationHeaderSupplierDetail({
            id: this.statementId
          })
          .then((res) => {
            this.apiEndLoading()
            this.hasChangeSupplierDetails = false
            const data = res?.data || {}
            this.headerInfo = data
            this.headerInfo.supplierOpeningBalance = 0 - data.openingBalance
            // 截止时间 格式转换
            let deadLine = null
            if (data.deadLine) {
              deadLine = new Date(Number(data.deadLine))
            }
            this.headerInfo.deadLine = deadLine // 截止时间
            // 采方金额汇总
            this.buyerSummary = data.buyerSummary || []
            // 供方金额汇总
            this.supplierSummary = data.supplierSummary || []
            // 计算对账总差异
            const closingBalance = this.headerInfo.closingBalance || 0 // 采方期末金额
            const supplierClosingBalance = this.headerInfo.supplierClosingBalance || 0 // 供方期末金额
            // 对账总差异 = 采方期末金额 + 供方期末金额
            this.totalDiff = bigDecimal.add(closingBalance, supplierClosingBalance)
            this.companyCode = data.companyCode
            this.getAdjDisplay(data.companyCode)
          })
          .catch(() => {
            this.apiEndLoading()
          })
      }
    },
    // 供方差异调整项 ToolBar
    supplierAdjustClickToolBar(args) {
      const { grid, toolbar } = args
      const selectRows = grid.getSelectedRecords()
      const commonToolbar = [
        'SupplierAdjustAdd',
        'SupplierAdjustUpdate',
        'more-option-btn',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]
      if (selectRows.length === 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedIds = []
      selectRows.forEach((item) => {
        selectedIds.push(item.id)
      })

      if (toolbar.id === 'SupplierAdjustAdd') {
        // 供方差异调整项-新增
        this.$refs.supplierAdjustRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id === 'SupplierAdjustUpdate') {
        // 供方差异调整项-更新 结束编辑
        this.$refs.supplierAdjustRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      } else if (toolbar.id === 'SupplierAdjustDelete') {
        // 供方差异调整项-删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            // 供方差异调整项-删除
            this.handleSupplierAdjustDelete({ selectRows, selectedIds })
          }
        })
      }
    },
    // 供方差异调整项-删除
    handleSupplierAdjustDelete(args) {
      const { selectedIds } = args
      // 租户级-往来对账单差异调整项接口-批量删除
      this.deleteTransactionReconciliationAdjustBatchDelete({
        ids: selectedIds
      })
    },
    // 租户级-往来对账单差异调整项接口-批量删除
    deleteTransactionReconciliationAdjustBatchDelete(params) {
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .deleteTransactionReconciliationAdjustBatchDelete(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const data = res?.data || {}
            // 通过响应更新差异调整项
            this.upDateTotalDiffByResponse(data)

            this.$toast({ content: this.$t('操作成功'), type: 'success' })

            // 刷新 Grid
            this.$refs.supplierAdjustRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    //采方往来明细 ToolBar
    purchaseDetailClickToolBar(args) {
      if (args.toolbar.id === 'PurchaseDetailExport') {
        // 供方往来明细-导出
        this.purchaseDetailExport()
      }
    },
    // 供方往来明细 ToolBar
    supplierDetailClickToolBar(args) {
      const { grid, toolbar } = args
      const selectRows = grid.getSelectedRecords()
      const commonToolbar = [
        'SupplierDetailAdd',
        'SupplierDetailUpdate',
        'SupplierDetailImport',
        'SupplierDetailExport',
        'more-option-btn',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal',
        'SupplierDetailSave'
      ]
      if (this.isEditing && toolbar.id === 'SupplierDetailSave') {
        // 结束编辑状态
        this.$refs.supplierDetailsRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }
      if (selectRows.length === 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedIds = []
      selectRows.forEach((item) => {
        selectedIds.push(item.id)
      })

      if (toolbar.id === 'SupplierDetailAdd') {
        // 供方往来明细-新增
        this.$refs.supplierDetailsRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id === 'SupplierDetailUpdate') {
        // 供方往来明细-更新 结束编辑
        this.$refs.supplierDetailsRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      } else if (toolbar.id === 'SupplierDetailDelete') {
        // 供方往来明细-删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            // 供方往来明细-删除
            this.handleSupplierDetailDelete({ selectRows, selectedIds })
          }
        })
      } else if (toolbar.id === 'SupplierDetailImport') {
        // 供方往来明细-导入
        this.showSupplierDetailsUploadExcel(true)
      } else if (toolbar.id === 'SupplierDetailExport') {
        // 供方往来明细-导出
        this.supplierDetailExport()
      } else if (toolbar.id === 'SupplierDetailSave') {
        // 此保存只作失焦处理，不作实际保存操作
        if (!this.isEditing) {
          this.$toast({
            content: this.$t('数据未更新，无需保存'),
            type: 'warning'
          })
        }
      }
    },
    // 供方往来明细-删除
    handleSupplierDetailDelete(args) {
      const { selectedIds } = args
      // 租户级-往来对账明细供应商控制器-批量删除
      this.deleteTransactionReconciliationItemSupplierBatchDelete({
        ids: selectedIds
      })
    },
    // 租户级-往来对账明细供应商控制器-批量删除
    deleteTransactionReconciliationItemSupplierBatchDelete(params) {
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .deleteTransactionReconciliationItemSupplierBatchDelete(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.hasChangeSupplierDetails = true // 修改了供方往来明细，回到金额汇总tab时重新获取主单明细数据
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 Grid
            this.$refs.supplierDetailsRef.refreshCurrentGridData()
            this.updateBalance()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 差异调整项 数据序列化
    serializeAdjustList(list) {
      if (list.length > 0) {
        list.forEach((item, index) => {
          item.serialNumber = index + 1 // 序号
          item.thePrimaryKey = item.id // 主键
        })
      }
      return list
    },
    // 往来明细 数据序列化
    serializeDetailList(list) {
      if (list.length > 0) {
        list.forEach((item, index) => {
          item.serialNumber = index + 1 // 序号
          item.thePrimaryKey = item.id // 主键
        })
      }
      return list
    },
    // 供方差异调整项 actionBegin 表格编辑生命周期
    supplierAdjustActionBegin(args) {
      const { requestType, action, rowData } = args
      // console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args);
      if (requestType === RequestType.add) {
        // 开始行添加
        this.isEditing = true
        // 即将添加一行，赋值新增行的初始数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        const newRowData = cloneDeep(SupplierAdjustNewRowData)
        newRowData.headerId = this.statementId // 主单id
        rowDataTemp.push(newRowData)
        args.rowData = newRowData
        args.data = newRowData
      } else if (requestType === RequestType.save && action === ActionType.add) {
        // 即将保存新增时，将新增中编辑的数据付给组件
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === RequestType.beginEdit) {
        // 开始行编辑
        this.isEditing = true
        // 即将编辑行，赋值当前行的数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },
    // 供方差异调整项 actionComplete 表格编辑生命周期
    supplierAdjustActionComplete(args) {
      const { requestType, action, rowIndex, index } = args
      const rowData = rowDataTemp[rowDataTemp.length - 1]
      // console.log(
      //   `Complete,\nrequest: ${requestType}\naction: ${action}`,
      //   args
      // );
      if (requestType === RequestType.save && action == ActionType.edit) {
        // 完成编辑行
        this.isEditing = false
        // rowData, rowIndex
        if (!this.isValidAdjustSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.supplierAdjustRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.supplierAdjustRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API 租户级-往来对账单差异调整项接口-新增或修改
          this.postTransactionReconciliationAdjustBatchSave({
            rowData,
            rowIndex
          })
        }
      } else if (requestType === RequestType.save && action == ActionType.add) {
        // 完成新增行
        this.isEditing = false
        if (!this.isValidAdjustSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.supplierAdjustRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
          this.$refs.supplierAdjustRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API 租户级-往来对账单差异调整项接口-新增或修改
          this.postTransactionReconciliationAdjustBatchSave({
            rowData,
            rowIndex: index
          })
        }
      }
    },
    // 供方往来明细 actionBegin 表格编辑生命周期
    supplierDetailActionBegin(args) {
      const { requestType, action, rowData } = args
      console.log(`Complete,\nrequest: ${requestType}\naction: ${action}`, args)
      // console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args);
      if (requestType === RequestType.add) {
        // 开始行添加
        this.isEditing = true
        // 即将添加一行，赋值新增行的初始数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        const newRowData = cloneDeep(SupplierDetailNewRowData)
        newRowData.headerId = this.statementId // 主单id
        rowDataTemp.push(newRowData)
        args.rowData = newRowData
        args.data = newRowData
      } else if (requestType === RequestType.save && action === ActionType.add) {
        // 即将保存新增时，将新增中编辑的数据付给组件
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === RequestType.beginEdit) {
        // 开始行编辑
        this.isEditing = true
        // 即将编辑行，赋值当前行的数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },
    // 供方往来明细 actionComplete 表格编辑生命周期
    supplierDetailActionComplete(args) {
      const { requestType, action, rowIndex, index } = args
      const rowData = rowDataTemp[rowDataTemp.length - 1]

      if (requestType === RequestType.save && action == ActionType.edit) {
        // 完成编辑行
        this.isEditing = false
        // rowData, rowIndex
        if (!this.isValidDetailSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.supplierDetailsRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.supplierDetailsRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API 租户级-往来对账明细供应商控制器-新增或修改
          this.postTransactionReconciliationItemSupplierSave({
            rowData,
            rowIndex
          })
        }
      } else if (requestType === RequestType.save && action == ActionType.add) {
        // 完成新增行
        this.isEditing = false
        if (!this.isValidDetailSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.supplierDetailsRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
          this.$refs.supplierDetailsRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API 租户级-往来对账明细供应商控制器-新增或修改
          this.postTransactionReconciliationItemSupplierSave({
            rowData,
            rowIndex: index
          })
        }
      }
    },
    // 校验 差异调整项 数据
    isValidAdjustSaveData(data) {
      const { adjustType, amount } = data
      let valid = true
      if (!adjustType) {
        this.$toast({ content: this.$t('请选择类型'), type: 'warning' })
        valid = false
      } else if (!amount) {
        this.$toast({ content: this.$t('请填写调整金额'), type: 'warning' })
        valid = false
      }

      return valid
    },
    // 校验 往来明细 数据
    isValidDetailSaveData(data) {
      const { accountDate, amount, type, claimDemageNo, remark, invoiceNo } = data
      let valid = true
      if (!accountDate) {
        this.$toast({ content: this.$t('请选择入账日期'), type: 'warning' })
        valid = false
      } else if (!amount && amount !== 0) {
        this.$toast({ content: this.$t('请填写金额'), type: 'warning' })
        valid = false
      } else if (amount || amount === 0) {
        const _message = this.validAmount(data)
        if (_message) {
          this.$toast({ content: _message, type: 'warning' })
          valid = false
        }
      }
      if (type == '9') {
        if (!remark) {
          this.$toast({
            content: this.$t('类别选择其它时，备注必填'),
            type: 'warning'
          })
          valid = false
        }
      }
      if (type == '5') {
        if (!claimDemageNo) {
          this.$toast({
            content: this.$t('类型选择扣款时，索赔单号必填'),
            type: 'warning'
          })
          valid = false
        }
      }
      if (type == '4' || type == '7') {
        if (!invoiceNo) {
          this.$toast({
            content: this.$t('类型选择开发票给TCL或收到TCL开具的发票,发票号必填'),
            type: 'warning'
          })
          valid = false
        }
      }

      return valid
    },
    // 根据对账类型校验金额
    validAmount(data) {
      const { type, amount } = data
      let _message = ''

      if ((type === 3 || type === 4) && amount <= 0) {
        //保证金、开发票给TCL金额需要大于0
        _message = this.$t('往来类别为保证金或开发票给TCL时，金额需大于0')
      }
      if ((type === 5 || type === 6 || type === 7) && amount >= 0) {
        //扣款、收款、收到TCL开具的发票金额需要小于0
        _message = this.$t('往来类别为扣款、收款或收到TCL开具的发票时，金额需小于0')
      }
      return _message
    },
    // 租户级-往来对账单差异调整项接口-新增或修改
    postTransactionReconciliationAdjustBatchSave(args) {
      const { rowData, rowIndex } = args
      const params = {
        ...rowData,
        id: rowData.id || undefined, // 没有时即为新增
        thePrimaryKey: undefined
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationAdjustBatchSave(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const data = res?.data || {}
            if (data.header) {
              // 通过响应更新差异调整项
              this.upDateTotalDiffByResponse(data.header)
            }

            this.$toast({ content: this.$t('操作成功'), type: 'success' })

            // 刷新 Grid
            this.$refs.supplierAdjustRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.supplierAdjustRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.supplierAdjustRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    // 租户级-往来对账明细供应商控制器-新增或修改
    postTransactionReconciliationItemSupplierSave(args) {
      const { rowData, rowIndex } = args
      const params = {
        ...rowData,
        accountDate: Number(rowData.accountDate), // 入账日期 转时间戳
        id: rowData.id || undefined, // 没有时即为新增
        thePrimaryKey: undefined
      }
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .postTransactionReconciliationItemSupplierSave(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.hasChangeSupplierDetails = true // 修改了供方往来明细，回到金额汇总tab时重新获取主单明细数据
            this.$toast({ content: this.$t('操作成功'), type: 'success' })

            // 刷新 Grid
            this.$refs.supplierDetailsRef.refreshCurrentGridData()
            this.updateBalance()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.supplierDetailsRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.supplierDetailsRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    // 更新期初期末余额
    updateBalance() {
      this.$API.reconciliationCollaboration
        .queryBalanceById({ id: this.headerInfo.id })
        .then((res) => {
          if (res?.code == 200) {
            this.headerInfo.supplierClosingBalance = res.data.supplierClosingBalance
          }
        })
    },
    // 通过响应更新差异调整项
    upDateTotalDiffByResponse(data) {
      // 计算对账总差异
      this.headerInfo.closingBalance = data?.closingBalance // 采方期末金额
      this.headerInfo.supplierClosingBalance = data?.supplierClosingBalance // 供方期末金额
      const closingBalance = this.headerInfo.closingBalance || 0 // 采方期末金额
      const supplierClosingBalance = this.headerInfo.supplierClosingBalance || 0 // 供方期末金额
      // 对账总差异 = 采方期末金额 + 供方期末金额
      this.totalDiff = bigDecimal.add(closingBalance, supplierClosingBalance)
    },
    // 提交反馈
    doSubmit() {
      if (!this.headerInfo.supplierContact) {
        this.$toast({ content: this.$t('请填写制表人'), type: 'warning' })
        return
      }
      if (!this.headerInfo.supplierPhone) {
        this.$toast({ content: this.$t('请填写电话'), type: 'warning' })
        return
      }
      if (!this.headerInfo.supplierEmail) {
        this.$toast({ content: this.$t('请填写email'), type: 'warning' })
        return
      }
      const params = {
        supplierContact: this.headerInfo.supplierContact,
        supplierPhone: this.headerInfo.supplierPhone,
        supplierEmail: this.headerInfo.supplierEmail,
        id: this.statementId, // 对账单Id
        supRemark: this.headerInfo.supRemark // 供方备注
      }
      // 租户级-往来对账单供应商控制器-提交反馈
      this.postTransactionReconciliationHeaderSupplierFeedback(params)
    },
    // 租户级-往来对账单供应商控制器-提交反馈
    postTransactionReconciliationHeaderSupplierFeedback(params) {
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .postTransactionReconciliationHeaderSupplierFeedback(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 返回
            this.goBack()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 显示隐藏上传弹框
    showSupplierDetailsUploadExcel(flag) {
      if (flag) {
        this.$refs.supplierDetailsUploadExcelRef.uploadData = null // 清空数据
        this.$refs.supplierDetailsUploadExcelRef.fileLength = 0
        this.$refs.supplierDetailsUploadExcelRef.$refs.uploader.files = []
        this.$refs.supplierDetailsUploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.supplierDetailsUploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 导入成功后
    supplierDetailsUpExcelConfirm() {
      this.showSupplierDetailsUploadExcel(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
      this.$refs.supplierDetailsRef.refreshCurrentGridData()
    },
    // 采方-导出
    purchaseDetailExport() {
      const params = {
        pageFlag: true,
        page: { current: 1, size: 10000 },
        headerId: this.headerInfo.id,
        defaultRules: []
      } // 筛选条件
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .puchaseDetailExport(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方-导出
    supplierDetailExport() {
      const params = {
        pageFlag: true,
        page: { current: 1, size: 10000 },
        headerId: this.headerInfo.id,
        defaultRules: []
      } // 筛选条件
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .supplierDetailExport(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-往来对账明细供应商控制器-下载
    postTransactionReconciliationItemSupplierDownload() {
      const queryBuilderRules =
        this.$refs.supplierDetailsRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules,
        belong: 1, // 数据归属，0-采方,1-供方
        headerId: this.statementId
      } // 筛选条件
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .postTransactionReconciliationItemSupplierDownload(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
    },
    // 返回
    goBack() {
      // 将 tabIndex 放到 localStorage 往来对账单列表-供方 读
      localStorage.setItem('tabIndex', JSON.stringify(this.lastTabIndex))
      // 返回 往来对账单列表-供方
      if (this.$route.query.come) {
        this.$router.push({
          name: 'contact-reconciled-supplier-query',
          query: {}
        })
      } else {
        this.$router.push({
          name: 'contact-reconciled-supplier',
          query: {}
        })
      }
    },
    // 点击 tab
    handleSelectTab(e) {
      this.currentTabInfo = this.tabList[e]
      if (this.currentTabInfo.code === Tab.summary && this.hasChangeSupplierDetails) {
        // 修改了供方往来明细，回到金额汇总tab时重新获取主单明细数据
        this.getDetailsData()
      }
    },
    doExpand() {},
    // 获取字典项
    getAdjDisplay(customerCode) {
      this.$API.masterData
        .getAuthFindDictItemByCustomerCodeAndDictCode({
          customerCode,
          dictCode: 'ACCT_CELAR_ADJ_UNDISPLAY'
        })
        .then((res) => {
          this.companyCodeList = res.data.map((item) => item.itemCode) || []
        })
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.summary {
  background-color: var(--plugin-dg-bg-ff);
  height: 100%;
  overflow: auto;
  .box {
    flex-direction: column;
    .title {
      margin: 24px 0px 24px 24px;
      padding-left: 8px;
      border-left: 4px solid #3369ac;
      font-weight: 600;
    }
    .tips {
      margin: 24px 0px 24px 60px;
      .icon {
        padding-right: 8px;
        color: #6487bf;
      }
    }
    .row-box {
      flex: 1 1 auto;
      display: flex;
      position: relative;
      .item-box {
        width: calc(50% - 1px); // 两项 共计 2px 预留给中间的虚线
        &.empty {
          display: flex;
          justify-content: center;
          flex-direction: column;
          min-height: 200px;
          align-items: center;
          color: #a6a6a6;
        }

        .top {
          display: flex;
          justify-content: space-between;
          margin: 12px 32px 24px 32px;
          .item {
            width: 32%;
          }
        }
        .table-box {
          margin: 12px 32px 0px 32px;
        }
      }
      .dividing-line {
        border-left: 2px dashed #d9d9d9;
      }
    }
  }
}
</style>
