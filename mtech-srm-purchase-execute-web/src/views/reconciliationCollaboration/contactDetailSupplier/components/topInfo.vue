<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div :class="[statusClass(headerInfo.status), 'mr20']">
        {{ headerInfo.status | statusFormat }}
      </div>
      <div class="infos mr20">{{ $t('对账单号：') }}{{ headerInfo.code }}</div>
      <div class="infos mr20">{{ $t('创建人：') }}{{ headerInfo.createUserName }}</div>
      <div class="infos">{{ $t('创建时间：') }}{{ headerInfo.createTime | dateFormat }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        @click="doSubmit"
        v-if="entryType == EntryType.edit || entryType == EntryType.upload"
        >{{ $t('提交') }}</mt-button
      >

      <div class="sort-box" @click="doExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom" v-if="headerInfo">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="companyName" :label="$t('公司')">
          <mt-input v-model="headerInfo.companyName" :disabled="true" placeholder=""></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplierName" :label="$t('供应商/客户名称')">
          <mt-input v-model="headerInfo.supplierName" :disabled="true" placeholder=""></mt-input>
        </mt-form-item>

        <mt-form-item prop="currencyName" :label="$t('币种')">
          <mt-input v-model="headerInfo.currencyName" :disabled="true" placeholder=""></mt-input>
        </mt-form-item>

        <mt-form-item prop="deadLine" :label="$t('对账月份')">
          <mt-date-picker
            v-model="deadLine"
            :disabled="true"
            :show-clear-button="true"
            :allow-edit="false"
            placeholder=""
          ></mt-date-picker>
        </mt-form-item>

        <!-- <mt-form-item prop="openingBalance" :label="$t('期初余额')">
          <mt-input
            v-model="headerInfo.openingBalance"
            :disabled="true"
            placeholder=""
          ></mt-input>
        </mt-form-item> -->

        <mt-form-item prop="remark" :label="$t('采方备注')" class="full-width">
          <mt-input v-model="headerInfo.remark" :disabled="true" placeholder=""></mt-input>
        </mt-form-item>

        <mt-form-item prop="supRemark" :label="$t('供方备注')" class="full-width">
          <mt-input
            v-model="headerInfo.supRemark"
            :disabled="entryType != EntryType.edit && entryType != EntryType.upload"
            placeholder=""
            maxlength="200"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { EntryType, ReconciledStatusCssClass, ReconciledStatusText } from '../config/constant'

export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    },
    entryType: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      isExpand: true,
      rules: {},
      topInfo: {},
      EntryType
    }
  },
  mounted() {},
  computed: {
    deadLine() {
      let value = this.headerInfo.deadLine
      return timeStringToDate({ formatString: 'YYYY-mm', value })
    }
  },
  filters: {
    dateFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd HH:MM:SS', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd HH:MM:SS', value })
      }

      return str
    },
    statusFormat(value) {
      if (!ReconciledStatusText[value]) {
        return value
      } else {
        return ReconciledStatusText[value]
      }
    }
  },
  methods: {
    statusClass(value) {
      let cssClass = ''
      if (ReconciledStatusCssClass[value]) {
        cssClass = ReconciledStatusCssClass[value]
      }
      return cssClass
    },
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 提交
    doSubmit() {
      this.$emit('doSubmit')
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(25% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
