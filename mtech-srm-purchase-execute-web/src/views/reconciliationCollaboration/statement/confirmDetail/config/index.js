import Vue from 'vue'
import utils from '@/utils/utils'
import { timeDate, requiredHeader } from './columnComponent'
import { i18n } from '@/main.js'
import { Query } from '@syncfusion/ej2-data'
import dayjs from 'dayjs'
// 高开/低开
const TypeDropData = [
  {
    text: i18n.t('补差'),
    value: 0
  },
  {
    text: i18n.t('返利'),
    value: 1
  },
  {
    text: i18n.t('其他'),
    value: 2
  },
  {
    text: i18n.t('应付返工费用'),
    value: 3
  },
  {
    text: i18n.t('应扣住宿费用'),
    value: 4
  },
  {
    text: i18n.t('应扣考核费用'),
    value: 5
  },
  {
    text: i18n.t('外包借入工时费用'),
    value: 6
  },
  {
    text: i18n.t('外包借出工时费用'),
    value: 7
  },
  {
    text: i18n.t('损耗率返利'),
    value: 8
  }
]
// 对账单确认 页面类型
export const ConstantConfirmDetailType = {
  Confirm: '1', // 确认待反馈 编辑：附件、反馈意见，操作：接受、拒绝
  ConfirmClose: '2', // 确认关闭 不可编辑，操作：接受
  Edit: '3', // 编辑对账单 编辑：附件、备注，操作：提交
  Look: '4' // 查看
}

// 单据状态 -1:关闭 0:未发布 1:待反馈 2:反馈正常 3:反馈异常
export const ConstStatus = {
  close: -1,
  unpublished: 0,
  toBeConfirmed: 1,
  normal: 2,
  abnormal: 3
}

// 单据状态
export const Status = {
  [ConstStatus.close]: i18n.t('关闭'),
  [ConstStatus.unpublished]: i18n.t('未发布'),
  [ConstStatus.toBeConfirmed]: i18n.t('待反馈'),
  [ConstStatus.normal]: i18n.t('反馈正常'),
  [ConstStatus.abnormal]: i18n.t('反馈异常')
}

// 状态 对应的 css class
export const StatusCssClass = {
  [ConstStatus.close]: 'col-inactive',
  [ConstStatus.unpublished]: 'col-active',
  [ConstStatus.toBeConfirmed]: 'col-published',
  [ConstStatus.normal]: 'col-normal',
  [ConstStatus.abnormal]: 'col-abnormal'
}

// 表格 checkbox
export const ColumnCheckbox = {
  width: '50',
  type: 'checkbox',
  showInColumnChooser: false,
  visible: false // 不显示，无需使用 checkbox
}

// 时间戳转日期 string
export const stringToDate = (data) => {
  const { formatString, value } = data
  if (formatString && value) {
    const date = new Date(Number(value))
    if (isNaN(date.getTime())) {
      return value
    } else {
      return utils.formateTime(date, formatString)
    }
  } else {
    return value
  }
}

// tab code
export const TabCode = {
  highLowInfo: 'highLowInfo', // 高低开信息
  operationLog: 'operationLog', // 操作日志
  reconciliationFile: 'reconciliationFile', // 相关附件
  reconciliationField: 'reconciliationField', // 对账明细
  purHeaderFile: 'purHeaderFile', // 采方-整单附件
  supHeaderFile: 'supHeaderFile' // 供方-整单附件
}

export const formatTableColumnData = (data, businessTypeCode) => {
  let isFC = false
  if (
    businessTypeCode === 'BTTCL001' ||
    businessTypeCode === 'BTTCL002' ||
    businessTypeCode === 'BTTCL003'
  ) {
    isFC = true
  }
  const colData = []
  data.forEach((col) => {
    if (!col.checkStatus) return
    const defaultCol = {
      ...col,
      field: col.code,
      headerText: col.name,
      width: data.length > 10 ? '150' : 'auto',
      allowEditing: false
    }
    if (isFC) {
      // 非采情况下，字段名修改
      if (defaultCol.field === 'executeUntaxedUnitPrice') {
        // 采购未税单价
        defaultCol.headerText = i18n.t('采购未税单价')
      }
      if (defaultCol.field === 'executeUntaxedTotalPrice') {
        // 采购未税总价
        defaultCol.headerText = i18n.t('采购未税总价')
      }
      if (defaultCol.field === 'executeTaxedUnitPrice') {
        // 采购含税单价
        defaultCol.headerText = i18n.t('采购含税单价')
      }
      if (defaultCol.field === 'executeTaxedTotalPrice') {
        // 采购未税单价
        defaultCol.headerText = i18n.t('采购含税总价')
      }
      if (defaultCol.field === 'customProjectName') {
        // 采购未税单价
        defaultCol.headerText = i18n.t('寻源项目名称')
      }
    }
    if (defaultCol.field === 'advanceInvoicing') {
      // 提前开票
      defaultCol.valueConverter = {
        type: 'map',
        map: {
          0: i18n.t('否'),
          1: i18n.t('是')
        }
      }
    } else if (defaultCol.field === 'fileBaseInfoList') {
      // 文件信息
      defaultCol.template = () => {
        return {
          template: Vue.component('fileBaseInfoList', {
            template: `<div @click="showFileBaseInfo" class="cell-operable-title">{{data.fileBaseInfoList | listNumFormat}}</div>`,
            data: function () {
              return { data: {} }
            },
            filters: {
              listNumFormat(value) {
                if (value && value.length > 0) {
                  return value.length
                } else {
                  return ''
                }
              }
            },
            methods: {
              showFileBaseInfo() {
                this.$parent.$emit('showFileBaseInfo', {
                  index: this.data.index,
                  value: this.data.fileBaseInfoList
                })
              }
            }
          })
        }
      }
    } else if (defaultCol.field === 'remark') {
      // 反馈备注
      defaultCol.allowEditing = true
    } else if (defaultCol.field === 'sourceType') {
      // 来源类型（枚举） 0: 上游流入1:第三方接口
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('上游流入'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('第三方接口'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'receiveTime') {
      // 收货时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'status') {
      // 单据状态 0:待对账 1:已创建对账单
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('待对账'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('已创建对账单'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'syncStatus') {
      // 同步状态 0:否 1:是
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('否'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('是'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'frozenStatus') {
      // 冻结标记 0:否 1:是
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('否'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('是'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'prePayStatus') {
      // 是否预付 0-否；1-是
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('否'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('是'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'itemVoucherDate') {
      // 物料凭证日期
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'provisionalEstimateStatus') {
      // 是否暂估价 0-否；1-是 改成是否执行价
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('是'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('否'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'createTime') {
      // 创建时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'updateTime') {
      // 最后修改时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'type') {
      // 待对账类型:0-采购待对账；1-销售待对账
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('采购待对账'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('销售待对账'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'realPriceStatus') {
      // 正式价标识 0-无正式价；1-有正式价
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('无正式价'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('有正式价'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'inOutType') {
      // 出入库类型 0-采购入库；1-采购出库；2-销售出库；3-销售退回
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('采购入库'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('采购出库'),
            cssClass: ''
          },
          {
            value: 2,
            text: i18n.t('销售出库'),
            cssClass: ''
          },
          {
            value: 3,
            text: i18n.t('销售退回'),
            cssClass: ''
          }
        ]
      }
    } else if (
      defaultCol.field === 'modelCode' ||
      defaultCol.field === 'recoTime' ||
      defaultCol.field === 'priceType' ||
      defaultCol.field === 'workshop' ||
      defaultCol.field === 'prodtLine' ||
      defaultCol.field === 'workProcessName' ||
      defaultCol.field === 'teamGroupCode' ||
      defaultCol.field === 'batchNo' ||
      defaultCol.field === 'settleModel' ||
      defaultCol.field === 'dailyOutput' ||
      defaultCol.field === 'materialCode' ||
      defaultCol.field === 'workOrder' ||
      defaultCol.field === 'moduleUnitPrice' ||
      defaultCol.field === 'cbuUnitPrice' ||
      defaultCol.field === 'otherUnitPrice' ||
      defaultCol.field === 'domainRentalFeeUnitPrice' ||
      defaultCol.field === 'equipmentRentalFeeUnitPrice' ||
      defaultCol.field === 'domainRentalFeeTotalPrice' ||
      defaultCol.field === 'equipmentRentalFeeTotalPrice' ||
      defaultCol.field === 'moduleDomainRentalFeeUnitPrice' ||
      defaultCol.field === 'moduleEquipmentRentalFeeUnitPrice' ||
      defaultCol.field === 'cbuDomainRentalFeeUnitPrice' ||
      defaultCol.field === 'cbuEquipmentRentalFeeUnitPrice' ||
      defaultCol.field === 'moduleDomainRentalFeeTotalPrice' ||
      defaultCol.field === 'moduleEquipmentRentalFeeTotalPrice' ||
      defaultCol.field === 'cbuDomainRentalFeeTotalPrice' ||
      defaultCol.field === 'cbuEquipmentRentalFeeTotalPrice' ||
      defaultCol.field === 'settleUnitPriceUntaxed' ||
      defaultCol.field === 'settleUnitPriceTaxed' ||
      defaultCol.field === 'settleTotalPriceUntaxed' ||
      defaultCol.field === 'settleTotalPriceTaxed' ||
      defaultCol.field === 'shareTotalPrice'
    ) {
      // 机型编码(ID)、对账时间、价格类型、车间、工序、班组、批次号、结算机型、日产量、物料编码、工单、模组单价、整机单价、其他单价、场地租赁费单价、设备租赁费单价、场地租赁费总价、设备租赁费总价、模组场地租赁费单价、模组设备租赁费单价、整机场地租赁费单价、整机设备租赁费单价、模组场地租赁费总价、模组设备租赁费总价、整机场地租赁费总价、整机设备租赁费总价、结算未税单价、结算含税单价、结算未税总价、结算含税总价、分摊总价
      defaultCol.searchOptions = {
        renameField: `reconciliationHroResponse.${defaultCol.field}`
      }
      defaultCol.valueAccessor = (field, data) => {
        if (defaultCol.field === 'recoTime') {
          return dayjs(Number(data?.reconciliationHroResponse?.[defaultCol.field])).format(
            'YYYY-MM-DD HH:mm:ss'
          )
        }
        if (defaultCol.field === 'priceType') {
          const priceType = [
            {
              value: '0',
              text: i18n.t('整机'),
              cssClass: ''
            },
            {
              value: '2',
              text: i18n.t('模组'),
              cssClass: ''
            },
            {
              value: '3',
              text: i18n.t('其他'),
              cssClass: ''
            }
          ]
          // return data?.reconciliationHroResponse?.[defaultCol.field]
          return priceType.filter(
            (i) => i.value === data?.reconciliationHroResponse?.[defaultCol.field]
          )[0]?.['text']
        }
        return data?.reconciliationHroResponse?.[defaultCol.field]
      }
      defaultCol.ignore = true
    }
    colData.push(defaultCol)
  })

  colData.push({
    field: 'id',
    headerText: 'id',
    ignore: true,
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true, // 主键，导入解析后，设置表格数据需要
    allowEditing: false
  })

  return colData
}

export const OpenColumnData = (
  reconciliationTypeCode,
  workshopList,
  workProcessNameList,
  companyCode
) => [
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false,
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    headerText: 'addId主键',
    visible: false,
    isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。
    allowEditing: false
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    headerTemplate: requiredHeader({
      headerText: i18n.t('物料编码')
    }),
    validationRules: { required: true },
    visible: companyCode === '0530' ? true : false
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    headerTemplate: requiredHeader({
      headerText: i18n.t('物料名称')
    }),
    validationRules: { required: true },
    visible: companyCode === '0530' ? true : false
  },
  {
    width: '150',
    field: 'type',
    headerText: i18n.t('类型'), // 0-补差；1-返利；2-其他
    editType: 'dropdownedit',
    edit: {
      params: {
        allowFiltering: true,
        dataSource: TypeDropData,
        fields: { value: 'value', text: 'text' },
        query: new Query(),
        placeholder: i18n.t('请选择类型'),
        floatLabelType: 'Never',
        showClearButton: true,
        actionComplete: () => false
      }
    },
    valueAccessor: function (field, data, column) {
      let dataSource = column.edit.params.dataSource || []
      return dataSource.filter((i) => i.value == data[field])?.[0]?.text
    },
    headerTemplate: requiredHeader({
      headerText: i18n.t('类型')
    }),
    validationRules: { required: true }
  },
  {
    width: '150',
    field: 'freePrice',
    headerText: i18n.t('未税金额'),
    editType: 'numericedit',
    edit: {
      params: {
        // min: 0,
      }
    },
    headerTemplate: requiredHeader({
      headerText: i18n.t('未税金额')
    }),
    validationRules: { required: true }
  },
  {
    width: '150',
    field: 'taxPrice',
    headerText: i18n.t('含税金额'),
    editType: 'numericedit',
    edit: {
      params: {
        // min: 0,
      }
    },
    headerTemplate: requiredHeader({
      headerText: i18n.t('含税金额')
    }),
    validationRules: { required: true }
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注'),
    headerTemplate: requiredHeader({
      headerText: i18n.t('备注')
    }),
    validationRules: { required: true }
  },
  {
    width: '150',
    field: 'workshop',
    headerText: i18n.t('车间'),
    editType: 'dropdownedit',
    edit: {
      params: {
        allowFiltering: true,
        dataSource: workshopList,
        // fields: { value: 'value', text: 'text' },
        query: new Query(),
        placeholder: i18n.t('请选择车间'),
        floatLabelType: 'Never',
        showClearButton: true,
        actionComplete: () => false
      }
    },
    // valueAccessor: function (field, data, column) {
    //   let dataSource = column.edit.params.dataSource || []
    //   return dataSource.filter((i) => i.value == data[field])?.[0]?.text
    // },
    // headerTemplate: requiredHeader({
    //   headerText: i18n.t('车间')
    // }),
    // validationRules: { required: true },
    visible: reconciliationTypeCode === 'HRO' ? true : false
  },
  {
    width: '150',
    field: 'workProcessName',
    headerText: i18n.t('工序'),
    editType: 'dropdownedit',
    edit: {
      params: {
        allowFiltering: true,
        dataSource: workProcessNameList,
        // fields: { value: 'value', text: 'text' },
        query: new Query(),
        placeholder: i18n.t('请选择工序'),
        floatLabelType: 'Never',
        showClearButton: true,
        actionComplete: () => false
      }
    },
    // valueAccessor: function (field, data, column) {
    //   let dataSource = column.edit.params.dataSource || []
    //   return dataSource.filter((i) => i.value == data[field])?.[0]?.text
    // },
    // headerTemplate: requiredHeader({
    //   headerText: i18n.t('工序')
    // }),
    // validationRules: { required: true },
    visible: reconciliationTypeCode === 'HRO' ? true : false
  }
]

export const collectReconciliationInvoiceColumnData = [
  {
    field: 'serialNumber',
    headerText: i18n.t('序号')
  },
  {
    width: '150',
    field: 'projectName',
    headerText: i18n.t('项目')
  },
  {
    width: '150',
    field: 'workshop',
    headerText: i18n.t('车间')
  },
  {
    width: '150',
    field: 'apAmtUntaxed',
    headerText: i18n.t('应付不含税金额')
  },
  {
    width: '150',
    field: 'apReworkCost',
    headerText: i18n.t('应付返工费用')
  },
  {
    width: '150',
    field: 'netDeductionStayCost',
    headerText: i18n.t('应扣住宿费用')
  },
  {
    width: '150',
    field: 'netDeductionExamineCost',
    headerText: i18n.t('应扣考核费用')
  },
  {
    width: '150',
    field: 'wastageRateRebate',
    headerText: i18n.t('损耗率返利')
  },
  {
    width: '150',
    field: 'outsourceInManHourCost',
    headerText: i18n.t('外包借入工时费用')
  },
  {
    width: '150',
    field: 'outsourceLendingWorkHourCost',
    headerText: i18n.t('外包借出工时费用')
  },
  {
    width: '150',
    field: 'otherCost',
    headerText: i18n.t('其余费用')
  },
  // {
  //   width: '150',
  //   field: 'apAmtTaxed',
  //   headerText: i18n.t('应付含税金额')
  // },
  {
    width: '150',
    field: 'taxes',
    headerText: i18n.t('税费')
  },
  {
    width: '150',
    field: 'apOutsourceTaxed',
    headerText: i18n.t('应付含税外包费')
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
export const rentalFeeSummaryColumnData = [
  {
    field: 'serialNumber',
    headerText: i18n.t('序号')
  },
  {
    width: '150',
    field: 'projectName',
    headerText: i18n.t('项目')
  },
  {
    width: '150',
    field: 'workshop',
    headerText: i18n.t('车间')
  },
  {
    width: '150',
    field: 'domainRentalFeeUntaxed',
    headerText: i18n.t('场地租赁费（不含税）')
  },
  {
    width: '150',
    field: 'equipmentRentalFeeUntaxed',
    headerText: i18n.t('设备/线体租赁费（不含税）')
  },
  {
    width: '150',
    field: 'domainRentalTax',
    headerText: i18n.t('场地租赁税费（5%）')
  },
  {
    width: '150',
    field: 'equipmentRentalTax',
    headerText: i18n.t('设备/线体租赁税费（13%）')
  },
  {
    width: '150',
    field: 'receivableRentalFeeTaxed',
    headerText: i18n.t('应收含税租赁费')
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
