<template>
  <!-- 客户对账协同（供方）-对账单确认详情 -->
  <div class="full-height pt20 vertical-flex-box detail-fix-wrap">
    <!-- 头部信息 -->
    <top-info
      ref="topInfoRef"
      class="flex-keep"
      :header-info="headerInfo"
      :entry-type="entryType"
      @doReject="doReject"
      @doAccept="doAccept"
      @goBack="goBack"
      @doSubmit="doSubmit"
      @exceptionHandle="exceptionHandle"
      @doExpand="doExpand"
    ></top-info>

    <div class="bottom-tables">
      <mt-tabs :e-tab="false" :data-source="tabList" @handleSelectTab="handleSelectTab"></mt-tabs>
      <div class="flex-fit bottom-box">
        <!-- 对账明细 -->
        <div class="grid-wrap grid-wrap-page">
          <mt-template-page
            ref="templatePageRef"
            v-show="currentTabInfo.code == TabCode.reconciliationField"
            :template-config="componentConfig"
            @dataBound="handleDataBound"
            @handleClickToolBar="handleClickToolBar"
            @showFileBaseInfo="showFileBaseInfo"
          >
          </mt-template-page>
        </div>
        <!-- 高低开 -->
        <mt-template-page
          ref="componentHighLowInfo"
          v-show="currentTabInfo.code == TabCode.highLowInfo"
          :template-config="componentHighLowInfoConfig"
          @actionBegin="actionBegin"
          @actionComplete="actionComplete"
          @handleClickToolBar="handleClickToolBar1"
        >
        </mt-template-page>
        <!-- 相关附件 -->
        <relative-file
          ref="relativeFileRef"
          v-show="currentTabInfo.code == TabCode.reconciliationFile"
          :doc-id="relativeFileData.docId"
          :request-url-obj="relativeFileData.requestUrlObj"
          :module-file-list="moduleFileList"
          @fileListData="getFileListData"
          :is-view="relativeFileData.isView"
        ></relative-file>

        <!-- 总对账单 -->
        <div class="grid-wrap" v-show="currentTabInfo.code === 'collectReconciliationInvoice'">
          <collectReconciliationInvoice
            class="flex-fit"
            ref="collectReconRef"
            :data-source="collectReconDataSource"
          ></collectReconciliationInvoice>
        </div>

        <!-- 租赁费对账单 -->
        <div class="grid-wrap" v-show="currentTabInfo.code === 'rentalReconciliationInvoice'">
          <rentalFeeSummary
            class="flex-fit"
            ref="rentalFeeSummaryRef"
            :data-source="rentalFeeSummaryDataSource"
          ></rentalFeeSummary>
        </div>
        <!-- 操作日志 -->
        <operation-log
          v-if="currentTabInfo.code == TabCode.operationLog"
          :entry-code="headerInfo.reconciliationCode"
          :reconciliation-type="1"
        ></operation-log>
      </div>
    </div>
    <!-- 附件弹窗 -->
    <uploader-dialog ref="uploaderDialog"></uploader-dialog>
    <!-- 导入文件弹框 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :is-show-tips="false"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import {
  formatTableColumnData,
  ConstantConfirmDetailType,
  OpenColumnData,
  ColumnCheckbox,
  TabCode
} from './config/index'
import { detailTableData } from './config/variable.js'
import { BASE_TENANT } from '@/utils/constant'
import * as mockInfo from '@/views/reconciliationSettlement/purchaseRecon/statement/statementDetail/config/mock'
import TopInfo from './components/topInfo.vue'
import RelativeFile from '@/components/businessComponents/relativeFile/index.vue'
import UploaderDialog from '@/components/Upload/uploaderDialog'
import OperationLog from '@/components/businessComponents/comOperationLog/index.vue'
import UploadExcelDialog from '@/components/Upload/uploadExcelDialog.vue'
import { download, getHeadersFileName } from '@/utils/utils'
import bigDecimal from 'js-big-decimal'
import { utils } from '@mtech-common/utils'
export default {
  components: {
    TopInfo,
    RelativeFile,
    UploaderDialog,
    OperationLog,
    UploadExcelDialog,
    collectReconciliationInvoice: () => import('./components/collectReconciliationInvoice.vue'),
    rentalFeeSummary: () => import('./components/rentalFeeSummary.vue')
  },
  data() {
    const lastTabIndex = JSON.parse(localStorage.getItem('lastTabIndex'))
    let reconciliationData = JSON.parse(localStorage.getItem('reconciliationData'))
    if (this.$route.query.come === 'query') {
      reconciliationData = JSON.parse(localStorage.getItem('reconciliationQueryData'))
    }
    const { entryType, headerInfo } = reconciliationData
    headerInfo.isGF = true
    return {
      isInEdit: false, // 处于行内编辑中
      btnFlag: '', // 按钮点击的类型（无论是否处于编辑状态）
      initDetailUnTax: 0, // 扣除高低开 执行未税
      initDetailTax: 0, // 扣除高低开 执行含税
      apiWaitingQuantity: 0, // 调用的api正在等待数
      currentTabInfo: {}, // 当前tab的数据
      TabCode,
      tabList: [],
      entryType, // 页面类型
      headerInfo, // 头部信息
      lastTabIndex, // 前一页面的 Tab index
      moduleFileList: [],
      relativeFileData: {
        docId: headerInfo.id,
        requestUrlObj: {
          preUrl: 'reconciliationCollaboration',
          saveUrl: 'putReconciliationHeaderSupplierSaveFile', // 将附件文件url保存到列表 保存文件信息
          fileUrl: `${BASE_TENANT}/reconciliationHeaderSupplier/queryFileByDocIdAndDocType` // 获取附件列表的url 根据docType和docI查询所有文件信息
        },
        isView: entryType == ConstantConfirmDetailType.Look ?? false
      },
      uploadParams: {
        headerId: headerInfo.id
      }, // 导入文件参数
      // 导入请求接口配置
      requestUrls: {
        templateUrlPre: 'reconciliationCollaboration',
        uploadUrl: 'reconSupplierImport' // 上传接口方法名
      },
      componentHighLowInfoConfig: [],
      componentConfig: [], // 对账明细
      relativeFileList: null,
      collectReconDataSource: [], // 总对账单数据
      rentalFeeSummaryDataSource: [] // 租赁费对账单
    }
  },
  mounted() {
    this.handleDataBound = utils.debounce(this.handleDataBound, 1000)
    if (this.headerInfo?.reconciliationTypeCode === 'GQCGQZ_530') {
      this.setTemplateConfig(mockInfo.default)
    } else {
      this.getDetailTab()
    }
  },
  beforeDestroy() {
    localStorage.removeItem('reconciliationData')
    localStorage.removeItem('reconciliationQueryData')
    localStorage.removeItem('lastTabIndex')
  },
  methods: {
    // 表格数据绑定完成
    handleDataBound() {
      const currentViewRecords = this.$refs.templatePageRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let workshopList = []
      let workProcessNameList = []
      currentViewRecords?.forEach((i) => {
        if (!workshopList.includes(i?.reconciliationHroResponse?.workshop)) {
          workshopList.push(i?.reconciliationHroResponse?.workshop)
        }
        if (!workProcessNameList.includes(i?.reconciliationHroResponse?.workProcessName)) {
          workProcessNameList.push(i?.reconciliationHroResponse?.workProcessName)
        }
      })
      let toolbar = []
      if (this.entryType == ConstantConfirmDetailType.Edit && this.headerInfo?.status == 3) {
        toolbar = [
          {
            id: 'add',
            icon: 'icon_solid_Createorder',
            title: this.$t('新增')
          },
          {
            id: 'delete',
            icon: 'icon_solid_Closeorder',
            title: this.$t('删除')
          },
          {
            id: 'updateGrid',
            icon: 'icon_table_save',
            title: this.$t('更新')
          }
        ]
      }
      this.componentHighLowInfoConfig = [
        // 高低开配置
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar,
          gridId:
            this.$tableUUID.reconciliationCollaboration.reconciliationConfirmDetail
              .componentHighLowInfoConfig,
          grid: {
            height: '400',
            columnData: OpenColumnData(
              this.headerInfo?.reconciliationTypeCode,
              workshopList,
              workProcessNameList,
              this.headerInfo?.companyCode
            ),
            dataSource: [],
            editSettings: {
              allowEditing:
                this.entryType == ConstantConfirmDetailType.Edit && this.headerInfo?.status == 3
                  ? true
                  : false,
              allowAdding: true,
              allowDeleting: true,
              showDeleteConfirmDialog: false
            },
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliationHighLowSupplier/queryReconciliationHighLowSup`,
              defaultRules: [
                {
                  field: 'reconciliationId',
                  operator: 'equal',
                  value: this.headerInfo.id
                }
              ],
              afterAsyncData: (res) => {
                if (res.code === 200 && res.data.records) {
                  let freePriceTotal = 0 // 高低开 未税总额
                  let openTaxTotal = 0 // 高低开 含税总额
                  res.data.records.forEach((item) => {
                    freePriceTotal = bigDecimal.add(freePriceTotal, item.freePrice)
                    openTaxTotal = bigDecimal.add(openTaxTotal, item.taxPrice)
                  })
                  this.initDetailUnTax = bigDecimal.subtract(
                    this.headerInfo.executeUntaxedTotalPrice - freePriceTotal
                  ) // 对账单 汇总 执行未税
                  this.initDetailTax = bigDecimal.subtract(
                    this.headerInfo.executeTaxedTotalPrice - openTaxTotal
                  ) // 对账单 汇总 执行含税税
                }
              }
            }
          }
        }
      ]
      if (this.headerInfo.reconciliationTypeCode === 'HRO') {
        this.getCollectRecon()
      }
    },
    getCollectRecon() {
      // 获取对账明细的id
      // let reconciliationWaitIdList = this.headerInfo.idList
      const params = {
        // highLowList: highLowOpenData,
        reconciliationId: this.headerInfo.id
        // reconciliationWaitIdList
      }
      // 获取总对账单数据
      this.$API.reconciliationSettlement.reconciliationSummaryStatementSup(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.collectReconDataSource = data.itemList
        }
      })
      // 获取租赁费数据
      this.$API.reconciliationSettlement.rentalFeeSummaryStatementSup(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.rentalFeeSummaryDataSource = data.itemList
        }
      })
    },
    handleClickToolBar1(e) {
      const selectRows = e.grid.getSelectedRecords()
      let includesBtns = ['delete']
      if (selectRows.length === 0 && includesBtns.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // 如果新增时，有一行正在编辑。结束编辑后，再调用新增
      if (e.toolbar.id == 'add') {
        if (this.isInEdit) {
          this.btnFlag = e.toolbar.id
          this.endEdit()
        } else {
          this.handleAdd()
        }
      }
      if (e.toolbar.id == 'delete') {
        this.handleDelete()
      }
      if (e.toolbar.id == 'updateGrid') {
        this.endEdit()
      }
    },

    // 结束编辑状态
    endEdit() {
      const ref = this.$refs.componentHighLowInfo.getCurrentUsefulRef().gridRef.ejsRef
      ref.endEdit()
    },

    // 新增
    handleAdd() {
      const ref = this.$refs.componentHighLowInfo.getCurrentUsefulRef().gridRef.ejsRef
      ref.addRecord()
    },

    // 删除
    handleDelete() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认移除选中的数据？')
        },
        success: () => {
          const ref = this.$refs.componentHighLowInfo.getCurrentUsefulRef().gridRef.ejsRef
          ref.deleteRecord()
        }
      })
    },
    actionBegin(args) {
      console.log('actionBegin', args)
      // 记录开始 处于编辑状态
      if (args.requestType == 'add' || args.requestType == 'beginEdit') {
        this.isInEdit = true
      }
      if (args.requestType == 'refresh') {
        this.isInEdit = false // 结束编辑状态
      }

      if (args.requestType === 'add') {
        args.data.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
      }
    },

    actionComplete(args) {
      setTimeout(() => {
        if (['save', 'delete'].includes(args.requestType)) {
          this.isInEdit = false // 结束编辑状态

          // 如果新增时，有一行正在编辑。结束编辑后，再调用新增
          if (this.btnFlag == 'add') {
            this.handleAdd()
          }
          this.btnFlag = null

          // 计算执行未税/含税总价
          const ref = this.$refs.componentHighLowInfo?.getCurrentUsefulRef().gridRef?.ejsRef
          const _dataSource = ref?.getCurrentViewRecords() || []
          this.updateOpenTaxByTableData(_dataSource)
        }
      }, 10)
    },
    // 通过表格数据更新头部的执行含税、未税总额
    updateOpenTaxByTableData(tableData = []) {
      let freePriceTotal = 0 // 高低开 未税总额
      let openTaxTotal = 0 // 高低开 含税总额
      tableData.forEach((item) => {
        freePriceTotal = bigDecimal.add(freePriceTotal, item.freePrice)
        openTaxTotal = bigDecimal.add(openTaxTotal, item.taxPrice)
      })

      this.$store.commit('updateReconTotal', {
        detailUnTax: this.initDetailUnTax, // 对账单 汇总 执行未税
        detailTax: this.initDetailTax, // 对账单 汇总 执行含税
        openUnTax: Number(freePriceTotal), // 高低开 未税总额
        openTax: Number(openTaxTotal) // 高低开 含税总额
      })
    },
    setHighLowConfig() {
      if (this.entryType == ConstantConfirmDetailType.Edit && this.headerInfo?.status == 3) {
        this.$set(this.componentHighLowInfoConfig[0], 'toolbar', [
          {
            id: 'add',
            icon: 'icon_solid_Createorder',
            title: this.$t('新增')
          },
          {
            id: 'delete',
            icon: 'icon_solid_Closeorder',
            title: this.$t('删除')
          },
          {
            id: 'updateGrid',
            icon: 'icon_table_save',
            title: this.$t('更新')
          }
        ])
      } else {
        this.$set(this.componentHighLowInfoConfig[0], 'toolbar', [])
      }
    },
    //获取附件数据
    getFileListData(data) {
      this.relativeFileList = { ...data }
    },

    // 点击 tab
    handleSelectTab(e) {
      this.currentTabInfo = this.tabList[e]
    },

    // 获取到tab和动态列
    getDetailTab() {
      const params = {
        tenantId: this.headerInfo?.customerTenantId,
        reconciliationTypeCode: this.headerInfo?.reconciliationTypeCode, // 对账类型编码
        businessTypeCode: this.headerInfo?.businessTypeCode // 业务类型编码
      }
      this.apiStartLoading()
      // 获取 获取待对账类型字段-对账单明细
      this.$API.reconciliationCollaboration
        .postReconciliationWaitSupplierGetFieldsHeaderInfo(params)
        .then((res) => {
          this.apiEndLoading()
          this.setTemplateConfig(res?.data)
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },

    // 处理动态的配置
    setTemplateConfig(res) {
      const dataList = res || []
      const preTab = []
      dataList.forEach((itemTab) => {
        if (itemTab.code === TabCode.reconciliationField && itemTab.checkStatus) {
          // 对账明细
          this.formatDetail(itemTab)
          // // 获取明细表格数据
          // this.getDetailTableData();
        } else if (itemTab.code === TabCode.reconciliationFile && itemTab.checkStatus) {
          // 相关附件
          this.formatFile(itemTab)
        }
        if (itemTab.checkStatus) {
          preTab.push({
            title: itemTab.name,
            code: itemTab.code
          })
        }
      })
      this.tabList = preTab.concat([
        {
          title: this.$t('操作日志'),
          code: TabCode.operationLog
        }
        // {
        //   title: this.$t("高开/低开信息"),
        //   code: TabCode.highLowInfo,
        // },
      ])
      this.currentTabInfo = this.tabList[0]
    },

    // 整合对账明细
    formatDetail(itemTab) {
      let cols = formatTableColumnData(itemTab.fieldResponseList, this.headerInfo.businessTypeCode)
      cols = [ColumnCheckbox].concat(cols)

      const toolbar = [
        {
          id: 'ExportRecon',
          icon: 'icon_table_print',
          title: this.$t('导出')
        }
      ]
      // if (this.entryType == ConstantConfirmDetailType.Confirm) {
      //   // 反馈时，可行内编辑，导入文件解析，通过行号、物料凭证号设置解析后获得的备注
      //   toolbar.push({
      //     id: "ImportRecon",
      //     icon: "icon_solid_Import",
      //     title: this.$t("导入"),
      //   });
      // }

      this.componentConfig = [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId:
            this.$tableUUID.reconciliationCollaboration.reconciliationConfirmDetail.formatDetail,
          toolbar,
          grid: {
            height: 'auto',
            editSettings: {
              // 反馈时，可行内编辑
              allowEditing: this.entryType == ConstantConfirmDetailType.Confirm
            },
            columnData: cols,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliationItemSupplier/queryBuilder?BU_CODE=GF`,
              defaultRules: [
                {
                  field: 'headerId',
                  operator: 'equal',
                  value: this.headerInfo.id
                }
              ]
            },
            // dataSource: detailTableData,
            allowPaging: true // 分页
          }
        }
      ]
    },

    // 整合 相关附件
    formatFile(itemTab) {
      itemTab.fieldResponseList.forEach((item, index) => {
        if (item.code === TabCode.purHeaderFile && item.checkStatus) {
          // 采方-整单附件
          this.moduleFileList.push({
            id: 'reconciliation_header', // 选中时传给api的值
            nodeName: item.name, // 侧边栏名称
            nodeCode: index, // 即当前 index 用于选择左侧边栏时 获取当前项 配置
            btnRequired: {
              hasUpload: false,
              hasDownload: true,
              hasDelete: false
            }, // 表格 toolbar 配置（可选） hasUpload: Boolean、hasDownload: Boolean、 hasDelete: Boolean
            hasItem: false // 是否显示附件行数据（可选）
          })
        } else if (item.code === TabCode.supHeaderFile && item.checkStatus) {
          // 供方-整单附件
          this.moduleFileList.push({
            id: 'reconciliation_header_sup', // 选中时传给api的值
            nodeName: item.name, // 侧边栏名称
            nodeCode: index, // 即当前 index 用于选择左侧边栏时 获取当前项 配置
            btnRequired: {
              hasUpload:
                this.entryType == ConstantConfirmDetailType.Edit ||
                this.entryType == ConstantConfirmDetailType.Confirm,
              hasDownload: true,
              hasDelete:
                this.entryType == ConstantConfirmDetailType.Edit ||
                this.entryType == ConstantConfirmDetailType.Confirm
            }, // 表格 toolbar 配置（可选） hasUpload: Boolean、hasDownload: Boolean、 hasDelete: Boolean
            hasItem: false, // 是否显示附件行数据（可选）
            deleteFileUrl: `${BASE_TENANT}/reconciliationHeaderSupplier/deleteById` // 删除文件使用的 API 根据ID删除
          })
        }
      })
    },

    // 显示表格文件弹窗
    showFileBaseInfo(e) {
      const dialogParams = {
        fileData: cloneDeep(e.value),
        isView: true,
        title: this.$t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },
    // 跳转到对账单清单列表
    goToReconciliationCollaborationSummary() {
      // 将 tabIndex 放到 localStorage 对账单清单列表 读
      localStorage.setItem('tabIndex', JSON.stringify(this.lastTabIndex))
      if (this.$route.query.come === 'query') {
        this.$router.push({
          name: 'reconciliation-query-pv',
          query: {}
        })
      } else {
        this.$router.push({
          name: 'reconciliation-feedback-pv',
          query: {}
        })
      }
    },
    // 接受
    doAccept() {
      // 头部数据
      const topInfoData = this.$refs.topInfoRef.headerInfo
      // 对账明细备注
      const reconciliationWaitIdList = []
      if (this.componentConfig.length > 0) {
        try {
          const detailRefData =
            this.$refs.templatePageRef
              .getCurrentUsefulRef()
              .gridRef.$refs.ejsRef.getCurrentViewRecords() || []
          if (detailRefData && detailRefData.length > 0) {
            detailRefData.forEach((item) => {
              reconciliationWaitIdList.push({
                id: item.id,
                remark: item.remark
              })
            })
          }
        } catch (error) {
          console.warn(error)
        }
      }
      // 根据ID反馈正常
      const params = {
        id: this.headerInfo.id,
        remark: topInfoData.feedbackRemark, // 供方反馈意见
        idRemarkRequestList: reconciliationWaitIdList // 对账明细备注
      }
      this.putReconciliationHeaderSupplierFeedbackNormal(params)
    },
    // 拒绝
    doReject() {
      // 头部数据
      const topInfoData = this.$refs.topInfoRef.headerInfo
      // 对账明细备注
      const reconciliationWaitIdList = []
      if (this.componentConfig.length > 0) {
        try {
          const detailRefData =
            this.$refs.templatePageRef
              .getCurrentUsefulRef()
              .gridRef.$refs.ejsRef.getCurrentViewRecords() || []
          if (detailRefData && detailRefData.length > 0) {
            detailRefData.forEach((item) => {
              reconciliationWaitIdList.push({
                id: item.id,
                remark: item.feedbackRemark
              })
            })
          }
        } catch (error) {
          console.warn(error)
        }
      }
      // 根据ID反馈异常
      const params = {
        id: this.headerInfo.id,
        remark: topInfoData.feedbackRemark, // 供方反馈意见
        idRemarkRequestList: reconciliationWaitIdList // 对账明细备注
      }
      this.putReconciliationHeaderSupplierFeedbackAbnormal(params)
    },
    // 返回
    goBack() {
      this.goToReconciliationCollaborationSummary()
    },
    // 提交
    doSubmit() {
      // 头部数据
      const topInfoData = this.$refs.topInfoRef.headerInfo
      const params = {
        id: this.headerInfo.id,
        feedbackRemark: topInfoData.feedbackRemark // 供方备注 创建的时候供方备注传 feedbackRemark
      }
      // 保存对账单
      this.putReconciliationHeaderSupplierCreate(params)
    },
    //异常处理
    exceptionHandle() {
      const _flag = this.isInEdit
      // 如果高低开信息没完成，禁止提交
      if (_flag) {
        this.$toast({
          content: this.$t('请完善高低开信息'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交吗？')
        },
        success: () => {
          // 头部数据
          const topInfoData = this.$refs.topInfoRef.headerInfo
          // 对账明细
          let reconciliationWaitIdList = []

          if (this.tabList.find((item) => item.code === TabCode.reconciliationField)) {
            const detailRefData =
              this.$refs.stateDetailRef
                ?.getCurrentUsefulRef()
                .gridRef?.ejsRef.getCurrentViewRecords() || []
            reconciliationWaitIdList = detailRefData.map((item) => {
              return item
            })
          }

          // 相关附件
          let fileList = []

          if (this.tabList.find((item) => item.code === TabCode.reconciliationFile)) {
            fileList =
              this.relativeFileList?.tab === 'reconciliation_header_sup'
                ? this.relativeFileList?.data
                : [] // 采方获取采方附件
          }

          // 高低开信息
          let highLowList = []
          if (this.tabList.find((item) => item.code === TabCode.highLowInfo)) {
            const tmp =
              this.$refs.componentHighLowInfo
                ?.getCurrentUsefulRef()
                .gridRef?.$refs.ejsRef?.getCurrentViewRecords() || []
            // 格式化高低开信息
            tmp.forEach((item) => {
              highLowList.push({
                freePrice: item.freePrice, // 未税金额
                remark: item.remark, // 备注
                taxPrice: item.taxPrice, // 含税金额
                type: item.type // 类型
              })
            })
          }

          const params = {
            ...topInfoData, // 头部数据
            id: topInfoData.id,
            fileList, // 附件
            highLowList, // 高低开信息
            reconciliationWaitIdList
          }
          console.log(params)
          // return false;
          // 保存对账单
          this.putReconciliationHeaderSupplierCreate(params)
        }
      })
    },
    doExpand() {
      this.$nextTick(() => {
        this.$refs.templatePageRef?.resetGridHeight && this.$refs.templatePageRef.resetGridHeight()
      })
    },
    // 保存对账单
    putReconciliationHeaderSupplierCreate(params) {
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .putReconciliationHeaderSupplierCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.goBack()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 根据ID反馈异常
    putReconciliationHeaderSupplierFeedbackAbnormal(params) {
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .putReconciliationHeaderSupplierFeedbackAbnormal(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.goBack()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 根据ID反馈正常
    putReconciliationHeaderSupplierFeedbackNormal(params) {
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .putReconciliationHeaderSupplierFeedbackNormal(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({
              content: this.$t('操作成功，请到发票支付协同下上传发票！'),
              type: 'success'
            })
            this.goBack()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // ToolBar
    handleClickToolBar(args) {
      const { toolbar } = args
      if (toolbar.id === 'ImportRecon') {
        // 导入
        this.showUploadExcel(true)
      } else if (toolbar.id === 'ExportRecon') {
        // 导出
        this.postReconSupplierDownload()
      }
    },
    // 采购对账-对账单明细下载
    postReconSupplierDownload() {
      const params = {
        query: { headerId: this.headerInfo.id },
        body: {},
        currentBu: 'GF'
      }
      this.apiStartLoading()
      this.$API.reconciliationCollaboration.postReconSupplierDownload(params).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 显示隐藏上传弹框
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 上传成功后
    upExcelConfirm(res) {
      this.showUploadExcel(false)

      const dataList = res?.data || []
      let detailRefData = [] // 对账明细表格数据
      try {
        if (this.$refs.templatePageRef) {
          detailRefData =
            this.$refs.templatePageRef
              .getCurrentUsefulRef()
              .gridRef?.$refs.ejsRef.getCurrentViewRecords() || []
        }
      } catch (error) {
        console.warn(error)
      }

      if (detailRefData?.length > 0 && dataList?.length > 0) {
        detailRefData.forEach((itemDetail) => {
          const currentLineNo = itemDetail.lineNo // 行号
          const currentReceiveCode = itemDetail.receiveCode // 物料凭证号
          const matchData = dataList.find((itemData) => {
            const itemLineNo = itemData.lineNo // 行号
            const itemReceiveCode = itemData.receiveCode // 物料凭证号
            if (itemLineNo === currentLineNo && itemReceiveCode === currentReceiveCode) {
              // 行号 && 物料凭证号 相同
              return itemData
            }
          })

          if (matchData) {
            itemDetail.remark = matchData.remark
          }
          try {
            this.$refs.templatePageRef
              .getCurrentUsefulRef()
              .gridRef?.$refs.ejsRef.setRowData(itemDetail.id, itemDetail)
          } catch (error) {
            console.warn(error)
          }
        })
      }
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
    },
    // 获取表格数据
    getDetailTableData() {
      const params = {
        headerId: this.headerInfo.id
      }
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .reconItemSupplierNoPageFlag(params)
        .then((res) => {
          this.apiEndLoading()
          const data = res?.data || []
          detailTableData.length = 0
          data.forEach((item) => {
            detailTableData.push(item)
          })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.flex-fit {
  /deep/ .grid-container {
    overflow: auto;
    height: auto;
    flex: 1;
    > .mt-data-grid {
      height: 100%;

      > .e-grid {
        height: calc(100% - 42px);
      }
    }
  }
}

.mt-tabs {
  width: 100%;
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
</style>
