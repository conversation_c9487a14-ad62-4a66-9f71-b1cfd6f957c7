<template>
  <!-- 客户对账协同（供方）-对账单清单列表 -->
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :current-tab="currentTab"
      :permission-obj="permissionObj"
      :template-config="componentConfig"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
    />
  </div>
</template>

<script>
import {
  PendingColumnData,
  FeedbackColumnData,
  Tab,
  ConstStatus,
  ConstantConfirmDetailType,
  ConstSourcePath
} from './config/constant'
import { formatTableColumnData } from './config/index'
import { BASE_TENANT } from '@/utils/constant'
import Vue from 'vue'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  components: {},
  data() {
    const currentTab = JSON.parse(localStorage.getItem('tabIndex')) ?? 0

    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      currentTab, // 当前加载的 Tab 默认 0
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0054' },
          { dataPermission: 'b', permissionCode: 'T_02_0055' }
        ]
      },
      componentConfig: [
        {
          title: this.$t('待处理'),
          dataPermission: 'a',
          permissionCode: 'T_02_0054',
          toolbar: [
            {
              id: 'Close',
              icon: 'icon_solid_Closeorder',
              title: this.$t('关闭'),
              permission: ['O_02_0614']
            },
            {
              id: 'Publish',
              icon: 'icon_solid_pushorder',
              title: this.$t('发布'),
              permission: ['O_02_0615']
            },
            // {
            //   id: "Unpublish",
            //   icon: "icon_table_cancel",
            //   title: this.$t("取消发布"),
            //   permission: ["O_02_0616"],
            // },
            {
              id: 'BulkAcceptance',
              icon: 'icon_table_batchacceptance',
              title: this.$t('批量接受'),
              permission: ['O_02_0617']
            },
            {
              id: 'BatchRefuse',
              icon: 'icon_table_refuse1',
              title: this.$t('批量拒绝')
              // permission: ["O_02_0617"],
            },
            {
              id: 'printRecon',
              icon: 'icon_table_print',
              title: this.$t('打印'),
              permission: ['O_02_1168']
            },
            {
              id: 'excelExport',
              icon: 'icon_solid_export',
              title: this.$t('导出')
            }
          ],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId:
            this.$tableUUID.reconciliationCollaboration.reconciliationCollaborationSummary.listA,

          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: formatTableColumnData({
              tab: Tab.pending,
              data: PendingColumnData
            }),
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliationHeaderSupplier/queryBuilderWait`, // queryBuilder查询-待处理对账信息
              defaultRules: []
            },
            frozenColumns: 1
          }
        },
        {
          title: this.$t('已反馈'),
          dataPermission: 'b',
          permissionCode: 'T_02_0055',
          toolbar: [
            {
              id: 'printRecon1',
              icon: 'icon_table_print',
              title: this.$t('打印')
            }
          ],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId:
            this.$tableUUID.reconciliationCollaboration.reconciliationCollaborationSummary.listB,
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: formatTableColumnData({
              tab: Tab.feedback,
              data: FeedbackColumnData
            }),
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliationHeaderSupplier/queryBuilderFeedback`, // queryBuilder查询-已反馈对账信息
              defaultRules: []
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  mounted() {},
  beforeDestroy() {
    localStorage.removeItem('tabIndex')
  },
  methods: {
    // CellTool
    handleClickCellTool(e) {
      if (e.tool.id === 'Close') {
        // 关闭
        const params = {
          idList: [e.data.id]
        }
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认关闭选中的数据？')
          },
          success: () => {
            this.handleClose(params)
          }
        })
      } else if (e.tool.id === 'Publish') {
        // 发布
        const params = {
          idList: [e.data.id]
        }
        this.handlePublish(params)
      } else if (e.tool.id === 'exceptionHandle') {
        this.goToReconciliationConfirmDetail({
          headerInfo: e.data,
          entryType: ConstantConfirmDetailType.Edit // 点击异常处理跳转创建
        })
      } else if (e.tool.id === 'Unpublish') {
        // 取消发布
        const params = {
          idList: [e.data.id]
        }
        this.handleUnpublish(params)
      } else if (e.tool.id === 'PendingEditor') {
        // 对账单号 click
        this.goToReconciliationConfirmDetail({
          headerInfo: e.data,
          entryType: ConstantConfirmDetailType.Edit
        })
      } else if (e.tool.id === 'PendingFeedback') {
        // 反馈操作
        let entryType = ConstantConfirmDetailType.Look
        if (e.data.status == ConstStatus.toBeConfirmed) {
          // 对账单号-待处理Tab-采方-待反馈
          entryType = ConstantConfirmDetailType.Confirm // 可编辑、接受、拒绝
        } else if (e.data.status == ConstStatus.close) {
          // 对账单号-待处理Tab-采方-关闭
          entryType = ConstantConfirmDetailType.ConfirmClose // 不可编辑、可接受
        }
        // 对账单号 click
        this.goToReconciliationConfirmDetail({
          headerInfo: e.data,
          entryType: entryType
        })
      }
    },
    // CellTitle
    handleClickCellTitle(e) {
      let entryType = ConstantConfirmDetailType.Look
      if (
        e.data.sourcePath == ConstSourcePath.purchaser &&
        e.data.status == ConstStatus.toBeConfirmed
      ) {
        // 对账单号-待处理Tab-采方-待反馈
        entryType = ConstantConfirmDetailType.Confirm // 可编辑、接受、拒绝
      } else if (
        e.data.sourcePath == ConstSourcePath.purchaser &&
        e.data.status == ConstStatus.close
      ) {
        // 对账单号-待处理Tab-采方-关闭
        entryType = ConstantConfirmDetailType.ConfirmClose // 不可编辑、可接受
      }
      if (e.field === 'reconciliationCode') {
        // 对账单号 click
        this.goToReconciliationConfirmDetail({
          headerInfo: e.data,
          entryType: entryType // 点击 对账单号 仅可查看
        })
      }
    },
    // ToolBar
    handleClickToolBar(e) {
      const selectRows = e.gridRef.getMtechGridRecords()
      let hasPurData = selectRows.some((item) => item.sourcePath === 0)
      const commonToolbar = [
        'Add',
        'Filter',
        'Refresh',
        'Setting',
        'more-option-btn',
        'refreshDataByLocal',
        'filterDataByLocal',
        'excelExport'
      ]
      if (selectRows.length === 0 && !commonToolbar.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedIds = []
      selectRows.forEach((item) => selectedIds.push(item.id))

      // 校验数据
      const validData = this.checkSelectedValid({
        actionType: e.toolbar.id, // 操作类型
        selectedData: selectRows // 选择的数据
      })
      if (!validData.valid) {
        this.$toast({ content: validData.msg, type: 'warning' })

        return
      }

      if (e.toolbar.id === 'Close') {
        // 关闭
        const params = {
          idList: selectedIds
        }
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认关闭选中的数据？')
          },
          success: () => {
            this.handleClose(params)
          }
        })
      } else if (e.toolbar.id === 'Publish') {
        if (hasPurData) {
          this.$toast({
            content: this.$t('请选择供方创建数据'),
            type: 'warning'
          })
          return
        }

        // 发布
        const params = {
          idList: selectedIds
        }
        this.handlePublish(params)
      } else if (e.toolbar.id === 'Unpublish') {
        // 取消发布
        const params = {
          idList: selectedIds
        }
        this.handleUnpublish(params)
      } else if (e.toolbar.id === 'BulkAcceptance') {
        // 批量接受
        const params = {
          idList: selectedIds
        }
        this.handleBulkAcceptance(params)
      } else if (e.toolbar.id === 'BatchRefuse') {
        // 批量拒绝
        const params = {
          idList: selectedIds
        }
        this.handleBatchRefuse(params)
      } else if (e.toolbar.id == 'printRecon') {
        this.handlePrint(selectedIds)
      } else if (e.toolbar.id == 'excelExport') {
        this.handleExport()
      } else if (e.toolbar.id == 'printRecon1') {
        this.handlePrint(selectedIds)
      }
    },
    // 打印
    handlePrint(ids) {
      this.$API.reconciliationCollaboration.printRecon({ idList: ids }).then((res) => {
        if (res?.data?.type === 'application/json') {
          const reader = new FileReader()
          reader.readAsText(res?.data, 'utf-8')
          reader.onload = () => {
            const readerRes = reader.result
            const resObj = JSON.parse(readerRes)
            Vue.prototype.$toast({
              content: resObj.msg,
              type: 'error'
            })
          }

          return
        }
        const content = res.data
        this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
        // window.open(this.pdfUrl);
        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = this.pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    // 导出
    handleExport() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 10000 },
        pageFlag: true,
        rules: rule.rules || [],
        defaultRules: [
          {
            // 类型
            field: 'status',
            operator: 'in',
            value: [0, 1, 3]
          }
        ]
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationCollaboration.reconciliationHeaderExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    // 跳转到对账单确认
    goToReconciliationConfirmDetail(data) {
      const tabIndex = this.$refs.templateRef.getCurrentTabRef().tabIndex
      // 将 lastTabIndex 放到 localStorage
      localStorage.setItem('lastTabIndex', JSON.stringify(tabIndex))
      const { headerInfo, entryType } = data
      const reconciliationData = {
        headerInfo, // 头部信息
        entryType
      }
      // 将信息放到 localStorage 创建对账页面 读
      localStorage.setItem('reconciliationData', JSON.stringify(reconciliationData))
      // 跳转 对账单确认
      this.$router.push({
        name: 'reconciliation-confirm-detail',
        query: {}
      })
    },
    // 关闭 处理
    handleClose(params) {
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .putReconciliationHeaderSupplierCloseById(params)
        .then((res) => {
          this.apiEndLoading()
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 发布 处理
    handlePublish(params) {
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .putReconciliationHeaderSupplierPublishById(params)
        .then((res) => {
          this.apiEndLoading()
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 取消发布 处理
    handleUnpublish(params) {
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .putReconciliationHeaderSupplierCancelPublishById(params)
        .then((res) => {
          this.apiEndLoading()
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 批量接受 处理
    handleBulkAcceptance(params) {
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .putReconciliationHeaderSupplierBatchFeedbackNormal(params)
        .then((res) => {
          this.apiEndLoading()
          if (res.code == 200) {
            this.$toast({
              content: this.$t('操作成功，请到发票支付协同下上传发票！'),
              type: 'success'
            })
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 批量拒绝
    handleBatchRefuse(params) {
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .putReconciliationHeaderSupplierBatchRefuse(params)
        .then((res) => {
          this.apiEndLoading()
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 判断选中的数据是否符合可操作条件
    checkSelectedValid(data) {
      const validData = { valid: true, msg: '' }
      const { actionType, selectedData } = data
      if (selectedData?.length > 0) {
        for (let i = 0; i < selectedData.length; i++) {
          if (
            actionType === 'Close' &&
            (selectedData[i].sourcePath != ConstSourcePath.supplier ||
              selectedData[i].status == ConstStatus.toBeConfirmed)
          ) {
            // 操作： 关闭 && 条件： 创建方 = 供应商
            validData.valid = false
            validData.msg = this.$t('请选择供应商、状态为非待反馈的数据')
          } else if (
            actionType === 'Publish' &&
            selectedData[i].status != ConstStatus.unpublished &&
            selectedData[i].status != ConstStatus.abnormal
          ) {
            // 操作： 发布 && 条件： 状态 = 待发布 || 反馈异常
            validData.valid = false
            validData.msg = this.$t('请选择待发布或反馈异常的数据')
          } else if (
            actionType === 'Unpublish' &&
            (selectedData[i].sourcePath != ConstSourcePath.supplier ||
              selectedData[i].status != ConstStatus.toBeConfirmed)
          ) {
            // 操作： 取消发布 && 条件： 创建方 = 供应商 && 状态 = 待反馈
            validData.valid = false
            validData.msg = this.$t('请选择供应商的待反馈数据')
          } else if (
            actionType === 'BulkAcceptance' &&
            selectedData[i].sourcePath != ConstSourcePath.purchaser
          ) {
            // 操作： 批量接受 && 条件： 创建方 = 采方
            validData.valid = false
            validData.msg = this.$t('请选择采方的数据')
          }
        }
      }

      return validData
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
