<template>
  <!-- 客户对账协同（供方）-对账单清单列表 -->
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :current-tab="currentTab"
      :permission-obj="permissionObj"
      :template-config="componentConfig"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
    />
  </div>
</template>

<script>
import { FeedbackColumnData, Tab, ConstantConfirmDetailType } from './config/constant'
import { formatTableColumnData } from './config/index'
import { BASE_TENANT } from '@/utils/constant'
import Vue from 'vue'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  components: {},
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      currentTab: 0, // 当前加载的 Tab 默认 0
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [{ dataPermission: 'b', permissionCode: 'T_02_0055' }]
      },
      componentConfig: [
        {
          // dataPermission: "b",
          // permissionCode: "T_02_0055",
          toolbar: [
            {
              id: 'excelExport',
              icon: 'icon_solid_export',
              title: this.$t('导出')
            },
            {
              id: 'printRecon',
              icon: 'icon_table_print',
              title: this.$t('打印')
            }
          ],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId: this.$tableUUID.reconciliationCollaboration.reconciliationCollaborationQuery.list,
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: formatTableColumnData({
              tab: Tab.feedback,
              data: FeedbackColumnData
            }),
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliationHeaderSupplier/queryBuilderAll?BU_CODE=GF`,
              defaultRules: []
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  mounted() {},
  // beforeDestroy() {
  //   localStorage.removeItem("tabIndex");
  // },
  methods: {
    // ToolBar
    handleClickToolBar(e) {
      const selectRows = e.gridRef.getMtechGridRecords()
      const commonToolbar = [
        'Add',
        'Filter',
        'Refresh',
        'Setting',
        'more-option-btn',
        'refreshDataByLocal',
        'filterDataByLocal',
        'excelExport'
      ]
      if (selectRows.length === 0 && !commonToolbar.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedIds = []
      selectRows.forEach((item) => selectedIds.push(item.id))
      if (e.toolbar.id == 'excelExport') {
        this.handleExport()
      } else if (e.toolbar.id == 'printHRORecon') {
        this.handleHROPrint(e.gridRef.getMtechGridRecords())
      } else if (e.toolbar.id == 'printRecon') {
        for (let i = 0; i < selectRows.length; i++) {
          if (selectRows[i].status !== 2) {
            this.$toast({
              content: this.$t('只能打印反馈正常的'),
              type: 'warning'
            })
            return
          }
        }
        this.handlePrint(selectedIds)
      }
    },
    // CellTitle
    handleClickCellTitle(e) {
      if (e.field === 'reconciliationCode') {
        // 对账单号 click
        this.goToReconciliationConfirmDetail({
          headerInfo: e.data,
          entryType: ConstantConfirmDetailType.Look // 点击 对账单号 仅可查看
        })
      }
    },
    // 跳转到对账单确认
    goToReconciliationConfirmDetail(data) {
      // const tabIndex = this.$refs.templateRef.getCurrentTabRef().tabIndex;
      // 将 lastTabIndex 放到 localStorage
      // localStorage.setItem("lastTabIndex", JSON.stringify(tabIndex));
      const { headerInfo, entryType } = data
      const reconciliationData = {
        headerInfo, // 头部信息
        entryType
      }
      // 将信息放到 localStorage 创建对账页面 读
      localStorage.setItem('reconciliationQueryData', JSON.stringify(reconciliationData))
      // 跳转 对账单确认
      this.$router.push({
        name: 'reconciliation-feedback-detail-pv',
        query: {
          come: 'query'
        }
      })
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 人力外包打印
    handleHROPrint(ids) {
      if (ids.length !== 1) {
        this.$toast({
          content: this.$t('仅可选择一行进行打印'),
          type: 'warning'
        })
        return
      }
      if (ids[0]['reconciliationTypeCode'] !== 'HRO') {
        this.$toast({
          content: this.$t('仅可选择对账类型为人力外包的数据进行打印'),
          type: 'warning'
        })
        return
      }
      this.$API.reconciliationSettlement.printHROReconSup({ id: ids[0]['id'] }).then((res) => {
        if (res?.data?.type === 'application/json') {
          const reader = new FileReader()
          reader.readAsText(res?.data, 'utf-8')
          reader.onload = () => {
            console.log('======', reader)
            const readerRes = reader.result
            const resObj = JSON.parse(readerRes)
            Vue.prototype.$toast({
              content: resObj.msg,
              type: 'warning'
            })
          }

          return
        }
        const content = res.data
        this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
        // window.open(this.pdfUrl);
        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = this.pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    // 打印
    handlePrint(ids) {
      this.$API.reconciliationCollaboration
        .printRecon({ idList: ids, currentBu: 'GF' })
        .then((res) => {
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = () => {
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }

            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
          // window.open(this.pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    // 导出
    handleExport() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 10000 },
        pageFlag: true,
        rules: rule.rules || []
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationCollaboration.reconciliationHeaderExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>
