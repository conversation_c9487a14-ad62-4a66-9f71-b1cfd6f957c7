import { i18n } from '@/main.js'

export const ConstantType = {
  Add: '1', // 新增
  Edit: '2', // 编辑
  Look: '3' // 查看
}

// 对账单确认 页面类型
export const ConstantConfirmDetailType = {
  Confirm: '1', // 确认待反馈 编辑：附件、反馈意见，操作：接受、拒绝
  ConfirmClose: '2', // 确认关闭 不可编辑，操作：接受
  Edit: '3', // 编辑对账单 编辑：附件、备注，操作：提交
  Look: '4' // 查看
}

export const Tab = {
  pending: 0, // 待处理
  feedback: 1 // 已反馈
}

// 来源途径
export const ConstSourcePath = {
  purchaser: 0, // 采方
  supplier: 1 // 供方
}

// 来源途径
export const SourcePath = {
  [ConstSourcePath.purchaser]: i18n.t('采方'),
  [ConstSourcePath.supplier]: i18n.t('供方')
}

// 单据状态
export const ConstStatus = {
  close: -1, // 关闭
  unpublished: 0, // 待发布
  toBeConfirmed: 1, // 待反馈
  normal: 2, // 反馈正常
  abnormal: 3 // 反馈异常
}

// 单据状态
export const Status = {
  [ConstStatus.close]: i18n.t('关闭'),
  [ConstStatus.unpublished]: i18n.t('未发布'),
  [ConstStatus.toBeConfirmed]: i18n.t('待反馈'),
  [ConstStatus.normal]: i18n.t('反馈正常'),
  [ConstStatus.abnormal]: i18n.t('反馈异常')
}

// 状态 对应的 css class
export const StatusCssClass = {
  [ConstStatus.close]: 'col-inactive',
  [ConstStatus.unpublished]: 'col-active',
  [ConstStatus.toBeConfirmed]: 'col-published',
  [ConstStatus.normal]: 'col-normal',
  [ConstStatus.abnormal]: 'col-abnormal'
}

// 状态 对应的 Options
export const StatusOptions = [
  {
    // 关闭
    value: ConstStatus.close,
    text: Status[ConstStatus.close],
    cssClass: StatusCssClass[ConstStatus.close]
  },
  {
    // 未发布
    value: ConstStatus.unpublished,
    text: Status[ConstStatus.unpublished],
    cssClass: StatusCssClass[ConstStatus.unpublished]
  },
  {
    // 待反馈
    value: ConstStatus.toBeConfirmed,
    text: Status[ConstStatus.toBeConfirmed],
    cssClass: StatusCssClass[ConstStatus.toBeConfirmed]
  },
  {
    // 反馈正常
    value: ConstStatus.normal,
    text: Status[ConstStatus.normal],
    cssClass: StatusCssClass[ConstStatus.normal]
  },
  {
    // 反馈异常
    value: ConstStatus.abnormal,
    text: Status[ConstStatus.abnormal],
    cssClass: StatusCssClass[ConstStatus.abnormal]
  }
]

// 待处理
export const PendingColumnData = [
  {
    fieldCode: 'reconciliationCode', // 对账单编码
    fieldName: i18n.t('对账单号')
  },
  {
    fieldCode: 'status', // 单据状态 -1:关闭 0:未发布 1:发布待确认 2:反馈正常 3:反馈异常
    fieldName: i18n.t('状态')
  },
  {
    fieldCode: 'sourcePath', // 来源途径
    fieldName: i18n.t('创建方')
  },
  {
    fieldCode: 'reconciliationTypeName', // 对账类型
    fieldName: i18n.t('对账类型')
  },
  {
    fieldCode: 'companyName', // 公司名称
    fieldName: i18n.t('客户公司名称')
  },
  {
    fieldCode: 'siteName', // 工厂
    fieldName: i18n.t('工厂')
  },
  {
    fieldCode: 'totalNumber',
    fieldName: i18n.t('对账单总数量')
  },
  {
    fieldCode: 'executeUntaxedTotalPrice',
    fieldName: i18n.t('执行未税总价')
  },
  {
    fieldCode: 'taxAmount',
    fieldName: i18n.t('税额')
  },
  {
    fieldCode: 'executeTaxedTotalPrice',
    fieldName: i18n.t('执行含税总价')
  },
  {
    fieldCode: 'createTime',
    fieldName: i18n.t('创建时间')
  }
]

// 已反馈
export const FeedbackColumnData = [
  {
    fieldCode: 'status',
    fieldName: i18n.t('状态') // 单据状态 -1:关闭 0:未发布 1:发布待确认 2:反馈正常 3:反馈异常
  },
  {
    fieldCode: 'reconciliationCode',
    fieldName: i18n.t('对账单号') // 	对账单编码
  },
  {
    fieldCode: 'companyName',
    fieldName: i18n.t('客户公司名称') // 公司名称
  },
  {
    fieldCode: 'siteName', // 工厂
    fieldName: i18n.t('工厂')
  },
  {
    fieldCode: 'projectName', // 项目名称
    fieldName: i18n.t('项目名称')
  },
  {
    fieldCode: 'purchaserName', // 采购员
    fieldName: i18n.t('采购员')
  },
  {
    fieldCode: 'totalNumber',
    fieldName: i18n.t('对账单总数量')
  },
  {
    fieldCode: 'executeUntaxedTotalPrice',
    fieldName: i18n.t('执行未税总价')
  },
  {
    fieldCode: 'taxAmount',
    fieldName: i18n.t('税额')
  },
  {
    fieldCode: 'executeTaxedTotalPrice',
    fieldName: i18n.t('执行含税总价')
  },
  {
    fieldCode: 'createUserName',
    fieldName: i18n.t('创建人')
  },
  {
    fieldCode: 'createTime',
    fieldName: i18n.t('创建时间')
  }
]

// 表格 checkbox
export const ColumnCheckbox = {
  width: '50',
  type: 'checkbox',
  showInColumnChooser: false
}
