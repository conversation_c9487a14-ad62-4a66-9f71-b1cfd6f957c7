import Vue from 'vue'
import utils from '@/utils/utils'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
import { Tab, ConstSourcePath, ConstStatus, SourcePath, StatusOptions } from './constant'

// data: yyyy-mm-dd hh:mm:ss
export const timeToDate = (data) => {
  const { formatString, value } = data
  if (formatString && value) {
    const date = new Date(value)
    if (isNaN(date.getTime())) {
      return value
    } else {
      return utils.formateTime(date, formatString)
    }
  } else {
    return value
  }
}

export const formatTableColumnData = (config) => {
  const { tab, data } = config
  const colData = []
  if (tab === Tab.pending) {
    // 待处理 tab
    data.forEach((col) => {
      const defaultCol = {
        ...col,
        field: col.fieldCode,
        headerText: col.fieldName,
        width: '200'
      }
      if (col.fieldCode === 'reconciliationCode') {
        // 对账单号
        defaultCol.cellTools = [
          // { // 在创建对账单的时候编辑，对账单中不可编辑
          //   id: "PendingEditor",
          //   icon: "icon_Editor",
          //   title: i18n.t("编辑"),
          //   permission: ["O_02_0115"],
          //   visibleCondition: (data) =>
          //     data.sourcePath == ConstSourcePath.supplier &&
          //     (data.status == ConstStatus.unpublished ||
          //       data.status == ConstStatus.abnormal), // 创建方 = 供方 && (状态 = 待发布 || 状态 = 反馈异常)
          // },
        ]
      } else if (col.fieldCode === 'sourcePath') {
        // 创建方
        defaultCol.valueConverter = {
          type: 'map',
          map: SourcePath
        }
      } else if (col.fieldCode === 'status') {
        //  单据状态
        defaultCol.valueConverter = {
          type: 'map',
          map: StatusOptions
        }
        defaultCol.cellTools = [
          {
            id: 'Close',
            icon: 'icon_list_close',
            title: i18n.t('关闭'),
            permission: ['O_02_0614'],
            visibleCondition: (data) =>
              data.sourcePath == ConstSourcePath.supplier &&
              data.status !== ConstStatus.toBeConfirmed
          },
          {
            id: 'Publish',
            icon: 'icon_list_issue',
            title: i18n.t('发布'),
            permission: ['O_02_0615'],
            visibleCondition: (data) =>
              data.sourcePath == ConstSourcePath.supplier &&
              (data.status == ConstStatus.unpublished || data.status == ConstStatus.abnormal) // 创建方 = 供方 && 状态 = 未发布 || 反馈异常
          }
          // {
          //   id: "Unpublish",
          //   icon: "icon_list_Unpublish",
          //   title: i18n.t("取消发布"),
          //   permission: ["O_02_0616"],
          //   visibleCondition: (data) =>
          //     data.sourcePath == ConstSourcePath.supplier &&
          //     data.status == ConstStatus.toBeConfirmed, // 创建方 = 供方 && 状态 = 待反馈
          // },
        ]
      } else if (col.fieldCode === 'createTime') {
        // 创建时间
        defaultCol.template = () => {
          return {
            template: Vue.component('createTime', {
              template: `<div><div>{{data.createTime | timeFormat}}</div><div>{{data.createTime | dateFormat}}</div></div>`,
              data: function () {
                return { data: {} }
              },
              mounted() {},
              methods: {},
              filters: {
                dateFormat(value) {
                  return timeToDate({ formatString: 'YYYY-mm-dd', value })
                },
                timeFormat(value) {
                  return timeToDate({ formatString: 'HH:MM:SS', value })
                }
              }
            })
          }
        }
        defaultCol.searchOptions = MasterDataSelect.dateRange
      }
      colData.push(defaultCol)
    })
  } else if (tab === Tab.feedback) {
    // 已反馈 tab
    data.forEach((col) => {
      const defaultCol = {
        ...col,
        field: col.fieldCode,
        headerText: col.fieldName,
        width: '150'
      }
      if (col.fieldCode === 'reconciliationCode') {
        // 对账单号
        defaultCol.width = '200'
        defaultCol.cellTools = [] // 使其可点击查看
      } else if (col.fieldCode === 'status') {
        //  单据状态
        defaultCol.valueConverter = {
          type: 'map',
          map: StatusOptions
        }
      } else if (col.fieldCode === 'createTime') {
        // 创建时间
        defaultCol.template = () => {
          return {
            template: Vue.component('createTime', {
              template: `<div><div>{{data.createTime | timeFormat}}</div><div>{{data.createTime | dateFormat}}</div></div>`,
              data: function () {
                return { data: {} }
              },
              mounted() {},
              methods: {},
              filters: {
                dateFormat(value) {
                  return timeToDate({ formatString: 'YYYY-mm-dd', value })
                },
                timeFormat(value) {
                  return timeToDate({ formatString: 'HH:MM:SS', value })
                }
              }
            })
          }
        }
        defaultCol.searchOptions = MasterDataSelect.dateRange
      }
      colData.push(defaultCol)
    })
  }

  return colData
}
