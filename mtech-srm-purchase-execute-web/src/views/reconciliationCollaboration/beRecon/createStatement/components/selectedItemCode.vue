<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <!-- 选择物料/品项编码、SKU -->
      <mt-input
        :id="fieldName"
        v-model="data[fieldName]"
        disabled
        :width="130"
        :placeholder="$t('请选择物料编码')"
      ></mt-input>
      <mt-icon
        style="width: 20px"
        name="icon_list_refuse"
        @click.native="handleClear"
        v-if="allowEditing"
      ></mt-icon>
      <mt-icon
        v-if="allowEditing"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>

    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="$t('选择物料')"
      :buttons="buttons"
      @close="handleClose"
    >
      <mt-template-page
        v-show="dialogShow"
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="pageConfig"
        @recordDoubleClick="recordDoubleClick"
      >
      </mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
import { itemCodeColumnData } from '@/components/normalEdit/config/pcSelection.js' // 命名要与field code一致
import { PROXY_MDM_TENANT } from '@/utils/constant'
export default {
  data() {
    return {
      data: {
        // itemCode: null,
      },
      fieldName: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [],
      itemCodeColumnData,
      requestUrl: [`/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`],
      changedFieldArr: ['itemName', 'unitName'],
      changedRowArr: ['itemId', 'unitCode'],
      allowEditing: true,
      dialogShow: false
    }
  },
  mounted() {
    this.fieldName = this.data.column.field

    // console.log(this.data.column.allowEditing, "我是组的额范德萨发");
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    if (!this.allowEditing) return

    this.initDialog()
  },

  methods: {
    // 双击物料行，也进行提交
    recordDoubleClick(args) {
      console.log('recordDoubleClick', args)
      this.confirm([args.rowData])
    },

    // 提交
    confirm(records) {
      let _records = records
      if (!_records || !_records.length) {
        // _records = this.$refs.templateRef
        //   ?.getCurrentUsefulRef()
        //   ?.ejsRef?.getSelectedRecords();
        _records = this.$refs.templateRef?.getCurrentUsefulRef()?.gridRef?.getMtechGridRecords()
      }

      if (!_records.length) return

      console.log('选择到的物料信息：', _records[0])
      if (_records[0]) {
        let records = _records[0]

        let selectedRowInfo = {
          itemId: records?.id, // 物料数据
          itemCode: records?.itemCode,
          itemName: records?.itemName,
          unitName: records?.baseMeasureUnitName, // 基本单位
          unitCode: records?.baseMeasureUnitCode
        }
        // console.log("处理过的物料/sku数据：selectedRowInfo", selectedRowInfo);

        this.data[this.fieldName] = selectedRowInfo[this.fieldName]

        //  改变列的值、清空列的值
        this.changeOtherCol(selectedRowInfo)

        // 改变额外值、清空其他额外值
        this.setCellInfo(selectedRowInfo)

        // 关闭弹窗
        this.handleClose()
      }
    },

    // 调整其他列的数据：包括赋值和清空
    changeOtherCol(itemData) {
      // 如果配置了改变另一些列，就监听
      this.changedFieldArr.forEach((i) => {
        this.$bus.$emit(`${i}Change`, itemData[i] || null)
      })
      this.$bus.$emit('itemCodeChange1', itemData.itemCode)
    },

    // 调整额外值：包括赋值和清空
    setCellInfo(itemData) {
      // 记录下这行的id、code、name
      let _data = itemData

      this.$parent.$emit('selectedChanged', {
        fieldCode: this.fieldName,
        itemInfo: _data
      })

      // console.log("组合后的数据", _data);
    },

    // 点击 清除数据
    handleClear() {
      this.data[this.fieldName] = null

      let selectedRowInfo = {
        id: null,
        itemId: null,
        itemCode: null,
        itemName: null,
        unitName: null,
        unitCode: null
      }
      // 存入其他值
      this.setCellInfo(selectedRowInfo)

      // 改变其他列
      this.changeOtherCol(selectedRowInfo)
    },
    showDialog() {
      this.dialogShow = true
      this.$refs.dialog.ejsRef.show()
    },

    initDialog() {
      this.pageConfig = [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          gridId: '8cc86465-2745-a5b6-791a-f864bd323a19',
          grid: {
            allowPaging: true,
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: this[`${this.fieldName}ColumnData`],
            asyncConfig: {
              url: `${PROXY_MDM_TENANT}/${this.requestUrl[0]}`
              // recordsPosition: "data", //默认值为'data.records'
            }
          }
        }
      ]
    },

    handleClose() {
      this.dialogShow = false
      this.$refs.dialog.ejsRef.hide()
      // this.$bus.$emit("handleAddDialogShow", "addDialogShow", false);
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>

<style lang="scss">
.pc-item-dialog {
  .mt-data-grid {
    height: 100%;
    > .e-grid {
      height: calc(100% - 40px) !important;
      // display: flex;

      > .e-gridcontent {
        flex: 1;
        overflow: auto;
      }
    }

    .e-rowcell.e-active {
      background: #e0e0e0 !important;
    }
  }
}
</style>
