<template>
  <mt-template-page ref="dataGrid" :template-config="componentConfig"> </mt-template-page>
</template>

<script>
import { rentalFeeSummaryColumnData } from '../config/index'
export default {
  name: '',
  data() {
    return {
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [[]],
          grid: {
            // lineIndex: 0,
            // editSettings: {
            //   allowEditing: true,
            //   allowAdding: true,
            //   allowDeleting: true,
            //   showDeleteConfirmDialog: false
            // },
            height: 'auto',
            allowPaging: false, // 不分页
            columnData: rentalFeeSummaryColumnData,
            dataSource: []
          }
        }
      ]
    }
  },
  props: {
    dataSource: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    dataSource: {
      handler(newVal) {
        this.$set(this.componentConfig[0].grid, 'dataSource', newVal)
      },
      immediate: true
    }
  }
}
</script>
