import Vue from 'vue'
import utils from '@/utils/utils'
import { i18n } from '@/main.js'
import dayjs from 'dayjs'
import { timeDate, requiredHeader } from './columnComponent'
import cellChanged from '@/components/normalEdit/cellChanged'
import selectedItemCode from '../components/selectedItemCode'

export const ConstantType = {
  Add: '1', // 新增
  Edit: '2', // 编辑
  Look: '3' // 查看
}

// tab code
export const TabCode = {
  highLowInfo: 'highLowInfo', // 高低开信息
  operationLog: 'operationLog', // 操作日志
  reconciliationFile: 'reconciliationFile', // 相关附件
  reconciliationField: 'reconciliationField', // 对账明细
  collectReconciliationInvoice: 'collectReconciliationInvoice', // 总对账单
  rentalReconciliationInvoice: 'rentalReconciliationInvoice', // 租赁费对账单
  purHeaderFile: 'purHeaderFile', // 采方-整单附件
  supHeaderFile: 'supHeaderFile' // 供方-整单附件
}

// 冻结标记
export const FrozenStatus = {
  markFreeze: 1, // 是
  unmark: 0 // 否
}

// 表格 checkbox
export const ColumnCheckbox = {
  width: '50',
  type: 'checkbox',
  showInColumnChooser: false
}

// 同步状态
export const SyncStatus = {
  synced: 1, // 是
  notSynced: 0 // 否
}

// data: yyyy-mm-dd hh:mm:ss
export const timeToDate = (data) => {
  const { formatString, value } = data
  if (formatString && value) {
    const date = new Date(value)
    if (isNaN(date.getTime())) {
      return value
    } else {
      return utils.formateTime(date, formatString)
    }
  } else {
    return value
  }
}

export const formatTableColumnData = (data) => {
  const colData = []
  // 待对账明细
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.code,
      headerText: col.name,
      width: data.length > 10 ? '150' : 'auto',
      allowEditing: false
    }
    if (defaultCol.field === 'frozenStatus') {
      // 冻结标记
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: FrozenStatus.markFreeze,
            text: i18n.t('冻结'),
            cssClass: 'col-active'
          },
          {
            value: FrozenStatus.unmark,
            text: i18n.t('取消标记'),
            cssClass: 'col-inactive'
          }
        ]
      }
    } else if (defaultCol.field === 'advanceInvoicing') {
      // 提前开票
      defaultCol.valueConverter = {
        type: 'map',
        map: {
          0: i18n.t('否'),
          1: i18n.t('是')
        }
      }
    } else if (defaultCol.field === 'syncStatus') {
      // 同步状态
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: SyncStatus.notSynced,
            text: i18n.t('未同步'),
            cssClass: 'col-notSynced'
          },
          {
            value: SyncStatus.synced,
            text: i18n.t('已同步'),
            cssClass: 'col-synced'
          }
        ]
      }
    } else if (defaultCol.field === 'receiveTime') {
      // 收货时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'fileBaseInfoList') {
      // 文件信息
      defaultCol.template = () => {
        return {
          template: Vue.component('fileBaseInfoList', {
            template: `<div @click="showFileBaseInfo" class="cell-operable-title">{{data.fileBaseInfoList | listNumFormat}}</div>`,
            data: function () {
              return { data: {} }
            },
            filters: {
              listNumFormat(value) {
                if (value && value.length > 0) {
                  return value.length
                } else {
                  return ''
                }
              }
            },
            methods: {
              showFileBaseInfo() {
                this.$parent.$emit('showFileBaseInfo', {
                  index: this.data.index,
                  value: this.data.fileBaseInfoList
                })
              }
            }
          })
        }
      }
    } else if (defaultCol.field === 'sourceType') {
      // 来源类型（枚举） 0: 上游流入1:第三方接口
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('上游流入'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('第三方接口'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'status') {
      // 单据状态 0:待对账 1:已创建对账单
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('待对账'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('已创建对账单'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'prePayStatus') {
      // 是否预付 0-否；1-是
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('否'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('是'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'itemVoucherDate') {
      // 物料凭证日期
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'provisionalEstimateStatus') {
      // 是否暂估价 0-否；1-是 修改是否执行价
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('是'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('否'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'createTime') {
      // 创建时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'updateTime') {
      // 最后修改时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'type') {
      // 待对账类型:0-采购待对账；1-销售待对账
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('采购待对账'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('销售待对账'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'realPriceStatus') {
      // 正式价标识 0-无正式价；1-有正式价
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('无正式价'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('有正式价'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'inOutType') {
      // 出入库类型 0-采购入库；1-采购出库；2-销售出库；3-销售退回
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('采购入库'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('采购出库'),
            cssClass: ''
          },
          {
            value: 2,
            text: i18n.t('销售出库'),
            cssClass: ''
          },
          {
            value: 3,
            text: i18n.t('销售退回'),
            cssClass: ''
          }
        ]
      }
    }
    colData.push(defaultCol)
  })

  return colData
}

export const formatVXETableColumnData = (data) => {
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.code,
      title: col.name,
      width: data.length > 10 ? '150' : 'auto'
    }
    if (defaultCol.field === 'frozenStatus') {
      // 冻结标记
      const frozenStatusList = [
        {
          value: FrozenStatus.markFreeze,
          text: i18n.t('冻结')
        },
        {
          value: FrozenStatus.unmark,
          text: i18n.t('取消标记')
        }
      ]
      defaultCol.formatter = ({ cellValue }) => {
        let item = frozenStatusList.find((item) => item.value === cellValue)
        return item ? item.text : ''
      }
      // defaultCol.valueConverter = {
      //   type: "map",
      //   map: [
      //     {
      //       value: FrozenStatus.markFreeze,
      //       text: i18n.t("冻结"),
      //       cssClass: "col-active",
      //     },
      //     {
      //       value: FrozenStatus.unmark,
      //       text: i18n.t("取消标记"),
      //       cssClass: "col-inactive",
      //     },
      //   ],
      // };
    } else if (defaultCol.field === 'advanceInvoicing') {
      // 提前开票
      const frozenStatusList = [
        {
          value: 0,
          text: i18n.t('否')
        },
        {
          value: 1,
          text: i18n.t('是')
        }
      ]
      defaultCol.formatter = ({ cellValue }) => {
        let item = frozenStatusList.find((item) => item.value === cellValue)
        return item ? item.text : ''
      }
      // defaultCol.valueConverter = {
      //   type: "map",
      //   map: {
      //     0: i18n.t("否"),
      //     1: i18n.t("是"),
      //   },
      // };
    } else if (defaultCol.field === 'syncStatus') {
      // 同步状态
      const frozenStatusList = [
        {
          value: SyncStatus.notSynced,
          text: i18n.t('未同步'),
          cssClass: 'col-notSynced'
        },
        {
          value: SyncStatus.synced,
          text: i18n.t('已同步'),
          cssClass: 'col-synced'
        }
      ]
      defaultCol.formatter = ({ cellValue }) => {
        let item = frozenStatusList.find((item) => item.value === cellValue)
        return item ? item.text : ''
      }
      // defaultCol.valueConverter = {
      //   type: "map",
      //   map: [
      //     {
      //       value: SyncStatus.notSynced,
      //       text: i18n.t("未同步"),
      //       cssClass: "col-notSynced",
      //     },
      //     {
      //       value: SyncStatus.synced,
      //       text: i18n.t("已同步"),
      //       cssClass: "col-synced",
      //     },
      //   ],
      // };
    } else if (defaultCol.field === 'receiveTime') {
      // 收货时间
      defaultCol.formatter = ({ cellValue }) => {
        return dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
      }
      // defaultCol.template = timeDate({
      //   dataKey: defaultCol.field,
      //   hasTime: true,
      // });
    } else if (defaultCol.field === 'fileBaseInfoList') {
      // 文件信息
      defaultCol.slots = {
        default: 'fileBaseInfoListDefault'
      }
      // defaultCol.template = () => {
      //   return {
      //     template: Vue.component('fileBaseInfoList', {
      //       template: `<div @click="showFileBaseInfo" class="cell-operable-title">{{data.fileBaseInfoList | listNumFormat}}</div>`,
      //       data: function () {
      //         return { data: {} }
      //       },
      //       filters: {
      //         listNumFormat(value) {
      //           if (value && value.length > 0) {
      //             return value.length
      //           } else {
      //             return ''
      //           }
      //         }
      //       },
      //       methods: {
      //         showFileBaseInfo() {
      //           this.$parent.$emit('showFileBaseInfo', {
      //             index: this.data.index,
      //             value: this.data.fileBaseInfoList
      //           })
      //         }
      //       }
      //     })
      //   }
      // }
    } else if (defaultCol.field === 'sourceType') {
      // 来源类型（枚举） 0: 上游流入1:第三方接口
      const frozenStatusList = [
        {
          value: 0,
          text: i18n.t('上游流入'),
          cssClass: ''
        },
        {
          value: 1,
          text: i18n.t('第三方接口'),
          cssClass: ''
        }
      ]
      defaultCol.formatter = ({ cellValue }) => {
        let item = frozenStatusList.find((item) => item.value === cellValue)
        return item ? item.text : ''
      }
      // defaultCol.valueConverter = {
      //   type: "map",
      //   map: [
      //     {
      //       value: 0,
      //       text: i18n.t("上游流入"),
      //       cssClass: "",
      //     },
      //     {
      //       value: 1,
      //       text: i18n.t("第三方接口"),
      //       cssClass: "",
      //     },
      //   ],
      // };
    } else if (defaultCol.field === 'status') {
      // 单据状态 0:待对账 1:已创建对账单
      const frozenStatusList = [
        {
          value: 0,
          text: i18n.t('待对账'),
          cssClass: ''
        },
        {
          value: 1,
          text: i18n.t('已创建对账单'),
          cssClass: ''
        }
      ]
      defaultCol.formatter = ({ cellValue }) => {
        let item = frozenStatusList.find((item) => item.value === cellValue)
        return item ? item.text : ''
      }
      // defaultCol.valueConverter = {
      //   type: "map",
      //   map: [
      //     {
      //       value: 0,
      //       text: i18n.t("待对账"),
      //       cssClass: "",
      //     },
      //     {
      //       value: 1,
      //       text: i18n.t("已创建对账单"),
      //       cssClass: "",
      //     },
      //   ],
      // };
    } else if (defaultCol.field === 'prePayStatus') {
      // 是否预付 0-否；1-是
      const frozenStatusList = [
        {
          value: 0,
          text: i18n.t('否'),
          cssClass: ''
        },
        {
          value: 1,
          text: i18n.t('是'),
          cssClass: ''
        }
      ]
      defaultCol.formatter = ({ cellValue }) => {
        let item = frozenStatusList.find((item) => item.value === cellValue)
        return item ? item.text : ''
      }
      // defaultCol.valueConverter = {
      //   type: "map",
      //   map: [
      //     {
      //       value: 0,
      //       text: i18n.t("否"),
      //       cssClass: "",
      //     },
      //     {
      //       value: 1,
      //       text: i18n.t("是"),
      //       cssClass: "",
      //     },
      //   ],
      // };
    } else if (defaultCol.field === 'itemVoucherDate') {
      // 物料凭证日期
      defaultCol.formatter = ({ cellValue }) => {
        return dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
      }
      // defaultCol.template = timeDate({
      //   dataKey: defaultCol.field,
      //   hasTime: true,
      // });
    } else if (defaultCol.field === 'provisionalEstimateStatus') {
      // 是否暂估价 0-否；1-是 修改是否执行价
      const frozenStatusList = [
        {
          value: 0,
          text: i18n.t('是'),
          cssClass: ''
        },
        {
          value: 1,
          text: i18n.t('否'),
          cssClass: ''
        }
      ]
      defaultCol.formatter = ({ cellValue }) => {
        let item = frozenStatusList.find((item) => item.value === cellValue)
        return item ? item.text : ''
      }
      // defaultCol.valueConverter = {
      //   type: "map",
      //   map: [
      //     {
      //       value: 0,
      //       text: i18n.t("是"),
      //       cssClass: "",
      //     },
      //     {
      //       value: 1,
      //       text: i18n.t("否"),
      //       cssClass: "",
      //     },
      //   ],
      // };
    } else if (defaultCol.field === 'createTime') {
      // 创建时间
      defaultCol.formatter = ({ cellValue }) => {
        return dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss')
      }
      // defaultCol.template = timeDate({
      //   dataKey: defaultCol.field,
      //   hasTime: true,
      // });
    } else if (defaultCol.field === 'updateTime') {
      // 最后修改时间
      defaultCol.formatter = ({ cellValue }) => {
        return dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss')
      }
      // defaultCol.template = timeDate({
      //   dataKey: defaultCol.field,
      //   hasTime: true,
      // });
    } else if (defaultCol.field === 'type') {
      // 待对账类型:0-采购待对账；1-销售待对账
      const frozenStatusList = [
        {
          value: 0,
          text: i18n.t('采购待对账'),
          cssClass: ''
        },
        {
          value: 1,
          text: i18n.t('销售待对账'),
          cssClass: ''
        }
      ]
      defaultCol.formatter = ({ cellValue }) => {
        let item = frozenStatusList.find((item) => item.value === cellValue)
        return item ? item.text : ''
      }
      // defaultCol.valueConverter = {
      //   type: "map",
      //   map: [
      //     {
      //       value: 0,
      //       text: i18n.t("采购待对账"),
      //       cssClass: "",
      //     },
      //     {
      //       value: 1,
      //       text: i18n.t("销售待对账"),
      //       cssClass: "",
      //     },
      //   ],
      // };
    } else if (defaultCol.field === 'realPriceStatus') {
      // 正式价标识 0-无正式价；1-有正式价
      const frozenStatusList = [
        {
          value: 0,
          text: i18n.t('无正式价'),
          cssClass: ''
        },
        {
          value: 1,
          text: i18n.t('有正式价'),
          cssClass: ''
        }
      ]
      defaultCol.formatter = ({ cellValue }) => {
        let item = frozenStatusList.find((item) => item.value === cellValue)
        return item ? item.text : ''
      }
      // defaultCol.valueConverter = {
      //   type: "map",
      //   map: [
      //     {
      //       value: 0,
      //       text: i18n.t("无正式价"),
      //       cssClass: "",
      //     },
      //     {
      //       value: 1,
      //       text: i18n.t("有正式价"),
      //       cssClass: "",
      //     },
      //   ],
      // };
    } else if (defaultCol.field === 'inOutType') {
      // 出入库类型 0-采购入库；1-采购出库；2-销售出库；3-销售退回
      const frozenStatusList = [
        {
          value: 0,
          text: i18n.t('采购入库'),
          cssClass: ''
        },
        {
          value: 1,
          text: i18n.t('采购出库'),
          cssClass: ''
        },
        {
          value: 2,
          text: i18n.t('销售出库'),
          cssClass: ''
        },
        {
          value: 3,
          text: i18n.t('销售退回'),
          cssClass: ''
        }
      ]
      defaultCol.formatter = ({ cellValue }) => {
        let item = frozenStatusList.find((item) => item.value === cellValue)
        return item ? item.text : ''
      }
      // defaultCol.valueConverter = {
      //   type: "map",
      //   map: [
      //     {
      //       value: 0,
      //       text: i18n.t("采购入库"),
      //       cssClass: "",
      //     },
      //     {
      //       value: 1,
      //       text: i18n.t("采购出库"),
      //       cssClass: "",
      //     },
      //     {
      //       value: 2,
      //       text: i18n.t("销售出库"),
      //       cssClass: "",
      //     },
      //     {
      //       value: 3,
      //       text: i18n.t("销售退回"),
      //       cssClass: "",
      //     },
      //   ],
      // };
    } else if (
      defaultCol.field === 'modelCode' ||
      defaultCol.field === 'recoTime' ||
      defaultCol.field === 'priceType' ||
      defaultCol.field === 'workshop' ||
      defaultCol.field === 'prodtLine' ||
      defaultCol.field === 'workProcessName' ||
      defaultCol.field === 'teamGroupCode' ||
      defaultCol.field === 'batchNo' ||
      defaultCol.field === 'settleModel' ||
      defaultCol.field === 'dailyOutput' ||
      defaultCol.field === 'materialCode' ||
      defaultCol.field === 'workOrder' ||
      defaultCol.field === 'moduleUnitPrice' ||
      defaultCol.field === 'cbuUnitPrice' ||
      defaultCol.field === 'otherUnitPrice' ||
      defaultCol.field === 'domainRentalFeeUnitPrice' ||
      defaultCol.field === 'equipmentRentalFeeUnitPrice' ||
      defaultCol.field === 'domainRentalFeeTotalPrice' ||
      defaultCol.field === 'equipmentRentalFeeTotalPrice' ||
      defaultCol.field === 'moduleDomainRentalFeeUnitPrice' ||
      defaultCol.field === 'moduleEquipmentRentalFeeUnitPrice' ||
      defaultCol.field === 'cbuDomainRentalFeeUnitPrice' ||
      defaultCol.field === 'cbuEquipmentRentalFeeUnitPrice' ||
      defaultCol.field === 'moduleDomainRentalFeeTotalPrice' ||
      defaultCol.field === 'moduleEquipmentRentalFeeTotalPrice' ||
      defaultCol.field === 'cbuDomainRentalFeeTotalPrice' ||
      defaultCol.field === 'cbuEquipmentRentalFeeTotalPrice' ||
      defaultCol.field === 'settleUnitPriceUntaxed' ||
      defaultCol.field === 'settleUnitPriceTaxed' ||
      defaultCol.field === 'settleTotalPriceUntaxed' ||
      defaultCol.field === 'settleTotalPriceTaxed' ||
      defaultCol.field === 'shareTotalPrice'
    ) {
      // 机型编码(ID)、对账时间、价格类型、车间、工序、班组、批次号、结算机型、日产量、物料编码、工单、模组单价、整机单价、其他单价、场地租赁费单价、设备租赁费单价、场地租赁费总价、设备租赁费总价、模组场地租赁费单价、模组设备租赁费单价、整机场地租赁费单价、整机设备租赁费单价、模组场地租赁费总价、模组设备租赁费总价、整机场地租赁费总价、整机设备租赁费总价、结算未税单价、结算含税单价、结算未税总价、结算含税总价、分摊总价
      // defaultCol.searchOptions = {
      //   renameField: `reconciliationHroResponse.${defaultCol.field}`
      // }
      // defaultCol.valueAccessor = (field, data) => {
      //   return data?.reconciliationHroResponse?.[defaultCol.field]
      // }
      // defaultCol.ignore = true
      defaultCol.formatter = ({ row }) => {
        if (defaultCol.field === 'recoTime') {
          return dayjs(Number(row?.['reconciliationHroResponse']?.[defaultCol.field])).format(
            'YYYY-MM-DD HH:mm:ss'
          )
        }
        if (defaultCol.field === 'priceType') {
          const priceType = [
            {
              value: '0',
              text: i18n.t('整机'),
              cssClass: ''
            },
            {
              value: '2',
              text: i18n.t('模组'),
              cssClass: ''
            },
            {
              value: '3',
              text: i18n.t('其他'),
              cssClass: ''
            }
          ]
          // return data?.reconciliationHroResponse?.[defaultCol.field]
          return priceType.filter(
            (i) => i.value === row?.reconciliationHroResponse?.[defaultCol.field]
          )[0]['text']
        }
        return row?.['reconciliationHroResponse']?.[defaultCol.field]
      }
    }
    colData.push(defaultCol)
  })

  return colData
}

// 高开/低开
const typeDropData = [
  {
    text: i18n.t('补差'),
    value: 0
  },
  {
    text: i18n.t('返利'),
    value: 1
  },
  {
    text: i18n.t('其他'),
    value: 2
  },
  {
    text: i18n.t('应付返工费用'),
    value: 3
  },
  {
    text: i18n.t('应扣住宿费用'),
    value: 4
  },
  {
    text: i18n.t('应扣考核费用'),
    value: 5
  },
  {
    text: i18n.t('外包借入工时费用'),
    value: 6
  },
  {
    text: i18n.t('外包借出工时费用'),
    value: 7
  },
  {
    text: i18n.t('损耗率返利'),
    value: 8
  }
]
import { Query } from '@syncfusion/ej2-data'
export const OpenColumnData = (
  reconciliationTypeCode,
  workshopList,
  workProcessNameList,
  companyCode
) => [
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false,
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    headerText: 'addId主键',
    visible: false,
    isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。
    allowEditing: false
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    headerTemplate: requiredHeader({
      headerText: i18n.t('物料编码')
    }),
    visible: companyCode === '0530' ? true : false,
    editTemplate: () => {
      return {
        template: selectedItemCode
      }
    }
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    headerTemplate: requiredHeader({
      headerText: i18n.t('物料名称')
    }),
    visible: companyCode === '0530' ? true : false,
    editTemplate: () => {
      return {
        template: cellChanged
      }
    }
  },
  {
    width: '150',
    field: 'type',
    headerText: i18n.t('类型'), // 0-补差；1-返利；2-其他
    editType: 'dropdownedit',
    edit: {
      params: {
        allowFiltering: true,
        dataSource: typeDropData,
        fields: { value: 'value', text: 'text' },
        query: new Query(),
        placeholder: i18n.t('请选择类型'),
        floatLabelType: 'Never',
        showClearButton: true,
        actionComplete: () => false
      }
    },
    valueAccessor: function (field, data, column) {
      let dataSource = column.edit.params.dataSource || []
      return dataSource.filter((i) => i.value == data[field])?.[0]?.text
    },
    headerTemplate: requiredHeader({
      headerText: i18n.t('类型')
    }),
    validationRules: { required: true }
  },
  {
    width: '150',
    field: 'freePrice',
    headerText: i18n.t('未税金额'),
    editType: 'numericedit',
    headerTemplate: requiredHeader({
      headerText: i18n.t('未税金额')
    }),
    validationRules: { required: true },
    edit: {
      params: {
        // min: 0,
        decimals: 2
      }
    }
  },
  {
    width: '150',
    field: 'taxPrice',
    headerText: i18n.t('含税金额'),
    editType: 'numericedit',
    headerTemplate: requiredHeader({
      headerText: i18n.t('含税金额')
    }),
    validationRules: { required: true },
    edit: {
      params: {
        // min: 0,
        decimals: 2
      }
    }
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注'),
    headerTemplate: requiredHeader({
      headerText: i18n.t('备注')
    }),
    validationRules: { required: true }
  },
  {
    width: '150',
    field: 'workshop',
    headerText: i18n.t('车间'),
    editType: 'dropdownedit',
    edit: {
      params: {
        allowFiltering: true,
        dataSource: workshopList,
        // fields: { value: 'value', text: 'text' },
        query: new Query(),
        placeholder: i18n.t('请选择车间'),
        floatLabelType: 'Never',
        showClearButton: true,
        actionComplete: () => false
      }
    },
    // valueAccessor: function (field, data, column) {
    //   let dataSource = column.edit.params.dataSource || []
    //   return dataSource.filter((i) => i.value == data[field])?.[0]?.text
    // },
    headerTemplate: requiredHeader({
      headerText: i18n.t('车间')
    }),
    validationRules: { required: true },
    visible: reconciliationTypeCode === 'HRO' ? true : false
  },
  {
    width: '150',
    field: 'workProcessName',
    headerText: i18n.t('工序'),
    editType: 'dropdownedit',
    edit: {
      params: {
        allowFiltering: true,
        dataSource: workProcessNameList,
        // fields: { value: 'value', text: 'text' },
        query: new Query(),
        placeholder: i18n.t('请选择工序'),
        floatLabelType: 'Never',
        showClearButton: true,
        actionComplete: () => false
      }
    },
    // valueAccessor: function (field, data, column) {
    //   let dataSource = column.edit.params.dataSource || []
    //   return dataSource.filter((i) => i.value == data[field])?.[0]?.text
    // },
    headerTemplate: requiredHeader({
      headerText: i18n.t('工序')
    }),
    validationRules: { required: true },
    visible: reconciliationTypeCode === 'HRO' ? true : false
  }
]

export const collectReconciliationInvoiceColumnData = [
  {
    field: 'serialNumber',
    headerText: i18n.t('序号')
  },
  {
    width: '150',
    field: 'projectName',
    headerText: i18n.t('项目')
  },
  {
    width: '150',
    field: 'workshop',
    headerText: i18n.t('车间')
  },
  {
    width: '150',
    field: 'apAmtUntaxed',
    headerText: i18n.t('应付不含税金额')
  },
  {
    width: '150',
    field: 'apReworkCost',
    headerText: i18n.t('应付返工费用')
  },
  {
    width: '150',
    field: 'netDeductionStayCost',
    headerText: i18n.t('应扣住宿费用')
  },
  {
    width: '150',
    field: 'netDeductionExamineCost',
    headerText: i18n.t('应扣考核费用')
  },
  {
    width: '150',
    field: 'wastageRateRebate',
    headerText: i18n.t('损耗率返利')
  },
  {
    width: '150',
    field: 'outsourceInManHourCost',
    headerText: i18n.t('外包借入工时费用')
  },
  {
    width: '150',
    field: 'outsourceLendingWorkHourCost',
    headerText: i18n.t('外包借出工时费用')
  },
  {
    width: '150',
    field: 'otherCost',
    headerText: i18n.t('其余费用')
  },
  // {
  //   width: '150',
  //   field: 'apAmtTaxed',
  //   headerText: i18n.t('应付含税金额')
  // },
  {
    width: '150',
    field: 'taxes',
    headerText: i18n.t('税费')
  },
  {
    width: '150',
    field: 'apOutsourceTaxed',
    headerText: i18n.t('应付含税外包费')
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
export const rentalFeeSummaryColumnData = [
  {
    field: 'serialNumber',
    headerText: i18n.t('序号')
  },
  {
    width: '150',
    field: 'projectName',
    headerText: i18n.t('项目')
  },
  {
    width: '150',
    field: 'workshop',
    headerText: i18n.t('车间')
  },
  {
    width: '150',
    field: 'domainRentalFeeUntaxed',
    headerText: i18n.t('场地租赁费（不含税）')
  },
  {
    width: '150',
    field: 'equipmentRentalFeeUntaxed',
    headerText: i18n.t('设备/线体租赁费（不含税）')
  },
  {
    width: '150',
    field: 'domainRentalTax',
    headerText: i18n.t('场地租赁税费（5%）')
  },
  {
    width: '150',
    field: 'equipmentRentalTax',
    headerText: i18n.t('设备/线体租赁税费（13%）')
  },
  {
    width: '150',
    field: 'receivableRentalFeeTaxed',
    headerText: i18n.t('应收含税租赁费')
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
