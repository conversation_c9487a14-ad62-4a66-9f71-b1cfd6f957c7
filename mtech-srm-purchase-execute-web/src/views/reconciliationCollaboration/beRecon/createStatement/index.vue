<template>
  <!-- 客户对账协同（供方）-创建对账单 -->
  <div class="full-height pt20 vertical-flex-box detail-fix-wrap">
    <!-- 头部信息 -->
    <top-info
      ref="topInfoRef"
      class="flex-keep"
      :header-info="headerInfo"
      :entry-type="entryType"
      @doSubmit="doSubmit"
      @goBack="goBack"
      @doExpand="doExpand"
    ></top-info>

    <div class="bottom-tables">
      <mt-tabs
        :e-tab="false"
        tab-id="create-tab"
        :data-source="tabList"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>

      <div class="bottom-box">
        <div class="flex-fit grid-wrap" v-show="currentTabInfo.code == TabCode.reconciliationField">
          <!-- <mt-template-page
            ref="templatePageRef"
            :template-config="componentConfig"
            @showFileBaseInfo="showFileBaseInfo"
            @handleClickToolBar="handleClickToolBar"
          >
          </mt-template-page> -->

          <ScTable
            ref="xTable"
            class="xTable-class"
            :row-config="{ height: 70 }"
            :columns="columns"
            :table-data="tableData"
            show-overflow
            :loading="tableLoading"
            :min-height="600"
            border="none"
            header-align="left"
            align="center"
            style="padding-top: unset"
            :scroll-x="{ gt: 0, oSize: 20 }"
            :scroll-y="{ gt: 0, oSize: 10 }"
          >
            <template #fileBaseInfoListDefault="{ row }">
              <div
                @click="
                  showFileBaseInfo({
                    value: row.fileBaseInfoList
                  })
                "
                class="cell-operable-title"
              >
                {{
                  row.fileBaseInfoList && row.fileBaseInfoList.length
                    ? row.fileBaseInfoList.length
                    : ''
                }}
              </div>
            </template>
          </ScTable>
          <div style="font-weight: bold; padding-right: 30px; text-align: right">
            总条数：{{ tableData.length }}
          </div>
        </div>

        <!-- 相关附件 -->
        <div class="flex-fit" v-show="currentTabInfo.code == TabCode.reconciliationFile">
          <relative-file ref="relativeFileRef" :module-file-list="moduleFileList"></relative-file>
        </div>

        <!-- 高低开 -->
        <high-low-open
          class="flex-fit"
          v-show="currentTabInfo.code == TabCode.highLowInfo"
          ref="highLowRef"
          :material="material"
        ></high-low-open>

        <!-- 总对账单 -->
        <div
          class="grid-wrap"
          v-show="currentTabInfo.code === TabCode.collectReconciliationInvoice"
        >
          <collectReconciliationInvoice
            class="flex-fit"
            ref="collectReconRef"
            :data-source="collectReconDataSource"
          ></collectReconciliationInvoice>
        </div>

        <!-- 租赁费对账单 -->
        <div class="grid-wrap" v-show="currentTabInfo.code === TabCode.rentalReconciliationInvoice">
          <rentalFeeSummary
            class="flex-fit"
            ref="rentalFeeSummaryRef"
            :data-source="rentalFeeSummaryDataSource"
          ></rentalFeeSummary>
        </div>
      </div>
    </div>

    <!-- 附件弹窗 -->
    <uploader-dialog ref="uploaderDialog"></uploader-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { ConstantType, TabCode, formatVXETableColumnData } from './config/index'
import { nodeType } from '@/components/businessComponents/relativeFileNoDocId/config/relative.js'
import bigDecimal from 'js-big-decimal'
export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue'),
    RelativeFile: () => import('@/components/businessComponents/relativeFileNoDocId/index.vue'),
    UploaderDialog: () => import('@/components/Upload/uploaderDialog'),
    highLowOpen: () => import('./components/highLowOpen.vue'),
    collectReconciliationInvoice: () => import('./components/collectReconciliationInvoice.vue'),
    rentalFeeSummary: () => import('./components/rentalFeeSummary.vue'),
    ScTable: () => import('@/components/ScTable/src/index')
  },
  data() {
    const createStatementData = JSON.parse(localStorage.getItem('createStatementData')) || {}
    // this.updateReconTotalByTableData(createStatementData.requireData);
    const lastTabIndex = JSON.parse(localStorage.getItem('lastTabIndex'))

    return {
      createStatementData,
      lastTabIndex, // 前一页面的 Tab index
      TabCode, // TabCode
      ConstantType,
      idList: createStatementData.idList, // 表格行 id
      headerInfo: createStatementData.headerInfo, // 头部信息
      fieldsHeadParams: createStatementData.fieldsHeadParams || {}, // 获取动态tab api参数
      currentTabInfo: {}, // 当前tab的数据
      tabList: [],
      moduleFileList: [],
      entryType: createStatementData.entryType, // 页面状态
      apiWaitingQuantity: 0, // 调用的api正在等待数
      componentConfig: [],
      asyncParams: null,
      itemSize: 0,
      columns: [],
      tableData: [],
      tableLoading: false,
      collectReconDataSource: [], // 总对账单数据
      rentalFeeSummaryDataSource: [], // 租赁费对账单

      material: null
    }
  },
  computed: {
    isBatchCreate() {
      return this.$route.query?.flag === 'all'
    }
  },
  mounted() {
    // 获取动态tab
    this.getDynamicTab()
    this.getAmount()
    this.initTaxData()
    this.formatDetail()
  },
  beforeDestroy() {
    this.$store.commit('updateReconTotal', {
      ...this.$store.state.reconTotal,
      openUnTax: 0, // 高低开 未税
      openTax: 0, // 高低开 含税
      detailUnTax: 0, // 对账单 汇总 执行未税
      detailTax: 0 // 对账单 汇总 执行含税
    })
    localStorage.removeItem('createStatementData')
    localStorage.removeItem('createStatementParams')
  },
  methods: {
    getCollectRecon(highLowOpenData = null) {
      // 获取对账明细的id
      console.log('设置~~~', highLowOpenData)
      let reconciliationWaitIdList = this.idList
      if (highLowOpenData) {
        // 高低开数据 下面调接口
        const params = {
          highLowList: highLowOpenData,
          // reconciliationId: 0,
          reconciliationWaitIdList
        }
        // 获取总对账单数据
        this.$API.reconciliationSettlement.reconciliationSummaryStatementSup(params).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.collectReconDataSource = data.itemList
          }
        })
        // 获取租赁费数据
        this.$API.reconciliationSettlement.rentalFeeSummaryStatementSup(params).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.rentalFeeSummaryDataSource = data.itemList
          }
        })
      }
    },
    formatDetail() {
      // this.componentConfig = [
      //   {
      //     useToolTemplate: false, // 不使用预置(新增、编辑、删除)
      //     useBaseConfig: false, // 不使用组件中的toolbar配置
      //     toolbar: [[]],
      //     grid: {
      //       editSettings: {
      //         allowEditing: true,
      //       },
      //       // allowPaging: false,
      //       columnData: [ColumnCheckbox].concat(
      //         formatTableColumnData(this.createStatementData.asyncColumnData)
      //       ),
      //       dataSource: [],
      //     },
      //     frozenColumns: 1,
      //   },
      // ];
      this.columns = formatVXETableColumnData(this.createStatementData.asyncColumnData)
    },
    initTaxData() {
      this.$store.commit('updateReconTotal', {
        openUnTax: 0, // 高低开 未税
        openTax: 0, // 高低开 含税
        detailUnTax: 0, // 对账单 汇总 执行未税
        detailTax: 0 // 对账单 汇总 执行含税
      })
    },
    // 获取动态tab
    getDynamicTab() {
      const params = this.fieldsHeadParams
      this.apiStartLoading()
      // 获取 获取待对账类型字段-对账单明细
      this.$API.reconciliationCollaboration
        .postReconciliationWaitSupplierGetFieldsHeaderInfo(params)
        .then((res) => {
          this.apiEndLoading()
          const data = res?.data || []
          const dataList = []
          data.forEach((itemTab) => {
            if (itemTab.code === TabCode.reconciliationFile && itemTab.checkStatus) {
              // 相关附件
              this.formatFile(itemTab)
            }
            if (itemTab.checkStatus) {
              dataList.push({
                title: itemTab.name,
                code: itemTab.code
              })
            }
          })
          this.tabList = dataList
          this.currentTabInfo = this.tabList[0] // 默认选择第一个
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },

    // 整合 相关附件
    formatFile(itemTab) {
      itemTab.fieldResponseList.forEach((item) => {
        if (item.code === TabCode.supHeaderFile && item.checkStatus) {
          // 供方-整单附件
          this.moduleFileList.push({
            id: 'reconciliation_header_sup',
            type: nodeType.mainUpdate,
            nodeName: item.name // 侧边栏名称
          })
        }
      })
    },
    // ToolBar
    handleClickToolBar(e) {
      const selectRows = e.grid.getSelectedRecords()
      const commonToolbar = [
        'more-option-btn',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]

      if (selectRows.length === 0 && !commonToolbar.includes(e.toolbar.id)) {
        this.$toast({
          content: this.$t('请先选择一行'),
          type: 'warning'
        })
        return
      }

      const selectedIds = []
      selectRows.forEach((item) => {
        selectedIds.push(item.id)
      })

      if (e.toolbar.id == 'remove') {
        // 移除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认移除选中的数据？')
          },
          success: () => {
            const tableData = this.componentConfig[0].grid.dataSource
            selectedIds.forEach((id) => {
              for (let i = 0; i < tableData.length; i++) {
                if (tableData[i].id === id) {
                  tableData.splice(i, 1)
                  break
                }
              }
            })

            this.updateReconTotalByTableData(tableData)
          }
        })
      }
    },
    // 通过接口获取的数据更新高低含税、未税金额
    updateReconTotalByRequest(untaxedTotal, taxTotal) {
      this.$store.commit('updateReconTotal', {
        ...this.$store.state.reconTotal,
        detailUnTax: Number(untaxedTotal), // 汇总明细行 执行未税总金额
        detailTax: Number(taxTotal) // 汇总明细行 执行含税总金额
      })
    },
    // 通过表格数据更新头部的含税、未税总金额
    updateReconTotalByTableData(tableData = []) {
      // 含税、未税的总金额
      let untaxedTotal = 0 // 执行未税总金额
      let taxTotal = 0 // 执行含税总金额
      tableData.forEach((item) => {
        untaxedTotal = bigDecimal.add(untaxedTotal, item.executeUntaxedTotalPrice)
        taxTotal = bigDecimal.add(taxTotal, item.executeTaxedTotalPrice)
      })

      this.$store.commit('updateReconTotal', {
        ...this.$store.state.reconTotal,
        detailUnTax: Number(untaxedTotal), // 汇总明细行 执行未税总金额
        detailTax: Number(taxTotal) // 汇总明细行 执行含税总金额
      })
    },
    // 显示表格文件弹窗
    showFileBaseInfo(e) {
      const dialogParams = {
        fileData: cloneDeep(e.value),
        isView: true,
        title: this.$t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },
    doSubmit() {
      const _flag = this.$refs.highLowRef.isInEdit
      // 如果高低开信息没完成，禁止提交
      if (_flag) {
        this.$toast({
          content: this.$t('请完善高低开信息'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`对账明细总条数（包含关联凭证数）：${this.itemSize}，确认提交吗？`)
        },
        success: () => {
          // 头部数据
          const topInfoData = this.$refs.topInfoRef.topInfo
          // 对账明细备注
          let reconciliationWaitIdList = []
          // const detailRefData =
          //   this.$refs.templatePageRef
          //     ?.getCurrentUsefulRef()
          //     .gridRef?.$refs.ejsRef.getCurrentViewRecords() || [];
          reconciliationWaitIdList = this.idList.map((item) => {
            return {
              id: item,
              remark: ''
            }
          })
          // 高低开信息
          let highLowList = []
          const tmp =
            this.$refs.highLowRef?.$refs.dataGrid
              ?.getCurrentUsefulRef()
              .gridRef?.$refs.ejsRef.getCurrentViewRecords() || []
          // 格式化高低开信息
          tmp.forEach((item) => {
            highLowList.push({
              itemCode: item.itemCode, // 物料编码
              itemName: item.itemName, // 物料名称
              freePrice: item.freePrice, // 未税金额
              remark: item.remark, // 备注
              taxPrice: item.taxPrice, // 含税金额
              type: item.type, // 类型
              workProcessName: item.workProcessName, // 工序
              workshop: item.workshop // 车间
            })
          })
          // 相关附件
          const uploadFiles = this.$refs.relativeFileRef.getUploadFlies('reconciliation_header_sup')
          const fileList = this.formatUploadFiles(uploadFiles)
          const params = {
            ...topInfoData, // 头部数据
            createFlag: this.isBatchCreate ? true : false,
            id: undefined, // 创建是没有id
            fileList, // 附件
            reconciliationWaitIdList: this.isBatchCreate ? [] : reconciliationWaitIdList, // 待对账数据
            highLowList // 高低开信息
          }
          if (this.isBatchCreate) {
            params.queryBuilderDTO = this.asyncParams
          }
          // 保存对账单
          this.putReconciliationHeaderSupplierCreate(params)
        }
      })
    },
    putReconciliationHeaderSupplierCreate(params) {
      // 保存对账单
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .putReconciliationHeaderSupplierCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
          }
          setTimeout(() => {
            // 返回 待对账列表
            this.goBack()
          }, 100)
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    goBack() {
      // TODO 前页面 TabIndex
      // 返回 待对账列表
      this.$router.push({
        name: 'reconciliation-be-reconciled',
        query: {}
      })
    },
    doExpand() {
      this.$refs.templatePageRef?.resetGridHeight && this.$refs.templatePageRef.resetGridHeight()
    },
    // 格式化附件信息
    formatUploadFiles(list) {
      const tmp = []
      list.forEach((item) => {
        tmp.push({
          fileName: item.fileName, //	文件上传名称
          fileSize: item.fileSize, //	文件大小
          fileType: item.fileType, //	文件类型
          sysFileId: item.id || item.sysFileId, //	文件ID(MT_WP_SYS_FILE表ID)
          url: item.url //	文件路径
        })
      })

      return tmp
    },
    getParams() {
      const topInfoData = this.headerInfo
      if (this.isBatchCreate) {
        return {
          ...topInfoData,
          createFlag: true,
          queryBuilderDTO: this.asyncParams
        }
      }
      let reconciliationWaitIdList = this.idList.map((item) => {
        return {
          id: item,
          remark: ''
        }
      })
      return {
        ...topInfoData,
        createFlag: false,
        reconciliationWaitIdList
      }
    },
    getAmount() {
      if (this.isBatchCreate) {
        let _asyncParams = localStorage.getItem('createStatementParams')
        this.asyncParams = _asyncParams ? JSON.parse(_asyncParams) : {}
      }
      let params = this.getParams()
      this.tableLoading = true
      this.$API.reconciliationCollaboration
        .getAmount(params)
        .then((res) => {
          if (res.code === 200) {
            this.updateReconTotalByRequest(
              res.data.executeUntaxedTotalPrice,
              res.data.executeTaxedTotalPrice
            )
            this.headerInfo = Object.assign({}, this.headerInfo, {
              executeUntaxedTotalPrice: res.data.executeUntaxedTotalPrice,
              taxRate: res.data.taxRate,
              executeTaxedTotalPrice: res.data.executeTaxedTotalPrice,
              untaxedTotalPrice: res.data.untaxedTotalPrice,
              amountDifferentTotal: res.data.amountDifferentTotal,
              rateDifferent: res.data.rateDifferent,
              taxAmount:
                res.data?.taxAmount ||
                bigDecimal.subtract(
                  res.data.executeTaxedTotalPrice,
                  res.data.executeUntaxedTotalPrice
                )
            })
            // this.$set(
            //   this.componentConfig[0].grid,
            //   "dataSource",
            //   res.data?.waitSupplierList
            // );
            this.tableData = res.data?.waitSupplierList
            this.itemSize = res.data?.itemSize
            this.material = {
              itemCode: res.data?.highItemCode,
              itemName: res.data?.highItemName
            }
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 点击 tab
    handleSelectTab(e) {
      this.currentTabInfo = this.tabList[e]
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped>
.mt-tabs {
  width: 100%;
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
</style>
