<template>
  <!-- 客户对账协同（供方）-待对账 -->
  <div class="full-height">
    <div class="slot-select">
      <!-- 客户选择列表 -->
      <mt-select
        class="pur-tenant-select"
        :width="200"
        :data-source="tabList"
        v-model="currentTabId"
        :open-dispatch-change="false"
        :show-clear-button="false"
        @change="setTypeList"
        :fields="{ value: 'id', text: 'title' }"
        placeholder=""
      ></mt-select>
      <!-- 对账单类型 -->
      <mt-tabs
        :e-tab="false"
        :data-source="tabTypeList"
        id="supReconTypeTabs"
        tab-id="supR-tab"
        class="supReconTypeTabs"
        :selected-item="currentTypeTabIndex"
        @handleSelectTab="handleSelectTypeTab"
      ></mt-tabs>
    </div>

    <mt-template-page
      v-if="isShowTemplatePage"
      ref="templateRef"
      class="self-set-table"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @showFileBaseInfo="showFileBaseInfo"
    />
    <!-- 附件弹窗 -->
    <uploader-dialog ref="uploaderDialog"></uploader-dialog>
  </div>
</template>

<script>
import {
  formatTableColumnData,
  Tab,
  ConstantType,
  AsyncTabToolbar,
  FrozenStatus
} from './config/index'
import { BASE_TENANT } from '@/utils/constant'
import { defaultColumnData } from './config/defaultColumn'
import { cloneDeep } from 'lodash'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  components: {
    UploaderDialog: () => import('@/components/Upload/uploaderDialog')
  },
  data() {
    return {
      tabList: [], // 公司列表
      tabTypeList: [], // 当前公司下的 对账类型列表
      apiWaitingQuantity: 0, // 调用的api正在等待数
      actionType: {
        createStatement: 1, // 创建对账单
        markFreeze: 2, // 标记冻结
        unmark: 3 // 取消标记
      },
      asyncColumnData: [], // 从 API 获取的表头
      isShowTemplatePage: false, // 初始化不显示
      componentConfig: [],
      currentTabId: null, // 客户序号
      currentTabIndex: 0, // 客户序号
      currentTypeTabIndex: 0, // 对账类型序号
      total: 0,
      hasTab: true
    }
  },
  mounted() {
    this.getReconCostum()
  },
  methods: {
    // 获取客户对象
    getReconCostum() {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .getReconcliationSupplierName()
        .then((res) => {
          this.apiEndLoading()
          if (!res.data || !res.data?.length) {
            this.hasTab = false
            this.setDefaultConfig(defaultColumnData)
            return
          }
          res.data.forEach((item) => {
            this.tabList.push({
              title: item.companyName,
              id: item.id,
              purTenantId: item.purTenantId,
              types: item.types,
              companyCode: item.companyCode
            })
          })
          if (this.tabList.length) this.setTypeList(this.tabList[0]?.id, true)
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 设置默认表格 (没有tab页数据时候默认展示)
    setDefaultConfig(columnData) {
      const _columnData = formatTableColumnData({
        tab: Tab.toBeReconciledDetail,
        data: columnData
      })
      const config = {
        useToolTemplate: false, // 不使用预置(新增、编辑、删除)
        useBaseConfig: false, // 使用组件中的toolbar配置
        toolbar: [AsyncTabToolbar],
        saveSelectedRecordWithSearch: false,
        grid: {
          lineSelection: 0,
          lineIndex: 1,
          columnData: _columnData,
          dataSource: []
        }
      }

      this.componentConfig.push(config)
      this.isShowTemplatePage = true // 显示表格
    },
    // 切换客户列表  设置 对账类型 列表
    setTypeList(id, type) {
      const _id = type ? id : id.itemData.id
      if (!_id) return
      this.currentTabId = _id
      this.currentTypeTabIndex = 0
      this.tabTypeList = []
      this.currentTabIndex = this.tabList.findIndex((i) => i.id == this.currentTabId)

      console.log(this.currentTabIndex)
      this.tabList[this.currentTabIndex].types.forEach((item) => {
        this.tabTypeList.push({
          title: item.name,
          code: item.code
        })
      })
      this.setTemplateConfig()
    },

    // 切换对账类型
    handleSelectTypeTab(e) {
      this.currentTypeTabIndex = e
      this.setTemplateConfig()
    },

    // 处理列模板的配置
    setTemplateConfig() {
      this.isShowTemplatePage = false
      this.componentConfig = []
      const currentTableCode = this.tabTypeList[this.currentTypeTabIndex].code
      const params = {
        reconciliationTypeCode: currentTableCode,
        tenantId: this.tabList[this.currentTabIndex].purTenantId
      }
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .postReconciliationWaitSupplierGetFields(params)
        .then((res) => {
          this.apiEndLoading()

          const columnData = formatTableColumnData({
            tab: Tab.toBeReconciledDetail,
            data: res.data
          })

          this.asyncColumnData = res.data // 动态表头将会传递到详情页

          // 设置表格 gridId 配置
          const tableUUID =
            this.$tableUUID.reconciliationCollaboration.reconciliationBeReconciled.list
          const gridId = this.$md5(`${tableUUID}${currentTableCode}`) // 表格 uuid

          const config = {
            useToolTemplate: false, // 不使用预置(新增、编辑、删除)
            useBaseConfig: true, // 使用组件中的toolbar配置
            toolbar: AsyncTabToolbar,
            gridId,
            saveSelectedRecordWithSearch: false,
            grid: {
              frozenColumns: 1,
              lineSelection: 0,
              lineIndex: 1,
              columnData,
              dataSource: [],
              pageSettings: {
                currentPage: 1,
                pageSize: 200,
                pageSizes: [10, 20, 50, 100, 200, 1000],
                totalRecordsCount: 0
              },
              asyncConfig: {
                url: `${BASE_TENANT}/reconciliationWaitSupplier/queryBuilderItem`,
                defaultRules: [
                  {
                    field: 'reconciliationTypeCode',
                    operator: 'equal',
                    value: currentTableCode
                  },
                  {
                    field: 'companyCode',
                    operator: 'equal',
                    value: this.tabList[this.currentTabIndex].companyCode
                  }
                ],
                afterAsyncData: (res) => {
                  this.total = res?.data?.total
                }
              },
              defaultSearchItem: [
                {
                  field: 'frozenStatus',
                  headerText: this.$t('冻结状态')
                },
                {
                  field: 'provisionalEstimateStatus',
                  headerText: this.$t('是否执行价')
                },
                {
                  field: 'receiveTime',
                  headerText: this.$t('收货时间')
                }
              ]
            }
          }
          this.componentConfig = [config]
          // this.componentConfig.push(config)
          this.isShowTemplatePage = true // 显示表格
        })
        .catch(() => {
          this.isShowTemplatePage = false // 不显示表格
          this.apiEndLoading()
        })
    },

    // 显示表格文件弹窗
    showFileBaseInfo(e) {
      const dialogParams = {
        fileData: cloneDeep(e.value),
        isView: true,
        title: this.$t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },
    // ToolBar
    handleClickToolBar(e) {
      const selectRows = e.gridRef.getMtechGridRecords()
      const selectOperateId = ['DetailCreateStatement', 'MarkFreeze', 'Unmark']
      if (selectRows.length === 0 && selectOperateId.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedIds = []
      selectRows.forEach((item) => selectedIds.push(item.id))

      if (e.toolbar.id === 'DetailCreateStatement') {
        // 创建对账单
        this.handleDetailCreateStatement({ selectRows, selectedIds })
      } else if (e.toolbar.id === 'BatchDetailCreateStatement') {
        // 待对账明细-创建对账单
        this.handleBatchDetailCreateStatement({
          selectRows,
          selectedIds
        })
      } else if (e.toolbar.id === 'MarkFreeze') {
        // 冻结
        this.handleMarkFreeze({ selectRows, selectedIds })
      } else if (e.toolbar.id === 'Unmark') {
        // 取消冻结
        this.handleUnmark({ selectRows, selectedIds })
      } else if (e.toolbar.id == 'excelExport') {
        this.handleExport()
      }
    },
    // 创建对账单
    handleDetailCreateStatement(args) {
      const { selectRows } = args
      const {
        valid,
        validMarkFreeze,
        validProvisional
        // validBusinessType, // 业务类型
        // validCompany, // 公司
        // validSupplier, // 供应商
        // validCurrency, // 币种
      } = this.checkSelectedValid({
        selectedData: selectRows,
        actionType: this.actionType.createStatement
      })
      if (!valid) {
        if (!validMarkFreeze) {
          this.$toast({
            content: this.$t('冻结状态为已冻结的数据不可创建对账单'),
            type: 'warning'
          })
        } else if (!validProvisional) {
          this.$toast({
            content: this.$t('是否执行价为否的数据不可创建对账单'),
            type: 'warning'
          })
        } else {
          this.$toast({
            content: this.$t('选择的数据不可合并创建对账单'),
            type: 'warning'
          })
        }

        return
      }
      const _len = selectRows?.length
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(
            `是否创建当前勾选数据？（您勾选的数据条数：${_len}，当前待对账总条数：${this.total}）`
          )
        },
        success: () => {
          this.mergeStorageData()
        }
      })
    },
    // 批量（全量）创建对账单
    handleBatchDetailCreateStatement() {
      let { asyncParams, queryBuilderRules } =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef
      let _flag = this.checkParams(queryBuilderRules)
      let selectRows = this.$refs.templateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      if (selectRows.length <= 0) {
        this.$toast({
          content: this.$t('当前查询条件未查询对账单数据'),
          type: 'warning'
        })
        return false
      }
      if (!_flag) return false
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`是否全量创建当前查询的数据？（当前待对账总条数：${this.total}）`)
        },
        success: () => {
          queryBuilderRules = queryBuilderRules || {}
          const _params = {
            ...asyncParams,
            rules: queryBuilderRules.rules || []
          }
          localStorage.setItem('createStatementParams', JSON.stringify(_params))
          this.mergeStorageData('all')
        }
      })
    },
    // 组装缓存数据
    mergeStorageData(type) {
      let selectRows = this.$refs.templateRef.getCurrentUsefulRef().gridRef.getMtechGridRecords()
      if (type === 'all') {
        selectRows = this.$refs.templateRef
          .getCurrentUsefulRef()
          .gridRef.ejsRef.getCurrentViewRecords()
      }
      const selectedIds = []
      selectRows.forEach((item) => {
        selectedIds.push(item.id)
      })
      const createStatementData = {
        asyncColumnData: this.asyncColumnData, // API 获取的动态表头数据
        headerInfo: selectRows[0], // 头部信息
        requireData: selectRows, // 需求明细信息
        idList: selectedIds, // idList
        entryType: ConstantType.Add, // 页面类型
        fieldsHeadParams: {
          reconciliationTypeCode: this.tabTypeList[this.currentTypeTabIndex].code, // 对账类型编码
          tenantId: this.tabList[this.currentTabIndex].purTenantId,
          businessTypeCode: this.tabList[this.currentTabIndex].businessTypeCode // 业务类型编码
        } // 创建对账单详情动态tab参数
      }
      // 将信息放到 localStorage 创建对账页面 读
      localStorage.setItem('createStatementData', JSON.stringify(createStatementData))
      // 跳转 创建对账单
      this.$router.push({
        name: 'reconciliation-create-statement',
        query: type === 'all' ? { flag: 'all' } : {}
      })
    },
    checkParams(data) {
      if (!data) {
        this.$toast({
          content: this.$t('请设置搜索条件，按冻结状态、是否执行价、收货时间查询数据'),
          type: 'warning'
        })
        return false
      }

      let _rules = data.rules
      let _codes = _rules.map((item) => item.field)
      if (!_codes.includes('frozenStatus')) {
        this.$toast({
          content: this.$t('请设置搜索条件并按“冻结状态”查询'),
          type: 'warning'
        })
        return false
      }
      if (!_codes.includes('provisionalEstimateStatus')) {
        this.$toast({
          content: this.$t('请设置搜索条件并按“是否执行价”查询'),
          type: 'warning'
        })
        return false
      }
      if (!_codes.includes('receiveTime')) {
        this.$toast({
          content: this.$t('请设置搜索条件并按“收货时间”查询'),
          type: 'warning'
        })
        return false
      }

      for (var i = 0; i < _rules.length; i++) {
        let item = _rules[i]
        if (item.field === 'frozenStatus' && item.value === 1) {
          this.$toast({
            content: this.$t('请选择冻结状态为未冻结'),
            type: 'warning'
          })
          return false
        }
        if (item.field === 'provisionalEstimateStatus' && item.value === 1) {
          this.$toast({
            content: this.$t('请选择是否执行价为是'),
            type: 'warning'
          })
          return false
        }
      }
      return true
    },
    // 冻结
    handleMarkFreeze(args) {
      const { selectRows, selectedIds } = args
      const { valid } = this.checkSelectedValid({
        selectedData: selectRows,
        actionType: this.actionType.unmark
      })
      if (!valid) {
        this.$toast({
          content: this.$t('请选择相同的冻结状态'),
          type: 'warning'
        })

        return
      }
      if (selectRows[0].frozenStatus == FrozenStatus.markFreeze) {
        this.$toast({
          content: this.$t('请选择未冻结的数据'),
          type: 'warning'
        })

        return
      }
      const params = {
        idList: selectedIds
      }
      // 请求API
      this.doMarkFreeze(params)
    },
    // 取消冻结
    handleUnmark(args) {
      const { selectRows, selectedIds } = args
      const { valid } = this.checkSelectedValid({
        selectedData: selectRows,
        actionType: this.actionType.unmark
      })
      if (!valid) {
        this.$toast({
          content: this.$t('请选择相同的冻结状态'),
          type: 'warning'
        })

        return
      }
      if (selectRows[0].frozenStatus == FrozenStatus.unmark) {
        this.$toast({
          content: this.$t('请选择已冻结的数据'),
          type: 'warning'
        })

        return
      }
      const params = {
        idList: selectedIds
      }
      // 请求API
      this.doUnmark(params)
    },
    // 导出
    handleExport() {
      if (!this.hasTab) {
        this.$toast({
          content: this.$t('暂无数据导出'),
          type: 'warning'
        })
        return
      }
      let { tabIndex } = this.$refs.templateRef.getCurrentTabRef()
      let { gridId, grid } = this.componentConfig[tabIndex]
      let obj = JSON.parse(sessionStorage.getItem(gridId))?.visibleCols
      let field = []
      if (obj !== undefined && obj.length) {
        obj.forEach((item) => {
          if (item?.field) {
            if (item.field.slice(-4) === 'Date' && item.field !== 'itemVoucherDate') {
              const fieldName = item.field.slice(0, -4)
              field.push(fieldName)
            } else {
              field.push(item.field)
            }
          }
        })
      } else {
        grid.columnData.forEach((item) => {
          if (item?.field) {
            if (item.field.includes('Date')) {
              const fieldName = item.field.replace('Date', '')
              field.push(fieldName)
            } else {
              field.push(item.field)
            }
          }
        })
      }
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const currentTableCode = this.tabTypeList[this.currentTypeTabIndex].code
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 10000 },
        pageFlag: true,
        rules: rule.rules || [],
        defaultRules: [
          {
            field: 'reconciliationTypeCode',
            operator: 'equal',
            value: currentTableCode
          },
          {
            field: 'companyCode',
            operator: 'equal',
            value: this.tabList[this.currentTabIndex].companyCode
          }
        ]
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationCollaboration
        .reconciliationHeaderWaitExport(params, field)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
    },
    // 判断选择的数据是否符合要求
    checkSelectedValid(data) {
      const { selectedData: selectedList, actionType } = data
      let valid = true
      let validMarkFreeze = true // 冻结状态
      let validProvisional = true // 是否执行价
      let validBusinessType = true // 业务类型
      let validCompany = true // 公司
      let validSupplier = true // 供应商
      let validCurrency = true // 币种
      if (selectedList && selectedList.length > 0) {
        if (actionType === this.actionType.createStatement) {
          // 创建对账单
          for (let i = 0; i < selectedList.length; i++) {
            // 可创建条件：
            // 冻结状态为"未冻结" &&
            // 业务类型id 相同 &&
            // 公司id 相同 &&
            // 供应商code 相同 &&
            // 币种id 相同
            if (selectedList[i].frozenStatus === FrozenStatus.markFreeze) {
              // 冻结状态为"已冻结"
              this.$toast({
                content: this.$t('冻结状态为"已冻结"的数据不可创建对账单'),
                type: 'warning'
              })
              validMarkFreeze = false
              valid = false
            }
            if (selectedList[i].provisionalEstimateStatus === 1) {
              // 是否执行价为否的时候不让创建对账单
              this.$toast({
                content: this.$t('是否执行价为"否"的数据不可创建对账单'),
                type: 'warning'
              })
              validProvisional = false
              valid = false
            }
            if (selectedList[i + 1]) {
              // 存在下一条数据，与下一条数据比较
              if (selectedList[i].businessTypeId !== selectedList[i + 1].businessTypeId) {
                // 业务类型 不相同
                validBusinessType = false // 业务类型
                valid = false
              }
              if (selectedList[i].companyId !== selectedList[i + 1].companyId) {
                // 公司 不相同
                validCompany = false // 公司
                valid = false
              }
              if (selectedList[i].supplierCode !== selectedList[i + 1].supplierCode) {
                // 供应商 不相同
                validSupplier = false // 供应商
                valid = false
              }
              if (selectedList[i].currencyId !== selectedList[i + 1].currencyId) {
                // 币种 不相同
                validCurrency = false // 币种
                valid = false
              }
            }
          }
        }
      } else if (
        actionType === this.actionType.markFreeze ||
        actionType === this.actionType.unmark
      ) {
        // 冻结 || 取消冻结
        for (let i = 0; i < selectedList.length; i++) {
          if (
            selectedList[i + 1] &&
            selectedList[i].frozenStatus !== selectedList[i + 1].frozenStatus
          ) {
            valid = false
            break
          }
        }
      }

      return {
        valid,
        validMarkFreeze,
        validProvisional,
        validBusinessType,
        validCompany,
        validSupplier,
        validCurrency
      }
    },
    // 根据ID标记冻结
    doMarkFreeze(data) {
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .putSupplierFrozenById(data)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 根据ID取消标记冻结
    doUnmark(data) {
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .putSupplierCancelFrozenById(data)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },

    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  .slot-select {
    display: flex;
    position: relative;
    align-items: center;
  }
  .pur-tenant-select {
    margin: 25px;
  }
  /deep/ .supReconTypeTabs {
    flex: 1;
    display: flex;

    .mt-tabs-container {
      width: 100%;
    }
  }
  /deep/ .common-template-page {
    height: calc(100% - 104px);

    .mt-data-grid {
      height: 100%;

      > .e-grid {
        height: calc(100% - 44px);

        .e-gridcontent {
          height: calc(100% - 44px);
          .e-content {
            height: calc(100% - 10px) !important;
          }
        }
      }
    }
  }
}
</style>
