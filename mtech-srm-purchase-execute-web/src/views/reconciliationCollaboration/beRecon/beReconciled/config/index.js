import Vue from 'vue'
import utils from '@/utils/utils'
import { timeDate } from './columnComponent'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
export const ConstantType = {
  Add: '1', // 新增
  Edit: '2', // 编辑
  Look: '3' // 查看
}

// 动态 Tab 的 Toolbar
export const AsyncTabToolbar = [
  {
    id: 'DetailCreateStatement',
    icon: 'icon_solid_Createorder',
    title: i18n.t('创建对账单'),
    permission: ['O_02_0613']
  },
  {
    id: 'BatchDetailCreateStatement',
    icon: 'icon_solid_Createorder',
    title: i18n.t('批量创建对账单'),
    permission: ['O_02_0613']
  },
  {
    id: 'excelExport',
    icon: 'icon_solid_export',
    title: i18n.t('导出')
  }
  // {
  //   id: "MarkFreeze",
  //   icon: "a-icon_table_Markfreeze",
  //   title: i18n.t("冻结"),
  //   permission: ["O_02_1177"],
  // },
  // {
  //   id: "Unmark",
  //   icon: "a-icon_table_Unmarkfreezing",
  //   title: i18n.t("取消冻结"),
  //   permission: ["O_02_1178"],
  // },
]

export const Tab = {
  toBeReconciledDetail: 0 // 待对账明细
}

// 冻结标记
export const FrozenStatus = {
  markFreeze: 1, // 是
  unmark: 0 // 否
}
// 冻结标记 Options
export const FrozenStatusOptions = [
  {
    value: FrozenStatus.markFreeze,
    text: i18n.t('已冻结'),
    cssClass: 'col-inactive'
  },
  {
    value: FrozenStatus.unmark,
    text: i18n.t('未冻结'),
    cssClass: 'col-active'
  }
]

// 同步状态
export const SyncStatus = {
  synced: 1, // 是
  notSynced: 0 // 否
}

// data: yyyy-mm-dd hh:mm:ss
export const timeToDate = (data) => {
  const { formatString, value } = data
  if (formatString && value) {
    const date = new Date(value)
    if (isNaN(date.getTime())) {
      return value
    } else {
      return utils.formateTime(date, formatString)
    }
  } else {
    return value
  }
}

export const formatTableColumnData = (config) => {
  const { tab, data } = config
  const colData = []

  if (Tab.toBeReconciledDetail === tab) {
    // 待对账明细

    // 数组开头固定添加 "冻结状态" 列
    data.unshift({
      code: 'frozenStatus',
      name: i18n.t('冻结状态')
    })
  }
  data.forEach((col) => {
    let defaultCol = {
      ...col,
      field: col.code,
      headerText: col.name,
      width: data.length > 10 ? '150' : 'auto'
    }
    if (defaultCol.field === 'itemCode') {
      defaultCol.searchOptions = {
        operator: 'likeright',
        maxQueryValueLength: 20000
      }
    }
    // 处理 搜索使用 主数据选择器
    if (defaultCol.field === 'siteCode') {
      defaultCol.searchOptions = {
        ...MasterDataSelect.factorySupplierAddress
      }
    }
    if (defaultCol.field === 'frozenStatus') {
      // 冻结标记
      defaultCol.valueConverter = {
        type: 'map',
        map: FrozenStatusOptions
      }
    } else if (defaultCol.field === 'advanceInvoicing') {
      // 提前开票
      defaultCol.valueConverter = {
        type: 'map',
        map: {
          0: i18n.t('否'),
          1: i18n.t('是')
        }
      }
    } else if (defaultCol.field === 'syncStatus') {
      // 同步状态
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: SyncStatus.notSynced,
            text: i18n.t('未同步'),
            cssClass: 'col-notSynced'
          },
          {
            value: SyncStatus.synced,
            text: i18n.t('已同步'),
            cssClass: 'col-synced'
          }
        ]
      }
    } else if (defaultCol.field === 'receiveTime') {
      // 收货时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
      defaultCol.searchOptions = MasterDataSelect.dateRange
    } else if (defaultCol.field === 'fileBaseInfoList') {
      // 文件信息
      defaultCol.template = () => {
        return {
          template: Vue.component('fileBaseInfoList', {
            template: `<div @click="showFileBaseInfo" class="cell-operable-title">{{data.fileBaseInfoList | listNumFormat}}</div>`,
            data: function () {
              return { data: {} }
            },
            filters: {
              listNumFormat(value) {
                if (value && value.length > 0) {
                  return value.length
                } else {
                  return ''
                }
              }
            },
            methods: {
              showFileBaseInfo() {
                this.$parent.$emit('showFileBaseInfo', {
                  index: this.data.index,
                  value: this.data.fileBaseInfoList
                })
              }
            }
          })
        }
      }
    } else if (defaultCol.field === 'sourceType') {
      // 来源类型（枚举） 0: 上游流入1:第三方接口
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('上游流入'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('第三方接口'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'status') {
      // 单据状态 0:待对账 1:已创建对账单
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('待对账'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('已创建对账单'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'prePayStatus') {
      // 是否预付 0-否；1-是
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('否'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('是'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'itemVoucherDate') {
      // 物料凭证日期
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
      defaultCol.searchOptions = MasterDataSelect.dateRange
    } else if (defaultCol.field === 'provisionalEstimateStatus') {
      if (defaultCol.headerText == '是否执行价') {
        // 是否执行价 0-否；1-是
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('是'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('否'),
              cssClass: ''
            }
          ]
        }
      } else {
        // 是否执行价 0-否；1-是
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('否'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('是'),
              cssClass: ''
            }
          ]
        }
      }
    } else if (defaultCol.field === 'createTime') {
      // 创建时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
      defaultCol.searchOptions = MasterDataSelect.dateRange
    } else if (defaultCol.field === 'updateTime') {
      // 最后修改时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
      defaultCol.searchOptions = MasterDataSelect.dateRange
    } else if (defaultCol.field === 'type') {
      // 待对账类型:0-采购待对账；1-销售待对账
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('采购待对账'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('销售待对账'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'realPriceStatus') {
      // 正式价标识 0-无正式价；1-有正式价
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('无正式价'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('有正式价'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'inOutType') {
      // 出入库类型 0-采购入库；1-采购出库；2-销售出库；3-销售退回
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('采购入库'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('采购出库'),
            cssClass: ''
          },
          {
            value: 2,
            text: i18n.t('销售出库'),
            cssClass: ''
          },
          {
            value: 3,
            text: i18n.t('销售退回'),
            cssClass: ''
          }
        ]
      }
    }
    colData.push(defaultCol)
  })

  return colData
}
