import { i18n } from '@/main.js'
export const defaultColumnData = [
  {
    fieldId: '4',
    code: 'reconciliationTypeName',
    name: i18n.t('对账类型名称'),
    hide: 0
  },
  { fieldId: '6', code: 'businessTypeName', name: i18n.t('业务类型名称'), hide: 0 },
  { fieldId: '10', code: 'lineNo', name: i18n.t('行号'), hide: 0 },
  { fieldId: '11', code: 'itemVoucherDate', name: i18n.t('物料凭证日期'), hide: 0 },
  { fieldId: '13', code: 'sourceHeaderCode', name: i18n.t('订单号'), hide: 0 },
  { fieldId: '14', code: 'orderTypeName', name: i18n.t('订单类型'), hide: 0 },
  { fieldId: '18', code: 'supplierCode', name: i18n.t('供应商编码'), hide: 0 },
  { fieldId: '19', code: 'supplierName', name: i18n.t('供应商名称'), hide: 0 },
  { fieldId: '20', code: 'itemCode', name: i18n.t('物料编号'), hide: 0 },
  { fieldId: '21', code: 'itemName', name: i18n.t('物料名称'), hide: 0 },
  { fieldId: '24', code: 'spec', name: i18n.t('规格/型号'), hide: 0 },
  { fieldId: '25', code: 'categoryCode', name: i18n.t('品类'), hide: 0 },
  { fieldId: '31', code: 'customName', name: i18n.t('客户'), hide: 0 },
  { fieldId: '32', code: 'relationCustomOrder', name: i18n.t('销售订单号'), hide: 0 },
  { fieldId: '33', code: 'quantity', name: i18n.t('数量'), hide: 0 },
  { fieldId: '34', code: 'unitName', name: i18n.t('单位'), hide: 0 },
  {
    fieldId: '39',
    code: 'provisionalEstimateStatus',
    name: i18n.t('是否执行价'),
    hide: 0
  },
  {
    fieldId: '40',
    code: 'executeUntaxedUnitPrice',
    name: i18n.t('执行未税单价'),
    hide: 0
  },
  {
    fieldId: '41',
    code: 'executeUntaxedTotalPrice',
    name: i18n.t('执行未税总价'),
    hide: 0
  },
  {
    fieldId: '42',
    code: 'executeTaxedUnitPrice',
    name: i18n.t('执行含税单价'),
    hide: 0
  },
  {
    fieldId: '43',
    code: 'executeTaxedTotalPrice',
    name: i18n.t('执行含税总价'),
    hide: 0
  },
  { fieldId: '44', code: 'deductedTaxedPrice', name: i18n.t('扣款未税金额'), hide: 0 },
  { fieldId: '52', code: 'currencyName', name: i18n.t('币种'), hide: 0 },
  { fieldId: '53', code: 'companyCode', name: i18n.t('公司编号'), hide: 0 },
  { fieldId: '54', code: 'companyName', name: i18n.t('公司名称'), hide: 0 },
  { fieldId: '55', code: 'siteCode', name: i18n.t('地点/工厂代码'), hide: 0 },
  { fieldId: '57', code: 'stockSite', name: i18n.t('库存地点'), hide: 0 },
  { fieldId: '61', code: 'receiveUserName', name: i18n.t('收货人'), hide: 0 },
  { fieldId: '62', code: 'receiveTime', name: i18n.t('收货时间'), hide: 0 },
  { fieldId: '65', code: 'remark', name: i18n.t('采方备注'), hide: 0 },
  { fieldId: '66', code: 'syncStatus', name: i18n.t('同步状态'), hide: 0 },
  { fieldId: '70', code: 'inOutType', name: i18n.t('出入库类型'), hide: 0 },
  { fieldId: '73', code: 'receiveCode', name: i18n.t('物料凭证'), hide: 0 },
  {
    fieldId: '77',
    code: 'priceDateRangeStartDate',
    name: i18n.t('价格有效期开始'),
    hide: 0
  },
  {
    fieldId: '78',
    code: 'priceDateRangeEndDate',
    name: i18n.t('价格有效期结束'),
    hide: 0
  },
  { fieldId: '79', code: 'taxRate', name: i18n.t('税率'), hide: 0 },
  { fieldId: '80', code: 'rateDifferent', name: i18n.t('差异率'), hide: 0 },
  { fieldId: '81', code: 'amountDifferent', name: i18n.t('金额差异'), hide: 0 },
  { fieldId: '82', code: 'taxName', name: i18n.t('税率名称'), hide: 0 },
  { fieldId: '83', code: 'receiveItemNo', name: i18n.t('凭证行号'), hide: 0 }
]
