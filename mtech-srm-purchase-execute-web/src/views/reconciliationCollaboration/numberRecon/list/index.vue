<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      class="self-set-table"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { cols } from './config'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  data() {
    return {
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          toolbar: [
            {
              id: 'excelExport',
              icon: 'icon_solid_export',
              title: this.$t('导出')
            }
          ],
          gridId: this.$tableUUID.reconciliationCollaboration.quantitySupplier.list,

          grid: {
            lineSelection: 0,
            columnData: cols,
            // 销售待对账明细接口
            asyncConfig: {
              url: `${BASE_TENANT}/sup/quantity/reconciliation/query`,
              rules: [],
              defaultRules: [
                {
                  field: 'status',
                  operator: 'equal',
                  value: 1
                }
              ]
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id == 'excelExport') {
        this.handleExport()
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'feedback') {
        this.$router.push(`number-detail-supplier?id=${e.data.id}&type=feedback`)
      } else if (e.tool.id == 'view') {
        this.$router.push(`number-detail-supplier?id=${e.data.id}&type=view`)
      }
    },
    handleClickCellTitle(e) {
      if (e.field == 'code') {
        this.$router.push(`number-detail-supplier?id=${e.data.id}&type=view`)
      }
    },
    // 导出
    handleExport() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 10000 },
        pageFlag: true,
        rules: rule.rules || [],
        defaultRules: [
          {
            field: 'status',
            operator: 'equal',
            value: 1
          }
        ]
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationCollaboration.reconciliationQuantityExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>

<style></style>
