<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      class="self-set-table"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { cols } from './config'
import Vue from 'vue'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  data() {
    return {
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          toolbar: [
            {
              id: 'excelExport',
              icon: 'icon_solid_export',
              title: this.$t('导出')
            },
            {
              id: 'printRecon',
              icon: 'icon_table_print',
              title: this.$t('打印')
            }
          ],
          gridId: this.$tableUUID.reconciliationCollaboration.quantitySupplier.query,

          grid: {
            lineSelection: 0,
            columnData: cols,
            // 销售待对账明细接口
            asyncConfig: {
              url: `${BASE_TENANT}/sup/quantity/reconciliation/query`,
              rules: []
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      const selectRows = e.gridRef.getMtechGridRecords()
      console.log(selectRows)
      const commonToolbar = [
        'Add',
        'Filter',
        'Refresh',
        'Setting',
        'more-option-btn',
        'refreshDataByLocal',
        'filterDataByLocal',
        'excelExport'
      ]
      if (selectRows.length === 0 && !commonToolbar.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedIds = []
      selectRows.forEach((item) => selectedIds.push(item.id))
      if (e.toolbar.id == 'excelExport') {
        this.handleExport()
      } else if (e.toolbar.id == 'printRecon') {
        this.handlePrint(selectedIds)
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'view') {
        this.$router.push(`number-detail-supplier?id=${e.data.id}&type=view&come=detail`)
      }
    },
    handleClickCellTitle(e) {
      if (e.field == 'code') {
        this.$router.push(`number-detail-supplier?id=${e.data.id}&type=view&come=detail`)
      }
    },
    // 导出
    handleExport() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 10000 },
        pageFlag: true,
        rules: rule.rules || []
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationCollaboration.reconciliationQuantityExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 打印
    handlePrint(ids) {
      this.$API.reconciliationCollaboration.reconciliationPrint({ ids: ids }).then((res) => {
        if (res?.data?.type === 'application/json') {
          const reader = new FileReader()
          reader.readAsText(res?.data, 'utf-8')
          reader.onload = () => {
            console.log('======', reader)
            const readerRes = reader.result
            const resObj = JSON.parse(readerRes)
            Vue.prototype.$toast({
              content: resObj.msg,
              type: 'error'
            })
          }

          return
        }
        const content = res.data
        this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
        // window.open(this.pdfUrl);
        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = this.pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    }
  }
}
</script>

<style></style>
