import { i18n } from '@/main.js'
import { timeNumberToDate } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'
export const cols = [
  {
    width: '200',
    field: 'code',
    headerText: i18n.t('对账单号'),
    valueConverter: {
      type: 'placeholder',
      placeholder: '--' //placeholder可不传，默认值为"未填"
    },
    cellTools: [
      {
        id: 'view',
        icon: 'icon_outline_Preview',
        title: i18n.t('查看')
      }
    ]
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待反馈'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('反馈正常'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('反馈异常'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('已确认'), cssClass: 'col-active' },
        { value: 5, text: i18n.t('关闭'), cssClass: 'col-inactive' },
        { value: 6, text: i18n.t('异常确认'), cssClass: 'col-inactive' },
        { value: 7, text: i18n.t('异常已处理'), cssClass: 'col-inactive' }
      ]
    }
  },
  {
    width: '150',
    field: 'itemCertificateMonth',
    headerText: i18n.t('出入库月份'),
    ignore: true
  },
  {
    width: 0,
    field: 'year',
    headerText: i18n.t('年份')
  },
  {
    width: 0,
    field: 'month',
    headerText: i18n.t('月份')
  },
  {
    width: '350',
    field: 'companyCode',
    headerText: i18n.t('客户公司'),
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return row.companyCode + '-' + row.companyName
      }
    }
  },
  {
    width: '350',
    field: 'factoryCode',
    headerText: i18n.t('客户工厂'),
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return row.factoryCode + '-' + row.factoryName
      }
    }
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    width: '300',
    field: 'remark',
    headerText: i18n.t('采方备注')
  },
  {
    width: '300',
    field: 'supRemark',
    headerText: i18n.t('供方备注')
  }
]
