<template>
  <div>
    <sc-table
      ref="sctableRef"
      grid-id="4ec59e05-7e9e-4044-913f-bd45fc092d1c"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :is-show-refresh-bth="false"
      :scroll-x="{ gt: 0, oSize: 20 }"
      :scroll-y="{ gt: 0, oSize: 100 }"
      :edit-config="editConfig"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <div style="margin-top: 10px">{{ $t('总条数') }}：{{ total }}</div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import ScTable from '@/components/ScTable/src/index'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  components: { ScTable },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          align: 'center'
        },
        {
          field: 'orderCode',
          title: this.$t('采购订单号'),
          minWidth: 120
        },
        {
          field: 'lineNo',
          title: this.$t('行号')
        },
        {
          field: 'receiveItemNo',
          title: this.$t('物料凭证行号'),
          minWidth: 120
        },
        {
          field: 'itemCode',
          title: this.$t('物料编号'),
          minWidth: 140
        },
        {
          field: 'itemName',
          title: this.$t('物料名称'),
          minWidth: 160
        },
        {
          field: 'unitCode',
          title: this.$t('单位'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.unitName : ''
          }
        },
        {
          field: 'warehousingCount',
          title: this.$t('入库数量'),
          minWidth: 120
        },
        {
          field: 'orderTypeName',
          title: this.$t('订单类型'),
          minWidth: 120
        },
        {
          field: 'locationCode',
          title: this.$t('库存地点'),
          minWidth: 160,
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.locationName : ''
          }
        },
        {
          field: 'certificateTime',
          title: this.$t('凭证日期'),
          minWidth: 120,
          formatter: ({ cellValue }) => {
            let text = ''
            if (cellValue && cellValue !== '0') {
              text = dayjs(Number(cellValue)).format('YYYY-MM-DD')
            }
            return text
          }
        },
        {
          field: 'transType',
          title: this.$t('事务类型'),
          minWidth: 120
        },
        {
          field: 'itemCertificateNo',
          title: this.$t('物料凭证号'),
          minWidth: 120
        },
        {
          field: 'itemCertificateTime',
          title: this.$t('物料凭证日期'),
          minWidth: 140,
          formatter: ({ cellValue }) => {
            let text = ''
            if (cellValue && cellValue !== '0') {
              text = dayjs(Number(cellValue)).format('YYYY-MM-DD')
            }
            return text
          }
        },
        {
          field: 'deliveryNo',
          title: this.$t('送货单号'),
          minWidth: 120
        },
        {
          field: 'receiveTime',
          title: this.$t('过账日期'),
          minWidth: 140,
          formatter: ({ cellValue }) => {
            let text = ''
            if (cellValue && cellValue !== '0') {
              text = dayjs(Number(cellValue)).format('YYYY-MM-DD')
            }
            return text
          }
        },
        {
          field: 'remark',
          title: this.$t('采方备注'),
          minWidth: 160
        },
        {
          field: 'supRemark',
          title: this.$t('供方备注'),
          minWidth: 160,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.supRemark} clearable />]
            }
          }
        }
      ]
    },
    toolbar() {
      let arr = [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }]
      return arr
    },
    editConfig() {
      return {
        enabled: this.$route.query?.type === 'feedback',
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    getTableData() {
      this.loading = true
      this.$API.reconciliationCollaboration
        .getDetailReconciliationApi({
          id: this.$route.query?.id
        })
        .then((res) => {
          this.loading = false
          if (res.code === 200) {
            this.tableData = res.data
            this.total = this.tableData.length
          }
        })
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    // 数量对账-对账单明细下载
    handleExport() {
      this.$store.commit('startLoading')
      this.$API.reconciliationCollaboration
        .postNumListSupplierDownload(this.$route.query.id)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
    }
  }
}
</script>
