<template>
  <div>
    <sc-table
      ref="sctableRef"
      grid-id="c3365047-c5af-4c6c-9279-133c9d759424"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :is-show-refresh-bth="false"
      :scroll-x="{ gt: 0, oSize: 20 }"
      :scroll-y="{ gt: 0, oSize: 100 }"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <div style="margin-top: 10px">{{ $t('总条数') }}：{{ total }}</div>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  components: { ScTable },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          align: 'center'
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码'),
          minWidth: 140
        },
        {
          field: 'itemName',
          title: this.$t('物料名称'),
          minWidth: 160
        },
        {
          field: 'unitName',
          title: this.$t('单位')
        },
        {
          field: 'warehousingCount',
          title: this.$t('入库数量'),
          minWidth: 120
        }
      ]
    },
    toolbar() {
      let arr = [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }]
      return arr
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    getTableData() {
      this.loading = true
      this.$API.reconciliationCollaboration
        .summaryQuantityApi({
          id: this.$route.query?.id
        })
        .then((res) => {
          this.loading = false
          if (res.code === 200) {
            this.tableData = res.data
            this.total = this.tableData.length
          }
        })
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    // 数量对账-对账单明细下载
    handleExport() {
      this.$store.commit('startLoading')
      this.$API.reconciliationCollaboration
        .postNumSummaryListSupplierDownload(this.$route.query.id)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
    }
  }
}
</script>
