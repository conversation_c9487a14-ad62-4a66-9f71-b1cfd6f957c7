<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <div :class="['infos', 'mr20', 'status-box', `status-box-${headerInfo.status}`]">
        {{ headerInfo.status | statusFormat }}
      </div>
      <div class="infos mr20">{{ $t('数量对账单号：') }}{{ headerInfo.code || '-' }}</div>
      <div class="infos mr20">{{ $t('创建人：') }}{{ headerInfo.createUserName }}</div>
      <div class="infos">{{ $t('创建时间：') }}{{ headerInfo.createTime | dateFormat }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-if="$route.query.type == 'feedback'"
        @click="doSubmit(2)"
        >{{ $t('接受') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-if="$route.query.type == 'feedback'"
        @click="doSubmit(3)"
        >{{ $t('拒绝') }}</mt-button
      >

      <div class="sort-box" @click="doExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="headerInfo">
        <mt-form-item prop="company" :label="$t('客户公司')">
          <mt-input v-model="headerInfo.company" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="factory" :label="$t('客户工厂')">
          <mt-input v-model="headerInfo.factory" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="itemCertificateMonth" :label="$t('月份')">
          <mt-input v-model="headerInfo.itemCertificateMonth" :disabled="true"></mt-input>
        </mt-form-item>

        <mt-form-item prop="remark" :label="$t('采方备注')" class="full-width">
          <mt-input
            v-model="headerInfo.remark"
            max-length="200"
            :title="headerInfo.remark"
            :disabled="true"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="supRemark" :label="$t('供方备注')" class="full-width">
          <mt-input
            v-model="headerInfo.supRemark"
            max-length="200"
            :disabled="$route.query.type == 'view'"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { i18n } from '@/main.js'
export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      isExpand: true
    }
  },

  filters: {
    statusFormat(value) {
      let str = ''
      if (value == -1) {
        str = i18n.t('关闭')
      } else if (value == 0) {
        str = i18n.t('新建')
      } else if (value == 1) {
        str = i18n.t('待反馈')
      } else if (value == 2) {
        str = i18n.t('反馈正常')
      } else if (value == 3) {
        str = i18n.t('反馈异常')
      } else if (value == 4) {
        str = i18n.t('已完成')
      } else if (value == 5) {
        str = i18n.t('关闭')
      } else if (value == 6) {
        str = i18n.t('异常确认')
      } else if (value == 7) {
        str = i18n.t('异常已处理')
      }
      return str
    },
    dateFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }

      return str
    }
  },
  methods: {
    // 返回
    goBack() {
      if (this.$route.query.come) {
        this.$router.replace(`number-recon-supplier-query`)
      } else {
        this.$router.replace(`number-recon-supplier`)
      }
    },
    // 提交
    doSubmit(flag) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: flag === 2 ? this.$t('确认接受吗？') : this.$t('确认拒绝吗？')
        },
        success: () => {
          this.$emit('doSubmit', flag)
        }
      })
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    }
  }
}
</script>

<style lang="scss" scoped>
.status-box {
  line-height: 20px;
  padding: 4px;
  border-radius: 2px;
  margin: 0 36px 0 10px;
  font-size: 12px;
  font-family: PingFangSC;
  font-weight: 500;
  color: #6386c1;
  background: rgba(99, 134, 193, 0.1);
  &--1 {
    color: #9baac1;
    background: rgba(155, 170, 193, 0.1);
  }
}
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px dashed #e6e9ed;
    margin-bottom: 24px;

    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
