<template>
  <div class="full-height">
    <mt-template-page
      v-if="entryCode"
      ref="templateRef"
      class="template-height has-page"
      :template-config="pageConfig"
    >
    </mt-template-page>
    <span v-else></span>
  </div>
</template>
<script>
import { BASE_TENANT } from '@/utils/constant'
import { operationLog } from './config/operationLog.js'
export default {
  props: {
    entryCode: {
      type: String,
      default: '0'
    },
    logOperationType: {
      // BuyerOrder/RequestHeader/ReconciliationHeader(采购订单、采购申请、对账单)
      type: String,
      default: ''
    }
  },
  data() {
    const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))

    return {
      userInfo, // 用户信息
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [],
          grid: {
            columnData: operationLog,
            dataSource: [],
            allowPaging: true,
            asyncConfig: {}
          }
        }
      ]
    }
  },
  watch: {
    entryCode: {
      handler(val) {
        if (val) {
          this.$set(this.pageConfig[0].grid, 'asyncConfig', {
            url: `${BASE_TENANT}/log/queryOperationLog`,
            params: {
              reconciliationType: 2, //"RequestHeader",
              reconciliationCode: val,
              tenantId: this.userInfo.tenantId
            }
          })
        }
      },
      immediate: true
    }
  },
  mounted() {},
  methods: {}
}
</script>
<style lang="scss" scoped>
.full-height {
  width: 100%;
  flex: 1;

  /deep/ .toolbar-container {
    display: none;
  }
}
</style>
