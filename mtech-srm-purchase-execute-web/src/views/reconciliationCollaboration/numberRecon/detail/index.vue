<template>
  <div class="full-height pt20 vertical-flex-box">
    <!-- 头部信息 -->
    <top-info
      class="flex-keep"
      ref="topInfoRef"
      :header-info="headerInfo"
      @doSubmit="doSubmit"
      @doExpand="doExpand"
    ></top-info>

    <mt-tabs
      tab-id="reconci-tab"
      :e-tab="false"
      :data-source="tabList"
      @handleSelectTab="handleSelectTab"
      class="flex-keep toggle-tab"
    ></mt-tabs>

    <div class="toggle-content">
      <!-- 对账明细 -->
      <div v-if="headerInfo.id">
        <recon-detail ref="saleReconRef" v-show="tabIndex == 0"></recon-detail>
      </div>
      <!-- 数量汇总 -->
      <div>
        <summary-quantity ref="summaryQuantityRef" v-show="tabIndex == 1"></summary-quantity>
      </div>

      <!-- 相关附件 -->
      <relative-file
        ref="relativeFileRef"
        v-show="tabIndex == 2"
        :module-file-list="moduleFileList"
      ></relative-file>

      <!-- 操作日志 -->
      <operation-log
        :entry-code="headerInfo.code"
        log-operation-type=""
        v-if="tabIndex == 3"
      ></operation-log>
    </div>
  </div>
</template>

<script>
/**
 * entryType: create - 创建；view - 查看
 */
import { nodeType } from '@/components/businessComponents/relativeFileNoDocId/config/relative.js'
export default {
  components: {
    topInfo: require('./components/topInfo.vue').default,
    reconDetail: require('./components/reconDetail.vue').default,
    summaryQuantity: require('./components/summaryQuantity.vue').default,
    relativeFile: require('@/components/businessComponents/relativeFileNoDocId/index.vue').default,
    operationLog: require('./components/operationLog/index.vue').default
  },
  data() {
    return {
      tabIndex: 0,
      tabList: [
        {
          title: this.$t('对账明细')
        },
        {
          title: this.$t('数量汇总')
        },
        {
          title: this.$t('相关附件')
        },
        {
          title: this.$t('操作日志')
        }
      ],
      moduleFileList: [],
      headerInfo: null
    }
  },
  mounted() {
    this.getHeader()
  },
  methods: {
    getHeader() {
      this.$API.reconciliationCollaboration
        .headerReconciliationApi({
          id: this.$route.query?.id
        })
        .then((res) => {
          if (res.code === 200) {
            let headerInfo = {
              ...res.data,
              company: res.data?.companyCode + '-' + res.data?.companyName,
              factory: res.data?.factoryCode + '-' + res.data?.factoryName
            }
            this.headerInfo = headerInfo
            this.getFiles()
          }
        })
    },
    getFiles() {
      this.$API.reconciliationCollaboration
        .filesDetailReconciliationApi({
          id: this.$route.query?.id
        })
        .then((res) => {
          if (res.code === 200) {
            this.formatFile(res.data?.purFiles, res.data?.supFiles, res.data?.abnormalFiles)
          }
        })
    },
    // 整合附件
    formatFile(purFiles, supFiles, abnormalFiles) {
      if (this.$route.query.type == 'feedback') {
        this.moduleFileList = [
          {
            id: '01',
            code: 'reconciliation_settled_header_file', // 清账整单附件code
            nodeName: '采方 - 整单附件',
            type: nodeType.mainViewData,
            dataSource: purFiles || []
          },
          {
            id: '02',
            code: 'reconciliation_settled_header_file', // 清账整单附件code
            nodeName: '供方 - 整单附件',
            type: nodeType.mainUpdate
          }
        ]
      } else {
        this.moduleFileList = [
          {
            id: '01',
            code: 'reconciliation_settled_header_file', // 清账整单附件code
            nodeName: '采方 - 整单附件',
            type: nodeType.mainViewData,
            dataSource: purFiles || []
          },
          {
            id: '02',
            code: 'reconciliation_gong',
            nodeName: '供方 - 整单附件',
            type: nodeType.mainViewData,
            dataSource: supFiles || []
          }
        ]
      }
      this.moduleFileList.push({
        id: '03',
        code: 'reconciliation_abnormal',
        nodeName: this.$t('采方 - 异常处理附件'),
        type: nodeType.mainViewData,
        dataSource: abnormalFiles || []
      })
    },
    doSubmit(flag) {
      let _dataSource =
        this.$refs.saleReconRef.$refs.sctableRef.$refs.xGrid.getTableData().fullData || []

      let _waits = _dataSource.map((i) => {
        return {
          id: i.id,
          supRemark: i.supRemark
        }
      })

      let submitData = {
        header: {
          id: this.$route.query.id,
          supRemark: this.headerInfo.supRemark
        },
        fileList: this.$refs.relativeFileRef.getUploadFlies(this.moduleFileList[1].id),
        items: _waits,
        status: flag
      }

      this.$store.commit('startLoading')
      this.$API.reconciliationCollaboration
        .reconciliationFeedback(submitData)
        .then((res) => {
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$router.replace(`number-recon-supplier`)
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    handleSelectTab(e) {
      this.tabIndex = e
    }
  }
}
</script>
