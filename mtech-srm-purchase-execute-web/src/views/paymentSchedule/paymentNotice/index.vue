<template>
  <div class="full-height pt20 pc-my-apply">
    <mt-template-page
      class="template-height"
      slot="slot"
      ref="template"
      @handleClickToolBar="handleClickToolBar"
      :template-config="pageConfig0"
    >
    </mt-template-page>
  </div>
</template>

<script>
// import { } from "./config/index.js";
import { BASE_TENANT } from '@/utils/constant'
import { columnData } from './config/index.js'

export default {
  components: {},
  data() {
    return {
      pageConfig0: [
        {
          useToolTemplate: false, // 使用预置表格操作按钮(新增、编辑、删除)
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: {
            tools: [
              [
                {
                  id: 'Claim',
                  icon: 'icon_table_restart',
                  title: this.$t('更新状态'),
                  permission: ['O_02_0491']
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columnData,
            lineIndex: 1,
            dataSource: [],
            frozenColumns: 1,
            asyncConfig: {
              url: `${BASE_TENANT}/paymentNotice/query`
            }
          }
        }
      ],
      apiWaitingQuantity: 0 // 调用的api正在等待数
    }
  },
  mounted() {},
  methods: {
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    },
    handleClickToolBar(e) {
      console.log(e.grid.getSelectedRecords())
      if (e.grid.getSelectedRecords()?.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // 已认领的不能点击 分配、认领按钮
      let _headerIdList = []
      let tenantId = e.grid.getSelectedRecords()[0].tenantId

      e.grid.getSelectedRecords().map((item) => {
        _headerIdList.push(item.paymentNoticeItemId)
      })
      let obj = {
        headerIdList: _headerIdList,
        tenantId: tenantId
      }
      this.$API.paymentSchedule.paymentNoticeUpdateStatus(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
        this.$refs.template.refreshCurrentGridData()
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
