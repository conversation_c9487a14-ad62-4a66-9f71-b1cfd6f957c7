import { i18n } from '@/main.js'
import Vue from 'vue'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'companyCode',
    headerText: i18n.t('公司代码')
  },
  {
    width: '150',
    field: 'payType',
    headerText: i18n.t('付款类型')
  },
  {
    width: '150',
    field: 'currencyCode',
    headerText: i18n.t('币种')
  },
  {
    width: '150',
    field: 'noticeDate',
    headerText: i18n.t('月份')
  },
  {
    width: '150',
    field: 'paymentNoticeId',
    headerText: i18n.t('支付通知单')
  },
  {
    width: '150',
    field: 'paymentStatus',
    headerText: i18n.t('支付通知单状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: '1', text: i18n.t('审批中'), cssClass: 'col-active' },
        { value: '2', text: i18n.t('已审批'), cssClass: 'col-active' },
        { value: '3', text: i18n.t('付款中'), cssClass: 'col-active' },
        { value: '4', text: i18n.t('部分已付款'), cssClass: 'col-active' },
        { value: '5', text: i18n.t('已付款'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '150',
    field: 'paymentNoticeItemId',
    headerText: i18n.t('支付通知单行')
  },
  // {
  //   width: "150",
  //   field: "paymentStatus",
  //   headerText: i18n.t("支付通知单状态"),
  //   valueConverter: {
  //     type: "map",
  //     map: [
  //       { value: 1, text: i18n.t("审批中"), cssClass: "col-active" },
  //       { value: 2, text: i18n.t("已审批"), cssClass: "col-active" },
  //       { value: 3, text: i18n.t("付款中"), cssClass: "col-active" },
  //       { value: 4, text: i18n.t("部分已付款"), cssClass: "col-active" },
  //       { value: 5, text: i18n.t("已付款"), cssClass: "col-active" },
  //     ],
  //   },
  // },
  {
    width: '150',
    field: 'currency',
    headerText: i18n.t('原币金额')
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商')
  },
  {
    width: '150',
    field: 'payeeName',
    headerText: i18n.t('收款方名称')
  },
  {
    width: '150',
    field: 'receivingBankCode',
    headerText: i18n.t('收款方银行号')
  },
  {
    width: '150',
    field: 'receivingBankLinkCode',
    headerText: i18n.t('收款方银行联行号')
  },
  {
    width: '150',
    field: 'suggestPayDate',
    headerText: i18n.t('建议支付日期')
  },
  {
    width: '150',
    field: 'paymentMode',
    headerText: i18n.t('付款方式'),
    template: function () {
      return {
        template: Vue.component('paymentMode', {
          template: `<div>{{map(this.data.paymentMode)}}</div>`,
          data() {
            return { data: {} }
          },
          mounted() {
            console.log(this.data)
          },
          methods: {
            map(value) {
              const obj = {
                MonthlyPayment: i18n.t('月结付款（30天）'),
                QuarterlyPayment: i18n.t('季度结付款（90天）'),
                PayOnDelivery: i18n.t('到货付款')
              }
              let str = ''
              if (obj[value]) {
                str = obj[value]
              }
              return str
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'invoiceLinkCode',
    headerText: i18n.t('关联发票号')
  }
]
