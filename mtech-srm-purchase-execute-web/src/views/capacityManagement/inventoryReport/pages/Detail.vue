<!-- 箱体视图 -->
<template>
  <div class="full-height vertical-flex-box">
    <collapse-search :is-grid-display="true" @reset="handleCustomReset" @search="handleSearch">
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <!-- 工厂编码 -->
        <mt-form-item prop="siteCodeList" :label="$t('工厂编码')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.siteCodeList"
            :data-source="siteCodeListOptions"
            :allow-filtering="true"
            :multiple="true"
            filter-type="Contains"
            :filtering="getSiteCodeList"
            :fields="{ text: 'theCodeName', value: 'siteCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 供应商编码 -->
        <mt-form-item prop="supplierCodeList" :label="$t('供应商')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.supplierCodeList"
            :data-source="supplierTypeList"
            :allow-filtering="true"
            filter-type="Contains"
            :multiple="true"
            :filtering="getSupplierList"
            :fields="{ text: 'theCodeName', value: 'supplierCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('产能时间')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :placeholder="$t('请选择')"
            show-clear-button
            :change="(e) => dateChange(e)"
          />
        </mt-form-item>
        <mt-form-item prop="boxBody" :label="$t('箱体')">
          <mt-input
            v-model="searchFormModel.boxBody"
            :placeholder="$t('请输入')"
            show-clear-button
          />
        </mt-form-item>
        <mt-form-item prop="supplierName" :label="$t('部件')">
          <mt-input
            v-model="searchFormModel.supplierName"
            :placeholder="$t('请输入')"
            show-clear-button
          />
        </mt-form-item>
        <!-- 统计类型 -->
        <mt-form-item prop="statisticTypeList" :label="$t('统计类型')">
          <mt-multi-select
            v-model="searchFormModel.statisticTypeList"
            :data-source="statisticTypeOptions"
            :allow-filtering="true"
            :multiple="true"
            filter-type="Contains"
            :fields="{ text: 'statisticType', value: 'value' }"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
            @change="handleCustomSearch"
          />
        </mt-form-item>
        <mt-form-item prop="statisticDimensionList" :label="$t('统计维度')">
          <mt-multi-select
            v-model="searchFormModel.statisticDimensionList"
            :data-source="statisticDimensionList"
            :allow-filtering="true"
            :multiple="true"
            filter-type="Contains"
            :fields="{ text: 'statisticDimension', value: 'value' }"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
            @change="handleCustomSearch"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <div class="flex-fit">
      <ScTable
        ref="xTable"
        :columns="columns"
        :table-data="tableData"
        height="auto"
        header-align="left"
        align="left"
        show-overflow
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        :span-method="mergeRowMethod"
        style="padding-top: unset"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #historyNoDefault="{ row }">
          <vxe-input
            v-model="row.historyNo"
            :placeholder="$t('请输入')"
            type="number"
            min="0"
            clearable
          />
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, toolbar, statisticTypeList, statisticDimensionList } from '../config/detail'
import { getHeadersFileName, download, addCodeNameKeyInList } from '@/utils/utils'
import { utils } from '@mtech-common/utils'
export default {
  components: { CollapseSearch, ScTable },
  data() {
    return {
      searchFormModel: {
        statisticTypeList: ['OUTPUT_QUANTITY'],
        statisticDimensionList: []
      },
      siteCodeListOptions: [],
      supplierTypeList: [],
      columns: columnData,
      tableData: [],
      toolbar,
      pageSettings: {
        currentPage: 1,
        pageSize: 20, // 当前每页数据量
        pageCount: 5,
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [20, 50, 100, 200]
      },

      statisticTypeList,
      statisticDimensionList
    }
  },
  created() {
    this.handleSearch()
  },
  computed: {
    statisticTypeOptions() {
      if (this.searchFormModel.statisticDimensionList.length !== 0) {
        return this.statisticTypeList.filter(
          (item) => item.value !== 'DAY_CAPACITY_98' && item.value !== 'DAY_CAPACITY_95'
        )
      } else {
        return this.statisticTypeList
      }
    }
  },
  mounted() {
    this.getSupplierList = utils.debounce(this.getSupplierList, 1000)
    this.getSiteCodeList = utils.debounce(this.getSiteCodeList, 1000)
    this.getSiteCodeList()
    this.getSupplierList()
  },
  methods: {
    // 获取供应商信息
    getSupplierList(args = { text: '' }) {
      const { text } = args
      const param = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData.getSupplier(param).then((res) => {
        this.supplierTypeList.length = 0
        const list = res.data || []
        this.supplierTypeList = addCodeNameKeyInList({
          firstKey: 'supplierCode',
          secondKey: 'supplierName',
          list
        }).filter((n) => !!n.theCodeName && !!n.supplierCode)
      })
    },
    // 获取工厂信息
    getSiteCodeList(args = { text: '' }) {
      const { text } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteCodeListOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
          }
        })
        .catch(() => {})
    },
    mergeRowMethod({ _rowIndex, column }) {
      const fields = [
        'serialNumber',
        'siteCode',
        'boxBody',
        'parts',
        'supplierCode',
        'supplierAbbr',
        'preCycle',
        'cycle',
        'outputCapacity98',
        'outputCapacity95'
      ]
      if (fields.includes(column.property)) {
        let len = this.searchFormModel.statisticTypeList.length || 6
        if (_rowIndex % len === 0) {
          return { rowspan: len, colspan: 1 }
        } else {
          return { rowspan: 0, colspan: 0 }
        }
      }
    },
    dateChange(e) {
      if (e && e.startDate) {
        this.searchFormModel['beginDate'] = dayjs(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        ).valueOf()
        this.searchFormModel['endDate'] = dayjs(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        ).valueOf()
      } else {
        this.searchFormModel['beginDate'] = null
        this.searchFormModel['endDate'] = null
      }
    },
    handleClickToolBar(args) {
      const { code } = args
      if (code === 'Export') {
        this.handleExport()
      }
    },
    handleExport() {
      let params = {
        page: {
          pages: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        },
        dayCapacityLevel: '按箱体',
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.capacityManagement.exportInventoryReportApi(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          if (key === 'statisticTypeList') {
            this.searchFormModel[key] = ['OUTPUT_QUANTITY']
          } else if (key === 'statisticDimensionList') {
            this.searchFormModel[key] = []
          } else {
            this.searchFormModel[key] = null
          }
        }
      }
      this.handleCustomSearch()
    },
    handleSearch() {
      this.pageSettings.currentPage = 1
      this.handleCustomSearch()
    },
    handleCustomSearch() {
      let params = {
        page: {
          pages: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        },
        dayCapacityLevel: '按箱体',
        ...this.searchFormModel
      }
      if (params.createTime) {
        delete params.createTime
      }
      this.apiStartLoading()
      this.$API.capacityManagement
        .pageInventoryReportApi(params)
        .then((res) => {
          if (res?.code == 200) {
            const total = res.data.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            const records = res.data?.records || []
            this.handleColumns({ records })
            this.handleDataSource({ records })
          }
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    handleColumns(args) {
      const { records } = args
      const dynamicColumn = []
      records[0]?.headList?.forEach((item) => {
        dynamicColumn.push({
          title: item,
          field: item,
          width: 120
        })
      })
      let currentColumn = columnData
      if (this.searchFormModel.statisticDimensionList.length !== 0) {
        currentColumn.forEach((item) => {
          if (item.field === 'siteCode') {
            item.hide = !this.searchFormModel.statisticDimensionList.includes('site_code')
          } else if (item.field === 'boxBody') {
            item.hide = !this.searchFormModel.statisticDimensionList.includes('box_body')
          } else if (item.field === 'parts') {
            item.hide = !this.searchFormModel.statisticDimensionList.includes('parts')
          } else if (item.field === 'supplierCode' || item.field === 'supplierAbbr') {
            item.hide = !this.searchFormModel.statisticDimensionList.includes('supplier_code')
          } else if (
            item.field === 'preCycle' ||
            item.field === 'cycle' ||
            item.field === 'outputCapacity98' ||
            item.field === 'outputCapacity95'
          ) {
            item.hide = true
          } else {
            item.hide = false
          }
        })
      } else {
        currentColumn.forEach((item) => {
          item.hide = false
        })
      }
      currentColumn = currentColumn.filter((v) => !v.hide)
      this.columns = currentColumn.concat(dynamicColumn)
    },
    handleDataSource(args) {
      const { records } = args
      const data = []
      const arr =
        this.searchFormModel.statisticTypeList.length !== 0
          ? this.statisticTypeOptions.filter((item) => {
              return this.searchFormModel.statisticTypeList.includes(item.value)
            })
          : this.statisticTypeOptions
      records.forEach((item, index) => {
        arr.forEach((ele) => {
          let obj = { ...item }
          obj.serialNumber = index + 1
          obj.statisticType = ele.statisticType
          obj.total = item[ele.total]
          item.headList.forEach((field, i) => {
            obj[field] = item[ele.field][i]
          })
          data.push(obj)
        })
      })
      this.tableData = data
    },
    handleCurrentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.handleCustomSearch()
    },
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    apiStartLoading() {
      this.$store.commit('startLoading')
    },
    apiEndLoading() {
      this.$store.commit('endLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  & .span-style {
    display: flex;
    flex-flow: column;
    & span:nth-child(1) {
      border-bottom: 1px solid #cfd4d9;
    }
  }
}
</style>
