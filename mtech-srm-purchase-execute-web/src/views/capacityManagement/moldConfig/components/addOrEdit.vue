<template>
  <div class="dialog-form">
    <mt-dialog
      css-class="dialog-container"
      ref="dialogModel"
      width="70%"
      :header="dialogTitle"
      :buttons="buttons"
      @close="handleClose"
    >
      <div class="form-container">
        <mt-form ref="formRef" :model="formModel" :rules="formRules">
          <mt-row :gutter="24">
            <mt-col :span="8">
              <!-- 物料编码 -->
              <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
                <mt-select
                  style="flex: 1"
                  v-model="formModel.itemCode"
                  :data-source="itemCodeOptions"
                  :allow-filtering="true"
                  filter-type="Contains"
                  :filtering="getItemCodeList"
                  :fields="{ text: 'theCodeName', value: 'itemCode' }"
                  :placeholder="$t('')"
                  :show-clear-button="true"
                  @change="itemCodeChange"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="8">
              <!-- 物料名称 -->
              <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
                <mt-input style="flex: 1" v-model="formModel.itemName" :show-clear-button="true" />
              </mt-form-item>
            </mt-col>
            <mt-col :span="8">
              <!-- 工厂 -->
              <mt-form-item prop="siteCode" :label="$t('工厂')" label-style="top">
                <mt-select
                  style="flex: 1"
                  v-model="formModel.siteCode"
                  :data-source="siteCodeListOptions"
                  :allow-filtering="true"
                  filter-type="Contains"
                  :filtering="getSiteCodeList"
                  :fields="{ text: 'theCodeName', value: 'siteCode' }"
                  :placeholder="$t('')"
                  :show-clear-button="true"
                  @change="siteCodeChange"
                />
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row :gutter="24">
            <mt-col :span="8">
              <!-- 供应商 -->
              <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
                <mt-select
                  style="flex: 1"
                  v-model="formModel.supplierCode"
                  :data-source="supplierTypeList"
                  :allow-filtering="true"
                  filter-type="Contains"
                  :filtering="getSupplierList"
                  :fields="{ text: 'theCodeName', value: 'supplierCode' }"
                  :placeholder="$t('')"
                  :show-clear-button="true"
                  @change="supplierChange"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="8">
              <!-- 供应商简称 -->
              <mt-form-item prop="supplierAbbr" :label="$t('供应商简称')" label-style="top">
                <mt-input
                  style="flex: 1"
                  v-model="formModel.supplierAbbr"
                  :show-clear-button="true"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="8">
              <!-- 箱体 -->
              <mt-form-item prop="boxBody" :label="$t('箱体')" label-style="top">
                <mt-input style="flex: 1" v-model="formModel.boxBody" :show-clear-button="true" />
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row :gutter="24">
            <mt-col :span="8">
              <!-- 部件 -->
              <mt-form-item prop="parts" :label="$t('部件')" label-style="top">
                <mt-input style="flex: 1" v-model="formModel.parts" :show-clear-button="true" />
              </mt-form-item>
            </mt-col>
            <mt-col :span="8">
              <!-- 日产能收集维度 -->
              <mt-form-item prop="dayCapacityLevel" :label="$t('日产能收集维度')" label-style="top">
                <mt-select
                  style="flex: 1"
                  v-model="formModel.dayCapacityLevel"
                  :data-source="dayCapacityLevelList"
                  :fields="{ text: 'text', value: 'value' }"
                  :placeholder="$t('')"
                  :show-clear-button="true"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="8">
              <!-- 提速前周期 -->
              <mt-form-item prop="preCycle" :label="$t('提速前周期(s)')" label-style="top">
                <mt-input-number :step="1" v-model="formModel.preCycle" :show-clear-button="true" />
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row :gutter="24">
            <mt-col :span="8">
              <!-- 提速后周期 -->
              <mt-form-item prop="cycle" :label="$t('提速后周期(s)')" label-style="top">
                <mt-input-number :step="1" v-model="formModel.cycle" :show-clear-button="true" />
              </mt-form-item>
            </mt-col>
            <mt-col :span="8">
              <!-- 模号 -->
              <mt-form-item prop="moldNo" :label="$t('模号')" label-style="top">
                <mt-input style="flex: 1" v-model="formModel.moldNo" :show-clear-button="true" />
              </mt-form-item>
            </mt-col>
            <mt-col :span="8">
              <!-- 是否封模生产 -->
              <mt-form-item prop="closeMold" :label="$t('是否封模生产')" label-style="top">
                <mt-select
                  style="flex: 1"
                  v-model="formModel.closeMold"
                  :data-source="closeMoldOptions"
                  :fields="{ text: 'text', value: 'value' }"
                  :placeholder="$t('')"
                  :show-clear-button="true"
                />
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row :gutter="24">
            <mt-col :span="8">
              <!-- 采购组 -->
              <mt-form-item prop="purchaseGroupCode" :label="$t('采购组')" label-style="top">
                <mt-select
                  style="flex: 1"
                  :disabled="true"
                  v-model="formModel.purchaseGroupCode"
                  :data-source="buyerOrgCodeOptions"
                  :allow-filtering="true"
                  filter-type="Contains"
                  :filtering="getBuyerOrgCodeList"
                  :fields="{ text: 'theCodeName', value: 'groupCode' }"
                  :placeholder="$t('')"
                  :show-clear-button="true"
                  @change="buyerOrgChange"
                />
              </mt-form-item>
            </mt-col>
            <mt-col>
              <!-- 备注 -->
              <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
                <mt-input style="flex: 1" v-model="formModel.remark" :show-clear-button="true" />
              </mt-form-item>
            </mt-col>
          </mt-row>
        </mt-form>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
import { addCodeNameKeyInList } from '@/utils/utils'
import { utils } from '@mtech-common/utils'
import { closeMoldOptions, dayCapacityLevelList } from '../config/config.js'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      closeMoldOptions,
      dayCapacityLevelList,
      itemCodeOptions: [],
      siteCodeListOptions: [],
      buyerOrgCodeOptions: [],
      supplierTypeList: [],
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.handleClick,
          buttonModel: { isPrimary: true, content: this.$t('提交') }
        }
      ],
      formModel: {
        id: '',
        siteCode: '',
        siteName: '',
        itemCode: '',
        itemName: '',
        purchaseGroupCode: '',
        purchaseGroupName: '',
        supplierCode: '',
        supplierName: '',
        supplierAbbr: '',
        boxBody: '',
        parts: '',
        moldNo: '',
        dayCapacityLevel: '',
        closeMold: ''
      },
      formRules: {
        siteCode: [
          {
            required: true,
            trigger: ['blur', 'change'],
            message: this.$t('请选择工厂')
          }
        ],
        itemCode: [
          {
            required: true,
            trigger: ['blur', 'change'],
            message: this.$t('请选择物料')
          }
        ],
        supplierCode: [
          {
            required: true,
            trigger: ['blur', 'change'],
            message: this.$t('请选择供应商')
          }
        ],
        parts: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请输入部件')
          }
        ],
        dayCapacityLevel: [
          {
            required: true,
            trigger: ['blur', 'change'],
            message: this.$t('请选择日产能收集维度')
          }
        ],
        closeMold: [
          {
            required: true,
            trigger: ['blur', 'change'],
            message: this.$t('请选择是否封模生产')
          }
        ],
        moldNo: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请输入模号')
          }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.modalData.title
    }
  },
  mounted() {
    if (this.modalData.isEdit) {
      const _row = this.modalData.row
      for (let key in this.formModel) {
        if (_row[key]) {
          // this.formModel[key] = _row[key]
          this.$set(this.formModel, key, _row[key])
        }
      }
      this.$set(this.formModel, 'id', _row['id'])
      this.formModel.preCycle = Number(_row['preCycle'])
      this.formModel.cycle = Number(_row['cycle'])
    }
    // 获取工厂，物料，供应商信息
    this.getSiteCodeList = utils.debounce(this.getSiteCodeList, 1000)
    this.getItemCodeList = utils.debounce(this.getItemCodeList, 1000)
    this.getBuyerOrgCodeList = utils.debounce(this.getBuyerOrgCodeList, 1000)
    this.getSupplierList = utils.debounce(this.getSupplierList, 1000)
    this.getProcessorCodeList = utils.debounce(this.getProcessorCodeList, 1000)
    this.getSiteCodeList()
    this.getItemCodeList()
    this.getBuyerOrgCodeList()
    this.getSupplierList()

    this.$refs.dialogModel.ejsRef.show()
  },
  methods: {
    // 获取供应商信息
    getSupplierList(args = { text: '' }) {
      const { text } = args
      const param = {
        fuzzyNameOrCode: text || this.formModel.supplierCode || ''
      }
      this.$API.masterData.getSupplier(param).then((res) => {
        this.supplierTypeList.length = 0
        const list = res.data || []
        this.supplierTypeList = addCodeNameKeyInList({
          firstKey: 'supplierCode',
          secondKey: 'supplierName',
          list
        }).filter((n) => !!n.theCodeName && !!n.supplierCode)
      })
    },
    // 采购组下拉
    getBuyerOrgCodeList(args = { text: '' }) {
      const { text } = args
      const param = {
        fuzzyParam: text || this.formModel.purchaseGroupName || '',
        page: {
          current: 1,
          size: 100
        }
      }
      this.$API.masterData
        .getbussinessGroup(param)
        .then((res) => {
          if (res && res.code === 200) {
            let list = res.data || []
            this.buyerOrgCodeOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            }).filter((n) => !!n.theCodeName)
          }
        })
        .catch(() => {})
    },
    handleClick() {
      const param = this.formModel
      if (param.purchaseGroupCode) {
        delete param.purchaseGroupCode
        delete param.purchaseGroupName
      }
      if (param.supplierName) {
        delete param.supplierName
      }
      if (param.siteName) {
        delete param.siteName
      }
      if (param.itemName) {
        delete param.itemName
      }
      if (!this.modalData.isEdit) {
        delete param.id
      }
      this.$refs.formRef.validate((valid) => {
        let _flag = this.modalData.isEdit
          ? 'editCapacityManagementInfo'
          : 'addCapacityManagementInfo'
        if (valid) {
          this.$store.commit('startLoading')
          this.$API.capacityManagement[_flag](param)
            .then((res) => {
              if (res && res.code === 200) {
                this.$toast({
                  type: 'success',
                  content: res.msg || this.$t('操作成功')
                })
                this.handleClose()
                this.$bus.$emit('moldConfigChange')
              } else {
                this.$toast({
                  type: 'error',
                  content: res.msg || this.$t('操作失败')
                })
              }
            })
            .catch((err) => {
              this.$toast({
                type: 'error',
                content: err.msg || this.$t('操作失败')
              })
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },
    handleClose() {
      this.$refs.dialogModel.ejsRef.hide()
      this.$emit('close')
    },
    // 物料变更
    itemCodeChange(e) {
      if (e.itemData) {
        this.formModel.itemCode = e.itemData.itemCode
        this.formModel.itemName = e.itemData.itemName
      } else {
        this.formModel.itemCode = null
        this.formModel.itemName = null
      }
    },
    // 采购组变更
    buyerOrgChange(e) {
      if (e.itemData) {
        this.formModel.purchaseGroupCode = e.itemData.groupCode
        this.formModel.purchaseGroupName = e.itemData.groupName
      } else {
        this.formModel.purchaseGroupCode = null
        this.formModel.purchaseGroupName = null
      }
    },
    // 工厂变更
    siteCodeChange(e) {
      if (e.itemData) {
        this.formModel.siteCode = e.itemData.siteCode
        this.formModel.siteName = e.itemData.siteName
      } else {
        this.formModel.siteCode = null
        this.formModel.siteName = null
      }
    },
    // 供应商变更
    supplierChange(e) {
      if (e.itemData) {
        this.formModel.supplierCode = e.itemData.supplierCode
        this.formModel.supplierName = e.itemData.supplierName
      } else {
        this.formModel.supplierCode = null
        this.formModel.supplierName = null
      }
    },
    // 获取工厂信息
    getSiteCodeList(args = { text: '' }) {
      const { text } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text || this.formModel.siteCode || ''
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteCodeListOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
          }
        })
        .catch(() => {})
    },
    getItemCodeList(e = { text: '' }) {
      const { text } = e
      //物料下拉
      let params = {
        keyword: text || this.formModel.itemCode || '',
        pageSize: 50
      }
      this.$API.masterData.getItemByKeyword(params).then((res) => {
        const list = res.data?.records || []
        this.itemCodeOptions = addCodeNameKeyInList({
          firstKey: 'itemCode',
          secondKey: 'itemName',
          list
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .e-dlg-container .dialog-container .form-container {
  padding-top: 2rem;
}
</style>
