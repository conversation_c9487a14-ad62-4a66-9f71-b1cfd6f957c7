import { i18n } from '@/main.js'
export const columnData = [
  {
    type: 'checkbox',
    width: 60,
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 70,
    align: 'center'
  },
  {
    title: i18n.t('物料编码'),
    field: 'itemCode',
    width: 160,
    align: 'center',
    slots: {
      default: 'itemCodeBtn'
    }
  },
  {
    title: i18n.t('启用状态'),
    field: 'status',
    width: 100,
    align: 'center',
    slots: {
      default: 'statusBtn'
    }
  },
  {
    title: i18n.t('物料名称'),
    field: 'itemName',
    width: 200,
    align: 'center'
  },
  {
    title: i18n.t('采购组编码'),
    field: 'purchaseGroupCode',
    width: 150,
    align: 'center'
  },
  {
    title: i18n.t('采购组名称'),
    field: 'purchaseGroupName',
    width: 200,
    align: 'center'
  },
  {
    title: i18n.t('工厂编码'),
    field: 'siteCode',
    width: 150,
    align: 'center'
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName',
    width: 250,
    align: 'center'
  },
  {
    title: i18n.t('供应商编码'),
    field: 'supplierCode',
    width: 150,
    align: 'center'
  },
  {
    title: i18n.t('供应商名称'),
    field: 'supplierName',
    width: 250,
    align: 'center'
  },
  {
    title: i18n.t('供应商简称'),
    field: 'supplierAbbr',
    width: 200,
    align: 'center'
  },
  {
    title: i18n.t('箱体'),
    field: 'boxBody',
    width: 150,
    align: 'center'
  },
  {
    title: i18n.t('部件'),
    field: 'parts',
    width: 150,
    align: 'center'
  },
  {
    title: i18n.t('模号'),
    field: 'moldNo',
    width: 100,
    align: 'center'
  },
  {
    title: i18n.t('日产能收集维度'),
    field: 'dayCapacityLevel',
    width: 200,
    align: 'center'
  },
  {
    title: i18n.t('是否封模生产'),
    field: 'closeMold',
    width: 180,
    align: 'center'
  },
  {
    title: i18n.t('提速前周期(s)'),
    field: 'preCycle',
    width: 160,
    align: 'center'
  },
  {
    title: i18n.t('提速后周期(s)'),
    field: 'cycle',
    width: 160,
    align: 'center'
  },
  {
    title: i18n.t('创建人'),
    field: 'createUserName',
    width: 120,
    align: 'center'
  },
  {
    title: i18n.t('创建时间'),
    field: 'createTime',
    width: 140,
    align: 'center'
  },
  {
    title: i18n.t('最后修改人'),
    field: 'updateUserName',
    width: 150,
    align: 'center'
  },
  {
    title: i18n.t('最后修改时间'),
    field: 'updateTime',
    width: 160,
    align: 'center'
  },
  {
    title: i18n.t('备注'),
    field: 'remark',
    width: 160,
    align: 'center'
  }
]

export const toolbar = [
  {
    code: 'add',
    name: i18n.t('新增'),
    status: 'info'
  },
  {
    code: 'delete',
    name: i18n.t('删除'),
    status: 'info'
  },
  {
    code: 'enable',
    name: i18n.t('启用'),
    status: 'info'
  },
  {
    code: 'deactivate',
    name: i18n.t('停用'),
    status: 'info'
  },
  {
    code: 'import',
    name: i18n.t('导入'),
    status: 'info'
  },
  {
    code: 'export',
    name: i18n.t('导出'),
    status: 'info'
  },
  {
    code: 'refresh',
    name: i18n.t('刷新产能收集'),
    status: 'info'
  }
]

export const statusList = [
  {
    text: i18n.t('启用'),
    value: 1
  },
  {
    text: i18n.t('停用'),
    value: 0
  }
]

// 日产能收集维度
export const dayCapacityLevelList = [
  {
    text: i18n.t('按物料'),
    value: i18n.t('按物料')
  },
  {
    text: i18n.t('按箱体'),
    value: i18n.t('按箱体')
  }
]

// 是否封模生产
export const closeMoldOptions = [
  {
    text: i18n.t('是'),
    value: i18n.t('是')
  },
  {
    text: i18n.t('否'),
    value: i18n.t('否')
  }
]
