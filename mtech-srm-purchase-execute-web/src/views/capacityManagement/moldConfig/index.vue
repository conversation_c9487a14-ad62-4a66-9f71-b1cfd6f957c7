<template>
  <!-- 产能模具配置 - 采方 -->
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      @reset="handleCustomReset"
      @search="handleCustomSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <!-- 启用状态 -->
        <mt-form-item prop="status" :label="$t('启用状态')" label-style="top">
          <mt-select
            style="flex: 1"
            v-model="searchFormModel.status"
            :data-source="statusList"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 工厂编码 -->
        <mt-form-item prop="siteCodeList" :label="$t('工厂编码')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.siteCodeList"
            :data-source="siteCodeListOptions"
            :allow-filtering="true"
            :multiple="true"
            filter-type="Contains"
            :filtering="getSiteCodeList"
            :fields="{ text: 'theCodeName', value: 'siteCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 工厂名称 -->
        <mt-form-item prop="siteName" :label="$t('工厂名称')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.siteName" :show-clear-button="true" />
        </mt-form-item>
        <!-- 物料编码 -->
        <mt-form-item prop="itemCodeList" :label="$t('物料编码')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.itemCodeList"
            :data-source="itemCodeOptions"
            :allow-filtering="true"
            filter-type="Contains"
            :multiple="true"
            :filtering="getItemCodeList"
            :fields="{ text: 'theCodeName', value: 'itemCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 物料名称 -->
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.itemName" :show-clear-button="true" />
        </mt-form-item>
        <!-- 采购组 -->
        <mt-form-item prop="buyerOrgCode" :label="$t('采购组编码')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.buyerOrgCode"
            :data-source="buyerOrgCodeOptions"
            :allow-filtering="true"
            filter-type="Contains"
            :multiple="true"
            :filtering="getBuyerOrgCodeList"
            :fields="{ text: 'theCodeName', value: 'groupCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 采购组名称 -->
        <mt-form-item prop="buyerOrgName" :label="$t('采购组名称')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.buyerOrgName"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 供应商编码 -->
        <mt-form-item prop="supplierCodeList" :label="$t('供应商编码')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.supplierCodeList"
            :data-source="supplierTypeList"
            :allow-filtering="true"
            filter-type="Contains"
            :multiple="true"
            :filtering="getSupplierList"
            :fields="{ text: 'theCodeName', value: 'supplierCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 供应商名称 -->
        <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.supplierName"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 箱体 -->
        <mt-form-item prop="boxBody" :label="$t('箱体')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.boxBody" :show-clear-button="true" />
        </mt-form-item>
        <!-- 部件 -->
        <mt-form-item prop="parts" :label="$t('部件')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.parts" :show-clear-button="true" />
        </mt-form-item>
        <!-- 模号 -->
        <mt-form-item prop="moldNo" :label="$t('模号')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.moldNo" :show-clear-button="true" />
        </mt-form-item>
        <!-- 日产能收集维度 -->
        <mt-form-item prop="dayCapacityLevel" :label="$t('日产能收集维度')" label-style="top">
          <mt-select
            style="flex: 1"
            v-model="searchFormModel.dayCapacityLevel"
            :data-source="dayCapacityLevelList"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 是否封模生产 -->
        <mt-form-item prop="closeMold" :label="$t('是否封模生产')" label-style="top">
          <mt-select
            style="flex: 1"
            v-model="searchFormModel.closeMold"
            :data-source="closeMoldOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 创建人 -->
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.createUserName"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 创建时间 -->
        <mt-form-item prop="" :label="$t('创建时间')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateChange(e, 'createTime')"
          />
        </mt-form-item>
        <!-- 最后修改人 -->
        <mt-form-item prop="boxBody" :label="$t('最后修改人')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.updateUserName"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 最后更新时间 -->
        <mt-form-item prop="" :label="$t('最后更新时间')">
          <mt-date-range-picker
            v-model="searchFormModel.updateTime"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateChange(e, 'updateTime')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        class="xTable-class"
        :row-config="{ height: 130 }"
        :columns="columns"
        :table-data="tableData"
        show-overflow
        :min-height="600"
        border="none"
        header-align="left"
        row-class-name="table-row-class"
        align="center"
        style="padding-top: unset"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #itemCodeBtn="{ row }">
          <div>{{ row.itemCode }}</div>
          <div class="itemBtn">
            <span>
              <vxe-button @click="handleEdit(row)" icon="vxe-icon-edit">{{
                $t('编辑')
              }}</vxe-button>
            </span>
            <span>
              <vxe-button @click="handleDelete(row)" icon="vxe-icon-delete">{{
                $t('删除')
              }}</vxe-button>
            </span>
          </div>
        </template>
        <template #statusBtn="{ row }">
          <div>{{ [1, '1'].includes(row.status) ? $t('启用') : $t('停用') }}</div>
          <div class="itemBtn">
            <span v-if="row.status === 0">
              <vxe-button @click="handleStart(row)" icon="vxe-icon-caret-right">{{
                $t('启用')
              }}</vxe-button>
            </span>
            <span v-else>
              <vxe-button @click="handleStop(row)" icon="vxe-icon-close">{{
                $t('停用')
              }}</vxe-button>
            </span>
          </div>
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { addCodeNameKeyInList } from '@/utils/utils'
import { utils } from '@mtech-common/utils'
import dayjs from 'dayjs'
import {
  columnData,
  toolbar,
  dayCapacityLevelList,
  statusList,
  closeMoldOptions
} from './config/config'
import { getHeadersFileName, download, timeNumberToDate } from '@/utils/utils'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      toolbar: toolbar,
      statusList,
      dayCapacityLevelList,
      closeMoldOptions,
      siteCodeListOptions: [], // 工厂下拉
      itemCodeOptions: [], // 物料下拉
      buyerOrgCodeOptions: [], // 采购组下拉
      supplierTypeList: [], // 供应商下拉
      processorCodeList: [], // 加工商下拉
      apiWaitingQuantity: 0, // 调用的api正在等待数
      searchFormModel: {
        status: '',
        siteCodeList: [],
        siteName: '',
        itemCodeList: [],
        itemName: '',
        buyerOrgCodeList: [],
        buyerOrgName: '',
        supplierCodeList: [],
        supplierName: '',
        boxBody: '',
        parts: '',
        moldNo: '',
        dayCapacityLevel: '',
        closeMold: '',
        createUserName: '',
        createTimeStart: '',
        createTimeEnd: '',
        updateUserName: '',
        updateTimeStart: '',
        updateTimeEnd: ''
      },
      forecastPageCurrent: 1,
      syncVersion: '',
      titleList: [],
      tableData: [],
      columns: columnData,
      plannerListOptions: [], // 计划员 下列选项
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      isEditing: false, // 正在编辑数据
      isEdit: false
    }
  },
  mounted() {
    // 列表数据实时更新
    this.$bus.$on('moldConfigChange', () => {
      this.handleCustomSearch()
    })
    // 获取工厂，物料，供应商信息
    this.getSiteCodeList = utils.debounce(this.getSiteCodeList, 1000)
    this.getItemCodeList = utils.debounce(this.getItemCodeList, 1000)
    this.getBuyerOrgCodeList = utils.debounce(this.getBuyerOrgCodeList, 1000)
    this.getSupplierList = utils.debounce(this.getSupplierList, 1000)
    this.getProcessorCodeList = utils.debounce(this.getProcessorCodeList, 1000)
    this.getSiteCodeList()
    this.getItemCodeList()
    this.getBuyerOrgCodeList()
    this.getSupplierList()
    this.getProcessorCodeList()
  },
  filters: {
    transformValue(e) {
      if (e) {
        return timeNumberToDate({ formatString: 'Y-m-s', data: e })
      }
    }
  },
  methods: {
    // 启用
    handleStart(row) {
      let isList = false
      if (row instanceof Array) {
        isList = true
      }
      if (isList) {
        for (let i = 0; i < row.length; i++) {
          if (['1', 1].includes(row[i].status)) {
            this.$toast({
              type: 'warning',
              content: this.$t('请选择状态为停用的数据操作！')
            })
            return
          }
        }
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: isList ? this.$t('确认启用选中的数据吗?') : this.$t('确认启用该当前行数据吗?')
        },
        success: () => {
          this.$store.commit('startLoading')
          const ids = isList ? row.map((item) => item.id) : [row.id]
          this.$API.capacityManagement
            .enableCapacityManagementInfo({ ids: ids.join(), status: 1 })
            .then((res) => {
              if (res && res.code === 200) {
                this.$toast({
                  type: 'success',
                  content: res.msg || this.$t('启用成功')
                })
                this.handleCustomSearch()
              } else {
                this.$toast({
                  type: 'error',
                  content: res.msg || this.$t('启用失败')
                })
              }
            })
            .catch((err) => {
              this.$toast({
                type: 'error',
                content: err.msg || this.$t('启用失败')
              })
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        },
        cancel: () => {}
      })
    },
    // 停用
    handleStop(row) {
      let isList = false
      if (row instanceof Array) {
        isList = true
      }
      if (isList) {
        for (let i = 0; i < row.length; i++) {
          if (['0', 0].includes(row[i].status)) {
            this.$toast({
              type: 'warning',
              content: this.$t('请选择状态为启用的数据操作！')
            })
            return
          }
        }
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: isList ? this.$t('确认停用选中的数据吗?') : this.$t('确认停用当前行数据吗?')
        },
        success: () => {
          this.$store.commit('startLoading')
          const ids = isList ? row.map((item) => item.id) : [row.id]
          this.$API.capacityManagement
            .deactivateCapacityManagementInfo({ ids: ids.join(), status: 0 })
            .then((res) => {
              if (res && res.code === 200) {
                this.$toast({
                  type: 'success',
                  content: res.msg || this.$t('停用成功')
                })
                this.handleCustomSearch()
              } else {
                this.$toast({
                  type: 'error',
                  content: res.msg || this.$t('停用失败')
                })
              }
            })
            .catch((err) => {
              this.$toast({
                type: 'error',
                content: err.msg || this.$t('停用失败')
              })
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        },
        cancel: () => {}
      })
    },
    // 删除
    handleDelete(row) {
      let isList = false
      if (row instanceof Array) {
        isList = true
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: isList ? this.$t('确认删除选中的数据吗?') : this.$t('确认删除当前行数据吗?')
        },
        success: () => {
          this.$store.commit('startLoading')
          const ids = isList ? row.map((item) => item.id) : [row.id]
          this.$API.capacityManagement
            .deleteCapacityManagementInfo(ids)
            .then((res) => {
              if (res && res.code === 200) {
                this.$toast({
                  type: 'success',
                  content: res.msg || this.$t('删除成功')
                })
                this.handleCustomSearch()
              } else {
                this.$toast({
                  type: 'error',
                  content: res.msg || this.$t('删除失败')
                })
              }
            })
            .catch((err) => {
              this.$toast({
                type: 'error',
                content: err.msg || this.$t('删除失败')
              })
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        },
        cancel: () => {}
      })
    },
    // 编辑
    handleEdit(row) {
      this.$dialog({
        modal: () => import('@/views/capacityManagement/moldConfig/components/addOrEdit.vue'),
        data: {
          title: this.$t('编辑'),
          row: row,
          isEdit: true
        },
        success: () => {
          this.handleCustomSearch()
        }
      })
    },
    // 获取工厂信息
    getSiteCodeList(args = { text: '' }) {
      const { text } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteCodeListOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
          }
        })
        .catch(() => {})
    },
    // // 获取物料信息
    // getItemCodeList(args = { text: '' }) {
    //   let obj = {
    //     fuzzyNameOrCode: args.text
    //   }

    //   this.$API.masterData.getCustomer(obj).then((res) => {
    //     res.data.forEach((item) => {
    //       item.name = item.customerCode + item.customerName
    //       item.code = item.customerEnterpriseId
    //     })e
    //     this.itemCodeOptions = res.data
    //   })
    // },
    getItemCodeList(e = { text: '' }) {
      const { text } = e
      //物料下拉
      let params = {
        keyword: text || '',
        pageSize: 50
      }
      this.$API.masterData.getItemByKeyword(params).then((res) => {
        const list = res.data?.records || []
        this.itemCodeOptions = addCodeNameKeyInList({
          firstKey: 'itemCode',
          secondKey: 'itemName',
          list
        })
      })
    },
    // 采购组下拉
    getBuyerOrgCodeList(args = { text: '' }) {
      const { text } = args
      const param = {
        fuzzyParam: text,
        page: {
          current: 1,
          size: 100
        }
      }
      this.$API.masterData
        .getbussinessGroup(param)
        .then((res) => {
          if (res && res.code === 200) {
            let list = res.data || []
            this.buyerOrgCodeOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            }).filter((n) => !!n.theCodeName)
          }
        })
        .catch(() => {})
    },
    // 获取供应商信息
    getSupplierList(args = { text: '' }) {
      const { text } = args
      const param = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData.getSupplier(param).then((res) => {
        this.supplierTypeList.length = 0
        const list = res.data || []
        this.supplierTypeList = addCodeNameKeyInList({
          firstKey: 'supplierCode',
          secondKey: 'supplierName',
          list
        }).filter((n) => !!n.theCodeName && !!n.supplierCode)
      })
    },
    // 加工商下拉
    getProcessorCodeList(args = { text: '' }) {
      const { text } = args
      const param = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData.getSupplier(param).then((res) => {
        this.processorCodeList.length = 0
        const list = res.data || []
        this.processorCodeList = addCodeNameKeyInList({
          firstKey: 'supplierCode',
          secondKey: 'supplierName',
          list
        }).filter((n) => !!n.theCodeName && !!n.supplierCode)
      })
    },
    // 日期更改
    dateChange(e, flag) {
      if (e && e.startDate) {
        this.searchFormModel[flag + 'Start'] = dayjs(e.startDate).valueOf()
        this.searchFormModel[flag + 'End'] = dayjs(e.endDate).valueOf()
      } else {
        this.searchFormModel[flag + 'Start'] = null
        this.searchFormModel[flag + 'End'] = null
      }
    },

    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      if (code === 'CloseEdit') {
        // 清除编辑状态
        this.$refs.xTable.$refs.xGrid.clearEdit()
        this.handleCustomSearch()
        return
      } else if (code === 'add') {
        this.$dialog({
          modal: () => import('@/views/capacityManagement/moldConfig/components/addOrEdit.vue'),
          data: {
            title: this.$t('新增'),
            isEdit: false
          },
          success: () => {
            this.handleCustomSearch()
          }
        })
        return
      }
      if (this.isEdit) {
        return
      }
      if (code === 'export') {
        this.handleClickExport()
        return
      }
      // 导入
      if (code === 'import') {
        this.handleImport()
        return
      }
      if (code === 'refresh') {
        this.handleRefresh()
        return
      }
      if (selectedRecords.length == 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })
      // 启用
      if (code === 'enable') {
        this.handleStart(selectedRecords)
        return
      }
      // 停用
      if (code === 'deactivate') {
        this.handleStop(selectedRecords)
        return
      }
      // 删除
      if (code === 'delete') {
        this.handleDelete(selectedRecords)
        return
      }
    },
    handleRefresh() {
      this.$dialog({
        data: {
          title: this.$t('刷新产能收集'),
          message: this.$t('后台调用任务，将用户当天新增的配置生成收集表')
        },
        success: () => {
          this.$API.capacityManagement.refreshCapacityManagementApi().then((res) => {
            if (res.code === 200) {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
            } else {
              this.$toast({
                type: 'error',
                content: res.msg
              })
            }
          })
        }
      })
    },
    handleImport() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "@/components/uploadDialog/index.vue" */ '@/components/uploadDialog/index.vue'
          ),
        data: {
          title: this.$t('导入'),
          paramsKey: 'excel',
          importApi: this.$API.capacityManagement.importCapacityManagementList,
          downloadTemplateApi: this.$API.capacityManagement.downloadImportTemplate
        },
        success: () => {
          this.handleCustomSearch()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    handleClickExport() {
      const param = {
        ...this.searchFormModel,
        page: {
          current: 1,
          size: 5000
        }
      }
      if (param.requireDate) {
        delete param.requireDate
      }
      this.$store.commit('startLoading')
      this.$API.capacityManagement.exportCapacityManagementList(param).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 保存操作
    handleSaveInfo(row) {
      this.$API.CategoryThresholdConfiguration.editCategoryThresholdConfigurationInfo(row)
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t(res.msg || '修改成功')
            })
            this.handleCustomSearch()
          }
          this.$t({
            type: 'error',
            content: this.$t(res.msg || '修改失败')
          })
        })
        .catch((e) => {
          this.$toast({
            type: 'error',
            content: this.$t(e.msg)
          })
        })
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'bigVersionNo') {
            this.searchFormModel[key] = this.versionList[0]
          }
        }
      }
      this.handleCustomSearch()
    },
    // 采方-获取信息列表
    handleCustomSearch() {
      this.isEdit = false
      const params = {
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        },
        ...this.searchFormModel
      }
      if (params.createTime) {
        delete params.createTime
      }
      if (params.updateTime) {
        delete params.updateTime
      }
      this.apiStartLoading()
      this.$API.capacityManagement
        .getcapacityManagementList(params)
        .then((res) => {
          if (res?.code == 200) {
            const total = res?.data?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.totalRecordsCount = Number(total)
            const records = res?.data?.records || [] // 表格数据
            // 处理表数据
            this.tableData = records
          }
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  & .span-style {
    display: flex;
    flex-flow: column;
    & span:nth-child(1) {
      border-bottom: 1px solid #cfd4d9;
    }
  }
}
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
::v-deep .xTable-class .vxe-table .table-row-class {
  & td {
    height: 40px !important;
    & .vxe-cell .itemBtn {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      & .vxe-button {
        border: 0;
        color: blue;
      }
    }
  }
}
</style>
