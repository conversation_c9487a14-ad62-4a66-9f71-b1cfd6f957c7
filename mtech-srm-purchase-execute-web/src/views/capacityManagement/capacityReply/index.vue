<!-- 供方 - 模具产能回复 -->
<template>
  <div class="full-height vertical-flex-box">
    <collapse-search :is-grid-display="true" @reset="handleCustomReset" @search="handleSearch">
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="versionNo" :label="$t('版本号')">
          <mt-input
            v-model="searchFormModel.versionNo"
            :placeholder="$t('请输入')"
            show-clear-button
          />
        </mt-form-item>
        <mt-form-item prop="archiveStatus" :label="$t('归档状态')">
          <mt-select
            v-model="searchFormModel.archiveStatus"
            :data-source="statusOptions"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <div class="flex-fit">
      <ScTable
        ref="xTable"
        :columns="columns"
        :table-data="tableData"
        height="auto"
        header-align="center"
        align="center"
        show-overflow
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        style="padding-top: unset"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #versionNoDefault="{ row }">
          <span class="row-clickable" @click="handleLook(row)">{{ row.versionNo }}</span>
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, toolbar, statusOptions } from './config/config'
export default {
  components: {
    CollapseSearch,
    ScTable
  },
  data() {
    return {
      searchFormModel: {
        versionNo: '',
        archiveStatus: ''
      },
      columns: columnData,
      tableData: [],
      toolbar,
      pageSettings: {
        currentPage: 1,
        pageSize: 50, // 当前每页数据量
        pageCount: 5,
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },

      statusOptions
    }
  },
  created() {
    this.handleSearch()
  },
  methods: {
    handleLook(row) {
      this.handleUpdateStatus(row)
      this.$router.push({
        name: 'capacity-reply-detail',
        query: {
          versionNo: row.versionNo
        }
      })
    },
    handleUpdateStatus(row) {
      const userinfo = JSON.parse(sessionStorage.getItem('userInfo'))
      let params = {
        supplierCode: userinfo.accountName,
        versionNo: row.versionNo
      }
      this.$API.capacityManagement.updateStatusCapacityCollectionListApi(params).then((res) => {
        if (res.code !== 200) {
          this.$toast({ content: this.$t('刷新状态失败'), type: 'error' })
        }
      })
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      const ids = []
      selectedRecords.forEach((item) => {
        ids.push(item.id)
      })
      const commonToolbar = ['File']
      if (code === 'CloseEdit') {
        this.$refs.xTable.$refs.xGrid.clearEdit()
        this.handleSearch()
        return
      }
      if (selectedRecords.length === 0 && commonToolbar.includes(code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (code === 'File') {
        if (selectedRecords.some((item) => item.archiveStatus !== '未归档')) {
          this.$toast({ content: this.$t('请选择未归档的数据！'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认归档选中的数据？')
          },
          success: () => {
            this.handleFile(ids)
          }
        })
      }
    },
    handleFile(ids) {
      let params = {
        ids: ids.join(','),
        archiveStatus: '已归档'
      }
      this.apiStartLoading()
      this.$API.capacityManagement
        .fileCapacityCollectionApi(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleCustomSearch()
    },
    handleSearch() {
      this.pageSettings.currentPage = 1
      this.handleCustomSearch()
    },
    handleCustomSearch() {
      let params = {
        page: {
          pages: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          params[key] = params[key] || ''
        }
      }
      this.apiStartLoading()
      this.$API.capacityManagement
        .pageCapacityCollectionApi(params)
        .then((res) => {
          if (res?.code == 200) {
            const total = res.data.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            this.tableData = res.data?.records || []
          }
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    handleCurrentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.handleCustomSearch()
    },
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.row-clickable {
  color: blue;
  cursor: pointer;
}
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  & .span-style {
    display: flex;
    flex-flow: column;
    & span:nth-child(1) {
      border-bottom: 1px solid #cfd4d9;
    }
  }
}
</style>
