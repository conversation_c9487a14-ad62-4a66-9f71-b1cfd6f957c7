<!-- 箱体明细 -->
<template>
  <div class="full-height vertical-flex-box">
    <collapse-search :is-grid-display="true" @reset="handleCustomReset" @search="handleSearch">
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <!-- 工厂编码 -->
        <mt-form-item prop="siteCodeList" :label="$t('工厂编码')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.siteCodeList"
            :data-source="siteCodeListOptions"
            :allow-filtering="true"
            :multiple="true"
            filter-type="Contains"
            :filtering="getSiteCodeList"
            :fields="{ text: 'theCodeName', value: 'siteCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 反馈状态 -->
        <mt-form-item prop="viewStatus" :label="$t('反馈状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.viewStatus"
            :data-source="viewStatusOptions"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')">
          <mt-input
            v-model="searchFormModel.itemName"
            :placeholder="$t('请输入')"
            show-clear-button
          />
        </mt-form-item>
        <mt-form-item prop="boxBody" :label="$t('箱体')">
          <mt-input
            v-model="searchFormModel.boxBody"
            :placeholder="$t('请输入')"
            show-clear-button
          />
        </mt-form-item>
        <mt-form-item prop="parts" :label="$t('部件')">
          <mt-input v-model="searchFormModel.parts" :placeholder="$t('请输入')" show-clear-button />
        </mt-form-item>
        <mt-form-item prop="moldNo" :label="$t('模号')">
          <mt-input
            v-model="searchFormModel.moldNo"
            :placeholder="$t('请输入')"
            show-clear-button
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <div class="flex-fit">
      <ScTable
        ref="xTable"
        :columns="columns"
        :table-data="tableData"
        height="auto"
        header-align="left"
        align="left"
        show-overflow
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        style="padding-top: unset"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #mouldOutputQtyDefault="{ row }">
          <vxe-input
            v-model="row.mouldOutputQty"
            :placeholder="$t('请输入')"
            type="number"
            min="0"
            clearable
          />
        </template>
        <template #moldSendQtyDefault="{ row }">
          <vxe-input
            v-model="row.moldSendQty"
            :placeholder="$t('请输入')"
            type="number"
            min="0"
            clearable
          />
        </template>
        <template #completeInventoryQtyDefault="{ row }">
          <vxe-input
            v-model="row.completeInventoryQty"
            :placeholder="$t('请输入')"
            type="number"
            min="0"
            clearable
          />
        </template>
        <template #halfInventoryQtyDefault="{ row }">
          <vxe-input
            v-model="row.halfInventoryQty"
            :placeholder="$t('请输入')"
            type="number"
            min="0"
            clearable
          />
        </template>
        <template #innerLineChooseDefault="{ row }">
          <vxe-select
            v-model="row.innerLineChoose"
            :placeholder="$t('请选择')"
            :options="innerLineOptions"
            transfer
            clearable
            multiple
          />
        </template>
        <template #innerLineCallMaterialBatchNoDefault="{ row }">
          <vxe-input
            v-model="row.innerLineCallMaterialBatchNo"
            :placeholder="$t('请输入')"
            clearable
          />
        </template>
        <template #outerLineChooseDefault="{ row }">
          <vxe-select
            v-model="row.outerLineChoose"
            :placeholder="$t('请选择')"
            :options="outerLineOptions"
            transfer
            clearable
            multiple
          />
        </template>
        <template #outerLineCallMaterialBatchNoDefault="{ row }">
          <vxe-input
            v-model="row.outerLineCallMaterialBatchNo"
            :placeholder="$t('请输入')"
            clearable
          />
        </template>
        <template #remarkDefault="{ row }">
          <vxe-input v-model="row.remark" :placeholder="$t('请输入')" clearable />
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, toolbar } from '../config/detail'
import { getHeadersFileName, download, addCodeNameKeyInList } from '@/utils/utils'
import { utils } from '@mtech-common/utils'
export default {
  components: { CollapseSearch, ScTable },
  props: {
    version: {
      type: String,
      default: ''
    }
  },
  watch: {
    version: {
      handler(newVal) {
        if (newVal) {
          this.handleSearch()
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      viewStatusOptions: [
        { text: this.$t('已查看'), value: '已查看' },
        { text: this.$t('未查看'), value: '未查看' },
        { text: this.$t('已回复'), value: '已回复' }
      ],
      searchFormModel: {
        viewStatus: ''
      },
      columns: columnData,
      tableData: [],
      toolbar,
      siteCodeListOptions: [],
      itemCodeOptions: [],
      pageSettings: {
        currentPage: 1,
        pageSize: 50, // 当前每页数据量
        pageCount: 5,
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },

      innerLineOptions: [
        { label: '1', value: 1 },
        { label: '2', value: 2 },
        { label: '3', value: 3 },
        { label: '4', value: 4 }
      ],
      outerLineOptions: [
        { label: '1', value: 1 },
        { label: '2', value: 2 },
        { label: '3', value: 3 },
        { label: '4', value: 4 },
        { label: '5', value: 5 },
        { label: '6', value: 6 }
      ]
    }
  },
  mounted() {
    this.getSiteCodeList = utils.debounce(this.getSiteCodeList, 1000)
    this.getSiteCodeList()
  },
  methods: {
    getSiteCodeList(args = { text: '' }) {
      const { text } = args
      const params = {
        page: { current: 1, size: 20 },
        fuzzyParam: text
      }
      this.$API.deliverySchedule
        .getFactoryInfo(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteCodeListOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
          }
        })
        .catch(() => {})
    },
    getItemCodeList(args = { text: '' }) {
      const { text } = args
      //物料下拉
      let params = {
        keyword: text || '',
        pageSize: 50
      }
      this.$API.capacityManagement.getSupplierItemCode(params).then((res) => {
        const list = res.data?.records || []
        this.itemCodeOptions = addCodeNameKeyInList({
          firstKey: 'itemCode',
          secondKey: 'itemName',
          list
        })
      })
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      const commonToolbar = ['Save']
      if (code === 'CloseEdit') {
        this.$refs.xTable.$refs.xGrid.clearEdit()
        this.handleSearch()
        return
      }
      if (selectedRecords.length === 0 && commonToolbar.includes(code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (code === 'Save') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认保存选中的数据？')
          },
          success: () => {
            this.handleSave(selectedRecords)
          }
        })
      }
      if (code === 'Import') {
        this.handleImport()
      }
      if (code === 'Export') {
        this.handleExport()
      }
    },
    handleSave(selectedRecords, flag = true) {
      let params = [...selectedRecords]
      params.forEach((item) => {
        if (item.id?.includes('row_')) {
          item.id = null
        }
        item.innerLineChoose = item.innerLineChoose.join(',')
        item.outerLineChoose = item.outerLineChoose.join(',')
      })
      this.$API.capacityManagement.saveCapacityCollectionListApi(params).then((res) => {
        if (res.code == 200) {
          if (flag) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
          }
          this.handleSearch()
        }
      })
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.capacityManagement.importSupplierCapacityCollectionListApi,
          downloadTemplateApi: false,
          paramsKey: 'excel',
          queryParams: {
            dayCapacityLevel: '按箱体'
          },
          downloadTemplateParams: {
            templateType: 'SUPPLIER'
          }
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    handleExport() {
      const userinfo = JSON.parse(sessionStorage.getItem('userInfo'))
      let params = {
        page: {
          pages: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        },
        versionNo: this.version,
        dayCapacityLevel: '按箱体',
        supplierCodeList: [userinfo.accountName],
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.capacityManagement.exportCapacityCollectionListApi(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.pageSettings.currentPage = 1
      this.handleCustomSearch()
    },
    handleCustomSearch() {
      const userinfo = JSON.parse(sessionStorage.getItem('userInfo'))
      let params = {
        page: {
          pages: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        },
        versionNo: this.version,
        dayCapacityLevel: '按箱体',
        supplierCodeList: [userinfo.accountName],
        ...this.searchFormModel
      }
      this.$API.capacityManagement.pageCapacityCollectionListApi(params).then((res) => {
        if (res?.code == 200) {
          const total = res.data.total || 0
          this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
          this.pageSettings.totalRecordsCount = Number(total)
          this.tableData = res.data.records.map((item) => {
            item.innerLineChoose = item.innerLineChoose
              ? item.innerLineChoose.split(',').map((e) => Number(e))
              : []
            item.outerLineChoose = item.outerLineChoose
              ? item.outerLineChoose.split(',').map((e) => Number(e))
              : []
            return item
          })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.handleCustomSearch()
    },
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.handleCustomSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  & .span-style {
    display: flex;
    flex-flow: column;
    & span:nth-child(1) {
      border-bottom: 1px solid #cfd4d9;
    }
  }
}
</style>
