<!-- 供方 - 模具产能回收 - 明细 -->
<template>
  <div>
    <div style="display: flex; flex-direction: row-reverse">
      <mt-button css-class="e-flat" @click="goBack">{{ $t('返回') }}</mt-button>
    </div>
    <mt-tabs
      :e-tab="false"
      :data-source="tabList"
      :selected-item="tabIndex"
      @handleSelectTab="handleSelectTab"
    />
    <div>
      <List v-if="tabIndex === 0" :version="versionNo" />
      <Detail v-if="tabIndex === 1" :version="versionNo" />
    </div>
  </div>
</template>

<script>
export default {
  components: {
    List: () => import('./pages/List.vue'),
    Detail: () => import('./pages/Detail.vue')
  },
  data() {
    return {
      tabList: [{ title: this.$t('物料视图') }, { title: this.$t('箱体视图') }],
      tabIndex: 0,
      versionNo: ''
    }
  },
  watch: {
    '$route.query.versionNo': {
      handler(newVal) {
        this.tabIndex = 0
        this.versionNo = newVal
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    },
    goBack() {
      this.$router.push({
        name: 'capacity-reply'
      })
    }
  }
}
</script>
