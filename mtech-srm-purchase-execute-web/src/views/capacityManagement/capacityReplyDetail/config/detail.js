import { i18n } from '@/main.js'

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    ignore: true,
    align: 'center',
    fixed: 'left'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    align: 'center',
    fixed: 'left'
  },
  {
    title: i18n.t('反馈状态'),
    field: 'viewStatus',
    width: 120
  },
  {
    title: i18n.t('工厂'),
    field: 'siteName',
    width: 250,
    formatter: ({ cellValue, row }) => {
      return row.siteCode + '-' + cellValue
    }
  },
  {
    title: i18n.t('供应商'),
    field: 'supplierName',
    width: 200,
    formatter: ({ cellValue, row }) => {
      return row.supplierCode + '-' + cellValue
    }
  },
  {
    title: i18n.t('箱体'),
    field: 'boxBody',
    width: 100
  },
  {
    title: i18n.t('部件'),
    field: 'parts',
    width: 100
  },
  {
    title: i18n.t('模号'),
    field: 'moldNo',
    width: 100
  },
  {
    title: i18n.t('历史库存结余数'),
    field: 'historyInvQty',
    width: 160
  },
  {
    title: i18n.t('产能日期'),
    field: 'capacityTime',
    width: 150
  },
  {
    title: i18n.t('模具产出数'),
    field: 'mouldOutputQty',
    width: 150,
    slots: {
      default: 'mouldOutputQtyDefault'
    }
  },
  {
    title: i18n.t('模具送货数'),
    field: 'moldSendQty',
    width: 150,
    slots: {
      default: 'moldSendQtyDefault'
    }
  },
  {
    title: i18n.t('成品库存结余数'),
    field: 'completeInventoryQty',
    width: 160,
    slots: {
      default: 'completeInventoryQtyDefault'
    }
  },
  {
    title: i18n.t('半成品库存结余数'),
    field: 'halfInventoryQty',
    width: 160,
    slots: {
      default: 'halfInventoryQtyDefault'
    }
  },
  {
    title: i18n.t('内线选择'),
    field: 'innerLineChoose',
    width: 160,
    slots: {
      default: 'innerLineChooseDefault'
    }
  },
  {
    title: i18n.t('内线叫料扣减批次号'),
    field: 'innerLineCallMaterialBatchNo',
    width: 160,
    slots: {
      default: 'innerLineCallMaterialBatchNoDefault'
    }
  },
  {
    title: i18n.t('外线选择'),
    field: 'outerLineChoose',
    width: 160,
    slots: {
      default: 'outerLineChooseDefault'
    }
  },
  {
    title: i18n.t('外线叫料扣减批次号'),
    field: 'outerLineCallMaterialBatchNo',
    width: 160,
    slots: {
      default: 'outerLineCallMaterialBatchNoDefault'
    }
  },
  {
    title: i18n.t('供方备注'),
    field: 'remark',
    width: 160,
    slots: {
      default: 'remarkDefault'
    }
  },
  {
    title: i18n.t('创建人'),
    field: 'createUserName',
    width: 100
  },
  {
    title: i18n.t('创建时间'),
    field: 'createTime',
    width: 150
  },
  {
    title: i18n.t('最后修改人'),
    field: 'updateUserName',
    width: 100
  },
  {
    title: i18n.t('最后修改时间'),
    field: 'updateTime',
    width: 150
  }
]

export const toolbar = [
  {
    code: 'Save',
    name: i18n.t('保存'),
    status: 'info'
  },
  {
    code: 'Import',
    name: i18n.t('导入'),
    status: 'info'
  },
  {
    code: 'Export',
    name: i18n.t('导出'),
    status: 'info'
  }
]
