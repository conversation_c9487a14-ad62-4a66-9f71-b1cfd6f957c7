import { i18n } from '@/main.js'

export const statusOptions = [
  { label: i18n.t('已归档'), value: '已归档' },
  { label: i18n.t('未归档'), value: '未归档' }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    ignore: true,
    fixed: 'left'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left'
  },
  {
    title: i18n.t('版本号'),
    field: 'versionNo',
    slots: {
      default: 'versionNoDefault'
    }
  },
  {
    title: i18n.t('归档状态'),
    field: 'archiveStatus'
  },
  {
    title: i18n.t('创建人'),
    field: 'createUserName'
  },
  {
    title: i18n.t('创建时间'),
    field: 'createTime'
  },
  {
    title: i18n.t('最后修改人'),
    field: 'updateUserName'
  },
  {
    title: i18n.t('最后修改时间'),
    field: 'updateTime'
  }
]

export const toolbar = [
  {
    code: 'File',
    name: i18n.t('归档'),
    status: 'info'
  }
]
