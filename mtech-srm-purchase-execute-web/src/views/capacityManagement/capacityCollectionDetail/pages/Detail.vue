<!-- 箱体明细 -->
<template>
  <div class="full-height vertical-flex-box">
    <collapse-search :is-grid-display="true" @reset="handleCustomReset" @search="handleSearch">
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <!-- 工厂编码 -->
        <mt-form-item prop="siteCodeList" :label="$t('工厂编码')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.siteCodeList"
            :data-source="siteCodeListOptions"
            :allow-filtering="true"
            :multiple="true"
            filter-type="Contains"
            :filtering="getSiteCodeList"
            :fields="{ text: 'theCodeName', value: 'siteCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 供应商编码 -->
        <mt-form-item prop="supplierCodeList" :label="$t('供应商')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.supplierCodeList"
            :data-source="supplierTypeList"
            :allow-filtering="true"
            filter-type="Contains"
            :multiple="true"
            :filtering="getSupplierList"
            :fields="{ text: 'theCodeName', value: 'supplierCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 反馈状态 -->
        <mt-form-item prop="viewStatus" :label="$t('反馈状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.viewStatus"
            :data-source="viewStatusOptions"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="boxBody" :label="$t('箱体')">
          <mt-input
            v-model="searchFormModel.boxBody"
            :placeholder="$t('请输入')"
            show-clear-button
          />
        </mt-form-item>
        <mt-form-item prop="parts" :label="$t('部件')">
          <mt-input v-model="searchFormModel.parts" :placeholder="$t('请输入')" show-clear-button />
        </mt-form-item>
        <mt-form-item prop="moldNo" :label="$t('模号')">
          <mt-input
            v-model="searchFormModel.moldNo"
            :placeholder="$t('请输入')"
            show-clear-button
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <div class="flex-fit">
      <ScTable
        ref="xTable"
        :columns="columns"
        :table-data="tableData"
        height="auto"
        header-align="left"
        align="left"
        show-overflow
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        style="padding-top: unset"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #historyNoDefault="{ row }">
          <vxe-input
            v-model="row.historyInvQty"
            :placeholder="$t('请输入')"
            type="number"
            min="0"
            clearable
          />
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, toolbar } from '../config/detail'
import { getHeadersFileName, download, addCodeNameKeyInList } from '@/utils/utils'
import { utils } from '@mtech-common/utils'
export default {
  components: { CollapseSearch, ScTable },
  props: {
    version: {
      type: String,
      default: ''
    }
  },
  watch: {
    version: {
      handler(newVal) {
        if (newVal) {
          this.handleSearch()
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      searchFormModel: {
        viewStatus: ''
      },
      columns: columnData,
      siteCodeListOptions: [],
      supplierTypeList: [],
      tableData: [],
      toolbar,
      pageSettings: {
        currentPage: 1,
        pageSize: 50, // 当前每页数据量
        pageCount: 5,
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      viewStatusOptions: [
        { text: this.$t('已查看'), value: '已查看' },
        { text: this.$t('未查看'), value: '未查看' },
        { text: this.$t('已回复'), value: '已回复' }
      ]
    }
  },
  mounted() {
    this.getSupplierList = utils.debounce(this.getSupplierList, 1000)
    this.getSiteCodeList = utils.debounce(this.getSiteCodeList, 1000)
    this.getSiteCodeList()
    this.getSupplierList()
  },
  methods: {
    // 获取供应商信息
    getSupplierList(args = { text: '' }) {
      const { text } = args
      const param = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData.getSupplier(param).then((res) => {
        this.supplierTypeList.length = 0
        const list = res.data || []
        this.supplierTypeList = addCodeNameKeyInList({
          firstKey: 'supplierCode',
          secondKey: 'supplierName',
          list
        }).filter((n) => !!n.theCodeName && !!n.supplierCode)
      })
    },
    // 获取工厂信息
    getSiteCodeList(args = { text: '' }) {
      const { text } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteCodeListOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
          }
        })
        .catch(() => {})
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      const commonToolbar = ['Save']
      if (code === 'CloseEdit') {
        this.$refs.xTable.$refs.xGrid.clearEdit()
        this.handleSearch()
        return
      }
      if (selectedRecords.length === 0 && commonToolbar.includes(code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (code === 'Save') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认保存选中的数据？')
          },
          success: () => {
            this.handleSave(selectedRecords)
          }
        })
      }
      if (code === 'Import') {
        this.handleImport()
      }
      if (code === 'Export') {
        this.handleExport()
      }
    },
    handleSave(selectedRecords) {
      let params = [...selectedRecords]
      params.forEach((item) => {
        if (item.id?.includes('row_')) {
          item.id = null
        }
      })
      this.$API.capacityManagement.saveCapacityCollectionListApi(params).then((res) => {
        if (res.code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.capacityManagement.importCapacityCollectionListApi,
          downloadTemplateApi: false,
          paramsKey: 'excel',
          queryParams: {
            dayCapacityLevel: '按箱体'
          },
          downloadTemplateParams: {
            templateType: 'PURCHASE'
          }
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    handleExport() {
      let params = {
        page: {
          pages: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        },
        versionNo: this.version,
        dayCapacityLevel: '按箱体',
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.capacityManagement.exportCapacityCollectionListApi(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.pageSettings.currentPage = 1
      this.handleCustomSearch()
    },
    handleCustomSearch() {
      let params = {
        page: {
          pages: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        },
        versionNo: this.version,
        dayCapacityLevel: '按箱体',
        ...this.searchFormModel
      }
      this.$API.capacityManagement.pageCapacityCollectionListApi(params).then((res) => {
        if (res?.code == 200) {
          const total = res.data.total || 0
          this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
          this.pageSettings.totalRecordsCount = Number(total)
          this.tableData = res.data?.records || []
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.handleCustomSearch()
    },
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.handleCustomSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  & .span-style {
    display: flex;
    flex-flow: column;
    & span:nth-child(1) {
      border-bottom: 1px solid #cfd4d9;
    }
  }
}
</style>
