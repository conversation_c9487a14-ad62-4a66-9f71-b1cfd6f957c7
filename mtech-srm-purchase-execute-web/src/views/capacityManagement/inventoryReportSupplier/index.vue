<!-- 供方 - 模具库存报表 -->
<template>
  <div>
    <mt-tabs
      :e-tab="false"
      :data-source="tabList"
      :selected-item="tabIndex"
      @handleSelectTab="handleSelectTab"
    />
    <div>
      <List v-if="tabIndex === 0" />
      <Detail v-if="tabIndex === 1" />
    </div>
  </div>
</template>

<script>
export default {
  components: {
    List: () => import('./pages/List.vue'),
    Detail: () => import('./pages/Detail.vue')
  },
  data() {
    return {
      tabList: [{ title: this.$t('物料视图') }, { title: this.$t('箱体视图') }],
      tabIndex: 0
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    }
  }
}
</script>
