import { i18n } from '@/main.js'

export const statisticTypeList = [
  {
    statisticType: i18n.t('产出数'),
    field: 'outputQuantityList',
    total: 'outputQuantitySum',
    value: 'OUTPUT_QUANTITY'
  },
  {
    statisticType: i18n.t('送货数'),
    field: 'moldSendQuantityList',
    total: 'moldSendQuantitySum',
    value: 'MOLD_SEND_QUANTITY'
  },
  {
    statisticType: i18n.t('成品库存结余数'),
    field: 'completeInventoryQuantityList',
    total: 'completeInventoryQuantitySum',
    value: 'COMPLETE_INVENTORY_QUANTITY'
  },
  {
    statisticType: i18n.t('半成品库存结余数'),
    field: 'halfInventoryQuantityList',
    total: 'halfInventoryQuantitySum',
    value: 'HALF_INVENTORY_QUANTITY'
  },
  {
    statisticType: i18n.t('当日产量达成率（按98%）'),
    field: 'dayCapacity98List',
    total: 'outputCapacity98Sum',
    value: 'DAY_CAPACITY_98'
  },
  {
    statisticType: i18n.t('当日产量达成率（按95%）'),
    field: 'dayCapacity95List',
    total: 'outputCapacity95Sum',
    value: 'DAY_CAPACITY_95'
  }
]

export const statisticDimensionList = [
  { statisticDimension: i18n.t('工厂'), field: 'siteCode', value: 'site_code' },
  { statisticDimension: i18n.t('物料'), field: 'itemCode', value: 'item_code' },
  { statisticDimension: i18n.t('箱体'), field: 'boxBody', value: 'box_body' },
  { statisticDimension: i18n.t('部件'), field: 'parts', value: 'parts' },
  { statisticDimension: i18n.t('供应商'), field: 'supplierCode', value: 'supplier_code' }
]

export const columnData = [
  {
    title: i18n.t('序号'),
    field: 'serialNumber',
    width: 80,
    align: 'center',
    fixed: 'left'
  },
  {
    title: i18n.t('工厂'),
    field: 'siteCode',
    width: 250,
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.siteName
    }
  },
  {
    title: i18n.t('物料编码'),
    field: 'itemCode',
    width: 120
  },
  {
    title: i18n.t('物料名称'),
    field: 'itemName',
    width: 200
  },
  {
    title: i18n.t('箱体'),
    field: 'boxBody',
    width: 100
  },
  {
    title: i18n.t('部件'),
    field: 'parts',
    width: 100
  },
  {
    title: i18n.t('供应商'),
    field: 'supplierCode',
    width: 200,
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.supplierName
    }
  },
  {
    title: i18n.t('供应商简称'),
    field: 'supplierAbbr',
    width: 100
  },
  {
    title: i18n.t('提速前周期(s)'),
    field: 'preCycle',
    width: 160
  },
  {
    title: i18n.t('提速后周期(s)'),
    field: 'cycle',
    width: 150
  },
  {
    title: i18n.t('理论产量(效率98%)'),
    field: 'outputCapacity98',
    width: 150
  },
  {
    title: i18n.t('理论产量(效率95%)'),
    field: 'outputCapacity95',
    width: 160
  },
  {
    title: i18n.t('统计类型'),
    field: 'statisticType',
    width: 160
  },
  {
    title: i18n.t('合计'),
    field: 'total',
    width: 100
  }
]

export const toolbar = [
  {
    code: 'Export',
    name: i18n.t('导出'),
    status: 'info'
  }
]
