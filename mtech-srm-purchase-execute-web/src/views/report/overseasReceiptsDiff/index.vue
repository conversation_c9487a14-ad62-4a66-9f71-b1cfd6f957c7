<template>
  <!-- 交货计划横向报表 - 采方 -->
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      @reset="handleCustomReset"
      @search="handleSearch('handle')"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="supDeliveryCode" :label="$t('供应商送货单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.supDeliveryCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="hkDeliveryCode" :label="$t('香港电子送货单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.hkDeliveryCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="diff" :label="$t('是否有差异')" label-style="top">
          <mt-select
            v-model="searchFormModel.diff"
            :data-source="diffOptions"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('送货单创建时间')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
            :change="(e) => dateChange(e, 'CreateTime')"
          />
        </mt-form-item>
        <mt-form-item prop="asn1ReceiveTime" :label="$t('ASN1收货时间')">
          <mt-date-range-picker
            v-model="searchFormModel.asn1ReceiveTime"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
            :change="(e) => asnDateChange(e, 'asn1')"
          />
        </mt-form-item>
        <mt-form-item prop="asn2ReceiveTime" :label="$t('ASN2收货时间')">
          <mt-date-range-picker
            v-model="searchFormModel.asn2ReceiveTime"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
            :change="(e) => asnDateChange(e, 'asn2')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        class="xTable-class"
        :row-config="{ height: 130 }"
        :columns="columns"
        :table-data="tableData"
        show-overflow
        :min-height="600"
        border="none"
        header-align="left"
        row-class-name="table-row-class"
        align="center"
        style="padding-top: unset"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
      >
        <template slot="custom-tools">
          <!-- :icon="item.icon" -->
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #checkTemplate="{ column, row }">
          <span class="span-style" v-if="typeof row[column.field] === 'object'">
            <span>{{ row[column.field]['systemBuyerNum'] }}</span>
            <span>{{ row[column.field]['handleBuyerNum'] }}</span>
          </span>
        </template>
      </ScTable>
    </div>
    <mt-page
      ref="mtPage"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import dayjs from 'dayjs'
import { diffOptions, ToolBar, columnData } from './config'
import { getHeadersFileName, download } from '../../../utils/utils'

const getLastThreeMonthsRange = () => {
  var endDate = new Date() // 当前日期
  var startDate = new Date() // 开始日期
  startDate.setMonth(startDate.getMonth() - 3) // 将开始日期设置为三个月之前
  var formattedStartDate = startDate.toISOString().split('T')[0]
  var formattedEndDate = endDate.toISOString().split('T')[0]

  return [new Date(formattedStartDate), new Date(formattedEndDate)]
}

export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      toolbar: ToolBar,
      searchFormModel: {
        supDeliveryCode: null,
        hkDeliveryCode: null,
        diff: null,
        createTime: getLastThreeMonthsRange(),
        asn1ReceiveTime: null,
        asn2ReceiveTime: null
      },
      tableData: [],
      columns: columnData,
      pageSettings: {
        currentPage: 1,
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      diffOptions
    }
  },
  mounted() {
    // this.searchFormModel.createTime = this.getLastThreeMonthsRange()
    this.searchFormModel['minCreateTime'] = dayjs(this.searchFormModel.createTime[0]).valueOf()
    this.searchFormModel['maxCreateTime'] = dayjs(this.searchFormModel.createTime[1]).valueOf()
    this.getTableData()
  },
  methods: {
    // 需求日期更改
    dateChange(e, flag) {
      if (e && e.startDate) {
        this.searchFormModel['min' + flag] = dayjs(e.startDate).valueOf()
        this.searchFormModel['max' + flag] = dayjs(e.endDate).valueOf()
      } else {
        this.searchFormModel['min' + flag] = null
        this.searchFormModel['max' + flag] = null
      }
    },
    asnDateChange(e, flag) {
      if (e && e.startDate) {
        this.searchFormModel[flag + 'MinReceiveTime'] = dayjs(e.startDate).valueOf()
        this.searchFormModel[flag + 'MaxReceiveTime'] = dayjs(e.endDate).valueOf()
      } else {
        this.searchFormModel[flag + 'MinReceiveTime'] = null
        this.searchFormModel[flag + 'MaxReceiveTime'] = null
      }
    },
    handleClickToolBar(args) {
      const { code } = args
      if (code === 'export') {
        this.handleClickExport()
      }
    },
    handleClickExport() {
      const { visibleColumn } = this.$refs.xTable.$refs.xGrid.getTableColumn()
      const headerMap = {}
      visibleColumn.forEach((i) => {
        if (i.field && i.title) {
          headerMap[i.field] = i.title
        }
      })
      const param = {
        ...this.searchFormModel,
        headerMap
      }
      if (param.createTime) {
        delete param.createTime
      }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery.exportOverseasReceiptsDiff(param).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          if (Array.isArray(this.searchFormModel[key])) {
            this.searchFormModel[key] = []
          } else {
            this.searchFormModel[key] = null
          }
        }
      }
      // this.$refs.searchFormRef.resetFields()
      this.handleSearch('handle')
    },
    handleSearch() {
      if (this.currentPage === 1) {
        this.getTableData()
      } else {
        this.currentPage = 1
        // 这里会触发handleCurrentChange事件继而触发请求数据, 当前mt-page直接设置currentPage不生效，故如此操作
        this.$refs.mtPage.goToPage(1)
      }
    },
    // 采方-获取信息列表
    getTableData() {
      const params = {
        pageSize: this.pageSettings.pageSize,
        currentPage: this.currentPage,
        ...this.searchFormModel
      }
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .queryOverseasReceiptsDiff(params)
        .then((res) => {
          if (res?.code == 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            const records = res?.data?.records || [] // 表格数据

            // 处理表数据
            this.tableData = records
          }
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-cell {
  &.c--tooltip {
    padding: 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  & .span-style {
    display: flex;
    flex-flow: column;
    & span:nth-child(1) {
      border-bottom: 1px solid #cfd4d9;
    }
  }
}
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
::v-deep .xTable-class .vxe-table .table-row-class td {
  height: 40px !important;
}
</style>
