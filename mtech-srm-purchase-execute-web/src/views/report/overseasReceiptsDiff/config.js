import { i18n } from '@/main.js'

export const ToolBar = [
  {
    code: 'export',
    name: i18n.t('导出'),
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  }
]

export const diffOptions = [
  { text: '是', value: true },
  { text: '否', value: false }
]

// 选择 物料 弹框 表格列数据
export const columnData = [
  {
    field: 'supDeliveryCode',
    title: i18n.t('供应商送货单号')
  },
  {
    field: 'supDeliveryQuantity',
    title: i18n.t('ASN1送货数量')
  },
  {
    field: 'supReceiveQuantity',
    title: i18n.t('ASN1收货数量')
  },
  {
    field: 'supReceiveTime',
    title: i18n.t('ASN1收货时间')
  },
  {
    field: 'hkDeliveryCode',
    title: i18n.t('香港电子送货单号')
  },
  {
    field: 'hkDeliveryQuantity',
    title: i18n.t('ASN2送货数量')
  },
  {
    field: 'hkReceiveQuantity',
    title: i18n.t('ASN2收货数量')
  },
  {
    field: 'hkReceiveTime',
    title: i18n.t('ASN2收货时间')
  },
  {
    field: 'diff',
    title: i18n.t('是否有差异')
  },

  {
    field: 'diffNum',
    title: i18n.t('差异数量')
  },
  {
    field: 'createTime',
    title: i18n.t('送货单创建时间')
  }
]
