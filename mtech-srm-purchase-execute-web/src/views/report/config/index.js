import Vue from 'vue'
import { i18n } from '@/main.js'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, isDateTime, isDate, isTime } = args

  const template = () => {
    return {
      template: Vue.component('timeDateComponent', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <div v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</div>
            <div v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</div>
          </div>
        </div>`,
        data: function () {
          return { data: {}, dataKey, isDateTime, isDate, isTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
export const vmiLnBoundList = [
  {
    width: '80',
    field: 'serialNumber',
    headerText: i18n.t('序号')
  },
  {
    width: '95',
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    width: '125',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '125',
    field: 'purchaseGroupCode',
    headerText: i18n.t('采购组编码')
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编号')
  },
  {
    width: '125',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '185',
    field: 'quotaPercent',
    headerText: i18n.t('配额比例'),
    template: function () {
      return {
        template: Vue.component('bindCheck', {
          template: `<span>{{ data.quotaPercent ? data.quotaPercent + '%' : '' }}</span>`,
          data() {
            return {}
          }
        })
      }
    }
  },
  {
    width: '125',
    field: 'inboundRate',
    headerText: i18n.t('入库执行比例'),
    template: function () {
      return {
        template: Vue.component('bindCheck', {
          template: `<span>{{ data.inboundRate ? data.inboundRate + '%' : '' }}</span>`,
          data() {
            return {}
          }
        })
      }
    }
  },
  {
    width: '125',
    field: 'differRate', // 需要替换字段名
    headerText: i18n.t('入库差异比例'),
    template: function () {
      return {
        template: Vue.component('bindCheck', {
          template: `<span>{{ data.differRate ? data.differRate + '%' : '' }}</span>`,
          data() {
            return {}
          }
        })
      }
    }
  },
  // {
  //   width: "95",
  //   field: "expectInboundCount",
  //   headerText: i18n.t("应入库数量"),
  // },
  {
    width: '95',
    field: 'warehouseQty',
    headerText: i18n.t('总入库数量')
  },
  {
    field: 'quotaQuantity',
    headerText: i18n.t('配额数量')
  },
  {
    width: '165',
    field: 'realInboundCount',
    headerText: i18n.t('入库执行数量')
  },
  {
    width: '165',
    field: 'differCount',
    headerText: i18n.t('入库差异数量')
  },
  {
    width: '110',
    field: 'unitName',
    headerText: i18n.t('计量单位')
  },
  {
    width: '110',
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    field: 'purchaseGroupName',
    headerText: i18n.t('采购组名称')
  },
  {
    width: '85',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '110',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
  // {
  //   width: "85",
  //   field: "confirmTime",
  //   headerText: i18n.t("日期"),
  // },
]
export const vmiLnBoundMonthList = [
  {
    width: '80',
    field: 'serialNumber',
    headerText: i18n.t('序号')
  },
  {
    width: '125',
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    width: '125',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '125',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组编码')
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编号')
  },
  {
    width: '125',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '125',
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    width: '125',
    field: 'itemVoucherDate',
    headerText: i18n.t('入库凭证年月')
    // template: timeDate({ dataKey: "confirmTime", isDate: true }),
  },
  {
    width: '185',
    field: 'quotaPercent',
    headerText: i18n.t('配额比例'),
    template: function () {
      return {
        template: Vue.component('bindCheck', {
          template: `<span>{{ data.quotaPercent ? data.quotaPercent + '%' : '' }}</span>`,
          data() {
            return {}
          }
        })
      }
    }
  },
  {
    width: '125',
    field: 'inboundRate',
    headerText: i18n.t('入库执行比例'),
    template: function () {
      return {
        template: Vue.component('bindCheck', {
          template: `<span>{{ data.inboundRate ? data.inboundRate + '%' : '' }}</span>`,
          data() {
            return {}
          }
        })
      }
    }
  },
  {
    width: '125',
    field: 'differRate', // 需要替换字段名
    headerText: i18n.t('入库差异比例'),
    template: function () {
      return {
        template: Vue.component('bindCheck', {
          template: `<span>{{ data.differRate ? data.differRate + '%' : '' }}</span>`,
          data() {
            return {}
          }
        })
      }
    }
  },
  // {
  //   width: "95",
  //   field: "expectInboundCount",
  //   headerText: i18n.t("应入库数量"),
  // },
  {
    field: 'warehouseQty',
    headerText: i18n.t('总入库数量')
  },
  {
    field: 'quotaQuantity',
    headerText: i18n.t('配额数量')
  },
  {
    width: '165',
    field: 'realInboundCount',
    headerText: i18n.t('入库执行数量')
  },
  {
    width: '165',
    field: 'differCount',
    headerText: i18n.t('入库差异数量')
  },
  {
    width: '110',
    field: 'unitName',
    headerText: i18n.t('计量单位')
  },
  {
    width: '110',
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    field: 'purchaseGroupName',
    headerText: i18n.t('采购组名称')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '110',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '110',
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
  // {
  //   width: "85",
  //   field: "confirmTime",
  //   headerText: i18n.t("日期"),
  // },
]
export const vmiLnBoundPurchaseList = [
  {
    width: '80',
    field: 'serialNumber',
    headerText: i18n.t('序号')
  },
  {
    width: '125',
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    width: '125',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编号')
  },
  {
    width: '125',
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    width: '125',
    field: 'itemVoucherDate',
    headerText: i18n.t('入库凭证年月')
    // template: timeDate({ dataKey: "confirmTime", isDate: true }),
  },
  {
    field: 'expectInboundCount',
    headerText: i18n.t('总入库数量')
  },
  {
    width: '165',
    field: 'realInboundCount',
    headerText: i18n.t('入库执行数量')
  },
  {
    width: '165',
    field: 'inboundRate',
    headerText: i18n.t('入库执行比例'),
    template: function () {
      return {
        template: Vue.component('bindCheck', {
          template: `<span>{{ data.inboundRate ? data.inboundRate + '%' : '' }}</span>`,
          data() {
            return {}
          }
        })
      }
    }
  },
  {
    width: '165',
    field: 'priceCount',
    headerText: i18n.t('总入库金额')
  },
  {
    width: '165',
    field: 'groupPrice',
    headerText: i18n.t('入库金额')
  },
  {
    width: '165',
    field: 'inboundPriceRate',
    headerText: i18n.t('入库金额占比'),
    template: function () {
      return {
        template: Vue.component('bindCheck', {
          template: `<span>{{ data.inboundPriceRate ? data.inboundPriceRate + '%' : '' }}</span>`,
          data() {
            return {}
          }
        })
      }
    }
  },
  {
    width: '110',
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '110',
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
  // {
  //   width: "85",
  //   field: "confirmTime",
  //   headerText: i18n.t("日期"),
  // },
]
export const failureStockRateList = [
  {
    width: '95',
    field: 'index',
    headerText: i18n.t('序号')
  },
  {
    width: '95',
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    width: '250',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '125',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组编码')
  },
  {
    width: '85',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '125',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '110',
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },

  {
    width: '110',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },

  {
    field: 'buyerOrgName',
    headerText: i18n.t('采购组名称')
  },

  {
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },

  {
    width: '155',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
export const buyerStockRateList = [
  {
    width: '95',
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    width: '250',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '125',
    field: 'purGroupCode',
    headerText: i18n.t('采购组编码')
  },
  {
    width: '85',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '125',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '185',
    field: 'reportTime',
    headerText: i18n.t('生成报告时间'),
    template: timeDate({ dataKey: 'reportTime', isDate: true })
  },

  {
    width: '85',
    field: 'stockRateDesc',
    headerText: i18n.t('备货达成率')
  },
  {
    width: '85',
    field: 'stockCount',
    headerText: i18n.t('当前库存数量')
  },
  {
    width: '85',
    field: 'requireCount',
    headerText: i18n.t('N天内需求量')
  },
  {
    width: '85',
    field: 'unitName',
    headerText: i18n.t('计量单位')
  },
  {
    width: '110',
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },

  {
    width: '110',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },

  {
    field: 'purGroupName',
    headerText: i18n.t('采购组名称')
  },

  {
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },

  {
    width: '85',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
export const deliveryDateReplyList = [
  {
    width: '95',
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    width: '125',
    field: 'timeInfoTimestamp',
    headerText: i18n.t('需求日期'),
    template: timeDate({ dataKey: 'timeInfoTimestamp', isDate: true })
  },
  {
    width: '125',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '125',
    field: 'purchaseGroupCode',
    headerText: i18n.t('采购组编码')
  },

  {
    width: '125',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },

  {
    width: '125',
    field: 'deliveryDateReplyRate',
    headerText: i18n.t('交期回复率')
  },
  {
    width: '125',
    field: 'supplierNum',
    headerText: i18n.t('反馈行数')
  },
  {
    width: '125',
    field: 'buyerNum',
    headerText: i18n.t('总需求行数')
  },

  {
    width: '110',
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    width: '110',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },

  {
    field: 'purchaseGroupName',
    headerText: i18n.t('采购组名称')
  },

  {
    width: '145',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },

  {
    width: '125',
    field: 'createDate',
    headerText: i18n.t('创建日期')
  },
  {
    width: '125',
    field: 'modifyDate',
    headerText: i18n.t('修改日期')
  }
]
export const orderRate = [
  {
    width: '95',
    field: 'index',
    headerText: i18n.t('序号')
  },
  {
    width: '125',
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    width: '125',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '125',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组编码')
  },
  {
    width: '125',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '125',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '125',
    field: 'quotaPercent',
    headerText: i18n.t('配额比例'),
    template: function () {
      return {
        template: Vue.component('bindCheck', {
          template: `<span>{{ data.quotaPercent ? data.quotaPercent + '%' : '' }}</span>`,
          data() {
            return {}
          }
        })
      }
    }
  },
  {
    width: '125',
    field: 'orderExecutePercent',
    headerText: i18n.t('下单执行比例'),
    template: function () {
      return {
        template: Vue.component('bindCheck', {
          template: `<span>{{ data.orderExecutePercent ? data.orderExecutePercent + '%' : '' }}</span>`,
          data() {
            return {}
          }
        })
      }
    }
  },
  {
    width: '125',
    field: 'orderDifferPercent',
    headerText: i18n.t('下单差异比例'),
    template: function () {
      return {
        template: Vue.component('bindCheck', {
          template: `<span>{{ data.orderDifferPercent ? data.orderDifferPercent + '%' : '' }}</span>`,
          data() {
            return {}
          }
        })
      }
    }
  },
  {
    field: 'totalQuantity',
    headerText: i18n.t('总下单数量')
  },
  {
    field: 'quotaQuantity',
    headerText: i18n.t('配额数量')
  },
  {
    width: '110',
    field: 'orderExecuteQuantity',
    headerText: i18n.t('下单执行数量')
  },
  {
    width: '125',
    field: 'orderDifferQuantity',
    headerText: i18n.t('下单差异数量')
  },
  {
    field: 'uncleanQuantityTotal',
    headerText: i18n.t('总未清数量')
  },
  {
    field: 'uncleanQuantity',
    headerText: i18n.t('未清数量')
  },
  {
    field: 'uncleanQuantityPercent',
    headerText: i18n.t('未清数量占比'),
    template: function () {
      return {
        template: Vue.component('bindCheck', {
          template: `<span>{{ data.uncleanQuantityPercent ? data.uncleanQuantityPercent + '%' : '' }}</span>`,
          data() {
            return {}
          }
        })
      }
    }
  },
  {
    field: 'unitName',
    headerText: i18n.t('计量单位')
  },
  {
    width: '145',
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    width: '145',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '145',
    field: 'buyerOrgName',
    headerText: i18n.t('采购组名称')
  },
  {
    width: '145',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '125',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]
export const orderRateMonth = [
  {
    width: '80',
    field: 'serialNumber',
    headerText: i18n.t('序号')
  },
  {
    width: '125',
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    width: '125',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '125',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组编码')
  },
  {
    width: '125',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '125',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '125',
    field: 'categoryCode',
    headerText: i18n.t('品类编码')
  },
  {
    width: '125',
    field: 'yearMonth',
    headerText: i18n.t('订单下单年月')
    // template: timeDate({ dataKey: "timeInfoTimestamp", isDate: true }),
  },
  {
    width: '125',
    field: 'quotaPercent',
    headerText: i18n.t('配额比例'),
    template: function () {
      return {
        template: Vue.component('bindCheck', {
          template: `<span>{{ data.quotaPercent ? data.quotaPercent + '%' : '' }}</span>`,
          data() {
            return {}
          }
        })
      }
    }
  },
  {
    width: '125',
    field: 'orderExecutePercent',
    headerText: i18n.t('下单执行比例'),
    template: function () {
      return {
        template: Vue.component('bindCheck', {
          template: `<span>{{ data.orderExecutePercent ? data.orderExecutePercent + '%' : '' }}</span>`,
          data() {
            return {}
          }
        })
      }
    }
  },
  {
    width: '125',
    field: 'orderDifferPercent',
    headerText: i18n.t('下单差异比例'),
    template: function () {
      return {
        template: Vue.component('bindCheck', {
          template: `<span>{{ data.orderDifferPercent ? data.orderDifferPercent + '%' : '' }}</span>`,
          data() {
            return {}
          }
        })
      }
    }
  },
  {
    field: 'totalQuantity',
    headerText: i18n.t('总下单数量')
  },
  {
    field: 'quotaQuantity',
    headerText: i18n.t('配额数量')
  },
  {
    width: '110',
    field: 'orderExecuteQuantity',
    headerText: i18n.t('下单执行数量')
  },
  {
    width: '125',
    field: 'orderDifferQuantity',
    headerText: i18n.t('下单差异数量')
  },
  {
    field: 'uncleanQuantityTotal',
    headerText: i18n.t('总未清数量')
  },
  {
    field: 'uncleanQuantity',
    headerText: i18n.t('未清数量')
  },
  {
    field: 'uncleanQuantityPercent',
    headerText: i18n.t('未清数量占比'),
    template: function () {
      return {
        template: Vue.component('bindCheck', {
          template: `<span>{{ data.uncleanQuantityPercent ? data.uncleanQuantityPercent + '%' : '' }}</span>`,
          data() {
            return {}
          }
        })
      }
    }
  },
  {
    field: 'unitName',
    headerText: i18n.t('计量单位')
  },
  {
    width: '145',
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    width: '145',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '125',
    field: 'buyerOrgName',
    headerText: i18n.t('采购组名称')
  },
  {
    width: '145',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '125',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '125',
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  }
]
export const plan = [
  {
    width: '125',
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    width: '125',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '125',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组编码')
  },
  {
    width: '125',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '145',
    field: 'itemCode',
    headerText: i18n.t('物料')
  },
  {
    width: '145',
    field: 'timeInfoTimestamp',
    headerText: i18n.t('需求时间'),
    template: timeDate({ dataKey: 'timeInfoTimestamp', isDate: true })
  },
  {
    width: '110',
    field: 'demandSatisfiedRate',
    headerText: i18n.t('需求满足率')
  },
  {
    width: '125',
    field: 'promiseSatisfiedRate',
    headerText: i18n.t('承诺满足率')
  },
  {
    field: 'onTimeDeliveryRate',
    headerText: i18n.t('交货准时率')
  },
  {
    width: '145',
    field: 'sumBuyerNum',
    headerText: i18n.t('p值求和')
  },
  {
    width: '145',
    field: 'sumSupplierNum',
    headerText: i18n.t('c值求和')
  },
  {
    width: '125',
    field: 'deliveryQuantity',
    headerText: i18n.t('送货数量')
  },
  {
    width: '145',
    field: 'receiveQuantity',
    headerText: i18n.t('收货数量')
  },
  {
    width: '145',
    field: 'baseMeasureUnitName',
    headerText: i18n.t('计量单位')
  },
  {
    width: '125',
    field: 'buyerOrgName',
    headerText: i18n.t('采购组名称')
  },

  {
    width: '125',
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },

  {
    width: '145',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },

  {
    width: '145',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },

  {
    width: '145',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  }
]
export const planDay = [
  {
    width: '125',
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    width: '125',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },

  {
    width: '125',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '145',
    field: 'timeInfoTimestamp',
    headerText: i18n.t('需求时间'),
    template: timeDate({ dataKey: 'timeInfoTimestamp', isDate: true })
  },
  {
    width: '110',
    field: 'demandSatisfiedRate',
    headerText: i18n.t('需求满足率')
  },
  {
    width: '125',
    field: 'promiseSatisfiedRate',
    headerText: i18n.t('承诺满足率')
  },
  {
    field: 'onTimeDeliveryRate',
    headerText: i18n.t('交货准时率')
  },
  {
    width: '145',
    field: 'sumBuyerNum',
    headerText: i18n.t('p值求和')
  },
  {
    width: '145',
    field: 'sumSupplierNum',
    headerText: i18n.t('c值求和')
  },
  {
    width: '125',
    field: 'deliveryQuantity',
    headerText: i18n.t('送货数量')
  },
  {
    width: '145',
    field: 'receiveQuantity',
    headerText: i18n.t('收货数量')
  },

  {
    width: '125',
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },

  {
    width: '145',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },

  {
    width: '145',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  }
]
