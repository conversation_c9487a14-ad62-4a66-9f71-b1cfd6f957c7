<template>
  <!-- 采方-对账协同-采购对账单列表 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      class="template-height"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form
            ref="searchFormRef"
            :model="searchFormModel"
            :rules="{
              bigVersionNos: [{ required: true, message: $t('请输入'), trigger: 'blur' }],
              planMemberCode: [{ required: true, message: $t('请输入'), trigger: 'blur' }]
            }"
          >
            <mt-form-item prop="bigVersionNos" :label="$t('版本号')" label-style="top">
              <mt-multi-select
                v-model="searchFormModel.bigVersionNos"
                :data-source="versionList"
                :show-select-all="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择')"
              ></mt-multi-select>
            </mt-form-item>
            <mt-form-item prop="planMemberCode" :label="$t('计划员')" label-style="top">
              <mt-multi-select
                style="flex: 1"
                v-model="searchFormModel.planMemberCode"
                :data-source="plannerListOptions"
                :fields="{ text: 'codeAndName', value: 'userCode' }"
                :show-select-all="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择')"
              ></mt-multi-select>
            </mt-form-item>
            <mt-form-item prop="supplierCodes" :label="$t('供应商编码')" label-style="top">
              <mt-input
                style="flex: 1"
                v-model="supplierCodes"
                @change="supplierChange"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import * as UTILS from '@/utils/utils'
import Vue from 'vue'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      supplierCodes: '',
      versionList: [],
      plannerListOptions: [],
      searchFormModel: {},
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          toolbar: [
            {
              id: 'RateExport',
              icon: 'icon_table_restart',
              title: this.$t('导出')
            }
          ],
          gridId: '859d4192-064d-4a86-a7c1-4f78ae6c0b24',
          grid: {
            columnData: [
              {
                field: 'id',
                headerText: 'id',
                width: 0,
                visible: false,
                isPrimaryKey: true
              },
              {
                width: '120',
                field: 'bigVersionNo',
                headerText: this.$t('版本号')
              },
              {
                // width: "150",
                field: 'supplierCode',
                headerText: this.$t('供应商'),
                template: () => {
                  return {
                    template: Vue.component('supplierCode', {
                      template: `
                        <div class="headers">
                          <span>{{data.supplierCode}} - {{data.supplierName}}</span>
                        </div>
                      `,
                      data() {
                        return {
                          data: {}
                        }
                      }
                    })
                  }
                }
              },
              {
                field: 'planMemberCode',
                headerText: this.$t('计划员'),
                template: () => {
                  return {
                    template: Vue.component('planMemberCode', {
                      template: `
                        <div class="headers">
                          <span>{{data.planMemberCode}} - {{data.plannerName}}</span>
                        </div>
                      `,
                      data() {
                        return {
                          data: {}
                        }
                      }
                    })
                  }
                }
              },
              {
                width: '120',
                field: 'collaborativeSum',
                headerText: this.$t('协同总数')
              },
              {
                width: '120',
                field: 'alreadyCollaborativeNum',
                headerText: this.$t('已协同数')
              },
              {
                width: '120',
                field: 'alreadyCollaborativeRatio',
                headerText: this.$t('已协同比例%')
              },
              {
                width: '120',
                field: 'noCollaborativeNum',
                headerText: this.$t('未协同数')
              },
              {
                width: '120',
                field: 'noCollaborativeRatio',
                headerText: this.$t('未协同比例%')
              }
            ],
            dataSource: [],
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [20, 50, 100, 200],
              totalRecordsCount: 0
            },
            asyncConfig: {
              url: `/srm-purchase-execute/tenant/demand/plan/buyer/tv/report/query`,
              recordsPosition: 'data.records',
              ignoreDefaultSearch: true,
              ignoreSearchToast: true,
              params: {}
            },
            // lineSelection: 0,
            lineIndex: 1
            // frozenColumns: 1
          }
        }
      ]
    }
  },
  mounted() {
    this.getPlannerList()
    this.buyerGoodsDemandPlanInfoQueryVersionTv()
  },
  methods: {
    // 查询条件操作供应商编码切割
    supplierChange(e) {
      if (e) {
        this.searchFormModel.supplierCodes = this.supplierCodes.split(' ')
      } else {
        this.searchFormModel.supplierCodes = null
      }
    },
    // 获取计划员下拉
    getPlannerList() {
      this.$API.predictCollaboration.getPlannerAllName().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.plannerListOptions = data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.userCode} - ${i.userName}`
            }
          })
          // this.plannerListOptions.push({
          //   userCode: '',
          //   codeAndName: this.$t('计划员空')
          // })
        }
      })
    },
    // 获取版本下拉列表
    buyerGoodsDemandPlanInfoQueryVersionTv() {
      this.$API.deliverySchedule.buyerGoodsDemandPlanInfoQueryVersionTv().then((res) => {
        const { data, code } = res
        if (code === 200) {
          this.versionList = data
        }
      })
    },
    handleClickToolBar(e) {
      if (e.toolbar.id === 'RateExport') {
        this.handleExport()
      }
    },
    handleExport() {
      if (!this.searchFormModel.bigVersionNos || this.searchFormModel.bigVersionNos.length === 0) {
        this.$toast({ content: this.$t('请选择版本号'), type: 'warning' })
        return
      }
      if (
        !this.searchFormModel.planMemberCode ||
        this.searchFormModel.planMemberCode.length === 0
      ) {
        this.$toast({ content: this.$t('请选择计划员'), type: 'warning' })
        return
      }
      //导出
      const params = {
        page: {
          size: 9999,
          current: 1
        },
        ...this.searchFormModel
      }
      this.apiStartLoading()
      this.$API.report
        .exportFeedbackRateReport(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = UTILS.getHeadersFileName(res)
          UTILS.download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    // 重置查询条件
    handleCustomReset() {
      this.supplierCodes = null
      this.searchFormModel.supplierCodes = null
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style></style>
