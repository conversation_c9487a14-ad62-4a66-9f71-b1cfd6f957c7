<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      :hidden-tabs="true"
      class="template-height"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    />
    <delivery-dialog
      v-if="deliveryShow"
      ref="child"
      @handleAddDialogShow="handleAddDialogShow"
      :top-info="topData"
      @handleDialogShow="handleDialogShow"
    >
    </delivery-dialog>
  </div>
</template>

<script>
import { Toolbar, ColumnData } from './config/constant'
import { formatTableColumnData, serializeList } from './config/index.js'

export default {
  components: {
    DeliveryDialog: require('./components/deliveryDialog').default
  },
  data() {
    return {
      // 弹出框类型
      deliveryShow: false,
      topData: '',
      DialogActionType: {
        Add: 0, // 新增
        Edit: 1 // 编辑
      },
      componentConfig: [
        {
          toolbar: Toolbar,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          // gridId: this.$tableUUID.deliveryConfig.unloadingPlaceConfig.list,
          grid: {
            lineSelection: 0, // 添加 checkbox 在第一列
            frozenColumns: 1, // 冻结第一列
            columnData: formatTableColumnData(ColumnData),
            dataSource: [
              // 测试数据
            ],
            asyncConfig: {
              // 获取卸货地点列表
              url: `/statistics/tenant/stockRateConfig/query`,
              // 序列化
              serializeList: serializeList
            }
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id === 'ConfigAdd') {
        // 配置-toolbar-新增
        this.configAdd()
      }
      const selectRows = e.gridRef.getMtechGridRecords()
      console.log(selectRows)
      const selectedId = []
      selectRows.forEach((item) => selectedId.push(item.id))
      console.log(selectedId)

      if (e.toolbar.id === 'ConfigDelete') {
        // 配置-删除
        this.handleConfigDelete(selectedId)
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id === 'ConfigDelete') {
        // 配置-删除
        this.handleConfigDelete([e.data.id])
      }
      // if (e.tool.id === "ConfigEdit") {
      //   // 配置-
      //   this.handleConfigEdit(e.data);
      // }
    },
    handleConfigDelete(params) {
      let obj = {
        ids: []
      }
      obj.ids = params
      this.$API.report.stockRateConfigDelete(obj.ids).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.refreshColumns()
        }
      })
      // .then((res) => {
      //   if (res.code == 200) {

      //   }
      // });
    },
    configAdd() {
      // 显示 配置 新增 弹框
      this.deliveryShow = true
      return
    },
    // 配置-编辑
    // handleConfigEdit(data) {
    //   this.deliveryShow = true;
    //   this.topData = data;
    // },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    handleDialogShow(flag) {
      this.deliveryShow = flag
      this.topData = ''
    },
    handleAddDialogShow() {
      this.deliveryShow = false
      this.topData = ''

      this.refreshColumns()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped></style>
