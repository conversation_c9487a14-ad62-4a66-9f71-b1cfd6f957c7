import { i18n } from '@/main.js'

// 格式化表格动态数据
export const formatTableColumnData = (data) => {
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName
    }
    if (col.fieldCode === 'serialNumber') {
      // 序号
      defaultCol.cssClass = '' // 序号不可点击
      defaultCol.cellTools = [
        // {
        //   id: "ConfigEdit",
        //   icon: "icon_list_edit",
        //   // permission: ["O_02_0553"],
        //   title: i18n.t("编辑"),
        // },
        {
          id: 'ConfigDelete',
          icon: 'icon_list_delete',
          // permission: ["O_02_0548"],
          title: i18n.t('删除')
        }
      ]
      defaultCol.ignore = true
    }
    colData.push(defaultCol)
  })

  return colData
}

// 表格数据转换
export const serializeList = (list) => {
  if (list.length > 0) {
    let serialNumber = 1
    list.forEach((item) => {
      // 添加序号
      item.serialNumber = serialNumber++
    })
  }
  return list
}
