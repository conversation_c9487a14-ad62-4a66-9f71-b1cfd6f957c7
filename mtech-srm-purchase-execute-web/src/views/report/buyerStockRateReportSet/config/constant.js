import { i18n } from '@/main.js'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

import Vue from 'vue'
// Toolbar 按钮

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, isDateTime, isDate, isTime } = args

  const template = () => {
    return {
      template: Vue.component('timeDateComponent', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <div v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</div>
            <div v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</div>
          </div>
        </div>`,
        data: function () {
          return { data: {}, dataKey, isDateTime, isDate, isTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
export const Toolbar = [
  {
    id: 'ConfigAdd',
    icon: 'icon_solid_Createorder',
    // permission: ["O_02_0547"],
    title: i18n.t('新增')
  },
  {
    id: 'ConfigDelete',
    icon: 'icon_table_delete',
    // permission: ["O_02_0548"],
    title: i18n.t('删除')
  }
  // {
  //   id: "ConfigActive",
  //   icon: "icon_solid_Activateorder",
  //   // permission: ["O_02_0549"],
  //   title: i18n.t("启用"),
  // },
]

// 表格列数据
export const ColumnData = [
  {
    fieldCode: 'serialNumber', // 前端定义
    fieldName: i18n.t('序号'),
    width: '200'
  },
  {
    fieldCode: 'siteName',
    fieldName: i18n.t('工厂'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    },
    width: '300'
  },
  {
    fieldCode: 'configValue',
    fieldName: i18n.t('配置值（天）'),
    width: '300'
  },

  {
    fieldCode: 'validTimeStart',
    fieldName: i18n.t('有效开始时间'),
    width: '300',

    template: timeDate({ dataKey: 'validTimeStart', isDate: true })
  },
  {
    fieldCode: 'validTimeEnd',
    fieldName: i18n.t('有效结束时间'),
    width: '300',

    template: timeDate({ dataKey: 'validTimeEnd', isDate: true })
  }
]

// 弹出框类型
export const DialogActionType = {
  Add: 0 // 新增
  // Edit: 1, // 编辑
}
