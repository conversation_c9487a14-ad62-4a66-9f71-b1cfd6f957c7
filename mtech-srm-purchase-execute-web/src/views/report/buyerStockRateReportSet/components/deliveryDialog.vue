<template>
  <div>
    <mt-dialog
      ref="dialog"
      css-class="create-proj-dialog"
      :header="$t('操作')"
      :buttons="buttons"
      @close="handleClose"
      :open="onOpen"
    >
      <mt-form ref="addForm" :model="addForm" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="siteCode" :label="$t('工厂')">
          <debounce-filter-select
            v-model="addForm.siteCode"
            :request="postSiteFuzzyQuery"
            :data-source="siteOptions"
            :show-clear-button="true"
            :fields="{ text: 'theCodeName', value: 'siteCode' }"
            :value-template="siteCodeValueTemplate"
            @change="siteCodeChange"
            :placeholder="$t('请选择')"
          ></debounce-filter-select>
        </mt-form-item>
        <!-- <mt-form-item prop="status" :label="$t('状态')">
          <mt-select
            v-model="addForm.status"
            :data-source="statusOptions"
            :show-clear-button="true"
          ></mt-select>
        </mt-form-item> -->
        <mt-form-item prop="configValue" :label="$t('配置值（天）')">
          <mt-inputNumber v-model="addForm.configValue"></mt-inputNumber>
        </mt-form-item>
        <mt-form-item prop="validTimeStart" :label="$t('有效开始时间')">
          <mt-date-picker
            v-model="addForm.validTimeStart"
            :width="300"
            :min="maxDate"
            :placeholder="$t('选择日期')"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="validTimeEnd" :label="$t('有效结束时间')">
          <mt-date-picker
            v-model="addForm.validTimeEnd"
            :min="maxDate"
            :width="300"
            :placeholder="$t('选择日期')"
          ></mt-date-picker>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'

export default {
  components: {
    DebounceFilterSelect
  },
  props: {
    ids: {
      type: Array,
      default: () => {}
    },
    topInfo: {
      type: Array,
      default: () => {}
    }
  },
  data() {
    return {
      maxDate: new Date(),

      dialogTitle: '',
      locationOptions: [],
      logisticsCompanyOptions: [], // 物流公司
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],

      // statusOptions: [
      //   {
      //     value: 1,
      //     text: this.$t("启用"),
      //   },
      //   {
      //     value: 0,
      //     text: this.$t("停用"),
      //   },
      // ],
      addForm: {
        siteName: '',
        configValue: '',
        validTimeEnd: '',
        siteCode: '',
        validTimeStart: ''
      },
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      siteOptions: [],
      rules: {
        siteCode: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ],
        locationCode: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ],
        wmsUrl: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'blur'
          }
        ]
      },
      dimensionFields: { text: 'message', value: 'code' },
      dimensionList: [],
      editType: false,
      driverNameOptions: [] // 司机姓名下拉数据源
    }
  },
  mounted() {
    this.addForm.idList = this.ids
    this.$refs.dialog.ejsRef.show()
    if (this.topInfo.siteCode) {
      this.editType = true
      this.addForm = this.topInfo
      this.addForm.validTimeEnd = new Date(Number(this.topInfo.validTimeEnd))
      this.addForm.validTimeStart = new Date(Number(this.topInfo.validTimeStart))
      console.log(this.addForm)
    }
    this.addForm.siteCode
      ? this.postSiteFuzzyQuery({ text: this.addForm.siteCode })
      : this.postSiteFuzzyQuery({ text: undefined })
    // this.postSiteFuzzyQuery({ text: undefined });

    // this.$refs.ruleForm.resetFields();
  },

  methods: {
    // 切换方式
    selectChange(e) {
      console.log(e)
      this.$refs.addForm.resetFields()
      console.log(this.addForm)
    },
    postSiteFuzzyQuery(args) {
      const { text, updateData } = args
      //工厂下拉
      let obj = {
        buyerEnterpriseId: this.customerEnterpriseId,
        fuzzyParam: text
      }
      this.$API.masterData.postSiteFuzzyQuery(obj).then((res) => {
        if (res) {
          const list = res?.data || []
          this.siteOptions = addCodeNameKeyInList({
            firstKey: 'siteCode',
            secondKey: 'siteName',
            list
          })
          if (updateData && typeof updateData === 'function') {
            this.$nextTick(() => {
              updateData(this.siteOptions)
            })
          }
          // if (setSelectData) {
          //   this.$nextTick(() => {
          //     setSelectData();
          //   });
          // }
        }
      })
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    getDimension() {
      this.$API.moduleConfig.getDimension().then((res) => {
        console.log(res)
        this.dimensionList = res.data || []
      })
    },
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.addForm.siteId = itemData.id
        this.addForm.siteCode = itemData.siteCode
        this.addForm.siteName = itemData.siteName
      } else {
        this.formData.siteId = ''
        this.formData.siteCode = ''
        this.formData.siteName = ''
      }
    },

    confirm() {
      this.addForm.validTimeStart = Number(this.addForm.validTimeStart)
      this.addForm.validTimeEnd = Number(this.addForm.validTimeEnd)

      this.$refs.addForm.validate((valid) => {
        if (valid) {
          let params = this.addForm
          if (this.editType === true) {
            this.$API.report.stockRateConfigUpdate(params).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.$emit('handleAddDialogShow')
              }
            })
          } else {
            this.$API.report.stockRateConfigCreate(params).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.$emit('handleAddDialogShow')
              }
            })
          }
        }
      })
    },

    handleClose() {
      this.$emit('handleDialogShow', false)
    }
  }
}
</script>

<style></style>
