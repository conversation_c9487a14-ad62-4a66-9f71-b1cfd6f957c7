import { i18n } from '@/main.js'
import dayjs from 'dayjs'

export const statusOptions = [
  { text: i18n.t('已转交'), value: 2 },
  { text: i18n.t('待反馈'), value: 3 },
  { text: i18n.t('反馈异常'), value: 4 },
  { text: i18n.t('反馈正常'), value: 5 },
  { text: i18n.t('已关闭'), value: 6 }
]

export const operatorOptions = [
  { text: '=', value: 'equal' },
  { text: '≠', value: 'notequal' },
  { text: '>', value: 'greaterthan' },
  { text: '≥', value: 'greaterthanorequal' },
  { text: '<', value: 'lessthan' },
  { text: '≤', value: 'lessthanorequal' }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'rowCode',
    title: i18n.t('JIT行编号')
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    minWidth: 65,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂'),
    minWidth: 65,
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.siteName
    }
  },
  {
    field: 'subSiteCode',
    title: i18n.t('分厂'),
    minWidth: 65,
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.subSiteName
    }
  },
  {
    field: 'workOrder',
    title: i18n.t('生产工单')
  },
  {
    field: 'productLine',
    title: i18n.t('生产线'),
    minWidth: 75
  },
  {
    field: 'warehouseCode',
    title: i18n.t('库存地点'),
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.warehouseName
    }
  },
  {
    field: 'subSiteAddressCode',
    title: i18n.t('分厂库存地点'),
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.subSiteAddress
    }
  },
  {
    field: 'qteReservationNum',
    title: i18n.t('已报到数量')
  },
  {
    field: 'qteUnloadNum',
    title: i18n.t('已到厂待卸货数量')
  },
  {
    field: 'qteReviewNum',
    title: i18n.t('已卸货待报检数量')
  },
  {
    field: 'deliveryDate',
    title: i18n.t('交货日期'),
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    title: i18n.t('物料描述')
  },
  {
    field: 'buyerOrgCode',
    title: i18n.t('采购组'),
    minWidth: 75,
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.buyerOrgName
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 75,
    formatter: ({ cellValue, row }) => {
      return cellValue + '-' + row.supplierName
    }
  },
  {
    field: 'bidNum',
    title: i18n.t('需求数量')
  },
  {
    field: 'receiveNum',
    title: i18n.t('收货数量')
  },
  {
    field: 'deliveryNum',
    title: i18n.t('在途数量')
  },
  {
    field: 'remainingDeliveryNum',
    title: i18n.t('剩余送货数量')
  },
  {
    field: 'remarkExplain',
    title: i18n.t('大数据平台备注')
  },
  {
    field: 'origSupplierCode',
    title: i18n.t('上版供应商编码')
  },
  {
    field: 'origSupplierName',
    title: i18n.t('上版供应商名称')
  },
  {
    field: 'planDeliveryDate',
    title: i18n.t('上版交货日期'),
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'planDeliveryTime',
    title: i18n.t('上版交货时间'),
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'callMaterialQty',
    title: i18n.t('上版叫料数量')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  }
]
