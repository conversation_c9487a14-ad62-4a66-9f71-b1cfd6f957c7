<!-- 采方-叫料计划送货跟踪报表 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('JIT行编号')" prop="rowCode">
          <mt-input
            v-model="searchFormModel.rowCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('状态')" prop="statusList">
          <mt-multi-select
            v-model="searchFormModel.statusList"
            :data-source="statusOptions"
            :show-select-all="true"
            :show-clear-button="false"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('工厂')" prop="siteCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.siteCodeList"
            :url="$API.masterData.getSiteListUrl"
            :multiple="true"
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('分厂')" prop="subSiteCode">
          <RemoteAutocomplete
            v-model="searchFormModel.subSiteCodes"
            url="/srm-purchase-execute/tenant/buyerJitInfo/condition?conditionType=subSiteCode"
            :multiple="true"
            :placeholder="$t('请选择')"
            :fields="{ text: 'subSiteName', value: 'subSiteCode' }"
            :search-fields="['subSiteCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('生产工单')" prop="workOrder">
          <mt-input
            v-model="searchFormModel.workOrder"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('生产线')" prop="productLine">
          <mt-input
            v-model="searchFormModel.productLine"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('库存地点')" prop="warehouseNameList">
          <RemoteAutocomplete
            v-model="searchFormModel.warehouseNameList"
            url="/masterDataManagement/tenant/location/paged-query"
            :multiple="true"
            :placeholder="$t('请选择')"
            :fields="{ text: 'locationCode', value: 'locationName' }"
            :search-fields="['locationName', 'locationCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('分厂库存地点')" prop="subSiteAddressCode">
          <RemoteAutocomplete
            v-model="searchFormModel.subSiteAddressCode"
            url="/srm-purchase-execute/tenant/buyerJitInfo/condition?conditionType=subSiteAddressCode"
            :multiple="false"
            :placeholder="$t('请选择')"
            :fields="{ text: 'subSiteAddress', value: 'subSiteAddressCode' }"
            :search-fields="['subSiteAddress']"
          />
        </mt-form-item>
        <mt-form-item prop="deliveryDate" :label="$t('交货日期')">
          <mt-date-range-picker
            v-model="searchFormModel.deliveryDate"
            @change="(e) => deliveryDateChange(e)"
            :placeholder="$t('请选择交货日期')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料编码')" prop="itemCode">
          <mt-input
            v-model="searchFormModel.itemCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('采购组')" prop="buyerOrgCode">
          <RemoteAutocomplete
            v-model="searchFormModel.buyerOrgCodes"
            :url="$API.masterData.getBusinessGroupUrl"
            :multiple="true"
            :placeholder="$t('请选择')"
            :fields="{ text: 'groupName', value: 'groupCode' }"
            :search-fields="['groupName', 'groupCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供应商')" prop="supplierCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCodeList"
            url="/masterDataManagement/tenant/supplier/paged-query"
            :multiple="true"
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('剩余送货数量')" prop="remainingDeliveryNum">
          <div style="display: flex">
            <div style="width: 50px">
              <mt-select
                css-class="rule-element"
                v-model="searchFormModel.operator"
                :data-source="operatorOptions"
                popup-width="50px"
              />
            </div>
            <mt-input
              type="number"
              v-model="remainingDeliveryNum"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </div>
        </mt-form-item>
        <mt-form-item :label="$t('在途数量')" prop="deliveryNum">
          <div style="display: flex">
            <div style="width: 50px">
              <mt-select
                css-class="rule-element"
                v-model="searchFormModel.deliveryNumOperator"
                :data-source="operatorOptions"
                popup-width="50px"
              />
            </div>
            <mt-input
              type="number"
              v-model="deliveryNum"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </div>
        </mt-form-item>
        <mt-form-item :label="$t('创建时间')" prop="createTime">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            @change="(e) => createTimeChange(e)"
            :placeholder="$t('请选择创建时间')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('创建人')" prop="createUserName">
          <mt-input
            v-model="searchFormModel.createUserName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="b9fd9074-26c3-4829-a576-c6c679ffac21"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      show-header-overflow
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, statusOptions, operatorOptions } from './config'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  components: { CollapseSearch, ScTable },
  data() {
    return {
      searchFormModel: {
        operator: 'equal',
        deliveryNumOperator: 'equal'
      },
      searchFormRules: {
        deliveryDate: [
          {
            required: true,
            message: this.$t('请选择交货日期'),
            trigger: 'blur'
          }
        ]
      },
      toolbar: [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [50, 200, 500, 1000, 10000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      statusOptions,
      operatorOptions,
      remainingDeliveryNum: null,
      deliveryNum: null
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.setDefaultDate()
  },
  methods: {
    setDefaultDate() {
      const startDate = dayjs().subtract(2, 'day')
      const endDate = dayjs().add(2, 'day')
      this.searchFormModel['deliveryDateFrom'] = this.getUnix(
        startDate.format('YYYY-MM-DD 00:00:00')
      )
      this.searchFormModel['deliveryDateTo'] = this.getUnix(endDate.format('YYYY-MM-DD 23:59:59'))
      this.searchFormModel.deliveryDate = [startDate.$d, endDate.$d]
    },
    deliveryDateChange(e) {
      if (e.startDate) {
        this.searchFormModel['deliveryDateFrom'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel['deliveryDateTo'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel['deliveryDateFrom'] = null
        this.searchFormModel['deliveryDateTo'] = null
      }
    },
    planDeliveryDateChange(e) {
      if (e.startDate) {
        this.searchFormModel['planDeliveryDateFrom'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel['planDeliveryDateTo'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel['planDeliveryDateFrom'] = null
        this.searchFormModel['planDeliveryDateTo'] = null
      }
    },
    createTimeChange(e) {
      if (e.startDate) {
        this.searchFormModel['createStartDate'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel['createEndDate'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel['createStartDate'] = null
        this.searchFormModel['createEndDate'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.remainingDeliveryNum = null
      this.searchFormModel.operator = 'equal'
      this.searchFormModel.deliveryNumOperator = 'equal'
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      if (this.remainingDeliveryNum !== null && this.remainingDeliveryNum !== '') {
        params.remainingDeliveryNum = {
          operator: params.operator,
          number: Number(this.remainingDeliveryNum)
        }
      } else {
        delete params.remainingDeliveryNum
      }
      if (this.deliveryNum !== null && this.deliveryNum !== '') {
        params.deliveryNum = {
          operator: params.deliveryNumOperator,
          number: Number(this.deliveryNum)
        }
      } else {
        delete params.deliveryNum
      }
      delete params.deliveryDate
      delete params.planDeliveryDate
      delete params.operator
      delete params.deliveryNumOperator
      if (params.deliveryDateFrom !== null) {
        this.loading = true
        const res = await this.$API.report
          .pagePurchaseJitReportApi(params)
          .catch(() => (this.loading = false))
        this.loading = false
        if (res.code === 200) {
          const total = res?.data?.total || 0
          this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
          this.pageSettings.totalRecordsCount = Number(total)

          const records = res.data?.records || []
          this.tableData = records
        }
      } else {
        this.$toast({ content: this.$t('请选择交货日期'), type: 'warning' })
      }
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.report
        .exportPurchaseJitReportApi(params)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>
