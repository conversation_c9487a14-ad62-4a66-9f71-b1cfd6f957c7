<template>
  <div class="full-height pt20 vertical-flex-box">
    <div class="flex-keep">
      <div class="header-box">
        <div class="main-bottom">
          <mt-form ref="ruleForm" :model="forecastTemplate" :rules="rules">
            <!-- <mt-form-item prop="companyCode" :label="$t('公司')">
              <mt-select
                class="forecast-template-select"
                v-model="forecastTemplate.companyCode"
                :allow-filtering="true"
                :filtering="getSourceCompany"
                :data-source="buyerList"
                :fields="{ text: 'label', value: 'orgCode' }"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item> -->
            <mt-form-item prop="siteCode" :label="$t('工厂')">
              <!-- <mt-select
                class="forecast-template-select"
                v-model="forecastTemplate.siteCode"
                :allow-filtering="true"
                :filtering="getSiteList"
                :data-source="factorySelect"
                :fields="{ text: 'label', value: 'siteCode' }"
                :placeholder="$t('请选择')"
              ></mt-select> -->
              <RemoteAutocomplete
                style="flex: 1"
                v-model="forecastTemplate.siteCode"
                :url="$API.masterData.getSiteListUrl"
                multiple
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                :search-fields="['siteName', 'siteCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="groupCode" :label="$t('采购组')">
              <!-- <mt-select
                class="forecast-template-select"
                v-model="forecastTemplate.groupCode"
                :allow-filtering="true"
                :data-source="groupList"
                :fields="{ text: 'label', value: 'groupCode' }"
                :placeholder="$t('请选择')"
              ></mt-select> -->
              <RemoteAutocomplete
                style="flex: 1"
                v-model="forecastTemplate.groupCode"
                :url="$API.masterData.getBusinessGroupAuthFuzzyUrl"
                multiple
                :placeholder="$t('请选择采购组')"
                :fields="{ text: 'groupName', value: 'groupCode' }"
                records-position="data"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商')">
              <!-- <mt-select
                class="forecast-template-select"
                :allow-filtering="true"
                v-model="forecastTemplate.supplierCode"
                :data-source="supplierList"
                :fields="{ text: 'label', value: 'supplierCode' }"
                :placeholder="$t('请选择')"
              ></mt-select> -->
              <RemoteAutocomplete
                style="flex: 1"
                v-model="forecastTemplate.supplierCode"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择供应商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="itemCode" :label="$t('物料')">
              <!-- <mt-select
                :allow-filtering="true"
                class="forecast-template-select"
                v-model="forecastTemplate.itemCode"
                :data-source="itemList"
                :fields="{ text: 'itemName', value: 'itemCode' }"
                :placeholder="$t('请选择')"
              ></mt-select> -->
              <RemoteAutocomplete
                style="flex: 1"
                v-model="forecastTemplate.itemCode"
                multiple
                :url="$API.masterData.getItemUrl"
                :placeholder="$t('请选择物料')"
                :fields="{ text: 'itemName', value: 'itemCode' }"
                :search-fields="['itemName', 'itemCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="confirmDate" :label="$t('订单下单日期')">
              <mt-date-range-picker
                v-model="forecastTemplate.confirmDate"
                operator="in"
                :placeholder="$t('选择日期')"
                :show-clear-button="false"
                :allow-edit="false"
                :disabled="false"
              ></mt-date-range-picker>
            </mt-form-item>
            <mt-form-item prop="externalItemGroupCode" :label="$t('品类（外部物料组）')" class="">
              <debounce-filter-select
                v-model="forecastTemplate.externalItemGroupCode"
                :request="getItemGroupList"
                :data-source="ItemGroupOptions"
                :show-clear-button="true"
                :fields="{ text: 'theCodeName', value: 'categoryCode' }"
                :value-template="ItemGroupTemplate"
                @change="ItemGroupChange"
                :placeholder="$t('请选择')"
              ></debounce-filter-select>
            </mt-form-item>
            <mt-button css-class="e-flat" @click="see" :is-primary="true">{{
              $t('查询')
            }}</mt-button>
          </mt-form>
        </div>
      </div>
    </div>

    <mt-template-page
      slot="slot-0"
      @handleClickToolBar="handleClickToolBar"
      ref="templateRef"
      :template-config="pageConfig1"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { orderRateMonth } from './config/index.js'
import { download, getHeadersFileName } from '@/utils/utils'
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'

export default {
  components: {
    DebounceFilterSelect
  },
  mounted() {
    this.init()
  },

  data() {
    return {
      id: [],
      editData: [],
      forecastTemplate: {
        confirmDate: '',
        itemCode: '',
        supplierCode: '',
        companyCode: '',
        groupCode: '',
        siteCode: '',
        orgCode: ''
      },
      rules: {
        itemCode: [{ required: false, message: this.$t('请选择'), trigger: 'blur' }],
        siteCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        confirmDate: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      deliveryShow: false,
      // permissionObj: {
      //   permissionNode: {
      //     // 当前的dom元素
      //     code: "ignore-element",
      //     type: "remove",
      //   },
      //   childNode: [
      //     { dataPermission: "a", permissionCode: "T_02_0120" },
      //     { dataPermission: "b", permissionCode: "T_02_0121" },
      //   ],
      // },

      itemList: [],
      supplierList: [],
      buyerList: [],
      factorySelect: [],
      userInfo: null,
      addDialogShow: false,
      currentTabIndex: 0,
      groupList: [],
      ItemGroupOptions: [],
      ItemGroupTemplate: codeNameColumn({
        firstKey: 'categoryCode',
        secondKey: 'categoryName'
      }), // 品类
      pageConfig1: [
        {
          useToolTemplate: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'export',
                  icon: 'icon_solid_Import',
                  // permission: ["O_02_1081"],
                  title: this.$t('导出')
                }
              ],
              []
            ]
          },
          gridId: '3ca20d5e-9965-42ec-a588-0c74ee061b5a',

          grid: {
            pageSettings: {
              currentPage: 1,
              pageSize: 200,
              pageSizes: [50, 100, 200, 500, 1000]
            },
            columnData: orderRateMonth,
            // lineIndex: 1,

            autoWidthColumns: orderRateMonth.length + 1,
            dataSource: [],
            asyncConfig: {
              url: `/statistics/tenant/buyerOrderRate/queryGroupMonth`,
              rules: [],
              defaultRules: [],
              serializeList: (list) => {
                if (list.length > 0) {
                  let serialNumber = 1
                  list.forEach((item) => {
                    // 添加序号
                    item.serialNumber = serialNumber++
                  })
                }
                return list
              }
            }
          }
        }
      ],

      dialogData: null
    }
  },
  methods: {
    getItemGroupList(args) {
      const { text } = args
      const params = {
        fuzzyNameOrCode: text
        // groupTypeCode: "BG001CG",
      }
      this.$API.masterData
        .getCategoryfuzzy(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.ItemGroupOptions = addCodeNameKeyInList({
              firstKey: 'categoryCode',
              secondKey: 'categoryName',
              list
            })
            // if (updateData && typeof updateData === "function") {
            //   this.$nextTick(() => {
            //     updateData(this.buyerOrgOptions);
            //   });
            // }
            // if (setSelectData) {
            //   this.$nextTick(() => {
            //     setSelectData();
            //   });
            // }
          }
        })
        .catch(() => {})
    },
    ItemGroupChange(e) {
      const { itemData } = e
      console.log('按时发散发送-----', e)
      // this.formData.externalItemGroupId = itemData.id;
      this.forecastTemplate.externalItemGroupCode = itemData.categoryCode
      // this.formData.externalItemGroupName = itemData.categoryName;
    },
    see() {
      console.log(this.forecastTemplate)
      this.pageConfig1[0].grid.asyncConfig.defaultRules = []

      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.forecastTemplate.itemCode !== '' && this.forecastTemplate.itemCode.length > 0
            ? this.pageConfig1[0].grid.asyncConfig.defaultRules.push({
                field: 'itemCode',
                operator: 'in',
                value: this.forecastTemplate.itemCode
              })
            : ''
          this.forecastTemplate.supplierCode !== '' && this.forecastTemplate.supplierCode.length > 0
            ? this.pageConfig1[0].grid.asyncConfig.defaultRules.push({
                field: 'supplierCode',
                operator: 'in',
                value: this.forecastTemplate.supplierCode
              })
            : ''
          this.forecastTemplate.siteCode !== '' && this.forecastTemplate.siteCode.length > 0
            ? this.pageConfig1[0].grid.asyncConfig.defaultRules.push({
                field: 'siteCode',
                operator: 'in',
                value: this.forecastTemplate.siteCode
              })
            : ''
          this.forecastTemplate.groupCode !== '' && this.forecastTemplate.groupCode.length > 0
            ? this.pageConfig1[0].grid.asyncConfig.defaultRules.push({
                field: 'buyerOrgCode',
                operator: 'in',
                value: this.forecastTemplate.groupCode
              })
            : ''
          this.forecastTemplate.companyCode !== '' && this.forecastTemplate.companyCode.length > 0
            ? this.pageConfig1[0].grid.asyncConfig.defaultRules.push({
                field: 'companyCode',
                operator: 'in',
                value: this.forecastTemplate.companyCode
              })
            : ''
          this.forecastTemplate.confirmDate !== ''
            ? this.pageConfig1[0].grid.asyncConfig.defaultRules.push({
                field: 'orderTime',
                operator: 'between',
                value: this.formatDateRange(this.forecastTemplate.confirmDate)
              })
            : ''
          this.forecastTemplate.externalItemGroupCode
            ? this.pageConfig1[0].grid.asyncConfig.defaultRules.push({
                field: 'categoryCode',
                operator: 'equal',
                value: this.forecastTemplate.externalItemGroupCode
              })
            : ''
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    formatDateRange(data) {
      console.log(data)

      // data.forEach((item) => {
      //   item = item.valueOf();
      // });
      data[0] = Number(data[0])
      data[1] = Number(data[1])
      return data
    },
    init() {
      let obj = {
        organizationLevelCodes: ['ORG01', 'ORG02'],
        page: { current: 1, pages: 1, size: 20 },
        fuzzyParam: ''
      }
      this.$API.masterData.getDictSpecified(obj).then((res) => {
        res.data.records.forEach((item) => {
          item.label = item.orgCode + '-' + item.orgName
        })
        this.buyerList = res.data.records
      })
      this.$API.masterData.getSiteList().then((res) => {
        const list = res?.data.records || []
        this.factorySelect = [...list]
      })
      let groupObj = {
        page: {
          current: 1,
          pages: 12407,
          size: 20
        }
      }
      this.$API.masterData.getGroup(groupObj).then((res) => {
        const list = res?.data.records || []
        this.groupList = [...list]
      })
      let supplierObj = {
        page: {
          current: 1,
          pages: 6,
          size: 20
        }
      }
      this.$API.masterData.supplierPagedQuery(supplierObj).then((res) => {
        const list = res?.data.records || []
        this.supplierList = [...list]
      })
      let itemObj = {
        page: {
          current: 1,
          pages: 6,
          size: 20
        }
      }
      this.$API.masterData.getItemPage(itemObj).then((res) => {
        const list = res?.data.records || []
        this.itemList = [...list]
      })
    },
    handleClickToolBar(args) {
      if (args.toolbar.id == 'export') {
        // const queryBuilderRules =
        //   this.$refs.templateRef.getCurrentUsefulRef().pluginRef
        //     .queryBuilderRules || {};
        // console.log(queryBuilderRules);
        if (this.pageConfig1[0].grid.asyncConfig.defaultRules.length > 0) {
          const params = {
            page: { current: 1, size: 1000 },
            defaultRules: this.pageConfig1[0].grid.asyncConfig.defaultRules
          } // 筛选条件
          this.$store.commit('startLoading')
          this.$API.report.buyerOrderRateMonthReport(params).then((res) => {
            this.$store.commit('endLoading')
            const fileName = getHeadersFileName(res)
            download({ fileName: `${fileName}`, blob: res.data })
          })
          return
        } else {
          this.$toast({
            content: this.$t('查询条件必填项必须选择'),
            type: 'warning'
          })
        }
      }
    }
  }
  // 消失
  // beforeDestroy() {
  //   localStorage.removeItem("deliverDetailSupplier");
  // },
}
</script>

<style lang="scss" scoped>
.forecast-template {
  width: 250px;
  padding-bottom: 8px;
  .forecast-template-select {
    width: calc(250px - 70px);
  }
}
.full-height .flex-keep {
  display: flex;
}
.main-bottom {
  width: 100%;
  /deep/ .mt-form-item {
    width: calc(13% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
    .mt-form-item-topLabel {
      flex: 1;
      width: 100%;
    }
    // &.more-width {
    //   // width: 450px;
    // }

    .full-width {
      width: calc(100% - 20px) !important;
    }
  }
  /deep/ .e-flat {
    position: relative;
    top: 21px;
    background: #19a2d5;
    color: white;
    border-radius: 5px;
  }
}
.header-box {
  width: 100%;
  padding-right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .itemcon {
    display: flex;
    align-items: center;
  }
  .item {
    margin-right: 20px;
  }
  .middle-blank {
    flex: 1;
  }
  .status {
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(99, 134, 193, 1);
    padding: 4px;
    background: rgba(238, 242, 249, 1);
    border-radius: 2px;
  }

  .infos {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
  }

  .sort-box {
    position: relative;
    cursor: pointer;
    margin-right: 20px;
    .mt-icons {
      font-size: 12px;
      transform: scale(0.5);
      color: rgba(0, 70, 156, 1);
      margin-top: -10px;
      position: absolute;
      top: 0;

      &:nth-child(2) {
        top: 6px;
      }
    }
  }
}
</style>
