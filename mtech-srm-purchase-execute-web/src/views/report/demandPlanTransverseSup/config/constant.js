import { i18n } from '@/main.js'
export const ConstDynamicTitleStr = 'title_' // 动态表格数据的 key 标识

// 业务组类型数字字典编码
export const BusinessGroupTypeDictCode = 'BUSINESSGROUP'

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  status: 'draft', // 状态 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
  categoryCode: '',
  categoryName: '',
  createUserName: '',
  createTime: '',
  updateUserName: '',
  updateTime: ''
}

export const ToolBar = [
  {
    code: 'ForecastExport',
    name: i18n.t('导出'),
    status: 'info'
  }
]

// 配送方式
export const deliveryMethodOptions = [
  { label: i18n.t('直送'), value: '0' },
  { label: i18n.t('非直送'), value: '1' }
]

// 委外方式
export const outsourcedTypeOptions = [
  { label: i18n.t('标准委外'), value: '0' },
  { label: i18n.t('销售委外'), value: '1' },
  { label: i18n.t('标准采购'), value: '2' },
  { label: i18n.t('工序委外'), value: '3' }
]

export const isJitOptions = [
  { label: i18n.t('否'), value: 0 },
  { label: i18n.t('是'), value: 1 }
]

// 选择 物料 弹框 表格列数据
export const MaterielTableColumnData = [
  {
    fieldCode: 'itemCode', // 物料编号
    fieldName: i18n.t('物料编号')
  },
  {
    fieldCode: 'itemName', // 物料名称
    fieldName: i18n.t('物料名称')
  },
  {
    fieldCode: 'categoryResponse.categoryCode', // 品类
    fieldName: i18n.t('品类')
  },
  {
    fieldCode: 'itemDescription', // 规格型号
    fieldName: i18n.t('规格型号')
  },
  {
    fieldCode: 'oldItemCode', // 旧物料编号
    fieldName: i18n.t('旧物料编号')
  },
  {
    fieldCode: 'manufacturerName', // 制造商
    fieldName: i18n.t('制造商')
  }
]
