<!-- 供方 - 交货计划横向报表 -->
<template>
  <div>
    <mt-tabs
      :e-tab="false"
      :data-source="tabList"
      :selected-item="tabIndex"
      @handleSelectTab="handleSelectTab"
    />
    <div>
      <List v-if="tabIndex === 0" />
      <History v-if="tabIndex === 1" />
    </div>
  </div>
</template>

<script>
export default {
  components: {
    List: () => import('./pages/List.vue'),
    History: () => import('./pages/History.vue')
  },
  data() {
    return {
      tabList: [{ title: this.$t('列表') }, { title: this.$t('历史版本') }],
      tabIndex: 0
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    }
  }
}
</script>
