<template>
  <!-- 历史版本 - 供方 -->
  <div class="full-height vertical-flex-box">
    <collapse-search
      :is-grid-display="true"
      @reset="handleCustomReset"
      @search="handleCustomSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <!-- 工厂编码 -->
        <mt-form-item prop="siteCodeList" :label="$t('工厂')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.siteCodeList"
            :data-source="siteCodeListOptions"
            :allow-filtering="true"
            :multiple="true"
            filter-type="Contains"
            :filtering="getSiteCodeList"
            :fields="{ text: 'theCodeName', value: 'siteCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 物料编码 -->
        <mt-form-item prop="itemCode" :label="$t('物料')" label-style="top">
          <mt-select
            style="flex: 1"
            v-model="searchFormModel.itemCode"
            :data-source="itemCodeOptions"
            :allow-filtering="true"
            filter-type="Contains"
            :filtering="getItemCodeList"
            :fields="{ text: 'theCodeName', value: 'itemCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 采购组 -->
        <mt-form-item prop="buyerOrgCode" :label="$t('采购组')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.buyerOrgCodeList"
            :data-source="buyerOrgCodeOptions"
            :allow-filtering="true"
            filter-type="Contains"
            :multiple="true"
            :filtering="getBuyerOrgCodeList"
            :fields="{ text: 'theCodeName', value: 'groupCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 是否JIT -->
        <mt-form-item prop="jit" :label="$t('是否jit')" label-style="top">
          <mt-select
            style="flex: 1"
            v-model="searchFormModel.jit"
            :data-source="isJitOptions"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 委外方式 -->
        <mt-form-item prop="outsourcedType" :label="$t('委外方式')" label-style="top">
          <mt-select
            style="flex: 1"
            v-model="searchFormModel.outsourcedType"
            :data-source="outsourcedTypeOptions"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 配送方式 -->
        <mt-form-item prop="deliveryMethod" :label="$t('配送方式')" label-style="top">
          <mt-select
            style="flex: 1"
            v-model="searchFormModel.deliveryMethod"
            :data-source="deliveryMethodOptions"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 加工商 -->
        <mt-form-item prop="processorCode" :label="$t('加工商')" label-style="top">
          <mt-select
            style="flex: 1"
            v-model="searchFormModel.processorCode"
            :data-source="processorCodeList"
            :allow-filtering="true"
            filter-type="Contains"
            :filtering="getProcessorCodeList"
            :fields="{ text: 'theCodeName', value: 'supplierCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 需求日期 -->
        <mt-form-item prop="" :label="$t('需求日期')">
          <mt-date-range-picker
            v-model="searchFormModel.requireDate"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateChange(e, 'require', 'Date')"
          />
        </mt-form-item>
        <!-- 版本 -->
        <mt-form-item prop="" :label="$t('版本')">
          <mt-date-picker
            v-model="searchFormModel.createDate"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        class="xTable-class"
        :row-config="{ height: 130 }"
        :columns="columns"
        :table-data="tableData"
        show-overflow
        :min-height="600"
        border="none"
        header-align="left"
        row-class-name="table-row-class"
        align="center"
        style="padding-top: unset"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #checkTemplate="{ column, row }">
          <span class="span-style" v-if="typeof row[column.field] === 'object'">
            <span>{{ row[column.field]['systemBuyerNum'] }}</span>
            <span>{{ row[column.field]['handleBuyerNum'] }}</span>
          </span>
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { addCodeNameKeyInList } from '@/utils/utils'
import { utils } from '@mtech-common/utils'
import dayjs from 'dayjs'
import {
  deliveryMethodOptions,
  ToolBar,
  outsourcedTypeOptions,
  isJitOptions
} from '../config/constant'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      toolbar: ToolBar,
      // 配送方式
      deliveryMethodOptions,
      // 委外方式
      outsourcedTypeOptions,
      // 是否Jit
      isJitOptions,
      siteCodeListOptions: [], // 工厂下拉
      itemCodeOptions: [], // 物料下拉
      buyerOrgCodeOptions: [], // 采购组下拉
      supplierTypeList: [], // 供应商下拉
      processorCodeList: [], // 加工商下拉
      apiWaitingQuantity: 0, // 调用的api正在等待数
      searchFormModel: {
        siteCodeList: [],
        itemCode: '',
        buyerOrgCodeList: [],
        jit: '',
        outsourcedType: '',
        deliveryMethod: '',
        processorCode: '',
        requireStartDate: null,
        requireEndDate: null
      },
      forecastPageCurrent: 1,
      tableData: [],
      columns: [],
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      }
    }
  },
  mounted() {
    // 获取工厂，物料，供应商信息
    this.getSiteCodeList = utils.debounce(this.getSiteCodeList, 1000)
    this.getItemCodeList = utils.debounce(this.getItemCodeList, 1000)
    this.getBuyerOrgCodeList = utils.debounce(this.getBuyerOrgCodeList, 1000)
    this.getSupplierList = utils.debounce(this.getSupplierList, 1000)
    this.getProcessorCodeList = utils.debounce(this.getProcessorCodeList, 1000)
    this.getSiteCodeList()
    this.getItemCodeList()
    this.getBuyerOrgCodeList()
    this.getSupplierList()
    this.getProcessorCodeList()
    this.getTableList()
  },
  methods: {
    // 获取工厂信息
    getSiteCodeList(args = { text: '' }) {
      const { text } = args
      const params = {
        fuzzyParam: text
      }
      this.$API.report
        .getSiteCodeList(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteCodeListOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
          }
        })
        .catch(() => {})
    },
    getItemCodeList(e = { text: '' }) {
      const { text } = e
      //物料下拉
      let params = {
        keyword: text || '',
        pageSize: 50
      }
      this.$API.masterData.getItemByKeyword(params).then((res) => {
        const list = res.data?.records || []
        this.itemCodeOptions = addCodeNameKeyInList({
          firstKey: 'itemCode',
          secondKey: 'itemName',
          list
        })
      })
    },
    // 采购组下拉
    getBuyerOrgCodeList(args = { text: '' }) {
      const { text } = args
      const param = {
        fuzzyParam: text
      }
      this.$API.report
        .getBuyerOrgCodeList(param)
        .then((res) => {
          if (res && res.code === 200) {
            let list = res.data || []
            this.buyerOrgCodeOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            }).filter((n) => !!n.theCodeName)
          }
        })
        .catch(() => {})
    },
    // 获取供应商信息
    getSupplierList(args = { text: '' }) {
      const { text } = args
      const param = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData.getSupplier(param).then((res) => {
        this.supplierTypeList.length = 0
        const list = res.data || []
        this.supplierTypeList = addCodeNameKeyInList({
          firstKey: 'supplierCode',
          secondKey: 'supplierName',
          list
        }).filter((n) => !!n.theCodeName && !!n.supplierCode)
      })
    },
    // 加工商下拉
    getProcessorCodeList(args = { text: '' }) {
      const { text } = args
      const param = {
        fuzzyNameOrCode: text
      }
      this.$API.report.getSupplierList(param).then((res) => {
        this.processorCodeList.length = 0
        const list = res.data || []
        this.processorCodeList = addCodeNameKeyInList({
          firstKey: 'supplierCode',
          secondKey: 'supplierName',
          list
        }).filter((n) => !!n.theCodeName && !!n.supplierCode)
      })
    },
    // 需求日期更改
    dateChange(e, flag, flag2) {
      if (e && e.startDate) {
        this.searchFormModel[flag + 'Start' + flag2] = dayjs(e.startDate).valueOf()
        this.searchFormModel[flag + 'End' + flag2] = dayjs(e.endDate).valueOf()
      } else {
        this.searchFormModel[flag + 'Start' + flag2] = null
        this.searchFormModel[flag + 'End' + flag2] = null
      }
    },
    // 获取列表表头
    getTableList(createDate) {
      let params = {
        createDate
      }
      this.$API.report.getHistoryColumnListApi(params).then((res) => {
        const _list = res.data
        const _columnList = []
        if (_list && _list?.length > 0) {
          _list.forEach((item, index) => {
            if (item.field) {
              const obj = {
                field: item.field,
                title: item.fieldName,
                align: 'center',
                showOverflow: true,
                width:
                  index < 6 || index === 9 ? (item.fieldName.includes('名称') ? 250 : 160) : 120
              }
              if (index > 11) {
                obj['slots'] = {
                  default: 'checkTemplate'
                }
              }
              _columnList.push(obj)
            }
          })
        }
        this.columns = _columnList
      })
    },

    handleClickToolBar(args) {
      const { code } = args
      if (code === 'ForecastExport') {
        this.handleExport()
        return
      }
    },
    handleExport() {
      const params = {
        ...this.searchFormModel
      }
      params.createDate = params.createDate?.valueOf()
      if (params.requireDate) {
        delete params.requireDate
      }
      this.$store.commit('startLoading')
      this.$API.report.historyExportDemandPlanTransverseInfoSupApi(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'bigVersionNo') {
            this.searchFormModel[key] = this.versionList[0]
          }
        }
      }
      this.handleCustomSearch()
    },
    // 采方-获取信息列表
    handleCustomSearch() {
      let createDate = this.searchFormModel?.createDate.valueOf()
      this.getTableList(createDate)
      const params = {
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        },
        ...this.searchFormModel
      }
      params.createDate = params.createDate?.valueOf()
      if (params.requireDate) {
        delete params.requireDate
      }
      this.apiStartLoading()
      this.$API.report
        .historyQueryDemandPlanTransverseListSupApi(params)
        .then((res) => {
          if (res?.code == 200) {
            const total = res?.data?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.totalRecordsCount = Number(total)
            const records = res?.data?.records || [] // 表格数据
            records.map((item) => {
              if (item.dataItemList && item.dataItemList.length > 0) {
                item.dataItemList.forEach((n) => {
                  item[n.title] = {
                    systemBuyerNum: n.systemBuyerNum,
                    handleBuyerNum: n.handleBuyerNum
                  }
                })
              }
            })
            // 处理表数据
            this.tableData = records
          }
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-cell {
  &.c--tooltip {
    padding: 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  & .span-style {
    display: flex;
    flex-flow: column;
    & span:nth-child(1) {
      border-bottom: 1px solid #cfd4d9;
    }
  }
}
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
::v-deep .xTable-class .vxe-table .table-row-class td {
  height: 40px !important;
}
</style>
