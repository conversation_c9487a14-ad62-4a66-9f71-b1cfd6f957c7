<template>
  <div class="fields-config-page mt-flex-direction-column">
    <div class="top-info mt-flex-direction-column">
      <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
    </div>
    <div class="config-container">
      <predictlist v-show="tabTitle == $t('最新版本预测')" />
      <predicthistory v-show="tabTitle == $t('历史版本预测')" />
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
const purTabList = [
  {
    title: i18n.t('最新版本预测')
  },
  {
    title: i18n.t('历史版本预测')
  }
]
export default {
  components: {
    predictlist: require('./components/list.vue').default,
    predicthistory: require('./components/history.vue').default
  },
  data() {
    return {
      tabIndex: 0,
      tabTitle: this.$t('最新版本预测'),
      tabSource: purTabList
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
      this.tabTitle = this.tabSource[e]['title']
    }
  }
}
</script>

<style></style>
