<template>
  <!-- 采方-对账协同-采购对账单列表 -->
  <div class="full-height">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      @reset="handleCustomReset"
      @search="handleCustomSearch"
    >
      <mt-form
        ref="searchFormRef"
        :model="searchFormModel"
        :rules="{
          syncVersionList: [{ required: true, message: $t('请输入'), trigger: 'blur' }],
          planners: [{ required: true, message: $t('请输入'), trigger: 'blur' }]
        }"
      >
        <mt-form-item prop="syncVersionList" :label="$t('版本号')" label-style="top">
          <mt-multi-select
            disabled
            v-model="searchFormModel.syncVersionList"
            :data-source="versionList"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="planners" :label="$t('计划员')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.planners"
            :data-source="plannerListOptions"
            :fields="{ text: 'codeAndName', value: 'userCode' }"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="supplierCodes" :label="$t('供应商编码')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="supplierCodes"
            @change="supplierChange"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        :row-config="{ height: 70 }"
        :columns="columns"
        :table-data="tableData"
        :show-footer="true"
        :footer-method="footerMethod"
        show-overflow
        height="auto"
        header-align="left"
        align="left"
        style="padding-top: unset"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import * as UTILS from '@/utils/utils'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      supplierCodes: '',
      versionList: [],
      plannerListOptions: [],
      searchFormModel: {},
      toolbar: [
        {
          code: 'RateExport',
          icon: 'icon_table_restart',
          name: this.$t('导出')
        }
      ],
      columns: [
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left'
        },
        {
          width: '120',
          field: 'syncVersion',
          title: this.$t('版本号')
        },
        {
          width: '80',
          field: 'supplierCode',
          title: this.$t('供应商代码')
          // formatter: ({ row }) => {
          //   return `${row.supplierCode}-${row.supplierName}`
          // }
        },
        {
          width: '120',
          field: 'supplierName',
          title: this.$t('供应商名称')
          // formatter: ({ row }) => {
          //   return `${row.supplierCode}-${row.supplierName}`
          // }
        },
        {
          width: '130',
          field: 'planner',
          title: this.$t('计划员'),
          formatter: ({ row }) => {
            return `${row.planner}-${row.plannerName}`
          }
        },
        {
          width: '120',
          field: 'synergyTotalNum',
          title: this.$t('协同总数')
        },
        {
          width: '120',
          field: 'synergyNum',
          title: this.$t('已协同数')
        },
        {
          width: '120',
          field: 'synergyRate',
          title: this.$t('已协同比例%')
        },
        {
          width: '120',
          field: 'unSynergyNum',
          title: this.$t('未协同数')
        },
        {
          width: '120',
          field: 'unSynergyRate',
          title: this.$t('未协同比例%')
        }
      ],
      tableData: [],
      summaryData: [['', '', '', '汇总', '']],
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      }
    }
  },
  mounted() {
    this.getPlannerList()
    this.buyerGoodsDemandPlanInfoQueryVersionTv()
  },
  methods: {
    footerMethod() {
      return this.summaryData
    },
    // 查询条件操作供应商编码切割
    supplierChange(e) {
      if (e) {
        this.searchFormModel.supplierCodes = this.supplierCodes.split(' ')
      } else {
        this.searchFormModel.supplierCodes = null
      }
    },
    // 获取计划员下拉
    getPlannerList() {
      this.$API.predictCollaboration.getPlannerAllName().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.plannerListOptions = data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.userCode} - ${i.userName}`
            }
          })
          // this.plannerListOptions.push({
          //   userCode: '',
          //   codeAndName: this.$t('计划员空')
          // })
        }
      })
    },
    // 获取版本下拉列表
    buyerGoodsDemandPlanInfoQueryVersionTv() {
      this.$API.predictCollaboration
        .postBuyerForecastDataReceiveQueryHistoryVersion({
          latest: true
        })
        .then((res) => {
          const { data, code } = res
          if (code === 200) {
            this.versionList = data.map((i) => {
              return {
                text: i.substring(0, 8),
                value: i
              }
            })
            if (this.versionList.length) {
              this.searchFormModel.syncVersionList = [
                this.versionList[this.versionList.length - 1]['value']
              ]
            }
          }
        })
    },
    handleClickToolBar(e) {
      if (e.code === 'RateExport') {
        this.handleExport()
      }
    },
    handleExport() {
      if (
        !this.searchFormModel.syncVersionList ||
        this.searchFormModel.syncVersionList.length === 0
      ) {
        this.$toast({ content: this.$t('请选择版本号'), type: 'warning' })
        return
      }
      if (!this.searchFormModel.planners || this.searchFormModel.planners.length === 0) {
        this.$toast({ content: this.$t('请选择计划员'), type: 'warning' })
        return
      }
      //导出
      const params = {
        page: {
          size: 9999,
          current: 1
        },
        historyVersionFlag: false,
        ...this.searchFormModel
      }
      this.apiStartLoading()
      this.$API.report
        .exportForecastFeedbackRateReport(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = UTILS.getHeadersFileName(res)
          UTILS.download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    handleCustomSearch() {
      const params = {
        ...this.searchFormModel,
        historyVersionFlag: false,
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        }
      }
      this.apiStartLoading()
      this.tableData = []
      this.$API.report
        .queryFeedbackRateReport(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.tvForecastReportQuerPageVoIPage?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.totalRecordsCount = Number(total)
            this.tableData =
              res?.data?.tvForecastReportQuerPageVoIPage?.records.map((i) => {
                return {
                  ...i,
                  syncVersion: i.syncVersion.slice(0, 8) + i.syncVersion.slice(14)
                }
              }) || [] // 表格数据
            this.summaryData = [
              [
                '',
                '',
                '',
                '汇总',
                res.data.synergyTotalNum,
                res.data.synergyNum,
                res.data.synergyRate,
                res.data.unSynergyNum,
                res.data.unSynergyRate
              ]
            ]
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 重置查询条件
    handleCustomReset() {
      this.supplierCodes = null
      this.searchFormModel.supplierCodes = null
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  background: #fff;
  padding: 12px 8px;
}
</style>
