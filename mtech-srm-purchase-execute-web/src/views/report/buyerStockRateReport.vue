<template>
  <!-- //备货达成率 -->
  <div class="full-height pt20 vertical-flex-box">
    <div class="flex-keep">
      <div class="header-box">
        <div class="main-bottom">
          <mt-form ref="ruleForm" :model="forecastTemplate" :rules="rules">
            <!-- <mt-form-item prop="companyCode" :label="$t('公司')">
              <mt-select
                class="forecast-template-select"
                v-model="forecastTemplate.companyCode"
                :allow-filtering="true"
                :filtering="getSourceCompany"
                :data-source="buyerList"
                :fields="{ text: 'label', value: 'orgCode' }"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item> -->
            <mt-form-item prop="siteCode" :label="$t('工厂')">
              <!-- <mt-select
                class="forecast-template-select"
                v-model="forecastTemplate.siteCode"
                :allow-filtering="true"
                :filtering="getSiteList"
                :data-source="factorySelect"
                :fields="{ text: 'label', value: 'siteCode' }"
                :placeholder="$t('请选择')"
              ></mt-select> -->
              <RemoteAutocomplete
                style="flex: 1"
                v-model="forecastTemplate.siteCode"
                :url="$API.masterData.getSiteListUrl"
                multiple
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                :search-fields="['siteName', 'siteCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="groupCode" :label="$t('采购组')">
              <!-- <mt-select
                class="forecast-template-select"
                v-model="forecastTemplate.groupCode"
                :allow-filtering="true"
                :data-source="groupList"
                :fields="{ text: 'label', value: 'groupCode' }"
                :placeholder="$t('请选择')"
              ></mt-select> -->
              <RemoteAutocomplete
                style="flex: 1"
                v-model="forecastTemplate.groupCode"
                :url="$API.masterData.getBusinessGroupAuthFuzzyUrl"
                multiple
                :placeholder="$t('请选择采购组')"
                :fields="{ text: 'groupName', value: 'groupCode' }"
                records-position="data"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商')">
              <!-- <mt-select
                class="forecast-template-select"
                :allow-filtering="true"
                v-model="forecastTemplate.supplierCode"
                :data-source="supplierList"
                :fields="{ text: 'label', value: 'supplierCode' }"
                :placeholder="$t('请选择')"
              ></mt-select> -->
              <RemoteAutocomplete
                style="flex: 1"
                v-model="forecastTemplate.supplierCode"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择供应商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="itemCode" :label="$t('物料')">
              <!-- <mt-select
                :allow-filtering="true"
                class="forecast-template-select"
                v-model="forecastTemplate.itemCode"
                :data-source="itemList"
                :fields="{ text: 'itemName', value: 'itemCode' }"
                :placeholder="$t('请选择')"
              ></mt-select> -->
              <RemoteAutocomplete
                style="flex: 1"
                v-model="forecastTemplate.itemCode"
                multiple
                :url="$API.masterData.getItemUrl"
                :placeholder="$t('请选择物料')"
                :fields="{ text: 'itemName', value: 'itemCode' }"
                :search-fields="['itemName', 'itemCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="confirmDate" :label="$t('报告生成日期')">
              <mt-date-range-picker
                v-model="forecastTemplate.confirmDate"
                operator="in"
                :placeholder="$t('选择日期')"
                :show-clear-button="false"
                :allow-edit="false"
                :disabled="false"
              ></mt-date-range-picker>
            </mt-form-item>
            <mt-button css-class="e-flat" @click="see" :is-primary="true">{{
              $t('查询')
            }}</mt-button>
          </mt-form>
        </div>
      </div>
    </div>

    <mt-template-page
      slot="slot-0"
      ref="templateRef"
      @handleClickToolBar="handleClickToolBar"
      :template-config="pageConfig1"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { buyerStockRateList } from './config/index.js'
// import deliveryDialog from "./components/deliveryDialog";
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  mounted() {
    this.init()
  },

  data() {
    return {
      id: [],
      editData: [],
      forecastTemplate: {
        confirmDate: '',
        itemCode: '',
        supplierCode: '',
        groupCode: '',
        // companyCode: "",
        siteCode: '',
        orgCode: ''
      },
      rules: {
        supplierCode: [{ required: false, message: this.$t('请选择'), trigger: 'blur' }],
        siteCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        confirmDate: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      deliveryShow: false,
      // permissionObj: {
      //   permissionNode: {
      //     // 当前的dom元素
      //     code: "ignore-element",
      //     type: "remove",
      //   },
      //   childNode: [
      //     { dataPermission: "a", permissionCode: "T_02_0120" },
      //     { dataPermission: "b", permissionCode: "T_02_0121" },
      //   ],
      // },

      itemList: [],
      supplierList: [],
      buyerList: [],
      factorySelect: [],
      userInfo: null,
      addDialogShow: false,
      currentTabIndex: 0,
      groupList: [],
      pageConfig1: [
        {
          useToolTemplate: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'export',
                  icon: 'icon_solid_Import',
                  // permission: ["O_02_1081"],
                  title: this.$t('导出')
                }
              ],
              []
            ]
          },
          gridId: '889F25F2-28C8-B74F-655F-E42821CA605B',

          grid: {
            pageSettings: {
              currentPage: 1,
              pageSize: 200,
              pageSizes: [50, 100, 200, 500, 1000]
            },
            columnData: buyerStockRateList,
            lineIndex: 0,

            autoWidthColumns: buyerStockRateList.length + 1,
            dataSource: [],
            asyncConfig: {
              url: `/statistics/tenant/buyerStockRateReport/query`,
              rules: []
            },
            frozenColumns: 1
          }
        }
      ],

      dialogData: null
    }
  },
  methods: {
    see() {
      console.log(this.forecastTemplate)
      this.pageConfig1[0].grid.asyncConfig.rules = []

      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.forecastTemplate.itemCode !== '' && this.forecastTemplate.itemCode.length > 0
            ? this.pageConfig1[0].grid.asyncConfig.rules.push({
                field: 'itemCode',
                operator: 'in',
                value: this.forecastTemplate.itemCode
              })
            : ''
          this.forecastTemplate.supplierCode !== '' && this.forecastTemplate.supplierCode.length > 0
            ? this.pageConfig1[0].grid.asyncConfig.rules.push({
                field: 'supplierCode',
                operator: 'in',
                value: this.forecastTemplate.supplierCode
              })
            : ''
          this.forecastTemplate.siteCode !== '' && this.forecastTemplate.siteCode.length > 0
            ? this.pageConfig1[0].grid.asyncConfig.rules.push({
                field: 'siteCode',
                operator: 'in',
                value: this.forecastTemplate.siteCode
              })
            : ''
          this.forecastTemplate.groupCode !== '' && this.forecastTemplate.groupCode.length > 0
            ? this.pageConfig1[0].grid.asyncConfig.rules.push({
                field: 'purGroupCode',
                operator: 'in',
                value: this.forecastTemplate.groupCode
              })
            : ''
          // this.forecastTemplate.companyCode !== "" &&
          // this.forecastTemplate.companyCode.length > 0
          //   ? this.pageConfig1[0].grid.asyncConfig.rules.push({
          //       field: "companyCode",
          //       operator: "in",
          //       value: this.forecastTemplate.companyCode,
          //     })
          //   : "";
          this.forecastTemplate.confirmDate !== ''
            ? this.pageConfig1[0].grid.asyncConfig.rules.push({
                field: 'reportTime',
                type: 'string',
                operator: 'greaterthanorequal',
                value: Number(this.forecastTemplate.confirmDate[0])
              })
            : ''
          this.forecastTemplate.confirmDate !== ''
            ? this.pageConfig1[0].grid.asyncConfig.rules.push({
                field: 'reportTime',
                type: 'string',
                operator: 'lessthanorequal',
                value: Number(this.forecastTemplate.confirmDate[1])
              })
            : ''
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // ToolBar
    handleClickToolBar(args) {
      if (args.toolbar.id == 'export') {
        // const queryBuilderRules =
        //   this.$refs.templateRef.getCurrentUsefulRef().pluginRef
        //     .queryBuilderRules || {};
        // console.log(queryBuilderRules);
        if (this.pageConfig1[0].grid.asyncConfig.rules.length > 0) {
          const params = {
            page: { current: 1, size: 1000 },
            rules: this.pageConfig1[0].grid.asyncConfig.rules
          } // 筛选条件
          this.$store.commit('startLoading')
          this.$API.report.buyerStockRateReportReport(params).then((res) => {
            this.$store.commit('endLoading')
            const fileName = getHeadersFileName(res)
            download({ fileName: `${fileName}`, blob: res.data })
          })
          return
        } else {
          this.$toast({
            content: this.$t('查询条件必填项必须选择'),
            type: 'warning'
          })
        }
      }
    },
    init() {
      let obj = {
        organizationLevelCodes: ['ORG01', 'ORG02'],
        page: { current: 1, pages: 1, size: 20 },
        fuzzyParam: ''
      }
      this.$API.masterData.getDictSpecified(obj).then((res) => {
        res.data.records.forEach((item) => {
          item.label = item.orgCode + '-' + item.orgName
        })
        this.buyerList = res.data.records
      })
      this.$API.masterData.getSiteList().then((res) => {
        res.data.records.forEach((item) => {
          item.label = item.siteCode + '-' + item.siteName
        })
        const list = res?.data.records || []
        this.factorySelect = [...list]
      })
      let groupObj = {
        page: {
          current: 1,
          pages: 12407,
          size: 20
        }
      }
      this.$API.masterData.getGroup(groupObj).then((res) => {
        res.data.records.forEach((item) => {
          item.label = item.groupCode + '-' + item.groupName
        })
        const list = res?.data.records || []

        this.groupList = [...list]
      })
      let supplierObj = {
        page: {
          current: 1,
          pages: 6,
          size: 20
        }
      }
      this.$API.masterData.supplierPagedQuery(supplierObj).then((res) => {
        const list = res?.data.records || []
        res.data.records.forEach((item) => {
          item.label = item.supplierCode + '-' + item.supplierName
        })
        this.supplierList = [...list]
      })
      let itemObj = {
        page: {
          current: 1,
          pages: 6,
          size: 20
        }
      }
      this.$API.masterData.getItemPage(itemObj).then((res) => {
        res.data.records.forEach((item) => {
          item.label = item.itemCode + '-' + item.itemName
        })
        const list = res?.data.records || []
        this.itemList = [...list]
      })
    }
  }
  // 消失
  // beforeDestroy() {
  //   localStorage.removeItem("deliverDetailSupplier");
  // },
}
</script>

<style lang="scss" scoped>
.forecast-template {
  width: 250px;
  padding-bottom: 8px;
  .forecast-template-select {
    width: calc(250px - 70px);
  }
}
.full-height .flex-keep {
  display: flex;
}
.main-bottom {
  width: 100%;
  /deep/ .mt-form-item {
    width: calc(19% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
    .mt-form-item-topLabel {
      flex: 1;
      width: 100%;
    }
    // &.more-width {
    //   // width: 450px;
    // }

    .full-width {
      width: calc(100% - 20px) !important;
    }
  }
  /deep/ .e-flat {
    position: relative;
    top: 21px;
    background: #19a2d5;
    color: white;
    border-radius: 5px;
  }
}
.header-box {
  width: 100%;
  padding-right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .itemcon {
    display: flex;
    align-items: center;
  }
  .item {
    margin-right: 20px;
  }
  .middle-blank {
    flex: 1;
  }
  .status {
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(99, 134, 193, 1);
    padding: 4px;
    background: rgba(238, 242, 249, 1);
    border-radius: 2px;
  }

  .infos {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
  }

  .sort-box {
    position: relative;
    cursor: pointer;
    margin-right: 20px;
    .mt-icons {
      font-size: 12px;
      transform: scale(0.5);
      color: rgba(0, 70, 156, 1);
      margin-top: -10px;
      position: absolute;
      top: 0;

      &:nth-child(2) {
        top: 6px;
      }
    }
  }
}
</style>
