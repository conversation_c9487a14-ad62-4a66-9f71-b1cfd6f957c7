<template>
  <!-- //回货价格失效 -->
  <div class="full-height pt20 vertical-flex-box">
    <div class="flex-keep">
      <div class="header-box">
        <div class="main-bottom">
          <mt-form ref="ruleForm" :model="forecastTemplate" :rules="rules">
            <mt-form-item prop="siteCodeList" :label="$t('工厂')">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="forecastTemplate.siteCodeList"
                :url="$API.masterData.getSiteListUrl"
                multiple
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                :search-fields="['siteName', 'siteCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="buyerOrgCodeList" :label="$t('采购组')">
              <!-- <mt-select
                class="forecast-template-select"
                v-model="forecastTemplate.groupCode"
                :allow-filtering="true"
                :data-source="groupList"
                :fields="{ text: 'label', value: 'groupCode' }"
                :placeholder="$t('请选择')"
              ></mt-select> -->
              <RemoteAutocomplete
                style="flex: 1"
                v-model="forecastTemplate.buyerOrgCodeList"
                :url="$API.masterData.getBusinessGroupAuthFuzzyUrl"
                multiple
                :placeholder="$t('请选择采购组')"
                :fields="{ text: 'groupName', value: 'groupCode' }"
                records-position="data"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="supplierCodeList" :label="$t('供应商')">
              <!-- <mt-select
                class="forecast-template-select"
                :allow-filtering="true"
                v-model="forecastTemplate.supplierCode"
                :data-source="supplierList"
                :fields="{ text: 'label', value: 'supplierCode' }"
                :placeholder="$t('请选择')"
              ></mt-select> -->
              <RemoteAutocomplete
                style="flex: 1"
                v-model="forecastTemplate.supplierCodeList"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择供应商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="itemCodeList" :label="$t('物料')">
              <!-- <mt-select
                :allow-filtering="true"
                class="forecast-template-select"
                v-model="forecastTemplate.itemCode"
                :data-source="itemList"
                :fields="{ text: 'itemName', value: 'itemCode' }"
                :placeholder="$t('请选择')"
              ></mt-select> -->
              <RemoteAutocomplete
                style="flex: 1"
                v-model="forecastTemplate.itemCodeList"
                multiple
                :url="$API.masterData.getItemUrl"
                :placeholder="$t('请选择物料')"
                :fields="{ text: 'itemName', value: 'itemCode' }"
                :search-fields="['itemName', 'itemCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="confirmDate" :label="$t('价格搜索日期')">
              <mt-date-range-picker
                v-model="forecastTemplate.confirmDate"
                operator="in"
                :placeholder="$t('选择日期')"
                :show-clear-button="false"
                :allow-edit="false"
                :disabled="false"
              ></mt-date-range-picker>
            </mt-form-item>
            <mt-button css-class="e-flat" @click="see" :is-primary="true">{{
              $t('查询')
            }}</mt-button>
          </mt-form>
        </div>
      </div>
    </div>

    <mt-template-page
      slot="slot-0"
      ref="templateRef"
      @handleClickToolBar="handleClickToolBar"
      :template-config="pageConfig1"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { failureStockRateList } from './config/index.js'
// import deliveryDialog from "./components/deliveryDialog";
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  mounted() {
    this.init()
  },

  data() {
    return {
      id: [],
      editData: [],
      forecastTemplate: {
        confirmDate: '',
        itemCodeList: [],
        supplierCodeList: [],
        buyerOrgCodeList: [],
        // companyCode: "",
        siteCodeList: [],
        orgCode: []
      },
      rules: {
        siteCodeList: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        confirmDate: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      deliveryShow: false,
      // permissionObj: {
      //   permissionNode: {
      //     // 当前的dom元素
      //     code: "ignore-element",
      //     type: "remove",
      //   },
      //   childNode: [
      //     { dataPermission: "a", permissionCode: "T_02_0120" },
      //     { dataPermission: "b", permissionCode: "T_02_0121" },
      //   ],
      // },

      itemList: [],
      supplierList: [],
      buyerList: [],
      factorySelect: [],
      userInfo: null,
      addDialogShow: false,
      currentTabIndex: 0,
      groupList: [],
      pageConfig1: [
        {
          useToolTemplate: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'export',
                  icon: 'icon_solid_Import',
                  // permission: ["O_02_1081"],
                  title: this.$t('导出')
                }
              ],
              []
            ]
          },
          grid: {
            pageSettings: {
              currentPage: 1,
              pageSize: 200,
              pageSizes: [50, 100, 200, 500, 1000]
            },
            columnData: failureStockRateList,
            // lineIndex: 1,

            autoWidthColumns: failureStockRateList.length + 1,
            dataSource: [],
            asyncConfig: {},
            frozenColumns: 1
          }
        }
      ],

      dialogData: null
    }
  },
  methods: {
    see() {
      console.log(this.forecastTemplate)
      let obj = {}
      // this.pageConfig1[0].grid.asyncConfig.rules = [];

      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          obj.itemCodeList = this.forecastTemplate.itemCodeList
          obj.supplierCodeList = this.forecastTemplate.supplierCodeList
          obj.siteCodeList = this.forecastTemplate.siteCodeList
          obj.buyerOrgCodeList = this.forecastTemplate.buyerOrgCodeList
          obj.priceDateStart = Number(this.forecastTemplate.confirmDate[0])
          obj.priceDateEnd = Number(this.forecastTemplate.confirmDate[1])
          //   this.forecastTemplate.itemCodeList !== "" &&
          //   this.forecastTemplate.itemCodeList.length > 0
          //     ? obj.push({
          //         field: "itemCodeList",
          //         operator: "in",
          //         value: this.forecastTemplate.itemCodeList,
          //       })
          //     : "";
          //   this.forecastTemplate.supplierCodeList !== "" &&
          //   this.forecastTemplate.supplierCodeList.length > 0
          //     ? obj.push({
          //         field: "supplierCodeList",
          //         operator: "in",
          //         value: this.forecastTemplate.supplierCodeList,
          //       })
          //     : "";
          //   this.forecastTemplate.siteCodeList !== "" &&
          //   this.forecastTemplate.siteCodeList.length > 0
          //     ? obj.push({
          //         field: "siteCodeList",
          //         operator: "in",
          //         value: this.forecastTemplate.siteCodeList,
          //       })
          //     : "";
          //   this.forecastTemplate.buyerOrgCodeList !== "" &&
          //   this.forecastTemplate.buyerOrgCodeList.length > 0
          //     ? obj.push({
          //         field: "purGroupCode",
          //         operator: "in",
          //         value: this.forecastTemplate.buyerOrgCodeList,
          //       })
          //     : "";
          //   // this.forecastTemplate.companyCode !== "" &&
          //   // this.forecastTemplate.companyCode.length > 0
          //   //   ? obj.push({
          //   //       field: "companyCode",
          //   //       operator: "in",
          //   //       value: this.forecastTemplate.companyCode,
          //   //     })
          //   //   : "";
          //   this.forecastTemplate.confirmDate !== ""
          //     ? obj.push({
          //         field: "priceDateStart",
          //         type: "string",
          //         operator: "greaterthanorequal",
          //         value: Number(this.forecastTemplate.confirmDate[0]),
          //       })
          //     : "";
          //   this.forecastTemplate.confirmDate !== ""
          //     ? obj.push({
          //         field: "priceDateEnd",
          //         type: "string",
          //         operator: "lessthanorequal",
          //         value: Number(this.forecastTemplate.confirmDate[1]),
          //       })
          //     : "";
          //   this.pageConfig1[0].grid.asyncConfig.params = obj;
          //   this.$refs.templateRef.refreshCurrentGridData();
          this.pageConfig1[0].grid.asyncConfig = {
            url: `/srm-purchase-execute/tenant/deliveryPriceInvalidWarn/queryBuilder`,
            params: obj,
            rules: []
          }
        }
      })
    },
    // ToolBar
    handleClickToolBar(args) {
      if (args.toolbar.id == 'export') {
        // const queryBuilderRules =
        //   this.$refs.templateRef.getCurrentUsefulRef().pluginRef
        //     .queryBuilderRules || {};
        // console.log(queryBuilderRules);
        let obj = {}
        // this.pageConfig1[0].grid.asyncConfig.rules = [];

        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            obj.itemCodeList = this.forecastTemplate.itemCodeList
            obj.supplierCodeList = this.forecastTemplate.supplierCodeList
            obj.siteCodeList = this.forecastTemplate.siteCodeList
            obj.buyerOrgCodeList = this.forecastTemplate.buyerOrgCodeList
            obj.priceDateStart = Number(this.forecastTemplate.confirmDate[0])
            obj.priceDateEnd = Number(this.forecastTemplate.confirmDate[1])
            const params = {
              page: { current: 1, size: 10000 },
              ...obj
            } // 筛选条件
            this.$store.commit('startLoading')
            this.$API.report.failureReport(params).then((res) => {
              this.$store.commit('endLoading')
              const fileName = getHeadersFileName(res)
              download({ fileName: `${fileName}`, blob: res.data })
            })
          }
        })
      }
    },
    init() {
      let obj = {
        organizationLevelCodes: ['ORG01', 'ORG02'],
        page: { current: 1, pages: 1, size: 20 },
        fuzzyParam: ''
      }
      this.$API.masterData.getDictSpecified(obj).then((res) => {
        res.data.records.forEach((item) => {
          item.label = item.orgCode + '-' + item.orgName
        })
        this.buyerList = res.data.records
      })
      this.$API.masterData.getSiteList().then((res) => {
        res.data.records.forEach((item) => {
          item.label = item.siteCode + '-' + item.siteName
        })
        const list = res?.data.records || []
        this.factorySelect = [...list]
      })
      let groupObj = {
        page: {
          current: 1,
          pages: 12407,
          size: 20
        }
      }
      this.$API.masterData.getGroup(groupObj).then((res) => {
        res.data.records.forEach((item) => {
          item.label = item.groupCode + '-' + item.groupName
        })
        const list = res?.data.records || []

        this.groupList = [...list]
      })
      let supplierObj = {
        page: {
          current: 1,
          pages: 6,
          size: 20
        }
      }
      this.$API.masterData.supplierPagedQuery(supplierObj).then((res) => {
        const list = res?.data.records || []
        res.data.records.forEach((item) => {
          item.label = item.supplierCode + '-' + item.supplierName
        })
        this.supplierList = [...list]
      })
      let itemObj = {
        page: {
          current: 1,
          pages: 6,
          size: 20
        }
      }
      this.$API.masterData.getItemPage(itemObj).then((res) => {
        res.data.records.forEach((item) => {
          item.label = item.itemCode + '-' + item.itemName
        })
        const list = res?.data.records || []
        this.itemList = [...list]
      })
    }
  }
  // 消失
  // beforeDestroy() {
  //   localStorage.removeItem("deliverDetailSupplier");
  // },
}
</script>

<style lang="scss" scoped>
.forecast-template {
  width: 250px;
  padding-bottom: 8px;
  .forecast-template-select {
    width: calc(250px - 70px);
  }
}
.full-height .flex-keep {
  display: flex;
}
.main-bottom {
  width: 100%;
  /deep/ .mt-form-item {
    width: calc(19% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
    .mt-form-item-topLabel {
      flex: 1;
      width: 100%;
    }
    // &.more-width {
    //   // width: 450px;
    // }

    .full-width {
      width: calc(100% - 20px) !important;
    }
  }
  /deep/ .e-flat {
    position: relative;
    top: 21px;
    background: #19a2d5;
    color: white;
    border-radius: 5px;
  }
}
.header-box {
  width: 100%;
  padding-right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .itemcon {
    display: flex;
    align-items: center;
  }
  .item {
    margin-right: 20px;
  }
  .middle-blank {
    flex: 1;
  }
  .status {
    font-size: 12px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(99, 134, 193, 1);
    padding: 4px;
    background: rgba(238, 242, 249, 1);
    border-radius: 2px;
  }

  .infos {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 600;
    color: rgba(41, 41, 41, 1);
  }

  .sort-box {
    position: relative;
    cursor: pointer;
    margin-right: 20px;
    .mt-icons {
      font-size: 12px;
      transform: scale(0.5);
      color: rgba(0, 70, 156, 1);
      margin-top: -10px;
      position: absolute;
      top: 0;

      &:nth-child(2) {
        top: 6px;
      }
    }
  }
}
</style>
