<!-- 采方-库存监控配置-新增or编辑 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :height="900"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-form-item prop="configType" :label="$t('配置类型')">
        <mt-select
          v-model="formData.configType"
          :fields="{ text: 'text', value: 'value' }"
          :data-source="configTypeOptions"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[1, 2, 3, 4, 5, 6].includes(formData.configType)"
        prop="supplierCodeList"
        :label="$t('供应商')"
      >
        <RemoteAutocomplete
          v-model="formData.supplierCodeList"
          url="/masterDataManagement/tenant/supplier/app/paged-query"
          :multiple="actionType === 'add'"
          :placeholder="$t('请选择')"
          :fields="{ text: 'supplierName', value: 'supplierCode' }"
          :search-fields="['supplierName', 'supplierCode']"
          @change="supplierChange"
          @multiChange="supplierMultiChange"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[1, 2, 3, 4, 5, 6].includes(formData.configType)"
        prop="factoryCodeList"
        :label="$t('工厂')"
      >
        <RemoteAutocomplete
          v-model="formData.factoryCodeList"
          url="/masterDataManagement/tenant/site/app/paged-query"
          :multiple="actionType === 'add'"
          :placeholder="$t('请选择')"
          :fields="{ text: 'siteName', value: 'siteCode' }"
          :search-fields="['siteName', 'siteCode']"
          @change="siteChange"
          @multiChange="factoryMultiChange"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[2, 4, 6].includes(formData.configType)"
        prop="purchaseGroupCode"
        :label="$t('采购组')"
      >
        <RemoteAutocomplete
          v-model="formData.purchaseGroupCode"
          :url="$API.masterData.getBusinessGroupUrl"
          :multiple="actionType === 'add'"
          :placeholder="$t('请选择')"
          :fields="{ text: 'groupName', value: 'groupCode' }"
          :search-fields="['groupName', 'groupCode']"
          @change="purOrgChange"
          @multiChange="purOrgMultiChange"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[3, 4, 5, 6].includes(formData.configType)"
        prop="categoryCode"
        :label="$t('品类')"
      >
        <RemoteAutocomplete
          v-model="formData.categoryCode"
          url="/masterDataManagement/tenant/category/paged-query"
          :multiple="actionType === 'add'"
          :placeholder="$t('请选择')"
          :fields="{ text: 'categoryName', value: 'categoryCode' }"
          :search-fields="['categoryName', 'categoryCode']"
          @change="categoryChange"
          @multiChange="categoryMultiChange"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[5, 6].includes(formData.configType)"
        prop="materialCode"
        :label="$t('物料编码')"
      >
        <RemoteAutocomplete
          v-model="formData.materialCode"
          :url="$API.masterData.getItemUrl"
          :multiple="actionType === 'add'"
          :placeholder="$t('请选择')"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          :search-fields="['itemName', 'itemCode']"
          @change="itemChange"
          @multiChange="itemMultiChange"
        />
      </mt-form-item>
      <mt-form-item prop="standardModel" :label="$t('校验标准')">
        <mt-select
          v-model="formData.standardModel"
          :data-source="standardOptions"
          :fields="{ text: 'text', value: 'value' }"
          :show-clear-button="true"
          :allow-filtering="true"
          filter-type="Contains"
          :placeholder="$t('请选择')"
        />
      </mt-form-item>
      <mt-form-item prop="days" :label="$t('需求天数')">
        <mt-input-number
          v-model="formData.days"
          :min="1"
          :show-spin-button="false"
          :show-clear-button="true"
          :placeholder="$t('请输入')"
        />
      </mt-form-item>
      <mt-form-item prop="safeInvQty" :label="$t('安全库存配置比率（%）')">
        <mt-input-number
          v-model="formData.safeInvQty"
          :min="1"
          :max="100"
          :show-spin-button="false"
          :show-clear-button="true"
          :placeholder="$t('请输入')"
        />
      </mt-form-item>
      <mt-form-item prop="platform" :label="$t('平台')">
        <mt-input
          v-model="formData.platform"
          :show-clear-button="true"
          :placeholder="$t('请输入')"
        />
      </mt-form-item>
    </mt-form>
    <div>
      <sc-table
        ref="scTableRef"
        grid-id="38742d0b-f514-448d-ba04-2438d60627b3"
        :fix-height="180"
        :loading="loading"
        :columns="columns"
        :table-data="tableData"
        :edit-config="editConfig"
        :edit-rules="editRules"
        keep-source
        :sortable="false"
        :is-show-right-btn="false"
        :is-show-refresh-bth="false"
      />
    </div>
    <mt-form ref="ruleForm2" :model="formData2" :rules="rules2" style="margin-top: 16px">
      <mt-row :gutter="24">
        <mt-col :span="12">
          <mt-form-item prop="purchaseTypeCode" :label="$t('通知联系人-采方')">
            <mt-select
              v-model="formData2.purchaseTypeCode"
              :data-source="purTypeOptions"
              :fields="{ text: 'text', value: 'value' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item
            v-if="[1, 3].includes(formData2.purchaseTypeCode)"
            prop="buyerNoticeContactValue"
            :label="$t('采方')"
          >
            <RemoteAutocomplete
              v-if="formData2.purchaseTypeCode === 1"
              v-model="formData2.buyerNoticeContactValue"
              :url="$API.masterData.getBusinessGroupUrl"
              multiple
              :placeholder="$t('请选择')"
              :fields="{ text: 'groupName', value: 'groupCode' }"
              :search-fields="['groupName', 'groupCode']"
              @multiChange="buyerNoticeContactMultiUserChange"
            />
            <RemoteAutocomplete
              v-if="formData2.purchaseTypeCode === 3"
              v-model="formData2.buyerNoticeContactValue"
              url="/masterDataManagement/tenant/employee/paged-query"
              multiple
              :placeholder="$t('请选择')"
              :fields="{ text: 'employeeName', value: 'externalCode' }"
              :search-fields="['employeeName', 'employeeCode']"
              @multiChange="buyerNoticeContactMultiUserChange"
            />
          </mt-form-item>
        </mt-col>
      </mt-row>
      <mt-row :gutter="24">
        <mt-col :span="12">
          <mt-form-item prop="supplierTypeCode" :label="$t('通知联系人-供方')">
            <mt-select
              v-model="formData2.supplierTypeCode"
              :data-source="supTypeOptions"
              :fields="{ text: 'text', value: 'value' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item prop="supplierNoticeValue" :label="$t('供方')">
            <mt-input
              v-if="formData2.supplierTypeCode === 2"
              v-model="formData2.supplierNoticeValue"
            />
            <mt-multi-select
              v-else
              v-model="formData2.supplierNoticeValue"
              :data-source="supplierNoticeContactUserOptions"
              :fields="{ text: 'text', value: 'value' }"
              multiple
              :show-clear-button="true"
              :allow-filtering="true"
              :placeholder="$t('请选择')"
            />
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
  </mt-dialog>
</template>

<script>
import {
  configTypeOptions,
  standardOptions,
  purTypeOptions,
  riskOptions,
  isConfigOptions,
  noticeMethodOptions,
  supplierNoticeContactUserOptions
} from '../config/index'
import ScTable from '@/components/ScTable/src/index'
import { cloneDeep } from 'lodash'
export default {
  components: { ScTable },
  data() {
    return {
      dialogTitle: '',
      actionType: 'add',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {},
      rules: {
        configType: [
          {
            required: true,
            message: this.$t('请选择配置类型'),
            trigger: 'blur'
          }
        ],
        supplierCodeList: [
          {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          }
        ],
        factoryCodeList: [
          {
            required: true,
            message: this.$t('请选择工厂'),
            trigger: 'blur'
          }
        ],
        purchaseGroupCode: [
          {
            required: true,
            message: this.$t('请选择采购组'),
            trigger: 'blur'
          }
        ],
        categoryCode: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ],
        materialCode: [
          {
            required: true,
            message: this.$t('请选择物料编码'),
            trigger: 'blur'
          }
        ],
        standardModel: [
          {
            required: true,
            message: this.$t('请选择校验标准'),
            trigger: 'blur'
          }
        ],
        days: [
          {
            required: true,
            message: this.$t('请输入需求天数'),
            trigger: 'blur'
          }
        ],
        safeInvQty: [
          {
            required: true,
            message: this.$t('请输入安全库存配置比率'),
            trigger: 'blur'
          }
        ]
      },

      formData2: {},
      rules2: {
        // purchaseTypeCode: [
        //   {
        //     required: true,
        //     message: this.$t('请选择通知联系人-采方'),
        //     trigger: 'blur'
        //   }
        // ],
        supplierTypeCode: [
          {
            required: true,
            message: this.$t('请选择通知联系人-供方'),
            trigger: 'blur'
          }
        ]
      },

      configTypeOptions,
      standardOptions,
      purTypeOptions,
      supTypeOptions: [],
      riskOptions,
      isConfigOptions,
      noticeMethodOptions,
      supplierNoticeContactUserOptions,

      loading: false,
      tableData: [
        { riskLevel: 1, rate: 40, isConfig: 1 },
        { riskLevel: 2, rate: 60, isConfig: 1 },
        { riskLevel: 3, rate: 80, isConfig: 1 }
      ],
      editConfig: {
        enabled: true,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      },
      editRules: {
        rate: [{ required: true, message: this.$t('必填') }],
        isConfig: [{ required: true, message: this.$t('必填') }],
        noticeMethod: [{ required: true, message: this.$t('必填') }],
        inspectTimes: [{ required: true, message: this.$t('必填') }],
        inspectTimeStart: [{ required: true, message: this.$t('必填') }],
        intervalTime: [{ required: true, message: this.$t('必填') }]
      },

      supplierCheckList: [],
      factoryCheckList: [],
      purchaseGroupCheckList: [],
      categoryCheckList: [],
      itemCheckList: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          field: 'riskLevel',
          title: this.$t('风险登记'),
          formatter: ({ cellValue }) => {
            let item = this.riskOptions.find((item) => item.value === cellValue)
            return item ? item.text : ''
          }
        },
        {
          field: 'rate',
          title: this.$t('维护比例'),
          minWidth: 115,
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [<div>{`<=${row.rate}%`}</div>]
            },
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  min={1}
                  max={100}
                  v-model={row.rate}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'isConfig',
          title: this.$t('是否配置预警'),
          minWidth: 135,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.isConfig}
                  placeholder={this.$t('请选择')}
                  options={this.isConfigOptions}
                  transfer
                  clearable
                />
              ]
            }
          },
          formatter: ({ cellValue }) => {
            let item = this.isConfigOptions.find((item) => item.value === cellValue)
            return item ? item.label : ''
          }
        },
        {
          field: 'noticeMethod',
          title: this.$t('通知方式'),
          minWidth: 115,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.noticeMethod}
                  placeholder={this.$t('请选择')}
                  options={this.noticeMethodOptions}
                  transfer
                  clearable
                />
              ]
            }
          },
          formatter: ({ cellValue }) => {
            let item = this.noticeMethodOptions.find((item) => item.value === cellValue)
            return item ? item.label : ''
          }
        },
        {
          field: 'inspectTimes',
          title: this.$t('校验轮次'),
          minWidth: 115,
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [<div>{row.inspectTimes}</div>]
            },
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  min={1}
                  v-model={row.inspectTimes}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'inspectTimeStart',
          title: this.$t('校验开始时间'),
          editRender: { name: 'input', attrs: { type: 'time' } },
          slots: {
            default: ({ row }) => {
              return [<div>{row.inspectTimeStart}</div>]
            }
          }
        },
        {
          field: 'intervalTime',
          title: this.$t('校验间隔时间（min）'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [<div>{row.intervalTime}</div>]
            },
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  min={1}
                  v-model={row.intervalTime}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        }
      ]
    }
  },
  methods: {
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, row } = args
      this.dialogTitle = title
      this.actionType = actionType
      this.buttons = [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
      this.supTypeOptions = [{ text: this.$t('联系人'), value: 1 }]

      if (actionType === 'edit') {
        this.formData = cloneDeep(row)
        this.formData.supplierCodeList = this.formData.supplierCode
        this.formData.factoryCodeList = this.formData.factoryCode
        this.formData2 = cloneDeep(row)
        this.formData2.buyerNoticeContactValue = this.formData2.buyerNoticeContactValue
          ? this.formData2.buyerNoticeContactValue.split(',')
          : []
        this.formData2.supplierNoticeValue = this.formData2.supplierNoticeValue
          ? this.formData2.supplierNoticeValue.split(',')
          : []
        this.tableData = cloneDeep(JSON.parse(row.configValue))
        if (this.formData2.supplierTypeCode === 2) {
          this.buttons = []
          this.supTypeOptions = [
            { text: this.$t('联系人'), value: 1 },
            { text: this.$t('其他'), value: 2 }
          ]
        }
      }
    },
    beforeOpen() {
      this.formData = {}
      this.$refs.ruleForm.clearValidate()
      this.formData2 = {}
      this.$refs.ruleForm2.clearValidate()
      this.tableData = [
        { riskLevel: 1, rate: 40, isConfig: 1 },
        { riskLevel: 2, rate: 60, isConfig: 1 },
        { riskLevel: 3, rate: 80, isConfig: 1 }
      ]
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.ruleForm2.clearValidate()
      this.formData2 = {}
      this.tableData = [
        { riskLevel: 1, rate: 40, isConfig: 1 },
        { riskLevel: 2, rate: 60, isConfig: 1 },
        { riskLevel: 3, rate: 80, isConfig: 1 }
      ]
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.ruleForm2.clearValidate()
      this.formData2 = {}
      this.tableData = [
        { riskLevel: 1, rate: 40, isConfig: 1 },
        { riskLevel: 2, rate: 60, isConfig: 1 },
        { riskLevel: 3, rate: 80, isConfig: 1 }
      ]
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.tableRef.validate(this.tableData).then((tableValid) => {
            if (tableValid) {
              this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
              return
            }
            this.$refs.ruleForm2.validate((valid2) => {
              if (valid2) {
                this.handleSave()
              }
            })
          })
        }
      })
    },
    handleSave() {
      let arr = this.tableRef.getTableData().visibleData.map((item) => {
        return {
          inspectTimeStart: item.inspectTimeStart,
          inspectTimes: Number(item.inspectTimes),
          intervalTime: Number(item.intervalTime),
          isConfig: item.isConfig,
          noticeMethod: item.noticeMethod,
          rate: String(item.rate),
          riskLevel: item.riskLevel
        }
      })
      let configValue = JSON.stringify(arr)
      let params = { ...this.formData2, ...this.formData, configValue }
      if (this.actionType === 'add') {
        params.supplierInfo = JSON.stringify(
          this.supplierCheckList.map((item) => {
            return {
              code: item.supplierCode,
              name: item.supplierName
            }
          })
        )
        params.factoryInfo = JSON.stringify(
          this.factoryCheckList.map((item) => {
            return {
              code: item.siteCode,
              name: item.siteName
            }
          })
        )
        params.purchaseGroupInfo = JSON.stringify(
          this.purchaseGroupCheckList.map((item) => {
            return {
              code: item.groupCode,
              name: item.groupName
            }
          })
        )
        params.categoryInfo = JSON.stringify(
          this.categoryCheckList.map((item) => {
            return {
              code: item.categoryCode,
              name: item.categoryName
            }
          })
        )
        params.materialInfo = JSON.stringify(
          this.itemCheckList.map((item) => {
            return {
              code: item.itemCode,
              name: item.itemName
            }
          })
        )
      }
      if (this.actionType === 'edit') {
        params.supplierInfo = JSON.stringify([
          {
            code: params.supplierCode,
            name: params.supplierName
          }
        ])
        params.factoryInfo = JSON.stringify([
          {
            code: params.factoryCode,
            name: params.factoryName
          }
        ])
        params.purchaseGroupInfo = JSON.stringify([
          {
            code: params.purchaseGroupCode,
            name: params.purchaseGroupName
          }
        ])
        params.categoryInfo = JSON.stringify([
          {
            code: params.categoryCode,
            name: params.categoryName
          }
        ])
        params.materialInfo = JSON.stringify([
          {
            code: params.materialCode,
            name: params.materialName
          }
        ])
      }
      params.buyerNoticeContactValue = Array.isArray(params.buyerNoticeContactValue)
        ? params.buyerNoticeContactValue?.length
          ? params.buyerNoticeContactValue.join(',')
          : ''
        : params.buyerNoticeContactValue
      params.supplierNoticeValue = Array.isArray(params.supplierNoticeValue)
        ? params.supplierNoticeValue?.length
          ? params.supplierNoticeValue.join(',')
          : ''
        : params.supplierNoticeValue
      const api = this.$API.appConfig.saveInventoryMonitorConfigApi
      api(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.actionType === 'add' ? this.$t('新增成功') : this.$t('更改成功'),
            type: 'success'
          })
          this.$emit('confirm')
          this.handleClose()
        }
      })
    },
    factoryMultiChange(e) {
      this.factoryCheckList = e
    },
    siteChange(e) {
      if (this.actionType === 'edit') {
        this.formData.factoryCode = e.itemData?.siteCode
        this.formData.factoryName = e.itemData?.siteName
      }
    },
    purOrgChange(e) {
      this.formData.purchaseGroupName = e.itemData?.groupName
    },
    purOrgMultiChange(e) {
      this.purchaseGroupCheckList = e
    },
    supplierMultiChange(e) {
      this.supplierCheckList = e
    },
    supplierChange(e) {
      if (this.actionType === 'edit') {
        this.formData.supplierCode = e.itemData?.supplierCode
        this.formData.supplierName = e.itemData?.supplierName
      }
    },
    categoryChange(e) {
      this.formData.categoryName = e.itemData?.categoryName
    },
    categoryMultiChange(e) {
      this.categoryCheckList = e
    },
    itemChange(e) {
      this.formData.materialName = e.itemData?.itemName
    },
    itemMultiChange(e) {
      this.itemCheckList = e
    },
    purchaseTypeChange() {
      this.formData2.buyerNoticeContactUser = null
    },
    buyerNoticeContactUserChange(e) {
      this.formData2.buyerNoticeContactName = e.itemData?.groupName
    },
    buyerNoticeContactMultiUserChange(e) {
      let list = e.map((item) => {
        if (this.formData2.buyerUserType === 1) {
          return item.groupName
        } else {
          return item.employeeName
        }
      })
      this.formData2.buyerNoticeContactName = list.join(',')
    },
    employeeChange(e) {
      this.formData2.buyerNoticeContactName = e.itemData?.employeeName
    },
    supplierNoticeContactUserChange(e) {
      this.formData2.supplierNoticeName = e.itemData?.text
    }
  }
}
</script>
