import { i18n } from '@/main.js'

export const statusOptions = [
  { text: i18n.t('启用'), value: 2 },
  { text: i18n.t('停用'), value: 1 }
]
export const configTypeOptions = [
  { text: i18n.t('供应商+工厂'), value: 1 },
  { text: i18n.t('供应商+工厂+采购组'), value: 2 },
  { text: i18n.t('供应商+工厂+品类'), value: 3 },
  { text: i18n.t('供应商+工厂+采购组+品类'), value: 4 },
  { text: i18n.t('供应商+工厂+品类+物料编码'), value: 5 },
  { text: i18n.t('供应商+工厂+采购组+品类+物料编码'), value: 6 }
]

export const standardOptions = [
  { text: i18n.t('自动计算'), value: 1 },
  { text: i18n.t('手工计算'), value: 2 }
]

export const purTypeOptions = [
  // { text: i18n.t('采购组'), value: 1 },
  { text: i18n.t('创建人'), value: 2 },
  { text: i18n.t('其他'), value: 3 }
]

export const supTypeOptions = [
  { text: i18n.t('联系人'), value: 1 },
  { text: i18n.t('其他'), value: 2 }
]

export const riskOptions = [
  { text: i18n.t('高'), value: 1 },
  { text: i18n.t('中'), value: 2 },
  { text: i18n.t('低'), value: 3 }
]

export const isConfigOptions = [
  { label: i18n.t('是'), value: 1 },
  { label: i18n.t('否'), value: 2 }
]

export const noticeMethodOptions = [
  { label: i18n.t('APP通知'), value: 1 },
  { label: i18n.t('短信'), value: 2 },
  { label: i18n.t('电话'), value: 3 }
]

export const supplierNoticeContactUserOptions = [
  { text: i18n.t('业务负责人'), value: '业务负责人' },
  { text: i18n.t('总经理'), value: '总经理' },
  { text: i18n.t('质量负责人'), value: '质量负责人' },
  { text: i18n.t('业务'), value: '业务' },
  { text: i18n.t('副总经理'), value: '副总经理' },
  { text: i18n.t('财务负责人'), value: '财务负责人' },
  { text: i18n.t('财务'), value: '财务' },
  { text: i18n.t('质量'), value: '质量' },
  { text: i18n.t('关务'), value: '关务' },
  { text: i18n.t('销售'), value: '销售' }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    minWidth: 80,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'configType',
    title: i18n.t('配置类型'),
    formatter: ({ cellValue }) => {
      let item = configTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'factoryCode',
    title: i18n.t('工厂'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.factoryName : ''
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    }
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类编码')
  },
  {
    field: 'categoryName',
    title: i18n.t('品类名称')
  },
  {
    field: 'purchaseGroupCode',
    title: i18n.t('采购组'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.purchaseGroupName : ''
    }
  },
  // {
  //   field: 'materialGroupCode',
  //   title: i18n.t('物料组'),
  //   minWidth: 120,
  //   formatter: ({ cellValue, row }) => {
  //     return cellValue ? cellValue + '-' + row.materialGroupName : ''
  //   }
  // },
  {
    field: 'materialCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'materialName',
    title: i18n.t('物料名称')
  },
  {
    field: 'standardModel',
    title: i18n.t('校验标准')
  },
  {
    field: 'days',
    title: i18n.t('需求天数')
  },
  {
    field: 'safeInvQty',
    title: i18n.t('安全库存配置比率'),
    minWidth: 120
  },
  {
    field: 'highRiskRatio',
    title: i18n.t('高风险配置比例'),
    minWidth: 120,
    formatter: ({ cellValue }) => {
      return cellValue ? `<=${cellValue}%` : ''
    }
  },
  {
    field: 'highRiskNoticeMethod',
    title: i18n.t('高风险通知方式'),
    minWidth: 120,
    formatter: ({ cellValue }) => {
      let item = noticeMethodOptions.find((item) => item.value === cellValue)
      return item ? item.label : ''
    }
  },
  {
    field: 'highRiskInspectTimes',
    title: i18n.t('高风险校验轮次'),
    minWidth: 120
  },
  {
    field: 'highRiskInspectTimeStart',
    title: i18n.t('高风险校验开始时间'),
    minWidth: 120
  },
  {
    field: 'highRiskIntervalTime',
    title: i18n.t('高风险校验间隔时间（min）'),
    minWidth: 120
  },
  {
    field: 'mediumRiskRatio',
    title: i18n.t('中风险配置比例'),
    minWidth: 120,
    formatter: ({ cellValue }) => {
      return cellValue ? `<=${cellValue}%` : ''
    }
  },
  {
    field: 'mediumNoticeMethod',
    title: i18n.t('中风险通知方式'),
    minWidth: 120,
    formatter: ({ cellValue }) => {
      let item = noticeMethodOptions.find((item) => item.value === cellValue)
      return item ? item.label : ''
    }
  },
  {
    field: 'mediumInspectTimes',
    title: i18n.t('中风险校验轮次'),
    minWidth: 120
  },
  {
    field: 'mediumInspectTimeStart',
    title: i18n.t('中风险校验开始时间'),
    minWidth: 120
  },
  {
    field: 'mediumIntervalTime',
    title: i18n.t('中风险校验间隔时间（min）'),
    minWidth: 120
  },
  {
    field: 'lowRiskRatio',
    title: i18n.t('低风险配置比例'),
    minWidth: 120,
    formatter: ({ cellValue }) => {
      return cellValue ? `<=${cellValue}%` : ''
    }
  },
  {
    field: 'lowNoticeMethod',
    title: i18n.t('低风险通知方式'),
    minWidth: 120,
    formatter: ({ cellValue }) => {
      let item = noticeMethodOptions.find((item) => item.value === cellValue)
      return item ? item.label : ''
    }
  },
  {
    field: 'lowInspectTimes',
    title: i18n.t('低风险校验轮次'),
    minWidth: 120
  },
  {
    field: 'lowInspectTimeStart',
    title: i18n.t('低风险校验开始时间'),
    minWidth: 120
  },
  {
    field: 'lowIntervalTime',
    title: i18n.t('低风险校验间隔时间（min）'),
    minWidth: 120
  },
  {
    field: 'buyerNoticeContactName',
    title: i18n.t('通知联系人-采方'),
    minWidth: 120
  },
  {
    field: 'supplierNoticeValue',
    title: i18n.t('通知联系人-供方'),
    minWidth: 120
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    minWidth: 120
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    minWidth: 120
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]
