import { i18n } from '@/main.js'
import dayjs from 'dayjs'

export const statusOptions = [
  { text: i18n.t('启用'), value: 2 },
  { text: i18n.t('停用'), value: 1 }
]

export const configTypeOptions = [
  { text: i18n.t('业务类型+订单类型'), value: 1 },
  { text: i18n.t('业务类型+订单类型+采购组'), value: 2 },
  { text: i18n.t('业务类型+订单类型+公司+供应商'), value: 3 },
  { text: i18n.t('业务类型+订单类型+公司+供应商+采购组'), value: 4 }
]

export const businessTypeOptions = [
  { text: i18n.t('固资新增'), value: 1 },
  { text: i18n.t('费用类'), value: 2 },
  { text: i18n.t('固资装修'), value: 3 },
  { text: i18n.t('通采类'), value: 4 },
  { text: i18n.t('物流类'), value: 5 }
]

export const noticeMethodOptions = [
  { text: i18n.t('短信'), value: 1 },
  { text: i18n.t('电话'), value: 2 },
  { text: i18n.t('APP通知'), value: 3 }
]

export const purTypeOptions = [
  { text: i18n.t('采购组'), value: 1 },
  { text: i18n.t('创建人'), value: 2 },
  { text: i18n.t('其他'), value: 3 }
]

export const supTypeOptions = [
  { text: i18n.t('联系人'), value: 1 },
  { text: i18n.t('其他'), value: 2 }
]

export const supplierNoticeContactUserOptions = [
  { text: i18n.t('业务负责人'), value: '业务负责人' },
  { text: i18n.t('总经理'), value: '总经理' },
  { text: i18n.t('质量负责人'), value: '质量负责人' },
  { text: i18n.t('业务'), value: '业务' },
  { text: i18n.t('副总经理'), value: '副总经理' },
  { text: i18n.t('财务负责人'), value: '财务负责人' },
  { text: i18n.t('财务'), value: '财务' },
  { text: i18n.t('质量'), value: '质量' },
  { text: i18n.t('关务'), value: '关务' },
  { text: i18n.t('销售'), value: '销售' }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    minWidth: 80,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'announcementTitle',
    title: i18n.t('公告标题'),
    minWidth: 120
  },
  {
    field: 'announcementSummary',
    title: i18n.t('公告概要'),
    minWidth: 120
  },
  {
    field: 'announcementDetails',
    title: i18n.t('公告详情'),
    minWidth: 120
  },
  {
    field: 'announceShowEndTime',
    title: i18n.t('首页公告显示截止时间'),
    minWidth: 170,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD')
    }
  },
  {
    field: 'versionNumber',
    title: i18n.t('版本号')
  },
  {
    field: 'versionDesc',
    title: i18n.t('版本说明')
  },
  {
    field: 'versionReleaseTime',
    title: i18n.t('版本发布时间'),
    minWidth: 120,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      let time = ''
      if (cellValue && cellValue != '0') {
        time = dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
      }
      return time
    }
  }
]
