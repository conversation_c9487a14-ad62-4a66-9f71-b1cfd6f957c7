<!-- 首页配置 -->
<template>
  <div class="homePage-config-container">
    <div class="title">{{ $t('首页通知配置') }}</div>
    <div style="width: 40%">
      <div class="btn">
        <vxe-button size="small">{{ $t('启用') }}</vxe-button>
        <vxe-button size="small">{{ $t('停用') }}</vxe-button>
      </div>
      <vxe-textarea
        v-model="notice"
        :placeholder="$t('请输入')"
        maxlength="200"
        show-word-count
        :autosize="{ minRows: 3, maxRows: 5 }"
      ></vxe-textarea>
    </div>
    <div class="title">{{ $t('首页图片配置') }}</div>
    <div class="btn">
      <vxe-button size="small" @click="handleUpload">{{ $t('选择图片') }}</vxe-button>
      <input
        type="file"
        ref="file"
        style="display: none"
        accept=".png, .jpg"
        @change="chooseFiles"
      />
    </div>
  </div>
</template>

<script>
import { Button as VxeButton, Textarea as VxeTextarea } from 'vxe-table'

export default {
  components: { VxeButton, VxeTextarea },
  data() {
    return {
      notice: '',
      allowFileType: ['png', 'jpg']
    }
  },
  methods: {
    async handleUpload() {
      this.$refs.file.click()
    },
    chooseFiles(data) {
      this.$loading()
      let { files } = data.target
      files = Object.values(files)
      let params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (files.length < 1) {
        this.$hloading()
        // 您未选择需要上传的文件
        return
      } else if (files.length > 3) {
        this.$hloading()
        this.$toast({
          content: this.$t('一次性最多选择3张图片'),
          type: 'warning'
        })
        return
      }
      let bol = files.some((item) => {
        let _tempInfo = item.name.split('.')
        return _tempInfo.length < 2 || !this.allowFileType.includes(_tempInfo[_tempInfo.length - 1])
      })
      if (bol) {
        this.$toast({
          content: this.$t('文件格式仅支持png/jpg'),
          type: 'warning'
        })
        this.$hloading()
        return
      }
      bol = files.some((item) => {
        return item.size > params.limit * 1024
      })
      if (bol) {
        this.$hloading()
        this.$toast({
          content: params.msg
        })
        return
      }
      this.$refs.file.value = ''
      this.uploadFile(files)
    },
    uploadFile() {}
  }
}
</script>

<style lang="scss" scoped>
.homePage-config-container {
  height: 100%;
  overflow-y: hidden;
  padding: 0 20px;
  .title {
    height: 40px;
    line-height: 40px;
    width: 160px;
    background-color: #4e5a70;
    color: #fff;
    margin: 20px 0;
    text-align: center;
  }
  .btn {
    margin-bottom: 10px;
  }
}
</style>
