<!-- 采方-送货单监控配置-新增or编辑 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :height="800"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-form-item prop="configType" :label="$t('配置类型')">
        <mt-select
          v-model="formData.configType"
          :fields="{ text: 'text', value: 'value' }"
          :data-source="configTypeOptions"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
        />
      </mt-form-item>
      <mt-form-item prop="deliveryTypeList" :label="$t('送货单类型')">
        <mt-multi-select
          v-model="formData.deliveryTypeList"
          :fields="{ text: 'text', value: 'value' }"
          :data-source="deliveryTypeOptions"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[1, 2, 3, 4, 5, 6].includes(formData.configType)"
        prop="siteCodeList"
        :label="$t('工厂')"
      >
        <RemoteAutocomplete
          v-model="formData.siteCodeList"
          :url="$API.masterData.getSiteListUrl"
          multiple
          :placeholder="$t('请选择')"
          :fields="{ text: 'siteName', value: 'siteCode' }"
          :search-fields="['siteName', 'siteCode']"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[2, 5].includes(formData.configType)"
        prop="purOrgCodeList"
        :label="$t('采购组')"
      >
        <RemoteAutocomplete
          v-model="formData.purOrgCodeList"
          :url="$API.masterData.getBusinessGroupUrl"
          multiple
          :placeholder="$t('请选择')"
          :fields="{ text: 'groupName', value: 'groupCode' }"
          :search-fields="['groupName', 'groupCode']"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[3].includes(formData.configType)"
        prop="categoryCodeList"
        :label="$t('品类')"
      >
        <RemoteAutocomplete
          v-model="formData.categoryCodeList"
          url="/masterDataManagement/tenant/category/paged-query"
          multiple
          :placeholder="$t('请选择')"
          :fields="{ text: 'categoryName', value: 'categoryCode' }"
          :search-fields="['categoryName', 'categoryCode']"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[4, 5, 6].includes(formData.configType)"
        prop="supplierCodeList"
        :label="$t('供应商')"
      >
        <RemoteAutocomplete
          v-model="formData.supplierCodeList"
          url="/masterDataManagement/tenant/supplier/paged-query"
          multiple
          :placeholder="$t('请选择')"
          :fields="{ text: 'supplierName', value: 'supplierCode' }"
          :search-fields="['supplierName', 'supplierCode']"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[6].includes(formData.configType)"
        prop="itemCodeList"
        :label="$t('物料编码')"
      >
        <RemoteAutocomplete
          v-model="formData.itemCodeList"
          :url="$API.masterData.getItemUrl"
          multiple
          :placeholder="$t('请选择')"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          :search-fields="['itemName', 'itemCode']"
        />
      </mt-form-item>
    </mt-form>
    <div>
      <sc-table
        ref="sctableRef"
        grid-id="42aedae8-c29e-4635-8ecf-02bfcaa5bd2d"
        :fix-height="270"
        :loading="loading"
        :columns="columns"
        :table-data="tableData"
        :edit-config="editConfig"
        :edit-rules="editRules"
        keep-source
        :sortable="false"
        :is-show-right-btn="false"
        :is-show-refresh-bth="false"
      />
    </div>
    <mt-form ref="ruleForm2" :model="formData2" :rules="rules2" style="margin-top: 16px">
      <mt-row :gutter="24">
        <mt-col :span="12">
          <mt-form-item prop="buyerUserType" :label="$t('通知联系人-采方')">
            <mt-select
              v-model="formData2.buyerUserType"
              :data-source="buyerUserTypeOptions"
              :fields="{ text: 'text', value: 'value' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item
            v-if="[1, 3].includes(formData2.buyerUserType)"
            prop="buyerNoticeContactUser"
            :label="$t('采方')"
          >
            <RemoteAutocomplete
              v-if="formData2.buyerUserType === 1"
              v-model="formData2.buyerNoticeContactUser"
              :url="$API.masterData.getBusinessGroupUrl"
              multiple
              :placeholder="$t('请选择')"
              :fields="{ text: 'groupName', value: 'groupCode' }"
              :search-fields="['groupName', 'groupCode']"
              @multiChange="buyerNoticeContactMultiUserChange"
            />
            <RemoteAutocomplete
              v-if="formData2.buyerUserType === 3"
              v-model="formData2.buyerNoticeContactUser"
              url="/masterDataManagement/tenant/employee/paged-query"
              multiple
              :placeholder="$t('请选择')"
              :fields="{ text: 'employeeName', value: 'externalCode' }"
              :search-fields="['employeeName', 'employeeCode']"
              @multiChange="buyerNoticeContactMultiUserChange"
            />
          </mt-form-item>
        </mt-col>
      </mt-row>
      <mt-row :gutter="24">
        <mt-col :span="12">
          <mt-form-item prop="supplierUserType" :label="$t('通知联系人-供方')">
            <mt-select
              v-model="formData2.supplierUserType"
              :data-source="supplierUserTypeOptions"
              :fields="{ text: 'text', value: 'value' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item prop="supplierNoticeContactUser" :label="$t('供方')">
            <mt-input
              v-if="formData2.supplierUserType === 2"
              v-model="formData2.supplierNoticeContactUser"
            />
            <mt-multi-select
              v-else
              v-model="formData2.supplierNoticeContactUser"
              :data-source="supplierNoticeContactUserOptions"
              :fields="{ text: 'text', value: 'value' }"
              multiple
              :show-clear-button="true"
              :allow-filtering="true"
              :placeholder="$t('请选择')"
            />
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
  </mt-dialog>
</template>

<script>
import {
  configTypeOptions,
  deliveryTypeOptions,
  buyerUserTypeOptions,
  supplierNoticeContactUserOptions,
  onWayStatusOptions,
  noticeMethodOptions
} from '../config/index'
import ScTable from '@/components/ScTable/src/index'
export default {
  components: { ScTable },
  data() {
    return {
      dialogTitle: '',
      actionType: 'add',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {},
      rules: {
        configType: [
          {
            required: true,
            message: this.$t('请选择配置类型'),
            trigger: 'blur'
          }
        ],
        deliveryTypeList: [
          {
            required: true,
            message: this.$t('请选择送货单类型'),
            trigger: 'blur'
          }
        ],
        siteCodeList: [
          {
            required: true,
            message: this.$t('请选择工厂'),
            trigger: 'blur'
          }
        ],
        purOrgCodeList: [
          {
            required: true,
            message: this.$t('请选择采购组'),
            trigger: 'blur'
          }
        ],
        categoryCodeList: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ],
        supplierCodeList: [
          {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          }
        ],
        itemCodeList: [
          {
            required: true,
            message: this.$t('请选择物料编码'),
            trigger: 'blur'
          }
        ]
      },

      configTypeOptions,
      deliveryTypeOptions,
      buyerUserTypeOptions,
      supplierUserTypeOptions: [],
      supplierNoticeContactUserOptions,
      onWayStatusOptions,
      noticeMethodOptions,

      formData2: {},
      rules2: {
        // buyerUserType: [
        //   {
        //     required: true,
        //     message: this.$t('请选择通知联系人-采方'),
        //     trigger: 'blur'
        //   }
        // ],
        supplierUserType: [
          {
            required: true,
            message: this.$t('请选择通知联系人-供方'),
            trigger: 'blur'
          }
        ]
      },

      loading: false,
      tableData: [
        { onWayStatus: 0 },
        { onWayStatus: 2 },
        { onWayStatus: 3 },
        { onWayStatus: 11 },
        { onWayStatus: 1 },
        { onWayStatus: 7 },
        { onWayStatus: 8 }
      ],
      editConfig: {
        enabled: true,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      },
      editRules: {
        noticeMethod: [{ required: true, message: this.$t('必填') }],
        roundNo: [{ required: true, message: this.$t('必填') }],
        intervalTime: [{ required: true, message: this.$t('必填') }]
      }
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          field: 'onWayStatus',
          title: this.$t('送货单在途状态'),
          formatter: ({ cellValue }) => {
            let item = this.onWayStatusOptions.find((item) => item.value === cellValue)
            return item ? item.text : ''
          }
        },
        {
          field: 'noticeMethod',
          title: this.$t('通知方式'),
          minWidth: 115,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.noticeMethod}
                  placeholder={this.$t('请选择')}
                  options={this.noticeMethodOptions}
                  transfer
                  clearable
                />
              ]
            }
          },
          formatter: ({ cellValue }) => {
            let item = this.noticeMethodOptions.find((item) => item.value === cellValue)
            return item ? item.label : ''
          }
        },
        {
          field: 'roundNo',
          title: this.$t('校验轮次'),
          minWidth: 115,
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [<div>{row.roundNo}</div>]
            },
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  min={1}
                  v-model={row.roundNo}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'intervalTime',
          title: this.$t('校验间隔时间（min）'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [<div>{row.intervalTime}</div>]
            },
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  min={1}
                  v-model={row.intervalTime}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        }
      ]
    }
  },
  methods: {
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, row } = args
      this.dialogTitle = title
      this.actionType = actionType
      this.buttons = [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
      this.supplierUserTypeOptions = [{ text: this.$t('联系人'), value: 1 }]

      if (actionType === 'edit') {
        this.getDetail(row.id)
      }
    },
    getDetail(id) {
      this.$API.appConfig.detailDeliveryMonitorConfigApi({ id }).then((res) => {
        if (res.code === 200) {
          this.formData = res.data
          this.formData.deliveryTypeList = [res.data.deliveryType]
          this.formData.siteCodeList = [res.data.siteCode]
          this.formData.supplierCodeList = [res.data.supplierCode]
          this.formData.purOrgCodeList = [res.data.purOrgCode]
          this.formData.categoryCodeList = [res.data.categoryCode]
          this.formData.itemCodeList = [res.data.itemCode]
          this.formData2 = {
            buyerUserType: res.data.buyerUserType,
            buyerNoticeContactUser: res.data.buyerNoticeContactUser?.length
              ? res.data.buyerNoticeContactUser.split(',')
              : '',
            // buyerNoticeContactUserName: res.data.buyerNoticeContactUserName,
            supplierUserType: res.data.supplierUserType,
            supplierNoticeContactUser: res.data.supplierNoticeContactUser?.length
              ? res.data.supplierNoticeContactUser.split(',')
              : ''
            // supplierNoticeContactUserName: res.data.supplierNoticeContactUserName
          }
          this.tableData = res.data.itemList || []
          if (this.formData2.supplierUserType === 2) {
            this.buttons = []
            this.supplierUserTypeOptions = [
              { text: this.$t('联系人'), value: 1 },
              { text: this.$t('其他'), value: 2 }
            ]
          }
        }
      })
    },
    beforeOpen() {
      this.formData = {}
      this.$refs.ruleForm.clearValidate()
      this.formData2 = {}
      this.$refs.ruleForm2.clearValidate()
      this.tableData = [
        { onWayStatus: 0 },
        { onWayStatus: 2 },
        { onWayStatus: 3 },
        { onWayStatus: 11 },
        { onWayStatus: 1 },
        { onWayStatus: 7 },
        { onWayStatus: 8 }
      ]
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.ruleForm2.clearValidate()
      this.formData2 = {}
      this.tableData = [
        { onWayStatus: 0 },
        { onWayStatus: 2 },
        { onWayStatus: 3 },
        { onWayStatus: 11 },
        { onWayStatus: 1 },
        { onWayStatus: 7 },
        { onWayStatus: 8 }
      ]
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.ruleForm2.clearValidate()
      this.formData2 = {}
      this.tableData = [
        { onWayStatus: 0 },
        { onWayStatus: 2 },
        { onWayStatus: 3 },
        { onWayStatus: 11 },
        { onWayStatus: 1 },
        { onWayStatus: 7 },
        { onWayStatus: 8 }
      ]
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.tableRef.validate(this.tableData).then((tableValid) => {
            if (tableValid) {
              this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
              return
            }
            this.$refs.ruleForm2.validate((valid2) => {
              if (valid2) {
                this.handleSave()
              }
            })
          })
        }
      })
    },
    handleSave() {
      let params = { ...this.formData, ...this.formData2, itemList: this.tableData }
      params.buyerNoticeContactUser = params.buyerNoticeContactUser?.length
        ? params.buyerNoticeContactUser.join(',')
        : ''
      params.supplierNoticeContactUser = params.supplierNoticeContactUser?.length
        ? params.supplierNoticeContactUser.join(',')
        : ''
      const api = this.$API.appConfig.saveDeliveryMonitorConfigApi
      api(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.actionType === 'add' ? this.$t('新增成功') : this.$t('更改成功'),
            type: 'success'
          })
          this.$emit('confirm')
          this.handleClose()
        }
      })
    },
    siteChange(e) {
      this.formData.siteName = e.itemData?.siteName
    },
    purOrgChange(e) {
      this.formData.purOrgName = e.itemData?.groupName
    },
    supplierChange(e) {
      this.formData.supplierName = e.itemData?.supplierName
    },
    itemChange(e) {
      this.formData.itemName = e.itemData?.itemName
    },
    buyerNoticeContactUserChange(e) {
      this.formData2.buyerNoticeContactUserName = e.itemData?.groupName
    },
    buyerNoticeContactMultiUserChange(e) {
      let list = e.map((item) => {
        if (this.formData2.buyerUserType === 1) {
          return item.groupName
        } else {
          return item.employeeName
        }
      })
      this.formData2.buyerNoticeContactUserName = list.join(',')
    },
    employeeChange(e) {
      this.formData2.buyerNoticeContactUserName = e.itemData?.employeeName
    },
    supplierNoticeContactUserChange(e) {
      this.formData2.supplierNoticeContactUserName = e.itemData?.text
    }
  }
}
</script>
