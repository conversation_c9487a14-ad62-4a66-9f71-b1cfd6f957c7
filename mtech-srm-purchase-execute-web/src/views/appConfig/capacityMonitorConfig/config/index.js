import { i18n } from '@/main.js'

export const statusOptions = [
  { text: i18n.t('启用'), value: 1 },
  { text: i18n.t('停用'), value: 2 }
]
export const configTypeOptions = [
  { text: i18n.t('采购组+工厂+供应商'), value: 1 },
  { text: i18n.t('计划组+工厂+供应商'), value: 2 },
  { text: i18n.t('工厂'), value: 3 },
  { text: i18n.t('采购组+工厂+供应商+物料编码'), value: 4 },
  { text: i18n.t('采购组+工厂+分厂+供应商'), value: 5 }
]

export const businessTypeOptions = [
  { text: i18n.t('固资新增'), value: 1 },
  { text: i18n.t('费用类'), value: 2 },
  { text: i18n.t('固资装修'), value: 3 },
  { text: i18n.t('通采类'), value: 4 },
  { text: i18n.t('物流类'), value: 5 }
]

export const ext3Options = [
  { text: i18n.t('APP通知'), value: 1 },
  { text: i18n.t('短信'), value: 2 },
  { text: i18n.t('电话'), value: 3 }
]

export const standardOptions = [
  { text: i18n.t('自动计算'), value: 1 },
  { text: i18n.t('其他'), value: 2 }
]

export const ext5Options = [
  // { text: i18n.t('采购组'), value: 1 },
  // { text: i18n.t('创建人'), value: 2 },
  { text: i18n.t('其他'), value: 3 }
]

export const ext7Options = [
  { text: i18n.t('联系人'), value: 1 },
  { text: i18n.t('其他'), value: 2 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'configType',
    title: i18n.t('配置类型'),
    formatter: ({ cellValue }) => {
      let item = configTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'purOrgCode',
    title: i18n.t('采购组'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.purOrgName : ''
    }
  },
  {
    field: 'planGroupCode',
    title: i18n.t('计划组'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.planGroupName : ''
    }
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.siteName : ''
    }
  },
  {
    field: 'siteCode1',
    title: i18n.t('分厂'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.siteName : ''
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    }
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称')
  },
  {
    field: 'ext1',
    title: i18n.t('校验轮次')
  },
  {
    field: 'ext2',
    title: i18n.t('校验间隔时间（min）')
  },
  {
    field: 'ext3',
    title: i18n.t('通知方式'),
    formatter: ({ cellValue }) => {
      let item = ext3Options.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'ext19',
    title: i18n.t('通知联系人-采方'),
    minWidth: 120
  },
  {
    field: 'ext20',
    title: i18n.t('通知联系人-供方'),
    minWidth: 120
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    minWidth: 120
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    minWidth: 120
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]
