<!-- 采方-产能监控配置-新增or编辑 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :height="800"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-form-item prop="configType" :label="$t('配置类型')">
        <mt-select
          v-model="formData.configType"
          :fields="{ text: 'text', value: 'value' }"
          :data-source="configTypeOptions"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[1, 4].includes(formData.configType)"
        prop="purOrgCode"
        :label="$t('采购组')"
      >
        <RemoteAutocomplete
          v-model="formData.purOrgCode"
          :url="$API.masterData.getBusinessGroupUrl"
          :placeholder="$t('请选择')"
          :fields="{ text: 'groupName', value: 'groupCode' }"
          :search-fields="['groupName', 'groupCode']"
          @change="purOrgChange"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[2].includes(formData.configType)"
        prop="planGroupCode"
        :label="$t('计划组')"
      >
        <RemoteAutocomplete
          v-model="formData.planGroupCode"
          :url="$API.masterData.getBusinessGroupUrl"
          :placeholder="$t('请选择')"
          :fields="{ text: 'groupName', value: 'groupCode' }"
          :search-fields="['groupName', 'groupCode']"
          @change="purOrgChange"
        />
      </mt-form-item>
      <mt-form-item prop="siteCode" :label="$t('工厂')">
        <RemoteAutocomplete
          v-model="formData.siteCode"
          :url="$API.masterData.getSiteListUrl"
          multiple
          :placeholder="$t('请选择')"
          :fields="{ text: 'siteName', value: 'siteCode' }"
          :search-fields="['siteName', 'siteCode']"
          @change="siteChange"
        />
      </mt-form-item>
      <mt-form-item v-if="[5].includes(formData.configType)" prop="siteCode1" :label="$t('分厂')">
        <RemoteAutocomplete
          v-model="formData.siteCode1"
          :url="$API.masterData.getSiteListUrl"
          multiple
          :placeholder="$t('请选择')"
          :fields="{ text: 'siteName', value: 'siteCode' }"
          :search-fields="['siteName', 'siteCode']"
          @change="siteChange"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[1, 2, 5].includes(formData.configType)"
        prop="supplierCode"
        :label="$t('供应商')"
      >
        <RemoteAutocomplete
          v-model="formData.supplierCode"
          url="/masterDataManagement/tenant/supplier/paged-query"
          multiple
          :placeholder="$t('请选择')"
          :fields="{ text: 'supplierName', value: 'supplierCode' }"
          :search-fields="['supplierName', 'supplierCode']"
          @change="supplierChange"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[4].includes(formData.configType)"
        prop="itemCode"
        :label="$t('物料编码')"
      >
        <RemoteAutocomplete
          v-model="formData.itemCode"
          :url="$API.masterData.getItemUrl"
          :placeholder="$t('请选择')"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          :search-fields="['itemName', 'itemCode']"
          @change="itemChange"
        />
      </mt-form-item>
      <mt-form-item prop="ext1" :label="$t('校验轮次')">
        <mt-select
          v-model="formData.ext1"
          :data-source="standardOptions"
          :fields="{ text: 'text', value: 'value' }"
          :show-clear-button="true"
          :allow-filtering="true"
          filter-type="Contains"
          :placeholder="$t('请选择')"
        />
      </mt-form-item>
      <mt-form-item prop="ext2" :label="$t('校验间隔时间（min）')">
        <mt-input-number
          v-model="formData.ext2"
          :min="1"
          :show-spin-button="false"
          :show-clear-button="true"
          :placeholder="$t('请输入')"
        />
      </mt-form-item>
      <mt-form-item prop="ext3" :label="$t('通知方式')">
        <mt-input v-model="formData.ext3" :show-clear-button="true" :placeholder="$t('请输入')" />
      </mt-form-item>
    </mt-form>
    <mt-form ref="ruleForm2" :model="formData2" :rules="rules2" style="margin-top: 16px">
      <mt-row :gutter="24">
        <mt-col :span="12">
          <mt-form-item prop="ext5" :label="$t('通知联系人-采方')">
            <mt-select
              v-model="formData.ext5"
              :data-source="ext5Options"
              :fields="{ text: 'text', value: 'value' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item prop="ext6" :label="$t('采方')">
            <mt-input
              v-model="formData.ext6"
              :show-clear-button="true"
              :placeholder="$t('请输入')"
            />
          </mt-form-item>
        </mt-col>
      </mt-row>
      <mt-row :gutter="24">
        <mt-col :span="12">
          <mt-form-item prop="ext7" :label="$t('通知联系人-供方')">
            <mt-select
              v-model="formData.ext7"
              :data-source="ext7Options"
              :fields="{ text: 'text', value: 'value' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item prop="ext8" :label="$t('供方')">
            <mt-input
              v-model="formData.ext8"
              :show-clear-button="true"
              :placeholder="$t('请输入')"
            />
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
  </mt-dialog>
</template>

<script>
import {
  configTypeOptions,
  businessTypeOptions,
  standardOptions,
  ext5Options,
  ext7Options
} from '../config/index'
export default {
  data() {
    return {
      dialogTitle: '',
      actionType: 'add',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {},
      rules: {
        configType: [
          {
            required: true,
            message: this.$t('请选择配置类型'),
            trigger: 'blur'
          }
        ],
        siteCode: [
          {
            required: true,
            message: this.$t('请选择工厂'),
            trigger: 'blur'
          }
        ],
        purOrgCode: [
          {
            required: true,
            message: this.$t('请选择采购组'),
            trigger: 'blur'
          }
        ],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          }
        ],
        categoryCode: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ],
        itemCode: [
          {
            required: true,
            message: this.$t('请选择物料编码'),
            trigger: 'blur'
          }
        ],
        ext1: [
          {
            required: true,
            message: this.$t('请选择校验标准'),
            trigger: 'blur'
          }
        ]
      },

      configTypeOptions,
      businessTypeOptions,
      standardOptions,
      ext5Options,
      ext7Options
    }
  },
  methods: {
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, row } = args
      this.dialogTitle = title
      this.actionType = actionType
      if (actionType === 'edit') {
        this.formData = row
      }
    },
    beforeOpen() {
      this.formData = {
        consignee: null,
        contactNo: null
      }
      this.$refs.ruleForm.clearValidate()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.handleSave()
        }
      })
    },
    handleSave() {
      let params = { ...this.formData }
      const api = this.$API.deliverySchedule.saveDeliveryPlanConfigApi
      api(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.actionType === 'add' ? this.$t('新增成功') : this.$t('更改成功'),
            type: 'success'
          })
          this.$emit('confirm')
          this.handleClose()
        }
      })
    },
    siteChange(e) {
      this.formData.siteName = e.itemData?.siteName
    },
    purOrgChange(e) {
      this.formData.purOrgName = e.itemData?.groupName
    },
    supplierChange(e) {
      this.formData.supplierName = e.itemData?.supplierName
    },
    categoryChange(e) {
      this.formData.categoryName = e.itemData?.categoryName
    },
    itemChange(e) {
      this.formData.itemName = e.itemData?.itemName
    }
  }
}
</script>
