<!-- 采方-订单监控配置-新增or编辑 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :height="800"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-form-item prop="configType" :label="$t('配置类型')">
        <mt-select
          v-model="formData.configType"
          :fields="{ text: 'text', value: 'value' }"
          :data-source="configTypeOptions"
          :allow-filtering="true"
          filter-type="Contains"
          :placeholder="$t('请选择')"
        />
      </mt-form-item>
      <mt-form-item prop="businessTypeCodeList" :label="$t('业务类型')">
        <mt-multi-select
          v-if="actionType === 'add'"
          v-model="formData.businessTypeCodeList"
          :data-source="businessTypeOptions"
          :fields="{ text: 'text', value: 'value' }"
          :show-clear-button="true"
          :allow-filtering="true"
          filter-type="Contains"
          :placeholder="$t('请选择')"
        />
        <mt-select
          v-else
          v-model="formData.businessTypeCodeList"
          :data-source="businessTypeOptions"
          :fields="{ text: 'text', value: 'value' }"
          :show-clear-button="true"
          :allow-filtering="true"
          filter-type="Contains"
          :placeholder="$t('请选择')"
        />
      </mt-form-item>
      <mt-form-item prop="orderTypeCodeList" :label="$t('订单类型')">
        <mt-multi-select
          v-if="actionType === 'add'"
          v-model="formData.orderTypeCodeList"
          :data-source="orderTypeOptions"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          :show-clear-button="true"
          :allow-filtering="true"
          filter-type="Contains"
          :placeholder="$t('请选择')"
        />
        <mt-select
          v-else
          v-model="formData.orderTypeCodeList"
          :data-source="orderTypeOptions"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          :show-clear-button="true"
          :allow-filtering="true"
          filter-type="Contains"
          :placeholder="$t('请选择')"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[3, 4].includes(formData.configType)"
        prop="companyCode"
        :label="$t('公司')"
      >
        <RemoteAutocomplete
          v-model="formData.companyCode"
          url="/masterDataManagement/tenant/organization/specified-level-paged-query"
          :multiple="actionType === 'add'"
          :placeholder="$t('请选择')"
          :fields="{ text: 'orgName', value: 'orgCode' }"
          :params="{
            organizationLevelCodes: ['ORG01', 'ORG02']
          }"
          @change="companyChange"
          @multiChange="companyMultiChange"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[3, 4].includes(formData.configType)"
        prop="supplierCodeList"
        :label="$t('供应商')"
      >
        <RemoteAutocomplete
          v-model="formData.supplierCodeList"
          url="/masterDataManagement/tenant/supplier/paged-query"
          :multiple="actionType === 'add'"
          :placeholder="$t('请选择')"
          :fields="{ text: 'supplierName', value: 'supplierCode' }"
          :search-fields="['supplierName', 'supplierCode']"
          @change="supplierChange"
          @multiChange="supplierMultiChange"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[2, 4].includes(formData.configType)"
        prop="purchaseGroupCode"
        :label="$t('采购组')"
      >
        <RemoteAutocomplete
          v-model="formData.purchaseGroupCode"
          :url="$API.masterData.getBusinessGroupUrl"
          :multiple="actionType === 'add'"
          :placeholder="$t('请选择')"
          :fields="{ text: 'groupName', value: 'groupCode' }"
          :search-fields="['groupName', 'groupCode']"
          @change="purOrgChange"
          @multiChange="purOrgMultiChange"
        />
      </mt-form-item>
      <mt-form-item prop="totalRound" :label="$t('校验轮次')">
        <mt-input-number
          v-model="formData.totalRound"
          :min="1"
          :show-spin-button="false"
          :show-clear-button="true"
          :placeholder="$t('请输入')"
        />
      </mt-form-item>
      <mt-form-item prop="intervalTime" :label="$t('校验间隔时间（min）')">
        <mt-input-number
          v-model="formData.intervalTime"
          :min="1"
          :show-spin-button="false"
          :show-clear-button="true"
          :placeholder="$t('请输入')"
        />
      </mt-form-item>
      <mt-form-item prop="noticeMethod" :label="$t('通知方式')">
        <mt-select
          v-model="formData.noticeMethod"
          :data-source="noticeMethodOptions"
          :fields="{ text: 'text', value: 'value' }"
          :show-clear-button="true"
          :allow-filtering="true"
          filter-type="Contains"
          :placeholder="$t('请选择')"
        />
      </mt-form-item>
    </mt-form>
    <mt-form ref="ruleForm2" :model="formData2" :rules="rules2" style="margin-top: 16px">
      <mt-row :gutter="24">
        <mt-col :span="12">
          <mt-form-item prop="purchaseTypeCode" :label="$t('通知联系人-采方')">
            <mt-select
              v-model="formData2.purchaseTypeCode"
              :data-source="purTypeOptions"
              :fields="{ text: 'text', value: 'value' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item
            v-if="[1, 3].includes(formData2.purchaseTypeCode)"
            prop="buyerNoticeContactValue"
            :label="$t('采方')"
          >
            <RemoteAutocomplete
              v-if="formData2.purchaseTypeCode === 1"
              v-model="formData2.buyerNoticeContactValue"
              :url="$API.masterData.getBusinessGroupUrl"
              multiple
              :placeholder="$t('请选择')"
              :fields="{ text: 'groupName', value: 'groupCode' }"
              :search-fields="['groupName', 'groupCode']"
              @change="buyerNoticeContactUserChange"
            />
            <RemoteAutocomplete
              v-if="formData2.purchaseTypeCode === 3"
              v-model="formData2.buyerNoticeContactValue"
              url="/masterDataManagement/tenant/employee/paged-query"
              multiple
              :placeholder="$t('请选择')"
              :fields="{ text: 'employeeName', value: 'externalCode' }"
              :search-fields="['employeeName', 'employeeCode']"
              @change="employeeChange"
            />
          </mt-form-item>
        </mt-col>
      </mt-row>
      <mt-row :gutter="24">
        <mt-col :span="12">
          <mt-form-item prop="supplierTypeCode" :label="$t('通知联系人-供方')">
            <mt-select
              v-model="formData2.supplierTypeCode"
              :data-source="supTypeOptions"
              :fields="{ text: 'text', value: 'value' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item prop="supplierNoticeValue" :label="$t('供方')">
            <mt-input
              v-if="formData2.supplierTypeCode === 2"
              v-model="formData2.supplierNoticeValue"
            />
            <mt-multi-select
              v-else
              v-model="formData2.supplierNoticeValue"
              :data-source="supplierNoticeContactUserOptions"
              :fields="{ text: 'text', value: 'value' }"
              multiple
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
            />
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
  </mt-dialog>
</template>

<script>
import {
  configTypeOptions,
  businessTypeOptions,
  noticeMethodOptions,
  purTypeOptions,
  supplierNoticeContactUserOptions
} from '../config/index'
import { cloneDeep } from 'lodash'
export default {
  data() {
    return {
      dialogTitle: '',
      actionType: 'add',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {},
      rules: {
        configType: [
          {
            required: true,
            message: this.$t('请选择配置类型'),
            trigger: 'blur'
          }
        ],
        businessTypeCodeList: [
          {
            required: true,
            message: this.$t('请选择业务类型'),
            trigger: 'blur'
          }
        ],
        orderTypeCodeList: [
          {
            required: true,
            message: this.$t('请选择订单类型'),
            trigger: 'blur'
          }
        ],
        purchaseGroupCode: [
          {
            required: true,
            message: this.$t('请选择采购组'),
            trigger: 'blur'
          }
        ],
        companyCode: [
          {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          }
        ],
        supplierCodeList: [
          {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          }
        ],
        totalRound: [
          {
            required: true,
            message: this.$t('请输入校验轮次'),
            trigger: 'blur'
          }
        ],
        intervalTime: [
          {
            required: true,
            message: this.$t('请输入校验间隔时间'),
            trigger: 'blur'
          }
        ],
        noticeMethod: [
          {
            required: true,
            message: this.$t('请选择通知方式'),
            trigger: 'blur'
          }
        ]
      },

      formData2: {},
      rules2: {
        // purchaseTypeCode: [
        //   {
        //     required: true,
        //     message: this.$t('请选择通知联系人-采方'),
        //     trigger: 'blur'
        //   }
        // ],
        supplierTypeCode: [
          {
            required: true,
            message: this.$t('请选择通知联系人-供方'),
            trigger: 'blur'
          }
        ]
      },

      configTypeOptions,
      businessTypeOptions,
      orderTypeOptions: [],
      noticeMethodOptions,
      purTypeOptions,
      supTypeOptions: [],
      supplierNoticeContactUserOptions,

      supplierCheckList: [],
      companyCheckList: [],
      purchaseGroupCheckList: []
    }
  },
  created() {
    this.getOrderTypeOptions()
  },
  methods: {
    getOrderTypeOptions() {
      this.$API.masterData.getDictCode({ dictCode: 'OrderType' }).then((res) => {
        this.orderTypeOptions = res.data || []
      })
    },
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, row } = args
      this.dialogTitle = title
      this.actionType = actionType
      this.buttons = [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
      this.supTypeOptions = [{ text: this.$t('联系人'), value: 1 }]

      if (actionType === 'edit') {
        this.formData = cloneDeep(row)
        this.formData.supplierCodeList = this.formData.supplierCode
        this.formData2 = cloneDeep(row)
        this.formData2.buyerNoticeContactValue = this.formData2.buyerNoticeContactValue
          ? this.formData2.buyerNoticeContactValue.split(',')
          : []
        this.formData2.supplierNoticeValue = this.formData2.supplierNoticeValue
          ? this.formData2.supplierNoticeValue.split(',')
          : []
        if (this.formData2.supplierTypeCode === 2) {
          this.buttons = []
          this.supTypeOptions = [
            { text: this.$t('联系人'), value: 1 },
            { text: this.$t('其他'), value: 2 }
          ]
        }
        this.formData.businessTypeCodeList = this.formData.businessTypeCode
        this.formData.orderTypeCodeList = this.formData.orderTypeCode
      }
    },
    beforeOpen() {
      this.formData = {}
      this.$refs.ruleForm.clearValidate()
      this.formData2 = {}
      this.$refs.ruleForm2.clearValidate()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.ruleForm2.clearValidate()
      this.formData2 = {}
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.ruleForm2.clearValidate()
      this.formData2 = {}
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$refs.ruleForm2.validate((valid2) => {
            if (valid2) {
              this.handleSave()
            }
          })
        }
      })
    },
    handleSave() {
      let params = { ...this.formData2, ...this.formData }
      console.log('handleSave', params)

      if (this.actionType === 'add') {
        params.supplierInfo = JSON.stringify(
          this.supplierCheckList.map((item) => {
            return {
              code: item.supplierCode,
              name: item.supplierName
            }
          })
        )
        params.companyInfo = JSON.stringify(
          this.companyCheckList.map((item) => {
            return {
              code: item.orgCode,
              name: item.orgName
            }
          })
        )
        params.purchaseGroupInfo = JSON.stringify(
          this.purchaseGroupCheckList.map((item) => {
            return {
              code: item.groupCode,
              name: item.groupName
            }
          })
        )
      }
      if (this.actionType === 'edit') {
        params.businessTypeCodeList = [params.businessTypeCodeList]
        params.orderTypeCodeList = [params.orderTypeCodeList]

        params.supplierInfo = JSON.stringify([
          {
            code: params.supplierCode,
            name: params.supplierName
          }
        ])
        params.companyInfo = JSON.stringify([
          {
            code: params.companyCode,
            name: params.companyName
          }
        ])
        params.purchaseGroupInfo = JSON.stringify([
          {
            code: params.purchaseGroupCode,
            name: params.purchaseGroupName
          }
        ])
      }
      params.buyerNoticeContactValue = Array.isArray(params.buyerNoticeContactValue)
        ? params.buyerNoticeContactValue?.length
          ? params.buyerNoticeContactValue.join(',')
          : ''
        : params.buyerNoticeContactValue
      params.supplierNoticeValue = Array.isArray(params.supplierNoticeValue)
        ? params.supplierNoticeValue?.length
          ? params.supplierNoticeValue.join(',')
          : ''
        : params.supplierNoticeValue
      const api = this.$API.appConfig.saveOrderMonitorConfigApi
      api(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.actionType === 'add' ? this.$t('新增成功') : this.$t('更改成功'),
            type: 'success'
          })
          this.$emit('confirm')
          this.handleClose()
        }
      })
    },
    companyChange(e) {
      this.formData.companyName = e.itemData?.orgName
    },
    companyMultiChange(e) {
      this.companyCheckList = e
    },
    purOrgChange(e) {
      this.formData.purchaseGroupName = e.itemData?.groupName
    },
    purOrgMultiChange(e) {
      this.purchaseGroupCheckList = e
    },
    supplierMultiChange(e) {
      this.supplierCheckList = e
    },
    supplierChange(e) {
      if (this.actionType === 'edit') {
        this.formData.supplierCode = e.itemData?.supplierCode
        this.formData.supplierName = e.itemData?.supplierName
      }
    },
    buyerNoticeContactUserChange(e) {
      this.formData2.buyerNoticeContactName = e.itemData?.groupName
    },
    employeeChange(e) {
      this.formData2.buyerNoticeContactName = e.itemData?.employeeName
    },
    supplierNoticeContactUserChange(e) {
      this.formData2.supplierNoticeName = e.itemData?.text
    }
  }
}
</script>
