<!-- 采方-交货计划监控配置-新增or编辑 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :height="800"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-form-item prop="configType" :label="$t('配置类型')">
        <mt-select
          v-model="formData.configType"
          :fields="{ text: 'text', value: 'value' }"
          :data-source="configTypeOptions"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[1, 2, 4].includes(formData.configType)"
        prop="siteCodeList"
        :label="$t('工厂')"
      >
        <RemoteAutocomplete
          v-model="formData.siteCodeList"
          :url="$API.masterData.getSiteListUrl"
          multiple
          :placeholder="$t('请选择')"
          :fields="{ text: 'siteName', value: 'siteCode' }"
          :search-fields="['siteName', 'siteCode']"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[2, 4].includes(formData.configType)"
        prop="purOrgCodeList"
        :label="$t('采购组')"
      >
        <RemoteAutocomplete
          v-model="formData.purOrgCodeList"
          :url="$API.masterData.getBusinessGroupUrl"
          multiple
          :placeholder="$t('请选择')"
          :fields="{ text: 'groupName', value: 'groupCode' }"
          :search-fields="['groupName', 'groupCode']"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[2, 4].includes(formData.configType)"
        prop="supplierCodeList"
        :label="$t('供应商')"
      >
        <RemoteAutocomplete
          v-model="formData.supplierCodeList"
          url="/masterDataManagement/tenant/supplier/paged-query"
          multiple
          :placeholder="$t('请选择')"
          :fields="{ text: 'supplierName', value: 'supplierCode' }"
          :search-fields="['supplierName', 'supplierCode']"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[4].includes(formData.configType)"
        prop="itemCodeList"
        :label="$t('物料编码')"
      >
        <RemoteAutocomplete
          v-model="formData.itemCodeList"
          :url="$API.masterData.getItemUrl"
          multiple
          :placeholder="$t('请选择')"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          :search-fields="['itemName', 'itemCode']"
        />
      </mt-form-item>
      <mt-form-item prop="roundNo" :label="$t('校验轮次')">
        <mt-input-number
          v-model="formData.roundNo"
          :min="1"
          :show-spin-button="false"
          :show-clear-button="true"
          :placeholder="$t('请输入')"
        />
      </mt-form-item>
      <mt-form-item prop="intervalTime" :label="$t('校验间隔时间（min）')">
        <mt-input-number
          v-model="formData.intervalTime"
          :min="1"
          :show-spin-button="false"
          :show-clear-button="true"
          :placeholder="$t('请输入')"
        />
      </mt-form-item>
      <mt-form-item prop="noticeMethod" :label="$t('通知方式')">
        <mt-select
          v-model="formData.noticeMethod"
          :data-source="noticeMethodOptions"
          :fields="{ text: 'text', value: 'value' }"
          :show-clear-button="true"
          :allow-filtering="true"
          filter-type="Contains"
          :placeholder="$t('请选择')"
        />
      </mt-form-item>
    </mt-form>
    <mt-form ref="ruleForm2" :model="formData2" :rules="rules2" style="margin-top: 16px">
      <mt-row :gutter="24">
        <mt-col :span="12">
          <mt-form-item prop="buyerUserType" :label="$t('通知联系人-采方')">
            <mt-select
              v-model="formData2.buyerUserType"
              :data-source="buyerUserTypeOptions"
              :fields="{ text: 'text', value: 'value' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item
            v-if="[1, 3].includes(formData2.buyerUserType)"
            prop="buyerNoticeContactUser"
            :label="$t('采方')"
          >
            <RemoteAutocomplete
              v-if="formData2.buyerUserType === 1"
              v-model="formData2.buyerNoticeContactUser"
              :url="$API.masterData.getBusinessGroupUrl"
              multiple
              :placeholder="$t('请选择')"
              :fields="{ text: 'groupName', value: 'groupCode' }"
              :search-fields="['groupName', 'groupCode']"
              @multiChange="buyerNoticeContactMultiUserChange"
            />
            <RemoteAutocomplete
              v-if="formData2.buyerUserType === 3"
              v-model="formData2.buyerNoticeContactUser"
              url="/masterDataManagement/tenant/employee/paged-query"
              multiple
              :placeholder="$t('请选择')"
              :fields="{ text: 'employeeName', value: 'externalCode' }"
              :search-fields="['employeeName', 'employeeCode']"
              @multiChange="buyerNoticeContactMultiUserChange"
            />
          </mt-form-item>
        </mt-col>
      </mt-row>
      <mt-row :gutter="24">
        <mt-col :span="12">
          <mt-form-item prop="supplierUserType" :label="$t('通知联系人-供方')">
            <mt-select
              v-model="formData2.supplierUserType"
              :data-source="supplierUserTypeOptions"
              :fields="{ text: 'text', value: 'value' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item prop="supplierNoticeContactUser" :label="$t('供方')">
            <mt-input
              v-if="formData2.supplierUserType === 2"
              v-model="formData2.supplierNoticeContactUser"
            />
            <mt-multi-select
              v-else
              v-model="formData2.supplierNoticeContactUser"
              :data-source="supplierNoticeContactUserOptions"
              :fields="{ text: 'text', value: 'value' }"
              multiple
              :show-clear-button="true"
              :allow-filtering="true"
              :placeholder="$t('请选择')"
            />
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
  </mt-dialog>
</template>

<script>
import {
  configTypeOptions,
  noticeMethodOptions,
  buyerUserTypeOptions,
  supplierNoticeContactUserOptions
} from '../config/index'
import { cloneDeep } from 'lodash'
export default {
  data() {
    return {
      dialogTitle: '',
      actionType: 'add',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {},
      rules: {
        configType: [
          {
            required: true,
            message: this.$t('请选择配置类型'),
            trigger: 'blur'
          }
        ],
        siteCodeList: [
          {
            required: true,
            message: this.$t('请选择工厂'),
            trigger: 'blur'
          }
        ],
        purOrgCodeList: [
          {
            required: true,
            message: this.$t('请选择采购组'),
            trigger: 'blur'
          }
        ],
        supplierCodeList: [
          {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          }
        ],
        itemCodeList: [
          {
            required: true,
            message: this.$t('请选择物料编码'),
            trigger: 'blur'
          }
        ],
        roundNo: [
          {
            required: true,
            message: this.$t('请输入校验轮次'),
            trigger: 'blur'
          }
        ],
        intervalTime: [
          {
            required: true,
            message: this.$t('请输入校验间隔时间'),
            trigger: 'blur'
          }
        ],
        noticeMethod: [
          {
            required: true,
            message: this.$t('请选择通知方式'),
            trigger: 'blur'
          }
        ]
      },

      configTypeOptions,
      noticeMethodOptions,
      buyerUserTypeOptions,
      supplierUserTypeOptions: [],
      supplierNoticeContactUserOptions,

      formData2: {},
      rules2: {
        // buyerUserType: [
        //   {
        //     required: true,
        //     message: this.$t('请选择通知联系人-采方'),
        //     trigger: 'blur'
        //   }
        // ],
        supplierUserType: [
          {
            required: true,
            message: this.$t('请选择通知联系人-供方'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, row } = args
      this.dialogTitle = title
      this.actionType = actionType
      this.buttons = [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
      this.supplierUserTypeOptions = [{ text: this.$t('联系人'), value: 1 }]

      if (actionType === 'edit') {
        this.formData = cloneDeep(row)
        this.formData2 = {
          buyerUserType: row.buyerUserType,
          buyerNoticeContactUser: row.buyerNoticeContactUser?.length
            ? row.buyerNoticeContactUser.split(',')
            : '',
          // buyerNoticeContactUserName: row.buyerNoticeContactUserName,
          supplierUserType: row.supplierUserType,
          supplierNoticeContactUser: row.supplierNoticeContactUser?.length
            ? row.supplierNoticeContactUser.split(',')
            : ''
          // supplierNoticeContactUserName: row.supplierNoticeContactUserName
        }
        this.formData.siteCodeList = [this.formData.siteCode]
        this.formData.supplierCodeList = [this.formData.supplierCode]
        this.formData.purOrgCodeList = [this.formData.purOrgCode]
        this.formData.itemCodeList = [this.formData.itemCode]
        if (this.formData2.supplierUserType === 2) {
          this.buttons = []
          this.supplierUserTypeOptions = [
            { text: this.$t('联系人'), value: 1 },
            { text: this.$t('其他'), value: 2 }
          ]
        }
      }
    },
    beforeOpen() {
      this.formData = {}
      this.$refs.ruleForm.clearValidate()
      this.formData2 = {}
      this.$refs.ruleForm2.clearValidate()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.ruleForm2.clearValidate()
      this.formData2 = {}
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.ruleForm2.clearValidate()
      this.formData2 = {}
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$refs.ruleForm2.validate((valid2) => {
            if (valid2) {
              this.handleSave()
            }
          })
        }
      })
    },
    handleSave() {
      let params = { ...this.formData, ...this.formData2 }
      params.buyerNoticeContactUser = params.buyerNoticeContactUser?.length
        ? params.buyerNoticeContactUser.join(',')
        : ''
      params.supplierNoticeContactUser = params.supplierNoticeContactUser?.length
        ? params.supplierNoticeContactUser.join(',')
        : ''
      const api = this.$API.appConfig.saveDeliveryScheduleMonitorConfigApi
      api(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.actionType === 'add' ? this.$t('新增成功') : this.$t('更改成功'),
            type: 'success'
          })
          this.$emit('confirm')
          this.handleClose()
        }
      })
    },
    siteChange(e) {
      this.formData.siteName = e.itemData?.siteName
    },
    purOrgChange(e) {
      this.formData.purOrgName = e.itemData?.groupName
    },
    planGroupChange(e) {
      this.formData.planGroupName = e.itemData?.groupName
    },
    supplierChange(e) {
      this.formData.supplierName = e.itemData?.supplierName
    },
    categoryChange(e) {
      this.formData.categoryName = e.itemData?.categoryName
    },
    itemChange(e) {
      this.formData.itemName = e.itemData?.itemName
    },
    buyerNoticeContactUserChange(e) {
      this.formData2.buyerNoticeContactUserName = e.itemData?.groupName
    },
    buyerNoticeContactMultiUserChange(e) {
      let list = e.map((item) => {
        if (this.formData2.buyerUserType === 1) {
          return item.groupName
        } else {
          return item.employeeName
        }
      })
      this.formData2.buyerNoticeContactUserName = list.join(',')
    },
    employeeChange(e) {
      this.formData2.buyerNoticeContactUserName = e.itemData?.employeeName
    },
    supplierNoticeContactUserChange(e) {
      this.formData2.supplierNoticeContactUserName = e.itemData?.text
    }
  }
}
</script>
