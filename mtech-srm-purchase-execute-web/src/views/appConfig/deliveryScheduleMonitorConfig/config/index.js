import { i18n } from '@/main.js'

export const statusOptions = [
  { text: i18n.t('启用'), value: 1 },
  { text: i18n.t('停用'), value: 0 }
]
export const configTypeOptions = [
  { text: i18n.t('工厂'), value: 1 },
  { text: i18n.t('采购组+工厂+供应商'), value: 2 },
  { text: i18n.t('采购组+工厂+供应商+物料编码'), value: 4 }
]

export const noticeMethodOptions = [
  { text: i18n.t('APP通知'), value: 1 },
  { text: i18n.t('短信'), value: 2 },
  { text: i18n.t('电话'), value: 3 }
]

export const buyerUserTypeOptions = [
  // { text: i18n.t('采购组'), value: 1 },
  { text: i18n.t('创建人'), value: 2 },
  { text: i18n.t('其他'), value: 3 }
]

export const supplierUserTypeOptions = [
  { text: i18n.t('联系人'), value: 1 },
  { text: i18n.t('其他'), value: 2 }
]

export const supplierNoticeContactUserOptions = [
  { text: i18n.t('业务负责人'), value: '业务负责人' },
  { text: i18n.t('总经理'), value: '总经理' },
  { text: i18n.t('质量负责人'), value: '质量负责人' },
  { text: i18n.t('业务'), value: '业务' },
  { text: i18n.t('副总经理'), value: '副总经理' },
  { text: i18n.t('财务负责人'), value: '财务负责人' },
  { text: i18n.t('财务'), value: '财务' },
  { text: i18n.t('质量'), value: '质量' },
  { text: i18n.t('关务'), value: '关务' },
  { text: i18n.t('销售'), value: '销售' }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    minWidth: 80,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'configType',
    title: i18n.t('配置类型'),
    minWidth: 150,
    formatter: ({ cellValue }) => {
      let item = configTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'companyCode',
    title: i18n.t('公司'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.companyName : ''
    }
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.siteName : ''
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    }
  },
  {
    field: 'purOrgCode',
    title: i18n.t('采购组'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.purOrgName : ''
    }
  },
  {
    field: 'planGroupCode',
    title: i18n.t('计划组'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.planGroupName : ''
    }
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称')
  },
  {
    field: 'roundNo',
    title: i18n.t('校验轮次')
  },
  {
    field: 'intervalTime',
    title: i18n.t('校验间隔时间（min）')
  },
  {
    field: 'noticeMethod',
    title: i18n.t('通知方式'),
    formatter: ({ cellValue }) => {
      let item = noticeMethodOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'buyerNoticeContactUserName',
    title: i18n.t('通知联系人-采方'),
    minWidth: 120
  },
  {
    field: 'supplierNoticeContactUser',
    title: i18n.t('通知联系人-供方'),
    minWidth: 120
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    minWidth: 120
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    minWidth: 120
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]
