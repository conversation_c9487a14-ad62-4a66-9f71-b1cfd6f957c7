<template>
  <div class="full-height pt20">
    <!-- <button @click="handleLink">{{ $t('跳转') }}</button> -->
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>

    <!-- <add-dialog
      v-if="addDialogShow"
      :dialog-data="dialogData"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></add-dialog> -->
  </div>
</template>

<script>
import { columnData } from './config/index.js'
export default {
  data() {
    return {
      pageConfig: [
        {
          title: this.$t('汇总列表'),
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              // ["Add", "Delete"],
              [],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columnData,
            lineIndex: 1,
            autoWidthColumns: columnData.length + 1,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/pe/business/configs',
              recordsPosition: 'data'
            },
            frozenColumns: 1
          }
        }
        // {
        //   title: this.$t("已删除"),
        //   toolbar: {
        //     useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
        //     tools: [
        //       [
        //         {
        //           id: "active",
        //           icon: "icon_Activation",
        //           title: this.$t("恢复"),
        //         },
        //       ],
        //     ],
        //   },
        //   grid: {
        //     columnData: columnData,
        //     dataSource: [],
        //     // asyncConfig: {
        //     //   url: "/masterDataManagement/tenant/user/paged-query",
        //     // },
        //     frozenColumns: 1,
        //   },
        // },
      ],
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    handleClickToolBar(e) {
      console.log(e.gridRef.getMtechGridRecords(), e)
      if (
        e.gridRef.getMtechGridRecords().length <= 0 &&
        !(
          e.toolbar.id == 'Add' ||
          e.toolbar.id == 'Filter' ||
          e.toolbar.id == 'Refresh' ||
          e.toolbar.id == 'Setting'
        )
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = []
      e.gridRef.getMtechGridRecords().map((item) => _id.push(item.id))
      if (e.toolbar.id == 'Refresh') {
        this.getTableData()
      } else if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(_id)
      }
    },

    handleAdd() {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'add',
        requestUrl: 'saveBusinessConfig'
      }
    },

    handleEdit(row) {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'edit',
        requestUrl: 'saveBusinessConfig',
        row: row
      }
    },

    handleDelete() {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message:
            '业务类型删除后，已被引用的历史单据将仍可以搜索。新创建的单据将不能再选择已删除的业务类型'
        },
        success: () => {
          //  this.$store.commit("startLoading");
          //   this.$API.moduleConfig.deleteDimension({ idList: id }).then((res) => {
          //     this.$store.commit("endLoading");
          //     if (res.code == 200) {
          //       this.$toast({ content: this.$t("操作成功"), type: "success" });
          //       this.confirmSuccess();
          //     }
          //   });
        }
      })
    },

    handleLink() {
      var _query = {
        // businessTypeCode: "BT002",
        // businessTypeId: "1387704552388108245",
        // businessTypeName: this.$t("生产采购"),
        // enableStatus: 1,
        // configId: "86918622304444488",
        // remark: this.$t("这是一条生产采购信息"),
        // updateTime: null,
        // updateUserId: "0",
        // updateUserName: "",

        businessTypeCode: 'BT001',
        businessTypeId: '1387704552388108256',
        businessTypeName: this.$t('一般采购'),
        enableStatus: 1,
        configId: '89145000742559780',
        remark: this.$t('这是一条一般采购信息'),
        updateTime: null,
        updateUserId: '0',
        updateUserName: ''
      }
      localStorage.sourceModuleConfigInfo = JSON.stringify(_query)
      this.redirectPage('purchase-execute/business-configure-detail', {})
    },

    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },

    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    },

    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      } else if (e.tool.id == 'edit') {
        let _row = e.data
        this.handleEdit(_row)
      } else if (e.tool.id == 'config') {
        let _query = e.data
        if (e.data.id) {
          _query.configId = e.data.id
        }
        localStorage.sourceModuleConfigInfo = JSON.stringify(_query)
        this.redirectPage('purchase-execute/business-configure-detail', {})
      }
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
