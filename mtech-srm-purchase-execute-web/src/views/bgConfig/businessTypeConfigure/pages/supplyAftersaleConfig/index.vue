<template>
  <div class="purchase-order">
    <common-config
      doc-type="as_so"
      base-doc-type="as_po"
      :relative-file-module-type="14"
      :base-module-type="11"
      :column-name="$t('供应商售后订单')"
      :base-column-name="$t('售后订单')"
    ></common-config>
  </div>
</template>

<script>
export default {
  components: {
    commonConfig: require('@/components/businessComponents/fieldsConfig/commonConfig/index.vue')
      .default
  }
}
</script>

<style></style>
