<template>
  <div class="fields-config-page mt-flex-direction-column">
    <div class="top-info mt-flex-direction-column">
      <div class="detail-info">
        <div class="name-wrap">
          <div class="first-line">
            <span class="code">{{ configInfo.businessTypeCode }}</span>
            <span
              :class="['tags', `tags-${index + 1}`]"
              v-for="(item, index) in tagList"
              :key="index"
              >{{ item }}</span
            >
          </div>
          <div class="second-line">
            <div class="cai-name">{{ configInfo.businessTypeId }}</div>
          </div>
        </div>
        <div class="btns-wrap">
          <!-- <mt-button>{{ $t('保存') }}</mt-button> -->
          <mt-button @click.native="backToBusinessConfig">{{ $t('返回') }}</mt-button>
        </div>
      </div>

      <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
    </div>
    <div class="config-container">
      <!-- 0. 采购申请配置 -->
      <!-- <tab-source-config v-if="tabIndex == 0"></tab-source-config> -->
      <purchase-apply-config v-if="tabIndex == 0"></purchase-apply-config>

      <!-- 1. 采购订单配置 -->
      <purchase-order-config v-if="tabIndex == 1"></purchase-order-config>

      <!-- 2. 供应商订单接收配置 -->
      <supply-order-config v-if="tabIndex == 2"></supply-order-config>

      <!-- 3. 售后订单配置 -->
      <aftersale-order-config v-if="tabIndex == 3"></aftersale-order-config>

      <!-- 4. 供应商接收售后订单 -->
      <supply-aftersale-config v-if="tabIndex == 4"></supply-aftersale-config>
    </div>
  </div>
</template>
<script>
const mainTabList = [
  {
    title: this.$t('采购申请配置')
  },
  {
    title: this.$t('采购订单配置')
  },
  {
    title: this.$t('供应商接收订单配置')
  },
  {
    title: this.$t('售后订单配置')
  },
  {
    title: this.$t('供应商接收售后订单配置')
  }
]
export default {
  components: {
    purchaseApplyConfig: require('./pages/purchaseApply/index.vue').default,
    purchaseOrderConfig: require('./pages/purchaseOrder/index.vue').default,
    supplyOrderConfig: require('./pages/supplyOrder/index.vue').default,
    aftersaleOrderConfig: require('./pages/aftersaleOrderConfig/index.vue').default,
    supplyAftersaleConfig: require('./pages/supplyAftersaleConfig/index.vue').default
  },
  data() {
    return {
      tabIndex: 0,
      configId: '',
      businessTypeId: null,
      businessTypeCode: null,
      tagList: [this.$t('一般采购')],
      configInfo: {
        businessTypeId: '',
        businessTypeCode: '',
        businessTypeName: null,
        configId: null,
        version: null
      },
      tabSource: mainTabList
    }
  },
  mounted() {
    let sourceModuleConfigInfo = JSON.parse(localStorage.sourceModuleConfigInfo)
    this.configInfo.businessTypeId = sourceModuleConfigInfo.businessTypeId
    this.configInfo.businessTypeCode = sourceModuleConfigInfo.businessTypeCode
    this.configId = sourceModuleConfigInfo.configId
    this.tagList = [sourceModuleConfigInfo.businessTypeName]
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    },
    backToBusinessConfig() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/pages/_fieldsConfig.scss';
</style>
