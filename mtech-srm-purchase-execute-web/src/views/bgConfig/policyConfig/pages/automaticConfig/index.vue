// 自动化配置
<template>
  <div class="full-height">
    <mt-template-page
      ref="template-6"
      :template-config="pageConfig6"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      class="ml20"
    >
    </mt-template-page>
    <!-- 自动化配置弹窗 -->
    <add-automatic-config-dialog
      v-if="isAutomaticDialogShow"
      @handleAutomaticDialogShow="handleAutomaticDialogShow"
      :dialog-data="dialogData"
    ></add-automatic-config-dialog>
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-name="downTemplateName"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { cloneDeep } from 'lodash'
import * as config from '../../config/index.js'
export default {
  components: {
    addAutomaticConfigDialog: require('../../components/addAutomaticConfigDialog.vue').default,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  data() {
    return {
      dialogData: null,
      downTemplateName: null, // 下载模板文件名
      downTemplateParams: {}, // 下载模板参数
      requestUrls: {}, // 上传下载接口地址
      editFlag: false, //是否可编辑
      automaticDataOrigin: [], // 源数据（用于Tab切换前对比）
      isAutomaticDialogShow: false, // 是否显示弹窗
      pageConfig6: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [],
              [
                {
                  id: 'download',
                  icon: 'icon_solid_export',
                  title: this.$t('导出'),
                  visibleCondition: () => false
                },
                {
                  id: 'upload',
                  icon: 'icon_solid_Import',
                  title: this.$t('导入'),
                  visibleCondition: () => false
                }
              ]
            ]
          },
          grid: {
            allowPaging: false,
            // autoWidthColumns: config.automaticConfigColumn.length + 1,
            lineSelection: true,
            // frozenColumns: 1,
            columnData: config.automaticConfigColumn,
            dataSource: config.automaticDataSource
          }
        }
      ]
    }
  },
  methods: {
    // 打开编辑弹窗
    handleEdit(data) {
      this.dialogData = {
        dialogType: 'edit',
        row: data
      }
      this.isAutomaticDialogShow = true
    },

    // 打开新增弹窗
    handleAdd() {
      this.isAutomaticDialogShow = true
      this.dialogData = {
        dialogType: 'add'
      }
    },

    // 删除行
    handleDelete(serialNumber) {
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t('确定删除选中行?')
        },
        success: () => {
          serialNumber.forEach((e) => {
            config.automaticDataSource.forEach((item) => {
              if (item.serialNumber == e) {
                config.automaticData.splice(config.automaticDataSource.indexOf(item), 1)
                config.automaticDataSource.splice(config.automaticDataSource.indexOf(item), 1)
              }
            })
          })
          config.automaticDataSource.forEach((e, index) => {
            e.serialNumber = index + 1
          })
        }
      })
    },

    // 获取供应商下拉
    async getSupplier() {
      await this.$API.masterData.getSupplier().then((res) => {
        config.supplierTypeList.length = 0
        res.data.forEach((e) => {
          config.supplierTypeList.push({
            ...e,
            text: `${e.supplierCode}-${e.supplierName}`
          })
        })
      })
    },

    // 获取业务类型下拉
    async getBusinessConfig() {
      await this.$API.bgConfig.getLocalBusinessTypeList({}).then((res) => {
        config.businessTypeList.length = 0
        res.data.forEach((e) => {
          config.businessTypeList.push(e)
        })
      })
    },

    // 自动化配置
    async getConfig() {
      await this.getBusinessConfig()
      await this.getSupplier()
      await this.$API.bgConfig.getOrderAutomation().then((res) => {
        this.$store.commit('endLoading')
        this.automaticDataOrigin = cloneDeep(res.data)
        this.formatOrderAutomation(cloneDeep(res.data))
      })
    },

    // 格式化自动化配置数据
    formatOrderAutomation(data) {
      config.automaticData.length = 0
      config.automaticDataSource.length = 0
      for (let i = 0; i < data.length; i++) {
        config.automaticData.push(cloneDeep(data[i]))
        let tempMap = {
          ...data[i]
        }
        tempMap.serialNumber = i + 1
        config.automaticDataSource.push(cloneDeep(tempMap))
      }
    },

    // 切换页面编辑状态
    changeEditFlag(flag) {
      if (!flag) {
        this.formatOrderAutomation(cloneDeep(this.automaticDataOrigin))
      }
      this.editFlag = flag
      this.$set(this.pageConfig6[0].grid, 'dataSource', [])
      this.$set(
        this.pageConfig6[0].toolbar,
        'tools',
        flag ? [config.editToolbar, config.importExport] : [[], config.importExport]
      )
      this.$set(this.pageConfig6[0].grid, 'dataSource', config.automaticDataSource)
    },

    handleClickToolBar(e) {
      if (e.grid.getSelectedRecords().length <= 0 && e.toolbar.id == 'Delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      let _serialNumber = []
      e.grid.getSelectedRecords().map((item) => {
        _serialNumber.push(item.serialNumber)
      })
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(_serialNumber)
      } else if (e.toolbar.id == 'upload') {
        console.log('this.handleUpload();')
        this.handleUpload()
      } else if (e.toolbar.id == 'download') {
        this.handleDownload()
      }
    },

    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.serialNumber])
      } else if (e.tool.id == 'edit') {
        this.handleEdit(e.data)
      }
    },

    handleAutomaticDialogShow(flag) {
      this.isAutomaticDialogShow = flag
    },

    // 上传（显示弹窗）
    handleUpload() {
      this.downTemplateParams = {
        flag: 0
      }
      this.requestUrls = {
        templateUrlPre: 'bgConfig',
        templateUrl: 'downloadAutomationStrategy',
        uploadUrl: 'uploadAutomationStrategy'
      }
      this.downTemplateName = this.$t('自动化配置模板')
      this.showUploadExcel(true)
    },

    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = [] // 清空数据
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },

    // 导出
    handleDownload() {
      this.$store.commit('startLoading')
      this.$API.bgConfig.downloadAutomationStrategy({ flag: 1 }).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },

    // 上传成功后，获取到的数据
    upExcelConfirm(res) {
      let tempNum = config.automaticDataSource.length
      res.data.forEach((record) => {
        let _tempRecord = {
          ...record,
          strategyConfigMap: {
            accountReconciliationAutoConfirm: Number(record.accountReconciliationAutoConfirm),
            orderAutoConfirm: Number(record.orderAutoConfirm),
            saleOrderAutoConfirm: Number(record.saleOrderAutoConfirm)
          }
        }
        delete _tempRecord.accountReconciliationAutoConfirm
        delete _tempRecord.orderAutoConfirm
        delete _tempRecord.saleOrderAutoConfirm
        // 自动化配置 用于提交的特殊配置
        config.automaticData.push(cloneDeep(_tempRecord))

        // 自动化配置 用于页面展示的特殊配置
        config.automaticDataSource.push({
          ..._tempRecord,
          serialNumber: ++tempNum
        })
      })
      this.showUploadExcel(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
    },

    sameTypeConfigCheck(arr) {
      var tempSet = new Set(arr)
      return tempSet.size !== arr.length
    },

    saveConfig() {
      if (config.automaticData.some((item) => item.supplierId == null)) {
        this.$toast({
          content: this.$t('供应商不能为空，请确认后再提交'),
          type: 'warning'
        })
        return
      }
      let tempArr = config.automaticData.map((ele) => `${ele.businessTypeId}!!!!${ele.supplierId}`)
      if (this.sameTypeConfigCheck(tempArr)) {
        this.$toast({
          content: this.$t('存在类型重复的配置，请确认后再提交'),
          type: 'warning'
        })
        return
      }
      this.$API.bgConfig.saveOrderAutomation(config.automaticData).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$store.commit('startLoading')
          this.getConfig()
        }
      })
    }
  }
}
</script>

<style></style>
