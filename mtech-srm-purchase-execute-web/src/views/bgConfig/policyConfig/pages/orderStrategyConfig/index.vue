// 采购执行
<template>
  <div class="full-height">
    <mt-template-page
      ref="template1"
      :template-config="pageConfig1"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      class="ml20"
    >
      <div
        slot="slot-filter"
        :class="['top-filter', !isTopFilterExpand && 'top-filter-small']"
        class="top-filter"
      >
        <div>
          <div class="accordion-title">{{ $t('采购订单库存预占配置') }}</div>

          <div class="sort-box" @click="isTopFilterExpand = !isTopFilterExpand">
            <mt-icon
              v-for="index in 2"
              :key="index"
              :name="isTopFilterExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
            ></mt-icon>
          </div>
        </div>
        <div class="accordion-main">
          <div class="left-status">
            <span class="titles">{{ $t('默认配置：') }}</span>
          </div>
          <div class="table-container">
            <div class="default-config-scroll">
              <div class="default-setting">
                <span class="default-setting-title">{{ $t('采购执行方式') }}</span>
                <mt-select
                  :width="230"
                  :data-source="purExecutionMethodOptions"
                  :placeholder="$t('请选择')"
                  :disabled="!editFlag"
                  css-class="input-select"
                  @focus="getPurExecutionMethod"
                  @change="purExecutionMethodChange"
                  v-model="defaultOption.purExecutionMethod"
                ></mt-select>
              </div>
              <div class="default-setting">
                <span class="default-setting-title">{{ $t('采购订单审批通过后自动发布') }}</span>
                <mt-radio
                  v-model="defaultOption.autoReleaseFlag"
                  :data-source="radioData"
                  @input="onchangDefAutoReleaseFlag"
                ></mt-radio>
              </div>
              <div class="default-setting">
                <span class="default-setting-title">{{ $t('供方拒绝后订单自动取消') }}</span>
                <mt-radio
                  v-model="defaultOption.autoCancelFlag"
                  :data-source="radioData"
                  @input="onchangDefAutoCancelFlag"
                ></mt-radio>
              </div>
              <div class="default-setting">
                <span class="default-setting-title">{{
                  $t('供方确认订单后采购订单允许修改')
                }}</span>
                <mt-radio
                  v-model="defaultOption.allowModifyFlag"
                  :data-source="radioData"
                  @input="onchangDefAllowModifyFlag"
                ></mt-radio>
              </div>
              <!-- 当前版本不需要 -->
              <!-- <div class="default-setting">
              <span class="default-setting-title">{{ $t('是否需要妥投') }}</span>
              <mt-radio
                v-model="defaultOption.deliverFlag"
                :data-source="radioData"
                @input="onchangDefDeliverFlag"
              ></mt-radio>
            </div> -->
            </div>
          </div>
        </div>
      </div>

      <div slot="slot-filter" class="second-top-filter">
        <div class="second-left-status">
          <span class="titles">{{ $t('特殊配置：') }}</span>
        </div>
      </div>
    </mt-template-page>
    <!-- 采购执行配置弹窗 -->
    <add-order-strategy-dialog
      v-if="isOrderStrategyDialogShow"
      @handleOrderStrategyDialogShow="handleOrderStrategyDialogShow"
      :dialog-data="dialogData"
    ></add-order-strategy-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import * as config from '../../config/index.js'
export default {
  components: {
    addOrderStrategyDialog: require('../../components/addOrderStrategyDialog.vue').default
  },
  data() {
    return {
      dialogData: null,
      isOrderStrategyDialogShow: false, // 是否显示弹窗
      orderStrategyOrigin: [], // 源数据（用于Tab切换前对比）
      editFlag: false, //是否可编辑
      // 单选按钮
      radioData: [
        {
          label: this.$t('是'),
          value: '1',
          disabled: !this.editFlag
        },
        {
          label: this.$t('否'),
          value: '0',
          disabled: !this.editFlag
        }
      ],
      // 采购执行默认配置
      defaultOption: {
        allowModifyFlag: null,
        autoCancelFlag: null,
        autoReleaseFlag: null,
        deliverFlag: null,
        purExecutionMethod: null
      },
      isTopFilterExpand: true,
      purExecutionMethodOptions: config.purExecutionMethodOptions,
      pageConfig1: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[]]
          },
          grid: {
            allowPaging: false,
            allowEditing: false,
            // frozenColumns: 1,
            lineSelection: true,
            autoWidthColumns: config.procurementExecColumn.length + 1,
            columnData: config.procurementExecColumn,
            dataSource: config.orderStrategyDataSource
          }
        }
      ]
    }
  },
  methods: {
    handleAdd() {
      this.isOrderStrategyDialogShow = true
      this.dialogData = {
        dialogType: 'add'
      }
    },
    handleDelete(serialNumber) {
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t('确定删除选中行?')
        },
        success: () => {
          serialNumber.forEach((e) => {
            config.orderStrategyDataSource.forEach((item) => {
              if (item.serialNumber == e) {
                config.orderStrategySpecData.splice(config.orderStrategyDataSource.indexOf(item), 1)
                config.orderStrategyDataSource.splice(
                  config.orderStrategyDataSource.indexOf(item),
                  1
                )
              }
            })
          })
          config.orderStrategyDataSource.forEach((e, index) => {
            e.serialNumber = index + 1
          })
        }
      })
    },

    handleOrderStrategyDialogShow(flag) {
      this.isOrderStrategyDialogShow = flag
    },

    changeEditFlag(flag) {
      if (!flag) {
        this.formatOrderStrategyConfig(cloneDeep(this.orderStrategyOrigin))
      }
      this.editFlag = flag
      this.radioData.forEach((element) => {
        element.disabled = !flag
      })
      this.$set(this.pageConfig1[0].grid, 'dataSource', [])
      this.$set(this.pageConfig1[0].toolbar, 'tools', flag ? [config.editToolbar] : [[]])
      this.$set(this.pageConfig1[0].grid, 'dataSource', config.orderStrategyDataSource)
    },
    onchangDefAutoReleaseFlag(e) {
      config.orderStrategyDefaultData[0].strategyConfigMap.autoReleaseFlag = Number(e)
    },
    onchangDefAutoCancelFlag(e) {
      config.orderStrategyDefaultData[0].strategyConfigMap.autoCancelFlag = Number(e)
    },

    onchangDefAllowModifyFlag(e) {
      config.orderStrategyDefaultData[0].strategyConfigMap.allowModifyFlag = Number(e)
    },

    onchangDefDeliverFlag(e) {
      config.orderStrategyDefaultData[0].strategyConfigMap.deliverFlag = Number(e)
    },
    purExecutionMethodChange(e) {
      config.orderStrategyDefaultData[0].strategyConfigMap.purExecutionMethod = e.value
    },
    handleClickToolBar(e) {
      if (e.grid.getSelectedRecords().length <= 0 && e.toolbar.id == 'Delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      let _serialNumber = []
      e.grid.getSelectedRecords().map((item) => {
        _serialNumber.push(item.serialNumber)
      })
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(_serialNumber)
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.serialNumber], [e.data.id])
      }
    },

    // 查询采购订单策略配置
    async getConfig() {
      await this.getPurExecutionMethod()
      await this.getBusinessConfig()
      await this.getOrderType()
      await this.$API.bgConfig.getOrderStrategy().then((res) => {
        this.$store.commit('endLoading')
        this.orderStrategyOrigin = cloneDeep(res.data)
        this.formatOrderStrategyConfig(cloneDeep(this.orderStrategyOrigin))
      })
    },

    // 格式化采购订单策略配置数据
    formatOrderStrategyConfig(data) {
      config.orderStrategySpecData.length = 0
      config.orderStrategyDataSource.length = 0
      let serialNumber = 0
      for (let i = 0; i < data.length; i++) {
        if (data[i].commonFlag == 1) {
          config.orderStrategyDefaultData.length = 0
          config.orderStrategyDefaultData.push(cloneDeep(data[i]))
          this.defaultOption = cloneDeep(data[i].strategyConfigMap)
          for (var key in this.defaultOption) {
            if (key != 'purExecutionMethod') {
              this.defaultOption[key] = this.defaultOption[key].toString()
            }
          }
        } else {
          config.orderStrategySpecData.push(cloneDeep(data[i]))
          let tempMap
          if (data[i].strategyConfigMap == null) {
            data[i].strategyConfigMap = {
              allowModifyFlag: 0,
              autoCancelFlag: 0,
              autoReleaseFlag: 0,
              autoSourcingFlag: 0,
              deliverFlag: 0,
              isSplitFlag: 0,
              purExecutionMethod: null
            }
          }
          tempMap = {
            ...data[i],
            serialNumber: ++serialNumber
          }
          config.orderStrategyDataSource.editFlag = this.editFlag
          config.orderStrategyDataSource.push(cloneDeep(tempMap))
        }
      }
    },

    // 获取业务类型下拉
    async getBusinessConfig() {
      await this.$API.bgConfig.getLocalBusinessTypeList({}).then((res) => {
        config.businessTypeList.length = 0
        res.data.forEach((e) => {
          config.businessTypeList.push(e)
        })
      })
    },

    // 获取订单类型下拉
    async getOrderType() {
      await this.$API.masterData.getDictCode({ dictCode: 'OrderType' }).then((res) => {
        config.orderTypeOptions.length = 0
        res.data.forEach((item) => {
          item.message = item.itemName
          item.code = item.itemCode
          config.orderTypeOptions.push(item)
        })
      })
    },

    // 获取采购方式下拉列表
    async getPurExecutionMethod() {
      await this.$API.bgConfig.getPurExecutionMethod().then((res) => {
        if (res.code === 200) {
          config.purExecutionMethodOptions.length = 0
          res.data.forEach((e) => {
            config.purExecutionMethodOptions.push({
              value: e.code,
              text: e.message
            })
          })
        }
      })
    },

    sameTypeConfigCheck(arr) {
      var tempSet = new Set(arr)
      return tempSet.size !== arr.length
    },

    saveConfig() {
      let tempArr = config.orderStrategySpecData.map(
        (ele) => `${ele.businessTypeId}!!!!${ele.orderTypeId}`
      )

      if (this.sameTypeConfigCheck(tempArr)) {
        this.$toast({
          content: this.$t('存在类型重复的特殊配置，请确认后再提交'),
          type: 'warning'
        })
        return
      }

      let orderStrategy = {
        orderStrategy: config.orderStrategyDefaultData.concat(config.orderStrategySpecData)
      }
      this.$API.bgConfig.saveOrderStrategy(orderStrategy).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$store.commit('startLoading')
          this.getConfig()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.top-filter {
  background: #fff;
  padding: 20px 20px 20px 0;
  font-weight: 500;

  .accordion-main {
    margin-right: 20px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 25px;
  }

  .left-status {
    margin: 0px 20px 20px 0px;
    clear: both;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  &-small {
    height: 50px;
    overflow: hidden;
  }
}
.second-top-filter {
  background: #fff;
  padding-bottom: 5px;
  font-weight: 500;

  .second-left-status {
    height: 20px;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  &-small {
    height: 50px;
    overflow: hidden;
  }
}
.default-setting {
  width: 240px;
  height: 70px;
  float: left;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: normal;
  margin-right: 20px;
  color: rgba(41, 41, 41, 1);
  &-small {
    height: 60px;
  }
  &-title {
    height: 14px;
    display: block;
    margin-bottom: 20px;
    &-large {
      display: block;
      margin-bottom: 20px;
      width: 320px;
    }
  }
  &-suppliertab {
    width: 500px;
    default-setting-title {
      display: block;
      margin-bottom: 20px;
    }
  }
  &:nth-child(6) {
    width: 163px;
  }
}
.accordion-main {
  margin-right: 20px;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 25px;
}

.left-status {
  margin: 0px 20px 20px 0px;
  clear: both;
}
.accordion-title {
  float: left;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  text-indent: 10px;
  border-left: 5px solid #00469c;
  margin-bottom: 20px;
  margin-left: 20px;
  border-radius: 2px 0 0 2px;
}

.sort-box {
  position: relative;
  cursor: pointer;
  width: 5px;
  float: right;
  margin: 15px 15px 0px 0px;

  .mt-icons {
    font-size: 12px;
    transform: scale(0.5);
    color: rgba(0, 70, 156, 1);
    margin-top: -15px;
    position: absolute;
    top: 0;
    &:nth-child(2) {
      top: 6px;
    }
  }
}
.mt-radio /deep/ .e-radio-wrapper {
  margin-right: 40px;
}
</style>
