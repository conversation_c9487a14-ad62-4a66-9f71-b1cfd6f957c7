// 采购申请
<template>
  <div class="full-height">
    <mt-template-page
      ref="template2"
      :template-config="pageConfig2"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      class="ml20"
    >
      <div
        slot="slot-filter"
        :class="['top-filter', !isOrderApplyFilterExpand && 'top-filter-small']"
        class="top-filter"
      >
        <div>
          <div class="accordion-title">{{ $t('采购申请配置') }}</div>

          <div class="sort-box" @click="isOrderApplyFilterExpand = !isOrderApplyFilterExpand">
            <mt-icon
              v-for="index in 2"
              :key="index"
              :name="isOrderApplyFilterExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
            ></mt-icon>
          </div>
        </div>
        <div class="accordion-main">
          <div class="left-status">
            <span class="titles">{{ $t('默认配置：') }}</span>
          </div>
          <div class="default-setting-small">
            <span class="default-setting-title">{{ $t('采购申请行拆成每行一个单据') }}</span>
            <mt-radio
              v-model="defaultOrderApplyOption.isSplitFlag"
              :data-source="radioData"
              @input="onchangDefSplitFlag"
            ></mt-radio>
          </div>
          <!-- 当前版本不需要 -->
          <!-- <div class="default-setting">
              <span class="default-setting-title-large"
                >无价格自动转寻源（按照采购申请单转寻源订单）</span
              >
              <mt-radio
                v-model="defaultOrderApplyOption.autoSourcingFlag"
                :data-source="radioData"
                @input="onchangDefAutoSourcingFlag"
              ></mt-radio>
            </div> -->
        </div>
      </div>

      <div slot="slot-filter" class="second-top-filter">
        <div class="second-left-status">
          <span>{{ $t('特殊配置：') }}</span>
        </div>
      </div>
    </mt-template-page>
    <!-- 采购申请弹窗 -->
    <add-order-apply-strategy-dialog
      v-if="isOrderApplyStrategyDialogShow"
      @handleOrderApplyStrategyDialogShow="handleOrderApplyStrategyDialogShow"
      :dialog-data="dialogData"
    ></add-order-apply-strategy-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import * as config from '../../config/index.js'
export default {
  components: {
    addOrderApplyStrategyDialog: require('../../components/addOrderApplyStrategyDialog.vue').default
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogData: null,
      editFlag: false, //是否可编辑
      isOrderApplyStrategyDialogShow: false, // 是否显示弹窗
      orderApplyStrategyOrigin: [], // 源数据（用于Tab切换前对比）
      // 采购申请默认配置
      defaultOrderApplyOption: {
        isSplitFlag: null,
        autoSourcingFlag: null
      },
      radioData: [
        {
          label: this.$t('是'),
          value: '1',
          disabled: !this.editFlag
        },
        {
          label: this.$t('否'),
          value: '0',
          disabled: !this.editFlag
        }
      ],
      isOrderApplyFilterExpand: true,
      pageConfig2: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[]]
          },
          grid: {
            allowPaging: false,
            // frozenColumns: 1,
            lineSelection: true,
            autoWidthColumns: config.orderApplyColumn.length + 1,
            columnData: config.orderApplyColumn,
            dataSource: config.orderApplyStrategyDataSource
          }
        }
      ]
    }
  },
  methods: {
    onchangDefAutoSourcingFlag(e) {
      config.orderApplyStrategyDefaultData[0].strategyConfigMap.autoSourcingFlag = Number(e)
    },

    handleOrderApplyStrategyDialogShow(flag) {
      this.isOrderApplyStrategyDialogShow = flag
    },
    handleAdd() {
      this.isOrderApplyStrategyDialogShow = true
      this.dialogData = {
        dialogType: 'add'
      }
    },
    changeEditFlag(flag) {
      if (!flag) {
        this.formatOrderApplyStrategy(cloneDeep(this.orderApplyStrategyOrigin))
      }
      this.editFlag = flag
      this.radioData.forEach((element) => {
        element.disabled = !flag
      })
      this.$set(this.pageConfig2[0].grid, 'dataSource', [])
      this.$set(this.pageConfig2[0].toolbar, 'tools', flag ? [config.editToolbar] : [[]])
      this.$set(this.pageConfig2[0].grid, 'dataSource', config.orderApplyStrategyDataSource)
    },
    // 获取业务类型下拉
    async getBusinessConfig() {
      await this.$API.bgConfig.getLocalBusinessTypeList({}).then((res) => {
        config.businessTypeList.length = 0
        res.data.forEach((e) => {
          config.businessTypeList.push(e)
        })
      })
    },

    // 采购申请配置
    async getConfig() {
      await this.getBusinessConfig()
      await this.$API.bgConfig.getOrderApplyStrategy().then((res) => {
        this.$store.commit('endLoading')
        this.orderApplyStrategyOrigin = cloneDeep(res.data)
        this.formatOrderApplyStrategy(cloneDeep(res.data))
      })
    },

    // 格式化采购申请配置数据
    formatOrderApplyStrategy(data) {
      config.orderApplyStrategySpecData.length = 0
      config.orderApplyStrategyDataSource.length = 0
      let serialNumber = 0
      for (let i = 0; i < data.length; i++) {
        if (data[i].commonFlag == 1) {
          config.orderApplyStrategyDefaultData.length = 0
          config.orderApplyStrategyDefaultData.push(data[i])
          this.defaultOrderApplyOption = cloneDeep(data[i].strategyConfigMap)
          for (var key in this.defaultOrderApplyOption) {
            this.defaultOrderApplyOption[key] = this.defaultOrderApplyOption[key].toString()
          }
        } else {
          config.orderApplyStrategySpecData.push(cloneDeep(data[i]))
          let tempMap
          if (data[i].strategyConfigMap == null) {
            data[i].strategyConfigMap = {
              allowModifyFlag: 0,
              autoCancelFlag: 0,
              autoReleaseFlag: 0,
              autoSourcingFlag: 0,
              deliverFlag: 0,
              isSplitFlag: 0
            }
          }
          tempMap = {
            ...data[i],
            serialNumber: ++serialNumber
          }
          config.orderApplyStrategyDataSource.push(cloneDeep(tempMap))
        }
      }
    },
    onchangDefSplitFlag(e) {
      config.orderApplyStrategyDefaultData[0].strategyConfigMap.isSplitFlag = Number(e)
    },
    handleClickToolBar(e) {
      if (e.grid.getSelectedRecords().length <= 0 && e.toolbar.id == 'Delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      let _serialNumber = []
      e.grid.getSelectedRecords().map((item) => {
        _serialNumber.push(item.serialNumber)
      })
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(_serialNumber)
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.serialNumber])
      }
    },
    handleDelete(serialNumber) {
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t('确定删除选中行?')
        },
        success: () => {
          serialNumber.forEach((e) => {
            config.orderApplyStrategyDataSource.forEach((item) => {
              if (item.serialNumber == e) {
                config.orderApplyStrategySpecData.splice(
                  config.orderApplyStrategyDataSource.indexOf(item),
                  1
                )
                config.orderApplyStrategyDataSource.splice(
                  config.orderApplyStrategyDataSource.indexOf(item),
                  1
                )
              }
            })
          })
          config.orderApplyStrategyDataSource.forEach((e, index) => {
            e.serialNumber = index + 1
          })
        }
      })
    },

    sameTypeConfigCheck(arr) {
      var tempSet = new Set(arr)
      return tempSet.size !== arr.length
    },

    saveConfig() {
      let tempArr = config.orderApplyStrategySpecData.map((ele) => ele.businessTypeId)
      if (this.sameTypeConfigCheck(tempArr)) {
        this.$toast({
          content: this.$t('存在类型重复的特殊配置，请确认后再提交'),
          type: 'warning'
        })
        return
      }

      let orderApplyStrategy = {
        orderStrategy: config.orderApplyStrategyDefaultData.concat(
          config.orderApplyStrategySpecData
        )
      }
      this.$API.bgConfig.saveOrderApplyStrategy(orderApplyStrategy).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$store.commit('startLoading')
          this.getConfig()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.top-filter {
  background: #fff;
  padding: 20px 20px 20px 0;
  font-weight: 500;

  .accordion-main {
    margin-right: 20px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 25px;
  }

  .left-status {
    margin: 0px 20px 20px 0px;
    clear: both;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  &-small {
    height: 50px;
    overflow: hidden;
  }
}
.second-top-filter {
  background: #fff;
  padding-bottom: 5px;
  font-weight: 500;

  .second-left-status {
    height: 20px;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
  &-small {
    height: 50px;
    overflow: hidden;
  }
}
.default-setting {
  width: 240px;
  height: 70px;
  float: left;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: normal;
  margin-right: 20px;
  color: rgba(41, 41, 41, 1);
  &-small {
    height: 60px;
  }
  &-title {
    height: 14px;
    display: block;
    margin-bottom: 20px;
    &-large {
      display: block;
      margin-bottom: 20px;
      width: 320px;
    }
  }
  &-suppliertab {
    width: 500px;
    default-setting-title {
      display: block;
      margin-bottom: 20px;
    }
  }
  &:nth-child(6) {
    width: 163px;
  }
}
.accordion-main {
  margin-right: 20px;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 25px;
}

.left-status {
  margin: 0px 20px 20px 0px;
  clear: both;
}
.accordion-title {
  float: left;
  font-size: 14px;
  margin-left: 20px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  text-indent: 10px;
  border-left: 5px solid #00469c;
  margin-bottom: 20px;
  border-radius: 2px 0 0 2px;
}

.sort-box {
  position: relative;
  cursor: pointer;
  width: 5px;
  float: right;
  margin: 15px 15px 0px 0px;

  .mt-icons {
    font-size: 12px;
    transform: scale(0.5);
    color: rgba(0, 70, 156, 1);
    margin-top: -15px;
    position: absolute;
    top: 0;
    &:nth-child(2) {
      top: 6px;
    }
  }
}
.mt-radio /deep/ .e-radio-wrapper {
  margin-right: 40px;
}
</style>
