// 验收项配置
<template>
  <div class="full-height">
    <mt-template-page
      ref="template5"
      :template-config="pageConfig5"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      class="ml20"
    >
    </mt-template-page>
    <!-- 验收项配置弹窗 -->
    <add-acceptance-config-dialog
      v-if="isAcceptanceDialogShow"
      @handleAcceptanceDialogShow="handleAcceptanceDialogShow"
      @confirmSuccess="confirmSuccess"
      :dialog-data="dialogData"
    ></add-acceptance-config-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import * as config from '../../config/index.js'
export default {
  components: {
    addAcceptanceConfigDialog: require('../../components/addAcceptanceConfigDialog.vue').default
  },
  data() {
    return {
      editFlag: false, //是否可编辑
      dialogData: null,
      acceptanceDataOrigin: [], // 源数据（用于Tab切换前对比）
      isAcceptanceDialogShow: false, // 是否显示弹窗
      pageConfig5: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[]]
          },
          grid: {
            allowPaging: false,
            lineSelection: true,
            // frozenColumns: 1,
            columnData: config.acceptanceConfigColumn,
            dataSource: config.acceptanceDataSource
          }
        }
      ]
    }
  },
  methods: {
    // 弹窗修改数据提交后刷新数据源
    confirmSuccess() {
      this.$set(this.pageConfig5[0].grid, 'dataSource', [])
      this.$set(this.pageConfig5[0].grid, 'dataSource', config.acceptanceDataSource)
    },

    // 打开编辑弹窗
    handleEdit(data) {
      this.dialogData = {
        dialogType: 'edit',
        row: data
      }
      this.isAcceptanceDialogShow = true
    },

    // 打开新增弹窗
    handleAdd() {
      this.isAcceptanceDialogShow = true
      this.dialogData = {
        dialogType: 'add'
      }
    },

    // 删除行
    handleDelete(serialNumber) {
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t('确定删除选中行?')
        },
        success: () => {
          serialNumber.forEach((e) => {
            config.acceptanceDataSource.forEach((item) => {
              if (item.serialNumber == e) {
                config.acceptanceData.splice(config.acceptanceDataSource.indexOf(item), 1)
                config.acceptanceDataSource.splice(config.acceptanceDataSource.indexOf(item), 1)
              }
            })
          })
          config.acceptanceDataSource.forEach((e, index) => {
            e.serialNumber = index + 1
          })
        }
      })
    },

    // 验收项配置
    getConfig() {
      this.$API.bgConfig.getOrderAcceptance().then((res) => {
        this.$store.commit('endLoading')
        this.acceptanceDataOrigin = cloneDeep(res.data)
        this.formatOrderAcceptance(cloneDeep(res.data))
      })
    },

    // 格式化验收项配置数据
    formatOrderAcceptance(data) {
      config.acceptanceData.length = 0
      config.acceptanceDataSource.length = 0
      for (let i = 0; i < data.length; i++) {
        config.acceptanceData.push(data[i])
        let tempMap = cloneDeep(data[i])
        tempMap.serialNumber = i + 1
        config.acceptanceDataSource.push(tempMap)
      }
    },

    // 改变页面编辑状态
    changeEditFlag(flag) {
      if (!flag) {
        this.formatOrderAcceptance(cloneDeep(this.acceptanceDataOrigin))
      }
      this.editFlag = flag
      this.$set(this.pageConfig5[0].grid, 'dataSource', [])
      this.$set(this.pageConfig5[0].toolbar, 'tools', flag ? [config.editToolbar] : [[]])
      this.$set(this.pageConfig5[0].grid, 'dataSource', config.acceptanceDataSource)
    },

    handleClickToolBar(e) {
      if (e.grid.getSelectedRecords().length <= 0 && e.toolbar.id == 'Delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      let _serialNumber = []
      e.grid.getSelectedRecords().map((item) => {
        _serialNumber.push(item.serialNumber)
      })
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(_serialNumber)
      }
    },

    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.serialNumber])
      } else if (e.tool.id == 'edit') {
        this.handleEdit(e.data)
      }
    },

    handleAcceptanceDialogShow(flag) {
      this.isAcceptanceDialogShow = flag
    },

    // 检查是否有重复配置
    sameTypeConfigCheck(arr) {
      var tempSet = new Set(arr)
      return tempSet.size !== arr.length
    },

    // 保存配置
    saveConfig() {
      let tempArr = config.acceptanceData.map((ele) => ele.acceptanceCode)
      if (this.sameTypeConfigCheck(tempArr)) {
        this.$toast({
          content: this.$t('存在重复的验收项编号，请确认后再提交'),
          type: 'warning'
        })
        return
      }
      this.$API.bgConfig.saveOrderAcceptance(config.acceptanceData).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$store.commit('startLoading')
          this.getConfig()
        }
      })
    }
  }
}
</script>

<style></style>
