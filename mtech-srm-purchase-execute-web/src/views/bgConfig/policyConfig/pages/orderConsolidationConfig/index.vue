<template>
  <div>
    <div id="purchaseAccordion" :class="['outerBox', !isExpand && 'outerBox-small']">
      <div class="accordion-title ml20">
        {{ $t('采购申请转订单/手工创建采购订单并单配置') }}
      </div>

      <div class="sort-box" @click="isExpand = !isExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <purchase-component
        ref="purchaseRef"
        :purchase-data="purchaseData"
        v-if="purchaseData.length > 0"
      ></purchase-component>
    </div>

    <div
      id="searchSourceAccordion"
      :class="['outerBox', !isSearchSourceExpand && 'outerBox-small']"
      class="outerBox"
    >
      <div class="accordion-title ml20">{{ $t('寻源需求并单配置') }}</div>

      <div class="sort-box" @click="isSearchSourceExpand = !isSearchSourceExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isSearchSourceExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <search-source
        ref="searchSourceRef"
        :search-source-data="searchSourceData"
        v-if="searchSourceData.length > 0"
      ></search-source>
    </div>

    <div
      id="supplierAccordion"
      :class="['outerBox', !isSupplierExpand && 'outerBox-small']"
      class="outerBox"
    >
      <div class="accordion-title ml20">
        {{ $t('供应商送货单合并生成配置') }}
      </div>

      <div class="sort-box" @click="isSupplierExpand = !isSupplierExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isSupplierExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
      <supplier-component
        ref="supplierRef"
        :supplier-data="supplierData"
        v-if="supplierData.length > 0"
      ></supplier-component>
    </div>
  </div>
</template>

// 并单策略
<script>
import { cloneDeep } from 'lodash'
import * as config from '../../config/index.js'
export default {
  components: {
    purchaseComponent: require('../../components/purchaseComponent.vue').default,
    searchSource: require('../../components/searchSource.vue').default,
    supplierComponent: require('../../components/supplierComponent.vue').default
  },
  props: {},
  data() {
    return {
      editFlag: false, //是否可编辑
      orderConfigDataOrigin: [], // 源数据（用于Tab切换前对比）
      // 顶部是否折叠
      isExpand: true,
      isSearchSourceExpand: true,
      isSupplierExpand: true,
      purchaseData: config.purchaseDataSource,
      searchSourceData: config.searchSourceDataSource,
      supplierData: config.supplierDataSource
    }
  },
  mounted() {
    this.$store.commit('startLoading')
    this.getConfig()
  },
  methods: {
    // 查询并单策略配置
    getConfig() {
      this.$API.bgConfig.getOrderConsolidationConfig().then((res) => {
        this.$store.commit('endLoading')
        // 保存源数据，用于对比是否有数据变更
        this.orderConfigDataOrigin = cloneDeep(res.data)
        this.formatOrderConsolidationConfig(cloneDeep(res.data))
      })
    },

    // 格式化并单策略配置数据
    formatOrderConsolidationConfig(data) {
      config.orderConfigData.length = 0
      data.forEach((record, index) => {
        config.orderConfigData.push(cloneDeep(record))
        // 解析采购并单数据
        if (index == 0) {
          let tempConsolidationData = record
          let _businessTypeId = [],
            _businessTypeName = [],
            _singleRowData = {},
            // 固定列
            _columnData = [this.$t('序号'), this.$t('业务类型')]
          config.purchaseDataSource.length = 0
          // 根据相同的业务类型拼接出表头数据
          tempConsolidationData.orderConsolidationConfigDTOList.map((item) => {
            if (_businessTypeId.indexOf(item.businessTypeId) < 0) {
              _businessTypeId.push(item.businessTypeId)
              _businessTypeName.push(item.businessTypeName)
            }
            if (_columnData.indexOf(item.ruleName) < 0) {
              _columnData.push(item.ruleName)
            }
          })
          // 根据表头将每个单元格组成的数组分解为以行为单位的数组
          for (let i = 0; i < _businessTypeId.length; i++) {
            _singleRowData.serialNumber = i + 1
            _singleRowData.businessType = _businessTypeName[i]
            for (let j = 2; j < _columnData.length; j++) {
              _singleRowData['rule' + (j - 1)] =
                tempConsolidationData.orderConsolidationConfigDTOList.shift()
            }
            config.purchaseDataSource.push(cloneDeep(_singleRowData))
            _singleRowData.length = 0
          }
          // 用接口返回的表头信息替换本地默认值
          _columnData.forEach((item, index) => {
            config.purchaseColumnData[index].headerText = item
          })
        } else if (index == 1) {
          let tempConsolidationData = record
          let _businessTypeId = [],
            _businessTypeName = [],
            _singleRowData = {},
            _columnData = [this.$t('序号'), this.$t('业务类型')]
          config.searchSourceDataSource.length = 0
          tempConsolidationData.orderConsolidationConfigDTOList.map((item) => {
            if (_businessTypeId.indexOf(item.businessTypeId) < 0) {
              _businessTypeId.push(item.businessTypeId)
              _businessTypeName.push(item.businessTypeName)
            }
            if (_columnData.indexOf(item.ruleName) < 0) {
              _columnData.push(item.ruleName)
            }
          })
          for (let i = 0; i < _businessTypeId.length; i++) {
            _singleRowData.serialNumber = i + 1
            _singleRowData.businessType = _businessTypeName[i]
            for (let j = 2; j < _columnData.length; j++) {
              _singleRowData['rule' + (j - 1)] =
                tempConsolidationData.orderConsolidationConfigDTOList.shift()
            }
            config.searchSourceDataSource.push(JSON.parse(JSON.stringify(_singleRowData)))
            _singleRowData.length = 0
          }

          _columnData.forEach((item, index) => {
            config.searchSourceColumnData[index].headerText = item
          })
        } else if (index == 2) {
          let tempConsolidationData = record
          let _businessTypeId = [],
            _businessTypeName = [],
            _orderTypeName = [],
            _singleRowData = {},
            _columnData = [this.$t('序号'), this.$t('业务类型'), this.$t('订单类型')]
          config.supplierDataSource.length = 0
          tempConsolidationData.orderConsolidationConfigDTOList.map((item) => {
            if (_businessTypeId.indexOf(item.businessTypeId + item.orderTypeId) < 0) {
              _businessTypeId.push(item.businessTypeId + item.orderTypeId)
              _businessTypeName.push(item.businessTypeName)
              _orderTypeName.push(item.orderTypeName)
            }
            if (_columnData.indexOf(item.ruleName) < 0) {
              _columnData.push(item.ruleName)
            }
          })
          for (let i = 0; i < _businessTypeId.length; i++) {
            _singleRowData.serialNumber = i + 1
            _singleRowData.businessType = _businessTypeName[i]
            _singleRowData.orderType = _orderTypeName[i]
            for (let j = 3; j < _columnData.length; j++) {
              _singleRowData['rule' + (j - 2)] =
                tempConsolidationData.orderConsolidationConfigDTOList.shift()
            }
            config.supplierDataSource.push(JSON.parse(JSON.stringify(_singleRowData)))
            _singleRowData.length = 0
          }

          _columnData.forEach((item, index) => {
            config.supplierColumnData[index].headerText = item
          })
        }
      })
    },

    changeEditFlag(flag) {
      if (!flag) {
        this.formatOrderConsolidationConfig(cloneDeep(this.orderConfigDataOrigin))
      }
      this.editFlag = flag
      this.$set(this.$refs.purchaseRef.pageConfig[0].grid, 'dataSource', [])
      this.$set(this.$refs.purchaseRef.pageConfig[0].grid, 'dataSource', this.purchaseData)
      this.$set(this.$refs.searchSourceRef.pageConfig[0].grid, 'dataSource', [])
      this.$set(this.$refs.searchSourceRef.pageConfig[0].grid, 'dataSource', this.searchSourceData)
      this.$set(this.$refs.supplierRef.pageConfig[0].grid, 'dataSource', [])
      this.$set(this.$refs.supplierRef.pageConfig[0].grid, 'dataSource', this.supplierData)
    },
    saveConfig() {
      this.$API.bgConfig.saveOrderConsolidationConfig(config.orderConfigData).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$store.commit('startLoading')
          this.getConfig()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.outerBox {
  // width: 1630px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px;
  margin-top: 20px;
  padding-right: 20px;
  padding-top: 20px;
  padding-bottom: 20px;
  &-small {
    height: 55px;
    overflow: hidden;
  }
}
.accordion-title {
  float: left;
  font-size: 14px;
  margin-left: 20px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(41, 41, 41, 1);
  text-indent: 10px;
  border-left: 5px solid #00469c;
  margin-bottom: 20px;
  border-radius: 2px 0 0 2px;
}

.sort-box {
  position: relative;
  cursor: pointer;
  width: 5px;
  float: right;
  margin: 15px 15px 0px 0px;

  .mt-icons {
    font-size: 12px;
    transform: scale(0.5);
    color: rgba(0, 70, 156, 1);
    margin-top: -15px;
    position: absolute;
    top: 0;
    &:nth-child(2) {
      top: 6px;
    }
  }
}
</style>
