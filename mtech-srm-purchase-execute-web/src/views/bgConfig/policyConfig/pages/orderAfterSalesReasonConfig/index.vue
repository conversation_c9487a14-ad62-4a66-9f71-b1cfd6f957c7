// 售后原因
<template>
  <div class="full-height">
    <mt-template-page
      ref="template-4"
      :template-config="pageConfig4"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      class="ml20"
    >
    </mt-template-page>
    <!-- 售后原因弹窗 -->
    <add-dialog
      v-if="addDialogShow"
      :dialog-data="dialogData"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></add-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import * as config from '../../config/index.js'
export default {
  components: {
    addDialog: require('../../components/addDialog.vue').default
  },
  data() {
    return {
      dialogData: null,
      orderAfterSalesReasonOrigin: [], // 源数据（用于Tab切换前对比）
      addDialogShow: false, // 是否显示弹窗
      editFlag: false, //是否可编辑
      pageConfig4: [
        {
          toolbar: {
            useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[]]
          },
          grid: {
            allowPaging: false,
            lineSelection: true,
            columnData: config.aftermarketReasonColumn,
            autoWidthColumns: config.aftermarketReasonColumn.length + 1,
            dataSource: config.orderAfterSalesReasonDataSource
            // frozenColumns: 1,
          }
        }
      ]
    }
  },
  methods: {
    // 弹窗修改数据提交后刷新数据源
    confirmSuccess() {
      this.$set(this.pageConfig4[0].grid, 'dataSource', [])
      this.$set(this.pageConfig4[0].grid, 'dataSource', config.orderAfterSalesReasonDataSource)
    },
    handleEdit(data) {
      this.dialogData = {
        dialogType: 'edit',
        row: data
      }
      this.addDialogShow = true
    },
    handleAdd() {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'add'
      }
    },
    handleDelete(serialNumber) {
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t('确定删除选中行?')
        },
        success: () => {
          serialNumber.forEach((e) => {
            config.orderAfterSalesReasonDataSource.forEach((item) => {
              if (item.serialNumber == e) {
                config.orderAfterSalesReasonData.splice(
                  config.orderAfterSalesReasonDataSource.indexOf(item),
                  1
                )
                config.orderAfterSalesReasonDataSource.splice(
                  config.orderAfterSalesReasonDataSource.indexOf(item),
                  1
                )
              }
            })
          })
          config.orderAfterSalesReasonDataSource.forEach((e, index) => {
            e.serialNumber = index + 1
          })
        }
      })
    },
    // 查询订单售后原因
    getConfig() {
      this.$API.bgConfig.getOrderAfterSalesReason().then((res) => {
        this.$store.commit('endLoading')
        this.orderAfterSalesReasonOrigin = cloneDeep(res.data)
        this.formatOrderAfterSalesReason(cloneDeep(res.data))
      })
    },

    // 格式化售后原因数据
    formatOrderAfterSalesReason(data) {
      config.orderAfterSalesReasonData.length = 0
      config.orderAfterSalesReasonDataSource.length = 0
      for (let i = 0; i < data.length; i++) {
        config.orderAfterSalesReasonData.push(cloneDeep(data[i]))
        let tempMap = { ...data[i], serialNumber: i + 1 }
        config.orderAfterSalesReasonDataSource.push(tempMap)
      }
    },
    changeEditFlag(flag) {
      if (!flag) {
        this.formatOrderAfterSalesReason(cloneDeep(this.orderAfterSalesReasonOrigin))
      }
      this.editFlag = flag
      this.$set(this.pageConfig4[0].grid, 'dataSource', [])
      this.$set(this.pageConfig4[0].toolbar, 'tools', flag ? [config.editToolbar] : [[]])
      this.$set(this.pageConfig4[0].grid, 'dataSource', config.orderAfterSalesReasonDataSource)
    },

    handleClickToolBar(e) {
      if (e.grid.getSelectedRecords().length <= 0 && e.toolbar.id == 'Delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      let _serialNumber = []
      e.grid.getSelectedRecords().map((item) => {
        _serialNumber.push(item.serialNumber)
      })
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(_serialNumber)
      }
    },

    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.serialNumber])
      } else if (e.tool.id == 'edit') {
        this.handleEdit(e.data)
      }
    },

    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    },

    sameTypeConfigCheck(arr) {
      var tempSet = new Set(arr)
      return tempSet.size !== arr.length
    },

    saveConfig() {
      let tempArr = config.orderAfterSalesReasonData.map((ele) => ele.afterSalesCode)
      if (this.sameTypeConfigCheck(tempArr)) {
        this.$toast({
          content: this.$t('存在重复的售后编码，请确认后再提交'),
          type: 'warning'
        })
        return
      }

      this.$API.bgConfig.saveOrderAfterSalesReason(config.orderAfterSalesReasonData).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
        this.$store.commit('startLoading')
        this.getConfig()
      })
    }
  }
}
</script>

<style></style>
