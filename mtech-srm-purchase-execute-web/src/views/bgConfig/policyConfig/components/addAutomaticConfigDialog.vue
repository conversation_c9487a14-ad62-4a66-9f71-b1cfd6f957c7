// 创建自动化配置弹窗
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <div class="dialog-content">
      <mt-form ref="ruleForm" :model="automaticObject" :rules="dialogRules">
        <mt-form-item prop="businessTypeId" :label="$t('业务类型')">
          <mt-select
            ref="businessTypeRef"
            v-model="automaticObject.businessTypeId"
            :data-source="businessTypeList"
            :allow-filtering="true"
            :placeholder="$t('请选择业务类型')"
            :fields="{ text: 'businessTypeName', value: 'businessTypeId' }"
            @change="businessTypeIdChange"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="supplierCode" :label="$t('供应商')">
          <mt-select
            v-model="automaticObject.supplierCode"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="supplierTypeList"
            :placeholder="$t('请选择供应商')"
            @change="supplierCodeChange"
          ></mt-select>
        </mt-form-item>

        <div style="width: 100%; display: flex">
          <mt-form-item prop="orderAutoConfirm" :label="$t('采购订单自动确认：')">
            <mt-radio
              v-model="automaticObject.strategyConfigMap.orderAutoConfirm"
              :data-source="radioData"
            ></mt-radio>
          </mt-form-item>
          <mt-form-item prop="saleOrderAutoConfirm" :label="$t('售后订单自动确认：')">
            <mt-radio
              v-model="automaticObject.strategyConfigMap.saleOrderAutoConfirm"
              :data-source="radioData"
            ></mt-radio>
          </mt-form-item>
          <mt-form-item prop="accountReconciliationAutoConfirm" :label="$t('对账单自动确认：')">
            <mt-radio
              v-model="automaticObject.strategyConfigMap.accountReconciliationAutoConfirm"
              :data-source="radioData"
            ></mt-radio>
          </mt-form-item>
        </div>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { automaticData, automaticDataSource } from '../config/index.js'
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      radioData: [
        {
          label: this.$t('是'),
          value: '1'
        },
        {
          label: this.$t('否'),
          value: '0'
        }
      ],
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      dialogTitle: '',

      automaticObject: {
        businessTypeId: '',
        supplierCode: '',
        strategyConfigMap: {
          orderAutoConfirm: '0',
          saleOrderAutoConfirm: '0',
          accountReconciliationAutoConfirm: '0'
        },
        id: '',
        tenantId: ''
      },
      dialogRules: {
        businessTypeId: [{ required: true, message: this.$t('请选择业务类型'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }]
      },
      showTreeView: false,
      businessTypeList: [], // 业务类型list
      supplierTypeList: [] // 供应商list
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.getBusinessConfig()
    this.getSupplier()
    this.$refs['dialog'].ejsRef.show()
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      let _addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
      this.automaticObject = _addForm
      this.dialogTitle = this.$t('编辑自动化配置')
    } else {
      this.dialogTitle = this.$t('新增自动化配置')
    }
  },
  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    // 获取业务类型下拉
    getBusinessConfig() {
      this.$API.bgConfig.getLocalBusinessTypeList({}).then((res) => {
        this.businessTypeList.length = 0
        res.data.forEach((e) => {
          this.businessTypeList.push(e)
        })
      })
    },
    // 获取供应商下拉
    getSupplier() {
      this.$API.masterData.getSupplier().then((res) => {
        this.supplierTypeList.length = 0
        res.data.forEach((e) => {
          this.supplierTypeList.push({
            value: e.supplierCode,
            id: e.id,
            text: e.supplierCode + '-' + e.supplierName,
            supplierName: e.supplierName
          })
        })
      })
    },

    handleClose() {
      this.$emit('handleAutomaticDialogShow', false)
    },

    businessTypeIdChange(e) {
      this.automaticObject.businessTypeName = e.itemData.businessTypeName
      this.automaticObject.businessTypeCode = e.itemData.businessTypeCode
      this.automaticObject.tenantId = e.itemData.tenantId
    },
    supplierCodeChange(e) {
      this.automaticObject.supplierName = e.itemData.supplierName
      this.automaticObject.supplierId = e.itemData.id
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid && this.dialogData.dialogType === 'add') {
          let tempData = {
            ...this.automaticObject,
            strategyConfigMap: {
              orderAutoConfirm: Number(this.automaticObject.strategyConfigMap.orderAutoConfirm),
              saleOrderAutoConfirm: Number(
                this.automaticObject.strategyConfigMap.saleOrderAutoConfirm
              ),
              accountReconciliationAutoConfirm: Number(
                this.automaticObject.strategyConfigMap.accountReconciliationAutoConfirm
              )
            }
          }
          automaticData.push(tempData)
          let tempMap = {
            ...this.automaticObject,
            businessTypeList: this.businessTypeList,
            supplierTypeList: this.supplierTypeList
          }
          let tempNumber = 1
          if (automaticDataSource?.length > 0) {
            tempNumber = automaticDataSource.slice(-1)[0].serialNumber + 1
          }
          tempMap.serialNumber = tempNumber
          automaticDataSource.push(tempMap)
          this.handleClose()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/span.e-input-group {
    padding: 0;
  }
  /deep/.process-desc {
    width: 820px !important;
  }
  /deep/.tree-view-container {
    box-shadow: inset 0 0 0 1px rgba(232, 232, 232, 1);
    width: 100%;
    position: absolute;
    left: 0;
    top: 50px;
    z-index: 2;
    background: #fff;
    height: 200px;
    overflow: auto;
  }
}
</style>
