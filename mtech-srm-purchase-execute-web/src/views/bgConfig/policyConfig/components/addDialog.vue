// 新增售后原因弹窗
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog dialog-payment"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="afterSalesCode" :label="$t('售后编码')">
        <mt-input
          v-model="addForm.afterSalesCode"
          :show-clear-button="true"
          :multiline="false"
          :placeholder="$t('请输入售后编码')"
          v-if="maxlength1"
          :maxlength="maxlength1"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="afterSalesReason" :label="$t('售后原因')" class="full-width">
        <mt-input
          v-model="addForm.afterSalesReason"
          :show-clear-button="true"
          :multiline="true"
          :placeholder="$t('请输入售后原因')"
          v-if="maxlength2"
          :maxlength="maxlength2"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { orderAfterSalesReasonData, orderAfterSalesReasonDataSource } from '../config/index.js'
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      maxlength1: 50,
      maxlength2: 100,
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        afterSalesCode: '',
        afterSalesReason: ''
      },
      rules: {
        afterSalesCode: [
          {
            required: true,
            message: this.$t('请输入售后编号'),
            trigger: 'blur'
          }
        ],
        afterSalesReason: [
          {
            required: true,
            message: this.$t('请输入售后原因'),
            trigger: 'blur'
          }
        ]
      },
      fields: {
        dataSource: []
      }
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      let _addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
      this.addForm = _addForm
      this.dialogTitle = this.$t('编辑售后原因')
    } else {
      this.dialogTitle = this.$t('新增售后原因')
    }
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid && this.dialogData.dialogType === 'add') {
          orderAfterSalesReasonData.push(this.addForm)
          let tempMap = JSON.parse(JSON.stringify(this.addForm))
          let tempNumber = 1
          if (orderAfterSalesReasonDataSource?.length > 0) {
            tempNumber = orderAfterSalesReasonDataSource.slice(-1)[0].serialNumber + 1
          }
          tempMap.serialNumber = tempNumber
          tempMap.id = null
          orderAfterSalesReasonDataSource.push(tempMap)
          this.handleClose()
        } else if (valid && this.dialogData.dialogType === 'edit') {
          orderAfterSalesReasonData[Number(this.dialogData.row.serialNumber) - 1] = {
            ...this.addForm
          }
          orderAfterSalesReasonDataSource[Number(this.dialogData.row.serialNumber) - 1] = {
            ...this.addForm
          }
          this.$emit('confirmSuccess')
          this.handleClose()
        }
      })
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    }
  }
}
</script>

<style></style>
