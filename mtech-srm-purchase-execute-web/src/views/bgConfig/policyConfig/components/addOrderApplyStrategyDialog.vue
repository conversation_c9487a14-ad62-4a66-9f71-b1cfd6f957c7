// 采购执行配置弹窗
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <div class="dialog-content">
      <mt-form ref="ruleForm" :model="orderStrategyObject" :rules="dialogRules">
        <mt-form-item prop="businessTypeId" :label="$t('业务类型')">
          <mt-select
            ref="businessTypeRef"
            v-model="orderStrategyObject.businessTypeId"
            :data-source="businessTypeList"
            :allow-filtering="true"
            :placeholder="$t('请选择')"
            :fields="{ text: 'businessTypeName', value: 'businessTypeId' }"
            @change="businessTypeIdChange"
          ></mt-select>
        </mt-form-item>

        <div style="width: 100%; float: left">
          <mt-form-item
            prop="strategyConfigMap.isSplitFlag"
            :label="$t('采购申请行拆成每行一个单据')"
            class="float-l"
            style="width: 220px"
          >
            <mt-radio
              v-model="orderStrategyObject.strategyConfigMap.isSplitFlag"
              :data-source="radioData"
            ></mt-radio>
          </mt-form-item>

          <mt-form-item
            prop="strategyConfigMap.autoSourcingFlag"
            :label="$t('无价格自动转寻源（按照采购申请单转寻源订单）')"
            class="float-l"
            style="width: 120px"
          >
            <mt-radio
              v-model="orderStrategyObject.strategyConfigMap.autoSourcingFlag"
              :data-source="radioData"
            ></mt-radio>
          </mt-form-item>
        </div>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { orderApplyStrategySpecData, orderApplyStrategyDataSource } from '../config/index.js'
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      radioData: [
        {
          label: this.$t('是'),
          value: '1'
        },
        {
          label: this.$t('否'),
          value: '0'
        }
      ],
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      dialogTitle: '',

      orderStrategyObject: {
        businessTypeId: '',
        orderTypeId: '',
        commonFlag: 0,
        strategyConfigMap: {
          isSplitFlag: '0',
          autoSourcingFlag: '0'
        },
        id: '',
        tenantId: ''
      },
      dialogRules: {
        businessTypeId: [
          {
            required: true,
            message: this.$t('请选择业务类型'),
            trigger: 'blur'
          }
        ],
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }]
      },
      showTreeView: false,
      businessTypeList: [], // 业务类型list
      purExecutionMethodOptions: [], //采购执行方式list
      orderTypeOptions: [] // 供应商list
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.getBusinessConfig()
    this.$refs['dialog'].ejsRef.show()
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      let _addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
      this.orderStrategyObject = _addForm
      this.dialogTitle = this.$t('编辑采购申请配置')
    } else {
      this.dialogTitle = this.$t('新增采购申请配置')
    }
  },
  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    // 获取业务类型下拉
    getBusinessConfig() {
      this.$API.bgConfig.getLocalBusinessTypeList({}).then((res) => {
        this.businessTypeList.length = 0
        res.data.forEach((e) => {
          this.businessTypeList.push(e)
        })
      })
    },

    handleClose() {
      this.$emit('handleOrderApplyStrategyDialogShow', false)
    },

    businessTypeIdChange(e) {
      this.orderStrategyObject.businessTypeName = e.itemData.businessTypeName
      this.orderStrategyObject.businessTypeCode = e.itemData.businessTypeCode
      this.orderStrategyObject.tenantId = e.itemData.tenantId
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid && this.dialogData.dialogType === 'add') {
          let tempData = {
            ...this.orderStrategyObject,
            strategyConfigMap: {
              isSplitFlag: Number(this.orderStrategyObject.strategyConfigMap.isSplitFlag),
              autoSourcingFlag: Number(this.orderStrategyObject.strategyConfigMap.autoSourcingFlag)
            }
          }
          orderApplyStrategySpecData.push(tempData)
          let tempMap = {
            ...this.orderStrategyObject,
            businessTypeList: this.businessTypeList
          }
          let tempNumber = 1
          if (orderApplyStrategyDataSource?.length > 0) {
            tempNumber = Number(orderApplyStrategyDataSource.slice(-1)[0].serialNumber) + 1
          }
          tempMap.serialNumber = tempNumber
          orderApplyStrategyDataSource.push(tempMap)
          this.handleClose()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/span.e-input-group {
    padding: 0;
  }
  /deep/.process-desc {
    width: 820px !important;
  }
  /deep/.tree-view-container {
    box-shadow: inset 0 0 0 1px rgba(232, 232, 232, 1);
    width: 100%;
    position: absolute;
    left: 0;
    top: 50px;
    z-index: 2;
    background: #fff;
    height: 200px;
    overflow: auto;
  }
}
</style>
