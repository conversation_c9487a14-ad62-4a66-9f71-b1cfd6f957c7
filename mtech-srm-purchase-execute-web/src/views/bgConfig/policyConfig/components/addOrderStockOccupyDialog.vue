// 创建采购订单库存预占配置弹窗
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <div class="dialog-content">
      <mt-form ref="ruleForm" :model="orderStockObject" :rules="dialogRules">
        <mt-form-item prop="businessTypeId" :label="$t('业务类型')">
          <mt-select
            ref="businessTypeRef"
            v-model="orderStockObject.businessTypeId"
            :data-source="businessTypeList"
            :allow-filtering="true"
            :placeholder="$t('请选择业务类型')"
            :fields="{ text: 'businessTypeName', value: 'businessTypeId' }"
            @change="businessTypeIdChange"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="supplierCode" :label="$t('供应商：')">
          <mt-select
            v-model="orderStockObject.supplierCode"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="supplierTypeList"
            :placeholder="$t('请选择供应商')"
            @change="supplierCodeChange"
            :fields="{ text: 'label', value: 'supplierCode' }"
            :filtering="serchText2"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="stockOccupyFlag" :label="$t('订单生成：')">
          <mt-radio
            v-model="orderStockObject.stockOccupyFlag"
            :data-source="supplierRadioData"
          ></mt-radio>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
import {
  supplierTabRadioData,
  orderStockOccupySpecConfig,
  orderStockOccupyDataSource
} from '../config/index.js'
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      supplierRadioData: supplierTabRadioData,
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      dialogTitle: '',

      orderStockObject: {
        businessTypeId: '',
        supplierCode: '',
        stockOccupyFlag: '0',
        commonFlag: 0,
        id: null
      },
      dialogRules: {
        businessTypeId: [
          {
            required: true,
            message: this.$t('请选择业务类型'),
            trigger: 'blur'
          }
        ],
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }]
      },
      showTreeView: false,
      businessTypeList: [], // 业务类型list
      supplierTypeList: [], // 供应商list
      supplierCode: ''
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.getBusinessConfig()
    this.getSupplier()
    this.getSupplier = utils.debounce(this.getSupplier, 1000)
    this.$refs['dialog'].ejsRef.show()
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      let _addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
      _addForm.useApproval = _addForm.enableStatus == 1
      this.orderStockObject = _addForm
    }
    this.dialogTitle = this.$t('新增采购订单库存预占配置')
  },
  methods: {
    serchText2(val) {
      console.log('搜索值', val)
      this.getSupplier(val && val.text ? val.text : '')
    },
    getSupplier(val, entryFirst) {
      //查询供应商的数据
      let str = val || this.supplierCode
      let params = {
        fuzzyNameOrCode: str || ''
      }
      this.$API.masterData.getSupplier(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.supplierCode}-${item.supplierName}`
        })
        this.supplierTypeList = res.data || []
        if (entryFirst === '1') {
          this.ruleForm.supplierCode = this.entryInfo.row.supplierCode
        }
      })
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    handleClose() {
      this.$emit('handleOrderStockAddDialogShow', false)
    },

    // 获取业务类型下拉
    getBusinessConfig() {
      this.$API.bgConfig.getLocalBusinessTypeList({}).then((res) => {
        this.businessTypeList.length = 0
        res.data.forEach((e) => {
          this.businessTypeList.push(e)
        })
      })
    },
    // // 获取供应商下拉
    // getSupplier() {
    //   this.$API.masterData.getSupplier().then((res) => {
    //     this.supplierTypeList.length = 0;
    //     res.data.forEach((e) => {
    //       this.supplierTypeList.push({
    //         value: e.supplierCode,
    //         id: e.id,
    //         text: e.supplierCode + "-" + e.supplierName,
    //         supplierName: e.supplierName,
    //       });
    //     });
    //   });
    // },

    businessTypeIdChange(e) {
      this.orderStockObject.businessTypeName = e.itemData.businessTypeName
      this.orderStockObject.businessTypeCode = e.itemData.businessTypeCode
      this.orderStockObject.tenantId = e.itemData.tenantId
    },
    supplierCodeChange(e) {
      this.orderStockObject.supplierName = e.itemData.supplierName
      this.orderStockObject.supplierId = e.itemData.id
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          orderStockOccupySpecConfig.push(this.orderStockObject)
          let tempMap = {
            ...this.orderStockObject,
            businessTypeList: this.businessTypeList,
            supplierTypeList: this.supplierTypeList
          }
          let tempNumber = 1
          if (orderStockOccupyDataSource?.length > 0) {
            tempNumber = orderStockOccupyDataSource.slice(-1)[0].serialNumber + 1
          }
          tempMap.serialNumber = tempNumber
          orderStockOccupyDataSource.push(tempMap)
          this.handleClose()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/span.e-input-group {
    padding: 0;
  }
  /deep/.process-desc {
    width: 820px !important;
  }
  /deep/.tree-view-container {
    box-shadow: inset 0 0 0 1px rgba(232, 232, 232, 1);
    width: 100%;
    position: absolute;
    left: 0;
    top: 50px;
    z-index: 2;
    background: #fff;
    height: 200px;
    overflow: auto;
  }
}
</style>
