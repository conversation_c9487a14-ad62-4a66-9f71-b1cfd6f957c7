// 创建验收项配置弹窗
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <div class="dialog-content">
      <mt-form ref="ruleForm" :model="acceptanceObject" :rules="dialogRules">
        <mt-form-item prop="acceptanceCode" :label="$t('验收项编号')">
          <mt-input
            v-model="acceptanceObject.acceptanceCode"
            :show-clear-button="true"
            :multiline="false"
            :disabled="dialogTitle == $t('编辑验收项配置')"
            :placeholder="$t('请输入验收项编号')"
            v-if="maxlength1"
            :maxlength="maxlength1"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="acceptanceTypeName" :label="$t('验收项类型名称')">
          <mt-input
            v-model="acceptanceObject.acceptanceTypeName"
            :show-clear-button="true"
            :multiline="false"
            :placeholder="$t('请输入验收项类型名称')"
            v-if="maxlength2"
            :maxlength="maxlength2"
          ></mt-input>
        </mt-form-item>

        <!-- <mt-form-item prop="prepaidFlag" label="是否预付">
          <mt-radio
            v-model="acceptanceObject.strategyConfigMap.prepaidFlag"
            :data-source="radioData"
          ></mt-radio>
        </mt-form-item> -->
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { acceptanceData, acceptanceDataSource } from '../config/index.js'
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      maxlength1: 50,
      maxlength2: 75,
      radioData: [
        {
          label: this.$t('是'),
          value: '1'
        },
        {
          label: this.$t('否'),
          value: '0'
        }
      ],
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      dialogTitle: '',
      acceptanceObject: {
        acceptanceCode: '',
        acceptanceTypeName: '',
        // strategyConfigMap: { prepaidFlag: "0" },
        acceptanceTypeId: null,
        acceptanceTypeCode: null,
        id: null
      },
      dialogRules: {
        acceptanceCode: [
          {
            required: true,
            message: this.$t('请输入验收项编号'),
            trigger: 'blur'
          }
        ],
        acceptanceTypeName: [
          {
            required: true,
            message: this.$t('请输入验收项类型名称'),
            trigger: 'blur'
          }
        ]
      },
      showTreeView: false
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      let _addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
      this.acceptanceObject = {
        ..._addForm
        // strategyConfigMap: {
        //   prepaidFlag: _addForm.strategyConfigMap.prepaidFlag.toString(),
        // },
      }
      this.dialogTitle = this.$t('编辑验收项配置')
    } else {
      this.dialogTitle = this.$t('新增验收项配置')
    }
  },
  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    handleClose() {
      this.$emit('handleAcceptanceDialogShow', false)
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid && this.dialogData.dialogType === 'add') {
          // acceptanceData.push(this.acceptanceObject);
          let tempMap = JSON.parse(JSON.stringify(this.acceptanceObject))
          let tempNumber = 1
          if (acceptanceDataSource?.length > 0) {
            tempNumber = acceptanceDataSource.slice(-1)[0].serialNumber + 1
          }
          tempMap.serialNumber = tempNumber
          acceptanceDataSource.push(tempMap)

          // let tempAcceptanceData = {
          //   ...this.acceptanceObject,
          //   strategyConfigMap: {
          //     prepaidFlag: Number(
          //       this.acceptanceObject.strategyConfigMap.prepaidFlag
          //     ),
          //   },
          // };
          // delete tempAcceptanceData.prepaidFlag;
          // acceptanceData.push(tempAcceptanceData);
          acceptanceData.push(this.acceptanceObject)
          this.handleClose()
        } else if (valid && this.dialogData.dialogType === 'edit') {
          acceptanceData[Number(this.dialogData.row.serialNumber) - 1] = {
            ...this.acceptanceObject
          }
          acceptanceDataSource[Number(this.dialogData.row.serialNumber) - 1] = {
            ...this.acceptanceObject
          }
          console.log(this.acceptanceObject)
          this.$emit('confirmSuccess')

          // this.$emit("confirmSuccess", this.acceptanceObject);
          this.handleClose()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/span.e-input-group {
    padding: 0;
  }
  /deep/.process-desc {
    width: 820px !important;
  }
  /deep/.tree-view-container {
    box-shadow: inset 0 0 0 1px rgba(232, 232, 232, 1);
    width: 100%;
    position: absolute;
    left: 0;
    top: 50px;
    z-index: 2;
    background: #fff;
    height: 200px;
    overflow: auto;
  }
}
</style>
