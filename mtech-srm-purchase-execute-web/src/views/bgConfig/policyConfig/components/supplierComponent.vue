<template>
  <div class="ml20">
    <mt-template-page
      slot="slot-0"
      ref="template-0"
      :template-config="pageConfig"
    ></mt-template-page>
  </div>
</template>

<script>
import { supplierColumnData } from '../config/index.js'
export default {
  components: {},
  props: {
    supplierData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      filterOptions: {
        type: 'Menu'
      },
      businessTypeList: [],
      pageConfig: [
        {
          grid: {
            allowPaging: false,
            height: 'auto',
            columnData: supplierColumnData,
            dataSource: this.supplierData
          }
        }
      ]
    }
  },
  methods: {
    handleSelectTab(e) {
      this.currentTabIndex = e
    }
  }
}
</script>

<style lang="scss" scoped>
.top-filter {
  background: #fff;
  padding: 20px;
  border-bottom: 1px solid #e8e8e8;

  .left-status {
    margin-right: 20px;
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
}
</style>
