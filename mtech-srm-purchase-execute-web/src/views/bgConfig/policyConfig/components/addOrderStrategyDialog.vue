// 采购执行配置弹窗
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <div class="dialog-content">
      <mt-form ref="ruleForm" :model="orderStrategyObject" :rules="dialogRules">
        <mt-form-item prop="businessTypeId" :label="$t('业务类型')">
          <mt-select
            ref="businessTypeRef"
            v-model="orderStrategyObject.businessTypeId"
            :data-source="businessTypeList"
            :allow-filtering="true"
            :placeholder="$t('请选择')"
            :fields="{ text: 'businessTypeName', value: 'businessTypeId' }"
            @change="businessTypeIdChange"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="orderTypeId" :label="$t('订单类型')">
          <mt-select
            v-model="orderStrategyObject.orderTypeId"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="orderTypeOptions"
            :fields="{ text: 'message', value: 'id' }"
            :placeholder="$t('请选择')"
            @change="orderTypeChange"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="purExecutionMethod" :label="$t('采购执行方式')">
          <mt-select
            v-model="orderStrategyObject.purExecutionMethod"
            float-label-type="Never"
            :allow-filtering="true"
            :data-source="purExecutionMethodOptions"
            :placeholder="$t('请选择')"
            @change="purExecutionMethodChange"
          ></mt-select>
        </mt-form-item>

        <div style="width: 100%; float: left">
          <mt-form-item
            prop="strategyConfigMap.autoReleaseFlag"
            :label="$t('采购订单审批通过后自动发布')"
            class="float-l"
            style="width: 220px"
          >
            <mt-radio
              v-model="orderStrategyObject.strategyConfigMap.autoReleaseFlag"
              :data-source="radioData"
            ></mt-radio>
          </mt-form-item>
          <mt-form-item
            prop="strategyConfigMap.autoCancelFlag"
            :label="$t('供方拒绝后订单自动取消')"
            class="float-l"
            style="width: 190px"
          >
            <mt-radio
              v-model="orderStrategyObject.strategyConfigMap.autoCancelFlag"
              :data-source="radioData"
            ></mt-radio>
          </mt-form-item>
          <mt-form-item
            prop="strategyConfigMap.allowModifyFlag"
            :label="$t('供方确认订单后采购订单允许修改')"
            class="float-l"
            style="width: 240px"
          >
            <mt-radio
              v-model="orderStrategyObject.strategyConfigMap.allowModifyFlag"
              :data-source="radioData"
            ></mt-radio>
          </mt-form-item>
          <!-- 当前版本不需要 -->
          <!-- <mt-form-item
            prop="strategyConfigMap.deliverFlag"
            label="是否需要妥投"
            class="float-l"
            style="width: 120px"
          >
            <mt-radio
              v-model="orderStrategyObject.strategyConfigMap.deliverFlag"
              :data-source="radioData"
            ></mt-radio>
          </mt-form-item> -->
        </div>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { orderStrategySpecData, orderStrategyDataSource } from '../config/index.js'
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      radioData: [
        {
          label: this.$t('是'),
          value: '1'
        },
        {
          label: this.$t('否'),
          value: '0'
        }
      ],
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      dialogTitle: '',

      orderStrategyObject: {
        businessTypeId: '',
        orderTypeId: '',
        commonFlag: 0,
        purExecutionMethod: null,
        strategyConfigMap: {
          autoReleaseFlag: '0',
          autoCancelFlag: '0',
          allowModifyFlag: '0',
          deliverFlag: '0'
        },
        id: '',
        tenantId: ''
      },
      dialogRules: {
        businessTypeId: [
          {
            required: true,
            message: this.$t('请选择业务类型'),
            trigger: 'blur'
          }
        ],
        orderTypeId: [
          {
            required: true,
            message: this.$t('请选择订单类型'),
            trigger: 'blur'
          }
        ],
        purExecutionMethod: [
          {
            required: true,
            message: this.$t('请选择采购执行方式'),
            trigger: 'blur'
          }
        ]
      },
      showTreeView: false,
      businessTypeList: [], // 业务类型list
      purExecutionMethodOptions: [], //采购执行方式list
      orderTypeOptions: [] // 供应商list
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.getBusinessConfig()
    this.getOrderType()
    this.getPurExecutionMethod()
    this.$refs['dialog'].ejsRef.show()
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      let _addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
      this.orderStrategyObject = _addForm
      this.dialogTitle = this.$t('编辑采购执行配置')
    } else {
      this.dialogTitle = this.$t('新增采购执行配置')
    }
  },
  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    // 获取业务类型下拉
    getBusinessConfig() {
      this.$API.bgConfig.getLocalBusinessTypeList({}).then((res) => {
        this.businessTypeList.length = 0
        res.data.forEach((e) => {
          this.businessTypeList.push(e)
        })
      })
    },

    // 获取订单类型下拉
    getOrderType() {
      this.$API.masterData.getDictCode({ dictCode: 'OrderType' }).then((res) => {
        this.orderTypeOptions.length = 0
        res.data.forEach((item) => {
          item.message = item.itemName
          item.code = item.itemCode
          this.orderTypeOptions.push(item)
        })
      })
    },

    // 获取采购方式下拉列表
    getPurExecutionMethod() {
      this.$API.bgConfig.getPurExecutionMethod().then((res) => {
        if (res.code === 200) {
          this.purExecutionMethodOptions.length = 0
          res.data.forEach((e) => {
            this.purExecutionMethodOptions.push({
              value: e.code,
              text: e.message
            })
          })
        }
      })
    },

    handleClose() {
      this.$emit('handleOrderStrategyDialogShow', false)
    },

    businessTypeIdChange(e) {
      this.orderStrategyObject.businessTypeName = e.itemData.businessTypeName
      this.orderStrategyObject.businessTypeCode = e.itemData.businessTypeCode
      this.orderStrategyObject.tenantId = e.itemData.tenantId
    },
    orderTypeChange(e) {
      this.orderStrategyObject.orderTypeCode = e.itemData.code
      this.orderStrategyObject.orderTypeId = e.itemData.id
      this.orderStrategyObject.orderTypeName = e.itemData.message
    },
    purExecutionMethodChange(e) {
      this.orderStrategyObject.strategyConfigMap.purExecutionMethod = e.itemData.value
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid && this.dialogData.dialogType === 'add') {
          let tempData = {
            ...this.orderStrategyObject,
            strategyConfigMap: {
              autoReleaseFlag: Number(this.orderStrategyObject.strategyConfigMap.autoReleaseFlag),
              autoCancelFlag: Number(this.orderStrategyObject.strategyConfigMap.autoCancelFlag),
              allowModifyFlag: Number(this.orderStrategyObject.strategyConfigMap.allowModifyFlag),
              deliverFlag: Number(this.orderStrategyObject.strategyConfigMap.deliverFlag),
              purExecutionMethod: this.orderStrategyObject.purExecutionMethod
            }
          }
          delete tempData.purExecutionMethod
          orderStrategySpecData.push(tempData)
          let tempMap = {
            ...this.orderStrategyObject,
            businessTypeList: this.businessTypeList,
            orderTypeOptions: this.orderTypeOptions,
            purExecutionMethodOptions: this.purExecutionMethodOptions
          }
          let tempNumber = 1
          if (orderStrategyDataSource?.length > 0) {
            tempNumber = Number(orderStrategyDataSource.slice(-1)[0].serialNumber) + 1
          }
          tempMap.serialNumber = tempNumber
          orderStrategyDataSource.push(tempMap)
          this.handleClose()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/span.e-input-group {
    padding: 0;
  }
  /deep/.process-desc {
    width: 820px !important;
  }
  /deep/.tree-view-container {
    box-shadow: inset 0 0 0 1px rgba(232, 232, 232, 1);
    width: 100%;
    position: absolute;
    left: 0;
    top: 50px;
    z-index: 2;
    background: #fff;
    height: 200px;
    overflow: auto;
  }
}
</style>
