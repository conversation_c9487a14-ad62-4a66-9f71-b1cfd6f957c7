<template>
  <div class="full-height">
    <!-- 右侧各种操作按钮 -->
    <div class="operateButton">
      <mt-button css-class="e-flat" v-show="editFlag" @click="changeEditFlag" :is-primary="true">{{
        $t('取消')
      }}</mt-button>
      <mt-button css-class="e-flat" v-show="!editFlag" @click="changeEditFlag" :is-primary="true">{{
        $t('编辑')
      }}</mt-button>
      <mt-button
        css-class="e-flat"
        v-show="editFlag && this.currentTabIndex != 7"
        @click="saveData"
        :is-primary="true"
        >{{ $t('保存') }}</mt-button
      >
    </div>

    <mt-tabs
      :tab-id="tabId"
      :e-tab="false"
      :data-source="pageConfig"
      :selected-item="currentTab"
      :halt-select="false"
      @handleSelectTab="handleSelectTab"
    >
    </mt-tabs>
    <div class="all-tabs">
      <orderConsolidationConfig
        ref="template0"
        v-show="currentTabIndex == 0"
      ></orderConsolidationConfig>
      <orderStrategyConfig
        ref="template1"
        :is-edit="editFlag"
        v-show="currentTabIndex == 1"
      ></orderStrategyConfig>

      <orderApplyStrategyConfig
        ref="template2"
        :is-edit="editFlag"
        v-show="currentTabIndex == 2"
      ></orderApplyStrategyConfig>

      <orderStockOccupyConfig
        ref="template3"
        :is-edit="editFlag"
        v-show="currentTabIndex == 3"
      ></orderStockOccupyConfig>

      <orderAfterSalesReasonConfig
        ref="template4"
        :is-edit="editFlag"
        v-show="currentTabIndex == 4"
      ></orderAfterSalesReasonConfig>
      <acceptanceConfig
        ref="template5"
        :is-edit="editFlag"
        v-show="currentTabIndex == 5"
      ></acceptanceConfig>

      <automaticConfig
        ref="template6"
        :is-edit="editFlag"
        v-show="currentTabIndex == 6"
      ></automaticConfig>
      <timingSyncConfig
        ref="template7"
        :is-edit="editFlag"
        v-show="currentTabIndex == 7"
      ></timingSyncConfig>
    </div>
  </div>
</template>

<script>
import { isEqual } from 'lodash'
import * as config from './config/index.js'
export default {
  components: {
    // 并单策略
    orderConsolidationConfig: require('./pages/orderConsolidationConfig/index.vue').default,
    // 采购执行配置
    orderStrategyConfig: require('./pages/orderStrategyConfig/index.vue').default,
    // 采购申请配置
    orderApplyStrategyConfig: require('./pages/orderApplyStrategyConfig/index.vue').default,
    // 供应商预占库存配置
    orderStockOccupyConfig: require('./pages/orderStockOccupyConfig/index.vue').default,
    // 售后原因维护
    orderAfterSalesReasonConfig: require('./pages/orderAfterSalesReasonConfig/index.vue').default,
    // 验收项配置
    acceptanceConfig: require('./pages/acceptanceConfig/index.vue').default,
    // 自动化配置
    automaticConfig: require('./pages/automaticConfig/index.vue').default,
    // 定时同步配置
    timingSyncConfig: require('./pages/timingSyncConfig/index.vue').default
  },
  data() {
    return {
      tabId: 'policyConfig', //用于区分多个自适应tabs
      editFlag: false, //是否可编辑
      currentTab: 0, // mt-tabs指定Tab
      isExpand: true, // 顶部是否折叠
      currentTabIndex: 0, //当前tab
      pageConfig: [
        { title: this.$t('并单策略') },
        { title: this.$t('采购执行配置') },
        { title: this.$t('采购申请配置') },
        { title: this.$t('供应商预占库存配置') },
        { title: this.$t('售后原因维护') },
        { title: this.$t('验收项配置') },
        { title: this.$t('自动化配置') },
        { title: this.$t('定时同步配置') }
      ]
    }
  },

  methods: {
    // 切换Tab前判断是否有未保存的变更
    handleSelectTab(e) {
      let orderStrategy = {
        orderStrategy: null
      }
      let orderStockOccupy
      let orderApplyStrategy = {
        orderStrategy: null
      }
      if (this.currentTabIndex === 0) {
        this.currentTab = this.currentTabIndex
        if (isEqual(config.orderConfigData, this.$refs.template0._data.orderConfigDataOrigin)) {
          this.changeTab(e)
        } else {
          this.alertConfirm(e)
        }
        return
      }
      if (this.currentTabIndex === 1) {
        this.currentTab = this.currentTabIndex
        if (this.$refs.template1._data.orderStrategyOrigin.some((item) => item.commonFlag == 1)) {
          orderStrategy.orderStrategy = config.orderStrategyDefaultData.concat(
            config.orderStrategySpecData
          )
        } else {
          orderStrategy.orderStrategy = config.orderStrategySpecData
        }
        if (isEqual(orderStrategy.orderStrategy, this.$refs.template1._data.orderStrategyOrigin)) {
          this.changeTab(e)
        } else {
          this.alertConfirm(e)
        }
        return
      }
      if (this.currentTabIndex === 3) {
        this.currentTab = this.currentTabIndex
        if (
          this.$refs.template3._data.orderStockOccupyOrigin.some((item) => item.commonFlag == 1)
        ) {
          orderStockOccupy = config.orderStockOccupyDefaultConfig.concat(
            config.orderStockOccupySpecConfig
          )
        } else {
          orderStockOccupy = config.orderStockOccupySpecConfig
        }
        if (isEqual(orderStockOccupy, this.$refs.template3._data.orderStockOccupyOrigin)) {
          this.changeTab(e)
        } else {
          this.alertConfirm(e)
        }
        return
      }
      if (this.currentTabIndex === 4) {
        this.currentTab = this.currentTabIndex
        if (
          isEqual(
            config.orderAfterSalesReasonData,
            this.$refs.template4._data.orderAfterSalesReasonOrigin
          )
        ) {
          this.changeTab(e)
        } else {
          this.alertConfirm(e)
        }
        return
      }
      if (this.currentTabIndex === 2) {
        this.currentTab = this.currentTabIndex
        if (
          this.$refs.template2._data.orderApplyStrategyOrigin.some((item) => item.commonFlag == 1)
        ) {
          orderApplyStrategy.orderStrategy = config.orderApplyStrategyDefaultData.concat(
            config.orderApplyStrategySpecData
          )
        } else {
          orderApplyStrategy.orderStrategy = config.orderApplyStrategySpecData
        }
        if (
          isEqual(
            orderApplyStrategy.orderStrategy,
            this.$refs.template2._data.orderApplyStrategyOrigin
          )
        ) {
          this.changeTab(e)
        } else {
          this.alertConfirm(e)
        }
        return
      }
      if (this.currentTabIndex === 5) {
        this.currentTab = this.currentTabIndex
        if (isEqual(config.acceptanceData, this.$refs.template5._data.acceptanceDataOrigin)) {
          this.changeTab(e)
        } else {
          this.alertConfirm(e)
        }
        return
      }
      if (this.currentTabIndex === 6) {
        this.currentTab = this.currentTabIndex
        if (isEqual(config.automaticData, this.$refs.template6._data.automaticDataOrigin)) {
          this.changeTab(e)
        } else {
          this.alertConfirm(e)
        }
        return
      }
      if (this.currentTabIndex === 7) {
        this.currentTab = this.currentTabIndex
        // 即时保存更新不需要校验是否有未保存变更
        this.changeTab(e)
        return
      }
    },
    // 未保存变更弹窗提示
    alertConfirm(e) {
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t('当前页面有未保存的变更，是否离开')
        },
        success: () => {
          this.changeTab(e)
        }
      })
    },
    // 切换tab
    changeTab(e) {
      this.$store.commit('startLoading')
      this.editFlag = false
      this.currentTab = e
      this.currentTabIndex = e
      config.editFlag.isEditable = this.editFlag
      this.$refs['template' + this.currentTabIndex].getConfig()
      this.$refs['template' + this.currentTabIndex].changeEditFlag(this.editFlag)
    },

    saveData() {
      // 保存当前Tab的修改
      this.$refs['template' + this.currentTabIndex].saveConfig()
    },

    // 是否可编辑状态改变
    changeEditFlag() {
      this.editFlag = this.editFlag ? false : true
      config.editFlag.isEditable = this.editFlag
      // 点击取消按钮还原配置数据
      this.$refs['template' + this.currentTabIndex].changeEditFlag(this.editFlag)
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  background-color: #fff;
}

.mt-tabs {
  width: 100%;
  background-color: #fafafa;
  /deep/.mt-tabs-container {
    width: 100%;
    margin-right: 155px;
  }
}

.operateButton {
  position: absolute;
  right: 0;
  z-index: 1;
}
/deep/ .mt-button {
  button {
    width: 76px;
    height: 34px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(0, 70, 156, 0.1);
    border-radius: 4px;
    box-shadow: unset;
    padding: 0;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(0, 70, 156, 1);
    margin-top: 6px;
  }
}

/deep/ .grid-container .mt-data-grid .e-grid td.e-rowcell .checkbox-checkeditem .e-check {
  background: rgba(154, 154, 154, 1) !important;
}

/deep/ .time-range-checkbox {
  label .e-icons {
    border: 1px solid #e8e8e8 !important;
  }
  line-height: 40px !important;
}

/deep/ .cell-checkbox-container {
  justify-content: left;
}
.all-tabs {
  background-color: #fff;
  overflow-y: auto;
  height: calc(100% - 65px);
}
/deep/ .select-container {
  .input-select {
    background-color: #fff !important;
    .e-disabled {
      background-color: #fff !important;
    }
  }
}
/deep/ .ml20 {
  padding-left: 20px;
  background-color: white;
  .common-template-page {
    border-bottom: 1px solid gainsboro;
  }
}
/deep/ .default-config-scroll {
  width: 1040px;
}
</style>
