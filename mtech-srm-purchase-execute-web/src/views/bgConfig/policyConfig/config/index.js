import { i18n } from '@/main.js'
import Vue from 'vue'
import { utils } from '@mtech-common/utils'
// 页面内容是否可编辑
export let editFlag = {
  isEditable: false
}

export const editToolbar = [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增')
  },
  {
    id: 'Delete',
    icon: 'icon_solid_Delete',
    title: i18n.t('删除')
  }
]

export const importExport = [
  {
    id: 'download',
    icon: 'icon_solid_export',
    title: i18n.t('导出'),
    visibleCondition: () => editFlag.isEditable
  },
  {
    id: 'upload',
    icon: 'icon_solid_Import',
    title: i18n.t('导入'),
    visibleCondition: () => editFlag.isEditable
  }
]

// 定时同步配置的周数据（用于计算二进制转换）
export const weekData = [
  { text: i18n.t('周一'), value: 1 },
  { text: i18n.t('周二'), value: 10 },
  { text: i18n.t('周三'), value: 100 },
  { text: i18n.t('周四'), value: 1000 },
  { text: i18n.t('周五'), value: 10000 },
  { text: i18n.t('周六'), value: 100000 },
  { text: i18n.t('周日'), value: 1000000 }
]
// 并单策略
export let orderConfigData = []

// 采购执行 默认配置
export let orderStrategyDefaultData = [
  {
    id: '',
    orderTypeId: '0',
    orderTypeCode: '0',
    orderTypeName: '0',
    businessTypeId: '0',
    businessTypeCode: '0',
    businessTypeName: '0',
    strategyConfig: '',
    strategyConfigMap: {
      allowModifyFlag: 0,
      autoCancelFlag: 0,
      autoReleaseFlag: 0,
      autoSourcingFlag: 0,
      deliverFlag: 0,
      isSplitFlag: 0,
      purExecutionMethod: null,
      autoSplitFlag: 0
    },
    commonFlag: 1
  }
]
// 采购执行 用于提交的特殊配置
export let orderStrategySpecData = []

// 采购执行 用于页面展示的特殊配置
export let orderStrategyDataSource = []

// 供应商预占库存 默认配置
export let orderStockOccupyDefaultConfig = [
  {
    id: '',
    businessTypeId: '0',
    businessTypeCode: '0',
    businessTypeName: '0',
    stockOccupyFlag: 0,
    supplierId: '0',
    supplierCode: '0',
    supplierName: '0',
    commonFlag: 1
  }
]

// 供应商预占库存 用于提交的特殊配置
export let orderStockOccupySpecConfig = []

// 供应商预占库存 用于页面展示的特殊配置
export let orderStockOccupyDataSource = []

// 售后原因维护 用于提交的数据
export let orderAfterSalesReasonData = []

// 售后原因维护 用于页面展示的数据
export let orderAfterSalesReasonDataSource = []

// 采购申请 默认配置
export let orderApplyStrategyDefaultData = [
  {
    id: '',
    businessTypeId: '0',
    businessTypeCode: '0',
    businessTypeName: '0',
    strategyConfig: '',
    strategyConfigMap: {
      autoReleaseFlag: 0,
      autoCancelFlag: 0,
      purExecutionMethod: null,
      deliverFlag: 0,
      autoSourcingFlag: 0,
      isSplitFlag: 0
    },
    commonFlag: 1
  }
]

// 采购申请 用于提交的特殊配置
export let orderApplyStrategySpecData = []

// 采购申请 用于页面展示的特殊配置
export let orderApplyStrategyDataSource = []

// 采购方式下拉列表
export let purExecutionMethodOptions = []

// 验收项配置 用于提交的数据
export let acceptanceData = []

// 验收项配置 用于页面展示的数据
export let acceptanceDataSource = []

// 自动化配置 用于提交的数据
export let automaticData = []

// 自动化配置 用于页面展示的数据
export let automaticDataSource = []

// 定时同步 用于提交的数据
export let timingSyncData = []

// 定时同步 用于页面展示的数据
export let timingSyncDataSource = []

// 业务类型list
export let businessTypeList = []

// 供应商list
export let supplierTypeList = []

// 租户员工list
export let userList = []

// 订单类型
export let orderTypeOptions = []

// 采购申请转订单/手工创建采购订单并单配置
export const purchaseColumnData = [
  {
    //   width: "50",
    field: 'serialNumber',
    headerText: i18n.t('序号')
  },
  {
    //   width: "100",
    field: 'businessType',
    headerText: i18n.t('业务类型')
  },
  {
    //   width: "100",
    field: 'rule1',
    headerText: i18n.t('规则1-公司'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-checkbox v-model="this.checkStatus" cssClass="checkbox-checkeditem"  :disabled="true"></mt-checkbox>`,
          data() {
            return { data: {} }
          },
          computed: {
            checkStatus() {
              // purchaseDataSource[this.data.index].rule1.ruleValue = 1;
              // orderConfigData[0].orderConsolidationConfigDTOList[
              //   this.data.index * 7
              // ].ruleValue = 1;
              return this.data.rule1.ruleValue ? true : false
            }
          }
        })
      }
    }
  },
  {
    //   width: "150",
    field: 'rule2',
    headerText: i18n.t('规则2-供应商'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule2.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.rule2 = e.checked ? 1 : 0;
              // purchaseDataSource[this.data.index].rule2.ruleValue = e.checked
              //   ? 1
              //   : 0;
              orderConfigData[0].orderConsolidationConfigDTOList[
                this.data.index * 7 + 1
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    //   width: "150",
    field: 'rule3',
    headerText: i18n.t('规则3-采购组织'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable"  :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule3.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.rule3 = e.checked;
              // purchaseDataSource[this.data.index].rule3.ruleValue = e.checked
              //   ? 1
              //   : 0;

              orderConfigData[0].orderConsolidationConfigDTOList[
                this.data.index * 7 + 2
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    //   width: "150",
    field: 'rule4',
    headerText: i18n.t('规则4-来源'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable"  :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule4.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              this.data.rule4 = e.checked
              // purchaseDataSource[this.data.index].rule4.ruleValue = e.checked
              //   ? 1
              //   : 0;
              orderConfigData[0].orderConsolidationConfigDTOList[
                this.data.index * 7 + 3
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    //   width: "150",
    field: 'rule5',
    headerText: i18n.t('规则5-申请编号'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable"  :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule5.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              this.data.rule5 = e.checked
              // purchaseDataSource[this.data.index].rule5.ruleValue = e.checked
              //   ? 1
              //   : 0;

              orderConfigData[0].orderConsolidationConfigDTOList[
                this.data.index * 7 + 4
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    //   width: "150",
    field: 'rule6',
    headerText: i18n.t('规则6-收货地址'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable"  :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule6.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              this.data.rule6 = e.checked
              // purchaseDataSource[this.data.index].rule6.ruleValue = e.checked
              //   ? 1
              //   : 0;
              orderConfigData[0].orderConsolidationConfigDTOList[
                this.data.index * 7 + 5
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    width: '250',
    field: 'rule7',
    headerText: i18n.t('规则7-需求时间范围'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable"  :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" label="间隔天数："></mt-checkbox>
      <mt-inputNumber ref="inputNumber" :min="0" :max="999" width="120" height="26"
      :disabled="!isEditable"  :class="[!isEditable && 'checkbox-checkeditem']"
      @change="handleInputNum" v-model="data.rule7.timeLimit"></mt-inputNumber>
    </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule7.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.rule7 = e.checked;
              // purchaseDataSource[this.data.index].rule7.ruleValue = e.checked
              //   ? 1
              //   : 0;
              orderConfigData[0].orderConsolidationConfigDTOList[
                this.data.index * 7 + 6
              ].ruleValue = e.checked ? 1 : 0
            },
            handleInputNum(e) {
              // purchaseDataSource[this.data.index].rule7.timeLimit = e;
              orderConfigData[0].orderConsolidationConfigDTOList[
                this.data.index * 7 + 6
              ].timeLimit = e
            }
          }
        })
      }
    }
  }
]

// 寻源需求并单配置
export const searchSourceColumnData = [
  {
    width: '150',
    field: 'serialNumber',
    headerText: i18n.t('序号')
  },
  {
    width: '150',
    field: 'businessType',
    headerText: i18n.t('业务类型')
  },
  {
    width: '150',
    field: 'rule1',
    headerText: i18n.t('规则1-采购组织'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-checkbox v-model="this.checkStatus" cssClass="checkbox-checkeditem"  :disabled="true"></mt-checkbox>`,
          data() {
            return { data: {} }
          },
          computed: {
            checkStatus() {
              // searchSourceDataSource[this.data.index].rule1.ruleValue = 1;
              // orderConfigData[1].orderConsolidationConfigDTOList[
              //   this.data.index * 7
              // ].ruleValue = 1;
              return this.data.rule1.ruleValue ? true : false
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'rule2',
    headerText: i18n.t('规则2-来源'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule2.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.rule2 = e.checked;
              // searchSourceDataSource[this.data.index].rule2.ruleValue =
              //   e.checked ? 1 : 0;
              orderConfigData[1].orderConsolidationConfigDTOList[
                this.data.index * 7 + 1
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'rule3',
    headerText: i18n.t('规则3-申请编号'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule3.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.rule3 = e.checked;
              // searchSourceDataSource[this.data.index].rule3.ruleValue =
              //   e.checked ? 1 : 0;

              orderConfigData[1].orderConsolidationConfigDTOList[
                this.data.index * 7 + 2
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'rule4',
    headerText: i18n.t('规则4-公司'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule4.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.rule4 = e.checked;
              // searchSourceDataSource[this.data.index].rule4.ruleValue =
              //   e.checked ? 1 : 0;

              orderConfigData[1].orderConsolidationConfigDTOList[
                this.data.index * 7 + 3
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'rule5',
    headerText: i18n.t('规则5-工厂'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule5.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.rule5 = e.checked;
              // searchSourceDataSource[this.data.index].rule5.ruleValue =
              //   e.checked ? 1 : 0;

              orderConfigData[1].orderConsolidationConfigDTOList[
                this.data.index * 7 + 4
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'rule6',
    headerText: i18n.t('规则6-采购组'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule6.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              this.data.rule6 = e.checked
              // searchSourceDataSource[this.data.index].rule6.ruleValue =
              //   e.checked ? 1 : 0;

              orderConfigData[1].orderConsolidationConfigDTOList[
                this.data.index * 7 + 5
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    width: '220',
    field: 'rule7',
    headerText: i18n.t('规则7-需求时间范围'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" label="间隔天数："></mt-checkbox>
      <mt-inputNumber ref="inputNumber" width="120" height="26"  :min="0" :max="999"
      :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
      @change="handleInputNum" v-model="data.rule7.timeLimit"></mt-inputNumber>
    </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule7.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.rule7 = e.checked;
              // searchSourceDataSource[this.data.index].rule7.ruleValue =
              //   e.checked ? 1 : 0;
              orderConfigData[1].orderConsolidationConfigDTOList[
                this.data.index * 7 + 6
              ].ruleValue = e.checked ? 1 : 0
            },
            handleInputNum(e) {
              // searchSourceDataSource[this.data.index].rule7.timeLimit = e;
              orderConfigData[1].orderConsolidationConfigDTOList[
                this.data.index * 7 + 6
              ].timeLimit = e
            }
          }
        })
      }
    }
  }
]

// 供应商送货单合并生成配置
export const supplierColumnData = [
  {
    width: '130',
    field: 'serialNumber',
    headerText: i18n.t('序号')
  },
  {
    width: '130',
    field: 'businessType',
    headerText: i18n.t('业务类型')
  },
  {
    width: '130',
    field: 'orderType',
    headerText: i18n.t('订单类型')
  },
  {
    width: '130',
    field: 'rule1',
    headerText: i18n.t('规则1-公司'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-checkbox v-model="this.checkStatus" cssClass="checkbox-checkeditem"  :disabled="true"></mt-checkbox>`,
          data() {
            return { data: {} }
          },
          computed: {
            checkStatus() {
              // supplierDataSource[this.data.index].rule1.ruleValue = 1;
              // orderConfigData[2].orderConsolidationConfigDTOList[
              //   this.data.index * 6
              // ].ruleValue = 1;
              return this.data.rule1.ruleValue ? true : false
            }
          }
        })
      }
    }
  },
  {
    width: '180',
    field: 'rule2',
    headerText: i18n.t('规则2-订单类型'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<mt-checkbox v-model="this.checkStatus" cssClass="checkbox-checkeditem"  :disabled="true"></mt-checkbox>`,
          data() {
            return { data: {} }
          },
          computed: {
            checkStatus() {
              // supplierDataSource[this.data.index].rule2.ruleValue = 1;
              // orderConfigData[2].orderConsolidationConfigDTOList[
              //   this.data.index * 6 + 1
              // ].ruleValue = 1;
              return this.data.rule2.ruleValue ? true : false
            }
          }
        })
      }
    }
  },
  {
    width: '200',
    field: 'rule3',
    headerText: i18n.t('规则11-相同订单编号'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule3.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.rule3 = e.checked;
              // supplierDataSource[this.data.index].rule3.ruleValue = e.checked
              //   ? 1
              //   : 0;
              orderConfigData[2].orderConsolidationConfigDTOList[
                this.data.index * 6 + 2
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    width: '170',
    field: 'rule4',
    headerText: i18n.t('规则6-收货地址'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule4.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.rule4 = e.checked;
              // supplierDataSource[this.data.index].rule4.ruleValue = e.checked
              //   ? 1
              //   : 0;
              orderConfigData[2].orderConsolidationConfigDTOList[
                this.data.index * 6 + 3
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'rule5',

    headerText: i18n.t('规则5-工厂'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule5.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.rule5 = e.checked;
              // supplierDataSource[this.data.index].rule5.ruleValue = e.checked
              //   ? 1
              //   : 0;
              orderConfigData[2].orderConsolidationConfigDTOList[
                this.data.index * 6 + 4
              ].ruleValue = e.checked ? 1 : 0
            }
          }
        })
      }
    }
  },
  {
    width: '380',
    field: 'rule6',
    headerText: i18n.t('送货单创建提前期（物流天数+提前期）小于(要求交期-今天)送货单才能创建成功'),
    headerTemplate: function () {
      return {
        template: Vue.component('uploadFile', {
          template: `
                  <div class="headers">
                    <span class="e-headertext">{{data.headerText}}</span>
                    <mt-tooltip :content="content" position="BottomCenter" target="#box">
                      <MtIcon id="box" name="icon_outline_prompt" />
                    </mt-tooltip>
                  </div>
                `,
          data() {
            return {
              content: function () {
                return {
                  template: Vue.component('demo', {
                    template: `
                    <div id="tooltip" ref="content" style="width:220px;font-size:12px;padding:6px 11px;">
                    （物流天数+提前期）小于(要求交期-今天)送货单才能创建成功
                    </div>`,
                    data() {
                      return {
                        data: {}
                      }
                    }
                  })
                }
              },
              data: {}
            }
          }
        })
      }
    },

    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="this.checkStatus"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" label="间隔天数："></mt-checkbox>
          <mt-inputNumber ref="inputNumber" width="120" height="26" :min="0" :max="999"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleInputNum" v-model="data.rule6.timeLimit"></mt-inputNumber>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          computed: {
            checkStatus() {
              return this.data.rule6.ruleValue ? true : false
            }
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.rule6 = e.checked;
              // supplierDataSource[this.data.index].rule6.ruleValue = e.checked
              //   ? 1
              //   : 0;
              orderConfigData[2].orderConsolidationConfigDTOList[
                this.data.index * 6 + 5
              ].ruleValue = e.checked ? 1 : 0
            },
            handleInputNum(e) {
              // supplierDataSource[this.data.index].rule6.timeLimit = e;
              orderConfigData[2].orderConsolidationConfigDTOList[
                this.data.index * 6 + 5
              ].timeLimit = e
            }
          }
        })
      }
    }
  }
]

// 采购执行配置
export const procurementExecColumn = [
  {
    width: '150',
    field: 'serialNumber',
    headerText: i18n.t('序号'),
    cssClass: ''
    // cellTools: [
    //   {
    //     id: "delete",
    //     icon: "icon_solid_Delete",
    //     title: i18n.t("删除"),
    //     visibleCondition: () => editFlag.isEditable,
    //   },
    // ],
  },
  {
    width: '210',
    field: 'businessTypeId',
    headerText: i18n.t('业务类型'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row;width:"100%"; display: inline-flex;">
          <mt-select
                width="100%"
                :data-source="businessTypeList"
                :placeholder="$t('请选择')"
                @change="businessTypeChange"
                @focus="getBusinessConfig"
                css-class="input-select"
                :disabled="!isEditable"
                :fields="{ text: 'businessTypeName', value: 'businessTypeId' }"
                v-model="data.businessTypeId"
              ></mt-select>
          </div>`,
          data() {
            return { data: {}, businessTypeList: [], isEditable: null }
          },
          mounted() {
            this.businessTypeList = businessTypeList
            this.isEditable = editFlag.isEditable
            console.log('====', this.data, this.isEditable)
          },
          methods: {
            // 获取业务类型下拉
            getBusinessConfig() {
              this.$API.bgConfig.getLocalBusinessTypeList({}).then((res) => {
                this.businessTypeList.length = 0
                res.data.forEach((e) => {
                  this.businessTypeList.push(e)
                })
              })
            },
            businessTypeChange(e) {
              orderStrategySpecData[this.data.index].businessTypeCode = e.itemData.businessTypeCode
              orderStrategySpecData[this.data.index].businessTypeId = e.itemData.businessTypeId
              orderStrategySpecData[this.data.index].businessTypeName = e.itemData.businessTypeName
              // orderStrategyDataSource[this.data.index].purExecutionMethod =
              //   e.value;
            }
          }
        })
      }
    }
  },
  {
    width: '210',
    field: 'orderTypeId',
    headerText: i18n.t('订单类型'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row;width:"100%"; display: inline-flex;">
          <mt-select
                width="100%"
                :data-source="orderTypeOptions"
                :placeholder="$t('请选择')"
                @change="orderTypeChange"
                @focus="getOrderType"
                :disabled="!isEditable"
                css-class="input-select"
                :fields="{ text: 'message', value: 'id' }"
                v-model="data.orderTypeId"
              ></mt-select>
          </div>`,
          data() {
            return { data: {}, orderTypeOptions: [], isEditable: null }
          },
          mounted() {
            this.orderTypeOptions = orderTypeOptions
            this.isEditable = editFlag.isEditable
          },
          methods: {
            // 获取订单类型下拉
            getOrderType() {
              this.$API.masterData.getDictCode({ dictCode: 'OrderType' }).then((res) => {
                this.orderTypeOptions.length = 0
                res.data.forEach((item) => {
                  item.message = item.itemName
                  item.code = item.itemCode
                  this.orderTypeOptions.push(item)
                })
              })
            },
            orderTypeChange(e) {
              orderStrategySpecData[this.data.index].orderTypeId = e.itemData.id
              orderStrategySpecData[this.data.index].orderTypeCode = e.itemData.code
              orderStrategySpecData[this.data.index].orderTypeName = e.itemData.message
              // orderStrategyDataSource[this.data.index].orderTypeId = e.value;
            }
          }
        })
      }
    }
  },
  {
    width: '210',
    field: 'purExecutionMethod',
    headerText: i18n.t('采购执行方式'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row;width:"100%"; display: inline-flex;">
          <mt-select
                width="100%"
                :data-source="purExecutionMethodOptions"
                :placeholder="$t('请选择')"
                @change="purExecutionMethodChange"
                @focus="getPurExecutionMethod"
                :disabled="!isEditable"
                css-class="input-select"
                v-model="data.strategyConfigMap.purExecutionMethod"
              ></mt-select>
          </div>`,
          data() {
            return {
              data: {},
              purExecutionMethodOptions: [],
              isEditable: null
            }
          },
          mounted() {
            this.purExecutionMethodOptions = purExecutionMethodOptions
            this.isEditable = editFlag.isEditable
          },
          methods: {
            // 获取采购方式下拉列表
            getPurExecutionMethod() {
              this.$API.bgConfig.getPurExecutionMethod().then((res) => {
                if (res.code === 200) {
                  this.purExecutionMethodOptions.length = 0
                  res.data.forEach((e) => {
                    this.purExecutionMethodOptions.push({
                      value: e.code,
                      text: e.message
                    })
                  })
                }
              })
            },
            purExecutionMethodChange(e) {
              orderStrategySpecData[this.data.index].strategyConfigMap.purExecutionMethod = e.value
              // orderStrategyDataSource[this.data.index].purExecutionMethod =
              //   e.value;
            }
          }
        })
      }
    }
  },
  {
    // width: "250",
    field: 'autoReleaseFlag',
    headerText: i18n.t('采购订单审批通过后自动发布'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox  v-model="data.strategyConfigMap.autoReleaseFlag  == 1 ? true : false"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          methods: {
            handleChangeCellCheckBox(e) {
              this.data.autoReleaseFlag = e.checked ? 1 : 0
              orderStrategySpecData[this.data.index].strategyConfigMap.autoReleaseFlag = e.checked
                ? 1
                : 0
              // orderStrategyDataSource[this.data.index].autoReleaseFlag =
              //   e.checked ? 1 : 0;
            }
          }
        })
      }
    }
  },
  {
    // width: "210",
    field: 'autoCancelFlag',
    headerText: i18n.t('供方拒绝订单自动取消'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox :disabled="!isEditable"  :class="[!isEditable && 'checkbox-checkeditem']" v-model="data.strategyConfigMap.autoCancelFlag  == 1 ? true : false" @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          methods: {
            handleChangeCellCheckBox(e) {
              this.data.autoCancelFlag = e.checked
              orderStrategySpecData[this.data.index].strategyConfigMap.autoCancelFlag = e.checked
                ? 1
                : 0
              // orderStrategyDataSource[this.data.index].autoCancelFlag =
              //   e.checked ? 1 : 0;
            }
          }
        })
      }
    }
  },
  {
    // width: "300",
    field: 'allowModifyFlag',
    headerText: i18n.t('供方确认订单后采购订单允许修改'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox :disabled="!isEditable"  :class="[!isEditable && 'checkbox-checkeditem']" v-model="data.strategyConfigMap.allowModifyFlag  == 1 ? true : false" @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          methods: {
            handleChangeCellCheckBox(e) {
              this.data.allowModifyFlag = e.checked
              orderStrategySpecData[this.data.index].strategyConfigMap.allowModifyFlag = e.checked
                ? 1
                : 0
              // orderStrategyDataSource[this.data.index].allowModifyFlag =
              //   e.checked ? 1 : 0;
            }
          }
        })
      }
    }
  }
  // 当前版本不需要
  // {
  //   // width: "150",
  //   field: "deliverFlag",
  //   headerText: i18n.t("需要妥投"),
  //   template: function () {
  //     return {
  //       template: Vue.component("actionOption", {
  //         template: `<div style="flex-direction: row; display: inline-flex;">
  //         <mt-checkbox :disabled="!isEditable"  :class="[!isEditable && 'checkbox-checkeditem']" v-model="data.strategyConfigMap.deliverFlag  == 1 ? true : false" @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
  //         </div>`,
  //         data() {
  //           return { data: {}, isEditable: null };
  //         },
  //         mounted() {
  //           this.isEditable = editFlag.isEditable;
  //         },
  //         methods: {
  //           handleChangeCellCheckBox(e) {
  //             this.data.deliverFlag = e.checked;
  //             orderStrategySpecData[
  //               this.data.index
  //             ].strategyConfigMap.deliverFlag = e.checked ? 1 : 0;
  //             // orderStrategyDataSource[this.data.index].deliverFlag = e.checked
  //             //   ? 1
  //             //   : 0;
  //           },
  //         },
  //       }),
  //     };
  //   },
  // },
]

// 供应商预占库存配置
export const supplierTabColumn = [
  {
    width: '230',
    field: 'serialNumber',
    headerText: i18n.t('序号'),
    cellTools: [
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: () => editFlag.isEditable
      }
    ]
  },
  {
    width: '210',
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
    // template: function () {
    //   return {
    //     template: Vue.component("actionOption", {
    //       template: `<div style="flex-direction: row; display: inline-flex;">
    //       <mt-select
    //             :width="140"
    //             :data-source="businessTypeList"
    //             :placeholder="$t('请选择')"
    //             @change="businessTypeChange"
    //             @focus="getBusinessConfig"
    //             :disabled="!isEditable"
    //             css-class="input-select"
    //             :fields="{ text: 'businessTypeName', value: 'businessTypeId' }"
    //             v-model="data.businessTypeId"
    //           ></mt-select>
    //       </div>`,
    //       data() {
    //         return { data: {}, businessTypeList: [], isEditable: null };
    //       },
    //       mounted() {
    //         this.businessTypeList = businessTypeList;
    //         this.isEditable = editFlag.isEditable;
    //       },
    //       methods: {
    //         // 获取业务类型下拉
    //         getBusinessConfig() {
    //           this.$API.bgConfig.getLocalBusinessTypeList({}).then((res) => {
    //             this.businessTypeList.length = 0;
    //             res.data.forEach((e) => {
    //               this.businessTypeList.push(e);
    //             });
    //           });
    //         },
    //         businessTypeChange(e) {
    //           if (e != null && e?.itemData) {
    //             orderStockOccupySpecConfig[this.data.index].businessTypeCode =
    //               e.itemData.businessTypeCode;
    //             orderStockOccupySpecConfig[this.data.index].businessTypeId =
    //               e.itemData.businessTypeId;
    //             orderStockOccupySpecConfig[this.data.index].businessTypeName =
    //               e.itemData.businessTypeName;
    //             // orderStockOccupyDataSource[this.data.index].purExecutionMethod =
    //             //   e.value;
    //           } else {
    //             orderStockOccupySpecConfig[this.data.index].businessTypeCode =
    //               null;
    //             orderStockOccupySpecConfig[this.data.index].businessTypeId =
    //               null;
    //             orderStockOccupySpecConfig[this.data.index].businessTypeName =
    //               null;
    //             // orderStockOccupyDataSource[this.data.index].purExecutionMethod = null;
    //           }
    //         },
    //       },
    //     }),
    //   };
    // },
  },
  {
    // width: "440",
    field: 'supplierName',
    headerText: i18n.t('供应商'),
    width: '300',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.supplierCode}}-{{data.supplierName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
    // template: function () {
    //   return {
    //     template: Vue.component("actionOption", {
    //       template: `<div style="flex-direction: row; display: inline-flex;">
    //       <mt-select
    //             :width="360"
    //             :data-source="supplierTypeList"
    //             :placeholder="$t('请选择')"
    //             :allowFiltering="true"
    //             @change="supplierTypeChange"
    //             :filtering="getSupplierTimer"
    //             :disabled="!isEditable"
    //             css-class="input-select"
    //             :fields="{ text: 'text', value: 'supplierCode' }"
    //             @focus="getSupplier"
    //             v-model="data.supplierCode"
    //           ></mt-select>
    //       </div>`,
    //       data() {
    //         return {
    //           data: {},
    //           supplierTypeList: [],
    //           getSupplierTimer: null,
    //           isEditable: null,
    //         };
    //       },
    //       mounted() {
    //         this.getSupplierTimer = utils.debounce(this.getSupplier, 1000);
    //         this.supplierTypeList = supplierTypeList;
    //         this.isEditable = editFlag.isEditable;
    //       },
    //       methods: {
    //         // 获取供应商下拉
    //         getSupplier(e = { text: "" }) {
    //           let _supplierTypeList = [];
    //           this.$API.masterData
    //             .getFuzzySupplier({
    //               fuzzyNameOrCode: e.text,
    //             })
    //             .then((res) => {
    //               res.data.forEach((e) => {
    //                 _supplierTypeList.push({
    //                   ...e,
    //                   text: `${e.supplierCode}-${e.supplierName}`,
    //                 });
    //               });
    //               this.$set(this, "supplierTypeList", _supplierTypeList);
    //             });
    //         },
    //         supplierTypeChange(e) {
    //           if (e != null && e?.itemData) {
    //             orderStockOccupySpecConfig[this.data.index].supplierCode =
    //               e.itemData.supplierCode;
    //             orderStockOccupySpecConfig[this.data.index].supplierName =
    //               e.itemData.supplierName;
    //             orderStockOccupySpecConfig[this.data.index].supplierId =
    //               e.itemData.id;
    //           } else {
    //             orderStockOccupySpecConfig[this.data.index].supplierCode = null;
    //             orderStockOccupySpecConfig[this.data.index].supplierName = null;
    //             orderStockOccupySpecConfig[this.data.index].supplierId = null;
    //           }
    //           // orderStockOccupyDataSource[this.data.index].supplierCode =
    //           //   e.value;
    //         },
    //       },
    //     }),
    //   };
    // },
  },
  {
    // width: "300",
    field: 'stockOccupyFlag',
    headerText: i18n.t('预占库存'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="data.stockOccupyFlag  == 1 ? true : false"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          methods: {
            handleChangeCellCheckBox(e) {
              this.data.stockOccupyFlag = e.checked
              orderStockOccupySpecConfig[this.data.index].stockOccupyFlag = e.checked ? 1 : 0
              // orderStockOccupyDataSource[this.data.index].stockOccupyFlag =
              //   e.checked ? 1 : 0;
            }
          }
        })
      }
    }
  }
]

// 售后原因维护
export const aftermarketReasonColumn = [
  {
    // width: "210",
    field: 'serialNumber',
    headerText: i18n.t('序号'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        visibleCondition: () => editFlag.isEditable
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: () => editFlag.isEditable
      }
    ]
  },
  {
    // width: "260",
    field: 'afterSalesCode',
    headerText: i18n.t('售后编码')
  },
  {
    // width: "350",
    field: 'afterSalesReason',
    headerText: i18n.t('售后原因')
  }
]

// 采购申请配置
export const orderApplyColumn = [
  {
    width: '230',
    field: 'serialNumber',
    headerText: i18n.t('序号'),
    cellTools: [
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: () => editFlag.isEditable
      }
    ]
  },
  {
    width: '210',
    field: 'businessTypeName',
    headerText: i18n.t('业务类型'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-select
                :width="140"
                :data-source="businessTypeList"
                :placeholder="$t('请选择')"
                @change="businessTypeChange"
                @focus="getBusinessConfig"
                :disabled="!isEditable"
                css-class="input-select"
                :fields="{ text: 'businessTypeName', value: 'businessTypeId' }"
                v-model="data.businessTypeId"
              ></mt-select>
          </div>`,
          data() {
            return { data: {}, businessTypeList: [], isEditable: null }
          },
          mounted() {
            this.businessTypeList = businessTypeList
            this.isEditable = editFlag.isEditable
          },
          methods: {
            // 获取业务类型下拉
            getBusinessConfig() {
              this.$API.bgConfig.getLocalBusinessTypeList({}).then((res) => {
                this.businessTypeList.length = 0
                res.data.forEach((e) => {
                  this.businessTypeList.push(e)
                })
              })
            },
            businessTypeChange(e) {
              orderApplyStrategySpecData[this.data.index].businessTypeCode =
                e.itemData.businessTypeCode
              orderApplyStrategySpecData[this.data.index].businessTypeId = e.itemData.businessTypeId
              orderApplyStrategySpecData[this.data.index].businessTypeName =
                e.itemData.businessTypeName
              // orderApplyStrategyDataSource[this.data.index].businessTypeId =
              //   e.value;
            }
          }
        })
      }
    }
  },
  {
    // width: "280",
    field: 'isSplitFlag',
    headerText: i18n.t('采购申请行拆成每行一个单据'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="data.strategyConfigMap.isSplitFlag  == 1 ? true : false"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          methods: {
            handleChangeCellCheckBox(e) {
              this.data.isSplitFlag = e.checked
              orderApplyStrategySpecData[this.data.index].strategyConfigMap.isSplitFlag = e.checked
                ? 1
                : 0
              // orderApplyStrategyDataSource[
              //   this.data.index
              // ].strategyConfigMap.isSplitFlag = e.checked ? 1 : 0;
            }
          }
        })
      }
    }
  },
  {
    // width: "350",
    field: 'autoSourcingFlag',
    headerText: i18n.t('无价格自动转寻源（按照采购申请单转寻源订单）'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="data.strategyConfigMap.autoSourcingFlag  == 1 ? true : false"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          methods: {
            handleChangeCellCheckBox(e) {
              this.data.autoSourcingFlag = e.checked
              orderApplyStrategySpecData[this.data.index].strategyConfigMap.autoSourcingFlag =
                e.checked ? 1 : 0
              // orderApplyStrategyDataSource[
              //   this.data.index
              // ].strategyConfigMap.autoSourcingFlag = e.checked ? 1 : 0;
            }
          }
        })
      }
    }
  }
]

// 验收项配置
export const acceptanceConfigColumn = [
  {
    width: '230',
    field: 'serialNumber',
    headerText: i18n.t('序号'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        visibleCondition: () => editFlag.isEditable
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: () => editFlag.isEditable
      }
    ]
  },
  {
    width: '280',
    field: 'acceptanceCode',
    headerText: i18n.t('验收项编号')
  },
  {
    // width: "200",
    field: 'acceptanceTypeName',
    headerText: i18n.t('验收类型名称')
  }
  // {
  //   width: "200",
  //   field: "prepaidFlag",
  //   headerText: i18n.t("是否预付"),
  //   template: function () {
  //     return {
  //       template: Vue.component("actionOption", {
  //         template: `<div style="flex-direction: row; display: inline-flex;">
  //         <mt-checkbox v-model="data.strategyConfigMap.prepaidFlag  == 1 ? true : false" @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
  //         </div>`,
  //         data() {
  //           return { data: {} };
  //         },
  //         methods: {
  //           handleChangeCellCheckBox(e) {
  //             this.data.strategyConfigMap.prepaidFlag = e.checked ? 1 : 0;
  //             acceptanceData[this.data.index].strategyConfigMap.prepaidFlag =
  //               e.checked ? 1 : 0;
  //             acceptanceDataSource[this.data.index].prepaidFlag = e.checked
  //               ? 1
  //               : 0;
  //           },
  //         },
  //       }),
  //     };
  //   },
  // },
]

// 自动化配置
export const automaticConfigColumn = [
  {
    width: '200',
    field: 'serialNumber',
    headerText: i18n.t('序号'),
    cellTools: [
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: () => editFlag.isEditable
      }
    ]
  },
  {
    width: '220',
    field: 'businessTypeName',
    headerText: i18n.t('业务类型'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-select
                :width="140"
                :data-source="businessTypeList"
                :placeholder="$t('请选择')"
                @change="businessTypeChange"
                @focus="getBusinessConfig"
                :disabled="!isEditable"
                css-class="input-select"
                :fields="{ text: 'businessTypeName', value: 'businessTypeId' }"
                v-model="data.businessTypeId"
              ></mt-select>
          </div>`,
          data() {
            return { data: {}, businessTypeList: [], isEditable: null }
          },
          mounted() {
            this.businessTypeList = businessTypeList
            this.isEditable = editFlag.isEditable
          },
          methods: {
            // 获取业务类型下拉
            getBusinessConfig() {
              this.$API.bgConfig.getLocalBusinessTypeList({}).then((res) => {
                this.businessTypeList.length = 0
                res.data.forEach((e) => {
                  this.businessTypeList.push(e)
                })
              })
            },
            businessTypeChange(e) {
              if (e != null && e?.itemData) {
                automaticData[this.data.index].businessTypeCode = e.itemData.businessTypeCode
                automaticData[this.data.index].businessTypeId = e.itemData.businessTypeId
                automaticData[this.data.index].businessTypeName = e.itemData.businessTypeName
                // automaticDataSource[this.data.index].purExecutionMethod = e.value;
              } else {
                automaticData[this.data.index].businessTypeCode = null
                automaticData[this.data.index].businessTypeId = null
                automaticData[this.data.index].businessTypeName = null
                // automaticDataSource[this.data.index].purExecutionMethod = null;
              }
            }
          }
        })
      }
    }
  },
  {
    width: '450',
    field: 'supplierName',
    headerText: i18n.t('供应商'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-select
                :width="360"
                :data-source="supplierTypeList"
                :placeholder="$t('请选择')"
                :allowFiltering="true"
                :filtering="getSupplierTimer"
                :disabled="!isEditable"
                css-class="input-select"
                :fields="{ text: 'text', value: 'supplierCode' }"
                @change="supplierTypeChange"
                @focus="getSupplier"
                v-model="data.supplierCode"
              ></mt-select>
          </div>`,
          data() {
            return {
              data: {},
              supplierTypeList: [],
              getSupplierTimer: null,
              isEditable: null
            }
          },
          mounted() {
            this.getSupplierTimer = utils.debounce(this.getSupplier, 1000)
            this.supplierTypeList = supplierTypeList
            this.isEditable = editFlag.isEditable
          },
          methods: {
            // 获取供应商下拉
            getSupplier(e = { text: '' }) {
              this.$API.masterData
                .getFuzzySupplier({
                  fuzzyNameOrCode: e.text
                })
                .then((res) => {
                  var _supplierTypeList = []
                  res.data.forEach((e) => {
                    _supplierTypeList.push({
                      ...e,
                      text: e.supplierCode + '-' + e.supplierName
                    })
                  })

                  this.$set(this, 'supplierTypeList', _supplierTypeList)
                })
            },
            supplierTypeChange(e) {
              if (e != null && e?.itemData) {
                automaticData[this.data.index].supplierCode = e.itemData.supplierCode
                automaticData[this.data.index].supplierName = e.itemData.supplierName
                automaticData[this.data.index].supplierId = e.itemData.id
              } else {
                automaticData[this.data.index].supplierCode = null
                automaticData[this.data.index].supplierName = null
                automaticData[this.data.index].supplierId = null
              }
              // automaticDataSource[this.data.index].supplierCode = e.value;
            }
          }
        })
      }
    }
  },
  {
    width: 'auto',
    field: 'orderAutoConfirm',
    headerText: i18n.t('采购订单自动确认'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="data.strategyConfigMap.orderAutoConfirm  == 1 ? true : false"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.orderAutoConfirm = e.checked;
              automaticData[this.data.index].strategyConfigMap.orderAutoConfirm = e.checked ? 1 : 0
              // automaticDataSource[this.data.index].orderAutoConfirm = e.checked
              //   ? "1"
              //   : "0";
            }
          }
        })
      }
    }
  },
  {
    width: 'auto',
    field: 'saleOrderAutoConfirm',
    headerText: i18n.t('售后订单自动确认'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="data.strategyConfigMap.saleOrderAutoConfirm  == 1 ? true : false"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.saleOrderAutoConfirm = e.checked;
              automaticData[this.data.index].strategyConfigMap.saleOrderAutoConfirm = e.checked
                ? 1
                : 0
              // automaticDataSource[this.data.index].saleOrderAutoConfirm =
              //   e.checked ? "1" : "0";
            }
          }
        })
      }
    }
  },
  {
    width: 'auto',
    field: 'accountReconciliationAutoConfirm',
    headerText: i18n.t('对账单自动确认'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="data.strategyConfigMap.accountReconciliationAutoConfirm  == 1 ? true : false"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          methods: {
            handleChangeCellCheckBox(e) {
              // this.data.accountReconciliationAutoConfirm = e.checked;
              automaticData[this.data.index].strategyConfigMap.accountReconciliationAutoConfirm =
                e.checked ? 1 : 0
              // automaticDataSource[
              //   this.data.index
              // ].accountReconciliationAutoConfirm = e.checked ? "1" : "0";
            }
          }
        })
      }
    }
  }
]

// 定时同步配置
export const timingSyncColumn = [
  {
    width: '200',
    field: 'serialNumber',
    headerText: i18n.t('序号'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑')
        // visibleCondition: () => editFlag.isEditable,
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除')
        // visibleCondition: () => editFlag.isEditable,
      }
    ]
  },
  {
    width: '220',
    field: 'title',
    headerText: i18n.t('同步名称')
  },
  {
    width: '180',
    field: 'loopStatus',
    headerText: i18n.t('是否循环'),
    valueConverter: {
      type: 'map',
      //0-不循环，1. 循环
      map: {
        0: i18n.t('不循环'),
        1: i18n.t('循环')
      }
    }
  },
  {
    width: '180',
    field: 'loopType',
    headerText: i18n.t('循环周期'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div  v-if="this.data.loopStatus==1" style="flex-direction: row; display: inline-flex;">
            <span v-if="this.data.loopStatus==1" style="margin-right:10px;">{{this.loopType}}</span>
          </div>`,
          data() {
            return { data: {} }
          },
          computed: {
            loopType() {
              if (this.data?.loopType) {
                return this.data.loopType == 1 ? i18n.t('按天') : i18n.t('按周')
              } else {
                return null
              }
            }
          },
          methods: {}
        })
      }
    }
  },
  {
    // width: "300",
    field: 'time',
    headerText: i18n.t('同步时间'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div  v-if="this.data.loopStatus==1" style="flex-direction: row; display: inline-flex;">
            <span v-if="this.data.loopType==2" style="margin-right:10px;">{{this.weekData}}</span>
            <span>{{this.time}}</span>
          </div>`,
          data() {
            return { data: {} }
          },
          computed: {
            time() {
              if (this.data?.time) {
                return this.data.time.split(' ')[1].substr(0, 5)
              } else {
                return null
              }
            },
            weekData() {
              if (this.data?.week) {
                let selectedDay = Number(this.data.week).toString(2).split('').reverse()
                let _tempDay = []
                selectedDay.forEach((e, i) => {
                  if (e * 10 ** i) {
                    _tempDay.push(e * 10 ** i)
                  }
                })
                let tempArr = this.data.weekData.map((item) => item.value)
                let weekData = []
                _tempDay.forEach((e) => {
                  if (tempArr.indexOf(e) > -1) {
                    weekData.push(this.data.weekData[tempArr.indexOf(e)].text)
                  }
                })
                return weekData.toString().replaceAll(',', '/')
              } else {
                return null
              }
            }
          },
          methods: {}
        })
      }
    }
  },
  {
    // width: "300",
    field: 'reminder',
    headerText: i18n.t('同步失败提醒人'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
            <span>{{this.reminderData}}</span>
          </div>`,
          data() {
            return { data: {} }
          },
          computed: {
            reminderData() {
              let tempArr = this.data.userList.map((item) => item.employeeId)
              let reminderData = []
              this.data.reminder.split(',').forEach((e) => {
                if (tempArr.indexOf(e) > -1) {
                  reminderData.push(this.data.userList[tempArr.indexOf(e)].text)
                }
              })
              return reminderData.toString().replaceAll(',', '，')
            }
          }
        })
      }
    }
  }
]

// 采购申请数据
export let purchaseDataSource = []

// 寻源需求并单数据
export let searchSourceDataSource = []

// 供应商送货单合并生成配置数据
export let supplierDataSource = []

// 供货商预占库存选项
export const supplierTabRadioData = [
  {
    label: i18n.t('订单生成时校验并预占库存'),
    value: '0',
    disabled: !editFlag.isEditable
  },
  {
    label: i18n.t('订单生成时不校验并预占库存'),
    value: '1',
    disabled: !editFlag.isEditable
  }
]
