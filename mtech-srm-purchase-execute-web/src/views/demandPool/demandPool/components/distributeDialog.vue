// 分配用户
<template>
  <mt-dialog ref="dialog" :buttons="buttons" :header="header">
    <div class="dialog-content">
      <mt-tooltip :content="content" position="BottomCenter" target="#box">
        <div class="source-label label-left">
          <span>{{ $t('请选择分配人') }}</span>
          <MtIcon id="box" class="icon-style" name="icon_outline_prompt" />
        </div>
      </mt-tooltip>
      <debounce-filter-select
        :width="820"
        :request="getUser"
        v-model="selectedUser"
        float-label-type="Never"
        :data-source="userList"
        :show-clear-button="true"
        :fields="{ text: 'text', value: 'uid' }"
        :placeholder="$t('请选择分配人')"
      ></debounce-filter-select>
    </div>
  </mt-dialog>
</template>

<script>
import Vue from 'vue'

export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      content: function () {
        return {
          template: Vue.component('demo', {
            template: `
            <div id="tooltip" ref="content" style="width:217px;font:14px;padding:8px 10px;">
                {{$t("分配确认后，需求将出现在分配人“我认领的明细”中")}}
            </div>`,
            data() {
              return {
                data: {}
              }
            }
          })
        }
      },
      userList: [],
      selectedUser: -1,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons,
        cssClass: this.modalData.cssClass.trim()
      }
    },
    header() {
      return this.modalData.title
    },
    id() {
      console.log(this.modalData.id)
      return this.modalData.id || []
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    // 获取 用户列表
    getUser(e) {
      const { text: fuzzyName } = e
      this.userList = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        this.userList = tmp
      })
    },
    confirm() {
      if (this.selectedUser <= -1) {
        this.$toast({ content: this.$t('请选择分配的人员') })
        return
      }
      let _selectRow = this.userList.find((item) => {
        return item.uid == this.selectedUser
      })
      if (this.modalData?.businessTypeCode === 'BTTCL006') {
        this.$API.purchaseRequest
          .logisticsTransfer({
            purchaserCode: _selectRow.employeeCode,
            purchaserName: _selectRow.employeeName,
            idList: this.id
          })
          .then((res) => {
            this.$store.commit('endLoading')
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('confirm-function')
            }
          })
        return
      }
      if (this.modalData.tabIndex == 0) {
        this.$API.purchaseRequest
          .claimDetail({
            claimUserId: _selectRow.uid,
            claimUserName: _selectRow.employeeName,
            idList: this.id
          })
          .then((res) => {
            this.$store.commit('endLoading')
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('confirm-function')
            }
          })
      } else {
        this.$API.purchaseRequest
          .allotDetail({
            claimUserId: _selectRow.uid,
            claimUserName: _selectRow.employeeName,
            idList: this.id
          })
          .then((res) => {
            this.$store.commit('endLoading')
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('confirm-function')
            }
          })
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 40px;
  padding-left: 22px;
  font-size: 16px;
}
#container {
  float: left;
  transform: translateX(-50%);
  margin-top: 75px;
}
.icon-style {
  font-size: 18px;
  vertical-align: bottom;
}
</style>
