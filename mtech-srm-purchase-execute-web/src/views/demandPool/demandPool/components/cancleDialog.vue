<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="$t('申请单取消')"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <mt-form-item prop="closeReason" :label="$t('原因备注')" class="full-width">
        <mt-input
          v-model="ruleForm.closeReason"
          v-if="maxlength1"
          :maxlength="maxlength1"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      maxlength1: 500,
      uploadData: [],
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {
        closeReason: ''
      },
      rules: {},
      entryId: '' // 带入的需要取消的id
    }
  },
  methods: {
    fileChange(data) {
      this.uploadData = data
    },
    dialogInit(entryId) {
      this.entryId = entryId
      this.ruleForm.closeReason = ''
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const params = {
            idList: [this.entryId],
            rejectReason: this.ruleForm.closeReason
          }
          this.$API.purchaseRequest.cancelClaim(params).then(() => {
            this.$toast({ content: this.$t('驳回操作成功'), type: 'success' })
            this.handleClose()
            this.$emit('confirmSuccess')
          })
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
