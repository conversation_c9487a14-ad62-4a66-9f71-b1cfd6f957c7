<template>
  <mt-dialog
    ref="consolidation-error-dialog"
    css-class="order-consolidation-dialog"
    :buttons="buttons"
    :header="modalData.title"
  >
    <div class="top-info">
      <span>{{ $t('不符合采购订单需求并单配置') }}</span>
      <span>{{ $t('业务类型：') }}{{ modalData.businessTypeName }}</span>
      <p>{{ $t('配置如下：') }}</p>
      <div v-for="(item, index) in modalData.checkItems" class="check-item" :key="index">
        <div>
          <span>{{ index + 1 }}.</span>
          <span>{{ item }}</span>
        </div>
      </div>
    </div>
    <mt-data-grid
      class="consolidation-dialog"
      :data-source="modalData.consolidationData"
      :column-data="modalData.columnData"
      ref="dataGrid"
      locale="zh"
    ></mt-data-grid>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { isPrimary: 'true', content: this.$t('我知道了') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  mounted() {
    this.$refs['consolidation-error-dialog'].ejsRef.show()
  },
  methods: {
    cancel() {
      this.$refs['consolidation-error-dialog'].ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.top-info {
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: normal;
  color: rgba(41, 41, 41, 1);
  margin: 25px 0 0 25px;
  span {
    margin-right: 20px;
  }
  p {
    margin: 5px 0;
  }
  .check-item {
    margin: 5px 0 5px 14px;
  }
}
.consolidation-dialog {
  margin: 20px 22px 0 22px;
}
</style>
