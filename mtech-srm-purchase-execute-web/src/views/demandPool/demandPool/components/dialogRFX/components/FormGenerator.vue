<template>
  <mt-form :model="form" :rules="formRules" ref="form" class="form-generator-form">
    <template v-for="(formItem, index) in fieldDefines">
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'text'"
      >
        <mt-input
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :show-clear-button="false"
          @change="handleFormItemChange($event, formItem)"
          :placeholder="$t(formItem.form.label)"
          :label="$t(formItem.form.label)"
          type="text"
          :maxlength="75"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'number'"
      >
        <mt-input-number
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          @change="handleFormItemChange($event, formItem)"
          :placeholder="$t(formItem.form.label)"
          :min="typeof formItem.form.min === 'number' ? formItem.form.min : 0"
          :max="formItem.form.max"
          :precision="4"
          :show-clear-button="false"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="{
          ['form-generator-item-col-' + formItem.form.col]: true
        }"
        v-if="formItem.form.type === 'select'"
      >
        <mt-select
          :data-source="formItem.form.dataSource"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :show-clear-button="true"
          @select="handleFormSelectItemChange($event, formItem)"
          @change="handleFormItemChange($event, formItem)"
          :fields="{ text: 'text', value: 'value' }"
          :placeholder="$t(formItem.form.label)"
          @open="handleFormItemOpen($event, formItem)"
        />
        <span
          v-if="showBadgeList.includes(formItem.form.modalName)"
          class="label-badge-content"
          :style="{ left: badgeMesMap[formItem.form.modalName]['left'] }"
          :badge-msg="$t(badgeMesMap[formItem.form.modalName]['msg'])"
        >
          ?
        </span>
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'multiSelect'"
      >
        <mt-multi-select
          :data-source="formItem.form.dataSource"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :show-clear-button="false"
          @change="handleFormItemChange($event, formItem)"
          :placeholder="$t(formItem.form.label)"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :label="$t(formItem.form.interactionLabel)"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        :key="formItem.form.interactionModel"
        v-if="formItem.form.interactionModel"
      >
        <mt-input
          v-model="form[formItem.form.interactionModel]"
          :show-clear-button="false"
          :placeholder="$t(formItem.form.interactionLabel)"
          disabled
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'date'"
      >
        <mt-date-picker
          @change="handleFormItemChange($event, formItem)"
          :open-on-focus="true"
          format="yyyy-MM-dd"
          time-stamp
          :show-clear-button="false"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :placeholder="$t(formItem.form.label)"
          :min="formItem.form.min"
          :max="formItem.form.max"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'time'"
      >
        <mt-time-picker
          @change="handleFormItemChange($event, formItem)"
          :open-on-focus="true"
          :show-clear-button="false"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :placeholder="$t(formItem.form.label)"
      /></mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'datetime'"
      >
        <DateTimePicker
          @change="handleFormItemChange($event, formItem)"
          :open-on-focus="true"
          format="yyyy-MM-dd HH:mm:ss"
          time-stamp
          :show-clear-button="false"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :placeholder="$t(formItem.form.label)"
          :min="formItem.form.min"
          :max="formItem.form.max"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'dropdown-tree'"
      >
        <mt-drop-down-tree
          @change="handleFormItemChange($event, formItem)"
          :open-on-focus="true"
          :show-clear-button="false"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :placeholder="$t(formItem.form.label)"
          :fields="[]"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.form.modalName"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'checkbox'"
      >
        <mt-select
          :data-source="[
            { text: $t('是'), value: 1 },
            { text: $t('否'), value: 0 }
          ]"
          v-model="form[formItem.form.modalName]"
          :disabled="formItem.form.readonly"
          :show-clear-button="true"
          @change="handleFormItemChange($event, formItem)"
          :fields="{ text: 'text', value: 'value' }"
          :placeholder="$t(formItem.form.label)"
        />
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :prop="formItem.field"
        :label="$t(formItem.form.label)"
        :key="formItem.form.label"
        class="form-generator-form-item"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'debounce-select'"
      >
        <debounce-filter-select
          v-model="form[formItem.form.modalName]"
          :data-source="formItem.form.employeeList"
          @change="handleFormItemChange($event, formItem)"
          :show-clear-button="false"
          :fields="{ text: 'text', value: 'uid' }"
          :placeholder="$t(formItem.form.label)"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item
        :disabled="formItem.form.readonly"
        :key="index"
        :class="'form-generator-item-col-' + formItem.form.col"
        v-if="formItem.form.type === 'null'"
      >
        <!-- 为了布局，空的占位符-->
      </mt-form-item>
    </template>
    <slot :records="fieldDefines"></slot>
  </mt-form>
</template>

<script>
import { cloneDeep } from 'lodash'
// 这个组件跟着需求一步步做的，到后面就非常恶了心，并不比直接页面写死然后if-else好
import DateTimePicker from './DateTimePicker.vue'
export default {
  name: 'FormGenerator',
  props: {
    fieldDefines: {
      type: Array,
      required: true
    },
    formFieldConfig: {
      type: Object,
      required: true
    },
    formRules: {
      type: Object,
      required: true
    }
    // purOrgNameDataSource: {
    //   type: Array,
    //   required: true,
    // },
  },
  data() {
    return {
      form: {},
      rules: {},
      currentEmployees: [],
      showBadgeList: ['priceControl', 'priceDirectionControl', 'accountingMode'],
      badgeMesMap: {
        priceControl: {
          left: '70px',
          msg: `1. 整单报价：供方报价时同一标案的所有明细行需全部报价; 
            2. 首次整单报价：供方报价时，首次填写需同一标案所有明细行全部报价，重新报价时可只更改某一行; 
            3. 无限制：供方报价时同一标案可自由选择报那些行；
          `
        },
        priceDirectionControl: {
          left: '100px',
          msg: `1. 整单控制：同一标案多标的时，按照总价控制供方报价的递增/递减；
            2. 单行控制：同一标案多标的时，按照单行控制供方报价的递增/递减；
          `
        },
        accountingMode: {
          left: '70px',
          msg: `1. 部分中标：定标时，标案下的每一行标的可选择多个供应商中标；
            2. 整单中标：一个标案只能一个供应商中标；
          `
        }
      }
    }
  },
  components: {
    DateTimePicker,
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  watch: {
    fieldDefines: {
      immediate: true,
      handler() {
        this.handlePropsChange()
      }
    }
    // purOrgNameDataSource: {
    //   immediate: true,
    //   handler(val) {
    //     this.form.purOrgName = "";
    //     let purOrgNameConf = this.fieldDefines.find(
    //       (item) => item.fieldCode === "purOrgName"
    //     );
    //     if (purOrgNameConf) {
    //       purOrgNameConf.form.dataSource = val;
    //     }
    //     let siteNameConf = this.fieldDefines.find(
    //       (item) => item.fieldCode === "siteName"
    //     );
    //     if (siteNameConf) {
    //       siteNameConf.form.dataSource = [];
    //     }
    //   },
    // },
  },

  methods: {
    handlePropsChange() {
      if (this.fieldDefines.length && this.formFieldConfig) {
        let _form = cloneDeep(this.form)
        for (let field of this.fieldDefines) {
          let config = this.formFieldConfig[field.fieldCode]
          if (config) {
            this.$set(field, 'form', {
              col: 2,
              handler: () => {},
              readonly: field.readonly,
              ...config,
              required: !!field.required,
              label: field.fieldName,
              modalName: field.fieldCode,
              dataSource: config.dataSource || []
            })
            if (config.defaultValue) {
              if (config.type === 'select' && !config.api) {
                config.dataSource = Array.isArray(config?.dataSource) ? config?.dataSource : []
                this.form[field.fieldCode] = config.dataSource.find(
                  (item) => item.value === config.defaultValue()
                )?.value
              } else {
                this.form[field.fieldCode] = config.defaultValue()
              }
            }
            if (config.api) {
              config.api.then((data) => {
                this.$set(field.form, 'dataSource', data)
                if (config.defaultValue) {
                  this.form[field.fieldCode] = data.find(
                    (item) => item.value === config.defaultValue()
                  )?.value
                }
              })
            }
            // if (field.required) {
            //   this.$set(this.rules, field.fieldCode, [
            //     {
            //       required: true,
            //       message: i18n.t("请输入") + field.fieldName,
            //     },
            //   ]);
            // }
            // if (config.valid) {
            //   this.$set(this.rules, field.fieldCode, config.valid);
            // }
            _form[field.fieldCode] = config.defaultValue ? config.defaultValue() : ''
            // this.$set(
            //   this.form,
            //   field.fieldCode,
            //   config.defaultValue ? config.defaultValue() : ""
            // );
          } else {
            field.form = {}
          }
        }
        this.form = _form
      }
    },
    parentGetFormData() {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate((valid) => {
          valid ? resolve(this.form) : reject()
        })
      })
    },
    handleFormItemChange(event, formItem) {
      if (formItem.form.interactionModel && formItem.form.dataSource.length) {
        this.$set(
          this.form,
          formItem.form.interactionModel,
          formItem.form.dataSource.find((item) => item.value === event.value)[
            formItem.form.interactionModel
          ]
        )
      }
      formItem.form.handler(event, formItem, this)
    },
    handleFormItemOpen(event, formItem) {
      if (
        formItem.fieldCode == 'purOrgName' &&
        (!formItem.dataSource || formItem.dataSource.length === 0)
      ) {
        console.error('open事件成功')
        this.updatePurOrgName({
          data: { companyId: sessionStorage.getItem('dialogRFX_companyId') }
        })
      }
    },
    handleFormSelectItemChange(event, formItem) {
      if (this[formItem.fieldCode + 'ChangeHandler']) {
        if (event.itemData) {
          this[formItem.fieldCode + 'ChangeHandler'](event.itemData)
        }
      }
    },
    // async companyNameChangeHandler(itemData) {
    //   this.updatePurOrgName(itemData);
    // },
    async purOrgNameChangeHandler(itemData) {
      //弹框打开时，无工厂数据，通过接口获取
      if (!sessionStorage.getItem('dialogRFX_siteCode') && itemData) {
        console.error(itemData)
        this.updateSiteName(itemData)
      }
    },
    // 采购组织
    async updatePurOrgName(itemData) {
      this.form.purOrgName = ''
      //弹框打开时，无工厂数据，通过接口获取
      if (!sessionStorage.getItem('dialogRFX_siteCode')) {
        this.form.siteName = ''
        let purOrgNameConf = this.fieldDefines.find((item) => item.fieldCode === 'purOrgName')
        let res = await this.$API.masterData
          .permissionOrgList({
            orgId: itemData.data.companyId
          })
          .catch(() => {})
        if (res && res.data) {
          console.error('调用接口成功')
          purOrgNameConf.form.dataSource = res.data.map((item) => ({
            text: item.organizationName,
            value: item.organizationName,
            data: {
              purOrgName: item.organizationName,
              purOrgCode: item.organizationCode,
              purOrgId: item.id
            }
          }))
        }
      } else {
        console.error('进入方法')
        if (!sessionStorage.getItem('dialogRFX_siteId')) return
        let purOrgNameConf = this.fieldDefines.find((item) => item.fieldCode === 'purOrgName')

        let res = await this.$API.masterData
          .getByOrgIdAndBgOrgTypeCode({
            siteId: sessionStorage.getItem('dialogRFX_siteId'),
            orgTypeCode: 'BUORG002ADM'
          })
          .catch(() => {})
        if (res && res.data) {
          console.error('调用接口成功')
          purOrgNameConf.form.dataSource = res.data.map((item) => ({
            text: item.organizationName,
            value: item.organizationName,
            data: {
              purOrgName: item.organizationName,
              purOrgCode: item.organizationCode,
              purOrgId: item.id
            }
          }))
        }
      }

      let siteNameConf = this.fieldDefines.find((item) => item.fieldCode === 'siteName')
      if (siteNameConf) {
        siteNameConf.form.dataSource = []
      }
    },
    // 工厂
    async updateSiteName(itemData) {
      this.form.siteName = ''
      let siteNameConf = this.fieldDefines.find((item) => item.fieldCode === 'siteName')
      if (siteNameConf && itemData?.data?.purOrgId) {
        siteNameConf.form.dataSource = await this.getSiteNameDataSource(itemData.data.purOrgId)
      }
    },
    async getSiteNameDataSource(organizationId) {
      if (!organizationId) return []
      let res = await this.$API.masterData
        .permissionSiteList({
          buOrgId: organizationId,
          companyId: sessionStorage.getItem('dialogRFX_companyId'),
          orgLevelTypeCode: 'ORG06'
        })
        .catch(() => {})
      if (res.data == null) {
        return []
      }
      return res.data.map((item) => ({
        text: item.orgName,
        value: item.orgName,
        data: {
          siteName: item.orgName,
          siteId: item.id,
          siteCode: item.orgCode
        }
      }))
    },
    async sourcingModeChangeHandler(itemData) {
      this.form.sourcingObj = ''
      let sourcingObjConf = this.fieldDefines.find((item) => item.fieldCode === 'sourcingObj')
      if (sourcingObjConf) {
        sourcingObjConf.form.dataSource = await this.getSourcingObjDataSource(itemData)
      }
    },
    async getSourcingObjDataSource(data) {
      let res = await this.$API.rfxList
        .getConfigList({
          page: { current: 1, size: 1000 },
          condition: 'and',
          defaultRules: [
            {
              field: 'sourcingMode',
              type: 'string',
              operator: 'equal',
              value: data.value
            },
            {
              field: 'status',
              type: 'string',
              operator: 'equal',
              value: 1
            },
            {
              field: 'businessTypeCode',
              type: 'string',
              operator: 'equal',
              value: sessionStorage.getItem('dialogRFX_businessTypeCode')
            }
          ]
        })
        .catch(() => {})
      if (!res) {
        return []
      }
      return res.data.records.map((item) => ({
        text: item.sourcingObj,
        value: item.sourcingObj,
        data: {
          sourcingObj: item.sourcingObj,
          sourcingObjType: item.sourcingObjType
        }
      }))
    }
  }
}
</script>

<style lang="scss" scoped>
.form-generator-form {
  display: flex;
  flex-flow: row wrap;
  row-gap: 20px;
  .form-generator-form-item {
    padding-left: 10px;
    padding-right: 10px;
  }

  .form-generator-item-col-4 {
    display: block;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .form-generator-item-col-2 {
    display: block;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .form-generator-item-col-1 {
    display: block;
    flex: 0 0 100%;
    max-width: 100%;
  }
}
</style>

<style lang="scss">
::placeholder {
  color: #9a9a9a;
  font-size: 12px;
}
</style>
