<template>
  <mt-date-time-picker v-bind="$attrs" :value="displayValue" @input="input" @change="change" />
</template>

<script>
export default {
  props: {
    value: {
      type: [String, Number, Object],
      default: () => null
    },
    valueFormat: {
      type: String,
      default: ''
    },
    // 修复 mt-date-time-picker 在父组件修改 value 后，它会把 value 改为 Date 类型的 bug
    timeStamp: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      displayValue: null,
      userInput: null
    }
  },
  methods: {
    change(event) {
      event.value = this.getValue(event.value)
      this.$emit('change', event)
    },
    input(value) {
      const ret = this.getValue(value)
      this.userInput = ret
      this.$emit('input', ret)
    },
    getValue(value) {
      if (!value) {
        return value
      }
      let result = value
      let valueFormat = this.valueFormat
      if (this.timeStamp) {
        valueFormat = 'time-stamp'
      }
      if (valueFormat) {
        result =
          valueFormat === 'time-stamp' ? value.getTime() : this.formatTime(value, valueFormat)
      }
      return result
    },
    formatTime(date, fmt = 'yyyy-MM-dd HH:mm:ss') {
      if (!date) {
        return
      }
      let ret
      const opt = {
        'y+': date.getFullYear().toString(),
        'M+': (date.getMonth() + 1).toString(),
        'd+': date.getDate().toString(),
        'H+': date.getHours().toString(),
        'm+': date.getMinutes().toString(),
        's+': date.getSeconds().toString()
      }
      for (let k in opt) {
        ret = new RegExp('(' + k + ')').exec(fmt)
        if (ret) {
          fmt = fmt.replace(
            ret[1],
            ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, '0')
          )
        }
      }
      return fmt
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(value) {
        if (this.userInput === value) {
          return
        }
        if (typeof value === 'object') {
          this.displayValue = value
        } else if (/^\d{13}$/.test(value)) {
          this.displayValue = new Date(Number(value))
        } else {
          this.displayValue = new Date(value)
        }
      }
    }
  }
}
</script>
