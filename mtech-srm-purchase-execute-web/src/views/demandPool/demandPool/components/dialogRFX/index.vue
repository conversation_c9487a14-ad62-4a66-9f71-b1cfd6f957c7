<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content" style="padding-top: 20px">
      <FormGenerator
        :form-rules="formRules"
        :field-defines="fixedFieldDefines"
        :form-field-config="fixedFormFieldConfig"
        ref="fixedForm"
        :pur-org-name-data-source="modalData.purOrgNameDataSource"
      />
      <FormGenerator
        :form-rules="formRules"
        :field-defines="headerFieldDefines"
        :form-field-config="headerFormFieldConfig"
        ref="headerForm"
        :key="updateIndex"
        :pur-org-name-data-source="modalData.purOrgNameDataSource"
      />
      <FormGenerator
        :form-rules="formRules"
        :field-defines="strategyFieldDefines"
        :form-field-config="strategyFormFieldConfig"
        :pur-org-name-data-source="modalData.purOrgNameDataSource"
        ref="strategyForm"
      />
    </div>
  </mt-dialog>
</template>

<script>
import {
  defaultHeaderFields,
  saveObjectMaps,
  saveObjectExtMaps,
  saveObjectLogisticsMaps
} from './config'
import FormGenerator from './components/FormGenerator.vue'
import { i18n } from '@/main.js'
import { cloneDeep } from 'lodash'
import { isDate } from './config/is'
import utils from '@/utils/utils'
export default {
  name: 'CreateRequirement',
  components: { FormGenerator },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: i18n.t('取消') }
        },
        {
          click: this.createRFX,
          buttonModel: { isPrimary: 'true', content: i18n.t('确定') }
        }
      ],
      createRFXLoading: false,
      formObject: {
        rfxCode: null,
        siteCode: null
      },
      formRules: {},
      strategyInfo: {},
      // 获取头部字段
      headerFixedParams: {
        rfxName: '', //标题
        sourcingObj: '', //寻源对象
        sourcingMode: '', //寻源方式
        businessTypeCode: ''
      },
      // 头部字段
      headerHeaderParams: {
        currencyCode: 'CNY', //货币编码
        companyCode: '', //公司编码
        departmentCode: '', //部门编码
        siteCode: '', //工厂
        strategyConfigId: '' //策略
      },
      // 获取策略字段
      strategyConfigParams: {
        businessTypeCode: '',
        sourcingMode: '', //寻源方式
        sourcingType: 'new_products', //询价类型
        sourcingObjType: 'common', //询价对象
        biddingMode: 'target', //招标方式
        sourcingDirection: 'forward', //寻源方向
        minSupplierBiddingQuantity: 1, //最少供应商报价数量
        nonDirectionalSupNum: 0, //非定向议价供应商数量
        // supplierRange: "category_qualified", //拓展（定制）   字段暂不显示   不是拓展
        supplierSelectionRange: 0, //供应商选择范围
        supplierQuotationRule: 0, //供应商报价规则
        allocationRange: 0, //配额分配范围
        directionalBargaining: 1, //是否定向议价
        sealedPrice: 1, //是否密封报价
        publicOptimalPrice: 1, //公开最优价
        publicRanking: 1, //公开排名
        publicQuotation: 1, //公开报价
        publicIdentity: 1, //公开身份
        publicParticipantNum: 1, //公开参与者数量
        allocationRequired: 1, //配额是否必输
        bidNumRequired: 1, //中标数量是否必输
        biddingPromotion: 1, //招标单是否晋级
        roundCount: '1', //轮次总计
        // stepQuote: 1,
        accountingMode: 'single_item', //核算方式
        bidEvaluationMode: 'mini_price', //招标评估方式
        tecBidStartTime: null,
        tecOpenBidTime: null,
        tecScoreEndTime: null,
        needTecBid: null,
        pointMode: null // 定点模式 1：内部定点，2：推荐定点
      },
      updateIndex: 0,
      fixedFieldDefines: defaultHeaderFields,
      headerFieldDefines: [],
      strategyFieldDefines: [],
      strategyFieldDefinesCopy: [],
      fixedFormFieldConfig: {},
      headerFormFieldConfig: {},
      strategyFormFieldConfig: {},
      cacheHeaderFixedParams: {}, //存一下sourcingObj，sourcingMode，businessTypeCode
      equalDept: true,
      equalSite: true
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    isGeneral() {
      // 是否通采
      return this.modalData.businessTypeCode === 'BTTCL004'
    }
  },
  created() {
    this.initDialogDefaultValue()
  },
  mounted() {
    this.$API.rfxList.addRfxHeaderValid().then((res) => {
      this.$refs['dialog'].ejsRef.show()
      if (res.code === 200) {
        let _formRules = utils.formatRules(res.data)
        Object.keys(_formRules).forEach((e) => {
          if (_formRules[e].length < 1) {
            delete _formRules[e]
          }
        })
        if (_formRules.rfxName == undefined) {
          _formRules.rfxName = []
        }
        _formRules.rfxName.push({
          max: 128,
          message: this.$t(`标题长度不能超过128个字符`),
          trigger: 'blur'
        })
        // if (_formRules.nonDirectionalSupNum == undefined) {
        //   _formRules.nonDirectionalSupNum = [];
        // }
        // _formRules.nonDirectionalSupNum.push({
        //   validator: this.ValidatorNum,
        //   trigger: "blur",
        // });
        // if (_formRules.minSupplierBiddingQuantity == undefined) {
        //   _formRules.minSupplierBiddingQuantity = [];
        // }
        // _formRules.minSupplierBiddingQuantity.push({
        //   validator: this.ValidatorNum,
        //   trigger: "blur",
        // });
        if (_formRules.remark == undefined) {
          _formRules.remark = []
        }
        _formRules.remark.push({
          max: 512,
          message: this.$t(`备注长度不能超过512个字符`),
          trigger: 'blur'
        })
        this.formRules = _formRules
      } else {
        Promise.reject(res)
      }
    })
    this.getUserInfo()
  },
  watch: {
    headerFixedParams: {
      handler() {
        if (
          this.headerFixedParams.sourcingObj &&
          this.headerFixedParams.sourcingMode &&
          this.headerFixedParams.businessTypeCode
        ) {
          this.createRFXLoading = false
          if (
            this.headerFixedParams.sourcingObj == this.cacheHeaderFixedParams.sourcingObj &&
            this.headerFixedParams.sourcingMode == this.cacheHeaderFixedParams.sourcingMode &&
            this.headerFixedParams.businessTypeCode == this.cacheHeaderFixedParams.businessTypeCode
          ) {
            return
          } else {
            this.cacheHeaderFixedParams.sourcingObj = this.headerFixedParams.sourcingObj
            this.cacheHeaderFixedParams.sourcingMode = this.headerFixedParams.sourcingMode
            this.cacheHeaderFixedParams.businessTypeCode = this.headerFixedParams.businessTypeCode
            this.getHeaderConfigFields().catch(() => {
              this.headerFieldDefines = []
              this.strategyFieldDefines = []
            })
          }
        } else {
          this.createRFXLoading = true
        }
      },
      deep: true
    }
  },
  methods: {
    resetRfxCode() {
      this.$API.rfxList
        .getRfxCode({ sourcingMode: this.headerFixedParams.sourcingMode })
        .then((res) => {
          if (res.code === 200) {
            this.formObject.rfxCode = res.data
          }
        })
    },
    async getPurOrgName() {
      let itemData = {
        data: { companyId: sessionStorage.getItem('dialogRFX_companyId') }
      }
      if (
        !sessionStorage.getItem('dialogRFX_siteCode') ||
        sessionStorage.getItem('dialogRFX_siteCode') === 'undefined'
      ) {
        let res = await this.$API.masterData
          .permissionOrgList({
            orgId: itemData.data.companyId
          })
          .catch(() => {})
        if (res && res.data) {
          let dataSource = res.data.map((item) => ({
            text: item.organizationName,
            value: item.organizationName,
            data: {
              purOrgName: item.organizationName,
              purOrgCode: item.organizationCode,
              purOrgId: item.id
            }
          }))
          return dataSource
        }
      } else {
        let res = await this.$API.masterData
          .getByOrgIdAndBgOrgTypeCode({
            siteId: sessionStorage.getItem('dialogRFX_siteId'),
            orgTypeCode: 'BUORG002ADM'
          })
          .catch(() => {})
        if (res && res.data) {
          let dataSource = res.data.map((item) => ({
            text: item.organizationName,
            value: item.organizationName,
            data: {
              purOrgName: item.organizationName,
              purOrgCode: item.organizationCode,
              purOrgId: item.id
            }
          }))
          return dataSource
        }
      }
    },
    initDialogDefaultValue() {
      if (Array.isArray(this.modalData.recordsList)) {
        let {
          companyId,
          companyCode,
          companyName,
          siteId,
          siteCode,
          siteName,
          applyDepId,
          applyDepName
        } = this.modalData.recordsList[0]
        this.formObject.companyId = companyId
        this.formObject.companyCode = companyCode
        this.formObject.companyName = companyName
        this.formObject.siteId = siteId
        this.formObject.siteCode = siteCode
        this.formObject.siteName = siteName
        this.formObject.deptName = applyDepName
        this.formObject.deptCode = ''
        this.formObject.deptId = applyDepId
        if (this.modalData.recordsList.length > 1) {
          //勾选了多条数据  判断是否工厂一致、部门一致
          const isAllEqual = (array, key) => {
            if (array.length > 0) {
              return !array.some(function (value) {
                return value[key] !== array[0][key]
              })
            } else {
              return true
            }
          }
          this.equalDept = isAllEqual(this.modalData.recordsList, 'applyDepId')
          this.equalSite = isAllEqual(this.modalData.recordsList, 'siteCode')
        }
      }
      this.headerFixedParams.businessTypeCode = this.modalData.businessTypeCode
      this.strategyConfigParams.businessTypeCode = this.modalData.businessTypeCode
      this.headerFixedParams.businessTypeName = this.modalData.businessTypeName
      this.strategyConfigParams.businessTypeName = this.modalData.businessTypeName
      this.formObject.businessTypeCode = this.modalData.businessTypeCode
      this.formObject.businessTypeId = this.modalData.businessTypeId
      this.formObject.businessTypeName = this.modalData.businessTypeName

      this.fixedFormFieldConfig = {
        //标题
        rfxName: {
          type: 'text',
          // valid: [
          //   {
          //     max: 50,
          //     message: i18n.t(`标题长度不能超过50`),
          //     trigger: "blur",
          //   },
          // ],
          handler: (ej2Value) => {
            if (ej2Value && this.headerFixedParams.rfxName !== ej2Value) {
              this.headerFixedParams.rfxName = ej2Value
              Object.assign(this.formObject, { rfxName: ej2Value })
            }
          },
          defaultValue: () => this.headerFixedParams.rfxName
        },
        //询价单号  rfxCode  不显示
        //寻源方式
        sourcingMode: {
          type: 'select',
          handler: (ej2EventObject) => {
            if (
              ej2EventObject.value &&
              this.headerFixedParams.sourcingMode !== ej2EventObject.value
            ) {
              this.headerFixedParams.sourcingObj = '' //切换寻源方式，清除‘询价对象’
              this.headerFieldDefines = []
              this.strategyFieldDefines = []
              this.headerFixedParams.sourcingMode = ej2EventObject.value
              this.strategyConfigParams.sourcingMode = ej2EventObject.value
              this.resetRfxCode()
            }
          },
          defaultValue: () => this.headerFixedParams.sourcingMode,
          dataSource: [
            { text: i18n.t('询报价'), value: 'rfq' },
            // { text: i18n.t("直接定价"), value: "direct_pricing" },
            { text: i18n.t('招投标'), value: 'invite_bids' },
            { text: i18n.t('竞价'), value: 'bidding_price' }
          ]
        },
        //寻源对象
        sourcingObj: {
          type: 'select',
          handler: (ej2EventObject) => {
            if (
              ej2EventObject.value &&
              this.headerFixedParams.sourcingObj !== ej2EventObject.value
            ) {
              this.headerFieldDefines = []
              this.strategyFieldDefines = []
              this.headerFixedParams.sourcingObj = ej2EventObject.value
              Object.assign(this.formObject, ej2EventObject.itemData.data)
            }
          },
          dataSource: [],
          defaultValue: () => this.headerFixedParams.sourcingObj
          // api: this.$API.rfxList
          //   .getConfigList({
          //     page: { current: 1, size: 1000 },
          //     condition: "and",
          //     defaultRules: [
          //       {
          //         field: "businessTypeCode",
          //         type: "string",
          //         operator: "equal",
          //         value: this.headerFixedParams.businessTypeCode,
          //       },
          //     ],
          //   })
          //   .then((res) =>
          //     res.data.records
          //       .filter((item) => item.status === 1)
          //       .map((item) => ({
          //         text: item.sourcingObj,
          //         value: item.sourcingObj,
          //         data: {
          //           sourcingObj: item.sourcingObj,
          //           sourcingObjType: item.sourcingObjType,
          //         },
          //       }))
          //   ),
        },
        //业务类型
        businessTypeName: {
          type: 'text',
          readonly: true,
          defaultValue: () => this.headerFixedParams.businessTypeName
          // type: "select",
          // handler: (ej2EventObject) => {
          //   if (
          //     ej2EventObject.itemData?.data &&
          //     ej2EventObject.itemData?.data.businessTypeCode !==
          //       this.headerFixedParams.businessTypeCode
          //   ) {
          //     this.headerFixedParams.businessTypeCode =
          //       ej2EventObject.itemData.data.businessTypeCode;
          //     this.strategyConfigParams.businessTypeCode =
          //       ej2EventObject.itemData.data.businessTypeCode;
          //     Object.assign(this.formObject, ej2EventObject.itemData.data);
          //   }
          // },
          // defaultValue: () => this.headerFixedParams.businessTypeCode,
          // api: this.$API.masterData
          //   .dictionaryGetList({
          //     dictCode: "businessType",
          //   })
          //   .then((res) => {
          //     return res.data.map((item) => ({
          //       text: item.itemName,
          //       value: item.itemCode,
          //       data: {
          //         businessTypeName: item.itemName,
          //         businessTypeCode: item.itemCode,
          //         businessTypeId: item.id,
          //       },
          //     }));
          //   }),
        }
      }

      this.headerFormFieldConfig = {
        //公司名称  companyCode companyId companyName
        companyName: {
          type: 'text',
          readonly: true,
          defaultValue: () => this.formObject.companyName
          // type: "select",
          // handler: (ej2EventObject) => {
          //   Object.assign(this.formObject, ej2EventObject.itemData.data);
          // },
          // api: this.$API.masterData
          //   .findSpecifiedChildrenLevelOrgs()
          //   .then((res) => {
          //     return res?.data.map((item) => ({
          //       text: item.orgName,
          //       value: item.orgName,
          //       data: {
          //         companyCode: item.orgCode,
          //         companyName: item.orgName,
          //         companyId: item.id,
          //       },
          //     }));
          //   }),
        },
        //工厂 siteName siteId  siteCode
        siteName: {
          type: this.formObject.siteCode ? 'text' : 'select',
          readonly: this.formObject.siteCode ? true : false, //如果存在siteCode 就不执行选择
          defaultValue: () =>
            this.formObject.siteCode ? this.formObject.siteName : this.formObject.siteCode,
          handler: (ej2EventObject) => {
            if (ej2EventObject?.itemData?.data) {
              Object.assign(this.formObject, ej2EventObject?.itemData?.data)
            }
          },
          dataSource: []
        },
        //采购组织  purOrgName  purOrgCode  purOrgId
        purOrgName: {
          type: 'select',
          dataSource: [],
          handler: (ej2EventObject) => {
            if (ej2EventObject.itemData == null) {
              Object.assign(this.formObject, {
                purOrgCode: null,
                purOrgId: null,
                purOrgName: null
              })
            } else {
              Object.assign(this.formObject, ej2EventObject.itemData.data)
            }
          },
          api: this.getPurOrgName()
        },
        //采购员  purExecutorName  purExecutorId
        purExecutorName: {
          type: 'debounce-select',
          employeeList: this.modalData.employeeList,
          defaultValue: () => this.$store.state.userInfo.uid,
          handler: (ej2EventObject) => {
            if (ej2EventObject?.itemData) {
              Object.assign(this.formObject, {
                purExecutorName: ej2EventObject.itemData?.employeeName,
                purExecutorId: ej2EventObject.itemData?.uid
              })
            }
          }
        },
        //备注
        remark: {
          col: 1,
          type: 'text'
        },
        //需求部门  deptName  deptCode deptId
        deptName: {
          type: 'text',
          readonly: true,
          defaultValue: () => this.formObject.deptName
          // type: "select",
          // handler: (ej2EventObject) => {
          //   Object.assign(this.formObject, ej2EventObject.itemData.data);
          // },
          // api: this.$API.masterData
          //   .getDepartmentList({
          //     departmentName: "",
          //   })
          //   .then((res) => {
          //     return res.data.map((item) => ({
          //       text: item.departmentName,
          //       value: item.departmentName,
          //       data: {
          //         deptCode: item.departmentCode,
          //         deptName: item.departmentName,
          //       },
          //     }));
          //   }),
        },
        //询价类型
        sourcingType: {
          type: 'select',
          dataSource: [
            { text: i18n.t('新品'), value: 'new_products' },
            { text: i18n.t('二次'), value: 'second_inquiry' },
            { text: i18n.t('已有'), value: 'exist' }
          ],
          defaultValue: () => this.strategyConfigParams.sourcingType
        },
        //价格分类
        priceClassification: {
          type: 'select',
          dataSource: [
            { text: this.$t('暂估价格'), value: 'predict_price' },
            { text: this.$t('SRM价格'), value: 'srm_price' },
            { text: this.$t('执行价格'), value: 'execute_price' },
            { text: this.$t('基价'), value: 'basic_price' }
          ],
          defaultValue: () => this.strategyConfigParams.priceClassification
        },
        //基础/派生
        basicDerivation: {
          type: 'select',
          dataSource: [
            { text: i18n.t('基础'), value: 0 },
            { text: i18n.t('派生'), value: 1 }
          ]
        },
        //询价对象
        // sourcingObjType: {
        //   type: "select",
        //   dataSource: this.$constants.sourcingObjTypeList,
        //   defaultValue: () => this.strategyConfigParams.sourcingObjType,
        // },
        //策略地图配置名称 strategyConfigId  strategyConfigName
        strategyConfigName: {
          type: 'select',
          dataSource: [],
          handler: (ej2EventObject) => {
            if (ej2EventObject.itemData == null) {
              return
            }
            this.strategyInfo = {
              strategyConfigName: ej2EventObject.itemData.strategyName,
              strategyConfigId: ej2EventObject.itemData.id
            }
            // 更新默认值
            ej2EventObject.itemData.details.forEach((detail) => {
              const { defaultValue, strategyCode } = detail
              this.strategyConfigParams[strategyCode] = defaultValue
            })
            // 处理关联
            let strategyFieldDefines = ej2EventObject.itemData.data
            let tenderExtendTimeRecord = strategyFieldDefines.find(
              (e) => e.fieldCode === 'tenderExtendTime'
            )
            if (tenderExtendTimeRecord) {
              tenderExtendTimeRecord.fieldName = i18n.t('投标时间扩展（单位：分钟）')
            }
            const findField = (fieldCode) =>
              strategyFieldDefines.find((e) => e.fieldCode === fieldCode)
            // 定向数量
            const nonDirectionalSupNum = findField('nonDirectionalSupNum')
            const directionalBargaining = findField('directionalBargaining')
            if (nonDirectionalSupNum && directionalBargaining && !nonDirectionalSupNum.readonly) {
              const readonly = !!(Number(this.strategyConfigParams.directionalBargaining) === 1)
              nonDirectionalSupNum.readonly = readonly
            }
            const tecFields = ['tecOpenBidTime', 'tecScoreEndTime', 'tecBidStartTime']
            const needTecBid = findField('needTecBid')
            if (needTecBid) {
              for (const tecField of tecFields) {
                const row = findField(tecField)
                if (row && !row.readonly) {
                  const readonly = !(Number(this.strategyConfigParams.needTecBid) === 1)
                  row.readonly = readonly
                }
              }
            }
            // 非采竞价-隐藏技术投标及商务投标字段
            if (!this.isGeneral && this.headerFixedParams.sourcingMode === 'bidding_price') {
              let timeFields = [
                'tecOpenBidTime',
                'tecScoreEndTime',
                'tecBidStartTime',
                'tenderStartTime',
                'openBidTime'
              ]
              strategyFieldDefines = strategyFieldDefines.filter(
                (item) => !timeFields.includes(item.fieldCode)
              )
            }
            this.strategyFieldDefines = cloneDeep(strategyFieldDefines)
            this.strategyFieldDefinesCopy = cloneDeep(strategyFieldDefines)
          },
          defaultValue: () => this.headerHeaderParams.strategyConfigId
        },
        // 币种 currencyName currencyName
        currencyName: {
          type: 'select',
          handler: (ej2EventObject) => {
            Object.assign(this.formObject, ej2EventObject.itemData.data)
          },
          defaultValue: () => this.headerHeaderParams.currencyCode,
          api: this.$API.masterData.queryAllCurrency().then((res) => {
            return res.data.map((item) => ({
              text: item.currencyName,
              value: item.currencyCode,
              data: {
                currencyName: item.currencyName,
                currencyCode: item.currencyCode
              }
            }))
          })
        },
        // 扩展
        sourcingExpand: {
          type: 'multiSelect',
          handler: (ej2EventObject, formItem) => {
            if (!ej2EventObject) {
              return
            } else {
              if (formItem.fieldCode == 'sourcingExpand') {
                let expandSaveRequestList = []
                ej2EventObject.value.forEach((v) => {
                  formItem.form.dataSource.forEach((x) => {
                    if (v == x.value) {
                      expandSaveRequestList.push(x.data)
                    }
                  })
                })
                Object.assign(this.formObject, {
                  expandSaveRequestList: expandSaveRequestList
                })
              }
            }
          },
          defaultValue: () => this.headerHeaderParams.sourcingExpand,
          api: this.$API.masterData
            .findBusinessOrgInfoByOrg({
              companyId: this.formObject.companyId
            })
            .then((res) => {
              let dataSource = []
              res.data.forEach((v) => {
                v.siteOrgs?.forEach((x) => {
                  dataSource.push({
                    text: v.businessOrganizationName + '+' + x.orgName,
                    value: v.businessOrganizationCode + '+' + x.orgCode,
                    data: {
                      purOrgCode: v.businessOrganizationCode,
                      purOrgId: v.id,
                      purOrgName: v.businessOrganizationName,
                      siteCode: x.orgCode,
                      siteId: x.id,
                      siteName: x.orgName
                    }
                  })
                })
              })
              return dataSource
            })
        }
      }
      this.strategyFormFieldConfig = {
        //寻源方向
        sourcingDirection: {
          type: 'select',
          // handler: (ej2EventObject) => {
          //   Object.assign(this.formObject, {
          //     sourcingDirection: ej2EventObject.value,
          //   });
          // },
          dataSource: [
            { text: i18n.t('递增'), value: 'forward' },
            { text: i18n.t('递减'), value: 'reverse' },
            { text: i18n.t('无限制'), value: 'unlimited' }
          ],
          defaultValue: () => this.strategyConfigParams.sourcingDirection
        },
        //招标方式
        biddingMode: {
          type: 'select',
          dataSource: [
            { text: i18n.t('公开'), value: 'open' },
            { text: i18n.t('邀请'), value: 'target' }
          ],
          defaultValue: () => this.strategyConfigParams.biddingMode
        },
        //最少供应商报价数量
        minSupplierBiddingQuantity: {
          type: 'number',
          defaultValue: () => this.strategyConfigParams.minSupplierBiddingQuantity
        },
        //是否定向议价
        directionalBargaining: {
          type: 'select',
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { text: i18n.t('是'), value: '1' },
            { text: i18n.t('否'), value: '0' }
          ],
          defaultValue: () => this.strategyConfigParams.directionalBargaining,
          handler: (event, _, formVm) => {
            const nonDirectionalSupNum = this.strategyFieldDefines.find(
              (e) => e.fieldCode === 'nonDirectionalSupNum'
            )
            if (nonDirectionalSupNum) {
              const oldReadonly = !nonDirectionalSupNum.detail.editEnable
              let readonly = !!(Number(event.value) === 1)
              if (oldReadonly) {
                readonly = oldReadonly
              }
              if (readonly) {
                formVm.form = {
                  ...formVm.form,
                  nonDirectionalSupNum: 0
                }
              }
              nonDirectionalSupNum.readonly = readonly
              nonDirectionalSupNum.form.readonly = readonly
            }
          }
        },
        //非定向议价供应商数量
        nonDirectionalSupNum: {
          type: 'number',
          defaultValue: () => this.strategyConfigParams.nonDirectionalSupNum
        },
        //供应商选择范围
        supplierSelectionRange: {
          type: 'select',
          api: this.$API.masterData
            .dictionaryGetList({
              dictCode: 'SupplierRange'
            })
            .then((res) => {
              return res.data.map((item) => ({
                text: item.itemName,
                value: item.itemCode
              }))
            }),
          // dataSource: [
          //   { text: i18n.t("品类合格"), value: "category_qualified" },
          //   { text: i18n.t("品类合格+有价格记录"), value: "price_record" },
          //   { text: i18n.t("品类合格+无价格记录"), value: "non_price_Record" },
          //   { text: i18n.t("所有供应商"), value: "all_supplier" },
          // ],
          defaultValue: () => this.strategyConfigParams.supplierSelectionRange
        },
        //是否密封报价
        sealedPrice: {
          type: 'select',
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { text: i18n.t('是'), value: '1' },
            { text: i18n.t('否'), value: '0' }
          ],
          defaultValue: () => this.strategyConfigParams.sealedPrice
        },
        //报价截止时间
        quotationEndTime: {
          type: 'datetime',
          min: new Date(),
          defaultValue: () => this.strategyConfigParams.quotationEndTime
        },
        //应标截止时间
        responseBidEndTime: {
          type: 'datetime',
          min: new Date(),
          defaultValue: () => this.strategyConfigParams.responseBidEndTime
        },
        //投标开始时间
        tenderStartTime: {
          type: 'datetime',
          min: new Date(),
          defaultValue: () => this.strategyConfigParams.tenderStartTime
        },
        //开标时间
        openBidTime: {
          type: 'datetime',
          min: new Date(),
          defaultValue: () => this.strategyConfigParams.openBidTime
        },
        //公开最优价
        publicOptimalPrice: {
          type: 'select',
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { text: i18n.t('是'), value: '1' },
            { text: i18n.t('否'), value: '0' }
          ],
          defaultValue: () => this.strategyConfigParams.publicOptimalPrice
        },
        //公开排名
        publicRanking: {
          type: 'select',
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { text: i18n.t('是'), value: '1' },
            { text: i18n.t('否'), value: '0' }
          ],
          defaultValue: () => this.strategyConfigParams.publicRanking
        },
        //公开报价
        publicQuotation: {
          type: 'select',
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { text: i18n.t('是'), value: '1' },
            { text: i18n.t('否'), value: '0' }
          ],
          defaultValue: () => this.strategyConfigParams.publicQuotation
        },
        //公开身份
        publicIdentity: {
          type: 'select',
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { text: i18n.t('是'), value: '1' },
            { text: i18n.t('否'), value: '0' }
          ],
          defaultValue: () => this.strategyConfigParams.publicIdentity
        },
        //公开参与者数量
        publicParticipantNum: {
          type: 'select',
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { text: this.$t('是'), value: '1' },
            { text: this.$t('否'), value: '0' }
          ],
          defaultValue: () => this.strategyConfigParams.publicParticipantNum
        },
        //供应商报价规则
        supplierQuotationRule: {
          type: 'select',
          dataSource: [
            { text: i18n.t('不限（默认）'), value: '0' },
            { text: i18n.t('报价胜过整体最优'), value: '1' },
            { text: i18n.t('报价胜过竞价人最优报价'), value: '2' }
          ],
          defaultValue: () => this.strategyConfigParams.supplierQuotationRule
        },
        //投标时间扩展
        tenderExtendTime: {
          type: 'number'
        },
        //扩展次数
        expansionNum: {
          type: 'number'
        },
        //剩余时间触发扩展
        triggerExtendRemainingTime: {
          type: 'number'
        },
        //配额是否必输
        allocationRequired: {
          type: 'select',
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { text: i18n.t('是'), value: '1' },
            { text: i18n.t('否'), value: '0' }
          ],
          defaultValue: () => this.strategyConfigParams.allocationRequired
        },
        //中标数量是否必输
        bidNumRequired: {
          type: 'select',
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { text: i18n.t('是'), value: '1' },
            { text: i18n.t('否'), value: '0' }
          ],
          defaultValue: () => this.strategyConfigParams.bidNumRequired
        },
        //配额分配范围
        allocationRange: {
          type: 'select',
          dataSource: [
            { text: i18n.t('当前寻源单'), value: '0' },
            { text: i18n.t('所有有效配额'), value: '1' }
          ],
          defaultValue: () => this.strategyConfigParams.allocationRange
        },
        //配额分配比例
        allocationRatio: {
          type: 'number'
        },
        //招标单是否晋级
        biddingPromotion: {
          type: 'select',
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { text: i18n.t('是'), value: '1' },
            { text: i18n.t('否'), value: '0' }
          ],
          defaultValue: () => this.strategyConfigParams.biddingPromotion
        },
        //轮次总计
        roundCount: {
          type: 'select',
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { text: '1', value: '1' },
            { text: '2', value: '2' },
            { text: '3', value: '3' }
          ],
          defaultValue: () => this.strategyConfigParams.roundCount
        },
        // 核算方式
        accountingMode: {
          type: 'select',
          dataSource: [
            { text: i18n.t('整单中标'), value: 'single_item' },
            { text: i18n.t('部分中标'), value: 'multi_item' }
          ],
          defaultValue: () => this.strategyConfigParams.accountingMode
        },
        // 评标方法
        bidEvaluationMode: {
          type: 'select',
          dataSource: [
            { text: i18n.t('最低价评分法'), value: 'mini_price' },
            { text: i18n.t('综合评分法'), value: 'no_controller' }
          ],
          defaultValue: () => this.strategyConfigParams.bidEvaluationMode
        },
        // 技术投标开始时间
        tecBidStartTime: {
          type: 'datetime',
          format: 'yyyy-MM-dd HH:mm:ss',
          'time-stamp': true,
          'show-clear-button': false,
          min: new Date(),
          defaultValue: () => this.strategyConfigParams.tecBidStartTime
        },
        // 技术开标时间
        tecOpenBidTime: {
          type: 'datetime',
          format: 'yyyy-MM-dd HH:mm:ss',
          'time-stamp': true,
          'show-clear-button': false,
          min: new Date(),
          defaultValue: () => this.strategyConfigParams.tecOpenBidTime
        },
        // 技术评分截止时间
        tecScoreEndTime: {
          type: 'datetime',
          format: 'yyyy-MM-dd HH:mm:ss',
          'time-stamp': true,
          'show-clear-button': false,
          min: new Date(),
          defaultValue: () => this.strategyConfigParams.tecScoreEndTime
        },
        // 是否需要技术投标
        needTecBid: {
          type: 'select',
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { text: this.$t('是'), value: '1' },
            { text: this.$t('否'), value: '0' }
          ],
          defaultValue: () => this.strategyConfigParams.needTecBid,
          handler: (arg, _, formVm) => {
            const { value } = arg
            const readonly = !(Number(value) === 1) // 非技术投标
            const fields = [
              'tecOpenBidTime',
              'tecScoreEndTime',
              'tecBidStartTime',
              'biddingPromotion'
            ]
            // 重置needTecBid
            this.strategyConfigParams.needTecBid = value
            if (readonly) {
              for (const field of fields) {
                this.strategyFieldDefines.forEach((item, index) => {
                  if (item.fieldCode == field) {
                    this.strategyFieldDefines.splice(index, 1)
                  }
                })
              }
            } else {
              this.strategyFieldDefines = cloneDeep(this.strategyFieldDefinesCopy)
              for (const field of fields) {
                const row = this.strategyFieldDefines.find((item) => item.fieldCode === field)
                const oldReadonly = !row?.detail.editEnable
                if (row && !oldReadonly) {
                  // 如果原始配置不让编辑, 则不处理
                  row.readonly = readonly
                  if (row.form) {
                    row.form.readonly = readonly
                  }
                }
                // 如果【是否需要技术投标】 = 【否】
                // 则 【招标单是否晋级】 = 【否】 且只读
                if (row && readonly && field === 'biddingPromotion') {
                  formVm.form = {
                    ...formVm.form,
                    biddingPromotion: '0'
                  }
                }
              }
            }
            this.strategyConfigParams.bidEvaluationMode = readonly ? 'mini_price' : 'no_controller'
          }
        },
        // 定点模式 1：内部定点，2：推荐定点
        pointMode: {
          type: 'select',
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { text: this.$t('内部定点'), value: '1' },
            { text: this.$t('推荐定点'), value: '2' }
          ],
          defaultValue: () => this.strategyConfigParams.pointMode
        }
      }
    },
    getHeaderConfigFields() {
      return this.$API.rfxList.getUserConfigFields(this.headerFixedParams).then((res) => {
        if (res?.data?.fieldDefines) {
          if (Array.isArray(res.data.fieldDefines) && res.data.fieldDefines.length) {
            this.headerFieldDefines = res.data.fieldDefines
            this.headerFieldDefines.sort((a, b) => {
              return b.sortValue - a.sortValue
            })
            let _find = this.headerFieldDefines.filter((e) => {
              e.fieldCode === 'siteCode' || e.fieldCode === 'deptCode'
            })
            if (_find.length) {
              //当前询价对象，配置了工厂、部门等字段
              if (this.equalDept && this.equalSite) {
                return this.getStrategyConfigFields()
              } else {
                this.$toast({
                  type: 'warning',
                  content: i18n.t(
                    '您选择的询价对象，配置了工厂、部门等字段，但是勾选的数据行，字段数据值不一致'
                  )
                })
                return Promise.reject(res)
              }
            } else {
              return this.getStrategyConfigFields()
            }
          } else {
            this.$toast({
              type: 'warning',
              content: i18n.t('头部字段，为空数组')
            })
            return Promise.reject(res)
          }
        } else {
          return Promise.reject(res)
        }
      })
    },
    getStrategyConfigFields() {
      return this.$API.rfxList.getStrategyConfigFields(this.strategyConfigParams).then((res) => {
        if (res.data?.length) {
          this.headerFormFieldConfig.strategyConfigName.dataSource = res.data.map((item) => {
            if (item.details?.length) {
              item.details.sort((a, b) => b.sortValue - a.sortValue)
              item.text = item.strategyName
              item.value = item.id
              item.data = item.details
                .filter((detail) => detail.enableStatus)
                .map((detail) => ({
                  detail,
                  fieldCode: detail.strategyCode,
                  fieldName: detail.strategyName,
                  readonly: !detail.editEnable
                }))
                .sort((a, b) => {
                  // 是否技术投标排序
                  if (a.fieldCode === 'needTecBid') {
                    return -1
                  }
                  if (b.fieldCode === 'needTecBid') {
                    return 1
                  }
                  return 0
                }) // 改为过滤enableStatus=1的数据。后端反馈，可以不用过滤，返回的数据都是enableStatus=1。
              return item
            }
          })
          this.updateIndex++
        }
      })
    },
    //获取当前用户信息
    getUserInfo() {
      this.$API.iamService.getUserDetail().then((res) => {
        this.formObject.deptId = res.data.department.id
        this.formObject.deptCode = res.data.department.orgCode
        this.formObject.deptName = res.data.department.orgName
      })
    },
    hasForm(fieldCode) {
      return !!this.strategyFieldDefines.find((e) => e.fieldCode === fieldCode)
    },
    validateRfxHeader(headerFormData) {
      const rules = [
        {
          message: this.$t('是否需要技术投标不能为空'),
          handler: (row) =>
            !this.isGeneral &&
            this.hasForm('needTecBid') &&
            !row.needTecBid &&
            row.needTecBid !== '0'
        },
        {
          message: this.$t('商务投标开始时间不能为空'),
          handler: (row) => this.hasForm('tenderStartTime') && !row.tenderStartTime
        },
        {
          message: this.$t('商务开标时间不能为空'),
          handler: (row) => this.hasForm('openBidTime') && !row.openBidTime
        },
        {
          message: this.$t('技术投标开始时间不能为空'),
          handler: (row) =>
            this.hasForm('needTecBid') &&
            this.hasForm('tecBidStartTime') &&
            !!Number(row.needTecBid) &&
            !row.tecBidStartTime
        },
        {
          message: this.$t('技术开标时间不能为空'),
          handler: (row) =>
            this.hasForm('needTecBid') &&
            this.hasForm('tecOpenBidTime') &&
            !!Number(row.needTecBid) &&
            !row.tecOpenBidTime
        },
        {
          message: this.$t('非定向议价供应商数量应大于0'),
          handler: (row) => {
            return (
              this.hasForm('directionalBargaining') &&
              this.hasForm('nonDirectionalSupNum') &&
              Number(row?.directionalBargaining) === 0 &&
              (!row?.nonDirectionalSupNum || row?.nonDirectionalSupNum <= 0)
            )
          }
        }
      ]
      for (let i = 0; i < rules.length; i++) {
        const rule = rules[i]
        if (rule.handler(headerFormData.strategyRequest)) {
          this.$toast({ type: 'warning', content: rule.message })
          return false
        }
      }
      return true
    },
    createRFX() {
      this.createRFXLoading = true
      Promise.all([
        // this.$refs.headerForm.parentGetFormData().then(() => {
        //   return Object.assign(this.formObject, this.strategyInfo);
        // }),
        this.$refs.headerForm.parentGetFormData().then((formData) => {
          // let purExecutorName = this.formObject.purExecutorName;
          let returnObject = Object.assign(this.formObject, formData, this.strategyInfo)
          // returnObject.purExecutorName = purExecutorName;
          return returnObject
        }),
        this.$refs.fixedForm.parentGetFormData().then((data) => {
          return {
            businessTypeName: data.businessTypeName,
            rfxName: data.rfxName,
            sourcingMode: data.sourcingMode,
            sourcingObj: data.sourcingObj
          }
        }),
        this.$refs.strategyForm.parentGetFormData().then((data) => {
          return {
            ...data,
            needTecBid: this.strategyConfigParams.needTecBid
          }
        })
      ]).then(([headerFormData, fixedFormData, strategyFormData]) => {
        let _saveParams = { ...headerFormData, ...fixedFormData }
        // 后端反馈没有将询价策略中配置的报价方向控制的默认值传给后台，故在提交时插入此默认值 -- lbj
        const tempItem = this.strategyFieldDefines.find(
          (item) => item.fieldCode === 'priceDirectionControl'
        )
        if (tempItem) {
          strategyFormData.priceDirectionControl = tempItem.detail?.defaultValue
        }
        _saveParams.strategyRequest = strategyFormData
        if (!this.validateRfxHeader(_saveParams)) {
          return Promise.reject()
        }
        Object.keys(_saveParams.strategyRequest).forEach((e) => {
          if (isDate(_saveParams.strategyRequest[e])) {
            _saveParams.strategyRequest[e] = +_saveParams.strategyRequest[e]
          }
        })
        let _saveList = []
        let _idList = []
        let _recordsList = cloneDeep(this.modalData.recordsList)
        _recordsList.forEach((e) => {
          let _extObj = {},
            _saveObj = {},
            _saveLogisticsObj = {}
          for (let i in saveObjectMaps) {
            _saveObj[i] = e[saveObjectMaps[i]]
          }
          for (let i in saveObjectExtMaps) {
            _extObj[i] = e[saveObjectExtMaps[i]]
          }
          for (let i in saveObjectLogisticsMaps) {
            _saveLogisticsObj[i] = e[saveObjectLogisticsMaps[i]]
          }
          _saveObj['extSaveRequest'] = _extObj
          _saveObj['logisticsSaveRequest'] = _saveLogisticsObj
          if (_saveParams.businessTypeCode === 'BTTCL006') {
            _idList.push(e.headerId)
          }
          _saveList.push(_saveObj)
        })
        _saveParams['rfxItemSaveList'] = _saveList
        // 下面的判断是这样的场景导致的，先将是否需要技术标选择是，是否晋级会被带出（默认为是），再将是否需要技术标选择为否时是否晋级字段隐藏，但是值还是1
        if (
          _saveParams.strategyRequest?.needTecBid === '0' &&
          _saveParams.strategyRequest?.biddingPromotion === '1'
        ) {
          _saveParams.strategyRequest.biddingPromotion = '0'
        }
        console.log(this.$t('保存单据数据'), _saveParams)
        if (_saveParams.businessTypeCode === 'BTTCL006') {
          _saveParams.idList = _idList
          _saveParams.purExecutorId = _saveParams?.purExecutorName || ''
          return this.$API.rfxList.createLogisticsRfxByPr(_saveParams).then((res) => {
            this.createRFXLoading = false
            if (res.code === 200) {
              this.$toast({ type: 'success', content: i18n.t('保存成功') })
              this.$emit('confirm-function', {
                sourcingMode: this.headerFixedParams.sourcingMode,
                data: res.data
              })
            } else {
              this.$toast({ type: 'error', content: i18n.t('保存失败') })
            }
          })
        } else {
          return this.$API.rfxList.createRfxByPr(_saveParams).then((res) => {
            this.createRFXLoading = false
            if (res.code === 200) {
              this.$toast({ type: 'success', content: i18n.t('保存成功') })
              this.$emit('confirm-function', {
                sourcingMode: this.headerFixedParams.sourcingMode,
                data: res.data
              })
            } else {
              this.$toast({ type: 'error', content: i18n.t('保存失败') })
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
