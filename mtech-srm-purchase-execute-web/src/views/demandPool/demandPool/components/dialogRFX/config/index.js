import { i18n } from '@/main.js'
export const defaultHeaderFields = [
  {
    recordId: null,
    fieldId: '179393898183004200',
    fieldKey: '56ebab79-da5c-11eb-96d2-0242ac150162',
    fieldCode: 'rfxName',
    fieldGroup: i18n.t('询价信息'),
    fieldGroupCode: '',
    fieldName: i18n.t('项目名称'),
    fieldType: 0,
    fixed: 0,
    required: 0,
    fieldData: null,
    tableField: 1,
    customer: 0,
    tableName: 'rfx_header',
    sortValue: 100,
    pageShow: 1,
    structureKey: '',
    defaultFieldName: i18n.t('项目名称')
  },
  {
    recordId: null,
    fieldId: '170720062705745936',
    fieldKey: 'e9442289-8be1-11ec-b83b-fa163e4f7edd',
    fieldCode: 'sourcingMode',
    fieldGroup: i18n.t('询价信息'),
    fieldGroupCode: '',
    fieldName: i18n.t('寻源方式'),
    fieldType: 0,
    fixed: 1,
    required: 0,
    fieldData: null,
    tableField: 1,
    customer: 0,
    tableName: 'rfx_header',
    sortValue: 97,
    pageShow: 1,
    structureKey: '',
    defaultFieldName: i18n.t('寻源方式')
  },
  {
    recordId: null,
    fieldId: '170720062772854858',
    fieldKey: 'd2d4e2de-8ba2-11ec-b83b-fa163e4f7edd',
    fieldCode: 'businessTypeName',
    fieldGroup: i18n.t('询价信息'),
    fieldGroupCode: '',
    fieldName: i18n.t('业务类型'),
    fieldType: 0,
    fixed: 1,
    required: 0,
    fieldData: null,
    tableField: 1,
    customer: 0,
    tableName: 'rfx_header',
    sortValue: 98,
    pageShow: 1,
    structureKey: '',
    defaultFieldName: i18n.t('业务类型')
  },
  {
    recordId: null,
    fieldId: '170720062735106060',
    fieldKey: 'd9b6e285-8ba2-11ec-b83b-fa163e4f7edd',
    fieldCode: 'sourcingObj',
    fieldGroup: i18n.t('询价信息'),
    fieldGroupCode: '',
    fieldName: i18n.t('寻源对象'),
    fieldType: 0,
    fixed: 1,
    required: 0,
    fieldData: null,
    tableField: 1,
    customer: 0,
    tableName: 'rfx_header',
    sortValue: 96,
    pageShow: 1,
    structureKey: '',
    defaultFieldName: i18n.t('寻源对象')
  }
]

export const saveObjectMaps = {
  requireDeptId: 'applyDepId',
  requireDeptName: 'applyDepName',
  requireUserId: 'applyUserId',
  requireUserName: 'applyUserName',
  purGroupCode: 'buyerOrgCode',
  purGroupId: 'buyerOrgId',
  purGroupName: 'buyerOrgName',
  categoryCode: 'categoryCode',
  categoryId: 'categoryId',
  categoryName: 'categoryName',
  companyCode: 'companyCode',
  companyId: 'companyId',
  companyName: 'companyName',
  currencyCode: 'currencyCode',
  currencyName: 'currencyName',
  porId: 'headerId',
  porItemId: 'id',
  itemCode: 'itemCode',
  itemId: 'itemId',
  itemName: 'itemName',
  porLineNo: 'itemNo',
  purUnitId: 'orderUnitId',
  purUnitCode: 'orderUnitCode',
  purUnitName: 'orderUnitName',
  remark: 'remark',
  porName: 'title',
  porCode: 'requestCode',
  applyType: 'requestType',
  siteCode: 'siteCode',
  siteId: 'siteId',
  siteName: 'siteName',
  skuCode: 'skuCode',
  skuId: 'skuId',
  skuName: 'skuName',
  spec: 'spec',
  tconCode: 'tconCode',
  unitId: 'unitId',
  unitCode: 'unitCode',
  unitName: 'unitName',
  requireName: 'requireName',
  requireDesc: 'requireDesc',
  taxRateValue: 'taxid',
  taxRateCode: 'taxRateCode',
  taxRateName: 'taxRateName'
}

export const saveObjectExtMaps = {
  budgetCode: 'budgetCode',
  budgetDept: 'budgetDept',
  forecastQuantity: 'budgetQuantity',
  budgetSubject: 'budgetSubjectCode',
  budgetTotalPriceUntaxed: 'budgetTotalPrice',
  budgetUnitPriceUntaxed: 'budgetUnitPrice',
  contractRecType: 'contractRecType',
  costCenter: 'costSharing',
  packageRequire: 'packageDesc',
  packageMode: 'packageMethod',
  postingSubject: 'postingAccountName',
  requireQuantity: 'quantity',
  receiveAddress: 'receiveAddress',
  requireDate: 'requiredDeliveryDate',
  screenCode: 'screenCode',
  logisticsMode: 'shippingMethodName',
  subjectTotal: 'subjectTotal',
  subjectType: 'subjectType',
  budgetTotalPriceTaxed: 'taxedTotalPrice',
  budgetUnitPriceTaxed: 'taxedUnitPrice',
  tradeTerms: 'tradeClauseName'
}

export const saveObjectLogisticsMaps = {
  shippingPartition: 'shippingPartition', //物流分区
  goodsVolume: 'goodsVolume', // 货品体积（CBM）
  startPort: 'startPlaceName', //起始地
  endPort: 'endPlaceName', //目的地
  terms: 'terms', //条款
  containerTypeName: 'containerTypeName', //柜型
  goodGoodsTime: 'goodGoodsTime', //货好时间
  freeCabinetDemurrage: 'freeCabinetDemurrage', //免柜Demurrage
  freeCabinetDetention: 'freeCabinetDetention', //免柜Detention
  freeHeapDetention: 'freeHeapDetention', //免堆Detention
  serviceModel: 'serviceModel', //服务模式
  declareCustomsMethod: 'declareCustomsMethod', //报关方式
  startAddrName: 'startAddrName', //起运地址
  endAddrName: 'endAddrName', //目的地址
  demandStartTime: 'demandStartTime', //需求起运时间
  demandDeliveryTime: 'demandArriverTime', //需求送达时间
  goodsSize: 'goodsSize', //货品尺寸
  goodsGrossWeight: 'goodsGrossWeight', //货品毛重（KG）
  goodsVolHeavy: 'goodsVolHeavy', //货品体积重（KG）
  pieceQty: 'pieceQty', //件数（包装）
  shippingMethodName: 'shippingMethodName', //物流方式
  productName: 'productName', //品名
  // requirementNumber: 'requireName', //需求编码
  requirementType: 'demandType', //需求类型
  requirementName: 'requireName', //需求名称
  department: 'extDepName', //事业部
  associateExtLineNo: 'associateExtLineNo', // 关联外部行号行号
  associateExtDocNo: 'associateExtDocNo' //关联单据号
}
