<template>
  <mt-dialog
    ref="dialog-stage"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="modalData.title"
  >
    <mt-data-grid :data-source="modalData.stageList" :column-data="cols"></mt-data-grid
  ></mt-dialog>
</template>

<script>
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        // {
        //   click: this.cancel,
        //   buttonModel: { content: this.$t("取消") },
        // },
        {
          click: this.cancel,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      cols: [
        {
          width: '150',
          field: 'untaxedUnitPrice',
          headerText: this.$t('未税单价')
        },
        {
          width: '150',
          field: 'taxedUnitPrice',
          headerText: this.$t('含税单价')
        },
        {
          width: '150',
          field: 'discountRate',
          headerText: this.$t('折扣率')
        }
      ]
    }
  },
  mounted() {
    this.$refs['dialog-stage'].ejsRef.show()
  },
  methods: {
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
