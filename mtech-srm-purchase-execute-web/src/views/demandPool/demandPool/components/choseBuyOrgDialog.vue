// 选择 采购组织
<template>
  <mt-dialog
    ref="choseBuyOrg"
    css-class="chooseBuyOrgDialog"
    :buttons="buttons"
    :header="$t('采购组织')"
    @close="cancel"
  >
    <mt-form ref="ruleForm" :model="formObject" :rules="rules">
      <mt-row :gutter="24">
        <mt-col :span="12">
          <mt-form-item prop="buyerGroupCode" :label="$t('采购组织')">
            <RemoteAutocomplete
              v-model="formObject.buyerGroupCode"
              :url="$API.masterData.getBusinessOrganizationByOrgIdUrl"
              method="get"
              :params="{
                orgId: this.companyId
              }"
              records-position="data"
              :fields="{ text: 'organizationName', value: 'organizationCode' }"
              :placeholder="$t('请选择')"
              @change="handleOrgChange"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item prop="demandType" :label="$t('需求类型')">
            <mt-select
              v-model="formObject.demandType"
              :data-source="demandTypeOptions"
              :show-clear-button="true"
              :placeholder="$t('请选择需求类型')"
            ></mt-select>
          </mt-form-item>
        </mt-col>
      </mt-row>
      <mt-row :gutter="24">
        <mt-col :span="12">
          <mt-form-item prop="fileId" :label="$t('附件')">
            <mt-button class="e-flat" @click="handleUpload">{{ $t('附件上传') }}</mt-button>
            <span
              v-if="fileNum !== 0"
              style="display: inline-block; margin-left: 10px; height: 28px; line-height: 28px"
              >{{ $t('已上传') }}：{{ fileNum }}</span
            >
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
    <uploader-dialog ref="uploaderDialog" @confirm="uploadConfirm"></uploader-dialog>
  </mt-dialog>
</template>

<script>
import UploaderDialog from '@/components/Upload/uploaderDialog'
export default {
  props: {
    companyId: {
      type: String,
      default: ''
    }
  },
  components: {
    UploaderDialog
  },

  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      demandTypeOptions: [
        { text: this.$t('项目'), value: this.$t('项目') },
        { text: this.$t('非项目'), value: this.$t('非项目') }
      ],
      formObject: {},
      rules: {
        buyerGroupCode: [{ required: true, message: this.$t('请选择采购组织'), trigger: 'blur' }],
        demandType: [{ required: true, message: this.$t('请选择需求类型'), trigger: 'blur' }]
      },
      fileList: []
    }
  },
  computed: {
    fileNum() {
      return this.fileList.length
    }
  },
  mounted() {
    this.$refs.choseBuyOrg.ejsRef.show()
  },
  methods: {
    cancel() {
      this.$refs.choseBuyOrg.ejsRef.hide()
      this.$emit('buyOrgDialogShow')
    },
    handleOrgChange(e) {
      this.formObject.buyerGroupId = e?.itemData?.id
      this.formObject.buyerGroupCode = e?.itemData?.organizationCode
      this.formObject.buyerGroupName = e?.itemData?.organizationName
    },
    handleUpload() {
      const dialogParams = {
        title: this.$t('附件'),
        fileData: this.fileList,
        isSingleFile: true
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },
    uploadConfirm(args) {
      const { uploadData } = args
      this.fileList = uploadData
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            ...this.formObject
          }
          if (this.fileList.length !== 0) {
            params.fileId = this.fileList[0].id
            params.fileName = this.fileList[0].fileName
          }
          console.log('confirm', params)

          this.$emit('buyOrgDialogShow', params)
          this.$refs.choseBuyOrg.ejsRef.hide()
        }
      })
    }
  }
}
</script>

<style lang="scss">
.chooseBuyOrgDialog .e-dlg-content {
  padding: 40px !important;

  .dialog-label {
    display: inline-block;
    margin-bottom: 20px;
  }
}
</style>
