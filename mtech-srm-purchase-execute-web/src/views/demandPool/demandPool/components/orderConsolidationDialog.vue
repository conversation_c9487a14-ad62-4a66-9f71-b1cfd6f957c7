<template>
  <mt-dialog
    ref="consolidation-dialog"
    css-class="order-consolidation-dialog"
    :buttons="buttons"
    :header="modalData.title"
  >
    <mt-data-grid
      class="consolidation-dialog"
      :data-source="modalData.consolidationData"
      :column-data="modalData.columnData"
      ref="dataGrid"
      locale="zh"
    ></mt-data-grid>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { isPrimary: 'true', content: this.$t('我了解了') }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  mounted() {
    this.$refs['consolidation-dialog'].ejsRef.show()
  },
  methods: {
    cancel() {
      this.$refs['consolidation-dialog'].ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.consolidation-dialog {
  margin: 40px 22px 0 22px;
}
/deep/.mt-icon-icon_V {
  color: #54bf00;
  font-size: 16px;
}

/deep/.mt-icon-icon_X {
  color: #ed5633;
  font-size: 16px;
}
</style>
