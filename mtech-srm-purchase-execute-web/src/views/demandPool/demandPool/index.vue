<template>
  <div class="full-height">
    <mt-template-page
      v-if="isLoad"
      :template-config="pageConfig"
      :permission-obj="permissionObj"
      @handleSelectTab="handleSelectTab"
    >
      <!-- 转订单 -->
      <mt-template-page
        slot="slot-0"
        ref="template-0"
        class="template-height"
        :template-config="pageConfig0"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
        @handleClickToolTips="handleClickToolTips"
        @showFileBaseInfo="showFileBaseInfo"
      >
        <div slot="slot-filter" class="top-filter">
          <div class="left-status">
            <span class="tit">{{ $t('业务类型') }}：</span>
            <status-check
              ref="statusCheckRef0"
              :status-data="businessTypeList"
              @handleChose="handleChose"
            ></status-check>
          </div>
          <div class="total-number">
            {{ businessTypeName }}{{ $t('共') }}{{ totalNumber }}{{ $t('行信息内容') }}
          </div>
        </div>
      </mt-template-page>

      <!-- 我认领的 - 转订单 -->
      <mt-template-page
        slot="slot-1"
        ref="template-1"
        class="template-height"
        :template-config="pageConfig1"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
        @handleClickToolTips="handleClickToolTips"
      >
        <div slot="slot-filter" class="top-filter">
          <div class="left-status">
            <span class="tit">{{ $t('业务类型') }}：</span>
            <status-check
              ref="statusCheckRef1"
              :status-data="businessTypeList"
              @handleChose="handleChose"
            ></status-check>
          </div>
          <div class="total-number">
            {{ businessTypeName }}{{ $t('共') }}{{ totalNumber }}{{ $t('行信息内容') }}
          </div>
        </div>
      </mt-template-page>

      <!-- 转寻源 -->
      <mt-template-page
        slot="slot-2"
        ref="template-2"
        class="template-height"
        :template-config="pageConfig2"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
        @handleClickToolTips="handleClickToolTips"
        @showFileBaseInfo="showFileBaseInfo"
      >
        <div slot="slot-filter" class="top-filter">
          <div class="left-status">
            <span class="tit">{{ $t('业务类型') }}：</span>
            <status-check
              ref="statusCheckRef2"
              :status-data="businessTypeList"
              @handleChose="handleChose"
            ></status-check>
          </div>
          <div class="total-number">
            {{ businessTypeName }}{{ $t('共') }}{{ totalNumber }}{{ $t('行信息内容') }}
          </div>
        </div>
      </mt-template-page>

      <!-- 我认领的 - 转寻源 -->
      <mt-template-page
        slot="slot-3"
        ref="template-3"
        class="template-height"
        :template-config="pageConfig3"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
        @handleClickToolTips="handleClickToolTips"
      >
        <div slot="slot-filter" class="top-filter">
          <div class="left-status">
            <span class="tit">{{ $t('业务类型') }}：</span>
            <status-check
              ref="statusCheckRef3"
              :status-data="businessTypeList"
              @handleChose="handleChose"
            ></status-check>
          </div>
          <div class="total-number">
            {{ businessTypeName }}{{ $t('共') }}{{ totalNumber }}{{ $t('行信息内容') }}
          </div>
        </div>
      </mt-template-page>

      <!-- 非采台账 -->
      <mt-template-page
        slot="slot-4"
        ref="template-4"
        class="template-height"
        :template-config="pageConfig4"
        @handleClickToolBar="handleClickToolBar"
        @handleCustomReset="handleCustomReset"
      >
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="purchaseMode" :label="$t('采购模式')" label-style="top">
                <mt-select
                  v-model="searchFormModel.purchaseMode"
                  css-class="rule-element"
                  :data-source="[
                    { value: 'selfPurChase', text: $t('自采'), cssClass: '' },
                    { value: 'polymerizationPurchase', text: $t('聚采'), cssClass: '' }
                  ]"
                  :fields="{ text: 'text', value: 'value' }"
                  :show-clear-button="true"
                  :allow-filtering="true"
                  filter-type="Contains"
                  :placeholder="$t('请选择采购模式')"
                />
              </mt-form-item>
              <!-- <mt-form-item prop="companyCode" :label="$t('业务公司编码')" label-style="top">
                <mt-input
                  v-model="searchFormModel.companyCode"
                  :show-clear-button="true"
                  :placeholder="$t('请输入业务公司编码')"
                />
              </mt-form-item> -->
              <mt-form-item prop="companyName" :label="$t('业务公司名称')" label-style="top">
                <mt-input
                  v-model="searchFormModel.companyName"
                  :show-clear-button="true"
                  :placeholder="$t('请输入业务公司名称')"
                />
              </mt-form-item>
              <mt-form-item prop="requestCode" :label="$t('需求申请号')" label-style="top">
                <mt-input
                  v-model="searchFormModel.requestCode"
                  :show-clear-button="true"
                  :placeholder="$t('请输入需求申请号')"
                />
              </mt-form-item>
              <mt-form-item prop="purchaserName" :label="$t('采购员名称')" label-style="top">
                <mt-input
                  v-model="searchFormModel.purchaserName"
                  :show-clear-button="true"
                  :placeholder="$t('请输入采购员名称')"
                />
              </mt-form-item>
              <!-- <mt-form-item prop="demandDeptCode" :label="$t('需求部门编码')" label-style="top">
                <mt-input
                  v-model="searchFormModel.demandDeptCode"
                  :show-clear-button="true"
                  :placeholder="$t('请输入需求部门编码')"
                />
              </mt-form-item> -->
              <mt-form-item prop="demandDeptName" :label="$t('需求部门')" label-style="top">
                <mt-input
                  v-model="searchFormModel.demandDeptName"
                  :show-clear-button="true"
                  :placeholder="$t('请输入需求部门')"
                />
              </mt-form-item>
              <mt-form-item prop="projectName" :label="$t('项目/物料名称')" label-style="top">
                <mt-input
                  v-model="searchFormModel.projectName"
                  :show-clear-button="true"
                  :placeholder="$t('请输入项目/物料名称')"
                />
              </mt-form-item>
              <!-- <mt-form-item prop="pricingTime" :label="$t('招标/定标时间')" label-style="top">
                <mt-input
                  v-model="searchFormModel.pricingTime"
                  :show-clear-button="true"
                  :placeholder="$t('请输入招标/定标时间')"
                />
              </mt-form-item> -->
              <mt-form-item prop="orderNo" :label="$t('订单号')" label-style="top">
                <mt-input
                  v-model="searchFormModel.orderNo"
                  :show-clear-button="true"
                  :placeholder="$t('请输入订单号')"
                />
              </mt-form-item>
              <mt-form-item
                prop="associateContractNo"
                :label="$t('关联合同编号')"
                label-style="top"
              >
                <mt-input
                  v-model="searchFormModel.associateContractNo"
                  :show-clear-button="true"
                  :placeholder="$t('请输入关联合同编号')"
                />
              </mt-form-item>
              <mt-form-item prop="associateSourceNo" :label="$t('关联寻源编号')" label-style="top">
                <mt-input
                  v-model="searchFormModel.associateSourceNo"
                  :show-clear-button="true"
                  :placeholder="$t('请输入关联寻源编号')"
                />
              </mt-form-item>
            </mt-form>
          </div>
        </template>
        <!-- <div slot="slot-filter" class="top-filter">
          <div class="left-status">
            <span class="tit">{{ $t('业务类型') }}：</span>
            <status-check
              ref="statusCheckRef4"
              :status-data="businessTypeList"
              @handleChose="handleChose"
            ></status-check>
          </div>
          <div class="total-number">
            {{ businessTypeName }}{{ $t('共') }}{{ totalNumber }}{{ $t('行信息内容') }}
          </div>
        </div> -->
      </mt-template-page>
    </mt-template-page>

    <!-- 需求附件弹窗 -->
    <uploader-dialog ref="uploaderDialog"></uploader-dialog>

    <!-- 聚采时， 选择 采购组织 -->
    <chose-org-dialog
      ref="choseBuyDialog"
      v-if="showPruchaseDialog"
      :company-id="companyId"
      @buyOrgDialogShow="buyOrgDialogShow"
    ></chose-org-dialog>

    <!-- 驳回填写取消原因的弹窗 -->
    <cancleDialog @confirmSuccess="confirmSuccess" ref="cancleDialog"></cancleDialog>
  </div>
</template>

<script>
import Vue from 'vue'
import { BASE_TENANT } from '@/utils/constant'
import { cloneDeep } from 'lodash'
import { formatMasterFilter } from '@/utils/utils'
import * as finalCols from './config/final.js'
import {
  purchaseColumnData,
  searchSourceColumnData,
  errorColumns
} from './config/orderConsolidation.js'
import UTILS from '@/utils/utils'
export default {
  components: {
    statusCheck: require('@/components/businessComponents/statusCheck.vue').default,
    UploaderDialog: () => import('@/components/Upload/uploaderDialog'),
    cancleDialog: () => import('./components/cancleDialog'),
    choseOrgDialog: require('./components/choseBuyOrgDialog.vue').default
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      isLoad: false,
      searchFormModel: {},
      allBusinessTypeList: [],
      allCodes: [],
      businessTypeName: null,
      totalNumber: null, //数据总条数
      createOrderRowData: [],
      createSourcingRowData: [],
      consolidationDialogShow: false,
      businessTypeList: [],
      addDialogShow: false,
      dialogData: null,
      tabIndex: 0,
      selectedBusinessId: 0,
      selectedBusinessCode: '',
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0006' },
          { dataPermission: 'b', permissionCode: 'T_02_0007' },
          { dataPermission: 'c', permissionCode: 'T_02_0008' },
          { dataPermission: 'd', permissionCode: 'T_02_0009' },
          { dataPermission: 'e', permissionCode: 'T_02_0171' }
        ]
      },
      pageConfig: [
        {
          title: this.$t('转订单'),
          dataPermission: 'a',
          permissionCode: 'T_02_0006'
        },
        {
          title: this.$t('我认领的-转订单'),
          dataPermission: 'b',
          permissionCode: 'T_02_0007'
        },
        {
          title: this.$t('转寻源'),
          dataPermission: 'c',
          permissionCode: 'T_02_0008'
        },
        {
          title: this.$t('我的任务-转寻源'),
          dataPermission: 'd',
          permissionCode: 'T_02_0009'
        },
        {
          title: this.$t('非采台账'),
          dataPermission: 'e',
          permissionCode: 'T_02_0171'
        }
      ],
      pageConfig0: [
        {
          useToolTemplate: false,
          useBaseConfig: true, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: [
            {
              id: 'Claim',
              icon: 'icon_solid_Submit',
              title: this.$t('认领'),
              permission: ['O_02_0350']
            },
            {
              id: 'Distribute',
              icon: 'icon_smooth_Audit',
              title: this.$t('分配'),
              permission: ['O_02_0351']
            }
          ],
          grid: {
            columnData: finalCols.checkCol, // 还要加上自定义字段
            dataSource: [],
            lineIndex: 1
            // frozenColumns: 1,
          }
        }
      ],
      pageConfig1: [
        {
          useToolTemplate: false,
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: {
            tools: [
              [
                {
                  id: 'transfer',
                  icon: 'icon_solid_Submit',
                  title: this.$t('转办'),
                  permission: ['O_02_0355']
                },
                {
                  id: 'cancelClaim',
                  icon: 'icon_solid_export',
                  title: this.$t('取消认领'),
                  permission: ['O_02_0356']
                },
                {
                  id: 'create-order',
                  icon: 'icon_solid_Submit',
                  title: this.$t('创建采购订单'),
                  permission: ['O_02_0357'],
                  tips: {
                    fontSize: '18px'
                  }
                },
                {
                  id: 'create-require',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('创建询价招标单'),
                  permission: ['O_02_0358']
                  // tips: {
                  //   fontSize: "18px",
                  // },
                },
                {
                  id: 'create-require-ju',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('创建聚采寻源需求'),
                  permission: ['O_02_0359']
                  // tips: {
                  //   fontSize: "18px",
                  // },
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: finalCols.checkCol, // 还要加上自定义字段,
            dataSource: [],
            lineIndex: 1
            // frozenColumns: 1,
          }
        }
      ],
      pageConfig2: [
        {
          useToolTemplate: false,
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: {
            tools: [
              [
                {
                  id: 'Claim',
                  icon: 'icon_solid_Submit',
                  title: this.$t('认领'),
                  permission: ['O_02_0360']
                },
                {
                  id: 'Distribute',
                  icon: 'icon_smooth_Audit',
                  title: this.$t('分配'),
                  permission: ['O_02_0361']
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: finalCols.checkCol, // 还要加上自定义字段,
            dataSource: [],
            lineIndex: 1
            // frozenColumns: 1,
          }
        }
      ],
      pageConfig3: [
        {
          useToolTemplate: false,
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: {
            tools: [
              [
                {
                  id: 'transfer',
                  icon: 'icon_solid_Submit',
                  title: this.$t('转办'),
                  permission: ['O_02_0364']
                },
                // {
                //   id: 'cancelClaim',
                //   icon: 'icon_solid_export',
                //   title: this.$t('取消认领'),
                //   permission: ['O_02_0365']
                // },
                {
                  id: 'cancelClaim',
                  icon: 'icon_list_recall',
                  title: this.$t('驳回'),
                  permission: ['O_02_1391']
                },
                {
                  id: 'create-require',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('创建询价招标单'),
                  permission: ['O_02_0366']
                  // tips: {
                  //   fontSize: "18px",
                  // },
                },
                {
                  id: 'create-require-ju',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('创建聚采寻源需求'),
                  permission: ['O_02_0367']
                  // tips: {
                  //   fontSize: "18px",
                  // },
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: finalCols.checkCol,
            lineIndex: 1,
            dataSource: []
            // frozenColumns: 1,
          }
        }
      ],
      pageConfig4: [
        {
          useToolTemplate: false,
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          // isUseCustomEditor: true,
          toolbar: {
            tools: [
              [
                {
                  id: 'Export1',
                  icon: 'icon_solid_export',
                  title: this.$t('导出')
                  // permission: ['O_02_0367']
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: finalCols.checkCol.concat(finalCols.FCColumn),
            lineIndex: 1,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/standingBook/query`
            }
          }
        }
      ],
      // buyerOrgOptions: [], //采购组织
      juIds: [], // 推送聚采时 选择的idlist。因为有弹窗层，所以在此存储
      companyId: null,
      showPruchaseDialog: false, // 推送聚采选择业务组织的弹窗
      businessNumList: [[], [], [], []], // 不同tab上的业务类型待办数量, 与 4 个tab的顺序有关
      employeeList: []
    }
  },
  async created() {
    // 下方的循环是为了解决tab因为配置了权限导致的表格内容不渲染
    this.pageConfig.forEach((i) => {
      const elementPermissionSet = window.elementPermissionSet
      if (!elementPermissionSet.includes(i.permissionCode)) {
        delete i.permissionCode
      }
    })
    await this.getBusinessConfig() // 获取业务类型
    await this.handleChose(this.selectedBusinessId) // 获取第一个tab的行列数据
    this.$nextTick(() => {
      this.isLoad = true
    })
  },
  methods: {
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 显示表格文件弹窗
    showFileBaseInfo(e) {
      const dialogParams = {
        fileData: cloneDeep(e.value),
        isView: true,
        title: this.$t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },

    //处理列模板组件异步请求后数据
    afterAsyncData(res) {
      this.totalNumber = res.data.total
    },
    // 点击按钮旁边的tips
    async handleClickToolTips(e) {
      if (this.createOrderRowData.length <= 0 || this.createSourcingRowData.length <= 0) {
        let orderConsolidation = await this.$API.bgConfig.getOrderConsolidationConfig({ type: 0 })
        console.log('orderConsolidation', orderConsolidation)
        if (orderConsolidation.code === 200) {
          this.getCreateOrderReqPolicy(orderConsolidation) // 转订单的校验
          // this.getCreateSourcingReqPolicy(orderConsolidation);
        }
      }
      this.$dialog({
        modal: () => import('./components/orderConsolidationDialog.vue'),
        data: {
          title: this.$t('并单策略'),
          consolidationData:
            e.toolbar.id == 'create-order' ? this.createOrderRowData : this.createSourcingRowData,
          columnData: e.toolbar.id == 'create-order' ? purchaseColumnData : searchSourceColumnData
        }
      })
    },

    // 获取创建采购订单并单配置策略
    getCreateOrderReqPolicy(res) {
      let tempConsolidationData = res.data[0]
      let _businessTypeId = [],
        _businessTypeName = [],
        _singleRowData = {},
        _columnData = [this.$t('序号'), this.$t('业务类型')]
      this.createOrderRowData.length = 0
      tempConsolidationData.orderConsolidationConfigDTOList.map((item) => {
        if (_businessTypeId.indexOf(item.businessTypeId) < 0) {
          _businessTypeId.push(item.businessTypeId)
          _businessTypeName.push(item.businessTypeName)
        }
        if (_columnData.indexOf(item.ruleName) < 0) {
          _columnData.push(item.ruleName)
        }
      })
      for (let i = 0; i < _businessTypeId.length; i++) {
        _singleRowData.serialNumber = i + 1
        _singleRowData.businessType = _businessTypeName[i]
        for (let j = 2; j < _columnData.length; j++) {
          _singleRowData['rule' + (j - 1)] =
            tempConsolidationData.orderConsolidationConfigDTOList.shift()
        }
        this.createOrderRowData.push(JSON.parse(JSON.stringify(_singleRowData)))
        _singleRowData.length = 0
      }

      _columnData.forEach((item, index) => {
        purchaseColumnData[index].headerText = item
      })
    },

    // 废弃：获取创建寻源并单配置策略
    getCreateSourcingReqPolicy(res) {
      let tempSouringConsolidationData = res.data[1]
      let _businessTypeId = [],
        _businessTypeName = [],
        _singleRowData = {},
        _souringColumnData = [this.$t('序号'), this.$t('业务类型')]
      this.createSourcingRowData.length = 0
      tempSouringConsolidationData.orderConsolidationConfigDTOList.map((item) => {
        if (_businessTypeId.indexOf(item.businessTypeId) < 0) {
          _businessTypeId.push(item.businessTypeId)
          _businessTypeName.push(item.businessTypeName)
        }
        if (_souringColumnData.indexOf(item.ruleName) < 0) {
          _souringColumnData.push(item.ruleName)
        }
      })
      for (let i = 0; i < _businessTypeId.length; i++) {
        _singleRowData.serialNumber = i + 1
        _singleRowData.businessType = _businessTypeName[i]
        for (let j = 2; j < _souringColumnData.length; j++) {
          _singleRowData['rule' + (j - 1)] =
            tempSouringConsolidationData.orderConsolidationConfigDTOList.shift()
        }
        this.createSourcingRowData.push(JSON.parse(JSON.stringify(_singleRowData)))
        _singleRowData.length = 0
      }

      _souringColumnData.forEach((item, index) => {
        searchSourceColumnData[index].headerText = item
      })
    },

    // 匹配价格记录
    handlePrice(row) {
      this.$dialog({
        modal: () => import('./components/priceDialog.vue'),
        data: {
          title: this.$t('匹配价格记录详情'),
          row
        },
        success: () => {
          // this.confirmSuccess();
        }
      })
    },

    handleClickToolBar(e) {
      if (
        e.gridRef.getMtechGridRecords().length <= 0 &&
        !(
          e.toolbar.id == 'Add' ||
          e.toolbar.id == 'Filter' ||
          e.toolbar.id == 'Refresh' ||
          e.toolbar.id == 'Setting' ||
          e.toolbar.id == 'more-option-btn' ||
          e.toolbar.id == 'refreshDataByLocal' ||
          e.toolbar.id == 'Export1' ||
          e.toolbar.id == 'filterDataByLocal'
        )
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // 已认领的不能点击 分配、认领按钮
      let _orderStatus = [],
        _sourceStatus = [],
        _remainingQuantity = [],
        _companyCodes = [],
        _shippingPartitions = [], // 物流分区
        _shippingMethodNames = [], // 物流方式
        _id = [],
        _currencyCodes = [], // 币种
        _purchasers = [], // 采购员
        _railwayModes = [] //铁运模式
      let selectedRecords = e.gridRef.getMtechGridRecords()

      e.gridRef.getMtechGridRecords().map((item) => {
        _id.push(item.id),
          _orderStatus.push(item.orderStatus),
          _sourceStatus.push(item.sourceStatus),
          _remainingQuantity.push(item.remainingQuantity),
          _companyCodes.push(item.companyCode),
          _shippingPartitions.push(item.shippingPartition),
          _shippingMethodNames.push(item.shippingMethodName),
          _currencyCodes.push(item.currencyCode)
        _purchasers.push(item.purchaserId)
        _railwayModes.push(item.railwayMode)
      })
      if (e.toolbar.id == 'Export1') {
        this.handleExport()
      } else if (e.toolbar.id == 'Claim') {
        this.handleClaim(_id)
      } else if (e.toolbar.id == 'Distribute') {
        this.handleDistribute(_id)
      } else if (e.toolbar.id.includes('create-require')) {
        this.handleCreateRequire(
          e.toolbar.id,
          _id,
          e.gridRef.getMtechGridRecords(),
          _orderStatus,
          _sourceStatus,
          _companyCodes,
          _shippingPartitions,
          _shippingMethodNames,
          _railwayModes
          // _claimStatus
        )
      } else if (e.toolbar.id == 'create-order') {
        this.handleCreateOrder(
          _id,
          e.gridRef.getMtechGridRecords(),
          _remainingQuantity
          // _claimStatus
        )
      } else if (e.toolbar.id == 'transfer') {
        this.handleTransfer(_id)
      } else if (e.toolbar.id == 'cancelClaim') {
        this.handleCancelClaim(_id)
      } else if (e.toolbar.id == 'create-contract') {
        this.handleCreateContract(selectedRecords, _companyCodes, _currencyCodes, _purchasers)
      }
    },
    handleCreateContract(selectedRecords, companyCodes, currencyCodes, purchasers) {
      // 选择相同业务公司+币种+采购员
      if (selectedRecords.length > 1) {
        let _companyCodes = new Set(companyCodes)
        if (_companyCodes.size > 1) {
          this.$toast({
            content: this.$t('请选择同一业务公司'),
            type: 'warning'
          })
          return
        }
        let _currencyCodes = new Set(currencyCodes)
        if (_currencyCodes.size > 1) {
          this.$toast({
            content: this.$t('请选择同一币种'),
            type: 'warning'
          })
          return
        }
        let _purchasers = new Set(purchasers)
        if (_purchasers.size > 1) {
          this.$toast({
            content: this.$t('请选择同一采购员'),
            type: 'warning'
          })
          return
        }
      }
      if (selectedRecords[0].sourceStatus !== 0) {
        this.$toast({ content: this.$t('只能选择已转寻源为“否”数据进行操作'), type: 'warning' })
        return
      }
      if (selectedRecords[0].createdContract !== 0) {
        this.$toast({ content: this.$t('只能选择已转合同为“否”数据进行操作'), type: 'warning' })
        return
      }

      let ids = selectedRecords.map((v) => v.headerId)

      this.$router.push({
        path: '/purchase-execute/contract-create',
        query: {
          type: 'add',
          id: ids.join(','),
          currencyCode: selectedRecords[0].currencyCode,
          currencyName: selectedRecords[0].currencyName
        }
      })
    },

    // 取消认领
    handleCancelClaim(ids) {
      // this.$dialog({
      //   data: {
      //     title: this.$t('取消认领'),
      //     message: this.$t('取消认领后，需求将出现在采购需求池中被重新分配')
      //   },
      //   success: () => {
      //     this.$store.commit('startLoading')
      //     this.$API.purchaseRequest
      //       .cancelClaim({ idList: ids })
      //       .then((res) => {
      //         this.$store.commit('endLoading')
      //         if (res.code == 200) {
      //           this.$toast({ content: this.$t('操作成功'), type: 'success' })
      //           this.confirmSuccess()
      //         }
      //       })
      //       .catch(() => {
      //         this.$store.commit('endLoading')
      //       })
      //   }
      // })
      if (ids.length !== 1) {
        this.$toast({
          content: this.$t('请仅选择一条数据进行操作'),
          type: 'warning'
        })
        return
      }
      this.$refs.cancleDialog.dialogInit(ids[0])
    },
    // 转办
    handleTransfer(ids) {
      this.$dialog({
        modal: () => import('./components/distributeDialog.vue'),
        data: {
          title: this.$t('转办'),
          message: this.$t('是否确认转办？'),
          id: ids,
          businessTypeCode: this.selectedBusinessCode
        },
        success: () => {
          this.confirmSuccess()
        }
      })
    },

    // 创建采购订单：先校验剩余可创建订单数量 >0且有匹配价格记录, 后接口校验。都通过了，再缓存下当前列和勾选的行数据
    handleCreateOrder(ids, recordsList, remainingQuantity) {
      if (
        remainingQuantity &&
        remainingQuantity.length > 0 &&
        remainingQuantity.some((item) => item <= 0)
      ) {
        this.$toast({
          content: this.$t('剩余可创建采购订单数量为0的行无法进行该操作'),
          type: 'warning'
        })
        return
      }
      if (recordsList.some((item) => item.historyPriceNum <= 0)) {
        this.$toast({
          content: this.$t('未匹配价格记录的行无法进行该操作'),
          type: 'warning'
        })
        return
      }
      if (ids.length > 1) {
        this.$API.purchaseRequest
          .porCreateOrderConfigMatch({
            type: 0,
            businessTypeId: this.selectedBusinessId,
            ids: ids
          })
          .then((res) => {
            if (res.code == 200 && res.data) {
              this.setCreateOrderSession(recordsList)
              this.toCreateOrderPage()
            } else if (res.code == 200 && !res.data.result) {
              this.$dialog({
                modal: () => import('./components/consolidationErrorDialog.vue'),
                data: {
                  title: this.$t('提示'),
                  businessTypeName: this.businessTypeName,
                  checkItems: res.data?.checkItems,
                  consolidationData: res.data?.errorResponses,
                  columnData: errorColumns
                }
              })
              return
            }
          })
      } else {
        this.setCreateOrderSession(recordsList)
        this.toCreateOrderPage()
      }
    },
    // 跳转创建采购订单
    toCreateOrderPage() {
      this.$router.push({
        name: 'purchase-coordination-detail',
        query: {
          source: '0',
          type: '1'
        }
      })
    },

    // 设置跳转创建采购订单页面session
    setCreateOrderSession(recordsList) {
      let _this = this
      let _data = this.$refs[`statusCheckRef${this.tabIndex}`].statusData.find(function (a) {
        return a.value == _this.selectedBusinessId
      })
      sessionStorage.setItem(
        'purchaseRequestSession',
        JSON.stringify({
          columnData: this.pageConfig0[0].grid.columnData,
          dataSource: recordsList,
          businessInfo: {
            id: this.selectedBusinessId,
            code: _data.code,
            name: _data.label
          },
          companyInfo: {
            id: recordsList[0].companyId,
            code: recordsList[0].companyCode,
            name: recordsList[0].companyName
          }
        })
      )
    },

    // 页面跳转：创建寻源需求
    handleCreateRequire(
      toolbarId,
      ids,
      recordsList,
      orderStatus,
      sourceStatus,
      companyCodes,
      shippingPartitions,
      shippingMethodNames,
      railwayModes
    ) {
      if (sourceStatus && sourceStatus.length > 0 && sourceStatus.some((item) => item != 0)) {
        this.$toast({
          content: this.$t('已创建寻源需求的行无法进行该操作'),
          type: 'warning'
        })
        return
      } else if (companyCodes.length > 1) {
        let _companyCodes = new Set(companyCodes)
        if (_companyCodes.size > 1) {
          this.$toast({
            content: this.$t('请选择同一业务公司'),
            type: 'warning'
          })
          return
        }
      }
      if (shippingPartitions.length > 1) {
        let _shippingPartitions = new Set(shippingPartitions)
        if (_shippingPartitions.size > 1) {
          this.$toast({
            content: this.$t('请选择同一物流分区'),
            type: 'warning'
          })
          return
        }
      }
      if (shippingMethodNames.length > 1) {
        let _shippingMethodNames = new Set(shippingMethodNames)
        if (_shippingMethodNames.size > 1) {
          this.$toast({
            content: this.$t('请选择同一物流方式'),
            type: 'warning'
          })
          return
        }
      }
      if (railwayModes.length > 1) {
        let _railwayModes = new Set(railwayModes)
        if (_railwayModes.size > 1) {
          this.$toast({
            content: this.$t('请选择同一铁运方式'),
            type: 'warning'
          })
          return
        }
      }
      this.judgeSourcing(toolbarId, recordsList, ids)
    },
    //转寻源 弹框处理
    async createRFXByDialog(recordsList) {
      console.log('创建寻源需求，数据', recordsList)
      sessionStorage.setItem('dialogRFX_businessTypeCode', this.selectedBusinessCode)
      sessionStorage.setItem('dialogRFX_companyId', recordsList[0]['companyId'])
      sessionStorage.setItem('dialogRFX_siteCode', recordsList[0]['siteCode'])
      sessionStorage.setItem('dialogRFX_siteId', recordsList[0]['siteId'])
      await this.getCurrentEmployees()

      this.$dialog({
        modal: () => import('./components/dialogRFX/index.vue'),
        data: {
          title: this.$t('创建询价招标单'),
          source: this.biddingSource,
          businessTypeCode: this.selectedBusinessCode,
          businessTypeName: this.businessTypeName,
          businessTypeId: this.selectedBusinessId,
          recordsList,
          employeeList: this.employeeList
          // purOrgNameDataSource,
        },
        success: ({ sourcingMode, data }) => {
          console.log('创建询价招标单-成功', sourcingMode, data)
          if (document.domain === 'localhost') {
            // localhost 不执行路由跳转
            this.$refs[`template-${this.tabIndex}`].refreshCurrentGridData()
          } else {
            this.$router.push(
              `/sourcing/bid-hall/hall-detail?source=${sourcingMode}&sourceType=demand-pool&rfxId=${data.id}&status=0`
            )
          }
        }
      })
    },
    // 获取 用户列表
    async getCurrentEmployees() {
      await this.$API.masterData.getCurrentTenantEmployees({ fuzzyName: '' }).then((res) => {
        let tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        this.employeeList = cloneDeep(tmp)
      })
    },
    // 判断是推送 自采还是聚采 的寻源
    judgeSourcing(toolbarId, recordsList, ids) {
      if (toolbarId == 'create-require') {
        // this.setSourcingSession(recordsList);
        // this.$router.push({
        //   name: "create-sourcing-requirement",
        this.createRFXByDialog(recordsList)
      } else {
        this.juIds = ids
        // 聚采需要选择弹窗：采购组织
        this.getPurchaseOrg(recordsList)
      }
    },

    // 聚采 需要获取到采购组织
    getPurchaseOrg(recordsList) {
      this.showPruchaseDialog = true
      // this.$API.masterData
      //   .getOrganizateByOrgId({ orgId: recordsList[0].companyId })
      //   .then((res) => {
      //     this.buyerOrgOptions = res.data.map((item) => {
      //       return {
      //         text: item.organizationName,
      //         value: item.id,
      //         organizationName: item.organizationName,
      //         organizationCode: item.organizationCode,
      //       };
      //     });
      //     this.$refs.choseBuyDialog.buyerOrg = null;
      this.companyId = recordsList[0].companyId

      // });
    },

    // 聚采 获取采购组织，提交数据
    buyOrgDialogShow(obj) {
      if (!obj) {
        // 关闭获取弹窗
        this.showPruchaseDialog = false
        this.juIds = []
      } else {
        this.$API.purchaseRequest
          .createGatherPurchasePor({ ...obj, idList: this.juIds })
          .then(() => {
            this.$toast({
              content: this.$t('创建聚采需求成功'),
              type: 'success'
            })
            // 关闭获取弹窗
            this.showPruchaseDialog = false
            this.juIds = []
            this.confirmSuccess()
          })
      }
    },

    // 废弃：设置跳转创建寻源需求页面session
    // setSourcingSession(recordsList) {
    //   let _this = this;
    //   let _data;
    //   if (this.tabIndex === 0) {
    //     _data = this.$refs.statusCheckRef0.statusData.filter(function (a) {
    //       return a.value == _this.selectedBusinessId;
    //     });
    //   } else {
    //     _data = this.$refs.statusCheckRef1.statusData.filter(function (a) {
    //       return a.value == _this.selectedBusinessId;
    //     });
    //   }

    //   sessionStorage.setItem(
    //     "sourcingRequestSession",
    //     JSON.stringify({
    //       columnData:
    //         this.tabIndex == 1
    //           ? this.pageConfig0[0].grid.columnData
    //           : this.pageConfig1[0].grid.columnData,
    //       dataSource: recordsList,
    //       businessInfo: {
    //         id: this.selectedBusinessId,
    //         code: _data[0].code,
    //         name: _data[0].label,
    //       },
    //     })
    //   );
    // },

    handleExport() {
      let params = {
        ...this.searchFormModel,
        page: { current: 1, size: 999999 }
      }
      this.$store.commit('startLoading')
      this.$API.purchaseRequest.standingBookExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },

    // 弹窗：认领点击
    handleClaim(ids, claimStatus) {
      if (claimStatus && claimStatus.length > 0 && claimStatus.some((item) => item == 2)) {
        this.$toast({
          content: this.$t('已认领的需求无法进行该操作'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('认领'),
          message: this.$t('确定认领后，需求将出现在我认领的明细中')
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.purchaseRequest
            .claimDetail({ idList: ids })
            .then((res) => {
              this.$store.commit('endLoading')
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.confirmSuccess()
              }
            })
            .catch(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },

    // 弹窗：分配点击
    handleDistribute(ids) {
      this.$dialog({
        modal: () => import('./components/distributeDialog.vue'),
        data: {
          title: this.$t('分配'),
          id: ids,
          tabIndex: this.tabIndex
        },
        success: () => {
          this.$refs[`template-${this.tabIndex}`].refreshCurrentGridData()
        }
      })
    },

    // tab切换
    handleSelectTab(e) {
      this.tabIndex = e
      if (e !== 4) {
        this.businessTypeList.forEach((item, index) => {
          item.num = this.businessNumList[e][index]
        })
      }
    },

    // 顶部筛选 调整
    async handleChose(e) {
      this.$store.commit('startLoading')
      this.selectedBusinessId = e
      this.businessTypeName = this.businessTypeList.find((item) => item.value == e)?.label
      this.selectedBusinessCode = this.businessTypeList.find((item) => item.value == e)?.code
      this.$nextTick(() => {
        // this.$refs[`template-${this.tabIndex}`].refreshCurrentGridData()
      })
      await this.getDetailData()
      this.$store.commit('endLoading')
    },

    // 1. 获取动态列，2. 获取行数据
    async getDetailData() {
      // 获取明细的列
      let tempCloumnData = await this.$API.purchaseRequest.getModuleByBusinessId({
        businessType: this.selectedBusinessId,
        moduleType: 0
      })
      // 组合列
      let cloumnData = []
      // if ([0, 2].includes(this.tabIndex)) {
      // cloumnData = this.handleUnionColumn(tempCloumnData.data)
      // } else {
      //   cloumnData = this.handleUnionColumn2(tempCloumnData.data);
      // }
      this.handleUnionColumn(tempCloumnData.data).forEach((item) => {
        if (
          this.tabIndex === 3 &&
          ['distributionUserName', 'claimDate', 'claimStatus', 'distributionDate'].includes(
            item.field
          )
        ) {
          // cloumnData.push(item)
          return
        }
        if (this.tabIndex === 3 && ['claimUserName'].includes(item.field)) {
          cloumnData.push({
            width: '150',
            field: 'purchaserName',
            headerText: this.$t('采购员')
          })
          return
        }
        // 如果是物流类年约需求，过滤相关字段 [已转订单、剩余可创建订单数、项目类型、项目编号、项目名称]
        if (
          this.selectedBusinessCode === 'BTTCL006' &&
          [
            'orderStatus',
            'remainingQuantity',
            'projectType',
            'projectCode',
            'projectName'
          ].includes(item.field)
        ) {
          return
        }
        // 如果不是非采，过滤相关字段【已转合同】
        if (
          ['createdContract'].includes(item.field) &&
          (this.tabIndex !== 3 ||
            !['BTTCL001', 'BTTCL002', 'BTTCL003'].includes(this.selectedBusinessCode))
        ) {
          return
        }
        cloumnData.push(item)
      })

      let _grid = {
        columnData: cloumnData,
        dataSource: [],
        lineIndex: 1
        // frozenColumns: 1,
      }
      // this.$set(
      //   this[`pageConfig${this.tabIndex}`][0].grid,
      //   "columnData",
      //   cloumnData
      // );

      let _defaultRules = [
        {
          label: this.$t('业务类型'),
          field: 'businessTypeId',
          type: 'number',
          operator: 'equal',
          value: this.selectedBusinessId
        }
      ]
      let tools = [
        [
          {
            id: 'transfer',
            icon: 'icon_solid_Submit',
            title: this.$t('转办'),
            permission: ['O_02_0364']
          },
          {
            id: 'cancelClaim',
            icon: 'icon_list_recall',
            title: this.$t('驳回'),
            permission: ['O_02_1391']
          },
          {
            id: 'create-require',
            icon: 'icon_solid_Createorder',
            title: this.$t('创建询价招标单'),
            permission: ['O_02_0366']
          },
          {
            id: 'create-require-ju',
            icon: 'icon_solid_Createorder',
            title: this.$t('创建聚采寻源需求'),
            permission: ['O_02_0367']
          }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
      let _asyncConfig, uuidName
      // 获取业务类型对应的明细数据
      // 转订单 条件：业务类型、 有价格（priceStatus=1）  未认领（claimStatus=0）  审批状态为通过（status=2）
      if (this.tabIndex == 0) {
        uuidName = 'toOrder'
        let nowRules = [
          {
            label: this.$t('有价格'),
            field: 'priceStatus',
            type: 'number',
            operator: 'equal',
            value: '1'
          },
          {
            label: this.$t('未认领'),
            field: 'claimStatus',
            type: 'number',
            operator: 'equal',
            value: '0'
          },
          {
            label: this.$t('审批状态为通过'),
            field: 'status',
            type: 'number',
            operator: 'equal',
            value: '2'
          }
        ]
        _asyncConfig = {
          url: `${BASE_TENANT}/requestItem/requestPoolQuery`,
          afterAsyncData: this.afterAsyncData,
          defaultRules: _defaultRules.concat(nowRules)
        }
      } else if (this.tabIndex == 1) {
        uuidName = 'myOrder'
        // 我认领的转订单 条件：业务类型  有价格（priceStatus=1）  审批状态为通过（status=2）
        _asyncConfig = {
          url: `${BASE_TENANT}/requestItem/queryOwn`,
          afterAsyncData: this.afterAsyncData,
          defaultRules: _defaultRules.concat([
            {
              label: this.$t('有价格'),
              field: 'priceStatus',
              type: 'number',
              operator: 'equal',
              value: '1'
            },
            {
              label: this.$t('审批通过'),
              field: 'status',
              type: 'number',
              operator: 'equal',
              value: '2'
            }
          ])
        }
      } else if (this.tabIndex == 2) {
        uuidName = 'toSource'
        // 转寻源条件：业务类型  无价格（priceStatus=0）  未认领 （claimStatus=0）审批状态为通过（status=2）
        let nowRules = [
          {
            label: this.$t('无价格'),
            field: 'priceStatus',
            type: 'number',
            operator: 'equal',
            value: '0'
          },
          {
            label: this.$t('未认领'),
            field: 'claimStatus',
            type: 'number',
            operator: 'equal',
            value: '0'
          },
          {
            label: this.$t('审批状态为通过'),
            field: 'status',
            type: 'number',
            operator: 'equal',
            value: '2'
          }
        ]
        _asyncConfig = {
          url: `${BASE_TENANT}/requestItem/requestPoolQuery`,
          afterAsyncData: this.afterAsyncData,
          defaultRules: _defaultRules.concat(nowRules)
        }
      } else if (this.tabIndex == 3) {
        uuidName = 'mySource'
        // 我认领的条件：业务类型  无价格（priceStatus=0）  审批状态为通过（status=2）
        if (this.selectedBusinessCode === 'BTTCL006') {
          _asyncConfig = {
            url: `/contract/tenant/logistics/auto/header/list`,
            afterAsyncData: this.afterAsyncData
          }
          tools = [
            [
              {
                id: 'transfer',
                icon: 'icon_solid_Submit',
                title: this.$t('转办'),
                permission: ['O_02_0364']
              },
              {
                id: 'create-require',
                icon: 'icon_solid_Createorder',
                title: this.$t('创建询价招标单'),
                permission: ['O_02_0366']
              }
            ],
            ['Filter', 'Refresh', 'Setting']
          ]
        } else {
          if (['BTTCL001', 'BTTCL002', 'BTTCL003'].includes(this.selectedBusinessCode)) {
            tools = [
              [
                {
                  id: 'transfer',
                  icon: 'icon_solid_Submit',
                  title: this.$t('转办'),
                  permission: ['O_02_0364']
                },
                {
                  id: 'cancelClaim',
                  icon: 'icon_list_recall',
                  title: this.$t('驳回'),
                  permission: ['O_02_1391']
                },
                {
                  id: 'create-require',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('创建询价招标单'),
                  permission: ['O_02_0366']
                },
                {
                  id: 'create-require-ju',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('创建聚采寻源需求'),
                  permission: ['O_02_0367']
                },
                {
                  id: 'create-contract',
                  title: this.$t('转合同'),
                  permission: ['O_02_0367']
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          }
          _asyncConfig = {
            url: `${BASE_TENANT}/requestItem/queryOwn`,
            afterAsyncData: this.afterAsyncData,
            serializeList: (list) => {
              let dateFields = ['goodGoodsTime', 'demandStartTime', 'demandArriverTime']
              list.forEach((item) => {
                dateFields.forEach((field) => {
                  item[field] &&
                    (item[field] =
                      item[field].length === 13
                        ? UTILS.formateTime(new Date(Number(item[field])), 'YYYY-mm-dd HH:MM:SS')
                        : UTILS.formateTime(new Date(item[field]), 'YYYY-mm-dd HH:MM:SS'))
                })
              })
              return list
            },
            defaultRules: _defaultRules.concat([
              {
                label: this.$t('无价格'),
                field: 'priceStatus',
                type: 'number',
                operator: 'equal',
                value: '0'
              },
              {
                label: this.$t('状态'),
                field: 'status',
                type: 'number',
                operator: 'equal',
                value: '2'
              }
            ])
          }
        }
      }

      _grid.asyncConfig = _asyncConfig
      this.$set(this[`pageConfig${this.tabIndex}`][0], 'grid', _grid)
      this.$set(this[`pageConfig${this.tabIndex}`][0], 'toolbar', { tools })
      this.$set(
        this[`pageConfig${this.tabIndex}`][0],
        'gridId',
        this.$md5(this.$tableUUID.demandPool[uuidName] + this.selectedBusinessId)
      )
    },
    // 获取业务类型下拉
    async getBusinessConfig() {
      await this.$API.bgConfig.getBusinessConfig().then((res) => {
        if (res.data && res.data.length) {
          let businessTypeList = []
          let codes = []
          res.data.map((item) => {
            businessTypeList.push({
              label: item.businessTypeName,
              value: item.businessTypeId,
              code: item.businessTypeCode
            })
            codes.push(item.businessTypeCode)
          })
          this.allBusinessTypeList = businessTypeList
          this.allCodes = codes
          this.getBusinessNum(businessTypeList, codes)
        }
      })
    },
    updateGetBusinessNum() {
      //更新获取业务类型代办数量
      let allBusinessTypeList = cloneDeep(this.allBusinessTypeList)
      let allCodes = cloneDeep(this.allCodes)
      this.getBusinessNum(allBusinessTypeList, allCodes)
    },
    // 获取不同tab的业务类型的代办数量
    async getBusinessNum(businessTypeList, codes) {
      let res = await this.$API.purchaseRequest.getBusinessNum({ businessTypeCodeList: codes })
      let logisticsRes = await this.$API.purchaseRequest.getLogisticsBusinessNum({
        businessTypeCode: 'BTTCL006'
      })
      let businessNumList = [[], [], [], []]
      // unClaimOrderAmount: [], // 转订单未认领数量
      // unClaimSourceAmount: [], // 待转寻源的数量
      // unOrderedAmount: [], // 已认领的 转订单
      // unSourceAmount: [], // 已认领的转寻源
      let _data = res.data
      _data.forEach((item) => {
        businessNumList[0].push(item.unClaimOrderAmount)
        businessNumList[1].push(item.unOrderedAmount)
        businessNumList[2].push(item.unClaimSourceAmount)
        businessNumList[3].push(item.unSourceAmount)
        // 物流类接口不一样
        if (logisticsRes.code === 200) {
          businessNumList[0].push(logisticsRes.data.count)
          businessNumList[1].push(logisticsRes.data.count)
          businessNumList[2].push(logisticsRes.data.count)
          businessNumList[3].push(logisticsRes.data.count)
        }
      })

      this.businessNumList = businessNumList
      let tabIndex = this.tabIndex || 0
      businessTypeList.forEach((item, index) => {
        item.num = this.businessNumList[tabIndex][index]
      })
      this.businessTypeList = businessTypeList
    },

    // 点击单元格 标题
    handleClickCellTitle(e) {
      if (e.field == 'requestCode') {
        // requestType  0-采购申请、1-寻源申请
        if (
          e.data.requestType == 0 ||
          e.data.requestType == 2 ||
          e.data.requestType == 3 ||
          e.data.requestType == 4
        ) {
          this.$router.push(
            `pr-apply-detail?id=${e.data.headerId}&type=view&sourceType=${e.data.sourceType}`
          )
        } else if (e.data.requestType == 5) {
          // this.$router.push(
          //   `pr-apply-detail?id=${e.data.headerId}&type=view&sourceType=${e.data.sourceType}&pageType=logistics`
          // )
          // 物流采购申请新增了页面，直接跳转到新页面，不在pr-apply-detail作兼容
          this.$router.push(`pr-apply-logistics-detail?id=${e.data.headerId}&type=view`)
        } else {
          this.$router.push(`source-apply-detail?id=${e.data.headerId}&type=view`)
        }
      } else if (e.field == 'historyPriceNum') {
        this.handlePrice(e.data)
      }
    },

    // 整合列
    handleUnionColumn(dynamicColumn) {
      let _colFormatRes = [],
        _res = []
      let { checkCol, preCols, lastCols, priceCol, syncStatusCol, createInfo } = finalCols
      // 待转订单 只有价格记录列不一样
      if ([0, 1].includes(this.tabIndex)) {
        _colFormatRes = preCols.concat(dynamicColumn, lastCols, priceCol, createInfo)
        let _colRes = this.arrSet(_colFormatRes) // 去重
        _res = this.handleRepeatCol(_colRes)
        _res = checkCol.concat(_res)
      } else if ([2, 3].includes(this.tabIndex)) {
        // 我认领的转订单 只有同步状态列不一样
        _colFormatRes = preCols.concat(dynamicColumn, lastCols, syncStatusCol, createInfo)
        let _colRes = this.arrSet(_colFormatRes) // 去重
        _res = this.handleRepeatCol(_colRes)
        _res = checkCol.concat(_res)
        // } else if ([4].includes(this.tabIndex)) {
        //   _res = FCColumn
      }

      return _res
    },

    // 去重
    arrSet(arr) {
      let obj = {}
      const res = arr.reduce((setArr, item) => {
        let _field = item.field || item.fieldCode
        if (!obj[_field]) {
          obj[_field] = true
          setArr.push(item)
        } else {
          let _findIndex = setArr.findIndex((i) => i[_field] == _field)
          setArr[_findIndex] = {
            ...setArr[_findIndex],
            ...item
          }
        }
        return setArr
      }, [])
      return res
    },

    // 处理字段对应的其他设置
    handleRepeatCol(cols) {
      let res = []
      cols.forEach((item) => {
        let _col = {
          ...item,
          width: item.width || '150',
          field: item.field || item.fieldCode,
          headerText: item.headerText || item.fieldName
        }

        // 添加主数据搜索的字段：利润中心  成本中心  币种  采购单位  基本单位  地点/工厂 税率编码
        _col = formatMasterFilter(_col)

        // 判断动态列中 是否有 认领状态
        if (item.fieldCode == 'claimStatus') {
          _col = {
            ..._col,
            valueConverter: {
              type: 'map',
              map: [
                {
                  value: '0',
                  text: this.$t('未认领'),
                  cssClass: 'col-inactive'
                },
                {
                  value: '2',
                  text: this.$t('已认领'),
                  cssClass: 'col-active'
                }
              ]
            }
          }
        }
        // 判断动态列中是否有质量免检标识
        if (item.fieldCode == 'qualityExemptionMark') {
          _col = {
            ..._col,
            valueConverter: {
              type: 'map',
              map: {
                0: this.$t('需检验'),
                1: this.$t('免检')
              }
            }
          }
        }
        // 判断是否有成本中心
        if (item.fieldCode == 'costSharingAccName') {
          _col = {
            ..._col,
            ...finalCols.costSharingAccName[0]
          }
        }
        // 判断是否有推荐供应商代码
        if (item.fieldCode == 'supplierCode') {
          _col = {
            ..._col,
            ...finalCols.supplierCode[0]
          }
        }
        // 判断是否有推荐供应商名称
        if (item.fieldCode == 'supplierName') {
          _col = {
            ..._col,
            ...finalCols.supplierName[0]
          }
        }
        // 判断动态列中 是否有 附件
        if (item.fieldCode == 'file') {
          _col = {
            width: '150',
            field: item.fieldCode,
            headerText: item.fieldName,
            template: function () {
              return {
                template: Vue.component('fileTypeOption', {
                  template: `<div @click="showFileBaseInfo" class="cell-operable-title">{{data.file | listNumFormat}}</div>`,
                  data() {
                    return { data: {} }
                  },
                  filters: {
                    listNumFormat(value) {
                      if (value && value.length > 0) {
                        return value.length
                      } else {
                        return ''
                      }
                    }
                  },
                  methods: {
                    showFileBaseInfo() {
                      this.$parent.$emit('showFileBaseInfo', {
                        index: this.data.index,
                        value: this.data.file
                      })
                    }
                  }
                })
              }
            }
          }
        }
        // 判断动态列中 是否有 项目名称
        if (item.fieldCode == 'projectName') {
          _col = {
            ..._col,
            ...finalCols.lastCols[2]
          }
        }
        // 判断动态列中 是否有 项目编码
        if (item.fieldCode == 'projectCode') {
          _col = {
            ..._col,
            ...finalCols.lastCols[1]
          }
        }
        // 如果有税率，需要*100
        if (item.fieldCode == 'taxid') {
          _col = {
            ..._col,
            valueAccessor: (field, data) => {
              return data.taxid * 100
            }
          }
        }
        res.push(_col)
      })
      return res
    },

    // 废弃：调整从后台获取的动态列，判断是否在本地配置过
    formatColumnToGrid(columns, chkColumns) {
      let res = []
      columns.forEach((item) => {
        if (item.fieldCode != 'picUrl' && chkColumns.indexOf(item.fieldCode) < 0) {
          let _col = {
            width: '150',
            field: item.fieldCode,
            headerText: item.fieldName
          }

          // 判断动态列中 是否有 认领状态
          if (item.fieldCode == 'claimStatus') {
            _col = {
              ..._col,
              valueConverter: {
                type: 'map',
                map: [
                  {
                    value: '0',
                    text: this.$t('未认领'),
                    cssClass: 'col-inactive'
                  },
                  {
                    value: '2',
                    text: this.$t('已认领'),
                    cssClass: 'col-active'
                  }
                ]
              }
            }
          }
          // 判断动态列中是否有质量免检标识
          if (item.fieldCode == 'qualityExemptionMark') {
            _col = {
              ..._col,
              valueConverter: {
                type: 'map',
                map: {
                  0: this.$t('需检验'),
                  1: this.$t('免检')
                }
              }
            }
          }
          // 判断是否有成本中心
          if (item.fieldCode == 'costSharingAccName') {
            _col = {
              ..._col,
              ...finalCols.costSharingAccName[0]
            }
          }
          // 判断是否有推荐供应商代码
          if (item.fieldCode == 'supplierCode') {
            _col = {
              ..._col,
              ...finalCols.supplierCode[0]
            }
          }
          // 判断是否有推荐供应商名称
          if (item.fieldCode == 'supplierName') {
            _col = {
              ..._col,
              ...finalCols.supplierName[0]
            }
          }
          // 判断动态列中 是否有 附件
          if (item.fieldName == 'file') {
            _col = {
              width: '150',
              field: item.fieldCode,
              headerText: item.fieldName,
              template: function () {
                return {
                  template: Vue.component('fileTypeOption', {
                    template: `<div @click="showFileBaseInfo" class="cell-operable-title">{{data.file | listNumFormat}}</div>`,
                    data() {
                      return { data: {} }
                    },
                    filters: {
                      listNumFormat(value) {
                        if (value && value.length > 0) {
                          return value.length
                        } else {
                          return ''
                        }
                      }
                    },
                    methods: {
                      showFileBaseInfo() {
                        this.$parent.$emit('showFileBaseInfo', {
                          index: this.data.index,
                          value: this.data.file
                        })
                      }
                    }
                  })
                }
              }
            }
          }
          // 判断动态列中 是否有 项目名称
          if (item.fieldName == 'projectName') {
            _col = {
              ..._col,
              ...finalCols.lastCols[2]
            }
          }
          // 判断动态列中 是否有 项目编码
          if (item.fieldName == 'projectCode') {
            _col = {
              ..._col,
              ...finalCols.lastCols[1]
            }
          }

          // 判断动态列中 是否有 科目类型
          if (item.fieldName == 'subjectType') {
            _col = {
              ..._col,
              valueConverter: {
                type: 'map',
                map: {
                  0: this.$t('成本中心'),
                  1: this.$t('销售订单'),
                  2: this.$t('生产工单'),
                  3: this.$t('项目')
                }
              }
            }
          }

          res.push(_col)
        }
      })
      return res
    },

    async confirmSuccess() {
      await this.$refs[`template-${this.tabIndex}`].refreshCurrentGridData()
      this.updateGetBusinessNum()
    }
  }
}
</script>

<style lang="scss" scoped>
.top-filter {
  height: 70px;
  background: #fff;
  padding: 20px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left-status {
    margin-right: 20px;
    overflow: hidden;
    display: flex;
    align-items: center;
    .tit {
      flex-shrink: 0;
    }
  }
  /deep/ .total-number {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: #9a9a9a;
    line-height: 28px;
    flex-shrink: 0;
    margin-left: 20px;
  }
}

/deep/ .mt-icon-icon_card_info {
  margin-left: -16px;
}
</style>
