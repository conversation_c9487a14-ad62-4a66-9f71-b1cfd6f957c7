import Vue from 'vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant.js'

export const checkCol = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  }
]

// 仅订单的tab：匹配价格记录
export const priceCol = [
  {
    width: '150',
    field: 'historyPriceNum',
    headerText: i18n.t('匹配价格记录'),
    cellTools: []
  }
]

// 仅转寻源的tab：同步状态
export const syncStatusCol = [
  {
    width: '120',
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('未同步'), cssClass: 'col-inactive' },
        { value: 1, text: i18n.t('已同步'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('同步失败'), cssClass: 'col-inactive' }
      ]
    }
  }
]

// 序号，待处理时间，转订单，转寻源，剩余需求数，单据编号，单据名称,业务类型
export const preCols = [
  {
    width: '150',
    field: 'waitingTime',
    headerText: i18n.t('待处理时间'),
    ignore: true
  },
  {
    width: '120',
    field: 'orderStatus',
    headerText: i18n.t('已转订单'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    width: '120',
    field: 'sourceStatus',
    headerText: i18n.t('已转寻源'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    width: '120',
    field: 'createdContract',
    headerText: i18n.t('已转合同'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    width: '180',
    field: 'remainingQuantity',
    headerText: i18n.t('剩余可创建订单数')
  },
  {
    width: '150',
    field: 'requestCode',
    headerText: i18n.t('单据编号'),
    cellTools: []
  },
  {
    width: '150',
    field: 'title',
    headerText: i18n.t('单据名称')
  },
  {
    width: '150',
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  }
]

// 项目类型，项目编号，项目名称，业务公司，行政公司，申请部门，申请人，申请日期，来源，匹配价格记录（仅订单的tab有），认领数量（仅认领的tab有），同步状态（仅转寻源有），创建人，创建时间，备注
export const lastCols = [
  {
    width: '150',
    field: 'projectType',
    headerText: i18n.t('项目类型'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('项目型'), 0: i18n.t('非项目型') }
    }
  },
  {
    width: '150',
    field: 'projectCode',
    headerText: i18n.t('项目编号')
  },
  {
    width: '150',
    field: 'projectName',
    headerText: i18n.t('项目名称')
  },
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('业务公司')
  },
  {
    width: '150',
    field: 'administrationCompanyName',
    headerText: i18n.t('行政公司')
  },
  {
    width: '150',
    field: 'applyDepName',
    headerText: i18n.t('申请部门')
  },
  {
    width: '150',
    field: 'applyUserName',
    headerText: i18n.t('申请人')
  },
  {
    width: '150',
    field: 'applyDate',
    headerText: i18n.t('申请日期'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    width: '150',
    field: 'sourceType',
    headerText: i18n.t('来源'),
    valueConverter: {
      type: 'map', // 0：手工创建 1:导入 2:第三方接口 3:TMS 4:0A
      map: {
        0: i18n.t('手工创建'),
        1: i18n.t('共享财务'),
        2: i18n.t('商城'),
        3: i18n.t('TMS'),
        4: i18n.t('OA')
      }
    }
  }
]

export const createInfo = [
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    width: '150',
    field: 'docUpdateTime',
    headerText: i18n.t('单据更新时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  }
]

// 匹配价格记录详情
export const priceColumn = [
  // {
  //   width: "70",
  //   headerText: i18n.t("序号"),
  // },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'skuCode',
    headerText: i18n.t('SKU编号')
  },
  {
    width: '150',
    field: 'skuName',
    headerText: i18n.t('SKU名称')
  },
  {
    width: '150',
    field: 'spec',
    headerText: i18n.t('规格型号')
  },
  {
    width: '150',
    field: 'categoryCode',
    headerText: i18n.t('品类编号')
  },
  {
    width: '150',
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    width: '150',
    field: 'contractCode',
    headerText: i18n.t('合同协议编号')
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编号')
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  // {
  //   width: "150",
  //   field: "",
  //   headerText: i18n.t("未税单价"),
  // },
  {
    width: '150',
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  // {
  //   width: "150",
  //   field: "",
  //   headerText: i18n.t("含税单价"),
  // },
  // {
  //   width: "150",
  //   field: "",
  //   headerText: i18n.t("折扣率"),
  // },
  {
    width: '150',
    field: '',
    headerText: i18n.t('阶梯价格'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `
            <mt-button :isPrimary="true" @click="handleViewStage">{{i18n.t("查看")}}</mt-button>
          `,
          data() {
            return { data: { i18n } }
          },
          methods: {
            handleViewStage() {
              this.$parent.$emit('handleViewStage', this.data.stageList)
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'validStartTime',
    headerText: i18n.t('有效期起')
  },
  {
    width: '150',
    field: 'validEndTime',
    headerText: i18n.t('有效期至')
  }
]

export const costSharingAccName = [
  {
    width: '180',
    field: 'costSharingAccName',
    headerText: i18n.t('成本中心'),
    template: () => {
      return {
        template: Vue.component('cost', {
          template: `<div>{{showCost}}</div>`,
          data() {
            return {
              data: {},
              showCost: ''
            }
          },
          mounted() {
            this.showCost = ''
            if (!this.data.costSharingResponses || !this.data.costSharingResponses.length) return
            this.data.costSharingResponses.forEach((item) => {
              this.showCost += item.costSharingAccName + '-' + item.appProportion + '%;'
            })
          }
        })
      }
    }
  }
]
export const supplierCode = [
  {
    width: '180',
    field: 'supplierCode',
    headerText: i18n.t('推荐供应商代码'),
    template: () => {
      return {
        template: Vue.component('cost', {
          template: `<div>{{code}}</div>`,
          data() {
            return {
              data: {},
              code: ''
            }
          },
          mounted() {
            this.code = ''
            if (!this.data.supplierResponses || !this.data.supplierResponses.length) {
              return
            } else {
              this.code = this.data.supplierResponses[0].supplierCode
            }
          }
        })
      }
    }
  }
]
export const supplierName = [
  {
    width: '180',
    field: 'supplierName',
    headerText: i18n.t('推荐供应商名称'),
    template: () => {
      return {
        template: Vue.component('cost', {
          template: `<div>{{name}}</div>`,
          data() {
            return {
              data: {},
              name: ''
            }
          },
          mounted() {
            this.name = ''
            if (!this.data.supplierResponses || !this.data.supplierResponses.length) {
              return
            } else {
              this.name = this.data.supplierResponses[0].supplierName
            }
          }
        })
      }
    }
  }
]
export const FCColumn = [
  {
    width: '180',
    field: 'purchaseMode',
    headerText: i18n.t('采购模式'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 'selfPurChase', text: i18n.t('自采'), cssClass: '' },
        { value: 'polymerizationPurchase', text: i18n.t('聚采'), cssClass: '' }
      ]
    }
  },
  {
    width: '180',
    field: 'associateSourceNo',
    headerText: i18n.t('关联寻源单号')
  },
  {
    width: '180',
    field: 'companyName',
    headerText: i18n.t('业务公司名称')
  },
  {
    width: '180',
    field: 'requestCode',
    headerText: i18n.t('需求申请号')
  },
  {
    width: '180',
    field: 'purchaserName',
    headerText: i18n.t('采购员')
  },
  {
    width: '180',
    field: 'demandDeptName',
    headerText: i18n.t('需求部门')
  },
  {
    width: '180',
    field: 'projectName',
    headerText: i18n.t('项目/物料名称')
  },
  {
    width: '180',
    field: 'pricingTime',
    headerText: i18n.t('招标/定标时间')
  },
  {
    width: '180',
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    width: '180',
    field: 'quantity',
    headerText: i18n.t('数量')
  },
  {
    width: '180',
    field: 'unitName',
    headerText: i18n.t('单位')
  },
  {
    width: '180',
    field: 'budgetTotalPriceUntaxed',
    headerText: i18n.t('预算金额 未税（万元）')
  },
  {
    width: '180',
    field: 'isSupplementDemand',
    headerText: i18n.t('是否增补'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('是'), cssClass: '' },
        { value: 0, text: i18n.t('否'), cssClass: '' }
      ]
    }
  },
  {
    width: '180',
    field: 'supplementTaxedTotalPrice',
    headerText: i18n.t('增补金额')
  },
  {
    width: '180',
    field: 'purchaseAmtUntaxed',
    headerText: i18n.t('采购金额 未税（万元）')
  },
  {
    width: '180',
    field: 'saveAmtUntaxed',
    headerText: i18n.t('节省金额 未税（万元）')
  },
  {
    width: '180',
    field: 'declinePercent',
    headerText: i18n.t('降幅')
  },
  // {
  //   width: '180',
  //   field: 'businessClassifyL1',
  //   headerText: i18n.t('业务一级分类')
  // },
  // {
  //   width: '180',
  //   field: 'businessClassifyL2',
  //   headerText: i18n.t('业务二级分类')
  // },
  {
    width: '180',
    field: 'businessClassifyL3',
    headerText: i18n.t('业务三级分类')
  },
  {
    width: '180',
    field: 'purchaseMethodDesc',
    headerText: i18n.t('采购方式')
  },
  {
    width: '180',
    field: 'orderNo',
    headerText: i18n.t('订单号')
  },
  {
    width: '180',
    field: 'associateContractNo',
    headerText: i18n.t('关联合同编号')
  },
  {
    width: '180',
    field: 'contractEffectiveTime',
    headerText: i18n.t('合同生效日期')
  },
  {
    width: '180',
    field: 'contractExpireTime',
    headerText: i18n.t('合同到期日期')
  },
  {
    width: '180',
    field: 'supplierNameL1',
    headerText: i18n.t('供应商名称（一级）')
  },
  {
    width: '180',
    field: 'supplierNameL2',
    headerText: i18n.t('供应商名称（二级）')
  },
  {
    width: '180',
    field: 'paymentMethod',
    headerText: i18n.t('付款方式')
  },
  {
    width: '180',
    field: 'qaTimeLimitValue',
    headerText: i18n.t('质保期')
  },
  {
    width: '180',
    field: 'checkAcceptTime',
    headerText: i18n.t('验收时间')
  },
  {
    width: '180',
    field: 'remark',
    headerText: i18n.t('备注')
  },
  {
    width: '180',
    field: 'demandSubmitTime',
    headerText: i18n.t('需求提交时间')
  }
]
