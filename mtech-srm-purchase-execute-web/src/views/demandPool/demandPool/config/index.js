import Vue from 'vue'
import cellImgShow from '@/components/normalEdit/cellImgShow' // 单元格显示图片
import { i18n } from '@/main.js'
export const checkCol = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  }
]

export const mainFirstTowCol = [
  {
    width: '150',
    field: 'requestCode',
    headerText: i18n.t('编号'),
    cellTools: []
  },
  {
    width: '150',
    field: 'title',
    headerText: i18n.t('名称')
  }
]

export const noPriceFirstCol = [
  {
    width: '150',
    field: 'requestCode',
    headerText: i18n.t('单据编号')
  },
  {
    width: '150',
    field: 'title',
    headerText: i18n.t('单据名称')
  }
]
export const detailFirstCol = [
  {
    width: '150',
    field: 'requestCode',
    headerText: i18n.t('单据编号')
  },
  {
    width: '150',
    field: 'title',
    headerText: i18n.t('单据名称')
  }
]

export const completedFirstCol = [
  {
    width: '150',
    field: 'picUrl',
    headerText: i18n.t('图片'),
    template: () => {
      return { template: cellImgShow }
    }
  },
  {
    width: '150',
    field: 'requestCode',
    headerText: i18n.t('编号')
  },
  {
    width: '150',
    field: 'title',
    headerText: i18n.t('名称')
  }
]

// 匹配价格记录
export const priceCol = [
  {
    width: '150',
    field: 'historyPriceNum',
    headerText: i18n.t('匹配价格记录'),
    cellTools: []
  }
]

export const middleLeftCol = [
  {
    width: '150',
    field: 'sourceType',
    headerText: i18n.t('来源'),
    valueConverter: {
      type: 'map', // 0：手工创建 1:导入 2:第三方接口
      map: {
        0: i18n.t('手工创建'),
        1: i18n.t('共享财务'),
        2: i18n.t('商城')
      }
    }
  },
  {
    width: '150',
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  }
]
export const mainMiddleCol = [
  {
    width: '150',
    field: 'projectType',
    headerText: i18n.t('项目类型'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('项目型'), 0: i18n.t('非项目型') }
    }
  },
  {
    width: '150',
    field: 'projectDesc',
    headerText: i18n.t('项目信息')
  }
]

export const detailMiddleCol = [
  {
    width: '150',
    field: 'projectType',
    headerText: i18n.t('项目类型'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('项目型'), 0: i18n.t('非项目型') }
    }
  },
  {
    width: '150',
    field: 'projectCode',
    headerText: i18n.t('项目编号')
  },
  {
    width: '150',
    field: 'projectName',
    headerText: i18n.t('项目名称')
  }
]

export const buyerGroupCol = [
  // {
  //   width: "150",
  //   field: "buyerGroupName",
  //   headerText: i18n.t("采购组织"),
  // },
]
export const middleRightCol = [
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('业务公司')
  },
  {
    width: '150',
    field: 'administrationCompanyName',
    headerText: i18n.t('行政公司')
  },
  {
    width: '150',
    field: 'applyDepName',
    headerText: i18n.t('申请部门')
  },
  {
    width: '150',
    field: 'applyUserName',
    headerText: i18n.t('申请人')
  },
  {
    width: '150',
    field: 'applyDate',
    headerText: i18n.t('申请日期')
  }
]
export const creatorCol = [
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间')
  }
]
export const lastCol = [
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

// 同步状态
export const syncStatusCol = [
  {
    width: '120',
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('未同步'), cssClass: 'col-inactive' },
        { value: 1, text: i18n.t('已同步'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('同步失败'), cssClass: 'col-inactive' }
      ]
    }
  }
]

export const mainClaimCol = [
  {
    width: '150',
    field: 'waitingTime',
    headerText: i18n.t('等待时间')
  },
  {
    width: '150',
    field: 'claimStatus',
    headerText: i18n.t('认领状态'),
    valueConverter: {
      type: 'map',
      // map: { 0: i18n.t("未认领"), 1: i18n.t("部分认领"), 2: i18n.t("全部认领") },
      map: [
        { value: 0, text: i18n.t('未认领'), cssClass: 'col-inactive' },
        { value: 1, text: i18n.t('部分认领'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('全部认领'), cssClass: 'col-active' }
      ]
    }
  }
]

export const detailClaimCol = [
  {
    width: '150',
    field: 'waitingTime',
    headerText: i18n.t('待处理时间'),
    ignore: true
  }
]

export const remainingQuantity = [
  {
    width: '210',
    field: 'remainingQuantity',
    headerText: i18n.t('剩余可创建采购订单数量')
  }
]

export const souringRelativeStatus = [
  {
    width: '120',
    field: 'orderStatus',
    headerText: i18n.t('已转订单'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    width: '120',
    field: 'sourceStatus',
    headerText: i18n.t('已转寻源'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  }
]

export const claimNumbers = [
  {
    width: '150',
    field: 'claimedLineNum',
    headerText: i18n.t('认领行数'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><span>{{data.claimedLineNum}}/{{data.totalLineNum}}</span></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  }
]

export const handleUser = [
  {
    width: '150',
    field: 'updateUserName',
    headerText: i18n.t('处理人')
  },
  {
    width: '150',
    field: 'updateTime',
    headerText: i18n.t('处理时间')
  }
]

// 匹配价格记录详情
export const priceColumn = [
  // {
  //   width: "70",
  //   headerText: i18n.t("序号"),
  // },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'skuCode',
    headerText: i18n.t('SKU编号')
  },
  {
    width: '150',
    field: 'skuName',
    headerText: i18n.t('SKU名称')
  },
  {
    width: '150',
    field: 'spec',
    headerText: i18n.t('规格型号')
  },
  {
    width: '150',
    field: 'categoryCode',
    headerText: i18n.t('品类编号')
  },
  {
    width: '150',
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    width: '150',
    field: 'contractCode',
    headerText: i18n.t('合同协议编号')
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编号')
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  // {
  //   width: "150",
  //   field: "",
  //   headerText: i18n.t("未税单价"),
  // },
  {
    width: '150',
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  // {
  //   width: "150",
  //   field: "",
  //   headerText: i18n.t("含税单价"),
  // },
  // {
  //   width: "150",
  //   field: "",
  //   headerText: i18n.t("折扣率"),
  // },
  {
    width: '150',
    field: '',
    headerText: i18n.t('阶梯价格'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `
            <mt-button :isPrimary="true" @click="handleViewStage">{{i18n.t("查看")}}</mt-button>
          `,
          data() {
            return { data: { i18n } }
          },
          methods: {
            handleViewStage() {
              this.$parent.$emit('handleViewStage', this.data.stageList)
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'validStartTime',
    headerText: i18n.t('有效期起')
  },
  {
    width: '150',
    field: 'validEndTime',
    headerText: i18n.t('有效期至')
  }
]

export const costSharingAccName = [
  {
    width: '180',
    field: 'costSharingAccName',
    headerText: i18n.t('成本中心'),
    template: () => {
      return {
        template: Vue.component('cost', {
          template: `<div>{{showCost}}</div>`,
          data() {
            return {
              data: {},
              showCost: ''
            }
          },
          mounted() {
            this.showCost = ''
            if (!this.data.costSharingResponses || !this.data.costSharingResponses.length) return
            this.data.costSharingResponses.forEach((item) => {
              this.showCost += item.costSharingAccName + '-' + item.appProportion + '%;'
            })
          }
        })
      }
    }
  }
]
export const supplierCode = [
  {
    width: '180',
    field: 'supplierCode',
    headerText: i18n.t('推荐供应商代码'),
    template: () => {
      return {
        template: Vue.component('cost', {
          template: `<div>{{code}}</div>`,
          data() {
            return {
              data: {},
              code: ''
            }
          },
          mounted() {
            this.code = ''
            if (!this.data.supplierResponses || !this.data.supplierResponses.length) {
              return
            } else {
              this.code = this.data.supplierResponses[0].supplierCode
            }
          }
        })
      }
    }
  }
]
export const supplierName = [
  {
    width: '180',
    field: 'supplierName',
    headerText: i18n.t('推荐供应商名称'),
    template: () => {
      return {
        template: Vue.component('cost', {
          template: `<div>{{name}}</div>`,
          data() {
            return {
              data: {},
              name: ''
            }
          },
          mounted() {
            this.name = ''
            if (!this.data.supplierResponses || !this.data.supplierResponses.length) {
              return
            } else {
              this.name = this.data.supplierResponses[0].supplierName
            }
          }
        })
      }
    }
  }
]
