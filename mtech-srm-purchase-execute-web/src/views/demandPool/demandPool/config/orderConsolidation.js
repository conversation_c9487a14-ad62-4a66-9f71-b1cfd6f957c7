import Vue from 'vue'
import { i18n } from '@/main.js'

// 采购申请转订单/手工创建采购订单并单配置
export const purchaseColumnData = [
  {
    width: '70',
    field: 'serialNumber',
    headerText: i18n.t('序号')
  },
  {
    width: '180',
    field: 'businessType',
    headerText: i18n.t('业务类型')
  },
  {
    width: '100',
    field: 'rule1',
    headerText: i18n.t('规则1-公司'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><MtIcon v-if="data.rule1.ruleValue" name="icon_V" /><MtIcon v-if="!data.rule1.ruleValue" name="icon_X" /></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'rule2',
    headerText: i18n.t('规则2-供应商'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><MtIcon v-if="data.rule2.ruleValue" name="icon_V" /><MtIcon v-if="!data.rule2.ruleValue" name="icon_X" /></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'rule3',
    headerText: i18n.t('规则3-采购组织'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><MtIcon v-if="data.rule3.ruleValue" name="icon_V" /><MtIcon v-if="!data.rule3.ruleValue" name="icon_X" /></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'rule4',
    headerText: i18n.t('规则4-来源'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><MtIcon v-if="data.rule4.ruleValue" name="icon_V" /><MtIcon v-if="!data.rule4.ruleValue" name="icon_X" /></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'rule5',
    headerText: i18n.t('规则5-申请编号'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><MtIcon v-if="data.rule5.ruleValue" name="icon_V" /><MtIcon v-if="!data.rule5.ruleValue" name="icon_X" /></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'rule6',
    headerText: i18n.t('规则6-收货地址'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><MtIcon v-if="data.rule6.ruleValue" name="icon_V" /><MtIcon v-if="!data.rule6.ruleValue" name="icon_X" /></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    width: '220',
    field: 'rule7',
    headerText: i18n.t('规则9-需求时间范围'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div v-if="data.rule7.ruleValue"><MtIcon name="icon_V" /><span style="margin-left:20px;">间隔天数：{{data.rule7.timeLimit}}</span></div>
                    <div v-else-if="!data.rule7.ruleValue"><MtIcon  name="icon_X" /></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  }
]

// 寻源需求并单配置
export const searchSourceColumnData = [
  {
    width: '150',
    field: 'serialNumber',
    headerText: i18n.t('序号')
  },
  {
    width: '150',
    field: 'businessType',
    headerText: i18n.t('业务类型')
  },
  {
    width: '150',
    field: 'rule1',
    headerText: i18n.t('规则1-采购组织'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><MtIcon v-if="data.rule1.ruleValue" name="icon_V" /><MtIcon v-if="!data.rule1.ruleValue" name="icon_X" /></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'rule2',
    headerText: i18n.t('规则2-来源'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><MtIcon v-if="data.rule2.ruleValue" name="icon_V" /><MtIcon v-if="!data.rule2.ruleValue" name="icon_X" /></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'rule3',
    headerText: i18n.t('规则3-申请编号'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><MtIcon v-if="data.rule3.ruleValue" name="icon_V" /><MtIcon v-if="!data.rule3.ruleValue" name="icon_X" /></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'rule4',
    headerText: i18n.t('规则4-公司'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><MtIcon v-if="data.rule4.ruleValue" name="icon_V" /><MtIcon v-if="!data.rule4.ruleValue" name="icon_X" /></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'rule5',
    headerText: i18n.t('规则5-工厂'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><MtIcon v-if="data.rule5.ruleValue" name="icon_V" /><MtIcon v-if="!data.rule5.ruleValue" name="icon_X" /></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'rule6',
    headerText: i18n.t('规则6-采购组'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><MtIcon v-if="data.rule6.ruleValue" name="icon_V" /><MtIcon v-if="!data.rule6.ruleValue" name="icon_X" /></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    width: '220',
    field: 'rule7',
    headerText: i18n.t('规则7-需求时间范围'),
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div v-if="data.rule7.ruleValue"><MtIcon name="icon_V" /><span style="margin-left:20px;">{{$t("间隔天数：")}}{{data.rule7.timeLimit}}</span></div>
                    <div v-else-if="!data.rule7.ruleValue"><MtIcon  name="icon_X" /></div>`,
          data() {
            return { data: {} }
          }
        })
      }
    }
  }
]

export const errorColumns = [
  {
    width: '120',
    field: 'docNo',
    headerText: i18n.t('单据编号')
  },
  {
    width: '70',
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '300',
    field: 'errorMsg',
    headerText: i18n.t('错误类型')
  }
]
