import { i18n } from '@/main.js'
import Vue from 'vue'

// 页面内容是否可编辑
export let editFlag = {
  isEditable: false
}

// 业务类型list
export let businessTypeList = []

// 采购申请 用于提交的特殊配置
export let orderApplyStrategySpecData = []

// 采购申请 用于页面展示的特殊配置
export let orderApplyStrategyDataSource = []

// 采购申请 默认配置
export let orderApplyStrategyDefaultData = [
  {
    id: '',
    businessTypeId: '0',
    businessTypeCode: '0',
    businessTypeName: '0',
    strategyConfig: '',
    strategyConfigMap: {
      autoReleaseFlag: 0,
      autoCancelFlag: 0,
      purExecutionMethod: null,
      deliverFlag: 0,
      autoSourcingFlag: 0,
      isSplitFlag: 0
    },
    commonFlag: 1
  }
]

export const editToolbar = [
  {
    id: 'Add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增')
  },
  {
    id: 'Delete',
    icon: 'icon_solid_Delete',
    title: i18n.t('删除')
  }
]

// 采购申请配置
export const orderApplyColumn = [
  {
    width: '230',
    field: 'serialNumber',
    headerText: i18n.t('序号'),
    cellTools: [
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: () => editFlag.isEditable
      }
    ]
  },
  {
    width: '210',
    field: 'businessTypeName',
    headerText: i18n.t('业务类型'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-select
                :width="140"
                :data-source="businessTypeList"
                :placeholder="$t('请选择')"
                @change="businessTypeChange"
                @focus="getBusinessConfig"
                :disabled="!isEditable"
                css-class="input-select"
                :fields="{ text: 'businessTypeName', value: 'businessTypeId' }"
                v-model="data.businessTypeId"
              ></mt-select>
          </div>`,
          data() {
            return { data: {}, businessTypeList: [], isEditable: null }
          },
          mounted() {
            this.businessTypeList = businessTypeList
            this.isEditable = editFlag.isEditable
          },
          methods: {
            // 获取业务类型下拉
            getBusinessConfig() {
              this.$API.bgConfig.getLocalBusinessTypeList({}).then((res) => {
                this.businessTypeList.length = 0
                res.data.forEach((e) => {
                  this.businessTypeList.push(e)
                })
              })
            },
            businessTypeChange(e) {
              orderApplyStrategySpecData[this.data.index].businessTypeCode =
                e.itemData.businessTypeCode
              orderApplyStrategySpecData[this.data.index].businessTypeId = e.itemData.businessTypeId
              orderApplyStrategySpecData[this.data.index].businessTypeName =
                e.itemData.businessTypeName
              // orderApplyStrategyDataSource[this.data.index].businessTypeId =
              //   e.value;
            }
          }
        })
      }
    }
  },
  {
    // width: "280",
    field: 'isSplitFlag',
    headerText: i18n.t('采购申请行拆成每行一个单据'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="data.strategyConfigMap.isSplitFlag  == 1 ? true : false"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          methods: {
            handleChangeCellCheckBox(e) {
              this.data.isSplitFlag = e.checked
              orderApplyStrategySpecData[this.data.index].strategyConfigMap.isSplitFlag = e.checked
                ? 1
                : 0
              // orderApplyStrategyDataSource[
              //   this.data.index
              // ].strategyConfigMap.isSplitFlag = e.checked ? 1 : 0;
            }
          }
        })
      }
    }
  },
  {
    // width: "350",
    field: 'autoSourcingFlag',
    headerText: i18n.t('无价格自动转寻源（按照采购申请单转寻源订单）'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
          <mt-checkbox v-model="data.strategyConfigMap.autoSourcingFlag  == 1 ? true : false"
          :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
          @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {}, isEditable: null }
          },
          mounted() {
            this.isEditable = editFlag.isEditable
          },
          methods: {
            handleChangeCellCheckBox(e) {
              this.data.autoSourcingFlag = e.checked
              orderApplyStrategySpecData[this.data.index].strategyConfigMap.autoSourcingFlag =
                e.checked ? 1 : 0
              // orderApplyStrategyDataSource[
              //   this.data.index
              // ].strategyConfigMap.autoSourcingFlag = e.checked ? 1 : 0;
            }
          }
        })
      }
    }
  }
]
