<template>
  <mt-dialog ref="dialog" :header="dialogTitle" :buttons="buttons" :open="onOpen">
    <mt-form ref="ruleForm" :model="addForm" :rules="rules" style="padding-top: 20px">
      <mt-form-item prop="type" :label="$t('物流类型')">
        <mt-select
          v-model.trim="addForm.type"
          :data-source="typeList"
          :show-clear-button="true"
          :fields="{ text: 'label', value: 'value' }"
          :placeholder="$t('请选择物流类型')"
          @change="typeChange"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="templateCode" :label="$t('模板编码')">
        <mt-input
          v-model.trim="addForm.templateCode"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入模板编码')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="templateName" :label="$t('模板名称')">
        <mt-input
          v-model.trim="addForm.templateName"
          :show-clear-button="true"
          type="text"
          :placeholder="$t('请输入模板名称')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        templateCode: '',
        templateName: ''
      },
      typeList: [
        { label: '海运', value: 'sea' },
        { label: '铁运', value: 'raiway' },
        { label: '干线', value: 'trunk' }
      ],
      rules: {
        type: [
          {
            required: true,
            message: this.$t('请选择物流类型'),
            trigger: 'blur'
          }
        ],
        templateCode: [
          {
            required: true,
            message: this.$t('请输入模板编码'),
            trigger: 'blur'
          }
        ],
        templateName: [
          {
            required: true,
            message: this.$t('请输入模板名称'),
            trigger: 'blur'
          }
        ]
      }
    }
  },

  mounted() {
    this.cacheAddForm = {
      ...this.addForm
    }
  },

  methods: {
    dialogInit(args) {
      const { dialogType, rowData } = args
      if (dialogType == 'add') {
        this.addForm = {
          ...this.cacheAddForm
        }
        this.dialogTitle = this.$t('新增')
      } else {
        this.dialogTitle = this.$t('编辑')
        const { id, templateCode, templateName, type, typeName } = rowData
        this.addForm = {
          ...this.addForm,
          ...{ id, templateCode, templateName, type, typeName }
        }
      }
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    typeChange(e) {
      this.$set(this.addForm, 'typeName', e.itemData.label)
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            ...this.addForm
          }
          this.$API.bgConfig.logisticsTempalteUpdate(params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$refs.dialog.ejsRef.hide()
              this.$emit('confirmSuccess')
            }
          })
        }
      })
    }
  }
}
</script>
