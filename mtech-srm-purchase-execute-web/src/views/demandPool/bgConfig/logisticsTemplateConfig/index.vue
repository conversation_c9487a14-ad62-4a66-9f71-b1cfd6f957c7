<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <add-dialog ref="addDialog" @confirmSuccess="confirmSuccess"></add-dialog>
  </div>
</template>
<script>
import { columnData } from './config/index.js'
import AddDialog from './component/addDialog.vue'
export default {
  components: {
    AddDialog
  },
  data() {
    return {
      pageConfig: [
        {
          title: this.$t('汇总列表'),
          useToolTemplate: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: {
            tools: [
              [
                'Add',
                'Delete',
                {
                  id: 'Copy',
                  icon: 'icon_solid_Import',
                  title: this.$t('复制')
                }
              ],
              ['Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columnData,
            lineIndex: 1,
            dataSource: [],
            asyncConfig: {
              url: '/contract/tenant/request/config/template/list'
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },

  methods: {
    handleClickToolBar(e) {
      let _checkedRecords = e.grid.getSelectedRecords()

      if (_checkedRecords.length <= 0 && ['Delete', 'Copy'].includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (_checkedRecords.length > 1 && ['Copy'].includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
        return
      }
      let _ids = []
      _checkedRecords.map((item) => _ids.push(item.id))
      if (e.toolbar.id === 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id === 'Delete') {
        this.handleDelete(_ids)
      } else if (e.toolbar.id === 'Copy') {
        this.handleCopy(_ids)
      }
    },
    handleClickCellTool(e) {
      let _row = e.data
      if (e.tool.id == 'config') {
        localStorage.setItem(
          'templateRow',
          JSON.stringify({ templateCode: e.data.templateCode, templateName: e.data.templateName })
        )
        this.$router.push({ name: 'logistics-template-detail', query: { tid: e.data.id } })
      } else if (e.tool.id == 'edit') {
        delete _row.cellTools
        this.handleEdit(_row)
      } else if (e.tool.id == 'enable') {
        this.handleUpdateStatus([e.data.id], 1, this.$t('确认启用？'))
      } else if (e.tool.id == 'disable') {
        this.handleUpdateStatus([e.data.id], 0, this.$t('确认禁用用？'))
      }
    },
    handleClickCellTitle(e) {
      if (e.field === 'templateCode') {
        localStorage.setItem(
          'templateRow',
          JSON.stringify({ templateCode: e.data.templateCode, templateName: e.data.templateName })
        )
        this.$router.push({
          name: 'logistics-template-detail',
          query: { tid: e.data.id, type: 'view' }
        })
      }
    },
    handleAdd() {
      const params = {
        dialogType: 'add'
      }
      this.$refs.addDialog.dialogInit(params)
    },
    // 删除模板
    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$loading()
          this.$API.bgConfig
            .logisticsTempalteDelete({ ids })
            .then((res) => {
              this.$hloading()
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.confirmSuccess()
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    // 复制模板
    handleCopy(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认复制选中模板？')
        },
        success: () => {
          this.$loading()
          this.$API.bgConfig
            .logisticsTempalteCopy({ templateId: ids[0] })
            .then((res) => {
              this.$hloading()
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.confirmSuccess()
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    handleEdit(row) {
      const params = {
        dialogType: 'edit',
        rowData: row
      }
      this.$refs.addDialog.dialogInit(params)
    },
    handleUpdateStatus(ids, flag, msg) {
      this.$dialog({
        data: {
          title: this.$t('操作'),
          message: msg
        },
        success: () => {
          this.$loading()
          this.$API.bgConfig
            .logisticsTempalteSateUpdate({ id: ids, status: flag })
            .then((res) => {
              this.$hloading()
              if (res.code == 200) {
                this.confirmSuccess()
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
