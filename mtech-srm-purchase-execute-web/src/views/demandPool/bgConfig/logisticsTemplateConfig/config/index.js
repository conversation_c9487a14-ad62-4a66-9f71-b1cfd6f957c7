import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'templateCode',
    headerText: i18n.t('模板编码'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        visibleCondition: (data) => data.status !== 1
      },
      {
        id: 'config',
        icon: 'icon_list_deploy',
        title: i18n.t('配置'),
        visibleCondition: (data) => data.status !== 1
      }
    ]
  },
  {
    field: 'type',
    headerText: i18n.t('物流类型')
  },
  {
    field: 'typeName',
    headerText: i18n.t('物流名称')
  },
  {
    field: 'templateName',
    headerText: i18n.t('模板名称')
  },
  {
    field: 'status',
    headerText: i18n.t('启用状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('停用'), cssClass: 'col-inactive' },
        { value: 1, text: i18n.t('启用'), cssClass: 'col-active' }
      ]
    },
    cellTools: [
      {
        id: 'enable',
        icon: 'icon_solid_Createorder',
        title: i18n.t('启用'),
        visibleCondition: (data) => data.status == 0
      },
      {
        id: 'disable',
        icon: 'icon_solid_Createorder',
        title: i18n.t('禁用'),
        visibleCondition: (data) => data.status == 1
      }
    ]
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('修改人')
  },
  {
    field: 'updateTime',
    headerText: i18n.t('修改时间'),
    type: 'date',
    format: 'yyyy-MM-dd'
  }
]
