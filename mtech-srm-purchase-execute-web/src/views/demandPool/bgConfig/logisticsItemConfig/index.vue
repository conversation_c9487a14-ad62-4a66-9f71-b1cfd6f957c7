<template>
  <div class="logistics-wrapper">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>

    <add-dialog
      ref="addDialog"
      :dialog-data="dialogData"
      @confirmSuccess="confirmSuccess"
    ></add-dialog>
    <add-detail ref="addDetail" @confirmSuccess="confirmSuccess"></add-detail>
  </div>
</template>

<script>
import { columnData } from './config'
import addDialog from './component/addDialog.vue'
import addDetail from './component/addDetail.vue'
export default {
  components: {
    addDialog,
    addDetail
  },
  data() {
    return {
      addDetailKey: 1,
      currentTabIndex: 0,
      pageConfig: [
        {
          toolbar: {
            tools: []
          },
          grid: {
            columnData: columnData,
            dataSource: [],
            frozenColumns: 1,
            asyncConfig: {
              url: '/contract/tenant/request/config/field/list'
            }
          }
        }
      ],
      dialogData: null,
      showDefault: false,
      rowInfo: null,
      asyncSettings: {
        saveUrl: ``
      }
    }
  },
  created() {
    this.setTools()
  },
  methods: {
    setTools() {
      let tools = ['Add', 'Delete']
      this.pageConfig[this.currentTabIndex].toolbar.tools.push(tools)
      this.pageConfig[this.currentTabIndex].toolbar.tools.push(['Filter', 'Refresh', 'Setting'])
    },
    handleClickToolBar(e) {
      let _checkedRecords = e.grid.getSelectedRecords()

      if (_checkedRecords.length <= 0 && e.toolbar.id == 'Delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _ids = []
      _checkedRecords.map((item) => _ids.push(item.id))
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(_ids)
      }
    },
    handleClickCellTool(e) {
      let _row = e.data
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      } else if (e.tool.id == 'edit') {
        delete _row.cellTools
        this.handleEdit(_row)
      } else if (e.tool.id == 'enable') {
        this.handleUpdateStatus([e.data.id], 1, this.$t('确认启用？'))
      } else if (e.tool.id == 'disable') {
        this.handleUpdateStatus([e.data.id], 0, this.$t('确认禁用用？'))
      } else if (e.tool.id == 'AddStepList') {
        this.handleAddStepList(_row)
      }
    },
    handleAddStepList(row) {
      this.$refs.addDetail.initSidebar(row)
    },

    handleAdd() {
      const params = {
        dialogType: 'add'
      }
      this.$refs.addDialog.dialogInit(params)
    },
    handleEdit(row) {
      const params = {
        dialogType: 'edit',
        rowData: row
      }
      this.$refs.addDialog.dialogInit(params)
    },

    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$loading()
          this.$API.bgConfig
            .logisticsFieldDelete({ ids })
            .then((res) => {
              this.$hloading()
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.confirmSuccess()
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },

    handleUpdateStatus(ids, flag, msg) {
      this.$dialog({
        data: {
          title: this.$t('操作'),
          message: msg
        },
        success: () => {
          this.$loading()
          this.$API.bgConfig
            .logisticsFieldSateUpdate({ id: ids, status: flag })
            .then((res) => {
              this.$hloading()
              if (res.code == 200) {
                this.confirmSuccess()
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },

    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    }
  }
}
</script>

<style lang="scss">
.side-detail {
  width: 80% !important;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .info {
    width: 100%;
    ul {
      display: flex;
      li {
        flex: 1;
        display: flex;
      }
    }
  }

  .logistics-wrapper {
    flex: 1;
  }
}
</style>
