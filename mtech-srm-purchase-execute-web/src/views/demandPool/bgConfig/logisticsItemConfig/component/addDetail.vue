<template>
  <mt-side-bar
    v-if="showSideBar"
    position="Right"
    ref="sidebar"
    :show-backdrop="true"
    :close-on-document-click="false"
    class="side-detail"
  >
    <div class="right-btn">
      <div class="left-title">{{ $t('阶梯明细') }}</div>
      <mt-icon name="icon_Close_1" @click.native="handleClose"></mt-icon>
    </div>
    <div class="blue-title mt0">{{ $t('阶梯明细列表') }}</div>
    <step-detail @updateSuccess="updateSuccess" ref="stepDetail" :row-info="rowInfo"></step-detail>
  </mt-side-bar>
</template>

<script>
import MtSideBar from '@mtech-ui/side-bar'
import StepDetail from './stepDetail/index.vue'
export default {
  components: {
    MtSideBar,
    StepDetail
  },
  props: {},
  data() {
    return {
      dictionKey: 1,
      isDetailShow: false,
      rowInfo: {},
      showSideBar: false
    }
  },
  mounted() {},
  methods: {
    initSidebar(row) {
      this.showSideBar = true
      this.rowInfo = { ...row }
    },
    handleClose() {
      this.showSideBar = false
    },
    updateSuccess() {
      this.$emit('confirmSuccess')
    }
  }
}
</script>

<style lang="scss" scoped>
.side-detail {
  width: 80% !important;
  top: 60px;
  height: calc(100% - 60px);
  overflow: auto;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px 0 0 0;

  .right-btn {
    width: 100%;
    font-size: 16px;
    line-height: 1;
    padding: 14px 20px;
    background: rgba(243, 243, 243, 1);
    color: #292929;
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      cursor: pointer;
    }
    .mt-icons {
      font-size: 14px;
      cursor: pointer;
    }
  }

  .info {
    width: 100%;
    padding: 0 40px;
    font-size: 15px;
    ul {
      display: flex;
      li {
        flex: 1;
        display: flex;
        .left-label {
          font-weight: bold;
        }
      }
    }
  }

  .set-country {
    flex: 1;
    .common-template-page {
      background: transparent;

      .page-grid-container {
        box-shadow: unset;
      }

      .e-grid {
        flex: 1;
        height: auto !important;
      }
    }
  }

  .blue-title {
    font-size: 14px;
    padding-left: 8px;
    margin: 20px;
    position: relative;
    color: #2f353c;
    &::before {
      content: '';
      width: 3px;
      height: 70%;
      position: absolute;
      left: 0;
      top: 15%;
      background: rgba(0, 70, 156, 1);
      border-radius: 5px 0 0 5px;
    }

    &.mt0 {
      margin-bottom: 0;
    }
  }
  .footer {
    .btn {
      width: 100px;
      height: 28px;
      margin: 12px 20px 12px auto;
      line-height: 26px;
      text-align: center;
      border-radius: 4px;
      font-weight: 400;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      border: 1px solid #4a556b;
      color: #4a556b;
      white-space: nowrap;
      box-sizing: border-box;
      cursor: pointer;
      background: #4a556b;
      border: 1px solid #4a556b;
      color: #fff;
    }
  }
}
</style>
