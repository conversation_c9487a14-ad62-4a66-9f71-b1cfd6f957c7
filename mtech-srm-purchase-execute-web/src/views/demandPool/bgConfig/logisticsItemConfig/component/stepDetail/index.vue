<template>
  <div class="stepDetail">
    <mt-template-page
      ref="templateDetailRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    ></mt-template-page>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { columnData } from './index.js'
export default {
  props: {
    rowInfo: {
      type: Object,
      default: () => {}
    }
  },
  components: {},
  data() {
    return {
      dataSource: [],
      currentTabIndex: 0,
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: {
            useBaseConfig: false,
            tools: [['Add', 'Delete']]
          },
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false,
            columnData: columnData,
            dataSource: []
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },

  computed: {},
  created() {},
  mounted() {
    const stepList = this.rowInfo?.stepList?.length ? cloneDeep(this.rowInfo?.stepList) : []
    this.$set(this.pageConfig[0].grid, 'dataSource', stepList)
  },

  methods: {
    handleAdd() {
      this.$refs.templateDetailRef.getCurrentUsefulRef().gridRef?.ejsRef.addRecord()
    },
    // 组件事件开始监听
    actionBegin(args) {
      const { requestType, data } = args
      if (requestType === 'save') {
        let flag = this.validRowData(data)
        if (!flag) args.cancel = true
      }
    },
    // 组件事件结束监听
    actionComplete(args) {
      const { requestType, rowIndex } = args
      if (requestType === 'save') {
        this.saveData(rowIndex)
      }
    },
    // 保存行数据
    saveData(rowIndex = 0) {
      const stepList = this.getStepList()
      const rowInfo = { ...this.rowInfo, stepList }
      this.$API.bgConfig
        .logisticsFieldUpdate(rowInfo)
        .then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('updateSuccess')
          } else {
            this.$toast({ content: res.msg, type: 'warning' })
            this.$refs.templateDetailRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
            this.$refs.templateDetailRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          }
        })
        .catch((err) => {
          this.$toast({ content: err.msg, type: 'warning' })
          this.$refs.templateDetailRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateDetailRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    // 校验行数据
    validRowData(data) {
      let flag = true
      const validateMap = {
        fieldCode: {
          value: data.stepCode,
          msg: this.$t('阶梯编码不能为空')
        },
        fieldName: {
          value: data.stepName,
          msg: this.$t('阶梯名称不能为空')
        }
      }
      for (const key in validateMap) {
        if (Object.hasOwnProperty.call(validateMap, key)) {
          const element = validateMap[key]
          if (!element.value && element.value !== 0) {
            this.$toast({ content: element.msg, type: 'warning' })
            flag = false
            break
          }
        }
      }
      return flag
    },
    handleClickToolBar(e) {
      let _checkedRecords = e.grid.getSelectedRecords()
      if (_checkedRecords.length <= 0 && e.toolbar.id == 'Delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _ids = []
      _checkedRecords.map((item) => _ids.push(item.id))
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(_checkedRecords)
      }
    },

    handleDelete(_checkedRecords) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确定删除所选数据？')
        },
        success: () => {
          this.$refs.templateDetailRef
            .getCurrentUsefulRef()
            .gridRef?.ejsRef.deleteRecord(_checkedRecords)
          setTimeout(() => {
            this.saveData()
          }, 100)
        }
      })
    },

    getStepList() {
      return this.$refs.templateDetailRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
    }
  }
}
</script>

<style lang="scss">
/deep/ .grid-edit-column {
  padding: 0 !important;
}
.codeStyle {
  padding-left: 30px;
}
</style>
