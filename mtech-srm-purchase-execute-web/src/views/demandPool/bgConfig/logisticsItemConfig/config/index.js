import { i18n } from '@/main.js'

export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '70',
    field: 'seqNo',
    headerText: i18n.t('序号')
  },
  {
    width: '70',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('禁用'),
        1: i18n.t('启用')
      }
    },
    cellTools: [
      {
        id: 'enable',
        icon: 'icon_solid_Createorder',
        title: i18n.t('启用'),
        visibleCondition: (data) => data.status == 0
      },
      {
        id: 'disable',
        icon: 'icon_solid_Createorder',
        title: i18n.t('禁用'),
        visibleCondition: (data) => data.status == 1
      }
    ]
  },
  {
    width: '200',
    field: 'fieldCode',
    headerText: i18n.t('字段Code'),
    cellTools: [
      'Edit',
      'Delete'
      // {
      //   id: 'AddStepList',
      //   icon: 'icon_solid_Createorder',
      //   title: i18n.t('阶梯明细'),
      //   visibleCondition: (data) => data.fieldCode == 'step'
      // }
    ]
  },
  {
    width: '100',
    field: 'fieldName',
    headerText: i18n.t('字段名称')
  },
  {
    width: '100',
    field: 'groupCode',
    headerText: i18n.t('分组编码')
  },
  {
    width: '150',
    field: 'groupName',
    headerText: i18n.t('分组名称')
  },
  {
    width: '100',
    field: 'displayType',
    headerText: i18n.t('展示类型'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('正常'),
        2: i18n.t('向下展示'),
        3: i18n.t('平铺展示')
      }
    }
  },
  {
    width: '100',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '180',
    field: 'updateTime',
    headerText: i18n.t('更新时间')
  }
]
