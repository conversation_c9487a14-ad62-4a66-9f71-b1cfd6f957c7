<!-- 业务字段明细配置 -->
<template>
  <div class="notice-hander hander">
    <mt-form>
      <mt-row :gutter="20">
        <mt-col :span="6">
          <mt-form-item :label="$t('文档类型')">
            <mt-select
              v-model="searchForm.docType"
              :data-source="docTypeList"
              @change="docTypeChange"
            ></mt-select>
          </mt-form-item>
        </mt-col>
        <mt-col :span="6">
          <mt-form-item :label="$t('模块')">
            <mt-select
              v-model="searchForm.moduleId"
              :fields="{ text: 'moduleName', value: 'moduleId' }"
              :data-source="moduleList"
              @change="moduleChange"
            ></mt-select>
          </mt-form-item>
        </mt-col>
        <mt-col :span="6">
          <mt-form-item :label="$t('字段组')">
            <mt-select
              v-model="searchForm.fieldGroup"
              :data-source="fieldGroupList"
              :fields="{ text: 'fieldGroup', value: 'fieldGroup' }"
              @change="fieldGroupChange"
            ></mt-select>
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @actionComplete="actionComplete"
      @actionBegin="actionBegin"
    />
  </div>
</template>
<script>
import { pageConfig } from './config'
import { i18n } from '@/main.js'
import { cloneDeep } from 'lodash'

export default {
  data() {
    return {
      i18n,
      pageConfig: null,
      searchForm: {
        docType: '',
        moduleId: '',
        fieldGroup: ''
      },
      moduleKey: '',
      docTypeList: [
        { text: '采购申请', value: 'pr' },
        { text: '采方订单', value: 'po' },
        { text: '供方订单', value: 'so' },
        { text: '采方售后订单', value: 'as_po' },
        { text: '供方售后订单', value: 'as_so' },
        { text: '收货协同明细', value: 're' },
        { text: '寻源申请', value: 'sr' }
      ], //DOCTYPE下拉列表
      moduleList: [], // 模块下拉列表
      fieldGroupList: [] // 字段组下拉列表
    }
  },
  mounted() {
    this.pageConfig = pageConfig(this)
  },
  methods: {
    // 表头操作
    handleClickToolBar(e) {
      let _selectGridRecords = e.grid.getSelectedRecords()
      if (_selectGridRecords.length <= 0 && ['Delete', 'Download'].includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const ids = _selectGridRecords.map((item) => item.id)
      if (e.toolbar.id === 'Add') {
        if (!this.searchForm.fieldGroup) {
          this.$toast({ content: this.$t('请先选择字段组'), type: 'warning' })
          return
        }
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (e.toolbar.id === 'Delete') {
        this.handleDelete(ids)
      } else if (e.toolbar.id === 'Import') {
        this.handleImport()
      } else if (e.toolbar.id === 'Download') {
        this.handleExport(ids)
      }
    },
    // 组件事件开始监听
    actionBegin(args) {
      const { requestType, data } = args
      if (requestType === 'save') {
        let flag = this.validRowData(data)
        if (!flag) args.cancel = true
      }
    },
    // 组件事件结束监听
    actionComplete(args) {
      const { requestType, data, rowIndex } = args
      if (requestType === 'save') {
        this.saveRowData(data, rowIndex)
      }
    },
    // 校验行数据
    validRowData(data) {
      let flag = true
      const validateMap = {
        sortValue: {
          value: data.sortValue,
          msg: this.$t('排序序号')
        },
        fieldCode: {
          value: data.fieldCode,
          msg: this.$t('字段Code')
        },
        fieldName: {
          value: data.fieldName,
          msg: this.$t('字段名称')
        }
      }
      for (const key in validateMap) {
        if (Object.hasOwnProperty.call(validateMap, key)) {
          const element = validateMap[key]
          if (!element.value && element.value !== 0) {
            this.$toast({ content: element.msg, type: 'warning' })
            flag = false
            break
          }
        }
      }
      return flag
    },
    // 保存行数据
    saveRowData(data, rowIndex = 0) {
      let params = {
        ...this.searchForm,
        ...data,
        moduleKey: this.moduleKey
      }
      this.$API.bgConfig
        .saveItemFileConfig(params)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          } else {
            this.$toast({ content: res.msg, type: 'warning' })
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
          }
        })
        .catch((err) => {
          this.$toast({ content: err.msg, type: 'warning' })
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    // 删除
    handleDelete(ids) {
      this.$API.bgConfig.deleteBaseField(ids).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 导入
    handleImport() {
      this.$dialog({
        modal: () => import('./components/importDialog.vue'),
        data: {
          title: this.$t('导入JSON')
        },
        success: (res) => {
          this.$toast({
            content: res.msg ? res.msg : this.$t('导入成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 导出
    handleExport(ids) {
      this.$API.bgConfig
        .exportBaseField(ids)
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.$dialog({
              data: {
                title: this.$t('导出JSON(点击"确认"复制到剪切板，原有内容将会被覆盖)'),
                message: data,
                cssClass: 'export-dialog'
              },
              success: () => {
                this.copyToClipboard(JSON.stringify(data))
                this.$toast({
                  content: res.message ? res.message : this.$t('复制成功'),
                  type: 'success'
                })
              }
            })
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    copyToClipboard(str) {
      const el = document.createElement('textarea')
      el.value = str
      el.setAttribute('readonly', '')
      el.style.position = 'absolute'
      el.style.left = '-9999px'
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)
    },
    // 文档类型切换
    docTypeChange(e) {
      this.getModuleListByDocType(e.value)
      this.searchForm.moduleId = ''
      this.searchForm.fieldGroup = ''
    },
    // 模块类型切换
    moduleChange(e) {
      this.getfieldGroupList(e.value)
      this.moduleKey = e.itemData?.moduleKey
    },
    // 字段组类型切换
    fieldGroupChange() {
      // 重置searchForm 达到查询的目的
      this.$set(this.pageConfig[0]['grid']['asyncConfig'], 'params', this.searchForm)
    },
    // 根据docType 获取模块下拉数据
    getModuleListByDocType(docType) {
      this.$API.bgConfig.getBaseModules({ docType: docType }).then((res) => {
        if (res.code === 200) {
          this.moduleList = cloneDeep(res.data?.modules)
        }
      })
    },
    // 根据模块 获取字段组下拉数据
    getfieldGroupList(moduleId) {
      let params = {
        docType: this.searchForm.docType,
        moduleId
      }
      this.$API.bgConfig.queryFieldGroup(params).then((res) => {
        if (res.code === 200) {
          this.fieldGroupList = cloneDeep(res.data)
        }
      })
    }
  }
}
</script>
<style>
.notice-hander .mt-select-index {
  display: inline-block;
}
</style>
<style lang="scss" scope>
.hander {
  padding-top: 15px;
  .mt-form {
    padding-left: 15px;
  }
  .e-content {
    height: calc(100vh - 298px) !important;
  }
}
</style>
