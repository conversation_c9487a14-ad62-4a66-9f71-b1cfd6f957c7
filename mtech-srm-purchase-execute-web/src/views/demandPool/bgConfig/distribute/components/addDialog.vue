<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="strategyCode" :label="$t('策略编号')">
        <mt-input
          v-model="addForm.strategyCode"
          :show-clear-button="true"
          :placeholder="$t('请输入策略编号')"
          :maxlength="50"
          :disabled="dialogData && dialogData.dialogType == 'edit'"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="strategyName" :label="$t('策略名称')">
        <mt-input
          v-model="addForm.strategyName"
          :show-clear-button="true"
          :placeholder="$t('请输入策略名称')"
          :maxlength="75"
          :disabled="dialogData && dialogData.dialogType == 'edit'"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="strategyDimension" :label="$t('策略纬度')">
        <mt-multi-select
          v-model="addForm.strategyDimension"
          :data-source="dimensionList"
          :show-clear-button="true"
          :allow-filtering="true"
          :fields="dimensionFields"
          :enable-group-check-box="true"
          mode="CheckBox"
          :placeholder="$t('请选择')"
        ></mt-multi-select>
      </mt-form-item>

      <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
        <mt-input
          v-model="addForm.remark"
          :show-clear-button="true"
          :multiline="true"
          :maxlength="200"
          :placeholder="$t('请输入备注')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
// import common from "@/utils/constant";
import utils from '@/utils/utils'
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        strategyCode: '',
        strategyName: '',
        autoDistributeType: '',
        strategyWei: [],
        strategyDimension: null,
        remark: ''
      },
      rules: {
        strategyCode: [
          {
            required: true,
            message: this.$t('请输入策略编号'),
            trigger: 'blur'
          }
        ],
        strategyName: [
          {
            required: true,
            message: this.$t('请输入策略名称'),
            trigger: 'blur'
          }
        ],
        strategyWei: [
          {
            required: true,
            message: this.$t('请选择策略纬度'),
            trigger: 'blur'
          }
        ]
      },
      dimensionFields: { text: 'message', value: 'code' },
      dimensionList: []
    }
  },
  mounted() {
    this.addForm.autoDistributeType = this.type
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      let _addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
      this.addForm = _addForm
      let _strategyWei = _addForm.strategyDimension.split(',')
      this.addForm.strategyDimension = _strategyWei.map((item) => Number(item))
    }
    this.getDimension()
    if (this.dialogData?.dialogType == 'add') {
      this.dialogTitle = this.$t('新增')
    } else {
      this.dialogTitle = this.$t('编辑')
    }
    this.getRules()
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    getDimension() {
      this.$API.moduleConfig.getDimension().then((res) => {
        console.log(res)
        this.dimensionList = res.data || []
      })
    },

    handleCurrencyChange(val, keys) {
      // console.log(this.$t("改变后的值"), val, keys);
      let _currencyAll = JSON.parse(JSON.stringify(this.currencyAll))
      if (val?.value) {
        _currencyAll = _currencyAll.filter((item) => item.id != val.value)
        // console.log(_currencyAll);
      }
      this[keys] = _currencyAll
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          console.log(this.addForm)
          let _request = this.dialogData?.requestUrl
          console.log(this.dialogData, _request)
          let params = this.addForm
          params.strategyDimension = this.addForm.strategyDimension.join(',')
          this.$API.moduleConfig[_request](params)
            .then((res) => {
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('handleAddDialogShow', false)
                this.$emit('confirmSuccess')
              }
            })
            .catch(() => {
              this.$emit('handleAddDialogShow', false)
            })
        }
      })
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },

    getRules() {
      if (this.dialogData?.dialogType == 'add') {
        this.$API.moduleConfig.addDimensionValid().then((res) => {
          if (res.code == 200) this.rules = utils.formatRules(res.data)
          this.rules.effectiveTime = [
            {
              required: true,
              message: this.$t('请选择有效时间'),
              trigger: 'blur'
            }
          ]
        })
      } else {
        this.$API.moduleConfig.updateDimensionValid().then((res) => {
          if (res.code == 200) this.rules = utils.formatRules(res.data)
          this.rules.effectiveTime = [
            {
              required: true,
              message: this.$t('请选择有效时间'),
              trigger: 'blur'
            }
          ]
        })
      }
    }
  }
}
</script>

<style></style>
