<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="companyId" :label="$t('公司')" v-if="visibleList.includes($t('公司'))">
        <!-- <mt-DropDownTree
          ref="companyRef"
          v-model="addForm.companyId"
          id="companyTree"
          :popup-height="400"
          :fields="masterData.companyData"
          :allow-filtering="true"
          :show-clear-button="true"
          :placeholder="$t('请选择公司')"
        ></mt-DropDownTree> -->
        <select-filter
          :width="390"
          ref="companyRef"
          :fields="companyObj.fields"
          :request-url="companyObj.requestUrl"
          :request-key="companyObj.requestKey"
          :init-val.sync="addForm.companyId"
          :other-params="companyObj.otherParams"
          :label-show-obj="companyObj.labelShowObj"
          :placeholder="$t('请选择公司')"
          @handleChange="handleCompanyChange"
        ></select-filter>
      </mt-form-item>

      <mt-form-item prop="categoryId" :label="$t('品类')" v-if="visibleList.includes($t('品类'))">
        <mt-select
          ref="categoryRef"
          v-model="addForm.categoryId"
          :popup-height="400"
          :fields="categoryField"
          :data-source="masterData.categoryData"
          :allow-filtering="true"
          :show-clear-button="true"
          :placeholder="$t('请选择品类')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="itemId" :label="$t('物料')" v-if="visibleList.includes($t('物料'))">
        <mt-select
          ref="itemRef"
          v-model="addForm.itemId"
          :popup-height="400"
          :fields="categoryItemField"
          :data-source="masterData.categoryItemData"
          :allow-filtering="true"
          :show-clear-button="true"
          :placeholder="$t('请选择物料')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="siteId" :label="$t('工厂')" v-if="visibleList.includes($t('工厂'))">
        <!-- <mt-select
          ref="siteRef"
          v-model="addForm.siteId"
          :fields="siteField"
          :data-source="masterData.siteData"
          :allow-filtering="true"
          :show-clear-button="true"
          :placeholder="$t('请选择工厂')"
        ></mt-select> -->
        <select-filter
          ref="siteRef"
          v-model="addForm.siteId"
          :width="390"
          :fields="siteChose.fields"
          :request-url="siteChose.requestUrl"
          :request-key="siteChose.requestKey"
          :init-val.sync="addForm.siteId"
          :other-params="siteChose.otherParams"
          :label-show-obj="siteChose.labelShowObj"
          :placeholder="$t('请选择工厂')"
          @handleChange="handleSiteChange"
        ></select-filter>
      </mt-form-item>

      <!-- <mt-form-item
        prop="buyerOrgId"
        :label="$t('采购组织')"
        v-if="visibleList.includes('采购组织')"
      >
        <mt-select
          ref="buyerOrgRef"
          v-model="addForm.buyerOrgId"
          :fields="busOrgFields"
          :data-source="masterData.organizationData"
          :allow-filtering="true"
          :show-clear-button="true"
          :placeholder="$t('请选择采购组织')"
        ></mt-select>
      </mt-form-item> -->

      <mt-form-item prop="distributeType" :label="$t('分配方式')">
        <mt-select
          ref="distributeTypeRef"
          v-model="addForm.distributeType"
          :data-source="dataArr"
          :allow-filtering="true"
          :show-clear-button="true"
          :placeholder="$t('请选择分配方式')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="distributeUserId" :label="$t('分配给')">
        <debounce-filter-select
          ref="userRef"
          v-model="addForm.distributeUserId"
          :request="getUser"
          :data-source="applyUserIdData"
          :show-clear-button="false"
          :fields="{ text: 'text', value: 'employeeId' }"
          :placeholder="$t('请选择分配人员')"
          @change="handleChange"
        ></debounce-filter-select>
        <!-- 因为树形的下拉，搜索时样式会错乱，所以改用上方的平铺下拉 -->
        <!-- <mt-DropDownTree
          ref="distributeUserRef"
          v-model="addForm.distributeUserId"
          id="organizationByUserTree"
          :popup-height="400"
          :fields="organizateByUser"
          :allow-filtering="true"
          :show-clear-button="true"
          :filtering="getTree"
          :placeholder="$t('请选择分配人员')"
        ></mt-DropDownTree> -->
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
// import common from "@/utils/constant";
// import { formatDate, formatRules } from "@/utils/util";

import { utils } from '@mtech-common/utils'
export default {
  components: {
    debounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    },
    visibleList: {
      type: Array,
      default: () => []
    },
    masterData: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        companyId: '', // 公司
        companyCode: '', // 公司
        companyName: '', // 公司
        categoryId: '', // 品类
        itemId: '', // 物料
        siteId: null, // 工厂
        siteCode: null,
        siteName: null,
        buyerOrgId: '', // 采购组织
        distributeType: 0,
        autoDistributeType: '',
        distributeUserId: '', // 分配给
        distributeUserCode: '',
        distributeUserName: ''
      },
      rules: {
        distributeUserId: [
          {
            required: true,
            message: this.$t('请选择分配人员'),
            trigger: 'blur'
          }
        ]
      },
      dataArr: [{ text: this.$t('按人员'), value: 1 }],
      categoryField: { text: 'categoryName', value: 'id' },
      categoryItemField: { text: 'itemName', value: 'id' },
      siteField: { text: 'siteName', value: 'organizationId' },
      busOrgFields: { text: 'organizationName', value: 'id' },
      organizateByUser: {
        value: 'id',
        text: 'name',
        child: 'children',
        parentValue: 'parentId',
        dataSource: []
      }, // 分配人员下拉树
      applyUserIdData: [],
      siteChose: {
        // 选择工厂
        requestUrl: {
          pre: 'masterData',
          url: 'postSiteFuzzyQuery'
        },
        requestKey: 'fuzzyParam',
        otherParams: {},
        fields: {
          text: 'labelShow',
          value: 'organizationId'
        },
        labelShowObj: {
          code: 'siteCode',
          name: 'siteName'
        }
      },
      // 选择业务公司
      companyObj: {
        requestUrl: {
          pre: 'masterData',
          url: 'OrgFindSpecifiedChildrenLevelOrgs'
        },
        requestKey: 'fuzzyParam',
        otherParams: {
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true
        },
        fields: {
          text: 'labelShow',
          value: 'id'
        },
        labelShowObj: {
          code: 'orgCode',
          name: 'orgName'
        }
      }
    }
  },

  mounted() {
    this.addForm.autoDistributeType = this.type

    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    if (this.dialogData?.row) {
      this.addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
    }

    console.log('addForm', this.addForm)

    if (this.dialogData?.dialogType == 'add') {
      this.dialogTitle = this.$t('新增')
    } else {
      this.dialogTitle = this.$t('编辑')
      this.getUser({ text: this.addForm?.distributeUserName })
    }
    // this.getRules();

    //引入mtech-common/utils中的防抖，(mtech-common/utils )
    this.getTree = utils.debounce(this.getTree, 300)
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    // 分配人员改动
    handleChange(e) {
      console.log('分配人员改动了', e)
      this.addForm.distributeUserCode = e?.itemData?.employeeCode
      this.addForm.distributeUserName = e?.itemData?.employeeName
    },

    // 公司下拉改变
    handleCompanyChange(e) {
      console.log('公司改变aa', e)
      this.addForm.companyCode = e.itemData?.orgCode
      this.addForm.companyName = e.itemData?.orgName
    },

    // 工厂下拉改变
    handleSiteChange(e) {
      // console.log("工厂改变", e);
      // this.addForm.siteId = e.itemData?.organizationId;
      this.addForm.siteCode = e.itemData?.siteCode
      this.addForm.siteName = e.itemData?.siteName
    },

    // 获取 用户列表
    getUser(e) {
      const { text: fuzzyName } = e
      this.applyUserIdData = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        this.applyUserIdData = tmp
        if (this.dialogData?.dialogType == 'edit') {
          this.addForm.distributeUserId = tmp[0].employeeId
        }
      })
    },

    // 废弃： 不用搜索树了
    getTree(e) {
      console.log('搜索数据', e)
      // 按人员的组织架构
      this.$API.masterData
        .getOrganizateTree({
          orgLevelCode: 'ORG05',
          orgType: 'ORG001ADM',
          fuzzyName: e?.text
        })
        .then((res) => {
          console.log(res)
          this.organizateByUser = {
            value: 'id',
            text: 'name',
            child: 'children',
            parentValue: 'parentId',
            dataSource: res.data || []
          } // 分配人员下拉树
          // this.organizateByUser.dataSource = res.data || [];
          this.$nextTick(() => {
            if (e.updateData && typeof e.updateData == 'function') {
              e.updateData(this.organizateByUser)
            }
          })
        })
    },

    handleTreeSelect(e) {
      console.log('handleTreeSelect', e)
    },

    // 获取其他数据 code、name
    getOtherInfo(params) {
      // 公司(tree)、品类（有公司的话是tree）、物料、工厂、分配人员(tree)
      // // tree类型
      // if (this.addForm.companyId && this.$refs.companyRef) {
      //   let _data = this.$refs.companyRef.ejsRef.getData(
      //     this.addForm.companyId[0]
      //   );
      //   console.log("公司_data", _data);
      //   if (_data) {
      //     params.companyCode = _data[0].orgCode;
      //     params.companyName = _data[0].name;
      //     params.companyOrgLeveLTypeCode = _data[0].orgLeveLTypeCode;
      //   }
      // }

      // if (this.addForm.distributeUserId && this.$refs.distributeUserRef) {
      //   let _data = this.$refs.distributeUserRef.ejsRef.getData(
      //     this.addForm.distributeUserId[0]
      //   );
      //   console.log("分配人员_data", _data);
      //   if (_data) {
      //     params.distributeUserCode = _data[0].orgCode;
      //     params.distributeUserName = _data[0].name;
      //     params.distributeLeveLTypeCode = _data[0].orgLeveLTypeCode;
      //   }
      // }

      // 普通下拉
      if (
        this.visibleList.includes(this.$t('品类')) &&
        this.addForm.categoryId &&
        this.$refs.categoryRef
      ) {
        let _data = this.$refs.categoryRef.ejsRef.getDataByValue(this.addForm.categoryId)
        console.log('品类_data', _data)
        if (_data) {
          params.categoryCode = _data.categoryCode
          params.categoryName = _data.categoryName
        }
      }
      if (this.addForm.itemId && this.$refs.itemRef) {
        let _data = this.$refs.itemRef.ejsRef.getDataByValue(this.addForm.itemId)
        console.log('物料_data', _data)
        if (_data) {
          params.itemCode = _data.itemCode
          params.itemName = _data.itemName
        }
      }
      // if (this.addForm.siteId && this.$refs.siteRef) {
      //   let _data = this.$refs.siteRef.ejsRef.getDataByValue(
      //     this.addForm.siteId
      //   );
      //   console.log("工厂_data", _data);
      //   if (_data) {
      //     params.siteCode = _data.siteCode;
      //     params.siteName = _data.siteName;
      //   }
      // }
      // if (this.addForm.buyerOrgId && this.$refs.buyerOrgRef) {
      //   let _data = this.$refs.buyerOrgRef.ejsRef.getDataByValue(
      //     this.addForm.buyerOrgId
      //   );
      //   console.log("采购组织_data", _data);
      //   if (_data) {
      //     params.buyerOrgCode = _data.organizationCode;
      //     params.buyerOrgName = _data.organizationName;
      //   }
      // }

      return params
    },

    confirm() {
      console.log('addForm confirm', this.addForm)

      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          console.log(this.addForm)

          let _request = this.dialogData?.requestUrl
          console.log(this.dialogData, _request)

          let params = {
            ...this.addForm,
            parentId: this.dialogData.parentId,
            distributeUserId: this.addForm?.distributeUserId
          }

          // 获取id、code、name；公司和人员加一个orgLeveLTypeCode
          params = this.getOtherInfo(params)
          console.log('params', params)

          // 校验公司和人员是否单纯选择对了
          // if (
          //   params.companyId &&
          //   params.companyOrgLeveLTypeCode &&
          //   params.companyOrgLeveLTypeCode != "ORG02"
          // ) {
          //   this.$toast({
          //     content: this.$t("公司选择错误，请选择公司级别的数据"),
          //     type: "warning",
          //   });
          //   return;
          // }
          // if (
          //   params.distributeUserId &&
          //   params.distributeLeveLTypeCode &&
          //   params.distributeLeveLTypeCode != "ORG05"
          // ) {
          //   this.$toast({
          //     content: this.$t("分配人员选择错误，请选择员工级别的数据"),
          //     type: "warning",
          //   });
          //   return;
          // }

          this.$API.moduleConfig[_request](params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('handleAddDialogShow', false)
              this.$emit('confirmSuccess')
            }
          })
        }
      })
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },

    getRules() {
      if (this.dialogData?.dialogType == 'add') {
        // this.$API.baseMainData.getAddRulesExchangeRate().then((res) => {
        //   if (res.code == 200) this.rules = formatRules(res.data);
        //   this.rules.effectiveTime = [
        //     { required: true, message: this.$t("请选择有效时间"), trigger: "blur" },
        //   ];
        // });
      } else {
        // this.$API.baseMainData.getUpdateRulesExchangeRate().then((res) => {
        //   if (res.code == 200) this.rules = formatRules(res.data);
        //   this.rules.effectiveTime = [
        //     { required: true, message: this.$t("请选择有效时间"), trigger: "blur" },
        //   ];
        // });
      }
    }
  }
}
</script>

<style></style>
