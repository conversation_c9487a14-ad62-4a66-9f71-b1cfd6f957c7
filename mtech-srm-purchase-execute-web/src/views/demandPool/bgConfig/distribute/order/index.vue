<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>

    <add-dialog
      v-if="addDialogShow"
      :dialog-data="dialogData"
      :type="this.type"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></add-dialog>
  </div>
</template>

<script>
import { columnData } from '../config/order.js'
export default {
  components: {
    addDialog: require('../components/addDialog.vue').default
  },
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false,
          useBaseConfig: true,
          toolbar: [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              title: this.$t('新增'),
              permission: ['O_02_0368']
            },
            {
              id: 'Delete',
              icon: 'icon_solid_Delete',
              title: this.$t('删除'),
              permission: ['O_02_0370']
            }

            // {
            //   id: "Activateorder",
            //   icon: "icon_solid_Activateorder",
            //   title: this.$t("启用"),
            //   // permission: ["O_02_0123"],
            // },
            // {
            //   id: "Pauseorder",
            //   icon: "icon_solid_Pauseorder",
            //   title: this.$t("停用"),
            //   // permission: ["O_02_0123"],
            // },
          ],
          gridId: this.$tableUUID.bgConfig.orderDistribute,
          grid: {
            columnData: columnData,
            lineIndex: 1,
            autoWidthColumns: columnData.length + 1,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/_distribute_rule/list',
              defaultRules: [
                {
                  field: 'autoDistributeType',
                  operator: 'equal',
                  value: '0'
                }
              ]
            },
            frozenColumns: 1
          }
        }
      ],
      type: '0',
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    handleClickToolBar(e) {
      console.log(e.gridRef.getMtechGridRecords(), e)
      if (
        e.gridRef.getMtechGridRecords().length <= 0 &&
        !(
          e.toolbar.id == 'Add' ||
          e.toolbar.id == 'Filter' ||
          e.toolbar.id == 'Refresh' ||
          e.toolbar.id == 'Setting' ||
          e.toolbar.id == 'refreshDataByLocal' ||
          e.toolbar.id == 'filterDataByLocal'
        )
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = [],
        _status = []
      e.gridRef.getMtechGridRecords().map((item) => {
        _id.push(item.id)
        _status.push(item.status)
      })
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        if (_status.includes(1)) {
          this.$toast({ content: this.$t('已启用状态的策略请先停用再删除') })
          return
        }
        this.handleDelete(_id)
      }
    },

    handleAdd() {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'add',
        requestUrl: 'addDimension'
      }
    },

    handleEdit(row) {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'edit',
        requestUrl: 'updateDimension',
        row: row
      }
    },

    handleDelete(id) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('确认删除该分配规则？')
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.moduleConfig.deleteDimension({ idList: id }).then((res) => {
            this.$store.commit('endLoading')
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.confirmSuccess()
            }
          })
        }
      })
    },

    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    },

    handleClickCellTool(e) {
      console.log(e)
      if (e.tool.id == 'edit') {
        this.handleEdit(e.data)
      }
      if (e.tool.id == 'delete') {
        if (e.data.status == 1) {
          this.$toast({ content: this.$t('已启用状态的策略请先停用再删除') })
          return
        }
        this.handleDelete([e.data.id])
      } else if (e.tool.id == 'tedit') {
        localStorage.applyAllocate = JSON.stringify(e.data)
        setTimeout(() => {
          this.$router.push(`/purchase-execute/order-distribute-detail?type=1`)
        }, 10)
      } else if (e.tool.id == 'active') {
        this.handleUpdateStatus(e.data.id, 1)
        // alert("1");
      } else if (e.tool.id == 'inactive') {
        this.handleUpdateStatus(e.data.id, 0)
      }
    },

    handleUpdateStatus(id, status) {
      this.$API.moduleConfig
        .updateStatusDimension({
          id: id,
          status: status,
          autoDistributeType: '0'
        })
        .then((res) => {
          this.$store.commit('endLoading')
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.confirmSuccess()
          }
        })
    },

    handleClickCellTitle(e) {
      console.log(e)
      if (e.field === 'strategyCode') {
        localStorage.applyAllocate = JSON.stringify(e.data)
        setTimeout(() => {
          this.$router.push(`/purchase-execute/order-distribute-detail?type=0`)
        }, 10)
      }
    }
  }
}
</script>
