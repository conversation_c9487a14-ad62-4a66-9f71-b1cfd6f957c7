<template>
  <div class="full-height pt20">
    <div class="rule-box">
      <div class="main-info">
        <div class="code">
          <span>{{ $t('策略编号') }}</span>
          <div>{{ applyAllocateRow && applyAllocateRow.strategyCode }}</div>
        </div>
        <div class="code">
          <span>{{ $t('策略名称') }}</span>
          <div>{{ applyAllocateRow && applyAllocateRow.strategyName }}</div>
        </div>
        <div class="code">
          <span>{{ $t('策略纬度') }}</span>
          <div>
            {{ applyAllocateRow && applyAllocateRow.strategyDimensionName }}
          </div>
        </div>
        <div class="code1">
          <span>{{ $t('备注') }}</span>
          <div>
            {{ applyAllocateRow && applyAllocateRow.remark }}
          </div>
        </div>
      </div>
      <!-- <div class="main-info">
        <div class="code">
          <span>{{ $t("备注") }}</span>
          <div>{{ applyAllocateRow && applyAllocateRow.strategyName }}</div>
        </div>
      </div> -->
      <div class="returns" @click="handleBack">{{ $t('返回') }}</div>
    </div>

    <mt-template-page
      ref="templateDetailRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>

    <add-dialog
      v-if="addDialogShow"
      :visible-list="visibleList"
      :dialog-data="dialogData"
      :type="'0'"
      :master-data="{
        companyData: companyData,
        categoryData: categoryData,
        categoryItemData: categoryItemData,
        siteData: siteData,
        organizationData: organizationData
      }"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></add-dialog>
  </div>
</template>

<script>
import {
  detailColumnData,
  detailDefaultColumnData1,
  detailDefaultColumnData2
} from '../config/order.js'
export default {
  components: {
    addDialog: require('../components/ruleAddDialog.vue').default
  },
  data() {
    return {
      type: this.$route.query.type,

      applyAllocateRow: null,
      configColumnList: [
        this.$t('公司'),
        this.$t('品类'),
        this.$t('物料'),
        this.$t('工厂'),
        this.$t('采购组织')
      ],
      visibleList: [],
      pageConfig: [
        {
          useToolTemplate: false,
          useBaseConfig: false,
          toolbar: [
            ['Add', 'Delete'],
            ['Filter', 'Refresh']
          ],
          grid: {
            columnData: detailDefaultColumnData1(this.type),
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/_distribute_rule_detail/list',
              defaultRules: [
                {
                  // label: this.$t("状态"),
                  field: 'parentId',
                  type: 'string',
                  operator: 'equal',
                  value: JSON.parse(localStorage.applyAllocate).id
                }
              ],
              serializeList: (list) => {
                if (list.length > 0) {
                  // 添加序号
                  list.forEach((item, index) => {
                    item.serialNumber = ++index
                  })
                }
                return list
              }
            },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null,

      companyData: {},
      categoryData: [],
      categoryItemData: [],
      siteData: [],
      organizationData: [],
      organizateByUser: {} // 按人员的组织架构人
    }
  },

  mounted() {
    if (this.$route.query.type == 0) {
      this.pageConfig[0].toolbar = []
    }
    this.applyAllocateRow = localStorage?.applyAllocate
      ? JSON.parse(localStorage.applyAllocate)
      : null

    this.visibleList = this.applyAllocateRow.strategyDimensionName.split(',')
    // console.log("this.applyAllocateRow", this.applyAllocateRow);

    let _cols = [],
      _detailColumnData = JSON.parse(JSON.stringify(detailColumnData))
    console.log(_detailColumnData)
    _detailColumnData.forEach((item) => {
      if (this.visibleList.includes(item.headerText)) {
        // 这边做了修改  之前有问题 放的是写死的configColumnList
        _cols.push(item)
      }
    })

    _cols = detailDefaultColumnData1(this.type).concat(_cols, detailDefaultColumnData2)
    console.log(this.$t('整合后的列'), _cols)
    this.$set(this.pageConfig[0].grid, 'columnData', _cols)

    this.getListData()
  },

  methods: {
    handleClickToolBar(e) {
      console.log(e.gridRef.getMtechGridRecords(), e)
      if (
        e.gridRef.getMtechGridRecords().length <= 0 &&
        !(
          e.toolbar.id == 'Add' ||
          e.toolbar.id == 'Filter' ||
          e.toolbar.id == 'Refresh' ||
          e.toolbar.id == 'Setting' ||
          e.toolbar.id == 'refreshDataByLocal' ||
          e.toolbar.id == 'filterDataByLocal'
        )
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = []
      e.gridRef.getMtechGridRecords().map((item) => _id.push(item.id))
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(_id)
      }
    },

    handleAdd() {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'add',
        requestUrl: 'addDimensionDetail',
        parentId: this.applyAllocateRow?.id
      }
    },

    handleEdit(row) {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'edit',
        requestUrl: 'updateDimensionDetail',
        row: row,
        parentId: this.applyAllocateRow?.id
      }
    },

    handleDelete(id) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('确认删除该规则详情？')
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.moduleConfig.deleteDimensionDetail({ idList: id }).then((res) => {
            this.$store.commit('endLoading')
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.confirmSuccess()
            }
          })
        }
      })
    },

    confirmSuccess() {
      this.$refs.templateDetailRef.refreshCurrentGridData()
    },
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    },

    handleClickCellTool(e) {
      console.log(e)
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      } else if (e.tool.id == 'edit') {
        let _row = e.data
        this.handleEdit(_row)
      }
    },

    getListData() {
      // 按人员的组织架构
      // this.$API.masterData
      //   .getOrganizateTree({ orgLevelCode: "ORG05", orgType: "ORG001ADM" })
      //   .then((res) => {
      //     console.log(res);
      //     this.organizateByUser = {
      //       dataSource: res.data,
      //       value: "id",
      //       text: "name",
      //       child: "children",
      //       parentValue: "parentId",
      //     };
      //   });

      // 公司
      if (this.visibleList.includes(this.$t('公司'))) {
        this.$API.masterData.getFuzzyCompanyTree().then((res) => {
          console.log(res)
          this.companyData = {
            dataSource: res.data,
            value: 'id',
            text: 'name',
            child: 'children',
            parentValue: 'parentId'
          }
        })
      }

      // 品类（没选公司的前提）
      if (this.visibleList.includes(this.$t('品类'))) {
        this.$API.masterData.getCategory().then((res) => {
          this.categoryData = res.data
        })
      }

      // 物料
      if (this.visibleList.includes(this.$t('物料'))) {
        this.$API.masterData.getCategoryItem().then((res) => {
          console.log(res)
          this.categoryItemData = res.data
        })
      }

      // 工厂（地点）
      if (this.visibleList.includes(this.$t('工厂'))) {
        this.$API.masterData.getSite().then((res) => {
          console.log(res)
          this.siteData = res.data
        })
      }

      // 采购组织
      // if (this.visibleList.includes(this.$t("采购组织"))) {
      //   this.$API.masterData.getBusinessOrg().then((res) => {
      //     this.organizationData = res.data;
      //   });
      // }
    },

    handleBack() {
      this.$router.go(-1)
    }
  },

  beforeDestroy() {
    if (localStorage.applyAllocate) {
      localStorage.removeItem('applyAllocate')
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  display: flex;
  flex-direction: column;
  .common-template-page {
    flex: 1;
  }
}
.rule-box {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 40px;
  margin-bottom: 20px;
  position: relative;

  .main-info {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    .code {
      flex: 1;
      margin: 0 10px;
      color: #9a9a9a;
      div {
        border-bottom: 1px solid #e8e8e8;
        padding: 12px 0;
      }
    }
    .code1 {
      display: inline-block;
      width: 100%;
      margin: 10px 10px;

      color: #9a9a9a;

      div {
        border-bottom: 1px solid #e8e8e8;
        padding: 12px 0;
      }
    }
  }

  .returns {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(0, 70, 156, 1);
    padding: 10px;
    cursor: pointer;
    position: absolute;
    right: 20px;
    top: 20px;
  }
}
</style>
