import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    // width: "150",
    field: 'strategyCode',
    headerText: i18n.t('策略编号'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        permission: ['O_02_0369']
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),
        permission: ['O_02_0370']
      },
      {
        id: 'tedit',
        icon: 'icon_table_edit',
        title: i18n.t('配置'),
        permission: ['O_02_0371']
      }
    ]
  },
  {
    // width: "150",
    field: 'strategyName',
    headerText: i18n.t('策略名称')
  },
  {
    // width: "150",
    field: 'strategyDimensionName',
    headerText: i18n.t('策略纬度')
  },
  {
    // width: "150",
    field: 'status',
    headerText: i18n.t('状态'),

    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('启用'), cssClass: 'col-active' },
        { value: 0, text: i18n.t('停用'), cssClass: 'col-inactive' }
      ]
    },
    cellTools: [
      {
        id: 'active',
        icon: 'icon_list_disable',
        title: i18n.t('启用'),
        permission: ['O_02_0372'],
        visibleCondition: (data) => data['status'] == '0'
      },
      {
        id: 'inactive',
        icon: 'icon_list_enable',
        title: i18n.t('停用'),
        permission: ['O_02_0373'],
        visibleCondition: (data) => data['status'] == '1'
      }
    ]
  },
  {
    // width: "150",
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    // width: "150",
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

export const detailDefaultColumnData1 = (isView) => [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'serialNumber',
    headerText: i18n.t('序号'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        visibleCondition: () => isView == 1
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),
        visibleCondition: () => isView == 1
      }
    ],
    showInColumnChooser: false
  }
]

export const detailDefaultColumnData2 = [
  {
    width: '150',
    field: 'distributeType',
    headerText: i18n.t('分配方式'),
    showInColumnChooser: false,
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('人员'),
        2: i18n.t('采购组')
      }
    }
  },
  {
    width: '150',
    field: 'distributeUserName',
    showInColumnChooser: false,
    headerText: i18n.t('分配给')
  }
]

export const detailColumnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'serialNumber',
    headerText: i18n.t('序号'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑')
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除')
      }
    ]
  },
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('公司')
    // visible: false,
  },
  {
    width: '150',
    field: 'categoryName',
    headerText: i18n.t('品类')
    // visible: false,
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料')
    // visible: false,
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('工厂')
    // visible: false,
  },
  // {
  //   width: "150",
  //   field: "buyerOrgName",
  //   headerText: i18n.t("采购组织"),
  //   // visible: false,
  // },
  {
    width: '150',
    field: 'distributeType',
    headerText: i18n.t('分配方式'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('人员'),
        2: i18n.t('采购组')
      }
    }
  },
  {
    width: '150',
    field: 'distributeUserName',
    headerText: i18n.t('分配给')
  }
]
