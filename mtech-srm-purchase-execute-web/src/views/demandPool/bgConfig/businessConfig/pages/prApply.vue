<template>
  <div class="purchase-order">
    <common-config
      doc-type="pr"
      :column-name="$t('采购申请')"
      :relative-file-module-type="3"
      :remove-memory-grids="removeMemoryGrids"
    ></common-config>
  </div>
</template>

<script>
export default {
  props: {
    businessTypeId: {
      type: String,
      default: ''
    }
  },
  components: {
    commonConfig: require('@/components/businessComponents/fieldsConfig/commonConfig/index.vue')
      .default
  },
  computed: {
    removeMemoryGrids() {
      return [
        this.$md5(this.$tableUUID.prApply.detailTab + this.businessTypeId),
        this.$md5(this.$tableUUID.demandPool.toOrder + this.businessTypeId),
        this.$md5(this.$tableUUID.demandPool.myOrder + this.businessTypeId),
        this.$md5(this.$tableUUID.demandPool.toSource + this.businessTypeId),
        this.$md5(this.$tableUUID.demandPool.mySource + this.businessTypeId)
      ]
    }
  }
}
</script>

<style></style>
