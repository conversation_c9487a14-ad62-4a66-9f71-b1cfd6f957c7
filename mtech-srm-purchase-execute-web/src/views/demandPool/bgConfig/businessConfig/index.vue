<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config/index.js'
export default {
  data() {
    return {
      pageConfig: [
        {
          title: this.$t('汇总列表'),
          useToolTemplate: false,
          useBaseConfig: true, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: [],
          gridId: this.$tableUUID.bgConfig.businessConfig,
          grid: {
            columnData: columnData,
            lineIndex: 1,
            autoWidthColumns: columnData.length + 1,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/pe/business/configs',
              recordsPosition: 'data'
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },

  methods: {
    handleClickToolBar(e) {
      console.log(e.gridRef.getMtechGridRecords(), e)
      if (
        e.gridRef.getMtechGridRecords().length <= 0 &&
        !(
          e.toolbar.id == 'Add' ||
          e.toolbar.id == 'Filter' ||
          e.toolbar.id == 'Refresh' ||
          e.toolbar.id == 'Setting' ||
          e.toolbar.id == 'refreshDataByLocal' ||
          e.toolbar.id == 'filterDataByLocal'
        )
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = []
      e.gridRef.getMtechGridRecords().map((item) => _id.push(item.id))
      if (e.toolbar.id == 'Refresh') {
        this.getTableData()
      }
    },

    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    },

    handleClickCellTool(e) {
      if (e.tool.id == 'config') {
        let _query = e.data
        if (e.data.id) {
          _query.configId = e.data.id
        }
        localStorage.sourceModuleConfigInfo = JSON.stringify(_query)
        this.$router.push('business-config-detail')
      }
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
