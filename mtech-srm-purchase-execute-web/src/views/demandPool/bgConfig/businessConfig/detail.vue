<template>
  <div class="fields-config-page mt-flex-direction-column">
    <div class="top-info mt-flex-direction-column">
      <div class="detail-info">
        <div class="name-wrap">
          <div class="first-line">
            <span class="code">{{ configInfo.businessTypeCode }}</span>
            <span
              :class="['tags', `tags-${index + 1}`]"
              v-for="(item, index) in tagList"
              :key="index"
              >{{ item }}</span
            >
          </div>
          <div class="second-line">
            <div class="cai-name">{{ configInfo.businessTypeId }}</div>
          </div>
        </div>
        <div class="btns-wrap">
          <!-- <mt-button>{{ $t("保存") }}</mt-button> -->
          <mt-button @click.native="backToBusinessConfig">{{ $t('返回') }}</mt-button>
        </div>
      </div>

      <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
    </div>
    <div class="config-container">
      <!-- 0. 采购申请配置 -->
      <pr-apply v-if="tabIndex == 0" :business-type-id="configInfo.businessTypeId"></pr-apply>

      <!-- 1. 寻源申请配置 -->
      <sc-apply v-if="tabIndex == 1" :business-type-id="configInfo.businessTypeId"></sc-apply>
    </div>
  </div>
</template>
<script>
export default {
  components: {
    prApply: require('./pages/prApply.vue').default,
    scApply: require('./pages/scApply.vue').default
  },
  data() {
    return {
      tabIndex: 0,
      configId: '',
      businessTypeId: null,
      businessTypeCode: null,
      tagList: [this.$t('一般采购')],
      configInfo: {
        businessTypeId: '',
        businessTypeCode: '',
        businessTypeName: null,
        configId: null,
        version: null
      },
      tabSource: [
        {
          title: this.$t('采购申请配置')
        },
        {
          title: this.$t('寻源申请配置')
        }
      ]
    }
  },
  mounted() {
    let sourceModuleConfigInfo = JSON.parse(localStorage.sourceModuleConfigInfo)
    this.configInfo.businessTypeId = sourceModuleConfigInfo.businessTypeId
    this.configInfo.businessTypeCode = sourceModuleConfigInfo.businessTypeCode
    this.configId = sourceModuleConfigInfo.configId
    this.tagList = [sourceModuleConfigInfo.businessTypeName]
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    },
    backToBusinessConfig() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/pages/_fieldsConfig.scss';
</style>
