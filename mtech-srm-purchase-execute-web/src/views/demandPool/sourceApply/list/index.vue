<template>
  <div class="full-height pc-source-apply">
    <mt-template-page
      :template-config="pageConfig"
      :permission-obj="permissionObj"
      @handleSelectTab="handleSelectTab"
    >
      <mt-template-page
        slot="slot-0"
        ref="template-0"
        :template-config="pageConfig0"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
        @handleQuerySearch="handleQuerySearch"
      >
      </mt-template-page>

      <mt-template-page
        slot="slot-1"
        ref="template-1"
        :template-config="pageConfig1"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
      >
        <div slot="slot-filter" class="top-filter">
          <div class="left-status">
            <span>{{ $t('业务类型：') }}</span>
            <status-check
              ref="statusCheckRef"
              :status-data="businessTypeList"
              @handleChose="handleChose"
            ></status-check>
            <div class="total-number" v-if="claimTabIndex != 2">
              {{ bussinessTypeName }}{{ $t('共') }}{{ totalNumber }}{{ $t('行信息内容') }}
            </div>
          </div>
        </div>
      </mt-template-page>
    </mt-template-page>
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { columnData } from './config/index.js'
import { formatMasterFilter } from '@/utils/utils'
import {
  checkField,
  orderStatusField,
  sourceStatusField,
  danjuField,
  priceField,
  fromBusinessTypeField,
  projField,
  companyUser,
  claimField,
  costSharingAccName,
  supplierCode,
  supplierName
} from './config/claim.js'

export default {
  components: {
    statusCheck: require('@/components/businessComponents/statusCheck.vue').default
  },
  data() {
    return {
      bussinessTypeName: null,
      totalNumber: null, //数据总条数
      currentTabIndex: 0,
      claimTabIndex: 0,
      addDialogShow: false,
      dialogData: null,
      businessTypeList: [],
      statusData: [
        { label: this.$t('待审批'), value: 3 },
        { label: this.$t('审批通过'), value: 4 },
        { label: this.$t('审批拒绝'), value: 5 },
        { label: this.$t('已关闭'), value: 2 },
        { label: this.$t('草稿'), value: 1 }
      ],
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0004' },
          { dataPermission: 'b', permissionCode: 'T_02_0005' }
        ]
      },
      pageConfig: [
        {
          title: this.$t('单据视图'),
          dataPermission: 'a',
          permissionCode: 'T_02_0004' // 需要与permissionObj中的参数和权限code对应
        },
        {
          title: this.$t('明细视图'),
          dataPermission: 'b',
          permissionCode: 'T_02_0005' // 需要与permissionObj中的参数和权限code对应
        }
      ],
      pageConfig0: [
        {
          useToolTemplate: false,
          toolbar: [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              title: this.$t('新增'),
              permission: ['O_02_0344']
            },
            {
              id: 'delete',
              icon: 'icon_solid_Delete',
              title: this.$t('删除'),
              permission: ['O_02_0346']
            },
            {
              id: 'close',
              icon: 'icon_solid_Closeorder',
              title: this.$t('关闭'),
              permission: ['O_02_0348']
            },
            {
              id: 'submit',
              icon: 'icon_solid_upload',
              title: this.$t('提交'),
              permission: ['O_02_0347']
            }
          ],
          gridId: this.$tableUUID.sourceApply.mainTab,
          grid: {
            columnData: columnData,
            lineIndex: 1,
            dataSource: [],
            frozenColumns: 1,
            queryCellInfo: (args) => {
              // console.log("queryinfo", args);
              if (args.column.field == 'collectCode') {
                args.cell.classList.add('pld-content-pc')
              }
            }
          }
        }
      ],
      pageConfig1: [
        {
          useToolTemplate: false,
          toolbar: [],
          grid: {
            columnData: checkField,
            lineIndex: 1,
            dataSource: [],
            frozenColumns: 1
          }
        }
      ],
      createOrderRowData: [], // 创建订单的并单策略
      createSourcingRowData: [], // 创建寻源需求的并单策略
      bingTitle: '', // 并单名称
      userInfo: null,
      buyerOrgOptions: [], //采购组织
      juIds: [] // 推送聚采时 选择的idlist。因为有弹窗层，所以在此存储
    }
  },

  created() {
    this.getBusinessConfig() // 获取业务类型
  },

  mounted() {
    let userInfo = sessionStorage.getItem('userInfo')
    console.log('userInfo')
    if (userInfo) {
      this.userInfo = JSON.parse(userInfo)
      this.$set(this.pageConfig0[0].grid, 'asyncConfig', {
        url: `${BASE_TENANT}/requestHeader/query`,
        defaultRules: [
          {
            label: this.$t('创建人'),
            field: 'createUserId',
            type: 'string',
            operator: 'equal',
            value: this.userInfo?.uid
          },
          {
            label: this.$t('来源'),
            field: 'requestType',
            type: 'string',
            operator: 'equal',
            value: 1
          }
        ]
      })
    }
  },

  methods: {
    //处理列模板组件异步请求后数据
    afterAsyncData(res) {
      // 获取数据条数
      this.totalNumber = res.data.total
    },
    handleSelectTab(e) {
      this.currentTabIndex = e
    },

    getRules() {
      // 可能为null
      console.log(
        this.$refs['template-0'].getCurrentUsefulRef(),
        this.$refs['template-0'].getCurrentUsefulRef().pluginRef.queryBuilderRules
      )
      return this.$refs['template-0'].getCurrentUsefulRef()?.pluginRef?.queryBuilderRules
    },
    handleQuerySearch() {
      // console.log("handleQuerySearch-----------------", e);
      // if (e?.rules && e?.rules.length) {
      //   e.rules.forEach((item) => {
      //     if (item.field == "status") {
      //       this.$refs.statusCheckRef.handleClose();
      //     }
      //   });
      // }
    },

    // 获取业务类型下拉
    getBusinessConfig() {
      this.$API.masterData.getDictCode({ dictCode: 'businessType' }).then((res) => {
        if (res.data && res.data.length) {
          this.businessTypeList = []
          res.data.map((item) => {
            this.businessTypeList.push({
              label: item.itemName,
              value: item.id,
              code: item.itemCode
            })
          })
        }
      })
    },

    // 顶部业务类型筛选 修改
    handleChose(e) {
      this.selectedBusinessId = e
      console.log('nowRules 当前规则', this.selectedBusinessId)
      this.bussinessTypeName = this.businessTypeList.find((item) => item.value == e).label

      this.getColumnModule() // 获取明细的列
      // this.setAsyncConfig(); // 获取行数据（配置业务类型id等传参）
    },

    // 获取动态列
    getColumnModule() {
      this.$API.purchaseRequest
        .getModuleByBusinessId({
          businessType: this.selectedBusinessId,
          moduleType: 0
        })
        .then((res) => {
          console.log('getColumnModule', res)
          this.handleUnionColumn(res.data)
        })
    },

    // 整合列
    handleUnionColumn(columns) {
      let _columnData = checkField
      let _columnDataChk,
        dynamicColumn = null
      _columnDataChk = orderStatusField
        .concat(
          sourceStatusField,
          danjuField,
          priceField,
          fromBusinessTypeField,
          projField,
          companyUser,
          claimField
        )
        .map((e) => e.field)
      dynamicColumn = this.formatColumnToGrid(columns, _columnDataChk)
      _columnData = _columnData.concat(
        orderStatusField,
        sourceStatusField,
        danjuField,
        priceField,
        fromBusinessTypeField,
        dynamicColumn,
        projField,
        companyUser,
        claimField
      )

      console.log(this.$t('所有列'), _columnData)

      // this.$set(
      //   this[`pageConfig${this.currentTabIndex}`][this.claimTabIndex].grid,
      //   "columnData",
      //   _columnData
      // );
      this.setAsyncConfig(_columnData) // 获取行数据（配置业务类型id等传参）
    },

    // 调整从后台获取的动态列
    formatColumnToGrid(columns, chkColumns) {
      let res = []
      columns.forEach((item) => {
        if (chkColumns.indexOf(item.fieldCode) < 0) {
          let _col = {
            width: '150',
            field: item.fieldCode,
            headerText: item.fieldName
          }
          // 添加主数据搜索的字段：利润中心  成本中心  币种  采购单位  基本单位  地点/工厂 税率编码
          _col = formatMasterFilter(_col)

          // 判断动态列中 是否有 认领状态
          if (item.fieldCode == 'claimStatus') {
            _col = {
              ..._col,
              ...claimField[0]
            }
            console.log(this.$t('获取倒了认领状态'), _col)
          }
          // 判断是否有成本中心
          if (item.fieldCode == 'costSharingAccName') {
            _col = {
              ..._col,
              ...costSharingAccName[0]
            }
          }
          // 判断动态列中是否有质量免检标识
          if (item.fieldCode == 'qualityExemptionMark') {
            _col = {
              ..._col,
              valueConverter: {
                type: 'map',
                map: { 0: this.$t('需检验'), 1: this.$t('免检') }
              }
            }
          }
          // 判断是否有推荐供应商代码
          if (item.fieldCode == 'supplierCode') {
            _col = {
              ..._col,
              ...supplierCode[0]
            }
          }
          // 判断是否有推荐供应商名称
          if (item.fieldCode == 'supplierName') {
            _col = {
              ..._col,
              ...supplierName[0]
            }
          }
          // 如果有税率，需要*100
          if (item.fieldCode == 'taxid') {
            _col = {
              ..._col,
              valueAccessor: (field, data) => {
                return data.taxid * 100
              }
            }
          }
          res.push(_col)
        }
      })
      return res
    },

    //  获取行数据（通过配置，列模板组件自己监听。配置业务类型id等传参）
    setAsyncConfig(_columnData) {
      // 我认领的 明细
      if (this.currentTabIndex == 1) {
        let _asyncConfig = {
          url: `${BASE_TENANT}/requestItem/query`,
          defaultRules: [
            {
              label: this.$t('创建人'),
              field: 'createUserId',
              type: 'string',
              operator: 'equal',
              value: this.userInfo?.uid
            },
            {
              label: this.$t('业务类型'),
              field: 'businessTypeId',
              type: 'string',
              operator: 'equal',
              value: this.selectedBusinessId
            },
            {
              label: this.$t('来源'),
              field: 'requestType',
              type: 'string',
              operator: 'equal',
              value: 1
            }
          ],
          afterAsyncData: this.afterAsyncData
        }
        let grid = {
          asyncConfig: _asyncConfig,
          columnData: _columnData,
          lineIndex: 1,
          frozenColumns: 1
        }
        this.$set(this[`pageConfig${this.currentTabIndex}`][0], 'grid', grid)

        this.$set(
          this[`pageConfig${this.currentTabIndex}`][0],
          'gridId',
          this.$md5(this.$tableUUID.sourceApply.detailTab + this.selectedBusinessId)
        )

        // this.$set(
        //   this[`pageConfig${this.currentTabIndex}`][0].grid,
        //   "asyncConfig",
        //   _asyncConfig
        // );
      }
    },

    handleClickToolBar(e) {
      console.log(e.gridRef.getMtechGridRecords(), e)
      if (
        e.gridRef.getMtechGridRecords().length <= 0 &&
        !(
          e.toolbar.id == 'Add' ||
          e.toolbar.id == 'Filter' ||
          e.toolbar.id == 'Refresh' ||
          e.toolbar.id == 'Setting' ||
          e.toolbar.id == 'more-option-btn' ||
          e.toolbar.id == 'refreshDataByLocal' ||
          e.toolbar.id == 'filterDataByLocal'
        )
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = [],
        _status = []
      e.gridRef.getMtechGridRecords().map((item) => {
        _id.push(item.id)
        _status.push(item.status)
      })
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'delete') {
        this.handleDelete(e.gridRef.getMtechGridRecords())
      } else if (e.toolbar.id == 'close') {
        // 只有待审批和已关闭 不可以再关闭
        if (_status.every((i) => i != 1 && i != 4)) {
          this.handleClose(_id)
        } else {
          this.$toast({
            content: this.$t('请选择草稿/审批拒绝/审批通过状态的申请进行关闭操作'),
            type: 'warning'
          })
        }
      } else if (e.toolbar.id == 'submit') {
        // 只有 草稿和审批拒绝 可以提交
        if (_status.every((i) => i == 0 || i == 3)) {
          this.handleSubmit(_id)
        } else {
          this.$toast({
            content: this.$t('请选择草稿/审批拒绝状态的申请进行提交操作'),
            type: 'warning'
          })
        }
      } else if (e.toolbar.id == 'transfer') {
        this.handleTransfer(_id)
      } else if (e.toolbar.id == 'cancelClaim') {
        this.handleCancelClaim(_id)
      }
    },

    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
      if (e.tool.id == 'edit') {
        this.handleEdit(e)
      } else if (e.tool.id == 'delete') {
        this.handleDeleteRow([e.data.id])
      } else if (e.tool.id == 'close') {
        this.handleClose([e.data.id])
      } else if (e.tool.id == 'submit') {
        this.handleSubmit([e.data.id])
      } else if (e.tool.id == 'price') {
        this.handlePrice(e.data.id)
      } else if (e.tool.id == 'replanish') {
        this.handleReplanish(e.data.id)
      }
    },

    // 点击单元格 标题
    handleClickCellTitle(e) {
      if (e.field == 'requestCode') {
        this.$router.push(`source-apply-detail?id=${e.data.id}&type=view`)
      } else if (e.field == 'historyPriceNum') {
        this.handlePrice(e.data)
      }
    },

    // 匹配价格记录
    handlePrice(row) {
      this.$dialog({
        modal: () => import('./components/priceDialog.vue'),
        data: {
          title: this.$t('匹配价格记录详情'),
          row
        },
        success: () => {
          // this.confirmSuccess();
        }
      })
    },

    // 取消认领
    handleCancelClaim(ids) {
      this.$dialog({
        data: {
          title: this.$t('取消认领'),
          message: this.$t('取消认领后，需求将出现在采购需求池中被重新分配')
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.purchaseRequest
            .cancelClaim({ idList: ids })
            .then((res) => {
              this.$store.commit('endLoading')
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.confirmSuccess()
              }
            })
            .catch(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },

    // 转办
    handleTransfer(ids) {
      this.$dialog({
        modal: () => import('./components/transferDialog.vue'),
        data: {
          title: this.$t('转办'),
          message: this.$t('是否确认转办？'),
          ids: ids
        },
        success: () => {
          this.confirmSuccess()
        }
      })
    },

    handleAdd() {
      this.$router.push(`source-apply-detail?type=add`)
    },

    handleEdit(e) {
      if (e.data.status == 0) {
        this.$router.push(`source-apply-detail?id=${e.data.id}&type=add`)
      } else {
        this.$router.push(`source-apply-detail?id=${e.data.id}&type=edit`)
      }
    },

    // 删除
    handleDelete(records) {
      let ids = []
      try {
        records.forEach((i) => {
          // 草稿状态 && 手工新建  可以删除
          if (i.sourceType != 0 || i.status != 0) {
            this.$toast({
              content: this.$t('请选择手工创建且为草稿状态的单据删除')
            })
            throw new Error(this.$t(`请选择手工创建且为草稿状态的单据删除`))
          } else {
            ids.push(i.id)
          }
        })
        this.handleDeleteRow(ids)
      } catch (e) {
        console.log(e)
      }
    },

    // 删除 调用接口
    handleDeleteRow(ids) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('确认删除该寻源申请？')
        },
        success: () => {
          this.$API.purchaseRequest
            .deleteHeader({ idList: ids })
            .then((res) => {
              this.$store.commit('endLoading')
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.confirmSuccess()
              }
            })
            .catch(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },

    // 补充信息
    handleReplanish(id) {
      this.$router.push(`source-apply-detail?id=${id}&type=replanish`)
    },

    handleClose(ids) {
      this.$dialog({
        data: {
          title: this.$t('关闭采购申请'),
          message: this.$t('确认关闭该采购申请？')
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.purchaseRequest
            .closeRequest({ idList: ids })
            .then((res) => {
              this.$store.commit('endLoading')
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.confirmSuccess()
              }
            })
            .catch(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },

    handleSubmit(ids) {
      this.$API.purchaseRequest
        .submitRequest({ idList: ids, requestType: 1 })
        .then((res) => {
          this.$store.commit('endLoading')
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.confirmSuccess()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },

    handleUpdateStatus(ids, flag, msg) {
      this.$dialog({
        data: {
          title: this.$t('操作'),
          message: msg
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.base.updateUserStatus({ ids: ids, statusId: flag }).then((res) => {
            this.$store.commit('endLoading')
            if (res.code == 200) {
              this.$refs.templateRef.refreshCurrentGridData()
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
            }
          })
        }
      })
    },

    confirmSuccess() {
      this.$refs[`template-${this.currentTabIndex}`].refreshCurrentGridData()
    }
  }
}
</script>

<style lang="scss">
/* 单元格内容是置灰颜色 */
td.pld-content-pc {
  .grid-edit-column > div:first-child {
    font-size: 14px;
    color: rgba(155, 170, 193, 1);
  }
}
</style>

<style lang="scss" scoped>
.top-filter {
  background: #fff;
  padding: 20px;
  border-bottom: 1px solid #e8e8e8;

  .left-status {
    margin-right: 20px;
    .total-number {
      float: right;
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: #9a9a9a;
      line-height: 28px;
    }
  }

  .titles {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
  }
}
</style>
