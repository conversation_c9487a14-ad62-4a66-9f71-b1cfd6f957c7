import { i18n } from '@/main.js'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant'
export const checkField = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  }
  // {
  //   width: "150",
  //   field: "associateExtDocNo",
  //   headerText: i18n.t("关联外部单据号"),
  // },
]

export const orderStatusField = [
  {
    width: '150',
    field: 'orderStatus',
    headerText: i18n.t('创建订单状态'), // 是否创建采购订单  1是0否
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('待处理'), 1: i18n.t('已转订单') }
    }
  }
]

export const sourceStatusField = [
  {
    width: '150',
    field: 'sourceStatus',
    headerText: i18n.t('创建寻源状态'), // 是否创建寻源需求 1是0否
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('待转寻源'), 1: i18n.t('已转寻源') }
    }
  }
]

export const danjuField = [
  {
    width: '150',
    field: 'requestCode',
    headerText: i18n.t('单据编码')
  },
  {
    width: '150',
    field: 'title',
    headerText: i18n.t('单据名称')
  }
]

export const priceField = [
  {
    width: '150',
    field: 'historyPriceNum',
    headerText: i18n.t('匹配价格记录'),
    cellTools: []
  }
]

export const fromBusinessTypeField = [
  {
    width: '150',
    field: 'sourceType',
    headerText: i18n.t('来源'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('手工创建'),
        1: i18n.t('1：共享财务'),
        2: i18n.t('2：商城')
      } // 0： 手工创建  1：共享财务  2：商城
    }
  },
  {
    width: '150',
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  }
]

export const projField = [
  {
    width: '150',
    field: 'projectType',
    headerText: i18n.t('项目类型'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('项目型'), 0: i18n.t('非项目型') }
    }
  },
  {
    width: '150',
    field: 'projectCode',
    headerText: i18n.t('项目编号')
  },
  {
    width: '150',
    field: 'projectName',
    headerText: i18n.t('项目名称')
  }
]

export const purchaseOrgField = [
  // {
  //   width: "150",
  //   field: "buyerGroupName",
  //   headerText: i18n.t("采购组织"),
  // },
]

export const companyUser = [
  {
    width: '250',
    field: 'companyCode',
    headerText: i18n.t('公司'),
    searchOptions: MasterDataSelect.businessCompany,
    valueAccessor: (field, data) => {
      return `${data.companyCode}-${data.companyName}`
    }
  },
  {
    width: '150',
    field: 'applyDepName',
    headerText: i18n.t('申请部门')
  },
  {
    width: '100',
    field: 'applyUserName',
    headerText: i18n.t('申请人')
  },
  {
    width: '150',
    field: 'applyDate',
    headerText: i18n.t('申请日期')
  },

  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  },
  {
    width: '180',
    field: 'remainingQuantity',
    headerText: i18n.t('剩余可创建订单数')
  }
]

export const claimField = [
  {
    width: '150',
    field: 'claimStatus',
    headerText: i18n.t('认领状态'),
    valueConverter: {
      type: 'map',
      // map: { 0: i18n.t("未认领"), 1: i18n.t("部分认领"), 2: i18n.t("全部认领") },
      map: [
        { value: '0', text: i18n.t('未认领'), cssClass: 'col-inactive' },
        { value: '1', text: i18n.t('已认领'), cssClass: 'col-active' },
        { value: '2', text: i18n.t('全部认领'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '150',
    field: 'claimUserName',
    headerText: i18n.t('认领人')
  }
]

// export const dealField = [
//   {
//     width: "150",
//     field: "telephone",
//     headerText: i18n.t("处理人"),
//   },
//   {
//     width: "150",
//     field: "email",
//     headerText: i18n.t("处理时间"),
//   },
// ];

export const souringRelativeStatus = [
  {
    width: '200',
    field: 'orderStatus',
    headerText: i18n.t('是否已创建采购订单'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    width: '200',
    field: 'sourceStatus',
    headerText: i18n.t('是否已创建寻源需求'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  }
]

export const costSharingAccName = [
  {
    width: '250',
    field: 'costSharingAccName',
    headerText: i18n.t('成本中心'),
    // searchOptions: MasterDataSelect.costCenter,
    // renameField: "costSharingAccCode",
    template: () => {
      return {
        template: Vue.component('cost', {
          template: `<div>{{showCost}}</div>`,
          data() {
            return {
              data: {},
              showCost: ''
            }
          },
          mounted() {
            this.showCost = ''
            if (!this.data.costSharingResponses || !this.data.costSharingResponses.length) return
            this.data.costSharingResponses.forEach((item) => {
              this.showCost += item.costSharingAccName + '-' + item.appProportion + '%;'
            })
            console.log(i18n.t('组合成本中心'), this.showCost)
          }
        })
      }
    }
  }
]
export const supplierCode = [
  {
    width: '180',
    field: 'supplierCode',
    headerText: i18n.t('推荐供应商代码'),
    template: () => {
      return {
        template: Vue.component('cost', {
          template: `<div>{{code}}</div>`,
          data() {
            return {
              data: {},
              code: ''
            }
          },
          mounted() {
            this.code = ''
            if (!this.data.supplierResponses || !this.data.supplierResponses.length) {
              return
            } else {
              this.code = this.data.supplierResponses[0].supplierCode
            }
          }
        })
      }
    }
  }
]
export const supplierName = [
  {
    width: '180',
    field: 'supplierName',
    headerText: i18n.t('推荐供应商名称'),
    template: () => {
      return {
        template: Vue.component('cost', {
          template: `<div>{{name}}</div>`,
          data() {
            return {
              data: {},
              name: ''
            }
          },
          mounted() {
            this.name = ''
            if (!this.data.supplierResponses || !this.data.supplierResponses.length) {
              return
            } else {
              this.name = this.data.supplierResponses[0].supplierName
            }
          }
        })
      }
    }
  }
]
