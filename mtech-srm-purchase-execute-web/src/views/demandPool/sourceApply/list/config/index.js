import Vue from 'vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName, timeNumberToDate } from '@/utils/utils'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'requestCode',
    headerText: i18n.t('编号'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'), // 草稿、审批拒绝可以重新编辑
        visibleCondition: (data) => {
          return data.status == 0 || data.status == 3
        },
        permission: ['O_02_0345']
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'), // 草稿状态 && 手工新建  可以删除
        visibleCondition: (data) => {
          return data.sourceType == 0 && data.status == 0
        },
        permission: ['O_02_0346']
      },
      {
        id: 'replanish',
        icon: 'icon_Editor',
        title: i18n.t('补充信息'), // 草稿、审批拒绝可以重新编辑
        visibleCondition: (data) => {
          return data.status == 2
        },
        permission: ['O_02_0349']
      }
    ],
    headerTemplate: function () {
      return {
        template: Vue.component('uploadFile', {
          template: `
                  <div class="headers">
                    <span class="e-headertext">{{data.headerText}}</span>
                    <mt-tooltip :content="content" position="BottomCenter" target="#box">
                      <MtIcon id="box" name="icon_outline_prompt" style="font-size: 18px;vertical-align: middle" />
                    </mt-tooltip>
                  </div>
                `,
          data() {
            return {
              content: function () {
                return {
                  template: Vue.component('demo', {
                    template: `
                    <div id="tooltip" ref="content" style="width:240px;font-size:12px;padding:6px 11px;">
                    {{$t("草稿状态下无编号，提交后系统自动生成")}}
                    </div>`,
                    data() {
                      return {
                        data: {}
                      }
                    }
                  })
                }
              },
              data: {}
            }
          }
        })
      }
    }
    // valueConverter: {
    //   type: "placeholder",
    //   placeholder: "-", //placeholder可不传，默认值为"未填"
    // },
  },
  {
    width: '150',
    field: 'title',
    headerText: i18n.t('名称')
  },
  {
    width: '100',
    field: 'status',
    headerText: i18n.t('状态'), // 0-草稿，1. 待审批，2. 审批通过，3. 审批拒绝，4. 关闭
    valueConverter: {
      type: 'map',
      // map: { 0: i18n.t("草稿"), 1: i18n.t("待审批"), 2: i18n.t("审批通过"), 3: i18n.t("审批拒绝"), 4: i18n.t("关闭") },
      map: [
        { value: 0, text: i18n.t('草稿'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待审批'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('审批通过'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('审批拒绝'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('关闭'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('已完成'), cssClass: 'col-active' }
      ]
    },
    cellTools: [
      // {
      //   id: "edit",
      //   icon: "icon_Editor",
      //   title: i18n.t("审批进度"),
      //   visibleCondition: (data) => {
      //     return data.status == 1 || data.status == 2;
      //   },
      // },
      {
        id: 'submit',
        icon: 'icon_Share_2',
        title: i18n.t('提交'),
        visibleCondition: (data) => {
          return data.status == 0 || data.status == 3
        },
        permission: ['O_02_0347']
      },
      {
        id: 'close',
        icon: 'icon_list_close',
        title: i18n.t('关闭'),
        visibleCondition: (data) => {
          return data.status == 0 || data.status == 3 || data.status == 2
        },
        permission: ['O_02_0348']
      }
    ]
  },
  {
    width: '150',
    field: 'sourceType',
    headerText: i18n.t('来源'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('手工创建'),
        1: i18n.t('共享财务'),
        2: i18n.t('商城')
      } // 0： 手工创建  1：共享财务  2：商城
    }
  },
  {
    width: '150',
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    width: '150',
    field: 'projectType',
    headerText: i18n.t('项目类型'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('项目型'), 0: i18n.t('非项目型') }
    }
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  },
  // {
  //   width: "150",
  //   field: "buyerGroupName",
  //   headerText: i18n.t("采购组织"),
  // },
  {
    width: '250',
    field: 'administrationCompanyCode',
    headerText: i18n.t('行政公司'),
    searchOptions: MasterDataSelect.administrativeCompany,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data.administrationCompanyCode, data.administrationCompanyName)
    }
  },
  {
    width: '250',
    field: 'companyCode',
    headerText: i18n.t('业务公司'),
    searchOptions: MasterDataSelect.businessCompany,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data.companyCode, data.companyName)
    }
  },
  {
    width: '150',
    field: 'applyDepName',
    headerText: i18n.t('申请部门')
  },
  {
    width: '100',
    field: 'applyUserName',
    headerText: i18n.t('申请人')
  },
  {
    width: '150',
    field: 'applyDate',
    headerText: i18n.t('申请日期')
    // type: "date",
  },
  {
    width: '150',
    field: 'projectDesc',
    headerText: i18n.t('项目信息')
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    }
  },
  {
    width: '150',
    field: 'collectCode',
    headerText: i18n.t('汇总编号'),
    valueConverter: {
      type: 'placeholder',
      placeholder: '-' //placeholder可不传，默认值为"未填"
    }
  }
]

export const columnDataDetail = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'userCode',
    headerText: i18n.t('编号')
  },
  {
    width: '150',
    field: 'fullName',
    headerText: i18n.t('名称')
  },
  {
    width: '150',
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    width: '150',
    field: 'email',
    headerText: i18n.t('类型')
  },
  {
    width: '150',
    field: 'userDescription',
    headerText: i18n.t('推荐合同类型')
  },
  {
    width: '150',
    field: 'statusDescription',
    headerText: i18n.t('推荐协议编码')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('分摊方式')
  },
  {
    width: '150',
    field: 'telephone',
    headerText: i18n.t('项目类型')
  },
  {
    width: '150',
    field: 'email',
    headerText: i18n.t('项目编号')
  },
  {
    width: '150',
    field: 'userDescription',
    headerText: i18n.t('项目名称')
  },
  {
    width: '100',
    field: 'statusDescription',
    headerText: i18n.t('是否独家')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('独家类型')
  },
  {
    width: '150',
    field: 'telephone',
    headerText: i18n.t('备注')
  },
  {
    width: '150',
    field: 'email',
    headerText: i18n.t('公司')
  },
  {
    width: '150',
    field: 'applyDepName',
    headerText: i18n.t('申请部门')
  },
  {
    width: '150',
    field: 'applyUserName',
    headerText: i18n.t('申请人')
  },
  {
    width: '150',
    field: 'applyDate',
    headerText: i18n.t('申请日期'),
    type: 'date'
  },
  {
    width: '150',
    field: 'statusDescription',
    headerText: i18n.t('认领状态')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('认领人')
  }
]

// 匹配价格记录详情
export const priceColumn = [
  // {
  //   width: "70",
  //   headerText: i18n.t("序号"),
  // },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'skuCode',
    headerText: i18n.t('SKU编号')
  },
  {
    width: '150',
    field: 'skuName',
    headerText: i18n.t('SKU名称')
  },
  {
    width: '150',
    field: 'spec',
    headerText: i18n.t('规格型号')
  },
  {
    width: '150',
    field: 'categoryCode',
    headerText: i18n.t('品类编号')
  },
  {
    width: '150',
    field: 'categoryName',
    headerText: i18n.t('品类名称')
  },
  {
    width: '150',
    field: 'contractCode',
    headerText: i18n.t('合同协议编号')
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编号')
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '150',
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    width: '150',
    field: '',
    headerText: i18n.t('阶梯价格'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `
            <mt-button :isPrimary="true" @click="handleViewStage">{{$t("查看")}}</mt-button>
          `,
          data() {
            return { data: {} }
          },
          methods: {
            handleViewStage() {
              this.$parent.$emit('handleViewStage', this.data.stageList)
            }
          }
        })
      }
    }
  },
  // {
  //   width: "150",
  //   field: "untaxedUnitPrice",
  //   headerText: i18n.t("未税单价"), // stageList数组里
  // },
  // {
  //   width: "150",
  //   field: "taxedUnitPrice",
  //   headerText: i18n.t("含税单价"), // stageList数组里
  // },
  // {
  //   width: "150",
  //   field: "discountRate",
  //   headerText: i18n.t("折扣率"), // stageList数组里
  // },
  {
    width: '150',
    field: 'validStartTime',
    headerText: i18n.t('有效期起')
  },
  {
    width: '150',
    field: 'validEndTime',
    headerText: i18n.t('有效期至')
  }
]
