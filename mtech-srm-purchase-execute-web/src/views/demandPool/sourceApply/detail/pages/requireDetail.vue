<template>
  <div :class="['pt20', 'grid-wrap', $route.query.type == 'view' && 'grid-wrap-page']">
    <mt-data-grid
      id="Grid1"
      class="pe-edit-grid custom-toolbar-grid"
      :data-source="dataSource"
      :column-data="columnData"
      ref="dataGrid"
      :allow-paging="allowPaging"
      :page-settings="pageSettings"
      :edit-settings="editSettings"
      :query-cell-info="customiseCell"
      :toolbar="toolbarOptions"
      @toolbarClick="toolbarClick"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
      @handleSetSelectedInfo="handleSetSelectedInfo"
      @selectedChanged="selectedChanged"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    ></mt-data-grid>

    <!-- 批量编辑的弹窗 -->
    <batch-edit-dialog
      v-if="batchEditShow"
      :taxid-data="taxidData"
      :supplier-id-data="supplierIdData"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmBatchSuccess="confirmBatchSuccess"
    ></batch-edit-dialog>

    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-name="downTemplateName"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
// Normal模式，没有绿色背景
// Batch模式，
//     要在 beforeBatchSave 中根据args值对dataSource做改变；
//     且新增时，要换成行内新增
//     去掉保存时的dialog (设置editSetting)
//     一列根据另一列动态修改的

// 设置下拉的数据源
import Vue from 'vue'
import { cloneDeep, isEqual } from 'lodash'
import { maxPageSize } from '@/utils/constant'
import utils from '@/utils/utils'
import { download, getHeadersFileName } from '@/utils/utils'
import {
  editColumnBefore,
  editColumn,
  lastColumn,
  supplementCanEdit,
  editCanNotEdit,
  mallCanNotEdit
} from '../config/requireDetail.js'
export default {
  props: {
    moduleInfo: {
      type: Object,
      default: () => {}
    },
    headerStatus: {
      // 0-草稿，1. 待审批，2. 审批通过，3. 审批拒绝，4. 关闭
      type: Number,
      default: null
    },
    userInfo: {
      type: Object,
      default: () => {}
    },
    // 上一个tab的moduleKey：为了切换时判断是否要 整合 相关附件的列表
    prevTabModuleKey: {
      type: String,
      default: ''
    },
    // 头部信息：来源
    sourceType: {
      type: Number,
      default: null
    },
    // 商城转申请时，获取到的行内数据，只有url中的source=2时，才需要接收
    detailData: {
      type: Array,
      default: () => []
    }
  },

  components: {
    batchEditDialog: require('../components/batchEditDialog.vue').default,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },

  data() {
    return {
      addDialogShow: false,
      modalData: null,
      dataSource: [],
      columnData: editColumnBefore,
      // toolbarOptions: ["Add", "Edit", "Delete", "Update", "Cancel"],
      toolbarOptions: null,
      editSettings: null,
      editColumns: [],

      allowPaging: false,
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        totalRecordsCount: 0,
        pageSizes: [10, 20, 50, 100, 200]
      },

      siteNameData: [], // 地点/工厂列表
      taxidData: null, // 税率列表
      currencyNameData: null, // 币种
      tradeClauseNameData: null, // 贸易条款
      shippingMethodNameData: null, // 物流方式
      // receiveUserIdData: [], // 收货人
      costShareData: null, // 成本中心
      profitData: null, // 利润中心
      supplierIdData: null, // 推荐供应商
      contractData: null, // 推荐合同/协议编号列表

      rowsSelectedInfo: {}, // 编辑行的下拉数据，包括对应的id和code（不能用序号，得换id或addId作为唯一标识）  {id/addId:{categoryId:"", categoryCode:""....}}

      submitTableData: [], // 新增或修改的数据，最终要提交给后台的...来源1：新增或编辑 来源2：批量编辑
      deleteIdList: [], // 被删除的行数据 idList

      batchEditShow: false, // 批量编辑的弹窗
      batchRowIndexs: [], // 批量编辑时的行序号

      nowEditRowFlag: '', // 当前编辑行的id或addId，可以是编辑也可能是新增。。在actionBegin时记录，在actionComplete时清空

      setTimerForEndEdit: 100, // 等待结束编辑的时长，修改后，也要修改 提交按钮那边的时长
      isInEdit: false, // 当前表格是否处于编辑状态

      dynamicFieldsMap: new Map(), // 记录动态字段，在提交数据的时候，需要变换形式

      downTemplateParams: {}, // 下载模板参数
      uploadParams: {}, // 明细行上传excel的
      requestUrls: {},

      isInit: true, // 是第一次赋值

      requiredCols: [], // 必填字段的字段

      initFileListMap: new Map(), // 初始获取数据时，拿到的附件map

      downTemplateName: this.$t('采购申请明细模板'),

      subjectTypeData: [
        { text: this.$t('成本中心'), value: 0 },
        { text: this.$t('销售订单'), value: 1 },
        { text: this.$t('生产工单'), value: 2 },
        { text: this.$t('项目'), value: 3 }
      ] // 科目类型
    }
  },
  computed: {
    pcBusinessCode() {
      return this.$store.state.pcBusinessCode
    },
    prCompanyInfo() {
      return this.$store.state.prCompanyInfo
    }
  },
  watch: {
    moduleInfo() {
      // console.log("moduleInfo", newVal);
      this.getDropDownData()
      this.getMasterDropData() // 无物料时，一些列是下拉。在此获取数据源，存到session
    },
    headerStatus(newVal) {
      sessionStorage.setItem('headerStatus', newVal)
    }
  },

  mounted() {
    this.allowPaging = this.$route.query.type == 'view'
    console.log('this.allowPaging', this.allowPaging)
    if (this.$route.query.type == 'view') {
      this.editSettings = null
    } else if (this.$route.query.type == 'add') {
      sessionStorage.setItem('headerStatus', 0)
      if (!this.$route.query?.source || this.$route.query?.source != 2) {
        // 使用自带的新增按钮，为了处于编辑状态时，能禁用新增按钮
        this.toolbarOptions = [
          {
            text: this.$t('新增'),
            id: 'addinlinebtn',
            prefixIcon: 'e-add',
            fn: this.handleAdd
          },
          // "Add",
          {
            text: this.$t('批量编辑'),
            id: 'batchEdit',
            prefixIcon: 'e-edit',
            fn: this.startBatchEdit
          },
          // "Edit",
          {
            text: this.$t('删除'),
            id: 'delete',
            prefixIcon: 'e-delete',
            fn: this.startBatchDelete
          },
          // "Update",
          // "Cancel",
          {
            text: this.$t('上传'),
            prefixIcon: 'e-upload-1',
            id: 'upload',
            fn: this.handleUpload
          },
          {
            text: this.$t('下载'),
            prefixIcon: 'e-expand',
            id: 'download',
            fn: this.handleDownload
          }
        ]
      } else {
        this.toolbarOptions = [
          // "Edit",
          // "Delete",
          // "Update",
          // "Cancel",
          {
            text: this.$t('删除'),
            id: 'delete',
            prefixIcon: 'e-delete',
            fn: this.startBatchDelete
          },
          {
            text: this.$t('下载'),
            prefixIcon: 'e-expand',
            id: 'download',
            fn: this.handleDownload
          }
        ]
      }
    } else {
      // 编辑状态
      this.toolbarOptions = null
    }

    if (this.$route.query.type != 'view') {
      this.editSettings = {
        allowEditing: true,
        allowAdding: true,
        allowDeleting: true,
        mode: 'Normal', // Batch模式下editTemplate使用下拉的话，保存的args里获取不到，所以就回显不上。最终用重新赋值dataSource解决(币种和贸易条款的下拉)
        showConfirmDialog: false,
        showDeleteConfirmDialog: false, // 因为切换业务类型的时候，只能通过deleteRecord来删除了，但这个方法会触发这个弹窗，所以只能把弹窗去掉
        newRowPosition: 'Bottom'
      }
    }

    // 将物料、sku等部分信息存到session里
    this.setSession()

    // 业务类型变化时，清空数据
    // 新增时拿空配置，编辑/补充信息/查看时拿配置和头部数据
    if (this.$route.query.type == 'add') {
      // 监听业务类型变化（只有新增时候才有）
      this.$bus.$off('changedPRBusinessType')
      this.$bus.$on('changedPRBusinessType', (itemData, previousItemData) => {
        if (!previousItemData) {
          return
        } else {
          this.dataSource = []
          this.rowsSelectedInfo = {}
          this.submitTableData = []
          this.deleteIdList = []
          this.batchRowIndexs = []
          this.nowEditRowFlag = ''
          this.isInEdit = false
          this.columnData = editColumnBefore
        }
      })
    }

    this.$nextTick(() => {
      window.gridObj = this.$refs.dataGrid.ejsRef
    })
  },

  methods: {
    // 不可修改的单元格，修改背景色
    customiseCell(args) {
      // console.log("customiseCell", args);
      if (!args.column.allowEditing) {
        args.cell.classList.add('bg-grey')
      }
    },
    // 获取数据
    getModuleData() {
      let params = {
        docId: this.$route.query.id,
        moduleKey: this.moduleInfo?.moduleKey,
        requestParams: {
          defaultRules: [],
          page: {
            current: 1,
            size: this.$route.query.type == 'view' ? this.pageSettings.pageSize : maxPageSize
          }
        }
      }
      this.initFileListMap = new Map()
      this.$API.purchaseRequest.getModuleData(params).then((res) => {
        let _dataSource = res?.data?.itemDataList?.records
        this.pageSettings.totalRecordsCount = res?.data?.itemDataList?.total
        _dataSource.map((item) => {
          // item.indexDefine = +index + 1;
          // 赋值主键
          item.addId = item.id
          // 下面字段接口返回的是对象，所以需要改成平铺的以展示到cell中
          // 供应商字段
          item.supplierCode = item.supplier?.supplierCode
          item.supplierName = item.supplier?.supplierName

          // 工厂
          item.siteName = item?.siteCode ? item?.siteCode + ' - ' + item?.siteName : null

          // 成本中心
          item.costSharingAccName = item.costSharing?.costSharingAccCode
            ? item.costSharing?.costSharingAccCode + ' - ' + item.costSharing?.costSharingAccName
            : null

          // 利润中心
          item.profitCenterName = item?.profitCenterCode
            ? item?.profitCenterCode + ' - ' + item?.profitCenterName
            : null

          // 采购组
          item.buyerOrgName = item?.buyerOrgCode
            ? item.buyerOrgCode + ' - ' + item.buyerOrgName
            : null

          // 附件
          if (item.file && item.file.length) {
            item.file.forEach((f) => {
              let mapInfo = {
                id: f.id,
                fileName: f.fileName,
                fileSize: f.fileSize,
                fileType: f.fileType,
                createUserName: this.userInfo.username,
                createTime: new Date(),
                itemNo: item.itemNo,
                itemCode: item.itemCode,
                itemName: item.itemName,
                skuCode: item.skuCode,
                skuName: item.skuName,
                spec: item.spec
              }
              this.initFileListMap.set(f.id, mapInfo)
            })
            item.file = JSON.stringify(item.file)
          }
          // 税率
          if (item.taxRateCode) {
            item.taxid = item.taxid * 100
            item.taxRateCode = item.taxRateCode + ' - ' + item.taxid + '%'
          }

          // 动态配置字段 处理： 将数据平铺   fieldDataList：[{fieldCode:'requireName', fieldData: '', fieldId:'', fieldKey: '', fieldType: '', recordId: ''}, {fieldCode: 'requireCode'....}]
          if (item.fieldDataList && item.fieldDataList.length) {
            let dynamicFields = utils.platFieldList(item.fieldDataList)
            for (let i in dynamicFields) {
              item[i] = dynamicFields[i]
            }
          }
        })
        this.dataSource = _dataSource
        console.log('getModuleData', this.dataSource)
        this.$store.commit('endLoading')
      })
    },

    // 物料、sku、工厂的点击确认> 存入这一行的选择数据
    // params：普通下拉的：有field、value
    selectedChanged(params) {
      // console.log(this.$t("得到了"), params);
      this.handleSetSelectedInfo(params)
    },

    // 根据fieldCode，设置下拉选中值。。弹窗下拉以及editTemplate的下拉(列表上会清空部分code、name，这里不重复修改)
    // 注意还要清空，修改了物料/SKU后，工厂、采购组、基本单位、采购单位要清空
    // 地点/工厂 能带出 品类、采购组、基本单位、采购单位
    //     品项  itemId itemCode  itemName  （弹框下拉）
    //     sku  skuId    skuCode  skuName （弹框下拉）
    //     工厂/地址  siteId   siteCode    siteName （弹框下拉）
    //     品类  categoryId    categoryCode    categoryName  （带出）
    //     采购组 buyerOrgId  buyerOrgCode  buyerOrgName  （带出）
    //     基本单位  unitId     unitCode    unitName  （带出）
    //     订单单位  orderUnitId    orderUnitCode    orderUnitName  == 采购单位  （带出）
    //     币种 currencyCode  currencyName  （独立选择）
    //     贸易条款   tradeClauseId    tradeClauseCode    tradeClauseName  （独立选择）
    handleSetSelectedInfo(params) {
      let { fieldCode, itemInfo } = params
      // 暂时没有考虑分页，要是分页，得外层再加一层page的
      // console.log("handleSetSelectedInfo", fieldCode, params);
      let _nowRowSelectedInfo = itemInfo,
        _flag = this.nowEditRowFlag // 如果已有这一行数据
      // rowsSelectedInfo  {row-1: {name: 'sdf', age: 13}, row-2xxx: {class: '13d'}}
      if (Object.prototype.hasOwnProperty.call(this.rowsSelectedInfo, `row-${_flag}`)) {
        _nowRowSelectedInfo = {
          ...this.rowsSelectedInfo[`row-${_flag}`],
          ...itemInfo
        }
      }

      if (fieldCode == 'supplierName') {
        // 推荐供应商
        _nowRowSelectedInfo.supplier = itemInfo
      }
      this.rowsSelectedInfo[`row-${_flag}`] = _nowRowSelectedInfo
      console.log('下拉选择完后获取到的id、code、name', _nowRowSelectedInfo, this.rowsSelectedInfo)
    },

    // 获取下拉数据
    async getDropDownData() {
      if (
        this.taxidData &&
        this.currencyNameData &&
        this.tradeClauseNameData &&
        this.shippingMethodNameData &&
        this.costShareData &&
        this.profitData &&
        this.supplierIdData &&
        this.contractData
      ) {
        this.handleUnionColumns()
        return
      }
      // 获取税率
      await this.$API.masterData.getTaxItem().then((res) => {
        this.taxidData = res.data
        this.taxidData.forEach((item) => {
          item.taxRate = item.taxRate * 100
          item.showLabel = item.taxItemCode + ' - ' + item.taxRate + '%'
        })
      })

      // 获取币种
      await this.$API.masterData.getCurrency().then((res) => {
        this.currencyNameData = res.data || []
      })

      // 贸易条款
      await this.$API.masterData.getDictCode({ dictCode: 'TradeClause' }).then((res) => {
        this.tradeClauseNameData = res.data || []
      })

      // 物流方式
      await this.$API.masterData.getDictCode({ dictCode: 'TransportMode' }).then((res) => {
        this.shippingMethodNameData = res.data || []
      })

      // 收货人
      // await this.$API.masterData.getEmploee().then((res) => {
      //   this.receiveUserIdData = res.data;
      // });

      // // 成本中心
      // await this.$API.masterData.postCostCenterCriteriaQuery().then((res) => {
      //   this.costShareData = res.data || [];
      // });

      // // 利润中心
      // await this.$API.masterData
      //   .postProfitCenterCriteriaQuery({})
      //   .then((res) => {
      //     this.profitData = res.data || [];
      //   });

      // // 推荐供应商
      // await this.$API.masterData.getSupplier().then((res) => {
      //   this.supplierIdData = res.data || [];
      // });

      // 推荐合同/协议编号
      await this.$API.contract.getContractByName({ contractName: '' }).then((res) => {
        let contractData = []
        res.data.forEach((item) => {
          contractData.push({
            ...item,
            text: item.contractId + '-' + item.contractName
          })
        })
        // console.log("contractData", contractData);
        this.contractData = contractData || []
      })
      this.handleUnionColumns()
    },

    // 组合列
    handleUnionColumns() {
      this.requiredCols = []
      let params = {
        that: this,
        taxidData: this.taxidData,
        currencyNameData: this.currencyNameData,
        tradeClauseNameData: this.tradeClauseNameData,
        shippingMethodNameData: this.shippingMethodNameData,
        // receiveUserIdData: this.receiveUserIdData,
        costShareData: this.costShareData,
        profitData: this.profitData,
        supplierIdData: this.supplierIdData || [],
        contractData: this.contractData || [],
        subjectTypeData: this.subjectTypeData
      }
      this.editColumns = editColumn(params)
      // console.log("this.editColumns", this.editColumns);

      let _columnData = cloneDeep(editColumnBefore)
      let _originColumns = this.moduleInfo?.fieldDefines || this.moduleInfo?.fields
      // console.log("_originColumns 从接口获取的列", _originColumns);
      if (_originColumns && _originColumns.length) {
        _originColumns.forEach((col) => {
          // 如果是 动态配置字段
          if (col.tableField == 0) {
            this.dynamicFieldsMap.set(col.fieldCode, col)
          }
          var _one = {
            ...col,
            field: col.fieldCode,
            headerText: col.fieldName,
            width: '150'
          }
          // 如果是必填 0-非必填；1-必填；2-无需配置
          if (_one.required == 1) {
            this.requiredCols.push({
              field: _one.fieldCode,
              headerText: _one.fieldName
            })
            // console.log("_one", _one);
            if (!['itemCode'].includes(_one.fieldCode)) {
              // _one.validationRules = { required: true }; // 用自带的校验会报错，因为要自动跳转到下一个，但校验报错，导致focus不了
            }

            _one.headerTemplate = () => {
              return {
                template: Vue.component('requiredCell', {
                  template: `
                        <div class="headers">
                          <span style="color: red">*</span>
                          <span class="e-headertext">{{fieldName}}</span>
                        </div>
                      `,
                  data() {
                    return {
                      data: {},
                      fieldName: ''
                    }
                  },
                  mounted() {
                    // console.log("我是mounted---", this.data, _one);
                    this.fieldName = _one.fieldName
                  }
                })
              }
            }
          }

          // 如果该列是下拉或者其他类型的复杂情况
          let editFind = this.editColumns.find((editCol) => _one.fieldCode == editCol.field)
          // 校验是否可以编辑
          let _allowEditing = _one.allowEditing === false ? false : true
          if (editFind) {
            _one = {
              ..._one,
              ...editFind,
              width: editFind.width || '150'
            }
            _allowEditing = editFind.allowEditing === false ? false : true
          }

          if (this.$route.query.type == 'replanish') {
            // 包含的都是可编辑的
            _allowEditing = supplementCanEdit.includes(col.fieldCode) ? true : false
          } else if (this.$route.query.type == 'edit') {
            // 包含的都是不可编辑的，因为不可编辑的少
            _allowEditing =
              _one.allowEditing === false || editCanNotEdit.includes(col.fieldCode) ? false : true
          } else if (this.$route.query?.source == 2) {
            // 从商城过来的
            _allowEditing =
              _one.allowEditing === false || mallCanNotEdit.includes(col.fieldCode) ? false : true
          }
          // 如果是外部来的，这三列不可编辑： associateExtDocNo关联单据号  associateExtDocName关联单据名称 associateExtLineNo关联外部行号不可编辑
          if (
            ![0, 1].includes(this.sourceType) &&
            ['associateExtDocNo', 'associateExtDocName', 'associateExtLineNo'].includes(
              col.fieldCode
            )
          ) {
            _allowEditing = false
          }

          _one.allowEditing = _allowEditing
          _columnData.push(_one)
        })
      }
      _columnData = _columnData.concat(lastColumn)
      // console.log("_columnData", _columnData);
      this.columnData = _columnData
      console.log('this.columnData', this.columnData)

      // 如果有 状态列，但不是view状态，那么隐藏 状态列
      if (this.$route.query.type != 'view') {
        let _findClaim = _columnData.find((i) => i.field == 'claimStatus')
        if (_findClaim) {
          this.$nextTick(() => {
            this.$refs.dataGrid.ejsRef.hideColumns(this.$t('认领状态'))
          })
        }
      }

      // if (!this.isInit) {
      //   // 只要列的数据改变了，就清空行数据
      //   let ref = this.$refs.dataGrid.ejsRef;
      //   ref.deleteRecord("addId", ref.getCurrentViewRecords());
      //   this.$store.commit("endLoading");
      //   return;
      // } else {
      //   // 如果是第一次切换业务类型，就需要重新获取数据
      //   if (this.$route.query.id) {
      //     this.getModuleData(); // 如果保存过了，即有了id
      //   } else {
      //     this.$store.commit("endLoading");
      //   }
      // }
      // this.isInit = false;
      // 如果是保存过数据库，就重新拿行数据
      if (this.$route.query.id) {
        this.getModuleData()
      } else if (this.$route.query.source == 2 && this.detailData) {
        this.dataSource = this.detailData
        this.$store.commit('endLoading')
      } else {
        // 如果只是新增
        // 只要列的数据改变了，就清空行数据
        let ref = this.$refs.dataGrid.ejsRef
        ref.deleteRecord('addId', ref.getCurrentViewRecords())
        this.$store.commit('endLoading')
      }
    },

    //  无物料时，一些列是下拉。在此获取数据源，存到session
    getMasterDropData() {
      // 品类、单位 放到了动态搜索里了
      // 2. 地点/工厂(在它自己的下拉里获取)
      // this.$API.masterData.getSite({}).then((res) => {
      //   sessionStorage.setItem("cmSite", res.data);
      // });
      // 3. 采购组
      if (!sessionStorage.getItem('buyerOrgNameSession')) {
        this.$API.masterData
          .getbussinessGroup({
            groupTypeCode: 'BG001CG'
          })
          .then((res) => {
            let sessionData = {
              dataSource: res.data,
              changeFieldObj: {
                buyerOrgId: 'id',
                buyerOrgCode: 'groupCode',
                buyerOrgName: 'groupName'
              },
              fields: { text: 'groupName', value: 'groupName' },
              pld: this.$t('请选择采购组')
            }
            sessionStorage.setItem('buyerOrgNameSession', JSON.stringify(sessionData))
          })
      }
      // 6. 质量免检标识
      if (!sessionStorage.getItem('qualityExemptionMarkSession')) {
        let sessionData = {
          dataSource: [
            {
              value: 0,
              label: this.$t('需检验')
            },
            {
              value: 1,
              label: this.$t('免检')
            }
          ],
          changeRowObj: {
            qualityExemptionMark: 'value'
          },
          fields: { text: 'label', value: 'value' },
          pld: this.$t('请选择质量免检标识')
        }
        sessionStorage.setItem('qualityExemptionMarkSession', JSON.stringify(sessionData))
      }
    },

    actionBegin(args) {
      console.log('actionBegin', args)
      if (args.rowData) {
        args.rowData.querytype = this.$route.query.type
      }

      if (args.requestType === 'add') {
        args.data.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
        this.nowEditRowFlag = args.data.addId // 记录当前编辑行的唯一标识
      }
      if (args.action === 'add' && args.requestType === 'save') {
        // console.log("准备改变index序号");
        const ref = this.$refs.dataGrid.ejsRef
        const length = ref.getCurrentViewRecords()?.length
        args.index = length
      }
      // 记录当前编辑行的唯一标识
      if (args.requestType == 'beginEdit') {
        this.nowEditRowFlag = args.rowData.addId
      }
      // 记录开始 处于编辑状态
      if (args.requestType == 'add' || args.requestType == 'beginEdit') {
        this.isInEdit = true
        // console.log("我准备变成0了-------------callAdd");
      }
      if (args.requestType == 'refresh') {
        this.isInEdit = false // 结束编辑状态
      }
    },

    // 结束操作时
    actionComplete(args) {
      console.log('actionComplete', args)
      if (args.action == 'add' || args.action == 'edit') {
        this.nowEditRowFlag = ''
      }
      if (args.requestType == 'delete') {
        this.handleDelete(args)
      } else if (args.requestType == 'save' && args.action == 'add') {
        // 新增一行，就存下来
        this.handleAddRow(args)
      } else if (args.requestType == 'save' && args.action == 'edit') {
        // 先判断一下是否结束了编辑状态，但数据未改变
        // console.log("编辑前后相等吗？", isEqual(args.data, args.previousData));
        if (!isEqual(args.data, args.previousData)) {
          this.intergrateEditData(args.data) // 整合编辑数据
        }
      }
      // setTimeout(() => {
      if (args.requestType == 'save') {
        this.isInEdit = false // 结束编辑状态
      }
      // }, 10);
    },

    // 新增的完成事件
    handleAddRow(args) {
      this.submitTableData.push(args.data)
    },

    // 删除事件：存被删除的id；改变行额外数据；移除相关附件
    handleDelete(args) {
      // 将删除的id，存起来 并且 判断这行是否有 行的额外数据，如果有，也一并删除
      let _idItems = args.data.filter((item) => item.id)
      let _ids = _idItems.map((item) => item.id)
      this.deleteIdList = this.deleteIdList.concat(_ids)

      args.data.forEach((item) => {
        // 处理 submitTableData
        let findIndex = this.submitTableData.findIndex((i) => i.addId == item.addId)
        if (findIndex >= 0) {
          this.submitTableData.splice(findIndex, 1)
        }

        // 处理 行的额外数据
        let flag = [`row-${item.addId}`]
        if (this.rowsSelectedInfo[flag]) {
          delete this.rowsSelectedInfo[flag]
        }
        // if (item.file && Object.keys(item.file).length) {
        //   removeFileItem.push(item);
        // }
      })
    },

    // 整合编辑数据，更新到 提交数据里
    intergrateEditData(rowData) {
      let _name = rowData.id ? 'id' : 'addId'
      let _index = this.submitTableData.findIndex((item) => rowData[_name] == item[_name])
      if (_index > -1) {
        this.$set(this.submitTableData, _index, rowData)
      } else {
        this.submitTableData.push(rowData)
      }
    },

    toolbarClick(args) {
      console.log('toolbarClick', args)
      // 自定义了一个行内新增按钮，为了给新增的这一行赋默认值
      if (args.item.id == 'addinlinebtn') {
        this.handleAdd()
      } else if (args.item.id == 'batchEdit') {
        this.startBatchEdit()
      } else if (args.item.id == 'delete') {
        this.startBatchDelete()
      } else if (typeof args.item.fn == 'function') {
        args.item.fn(this.selectRowData)
      }
    },

    // 给物料、sku、工厂设置session
    setSession() {
      let itemCodeData = {
        requestUrl: [`/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`], // 因为主数据的物料和sku的下拉和弹窗是两个接口给的，所以这里传两个值
        title: this.$t('请选择物料/品项编码'),
        changedFieldArr: ['itemName', 'spec', 'unitName'], // 物料编码 要改变的是物料名称、规格型号、基本单位
        changedRowArr: ['itemId', 'unitId', 'unitCode'], //要存入的是物料id
        clearFieldArr: [
          // 要清空的列
          'skuCode',
          'skuName',
          'categoryName',
          'siteName',
          'buyerOrgId',
          'buyerOrgCode',
          'buyerOrgName',
          'orderUnitName',
          'qualityExemptionMark'
        ], // 改变物料 会清空 sku编码、sku名称、品类 、工厂、采购组、采购单位、质量免检标识
        clearRowArr: [
          // 要清空的存入
          'skuId',
          'siteId', // 地点/工厂
          'siteCode',
          'categoryId', // 品类
          'buyerOrgId', // 采购组
          'orderUnitId' // 采购单位
        ]
      }
      sessionStorage.setItem('pritemCodeData', JSON.stringify(itemCodeData))

      let skuCodeData = {
        requestUrl: ['/sku/page-query-with-item'], // 因为主数据的物料和sku的下拉和弹窗是两个接口给的，所以这里传两个值
        title: this.$t('请选择sku编码'),
        changedFieldArr: ['itemCode', 'itemName', 'skuName', 'spec', 'unitName'],
        changedRowArr: ['itemId', 'skuId', 'unitId', 'unitCode'], //要存入的是物料id
        clearFieldArr: [
          'categoryName',
          'siteName',
          'buyerOrgId',
          'buyerOrgCode',
          'buyerOrgName',
          'orderUnitName',
          'qualityExemptionMark'
        ], // 改变物料 会清空 品类 、工厂、采购组、基本单位、采购单位、质量免检标识
        clearRowArr: [
          'siteId', // 地点/工厂
          'siteCode',
          'categoryId', // 品类
          'buyerOrgId', // 采购组
          'orderUnitId' // 采购单位
        ]
      }
      sessionStorage.setItem('prskuCodeData', JSON.stringify(skuCodeData))

      let siteNameData = {
        sourceField: 'siteName', // 改变它数据源的字段，即参数
        title: this.$t('请选择地点/工厂'),
        requestUrl: 'getFactoryList',
        fields: { text: 'organizationName', value: 'organizationName' },
        // 因为物料+工厂变了，就直接改变，不需要清空了
        changedFieldArr: [
          // 改变物料 会改变/清空 品类 、工厂、采购组、基本单位、采购单位、质量免检标识
          'categoryName',
          'buyerOrgId',
          'buyerOrgName',
          'orderUnitName',
          'qualityExemptionMark'
        ],
        changedRowArr: [
          // 一行数据里，还带的其他数据。也要被改变/清空
          'categoryId', // 品类
          'buyerOrgId', // 采购组
          'orderUnitId' // 采购单位
        ]
      }
      sessionStorage.setItem('prsiteNameData', JSON.stringify(siteNameData))
    },

    // 测试 获取数据
    getData() {
      this.endEdit()
      setTimeout(() => {
        this.formatData()
      }, this.setTimerForEndEdit)
    },

    // 返回当前view的表格
    getCurrentRecords() {
      return this.$refs.dataGrid.ejsRef.getCurrentViewRecords()
    },

    // 结束编辑状态
    // 要结束编辑的时机：1.切换头部的业务公司 2.切换tab 3.点击新增 4.保存草稿 5.提交 6.批量编辑
    endEdit() {
      const ref = this.$refs.dataGrid.ejsRef

      ref.endEdit()
    },

    // 开始批量编辑
    startBatchEdit() {
      if (this.isInEdit) {
        this.endEdit()
      }
      setTimeout(() => {
        this.handleBatchEdit()
      }, this.setTimerForEndEdit)
    },

    // 批量编辑弹窗显示
    handleBatchEdit() {
      // console.log(this.$refs.dataGrid.ejsRef.getSelectedRecords());
      let selectedRecords = this.$refs.dataGrid.ejsRef.getSelectedRecords()
      if (!selectedRecords || !selectedRecords.length) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      this.batchRowIndexs = selectedRecords.map((item, index) => index)
      this.batchEditShow = true
    },

    // 批量编辑 回调，弹窗关闭/显示
    handleAddDialogShow(code, flag) {
      this[code] = flag
      // console.log("我准备变成0了-------------handleAddDialogShow");
    },

    // 批量编辑修改,回显到表格上（批量修改的时候，一定是保存过数据的）
    confirmBatchSuccess(params) {
      if (Object.keys(params).length === 0) {
        return
      }
      // console.log("获取到批量修改的数据，可能包括下拉选的额外数据", params);
      let _dataSource = this.getCurrentRecords()
      for (let i = 0; i < this.batchRowIndexs.length; i++) {
        for (let key in params) {
          _dataSource[this.batchRowIndexs[i]][key] = params[key]
        }
      }
      // console.log("回显完后：", _dataSource);
      this.dataSource = cloneDeep(_dataSource)

      // 还要修改submitTableData的值：如果修改数组里已经有了就替换，否则就push进去
      let _submitTableData = this.submitTableData
      for (let i = 0; i < this.batchRowIndexs.length; i++) {
        let _row = this.dataSource[i]
        let _findIndex = _submitTableData.findIndex((item) => item.addId == _row.addId)
        if (_findIndex > -1) {
          _submitTableData[_findIndex] = _row
        } else {
          _submitTableData.push(_row)
        }

        // 调整 行的额外数据，在最后保存到时候整合到submitTableData或viewCords() 里
        if (this.rowsSelectedInfo && this.rowsSelectedInfo[`row-${_row.addId}`]) {
          this.rowsSelectedInfo[`row-${_row.addId}`] = {
            ...this.rowsSelectedInfo[`row-${_row.addId}`],
            ...params
          }
        }
      }
      this.submitTableData = cloneDeep(_submitTableData)
    },

    // 响应公司下拉改变，去清空对应的数据
    responseCompanyChange() {
      if (this.isInEdit) {
        this.endEdit()
      }
      setTimeout(() => {
        // 因为物料+工厂变了，就直接改变，不需要清空了
        let clearFieldArr = [
          // 改变物料 会改变/清空 品类 、工厂、采购组、基本单位、采购单位、质量免检标识、成本中心、利润中心
          'siteName',
          'categoryName',
          'buyerOrgId',
          'buyerOrgName',
          'unitName',
          'orderUnitName',
          'qualityExemptionMark',
          'costSharingAccName',
          'profitCenterName'
        ]
        let clearRowArr = [
          // 一行数据里，还带的其他数据。也要被改变/清空
          'siteId',
          'categoryId', // 品类
          'buyerOrgId', // 采购组
          'unitId', // 基本单位
          'orderUnitId', // 采购单位
          'siteName',
          'categoryName',
          'buyerOrgId',
          'buyerOrgName',
          'unitName',
          'orderUnitName',
          'qualityExemptionMark',
          'costSharing', // 成本中心
          'profitCenterId', // 利润中心
          'profitCenterCode',
          'profitCenterName'
        ]
        let _dataSource = this.getCurrentRecords()
        _dataSource.forEach((row) => {
          clearFieldArr.forEach((c) => {
            row[c] = null
          })
        })

        let _rowsSelectedInfo = this.rowsSelectedInfo
        for (let i in _rowsSelectedInfo) {
          clearRowArr.forEach((c) => {
            _rowsSelectedInfo[i][c] = null
          })
        }
        this.dataSource = cloneDeep(_dataSource)
        this.rowsSelectedInfo = cloneDeep(_rowsSelectedInfo)
        console.log('修改后的dataSource', _dataSource, _rowsSelectedInfo)

        // 在下拉选成本中心的组件中，修改参数 成本中心和利润中心的数据
      }, this.setTimerForEndEdit)
    },

    // 新增时，判断头部数据是否都填了，再校验如果正在编辑，就结束编辑，再新增一行
    handleAdd() {
      this.$parent.$refs.headerTop.confirm().then((res) => {
        // 先校验头部的某些必填字段是否填写
        if (!res) {
          this.$toast({
            content: this.$t('请先将头部信息填写完整'),
            type: 'warning'
          })
          return
        }
        // if (this.isInEdit) {
        this.endEdit()
        // }
        setTimeout(() => {
          this.callAdd() //如果没有处于编辑状态，就直接新增好了
        }, this.setTimerForEndEdit)
      })
    },

    callAdd() {
      this.$refs.dataGrid.ejsRef.addRecord()
    },

    // 批量删除
    startBatchDelete() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          let ref = this.$refs.dataGrid.ejsRef
          console.log('须贺列了---222')
          ref.deleteRecord()
        }
      })
    },

    // 上传（显示弹窗）
    handleUpload() {
      this.downTemplateParams = {
        businessTypeCode: this.pcBusinessCode,
        docType: 'sr',
        moduleType: this.moduleInfo?.moduleType
      }
      this.uploadParams = {
        businessTypeCode: this.pcBusinessCode,
        docType: 'sr'
      }
      this.requestUrls = {
        templateUrlPre: 'purchaseRequest',
        templateUrl: 'downloadItemTemplate',
        uploadUrl: 'uploadRequestItem'
      }
      this.showUploadExcel(true)
    },

    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = [] // 清空数据
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },

    // 下载
    handleDownload() {
      let _records = this.$refs.dataGrid.ejsRef.getSelectedRecords()
      if (!_records.length) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      _records.forEach((item) => {
        // 修复文件类型,从string改成对象
        if (item.file && typeof item.file == 'string') {
          item.file = JSON.parse(item.file)
          if (item.file.length) {
            item.file.forEach((f) => {
              f.sysFileId = f.id
            })
          }
        } else if (!item.file) {
          item.file = []
        }

        // 成本中心
        item.costSharing = {
          id: item.costSharing?.id,
          costSharingAccName: item.costSharingAccName
        }

        // 推荐供应商 id supplierId supplierCode supplierName
        item.supplier = {
          supplierId: item.supplierId,
          supplierCode: item.supplierCode,
          supplierName: item.supplierName
        }

        // 一些字段显示是 code-name 拼接的
        if (item.categoryName) {
          item.categoryName = item.categoryCode + ' - ' + item.categoryName
        }
        if (item.unitName) {
          item.unitName = item.unitCode + ' - ' + item.unitName
        }
        if (item.orderUnitName) {
          item.orderUnitName = item.orderUnitCode + ' - ' + item.orderUnitName
        }
        if (item.currencyName) {
          item.currencyName = item.currencyCode + ' - ' + item.currencyName
        }

        // 动态字段
        item.fieldDataList = []
        for (let key in item) {
          let keyFind = this.dynamicFieldsMap.get(key)
          if (keyFind && item[key]) {
            item.fieldDataList.push({
              ...keyFind,
              fieldData: item[key]
            })
          }
        }
      })
      let params = {
        businessTypeCode: this.pcBusinessCode,
        docType: 'sr',
        itemDataList: _records
      }
      this.$store.commit('startLoading')
      this.$API.purchaseRequest.downloadRequestItem(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },

    // 上传成功后，获取到的数据
    upExcelConfirm(uploadArr) {
      // console.log("upExcelConfirm", uploadArr);
      let _records = this.$refs.dataGrid.ejsRef.getCurrentViewRecords()
      _records = _records.concat(uploadArr?.data)
      this.dataSource = cloneDeep(_records)
      this.showUploadExcel(false)
      this.submitTableData = this.submitTableData.concat(cloneDeep(uploadArr?.data))
    },

    // 分页的两个方法
    handleCurrentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.getModuleData()
    },

    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.getModuleData()
    }
  },

  beforeDestroy() {
    if (sessionStorage.getItem('headerStatus')) sessionStorage.removeItem('headerStatus')
    sessionStorage.removeItem('pritemCodeData')
    sessionStorage.removeItem('prskuCodeData')
    sessionStorage.removeItem('prsiteNameData')
    sessionStorage.removeItem('categoryNameSession')
    sessionStorage.removeItem('organizationName')
    sessionStorage.removeItem('unitNameSession')
    sessionStorage.removeItem('orderUnitNameSession')
    sessionStorage.removeItem('qualityExemptionMarkSession')
    sessionStorage.removeItem('buyerOrgNameSession')
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  background: #fff;
}
// .e-grid td.e-rowcell .e-frame {
//   position: relative;
//   right: 6px;
// }
/deep/ .pe-edit-grid .e-rowcell .e-css .e-icons {
  position: relative;
  right: 6px;
}
.e-css {
  display: none;
}
.toolbar-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;

  .one-bar {
    display: flex;
    align-items: center;
    margin-right: 20px;
    cursor: pointer;
    .mt-icons {
      font-size: 14px;
      color: #4f5b6d;
      margin-right: 5px;
    }
    span {
      word-break: keep-all;
      font-size: 14px;
      color: #4f5b6d;
      font-weight: normal;
    }
  }

  .flex1 {
    flex: 1;
  }
}
</style>
