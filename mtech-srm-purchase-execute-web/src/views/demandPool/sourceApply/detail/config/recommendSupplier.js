import { i18n } from '@/main.js'
export const supllierColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    // width: "150",
    field: 'itemCode',
    headerText: i18n.t('物料编号')
  },
  {
    // width: "150",
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    // width: "150",
    field: 'skuCode',
    headerText: i18n.t('SKU编号')
  },
  {
    // width: "150",
    field: 'skuName',
    headerText: i18n.t('SKU名称')
  },
  {
    // width: "150",
    field: 'spec',
    headerText: i18n.t('规格型号')
  },
  {
    // width: "150",
    field: 'supplierName',
    headerText: i18n.t('推荐供应商')
  }
]
