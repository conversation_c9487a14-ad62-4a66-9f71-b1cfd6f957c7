import UTILS from '@/utils/utils'
import { i18n } from '@/main.js'
export const operationLog = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  // {
  //   width: "150",
  //   field: "index",
  //   headerText: i18n.t("序号"),
  // },
  {
    field: 'operationName',
    headerText: i18n.t('操作类型'),
    valueConverter: {
      type: 'map',
      map: {
        INSERT: i18n.t('新增'),
        UPDATE: i18n.t('更新'),
        DELETE: i18n.t('删除'),
        QUERY: i18n.t('查询')
      }
    }
  },
  {
    width: '550',
    field: 'comment',
    headerText: i18n.t('操作内容')
  },
  {
    // width: "250",
    field: 'createUserName',
    headerText: i18n.t('操作人')
  },
  {
    // width: "520",
    field: 'createTime',
    headerText: i18n.t('操作时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return e
        }
      }
    }
  }
]
