import { i18n } from '@/main.js'
export const defaultColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  }
]
export const costShareColumn = (params) => {
  return [
    // {
    //   width: "150",
    //   field: "addId", // 隐藏的主键，获取到数据源时，需要把id赋值给它
    //   headerText: "addId主键",
    //   visible: false,
    //   isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    //   isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。而且水平滚动条会回到最左侧。但现在看来好像并不会。。。
    //   allowEditing: false,
    // },
    // {
    //   width: "150",
    //   field: "",
    //   headerText: i18n.t("序号"),
    // },
    {
      // width: "150",
      field: 'costSharingAccName',
      headerText: i18n.t('成本中心'),
      // validationRules: {
      //   required: true,
      // },
      cellTools: [
        {
          id: 'edit',
          icon: 'icon_Editor',
          title: i18n.t('编辑'), // 草稿、审批拒绝可以重新编辑
          visibleCondition: () => {
            return params.type != 'view' && params.flag
          }
        },
        {
          id: 'delete',
          icon: 'icon_solid_Delete',
          title: i18n.t('删除'), // 草稿、审批拒绝可以重新编辑
          visibleCondition: () => {
            return params.type != 'view' && params.flag
          }
        }
      ]
    },
    {
      // width: "150",
      field: 'appProportion',
      headerText: i18n.t('分摊比例（%）'),
      editType: 'numericedit',
      min: 0,
      max: 100
      // validationRules: {
      //   required: true,
      //   max: [
      //     (args) => {
      //       console.log(args, Number(args["value"]) > 100);
      //       return Number(args["value"]) <= 100;
      //     },
      //     "分摊比例不得超过100",
      //   ],
      //   min: [
      //     (args) => {
      //       return args["value"] > 0;
      //     },
      //     "分摊比例不得小于等于0",
      //   ],
      // },
    },
    {
      // width: "150",
      field: 'description',
      headerText: i18n.t('分摊说明')
    }
  ]
}
