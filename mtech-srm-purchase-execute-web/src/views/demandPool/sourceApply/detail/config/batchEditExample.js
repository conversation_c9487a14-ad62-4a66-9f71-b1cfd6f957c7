import { Query } from '@syncfusion/ej2-data'
import { getComponent } from '@syncfusion/ej2-base'
import { TextBox } from '@syncfusion/ej2-inputs'
import cellUpload from '@/components/Upload/cellUpload'
import { i18n } from '@/main.js'

var grid

let dropDownData = [
  { stateName: 'NewYork', countryId: '1', stateId: '101' },
  { stateName: 'Virginia ', countryId: '1', stateId: '102' },
  { stateName: 'Washington', countryId: '1', stateId: '103' },
  { stateName: 'Queensland', countryId: '2', stateId: '104' },
  { stateName: 'Tasmania ', countryId: '2', stateId: '105' },
  { stateName: 'Victoria', countryId: '2', stateId: '106' }
]

export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  }
]

var itemNameEle, itemNameObj

export const editColumn = [
  {
    field: 'item_no',
    headerText: i18n.t('行号'),
    isPrimaryKey: true,
    allowEditing: false
  },
  {
    field: 'item_code',
    headerText: i18n.t('物料/品项编码'),
    editType: 'dropdownedit',
    edit: {
      params: {
        allowFiltering: true,
        dataSource: dropDownData,
        fields: { text: 'stateId', value: 'stateId' },
        query: new Query(),
        actionComplete: () => false,
        change: (e) => {
          grid = new getComponent('Grid1', 'grid')
          console.log(e, grid)
          // let { rowIndex } = grid.editModule.editModule.args; // Normal模式下
          itemNameObj.value = e.itemData.stateName

          // let { rowIndex, rowData } = grid.editModule.editModule.cellDetails; // Batch模式下
          // grid.updateCell(rowIndex, "item_name", e.itemData.stateName); // Batch模式下
        }
      }
    }
  },
  {
    field: 'item_name',
    headerText: i18n.t('物料/品项名称'),
    allowEditing: false,
    edit: {
      create: () => {
        itemNameEle = document.createElement('input')
        return itemNameEle
      },
      read: () => {
        return itemNameObj.value
      },
      destroy: () => {
        itemNameObj.destroy()
      },
      write: () => {
        itemNameObj = new TextBox({
          disabled: true
        })
        itemNameObj.appendTo(itemNameEle)
      }
    }
  },
  {
    field: 'sku_code',
    headerText: i18n.t('SKU编码')
  },
  {
    field: 'currency_name',
    headerText: i18n.t('日期'),
    editType: 'datepickeredit',
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    field: 'file',
    width: '250',
    headerText: i18n.t('附件'), // 只有编辑状态能修改、上传，否则只能展示
    allowEditing: false,
    // 使用template时，新增一行且未保存时，获取不到index（编辑状态，正常的template不会的）
    // 使用editTemplate时，显示的值不能是对象
    template: function () {
      return {
        template: cellUpload
      }
    }
  }
]

export const editData = [
  {
    item_no: 1,
    item_code: '101',
    item_name: 'NewYork'
  }
]
