// 物料能带出 物料编号、名称、规格型号、工厂数据源；清空SKU编码、sku名称；改变时清空 品类 、工厂、采购组、基本单位、采购单位、质量免检标识
// SKU能带出 SKU编号、名称、规格型号、物料编码、物料名称、工厂数据源；改变时清空  品类 、 工厂、采购组、基本单位、采购单位、质量免检标识
// 物料/SKU+工厂能带出  品类、采购组、基本单位、采购单位、质量免检标识
// 预估含税总价 = 预估含税单价 * 数量
// 预估未税总价 = 预估未税单价 * 数量

// 纯展示的字段：物料名称、sku名称、规格型号、采购组id、采购组名称、品类、基本单位、采购单位、质量免检标识、预估含税总价、预估未税总价

// 下拉选择的字段： 税率（%） 币种 总账科目(暂时用输入，主数据未给) 是否抵扣  贸易条款 物流方式  收货人  推荐供应商
//    推荐供应商 是对象

// 数字输入框：申请数量 预测采购量 预估单价（未税） 预估单价（含税） 科目总额 批准预算总金额

// 特殊类型：物料、sku、工厂、附件、合同

/**
 * 品类、基本单位、采购单位、币种、工厂，在能下拉的时候，需要展示code-name
 */

/**
 * 逻辑顺序
 * 1. 预估 含税未税 的计算 ...完成
 * 2. 下拉
 * 3. 物料、sku
 * 4. 工厂
 * 5. 批量编辑
 * 6. 提交
 */

/**
 * 待处理：新增一行结束后，水平滚动条回到最左侧。而编辑时不会
 */

var bigDecimal = require('js-big-decimal')

import { i18n } from '@/main.js'
import Vue from 'vue'
import { TextBox, NumericTextBox } from '@syncfusion/ej2-inputs'
import { DropDownList } from '@syncfusion/ej2-dropdowns'
import { Query } from '@syncfusion/ej2-data'

import cellUpload from '@/components/normalEdit/cellUpload' // 单元格上传
import cellFileView from '@/components/normalEdit/cellFileView' // 单元格附件查看
import cellChanged from '@/components/normalEdit/cellChanged' // 单元格被改变（纯展示）
import selectedItemCode from '@/components/normalEdit/selectedItemCode' // 物料、sku
import selectFactory from '@/components/normalEdit/prApply/selectFactory' // 工厂下拉
import cellImgShow from '@/components/normalEdit/cellImgShow' // 单元格显示图片
import cellTwoForm from '@/components/normalEdit/cellTwoForm' // 选不选择物料，是两种显示内容
import techContactPersonEdit from '@/components/normalEdit/techContactPerson' // 选择技术对接人
import requireCost from '@/components/normalEdit/requireCost' // 成本中心
import requireProfit from '@/components/normalEdit/requireProfit' // 利润中心
import cellTwoFormFilter from '@/components/normalEdit/cellTwoFormFilter' // 选不选物料，是两种显示，但为下拉时，根据输入搜索接口（品类、单位）

var quantityEle, // 数量
  quantityObj,
  taxedUnitPriceEle, // 预估 含税 单价
  taxedUnitPriceObj,
  taxedTotalPriceEle, // 预估 含税 总价
  taxedTotalPriceObj,
  budgetUnitPriceEle, // 预估未税 单价
  budgetUnitPriceObj,
  budgetTotalPriceEle, // 预估 未税 总价
  budgetTotalPriceObj,
  taxidEle, // 税率
  taxidObj,
  taxidVal = 0 // 税率的值
// supplierCodeEle, // 推荐供应商代码
// supplierCodeObj;

export const editColumnBefore = [
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false,
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    headerText: 'addId主键',
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。
    allowEditing: false
  },
  {
    width: 0,
    field: 'otherDataPew',
    headerText: i18n.t('其他数据'),
    editTemplate: () => {
      return { template: cellChanged }
    }
  }
  // {
  //   width: "80",
  //   field: "indexDefine",
  //   headerText: i18n.t("序号"),
  //   allowEditing: false,
  // },
]

// 由未税总价，计算 含税总价、含税单价  前提：要有数量和税率
function baseUnTax() {
  taxedTotalPriceObj.value = bigDecimal.round(
    bigDecimal.multiply(
      budgetTotalPriceObj.value,
      bigDecimal.add(1, bigDecimal.divide(taxidVal, 100))
    ),
    2
  )
  taxedUnitPriceObj.value = bigDecimal.round(
    bigDecimal.divide(taxedTotalPriceObj.value, quantityObj.value),
    2
  )
}

// 由含税总价，计算 未税总价、未税单价  前提：要有数量和税率
function baseTaxed() {
  budgetTotalPriceObj.value = bigDecimal.divide(
    taxedTotalPriceObj.value,
    bigDecimal.add(1, bigDecimal.divide(taxidVal, 100))
  )
  budgetUnitPriceObj.value = bigDecimal.divide(budgetTotalPriceObj.value, quantityObj.value)
}

export const editColumn = (editColumnParams) => {
  var {
    that,
    taxidData,
    // currencyNameData,
    tradeClauseNameData,
    shippingMethodNameData,
    // receiveUserIdData,
    // costShareData,
    subjectTypeData,
    // profitData,
    // supplierIdData,
    contractData
  } = editColumnParams

  // console.log("receiveUserIdData", receiveUserIdData);
  return [
    {
      field: 'picUrl',
      headerText: i18n.t('图片'),
      // allowEditing: false,
      template: () => {
        return { template: cellImgShow }
      },
      editTemplate: () => {
        return { template: cellImgShow }
      }
    },
    {
      field: 'itemNo',
      headerText: i18n.t('行号'),
      allowEditing: false,
      valueAccessor: (field, data) => {
        return data[field] || 0
      }
    },
    {
      field: 'claimStatus',
      headerText: i18n.t('认领状态'), // 0-未认领，1-部分认领，2-全部认领
      allowEditing: false,
      formatter: (field, data) => {
        let _label = i18n.t('未认领')
        if (data.claimStatus == 1) {
          _label = i18n.t('部分认领')
        } else if (data.claimStatus == 2) {
          _label = i18n.t('全部认领')
        }
        return _label
      }
    },
    {
      width: '200',
      field: 'itemCode',
      headerText: i18n.t('物料/品项编码'),
      editTemplate: () => {
        return {
          template: selectedItemCode
        }
      }
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料/品项名称'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      width: '200',
      field: 'skuCode',
      headerText: i18n.t('SKU编码'), // 带出 物料编码、物料名称、SKU名称、规格型号、（后面两个带不出？？？）品类编码、品类名称
      editTemplate: () => {
        return {
          template: selectedItemCode
        }
      }
    },
    {
      field: 'skuName',
      headerText: i18n.t('SKU名称'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'spec',
      headerText: i18n.t('规格型号'),
      allowEditing: false,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      width: '300',
      field: 'categoryName',
      headerText: i18n.t('品类'),
      // allowEditing: false,
      editTemplate: () => {
        return {
          // template: cellChanged,
          template: cellTwoFormFilter
        }
      }
    },
    {
      width: '225',
      field: 'siteName',
      headerText: i18n.t('地点/工厂'), // 也得是个按钮，点击出弹窗
      editTemplate: () => {
        return {
          template: selectFactory
        }
      }
    },
    // {
    //   field: "buyerOrgId",
    //   headerText: i18n.t("采购组Id"),
    //   allowEditing: false,
    //   editTemplate: () => {
    //     return {
    //       template: cellChanged,
    //     };
    //   },
    // },
    // {
    //   field: "buyerOrgCode",
    //   headerText: i18n.t("采购组代码"),
    //   allowEditing: false,
    //   editTemplate: () => {
    //     return {
    //       template: cellChanged,
    //     };
    //   },
    // },
    {
      field: 'buyerOrgName',
      headerText: i18n.t('采购组'),
      // allowEditing: false,
      editTemplate: () => {
        return {
          // template: cellChanged,
          template: cellTwoFormFilter
        }
      }
    },
    {
      width: '200',
      field: 'unitName',
      headerText: i18n.t('基本单位'),
      // allowEditing: false,
      selectWidth: 180,
      editTemplate: () => {
        return {
          // template: cellChanged,
          template: cellTwoFormFilter
        }
      }
    },
    {
      width: '200',
      field: 'orderUnitName',
      headerText: i18n.t('采购单位'),
      // allowEditing: false,
      selectWidth: 180,
      editTemplate: () => {
        return {
          // template: cellChanged,
          template: cellTwoFormFilter
        }
      }
    },
    {
      field: 'quantity',
      headerText: i18n.t('申请数量'),
      // editType: "numericedit",
      edit: {
        create: () => {
          quantityEle = document.createElement('input')
          return quantityEle
        },
        read: () => {
          return quantityObj.value
        },
        destroy: () => {
          quantityObj.destroy()
        },
        write: (args) => {
          // console.log("开始编辑 申请数量", args);
          // grid = new getComponent("Grid1", "grid");
          // var rowIndex = grid.getRowInfo(args.row).rowIndex;
          if (['edit', 'add'].includes(args.rowData.querytype)) {
            quantityObj = new NumericTextBox({
              placeholder: i18n.t('请输入申请数量'),
              value: args.rowData.quantity || null,
              min: 0,
              format: '###.###',
              validateDecimalOnType: true,
              decimals: 3,
              change: (args) => {
                console.log('输入的申请数量是：', Number(args.value), taxidVal)
                // 如果有 预估含税单价，则计算 预估含税总价
                if (taxedUnitPriceObj?.value) {
                  taxedTotalPriceObj.value = bigDecimal.round(
                    bigDecimal.multiply(taxedUnitPriceObj.value, args.value),
                    2
                  )
                  // 如果有税率，则计算 预估总价（含税）、预估单价（含税）
                  if (taxidVal) {
                    baseUnTax()
                  }
                }
                if (budgetUnitPriceObj?.value) {
                  budgetTotalPriceObj.value = bigDecimal.round(
                    bigDecimal.multiply(budgetUnitPriceObj.value, args.value),
                    2
                  )
                  if (taxidVal) {
                    baseTaxed()
                  }
                }
              }
            })
          } else {
            quantityObj = new TextBox({
              placeholder: i18n.t('请输入申请数量'),
              value: args.rowData.quantity || null,
              min: 0,
              readonly: true
            })
          }
          quantityObj.appendTo(quantityEle)
        }
      }
    },
    {
      field: 'budgetQuantity',
      headerText: i18n.t('预测采购量'),
      editType: 'numericedit',
      edit: {
        params: {
          min: 0,
          decimals: 3,
          format: '###.###',
          validateDecimalOnType: true
        }
      }
    },
    {
      field: 'startDate',
      headerText: i18n.t('预测采购周期起'),
      editType: 'datepickeredit',
      type: 'date',
      format: 'yyyy-MM-dd'
    },
    {
      field: 'endDate',
      headerText: i18n.t('预测采购周期止'),
      editType: 'datepickeredit',
      type: 'date',
      format: 'yyyy-MM-dd'
    },
    {
      field: 'requiredDeliveryDate',
      headerText: i18n.t('要求交期'),
      editType: 'datepickeredit',
      type: 'date',
      format: 'yyyy-MM-dd'
    },
    {
      field: 'budgetUnitPrice',
      headerText: i18n.t('预估单价（未税）'),
      editType: 'numericedit',
      edit: {
        create: () => {
          budgetUnitPriceEle = document.createElement('input')
          return budgetUnitPriceEle
        },
        read: () => {
          return budgetUnitPriceObj.value
        },
        destroy: () => {
          budgetUnitPriceObj.destroy()
        },
        write: (args) => {
          // grid = new getComponent("Grid1", "grid");
          // var rowIndex = grid.getRowInfo(args.row).rowIndex;
          budgetUnitPriceObj = new NumericTextBox({
            placeholder: i18n.t('最多输入两位小数'),
            value: args?.rowData?.budgetUnitPrice || null,
            min: 0,
            format: '###.##',
            validateDecimalOnType: true,
            decimals: 2,
            change: (args) => {
              // 如果有 数量，则计算 预估总价（未税）
              if (quantityObj?.value) {
                budgetTotalPriceObj.value = bigDecimal.round(
                  bigDecimal.multiply(quantityObj.value, args.value),
                  2
                )
                // 如果有税率，则计算 预估总价（含税）、预估单价（含税）
                if (taxidVal) {
                  baseUnTax()
                }
              } else {
                budgetTotalPriceObj.value = 0
              }
            }
          })
          budgetUnitPriceObj.appendTo(budgetUnitPriceEle)
        }
      },
      formatter: (field, data) => {
        return Number(Number(data.budgetUnitPrice).toFixed(2))
      }
    },
    {
      field: 'budgetTotalPrice',
      headerText: i18n.t('预估总价（未税）'),
      allowEditing: false,
      edit: {
        create: () => {
          budgetTotalPriceEle = document.createElement('input')
          return budgetTotalPriceEle
        },
        read: () => {
          return budgetTotalPriceObj.value
        },
        destroy: () => {
          budgetTotalPriceObj.destroy()
        },
        write: (args) => {
          budgetTotalPriceObj = new NumericTextBox({
            placeholder: i18n.t('=申请数量*预估单价（未税）'),
            value: args.rowData?.budgetTotalPrice || null,
            readonly: true,
            cssClass: 'cell-disabled',
            format: '###.##',
            validateDecimalOnType: true,
            decimals: 2
          })
          budgetTotalPriceObj.appendTo(budgetTotalPriceEle)
        }
      },
      formatter: (field, data) => {
        return Number(Number(data.budgetTotalPrice).toFixed(2))
      }
    },
    {
      width: '250',
      field: 'taxRateCode',
      headerText: i18n.t('税率编码'),
      type: 'string',
      editType: 'dropdownedit',
      allowEditing: false,
      edit: {
        create() {
          // console.log(i18n.t("税率的初始值"), args);
          taxidEle = document.createElement('input')
          return taxidEle
        },
        read: () => {
          return taxidObj.value
        },
        destroy() {
          taxidObj.destroy()
        },
        write: (args) => {
          console.log('税率编码 args', args)
          taxidObj = new DropDownList({
            enabled: args.rowData?.querySource != 1,
            width: '230',
            dataSource: taxidData,
            value: args.rowData?.taxRateCode,
            fields: { text: 'showLabel', value: 'showLabel' },
            allowFiltering: true,
            showClearButton: true,
            created() {
              // console.log("我创建了税率", e, args.rowData);
              if (!args?.rowData) return
              // taxRateCode: finance and insurance tax - 5%
              let taxid = args.rowData?.taxRateCode.split(' - ')[1].split('%')[0]
              that.selectedChanged({
                fieldCode: 'taxRateCode',
                itemInfo: {
                  taxid: bigDecimal.divide(taxid, 100),
                  taxRateName: args.rowData?.taxRateName,
                  taxRateCode: args.rowData?.taxRateCode.split(' - ')[0]
                }
              })
            },
            change: (e) => {
              console.log('change事件', e)
              // 更新另外两列：税率编码和名称
              // that.$bus.$emit("taxidChange", e?.itemData?.taxRate);
              // that.$bus.$emit("taxRateNameChange", e?.itemData?.taxItemName);
              that.selectedChanged({
                fieldCode: 'taxRateCode',
                itemInfo: {
                  taxid: Number(bigDecimal.divide(e?.itemData?.taxRate, 100)).toFixed(2),
                  taxRateName: e.itemData?.taxItemName,
                  taxRateCode: e.itemData?.taxItemCode.split(' - ')[0]
                }
              })

              if (!e.itemData) {
                return
              }

              // 将数量、id和code存到额外的值上，提交的时候用 数量
              taxidVal = e.itemData.taxRate
              // 如果有 未税单价、未税总价、数量、税率，那么计算含税的俩
              if (budgetUnitPriceObj?.value && budgetTotalPriceObj?.value && quantityObj?.value) {
                baseUnTax()
              } else if (
                taxedUnitPriceObj?.value &&
                taxedUnitPriceObj.value &&
                quantityObj?.value
              ) {
                baseTaxed()
              }
            },
            query: new Query(),
            filtering: (e) => {
              console.log(i18n.t('开始搜索税率'), e)
              let query = new Query()
              // change the type of filtering
              query = query.where('text', 'contains', e.text, true)
              e.updateData(taxidData, query)
            }
          })
          taxidObj.appendTo(taxidEle)
        }
      }
    },
    // {
    //   field: "taxid",
    //   headerText: i18n.t("税率（%）"),
    //   editTemplate: () => {
    //     return {
    //       template: cellChanged,
    //     };
    //   },
    // },
    // {
    //   field: "taxRateName",
    //   headerText: i18n.t("税率名称"),
    //   editTemplate: () => {
    //     return {
    //       template: cellChanged,
    //     };
    //   },
    // },
    {
      field: 'subjectTotal',
      headerText: i18n.t('科目总额'),
      editType: 'numericedit',
      edit: {
        params: {
          min: 0,
          decimals: 3,
          format: '###.###',
          validateDecimalOnType: true
        }
      }
    },
    {
      field: 'taxedUnitPrice',
      headerText: i18n.t('预估单价（含税）'),
      // editType: "numericedit",
      edit: {
        create: () => {
          taxedUnitPriceEle = document.createElement('input')
          return taxedUnitPriceEle
        },
        read: () => {
          return taxedUnitPriceObj.value
        },
        destroy: () => {
          taxedUnitPriceObj.destroy()
        },
        write: (args) => {
          // grid = new getComponent("Grid1", "grid");
          // var rowIndex = grid.getRowInfo(args.row).rowIndex;
          taxedUnitPriceObj = new NumericTextBox({
            placeholder: i18n.t('最多输入两位小数'),
            // type: "number",
            value: args?.rowData?.taxedUnitPrice || null,
            min: 0,
            format: '###.##',
            validateDecimalOnType: true,
            decimals: 2,
            change: (args) => {
              // 如果有数量，则计算 预估总价（含税）
              if (quantityObj?.value) {
                taxedTotalPriceObj.value = bigDecimal.round(
                  bigDecimal.multiply(quantityObj.value, args.value),
                  2
                )
                // 如果有税率，则计算 预估总价（未税）、预估单价（未税）
                if (taxidVal) {
                  baseTaxed()
                }
              } else {
                taxedTotalPriceObj.value = 0
              }
            }
          })
          taxedUnitPriceObj.appendTo(taxedUnitPriceEle)
        }
      },
      formatter: (field, data) => {
        return Number(Number(data.taxedUnitPrice).toFixed(2))
      }
    },
    {
      field: 'taxedTotalPrice',
      headerText: i18n.t('预估总价（含税）'),
      allowEditing: false,
      edit: {
        create: () => {
          taxedTotalPriceEle = document.createElement('input')
          return taxedTotalPriceEle
        },
        read: () => {
          return taxedTotalPriceObj.value
        },
        destroy: () => {
          taxedTotalPriceObj.destroy()
        },
        write: (args) => {
          taxedTotalPriceObj = new NumericTextBox({
            placeholder: i18n.t('=申请数量*预估单价（含税）'),
            value: args.rowData?.taxedTotalPrice || null,
            readonly: true,
            cssClass: 'cell-disabled',
            format: '###.##',
            validateDecimalOnType: true,
            decimals: 2
          })
          taxedTotalPriceObj.appendTo(taxedTotalPriceEle)
        }
      },
      formatter: (field, data) => {
        return Number(Number(data.taxedTotalPrice).toFixed(2))
      }
    },
    {
      field: 'approvedTotalPrice',
      headerText: i18n.t('批准预算总金额'),
      editType: 'numericedit',
      edit: {
        params: {
          min: 0,
          validateDecimalOnType: true,
          decimals: 3,
          format: '###.###'
        }
      }
    },
    {
      width: '200',
      field: 'currencyName',
      headerText: i18n.t('币种'), // 因为币种修改时要传值 id code，edit那种方式找不到this，所以改用editTemplate
      selectWidth: 180,
      editTemplate: () => {
        return {
          // template: cellChanged,
          template: cellTwoFormFilter
        }
      }
      // editType: "dropdownedit",
      // edit: {
      //   params: {
      //     allowFiltering: true,
      //     dataSource: currencyNameData,
      //     fields: { value: "currencyName", text: "currencyName" },
      //     query: new Query(),
      //     actionComplete: () => false,
      //     change: (e) => {
      //       that.selectedChanged({
      //         fieldCode: "currencyName",
      //         itemInfo: {
      //           currencyCode: e.itemData.currencyCode,
      //           currencyName: e.itemData.currencyName,
      //         },
      //       });
      //     },
      //   },
      // },
    },
    {
      field: 'postingAccountName',
      headerText: i18n.t('总账科目') // 之后是下拉，目前主数据没给
    },
    {
      field: 'tradeClauseName',
      headerText: i18n.t('贸易条款'),
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: tradeClauseNameData,
          fields: { value: 'itemName', text: 'itemName' },
          query: new Query(),
          placeholder: i18n.t('请选择贸易条款'),
          floatLabelType: 'Never',
          showClearButton: true,
          actionComplete: () => false,
          change: (e) => {
            that.selectedChanged({
              fieldCode: 'tradeClauseName',
              itemInfo: {
                tradeClauseId: e.itemData.id,
                tradeClauseCode: e.itemData.itemCode,
                tradeClauseName: e.itemData.itemName
              }
            })
          }
        }
      }
    },
    {
      field: 'shippingMethodName',
      headerText: i18n.t('物流方式'),
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: shippingMethodNameData,
          fields: { value: 'itemName', text: 'itemName' },
          query: new Query(),
          placeholder: i18n.t('请选择物流方式'),
          floatLabelType: 'Never',
          showClearButton: true,
          actionComplete: () => false,
          change: (e) => {
            that.selectedChanged({
              fieldCode: 'shippingMethodName',
              itemInfo: {
                shippingMethodId: e.itemData.id,
                shippingMethodCode: e.itemData.itemCode,
                shippingMethodName: e.itemData.itemName
              }
            })
          }
        }
      }
    },
    {
      field: 'agreementCode',
      headerText: i18n.t('推荐协议编号/名称'),
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: contractData,
          fields: { value: 'text', text: 'text' },
          query: new Query(),
          placeholder: i18n.t('请选择推荐协议编号/名称'),
          floatLabelType: 'Never',
          showClearButton: true,
          actionComplete: () => false,
          change: (e) => {
            that.selectedChanged({
              fieldCode: 'agreementCode',
              itemInfo: {
                agreementId: e.itemData.contractId,
                agreementCode: e.itemData.contractId,
                agreementName: e.itemData.contractName
              }
            })
          }
        }
      }
    },
    {
      field: 'receiveUserName',
      headerText: i18n.t('收货人')
      // editType: "dropdownedit",
      // edit: {
      //   params: {
      //     allowFiltering: true,
      //     dataSource: receiveUserIdData,
      //     fields: { value: "employeeName", text: "employeeName" },
      //     query: new Query(),
      //     placeholder: i18n.t("请选择收货人"),
      //     floatLabelType: "Never",
      //     showClearButton: true,
      //     actionComplete: () => false,
      //     change: (e) => {
      //       that.selectedChanged({
      //         fieldCode: "receiveUserName",
      //         itemInfo: {
      //           receiveId: e.itemData.id,
      //           receiveUserName: e.itemData.employeeName,
      //         },
      //       });
      //     },
      //   },
      // },
    },
    {
      field: 'qualityExemptionMark',
      headerText: i18n.t('质量免检标识'),
      // allowEditing: false,
      template: function () {
        return {
          template: Vue.component('datePicker', {
            template: `<span>{{data.qualityExemptionMark == 1 ?'免检': data.qualityExemptionMark == 0 ? '需检验' : ''}}</span>`,
            data() {
              return { data: {} }
            }
          })
        }
      },
      editTemplate: function () {
        return {
          template: cellTwoForm
        }
      }
    },
    {
      width: '300',
      field: 'costSharingAccName',
      headerText: i18n.t('成本中心'),
      editTemplate: () => {
        return {
          template: requireCost
        }
      }
      // editType: "dropdownedit",
      // edit: {
      //   params: {
      //     allowFiltering: true,
      //     dataSource: costShareData,
      //     fields: { value: "costCenter", text: "costCenter" },
      //     query: new Query(),
      //     placeholder: i18n.t("请选择成本中心"),
      //     floatLabelType: "Never",
      //     showClearButton: true,
      //     actionComplete: () => false,
      //     change: (e) => {
      //       that.selectedChanged({
      //         fieldCode: "costSharingAccName",
      //         itemInfo: {
      //           costSharing: {
      //             id: e.itemData.id,
      //             // costSharingAccCode: e.itemData.supplierCode,
      //             costSharingAccName: e.itemData.costCenter,
      //           },
      //         },
      //       });
      //     },
      //   },
      // },
    },
    {
      width: '300',
      field: 'profitCenterName',
      headerText: i18n.t('利润中心'),
      editTemplate: () => {
        return {
          template: requireProfit
        }
      }
    },
    // {
    // field: "supplierCode",
    // headerText: i18n.t("推荐供应商代码"),
    // allowEditing: false,
    // edit: {
    //   create: () => {
    //     supplierCodeEle = document.createElement("input");
    //     return supplierCodeEle;
    //   },
    //   read: () => {
    //     return supplierCodeObj.value;
    //   },
    //   destroy: () => {
    //     supplierCodeObj.destroy();
    //   },
    //   write: (args) => {
    //     supplierCodeObj = new TextBox({
    //       value: args.rowData?.supplierCode || null,
    //       readonly: true,
    //       cssClass: "cell-disabled",
    //     });
    //     supplierCodeObj.appendTo(supplierCodeEle);
    //   },
    // },
    {
      width: '150',
      field: 'recommendSupplierName',
      headerText: i18n.t('推荐供应商')
      // editType: "dropdownedit",
      // edit: {
      //   params: {
      //     allowFiltering: true,
      //     dataSource: supplierIdData,
      //     fields: { value: "supplierName", text: "supplierName" },
      //     query: new Query(),
      //     placeholder: i18n.t("请选择推荐供应商"),
      //     floatLabelType: "Never",
      //     showClearButton: true,
      //     actionComplete: () => false,
      //     change: (e) => {
      //       supplierCodeObj.value = e.itemData.supplierCode;
      //       that.selectedChanged({
      //         fieldCode: "supplierName",
      //         itemInfo: {
      //           supplierId: e.itemData.id,
      //           supplierCode: e.itemData.supplierCode,
      //           supplierName: e.itemData.supplierName,
      //         },
      //       });
      //     },
      //   },
      // },
    },
    {
      field: 'distributionDate',
      headerText: i18n.t('分配日期'),
      allowEditing: false
    },
    {
      field: 'distributionUserName',
      headerText: i18n.t('分配人'),
      allowEditing: false
    },
    {
      field: 'budgetCode',
      headerText: i18n.t('预算编号'),
      edit: {
        params: {
          placeholder: i18n.t('请输入预算编号')
        }
      }
    },
    {
      field: 'subjectType',
      headerText: i18n.t('科目类型'),
      editType: 'dropdownedit',
      edit: {
        params: {
          allowFiltering: true,
          dataSource: subjectTypeData,
          query: new Query(),
          fields: { value: 'value', text: 'text' },
          placeholder: i18n.t('请选择科目类型'),
          floatLabelType: 'Never',
          showClearButton: true
        }
      },
      valueAccessor: function (field, data, column) {
        // console.log(i18n.t("科目类型"), field, data, column);
        let dataSource = column.edit.params.dataSource || []
        return dataSource.filter((i) => i.value == data[field])?.[0]?.text
      }
    },
    {
      width: '300',
      field: 'techContactPerson',
      headerText: i18n.t('技术对接人'),
      editTemplate: () => {
        return {
          template: techContactPersonEdit
        }
      }
    },
    {
      width: '150',
      field: 'linkWay',
      headerText: i18n.t('联系方式')
      // editType: "numericedit",
      // edit: {
      //   params: {
      //     showSpinButton: false,
      //     format: "###",
      //   },
      // },
    },
    {
      width: '150',
      field: 'customerOrderLineNo',
      headerText: i18n.t('关联销售订单行号'),
      editType: 'numericedit',
      edit: {
        params: {
          // 最多10位数
          showSpinButton: false,
          format: '###',
          min: 0,
          max: 9999999999
        }
      }
    },
    {
      width: '150',
      field: 'associateExtLineNo',
      headerText: i18n.t('关联外部行号'),
      editType: 'numericedit',
      edit: {
        params: {
          // 最多10位数
          showSpinButton: false,
          format: '###',
          min: 0
        }
      }
    },
    {
      width: '150',
      field: 'associateExtDocNo',
      headerText: i18n.t('关联单据号')
      // editType: "numericedit",
      // edit: {
      //   params: {
      //     // 最多10位数
      //     showSpinButton: false,
      //     format: "###",
      //     min: 0,
      //   },
      // },
    },
    {
      width: '150',
      field: 'claimDate',
      headerText: i18n.t('认领时间'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'claimUserName',
      headerText: i18n.t('认领人'),
      allowEditing: false
    }
  ]
}

export const lastColumn = [
  {
    field: 'file',
    width: '250',
    headerText: i18n.t('附件'), // 只有编辑状态能修改、上传，否则只能展示
    allowEditing: false,
    // 使用template时，新增一行且未保存时，获取不到index（编辑状态，正常的template不会的）
    // 使用editTemplate时，显示的值不能是对象
    template: function () {
      return {
        template: cellFileView
      }
    },
    editTemplate: () => {
      return {
        template: cellUpload
      }
    }
  }
]

// 编辑状态：不可以编辑的列
// 物料名称、sku名称、需求名称、需求描述、预估含税单价、预估未税单价、推荐供应商、附件、备注
export const editCanNotEdit = [
  // 一些目前就不可编辑的，还有下列
  'costSharingAccName',
  'profitCenterName', // 利润中心
  'seriesName', // 关联产品系列名称
  'agreementCode' // 推荐合同/协议编号
]

// 补充信息状态：可以编辑的列
// 需求名称、需求描述、预估含税单价、预估未税单价、推荐供应商、附件、备注
export const supplementCanEdit = [
  'requireName',
  'requireDesc',
  'budgetUnitPrice',
  'taxedUnitPrice',
  'supplierName',
  'file',
  'remark'
]

// 商城转申请：不可以操作的列
// sku、物料、收货信息
export const mallCanNotEdit = [
  'skuCode',
  'skuName',
  'itemCode',
  'itemName',
  'receiveUserName',
  'linkWay',
  'receiveAddress'
]
