import { i18n } from '@/main.js'
export const lengthArr = [
  {
    field: 'projectCode', // 项目编号
    length: 50,
    headerText: i18n.t('项目编号')
  },
  {
    field: 'projectName', // 项目名称
    length: 200,
    headerText: i18n.t('项目名称')
  },
  {
    field: 'projectDesc', // 项目描述
    length: 200,
    headerText: i18n.t('项目描述')
  },
  {
    field: 'customerOrder', // 关联客户订单
    length: 200,
    headerText: i18n.t('关联客户订单')
  },
  {
    field: 'customerOrderLineNo', // 关联销售订单行号
    length: 20,
    headerText: i18n.t('关联销售订单行号')
  },
  {
    field: 'budgetCode', // 预算编号
    length: 50,
    headerText: i18n.t('预算编号')
  },
  {
    field: 'budgetUnitPrice', // 预算单价（未税）
    length: 100000000,
    headerText: i18n.t('预算单价（未税）')
  },
  {
    field: 'budgetDept', // 预算部门
    length: 200,
    headerText: i18n.t('预算部门')
  },
  {
    field: 'receiveUserName', // 收货人姓名
    length: 256,
    headerText: i18n.t('收货人姓名')
  },
  {
    field: 'linkWay', // 联系方式
    length: 20,
    headerText: i18n.t('联系方式')
  },
  {
    field: 'receiveAddress', // 收货地址
    length: 200,
    headerText: i18n.t('收货地址')
  },
  {
    field: 'packageMethod', // 包装方式
    length: 200,
    headerText: i18n.t('包装方式')
  },
  {
    field: 'packageSpec', // 包装规格
    length: 200,
    headerText: i18n.t('包装规格')
  },
  {
    field: 'packageDesc', // 包装说明
    length: 200,
    headerText: i18n.t('包装说明')
  },
  {
    field: 'seriesCode', // 涉及产品系列代码
    length: 50,
    headerText: i18n.t('涉及产品系列代码')
  },
  {
    field: 'seriesName', // 涉及产品系列名称
    length: 75,
    headerText: i18n.t('涉及产品系列名称')
  },
  {
    field: 'remark', // 备注
    length: 200,
    headerText: i18n.t('备注')
  },
  {
    field: 'associateExtDocNo', // 关联单据号
    length: 50,
    headerText: i18n.t('关联单据号')
  },
  {
    field: 'associateExtLineNo', // 关联外部行号
    length: 50,
    headerText: i18n.t('关联外部行号')
  },
  {
    field: 'recommendSupplierName', // 推荐供应商名称
    length: 200,
    headerText: i18n.t('推荐供应商名称')
  },

  // 下面是动态字段
  {
    field: 'requireName', // 需求名称
    length: 500,
    headerText: i18n.t('需求名称')
  },
  {
    field: 'requireDesc', // 需求描述
    length: 500,
    headerText: i18n.t('需求描述')
  },
  {
    field: 'assetType', // 关联资产类别
    length: 500,
    headerText: i18n.t('关联资产类别')
  },
  {
    field: 'assetCode', // 关联资产编号
    length: 500,
    headerText: i18n.t('关联资产编号')
  },
  {
    field: 'assetCard', // 资产卡片
    length: 500,
    headerText: i18n.t('资产卡片')
  },
  {
    field: 'customerName', // 客户
    length: 500,
    headerText: i18n.t('客户')
  },
  {
    field: 'customerOrder', // 关联客户订单
    length: 500,
    headerText: i18n.t('关联客户订单')
  }
]
