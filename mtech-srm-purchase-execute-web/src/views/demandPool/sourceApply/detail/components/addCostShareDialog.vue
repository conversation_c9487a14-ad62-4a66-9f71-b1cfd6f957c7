<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="costSharingAccName" :label="$t('成本中心')">
        <mt-input
          v-model="addForm.costSharingAccName"
          :show-clear-button="true"
          :maxlength="50"
          :placeholder="$t('请输入成本中心')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="appProportion" :label="$t('分摊比例（%）')">
        <mt-input-number
          v-model="addForm.appProportion"
          :show-clear-button="true"
          :max="100"
          :placeholder="$t('请输入分摊比例')"
        ></mt-input-number>
      </mt-form-item>

      <mt-form-item prop="description" :label="$t('分摊说明')" class="full-width">
        <mt-input
          v-model="addForm.description"
          :show-clear-button="true"
          :multiline="true"
          :maxlength="200"
          :placeholder="$t('请输入分摊说明')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
// import common from "@/utils/constant";
// import { formatDate, formatRules } from "@/utils/util";
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        costSharingAccName: '',
        appProportion: null,
        description: ''
      },
      rules: {
        costSharingAccName: [
          {
            required: true,
            message: this.$t('请输入成本中心'),
            trigger: 'blur'
          }
        ],
        appProportion: [
          {
            required: true,
            message: this.$t('请输入分摊比例'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  mounted() {
    if (this.dialogData?.row) {
      let _addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
      this.addForm = _addForm
    }

    if (this.dialogData?.dialogType == 'add') {
      this.dialogTitle = this.$t('新增费用分摊')
    } else {
      this.dialogTitle = this.$t('编辑费用分摊')
    }
    // this.getRules();
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('confirmSuccess', this.addForm)
          this.$emit('handleAddDialogShow', false)
        }
      })
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },

    getRules() {
      if (this.dialogData?.dialogType == 'add') {
        // this.$API.baseMainData.getAddRulesExchangeRate().then((res) => {
        //   if (res.code == 200) this.rules = formatRules(res.data);
        //   this.rules.effectiveTime = [
        //     { required: true, message: this.$t("请选择有效时间"), trigger: "blur" },
        //   ];
        // });
      } else {
        // this.$API.baseMainData.getUpdateRulesExchangeRate().then((res) => {
        //   if (res.code == 200) this.rules = formatRules(res.data);
        //   this.rules.effectiveTime = [
        //     { required: true, message: this.$t("请选择有效时间"), trigger: "blur" },
        //   ];
        // });
      }
    }
  }
}
</script>

<style></style>
