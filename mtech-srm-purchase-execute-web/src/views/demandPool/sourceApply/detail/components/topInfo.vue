<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="status mr20">
        {{ headerInfo && headerInfo.statusTxt }}
      </div>
      <div class="infos mr20" v-if="headerInfo && headerInfo.requestCode">
        {{ $t('单据编号：') }}
        {{ (headerInfo && headerInfo.requestCode) || '-' }}
      </div>
      <div class="infos mr20">
        {{ $t('创建人：') }}{{ headerInfo && headerInfo.createUserName }}
      </div>
      <div class="infos mr20" v-if="headerInfo && headerInfo.createTime">
        {{ $t('创建时间：') }}{{ headerInfo.createTime | formatTime }}
      </div>
      <div class="infos" v-if="headerInfo && headerInfo.docUpdateTime">
        {{ $t('单据更新时间：') }}{{ headerInfo.docUpdateTime | formatTime }}
      </div>

      <div class="middle-blank"></div>

      <!-- 右侧各种操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <!-- 草稿和审批拒绝状态可以保存草稿 -->
      <mt-button css-class="e-flat" :is-primary="true" v-if="canSave" @click="startToSave(1)">{{
        $t('保存草稿')
      }}</mt-button>
      <!-- 草稿和审批拒绝状态可以提交 -->
      <mt-button css-class="e-flat" :is-primary="true" v-if="canSubmit" @click="startToSave(2)">{{
        $t('提交')
      }}</mt-button>

      <div class="sort-box" @click="isExpand = !isExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="addForm" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="businessTypeId" :label="$t('业务类型')">
          <mt-select
            ref="businessRef"
            v-model="addForm.businessTypeId"
            :data-source="businessTypeList"
            :show-clear-button="false"
            :disabled="type != 'add'"
            :fields="{ text: 'itemName', value: 'id' }"
            :placeholder="$t('请选择业务类型')"
            @change="handleBusinessChange"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="title" :label="$t('名称')">
          <mt-input
            v-model="addForm.title"
            :disabled="type != 'add'"
            :maxlength="50"
            :placeholder="$t('名称')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="applyUserId" :label="$t('申请人')" class="apply-item">
          <debounce-filter-select
            ref="userRef"
            :width="350"
            v-model="addForm.applyUserId"
            :request="getUser"
            :data-source="applyUserIdData"
            :show-clear-button="false"
            :fields="{ text: 'text', value: 'employeeId' }"
            :placeholder="$t('请选择申请人')"
            :disabled="type != 'add'"
          ></debounce-filter-select>
        </mt-form-item>

        <mt-form-item prop="administrationCompanyId" :label="$t('行政公司')" class="apply-item">
          <mt-select
            ref="companyRef"
            width="350"
            v-model="addForm.administrationCompanyId"
            :data-source="applyCompanyData"
            :show-clear-button="false"
            :allow-filtering="true"
            :filtering="getCompany"
            :fields="{ text: 'labelShow', value: 'id' }"
            :placeholder="$t('请选择行政公司')"
            :disabled="type != 'add'"
            @change="handleCompanyChange"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="companyId" :label="$t('业务公司')" class="apply-item">
          <mt-select
            ref="companyRef1"
            width="350"
            v-model="addForm.companyId"
            :data-source="companyOptions"
            :show-clear-button="false"
            :allow-filtering="true"
            :filtering="getCompanyOptions"
            :fields="{ text: 'labelShow', value: 'id' }"
            :placeholder="$t('请选择业务公司')"
            :disabled="type != 'add'"
            @change="handleCompanyChange1"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="applyDepId" :label="$t('申请部门')">
          <mt-select
            ref="depRef"
            v-model="addForm.applyDepId"
            :data-source="applyDepartData"
            :show-clear-button="false"
            :fields="{ text: 'orgName', value: 'id' }"
            :disabled="type != 'add'"
            :placeholder="$t('请选择申请部门')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="projectDesc" :label="$t('项目')">
          <mt-input
            disabled
            v-model="addForm.projectDesc"
            :placeholder="$t('提交后系统生成')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input
            v-model="addForm.remark"
            :multiline="true"
            :placeholder="$t('备注')"
            :maxlength="200"
            :disabled="type == 'view'"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
// import { cloneDeep } from "lodash";
import { utils } from '@mtech-common/utils'
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    },
    userInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isInit: true, // 是第一次赋值
      isExpand: true,
      type: '', // add view edit

      businessTypeList: [], // 业务类型
      applyUserIdData: [], // 申请人列表
      organizationData: {},
      applyCompanyData: [], // 行政公司
      companyOptions: [], //业务公司
      applyDepartData: [], // 部门
      addForm: {
        title: '', // 采购申请名称
        businessTypeId: '', // 业务类型
        // businessTypeCode: "",
        // businessTypeName: "",
        applyUserId: null, // 申请人
        // applyUserName: "",
        // applyUserCode: ""
        administrationCompanyId: null, // 行政公司
        companyId: '', //业务公司
        // administrationCompanyName: "",
        // administrationCompanyCode : ""
        applyDepId: '', // 申请部门
        // applyDepCode: "",
        // applyDepName: "",
        remark: '',
        projectDesc: '' // 项目名称的聚合
      },
      rules: {
        businessTypeId: [
          {
            required: true,
            message: this.$t('请选择业务类型'),
            trigger: 'blur'
          }
        ],
        applyUserId: [{ required: true, message: this.$t('请选择申请人'), trigger: 'blur' }],
        administrationCompanyId: [
          {
            required: true,
            message: this.$t('请选择行政公司'),
            trigger: 'blur'
          }
        ],
        companyId: [
          {
            required: true,
            message: this.$t('请选择业务公司'),
            trigger: 'blur'
          }
        ],
        applyDepId: [
          {
            required: true,
            message: this.$t('请选择申请部门'),
            trigger: 'blur'
          }
        ]
      },
      isClickCancel: false //手动点击了取消按钮， 为了不再次触发change事件
    }
  },

  computed: {
    // 0-草稿，1. 待审批，2. 审批通过，3. 审批拒绝，4. 关闭
    // !查看状态 && !商城转申请 && (状态为0或3)
    canSave() {
      return (
        this.$route.query.type != 'view' &&
        this.$route.query.source != 2 &&
        this.headerInfo &&
        (this.headerInfo.status == 0 || this.headerInfo.status == 3)
      )
    },
    // !查看状态 && (状态为0或3 || 是 补充信息操作)
    canSubmit() {
      return (
        this.$route.query.type != 'view' &&
        ((this.headerInfo && (this.headerInfo.status == 0 || this.headerInfo.status == 3)) ||
          this.type == 'replanish')
      )
    }
  },

  watch: {
    headerInfo: {
      handler(newVal) {
        if (!this.isInit) return
        console.log('获取到头部信息了，，', newVal)
        this.addForm = {
          ...this.addForm,
          ...newVal
        }
        // this.isClickCancel = true;
        this.isInit = false
        // this.getList();
      }
    },
    'addForm.companyId': {
      handler: function (newVal) {
        let params = {
          companyId: newVal
        }
        if (newVal && this.$refs.companyRef1) {
          this.$nextTick(() => {
            let _data = this.$refs.companyRef1.ejsRef.getDataByValue(newVal)
            console.log('业务公司_data', _data)
            if (_data) {
              params.companyName = _data.orgName
              params.companyCode = _data.orgCode
            }
            this.$store.commit('updateSourceCompanyInfo', params)
          })
        } else {
          this.$store.commit('updateSourceCompanyInfo', params)
        }
      }
    },
    'addForm.applyUserId': {
      handler() {
        console.log('监听到了用户变化了 --watch')
        this.getCompany()
      }
    }
  },

  mounted() {
    this.type = this.$route.query.type
    this.getList()
    //引入mtech-common/utils中的防抖，(mtech-common/utils )
    this.getCompany = utils.debounce(this.getCompany, 300)
    this.getCompanyOptions = utils.debounce(this.getCompanyOptions, 300)
    this.getCompanyOptions({ text: '' })
  },

  methods: {
    getCompanyOptions(e = { text: '' }) {
      //查询业务公司下拉数据
      this.$API.masterData
        .OrgFindSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true,
          organizationIds: [],
          fuzzyParam: e.text
        })
        .then((res) => {
          let companyOptions = res.data
          companyOptions.forEach((item) => {
            item.labelShow = item.orgCode + ' - ' + item.orgName
          })
          this.companyOptions = companyOptions

          this.$nextTick(() => {
            if (e.updateData && typeof e.updateData == 'function') {
              e.updateData(companyOptions)
            }
          })
        })
    },
    startToSave(flag) {
      this.$emit('startToSave', flag)
    },

    // 业务类型变化
    handleBusinessChange(e) {
      console.log(this.$t('业务类型下拉改变'), this.isClickCancel, e)
      // 手动点了取消，会改变值再次触发change事件，此时不需要再弹窗提示了；
      // 如果没有赋过值，即第一次下拉选择，也可以不用手工提示
      if (!this.isClickCancel && e.previousItemData) {
        // 已有过下拉数据，且手动点击的下拉时
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('更换业务类型将重新清空/获取行数据，请确定是否继续？')
          },
          success: () => {
            this.isClickCancel = false
            this.$bus.$emit('changedPRBusinessType', e.itemData, e.previousItemData)
            this.$store.commit('updatePcBusinessCode', e.itemData.itemCode)
          },
          close: () => {
            this.isClickCancel = true
            this.addForm.businessTypeId = e.previousItemData.id
          }
        })
      } else if (!this.isClickCancel && !e.previousItemData) {
        // 新增第一次进入时
        this.isClickCancel = false
        this.$bus.$emit('changedPRBusinessType', e.itemData, e.previousItemData)
        this.$store.commit('updatePcBusinessCode', e.itemData.itemCode)
      } else {
        // 编辑/草稿状态新增时，只改变数据，不提交改变事件
        this.isClickCancel = false
        this.$store.commit('updatePcBusinessCode', e.itemData.itemCode)
      }
    },

    async getList() {
      // 获取业务类型列表
      this.$API.masterData.getDictCode({ dictCode: 'businessType' }).then((res) => {
        if (res.data && res.data.length) {
          this.businessTypeList = res.data
          if (this.$route.query.id && this.headerInfo.businessTypeId) {
            this.addForm.businessTypeId = this.headerInfo.businessTypeId
          } else if (this.$route.query?.source == 2) {
            this.queryMallBusinessType()
          }
          console.log(
            this.$t('获取到了业务类型的数据'),
            this.businessTypeList,
            this.addForm.businessTypeId
          )
        }
      })

      // 获取 用户列表
      await this.$API.masterData.getCurrentTenantEmployees().then((res) => {
        console.log(
          this.$t('获取到了用户列表'),
          this.addForm.applyUserId,
          this.headerInfo?.applyUserId
        )
        const tmp = []
        res.data?.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        this.applyUserIdData = tmp
        if (this.headerInfo?.applyUserId) {
          this.addForm.applyUserId = this.headerInfo.applyUserId
        } else {
          this.addForm.applyUserId = this.applyUserIdData?.find(
            (item) => item.employeeId == this.userInfo?.employeeId
          )?.employeeId
        }
      })
    },
    // 获取 用户列表
    getUser(e) {
      const { text: fuzzyName } = e
      this.applyUserIdData = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        this.applyUserIdData = tmp
      })
    },

    // 获取 商城转申请 的业务类型
    queryMallBusinessType() {
      this.$API.purchaseRequest
        .queryMallBusinessType()
        .then((res) => {
          this.addForm.businessTypeId = res?.data?.businessTypeId || this.businessTypeList[0].id
          this.isClickCancel = false
        })
        .catch(() => {
          this.addForm.businessTypeId = this.businessTypeList[0].id
          this.isClickCancel = false
        })
    },

    // 用户改变
    // async handleUserChange(e) {
    //   console.log("用户改变了---", e);
    //   if (e.itemData != null) {
    //     this.addForm.applyUserId = e.itemData.employeeId;
    //   }
    //   await this.getCompany();
    //   // await this.getDepart();
    // },

    // 获取当前用户下的行政公司
    getCompany(e = { text: '' }) {
      setTimeout(() => {
        if (!this.addForm.applyUserId) {
          return
        }
        this.$API.masterData
          .getOrgCompanysByEmpId({
            employeeId: this.addForm.applyUserId,
            fuzzyParam: e.text
          })
          .then((res) => {
            let applyCompanyData = res.data
            applyCompanyData.forEach((item) => {
              item.labelShow = item.orgCode + ' - ' + item.orgName
            })
            this.applyCompanyData = applyCompanyData
            console.log('applyCompanyData', this.applyCompanyData)
            this.$nextTick(() => {
              if (e.updateData && typeof e.updateData == 'function') {
                e.updateData(applyCompanyData)
              }
            })

            if (this.headerInfo.administrationCompanyId) {
              this.addForm.administrationCompanyId = this.headerInfo.administrationCompanyId
            } else if (this.applyCompanyData && this.applyCompanyData.length == 1) {
              this.addForm.administrationCompanyId = this.applyCompanyData[0].id
            } else if (this.applyCompanyData.length > 1) {
              this.addForm.administrationCompanyId = null
            }
          })
      }, 10)
    },
    //业务公司改变
    handleCompanyChange1() {
      this.$emit('companyChange')
    },
    // 行政公司改变
    handleCompanyChange() {
      this.getDepart()
    },

    // 获取当前用户下公司 的 部门
    getDepart() {
      setTimeout(() => {
        if (!this.addForm.administrationCompanyId) {
          return
        }
        this.$API.masterData
          .getDepartmentsByEmpId({
            employeeId: this.addForm.applyUserId,
            companyOrganizationId: this.addForm.administrationCompanyId
          })
          .then((res) => {
            console.log('applyDepartData', this.applyDepartData)
            this.applyDepartData = res.data

            if (this.headerInfo.applyDepId) {
              this.addForm.applyDepId = this.headerInfo.applyDepId
            } else if (this.applyDepartData && this.applyDepartData.length == 1) {
              this.addForm.applyDepId = this.applyDepartData[0].id
            } else if (this.applyDepartData.length > 1) {
              this.addForm.applyDepId = null
            }
          })
      }, 10)
    },

    // 获取其他数据 code、name
    getOtherInfo(params) {
      // 业务类型、申请人、公司、申请部门
      if (this.addForm.businessTypeId && this.$refs.businessRef) {
        let _data = this.$refs.businessRef.ejsRef.getDataByValue(this.addForm.businessTypeId)
        if (!_data) return
        // console.log("业务类型_data", _data);
        params.businessTypeCode = _data.itemCode
        params.businessTypeName = _data.itemName
      }
      if (this.addForm.applyUserId && this.$refs.userRef) {
        let _data = this.$refs.userRef.$refs.filterSelectRef.ejsRef.getDataByValue(
          this.addForm.applyUserId
        )
        if (!_data) return
        // console.log("申请人_data", _data);
        params.applyUserName = _data.employeeName
        params.applyUserCode = _data.employeeCode
      }
      if (this.addForm.administrationCompanyId && this.$refs.companyRef) {
        let _data = this.$refs.companyRef.ejsRef.getDataByValue(
          this.addForm.administrationCompanyId
        )
        if (!_data) return
        // console.log("行政公司_data", _data);
        params.administrationCompanyName = _data.orgName
        params.administrationCompanyCode = _data.orgCode
      }
      if (this.addForm.companyId && this.$refs.companyRef1) {
        let _data = this.$refs.companyRef1.ejsRef.getDataByValue(this.addForm.companyId)
        if (!_data) return
        // console.log("业务公司_data", _data);
        params.companyName = _data.orgName
        params.companyCode = _data.orgCode
      }
      if (this.addForm.applyDepId && this.$refs.depRef) {
        let _data = this.$refs.depRef.ejsRef.getDataByValue(this.addForm.applyDepId)
        if (!_data) return
        // console.log("部门_data", _data);
        params.applyDepName = _data.orgName
        params.applyDepCode = _data.orgCode
      }
      // console.log("params", params);
      return params
    },

    async confirm() {
      let res = 0
      await this.$refs.ruleForm.validate((valid) => {
        console.log(this.$t('校验'), valid)
        console.log(this.addForm)
        if (valid) {
          let params = {
            ...this.addForm
          }
          params = this.getOtherInfo(params)

          // console.log(this.$t("头部的提交数据"), params);
          res = params
        }
      })
      return res
    },

    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 0 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }

      &.apply-item {
        width: 350px;
      }
    }
  }
}
</style>
