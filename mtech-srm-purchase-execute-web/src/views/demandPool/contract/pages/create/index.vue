<!-- 创建合同 -->
<template>
  <div>
    <FormInput
      :untaxed-total-price="untaxedTotalPrice"
      :taxed-total-price="taxedTotalPrice"
      @save="handleSave"
      @publish="handlePublish"
    />
    <Detail ref="detailRef" :contract-temp-id="contractTempId" @setPrice="setPrice" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import FormInput from './components/FormInput.vue'
import Detail from './components/Detail.vue'

export default {
  components: { FormInput, Detail },
  data() {
    const contractTempId = String(dayjs().unix())
    return {
      contractTempId,
      untaxedTotalPrice: 0,
      taxedTotalPrice: 0
    }
  },
  methods: {
    setPrice(args) {
      const { untaxedTotalPrice, taxedTotalPrice } = args
      this.untaxedTotalPrice = untaxedTotalPrice
      this.taxedTotalPrice = taxedTotalPrice
    },
    handleSave(form) {
      let _this = this
      let params = {
        ...form,
        contractTempId: this.contractTempId,
        createContractDirectlyMtPendingItemDtoList:
          this.$refs.detailRef.templateConfig[0].grid.dataSource
      }
      params.untaxedTotalPrice = this.untaxedTotalPrice
      params.taxedTotalPrice = this.taxedTotalPrice
      params.contractCategoryCode = params.contractCategoryCodeStr
      delete params.contractCategoryCodeStr
      delete params.countryCode

      let valid = params.createContractDirectlyMtPendingItemDtoList.every(
        (item) => item.customProjectName && item.taxRate && item.untaxedUnitPrice > 0
      )
      if (!valid) {
        this.$toast({
          content: this.$t('标的明细项目名称、税率、未税单价不能为空'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认保存草稿？')
        },
        success: () => {
          _this.$API.demandPool.createDraftContractDirectlyApi(params).then((res) => {
            if (res.code === 200) {
              _this.$toast({
                content: res.msg || this.$t('操作成功'),
                type: 'success'
              })
              _this.$router.go(-1)
            }
          })
        }
      })
    },
    handlePublish(form) {
      let _this = this
      let params = {
        ...form,
        contractTempId: this.contractTempId,
        createContractDirectlyMtPendingItemDtoList:
          this.$refs.detailRef.templateConfig[0].grid.dataSource
      }
      params.untaxedTotalPrice = this.untaxedTotalPrice
      params.taxedTotalPrice = this.taxedTotalPrice
      params.contractCategoryCode = params.contractCategoryCodeStr
      delete params.contractCategoryCodeStr
      delete params.countryCode

      let valid = params.createContractDirectlyMtPendingItemDtoList.every(
        (item) => item.customProjectName && item.taxRate && item.untaxedUnitPrice > 0
      )
      if (!valid) {
        this.$toast({
          content: this.$t('标的明细项目名称、税率、未税单价不能为空'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认发布？')
        },
        success: () => {
          _this.$API.demandPool.publishContractDirectlyApi(params).then((res) => {
            if (res.code === 200) {
              _this.$toast({
                content: res.msg || this.$t('操作成功'),
                type: 'success'
              })
              _this.$router.go(-1)
            }
          })
        }
      })
    }
  }
}
</script>
