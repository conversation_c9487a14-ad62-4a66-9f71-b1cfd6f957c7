<template>
  <div class="filter-wrapper">
    <mt-input
      item-label-style="left"
      v-model="valued"
      :disabled="disabled"
      :show-clear-button="true"
      type="text"
      :placeholder="$t('关联合同')"
      @blur="blur"
      @focus="focus"
      @input="input"
    ></mt-input>
    <ul class="uls" v-if="show">
      <li
        v-for="item in contractList"
        :title="item.contractName"
        :key="item.contractId"
        @mousedown="setValued(item)"
      >
        {{ item.contractName }}
      </li>
      <li v-if="contractList.length == 0">{{ $t('暂无数据') }}</li>
    </ul>
  </div>
</template>
<script>
function debounce(func, wait = 1000) {
  // 可以放入项目中的公共方法中进行调用
  let timeout
  return function (event) {
    clearTimeout(timeout)
    timeout = setTimeout(() => {
      func.call(this, event)
    }, wait)
  }
}
export default {
  props: {
    disabled: {
      type: <PERSON><PERSON>an,
      default: () => {
        return false
      }
    },
    value: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      show: false,
      contractList: [],
      valued: '',
      first: true
    }
  },
  methods: {
    focus() {
      this.show = true
    },
    blur() {
      this.show = false
      if (this.contractList.length == 0) {
        this.valued = ''
        this.$emit('input', '')
        this.$bus.$emit('contractNameRelation', '')
      }
    },
    input: debounce(function (e) {
      const value = e
      if (!value) {
        this.contractList = []
        this.$emit('input', '')
        this.$bus.$emit('contractNameRelation', '')
      } else {
        this.contractApi(value)
      }
    }),
    contractApi(value) {
      this.$API.demandPool.getRelatedContractsApi({ contractNameOrId: value }).then((res) => {
        this.contractList = res.data
      })
    },
    setValued(item) {
      this.valued = item.contractName
      this.$emit('input', item.contractId)
      this.$bus.$emit('contractNameRelation', item.contractName)
    },
    getNameById(id) {
      this.$API.demandPool.getPrviewApi(id).then((res) => {
        if (res.data) {
          this.valued = res.data.contractName
          this.contractList = [{ contractId: id, contractName: res.data.contractName }]
        }
      })
    }
  },
  watch: {
    value: {
      handler(n) {
        if (this.first) {
          this.first = false
          if (n) {
            this.getNameById(n)
          } else {
            this.valued = ''
            this.$emit('input', '')
            this.$bus.$emit('contractNameRelation', '')
          }
        }
      },
      immediate: true
    },
    $route: {
      handler() {
        if (this.$route.query?.contractId || this.$route.query?.id) {
          this.first = true
        }
      },
      deep: true
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .search {
  border-color: #d3d3d3;
  height: 22px;
}
.filter-wrapper {
  position: relative;
  z-index: 20;
  .uls {
    width: 100%;
    position: absolute;
    top: 32px;
    left: 0;
    max-height: 300px;
    overflow: auto;
    box-shadow: 0 2px 10px #d3d3d3;
    background: #fff;
    li {
      height: 41px;
      padding-left: 15px;
      font-size: 12px;
      line-height: 41px;
      cursor: pointer;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      &:hover {
        background-color: #f3f3f3;
      }
    }
  }
}
</style>
