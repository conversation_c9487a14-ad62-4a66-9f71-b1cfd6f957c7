<template>
  <div class="upload-wrap">
    <mt-dialog ref="dialog" css-class="create-proj-dialog" :buttons="buttons" :header="header">
      <upload :multiple="false"></upload>
    </mt-dialog>
  </div>
</template>
<script>
import upload from './upload'
export default {
  components: {
    upload
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      signForm: {
        type: '2'
      },
      fileList: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$bus.$on('getFileData', (d) => {
      this.getFileData(d)
    })
  },
  methods: {
    confirm() {
      this.save()
    },
    cancel() {
      this.$emit('cancel-function')
    },

    save() {
      if (this.fileList.length > 0) {
        this.$emit('confirm-function', this.fileList)
      } else {
        this.$toast({
          content: this.$t('请上传文件'),
          type: 'warning'
        })
      }
    },
    getFileData(fileList) {
      this.fileList = fileList
    }
  },
  created() {}
}
</script>
<style lang="scss" scoped></style>
