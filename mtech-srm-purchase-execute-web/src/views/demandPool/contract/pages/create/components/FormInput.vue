<template>
  <div class="form-box">
    <div class="headr-box">
      <div class="headr-btn">
        <span @click="handleBack">{{ $t('返回') }}</span>
        <!-- 供方 -->
        <span @click="handleSave">{{ $t('保存草稿') }}</span>
        <span @click="handlePublish">{{ $t('发布') }}</span>
        <span @click="handleToggle"
          >{{ $t('收起')
          }}<span
            :style="{
              transform: isToggle ? 'rotate(180deg)' : 'rotate(0deg)',
              marginTop: isToggle ? '-5px' : '2px'
            }"
            ><i class="mt-icons mt-icon-MT_DownArrow"></i></span
        ></span>
      </div>
      <div v-if="!isToggle" class="headr-border"></div>
    </div>
    <mt-form ref="ruleForm" :model="formObject" :rules="formRules" class="form-input">
      <div class="demo-block">
        <mt-form-item :label="$t('合同编号')" prop="contractCode" class="form-item label-style">
          <mt-input
            v-model="formObject.contractCode"
            :disabled="true"
            :placeholder="$t('保存后自动生成')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          :label="$t('合同名称')"
          prop="contractName"
          class="form-item"
          style="color: #f0f"
        >
          <mt-input
            v-model="formObject.contractName"
            :show-clear-button="true"
            maxlength="64"
            :placeholder="$t('请输入合同名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('供应商编号')" prop="supplierCode" class="form-item">
          <RemoteAutocomplete
            v-model="formObject.supplierCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
            :rule-params="[
              {
                condition: 'and',
                field: 'statusId',
                type: 'string',
                operator: 'contains',
                value: 10
              },
              {
                condition: 'and',
                field: 'organizationCode',
                type: 'string',
                operator: 'contains',
                value: this.formObject.purCompanyCode
              }
            ]"
            @change="supplierChange"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供应商名称')" prop="supplierName" class="form-item label-style">
          <mt-input
            v-model="formObject.supplierName"
            :disabled="true"
            :show-clear-button="true"
            :placeholder="$t('请选择供应商')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('合同类别')" prop="contractCategoryCode" class="form-item">
          <mt-DropDownTree
            v-if="fieldsStatus"
            id="baseTreeSelect"
            v-model="formObject.contractCategoryCode"
            :allow-filtering="true"
            :popup-height="300"
            :fields="fields"
            @select="selectTree"
            :placeholder="$t('请选择合同类别')"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item :label="$t('采购组织')" prop="purOrgId" class="form-item">
          <mt-select
            v-model="formObject.purOrgId"
            :show-clear-button="true"
            :allow-filtering="true"
            :data-source="purOrgOptions"
            :placeholder="$t('请选择')"
            @change="purOrgChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('公司')" prop="purCompanyName" class="form-item label-style">
          <mt-input v-model="formObject.purCompanyName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('采购组')" prop="purchaseGroupId" class="form-item">
          <mt-select
            v-model="formObject.purchaseGroupId"
            :show-clear-button="true"
            :allow-filtering="true"
            :data-source="purchaseGroupOptions"
            :placeholder="$t('请选择采购组')"
            @change="purchaseGroupChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('关联合同/协议')" prop="contractIdRelation" class="form-item">
          <filterSearch v-model="formObject.contractIdRelation"></filterSearch>
        </mt-form-item>
        <mt-form-item :label="$t('合同类型')" prop="contractTypeId" class="form-item">
          <mt-select
            v-model="formObject.contractTypeId"
            :data-source="contractTypeOptions"
            :placeholder="$t('请选择合同类型')"
            :show-clear-button="true"
            @change="contractTypeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('币种')" prop="currencyName" class="form-item label-style">
          <mt-input v-model="currencyName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item
          v-if="showPrice"
          :label="$t('合同总金额（未税）')"
          prop="untaxedTotalPrice"
          class="form-item label-style"
        >
          <mt-input v-model="untaxedTotalPrice" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item
          v-if="showPrice"
          :label="$t('合同总金额（含税）')"
          prop="taxedTotalPrice"
          class="form-item label-style"
        >
          <mt-input v-model="taxedTotalPrice" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('合同生效时间')" prop="effectiveBeginTime" class="form-item">
          <mt-date-time-picker
            v-model="formObject.effectiveBeginTime"
            :placeholder="$t('请选择日期和时间')"
            format="yyyy-MM-dd HH:mm"
          ></mt-date-time-picker>
        </mt-form-item>
        <mt-form-item :label="$t('合同终止时间')" prop="effectiveEndTime" class="form-item">
          <mt-date-time-picker
            v-model="formObject.effectiveEndTime"
            :placeholder="$t('请选择日期和时间')"
            format="yyyy-MM-dd HH:mm"
          ></mt-date-time-picker>
        </mt-form-item>
        <mt-form-item :label="$t('供应商联系人')" prop="supplierContactName" class="form-item">
          <mt-select
            v-model="formObject.supplierContactName"
            :data-source="supplierContactOptions"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
            @change="supplierContactChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('供应商联系电话')" prop="supplierContactPhone" class="form-item">
          <mt-input
            v-model="formObject.supplierContactPhone"
            :show-clear-button="true"
            :placeholder="$t('请输入供应商联系电话')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('供应商邮箱')" prop="supplierContactMail" class="form-item">
          <mt-input
            v-model="formObject.supplierContactMail"
            :show-clear-button="true"
            :placeholder="$t('请输入供应商邮箱')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('供应商备用邮箱')" prop="supplierSpareEmail" class="form-item">
          <mt-input
            v-model="formObject.supplierSpareEmail"
            :show-clear-button="true"
            :placeholder="$t('请输入供应商备用邮箱')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          v-if="formObject.contractCategoryCode && contractCategoryItemCode !== '41'"
          :label="$t('付款条件')"
          prop="paymentItemId"
          class="form-item"
        >
          <mt-select
            v-model="formObject.paymentItemId"
            :data-source="paymentOptions"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
            @change="paymentChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          v-if="formObject.contractCategoryCode && contractCategoryItemCode !== '41'"
          :label="$t('账期天数')"
          prop="accountPeriod"
          class="form-item"
        >
          <mt-inputNumber
            v-model="formObject.accountPeriod"
            :show-clear-button="true"
            :min="1"
            :placeholder="$t('请输入账期天数')"
          ></mt-inputNumber>
        </mt-form-item>
        <mt-form-item :label="$t('合同模板')" prop="contractTemplateId" class="form-item">
          <mt-select
            v-model="formObject.contractTemplateId"
            :data-source="contractTemplateOptions"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
            @change="contractTemplateChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('是否立项')" prop="established" class="form-item">
          <mt-select
            v-model="formObject.established"
            :data-source="establishedOptions"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('是否关联公司')" prop="isRelationCompany" class="form-item">
          <mt-select
            v-model="formObject.isRelationCompany"
            :data-source="isRelationCompanyOptions"
            :show-clear-button="true"
            :placeholder="$t('是否关联公司')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('采购员')" prop="purId" class="form-item" style="width: 450px">
          <mt-select
            v-model="formObject.purId"
            :data-source="purOptions"
            :show-clear-button="true"
            :allow-filtering="true"
            :placeholder="$t('请选择采购员')"
            @change="purChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('质保期')" prop="qaTimeLimitValue" class="form-item">
          <mt-date-time-picker
            v-model="formObject.qaTimeLimitValue"
            :min="new Date(new Date().getTime() + 86400000)"
            :placeholder="$t('请选择质保期')"
            format="yyyy-MM-dd HH:mm"
          ></mt-date-time-picker>
        </mt-form-item>
        <div class="demo-content">
          <mt-form-item :label="$t('税费承担描述')" prop="taxesDesc" class="item-content">
            <mt-input
              v-model="formObject.taxesDesc"
              :placeholder="$t('税费承担描述内容')"
              :show-clear-button="true"
              :rows="2"
              maxlength="128"
            ></mt-input>
          </mt-form-item>
          <mt-form-item :label="$t('备注')" prop="memo" class="item-content">
            <mt-input
              v-model="formObject.memo"
              :placeholder="$t('备注内容')"
              :show-clear-button="true"
              :rows="2"
              maxlength="128"
            ></mt-input>
          </mt-form-item>
        </div>
      </div>
    </mt-form>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import filterSearch from '../components/filterSearch.vue'
export default {
  props: {
    untaxedTotalPrice: {
      type: Number,
      default: 0
    },
    taxedTotalPrice: {
      type: Number,
      default: 0
    }
  },
  components: { filterSearch },
  data() {
    const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
    return {
      employeeId: userInfo.employeeId,
      isToggle: true,
      formObject: {
        countryCode: 'CN'
      },
      formRules: {
        contractName: [{ required: true, message: this.$t('请输入合同名称'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }],
        contractCategoryCode: [
          { required: true, message: this.$t('请选择合同类别'), trigger: 'blur' }
        ],
        purOrgId: [{ required: true, message: this.$t('请选择采购组织'), trigger: 'blur' }],
        purchaseGroupId: [{ required: true, message: this.$t('请选择采购组'), trigger: 'blur' }],
        contractTypeId: [{ required: true, message: this.$t('请选择合同类型'), trigger: 'blur' }],
        effectiveBeginTime: [{ required: true, validator: this.beginTimeRules, trigger: 'blur' }],
        effectiveEndTime: [{ required: true, validator: this.endTimeRules, trigger: 'blur' }],
        supplierContactName: [
          { required: true, message: this.$t('请选择供应商联系人'), trigger: 'blur' }
        ],
        supplierContactPhone: [
          { required: true, validator: this.validatePhoneNum, trigger: 'blur' }
        ],
        supplierContactMail: [{ required: true, validator: this.validateEmail, trigger: 'blur' }],
        supplierSpareEmail: [{ validator: this.validateEmail, trigger: 'blur' }],
        accountPeriod: [{ required: true, message: this.$t('请输入账期天数'), trigger: 'blur' }],
        contractTemplateId: [
          { required: true, message: this.$t('请选择合同模板'), trigger: 'blur' }
        ],
        purId: [{ required: true, message: this.$t('请选择采购员'), trigger: 'blur' }],
        qaTimeLimitValue: [{ required: true, message: this.$t('请选择质保期'), trigger: 'blur' }]
      },

      fields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      fieldsStatus: false,
      recursionData: [],
      recursionObj: [],
      contractCategoryItemCode: null,

      purOrgOptions: [],
      purchaseGroupOptions: [],
      contractTypeOptions: [],

      showPrice: true,

      supplierContactOptions: [],
      paymentOptions: [],
      contractTemplateOptions: [],
      establishedOptions: [],
      isRelationCompanyOptions: [
        { text: this.$t('否'), value: 0 },
        { text: this.$t('是'), value: 1 }
      ],
      purOptions: []
    }
  },
  computed: {
    currencyName() {
      return this.$route.query?.currencyName
    }
  },
  created() {
    this.getHeader()
  },
  mounted() {
    // 关联合同名称
    this.$bus.$on('contractNameRelation', (data) => {
      this.formObject.contractNameRelation = data
    })
  },
  methods: {
    getHeader() {
      if (!this.$route.query.id) return
      let ids = this.$route.query.id.split(',')
      this.$API.purchaseRequest.getHeaderInfo({ headerId: ids[0] }).then((res) => {
        if (res.code === 200) {
          this.$set(this, 'formObject', {
            supplierEnterpriseId: null,
            supplierEnterpriseCode: null,
            supplierEnterpriseName: null,
            businessTypeId: res.data.businessTypeId, // 业务类型
            businessTypeCode: res.data.businessTypeCode,
            businessTypeName: res.data.businessTypeName,
            purOrgId: null, // 采购组织
            purOrgCode: null, // 采购组织编码
            purOrgName: null, // 采购组织名称
            purCompanyId: res.data.companyId, // 公司公司id
            purCompanyCode: res.data.companyCode, // 公司编码
            purCompanyName: res.data.companyName, // 公司名称
            countryCode: 'CN', // 国家code
            purchaseGroupId: res.data.buyerGroupId, // 采购组id
            purchaseGroupCode: res.data.buyerGroupCode, // 采购组编码
            purchaseGroupName: res.data.buyerGroupName, // 采购组名称
            contractIdRelation: null, // 关联合同
            untaxedTotalPrice: null, // 合同总金额（未税）
            taxedTotalPrice: null, // 合同总金额（含税）
            purId: res.data.purchaserId, // 采购员id
            purName: res.data.purchaserName // 采购员Name
          })
          const fullYearLastDay = dayjs().endOf('year').valueOf()
          this.formObject.effectiveEndTime = dayjs(fullYearLastDay).format('YYYY-MM-DD HH:mm:ss')
          this.formObject.currencyCode = this.$route.query?.currencyCode
          this.formObject.currencyName = this.currencyName
          this.formObject.established = 0
          this.formObject.isRelationCompany = 0
          this.getPurOrgOptions()
          this.contractTypeTree()
          this.getPurchaseGroupOptions()
          this.getContractTypeOptions()
          this.getPaymentOptions()
          this.getContractTemplateOptions()
          this.getEstablishedOptions()
          this.getPurOptions()
        }
      })
    },
    beginTimeRules(rule, value, callback) {
      const effective = new Date(this.formObject.effectiveEndTime).getTime()
      if (!value) {
        callback(new Error(this.$t('请选择生效时间')))
      } else if (effective < new Date(value).getTime() && this.formObject.effectiveEndTime) {
        callback(new Error(this.$t('生效时间不能大于终止时间')))
      } else {
        callback()
      }
    },
    endTimeRules(rule, value, callback) {
      const effective = new Date(this.formObject.effectiveBeginTime).getTime()
      if (!value) {
        callback(new Error(this.$t('请选择终止时间')))
      } else if (effective > new Date(value).getTime() && this.formObject.effectiveBeginTime) {
        callback(new Error(this.$t('终止时间不能小于生效时间')))
      } else {
        callback()
      }
    },
    validatePhoneNum(rule, value, callback) {
      const phoneRegex = /^1[3-9]\d{9}$/
      let isUnValidMobile = this.formObject.countryCode !== 'CN'
      if (value && !phoneRegex.test(value) && !isUnValidMobile) {
        callback(new Error(this.$t('手机号格式不正确')))
      } else {
        callback()
      }
    },
    validateEmail(rule, value, callback) {
      const emailRegex =
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      if (value && !emailRegex.test(value)) {
        callback(new Error(this.$t('邮箱地址格式不正确')))
      } else {
        callback()
      }
    },
    supplierChange(e) {
      if (e.itemData) {
        this.formObject.supplierCode = e.itemData.supplierCode
        this.formObject.supplierName = e.itemData.supplierName
        this.formObject.supplierEnterpriseId = e.itemData.supplierEnterpriseId
        this.formObject.supplierEnterpriseCode = e.itemData.supplierCode
        this.formObject.supplierEnterpriseName = e.itemData.supplierName
        this.getSupplierContactOptions(e.itemData.partnerCode)
        this.getCountryCode(e.itemData.supplierEnterpriseId)
      }
    },
    getCountryCode(supplierEnterpriseId) {
      this.$API.demandPool.getCountryCodeByIdApi(supplierEnterpriseId).then((res) => {
        if (res.code === 200) {
          this.formObject.countryCode = res.data.countryCode
        }
      })
    },
    contractTypeTree() {
      this.$API.masterData.getDictItemTree({ dictCode: 'contractCategory' }).then((res) => {
        if (res.code === 200) {
          this.fields.dataSource = res.data
          this.formObject.contractCategoryCodeStr = ''
          this.formObject.contractCategoryName = ''
          this.recursionTree(this.fields.dataSource)
          this.fieldsStatus = true
        }
      })
    },
    recursionTree(data) {
      data.forEach((item) => {
        if (
          item.itemCode === '41' &&
          (!this.formObject.contractCategoryCode || !this.formObject.contractCategoryCode?.length)
        ) {
          this.$set(this.formObject, 'contractCategoryCode', [item.id])
          this.$set(this.formObject, 'contractHroStatus', 0)
          if (item.id) {
            this.formObject.contractCategoryCodeStr += item.id
            this.formObject.contractCategoryName += item.name
          }
          this.contractCategoryItemCode = '41'
        }
        this.recursionData.push(item)
        if (item.children) {
          this.recursionTree(item.children)
        }
      })
    },
    // 选中合同类别
    selectTree(data) {
      this.recursionObj = []
      this.formObject.contractCategoryCodeStr = ''
      this.formObject.contractCategoryName = ''
      this.recursionObj.push(data.itemData)
      this.recursionId(data.itemData.parentID)
      this.recursionObj = this.recursionObj.reverse()
      this.recursionData.forEach((i) => {
        if (data.itemData.id === i.id) {
          this.contractCategoryItemCode = i.itemCode
          if (i.itemCode === '42') {
            this.formObject.contractHroStatus = 1
          } else {
            this.formObject.contractHroStatus = 0
          }
        }
      })
      this.recursionObj.forEach((item, index) => {
        if (index !== this.recursionObj.length - 1) {
          this.formObject.contractCategoryCodeStr += item.id + '-'
          this.formObject.contractCategoryName += item.name + '-'
        } else {
          this.formObject.contractCategoryCodeStr += item.id
          this.formObject.contractCategoryName += item.text
        }
      })
    },
    // 获取选中合同类别的父级数据  递归
    recursionId(id) {
      this.recursionData.forEach((item) => {
        if (item.id === id) {
          this.recursionObj.push(item)
          if (item.parentId !== '0') {
            this.recursionId(item.parentId)
          }
        }
      })
    },
    // 获取采购组织
    getPurOrgOptions() {
      this.$API.masterData
        .permissionOrgList({
          orgId: this.formObject.purCompanyId
        })
        .then((res) => {
          if (res.code === 200) {
            this.purOrgOptions = res.data.map((item) => {
              return {
                text: item.organizationName,
                value: item.id,
                code: item.organizationCode
              }
            })
          }
        })
    },
    purOrgChange(e) {
      this.formObject.purOrgName = e.itemData?.text
      this.formObject.purOrgCode = e.itemData?.code
    },
    // 获取采购组
    getPurchaseGroupOptions() {
      this.$API.masterData.getbussinessGroup({ groupTypeCode: 'BG001CG' }).then((res) => {
        if (res.code === 200) {
          this.purchaseGroupOptions = res.data.map((item) => {
            return {
              text: item.groupName,
              value: item.id,
              code: item.groupCode
            }
          })
          this.formObject.purchaseGroupId = this.purchaseGroupOptions.find(
            (v) => v.code === 'F01'
          ).value
          if (!this.formObject.purchaseGroupId) {
            this.formObject.purchaseGroupId = this.purchaseGroupOptions[0].value
          }
        }
      })
    },
    purchaseGroupChange(e) {
      this.formObject.purchaseGroupName = e.itemData?.text
      this.formObject.purchaseGroupCode = e.itemData?.code
    },
    getContractTypeOptions() {
      this.$API.masterData.getDictItemTree({ dictCode: 'contractType' }).then((res) => {
        if (res.code === 200) {
          this.contractTypeOptions = res.data.map((item) => {
            return {
              text: item.name,
              value: item.id
            }
          })
          this.formObject.contractTypeId = this.contractTypeOptions[0].value
        }
      })
    },
    contractTypeChange(e) {
      this.formObject.contractTypeName = e.itemData?.text
      if (this.formObject.contractTypeName === this.$t('框架协议')) {
        this.showPrice = false
      } else {
        this.showPrice = true
      }
    },
    getSupplierContactOptions(partnerCode) {
      const params = {
        supplierCode: partnerCode
      }
      this.$API.demandPool.getSupplierContactApi(params).then((res) => {
        if (res.code === 200) {
          this.supplierContactOptions = res.data.map((item) => {
            return {
              text: `${item.name}-${item.duty}`,
              value: item.name,
              mobile: item.mobile,
              email: item.email
            }
          })
        }
      })
    },
    supplierContactChange(e) {
      this.formObject.supplierContactPhone = e.itemData?.mobile
      this.formObject.supplierContactMail = e.itemData?.email
    },
    getPaymentOptions() {
      this.$API.masterData.getDictItemTree({ dictCode: 'PaymentType' }).then((res) => {
        if (res.code === 200) {
          this.paymentOptions = res.data.map((item) => {
            return {
              text: item.name,
              value: item.id
            }
          })
          this.formObject.paymentItemId = this.paymentOptions[0].value
        }
      })
    },
    paymentChange(e) {
      this.formObject.paymentItemName = e.itemData?.text
    },
    getContractTemplateOptions() {
      this.$API.demandPool.getContractTemplateApi().then((res) => {
        if (res.code === 200) {
          this.contractTemplateOptions = res.data.map((item) => {
            return {
              text: item.label,
              value: item.value
            }
          })
          this.formObject.contractTemplateId = this.contractTemplateOptions[0].value
        }
      })
    },
    contractTemplateChange(e) {
      this.formObject.contractTemplateName = e.itemData?.text
    },
    getEstablishedOptions() {
      this.$API.demandPool.getEstablishedApi().then((res) => {
        if (res.code === 200) {
          this.establishedOptions = res.data.map((item) => {
            return {
              text: item.label,
              value: item.value
            }
          })
        }
      })
    },
    getPurOptions() {
      const params = {
        fuzzyName: ''
      }
      this.$API.masterData.getCurrentTenantEmployees(params).then((res) => {
        if (res.code === 200) {
          this.purOptions = res.data.map((item) => {
            if (item?.employeeId == this.employeeId) {
              this.formObject.purId = item.uid
            }
            return {
              text: `${item.employeeCode}-${item.employeeName}`,
              value: item.uid,
              employeeName: item.employeeName
            }
          })
        }
      })
    },
    purChange(e) {
      this.formObject.purName = e.itemData?.employeeName
    },

    handleBack() {
      this.$router.go(-1)
    },
    handleSave() {
      let _this = this
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          params.effectiveBeginTime = dayjs(params.effectiveBeginTime).format(
            'YYYY-MM-DD[T]HH:mm:ss[Z]'
          )
          params.effectiveEndTime = dayjs(params.effectiveEndTime).format(
            'YYYY-MM-DD[T]HH:mm:ss[Z]'
          )
          params.qaTimeLimitValue = dayjs(params.qaTimeLimitValue).valueOf()
          _this.$emit('save', params)
        }
      })
    },
    handlePublish() {
      let _this = this
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          params.effectiveBeginTime = dayjs(params.effectiveBeginTime).format(
            'YYYY-MM-DD[T]HH:mm:ss[Z]'
          )
          params.effectiveEndTime = dayjs(params.effectiveEndTime).format(
            'YYYY-MM-DD[T]HH:mm:ss[Z]'
          )
          params.qaTimeLimitValue = dayjs(params.qaTimeLimitValue).valueOf()
          _this.$emit('publish', params)
        }
      })
    },
    handleToggle() {
      if (this.isToggle) {
        this.$refs.ruleForm.$el.style.height = '0px'
        this.$refs.ruleForm.$el.style.transition = 'height 0.2s'
        this.$refs.ruleForm.$el.style.overflow = 'hidden'
        this.$refs.ruleForm.$el.style.marginTop = '0px'
      } else {
        this.$refs.ruleForm.$el.style.height = '363px'
        this.$refs.ruleForm.$el.style.transition = 'height 0.2s'
        this.$refs.ruleForm.$el.style.marginTop = '45px'
      }
      this.isToggle = !this.isToggle
    }
  }
}
</script>

<style lang="scss" scoped>
.form-box {
  width: 100%;
  padding: 20px 0 20px 0;
  background: #fff;
  .headr-box {
    margin-bottom: 25px;
    padding: 0 20px 0 20px;
    display: flex;
    justify-content: flex-end;
    .span-status {
      display: inline-block;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      padding: 2px;
      border-radius: 2px;
      margin-right: 20px;
    }
    .cancel {
      color: #eda133;
      background: rgba(237, 161, 51, 0.1);
    }
    .green {
      color: #8acc40;
      background: rgba(138, 204, 64, 0.1);
    }
    .red {
      color: #ed5633;
      background: rgba(237, 86, 51, 0.1);
    }
    // .ash {
    //   color: #9a9a9a;
    //   background: rgba(154, 154, 154, 0.1);
    // }
    .blue {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
    .headr-text {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }
    .headr-time {
      margin-left: 13px;
    }
    .headr-btn {
      display: flex;
      span {
        display: inline-block;
        margin-left: 40px;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      span:nth-of-type(1) {
        margin-left: 0;
      }
      span:nth-last-of-type(1) {
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(99, 134, 193, 1);
      }
    }
  }
  .headr-border {
    border-top: 1px solid#E8E8E8;
    height: 20px;
  }
  .form-input {
    // height: 389px;
    padding: 0 20px 0 30px;
    margin-top: 45px;
  }
}
.demo-block {
  display: flex;
  // margin-bottom: 10px;
  flex-wrap: wrap;
  .demo-input {
    margin-bottom: 10px;
    margin-left: 10px;
  }
  .title-text {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(154, 154, 154, 1);
  }
  .form-item {
    width: 225px;
    margin-bottom: 15px;
    margin-right: 10px;
  }
}
.demo-content {
  width: 100%;
  display: flex;
  .item-content {
    width: calc(50% - 10px);
  }
  .item-content:nth-of-type(2) {
    margin-left: 20px;
  }
}
.input-item {
  padding-right: 20px;
}
.col-xs-12,
.col-sm-12,
.col-lg-6,
.col-md-6 {
  margin-bottom: 15px;
}
/deep/ input.e-disabled {
  min-height: 30px !important;
  height: 30px;
  background: #fff !important;
}
/deep/ span.e-disabled {
  height: 31px;
  background: #fff !important;
}
/deep/ span.e-multi-line-input {
  height: auto !important;
  background: #fff !important;
}
/deep/ textarea.e-disabled {
  background: #fff !important;
}
/deep/ .label {
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: normal;
  color: rgba(41, 41, 41, 1);
}

.label-style /deep/ .label {
  color: rgba(154, 154, 154, 1);
}
.label-style /deep/#baseTreeSelect {
  color: rgba(154, 154, 154, 1);
}
.accept {
  color: #8acc40 !important;
}
.reject {
  color: #ed5633 !important;
}
</style>
