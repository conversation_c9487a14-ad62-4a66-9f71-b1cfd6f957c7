<template>
  <div class="buyerContract-wrap">
    <mt-template-page
      ref="tempaltePageRef"
      :template-config="templateConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @actionComplete="actionComplete"
    >
      <mt-template-page
        slot="slot-1"
        ref="templateRef1"
        :current-tab="currentTab"
        :template-config="FilePageConfig"
        @handleSelectTab="handleSelectTabSlot"
        @handleClickToolBar="handleClickToolBarSlot"
        @handleClickCellTool="handleClickCellToolSlot"
        @handleClickCellTitle="handleClickCellTitleSlot"
      ></mt-template-page>
    </mt-template-page>
  </div>
</template>

<script>
import { TemplateConfig, FilePageConfig } from '../config/detail.js'
import bigDecimal from 'js-big-decimal'
export default {
  props: {
    contractTempId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      templateConfig: TemplateConfig(this),
      FilePageConfig: FilePageConfig(this.contractTempId),
      currentTab: 0,

      taxRateOptions: []
    }
  },
  created() {
    this.getDetail()
    this.getTaxRateOptions()
  },
  methods: {
    getTaxRateOptions() {
      let params = {
        page: {
          current: 1,
          size: 200
        },
        condition: 'and',
        rules: []
      }
      this.$API.masterData.pageTaxItemApi(params).then((res) => {
        if (res.code === 200) {
          this.taxRateOptions = res.data.records
        }
      })
    },
    actionComplete(args) {
      const { requestType, data, rowIndex } = args
      if (requestType === 'save') {
        this.$set(this.$refs.tempaltePageRef.templateConfig[0].grid.dataSource, rowIndex, data)
        let untaxedTotalPrice = 0,
          taxedTotalPrice = 0
        this.$refs.tempaltePageRef.templateConfig[0].grid.dataSource.forEach((item) => {
          untaxedTotalPrice = bigDecimal.add(untaxedTotalPrice, item.untaxedTotalPrice)
          taxedTotalPrice = bigDecimal.add(taxedTotalPrice, item.taxedTotalPrice)
        })
        this.$emit('setPrice', { untaxedTotalPrice, taxedTotalPrice })
      }
    },
    getDetail() {
      if (!this.$route.query.id) return
      let ids = this.$route.query.id.split(',')
      let promises = []
      const getDataById = (id) => {
        return new Promise((resolve) => {
          let params = {
            docId: id,
            moduleKey: '015ed05f-d3f3-11eb-96d2-0242ac130001', // 需求明细
            requestParams: {
              defaultRules: [],
              page: {
                current: 1,
                size: 9999
              }
            }
          }
          this.$API.purchaseRequest.getModuleData(params).then((res) => {
            if (res.code === 200) {
              resolve(res.data?.itemDataList?.records)
            }
          })
        })
      }
      ids.forEach((id) => {
        promises.push(getDataById(id))
      })
      Promise.all(promises).then((res) => {
        let _dataSource = []
        res.forEach((arr) => {
          arr.forEach((v) => {
            _dataSource.push({
              purRequestCode: v.requestCode,
              lineNo: v.itemNo,
              purchaseRequestNo: v.itemNo,
              categoryId: v.categoryId,
              categoryCode: v.categoryCode,
              categoryName: v.categoryName,
              customProjectName: null,
              purRequirementName: v.fieldDataList.find((e) => e.fieldCode === 'requireName')
                ?.fieldData,
              purRequirementDesc: v.fieldDataList.find((e) => e.fieldCode === 'requireDesc')
                ?.fieldData,
              purRequirementNum: v.quantity,
              siteId: v.siteId,
              siteCode: v.siteCode,
              siteName: v.siteName,
              priceUnit: 1,
              unitId: v.unitId,
              unitCode: v.unitCode,
              unitName: v.unitName,
              conversionRate: 1,
              taxRate: null,
              stepQuote: 0,
              stageType: 0,
              untaxedUnitPrice: null,
              untaxedTotalPrice: null,
              taxedUnitPrice: null,
              taxedTotalPrice: null
            })
          })
        })
        this.$set(this.templateConfig[0].grid, 'dataSource', _dataSource)
      })
    },
    handleClickToolBar() {},
    handleClickCellTool() {},
    handleClickCellTitle(e) {
      const { field, data } = e
      if (field === 'stageList') {
        this.$refs.dialog.ejsRef.show()
        this.dialogPageConfig[0].grid.dataSource = data.stageList
      }
    },
    handleSelectTabSlot(e) {
      this.currentTab = e
    },
    handleClickToolBarSlot(e) {
      const { grid, toolbar } = e
      const sections = grid.getSelectedRecords()
      if (toolbar.id === 'upload') {
        if (!this.contractTempId) {
          this.$toast({
            content: this.$t('数据异常'),
            type: 'warning'
          })
          return
        }
        this.uploadFile()
      }
      if (sections.length === 0 && toolbar.id !== 'upload') {
        this.$toast({
          content: this.$t('请选择一条数据'),
          type: 'warning'
        })
        return
      }
      if (toolbar.id === 'del') {
        this.deleteFile(sections)
      }
      if (toolbar.id === 'download') {
        this.download(sections)
      }
    },
    uploadFile() {
      this.$dialog({
        modal: () => import('./uploadDialog.vue'),
        data: {
          title: this.$t('上传附件')
        },
        success: (data) => {
          this.requestFile(data)
        }
      })
    },
    requestFile(files) {
      const formData = new FormData()
      let contractId = this.contractTempId
      let isContractTempId = '1'
      let providerType = 0
      formData.append('contractId', contractId)
      formData.append('isContractTempId', isContractTempId)
      formData.append('provider', providerType)
      formData.append('file', files[0])
      this.$store.commit('startLoading')
      this.$API.demandPool.uploadContractApi(formData).then((res) => {
        this.$store.commit('endLoading')
        if (res.code == 200) {
          this.$toast({
            content: this.$t('上传成功'),
            type: 'success'
          })
          this.$refs.templateRef1.refreshCurrentGridData()
        }
      })
    },
    deleteFile(sections) {
      sections.map((item) => {
        this.$API.demandPool.delContractApi(item.id).then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef1.refreshCurrentGridData()
          }
        })
      })
    },

    download(sections) {
      sections.map((item) => {
        this.$API.demandPool.downloadContractApi(item.id).then((res) => {
          const filename = item.fileName
          const url = window.URL.createObjectURL(new Blob([res.data]))
          const link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.download = filename
          document.body.appendChild(link)
          link.click()
          URL.revokeObjectURL(link.href) // 释放URL 对象
          document.body.removeChild(link)
        })
      })
    },
    handleClickCellToolSlot(e) {
      const { tool, data } = e
      if (tool.id == 'del') {
        this.deleteFile([data])
      }
      if (tool.id == 'download') {
        this.download([data])
      }
    },
    handleClickCellTitleSlot(e) {
      if (e.field == 'fileName') {
        let params = {
          id: e.data.sysFileId || e.data.id,
          useType: 1
        }
        this.$API.fileService.filePreview(params).then((res) => {
          window.open(res.data)
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.buyerContract-wrap {
  height: 500px;
}
</style>
