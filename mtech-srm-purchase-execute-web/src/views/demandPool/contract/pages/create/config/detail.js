import { i18n } from '@/main.js'
import Vue from 'vue'
import dayjs from 'dayjs'
import bigDecimal from 'js-big-decimal'

const columnData = (that) => {
  return [
    {
      field: 'lineNo',
      headerText: i18n.t('行号'),
      allowEditing: false
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料/品相编码'),
      allowEditing: false
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料/品相名称'),
      allowEditing: false
    },
    {
      field: 'categoryName',
      headerText: i18n.t('品类名称'),
      allowEditing: false
    },
    {
      field: 'spec',
      headerText: i18n.t('规格型号'),
      allowEditing: false
    },
    {
      field: 'customProjectName',
      headerText: i18n.t('项目名称'),
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('项目名称')}}</span>
                </div>
              `
          })
        }
      },
      providedEditor: true,
      editorParams: {
        type: 'input',
        disabled: false
      }
    },
    {
      field: 'purRequirementName',
      headerText: i18n.t('需求名称'),
      allowEditing: false
    },
    {
      field: 'purRequirementDesc',
      headerText: i18n.t('需求描述'),
      allowEditing: false
    },
    {
      field: 'purRequirementNum',
      headerText: i18n.t('需求数量'),
      allowEditing: false
    },
    {
      field: 'siteName',
      headerText: i18n.t('地点/工厂'),
      allowEditing: false
    },
    {
      field: 'priceUnit',
      headerText: i18n.t('价格单位'),
      allowEditing: false
    },
    {
      field: 'unitName',
      headerText: i18n.t('基本单位'),
      allowEditing: false
    },
    {
      field: 'purUnitName',
      headerText: i18n.t('采购单位名称'),
      allowEditing: false
    },
    {
      field: 'conversionRate',
      headerText: i18n.t('转换率（%）'),
      valueConverter: {
        type: 'function',
        filter: (data) => {
          if (!data) return
          return data * 100
        }
      },
      allowEditing: false
    },
    {
      field: 'taxRate',
      headerText: i18n.t('税率（%）'),
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('税率（%）')}}</span>
                </div>
              `
          })
        }
      },
      valueConverter: {
        type: 'function',
        filter: (data) => {
          if (data === null || data === '') return
          return bigDecimal.multiply(data, 100)
        }
      },
      editorRender(h, scoped) {
        return (
          <div>
            <mt-select
              v-model={scoped.taxId}
              fields={{ text: 'taxItemName', value: 'id' }}
              dataSource={that.taxRateOptions || []}
              allow-filtering={true}
              filter-type='Contains'
              popup-width={250}
              placeholder={i18n.t('请选择')}
              onChange={(e) => {
                const { taxRate, taxItemCode, taxItemName } = e.itemData
                scoped.taxRate = taxRate
                scoped.taxCode = taxItemCode
                scoped.taxName = taxItemName
                scoped.untaxedTotalPrice = bigDecimal.multiply(
                  scoped.untaxedUnitPrice,
                  scoped.purRequirementNum
                )
                scoped.taxedUnitPrice = bigDecimal.multiply(
                  scoped.untaxedUnitPrice,
                  bigDecimal.add(1 + scoped.taxRate)
                )
                scoped.taxedTotalPrice = bigDecimal.multiply(
                  scoped.taxedUnitPrice,
                  scoped.purRequirementNum
                )
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'stepQuote',
      headerText: i18n.t('是否阶梯'),
      valueConverter: {
        type: 'map',
        fields: { text: 'label', value: 'status' },
        map: {
          0: i18n.t('否'),
          1: i18n.t('是')
        }
      },
      allowEditing: false
    },
    {
      field: 'stageType',
      headerText: i18n.t('阶梯方式'),
      valueConverter: {
        type: 'map',
        fields: { text: 'label', value: 'status' },
        map: {
          0: i18n.t('数量'),
          1: i18n.t('时间'),
          2: i18n.t('金额')
        }
      },
      allowEditing: false
    },
    {
      field: 'stageList',
      headerText: i18n.t('阶梯报价'),
      allowEditing: false
    },
    {
      field: 'untaxedUnitPrice',
      headerText: i18n.t('未税单价'),
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('未税单价')}}</span>
                </div>
              `
          })
        }
      },
      editorRender(h, scoped) {
        scoped.untaxedUnitPrice = scoped.untaxedUnitPrice ?? 0
        return (
          <div>
            <mt-input-number
              v-model={scoped.untaxedUnitPrice}
              min={0}
              placeholder={i18n.t('请输入')}
              onInput={(val) => {
                if (val) {
                  scoped.untaxedTotalPrice = bigDecimal.multiply(val, scoped.purRequirementNum)
                  scoped.taxedUnitPrice = bigDecimal.multiply(
                    val,
                    bigDecimal.add(1 + scoped.taxRate)
                  )
                  scoped.taxedTotalPrice = bigDecimal.multiply(
                    scoped.taxedUnitPrice,
                    scoped.purRequirementNum
                  )
                } else {
                  scoped.untaxedTotalPrice = 0
                  scoped.taxedUnitPrice = 0
                  scoped.taxedTotalPrice = 0
                }
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'untaxedTotalPrice',
      headerText: i18n.t('未税总价'),
      editorRender(h, scoped) {
        return (
          <div>
            <mt-input v-model={scoped.untaxedTotalPrice} disabled />
          </div>
        )
      }
    },

    {
      field: 'taxedUnitPrice',
      headerText: i18n.t('含税单价'),
      editorRender(h, scoped) {
        return (
          <div>
            <mt-input v-model={scoped.taxedUnitPrice} disabled />
          </div>
        )
      }
    },
    {
      field: 'taxedTotalPrice',
      headerText: i18n.t('含税总价'),
      editorRender(h, scoped) {
        return (
          <div>
            <mt-input v-model={scoped.taxedTotalPrice} disabled />
          </div>
        )
      }
    },
    {
      field: 'priceValidStartTime',
      headerText: i18n.t('价格开始时间'),
      allowEditing: false
    },
    {
      field: 'priceValidEndTime',
      headerText: i18n.t('价格结束时间'),
      allowEditing: false
    },
    {
      field: 'stockSite',
      headerText: i18n.t('库存地址'),
      allowEditing: false
    },
    {
      field: 'requiredDeliveryDate',
      headerText: i18n.t('交期'),
      allowEditing: false
    },
    {
      field: 'replyMemo',
      headerText: i18n.t('反馈备注'),
      allowEditing: false
    }
  ]
}
export const TemplateConfig = (that) => [
  {
    title: i18n.t('标的明细'),
    useToolTemplate: false,
    toolbar: {
      useBaseConfig: false, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [[], []]
    },
    grid: {
      isUseCustomEditor: true,
      editSettings: {
        allowEditing: true,
        allowAdding: true,
        allowDeleting: true,
        mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
        showConfirmDialog: false,
        showDeleteConfirmDialog: false,
        newRowPosition: 'Top'
      },
      columnData: columnData(that),
      dataSource: []
    }
  },
  {
    title: i18n.t('附件')
  }
]

const columnDataFileCf = (tag) => [
  {
    type: 'checkbox',
    width: 50
  },
  {
    // tag:1 只可以删除自己上传的附件，其他的只有下载的权限
    headerText: i18n.t('文件名'),
    field: 'fileName',
    cellTools: [
      {
        id: 'del',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: () => {
          return tag == 1
        }
      },
      {
        id: 'download',
        icon: 'icon_list_download',
        title: i18n.t('下载'),
        visibleCondition: () => {
          return tag == 1 || tag == 3
        }
      }
    ]
  },
  {
    headerText: i18n.t('文件类型'),
    field: 'fileType'
  },
  {
    headerText: i18n.t('文件大小'),
    field: 'fileSize',
    template: () => {
      return {
        template: Vue.component('fileSize', {
          data() {
            return {
              data: {}
            }
          },
          template:
            "<span>{{ (data.fileSize/1024)>1024?(data.fileSize/1024/1024).toFixed('2')+'MB':(data.fileSize/1024).toFixed('2')+'KB'}}</span>"
        })
      }
    }
  },
  {
    headerText: i18n.t('创建人'),
    field: 'createUserName'
  },
  {
    headerText: i18n.t('创建时间'),
    field: 'createTime',
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return dayjs(data).format('YYYY-MM-DD HH:mm:ss')
      }
    }
  }
]
const columnDataFileGf = (tag) => [
  {
    type: 'checkbox',
    width: 50
  },
  {
    // tag:1 只可以删除自己上传的附件，其他的只有下载的权限
    headerText: i18n.t('文件名'),
    width: 350,
    field: 'fileName',
    cellTools: [
      {
        id: 'del',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: () => {
          return tag == 2
        }
      },
      {
        id: 'download',
        icon: 'icon_solid_Createorder',
        title: i18n.t('下载'),
        visibleCondition: () => {
          return tag == 2 || tag == 3
        }
      }
    ]
  },
  {
    headerText: i18n.t('文件类型'),
    field: 'fileType'
  },
  {
    headerText: i18n.t('文件大小'),
    field: 'fileSize',
    template: () => {
      return {
        template: Vue.component('fileSize', {
          data() {
            return {
              data: {}
            }
          },
          template:
            "<span>{{ (data.fileSize/1024)>1024?(data.fileSize/1024/1024).toFixed('2')+'MB':(data.fileSize/1024).toFixed('2')+'KB'}}</span>"
        })
      }
    }
  },
  {
    headerText: i18n.t('创建人'),
    field: 'createUserName'
  },
  {
    headerText: i18n.t('创建时间'),
    field: 'createTime',
    valueConverter: {
      type: 'function',
      filter: (data) => {
        return dayjs(data).format('YYYY-MM-DD HH:mm:ss')
      }
    }
  }
]

export const FilePageConfig = (contractId) => [
  {
    title: i18n.t('采方'),
    useToolTemplate: false,
    toolbar: [
      {
        id: 'upload',
        icon: 'icon_solid_upload',
        title: i18n.t('上传')
      },
      {
        id: 'download',
        icon: 'icon_list_download',
        title: i18n.t('下载')
      },
      {
        id: 'del',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除')
      }
    ],
    grid: {
      columnData: columnDataFileCf(1),
      allowPaging: false,
      asyncConfig: {
        url: '/contract/attachment',
        methods: 'get',
        params: {
          contractId: contractId,
          attachmentType: 0
        },
        recordsPosition: 'data'
      }
    }
  },
  {
    title: i18n.t('供方'),
    useToolTemplate: false,
    toolbar: [
      {
        id: 'download',
        icon: 'icon_solid_Download',
        title: i18n.t('下载')
      }
    ],
    grid: {
      columnData: columnDataFileGf(1),
      allowPaging: false,
      asyncConfig: {
        url: '/contract/attachment',
        methods: 'get',
        params: {
          contractId: contractId,
          attachmentType: 1
        },
        recordsPosition: 'data'
      }
    }
  }
]
