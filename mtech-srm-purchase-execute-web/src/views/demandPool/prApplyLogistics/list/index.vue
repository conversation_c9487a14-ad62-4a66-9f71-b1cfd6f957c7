<template>
  <div class="full-height pc-pr-apply">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @handleQuerySearch="handleQuerySearch"
    >
    </mt-template-page>
    <cancleDialog @confirmSuccess="confirmSuccess" ref="cancleDialog"></cancleDialog>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { columnData, statusOptions, sourceTypeOptions } from './config/index.js'

export default {
  components: {
    cancleDialog: () => import('./components/cancleDialog')
  },
  data() {
    return {
      statusOptions: statusOptions,
      sourceTypeOptions: sourceTypeOptions,
      bussinessTypeName: null,
      businessTypeCode: null,
      totalNumber: null, //数据总条数
      currentTabIndex: 0,
      claimTabIndex: 0,
      addDialogShow: false,
      dialogData: null,
      businessTypeList: [],
      pageConfig: [
        {
          useToolTemplate: false,
          toolbar: [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              title: this.$t('新增')
              // permission: ['O_02_0337']
            },
            {
              id: 'delete',
              icon: 'icon_solid_Delete',
              title: this.$t('删除')
              // permission: ['O_02_0339']
            },
            {
              id: 'close',
              icon: 'icon_solid_Closeorder',
              title: this.$t('关闭')
              // permission: ['O_02_0342']
            },
            {
              id: 'cancle',
              icon: 'icon_solid_pushorder',
              title: this.$t('取消')
              // permission: ['O_02_1390']
            },
            {
              id: 'submit',
              icon: 'icon_solid_upload',
              title: this.$t('提交')
              // permission: ['O_02_0341']
            }
          ],
          gridId: '420fe741-3c93-48c9-8a08-637b2aff4f32',
          grid: {
            columnData: columnData,
            lineIndex: 1,
            dataSource: [],
            frozenColumns: 1,
            asyncConfig: {
              url: `/contract/tenant/logistics/auto/request/list`
            }
          }
        }
      ]
    }
  },

  computed: {
    userInfo() {
      return this.$store.state.userInfo
    }
  },

  methods: {
    dataTimeChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'S'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[flag + 'E'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[flag + 'S'] = null
        this.searchFormModel[flag + 'E'] = null
      }
    },
    //处理列模板组件异步请求后数据
    afterAsyncData(res) {
      // 获取数据条数
      this.totalNumber = res.data.total
    },

    handleClickToolBar(e) {
      if (
        e.gridRef.getMtechGridRecords().length <= 0 &&
        !(
          e.toolbar.id == 'Add' ||
          e.toolbar.id == 'Filter' ||
          e.toolbar.id == 'Refresh' ||
          e.toolbar.id == 'Setting' ||
          e.toolbar.id == 'more-option-btn' ||
          e.toolbar.id == 'refreshDataByLocal' ||
          e.toolbar.id == 'filterDataByLocal'
        )
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = [],
        _status = []
      e.gridRef.getMtechGridRecords().map((item) => {
        _id.push(item.id)
        _status.push(item.requestStatus)
      })
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'delete') {
        this.handleDelete(e.gridRef.getMtechGridRecords())
      } else if (e.toolbar.id == 'cancle') {
        // 取消
        if (_status.every((i) => i != 1 && i != 4)) {
          if (_status.length !== 1) {
            this.$toast({
              content: this.$t('请仅选择一行进行取消操作'),
              type: 'warning'
            })
            return
          }
          // 取消的弹窗
          this.handleCancle(_id)
        } else {
          this.$toast({
            content: this.$t('请选择草稿/审批拒绝/审批通过状态的申请进行关闭操作'),
            type: 'warning'
          })
        }
      } else if (e.toolbar.id == 'close') {
        this.handleClose(_id)
      } else if (e.toolbar.id == 'submit') {
        // 只有 草稿和审批拒绝 可以提交
        this.handleSubmit(_id)
      } else if (e.toolbar.id == 'transfer') {
        this.handleTransfer(_id)
      }
    },

    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
      if (e.tool.id == 'edit') {
        this.handleEdit(e)
      } else if (e.tool.id == 'delete') {
        this.handleDeleteRow([e.data.id])
      } else if (e.tool.id == 'close') {
        this.handleClose([e.data.id])
      } else if (e.tool.id == 'cancle') {
        this.handleCancle([e.data.id])
      } else if (e.tool.id == 'submit') {
        this.handleSubmit([e.data.id])
      } else if (e.tool.id == 'replanish') {
        this.handleReplanish(e.data.id, e.data.sourceType)
      }
    },

    // 点击单元格 标题
    handleClickCellTitle(e) {
      if (e.field == 'requestCode') {
        this.$router.push(
          `pr-apply-logistics-detail?id=${e.data.id}&type=view&source=${e.data?.sourceType}`
        )
      }
    },

    // 转办
    handleTransfer(ids) {
      this.$dialog({
        modal: () => import('./components/transferDialog.vue'),
        data: {
          title: this.$t('转办'),
          message: this.$t('是否确认转办？'),
          ids: ids
        },
        success: () => {
          this.confirmSuccess()
        }
      })
    },
    // 新增
    handleAdd() {
      this.$router.push(`pr-apply-logistics-detail?type=add`)
    },

    // 编辑
    handleEdit(e) {
      this.$router.push(
        `pr-apply-logistics-detail?id=${e.data.id}&type=edit&source=${e.data.sourceType}`
      )
    },

    // 删除
    handleDelete(records) {
      let ids = []
      try {
        records.forEach((i) => {
          // 草稿状态 && 手工新建  可以删除
          if (i.sourceType != 0 || i.status != 0) {
            this.$toast({
              content: this.$t('请选择手工创建且为草稿状态的单据删除')
            })
            throw new Error(this.$t(`请选择手工创建且为草稿状态的单据删除`))
          } else {
            ids.push(i.id)
          }
        })
        this.handleDeleteRow(ids)
      } catch (e) {
        console.log(e)
      }
    },

    // 删除 调用接口
    handleDeleteRow(ids) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('确认删除该采购申请？')
        },
        success: () => {
          this.$API.purchaseRequest
            .deleteLogisticsList({ ids })
            .then((res) => {
              this.$store.commit('endLoading')
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.confirmSuccess()
              }
            })
            .catch(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },

    // 补充信息
    handleReplanish(id, sourceType) {
      this.$router.push(`pr-apply-logistics-detail?id=${id}&type=replanish&source=${sourceType}`)
    },
    // 关闭
    handleClose(ids) {
      this.$dialog({
        data: {
          title: this.$t('关闭采购申请'),
          message: this.$t('确认关闭该采购申请？')
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.purchaseRequest
            .closeLogisticsList({ ids })
            .then((res) => {
              this.$store.commit('endLoading')
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.confirmSuccess()
              }
            })
            .catch(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },
    // 取消
    handleCancle(ids) {
      this.$refs.cancleDialog.dialogInit(ids[0])
    },

    handleSubmit(ids) {
      this.$API.purchaseRequest
        .commitLogisticsList({ ids, requestType: 0 })
        .then((res) => {
          this.$store.commit('endLoading')
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.confirmSuccess()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },

    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    }
  }
}
</script>

<style lang="scss">
/* 单元格内容是置灰颜色 */
td.pld-content-pc {
  .grid-edit-column > div:first-child {
    font-size: 14px;
    color: rgba(155, 170, 193, 1);
  }
}
</style>

<style lang="scss" scoped>
.top-filter {
  height: 70px;
  background: #fff;
  padding: 20px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left-status {
    margin-right: 20px;
    overflow: hidden;
    display: flex;
    align-items: center;
    .tit {
      flex-shrink: 0;
    }
  }
  /deep/ .total-number {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: #9a9a9a;
    line-height: 28px;
    flex-shrink: 0;
    margin-left: 20px;
  }
}
</style>
