<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="$t('申请单取消')"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <mt-form-item prop="uploadData" :label="$t('上传附件')" class="full-width">
        <upload-file ref="uploader" @change="fileChange"></upload-file>
      </mt-form-item>
      <mt-form-item prop="cancelReason" :label="$t('原因备注')" class="full-width">
        <mt-input
          v-model="ruleForm.cancelReason"
          v-if="maxlength1"
          :maxlength="maxlength1"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  components: {
    UploadFile: () => import('@/components/Upload/uploader')
  },
  data() {
    return {
      maxlength1: 500,
      uploadData: [],
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {
        cancelReason: ''
      },
      rules: {
        cancelReason: [
          {
            required: true,
            message: this.$t('请输入原因备注'),
            trigger: 'blur'
          }
        ]
      },
      entryId: '' // 带入的需要取消的id
    }
  },
  methods: {
    fileChange(data) {
      this.uploadData = data
    },
    dialogInit(entryId) {
      this.entryId = entryId
      this.$refs.uploader.init()
      this.uploadData = []
      this.ruleForm.cancelReason = ''
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    confirm() {
      if (this.uploadData.length == 0) {
        this.$toast({ content: this.$t('请上传附件'), type: 'warning' })
        return
      }
      const uploadDataTmp = []
      this.uploadData.forEach((item) => {
        uploadDataTmp.push({
          docId: 0,
          docType: '',
          fileDetailId: 0,
          fileDetailInfo: '',
          fileName: item.fileName,
          fileSize: item.fileSize,
          fileType: item.fileType,
          lineNo: 0,
          nodeCode: '',
          nodeName: '',
          nodeType: 1,
          parentId: 0,
          supplierCode: '',
          supplierId: 0,
          supplierName: '',
          syncStatus: 0,
          sysFileId: item.id,
          url: item.url
        })
      })
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const params = {
            requestHeader: { id: this.entryId, cancelReason: this.ruleForm.cancelReason },
            fileDataList: uploadDataTmp
          }
          this.$API.purchaseRequest.cancelLogisticsList(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('取消操作成功'), type: 'success' })
              this.handleClose()
              this.$emit('confirmSuccess')
            }
          })
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
