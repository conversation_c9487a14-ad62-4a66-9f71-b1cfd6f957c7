<template>
  <mt-dialog ref="dialog" css-class="small-dialog" :buttons="buttons" :header="modalData.title">
    <div class="dialog-content">
      <div class="source-label label-left">
        <mt-tooltip :content="content" target="#box">
          {{ $t('转办给：') }}<mt-icon name="icon_outline_prompt" class="box" id="box"></mt-icon>
        </mt-tooltip>
      </div>
      <debounce-filter-select
        :width="450"
        :request="getUser"
        v-model="selectedUser"
        float-label-type="Never"
        :data-source="userList"
        :show-clear-button="true"
        :fields="{ text: 'text', value: 'uid' }"
        :placeholder="$t('请选择人员')"
      ></debounce-filter-select>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      content: this.$t(`转办后，需求将出现在转办人的 我认领的明细中`),
      selectedUser: -1,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      userList: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    // 获取 用户列表
    getUser(e) {
      const { text: fuzzyName } = e
      this.userList = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
        this.userList = tmp
      })
    },

    confirm() {
      if (this.selectedUser <= -1) {
        this.$toast({ content: this.$t('请选择人员') })
        return
      }
      let _selectRow = this.userList.find((item) => {
        return item.uid == this.selectedUser
      })

      this.$API.purchaseRequest
        .allotRequest({
          idList: this.modalData.ids,
          claimUserId: this.selectedUser,
          claimUserName: _selectRow.employeeName
        })
        .then(() => {
          this.$emit('confirm-function')
        })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
/deep/ .source-label.label-left .e-lib {
  font-size: 14px;
  .box {
    width: 12px;
    height: 12px;
  }
}
// .e-dlg-container
//   .e-dlg-content
//   .dialog-content
//   .source-label
//   .mt-tooptip
//   .e-control {
//   font-size: 14px;
// }
</style>
