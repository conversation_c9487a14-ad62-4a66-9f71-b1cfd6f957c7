import Vue from 'vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName, timeNumberToDate } from '@/utils/utils'
import { download } from '@/utils/utils'
export const statusOptions = [
  { text: i18n.t('草稿'), value: 0, cssClass: '' },
  { text: i18n.t('待审批'), value: 1, cssClass: '' },
  { text: i18n.t('审批通过'), value: 2, cssClass: '' },
  { text: i18n.t('审批拒绝'), value: 3, cssClass: '' },
  { text: i18n.t('已关闭'), value: 4, cssClass: '' },
  { text: i18n.t('已完成'), value: 5, cssClass: '' },
  { text: i18n.t('审核中'), value: 6, cssClass: '' }
]
export const sourceTypeOptions = [
  { text: i18n.t('手工创建'), value: 0, cssClass: '' },
  { text: i18n.t('共享财务'), value: 1, cssClass: '' },
  { text: i18n.t('TMS'), value: 3, cssClass: '' }
]
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'requestCode',
    headerText: i18n.t('编号'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'), // 草稿、审批拒绝可以重新编辑
        visibleCondition: (data) => {
          return data.requestStatus == 0 || data.requestStatus == 3
        }
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'), // 草稿状态 && 手工新建  可以删除
        visibleCondition: (data) => {
          return data.sourceType == 0 && data.requestStatus == 0
        }
      },
      {
        id: 'replanish',
        icon: 'O_02_0343',
        title: i18n.t('补充信息'), // 审批通过 && 没有已转订单的明细 && 没有已认领的明细
        visibleCondition: (data) => {
          return data.requestStatus == 2 && data?.orderedLineNum == 0 && data?.claimedLineNum == 0
        }
      }
    ],
    headerTemplate: function () {
      return {
        template: Vue.component('uploadFile', {
          template: `
                  <div class="headers">
                    <span class="e-headertext">{{data.headerText}}</span>
                    <mt-tooltip :content="content" position="BottomCenter" target="#box">
                      <MtIcon id="box" name="icon_outline_prompt" style="font-size: 18px;vertical-align: middle" />
                    </mt-tooltip>
                  </div>
                `,
          data() {
            return {
              content: function () {
                return {
                  template: Vue.component('demo', {
                    template: `
                    <div id="tooltip" ref="content" style="width:240px;font-size:12px;padding:6px 11px;">
                    {{$t("草稿状态下无编号，提交后系统自动生成")}}
                    </div>`,
                    data() {
                      return {
                        data: {}
                      }
                    }
                  })
                }
              },
              data: {}
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'title',
    headerText: i18n.t('名称')
  },
  {
    width: '100',
    field: 'requestStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('草稿'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待审批'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('审批通过'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('审批拒绝'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('关闭'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('已完成'), cssClass: 'col-active' },
        { value: 6, text: i18n.t('审核中'), cssClass: 'col-active' }
      ]
    },
    cellTools: [
      {
        id: 'submit',
        icon: 'icon_Share_2',
        title: i18n.t('提交'),
        visibleCondition: (data) => {
          return [0, 3].includes(data.requestStatus)
        }
      },
      {
        id: 'close',
        icon: 'icon_list_close',
        title: i18n.t('关闭'),
        permission: ['O_02_0342'],
        visibleCondition: (data) => {
          // 草稿、审批拒绝、待审批
          return [0, 2, 3].includes(data.requestStatus)
        }
      },
      {
        id: 'cancle',
        icon: 'icon_solid_pushorder',
        title: i18n.t('取消'),
        visibleCondition: (data) => {
          return [0, 2, 3].includes(data.requestStatus)
        }
      }
    ]
  },
  {
    width: '150',
    field: 'sourceType',
    headerText: i18n.t('来源'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('手工创建'),
        1: i18n.t('共享财务'),
        // 2: i18n.t('商城'),
        3: i18n.t('TMS')
      } // 0： 手工创建  1：共享财务  2：商城  3:TMS
    }
  },
  {
    width: '150',
    field: 'businessTypeName',
    headerText: i18n.t('业务类型'),
    template: function () {
      return {
        template: Vue.component('businessTypeName', {
          template: `
            <div>{{ $t(data.businessTypeName) }}</div>
          `,
          data() {
            return { data: {} }
          },
          methods: {}
        })
      }
    }
  },
  {
    width: '150',
    field: 'rejectReason',
    headerText: i18n.t('驳回原因')
  },
  {
    width: '150',
    field: 'closeReason',
    headerText: i18n.t('取消原因')
  },
  {
    field: 'closeRelatedFile',
    width: '250',
    headerText: i18n.t('取消附件'),
    template: function () {
      return {
        template: Vue.component('closeRelatedFile', {
          template: `<div class="action-boxs">
            <span v-if="isHaveFile" @click="downloadFile" style="color: #00469c; cursor: pointer">{{ $t('下载')}}</span>
            <span v-else style="color: #00469c;">{{ $t('暂无附件')}}</span>
          </div>`,
          data() {
            return {
              isHaveFile: false
            }
          },
          mounted() {
            if (this.data && this.data.closeRelatedFile && this.data.closeRelatedFile.length) {
              this.isHaveFile = true
            }
          },
          methods: {
            downloadFile() {
              if (this.isHaveFile) {
                // 下载
                this.data.closeRelatedFile.forEach((itemFiles) => {
                  this.handlerDownloadPrivateFile(itemFiles)
                })
              }
            },
            handlerDownloadPrivateFile(data) {
              this.$store.commit('startLoading')
              this.$API.fileService
                .downloadPrivateFile({
                  id: data.sysFileId || data.id
                })
                .then((res) => {
                  this.$store.commit('endLoading')
                  download({ fileName: data.fileName, blob: res.data })
                })
            }
          }
        })
      }
    }
  },

  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  },
  {
    width: '250',
    field: 'administrationCompanyCode',
    headerText: i18n.t('行政公司'),
    searchOptions: MasterDataSelect.administrativeCompany,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.administrationCompanyCode, data?.administrationCompanyName)
    },
    allowGlobalSorting: true
  },
  {
    width: '250',
    field: 'companyCode',
    headerText: i18n.t('业务公司'),
    searchOptions: MasterDataSelect.businessCompany,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.companyCode, data?.companyName)
    },
    allowGlobalSorting: true
  },
  {
    width: '150',
    field: 'applyDepName',
    headerText: i18n.t('申请部门')
  },
  {
    width: '100',
    field: 'applyUserName',
    headerText: i18n.t('申请人')
  },
  {
    width: '150',
    field: 'applyDate',
    headerText: i18n.t('申请日期'),
    searchOptions: MasterDataSelect.timeRange
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    searchOptions: MasterDataSelect.timeRange
  }
]
