<template>
  <div class="detail-fix-wrap full-height">
    <top-info
      class="top-info"
      ref="headerTop"
      :header-info="headerInfo"
      @save="handleSave"
      @submit="handleSubmit"
    ></top-info>
    <div class="bottom-tables" v-if="showItem">
      <mt-tabs
        tab-id="hall-tab"
        :e-tab="false"
        :data-source="logisticsTabList"
        :selected-item="tabIndex"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
      <!-- 需求明细 -->
      <logisticsRequireDetail
        ref="logisticsRequireDetailRef"
        :user-info="userInfo"
        :detail-info="headerInfo"
        @itemSave="itemSave"
      ></logisticsRequireDetail>
    </div>
  </div>
</template>

<script>
// 合同转申请的url，因为只有查看：/purchase-execute/pr-apply-detail?type=view&source=shopMall&id=PR2022030400013
// id：是商城那边得code，所以要通过调用接口，换成id
// source：固定是shopMall
// type: view 商城转申请只有查看

// 采购商城转申请的url /pr-apply-detail?type=add&source=2&key=20220307182600009SHOP

import { cloneDeep } from 'lodash'
import { BASE_TENANT } from '@/utils/constant'
import { nodeType } from '@/components/businessComponents/relativeFileNoDocId/config/relative.js'
import { RegExpMap } from '@/utils/constant'
import { lengthArr } from './config/judgeFields'

export default {
  components: {
    topInfo: require('./components/topInfo.vue').default,
    // logisticsTopInfo: require('./components/logisticsTopInfo.vue').default, // 物流topInfo信息
    logisticsRequireDetail: require('./pages/logisticsRequireDetail.vue').default
  },
  data() {
    return {
      tabIndex: 0, // 业务类型切换的时候，重新设置为0
      canShowContent: false, // 来源不是商城 || 来源是商城，且 已通过code获取到了id
      isView: false,
      userInfo: {}, // 当前登录的用户信息
      tabList: [],
      logisticsTabList: [{ title: this.$t('采购明细') }],
      currentInfo: { moduleType: -1 },
      prevTabModuleKey: null, // 上一个选中的tab数据
      headerInfo: {
        // 默认是草稿状态，新增时用这个，编辑等状态从接口获取
        statusTxt: this.$t('草稿'),
        status: 0,
        createUserName: '-',
        createTime: '-'
      },
      detailModuleInfo: null, // 需求明细的moduleInfo
      moduleKeyList: [
        '015ed05f-d3f3-11eb-96d2-0242ac130001', // 需求明细
        '015ed05f-d3f3-11eb-96d2-0242ac130002', // 推荐供方
        '015ed05f-d3f3-11eb-96d2-0242ac130003', // 费用分摊/整单分摊
        '015ed05f-d3f3-11eb-96d2-0242ac130004', // 相关文件
        '015ed05f-d3f3-11eb-96d2-0242ac130005', // 相关说明
        '015ed05f-d3f3-11eb-96d2-0242ac130015' // 操作日志
      ],
      businessTypeList: [], // 业务类型列表
      selectedBusinessCode: null, // 选中的业务类型id
      moduleFileList: [], // 附件节点（传给附件组件）
      requireFiles: [], // 能上传的行内明细附件的文件列表，因为要从需求明细那边同步过去。。

      requestHeader: null, // 头的信息

      entryId: '', // 获取操作日志的id
      detailData: [], // 商城转申请时，获取到的行内数据，只有url中的source=2时，才需要接收

      moduleKeyConfigs: [], // 从接口获取到的模块配置
      btnRequired: {
        hasUpload: true,
        hasDownload: true,
        hasDelete: true
      },
      unMobileCompany: [] // 不用校验手机格式的公司
    }
  },
  computed: {
    isLogistics() {
      return this.$route?.query?.pageType === 'logistics'
    },
    showItem() {
      return this.$route?.query?.type !== 'add'
    }
  },
  watch: {
    canShowContent(newVal) {
      if (!newVal) return
      // 如果有id
      if (this.$route.query.id) {
        this.entryId = this.$route.query.id
      }
      // type类型包括 add新增、view查看、edit编辑、replanish补充信息
      // 校验是否是查看
      if (this.$route.query.type == 'view') {
        this.isView = true
      }

      // 新增时拿空配置，编辑/补充信息/查看时拿配置和头部数据
      if (this.$route.query.type == 'add') {
        // 监听业务类型变化（只有新增时候才有）
        this.$bus.$off('changedPRBusinessType')
        this.$bus.$on('changedPRBusinessType', (itemData) => {
          this.$store.commit('startLoading')
          this.tabIndex = 0
          // console.log(this.$t("监听到了变化"), itemData);
          this.selectedBusinessCode = itemData.itemCode
          if (this.$route.query.id) {
            // 草稿状态下，也可能是有数据的
            this.getHeaderInfo() // 获取主单顶部数据
            this.handleGetModuleConfig()
            this.getFileNode() // 获取附件的节点信息
          } else {
            this.getBusinessConfigDetail()
            // 如果是商城来源，获取头部的部分数据和明细行的数据
            if (this.$route.query.source == 2) {
              this.getModuleDataFromMall()
            }
          }
        })

        if (this.$route.query.id) {
          this.$store.commit('startLoading')
          // 草稿状态下，也可能是有数据的
          this.getHeaderInfo() // 获取主单顶部数据
          this.handleGetModuleConfig()
          this.getFileNode() // 获取附件的节点信息
        } else {
          // this.getBusinessConfigDetail();
        }
      } else if (this.$route.query.pageType === 'logistics') {
        this.$store.commit('startLoading')
        this.getLogisticHeaderInfo() // 获取物流主单数据
      } else {
        this.$store.commit('startLoading')
        this.getHeaderInfo() // 获取主单顶部数据
        this.handleGetModuleConfig()
        this.getFileNode() // 获取附件的节点信息
      }
      // 获取当前登录的用户
      this.userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
    }
  },

  created() {
    // this.getLogisticsCodeById()
    this.getUnMobileCompany()
    this.getLogisticHeaderInfo()
  },

  mounted() {},

  methods: {
    // 获取订单类型下拉
    getUnMobileCompany() {
      this.$API.masterData.getDictCode({ dictCode: 'UN_MOBILE_COMPANY' }).then((res) => {
        this.unMobileCompany.length = 0
        res.data.forEach((item) => {
          this.unMobileCompany.push(item)
        })
      })
    },
    // 通过code换 id
    getCodeById() {
      this.$API.purchaseRequest.getIdByCode({ code: this.$route.query.id }).then((res) => {
        console.log('换取到得id是', res)
        if (res.data) {
          this.canShowContent = true
          this.$router.replace(`pr-apply-detail?id=${res.data}&type=view`)
        }
      })
    },
    // 物流类通过code查找id(物流类的查询在合同模块)
    getLogisticsCodeById() {
      this.$API.purchaseRequest
        .getLogisticsCodeById({
          docNo: this.$route.query.id,
          businessTypeCode: this.$route.query?.businessTypeCode
        })
        .then((res) => {
          console.log('换取到得id是', res)
          if (res.data) {
            this.canShowContent = true
            this.$router.replace(
              `pr-apply-detail?id=${res.data.id}&type=view&type=view&sourceType=3&pageType=logistics`
            )
          }
        })
    },

    // 新增时：根据业务类型获取所有模块的配置
    getBusinessConfigDetail() {
      this.$API.bgConfig
        .getBusinessConfigDetail({
          businessTypeCode: this.selectedBusinessCode,
          docType: 'pr'
        })
        .then((res) => {
          // console.log("res", res);
          if (res && res.data && res?.data.modules) {
            this.formatModuleNode(res?.data?.moduleFileList)
            this.tabList = []
            this.moduleKeyConfigs = []
            res?.data?.modules.forEach((item) => {
              this.moduleKeyConfigs.push(item.moduleKey)
              // 获取需求明细的moduleInfo
              if (item.moduleKey == this.moduleKeyList[0]) {
                this.detailModuleInfo = item
              }
              // 如果是推荐供应商，就不用展示 产品经理已去掉
              if (item.moduleKey != this.moduleKeyList[1]) {
                this.tabList.push({
                  ...item,
                  title: item.moduleName
                })
              }
            })
            // console.log("tabList", this.tabList);
            this.currentInfo = this.tabList[0]
          } else {
            this.tabList = []
            this.$toast({
              content: this.$t('该业务类型暂无配置'),
              type: 'warning'
            })
            this.$store.commit('endLoading')
          }
        })
    },

    // 非新增时：获取主单顶部数据
    getHeaderInfo() {
      if (!this.$route.query.id) return
      this.$API.purchaseRequest.getHeaderInfo({ headerId: this.$route.query.id }).then((res) => {
        this.headerInfo = res.data
        this.setHeaderTxt()
      })
    },

    // 获取海运主单数据
    getLogisticHeaderInfo() {
      if (!this.$route.query.id) return
      this.$API.purchaseRequest
        .getLogisticsHeaderInfo({ headerId: this.$route.query.id })
        .then((res) => {
          this.$store.commit('endLoading')
          this.headerInfo = res.data
          this.setHeaderTxt()
        })
    },

    // 设置头部显示的状态文案
    setHeaderTxt() {
      let statusTxt = this.$t('草稿')
      switch (this.headerInfo.requestStatus) {
        //  0: this.$t("草稿"), 1: this.$t("待审批"), 2: this.$t("审批通过"), 3: this.$t("审批拒绝"), 4: this.$t("关闭")
        case 0:
          statusTxt = this.$t('草稿')
          break
        case 1:
          statusTxt = this.$t('待审批')
          break
        case 2:
          statusTxt = this.$t('审批通过')
          break
        case 3:
          statusTxt = this.$t('审批拒绝')
          break
        case 4:
          statusTxt = this.$t('已关闭')
          break
        case 5:
          statusTxt = this.$t('已完成')
          break
        case 6:
          statusTxt = this.$t('审核中')
          break
        default:
          break
      }
      if (this.headerInfo.projectType == 1) {
        this.headerInfo.projectTypeTxt = this.$t('项目型')
      } else {
        this.headerInfo.projectTypeTxt = this.$t('非项目型')
      }
      this.headerInfo.statusTxt = statusTxt
    },

    // 非新增时：获取所有模块的配置
    handleGetModuleConfig() {
      if (!this.$route.query.id) return
      this.$API.purchaseRequest.getModuleByDocId({ docId: this.$route.query.id }).then((res) => {
        this.tabList = []
        this.moduleKeyConfigs = []
        res?.data?.moduleItems.forEach((item) => {
          this.moduleKeyConfigs.push(item.moduleKey)
          // 获取需求明细的moduleInfo
          if (item.moduleKey == this.moduleKeyList[0]) {
            this.detailModuleInfo = item
          }
          // 如果是推荐供应商，就不用展示 产品经理已去掉
          if (item.moduleKey != this.moduleKeyList[1]) {
            this.tabList.push({
              ...item,
              title: item.moduleName
            })
          }
        })
        // console.log("tabList", this.tabList);
        this.currentInfo = this.tabList[0]
      })
    },

    // 非新增时：获取相关附件的节点列表
    getFileNode() {
      this.$API.purchaseRequest.getFileNodeByDocId({ docId: this.$route.query.id }).then((res) => {
        this.formatModuleNode(res.data)
      })
    },

    // 商城转申请
    getModuleDataFromMall() {
      this.$API.purchaseRequest
        .getModuleDataFromMall({
          businessTypeCode: this.selectedBusinessCode,
          showKey: this.$route.query.key
        })
        .then((res) => {
          console.log('打印出来的,从商城转化的数据', res, this.userInfo)
          this.headerInfo = {
            ...res.data?.requestHeader,
            status: 0,
            applyUserId: this.userInfo?.employeeId
          }
          this.detailData = res.data?.itemDataList
          this.setHeaderTxt()
        })
    },

    // 整合 相关附件 的左侧节点信息：新增和其他状态不一样
    formatModuleNode(originNodeList) {
      if (!(originNodeList instanceof Array) || !originNodeList.length) return
      let moduleFileList = []
      originNodeList.forEach((item) => {
        let _type = '',
          _url = '',
          _params = null,
          _methods = null
        if (item.nodeCode.includes('header')) {
          // 主单的
          if (this.$route.query.type == 'view') {
            // 仅查看时，要获取主单附件的数据
            _type = nodeType.mainView
            _url = `${BASE_TENANT}/requestFile/queryFileByDocId`
            _params = {
              docId: this.$route.query.id,
              parentId: item.id
            }
            _methods = 'get'
          } else if (this.$route.query.id) {
            // 有过保存时，要获取主单附件的数据
            _type = nodeType.mainUpdateEdit
            _url = `${BASE_TENANT}/requestFile/queryFileByDocId`
            _params = {
              docId: this.$route.query.id,
              parentId: item.id
            }
            _methods = 'get'
          } else {
            // 没报错过，第一次add时，不需要获取
            _type = nodeType.mainUpdate
          }
        } else {
          _type = nodeType.itemUpdate
        }
        moduleFileList.push({
          ...item,
          type: _type, // 01:只能下载的明细附件，02:能上传的行内明细附件，03:只能下载的主单附件，04:能上传的主单附件，05：编辑已上传的整单附件，数据保存在本地
          url: _url,
          params: _params,
          methods: _methods
        })
      })

      this.moduleFileList = moduleFileList
      console.log(this.$t('得到附件'), this.moduleFileList)
    },

    // 切换tab
    handleSelectTab(index, item) {
      this.prevTabModuleKey = this.currentInfo?.moduleKey
      this.currentInfo = item
      console.log(
        'handleSelectTab',
        this.currentInfo,
        this.prevTabModuleKey,
        this.$refs.requireDetailRef.isInEdit
      )

      // 如果是从需求明细跳出去  && 处于编辑状态，就结束编辑
      if (this.prevTabModuleKey == this.moduleKeyList[0] && this.$refs.requireDetailRef.isInEdit) {
        this.$refs.requireDetailRef.endEdit()
      }
    },
    // 明细保存
    itemSave() {
      this.handleSave()
    },
    // 保存草稿
    handleSave() {
      this.requestData('save')
    },
    // 提交
    handleSubmit() {
      this.requestData('submit')
    },
    requestData(type) {
      const itemVoList = this.$refs.logisticsRequireDetailRef
        ? this.$refs.logisticsRequireDetailRef.getTableData()
        : []
      this.$refs.headerTop.confirm().then((topInfo) => {
        if (!topInfo) {
          this.$toast({
            content: this.$t('请先将头部信息填写完整'),
            type: 'warning'
          })
          return
        }
        this.requestHeader = topInfo
        const params = {
          ...topInfo,
          itemVoList
        }
        const name = type === 'save' ? 'addOrUpdateLogisticsList' : 'saveAndCommitLogisticsList'
        this.$API.purchaseRequest[name](params).then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            let _query = {
              ...this.$route.query,
              type: 'edit',
              id: !this.$route.query.id ? res?.data : this.$route.query.id
            }
            if (type === 'save' && !this.$route.query.id) {
              this.$router.replace({
                path: this.$route.path,
                query: _query
              })
            } else if (type === 'submit') {
              this.$router.go(-1)
            }
          }
        })
      })
    },
    // 调整数据、校验
    // flag: 保存-1；提交-2
    formatSubmitData(flag) {
      try {
        this.$store.commit('startLoading')
        let _submitTableData = [],
          costSharingDataList = this.$refs.costShareRef?.pageConfig?.[0]?.grid.dataSource || [],
          openSwitch = this.$refs.costShareRef?.openSwitch || false, // 整单分摊的switch
          descriptionData = this.$refs.relativeStateRef?.saveTxt(),
          projectDesc = '' // 项目名称（将需求明细的项目名称串起来）编号-名称；编号-名称

        // console.log("formatSubmitData的数据", costSharingDataList, openSwitch);

        // 整合需求明细的数据----------------------------------------------------------
        // console.log("this.$refs.requireDetailRef", this.$refs.requireDetailRef);
        let {
          submitTableData,
          rowsSelectedInfo,
          dynamicFieldsMap,
          deleteIdList,
          requiredCols,
          dataSource
        } = this.$refs.requireDetailRef
        submitTableData = cloneDeep(submitTableData)

        console.log(this.$t('要提交的数据'), submitTableData, rowsSelectedInfo)

        // 1.1 整合 动态配置字段的数据
        submitTableData.forEach((item) => {
          console.log('遍历提交submitTableData', item)
          // 行的额外列数据
          // let _otherDataPew = item?.otherDataPew
          //   ? JSON.parse(item?.otherDataPew)
          //   : null;
          // let otherDataPew = cloneDeep(_otherDataPew);

          // console.log("渠道的值", otherDataPew);

          // 填充项目名称字段
          if (item.projectCode || item.projectName) {
            projectDesc += item?.projectCode + '-' + item?.projectName + ';'
          }
          // 修复文件类型,从string改成对象
          if (item.file && typeof item.file == 'string') {
            item.file = JSON.parse(item.file)
            if (item.file.length) {
              item.file.forEach((f) => {
                f.sysFileId = f.id
              })
            }
          } else if (!item.file) {
            item.file = []
          }
          // 成本中心
          item.costSharing = {
            id: item.costSharing?.id,
            costSharingAccCode: item.costSharing?.costSharingAccCode,
            costSharingAccName: item.costSharing?.costSharingAccName
          }

          // 推荐供应商 id supplierId supplierCode supplierName
          item.supplier = {
            supplierId: item.supplierId,
            supplierCode: item.supplierCode,
            supplierName: item.supplierName
          }

          // 如果是 code-name 拼接的
          if (item.categoryName && item.categoryName.includes(' - ')) {
            item.categoryName = item.categoryName.split(' - ')[1]
          }
          if (item.unitName && item.unitName.includes(' - ')) {
            item.unitName = item.unitName.split(' - ')[1]
          }
          if (item.orderUnitName && item.orderUnitName.includes(' - ')) {
            item.orderUnitName = item.orderUnitName.split(' - ')[1]
          }
          if (item.currencyName && item.currencyName.includes(' - ')) {
            item.currencyName = item.currencyName.split(' - ')[1]
          }
          // 日期字段字段转换
          this.dateToTimeStamp(item)

          // 动态字段
          item.fieldDataList = []
          for (let key in item) {
            let keyFind = dynamicFieldsMap.get(key)
            if (keyFind && item[key]) {
              item.fieldDataList.push({
                ...keyFind,
                fieldData: item[key],
                recordId: keyFind.recordId ? keyFind.recordId : item.id
              })
            }
          }
        })

        // 1.2 整合行的额外数据：采购申请提交的是修改数据，故用submitTableData整合行的额外数据。采购订单需要改成 ref.getCurrentViewRecords()去整合
        submitTableData.forEach((item) => {
          if (item.addId && rowsSelectedInfo[`row-${item.addId}`]) {
            _submitTableData.push({
              ...item,
              ...rowsSelectedInfo[`row-${item.addId}`]
            })
            console.log('进入的是if', item)
          } else {
            // 新增的，没有行额外数据的 行数据
            _submitTableData.push(item)
          }
        })

        if (!_submitTableData.length) {
          _submitTableData = dataSource
        }

        // 1.3 需求明细 做校验
        _submitTableData.forEach((row, index) => {
          // 修复文件类型,从string改成对象
          if (row.file && typeof row.file == 'string') {
            row.file = JSON.parse(row.file)
            if (row.file.length) {
              row.file.forEach((f) => {
                f.sysFileId = f.id
              })
            }
          } else if (!row.file) {
            row.file = []
          }
          // 校验------------------------
          if (flag == 2) {
            // 校验1. 必填--提交才校验必填
            requiredCols.forEach((rc) => {
              if (!row[rc.field] && row[rc.field] !== 0) {
                // // 当为非采时，不触发币种必填校验
                // if (
                //   !(
                //     rc.field === 'currencyName' &&
                //     (this.requestHeader.businessTypeCode === 'BTTCL001' ||
                //       this.requestHeader.businessTypeCode === 'BTTCL002' ||
                //       this.requestHeader.businessTypeCode === 'BTTCL003')
                //   )
                // ) {
                //   }
                this.$toast({
                  content: `${this.$t('请填写第')}${Number(index + 1)}${this.$t('行的')}${
                    rc.headerText
                  }`,
                  type: 'error'
                })
                throw new Error(`请填写第${Number(index + 1)}行的${rc.headerText}`)
              }
            })
          }
          // 校验2 长度校验
          if (!this.judgeLength(row, index)) {
            throw new Error()
          }
          const { englishAndNumReg } = RegExpMap
          // 校验2： 预算编号为英文/数字
          if (row.budgetCode) {
            if (!englishAndNumReg.test(row.budgetCode)) {
              this.$toast({
                content: `${this.$t('第')}${Number(index + 1)}${this.$t(
                  '行的预算编号输入错误：可输入字母/数字类型'
                )}`,
                type: 'error'
              })
              throw new Error(`第${Number(index + 1)}行的预算编号输入错误：可输入字母/数字类型`)
            }
          }
          // 校验3：预测采购周期起止
          if (row?.startDate && row?.endDate) {
            if (this.judgeTime(row.startDate, row.endDate)) {
              this.$toast({
                content: `${this.$t('第')}${Number(index + 1)}${this.$t(
                  '行的预测采购周期起应小于预测采购周期止'
                )}`,
                type: 'error'
              })
              throw new Error(`第${Number(index + 1)}行的预测采购周期起应小于预测采购周期止`)
            }
          }

          const { phoneNumReg, hasChinese } = RegExpMap
          // 校验4 关联单据号 非中文
          if (row?.associateExtDocNo) {
            if (hasChinese.test(row.associateExtDocNo)) {
              this.$toast({
                content: `${this.$t('第')}${Number(index + 1)}${this.$t(
                  '行的关联单据号输入错误：不可输入中文'
                )}`,
                type: 'error'
              })
              throw new Error(`第${Number(index + 1)}行的关联单据号输入错误：不可输入中文`)
            }
          }

          // 废弃：因为接口改了 校验4：技术对接人 得是用户层级的数据

          // 校验5：联系方式  得是11位的数字
          if (row.linkWay) {
            let isUnValidMobile = this.unMobileCompany.some(
              (item) => item.itemCode === this.$refs.headerTop?.addForm?.companyCode
            )
            if (!phoneNumReg.test(row.linkWay) && !isUnValidMobile) {
              this.$toast({
                content: this.$t('请输入正确的联系方式')
              })
              throw new Error(`第${Number(index + 1)}行，请输入正确的联系方式`)
            }
          }

          // 校验6：关联资产编号 只能输入数字和英文
          if (row?.assetCode) {
            if (!/^[A-Za-z0-9]+$/g.test(row.assetCode)) {
              this.$toast({
                content: `${this.$t('第')}${Number(index + 1)}${this.$t(
                  '行的关联资产编号输入错误：可输入字母/数字类型'
                )}`,
                type: 'error'
              })
              throw new Error(`第${Number(index + 1)}行的关联资产编号输入错误：可输入字母/数字类型`)
            }
          }
        })

        console.log('需求明细 整合完的数据', _submitTableData)

        // 2. 整合整单分摊----------------------------------------------------
        if (openSwitch) {
          if (Array.isArray(costSharingDataList) && costSharingDataList.length) {
            let sum = 0
            costSharingDataList.forEach((cs) => {
              sum += +cs.appProportion
            })
            if (sum != 100) {
              this.$toast({
                content: `${this.$t('启用了整单分摊后比例和须为100')}`,
                type: 'warning'
              })
              throw new Error(`启用了整单分摊后比例和须为100`)
            }
          } else {
            this.$toast({
              content: `${this.$t('启用了整单分摊后比例和须为100')}`,
              type: 'warning'
            })
            throw new Error(`启用了整单分摊后比例和须为100`)
          }
        }

        // 3. 整合附件----------------------------------------------------------
        // 因为只有主单是要手动上传的，所以这里去拿主单的，明细的就是requireFiles
        let uploadFiles = []
        if (this.moduleFileList?.[0]?.id) {
          uploadFiles = this.$refs['relativeFileRef']?.getUploadFlies(this.moduleFileList[0].id) // 拿到主单的上传附件
        }
        // console.log("getUploadFlies", uploadFiles, this.requireFiles);

        let params = {
          headerId: this.$route.query.id || null,
          requestHeader: {
            ...this.requestHeader,
            id: this.$route.query.id || null,
            projectDesc: projectDesc,
            status: this.headerInfo?.status || 0,
            requestType: 0 // requestType  0-采购申请、1-寻源申请
          }, // 主单信息
          itemDataList: _submitTableData,
          delIds: deleteIdList,
          costSharingDataList: costSharingDataList, // 整单分摊
          fileDataList: uploadFiles, // 相关附件
          descriptionData: descriptionData
            ? {
                content: descriptionData
              }
            : null // 描述
        }
        // 如果是商城来的数据，添加下 省份编码、店铺数据
        if (this.$route.query?.souce == 2) {
          params.requestHeader = {
            ...params.requestHeader,
            provinceCode: this.headerInfo?.provinceCode,
            shopId: this.headerInfo?.shopId,
            shopName: this.headerInfo?.shopName
          }
        }
        console.log('保存数据时提交的数据-------------：', params)
        return params
      } catch (e) {
        console.log(this.$t('报错'), e)
        this.$store.commit('endLoading')
      }
    },
    // 时间格式字段转换
    dateToTimeStamp(data) {
      const timestampFields = ['goodGoodsTime', 'demandStartTime', 'demandArriverTime']
      timestampFields.forEach((item) => {
        if (item in data && data[item]) {
          let _time = new Date(data[item]).getTime()
          data[item] = _time ? _time.toString() : _time
        }
      })
    },
    // 校验长度
    judgeLength(row, index) {
      let res = true
      lengthArr.forEach((len) => {
        if (row[len.field] && row[len.field].length > len.length) {
          res = false
          this.$toast({
            content: this.$t(
              `第${Number(index + 1)}行${len.headerText}字段长度应小于${len.length}个字符`
            )
          })
          throw new Error(
            `第${Number(index + 1)}行${len.headerText}字段长度应小于${len.length}个字符`
          )
        }
      })
      return res
    },

    // 格式化 附件，添加parentId
    formatRequireFiles() {
      let _res = []
      this.requireFiles.forEach((item) => {
        let _item = {
          parentId: this.moduleFileList[1].id,
          fileName: item.fileName,
          fileSize: item.fileSize,
          fileType: item.fileType,
          createUserName: this.userInfo.username,
          createTime: new Date(),
          itemNo: item.itemNo,
          itemCode: item.itemCode,
          itemName: item.itemName,
          skuCode: item.skuCode,
          skuName: item.skuName,
          spec: item.spec
        }
        _res.push(_item)
      })
      return _res
    },

    // 比较时间先后，先：true
    judgeTime(start, end) {
      if (new Date(start).getTime() >= new Date(end).getTime()) {
        return true
      }
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
.e-grid td.e-rowcell .e-frame {
  display: none;
}
/deep/ .e-frame {
  display: none;
}
/deep/ .top-info {
  margin-top: 20px;
}
/deep/ .top-info .header-box {
  padding-top: 30px;
  padding-bottom: 43px;
}
</style>
