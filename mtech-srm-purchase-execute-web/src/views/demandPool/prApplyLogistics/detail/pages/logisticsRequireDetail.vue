<template>
  <div :class="['pt20', 'grid-wrap', $route.query.type == 'view' && 'grid-wrap-page']">
    <sc-table
      ref="sctableRef"
      :loading="loading"
      :is-show-refresh-bth="false"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      keep-source
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #cellFieldVoList="{ row }">
        <span style="color: #2783fe; cursor: pointer" @click="showFiledVolist(row)">{{
          $t('柜量明细')
        }}</span>
      </template>
      <!-- 待确认 -->
      <template #cellFie>
        <span style="color: #2783fe">{{ $t('暂无附件') }}</span>
      </template>
    </sc-table>
    <!-- <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    /> -->
    <mt-dialog
      ref="filedVolistRef"
      css-class="column-setting"
      :buttons="buttons"
      :header="$t('柜量明细')"
    >
      <mt-template-page ref="templateRef" class="template-height" :template-config="pageConfig">
      </mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
// 设置下拉的数据源
import ScTable from '@/components/ScTable/src/index'
import { fixLeftColumns, fixRightColumns } from '../config/logisticsRequireDetail.js'
export default {
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    },
    userInfo: {
      type: Object,
      default: () => {}
    }
  },
  components: { ScTable },
  data() {
    return {
      loading: false,
      columns: [],
      editRules: {},
      dataSource: [],
      tableData: [],
      fieldDataSource: [],
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        totalRecordsCount: 0,
        pageSizes: [10, 20, 50, 100, 200]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.cancel,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [],
          grid: {
            columnData: [
              {
                width: '150',
                field: 'fieldCode',
                headerText: this.$t('月份')
              },
              {
                width: '150',
                field: 'fieldData',
                headerText: this.$t('柜量')
              }
            ],
            dataSource: [],
            allowPaging: false,
            asyncConfig: {}
          }
        }
      ],
      LogisticsMethodCodeMap: {
        1: 'itemSeaVo',
        3: 'itemRailwayVo',
        5: 'itemTrunkVo'
      },
      dictItems: {},
      stepObj: {}
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    editColums() {
      return [
        {
          field: 'transportTermsCode',
          // title: i18n.t('运输条款编码'),
          width: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.transportTermsCode}
                  options={this.dictItems.LOGISTICS_POINT_RAILWAY_TRANSPORTTERMS}
                  option-props={{ label: 'dictName', value: 'dictCode' }}
                  placeholder={this.$t('请选择')}
                  filterable
                  clearable
                  transfer
                  onChange={() => {
                    row.transportTermsName =
                      this.dictItems.LOGISTICS_POINT_RAILWAY_TRANSPORTTERMS.find(
                        (item) => item.value === row.dictCode
                      )?.dictName
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'tradeTermsCode',
          // title: i18n.t('贸易条款编码'),
          width: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.tradeTermsCode}
                  options={this.dictItems.LOGISTICS_POINT_RAILWAY_TRADETERMS}
                  option-props={{ label: 'dictName', value: 'dictCode' }}
                  placeholder={this.$t('请选择')}
                  filterable
                  clearable
                  transfer
                  onChange={() => {
                    row.tradeTermsName = this.dictItems.LOGISTICS_POINT_RAILWAY_TRADETERMS.find(
                      (item) => item.value === row.dictCode
                    )?.dictName
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'bidDemurrage',
          // title: i18n.t('场内免箱期'),
          editRender: { name: 'input', attrs: { type: 'number', placeholder: this.$t('请输入') } }
        },
        {
          field: 'bidDetention',
          // title: i18n.t('场外免箱期'),
          editRender: { name: 'input', attrs: { type: 'number', placeholder: this.$t('请输入') } }
        },
        {
          field: 'cabinetType',
          // title: i18n.t('柜型'),
          editRender: { name: 'input', attrs: { placeholder: this.$t('请输入') } }
        },
        {
          field: 'receivePlace',
          // title: i18n.t('收货地'),
          width: 140,
          editRender: { name: 'input', attrs: { placeholder: this.$t('请输入') } }
        },
        {
          field: 'endAddress',
          // title: i18n.t('目的地'),
          width: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.endAddress}
                  options={this.dictItems.LOGISTICS_POINT_RAILWAY_ENDADDRESS}
                  option-props={{ label: 'dictName', value: 'dictCode' }}
                  placeholder={this.$t('请选择')}
                  filterable
                  clearable
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'endTransportMethod',
          // title: i18n.t('目的港运输方式'),
          width: 150,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.endTransportMethod}
                  options={this.dictItems.END_TRANSPORT_METHOD}
                  option-props={{ label: 'dictName', value: 'dictCode' }}
                  placeholder={this.$t('请选择')}
                  filterable
                  clearable
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'associateOuterItemNo',
          // title: i18n.t('关联外部行号'),
          width: 160,
          editRender: { name: 'input', attrs: { type: 'number', placeholder: this.$t('请输入') } }
        },
        {
          field: 'associateOuterDocNo',
          // title: i18n.t('关联外部单据编号'),
          width: 170,
          editRender: { name: 'input', attrs: { placeholder: this.$t('请输入') } }
        },
        {
          field: 'startPlaceCode',
          // title: i18n.t('起运地编码'),
          width: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.startPlaceCode}
                  options={this.dictItems.LOGISTICS_POINT_RAILWAY_STARTADDRESS}
                  option-props={{ label: 'dictName', value: 'dictCode' }}
                  placeholder={this.$t('请选择')}
                  filterable
                  clearable
                  transfer
                  onChange={() => {
                    row.startPlaceName = this.dictItems.LOGISTICS_POINT_RAILWAY_STARTADDRESS.find(
                      (item) => item.value === row.dictCode
                    )?.dictName
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'loadingAddr',
          // title: i18n.t('装货地'),
          width: 160,
          editRender: { name: 'input', attrs: { placeholder: this.$t('请输入') } }
        },
        // {
        //   field: 'domesticStartStationCode',
        //   // title: i18n.t('国内始发站code'),
        //   width: 160,
        //   editRender: { name: 'input', attrs: { placeholder: this.$t('请输入') } }
        // },
        {
          field: 'domesticStartStationName',
          // title: i18n.t('国内始发站name'),
          width: 170,
          editRender: { name: 'input', attrs: { placeholder: this.$t('请输入') } }
        },
        // {
        //   field: 'domesticEndStationCode',
        //   // title: i18n.t('国内目的站code'),
        //   width: 160,
        //   editRender: { name: 'input', attrs: { placeholder: this.$t('请输入') } }
        // },
        {
          field: 'domesticEndStationName',
          // title: i18n.t('国内目的站name'),
          width: 170,
          editRender: { name: 'input', attrs: { placeholder: this.$t('请输入') } }
        },
        // {
        //   field: 'endStationCode',
        //   // title: i18n.t('目的站code'),
        //   width: 160,
        //   editRender: { name: 'input', attrs: { placeholder: this.$t('请输入') } }
        // },
        {
          field: 'endStationName',
          // title: i18n.t('目的站name'),
          width: 170,
          editRender: { name: 'input', attrs: { placeholder: this.$t('请输入') } }
        },
        {
          field: 'estimationCabinetQuantity',
          // title: i18n.t('预估柜量'),
          width: 170,
          editRender: { name: 'input', attrs: { type: 'number', placeholder: this.$t('请输入') } }
        },
        {
          field: 'pickupLocation',
          // title: i18n.t('提箱地点'),
          width: 170,
          editRender: { name: 'input', attrs: { placeholder: this.$t('请输入') } }
        },
        {
          field: 'demandTimeliness',
          // title: i18n.t('需求时效性'),
          width: 160,
          editRender: { name: 'input', attrs: { placeholder: this.$t('请输入') } }
        }
      ]
    },
    logisticsMethodCode() {
      return this.detailInfo.logisticsMethodCode ? Number(this.detailInfo.logisticsMethodCode) : ''
    },
    // 编辑配置
    editConfig() {
      return {
        trigger: this.$route.query?.type === 'view' ? 'manual' : 'click',
        mode: 'row',
        showStatus: true
      }
    },
    // 操作栏
    toolbar() {
      return this.$route.query?.type === 'view'
        ? []
        : [
            { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
            { code: 'save', name: this.$t('保存'), status: 'info', loading: false },
            { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
          ]
    }
  },
  watch: {
    detailInfo: {
      handler(v) {
        v.templateId && this.getLogisticsFields()
      },
      immediate: true
    }
  },
  mounted() {
    this.initDictItems()
  },

  methods: {
    // 获取字段明细
    getLogisticsFields() {
      this.$API.purchaseRequest
        .getLogisticsFields({ templateId: this.detailInfo.templateId || '' })
        .then((res) => {
          this.columns = this.defineGridColumn(res.data)
          this.tableData = this.defineGridData(this.detailInfo?.itemVoList)
        })
    },
    // 初始化获取字典数据
    async initDictItems() {
      let codeList = [
        { code: 'LOGISTICS_POINT_RAILWAY_ENDADDRESS', type: 'string' }, // 目的地
        { code: 'LOGISTICS_POINT_RAILWAY_STARTADDRESS', type: 'string' }, // 起始地
        { code: 'LOGISTICS_POINT_RAILWAY_TRANSPORTTERMS', type: 'string' }, // 运输条款
        { code: 'LOGISTICS_POINT_RAILWAY_TRADETERMS', type: 'string' }, // 贸易条款
        { code: 'LOGISTICS_POINT_RAILWAY_RAILWAYMODE', type: 'string' }, // 铁运模式
        { code: 'END_TRANSPORT_METHOD', type: 'string' } // 目的港运输方式
      ]
      await this.$API.masterData.getNumDictListAllByCode(codeList).then((res) => {
        if (res.code === 200) {
          this.dictItems = res.data
        }
      })
      // 获取币种名称
      await this.$API.masterData.queryAllCurrency().then((res) => {
        this.$set(this.dictItems, 'currencyList', res.data || [])
      })
    },
    // 设置列表字段
    defineGridColumn(data) {
      let _columns = []
      data?.forEach((item) => {
        const find = this.editColums.find((i) => i.field === item?.fieldCode)
        if (item.fieldCode.includes('step') && 'stepList' in item) {
          this.stepObj[item.fieldCode] = []
          item.stepList.forEach((i) => {
            _columns.push({
              field: i.stepCode,
              title: i.stepName,
              width: 180,
              editRender: {
                name: 'input',
                attrs: {
                  type: item.fieldCode === 'stepTrunk' ? 'number' : '',
                  placeholder: this.$t('请输入')
                }
              }
            })
            this.stepObj[i.fieldCode].push({
              stepCode: i.stepCode,
              stepName: i.stepName,
              dataType: i.fieldCode
            })
          })
        } else {
          _columns.push({
            field: item?.fieldCode,
            title: item?.fieldName,
            width: 140,
            editRender: { name: 'input', attrs: { placeholder: this.$t('请输入') } },
            ...find
          })
        }
      })
      return [...fixLeftColumns, ..._columns, ...fixRightColumns]
    },
    // 获取tableData
    getTableData() {
      const key = this.LogisticsMethodCodeMap[this.logisticsMethodCode]
      const dataSource = this.tableRef.getTableData().tableData
      const _tableData = []
      dataSource.forEach((item) => {
        item.dynamicFields = {}
        for (let i in this.stepObj) {
          item.dynamicFields[i] = []
          this.stepObj[i].forEach((step) => {
            let stepCode = step.stepCode
            item.dynamicFields[i].push({
              fieldCode: stepCode,
              fieldName: step.stepName,
              fieldData: item[stepCode] || '',
              dataType: step.dataType
            })
            // delete item[i]
          })
        }

        const obj = {
          [key]: { ...item },
          id: item.id.includes('row_') ? '' : item.id
        }
        _tableData.push(obj)
      })

      return _tableData
    },
    // 设置列表数据
    defineGridData(data) {
      if (!data) return []
      const key = this.LogisticsMethodCodeMap[this.logisticsMethodCode]
      data.forEach((item) => {
        let logisticsDTO = item[key]
        for (let i in logisticsDTO) {
          if (i === 'dynamicFields') {
            for (let stepField in logisticsDTO['dynamicFields']) {
              logisticsDTO['dynamicFields'][stepField].forEach((step) => {
                item[step.fieldCode] = step.fieldData
              })
            }
          }
          item[i] = logisticsDTO[i]
        }
        delete item[key]
      })
      return data
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'save':
          this.handleSave()
          break
        case 'delete':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除？')
            },
            success: () => {
              this.handleDelete(selectedRecords)
            }
          })
          break
        case 'enable':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认启用？')
            },
            success: () => {
              this.handleEnable(selectedRecords)
            }
          })
          break
        case 'disable':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认停用？')
            },
            success: () => {
              this.handleDisable(selectedRecords)
            }
          })
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    // 新增数据
    handleAdd() {
      this.tableRef.insert([{}])
      this.$nextTick(() => {
        const currentViewRecords = this.tableRef.getTableData().visibleData
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },
    // 删除数据
    handleDelete(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      const _dataSource = this.tableRef.getTableData().tableData
      this.tableData = _dataSource.filter((item) => !ids.includes(item.id))
      this.handleSave()
    },
    // 显示柜量明细
    showFiledVolist(data) {
      this.$refs.filedVolistRef.ejsRef.show()
      this.$set(this.pageConfig[0].grid, 'dataSource', data?.fieldVoList || [])
    },
    cancel() {
      this.$refs.filedVolistRef.ejsRef.hide()
    },
    async handleSave() {
      await this.delay(800)
      this.$emit('itemSave')
    },
    // 分页的两个方法
    handleCurrentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.getModuleData()
    },

    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.getModuleData()
    },
    // 表格 - 工具 - 延时提交
    delay(ms) {
      return new Promise((resolve) => setTimeout(resolve, ms))
    }
  }
}
</script>
<style lang="scss"></style>
<style lang="scss" scoped>
.full-height {
  background: #fff;
}
// .e-grid td.e-rowcell .e-frame {
//   position: relative;
//   right: 6px;
// }
/deep/ .pe-edit-grid .e-rowcell .e-css .e-icons {
  position: relative;
  right: 6px;
}
.e-css {
  display: none;
}
.toolbar-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;

  .one-bar {
    display: flex;
    align-items: center;
    margin-right: 20px;
    cursor: pointer;
    .mt-icons {
      font-size: 14px;
      color: #4f5b6d;
      margin-right: 5px;
    }
    span {
      word-break: keep-all;
      font-size: 14px;
      color: #4f5b6d;
      font-weight: normal;
    }
  }

  .flex1 {
    flex: 1;
  }
}
</style>
