<template>
  <div>
    <div class="grid-edit-column mt-flex-direction-column">
      <mt-input
        :id="data.column.field"
        v-model="data[data.column.field]"
        style="display: none"
      ></mt-input>
      <div class="field-content" v-if="!allowEditing">
        <span>{{ data[data.column.field] }}</span>
      </div>
      <div class="field-content" v-else>
        <debounce-filter-select
          :open-dispatch-change="false"
          @change="selectChange"
          v-model="data['receiveUserId']"
          :request="getUser"
          :data-source="acceptorOptions"
          :show-clear-button="false"
          :placeholder="$t('请选择')"
          :fields="{ text: 'text', value: 'value' }"
        ></debounce-filter-select>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      data: {},
      acceptorOptions: [],
      allowEditing: true
    }
  },
  mounted() {
    console.log(this.data[this.data.column.field], '我是收货人或者验收人')
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    if (!this.data[this.data.column.field]) {
      this.getUser({ text: '' })
    }
    if (this.data[this.data.column.field]) {
      let text1 = this.data[this.data.column.field]
      this.getUser({ text: text1 })
    }
    // 2. 如果勾选有变化
    // this.$bus.$on('acceptancePlanSetAcceptor', (e) => {
    //   if (e.buyerUserName) {
    //     this.getUser({ text: e.buyerUserName })
    //   }
    //   this.selectChange({
    //     itemData: { name: e.buyerUserName, value: e.buyerUserId }
    //   })
    //   this.data[this.data.column.field] = e.buyerUserName
    //   this.data.acceptorId = e.buyerUserId
    // })
  },
  methods: {
    getUser(e) {
      const { text: fuzzyName } = e
      this.acceptorOptions = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data.forEach((item) => {
          tmp.push({
            ...item,
            // text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`,
            text: `${item.employeeCode}-${item.employeeName}`,
            value: item.uid,
            name: item.employeeName
          })
        })
        this.acceptorOptions = tmp
      })
    },
    selectChange(e) {
      this.$parent.$emit('selectedChanged', {
        fieldCode: this.data.column.field,
        itemInfo: {
          employeeName: e.itemData.name,
          userId: e.itemData.value,
          linkWay: e.itemData.phoneNum,
          addId: this.data.addId
        }
      })
      if (this.data.column.field === 'receiveUserName') {
        this.$bus.$emit('requireDetailSetlinkWay', e.itemData.phoneNum)
        this.data.linkWay = e.itemData.phoneNum
      }
    }
  },

  beforeDestroy() {
    this.$bus.$off(`acceptancePlanSetAcceptor`)
  }
}
</script>
