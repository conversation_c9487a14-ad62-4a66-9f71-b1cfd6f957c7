<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="$t('批量编辑')"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="requiredDeliveryDate" :label="$t('要求交期')">
        <mt-date-picker
          v-model="addForm.requiredDeliveryDate"
          :show-clear-button="true"
          :placeholder="$t('请选择要求交期')"
        ></mt-date-picker>
      </mt-form-item>

      <mt-form-item prop="budgetCode" :label="$t('预算编号')">
        <mt-input
          v-model="addForm.budgetCode"
          :show-clear-button="true"
          :maxlength="50"
          :placeholder="$t('请输入预算编号')"
        ></mt-input>
      </mt-form-item>

      <!-- <mt-form-item prop="taxid" :label="$t('税率')">
        <mt-select
          ref="taxidRef"
          v-model="addForm.taxid"
          :data-source="taxidData"
          :show-clear-button="true"
          :fields="{ value: 'id', text: 'taxRate' }"
          :placeholder="$t('请选择税率')"
        ></mt-select>
      </mt-form-item> -->

      <mt-form-item prop="postingAccountName" :label="$t('总账科目')">
        <mt-input
          v-model="addForm.postingAccountName"
          :show-clear-button="true"
          :maxlength="50"
          :placeholder="$t('请输入总账科目')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="receiveUserId" :label="$t('收货人')">
        <!-- <mt-select
          ref="receiveUserIdRef"
          v-model="addForm.receiveUserId"
          :data-source="receiveUserIdData"
          :show-clear-button="true"
          :fields="{ value: 'id', text: 'employeeName' }"
          :placeholder="$t('请选择收货人')"
        ></mt-select> -->
        <mt-input
          v-model="addForm.receiveUserId"
          :show-clear-button="true"
          :maxlength="50"
          :placeholder="$t('请输入收货人')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="linkWay" :label="$t('联系方式')">
        <mt-input
          v-model="addForm.linkWay"
          :show-clear-button="true"
          :maxlength="50"
          :placeholder="$t('请输入联系方式')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="receiveAddress" :label="$t('收货地址')">
        <mt-input
          v-model="addForm.receiveAddress"
          :show-clear-button="true"
          :maxlength="200"
          :placeholder="$t('请输入收货地址')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="packageMethod" :label="$t('包装方式')">
        <mt-input
          v-model="addForm.packageMethod"
          :show-clear-button="true"
          :maxlength="50"
          :placeholder="$t('请输入包装方式')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="packageSpec" :label="$t('包装规格')">
        <mt-input
          v-model="addForm.packageSpec"
          :show-clear-button="true"
          :maxlength="50"
          :placeholder="$t('请输入包装规格')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="packageDesc" :label="$t('包装说明')">
        <mt-input
          v-model="addForm.packageDesc"
          :show-clear-button="true"
          :maxlength="200"
          :placeholder="$t('请输入包装说明')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="seriesName" :label="$t('关联产品系列名称')">
        <mt-input
          v-model="addForm.seriesName"
          :show-clear-button="true"
          :maxlength="50"
          :placeholder="$t('请输入关联产品系列名称')"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="supplierId" :label="$t('推荐供应商')">
        <mt-input
          v-model="addForm.recommendSupplierName"
          :show-clear-button="true"
          :maxlength="50"
          :placeholder="$t('请输入推荐供应商')"
        ></mt-input>
      </mt-form-item>

      <!-- <mt-form-item prop="costSharingAccName" :label="$t('成本中心')">
        <mt-input
          v-model="addForm.costSharingAccName"
          :show-clear-button="true"
          :maxlength="50"
          :placeholder="$t('请输入成本中心')"
        ></mt-input>
      </mt-form-item> -->

      <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
        <mt-input
          v-model="addForm.remark"
          :show-clear-button="true"
          :multiline="true"
          :maxlength="200"
          :placeholder="$t('请输入备注')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
// import common from "@/utils/constant";
// import { formatDate, formatRules } from "@/utils/util";
export default {
  props: {
    taxidData: {
      type: Array,
      default: () => {}
    },
    // receiveUserIdData: {
    //   type: Array,
    //   default: () => {},
    // },
    supplierIdData: {
      type: Array,
      default: () => {}
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        requiredDeliveryDate: null,
        budgetCode: null,
        taxid: null,
        postingAccountName: null,
        receiveUserId: null,
        linkWay: null,
        receiveAddress: null,
        packageMethod: null,
        packageSpec: null,
        packageDesc: null,
        seriesName: null,
        supplierId: null,
        costSharingAccName: null,
        remark: null
      },
      rules: {
        costSharingAccCode: [
          {
            required: true,
            message: this.$t('请选择成本中心'),
            trigger: 'blur'
          }
        ],
        appProportion: [
          {
            required: true,
            message: this.$t('请输入分摊比例'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    confirm() {
      console.log(this.$t('表单数据'), this.addForm)

      let params = {}
      for (var i in this.addForm) {
        if (this.addForm[i]) {
          params[i] = this.addForm[i]
        }
      }
      if (this.addForm.supplierId && this.$refs.supplierIdRef) {
        let _data = this.$refs.supplierIdRef.ejsRef.getDataByValue(this.addForm.supplierId)
        // console.log("业务类型_data", _data);
        params.supplierName = _data.supplierName
        params.supplierCode = _data.supplierCode
      }

      if (this.addForm.receiveUserId && this.$refs.receiveUserIdRef) {
        let _data = this.$refs.receiveUserIdRef.ejsRef.getDataByValue(this.addForm.receiveUserId)
        console.log('收货人_data', _data)
        params.receiveUserId = _data.id
        params.receiveUserName = _data.employeeName
      }

      this.$emit('handleAddDialogShow', 'batchEditShow', false)
      this.$emit('confirmBatchSuccess', params)
      console.log(this.$t('最终的数据'), params)
    },

    handleClose() {
      this.$emit('handleAddDialogShow', 'batchEditShow', false)
    }
  }
}
</script>

<style></style>
