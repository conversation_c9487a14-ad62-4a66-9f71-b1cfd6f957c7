<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <div class="header-box">
      <!-- 左侧的信息 -->
      <!-- <div class="status mr20">
        {{ headerInfo && headerInfo.statusTxt }}
      </div> -->
      <div class="infos mr20" v-if="headerInfo && headerInfo.docNo">
        {{ (headerInfo && headerInfo.docNo) || '-' }}
      </div>
      <div class="infos mr20">
        {{ $t('创建人：') }}{{ headerInfo && headerInfo.createUserName }}
      </div>
      <div class="infos mr20" v-if="headerInfo && headerInfo.createTime">
        {{ $t('创建时间：') }}{{ headerInfo.createTime | formatTime }}
      </div>
      <div class="infos" v-if="headerInfo && headerInfo.updateTime">
        {{ $t('单据更新时间：') }}{{ headerInfo.updateTime | formatTime }}
      </div>
      <div class="middle-blank"></div>
      <!-- 右侧各种操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <div class="sort-box" @click="isExpand = !isExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>
    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="headerInfo" :validate-on-rule-change="false">
        <mt-form-item prop="businessTypeId" :label="$t('业务类型')">
          <mt-input v-model="headerInfo.businessTypeName" disabled></mt-input>
        </mt-form-item>
        <mt-form-item prop="title" :label="$t('名称')">
          <mt-input v-model="headerInfo.processDocName" disabled :maxlength="50"></mt-input>
        </mt-form-item>
        <mt-form-item prop="applicantName" :label="$t('申请人')" class="apply-item">
          <mt-input v-model="headerInfo.applicantName" disabled></mt-input>
        </mt-form-item>
        <mt-form-item prop="companyId" :label="$t('业务公司')" class="apply-item">
          <mt-input disabled v-model="headerInfo.companyName"></mt-input>
        </mt-form-item>
        <mt-form-item prop="categoryName" :label="$t('品类名称')" class="apply-item">
          <mt-input disabled v-model="headerInfo.categoryName"></mt-input>
        </mt-form-item>
        <mt-form-item prop="purchaserName" :label="$t('采购员')" class="apply-item">
          <mt-input disabled v-model="headerInfo.purchaserName"></mt-input>
        </mt-form-item>
        <mt-form-item prop="polName" :label="$t('POL')" class="apply-item">
          <mt-input disabled v-model="headerInfo.polName"></mt-input>
        </mt-form-item>
        <mt-form-item prop="podName" :label="$t('POD')" class="apply-item">
          <mt-input disabled v-model="headerInfo.podName"></mt-input>
        </mt-form-item>
        <mt-form-item prop="lineName" :label="$t('TCL LINE')" class="apply-item">
          <mt-input disabled v-model="headerInfo.lineName"></mt-input>
        </mt-form-item>
        <mt-form-item prop="mainLineName" :label="$t('MAIN LINE')" class="apply-item">
          <mt-input disabled v-model="headerInfo.mainLineName"></mt-input>
        </mt-form-item>
        <mt-form-item prop="demandTypeName" :label="$t('需求类型')" class="apply-item">
          <mt-input disabled v-model="headerInfo.demandTypeName"></mt-input>
        </mt-form-item>
        <mt-form-item prop="logisticsMethodName" :label="$t('物流方式')" class="apply-item">
          <mt-input disabled v-model="headerInfo.logisticsMethodName"></mt-input>
        </mt-form-item>
        <mt-form-item prop="countryName" :label="$t('国家')" class="apply-item">
          <mt-input disabled v-model="headerInfo.countryName"></mt-input>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input
            v-model="headerInfo.remark"
            :multiline="true"
            :maxlength="200"
            disabled
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { timeNumberToDate } from '@/utils/utils'
export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    },
    userInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isExpand: true,
      requestTypeOptions: [
        { text: this.$t('采购申请'), value: 0 },
        { text: this.$t('寻源申请'), value: 1 },
        { text: this.$t('合约需求'), value: 2 },
        { text: this.$t('滚动需求'), value: 3 },
        { text: this.$t('临时需求'), value: 4 }
      ]
    }
  },
  watch: {},

  filters: {
    formatTime: (e) => {
      if (e && !isNaN(e) && e.length == 13) {
        e = Number(e)
        return timeNumberToDate({
          formatString: 'YYYY-mm-dd HH:MM:SS',
          value: e
        })
      } else {
        return '-'
      }
    }
  },

  mounted() {
    this.type = this.$route.query.type
    this.sourceType = this.$route.query.source
  },
  methods: {
    goBack() {
      sessionStorage.removeItem('prApplyPurchaserInfo')
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 0 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }

      // &.apply-item {
      //   width: 350px;
      // }
    }
  }
}
</style>
