<template>
  <div class="two-form">
    <div
      v-if="fieldName != 'currencyName' && (!allowEditing || nowRowHasItemCode)"
      :class="['cell-changed', 'cell-changed-disabled']"
      id="cell-changed"
    >
      <mt-input :id="fieldName" style="display: none" :value="data[fieldName]"></mt-input>
      <mt-input
        v-if="fieldName != 'qualityExemptionMark'"
        v-model="data[fieldName]"
        disabled
      ></mt-input>
    </div>
    <div class="selects" v-else @click="handleSelectClick">
      <select-filter
        v-if="fieldName"
        :id="fieldName"
        :width="width"
        :fields="fields"
        :request-url="requestUrl"
        :request-key="requestKey"
        :init-val.sync="data[fieldName]"
        :label-show-obj="labelShowObj"
        :other-params="otherParams"
        @handleChange="handleChange"
        :disabled="isPermissionUserId && businessTypeCode"
        :remote-aotu-query="remoteAotuQuery"
      ></select-filter>
    </div>
  </div>
</template>

<script>
// 1. 默认无物料，这些显示下拉：品类、基本单位、采购单位....并且可以手输后调用接口，重新获取数据源
// 2. 选择了物料，这些变成被带出的字段
// 3. 不可修改的情况 allowEditing
export default {
  components: {
    // cellChanged: "@/components/normalEdit/cellChanged", // 单元格被改变（纯展示）
  },
  data() {
    return {
      width: 280,
      data: {},
      fieldName: '',
      nowRowHasItemCode: false, // 默认没有物料编码
      dataSource: [],
      fields: null,
      pld: '',
      changeRowObj: null, // 改变的额外行数据
      changeFieldObj: null, // 改变的其他列数据

      allowEditing: true,

      requestUrl: {},
      requestKey: '',
      labelShowObj: {}, // 下拉时候 显示成code-name的
      otherParams: null,
      isPermissionUserId: false, //
      businessTypeCode: '',
      remoteAotuQuery: true
    }
  },
  mounted() {
    this.fieldName = this.data.column.field
    this.nowRowHasItemCode = this.data.itemCode ? true : false

    this.width = this.data.column.selectWidth || 280

    // console.log(
    //   this.data.column.allowEditing,
    //   "我是双重组件的判断是否可以编辑===="
    // );
    this.allowEditing = this.data.column.allowEditing === false ? false : true
    if (!this.allowEditing) return

    const permissionUserInfo = sessionStorage.getItem('prApplyPurchaserInfo')
    let permissionUserId = ''
    if (permissionUserInfo && JSON.parse(permissionUserInfo)) {
      this.isPermissionUserId = false
      this.businessTypeCode = JSON.parse(permissionUserInfo)['businessTypeCode']
      permissionUserId = JSON.parse(permissionUserInfo)['uid']
    } else {
      this.isPermissionUserId = true
    }
    // 根据fieldName 设置 requestUrl 和 requestKey
    this.setRequestInfo(permissionUserId)

    // 监听是否有物料
    // this.$bus.$off(`itemCodeChange`);
    this.$bus.$on(`itemCodeChange1`, (txt) => {
      console.log('在双重单元格中，监听到了物料编码变化了--itemCodeChange1', this.fieldName, txt)
      this.nowRowHasItemCode = txt ? true : false
    })

    this.$bus.$on(`${this.fieldName}Change`, (txt) => {
      // console.log("￥emit的监听到了被展示的数据------", this.fieldName, txt);
      // that.data[_field] = txt;
      this.$set(this.data, this.fieldName, txt)
    })
  },
  methods: {
    handleSelectClick() {
      const permissionUserInfo = sessionStorage.getItem('prApplyPurchaserInfo')
      let permissionUserId = ''
      if (permissionUserInfo && JSON.parse(permissionUserInfo)) {
        this.isPermissionUserId = false
        this.businessTypeCode = JSON.parse(permissionUserInfo)['businessTypeCode']
        permissionUserId = JSON.parse(permissionUserInfo)['uid']
        this.setRequestInfo(permissionUserId)
      } else {
        this.$toast({ content: this.$t('请先选择采购员'), type: 'warning' })
        this.isPermissionUserId = true
      }
    },
    // 根据字段，设置 requestUrl 和 requestKey
    setRequestInfo(permissionUserId) {
      // 品类
      if (this.fieldName == 'categoryName') {
        this.requestUrl = {
          pre: 'masterData',
          url: 'getCategory'
        }
        this.requestKey = 'patternKeyword'
        this.otherParams = {
          permissionFlag: 1
        }
        if (permissionUserId) {
          this.otherParams.permissionUserId = permissionUserId
        }
        this.fields = { text: 'labelShow', value: 'labelShow' }
        this.changeRowObj = {
          categoryId: 'id',
          categoryCode: 'categoryCode',
          categoryName: 'categoryName'
        }
        this.labelShowObj = {
          code: 'categoryCode',
          name: 'categoryName'
        }
      } else if (this.fieldName == 'unitName') {
        // 基本单位
        this.requestUrl = {
          pre: 'masterData',
          url: 'getUnitByFilter'
        }
        this.requestKey = 'fuzzyParam'
        this.fields = { text: 'labelShow', value: 'labelShow' }
        this.changeRowObj = {
          unitId: 'id',
          unitCode: 'unitCode',
          unitName: 'unitName'
        }
        this.labelShowObj = {
          code: 'unitCode',
          name: 'unitName'
        }
      } else if (this.fieldName == 'orderUnitName') {
        // 采购单位
        this.requestUrl = {
          pre: 'masterData',
          url: 'getUnitByFilter'
        }
        this.requestKey = 'fuzzyParam'
        this.fields = { text: 'labelShow', value: 'labelShow' }
        this.changeRowObj = {
          orderUnitId: 'id',
          orderUnitCode: 'unitCode',
          orderUnitName: 'unitName'
        }
        this.labelShowObj = {
          code: 'unitCode',
          name: 'unitName'
        }
      } else if (this.fieldName == 'currencyName') {
        // 币种
        this.remoteAotuQuery = false
        this.requestUrl = {
          pre: 'masterData',
          url: 'getCurrencyAll'
        }
        this.requestKey = 'fuzzyParam'
        this.fields = { text: 'labelShow', value: 'labelShow' }
        this.changeRowObj = {
          currencyCode: 'currencyCode',
          currencyName: 'currencyName'
        }
        this.labelShowObj = {
          code: 'currencyCode',
          name: 'currencyName'
        }
      } else if (this.fieldName == 'buyerOrgName') {
        // 采购组
        this.requestUrl = {
          pre: 'masterData',
          url: 'getbussinessGroup'
        }
        this.requestKey = 'fuzzyParam'
        this.fields = { text: 'labelShow', value: 'labelShow' }
        this.changeRowObj = {
          buyerOrgId: 'id',
          buyerOrgCode: 'groupCode',
          buyerOrgName: 'groupName'
        }
        this.otherParams = {
          groupTypeCode: 'BG001CG'
        }
        this.labelShowObj = {
          code: 'groupCode',
          name: 'groupName'
        }
      }
    },

    handleChange(e) {
      // console.log(this.$t("下拉选择改变了"), this.fieldName, e);
      // 改变行的额外数据
      if (this.changeRowObj) {
        let _itemInfo = {}
        for (let i in this.changeRowObj) {
          _itemInfo[i] = e.itemData[this.changeRowObj[i]]
        }
        this.$parent.$emit('selectedChanged', {
          fieldCode: this.fieldName,
          itemInfo: _itemInfo
        })
      }

      // 改变行的其他列数据
      if (this.changeFieldObj) {
        for (let i in this.changeFieldObj) {
          this.$bus.$emit(`${i}Change`, e.itemData[this.changeFieldObj[i]] || null)
        }
      }
    }
  },

  beforeDestroy() {
    this.$bus.$off(`itemCodeChange`)
    this.$bus.$off(`${this.fieldName}Change`)
  }
}
</script>

<style lang="scss" scoped>
#cell-changed /deep/ .e-input.e-disabled {
  height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
  padding-left: 10px !important;
  background: #f5f5f5 !important;
}
</style>
