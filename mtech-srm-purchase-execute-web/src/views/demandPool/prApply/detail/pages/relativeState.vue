<template>
  <div class="intro-box">
    <!-- <div class="setting-banner" v-show="!isView" @click="saveTxt">
      <mt-icon name="icon_solid_Save" />
      <span>{{ $t('保存') }}</span>
    </div> -->

    <div class="edit-box">
      <mt-rich-text-editor
        ref="MtRichTextEditor"
        :height="500"
        :readonly="isView"
        css-class="rich-editor"
        :enable-html-encode="true"
        :toolbar-settings="toolbarSettings"
        :background-color="backgroundColor"
        v-model.trim="richValue"
        @change="changeText"
        @created="createdTextEditor"
      >
      </mt-rich-text-editor>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import MtRichTextEditor from '@mtech-ui/rich-text-editor'
Vue.use(MtRichTextEditor)

export default {
  props: {
    moduleKey: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isView: false,
      richValue: null,
      toolbarSettings: {
        enable: true,
        enableFloating: true,
        type: 'Expand',
        items: [
          'Bold',
          'Italic',
          'Underline',
          '|',
          'Formats',
          'Alignments',
          'OrderedList',
          'UnorderedList',
          '|',
          'CreateLink',
          'Image',
          'backgroundColor',
          '|',
          'SourceCode',
          'Undo',
          'Redo'
        ],
        itemConfigs: {}
      },
      backgroundColor: {
        columns: 10,
        modeSwitcher: true,
        colorCode: {
          Custom: [
            '#ffff00',
            '#00ff00',
            '#00ffff',
            '#ff00ff',
            '#0000ff',
            '#ff0000',
            '#000080',
            '#008080',
            '#008000',
            '#800080',
            '#800000',
            '#808000',
            '#c0c0c0',
            '#000000',
            '#000000'
          ]
        }
      }
    }
  },
  mounted() {
    // attr 传属性不行 只能这样写高度了
    this.$nextTick(() => {
      this.$refs.MtRichTextEditor.ejsInstances.height = '500'
      console.log(this.$refs.MtRichTextEditor.ejsInstances.height)
    })
    // 校验是否是查看
    if (this.$route.query.type == 'view') {
      this.isView = true
    }

    // 只有新增且没有id时不拿数据
    if (this.$route.query.id) {
      this.getModuleData()
    }
  },
  methods: {
    // 获取描述说明tab数据
    getModuleData() {
      let params = {
        docId: this.$route.query.id,
        moduleKey: this.moduleKey
      }
      this.$API.purchaseRequest.getModuleData(params).then((res) => {
        this.richValue = res?.data?.extClobResponse?.content || null
      })
    },

    saveTxt() {
      if (this.richValue) {
        return this.$refs.MtRichTextEditor.ejsRef.getHtml() || {}
      } else return null

      // if (this.$refs.MtRichTextEditor.ejsRef.getHtml().length <= 0) {
      //   this.$toast({ content: this.$t("请先添加描述说明"), type: "warning" });
      //   return;
      // }
      // let params = {
      //   docId: this.$route.query.id,
      //   content: this.$refs.MtRichTextEditor.ejsRef.getHtml(),
      // };
      // this.$API.purchaseRequest.saveHeaderState(params).then(() => {
      //   this.$toast({ content: this.$t("保存成功"), type: "success" });
      // });
    },
    changeText(value) {
      console.log(value)
    },
    createdTextEditor(value) {
      console.log('created', value)
    },
    // 获取html 获取 Text
    getHtml() {
      console.log(this.$refs.MtRichTextEditor.ejsRef.getHtml())
    },
    getText() {
      console.log(this.$refs.MtRichTextEditor.ejsRef.getText())
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .mt-rich-text-editor {
  .e-richtexteditor {
    height: 100% !important;
  }
  .e-toolbar-wrapper {
    height: 42px !important;
  }
  .e-rte-content {
    // height: 500px !important;
    height: calc(100% - 42px) !important;
  }
}
.intro-box {
  width: 100%;
  height: 100%;
  background: #fff;
  .setting-banner {
    width: 100%;
    height: 50px;
    cursor: pointer;
    padding: 0 20px;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(79, 91, 109, 1);

    i {
      font-size: 16px;
      line-height: 14px;
      display: inline-block;
      height: 16px;
      width: 16px;
      cursor: pointer;
    }

    span {
      display: inline-block;
      margin-left: 6px;
      cursor: pointer;
    }
  }

  .edit-box {
    padding: 0 20px;
    width: 100%;
    height: 100%;
    background: #fff;
  }
}
</style>

<style lang="scss">
.intro-box {
  .rich-editor {
    height: 100%;
  }
  .mt-rich-text-editor {
    height: 100%;
  }
}
</style>
