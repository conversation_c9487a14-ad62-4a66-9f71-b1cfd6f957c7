<template>
  <div class="full-height pt20">
    <button @click="getData">{{ $t('获取数据') }}</button>

    <mt-DataGrid
      id="Grid1"
      class="custom-toolbar-grid"
      :data-source="dataSource"
      :column-data="columnData"
      ref="dataGrid"
      :edit-settings="editSettings"
      :toolbar="toolbarOptions"
      :toolbar-click="toolbarClick"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
      @confirm-function="handleUploadSuccess"
      @remove-file="handleRemoveFile"
    ></mt-DataGrid>
  </div>
</template>

<script>
import { getComponent } from '@syncfusion/ej2-base'
import { columnData, editColumn, editData } from '../config/batchEditExample.js'
export default {
  data() {
    return {
      addDialogShow: false,
      dialogData: null,
      dataSource: editData,
      columnData: columnData,
      editSettings: {
        allowEditing: true,
        allowAdding: true,
        allowDeleting: true,
        showConfirmDialog: false,
        showDeleteConfirmDialog: true,
        mode: 'Normal' // 能显示行的编辑状态；能在actionCompelete中获取到最新数据
        // mode: "Batch", // 已修改的单元格背景色能标记；修改后可通过外部方法getBatchChanges获取到修改数据从而获取到最新更改的数据
      },
      toolbarOptions: []
    }
  },

  mounted() {
    editColumn.forEach((item) => (item.width = 150))
    console.log('_columnData', editColumn)
    this.columnData = columnData.concat(editColumn)

    this.toolbarOptions = [
      {
        text: this.t('新增'),
        id: 'addinlinebtn',
        prefixIcon: 'e-add',
        fn: this.handleAdd
      },
      'Edit',
      'Delete',
      'Update',
      'Cancel'
    ]

    // 造数据
    sessionStorage.setItem('headerStatus', 0)
  },

  methods: {
    actionBegin(args) {
      console.log('actionBegin', args)
      if (args.action == 'edit') {
        this.$set(this.dataSource, args.rowIndex, args.data)
      }
    },

    actionComplete(args) {
      console.log('actionComplete', args)
    },

    toolbarClick(args) {
      console.log('toolbarClick', args)
      // 自定义了一个行内新增按钮，为了给新增的这一行赋默认值
      if (args.item.id == 'addinlinebtn') {
        this.handleAdd()
      } else if (typeof args.item.fn == 'function') {
        args.item.fn(this.selectRowData)
      }
    },

    handleAdd() {
      // 新增时，判断下，是否已新增过空行???未做
      this.$refs.dataGrid.ejsRef.addRecord(this.newRowData())
    },

    // 根据列新建行数据
    newRowData() {
      let row = {
        addId: 'add' + Math.random().toString(36).substr(3, 8), // 新增时是addId，后台获取过来的数据是id
        claimStatus: 0 // 新增一行明细的时候，如果有认领状态claimStatus字段，默认是0  （0-未认领，1-部分认领，2-全部认领）
      }
      // 初始化数据
      this.columnData.forEach((item) => {
        // if (item.allowEditing) {
        if (item.editType) {
          if (item.editType.includes('dropdown')) {
            row[item.field] = ''
            // row[item.field] =
            //   item.selectData && item.selectData.length > 0
            //     ? item.selectData[0].value
            //     : "";
          } else if (item.editType.includes('date')) {
            // row[item.field] = new Date();
          } else if (item.editType.includes('numer')) {
            row[item.field] = 0
          } else if (item.editType.includes('boolean')) {
            row[item.field] = true
          } else {
            row[item.field] = ''
          }
        } else {
          row[item.field] = ''
        }
        // }
      })
      return row
    },

    // 文件上传成功
    handleUploadSuccess(params) {
      console.log('handleUploadSuccess', JSON.parse(params))
      var grid = new getComponent('Grid1', 'grid')
      console.log('grid', grid)
      let { rowIndex } = grid.editModule.editModule.args // Normal模式下
      // let rowIndex = grid.selectedRowIndex; // Batch模式下
      console.log(this.$t('上传完文件'), rowIndex, JSON.parse(params)?.rowIndex) // Batch模式下，一旦有新增行未保存的，params中的index就不准了

      grid.updateCell(rowIndex, 'file', JSON.parse(params)?.fileInfo)
    },
    handleRemoveFile(params) {
      console.log('handleRemoveFile', JSON.parse(params))
    },

    getData() {
      console.log('getData - dataSource：', this.$refs.dataGrid.ejsRef.dataSource)
      console.log(
        'getData - getDataModule：',
        this.$refs.dataGrid.ejsRef.getDataModule()?.dataManager?.dataSource?.json
      )
      // Batch模式下才有
      console.log('getData-getBatchChanges：', this.$refs.dataGrid.ejsRef.getBatchChanges())
    }
  }
}
</script>
