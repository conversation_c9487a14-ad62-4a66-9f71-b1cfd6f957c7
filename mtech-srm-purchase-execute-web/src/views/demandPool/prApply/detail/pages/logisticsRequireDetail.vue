<template>
  <div :class="['pt20', 'grid-wrap', $route.query.type == 'view' && 'grid-wrap-page']">
    <sc-table
      ref="sctableRef"
      grid-id="a0998849-8f98-444e-9063-48d594f99a8d"
      :loading="loading"
      :is-show-refresh-bth="false"
      :columns="columns"
      :table-data="tableData"
      keep-source
    >
      <template #cellFieldVoList="{ row }">
        <span style="color: #2783fe; cursor: pointer" @click="showFiledVolist(row)">{{
          $t('柜量明细')
        }}</span>
      </template>
      <!-- 待确认 -->
      <template #cellFie>
        <span style="color: #2783fe">{{ $t('暂无附件') }}</span>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
    <mt-dialog
      ref="filedVolistRef"
      css-class="column-setting"
      :buttons="buttons"
      :header="$t('柜量明细')"
    >
      <mt-template-page ref="templateRef" class="template-height" :template-config="pageConfig">
      </mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
// 设置下拉的数据源
import ScTable from '@/components/ScTable/src/index'
import { cloneDeep } from 'lodash'
import { fixLeftColumns, fixRightColumns } from '../config/logisticsRequireDetail.js'
export default {
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    },
    userInfo: {
      type: Object,
      default: () => {}
    }
  },
  components: { ScTable },
  data() {
    return {
      loading: false,
      columns: [],
      dataSource: [],
      tableData: [],
      fieldDataSource: [],
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        totalRecordsCount: 0,
        pageSizes: [10, 20, 50, 100, 200]
      },
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.cancel,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [],
          grid: {
            columnData: [
              {
                width: '150',
                field: 'fieldCode',
                headerText: this.$t('月份')
              },
              {
                width: '150',
                field: 'fieldData',
                headerText: this.$t('柜量')
              }
            ],
            dataSource: [],
            allowPaging: false,
            asyncConfig: {}
          }
        }
      ],
      LogisticsMethodCodeMap: {
        1: 'itemSeaVo',
        3: 'itemRailwayVo'
      }
    }
  },
  // logisticsMethodCode
  // TRANS_SEA(1,"海运"),
  // TRANS_AIR(2,"空运"),
  // TRANS_RAILWAY(3,"铁运"),
  // LAND_RAILWAY(4,"陆运"),
  // TRANS_HIGHWAY(5,"公路"),
  computed: {
    logisticsMethodCode() {
      return this.detailInfo?.logisticsMethodCode ? Number(this.detailInfo?.logisticsMethodCode) : 0
    }
  },

  mounted() {
    this.getLogisticsFields()
  },

  methods: {
    // 获取字段明细
    getLogisticsFields() {
      this.$API.purchaseRequest
        .getLogisticsFields({ templateId: this.detailInfo?.templateId || '' })
        .then((res) => {
          this.columns = this.defineGridColumn(res.data)
          this.tableData = this.defineGridData(this.detailInfo?.itemVoList)
        })
    },
    // 设置列表字段
    defineGridColumn(data) {
      let _columns = data?.map((item) => {
        return {
          field: item?.fieldCode,
          title: item?.fieldName
        }
      })
      return [...fixLeftColumns, ..._columns, ...fixRightColumns]
    },
    // 设置列表数据
    defineGridData(data) {
      if (!data) return []
      console.log(this.logisticsMethodCode)
      console.log(this.LogisticsMethodCodeMap)
      const key = this.LogisticsMethodCodeMap[this.logisticsMethodCode]
      data.forEach((item) => {
        let logisticsDTO = item[key]
        for (let i in logisticsDTO) {
          item[i] = logisticsDTO[i]
        }
      })
      return data
    },
    // 显示柜量明细
    showFiledVolist(data) {
      this.$refs.filedVolistRef.ejsRef.show()
      this.$set(this.pageConfig[0].grid, 'dataSource', data?.fieldVoList || [])
    },
    cancel() {
      this.$refs.filedVolistRef.ejsRef.hide()
    },

    // 分页的两个方法
    handleCurrentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.getModuleData()
    },

    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.getModuleData()
    }
  }
}
</script>
<style lang="scss"></style>
<style lang="scss" scoped>
.full-height {
  background: #fff;
}
// .e-grid td.e-rowcell .e-frame {
//   position: relative;
//   right: 6px;
// }
/deep/ .pe-edit-grid .e-rowcell .e-css .e-icons {
  position: relative;
  right: 6px;
}
.e-css {
  display: none;
}
.toolbar-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;

  .one-bar {
    display: flex;
    align-items: center;
    margin-right: 20px;
    cursor: pointer;
    .mt-icons {
      font-size: 14px;
      color: #4f5b6d;
      margin-right: 5px;
    }
    span {
      word-break: keep-all;
      font-size: 14px;
      color: #4f5b6d;
      font-weight: normal;
    }
  }

  .flex1 {
    flex: 1;
  }
}
</style>
