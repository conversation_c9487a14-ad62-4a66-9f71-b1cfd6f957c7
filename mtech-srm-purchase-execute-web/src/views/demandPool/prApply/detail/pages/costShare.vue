<template>
  <div class="cost-share">
    <div class="tools">
      <!-- <div class="toolbar-box">
        <div class="one-bar" @click="handleClickToolBar('add')">
          <mt-icon name="icon_solid_Createorder"></mt-icon>
          <span>{{ $t("新增") }}</span>
        </div>
        <div class="one-bar" @click="handleClickToolBar('delete')">
          <mt-icon name="icon_solid_Delete1"></mt-icon>
          <span>{{ $t("删除") }}</span>
        </div>
      </div> -->
      <div class="switch-wrap">
        <span>{{ $t('整单分摊') }}</span>
        <mt-switch
          v-model="openSwitch"
          :disabled="disabledSwitch"
          @change="handleSwitch"
        ></mt-switch>
      </div>
    </div>

    <!-- <mt-data-grid
      id="Grid2"
      :data-source="dataSource"
      :column-data="columnData"
      ref="prCostShareRef"
      :allow-paging="false"
    ></mt-data-grid> -->

    <mt-template-page
      ref="costTemplate"
      :template-config="pageConfig"
      :toolbar-config="[]"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>

    <add-cost-share-dialog
      ref="addCostRef"
      v-if="showAddDialog"
      :dialog-data="dialogData"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></add-cost-share-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { maxPageSize } from '@/utils/constant'
import { defaultColumn, costShareColumn } from '../config/costShare.js'
export default {
  components: {
    addCostShareDialog: require('../components/addCostShareDialog.vue').default
  },
  props: {
    moduleKey: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      openSwitch: false, // 0-整单分摊，1-分摊明细（非新增时，switch处于禁用状态，不可修改）
      disabledSwitch: false, // 禁用整单分摊...除了新增，其他都禁用
      columnData: defaultColumn,

      pageConfig: [
        {
          toolbar: ['Add', 'Delete'],
          grid: {
            allowPaging: false,
            columnData: defaultColumn,
            dataSource: []
          }
        }
      ],
      dialogData: null,
      showAddDialog: false
    }
  },
  computed: {
    prCompanyInfo() {
      return this.$store.state.prCompanyInfo
    }
  },
  watch: {
    openSwitch(newVal) {
      this.$bus.$emit('openWhole', newVal)
    },
    prCompanyInfo: {
      handler() {
        this.$set(this.pageConfig[0].grid, 'dataSource', [])
      },
      immediate: false,
      deep: true
    }
  },
  mounted() {
    // 校验是否是查看
    if (this.$route.query.type == 'view') {
      this.editSettings = { allowEditing: false }
    }

    console.log(this.$t('我是整单分摊'), this.moduleKey)
    this.getModuleData()
  },
  methods: {
    // 设置列...新增时为true，非新增状态下，如果开启了整单分摊，那么也可以编辑（为true）。否则为false
    setColumn(flag) {
      console.log('进入到了setColumn', flag)
      // 构造列：传当前页面状态进去，因为查看类型不可编辑
      let columnData = defaultColumn.concat(costShareColumn({ type: this.$route.query.type, flag }))
      this.$set(this.pageConfig[0].grid, 'columnData', columnData)
    },

    // 非新增状态：获取费用分摊tab数据
    getModuleData() {
      if (!this.$route.query.id) {
        this.setColumn(true)
        return
      }
      let params = {
        docId: this.$route.query.id,
        moduleKey: this.moduleKey,
        requestParams: {
          defaultRules: [],
          page: {
            current: 1,
            size: maxPageSize
          }
        }
      }
      this.$API.purchaseRequest.getModuleData(params).then((res) => {
        let _records = res?.data?.costSharingDataList?.records || []
        this.openSwitch = _records.length > 0 ? true : false // 0-整单分摊，1-分摊明细
        if (this.$route.query.type === 'add') {
          // 草稿状态
          this.disabledSwitch = false
          this.setColumn(true)
          this.$set(this.pageConfig[0].grid, 'dataSource', _records)
        } else {
          this.disabledSwitch = true
          // 如果是整单分摊，就可以再去
          if (this.openSwitch) {
            this.setColumn(true)
            this.$set(this.pageConfig[0].grid, 'dataSource', _records)
          } else {
            this.setColumn(false)
            this.$set(this.pageConfig[0], 'toolbar', []) // 非新增时，启用了分摊明细，就不可再修改了
          }
        }
      })
    },

    // 开启分摊switch
    handleSwitch(e) {
      console.log(this.$t('切换整单分摊'), e)
      if (e) {
        this.$dialog({
          data: {
            title: this.$t('提醒'),
            message: this.$t('启用整单分摊后，将清空需求明细中维护的成本中心')
          },
          success: () => {},
          close: () => {
            console.log('close')
            this.openSwitch = false
          }
        })
      }
    },

    handleClickToolBar(e) {
      console.log('handleClickToolBar', e)
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        if (e.grid.getSelectedRecords().length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        } else {
          // 传index
          let indexs = e.grid.getSelectedRowIndexes()
          this.handleDelete(indexs)
        }
      }
    },

    handleClickCellTool(e) {
      // console.log("handleClickCellTool", e, cloneDeep(e.data));
      if (e.tool.id == 'edit') {
        this.handleEdit(e)
      } else if (e.tool.id == 'delete') {
        this.handleDelete([+e.componentData.index])
      }
    },

    handleAdd() {
      if (!this.openSwitch) {
        this.$toast({ content: this.$t('请先开启整单分摊'), type: 'warning' })
        return
      }

      this.dialogData = {
        dialogType: 'add',
        requestUrl: 'addCostShare'
      }
      this.handleAddDialogShow(true)
    },

    handleEdit(e) {
      this.dialogData = {
        dialogType: 'edit',
        requestUrl: 'editCostShare',
        row: cloneDeep(e.data),
        index: e.componentData.index
      }
      this.handleAddDialogShow(true)
      console.log('整单分摊 编辑', this.dialogData)
    },

    // 保存数据
    handleSave() {},

    handleDelete(indexs) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('确认删除该成本中心？')
        },
        success: () => {
          let dataSource = cloneDeep(this.pageConfig[0].grid.dataSource)
          let _dataSource = dataSource.filter((item, index) => !indexs.includes(index))
          // console.log("dataSource", _dataSource);
          this.$set(this.pageConfig[0].grid, 'dataSource', _dataSource)
        }
      })
    },

    confirmSuccess(rowInfo) {
      // console.log("this.dialogData", this.dialogData);
      let dataSource = cloneDeep(this.pageConfig[0].grid.dataSource)
      if (this.dialogData.dialogType == 'add') {
        dataSource.push({
          ...rowInfo
        })
      } else if (this.dialogData.dialogType == 'edit') {
        let index = +this.dialogData.index
        dataSource[index] = cloneDeep(rowInfo)
      }
      // console.log("dataSource", dataSource);
      this.$set(this.pageConfig[0].grid, 'dataSource', dataSource)
    },
    handleAddDialogShow(flag) {
      if (flag) {
        this.showAddDialog = flag
        this.$nextTick(() => {
          this.$refs.addCostRef.$refs.dialog.$refs.ejsRef.show()
          this.$refs.addCostRef.$refs.ruleForm.resetFields()
          if (this.dialogData.dialogType == 'add') {
            this.$refs.addCostRef.dialogTitle = this.$t('新增费用分摊')
          } else if (this.dialogData.dialogType == 'edit') {
            this.$refs.addCostRef.dialogTitle = this.$t('编辑费用分摊')
            this.$refs.addCostRef.addForm = this.dialogData.row
          }
        })
      } else {
        this.$refs.addCostRef.$refs.dialog.$refs.ejsRef.hide()
        this.showAddDialog = flag
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.cost-share {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;

  .tools {
    position: absolute;
    top: 14px;
    right: 20px;
    z-index: 10;
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
    align-items: center;
    background: #fff;
    // padding: 20px 20px 10px 0;

    .toolbar-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-left: 20px;

      .one-bar {
        display: flex;
        align-items: center;
        margin-right: 20px;
        cursor: pointer;
        .mt-icons {
          font-size: 14px;
          color: #4f5b6d;
          margin-right: 5px;
        }
        span {
          word-break: keep-all;
          font-size: 14px;
          color: #4f5b6d;
          font-weight: normal;
        }
      }

      .flex1 {
        flex: 1;
      }
    }
  }

  .switch-wrap {
    display: flex;
    justify-content: flex-end;
    align-items: center;

    span {
      margin-right: 14px;
    }
  }

  /deep/ .mt-data-grid {
    flex: 1;
    #Grid2 {
      height: 100%;

      > .e-gridcontent {
        height: 80%;
      }
    }
  }
}
</style>
