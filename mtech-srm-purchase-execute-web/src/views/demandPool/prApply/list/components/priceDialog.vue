<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="modalData.title"
  >
    <mt-data-grid
      class="price-dialog"
      :data-source="sampleData"
      :column-data="sampleColumns"
      ref="dataGrid"
      locale="zh"
      @handleViewStage="handleViewStage"
    ></mt-data-grid>

    <!-- <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page> -->
  </mt-dialog>
</template>

<script>
import { priceColumn } from '../config'
export default {
  data() {
    return {
      sampleColumns: priceColumn,
      buttons: [
        // {
        //   click: this.cancel,
        //   buttonModel: { content: this.$t("取消") },
        // },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      sampleData: [],
      pageConfig: [
        // 废弃
        {
          toolbar: [],
          grid: {
            columnData: priceColumn,
            dataSource: [],
            allowPaging: false, //不使用翻页
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/requestItem/price/record',
              defaultRules: [
                {
                  label: 'skuId',
                  field: 'skuId',
                  type: 'string',
                  operator: 'equal',
                  value: '0'
                },
                {
                  label: 'categoryId',
                  field: 'categoryId',
                  type: 'string',
                  operator: 'equal',
                  value: '3'
                },
                {
                  label: 'siteId',
                  field: 'siteId',
                  type: 'string',
                  operator: 'equal',
                  value: '1415883217227599874'
                },
                {
                  label: 'itemId',
                  field: 'itemId',
                  type: 'string',
                  operator: 'equal',
                  value: '333'
                }
              ]
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  mounted() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
    this.$refs['dialog'].ejsRef.show()
    this.getPrice()
  },
  methods: {
    getPrice() {
      this.$API.purchaseRequest
        .getPriceRecord({
          skuId: this.modalData.row.skuId,
          categoryId: this.modalData.row.categoryId,
          siteId: this.modalData.row.siteId,
          itemId: this.modalData.row.itemId,
          tenantId: this.userInfo?.tenantId
        })
        .then((res) => {
          console.log(res)
          this.sampleData = res.data
        })
    },

    handleViewStage(stageList) {
      this.$dialog({
        modal: () => import('./priceStage.vue'),
        data: {
          title: this.$t('阶梯价格'),
          stageList: stageList
        },
        success: () => {
          // this.confirmSuccess();
        }
      })
    },

    handleClickCellTitle() {
      console.log('点击了啥')
    },
    confirm() {
      this.$emit('confirm-function')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
