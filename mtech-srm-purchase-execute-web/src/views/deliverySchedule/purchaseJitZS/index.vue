<!-- 叫料计划-中山空调 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <template v-for="item in searchConfigs">
          <mt-form-item
            v-if="item.title && item.isQuery !== true"
            :key="item.field"
            :prop="item.field"
            :label="item.title"
            label-style="top"
          >
            <mt-select
              v-if="item.searchOptions && item.searchOptions.elementType === 'select'"
              style="flex: 1"
              v-model="item.searchOptions.value"
              :fields="item.searchOptions.fields"
              :allow-filtering="true"
              filter-type="Contains"
              :data-source="item.searchOptions.dataSource"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
            ></mt-select>
            <mt-multi-select
              v-else-if="item.searchOptions && item.searchOptions.elementType === 'multi-select'"
              style="flex: 1"
              v-model="item.searchOptions.value"
              :fields="item.searchOptions.fields"
              :show-select-all="item.searchOptions.showSelectAll"
              :allow-filtering="true"
              filter-type="Contains"
              :data-source="item.searchOptions.dataSource"
              :placeholder="$t('请选择')"
            ></mt-multi-select>
            <RemoteAutocomplete
              v-else-if="
                item.searchOptions && item.searchOptions.elementType === 'remote-autocomplete'
              "
              v-model="item.searchOptions.value"
              :url="item.searchOptions.url"
              :placeholder="$t('请选择')"
              :fields="item.searchOptions.fields"
              :multiple="item.searchOptions.multiple"
              :params="item.searchOptions.params"
              :params-key="item.searchOptions.paramsKey"
              :search-fields="item.searchOptions.searchFields"
              :records-position="item.searchOptions.recordsPosition"
            ></RemoteAutocomplete>
            <mt-date-range-picker
              v-else-if="item.searchOptions && item.searchOptions.elementType === 'date-range'"
              :open-on-focus="true"
              :show-clear-button="item.searchOptions.clearButton"
              :allow-edit="false"
              v-model="item.searchOptions.value"
              :placeholder="item.searchOptions.placeholder"
            ></mt-date-range-picker>
            <div
              v-else-if="item.searchOptions && item.searchOptions.elementType === 'number'"
              style="display: flex"
            >
              <div class="operator-list">
                <mt-select
                  v-model="item.searchOptions.operator"
                  :data-source="item.searchOptions.dataSource"
                  popup-width="50px"
                ></mt-select>
              </div>
              <div class="custom-input-number">
                <mt-input-number
                  v-model="item.searchOptions.value"
                  type="number"
                  :show-spin-button="false"
                  :show-clear-button="item.searchOptions.clearButton"
                  :placeholder="item.searchOptions.placeholder"
                />
              </div>
            </div>
            <mt-input
              v-else
              v-model="item.searchOptions.value"
              :placeholder="item.searchOptions.placeholder || $t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
        </template>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="50951338-5456-4455-ad4c-6ca2187e1de3"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :sortable="true"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />

    <BatchUpdateDialog ref="batchUpdateDialogRef" @confirm="batchUpdateConfirm" />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, statusOptions, formatColumn, formatParams } from './config'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  components: {
    CollapseSearch,
    ScTable,
    BatchUpdateDialog: () => import('./components/BatchUpdateDialog')
  },
  data() {
    return {
      original: 0,
      searchConfigs: formatColumn(columnData),
      searchFormModel: {},
      toolbar: [
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
        { code: 'batchUpdate', name: this.$t('批量修改交货日期'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
        { code: 'sync', name: this.$t('数据同步'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],
      statusOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleReset() {
      this.searchConfigs.forEach((i) => {
        i.searchOptions = {
          ...i.searchOptions,
          value: null
        }
      })
      this.handleSearch()
    },
    handleSearch() {
      this.original = 0
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.original = 0
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.original = 0
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      let original = this.original
      let params = this.getParams()
      this.loading = true
      const res = await this.$API.deliverySchedule
        .pageKtMaterialControlApi(params, original)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    getParams() {
      let params = {}
      const rules = formatParams(this.searchConfigs)
      params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        condition: 'and',
        rules
      }
      return params
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete', 'batchUpdate']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'delete':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除？')
            },
            success: () => {
              this.handleDelete(selectedRecords)
            }
          })
          break
        case 'batchUpdate':
          this.handleBatchUpdate(selectedRecords)
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        case 'sync':
          this.handleSync()
          break
        default:
          break
      }
    },
    handleSync() {
      this.original = 1
      this.currentPage = 1
      this.getTableData()
    },
    handleDelete(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.deliverySchedule.deleteKtMaterialControlApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.original = 0
          this.currentPage = 1
          this.getTableData()
        }
      })
    },
    handleBatchUpdate(selectedRecords) {
      const hasDiffElements = (arr, prop) => {
        for (var i = 0; i < arr.length - 1; i++) {
          for (var j = i + 1; j < arr.length; j++) {
            if (arr[i][prop] !== arr[j][prop]) {
              return true
            }
          }
        }
        return false
      }
      let canUpdate = selectedRecords.every(
        (item) => item.status === 1 || item.status === 3 || item.status === 4 || item.status === 5
      )
      let isNotSameStatus = hasDiffElements(selectedRecords, 'isJit')
      if (!canUpdate) {
        this.$toast({
          content: this.$t(
            '所选叫料计划中有【状态】为非‘新增’、‘待反馈’、‘反馈正常’、‘反馈异常’的叫料计划，不支持批量修改'
          ),
          type: 'warning'
        })
        return
      }
      if (isNotSameStatus) {
        this.$toast({
          content: this.$t('同一次处理，请选择【是否JIT】全部为‘是’或都全部为‘否’的叫料计划处理'),
          type: 'warning'
        })
        return
      }
      this.$refs.batchUpdateDialogRef.dialogInit({
        title: this.$t('批量修改交货日期'),
        selectedRecords
      })
    },
    batchUpdateConfirm(form) {
      this.$API.deliverySchedule.batchUpdateKtMaterialControlApi(form).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: res.data, type: 'success' })
          this.original = 0
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: res.msg, type: 'error' })
        }
      })
    },
    handleExport(e) {
      let params = this.getParams()
      this.$store.commit('startLoading')
      this.$API.deliverySchedule
        .exportKtMaterialControlApi(params)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .operator-list {
  width: 50px !important;
}
::v-deep .custom-input-number {
  width: 100%;
  .mt-input-number {
    width: 100%;
    margin-bottom: 0px;
    input {
      height: 23px;
      padding-right: 30px;
    }
  }
}
</style>
