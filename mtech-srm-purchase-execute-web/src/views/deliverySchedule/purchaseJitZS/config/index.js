import { i18n } from '@/main.js'
import dayjs from 'dayjs'
import { MasterDataSelect } from '@/utils/constant'

export const statusOptions = [
  { text: i18n.t('新建'), value: 1 },
  { text: i18n.t('已转交'), value: 2 },
  { text: i18n.t('待反馈'), value: 3 },
  { text: i18n.t('反馈异常'), value: 4 },
  { text: i18n.t('反馈正常'), value: 5 },
  { text: i18n.t('已关闭'), value: 6 }
]

export const isNotOptions = [
  { text: i18n.t('是'), value: 1 },
  { text: i18n.t('否'), value: 0 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center',
    visible: false
  },
  {
    field: 'workOrder',
    title: i18n.t('生产工单'),
    searchOptions: {
      placeholder: i18n.t('多个订单号需要用空格隔开'),
      operator: 'likeright',
      maxQueryValueLength: 100000
    }
  },
  {
    field: 'rowCode',
    title: i18n.t('JIT 行编号'),
    minWidth: 140,
    searchOptions: {
      placeholder: i18n.t('多个订单号需要用空格隔开'),
      operator: 'likeright',
      maxQueryValueLength: 100000
    }
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    },
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in',
      dataSource: statusOptions
    }
  },
  {
    field: 'productLine',
    title: i18n.t('生产线')
  },
  {
    field: 'deliveryDate',
    title: i18n.t('交货日期'),
    minWidth: 140,
    formatter: ({ cellValue }) => {
      let text = ''
      if (cellValue && cellValue !== '0') {
        text = dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
      }
      return text
    },
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    field: 'isJit',
    title: i18n.t('是否JIT'),
    formatter: ({ cellValue }) => {
      let item = isNotOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    },
    searchOptions: {
      elementType: 'select',
      operator: 'equal',
      dataSource: isNotOptions
    },
    isQuery: true
  },
  {
    field: 'isItemBatch',
    title: i18n.t('是否批次来料'),
    minWidth: 120,
    formatter: ({ cellValue }) => {
      let item = isNotOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    },
    searchOptions: {
      elementType: 'select',
      operator: 'equal',
      dataSource: isNotOptions
    },
    isQuery: true
  },
  {
    field: 'materialFlag',
    title: i18n.t('是否散件工厂'),
    minWidth: 120,
    formatter: ({ cellValue }) => {
      let item = isNotOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    },
    searchOptions: {
      elementType: 'select',
      operator: 'equal',
      dataSource: isNotOptions
    },
    isQuery: true
  },
  {
    field: 'uniqueCode',
    title: i18n.t('大数据平台唯一编号'),
    minWidth: 140,
    isQuery: true
  },
  {
    field: 'versionNo',
    title: i18n.t('版本号'),
    isQuery: true
  },
  {
    field: 'remarkExplain',
    title: i18n.t('大数据平台备注'),
    minWidth: 120,
    isQuery: true
  },
  {
    field: 'origSupplierCode',
    title: i18n.t('上版供应商编号'),
    minWidth: 120,
    isQuery: true
  },
  {
    field: 'origSupplierName',
    title: i18n.t('上版供应商名称'),
    minWidth: 120,
    isQuery: true
  },
  {
    field: 'planDeliveryDate',
    title: i18n.t('上版交货日期'),
    searchOptions: { ...MasterDataSelect.timeRange },
    isQuery: true
  },
  {
    field: 'planDeliveryTime',
    title: i18n.t('上版交货时间'),
    searchOptions: { ...MasterDataSelect.timeRange },
    isQuery: true
  },
  {
    field: 'prodtScheduleTime',
    title: i18n.t('计划开工时间'),
    minWidth: 140,
    formatter: ({ cellValue }) => {
      let text = ''
      if (cellValue && cellValue !== '0') {
        text = dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
      }
      return text
    },
    searchOptions: { ...MasterDataSelect.timeRange },
    isQuery: true
  },
  {
    field: 'callMaterialQty',
    title: i18n.t('上版叫料数量'),
    isQuery: true
  },
  {
    field: 'supRemark',
    title: i18n.t('供应商备注'),
    isQuery: true
  },
  {
    field: 'planGroupRemark',
    title: i18n.t('计划组备注'),
    isQuery: true
  },
  {
    field: 'dispatcherRemark',
    title: i18n.t('叫料员备注'),
    isQuery: true
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.siteName : ''
    },
    searchOptions: {
      ...MasterDataSelect.factoryAddress
    }
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称'),
    isQuery: true
  },
  {
    field: 'packingSchemeCode',
    title: i18n.t('装箱方案编码'),
    searchOptions: {
      placeholder: '支持粘贴多个精准及单个右模糊查询',
      operator: 'likeright',
      maxQueryValueLength: 100000
    },
    isQuery: true
  },
  {
    field: 'packingSchemeDesc',
    title: i18n.t('装箱方案描述'),
    isQuery: true
  },
  {
    field: 'demandCode',
    title: i18n.t('净需求编号'),
    isQuery: true
  },
  {
    field: 'warehouseCode',
    title: i18n.t('库存地点'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.warehouseName : ''
    },
    searchOptions: {
      ...MasterDataSelect.stockAddress,
      renameField: 'warehouseName',
      fields: { text: 'locationCode', value: 'locationName' }
    }
  },
  {
    field: 'buyerOrgCode',
    title: i18n.t('采购组'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.buyerOrgName : ''
    },
    searchOptions: {
      ...MasterDataSelect.businessGroupIn
    }
  },
  {
    field: 'subSiteCode',
    title: i18n.t('分厂'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.subSiteName : ''
    },
    searchOptions: {
      ...MasterDataSelect.subSiteCodeBuyer,
      url: '/srm-purchase-execute/tenant/buyerJitInfo/condition?conditionType=subSiteCode'
    }
  },
  {
    field: 'subSiteAddressCode',
    title: i18n.t('分厂库存地点'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.subSiteAddressName : ''
    },
    searchOptions: {
      ...MasterDataSelect.subSiteAddressBuyer,
      url: '/srm-purchase-execute/tenant/buyerJitInfo/condition?conditionType=subSiteAddressCode'
    },
    isQuery: true
  },
  {
    field: 'senderAddress',
    title: i18n.t('收货信息'),
    isQuery: true
  },
  {
    field: 'companyCode',
    title: i18n.t('公司'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.companyName : ''
    },
    searchOptions: {
      ...MasterDataSelect.businessCompany
    },
    isQuery: true
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    },
    searchOptions: {
      ...MasterDataSelect.supplierNoScope
    }
  },
  {
    field: 'vmiWarehouseType',
    title: i18n.t('VMI仓类型'),
    isQuery: true
  },
  {
    field: 'projectTextBatch',
    title: i18n.t('项目文本批次'),
    isQuery: true
  },
  {
    field: 'bidNum',
    title: i18n.t('需求数量'),
    searchOptions: { elementType: 'number' },
    isQuery: true
  },
  {
    field: 'receiveNum',
    title: i18n.t('收货数量'),
    searchOptions: { elementType: 'number' },
    isQuery: true
  },
  {
    field: 'deliveryNum',
    title: i18n.t('在途数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    field: 'remainingDeliveryNum',
    title: i18n.t('剩余送货数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    field: 'scheduleUserCode',
    title: i18n.t('叫料员')
  },
  {
    field: 'storemanName',
    title: i18n.t('仓管员'),
    isQuery: true
  },
  {
    field: 'batchCode',
    title: i18n.t('版次号'),
    isQuery: true
  },
  {
    field: 'renewTime',
    title: i18n.t('JIT更新时间'),
    minWidth: 140,
    formatter: ({ cellValue }) => {
      let text = ''
      if (cellValue && cellValue !== '0') {
        text = dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
      }
      return text
    },
    searchOptions: { ...MasterDataSelect.timeRange },
    isQuery: true
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    isQuery: true
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'closePersonName',
    title: i18n.t('关闭人'),
    isQuery: true
  },
  {
    field: 'closeTime',
    title: i18n.t('关闭时间'),
    minWidth: 140,
    formatter: ({ cellValue }) => {
      let text = ''
      if (cellValue && cellValue !== '0') {
        text = dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
      }
      return text
    },
    searchOptions: { ...MasterDataSelect.timeRange },
    isQuery: true
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 140,
    searchOptions: { ...MasterDataSelect.timeRange },
    isQuery: true
  },
  {
    field: 'transferPlanCode',
    title: i18n.t('转交计划员'),
    searchOptions: {
      ...MasterDataSelect.staff
    },
    isQuery: true
  },
  {
    field: 'errorInfo',
    title: i18n.t('自动叫料失败原因'),
    minWidth: 140,
    isQuery: true
  }
]

export const formatColumn = (column) => {
  const columnData = [
    {
      type: 'checkbox',
      width: 50,
      isQuery: true,
      fixed: 'left'
    }
  ]
  if (!column) {
    return columnData
  }
  column.forEach((i) => {
    if (i.visible !== false && i.title) {
      // 将动态列配置与动态查询条件数据关联配置组装到一起
      const columnItem = {
        minWidth: i.width,
        title: i.title,
        label: i.title,
        field: i.field,
        isQuery: i.isQuery,
        showOverflow: true,
        searchOptions: {
          value: null,
          type: 'string',
          operator: 'contains'
        }
      }
      if (i.searchOptions) {
        columnItem.searchOptions = {
          ...columnItem.searchOptions,
          ...i.searchOptions
        }
        if (i.searchOptions?.elementType === 'number') {
          columnItem.searchOptions.operator = 'equal'
          columnItem.searchOptions.dataSource = [
            { text: '=', value: 'equal' },
            { text: '≠', value: 'notequal' },
            { text: '>', value: 'greaterthan' },
            { text: '≥', value: 'greaterthanorequal' },
            { text: '<', value: 'lessthan' },
            { text: '≤', value: 'lessthanorequal' }
          ]
        }
      }
      if (i.valueConverter) {
        const dataSource = []
        for (const key in i.valueConverter.map) {
          dataSource.push({
            text: i.valueConverter.map[key],
            value: Number(key)
          })
        }
        columnItem.searchOptions = {
          ...columnItem.searchOptions,
          dataSource
        }
      }
      if (i.cellTools) {
        columnItem.cellTools = i.cellTools
        columnItem.slots = {
          // 使用插槽模板渲染
          default: i.field + 'Default'
        }
      }
      if (!columnData.some((i) => i.title === columnItem.title)) {
        columnData.push(columnItem)
      }
    }
  })
  return columnData
}

export const formatParams = (searchConfigs) => {
  const searchRules = []
  searchConfigs.forEach((item) => {
    if (
      ((item?.searchOptions?.value && item?.searchOptions?.value?.length) ||
        item?.searchOptions?.value === 0 ||
        (!isNaN(item?.searchOptions?.value) && item?.searchOptions?.value !== null)) &&
      item?.searchOptions?.value !== ''
    ) {
      let value = item?.searchOptions?.value
      // if (item?.searchOptions?.elementType === 'date-range') {
      // value = [
      //   Number(new Date(value[0].toString())),
      //   Number(new Date(value[1].toString())) + Number(86400000 - 1440000)
      // ]
      // }
      if (item?.searchOptions?.serializeValue) {
        value = item?.searchOptions?.serializeValue(value)
      }
      if (Array.isArray(value) && value.length === 0) {
        return false
      }
      let option = {
        label: item.label,
        field: item?.searchOptions?.renameField ? item?.searchOptions?.renameField : item.field,
        type: item?.searchOptions?.type,
        operator: item?.searchOptions?.operator,
        value
      }
      if (item?.searchOptions && item?.searchOptions?.customRule) {
        option = item?.searchOptions?.customRule(value)
      }
      searchRules.push(option)
    }
  })
  return searchRules
}
