<template>
  <div class="selfType">
    <div v-for="(item, index) in list" :key="item">
      <span v-if="index == 2" style="color: red">*</span>
      {{ item }}
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      list: [
        this.$t('D（原始需求）'),
        this.$t('P（需求量）'),
        this.$t('C（承诺量）'),
        this.$t('GAP（差异）'),
        this.$t('累计差额')
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.selfType {
  > div {
    height: 36px;
    line-height: 36px;
    box-sizing: border-box;
    // padding: 12px 0;
  }
}
</style>
