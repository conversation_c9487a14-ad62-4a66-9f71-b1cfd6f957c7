<template>
  <div class="full-height vertical-flex-box">
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <mt-template-page
        ref="templateRef1"
        :template-config="componentConfig1"
        @handleClickToolBar="handleClickToolBar1"
        @handleClickCellTool="handleClickCellTool1"
        @actionBegin="actionBegin1"
        @actionComplete="actionComplete1"
        @selectedChanged="selectedChanged1"
      ></mt-template-page>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>
<script>
import { checkColumn1, lastColumn1 } from '../config/index1'
import { cloneDeep } from 'lodash'
import InputNumber from '../components1/InputNumber.vue'
import InputNumberView from '../components1/InputNumberView.vue'
import * as UTILS from '@/utils/utils'
var bigDecimal = require('js-big-decimal')
export default {
  data() {
    return {
      lastColumn1: lastColumn1,
      componentConfig1: [],
      currentList: [], //从接口获取到的最新的数据
      nowEditRowFlag: '', //当前编辑的行id
      addId: '1',
      isEdit: '1', //是否编辑 1是编辑 2不是编辑
      selectedOtherInfo: {},

      apiWaitingQuantity: 0, // 调用的api正在等待数
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 10, // 当前每页数据量
        totalPages: 0, // 总页数
        pageSizes: [10, 50, 100, 200]
      },
      forecastPageCurrent: 1, // 预测表格 当前页码
      forecastRules: [], // 预测表格请求规则
      tableContainerClientHeight: 0,
      titleList: []
    }
  },
  mounted() {
    this.buyerGoodsDemandPlanInfoQuery()
    this.handleColumns([])
  },
  methods: {
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.buyerGoodsDemandPlanInfoQuery()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.buyerGoodsDemandPlanInfoQuery()
    },
    // 采方-获取采方预测信息列表
    buyerGoodsDemandPlanInfoQuery() {
      this.componentConfig1 = []
      const params = {
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        },
        rules: [...this.forecastRules]
      }
      // this.apiStartLoading();
      this.$API.deliverySchedule
        .supplierGoodsDemandPlanInfoQuery(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.goodsDemandPlanInfoRespIPage?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            const records = res?.data?.goodsDemandPlanInfoRespIPage?.records || [] // 表格数据
            let titleList = res?.data?.titleList || [] // 动态表头数据
            // 处理表头数据
            let columnData = this.handleColumns(titleList)
            // 处理表数据
            let dataSource = this.handleDataSource(records, titleList)
            // this.$refs.templateRef1.getCurrentUsefulRef().gridRef.refresh(); // 更新表格数据
            let config = {
              useToolTemplate: false, // 不使用预置(新增、编辑、删除)
              useBaseConfig: false, // 使用组件中的toolbar配置
              toolbar: [
                [
                  {
                    id: 'Submit',
                    icon: 'icon_solid_Import',
                    title: this.$t('提交'),
                    permission: ['O_02_0677']
                  },
                  {
                    id: 'Export1',
                    icon: 'icon_solid_Import',
                    title: this.$t('导出'),
                    permission: ['O_02_0678']
                  }
                ],
                ['Filter', 'Refresh']
              ],
              gridId: this.$tableUUID.purchaseSchedule.purchaseScheduleSupplierDayTab,
              grid: {
                allowPaging: false, // 不分页
                allowReordering: false,
                dataSource: dataSource,
                allowEditing: true, //开启表格编辑操作
                editSettings: {
                  allowEditing: true,
                  allowAdding: true,
                  allowDeleting: true,
                  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
                  showConfirmDialog: false,
                  showDeleteConfirmDialog: false,
                  newRowPosition: 'Top'
                },
                columnData: columnData
              }
            }
            this.componentConfig1.push(config)
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleDataSource(records, titleList) {
      let list1 = []
      titleList.forEach((item) => {
        if (item && item.length === 13) {
          list1.push(UTILS.default.dateFormat(Number(item), 'Y-m-d'))
        }
      })
      this.titleList = list1
      records.forEach((obj) => {
        obj.addId = this.addId++
        obj.isEntry = '1' //是否是带入的数据
        let afterData = []
        let tempArr = []
        obj.limitNum = ''
        obj.remainingNum = ''
        obj.outstandingNum = ''
        if (obj.item.length > 0) {
          obj.buyerRemark = obj.item[0].buyerRemark
          obj.released = obj.item[0].released
          obj.supplierRemark = obj.item[0].supplierRemark
          obj.item.forEach((item1) => {
            obj.limitNum = bigDecimal.add(obj.limitNum, item1.limitNum)
            obj.remainingNum = bigDecimal.add(obj.remainingNum, item1.remainingNum)
            obj.outstandingNum = bigDecimal.add(obj.outstandingNum, item1.outstandingNum)
          })
        }
        obj.item.forEach((item1) => {
          if (item1.timeInfoTimestamp && item1.timeInfoTimestamp.length === 13) {
            item1.timeInfoTimestamp = UTILS.default.dateFormat(
              Number(item1.timeInfoTimestamp),
              'Y-m-d'
            )
          }
          console.log(item1.timeInfoTimestamp, 'a哈哈多喝水2')
          if (tempArr.indexOf(item1.timeInfoTimestamp) === -1) {
            tempArr.push(item1.timeInfoTimestamp)
            afterData.push({
              timeInfoTimestamp: item1.timeInfoTimestamp,
              origin: [item1]
            })
          } else {
            afterData.some((item2) => {
              if (item2.timeInfoTimestamp === item1.timeInfoTimestamp) {
                item2.origin.push(item1)
              }
            })
          }
        })
        afterData.forEach((item1) => {
          if (item1.origin.length === 1) {
            obj[
              item1.timeInfoTimestamp
            ] = `${item1.origin[0].total}_${item1.origin[0].buyerNum}_${item1.origin[0].supplierNum}_${item1.origin[0].gapNum}_${item1.origin[0].countGapNum}`
          }
          if (item1.origin.length > 1) {
            let total = ''
            let buyerNum = ''
            let supplierNum = ''
            let gapNum = ''
            let countGapNum = ''
            item1.origin.forEach((item2) => {
              total = bigDecimal.add(item2.total, total)
              buyerNum = bigDecimal.add(item2.buyerNum, buyerNum)
              supplierNum = bigDecimal.add(item2.supplierNum, supplierNum)
              gapNum = bigDecimal.add(item2.gapNum, gapNum)
              countGapNum = bigDecimal.add(item2.countGapNum, countGapNum)
            })
            obj[
              item1.timeInfoTimestamp
            ] = `${total}_${buyerNum}_${supplierNum}_${gapNum}_${countGapNum}`
          }
        })
        this.currentList = records
      })
      // this.componentConfig1[0].grid.dataSource = records;
      return records
    },
    handleColumns(titleList) {
      let lastColumn2 = []
      titleList.forEach((item) => {
        if (item && item.length === 13) {
          item = UTILS.default.dateFormat(Number(item), 'Y-m-d')
        }
        lastColumn2.push({
          field: item,
          headerText: item,
          ignore: true,
          allowFiltering: false,
          width: '150',
          allowEditing: false,
          template: () => {
            return { template: InputNumberView }
          },
          editTemplate: () => {
            return { template: InputNumber }
          }
        })
      })
      let columnData = checkColumn1.concat(lastColumn1).concat(lastColumn2)
      return columnData
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    },
    handleExport() {
      //导出
      let obj = JSON.parse(
        sessionStorage.getItem('a20dd9f6-b694-8d98-9465-ececd80e4022')
      ).visibleCols
      let field = []
      if (obj !== undefined) {
        obj.forEach((item) => {
          field.push(item.field)
        })
      } else {
        lastColumn1.forEach((item) => {
          field.push(item.field)
        })
      }
      console.log(field)
      let params = {
        condition: this.forecastCondition,
        page: { current: 1, size: 1000 },
        pageFlag: true,
        rules: [...this.forecastRules]
      }
      this.$store.commit('startLoading')
      this.$API.deliverySchedule.supplierGoodsDemandPlanInfoExport(params, field).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    generateUUID() {
      var d = new Date().getTime()
      if (window.performance && typeof window.performance.now === 'function') {
        d += performance.now() //use high-precision timer if available
      }
      var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (d + Math.random() * 16) % 16 | 0
        d = Math.floor(d / 16)
        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16)
      })
      return uuid
    },
    updateList() {
      this.$refs.templateRef1.refreshCurrentGridData()
    },
    selectedChanged1(val) {
      Object.assign(this.selectedOtherInfo, val.itemInfo)
      console.log(this.selectedOtherInfo, '最新的额外数据')
    },
    actionComplete1(args) {
      console.log(args, '我是actionComplete')
      if (args.requestType === 'save' && args.action === 'edit') {
        //编辑完成
        // let row = this.getRow();
        // this.editRow(row);
      }
    },
    endEdit() {
      //让组件失去焦点
      this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
    },
    editRow(rows) {
      console.log(rows, '编辑行数据')
      let params = []
      rows.forEach((obj) => {
        obj.item.forEach((obj1) => {
          params.push({
            id: obj.id,
            goodsDemandPlanItemId: obj1.id,
            feedbackQuantity: obj[obj1.timeInfoTimestamp]?.split('_')[2] || '',
            supplierRemark: obj.supplierRemark
          })
        })
      })
      let flag = this.judgeValid(params)
      if (!flag) return
      this.$API.deliverySchedule.supplierGoodsDemandPlanInfoFeedback(params).then(() => {
        this.$toast({
          content: this.$t('反馈交货排期操作成功'),
          type: 'success'
        })
        this.updateList()
      })
    },
    judgeValid(params) {
      let flag = true
      let flag1 = true
      params.forEach((item) => {
        if (item.feedbackQuantity === '') {
          flag1 = false
        }
      })
      if (!flag1) {
        this.$toast({
          content: this.$t('c必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      return flag
    },
    getRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.templateRef1
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = cloneDeep(this.selectedOtherInfo)
      let info = {}
      currentRecords.some((item) => {
        if (item.addId == this.nowEditRowFlag) {
          Object.assign(item, row)
          info = item
        }
      })
      return info
    },
    actionBegin1(args) {
      console.log(args, '我是actionBegin')
      if (args.requestType == 'beginEdit') {
        this.nowEditRowFlag = args.rowData.addId
        if (!(args.rowData.status == 2)) {
          this.$toast({
            content: this.$t('此状态不可编辑'),
            type: 'warning'
          })
          args.cancel = true
        }
      }
    },
    submit(selectRecords) {
      console.log(selectRecords, '我是选择的行')
      let flag = true
      selectRecords.forEach((item) => {
        console.log(item, '值')
        if (item.status != 2) {
          flag = false
        }
      })
      if (!flag) {
        this.$toast({
          content: this.$t('请选择待反馈的行操作'),
          type: 'warning'
        })
        return
      }
      this.editRow(selectRecords)
    },
    //点击表格的操作按钮
    handleClickCellTool1(e) {
      console.log('方法2', e)
      if (e.tool.id === 'Publish') {
        this.handlePublish([e.data])
      }
      if (e.tool.id === 'Cancle') {
        this.handleCancle([e.data])
      }
      if (e.tool.id === 'Close') {
        this.handleClose([e.data])
      }
      if (e.tool.id === 'Confirm') {
        this.handleConfirm([e.data])
      }
    },
    //点击顶部的操作按钮
    handleClickToolBar1(e) {
      console.log('方法1', e)
      console.log(e.grid.getSelectedRecords(), e.toolbar.id)
      const commonToolbar = [
        'ForecastUpdate',
        'ForecastAdd',
        'ForecastImport',
        'Filter',
        'Refresh',
        'refreshDataByLocal',
        'filterDataByLocal',
        'resetDataByLocal',
        'ForecastExport',
        'Setting'
      ]
      if (e.toolbar.id === 'Add') {
        this.handleAdd()
        return
      }
      if (e.toolbar.id === 'Import') {
        this.handleImport()
        return
      }
      if (e.toolbar.id === 'Export1') {
        this.handleExport()
        return
      }
      if (e.toolbar.id === 'refreshDataByLocal') {
        this.buyerGoodsDemandPlanInfoQuery()
        return
      }
      if (e.toolbar.id === 'filterDataByLocal') {
        // 筛选-过滤
        const { condition, rules: ruleList } = e.rules
        this.forecastCondition = condition
        this.forecastRules = ruleList
        // 采方-获取采方预测信息列表
        this.buyerGoodsDemandPlanInfoQuery()
        return
      }
      if (e.toolbar.id === 'resetDataByLocal') {
        // 筛选重置
        this.forecastCondition = 'and'
        this.forecastRules = []
        // 采方-获取采方预测信息列表
        this.buyerGoodsDemandPlanInfoQuery()
        return
      }
      let selectRecords = e.grid.getSelectedRecords()
      if (!selectRecords.length && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请至少选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Submit') {
        this.submit(selectRecords)
      }
    }
  }
}
</script>
