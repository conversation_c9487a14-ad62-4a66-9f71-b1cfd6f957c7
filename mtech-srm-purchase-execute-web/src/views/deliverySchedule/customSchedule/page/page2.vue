<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      @actionBegin="actionBegin"
      @handleClickToolBar="handleClickToolBar"
      @actionComplete="actionComplete"
    ></mt-template-page>
  </div>
</template>
<script>
import { checkColumn, lastColumn } from '../config/index2'
import { BASE_TENANT } from '@/utils/constant'
import * as UTILS from '@/utils/utils'
export default {
  props: {
    isDetail: {
      type: String,
      default: '2'
    },
    version: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'Submit',
              icon: 'icon_solid_Import',
              title: this.$t('提交'),
              permission: ['O_02_0679']
            },
            {
              id: 'Export1',
              icon: 'icon_solid_Import',
              title: this.$t('导出'),
              permission: ['O_02_0680']
            }
          ],
          gridId: '63748E18-96E3-BBCB-06BB-93FFB7584179',
          grid: {
            // height: "auto",
            // frozenColumns: 1,
            allowEditing: true, //开启表格编辑操作
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            columnData: checkColumn.concat(lastColumn),
            asyncConfig: {
              // url: `${BASE_TENANT}/supplierGoodsDemandPlanInfo/query/full`,
              // serializeList: (list) => {
              //   list.forEach((item) => {
              //     item.addId = this.addId++;
              //     item.self48 = item.total;
              //     item.self49 = item.buyerNum;
              //     item.self50 = item.supplierNum;
              //     item.self51 = item.gapNum;
              //     item.isEntry = "1"; //是否是带入的数据
              //   });
              //   this.currentList = list;
              //   return list;
              // },
            }
          }
        }
      ],
      addId: '1',
      nowEditRowFlag: ''
    }
  },
  watch: {
    isDetail: {
      handler(val) {
        if (val === '1') {
          this.componentConfig[0].toolbar = []
        }
      },
      immediate: true
    },
    version: {
      handler(val) {
        if (!val) return
        if (this.isDetail === '1') {
          this.componentConfig[0].toolbar = []
          this.$set(this.componentConfig[0].grid, 'asyncConfig', {
            url: `${BASE_TENANT}/supplierGoodsDemandPlanInfo/queryByVersion`,
            defaultRules: [
              {
                field: 'version',
                operator: 'equal',
                value: this.version.slice(1)
              }
            ],
            serializeList: (list) => {
              list.forEach((item) => {
                item.addId = this.addId++
                item.self48 = item.total
                item.self49 = item.buyerNum
                item.self50 = item.supplierNum
                item.self51 = item.gapNum
                item.isEntry = '1' //是否是带入的数据
              })
              this.currentList = list
              return list
            }
          })
        }
        if (this.isDetail === '2') {
          this.$set(this.componentConfig[0].grid, 'asyncConfig', {
            url: `${BASE_TENANT}/supplierGoodsDemandPlanInfo/query/full`,
            serializeList: (list) => {
              list.forEach((item) => {
                item.addId = this.addId++
                item.self48 = item.total
                item.self49 = item.buyerNum
                item.self50 = item.supplierNum
                item.self51 = item.gapNum
                item.isEntry = '1' //是否是带入的数据
              })
              this.currentList = list
              return list
            }
          })
        }
      },
      immediate: true
    }
  },
  mounted() {},
  methods: {
    //点击顶部的操作按钮
    handleClickToolBar(e) {
      console.log('方法1', e)
      if (e.toolbar.id === 'Export1') {
        this.handleExport()
        return
      }
      let selectRecords = e.grid.getSelectedRecords()
      if (!selectRecords.length && e.toolbar.id != 'refreshDataByLocal') {
        this.$toast({ content: this.$t('请至少选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Submit') {
        this.submit(selectRecords)
      }
    },
    submit(selectRecords) {
      console.log(selectRecords, '我是选择的行')
      let flag = true
      selectRecords.forEach((item) => {
        console.log(item, '值')
        if (item.status != 2) {
          flag = false
        }
      })
      if (!flag) {
        this.$toast({
          content: this.$t('请选择待反馈的行操作'),
          type: 'warning'
        })
        return
      }
      this.editRow(selectRecords)
    },
    handleExport() {
      let obj = JSON.parse(
        sessionStorage.getItem('63748E18-96E3-BBCB-06BB-93FFB7584179')
      ).visibleCols
      let field = []
      if (obj !== undefined) {
        obj.forEach((item) => {
          field.push(item.field)
        })
      } else {
        lastColumn.forEach((item) => {
          field.push(item.field)
        })
      }
      console.log(field)

      //导出
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 1000 },
        pageFlag: true,
        rules: rule.rules || []
      }
      this.$store.commit('startLoading')
      this.$API.deliverySchedule.supplierGoodsDemandPlanInfoExport(params, field).then((res) => {
        this.$store.commit('endLoading')
        console.log(res, '我是返回数据')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    actionBegin(args) {
      console.log(args, '我是actionBegin')
      if (args.requestType == 'beginEdit') {
        this.nowEditRowFlag = args.rowData.addId
        if (this.isDetail === '1') {
          args.cancel = true
          return
        }
        if (!(args.rowData.status == 2)) {
          this.$toast({
            content: this.$t('此状态不可编辑'),
            type: 'warning'
          })
          args.cancel = true
        }
      }
    },
    actionComplete(args) {
      console.log(args, '我是actionComplete')
      if (args.requestType === 'save' && args.action === 'edit') {
        //编辑完成
        // let row = this.getRow();
        // this.editRow(row);
      }
    },
    getRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.templateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = {}
      currentRecords.some((item) => {
        if (item.addId == this.nowEditRowFlag) {
          row = item
        }
      })
      return row
    },
    editRow(rows) {
      console.log(rows, '编辑行数据')
      let params = []
      rows.forEach((item) => {
        params.push({
          id: item.id,
          goodsDemandPlanItemId: item.goodsDemandPlanItemId,
          feedbackQuantity: item.self50,
          supplierRemark: item.supplierRemark
        })
      })
      let flag = this.judgeValid(params)
      if (!flag) return
      this.$API.deliverySchedule.supplierGoodsDemandPlanInfoFeedback(params).then(() => {
        this.$toast({
          content: this.$t('反馈交货排期操作成功'),
          type: 'success'
        })
        this.updateList()
      })
    },
    judgeValid(params) {
      console.log(params, '我是校验数据')
      let flag = true
      let flag1 = true
      params.forEach((item) => {
        if (item.feedbackQuantity === '') {
          flag1 = false
        }
      })
      if (!flag1) {
        this.$toast({
          content: this.$t('c必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      return flag
    },
    updateList() {
      this.$refs.templateRef.refreshCurrentGridData()
    }
  }
}
</script>
