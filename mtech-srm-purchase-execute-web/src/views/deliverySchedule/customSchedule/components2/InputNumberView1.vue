<template>
  <div>
    <mt-input id="self51" v-model="data.self51" style="display: none"></mt-input>
    <span :class="calssName">{{ data.self51 }}</span>
  </div>
</template>
<script>
export default {
  data() {
    return {
      calssName: '',
      data: {},
      cNum: ''
    }
  },
  mounted() {
    this.cNum = this.data.self50
    this.$bus.$on('cChange', (val, gNum) => {
      console.log('我接受到了', val, gNum)
      this.data.self51 = gNum
      this.cNum = val
    })
  }
}
</script>
<style lang="scss" scoped>
.red {
  color: #ed5836;
  background-color: #fdeeea;
}
</style>
