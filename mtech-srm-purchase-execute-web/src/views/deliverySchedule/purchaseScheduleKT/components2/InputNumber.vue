<template>
  <div>
    <mt-input id="self49" v-model="data[data.column.field]" style="display: none"></mt-input>
    <mt-inputNumber
      v-model="data[data.column.field]"
      :show-clear-button="true"
      :min="0"
      @change="numberChange"
      :step="1"
      :precision="3"
    ></mt-inputNumber>
  </div>
</template>
<script>
export default {
  data() {
    return {
      data: {}
    }
  },
  mounted() {
    if (this.data.column.field === 'isItemBatch') {
      this.$bus.$on('itemBatchChange', (val) => {
        debugger
        this.dataisItemBatch = val
      })
    }
  },
  methods: {
    numberChange(e) {
      console.log(e)
      if (this.data.column.field == 'buyerNum') {
        this.$parent.$emit('selectedChanged', {
          //传出额外数据物料
          fieldCode: 'buyerNum',
          itemInfo: {
            buyerNum: e
          }
        })
        // this.$bus.$emit("noPromiseCountChange", e);
      }
    }
  }
}
</script>
