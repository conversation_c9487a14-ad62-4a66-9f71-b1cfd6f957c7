<template>
  <div>
    <mt-input id="timeInfoTimestamp" style="display: none"></mt-input>
    <span>{{ timeInfoTimestamp }}</span>
  </div>
</template>
<script>
import UTILS from '../../../../utils/utils'
export default {
  data() {
    return {
      timeInfoTimestamp: '',
      updateTime: '',
      createTime: ''
    }
  },
  mounted() {
    if (this.data.timeInfoTimestamp) {
      this.timeInfoTimestamp = UTILS.formateTime(this.data.timeInfoTimestamp, 'Y-m-d')
    }
    if (this.data.timeInfoTimestamp) {
      this.timeInfoTimestamp = UTILS.formateTime(this.data.timeInfoTimestamp, 'Y-m-d')
    }
    if (this.data.createTime) {
      this.createTime = UTILS.formateTime(this.data.createTime, 'Y-m-d')
    }
  }
}
</script>
