<template>
  <div>
    <span>{{ getName(data.column.field) }}</span>
    <vxe-Icon
      v-if="
        data.encryptMap &&
        data.encryptMap[data.column.field === 'address' ? 'consigneePhone' : data.column.field]
      "
      :name="
        data[data.column.field === 'address' ? 'consigneePhone' : data.column.field].includes('**')
          ? 'eye-fill-close'
          : 'eye-fill'
      "
      class="eyeStyle"
      @click="checkInfo"
    ></vxe-Icon>
  </div>
</template>
<script>
import { Icon } from 'vxe-table'
export default {
  components: {
    vxeIcon: Icon
  },
  data() {
    return {
      data: {},
      oldValue: null
    }
  },
  mounted() {
    console.log(this.data, 'data')
    if (this.data.column.field === 'address') {
      this.oldValue = this.data['consigneePhone']
    }
  },
  methods: {
    getName(val) {
      if (val === 'address' && this.data['address']) {
        let _firstKey = this.data['consigneeName'],
          _secondKey = this.data['consigneePhone'],
          _thirdKey = this.data['consigneeAddress'],
          _value = ''
        if (_firstKey && _secondKey && _thirdKey) {
          _value = `${_firstKey}-${_secondKey}-${_thirdKey}`
        } else if (_firstKey && _secondKey) {
          _value = `${_firstKey}-${_secondKey}`
        } else if (_secondKey && _thirdKey) {
          _value = `${_secondKey}-${_thirdKey}`
        } else if (_firstKey && _thirdKey) {
          _value = `${_firstKey}-${_thirdKey}`
        } else if (_firstKey) {
          _value = _firstKey
        } else if (_secondKey) {
          _value = _secondKey
        } else if (_thirdKey) {
          _value = _thirdKey
        } else {
          // ""
          _value = ''
        }
        return _value
      }
      return ''
    },
    checkInfo() {
      let _val = '',
        val = ''
      if (this.data.column.field === 'address') {
        let val = this.data['consigneePhone']
        _val = this.data.encryptMap['consigneePhone']
        if (val && !val?.includes('**')) {
          this.data['consigneePhone'] = this.oldValue
          return
        }
      } else {
        val = this.data[this.data.column.field]
        _val = this.data.encryptMap[this.data.column.field]
        if (val && !val?.includes('**')) {
          this.data[this.data.column.field] = this.oldValue
          return
        }
      }

      this.$API.deliveryConfig.checkDeliveryConfigInfo({ key: _val || '' }).then((res) => {
        if (res && res.code === 200) {
          if (this.data.column.field === 'address') {
            this.data['consigneePhone'] = res.data || ''
            return
          }
          this.data[this.data.column.field] = res.data || ''
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.eyeStyle {
  cursor: pointer;
  margin-left: 5px;
}
</style>
