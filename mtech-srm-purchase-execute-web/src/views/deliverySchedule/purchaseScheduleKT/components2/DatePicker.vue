<template>
  <div>
    <mt-date-picker
      :id="data.column.field"
      v-model="data[data.column.field]"
      :placeholder="$t('选择日期')"
      :show-clear-button="true"
      :min="minDate"
      :allow-edit="false"
      :disabled="false"
    ></mt-date-picker>
  </div>
</template>
<script>
export default {
  data() {
    const year = new Date().getFullYear()
    const month = new Date().getMonth()
    const dd = new Date().getDate()

    return {
      minDate: new Date(year, month, dd),
      maxDate: new Date(year, month, 27),
      formateDate: '',
      data: {}
    }
  },
  mounted() {}
}
</script>
