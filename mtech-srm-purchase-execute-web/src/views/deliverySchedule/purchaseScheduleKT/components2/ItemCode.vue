<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <!-- 选择物料/品项编码、SKU -->
      <!-- <mt-input
        :id="fieldName"
        v-model="data[fieldName]"
        disabled
        width="100%"
        :placeholder="headerTxt"
      ></mt-input> -->
      <mt-select
        :id="fieldName"
        :data-source="itemList"
        :filtering="serchText"
        :open-dispatch-change="false"
        :show-clear-button="true"
        :popup-width="450"
        :allow-filtering="true"
        v-if="isItem"
        @open="startOpen"
        @change="itemClick"
        :fields="{ text: 'label', value: 'itemCode' }"
        v-model="data.itemCode"
        width="100%"
      ></mt-select>
      <mt-icon
        v-if="!isDisabled || isItem"
        style="width: 10px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>

    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="headerTxt"
      :buttons="buttons"
      @close="handleClose"
    >
      <mt-template-page
        v-show="dialogShow"
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="pageConfig"
        @recordDoubleClick="recordDoubleClick"
      >
      </mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
import { itemCodeColumnData } from './config/pcSelection.js' // 命名要与field code一致
import { PROXY_MDM_TENANT } from '@/utils/constant'
import { utils } from '@mtech-common/utils'

export default {
  data() {
    return {
      data: {
        itemCode: ''
      },
      isItem: true,
      fieldName: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [],
      itemCodeColumnData,
      fields: {},
      headerTxt: '',
      dialogShow: false,
      itemCode: '',
      itemList: [],
      siteCode: null,
      isDisabled: false
    }
  },
  mounted() {
    const _this = this
    this.init = utils.debounce(this.init, 500)

    this.fieldName = this.data.column.field
    this.itemCode = this.data.itemCode
    this.setDisabled()
    this.$bus.$on('siteKtCodeChangeClick2', (e) => {
      console.log(e, '123123')
      // if (e.e.e !== null) {
      //   this.data.itemCode = ''
      // }
      this.siteCode = e.organizationCode
      this.init(_this.data.itemCode)

      this.initDialog()
    })
  },

  methods: {
    startOpen() {
      if (this.siteCode !== null) {
        // this.init("");
      } else {
        this.$toast({ content: this.$t('请先选择工厂'), type: 'warning' })
      }
    },
    setDisabled() {
      if (this.data.isEntry === '1' && this.data.status != 0 && this.data.status != 1) {
        this.isDisabled = true
      }
      if (this.data.isEntry === '2') {
        this.isDisabled = false
      }
    },
    // 双击物料行，也进行提交
    recordDoubleClick(args) {
      console.log(args)

      console.log('recordDoubleClick', args)
      this.confirm([args.rowData])
    },
    itemClick(e) {
      if (e.e !== null) {
        this.confirm([e.itemData], 'itemChange')
      }
    },
    getBySiteItemCode(params1) {
      //根据工厂和物料查询jit配置信息
      // this.$API.masterData.getBasicDetail(params1).then((res) => {
      this.$API.masterData.getBySiteItemCode(params1).then((res) => {
        // res.data.itemIdentification = "Y";

        if (res.data.itemIdentification === 'Y') {
          this.$bus.$emit('scheduleTypeChange', {
            isJit: 'JIT',
            isItemBatch: res.data.itemIsBatch ? 1 : 0
          })
          this.$parent.$emit('selectedChanged', {
            //传出额外数据物料
            fieldCode: 'scheduleType',
            itemInfo: {
              scheduleType: 'JIT',
              isItemBatch: res.data.itemIsBatch ? 1 : 0
            }
          })
        }
        if (res.data.itemIdentification === 'N') {
          this.$bus.$emit('scheduleTypeChange', {
            isJit: this.$t('非JIT'),
            isItemBatch: res.data.itemIsBatch ? 1 : 0
          })
          this.$parent.$emit('selectedChanged', {
            //传出额外数据物料
            fieldCode: 'scheduleType',
            itemInfo: {
              scheduleType: this.$t('非JIT'),
              isItemBatch: res.data.itemIsBatch ? 1 : 0
            }
          })
        }
        if (res.data.itemIdentification === null) {
          this.$bus.$emit('scheduleTypeChange', {
            isJit: this.$t('非JIT'),
            isItemBatch: res.data.itemIsBatch ? 1 : 0
          })
          this.$parent.$emit('selectedChanged', {
            //传出额外数据物料
            fieldCode: 'scheduleType',
            itemInfo: {
              scheduleType: this.$t('非JIT'),
              isItemBatch: res.data.itemIsBatch ? 1 : 0
            }
          })
        }
      })
    },
    getBasicDetail(params) {
      //查询采购组
      this.$API.masterData.getBasicDetail(params).then((res) => {
        this.$bus.$emit('buyerOrgNameChange', res.data?.purchaseGroupName)
        this.$parent.$emit('selectedChanged', {
          //传出额外数据采购组
          fieldCode: 'buyerOrgName',
          itemInfo: {
            buyerOrgName: res.data?.purchaseGroupName,
            buyerOrgId: res.data?.purchaseGroupId,
            buyerOrgCode: res.data?.purchaseGroupCode
          }
        })
      })
    },
    // 提交
    confirm(records, from) {
      if (from) {
        console.log(from)
      } else {
        this.isItem = false
      }

      let _records = ''
      records.altKey === false
        ? (_records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords())
        : (_records = records)

      console.log(records)
      this.init(_records[0].itemCode)

      if (!_records || !_records.length) {
        _records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      }
      console.log(_records)
      //查询采购组
      let params = {
        itemCode: _records[0].itemCode, // 物料code
        organizationCode: _records[0].organizationCode, //工厂code
        organizationId: _records[0].organizationId
      }
      this.getBasicDetail(params)
      let params1 = {
        itemCode: _records[0].itemCode, // 物料code
        siteCode: _records[0].organizationCode //工厂code
        // organizationCode: val.itemData.organizationCode, //todo要注释
        // organizationId: val.itemData.organizationId, //todo要注释
      }

      this.getBySiteItemCode(params1)
      this.$API.masterData
        .getBasicByFacItem({
          organizationId: _records[0]?.organizationId,
          itemCode: _records[0]?.itemCode
        })
        .then((res) => {
          this.$bus.$emit('purchaseGroupCodeChange', {
            purchaseGroupCode: res.data.purchasingInfo.purchaseGroupCode,
            purchaseGroupId: res.data.purchasingInfo.purchaseGroupId,
            purchaseGroupName: res.data.purchasingInfo.purchaseGroupName
          })
        })
      if (!_records.length) return
      console.log('选择到的物料信息：', _records[0])
      if (_records[0]) {
        let selectedRowInfo = _records[0]
        // 如果是sku，需要整合一下数据源，将物料信息展开
        selectedRowInfo = {
          itemId: selectedRowInfo?.id, // 物料数据
          itemCode: selectedRowInfo?.itemCode,
          itemName: selectedRowInfo?.itemName,
          itemDescription: selectedRowInfo?.itemDescription, // 规格型号(采购申请)
          itemGroupCode: selectedRowInfo?.itemGroupCode, //品项组编码
          itemGroupName: selectedRowInfo?.itemGroupName //品项组名称
        }
        this.data[this.fieldName] = selectedRowInfo[this.fieldName]
        //物料下拉
        // this.$bus.$emit("itemCodeChange", {
        //   itemCode: selectedRowInfo.itemCode,
        //   itemId: selectedRowInfo.itemId,
        // }); //传给工厂
        this.$bus.$emit('itemCodeChange3', selectedRowInfo.itemCode) //传给计划组

        this.$bus.$emit('itemCodeChange4', selectedRowInfo.itemCode) //传给配送方式
        // this.$bus.$emit("itemCodeChange1", selectedRowInfo.itemDescription); //传给物料描述
        this.$bus.$emit('itemCodeChange2', selectedRowInfo.itemName) //传给物料名称
        this.$parent.$emit('selectedChanged', {
          //传出额外数据物料
          fieldCode: 'itemCode',
          itemInfo: {
            itemCode: selectedRowInfo.itemCode,
            itemName: selectedRowInfo.itemName,
            itemId: selectedRowInfo.itemId,
            itemGroupName: selectedRowInfo.itemGroupName,
            itemGroupCode: selectedRowInfo.itemGroupCode
          }
        })

        this.handleClose()
      }
    },
    showDialog() {
      if (this.siteCode !== null) {
        this.dialogShow = true
        this.$refs.dialog.ejsRef.show()
      } else {
        this.$toast({ content: this.$t('请先选择工厂'), type: 'warning' })
      }
    },
    initDialog() {
      if (this.fieldName == 'itemCode') {
        this.pageConfig = [
          {
            useToolTemplate: false, // 不使用预置(新增、编辑、删除)
            useBaseConfig: true, // 使用组件中的toolbar配置
            toolbar: [],
            gridId: '45AEA26B-EE84-2EBD-F2A3-AD0D04C0C7B4',

            grid: {
              allowPaging: true,
              allowSelection: true,
              selectionSettings: {
                checkboxOnly: false
              },
              columnData: this[`${this.fieldName}ColumnData`],
              asyncConfig: {
                url: `${PROXY_MDM_TENANT}/item/paged-query?BU_CODE=${localStorage.getItem(
                  'currentBu'
                )}`,
                defaultRules: [
                  {
                    field: 'organizationCode',
                    operator: 'equal',
                    value: this.siteCode
                  }
                ]
              }
            }
          }
        ]
      }
    },
    serchText(e) {
      this.init(e.text)
    },
    init(e) {
      let itemObj = {
        page: {
          current: 1,
          pages: 6,
          size: 20
        },
        defaultRules: [
          {
            field: 'organizationCode',
            operator: 'likeright',
            value: this.siteCode
          }
        ],

        rules: [
          {
            field: 'itemCode',
            operator: 'likeright',
            value: e
          }
          // {
          //   condition: "and",
          //   rules: [
          //     {
          //       condition: "or",
          //       field: "itemName",
          //       type: "string",
          //       operator: "contains",
          //       value: e,
          //     },
          //   ],
          // },
        ]
      }
      let _itemCode = JSON.parse(JSON.stringify(this.data.itemCode))
      let _itemName = JSON.parse(JSON.stringify(this.data.itemName))
      this.$API.masterData.getItemPage(itemObj).then((res) => {
        res.data.records.forEach((item) => {
          item.label = item.itemCode + '-' + item.itemName
        })

        const list = res?.data.records || []
        this.itemList = [...list]

        this.isItem = true
        if (res.data?.records && res.data.records.length === 0) {
          this.data.itemCode = ''
          this.$bus.$emit('itemCodeChange2', '') // 清空物料名称
        } else if (res.data.records.length === 1) {
          this.data.itemCode = res.data.records[0].itemCode
          this.$bus.$emit('itemCodeChange2', res.data.records[0].itemName) // 清空物料名称
        } else {
          this.data.itemCode = _itemCode
          this.$bus.$emit('itemCodeChange2', _itemName)
        }
      })
    },
    handleClose() {
      this.dialogShow = false
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>

<style lang="scss">
.pc-item-dialog {
  .mt-data-grid {
    height: 100%;
    > .e-grid {
      height: calc(100% - 40px) !important;
      // display: flex;

      > .e-gridcontent {
        flex: 1;
        overflow: auto;
      }
    }

    .e-rowcell.e-active {
      background: #e0e0e0 !important;
    }
  }
}
</style>
