<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :show-clear-button="true"
      :disabled="isDisabled"
      :maxlength="maxlength"
      :placeholder="data.column.field === 'associatedNumber' ? $t('请保留编号前的0') : ''"
      @input="onInput"
      @blur="autoFillValue"
    ></mt-input>
  </div>
</template>
<script>
export default {
  data() {
    return { data: {}, isDisabled: false, maxlength: 40, siteCode: null }
  },
  mounted() {
    this.$bus.$on('siteKtCodeChangeClick2', (val) => {
      this.siteCode = val.siteCode
      this.autoFillValue(this.componentData, val.siteCode)
    })
    this.maxlength = this.data.column.maxlength || 128
    if (
      this.data.column.field === 'scheduleArea' ||
      this.data.column.field === 'bom' ||
      this.data.column.field === 'saleOrder' ||
      this.data.column.field === 'saleOrderRowCode' ||
      this.data.column.field === 'processName' ||
      this.data.column.field === 'productCode' ||
      this.data.column.field === 'buyerOrder' ||
      this.data.column.field === 'buyerOrderRowCode'
    ) {
      //计划区域
      this.setDisabled()
    }
    if (
      this.data.column.field === 'projectTextBatch' ||
      this.data.column.field === 'associatedNumber' ||
      this.data.column.field === 'bom'
    ) {
      //项目文本批次 关联工单号 bom号
      this.setDisabled1()
    }
  },
  methods: {
    autoFillValue(val, siteCode) {
      if (!['associatedNumber'].includes(this.data.column.field)) {
        return
      }
      let _val = this.data[this.data.column.field]
      if (_val instanceof Number) {
        _val = String(_val)
      }

      if (!_val || !_val?.trim() || _val?.length <= 0) return

      if (_val?.trim() && _val?.trim().length >= 2) {
        _val = _val.trim()
        if (_val[0] === '/' && _val[_val.length - 1] === '/') {
          this.$toast({
            type: 'warning',
            content: this.$t('不能以/开始或者结尾, 且只能输入一个/')
          })
          _val = _val.slice(1, _val.length - 1)
        }
        let i = 0
        _val = _val.replace(/\//g, (e) => {
          i++
          if (i === 1) {
            return e
          }
          return ''
        })
      } else if (_val?.trim() === '/') {
        _val = ''
      }

      if (!_val || _val?.length <= 0) return
      if (
        (_val[0] === '3' || (_val[0] === '0' && _val[1] === '3')) &&
        ['5500', '5520'].includes(siteCode ?? this.siteCode)
      ) {
        if (_val[0] === '0') {
          _val = '0' + _val
        } else {
          _val = '00' + _val
        }
      }
      setTimeout(() => {
        this.data[this.data.column.field] = _val
      }, 100)
    },
    onInput(e) {
      if (!['associatedNumber'].includes(this.data.column.field)) {
        return
      }
      let _val = e
      let reg = /[^\w/]/g
      if (reg.test(_val)) {
        this.$toast({
          type: 'warning',
          content: this.$t('只能输入数字,字母和/')
        })
        _val = e.replace(/[^\w/]/g, '')
      }
      setTimeout(() => {
        this.data[this.data.column.field] = _val
      }, 100)
    },
    setDisabled1() {
      if (this.data.status === 4) {
        this.isDisabled = true
      } else {
        this.isDisabled = false
      }
    },
    setDisabled() {
      if (this.data.isEntry === '1') {
        this.isDisabled = true
      }
      if (this.data.isEntry === '2') {
        this.isDisabled = false
      }
    }
  }
}
</script>
