<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef2"
      :template-config="componentConfig2"
      @handleClickToolBar="handleClickToolBar2"
      @handleClickCellTool="handleClickCellTool2"
      @handleClickCellTitle="handleClickCellTitle2"
      @handleQuickSearch="handleQuickSearch"
      @actionBegin="actionBegin2"
      v-if="scheduleType"
      class="frozenColumns"
      @actionComplete="actionComplete2"
      @selectedChanged="selectedChanged2"
    ></mt-template-page>

    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @sizeChange="handleSizeChange"
      @currentChange="handleCurrentChange"
    />
    <!-- 配置导入弹框 -->
    <!-- <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="handleImport(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog> -->
    <import-dialog
      :accept="['.xls', '.xlsx']"
      :from-data-key="fromDataKey"
      :down-template-params="downTemplateParams"
      :save-url="saveUrl"
      :down-load="true"
      ref="importDialog"
      :request-urls="requestUrls"
      dialog-class="create-proj-dialog full-size-dialog"
      @import="importDialogImport"
      @uploadCompleted="uploadCompleted"
    >
      <mt-template-page
        ref="templateRef3"
        :template-config="componentConfig3"
        @actionBegin="actionBegin3"
        @actionComplete="actionComplete3"
        @selectedChanged="selectedChanged3"
      ></mt-template-page>
    </import-dialog>
  </div>
</template>
<script>
import { checkColumn2, lastColumn2, lastColumn3 } from '../config/index2'
import { BASE_TENANT } from '@/utils/constant'
import { cloneDeep } from 'lodash'
import ImportDialog from '@/components/Upload/importDialog'
// import PredictImportDialogSlot from "@/views/predictCollaboration/predictManage/components/predict-import-dialog-slot";

import * as UTILS from '@/utils/utils'
export default {
  props: {
    isDetail: {
      type: String,
      default: '2'
    },
    version: {
      type: String,
      default: ''
    }
  },
  components: {
    ImportDialog
    // PredictImportDialogSlot,
    // UploadExcelDialog: () =>
    //   import("@/components/Upload/uploadExcelDialog.vue"),
  },
  data() {
    return {
      saveUrl: `${BASE_TENANT}/buyerGoodsDemandPlanInfo/data/importExcelKT`, // 导入API
      fromDataKey: 'excel', // 上传组件的 name，此值为调用 api 请求参数的 key
      downTemplateParams: {
        pageFlag: false
      }, // 通知配置导入下载模板参数
      scheduleType: true,
      lastColumn2: lastColumn2,
      requestUrls: {
        templateUrlPre: 'deliveryConfig',
        templateUrl: 'buyerGoodsDemandPlanInfoKtExport' // 下载模板接口方法名
        // uploadUrl: "buyerGoodsDemandPlanInfoImportExcelKT", // 上传接口方法名
      },
      uploadParams: {}, // 导入通知配置文件参数
      beginData: null,
      batchCode: '',
      forecastPageSettings: {
        currentPage: 1,
        pageSize: 50,
        pageSizes: [50, 100, 200, 500, 1000],
        totalRecordsCount: 0,
        pageCount: 5,
        totalPages: 0 // 总页数
      },
      componentConfig2: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          buttonQuantity: 8,

          toolbar: [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              title: this.$t('新增'),
              permission: ['O_02_1698']
            },
            {
              id: 'Delete',
              icon: 'icon_solid_Delete',
              title: this.$t('删除'),
              permission: ['O_02_1699']
            },
            {
              id: 'closeEdit',
              icon: 'icon_table_delete',
              // permission: ["O_02_1141"],
              title: this.$t('取消编辑')
            },
            {
              id: 'Import',
              icon: 'icon_solid_Import',
              title: this.$t('导入'),
              permission: ['O_02_1700']
            },
            {
              id: 'Import2',
              icon: 'icon_solid_Import',
              title: this.$t('印刷品导入'),
              permission: ['O_02_1700']
            },
            {
              id: 'copy',
              icon: 'icon_solid_Createorder',
              title: this.$t('复制'),
              permission: ['O_02_1701']
            },
            {
              id: 'Publish',
              icon: 'icon_solid_Activateorder',
              title: this.$t('发布'),
              permission: ['O_02_1702']
            },
            {
              permission: ['O_02_1703'],
              id: 'Cancle',
              icon: 'icon_solid_pushorder',
              title: this.$t('取消发布')
            },
            {
              id: 'plan_push',
              icon: 'icon_solid_pushorder',
              title: this.$t('周计划上传'),
              permission: ['O_02_1704']
            },
            {
              id: 'retry',
              icon: 'icon_solid_Pauseorder',
              title: this.$t('重新推送SAP'),
              permission: ['O_02_1705']
            },
            // {
            //   id: "Release",
            //   icon: "icon_solid_Pauseorder",
            //   title: this.$t("下达"),
            //   // permission: ["O_02_0447"],
            // },
            {
              id: 'Close',
              icon: 'icon_solid_Pauseorder',
              title: this.$t('关闭'),
              permission: ['O_02_1706']
            },
            // {
            //   id: "Confirm",
            //   icon: "icon_solid_Pauseorder",
            //   title: this.$t("确认"),
            //   // permission: ["O_02_0448"],
            // },
            {
              id: 'Export1',
              icon: 'icon_solid_export',
              title: this.$t('导出'),
              permission: ['O_02_1707']
            },
            {
              id: 'search',
              title: this.$t('印刷件份数查询')
            }
          ],
          gridId: '835efd56-8cca-4998-b926-91a44dd633bc',
          activatedRefresh: false,
          // defaultSearchTemplates: [
          //   {
          //     templateName: "新建-修改",
          //     searchRule: {
          //       rules: {
          //         condition: "and",
          //         rules: [
          //           {
          //             label: "状态",
          //             field: "status",
          //             type: "string",
          //             operator: "in",
          //             value: ["0", "1"],
          //           },
          //         ],
          //       },
          //     },
          //   },
          // ],

          grid: {
            virtualPageSize: 30,
            // rowHeight: 40,
            showSelected: false,
            selectionSettings: {
              persistSelection: true,
              type: 'Multiple',
              checkboxOnly: true
            },
            allowPaging: false,
            enableVirtualization: true,

            gridLines: 'Both',
            allowEditing: true, //开启表格编辑操作
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            columnData: checkColumn2.concat(lastColumn2),
            asyncConfig: {
              defaultRules:
                this.$route.query.from === 'mytodo1'
                  ? JSON.parse(sessionStorage.getItem('todoDetail')).rules
                  : []
            }
          }
        }
      ],

      componentConfig3: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置

          activatedRefresh: false,

          grid: {
            gridLines: 'Both',
            // height: "600",
            // frozenColumns: 1,
            allowEditing: true, //开启表格编辑操作
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            dataSource: [],
            columnData: checkColumn2.concat(lastColumn3),
            asyncConfig: {
              ignoreDefaultSearch: true
            }
          }
        }
      ],
      currentList: [], //从接口获取到的最新的数据
      nowEditRowFlag: '', //当前编辑的行id
      addId: '1',
      isEdit: '1', //是否编辑 1是编辑 2不是编辑
      selectedOtherInfo: {},
      selectedOtherInfo3: {}
    }
  },
  watch: {
    isDetail: {
      handler(val) {
        if (val === '1') {
          this.componentConfig2[0].toolbar = []
        }
      },
      immediate: true
    },
    version: {
      handler(val) {
        if (!val) return
        if (this.isDetail === '1') {
          this.componentConfig2[0].toolbar = []
          lastColumn2.some((item) => {
            if (item.field === 'status') {
              item.cellTools = []
            }
          })
          this.componentConfig2[0].grid.columnData = checkColumn2.concat(lastColumn2)
          this.$set(this.componentConfig2[0].grid, 'asyncConfig', {
            url: `${BASE_TENANT}/buyerGoodsDemandPlanInfo/queryByVersion`,
            rules: [],

            defaultRules: [
              {
                field: 'version',
                operator: 'equal',
                value: this.version.slice(1)
              },
              {
                field: 'sourceFrom',
                operator: 'equal',
                value: 'KT'
              }
            ],
            ignoreDefaultSearch: true,
            serializeList: (list) => {
              list.forEach((item) => {
                item.addId = this.addId++
                item.self48 = item.total
                item.self49 = item.buyerNum
                item.self50 = item.supplierNum
                item.self51 = item.gapNum
                if (item.timeInfoTimestamp && item.timeInfoTimestamp.length === 13) {
                  item.timeInfoTimestamp = new Date(Number(item.timeInfoTimestamp))
                } else {
                  item.timeInfoTimestamp = null
                }
                item.isEntry = '1' //是否是带入的数据
              })
              this.currentList = list
              return list
            }
          })
        }
        if (this.isDetail === '2') {
          this.$set(this.componentConfig2[0].grid, 'asyncConfig', {
            url: `${BASE_TENANT}/buyerGoodsDemandPlanInfo/query/full`,
            rules: [],
            defaultRules: [
              {
                field: 'sourceFrom',
                operator: 'equal',
                value: 'KT'
              }
            ],
            ignoreDefaultSearch: true,
            serializeList: (list) => {
              list.forEach((item, index) => {
                item.addId = this.addId++
                item.self48 = item.total
                item.self49 = item.buyerNum
                item.self50 = item.supplierNum
                item.self51 = item.gapNum
                item.numBer = index + 1
                if (item.timeInfoTimestamp && item.timeInfoTimestamp.length === 13) {
                  item.timeInfoTimestamp = new Date(Number(item.timeInfoTimestamp))
                } else {
                  item.timeInfoTimestamp = null
                }
                item.isEntry = '1' //是否是带入的数据
              })
              this.currentList = list
              return list
            },
            afterAsyncData: (res) => {
              console.log(res, 'res')
              this.forecastPageSettings.totalRecordsCount = res?.data?.total
              this.forecastPageSettings.totalPages = Math.ceil(
                Number(res?.data?.total) / this.forecastPageSettings.pageSize
              )
            }
          })
        }
      },
      immediate: true
    }
  },
  mounted() {
    // this.$toast({
    //   content: this.$t("请输入查询条件，才能查看交货计划!"),
    //   type: "warning",
    // });
    // setTimeout(() => {
    //   this.$set(this.forecastPageSettings, 'pageSize', 50)
    // }, 1000)
    this.$nextTick(() => {
      let _defaultRules = null
      if (['mytodo1', 'mytodo2', 'mytodo3'].includes(this.$route.query.from)) {
        _defaultRules = JSON.parse(sessionStorage.getItem('todoDetail')) || {
          rules: [],
          defaultRules: []
        }
        this.componentConfig2[0].grid.asyncConfig.ignoreDefaultSearch = false
      }

      this.componentConfig2[0].grid.asyncConfig.rules = _defaultRules ? _defaultRules?.rules : []
      this.$set(
        this.componentConfig2[0].grid.asyncConfig,
        'defaultRules',
        this.componentConfig2[0].grid.asyncConfig.defaultRules.concat(
          _defaultRules.defaultRules || []
        )
      )
    })
  },
  methods: {
    handleQuickSearch() {
      // this.$set(this.componentConfig2[0].grid.asyncConfig, "rules", {});
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      // this.componentConfig2[0].grid.asyncConfig.page.current = currentPage;
      // let rule =
      //   this.$refs.templateRef2.getCurrentUsefulRef().pluginRef
      //     .queryBuilderRules || {};
      // this.componentConfig2[0].grid.asyncConfig.rules = {
      //   condition: "and",
      //   rules: [
      //     ...rule.rules,
      //     {
      //       condition: "or",
      //       field: "a",
      //       operator: "contains",
      //       type: "string",
      //       value: "4",
      //     },
      //   ],
      // };

      // this.scheduleType = false;
      // setTimeout(() => {
      //   this.scheduleType = true;
      // }, 1);
      this.$refs.templateRef2.getCurrentUsefulRef().gridRef.$parent.currentChange(currentPage)
      this.$set(this.forecastPageSettings, 'currentPage', currentPage)
      document.querySelector('.e-content').scrollTop = 0
    },
    handleSizeChange(pageSize) {
      this.$refs.templateRef2.getCurrentUsefulRef().gridRef.$parent.sizeChange(pageSize)
      this.$set(this.forecastPageSettings, 'pageSize', pageSize)
      this.$set(this.forecastPageSettings, 'currentPage', 1)
      document.querySelector('.e-content').scrollTop = 0
    },
    // upExcelConfirm() {
    //   this.handleImport(false);
    //   let rule = this.$refs.templateRef2.getCurrentUsefulRef().pluginRef
    //     .queryBuilderRules || { rules: [] };
    //   console.log(rule);
    //   if (rule.rules.length === 0) {
    //     this.componentConfig2[0].grid.asyncConfig.rules.push({
    //       field: "batchCode",
    //       operator: "equal",

    //       value: this.batchCode,
    //     });
    //     console.log(this.componentConfig2[0]);
    //   }
    //   this.$toast({
    //     content: this.$t("导入成功"),
    //     type: "success",
    //   });
    //   setTimeout(() => {
    //     this.$refs.templateRef2.refreshCurrentGridData();
    //   }, 500);
    // },
    getToday() {
      var today = new Date()
      today.setHours(0)
      today.setMinutes(0)
      today.setSeconds(0)
      today.setMilliseconds(0)
      today = new Date(today.getTime())
      return today
    },
    // 文件上传完成
    uploadCompleted(response) {
      this.$refs.importDialog.showStepSecond()

      // this.$nextTick(() => {
      console.log(response, '12')
      this.batchCode = response.data.batchCode
      if (response.data.ktOutAllocationOrderItemExportExcelList.length > 0) {
        response.data.ktOutAllocationOrderItemExportExcelList.forEach((item) => {
          item.timeInfoTimestamp = new Date(Number(item.timeInfoTimestamp))
          item.outsourcedType !== null
            ? (item.outsourcedType = item.outsourcedType.toString())
            : null
          item.addId = this.addId++
        })
        this.componentConfig3[0].grid.dataSource = cloneDeep(
          response.data.ktOutAllocationOrderItemExportExcelList
        )
        this.updateList()
      } else {
        this.$refs.importDialog.showStepSecond()
        this.$refs.importDialog.handleClose()
        this.$toast({
          content: this.$t('导入成功'),
          type: 'success'
        })
        this.updateList()

        let rule = this.$refs.templateRef2.getCurrentUsefulRef().pluginRef.queryBuilderRules || {
          rules: []
        }
        console.log(rule)
        if (rule.rules.length === 0) {
          this.scheduleType = false
          this.componentConfig2[0].grid.asyncConfig.rules = [
            {
              field: 'batchCode',
              operator: 'equal',
              value: this.batchCode
            }
          ]
          setTimeout(() => {
            this.scheduleType = true
          }, 1)
          console.log(this.componentConfig2[0])
        }
      }
    },

    importDialogImport() {
      this.handleCopy(this.componentConfig3[0].grid.dataSource, '1')
    },
    // 导入提交
    predictImportDialogSlotSubmitted() {
      this.$refs.importDialog.handleClose()
      this.updateList()
    },
    handleClose(row) {
      //关闭
      let _params = {
        tenantId: row[0].tenantId,
        itemCode: row[0].itemCode,
        factoryCode: row[0].siteCode,
        supplierCode: row[0].supplierCode,
        planItemId: row[0].goodsDemandPlanItemId
      }
      this.$API.deliverySchedule.getAsnCount(_params).then((res) => {
        if (res.code == 200 && res.data.asnNos.length < 4) {
          this.$dialog({
            data: {
              title: this.$t('提醒'),
              message: this.$t('确认关闭选中的排期列表吗?')
            },
            success: () => {
              let ids = row.map((item) => item.goodsDemandPlanItemId)
              let params = ids
              this.$API.deliverySchedule.buyerGoodsDemandPlanInfoClose(params).then(() => {
                this.$toast({
                  content: this.$t('关闭排期列表操作成功'),
                  type: 'success'
                })
                this.updateList()
              })
            }
          })
        } else {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('该交货计划已经存在送货单') + res.data.asnNos + this.$t(',请关注')
            },
            success: () => {
              let ids = row.map((item) => item.goodsDemandPlanItemId)
              let params = ids
              this.$API.deliverySchedule.buyerGoodsDemandPlanInfoClose(params).then(() => {
                this.$toast({
                  content: this.$t('关闭排期列表操作成功'),
                  type: 'success'
                })
                this.updateList()
              })
            }
          })
        }
      })
    },
    handleCancle(row) {
      //取消发布
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认取消发布选中的排期列表吗?')
        },
        success: () => {
          let ids = row.map((item) => item.goodsDemandPlanItemId)
          let params = ids
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoCancel(params).then(() => {
            this.$toast({
              content: this.$t('取消发布排期列表操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handlePublish(row) {
      //发布
      let flag = true
      row.forEach((item) => {
        if (!item.addressId) {
          flag = false
        }
      })
      if (!flag) {
        this.$toast({
          content: this.$t('送货联系人电话地址填写才可以发布'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认发布选中的排期列表吗?')
        },
        success: () => {
          let ids = row.map((item) => item.goodsDemandPlanItemId)
          let params = ids
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoPublish(params).then(() => {
            this.$toast({
              content: this.$t('发布排期列表操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleAdd() {
      //新增
      this.$refs.templateRef2.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
    },
    handleImport(flag) {
      //导入
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    handleExport() {
      let obj = JSON.parse(
        sessionStorage.getItem('1ba6e707-38b6-43c2-a195-fcef80fecf21')
      )?.visibleCols
      let field = []
      if (obj !== undefined && obj.length) {
        obj.forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      } else {
        lastColumn2.forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      }
      field.forEach((item) => {
        if (item === 'self49') {
          field.push('buyerNum')
        }
        if (item === 'self48') {
          field.push('total')
        }
        if (item === 'self50') {
          field.push('supplierNum')
        }
        if (item === 'self51') {
          field.push('gapNum')
        }
        if (item === 'supplierCode') {
          field.push('supplierName')
        }
      })
      console.log(field)
      //导出
      let rule = this.$refs.templateRef2.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}

      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 1000 },
        pageFlag: true,
        rules: rule.rules || []
      }
      this.$store.commit('startLoading')
      this.$API.deliveryConfig.buyerGoodsDemandPlanInfoKtExport(params, field).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleRelease(row) {
      //下达
      let hasOne = row.some((item) => {
        return item.released === '1'
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择未下达的行操作!'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认下达选中的排期列表吗?')
        },
        success: () => {
          let ids = row.map((item) => item.goodsDemandPlanItemId)
          let params = ids
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoReleased(params).then(() => {
            this.$toast({
              content: this.$t('确认下达排期列表操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleConfirm(row) {
      //确认排期列表
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认选中的排期列表吗?')
        },
        success: () => {
          let ids = row.map((item) => item.goodsDemandPlanItemId)
          let params = ids
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoConfirm(params).then(() => {
            this.$toast({
              content: this.$t('确认排期列表操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleRetry(row) {
      let ids = row.map((item) => item.goodsDemandPlanItemId)

      this.$API.deliverySchedule.buyerGoodsDemandPlanInfoRetry(ids).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.updateList()
      })
    },
    handleShow(selectRecords) {
      this.$store.commit('startLoading')
      this.$API.drawingTogether
        .getItemParentDosageApi({ itemCode: selectRecords[0].itemCode })
        .then((res) => {
          if (res.code == 200) {
            this.$dialog({
              modal: () => import('../components2/printSeachDialog.vue'),
              data: {
                tableData: res.data
              },
              success: () => {}
            })
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    generateUUID() {
      var d = new Date().getTime()
      if (window.performance && typeof window.performance.now === 'function') {
        d += performance.now() //use high-precision timer if available
      }
      var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (d + Math.random() * 16) % 16 | 0
        d = Math.floor(d / 16)
        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16)
      })
      return uuid
    },
    judgeValid1(params) {
      //添加校验
      let flag = true
      if (params.buyerNum === '') {
        this.$toast({
          content: this.$t('P（需求量）必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.timeInfoTimestamp) {
        this.$toast({
          content: this.$t('需求日期必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.deliveryMethod) {
        this.$toast({
          content: this.$t('配送方式必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (params.deliveryMethod === '0') {
        //配送方式为直送
        if (!params.processorCode || !params.processorName) {
          this.$toast({
            content: this.$t('配送方式为直送时,加工商编码和名称必填'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      if (params.outsourcedType === '2') {
        //委外方式非委外
        if (params.deliveryMethod === '0') {
          this.$toast({
            content: this.$t('委外方式为非委外时,配送方式需为非直送'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      // 当 "批次来料" 为 是, 配送方式为 非直送 时，工单号必输
      if (
        [1, '1'].includes(params.isItemBatch) &&
        [1, '1'].includes(params.deliveryMethod) &&
        !params.associatedNumber
      ) {
        this.$toast({
          content: this.$t('配送方式为 “非直送” 且 是否批次来料为 "是" 时, 关联工单号必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (
        ['E'].includes(params.domesticDemandFlag) &&
        (!params.domesticDemandCode || !params.saleOrder || !params.saleOrderRowCode)
      ) {
        this.$toast({
          content: this.$t('内需跟单要求“内需单号”、“销售订单”和“销售订单行”不能为空，请填写！'),
          type: 'warning'
        })
        flag = false
        return
      }
      return flag
    },
    editRow(row, index) {
      console.log(row, '编辑行数据')
      let params = {
        id: row.id,
        outsourcedType: row.outsourcedType,
        buyerOrgCode: row.buyerOrgCode, //采购组code
        buyerOrgId: row.buyerOrgId, //采购组id
        buyerOrgName: row.buyerOrgName, //采购组name
        workCenter: row.workCenter, //工作中心
        workCenterCode: row.workCenterCode, //工作中心
        workCenterName: row.workCenterName, //工作中心
        warehouseKeeper: row.warehouseKeeper, //仓管员

        companyCode: row.companyCode, //公司code
        companyId: row.companyId, //公司id
        companyName: row.companyName, //公司name

        goodsDemandPlanItemId: row.goodsDemandPlanItemId,
        siteCode: row.siteCode, //工厂code
        siteId: row.siteId, //工厂id
        siteName: row.siteName, //工厂name
        supplierCode: row.supplierCode, //供应商
        supplierTenantId: row.supplierTenantId,
        supplierId: row.supplierId, //供应商id
        supplierName: row.supplierName, //供应商名称
        itemCode: row.itemCode,
        itemName: row.itemName,
        // itemDescription: row.itemDescription,
        deliveryMethod: row.deliveryMethod, //配送方式
        associatedNumber: row.associatedNumber, //关联工单号
        domesticDemandFlag: row.domesticDemandFlag, // 是否内需跟单
        domesticDemandCode: row.domesticDemandCode, // 内需单号
        saleOrder: row.saleOrder, //销售订单
        saleOrderRowCode: row.saleOrderRowCode, //销售订单行
        bom: row.bom, //BOM
        projectTextBatch: row.projectTextBatch, //项目文本批次
        scheduleType: row.scheduleType, // jit 物料
        jit: row.scheduleType === 'JIT' ? 1 : 0,
        released: row.released, //下达
        timeInfoTimestamp: row.timeInfoTimestamp.getTime() || 0,
        buyerNum: row.self49, //p字段
        processorCode: row.processorCode, //加工商编码
        processorId: row.processorId, //加工商id
        processorName: row.processorName, //加工商名称
        warehouseCode: row.warehouseCode, //库存地点编码
        warehouseId: row.warehouseId, //库存地点id
        warehouseName: row.warehouseName, //库存地点名称
        address: row.address,
        addressId: row.addressId, //地址id
        receiveAddressName: row.receiveAddressName,
        receiveAddressCode: row.receiveAddressCode,
        buyerRemark: row.buyerRemark, // 采购备注
        isItemBatch: row.isItemBatch,
        item: [
          {
            timeInfoTimestamp: row.timeInfoTimestamp.getTime() || 0,
            total: 0,
            buyerNum: row.self49,
            buyerRemark: row.buyerRemark, // 采购备注
            released: 0, //下达
            limitNum: 0, //限量数量           //需要给默认值
            remainingNum: 0, //剩余可创建数量        //需要给默认值
            outstandingNum: 0, // 未清订单数量
            jit: row.scheduleType === 'JIT' ? 1 : 0
          }
        ]
      }
      let flag = this.judgeValid1(params)
      if (!flag) {
        this.startEdit(index)
        return
      }
      this.$API.deliverySchedule
        .buyerGoodsDemandPlanInfoUpdate({ ...params, sourceFrom: 'KT' })
        .then(() => {
          this.$toast({
            content: this.$t('编辑要货排期操作成功'),
            type: 'success'
          })
          let rule = this.$refs.templateRef2.getCurrentUsefulRef().pluginRef.queryBuilderRules || {
            rules: []
          }
          if (rule.rules.length === 0) {
            this.$set(this.componentConfig2[0].grid.asyncConfig, 'rules', [
              {
                label: '状态',
                field: 'status',
                type: 'string',
                operator: 'in',
                value: ['1']
              }
            ])
          }
          this.updateList()
        })
        .catch(() => {
          this.startEdit(index)
        })
    },
    handleCopy(row, type) {
      row.forEach((e) => {
        e.id = null
        e.serialNumber = null
        e.goodsDemandPlanItemId = null
        e.timeInfoTimestamp = Number(e.timeInfoTimestamp)
        e.sourceFrom = 'KT'
        e.item = [
          {
            timeInfoTimestamp: Number(e.timeInfoTimestamp),
            total: 0,
            buyerNum: e.buyerNum,
            buyerRemark: e.buyerRemark, // 采购备注
            released: 0, //下达
            limitNum: 0, //限量数量           //需要给默认值
            remainingNum: 0, //剩余可创建数量        //需要给默认值
            outstandingNum: 0, // 未清订单数量
            jit: e.jit
          }
        ]
      })
      console.log(row)
      this.$API.deliverySchedule.buyerGoodsDemandPlanInfoSaveBatch(row).then(() => {
        type === '1'
          ? this.$toast({
              content: this.$t('保存要货排期操作成功'),
              type: 'success'
            })
          : this.$toast({
              content: this.$t('复制要货排期操作成功'),
              type: 'success'
            })
        this.updateList()
        this.$refs.importDialog.showStepSecond()
        this.$refs.importDialog.handleClose()
      })
      if (type === '1') {
        let rule = this.$refs.templateRef2.getCurrentUsefulRef().pluginRef.queryBuilderRules || {
          rules: []
        }
        console.log(rule)
        if (rule.rules.length === 0) {
          this.scheduleType = false

          this.componentConfig2[0].grid.asyncConfig.rules.push({
            field: 'batchCode',
            operator: 'equal',

            value: this.batchCode
          })
          setTimeout(() => {
            this.scheduleType = true
          }, 1)
          console.log(this.componentConfig2[0])
        }
      }
    },
    // 跳转到大数据平台
    intoPlanPush() {
      if (location.href.includes('eads')) {
        window.open('http://bitest.tclking.com/tcloms/to/*********')
      } else {
        window.open('https://data.tcl.com/tcloms/to/ME13993')
      }
    },
    handleDelete(row) {
      //删除
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认删除选中的要货排期吗?')
        },
        success: () => {
          let ids = row.map((item) => item.goodsDemandPlanItemId)
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoDelete(ids).then(() => {
            this.$toast({
              content: this.$t('删除要货排期操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    updateList() {
      setTimeout(() => {
        document.querySelector('.e-content').scrollTop = 0
        this.$refs.templateRef2.refreshCurrentGridData()
      }, 1000)
    },
    selectedChanged2(val) {
      Object.assign(this.selectedOtherInfo, val.itemInfo)
      console.log(this.selectedOtherInfo, '最新的额外数据')
    },
    selectedChanged3(val) {
      Object.assign(this.selectedOtherInfo3, val.itemInfo)
      console.log(this.selectedOtherInfo3, '最新的额外数据')
    },
    actionComplete2(args) {
      console.log(args, '我是actionComplete')
      const { rowIndex, index } = args
      if (args.requestType === 'beginEdit' || args.requestType === 'add') {
        this.isEdit = '1'
      }
      if (
        args.requestType === 'refresh' ||
        args.requestType === 'save' ||
        args.requestType === 'delete'
      ) {
        this.isEdit = '2'
      }
      if (args.requestType === 'save' && args.action === 'edit') {
        //编辑完成
        let row = this.getRow()

        this.$refs.templateRef2.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
        if (row.isEntry === '2') {
          //新增错误重新编辑
          this.addRow(row, rowIndex ?? index)
        }
        if (row.isEntry === '1') {
          this.editRow(row, rowIndex ?? index)
        }
      }
      if (args.requestType === 'save' && args.action === 'add') {
        //新增完成
        let row = this.getRow()
        this.addRow(row, index ?? rowIndex)
        this.$refs.templateRef2.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
      }
    },
    actionComplete3(args) {
      console.log(args, '我是actionComplete')
      if (args.requestType === 'beginEdit' || args.requestType === 'add') {
        this.isEdit = '1'
      }
      if (
        args.requestType === 'refresh' ||
        args.requestType === 'save' ||
        args.requestType === 'delete'
      ) {
        this.isEdit = '2'
      }
      if (args.requestType === 'save' && args.action === 'edit') {
        //   //编辑完成
        let row = this.getRow2()
        console.log(row)
        let obj = cloneDeep(this.componentConfig3[0].grid.dataSource)
        this.componentConfig3[0].grid.dataSource.forEach((item, index) => {
          if (item.addId === row.addId) {
            obj[index] = row
          }
        })
        this.$refs.templateRef3.refreshCurrentGridData()
        this.$set(this.componentConfig3[0].grid, 'dataSource', obj)
        console.log(this.componentConfig3[0].grid.dataSource)
        //   this.$refs.templateRef2
        //     .getCurrentUsefulRef()
        //     .gridRef.ejsRef.clearSelection();
        //   if (row.isEntry === "2") {
        //     //新增错误重新编辑
        //     this.addRow(row, rowIndex);
        //   }
        //   if (row.isEntry === "1") {
        //     this.editRow(row, rowIndex);
        //   }
      }
      // if (args.requestType === "save" && args.action === "add") {
      //   //新增完成
      //   let row = this.getRow();
      //   this.addRow(row, index);
      //   this.$refs.templateRef2
      //     .getCurrentUsefulRef()
      //     .gridRef.ejsRef.clearSelection();
      // }
    },
    startEdit(index) {
      this.$refs.templateRef2.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
      this.$refs.templateRef2.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
    },
    endEdit() {
      //让组件失去焦点
      this.$refs.templateRef2.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
    },
    addRow(row, index) {
      if (row?.timeInfoTimestamp?.length == 0) {
        row.timeInfoTimestamp = this.getToday()
      }
      let params = {
        receiveAddressName: row.receiveAddressName,
        receiveAddressCode: row.receiveAddressCode,
        address: row.address,
        addressId: row.addressId, //地址id
        associatedNumber: row.associatedNumber, //关联工单号
        domesticDemandFlag: row.domesticDemandFlag, // 是否内需跟单
        domesticDemandCode: row.domesticDemandCode, // 内需单号
        bom: row.bom, //BOM
        buyerOrder: row.buyerOrder, //采购订单号
        buyerOrderRowCode: row.buyerOrderRowCode, //采购订单行号
        buyerOrgCode: row.buyerOrgCode, //采购组code
        buyerOrgId: row.buyerOrgId, //采购组id
        buyerOrgName: row.buyerOrgName, //采购组name
        checkDate: 0, //确认日期         //需要给默认值
        companyCode: row.companyCode, //公司code
        companyId: row.companyId, //公司id
        companyName: row.companyName, //公司name
        deliveryStatus: 0, //发货状态            //需要给默认值
        itemCode: row.itemCode, //物料code
        itemId: row.itemId, //物料id
        itemName: row.itemName, //物料name
        // itemDescription: row.itemDescription,
        itemGroupName: row.itemGroupName, //物料组名称
        itemGroupCode: row.itemGroupCode, //物料组code
        outsourcedType: row.outsourcedType, //委外方式
        planGroupCode: row.planGroupCode, //计划组code
        planGroupId: row.planGroupId, //计划组id
        planGroupName: row.planGroupName, //计划组name
        processName: row.processName, //工序名称
        productCode: row.productCode, //产品代码
        projectTextBatch: row.projectTextBatch, //项目文本批次
        saleOrder: row.saleOrder, //销售订单
        saleOrderRowCode: row.saleOrderRowCode, //销售订单行
        scheduleArea: row.scheduleArea, //计划区域
        scheduleType: row.scheduleType, //计划类型
        jit: row.scheduleType === 'JIT' ? 1 : 0,

        // serialNumber: this.generateUUID(), //序列号          //需要给默认值
        siteCode: row.siteCode, //工厂code
        siteId: row.siteId, //工厂id
        siteName: row.siteName, //工厂name
        status: '', //状态                           //需要给默认值
        supplierCheckUser: '', //供应商确认人       //需要给默认值
        supplierCode: row.supplierCode, //供应商
        supplierTenantId: row.supplierTenantId,
        supplierId: row.supplierId, //供应商id
        supplierName: row.supplierName, //供应商名称
        supplierRemark: '', //供应商备注         //需要给默认值
        systemRemark: '', //系统备注                 //需要给默认值
        version: '', //版本号                      //需要给默认值
        warehouseCode: row.warehouseCode, //库存地点编码
        warehouseId: row.warehouseId, //库存地点id
        warehouseName: row.warehouseName, //库存地点名称
        warehouseKeeper: row.warehouseKeeper, //仓管员
        warehouseQty: 0, //库存数量           //需要给默认值
        workCenter: row.workCenter, //工作中心
        workCenterCode: row.workCenterCode, //工作中心
        workCenterName: row.workCenterName, //工作中心

        deliveryMethod: row.deliveryMethod, //配送方式

        processorCode: row.processorCode, //加工商编码
        processorId: row.processorId, //加工商id
        processorName: row.processorName, //加工商名称
        isItemBatch: row.isItemBatch,
        item: [
          {
            timeInfoTimestamp: row.timeInfoTimestamp.getTime() || 0,
            total: 0,
            buyerNum: row.self49,
            buyerRemark: row.buyerRemark, // 采购备注
            released: 0, //下达
            limitNum: 0, //限量数量           //需要给默认值
            remainingNum: 0, //剩余可创建数量        //需要给默认值
            outstandingNum: 0, // 未清订单数量
            jit: row.scheduleType === 'JIT' ? 1 : 0
          }
        ]
      }
      setTimeout(() => {
        let flag = this.judgeValid(params)
        if (!flag) {
          this.startEdit(index)
          return
        }
        this.$API.deliverySchedule
          .buyerGoodsDemandPlanInfoSave({ ...params, sourceFrom: 'KT' })
          .then(() => {
            this.$toast({
              content: this.$t('新增要货排期操作成功'),
              type: 'success'
            })
            let rule = this.$refs.templateRef2.getCurrentUsefulRef().pluginRef
              .queryBuilderRules || {
              rules: []
            }
            if (rule.rules.length === 0) {
              this.$set(this.componentConfig2[0].grid.asyncConfig, 'rules', [
                {
                  label: '状态',
                  field: 'status',
                  type: 'string',
                  operator: 'in',
                  value: ['0']
                }
              ])
            }
            this.updateList()
          })
          .catch(() => {
            this.startEdit(index)
          })
      }, 1)
    },
    judgeValid(params) {
      console.log(params)
      //添加校验
      let flag = true
      if (!params.itemCode) {
        this.$toast({
          content: this.$t('物料信息必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      // 当 "批次来料" 为 是, 配送方式为 非直送 时，工单号必输
      if (
        [1, '1'].includes(params.isItemBatch) &&
        [1, '1'].includes(params.deliveryMethod) &&
        !params.associatedNumber
      ) {
        this.$toast({
          content: this.$t('配送方式为 “非直送” 且 是否批次来料为 "是" 时, 关联工单号必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (
        ['E'].includes(params.domesticDemandFlag) &&
        (!params.domesticDemandCode || !params.saleOrder || !params.saleOrderRowCode)
      ) {
        this.$toast({
          content: this.$t('内需跟单要求“内需单号”、“销售订单”和“销售订单行”不能为空，请填写！'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (
        params.deliveryMethod === '1' &&
        params.outsourcedType === '2' &&
        (params.warehouseCode === null || params.warehouseCode === '')
      ) {
        this.$toast({
          content: this.$t('库存地点必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.supplierCode) {
        this.$toast({
          content: this.$t('供应商信息必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.siteCode) {
        this.$toast({
          content: this.$t('工厂必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      // if (!params.companyCode) {
      //   this.$toast({
      //     content: this.$t("公司必填"),
      //     type: "warning",
      //   });
      //   flag = false;
      //   return;
      // }
      // if (!params.scheduleType) {
      //   this.$toast({
      //     content: this.$t("JIT物料必填"),
      //     type: "warning",
      //   });
      //   flag = false;
      //   return;
      // }
      if (params.item[0].buyerNum === '') {
        this.$toast({
          content: this.$t('P（需求量）必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.item[0].timeInfoTimestamp) {
        this.$toast({
          content: this.$t('需求日期必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.deliveryMethod) {
        this.$toast({
          content: this.$t('配送方式必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      // if (!params.addressId) {
      //   this.$toast({
      //     content: this.$t("送货联系人电话地址必填"),
      //     type: "warning",
      //   });
      //   flag = false;
      //   return;
      // }
      if (params.deliveryMethod === '0') {
        //配送方式为直送
        if (!params.processorCode || !params.processorName) {
          this.$toast({
            content: this.$t('配送方式为直送时,加工商编码和名称必填'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      if (params.outsourcedType === '2') {
        //委外方式非委外
        if (params.deliveryMethod === '0') {
          this.$toast({
            content: this.$t('委外方式为非委外时,配送方式需为非直送'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      return flag
    },
    getRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.templateRef2
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = cloneDeep(this.selectedOtherInfo)
      let info = {}
      currentRecords.some((item) => {
        if (item.addId == this.nowEditRowFlag) {
          Object.assign(item, row)
          info = item
        }
      })
      return info
    },
    getRow2() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.templateRef3
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = cloneDeep(this.selectedOtherInfo3)
      let info = {}
      currentRecords.some((item) => {
        if (item.addId == this.nowEditRowFlag) {
          Object.assign(item, row)
          info = item
        }
      })
      return info
    },
    actionBegin2(args) {
      console.log(args, '我是actionBegin')
      if (args.requestType === 'add') {
        this.selectedOtherInfo = {}

        let lastColumn2 = cloneDeep(this.lastColumn2)
        lastColumn2.forEach((item) => {
          args.data[item.field] = ''
          args.data.isEntry = '2'

          args.data.scheduleType = null

          if (item.field === 'status') {
            args.data[item.field] = this.$t('新建')
          }
          if (item.field === 'deliveryStatus') {
            args.data[item.field] = this.$t('未发货')
          }
          if (item.field === 'self5') {
            args.data[item.field] = this.$t('未收货')
          }
          if (item.field === 'limitNum') {
            args.data[item.field] = '0'
          }
          if (item.field === 'remainingNum') {
            args.data[item.field] = '0'
          }
          if (item.field === 'warehouseQty') {
            args.data[item.field] = '0'
          }
          if (item.field === 'timeInfoTimestamp') {
            args.data[item.field] = this.getToday()
            args.rowData[item.field] = this.getToday()
          }
          if (item.field === 'scheduleType') {
            args.data[item.field] = '非JIT'
          }
          if (item.field === 'outsourcedType') {
            args.data[item.field] = '2'
          }
          if (item.field === 'deliveryMethod') {
            args.data[item.field] = '1'
          }
          args.data.isEntry = '2'
        })
        args.data.addId = this.addId++
        this.nowEditRowFlag = args.data.addId
      }
      if (args.requestType == 'beginEdit') {
        this.beginData = args.rowData
        this.selectedOtherInfo = {}
        this.nowEditRowFlag = args.rowData.addId
        if (!args?.rowData?.noPromiseCount) {
          args.rowData.noPromiseCount = 0
        }
        if (this.isDetail === '1') {
          args.cancel = true
          return
        }
        if (
          !(
            args.rowData.status == 0 ||
            args.rowData.status == 1 ||
            args.rowData.status == 4 ||
            args.rowData.isEntry == '2'
          )
        ) {
          this.$toast({
            content: this.$t('此状态不可编辑'),
            type: 'warning'
          })
          args.cancel = true
        }
      }
      if (args.requestType === 'save' && args.action === 'add') {
        args.data.isEntry = '2'
        args.data.scheduleType = null

        args.data.addId = this.addId++
        this.nowEditRowFlag = args.data.addId
      }
      if (args.requestType === 'save') {
        if (args.data.itemCode === null && this.beginData !== null) {
          args.data.itemCode = this.beginData.itemCode
        }
      }
    },
    actionBegin3(args) {
      console.log(args, '我是actionBegin')
      // if (args.requestType === "add") {
      //   let lastColumn2 = cloneDeep(this.lastColumn2);
      //   lastColumn2.forEach((item) => {
      //     args.data[item.field] = "";
      //     if (item.field === "status") {
      //       args.data[item.field] = this.$t("新建");
      //     }
      //     if (item.field === "deliveryStatus") {
      //       args.data[item.field] = this.$t("未发货");
      //     }
      //     if (item.field === "self5") {
      //       args.data[item.field] = this.$t("未收货");
      //     }
      //     if (item.field === "limitNum") {
      //       args.data[item.field] = "0";
      //     }
      //     if (item.field === "remainingNum") {
      //       args.data[item.field] = "0";
      //     }
      //     if (item.field === "warehouseQty") {
      //       args.data[item.field] = "0";
      //     }
      //     if (item.field === "timeInfoTimestamp") {
      //       args.data[item.field] = this.getToday();
      //       args.rowData[item.field] = this.getToday();
      //     }
      //     if (item.field === "scheduleType") {
      //       args.data[item.field] = "非JIT";
      //     }
      //     if (item.field === "outsourcedType") {
      //       args.data[item.field] = "2";
      //     }
      //     if (item.field === "deliveryMethod") {
      //       args.data[item.field] = "1";
      //     }
      //     args.data.isEntry = "2";
      //   });
      //   args.data.addId = this.addId++;
      //   this.nowEditRowFlag = args.data.addId;
      // }
      if (args.requestType == 'save') {
        this.nowEditRowFlag = args.rowData.addId
        // if (!args?.rowData?.noPromiseCount) {
        //   args.rowData.noPromiseCount = 0;
        // }

        // if (this.isDetail === "1") {
        //   args.cancel = true;
        //   return;
        // }
      }
    },
    handleClickCellTitle2(e) {
      if (e.field === 'printMaterialFileName') {
        this.$API.drawingTogether
          .getFileUrlApi({
            id: e.data.printMaterialFileId
          })
          .then((res) => {
            if (res.code === 200) {
              window.open(res.data?.fileUrl)
            }
          })
      }
    },
    //点击表格的操作按钮
    handleClickCellTool2(e) {
      console.log('方法2', e)
      if (e.tool.id === 'Publish') {
        this.handlePublish([e.data])
      }
      if (e.tool.id === 'Cancle') {
        this.handleCancle([e.data])
      }
      if (e.tool.id === 'Close') {
        this.handleClose([e.data])
      }
      if (e.tool.id === 'Confirm') {
        this.handleConfirm([e.data])
      }
    },
    //点击顶部的操作按钮
    handleClickToolBar2(e) {
      console.log('方法1', e)
      if (e.toolbar.id === 'closeEdit') {
        this.$refs.templateRef2.getCurrentUsefulRef().gridRef.ejsRef.refresh()
        // e.grid.closeEdit()
        // this.updateList()

        return
      }

      console.log(e.gridRef.getMtechGridRecords(), e.toolbar.id)
      if (e.toolbar.id === 'Add') {
        this.handleAdd()
        return
      }

      if (e.toolbar.id === 'Import') {
        // this.handleImport(true);
        this.$refs.importDialog.init({
          title: this.$t('导入')
        })
        return
      }
      if (e.toolbar.id === 'Import2') {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "@/components/uploadDialog/index.vue" */ '@/components/uploadDialog/index.vue'
            ),
          data: {
            title: this.$t('导入'),
            paramsKey: 'excel',
            importApi: this.$API.deliverySchedule.importPrintMaterialApi,
            downloadTemplateApi: this.$API.deliverySchedule.getImportPrintMaterialTemplateApi
          },
          success: () => {
            this.$refs.templateRef2.refreshCurrentGridData()
          }
        })
        return
      }
      if (e.toolbar.id === 'Export1') {
        this.handleExport()
        return
      }

      if (e.toolbar.id === 'plan_push') {
        this.intoPlanPush()
      }
      let selectRecords = e.gridRef.$refs.ejsRef.getSelectedRecords()
      if (!selectRecords.length && e.toolbar.id != 'refreshDataByLocal') {
        this.$toast({ content: this.$t('请至少选择一行'), type: 'warning' })
        return
      }
      if (selectRecords.length > 1 && e.toolbar.id === 'search') {
        this.$toast({ content: this.$t('只能查询单条数据'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Delete') {
        this.handleDelete(selectRecords)
      }
      if (e.toolbar.id === 'copy') {
        this.handleCopy(selectRecords)
      }
      if (e.toolbar.id === 'Cancle') {
        this.handleCancle(selectRecords)
      }
      if (e.toolbar.id === 'Publish') {
        this.handlePublish(selectRecords)
      }
      if (e.toolbar.id === 'Confirm') {
        this.handleConfirm(selectRecords)
      }
      if (e.toolbar.id === 'Release') {
        this.handleRelease(selectRecords)
      }
      if (e.toolbar.id === 'Close') {
        this.handleClose(selectRecords)
      }
      if (e.toolbar.id === 'retry') {
        this.handleRetry(selectRecords)
      }
      if (e.toolbar.id === 'search') {
        this.handleShow(selectRecords)
      }
    }
  }
}
</script>

<style lang="scss">
/deep/ .e-dlg-container .e-control .e-dlg-content .dialog-content {
  word-break: break-word;
}
.e-dlg-container .e-control .e-dlg-content .dialog-content {
  word-break: break-word;
}
</style>
<style scoped lang="scss">
/deep/ .e-grid .e-altrow {
  background-color: #f9f9f9;
}
/deep/ .e-grid .e-headercell {
  background-color: #e9e9e9;
}
/deep/ .quick-search .form-box {
  margin: 5px 0 0;
  border-radius: 0;
}
/deep/ .mt-data-grid {
  .e-gridcontent {
    .e-table {
      tbody {
        .e-editedrow {
          td {
            overflow: visible !important;
            form {
              .e-table {
                tbody {
                  tr:nth-child(2n-1) {
                    background: #f6f7fb;
                    td:first-child {
                      position: sticky;
                      left: 0px;
                      z-index: 1;
                      border-right: 1px solid var(--plugin-dg-shadow-color);
                      background-color: #f6f7fb;
                    }
                  }
                  // tr:nth-child(2n) {
                  //   background: #fff;
                  // }
                }
              }
            }
          }
        }
        // tr:nth-child(2n) {
        //   background: #fff;
        // }
      }
    }
  }
}
/deep/ .common-template-page {
  height: auto !important;
}
#forecast-manage-table-container {
  position: relative;

  // 表格数据视图
  /deep/ .e-gridcontent > .e-content {
    height: 0px;
    transition: height 0.5s ease;
  }

  // 分页
  /deep/ .mt-pagertemplate {
    margin: 10px 0 10px !important;
  }
}
/deep/ .frozenColumns {
  .template-wrap {
    .e-grid .e-table {
      & thead th:first-child {
        position: sticky;
        left: 0px;
        z-index: 1;
      }

      & tbody td:first-child {
        position: sticky;
        left: 0px;
        z-index: 1;
        border-right: 1px solid var(--plugin-dg-shadow-color);
        background-color: #fff;
      }
    }
  }
}
</style>
