<template>
  <div class="full-height vertical-flex-box">
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <mt-template-page
        ref="templateRef1"
        :template-config="componentConfig1"
        @handleClickToolBar="handleClickToolBar1"
        @handleClickCellTool="handleClickCellTool1"
        @actionBegin="actionBegin1"
        @actionComplete="actionComplete1"
        @selectedChanged="selectedChanged1"
      ></mt-template-page>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
    <import-dialog
      :accept="['.xls', '.xlsx']"
      :from-data-key="fromDataKey"
      :save-url="saveUrl"
      ref="importDialog"
      @import="importDialogImport"
      @uploadCompleted="uploadCompleted"
    >
      <predict-import-dialog-slot
        ref="predictImportDialogSlot"
        @submitted="predictImportDialogSlotSubmitted"
      ></predict-import-dialog-slot>
    </import-dialog>
  </div>
</template>
<script>
import { checkColumn1, lastColumn1 } from '../config/index1'
import { BASE_TENANT } from '@/utils/constant'
import { cloneDeep } from 'lodash'
import InputNumber from '../components1/InputNumber.vue'
import InputNumberView from '../components1/InputNumberView.vue'
import * as UTILS from '@/utils/utils'
var bigDecimal = require('js-big-decimal')
export default {
  components: {
    ImportDialog: () => import('@/components/Upload/importDialog'),
    PredictImportDialogSlot: () => import('../components1/predict-import-dialog-slot.vue')
  },
  data() {
    return {
      saveUrl: `${BASE_TENANT}/po/time/config/upload`, // 导入API
      fromDataKey: 'excel', // 上传组件的 name，此值为调用 api 请求参数的 key
      lastColumn1: lastColumn1,
      componentConfig1: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [
            [
              {
                id: 'Add',
                icon: 'icon_solid_Createorder',
                title: this.$t('新增')
                // permission: ["O_02_0436"],
              },
              {
                id: 'closeEdit',
                icon: 'icon_table_delete',
                // permission: ["O_02_1141"],
                title: this.$t('取消')
              },
              {
                id: 'Delete',
                icon: 'icon_solid_Delete',
                title: this.$t('删除')
                // permission: ["O_02_0437"],
              },
              {
                id: 'Publish',
                icon: 'icon_solid_Activateorder',
                title: this.$t('发布')
                // permission: ["O_02_0438"],
              },
              {
                id: 'Release',
                icon: 'icon_solid_Pauseorder',
                title: this.$t('下达')
                // permission: ["O_02_0439"],
              },
              {
                id: 'Confirm',
                icon: 'icon_solid_Pauseorder',
                title: this.$t('确认')
                // permission: ["O_02_0440"],
              },
              {
                id: 'Import',
                icon: 'icon_solid_Import',
                title: this.$t('导入')
                // permission: ["O_02_0441"],
              },
              {
                id: 'Import2',
                icon: 'icon_solid_Import',
                title: this.$t('印刷品导入')
              },
              {
                id: 'Export1',
                icon: 'icon_solid_export',
                title: this.$t('导出')
                // permission: ["O_02_0442"],
              }
            ],
            ['Filter', 'Refresh']
          ],
          // gridId: this.$tableUUID.purchaseSchedule.purchaseScheduleDayTab,
          grid: {
            allowReordering: false,
            allowPaging: false, // 不分页
            dataSource: [],
            allowEditing: true, //开启表格编辑操作
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            columnData: []
          }
        }
      ],
      currentList: [], //从接口获取到的最新的数据
      nowEditRowFlag: '', //当前编辑的行id
      addId: '1',
      isEdit: '1', //是否编辑 1是编辑 2不是编辑
      selectedOtherInfo: {},

      apiWaitingQuantity: 0, // 调用的api正在等待数
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 10, // 当前每页数据量
        totalPages: 0, // 总页数
        pageSizes: [10, 50, 100, 200]
      },
      forecastPageCurrent: 1, // 预测表格 当前页码
      forecastRules: [], // 预测表格请求规则
      forecastCondition: 'and', // 过滤-规则-关系，默认为 "and"
      tableContainerClientHeight: 0,
      titleList: []
    }
  },
  mounted() {
    // this.buyerGoodsDemandPlanInfoQuery()
    this.componentConfig1[0].grid.columnData = []
    this.componentConfig1[0].grid.columnData = this.handleColumns([])
  },
  methods: {
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.buyerGoodsDemandPlanInfoQuery()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.buyerGoodsDemandPlanInfoQuery()
    },
    // 采方-获取采方预测信息列表
    buyerGoodsDemandPlanInfoQuery() {
      // delete this.componentConfig1[0].gridId;
      this.componentConfig1 = []
      const params = {
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        },
        rules: [...this.forecastRules],
        defaultRules: [
          {
            field: 'sourceFrom',
            operator: 'equal',
            value: 'KT'
          }
        ]
      }
      // this.apiStartLoading();
      this.$API.deliverySchedule
        .buyerGoodsDemandPlanInfoQuery(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.goodsDemandPlanInfoRespIPage?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            const records = res?.data?.goodsDemandPlanInfoRespIPage?.records || [] // 表格数据
            let titleList = res?.data?.titleList || [] // 动态表头数据
            // 处理表头数据
            let columnData = this.handleColumns(titleList)
            // 处理表数据
            let dataSource = this.handleDataSource(records, titleList)
            let config = {
              useToolTemplate: false, // 不使用预置(新增、编辑、删除)
              useBaseConfig: false, // 使用组件中的toolbar配置
              toolbar: [
                [
                  {
                    id: 'Add',
                    icon: 'icon_solid_Createorder',
                    title: this.$t('新增')
                    // permission: ["O_02_0436"],
                  },
                  {
                    id: 'closeEdit',
                    icon: 'icon_table_delete',
                    // permission: ["O_02_1141"],
                    title: this.$t('取消')
                  },
                  {
                    id: 'Delete',
                    icon: 'icon_solid_Delete',
                    title: this.$t('删除')
                    // permission: ["O_02_0437"],
                  },
                  {
                    id: 'Publish',
                    icon: 'icon_solid_Activateorder',
                    title: this.$t('发布')
                    // permission: ["O_02_0438"],
                  },
                  {
                    id: 'Release',
                    icon: 'icon_solid_Pauseorder',
                    title: this.$t('下达')
                    // permission: ["O_02_0439"],
                  },
                  {
                    id: 'Confirm',
                    icon: 'icon_solid_Pauseorder',
                    title: this.$t('确认')
                    // permission: ["O_02_0440"],
                  },
                  {
                    id: 'Import',
                    icon: 'icon_solid_Import',
                    title: this.$t('导入')
                    // permission: ["O_02_0441"],
                  },
                  {
                    id: 'Export1',
                    icon: 'icon_solid_export',
                    title: this.$t('导出')
                    // permission: ["O_02_0442"],
                  }
                ],
                ['Filter', 'Refresh']
              ],
              gridId: 'fde18e09-cc45-4845-b73a-ace363a8ff2a',
              grid: {
                gridLines: 'Both',
                allowReordering: false,
                allowPaging: false, // 不分页
                dataSource: dataSource,
                allowEditing: true, //开启表格编辑操作
                editSettings: {
                  allowEditing: true,
                  allowAdding: true,
                  allowDeleting: true,
                  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
                  showConfirmDialog: false,
                  showDeleteConfirmDialog: false,
                  newRowPosition: 'Top'
                },
                columnData: columnData
              }
            }
            this.componentConfig1.push(config)
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleDataSource(records, titleList) {
      let list1 = []
      titleList.forEach((item) => {
        if (item && item.length === 13) {
          list1.push(UTILS.default.dateFormat(Number(item), 'Y-m-d'))
        }
      })
      this.titleList = list1
      records.forEach((obj) => {
        obj.addId = this.addId++
        obj.isEntry = '1' //是否是带入的数据
        let afterData = []
        let tempArr = []
        obj.limitNum = ''
        obj.remainingNum = ''
        obj.outstandingNum = ''
        obj.deliveryMethod = String(obj.deliveryMethod)
        if (obj.item.length > 0) {
          obj.buyerRemark = obj.item[0].buyerRemark
          obj.released = obj.item[0].released
          obj.supplierRemark = obj.item[0].supplierRemark
          obj.item.forEach((item1) => {
            obj.limitNum = bigDecimal.add(obj.limitNum, item1.limitNum)
            obj.remainingNum = bigDecimal.add(obj.remainingNum, item1.remainingNum)
            obj.outstandingNum = bigDecimal.add(obj.outstandingNum, item1.outstandingNum)
          })
        }
        obj.item.forEach((item1) => {
          if (item1.timeInfoTimestamp && item1.timeInfoTimestamp.length === 13) {
            item1.timeInfoTimestamp = UTILS.default.dateFormat(
              Number(item1.timeInfoTimestamp),
              'Y-m-d'
            )
          }
          console.log(item1.timeInfoTimestamp, 'a哈哈多喝水2')
          if (tempArr.indexOf(item1.timeInfoTimestamp) === -1) {
            tempArr.push(item1.timeInfoTimestamp)
            afterData.push({
              timeInfoTimestamp: item1.timeInfoTimestamp,
              origin: [item1]
            })
          } else {
            afterData.some((item2) => {
              if (item2.timeInfoTimestamp === item1.timeInfoTimestamp) {
                item2.origin.push(item1)
              }
            })
          }
        })
        afterData.forEach((item1) => {
          if (item1.origin.length === 1) {
            obj[
              item1.timeInfoTimestamp
            ] = `${item1.origin[0].total}_${item1.origin[0].buyerNum}_${item1.origin[0].supplierNum}_${item1.origin[0].gapNum}_${item1.origin[0].countGapNum}`
          }
          if (item1.origin.length > 1) {
            let total = ''
            let buyerNum = ''
            let supplierNum = ''
            let gapNum = ''
            let countGapNum = ''
            item1.origin.forEach((item2) => {
              total = bigDecimal.add(item2.total, total)
              buyerNum = bigDecimal.add(item2.buyerNum, buyerNum)
              supplierNum = bigDecimal.add(item2.supplierNum, supplierNum)
              gapNum = bigDecimal.add(item2.gapNum, gapNum)
              countGapNum = bigDecimal.add(item2.countGapNum, countGapNum)
            })
            obj[
              item1.timeInfoTimestamp
            ] = `${total}_${buyerNum}_${supplierNum}_${gapNum}_${countGapNum}`
          }
        })
        this.currentList = records
      })
      // this.componentConfig1[0].grid.dataSource = records;
      return records
    },
    handleColumns(titleList) {
      try {
        throw new Error(123)
      } catch (error) {
        console.log(error)
      }
      let lastColumn2 = []
      titleList.forEach((item) => {
        if (item && item.length === 13) {
          item = UTILS.default.dateFormat(Number(item), 'Y-m-d')
        }
        lastColumn2.push({
          field: item,
          headerText: item,
          width: '150',
          ignore: true,
          allowEditing: false,
          allowFiltering: false,
          template: () => {
            return { template: InputNumberView }
          },
          editTemplate: () => {
            return { template: InputNumber }
          }
        })
      })
      // this.componentConfig1[0].grid.columnData = checkColumn1
      //   .concat(lastColumn1)
      //   .concat(lastColumn2);
      let columnData = checkColumn1.concat(lastColumn1).concat(lastColumn2)
      return columnData
    },
    // 文件上传完成
    uploadCompleted(response) {
      this.$refs.importDialog.showStepSecond()
      this.$nextTick(() => {
        this.$refs.predictImportDialogSlot.init({
          resData: response?.data || []
        })
      })
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    },
    // 预测数据导入编辑弹框 点击导入
    importDialogImport() {
      this.$refs.predictImportDialogSlot.doImport()
    },
    // 导入提交
    predictImportDialogSlotSubmitted() {
      this.$refs.importDialog.handleClose()
      this.updateList()
    },
    handleClose(row) {
      //关闭
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认关闭选中的排期列表吗?')
        },
        success: () => {
          let items = []
          row.forEach((obj) => {
            items = items.concat(obj.item || [])
          })
          let ids = items.map((item) => item.id)
          let params = ids
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoClose(params).then(() => {
            this.$toast({
              content: this.$t('关闭排期列表操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleCancle(row) {
      //取消发布
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认取消发布选中的排期列表吗?')
        },
        success: () => {
          let items = []
          row.forEach((obj) => {
            items = items.concat(obj.item || [])
          })
          let ids = items.map((item) => item.id)
          let params = ids
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoCancel(params).then(() => {
            this.$toast({
              content: this.$t('取消发布排期列表操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handlePublish(row) {
      //发布
      let flag = true
      row.forEach((item) => {
        if (!item.addressId) {
          flag = false
        }
      })
      if (!flag) {
        this.$toast({
          content: this.$t('送货联系人电话地址填写才可以发布'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认发布选中的排期列表吗?')
        },
        success: () => {
          let items = []
          row.forEach((obj) => {
            items = items.concat(obj.item || [])
          })
          let ids = items.map((item) => item.id)
          let params = ids
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoPublish(params).then(() => {
            this.$toast({
              content: this.$t('发布排期列表操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleAdd() {
      //新增
      this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
    },
    handleImport() {
      //导入
      this.$refs.importDialog.init({
        title: this.$t('导入')
      })
    },
    handleExport() {
      //导出
      let params = {
        condition: this.forecastCondition,
        page: { current: 1, size: 1000 },
        pageFlag: true,
        rules: [...this.forecastRules]
      }
      this.$store.commit('startLoading')
      this.$API.deliverySchedule.buyerGoodsDemandPlanInfoExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleRelease(row) {
      //下达
      let hasOne = row.some((item) => {
        return item.released === '1'
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择未下达的行操作!'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认下达选中的排期列表吗?')
        },
        success: () => {
          let items = []
          row.forEach((obj) => {
            items = items.concat(obj.item || [])
          })
          let ids = items.map((item) => item.id)
          let params = ids
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoReleased(params).then(() => {
            this.$toast({
              content: this.$t('确认下达排期列表操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleConfirm(row) {
      //确认排期列表
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认选中的排期列表吗?')
        },
        success: () => {
          let items = []
          row.forEach((obj) => {
            items = items.concat(obj.item || [])
          })
          let ids = items.map((item) => item.id)
          let params = ids
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoConfirm(params).then(() => {
            this.$toast({
              content: this.$t('确认排期列表操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    generateUUID() {
      var d = new Date().getTime()
      if (window.performance && typeof window.performance.now === 'function') {
        d += performance.now() //use high-precision timer if available
      }
      var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (d + Math.random() * 16) % 16 | 0
        d = Math.floor(d / 16)
        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16)
      })
      return uuid
    },
    handleDelete(row) {
      //删除
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认删除选中的要货排期吗?')
        },
        success: () => {
          let items = []
          row.forEach((obj) => {
            items = items.concat(obj.item || [])
          })
          let ids = items.map((item) => item.id)
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoDelete(ids).then(() => {
            this.$toast({
              content: this.$t('删除要货排期操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    updateList() {
      this.$refs.templateRef1.refreshCurrentGridData()
    },
    selectedChanged1(val) {
      console.log(arguments, '-=-==')
      Object.assign(this.selectedOtherInfo, val.itemInfo)
      console.log(this.selectedOtherInfo, '最新的额外数据')
    },
    actionComplete1(args) {
      console.log(args, '我是actionComplete')
      const { rowIndex, index } = args
      if (args.requestType === 'beginEdit' || args.requestType === 'add') {
        this.isEdit = '1'
      }
      if (
        args.requestType === 'refresh' ||
        args.requestType === 'save' ||
        args.requestType === 'delete'
      ) {
        this.isEdit = '2'
      }
      if (args.requestType === 'save' && args.action === 'edit') {
        //编辑完成
        let row = this.getRow()
        this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
        if (row.isEntry === '2') {
          //新增错误重新编辑
          this.addRow(row, rowIndex)
        }
        if (row.isEntry === '1') {
          this.editRow(row, rowIndex)
        }
      }
      if (args.requestType === 'save' && args.action === 'add') {
        //新增完成
        let row = this.getRow()
        this.addRow(row, index)
        this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
      }
    },
    startEdit(index) {
      this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
      this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
    },
    endEdit() {
      //让组件失去焦点
      this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
    },
    judgeValid1(params) {
      //添加校验
      console.log('我执行了1', params)
      let flag = true
      let flag1 = true
      params.some((item) => {
        if (item.buyerNum === '') {
          flag1 = false
        }
      })
      if (!flag1) {
        this.$toast({
          content: this.$t('P（需求量）必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.length) {
        this.$toast({
          content: this.$t('需求日期必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params[0].deliveryMethod) {
        this.$toast({
          content: this.$t('配送方式必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (params[0].deliveryMethod === '0') {
        //配送方式为直送
        if (!params[0].processorCode || !params[0].processorName) {
          this.$toast({
            content: this.$t('配送方式为直送时,加工商编码和名称必填'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      if (params[0].outsourcedType === '2') {
        //委外方式非委外
        if (params[0].deliveryMethod === '0') {
          this.$toast({
            content: this.$t('委外方式为非委外时,配送方式需为非直送'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      return flag
    },
    editRow(row, index) {
      console.log(row, '编辑行数据')
      let params = []
      row.item.forEach((obj) => {
        params.push({
          id: row.id,
          outsourcedType: row.outsourcedType,
          workCenter: row.workCenter, //工作中心
          workCenterCode: row.workCenterCode, //工作中心
          workCenterName: row.workCenterName, //工作中心
          goodsDemandPlanItemId: obj.id,
          supplierCode: row.supplierCode, //供应商
          supplierId: row.supplierId, //供应商id
          supplierName: row.supplierName, //供应商名称
          deliveryMethod: row.deliveryMethod, //配送方式
          released: row.released, //下达
          associatedNumber: row.associatedNumber, //关联工单号
          domesticDemandFlag: row.domesticDemandFlag, // 是否内需跟单
          domesticDemandCode: row.domesticDemandCode, // 内需单号
          bom: row.bom, //BOM
          projectTextBatch: row.projectTextBatch, //项目文本批次
          scheduleType: row.scheduleType, //计划类型
          jit: row.scheduleType === 'JIT' ? 1 : 0,

          timeInfoTimestamp: new Date(`${obj.timeInfoTimestamp} 00:00:00`).getTime(), // 需求日期
          buyerNum: row[obj.timeInfoTimestamp]?.split('_')[1] || '', //p字段
          processorCode: row.processorCode, //加工商编码
          processorId: row.processorId, //加工商id
          processorName: row.processorName, //加工商名称
          warehouseCode: row.warehouseCode, //库存地点编码
          warehouseId: row.warehouseId, //库存地点id
          warehouseName: row.warehouseName, //库存地点名称
          addressId: row.addressId, //地址id
          receiveAddressName: row.receiveAddressName,
          receiveAddressCode: row.receiveAddressCode,
          buyerRemark: row.buyerRemark, // 采购备注
          sourceFrom: 'KT'
        })
      })
      let flag = this.judgeValid1(params)
      if (!flag) {
        this.startEdit(index)
        return
      }
      this.$API.deliverySchedule
        .buyerGoodsDemandPlanInfoUpdateList(params)
        .then(() => {
          this.$toast({
            content: this.$t('编辑要货排期操作成功'),
            type: 'success'
          })
          this.updateList()
        })
        .catch(() => {
          this.startEdit(index)
        })
    },
    async addRow(row, index) {
      console.log(row, '新增行数据')
      let params = {
        receiveAddressName: row.receiveAddressName,
        receiveAddressCode: row.receiveAddressCode,
        addressId: row.addressId, //地址id
        associatedNumber: row.associatedNumber, //关联工单号
        domesticDemandFlag: row.domesticDemandFlag, //是否内需跟单
        domesticDemandCode: row.domesticDemandCode, //内需单号
        bom: row.bom, //BOM
        buyerOrder: row.buyerOrder, //采购订单号
        buyerOrderRowCode: row.buyerOrderRowCode, //采购订单行号
        buyerOrgCode: row.buyerOrgCode, //采购组code
        buyerOrgId: row.buyerOrgId, //采购组id
        buyerOrgName: row.buyerOrgName, //采购组name
        checkDate: 0, //确认日期         //需要给默认值
        companyCode: row.companyCode, //公司code
        companyId: row.companyId, //公司id
        companyName: row.companyName, //公司name
        deliveryStatus: 0, //发货状态            //需要给默认值
        itemCode: row.itemCode, //物料code
        itemId: row.itemId, //物料id
        itemName: row.itemName, //物料name
        itemGroupName: row.itemGroupName, //物料组名称
        itemGroupCode: row.itemGroupCode, //物料组code
        outsourcedType: row.outsourcedType, //委外方式
        planGroupCode: row.planGroupCode, //计划组code
        planGroupId: row.planGroupId, //计划组id
        planGroupName: row.planGroupName, //计划组name
        processName: row.processName, //工序名称
        productCode: row.productCode, //产品代码
        projectTextBatch: row.projectTextBatch, //项目文本批次
        saleOrder: row.saleOrder, //销售订单
        saleOrderRowCode: row.saleOrderRowCode, //销售订单行
        scheduleArea: row.scheduleArea, //计划区域
        scheduleType: row.scheduleType, //计划类型
        jit: row.scheduleType === 'JIT' ? 1 : 0,

        // serialNumber: this.generateUUID(), //序列号          //需要给默认值
        siteCode: row.siteCode, //工厂code
        siteId: row.siteId, //工厂id
        siteName: row.siteName, //工厂name
        status: '', //状态                           //需要给默认值
        supplierCheckUser: '', //供应商确认人       //需要给默认值
        supplierCode: row.supplierCode, //供应商
        supplierId: row.supplierId, //供应商id
        supplierName: row.supplierName, //供应商名称
        supplierRemark: '', //供应商备注         //需要给默认值
        systemRemark: '', //系统备注                 //需要给默认值
        version: '', //版本号                      //需要给默认值
        warehouseCode: row.warehouseCode, //库存地点编码
        warehouseId: row.warehouseId, //库存地点id
        warehouseName: row.warehouseName, //库存地点名称
        warehouseKeeper: row.warehouseKeeper, //仓管员
        warehouseQty: 0, //库存数量           //需要给默认值
        workCenter: row.workCenter, //工作中心
        workCenterCode: row.workCenterCode, //工作中心
        workCenterName: row.workCenterName, //工作中心
        itemDescription: row.itemDescription, //物料描述
        deliveryMethod: row.deliveryMethod, //配送方式
        processorCode: row.processorCode, //加工商编码
        processorId: row.processorId, //加工商id
        processorName: row.processorName //加工商名称
      }
      let list = []
      // console.log(this.)
      this.titleList.forEach((item) => {
        list.push({
          timeInfoTimestamp: new Date(`${item} 00:00:00`).getTime(),
          total: 0,
          buyerNum: row[item]?.split('_')[1] || '',
          buyerRemark: row.buyerRemark, // 采购备注
          released: 0, //下达
          limitNum: 0, //限量数量           //需要给默认值
          remainingNum: 0, //剩余可创建数量        //需要给默认值
          outstandingNum: 0 // 未清订单数量
        })
      })
      params.item = list
      console.log('我是谁', this.titleList, list)
      let flag = await this.judgeValid(params, list)
      if (!flag) {
        this.startEdit(index)
        return
      }
      this.$API.deliverySchedule
        .buyerGoodsDemandPlanInfoSave({ ...params, sourceFrom: 'KT' })
        .then(() => {
          this.$toast({
            content: this.$t('新增要货排期操作成功'),
            type: 'success'
          })
          this.updateList()
        })
        .catch(() => {
          this.startEdit(index)
        })
    },
    async judgeValid(params, list) {
      //添加校验
      let flag = true
      if (!params.itemCode) {
        this.$toast({
          content: this.$t('物料信息必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.supplierCode) {
        this.$toast({
          content: this.$t('供应商信息必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.siteCode) {
        this.$toast({
          content: this.$t('工厂必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.companyCode) {
        this.$toast({
          content: this.$t('公司必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.scheduleType) {
        this.$toast({
          content: this.$t('JIT物料必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      let falg1 = true
      await list.forEach((item) => {
        if (item.buyerNum === '') {
          falg1 = false
        }
      })
      if (!falg1) {
        this.$toast({
          content: this.$t('P（需求量）必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.deliveryMethod) {
        this.$toast({
          content: this.$t('配送方式必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (params.deliveryMethod === '0') {
        //配送方式为直送
        if (!params.processorCode || !params.processorName) {
          this.$toast({
            content: this.$t('配送方式为直送时,加工商编码和名称必填'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      if (params.outsourcedType === '2') {
        //委外方式非委外
        if (params.deliveryMethod === '0') {
          this.$toast({
            content: this.$t('委外方式为非委外时,配送方式需为非直送'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      if (
        [1, '1'].includes(params.domesticDemandFlag) &&
        (!params.domesticDemandCode || !params.saleOrderRowCode)
      ) {
        this.$toast({
          content: this.$t('是否内需跟单为“是”时, 内需单号和销售订单行不能为空'),
          type: 'warning'
        })
        flag = false
        return
      }
      return flag
    },
    getRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.templateRef1
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = cloneDeep(this.selectedOtherInfo)
      let info = {}
      currentRecords.some((item) => {
        if (item.addId == this.nowEditRowFlag) {
          Object.assign(item, row)
          info = item
        }
      })
      return info
    },
    actionBegin1(args) {
      console.log(args, '我是111actionBegin')
      if (args.requestType === 'add') {
        let lastColumn1 = cloneDeep(this.lastColumn1)
        lastColumn1.forEach((item) => {
          args.data[item.field] = ''
          if (item.field === 'status') {
            args.data[item.field] = this.$t('新建')
          }
          if (item.field === 'limitNum') {
            args.data[item.field] = '0'
          }
          if (item.field === 'remainingNum') {
            args.data[item.field] = '0'
          }
          if (item.field === 'warehouseQty') {
            args.data[item.field] = '0'
          }
          if (item.field === 'timeInfoTimestamp') {
            args.data[item.field] = new Date()
          }
          if (item.field === 'scheduleType') {
            args.data[item.field] = '非JIT'
          }
          if (item.field === 'outsourcedType') {
            args.data[item.field] = '2'
          }
          if (item.field === 'deliveryMethod') {
            args.data[item.field] = '1'
          }
          args.data.isEntry = '2'
        })
        args.data.addId = this.addId++
        this.nowEditRowFlag = args.data.addId
      }
      if (args.requestType == 'beginEdit') {
        this.nowEditRowFlag = args.rowData.addId
        if (
          !(
            args.rowData.status == 0 ||
            args.rowData.status == 1 ||
            args.rowData.status == 4 ||
            args.rowData.isEntry == '2'
          )
        ) {
          this.$toast({
            content: this.$t('此状态不可编辑'),
            type: 'warning'
          })
          args.cancel = true
        }
      }
    },
    //点击表格的操作按钮
    handleClickCellTool1(e) {
      console.log('方法2', e)
      if (e.tool.id === 'Publish') {
        this.handlePublish([e.data])
      }
      if (e.tool.id === 'Cancle') {
        this.handleCancle([e.data])
      }
      if (e.tool.id === 'Close') {
        this.handleClose([e.data])
      }
      if (e.tool.id === 'Confirm') {
        this.handleConfirm([e.data])
      }
    },
    //点击顶部的操作按钮
    handleClickToolBar1(e) {
      console.log('方法1', e)
      console.log(e.grid.getSelectedRecords(), e.toolbar.id)
      const commonToolbar = [
        'ForecastUpdate',
        'ForecastAdd',
        'ForecastImport',
        'Filter',
        'Refresh',
        'refreshDataByLocal',
        'filterDataByLocal',
        'resetDataByLocal',
        'ForecastExport',
        'Setting'
      ]
      if (e.toolbar.id === 'closeEdit') {
        e.grid.closeEdit()
        return
      }
      if (e.toolbar.id === 'Add') {
        this.handleAdd()
        return
      }
      if (e.toolbar.id === 'Import') {
        this.handleImport()
        return
      }
      if (e.toolbar.id === 'Export1') {
        this.handleExport()
        return
      }
      if (e.toolbar.id === 'refreshDataByLocal') {
        this.buyerGoodsDemandPlanInfoQuery()
        return
      }
      if (e.toolbar.id === 'filterDataByLocal') {
        // 筛选-过滤
        const { condition, rules: ruleList } = e.rules
        this.forecastCondition = condition
        this.forecastRules = ruleList
        // 采方-获取采方预测信息列表
        this.buyerGoodsDemandPlanInfoQuery()
        return
      }
      if (e.toolbar.id === 'resetDataByLocal') {
        // 筛选重置
        this.forecastCondition = 'and'
        this.forecastRules = []
        // 采方-获取采方预测信息列表
        this.buyerGoodsDemandPlanInfoQuery()
        return
      }
      let selectRecords = e.grid.getSelectedRecords()
      if (!selectRecords.length && !commonToolbar.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请至少选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Delete') {
        this.handleDelete(selectRecords)
      }
      if (e.toolbar.id === 'Publish') {
        this.handlePublish(selectRecords)
      }
      if (e.toolbar.id === 'Confirm') {
        this.handleConfirm(selectRecords)
      }
      if (e.toolbar.id === 'Release') {
        this.handleRelease(selectRecords)
      }
    },
    selectCell() {
      console.log(arguments, 9999)
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .e-grid .e-altrow {
  background-color: #e9e9e9;
}
</style>
