import UTILS from '../../../../utils/utils'
import Input from '../components2/Input.vue'
import Vue from 'vue'
import InputView from '../components2/InputView.vue'
import inputViewInfo from '../components2/inputViewInfo.vue'
import Select from '../components2/Select.vue'
import DatePicker from '../components2/DatePicker.vue'
import InputNumber from '../components2/InputNumber.vue'
import InputNumberView from '../components2/InputNumberView.vue'
import ItemCode from '../components2/ItemCode.vue'
import editView from '../components1/editView.vue'
import editDateView from '../components1/editDateView.vue'
import { MasterDataSelect } from '@/utils/constant'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

export const timeDate = (dataKey, hasTime) => {
  const template = () => {
    return {
      template: Vue.component('date', {
        template: `<div><div>{{data[dataKey] | dateFormat}}</div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div></div>`,
        data: function () {
          return { data: {}, dataKey, hasTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
import { i18n } from '@/main.js'
export const checkColumn2 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false,
    allowEditing: false
  }
]

export const importColumn2 = [
  {
    field: 'ImportType',
    headerText: i18n.t('导入状态'),
    width: '150',
    allowEditing: false
  }
]

export const lastColumn2 = [
  {
    field: 'addId',
    headerText: 'addId',
    width: 0,
    allowEditing: false,
    visible: true,
    ignore: true
  },
  {
    field: 'id',
    headerText: 'id',
    isPrimaryKey: true,

    width: 0,
    allowEditing: false,
    visible: false
  },
  {
    field: 'numBer',
    headerText: i18n.t('序号'),
    width: '100',
    ignore: true,
    allowEditing: false
  },
  // {
  //   field: "version",
  //   headerText: i18n.t("版本号"),
  //   width: "150",
  //   valueConverter: {
  //     type: "function",
  //     filter: (e) => {
  //       return "V" + e;
  //     },
  //   },
  //   allowEditing: false,
  // },
  {
    field: 'deliveryMethod',
    headerText: i18n.t('配送方式'),
    selectOptions: [
      { label: i18n.t('直送'), value: '0' },
      { label: i18n.t('非直送'), value: '1' }
    ],
    selectOptions2: [
      { label: i18n.t('直送'), value: '0' }
      // { label: i18n.t("非直送"), value: "1" },
    ],
    width: '120',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    editTemplate: () => {
      return { template: Select }
    },
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('直送'), 1: i18n.t('非直送') }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('配送方式')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'outsourcedType',
    headerText: i18n.t('委外方式'),
    selectOptions: [
      { label: i18n.t('销售委外'), value: '1' },
      { label: i18n.t('标准委外'), value: '0' },
      { label: i18n.t('标准采购'), value: '2' }
    ],
    width: '100',
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('销售委外'),
        2: i18n.t('标准采购'),
        0: i18n.t('标准委外')
      }
    }
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '105',
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('新建'),
        1: i18n.t('新建-修改'),
        2: i18n.t('已发布'),
        3: i18n.t('反馈正常'),
        4: i18n.t('反馈异常'),
        5: i18n.t('已确认'),
        6: i18n.t('已关闭'),
        7: i18n.t('同步中')
      }
    },
    options: [
      { text: i18n.t('新建'), value: '0' },
      { text: i18n.t('新建-修改'), value: '1' },
      { text: i18n.t('已发布'), value: '2' },
      { text: i18n.t('反馈正常'), value: '3' },
      { text: i18n.t('反馈异常'), value: '4' },
      { text: i18n.t('已确认'), value: '5' },
      { text: i18n.t('已关闭'), value: '6' },
      { text: i18n.t('同步中'), value: '7' }
    ],
    editTemplate: () => {
      return { template: editView }
    },
    cellTools: [
      {
        // permission: ["O_02_0446"],
        id: 'Publish',
        icon: 'icon_solid_pushorder',
        title: i18n.t('发布'),
        visibleCondition: (data) => {
          return data.status === 0 || data.status === 1 || data.status === 4
        }
      },
      {
        // permission: ["O_02_0693"],
        id: 'Cancle',
        icon: 'icon_solid_pushorder',
        title: i18n.t('取消发布'),
        visibleCondition: (data) => {
          return data.status === 2
        }
      },
      {
        // permission: ["O_02_0451"],
        id: 'Close',
        icon: 'icon_solid_pushorder',
        title: i18n.t('关闭'),
        visibleCondition: (data) => {
          return data.status === 2 || data.status === 3 || data.status === 4 || data.status === 5
        }
      },
      {
        // permission: ["O_02_0448"],
        id: 'Confirm',
        icon: 'icon_solid_pushorder',
        title: i18n.t('确认'),
        visibleCondition: (data) => {
          return data.status === 4
        }
      }
    ],
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    }
  },
  // 会引起错位 ，暂时注释
  // {
  //   allowFiltering: false,
  //   ignore: true,
  //   field: "itemDescription",
  //   // headerText: i18n.t("物料描述"),
  //   width: 0,
  //   // editTemplate: () => {
  //   //   return { template: InputView };
  //   // },
  //   // headerTemplate: () => {
  //   //   return {
  //   //     template: Vue.component("headers", {
  //   //       template: `
  //   //           <div class="headers">
  //   //             <span style="color: red">*</span>
  //   //             <span class="e-headertext">{{$t('物料描述')}}</span>
  //   //           </div>
  //   //         `,
  //   //     }),
  //   //   };
  //   // },
  // },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    selectOptions: [],
    width: '130',
    searchOptions: {
      ...MasterDataSelect.factoryAddress,
      renameField: 'siteCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('工厂')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.siteCode}}-{{data.siteName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组'),
    width: '130',
    searchOptions: {
      ...MasterDataSelect.businessGroupIn,
      renameField: 'buyerOrgCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    selectOptions: [],
    width: '125',
    searchOptions: {
      operator: 'likeright',
      maxQueryValueLength: 20000
    },
    // searchOptions: {
    //   ...MasterDataSelect.material,
    //   renameField: "itemCode",
    // },
    editTemplate: () => {
      return { template: ItemCode }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料编号')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    selectOptions: [],
    ignore: true,
    width: '150',
    editTemplate: () => {
      return { template: InputView }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料名称')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'isItemBatch',
    selectOptions: [
      { label: i18n.t('是'), value: 1 },
      { label: i18n.t('否'), value: 0 }
    ],
    headerText: i18n.t('是否批次来料'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否') }
    },
    allowEditing: false,
    width: '120',
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'associatedNumber',
    headerText: i18n.t('关联工单号'),
    maxlength: '100000',
    editTemplate: () => {
      return { template: Input }
    },
    searchOptions: {
      maxQueryValueLength: 100000,
      placeholder: i18n.t('支持粘贴多个精准及单个右模糊查询')
    },
    width: '160'
  },
  {
    field: 'domesticDemandFlag',
    headerText: i18n.t('是否内需跟单'),
    width: '160',
    valueConverter: {
      type: 'map',
      map: { E: i18n.t('是'), F: i18n.t('否') }
    },
    editTemplate: () => {
      return {
        template: Vue.component('domesticDemandFlag', {
          template: `
            <mt-select
              :id="data.column.field"
              v-model="data[data.column.field]"
              :data-source="options"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
            ></mt-select>
            `,
          data() {
            return {
              options: [
                { text: i18n.t('是'), value: 'E' },
                { text: i18n.t('否'), value: 'F' }
              ]
            }
          }
        })
      }
    }
  },
  {
    field: 'domesticDemandCode',
    headerText: i18n.t('内需单号'),
    width: '150'
  },
  {
    field: 'saleOrder',
    headerText: i18n.t('销售订单'),
    width: '150'
  },
  {
    field: 'saleOrderRowCode',
    headerText: i18n.t('销售订单行'),
    width: '150'
  },
  {
    field: 'printMaterialSerialNumber',
    headerText: i18n.t('客户序列流水号/变量'),
    width: '150',
    allowEditing: false,
    allowFiltering: false,
    ignore: true
  },
  {
    field: 'printMaterialFileName',
    headerText: i18n.t('流水号附件'),
    width: '150',
    cellTools: [],
    allowEditing: false,
    allowFiltering: false,
    ignore: true
  },
  {
    field: 'self48',
    headerText: i18n.t('D（原始需求）'),
    width: '145',
    allowEditing: false,
    allowFiltering: false,
    ignore: true
  },
  {
    field: 'self49',
    headerText: i18n.t('P（需求量）'),
    width: '165',
    allowFiltering: false,
    ignore: true,
    editTemplate: () => {
      return { template: InputNumber }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('P（需求量）')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    allowFiltering: false,
    ignore: true,
    field: 'self50',
    headerText: i18n.t('C（承诺量）'),
    width: '145',
    allowEditing: false
  },
  {
    allowFiltering: false,
    ignore: true,
    field: 'self51',
    headerText: i18n.t('Gap（差额）'),
    width: '140',
    allowEditing: false,
    template: () => {
      return { template: InputNumberView }
    }
  },
  {
    allowFiltering: false,
    ignore: true,
    field: 'noPromiseCount',
    headerText: i18n.t('未承诺数量'),
    width: '140',
    allowEditing: false
    // editTemplate: () => {
    //   return { template: InputView };
    // },
  },
  {
    field: 'remainingNum',
    headerText: i18n.t('剩余可创建数量'),
    type: 'number',
    width: '140',
    allowEditing: false
  },
  {
    field: 'outstandingNum',
    headerText: i18n.t('未清订单数量'),
    width: '140',
    allowEditing: false,
    searchOptions: { elementType: 'number' }
  },
  {
    field: 'haveDeliveryNum',
    headerText: i18n.t('已打单数量'),
    width: '130',
    allowEditing: false,
    searchOptions: { elementType: 'number' }
  },
  {
    field: 'timeInfoTimestamp',
    headerText: i18n.t('需求日期'),
    allowEditing: false,
    searchOptions: {
      ...MasterDataSelect.timeRange,
      renameField: 'timeInfoTimestamp'
    },
    template: timeDate('timeInfoTimestamp', false),
    width: '90',
    editTemplate: () => {
      return { template: DatePicker }
    },

    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('需求日期')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    selectOptions: [],
    width: '180',
    searchOptions: {
      ...MasterDataSelect.supplier,
      renameField: 'supplierCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('供应商')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.supplierCode}}-{{data.supplierName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'warehouseCode',
    headerText: i18n.t('库存地点编号+名称'),
    selectOptions: [],
    width: '180',
    searchOptions: {
      ...MasterDataSelect.stockAddress,
      renameField: 'warehouseName',
      fields: { text: 'locationCode', value: 'locationName' }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('库存地点编号+名称')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.warehouseCode}}-{{data.warehouseName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'stockType',
    headerText: i18n.t('库存类型'),
    width: '100',
    valueConverter: {
      type: 'map',
      map: {
        STOCK: i18n.t('STOCK'),
        OPSTK: i18n.t('OPSTK')
      }
    },
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'processorCode',
    headerText: i18n.t('加工商'),
    selectOptions: [],
    width: '130',
    searchOptions: {
      ...MasterDataSelect.supplier,
      renameField: 'processorCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.processorCode}}-{{data.processorName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'warehouseKeeper',
    headerText: i18n.t('仓管员'),
    width: '85',
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    width: '80',
    allowEditing: false,
    allowFiltering: false
    // ignore: true,
  },
  {
    field: 'systemRemark',
    headerText: i18n.t('系统备注'),
    width: '95',
    allowEditing: false
  },
  {
    field: 'buyerRemark',
    headerText: i18n.t('采购方备注'),
    width: '90',
    maxlength: 200,
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'supplierRemark',
    headerText: i18n.t('供应商备注'),
    width: '90',
    allowEditing: false
  },
  {
    field: 'serialNumber',
    headerText: i18n.t('序列号'),
    width: '155',
    allowEditing: false
  },
  {
    field: 'scheduleType',
    headerText: i18n.t('是否JIT'),
    selectOptions: [
      { label: i18n.t('是'), value: 'JIT' },
      { label: i18n.t('否'), value: i18n.t('非JIT') }
    ],
    searchOptions: {
      elementType: 'select',
      dataSource: [
        { label: i18n.t('否'), value: '0' },
        { label: i18n.t('是'), value: '1' }
      ],
      fields: { text: 'label', value: 'value' },

      renameField: 'jit'
    },
    // valueConverter: {
    //   type: "map",
    //   map: {
    //     非JIT: i18n.t("否"),
    //     JIT: i18n.t("是"),
    //   },
    // },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{Type(data.jit)}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {},
          methods: {
            Type(e) {
              if (e === 1) {
                return i18n.t('是')
              } else if (e === 0) {
                return i18n.t('否')
              }
            }
          }
        })
      }
    },
    width: '80',
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('是否JIT')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'syncSapFlag',
    headerText: i18n.t('SAP同步状态'),
    width: '100',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未同步'),
        200: i18n.t('已同步'),
        500: i18n.t('同步失败')
      }
    },
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'sapMessage',
    headerText: i18n.t('导入sap消息'),
    width: '95',
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'address',
    headerText: i18n.t('收货信息'),
    width: '125',
    selectOptions: [],
    editTemplate: () => {
      return { template: Select }
    },
    allowFiltering: false,
    ignore: true,
    template: () => ({ template: inputViewInfo })
    // headerTemplate: () => {
    //   return {
    //     template: Vue.component("headers", {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('收货信息')}}</span>
    //           </div>
    //         `,
    //     }),
    //   };
    // },
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    width: '80',
    searchOptions: {
      ...MasterDataSelect.timeRange,
      renameField: 'createTime'
    },
    template: timeDate('createTime', true),
    allowEditing: false,
    allowFiltering: false
    // ignore: true,
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('最后更新人'),
    width: '80',
    allowEditing: false,
    allowFiltering: false
    // ignore: true,
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    width: '80',
    searchOptions: {
      ...MasterDataSelect.timeRange,
      renameField: 'updateTime'
    },
    template: timeDate('updateTime', true),
    allowEditing: false,
    allowFiltering: false
    // ignore: true,
  },
  {
    field: 'shippingSpace',
    headerText: i18n.t('发料仓位'),
    width: '95',
    allowEditing: false
  },
  {
    field: 'supplierCheckUser',
    headerText: i18n.t('供应商确认人'),
    width: '125',
    allowEditing: false,
    allowFiltering: false,
    ignore: true
  },
  {
    allowFiltering: false,
    ignore: true,
    field: 'checkDate',
    headerText: i18n.t('确认日期'),
    width: '90',
    allowEditing: false,
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    editTemplate: () => {
      return { template: editDateView }
    }
  },
  {
    field: 'minPacketCount',
    headerText: i18n.t('最小包装量'),
    width: '100',
    // valueConverter: {
    //   type: "map",
    //   map: {
    //     0: i18n.t("未同步"),
    //     200: i18n.t("已同步"),
    //     500: i18n.t("同步失败"),
    //   },
    // },
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'projectTextBatch',
    headerText: i18n.t('项目文本批次'),
    width: '120',
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'publishMan',
    headerText: i18n.t('发布人'),
    width: '120',
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'publishTime',
    headerText: i18n.t('发布时间'),
    width: '120',
    maxlength: '40',
    editTemplate: () => {
      return { template: editDateView }
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    searchOptions: { ...MasterDataSelect.timeRange }
  }
  // {
  //   field: "scheduleArea",
  //   headerText: i18n.t("计划区域"),
  //   width: "150",
  //   maxlength: "40",
  //   editTemplate: () => {
  //     return { template: Input };
  //   },
  // },
  // {
  //   field: "supplierName",
  //   headerText: i18n.t("供应商名称"),
  //   width: "200",
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  //   headerTemplate: () => {
  //     return {
  //       template: Vue.component("headers", {
  //         template: `
  //             <div class="headers">
  //               <span style="color: red">*</span>
  //               <span class="e-headertext">{{$t('供应商名称')}}</span>
  //             </div>
  //           `,
  //       }),
  //     };
  //   },
  // },
  // {
  //   field: "bom",
  //   headerText: i18n.t("BOM号"),
  //   width: "150",
  //   maxlength: "40",
  //   editTemplate: () => {
  //     return { template: Input };
  //   },
  // },
  // {
  //   field: "saleOrder",
  //   headerText: i18n.t("销售订单号"),
  //   width: "150",
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
  // {
  //   ignore: true,
  //   allowFiltering: false,
  //   field: "saleOrderRowCode",
  //   headerText: i18n.t("销售订单行号"),
  //   width: 0,
  //   editTemplate: () => {
  //     return { template: Input };
  //   },
  // },
  // {
  //   field: "workCenterName",
  //   headerText: i18n.t("工作中心"),
  //   selectOptions: [],
  //   searchOptions: {
  //     ...MasterDataSelect.workCenter,
  //     renameField: "workCenterCode",
  //   },
  //   editTemplate: () => {
  //     return { template: Select };
  //   },
  //   width: "250",
  //   template: () => {
  //     return {
  //       template: Vue.component("headers", {
  //         template: `
  //           <div class="headers">
  //             <span>{{data.workCenterCode}}-{{data.workCenterName}}</span>
  //           </div>
  //         `,
  //         data() {
  //           return {
  //             data: {},
  //           };
  //         },
  //       }),
  //     };
  //   },
  // },
  // {
  //   field: "processName",
  //   headerText: i18n.t("工序名称"),
  //   width: 0,
  //   ignore: true,
  //   allowFiltering: false,
  //   editTemplate: () => {
  //     return { template: Input };
  //   },
  // },
  // {
  //   field: "productCode",
  //   headerText: i18n.t("产品代码"),
  //   width: 0,
  //   allowFiltering: false,
  //   ignore: true,
  //   editTemplate: () => {
  //     return { template: Input };
  //   },
  // },
  // {
  //   field: "buyerOrder",
  //   headerText: i18n.t("采购订单"),
  //   selectOptions: [],
  //   width: "200",
  //   editTemplate: () => {
  //     return { template: Select };
  //   },
  // },
  // {
  //   field: "buyerOrderRowCode",
  //   headerText: i18n.t("采购订单行"),
  //   width: "150",
  //   maxlength: "240",
  //   editTemplate: () => {
  //     return { template: Input };
  //   },
  // },
  // {
  //   field: "warehouseQty",
  //   headerText: i18n.t("库存量"),
  //   width: "150",
  //   allowEditing: false,
  // },
  // {
  //   field: "released",
  //   headerText: i18n.t("下达"),
  //   allowEditing: false,
  //   allowFiltering: false,
  //   ignore: true,
  //   // selectOptions: [
  //   //   { label: i18n.t("未下达"), value: "0" },
  //   //   { label: i18n.t("已下达"), value: "1" },
  //   // ],
  //   width: "150",
  //   // editTemplate: () => {
  //   //   return { template: Select };
  //   // },
  //   valueConverter: {
  //     type: "map",
  //     map: { 0: i18n.t("未下达"), 1: i18n.t("已下达") },
  //   },
  //   options: [
  //     { text: i18n.t("未下达"), value: "0" },
  //     { text: i18n.t("已下达"), value: "1" },
  //   ],
  //   editTemplate: () => {
  //     return { template: editView };
  //   },
  // },
  // {
  //   field: "limitNum",
  //   headerText: i18n.t("限量数量"),
  //   width: "150",
  //   allowEditing: false,
  // },
  // {
  //   field: "processorName",
  //   headerText: i18n.t("加工商名称"),
  //   width: "150",
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
  // {
  //   field: "warehouseName",
  //   headerText: i18n.t("库存地点名称"),
  //   width: 0,
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
]
export const lastColumn3 = [
  // {
  //   field: "addId",
  //   headerText: "addId",
  //   width: 0,
  //   allowEditing: false,
  //   visible: false,
  // },
  {
    field: 'errorMsg',
    headerText: i18n.t('错误原因'),
    width: '200',
    allowEditing: false,
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span style="color: red" class="e-headertext">{{$t('错误原因')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">{{data.errorMsg}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  // {
  //   field: "version",
  //   headerText: i18n.t("版本号"),
  //   width: "150",
  //   valueConverter: {
  //     type: "function",
  //     filter: (e) => {
  //       return "V" + e;
  //     },
  //   },
  //   allowEditing: false,
  // },
  {
    field: 'deliveryMethod',
    headerText: i18n.t('配送方式'),
    selectOptions: [
      { label: i18n.t('直送'), value: '0' },
      { label: i18n.t('非直送'), value: '1' }
    ],
    selectOptions2: [
      { label: i18n.t('直送'), value: '0' }
      // { label: i18n.t("非直送"), value: "1" },
    ],
    width: '120',

    editTemplate: () => {
      return { template: Select }
    },
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('直送'), 1: i18n.t('非直送') }
    },
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('配送方式')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'outsourcedType',
    headerText: i18n.t('委外方式'),
    selectOptions: [
      { label: i18n.t('销售委外'), value: '1' },
      { label: i18n.t('标准委外'), value: '0' },
      { label: i18n.t('标准采购'), value: '2' }
    ],
    width: '100',
    editTemplate: () => {
      return { template: Select }
    },
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('销售委外'),
        2: i18n.t('标准采购'),
        0: i18n.t('标准委外')
      }
    }
  },
  // {
  //   field: "status",
  //   headerText: i18n.t("状态"),
  //   width: "105",
  //   allowEditing: false,
  //   valueConverter: {
  //     type: "map",
  //     map: {
  //       0: i18n.t("新建"),
  //       1: i18n.t("新建-修改"),
  //       2: i18n.t("已发布"),
  //       3: i18n.t("反馈正常"),
  //       4: i18n.t("反馈异常"),
  //       5: i18n.t("已确认"),
  //       6: i18n.t("已关闭"),
  //       7: i18n.t("同步中"),
  //     },
  //   },
  //   options: [
  //     { text: i18n.t("新建"), value: "0" },
  //     { text: "新建-修改", value: "1" },
  //     { text: i18n.t("已发布"), value: "2" },
  //     { text: i18n.t("反馈正常"), value: "3" },
  //     { text: i18n.t("反馈异常"), value: "4" },
  //     { text: i18n.t("已确认"), value: "5" },
  //     { text: i18n.t("已关闭"), value: "6" },
  //     { text: i18n.t("同步中"), value: "7" },
  //   ],
  //   editTemplate: () => {
  //     return { template: editView };
  //   },
  //   cellTools: [
  //     {
  //       // permission: ["O_02_0446"],
  //       id: "Publish",
  //       icon: "icon_solid_pushorder",
  //       title: i18n.t("发布"),
  //       visibleCondition: (data) => {
  //         return data.status === 0 || data.status === 1 || data.status === 4;
  //       },
  //     },
  //     {
  //       // permission: ["O_02_0693"],
  //       id: "Cancle",
  //       icon: "icon_solid_pushorder",
  //       title: i18n.t("取消发布"),
  //       visibleCondition: (data) => {
  //         return data.status === 2;
  //       },
  //     },
  //     {
  //       // permission: ["O_02_0451"],
  //       id: "Close",
  //       icon: "icon_solid_pushorder",
  //       title: i18n.t("关闭"),
  //       visibleCondition: (data) => {
  //         return (
  //           data.status === 2 ||
  //           data.status === 3 ||
  //           data.status === 4 ||
  //           data.status === 5
  //         );
  //       },
  //     },
  //     {
  //       // permission: ["O_02_0448"],
  //       id: "Confirm",
  //       icon: "icon_solid_pushorder",
  //       title: i18n.t("确认"),
  //       visibleCondition: (data) => {
  //         return data.status === 4;
  //       },
  //     },
  //   ],
  //   searchOptions: {
  //     elementType: "multi-select",
  //     operator: "in",
  //   },
  // },
  // 会引起错位 ，暂时注释
  // {
  //   allowFiltering: false,
  //   ignore: true,
  //   field: "itemDescription",
  //   // headerText: i18n.t("物料描述"),
  //   width: 0,
  //   // editTemplate: () => {
  //   //   return { template: InputView };
  //   // },
  //   // headerTemplate: () => {
  //   //   return {
  //   //     template: Vue.component("headers", {
  //   //       template: `
  //   //           <div class="headers">
  //   //             <span style="color: red">*</span>
  //   //             <span class="e-headertext">{{$t('物料描述')}}</span>
  //   //           </div>
  //   //         `,
  //   //     }),
  //   //   };
  //   // },
  // },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    selectOptions: [],
    width: '130',
    searchOptions: {
      ...MasterDataSelect.factoryAddress,
      renameField: 'siteCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('工厂')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.siteCode}}-{{data.siteName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组'),
    width: '130',
    searchOptions: {
      ...MasterDataSelect.businessGroupIn,
      renameField: 'buyerOrgCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    width: '125',
    // searchOptions: {
    //   ...MasterDataSelect.material,
    //   renameField: "itemCode",
    // },
    editTemplate: () => {
      return { template: ItemCode }
    },
    searchOptions: {
      operator: 'likeright'
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料编号')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    selectOptions: [],
    ignore: true,
    width: '150',
    editTemplate: () => {
      return { template: InputView }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料名称')}}</span>
              </div>
            `
        })
      }
    }
  },
  // {
  //   field: "self48",
  //   headerText: i18n.t("D（原始需求）"),
  //   width: "145",
  //   allowEditing: false,
  //   allowFiltering: false,
  //   ignore: true,
  // },
  {
    field: 'buyerNum',
    headerText: i18n.t('P（需求量）'),
    width: '165',
    allowFiltering: false,
    ignore: true,
    editTemplate: () => {
      return { template: InputNumber }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('P（需求量）')}}</span>
              </div>
            `
        })
      }
    }
  },
  // {
  //   allowFiltering: false,
  //   ignore: true,
  //   field: "self50",
  //   headerText: i18n.t("C（承诺量）"),
  //   width: "145",
  //   allowEditing: false,
  // },
  // {
  //   allowFiltering: false,
  //   ignore: true,
  //   field: "self51",
  //   headerText: i18n.t("Gap（差额）"),
  //   width: "140",
  //   allowEditing: false,
  //   template: () => {
  //     return { template: InputNumberView };
  //   },
  // },
  // {
  //   allowFiltering: false,
  //   ignore: true,
  //   field: "noPromiseCount",
  //   headerText: i18n.t("未承诺数量"),
  //   width: "140",
  //   allowEditing: false,
  //   // editTemplate: () => {
  //   //   return { template: InputView };
  //   // },
  // },
  // {
  //   field: "remainingNum",
  //   headerText: i18n.t("剩余可创建数量"),
  //   type: "number",
  //   width: "140",
  //   allowEditing: false,
  // },
  // {
  //   field: "outstandingNum",
  //   headerText: i18n.t("未清订单数量"),
  //   width: "140",
  //   allowEditing: false,
  //   searchOptions: { elementType: "number" },
  // },
  // {
  //   field: "haveDeliveryNum",
  //   headerText: i18n.t("已打单数量"),
  //   width: "130",
  //   allowEditing: false,
  //   searchOptions: { elementType: "number" },
  // },
  {
    field: 'timeInfoTimestamp',
    headerText: i18n.t('需求日期'),
    allowEditing: false,
    searchOptions: {
      ...MasterDataSelect.timeRange,
      renameField: 'timeInfoTimestamp'
    },
    template: timeDate('timeInfoTimestamp', false),
    width: '90',
    editTemplate: () => {
      return { template: DatePicker }
    },

    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('需求日期')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    selectOptions: [],
    width: '180',
    searchOptions: {
      ...MasterDataSelect.supplier,
      renameField: 'supplierCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('供应商')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.supplierCode}}-{{data.supplierName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'warehouseCode',
    headerText: i18n.t('库存地点编号+名称'),
    selectOptions: [],
    width: '180',
    searchOptions: {
      ...MasterDataSelect.stockAddress,
      renameField: 'warehouseName',
      fields: { text: 'locationCode', value: 'locationName' }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('库存地点编号+名称')}}</span>
              </div>
            `
        })
      }
    },
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.warehouseCode}}-{{data.warehouseName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'stockType',
    headerText: i18n.t('库存类型'),
    width: '100',
    // valueConverter: {
    //   type: "map",
    //   map: {
    //     0: i18n.t("未同步"),
    //     200: i18n.t("已同步"),
    //     500: i18n.t("同步失败"),
    //   },
    // },
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'processorCode',
    headerText: i18n.t('加工商'),
    selectOptions: [],
    width: '130',
    searchOptions: {
      ...MasterDataSelect.supplier,
      renameField: 'processorCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.processorCode}}-{{data.processorName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'warehouseKeeper',
    headerText: i18n.t('仓管员'),
    width: '85',
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    width: '80',
    allowEditing: false,
    allowFiltering: false
    // ignore: true,
  },
  // {
  //   field: "systemRemark",
  //   headerText: i18n.t("系统备注"),
  //   width: "95",
  //   allowEditing: false,
  // },
  {
    field: 'buyerRemark',
    headerText: i18n.t('采购方备注'),
    width: '90',
    maxlength: 200,
    editTemplate: () => {
      return { template: Input }
    }
  }
  // {
  //   field: "supplierRemark",
  //   headerText: i18n.t("供应商备注"),
  //   width: "90",
  //   allowEditing: false,
  // },
  // {
  //   field: "serialNumber",
  //   headerText: i18n.t("序列号"),
  //   width: "155",
  //   allowEditing: false,
  // },
  // {
  //   field: "scheduleType",
  //   headerText: i18n.t("是否JIT"),
  //   selectOptions: [
  //     { label: i18n.t("是"), value: "JIT" },
  //     { label: i18n.t("否"), value: i18n.t("非JIT") },
  //   ],
  //   searchOptions: {
  //     elementType: "select",
  //     dataSource: [
  //       { label: i18n.t("否"), value: "0" },
  //       { label: i18n.t("是"), value: "1" },
  //     ],
  //     fields: { text: "label", value: "value" },

  //     renameField: "jit",
  //   },
  //   // valueConverter: {
  //   //   type: "map",
  //   //   map: {
  //   //     非JIT: i18n.t("否"),
  //   //     JIT: i18n.t("是"),
  //   //   },
  //   // },
  //   template: () => {
  //     return {
  //       template: Vue.component("headers", {
  //         template: `
  //             <div class="headers">
  //               <span>{{Type(data.jit)}}</span>
  //             </div>
  //           `,
  //         data() {
  //           return {
  //             data: {},
  //           };
  //         },
  //         mounted() {},
  //         methods: {
  //           Type(e) {
  //             if (e === 1) {
  //               return i18n.t("是");
  //             } else if (e === 0) {
  //               return i18n.t("否");
  //             }
  //           },
  //         },
  //       }),
  //     };
  //   },
  //   width: "80",
  //   editTemplate: () => {
  //     return { template: Select };
  //   },
  //   headerTemplate: () => {
  //     return {
  //       template: Vue.component("headers", {
  //         template: `
  //             <div class="headers">
  //               <span style="color: red">*</span>
  //               <span class="e-headertext">{{$t('是否JIT')}}</span>
  //             </div>
  //           `,
  //       }),
  //     };
  //   },
  // },
  // {
  //   field: "syncSapFlag",
  //   headerText: i18n.t("SAP同步状态"),
  //   width: "100",
  //   valueConverter: {
  //     type: "map",
  //     map: {
  //       0: i18n.t("未同步"),
  //       200: i18n.t("已同步"),
  //       500: i18n.t("同步失败"),
  //     },
  //   },
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
  // {
  //   field: "sapMessage",
  //   headerText: i18n.t("导入sap消息"),
  //   width: "95",
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
  // {
  //   field: "address",
  //   headerText: i18n.t("收货信息"),
  //   width: "125",
  //   selectOptions: [],
  //   editTemplate: () => {
  //     return { template: Select };
  //   },
  //   allowFiltering: false,
  //   ignore: true,
  //   // headerTemplate: () => {
  //   //   return {
  //   //     template: Vue.component("headers", {
  //   //       template: `
  //   //           <div class="headers">
  //   //             <span style="color: red">*</span>
  //   //             <span class="e-headertext">{{$t('收货信息')}}</span>
  //   //           </div>
  //   //         `,
  //   //     }),
  //   //   };
  //   // },
  // },
  // {
  //   field: "createTime",
  //   headerText: i18n.t("创建时间"),
  //   width: "80",
  //   searchOptions: {
  //     ...MasterDataSelect.timeRange,
  //     renameField: "createTime",
  //   },
  //   template: timeDate("createTime", true),
  //   allowEditing: false,
  //   allowFiltering: false,
  //   // ignore: true,
  // },
  // {
  //   field: "updateUserName",
  //   headerText: i18n.t("最后更新人"),
  //   width: "80",
  //   allowEditing: false,
  //   allowFiltering: false,
  //   // ignore: true,
  // },
  // {
  //   field: "updateTime",
  //   headerText: i18n.t("更新时间"),
  //   width: "80",
  //   searchOptions: {
  //     ...MasterDataSelect.timeRange,
  //     renameField: "updateTime",
  //   },
  //   template: timeDate("updateTime", true),
  //   allowEditing: false,
  //   allowFiltering: false,
  //   // ignore: true,
  // },
  // {
  //   field: "shippingSpace",
  //   headerText: i18n.t("发料仓位"),
  //   width: "95",
  //   allowEditing: false,
  // },
  // {
  //   field: "supplierCheckUser",
  //   headerText: i18n.t("供应商确认人"),
  //   width: "125",
  //   allowEditing: false,
  //   allowFiltering: false,
  //   ignore: true,
  // },
  // {
  //   allowFiltering: false,
  //   ignore: true,
  //   field: "checkDate",
  //   headerText: i18n.t("确认日期"),
  //   width: "90",
  //   allowEditing: false,
  //   valueConverter: {
  //     type: "function",
  //     filter: (e) => {
  //       if (e && e.length === 13) {
  //         e = Number(e);
  //         return UTILS.dateFormat(e, "Y-m-d");
  //       } else {
  //         return "";
  //       }
  //     },
  //   },
  //   editTemplate: () => {
  //     return { template: editDateView };
  //   },
  // },
  // {
  //   field: "minPacketCount",
  //   headerText: i18n.t("最小包装量"),
  //   width: "100",
  //   // valueConverter: {
  //   //   type: "map",
  //   //   map: {
  //   //     0: i18n.t("未同步"),
  //   //     200: i18n.t("已同步"),
  //   //     500: i18n.t("同步失败"),
  //   //   },
  //   // },
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
  // {
  //   field: "projectTextBatch",
  //   headerText: i18n.t("项目文本批次"),
  //   width: "120",
  //   maxlength: "40",
  //   editTemplate: () => {
  //     return { template: Input };
  //   },
  // },
  // {
  //   field: "publishMan",
  //   headerText: i18n.t("发布人"),
  //   width: "120",
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
  // {
  //   field: "publishTime",
  //   headerText: i18n.t("发布时间"),
  //   width: "120",
  //   maxlength: "40",
  //   editTemplate: () => {
  //     return { template: editDateView };
  //   },
  //   valueConverter: {
  //     type: "function",
  //     filter: (e) => {
  //       if (e && e.length === 13) {
  //         e = Number(e);
  //         return UTILS.dateFormat(e, "Y-m-d");
  //       } else {
  //         return "";
  //       }
  //     },
  //   },
  //   searchOptions: { ...MasterDataSelect.timeRange },
  // },
  // {
  //   field: "scheduleArea",
  //   headerText: i18n.t("计划区域"),
  //   width: "150",
  //   maxlength: "40",
  //   editTemplate: () => {
  //     return { template: Input };
  //   },
  // },
  // {
  //   field: "supplierName",
  //   headerText: i18n.t("供应商名称"),
  //   width: "200",
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  //   headerTemplate: () => {
  //     return {
  //       template: Vue.component("headers", {
  //         template: `
  //             <div class="headers">
  //               <span style="color: red">*</span>
  //               <span class="e-headertext">{{$t('供应商名称')}}</span>
  //             </div>
  //           `,
  //       }),
  //     };
  //   },
  // },
  // {
  //   field: "bom",
  //   headerText: i18n.t("BOM号"),
  //   width: "150",
  //   maxlength: "40",
  //   editTemplate: () => {
  //     return { template: Input };
  //   },
  // },
  // {
  //   field: "saleOrder",
  //   headerText: i18n.t("销售订单号"),
  //   width: "150",
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
  // {
  //   ignore: true,
  //   allowFiltering: false,
  //   field: "saleOrderRowCode",
  //   headerText: i18n.t("销售订单行号"),
  //   width: 0,
  //   editTemplate: () => {
  //     return { template: Input };
  //   },
  // },
  // {
  //   field: "workCenterName",
  //   headerText: i18n.t("工作中心"),
  //   selectOptions: [],
  //   searchOptions: {
  //     ...MasterDataSelect.workCenter,
  //     renameField: "workCenterCode",
  //   },
  //   editTemplate: () => {
  //     return { template: Select };
  //   },
  //   width: "250",
  //   template: () => {
  //     return {
  //       template: Vue.component("headers", {
  //         template: `
  //           <div class="headers">
  //             <span>{{data.workCenterCode}}-{{data.workCenterName}}</span>
  //           </div>
  //         `,
  //         data() {
  //           return {
  //             data: {},
  //           };
  //         },
  //       }),
  //     };
  //   },
  // },
  // {
  //   field: "processName",
  //   headerText: i18n.t("工序名称"),
  //   width: 0,
  //   ignore: true,
  //   allowFiltering: false,
  //   editTemplate: () => {
  //     return { template: Input };
  //   },
  // },
  // {
  //   field: "productCode",
  //   headerText: i18n.t("产品代码"),
  //   width: 0,
  //   allowFiltering: false,
  //   ignore: true,
  //   editTemplate: () => {
  //     return { template: Input };
  //   },
  // },
  // {
  //   field: "buyerOrder",
  //   headerText: i18n.t("采购订单"),
  //   selectOptions: [],
  //   width: "200",
  //   editTemplate: () => {
  //     return { template: Select };
  //   },
  // },
  // {
  //   field: "buyerOrderRowCode",
  //   headerText: i18n.t("采购订单行"),
  //   width: "150",
  //   maxlength: "240",
  //   editTemplate: () => {
  //     return { template: Input };
  //   },
  // },
  // {
  //   field: "warehouseQty",
  //   headerText: i18n.t("库存量"),
  //   width: "150",
  //   allowEditing: false,
  // },
  // {
  //   field: "released",
  //   headerText: i18n.t("下达"),
  //   allowEditing: false,
  //   allowFiltering: false,
  //   ignore: true,
  //   // selectOptions: [
  //   //   { label: i18n.t("未下达"), value: "0" },
  //   //   { label: i18n.t("已下达"), value: "1" },
  //   // ],
  //   width: "150",
  //   // editTemplate: () => {
  //   //   return { template: Select };
  //   // },
  //   valueConverter: {
  //     type: "map",
  //     map: { 0: i18n.t("未下达"), 1: i18n.t("已下达") },
  //   },
  //   options: [
  //     { text: i18n.t("未下达"), value: "0" },
  //     { text: i18n.t("已下达"), value: "1" },
  //   ],
  //   editTemplate: () => {
  //     return { template: editView };
  //   },
  // },
  // {
  //   field: "limitNum",
  //   headerText: i18n.t("限量数量"),
  //   width: "150",
  //   allowEditing: false,
  // },
  // {
  //   field: "processorName",
  //   headerText: i18n.t("加工商名称"),
  //   width: "150",
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
  // {
  //   field: "warehouseName",
  //   headerText: i18n.t("库存地点名称"),
  //   width: 0,
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
]
