import UTILS from '../../../../utils/utils'
import Input from '../components1/Input.vue'
import InputView from '../components1/InputView.vue'
import Select from '../components2/Select.vue'
import selfType from '../components1/selfType.vue'
import ItemCode from '../components1/ItemCode.vue'
import editView from '../components1/editView.vue'
import editDateView from '../components1/editDateView.vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'

import Vue from 'vue'
export const checkColumn1 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false,
    allowEditing: false
  }
]

export const lastColumn1 = [
  {
    field: 'addId',
    headerText: 'addId',
    width: 0,
    visible: false,
    allowEditing: false
  },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false
  },
  // {
  //   field: "version",
  //   headerText: i18n.t("版本号"),
  //   width: "150",
  //   valueConverter: {
  //     type: "function",
  //     filter: (e) => {
  //       return "V" + e;
  //     },
  //   },
  //   allowEditing: false,
  // },
  // {
  //   field: "serialNumber",
  //   headerText: i18n.t("序列号"),
  //   width: "150",
  //   allowEditing: false,
  // },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '85',
    allowEditing: false,
    options: [
      { text: i18n.t('新建'), value: '0' },
      { text: i18n.t('新建-修改'), value: '1' },
      { text: i18n.t('已发布'), value: '2' },
      { text: i18n.t('反馈正常'), value: '3' },
      { text: i18n.t('反馈异常'), value: '4' },
      { text: i18n.t('已确认'), value: '5' },
      { text: i18n.t('已关闭'), value: '6' },
      { text: i18n.t('同步中'), value: '7' }
    ],
    editTemplate: () => {
      return { template: editView }
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('新建'),
        1: i18n.t('新建-修改'),
        2: i18n.t('已发布'),
        3: i18n.t('反馈正常'),
        4: i18n.t('反馈异常'),
        5: i18n.t('已确认'),
        6: i18n.t('已关闭'),
        7: i18n.t('同步中')
      }
    },
    cellTools: [
      {
        permission: ['O_02_0438'],
        id: 'Publish',
        icon: 'icon_solid_pushorder',
        title: i18n.t('发布'),
        visibleCondition: (data) => {
          return data.status === 0 || data.status === 1 || data.status === 4
        }
      },
      {
        permission: ['O_02_0692'],
        id: 'Cancle',
        icon: 'icon_solid_pushorder',
        title: i18n.t('取消发布'),
        visibleCondition: (data) => {
          return data.status === 2
        }
      },
      {
        permission: ['O_02_0443'],
        id: 'Close',
        icon: 'icon_solid_pushorder',
        title: i18n.t('关闭'),
        visibleCondition: (data) => {
          return data.status === 2 || data.status === 3 || data.status === 4 || data.status === 5
        }
      },
      {
        permission: ['O_02_0440'],
        id: 'Confirm',
        icon: 'icon_solid_pushorder',
        title: i18n.t('确认'),
        visibleCondition: (data) => {
          return data.status === 4
        }
      }
    ],
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    }
  },
  // {
  //   field: "deliveryStatus",
  //   headerText: i18n.t("发货状态"),
  //   width: "150",
  //   allowEditing: false,
  //   valueConverter: {
  //     type: "map",
  //     map: {
  //       0: i18n.t("未发货"),
  //       1: i18n.t("全部发货"),
  //       2: i18n.t("部分发货"),
  //     },
  //   },
  // },
  // {
  //   field: "receiveStatus",
  //   headerText: i18n.t("收货状态"),
  //   width: "150",
  //   allowEditing: false,
  //   valueConverter: {
  //     type: "map",
  //     map: {
  //       0: i18n.t("未收货"),
  //       1: i18n.t("部分收货"),
  //       2: i18n.t("全部收货"),
  //     },
  //   },
  // },
  {
    field: 'planGroupName',
    headerText: i18n.t('计划组'),
    width: '85',
    selectOptions: [],
    searchOptions: {
      ...MasterDataSelect.businessGroup,
      renameField: 'planGroupCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.planGroupCode}}-{{data.planGroupName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    selectOptions: [],
    width: '300',
    searchOptions: {
      ...MasterDataSelect.factoryAddress,
      renameField: 'siteCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('工厂')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.siteCode}}-{{data.siteName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    width: '220',
    searchOptions: {
      ...MasterDataSelect.businessCompany,
      renameField: 'companyCode'
    },
    editTemplate: () => {
      return { template: InputView }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('公司')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.companyCode}}-{{data.companyName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'buyerOrgName',
    headerText: i18n.t('采购组'),
    width: '150',
    searchOptions: {
      ...MasterDataSelect.businessGroup,
      renameField: 'buyerOrgCode'
    },
    editTemplate: () => {
      return { template: InputView }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'scheduleArea',
    width: '100',
    headerText: i18n.t('计划区域'),
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    selectOptions: [],
    width: '145',
    // searchOptions: {
    //   ...MasterDataSelect.material,
    //   renameField: "itemCode",
    // },
    editTemplate: () => {
      return { template: ItemCode }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料编号')}}</span>
              </div>
            `
        })
      }
    },
    searchOptions: { maxQueryValueLength: 20000 }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    ignore: true,
    selectOptions: [],
    width: '430',
    editTemplate: () => {
      return { template: InputView }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料名称')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'itemDescription',
    // headerText: i18n.t("物料描述"),
    width: 0,
    ignore: true,
    allowFiltering: false,
    visible: false,
    editTemplate: () => {
      return { template: InputView }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料描述')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    selectOptions: [],
    width: '245',
    searchOptions: {
      ...MasterDataSelect.supplier,
      renameField: 'supplierCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('供应商')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.supplierCode}}-{{data.supplierName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  // {
  //   field: "supplierName",
  //   headerText: i18n.t("供应商名称"),
  //   width: "200",
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  //   headerTemplate: () => {
  //     return {
  //       template: Vue.component("headers", {
  //         template: `
  //             <div class="headers">
  //               <span style="color: red">*</span>
  //               <span class="e-headertext">{{$t('供应商名称')}}</span>
  //             </div>
  //           `,
  //       }),
  //     };
  //   },
  // },
  {
    field: 'outsourcedType',
    headerText: i18n.t('委外方式'),
    selectOptions: [
      { label: i18n.t('销售委外'), value: '1' },
      { label: i18n.t('标准委外'), value: '0' },
      { label: i18n.t('非委外'), value: '2' }
    ],
    width: '95',
    editTemplate: () => {
      return { template: Select }
    },
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('销售委外'),
        2: i18n.t('非委外'),
        0: i18n.t('标准委外')
      }
    }
  },
  {
    field: 'deliveryMethod',
    headerText: i18n.t('配送方式'),
    selectOptions: [
      { label: i18n.t('直送'), value: '0' },
      { label: i18n.t('非直送'), value: '1' }
    ],
    width: '90',
    editTemplate: () => {
      return { template: Select }
    },
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('直送'), 1: i18n.t('非直送') }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('配送方式')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'associatedNumber',
    headerText: i18n.t('关联工单号'),
    width: '115',
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'domesticDemandFlag',
    headerText: i18n.t('是否内需跟单'),
    width: '160',
    valueConverter: {
      type: 'map',
      map: { E: i18n.t('是'), F: i18n.t('否') }
    },
    editTemplate: () => {
      return {
        template: Vue.component('domesticDemandFlag', {
          template: `
            <mt-select
              :id="data.column.field"
              v-model="data[data.column.field]"
              :data-source="options"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
            ></mt-select>
            `,
          data() {
            return {
              options: [
                { text: i18n.t('是'), value: 'E' },
                { text: i18n.t('否'), value: 'F' }
              ]
            }
          }
        })
      }
    }
  },
  {
    field: 'domesticDemandCode',
    headerText: i18n.t('内需单号'),
    width: '150'
  },
  {
    field: 'bom',
    headerText: i18n.t('BOM号'),
    width: '110',
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'saleOrder',
    headerText: i18n.t('销售订单号'),
    width: '120',
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    allowFiltering: false,
    field: 'saleOrderRowCode',
    headerText: i18n.t('销售订单行号'),
    width: 0,
    ignore: true,
    visible: false,
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'workCenterName',
    headerText: i18n.t('工作中心'),
    selectOptions: [],
    editTemplate: () => {
      return { template: Select }
    },
    width: '100',
    searchOptions: {
      ...MasterDataSelect.workCenter,
      renameField: 'workCenterCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.workCenterCode}}-{{data.workCenterName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'processName',
    headerText: i18n.t('工序名称'),
    width: 0,
    allowFiltering: false,
    visible: false,
    ignore: true,
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'productCode',
    headerText: i18n.t('产品代码'),
    width: 0,
    ignore: true,
    allowFiltering: false,
    visible: false,
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'buyerOrder',
    headerText: i18n.t('采购订单'),
    selectOptions: [],
    width: '100',
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'buyerOrderRowCode',
    headerText: i18n.t('采购订单行'),
    width: '110',
    maxlength: '240',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'warehouseQty',
    headerText: i18n.t('库存量'),
    width: '85',
    allowEditing: false
  },
  {
    field: 'projectTextBatch',
    headerText: i18n.t('项目文本批次'),
    width: '125',
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'released',
    headerText: i18n.t('下达'),
    allowEditing: false,
    width: '80',
    allowFiltering: false,
    ignore: true,
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('未下达'), 1: i18n.t('已下达') }
    },
    options: [
      { text: i18n.t('未下达'), value: '0' },
      { text: i18n.t('已下达'), value: '1' }
    ],
    editTemplate: () => {
      return { template: editView }
    }
  },
  {
    field: 'limitNum',
    headerText: i18n.t('限量数量'),
    width: '100',
    allowEditing: false
  },
  {
    field: 'outstandingNum',
    headerText: i18n.t('未清订单数量'),
    width: '125',
    allowEditing: false
  },
  {
    field: 'remainingNum',
    headerText: i18n.t('剩余可创建数量'),
    type: 'number',
    width: '135',
    allowEditing: false
  },
  {
    field: 'warehouseCode',
    headerText: i18n.t('库存地点编号+名称'),
    selectOptions: [],
    width: '200',
    searchOptions: {
      ...MasterDataSelect.stockAddress,
      renameField: 'warehouseCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.warehouseCode}}-{{data.warehouseName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  // {
  //   field: "warehouseName",
  //   headerText: i18n.t("库存地点名称"),
  //   width: "150",
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
  {
    field: 'processorCode',
    headerText: i18n.t('加工商'),
    selectOptions: [],
    width: '260',
    searchOptions: {
      ...MasterDataSelect.supplier,
      renameField: 'processorCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.processorCode}}-{{data.processorName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  // {
  //   field: "processorName",
  //   headerText: i18n.t("加工商名称"),
  //   width: "150",
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
  {
    field: 'address',
    headerText: i18n.t('收货信息'),
    width: '270',
    allowFiltering: false,
    ignore: true,
    selectOptions: [],
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'warehouseKeeper',
    headerText: i18n.t('仓管员'),
    width: '85',
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    width: '85',
    allowFiltering: false,
    ignore: true,
    allowEditing: false
  },
  {
    field: 'systemRemark',
    headerText: i18n.t('系统备注'),
    width: '95',
    allowEditing: false
  },
  {
    field: 'buyerRemark',
    headerText: i18n.t('采购方备注'),
    width: '105',
    maxlength: 200,
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'supplierRemark',
    headerText: i18n.t('供应商备注'),
    width: '105',
    allowEditing: false
  },
  {
    field: 'scheduleType',
    headerText: i18n.t('JIT物料'),
    selectOptions: [
      { label: 'JIT', value: 'JIT' },
      { label: i18n.t('非JIT'), value: i18n.t('非JIT') }
    ],
    width: '90',
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('JIT物料')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'supplierCheckUser',
    headerText: i18n.t('供应商确认人'),
    width: '125',
    allowEditing: false,
    allowFiltering: false,
    ignore: true
  },
  {
    allowFiltering: false,
    ignore: true,
    field: 'checkDate',
    headerText: i18n.t('确认日期'),
    width: '120',
    allowEditing: false,
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    editTemplate: () => {
      return { template: editDateView }
    }
  },
  {
    allowFiltering: false,
    ignore: true,
    field: 'selfType',
    headerText: i18n.t('类型'),
    width: '110',
    allowEditing: false,
    editTemplate: () => {
      return { template: selfType }
    },
    template: () => {
      return { template: selfType }
    }
  }
]
