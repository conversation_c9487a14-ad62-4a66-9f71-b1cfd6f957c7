import Input from '../../purchaseScheduleKT/components1/Input.vue'
import InputNumber from '../../purchaseScheduleKT/components1/InputNumber.vue'
import selfType from '../components/selfType.vue'
import editView from '../../purchaseScheduleKT/components1/editView.vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

import Vue from 'vue'

export const timeDate = (dataKey, hasTime) => {
  // const { dataKey, hasTime } = args;
  // console.log(args);
  const template = () => {
    return {
      template: Vue.component('date', {
        template: `<div><div>{{data[dataKey] | dateFormat}}</div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div></div>`,
        data: function () {
          return { data: {}, dataKey, hasTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
export const checkColumn1 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false,
    allowEditing: false

    // customAttributes: {
    //   class: 'sticky-col-0' // sticky-col-0不是固定写法，自定义命名即可
    // }
  }
]

export const lastColumn1 = [
  {
    field: 'addId',
    headerText: 'addId',
    width: 0,
    visible: false,
    allowEditing: false
  },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false
  },
  {
    field: 'lineNo', // 前端定义 不可编辑
    headerText: i18n.t('序号')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '85',
    allowEditing: false,
    options: [
      { text: i18n.t('新建'), value: '0' },
      { text: i18n.t('已修改'), value: '1' },
      { text: i18n.t('已发布'), value: '2' },
      { text: i18n.t('反馈-满足'), value: '3' },
      { text: i18n.t('反馈-不满足'), value: '4' },
      { text: i18n.t('已确认'), value: '5' },
      { text: i18n.t('已关闭'), value: '6' }
      // { text: i18n.t('同步中'), value: '7' }
    ],
    editTemplate: () => {
      return { template: editView }
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('新建'),
        1: i18n.t('已修改'),
        2: i18n.t('已发布'),
        3: i18n.t('反馈-满足'),
        4: i18n.t('反馈-不满足'),
        5: i18n.t('已确认'),
        6: i18n.t('已关闭')
        // 7: i18n.t('同步中')
      }
    },
    cellTools: [
      // {
      //   // permission: ['O_02_0438'],
      //   id: 'Publish',
      //   icon: 'icon_solid_pushorder',
      //   title: i18n.t('发布'),
      //   visibleCondition: (data) => {
      //     return data.status === 0 || data.status === 1 || data.status === 4
      //   }
      // },
      // {
      //   // permission: ['O_02_0438'],
      //   id: 'Feedback',
      //   icon: 'icon_list_edit',
      //   title: i18n.t('反馈'),
      //   visibleCondition: (data) => {
      //     return data.status === 2
      //   }
      // },
      // {
      //   permission: ['O_02_0692'],
      //   id: 'Cancle',
      //   icon: 'icon_solid_pushorder',
      //   title: i18n.t('取消发布'),
      //   visibleCondition: (data) => {
      //     return data.status === 2
      //   }
      // },
      // {
      //   // permission: ['O_02_0443'],
      //   id: 'Close',
      //   icon: 'icon_solid_pushorder',
      //   title: i18n.t('关闭'),
      //   visibleCondition: (data) => {
      //     return data.status === 2 || data.status === 3 || data.status === 4 || data.status === 5
      //   }
      // }
      // {
      //   // permission: ['O_02_0440'],
      //   id: 'Confirm',
      //   icon: 'icon_solid_pushorder',
      //   title: i18n.t('确认'),
      //   visibleCondition: (data) => {
      //     return data.status === 4
      //   }
      // }
    ],
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    }
  },
  // {
  //   field: "deliveryStatus",
  //   headerText: i18n.t("发货状态"),
  //   width: "150",
  //   allowEditing: false,
  //   valueConverter: {
  //     type: "map",
  //     map: {
  //       0: i18n.t("未发货"),
  //       1: i18n.t("全部发货"),
  //       2: i18n.t("部分发货"),
  //     },
  //   },
  // },
  // {
  //   field: "receiveStatus",
  //   headerText: i18n.t("收货状态"),
  //   width: "150",
  //   allowEditing: false,
  //   valueConverter: {
  //     type: "map",
  //     map: {
  //       0: i18n.t("未收货"),
  //       1: i18n.t("部分收货"),
  //       2: i18n.t("全部收货"),
  //     },
  //   },
  // },
  {
    field: 'plannerName',
    headerText: i18n.t('计划员'),
    selectOptions: [],
    searchOptions: {
      ...MasterDataSelect.businessGroup,
      renameField: 'planGroupCode'
    },
    allowEditing: false,
    // editTemplate: () => {
    //   return { template: Select }
    // },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.plannerName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'factoryCode',
    headerText: i18n.t('工厂'),
    selectOptions: [],
    width: '300',
    allowEditing: false,
    searchOptions: {
      ...MasterDataSelect.factoryAddress,
      renameField: 'factoryCode'
    },
    allowResizing: false,
    // customAttributes: {
    //   class: 'sticky-col-1' // sticky-col-0不是固定写法，自定义命名即可
    // },
    editTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.factoryCode}}-{{data.factoryName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('工厂')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.factoryCode}}-{{data.factoryName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'mrpArea',
    width: '100',
    headerText: i18n.t('MRP区域'),
    maxlength: '40',
    allowEditing: false
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    selectOptions: [],
    width: '200',
    allowEditing: false,
    allowResizing: false
    // customAttributes: {
    //   class: 'sticky-col-2' // sticky-col-0不是固定写法，自定义命名即可
    // }
    // editTemplate: () => {
    //   return { template: ItemCode }
    // },
    // headerTemplate: () => {
    //   return {
    //     template: Vue.component('headers', {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('物料编号')}}</span>
    //           </div>
    //         `
    //     })
    //   }
    // }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    ignore: true,
    selectOptions: [],
    width: '430',
    allowEditing: false
    // customAttributes: {
    //   class: 'sticky-col-3' // sticky-col-0不是固定写法，自定义命名即可
    // }
    // editTemplate: () => {
    //   return { template: InputView }
    // },
    // headerTemplate: () => {
    //   return {
    //     template: Vue.component('headers', {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('物料名称')}}</span>
    //           </div>
    //         `
    //     })
    //   }
    // }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    // selectOptions: [],
    width: '245',
    allowEditing: false,
    allowResizing: false
    // customAttributes: {
    //   class: 'sticky-col-4' // sticky-col-0不是固定写法，自定义命名即可
    // }
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '200',
    allowEditing: false
    // editTemplate: () => {
    //   return { template: InputView }
    // },
    // headerTemplate: () => {
    //   return {
    //     template: Vue.component('headers', {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('供应商名称')}}</span>
    //           </div>
    //         `
    //     })
    //   }
    // }
  },
  {
    field: 'outsourceMethod',
    headerText: i18n.t('委外方式'),
    selectOptions: [
      { label: i18n.t('销售委外'), value: '1' },
      { label: i18n.t('标准委外'), value: '0' },
      { label: i18n.t('非委外'), value: '2' }
    ],
    width: '95',
    allowEditing: false,
    options: [
      { text: i18n.t('标准委外'), value: '0' },
      { text: i18n.t('销售委外'), value: '1' },
      { text: i18n.t('非委外'), value: '2' }
    ],
    editTemplate: () => {
      return { template: editView }
    },
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('销售委外'),
        2: i18n.t('非委外'),
        0: i18n.t('标准委外')
      }
    }
  },
  {
    field: 'deliveryMethod',
    headerText: i18n.t('配送方式'),
    selectOptions: [
      { label: i18n.t('直送'), value: '0' },
      { label: i18n.t('非直送'), value: '1' }
    ],
    width: '90',
    allowEditing: false,
    options: [
      { text: i18n.t('直送'), value: '0' },
      { text: i18n.t('非直送'), value: '1' }
    ],
    editTemplate: () => {
      return { template: editView }
    },
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('直送'), 1: i18n.t('非直送') }
    }
  },
  {
    field: 'isJit',
    headerText: i18n.t('是否jit'),
    width: '90',
    allowEditing: false,
    options: [
      { text: i18n.t('否'), value: 0 },
      { text: i18n.t('是'), value: 1 }
    ],
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    field: 'invAddrCode',
    headerText: i18n.t('库存地点'),
    selectOptions: [],
    width: '200',
    // searchOptions: {
    //   ...MasterDataSelect.stockAddress,
    //   renameField: 'invAddrCode'
    // },
    allowEditing: false
    // editTemplate: () => {
    //   return { template: Select }
    // },
    // template: () => {
    //   return {
    //     template: Vue.component('headers', {
    //       template: `
    //           <div class="headers">
    //             <span>{{data.invAddrCode}}-{{data.invAddrName}}</span>
    //           </div>
    //         `,
    //       data() {
    //         return {
    //           data: {}
    //         }
    //       },
    //       mounted() {}
    //     })
    //   }
    // }
  },
  // {
  //   field: 'warehouseName',
  //   headerText: i18n.t('库存地点名称'),
  //   width: '150',
  //   editTemplate: () => {
  //     return { template: InputView }
  //   }
  // },
  {
    field: 'plusBizCirclesCode',
    headerText: i18n.t('加工商'),
    selectOptions: [],
    width: '260',
    allowEditing: false,
    searchOptions: {
      ...MasterDataSelect.supplier,
      renameField: 'plusBizCirclesCode'
    },
    editTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.plusBizCirclesCode}}-{{data.plusBizCirclesName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.plusBizCirclesCode}}-{{data.plusBizCirclesName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  // {
  //   field: 'processorName',
  //   headerText: i18n.t('加工商名称'),
  //   width: '150',
  //   editTemplate: () => {
  //     return { template: InputView }
  //   }
  // },
  // {
  //   field: 'batchNo',
  //   headerText: i18n.t('批次'),
  //   width: '125',
  //   maxlength: '40',
  //   allowEditing: false
  //   // editTemplate: () => {
  //   //   return { template: Input }
  //   // }
  // },
  {
    field: 'projectTextBatch',
    headerText: i18n.t('项目文本批次'),
    width: '125',
    allowEditing: false,
    maxlength: '40'
    // editTemplate: () => {
    //   return { template: Input }
    // }
  },
  {
    field: 'batchNo',
    headerText: i18n.t('批次号'),
    width: '125',
    // allowEditing: false,
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'lineBody',
    headerText: i18n.t('线体'),
    width: '125',
    // allowEditing: false,
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'flag',
    headerText: i18n.t('标识'),
    width: '125',
    // allowEditing: false,
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'workCenterName',
    headerText: i18n.t('工作中心'),
    selectOptions: [],
    // editTemplate: () => {
    //   return { template: Select }
    // },
    allowEditing: false,
    width: '100',
    searchOptions: {
      ...MasterDataSelect.workCenter,
      renameField: 'workCenterName'
    },
    editTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.workCenterCode}}-{{data.workCenterName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.workCenterCode}}-{{data.workCenterName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'batchQty',
    headerText: i18n.t('批量'),
    width: '125',
    // allowEditing: false,
    maxlength: '40',
    editTemplate: () => {
      return { template: InputNumber }
    }
  },
  {
    field: 'bomNo',
    headerText: i18n.t('BOM号'),
    width: '110',
    allowEditing: false,
    maxlength: '40'
    // editTemplate: () => {
    //   return { template: Input }
    // }
  },
  {
    field: 'purchaserRemark',
    headerText: i18n.t('采购方备注'),
    width: '125',
    maxlength: 200,
    allowEditing: false
  },
  {
    field: 'supplierRemark',
    headerText: i18n.t('供应商备注'),
    width: '125',
    maxlength: 200,
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'purchaseOrderNo',
    headerText: i18n.t('采购订单'),
    selectOptions: [],
    width: '100',
    allowEditing: false
    // editTemplate: () => {
    //   return { template: Select }
    // }
  },
  {
    field: 'bigVersionNo',
    headerText: i18n.t('版本号'),
    width: '150',
    // valueConverter: {
    //   type: 'function',
    //   filter: (e) => {
    //     return 'V' + e
    //   }
    // },
    allowEditing: false
  },
  {
    field: 'deliverType',
    headerText: i18n.t('送货类型'),
    width: '125',
    allowEditing: false,
    options: [
      { text: i18n.t('采购订单'), value: '1' },
      { text: i18n.t('交货计划'), value: '2' },
      { text: 'JIT', value: '3' },
      { text: i18n.t('无需求'), value: '4' },
      { text: 'VMI', value: '5' },
      { text: i18n.t('钢材'), value: '6' }
    ],
    editTemplate: () => {
      return { template: editView }
    },
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('采购订单'),
        2: i18n.t('交货计划'),
        3: i18n.t('JIT'),
        4: i18n.t('无需求'),
        5: i18n.t('VMI'),
        6: i18n.t('钢材')
      }
    }
  },
  {
    field: 'unclearOrderQty',
    headerText: i18n.t('未清订单数量'),
    width: '125',
    allowEditing: false
  },
  {
    field: 'invQty',
    headerText: i18n.t('库存量'),
    width: '85',
    allowEditing: false
  },
  {
    field: 'limitQty',
    headerText: i18n.t('限量数量'),
    width: '100',
    allowEditing: false
  },
  {
    field: 'structSeqNo',
    headerText: i18n.t('结构序号'),
    allowEditing: false
  },
  {
    allowFiltering: false,
    ignore: true,
    field: 'selfType',
    headerText: i18n.t('类型'),
    width: '110',
    allowEditing: false,
    editTemplate: () => {
      return { template: selfType }
    },
    template: () => {
      return { template: selfType }
    }
  }
]
