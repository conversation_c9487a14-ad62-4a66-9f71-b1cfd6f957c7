<template>
  <div class="selfType">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      style="display: none"
    ></mt-input>
    <!-- <div>{{ showInfo.d }}</div> -->
    <div class="self-type-cell-boder">{{ showInfo.p }}</div>
    <mt-inputNumber
      v-if="show"
      v-model="num"
      :min="0"
      :step="1"
      :precision="3"
      :show-clear-button="false"
      @input="inputChange"
      class="self-type-cell-boder"
    ></mt-inputNumber>
    <!-- <div>{{ showInfo.c }}</div> -->
    <!-- <div>{{ showInfo.g }}</div>
    <div>{{ showInfo.u }}</div> -->
  </div>
</template>
<script>
export default {
  data() {
    return {
      data: {},
      showInfo: {
        p: '',
        d: '',
        // c: '',
        g: '',
        u: ''
      },
      num: '',
      show: true
    }
  },
  mounted() {
    if (this.data[this.data.column.field]) {
      this.show = true
      let list = this.data[this.data.column.field].split('_')
      this.showInfo.d = list[0]
      // this.num = list[1]
      this.showInfo.p = list[1]
      this.num = list[2]
      this.showInfo.g = list[3]
      this.showInfo.u = list[4]
    } else {
      this.show = false
    }
    if (this.data.isEntry === '2') {
      this.show = true
    }
    const purchaseScheduleTvRow = sessionStorage.getItem('purchaseScheduleTvRow')
      ? JSON.parse(sessionStorage.getItem('purchaseScheduleTvRow'))
      : {}
    if (purchaseScheduleTvRow && purchaseScheduleTvRow[this.data.column.field]) {
      this.num = purchaseScheduleTvRow[this.data.column.field]?.split('_')[2] || ''
    }
  },
  methods: {
    inputChange(e) {
      const purchaseScheduleTvRow = sessionStorage.getItem('purchaseScheduleTvRow')
        ? JSON.parse(sessionStorage.getItem('purchaseScheduleTvRow'))
        : {}
      this.data[
        this.data.column.field
      ] = `${this.showInfo.d}_${this.showInfo.p}_${e}_${this.showInfo.g}_${this.showInfo.u}`
      purchaseScheduleTvRow[
        this.data.column.field
      ] = `${this.showInfo.d}_${this.showInfo.p}_${e}_${this.showInfo.g}_${this.showInfo.u}`
      sessionStorage.setItem('purchaseScheduleTvRow', JSON.stringify(purchaseScheduleTvRow))
    }
  }
}
</script>
<style lang="scss" scoped>
.selfType {
  > div {
    height: 36px;
    line-height: 36px;
    box-sizing: border-box;
    // padding: 12px 0;
  }
  ::v-deep .mt-input-number {
    input {
      padding-right: 26px;
    }
    .step-box {
      bottom: -4px;
    }
  }
}
.self-type-cell-boder {
  border: 1px solid #e0e0e0;
}
</style>
