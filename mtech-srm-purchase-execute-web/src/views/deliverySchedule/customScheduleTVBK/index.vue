<template>
  <div class="full-height vertical-flex-box">
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <mt-template-page
        ref="templateRef1"
        :template-config="componentConfig1"
        @handleClickToolBar="handleClickToolBar1"
        @handleClickCellTool="handleClickCellTool1"
        @actionBegin="actionBegin1"
        @handleCustomReset="handleCustomReset"
        @actionComplete="actionComplete1"
        @selectedChanged="selectedChanged1"
      >
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form
              ref="searchFormRef"
              :model="searchFormModel"
              :rules="{
                bigVersionNo: [{ required: true, message: $t('请输入'), trigger: 'blur' }]
              }"
            >
              <mt-form-item prop="factoryCode" :label="$t('工厂')" label-style="top">
                <RemoteAutocomplete
                  style="flex: 1"
                  v-model="searchFormModel.factoryCode"
                  :url="$API.masterData.getSiteAuthFuzzyUrl"
                  multiple
                  :placeholder="$t('请选择')"
                  :fields="{ text: 'siteName', value: 'siteCode' }"
                  params-key="fuzzyParam"
                  records-position="data"
                ></RemoteAutocomplete>
              </mt-form-item>
              <!-- <mt-form-item prop="plannerName" :label="$t('计划员')" label-style="top">
                <mt-input
                  v-model="searchFormModel.plannerName"
                  :show-clear-button="true"
                  :placeholder="$t('请输入计划员')"
                />
              </mt-form-item> -->
              <mt-form-item prop="itemCodes" :label="$t('物料编码')" label-style="top">
                <mt-input v-model="itemCodes" @change="itemChange" />
              </mt-form-item>
              <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
                <mt-input v-model="searchFormModel.itemName"></mt-input>
              </mt-form-item>
              <!-- <mt-form-item prop="salesOrderNo" :label="$t('销售订单号')" label-style="top">
                <mt-input
                  v-model="searchFormModel.salesOrderNo"
                  :show-clear-button="true"
                  :placeholder="$t('请输入销售订单号')"
                />
              </mt-form-item> -->
              <mt-form-item prop="bigVersionNo" :label="$t('版本号')" label-style="top">
                <!-- <mt-input
                  v-model="searchFormModel.bigVersionNo"
                  :show-clear-button="true"
                  :placeholder="$t('请输入版本号')"
                /> -->
                <mt-select
                  v-model="searchFormModel.bigVersionNo"
                  :data-source="versionList"
                  :placeholder="$t('请选择')"
                ></mt-select>
              </mt-form-item>
              <!-- <mt-form-item prop="structSeqNo" :label="$t('结构序号')" label-style="top">
                <mt-input
                  v-model="structSeqNo"
                  @change="() => (searchFormModel.structSeqNo = [structSeqNo])"
                />
              </mt-form-item> -->
              <mt-form-item prop="status" :label="$t('状态')" label-style="top">
                <mt-multi-select
                  style="flex: 1"
                  v-model="searchFormModel.status"
                  :show-select-all="true"
                  :allow-filtering="true"
                  filter-type="Contains"
                  :data-source="statusOptions"
                  :placeholder="$t('请选择')"
                ></mt-multi-select>
              </mt-form-item>
              <mt-form-item prop="planMemberCode" :label="$t('计划员')" label-style="top">
                <mt-multi-select
                  style="flex: 1"
                  v-model="searchFormModel.planMemberCode"
                  :data-source="plannerListOptions"
                  :fields="{ text: 'codeAndName', value: 'userCode' }"
                  :show-select-all="true"
                  :allow-filtering="true"
                  filter-type="Contains"
                  :placeholder="$t('请选择')"
                ></mt-multi-select>
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </mt-template-page>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>
<script>
import { checkColumn1, lastColumn1 } from './config/index.js'
import { cloneDeep } from 'lodash'
import InputNumber from './components/InputNumber.vue'
import InputNumberView from './components/InputNumberView.vue'
import * as UTILS from '@/utils/utils'
import dayjs from 'dayjs'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      itemCodes: '',
      // structSeqNo: '',
      statusOptions: [
        // { text: this.$t('新建'), value: '0' },
        // { text: '新建-修改', value: '1' },
        { text: this.$t('已发布'), value: '2' },
        { text: this.$t('反馈-满足'), value: '3' },
        { text: this.$t('反馈-不满足'), value: '4' },
        { text: this.$t('已确认'), value: '5' }
        // { text: this.$t('已关闭'), value: '6' },
        // { text: this.$t('同步中'), value: '7' }
      ],
      // plannerListOptions: [], // 计划员 下列选项
      searchFormModel: {
        bigVersionNo: dayjs(new Date()).format('YYYYMMDD')
      },
      // saveUrl: `${BASE_TENANT}/po/time/config/upload`, // 导入API
      // fromDataKey: 'excel', // 上传组件的 name，此值为调用 api 请求参数的 key
      lastColumn1: lastColumn1,
      componentConfig1: [
        // {
        //   useToolTemplate: false, // 不使用预置(新增、编辑、删除)
        //   useCombinationSelection: false,
        //   useBaseConfig: false, // 使用组件中的toolbar配置
        //   isUseCustomSearch: true,
        //   isCustomSearchRules: true,
        //   toolbar: [
        //     [
        //       // {
        //       //   id: 'Add',
        //       //   icon: 'icon_solid_Createorder',
        //       //   title: this.$t('新增')
        //       //   // permission: ["O_02_0436"],
        //       // },
        //       {
        //         id: 'closeEdit',
        //         icon: 'icon_table_delete',
        //         // permission: ["O_02_1141"],
        //         title: this.$t('取消编辑')
        //       },
        //       // {
        //       //   id: 'Delete',
        //       //   icon: 'icon_solid_Delete',
        //       //   title: this.$t('删除')
        //       //   // permission: ["O_02_0437"],
        //       // },
        //       // {
        //       //   id: 'Publish',
        //       //   icon: 'icon_solid_Activateorder',
        //       //   title: this.$t('发布')
        //       //   // permission: ["O_02_0438"],
        //       // },
        //       // {
        //       //   id: 'Release',
        //       //   icon: 'icon_solid_Pauseorder',
        //       //   title: this.$t('下达')
        //       //   // permission: ["O_02_0439"],
        //       // },
        //       // {
        //       //   id: 'Confirm',
        //       //   icon: 'icon_solid_Pauseorder',
        //       //   title: this.$t('确认')
        //       //   // permission: ["O_02_0440"],
        //       // },
        //       {
        //         id: 'Feedback',
        //         icon: 'icon_list_edit',
        //         title: this.$t('反馈')
        //         // permission: ["O_02_0440"],
        //       },
        //       {
        //         id: 'Import',
        //         icon: 'icon_solid_Import',
        //         title: this.$t('导入')
        //         // permission: ["O_02_0441"],
        //       },
        //       {
        //         id: 'Export1',
        //         icon: 'icon_solid_export',
        //         title: this.$t('导出')
        //         // permission: ["O_02_0442"],
        //       }
        //     ],
        //     []
        //   ],
        //   // gridId: this.$tableUUID.purchaseSchedule.purchaseScheduleDayTab,
        //   grid: {
        //     allowReordering: false,
        //     allowPaging: false, // 不分页
        //     dataSource: [],
        //     allowEditing: true, //开启表格编辑操作
        //     editSettings: {
        //       allowEditing: true,
        //       allowAdding: true,
        //       allowDeleting: true,
        //       mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
        //       showConfirmDialog: false,
        //       showDeleteConfirmDialog: false,
        //       newRowPosition: 'Top'
        //     },
        //     columnData: []
        //   }
        // }
      ],
      currentList: [], //从接口获取到的最新的数据
      nowEditRowFlag: '', //当前编辑的行id
      addId: '1',
      isEdit: '1', //是否编辑 1是编辑 2不是编辑
      selectedOtherInfo: {},

      apiWaitingQuantity: 0, // 调用的api正在等待数
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 10, // 当前每页数据量
        totalPages: 0, // 总页数
        pageSizes: [10, 50, 100, 200]
      },
      forecastPageCurrent: 1, // 预测表格 当前页码
      forecastRules: [], // 预测表格请求规则
      forecastCondition: 'and', // 过滤-规则-关系，默认为 "and"
      tableContainerClientHeight: 0,
      titleList: [],
      versionList: [],
      plannerListOptions: []
    }
  },
  mounted() {
    this.buyerGoodsDemandPlanInfoQueryVersionTv()
    // this.buyerGoodsDemandPlanInfoQuery()
    // this.handleColumns([])
  },
  methods: {
    itemChange(e) {
      if (e) {
        this.searchFormModel.itemCodes = this.itemCodes.split(' ')
      } else {
        this.searchFormModel.itemCodes = null
      }
    },
    // 获取计划员下拉
    getPlannerList(params) {
      this.$API.deliverySchedule.supplierGoodsDemandPlanInfoQueryPlannerTv(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.plannerListOptions = data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.userCode} - ${i.userName}`
            }
          })
          this.plannerListOptions.push({
            userCode: '',
            codeAndName: this.$t('计划员空')
          })
        }
      })
    },
    buyerGoodsDemandPlanInfoQueryVersionTv() {
      this.$API.deliverySchedule.supplierGoodsDemandPlanInfoQueryVersionTv().then((res) => {
        const { data, code } = res
        if (code === 200) {
          this.versionList = data
          this.searchFormModel.bigVersionNo = data[0]
          this.getPlannerList({ version: data[0] })
          this.buyerGoodsDemandPlanInfoQuery()
        }
      })
    },
    // 重置查询条件
    handleCustomReset() {
      // this.structSeqNo = ''
      this.itemCodes = null
      this.searchFormModel.itemCodes = null
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'bigVersionNo') {
            this.searchFormModel[key] = this.versionList[0]
          }
        }
      }
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.buyerGoodsDemandPlanInfoQuery()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageCurrent = 1
      this.forecastPageSettings.pageSize = pageSize
      this.buyerGoodsDemandPlanInfoQuery()
    },
    // 采方-获取采方预测信息列表
    buyerGoodsDemandPlanInfoQuery() {
      // delete this.componentConfig1[0].gridId;
      this.componentConfig1 = []
      const params = {
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        },
        ...this.searchFormModel
      }
      this.apiStartLoading()
      this.$API.deliverySchedule
        .supplierGoodsDemandPlanInfoQueryTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.supplierDemandPlanPage?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.totalRecordsCount = Number(total)
            const records = res?.data?.supplierDemandPlanPage?.records || [] // 表格数据
            let titleList = res?.data?.titleList || [] // 动态表头数据
            this.titleList = res?.data?.titleList || [] // 动态表头数据
            // 处理表头数据
            let columnData = this.handleColumns(titleList)
            // 处理表数据
            let dataSource = this.handleDataSource(records, titleList)
            let config = {
              useToolTemplate: false, // 不使用预置(新增、编辑、删除)
              useBaseConfig: false, // 使用组件中的toolbar配置
              isUseCustomSearch: true,
              isCustomSearchRules: true,
              toolbar: [
                [
                  // {
                  //   id: 'Add',
                  //   icon: 'icon_solid_Createorder',
                  //   title: this.$t('新增')
                  //   // permission: ["O_02_0436"],
                  // },
                  {
                    id: 'closeEdit',
                    icon: 'icon_table_delete',
                    // permission: ["O_02_1141"],
                    title: this.$t('取消编辑')
                  },
                  // {
                  //   id: 'Delete',
                  //   icon: 'icon_solid_Delete',
                  //   title: this.$t('删除')
                  //   // permission: ["O_02_0437"],
                  // },
                  // {
                  //   id: 'Publish',
                  //   icon: 'icon_solid_Activateorder',
                  //   title: this.$t('发布')
                  //   // permission: ["O_02_0438"],
                  // },
                  // {
                  //   id: 'Release',
                  //   icon: 'icon_solid_Pauseorder',
                  //   title: this.$t('下达')
                  //   // permission: ["O_02_0439"],
                  // },
                  // {
                  //   id: 'Confirm',
                  //   icon: 'icon_solid_Pauseorder',
                  //   title: this.$t('确认')
                  //   // permission: ["O_02_0440"],
                  // },
                  {
                    id: 'Feedback',
                    icon: 'icon_list_edit',
                    title: this.$t('反馈')
                    // permission: ["O_02_0440"],
                  },
                  {
                    id: 'Import',
                    icon: 'icon_solid_Import',
                    title: this.$t('导入')
                    // permission: ["O_02_0441"],
                  },
                  {
                    id: 'Export1',
                    icon: 'icon_solid_export',
                    title: this.$t('导出')
                    // permission: ["O_02_0442"],
                  }
                ],
                []
              ],
              gridId: 'cc68214f-d641-4f9a-9a70-7ec6030c88f2',
              grid: {
                gridLines: 'Both',
                allowReordering: false,
                allowPaging: false, // 不分页
                dataSource: dataSource,
                allowEditing: true, //开启表格编辑操作
                editSettings: {
                  allowEditing: true,
                  allowAdding: true,
                  allowDeleting: true,
                  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
                  showConfirmDialog: false,
                  showDeleteConfirmDialog: false,
                  newRowPosition: 'Top'
                },
                columnData: columnData
              }
            }
            this.componentConfig1.push(config)
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
      // this.$refs.searchFormRef.validate((valid) => {
      //   if (valid) {
      //   }
      // })
    },
    handleDataSource(records, titleList) {
      // let list1 = []
      // titleList.forEach((item) => {
      //   if (item && item.length === 13) {
      //     list1.push(UTILS.default.dateFormat(Number(item), 'Y-m-d'))
      //   }
      // })
      // this.titleList = list1
      records.forEach((obj, index) => {
        obj.addId = this.addId++
        obj.isEntry = '2' //是否是带入的数据
        obj.lineNo = index + 1
        if (obj.tvSupplierDemandPlanItemExtList.length > 0) {
          titleList.forEach((title, index) => {
            let total = '0'
            let buyerNum = '0'
            let supplierNum = '0'
            // let gapNum = ''
            // let countGapNum = ''
            obj.tvSupplierDemandPlanItemExtList.forEach((itm) => {
              if (!itm) {
                total = '0'
                buyerNum = '0'
                supplierNum = '0'
                return
              }
              if (itm.type === 'D') {
                total = itm[`planDemandQty${index + 1}`]
              }
              if (itm.type === 'P') {
                buyerNum = itm[`planDemandQty${index + 1}`]
              }
              if (itm.type === 'C') {
                supplierNum = itm[`planDemandQty${index + 1}`]
              }
            })
            obj[title] = `${total}_${buyerNum}_${supplierNum}_${0}_${0}`
          })
        }
        // let afterData = []
        // let tempArr = []
        // obj.limitNum = ''
        // obj.remainingNum = ''
        // obj.outstandingNum = ''
        // obj.deliveryMethod = String(obj.deliveryMethod)
        // if (obj.item.length > 0) {
        //   obj.purchaserRemark = obj.item[0].purchaserRemark
        //   obj.release = obj.item[0].release
        //   obj.supplierRemark = obj.item[0].supplierRemark
        //   obj.item.forEach((item1) => {
        //     obj.limitNum = bigDecimal.add(obj.limitNum, item1.limitNum)
        //     obj.remainingNum = bigDecimal.add(obj.remainingNum, item1.remainingNum)
        //     obj.outstandingNum = bigDecimal.add(obj.outstandingNum, item1.outstandingNum)
        //   })
        // }
        // obj.item.forEach((item1) => {
        //   if (item1.timeInfoTimestamp && item1.timeInfoTimestamp.length === 13) {
        //     item1.timeInfoTimestamp = UTILS.default.dateFormat(
        //       Number(item1.timeInfoTimestamp),
        //       'Y-m-d'
        //     )
        //   }
        //   if (tempArr.indexOf(item1.timeInfoTimestamp) === -1) {
        //     tempArr.push(item1.timeInfoTimestamp)
        //     afterData.push({
        //       timeInfoTimestamp: item1.timeInfoTimestamp,
        //       origin: [item1]
        //     })
        //   } else {
        //     afterData.some((item2) => {
        //       if (item2.timeInfoTimestamp === item1.timeInfoTimestamp) {
        //         item2.origin.push(item1)
        //       }
        //     })
        //   }
        // })
        // afterData.forEach((item1) => {
        //   if (item1.origin.length === 1) {
        //     obj[
        //       item1.timeInfoTimestamp
        //     ] = `${item1.origin[0].total}_${item1.origin[0].buyerNum}_${item1.origin[0].supplierNum}_${item1.origin[0].gapNum}_${item1.origin[0].countGapNum}`
        //   }
        //   if (item1.origin.length > 1) {
        //     let total = ''
        //     let buyerNum = ''
        //     let supplierNum = ''
        //     let gapNum = ''
        //     let countGapNum = ''
        //     item1.origin.forEach((item2) => {
        //       total = bigDecimal.add(item2.total, total)
        //       buyerNum = bigDecimal.add(item2.buyerNum, buyerNum)
        //       supplierNum = bigDecimal.add(item2.supplierNum, supplierNum)
        //       gapNum = bigDecimal.add(item2.gapNum, gapNum)
        //       countGapNum = bigDecimal.add(item2.countGapNum, countGapNum)
        //     })
        //     obj[
        //       item1.timeInfoTimestamp
        //     ] = `${total}_${buyerNum}_${supplierNum}_${gapNum}_${countGapNum}`
        //   }
        // })
        // this.currentList = records
      })
      // this.componentConfig1[0].grid.dataSource = records;
      return records
    },
    handleColumns(titleList) {
      let lastColumn2 = []
      titleList.forEach((item) => {
        if (item && item.length === 13) {
          item = UTILS.default.dateFormat(Number(item), 'Y-m-d')
        }
        lastColumn2.push({
          field: item,
          headerText: item,
          width: '100',
          ignore: true,
          allowEditing: false,
          allowFiltering: false,
          template: () => {
            return { template: InputNumberView }
          },
          editTemplate: () => {
            return { template: InputNumber }
          }
        })
      })
      // this.componentConfig1[0].grid.columnData = checkColumn1
      //   .concat(lastColumn1)
      //   .concat(lastColumn2);
      let columnData = checkColumn1.concat(lastColumn1).concat(lastColumn2)
      return columnData
    },
    // 文件上传完成
    // uploadCompleted(response) {
    //   this.$refs.importDialog.showStepSecond()
    //   this.$nextTick(() => {
    //     this.$refs.predictImportDialogSlot.init({
    //       resData: response?.data || []
    //     })
    //   })
    // },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    },
    // 预测数据导入编辑弹框 点击导入
    // importDialogImport() {
    //   this.$refs.predictImportDialogSlot.doImport()
    // },
    // 导入提交
    // predictImportDialogSlotSubmitted() {
    //   this.$refs.importDialog.handleClose()
    //   this.updateList()
    // },
    handleClose(row) {
      //关闭
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认关闭选中的交货计划吗?')
        },
        success: () => {
          // let items = []
          // row.forEach((obj) => {
          //   items = items.concat(obj.item || [])
          // })
          // let ids = items.map((item) => item.id)
          // let params = ids
          const params = []
          // let ids = items.map((item) => item.id)
          row.forEach((i) => {
            params.push({
              id: i.id,
              status: i.status
            })
          })
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoCloseTv(params).then(() => {
            this.$toast({
              content: this.$t('关闭交货计划操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleCancle(row) {
      //取消发布
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认取消发布选中的交货计划吗?')
        },
        success: () => {
          let items = []
          row.forEach((obj) => {
            items = items.concat(obj.item || [])
          })
          let ids = items.map((item) => item.id)
          let params = ids
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoCancel(params).then(() => {
            this.$toast({
              content: this.$t('取消发布交货计划操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handlePublish(row) {
      //发布
      // let flag = true
      // row.forEach((item) => {
      //   if (!item.addressId) {
      //     flag = false
      //   }
      // })
      // if (!flag) {
      //   this.$toast({
      //     content: this.$t('送货联系人电话地址填写才可以发布'),
      //     type: 'warning'
      //   })
      //   return
      // }
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认发布选中的交货计划吗?')
        },
        success: () => {
          // let items = []
          // row.forEach((obj) => {
          //   items = items.concat(obj.item || [])
          // })
          const params = []
          // let ids = items.map((item) => item.id)
          row.forEach((i) => {
            params.push({
              id: i.id,
              status: i.status,
              line: i.lineNo,
              supplierCode: i.supplierCode,
              bigVersionNo: i.bigVersionNo,
              md5Code: i.md5Code,
              sameBatchData: i.sameBatchData,
              supplierTenantId: i.supplierTenantId,
              supplierName: i.supplierName,
              plannerName: i.plannerName,
              planMemberCode: i.planMemberCode
            })
          })
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoPublishTv(params).then(() => {
            this.$toast({
              content: this.$t('发布交货计划操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleAdd() {
      //新增
      this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
    },
    handleImport() {
      //导入
      // this.$refs.importDialog.init({
      //   title: this.$t('导入')
      // })
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.deliverySchedule.supplierGoodsDemandPlanInfoImportTv,
          // downloadTemplateApi: this.$API.deliverySchedule.buyerGoodsDemandPlanInfoExportTv,
          paramsKey: 'excel'
          // asyncParams: {
          //   // requestJson: JSON.stringify(parameter),
          // },
        },
        success: () => {
          // 导入之后刷新列表
          this.$refs.templateRef1.refreshCurrentGridData()
        }
      })
    },
    handleExport() {
      //导出
      // let params = {
      //   condition: this.forecastCondition,
      //   page: { current: 1, size: 1000 },
      //   pageFlag: true,
      //   rules: [...this.forecastRules]
      // }
      const params = {
        page: {
          size: 1000,
          current: 1
        },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.deliverySchedule.supplierGoodsDemandPlanInfoExportTv(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleRelease(row) {
      //下达
      let hasOne = row.some((item) => {
        return item.release === '1'
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择未下达的行操作!'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认下达选中的交货计划吗?')
        },
        success: () => {
          let items = []
          row.forEach((obj) => {
            items = items.concat(obj.item || [])
          })
          let ids = items.map((i) => {
            return {
              id: i.id,
              status: i.status,
              line: i.lineNo,
              supplierCode: i.supplierCode,
              bigVersionNo: i.bigVersionNo,
              md5Code: i.md5Code,
              sameBatchData: i.sameBatchData,
              supplierTenantId: i.supplierTenantId,
              supplierName: i.supplierName,
              plannerName: i.plannerName,
              planMemberCode: i.planMemberCode
            }
          })
          let params = ids
          this.$API.deliverySchedule.buyerGoodsDemandPlanInforelease(params).then(() => {
            this.$toast({
              content: this.$t('确认下达交货计划操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleConfirm(row) {
      //确认交货计划
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认选中的交货计划吗?')
        },
        success: () => {
          // let items = []
          // row.forEach((obj) => {
          //   items = items.concat(obj.item || [])
          // })
          // let ids = items.map((item) => item.id)
          // let params = ids
          const params = row.map((i) => {
            return { id: i.id, status: i.stauts }
          })
          this.$API.deliverySchedule.supplierGoodsDemandPlanInfoConfirm(params).then(() => {
            this.$toast({
              content: this.$t('确认交货计划操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleFeedback(row) {
      // 反馈交货计划
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认反馈交货计划吗?')
        },
        success: () => {
          // let items = []
          // row.forEach((obj) => {
          //   items = items.concat(obj.item || [])
          // })
          // let ids = items.map((item) => item.id)
          // let params = ids
          const params = {
            queryRulesReq: { ...this.searchFormModel, page: { current: 1, size: 999999 } },
            chooseOperateReqs: row.map((i) => {
              return {
                id: i.id,
                status: i.status,
                line: i.lineNo,
                supplierCode: i.supplierCode,
                bigVersionNo: i.bigVersionNo,
                md5Code: i.md5Code,
                sameBatchData: i.sameBatchData,
                supplierTenantId: i.supplierTenantId,
                supplierName: i.supplierName,
                plannerName: i.plannerName,
                planMemberCode: i.planMemberCode
              }
            })
          }
          this.$API.deliverySchedule.supplierGoodsDemandPlanInfoFeedbackTv(params).then((res) => {
            const { code, msg } = res
            if (code === 200) {
              this.$toast({
                content: this.$t('反馈交货计划操作成功'),
                type: 'success'
              })
              this.updateList()
            } else {
              this.$toast({
                content: msg,
                type: 'warning'
              })
            }
          })
        }
      })
    },
    generateUUID() {
      var d = new Date().getTime()
      if (window.performance && typeof window.performance.now === 'function') {
        d += performance.now() //use high-precision timer if available
      }
      var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (d + Math.random() * 16) % 16 | 0
        d = Math.floor(d / 16)
        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16)
      })
      return uuid
    },
    handleDelete(row) {
      //删除
      const params = []
      // row.forEach((obj) => {
      //   // items = items.concat(obj.item || [])
      //   params.push({
      //     id: obj.id,
      //     status: obj.status
      //   })
      // })
      for (let i = 0; i < row.length; i++) {
        const obj = row[i]
        params.push({
          id: obj.id,
          status: obj.status,
          line: obj.lineNo,
          supplierCode: obj.supplierCode,
          bigVersionNo: obj.bigVersionNo,
          md5Code: obj.md5Code,
          sameBatchData: obj.sameBatchData,
          supplierTenantId: obj.supplierTenantId,
          supplierName: obj.supplierName,
          plannerName: obj.plannerName,
          planMemberCode: obj.planMemberCode
        })
        if (obj.status != 0 && obj.status != 1) {
          this.$toast({
            content: this.$t('仅可删除新建、已修改状态下的数据'),
            type: 'warning'
          })
          return false
        }
      }
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认删除选中的交货计划吗?')
        },
        success: () => {
          // let ids = items.map((item) => item.id)
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoDeleteTv(params).then(() => {
            this.$toast({
              content: this.$t('删除交货计划操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    updateList() {
      sessionStorage.removeItem('purchaseScheduleTvRow')
      this.$refs.templateRef1.refreshCurrentGridData()
      this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
    },
    selectedChanged1(val) {
      console.log(arguments, '-=-==')
      Object.assign(this.selectedOtherInfo, val.itemInfo)
      console.log(this.selectedOtherInfo, '最新的额外数据')
    },
    actionComplete1(args) {
      console.log(args, '我是actionComplete')
      const { rowIndex, index } = args
      if (args.requestType === 'beginEdit' || args.requestType === 'add') {
        this.isEdit = '1'
      }
      if (
        args.requestType === 'refresh' ||
        args.requestType === 'save' ||
        args.requestType === 'delete'
      ) {
        this.isEdit = '2'
      }
      if (args.requestType === 'save' && args.action === 'edit') {
        //编辑完成
        let row = this.getRow()
        this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
        if (row.isEntry === '2') {
          //新增错误重新编辑
          this.addRow(row, rowIndex)
        }
        if (row.isEntry === '1') {
          this.editRow(row, rowIndex)
        }
      }
      if (args.requestType === 'save' && args.action === 'add') {
        //新增完成
        let row = this.getRow()
        this.addRow(row, index)
        this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
      }
    },
    startEdit(index) {
      this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
      this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
    },
    endEdit() {
      //让组件失去焦点
      this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
    },
    judgeValid1(params) {
      //添加校验
      console.log('我执行了1', params)
      let flag = true
      let flag1 = true
      params.some((item) => {
        if (item.buyerNum === '') {
          flag1 = false
        }
      })
      if (!flag1) {
        this.$toast({
          content: this.$t('P（需求量）必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.length) {
        this.$toast({
          content: this.$t('需求日期必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params[0].deliveryMethod) {
        this.$toast({
          content: this.$t('配送方式必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (params[0].deliveryMethod === '0') {
        //配送方式为直送
        if (!params[0].plusBizCirclesCode || !params[0].plusBizCirclesName) {
          this.$toast({
            content: this.$t('配送方式为直送时,加工商编码和名称必填'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      if (params[0].outsourceMethod === '2') {
        //委外方式非委外
        if (params[0].deliveryMethod === '0') {
          this.$toast({
            content: this.$t('委外方式为非委外时,配送方式需为非直送'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      return flag
    },
    editRow(row, index) {
      console.log(row, '编辑行数据')
      let params = []
      row.item.forEach((obj) => {
        params.push({
          id: row.id,
          outsourceMethod: row.outsourceMethod,
          workCenter: row.workCenter, //工作中心
          workCenterCode: row.workCenterCode, //工作中心
          workCenterName: row.workCenterName, //工作中心
          goodsDemandPlanItemId: obj.id,
          supplierCode: row.supplierCode, //供应商
          supplierId: row.supplierId, //供应商id
          supplierName: row.supplierName, //供应商名称
          deliveryMethod: row.deliveryMethod, //配送方式
          release: row.release, //下达
          associatedNumber: row.associatedNumber, //关联工单号
          bomNo: row.bomNo, //BOM
          projectTextBatch: row.projectTextBatch, //项目文本批次
          scheduleType: row.scheduleType, //计划类型
          jit: row.scheduleType === 'JIT' ? 1 : 0,

          // timeInfoTimestamp: new Date(`${obj.timeInfoTimestamp} 00:00:00`).getTime(), // 需求日期
          buyerNum: row[obj.timeInfoTimestamp]?.split('_')[1] || '', //p字段
          plusBizCirclesCode: row.plusBizCirclesCode, //加工商编码
          plusBizCirclesId: row.plusBizCirclesId, //加工商id
          plusBizCirclesName: row.plusBizCirclesName, //加工商名称
          invAddrCode: row.invAddrCode, //库存地点编码
          invAddrId: row.invAddrId, //库存地点id
          invAddrName: row.invAddrName, //库存地点名称
          // addressId: row.addressId, //地址id
          // receiveAddressName: row.receiveAddressName,
          // receiveAddressCode: row.receiveAddressCode,
          purchaserRemark: row.purchaserRemark // 采购备注
          // sourceFrom: 'KT'
        })
      })
      let flag = this.judgeValid1(params)
      if (!flag) {
        this.startEdit(index)
        return
      }
      this.$API.deliverySchedule
        .buyerGoodsDemandPlanInfoUpdateList(params)
        .then(() => {
          this.$toast({
            content: this.$t('编辑交货计划操作成功'),
            type: 'success'
          })
          this.updateList()
        })
        .catch(() => {
          this.startEdit(index)
        })
    },
    async addRow(row, index) {
      console.log(row, '新增行数据')
      // let params = {
      //   // receiveAddressName: row.receiveAddressName,
      //   // receiveAddressCode: row.receiveAddressCode,
      //   // addressId: row.addressId, //地址id
      //   // associatedNumber: row.associatedNumber, //关联工单号
      //   // bomNo: row.bomNo, //BOM
      //   // buyerOrder: row.buyerOrder, //采购订单号
      //   // buyerOrderRowCode: row.buyerOrderRowCode, //采购订单行号
      //   // buyerOrgCode: row.buyerOrgCode, //采购组code
      //   // buyerOrgId: row.buyerOrgId, //采购组id
      //   // buyerOrgName: row.buyerOrgName, //采购组name
      //   // checkDate: 0, //确认日期         //需要给默认值
      //   // companyCode: row.companyCode, //公司code
      //   // companyId: row.companyId, //公司id
      //   // companyName: row.companyName, //公司name
      //   // deliveryStatus: 0, //发货状态            //需要给默认值
      //   // itemCode: row.itemCode, //物料code
      //   // itemId: row.itemId, //物料id
      //   // itemName: row.itemName, //物料name
      //   // itemGroupName: row.itemGroupName, //物料组名称
      //   // itemGroupCode: row.itemGroupCode, //物料组code
      //   // outsourceMethod: row.outsourceMethod, //委外方式
      //   // planGroupCode: row.planGroupCode, //计划组code
      //   // planGroupId: row.planGroupId, //计划组id
      //   // planGroupName: row.planGroupName, //计划组name
      //   // processName: row.processName, //工序名称
      //   // productCode: row.productCode, //产品代码
      //   // projectTextBatch: row.projectTextBatch, //项目文本批次
      //   // saleOrder: row.saleOrder, //销售订单
      //   // saleOrderRowCode: row.saleOrderRowCode, //销售订单行
      //   // scheduleArea: row.scheduleArea, //计划区域
      //   // scheduleType: row.scheduleType, //计划类型
      //   // jit: row.scheduleType === 'JIT' ? 1 : 0,

      //   // // serialNumber: this.generateUUID(), //序列号          //需要给默认值
      //   // tvSupplierDemandPlanItemExtList: row.tvSupplierDemandPlanItemExtList, // 排期数据集合
      //   // siteCode: row.siteCode, //工厂code
      //   // siteId: row.siteId, //工厂id
      //   // siteName: row.siteName, //工厂name
      //   // status: '', //状态                           //需要给默认值
      //   // supplierCheckUser: '', //供应商确认人       //需要给默认值
      //   // supplierCode: row.supplierCode, //供应商
      //   // supplierId: row.supplierId, //供应商id
      //   // supplierName: row.supplierName, //供应商名称
      //   // supplierRemark: '', //供应商备注         //需要给默认值
      //   // systemRemark: '', //系统备注                 //需要给默认值
      //   // version: '', //版本号                      //需要给默认值
      //   // invAddrCode: row.invAddrCode, //库存地点编码
      //   // invAddrId: row.invAddrId, //库存地点id
      //   // invAddrName: row.invAddrName, //库存地点名称
      //   // warehouseKeeper: row.warehouseKeeper, //仓管员
      //   // warehouseQty: 0, //库存数量           //需要给默认值
      //   // workCenter: row.workCenter, //工作中心
      //   // workCenterCode: row.workCenterCode, //工作中心
      //   // workCenterName: row.workCenterName, //工作中心
      //   // itemDescription: row.itemDescription, //物料描述
      //   // deliveryMethod: row.deliveryMethod, //配送方式
      //   // plusBizCirclesCode: row.plusBizCirclesCode, //加工商编码
      //   // plusBizCirclesId: row.plusBizCirclesId, //加工商id
      //   // plusBizCirclesName: row.plusBizCirclesName //加工商名称
      //   ...row
      // }
      // let list = []
      // this.titleList.forEach((item, index) => {
      //   list.push({
      //     timeInfoTimestamp: new Date(`${item} 00:00:00`).getTime(),
      //     total: 0,
      //     buyerNum: row[item]?.split('_')[1] || '',
      //     purchaserRemark: row.purchaserRemark, // 采购备注
      //     release: 0, //下达
      //     limitNum: 0, //限量数量           //需要给默认值
      //     remainingNum: 0, //剩余可创建数量        //需要给默认值
      //     outstandingNum: 0 // 未清订单数量
      //   })
      //   params.tvSupplierDemandPlanItemExtList.forEach((i) => {
      //     if (i.type === 'C') {
      //       i[`planDemandQty${index + 1}`] = row[item]?.split('_')[2] || ''
      //     }
      //     i.dateText = ''
      //   })
      // })
      // // params.item = list
      // let flag = await this.judgeValid(params, list)
      // if (!flag) {
      //   this.startEdit(index)
      //   return
      // }
      const params = { ...row }
      this.titleList.forEach((item, index) => {
        params.tvSupplierDemandPlanItemExtList.forEach((i) => {
          if (i && i.type === 'C') {
            i[`planDemandQty${index + 1}`] = row[item]?.split('_')[2] || ''
          }
          i.dateText = ''
        })
      })
      this.$API.deliverySchedule
        .supplierGoodsDemandPlanInfoUpdateTv({
          ...params.tvSupplierDemandPlanItemExtList[1],
          supplierRemark: params.supplierRemark
        })
        .then(() => {
          this.$toast({
            content: this.$t('保存交货计划操作成功'),
            type: 'success'
          })
          this.updateList()
        })
        .catch(() => {
          this.startEdit(index)
        })
    },
    async judgeValid(params, list) {
      //添加校验
      let flag = true
      if (!params.itemCode) {
        this.$toast({
          content: this.$t('物料信息必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.supplierCode) {
        this.$toast({
          content: this.$t('供应商信息必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.factoryCode) {
        this.$toast({
          content: this.$t('工厂必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      // if (!params.companyCode) {
      //   this.$toast({
      //     content: this.$t('公司必填'),
      //     type: 'warning'
      //   })
      //   flag = false
      //   return
      // }
      // if (!params.scheduleType) {
      //   this.$toast({
      //     content: this.$t('JIT物料必填'),
      //     type: 'warning'
      //   })
      //   flag = false
      //   return
      // }
      let falg1 = true
      await list.forEach((item) => {
        if (item.buyerNum === '') {
          falg1 = false
        }
      })
      if (!falg1) {
        this.$toast({
          content: this.$t('P（需求量）必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.deliveryMethod) {
        this.$toast({
          content: this.$t('配送方式必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (params.deliveryMethod === '0') {
        //配送方式为直送
        if (!params.plusBizCirclesCode || !params.plusBizCirclesName) {
          this.$toast({
            content: this.$t('配送方式为直送时,加工商编码和名称必填'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      if (params.outsourceMethod === '2') {
        //委外方式非委外
        if (params.deliveryMethod === '0') {
          this.$toast({
            content: this.$t('委外方式为非委外时,配送方式需为非直送'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      return flag
    },
    getRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.templateRef1
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = cloneDeep(this.selectedOtherInfo)
      let info = {}
      currentRecords.some((item) => {
        if (item.addId == this.nowEditRowFlag) {
          Object.assign(item, row)
          info = item
        }
      })
      const purchaseScheduleTvRow = sessionStorage.getItem('purchaseScheduleTvRow')
        ? JSON.parse(sessionStorage.getItem('purchaseScheduleTvRow'))
        : {}
      return { ...info, ...purchaseScheduleTvRow }
    },
    actionBegin1(args) {
      console.log(args, '我是111actionBegin')
      if (args.requestType === 'add') {
        let lastColumn1 = cloneDeep(this.lastColumn1)
        lastColumn1.forEach((item) => {
          args.data[item.field] = ''
          if (item.field === 'status') {
            args.data[item.field] = this.$t('新建')
          }
          if (item.field === 'limitNum') {
            args.data[item.field] = '0'
          }
          if (item.field === 'remainingNum') {
            args.data[item.field] = '0'
          }
          if (item.field === 'warehouseQty') {
            args.data[item.field] = '0'
          }
          if (item.field === 'timeInfoTimestamp') {
            args.data[item.field] = new Date()
          }
          if (item.field === 'scheduleType') {
            args.data[item.field] = this.$t('非JIT')
          }
          if (item.field === 'outsourceMethod') {
            args.data[item.field] = '2'
          }
          if (item.field === 'deliveryMethod') {
            args.data[item.field] = '1'
          }
          args.data.isEntry = '2'
        })
        args.data.addId = this.addId++
        this.nowEditRowFlag = args.data.addId
      }
      if (args.requestType == 'beginEdit') {
        this.nowEditRowFlag = args.rowData.addId
        if (!(args.rowData.status == 0 || args.rowData.status == 1 || args.rowData.status == 2)) {
          this.$toast({
            content: this.$t('此状态不可编辑'),
            type: 'warning'
          })
          args.cancel = true
        }
      }
    },
    //点击表格的操作按钮
    handleClickCellTool1(e) {
      console.log('方法2', e)
      if (e.tool.id === 'Publish') {
        this.handlePublish([e.data])
      }
      if (e.tool.id === 'Feedback') {
        this.handleFeedback([e.data])
      }
      if (e.tool.id === 'Cancle') {
        this.handleCancle([e.data])
      }
      if (e.tool.id === 'Close') {
        this.handleClose([e.data])
      }
      if (e.tool.id === 'Confirm') {
        this.handleConfirm([e.data])
      }
    },
    //点击顶部的操作按钮
    handleClickToolBar1(e) {
      console.log('方法1', e)
      console.log(e.grid.getSelectedRecords(), e.toolbar.id)
      // const commonToolbar = [
      //   'ForecastUpdate',
      //   'ForecastAdd',
      //   'ForecastImport',
      //   'Filter',
      //   'Refresh',
      //   'refreshDataByLocal',
      //   'filterDataByLocal',
      //   'resetDataByLocal',
      //   'ForecastExport',
      //   'Feedback',
      //   'Setting'
      // ]
      if (e.toolbar.id === 'closeEdit') {
        e.grid.closeEdit()
        return
      }
      if (e.toolbar.id === 'Add') {
        this.handleAdd()
        return
      }
      if (e.toolbar.id === 'Import') {
        this.handleImport()
        return
      }
      if (e.toolbar.id === 'Export1') {
        this.handleExport()
        return
      }
      if (e.toolbar.id === 'refreshDataByLocal') {
        this.buyerGoodsDemandPlanInfoQuery()
        return
      }
      if (e.toolbar.id === 'filterDataByLocal') {
        // 筛选-过滤
        const { condition, rules: ruleList } = e.rules
        this.forecastCondition = condition
        this.forecastRules = ruleList
        // 采方-获取采方预测信息列表
        this.forecastPageCurrent = 1
        this.buyerGoodsDemandPlanInfoQuery()
        return
      }
      if (e.toolbar.id === 'resetDataByLocal') {
        // 筛选重置
        this.forecastCondition = 'and'
        this.forecastRules = []
        // 采方-获取采方预测信息列表
        this.forecastPageCurrent = 1
        this.buyerGoodsDemandPlanInfoQuery()
        return
      }
      let selectRecords = e.grid.getSelectedRecords()
      // if (!selectRecords.length && !commonToolbar.includes(e.toolbar.id)) {
      //   this.$toast({ content: this.$t('请至少选择一行'), type: 'warning' })
      //   return
      // }
      if (e.toolbar.id === 'Delete') {
        this.handleDelete(selectRecords)
      }
      if (e.toolbar.id === 'Publish') {
        this.handlePublish(selectRecords)
      }
      if (e.toolbar.id === 'Confirm') {
        this.handleConfirm(selectRecords)
      }
      if (e.toolbar.id === 'Feedback') {
        this.handleFeedback(selectRecords)
      }
      if (e.toolbar.id === 'Release') {
        this.handleRelease(selectRecords)
      }
    },
    selectCell() {
      console.log(arguments, 9999)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/_mixin.scss';

.flex-fit {
  // container不是固定命名，根据实际页面样式命名来写
  ::v-deep .e-rowcell.sticky-col-0,
  ::v-deep .e-headercell.sticky-col-0 {
    @include sticky-col;
    left: 0px;
  }
  ::v-deep .e-rowcell.sticky-col-1,
  ::v-deep .e-headercell.sticky-col-1 {
    @include sticky-col;
    left: 50px; // 50px就是前面所有固定列的列宽相加，比如这里就是 0 + 复选框列（50px） = 50px
  }
  ::v-deep .e-rowcell.sticky-col-2,
  ::v-deep .e-headercell.sticky-col-2 {
    @include sticky-col;
    left: 350px;
  }
  ::v-deep .e-rowcell.sticky-col-3,
  ::v-deep .e-headercell.sticky-col-3 {
    @include sticky-col;
    left: 550px;
  }
  ::v-deep .e-rowcell.sticky-col-4,
  ::v-deep .e-headercell.sticky-col-4 {
    @include sticky-col;
    left: 980px;
  }
}
/deep/ .ant-select-selection {
  background: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.42) !important;
}
/deep/ .e-grid .e-altrow {
  background-color: #e9e9e9;
}
/deep/ .e-grid .e-gridcontent {
  td.e-rowcell:not(:first-of-type) {
    padding: 0 0px !important;
  }
}
</style>
