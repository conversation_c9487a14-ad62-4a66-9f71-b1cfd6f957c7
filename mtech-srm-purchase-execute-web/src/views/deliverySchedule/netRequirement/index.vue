<!--净需求查询-->
<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="false"
      :current-tab="currentTab"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    >
      <!-- <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel">
          <mt-form-item prop="syncTime" :label="$t('同步时间')" label-style="top">
            <mt-date-range-picker
              width="300"
              v-model="searchFormModel.syncTime"
              :show-clear-button="true"
              :placeholder="$t('请选择同步时间')"
              @change="(e) => handleDateChange(e, 'syncTime')"
            />
          </mt-form-item>
          <mt-form-item prop="versionNo" :label="$t('版本号')" label-style="top">
            <mt-input
              width="300"
              v-model="searchFormModel.versionNo"
              :show-clear-button="true"
              :placeholder="$t('请输入版本号')"
            />
          </mt-form-item>
          <mt-form-item prop="factoryCode" :label="$t('工厂编号')" label-style="top">
            <select-filter
              width="300"
              :fields="{ text: 'labelShow', vlaue: 'factoryCode' }"
              :request-url="requestUrl"
              :request-key="requestKey"
              :init-val.sync="searchFormModel.factoryCode"
              :other-params="otherParams"
              :label-show-obj="labelShowObj"
              @handleChange="handleChange"
            />
          </mt-form-item>
          <mt-form-item prop="handleState" :label="$t('处理状态')" label-style="top">
            <mt-select
              width="300"
              v-model="searchFormModel.handleState"
              :fields="{ text: 'text', value: 'value' }"
              :data-source="stateList"
              :placeholder="$t('请选择处理状态')"
              :show-clear-button="true"
            />
          </mt-form-item>
        </mt-form>
      </template> -->
    </mt-template-page>
  </div>
</template>

<!-- eslint-disable prettier/prettier -->
<script>
import { PAGE_PLUGIN, handleStatusOption } from './config.js'
import * as UTILS from '@/utils/utils'
import dayjs from 'dayjs'
export default {
  // provide() {
  //   return {
  //     searchFormModelList: [this.searchFormModel]
  //   }
  // },
  data() {
    const currentTab = JSON.parse(localStorage.getItem('tabIndex')) ?? 0
    localStorage.removeItem('tabIndex')
    return {
      currentTab,
      searchFormModel: {},
      stateList: handleStatusOption,
      requestUrl: {
        pre: 'deliverySchedule',
        url: 'getFactoryInfo'
      },
      requestKey: 'fuzzyParam',
      otherParams: {
        page: {
          current: 1,
          pages: 0,
          size: 20
        }
      },
      labelShowObj: { code: 'siteCode', name: 'siteName' },
      pageConfig: PAGE_PLUGIN,
      newJITData: [] //选择完JIT以后的数组
    }
  },
  computed: {},
  mounted() {
    // this.$set(this.pageConfig[0].grid, "dataSource", querylist.data.records);
  },
  methods: {
    // 重置查询条件
    resetSearchForm(...list) {
      console.log('list')
      console.log(list)
    },
    handleChange(e) {
      console.log('工厂')
      console.log(e)
      this.searchFormModel['siteCode'] = e.itemData?.siteCode
      this.searchFormModel['siteName'] = e.itemData.siteName
    },
    // 时间选择
    handleDateChange(e, prefix) {
      if (e.startDate) {
        this.searchFormModel[prefix + 'Start'] =
          dayjs(e.startDate).format('YYYY-MM-DD') + '00:00:00'
        this.searchFormModel[prefix + 'End'] = dayjs(e.endDate).format('YYYY-MM-DD') + '23:59:59'
      } else {
        this.searchFormModel[prefix + 'Start'] = null
        this.searchFormModel[prefix + 'End'] = null
      }
    },
    handleClickToolBar(e) {
      console.log('111111111111')
      const _selectRows = e.grid.getSelectedRecords()
      if (
        _selectRows.length <= 0 &&
        (e.toolbar.id == 'Edit' ||
          e.toolbar.id == 'delete' ||
          e.toolbar.id == 'activate' ||
          e.toolbar.id == 'failure')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //新增
      if (e.toolbar.id === 'Add') {
        this.handleClickAdd()
      }
      //编辑
      else if (e.toolbar.id === 'Edit') {
        this.handleClickEdit(_selectRows)
      }
      // 删除
      else if (e.toolbar.id === 'delete') {
        this.handleClickdelete(_selectRows)
      }
      //激活
      else if (e.toolbar.id === 'activate') {
        this.handleClickactivate(_selectRows)
      }
      //失效
      else if (e.toolbar.id === 'failure') {
        this.handleClickfailure(_selectRows)
      }
      //导入
      else if (e.toolbar.id === 'upload') {
        this.handleClickUpload()
      }
      //导出
      else if (e.toolbar.id === 'Download') {
        this.handleClickDownload(e.rules)
      } else if (e.toolbar.id === 'reset') {
        this.handleClickReset(_selectRows)
      }
    },
    //新增
    handleClickAdd() {
      this.$dialog({
        modal: () =>
          import(/* webpackChunkName: "material/components/SalesAddDialog.vue" */ './detail.vue'),
        data: {
          title: this.$t('新增'),
          headStates: 'Add'
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    //编辑
    handleClickEdit(_selectRows) {
      if (_selectRows.length > 1) {
        this.$toast({ content: this.$t('只能编辑一行'), type: 'warning' })
        return
      }
      this.$dialog({
        modal: () =>
          import(/* webpackChunkName: "material/components/SalesAddDialog.vue" */ './detail.vue'),
        data: {
          title: this.$t('编辑'),
          data: _selectRows,
          headStates: 'edit'
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    //删除
    handleClickdelete(_selectRows) {
      const _selectIds = []
      _selectRows.map((item) => {
        _selectIds.push(item.id)
      })
      let parameter = {
        ids: _selectIds
      }
      this.$API.material.sitelocationmanageBatchdelete(parameter).then(() => {
        this.$toast({
          content: this.$t('删除成功'),
          type: 'success'
        })
        this.$refs.tepPage.refreshCurrentGridData()
      })
    },
    //激活
    handleClickactivate(_selectRows) {
      const _selectIds = []
      _selectRows.map((item) => {
        _selectIds.push(item.id)
      })
      let parameter = {
        ids: _selectIds,
        statusId: 1 //状态1激活,3失效
      }
      this.$API.material.sitelocationmanageBatchupdatestatus(parameter).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
      })
    },
    //失效
    handleClickfailure(_selectRows) {
      const _selectIds = []
      _selectRows.map((item) => {
        _selectIds.push(item.id)
      })
      let parameter = {
        ids: _selectIds,
        statusId: 3 //状态1激活,3失效
      }
      this.$API.material.sitelocationmanageBatchupdatestatus(parameter).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
      })
    },
    //导入
    handleClickUpload() {
      console.log('导入')
      // this.$dialog({
      //   modal: () => import(/* webpackChunkName: "components/upload" */ './locationUpload.vue'),
      //   data: {
      //     title: this.$t('上传')
      //   },
      //   success: () => {
      //     this.$refs.tepPage.refreshCurrentGridData()
      //     this.$toast({
      //       content: this.$t('操作成功'),
      //       type: 'success'
      //     })
      //   }
      // })
    },
    //导出
    handleClickDownload() {
      let rule = this.$refs.tepPage.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        condition: rule.condition || '',
        page: { current: 1, size: 10000 },
        pageFlag: true,
        rules: rule.rules || []
      }
      this.$store.commit('startLoading')
      this.$API.deliverySchedule.netRequirementExport(params).then((res) => {
        this.$store.commit('endLoading')
        // console.log("=========", res);
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      })
    },
    // 重置
    handleClickReset(list = []) {
      if (list.length === 0) {
        this.$toast({ content: this.$t('请勾选需要重置数据'), type: 'warning' })
        return
      }
      const params = list.map((item) => item.versionNo) ?? []
      this.$API.deliverySchedule
        .netRequirementReset(params)
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              content: this.$t('重置成功'),
              type: 'success'
            })
            this.$refs.tepPage.refreshCurrentGridData()
            return
          }
          this.$toast({
            type: 'error',
            content: this.$t('重置失败')
          })
        })
        .catch(() => {
          this.$toast({
            type: 'error',
            content: this.$t('重置失败')
          })
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-subtitle {
  border-left: 3px solid #00469c;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: #2f353c;
  padding-left: 10px;
}
</style>
