<!--净需求详情 -->
<template>
  <div class="full-height">
    <!-- 头部 -->
    <!-- <div class="top-info flex-keep">
      <div class="header-box">
        <div class="middle-blank"></div>
        <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{
          $t('返回')
        }}</mt-button>
      </div>
    </div> -->
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    >
      <!-- <template v-slot:quick-search-form> -->
      <!-- <mt-form ref="searchFormRef" :model="searchFormMdel"> -->
      <!-- <mt-form-item prop="" :label="$t('版本号')" label-style="top">
            <mt-input
              v-model="searchFormModel.version"
              :show-clear-button="true"
              :placeholder="$t('请输入版本号')"
            />
          </mt-form-item>
          <mt-form-item prop="siteCode" :label="$t('工厂')" label-style="top">
            <select-filter
              :fields="{ text: 'labelShow', vlaue: 'siteCode' }"
              :request-url="requestUrl"
              :request-key="requestKey"
              :init-val.sync="searchFormModel.siteCode"
              :other-params="otherParams"
              :label-show-obj="labelShowObj"
              @handleChange="handleChange"
            />
          </mt-form-item>
          <mt-form-item prop="version" :label="$t('需求编号')" label-style="top">
            <mt-input
              v-model="searchFormModel.version"
              :show-clear-button="true"
              :placeholder="$t('请输入需求编号')"
            />
          </mt-form-item>
          <mt-form-item prop="itemCode" :label="$t('物料编号')" label-style="top">
            <mt-input
              v-model="searchFormModel.itemCode"
              :show-clear-button="true"
              :placeholder="$t('请输入物料编号')"
            />
          </mt-form-item>
          <mt-form-item prop="jit" :label="$t('是否JIT')" label-style="top">
            <mt-select
              v-model="searchFormModel.jit"
              :data-source="stateList"
              :placeholder="$t('请选择是否JIT')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="demandType" :label="$t('需求类型')" label-style="top">
            <mt-select
              v-model="searchFormModel.demandType"
              :data-source="stateList"
              :placeholder="$t('请选择需求类型')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="handleState" :label="$t('处理状态')" label-style="top">
            <mt-select
              v-model="searchFormModel.handleState"
              :data-source="stateList"
              :placeholder="$t('请选择处理状态')"
              :show-clear-button="true"
            />
          </mt-form-item> -->
      <!-- 需求时间 -->
      <!-- <mt-form-item prop="demandDate" :label="$t('需求日期')" label-style="top">
      <  <mt-date-range-picker
      <    v-model="searchFormModel.demandDate"
      <    :placeholder="$t('请选择需求日期')"
      <    :show-clear-button="true"
      <    @change="(e) => handleDateChange(e, 'date')"
      <  />
      <</mt-form-item> -->
      <!-- 同步时间 -->
      <!-- <mt-form-item prop="syncTime" :label="$t('同步时间')" label-style="top">
      <  <mt-date-range-picker
      <    v-model="searchFormModel.syncTime"
      <    :placeholder="$t('请选择同步时间')"
      <    :show-clear-button="true"
      <    @change="(e) => dateChange(e, 'time')"
      <  />
      <</mt-form-item> -->
      <!-- </mt-form> -->
      <!-- </template> -->
    </mt-template-page>
  </div>
</template>

<script>
import { PAGE_PLUGIN } from './detailConfig.js'
import { download, getHeadersFileName } from '@/utils/file.js'
import dayjs from 'dayjs'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      stateList: [],
      requestUrl: {
        pre: 'deliverySchedule',
        url: 'getFactoryInfo'
      },
      requestKey: 'fuzzyParam',
      otherParams: {
        page: {
          current: 1,
          pages: 0,
          size: 20
        }
      },
      labelShowObj: { code: 'siteCode', name: 'siteName' },
      pageConfig: PAGE_PLUGIN,
      newJITData: [] //选择完JIT以后的数组
    }
  },
  computed: {},
  mounted() {
    // this.$set(this.pageConfig[0].grid, "dataSource", querylist.data.records);
  },
  methods: {
    goBack() {
      this.$router.push({ name: 'net-requirement' })
    },
    // 需求时间选择
    handleDateChange(e, prefix) {
      if (e.startDate) {
        this.searchFormMdel['start' + prefix] = dayjs(e.startDate).format('YYYY-MM-DD') + '00:00:00'
        this.searchFormMdel['end' + prefix] = dayjs(e.endDate).format('YYYY-MM-DD') + '23:59:59'
      } else {
        this.searchFormMdel['start' + prefix] = null
        this.searchFormMdel['end' + prefix] = null
      }
    },
    // 同步选择
    dateChange(e, prefix) {
      if (e.startDate) {
        this.searchFormMdel['start' + prefix] = dayjs(e.startDate).format('YYYY-MM-DD') + '00:00:00'
        this.searchFormMdel['end' + prefix] = dayjs(e.endDate).format('YYYY-MM-DD') + '23:59:59'
      } else {
        this.searchFormMdel['start' + prefix] = null
        this.searchFormMdel['end' + prefix] = null
      }
    },
    handleClickToolBar(e) {
      const _selectRows = e.grid.getSelectedRecords()
      if (
        _selectRows.length <= 0 &&
        (e.toolbar.id == 'Download' ||
          e.toolbar.id == 'Edit' ||
          e.toolbar.id == 'delete' ||
          e.toolbar.id == 'activate' ||
          e.toolbar.id == 'failure')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //新增
      if (e.toolbar.id === 'Add') {
        this.handleClickAdd()
      }
      //编辑
      else if (e.toolbar.id === 'Edit') {
        this.handleClickEdit(_selectRows)
      }
      // 删除
      else if (e.toolbar.id === 'delete') {
        this.handleClickdelete(_selectRows)
      }
      //激活
      else if (e.toolbar.id === 'activate') {
        this.handleClickactivate(_selectRows)
      }
      //失效
      else if (e.toolbar.id === 'failure') {
        this.handleClickfailure(_selectRows)
      }
      //导入
      else if (e.toolbar.id === 'upload') {
        this.handleClickUpload()
      }
      //导出
      else if (e.toolbar.id === 'Download') {
        this.handleClickDownload(_selectRows)
      }
    },
    //新增
    handleClickAdd() {
      this.$dialog({
        modal: () =>
          import(/* webpackChunkName: "material/components/SalesAddDialog.vue" */ './detail.vue'),
        data: {
          title: this.$t('新增'),
          headStates: 'Add'
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    //编辑
    handleClickEdit(_selectRows) {
      if (_selectRows.length > 1) {
        this.$toast({ content: this.$t('只能编辑一行'), type: 'warning' })
        return
      }
      this.$dialog({
        modal: () =>
          import(/* webpackChunkName: "material/components/SalesAddDialog.vue" */ './detail.vue'),
        data: {
          title: this.$t('编辑'),
          data: _selectRows,
          headStates: 'edit'
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    //删除
    handleClickdelete(_selectRows) {
      const _selectIds = []
      _selectRows.map((item) => {
        _selectIds.push(item.id)
      })
      let parameter = {
        ids: _selectIds
      }
      this.$API.material.sitelocationmanageBatchdelete(parameter).then(() => {
        this.$toast({
          content: this.$t('删除成功'),
          type: 'success'
        })
        this.$refs.tepPage.refreshCurrentGridData()
      })
    },
    //激活
    handleClickactivate(_selectRows) {
      const _selectIds = []
      _selectRows.map((item) => {
        _selectIds.push(item.id)
      })
      let parameter = {
        ids: _selectIds,
        statusId: 1 //状态1激活,3失效
      }
      this.$API.material.sitelocationmanageBatchupdatestatus(parameter).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
      })
    },
    //失效
    handleClickfailure(_selectRows) {
      const _selectIds = []
      _selectRows.map((item) => {
        _selectIds.push(item.id)
      })
      let parameter = {
        ids: _selectIds,
        statusId: 3 //状态1激活,3失效
      }
      this.$API.material.sitelocationmanageBatchupdatestatus(parameter).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
      })
    },
    //导入
    handleClickUpload() {
      console.log('导入')
      // this.$dialog({
      //   modal: () => import(/* webpackChunkName: "components/upload" */ './locationUpload.vue'),
      //   data: {
      //     title: this.$t('上传')
      //   },
      //   success: () => {
      //     this.$refs.tepPage.refreshCurrentGridData()
      //     this.$toast({
      //       content: this.$t('操作成功'),
      //       type: 'success'
      //     })
      //   }
      // })
    },
    //导出
    handleClickDownload(_selectRows) {
      console.log('导出')
      if (_selectRows.length > 1) {
        this.$toast({ content: this.$t('只能编辑一行'), type: 'warning' })
        return
      }
      const id = _selectRows[0].id
      const pramas = {
        accountId: id
      }
      this.$store.commit('startLoading')
      this.$API.material.locationExportData(pramas).then((res) => {
        this.$store.commit('endLoading')
        // console.log("=========", res);
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-subtitle {
  border-left: 3px solid #00469c;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: #2f353c;
  padding-left: 10px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  // padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  .header-box {
    // border-bottom: 1px solid #e6e9ed;
    background-color: #fff;
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
  }
}
</style>
