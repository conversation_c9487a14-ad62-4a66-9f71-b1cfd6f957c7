import { i18n } from '@/main.js'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName } from '@/utils/utils'
import { columnData as columnData2 } from './detailConfig'
import utils from '@/utils/utils'
const handleStatusOption = [
  {
    label: i18n.t('原始'),
    value: '0'
  },
  {
    label: i18n.t('正在分配计划'),
    value: '1'
  },
  {
    label: i18n.t('已分配计划'),
    value: '2'
  },
  {
    label: i18n.t('分配计划失败'),
    value: '3'
  },
  {
    label: i18n.t('正在传入'),
    value: '9'
  },
  {
    label: i18n.t('原始状态作废'),
    value: '10'
  },
  {
    label: i18n.t('分配计划失败时作废'),
    value: '13'
  }
]

const stringToDate = (data) => {
  const { formatString, value } = data
  if (formatString) {
    const date = new Date(Number(value))
    if (!date.getTime()) {
      return value
    } else {
      return utils.formateTime(date, formatString)
    }
  } else {
    return value
  }
}

export const columnData1 = [
  // {
  //   width: "50",
  //   type: "checkbox",
  // },
  {
    field: 'syncTime',
    headerText: i18n.t('同步时间'),
    width: '120',
    maxlength: '40',
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<span>{{ data.syncTime }}</span>`,
          data() {
            return {
              data: {}
            }
          },
          methods: {}
        })
      }
    },
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    field: 'versionNo',
    headerText: i18n.t('版本号'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<span>{{ data.versionNo }}</span>`,
          data() {
            return {
              data: {},
              versionStyle: {
                'text-decoration': 'underline'
              }
            }
          },
          methods: {
            // toDetail() {
            //   this.$router.push({
            //     path: 'net-requirement-detail',
            //     query: {...this.data}
            //   })
            // }
          }
        })
      }
    }
  },
  {
    field: 'factoryCode',
    headerText: i18n.t('工厂编号'),
    searchOptions: {
      ...MasterDataSelect.factorySupplierAddress
    },
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.factoryCode, data?.factoryName)
    }
  },
  {
    field: 'dtlItemQty',
    headerText: i18n.t('本次传输明细行数')
  },
  {
    field: 'allocationStatus',
    headerText: i18n.t('处理状态'),
    selectOptions: handleStatusOption,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('原始'),
        1: i18n.t('正在分配计划'),
        2: i18n.t('已分配计划'),
        3: i18n.t('分配计划失败'),
        10: i18n.t('原始状态作废'),
        13: i18n.t('分配计划失败时作废'),
        19: i18n.t('传入失败')
      }
    }
  },
  {
    field: 'allocationDesc',
    headerText: i18n.t('处理说明')
  },
  {
    field: 'syncStartTime',
    headerText: i18n.t('生成交货计划开始时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    template: function () {
      return {
        template: Vue.component('actionView', {
          template: `<span>{{ data.syncStartTime | transformTime }}</span>`,
          data() {
            return {
              data: {}
            }
          },
          filters: {
            transformTime(data) {
              return stringToDate({ formatString: 'YYYY-mm-dd HH:MM:SS', value: data })
            }
          }
        })
      }
    }
  },
  {
    field: 'syncEndTime',
    headerText: i18n.t('生成交货计划结束时间'),
    template: function () {
      return {
        template: Vue.component('actionView', {
          template: `<span>{{ data.syncEndTime | transformTime }}</span>`,
          data() {
            return {
              data: {}
            }
          },
          filters: {
            transformTime(data) {
              return stringToDate({ formatString: 'YYYY-mm-dd HH:MM:SS', value: data })
            }
          }
        })
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  }
]
export const PAGE_PLUGIN = [
  {
    title: i18n.t('净需求同步记录'),
    toolbar: [
      {
        id: 'reset',
        icon: 'icon_solid_Reset',
        title: i18n.t('重置状态')
      }
    ],
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    isuseCustomSearch: true,
    gridId: 'aea88eb4-c33d-4900-87ba-bc7a6140517f',
    grid: {
      columnData: columnData1,
      allowEditing: false,
      lineSelection: 0,
      asyncConfig: {
        url: '/srm-purchase-execute/tenant/bdNetDemand/query',
        ignoreDefaultSearch: true,
        params: {}
      }
    }
  },
  {
    title: i18n.t('净需求明细'),
    toolbar: [
      {
        id: 'Download',
        icon: 'icon_solid_export',
        title: i18n.t('导出')
      }
    ],
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    isuseCustomSearch: true,
    gridId: 'bbfcb04f-6352-4da1-a329-47e19c64dddd',
    grid: {
      columnData: columnData2,
      allowEditing: false,
      // dataSource: [],
      asyncConfig: {
        url: '/srm-purchase-execute/tenant/bdNetDemand/query/item',
        ignoreDefaultSearch: true,
        params: {}
      }
    }
  }
]
