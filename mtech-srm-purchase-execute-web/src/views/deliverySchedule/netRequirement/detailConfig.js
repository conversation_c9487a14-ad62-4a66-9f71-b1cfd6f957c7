/* eslint-disable prettier/prettier */
import { i18n } from '@/main.js'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName } from '@/utils/utils'

const toolbar = []
export const columnData = [
  // {
  //   width: "50",
  //   type: "checkbox",
  // },
  {
    field: 'syncTime',
    headerText: i18n.t('同步时间'),
    width: '120',
    maxlength: '40',
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<span>{{ data.syncTime }}</span>`,
          data() {
            return {
              data: {}
            }
          },
          methods: {}
        })
      }
    },
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    field: 'versionNo',
    headerText: i18n.t('版本号'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<span>{{ data.versionNo }}</span>`,
          data() {
            return {
              data: {}
            }
          },
          methods: {}
        })
      }
    }
  },
  {
    field: 'factoryCode',
    headerText: i18n.t('工厂编号'),
    searchOptions: {
      ...MasterDataSelect.factoryAddress,
      renameField: 'factoryCode'
    },
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.factoryCode, data?.factoryName)
    }
  },
  {
    field: 'demandCode',
    headerText: i18n.t('需求编号')
  },
  {
    field: 'materialCode',
    headerText: i18n.t('物料编号')
  },
  {
    field: 'jitFlag',
    headerText: i18n.t('是否JIT'),
    valueConverter: {
      type: 'map',
      map: {
        0: '否',
        1: '是'
      }
    }
  },
  {
    field: 'demandQty',
    headerText: i18n.t('需求数量')
  },
  {
    field: 'demandDate',
    headerText: i18n.t('需求日期'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<span>{{ data.demandDate }}</span>`,
          data() {
            return {
              data: {}
            }
          },
          methods: {}
        })
      }
    },
    searchOptions: { ...MasterDataSelect.dateRange }
  },
  {
    field: 'demandType',
    headerText: i18n.t('需求类型'),
    valueConverter: {
      type: 'map',
      map: {
        SUBREQ: '标准委外',
        ORDRES: '工序委外',
        '': '普通'
      }
    }
  },
  {
    field: 'allocationStatus',
    headerText: i18n.t('处理状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('原始'),
        1: i18n.t('正在分配计划'),
        2: i18n.t('已分配计划'),
        3: i18n.t('分配计划失败'),
        10: i18n.t('原始状态作废'),
        11: i18n.t('正在分配计划时作废'),
        12: i18n.t('已分配计划后作废'),
        13: i18n.t('分配计划失败时作废'),
        14: i18n.t('未到发布期作废')
      }
    }
  },
  {
    field: 'allocationDesc',
    headerText: i18n.t('处理说明')
  },
  {
    field: 'systemRemark',
    headerText: i18n.t('系统备注')
  },
  {
    field: 'prodtWorkNo',
    headerText: i18n.t('生产工单号')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('加工供应商编号'),
    width: 200
  },
  {
    field: 'orderNo',
    headerText: i18n.t('委外组件采购订单'),
    width: 200
  },
  {
    field: 'orderItemNo',
    headerText: i18n.t('委外组件采购订单行号'),
    width: 200
  }
]
export const PAGE_PLUGIN = [
  {
    toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    isuseCustomSearch: true,
    grid: {
      columnData,
      allowEditing: false,
      // dataSource: [],
      asyncConfig: {
        url: '/srm-purchase-execute/tenant/bdNetDemand/query/item',
        params: {}
      }
    }
  }
]
