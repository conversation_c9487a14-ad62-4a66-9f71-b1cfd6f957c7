<!-- 发布校验 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :height="400"
    :width="600"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <div style="margin-top: 18px; font-size: 16px">
      {{ $t('有数据未维护报关信息，请及时跟进供方填写报关要素信息进度！') }}
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.handleDownload,
          buttonModel: { isPrimary: 'true', content: this.$t('下载无报关要素清单') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('继续发布') }
        }
      ],
      formData: {}
    }
  },
  mounted() {},
  methods: {
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, params } = args
      this.dialogTitle = title
      this.formData = params
    },
    beforeOpen() {
      this.formData = {}
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.formData = {}
    },
    handleClose() {
      this.formData = {}
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$emit('confirm', this.formData, 'Publish')
      this.handleClose()
    },
    handleDownload() {
      this.$emit('download', this.formData)
      this.handleClose()
    }
  }
}
</script>
