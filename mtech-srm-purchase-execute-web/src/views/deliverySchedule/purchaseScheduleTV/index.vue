<template>
  <!-- 屏采交货计划-采方 -->
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      @reset="handleCustomReset"
      @search="handleCustomSearch('handle')"
    >
      <mt-form
        ref="searchFormRef"
        :model="searchFormModel"
        :rules="{
          bigVersionNo: [{ required: true, message: $t('请输入'), trigger: 'blur' }]
        }"
      >
        <mt-form-item prop="bigVersionNo" :label="$t('版本号')" label-style="top">
          <!-- <mt-input
                  v-model="searchFormModel.bigVersionNo"
                  :show-clear-button="true"
                  :placeholder="$t('请输入版本号')"
                /> -->
          <mt-select
            v-model="searchFormModel.bigVersionNo"
            :data-source="versionList"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="factoryCode" :label="$t('工厂')" label-style="top">
          <!-- <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.factoryCode"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择工厂')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          ></RemoteAutocomplete> -->
          <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.factoryCode"
            url="/srm-purchase-execute/tenant/common/permission/querySiteList"
            multiple
            :placeholder="$t('请选择工厂')"
            :fields="{ text: 'dimensionNameValue', value: 'dimensionCodeValue' }"
            records-position="data"
            params-key="keyWord"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="supplierCodes" :label="$t('供应商编码')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="supplierCodes"
            @change="supplierChange"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierName"
            :placeholder="$t('支持模糊查询')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="structSeqNos" :label="$t('结构序号')" label-style="top">
          <mt-input
            v-model="structSeqNos"
            @change="structSeqNoChange"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="serialNo" :label="$t('序列号')" label-style="top">
          <mt-input v-model="searchFormModel.serialNo" />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.status"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :data-source="statusOptions"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="planMemberCode" :label="$t('计划员')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.planMemberCode"
            :data-source="plannerListOptions"
            :fields="{ text: 'codeAndName', value: 'userCode' }"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="supplierEmptyFlag" :label="$t('供应商为空')" label-style="top">
          <mt-select
            v-model="searchFormModel.supplierEmptyFlag"
            :data-source="[
              { text: $t('否'), value: 2 },
              { text: $t('是'), value: 1 }
            ]"
            :show-clear-button="true"
            :placeholder="$t('请选择供应商是否为空')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="itemGroups" :label="$t('物料组')" label-style="top">
          <div style="display: flex">
            <mt-input
              style="flex: 1"
              v-model="itemGroups"
              @change="itemGroupChange"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            />
            <mt-checkbox
              v-model="searchFormModel.joinMaterialGroup"
              @change="(e) => handleChange(e, 'joinMaterialGroup')"
              :label="$t('关联查询')"
              style="vertical-align: sub; display: inline-block; margin-left: 5px; width: 78px"
            />
          </div>
        </mt-form-item>
        <mt-form-item prop="itemCodes" :label="$t('物料编码')" label-style="top">
          <div style="display: flex">
            <mt-input
              style="flex: 1"
              v-model="itemCodes"
              @change="itemChange"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            />
            <mt-checkbox
              v-model="searchFormModel.joinMaterialCode"
              @change="(e) => handleChange(e, 'joinMaterialCode')"
              :label="$t('关联查询')"
              style="vertical-align: sub; display: inline-block; margin-left: 5px; width: 78px"
            />
          </div>
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input v-model="searchFormModel.itemName" :placeholder="$t('支持模糊查询')"></mt-input>
        </mt-form-item>
        <mt-form-item prop="typeList" :label="$t('类型')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.typeList"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :data-source="[
              { text: $t('D(原始需求)'), value: 'D' },
              { text: $t('P(需求量)'), value: 'P' },
              { text: $t('C(承诺量)'), value: 'C' }
            ]"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        :row-config="{ height: rowHeight }"
        :columns="columns"
        :table-data="tableData"
        height="auto"
        header-align="left"
        align="left"
        show-overflow
        style="padding-top: unset"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true,
          activeMethod: this.activeRowMethod
        }"
        :cell-style="cellStyle"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        @edit-closed="editComplete"
        @edit-actived="editBegin"
        @edit-disabled="editDisabledEvent"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            v-permission="item.permission"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #supplierCodeEdit="{ row }">
          <vxe-pulldown ref="xDown" transfer>
            <template #default>
              <vxe-input
                :disabled="row.status === 3"
                :value="row.supplierCode"
                :placeholder="$t('请选择供应商')"
                readonly
                @click="focusSupplierCode"
              ></vxe-input>
            </template>
            <template #dropdown>
              <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                @keyup="keyupSupplierCode"
                style="width: 100%"
              ></vxe-input>
              <vxe-list
                height="200"
                class="predict-vxe-dropdown"
                :data="supplierCodeOptions"
                auto-resize
              >
                <template #default="{ items }">
                  <div
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.value"
                    @click="selectSupplierCode(item, row)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template>
        <template #invAddrCodeEdit="{ row }">
          <vxe-pulldown ref="xDownInvAddr" transfer>
            <template #default>
              <vxe-input
                :disabled="row.status === 3"
                :value="row.invAddrCode"
                :placeholder="$t('请选择库存地点')"
                readonly
                @click="(e) => focusInvAddrCode(e, row)"
              ></vxe-input>
            </template>
            <template #dropdown>
              <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                @keyup="(e) => keyupInvAddrCode(e, row)"
                style="width: 100%"
              ></vxe-input>
              <vxe-list
                height="200"
                class="predict-vxe-dropdown"
                :data="invAddrOptions"
                auto-resize
              >
                <template #default="{ items }">
                  <div
                    v-show="invAddrOptions.length"
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.value"
                    @click="selectInvAddrCode(item, row)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                  <div v-show="!invAddrOptions.length" class="predict-vxe-list-item">
                    <span>{{ $t('暂无数据') }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template>
        <template #plusBizCirclesCodeEdit="{ row }">
          <vxe-pulldown ref="xDown1" transfer>
            <template #default>
              <vxe-input
                :disabled="row.status === 3"
                :value="row.plusBizCirclesCode"
                :placeholder="$t('请选择加工商')"
                readonly
                @click="focusProcessorCode"
              ></vxe-input>
            </template>
            <template #dropdown>
              <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                @keyup="keyupProcessorCode"
                style="width: 100%"
              ></vxe-input>
              <vxe-list
                height="200"
                class="predict-vxe-dropdown"
                :data="processorCodeOptions"
                auto-resize
              >
                <template #default="{ items }">
                  <div
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.value"
                    @click="selectProcessorCode(item, row)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template>
        <template #purchaseOrderNoEdit="{ row }">
          <vxe-pulldown ref="xDownPurchaseOrder" transfer>
            <template #default>
              <vxe-input
                :disabled="row.status === 3"
                :value="row.purchaseOrderNo"
                :placeholder="$t('请选择采购订单')"
                readonly
                @click="focusPurchaseOrder"
              ></vxe-input>
            </template>
            <template #dropdown>
              <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                @keyup="keyupPurchaseOrder"
                style="width: 100%"
              ></vxe-input>
              <vxe-list
                height="200"
                class="predict-vxe-dropdown"
                :data="purchaseOrderOptions"
                auto-resize
              >
                <template #default="{ items }">
                  <div
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.value"
                    @click="selectPurchaseOrder(item, row)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template>
        <template #forecastTypeDefault="{}">
          <div v-for="(item, index) in forecastTypeOptions" :key="index">
            <div
              v-if="item === $t('D(原始需求)') && typeList.includes('D')"
              class="vxe-cell-border"
            >
              {{ item }}
            </div>
            <div v-if="item === $t('P(需求量)') && typeList.includes('P')" class="vxe-cell-border">
              <span style="color: red">*</span>
              {{ item }}
            </div>
            <div v-if="item === $t('C(承诺量)') && typeList.includes('C')" class="vxe-cell-border">
              {{ item }}
            </div>
          </div>
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />

    <PublishValidate
      ref="publishValidateRef"
      @confirm="handleOperator"
      @download="handleDownload"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import {
  StatusSearchOptions,
  ForecastTypeDataSource,
  columnData,
  NewRowData,
  ToolBar
} from './config'
import { utils } from '@mtech-common/utils'
import * as UTILS from '@/utils/utils'
import PublishValidate from './components/PublishValidate.vue'
export default {
  components: {
    ScTable,
    CollapseSearch,
    PublishValidate
  },
  data() {
    return {
      rowHeight: 74,
      supplierCodes: '',
      itemCodes: '',
      itemGroups: '',
      structSeqNos: '',
      addId: '1',
      toolbar: ToolBar,
      supplierCodeOptions: [],
      getSupplierDataSource: () => {}, // 供应商 下拉选项
      invAddrOptions: [],
      getInvAddrDataSource: () => {},
      processorCodeOptions: [],
      getProcessorDataSource: () => {}, // 加工商 下拉选项
      purchaseOrderOptions: [],
      getPurchaseOrderDataSource: () => {}, // 采购订单 下拉选项
      searchFormModel: {
        bigVersionNo: '',
        joinMaterialCode: false,
        joinMaterialGroup: false
      },
      titleList: [],
      tableData: [],
      columns: columnData,
      forecastPageCurrent: 1,
      forecastPageSettings: {
        currentPage: 1,
        enableQueryString: false,
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      statusOptions: StatusSearchOptions,
      forecastTypeOptions: ForecastTypeDataSource,
      isEdit: false,
      versionList: [],
      plannerListOptions: [],
      warningContent: this.$t('此状态数据不可编辑'),
      typeList: ['D', 'P', 'C']
    }
  },
  mounted() {
    this.getPlannerList()
    this.buyerGoodsDemandPlanInfoQueryVersionTv()
    this.getSupplierDataSource = utils.debounce(this.getSupplier, 1000)
    this.getProcessorDataSource = utils.debounce(this.getSupplier, 1000)
    this.getInvAddrDataSource = utils.debounce(this.getInvAddr, 1000)
    this.getPurchaseOrderDataSource = utils.debounce(this.getPurchaseOrder, 1000)
  },
  methods: {
    handleChange(e, labelName) {
      this.searchFormModel[labelName] = e.checked
    },
    cellStyle({ row, column }) {
      if (column.field === 'itemCode') {
        if (row.status == 4) {
          return {
            color: 'red'
          }
        }
      }
      if (column.field === 'status') {
        if (row.status == 4) {
          return {
            color: 'red'
          }
        }
      }
    },
    // rowStyle({ row }) {
    //   let isWarning = this.titleList.some((i) => {
    //     return row[`title_${i}`]['buyerNum'] != row[`title_${i}`]['supplierNum'] && row.status > 2
    //   })
    //   if (isWarning) {
    //     return {
    //       backgroundColor: '#fdeeea'
    //       // color: '#ffffff'
    //     }
    //   }
    // },
    // 获取版本下拉列表
    buyerGoodsDemandPlanInfoQueryVersionTv() {
      this.$API.deliverySchedule.buyerGoodsDemandPlanInfoQueryVersionTv().then((res) => {
        const { data, code } = res
        if (code === 200) {
          this.versionList = data
          this.searchFormModel.bigVersionNo = data[0]
          // this.buyerGoodsDemandPlanInfoQuery()
        }
      })
    },
    // 获取计划员下拉
    getPlannerList() {
      this.$API.predictCollaboration.getPlannerAllName().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.plannerListOptions = data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.userCode} - ${i.userName}`
            }
          })
          this.plannerListOptions.push({
            userCode: '',
            codeAndName: this.$t('计划员空')
          })
        }
      })
    },
    // 查询条件操作供应商编码切割
    supplierChange(e) {
      if (e) {
        this.searchFormModel.supplierCodes = this.supplierCodes.split(' ')
      } else {
        this.searchFormModel.supplierCodes = null
      }
    },
    // 查询条件操作物料编码切割
    itemChange(e) {
      if (e) {
        this.searchFormModel.itemCodes = this.itemCodes.split(' ')
      } else {
        this.searchFormModel.itemCodes = null
      }
    },
    // 查询条件操作物料组切割
    itemGroupChange(e) {
      if (e) {
        this.searchFormModel.itemGroups = this.itemGroups.split(' ')
      } else {
        this.searchFormModel.itemGroups = null
      }
    },
    // 查询条件操作结构序号切割
    structSeqNoChange(e) {
      if (e) {
        this.searchFormModel.structSeqNos = this.structSeqNos.split(' ')
      } else {
        this.searchFormModel.structSeqNos = null
      }
    },
    // 采购订单插槽方法
    focusPurchaseOrder() {
      this.$refs.xDownPurchaseOrder.showPanel()
    },
    keyupPurchaseOrder(e) {
      this.getPurchaseOrderDataSource(e)
    },
    selectPurchaseOrder(e, row) {
      row.purchaseOrderNo = e.value
    },
    // 查询采购订单
    getPurchaseOrder(args) {
      const { value } = args
      let params = {
        condition: 'and',
        page: { current: 1, size: 50 },
        defaultRules: [
          {
            label: this.$t('采购订单号'),
            field: 'orderCode',
            type: 'string',
            operator: 'contains',
            value
          }
        ]
      }
      this.$API.purchaseOrder.purOrderQueryOrder(params).then((res) => {
        let list = res.data.records || []
        this.purchaseOrderOptions = list.map((item) => {
          return {
            label: item,
            value: item
          }
        })
      })
    },
    // 库存地点插槽方法
    focusInvAddrCode(e, row) {
      if (!row.factoryCode) {
        this.$toast({ content: this.$t('请先选择工厂'), type: 'warning' })
        return false
      }
      this.$refs.xDownInvAddr.showPanel()
    },
    keyupInvAddrCode(e, row) {
      this.getInvAddrDataSource(e, row)
    },
    selectInvAddrCode(e, row) {
      row.invAddrId = e.id
      row.invAddrCode = e.locationCode
      row.invAddrName = e.locationName
    },
    //查询库存地点
    getInvAddr(args, row) {
      const { value } = args
      let str = value
      let params = {
        commonCode: row.factoryCode, //工厂code
        dataLimit: 50,
        fuzzyParam: str //搜索参数
      }
      if (!params.commonCode) {
        console.log('工厂没有')
        return
      }
      this.$API.masterData.getLocationFuzzyQuery(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          if (item.externalCode) {
            item.locationCode = item.externalCode
            if (item.externalName) {
              item.locationName = item.externalName
            }
          }
          item.label = `${item.locationCode}-${item.locationName}`
        })
        this.invAddrOptions = list
      })
    },
    // 加工商插槽方法
    focusProcessorCode() {
      this.$refs.xDown1.showPanel()
    },
    keyupProcessorCode(e) {
      this.getProcessorDataSource({ ...e, type: 'processorCode' })
    },
    selectProcessorCode(e, row) {
      row.plusBizCirclesCode = e.supplierCode
      row.plusBizCirclesId = e.id
      row.plusBizCirclesName = e.supplierName
    },
    // 供应商插槽方法
    focusSupplierCode() {
      this.$refs.xDown.showPanel()
    },
    keyupSupplierCode(e) {
      this.getSupplierDataSource({ ...e, type: 'supplierCode' })
    },
    selectSupplierCode(e, row) {
      row.supplierCode = e.supplierCode
      row.supplierId = e.id
      row.supplierName = e.supplierName
    },
    // 主数据 供应商
    getSupplier(args) {
      const { value, type } = args
      const params = {
        fuzzyNameOrCode: value ? value : ''
      }
      this.$API.masterData.getSupplier(params).then((res) => {
        if (res) {
          const list = res?.data || []
          const newData = list.map((i) => {
            return {
              ...i,
              label: `${i.supplierCode}-${i.supplierName}`,
              value: i.supplierCode
            }
          })
          if (type === 'supplierCode') {
            this.supplierCodeOptions = [...newData]
          } else if (type === 'processorCode') {
            this.processorCodeOptions = [...newData]
          }
        }
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 校验点击的对象是否为取消编辑按钮
        if (args.$event.target.innerText === this.$t('取消编辑')) {
          // 清除编辑状态
          this.$refs.xTable.$refs.xGrid.clearEdit()
          this.handleCustomSearch()
          return
        }
        // 远程数据才有$event属性
        //1、 校验必填 没通过就是this.$refs.xTable.$refs.xGrid.setEditRow(row)
        // if (!this.isValidData(row)) {
        //   // 当出现错误时，指定行进入编辑状态
        //   this.$refs.xTable.$refs.xGrid.setEditRow(row)
        //   return
        // }
        //2、 判断是否有row.bigVersionNo调新增或者编辑接口
        if (row.bigVersionNo) {
          this.updateRow(row)
        } else {
          this.updateRow(row)
        }
        //3、 接口调用成功调刷新接口
      }
    },
    updateRow(row) {
      let params = {
        ...row
      }
      let list = []
      // console.log(this.)
      this.titleList.forEach((item, index) => {
        list.push({
          timeInfoTimestamp: new Date(`${item} 00:00:00`).getTime(),
          total: 0,
          buyerNum: row[`title_${item}`]['buyerNum'] || '',
          purchaserRemark: row.purchaserRemark, // 采购备注
          release: 0, //下达
          limitNum: 0, //限量数量           //需要给默认值
          remainingNum: 0, //剩余可创建数量        //需要给默认值
          outstandingNum: 0 // 未清订单数量
        })
        params.tvBuyerDemandPlanExtList.forEach((i) => {
          if (i.type === 'P') {
            i[`planDemandQty${index + 1}`] = row[`title_${item}`]['buyerNum'] || 0
          }
          i.dateText = ''
        })
      })
      // const tvSupplierDemandPlanItemExtList = params.tvBuyerDemandPlanExtList.filter((i) => {
      //   return i.type === 'P'
      // })
      this.$API.deliverySchedule
        .buyerGoodsDemandPlanInfoUpdateTv({
          ...params,
          tvBuyerDemandPlanExtList: [params.tvBuyerDemandPlanExtList[1]],
          demandDateList: this.titleList
        })
        .then(() => {
          this.$toast({
            content: this.$t('保存交货计划操作成功'),
            type: 'success'
          })
          this.handleCustomSearch()
        })
        .catch(() => {
          this.$refs.xTable.$refs.xGrid.setEditRow(row)
        })
    },
    editBegin(args) {
      const { row } = args
      this.columns.forEach((item) => {
        // 反馈-满足状态固定列全都不可编辑，没找到vxe在editRender中相应的api，就使用了通过forEach改列配置属性的办法
        if (item.needChange) {
          if (row.status === 3) {
            this.$set(item.editRender.attrs, 'disabled', true)
          } else {
            this.$set(item.editRender.attrs, 'disabled', false)
          }
        }
      })
      if (args.$event) {
        // 远程数据才有$event属性
        this.getSupplierDataSource({ value: row.supplierCode, type: 'supplierCode' })
        this.getInvAddrDataSource({ value: row.invAddrCode }, row)
        this.getProcessorDataSource({ value: row.processorCode, type: 'processorCode' })
        this.getPurchaseOrderDataSource({ value: row.purchaseOrderNo })
      } else {
        this.getSupplierDataSource({ value: '', type: 'supplierCode' })
        this.getProcessorDataSource({ value: '', type: 'processorCode' })
      }
      this.isEdit = true
    },
    activeRowMethod({ row }) {
      // 已确认的数据不可修改
      if (!(row.status === 0 || row.status === 1 || row.status === 3 || row.status === 4)) {
        return false
      }
      const { externalCode } = JSON.parse(sessionStorage.getItem('userInfo'))
      if (row.planMemberCode !== externalCode) {
        this.warningContent = this.$t('选中的数据为其他计划员的数据，无法编辑')
        return false
      }
      return true
    },
    editDisabledEvent() {
      this.$toast({
        content: this.warningContent,
        type: 'warning'
      })
      this.warningContent = this.$t('此状态数据不可编辑')
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectRecords = $grid.getCheckboxRecords()
      if (code === 'CloseEdit') {
        // 清除编辑状态
        this.$refs.xTable.$refs.xGrid.clearEdit()
        this.handleCustomSearch()
        return
      }
      if (this.isEdit) {
        // this.$toast({ content: this.$t('请先完成或结束编辑操作'), type: 'warning' })
        return
      }

      const params = {
        queryRulesReq: { ...this.searchFormModel, page: { current: 1, size: 999999 } },
        chooseOperateReqs: selectRecords.map((i) => {
          return {
            id: i.id,
            status: i.status,
            line: i.lineNo,
            supplierCode: i.supplierCode,
            bigVersionNo: i.bigVersionNo,
            md5Code: i.md5Code,
            sameBatchData: i.sameBatchData,
            supplierTenantId: i.supplierTenantId,
            supplierName: i.supplierName,
            plannerName: i.plannerName,
            planMemberCode: i.planMemberCode,
            factoryCode: i.factoryCode,
            itemCode: i.itemCode
          }
        })
      }

      if (code === 'Add') {
        // 新增
        const currentViewRecords = $grid.getTableData().visibleData
        if (!currentViewRecords.length) {
          this.$toast({
            content: this.$t('请先查询数据再进行新增操作'),
            type: 'warning'
          })
          return
        }
        this.titleList.forEach((itemTitle) => {
          const forecastItem = {
            buyerNum: null,
            total: null,
            supplierNum: null
          }
          NewRowData[`title_${itemTitle}`] = forecastItem
        })
        // 新增一行
        $grid.insert([NewRowData])
        this.$nextTick(() => {
          // 获取最新的表格视图数据
          const currentViewRecords = $grid.getTableData().visibleData
          // 将新增的那一条设置为编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow(currentViewRecords[0])
        })
        // } else if (code === 'Delete') {
        //   // 删除
        //   this.handleDelete(params, code)
        // } else if (code === 'Publish') {
        //   // 发布
        //   this.handlePublish(params, code)
        // } else if (code === 'Cancle') {
        //   // 取消发布
        //   this.handleCancle(params, code)
        // } else if (code === 'Confirm') {
        //   // 确认
        //   this.handleConfirm(params, code)
      } else if (code === 'Publish') {
        this.handlePublish(params, code)
      } else if (code === 'Import') {
        this.handleImport()
      } else if (code === 'Export') {
        this.handleExport()
      } else if (code === 'Export1') {
        this.handleExport('isAllowImport')
      } else if (code === 'Download') {
        this.handleDownload(params)
      } else {
        this.$dialog({
          data: {
            title: this.$t('提醒'),
            message: this.getMessage(code)
          },
          success: () => {
            this.handleOperator(params, code)
          }
        })
      }
    },
    handlePublish(params, code) {
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.getMessage(code)
        },
        success: () => {
          this.apiStartLoading()
          this.$API.deliverySchedule
            .validPublishTvApi(params)
            .then((res) => {
              if (res.data?.length === 0) {
                this.handleOperator(params, code)
              } else {
                this.apiEndLoading()
                this.$refs.publishValidateRef.dialogInit({
                  title: this.$t('提醒'),
                  params
                })
              }
            })
            .finally(() => {
              this.apiEndLoading()
            })
        }
      })
    },
    handleImport() {
      //导入
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.deliverySchedule.buyerGoodsDemandPlanInfoImportTv,
          downloadTemplateApi: this.$API.deliverySchedule.buyerGoodsDemandPlanInfoExportTempTv,
          paramsKey: 'excel',
          asyncParams: {
            bigVersionNo: this.searchFormModel.bigVersionNo
          },
          downloadTemplateParams: { bigVersionNo: this.searchFormModel.bigVersionNo }
        },
        success: () => {
          // 导入之后刷新列表
          this.handleCustomSearch()
        }
      })
    },
    handleExport(isAllowImport) {
      const { visibleColumn } = this.$refs.xTable.$refs.xGrid.getTableColumn()
      const headerMap = {}
      let apiName = 'buyerGoodsDemandPlanInfoExportTv'
      if (!isAllowImport) {
        let sortNumber = 0
        visibleColumn.forEach((i) => {
          if (i.field && i.title) {
            if (i.field === 'forecastType') {
              headerMap['type'] = i.title
            } else if (i.field.includes('title_')) {
              sortNumber++
              headerMap[`planDate${sortNumber}`] = i.title
            } else {
              headerMap[i.field] = i.title
            }
          }
        })
        apiName = 'buyerGoodsDemandPlanInfoExportDynamicTv'
      }
      //导出
      const params = {
        page: {
          size: 1000,
          current: 1
        },
        headerMap,
        ...this.searchFormModel
      }
      this.apiStartLoading()
      this.$API.deliverySchedule[apiName](params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = UTILS.getHeadersFileName(res)
          UTILS.download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    handleDownload(params) {
      this.apiStartLoading()
      this.$API.deliverySchedule
        .exportCustomsTvApi(params)
        .then((res) => {
          const fileName = UTILS.getHeadersFileName(res)
          UTILS.download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    handleOperator(params, code) {
      this.apiStartLoading()
      this.getHandleApi(code)(params)
        .then(() => {
          this.apiEndLoading()
          this.$toast({
            content: this.getContent(code),
            type: 'success'
          })
          this.handleCustomSearch()
        })
        .catch(() => {
          this.apiEndLoading()
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    getMessage(code) {
      let message = ''
      switch (code) {
        case 'Confirm':
          message = this.$t('确认选中的交货计划吗?')
          break
        case 'Delete':
          message = this.$t('确认删除选中的交货计划吗?')
          break
        case 'Cancle':
          message = this.$t('确认取消发布选中的交货计划吗?')
          break
        case 'Publish':
          message = this.$t('确认发布选中的交货计划吗?')
          break
        default:
          break
      }
      return message
    },
    getContent(code) {
      let message = ''
      switch (code) {
        case 'Confirm':
          message = this.$t('确认交货计划操作成功')
          break
        case 'Delete':
          message = this.$t('删除交货计划操作成功')
          break
        case 'Cancle':
          message = this.$t('取消发布交货计划操作成功')
          break
        case 'Publish':
          message = this.$t('发布交货计划操作成功')
          break
        default:
          break
      }
      return message
    },
    getHandleApi(code) {
      let api = null
      switch (code) {
        case 'Confirm':
          api = this.$API.deliverySchedule.buyerGoodsDemandPlanInfoConfirmTv
          break
        case 'Delete':
          api = this.$API.deliverySchedule.buyerGoodsDemandPlanInfoDeleteTv
          break
        case 'Cancle':
          api = this.$API.deliverySchedule.buyerGoodsDemandPlanInfoCancelTv
          break
        case 'Publish':
          api = this.$API.deliverySchedule.buyerGoodsDemandPlanInfoPublishTv
          break
        default:
          break
      }
      return api
    },
    // 校验数据
    isValidData(data) {
      const { materialCode, factoryCode } = data
      let valid = false
      if (!factoryCode) {
        // 工厂代码
        this.$toast({ content: this.$t('工厂不可为空'), type: 'warning' })
      } else if (!materialCode) {
        // 物料代码
        this.$toast({ content: this.$t('物料不可为空'), type: 'warning' })
        // } else if (!supplierCode) {
        //   // 供应商代码
        //   this.$toast({ content: this.$t('供应商不可为空'), type: 'warning' })
      } else {
        valid = true
      }

      return valid
    },
    // 重置查询条件
    handleCustomReset() {
      this.supplierCodes = null
      this.searchFormModel.supplierCodes = null
      this.itemCodes = null
      this.searchFormModel.itemCodes = null
      this.itemGroups = null
      this.searchFormModel.itemGroups = null
      this.structSeqNos = null
      this.searchFormModel.structSeqNos = null
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'bigVersionNo') {
            this.searchFormModel[key] = this.versionList[0]
          }
        }
      }
      this.searchFormModel.joinMaterialCode = false
      this.searchFormModel.joinMaterialGroup = false
      this.forecastPageCurrent = 1
      this.handleCustomSearch()
    },
    // 获取列表数据并组装表头
    handleCustomSearch(type) {
      this.$refs.xTable.$refs.xGrid.clearEdit()
      this.$refs.searchFormRef.validate(() => {})
      this.isEdit = false
      const params = {
        ...this.searchFormModel,
        page: {
          size: this.forecastPageSettings.pageSize,
          current: type === 'handle' ? 1 : this.forecastPageCurrent
        }
      }
      if (
        !this.searchFormModel?.typeList ||
        this.searchFormModel?.typeList?.length === 0 ||
        this.searchFormModel?.typeList?.length === 3
      ) {
        this.typeList = ['D', 'P', 'C']
        this.rowHeight = 74
      } else if (this.searchFormModel?.typeList?.length === 2) {
        this.typeList = this.searchFormModel?.typeList
        this.rowHeight = 50
      } else if (this.searchFormModel?.typeList?.length === 1) {
        this.typeList = this.searchFormModel?.typeList
        this.rowHeight = 26
      }
      this.apiStartLoading()
      this.$API.deliverySchedule
        .buyerGoodsDemandPlanInfoQueryTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.buyerDemandPlanPage?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.currentPage =
              Number(res?.data?.buyerDemandPlanPage?.current) || 1
            this.forecastPageSettings.totalRecordsCount = Number(total)
            const records = res?.data?.buyerDemandPlanPage?.records || [] // 表格数据
            const titleList = res?.data?.titleList || [] // 动态表头数据
            this.titleList = titleList // 动态表头数据
            // 处理表头数据
            this.columns = this.handleColumns({ titleList })
            // 处理表数据
            this.tableData = this.handleDataSource({ records, titleList })
            if (!this.searchFormModel.bigVersionNo) {
              // 处理表头数据
              this.columns = []
              // 处理表数据
              this.tableData = []
              this.forecastPageSettings.totalPages = 1
            }
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    handleColumns(data) {
      const { titleList } = data
      // 固定的表头
      const constantColumns = columnData
      // 动态的日期表头
      const titleListColumnData = []
      // const month =
      //   `${new Date().getMonth() + 1}`.length === 2
      //     ? new Date().getMonth() + 1
      //     : '0' + (new Date().getMonth() + 1)
      // const todayStr = `${new Date().getFullYear()}${month}${new Date().getDate()}`
      titleList.forEach((item, index) => {
        const title = `title_${titleList[index]}`
        titleListColumnData.push({
          field: title,
          title: item,
          minWidth: 125,
          showOverflow: true,
          editRender: {},
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <div>
                  <div v-show={this.typeList.includes('D')} class='vxe-cell-border'>
                    {row[title]['total']}
                  </div>
                  <div v-show={this.typeList.includes('P')} class='vxe-cell-border'>
                    {row[title]['buyerNum']}
                  </div>
                  <div v-show={this.typeList.includes('C')} class='vxe-cell-border'>
                    {row[title]['supplierNum']}
                  </div>
                </div>
              ]
            },
            edit: ({ row }) => {
              // <div style={todayStr === item ? 'background: #f5a1a1' : ''}>
              return [
                <div>
                  <div v-show={this.typeList.includes('D')} class='vxe-cell-border'>
                    {row[title]['total']}
                  </div>
                  <div v-show={this.typeList.includes('P')} class='vxe-cell-border'>
                    <mt-input-number
                      show-spin-button={false}
                      show-clear-button={false}
                      v-model={row[title]['buyerNum']}
                      v-show={!title.includes('W')}
                    />
                    <span v-show={title.includes('W')}>{row[title]['buyerNum']}</span>
                  </div>
                  <div v-show={this.typeList.includes('C')} class='vxe-cell-border'>
                    {row[title]['supplierNum']}
                  </div>
                </div>
              ]
            }
          }
        })
      })

      // 合并表头数组，然后 push 到 forecastColumnData 中
      const columns = [].concat(constantColumns).concat(titleListColumnData)
      return columns
    },
    handleDataSource(args) {
      const { records, titleList } = args
      records.forEach((obj, idx) => {
        obj.addId = this.addId++
        obj.lineNo = idx + 1
        if (obj.tvBuyerDemandPlanExtList && obj.tvBuyerDemandPlanExtList.length > 0) {
          titleList.forEach((title, index) => {
            let total = ''
            let buyerNum = ''
            let supplierNum = ''
            obj.tvBuyerDemandPlanExtList.forEach((itm) => {
              if (!itm) {
                total = ''
                buyerNum = ''
                supplierNum = ''
              }
              if (itm.type === 'D') {
                total = itm[`planDemandQty${index + 1}`]
              }
              if (itm.type === 'P') {
                buyerNum = itm[`planDemandQty${index + 1}`]
              }
              if (itm.type === 'C') {
                supplierNum = itm[`planDemandQty${index + 1}`]
              }
            })
            obj[`title_${title}`] = {
              total,
              buyerNum,
              supplierNum
            }
          })
        }
      })
      return records
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  },
  beforeRouteLeave(to, from, next) {
    document.querySelector('.vxe-table--body-wrapper').scrollTop = 0
    next()
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  div .vxe-cell-border {
    border: solid #e6e9ed 1px;
    height: 24px;
    box-sizing: border-box;
  }
}

/deep/ .vxe-cell .vxe-default-select {
  background: #fff;
}
/deep/ .col--seq,
/deep/ .col--checkbox {
  .c--tooltip {
    padding: 0 10px;
  }
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
