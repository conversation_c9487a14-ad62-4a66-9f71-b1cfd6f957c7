import { i18n } from '@/main.js'
export const ConstDynamicTitleStr = 'title_' // 动态表格数据的 key 标识

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 送货类型
export const deliverTypeList = [
  { label: i18n.t('采购订单'), value: 1 },
  { label: i18n.t('交货计划'), value: 2 },
  { label: i18n.t('JIT'), value: 3 },
  { label: i18n.t('无需求'), value: 4 },
  { label: i18n.t('VMI'), value: 5 },
  { label: i18n.t('钢材'), value: 6 }
]

// 配送方式
export const deliveryMethodList = [
  { label: i18n.t('直送'), value: '0' },
  { label: i18n.t('非直送'), value: '1' }
]

// 是否jit
export const isJitList = [
  { label: i18n.t('否'), value: 0 },
  { label: i18n.t('是'), value: 1 }
]

// 委外方式
export const outsourceMethodList = [
  { label: i18n.t('销售委外'), value: '1' },
  { label: i18n.t('标准委外'), value: '0' },
  { label: i18n.t('标准采购'), value: '2' }
]

// 预测数据类型cell
export const ForecastDataTypeCell = [
  {
    title: i18n.t('D(原始需求)')
  },
  {
    title: i18n.t('P(需求量)')
  },
  {
    title: i18n.t('C(承诺量)')
  }
]

// 类型字段，快捷搜索，下拉选项
export const ForecastTypeDataSource = [
  ForecastDataTypeCell[0]?.title, // D(原始需求)
  ForecastDataTypeCell[1]?.title, // P(需求量)
  ForecastDataTypeCell[2]?.title // C
]

// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  status: 0, // 状态 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
  planner: '', // 计划员
  factoryId: '', // 工厂id
  factoryName: '', // 工厂name
  factoryCode: '', // 工厂代码
  mrpArea: '', // 计划区域
  itemName: '', // 物料名称
  itemCode: '', // 物料编码
  itemId: '', // 物料id
  supplierName: '', // 供应商名称
  supplierCode: null, // 供应商编码
  supplierId: '', // 供应商id
  purchaseAdvanceDate: '', // 采购提前期
  supplierInv: '', // 供方库存
  supplierRemark: '', // 供应商备注
  quota: '', // 配额
  quotaFlag: '', // 无配额标识
  machineModel: '', // 机型/机芯
  purchaserRemark: '', // 采方备注
  manufacturer: '', // 生产厂商名称
  rawMaterialManufacturer: '', // 关键原材厂商
  keyRawMaterialOrigin: '', // 关键原材产地
  packageManufacturer: '', // 包装厂商
  packageProdtPlace: '', // 包装产地
  manufacturerDedicatedStatus: '', // 制造商专用状况
  syncVersion: '', // 版本
  unpaidPoQty: '' // 未交PO
}

export const ToolBar = [
  // { code: 'ForecastAdd', name: i18n.t('新增'), icon: 'vxe-icon-square-plus', status: 'info' },
  {
    code: 'CloseEdit',
    name: i18n.t('取消编辑'),
    icon: 'vxe-icon-edit',
    status: 'info',
    transfer: true
  },
  {
    code: 'Delete',
    name: i18n.t('删除'),
    permission: ['O_02_1619'],
    icon: 'vxe-icon-delete',
    status: 'info'
  },
  {
    code: 'Publish',
    name: i18n.t('发布'),
    permission: ['O_02_1620'],
    icon: 'vxe-icon-folder-open',
    status: 'info'
  },
  {
    code: 'Cancle',
    name: i18n.t('取消发布'),
    permission: ['O_02_1621'],
    icon: 'vxe-icon-error-circle',
    status: 'info'
  },
  {
    code: 'Confirm',
    name: i18n.t('确认'),
    permission: ['O_02_1622'],
    icon: 'vxe-icon-success-circle',
    status: 'info'
  },
  {
    code: 'Import',
    name: i18n.t('导入'),
    permission: ['O_02_1623'],
    icon: 'vxe-icon-cloud-upload',
    status: 'info'
  },
  {
    code: 'Export1',
    name: i18n.t('导出（可再导入）'),
    permission: ['O_02_1624'],
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  },
  {
    code: 'Export',
    name: i18n.t('灵活导出（仅查看）'),
    permission: ['O_02_1624'],
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  },
  {
    code: 'Download',
    name: i18n.t('下载无报关要素清单'),
    permission: ['O_02_1620'],
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  }
]

// 状态 0 新建、1 已修改、2 已发布、 3 反馈-满足、4 反馈-不满足、 5 已确认 6、 已关闭
export const StatusSearchOptions = [
  { text: i18n.t('新建'), value: 0 },
  { text: i18n.t('已修改'), value: 1 },
  { text: i18n.t('已发布'), value: 2 },
  { text: i18n.t('反馈-满足'), value: 3 },
  { text: i18n.t('反馈-不满足'), value: 4 },
  { text: i18n.t('已确认'), value: 5 },
  { text: i18n.t('已关闭'), value: 6 }
  // { text: i18n.t('同步中'), value: '7' }
]

// 状态 0 新建、1 已修改、2 已发布、 3 反馈-满足、4 反馈-不满足、 5 已确认 6、 已关闭
export const StatusSearchOptionsLabel = [
  { label: i18n.t('新建'), value: 0 },
  { label: i18n.t('已修改'), value: 1 },
  { label: i18n.t('已发布'), value: 2 },
  { label: i18n.t('反馈-满足'), value: 3 },
  { label: i18n.t('反馈-不满足'), value: 4 },
  { label: i18n.t('已确认'), value: 5 },
  { label: i18n.t('已关闭'), value: 6 }
]

// 预测表格列数据
export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    ignore: true,
    fixed: 'left'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'serialNo',
    title: i18n.t('序列号'),
    fixed: 'left',
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'factoryCode',
    title: i18n.t('工厂'),
    align: 'center',
    fixed: 'left',
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码'),
    width: 160,
    fixed: 'left',
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称'),
    width: 160,
    fixed: 'left',
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码'),
    fixed: 'left',
    align: 'center',
    width: 160,
    showOverflow: true,
    // formatter: ({ row }) => {
    //   let text = row.supplierCode
    //   if (row.supplierName) {
    //     text += `-${row.supplierName}`
    //   }
    //   return text
    // },
    editRender: {},
    slots: {
      edit: 'supplierCodeEdit'
    }
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    fixed: 'left',
    width: 160,
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = StatusSearchOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    },
    align: 'center',
    editRender: {
      name: 'select',
      options: StatusSearchOptionsLabel,
      props: { style: 'background: #fff' },
      attrs: { disabled: true }
    }
    // fixed: 'left'
  },
  {
    field: 'plannerName',
    title: i18n.t('计划员'),
    align: 'center',
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'mrpArea',
    title: i18n.t('MRP区域'),
    align: 'center',
    width: 120,
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'structSeqNo',
    title: i18n.t('结构序号'),
    width: 120,
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'outsourceMethod',
    title: i18n.t('委外方式'),
    width: 120,
    align: 'center',
    needChange: true,
    editRender: {
      name: 'select',
      options: outsourceMethodList,
      props: { placeholder: i18n.t('请选择委外方式'), style: 'background: #fff' },
      attrs: { disabled: false }
    }
  },
  {
    field: 'deliveryMethod',
    title: i18n.t('配送方式'),
    width: 120,
    align: 'center',
    needChange: true,
    editRender: {
      name: 'select',
      options: deliveryMethodList,
      props: { placeholder: i18n.t('请选择配送方式'), style: 'background: #fff' },
      attrs: { disabled: false }
    }
  },
  {
    field: 'isJit',
    title: i18n.t('是否JIT'),
    align: 'center',
    needChange: true,
    editRender: {
      name: 'select',
      options: isJitList,
      props: { placeholder: i18n.t('请选择是否JIT'), style: 'background: #fff' },
      attrs: { disabled: false }
    }
  },
  {
    field: 'invAddrCode',
    title: i18n.t('库存地点'),
    width: 120,
    formatter: ({ row }) => {
      let text = row.invAddrCode
      if (row.invAddrName) {
        text += `-${row.invAddrName}`
      }
      return text
    },
    showOverflow: true,
    editRender: {},
    slots: {
      edit: 'invAddrCodeEdit'
    }
  },
  {
    field: 'plusBizCirclesCode',
    title: i18n.t('加工商'),
    formatter: ({ row }) => {
      let text = row.plusBizCirclesCode
      if (row.plusBizCirclesName) {
        text += `-${row.plusBizCirclesName}`
      }
      return text
    },
    width: 160,
    showOverflow: true,
    editRender: {},
    slots: {
      edit: 'plusBizCirclesCodeEdit'
    }
  },
  {
    field: 'projectTextBatch',
    title: i18n.t('项目文本批次'),
    width: 140,
    showOverflow: true,
    needChange: true,
    editRender: {
      name: 'input',
      attrs: { placeholder: i18n.t('请输入项目文本批次'), disabled: false }
    }
  },
  {
    field: 'batchNo',
    title: i18n.t('销售订单号'),
    width: 130,
    showOverflow: true,
    needChange: true,
    editRender: {
      name: 'input',
      attrs: { placeholder: i18n.t('请输入销售订单号'), disabled: false }
    }
  },
  {
    field: 'lineBody',
    title: i18n.t('线体'),
    showOverflow: true,
    needChange: true,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入线体'), disabled: false } }
  },
  {
    field: 'flag',
    title: i18n.t('标识'),
    showOverflow: true,
    needChange: true,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入标识'), disabled: false } }
  },
  {
    field: 'workCenterName',
    title: i18n.t('工作中心'),
    width: 120,
    formatter: ({ row }) => {
      let text = row.workCenterCode
      if (row.workCenterName) {
        text += `-${row.workCenterName}`
      }
      return text
    },
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'batchQty',
    title: i18n.t('批量'),
    align: 'center',
    showOverflow: true,
    needChange: true,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入批量'), disabled: false } }
  },
  {
    field: 'bomNo',
    title: i18n.t('BOM号'),
    showOverflow: true,
    needChange: true,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入BOM号'), disabled: false } }
  },
  {
    field: 'purchaserRemark',
    title: i18n.t('采购方备注'),
    width: 130,
    showOverflow: true,
    needChange: true,
    editRender: {
      name: 'input',
      attrs: { placeholder: i18n.t('请输入采购方备注'), disabled: false }
    }
  },
  {
    field: 'supplierRemark',
    title: i18n.t('供应商备注'),
    width: 130,
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'purchaseOrderNo',
    title: i18n.t('采购订单'),
    width: 120,
    showOverflow: true,
    editRender: {},
    slots: {
      edit: 'purchaseOrderNoEdit'
    }
  },
  {
    field: 'bigVersionNo',
    title: i18n.t('版本号'),
    align: 'center',
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'deliverType',
    title: i18n.t('送货类型'),
    align: 'center',
    width: 120,
    formatter: ({ cellValue }) => {
      let item = deliverTypeList.find((item) => item.value === cellValue)
      return item ? item.label : ''
    },
    editRender: {
      name: 'select',
      options: deliverTypeList,
      props: { style: 'background: #fff' },
      attrs: { disabled: true }
    }
  },
  {
    field: 'unclearOrderQty',
    title: i18n.t('未清订单数量'),
    align: 'center',
    width: 140,
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'invQty',
    title: i18n.t('库存量'),
    align: 'center',
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'limitQty',
    title: i18n.t('限量数量'),
    width: 120,
    align: 'center',
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'forecastType',
    className: 'vxe-table-multi-cell',
    title: i18n.t('类型'),
    slots: {
      default: 'forecastTypeDefault'
      // 使用 JSX 渲染
      // default: ({ row }) => {
      //   return [
      //     <div>
      //       {ForecastTypeDataSource.map((item) => {
      //         return <span>{item}</span>
      //       })}
      //     </div>
      //   ]
      // }
    }
  }
]
