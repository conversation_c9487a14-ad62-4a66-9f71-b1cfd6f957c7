<template>
  <div class="selfType">
    <mt-input :id="data.column.field" style="display: none"></mt-input>
    <div class="self-type-cell-boder">{{ showInfo[0] }}</div>
    <div class="self-type-cell-boder">
      {{ showInfo[1] }}
    </div>
    <div :class="calssName">{{ showInfo[2] }}</div>
    <!-- <div>
      <span :class="calssName">{{ showInfo[3] }}</span>
    </div>
    <div>{{ showInfo[4] }}</div> -->
  </div>
</template>
<script>
export default {
  data() {
    return {
      showInfo: ['', '', '', '', ''],
      data: {},
      calssName: 'self-type-cell-boder'
    }
  },
  mounted() {
    if (this.data[this.data.column.field]) {
      this.showInfo = this.data[this.data.column.field].split('_')
    } else {
      this.showInfo = ['', '', '', '', '']
    }
    const cVal = this.showInfo[2] ? Number(this.showInfo[2]) : this.showInfo[2]
    const pVal = this.showInfo[1] ? Number(this.showInfo[1]) : this.showInfo[1]
    if (this.data['status'] == '4' && cVal < pVal) {
      this.calssName = 'self-type-cell-boder red'
    } else {
      this.calssName = 'self-type-cell-boder'
    }
  }
}
</script>
<style lang="scss" scoped>
.selfType {
  .red {
    color: #ed5836;
    background-color: #fdeeea;
    border: 1px solid #ed5836;
  }
  > div {
    height: 36px;
    line-height: 36px;
    box-sizing: border-box;
    // padding: 12px 0;
  }
}
.self-type-cell-boder {
  border: 1px solid #e0e0e0;
}
</style>
