import Input from '../../purchaseScheduleKT/components1/Input.vue'
import InputNumber from '../../purchaseScheduleKT/components1/InputNumber.vue'
import Select from '../../purchaseScheduleKT/components2/Select.vue'
import selfType from '../components/selfType.vue'
import editView from '../../purchaseScheduleKT/components1/editView.vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

import Vue from 'vue'

export const timeDate = (dataKey, hasTime) => {
  // const { dataKey, hasTime } = args;
  // console.log(args);
  const template = () => {
    return {
      template: Vue.component('date', {
        template: `<div><div>{{data[dataKey] | dateFormat}}</div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div></div>`,
        data: function () {
          return { data: {}, dataKey, hasTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
export const checkColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false,
    allowEditing: false

    // customAttributes: {
    //   class: 'sticky-col-0' // sticky-col-0不是固定写法，自定义命名即可
    // }
  }
]

export const normalColumn = [
  {
    field: 'addId',
    headerText: 'addId',
    width: 0,
    visible: false,
    allowEditing: false
  },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  },
  {
    field: 'lineNo', // 前端定义 不可编辑
    headerText: i18n.t('序号')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '85',
    allowEditing: false,
    options: [
      { text: i18n.t('新建'), value: '0' },
      { text: i18n.t('已修改'), value: '1' },
      { text: i18n.t('已发布'), value: '2' },
      { text: '反馈-满足', value: '3' },
      { text: '反馈-不满足', value: '4' },
      { text: i18n.t('已确认'), value: '5' },
      { text: i18n.t('已关闭'), value: '6' }
      // { text: i18n.t('同步中'), value: '7' }
    ],
    editTemplate: () => {
      return { template: editView }
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('新建'),
        1: i18n.t('已修改'),
        2: i18n.t('已发布'),
        3: i18n.t('反馈-满足'),
        4: i18n.t('反馈-不满足'),
        5: i18n.t('已确认'),
        6: i18n.t('已关闭')
        // 7: i18n.t('同步中')
      }
    },
    cellTools: [
      {
        // permission: ['O_02_0438'],
        id: 'Publish',
        icon: 'icon_solid_pushorder',
        title: i18n.t('发布'),
        visibleCondition: (data) => {
          return data.status === 0 || data.status === 1 || data.status === 4
        }
      },
      {
        // permission: ['O_02_0692'],
        id: 'Cancle',
        icon: 'icon_solid_pushorder',
        title: i18n.t('取消发布'),
        visibleCondition: (data) => {
          return data.status === 2
        }
      },
      // {
      //   // permission: ['O_02_0443'],
      //   id: 'Close',
      //   icon: 'icon_solid_pushorder',
      //   title: i18n.t('关闭'),
      //   visibleCondition: (data) => {
      //     return data.status === 2 || data.status === 3 || data.status === 4 || data.status === 5
      //   }
      // },
      {
        // permission: ['O_02_0440'],
        id: 'Confirm',
        icon: 'icon_solid_pushorder',
        title: i18n.t('确认'),
        visibleCondition: (data) => {
          return data.status === 4
        }
      }
    ],
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    }
  },
  // {
  //   field: "deliveryStatus",
  //   headerText: i18n.t("发货状态"),
  //   width: "150",
  //   allowEditing: false,
  //   valueConverter: {
  //     type: "map",
  //     map: {
  //       0: i18n.t("未发货"),
  //       1: i18n.t("全部发货"),
  //       2: i18n.t("部分发货"),
  //     },
  //   },
  // },
  // {
  //   field: "receiveStatus",
  //   headerText: i18n.t("收货状态"),
  //   width: "150",
  //   allowEditing: false,
  //   valueConverter: {
  //     type: "map",
  //     map: {
  //       0: i18n.t("未收货"),
  //       1: i18n.t("部分收货"),
  //       2: i18n.t("全部收货"),
  //     },
  //   },
  // },
  {
    field: 'plannerName',
    headerText: i18n.t('计划员'),
    selectOptions: [],
    searchOptions: {
      ...MasterDataSelect.businessGroup,
      renameField: 'planGroupCode'
    },
    allowEditing: false,
    // editTemplate: () => {
    //   return { template: Select }
    // },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.plannerName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'factoryCode',
    headerText: i18n.t('工厂'),
    selectOptions: [],
    width: '300',
    searchOptions: {
      ...MasterDataSelect.factoryAddress,
      renameField: 'factoryCode'
    },
    allowResizing: false,
    // customAttributes: {
    //   class: 'sticky-col-1' // sticky-col-0不是固定写法，自定义命名即可
    // },
    allowEditing: false,
    editTemplate: () => {
      // return { template: Select }
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.factoryCode}}-{{data.factoryName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('工厂')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.factoryCode}}-{{data.factoryName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  // {
  //   field: 'companyName',
  //   headerText: i18n.t('公司'),
  //   width: '220',
  //   searchOptions: {
  //     ...MasterDataSelect.businessCompany,
  //     renameField: 'companyCode'
  //   },
  //   editTemplate: () => {
  //     return { template: InputView }
  //   },
  //   headerTemplate: () => {
  //     return {
  //       template: Vue.component('headers', {
  //         template: `
  //             <div class="headers">
  //               <span style="color: red">*</span>
  //               <span class="e-headertext">{{$t('公司')}}</span>
  //             </div>
  //           `
  //       })
  //     }
  //   },
  //   template: () => {
  //     return {
  //       template: Vue.component('headers', {
  //         template: `
  //             <div class="headers">
  //               <span>{{data.companyCode}}-{{data.companyName}}</span>
  //             </div>
  //           `,
  //         data() {
  //           return {
  //             data: {}
  //           }
  //         }
  //       })
  //     }
  //   }
  // },
  // {
  //   field: 'buyerOrgName',
  //   headerText: i18n.t('采购组'),
  //   width: '150',
  //   searchOptions: {
  //     ...MasterDataSelect.businessGroup,
  //     renameField: 'buyerOrgCode'
  //   },
  //   editTemplate: () => {
  //     return { template: InputView }
  //   },
  //   template: () => {
  //     return {
  //       template: Vue.component('headers', {
  //         template: `
  //             <div class="headers">
  //               <span>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</span>
  //             </div>
  //           `,
  //         data() {
  //           return {
  //             data: {}
  //           }
  //         }
  //       })
  //     }
  //   }
  // },
  {
    field: 'mrpArea',
    width: '100',
    headerText: i18n.t('MRP区域'),
    maxlength: '40',
    allowEditing: false
    // editTemplate: () => {
    //   return { template: Input }
    // }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    selectOptions: [],
    width: '200',
    // searchOptions: {
    //   ...MasterDataSelect.material,
    //   renameField: "itemCode",
    // },
    allowEditing: false,
    allowResizing: false,
    // customAttributes: {
    //   class: 'sticky-col-2' // sticky-col-0不是固定写法，自定义命名即可
    // },
    // editTemplate: () => {
    //   return { template: ItemCode }
    // },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料编号')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    ignore: true,
    selectOptions: [],
    width: '430',
    // editTemplate: () => {
    //   return { template: InputView }
    // },
    allowEditing: false,
    allowResizing: false
    // customAttributes: {
    //   class: 'sticky-col-3' // sticky-col-0不是固定写法，自定义命名即可
    // }
    // headerTemplate: () => {
    //   return {
    //     template: Vue.component('headers', {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('物料名称')}}</span>
    //           </div>
    //         `
    //     })
    //   }
    // }
  },
  // {
  //   field: 'itemDescription',
  //   // headerText: i18n.t("物料描述"),
  //   width: 0,
  //   ignore: true,
  //   allowFiltering: false,
  //   visible: false,
  //   editTemplate: () => {
  //     return { template: InputView }
  //   },
  //   headerTemplate: () => {
  //     return {
  //       template: Vue.component('headers', {
  //         template: `
  //             <div class="headers">
  //               <span style="color: red">*</span>
  //               <span class="e-headertext">{{$t('物料描述')}}</span>
  //             </div>
  //           `
  //       })
  //     }
  //   }
  // },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: '245',
    allowResizing: false,
    // customAttributes: {
    //   class: 'sticky-col-4' // sticky-col-0不是固定写法，自定义命名即可
    // },
    // searchOptions: {
    //   ...MasterDataSelect.supplier,
    //   renameField: 'supplierCode'
    // },
    editTemplate: () => {
      return { template: Select }
    },
    allowEditing: false,
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('供应商')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.supplierCode}}-{{data.supplierName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  // {
  //   field: 'supplierName',
  //   headerText: i18n.t('供应商名称'),
  //   width: '200',
  //   editTemplate: () => {
  //     return { template: InputView }
  //   },
  //   headerTemplate: () => {
  //     return {
  //       template: Vue.component('headers', {
  //         template: `
  //             <div class="headers">
  //               <span style="color: red">*</span>
  //               <span class="e-headertext">{{$t('供应商名称')}}</span>
  //             </div>
  //           `
  //       })
  //     }
  //   }
  // },
  {
    field: 'structSeqNo',
    headerText: i18n.t('结构序号'),
    allowEditing: false
  },
  {
    field: 'outsourceMethod',
    headerText: i18n.t('委外方式'),
    selectOptions: [
      { label: i18n.t('销售委外'), value: '1' },
      { label: i18n.t('标准委外'), value: '0' },
      { label: i18n.t('标准采购'), value: '2' }
    ],
    width: '95',
    editTemplate: () => {
      return { template: Select }
    },
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('销售委外'),
        2: i18n.t('标准采购'),
        0: i18n.t('标准委外')
      }
    }
  },
  {
    field: 'deliveryMethod',
    headerText: i18n.t('配送方式'),
    selectOptions: [
      { label: i18n.t('直送'), value: '0' },
      { label: i18n.t('非直送'), value: '1' }
    ],
    width: '90',
    editTemplate: () => {
      return { template: Select }
    },
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('直送'), 1: i18n.t('非直送') }
    }
    // headerTemplate: () => {
    //   return {
    //     template: Vue.component('headers', {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('配送方式')}}</span>
    //           </div>
    //         `
    //     })
    //   }
    // }
  },
  {
    field: 'warehouseCode',
    headerText: i18n.t('库存地点'),
    selectOptions: [],
    width: '200',
    searchOptions: {
      ...MasterDataSelect.stockAddress,
      renameField: 'warehouseCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.warehouseCode}}-{{data.warehouseName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  // {
  //   field: 'warehouseName',
  //   headerText: i18n.t('库存地点名称'),
  //   width: '150',
  //   editTemplate: () => {
  //     return { template: InputView }
  //   }
  // },
  {
    field: 'plusBizCirclesCode',
    headerText: i18n.t('加工商'),
    selectOptions: [],
    width: '260',
    searchOptions: {
      ...MasterDataSelect.supplier,
      renameField: 'plusBizCirclesCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.plusBizCirclesCode}}-{{data.plusBizCirclesName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  // {
  //   field: 'processorName',
  //   headerText: i18n.t('加工商名称'),
  //   width: '150',
  //   editTemplate: () => {
  //     return { template: InputView }
  //   }
  // },
  // {
  //   field: 'batchNo',
  //   headerText: i18n.t('批次'),
  //   width: '125',
  //   maxlength: '40'
  //   // editTemplate: () => {
  //   //   return { template: Input }
  //   // }
  // },
  {
    field: 'projectTextBatch',
    headerText: i18n.t('项目文本批次'),
    width: '125',
    // allowEditing: false,
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'batchNo',
    headerText: i18n.t('批次号'),
    width: '125',
    // allowEditing: false,
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'lineBody',
    headerText: i18n.t('线体'),
    width: '125',
    // allowEditing: false,
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'flag',
    headerText: i18n.t('标识'),
    width: '125',
    // allowEditing: false,
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'workCenterName',
    headerText: i18n.t('工作中心'),
    selectOptions: [],
    // editTemplate: () => {
    //   return { template: Select }
    // },
    allowEditing: false,
    width: '100',
    searchOptions: {
      ...MasterDataSelect.workCenter,
      renameField: 'workCenterName'
    },
    editTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.workCenterCode}}-{{data.workCenterName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.workCenterCode}}-{{data.workCenterName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'batchQty',
    headerText: i18n.t('批量'),
    width: '125',
    // allowEditing: false,
    maxlength: '40',
    editTemplate: () => {
      return { template: InputNumber }
    }
  },
  {
    field: 'bomNo',
    headerText: i18n.t('BOM号'),
    width: '110',
    // allowEditing: false,
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'purchaserRemark',
    headerText: i18n.t('采购方备注'),
    width: '125',
    maxlength: 200,
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'supplierRemark',
    headerText: i18n.t('供应商备注'),
    width: '125',
    allowEditing: false
  },
  {
    field: 'purchaseOrderNo',
    headerText: i18n.t('采购订单'),
    selectOptions: [],
    width: '100',
    // allowEditing: false
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'bigVersionNo',
    headerText: i18n.t('版本号'),
    width: '150',
    // valueConverter: {
    //   type: 'function',
    //   filter: (e) => {
    //     return 'V' + e
    //   }
    // },
    allowEditing: false
  },
  {
    field: 'deliverType',
    headerText: i18n.t('送货类型'),
    width: '125',
    allowEditing: false,
    options: [
      { text: i18n.t('采购订单'), value: '1' },
      { text: i18n.t('交货计划'), value: '2' },
      { text: 'JIT', value: '3' },
      { text: i18n.t('无需求'), value: '4' },
      { text: 'VMI', value: '5' },
      { text: i18n.t('钢材'), value: '6' }
    ],
    editTemplate: () => {
      return { template: editView }
    },
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('采购订单'),
        2: i18n.t('交货计划'),
        3: i18n.t('JIT'),
        4: i18n.t('无需求'),
        5: i18n.t('VMI'),
        6: i18n.t('钢材')
      }
    }
  },
  {
    field: 'unclearOrderQty',
    headerText: i18n.t('未清订单数量'),
    width: '125',
    allowEditing: false
  },
  {
    field: 'invQty',
    headerText: i18n.t('库存量'),
    width: '85',
    allowEditing: false
  },
  {
    field: 'limitQty',
    headerText: i18n.t('限量数量'),
    width: '100',
    allowEditing: false
  },
  // {
  //   field: 'systemRemark',
  //   headerText: i18n.t('系统备注'),
  //   width: '95',
  //   allowEditing: false
  // },
  // {
  //   field: 'release',
  //   headerText: i18n.t('下达'),
  //   allowEditing: false,
  //   width: '80',
  //   allowFiltering: false,
  //   ignore: true,
  //   valueConverter: {
  //     type: 'map',
  //     map: { 0: i18n.t('未下达'), 1: i18n.t('已下达') }
  //   },
  //   options: [
  //     { text: i18n.t('未下达'), value: '0' },
  //     { text: i18n.t('已下达'), value: '1' }
  //   ],
  //   editTemplate: () => {
  //     return { template: editView }
  //   }
  // },
  // {
  //   field: 'associatedNumber',
  //   headerText: i18n.t('关联工单号'),
  //   width: '115',
  //   maxlength: '40',
  //   editTemplate: () => {
  //     return { template: Input }
  //   }
  // },
  // {
  //   field: 'saleOrder',
  //   headerText: i18n.t('销售订单号'),
  //   width: '120',
  //   editTemplate: () => {
  //     return { template: InputView }
  //   }
  // },
  // {
  //   allowFiltering: false,
  //   field: 'saleOrderRowCode',
  //   headerText: i18n.t('销售订单行号'),
  //   width: 0,
  //   ignore: true,
  //   visible: false,
  //   editTemplate: () => {
  //     return { template: Input }
  //   }
  // },
  // {
  //   field: 'processName',
  //   headerText: i18n.t('工序名称'),
  //   width: 0,
  //   allowFiltering: false,
  //   visible: false,
  //   ignore: true,
  //   editTemplate: () => {
  //     return { template: Input }
  //   }
  // },
  // {
  //   field: 'deliveredQty',
  //   headerText: i18n.t('已发货数量'),
  //   allowFiltering: false,
  //   visible: false,
  //   ignore: true,
  //   editTemplate: () => {
  //     return { template: Input }
  //   }
  // },
  // {
  //   field: 'demandDate',
  //   headerText: i18n.t('需求日期'),
  //   allowEditing: false,
  //   searchOptions: {
  //     ...MasterDataSelect.timeRange,
  //     renameField: 'demandDate'
  //   },
  //   template: timeDate('demandDate', false),
  //   width: '90',
  //   editTemplate: () => {
  //     return { template: DatePicker }
  //   },

  //   headerTemplate: () => {
  //     return {
  //       template: Vue.component('headers', {
  //         template: `
  //             <div class="headers">
  //               <span style="color: red">*</span>
  //               <span class="e-headertext">{{$t('需求日期')}}</span>
  //             </div>
  //           `
  //       })
  //     }
  //   }
  // },
  // {
  //   field: 'deliverAddr',
  //   headerText: i18n.t('送货地址'),
  //   width: '270',
  //   allowFiltering: false,
  //   ignore: true,
  //   selectOptions: [],
  //   editTemplate: () => {
  //     return { template: Select }
  //   }
  // },
  // {
  //   allowFiltering: false,
  //   ignore: true,
  //   field: 'checkDate',
  //   headerText: i18n.t('确认日期'),
  //   width: '120',
  //   allowEditing: false,
  //   valueConverter: {
  //     type: 'function',
  //     filter: (e) => {
  //       if (e && e.length === 13) {
  //         e = Number(e)
  //         return UTILS.dateFormat(e, 'Y-m-d')
  //       } else {
  //         return ''
  //       }
  //     }
  //   },
  //   editTemplate: () => {
  //     return { template: editDateView }
  //   }
  // },
  // {
  //   field: 'serialNo',
  //   headerText: i18n.t('序列号'),
  //   width: '150',
  //   allowEditing: false
  // },
  // {
  //   field: 'productCode',
  //   headerText: i18n.t('产品代码'),
  //   width: 0,
  //   ignore: true,
  //   allowFiltering: false,
  //   visible: false,
  //   editTemplate: () => {
  //     return { template: Input }
  //   }
  // },
  // {
  //   field: 'buyerOrderRowCode',
  //   headerText: i18n.t('采购订单行'),
  //   width: '110',
  //   maxlength: '240',
  //   editTemplate: () => {
  //     return { template: Input }
  //   }
  // },
  // {
  //   field: 'remainingNum',
  //   headerText: i18n.t('剩余可创建数量'),
  //   type: 'number',
  //   width: '135',
  //   allowEditing: false
  // },
  // {
  //   field: 'warehouseKeeper',
  //   headerText: i18n.t('仓管员'),
  //   width: '85',
  //   maxlength: '40',
  //   editTemplate: () => {
  //     return { template: Input }
  //   }
  // },
  // {
  //   field: 'createUserName',
  //   headerText: i18n.t('创建人'),
  //   width: '85',
  //   allowFiltering: false,
  //   ignore: true,
  //   allowEditing: false
  // },
  // {
  //   field: 'scheduleType',
  //   headerText: i18n.t('JIT物料'),
  //   selectOptions: [
  //     { label: 'JIT', value: 'JIT' },
  //     { label: i18n.t('非JIT'), value: i18n.t('非JIT') }
  //   ],
  //   width: '90',
  //   editTemplate: () => {
  //     return { template: Select }
  //   },
  //   headerTemplate: () => {
  //     return {
  //       template: Vue.component('headers', {
  //         template: `
  //             <div class="headers">
  //               <span style="color: red">*</span>
  //               <span class="e-headertext">{{$t('JIT物料')}}</span>
  //             </div>
  //           `
  //       })
  //     }
  //   }
  // },
  // {
  //   field: 'supplierCheckUser',
  //   headerText: i18n.t('供应商确认人'),
  //   width: '125',
  //   allowEditing: false,
  //   allowFiltering: false,
  //   ignore: true
  // },
  // {
  //   field: 'addressInfo',
  //   headerText: i18n.t('送货联系人+送货联系电话+送货地址'),
  //   width: '250',
  //   allowEditing: false,
  //   allowFiltering: false,
  //   ignore: true
  // },
  {
    allowFiltering: false,
    ignore: true,
    field: 'selfType',
    headerText: i18n.t('类型'),
    width: '110',
    allowEditing: false,
    editTemplate: () => {
      return { template: selfType }
    },
    template: () => {
      return { template: selfType }
    }
  }
]
