<template>
  <div class="full-height vertical-flex-box">
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <mt-template-page
        ref="templateRef1"
        :template-config="componentConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @actionBegin="actionBegin"
        @handleCustomReset="handleCustomReset"
        @actionComplete="actionComplete"
        @selectedChanged="selectedChanged1"
      >
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form
              ref="searchFormRef"
              :model="searchFormModel"
              :rules="{
                bigVersionNo: [{ required: true, message: $t('请输入'), trigger: 'blur' }]
              }"
            >
              <mt-form-item prop="bigVersionNo" :label="$t('版本号')" label-style="top">
                <!-- <mt-input
                  v-model="searchFormModel.bigVersionNo"
                  :show-clear-button="true"
                  :placeholder="$t('请输入版本号')"
                /> -->
                <mt-select
                  v-model="searchFormModel.bigVersionNo"
                  :data-source="versionList"
                  :placeholder="$t('请选择')"
                ></mt-select>
              </mt-form-item>
              <mt-form-item prop="factoryCode" :label="$t('工厂')" label-style="top">
                <RemoteAutocomplete
                  style="flex: 1"
                  v-model="searchFormModel.factoryCode"
                  :url="$API.masterData.getSiteListUrl"
                  multiple
                  :placeholder="$t('请选择')"
                  :fields="{ text: 'siteName', value: 'siteCode' }"
                  :search-fields="['siteName', 'siteCode']"
                ></RemoteAutocomplete>
              </mt-form-item>
              <!-- <mt-form-item prop="plannerName" :label="$t('计划员')" label-style="top">
                <mt-input
                  v-model="searchFormModel.plannerName"
                  :show-clear-button="true"
                  :placeholder="$t('请输入计划员')"
                />
              </mt-form-item> -->
              <mt-form-item prop="supplierCodes" :label="$t('供应商编码')" label-style="top">
                <mt-input style="flex: 1" v-model="supplierCodes" @change="supplierChange" />
              </mt-form-item>
              <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
                <mt-input v-model="searchFormModel.supplierName"></mt-input>
              </mt-form-item>
              <!-- <mt-form-item prop="salesOrderNo" :label="$t('销售订单号')" label-style="top">
                <mt-input
                  v-model="searchFormModel.salesOrderNo"
                  :show-clear-button="true"
                  :placeholder="$t('请输入销售订单号')"
                />
              </mt-form-item> -->
              <mt-form-item prop="structSeqNo" :label="$t('结构序号')" label-style="top">
                <mt-input
                  v-model="structSeqNo"
                  @change="() => (searchFormModel.structSeqNo = [structSeqNo])"
                />
              </mt-form-item>
              <mt-form-item prop="status" :label="$t('状态')" label-style="top">
                <mt-multi-select
                  style="flex: 1"
                  v-model="searchFormModel.status"
                  :show-select-all="true"
                  :allow-filtering="true"
                  filter-type="Contains"
                  :data-source="statusOptions"
                  :placeholder="$t('请选择')"
                ></mt-multi-select>
              </mt-form-item>
              <mt-form-item prop="planMemberCode" :label="$t('计划员')" label-style="top">
                <mt-multi-select
                  style="flex: 1"
                  v-model="searchFormModel.planMemberCode"
                  :data-source="plannerListOptions"
                  :fields="{ text: 'codeAndName', value: 'userCode' }"
                  :show-select-all="true"
                  :allow-filtering="true"
                  filter-type="Contains"
                  :placeholder="$t('请选择')"
                ></mt-multi-select>
              </mt-form-item>
              <mt-form-item prop="supplierEmptyFlag" :label="$t('供应商为空')" label-style="top">
                <mt-select
                  v-model="searchFormModel.supplierEmptyFlag"
                  :data-source="[
                    { text: $t('否'), value: 2 },
                    { text: $t('是'), value: 1 }
                  ]"
                  :show-clear-button="true"
                  :placeholder="$t('请选择供应商是否为空')"
                ></mt-select>
              </mt-form-item>
              <mt-form-item prop="itemCodes" :label="$t('物料编码')" label-style="top">
                <mt-input v-model="itemCodes" @change="itemChange" />
              </mt-form-item>
              <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
                <mt-input v-model="searchFormModel.itemName"></mt-input>
              </mt-form-item>
            </mt-form>
          </div>
        </template>
      </mt-template-page>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
    <!-- <import-dialog
      :accept="['.xls', '.xlsx']"
      :from-data-key="fromDataKey"
      :save-url="saveUrl"
      ref="importDialog"
      @import="importDialogImport"
      @uploadCompleted="uploadCompleted"
    >
      <predict-import-dialog-slot
        ref="predictImportDialogSlot"
        @submitted="predictImportDialogSlotSubmitted"
      ></predict-import-dialog-slot>
    </import-dialog> -->
  </div>
</template>
<script>
import { checkColumn, normalColumn } from './config/index.js'
import { cloneDeep } from 'lodash'
import InputNumber from './components/InputNumber.vue'
import InputNumberView from './components/InputNumberView.vue'
import * as UTILS from '@/utils/utils'
import dayjs from 'dayjs'
// var bigDecimal = require('js-big-decimal')
export default {
  // components: {
  //   ImportDialog: () => import('@/components/Upload/importDialog'),
  //   PredictImportDialogSlot: () =>
  //     import('../purchaseScheduleKT/components1/predict-import-dialog-slot.vue')
  // },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      supplierCodes: '',
      itemCodes: '',
      structSeqNo: '',
      statusOptions: [
        { text: this.$t('新建'), value: '0' },
        { text: this.$t('已修改'), value: '1' },
        { text: this.$t('已发布'), value: '2' },
        { text: this.$t('反馈-满足'), value: '3' },
        { text: this.$t('反馈-不满足'), value: '4' },
        { text: this.$t('已确认'), value: '5' },
        { text: this.$t('已关闭'), value: '6' }
        // { text: this.$t('同步中'), value: '7' }
      ],
      plannerListOptions: [], // 计划员 下列选项
      searchFormModel: {
        bigVersionNo: dayjs(new Date()).format('YYYYMMDD')
      },
      // saveUrl: `${BASE_TENANT}/po/time/config/upload`, // 导入API
      // fromDataKey: 'excel', // 上传组件的 name，此值为调用 api 请求参数的 key
      normalColumn: normalColumn,
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          buttonQuantity: 7,
          toolbar: {
            tools: [
              [
                // {
                //   id: 'Add',
                //   icon: 'icon_solid_Createorder',
                //   title: this.$t('新增')
                //   // permission: ["O_02_0436"],
                // },
                {
                  id: 'closeEdit',
                  icon: 'icon_table_delete',
                  // permission: ["O_02_1141"],
                  title: this.$t('取消编辑')
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete',
                  title: this.$t('删除')
                  // permission: ["O_02_0437"],
                },
                {
                  id: 'Publish',
                  icon: 'icon_solid_Activateorder',
                  title: this.$t('发布')
                  // permission: ["O_02_0438"],
                },
                {
                  id: 'Cancle',
                  icon: 'icon_solid_pushorder',
                  title: this.$t('取消发布')
                },
                // {
                //   id: 'Release',
                //   icon: 'icon_solid_Pauseorder',
                //   title: this.$t('下达')
                //   // permission: ["O_02_0439"],
                // },
                {
                  id: 'Confirm',
                  icon: 'icon_solid_Pauseorder',
                  title: this.$t('确认')
                  // permission: ["O_02_0440"],
                },
                {
                  id: 'Import',
                  icon: 'icon_solid_Import',
                  title: this.$t('导入')
                  // permission: ["O_02_0441"],
                },
                {
                  id: 'Export1',
                  icon: 'icon_solid_export',
                  title: this.$t('导出')
                  // permission: ["O_02_0442"],
                }
              ],
              ['Setting']
            ]
          },
          gridId: 'cc68214f-d641-4f9a-9a70-7ec6030c88f2',
          activatedRefresh: false,
          useCombinationSelection: false,
          grid: {
            columnData: checkColumn.concat(normalColumn),
            gridLines: 'Both',
            allowReordering: false,
            allowPaging: false, // 不分页
            dataSource: [],
            allowEditing: true, //开启表格编辑操作
            // height: 556,
            virtualPageSize: 10,
            rowHeight: 108,
            showSelected: false, // 是否允许跨页勾选
            selectionSettings: {
              persistSelection: true,
              type: 'Multiple',
              checkboxOnly: true
            },
            enableVirtualization: true,
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            }
          }
        }
      ],
      currentList: [], //从接口获取到的最新的数据
      nowEditRowFlag: '', //当前编辑的行id
      addId: '1',
      selectedOtherInfo: {},

      apiWaitingQuantity: 0, // 调用的api正在等待数
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 10, // 当前每页数据量
        totalPages: 0, // 总页数
        pageSizes: [10, 20, 50, 100, 200, 1000]
      },
      forecastPageCurrent: 1, // 预测表格 当前页码
      forecastRules: [], // 预测表格请求规则
      forecastCondition: 'and', // 过滤-规则-关系，默认为 "and"
      tableContainerClientHeight: 0,
      titleList: [],
      versionList: []
    }
  },
  mounted() {
    this.getPlannerList()
    this.buyerGoodsDemandPlanInfoQueryVersionTv()
    // this.buyerGoodsDemandPlanInfoQuery()
    // this.handleColumns([])
    setTimeout(() => {
      this.$set(this.forecastPageSettings, 'pageSize', 50)
    }, 500)
  },
  methods: {
    supplierChange(e) {
      if (e) {
        this.searchFormModel.supplierCodes = this.supplierCodes.split(' ')
      } else {
        this.searchFormModel.supplierCodes = null
      }
    },
    itemChange(e) {
      if (e) {
        this.searchFormModel.itemCodes = this.itemCodes.split(' ')
      } else {
        this.searchFormModel.itemCodes = null
      }
    },
    getPlannerList() {
      this.$API.predictCollaboration.getPlannerAllName().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.plannerListOptions = data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.userCode} - ${i.userName}`
            }
          })
          this.plannerListOptions.push({
            userCode: '',
            codeAndName: this.$t('计划员空')
          })
        }
      })
    },
    buyerGoodsDemandPlanInfoQueryVersionTv() {
      this.$API.deliverySchedule.buyerGoodsDemandPlanInfoQueryVersionTv().then((res) => {
        const { data, code } = res
        if (code === 200) {
          this.versionList = data
          this.searchFormModel.bigVersionNo = data[0]
          // this.buyerGoodsDemandPlanInfoQuery()
        }
      })
    },
    // 重置查询条件
    handleCustomReset() {
      this.structSeqNo = ''
      this.supplierCodes = null
      this.searchFormModel.supplierCodes = null
      this.itemCodes = null
      this.searchFormModel.itemCodes = null
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'bigVersionNo') {
            this.searchFormModel[key] = this.versionList[0]
          }
        }
      }
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.buyerGoodsDemandPlanInfoQuery()
      document.querySelector('.e-content').scrollTop = 0
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageCurrent = 1
      this.forecastPageSettings.pageSize = pageSize
      this.buyerGoodsDemandPlanInfoQuery()
      document.querySelector('.e-content').scrollTop = 0
    },
    // 采方-获取采方预测信息列表
    buyerGoodsDemandPlanInfoQuery() {
      const params = {
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        },
        ...this.searchFormModel
      }
      // this.apiStartLoading()
      this.$API.deliverySchedule.buyerGoodsDemandPlanInfoQueryTv(params).then((res) => {
        // this.apiEndLoading()
        if (res?.code == 200) {
          setTimeout(() => {
            document.querySelector('.e-content').scrollTop = 0
          }, 20)
          const total = res?.data?.buyerDemandPlanPage?.total || 0
          this.forecastPageSettings.totalPages = Math.ceil(
            Number(total) / this.forecastPageSettings.pageSize
          )
          // this.forecastPageSettings.totalRecordsCount = Number(total)
          this.$set(this.forecastPageSettings, 'totalRecordsCount', Number(total))
          const records = res?.data?.buyerDemandPlanPage?.records || [] // 表格数据
          let titleList = res?.data?.titleList || [] // 动态表头数据
          this.titleList = res?.data?.titleList || [] // 动态表头数据
          // 处理表头数据
          let dynamicCols = this.handleColumns(titleList)
          // 处理表数据
          let dataSource = this.handleDataSource(records, titleList)
          if (!this.searchFormModel.bigVersionNo) {
            // 处理表头数据
            dynamicCols = []
            // 处理表数据
            dataSource = []
            this.forecastPageSettings.totalPages = 1
            this.$set(this.forecastPageSettings, 'totalRecordsCount', 0)
          }
          const allCols = checkColumn.concat(normalColumn).concat(dynamicCols)
          this.componentConfig[0].grid['columnData'] = allCols
          setTimeout(() => {
            this.$set(this.componentConfig[0].grid, 'dataSource', dataSource)
            this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
          }, 20)
        }
      })
      // .catch(() => {
      //   this.apiEndLoading()
      // })
      // this.$refs.searchFormRef.validate((valid) => {
      //   if (valid) {
      //   }
      // })
    },
    handleDataSource(records, titleList) {
      // let list1 = []
      // titleList.forEach((item) => {
      //   if (item && item.length === 13) {
      //     list1.push(UTILS.default.dateFormat(Number(item), 'Y-m-d'))
      //   }
      // })
      // this.titleList = list1
      records.forEach((obj, index) => {
        obj.addId = this.addId++
        obj.isEntry = '2' //是否是带入的数据
        obj.warehouseCode = obj.invAddrCode
        obj.warehouseName = obj.invAddrName
        obj.lineNo = index + 1
        if (obj.tvBuyerDemandPlanExtList.length > 0) {
          titleList.forEach((title, index) => {
            let total = ''
            let buyerNum = ''
            let supplierNum = ''
            // let gapNum = ''
            // let countGapNum = ''
            obj.tvBuyerDemandPlanExtList.forEach((itm) => {
              if (!itm) {
                total = ''
                buyerNum = ''
                supplierNum = ''
              }
              if (itm.type === 'D') {
                total = itm[`planDemandQty${index + 1}`]
              }
              if (itm.type === 'P') {
                buyerNum = itm[`planDemandQty${index + 1}`]
              }
              if (itm.type === 'C') {
                supplierNum = itm[`planDemandQty${index + 1}`]
              }
            })
            obj[title] = `${total}_${buyerNum}_${supplierNum}_${0}_${0}`
          })
        }
      })
      return records
    },
    handleColumns(titleList) {
      let cols = []
      titleList.forEach((item) => {
        if (item && item.length === 13) {
          item = UTILS.default.dateFormat(Number(item), 'Y-m-d')
        }
        cols.push({
          field: item,
          headerText: item,
          width: '100',
          ignore: true,
          allowEditing: false,
          allowFiltering: false,
          template: () => {
            return { template: InputNumberView }
          },
          editTemplate: () => {
            return { template: InputNumber }
          }
        })
      })
      return cols
    },
    // 文件上传完成
    // uploadCompleted(response) {
    //   this.$refs.importDialog.showStepSecond()
    //   this.$nextTick(() => {
    //     this.$refs.predictImportDialogSlot.init({
    //       resData: response?.data || []
    //     })
    //   })
    // },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    },
    // 预测数据导入编辑弹框 点击导入
    // importDialogImport() {
    //   this.$refs.predictImportDialogSlot.doImport()
    // },
    // 导入提交
    // predictImportDialogSlotSubmitted() {
    //   this.$refs.importDialog.handleClose()
    //   this.updateList()
    // },
    handleClose(row) {
      //关闭
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认关闭选中的交货计划吗?')
        },
        success: () => {
          // let items = []
          // row.forEach((obj) => {
          //   items = items.concat(obj.item || [])
          // })
          // let ids = items.map((item) => item.id)
          // let params = ids
          const params = []
          // let ids = items.map((item) => item.id)
          row.forEach((i) => {
            params.push({
              id: i.id,
              status: i.status
            })
          })
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoCloseTv(params).then(() => {
            this.$toast({
              content: this.$t('关闭交货计划操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleCancle(row) {
      //取消发布
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认取消发布选中的交货计划吗?')
        },
        success: () => {
          // let items = []
          // row.forEach((obj) => {
          //   items = items.concat(obj.item || [])
          // })
          // let ids = items.map((item) => item.id)
          // let params = ids
          const params = {
            queryRulesReq: { ...this.searchFormModel, page: { current: 1, size: 999999 } },
            chooseOperateReqs: row.map((i) => {
              return {
                id: i.id,
                status: i.status,
                line: i.lineNo,
                supplierCode: i.supplierCode,
                bigVersionNo: i.bigVersionNo,
                md5Code: i.md5Code,
                sameBatchData: i.sameBatchData,
                supplierTenantId: i.supplierTenantId,
                supplierName: i.supplierName,
                plannerName: i.plannerName,
                planMemberCode: i.planMemberCode
              }
            })
          }
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoCancelTv(params).then(() => {
            this.$toast({
              content: this.$t('取消发布交货计划操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handlePublish(row) {
      //发布
      // let flag = true
      // row.forEach((item) => {
      //   if (!item.addressId) {
      //     flag = false
      //   }
      // })
      // if (!flag) {
      //   this.$toast({
      //     content: this.$t('送货联系人电话地址填写才可以发布'),
      //     type: 'warning'
      //   })
      //   return
      // }
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认发布选中的交货计划吗?')
        },
        success: () => {
          // let items = []
          // row.forEach((obj) => {
          //   items = items.concat(obj.item || [])
          // })
          const idList = []
          // let ids = items.map((item) => item.id)
          row.forEach((i) => {
            idList.push({
              id: i.id,
              status: i.status,
              line: i.lineNo,
              supplierCode: i.supplierCode,
              bigVersionNo: i.bigVersionNo,
              md5Code: i.md5Code,
              sameBatchData: i.sameBatchData,
              supplierTenantId: i.supplierTenantId,
              supplierName: i.supplierName,
              plannerName: i.plannerName,
              planMemberCode: i.planMemberCode
            })
          })
          const params = {
            queryRulesReq: { ...this.searchFormModel, page: { current: 1, size: 999999 } },
            chooseOperateReqs: idList
          }
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoPublishTv(params).then(() => {
            this.$toast({
              content: this.$t('发布交货计划操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleAdd() {
      //新增
      this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
    },
    handleImport() {
      //导入
      // this.$refs.importDialog.init({
      //   title: this.$t('导入')
      // })
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.deliverySchedule.buyerGoodsDemandPlanInfoImportTv,
          // downloadTemplateApi: this.$API.deliverySchedule.buyerGoodsDemandPlanInfoExportTv,
          paramsKey: 'excel'
          // asyncParams: {
          //   // requestJson: JSON.stringify(parameter),
          // },
        },
        success: () => {
          // 导入之后刷新列表
          this.$refs.templateRef1.refreshCurrentGridData()
        }
      })
    },
    handleExport() {
      //导出
      // let params = {
      //   condition: this.forecastCondition,
      //   page: { current: 1, size: 1000 },
      //   pageFlag: true,
      //   rules: [...this.forecastRules]
      // }
      const params = {
        page: {
          size: 1000,
          current: 1
        },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.deliverySchedule.buyerGoodsDemandPlanInfoExportTv(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleRelease(row) {
      //下达
      let hasOne = row.some((item) => {
        return item.release === '1'
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择未下达的行操作!'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认下达选中的交货计划吗?')
        },
        success: () => {
          let items = []
          row.forEach((obj) => {
            items = items.concat(obj.item || [])
          })
          let ids = items.map((item) => item.id)
          let params = ids
          this.$API.deliverySchedule.buyerGoodsDemandPlanInforelease(params).then(() => {
            this.$toast({
              content: this.$t('确认下达交货计划操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleConfirm(row) {
      //确认交货计划
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认选中的交货计划吗?')
        },
        success: () => {
          // let items = []
          // row.forEach((obj) => {
          //   items = items.concat(obj.item || [])
          // })
          // let ids = items.map((item) => item.id)
          // let params = ids
          const params = {
            queryRulesReq: { ...this.searchFormModel, page: { current: 1, size: 999999 } },
            chooseOperateReqs: row.map((i) => {
              return {
                id: i.id,
                status: i.status,
                line: i.lineNo,
                supplierCode: i.supplierCode,
                bigVersionNo: i.bigVersionNo,
                md5Code: i.md5Code,
                sameBatchData: i.sameBatchData,
                supplierTenantId: i.supplierTenantId,
                supplierName: i.supplierName,
                plannerName: i.plannerName,
                planMemberCode: i.planMemberCode
              }
            })
          }
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoConfirmTv(params).then(() => {
            this.$toast({
              content: this.$t('确认交货计划操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    generateUUID() {
      var d = new Date().getTime()
      if (window.performance && typeof window.performance.now === 'function') {
        d += performance.now() //use high-precision timer if available
      }
      var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (d + Math.random() * 16) % 16 | 0
        d = Math.floor(d / 16)
        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16)
      })
      return uuid
    },
    handleDelete(row) {
      //删除
      const idList = []
      // row.forEach((obj) => {
      //   // items = items.concat(obj.item || [])
      //   params.push({
      //     id: obj.id,
      //     status: obj.status
      //   })
      // })
      for (let i = 0; i < row.length; i++) {
        const obj = row[i]
        idList.push({
          id: obj.id,
          status: obj.status,
          line: obj.lineNo,
          supplierCode: obj.supplierCode,
          bigVersionNo: obj.bigVersionNo,
          md5Code: obj.md5Code,
          sameBatchData: obj.sameBatchData,
          supplierTenantId: obj.supplierTenantId,
          supplierName: obj.supplierName,
          plannerName: obj.plannerName,
          planMemberCode: obj.planMemberCode
        })
        // if (obj.status != 0 && obj.status != 1) {
        //   this.$toast({
        //     content: this.$t('仅可删除新建、已修改状态下的数据'),
        //     type: 'warning'
        //   })
        //   return false
        // }
      }
      const params = {
        queryRulesReq: { ...this.searchFormModel, page: { current: 1, size: 999999 } },
        chooseOperateReqs: idList
      }
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认删除选中的交货计划吗?')
        },
        success: () => {
          // let ids = items.map((item) => item.id)
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoDeleteTv(params).then(() => {
            this.$toast({
              content: this.$t('删除交货计划操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    updateList() {
      setTimeout(() => {
        document.querySelector('.e-content').scrollTop = 0
      }, 500)
      sessionStorage.removeItem('purchaseScheduleTvRow')
      this.$refs.templateRef1.refreshCurrentGridData()
      this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
    },
    selectedChanged1(val) {
      console.log(arguments, '-=-==')
      Object.assign(this.selectedOtherInfo, val.itemInfo)
      console.log(this.selectedOtherInfo, '最新的额外数据')
    },
    actionComplete(args) {
      console.log(args, '我是actionComplete')
      const { rowIndex, index } = args
      // 如果是虚拟滚动，需要延长loading的动画时间来阻止用户操作滚轮或者滚动条
      if (args.requestType === 'refresh') {
        if (!args.rows) {
          if (this.componentConfig[0].grid.dataSource?.length) {
            this.$refs.templateRef1.getCurrentUsefulRef().pluginRef.showCustomLoading()
          }
        } else {
          this.$refs.templateRef1.getCurrentUsefulRef().pluginRef.hideCustomLoading()
        }
      }
      if (args.requestType === 'save' && args.action === 'edit') {
        //编辑完成
        let row = this.getRow()
        this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
        if (row.isEntry === '2') {
          //新增错误重新编辑
          this.addRow(row, rowIndex)
        }
        if (row.isEntry === '1') {
          this.editRow(row, rowIndex)
        }
      }
      if (args.requestType === 'save' && args.action === 'add') {
        //新增完成
        let row = this.getRow()
        this.addRow(row, index)
        this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
      }
    },
    startEdit(index) {
      this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
      this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
    },
    endEdit() {
      //让组件失去焦点
      this.$refs.templateRef1.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
    },
    judgeValid1(params) {
      //添加校验
      console.log('我执行了1', params)
      let flag = true
      let flag1 = true
      params.some((item) => {
        if (item.buyerNum === '') {
          flag1 = false
        }
      })
      if (!flag1) {
        this.$toast({
          content: this.$t('P（需求量）必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.length) {
        this.$toast({
          content: this.$t('需求日期必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params[0].deliveryMethod) {
        this.$toast({
          content: this.$t('配送方式必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (params[0].deliveryMethod === '0') {
        //配送方式为直送
        if (!params[0].plusBizCirclesCode || !params[0].plusBizCirclesName) {
          this.$toast({
            content: this.$t('配送方式为直送时,加工商编码和名称必填'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      if (params[0].outsourceMethod === '2') {
        //委外方式非委外
        if (params[0].deliveryMethod === '0') {
          this.$toast({
            content: this.$t('委外方式为非委外时,配送方式需为非直送'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      return flag
    },
    editRow(row, index) {
      console.log(row, '编辑行数据')
      let params = []
      row.item.forEach((obj) => {
        params.push({
          id: row.id,
          outsourceMethod: row.outsourceMethod,
          workCenter: row.workCenter, //工作中心
          workCenterCode: row.workCenterCode, //工作中心
          workCenterName: row.workCenterName, //工作中心
          goodsDemandPlanItemId: obj.id,
          supplierCode: row.supplierCode, //供应商
          supplierId: row.supplierId, //供应商id
          supplierName: row.supplierName, //供应商名称
          deliveryMethod: row.deliveryMethod, //配送方式
          release: row.release, //下达
          associatedNumber: row.associatedNumber, //关联工单号
          bomNo: row.bomNo, //BOM
          projectTextBatch: row.projectTextBatch, //项目文本批次
          scheduleType: row.scheduleType, //计划类型
          jit: row.scheduleType === 'JIT' ? 1 : 0,

          // timeInfoTimestamp: new Date(`${obj.timeInfoTimestamp} 00:00:00`).getTime(), // 需求日期
          buyerNum: row[obj.timeInfoTimestamp]?.split('_')[1] || '', //p字段
          plusBizCirclesCode: row.plusBizCirclesCode, //加工商编码
          plusBizCirclesId: row.plusBizCirclesId, //加工商id
          plusBizCirclesName: row.plusBizCirclesName, //加工商名称
          invAddrCode: row.invAddrCode, //库存地点编码
          invAddrId: row.invAddrId, //库存地点id
          invAddrName: row.invAddrName, //库存地点名称
          // addressId: row.addressId, //地址id
          // receiveAddressName: row.receiveAddressName,
          // receiveAddressCode: row.receiveAddressCode,
          purchaserRemark: row.purchaserRemark // 采购备注
          // sourceFrom: 'KT'
        })
      })
      let flag = this.judgeValid1(params)
      if (!flag) {
        this.startEdit(index)
        return
      }
      this.$API.deliverySchedule
        .buyerGoodsDemandPlanInfoUpdateList(params)
        .then(() => {
          this.$toast({
            content: this.$t('编辑交货计划操作成功'),
            type: 'success'
          })
          this.updateList()
        })
        .catch(() => {
          this.startEdit(index)
        })
    },
    async addRow(row, index) {
      console.log(row, '新增行数据')
      let params = {
        // receiveAddressName: row.receiveAddressName,
        // receiveAddressCode: row.receiveAddressCode,
        // addressId: row.addressId, //地址id
        // associatedNumber: row.associatedNumber, //关联工单号
        // bomNo: row.bomNo, //BOM
        // buyerOrder: row.buyerOrder, //采购订单号
        // buyerOrderRowCode: row.buyerOrderRowCode, //采购订单行号
        // buyerOrgCode: row.buyerOrgCode, //采购组code
        // buyerOrgId: row.buyerOrgId, //采购组id
        // buyerOrgName: row.buyerOrgName, //采购组name
        // checkDate: 0, //确认日期         //需要给默认值
        // companyCode: row.companyCode, //公司code
        // companyId: row.companyId, //公司id
        // companyName: row.companyName, //公司name
        // deliveryStatus: 0, //发货状态            //需要给默认值
        // itemCode: row.itemCode, //物料code
        // itemId: row.itemId, //物料id
        // itemName: row.itemName, //物料name
        // itemGroupName: row.itemGroupName, //物料组名称
        // itemGroupCode: row.itemGroupCode, //物料组code
        // outsourceMethod: row.outsourceMethod, //委外方式
        // planGroupCode: row.planGroupCode, //计划组code
        // planGroupId: row.planGroupId, //计划组id
        // planGroupName: row.planGroupName, //计划组name
        // processName: row.processName, //工序名称
        // productCode: row.productCode, //产品代码
        // projectTextBatch: row.projectTextBatch, //项目文本批次
        // saleOrder: row.saleOrder, //销售订单
        // saleOrderRowCode: row.saleOrderRowCode, //销售订单行
        // scheduleArea: row.scheduleArea, //计划区域
        // scheduleType: row.scheduleType, //计划类型
        // jit: row.scheduleType === 'JIT' ? 1 : 0,

        // // serialNumber: this.generateUUID(), //序列号          //需要给默认值
        // tvBuyerDemandPlanExtList: row.tvBuyerDemandPlanExtList, // 排期数据集合
        // siteCode: row.siteCode, //工厂code
        // siteId: row.siteId, //工厂id
        // siteName: row.siteName, //工厂name
        // status: '', //状态                           //需要给默认值
        // supplierCheckUser: '', //供应商确认人       //需要给默认值
        // supplierCode: row.supplierCode, //供应商
        // supplierId: row.supplierId, //供应商id
        // supplierName: row.supplierName, //供应商名称
        // supplierRemark: '', //供应商备注         //需要给默认值
        // systemRemark: '', //系统备注                 //需要给默认值
        // version: '', //版本号                      //需要给默认值
        // invAddrCode: row.invAddrCode, //库存地点编码
        // invAddrId: row.invAddrId, //库存地点id
        // invAddrName: row.invAddrName, //库存地点名称
        // warehouseKeeper: row.warehouseKeeper, //仓管员
        // warehouseQty: 0, //库存数量           //需要给默认值
        // workCenter: row.workCenter, //工作中心
        // workCenterCode: row.workCenterCode, //工作中心
        // workCenterName: row.workCenterName, //工作中心
        // itemDescription: row.itemDescription, //物料描述
        // deliveryMethod: row.deliveryMethod, //配送方式
        // plusBizCirclesCode: row.plusBizCirclesCode, //加工商编码
        // plusBizCirclesId: row.plusBizCirclesId, //加工商id
        // plusBizCirclesName: row.plusBizCirclesName //加工商名称
        ...row,

        invAddrCode: row.warehouseCode,
        invAddrName: row.warehouseName
      }
      let list = []
      // console.log(this.)
      this.titleList.forEach((item, index) => {
        list.push({
          timeInfoTimestamp: new Date(`${item} 00:00:00`).getTime(),
          total: 0,
          buyerNum: row[item]?.split('_')[1] || '',
          purchaserRemark: row.purchaserRemark, // 采购备注
          release: 0, //下达
          limitNum: 0, //限量数量           //需要给默认值
          remainingNum: 0, //剩余可创建数量        //需要给默认值
          outstandingNum: 0 // 未清订单数量
        })
        params.tvBuyerDemandPlanExtList.forEach((i) => {
          if (i.type === 'P') {
            i[`planDemandQty${index + 1}`] = row[item]?.split('_')[1] || ''
          }
          i.dateText = ''
        })
      })
      // params.item = list
      let flag = await this.judgeValid(params, list)
      if (!flag) {
        this.startEdit(index)
        return
      }
      this.$API.deliverySchedule
        .buyerGoodsDemandPlanInfoUpdateTv({
          ...params,
          tvBuyerDemandPlanExtList: [params.tvBuyerDemandPlanExtList[1]]
        })
        .then(() => {
          this.$toast({
            content: this.$t('保存交货计划操作成功'),
            type: 'success'
          })
          this.updateList()
        })
        .catch(() => {
          this.startEdit(index)
        })
    },
    async judgeValid(params, list) {
      //添加校验
      let flag = true
      if (!params.itemCode) {
        this.$toast({
          content: this.$t('物料信息必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.supplierCode) {
        this.$toast({
          content: this.$t('供应商信息必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.factoryCode) {
        this.$toast({
          content: this.$t('工厂必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      // if (!params.companyCode) {
      //   this.$toast({
      //     content: this.$t('公司必填'),
      //     type: 'warning'
      //   })
      //   flag = false
      //   return
      // }
      // if (!params.scheduleType) {
      //   this.$toast({
      //     content: this.$t('JIT物料必填'),
      //     type: 'warning'
      //   })
      //   flag = false
      //   return
      // }
      let falg1 = true
      await list.forEach((item) => {
        if (item.buyerNum === '') {
          falg1 = false
        }
      })
      if (!falg1) {
        this.$toast({
          content: this.$t('P（需求量）必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.deliveryMethod) {
        this.$toast({
          content: this.$t('配送方式必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (params.deliveryMethod === '0') {
        //配送方式为直送
        if (!params.plusBizCirclesCode || !params.plusBizCirclesName) {
          this.$toast({
            content: this.$t('配送方式为直送时,加工商编码必填'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      if (params.outsourceMethod === '2') {
        //委外方式非委外
        if (params.deliveryMethod === '0') {
          this.$toast({
            content: this.$t('委外方式为非委外时,配送方式需为非直送'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      return flag
    },
    getRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.templateRef1
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = cloneDeep(this.selectedOtherInfo)
      let info = {}
      currentRecords.some((item) => {
        if (item.addId == this.nowEditRowFlag) {
          Object.assign(item, row)
          info = item
        }
      })
      const purchaseScheduleTvRow = sessionStorage.getItem('purchaseScheduleTvRow')
        ? JSON.parse(sessionStorage.getItem('purchaseScheduleTvRow'))
        : {}
      return { ...info, ...purchaseScheduleTvRow }
    },
    actionBegin(args) {
      console.log(args, '我是111actionBegin')
      if (args.requestType === 'add') {
        let normalColumn = cloneDeep(this.normalColumn)
        normalColumn.forEach((item) => {
          args.data[item.field] = ''
          if (item.field === 'status') {
            args.data[item.field] = this.$t('新建')
          }
          if (item.field === 'limitNum') {
            args.data[item.field] = '0'
          }
          if (item.field === 'remainingNum') {
            args.data[item.field] = '0'
          }
          if (item.field === 'warehouseQty') {
            args.data[item.field] = '0'
          }
          if (item.field === 'timeInfoTimestamp') {
            args.data[item.field] = new Date()
          }
          if (item.field === 'scheduleType') {
            args.data[item.field] = this.$t('非JIT')
          }
          if (item.field === 'outsourceMethod') {
            args.data[item.field] = '2'
          }
          if (item.field === 'deliveryMethod') {
            args.data[item.field] = '1'
          }
          args.data.isEntry = '2'
        })
        args.data.addId = this.addId++
        this.nowEditRowFlag = args.data.addId
      }
      if (args.requestType == 'beginEdit') {
        this.nowEditRowFlag = args.rowData.addId
        if (
          !(args.rowData.status === 0 || args.rowData.status === 1 || args.rowData.status === 4)
        ) {
          args.cancel = true
          this.$toast({
            content: this.$t('此状态不可编辑'),
            type: 'warning'
          })
          return
        }
        // const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
        // if (args.rowData.plannerName !== userInfo.userName) {
        //   args.cancel = true
        //   this.$toast({
        //     content: this.$t('不可操作非本人的数据'),
        //     type: 'warning'
        //   })
        // }
      }
    },
    //点击表格的操作按钮
    handleClickCellTool(e) {
      console.log('方法2', e)
      if (e.tool.id === 'Publish') {
        this.handlePublish([e.data])
      }
      if (e.tool.id === 'Cancle') {
        this.handleCancle([e.data])
      }
      if (e.tool.id === 'Close') {
        this.handleClose([e.data])
      }
      if (e.tool.id === 'Confirm') {
        this.handleConfirm([e.data])
      }
    },
    //点击顶部的操作按钮
    handleClickToolBar(e) {
      // const commonToolbar = [
      //   'ForecastUpdate',
      //   'ForecastAdd',
      //   'ForecastImport',
      //   'Filter',
      //   'Refresh',
      //   'refreshDataByLocal',
      //   'filterDataByLocal',
      //   'resetDataByLocal',
      //   'ForecastExport',
      //   'Delete',
      //   'Setting'
      // ]
      if (e.toolbar.id === 'closeEdit') {
        e.grid.closeEdit()
        return
      }
      if (e.toolbar.id === 'Add') {
        this.handleAdd()
        return
      }
      if (e.toolbar.id === 'Import') {
        this.handleImport()
        return
      }
      if (e.toolbar.id === 'Export1') {
        this.handleExport()
        return
      }
      if (e.toolbar.id === 'refreshDataByLocal') {
        this.buyerGoodsDemandPlanInfoQuery()
        return
      }
      if (e.toolbar.id === 'filterDataByLocal') {
        // 筛选-过滤
        const { condition, rules: ruleList } = e.rules
        this.forecastCondition = condition
        this.forecastRules = ruleList
        // 采方-获取采方预测信息列表
        this.forecastPageCurrent = 1
        this.buyerGoodsDemandPlanInfoQuery()
        return
      }
      if (e.toolbar.id === 'resetDataByLocal') {
        // 筛选重置
        this.forecastCondition = 'and'
        this.forecastRules = []
        // 采方-获取采方预测信息列表
        this.forecastPageCurrent = 1
        this.buyerGoodsDemandPlanInfoQuery()
        return
      }
      let selectRecords = e.grid.getSelectedRecords()
      // if (!selectRecords.length && !commonToolbar.includes(e.toolbar.id)) {
      //   this.$toast({ content: this.$t('请至少选择一行'), type: 'warning' })
      //   return
      // }
      if (e.toolbar.id === 'Delete') {
        this.handleDelete(selectRecords)
      }
      if (e.toolbar.id === 'Publish') {
        this.handlePublish(selectRecords)
      }
      if (e.toolbar.id === 'Cancle') {
        this.handleCancle(selectRecords)
      }
      if (e.toolbar.id === 'Confirm') {
        this.handleConfirm(selectRecords)
      }
      if (e.toolbar.id === 'Release') {
        this.handleRelease(selectRecords)
      }
    },
    selectCell() {
      console.log(arguments, 9999)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/_mixin.scss';

.flex-fit {
  // container不是固定命名，根据实际页面样式命名来写
  ::v-deep .e-rowcell.sticky-col-0,
  ::v-deep .e-headercell.sticky-col-0 {
    @include sticky-col;
    left: 0px;
  }
  ::v-deep .e-rowcell.sticky-col-1,
  ::v-deep .e-headercell.sticky-col-1 {
    @include sticky-col;
    left: 50px; // 50px就是前面所有固定列的列宽相加，比如这里就是 0 + 复选框列（50px） = 50px
  }
  ::v-deep .e-rowcell.sticky-col-2,
  ::v-deep .e-headercell.sticky-col-2 {
    @include sticky-col;
    left: 122px; // 50px就是前面所有固定列的列宽相加，比如这里就是 0 + 复选框列（50px） = 50px
  }
  ::v-deep .e-rowcell.sticky-col-3,
  ::v-deep .e-headercell.sticky-col-3 {
    @include sticky-col;
    left: 241px; // 50px就是前面所有固定列的列宽相加，比如这里就是 0 + 复选框列（50px） = 50px
  }
  ::v-deep .e-rowcell.sticky-col-4,
  ::v-deep .e-headercell.sticky-col-4 {
    @include sticky-col;
    left: 398px; // 50px就是前面所有固定列的列宽相加，比如这里就是 0 + 复选框列（50px） = 50px
  }
}
::v-deep .e-columnheader {
  height: 40px !important;
}
::v-deep .field-content span {
  display: block;
  height: 10px;
}
::v-deep .e-rowcell p {
  white-space: inherit !important;
}
/deep/ .ant-select-selection {
  background: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.42) !important;
}
/deep/ .e-grid .e-altrow {
  background-color: #e9e9e9;
}
/deep/ .e-grid .e-gridcontent {
  td.e-rowcell:not(:first-of-type) {
    padding: 0 0px !important;
  }
}
</style>
