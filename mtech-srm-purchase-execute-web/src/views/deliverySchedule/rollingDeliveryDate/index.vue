<template>
  <iframe :src="iframeUrl" frameborder="0" class="ps-iframe"></iframe>
</template>
<script>
export default {
  data() {
    return {
      iframeUrl: ''
    }
  },
  created() {
    const userInfo = JSON.parse(sessionStorage.getItem('userInfo') || {})
    this.iframeUrl = `https://aps-web.tcl.com/#/tcl/ps/materialplatform/scrolldelivery?isIframePage=srm&userName=${userInfo.externalCode}`
  }
}
</script>
<style lang="scss" scoped>
.ps-iframe {
  width: 100%;
  height: 100%;
  margin-top: 16px;
}
</style>
