import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'

export default {
  components: { VxeRemoteSearch },
  data() {
    return {
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
        { code: 'clearEdit', name: this.$t('取消编辑'), status: 'info', loading: false },
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
        { code: 'enable', name: this.$t('生效'), status: 'info', loading: false },
        { code: 'disable', name: this.$t('失效'), status: 'info', loading: false },
        { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
      ]
    }
  },
  computed: {
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          field: 'companyCode',
          title: this.$t('公司代码'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.companyCode}
                  options={this.companyOptions}
                  option-props={{ label: 'text', value: 'value' }}
                  placeholder={this.$t('请选择')}
                  filterable
                  clearable
                  transfer
                  onChange={() => {
                    row.companyName = this.companyOptions.find(
                      (item) => item.value === row.companyCode
                    )?.orgName
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'companyName',
          title: this.$t('公司名称'),
          minWidth: 160
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商代码'),
          minWidth: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.supplierCode}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'supplierName', value: 'supplierCode' }}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'supplierPagedQuery',
                    searchFields: ['supplierCode', 'supplierName'],
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    row.supplierName = e?.supplierName || null
                    row.supplierTenantId = e?.supplierTenantId || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          minWidth: 160
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.itemCode}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'itemName', value: 'itemCode' }}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'getItemPage',
                    searchFields: ['itemCode', 'itemName'],
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    row.itemName = e?.itemName || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'itemName',
          title: this.$t('物料名称'),
          minWidth: 160
        },
        {
          field: 'extMaterialGroup',
          title: this.$t('外部物料组'),
          minWidth: 160,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <VxeRemoteSearch
                  v-model={row.extMaterialGroup}
                  placeholder={this.$t('请选择')}
                  fields={{ text: 'categoryName', value: 'categoryCode' }}
                  request-info={{
                    urlPre: 'masterData',
                    url: 'pageCategoryApi',
                    searchFields: ['categoryCode', 'categoryName'],
                    params: {
                      page: {
                        current: 1,
                        size: 50
                      }
                    },
                    recordsPosition: 'data.records'
                  }}
                  onChange={(e) => {
                    row.extMaterialGroupDesc = e?.categoryName || null
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'extMaterialGroupDesc',
          title: this.$t('外部物料组描述'),
          minWidth: 160
        },
        {
          field: 'status',
          title: this.$t('状态'),
          minWidth: 120,
          editRender: {},
          slots: {
            default: ({ row }) => {
              let label = this.statusOptions.find((item) => item.value === row.status)?.label
              return [<div>{label || ''}</div>]
            }
          }
        },
        {
          field: 'createUserName',
          title: this.$t('创建人'),
          minWidth: 120
        },
        {
          field: 'createTime',
          title: this.$t('创建日期'),
          minWidth: 160
        },
        {
          field: 'updateUserName',
          title: this.$t('更新人'),
          minWidth: 120
        },
        {
          field: 'updateTime',
          title: this.$t('更新日期'),
          minWidth: 160
        }
      ]
    }
  },
  mounted() {},
  methods: {}
}
