<!-- PSI范围设置 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item prop="companyCode" :label="$t('公司代码')" label-style="top">
          <mt-select
            v-model="searchFormModel.companyCode"
            :data-source="companyOptions"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商代码')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      row-id="id"
      grid-id="eb483df0-1b3c-471b-80e0-9b9d2b3b2902"
      align="center"
      show-overflow
      keep-source
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      @edit-closed="editComplete"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :loading="item.loading"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import pagingMixin from '@/mixins/paging.js'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  components: {
    CollapseSearch,
    ScTable
  },
  mixins: [mixin, pagingMixin],
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      tableData: [],
      loading: false,
      editConfig: {
        trigger: 'click',
        mode: 'row',
        showStatus: true
      },
      editRules: {
        companyCode: [{ required: true, message: this.$t('必填') }],
        supplierCode: [{ required: true, message: this.$t('必填') }]
      },
      statusOptions: [
        { label: this.$t('有效'), value: 0 },
        { label: this.$t('无效'), value: 1 }
      ],
      companyOptions: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getCompanyOptions()
  },
  methods: {
    getCompanyOptions() {
      this.$API.reconciliationReport.getCompanyList().then((res) => {
        if (res.code === 200) {
          this.companyOptions = res.data.map((item) => {
            return {
              ...item,
              text: item.orgCode + '-' + item.orgName,
              value: item.orgCode
            }
          })
        }
      })
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch(type) {
      this.tableRef.clearEdit()
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          if (!type && this.$refs.pageRef) {
            this.pageInfo.current = 1
            this.$refs.pageRef.jumpNum = 1
            this.$refs.pageRef.currentPage = 1
          }
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    // 获取table数据
    async getTableData() {
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.deliverySchedule
        .pagePsiConfigApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['enable', 'disable', 'delete'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'clearEdit':
          this.handleSearch()
          break
        case 'enable':
          this.handleEnable(selectedRecords)
          break
        case 'disable':
          this.handleDisable(selectedRecords)
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          this.handleExport()
          break
        default:
          break
      }
    },
    handleAdd() {
      const item = {}
      this.tableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.tableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }
          this.handleSave(row)
        })
      }
    },
    handleSave(row) {
      let params = cloneDeep(row)
      if (params.id?.includes('row_')) {
        params.id = null
      }
      this.$API.deliverySchedule
        .savePsiConfigApi(params)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.handleSearch()
          }
        })
        .catch(() => {
          // 当出现错误时，指定行进入编辑状态
          this.tableRef.setEditRow(row)
        })
    },
    handleEnable(selectedRecords) {
      if (selectedRecords.some((v) => v.status === 0)) {
        this.$toast({ content: this.$t('只能生效【无效】状态的数据'), type: 'warning' })
        return
      }
      let ids = selectedRecords.map((v) => v.id)
      this.handleOperate('enable', { ids, status: 0 })
    },
    handleDisable(selectedRecords) {
      if (selectedRecords.some((v) => v.status === 1)) {
        this.$toast({ content: this.$t('只能失效【有效】状态的数据'), type: 'warning' })
        return
      }
      let ids = selectedRecords.map((v) => v.id)
      this.handleOperate('disable', { ids, status: 1 })
    },
    handleDelete(selectedRecords) {
      // if (selectedRecords.some((v) => v.status === 1)) {
      //   this.$toast({ content: this.$t('【有效】状态的数据不能删除'), type: 'warning' })
      //   return
      // }
      const ids = selectedRecords.map((v) => v.id)
      this.handleOperate('delete', ids)
    },
    async handleOperate(type, params) {
      let api = null,
        content = ''
      switch (type) {
        case 'enable':
          api = this.$API.deliverySchedule.statusPsiConfigApi
          content = this.$t('生效成功')
          break
        case 'disable':
          api = this.$API.deliverySchedule.statusPsiConfigApi
          content = this.$t('失效成功')
          break
        case 'delete':
          api = this.$API.deliverySchedule.deletePsiConfigApi
          content = this.$t('删除成功')
          break
        default:
          break
      }
      const res = await api(params)
      if (res.code === 200) {
        this.$toast({ content: content, type: 'success' })
        this.handleSearch()
      }
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.deliverySchedule.importPsiConfigApi,
          downloadTemplateApi: this.$API.deliverySchedule.downloadPsiConfigApi,
          paramsKey: 'excel'
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    handleExport(e) {
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      e.loading = true
      this.$API.deliverySchedule
        .exportPsiConfigApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>
