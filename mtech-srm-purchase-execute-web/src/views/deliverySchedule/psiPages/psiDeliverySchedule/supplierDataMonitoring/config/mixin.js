export default {
  data() {
    return {
      columns: [],
      toolbar: [
        {
          code: 'export',
          name: this.$t('导出'),
          status: 'info',
          loading: false,
          permission: ['O_02_1759']
        }
      ]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    getDayValue() {
      return (column, row, type) => {
        let value = '',
          rowData = {}
        // console.log(row, 'row==>')
        switch (type) {
          case 1:
            rowData = row['stockSystemList'][0] || {}
            value = column.field === 'total' ? rowData['count'] : rowData[column.field]
            break
          case 2:
            rowData = row['stockHandleList'][0] || {}
            value = column.field === 'total' ? rowData['count'] : rowData[column.field]
            break
          case 3:
            rowData = row['dayPlanSystemList'][0] || {}
            value = column.field === 'total' ? rowData['count'] : rowData[column.field]
            break
          case 4:
            rowData = row['dayPlanHandleList'][0] || {}
            value = column.field === 'total' ? rowData['count'] : rowData[column.field]
            break
        }
        return value
      }
    },
    getTotal() {
      return (row) => {
        let dayList = Object.keys(row).filter((i) => i.indexOf('srmField') > -1)
        let total = 0
        dayList.forEach((key) => {
          if ([null, undefined, ''].includes(row[key]) === false) total += 1
        })
        return total
      }
    }
  }
}
