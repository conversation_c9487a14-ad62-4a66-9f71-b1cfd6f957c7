<!--采方-供方数据对接监控表-->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <!-- <mt-form-item :label="$t('监控月份')" prop="month">
          <mt-select
            v-model="searchFormModel.month"
            :data-source="monthList"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item> -->

        <mt-form-item :label="$t('监控年月')" prop="date">
          <mt-date-picker
            start="Year"
            depth="Year"
            format="yyyy-MM"
            :allow-edit="false"
            :show-clear-button="false"
            v-model="searchFormModel.date"
            :placeholder="$t('选择年月')"
            @change="handleDateChange"
          ></mt-date-picker>
        </mt-form-item>

        <mt-form-item :label="$t('供应商')" prop="supplierCodes">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCodes"
            url="/masterDataManagement/tenant/supplier/paged-query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
            multiple
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>

    <sc-table
      ref="sctableRef"
      row-id="id"
      class="my-table"
      grid-id="11632cf8-a86e-4f46-bca9-9ab2b4a5df12"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :row-config="{ height: rowHeight }"
      :is-show-refresh-bth="true"
      :sortable="false"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          v-permission="item.permission"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>

      <template #rowSpan2>
        <div class="rowspan rowspan2-div">供方库存</div>
        <div class="rowspan rowspan2-div">供方日计划</div>
      </template>

      <template #rowSpan4="{ row, column }">
        <div class="rowspan rowspan4-div">
          {{ column.field === 'synchroType' ? '系统' : getDayValue(column, row, 1) }}
        </div>
        <div class="rowspan rowspan4-div">
          {{ column.field === 'synchroType' ? '手工' : getDayValue(column, row, 2) }}
        </div>
        <div class="rowspan rowspan4-div">
          {{ column.field === 'synchroType' ? '系统' : getDayValue(column, row, 3) }}
        </div>
        <div class="rowspan rowspan4-div">
          {{ column.field === 'synchroType' ? '手工' : getDayValue(column, row, 4) }}
        </div>
      </template>
    </sc-table>

    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
// import cloneDeep from 'lodash/cloneDeep'
import { getHeadersFileName, download } from '@/utils/utils'
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import { cloneDeep } from 'lodash'
export default {
  components: {
    CollapseSearch,
    ScTable
  },
  mixins: [mixin],
  data() {
    return {
      searchFormModel: {},
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      rowHeight: 96,
      tableData: [],
      monthList: [],
      loading: false
    }
  },
  created() {
    let m = 0,
      year = dayjs().year()
    while (m < 12) {
      m++
      this.monthList.push({ value: `${year}-${m < 10 ? '0' + m : m}`, text: m + '月' })
    }
    // console.log(this.monthList, ' this.monthList')
    let curM = dayjs().month() + 1
    this.searchFormModel.month = `${year}-${curM < 10 ? '0' + curM : curM}`
    this.searchFormModel.date = this.searchFormModel.month
    // console.log(dayjs().month() + 1, 'month')
    this.handleSearch()
  },
  methods: {
    handleDateChange(val) {
      if (val) {
        const year = val.getFullYear(),
          month = val.getMonth() + 1
        this.searchFormModel['month'] = `${year}-${month < 10 ? '0' + month : month}`
      }
    },
    handleCurrentChange(currentPage) {
      this.pageSettings.current = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      // this.pageSettings.current = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    handleClickToolBar(e) {
      // const selectedRecords = this.tableRef.getCheckboxRecords()
      // 导出
      e.loading = true
      this.handleExport(e)
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      let curM = dayjs().month() + 1,
        year = dayjs().year()
      this.searchFormModel.month = `${year}-${curM < 10 ? '0' + curM : curM}`
      this.searchFormModel.date = this.searchFormModel.month
      this.handleSearch()
    },
    handleSearch() {
      this.pageSettings.current = 1
      this.getTableData()
    },
    handleExport(e) {
      const query = { ...this.searchFormModel }
      delete query.date
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...query
      }
      this.$store.commit('startLoading')
      this.$API.deliverySchedule
        .exportSupplierDataMonitoringApi(params)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    // 获取table数据
    async getTableData() {
      const query = { ...this.searchFormModel }
      delete query.date
      this.updateColumns()
      const params = {
        page: {
          current: this.pageSettings.current,
          size: this.pageSettings.pageSize
        },
        ...query
      }
      this.loading = true
      const res = await this.$API.deliverySchedule
        .pageSupplierDataMonitoringApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      this.tableData = []
      if (res.code === 200) {
        let dataList = res.data?.records || []
        let newArr = []
        // type 1 库存 2 日计划
        // dataSource 0 系统 1 手工
        dataList.forEach((item) => {
          const repeat = newArr.find((i) => i.supplierCode === item.supplierCode)

          if (!repeat) {
            let sameSupplier = dataList.filter((i) => i.supplierCode === item.supplierCode) // 同一个供应商的数据
            let stockSystemList = [],
              stockHandleList = [],
              dayPlanSystemList = [],
              dayPlanHandleList = []

            for (let i = 0; i < sameSupplier.length; i++) {
              let row = cloneDeep(sameSupplier[i])
              delete row.id
              if ([1, '1'].includes(row.type)) {
                if ([0, '0'].includes(row.dataSource)) {
                  // 库存系统
                  stockSystemList.push(row)
                }

                if ([1, '1'].includes(row.dataSource)) {
                  // 库存手工
                  stockHandleList.push(row)
                }
              } else if ([2, '2'].includes(row.type)) {
                if ([0, '0'].includes(row.dataSource)) {
                  // 日计划系统
                  dayPlanSystemList.push(row)
                }
                if ([1, '1'].includes(row.dataSource)) {
                  // 日计划手工
                  dayPlanHandleList.push(row)
                }
              }
            }
            item['stockSystemList'] = stockSystemList
            item['stockHandleList'] = stockHandleList
            item['dayPlanSystemList'] = dayPlanSystemList
            item['dayPlanHandleList'] = dayPlanHandleList
            newArr.push(cloneDeep(item))
          }
        })

        this.tableData = newArr

        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / 4 / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Math.ceil(Number(res.data?.total) / 4)
      }
    },
    updateColumns() {
      this.columns = defaultColumns(this)
      const daysInMonth = dayjs(this.searchFormModel.month).daysInMonth() // 天数
      let day = 1
      while (day <= daysInMonth) {
        const field = 'srmField' + day
        const repeat = this.columns.find((i) => i.field === field)
        if (!repeat) {
          this.columns.push({
            field,
            title: day + '',
            minWidth: 130,
            sortable: false,
            slots: { default: 'rowSpan4' }
          })
        }
        day++
      }
    }
  }
}
const defaultColumns = (that) => [
  {
    type: 'seq',
    title: that.$t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'supplierCode',
    title: that.$t('供应商编码'),
    minWidth: 120,
    fixed: 'left'
  },
  {
    field: 'supplierName',
    title: that.$t('供应商名称'),
    minWidth: 170,
    fixed: 'left'
  },
  {
    field: 'synchrodata',
    title: that.$t('同步数据'),
    minWidth: 100,
    fixed: 'left',
    sortable: false,
    slots: { default: 'rowSpan2' }
  },
  {
    field: 'synchroType',
    title: that.$t('同步类型'),
    minWidth: 100,
    fixed: 'left',
    sortable: false,
    slots: { default: 'rowSpan4' }
  },
  {
    field: 'total',
    title: that.$t('合计'),
    minWidth: 100,
    fixed: 'left',
    sortable: false,
    slots: { default: 'rowSpan4' }
  }
]
</script>

<style scoped>
.my-table .rowspan {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.my-table .rowspan2-div {
  height: 48px;
}
.my-table .rowspan4-div {
  height: 24px;
}

.my-table .rowspan2-div:last-child::before {
  content: '';
  position: absolute;
  top: 0;
  width: 110%;
  height: 1px;
  background-color: #e6e9ed;
  /* border-bottom: 1px solid red; */
  transform: scaleY(0.5);
}

.my-table .rowspan4-div:nth-last-child(1)::before,
.rowspan4-div:nth-last-child(2)::before,
.rowspan4-div:nth-last-child(3)::before {
  content: '';
  position: absolute;
  top: 0;
  width: 110%;
  height: 1px;
  background-color: #e6e9ed;
  /* border-bottom: 1px solid red; */
  transform: scaleY(0.5);
}
</style>
