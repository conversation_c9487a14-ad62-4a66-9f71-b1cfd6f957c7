export default {
  data() {
    return {
      toolbar: [
        { code: 'recalculate', name: this.$t('重新计算'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
        { code: 'detailExport', name: this.$t('差异明细导出'), status: 'info', loading: false }
      ],
      fixedColumn: [
        // {
        //   width: 50,
        //   type: 'checkbox'
        // },
        // {
        //   type: 'seq',
        //   title: this.$t('序号'),
        //   width: 50
        // },
        {
          field: 'itemCode',
          title: this.$t('物料编码'),
          fixed: 'left'
        },
        {
          field: 'itemName',
          title: this.$t('物料描述'),
          fixed: 'left'
        },
        {
          field: 'plannerName',
          title: this.$t('计划员'),
          minWidth: 80,
          fixed: 'left'
        },
        {
          field: 'extMaterialGroup',
          title: this.$t('品类'),
          minWidth: 65,
          fixed: 'left'
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商代码'),
          fixed: 'left'
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          fixed: 'left'
        },
        {
          field: 'supplierItemCode',
          title: this.$t('供方物料编码'),
          minWidth: 120,
          fixed: 'left'
        },
        {
          field: 'ratio',
          title: this.$t('比例'),
          minWidth: 65,
          fixed: 'left'
        },
        {
          field: 'countType',
          title: this.$t('统计类别'),
          className: 'vxe-table-multi-cell',
          minWidth: 90,
          fixed: 'left',
          slots: {
            default: 'typeDefault'
          }
        },
        {
          field: 'supplierInventory',
          title: this.$t('供方库存'),
          minWidth: 90,
          fixed: 'left'
        }
      ]
    }
  },
  computed: {},
  mounted() {},
  methods: {}
}
