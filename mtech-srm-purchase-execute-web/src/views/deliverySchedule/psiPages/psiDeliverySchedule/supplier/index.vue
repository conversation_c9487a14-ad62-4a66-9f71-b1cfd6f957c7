<!-- 供方-PSI交货计划 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('数据计算日期')" prop="dataDate">
          <mt-date-picker
            v-model="searchFormModel.dataDate"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierItemCode" :label="$t('供应商物料编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierItemCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('计划员')" prop="planMemberCode">
          <RemoteAutocomplete
            v-model="searchFormModel.planMemberCode"
            url="/masterDataManagement/tenant/employee/paged-query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'employeeName', value: 'employeeCode' }"
            :search-fields="['employeeName', 'employeeCode']"
            multiple
          />
        </mt-form-item>
        <mt-form-item :label="$t('统计类别')" prop="countTypes">
          <div style="display: flex">
            <mt-multi-select
              v-model="searchFormModel.countTypes"
              :data-source="countTypeOptions"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
              style="flex: 1"
            />
            <mt-checkbox
              v-model="searchFormModel.exceptionFlag"
              @change="(e) => handleChange(e, 'exceptionFlag')"
              :label="$t('PSI异常')"
              style="vertical-align: sub; display: inline-block; margin-left: 5px; width: 78px"
            />
          </div>
        </mt-form-item>
        <mt-form-item :label="$t('品类')" prop="extMaterialGroup">
          <RemoteAutocomplete
            v-model="searchFormModel.extMaterialGroup"
            url="/masterDataManagement/tenant/category/paged-query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'categoryName', value: 'categoryCode' }"
            :search-fields="['categoryName', 'categoryCode']"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      row-id="id"
      grid-id="8cee06b8-112b-4dcd-a13b-a5d132e99a4d"
      show-overflow
      keep-source
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :row-config="{ height: rowHeight }"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :loading="item.loading"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
      <template #typeDefault="{ row }">
        <div v-for="(item, index) in row.detailData" :key="index" class="vxe-cell-border">
          <span style="margin-left: 10px">{{ item.countTypeDesc }}</span>
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import mixin from './config/mixin'
import pagingMixin from '@/mixins/paging.js'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  components: {
    CollapseSearch,
    ScTable
  },
  mixins: [mixin, pagingMixin],
  data() {
    return {
      searchFormModel: {
        dataDate: new Date()
      },
      searchFormRules: {
        dataDate: [{ required: true, message: this.$t('请选择数据计算日期'), trigger: 'blur' }]
      },
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 30,
        pageSizes: [30, 60, 180, 480, 900]
      },
      pageInfo: {
        size: 30,
        current: 1
      },
      tableData: [],
      loading: false,
      columns: [],
      rowHeight: 96,
      countTypeOptions: [
        { text: this.$t('日需求'), value: 1 },
        { text: this.$t('供方日产出'), value: 2 },
        { text: this.$t('PSI'), value: 3 }
      ]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getHeader()
  },
  methods: {
    getHeader() {
      let params = {
        dataDate: dayjs(this.searchFormModel.dataDate).format('YYYY-MM-DD')
      }
      this.$API.deliverySchedule.headerPsApi(params).then((res) => {
        if (res.code === 200) {
          this.handleColumns(res.data)
        }
      })
    },
    handleColumns(arr) {
      let currentColumn = this.fixedColumn
      const dynamicColumn = []
      arr.forEach((item, i) => {
        const title = `srmField${i + 1}`
        dynamicColumn.push({
          title: item,
          field: title,
          width: 110,
          editRender: {},
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return row.detailData.map((item, index) => (
                <div key={index} class='vxe-cell-border'>
                  <span style={{ 'margin-left': '10px' }} class={item[title] < 0 ? 'red' : ''}>
                    {item[title]}
                  </span>
                </div>
              ))
            }
          }
        })
      })
      this.columns = currentColumn.concat(dynamicColumn)
    },
    handleChange(e, labelName) {
      this.searchFormModel[labelName] = e.checked
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          if (key === 'dataDate') {
            this.searchFormModel[key] = new Date()
          } else {
            this.searchFormModel[key] = null
          }
        }
      }
      this.handleSearch()
    },
    handleSearch(type) {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          if (!type && this.$refs.pageRef) {
            this.pageInfo.current = 1
            this.$refs.pageRef.jumpNum = 1
            this.$refs.pageRef.currentPage = 1
          }
          this.getHeader()
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    // 获取table数据
    async getTableData() {
      const params = {
        bizTypeId: 2,
        page: this.pageInfo,
        ...this.searchFormModel
      }
      params.dataDate = dayjs(params.dataDate).format('YYYYMMDD')
      params.exceptionFlag = params.exceptionFlag ? 1 : 0
      this.loading = true
      const res = await this.$API.deliverySchedule
        .pagePsiDeliveryScheduleSupApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          this.handleExport(e)
          break
        case 'detailExport':
          this.handleDetailExport(e)
          break
        default:
          break
      }
    },
    handleExport(e) {
      e.loading = true
      const params = {
        bizTypeId: 2,
        page: this.pageInfo,
        ...this.searchFormModel
      }
      params.dataDate = dayjs(params.dataDate).format('YYYYMMDD')
      params.exceptionFlag = params.exceptionFlag ? 1 : 0
      this.$API.deliverySchedule
        .exportPsiDeliveryScheduleSupApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    handleDetailExport(e) {
      e.loading = true
      const params = {
        bizTypeId: 2,
        page: this.pageInfo,
        ...this.searchFormModel
      }
      params.dataDate = dayjs(params.dataDate).format('YYYYMMDD')
      params.exceptionFlag = 1
      this.$API.deliverySchedule
        .exportDetailPsiDeliveryScheduleSupApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  .vxe-cell-border {
    &:first-child {
      border-top: none;
    }
    border-top: solid #e6e9ed 1px;
    height: 32px;
    line-height: 32px;
    box-sizing: border-box;
    .red {
      color: red;
    }
  }
}
</style>
