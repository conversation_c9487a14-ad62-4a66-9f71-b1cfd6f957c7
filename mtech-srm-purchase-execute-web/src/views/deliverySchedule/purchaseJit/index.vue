<template>
  <!-- JIT送货计划-采方 -->
  <div class="full-height pt20">
    <!-- 列模板 -->
    <mt-template-page
      ref="templateRef"
      class="frozenFistColumns"
      :hidden-tabs="false"
      :template-config="templateConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @siteCodeChangeRo="siteCodeChangeRo"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
      @dataBound="handleDataBound"
      @handleSearch="handleSearch"
    />
    <!-- 物料选择 弹框 -->
    <materiel-select-dialog
      ref="materielSelectDialog"
      @confirm="materielSelectDialogConfirm"
    ></materiel-select-dialog>
    <!-- 导入弹框 -->
    <!-- <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog> -->

    <import-dialog
      :accept="['.xls', '.xlsx']"
      :from-data-key="fromDataKey"
      :down-template-params="downTemplateParams"
      :save-url="saveUrl"
      :down-load="true"
      ref="importDialog"
      :request-urls="requestUrls"
      dialog-class="create-proj-dialog full-size-dialog"
      @import="importDialogImport"
      @uploadCompleted="uploadCompleted"
    >
      <mt-template-page
        ref="templateRef3"
        class="frozenFistColumns"
        :template-config="componentConfig3"
        @siteCodeChangeRo="siteCodeChangeRo"
        @actionBegin="actionBegin2"
        @handleSearch="handleSearch"
        @handleClickToolBar="handleClickToolBar2"
      />
    </import-dialog>
    <!-- 选择转交人弹框 -->
    <transfer-select-dialog
      ref="transferSelectDialog"
      @confirm="transferSelectDialogConfirm"
    ></transfer-select-dialog>

    <BatchUpdateDialog ref="batchUpdateDialogRef" @confirm="batchUpdateConfirm" />
  </div>
</template>

<script>
import { formatTableColumnData, serializeList, formatImportColumnData } from './config/index'
import {
  EditSettings,
  JitTableColumnData,
  JitToolbar_bd,
  JitToolbar_kt,
  RequestType,
  JitTableColumnImportData,
  ActionType,
  NewRowData,
  Status,
  ComponentChangeType,
  DialogActionType
} from './config/constant'
import { rowDataTemp } from './config/variable'
import { cloneDeep } from 'lodash'
import { BASE_TENANT } from '@/utils/constant'
import MaterielSelectDialog from './components/materielSelectDialog'
import { download, getHeadersFileName } from '@/utils/utils'
import TransferSelectDialog from './components/transferSelectDialog'

import ImportDialog from '@/components/Upload/importDialog'
export default {
  components: {
    MaterielSelectDialog,
    TransferSelectDialog,
    ImportDialog,
    BatchUpdateDialog: () => import('./components/BatchUpdateDialog')
  },
  data() {
    const lastTabIndex = JSON.parse(localStorage.getItem('lastTabIndex'))
    const pageType = this.$route.query.type
    const ktType = this.$route.path.includes('-kt')
    return {
      beginData: null,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      pageType, // 页面类型
      saveUrl: `${BASE_TENANT}/buyerJitInfo/data/importKT`, // 导入API
      lastTabIndex, // 前一页面的 Tab index
      fromDataKey: 'excel', // 上传组件的 name，此值为调用 api 请求参数的 key
      downTemplateParams: {
        pageFlag: false
      }, // 导入下载模板参数
      uploadParams: {}, // 导入文件参数
      // 导入请求接口配置
      requestUrls: {
        templateUrlPre: 'deliverySchedule',
        // templateUrl: ktType ? "buyerJitInfoExportKt" : "buyerJitInfoExport", // 下载模板接口方法名
        // uploadUrl: ktType ? "buyerJitInfoImportKt" : "buyerJitInfoImport", // 上传接口方法名
        templateUrl: 'buyerJitInfoExportKt' // 下载模板接口方法名
        // uploadUrl: "buyerJitInfoImportKt", // 上传接口方法名
      },
      transferSelectRowIdList: [], // 转交行id列表
      templateConfig: [
        {
          activatedRefresh: false,

          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 不使用组件中的toolbar配置
          toolbar: ktType ? JitToolbar_kt : JitToolbar_bd,
          buttonQuantity: 8,
          gridId: this.$route.path.includes('kt')
            ? this.$tableUUID.deliverySchedule.purchaseJitKt.list
            : this.$tableUUID.deliverySchedule.purchaseJit.list,
          grid: {
            editSettings: EditSettings,
            allowPaging: true, // 分页
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [10, 20, 50, 100, 200],
              totalRecordsCount: 0
            },
            columnData: formatTableColumnData({
              data: JitTableColumnData
            }),
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/buyerJitInfo/query`,
              ignoreDefaultSearch: true,

              // defaultRules: [
              //   {
              //     field: "sourceFrom",
              //     operator: "equal",
              //     value: "KT",
              //   },
              // ],
              serializeList: serializeList
            }
            // frozenColumns: 1, // 行内编辑的表格不可以冻结列，使用 frozenFistColumns 实现
          }
        }
      ],
      addId: '1',

      componentConfig3: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [
            [
              {
                id: 'export',
                title: this.$t('导出')
              }
            ],
            []
          ],
          activatedRefresh: false,
          grid: {
            allowEditing: true, //开启表格编辑操作
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            allowPaging: false, // 分页

            columnData: formatImportColumnData({
              data: JitTableColumnImportData
            }),
            dataSource: []
            // asyncConfig: {
            //   url: `${BASE_TENANT}/buyerJitInfo/query`,
            //   // defaultRules: [
            //   //   {
            //   //     field: "sourceFrom",
            //   //     operator: "equal",
            //   //     value: "KT",
            //   //   },
            //   // ],
            //   serializeList: serializeList,
            // },
          }
        }
      ],
      isEditing: false, // 正在编辑数据
      dialogType: false,

      traceId: null
    }
  },
  mounted() {
    this.$bus.$on('siteCodeChangeClick', () => {
      this.dialogType = true
    })
    this.$bus.$on('itemCodeUpdateBudgetUnitPrice', (e) => {
      // console.log(e);
      this.materielSelectDialogConfirm(e)
    })
  },
  beforeDestroy() {
    localStorage.removeItem('lastTabIndex')
  },
  methods: {
    handleClickToolBar2(args) {
      const { toolbar } = args
      if (toolbar.id === 'export') {
        this.handleExport()
      }
    },
    handleExport() {
      let params = {}
      this.$store.commit('startLoading')
      this.$API.deliverySchedule.exportBuyerJitInfoErrorKtApi(params, this.traceId).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 导入
    importDialogImport() {
      this.handleCopy(this.componentConfig3[0].grid.dataSource, '1')
    },
    handleCopy(row) {
      row.forEach(() => {
        //   e.id = null;
        //   e.serialNumber = null;
        //   e.goodsDemandPlanItemId = null;
        //   e.sourceFrom = "KT";
        //   e.item = [
        //     {
        //       timeInfoTimestamp: Number(e.timeInfoTimestamp),
        //       total: 0,
        //       buyerNum: e.buyerNum,
        //       buyerRemark: e.buyerRemark, // 采购备注
        //       released: 0, //下达
        //       limitNum: 0, //限量数量           //需要给默认值
        //       remainingNum: 0, //剩余可创建数量        //需要给默认值
        //       outstandingNum: 0, // 未清订单数量
        //       jit: e.jit,
        //     },
        //   ];
      })
      console.log(row)
      this.$API.deliverySchedule.buyerJitInfoSaveBatch(row).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })

        this.$refs.importDialog.showStepSecond()
        this.$refs.importDialog.handleClose()
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    siteCodeChangeRo() {
      this.dialogType = true
    },
    // toolbar 按钮点击
    handleClickToolBar(args) {
      console.log(args)
      const { toolbar, gridRef } = args
      const selectedRecords = gridRef.getMtechGridRecords()
      const commonToolbar = [
        'JitAdd',
        'JitImport',
        'JitExport',
        'Filter',
        'Refresh',
        'refreshDataByLocal',
        'filterDataByLocal',
        'resetDataByLocal',
        'Setting'
      ]
      if (toolbar.id === 'closeEdit') {
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.refresh()
        // args.grid.closeEdit()
        // this.$refs.templateRef.refreshCurrentGridData()
      }
      if (this.isEditing && toolbar.id !== 'refreshDataByLocal') {
        // 结束编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }
      if (toolbar.id === 'materialConfiguration') {
        // 自动叫料配置
        this.handleMaterialConfiguration()
        return
      }

      if (selectedRecords.length == 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })

      if (toolbar.id === 'JitAdd') {
        // 新增
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id === 'JitDelete') {
        // 删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            this.handleJitDelete({ selectedRecords, idList })
          }
        })
      } else if (toolbar.id === 'JitPublish') {
        // 发布
        this.handleJitPublish({ selectedRecords, idList })
      } else if (toolbar.id === 'JitTransfer') {
        // 转交
        this.handleJitTransfer({ selectedRecords, idList })
      } else if (toolbar.id === 'JitMatchSupplier') {
        // 匹配供应商
        this.handleJitMatchSupplier({ selectedRecords, idList })
      } else if (toolbar.id === 'JitClose') {
        // 关闭
        this.handleJitClosed({ selectedRecords, idList })
      } else if (toolbar.id === 'JitImport') {
        // 导入
        // this.showUploadExcel(true);
        if (this.$route.name.includes('purchase-jit-kt')) {
          this.$refs.importDialog.init({
            title: this.$t('导入')
          })
          return
        }
        this.handleImport()
        return
      } else if (toolbar.id === 'JitExport') {
        // 导出
        this.buyerJitInfoExport()
      } else if (toolbar.id === 'batchUpdate') {
        // 批量修改
        this.handleBatchUpdate({ selectedRecords })
      }
    },
    handleBatchUpdate(args) {
      const { selectedRecords } = args
      const hasDiffElements = (arr, prop) => {
        for (var i = 0; i < arr.length - 1; i++) {
          for (var j = i + 1; j < arr.length; j++) {
            if (arr[i][prop] !== arr[j][prop]) {
              return true
            }
          }
        }
        return false
      }
      let canUpdate = selectedRecords.every(
        (item) =>
          item.status === Status.new ||
          item.status === Status.pending ||
          item.status === Status.abnormal ||
          item.status === Status.normal
      )
      let isNotSameStatus = hasDiffElements(selectedRecords, 'isJit')
      if (!canUpdate) {
        this.$toast({
          content: this.$t(
            '所选叫料计划中有【状态】为非‘新增’、‘待反馈’、‘反馈正常’、‘反馈异常’的叫料计划，不支持批量修改'
          ),
          type: 'warning'
        })
        return
      }
      if (isNotSameStatus) {
        this.$toast({
          content: this.$t('同一次处理，请选择【是否JIT】全部为‘是’或都全部为‘否’的叫料计划处理'),
          type: 'warning'
        })
        return
      }
      this.$refs.batchUpdateDialogRef.dialogInit({
        title: this.$t('批量修改交货日期及供应商'),
        selectedRecords
      })
    },
    batchUpdateConfirm(form) {
      this.$API.deliverySchedule.batchUpdateBuyerJitInfoApi(form).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: res.data, type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: res.msg, type: 'error' })
        }
      })
    },
    handleImport() {
      this.$dialog({
        modal: () => import(/* webpackChunkName: "*/ '@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('导入'),
          paramsKey: 'excel',
          importApi: this.$API.deliverySchedule.buyerJitInfoImportBD,
          downloadTemplateApi: this.$API.deliverySchedule.buyerJitInfoImportBDTemplate
        },
        success: () => {
          this.$refs.templateRef2.refreshCurrentGridData()
        }
      })
    },
    // 文件上传完成
    uploadCompleted(response) {
      this.$refs.importDialog.showStepSecond()
      response.data.forEach((item) => {
        item.addId = this.addId++
        item.deliveryDate = item.deliveryDateName
        // item.deliveryDate = Number(item.deliveryDateName);
        item.renewTime = Number(new Date(item.renewTime))

        item.deliveryDate = Number(new Date(item.deliveryDateName))
      })
      // this.$nextTick(() => {
      console.log(response)
      if (response.data.length > 0) {
        this.traceId = response.traceId
        this.componentConfig3[0].grid.dataSource = cloneDeep(response.data)
        this.$refs.templateRef3.refreshCurrentGridData()
      } else {
        this.$refs.importDialog.showStepSecond()
        this.$refs.importDialog.handleClose()
        this.$toast({
          content: this.$t('导入成功'),
          type: 'success'
        })
        // this.$refs.templateRef.refreshCurrentGridData()
      }
    },
    // CellTool
    handleClickCellTool(args) {
      const { tool, data } = args

      if (this.isEditing) {
        // 正在编辑时，若不是点击刷新按钮，结束编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }

      if (tool.id === 'JitPublish') {
        // 发布
        this.handleJitPublish({
          selectedRecords: [data],
          idList: [data.id]
        })
      } else if (tool.id === 'JitUnpublish') {
        // 取消发布
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认取消发布选中的数据？')
          },
          success: () => {
            this.handleJitCancelPublish({
              selectedRecords: [data],
              idList: [data.id]
            })
          }
        })
      } else if (tool.id === 'JitTransfer') {
        // 转交
        this.handleJitTransfer({
          selectedRecords: data,
          idList: [data.id]
        })
      } else if (tool.id === 'JitClosed') {
        // 关闭
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认关闭选中的数据？')
          },
          success: () => {
            this.handleJitClosed({
              selectedRecords: [data],
              idList: [data.id]
            })
          }
        })
      }
    },
    // 删除
    handleJitDelete(args) {
      const { selectedRecords, idList } = args
      let isValid = true
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        if (item.status != Status.new) {
          isValid = false
          this.$toast({
            content: this.$t('请选择新建状态的数据'),
            type: 'warning'
          })
          break
        }
      }
      if (isValid) {
        // 删除
        this.buyerJitInfoDelete({ idList })
      }
    },
    // 发布
    handleJitPublish(args) {
      const { selectedRecords, idList } = args
      let isValid = true
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        if (
          item.status != Status.new &&
          item.status != Status.transferred &&
          item.status != Status.abnormal
        ) {
          isValid = false
          this.$toast({
            content: this.$t('请选择新建、已转交或反馈异常状态的数据'),
            type: 'warning'
          })
          break
        } else if (!item.supplierCode) {
          isValid = false
          this.$toast({
            content: this.$t('请选择供应商不为空的数据'),
            type: 'warning'
          })
          break
        }
      }
      if (isValid) {
        // 发布
        this.buyerJitInfoPublish({ idList })
      }
    },
    // 取消发布
    handleJitCancelPublish(args) {
      const { selectedRecords, idList } = args
      let isValid = true
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        if (item.status != Status.pending) {
          isValid = false
          this.$toast({
            content: this.$t('请选择待反馈状态的数据'),
            type: 'warning'
          })
          break
        }
      }
      if (isValid) {
        // 取消发布
        this.buyerJitInfoCancelPublish({ idList })
      }
    },
    // 转交
    handleJitTransfer(args) {
      const { selectedRecords, idList } = args
      let isValid = true
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        if (
          item.status != Status.new &&
          item.status != Status.transferred &&
          item.status != Status.abnormal
        ) {
          isValid = false
          this.$toast({
            content: this.$t('请选择新建、已转交或反馈异常状态的数据'),
            type: 'warning'
          })
          break
        }
      }
      if (isValid) {
        this.$refs.transferSelectDialog.dialogInit({
          title: this.$t('选择转交人'),
          actionType: DialogActionType.Add
        })
        this.transferSelectRowIdList = idList // 将转交行id存起来
      }
    },
    // 选择 转交人 弹框 确定 数据校验通过时
    transferSelectDialogConfirm(formData) {
      const params = {
        idList: this.transferSelectRowIdList,
        transferPlanId: formData.transferPlanId,
        transferPlanCode: formData.transferPlanCode,
        transferPlanName: formData.transferPlanName
      }
      // 转交
      this.buyerJitInfoChange(params)
    },
    // 匹配供应商
    handleJitMatchSupplier(args) {
      const { selectedRecords, idList } = args
      let isValid = true
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        if (
          item.status != Status.new &&
          item.status != Status.transferred &&
          item.status != Status.abnormal
        ) {
          isValid = false
          this.$toast({
            content: this.$t('请选择新建、已转交状态或反馈异常状态的数据'),
            type: 'warning'
          })
          break
        }
      }
      if (isValid) {
        // 采方jit-匹配供应商
        this.buyerJitInfoMatchSupplier(idList)
      }
    },
    // 自动叫料配置
    handleMaterialConfiguration() {
      if (location.href.includes('eads')) {
        window.open('http://bitest.tclking.com/tcloms/pc/collectv2/TableDataImport/ME7487')
      } else {
        window.open('https://data.tcl.com/tcloms/pc/collectv2/TableDataImport/ME7487')
      }
    },
    // 关闭
    handleJitClosed(args) {
      const { selectedRecords, idList } = args
      let isValid = true
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        if (
          item.status != Status.pending &&
          item.status != Status.abnormal &&
          item.status != Status.normal
        ) {
          isValid = false
          this.$toast({
            content: this.$t('请选择待反馈、反馈异常或反馈正常状态的数据'),
            type: 'warning'
          })
          break
        }
      }
      if (isValid) {
        // 关闭
        this.buyerJitInfoClose({ idList })
      }
    },
    // actionBegin 表格编辑生命周期
    actionBegin(args) {
      const { requestType, action, rowData } = args
      // console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args);
      if (args.requestType === 'save') {
        if (args.data.itemCode === null && this.beginData !== null) {
          args.data.itemCode = this.beginData.itemCode
        }
      }
      if (requestType === RequestType.add) {
        // 开始行添加
        this.isEditing = true
        // 即将添加一行，赋值新增行的初始数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
        let newRowData = cloneDeep(NewRowData)
        // 调度员名称 新增时-自动填入当前登录人
        newRowData = {
          ...newRowData,
          scheduleUserName: userInfo.employeeName, // 调度员名称
          scheduleUserCode: userInfo.employeeCode, // 调度员编码
          scheduleUserId: userInfo.employeeId // 调度员ID
        }
        rowDataTemp.push(newRowData)
        args.rowData = newRowData
        args.data = newRowData
      } else if (requestType === RequestType.save && action === ActionType.add) {
        // 即将保存新增时，将新增中编辑的数据付给组件
        args.data = { ...args.data, ...rowDataTemp[rowDataTemp.length - 1] }
        args.rowData = { ...args.rowData, ...rowDataTemp[rowDataTemp.length - 1] }
      } else if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === RequestType.beginEdit) {
        this.beginData = args.rowData

        if (
          rowData.status !== Status.new &&
          rowData.status !== Status.transferred &&
          rowData.status !== Status.abnormal &&
          rowData.status !== Status.normal
        ) {
          // 数据不可编辑 状态 != 新建 && 已转交 && 反馈异常
          args.cancel = true
          this.$toast({
            content: this.$t('仅新建、已转交或反馈异常状态的数据可编辑'),
            type: 'warning'
          })
          return
        }
        // 开始行编辑
        this.isEditing = true
        // 即将编辑行，赋值当前行的数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },
    // actionComplete 表格编辑生命周期

    actionBegin2(args) {
      const { requestType, action, rowData, rowIndex } = args
      // console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args);
      if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        console.log(args, 'actionbegin2')
        console.log(rowDataTemp)
        args.data = rowData

        this.componentConfig3[0].grid.dataSource[rowIndex] = args.data
      } else if (requestType === RequestType.beginEdit) {
        // 开始行编辑
        this.isEditing = true
        // 即将编辑行，赋值当前行的数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },
    actionComplete(args) {
      const { requestType, action, rowIndex, index } = args
      const rowData = rowDataTemp[rowDataTemp.length - 1]
      // console.log(
      //   `Complete,\nrequest: ${requestType}\naction: ${action}`,
      //   args
      // );
      if (requestType === RequestType.save && action == ActionType.edit) {
        // 完成编辑行
        this.isEditing = false
        // rowData, rowIndex
        if (!this.isValidSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API
          rowData.demandCode = args.data.demandCode // 净需求编号是使用自带的editor做的编辑，所以额外加进row中
          this.buyerJitInfoSave({ rowData, rowIndex })
        }
      } else if (requestType === RequestType.save && action == ActionType.add) {
        // 完成新增行
        this.isEditing = false
        // rowData, index
        if (!this.isValidSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API
          rowData.demandCode = args.data.demandCode // 净需求编号是使用自带的editor做的编辑，所以额外加进row中
          this.buyerJitInfoSave({ rowData, rowIndex: index })
        }
      } else if (requestType === RequestType.sorting || requestType === RequestType.refresh) {
        // 丢弃编辑中状态
        this.isEditing = false
      }
    },
    // 校验数据
    // 工作中心workCenterCode有值时，项目文本批次projectTextBatch 必填
    isValidSaveData(data) {
      const {
        // deliveryMethod, // 配送方式 必填
        // outsourcedType, // 委外方式 必填
        deliveryDate, // 交货日期-时间 必填
        itemCode, // 物料编码 必填
        bidNum,
        siteCode, // 工厂编码 必填
        warehouseCode, // 库存地点编码 必填
        // senderAddress, // 送货地址 必填
        // senderName, // 送货联系人 必填
        // senderPhone, // 送货联系电话 必填
        supplierCode,
        subSiteCode,
        subSiteAddressCode,
        buyerOrgCode,
        workOrder,
        // 收货信息
        workCenterCode, // 工作中心
        // processorCode, // 加工商编码 配送方式为 直送 时 必填
        projectTextBatch, // 工作中心 有值时，项目文本批次 必填
        isItemBatch
        // isJit
      } = data
      // console.log("=========================", outsourcedType);
      let valid = true
      // if (deliveryMethod === null || deliveryMethod === undefined) {
      //   this.$toast({
      //     content: this.$t("请选择配送方式"),
      //     type: "warning",
      //   });
      //   valid = false;
      // } else if (outsourcedType === null || outsourcedType === undefined) {
      //   this.$toast({
      //     content: this.$t("请选择委外方式"),
      //     type: "warning",
      //   });
      //   valid = false;
      // } else
      if (!deliveryDate) {
        this.$toast({
          content: this.$t('请选择交货日期'),
          type: 'warning'
        })
        valid = false
      } else if (!itemCode) {
        this.$toast({
          content: this.$t('请选择物料'),
          type: 'warning'
        })
        valid = false
      } else if (!bidNum) {
        this.$toast({
          content: this.$t('请输入需求数量'),
          type: 'warning'
        })
        valid = false
      } else if (!supplierCode) {
        this.$toast({
          content: this.$t('请输入供应商'),
          type: 'warning'
        })
        valid = false
      } else if (!buyerOrgCode) {
        this.$toast({
          content: this.$t('请选择采购组'),
          type: 'warning'
        })
        valid = false
      } else if (!siteCode) {
        this.$toast({
          content: this.$t('请选择工厂'),
          type: 'warning'
        })
        valid = false
      } else if (!warehouseCode) {
        this.$toast({
          content: this.$t('请选择库存地点'),
          type: 'warning'
        })
        valid = false
      } else if (!subSiteCode) {
        this.$toast({
          content: this.$t('请选择分厂'),
          type: 'warning'
        })
        valid = false
      } else if (!subSiteAddressCode) {
        this.$toast({
          content: this.$t('请选择分厂库存地点'),
          type: 'warning'
        })
        valid = false
      } else if ([1, '1'].includes(isItemBatch) && !workOrder) {
        this.$toast({
          content: this.$t('是否批次来料为 "是" 时, 生产工单必填'),
          type: 'warning'
        })
        valid = false
      }
      // else if (!senderAddress && !senderName && !senderPhone) {
      //   // 收货信息
      //   this.$toast({
      //     content: this.$t("请选择收货信息"),
      //     type: "warning",
      //   });
      //   valid = false;
      //   // } else if (!processorCode && deliveryMethod == DeliveryMethod.direct) {
      //   //   // 加工商编码 配送方式为 直送 时 必填
      //   //   this.$toast({
      //   //     content: this.$t("配送方式为直送时，必须选择加工商"),
      //   //     type: "warning",
      //   //   });
      //   //   valid = false;
      // }
      else if (workCenterCode && !projectTextBatch) {
        // 工作中心 有值时，项目文本批次 必填
        this.$toast({
          content: this.$t('工作中心有值时，项目文本批次必填'),
          type: 'warning'
        })
        valid = false
      }

      return valid
    },
    // 表格数据绑定完成
    handleDataBound() {},
    // 采方jit-保存jit
    buyerJitInfoSave(args) {
      const { rowData, rowIndex } = args
      const params = {
        ...rowData,
        thePrimaryKey: undefined
      }
      this.apiStartLoading()
      this.$API.deliverySchedule
        .buyerJitInfoSave(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    // 采方jit-采方删除jit
    buyerJitInfoDelete(params) {
      this.apiStartLoading()
      this.$API.deliverySchedule
        .buyerJitInfoDelete(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方jit-采方发布jit
    buyerJitInfoPublish(params) {
      this.apiStartLoading()
      this.$API.deliverySchedule
        .buyerJitInfoPublish(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方jit-采方取消发布jit
    buyerJitInfoCancelPublish(params) {
      this.apiStartLoading()
      this.$API.deliverySchedule
        .buyerJitInfoCancelPublish(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方jit-jit转交
    buyerJitInfoChange(params) {
      this.apiStartLoading()
      this.$API.deliverySchedule
        .buyerJitInfoChange(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方jit-采方关闭jit
    buyerJitInfoClose(params) {
      this.apiStartLoading()
      this.$API.deliverySchedule
        .buyerJitInfoClose(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方jit-匹配供应商
    buyerJitInfoMatchSupplier(params) {
      this.apiStartLoading()
      this.$API.deliverySchedule
        .buyerJitInfoMatchSupplier(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方jit-采方jitExcel导出
    buyerJitInfoExport() {
      let obj = JSON.parse(sessionStorage.getItem(this.templateConfig[0].gridId))?.visibleCols
      let field = []
      if (obj !== undefined && obj.length) {
        obj.forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      } else {
        JitTableColumnData.forEach((item) => {
          if (item.fieldCode) {
            field.push(item.fieldCode)
          }
        })
      }
      // 4452 tone
      setTimeout(() => {
        field.forEach((item, index) => {
          // if (item === 'itemCode') {
          //   field.splice(index, 1, 'itemName')
          //   // field.push("itemName");
          // }
          if (item === 'siteCode') {
            field.splice(index, 1, 'siteName')
            // field.push("siteName");
          }
          if (item === 'supplierCode') {
            field.splice(index, 1, 'supplierName')
            // field.push("supplierName");
          }
          if (item === 'companyCode') {
            field.splice(index, 1, 'companyName')
            // field.push("companyName");
          }
          if (item === 'buyerOrgCode') {
            field.splice(index, 1, 'buyerOrgName')
            // field.push("buyerOrgName");
          }
        })
        const queryBuilderRules =
          this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        const params = {
          page: { current: 1, size: 10000 },
          ...queryBuilderRules,
          rules: [].concat(queryBuilderRules.rules || [])
        } // 筛选条件
        this.apiStartLoading()
        this.$API.deliverySchedule.buyerJitInfoExportNewKt(params, field).then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
      }, 200)
    },
    // 显示隐藏上传弹框
    // showUploadExcel(flag) {
    //   if (flag) {
    //     this.$refs.uploadExcelRef.uploadData = null; // 清空数据
    //     this.$refs.uploadExcelRef.fileLength = 0;
    //     this.$refs.uploadExcelRef.$refs.uploader.files = [];
    //     this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show();
    //   } else {
    //     this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide();
    //   }
    // },
    // 上传成功后
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 行编辑 点击搜索按钮
    handleSearch(args) {
      const { data, dataKey } = args
      if (dataKey === 'itemCode') {
        if (this.dialogType === true) {
          this.$refs.materielSelectDialog.dialogInit({
            title: this.$t('选择物料'),
            data
          })
        } else {
          this.$toast({ content: this.$t('请先选择工厂'), type: 'warning' })
        }
        // this.$bus.$on("siteCodeChangeClick", (e) => {
        // 物料 选择物料弹框

        // return;
        // });
        // this.$toast({ content: this.$t("请先选择工厂"), type: "warning" });
      }
    },
    // 选择 物料 弹框点击确认
    materielSelectDialogConfirm(args) {
      const { data } = args
      // this.$bus.$on("siteCodeChangeClick", (e) => {

      // }
      this.$API.masterData
        .getBasicByFacItem({
          organizationId: data[0].organizationId,
          itemCode: data[0].itemCode
        })
        .then((res) => {
          this.$bus.$emit('purchaseGroupCodeChange', {
            purchaseGroupCode: res.data.purchasingInfo.purchaseGroupCode,
            purchaseGroupId: res.data.purchasingInfo.purchaseGroupId,
            purchaseGroupName: res.data.purchasingInfo.purchaseGroupName
          })
        })
      if (data?.length > 0) {
        this.$bus.$emit('purchaseJitColumnChange', {
          requestKey: 'itemCode', // 物料编码
          changeType: ComponentChangeType.code,
          data: {
            itemCode: data[0].itemCode, // 物料编码
            itemName: data[0].itemName, // 物料名称
            itemId: data[0].id // 物料id
          },
          modifiedKeys: [
            'itemCode', // 物料编码
            'itemName', // 物料名称
            'itemId' // 物料id
          ]
        })
        this.$bus.$emit('purchaseJitColumnChange', {
          requestKey: 'itemCode', // 物料编码
          changeType: ComponentChangeType.link,
          data: {
            itemCode: data[0].itemCode, // 物料编码
            itemName: data[0].itemName, // 物料名称
            itemId: data[0].id // 物料id
          },
          modifiedKeys: [
            'siteCode', // 工厂编码
            'planGroupCode' // 计划组编码
          ]
        })
      }
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
// 行内 cellTool
/deep/ .column-tool {
  margin-top: 8px;

  .template-svg {
    cursor: pointer;
    font-size: 12px;
    color: #6386c1;

    &:nth-child(n + 2) {
      margin-left: 10px;
    }
  }
}

// 行编辑时包含按钮的项目
/deep/ .input-search-content {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .mt-input {
    flex: 1;
  }

  .mutliselect-container {
    width: 100%;
  }
  // 超过宽度显示省略号
  .text-ellipsis {
    width: 125px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .mt-icons {
    width: 20px;
    flex-shrink: 0;
    text-align: center;
    cursor: pointer;
  }
}

/deep/ .grid-edit-column {
  padding: 12px 0;
}

.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  // padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
  }
}
</style>
