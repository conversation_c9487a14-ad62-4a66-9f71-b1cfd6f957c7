<!-- 批量修改弹框 -->
<template>
  <mt-dialog
    ref="dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    width="650px"
    height="450px"
  >
    <div style="padding-top: 1rem">
      <mt-form ref="modelForm" :model="modelForm" :rules="rules">
        <div v-if="isJit">
          <mt-row :gutter="24">
            <mt-col :span="24">
              <mt-form-item prop="changeMethod" :label="$t('修改方式')">
                <mt-radio v-model="modelForm.changeMethod" :data-source="changeMethodOptions" />
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row :gutter="24" v-if="modelForm.changeMethod === '0'">
            <mt-col :span="12">
              <mt-form-item prop="deliveryDate" :label="$t('交货日期')">
                <mt-date-time-picker
                  v-model="modelForm.deliveryDate"
                  :open-on-focus="true"
                  :allow-edit="false"
                  :time-stamp="true"
                  :min="new Date()"
                  :placeholder="$t('请选择')"
                />
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row :gutter="24" v-if="modelForm.changeMethod === '1'">
            <mt-col :span="24">
              <mt-form-item prop="changeType" :label="$t('变动类型')">
                <mt-radio v-model="modelForm.changeType" :data-source="changeTypeOptions" />
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row :gutter="24" v-if="modelForm.changeMethod === '1'">
            <mt-col :span="12">
              <mt-form-item prop="changeHours" :label="$t('变动时长（小时）')">
                <mt-input-number
                  v-model="modelForm.changeHours"
                  :show-clear-button="false"
                  :min="1"
                  :max="12"
                />
              </mt-form-item>
            </mt-col>
          </mt-row>
        </div>
        <mt-row :gutter="24" v-else>
          <mt-col :span="12">
            <mt-form-item prop="deliveryDate" :label="$t('交货日期')">
              <mt-date-picker
                v-model="modelForm.deliveryDate"
                :open-on-focus="true"
                :allow-edit="false"
                :time-stamp="true"
                :min="new Date()"
                :placeholder="$t('请选择')"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item prop="supplierCode" :label="$t('供应商')">
              <RemoteAutocomplete
                v-model="modelForm.supplierCode"
                url="/masterDataManagement/tenant/supplier/paged-query"
                :placeholder="$t('请选择')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      modelForm: {},
      rules: {
        // changeMethod: [{ required: true, message: this.$t('请选择修改方式'), trigger: 'blur' }]
        // deliveryDate: [{ required: true, message: this.$t('请选择交货日期'), trigger: 'blur' }],
        // changeType: [{ required: true, message: this.$t('请选择变动类型'), trigger: 'blur' }],
        // changeHours: [{ required: true, message: this.$t('请输入变动时长'), trigger: 'blur' }]
      },
      isJit: false,
      ids: [],

      changeMethodOptions: [
        { label: this.$t('具体时间'), value: '0' },
        { label: this.$t('平移小时数'), value: '1' }
      ],
      changeTypeOptions: [
        { label: this.$t('提前'), value: '0' },
        { label: this.$t('推后'), value: '1' }
      ]
    }
  },
  methods: {
    dialogInit(args) {
      const { title, selectedRecords } = args
      this.dialogTitle = title
      this.modelForm = {
        changeType: '1'
      }
      this.isJit = selectedRecords[0]?.isJit ? true : false
      this.ids = []
      selectedRecords.forEach((item) => {
        this.ids.push(item.id)
      })
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    confirm() {
      let params = {
        ids: this.ids,
        ...this.modelForm
      }
      this.$refs.modelForm.validate((valid) => {
        if (valid) {
          if (params?.deliveryDate || params?.changeHours || params?.supplierCode) {
            this.$emit('confirm', params)
            this.handleClose()
          } else {
            this.$toast({ content: this.$t('请选择需要修改的交货日期或供应商'), type: 'warning' })
          }
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>
