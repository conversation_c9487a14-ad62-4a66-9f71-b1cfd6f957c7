<template>
  <!-- 选择 物料 -->
  <mt-dialog
    ref="dialog"
    css-class="pc-item-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <div class="full-height">
      <mt-template-page
        ref="templateRef"
        v-if="isTrue"
        :template-config="componentConfig"
        :hidden-tabs="true"
        @recordDoubleClick="recordDoubleClick"
      />
    </div>
  </mt-dialog>
</template>

<script>
import { materielTableColumnData } from '../config/index.js'
import { MaterielTableColumnData } from '../config/constant'
import { PROXY_MDM_TENANT } from '@/utils/constant'

export default {
  data() {
    return {
      dialogTitle: '',
      apiWaitingQuantity: 0, // 调用的api正在等待数
      organizationCode: '',
      isTrue: false,
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          gridId: 'F683E3E3-4677-ED09-4019-D91DE6F617C5',
          grid: {
            allowPaging: true, // 分页
            // lineSelection: 0, // 选项列
            // lineIndex: 1, // 序号列
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: materielTableColumnData({
              data: MaterielTableColumnData
            }),
            asyncConfig: {
              // 供方送货司机信息维护-获取司机列表
              url: `${PROXY_MDM_TENANT}/item/paged-query?BU_CODE=${localStorage.getItem(
                'currentBu'
              )}`,
              defaultRules: [
                {
                  field: 'organizationCode',
                  operator: 'equal',
                  value: this.organizationCode
                },
                {
                  field: 'jitFlag',
                  operator: 'equal',
                  value: 1
                }
              ]
            },
            dataSource: []
            // frozenColumns: 1,
          }
        }
      ]
    }
  },
  mounted() {
    // this.$bus.$on("siteCodeChangeClick", (e) => {});
    this.$bus.$on('siteCodeChangeClick', (e) => {
      console.log(e)
      this.organizationCode = e.organizationCode

      this.componentConfig[0].grid.asyncConfig.defaultRules = [
        {
          field: 'organizationCode',
          operator: 'equal',
          value: this.organizationCode
        },
        {
          field: 'jitFlag',
          operator: 'equal',
          value: 1
        }
      ]
    })
  },

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      // setTimeout(() => {
      // if (this.isTrue === true) {
      const { title } = entryInfo
      this.dialogTitle = title // 弹框名称
      // this.refreshColumns();
      this.$refs.dialog.ejsRef.show()
      setTimeout(() => {
        this.isTrue = true
      }, 100)
    },
    onOpen(args) {
      args.preventFocus = true
    },

    confirm() {
      const selectedRowData = this.$refs.templateRef
        .getCurrentUsefulRef()
        .gridRef.getMtechGridRecords()
      this.$bus.$emit('itemCodeConfirm', {
        data: { data: [...selectedRowData] }
      })
      this.$emit('confirm', { data: [...selectedRowData] })
      this.$bus.$emit('itemCodeUpdateBudgetUnitPrice', {
        data: [...selectedRowData]
      })
      this.handleClose()
    },
    // 双击物料行，也进行提交
    recordDoubleClick(args) {
      const { rowData } = args
      // const selectedRowData = this.$refs.templateRef
      //   .getCurrentUsefulRef()
      //   .gridRef.getMtechGridRecords();

      this.$emit('confirm', { data: [rowData] })
      this.$bus.$emit('itemCodeUpdateBudgetUnitPrice', {
        data: [rowData]
      })
      this.$bus.$emit('itemCodeConfirm', {
        data: { data: rowData }
      })
      this.handleClose()
    },

    handleClose() {
      this.isTrue = false
      this.$refs.dialog.ejsRef.hide()
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-data-grid {
  height: 100%;
  > .e-grid {
    height: auto;
    // display: flex;

    > .e-gridcontent {
      flex: 1;
      overflow: auto;
    }
  }

  .e-rowcell.e-active {
    background: #e0e0e0 !important;
  }
}
</style>
