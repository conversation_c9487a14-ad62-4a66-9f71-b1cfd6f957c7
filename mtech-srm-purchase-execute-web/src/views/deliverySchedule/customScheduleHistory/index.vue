<template>
  <div class="full-height">
    <div class="con">
      <mt-select
        width="300"
        allow-filtering="true"
        v-model="selectVersion"
        :data-source="versionOptions"
        :placeholder="$t('请选择版本')"
      ></mt-select>
    </div>
    <div class="con2">
      <page2 :version="selectVersion" :is-detail="'1'"></page2>
    </div>
  </div>
</template>
<script>
export default {
  components: {
    page2: () => import('../customSchedule/page/page2')
  },
  data() {
    return {
      selectVersion: null,
      versionOptions: []
    }
  },
  async mounted() {
    await this.getversionOptions()
  },
  methods: {
    async getversionOptions() {
      await this.$API.deliverySchedule.supplierGoodsDemandPlanInfogetVersions().then((res) => {
        let list = res.data || []
        let list1 = []
        list.forEach((item) => {
          list1.push('V' + item)
        })
        this.versionOptions = list1
        if (this.versionOptions.length) {
          this.selectVersion = this.versionOptions[0]
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.con {
  padding: 10px 20px;
}
.con2 {
  height: calc(100% - 40px);
}
</style>
