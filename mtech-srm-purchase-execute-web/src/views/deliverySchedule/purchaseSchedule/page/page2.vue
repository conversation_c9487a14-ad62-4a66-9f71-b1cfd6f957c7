<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef2"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
      @selectedChanged="selectedChanged"
    ></mt-template-page>
    <import-dialog
      :accept="['.xls', '.xlsx']"
      :from-data-key="fromDataKey"
      :save-url="saveUrl"
      :request-urls="requestUrls"
      ref="importDialog"
      @import="importDialogImport"
      @uploadCompleted="uploadCompleted"
    >
      <predict-import-dialog-slot
        ref="predictImportDialogSlot"
        @submitted="predictImportDialogSlotSubmitted"
      ></predict-import-dialog-slot>
    </import-dialog>
  </div>
</template>
<script>
import { checkColumn2, lastColumn2 } from '../config/index2'
import { BASE_TENANT } from '@/utils/constant'
import { cloneDeep } from 'lodash'
import * as UTILS from '@/utils/utils'
export default {
  props: {
    isDetail: {
      type: String,
      default: '2'
    },
    version: {
      type: String,
      default: ''
    }
  },
  components: {
    ImportDialog: () => import('@/components/Upload/importDialog'),
    PredictImportDialogSlot: () => import('../components2/predict-import-dialog-slot.vue')
  },
  data() {
    return {
      // 通知配置导入请求接口配置
      requestUrls: {
        templateUrlPre: 'deliverySchedule',
        templateUrl: 'buyerGoodsDemandPlanInfoImportBDTemplate', // 下载模板接口方法名
        uploadUrl: 'buyerGoodsDemandPlanInfoImportBD' // 上传接口方法名
      },
      saveUrl: `${BASE_TENANT}/buyerGoodsDemandPlanInfo/importBD`, // 导入API
      fromDataKey: 'excel', // 上传组件的 name，此值为调用 api 请求参数的 key
      lastColumn2: lastColumn2,
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          buttonQuantity: 7,
          toolbar: [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              title: this.$t('新增'),
              permission: ['O_02_0444']
            },
            {
              id: 'closeEdit',
              icon: 'icon_table_delete',
              // permission: ["O_02_1141"],
              title: this.$t('取消编辑')
            },
            {
              id: 'Delete',
              icon: 'icon_solid_Delete',
              title: this.$t('删除'),
              permission: ['O_02_0445']
            },
            {
              id: 'Publish',
              icon: 'icon_solid_Activateorder',
              title: this.$t('发布'),
              permission: ['O_02_0446']
            },
            {
              id: 'Cancel',
              icon: 'icon_solid_Activateorder',
              title: this.$t('取消发布')
            },
            {
              id: 'Close',
              icon: 'icon_solid_pushorder',
              title: this.$t('关闭')
            },
            {
              id: 'Import',
              icon: 'icon_solid_Import',
              title: this.$t('导入'),
              permission: ['O_02_0449']
            },
            {
              id: 'Export_JIT',
              icon: 'icon_solid_export',
              title: this.$t('JIT导出'),
              permission: ['O_02_1400']
            },
            {
              id: 'TemplateExport',
              icon: 'icon_solid_export',
              title: this.$t('模板导出'),
              permission: ['O_02_1399']
            },
            {
              id: 'Export1',
              icon: 'icon_solid_export',
              title: this.$t('导出'),
              permission: ['O_02_0450']
            },
            {
              id: 'Release',
              icon: 'icon_solid_Pauseorder',
              title: this.$t('下达'),
              permission: ['O_02_0447']
            },
            {
              id: 'Confirm',
              icon: 'icon_solid_Pauseorder',
              title: this.$t('确认'),
              permission: ['O_02_0448']
            }
          ],
          gridId: this.$tableUUID.purchaseSchedule.purchaseScheduleListTabTab,
          grid: {
            // height: "600",
            // frozenColumns: 1,
            allowEditing: true, //开启表格编辑操作
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            columnData: checkColumn2.concat(lastColumn2),
            asyncConfig: {}
          }
        }
      ],
      currentList: [], //从接口获取到的最新的数据
      nowEditRowFlag: '', //当前编辑的行id
      addId: '1',
      selectedOtherInfo: {}
    }
  },
  watch: {
    isDetail: {
      handler(val) {
        if (val === '1') {
          this.componentConfig[0].toolbar = []
        }
      },
      immediate: true
    },
    version: {
      handler(val) {
        if (!val) return
        if (this.isDetail === '1') {
          this.componentConfig[0].toolbar = []
          lastColumn2.some((item) => {
            if (item.field === 'status') {
              item.cellTools = []
            }
          })
          this.componentConfig[0].grid.columnData = checkColumn2.concat(lastColumn2)
          this.$set(this.componentConfig[0].grid, 'asyncConfig', {
            url: `${BASE_TENANT}/buyerGoodsDemandPlanInfo/queryByVersion`,
            defaultRules: [
              {
                field: 'version',
                operator: 'equal',
                value: this.version.slice(1)
              }
            ],
            serializeList: (list) => {
              list.forEach((item) => {
                item.addId = this.addId++
                item.self48 = item.total
                item.self49 = item.buyerNum
                item.self50 = item.supplierNum
                item.self51 = item.gapNum
                if (item.timeInfoTimestamp && item.timeInfoTimestamp.length === 13) {
                  item.timeInfoTimestamp = new Date(Number(item.timeInfoTimestamp))
                } else {
                  item.timeInfoTimestamp = null
                }
                item.isEntry = '1' //是否是带入的数据
              })
              this.currentList = list
              return list
            }
          })
        }
        if (this.isDetail === '2') {
          this.$set(this.componentConfig[0].grid, 'asyncConfig', {
            url: `${BASE_TENANT}/buyerGoodsDemandPlanInfo/query/full`,
            serializeList: (list) => {
              list.forEach((item) => {
                item.addId = this.addId++
                item.self48 = item.total
                item.self49 = item.buyerNum
                item.self50 = item.supplierNum
                item.self51 = item.gapNum
                if (item.timeInfoTimestamp && item.timeInfoTimestamp.length === 13) {
                  item.timeInfoTimestamp = new Date(Number(item.timeInfoTimestamp))
                } else {
                  item.timeInfoTimestamp = null
                }
                item.isEntry = '1' //是否是带入的数据
              })
              this.currentList = list
              return list
            }
          })
        }
      },
      immediate: true
    }
  },
  mounted() {},
  methods: {
    getToday() {
      var today = new Date()
      today.setHours(0)
      today.setMinutes(0)
      today.setSeconds(0)
      today.setMilliseconds(0)
      today = new Date(today.getTime() + 24 * 60 * 60 * 1000)
      return today
    },
    // 文件上传完成
    uploadCompleted(response) {
      this.$refs.importDialog.showStepSecond()
      this.$nextTick(() => {
        this.$refs.predictImportDialogSlot.init({
          resData: response?.data || []
        })
      })
    },
    // 预测数据导入编辑弹框 点击导入
    importDialogImport() {
      this.$refs.predictImportDialogSlot.doImport()
    },
    // 导入提交
    predictImportDialogSlotSubmitted() {
      this.$refs.importDialog.handleClose()
      this.updateList()
    },
    handleClose(row) {
      //关闭
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认关闭选中的排期列表吗?')
        },
        success: () => {
          let ids = row.map((item) => item.goodsDemandPlanItemId)
          let params = ids
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoClose(params).then(() => {
            this.$toast({
              content: this.$t('关闭排期列表操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleCancle(row) {
      //取消发布
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认取消发布选中的排期列表吗?')
        },
        success: () => {
          let ids = row.map((item) => item.goodsDemandPlanItemId)
          let params = ids
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoCancel(params).then(() => {
            this.$toast({
              content: this.$t('取消发布排期列表操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handlePublish(row) {
      //发布
      let flag = true
      row.forEach((item) => {
        if (!item.addressId) {
          flag = false
        }
      })
      if (!flag) {
        this.$toast({
          content: this.$t('送货联系人电话地址填写才可以发布'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认发布选中的排期列表吗?')
        },
        success: () => {
          let ids = row.map((item) => item.goodsDemandPlanItemId)
          let params = ids
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoPublish(params).then(() => {
            this.$toast({
              content: this.$t('发布排期列表操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleAdd() {
      //新增
      this.$refs.templateRef2.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
    },
    // handleImport() {
    //   //导入
    //   // this.$refs.importDialog.init({
    //   //   title: this.$t('导入')
    //   // })
    // },
    handleImport() {
      this.$dialog({
        modal: () => import(/* webpackChunkName: "*/ '@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('导入'),
          paramsKey: 'excel',
          importApi: this.$API.deliverySchedule.buyerGoodsDemandPlanInfoImportBD,
          downloadTemplateApi: this.$API.deliverySchedule.buyerGoodsDemandPlanInfoImportBDTemplate
        },
        success: () => {
          this.$refs.templateRef2.refreshCurrentGridData()
        }
      })
    },
    handleExport() {
      //导出supply-plan-supplier
      let rule = this.$refs.templateRef2.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 1000 },
        pageFlag: true,
        rules: rule.rules || []
      }
      this.$store.commit('startLoading')
      this.$API.deliverySchedule.buyerGoodsDemandPlanInfoExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleExportJIT() {
      const rule = this.$refs.templateRef2.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        condition: rule.condition || '',
        page: { current: 1, size: 1000 },
        pageFlag: true,
        rules: rule.rules || []
      }
      this.$store.commit('startLoading')
      this.$API.deliverySchedule.buyerGoodsDemandPlanInfoExportJIT(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleTemplateExport() {
      const rule = this.$refs.templateRef2.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        condition: rule.condition || '',
        page: { current: 1, size: 1000 },
        pageFlag: true,
        rules: rule.rules || []
      }
      this.$store.commit('startLoading')
      this.$API.deliverySchedule.buyerGoodsDemandPlanInfoTemplateExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleRelease(row) {
      //下达
      let hasOne = row.some((item) => {
        return item.released === '1'
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择未下达的行操作!'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认下达选中的排期列表吗?')
        },
        success: () => {
          let ids = row.map((item) => item.goodsDemandPlanItemId)
          let params = ids
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoReleased(params).then(() => {
            this.$toast({
              content: this.$t('确认下达排期列表操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleConfirm(row) {
      //确认排期列表
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认选中的排期列表吗?')
        },
        success: () => {
          let ids = row.map((item) => item.goodsDemandPlanItemId)
          let params = ids
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoConfirm(params).then(() => {
            this.$toast({
              content: this.$t('确认排期列表操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    generateUUID() {
      var d = new Date().getTime()
      if (window.performance && typeof window.performance.now === 'function') {
        d += performance.now() //use high-precision timer if available
      }
      var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (d + Math.random() * 16) % 16 | 0
        d = Math.floor(d / 16)
        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16)
      })
      return uuid
    },
    judgeValid1(params) {
      //添加校验
      let flag = true
      if (params.buyerNum === '') {
        this.$toast({
          content: this.$t('P（需求量）必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.timeInfoTimestamp) {
        this.$toast({
          content: this.$t('需求日期必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.deliveryMethod) {
        this.$toast({
          content: this.$t('配送方式必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (params.deliveryMethod === '0') {
        //配送方式为直送
        if (!params.processorCode || !params.processorName) {
          this.$toast({
            content: this.$t('配送方式为直送时,加工商编码和名称必填'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      if (params.outsourcedType === '2') {
        //委外方式非委外
        if (params.deliveryMethod === '0') {
          this.$toast({
            content: this.$t('委外方式为非委外时,配送方式需为非直送'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      return flag
    },
    validateEdit(row) {
      const params = {
        ...row,
        timeInfoTimestamp: row.timeInfoTimestamp?.getTime() || 0,
        jit: row.scheduleType === 'JIT' ? 1 : 0,
        buyerNum: row.self49 // p字段
      }
      const flag = this.judgeValid1(params)
      return flag
    },
    getSupplier(val, row, index) {
      //查询供应商的数据
      let str = val
      let params = {
        fuzzyNameOrCode: str || ''
      }
      this.$API.masterData.getSupplier(params).then((res) => {
        const list = res.data || []
        for (let i = 0; i < list.length; i++) {
          if (list[i].supplierCode === val) {
            row.processorName = list[0].supplierName
          }
        }
        this.saveEditRow(row, index)
      })
    },
    editRow(row, index) {
      console.log(row, '编辑行数据')
      if (row.processorCode && !row.processorName) {
        this.getSupplier(row.processorCode, row, index)
      } else {
        this.saveEditRow(row, index)
      }
    },
    saveEditRow(row, index) {
      const params = {
        ...row,
        timeInfoTimestamp: row.timeInfoTimestamp?.getTime() || 0,
        jit: row.scheduleType === 'JIT' ? 1 : 0,
        buyerNum: row.self49 //p字段
      }
      let flag = this.judgeValid1(params)
      if (!flag) {
        this.startEdit(index)
        return
      }
      this.$API.deliverySchedule
        .buyerGoodsDemandPlanInfoUpdate(params)
        .then(() => {
          this.$toast({
            content: this.$t('编辑要货排期操作成功'),
            type: 'success'
          })
          this.updateList()
        })
        .catch(() => {
          this.startEdit(index)
        })
    },
    handleDelete(row) {
      //删除
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认删除选中的要货排期吗?')
        },
        success: () => {
          let ids = row.map((item) => item.goodsDemandPlanItemId)
          this.$API.deliverySchedule.buyerGoodsDemandPlanInfoDelete(ids).then(() => {
            this.$toast({
              content: this.$t('删除要货排期操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    updateList() {
      this.$refs.templateRef2.refreshCurrentGridData()
    },
    selectedChanged(val) {
      Object.assign(this.selectedOtherInfo, val.itemInfo)
      console.log(this.selectedOtherInfo, '最新的额外数据')
    },
    actionBegin(args) {
      console.log(args, '我是actionBegin')
      if (args.requestType === 'add') {
        let lastColumn2 = cloneDeep(this.lastColumn2)
        lastColumn2.forEach((item) => {
          args.data[item.field] = ''
          if (item.field === 'status') {
            args.data[item.field] = this.$t('新建')
          }
          if (item.field === 'deliveryStatus') {
            args.data[item.field] = this.$t('未发货')
          }
          if (item.field === 'self5') {
            args.data[item.field] = this.$t('未收货')
          }
          if (item.field === 'limitNum') {
            args.data[item.field] = '0'
          }
          if (item.field === 'remainingNum') {
            args.data[item.field] = '0'
          }
          if (item.field === 'warehouseQty') {
            args.data[item.field] = '0'
          }
          if (item.field === 'timeInfoTimestamp') {
            args.data[item.field] = this.getToday()
          }
          if (item.field === 'scheduleType') {
            args.data[item.field] = this.$t('非JIT')
          }
          if (item.field === 'outsourcedType') {
            args.data[item.field] = '2'
          }
          if (item.field === 'deliveryMethod') {
            args.data[item.field] = '1'
          }
          args.data.isEntry = '2'
        })
        args.data.addId = this.addId++
        this.nowEditRowFlag = args.data.addId
      } else if (args.requestType == 'beginEdit') {
        this.nowEditRowFlag = args.rowData.addId
        if (this.isDetail === '1') {
          args.cancel = true
          return
        }
        if (
          !(
            args.rowData.status == 0 ||
            args.rowData.status == 1 ||
            args.rowData.status == 4 ||
            args.rowData.isEntry == '2'
          )
        ) {
          this.$toast({
            content: this.$t('此状态不可编辑'),
            type: 'warning'
          })
          args.cancel = true
        }
      } else if (args.requestType === 'save') {
        let valid = true
        // if (args.action === 'add') {

        // }
        // else {
        //   const row = this.getRow()
        //   valid = this.validateEdit(row, args.rowIndex)
        // }
        if (!valid) args.cancel = true
      }
    },
    actionComplete(args) {
      console.log(args, '我是actionComplete')
      const { rowIndex, index } = args
      if (args.requestType === 'save' && args.action === 'edit') {
        //编辑完成
        let row = this.getRow()
        row.demandCode = args.data.demandCode // 净需求编号是使用自带的editor做的编辑，所以额外加进row中
        this.$refs.templateRef2.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
        if (row.isEntry === '2') {
          //新增错误重新编辑
          this.saveAddRow(row, rowIndex)
        }
        if (row.isEntry === '1') {
          this.editRow(row, rowIndex)
        }
      }
      if (args.requestType === 'save' && args.action === 'add') {
        //新增完成
        let row = this.getRow()
        this.validateAdd(row, args.index)
        row.demandCode = args.data.demandCode // 净需求编号是使用自带的editor做的编辑，所以额外加进row中
        this.saveAddRow(row, index)
        this.$refs.templateRef2.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
      }
    },
    validateAdd(row, index) {
      const params = this.dellAddRowData(row, index)
      let flag = this.judgeValid(params)
      return flag
    },
    dellAddRowData(row) {
      const params = {
        ...row,
        item: [
          {
            timeInfoTimestamp: row.timeInfoTimestamp?.getTime() || 0,
            total: 0,
            buyerNum: row.self49,
            buyerRemark: row.buyerRemark, // 采购备注
            released: 0, //下达
            limitNum: 0, //限量数量           //需要给默认值
            remainingNum: 0, //剩余可创建数量        //需要给默认值
            outstandingNum: 0 // 未清订单数量
          }
        ],
        checkDate: 0, // 确认日期, 需要给默认值
        deliveryStatus: 0, // 发货状态, 需要给默认值
        jit: row.scheduleType === 'JIT' ? 1 : 0,
        status: '', //状态
        supplierCheckUser: '', // 供应商确认人, 需要给默认值
        supplierRemark: '', // 供应商备注, 需要给默认值
        systemRemark: '', // 系统备注, 需要给默认值
        version: '', // 版本号, 需要给默认值
        warehouseQty: 0 // 库存数量, 需要给默认值
      }
      return params
    },
    saveAddRow(row, index) {
      const params = this.dellAddRowData(row, index)
      this.$API.deliverySchedule
        .buyerGoodsDemandPlanInfoSave(params)
        .then(() => {
          this.$toast({
            content: this.$t('新增要货排期操作成功'),
            type: 'success'
          })
          this.updateList()
        })
        .catch(() => {
          this.startEdit(index)
        })
    },
    judgeValid(params) {
      //添加校验
      let flag = true
      if (!params.itemCode) {
        this.$toast({
          content: this.$t('物料信息必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.supplierCode) {
        this.$toast({
          content: this.$t('供应商信息必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.siteName) {
        this.$toast({
          content: this.$t('工厂必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.companyName) {
        this.$toast({
          content: this.$t('公司必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.scheduleType) {
        this.$toast({
          content: this.$t('JIT物料必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (params.item[0].buyerNum === '') {
        this.$toast({
          content: this.$t('P（需求量）必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.item[0].timeInfoTimestamp) {
        this.$toast({
          content: this.$t('需求日期必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (!params.deliveryMethod) {
        this.$toast({
          content: this.$t('配送方式必填'),
          type: 'warning'
        })
        flag = false
        return
      }
      if (params.deliveryMethod === '0') {
        //配送方式为直送
        if (!params.processorCode || !params.processorName) {
          this.$toast({
            content: this.$t('配送方式为直送时,加工商编码和名称必填'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      if (params.outsourcedType === '2') {
        //委外方式非委外
        if (params.deliveryMethod === '0') {
          this.$toast({
            content: this.$t('委外方式为非委外时,配送方式需为非直送'),
            type: 'warning'
          })
          flag = false
          return
        }
      }
      return flag
    },
    startEdit(index) {
      this.$refs.templateRef2.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
      this.$refs.templateRef2.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
    },
    endEdit() {
      //让组件失去焦点
      this.$refs.templateRef2.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
    },
    getRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.templateRef2
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = cloneDeep(this.selectedOtherInfo)
      let info = {}
      currentRecords.some((item) => {
        if (item.addId == this.nowEditRowFlag) {
          Object.assign(item, row)
          info = item
        }
      })
      return info
    },
    //点击表格的操作按钮
    handleClickCellTool(e) {
      console.log('方法2', e)
      if (e.tool.id === 'Publish') {
        this.handlePublish([e.data])
      }
      if (e.tool.id === 'Cancel') {
        this.handleCancle([e.data])
      }
      if (e.tool.id === 'Close') {
        this.handleClose([e.data])
      }
      if (e.tool.id === 'Confirm') {
        this.handleConfirm([e.data])
      }
    },
    //点击顶部的操作按钮
    handleClickToolBar(e) {
      console.log('方法1', e)
      console.log(e.gridRef.getMtechGridRecords(), e.toolbar.id)
      if (e.toolbar.id === 'closeEdit') {
        e.grid.closeEdit()
        this.updateList()

        return
      }
      if (e.toolbar.id === 'Add') {
        this.handleAdd()
        return
      }
      if (e.toolbar.id === 'Import') {
        this.handleImport()
        return
      }
      if (e.toolbar.id === 'Export1') {
        this.handleExport()
        return
      }
      if (e.toolbar.id === 'Export_JIT') {
        this.handleExportJIT()
        return
      }
      if (e.toolbar.id === 'TemplateExport') {
        this.handleTemplateExport()
        return
      }
      let selectRecords = e.gridRef.getMtechGridRecords()
      if (!selectRecords.length && e.toolbar.id != 'refreshDataByLocal') {
        this.$toast({ content: this.$t('请至少选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Delete') {
        this.handleDelete(selectRecords)
      }
      if (e.toolbar.id === 'Cancel') {
        this.handleCancle(selectRecords)
      }
      if (e.toolbar.id === 'Close') {
        this.handleClose(selectRecords)
      }
      if (e.toolbar.id === 'Publish') {
        this.handlePublish(selectRecords)
      }
      if (e.toolbar.id === 'Confirm') {
        this.handleConfirm(selectRecords)
      }
      if (e.toolbar.id === 'Release') {
        this.handleRelease(selectRecords)
      }
    }
  }
}
</script>
