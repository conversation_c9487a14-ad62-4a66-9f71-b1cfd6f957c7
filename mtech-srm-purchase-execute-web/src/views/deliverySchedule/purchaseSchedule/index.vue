<template>
  <!-- 要货排期列表 -->
  <div class="orderConfig full-height vertical-flex-box">
    <mt-template-page
      ref="templateRef1"
      :template-config="pageConfig"
      :permission-obj="permissionObj"
    >
      <div slot="slot-0" class="full-height">
        <page1></page1>
      </div>
      <div slot="slot-1" class="full-height">
        <page2 :is-detail="'2'" version="2"></page2>
      </div>
    </mt-template-page>
  </div>
</template>
<script>
export default {
  components: {
    page1: () => import('./page/page1.vue'),
    page2: () => import('./page/page2.vue')
  },
  data() {
    return {
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0020' },
          { dataPermission: 'b', permissionCode: 'T_02_0021' }
        ]
      },
      pageConfig: [
        {
          title: this.$t('日程视图'),
          dataPermission: 'a',
          permissionCode: 'T_02_0020'
        },
        {
          title: this.$t('列表视图'),
          dataPermission: 'b',
          permissionCode: 'T_02_0021'
        }
      ]
    }
  }
}
</script>
