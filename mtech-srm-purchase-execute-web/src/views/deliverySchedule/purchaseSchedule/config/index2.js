import UTILS from '../../../../utils/utils'
import Input from '../components2/Input.vue'
import Vue from 'vue'
import InputView from '../components2/InputView.vue'
import Select from '../components2/Select.vue'
import DatePicker from '../components2/DatePicker.vue'
import DatePickerView from '../components2/DataPickerView.vue'
import InputNumber from '../components2/InputNumber.vue'
import InputNumberView from '../components2/InputNumberView.vue'
import ItemCode from '../components2/ItemCode.vue'
import editView from '../components1/editView.vue'
import editDateView from '../components1/editDateView.vue'
import { MasterDataSelect } from '@/utils/constant'

//开始改了啊啊
import { i18n } from '@/main.js'
export const checkColumn2 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false,
    allowEditing: false
  }
]

export const importColumn2 = [
  {
    field: 'ImportType',
    headerText: i18n.t('导入状态'),
    width: '150',
    allowEditing: false
  }
]

export const lastColumn2 = [
  {
    field: 'addId',
    headerText: 'addId',
    width: 0,
    allowEditing: false,
    visible: false
  },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    allowEditing: false,
    visible: false
  },
  {
    field: 'version',
    headerText: i18n.t('版本号'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return e
      }
    },
    allowEditing: false
  },
  {
    field: 'serialNumber',
    headerText: i18n.t('序列号'),
    width: '150',
    allowEditing: false
  },
  {
    field: 'demandCode',
    headerText: i18n.t('净需求编号'),
    width: '150'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '150',
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('新建'),
        1: i18n.t('已修改'),
        2: i18n.t('待反馈'),
        3: i18n.t('反馈正常'),
        4: i18n.t('反馈异常'),
        5: i18n.t('已确认'),
        6: i18n.t('已关闭'),
        7: i18n.t('同步中')
      }
    },
    options: [
      { text: i18n.t('新建'), value: '0' },
      { text: i18n.t('已修改'), value: '1' },
      { text: i18n.t('待反馈'), value: '2' },
      { text: i18n.t('反馈正常'), value: '3' },
      { text: i18n.t('反馈异常'), value: '4' },
      { text: i18n.t('已确认'), value: '5' },
      { text: i18n.t('已关闭'), value: '6' },
      { text: i18n.t('同步中'), value: '7' }
    ],
    editTemplate: () => {
      return { template: editView }
    },
    cellTools: [
      {
        permission: ['O_02_0446'],
        id: 'Publish',
        icon: 'icon_solid_pushorder',
        title: i18n.t('发布'),
        visibleCondition: (data) => {
          return data.status === 0 || data.status === 1 || data.status === 4
        }
      },
      {
        permission: ['O_02_0693'],
        id: 'Cancle',
        icon: 'icon_solid_pushorder',
        title: i18n.t('取消发布'),
        visibleCondition: (data) => {
          return data.status === 2
        }
      },
      {
        permission: ['O_02_0451'],
        id: 'Close',
        icon: 'icon_solid_pushorder',
        title: i18n.t('关闭'),
        visibleCondition: (data) => {
          return data.status === 2 || data.status === 3 || data.status === 4 || data.status === 5
        }
      },
      {
        permission: ['O_02_0448'],
        id: 'Confirm',
        icon: 'icon_solid_pushorder',
        title: i18n.t('确认'),
        visibleCondition: (data) => {
          return data.status === 4
        }
      }
    ]
  },
  {
    field: 'deliveryStatus',
    headerText: i18n.t('发货状态'),
    width: '99',
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未发货'),
        1: i18n.t('部分发货'),
        2: i18n.t('全部发货')
      }
    },
    options: [
      { text: i18n.t('未发货'), value: '0' },
      { text: i18n.t('部分发货'), value: '1' },
      { text: i18n.t('全部发货'), value: '2' }
    ],
    editTemplate: () => {
      return { template: editView }
    }
  },
  {
    field: 'receiveStatus',
    headerText: i18n.t('收货状态'),
    width: '99',
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未收货'),
        1: i18n.t('部分收货'),
        2: i18n.t('全部收货')
      }
    },
    options: [
      { text: i18n.t('未收货'), value: '0' },
      { text: i18n.t('部分收货'), value: '1' },
      { text: i18n.t('全部收货'), value: '2' }
    ],
    editTemplate: () => {
      return { template: editView }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    selectOptions: [],
    width: 300,
    searchOptions: {
      operator: 'likeright',
      maxQueryValueLength: 20000
    },
    // searchOptions: {
    //   ...MasterDataSelect.material,
    //   renameField: "itemCode",
    // },
    editTemplate: () => {
      return { template: ItemCode }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料编号')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    selectOptions: [],
    ignore: true,
    width: '425',
    editTemplate: () => {
      return { template: InputView }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料名称')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'planGroupName',
    headerText: i18n.t('计划组'),
    width: '250',
    selectOptions: [],
    searchOptions: {
      ...MasterDataSelect.businessGroup,
      renameField: 'planGroupCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.planGroupCode}}-{{data.planGroupName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    selectOptions: [],
    width: '252',
    searchOptions: {
      ...MasterDataSelect.factoryAddress,
      renameField: 'siteCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('工厂')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.siteCode}}-{{data.siteName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司'),
    width: '215',
    searchOptions: {
      ...MasterDataSelect.businessCompany,
      renameField: 'companyCode'
    },
    editTemplate: () => {
      return { template: InputView }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('公司')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.companyCode}}-{{data.companyName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'buyerOrgName',
    headerText: i18n.t('采购组'),
    width: '263',
    searchOptions: {
      ...MasterDataSelect.businessGroup,
      renameField: 'buyerOrgCode'
    },
    editTemplate: () => {
      return { template: InputView }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'scheduleArea',
    headerText: i18n.t('计划区域'),
    width: '150',
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    allowFiltering: false,
    ignore: true,
    field: 'itemDescription',
    // headerText: i18n.t("物料描述"),
    width: 0,
    editTemplate: () => {
      return { template: InputView }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料描述')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    selectOptions: [],
    width: '258',
    searchOptions: {
      ...MasterDataSelect.supplier,
      renameField: 'supplierCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    // template: () => {
    //   return {
    //     template: Vue.component('actionView', {
    //       template: `<span>{{ data['supplierCode'] + '-' + data['supplierName'] }}</span>`,
    //       data() {
    //         return {
    //           data: {}
    //         }
    //       }
    //     })
    //   }
    // },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('供应商')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.supplierCode}}-{{data.supplierName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  // {
  //   field: "supplierName",
  //   headerText: i18n.t("供应商名称"),
  //   width: "200",
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  //   headerTemplate: () => {
  //     return {
  //       template: Vue.component("headers", {
  //         template: `
  //             <div class="headers">
  //               <span style="color: red">*</span>
  //               <span class="e-headertext">{{$t('供应商名称')}}</span>
  //             </div>
  //           `,
  //       }),
  //     };
  //   },
  // },
  {
    field: 'outsourcedType',
    headerText: i18n.t('委外方式'),
    selectOptions: [
      { label: i18n.t('销售委外'), value: '1' },
      { label: i18n.t('标准委外'), value: '0' },
      { label: i18n.t('非委外'), value: '2' },
      { label: i18n.t('工序委外'), value: '3' }
    ],
    width: '200',
    editTemplate: () => {
      return { template: Select }
    },
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('销售委外'),
        2: i18n.t('非委外'),
        0: i18n.t('标准委外'),
        3: i18n.t('工序委外')
      }
    }
  },
  {
    field: 'deliveryMethod',
    headerText: i18n.t('配送方式'),
    selectOptions: [
      { label: i18n.t('直送'), value: '0' },
      { label: i18n.t('非直送'), value: '1' } // 默认非委外只能选择非直送
    ],
    width: '200',
    editTemplate: () => {
      return { template: Select }
    },
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('直送'), 1: i18n.t('非直送') }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('配送方式')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'associatedNumber',
    headerText: i18n.t('关联工单号'),
    width: '150',
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'bom',
    headerText: i18n.t('BOM号'),
    width: '150',
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'saleOrder',
    headerText: i18n.t('销售订单号'),
    width: '150',
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    ignore: true,
    allowFiltering: false,
    field: 'saleOrderRowCode',
    headerText: i18n.t('销售订单行号'),
    width: 0,
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'workCenterName',
    headerText: i18n.t('工作中心'),
    selectOptions: [],
    searchOptions: {
      ...MasterDataSelect.workCenter,
      renameField: 'workCenterCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    width: '250',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.workCenterCode}}-{{data.workCenterName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'processName',
    headerText: i18n.t('工序名称'),
    width: 0,
    ignore: true,
    allowFiltering: false,
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'productCode',
    headerText: i18n.t('产品代码'),
    width: 0,
    allowFiltering: false,
    ignore: true,
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'buyerOrder',
    headerText: i18n.t('采购订单'),
    selectOptions: [],
    width: '200',
    editTemplate: () => {
      return { template: Select }
    }
  },
  {
    field: 'buyerOrderRowCode',
    headerText: i18n.t('采购订单行'),
    width: '150',
    maxlength: '240',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'warehouseQty',
    headerText: i18n.t('库存量'),
    width: '150',
    allowEditing: false
  },
  {
    field: 'projectTextBatch',
    headerText: i18n.t('项目文本批次'),
    width: '150',
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'released',
    headerText: i18n.t('下达'),
    allowEditing: false,
    allowFiltering: false,
    ignore: true,
    // selectOptions: [
    //   { label: i18n.t("未下达"), value: "0" },
    //   { label: i18n.t("已下达"), value: "1" },
    // ],
    width: '150',
    // editTemplate: () => {
    //   return { template: Select };
    // },
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('未下达'), 1: i18n.t('已下达') }
    },
    options: [
      { text: i18n.t('未下达'), value: '0' },
      { text: i18n.t('已下达'), value: '1' }
    ],
    editTemplate: () => {
      return { template: editView }
    }
  },
  {
    field: 'limitNum',
    headerText: i18n.t('限量数量'),
    width: '96',
    allowEditing: false
  },
  {
    field: 'outstandingNum',
    headerText: i18n.t('未清订单数量'),
    width: '120',
    allowEditing: false
  },
  {
    field: 'remainingNum',
    headerText: i18n.t('剩余可创建数量'),
    type: 'number',
    width: '133',
    allowEditing: false
  },
  {
    field: 'haveDeliveryNum',
    headerText: i18n.t('已发货数量'),
    width: '150',
    allowEditing: false
  },
  {
    field: 'timeInfoTimestamp',
    headerText: i18n.t('需求日期'),
    searchOptions: {
      ...MasterDataSelect.dateRange
    },
    width: '150',
    editTemplate: () => {
      return { template: DatePicker }
    },
    template: () => {
      return { template: DatePickerView }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('需求日期')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'processorCode',
    headerText: i18n.t('加工商'),
    selectOptions: [],
    width: '350',
    searchOptions: {
      ...MasterDataSelect.supplier,
      renameField: 'processorCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.processorCode}}-{{data.processorName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  // {
  //   field: "processorName",
  //   headerText: i18n.t("加工商名称"),
  //   width: "150",
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
  {
    field: 'warehouseCode',
    headerText: i18n.t('库存地点编号+名称'),
    selectOptions: [],
    width: '250',
    searchOptions: {
      ...MasterDataSelect.stockAddress,
      renameField: 'warehouseCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.warehouseCode}}-{{data.warehouseName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  // {
  //   field: "warehouseName",
  //   headerText: i18n.t("库存地点名称"),
  //   width: 0,
  //   editTemplate: () => {
  //     return { template: InputView };
  //   },
  // },
  {
    field: 'address',
    headerText: i18n.t('收货信息'),
    width: '300',
    selectOptions: [],
    editTemplate: () => {
      return { template: Select }
    },
    allowFiltering: false,
    ignore: true
    // headerTemplate: () => {
    //   return {
    //     template: Vue.component("headers", {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('收货信息')}}</span>
    //           </div>
    //         `,
    //     }),
    //   };
    // },
  },
  {
    field: 'warehouseKeeper',
    headerText: i18n.t('仓管员'),
    width: '150',
    maxlength: '40',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    width: '80',
    allowEditing: false,
    allowFiltering: false,
    ignore: true
  },
  {
    field: 'systemRemark',
    headerText: i18n.t('系统备注'),
    width: '150',
    allowEditing: false
  },
  {
    field: 'buyerRemark',
    headerText: i18n.t('采购方备注'),
    width: '150',
    maxlength: 200,
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'supplierRemark',
    headerText: i18n.t('供应商备注'),
    width: '120',
    allowEditing: false
  },
  {
    field: 'scheduleType',
    headerText: i18n.t('JIT物料'),
    valueConverter: {
      type: 'map',
      map: {
        ['JIT']: i18n.t('JIT'),
        ['非JIT']: i18n.t('非JIT')
      }
    },
    selectOptions: [
      { label: 'JIT', value: 'JIT' },
      { label: i18n.t('非JIT'), value: i18n.t('非JIT') }
    ],
    width: '200',
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('JIT物料')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'supplierCheckUser',
    headerText: i18n.t('供应商确认人'),
    width: '120',
    allowEditing: false,
    allowFiltering: false,
    ignore: true
  },
  {
    allowFiltering: false,
    ignore: true,
    field: 'checkDate',
    headerText: i18n.t('确认日期'),
    width: '150',
    allowEditing: false,
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    editTemplate: () => {
      return { template: editDateView }
    }
  },
  {
    field: 'self48',
    headerText: i18n.t('D（原始需求）'),
    width: '150',
    allowEditing: false,
    allowFiltering: false,
    ignore: true
  },
  {
    field: 'self49',
    headerText: i18n.t('P（需求量）'),
    width: '150',
    allowFiltering: false,
    ignore: true,
    editTemplate: () => {
      return { template: InputNumber }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('P（需求量）')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    allowFiltering: false,
    ignore: true,
    field: 'self50',
    headerText: i18n.t('C（承诺量）'),
    width: '150',
    allowEditing: false
  },
  {
    allowFiltering: false,
    ignore: true,
    field: 'self51',
    headerText: i18n.t('Gap（差额）'),
    width: '150',
    allowEditing: false,
    template: () => {
      return { template: InputNumberView }
    }
  }
]
