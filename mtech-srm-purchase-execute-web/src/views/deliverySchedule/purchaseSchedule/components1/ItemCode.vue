<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <!-- 选择物料/品项编码、SKU -->
      <mt-input
        :id="fieldName"
        v-model="data[fieldName]"
        disabled
        width="200"
        :placeholder="headerTxt"
      ></mt-input>
      <mt-icon
        v-if="!isDisabled"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>

    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="headerTxt"
      :buttons="buttons"
      @close="handleClose"
    >
      <mt-template-page
        v-show="dialogShow"
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="pageConfig"
        @recordDoubleClick="recordDoubleClick"
      >
      </mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
import { itemCodeColumnData } from './config/pcSelection.js' // 命名要与field code一致
import { PROXY_MDM_TENANT } from '@/utils/constant'
export default {
  data() {
    return {
      data: {},
      fieldName: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [],
      itemCodeColumnData,
      fields: {},
      headerTxt: '',
      dialogShow: false,
      itemCode: '',
      isDisabled: false
    }
  },
  mounted() {
    this.fieldName = this.data.column.field
    this.itemCode = this.data.itemCode
    this.setDisabled()
    this.initDialog()
  },

  methods: {
    setDisabled() {
      if (this.data.isEntry === '1') {
        this.isDisabled = true
      }
      if (this.data.isEntry === '2') {
        this.isDisabled = false
      }
    },
    // 双击物料行，也进行提交
    recordDoubleClick(args) {
      console.log('recordDoubleClick', args)
      this.confirm([args.rowData])
    },
    // 提交
    confirm(records) {
      let _records = records
      if (!_records || !_records.length) {
        _records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      }
      if (!_records.length) return
      console.log('选择到的物料信息：', _records[0])
      if (_records[0]) {
        let selectedRowInfo = _records[0]
        // 如果是sku，需要整合一下数据源，将物料信息展开
        selectedRowInfo = {
          itemId: selectedRowInfo?.id, // 物料数据
          itemCode: selectedRowInfo?.itemCode,
          itemName: selectedRowInfo?.itemName,
          itemDescription: selectedRowInfo?.itemDescription, // 规格型号(采购申请)
          itemGroupCode: selectedRowInfo?.itemGroupCode, //品项组编码
          itemGroupName: selectedRowInfo?.itemGroupName //品项组名称
        }
        this.data[this.fieldName] = selectedRowInfo[this.fieldName]
        //物料下拉
        this.$bus.$emit('itemCodeChange', {
          itemCode: selectedRowInfo.itemCode,
          itemId: selectedRowInfo.itemId
        }) //传给工厂
        this.$bus.$emit('itemCodeChange3', selectedRowInfo.itemCode) //传给计划组
        this.$bus.$emit('itemCodeChange4', selectedRowInfo.itemCode) //传给配送方式
        this.$bus.$emit('itemCodeChange1', selectedRowInfo.itemDescription) //传给物料描述
        this.$bus.$emit('itemCodeChange2', selectedRowInfo.itemName) //传给物料名称
        this.$parent.$emit('selectedChanged', {
          //传出额外数据物料
          fieldCode: 'itemCode',
          itemInfo: {
            itemCode: selectedRowInfo.itemCode,
            itemName: selectedRowInfo.itemName,
            itemId: selectedRowInfo.itemId,
            itemGroupName: selectedRowInfo.itemGroupName,
            itemGroupCode: selectedRowInfo.itemGroupCode
          }
        })
        this.handleClose()
      }
    },
    showDialog() {
      this.dialogShow = true
      this.$refs.dialog.ejsRef.show()
    },
    initDialog() {
      if (this.fieldName == 'itemCode') {
        this.pageConfig = [
          {
            useToolTemplate: false, // 不使用预置(新增、编辑、删除)
            useBaseConfig: true, // 使用组件中的toolbar配置
            toolbar: [],
            grid: {
              allowPaging: true,
              allowSelection: true,
              selectionSettings: {
                checkboxOnly: false
              },
              columnData: this[`${this.fieldName}ColumnData`],
              asyncConfig: {
                url: `${PROXY_MDM_TENANT}/item/paged-query?BU_CODE=${localStorage.getItem(
                  'currentBu'
                )}`
              }
            }
          }
        ]
      }
    },
    handleClose() {
      this.dialogShow = false
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>

<style lang="scss">
.pc-item-dialog {
  .mt-data-grid {
    height: 100%;
    > .e-grid {
      height: calc(100% - 40px) !important;
      // display: flex;

      > .e-gridcontent {
        flex: 1;
        overflow: auto;
      }
    }

    .e-rowcell.e-active {
      background: #e0e0e0 !important;
    }
  }
}
</style>
