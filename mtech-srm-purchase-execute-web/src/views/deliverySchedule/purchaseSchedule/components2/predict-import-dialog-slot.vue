<template>
  <div
    class="full-height vertical-flex-box"
    id="predict-import-dialog-slot"
    ref="dialogSlotContainer"
  >
    <div id="toolbar-container" class="flex-keep">
      <div class="tool-left">
        <!-- <div class="delete-btn" @click="handleDelete()">
          <span><mt-icon name="icon_table_delete"></mt-icon></span>
          <span>{{ $t('删除') }}</span>
        </div> -->
      </div>
      <div class="tool-right">
        <div class="data-switch">
          <mt-switch
            v-model="isShowCacheError"
            @change="isShowCacheErrorChange"
            :on-label="$t('仅显示错误数据')"
            :off-label="$t('所有数据')"
          ></mt-switch>
        </div>
      </div>
    </div>
    <div class="flex-fit">
      <mt-template-page
        ref="templateRef1"
        :template-config="componentConfig"
        @handleClickToolBar="handleClickToolBar"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
        @selectedChanged="selectedChanged"
      ></mt-template-page>
    </div>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { checkColumn2, lastColumn2, importColumn2 } from '../config/index2'
import { BASE_TENANT } from '@/utils/constant'
import * as UTILS from '@/utils/utils'
export default {
  components: {},
  props: {},
  watch: {},
  data() {
    return {
      componentConfig: [
        {
          toolbar: [{ id: 'Delete', icon: 'icon_solid_Delete', title: this.$t('删除') }],
          grid: {
            height: 'auto',
            allowEditing: true, //开启表格编辑操作
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            columnData: checkColumn2.concat(lastColumn2).concat(importColumn2),
            asyncConfig: {}
          }
        }
      ],
      isEdit: '1',
      isShowCacheError: false, // 仅显示错误信息
      addId: '1',
      selectedOtherInfo: {},
      nowEditRowFlag: '' //当前编辑的行id
    }
  },
  mounted() {},
  methods: {
    doImport() {
      //todo确认导入
      const params = { code: this.forecastCacheCode }
      this.$API.predictCollaboration.getBuyerForecastCacheSubmit(params).then((res) => {
        if (res?.code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$emit('submitted')
        }
      })
    },
    selectedChanged(val) {
      Object.assign(this.selectedOtherInfo, val.itemInfo || {})
      console.log(this.selectedOtherInfo, '最新的额外数据导入的')
    },
    actionBegin(args) {
      console.log(args, '我是actionBegin')
      if (args.requestType == 'beginEdit') {
        this.nowEditRowFlag = args.rowData.addId
      }
    },
    actionComplete(args) {
      console.log(args, '我是actionComplete')
      if (args.requestType === 'beginEdit' || args.requestType === 'add') {
        this.isEdit = '1'
      }
      if (
        args.requestType === 'refresh' ||
        args.requestType === 'save' ||
        args.requestType === 'delete'
      ) {
        this.isEdit = '2'
      }
      if (args.requestType === 'save' && args.action === 'edit') {
        //编辑完成
        let row = this.getRow()
        this.editRow(row)
      }
    },
    editRow(row) {
      console.log(row, '编辑行数据')
      let params = {
        id: row.id,
        goodsDemandPlanItemId: row.goodsDemandPlanItemId,
        supplierCode: row.supplierCode, //供应商
        supplierId: row.supplierId, //供应商id
        supplierName: row.supplierName, //供应商名称
        deliveryMethod: row.deliveryMethod, //配送方式
        released: row.released, //下达
        timeInfo: UTILS.default.formateTime(row.timeInfo, 'YYYY/mm/dd') || 0, // 需求日期
        buyerNum: row.self49, //p字段
        processorCode: row.processorCode, //加工商编码
        processorId: row.processorId, //加工商id
        processorName: row.processorName, //加工商名称
        warehouseCode: row.warehouseCode, //库存地点编码
        warehouseId: row.warehouseId, //库存地点id
        warehouseName: row.warehouseName, //库存地点名称
        addressId: row.addressId, //地址id
        buyerRemark: row.buyerRemark // 采购备注
      }
      this.$API.deliverySchedule.buyerGoodsDemandPlanInfoUpdate(params).then(() => {
        this.$toast({
          content: this.$t('编辑要货排期操作成功'),
          type: 'success'
        })
        this.updateList()
      })
    },
    getRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.templateRef1
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = cloneDeep(this.selectedOtherInfo)
      currentRecords.some((item) => {
        if (item.addId === this.nowEditRowFlag) {
          Object.assign(row, item)
        }
      })
      return row
    },
    isShowCacheErrorChange(val) {
      if (val) {
        //显示错误信息
      }
      if (!val) {
        //显示全部信息
      }
    },
    handleDelete(row) {
      //删除
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认删除选中的要货排期吗?')
        },
        success: () => {
          let ids = row.map((item) => item.id)
          this.$API.purchaseOrder.poTimeConfigDel(ids).then(() => {
            this.$toast({
              content: this.$t('删除要货排期操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    updateList() {
      this.$refs.templateRef1.refreshCurrentGridData()
    },
    // 初始化
    init(args) {
      this.isShowCacheError = false
      console.log(args.resData, '我是args信息')
      this.$set(this.componentConfig[0].grid, 'asyncConfig', {
        url: `${BASE_TENANT}/buyerGoodsDemandPlanInfo/query/full`,
        serializeList: (list) => {
          list.forEach((item) => {
            item.addId = this.addId++
            item.self48 = item.total
            item.self49 = item.buyerNum
            item.self50 = item.supplierNum
            item.self51 = item.gapNum
            if (item.timeInfo && item.timeInfo.length === 10) {
              item.timeInfo = new Date(item.timeInfo)
            } else {
              item.timeInfo = null
            }
            item.isEntry = '2' //是否是带入的数据
          })
          this.currentList = list
          return list
        }
      })
    },
    //点击顶部的操作按钮
    handleClickToolBar(e) {
      console.log('方法1', e)
      console.log(e.gridRef.getMtechGridRecords(), e.toolbar.id)
      let selectRecords = e.gridRef.getMtechGridRecords()
      // if (!selectRecords.length) {
      //   this.$toast({ content: this.$t("请至少选择一行"), type: "warning" });
      //   return;
      // }
      if (e.toolbar.id === 'Delete') {
        this.handleDelete(selectRecords)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
#toolbar-container {
  display: flex;
  justify-content: space-between;

  .delete-btn {
    color: #4f5b6d;
    height: 42px;
    line-height: 42px;
    width: 66px;
    text-align: center;
    cursor: pointer;
  }
  .data-switch {
    line-height: 42px;
  }
}

/deep/ .errorText {
  color: #ed5633;
}
</style>
