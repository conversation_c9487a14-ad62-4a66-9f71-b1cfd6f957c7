<template>
  <div>
    <mt-input id="timeInfoTimestamp" style="display: none"></mt-input>
    <span>{{ timeInfoTimestamp }}</span>
  </div>
</template>
<script>
import UTILS from '../../../../utils/utils'
export default {
  data() {
    return {
      timeInfoTimestamp: ''
    }
  },
  mounted() {
    if (this.data.timeInfoTimestamp) {
      this.timeInfoTimestamp = UTILS.formateTime(this.data.timeInfoTimestamp, 'Y-m-d')
    }
  }
}
</script>
