<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="disabled"
    ></mt-input>
  </div>
</template>
<script>
export default {
  data() {
    return { data: {}, disabled: true }
  },
  mounted() {
    this.$bus.$on('itemCodeChange1', (val) => {
      console.log('物料描述啊啊啊', val)
      this.data.itemDescription = val
    }) //接受的物料描述
    this.$bus.$on('itemCodeChange2', (val) => {
      this.data.itemName = val
    }) //接受的物料名称
    this.$bus.$on('companyNameChange', (val) => {
      this.data.companyName = val
    }) //接受的公司信息
    this.$bus.$on('planGroupNameChange', (val) => {
      this.data.planGroupName = val
    }) //接受的计划组
    this.$bus.$on('buyerOrgNameChange', (val) => {
      this.data.buyerOrgName = val
    }) //接受的采购组
    this.$bus.$on('supplierNameChange', (val) => {
      this.data.supplierName = val
    }) //接受的供应商
    this.$bus.$on('processorNameChange', (val) => {
      this.data.processorName = val
    }) //接受的加工商
    this.$bus.$on('warehouseNameChange', (val) => {
      this.data.warehouseName = val
    }) //接受的库存地点
  },
  methods: {}
}
</script>
