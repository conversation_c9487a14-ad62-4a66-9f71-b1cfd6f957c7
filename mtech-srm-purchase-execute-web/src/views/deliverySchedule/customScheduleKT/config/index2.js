import UTILS from '../../../../utils/utils'
import Input from '../components2/Input.vue'
import Select from '../components2/Select.vue'
import InputNumber from '../components2/InputNumber.vue'
import InputNumberView from '../components2/InputNumberView.vue'
import InputNumberView1 from '../components2/InputNumberView1.vue'
import editView from '../components1/editView.vue'
import editDateView from '../components1/editDateView.vue'
import { MasterDataSelect } from '@/utils/constant'
import Vue from 'vue'
import { i18n } from '@/main.js'
import inputViewInfo from '../components2/inputViewInfo.vue'
export const checkColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false,
    allowEditing: false
  }
]
export const lastColumn = [
  {
    field: 'addId',
    headerText: 'addId',
    width: 0,
    allowEditing: false,
    visible: false
  },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    allowEditing: false,
    visible: false
  },
  // {
  //   field: "version",
  //   headerText: i18n.t("版本号"),
  //   width: "150",
  //   valueConverter: {
  //     type: "function",
  //     filter: (e) => {
  //       return "V" + e;
  //     },
  //   },
  //   allowEditing: false,
  // },
  {
    field: 'serialNumber',
    headerText: i18n.t('序列号'),
    width: '150',
    allowEditing: false,
    ignore: true
  },
  // {
  //   field: 'number',
  //   headerText: i18n.t('序号'),
  //   width: '150',
  //   allowEditing: false
  // },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '150',
    allowEditing: false,
    cellTools: [],
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('新建'),
        1: i18n.t('新建-修改'),
        2: i18n.t('已发布'),
        3: i18n.t('反馈正常'),
        4: i18n.t('反馈异常'),
        5: i18n.t('已确认'),
        6: i18n.t('已关闭'),
        7: i18n.t('同步中')
      }
    },
    options: [
      { text: i18n.t('新建'), value: '0' },
      { text: i18n.t('已修改'), value: '1' },
      { text: i18n.t('已发布'), value: '2' },
      { text: i18n.t('反馈正常'), value: '3' },
      { text: i18n.t('反馈异常'), value: '4' },
      { text: i18n.t('已确认'), value: '5' },
      { text: i18n.t('已关闭'), value: '6' },
      { text: i18n.t('同步中'), value: '7' }
    ],
    editTemplate: () => {
      return { template: editView }
    }
  },
  {
    field: 'minPacketCount',
    headerText: i18n.t('最小包装量'),
    width: '100'
    // valueConverter: {
    //   type: "map",
    //   map: {
    //     0: i18n.t("未同步"),
    //     200: i18n.t("已同步"),
    //     500: i18n.t("同步失败"),
    //   },
    // },
    // editTemplate: () => {
    //   return { template: InputView };
    // },
  },
  {
    field: 'stockType',
    headerText: i18n.t('库存类型'),
    width: '100',
    valueConverter: {
      type: 'map',
      map: {
        STOCK: i18n.t('STOCK'),
        OPSTK: i18n.t('OPSTK')
      }
    },
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    }
    // valueConverter: {
    //   type: "map",
    //   map: {
    //     0: i18n.t("未同步"),
    //     200: i18n.t("已同步"),
    //     500: i18n.t("同步失败"),
    //   },
    // },
    // editTemplate: () => {
    //   return { template: InputView };
    // },
  },
  // {
  //   field: "deliveryStatus",
  //   headerText: i18n.t("发货状态"),
  //   width: "150",
  //   allowEditing: false,
  //   valueConverter: {
  //     type: "map",
  //     map: {
  //       0: i18n.t("未发货"),
  //       1: i18n.t("部分发货"),
  //       2: i18n.t("全部发货"),
  //     },
  //   },
  //   options: [
  //     { text: i18n.t("未发货"), value: "0" },
  //     { text: i18n.t("部分发货"), value: "1" },
  //     { text: i18n.t("全部发货"), value: "2" },
  //   ],
  //   editTemplate: () => {
  //     return { template: editView };
  //   },
  // },
  // {
  //   field: "receiveStatus",
  //   headerText: i18n.t("收货状态"),
  //   width: "150",
  //   allowEditing: false,
  //   valueConverter: {
  //     type: "map",
  //     map: {
  //       0: i18n.t("未收货"),
  //       1: i18n.t("部分收货"),
  //       2: i18n.t("全部收货"),
  //     },
  //   },
  //   options: [
  //     { text: i18n.t("未收货"), value: "0" },
  //     { text: i18n.t("部分收货"), value: "1" },
  //     { text: i18n.t("全部收货"), value: "2" },
  //   ],
  //   editTemplate: () => {
  //     return { template: editView };
  //   },
  // },
  // {
  //   field: "planGroupName",
  //   headerText: i18n.t("计划组"),
  //   width: "200",
  //   allowEditing: false,
  //   template: () => {
  //     return {
  //       template: Vue.component("headers", {
  //         template: `
  //             <div class="headers">
  //               <span>{{data.planGroupCode}}-{{data.planGroupName}}</span>
  //             </div>
  //           `,
  //         data() {
  //           return {
  //             data: {},
  //           };
  //         },
  //       }),
  //     };
  //   },
  // },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    width: '300',
    allowEditing: false,
    searchOptions: { ...MasterDataSelect.factorySupplierAddress },

    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.siteCode}}-{{data.siteName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    width: '1',
    allowEditing: false
  },
  // {
  //   field: "companyName",
  //   headerText: i18n.t("客户公司"),
  //   width: "300",
  //   allowEditing: false,
  //   template: () => {
  //     return {
  //       template: Vue.component("headers", {
  //         template: `
  //             <div class="headers">
  //               <span>{{data.companyCode}}-{{data.companyName}}</span>
  //             </div>
  //           `,
  //         data() {
  //           return {
  //             data: {},
  //           };
  //         },
  //       }),
  //     };
  //   },
  // },
  {
    field: 'buyerOrgName',
    headerText: i18n.t('采购组'),
    width: '250',
    allowEditing: false,
    searchOptions: {
      ...MasterDataSelect.businessCodeName,
      renameField: 'buyerOrgCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    selectOptions: [],
    width: '245',
    searchOptions: {
      ...MasterDataSelect.supplier,
      renameField: 'supplierCode'
    },
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red"></span>
                <span class="e-headertext">{{$t('供应商')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.supplierCode}}-{{data.supplierName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  // {
  //   field: "scheduleArea",
  //   headerText: i18n.t("计划区域"),
  //   width: "200",
  //   allowEditing: false,
  // },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    searchOptions: {
      operator: 'likeright'
    },
    width: '250',
    allowEditing: false
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: '150',
    allowEditing: false
  },
  {
    field: 'isItemBatch',
    headerText: i18n.t('是否批次来料'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否') }
    },
    width: '120'
  },
  {
    ignore: true,
    allowFiltering: false,
    field: 'itemDescription',
    headerText: i18n.t('物料描述'),
    width: 0,
    allowEditing: false
  },
  {
    field: 'outsourcedType',
    headerText: i18n.t('委外方式'),
    width: '200',
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('销售委外'),
        2: i18n.t('标准采购'),
        0: i18n.t('标准委外')
      }
    },
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    options: [
      { text: i18n.t('标准委外'), value: '0' },
      { text: i18n.t('销售委外'), value: '1' },
      { text: i18n.t('标准采购'), value: '2' }
    ],
    editTemplate: () => {
      return { template: editView }
    }
  },
  {
    field: 'deliveryMethod',
    headerText: i18n.t('配送方式'),
    width: '200',
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('直送'), 1: i18n.t('非直送') }
    },
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    options: [
      { text: i18n.t('直送'), value: '0' },
      { text: i18n.t('非直送'), value: '1' }
    ],
    editTemplate: () => {
      return { template: editView }
    }
  },
  {
    field: 'associatedNumber',
    headerText: i18n.t('关联工单号'),
    width: '150',
    searchOptions: {
      maxQueryValueLength: 100000,
      placeholder: i18n.t('支持粘贴多个精准及单个右模糊查询')
    },
    allowEditing: false
  },
  {
    field: 'domesticDemandFlag',
    headerText: i18n.t('是否内需跟单'),
    width: '160',
    valueConverter: {
      type: 'map',
      map: { E: i18n.t('是'), F: i18n.t('否') }
    },
    allowEditing: false
  },
  {
    field: 'domesticDemandCode',
    headerText: i18n.t('内需单号'),
    width: '150'
  },
  {
    field: 'saleOrder',
    headerText: i18n.t('销售订单'),
    width: '150'
  },
  {
    field: 'saleOrderRowCode',
    headerText: i18n.t('销售订单行'),
    width: '150'
  },
  {
    field: 'printMaterialSerialNumber',
    headerText: i18n.t('客户序列流水号/变量'),
    width: '150',
    allowEditing: false,
    allowFiltering: false,
    ignore: true
  },
  {
    field: 'printMaterialFileName',
    headerText: i18n.t('流水号附件'),
    width: '150',
    cellTools: [],
    allowEditing: false,
    allowFiltering: false,
    ignore: true
  },
  // {
  //   field: "bom",
  //   headerText: i18n.t("BOM号"),
  //   width: "150",
  //   allowEditing: false,
  // },
  // {
  //   field: "saleOrder",
  //   headerText: i18n.t("销售订单号"),
  //   width: "150",
  //   allowEditing: false,
  // },
  // {
  //   field: "saleOrderRowCode",
  //   headerText: i18n.t("销售订单行号"),
  //   width: 0,
  //   allowEditing: false,
  //   ignore: true,
  //   allowFiltering: false,
  // },
  // {
  //   field: "workCenterName",
  //   headerText: i18n.t("工作中心"),
  //   width: "200",
  //   allowEditing: false,
  //   template: () => {
  //     return {
  //       template: Vue.component("headers", {
  //         template: `
  //           <div class="headers">
  //             <span>{{data.workCenterCode}}-{{data.workCenterName}}</span>
  //           </div>
  //         `,
  //         data() {
  //           return {
  //             data: {},
  //           };
  //         },
  //       }),
  //     };
  //   },
  // },
  // {
  //   field: "processName",
  //   headerText: i18n.t("工序名称"),
  //   width: 0,
  //   ignore: true,
  //   allowFiltering: false,
  //   allowEditing: false,
  // },
  // {
  //   field: "productCode",
  //   headerText: i18n.t("产品代码"),
  //   width: 0,
  //   ignore: true,
  //   allowFiltering: false,
  //   allowEditing: false,
  // },
  // {
  //   field: "buyerOrder",
  //   headerText: i18n.t("采购订单"),
  //   width: "150",
  //   allowEditing: false,
  // },
  // {
  //   field: "buyerOrderRowCode",
  //   headerText: i18n.t("采购订单行"),
  //   width: "150",
  //   allowEditing: false,
  // },
  // {
  //   field: "warehouseQty",
  //   headerText: i18n.t("库存量"),
  //   width: "150",
  //   allowEditing: false,
  // },
  {
    field: 'projectTextBatch',
    headerText: i18n.t('项目文本批次'),
    width: '150',
    allowEditing: false
  },
  // {
  //   ignore: true,
  //   allowFiltering: false,
  //   field: "released",
  //   headerText: i18n.t("下达"),
  //   width: "150",
  //   allowEditing: false,
  //   valueConverter: {
  //     type: "map",
  //     map: { 0: i18n.t("未下达"), 1: i18n.t("已下达") },
  //   },
  //   options: [
  //     { text: i18n.t("未下达"), value: "0" },
  //     { text: i18n.t("已下达"), value: "1" },
  //   ],
  //   editTemplate: () => {
  //     return { template: editView };
  //   },
  // },
  // {
  //   field: "limitNumber",
  //   headerText: i18n.t("限量数量"),
  //   width: "150",
  //   allowEditing: false,
  // },
  // {
  //   field: "outstandingNum",
  //   headerText: i18n.t("未清订单数量"),
  //   width: "150",
  //   allowEditing: false,
  // },
  // {
  //   field: "remainingNum",
  //   headerText: i18n.t("剩余可创建数量"),
  //   width: "150",
  //   allowEditing: false,
  // },
  {
    field: 'haveDeliveryNum',
    headerText: i18n.t('已发货数量'),
    width: '150',
    allowEditing: false
  },
  {
    field: 'timeInfoTimestamp',
    headerText: i18n.t('需求日期'),
    width: '150',
    allowEditing: false,
    searchOptions: {
      ...MasterDataSelect.timeRange,
      renameField: 'timeInfoTimestamp'
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    editTemplate: () => {
      return { template: editDateView }
    }
  },
  // {
  //   field: "processorCode",
  //   headerText: i18n.t("加工商编号"),
  //   width: "250",
  //   allowEditing: false,
  // },
  {
    field: 'processorName',
    headerText: i18n.t('加工商'),
    width: '250',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.processorCode}}-{{data.processorName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'warehouseCode',
    headerText: i18n.t('库存地点编号+名称'),
    width: '250',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.warehouseCode}}-{{data.warehouseName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    ignore: true,
    allowFiltering: false,
    field: 'address',
    headerText: i18n.t('收货信息'),
    width: '300',
    allowEditing: false,
    template: () => ({ template: inputViewInfo })
  },
  {
    field: 'warehouseKeeper',
    headerText: i18n.t('仓管员'),
    width: '150',
    allowEditing: false
  },
  {
    // ignore: true,
    allowFiltering: false,
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    width: '150',
    allowEditing: false
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    width: '80',
    searchOptions: {
      ...MasterDataSelect.timeRange,
      renameField: 'createTime'
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    allowEditing: false,
    allowFiltering: false
    // ignore: true,
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('最后更新人'),
    width: '80',
    allowEditing: false,
    allowFiltering: false
    // ignore: true,
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    width: '80',
    searchOptions: {
      ...MasterDataSelect.timeRange,
      renameField: 'updateTime'
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    allowEditing: false,
    allowFiltering: false
    // ignore: true,
  },
  {
    field: 'shippingSpace',
    headerText: i18n.t('发料仓位'),
    width: '95',
    allowEditing: false
  },
  {
    field: 'systemRemark',
    headerText: i18n.t('系统备注'),
    width: '150',
    allowEditing: false
  },
  {
    field: 'buyerRemark',
    headerText: i18n.t('采购方备注'),
    width: '150',
    allowEditing: false
  },
  {
    field: 'supplierRemark',
    headerText: i18n.t('供应商备注'),
    width: '150',
    editTemplate: () => {
      return { template: Input }
    }
  },
  {
    field: 'scheduleType',
    headerText: i18n.t('是否JIT'),
    width: '200',
    allowEditing: false,
    searchOptions: {
      elementType: 'select',
      dataSource: [
        { label: i18n.t('否'), value: '0' },
        { label: i18n.t('是'), value: '1' }
      ],
      fields: { text: 'label', value: 'value' },

      renameField: 'jit'
    },

    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{Type(data.jit)}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {},
          methods: {
            Type(e) {
              if (e === 1) {
                return i18n.t('是')
              } else if (e === 0) {
                return i18n.t('否')
              }
            }
          }
        })
      }
    }
  },
  {
    ignore: true,
    allowFiltering: false,
    field: 'supplierCheckUser',
    headerText: i18n.t('供应商确认人'),
    width: '150',
    allowEditing: false
  },
  {
    ignore: true,
    allowFiltering: false,
    field: 'checkDate',
    headerText: i18n.t('确认日期'),
    width: '150',
    allowEditing: false,
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    editTemplate: () => {
      return { template: editDateView }
    }
  },
  // {
  //   ignore: true,
  //   allowFiltering: false,
  //   field: "self48",
  //   headerText: i18n.t("D（原始需求）"),
  //   width: "150",
  //   allowEditing: false,
  // },
  {
    ignore: true,
    allowFiltering: false,
    field: 'self49',
    headerText: i18n.t('P（需求量）'),
    width: '150',
    allowEditing: false
  },
  {
    ignore: true,
    allowFiltering: false,
    field: 'self50',
    headerText: i18n.t('C（承诺量）'),
    width: '150',
    editTemplate: () => {
      return { template: InputNumber }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('C（承诺量）')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    allowFiltering: false,
    ignore: true,
    field: 'noPromiseCount',
    headerText: i18n.t('未承诺数量'),
    width: '150',
    allowEditing: false
  },
  {
    ignore: true,
    allowFiltering: false,
    field: 'self51',
    headerText: i18n.t('Gap（差额）'),
    width: '150',
    allowEditing: false,
    template: () => {
      return { template: InputNumberView }
    },
    editTemplate: () => {
      return { template: InputNumberView1 }
    }
  }
]
