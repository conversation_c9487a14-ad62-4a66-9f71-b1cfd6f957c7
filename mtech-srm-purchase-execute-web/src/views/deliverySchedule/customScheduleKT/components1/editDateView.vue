<template>
  <div>
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      style="display: none"
    ></mt-input>
    <mt-input v-model="nowText" :disabled="true"></mt-input>
  </div>
</template>
<script>
import UTILS from '../../../../utils/utils'
export default {
  data() {
    return {
      nowText: ''
    }
  },
  mounted() {
    console.log(this.data[this.data.column.field], 'a哈哈发的是')
    if (this.data[this.data.column.field] && this.data[this.data.column.field].length == 13) {
      this.nowText = UTILS.dateFormat(Number(this.data[this.data.column.field]), 'Y-m-d')
    }
  }
}
</script>
