<template>
  <div class="selfType">
    <mt-input :id="data.column.field" style="display: none"></mt-input>
    <div>{{ showInfo[0] }}</div>
    <div>{{ showInfo[1] }}</div>
    <div>
      {{ showInfo[2] }}
    </div>
    <div>
      <span :class="calssName">{{ showInfo[3] }}</span>
    </div>
    <div>{{ showInfo[4] }}</div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      showInfo: ['', '', '', '', ''],
      data: {},
      calssName: ''
    }
  },
  mounted() {
    if (this.data[this.data.column.field]) {
      this.showInfo = this.data[this.data.column.field].split('_')
    } else {
      this.showInfo = ['', '', '', '', '']
    }
    if (this.showInfo[0] === this.showInfo[2]) {
      this.calssName = ''
    } else {
      this.calssName = 'red'
    }
  }
}
</script>
<style lang="scss" scoped>
.selfType {
  .red {
    color: #ed5836;
    background-color: #fdeeea;
  }
  > div {
    height: 36px;
    line-height: 36px;
    box-sizing: border-box;
    // padding: 12px 0;
  }
}
</style>
