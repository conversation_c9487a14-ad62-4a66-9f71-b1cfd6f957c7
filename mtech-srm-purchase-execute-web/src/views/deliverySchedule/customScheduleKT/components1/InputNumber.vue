<template>
  <div class="selfType">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      style="display: none"
    ></mt-input>
    <div>{{ showInfo.d }}</div>
    <div>{{ showInfo.p }}</div>
    <mt-inputNumber
      v-if="show"
      v-model="num"
      :min="0"
      :step="1"
      :precision="3"
      :show-clear-button="false"
      @input="inputChange"
    ></mt-inputNumber>
    <div>{{ showInfo.g }}</div>
    <div>{{ showInfo.u }}</div>
  </div>
</template>
<script>
import bigDecimal from 'js-big-decimal'
export default {
  data() {
    return {
      data: {},
      showInfo: {
        d: '',
        p: '',
        g: '',
        u: ''
      },
      num: '',
      show: true
    }
  },
  mounted() {
    if (this.data[this.data.column.field]) {
      this.show = true
      let list = this.data[this.data.column.field].split('_')
      this.showInfo.d = list[0]
      this.showInfo.p = list[1]
      this.num = list[2]
      this.showInfo.g = list[3]
      this.showInfo.u = list[4]
    } else {
      this.show = false
    }
  },
  methods: {
    inputChange(e) {
      let gNum = ''
      gNum = bigDecimal.subtract(e, this.showInfo.p)
      console.log(gNum, '我是g')
      this.showInfo.g = gNum
      this.data[
        this.data.column.field
      ] = `${this.showInfo.d}_${this.showInfo.p}_${e}_${this.showInfo.g}_${this.showInfo.u}`
      console.log(
        `${this.showInfo.d}_${this.showInfo.p}_${e}_${this.showInfo.g}_${this.showInfo.u}`,
        '我只发的',
        this.data[this.data.column.field]
      )
    }
  }
}
</script>
<style lang="scss" scoped>
.selfType {
  > div {
    height: 36px;
    line-height: 36px;
    box-sizing: border-box;
    // padding: 12px 0;
  }
}
</style>
