<template>
  <div>
    <mt-input id="self51" v-model="data.self51" style="display: none"></mt-input>
    <span :class="calssName">{{ data.self51 }}</span>
  </div>
</template>
<script>
export default {
  data() {
    return {
      calssName: '',
      data: {}
    }
  },
  mounted() {
    if (this.data.self48 === this.data.self50) {
      this.calssName = ''
    } else {
      this.calssName = 'red'
    }
  }
}
</script>
<style lang="scss" scoped>
.red {
  color: #ed5836;
  background-color: #fdeeea;
}
</style>
