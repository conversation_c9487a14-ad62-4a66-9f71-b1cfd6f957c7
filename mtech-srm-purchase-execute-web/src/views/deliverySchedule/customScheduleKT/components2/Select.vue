<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="fields"
      :placeholder="placeholder"
      @open="startOpen"
      @change="selectChange"
      :open-dispatch-change="false"
      :disabled="isDisabled"
      :allow-filtering="true"
      :filtering="serchText"
    ></mt-select>
  </div>
</template>
<script>
import { utils } from '@mtech-common/utils'
import { cloneDeep } from 'lodash'
export default {
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      itemCode: '',
      itemId: '',
      purchase: '', //采购组code
      plan: 'BG001JH', //计划组code
      isDisabled: false,
      deliveryMethod: '', //配送方式0直送 1非直送
      siteCode: '', //工厂code
      processorCode: '', //加工商code
      warehouseCode: '', //库存地点编号
      supplierCode: '',
      workCenterCode: ''
    }
  },
  async mounted() {
    this.dataSource = cloneDeep(this.data.column.selectOptions)
    if (this.data.column.field === 'itemCode') {
      //物料下拉
      this.itemCode = this.data.itemCode
      this.getCategoryItem()
      this.getCategoryItem = utils.debounce(this.getCategoryItem, 1000)
      this.setDisabled()
    }
    if (this.data.column.field === 'buyerOrgCode') {
      //采购组
      console.log(this.data)
      this.data.buyerOrgName === '' ? this.getBuyer('') : this.getBuyer(this.data.buyerOrgCode)
      // this.getBuyer();
      this.getCategoryItem = utils.debounce(this.getBuyer, 1000)
      this.setDisabled()
    }
    if (this.data.column.field === 'planGroupName') {
      //计划组下拉
      // await this.getBusinessGroupTypeListByDictCode();
      if (this.data.itemCode) {
        this.getBusinessGroup(this.data.itemCode)
      }
      this.$bus.$on('itemCodeChange3', (val) => {
        this.itemCode = val
        this.getBusinessGroup(val)
      })
      this.setDisabled()
    }
    if (this.data.column.field === 'siteName') {
      //工厂下拉
      console.log(this.data.itemCode, '选择的物料信息阿')
      if (this.data.itemCode) {
        this.getFactoryList(this.data.itemCode)
      }
      this.itemCode = this.data.itemCode
      this.$bus.$on('itemCodeChange', (val) => {
        this.getFactoryList(val.itemCode)
        this.itemCode = val.itemCode
        this.itemId = val.itemId
        //begin 物料改变清空一些数据清空工厂 ,公司 计划组 采购组 物料描述
        // 工厂清空后会清空库存地点
        this.$bus.$emit('siteCodeChange', '')
        this.data.siteName = ''
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'siteName',
          itemInfo: {
            siteCode: '',
            siteName: '',
            siteId: ''
          }
        })
        this.$bus.$emit('companyNameChange', '') //传给公司
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'companyName',
          itemInfo: {
            companyCode: '',
            companyName: '',
            companyId: ''
          }
        })
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'planGroupName',
          itemInfo: {
            planGroupName: '',
            planGroupId: '',
            planGroupCode: ''
          }
        })
        this.$bus.$emit('planGroupNameChange', '')
        this.$bus.$emit('buyerOrgNameChange', '')
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'buyerOrgName',
          itemInfo: {
            buyerOrgName: '',
            buyerOrgId: '',
            buyerOrgCode: ''
          }
        })
        this.$bus.$emit('itemCodeChange1', '') //传给物料描述
        //end 物料改变清空一些数据清空工厂 ,公司 计划组 采购组 物料描述
      })
      this.setDisabled()
    }
    if (this.data.column.field === 'supplierCode') {
      //供应商下拉
      this.supplierCode = this.data.supplierCode
      this.getSupplier()
      this.setDisabled()
      this.getSupplier = utils.debounce(this.getSupplier, 1000)
    }
    if (this.data.column.field === 'processorCode') {
      //加工商
      this.supplierCode = this.data.processorCode
      this.getSupplier()
      this.getSupplier = utils.debounce(this.getSupplier, 1000)
      this.setDisabled1()
      this.$bus.$on('workCenterChange', (val) => {
        if (val) {
          // this.data.processorCode = val.supplierCode;
          this.data.processorCode = null
          this.supplierCode = val.supplierCode
          this.dataSource = []
          this.getSupplier(this.supplierCode, val.supplierCode)
          this.isDisabled = true
          this.$bus.$emit('processorNameChange', val.supplierName)
          this.$bus.$emit('processorCodeChange', val.supplierCode)
          this.$parent.$emit('selectedChanged', {
            //传出额外数据加工商
            fieldCode: 'processorCode',
            itemInfo: {
              processorCode: val.supplierCode,
              processorName: val.supplierName,
              processorId: val.id
            }
          })
        } else {
          this.data.processorCode = ''
          this.isDisabled = false
          this.$bus.$emit('processorNameChange', '')
          this.$bus.$emit('processorCodeChange', '')
          this.$parent.$emit('selectedChanged', {
            //传出额外数据加工商
            fieldCode: 'processorCode',
            itemInfo: {
              processorCode: '',
              processorName: '',
              processorId: ''
            }
          })
        }
      })
      this.$bus.$on('processorChange', (val) => {
        this.dataSource = []
        // this.data.processorCode = val.supplierCode;
        this.data.processorCode = null
        this.supplierCode = val.supplierCode
        this.getSupplier(this.supplierCode, val.supplierCode)
        this.isDisabled = true
        this.$bus.$emit('processorNameChange', val.supplierName)
        this.$bus.$emit('processorCodeChange', val.supplierCode)
        this.$parent.$emit('selectedChanged', {
          //传出额外数据加工商
          fieldCode: 'processorCode',
          itemInfo: {
            processorCode: val.supplierCode,
            processorName: val.supplierName,
            processorId: val.id
          }
        })
      })
    }
    if (this.data.column.field === 'warehouseCode') {
      //库存地点
      this.$bus.$on('deliveryChange', (val) => {
        if (val === '0') {
          this.isDisabled = true
        } else {
          // this.$set(this, "dataSource", []);
          this.isDisabled = false
        }
      })

      if (this.data.deliveryMethod === '0') {
        this.isDisabled = true
      } else {
        // this.$set(this, "dataSource", []);
        this.isDisabled = false
      }
      this.siteCode = this.data.siteCode
      this.warehouseCode = this.data.warehouseCode
      this.$bus.$on('siteCodeChange1', (val) => {
        //监听工厂code变化
        this.siteCode = val
        if (this.data.warehouseCode) {
          console.log('已经选择了库存地点')
          return
        }
        this.getLocation()
      })
      this.getLocation()
      this.getLocation = utils.debounce(this.getLocation, 1000)
      this.setDisabled1()
    }
    if (this.data.column.field === 'workCenterName') {
      //工作中心
      this.workCenterCode = this.data.workCenterCode
      this.getWorkCenter()
      this.getWorkCenter = utils.debounce(this.getWorkCenter, 1000)
      this.setDisabled1()
    }
    if (this.data.column.field === 'address') {
      //送货联系人电话地址
      this.$bus.$on('updateAddressOptions', (val, type) => {
        if (type === '1') {
          this.dataSource = val
          this.fields = { text: 'label', value: 'label' }
          if (this.data.address) {
            val.some((item) => {
              if (item.label === this.data.address) {
                this.$parent.$emit('selectedChanged', {
                  //传出额外数据地址id
                  fieldCode: 'address',
                  itemInfo: {
                    address: item.label,
                    addressId: item.addressId,
                    receiveAddressCode: item.consigneeAddressCode,
                    receiveAddressName: item.consigneeAddress
                  }
                })
              }
            })
          }
        } else {
          this.dataSource = val
          this.data.address = ''
          this.$parent.$emit('selectedChanged', {
            //传出额外数据地址id
            fieldCode: 'address',
            itemInfo: {
              address: '',
              addressId: '',
              receiveAddressCode: '',
              receiveAddressName: ''
            }
          })
          this.fields = { text: 'label', value: 'label' }
          if (val.length > 0) {
            let selectInfo = null
            val.some((item) => {
              if (item.needDefault === 1) {
                selectInfo = item
                return
              }
            })
            val.some((item) => {
              if (item.needDefault === 1 && item.itemCode) {
                selectInfo = item
                return
              }
            })
            if (selectInfo) {
              this.data.address = selectInfo.label
              this.$parent.$emit('selectedChanged', {
                //传出额外数据地址id
                fieldCode: 'address',
                itemInfo: {
                  address: selectInfo.label,
                  addressId: selectInfo.addressId,
                  receiveAddressCode: selectInfo.consigneeAddressCode,
                  receiveAddressName: selectInfo.consigneeAddress
                }
              })
            }
          }
        }
      })
      this.setDisabled1()
    }

    if (this.data.column.field === 'scheduleType') {
      //计划类型
      this.setDisabled1()
      this.$bus.$on('scheduleTypeChange', (val) => {
        this.data.scheduleType = val
        this.isDisabled = true
      })
    }
    if (this.data.column.field === 'buyerOrder') {
      //采购订单
      this.setDisabled()
      this.purOrderQueryOrder({ text: '' }, '1')
      this.purOrderQueryOrder = utils.debounce(this.purOrderQueryOrder, 1000)
    }
    if (this.data.column.field === 'deliveryMethod') {
      //配送方式
      if (this.data.outsourcedType === '2') {
        this.outsourcedClick()
      } else {
        // this.$set(this, "dataSource", []);
        setTimeout(() => {
          this.$set(this, 'dataSource', [
            { label: this.$t('直送'), value: '0' },
            { label: this.$t('非直送'), value: '1' }
          ])
        }, 1)
      }
      this.$bus.$on('outsourcedTypeChange2', (val) => {
        console.log(val)
        if (val === '2') {
          this.outsourcedClick()
          // this.dataSource = this.data.column.selectOptions2;
          console.log(this.dataSource)
        } else {
          // this.$set(this, "dataSource", []);
          setTimeout(() => {
            this.$set(this, 'dataSource', [
              { label: this.$t('直送'), value: '0' },
              { label: this.$t('非直送'), value: '1' }
            ])
          }, 1)
        }
      })
      this.deliveryMethod = this.data.deliveryMethod
      this.siteCode = this.data.siteCode
      this.processorCode = this.data.processorCode
      this.warehouseCode = this.data.warehouseCode
      this.itemCode = this.data.itemCode
      //监听物料的变化
      this.$bus.$on('itemCodeChange4', (val) => {
        this.itemCode = val
      })
      this.siteTenantExtendqueryBySite('1')
      this.$bus.$on('siteCodeChange', (val) => {
        //监听工厂code变化
        this.siteCode = val
        this.siteTenantExtendqueryBySite()
      })
      this.$bus.$on('warehouseCodeChange', (val) => {
        //库存地点
        this.warehouseCode = val
        this.siteTenantExtendqueryBySite()
      })
      this.$bus.$on('processorCodeChange', (val) => {
        //加工商
        this.processorCode = val
        this.siteTenantExtendqueryBySite()
      })
      this.$bus.$on('deliveryMethodChange', (val) => {
        //配送方式
        this.data.deliveryMethod = val
        this.deliveryMethod = val
        this.isDisabled = true
        this.siteTenantExtendqueryBySite()
      })
      this.setDisabled1()
    }
    if (this.data.column.field === 'outsourcedType') {
      //委外方式
      this.setDisabled1()
      if (this.data.deliveryMethod === '0') {
        this.deliveryClick()
      } else {
        // this.$set(this, "dataSource", []);
        setTimeout(() => {
          this.$set(this, 'dataSource', [
            { label: this.$t('销售委外'), value: '1' },
            { label: this.$t('标准委外'), value: '0' },
            { label: this.$t('标准采购'), value: '2' }
          ])
        }, 1)
      }
      this.$bus.$on('deliveryChange', (val) => {
        if (val === '0') {
          this.deliveryClick()
        } else {
          // this.$set(this, "dataSource", []);
          setTimeout(() => {
            this.$set(this, 'dataSource', [
              { label: this.$t('销售委外'), value: '1' },
              { label: this.$t('标准委外'), value: '0' },
              { label: this.$t('标准采购'), value: '2' }
            ])
          }, 1)
        }
      })
      this.$bus.$on('outsourcedTypeChange', (val) => {
        this.data.outsourcedType = val
        this.isDisabled = true
      })
    }
  },
  beforeDestroy() {
    this.$bus.$off('outsourcedTypeChange22')
    this.$bus.$off('deliveryChange')

    this.$bus.$off('itemCodeChange3')
    this.$bus.$off('itemCodeChange')
    this.$bus.$off('workCenterChange')
    this.$bus.$off('siteCodeChange1')
    this.$bus.$off('updateAddressOptions')
    this.$bus.$off('itemCodeChange4')
    this.$bus.$off('siteCodeChange')
    this.$bus.$off('warehouseCodeChange')
    this.$bus.$off('processorCodeChange')
    this.$bus.$off('processorChange')
    this.$bus.$off('outsourcedTypeChange')
    this.$bus.$off('scheduleTypeChange')
    this.$bus.$off('deliveryMethodChange')
    //
  },
  methods: {
    outsourcedClick() {
      this.$set(this, 'dataSource', [])
      setTimeout(() => {
        this.$set(this, 'dataSource', [{ label: this.$t('非直送'), value: '1' }])
      }, 1)
    },
    deliveryClick() {
      // this.$set(this, "dataSource", []);
      setTimeout(() => {
        this.$set(this, 'dataSource', [
          { label: this.$t('销售委外'), value: '1' },
          { label: this.$t('标准委外'), value: '0' }
        ])
      }, 1)
    },
    serchText(val) {
      console.log('搜索值', val)
      if (this.data.column.field == 'buyerOrder') {
        this.purOrderQueryOrder(val)
      }
      if (this.data.column.field == 'warehouseCode') {
        this.getLocation(val && val.text ? val.text : '')
      }
      if (this.data.column.field == 'itemCode') {
        this.getCategoryItem(val && val.text ? val.text : '')
      }
      if (this.data.column.field == 'buyerOrgCode') {
        this.getBuyer(val && val.text ? val.text : '')
      }
      if (this.data.column.field == 'supplierCode') {
        this.getSupplier(val && val.text ? val.text : '')
      }
      if (this.data.column.field == 'processorCode') {
        this.getSupplier(val && val.text ? val.text : '')
      }
      if (this.data.column.field == 'workCenterName') {
        this.getWorkCenter(val && val.text ? val.text : '')
      }
    },
    purOrderQueryOrder(val, type) {
      //模糊查询订单号1
      let params = {
        condition: 'and',
        page: {
          current: 1,
          size: 50
        },
        defaultRules: [
          {
            label: this.$t('采购订单号'),
            field: 'orderCode',
            type: 'string',
            operator: 'contains',
            value: val && val.text ? val.text : ''
          }
        ]
      }
      this.$API.purchaseOrder.purOrderQueryOrder(params).then((res) => {
        let orderRelOptions = res.data.records.map((item) => {
          return {
            label: item,
            value: item
          }
        })
        orderRelOptions = orderRelOptions.filter((item) => {
          return item.label
        })
        if (type === '1') {
          let flag = orderRelOptions.some((item) => {
            return item.label == val
          })
          if (!flag && this.data.buyerOrder) {
            orderRelOptions.unshift({
              label: this.data.buyerOrder,
              value: this.data.buyerOrder
            })
          }
        }
        this.dataSource = orderRelOptions
      })
    },
    setDisabled1() {
      if (this.data.status === 4) {
        this.isDisabled = true
      } else {
        this.isDisabled = false
      }
    },
    setDisabled() {
      if (this.data.isEntry === '1' && this.data.status != 0 && this.data.status != 1) {
        this.isDisabled = true
      }
      if (this.data.isEntry === '2') {
        this.isDisabled = false
      }
    },
    getWorkCenter(val) {
      let str = val || this.workCenterCode
      let params = {
        keyword: str || ''
      }
      this.$API.masterData.getWorkCenter(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.workCenterCode}-${item.workCenterName}`
        })
        this.dataSource = res.data || []
        this.fields = { text: 'label', value: 'workCenterName' }
      })
    },
    getLocation(val) {
      //查询库存地点
      let str = val || this.warehouseCode
      let params = {
        commonCode: this.siteCode, //工厂code
        dataLimit: 50,
        fuzzyParam: str //搜索参数
      }
      if (!params.commonCode) {
        console.log('工厂没有')
        return
      }
      this.$API.masterData.getLocationFuzzyQuery(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          if (item.externalCode) {
            item.locationCode = item.externalCode
            if (item.externalName) {
              item.locationName = item.externalName
            }
          }
          item.label = `${item.locationCode}-${item.locationName}`
        })
        this.dataSource = list
        this.fields = { text: 'label', value: 'locationCode' }
      })
    },
    getSupplier(val, supplierCode) {
      //查询供应商的数据
      let str = val || this.supplierCode
      let params = {
        fuzzyNameOrCode: str || ''
      }
      this.$API.masterData.getSupplier(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.supplierCode}-${item.supplierName}`
        })
        this.dataSource = list
        this.fields = { text: 'label', value: 'supplierCode' }
        if (supplierCode) {
          this.data.processorCode = supplierCode
        }
      })
    },
    getBusinessGroup(itemCode) {
      //查询计划组
      if (!this.plan) return
      let params = {
        businessGroupTypeCode: this.plan, //物料code
        itemCode: itemCode //计划组
      }
      this.$API.masterData.getBusinessGroup(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.groupCode}-${item.groupName}`
        })
        this.dataSource = list
        this.fields = { text: 'label', value: 'groupName' }
      })
    },
    getFactoryList(itemCode) {
      //工厂下拉
      let params = {
        orgLevelTypeCode: 'ORG06',
        itemCode: itemCode
      }
      this.$API.masterData.getFactoryList(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.organizationCode}-${item.organizationName}`
        })
        this.dataSource = list
        this.fields = { text: 'label', value: 'organizationName' }
      })
    },
    getCategoryItem(val) {
      //物料下拉
      let params = {
        keyword: val || this.itemCode || '',
        pageSize: 50
      }
      this.$API.masterData.getItemByKeyword(params).then((res) => {
        this.dataSource = res.data?.records || []
      })
      this.fields = { text: 'itemCode', value: 'itemCode' }
    },
    getBuyer(val) {
      let params = {
        fuzzyParam: val,
        groupTypeCode: 'BG001CG'
      }
      this.$API.masterData.getbussinessGroup(params).then((res) => {
        this.dataSource = res.data
      })
      this.fields = { text: 'groupName', value: 'groupCode' }
      console.log('getBuyer', this.fields)
    },
    startOpen() {
      console.log(this.data, '下拉打开时最新的行数据')
      if (this.data.column.field === 'siteName') {
        //工厂下拉
        if (!this.data.itemCode && !this.itemCode) {
          this.$toast({
            content: this.$t('请先选择物料信息'),
            type: 'warning'
          })
        }
      }

      if (this.data.column.field === 'planGroupName') {
        if (!this.data.itemCode && !this.itemCode) {
          this.$toast({
            content: this.$t('请先选择物料信息'),
            type: 'warning'
          })
        }
      }
      if (this.data.column.field === 'warehouseCode') {
        if (!this.dataSource.length) {
          this.getLocation()
        }
      }
      if (this.data.column.field === 'itemCode') {
        if (!this.dataSource.length) {
          this.getCategoryItem()
        }
      }
      if (this.data.column.field === 'workCenterName') {
        if (!this.dataSource.length) {
          this.getWorkCenter()
        }
      }
    },
    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      if (this.data.column.field === 'itemCode') {
        //物料下拉
        this.$bus.$emit('itemCodeChange', {
          itemCode: val.itemData.itemCode,
          itemId: val.itemData.id
        }) //传给工厂
        this.$bus.$emit('itemCodeChange3', val.itemData.itemCode) //传给计划组
        this.$bus.$emit('itemCodeChange1', val.itemData.itemDescription) //传给物料描述
        this.$bus.$emit('itemCodeChange2', val.itemData.itemName) //传给物料名称
        this.$parent.$emit('selectedChanged', {
          //传出额外数据物料
          fieldCode: 'itemCode',
          itemInfo: {
            itemCode: val.itemData.itemCode,
            itemName: val.itemData.itemName,
            itemId: val.itemData.id,
            itemGroupName: val.itemData.itemGroupName,
            itemGroupCode: val.itemData.itemGroupCode
          }
        })
      }
      if (this.data.column.field === 'siteName') {
        //工厂下拉
        this.getSite(val.itemData.organizationCode)
        this.$parent.$emit('selectedChanged', {
          //传出额外数据工厂
          fieldCode: 'siteName',
          itemInfo: {
            siteCode: val.itemData.organizationCode,
            siteName: val.itemData.organizationName,
            siteId: val.itemData.organizationId
          }
        })
        this.$bus.$emit('siteCodeChange', val.itemData.organizationCode)
        this.$bus.$emit('siteCodeChange1', val.itemData.organizationCode)
        //查询采购组
        let params = {
          itemCode: this.itemCode, // 物料code
          organizationCode: val.itemData.organizationCode, //工厂code
          organizationId: val.itemData.organizationId
        }
        if (!this.itemCode) return
        this.getBasicDetail(params)
        let params1 = {
          itemCode: this.itemCode, // 物料code
          siteCode: val.itemData.organizationCode //工厂code
          // organizationCode: val.itemData.organizationCode, //todo要注释
          // organizationId: val.itemData.organizationId, //todo要注释
        }
        if (!this.itemCode) return
        this.getBySiteItemCode(params1)
      }
      if (this.data.column.field === 'supplierCode') {
        this.$bus.$emit('supplierNameChange', val.itemData.supplierName)
        this.$parent.$emit('selectedChanged', {
          //传出额外数据供应商
          fieldCode: 'supplierCode',
          itemInfo: {
            supplierCode: val.itemData.supplierCode,
            supplierName: val.itemData.supplierName,
            supplierId: val.itemData.id,
            supplierTenantId: val.itemData.supplierTenantId
          }
        })
      }
      if (this.data.column.field === 'processorCode') {
        this.$bus.$emit('processorNameChange', val.itemData.supplierName)
        this.$bus.$emit('processorCodeChange', val.itemData.supplierCode)
        this.$parent.$emit('selectedChanged', {
          //传出额外数据加工商
          fieldCode: 'processorCode',
          itemInfo: {
            processorCode: val.itemData.supplierCode,
            processorName: val.itemData.supplierName,
            processorId: val.itemData.id
          }
        })
      }
      if (this.data.column.field === 'warehouseCode') {
        //库存地点
        this.$bus.$emit('warehouseNameChange', val.itemData.locationName)
        this.$bus.$emit('warehouseCodeChange', val.itemData.locationCode)
        this.$parent.$emit('selectedChanged', {
          //传出额外数据库存地点
          fieldCode: 'warehouseCode',
          itemInfo: {
            warehouseCode: val.itemData.locationCode,
            warehouseName: val.itemData.locationName,
            warehouseId: val.itemData.id
          }
        })
        let params = {
          siteCode: this.siteCode,
          locationCode: val.itemData.locationCode
        }
        if (!this.siteCode) {
          console.log('工厂没有')
          return
        }
        this.getBySiteLocationCode(params)
      }
      if (this.data.column.field === 'buyerOrgCode') {
        this.$parent.$emit('selectedChanged', {
          //传出额外数据供应商
          fieldCode: 'supplierCode',
          itemInfo: {
            buyerOrgName: val.itemData.groupName,
            buyerOrgCode: val.itemData.groupCode,
            buyerOrgId: val.itemData.id
          }
        })
      }
      if (this.data.column.field === 'workCenterName') {
        //工作中心
        this.$parent.$emit('selectedChanged', {
          //传出额外数据库存地点
          fieldCode: 'workCenterName',
          itemInfo: {
            workCenterCode: val.itemData.workCenterCode,
            workCenterName: val.itemData.workCenterName,
            workCenter: val.itemData.id || '1'
          }
        })
        this.workCenterSupplierRelquery(val.itemData.workCenterCode)
      }
      if (this.data.column.field === 'address') {
        //送货联系人电话地址
        this.$parent.$emit('selectedChanged', {
          //传出额外数据地址id
          fieldCode: 'address',
          itemInfo: {
            address: val.itemData.label,
            addressId: val.itemData.addressId,
            receiveAddressCode: val.itemData.consigneeAddressCode,
            receiveAddressName: val.itemData.consigneeAddress
          }
        })
      }
      if (this.data.column.field === 'planGroupName') {
        //计划组
        this.$bus.$emit('planGroupNameChange', val.itemData.groupName)
        this.$parent.$emit('selectedChanged', {
          //传出额外数据计划组
          fieldCode: 'planGroupName',
          itemInfo: {
            planGroupName: val.itemData.groupName,
            planGroupId: val.itemData.id,
            planGroupCode: val.itemData.groupCode
          }
        })
      }
      if (this.data.column.field === 'outsourcedType') {
        this.$bus.$emit('outsourcedTypeChange2', val.itemData.value)
      }
      if (this.data.column.field === 'deliveryMethod') {
        //配送方式 0直送 1非直送
        this.$bus.$emit('deliveryChange', val.itemData.value)

        this.deliveryMethod = val.itemData.value
        this.siteTenantExtendqueryBySite()
      }
    },
    siteTenantExtendqueryBySite(type) {
      //根据工厂库存地点或加工商查询
      if (!this.deliveryMethod) {
        console.log('没有配送方式')
        return
      }
      let params = {}
      if (this.deliveryMethod === '0') {
        //直送 工厂+加工商
        params = {
          siteCode: this.siteCode, //工厂
          supplierCode: this.processorCode //加工商
        }
        if (!this.siteCode || !this.processorCode) {
          console.log(this.siteCode, this.processorCode, '没有工厂 加工商')
          return
        }
      }
      if (this.deliveryMethod === '1') {
        //非直送 工厂+库存地点
        params = {
          siteAddress: this.warehouseCode, //库存地点code
          siteCode: this.siteCode, //工厂
          itemCode: this.itemCode
        }
        if (!this.siteCode || !this.warehouseCode) {
          console.log(this.siteCode, this.warehouseCode, '没有工厂 库存地点')
          return
        }
      }
      this.$API.deliverySchedule.siteTenantExtendqueryBySite(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.consigneeName}-${item.consigneePhone}-${item.consigneeAddress}`
          item.addressId = item.id
        })
        this.$bus.$emit('updateAddressOptions', list, type)
      })
    },
    workCenterSupplierRelquery(str) {
      //获取工作中心和加工商关系
      let params = {
        condition: 'and',
        page: {
          current: 1,
          size: 50
        },
        defaultRules: [
          {
            label: this.$t('工作中心'),
            field: 'workCenter',
            type: 'string',
            operator: 'contains',
            value: str
          }
        ]
      }
      this.$API.deliverySchedule.workCenterSupplierRelquery(params).then((res) => {
        if (res.data?.records.length === 1) {
          this.$bus.$emit('workCenterChange', {
            supplierCode: res.data.records[0].supplierCode,
            supplierId: res.data.records[0].supplierId,
            supplierName: res.data.records[0].supplierName,
            supplierTenantId: res.data.records[0].supplierTenantId
          })
        } else {
          this.$bus.$emit('workCenterChange', null)
        }
      })
    },
    getBySiteLocationCode(params) {
      //根据工厂和库存地点获取委外方式 配送方式是否直送 加工商
      console.log(params, '我是要传的参数')
      // this.$API.masterData
      //   .getWorkCenter({
      //     keyword: "",
      //   })
      //   .then((res) => {
      this.$API.masterData.getBySiteLocationCode(params).then((res) => {
        // console.log(res);
        // res.data = {
        //   ifDirectSend: "N",
        //   ifSell: "N",
        //   supplierCode: "CO00000013",
        //   supplierName: "供应商名称1234",
        // };
        if (res.data.ifDirectSend === 'Y') {
          //是否直送
          this.$bus.$emit('deliveryMethodChange', '0')
        }
        if (res.data.ifDirectSend === 'N') {
          //是否直送
          this.$bus.$emit('deliveryMethodChange', '1')
        }
        if (res.data.ifSell === 'Y') {
          //销售委外
          this.$bus.$emit('outsourcedTypeChange', '1')
        }
        if (res.data.ifSell === 'N') {
          //标准委外
          this.$bus.$emit('outsourcedTypeChange', '0')
        }
        if (res.data.supplierCode) {
          //加工商
          this.$bus.$emit('processorChange', {
            supplierCode: res.data.supplierCode,
            supplierName: res.data.supplierName
          })
        }
      })
    },
    getBySiteItemCode(params1) {
      //根据工厂和物料查询jit配置信息
      // this.$API.masterData.getBasicDetail(params1).then((res) => {
      this.$API.masterData.getBySiteItemCode(params1).then((res) => {
        // res.data.itemIdentification = "Y";
        if (res.data.itemIdentification === 'Y') {
          this.$bus.$emit('scheduleTypeChange', 'JIT')
        }
        if (res.data.itemIdentification === 'N') {
          this.$bus.$emit('scheduleTypeChange', '非JIT')
        }
      })
    },
    getBasicDetail(params) {
      //查询采购组
      this.$API.masterData.getBasicDetail(params).then((res) => {
        this.$bus.$emit('buyerOrgNameChange', res.data?.purchaseGroupName)
        this.$parent.$emit('selectedChanged', {
          //传出额外数据采购组
          fieldCode: 'buyerOrgName',
          itemInfo: {
            buyerOrgName: res.data?.purchaseGroupName,
            buyerOrgId: res.data?.purchaseGroupId,
            buyerOrgCode: res.data?.purchaseGroupCode
          }
        })
      })
    },
    getSite(siteCode) {
      //根据工厂获取公司的信息
      let params = { siteCode: siteCode }
      this.$API.masterData.getSite(params).then((res) => {
        if (res.data.length === 1) {
          this.$bus.$emit('companyNameChange', res.data[0].parentName) //传给公司
          this.$parent.$emit('selectedChanged', {
            //传出额外数据公司
            fieldCode: 'companyName',
            itemInfo: {
              companyCode: res.data[0].parentCode,
              companyName: res.data[0].parentName,
              companyId: res.data[0].parentId
            }
          })
        }
      })
    }
  }
}
</script>
