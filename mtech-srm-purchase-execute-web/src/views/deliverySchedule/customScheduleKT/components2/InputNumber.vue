<template>
  <div>
    <mt-input id="self50" v-model="data.self50" style="display: none"></mt-input>
    <mt-inputNumber
      v-model="data.self50"
      :min="0"
      :step="1"
      :max="data.self49"
      :precision="3"
      :show-clear-button="false"
      @input="cChange"
    ></mt-inputNumber>
  </div>
</template>
<script>
import bigDecimal from 'js-big-decimal'
export default {
  data() {
    return {
      data: {},
      pNum: ''
    }
  },
  mounted() {
    this.pNum = this.data.self49
  },
  methods: {
    cChange(val) {
      console.log(val, '我是c值改变了')
      let gNum = ''
      gNum = bigDecimal.subtract(val, this.pNum)
      console.log(gNum, '我是g')
      this.$bus.$emit('cChange', val, gNum)
    }
  }
}
</script>
