<template>
  <!-- JIT送货计划-供方 -->
  <div class="full-height pt20">
    <!-- 列模板 -->
    <mt-template-page
      ref="templateRef"
      class="frozenFistColumns"
      :hidden-tabs="false"
      :template-config="templateConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
      @dataBound="handleDataBound"
    />
  </div>
</template>

<script>
import { formatTableColumnData, serializeList } from './config/index'
import {
  EditSettings,
  JitTableColumnData,
  JitToolbar,
  RequestType,
  ActionType,
  Status,
  FeedbackStatus
} from './config/constant'
import { rowDataTemp } from './config/variable'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'
import { timeNumberToDate } from '@/utils/utils'

export default {
  components: {},
  data() {
    const lastTabIndex = JSON.parse(localStorage.getItem('lastTabIndex'))
    const pageType = this.$route.query.type

    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      pageType, // 页面类型
      lastTabIndex, // 前一页面的 Tab index
      templateConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 不使用组件中的toolbar配置
          toolbar: JitToolbar,
          activatedRefresh: false,

          gridId: this.$tableUUID.deliverySchedule.purchaseJitSupplier.list,
          grid: {
            editSettings: EditSettings,
            allowPaging: true, // 分页
            columnData: formatTableColumnData({
              data: JitTableColumnData
            }),
            dataSource: [],
            asyncConfig: {
              ignoreDefaultSearch: true,

              url: `${BASE_TENANT}/supplierJitInfo/query`,
              serializeList: serializeList
            }
            // frozenColumns: 1, // 行内编辑的表格不可以冻结列
          }
        }
      ],
      isEditing: false // 正在编辑数据
    }
  },
  mounted() {},
  beforeDestroy() {
    localStorage.removeItem('lastTabIndex')
  },
  methods: {
    // toolbar 按钮点击
    handleClickToolBar(args) {
      const { toolbar, gridRef } = args
      const selectedRecords = gridRef.getMtechGridRecords()
      const commonToolbar = [
        'JitExport',
        'Filter',
        'Refresh',
        'refreshDataByLocal',
        'filterDataByLocal',
        'resetDataByLocal',
        'Setting'
      ]

      if (this.isEditing && toolbar.id !== 'refreshDataByLocal') {
        // 结束编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }

      if (selectedRecords.length == 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })

      if (toolbar.id === 'JitAccept') {
        // 接受
        this.handleJitAccept({ selectedRecords, idList })
      } else if (toolbar.id === 'JitRefuse') {
        // 拒绝
        this.handleJitRefuse({ selectedRecords, idList })
      } else if (toolbar.id === 'JitExport') {
        // 导出
        this.supplierJitInfoExport()
      }
    },
    // CellTool
    handleClickCellTool(args) {
      const { tool, data } = args

      if (this.isEditing) {
        // 正在编辑时，若不是点击刷新按钮，结束编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }

      if (tool.id === 'JitAccept') {
        // 接受
        this.handleJitAccept({
          selectedRecords: data,
          idList: [data.id]
        })
      } else if (tool.id === 'JitRefuse') {
        // 拒绝
        this.handleJitRefuse({
          selectedRecords: data,
          idList: [data.id]
        })
      }
    },
    // 接受
    handleJitAccept(args) {
      const { selectedRecords, idList } = args
      let isValid = true
      const today = Number(new Date())
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        const canFeedBackDay = Number(item.canFeedBackDay)
        if (item.status != Status.pending) {
          isValid = false
          this.$toast({
            content: this.$t('请选择待反馈状态的数据'),
            type: 'warning'
          })
          break
        } else if (canFeedBackDay > 0 && today > canFeedBackDay) {
          // 时间戳，"0" 时即为空, 超过时间限制
          isValid = false
          const canFeedBackDayStr = timeNumberToDate({
            formatString: 'YYYY-mm-dd HH:MM:SS',
            value: canFeedBackDay
          })
          let content = this.$t(`此数据只能在 ${canFeedBackDayStr} 前反馈`)
          if (selectedRecords.length > 1) {
            content = this.$t(`第 ${i + 1} 条数据只能在 ${canFeedBackDayStr} 前反馈`)
          }
          this.$toast({
            content,
            type: 'warning'
          })
          break
        }
      }
      if (isValid) {
        // 反馈
        this.supplierJitInfoFeedback({
          ids: idList,
          feedbackStatus: FeedbackStatus.accept
        })
      }
    },
    // 拒绝
    handleJitRefuse(args) {
      const { selectedRecords, idList } = args
      let isValid = true
      const today = Number(new Date())
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        const canFeedBackDay = Number(item.canFeedBackDay)
        if (item.status != Status.pending) {
          isValid = false
          this.$toast({
            content: this.$t('请选择待反馈状态的数据'),
            type: 'warning'
          })
          break
        } else if (canFeedBackDay > 0 && today > canFeedBackDay) {
          // 时间戳，"0" 时即为空, 超过时间限制
          isValid = false
          const canFeedBackDayStr = timeNumberToDate({
            formatString: 'YYYY-mm-dd HH:MM:SS',
            value: canFeedBackDay
          })
          let content = this.$t(`此数据只能在 ${canFeedBackDayStr} 前反馈`)
          if (selectedRecords.length > 1) {
            content = this.$t(`第 ${i + 1} 条数据只能在 ${canFeedBackDayStr} 前反馈`)
          }
          this.$toast({
            content,
            type: 'warning'
          })
          break
        }
      }
      if (isValid) {
        // 反馈
        this.supplierJitInfoFeedback({
          ids: idList,
          feedbackStatus: FeedbackStatus.reject
        })
      }
    },
    // actionBegin 表格编辑生命周期
    actionBegin(args) {
      const { requestType, action, rowData } = args
      // console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args);
      if (requestType === RequestType.add) {
        // 开始行添加
        this.isEditing = true
        // // 即将添加一行，赋值新增行的初始数据
        // rowDataTemp.splice(0, rowDataTemp.length); // 清空数组数据
        // const newRowData = cloneDeep(NewRowData);
        // rowDataTemp.push(newRowData);
        // args.rowData = newRowData;
        // args.data = newRowData;
      } else if (requestType === RequestType.save && action === ActionType.add) {
        // 即将保存新增时，将新增中编辑的数据付给组件
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === RequestType.beginEdit) {
        if (rowData.status !== Status.pending) {
          // 数据不可编辑 状态 != 待反馈
          args.cancel = true
          this.$toast({
            content: this.$t('仅待反馈状态的数据可编辑'),
            type: 'warning'
          })
          return
        }
        // 开始行编辑
        this.isEditing = true
        // 即将编辑行，赋值当前行的数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },
    // actionComplete 表格编辑生命周期
    actionComplete(args) {
      const { requestType, action, rowIndex, index } = args
      const rowData = rowDataTemp[rowDataTemp.length - 1]
      // console.log(
      //   `Complete,\nrequest: ${requestType}\naction: ${action}`,
      //   args
      // );
      if (requestType === RequestType.save && action == ActionType.edit) {
        // 完成编辑行
        this.isEditing = false
        // rowData, rowIndex
        if (!this.isValidSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API
          this.supplierJitInfoRemark({ rowData, rowIndex })
        }
      } else if (requestType === RequestType.save && action == ActionType.add) {
        // 完成新增行
        this.isEditing = false
        // rowData, index
        if (!this.isValidSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API
          this.supplierJitInfoRemark({ rowData, rowIndex: index })
        }
      } else if (requestType === RequestType.sorting || requestType === RequestType.refresh) {
        // 丢弃编辑中状态
        this.isEditing = false
      }
    },
    // 校验数据
    isValidSaveData() {
      // 没有校验
      // const {
      //   supRemark, // 供应商备注
      // } = data;
      let valid = true
      // if (supRemark === null || supRemark === undefined) {
      //   this.$toast({
      //     content: this.$t("请选择配送方式"),
      //     type: "warning",
      //   });
      //   valid = false;
      // }

      return valid
    },
    // 表格数据绑定完成
    handleDataBound() {},
    // 供方jit-修改备注
    supplierJitInfoRemark(args) {
      const { rowData, rowIndex } = args
      const params = {
        feedbackRemark: rowData.supRemark, // 供应商备注
        ids: [rowData.id],
        thePrimaryKey: undefined
      }
      this.apiStartLoading()
      this.$API.deliverySchedule
        .supplierJitInfoRemark(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    // 供方jit-反馈
    supplierJitInfoFeedback(params) {
      this.apiStartLoading()
      this.$API.deliverySchedule
        .supplierJitInfoFeedback(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方jit-Excel导出
    supplierJitInfoExport() {
      let obj = JSON.parse(
        sessionStorage.getItem('2ed96657-be83-43b1-b77c-4a67a94d9cb0')
      )?.visibleCols
      let field = []

      if (obj !== undefined && obj.length) {
        obj.forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      } else {
        JitTableColumnData.forEach((item) => {
          if (item.fieldCode) {
            field.push(item.fieldCode)
          }
        })
      }
      field.forEach((item, index) => {
        if (item === 'itemCode') {
          field.splice(index, 1, 'itemName')
          // field.push("itemName");
        }
        if (item === 'siteCode') {
          field.splice(index, 1, 'siteName')
          // field.push("siteName");
        }
        if (item === 'supplierCode') {
          field.splice(index, 1, 'supplierName')
          // field.push("supplierName");
        }
        if (item === 'companyCode') {
          field.splice(index, 1, 'companyName')
          // field.push("companyName");
        }
        if (item === 'buyerOrgCode') {
          field.splice(index, 1, 'buyerOrgName')
          // field.push("buyerOrgName");
        }
      })
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules,
        rules: [].concat(queryBuilderRules.rules || [])
      } // 筛选条件
      this.apiStartLoading()
      this.$API.deliverySchedule.supplierJitInfoExport(params, field).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
// 行内 cellTool
/deep/ .column-tool {
  margin-top: 8px;

  .template-svg {
    cursor: pointer;
    font-size: 12px;
    color: #6386c1;

    &:nth-child(n + 2) {
      margin-left: 10px;
    }
  }
}

// 行编辑时包含按钮的项目
/deep/ .input-search-content {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .mt-input {
    flex: 1;
  }

  .mutliselect-container {
    width: 100%;
  }
  // 超过宽度显示省略号
  .text-ellipsis {
    width: 125px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .mt-icons {
    width: 20px;
    flex-shrink: 0;
    text-align: center;
    cursor: pointer;
  }
}

/deep/ .grid-edit-column {
  padding: 12px 0;
}

.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  // padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
  }
}
</style>
