import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
// 表格编辑 的 RequestType
export const RequestType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  sorting: 'sorting',
  refresh: 'refresh'
}

// 表格编辑 的 action
export const ActionType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  edit: 'edit'
}

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 行编辑触发的 change 事件类型
export const ComponentChangeType = {
  code: 'code', // 例：下拉框选择“供应商”，供应商编码（code)要被修改
  link: 'link' // 例：计划组：根据物料+工厂+计划组的业务组类型 code 带出
}

// 表格 EditSettings
export const EditSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
} // 编辑设置

// 反馈结果 1-接受,2-拒绝
export const FeedbackStatus = {
  accept: 1, // 接受
  reject: 2 // 拒绝
}

// 状态 1-新建 2-已转交 3-待反馈 4-反馈异常 5-反馈正常 6-已关闭
export const Status = {
  new: 1, // 新建
  transferred: 2, // 已转交
  pending: 3, // 待反馈
  abnormal: 4, // 反馈异常
  normal: 5, // 反馈正常
  closed: 6 // 已关闭
}
// 状态 text
export const StatusText = {
  [Status.new]: i18n.t('新建'),
  [Status.transferred]: i18n.t('已转交'),
  [Status.pending]: i18n.t('待反馈'),
  [Status.abnormal]: i18n.t('反馈异常'),
  [Status.normal]: i18n.t('反馈正常'),
  [Status.closed]: i18n.t('已关闭')
}
// 状态 class
export const StatusClass = {
  [Status.new]: 'col-active',
  [Status.transferred]: 'col-active',
  [Status.pending]: 'col-active',
  [Status.abnormal]: 'col-active',
  [Status.normal]: 'col-active',
  [Status.closed]: 'col-inactive'
}
// 状态 对应的 Options
export const StatusOptions = [
  {
    value: Status.new, // 新建
    text: StatusText[Status.new],
    cssClass: StatusClass[Status.new]
  },
  {
    value: Status.transferred, // 已转交
    text: StatusText[Status.transferred],
    cssClass: StatusClass[Status.transferred]
  },
  {
    value: Status.pending, // 待反馈
    text: StatusText[Status.pending],
    cssClass: StatusClass[Status.pending]
  },
  {
    value: Status.abnormal, // 反馈异常
    text: StatusText[Status.abnormal],
    cssClass: StatusClass[Status.abnormal]
  },
  {
    value: Status.normal, // 反馈正常
    text: StatusText[Status.normal],
    cssClass: StatusClass[Status.normal]
  },
  {
    value: Status.closed, // 已关闭
    text: StatusText[Status.closed],
    cssClass: StatusClass[Status.closed]
  }
]

// 发货状态 未发货-1 全部发货-2 部分发货-3
export const DeliveryStatus = {
  notShipped: 1, // 未发货
  allShipped: 2, // 全部发货
  partiallyShipped: 3 // 部分发货
}
// 发货状态 text
export const DeliveryStatusText = {
  [DeliveryStatus.notShipped]: i18n.t('未发货'),
  [DeliveryStatus.allShipped]: i18n.t('全部发货'),
  [DeliveryStatus.partiallyShipped]: i18n.t('部分发货')
}
// 发货状态 Class
export const DeliveryStatusClass = {
  [DeliveryStatus.notShipped]: '',
  [DeliveryStatus.allShipped]: '',
  [DeliveryStatus.partiallyShipped]: ''
}
// 发货状态 对应的 Options
export const DeliveryStatusOptions = [
  {
    value: DeliveryStatus.notShipped, // 未发货
    text: DeliveryStatusText[DeliveryStatus.notShipped],
    cssClass: DeliveryStatusClass[DeliveryStatus.notShipped]
  },
  {
    value: DeliveryStatus.allShipped, // 全部发货
    text: DeliveryStatusText[DeliveryStatus.allShipped],
    cssClass: DeliveryStatusClass[DeliveryStatus.allShipped]
  },
  {
    value: DeliveryStatus.partiallyShipped, // 部分发货
    text: DeliveryStatusText[DeliveryStatus.partiallyShipped],
    cssClass: DeliveryStatusClass[DeliveryStatus.partiallyShipped]
  }
]

// 收货状态 未收货-1 全部收货-2 部分收货-3
export const ReceiveStatus = {
  notReceived: 1, // 未收货
  allReceived: 2, // 全部收货
  partialReceipt: 3 // 部分收货
}
// 收货状态 text
export const ReceiveStatusText = {
  [ReceiveStatus.notReceived]: i18n.t('未收货'),
  [ReceiveStatus.allReceived]: i18n.t('全部收货'),
  [ReceiveStatus.partialReceipt]: i18n.t('部分收货')
}
// 收货状态 Class
export const ReceiveStatusClass = {
  [ReceiveStatus.notShipped]: '',
  [ReceiveStatus.allReceived]: '',
  [ReceiveStatus.partialReceipt]: ''
}
// 收货状态 对应的 Options
export const ReceiveStatusOptions = [
  {
    value: ReceiveStatus.notReceived, // 未收货
    text: ReceiveStatusText[ReceiveStatus.notReceived],
    cssClass: ReceiveStatusClass[ReceiveStatus.notReceived]
  },
  {
    value: ReceiveStatus.allReceived, // 全部收货
    text: ReceiveStatusText[ReceiveStatus.allReceived],
    cssClass: ReceiveStatusClass[ReceiveStatus.allReceived]
  },
  {
    value: ReceiveStatus.partialReceipt, // 部分收货
    text: ReceiveStatusText[ReceiveStatus.partialReceipt],
    cssClass: ReceiveStatusClass[ReceiveStatus.partialReceipt]
  }
]

// 送货地址配置类型
export const DeliveryAddressConfigType = {
  inventory: 1, // 工厂+库存地点
  processor: 2, // 工厂+加工商
  branch: 3, // 工厂+库存地点+分厂+分厂库存地点
  material: 4 // 工厂+库存地点+物料
}
// 送货地址配置类型 是否默认
export const NeedDefault = {
  yes: 1, // 默认
  no: 2 // 非默认
}

// 配送方式 0直送 1非直送
export const DeliveryMethod = {
  direct: '0', // 直送
  indirect: '1' // 非直送
}
// 配送方式 text
export const DeliveryMethodText = {
  [DeliveryMethod.direct]: i18n.t('直送'),
  [DeliveryMethod.indirect]: i18n.t('非直送')
}
// 配送方式 Class
export const DeliveryMethodClass = {
  [DeliveryMethod.direct]: '',
  [DeliveryMethod.indirect]: ''
}
// 配送方式 对应的 Options
export const DeliveryMethodOptions = [
  {
    value: DeliveryMethod.direct, // 直送
    text: DeliveryMethodText[DeliveryMethod.direct],
    cssClass: DeliveryMethodClass[DeliveryMethod.direct]
  },
  {
    value: DeliveryMethod.indirect, // 非直送
    text: DeliveryMethodText[DeliveryMethod.indirect],
    cssClass: DeliveryMethodClass[DeliveryMethod.indirect]
  }
]

// 委外方式 0标准委外 1销售委外 2非委外
export const OutsourcedTypeOptions = [
  {
    value: '0', // 标准委外
    text: i18n.t('标准委外'),
    cssClass: ''
  },
  {
    value: '1', // 销售委外
    text: i18n.t('销售委外'),
    cssClass: ''
  },
  {
    value: '2', // 非委外
    text: i18n.t('非委外'),
    cssClass: ''
  }
]

// Jit表格 Toolbar
export const JitToolbar = [
  {
    id: 'JitAccept',
    icon: 'icon_table_accept1',
    permission: ['O_02_1149'],
    title: i18n.t('接受')
  },
  {
    id: 'JitRefuse',
    icon: 'icon_table_refuse1',
    permission: ['O_02_1150'],
    title: i18n.t('拒绝')
  },
  {
    id: 'JitExport',
    icon: 'icon_solid_export',
    permission: ['O_02_1151'],
    title: i18n.t('导出')
  }
]
// 单元格操作按钮
export const CellTools = [
  {
    id: 'JitAccept',
    icon: '',
    permission: ['O_02_1149'],
    title: i18n.t('接受'),
    visibleCondition: (data) => {
      let isShow = false
      const canFeedBackDay = Number(data.canFeedBackDay)
      const today = Number(new Date())
      if (data.status == Status.pending && canFeedBackDay > 0 && today < canFeedBackDay) {
        // 状态：待反馈 && 时间戳，"0" 时即为空, 没超过时间限制
        isShow = true
      }

      return isShow
    }
  },
  {
    id: 'JitRefuse',
    icon: '',
    permission: ['O_02_1150'],
    title: i18n.t('拒绝'),
    visibleCondition: (data) => {
      let isShow = false
      const canFeedBackDay = Number(data.canFeedBackDay)
      const today = Number(new Date())
      if (data.status == Status.pending && canFeedBackDay > 0 && today < canFeedBackDay) {
        // 状态：待反馈 && 时间戳，"0" 时即为空, 没超过时间限制
        isShow = true
      }

      return isShow
    }
  }
]

// JIT表格列数据
export const JitTableColumnData = [
  {
    fieldCode: 'checkBox' // 不可编辑
  },
  {
    fieldCode: 'thePrimaryKey'
  },
  {
    fieldCode: 'serialNumber', // 前端定义 不可编辑
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'rowCode',
    fieldName: i18n.t('JIT 行编号')
  },
  {
    fieldCode: 'status',
    fieldName: i18n.t('状态'),
    searchOptions: {
      elementType: 'multi-select',
      operator: 'in',
      showSelectAll: true,
      dataSource: StatusOptions
    }
  },
  {
    fieldCode: 'workOrder',
    fieldName: i18n.t('生产工单'),
    allowEditing: false,
    searchOptions: {
      maxQueryValueLength: 100000,
      placeholder: i18n.t('支持粘贴多个精准及单个右模糊查询')
    }
  },
  // {
  //   fieldCode: "deliveryStatus",
  //   fieldName: i18n.t("发货状态"),
  // },
  // {
  //   fieldCode: "receiveStatus",
  //   fieldName: i18n.t("收货状态"),
  // },
  // {
  //   fieldCode: "deliveryMethod",
  //   fieldName: i18n.t("配送方式"),
  // },
  // {
  //   fieldCode: "outsourcedType", // 委外方式
  //   fieldName: i18n.t("委外方式"),
  // },
  {
    fieldCode: 'productLine',
    fieldName: i18n.t('生产线')
  },
  {
    fieldCode: 'deliveryDate',
    fieldName: i18n.t('交货日期'),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  // {
  //   fieldCode: 'isJit', // 是否JIT 	是否jit 0否1是
  //   fieldName: i18n.t('是否JIT'),
  //   allowFiltering: false
  // },
  // {
  //   fieldCode: 'uniqueCode',
  //   fieldName: i18n.t('大数据平台唯一编号'),
  //   width: 200
  // },
  {
    fieldCode: 'versionNo',
    fieldName: i18n.t('版本号')
  },
  // {
  //   fieldCode: 'remarkExplain',
  //   fieldName: i18n.t('大数据平台备注')
  // },
  // {
  //   fieldCode: 'origSupplierCode',
  //   fieldName: i18n.t('上版供应商编号')
  // },
  // {
  //   fieldCode: 'origSupplierName',
  //   fieldName: i18n.t('上版供应商名称')
  // },
  {
    fieldCode: 'planDeliveryDate',
    fieldName: i18n.t('上版交货日期'),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    fieldCode: 'planDeliveryTime',
    fieldName: i18n.t('上版交货时间'),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    fieldCode: 'callMaterialQty',
    fieldName: i18n.t('上版叫料数量'),
    allowFiltering: false
  },
  {
    fieldCode: 'supRemark',
    fieldName: i18n.t('供应商备注')
  },
  {
    fieldCode: 'planGroupRemark',
    fieldName: i18n.t('计划组备注')
  },
  {
    fieldCode: 'dispatcherRemark',
    fieldName: i18n.t('叫料员备注')
  },
  {
    fieldCode: 'buyerOrgCode',
    fieldName: i18n.t('采购组编码')
  },
  {
    fieldCode: 'buyerOrgName',
    fieldName: i18n.t('采购组名称')
  },
  {
    fieldCode: 'itemCode',
    fieldName: i18n.t('物料'),
    searchOptions: {
      ...MasterDataSelect.itemSupplier,
      renameField: 'itemCode'
    }

    // itemName
    // itemId
  },
  {
    fieldCode: 'siteCode',
    fieldName: i18n.t('工厂'),
    searchOptions: {
      ...MasterDataSelect.factorySupplierAddress,
      renameField: 'siteCode'
    }
    // siteId
    // siteName
  },
  {
    fieldCode: 'warehouseCode',
    fieldName: i18n.t('库存地点')
    // warehouseName
  },
  // {
  //   fieldCode: "planGroupCode",
  //   fieldName: i18n.t("计划组"),
  //   // planGroupId
  //   // planGroupName
  // },
  {
    fieldCode: 'subSiteCode',
    fieldName: i18n.t('分厂'),
    searchOptions: {
      ...MasterDataSelect.subSiteCodeSupplier,
      url: '/srm-purchase-execute/tenant/supplierJitInfo/condition?conditionType=subSiteCode'
    }

    // subSiteName
  },
  {
    fieldCode: 'subSiteAddressCode',
    fieldName: i18n.t('分厂库存地点'),
    searchOptions: {
      ...MasterDataSelect.subSiteAddressSupplier,
      url: '/srm-purchase-execute/tenant/supplierJitInfo/condition?conditionType=subSiteAddressCode'
    }

    // subSiteAddress
  },
  {
    fieldCode: 'senderAddress',
    fieldName: i18n.t('收货信息')
    // senderAddress 送货地址
    // senderName 送货联系人
    // senderPhone 送货联系电话
  },
  // {
  //   fieldCode: "workCenterCode",
  //   fieldName: i18n.t("工作中心"),
  //   // workCenter
  //   // workCenterId
  // },
  // {
  //   fieldCode: "processorCode",
  //   fieldName: i18n.t("加工商"),
  //   // processorName
  // },
  {
    fieldCode: 'companyCode',
    fieldName: i18n.t('客户')
    // companyId
    // companyName
  },
  // {
  //   fieldCode: "saleOrder",
  //   fieldName: i18n.t("销售订单"),
  // },
  {
    fieldCode: 'supplierCode',
    fieldName: i18n.t('供应商')
    // supplierId
    // supplierName
  },
  {
    fieldCode: 'projectTextBatch',
    fieldName: i18n.t('项目文本批次')
  },
  // {
  //   fieldCode: "batch",
  //   fieldName: i18n.t("批量"),
  // },
  {
    fieldCode: 'bidNum',
    fieldName: i18n.t('需求数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    fieldCode: 'receiveNum',
    fieldName: i18n.t('收货数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    fieldCode: 'deliveryNum',
    fieldName: i18n.t('在途数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    fieldCode: 'remainingDeliveryNum',
    fieldName: i18n.t('剩余送货数量'),
    searchOptions: { elementType: 'number' }
  },
  // {
  //   fieldCode: "totalBidNum",
  //   fieldName: i18n.t("累计叫料数量"),
  // },
  // {
  //   fieldCode: "totalReceiveNum",
  //   fieldName: i18n.t("累计收货数量"),
  // },
  // {
  //   fieldCode: "totalDeliveryNum",
  //   fieldName: i18n.t("累积在途数量"),
  // },
  {
    fieldCode: 'storemanName',
    fieldName: i18n.t('仓管员'),
    allowEditing: false
  },
  {
    fieldCode: 'batchCode',
    fieldName: i18n.t('版次号'),
    allowEditing: false
  },
  {
    fieldCode: 'closePersonName', //
    fieldName: i18n.t('关闭人'),
    allowFiltering: false
  },
  {
    fieldCode: 'closeTime', //
    fieldName: i18n.t('关闭时间'),
    allowFiltering: false
  },
  {
    fieldCode: 'scheduleUserName',
    fieldName: i18n.t('调度员名称')
    // scheduleUserId
    // scheduleUserCode
  },
  {
    fieldCode: 'renewTime',
    fieldName: i18n.t('JIT更新时间'),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    fieldCode: 'updateUserName',
    fieldName: i18n.t('更新人')
  },
  {
    fieldCode: 'transferPlanCode',
    fieldName: i18n.t('转交计划员')
    // transferPlanId
    // transferPlanName
  },
  {
    fieldCode: 'createTime',
    fieldName: i18n.t('创建时间'),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    fieldCode: 'createUserName',
    fieldName: i18n.t('创建人'),
    allowFiltering: false
  }
]
