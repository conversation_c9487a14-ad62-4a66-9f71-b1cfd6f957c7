<template>
  <!-- 屏采交货计划-采方 -->
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      @reset="handleCustomReset"
      @search="handleCustomSearch"
    >
      <mt-form
        ref="searchFormRef"
        :model="searchFormModel"
        :rules="{
          bigVersionNo: [{ required: true, message: $t('请输入'), trigger: 'blur' }]
        }"
      >
        <mt-form-item prop="factoryCode" :label="$t('工厂')" label-style="top">
          <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.factoryCode"
            :url="$API.masterData.getSiteAuthFuzzyUrl"
            multiple
            :placeholder="$t('请选择工厂')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            params-key="fuzzyParam"
            records-position="data"
          ></RemoteAutocomplete>
        </mt-form-item>
        <!-- <mt-form-item prop="structSeqNos" :label="$t('结构序号')" label-style="top">
          <mt-input
            v-model="structSeqNos"
            @change="structSeqNoChange"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
          />
        </mt-form-item> -->
        <mt-form-item prop="serialNo" :label="$t('序列号')" label-style="top">
          <mt-input v-model="searchFormModel.serialNo" />
        </mt-form-item>
        <!-- <mt-form-item prop="itemGroups" :label="$t('物料组')" label-style="top">
          <div style="display: flex">
            <mt-input
              style="flex: 1"
              v-model="itemGroups"
              @change="itemGroupChange"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            />
            <mt-checkbox
              v-model="searchFormModel.joinMaterialGroup"
              @change="(e) => handleChange(e, 'joinMaterialGroup')"
              :label="$t('关联查询')"
              style="vertical-align: sub; display: inline-block; margin-left: 5px; width: 78px"
            />
          </div>
        </mt-form-item> -->
        <mt-form-item prop="itemCodes" :label="$t('物料编码')" label-style="top">
          <div style="display: flex">
            <mt-input
              style="flex: 1"
              v-model="itemCodes"
              @change="itemChange"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            />
            <!-- <mt-checkbox
              v-model="searchFormModel.joinMaterialCode"
              @change="(e) => handleChange(e, 'joinMaterialCode')"
              :label="$t('关联查询')"
              style="vertical-align: sub; display: inline-block; margin-left: 5px; width: 78px"
            /> -->
          </div>
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input v-model="searchFormModel.itemName" :placeholder="$t('支持模糊查询')"></mt-input>
        </mt-form-item>
        <mt-form-item prop="bigVersionNo" :label="$t('版本号')" label-style="top">
          <mt-select
            v-model="searchFormModel.bigVersionNo"
            :data-source="versionList"
            :placeholder="$t('请选择')"
            @change="versionNoChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.status"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :data-source="statusOptions"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="planMemberCode" :label="$t('计划员')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.planMemberCode"
            :data-source="plannerListOptions"
            :fields="{ text: 'codeAndName', value: 'planMemberCode' }"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="typeList" :label="$t('类型')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.typeList"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :data-source="[
              { text: $t('P(需求量)'), value: 'P' },
              { text: $t('C(承诺量)'), value: 'C' }
            ]"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        :row-config="{ height: rowHeight }"
        :columns="columns"
        :table-data="tableData"
        height="auto"
        header-align="left"
        align="left"
        show-overflow
        style="padding-top: unset"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true,
          activeMethod: this.activeRowMethod
        }"
        :cell-style="cellStyle"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        @edit-closed="editComplete"
        @edit-actived="editBegin"
        @edit-disabled="editDisabledEvent"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            v-permission="item.permission"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #forecastTypeDefault="{}">
          <div v-for="(item, index) in forecastTypeOptions" :key="item">
            <div v-if="index === 0" class="vxe-cell-border">
              {{ item }}
            </div>
            <div v-if="index === 1" class="vxe-cell-border">
              {{ item }}
            </div>
          </div>
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import {
  StatusSearchOptionsFilter,
  ForecastTypeDataSource,
  columnData,
  NewRowData,
  ToolBar
} from './config'
// import { utils } from '@mtech-common/utils'
import * as UTILS from '@/utils/utils'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      rowHeight: 50,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      supplierCodes: '',
      itemCodes: '',
      itemGroups: '',
      structSeqNos: '',
      addId: '1',
      toolbar: ToolBar,
      searchFormModel: {
        bigVersionNo: '',
        joinMaterialCode: false,
        joinMaterialGroup: false
      },
      titleList: [],
      tableData: [],
      columns: columnData,
      forecastPageCurrent: 1,
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      statusOptions: StatusSearchOptionsFilter,
      forecastTypeOptions: ForecastTypeDataSource,
      isEdit: false,
      versionList: [],
      plannerListOptions: [],
      warningContent: this.$t('此状态数据不可编辑'),
      typeList: ['P', 'C']
    }
  },
  mounted() {
    // this.getPlannerList()
    this.buyerGoodsDemandPlanInfoQueryVersionTv()
  },
  methods: {
    handleChange(e, labelName) {
      this.searchFormModel[labelName] = e.checked
    },
    versionNoChange(e) {
      const { value } = e
      this.getPlannerList({ bigVersionNo: value })
    },
    cellStyle({ row, column }) {
      if (column.field === 'itemCode') {
        if (row.status == 4) {
          return {
            color: 'red'
          }
        }
      }
      if (column.field === 'status') {
        if (row.status == 4) {
          return {
            color: 'red'
          }
        }
      }
    },
    buyerGoodsDemandPlanInfoQueryVersionTv() {
      this.$API.deliverySchedule.supplierGoodsDemandPlanInfoQueryVersionTv().then((res) => {
        const { data, code } = res
        if (code === 200) {
          this.versionList = data
          this.searchFormModel.bigVersionNo = data[0]
          this.getPlannerList({ bigVersionNo: data[0] })
        }
      })
    },
    // 获取计划员下拉
    getPlannerList(params) {
      this.$API.deliverySchedule.supplierGoodsDemandPlanInfoQueryPlannerTv(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.plannerListOptions = data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.planMemberCode} - ${i.plannerName}`
            }
          })
        }
      })
    },
    // 查询条件操作物料编码切割
    itemChange(e) {
      if (e) {
        this.searchFormModel.itemCodes = this.itemCodes.split(' ')
      } else {
        this.searchFormModel.itemCodes = null
      }
    },
    // 查询条件操作物料组切割
    itemGroupChange(e) {
      if (e) {
        this.searchFormModel.itemGroups = this.itemGroups.split(' ')
      } else {
        this.searchFormModel.itemGroups = null
      }
    },
    // 查询条件操作结构序号切割
    structSeqNoChange(e) {
      if (e) {
        this.searchFormModel.structSeqNos = this.structSeqNos.split(' ')
      } else {
        this.searchFormModel.structSeqNos = null
      }
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 校验点击的对象是否为取消编辑按钮
        if (args.$event.target.innerText === this.$t('取消编辑')) {
          // 清除编辑状态
          this.$refs.xTable.$refs.xGrid.clearEdit()
          this.handleCustomSearch()
          return
        }
        // 远程数据才有$event属性
        //1、 校验必填 没通过就是this.$refs.xTable.$refs.xGrid.setEditRow(row)
        // if (!this.isValidData(row)) {
        //   // 当出现错误时，指定行进入编辑状态
        //   this.$refs.xTable.$refs.xGrid.setEditRow(row)
        //   return
        // }
        //2、 判断是否有row.bigVersionNo调新增或者编辑接口
        if (row.bigVersionNo) {
          this.updateRow(row)
        } else {
          this.updateRow(row)
        }
        //3、 接口调用成功调刷新接口
      }
    },
    updateRow(row) {
      const params = {
        ...row
      }
      // console.log(this.)
      this.titleList.forEach((item, index) => {
        params.tvSupplierDemandPlanItemExtList.forEach((i) => {
          if (i.type === 'C') {
            i[`planDemandQty${index + 1}`] = row[`title_${item}`]['supplierNum'] || 0
          }
          i.dateText = ''
        })
      })
      const tvSupplierDemandPlanItemExtList = params.tvSupplierDemandPlanItemExtList.filter((i) => {
        return i.type === 'C'
      })
      this.$API.deliverySchedule
        .supplierGoodsDemandPlanInfoUpdateTv({
          ...tvSupplierDemandPlanItemExtList[0],
          supplierRemark: params.supplierRemark
        })
        .then(() => {
          this.$toast({
            content: this.$t('保存交货计划操作成功'),
            type: 'success'
          })
          this.handleCustomSearch()
        })
        .catch(() => {
          this.$refs.xTable.$refs.xGrid.setEditRow(row)
        })
    },
    editBegin(args) {
      // const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        // this.getSupplierDataSource({ value: row.supplierCode, type: 'supplierCode' })
      } else {
        // this.getSupplierDataSource({ value: '', type: 'supplierCode' })
      }
      this.isEdit = true
    },
    activeRowMethod({ row }) {
      // 已确认的数据不可修改
      if (!(row.status === 0 || row.status === 1 || row.status === 2)) {
        this.warningContent = this.$t('此状态数据不可编辑')
        return false
      }
      // const { externalCode } = JSON.parse(sessionStorage.getItem('userInfo'))
      // if (row.planMemberCode !== externalCode) {
      //   this.warningContent = this.$t('选中的数据为其他计划员的数据，无法编辑')
      //   return false
      // }
      return true
    },
    editDisabledEvent() {
      this.$toast({
        content: this.warningContent,
        type: 'warning'
      })
      this.warningContent = this.$t('此状态数据不可编辑')
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectRecords = $grid.getCheckboxRecords()
      if (code === 'CloseEdit') {
        // 清除编辑状态
        this.$refs.xTable.$refs.xGrid.clearEdit()
        this.handleCustomSearch()
        return
      }
      if (this.isEdit) {
        // this.$toast({ content: this.$t('请先完成或结束编辑操作'), type: 'warning' })
        return
      }

      const params = {
        queryRulesReq: { ...this.searchFormModel, page: { current: 1, size: 999999 } },
        chooseOperateReqs: selectRecords.map((i) => {
          return {
            id: i.id,
            supplierCode: i.supplierCode,
            status: i.status,
            bigVersionNo: i.bigVersionNo,
            line: i.lineNo,
            md5Code: i.md5Code,
            sameBatchData: i.sameBatchData,
            supplierTenantId: i.supplierTenantId,
            supplierName: i.supplierName,
            plannerName: i.plannerName,
            planMemberCode: i.planMemberCode,
            serialNo: i.serialNo
          }
        })
      }

      if (code === 'Add') {
        // 新增
        const currentViewRecords = $grid.getTableData().visibleData
        if (!currentViewRecords.length) {
          this.$toast({
            content: this.$t('请先查询数据再进行新增操作'),
            type: 'warning'
          })
          return
        }
        this.titleList.forEach((itemTitle) => {
          const forecastItem = {
            buyerNum: null,
            supplierNum: null
          }
          NewRowData[`title_${itemTitle}`] = forecastItem
        })
        // 新增一行
        $grid.insert([NewRowData])
        this.$nextTick(() => {
          // 获取最新的表格视图数据
          const currentViewRecords = $grid.getTableData().visibleData
          // 将新增的那一条设置为编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow(currentViewRecords[0])
        })
      } else if (code === 'Import') {
        this.handleImport()
      } else if (code === 'Export1') {
        this.handleExport('isAllowImport')
      } else if (code === 'Export') {
        this.handleExport()
      } else if (code === 'Feedback') {
        this.handleFeedback(params)
      }
    },
    handleFeedback(params) {
      // 反馈交货计划
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认反馈交货计划吗?')
        },
        success: () => {
          this.$API.deliverySchedule.supplierGoodsDemandPlanInfoFeedbackTv(params).then((res) => {
            const { code, msg } = res
            if (code === 200) {
              this.$toast({
                content: this.$t('反馈交货计划操作成功'),
                type: 'success'
              })
              this.handleCustomSearch()
            } else {
              this.$toast({
                content: msg,
                type: 'warning'
              })
            }
          })
        }
      })
    },
    handleImport() {
      //导入
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.deliverySchedule.supplierGoodsDemandPlanInfoImportTv,
          downloadTemplateApi: this.$API.deliverySchedule.buyerGoodsDemandPlanInfoExportTempTv,
          paramsKey: 'excel',
          asyncParams: {
            bigVersionNo: this.searchFormModel.bigVersionNo
          },
          downloadTemplateParams: { bigVersionNo: this.searchFormModel.bigVersionNo }
        },
        success: () => {
          // 导入之后刷新列表
          this.handleCustomSearch()
        }
      })
    },
    handleExport(isAllowImport) {
      const { visibleColumn } = this.$refs.xTable.$refs.xGrid.getTableColumn()
      const headerMap = {}
      let apiName = 'supplierGoodsDemandPlanInfoExportTv'
      let sortNumber = 0
      if (!isAllowImport) {
        visibleColumn.forEach((i) => {
          if (i.field && i.title) {
            if (i.field === 'forecastType') {
              headerMap['type'] = i.title
            } else if (i.field.includes('title_')) {
              sortNumber++
              headerMap[`planDate${sortNumber}`] = i.title
            } else {
              headerMap[i.field] = i.title
            }
          }
        })
        apiName = 'supplierGoodsDemandPlanInfoExportDynamicTv'
      }
      //导出
      const params = {
        page: {
          size: 1000,
          current: 1
        },
        headerMap,
        ...this.searchFormModel
      }
      this.apiStartLoading()
      this.$API.deliverySchedule[apiName](params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = UTILS.getHeadersFileName(res)
          UTILS.download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    // 校验数据
    isValidData(data) {
      const { materialCode, factoryCode } = data
      let valid = false
      if (!factoryCode) {
        // 工厂代码
        this.$toast({ content: this.$t('工厂不可为空'), type: 'warning' })
      } else if (!materialCode) {
        // 物料代码
        this.$toast({ content: this.$t('物料不可为空'), type: 'warning' })
        // } else if (!supplierCode) {
        //   // 供应商代码
        //   this.$toast({ content: this.$t('供应商不可为空'), type: 'warning' })
      } else {
        valid = true
      }

      return valid
    },
    // 重置查询条件
    handleCustomReset() {
      this.itemCodes = null
      this.searchFormModel.itemCodes = null
      this.itemGroups = null
      this.searchFormModel.itemGroups = null
      this.structSeqNos = null
      this.searchFormModel.structSeqNos = null
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'bigVersionNo') {
            this.searchFormModel[key] = this.versionList[0]
          }
        }
      }
      this.searchFormModel.joinMaterialCode = false
      this.searchFormModel.joinMaterialGroup = false
      this.forecastPageCurrent = 1
      this.handleCustomSearch()
    },
    // 获取列表数据并组装表头
    handleCustomSearch() {
      this.$refs.searchFormRef.validate(() => {})
      this.isEdit = false
      const params = {
        ...this.searchFormModel,
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        }
      }
      if (
        !this.searchFormModel?.typeList ||
        this.searchFormModel?.typeList?.length === 0 ||
        this.searchFormModel?.typeList?.length === 2
      ) {
        this.typeList = ['P', 'C']
        this.rowHeight = 50
      } else if (this.searchFormModel?.typeList?.length === 1) {
        this.typeList = this.searchFormModel?.typeList
        this.rowHeight = 26
      }
      this.apiStartLoading()
      this.$API.deliverySchedule
        .supplierGoodsDemandPlanInfoQueryTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.supplierDemandPlanPage?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.totalRecordsCount = Number(total)
            const records = res?.data?.supplierDemandPlanPage?.records || [] // 表格数据
            const titleList = res?.data?.titleList || [] // 动态表头数据
            this.titleList = titleList // 动态表头数据
            // 处理表头数据
            this.columns = this.handleColumns({ titleList })
            // 处理表数据
            this.tableData = this.handleDataSource({ records, titleList })
            if (!this.searchFormModel.bigVersionNo) {
              // 处理表头数据
              this.columns = []
              // 处理表数据
              this.tableData = []
              this.forecastPageSettings.totalPages = 1
            }
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleColumns(data) {
      const { titleList } = data
      // 固定的表头
      const constantColumns = columnData
      // 动态的日期表头
      const titleListColumnData = []
      // const month =
      //   `${new Date().getMonth() + 1}`.length === 2
      //     ? new Date().getMonth() + 1
      //     : '0' + (new Date().getMonth() + 1)
      // const todayStr = `${new Date().getFullYear()}${month}${new Date().getDate()}`
      titleList.forEach((item, index) => {
        const title = `title_${titleList[index]}`
        titleListColumnData.push({
          field: title,
          title: item,
          minWidth: 125,
          showOverflow: true,
          editRender: {},
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <div>
                  <div v-show={this.typeList.includes('P')} class='vxe-cell-border'>
                    {row[title]['buyerNum']}
                  </div>
                  <div v-show={this.typeList.includes('C')} class='vxe-cell-border'>
                    {row[title]['supplierNum']}
                  </div>
                </div>
              ]
            },
            edit: ({ row }) => {
              return [
                <div>
                  <div v-show={this.typeList.includes('P')} class='vxe-cell-border'>
                    {row[title]['buyerNum']}
                  </div>
                  <div v-show={this.typeList.includes('C')} class='vxe-cell-border'>
                    <mt-input-number
                      show-spin-button={false}
                      show-clear-button={false}
                      v-model={row[title]['supplierNum']}
                      v-show={!title.includes('W')}
                    />
                    <span v-show={title.includes('W')}>{row[title]['supplierNum']}</span>
                  </div>
                </div>
              ]
            }
          }
        })
      })

      // 合并表头数组，然后 push 到 forecastColumnData 中
      const columns = [].concat(constantColumns).concat(titleListColumnData)
      return columns
    },
    handleDataSource(args) {
      const { records, titleList } = args
      records.forEach((obj, idx) => {
        obj.addId = this.addId++
        obj.lineNo = idx + 1
        if (obj.tvSupplierDemandPlanItemExtList && obj.tvSupplierDemandPlanItemExtList.length > 0) {
          titleList.forEach((title, index) => {
            let buyerNum = ''
            let supplierNum = ''
            obj.tvSupplierDemandPlanItemExtList.forEach((itm) => {
              if (!itm) {
                buyerNum = ''
                supplierNum = ''
              }
              if (itm.type === 'P') {
                buyerNum = itm[`planDemandQty${index + 1}`]
              }
              if (itm.type === 'C') {
                supplierNum = itm[`planDemandQty${index + 1}`]
              }
            })
            obj[`title_${title}`] = {
              buyerNum,
              supplierNum
            }
          })
        }
      })
      return records
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  },
  beforeRouteLeave(to, from, next) {
    document.querySelector('.vxe-table--body-wrapper').scrollTop = 0
    next()
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  div .vxe-cell-border {
    border-bottom: solid #e6e9ed 1px;
    height: 24px;
    box-sizing: border-box;
  }
}

/deep/ .vxe-cell .vxe-default-select {
  background: #fff;
}
/deep/ .col--seq,
/deep/ .col--checkbox {
  .c--tooltip {
    padding: 0 10px;
  }
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
