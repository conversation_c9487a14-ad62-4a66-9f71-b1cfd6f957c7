<template>
  <div class="full-height vertical-flex-box">
    <div class="top-info flex-keep">
      <div class="header-box">
        <div class="middle-blank"></div>
        <!-- 右侧操作按钮 -->
        <mt-button css-class="e-flat" :is-primary="true" @click="goBack">
          {{ $t('返回') }}
        </mt-button>
      </div>
    </div>
    <div>
      <collapse-search
        class="toggle-container"
        :is-grid-display="true"
        @reset="handleReset"
        @search="handleSearch"
      >
        <mt-form ref="searchFormRef" :model="searchFormModel">
          <mt-form-item :label="$t('工厂编码')" prop="siteCodeList">
            <RemoteAutocomplete
              v-model="searchFormModel.siteCodeList"
              :url="$API.masterData.getSiteListUrl"
              multiple
              :placeholder="$t('请选择')"
              :fields="{ text: 'siteName', value: 'siteCode' }"
              :search-fields="['siteName', 'siteCode']"
            />
          </mt-form-item>
          <mt-form-item :label="$t('物料编码')" prop="materialCode">
            <mt-input
              v-model="materialCode"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              :show-clear-button="true"
              @change="(e) => onChange(e, 'materialCode')"
            />
          </mt-form-item>
          <mt-form-item :label="$t('供应商编码')" prop="supplierCode">
            <mt-input
              v-model="supplierCode"
              :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              :show-clear-button="true"
              @change="(e) => onChange(e, 'supplierCode')"
            />
          </mt-form-item>
          <!-- <mt-form-item :label="$t('需求日期')" prop="demandDateList">
            <mt-multi-select
              v-model="searchFormModel.demandDateList"
              :show-select-all="true"
              :allow-filtering="true"
              filter-type="Contains"
              :data-source="demandDateOptions"
              :placeholder="$t('请选择')"
            />
          </mt-form-item> -->
        </mt-form>
      </collapse-search>
      <sc-table
        ref="scTableRef"
        grid-id="5405b369-4353-48da-9224-7ad8ee634656"
        :loading="loading"
        :is-show-refresh-bth="true"
        :columns="columns"
        :table-data="tableData"
        @refresh="handleSearch"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :loading="item.loading"
            size="small"
            @click="handleClickToolBar(item)"
          >
            {{ item.name }}
          </vxe-button>
        </template>
        <template #typeDefault>
          <div>
            {{ $t('P(需求量)') }}
          </div>
        </template>
      </sc-table>
      <mt-page
        ref="pageRef"
        class="flex-keep custom-page"
        :page-settings="pageSettings"
        :total-pages="pageSettings.totalPages"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
      />
    </div>
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { historyColumnData } from '../config'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  components: { CollapseSearch, ScTable },
  data() {
    const { deliveryPlanNo } = this.$route.query
    return {
      deliveryPlanNo,
      searchFormModel: {
        deliveryPlanNo: null
      },
      toolbar: [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: historyColumnData,
      loading: false,
      tableData: [],

      materialCode: null,
      supplierCode: null,
      demandDateOptions: []
    }
  },
  mounted() {
    // this.getDemandDate()
    this.getTableData()
  },
  methods: {
    goBack() {
      this.$router.push({
        name: 'distribution-schedule'
      })
    },
    getDemandDate() {
      this.$API.deliverySchedule.getDemandDateDistributionDemanPlandApi().then((res) => {
        console.log(res)
        if (res.code === 200) {
          this.demandDateOptions = res.data.map((item) => {
            return {
              text: item,
              value: item
            }
          })
        }
      })
    },
    onChange(e, field) {
      if (e) {
        this.searchFormModel[`${field}List`] = this[field].split(' ')
      } else {
        this.searchFormModel[`${field}List`] = null
        this[field] = null
      }
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.materialCode = null
      this.supplierCode = null
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      this.searchFormModel.deliveryPlanNo = this.deliveryPlanNo
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.deliverySchedule
        .pageHistoryDistributionDemanPlandApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.distributionDemandPlanDtoPage?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.distributionDemandPlanDtoPage?.records || []
        const titleList = res?.data?.titleList || []
        this.columns = this.handleColumns({ titleList })
        this.tableData = records
      }
    },
    handleColumns(data) {
      const { titleList } = data
      // 固定的表头
      const constantColumns = historyColumnData
      // 动态的日期表头
      const titleListColumnData = []
      titleList.forEach((item, index) => {
        const title = `title_${titleList[index]}`
        titleListColumnData.push({
          field: title,
          title: item,
          minWidth: 120,
          showOverflow: true,
          editRender: {},
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <div>
                  <div>{row['demandQtyMap'][item]}</div>
                </div>
              ]
            }
          }
        })
      })

      const columns = [].concat(constantColumns).concat(titleListColumnData)
      return columns
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleExport(e) {
      let params = {
        page: { current: 1, size: 99999999 },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.deliverySchedule
        .exportHistoryDistributionDemanPlandApi(params)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  transition: all 0.5s ease-in-out;

  .header-box {
    border-bottom: 1px solid #e6e9ed;
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
  }
  .title {
    margin: 24px 0px 8px 24px;
    padding-left: 8px;
    border-left: 4px solid #3369ac;
    font-weight: 600;
  }
}
</style>
