<!-- 集散中心交货计划 -->
<template>
  <mt-template-page
    ref="templateRef"
    :template-config="pageConfig"
    :current-tab="currentTab"
    @handleSelectTab="handleSelectTab"
  >
    <List slot="slot-0" @pushSuccess="handleJump" />
    <Detail slot="slot-1" />
  </mt-template-page>
</template>

<script>
export default {
  components: {
    List: () => import('./pages/List.vue'),
    Detail: () => import('./pages/Detail.vue')
  },
  data() {
    return {
      pageConfig: [
        { title: this.$t('集散中心交货计划') },
        { title: this.$t('集散中心交货计划-已下发TMS') }
      ],
      currentTab: 0
    }
  },
  methods: {
    handleJump() {
      this.currentTab = 1
    },
    handleSelectTab(e) {
      this.currentTab = e
    }
  }
}
</script>
