import { i18n } from '@/main.js'
import dayjs from 'dayjs'

export const statusOptions = [
  { label: i18n.t('新建'), value: 0 },
  { label: i18n.t('已修改'), value: 1 },
  { label: i18n.t('已发布'), value: 2 },
  { label: i18n.t('反馈-满足'), value: 3 },
  { label: i18n.t('反馈-不满足'), value: 4 },
  { label: i18n.t('已确认'), value: 5 },
  { label: i18n.t('已关闭'), value: 6 }
]

export const outsourceMethodOptions = [
  { label: i18n.t('标准委外'), value: '0' },
  { label: i18n.t('销售委外'), value: '1' },
  { label: i18n.t('非委外'), value: '2' }
]

export const deliveryMethodOptions = [
  { label: i18n.t('直送'), value: '0' },
  { label: i18n.t('非直送'), value: '1' }
]

export const typeOptions = [
  { text: i18n.t('D(原始需求)'), value: 'D' },
  { text: i18n.t('P(需求量)'), value: 'P' },
  { text: i18n.t('C(承诺量)'), value: 'C' }
]

export const deliverTypeOptions = [
  { label: i18n.t('采购订单'), value: 1 },
  { label: i18n.t('交货计划'), value: 2 },
  { label: i18n.t('JIT'), value: 3 },
  { label: i18n.t('无需求'), value: 4 },
  { label: i18n.t('VMI'), value: 5 },
  { label: i18n.t('钢材'), value: 6 }
]

export const isJitOptions = [
  { label: i18n.t('否'), value: 0 },
  { label: i18n.t('是'), value: 1 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    minWidth: 100,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.label : ''
    }
  },
  {
    field: 'plannerName',
    title: i18n.t('计划员'),
    minWidth: 100
  },
  {
    field: 'factoryCode',
    title: i18n.t('工厂编码'),
    minWidth: 100
  },
  {
    field: 'factoryName',
    title: i18n.t('工厂名称'),
    minWidth: 200
  },
  // {
  //   field: 'mrpArea',
  //   title: i18n.t('MRP区域'),
  //   minWidth: 100
  // },
  {
    field: 'itemCode',
    title: i18n.t('物料编码'),
    minWidth: 120
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称'),
    minWidth: 200
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码'),
    minWidth: 120
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 200
  },
  // {
  //   field: 'outsourceMethod',
  //   title: i18n.t('委外方式'),
  //   minWidth: 120,
  //   formatter: ({ cellValue }) => {
  //     let item = outsourceMethodOptions.find((item) => item.value === cellValue)
  //     return item ? item.label : ''
  //   }
  // },
  // {
  //   field: 'deliveryMethod',
  //   title: i18n.t('配送方式'),
  //   minWidth: 120,
  //   formatter: ({ cellValue }) => {
  //     let item = deliveryMethodOptions.find((item) => item.value === cellValue)
  //     return item ? item.label : ''
  //   }
  // },
  {
    field: 'invAddrCode',
    title: i18n.t('库存地点'),
    minWidth: 120
  },
  {
    field: 'invAddrName',
    title: i18n.t('库存地点名称'),
    minWidth: 120
  },
  // {
  //   field: 'plusBizCirclesCode',
  //   title: i18n.t('加工商编码'),
  //   minWidth: 120
  // },
  // {
  //   field: 'plusBizCirclesName',
  //   title: i18n.t('加工商名称'),
  //   minWidth: 120
  // },
  // {
  //   field: 'projectTextBatch',
  //   title: i18n.t('项目文本批次'),
  //   minWidth: 120
  // },
  // {
  //   field: 'salesOrderNo',
  //   title: i18n.t('销售订单号'),
  //   minWidth: 120
  // },
  // {
  //   field: 'lineBody',
  //   title: i18n.t('线体'),
  //   minWidth: 100
  // },
  // {
  //   field: 'flag',
  //   title: i18n.t('标识'),
  //   minWidth: 100
  // },
  // {
  //   field: 'workCenterCode',
  //   title: i18n.t('工作中心编码'),
  //   minWidth: 120
  // },
  // {
  //   field: 'workCenterName',
  //   title: i18n.t('工作中心名称'),
  //   minWidth: 120
  // },
  // {
  //   field: 'batchQty',
  //   title: i18n.t('批量'),
  //   minWidth: 100
  // },
  // {
  //   field: 'bomNo',
  //   title: i18n.t('BOM号'),
  //   minWidth: 120
  // },
  {
    field: 'purchaserRemark',
    title: i18n.t('采购方备注'),
    minWidth: 120
  },
  {
    field: 'supplierRemark',
    title: i18n.t('供应商备注'),
    minWidth: 120
  },
  // {
  //   field: 'purchaseOrderNo',
  //   title: i18n.t('采购订单'),
  //   minWidth: 120
  // },
  {
    field: 'bigVersionNo',
    title: i18n.t('版本号'),
    minWidth: 120
  },
  // {
  //   field: 'deliverType',
  //   title: i18n.t('送货类型'),
  //   minWidth: 120,
  //   formatter: ({ cellValue }) => {
  //     let item = deliverTypeOptions.find((item) => item.value === cellValue)
  //     return item ? item.label : ''
  //   }
  // },
  // {
  //   field: 'releaseFlag',
  //   title: i18n.t('下达'),
  //   minWidth: 120,
  //   formatter: ({ cellValue }) => {
  //     return cellValue !== 'N' ? i18n.t('是') : i18n.t('否')
  //   }
  // },
  {
    field: 'unclearOrderQty',
    title: i18n.t('未清PO'),
    minWidth: 120
  },
  {
    field: 'invQty',
    title: i18n.t('库存量'),
    minWidth: 120
  },
  // {
  //   field: 'limitQty',
  //   title: i18n.t('限量数量'),
  //   minWidth: 120
  // },
  // {
  //   field: 'structSeqNo',
  //   title: i18n.t('结构序号'),
  //   minWidth: 120
  // },
  // {
  //   field: 'isJit',
  //   title: i18n.t('是否JIT'),
  //   minWidth: 120,
  //   formatter: ({ cellValue }) => {
  //     let item = isJitOptions.find((item) => item.value === cellValue)
  //     return item ? item.label : ''
  //   }
  // },
  {
    field: 'type',
    title: i18n.t('类型'),
    minWidth: 120,
    slots: {
      default: 'typeDefault'
    }
  }
]

export const detailColumnData = [
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left'
  },
  {
    field: 'deliveryPlanNo',
    title: i18n.t('排期推送日期'),
    wdith: 160,
    slots: {
      default: 'deliveryPlanNoDefault'
    }
  },
  {
    field: 'createTime',
    title: i18n.t('推送时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue ? dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss') : ''
    }
  }
]

export const historyColumnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'deliveryPlanNo',
    title: i18n.t('排期推送日期'),
    minWidth: 160
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    minWidth: 100,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.label : ''
    }
  },
  {
    field: 'plannerName',
    title: i18n.t('计划员'),
    minWidth: 100
  },
  {
    field: 'factoryCode',
    title: i18n.t('工厂编码'),
    minWidth: 100
  },
  {
    field: 'factoryName',
    title: i18n.t('工厂名称'),
    minWidth: 200
  },
  // {
  //   field: 'mrpArea',
  //   title: i18n.t('MRP区域'),
  //   minWidth: 100
  // },
  {
    field: 'itemCode',
    title: i18n.t('物料编码'),
    minWidth: 120
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称'),
    minWidth: 200
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码'),
    minWidth: 120
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 200
  },
  // {
  //   field: 'outsourceMethod',
  //   title: i18n.t('委外方式'),
  //   minWidth: 120,
  //   formatter: ({ cellValue }) => {
  //     let item = outsourceMethodOptions.find((item) => item.value === cellValue)
  //     return item ? item.label : ''
  //   }
  // },
  // {
  //   field: 'deliveryMethod',
  //   title: i18n.t('配送方式'),
  //   minWidth: 120,
  //   formatter: ({ cellValue }) => {
  //     let item = deliveryMethodOptions.find((item) => item.value === cellValue)
  //     return item ? item.label : ''
  //   }
  // },
  {
    field: 'invAddrCode',
    title: i18n.t('库存地点'),
    minWidth: 120
  },
  {
    field: 'invAddrName',
    title: i18n.t('库存地点名称'),
    minWidth: 120
  },
  // {
  //   field: 'plusBizCirclesCode',
  //   title: i18n.t('加工商编码'),
  //   minWidth: 120
  // },
  // {
  //   field: 'plusBizCirclesName',
  //   title: i18n.t('加工商名称'),
  //   minWidth: 120
  // },
  // {
  //   field: 'projectTextBatch',
  //   title: i18n.t('项目文本批次'),
  //   minWidth: 120
  // },
  // {
  //   field: 'salesOrderNo',
  //   title: i18n.t('销售订单号'),
  //   minWidth: 120
  // },
  // {
  //   field: 'lineBody',
  //   title: i18n.t('线体'),
  //   minWidth: 100
  // },
  // {
  //   field: 'flag',
  //   title: i18n.t('标识'),
  //   minWidth: 100
  // },
  // {
  //   field: 'workCenterCode',
  //   title: i18n.t('工作中心编码'),
  //   minWidth: 120
  // },
  // {
  //   field: 'workCenterName',
  //   title: i18n.t('工作中心名称'),
  //   minWidth: 120
  // },
  // {
  //   field: 'batchQty',
  //   title: i18n.t('批量'),
  //   minWidth: 100
  // },
  // {
  //   field: 'bomNo',
  //   title: i18n.t('BOM号'),
  //   minWidth: 120
  // },
  {
    field: 'purchaserRemark',
    title: i18n.t('采购方备注'),
    minWidth: 120
  },
  {
    field: 'supplierRemark',
    title: i18n.t('供应商备注'),
    minWidth: 120
  },
  // {
  //   field: 'purchaseOrderNo',
  //   title: i18n.t('采购订单'),
  //   minWidth: 120
  // },
  {
    field: 'bigVersionNo',
    title: i18n.t('版本号'),
    minWidth: 120
  },
  // {
  //   field: 'deliverType',
  //   title: i18n.t('送货类型'),
  //   minWidth: 120,
  //   formatter: ({ cellValue }) => {
  //     let item = deliverTypeOptions.find((item) => item.value === cellValue)
  //     return item ? item.label : ''
  //   }
  // },
  // {
  //   field: 'releaseFlag',
  //   title: i18n.t('下达'),
  //   minWidth: 120,
  //   formatter: ({ cellValue }) => {
  //     return cellValue !== 'N' ? i18n.t('是') : i18n.t('否')
  //   }
  // },
  {
    field: 'unclearOrderQty',
    title: i18n.t('未清PO'),
    minWidth: 120
  },
  {
    field: 'invQty',
    title: i18n.t('库存量'),
    minWidth: 120
  },
  // {
  //   field: 'limitQty',
  //   title: i18n.t('限量数量'),
  //   minWidth: 120
  // },
  // {
  //   field: 'structSeqNo',
  //   title: i18n.t('结构序号'),
  //   minWidth: 120
  // },
  // {
  //   field: 'isJit',
  //   title: i18n.t('是否JIT'),
  //   minWidth: 120,
  //   formatter: ({ cellValue }) => {
  //     let item = isJitOptions.find((item) => item.value === cellValue)
  //     return item ? item.label : ''
  //   }
  // },
  {
    field: 'type',
    title: i18n.t('类型'),
    minWidth: 120,
    slots: {
      default: 'typeDefault'
    }
  }
]
