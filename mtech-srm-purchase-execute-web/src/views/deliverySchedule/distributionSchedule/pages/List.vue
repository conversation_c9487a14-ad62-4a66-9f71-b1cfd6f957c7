<template>
  <div class="full-height">
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <!-- <mt-form-item :label="$t('公司编码')" prop="companyCode">
          <RemoteAutocomplete
            v-model="searchFormModel.companyCode"
            url="/masterDataManagement/auth/company/auth-fuzzy"
            :params="{
              organizationLevelCodes: ['ORG02', 'ORG01'],
              orgType: 'ORG001PRO',
              includeItself: true
            }"
            :placeholder="$t('请选择')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            records-position="data"
          />
        </mt-form-item> -->
        <mt-form-item :label="$t('工厂编码')" prop="siteCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.siteCodeList"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料编码')" prop="materialCode">
          <mt-input
            v-model="materialCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
            @change="(e) => onChange(e, 'materialCode')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供应商编码')" prop="supplierCode">
          <mt-input
            v-model="supplierCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
            @change="(e) => onChange(e, 'supplierCode')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('需求日期')" prop="demandDateList">
          <mt-multi-select
            v-model="searchFormModel.demandDateList"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :data-source="demandDateOptions"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('类型')" prop="typeList">
          <mt-multi-select
            v-model="searchFormModel.typeList"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :data-source="typeOptions"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('版本号')" prop="bigVersionNos">
          <mt-multi-select
            v-model="searchFormModel.bigVersionNos"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :data-source="versionOptions"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('状态')" prop="status">
          <mt-multi-select
            v-model="searchFormModel.status"
            :show-select-all="true"
            :allow-filtering="true"
            filter-type="Contains"
            :data-source="statusOptions"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="58f68ad9-6ba4-42cf-acee-cb2946a7e683"
      :row-config="{ height: rowHeight }"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #typeDefault>
        <div v-for="(item, index) in typeOptions" :key="index">
          <div v-if="item.value === 'D' && typeList.includes('D')" class="vxe-cell-border">
            {{ item.text }}
          </div>
          <div v-if="item.value === 'P' && typeList.includes('P')" class="vxe-cell-border">
            {{ item.text }}
          </div>
          <div v-if="item.value === 'C' && typeList.includes('C')" class="vxe-cell-border">
            {{ item.text }}
          </div>
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, typeOptions } from '../config'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  components: { CollapseSearch, ScTable },
  data() {
    return {
      rowHeight: 96,
      searchFormModel: {
        // companyCode: '0602'
        typeList: ['P']
      },
      toolbar: [
        { code: 'push', name: this.$t('推送TMS'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
        { code: 'rollback', name: this.$t('回滚'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      materialCode: null,
      supplierCode: null,
      demandDateOptions: [],
      typeOptions,
      typeList: ['P'],

      versionOptions: [],
      statusOptions: [
        { text: this.$t('已发布'), value: 2 },
        { text: this.$t('反馈-满足'), value: 3 },
        { text: this.$t('反馈-不满足'), value: 4 },
        { text: this.$t('已确认'), value: 5 }
      ]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getVersion()
    this.getDemandDate()
    this.handleSearch()
  },
  methods: {
    getVersion() {
      this.$API.deliverySchedule.getVersionDistributionDemanPlandApi().then((res) => {
        if (res.code === 200) {
          this.versionOptions = res.data.map((item) => {
            return {
              text: item,
              value: item
            }
          })
        }
      })
    },
    getDemandDate() {
      this.$API.deliverySchedule.getDemandDateDistributionDemanPlandApi().then((res) => {
        if (res.code === 200) {
          this.demandDateOptions = res.data.map((item) => {
            return {
              text: item,
              value: item
            }
          })
        }
      })
    },
    onChange(e, field) {
      if (e) {
        this.searchFormModel[`${field}List`] = this[field].split(' ')
      } else {
        this.searchFormModel[`${field}List`] = null
        this[field] = null
      }
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.searchFormModel.typeList = ['P']
      this.typeList = ['P']
      this.materialCode = null
      this.supplierCode = null
      this.handleSearch()
    },
    handleSearch() {
      if (
        !this.searchFormModel?.typeList ||
        this.searchFormModel?.typeList?.length === 0 ||
        this.searchFormModel?.typeList?.length === 3
      ) {
        this.typeList = ['D', 'P', 'C']
        this.rowHeight = 96
      } else if (this.searchFormModel?.typeList?.length === 2) {
        this.typeList = this.searchFormModel?.typeList
        this.rowHeight = 64
      } else if (this.searchFormModel?.typeList?.length === 1) {
        this.typeList = this.searchFormModel?.typeList
        this.rowHeight = 32
      }
      if (this.searchFormModel?.typeList?.length === 0) {
        this.searchFormModel.typeList = ['P']
        this.typeList = this.searchFormModel?.typeList
        this.rowHeight = 32
      }
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.deliverySchedule
        .pageDistributionDemanPlandApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.buyerDemandPlanPage?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.buyerDemandPlanPage?.records || []
        const titleList = res?.data?.titleList || []
        this.columns = this.handleColumns({ titleList })
        this.tableData = this.handleDataSource({ records, titleList })
      }
    },
    handleColumns(data) {
      const { titleList } = data
      // 固定的表头
      const constantColumns = columnData
      // 动态的日期表头
      const titleListColumnData = []
      titleList.forEach((item, index) => {
        const title = `title_${titleList[index]}`
        titleListColumnData.push({
          field: title,
          title: item,
          minWidth: 120,
          showOverflow: true,
          editRender: {},
          className: 'vxe-table-multi-cell',
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return [
                <div>
                  <div v-show={this.typeList.includes('D')} class='vxe-cell-border'>
                    {row[title]['numD']}
                  </div>
                  <div v-show={this.typeList.includes('P')} class='vxe-cell-border'>
                    {row[title]['numP']}
                  </div>
                  <div v-show={this.typeList.includes('C')} class='vxe-cell-border'>
                    {row[title]['numC']}
                  </div>
                </div>
              ]
            }
          }
        })
      })

      const columns = [].concat(constantColumns).concat(titleListColumnData)
      return columns
    },
    handleDataSource(args) {
      const { records, titleList } = args
      records.forEach((obj) => {
        if (obj.tvBuyerDemandPlanExtMapList && obj.tvBuyerDemandPlanExtMapList.length > 0) {
          titleList.forEach((title) => {
            let numD = ''
            let numP = ''
            let numC = ''
            obj.tvBuyerDemandPlanExtMapList.forEach((item) => {
              if (!item) {
                numD = ''
                numP = ''
                numC = ''
              }
              if (item.type === 'D') {
                numD = item[title]
              }
              if (item.type === 'P') {
                numP = item[title]
              }
              if (item.type === 'C') {
                numC = item[title]
              }
            })
            obj[`title_${title}`] = {
              numD,
              numP,
              numC
            }
          })
        }
      })
      return records
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      switch (e.code) {
        case 'push':
          this.handlePushTms(selectedRecords)
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        case 'rollback':
          this.handleRollback()
          break
        default:
          break
      }
    },
    handlePushTms(selectedRecords) {
      let params = {
        queryReq: {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        },
        chooseReqList: []
      }
      if (selectedRecords.length === 0) {
        params.queryReq.typeList = ['P']
      } else {
        selectedRecords.forEach((item) => {
          let obj = {
            id: item.id,
            factoryCode: item.factoryCode,
            factoryName: item.factoryName,
            supplierCode: item.supplierCode,
            supplierName: item.supplierName,
            itemCode: item.itemCode,
            itemName: item.itemName,
            versionNo: item.bigVersionNo,
            quantities: {}
          }
          this.searchFormModel?.demandDateList || this.demandDateOptions
          if (this.searchFormModel?.demandDateList) {
            this.searchFormModel?.demandDateList.forEach((title) => {
              obj.quantities[title] = item[`title_${title}`]?.numP
            })
          } else {
            this.demandDateOptions.forEach((ele) => {
              obj.quantities[ele.value] = item[`title_${ele.value}`]?.numP
            })
          }
          params.chooseReqList.push(obj)
        })
      }
      this.$API.deliverySchedule.pushTmsDistributionDemanPlandApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleSearch()
          this.$emit('pushSuccess')
        }
      })
    },
    handleExport(e) {
      let params = {
        page: { current: 1, size: 99999999 },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.deliverySchedule
        .exportDistributionDemanPlandApi(params)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    handleDelete(selectedRecords) {
      let params = {
        queryReq: {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        },
        chooseReqList: []
      }
      if (selectedRecords.length === 0) {
        params.queryReq.typeList = ['P']
      } else {
        selectedRecords.forEach((item) => {
          let obj = {
            id: item.id
          }
          params.chooseReqList.push(obj)
        })
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除？')
        },
        success: () => {
          this.$API.deliverySchedule.deleteDistributionDemanPlandApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.handleSearch()
            }
          })
        }
      })
    },
    handleRollback() {
      this.$API.deliverySchedule.rollbackDistributionDemanPlandApi().then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleSearch()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  div .vxe-cell-border {
    border: solid #e6e9ed 1px;
    height: 32px;
    line-height: 32px;
    box-sizing: border-box;
  }
}

/deep/ .vxe-cell .vxe-default-select {
  background: #fff;
}
</style>
