<template>
  <div class="full-height">
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('排期推送日期')" prop="deliveryPlanNo">
          <mt-input
            v-model="searchFormModel.deliveryPlanNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('推送时间')" prop="createTime">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :show-clear-button="true"
            :open-on-focus="true"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            @change="(e) => dateChange(e, 'createTime')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template #deliveryPlanNoDefault="{ row }">
        <div class="able-click-field" @click="deliveryPlanNoClick(row)">
          {{ row.deliveryPlanNo }}
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { detailColumnData } from '../config'
export default {
  components: { CollapseSearch, ScTable },
  data() {
    return {
      searchFormModel: {},
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: detailColumnData,
      loading: false,
      tableData: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    deliveryPlanNoClick(row) {
      this.$router.push({
        name: 'distribution-schedule-detail',
        query: {
          deliveryPlanNo: row.deliveryPlanNo
        }
      })
    },
    dateChange(e, prefix) {
      if (e.startDate) {
        this.searchFormModel[prefix + 'Start'] = dayjs(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        ).valueOf()
        this.searchFormModel[prefix + 'End'] = dayjs(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        ).valueOf()
      } else {
        this.searchFormModel[prefix + 'Start'] = null
        this.searchFormModel[prefix + 'End'] = null
      }
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.deliverySchedule
        .pageNoDistributionDemanPlandApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const list = res.data?.distributionHistoryPage.records || []
        this.tableData = list
        const total = res?.data?.distributionHistoryPage.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
