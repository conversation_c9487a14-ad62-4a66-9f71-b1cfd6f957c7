// import UTILS from "@/utils/utils";
import Vue from 'vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
import inputView from '../components/inputView.vue'

export const checkColumn = [
  // {
  //   width: '50',
  //   type: 'checkbox',
  //   showInColumnChooser: false
  // },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    isPrimaryKey: true
  },
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  }
]
export const lastColumn = [
  {
    ignore: true,
    field: 'selfIndex',
    headerText: i18n.t('序号'),
    width: '150',
    cellTools: [
      {
        permission: ['O_02_0479'],
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data
        }
      },
      {
        permission: ['O_02_0480'],
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data
        }
      }
    ]
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('启用'), cssClass: 'col-active' },
        { value: 0, text: i18n.t('停用'), cssClass: 'col-inactive' }
      ]
    },
    cellTools: [
      {
        permission: ['O_02_0482'],
        id: 'disable',
        icon: '',
        title: i18n.t('停用'),
        visibleCondition: (data) => data.status === 1
      },
      {
        permission: ['O_02_0481'],
        id: 'enable',
        icon: '',
        title: i18n.t('启用'),
        visibleCondition: (data) => data.status === 0
      }
    ]
  },
  {
    field: 'code',
    headerText: i18n.t('地址唯一编号'),
    width: '120'
  },
  {
    field: 'configGroupType',
    headerText: i18n.t('配置方式'),
    width: '200',
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('工厂+库存地点'),
        2: i18n.t('工厂+加工商'),
        3: i18n.t('工厂+库存地点+分厂+分厂库存地点'),
        4: i18n.t('工厂+物料+库存地点'),
        5: i18n.t('加工商'),
        6: i18n.t('工厂+VMI仓'),
        7: i18n.t('工厂+库存地点+品类')
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '200',
    searchOptions: {
      ...MasterDataSelect.factoryAddress,
      renameField: 'siteCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.siteCode}}-{{data.siteName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'syncWmsStatus',
    headerText: i18n.t('白电WMS同步状态'),
    width: '160',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('未同步'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('同步中'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('同步成功'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('同步失败'), cssClass: 'col-inactive' }
      ]
    }
  },
  {
    field: 'syncWmsDesc',
    headerText: i18n.t('白电WMS同步信息'),
    width: '200'
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    width: '200',
    searchOptions: {
      operator: 'likeright',
      maxQueryValueLength: 20000
    }
    // searchOptions: {
    //   ...MasterDataSelect.material,
    //   renameField: "itemCode",
    // },
  },
  {
    ignore: true,
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: '200'
  },
  {
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓名称'),
    width: '200'
  },
  {
    field: 'siteAddressName',
    headerText: i18n.t('库存地点编号+名称'),
    width: '250',
    searchOptions: {
      ...MasterDataSelect.stockAddressName,
      renameField: 'siteAddressName',
      operator: 'in'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.siteAddress}}-{{data.siteAddressName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
    // valueConverter: {
    //   type: "function",
    //   filter: (e) => {
    //     let list = e.split("_");
    //     let str = "";
    //     if (list.length === 2) {
    //       str = list[1];
    //     }
    //     return str;
    //   },
    // },
  },
  {
    field: 'supplierName',
    headerText: i18n.t('加工商'),
    width: '300',
    searchOptions: {
      ...MasterDataSelect.supplier,
      renameField: 'supplierCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.supplierCode}}-{{data.supplierName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'subSiteCode',
    headerText: i18n.t('分厂编码'),
    width: '200'
  },
  {
    field: 'subSiteName',
    headerText: i18n.t('分厂名称'),
    width: '200'
  },
  {
    field: 'subSiteAddressCode',
    headerText: i18n.t('分厂库存地点编码'),
    width: '200',
    searchOptions: {
      renameField: 'subSiteAddress'
    }
  },
  {
    field: 'subSiteAddressName',
    headerText: i18n.t('分厂库存地点名称'),
    width: '200'
    // template: () => {
    //   return {
    //     template: Vue.component("headers", {
    //       template: `
    //           <div class="headers">
    //             <span>{{data.subSiteAddress}}-{{data.subSiteAddressName}}</span>
    //           </div>
    //         `,
    //       data() {
    //         return {
    //           data: {},
    //         };
    //       },
    //       mounted() {},
    //     }),
    //   };
    // },
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类'),
    width: '200',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.categoryCode}}-{{data.categoryName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'consigneeAddressCode',
    headerText: i18n.t('送货地址编码'),
    width: '200'
  },
  {
    field: 'consigneeName',
    headerText: i18n.t('送货联系人'),
    width: '200',
    template: () => ({ template: inputView })
  },
  {
    field: 'consigneePhone',
    headerText: i18n.t('送货联系电话'),
    width: '200',
    template: () => ({ template: inputView })
  },
  {
    field: 'consigneeAddress',
    headerText: i18n.t('送货地址'),
    width: '200'
  },
  {
    field: 'needDefault',
    headerText: i18n.t('是否默认'),
    width: '200',
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    field: 'isDirectDeliver',
    headerText: i18n.t('是否直送'),
    width: '200',
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    field: 'relationItemExpend',
    headerText: i18n.t('关联物料消耗'),
    width: '200',
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    width: '150'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    width: '150'
  }
]
