import { i18n } from '@/main.js'
import dayjs from 'dayjs'

export const isAutoAllocationList = [
  { text: i18n.t('是'), value: 1 },
  { text: i18n.t('否'), value: 0 }
]

export const isAutoAllocationOptions = [
  { label: i18n.t('是'), value: 1 },
  { label: i18n.t('否'), value: 0 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'factoryCode',
    title: i18n.t('工厂编码'),
    minWidth: 100
  },
  {
    field: 'factoryName',
    title: i18n.t('工厂名称'),
    minWidth: 200
  },
  {
    field: 'materialCode',
    title: i18n.t('物料编码'),
    minWidth: 120
  },
  {
    field: 'materialName',
    title: i18n.t('物料名称'),
    minWidth: 200
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类编码'),
    minWidth: 120
  },
  {
    field: 'categoryName',
    title: i18n.t('品类名称'),
    minWidth: 200
  },
  {
    field: 'isAutoAllocation',
    title: i18n.t('自动分配'),
    minWidth: 120,
    formatter: ({ cellValue }) => {
      return cellValue === 1 ? i18n.t('是') : i18n.t('否')
    },
    editRender: {},
    slots: {
      edit: 'isAutoAllocationEdit'
    }
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    minWidth: 120
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    minWidth: 120
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  }
]
