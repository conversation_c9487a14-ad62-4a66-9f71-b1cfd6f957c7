<!-- 采方-JIT物料配置 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('工厂编码')" prop="factoryCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.factoryCodeList"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料编码')" prop="materialCode">
          <mt-input
            v-model="materialCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
            @change="(e) => onChange(e, 'materialCode')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料名称')" prop="materialName">
          <mt-input
            v-model="searchFormModel.materialName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('自动分配')" prop="isAutoAllocation">
          <mt-select
            v-model="searchFormModel.isAutoAllocation"
            :show-clear-button="true"
            :data-source="isAutoAllocationList"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('创建人')" prop="createUserName">
          <mt-input
            v-model="searchFormModel.createUserName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建日期')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            @change="(e) => dateTimeChange(e, 'CreateTime')"
            :placeholder="$t('请选择创建日期')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('更新人')" prop="updateUserName">
          <mt-input
            v-model="searchFormModel.updateUserName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="updateTime" :label="$t('更新日期')">
          <mt-date-range-picker
            v-model="searchFormModel.updateTime"
            @change="(e) => dateTimeChange(e, 'UpdateTime')"
            :placeholder="$t('请选择更新日期')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      grid-id="6a9b3067-0756-449d-b7f9-5ea7883dee72"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :edit-config="{
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #isAutoAllocationEdit="{ row }">
        <vxe-select
          v-model="row.isAutoAllocation"
          :options="isAutoAllocationOptions"
          transfer
          :placeholder="$t('请选择')"
        />
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />

    <AddOrEdit ref="addOrEditRef" @confirm="handleSearch" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, isAutoAllocationList, isAutoAllocationOptions } from './config'
import { getHeadersFileName, download } from '@/utils/utils'
import AddOrEdit from './components/AddOrEdit.vue'

export default {
  components: { CollapseSearch, ScTable, AddOrEdit },
  data() {
    return {
      searchFormModel: {},
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
        { code: 'save', name: this.$t('保存'), status: 'info', loading: false },
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
        { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      materialCode: null,
      isAutoAllocationList,
      isAutoAllocationOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    onChange(e, field) {
      if (e) {
        this.searchFormModel[`${field}List`] = this[field].split(' ')
      } else {
        this.searchFormModel[`${field}List`] = null
        this[field] = null
      }
    },
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel['start' + field] = dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        this.searchFormModel['end' + field] = dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
      } else {
        this.searchFormModel['start' + field] = null
        this.searchFormModel['end' + field] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.materialCode = null
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.deliverySchedule
        .pageJitMaterialConfigApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.jitMaterialConfigDtoPage?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.jitMaterialConfigDtoPage?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'save':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认保存？')
            },
            success: () => {
              this.handleSave()
            }
          })
          break
        case 'delete':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除？')
            },
            success: () => {
              this.handleDelete(selectedRecords)
            }
          })
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleAdd() {
      this.$refs.addOrEditRef.dialogInit({
        title: this.$t('新增'),
        actionType: 'add'
      })
    },
    handleSave() {
      let params = []
      const updateRecords = this.tableRef.getUpdateRecords()
      params = updateRecords.map((item) => {
        return {
          id: item.id,
          isAutoAllocation: item.isAutoAllocation
        }
      })
      this.$API.deliverySchedule.updateJitMaterialConfigApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('更改成功'),
            type: 'success'
          })
          this.handleSearch()
        }
      })
    },
    handleDelete(selectedRecords) {
      let params = {
        queryReq: {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        },
        chooseReq: []
      }
      if (selectedRecords.length !== 0) {
        selectedRecords.forEach((item) => {
          let obj = {
            id: item.id
          }
          params.chooseReq.push(obj)
        })
      }
      this.$API.deliverySchedule.deleteJitMaterialConfigApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.deliverySchedule.importJitMaterialConfigApi,
          downloadTemplateApi: this.$API.deliverySchedule.downloadJitMaterialConfigApi,
          paramsKey: 'excel'
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.deliverySchedule
        .exportJitMaterialConfigApi(params)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  div .vxe-cell-border {
    border: solid #e6e9ed 1px;
    height: 32px;
    line-height: 32px;
    box-sizing: border-box;
  }
}

/deep/ .vxe-cell .vxe-default-select {
  background: #fff;
}
</style>
