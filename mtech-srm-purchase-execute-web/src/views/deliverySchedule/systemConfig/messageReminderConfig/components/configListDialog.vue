<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <mt-form-item prop="templateCode" :label="$t('消息提醒場景')">
        <mt-select
          :allow-filtering="true"
          :open-dispatch-change="false"
          v-model="ruleForm.templateCode"
          :data-source="templateCodeOptions"
          :fields="{ text: 'title', value: 'code' }"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="configType" :label="$t('配置类型')">
        <mt-select
          :open-dispatch-change="false"
          v-model="ruleForm.configType"
          :data-source="configTypeOptions"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="companyCode" :label="$t('公司')" v-if="ruleForm.configType.includes('1')">
        <mt-select
          :show-clear-button="false"
          ref="companyRef"
          v-model="ruleForm.companyCode"
          :data-source="companyOptions"
          :fields="{ text: 'label', value: 'orgCode' }"
          :allow-filtering="true"
          :filtering="getCompany"
          :open-dispatch-change="false"
          @change="companyCodeChange"
          :placeholder="$t('请选择公司')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item
        prop="siteCode"
        :label="$t('工厂')"
        v-if="ruleForm.configType && !ruleForm.configType.includes('1')"
      >
        <mt-select
          :open-dispatch-change="false"
          v-model="ruleForm.siteCode"
          :data-source="siteCodeOptions"
          :fields="{ text: 'label', value: 'siteCode' }"
          :placeholder="$t('请选择')"
          :allow-filtering="true"
          :filtering="postSiteFuzzyQuery"
          @change="siteCodeChange"
        ></mt-select>
      </mt-form-item>
      <mt-form-item
        prop="company"
        :label="$t('公司')"
        v-if="ruleForm.configType && !ruleForm.configType.includes('1')"
      >
        <mt-input type="text" v-model="ruleForm.company" disabled></mt-input>
      </mt-form-item>
      <mt-form-item
        prop="purchaseGroupCode"
        :label="$t('采购组')"
        v-if="ruleForm.configType.includes('6')"
      >
        <mt-select
          :open-dispatch-change="false"
          :allow-filtering="true"
          v-model="ruleForm.purchaseGroupCode"
          :data-source="purchaseGroupCodeOptions"
          :fields="{ text: 'label', value: 'groupCode' }"
          :placeholder="$t('请选择')"
          :filtering="getbussinessGroup"
          @open="startOpen"
        ></mt-select>
      </mt-form-item>
      <mt-form-item
        prop="planGroupCode"
        :label="$t('计划组')"
        v-if="ruleForm.configType.includes('5')"
      >
        <mt-select
          :open-dispatch-change="false"
          :allow-filtering="true"
          v-model="ruleForm.planGroupCode"
          :data-source="planGroupCodeOptions"
          :fields="{ text: 'label', value: 'groupCode' }"
          :placeholder="$t('请选择')"
          :filtering="getbussinessGroup1"
          @open="startOpen1"
        ></mt-select>
      </mt-form-item>
      <mt-form-item
        prop="supplierCode"
        :label="$t('供应商')"
        v-if="ruleForm.configType.includes('2')"
      >
        <mt-select
          :open-dispatch-change="false"
          v-model="ruleForm.supplierCode"
          :data-source="supplierOptions"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
          :fields="{ text: 'label', value: 'supplierCode' }"
          :filtering="serchText2"
        ></mt-select>
      </mt-form-item>
      <!-- <mt-form-item prop="noticeStartTime" :label="$t('提醒开始时间（H）')">
        <mt-inputNumber
          :show-spin-button="false"
          v-model="ruleForm.noticeStartTime"
          :min="0"
          :step="1"
          :precision="2"
          :show-clear-button="false"
          :placeholder="$t('请输入')"
        ></mt-inputNumber>
      </mt-form-item> -->
      <mt-form-item prop="intervalTime" :label="$t('间隔时间（H）')">
        <mt-inputNumber
          :show-spin-button="false"
          v-model="ruleForm.intervalTime"
          :min="0"
          :step="1"
          :precision="2"
          :show-clear-button="false"
          :placeholder="$t('请输入')"
        ></mt-inputNumber>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      configTypeOptions: [
        { text: this.$t('公司'), value: '1' },
        { text: this.$t('工厂'), value: '3' },
        { text: this.$t('工厂+采购组'), value: '3,6' },
        { text: this.$t('工厂+计划组'), value: '3,5' },
        { text: this.$t('工厂+采购组+供应商'), value: '3,6,2' },
        { text: this.$t('工厂+计划组+供应商'), value: '3,5,2' }
      ], //配置
      templateCodeOptions: [],
      siteCodeOptions: [], // 工厂下拉
      purchaseGroupCodeOptions: [], // 采购组下列选项
      planGroupCodeOptions: [], // 计划组下列选项
      supplierOptions: [], //供应商下拉
      dialogTitle: '',
      rules: {
        configType: [
          {
            required: true,
            message: this.$t('请选择配置类型'),
            trigger: 'blur'
          }
        ],
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        purchaseGroupCode: [{ required: true, message: this.$t('请选择采购组'), trigger: 'blur' }],
        planGroupCode: [{ required: true, message: this.$t('请选择计划组'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }],
        noticeStartTime: [
          {
            required: true,
            message: this.$t('请输入提醒开始时间'),
            trigger: 'blur'
          }
        ],
        intervalTime: [
          {
            required: true,
            message: this.$t('请输入间隔时间'),
            trigger: 'blur'
          }
        ],
        templateCode: [
          {
            required: true,
            message: this.$t('请选择消息提醒場景'),
            trigger: 'blur'
          }
        ],
        companyCode: [
          {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          }
        ]
      },
      companyOptions: [],
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {
        id: '',
        configType: '', //配置组合方式
        siteCode: '', //工厂code
        status: 1,
        purchaseGroupCode: '',
        planGroupCode: '',
        supplierCode: '', //供应商code
        noticeStartTime: '',
        intervalTime: '',
        templateCode: '',
        companyCode: '',
        companyName: '',
        company: ''
      },
      show: false,
      entryInfo: {}
    }
  },
  mounted() {
    this.templateGrouplist()
    this.getLocation = utils.debounce(this.getLocation, 1000)
    this.postSiteFuzzyQuery = utils.debounce(this.postSiteFuzzyQuery, 1000)
    this.getSupplier = utils.debounce(this.getSupplier, 1000)
    this.getbussinessGroup = utils.debounce(this.getbussinessGroup, 1000)
    this.getbussinessGroup1 = utils.debounce(this.getbussinessGroup1, 1000)
    this.getCompany = utils.debounce(this.getCompany, 1000)
  },
  methods: {
    templateGrouplist() {
      this.$API.deliverySchedule.templateGrouplist().then((res) => {
        let list = res.data
        list.some((item) => {
          if (item.code === 'purchase_execute') {
            this.templateQuery(item.id)
            return
          }
        })
      })
    },
    templateQuery(e) {
      let params = {
        page: {
          current: 1,
          size: 1000
        },
        defaultRules: [
          {
            field: 'groupId',
            operator: 'equal',
            value: e
          }
        ]
      }
      this.$API.deliverySchedule.templateQuery(params).then((res) => {
        let list = res.data?.records
        this.templateCodeOptions = list
      })
    },
    startOpen() {
      if (!this.purchaseGroupCodeOptions.length) {
        this.getbussinessGroup()
      }
    },
    startOpen1() {
      if (!this.planGroupCodeOptions.length) {
        this.getbussinessGroup1()
      }
    },
    // 根据员工ID查询公司
    getCompany(val, entryFirst) {
      this.$API.masterData
        .OrgFindSpecifiedChildrenLevelOrgs({
          fuzzyParam: val?.text || '',
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true,
          organizationIds: []
        })
        .then((res) => {
          let list = res.data || []
          list.forEach((item) => {
            item.label = `${item.orgCode}-${item.orgName}`
          })
          this.companyOptions = res.data
          if (val?.updateData) {
            this.$nextTick(() => {
              val.updateData(this.companyOptions)
            })
          }
          if (entryFirst === '1') {
            this.ruleForm.companyCode = this.entryInfo.row.companyCode
          }
        })
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args, entryFirst) {
      const { text, updateData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteCodeOptions = list.map((item) => {
              return {
                ...item,
                name: item.siteName,
                label: `${item.siteCode}-${item.siteName}`
              }
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.siteCodeOptions)
              })
            }
            if (entryFirst === '1') {
              this.ruleForm.siteCode = this.entryInfo.row.siteCode
            }
          }
        })
        .catch(() => {})
    },
    getbussinessGroup(val, entryFirst) {
      console.log(val, '我是采购组')
      let str = val?.text || ''
      let params = {
        fuzzyParam: str,
        groupTypeCode: 'BG001CG'
      }
      this.$API.masterData.getbussinessGroup(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.groupCode}-${item.groupName}`
        })
        this.purchaseGroupCodeOptions = list
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.purchaseGroupCodeOptions)
          })
        }
        if (entryFirst === '1') {
          this.ruleForm.purchaseGroupCode = this.entryInfo.row.purchaseGroupCode
        }
      })
    },
    getbussinessGroup1(val, entryFirst) {
      console.log(val, '我是计划组')
      let str = val?.text || ''
      let params = {
        fuzzyParam: str,
        groupTypeCode: 'BG001JH'
      }
      this.$API.masterData.getbussinessGroup(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.groupCode}-${item.groupName}`
        })
        this.planGroupCodeOptions = list
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.planGroupCodeOptions)
          })
        }
        if (entryFirst === '1') {
          this.ruleForm.planGroupCode = this.entryInfo.row.planGroupCode
        }
      })
    },
    serchText2(val) {
      console.log(this.$t('搜索值'), val)
      this.getSupplier(val && val.text ? val.text : '', '', val)
    },
    getSupplier(val, entryFirst, e) {
      //查询供应商的数据
      let str = val || this.ruleForm.supplierCode
      let params = {
        fuzzyNameOrCode: str || ''
      }
      this.$API.masterData.getSupplier(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.supplierCode}-${item.supplierName}`
        })
        this.supplierOptions = res.data || []
        if (entryFirst === '1') {
          this.ruleForm.supplierCode = this.entryInfo.row.supplierCode
        }
        if (e?.updateData) {
          this.$nextTick(() => {
            e.updateData(this.supplierOptions)
          })
        }
      })
    },
    companyCodeChange(val) {
      console.log(val, '我是公司改变')
      this.ruleForm.companyName = val?.itemData?.orgName
    },
    siteCodeChange(val) {
      console.log(val, '我是工厂改变了')
      this.getSite(val?.value)
    },
    getSite(siteCode) {
      //根据工厂获取公司的信息
      let params = { siteCode: siteCode }
      this.$API.masterData.getSite(params).then((res) => {
        if (res.data.length === 1) {
          this.ruleForm.companyCode = res.data[0].parentCode
          this.ruleForm.companyName = res.data[0].parentName
          this.ruleForm.company = `${res.data[0].parentCode}-${res.data[0].parentName}`
        }
      })
    },
    // 初始化
    async dialogInit(entryInfo) {
      this.dialogTitle = entryInfo.title
      if (this.dialogTitle === this.$t('新增')) {
        this.ruleForm = {
          configType: '', //配置组合方式
          siteCode: '', //工厂code
          status: 1,
          purchaseGroupCode: '',
          planGroupCode: '',
          supplierCode: '', //供应商code
          noticeStartTime: '',
          intervalTime: '',
          templateCode: '',
          templateName: '',
          companyCode: '',
          companyName: '',
          company: ''
        }
        this.getCompany()
        this.postSiteFuzzyQuery({ text: undefined })
        this.getSupplier()
        this.getbussinessGroup()
        this.getbussinessGroup1()
      }
      if (this.dialogTitle === this.$t('编辑')) {
        this.entryInfo = entryInfo
        console.log(entryInfo.row.supplierCode, '编辑数据')
        this.ruleForm.status = entryInfo.row.status
        this.ruleForm.id = entryInfo.row.id
        this.ruleForm.configType = entryInfo.row.configType //配置组合方式
        this.ruleForm.companyCode = entryInfo.row.companyCode
        this.ruleForm.companyName = entryInfo.row.companyName
        this.ruleForm.company = `${entryInfo.row.companyCode}-${entryInfo.row.companyName}`
        this.ruleForm.noticeStartTime = entryInfo.row.noticeStartTime
        this.ruleForm.intervalTime = entryInfo.row.intervalTime
        this.ruleForm.templateName = entryInfo.row.templateName
        this.ruleForm.templateCode = entryInfo.row.templateCode
        this.ruleForm.siteCode = null //工厂code
        this.ruleForm.supplierCode = null //供应商code
        this.ruleForm.supplierName = null //供应商code
        this.ruleForm.purchaseGroupCode = null //供应商code
        this.ruleForm.planGroupCode = null //供应商code
        if (entryInfo.row.configType == '1') {
          this.ruleForm.companyCode = null
          this.ruleForm.companyName = null
          this.ruleForm.company = null
          this.getCompany({ text: entryInfo.row.companyCode }, '1')
        }
        this.getSupplier(entryInfo.row.supplierCode, '1')
        this.postSiteFuzzyQuery({ text: entryInfo.row.siteCode }, '1')
        this.getbussinessGroup({ text: entryInfo.row.purchaseGroupCode }, '1')
        this.getbussinessGroup1({ text: entryInfo.row.planGroupCode }, '1')
      }
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            companyCode: this.ruleForm.companyCode,
            companyName: this.ruleForm.companyName,
            configType: this.ruleForm.configType,
            intervalTime: this.ruleForm.intervalTime,
            intervalTimeType: 0,
            // itemCode: this.ruleForm.itemCode || "",
            // itemName: this.ruleForm.itemName || "",
            noticeStartTime: this.ruleForm.noticeStartTime,
            noticeTimeType: 0,
            planGroupCode: this.ruleForm.planGroupCode,
            planGroupName: this.planGroupCodeOptions.find((item) => {
              return item.groupCode === this.ruleForm.planGroupCode
            })?.groupName,
            purchaseGroupCode: this.ruleForm.purchaseGroupCode,
            purchaseGroupName: this.purchaseGroupCodeOptions.find((item) => {
              return item.groupCode === this.ruleForm.purchaseGroupCode
            })?.groupName,
            siteCode: this.ruleForm.siteCode,
            siteName: this.siteCodeOptions.find((item) => {
              return item.siteCode === this.ruleForm.siteCode
            })?.siteName,
            status: this.ruleForm.status,
            supplierCode: this.ruleForm.supplierCode,
            supplierName: this.supplierOptions.find((item) => {
              return item.supplierCode === this.ruleForm.supplierCode
            })?.supplierName,
            templateCode: this.ruleForm.templateCode,
            templateName: this.templateCodeOptions.find((item) => {
              return item.code === this.ruleForm.templateCode
            })?.title
          }
          if (params.configType === '3') {
            //工厂
            params.planGroupCode = ''
            params.planGroupName = ''
            params.purchaseGroupCode = ''
            params.purchaseGroupName = ''
            params.supplierCode = ''
            params.supplierName = ''
          }
          if (params.configType === '3,6') {
            //工厂+采购组
            params.planGroupCode = ''
            params.planGroupName = ''
            params.supplierCode = ''
            params.supplierName = ''
          }
          if (params.configType === '3,5') {
            //工厂+计划组
            params.purchaseGroupCode = ''
            params.purchaseGroupName = ''
            params.supplierCode = ''
            params.supplierName = ''
          }
          if (params.configType === '3,6,2') {
            //工厂+采购组+供应商
            params.planGroupCode = ''
            params.planGroupName = ''
          }
          if (params.configType === '3,5,2') {
            //工厂+计划组+供应商
            params.purchaseGroupCode = ''
            params.purchaseGroupName = ''
          }
          if (params.configType === '1') {
            //公司
            params.planGroupCode = ''
            params.planGroupName = ''
            params.purchaseGroupCode = ''
            params.purchaseGroupName = ''
            params.supplierCode = ''
            params.supplierName = ''
            params.siteCode = ''
            params.siteName = ''
          }
          if (this.dialogTitle === this.$t('编辑')) {
            params.id = this.ruleForm.id
            this.$API.deliverySchedule.noticeConfigSave(params).then(() => {
              this.$refs.dialog.ejsRef.hide()
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$emit('updateList')
            })
          }
          if (this.dialogTitle === this.$t('新增')) {
            this.$API.deliverySchedule.noticeConfigSave(params).then(() => {
              this.$refs.dialog.ejsRef.hide()
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$emit('updateList')
            })
          }
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
<style lang="scss" scoped></style>
