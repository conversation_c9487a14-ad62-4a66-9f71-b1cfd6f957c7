<template>
  <div class="orderConfig full-height">
    <!-- 消息提醒配置 -->
    <mt-template-page
      ref="template-0"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <!-- 配置新增/编辑弹框 -->
    <config-list-dialog ref="configListDialog" @updateList="updateList"></config-list-dialog>
    <!-- 配置导入弹框 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-name="downTemplateName"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>
<script>
import { checkColumn, lastColumn } from './config/config'
import { BASE_MESSAGE } from '@/utils/constant'
import * as UTILS from '@/utils/utils'
export default {
  components: {
    ConfigListDialog: () => import('./components/configListDialog'),
    uploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    return {
      downTemplateName: this.$t('消息提醒配置模板'),
      downTemplateParams: {}, // 下载模板参数
      uploadParams: {}, // 明细行上传excel的
      requestUrls: {},
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              title: this.$t('新增'),
              permission: ['O_02_1100']
            },
            {
              id: 'Delete',
              icon: 'icon_solid_Delete',
              title: this.$t('删除'),
              permission: ['O_02_1101']
            },
            {
              permission: ['O_02_1102'],
              id: 'Enable',
              icon: 'icon_solid_Activateorder',
              title: this.$t('启用')
            },
            {
              permission: ['O_02_1103'],
              id: 'Disable',
              icon: 'icon_solid_Pauseorder',
              title: this.$t('停用')
            },
            {
              id: 'Import',
              icon: 'icon_solid_Import',
              title: this.$t('导入'),
              permission: ['O_02_1104']
            },
            {
              permission: ['O_02_1105'],
              id: 'Export1',
              icon: 'icon_solid_Import',
              title: this.$t('导出')
            }
          ],
          grid: {
            // height: "auto",
            frozenColumns: 1,
            columnData: checkColumn.concat(lastColumn),
            asyncConfig: {
              url: `${BASE_MESSAGE}/notice/config/query`,
              serializeList: (list) => {
                list.forEach((item, index) => {
                  item.selfIndex = index + 1
                  item.siteAddressCode = item.siteAddress
                  item.subSiteAddressCode = item.subSiteAddress
                })
                return list
              }
            }
          }
        }
      ]
    }
  },
  methods: {
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 上传成功后，获取到的数据
    upExcelConfirm(res) {
      this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      let addRow = res.data || []
      addRow.forEach((item) => {
        delete item.id
        delete item.errorInfo
      })
      let params = addRow
      console.log(params, '我是数据阿啊啊')
      this.$API.deliverySchedule.noticeConfigSaveBatch(params).then(() => {
        this.$toast({ content: this.$t('导入成功'), type: 'success' })
        this.updateList()
      })
    },
    //点击顶部的操作按钮
    handleClickToolBar(e) {
      console.log('方法1', e)
      console.log(e.gridRef.getMtechGridRecords(), e.toolbar.id)
      if (e.toolbar.id === 'Add') {
        this.handleAdd()
        return
      }
      if (e.toolbar.id === 'Import') {
        this.handleImport()
        return
      }
      if (e.toolbar.id === 'Export1') {
        this.handleExport()
        return
      }
      let selectRecords = e.gridRef.getMtechGridRecords()
      if (!selectRecords.length) {
        this.$toast({ content: this.$t('请至少选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Delete') {
        this.handleDelete(selectRecords)
      }
      if (e.toolbar.id === 'Enable') {
        this.handleEnable(selectRecords)
      }
      if (e.toolbar.id === 'Disable') {
        this.handleDisable(selectRecords)
      }
    },
    handleExport() {
      //导出
      let rule = this.$refs[`template-0`].getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 1000 },
        pageFlag: true,
        rules: rule.rules || []
      }
      this.$store.commit('startLoading')
      this.$API.deliverySchedule.noticeConfigExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    updateList() {
      //更新数据
      this.$refs[`template-0`].refreshCurrentGridData()
    },
    handleImport() {
      //导入
      this.downTemplateParams = {
        condition: '',
        page: { current: 1, size: 0 },
        pageFlag: false,
        defaultRules: []
      }
      this.uploadParams = {}
      this.requestUrls = {
        templateUrlPre: 'deliverySchedule',
        templateUrl: 'noticeConfigExport',
        uploadUrl: 'noticeConfigImport'
      }
      this.showUploadExcel(true)
    },
    handleAdd() {
      //新增
      this.$refs.configListDialog.dialogInit({ title: this.$t('新增') })
    },
    handleDelete(row) {
      //删除
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认删除选中的消息提醒配置吗?')
        },
        success: () => {
          let ids = row.map((item) => item.id)
          let params = {
            idList: ids
          }
          this.$API.deliverySchedule.noticeConfigDel(params).then(() => {
            this.$toast({
              content: this.$t('删除消息提醒配置操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleEnable(row) {
      //启用
      let hasOne = row.some((item) => {
        return item.status === 1
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择停用状态的行操作!'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认启用选中的消息提醒配置吗?')
        },
        success: () => {
          let ids = row.map((item) => item.id)
          let params = {
            ids: ids,
            status: 1
          }
          this.$API.deliverySchedule.noticeConfigStatus(params).then(() => {
            this.$toast({
              content: this.$t('启用消息提醒配置操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleDisable(row) {
      //停用
      let hasOne = row.some((item) => {
        return item.status === 0
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择已启用状态的行操作!'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认停用选中的消息提醒配置吗?')
        },
        success: () => {
          let ids = row.map((item) => item.id)
          let params = {
            ids: ids,
            status: 0
          }
          this.$API.deliverySchedule.noticeConfigStatus(params).then(() => {
            this.$toast({
              content: this.$t('停用消息提醒配置操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleEdit(row) {
      //编辑
      this.$refs.configListDialog.dialogInit({
        title: this.$t('编辑'),
        row: row
      })
    },
    //点击表格的操作按钮
    handleClickCellTool(e) {
      console.log('方法2', e)
      if (e.tool.id === 'edit') {
        this.handleEdit(e.data)
      }
      if (e.tool.id === 'delete') {
        this.handleDelete([e.data])
      }
      if (e.tool.id === 'enable') {
        this.handleEnable([e.data])
      }
      if (e.tool.id === 'disable') {
        this.handleDisable([e.data])
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.orderConfig {
}
</style>
