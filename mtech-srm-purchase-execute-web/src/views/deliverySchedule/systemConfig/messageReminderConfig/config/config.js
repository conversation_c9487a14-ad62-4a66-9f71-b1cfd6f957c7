// import UTILS from "@/utils/utils";
import Vue from 'vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'

export const checkColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  }
]
const configTypeOptions = [
  { text: i18n.t('工厂'), value: '3' },
  { text: i18n.t('工厂+采购组'), value: '3,6' },
  { text: i18n.t('工厂+计划组'), value: '3,5' },
  { text: i18n.t('工厂+采购组+供应商'), value: '3,6,2' },
  { text: i18n.t('工厂+计划组+供应商'), value: '3,5,2' }
]
export const lastColumn = [
  {
    ignore: true,
    field: 'selfIndex',
    headerText: i18n.t('序号'),
    width: '150',
    cellTools: [
      {
        permission: ['O_02_0479'],
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data
        }
      },
      {
        permission: ['O_02_0480'],
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data
        }
      }
    ]
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('启用'), cssClass: 'col-active' },
        { value: 0, text: i18n.t('停用'), cssClass: 'col-inactive' }
      ]
    },
    cellTools: [
      {
        permission: ['O_02_0482'],
        id: 'disable',
        icon: '',
        title: i18n.t('停用'),
        visibleCondition: (data) => data.status === 1
      },
      {
        permission: ['O_02_0481'],
        id: 'enable',
        icon: '',
        title: i18n.t('启用'),
        visibleCondition: (data) => data.status === 0
      }
    ]
  },
  {
    width: '200',
    field: 'templateName',
    headerText: i18n.t('消息提醒场景')
  },
  {
    field: 'configType',
    headerText: i18n.t('配置类型'),
    width: '200',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        let str = ''
        configTypeOptions.some((item1) => {
          if (e == item1.value) {
            str = item1.text
          }
        })
        return str
      }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '200',
    searchOptions: {
      ...MasterDataSelect.factoryAddress,
      renameField: 'siteCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.siteCode}}-{{data.siteName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  // {
  //   field: "itemCode",
  //   headerText: i18n.t("物料编码"),
  //   width: "200",
  // },
  // {
  //   field: "itemName",
  //   headerText: i18n.t("物料名称"),
  //   width: "200",
  // },
  {
    field: 'companyCode',
    headerText: i18n.t('公司'),
    width: '250',
    searchOptions: {
      ...MasterDataSelect.businessCompany,
      renameField: 'companyCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.companyCode}}-{{data.companyName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'planGroupName',
    headerText: i18n.t('计划组'),
    width: '250',
    searchOptions: {
      ...MasterDataSelect.businessGroup,
      renameField: 'planGroupCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.planGroupCode}}-{{data.planGroupName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'purchaseGroupName',
    headerText: i18n.t('采购组'),
    width: '250',
    searchOptions: {
      ...MasterDataSelect.businessGroup,
      renameField: 'purchaseGroupCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.purchaseGroupCode}}-{{data.purchaseGroupName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商'),
    width: '300',
    searchOptions: {
      ...MasterDataSelect.supplier,
      renameField: 'supplierCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.supplierCode}}-{{data.supplierName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'noticeStartTime',
    headerText: i18n.t('提醒开始时间（H）'),
    width: '200'
  },
  {
    field: 'intervalTime',
    headerText: i18n.t('间隔时间（H）'),
    width: '200'
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    width: '150'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    width: '150'
  }
]
