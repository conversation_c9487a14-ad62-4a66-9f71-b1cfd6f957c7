<!-- 采方-调度员分工-新增or编辑 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-form-item prop="factoryCode" :label="$t('工厂')">
        <!-- <RemoteAutocomplete
          v-model="formData.factoryCode"
          :url="$API.masterData.getSiteListUrl"
          :placeholder="$t('请选择')"
          :fields="{ text: 'siteName', value: 'siteCode' }"
          :search-fields="['siteName', 'siteCode']"
          :disabled="actionType === 'edit'"
          @change="siteChange"
        /> -->
        <mt-select
          v-model="formData.factoryCode"
          :fields="{ text: 'theCodeName', value: 'siteCode' }"
          :data-source="siteOptions"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
          @change="siteChange"
        />
      </mt-form-item>
      <mt-form-item prop="materialCode" :label="$t('物料编码')">
        <mt-input
          v-model="formData.materialCode"
          :show-clear-button="true"
          :placeholder="$t('请输入')"
        />
      </mt-form-item>
      <mt-form-item prop="workCenterCode" :label="$t('工作中心')">
        <!-- <RemoteAutocomplete
          v-model="formData.workCenterCode"
          url="/masterDataManagement/tenant/work-center/paged-query"
          :placeholder="$t('请选择')"
          :fields="{ text: 'workCenterName', value: 'workCenterCode' }"
          :search-fields="['workCenterName', 'workCenterCode']"
          :disabled="actionType === 'edit'"
          @change="workCenterChange"
        /> -->
        <!-- <mt-select
          v-model="formData.workCenterCode"
          :fields="{ text: 'theCodeName', value: 'workCenterCode' }"
          :data-source="workCenterOptions"
          :allow-filtering="true"
          :filtering="getWorkCenterOptions"
          filter-type="Contains"
          :placeholder="$t('请选择')"
          :disabled="actionType === 'edit'"
          @change="workCenterChange"
        /> -->
        <mt-input
          v-model="formData.workCenterCode"
          :show-clear-button="true"
          :placeholder="$t('请输入')"
        />
      </mt-form-item>
      <mt-form-item prop="lineBody" :label="$t('线体')">
        <!-- <RemoteAutocomplete
          v-model="formData.lineBody"
          url="/masterDataManagement/tenant/dict-item/dict-code"
          :params="{
            dictCode: 'JIT_PRODUCTION_LINE'
          }"
          :placeholder="$t('请选择')"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          records-position="data"
          :disabled="actionType === 'edit'"
        /> -->
        <mt-select
          v-model="formData.lineBody"
          :fields="{ text: 'theCodeName', value: 'itemCode' }"
          :data-source="lineBodyOptions"
          :allow-filtering="true"
          filter-type="Contains"
          :placeholder="$t('请选择')"
          :disabled="actionType === 'edit'"
          @change="lineBodyChange"
        />
      </mt-form-item>
      <mt-form-item prop="dispatcherCode" :label="$t('调度员')">
        <RemoteAutocomplete
          v-model="formData.dispatcherCode"
          url="/masterDataManagement/tenant/employee/currentTenantEmployees"
          method="get"
          :placeholder="$t('请选择')"
          :fields="{ text: 'employeeName', value: 'accountName' }"
          records-position="data"
          params-key="fuzzyName"
          @change="dispatcherChange"
        />
      </mt-form-item>
      <mt-form-item prop="receiveAddress" :label="$t('收货地址')">
        <mt-select
          v-model="formData.receiveAddress"
          :data-source="receiveAddressOptions"
          :fields="{ text: 'consigneeAddress', value: 'consigneeAddress' }"
          :show-clear-button="true"
          :allow-filtering="true"
          filter-type="Contains"
          :placeholder="$t('请选择')"
          @change="receiveAddressChange"
        />
      </mt-form-item>
      <mt-form-item prop="consignee" :label="$t('收货人')">
        <mt-input
          v-model="formData.consignee"
          :show-clear-button="true"
          :placeholder="$t('多个用 / 拼接')"
        />
      </mt-form-item>
      <mt-form-item prop="contactNo" :label="$t('联系电话')">
        <mt-input
          v-model="formData.contactNo"
          :show-clear-button="true"
          :placeholder="$t('多个用 / 拼接')"
        />
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { addCodeNameKeyInList } from '@/utils/utils'
// import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      dialogTitle: '',
      actionType: 'add',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {},
      rules: {
        factoryCode: [
          {
            required: true,
            message: this.$t('请选择工厂'),
            trigger: 'blur'
          }
        ],
        materialCode: [
          {
            required: true,
            message: this.$t('请输入物料编码'),
            trigger: 'blur'
          }
        ],
        workCenterCode: [
          {
            required: true,
            message: this.$t('请选择工作中心'),
            trigger: 'blur'
          }
        ],
        lineBody: [
          {
            required: true,
            message: this.$t('请选择线体'),
            trigger: 'blur'
          }
        ],
        dispatcherCode: [
          {
            required: true,
            message: this.$t('请选择调度员'),
            trigger: 'blur'
          }
        ],
        receiveAddress: [
          {
            required: true,
            message: this.$t('请选择收货地址'),
            trigger: 'blur'
          }
        ],
        consignee: [
          {
            required: true,
            message: this.$t('请输入收货人'),
            trigger: 'blur'
          }
        ],
        contactNo: [
          {
            required: true,
            message: this.$t('请输入联系电话'),
            trigger: 'blur'
          }
        ]
      },

      receiveAddressOptions: [],
      workCenterOptions: [],
      firstRender: false,
      lineBodyOptions: [],
      siteOptions: []
    }
  },
  mounted() {
    this.getLineBodyOptions()
    this.getSiteOptions()
  },
  methods: {
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, row } = args
      this.dialogTitle = title
      this.actionType = actionType
      if (actionType === 'edit') {
        this.formData = row
        this.firstRender = true
      }
    },
    beforeOpen() {
      this.formData = {
        consignee: null,
        contactNo: null
      }
      this.$refs.ruleForm.clearValidate()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.handleSave()
        }
      })
    },
    handleSave() {
      let params = { ...this.formData }
      const api =
        this.actionType === 'add'
          ? this.$API.deliverySchedule.addDispatcherDivideApi
          : this.$API.deliverySchedule.updateDispatcherDivideApi
      api(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.actionType === 'add' ? this.$t('新增成功') : this.$t('更改成功'),
            type: 'success'
          })
          this.$emit('confirm')
          this.handleClose()
        }
      })
    },
    getSiteOptions() {
      let params = {
        orgCode: 'SITE'
      }
      this.$API.masterData.getOrgListByCode(params).then((res) => {
        if (res.code === 200) {
          this.siteOptions = res.data.map((item) => {
            return {
              theCodeName: item.dimensionCodeValue + '-' + item.dimensionNameValue,
              siteCode: item.dimensionCodeValue,
              siteName: item.dimensionNameValue
            }
          })
        }
      })
    },
    siteChange(e) {
      this.formData.factoryName = e.itemData?.siteName
      // this.getWorkCenterOptions('', e.itemData?.siteCode)
      // this.getWorkCenterOptions = utils.debounce(this.getWorkCenterOptions, 500)
      let params = {
        siteCode: e.itemData?.siteCode
      }
      this.$API.deliverySchedule.queryBySiteAndAddress(params).then((res) => {
        if (res.code === 200) {
          const list = res?.data || []
          this.receiveAddressOptions = addCodeNameKeyInList({
            firstKey: 'consigneeAddress',
            list
          })
        }
      })
    },
    categoryChange(e) {
      this.formData.categoryName = e.itemData?.categoryName
    },
    workCenterChange(e) {
      this.formData.workCenterName = e.itemData?.workCenterName
    },
    dispatcherChange(e) {
      this.formData.dispatcherName = e.itemData?.employeeName
    },
    receiveAddressChange(e) {
      if (this.actionType === 'edit' && this.firstRender) {
        this.firstRender = false
        return
      } else {
        this.formData.consignee = e.itemData?.consigneeName
        this.formData.contactNo = e.itemData?.consigneePhone
      }
      this.$refs.ruleForm.validate()
    },
    getWorkCenterOptions(e = { text: '' }, factoryCode) {
      const { text } = e
      let siteCode = factoryCode || this.formData.factoryCode
      let params = {
        siteCode,
        workCenterCode: text
      }
      this.$API.masterData.getWorkCenter(params).then((res) => {
        if (res.code === 200) {
          const list = res?.data || []
          this.workCenterOptions = addCodeNameKeyInList({
            firstKey: 'workCenterCode',
            secondKey: 'workCenterName',
            list
          })
          this.$nextTick(() => {
            if (e.updateData && typeof e.updateData == 'function') {
              e.updateData(this.workCenterOptions)
            }
          })
        }
      })
    },
    getLineBodyOptions(e = { text: '' }) {
      let params = {
        dictCode: 'JIT_PRODUCTION_LINE'
      }
      this.$API.masterData.getDictCode(params).then((res) => {
        if (res.code === 200) {
          const list = res?.data || []
          this.lineBodyOptions = addCodeNameKeyInList({
            firstKey: 'itemCode',
            secondKey: 'itemName',
            list
          })
          this.$nextTick(() => {
            if (e.updateData && typeof e.updateData == 'function') {
              e.updateData(this.lineBodyOptions)
            }
          })
        }
      })
    }
  }
}
</script>
