import { i18n } from '@/main.js'
import dayjs from 'dayjs'

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'factoryCode',
    title: i18n.t('工厂编码'),
    minWidth: 100
  },
  {
    field: 'materialCode',
    title: i18n.t('物料编码'),
    minWidth: 120
  },
  {
    field: 'workCenterCode',
    title: i18n.t('工作中心'),
    minWidth: 120
  },
  {
    field: 'lineBody',
    title: i18n.t('线体'),
    minWidth: 120
  },
  {
    field: 'dispatcherCode',
    title: i18n.t('调度员账号'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'dispatcherCodeEdit'
    }
  },
  {
    field: 'dispatcherName',
    title: i18n.t('调度员姓名'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'dispatcherNameEdit'
    }
  },
  {
    field: 'receiveAddress',
    title: i18n.t('收货地址'),
    minWidth: 160,
    editRender: {},
    slots: {
      edit: 'receiveAddressEdit'
    }
  },
  {
    field: 'consignee',
    title: i18n.t('收货人'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'consigneeEdit'
    }
  },
  {
    field: 'contactNo',
    title: i18n.t('联系电话'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'contactNoEdit'
    }
  },
  {
    field: 'factoryName',
    title: i18n.t('工厂名称'),
    minWidth: 200
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    minWidth: 120
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    minWidth: 120
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  }
]
