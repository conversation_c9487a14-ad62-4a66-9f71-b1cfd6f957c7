<!-- 采方-调度员分工 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('工厂编码')" prop="factoryCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.factoryCodeList"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料编码')" prop="materialCode">
          <mt-input
            v-model="searchFormModel.materialCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('工作中心')" prop="workCenterCode">
          <mt-input
            v-model="searchFormModel.workCenterCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('线体编码')" prop="lineBody">
          <mt-input
            v-model="searchFormModel.lineBody"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('调度员')" prop="dispatcherName">
          <mt-input
            v-model="searchFormModel.dispatcherName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('收货地址')" prop="receiveAddress">
          <mt-input
            v-model="searchFormModel.receiveAddress"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('收货人')" prop="consignee">
          <mt-input
            v-model="searchFormModel.consignee"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('联系电话')" prop="contactNo">
          <mt-input
            v-model="searchFormModel.contactNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('创建人')" prop="createUserName">
          <mt-input
            v-model="searchFormModel.createUserName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建日期')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            @change="(e) => dateTimeChange(e, 'CreateTime')"
            :placeholder="$t('请选择创建日期')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('更新人')" prop="updateUserName">
          <mt-input
            v-model="searchFormModel.updateUserName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="updateTime" :label="$t('更新日期')">
          <mt-date-range-picker
            v-model="searchFormModel.updateTime"
            @change="(e) => dateTimeChange(e, 'UpdateTime')"
            :placeholder="$t('请选择更新日期')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      grid-id="f0bb6333-09d3-4606-9762-e91c8339fac0"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :edit-config="{
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }"
      @edit-actived="editBegin"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #dispatcherCodeEdit="{ row }">
        <vxe-pulldown ref="xDownItem" transfer>
          <template #default>
            <vxe-input
              :value="row.dispatcherCode"
              :placeholder="$t('请选择')"
              readonly
              @click="(e) => focusDispatcherCode(e, row)"
            />
          </template>
          <template #dropdown>
            <vxe-input
              prefix-icon="vxe-icon-search"
              :placeholder="$t('搜索')"
              @keyup="(e) => keyupDispatcherCode(e, row)"
              style="width: 100%"
            />
            <vxe-list
              height="200"
              class="predict-vxe-dropdown"
              :data="dispatcherOptions"
              auto-resize
            >
              <template #default="{ items }">
                <div
                  v-show="dispatcherOptions.length"
                  class="predict-vxe-list-item"
                  v-for="item in items"
                  :key="item.value"
                  @click="selectDispatcherCode(item, row)"
                >
                  <span>{{ item.label }}</span>
                </div>
                <div v-show="!dispatcherOptions.length" class="predict-vxe-list-item">
                  <span>{{ $t('暂无数据') }}</span>
                </div>
              </template>
            </vxe-list>
          </template>
        </vxe-pulldown>
      </template>
      <template #dispatcherNameEdit="{ row }">
        <vxe-input v-model="row.dispatcherName" disabled />
      </template>
      <template #receiveAddressEdit="{ row }">
        <vxe-select
          v-model="row.receiveAddress"
          :options="receiveAddressOptions"
          transfer
          filterable
          :placeholder="$t('请选择')"
          @change="({ value }) => receiveAddressChange(row, value)"
        />
      </template>
      <template #consigneeEdit="{ row }">
        <vxe-input v-model.trim="row.consignee" :placeholder="$t('请输入')" />
      </template>
      <template #contactNoEdit="{ row }">
        <vxe-input v-model.trim="row.contactNo" :placeholder="$t('请输入')" />
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />

    <AddOrEdit ref="addOrEditRef" @confirm="handleSearch" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData } from './config'
import { getHeadersFileName, download } from '@/utils/utils'
import AddOrEdit from './components/AddOrEdit.vue'
import { utils } from '@mtech-common/utils'

export default {
  components: { CollapseSearch, ScTable, AddOrEdit },
  data() {
    return {
      searchFormModel: {},
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
        { code: 'save', name: this.$t('保存'), status: 'info', loading: false },
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
        { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      dispatcherOptions: [],
      receiveAddressOptions: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    editBegin(args) {
      const { row } = args
      if (args.$event) {
        this.getReceiveAddressOptions(row.factoryCode)
        this.getDispatcherOption(row.dispatcherCode)
        this.getDispatcher = utils.debounce(this.getDispatcherOption, 500)
      }
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        if (!this.isValidData(row)) {
          // 当出现错误时，指定行进入编辑状态
          this.tableRef.setEditRow(row)
          return
        }
      }
    },
    isValidData(data) {
      const { consignee, contactNo } = data
      let valid = false
      if (!consignee) {
        this.$toast({ content: this.$t('收货人不能为空'), type: 'warning' })
      } else if (!contactNo) {
        this.$toast({ content: this.$t('联系电话不能为空'), type: 'warning' })
      } else {
        valid = true
      }

      return valid
    },
    getReceiveAddressOptions(siteCode) {
      let params = {
        siteCode
      }
      this.$API.deliverySchedule.queryBySiteAndAddress(params).then((res) => {
        if (res.code === 200) {
          const list = res?.data || []
          this.receiveAddressOptions = list.map((item) => {
            return {
              label: item.consigneeAddress,
              value: item.consigneeAddress,
              consigneeName: item.consigneeName,
              consigneePhone: item.consigneePhone
            }
          })
        }
      })
    },
    receiveAddressChange(row, receiveAddress) {
      this.receiveAddressOptions.forEach((item) => {
        if (item.value === receiveAddress) {
          row.consignee = item.consigneeName
          row.contactNo = item.consigneePhone
        }
      })
    },
    getDispatcherOption(text) {
      let params = {
        fuzzyName: text
      }
      this.$API.masterData.getCurrentTenantEmployees(params).then((res) => {
        if (res.code === 200) {
          const list = res?.data || []
          this.dispatcherOptions = list.map((item) => {
            return {
              label: item.accountName + '-' + item.employeeName,
              value: item.accountName,
              ...item
            }
          })
        }
      })
    },
    focusDispatcherCode() {
      this.$refs.xDownItem.showPanel()
    },
    keyupDispatcherCode(e) {
      this.getDispatcher(e.value)
    },
    selectDispatcherCode(item, row) {
      row.dispatcherCode = item.accountName
      row.dispatcherName = item.employeeName
      this.$refs.xDownItem.hidePanel()
    },
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel['start' + field] = dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        this.searchFormModel['end' + field] = dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
      } else {
        this.searchFormModel['start' + field] = null
        this.searchFormModel['end' + field] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      params.materialCodeList = params.materialCode !== '' ? params.materialCode?.split(' ') : []
      this.loading = true
      const res = await this.$API.deliverySchedule
        .pageDispatcherDivideApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.dispatcherDivisionDtoPage?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.dispatcherDivisionDtoPage?.records || []
        this.tableData = records.map((item, index) => {
          return {
            line: index + 1,
            ...item
          }
        })
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const updateRecords = this.tableRef.getUpdateRecords()
      if (e.code === 'save' && updateRecords.length === 0) {
        this.$toast({ content: this.$t('暂无需要保存的数据'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'save':
          this.handleSave(updateRecords)
          break
        case 'delete':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除？')
            },
            success: () => {
              this.handleDelete(selectedRecords)
            }
          })
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleAdd() {
      this.$refs.addOrEditRef.dialogInit({
        title: this.$t('新增'),
        actionType: 'add'
      })
    },
    handleSave(updateRecords) {
      let params = updateRecords
      let canSave = false
      for (let i = 0; i < updateRecords.length; i++) {
        const item = updateRecords[i]
        if (!item.consignee) {
          this.$toast({
            content: this.$t('第') + item.line + this.$t('行') + this.$t('收货人不能为空'),
            type: 'warning'
          })
          canSave = false
          break
        }
        if (!item.contactNo) {
          this.$toast({
            content: this.$t('第') + item.line + this.$t('行') + this.$t('联系电话不能为空'),
            type: 'warning'
          })
          canSave = false
          break
        }
        canSave = true
      }
      if (canSave) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认保存？')
          },
          success: () => {
            this.$API.deliverySchedule.updateDispatcherDivideApi(params).then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: this.$t('更改成功'),
                  type: 'success'
                })
                this.handleSearch()
              }
            })
          }
        })
      }
    },
    handleDelete(selectedRecords) {
      let params = {
        queryReq: {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        },
        chooseReqList: []
      }
      if (selectedRecords.length !== 0) {
        selectedRecords.forEach((item) => {
          let obj = {
            id: item.id
          }
          params.chooseReqList.push(obj)
        })
      }
      this.$API.deliverySchedule.deleteDispatcherDivideApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.deliverySchedule.importDispatcherDivideApi,
          downloadTemplateApi: this.$API.deliverySchedule.downloadDispatcherDivideApi,
          paramsKey: 'excel'
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.deliverySchedule
        .exportDispatcherDivideApi(params)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  div .vxe-cell-border {
    border: solid #e6e9ed 1px;
    height: 32px;
    line-height: 32px;
    box-sizing: border-box;
  }
}

/deep/ .vxe-cell .vxe-default-select {
  background: #fff;
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
