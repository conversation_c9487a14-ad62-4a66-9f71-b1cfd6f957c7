import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import Vue from 'vue'
import { ConfigType, WeekOptions, SymbolConst, ConfigGroupType } from '../config/constant'

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, hasTime } = args

  const template = () => {
    return {
      template: Vue.component('date', {
        template: `<div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div><div>{{data[dataKey] | dateFormat}}</div></div>`,
        data: function () {
          return { data: {}, dataKey, hasTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}

// 循环时间
export const circulationTime = (args) => {
  const { dataKey } = args

  const template = () => {
    return {
      template: Vue.component('date', {
        template: `
        <div>
          <!-- 按周，配置类型=固定时间 -->
          <div
            v-if="data.configType == ConfigType.fixed"
          >{{data.timeValue | weekFormat}}</div>
          <div v-else>-</div>
        </div>`,
        data: function () {
          return {
            data: {},
            dataKey,
            ConfigType,
            WeekOptions,
            SymbolConst
          }
        },
        filters: {
          weekFormat(value) {
            const weekTemp = []
            if (value?.week?.length > 0) {
              const weekList = value.week.split(SymbolConst.comma) // "1,2,3" => [1, 2, 3];
              weekList.forEach((itemWeek) => {
                for (let i = 0; i < WeekOptions.length; i++) {
                  if (WeekOptions[i].value == itemWeek) {
                    weekTemp.push(WeekOptions[i].text)
                    break
                  }
                }
              })
            }
            const weekStr = weekTemp.join(SymbolConst.slash) // "周一/周二/周三"
            const timeStr = value.timeFormat || ''

            return `${weekStr} ${timeStr}`
          }
        }
      })
    }
  }

  return template
}

// 超时时间(H)
export const overtimeTime = (args) => {
  const { dataKey } = args

  const template = () => {
    return {
      template: Vue.component('date', {
        template: `
        <div>
          <!-- 配置类型=超时时间 -->
          <div v-if="data.configType === ConfigType.exceed"
          >{{data.timeValue | dataFormat}}</div>
          <div v-else>-</div>
        </div>`,
        data: function () {
          return {
            data: {},
            dataKey,
            ConfigType
          }
        },
        filters: {
          dataFormat(value) {
            return value.hour
          }
        }
      })
    }
  }

  return template
}

// 配置方式改变的：采购组、计划组、工厂、供应商
export const textByConfigGroupType = (args) => {
  const { dataKey } = args

  const template = () => {
    return {
      template: Vue.component('textByConfigGroupType', {
        template: `
        <div>
          <div v-if="isShowData()"
          >{{data[dataKey]}}</div>
          <div v-else>-</div>
        </div>`,
        data: function () {
          return {
            data: {},
            dataKey,
            ConfigGroupType
          }
        },
        methods: {
          // 是否显示当前数据
          isShowData() {
            let isShow = false
            if (
              this.dataKey === 'buyerOrgName' &&
              this.data.configGroupType == this.ConfigGroupType.purchase
            ) {
              // 采购组 && 配置方式 == 采购组/工厂/供应商
              isShow = true
            } else if (
              this.dataKey === 'planGroupName' &&
              this.data.configGroupType == this.ConfigGroupType.plan
            ) {
              // 计划组 && 配置方式 == 计划组/工厂/供应商
              isShow = true
            } else if (
              this.dataKey === 'supplierName' &&
              (this.data.configGroupType == this.ConfigGroupType.purchase ||
                this.data.configGroupType == this.ConfigGroupType.plan)
            ) {
              // 供应商 && (配置方式 == 采购组/工厂/供应商 || 配置方式 == 计划组/工厂/供应商)
              isShow = true
            }

            return isShow
          }
        }
      })
    }
  }

  return template
}
