<template>
  <!-- 大数据批次来料 - 采方 -->
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      default-max-height="75"
      @reset="handleCustomReset"
      @search="handleCustomSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <!-- 版次号 -->
        <mt-form-item prop="batchCode" :label="$t('版次号')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.batchCode" :show-clear-button="true" />
        </mt-form-item>
        <!-- JIT行编号 -->
        <mt-form-item prop="rowCode" :label="$t('JIT行编号')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.rowCode" :show-clear-button="true" />
        </mt-form-item>
        <!-- 采购组 -->
        <mt-form-item prop="buyerOrgCodeList" :label="$t('采购组')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.buyerOrgCodeList"
            :data-source="buyerOrgCodeOptions"
            :allow-filtering="true"
            filter-type="Contains"
            :multiple="true"
            :filtering="getBuyerOrgCodeList"
            :fields="{ text: 'theCodeName', value: 'groupCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 生产线 -->
        <mt-form-item prop="productLine" :label="$t('生产线')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.productLine"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 交货日期 -->
        <mt-form-item prop="deliveryDate" :label="$t('交货日期')">
          <mt-date-range-picker
            v-model="searchFormModel.deliveryDate"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :disabled-date="formaterDate"
            :change="(e) => dateChange(e, 'deliveryDateList')"
          />
        </mt-form-item>
        <!-- 工厂编码 -->
        <mt-form-item prop="siteCodeList" :label="$t('工厂')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.siteCodeList"
            :data-source="siteCodeListOptions"
            :allow-filtering="true"
            :multiple="true"
            filter-type="Contains"
            :filtering="getSiteCodeList"
            :fields="{ text: 'theCodeName', value: 'siteCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 交货地点 -->
        <mt-form-item prop="warehouseCodeList" :label="$t('库存地点')" label-style="top">
          <mt-multi-select
            :show-clear-button="true"
            v-model="searchFormModel.warehouseCodeList"
            :open-dispatch-change="false"
            :allow-filtering="true"
            :filtering="getWarehouseCodeOptions"
            :data-source="warehouseCodeOptions"
            :fields="{
              text: 'theCodeName',
              value: 'externalCode'
            }"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <!-- 分厂编码 -->
        <mt-form-item prop="subSiteCodeList" :label="$t('分厂编码')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.subSiteCodeList"
            :data-source="subSiteCodeListOptions"
            :allow-filtering="true"
            :multiple="true"
            filter-type="Contains"
            :filtering="getsubSiteCodeList"
            :fields="{ text: 'subSiteName', value: 'subSiteCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 分厂库存地点 -->
        <mt-form-item prop="subSiteAddressCodeList" :label="$t('分厂库存地点')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.subSiteAddressCodeList"
            :data-source="subSiteAddressCodeListOptions"
            :allow-filtering="true"
            :multiple="true"
            filter-type="Contains"
            :filtering="getsubSiteAddressCode"
            :fields="{ text: 'subSiteAddress', value: 'subSiteAddressCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 物料编码 -->
        <mt-form-item prop="itemCodeList" :label="$t('物料')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.itemCodeList"
            :data-source="itemCodeOptions"
            :allow-filtering="true"
            filter-type="Contains"
            :multiple="true"
            :filtering="getItemCodeList"
            :fields="{ text: 'theCodeName', value: 'itemCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 供应商 -->
        <mt-form-item prop="supplierCodeList" :label="$t('供应商编码')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.supplierCodeList"
            :data-source="supplierTypeList"
            :allow-filtering="true"
            filter-type="Contains"
            :multiple="true"
            :filtering="getSupplierList"
            :fields="{ text: 'theCodeName', value: 'supplierCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- JIT更新时间 -->
        <mt-form-item prop="renewTime" :label="$t('JIT更新时间')">
          <mt-date-range-picker
            v-model="searchFormModel.renewTime"
            :placeholder="$t('请选择时间')"
            :show-clear-button="true"
            :change="(e) => dateChange(e, 'renewTimeList')"
          />
        </mt-form-item>
        <!-- 生产工单 -->
        <mt-form-item prop="workOrder" :label="$t('生产工单')" label-style="top">
          <mt-input style="flex: 1" v-model="searchFormModel.workOrder" :show-clear-button="true" />
        </mt-form-item>
        <!-- 标识 -->
        <mt-form-item prop="flag" :label="$t('标识')" label-style="top">
          <mt-select
            style="flex: 1"
            v-model="searchFormModel.flag"
            :data-source="flagList"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 是否JIT -->
        <mt-form-item prop="isJit" :label="$t('是否jit')" label-style="top">
          <mt-select
            style="flex: 1"
            v-model="searchFormModel.isJit"
            :data-source="isJitOptions"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 大数据平台唯一编号 -->
        <mt-form-item prop="uniqueCode" :label="$t('大数据平台唯一编号')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.uniqueCode"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 上版供应商编码 -->
        <mt-form-item prop="origSupplierCodeList" :label="$t('上版供应商编码')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.origSupplierCodeList"
            :data-source="origSupplierCodeList"
            :allow-filtering="true"
            filter-type="Contains"
            :multiple="true"
            :filtering="getOrigSupplierCodeList"
            :fields="{ text: 'theCodeName', value: 'supplierCode' }"
            :placeholder="$t('')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        class="xTable-class"
        :row-config="{ height: 130 }"
        :columns="columns"
        :table-data="tableData"
        show-overflow
        :min-height="600"
        border="none"
        header-align="left"
        row-class-name="table-row-class"
        align="center"
        style="padding-top: unset"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #checkTemplate="{ column, row }">
          <span class="span-style" v-if="typeof row[column.field] === 'object'">
            <span>{{ row[column.field]['systemBuyerNum'] }}</span>
            <span>{{ row[column.field]['handleBuyerNum'] }}</span>
          </span>
        </template>
        <template #isItemBatch="{ row }">
          <span>{{ [1, '1'].includes(row.isItemBatch) ? $t('是') : $t('否') }}</span>
        </template>
        <template #planDeliveryTime="{ row }">
          <span>{{ dateTransform(row.planDeliveryTime, 'time') }}</span>
        </template>
        <template #planDeliveryDate="{ row }">
          <span>{{ dateTransform(row.planDeliveryDate, 'date') }}</span>
        </template>
        <template #requestOrderMethod="{ row }">
          <span>{{ [1, '1'].includes(row.requestOrderMethod) ? $t('独立') : $t('集中') }}</span>
        </template>
        <!-- 配送方式 -->
        <template #deliveryMethod="{ row }">
          <span>{{ [0, '0'].includes(row.deliveryMethod) ? $t('直送') : $t('非直送') }}</span>
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { addCodeNameKeyInList } from '@/utils/utils'
import { utils } from '@mtech-common/utils'
import dayjs from 'dayjs'
import {
  deliveryMethodOptions,
  ToolBar,
  outsourcedTypeOptions,
  isJitOptions,
  columns,
  flagList
} from './config/constant'
import { getHeadersFileName, download, timeNumberToDate } from '@/utils/utils'
export default {
  components: {
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      toolbar: ToolBar,
      // 配送方式
      deliveryMethodOptions,
      // 委外方式
      outsourcedTypeOptions,
      // 是否Jit
      isJitOptions,
      flagList,
      warehouseCodeOptions: [], // 库存地点
      siteCodeListOptions: [], // 工厂下拉
      subSiteCodeListOptions: [], // 分厂下拉
      subSiteAddressCodeListOptions: [], // 分厂库存地点
      origSupplierCodeList: [], // 上版供应商下拉
      itemCodeOptions: [], // 物料下拉
      buyerOrgCodeOptions: [], // 采购组下拉
      supplierTypeList: [], // 供应商下拉
      processorCodeList: [], // 加工商下拉
      apiWaitingQuantity: 0, // 调用的api正在等待数
      searchFormModel: {
        renewTime: [],
        renewTimeList: [],
        origSupplierCodeList: [],
        warehouseCodeList: [],
        subSiteAddressCodeList: [],
        subSiteCodeList: [],
        siteCodeList: [],
        productLine: '',
        deliveryDate: [],
        deliveryDateList: [],
        buyerOrgName: '',
        rowCode: '',
        batchCode: '',
        workOrder: '',
        itemCodeList: [],
        buyerOrgCodeList: [],
        isJit: '',
        supplierCodeList: [],
        outsourcedType: '',
        deliveryMethod: '',
        processorCode: '',
        flag: ''
      },
      forecastPageCurrent: 1,
      syncVersion: '',
      titleList: [],
      tableData: [],
      columns,
      plannerListOptions: [], // 计划员 下列选项
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      isEditing: false, // 正在编辑数据
      isEdit: false
    }
  },
  mounted() {
    // 获取工厂，物料，供应商信息
    this.getSiteCodeList = utils.debounce(this.getSiteCodeList, 1000)
    this.getsubSiteCodeList = utils.debounce(this.getsubSiteCodeList, 1000)
    // 分厂库存地点
    this.getsubSiteAddressCode = utils.debounce(this.getsubSiteAddressCode, 1000)
    // 物料
    this.getItemCodeList = utils.debounce(this.getItemCodeList, 1000)
    // 获取采购组
    this.getBuyerOrgCodeList = utils.debounce(this.getBuyerOrgCodeList, 1000)
    // 供应商
    this.getSupplierList = utils.debounce(this.getSupplierList, 1000)
    this.getWarehouseCodeOptions = utils.debounce(this.getWarehouseCodeOptions, 1000)
    this.getOrigSupplierCodeList = utils.debounce(this.getOrigSupplierCodeList, 1000)
    this.getSiteCodeList()
    this.getsubSiteCodeList()
    this.getsubSiteAddressCode()
    this.getItemCodeList()
    this.getBuyerOrgCodeList()
    this.getSupplierList()
    this.getOrigSupplierCodeList()
    this.getWarehouseCodeOptions()
  },
  methods: {
    formaterDate(e) {
      console.log('123e', e)
      try {
        throw new Error(123)
      } catch (error) {
        console.log(error)
      }
      return true
    },
    // 时间戳转日期
    dateTransform(value, flag) {
      let formatString = 'Y-m-d'
      if (flag === 'time') {
        formatString = 'Y-m-d HH:MM:SS'
      }
      return timeNumberToDate({ value, formatString })
    },
    // 获取交货地址
    getWarehouseCodeOptions(args = { text: '' }) {
      const { text, updateData, setSelectData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .getLocationFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.warehouseCodeOptions = addCodeNameKeyInList({
              firstKey: 'externalCode',
              secondKey: 'externalName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.warehouseCodeOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取工厂信息
    getSiteCodeList(args = { text: '' }) {
      const { text } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteCodeListOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
          }
        })
        .catch(() => {})
    },
    // 分厂
    getsubSiteCodeList(args = { text: '' }) {
      const { text } = args
      const params = {
        condition: 'and',
        page: { current: 1, size: 20 },
        rules: [
          {
            condition: 'and',
            rules: [
              {
                condition: 'or',
                label: '',
                field: 'subSiteCode',
                type: 'string',
                operator: 'contains',
                value: text || ''
              }
            ]
          }
        ]
      }
      this.$API.report
        .getSubSiteCodeListOptions(params)
        .then((res) => {
          if (res) {
            const list = res?.data.records || []
            this.subSiteCodeListOptions = addCodeNameKeyInList({
              firstKey: 'subSiteCode',
              secondKey: 'subSiteName',
              list
            })
          }
        })
        .catch(() => {})
    },
    // 库存地点
    getsubSiteAddressCode(args = { text: '' }) {
      const { text, updateData, setSelectData } = args
      const params = {
        condition: 'and',
        page: { current: 1, size: 20 },
        rules: [
          {
            condition: 'and',
            rules: [
              {
                condition: 'or',
                label: '',
                field: 'subSiteAddress',
                type: 'string',
                operator: 'contains',
                value: text || ''
              }
            ]
          }
        ]
      }
      this.$API.report.getSubSiteAddressOptions(params).then((res) => {
        if (res) {
          const list = res?.data.records || []
          this.subSiteAddressCodeListOptions = addCodeNameKeyInList({
            firstKey: 'subSiteAddressCode',
            secondKey: 'subSiteAddress',
            list
          })
          if (updateData) {
            this.$nextTick(() => {
              updateData(this.warehouseOptions)
            })
          }
          if (setSelectData) {
            this.$nextTick(() => {
              setSelectData()
            })
          }
        }
      })
    },
    // // 获取物料信息
    // getItemCodeList(args = { text: '' }) {
    //   let obj = {
    //     fuzzyNameOrCode: args.text
    //   }

    //   this.$API.masterData.getCustomer(obj).then((res) => {
    //     res.data.forEach((item) => {
    //       item.name = item.customerCode + item.customerName
    //       item.code = item.customerEnterpriseId
    //     })e
    //     this.itemCodeOptions = res.data
    //   })
    // },
    getItemCodeList(e = { text: '' }) {
      const { text } = e
      //物料下拉
      let params = {
        keyword: text || '',
        pageSize: 50
      }
      this.$API.masterData.getItemByKeyword(params).then((res) => {
        const list = res.data?.records || []
        this.itemCodeOptions = addCodeNameKeyInList({
          firstKey: 'itemCode',
          secondKey: 'itemName',
          list
        })
      })
    },
    // 采购组下拉
    getBuyerOrgCodeList(args = { text: '' }) {
      const { text } = args
      const param = {
        fuzzyParam: text,
        page: {
          current: 1,
          size: 100
        }
      }
      this.$API.masterData
        .getbussinessGroup(param)
        .then((res) => {
          if (res && res.code === 200) {
            let list = res.data || []
            this.buyerOrgCodeOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            }).filter((n) => !!n.theCodeName)
          }
        })
        .catch(() => {})
    },
    // 获取供应商信息
    getSupplierList(args = { text: '' }) {
      const { text } = args
      const param = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData.getSupplier(param).then((res) => {
        this.supplierTypeList.length = 0
        const list = res.data || []
        this.supplierTypeList = addCodeNameKeyInList({
          firstKey: 'supplierCode',
          secondKey: 'supplierName',
          list
        }).filter((n) => !!n.theCodeName && !!n.supplierCode)
      })
    },
    // 上版供应商编码下拉
    getOrigSupplierCodeList(args = { text: '' }) {
      const { text } = args
      const param = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData.getSupplier(param).then((res) => {
        this.processorCodeList.length = 0
        const list = res.data || []
        this.origSupplierCodeList = addCodeNameKeyInList({
          firstKey: 'supplierCode',
          secondKey: 'supplierName',
          list
        }).filter((n) => !!n.theCodeName && !!n.supplierCode)
      })
    },
    // 需求日期更改
    dateChange(e, flag) {
      if (e && e.startDate) {
        let _startDate = dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        let _endDate = dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        this.searchFormModel[flag] = [dayjs(_startDate).valueOf(), dayjs(_endDate).valueOf()]
      } else {
        this.searchFormModel[flag] = []
      }
    },

    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      const commonToolbar = [
        'lose_efficacy',
        'activation',
        'Filter',
        'Delete',
        'Refresh',
        'refreshDataByLocal',
        'filterDataByLocal',
        'CloseEdit',
        'Setting'
      ]
      if (code === 'CloseEdit') {
        // 清除编辑状态
        this.$refs.xTable.$refs.xGrid.clearEdit()
        this.handleCustomSearch()
        return
      }
      if (this.isEdit) {
        // this.$toast({ content: this.$t('请先完成或结束编辑操作'), type: 'warning' })
        return
      }

      if (selectedRecords.length == 0 && commonToolbar.includes(code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })

      if (code === 'ForecastExport') {
        this.handleClickExport()
        return
      }
    },
    handleClickExport() {
      const param = {
        ...this.searchFormModel,
        page: {
          size: 5000,
          current: 1
        }
      }
      this.$store.commit('startLoading')
      this.$API.report.exportbatchLncomingMaterialDetails(param).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 保存操作
    handleSaveInfo(row) {
      this.$API.CategoryThresholdConfiguration.editCategoryThresholdConfigurationInfo(row)
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t(res.msg || '修改成功')
            })
            this.handleCustomSearch()
          }
          this.$t({
            type: 'error',
            content: this.$t(res.msg || '修改失败')
          })
        })
        .catch((e) => {
          this.$toast({
            type: 'error',
            content: this.$t(e.msg)
          })
        })
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'bigVersionNo') {
            this.searchFormModel[key] = this.versionList[0]
          }
        }
      }
      this.handleCustomSearch()
    },
    // 采方-获取信息列表
    handleCustomSearch() {
      this.isEdit = false
      const params = {
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        },
        ...this.searchFormModel
      }
      if (params.requireDate) {
        delete params.requireDate
      }
      if (params.renewTime) {
        delete params.renewTime
      }
      if (params.deliveryDate) {
        delete params.deliveryDate
      }
      this.apiStartLoading()
      this.$API.report
        .getbatchLncomingMaterialDetails(params)
        .then((res) => {
          if (res?.code == 200) {
            const total = res?.data?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.totalRecordsCount = Number(total)
            const records = res?.data?.records || [] // 表格数据
            // const _record = []
            records.map((item) => {
              if (item.dataItemList && item.dataItemList.length > 0) {
                item.dataItemList.forEach((n) => {
                  item[n.title] = {
                    systemBuyerNum: n.systemBuyerNum,
                    handleBuyerNum: n.handleBuyerNum
                  }
                })
              }
            })
            // 处理表数据
            this.tableData = records
          }
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  & .span-style {
    display: flex;
    flex-flow: column;
    & span:nth-child(1) {
      border-bottom: 1px solid #cfd4d9;
    }
  }
}
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
::v-deep .xTable-class .vxe-table .table-row-class td {
  height: 40px !important;
}
</style>
