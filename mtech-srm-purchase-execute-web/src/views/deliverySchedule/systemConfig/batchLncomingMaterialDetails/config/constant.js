import { i18n } from '@/main.js'
export const ConstDynamicTitleStr = 'title_' // 动态表格数据的 key 标识

// 业务组类型数字字典编码
export const BusinessGroupTypeDictCode = 'BUSINESSGROUP'

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  status: 'draft', // 状态 0 新建、1 已修改、2 待反馈、3 反馈正常、4 反馈异常、5 已确认
  categoryCode: '',
  categoryName: '',
  createUserName: '',
  createTime: '',
  updateUserName: '',
  updateTime: ''
}

export const ToolBar = [
  {
    code: 'ForecastExport',
    name: i18n.t('导出'),
    icon: 'vxe-icon-cloud-download',
    status: 'primary'
  }
]

// 配送方式
export const deliveryMethodOptions = [
  { label: i18n.t('直送'), value: '0' },
  { label: i18n.t('非直送'), value: '1' }
]

// 委外方式
export const outsourcedTypeOptions = [
  { label: i18n.t('标准委外'), value: '0' },
  { label: i18n.t('销售委外'), value: '1' },
  { label: i18n.t('标准采购'), value: '2' },
  { label: i18n.t('工序委外'), value: '3' }
]

export const isJitOptions = [
  { label: i18n.t('否'), value: 0 },
  { label: i18n.t('是'), value: 1 }
]

export const flagList = [
  { label: 'SRM已有叫料计划-不变', value: 0 },
  { label: 'SRM已有叫料计划-新增计划', value: 11 },
  { label: 'SRM已有叫料计划-减少数量', value: 12 },
  { label: 'SRM已有叫料计划-修改需求时间', value: 13 },
  { label: 'SRM已有叫料计划-取消或待定', value: 14 },
  { label: 'SRM已有叫料计划-变更供应商', value: 15 },
  { label: i18n.t('计划无任何变化'), value: 20 },
  { label: i18n.t('新增数量'), value: 21 },
  { label: i18n.t('减少数量'), value: 22 },
  { label: i18n.t('修改需求时间'), value: 23 },
  { label: i18n.t('变更供应商'), value: 25 }
]

// 选择 物料 弹框 表格列数据
export const columns = [
  {
    code: 'batchCode', // 版次号
    title: i18n.t('版次号')
  },
  {
    code: 'rowCode', // jit行编号
    title: i18n.t('jit行编号')
  },
  {
    code: 'productLine', // 生产线
    title: i18n.t('生产线')
  },
  {
    code: 'deliveryDate', // 交货日期
    title: i18n.t('交货日期')
  },
  {
    code: 'planGroupRemark', // 计划组备注
    title: i18n.t('计划组备注')
  },
  {
    code: 'siteCode', // 工厂编码
    title: i18n.t('工厂编码')
  },
  {
    code: 'siteName', // 工厂名称
    title: i18n.t('工厂名称')
  },
  {
    code: 'workCenterCode', // 工作中心编码
    title: i18n.t('工作中心编码')
  },
  {
    code: 'workCenter', // 工作中心
    title: i18n.t('工作中心')
  },
  {
    code: 'saleOrder', // 销售订单
    title: i18n.t('销售订单')
  },
  {
    code: 'itemCode', // 物料名称
    title: i18n.t('物料名称')
  },
  {
    code: 'supplierCode', // 供应商编码
    title: i18n.t('供应商编码')
  },
  {
    code: 'supplierName', // 供应商名称
    title: i18n.t('供应商名称')
  },
  {
    code: 'buyerOrgCode', // 采购组编码
    title: i18n.t('采购组编码')
  },
  {
    code: 'buyerOrgName', // 采购组名称
    title: i18n.t('采购组名称')
  },
  {
    code: 'companyCode', // 公司编码
    title: i18n.t('公司编码')
  },
  {
    code: 'companyCode', // 公司编码
    title: i18n.t('公司编码')
  },
  {
    code: 'companyName', // 公司名称
    title: i18n.t('公司名称')
  },
  {
    code: 'scheduleUserCode', // 调度员编码
    title: i18n.t('调度员编码')
  },
  {
    code: 'scheduleUserName', // 调度员姓名
    title: i18n.t('调度员姓名')
  },
  {
    code: 'bidNum', // 叫料数量
    title: i18n.t('叫料数量')
  },
  {
    code: 'tradeLocationCode', // 交货地点编码
    title: i18n.t('交货地点编码')
  },
  {
    code: 'tradeLocationName', // 交货地点名称
    title: i18n.t('交货地点名称')
  },
  {
    code: 'receiver', // 收货人
    title: i18n.t('收货人')
  },
  {
    code: 'contact', // 联系方式(SRM)
    title: i18n.t('联系方式(SRM)')
  },
  {
    code: 'renewTime', // JIT更新时间
    title: i18n.t('JIT更新时间')
  },
  {
    code: 'external', // 外部关联(SRM)
    title: i18n.t('外部关联(SRM)')
  },
  {
    code: 'remark', // 采方备注
    title: i18n.t('采方备注')
  },
  {
    code: 'requestOrderMethod', // 申请转化订单方式
    title: i18n.t('申请转化订单方式'),
    slots: {
      default: 'requestOrderMethod'
    }
  },
  {
    code: 'itemGroupCode', // 品项组编码
    title: i18n.t('品项组编码')
  },
  {
    code: 'itemGroupName', // 品项组名称
    title: i18n.t('品项组名称')
  },
  {
    code: 'warehouseCode', // 库存地点编码
    title: i18n.t('库存地点编码')
  },
  {
    code: 'warehouseName', // 库存地点名称
    title: i18n.t('库存地点名称')
  },
  {
    code: 'subSiteCode', // 分厂编码
    title: i18n.t('分厂编码')
  },
  {
    code: 'subSiteName', // 分厂名称
    title: i18n.t('分厂名称')
  },
  {
    code: 'subSiteAddressCode', // 分厂库存地点编码
    title: i18n.t('分厂库存地点编码')
  },
  {
    code: 'subSiteAddress', // 分厂库存地点
    title: i18n.t('分厂库存地点')
  },
  {
    code: 'deliveryMethod', // 配送方式
    title: i18n.t('配送方式'),
    slots: {
      default: 'deliveryMethod'
    }
  },
  {
    code: 'senderName', // 送货联系电话
    title: i18n.t('送货联系电话')
  },
  {
    code: 'senderAddress', // 送货地址
    title: i18n.t('送货地址')
  },
  {
    code: 'projectTextBatch', // 项目文本批次
    title: i18n.t('项目文本批次')
  },
  {
    code: 'outsourcedType', // 委外方式
    title: i18n.t('委外方式')
  },
  {
    code: 'receiptPartCode', // 收货方编码
    title: i18n.t('收货方编码')
  },
  {
    code: 'receiptPartName', // 收货方
    title: i18n.t('收货方')
  },
  {
    code: 'dispatcherRemark', // 调度员备注
    title: i18n.t('调度员备注')
  },
  {
    code: 'workOrder', // 生产工单
    title: i18n.t('生产工单')
  },
  {
    code: 'demandCode', // 净需求编码
    title: i18n.t('净需求编码')
  },
  {
    code: 'senderAddress', // 送货地址
    title: i18n.t('送货地址')
  },
  {
    code: 'isJit', // 是否JIT
    title: i18n.t('是否JIT')
  },
  {
    code: 'isItemBatch', // 是否批次来料
    title: i18n.t('是否批次来料'),
    slots: {
      default: 'isItemBatch'
    }
  },
  {
    code: 'flag', // 标识
    title: i18n.t('标识')
  },
  {
    code: 'materialFlag',
    title: i18n.t('是否散件')
  },
  {
    code: 'dealStatus',
    title: i18n.t('是否处理')
  },
  {
    code: 'uniqueCode', // 唯一编码大数据
    title: i18n.t('唯一编码大数据')
  },
  {
    code: 'versionNo', // 版本号
    title: i18n.t('版本号')
  },
  {
    code: 'remarkExplain', // 大数据备注
    title: i18n.t('大数据备注')
  },
  {
    code: 'origSupplierCode', // 上版供应商编码
    title: i18n.t('上版供应商编码')
  },
  {
    code: 'origSupplierName', // 上版供应商名称
    title: i18n.t('上版供应商名称')
  },
  {
    code: 'planDeliveryTime', // 计划交货时间
    title: i18n.t('上版交货时间'),
    slots: {
      default: 'planDeliveryTime'
    }
  },
  {
    code: 'planDeliveryDate', // 计划交货日期
    title: i18n.t('上版交货日期'),
    slots: {
      default: 'planDeliveryDate'
    }
  },
  {
    code: 'callMaterialQty', // 上版叫料数量
    title: i18n.t('上版叫料数量')
  },
  {
    code: 'errorInfo', // 错误信息
    title: i18n.t('错误信息')
  }
].map((item) => ({ ...item, title: item.title, field: item.code, width: 150, align: 'center' }))
