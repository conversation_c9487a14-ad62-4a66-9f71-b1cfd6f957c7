<!-- 采方-物料送货类型配置-物料查询 -->
<template>
  <mt-dialog
    ref="dialog"
    class="display-block"
    :header="dialogTitle"
    :height="900"
    :width="1400"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <div style="margin-top: 16px">
      <mt-template-page
        ref="templateRef"
        :template-config="componentConfig"
        @handleCustomReset="handleCustomReset"
        @handleClickToolBar="handleClickToolBar"
      >
        <template v-slot:quick-search-form>
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item :label="$t('工厂编码')" prop="siteCodeList">
              <RemoteAutocomplete
                v-model="searchFormModel.siteCodeList"
                :url="$API.masterData.getSiteListUrl"
                multiple
                :placeholder="$t('请选择')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                :search-fields="['siteName', 'siteCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('物料编码')" prop="itemCode">
              <mt-input
                v-model="itemCode"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                :show-clear-button="true"
                @change="itemCodeChange"
              />
            </mt-form-item>
            <mt-form-item :label="$t('物料名称')" prop="itemName">
              <mt-input
                v-model="searchFormModel.itemName"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item :label="$t('品类')" prop="categoryCodeList">
              <RemoteAutocomplete
                v-model="searchFormModel.categoryCodeList"
                url="/masterDataManagement/tenant/category/paged-query"
                multiple
                :placeholder="$t('请选择')"
                :fields="{ text: 'categoryName', value: 'categoryCode' }"
                :search-fields="['categoryName', 'categoryCode']"
              />
            </mt-form-item>
            <mt-form-item :label="$t('送货类型')" prop="deliveryTypeList">
              <mt-multi-select
                v-model="searchFormModel.deliveryTypeList"
                :data-source="deliveryTypeOptions"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
              />
            </mt-form-item>
          </mt-form>
        </template>
      </mt-template-page>
    </div>
  </mt-dialog>
</template>

<script>
import { componentConfig } from '../config'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      dialogTitle: '',
      searchFormModel: {},
      componentConfig: componentConfig(this),
      deliveryTypeOptions: [
        { text: this.$t('JIT'), value: 'JIT' },
        { text: this.$t('无'), value: 'NO' }
      ],

      itemCode: ''
    }
  },
  methods: {
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title } = args
      this.dialogTitle = title
    },
    beforeOpen() {},
    onOpen(args) {
      args.preventFocus = true
    },
    close() {},
    itemCodeChange(e) {
      if (e) {
        this.searchFormModel.itemCodeList = this.itemCode.split(' ')
      } else {
        this.searchFormModel.itemCodeList = null
      }
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.itemCode = ''
    },
    handleClickToolBar(e) {
      const { toolbar } = e
      switch (toolbar.id) {
        case 'export':
          this.handleExport()
          break
      }
    },
    handleExport() {
      this.$loading()
      const params = {
        page: {
          current: 1,
          size: 9999
        },
        ...this.searchFormModel
      }
      this.$API.deliverySchedule
        .exportItemMaterialDeliveryTypeConfigApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          this.$hloading()
        })
    }
  }
}
</script>
