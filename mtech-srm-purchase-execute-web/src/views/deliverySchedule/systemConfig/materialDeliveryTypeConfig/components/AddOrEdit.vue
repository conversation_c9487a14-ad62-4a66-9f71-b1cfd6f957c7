<!-- 采方-物料送货类型配置-新增or编辑 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-form-item prop="factoryCode" :label="$t('工厂')">
        <mt-select
          v-model="formData.factoryCode"
          :fields="{ text: 'theCodeName', value: 'siteCode' }"
          :data-source="siteOptions"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
          @change="siteChange"
        />
      </mt-form-item>
      <mt-form-item prop="materialCode" :label="$t('物料')">
        <mt-select
          v-model="formData.materialCode"
          :fields="{ text: 'theCodeName', value: 'itemCode' }"
          :data-source="itemOptions"
          :allow-filtering="true"
          :filtering="getItemOptions"
          :placeholder="$t('请选择物料')"
          @change="itemChange"
        />
      </mt-form-item>
      <mt-form-item prop="deliveryType" :label="$t('送货类型')">
        <mt-select
          v-model="formData.deliveryType"
          :data-source="deliveryTypeOptions"
          :allow-filtering="true"
          filter-type="Contains"
          :placeholder="$t('请选择')"
        />
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { addCodeNameKeyInList } from '@/utils/utils'
import { utils } from '@mtech-common/utils'
import { deliveryTypeOptions } from '../config'
export default {
  data() {
    return {
      dialogTitle: '',
      actionType: 'add',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {},
      rules: {
        factoryCode: [
          {
            required: true,
            message: this.$t('请选择工厂'),
            trigger: 'blur'
          }
        ],
        materialCode: [
          {
            required: true,
            message: this.$t('请选择物料'),
            trigger: 'blur'
          }
        ],
        deliveryType: [
          {
            required: true,
            message: this.$t('请选择送货类型'),
            trigger: 'blur'
          }
        ]
      },

      siteOptions: [],
      itemOptions: [],
      deliveryTypeOptions
    }
  },
  mounted() {
    this.getSiteOptions()
  },
  methods: {
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, row } = args
      this.dialogTitle = title
      this.actionType = actionType
      if (actionType === 'edit') {
        this.formData = row
      }
    },
    beforeOpen() {
      this.formData = {}
      this.$refs.ruleForm.clearValidate()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.handleSave()
        }
      })
    },
    handleSave() {
      let params = { ...this.formData }
      params.deliveryType = params.deliveryType !== '无' ? params.deliveryType : ''
      const api =
        this.actionType === 'add'
          ? this.$API.deliverySchedule.addMaterialDeliveryTypeConfigApi
          : this.$API.deliverySchedule.updateMaterialDeliveryTypeConfigApi
      api(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.actionType === 'add' ? this.$t('新增成功') : this.$t('更改成功'),
            type: 'success'
          })
          this.$emit('confirm')
          this.handleClose()
        }
      })
    },
    getSiteOptions() {
      let params = {
        orgCode: 'SITE'
      }
      this.$API.masterData.getOrgListByCode(params).then((res) => {
        if (res.code === 200) {
          this.siteOptions = res.data.map((item) => {
            return {
              theCodeName: item.dimensionCodeValue + '-' + item.dimensionNameValue,
              siteCode: item.dimensionCodeValue,
              siteName: item.dimensionNameValue
            }
          })
        }
      })
    },
    siteChange(e) {
      this.formData.factoryName = e.itemData?.siteName
      this.getItemOptions('', e.itemData?.siteCode)
      this.getItemOptions = utils.debounce(this.getItemOptions, 500)
    },
    itemChange(e) {
      this.formData.materialName = e.itemData?.itemName
    },
    getItemOptions(e = { text: '' }, factoryCode) {
      const { text } = e
      let siteCode = factoryCode || this.formData.factoryCode
      let params = {
        page: { current: 1, size: 50 },
        condition: 'and',
        rules: [
          {
            field: 'organizationCode',
            type: 'string',
            operator: 'contains',
            value: siteCode
          },
          {
            field: 'itemCode',
            type: 'string',
            operator: 'contains',
            value: text
          }
        ]
      }
      this.$API.Inventory.getItemforSite({ ...params }).then((res) => {
        if (res.code === 200) {
          const list = res?.data?.records || []
          this.itemOptions = addCodeNameKeyInList({
            firstKey: 'itemCode',
            secondKey: 'itemName',
            list
          })
          this.$nextTick(() => {
            if (e.updateData && typeof e.updateData == 'function') {
              e.updateData(this.itemOptions)
            }
          })
        }
      })
    }
  }
}
</script>
