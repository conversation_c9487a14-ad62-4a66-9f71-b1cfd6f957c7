<!-- 采方-物料送货类型配置 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('工厂编码')" prop="factoryCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.factoryCodeList"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料编码')" prop="materialCode">
          <mt-input
            v-model="searchFormModel.materialCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('送货类型')" prop="deliveryType">
          <mt-select
            v-model="searchFormModel.deliveryType"
            :data-source="deliveryTypeOptions"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('SAP同步状态')" prop="sapSyncStatus">
          <mt-multi-select
            v-model="searchFormModel.sapSyncStatus"
            :data-source="statusOptions"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('品类')" prop="categoryCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.categoryCodeList"
            url="/masterDataManagement/tenant/category/paged-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'categoryName', value: 'categoryCode' }"
            :search-fields="['categoryName', 'categoryCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('物料名称')" prop="materialName">
          <mt-input
            v-model="searchFormModel.materialName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('创建人')" prop="createUserName">
          <mt-input
            v-model="searchFormModel.createUserName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建日期')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            @change="(e) => dateTimeChange(e, 'CreateTime')"
            :placeholder="$t('请选择创建日期')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('更新人')" prop="updateUserName">
          <mt-input
            v-model="searchFormModel.updateUserName"
            :placeholder="$t('支持模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="updateTime" :label="$t('更新日期')">
          <mt-date-range-picker
            v-model="searchFormModel.updateTime"
            @change="(e) => dateTimeChange(e, 'UpdateTime')"
            :placeholder="$t('请选择更新日期')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="scTableRef"
      grid-id="498ffd77-7f7c-4170-89cd-7ceaca49f4b5"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :edit-config="{
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #deliveryTypeEdit="{ row }">
        <vxe-select
          v-model="row.deliveryType"
          :options="deliveryTypeOptions"
          transfer
          :placeholder="$t('请选择')"
        />
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />

    <AddOrEdit ref="addOrEditRef" @confirm="handleSearch" />
    <MaterialQuery ref="materialQueryRef" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, deliveryTypeOptions, statusOptions } from './config'
import { getHeadersFileName, download } from '@/utils/utils'
import AddOrEdit from './components/AddOrEdit.vue'
import MaterialQuery from './components/MaterialQuery.vue'
import { cloneDeep } from 'lodash'

export default {
  components: { CollapseSearch, ScTable, AddOrEdit, MaterialQuery },
  data() {
    return {
      searchFormModel: {},
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
        { code: 'save', name: this.$t('保存'), status: 'info', loading: false },
        // { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
        { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
        { code: 'materialQuery', name: this.$t('物料查询'), status: 'info', loading: false },
        { code: 'syncSap', name: this.$t('同步SAP'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      deliveryTypeOptions,
      statusOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel['start' + field] = dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        this.searchFormModel['end' + field] = dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
      } else {
        this.searchFormModel['start' + field] = null
        this.searchFormModel['end' + field] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      params.deliveryType = params.deliveryType !== '无' ? params.deliveryType : ''
      params.materialCodeList = params.materialCode !== '' ? params.materialCode?.split(' ') : []
      this.loading = true
      const res = await this.$API.deliverySchedule
        .pageMaterialDeliveryTypeConfigApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.materialDeliveryTypeDtoPage?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.materialDeliveryTypeDtoPage?.records || []
        this.tableData = records.map((item, index) => {
          item.deliveryType = item.deliveryType || this.$t('无')
          return {
            line: index + 1,
            ...item
          }
        })
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const updateRecords = this.tableRef.getUpdateRecords()
      if (e.code === 'save' && updateRecords.length === 0) {
        this.$toast({ content: this.$t('暂无需要保存的数据'), type: 'warning' })
        return
      }
      if (e.code === 'syncSap' && selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'save':
          this.handleSave(updateRecords)
          break
        case 'delete':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除？')
            },
            success: () => {
              this.handleDelete(selectedRecords)
            }
          })
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        case 'materialQuery':
          this.handleMaterialQuery()
          break
        case 'syncSap':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认同步SAP？')
            },
            success: () => {
              this.handleSyncSap(selectedRecords)
            }
          })
          break
        default:
          break
      }
    },
    handleAdd() {
      this.$refs.addOrEditRef.dialogInit({
        title: this.$t('新增'),
        actionType: 'add'
      })
    },
    handleSave(updateRecords) {
      let params = cloneDeep(updateRecords)
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认保存？')
        },
        success: () => {
          params.forEach((item) => {
            item.deliveryType = item.deliveryType !== '无' ? item.deliveryType : ''
          })
          this.$API.deliverySchedule.updateMaterialDeliveryTypeConfigApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({
                content: this.$t('更改成功'),
                type: 'success'
              })
              this.handleSearch()
            }
          })
        }
      })
    },
    handleDelete(selectedRecords) {
      let params = {
        queryReq: {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        },
        chooseReqList: []
      }
      if (selectedRecords.length !== 0) {
        selectedRecords.forEach((item) => {
          let obj = {
            id: item.id
          }
          params.chooseReqList.push(obj)
        })
      }
      this.$API.deliverySchedule.deleteMaterialDeliveryTypeConfigApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.deliverySchedule.importMaterialDeliveryTypeConfigApi,
          downloadTemplateApi: this.$API.deliverySchedule.downloadMaterialDeliveryTypeConfigApi,
          paramsKey: 'excel'
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      params.deliveryType = params.deliveryType !== '无' ? params.deliveryType : ''
      params.materialCodeList = params.materialCode !== '' ? params.materialCode?.split(' ') : []
      this.$API.deliverySchedule
        .exportMaterialDeliveryTypeConfigApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    handleMaterialQuery() {
      this.$refs.materialQueryRef.dialogInit({
        title: this.$t('物料查询')
      })
    },
    handleSyncSap(selectedRecords) {
      let params = {
        queryReq: {
          page: {
            current: this.currentPage,
            size: this.pageSettings.pageSize
          },
          ...this.searchFormModel
        },
        chooseReq: []
      }
      if (selectedRecords.length !== 0) {
        selectedRecords.forEach((item) => {
          let obj = {
            id: item.id
          }
          params.chooseReq.push(obj)
        })
      }
      this.$API.deliverySchedule.syncSapMaterialDeliveryTypeConfigApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('同步成功'), type: 'success' })
          this.handleSearch()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  div .vxe-cell-border {
    border: solid #e6e9ed 1px;
    height: 32px;
    line-height: 32px;
    box-sizing: border-box;
  }
}

/deep/ .vxe-cell .vxe-default-select {
  background: #fff;
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
