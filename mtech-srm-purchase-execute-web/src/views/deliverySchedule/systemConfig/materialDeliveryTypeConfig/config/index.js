import { i18n } from '@/main.js'
import dayjs from 'dayjs'

export const deliveryTypeOptions = [
  { label: i18n.t('JIT'), text: i18n.t('JIT'), value: 'JIT' },
  { label: i18n.t('无'), text: i18n.t('无'), value: '无' }
]

export const statusOptions = [
  { text: i18n.t('未同步'), value: 0 },
  { text: i18n.t('同步中'), value: 1 },
  { text: i18n.t('同步成功'), value: 2 },
  { text: i18n.t('同步失败'), value: 3 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'sapSyncStatus',
    title: i18n.t('SAP同步状态'),
    minWidth: 140,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'sapBackInfo',
    title: i18n.t('SAP返回信息'),
    minWidth: 200
  },
  {
    field: 'factoryCode',
    title: i18n.t('工厂编码'),
    minWidth: 120
  },
  {
    field: 'factoryName',
    title: i18n.t('工厂名称'),
    minWidth: 200
  },
  {
    field: 'materialCode',
    title: i18n.t('物料编码'),
    minWidth: 160
  },
  {
    field: 'materialName',
    title: i18n.t('物料名称'),
    minWidth: 200
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类编码'),
    minWidth: 120
  },
  {
    field: 'categoryName',
    title: i18n.t('品类名称'),
    minWidth: 200
  },
  {
    field: 'deliveryType',
    title: i18n.t('送货类型'),
    minWidth: 120,
    editRender: {},
    slots: {
      edit: 'deliveryTypeEdit'
    }
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    minWidth: 120
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人'),
    minWidth: 120
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  }
]

export const materialColumnData = () => {
  return [
    {
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      width: 120
    },
    {
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      width: 200
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      width: 120
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      width: 200
    },
    {
      field: 'categoryCode',
      headerText: i18n.t('品类编码'),
      width: 120
    },
    {
      field: 'categoryName',
      headerText: i18n.t('品类名称'),
      width: 200
    },
    {
      field: 'deliveryType',
      headerText: i18n.t('送货类型'),
      width: 120
    }
  ]
}

export const componentConfig = (that) => {
  const config = [
    {
      activatedRefresh: false,
      isUseCustomSearch: true, // 是否使用自定义查询
      isCustomSearchRules: true,
      isUseCustomEditor: true,
      useToolTemplate: false, // 此项不使用预置的表格操作按钮'新增、编辑、删除'
      useBaseConfig: false, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
      toolbar: {
        tools: [
          [
            {
              id: 'export',
              // permission: [''],
              title: i18n.t('导出')
            }
          ],
          ['Setting']
        ]
      },
      gridId: '5eec0878-3e08-4763-a477-20b7edb456c5',
      grid: {
        allowPaging: true, // 是否使用内置分页器
        allowEditing: false, //开启表格编辑操作
        virtualPageSize: 30,
        enableVirtualization: true,
        columnData: materialColumnData(that),
        asyncConfig: {
          url: '/masterDataManagement/common/item/org/rel/query'
        }
      }
    }
  ]
  return config
}
