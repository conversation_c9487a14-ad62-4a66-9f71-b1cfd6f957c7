<template>
  <div class="orderConfig full-height">
    <!-- 工作中心与加工商配置 -->
    <mt-template-page
      ref="template-0"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <!-- 配置新增/编辑弹框 -->
    <config-list-dialog ref="configListDialog" @updateList="updateList"></config-list-dialog>
  </div>
</template>
<script>
import { checkColumn, lastColumn } from './config/config'
import { BASE_TENANT } from '@/utils/constant'
export default {
  components: {
    ConfigListDialog: () => import('./components/configListDialog')
  },
  data() {
    return {
      downTemplateName: this.$t('工作中心与加工商配置配置模板'),
      downTemplateParams: {}, // 下载模板参数
      uploadParams: {}, // 明细行上传excel的
      requestUrls: {},
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              title: this.$t('新增'),
              permission: ['O_02_0484']
            },
            {
              id: 'Delete',
              icon: 'icon_solid_Delete',
              title: this.$t('删除'),
              permission: ['O_02_0486']
            }
          ],
          gridId: this.$tableUUID.purchaseSchedule.workCenterConverterTab,
          grid: {
            // height: "auto",
            // frozenColumns: 1,
            columnData: checkColumn.concat(lastColumn),
            asyncConfig: {
              url: `${BASE_TENANT}/workCenterSupplierRel/query`,
              serializeList: (list) => {
                list.forEach((item, index) => {
                  item.selfIndex = index + 1
                  item.workCenterCode = ''
                  let list = item.workCenter.split('_')
                  item.workCenterName = ''
                  if (list.length === 2) {
                    item.workCenterCode = list[0]
                    item.workCenterName = list[1]
                  }
                })
                return list
              }
            }
          }
        }
      ]
    }
  },
  methods: {
    //点击顶部的操作按钮
    handleClickToolBar(e) {
      console.log('方法1', e)
      console.log(e.gridRef.getMtechGridRecords(), e.toolbar.id)
      if (e.toolbar.id === 'Add') {
        this.handleAdd()
        return
      }
      if (e.toolbar.id === 'Import') {
        this.handleImport()
        return
      }
      if (e.toolbar.id === 'Export1') {
        this.handleExport()
        return
      }
      let selectRecords = e.gridRef.getMtechGridRecords()
      if (!selectRecords.length) {
        this.$toast({ content: this.$t('请至少选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Delete') {
        this.handleDelete(selectRecords)
      }
      if (e.toolbar.id === 'Enable') {
        this.handleEnable(selectRecords)
      }
      if (e.toolbar.id === 'Disable') {
        this.handleDisable(selectRecords)
      }
    },
    updateList() {
      //更新数据
      this.$refs[`template-0`].refreshCurrentGridData()
    },
    handleAdd() {
      //新增
      this.$refs.configListDialog.dialogInit({ title: this.$t('新增') })
    },
    handleDelete(row) {
      //删除
      this.$dialog({
        data: {
          title: this.$t('提醒'),
          message: this.$t('确认删除选中的工作中心与加工商配置配置吗?')
        },
        success: () => {
          let ids = row.map((item) => item.id)
          this.$API.deliverySchedule.workCenterSupplierReldelete(ids).then(() => {
            this.$toast({
              content: this.$t('删除工作中心与加工商配置配置操作成功'),
              type: 'success'
            })
            this.updateList()
          })
        }
      })
    },
    handleEdit(row) {
      //编辑
      this.$refs.configListDialog.dialogInit({
        title: this.$t('编辑'),
        row: row
      })
    },
    //点击表格的操作按钮
    handleClickCellTool(e) {
      console.log('方法2', e)
      if (e.tool.id === 'edit') {
        this.handleEdit(e.data)
      }
      if (e.tool.id === 'delete') {
        this.handleDelete([e.data])
      }
      if (e.tool.id === 'enable') {
        this.handleEnable([e.data])
      }
      if (e.tool.id === 'disable') {
        this.handleDisable([e.data])
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.orderConfig {
}
</style>
