<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <mt-form-item prop="workCenterCode" :label="$t('工作中心')">
        <mt-select
          v-model="ruleForm.workCenterCode"
          :data-source="workCenterOptions"
          :fields="{ text: 'label', value: 'workCenterCode' }"
          :placeholder="$t('请选择')"
          :allow-filtering="true"
          :filtering="serchText1"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="supplierCode" :label="$t('加工商')">
        <mt-select
          v-model="ruleForm.supplierCode"
          :data-source="supplierOptions"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
          :fields="{ text: 'label', value: 'supplierCode' }"
          :filtering="serchText2"
        ></mt-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      workCenterOptions: [], // 工作中心 下列选项
      supplierOptions: [], // 加工商 下列选项
      dialogTitle: '',
      rules: {
        workCenterCode: [
          {
            required: true,
            message: this.$t('请选择工作中心'),
            trigger: 'blur'
          }
        ],
        supplierCode: [{ required: true, message: this.$t('请选择加工商'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {
        id: '',
        workCenterCode: '',
        supplierCode: ''
      }
    }
  },
  mounted() {
    this.getOptions()
    this.getSupplier = utils.debounce(this.getSupplier, 1000)
    this.getWorkCenter = utils.debounce(this.getWorkCenter, 1000)
  },
  methods: {
    serchText1(val) {
      console.log('搜索值', val)
      this.getWorkCenter(val && val.text ? val.text : '')
    },
    getWorkCenter(val, entryFirst) {
      let str = val || this.ruleForm.workCenterCode
      let params = {
        keyword: str || ''
      }
      this.$API.masterData.getWorkCenter(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.workCenterCode}-${item.workCenterName}`
        })
        this.workCenterOptions = res.data || []
        if (entryFirst === '1') {
          this.ruleForm.workCenterCode = this.entryInfo.row.workCenterCode
        }
      })
    },
    serchText2(val) {
      console.log('搜索值', val)
      this.getSupplier(val && val.text ? val.text : '')
    },
    getSupplier(val, entryFirst) {
      //查询供应商的数据
      let str = val || this.ruleForm.supplierCode
      let params = {
        fuzzyNameOrCode: str || ''
      }
      this.$API.masterData.getSupplier(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.supplierCode}-${item.supplierName}`
        })
        this.supplierOptions = res.data || []
        if (entryFirst === '1') {
          this.ruleForm.supplierCode = this.entryInfo.row.supplierCode
        }
      })
    },
    getOptions() {
      // this.$API.masterData.getWorkCenter().then((res) => {
      //   this.workCenterOptions = res.data || [];
      // });
      // //供应商下拉
      // this.$API.masterData.getSupplier().then((res) => {
      //   this.supplierOptions = res.data || [];
      // });
    },
    // 初始化
    dialogInit(entryInfo) {
      this.dialogTitle = entryInfo.title
      if (this.dialogTitle === this.$t('新增')) {
        this.ruleForm = {
          workCenterCode: '',
          supplierCode: ''
        }
        this.getSupplier()
        this.getWorkCenter()
      }
      if (this.dialogTitle === this.$t('编辑')) {
        this.entryInfo = entryInfo
        this.ruleForm = {
          workCenterCode: null,
          id: entryInfo.row.id,
          supplierCode: null
        }
        this.getWorkCenter(entryInfo.row.workCenterCode, '1')
        this.getSupplier(entryInfo.row.supplierCode, '1')
      }
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            supplierCode: this.ruleForm.supplierCode,
            supplierId: this.supplierOptions.find((item) => {
              return item.supplierCode === this.ruleForm.supplierCode
            })?.id,
            supplierName: this.supplierOptions.find((item) => {
              return item.supplierCode === this.ruleForm.supplierCode
            })?.supplierName,
            workCenter:
              this.ruleForm.workCenterCode +
              '_' +
              this.workCenterOptions.find((item) => {
                return item.workCenterCode === this.ruleForm.workCenterCode
              })?.workCenterName
          }
          if (this.dialogTitle === this.$t('编辑')) {
            params.id = this.ruleForm.id
            this.$API.deliverySchedule.workCenterSupplierRelupdate(params).then(() => {
              this.$refs.dialog.ejsRef.hide()
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$emit('updateList')
            })
          }
          if (this.dialogTitle === this.$t('新增')) {
            this.$API.deliverySchedule.workCenterSupplierRelsave(params).then(() => {
              this.$refs.dialog.ejsRef.hide()
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$emit('updateList')
            })
          }
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
<style lang="scss" scoped></style>
