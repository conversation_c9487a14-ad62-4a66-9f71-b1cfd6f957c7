// import UTILS from "@/utils/utils";
import { i18n } from '@/main.js'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant'

export const checkColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  }
]
export const lastColumn = [
  {
    ignore: true,
    field: 'selfIndex',
    headerText: i18n.t('序号'),
    width: '150',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        permission: ['O_02_0485'],
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data
        }
      },
      {
        permission: ['O_02_0486'],
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data
        }
      }
    ]
  },
  {
    field: 'workCenterName',
    headerText: i18n.t('工作中心'),
    width: '250',
    searchOptions: {
      ...MasterDataSelect.workCenter,
      renameField: 'workCenterCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.workCenterCode}}-{{data.workCenterName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'supplierName',
    headerText: i18n.t('加工商'),
    width: '300',
    searchOptions: {
      ...MasterDataSelect.supplier,
      renameField: 'supplierCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.supplierCode}}-{{data.supplierName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    width: '150'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    width: '150'
  }
]
