<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <mt-form-item prop="configGroupType" :label="$t('配置方式')">
        <mt-select
          :open-dispatch-change="false"
          v-model="ruleForm.configGroupType"
          :data-source="configGroupTypeOptions"
          :placeholder="$t('请选择')"
          @change="configGroupTypeChange"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="buyerOrgCode" :label="$t('采购组')" v-if="ruleForm.configGroupType === 1">
        <mt-select
          :allow-filtering="true"
          v-model="ruleForm.buyerOrgCode"
          :data-source="buyerOrgCodeOptions"
          :fields="{ text: 'label', value: 'groupCode' }"
          :placeholder="$t('请选择')"
          :filtering="getbussinessGroup"
          @open="startOpen"
        ></mt-select>
      </mt-form-item>
      <mt-form-item
        prop="planGroupCode"
        :label="$t('计划组')"
        v-if="ruleForm.configGroupType === 2"
      >
        <mt-select
          :allow-filtering="true"
          v-model="ruleForm.planGroupCode"
          :data-source="planGroupCodeOptions"
          :fields="{ text: 'label', value: 'groupCode' }"
          :placeholder="$t('请选择')"
          :filtering="getbussinessGroup1"
          @open="startOpen1"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="siteCode" :label="$t('工厂')">
        <mt-select
          v-model="ruleForm.siteCode"
          :data-source="siteCodeOptions"
          :fields="{ text: 'label', value: 'siteCode' }"
          :placeholder="$t('请选择')"
          :allow-filtering="true"
          :filtering="postSiteFuzzyQuery"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="supplierCode" :label="$t('供应商')">
        <mt-select
          v-model="ruleForm.supplierCode"
          :data-source="supplierOptions"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
          :fields="{ text: 'label', value: 'supplierCode' }"
          :filtering="serchText"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="timeInfo" :label="$t('排期频率')">
        <mt-multi-select
          v-model="ruleForm.timeInfo"
          :data-source="timeInfoOptions"
          :placeholder="$t('请选择')"
          :show-clear-button="true"
        ></mt-multi-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
// import bigDecimal from "js-big-decimal";
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      planGroupCodeOptions: [], // 计划组下列选项
      siteCodeOptions: [], // 工厂 下列选项
      buyerOrgCodeOptions: [], // 公司 下列选项
      supplierOptions: [], // 供应商 下列选项
      timeInfoOptions: [
        { text: this.$t('周一'), value: '1' },
        { text: this.$t('周二'), value: '2' },
        { text: this.$t('周三'), value: '3' },
        { text: this.$t('周四'), value: '4' },
        { text: this.$t('周五'), value: '5' },
        { text: this.$t('周六'), value: '6' },
        { text: this.$t('周日'), value: '7' }
      ],
      configGroupTypeOptions: [
        { text: '采购组+工厂+供应商', value: 1 },
        { text: '计划组+工厂+供应商', value: 2 }
      ], // 配置方式 下拉数据
      dialogTitle: '',
      rules: {
        configGroupType: [{ required: true, message: this.$t('请选择配置方式'), trigger: 'blur' }],
        buyerOrgCode: [{ required: true, message: this.$t('请选择采购组'), trigger: 'blur' }],
        planGroupCode: [{ required: true, message: this.$t('请选择计划组'), trigger: 'blur' }],
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        timeInfo: [{ required: true, message: this.$t('请选择排期频率'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {
        id: '',
        configGroupType: '', //配置方式
        buyerOrgCode: '',
        planGroupCode: '',
        siteCode: '',
        status: 1,
        supplierCode: '',
        timeInfo: []
      }
    }
  },
  mounted() {
    this.getOptions()
    this.postSiteFuzzyQuery = utils.debounce(this.postSiteFuzzyQuery, 1000)
    this.getSupplier = utils.debounce(this.getSupplier, 1000)
    this.getbussinessGroup = utils.debounce(this.getbussinessGroup, 1000)
    this.getbussinessGroup1 = utils.debounce(this.getbussinessGroup1, 1000)
  },
  methods: {
    startOpen() {
      if (!this.buyerOrgCodeOptions.length) {
        this.getbussinessGroup()
      }
    },
    startOpen1() {
      if (!this.planGroupCodeOptions.length) {
        this.getbussinessGroup1()
      }
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args, entryFirst) {
      const { text, updateData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteCodeOptions = list.map((item) => {
              return {
                ...item,
                name: item.siteName,
                label: `${item.siteCode}-${item.siteName}`
              }
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.siteCodeOptions)
              })
            }
            if (entryFirst === '1') {
              this.ruleForm.siteCode = this.editInfo.siteCode
            }
          }
        })
        .catch(() => {})
    },
    serchText(val) {
      console.log(this.$t('搜索值'), val)
      this.getSupplier(val && val.text ? val.text : '')
    },
    getSupplier(val, entryFirst) {
      //查询供应商的数据
      let str = val || this.supplierCode
      let params = {
        fuzzyNameOrCode: str || ''
      }
      this.$API.masterData.getSupplier(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.supplierCode}-${item.supplierName}`
        })
        this.supplierOptions = list
        if (entryFirst === '1') {
          this.ruleForm.supplierCode = this.editInfo.supplierCode
        }
      })
    },
    configGroupTypeChange(val) {
      console.log(val?.value, '我改变')
      if (val?.value === 1) {
        this.buyerOrgCodeOptions = []
        this.ruleForm.buyerOrgCode = ''
      }
      if (val?.value === 2) {
        this.planGroupCodeOptions = []
        this.ruleForm.planGroupCode = ''
      }
    },
    getbussinessGroup(val, entryFirst) {
      console.log(val, '我是采购组')
      let str = val?.text || ''
      let params = {
        fuzzyParam: str,
        groupTypeCode: 'BG001CG'
      }
      this.$API.masterData.getbussinessGroup(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.groupCode}-${item.groupName}`
        })
        this.buyerOrgCodeOptions = list
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.buyerOrgCodeOptions)
          })
        }
        if (entryFirst === '1') {
          this.ruleForm.buyerOrgCode = this.editInfo.buyerOrgCode
        }
      })
    },
    getbussinessGroup1(val, entryFirst) {
      console.log(val, '我是计划组')
      let str = val?.text || ''
      let params = {
        fuzzyParam: str,
        groupTypeCode: 'BG001JH'
      }
      this.$API.masterData.getbussinessGroup(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.groupCode}-${item.groupName}`
        })
        this.planGroupCodeOptions = list
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.planGroupCodeOptions)
          })
        }
        if (entryFirst === '1') {
          this.ruleForm.planGroupCode = this.editInfo.planGroupCode
        }
      })
    },
    getOptions() {
      // 计划组下拉
      // this.$API.masterData
      //   .getbussinessGroup({
      //     groupTypeName: this.$t("计划组"),
      //   })
      //   .then((res) => {
      //     this.planGroupCodeOptions = res.data || [];
      //   });
      // 采购组下拉
      // this.$API.masterData
      //   .getbussinessGroup({
      //     groupTypeCode: "BG001CG",
      //   })
      //   .then((res) => {
      //     this.buyerOrgCodeOptions = res.data || [];
      //   });
      // //工厂
      // this.$API.masterData.findOrgSiteInfo({}).then((res) => {
      //   this.siteCodeOptions = res.data || [];
      // });
      // //供应商下拉
      // this.$API.masterData.getSupplier().then((res) => {
      //   this.supplierOptions = res.data || [];
      // });
    },
    // 初始化
    dialogInit(entryInfo) {
      this.dialogTitle = entryInfo.title
      if (this.dialogTitle === this.$t('新增')) {
        this.ruleForm = {
          id: '',
          configGroupType: '', //配置方式
          buyerOrgCode: '',
          planGroupCode: '',
          siteCode: '',
          status: 1,
          supplierCode: null,
          timeInfo: []
        }
        this.postSiteFuzzyQuery({ text: undefined })
        this.getSupplier()
        this.getbussinessGroup()
        this.getbussinessGroup1()
      }
      if (this.dialogTitle === this.$t('编辑')) {
        this.editInfo = entryInfo.row
        this.ruleForm = {
          configGroupType: entryInfo.row.configGroupType, //配置方式
          buyerOrgCode: null,
          planGroupCode: null,
          siteCode: null,
          status: entryInfo.row.status,
          supplierCode: null,
          timeInfo: entryInfo.row.timeInfo.split(';'),
          id: entryInfo.row.id
        }
        this.postSiteFuzzyQuery({ text: entryInfo.row.siteCode }, '1')
        this.getSupplier(entryInfo.row.supplierCode, '1')
        this.getbussinessGroup({ text: entryInfo.row.buyerOrgCode }, '1')
        this.getbussinessGroup1({ text: entryInfo.row.planGroupCode }, '1')
      }
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            configGroupType: this.ruleForm.configGroupType, //配置方式
            buyerOrgCode: this.ruleForm.buyerOrgCode,
            buyerOrgName: this.buyerOrgCodeOptions.find((item) => {
              return item.groupCode === this.ruleForm.buyerOrgCode
            })?.groupName,
            planGroupCode: this.ruleForm.planGroupCode,
            planGroupName: this.planGroupCodeOptions.find((item) => {
              return item.groupCode === this.ruleForm.planGroupCode
            })?.groupName,
            siteCode: this.ruleForm.siteCode,
            siteName: this.siteCodeOptions.find((item) => {
              return item.siteCode === this.ruleForm.siteCode
            })?.siteName,
            status: this.ruleForm.status,
            supplierCode: this.ruleForm.supplierCode,
            supplierName: this.supplierOptions.find((item) => {
              return item.supplierCode === this.ruleForm.supplierCode
            })?.supplierName,
            timeInfo: this.ruleForm.timeInfo.join(';')
          }
          if (params.configGroupType === 1) {
            params.planGroupCode = ''
            params.planGroupName = ''
          }
          if (params.configGroupType === 2) {
            params.buyerOrgCode = ''
            params.buyerOrgName = ''
          }
          if (this.dialogTitle === this.$t('编辑')) {
            params.id = this.ruleForm.id
            this.$API.deliverySchedule.buyerReleaseFrequencyConfigureupdate(params).then(() => {
              this.$refs.dialog.ejsRef.hide()
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$emit('updateList')
            })
          }
          if (this.dialogTitle === this.$t('新增')) {
            this.$API.deliverySchedule.buyerReleaseFrequencyConfiguresave(params).then(() => {
              this.$refs.dialog.ejsRef.hide()
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$emit('updateList')
            })
          }
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
<style lang="scss" scoped></style>
