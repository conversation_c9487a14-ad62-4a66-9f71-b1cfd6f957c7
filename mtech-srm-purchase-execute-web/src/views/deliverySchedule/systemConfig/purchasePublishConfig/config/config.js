// import UTILS from "@/utils/utils";
import { i18n } from '@/main.js'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant'
export const checkColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  }
]
const timeInfoOptions = [
  { text: i18n.t('周一'), value: '1' },
  { text: i18n.t('周二'), value: '2' },
  { text: i18n.t('周三'), value: '3' },
  { text: i18n.t('周四'), value: '4' },
  { text: i18n.t('周五'), value: '5' },
  { text: i18n.t('周六'), value: '6' },
  { text: i18n.t('周日'), value: '7' }
]
export const lastColumn = [
  {
    field: 'selfIndex',
    headerText: i18n.t('序号'),
    width: '150',
    ignore: true,
    cellTools: [
      {
        permission: ['O_02_0467'],
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data
        }
      },
      {
        permission: ['O_02_0468'],
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data
        }
      }
    ]
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('启用'), cssClass: 'col-active' },
        { value: 0, text: i18n.t('停用'), cssClass: 'col-inactive' }
      ]
    },
    cellTools: [
      {
        permission: ['O_02_0470'],
        id: 'disable',
        icon: '',
        title: i18n.t('停用'),
        visibleCondition: (data) => data.status === 1
      },
      {
        permission: ['O_02_0454'],
        id: 'enable',
        icon: '',
        title: i18n.t('启用'),
        visibleCondition: (data) => data.status === 0
      }
    ]
  },
  {
    field: 'configGroupType',
    headerText: i18n.t('配置方式'),
    width: '250',
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('采购组+工厂+供应商'), 2: i18n.t('计划组+工厂+供应商') }
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '250',
    searchOptions: {
      ...MasterDataSelect.factoryAddress,
      renameField: 'siteCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.siteCode}}-{{data.siteName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'buyerOrgName',
    headerText: i18n.t('采购组'),
    width: '250',
    searchOptions: {
      ...MasterDataSelect.businessGroup,
      renameField: 'buyerOrgCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'planGroupName',
    headerText: i18n.t('计划组'),
    width: '250',
    searchOptions: {
      ...MasterDataSelect.businessGroup,
      renameField: 'planGroupCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.planGroupCode}}-{{data.planGroupName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商'),
    width: '300',
    searchOptions: {
      ...MasterDataSelect.supplier,
      renameField: 'supplierCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.supplierCode}}-{{data.supplierName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'timeInfo',
    headerText: i18n.t('排期频率'),
    width: '250',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        let list = e.split(';')
        let list1 = []
        list.forEach((item) => {
          timeInfoOptions.some((item1) => {
            if (item == item1.value) {
              list1.push(item1.text)
            }
          })
        })
        let str = list1.join(';')
        return str
      }
    }
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    width: '150'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    width: '150'
  }
]
