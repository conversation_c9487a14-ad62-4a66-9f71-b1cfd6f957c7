<!-- 物料选择 -->
<template>
  <mt-dialog ref="dialog" css-class="dialog-main" width="80%" :buttons="buttons" :header="header">
    <div class="dialog-content">
      <mt-template-page
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="pageConfig"
        @recordDoubleClick="recordDoubleClick"
      />
    </div>
  </mt-dialog>
</template>

<script>
import { PAGE_CONFIG } from './config/materialSelect.js'
export default {
  data() {
    return {
      header: '',
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: PAGE_CONFIG,
      selectedRow: {}
    }
  },
  methods: {
    recordDoubleClick(args) {
      const { rowData } = args
      this.selectedRow.itemCode = rowData.itemCode
      this.selectedRow.itemName = rowData.itemName
      this.$emit('confirm', this.selectedRow)
      this.cancel()
    },
    open(args) {
      const { title, id } = args
      this.header = title
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem(
          'currentBu'
        )}`,
        params: {
          condition: 'and',
          tenantId: '10000',
          defaultRules: [
            {
              field: 'organizationId',
              operator: 'equal',
              type: 'long',
              value: id
            }
          ]
        },
        serializeList: (list) => {
          return list.map((item) => {
            return {
              ...item,
              categoryCode: item?.categoryResponse?.categoryCode ?? ''
            }
          })
        }
      })
      this.$refs['dialog'].ejsRef.show()
    },
    confirm() {
      let _records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      this.selectedRow.itemCode = _records[0].itemCode
      this.selectedRow.itemName = _records[0].itemName
      this.$emit('confirm', this.selectedRow)
      this.cancel()
    },
    cancel() {
      this.$refs['dialog'].ejsRef.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-main {
  .e-dlg-content {
    padding: 0;
    .dialog-content {
      padding: 20px;
    }
  }
}
</style>
