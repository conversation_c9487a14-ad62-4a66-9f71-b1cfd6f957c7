import { i18n } from '@/main.js'

const columnData = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类')
  },
  {
    field: 'itemDescription',
    headerText: i18n.t('规格型号')
  },
  {
    field: 'oldItemCode',
    headerText: i18n.t('旧物料编号')
  },
  {
    field: 'manufacturerName',
    headerText: i18n.t('制造商')
  }
]

export const PAGE_CONFIG = [
  {
    toolbar: [],
    useToolTemplate: false,
    grid: {
      height: 350,
      allowPaging: true,
      allowSelection: true,
      selectionSettings: {
        checkboxOnly: false
      },
      columnData,
      asyncConfig: {}
    }
  }
]
