<!-- 新增/编辑 -->
<template>
  <div>
    <mt-dialog
      ref="dialog"
      css-class="dialog-main"
      width="30%"
      :header="header"
      :buttons="buttons"
      @beforeClose="cancel"
    >
      <div class="dialog-content">
        <mt-form class="dialog-mt-form" ref="dialogRef" :model="formObject" :rules="formRules">
          <mt-row>
            <mt-col :span="24">
              <mt-form-item prop="companyCode" :label="$t('公司')">
                <mt-select
                  v-if="headStates === 'Add'"
                  v-model="formObject.companyCode"
                  :data-source="companyOptions"
                  float-label-type="Never"
                  :fields="{ text: 'orgName', value: 'orgCode' }"
                  :placeholder="$t('请选择公司')"
                  :allow-filtering="true"
                  :filtering="getCompanyOptions"
                  :disabled="headStates !== 'Add'"
                  width="100%"
                  @change="companyChange"
                />
                <mt-input
                  v-else
                  v-model="formObject.companyName"
                  float-label-type="Never"
                  :disabled="true"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="24">
              <mt-form-item prop="siteCode" :label="$t('工厂')">
                <mt-select
                  v-if="headStates === 'Add'"
                  v-model="formObject.siteCode"
                  float-label-type="Never"
                  :data-source="siteOptions"
                  :fields="{ text: 'siteName', value: 'siteCode' }"
                  :placeholder="formObject.companyCode ? $t('请选择工厂') : $t('请先选择公司')"
                  :disabled="headStates !== 'Add' || !formObject.companyCode"
                  @change="siteChange"
                />
                <mt-input
                  v-else
                  v-model="formObject.siteName"
                  float-label-type="Never"
                  :disabled="true"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="24">
              <mt-form-item prop="itemCode" :label="$t('物料')">
                <mt-input
                  v-if="headStates === 'Add'"
                  v-model="formObject.itemName"
                  float-label-type="Never"
                  :disabled="true"
                  :placeholder="formObject.siteCode ? $t('请选择物料') : $t('请先选择工厂')"
                  style="display: inline-block; width: calc(100% - 30px)"
                />
                <mt-icon
                  v-if="headStates === 'Add'"
                  :class="[formObject.siteCode ? '' : 'allowed']"
                  style="width: 20px; margin-left: 10px"
                  name="icon_input_search"
                  @click.native="handleOpen"
                />
                <mt-input
                  v-if="headStates === 'Edit'"
                  v-model="formObject.itemName"
                  float-label-type="Never"
                  :disabled="true"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="24">
              <mt-form-item prop="itemIsBatch" :label="$t('是否批次来料')">
                <mt-select
                  v-model="formObject.itemIsBatch"
                  :data-source="isBatchMaterialOptions"
                  float-label-type="Never"
                  :placeholder="$t('请选择是否批次来料')"
                  width="100%"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="24">
              <mt-form-item prop="status" :label="$t('是否生效')">
                <mt-select
                  v-model="formObject.status"
                  :data-source="statusOptions"
                  float-label-type="Never"
                  :disabled="headStates !== 'Add'"
                  :placeholder="$t('请选择是否生效')"
                  width="100%"
                />
              </mt-form-item>
            </mt-col>
          </mt-row>
        </mt-form>
      </div>
    </mt-dialog>
    <material-select ref="materialSelectRef" @confirm="handleConfirm" />
  </div>
</template>

<script>
import MaterialSelect from './MaterialSelect.vue'
export default {
  components: { MaterialSelect },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObject: {
        id: null,
        companyCode: null,
        companyName: null,
        organizationId: null,
        siteCode: null,
        siteName: null,
        itemCode: null,
        itemName: null,
        itemIsBatch: null,
        status: null
      },
      formRules: {
        companyCode: [
          {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          }
        ],
        siteCode: [
          {
            required: true,
            message: this.$t('请选择工厂'),
            trigger: 'blur'
          }
        ],
        itemCode: [
          {
            required: true,
            message: this.$t('请选择物料'),
            trigger: 'blur'
          }
        ],
        itemIsBatch: [
          {
            required: true,
            message: this.$t('请选择是否批次来料'),
            trigger: 'blur'
          }
        ],
        status: [
          {
            required: true,
            message: this.$t('请选择是否生效'),
            trigger: 'blur'
          }
        ]
      },
      companyOptions: [],
      siteOptions: [],
      materialOptions: [],
      isBatchMaterialOptions: [
        { text: this.$t('是'), value: '1' },
        { text: this.$t('否'), value: '0' }
      ],
      statusOptions: [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ]
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    headStates() {
      return this.modalData.headStates
    },
    selectedRows() {
      return this.modalData.data
    }
  },
  mounted() {
    this.getCompanyOptions()
    this.$refs['dialog'].ejsRef.show()
    if (this.headStates === 'Edit') {
      this.editEcho()
    }
  },
  methods: {
    handleOpen() {
      if (this.formObject.siteCode) {
        this.$refs['materialSelectRef'].open({
          title: this.$t('选择物料'),
          id: this.formObject.organizationId
        })
      }
    },
    handleConfirm(selectedRow) {
      const { itemCode, itemName } = selectedRow
      this.formObject.itemCode = itemCode
      this.formObject.itemName = itemName
    },
    editEcho() {
      let selectedRow = this.selectedRows[0]
      this.formObject.id = selectedRow.id
      this.formObject.companyCode = selectedRow.companyCode
      this.formObject.companyName = selectedRow.companyName
      this.formObject.siteCode = selectedRow.siteCode
      this.formObject.siteName = selectedRow.siteName
      this.formObject.itemCode = selectedRow.itemCode
      this.formObject.itemName = selectedRow.itemName
      this.formObject.itemIsBatch = selectedRow.itemIsBatch
      this.formObject.status = selectedRow.status
    },
    getCompanyOptions(args = { text: '' }) {
      const { text } = args
      let params = {
        fuzzyParam: text,
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true,
        organizationIds: []
      }
      this.$API.contractPrint.findSpecifiedChildrenLevelOrgs(params).then((res) => {
        this.companyOptions = res.data
      })
    },
    companyChange(e) {
      if (this.headStates === 'Add') {
        this.formObject.companyCode = e.itemData.orgCode
        this.formObject.companyName = e.itemData.orgName
        this.formObject.siteCode = ''
        this.formObject.siteName = ''
        this.formObject.itemCode = ''
        this.formObject.itemName = ''
        let id = e.itemData.id
        let params = {
          parentId: id,
          tenantId: 10000
        }
        this.$API.masterData.findSiteInfoByParentId(params).then((res) => {
          this.siteOptions = res.data
        })
      }
    },
    siteChange(e) {
      if (this.headStates === 'Add') {
        const { itemData } = e
        this.formObject.organizationId = itemData.organizationId
        this.formObject.siteCode = itemData.siteCode
        this.formObject.siteName = itemData.siteName
        this.formObject.itemCode = ''
        this.formObject.itemName = ''
      }
    },
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = {
            ...this.formObject
          }
          if (!params.id) {
            this.$API.deliverySchedule
              .addBatchMaterialConfigApi(params)
              .then((res) => {
                if (res && res.code === 200) {
                  this.$emit('confirm-function')
                  this.$toast({
                    content: res.msg,
                    type: 'success'
                  })
                } else {
                  this.$toast({
                    content: res.msg,
                    type: 'error'
                  })
                }
              })
              .catch((err) => {
                this.$toast({
                  content: err.msg,
                  type: 'error'
                })
                this.$emit('cancel-function')
              })
          } else {
            this.$API.deliverySchedule
              .editBatchMaterialConfigApi(params)
              .then((res) => {
                if (res && res.code === 200) {
                  this.$emit('confirm-function')
                  this.$toast({
                    content: res.msg,
                    type: 'success'
                  })
                } else {
                  this.$toast({
                    content: res.msg,
                    type: 'error'
                  })
                }
              })
              .catch((err) => {
                this.$toast({
                  content: err.msg,
                  type: 'error'
                })
                this.$emit('cancel-function')
              })
          }
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    padding: 0;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          .mt-icons {
            cursor: pointer;
          }
          .allowed {
            cursor: no-drop;
          }
        }
      }
    }
  }
}
</style>
