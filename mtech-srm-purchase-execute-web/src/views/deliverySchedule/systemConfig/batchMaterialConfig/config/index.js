import { i18n } from '@/main.js'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant.js'

const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增'), permission: ['O_02_1412'] },
  { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('编辑'), permission: ['O_02_1413'] },
  { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除'), permission: ['O_02_1414'] },
  { id: 'Enable', icon: 'icon_table_enable', title: i18n.t('生效'), permission: ['O_02_1415'] },
  { id: 'Disable', icon: 'icon_table_disable', title: i18n.t('失效'), permission: ['O_02_1416'] },
  { id: 'Import', icon: 'icon_solid_upload', title: i18n.t('导入'), permission: ['O_02_1417'] },
  { id: 'Export', icon: 'icon_solid_Download', title: i18n.t('导出'), permission: ['O_02_1418'] }
]

const columnData = [
  {
    type: 'checkbox',
    width: '50'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    template: () => {
      return {
        template: Vue.component('state', {
          template: `<div>{{ transformValue() }}</div>`,
          data() {
            return {
              data: {}
            }
          },
          methods: {
            transformValue() {
              return this.data?.status === 1 ? i18n.t('生效') : i18n.t('失效')
            }
          }
        })
      }
    },
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('生效'), 0: i18n.t('失效') }
    },
    width: '70'
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码'),
    width: '100'
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称'),
    width: '190'
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂编码'),
    width: '100'
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    width: '220'
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    width: '150'
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: '450'
  },
  {
    field: 'itemIsBatch',
    headerText: i18n.t('是否批次来料'),
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否') }
    },
    width: '120'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    width: '100'
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    width: '140'
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    width: '100'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    width: '140'
  }
]

export const PAGE_CONFIG = [
  {
    toolbar,
    useToolTemplate: false,
    gridId: 'b14e54e3-7497-46a9-becf-3a99bfbb2402',
    grid: {
      columnData,
      asyncConfig: {
        url: '/masterDataManagement/tenant/item-org-batch/paged-query',
        params: {}
      }
    }
  }
]
