<!-- 批次来料物料属性 -->
<template>
  <div class="full-height vertical-flex-box">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    />

    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    />
  </div>
</template>

<script>
import { PAGE_CONFIG } from './config/index.js'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    UploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    return {
      pageConfig: PAGE_CONFIG,
      downTemplateParams: {
        pageFlag: false
      }, // 配置导入下载模板参数
      uploadParams: {}, // 导入配置文件参数
      // 配置导入请求接口配置
      requestUrls: {
        templateUrlPre: 'deliverySchedule',
        templateUrl: 'downloadTempBatchMaterialConfigApi', // 下载模板接口方法名
        uploadUrl: 'importBatchMaterialConfigApi' // 上传接口方法名
      }
    }
  },
  methods: {
    handleClickToolBar(e) {
      const selectedRows = e.grid.getSelectedRecords()
      const commonToolbar = ['Edit', 'Delete', 'Enable', 'Disable']
      if (selectedRows.length === 0 && commonToolbar.includes(e.toolbar.id)) {
        return this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      }
      switch (e.toolbar.id) {
        case 'Add':
          this.handleAdd()
          break
        case 'Edit':
          this.handleEdit(selectedRows)
          break
        case 'Delete':
          this.handleDelete(selectedRows)
          break
        case 'Enable':
          this.handleEnable(selectedRows)
          break
        case 'Disable':
          this.handleDisable(selectedRows)
          break
        case 'Import':
          this.handleImport()
          break
        case 'Export':
          this.handleExport()
          break
      }
    },
    handleAdd() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "batchMaterialConfig/addOrEdot" */ './components/AddOrEdit.vue'
          ),
        data: {
          title: this.$t('新增'),
          headStates: 'Add'
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    handleEdit(selectedRows) {
      if (selectedRows.length > 1) {
        return this.$toast({ content: this.$t('只能编辑一行'), type: 'warning' })
      }
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "batchMaterialConfig/addOrEdot" */ './components/AddOrEdit.vue'
          ),
        data: {
          title: this.$t('编辑'),
          headStates: 'Edit',
          data: selectedRows
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    handleDelete(selectedRows) {
      const selectedIds = []
      selectedRows.forEach((item) => {
        selectedIds.push(item.id)
      })
      let params = {
        ids: selectedIds
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$API.deliverySchedule.deleteBatchMaterialConfigApi(params).then((res) => {
            if (res && res.code === 200) {
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            } else {
              this.$toast({
                content: res.msg,
                type: 'error'
              })
            }
          })
        }
      })
    },
    handleEnable(selectedRows) {
      const selectedIds = []
      selectedRows.forEach((item) => {
        selectedIds.push(item.id)
      })
      let params = {
        ids: selectedIds,
        statusId: 1
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认使选中的数据生效？')
        },
        success: () => {
          this.$API.deliverySchedule.updateStatusBatchMaterialConfigApi(params).then((res) => {
            if (res && res.code === 200) {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            } else {
              this.$toast({
                content: res.msg,
                type: 'error'
              })
            }
          })
        }
      })
    },
    handleDisable(selectedRows) {
      const selectedIds = []
      selectedRows.forEach((item) => {
        selectedIds.push(item.id)
      })
      let params = {
        ids: selectedIds,
        statusId: 0
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认使选中的数据失效？')
        },
        success: () => {
          this.$API.deliverySchedule.updateStatusBatchMaterialConfigApi(params).then((res) => {
            if (res && res.code === 200) {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            } else {
              this.$toast({
                content: res.msg,
                type: 'error'
              })
            }
          })
        }
      })
    },
    handleImport() {
      this.$dialog({
        modal: () =>
          import(/* webpackChunkName: "uploadDialog" */ '@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('上传'),
          paramsKey: 'file',
          importApi: this.$API.deliverySchedule.importBatchMaterialConfigApi,
          downloadTemplateApi: this.$API.deliverySchedule.downloadTempBatchMaterialConfigApi
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
      this.$refs.tepPage.refreshCurrentGridData()
    },
    handleExport() {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      }
      this.$store.commit('startLoading')
      this.$API.deliverySchedule.exportBatchMaterialConfigApi(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>
