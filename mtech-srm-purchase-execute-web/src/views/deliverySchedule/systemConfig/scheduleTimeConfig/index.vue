<!-- 交货计划时间限制参数配置 -->
<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
/* eslint-disable */
import { PAGE_PLUGIN } from './config.js'
import * as UTILS from '@/utils/utils'
export default {
  components: {
    UploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    return {
      downTemplateParams: {
        pageFlag: false
      }, // 配置导入下载模板参数
      uploadParams: {}, // 导入配置文件参数
      // 配置导入请求接口配置
      requestUrls: {
        templateUrlPre: 'deliverySchedule',
        templateUrl: 'importScheduleTimeConfigDown', // 下载模板接口方法名
        uploadUrl: 'importScheduleTimeConfig' // 上传接口方法名
      },
      pageConfig: PAGE_PLUGIN,
      newJITData: [] //选择完JIT以后的数组
    }
  },
  computed: {},
  mounted() {
    // this.$set(this.pageConfig[0].grid, "dataSource", querylist.data.records);
  },
  methods: {
    handleClickToolBar(e) {
      // 获取当前列表中被选中的数据
      const _selectRows = e.grid.getSelectedRecords()
      if (
        _selectRows.length <= 0 &&
        (e.toolbar.id == 'Edit' ||
          e.toolbar.id == 'delete' ||
          e.toolbar.id == 'activate' ||
          e.toolbar.id == 'failure')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //新增
      if (e.toolbar.id === 'Add') {
        this.handleClickAdd()
      }
      //编辑
      else if (e.toolbar.id === 'Edit') {
        this.handleClickEdit(_selectRows)
      }
      // 删除
      else if (e.toolbar.id === 'delete') {
        this.handleClickdelete(_selectRows)
      }
      //激活
      else if (e.toolbar.id === 'activate') {
        this.handleClickactivate(_selectRows)
      }
      //失效
      else if (e.toolbar.id === 'failure') {
        this.handleClickfailure(_selectRows)
      }
      //导入
      else if (e.toolbar.id === 'upload') {
        this.handleClickUpload()
        // this.showUploadExcel(true);
      }
      //导出
      else if (e.toolbar.id === 'Download') {
        this.handleClickDownload(_selectRows)
      }
    },
    //新增
    handleClickAdd() {
      this.$dialog({
        modal: () =>
          import(/* webpackChunkName: "material/components/SalesAddDialog.vue" */ './detail.vue'),
        data: {
          title: this.$t('新增'),
          headStates: 'Add'
        },
        success: (data) => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    //编辑
    handleClickEdit(_selectRows) {
      if (_selectRows.length > 1) {
        this.$toast({ content: this.$t('只能编辑一行'), type: 'warning' })
        return
      }
      this.$dialog({
        modal: () =>
          import(/* webpackChunkName: "material/components/SalesAddDialog.vue" */ './detail.vue'),
        data: {
          title: this.$t('编辑'),
          data: _selectRows,
          headStates: 'edit'
        },
        success: (data) => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    //删除
    handleClickdelete(_selectRows) {
      const _selectIds = []
      _selectRows.map((item) => {
        _selectIds.push(item.id)
      })
      this.$API.deliverySchedule.deleteScheduleTimeConfig(_selectIds).then((res) => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.$refs.tepPage.refreshCurrentGridData()
      })
    },
    //激活
    handleClickactivate(_selectRows) {
      _selectRows.map((item) => {
        item['isEffective'] = 1
      })
      this.$API.deliverySchedule.addAndEditScheduleTimeConfigInfo(_selectRows).then((res) => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
      })
    },
    //失效
    handleClickfailure(_selectRows) {
      _selectRows.map((item) => {
        item['isEffective'] = 0
      })
      this.$API.deliverySchedule.addAndEditScheduleTimeConfigInfo(_selectRows).then((res) => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
      })
    },
    //导入
    handleClickUpload() {
      console.log('导入')
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "@/components/uploadDialog/index.vue" */ '@/components/uploadDialog/index.vue'
          ),
        data: {
          title: this.$t('上传'),
          paramsKey: 'file',
          importApi: this.$API.deliverySchedule.importScheduleTimeConfig,
          downloadTemplateApi: this.$API.deliverySchedule.importScheduleTimeConfigDown
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    // 显示隐藏上传弹框
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 上传成功后
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
      this.$refs.tepPage.refreshCurrentGridData()
    },
    //导出
    handleClickDownload(_selectRows) {
      // console.log('导出')
      // if (_selectRows.length > 1) {
      //   this.$toast({ content: this.$t('只能编辑一行'), type: 'warning' })
      //   return
      // }
      // const id = _selectRows[0].id
      // const pramas = {
      //   accountId: id
      // }
      const queryBuilderRules =
        this.$refs.tepPage.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.deliverySchedule.exportScheduleTimeConfig(params).then((res) => {
        this.$store.commit('endLoading')
        // console.log("=========", res);
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-subtitle {
  border-left: 3px solid #00469c;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: #2f353c;
  padding-left: 10px;
}
</style>
