import { i18n } from '@/main.js'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant.js'
// import Vue from "vue";
// import { i18n } from "@/main.js";
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('编辑') },
  { id: 'delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
  { id: 'activate', icon: 'icon_table_enable', title: i18n.t('生效') },
  { id: 'failure', icon: 'icon_table_disable', title: i18n.t('失效') },
  { id: 'upload', icon: 'icon_solid_upload', title: i18n.t('导入') },
  { id: 'Download', icon: 'icon_solid_Download', title: i18n.t('导出') }
]
// 标题
const titleFun = (title) => {
  return {
    template: Vue.component('actionInput', {
      template: `
        <div style="text-align: center">
          <span>{{ headerTitle }}</span>
        </div>
      `,
      data() {
        return {
          headerTitle: title
        }
      }
    })
  }
}

export const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: '80',
    field: 'isEffective',
    headerText: i18n.t('是否有效'),
    headerTemplate: () => titleFun('是否有效'),
    template: () => {
      return {
        template: Vue.component('actionView', {
          template: `<div style="text-align: center"><span>{{ transformValue() }}</span></div>`,
          data() {
            return {
              data: {}
            }
          },
          methods: {
            transformValue() {
              return this.data?.isEffective === 1 ? i18n.t('生效') : i18n.t('失效')
            }
          }
        })
      }
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('失效'),
        1: i18n.t('生效')
      }
    }
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编码'),
    headerTemplate: () => titleFun('公司编码')
  },
  {
    field: 'categCode',
    headerText: i18n.t('品类编码'),
    headerTemplate: () => titleFun('品类编码')
  },
  {
    field: 'publishTime',
    width: '120',
    headerText: i18n.t('计划发布提前期(天)'),
    headerTemplate: () => titleFun('计划发布提前期(天)')
  },
  {
    field: 'confirmTime',
    width: '120',
    headerText: i18n.t('交期确认提前期(天)'),
    headerTemplate: () => titleFun('交期确认提前期(天)')
  },
  {
    field: 'companyName',
    // width: "200",
    headerText: i18n.t('公司名称'),
    headerTemplate: () => titleFun('公司名称')
  },
  {
    field: 'categoryName',
    // width: "200",
    headerText: i18n.t('品类名称'),
    headerTemplate: () => titleFun('品类名称')
  },
  {
    field: 'createUserName',
    // width: "200",
    headerText: i18n.t('创建人'),
    headerTemplate: () => titleFun('创建人')
  },
  {
    field: 'createTime',
    // width: "200",
    headerText: i18n.t('创建时间'),
    headerTemplate: () => titleFun('创建时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'updateUserName',
    // width: "200",
    headerText: i18n.t('更新人'),
    headerTemplate: () => titleFun('更新人')
  },
  {
    field: 'updateTime',
    // width: "200",
    headerText: i18n.t('更新时间'),
    headerTemplate: () => titleFun('更新时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  }
]
export const PAGE_PLUGIN = [
  {
    toolbar: toolbar,
    useBaseConfig: true, // 使用 toolbar 中的配置
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    useCombinationSelection: false, // 不使用组件内部的高级查询
    isUseCustomSearch: false, // 是否使用自定义查询
    grid: {
      columnData,
      // dataSource: [],
      asyncConfig: {
        url: '/srm-purchase-execute/tenant/deliveryDateLimitationConfig/query',
        params: {}
      }
    }
  }
]
