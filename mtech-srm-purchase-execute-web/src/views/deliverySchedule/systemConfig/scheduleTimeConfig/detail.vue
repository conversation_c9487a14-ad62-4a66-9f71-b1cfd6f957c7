<template>
  <!-- eslint-disable -->
  <!-- @beforeClose="cancel" -->
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    width="30%"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="dialog-mt-form" ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-row>
          <mt-col :span="24">
            <mt-form-item prop="companyCode" :label="$t('公司名称')">
              <mt-select
                width="100%"
                v-model="formObject.companyCode"
                float-label-type="Never"
                :data-source="companySelect"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                @change="changecompanySelect"
                :placeholder="$t('请选择公司')"
                :disabled="headStates !== 'Add'"
                :allow-filtering="true"
                :filtering="getCompanyList"
              ></mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="24">
            <mt-form-item prop="categCode" :label="$t('品类')">
              <mt-select
                width="100%"
                v-model="formObject.categCode"
                float-label-type="Never"
                :data-source="categSelect"
                :fields="{ text: 'theCodeName', value: 'categoryCode' }"
                @change="changeCategSelect"
                :placeholder="$t('请选择品类')"
                :disabled="headStates !== 'Add'"
                :allow-filtering="true"
                :filtering="getCategoryList"
              ></mt-select>
              <!-- <debounce-filter-select
                v-model="formObject.categCode"
                :request="getItemGroupList"
                :data-source="categSelect"
                :show-clear-button="true"
                :fields="{ text: 'categoryName', value: 'categCode' }"
                :value-template="ItemGroupTemplate"
                @change="ItemGroupChange"
                :placeholder="$t('请选择')"
              ></debounce-filter-select> -->
            </mt-form-item>
          </mt-col>
          <mt-col :span="24">
            <mt-form-item :label="$t('计划发布提前期(自然天)')">
              <mt-input-number
                width="100%"
                v-model="formObject.publishTime"
                max="999999"
                :show-clear-button="true"
                :placeholder="$t('请输入计划发布提前期')"
                :precision="0"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="24">
            <mt-form-item :label="$t('交期确认提前期(自然天)')">
              <mt-input-number
                width="100%"
                v-model="formObject.confirmTime"
                max="999999"
                :show-clear-button="true"
                :placeholder="$t('请输入交期确认提前期')"
                :precision="0"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
/* eslint-disable */
// import { processors } from "../config/moduls.js";
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import { utils } from '@mtech-common/utils'
import { text } from 'body-parser'
export default {
  components: {
    DebounceFilterSelect
  },
  data() {
    return {
      ItemGroupTemplate: codeNameColumn({
        firstKey: 'categoryCode',
        secondKey: 'categoryName'
      }),
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      //v-model获取的值
      formObject: {
        companyName: '',
        companyCode: null,
        categCode: '',
        categoryName: '',
        publishTime: '',
        confirmTime: ''
      },
      //必填项
      formRules: {
        companyCode: [
          {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          }
        ],
        categCode: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ]
      },
      //公司下拉框
      companySelect: [],
      //品类下拉
      categSelect: [],
      //仓库下拉框
      locationSelect: [],
      //加工商
      supplierArr: [],
      //直送销售标识
      salesSelect: [
        { text: this.$t('否'), value: 'N' },
        { text: this.$t('是'), value: 'Y' }
      ],
      weight: 1 //权重值判断那些下拉框可以编辑
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    salesData() {
      return this.modalData.data
    },
    headStates() {
      return this.modalData.headStates
    }
  },
  mounted() {
    this.initialCallInterface()
    this.$refs['dialog'].ejsRef.show()
    if (this.headStates == 'Add') {
      // this.getItemGroupList()
    } else if (this.headStates == 'edit') {
      this.editCallInterface()
    }
  },
  methods: {
    ItemGroupChange(e) {
      const { itemData } = e

      this.formObject.categoryId = itemData.id
      this.formObject.categCode = itemData.categoryCode
      this.formObject.categoryName = itemData.categoryName
    },
    getItemGroupList(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData
        .getCategoryfuzzy(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.categSelect = addCodeNameKeyInList({
              firstKey: 'categoryCode',
              secondKey: 'categoryName',
              list
            })
          }
        })
        .catch(() => {})
    },
    // 获取公司枚举值
    getCompanyList(args = { text: '' }) {
      const { text } = args
      //公司
      let parameter = {
        fuzzyParam: text,
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true,
        organizationIds: []
      }

      // 获取公司下拉枚举
      this.$API.contractPrint.findSpecifiedChildrenLevelOrgs(parameter).then((res) => {
        this.companySelect = res.data
      })
    },
    // 获取品类枚举值
    getCategoryList(args = { text: '' }) {
      const { text } = args
      const params = {
        fuzzyNameOrCode: text
      }
      this.$API.deliverySchedule.fuzzyQuery(params).then((res) => {
        let list = res.data || []
        this.categSelect = addCodeNameKeyInList({
          firstKey: 'categoryCode',
          secondKey: 'categoryName',
          list
        })
      })
    },
    //新增调用接口
    initialCallInterface() {
      this.getCompanyList()
      this.getCategoryList()
    },
    //编辑回选
    editCallInterface() {
      let salesData = this.salesData[0]
      this.formObject.id = salesData.id
      this.formObject.companyCode = salesData.companyCode
      this.formObject.companyName = salesData.companyName
      this.formObject.categCode = salesData.categCode
      this.formObject.categoryName = salesData.categoryName
      this.formObject.publishTime = salesData.publishTime
      this.formObject.confirmTime = salesData.confirmTime
      this.weight = 0
    },
    //公司下拉框事件
    changecompanySelect(e) {
      if (this.headStates == 'Add') {
        // this.formObject.companyCode = e.itemData.companyCode;
        this.formObject.companyName = e.itemData.orgName
      }
    },
    //品类下拉框事件
    changeCategSelect(e) {
      if (this.headStates == 'Add') {
        // this.formObject.categCode = e.itemData.categCode
        this.formObject.categoryName = e.itemData.categoryName
      }
    },
    //点击确认
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let formObject = this.formObject
          let parameter = {
            ...formObject
          }
          parameter.id = this.headStates == 'edit' ? formObject.id : null
          this.$API.deliverySchedule
            .addAndEditScheduleTimeConfigInfo([parameter])
            .then((res) => {
              if (res && res.code === 200) {
                this.$emit('confirm-function')
                this.$toast({
                  content: res.msg,
                  type: 'success'
                })
              } else {
                this.$toast({
                  content: res.msg,
                  type: 'error'
                })
              }
            })
            .catch((err) => {
              this.$toast({
                content: err.msg,
                type: 'error'
              })
              this.$emit('cancel-function')
            })
        }
      })
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    padding: 0;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
      }
    }
  }
}
</style>
