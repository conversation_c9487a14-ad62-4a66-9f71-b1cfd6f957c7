<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
      <mt-form-item prop="siteCode" :label="$t('工厂')">
        <mt-select
          v-model="ruleForm.siteCode"
          :data-source="siteCodeOptions"
          :fields="{ text: 'label', value: 'siteCode' }"
          :placeholder="$t('请选择')"
          :allow-filtering="true"
          :filtering="postSiteFuzzyQuery"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="verifyType" :label="$t('校验条件')">
        <mt-select
          v-model="ruleForm.verifyType"
          :data-source="verifyTypeOptions"
          :fields="{ text: 'label', value: 'value' }"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      siteCodeOptions: [], // 工厂 下列选项
      verifyTypeOptions: [{ label: this.$t('累计剩余送货数量小于采购订单未发货数量'), value: 1 }], // 校验条件 下列选项
      dialogTitle: '',
      rules: {
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        verifyType: [{ required: true, message: this.$t('请选择校验条件'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {
        id: '',
        siteCode: '',
        status: 1,
        verifyType: ''
      }
    }
  },
  mounted() {
    // this.getOptions();
    // 获取主数据-工厂模糊查询
    // this.postSiteFuzzyQuery({ text: undefined });
    this.postSiteFuzzyQuery = utils.debounce(this.postSiteFuzzyQuery, 1000)
  },
  methods: {
    // 获取主数据-工厂
    postSiteFuzzyQuery(args, entryFirst) {
      const { text, updateData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteCodeOptions = list.map((item) => {
              return {
                ...item,
                name: item.siteName,
                label: `${item.siteCode}-${item.siteName}`
              }
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.siteCodeOptions)
              })
            }
            if (entryFirst === '1') {
              this.ruleForm.siteCode = this.editInfo.siteCode
            }
          }
        })
        .catch(() => {})
    },
    getOptions() {
      //工厂
      // this.$API.masterData.getSite().then((res) => {
      //   this.siteCodeOptions = res.data || [];
      // });
    },
    // 初始化
    dialogInit(entryInfo) {
      this.dialogTitle = entryInfo.title
      if (this.dialogTitle === this.$t('新增')) {
        this.ruleForm = {
          siteCode: null,
          siteName: null,
          status: 1,
          verifyType: ''
        }
        this.postSiteFuzzyQuery({ text: undefined })
      }
      if (this.dialogTitle === this.$t('编辑')) {
        this.editInfo = entryInfo.row
        this.ruleForm = {
          siteCode: null,
          siteName: null,
          status: entryInfo.row.status,
          verifyType: entryInfo.row.verifyType,
          id: entryInfo.row.id
        }
        this.postSiteFuzzyQuery({ text: entryInfo.row.siteCode }, '1')
      }
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            siteCode: this.ruleForm.siteCode,
            siteId: this.siteCodeOptions.find((item) => {
              return item.siteCode === this.ruleForm.siteCode
            })?.id,
            siteName: this.siteCodeOptions.find((item) => {
              return item.siteCode === this.ruleForm.siteCode
            })?.siteName,
            verifyType: this.ruleForm.verifyType,
            status: this.ruleForm.status
          }
          if (this.dialogTitle === this.$t('编辑')) {
            params.id = this.ruleForm.id
            this.$API.deliverySchedule.buyerVerifyConfigureupdate(params).then(() => {
              this.$refs.dialog.ejsRef.hide()
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$emit('updateList')
            })
          }
          if (this.dialogTitle === this.$t('新增')) {
            this.$API.deliverySchedule.buyerVerifyConfiguresave(params).then(() => {
              this.$refs.dialog.ejsRef.hide()
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$emit('updateList')
            })
          }
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
<style lang="scss" scoped></style>
