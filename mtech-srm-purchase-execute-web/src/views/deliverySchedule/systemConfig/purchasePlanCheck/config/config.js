// import UTILS from "@/utils/utils";
import { i18n } from '@/main.js'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant'

export const checkColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  }
]
export const lastColumn = [
  {
    ignore: true,
    field: 'selfIndex',
    headerText: i18n.t('序号'),
    width: '150',
    cellTools: [
      {
        permission: ['O_02_0474'],
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data
        }
      },
      {
        permission: ['O_02_0475'],
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => {
          return data
        }
      }
    ]
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('启用'), cssClass: 'col-active' },
        { value: 0, text: i18n.t('停用'), cssClass: 'col-inactive' }
      ]
    },
    cellTools: [
      {
        permission: ['O_02_0477'],
        id: 'disable',
        icon: '',
        title: i18n.t('停用'),
        visibleCondition: (data) => data.status === 1
      },
      {
        permission: ['O_02_0476'],
        id: 'enable',
        icon: '',
        title: i18n.t('启用'),
        visibleCondition: (data) => data.status === 0
      }
    ]
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '250',
    searchOptions: {
      ...MasterDataSelect.factoryAddress,
      renameField: 'siteCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.siteCode}}-{{data.siteName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'verifyType',
    headerText: i18n.t('校验条件'),
    width: '250',
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('累计剩余送货数量小于采购订单未发货数量') }
    }
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    width: '150'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    width: '150'
  }
]
