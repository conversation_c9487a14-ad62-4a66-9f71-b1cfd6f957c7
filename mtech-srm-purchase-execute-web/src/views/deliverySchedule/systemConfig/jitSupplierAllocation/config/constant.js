import { i18n } from '@/main.js'

export const ToolBar = [
  {
    code: 'CopyRow',
    name: i18n.t('复制行'),
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  },
  {
    code: 'BatchSave',
    name: i18n.t('保存'),
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  },
  {
    code: 'JitImport',
    name: i18n.t('导入'),
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  },
  {
    code: 'BatchSubmit',
    name: i18n.t('提交'),
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  },
  {
    code: 'Withdraw',
    name: i18n.t('撤回'),
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  },
  {
    code: 'BatchDelete',
    name: i18n.t('删除'),
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  },
  {
    code: 'JitExport',
    name: i18n.t('导出'),
    icon: 'vxe-icon-cloud-download',
    status: 'info'
  }
]

// 状态 UNCOMMITTED： 未提交； WITHDRAWN：已修改；COMMITTED： 已提交
// export const statusOptions = [
//   { text: i18n.t('未提交'), value: 'UNCOMMITTED' },
//   { text: i18n.t('已撤回'), value: 'WITHDRAWN' },
//   { text: i18n.t('已提交'), value: 'COMMITTED' }
// ]

// 预测表格列数据
export const columnData = (statusOptions, sourceTypeOptions) => [
  {
    type: 'checkbox',
    width: 50,
    ignore: true,
    fixed: 'left'
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    minWidth: 100,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    },
    showOverflow: true
    // editRender: {
    //   name: 'select',
    //   options: statusOptions,
    //   optionProps: { label: 'dictName', value: 'dictCode' },
    //   props: { style: 'background: #fff' },
    //   attrs: { disabled: true }
    // }
  },
  {
    field: 'factoryCode',
    title: i18n.t('工厂编码'),
    minWidth: 120,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'materialCode',
    title: i18n.t('物料编码'),
    minWidth: 120,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'materialName',
    title: i18n.t('物料名称'),
    minWidth: 120,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'salesOrderNo',
    title: i18n.t('销售订单号'),
    minWidth: 120,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'suggestSupplierCode',
    title: i18n.t('建议分配供应商编码'),
    minWidth: 180,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'suggestSupplierName',
    title: i18n.t('建议分配供应商名称'),
    minWidth: 180,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'suggestAllocationQty',
    title: i18n.t('建议分配叫料数量'),
    minWidth: 160,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'supplierCode',
    title: i18n.t('最终分配供应商编码'),
    minWidth: 180,
    showOverflow: true,
    editRender: {},
    slots: {
      edit: 'supplierCodeEdit'
    }
  },
  {
    field: 'supplierName',
    title: i18n.t('最终分配供应商名称'),
    minWidth: 180,
    showOverflow: true,
    editRender: {},
    slots: {
      edit: 'adjustSupplierNameEdit'
    }
  },
  {
    field: 'allocationQty',
    title: i18n.t('最终分配叫料数量'),
    minWidth: 160,
    showOverflow: true,
    editRender: {},
    slots: {
      edit: 'adjustCallMaterialQtyEdit'
    }
  },
  {
    field: 'psQty',
    title: i18n.t('PS叫料数量'),
    minWidth: 130,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'noAllocationReason',
    title: i18n.t('未自动分配原因'),
    minWidth: 160,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'demandQty',
    title: i18n.t('需求数量'),
    minWidth: 120,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'demandDateStr',
    title: i18n.t('需求日期'),
    minWidth: 100,
    showOverflow: true
  },
  {
    field: 'demandTimeStr',
    title: i18n.t('需求时间'),
    minWidth: 100,
    showOverflow: true
  },
  {
    field: 'categoryCode',
    title: i18n.t('品类编码'),
    minWidth: 120,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'categoryName',
    title: i18n.t('品类名称'),
    minWidth: 120,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'lineBody',
    title: i18n.t('线体'),
    minWidth: 80,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'workCenterCode',
    title: i18n.t('工作中心编码'),
    minWidth: 140,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'workCenterName',
    title: i18n.t('工作中心名称'),
    minWidth: 140,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'versionNo',
    title: i18n.t('版本号'),
    minWidth: 100,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'planVersionNo',
    title: i18n.t('排期版本号'),
    minWidth: 120,
    showOverflow: true
  },
  {
    field: 'plannerName',
    title: i18n.t('计划员'),
    minWidth: 100,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'sourceMethod',
    title: i18n.t('来源方式'),
    minWidth: 120,
    formatter: ({ cellValue }) => {
      let item = sourceTypeOptions.find((item) => item.dictCode === cellValue)
      return item ? item.dictName : ''
    },
    showOverflow: true
    // editRender: {
    //   name: 'select',
    //   options: sourceTypeOptions,
    //   optionProps: { label: 'dictName', value: 'dictCode' },
    //   props: { style: 'background: #fff' },
    //   attrs: { disabled: true }
    // }
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    minWidth: 100,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 150,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'updateUserName',
    title: i18n.t('最后更新人'),
    minWidth: 130,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'updateTime',
    title: i18n.t('最后更新时间'),
    minWidth: 150,
    showOverflow: true
    // editRender: { name: 'input', attrs: { disabled: true } }
  }
]
