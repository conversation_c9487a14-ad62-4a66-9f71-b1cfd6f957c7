<template>
  <!-- jit自动叫料-采方 -->
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      default-max-height="60"
      @reset="handleCustomReset"
      @search="handleCustomSearch('handle')"
    >
      <mt-form
        ref="searchFormRef"
        :model="searchFormModel"
        :rules="{
          versionNo: [{ required: true, message: $t('请输入'), trigger: ['input'] }]
        }"
      >
        <mt-form-item prop="versionNo" :label="$t('版本号')" label-style="top">
          <!-- <mt-input
            v-model="searchFormModel.versionNo"
            :show-clear-button="true"
            :placeholder="$t('请输入版本号')"
          /> -->
          <!-- <mt-select
            v-model="searchFormModel.versionNo"
            :data-source="versionList"
            :placeholder="$t('请选择')"
          ></mt-select> -->
          <combobox v-model="searchFormModel.versionNo" :data-source="versionList" />
        </mt-form-item>
        <mt-form-item prop="factoryCodeList" :label="$t('工厂')" label-style="top">
          <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.factoryCodeList"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择工厂')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="materialCode" :label="$t('物料编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.materialCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="salesOrderNo" :label="$t('销售订单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.salesOrderNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCodeList" :label="$t('供应商编码')" label-style="top">
          <!-- <mt-input
            style="flex: 1"
            v-model="searchFormModel.supplierCode"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
            :show-clear-button="true"
          /> -->
          <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.supplierCodeList"
            url="/masterDataManagement/tenant/supplier/paged-query"
            multiple
            :placeholder="$t('请选择供应商')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')" label-style="top">
          <mt-multi-select
            style="flex: 1"
            v-model="searchFormModel.status"
            :show-select-all="true"
            :allow-filtering="true"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            filter-type="Contains"
            :data-source="statusOptions"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
        <mt-form-item prop="demandDate" :label="$t('需求日期')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.demandDate"
            :placeholder="$t('请选择需求日期')"
            :show-clear-button="true"
            @change="(e) => handleDateChange(e, 'demandDate')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCodeFlag" :label="$t('分配供应商')" label-style="top">
          <mt-select
            v-model="searchFormModel.supplierCodeFlag"
            :data-source="[
              { value: 'N', text: $t('否'), cssClass: '' },
              { value: 'Y', text: $t('是'), cssClass: '' }
            ]"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择分配供应商')"
          />
        </mt-form-item>
        <mt-form-item prop="lineBody" :label="$t('线体')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.lineBody"
            :placeholder="$t('请输入线体')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="sourceMethod" :label="$t('来源方式')" label-style="top">
          <mt-select
            v-model="searchFormModel.sourceMethod"
            :data-source="sourceTypeOptions"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择来源方式')"
          />
        </mt-form-item>
        <mt-form-item prop="workCenterCode" :label="$t('工作中心')" label-style="top">
          <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.workCenterCode"
            url="/masterDataManagement/tenant/work-center/paged-query"
            multiple
            :placeholder="$t('请选择工作中心')"
            :fields="{ text: 'workCenterName', value: 'workCenterCode' }"
            :search-fields="['workCenterName', 'workCenterCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="plannerCode" :label="$t('计划员')" label-style="top">
          <mt-input
            v-model="searchFormModel.plannerCode"
            :show-clear-button="true"
            :placeholder="$t('请输入计划员')"
          />
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
          <mt-input
            v-model="searchFormModel.createUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入创建人')"
          />
        </mt-form-item>
        <!-- 创建日期 -->
        <mt-form-item prop="createTime" :label="$t('创建时间')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :placeholder="$t('请选择创建时间')"
            :show-clear-button="true"
            :change="(e) => handleDateChange(e, 'createTime')"
          />
        </mt-form-item>
        <!-- 最后修改人 -->
        <mt-form-item prop="updateUserName" :label="$t('最后更新人')" label-style="top">
          <mt-input
            style="flex: 1"
            v-model="searchFormModel.updateUserName"
            :show-clear-button="true"
            :placeholder="$t('请输入最后更新人')"
          />
        </mt-form-item>
        <!-- 最后更新时间 -->
        <mt-form-item prop="updateTime" :label="$t('最后更新时间')">
          <mt-date-range-picker
            v-model="searchFormModel.updateTime"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => handleDateChange(e, 'updateTime')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer">
      <ScTable
        ref="xTable"
        :row-config="{ height: 40 }"
        :columns="columns"
        :table-data="tableData"
        :is-show-refresh-bth="true"
        keep-source
        show-overflow
        height="auto"
        header-align="left"
        align="left"
        style="padding-top: unset"
        :edit-config="{
          trigger: 'click',
          mode: 'row',
          showStatus: true,
          activeMethod: this.activeRowMethod
        }"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        @edit-closed="editComplete"
        @edit-actived="editBegin"
        @edit-disabled="editDisabledEvent"
        @refresh="handleCustomSearch"
      >
        <template slot="custom-tools">
          <!-- :icon="item.icon" -->
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #supplierCodeEdit="{ row }">
          <vxe-pulldown ref="xDown" transfer>
            <template #default>
              <!-- <vxe-input
                :disabled="row.status === 'COMMITTED'"
                :value="row.supplierCode"
                :placeholder="$t('请选择供应商')"
                readonly
                @click="focusSupplierCode"
              ></vxe-input> -->
              <vxe-input
                :disabled="row.status === 'COMMITTED'"
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                :value="row.supplierCode"
                @click="focusSupplierCode"
                @keyup="keyupSupplierCode"
                style="width: 100%"
              ></vxe-input>
            </template>
            <template #dropdown>
              <!-- <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                @keyup="keyupSupplierCode"
                style="width: 100%"
              ></vxe-input> -->
              <vxe-list
                height="200"
                class="predict-vxe-dropdown"
                :data="supplierCodeOptions"
                auto-resize
              >
                <template #default="{ items }">
                  <div
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.value"
                    @click="selectSupplierCode(item, row)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template>
        <template #adjustSupplierNameEdit="{ row }">
          <vxe-input
            disabled
            v-model="row.supplierName"
            :placeholder="$t('请输入最终分配供应商名称')"
            clearable
          ></vxe-input>
        </template>
        <template #adjustCallMaterialQtyEdit="{ row }">
          <vxe-input
            type="number"
            min="0"
            v-model="row.allocationQty"
            :placeholder="$t('请输入最终分配叫料数量')"
            clearable
          ></vxe-input>
        </template>
      </ScTable>
    </div>
    <mt-page
      ref="mtPage"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import Combobox from '@/components/combobox'
import { columnData, ToolBar } from './config/constant'
import * as UTILS from '@/utils/utils'
// import { getSupplierDict } from '@/utils/utils'
import dayjs from 'dayjs'
import { utils } from '@mtech-common/utils'
export default {
  components: {
    ScTable,
    CollapseSearch,
    Combobox
  },
  data() {
    // const statusOptions = getSupplierDict('JIT_SUBMIT_STATUS')
    // const sourceTypeOptions = getSupplierDict('JIT_SOURCE_TYPE')
    const statusOptions = [
      { dictName: this.$t('未提交'), dictCode: 1 },
      { dictName: this.$t('已撤回'), dictCode: 3 },
      { dictName: this.$t('已提交'), dictCode: 2 },
      { dictName: this.$t('提交失败'), dictCode: 4 }
    ]
    const sourceTypeOptions = [
      { dictName: this.$t('自动'), dictCode: 1 },
      { dictName: this.$t('手动'), dictCode: 2 }
    ]
    return {
      statusOptions,
      sourceTypeOptions,
      toolbar: ToolBar,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      searchFormModel: {},
      tableData: [],
      columns: columnData(statusOptions, sourceTypeOptions),
      pageCurrent: 1,
      pageSettings: {
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      versionList: [],
      supplierCodeOptions: [],
      getSupplierDataSource: () => {} // 供应商 下拉选项
    }
  },
  mounted() {
    this.getSupplierDataSource = utils.debounce(this.getSupplier, 1000)
    this.buyerGoodsDemandPlanInfoQueryVersionTv()
    // this.handleCustomSearch()
  },
  methods: {
    // 供应商插槽方法
    focusSupplierCode() {
      this.$refs.xDown.showPanel()
    },
    keyupSupplierCode(e) {
      this.getSupplierDataSource(e)
    },
    selectSupplierCode(e, row) {
      row.supplierCode = e.supplierCode
      row.supplierId = e.id
      row.supplierTenantId = e.supplierTenantId
      row.supplierName = e.supplierName
      // this.$set(row, 'supplierCode', e.supplierCode)
      // this.$set(row, 'adjustSupplierId', e.id)
      // this.$set(row, 'supplierName', e.supplierName)
    },
    // 主数据 供应商
    getSupplier(value) {
      const params = {
        fuzzyNameOrCode: value ? value.value : ''
      }
      this.$API.masterData.getSupplier(params).then((res) => {
        if (res) {
          const list = res?.data || []
          const newData = list.map((i) => {
            return {
              ...i,
              label: `${i.supplierCode}-${i.supplierName}`,
              value: i.supplierCode
            }
          })
          this.supplierCodeOptions = [...newData]
        }
      })
    },
    // 需求时间选择
    handleDateChange(e, flag) {
      if (e && e.startDate) {
        this.searchFormModel[flag + 'S'] = new Date(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        ).getTime()
        this.searchFormModel[flag + 'E'] = new Date(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        ).getTime()
      } else {
        this.searchFormModel[flag + 'S'] = null
        this.searchFormModel[flag + 'E'] = null
      }
    },
    // 获取版本下拉列表
    buyerGoodsDemandPlanInfoQueryVersionTv() {
      this.$API.deliverySchedule.jitVersionListQuery().then((res) => {
        const { data, code } = res
        if (code === 200) {
          this.versionList = data
          this.searchFormModel.versionNo = data[0]
          this.$refs.searchFormRef.clearValidate()
          if (this.searchFormModel.versionNo) {
            this.handleCustomSearch()
          }
        }
      })
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      const commonToolbar = ['CopyRow', 'Withdraw', 'BatchDelete']
      if (selectedRecords.length === 0 && commonToolbar.includes(code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const validateEditToolbar = ['JitImport', 'BatchSubmit', 'Withdraw', 'BatchDelete']
      const isEdit = JSON.parse(
        JSON.stringify(this.$refs.xTable.$refs.xGrid.getUpdateRecords())
      ).length
      if (validateEditToolbar.includes(code) && isEdit) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('该操作将会清空未保存的数据，请确定是否继续？')
          },
          success: () => {
            const idList = []
            selectedRecords.forEach((i) => {
              if (!i.addId) {
                idList.push(i.id)
              }
            })
            if (code === 'CopyRow') {
              // 复制行
              if (selectedRecords.some((i) => i.status === 2)) {
                this.$toast({ content: this.$t('不可复制已提交的数据'), type: 'warning' })
                return
              }
              // this.handleCopyRow(selectedRecords)
              this.handleCopyRow(idList)
            } else if (code === 'BatchSave') {
              // 保存
              this.handleSave()
            } else if (code === 'JitImport') {
              // 导入
              this.handleImport()
            } else if (code === 'BatchSubmit') {
              // 提交
              this.handleSubmit(idList)
            } else if (code === 'Withdraw') {
              // 撤回
              this.handleWithdraw(idList)
            } else if (code === 'BatchDelete') {
              // 删除
              this.handleDelete(idList)
            } else if (code === 'JitExport') {
              // 导出
              this.handleExport()
            }
          }
        })
        return
      }
      const idList = []
      selectedRecords.forEach((i) => {
        if (!i.addId) {
          idList.push(i.id)
        }
      })
      if (code === 'CopyRow') {
        // 复制行
        if (selectedRecords.some((i) => i.status === 2)) {
          this.$toast({ content: this.$t('不可复制已提交的数据'), type: 'warning' })
          return
        }
        // this.handleCopyRow(selectedRecords)
        this.handleCopyRow(idList)
      } else if (code === 'BatchSave') {
        // 保存
        this.handleSave()
      } else if (code === 'JitImport') {
        // 导入
        this.handleImport()
      } else if (code === 'BatchSubmit') {
        // 提交
        this.handleSubmit(idList)
      } else if (code === 'Withdraw') {
        // 撤回
        this.handleWithdraw(idList)
      } else if (code === 'BatchDelete') {
        // 删除
        this.handleDelete(idList)
      } else if (code === 'JitExport') {
        // 导出
        this.handleExport()
      }
    },
    // 复制行
    handleCopyRow(idList) {
      // const selectRow = selectedRecords[0]
      // const newRow = {
      //   addId: 'add' + Math.random().toString(36).substr(3, 8),
      //   id: 'add' + Math.random().toString(36).substr(3, 8),
      //   status: 'UNCOMMITTED',
      //   factoryCode: selectRow.factoryCode,
      //   materialCode: selectRow.materialCode,
      //   materialName: selectRow.materialName,
      //   psQty: selectRow.psQty,
      //   demandQty: selectRow.demandQty,
      //   demandDate: selectRow.demandDate,
      //   lineBody: selectRow.lineBody,
      //   workCenterCode: selectRow.workCenterCode,
      //   workCenterName: selectRow.workCenterName,
      //   versionNo: selectRow.versionNo
      // }
      // this.$refs.xTable.$refs.xGrid.insertAt(newRow, selectRow)
      this.apiStartLoading()
      this.$API.deliverySchedule
        .jitAllocationBatchCopy(idList)
        .then((res) => {
          if (res.code === 200) {
            this.apiEndLoading()
            this.handleCustomSearch()
            this.$toast({
              content: this.$t('复制成功'),
              type: 'success'
            })
          }
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    // 保存
    handleSave() {
      // const { tableData } = JSON.parse(JSON.stringify(this.$refs.xTable.$refs.xGrid.getTableData()))
      const tableData = JSON.parse(JSON.stringify(this.$refs.xTable.$refs.xGrid.getUpdateRecords()))
      // tableData.forEach((i) => {
      //   if (i.addId) {
      //     delete i.id
      //   }
      // })
      const saveList = tableData.map((i) => {
        return {
          allocationQty: i.allocationQty,
          id: i.id,
          supplierCode: i.supplierCode,
          supplierName: i.supplierName,
          supplierTenantId: i.supplierTenantId
        }
      })
      if (!saveList.length) {
        return
      }
      this.apiStartLoading()
      this.$API.deliverySchedule
        .jitAllocationBatchSave(saveList)
        .then((res) => {
          if (res.code === 200) {
            this.apiEndLoading()
            this.handleCustomSearch()
            this.$toast({
              content: this.$t('保存成功'),
              type: 'success'
            })
          }
        })
        .finally(() => {
          this.apiEndLoading()
        })
      // 调接口再刷新列表
    },
    // 导入
    handleImport() {
      this.$dialog({
        modal: () => import(/* webpackChunkName: "*/ '@/components/uploadDialog/index.vue'),
        data: {
          title: this.$t('导入'),
          paramsKey: 'excel',
          importApi: this.$API.deliverySchedule.importJitAllocation,
          downloadTemplateApi: this.$API.deliverySchedule.importJitAllocationTemp,
          downloadTemplateParams: {
            page: {
              size: 50000,
              current: 1
            },
            ...this.searchFormModel
          }
        },
        success: () => {
          this.handleCustomSearch()
        }
      })
    },
    // 提交
    handleSubmit(ids) {
      const params = {
        ...this.searchFormModel,
        ids
      }
      this.apiStartLoading()
      this.$API.deliverySchedule
        .jitAllocationBatchSubmit(params)
        .then((res) => {
          if (res.code === 200) {
            this.apiEndLoading()
            this.handleCustomSearch()
            this.$toast({
              content: this.$t('提交成功'),
              type: 'success'
            })
          }
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    // 撤回
    handleWithdraw(idList) {
      this.apiStartLoading()
      this.$API.deliverySchedule
        .jitAllocationBatchRevoke(idList)
        .then((res) => {
          if (res.code === 200) {
            this.apiEndLoading()
            this.handleCustomSearch()
            this.$toast({
              content: this.$t('撤回成功'),
              type: 'success'
            })
          }
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    // 删除
    handleDelete(idList) {
      this.apiStartLoading()
      this.$API.deliverySchedule
        .jitAllocationBatchDelete(idList)
        .then((res) => {
          if (res.code === 200) {
            this.apiEndLoading()
            this.handleCustomSearch()
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
          }
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    // 导出
    handleExport() {
      const params = {
        page: {
          current: 1,
          size: 50000
        },
        ...this.searchFormModel
      }
      this.apiStartLoading()
      this.$API.deliverySchedule
        .exportJitAllocation(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = UTILS.getHeadersFileName(res)
          UTILS.download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    editComplete(args) {
      // const { row } = args
      if (args.$event) {
        // 校验点击的对象是否为取消编辑按钮
        // if (args.$event.target.innerText === '取消编辑') {
        //   // 清除编辑状态
        //   this.$refs.xTable.$refs.xGrid.clearEdit()
        //   this.handleCustomSearch()
        //   return
        // }
        //3、 接口调用成功调刷新接口
      }
    },
    editBegin(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        this.getSupplierDataSource({ value: row.supplierCode })
      } else {
        this.getSupplierDataSource({ value: '' })
      }
    },
    activeRowMethod({ row }) {
      // 已确认的数据不可修改
      if (row.status === 2) {
        return false
      }
      return true
    },
    editDisabledEvent() {
      // this.$toast({
      //   content: this.$t('此状态数据不可编辑'),
      //   type: 'warning'
      // })
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'versionNo') {
            this.searchFormModel[key] = this.versionList[0]
          }
        }
      }
      this.handleCurrentChange(1)
    },
    // 采方-获取列表数据
    handleCustomSearch(type) {
      this.$refs.searchFormRef.clearValidate()
      const isEdit = JSON.parse(
        JSON.stringify(this.$refs.xTable.$refs.xGrid.getUpdateRecords())
      ).length
      if (isEdit && type === 'handle') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('该操作将会清空未保存的数据，请确定是否继续？')
          },
          success: () => {
            // this.$refs.searchFormRef.validate(() => {})
            this.$refs.searchFormRef.clearValidate()

            const params = {
              page: {
                size: this.pageSettings.pageSize,
                current: type === 'handle' ? 1 : this.pageCurrent
              },
              ...this.searchFormModel
            }
            if (type === 'handle') {
              this.$refs.mtPage.currentPage = 1
            }
            this.apiStartLoading()
            this.$API.deliverySchedule
              .jitAllocationQuery(params)
              .then((res) => {
                this.apiEndLoading()
                if (res?.code == 200) {
                  const total = res?.data?.total || 0
                  this.pageSettings.totalPages = Math.ceil(
                    Number(total) / this.pageSettings.pageSize
                  )
                  this.pageSettings.totalRecordsCount = Number(total)
                  this.tableData = res?.data?.records || [] // 表格数据
                }
              })
              .catch(() => {
                this.apiEndLoading()
              })
          }
        })
        return
      }
      // this.$refs.searchFormRef.validate(() => {})
      this.$refs.searchFormRef.clearValidate()
      const params = {
        page: {
          size: this.pageSettings.pageSize,
          current: type === 'handle' ? 1 : this.pageCurrent
        },
        ...this.searchFormModel
      }
      if (type === 'handle') {
        this.$refs.mtPage.currentPage = 1
      }
      this.apiStartLoading()
      this.$API.deliverySchedule
        .jitAllocationQuery(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            this.tableData = res?.data?.records || [] // 表格数据
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      const isEdit = JSON.parse(
        JSON.stringify(this.$refs.xTable.$refs.xGrid.getUpdateRecords())
      ).length
      if (isEdit) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('该操作将会清空未保存的数据，请确定是否继续？')
          },
          success: () => {
            this.pageCurrent = currentPage
            this.handleCustomSearch()
          }
        })
        return
      }
      this.pageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      const isEdit = JSON.parse(
        JSON.stringify(this.$refs.xTable.$refs.xGrid.getUpdateRecords())
      ).length
      if (isEdit) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('该操作将会清空未保存的数据，请确定是否继续？')
          },
          success: () => {
            this.pageCurrent = 1
            this.pageSettings.pageSize = pageSize
            this.handleCustomSearch()
          }
        })
        return
      }
      this.pageCurrent = 1
      this.pageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  // div .vxe-cell-border {
  //   border: solid #e6e9ed 1px;
  // }
}
/deep/ .col--seq,
/deep/ .col--checkbox {
  .c--tooltip {
    padding: 0 10px;
  }
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
