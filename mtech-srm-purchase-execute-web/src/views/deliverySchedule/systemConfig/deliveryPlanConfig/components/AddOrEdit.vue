<!-- 采方-停用自动补交货计划配置-新增or编辑 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-form-item prop="configType" :label="$t('配置方式')">
        <mt-select
          v-model="formData.configType"
          :fields="{ text: 'text', value: 'value' }"
          :data-source="configTypeOptions"
          :allow-filtering="true"
          :placeholder="$t('请选择')"
        />
      </mt-form-item>
      <mt-form-item prop="siteCode" :label="$t('工厂')">
        <RemoteAutocomplete
          v-model="formData.siteCode"
          :url="$API.masterData.getSiteListUrl"
          :placeholder="$t('请选择')"
          :fields="{ text: 'siteName', value: 'siteCode' }"
          :search-fields="['siteName', 'siteCode']"
          @change="siteChange"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[2, 4, 6].includes(formData.configType)"
        prop="purOrgCode"
        :label="$t('采购组')"
      >
        <RemoteAutocomplete
          v-model="formData.purOrgCode"
          :url="$API.masterData.getBusinessGroupUrl"
          :placeholder="$t('请选择')"
          :fields="{ text: 'groupName', value: 'groupCode' }"
          :search-fields="['groupName', 'groupCode']"
          @change="purOrgChange"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[3, 4, 6].includes(formData.configType)"
        prop="supplierCode"
        :label="$t('供应商')"
      >
        <RemoteAutocomplete
          v-model="formData.supplierCode"
          url="/masterDataManagement/tenant/supplier/paged-query"
          :placeholder="$t('请选择')"
          :fields="{ text: 'supplierName', value: 'supplierCode' }"
          :search-fields="['supplierName', 'supplierCode']"
          @change="supplierChange"
        />
      </mt-form-item>
      <mt-form-item
        v-if="[5, 6].includes(formData.configType)"
        prop="categoryCode"
        :label="$t('品类')"
      >
        <RemoteAutocomplete
          v-model="formData.categoryCode"
          url="/masterDataManagement/tenant/category/paged-query"
          :placeholder="$t('请选择')"
          :fields="{ text: 'categoryName', value: 'categoryCode' }"
          :search-fields="['categoryName', 'categoryCode']"
          @change="categoryChange"
        />
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { configTypeOptions } from '../config/index'
export default {
  data() {
    return {
      dialogTitle: '',
      actionType: 'add',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {},
      rules: {
        configType: [
          {
            required: true,
            message: this.$t('请选择配置方式'),
            trigger: 'blur'
          }
        ],
        siteCode: [
          {
            required: true,
            message: this.$t('请选择工厂'),
            trigger: 'blur'
          }
        ],
        purOrgCode: [
          {
            required: true,
            message: this.$t('请选择采购组'),
            trigger: 'blur'
          }
        ],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          }
        ],
        categoryCode: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ]
      },

      configTypeOptions
    }
  },
  methods: {
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, row } = args
      this.dialogTitle = title
      this.actionType = actionType
      if (actionType === 'edit') {
        this.formData = row
      }
    },
    beforeOpen() {
      this.formData = {
        consignee: null,
        contactNo: null
      }
      this.$refs.ruleForm.clearValidate()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.handleSave()
        }
      })
    },
    handleSave() {
      let params = { ...this.formData }
      const api = this.$API.deliverySchedule.saveDeliveryPlanConfigApi
      api(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.actionType === 'add' ? this.$t('新增成功') : this.$t('更改成功'),
            type: 'success'
          })
          this.$emit('confirm')
          this.handleClose()
        }
      })
    },
    siteChange(e) {
      this.formData.siteName = e.itemData?.siteName
    },
    purOrgChange(e) {
      this.formData.purOrgName = e.itemData?.groupName
    },
    supplierChange(e) {
      this.formData.supplierName = e.itemData?.supplierName
    },
    categoryChange(e) {
      this.formData.categoryName = e.itemData?.categoryName
    }
  }
}
</script>
