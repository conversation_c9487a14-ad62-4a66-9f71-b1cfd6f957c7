<template>
  <mt-dialog
    ref="dialog1"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    :height="290"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" autocomplete="off">
      <mt-form-item prop="transferPlanCode" :label="$t('转交人')" class="">
        <debounce-filter-select
          v-model="formData.transferPlanCode"
          :request="getTransferPlanOptions"
          :data-source="transferPlanOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'employeeName' }"
          :value-template="transferPlanCodeValueTemplate"
          @change="transferPlanCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { DialogActionType } from '../config/constant'
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'

export default {
  components: {
    DebounceFilterSelect
  },
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      transferPlanOptions: [], // 转交计划员 下列选项
      transferPlanCodeValueTemplate: codeNameColumn({
        firstKey: 'companyOrgName',
        secondKey: 'departmentOrgName',
        thirdKey: 'employeeCode',
        fourthKey: 'employeeName'
      }), // 转交计划员
      dialogTitle: '',
      selectData: null, // 当前编辑的数据
      rules: {
        // 转交计划员
        transferPlanCode: [
          {
            required: true,
            message: this.$t('请选择转交计划员'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      actionType: DialogActionType.Add, // 默认类型：新增
      formData: {
        // 转交计划员
        transferPlanCode: '',
        transferPlanName: '',
        transferPlanId: ''
      }
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      this.$refs.dialog1.ejsRef.show()
      const { title, actionType, selectData } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.actionType = actionType // 弹框模式
      this.selectData = selectData // 行数据
      this.formData = this.initForm(selectData)
      // 编辑时获取所选择的下拉数据源
      this.handleGetSelectOptionsDuringEdit(selectData)
    },
    initForm(selectData) {
      let formData = null
      this.$refs.ruleForm.clearValidate()
      if (this.actionType === DialogActionType.Add) {
        // 新增
        formData = {
          // 转交计划员
          transferPlanCode: '',
          transferPlanName: '',
          transferPlanId: ''
        }
      } else if (this.actionType === DialogActionType.Edit) {
        // 编辑
        formData = {
          // 转交计划员
          transferPlanCode: selectData.transferPlanCode,
          transferPlanName: selectData.transferPlanName,
          transferPlanId: selectData.transferPlanId
        }
      }
      return formData
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 新增、编辑
          this.$emit('confirm', this.formData)
          this.handleClose()
        }
      })
    },
    handleClose() {
      this.$refs.dialog1.ejsRef.hide()
    },
    // 转交计划员 change
    transferPlanCodeChange(args) {
      const { itemData } = args
      if (itemData) {
        this.formData.transferPlanId = itemData.employeeId
        this.formData.transferPlanName = itemData.employeeName
        this.formData.transferPlanCode = itemData.employeeCode
      } else {
        this.formData.transferPlanId = null
        this.formData.transferPlanName = null
        this.formData.transferPlanCode = null
      }
    },
    // 主数据 获取 转交计划员
    getTransferPlanOptions(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyName: text
      }
      this.$API.masterData
        .getCurrentTenantEmployees(params)
        .then((res) => {
          const list = res?.data || []
          this.transferPlanOptions = addCodeNameKeyInList({
            firstKey: 'companyOrgName',
            secondKey: 'departmentOrgName',
            thirdKey: 'employeeCode',
            fourthKey: 'employeeName',
            list
          })
          if (updateData) {
            this.$nextTick(() => {
              updateData(this.transferPlanOptions)
            })
          }
          if (setSelectData) {
            this.$nextTick(() => {
              setSelectData()
            })
          }
        })
        .catch(() => {})
    },
    // 编辑时获取所选择的下拉数据源
    handleGetSelectOptionsDuringEdit(selectData) {
      if (this.actionType === DialogActionType.Edit) {
        // transferPlanCode 转交计划员
        this.getTransferPlanOptions({
          text: selectData.transferPlanCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.transferPlanCode = selectData.transferPlanCode
          }
        })
      } else if (this.actionType === DialogActionType.Add) {
        // 获取 转交计划员
        this.getTransferPlanOptions({ text: undefined })
      }
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped></style>
