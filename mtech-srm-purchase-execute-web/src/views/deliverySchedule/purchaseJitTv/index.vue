<!-- 采方-JIT叫料计划-泛智屏 -->
<template>
  <div class="full-height pt20">
    <!-- 列模板 -->
    <mt-template-page
      ref="templateRef"
      class="frozenFistColumns"
      :hidden-tabs="false"
      :template-config="templateConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @siteCodeChangeRo="siteCodeChangeRo"
      @handleCustomReset="handleCustomReset"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
      @dataBound="handleDataBound"
      @handleSearch="handleSearch"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="factoryCode" :label="$t('工厂')" label-style="top">
              <!-- <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.factoryCode"
                :url="$API.masterData.getSiteListUrl"
                multiple
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                :search-fields="['siteName', 'siteCode']"
              ></RemoteAutocomplete> -->
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.factoryCode"
                url="/srm-purchase-execute/tenant/common/permission/querySiteList"
                multiple
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'dimensionNameValue', value: 'dimensionCodeValue' }"
                records-position="data"
                params-key="keyWord"
              ></RemoteAutocomplete>
            </mt-form-item>
            <!-- <mt-form-item prop="plannerName" :label="$t('计划员')" label-style="top">
              <mt-input
                v-model="searchFormModel.plannerName"
                :show-clear-button="true"
                :placeholder="$t('请输入计划员')"
              />
            </mt-form-item> -->
            <mt-form-item prop="batchCodes" :label="$t('销售订单号')" label-style="top">
              <mt-input
                v-model="batchCodes"
                :show-clear-button="true"
                :placeholder="$t('请输入销售订单号')"
                @change="batchCodeChange"
              />
            </mt-form-item>
            <mt-form-item prop="supplierCodes" :label="$t('供应商编码')" label-style="top">
              <mt-input
                style="flex: 1"
                v-model="supplierCodes"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                :show-clear-button="true"
                @change="supplierChange"
              />
            </mt-form-item>
            <mt-form-item prop="supplierName" :label="$t('供应商名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.supplierName"
                :placeholder="$t('支持模糊搜索')"
                :show-clear-button="true"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="itemGroups" :label="$t('物料组')" label-style="top">
              <div style="display: flex">
                <mt-input
                  style="flex: 1"
                  v-model="itemGroups"
                  @change="itemGroupChange"
                  :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                />
                <mt-checkbox
                  v-model="searchFormModel.joinMaterialGroup"
                  @change="(e) => handleChange(e, 'joinMaterialGroup')"
                  :label="$t('关联查询')"
                  style="vertical-align: sub; display: inline-block; margin-left: 5px; width: 78px"
                />
              </div>
            </mt-form-item>
            <mt-form-item prop="itemCodes" :label="$t('物料编码')" label-style="top">
              <div style="display: flex">
                <mt-input
                  style="flex: 1"
                  v-model="itemCodes"
                  @change="itemChange"
                  :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                />
                <mt-checkbox
                  v-model="searchFormModel.joinMaterialCode"
                  @change="(e) => handleChange(e, 'joinMaterialCode')"
                  :label="$t('关联查询')"
                  style="vertical-align: sub; display: inline-block; margin-left: 5px; width: 78px"
                />
              </div>
            </mt-form-item>
            <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.itemName"
                :placeholder="$t('支持模糊搜索')"
                :show-clear-button="true"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="structSeqNos" :label="$t('结构序号')" label-style="top">
              <mt-input
                v-model="structSeqNos"
                @change="structSeqNoChange"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              />
            </mt-form-item>
            <mt-form-item prop="serialNo" :label="$t('序列号')" label-style="top">
              <mt-input v-model="searchFormModel.serialNo" />
            </mt-form-item>
            <!-- <mt-form-item prop="salesOrder" :label="$t('销售订单')" label-style="top">
              <mt-input
                v-model="searchFormModel.salesOrder"
                :show-clear-button="true"
                :placeholder="$t('请输入销售订单')"
              />
            </mt-form-item> -->
            <mt-form-item prop="workCenterCodes" :label="$t('工作中心')" label-style="top">
              <!-- <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.workCenterCode"
                url="/masterDataManagement/tenant/work-center/paged-query"
                multiple
                :placeholder="$t('请选择工作中心')"
                :fields="{ text: 'workCenterName', value: 'workCenterCode' }"
                :search-fields="['workCenterName', 'workCenterCode']"
              ></RemoteAutocomplete> -->
              <mt-input
                v-model="workCenterCodes"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
                @change="workCenterCodeChange"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-multi-select
                style="flex: 1"
                v-model="searchFormModel.status"
                :show-select-all="true"
                :allow-filtering="true"
                filter-type="Contains"
                :data-source="statusOptions"
                :placeholder="$t('请选择')"
              ></mt-multi-select>
            </mt-form-item>
            <mt-form-item prop="deliveryDate" :label="$t('交货日期')" label-style="top">
              <!-- <mt-date-picker
                style="flex: 1"
                v-model="searchFormModel.deliveryDate"
                float-label-type="Never"
                format="yyyy-MM-dd"
                :allow-edit="false"
                :open-on-focus="true"
                :show-today-button="false"
                placeholder="请选择交货日期"
              ></mt-date-picker> -->
              <mt-date-range-picker
                style="flex: 1"
                v-model="searchFormModel.deliveryDate"
                :allow-edit="false"
                :open-on-focus="true"
                :show-today-button="false"
                @change="(e) => handleDateTimeChange(e, 'deliveryDate')"
                :placeholder="$t('请选择交货日期')"
              />
            </mt-form-item>
            <mt-form-item prop="dispatcherCode" :label="$t('调度员')" label-style="top">
              <mt-multi-select
                style="flex: 1"
                v-model="searchFormModel.dispatcherCode"
                :data-source="dispatcherListOptions"
                :fields="{ text: 'codeAndName', value: 'dispatcherCode' }"
                :show-select-all="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择')"
              ></mt-multi-select>
            </mt-form-item>
            <mt-form-item prop="receiptShipmentStatus" :label="$t('收发货状态')" label-style="top">
              <mt-multi-select
                style="flex: 1"
                v-model="searchFormModel.receiptShipmentStatus"
                :show-select-all="true"
                :allow-filtering="true"
                filter-type="Contains"
                :data-source="receiptShipmentStatusOptions"
                :placeholder="$t('请选择')"
              ></mt-multi-select>
            </mt-form-item>
            <mt-form-item prop="consignee" :label="$t('收货人')" label-style="top">
              <mt-input v-model="searchFormModel.consignee" />
            </mt-form-item>
            <mt-form-item prop="contactWay" :label="$t('联系方式')" label-style="top">
              <mt-input v-model="searchFormModel.contactWay" />
            </mt-form-item>
            <mt-form-item prop="deliverAddr" :label="$t('收货地址')" label-style="top">
              <mt-input v-model="searchFormModel.deliverAddr" />
            </mt-form-item>
            <mt-form-item prop="sourceMethod" :label="$t('来源方式')" label-style="top">
              <mt-select
                style="flex: 1"
                v-model="searchFormModel.sourceMethod"
                :allow-filtering="true"
                :show-clear-button="true"
                filter-type="Contains"
                :data-source="[
                  { text: $t('自动'), value: '1', cssClass: '' },
                  { text: $t('手动'), value: '2', cssClass: '' }
                ]"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
    <!-- 物料选择 弹框 -->
    <materiel-select-dialog
      ref="materielSelectDialog"
      @confirm="materielSelectDialogConfirm"
    ></materiel-select-dialog>
    <!-- 选择转交人弹框 -->
    <transfer-select-dialog
      ref="transferSelectDialog"
      @confirm="transferSelectDialogConfirm"
    ></transfer-select-dialog>
  </div>
</template>

<script>
import { formatTableColumnData, serializeList } from './config/index'
import {
  EditSettings,
  JitTableColumnData,
  JitToolbar,
  RequestType,
  // JitTableColumnImportData,
  ActionType,
  NewRowData,
  Status,
  StatusOptions,
  ComponentChangeType,
  // DeliveryMethod,
  DialogActionType,
  ReceiptShipmentStatusOptions
} from './config/constant'
import { rowDataTemp } from './config/variable'
import { cloneDeep } from 'lodash'
import { BASE_TENANT } from '@/utils/constant'
import MaterielSelectDialog from './components/materielSelectDialog'
// import UploadExcelDialog from '@/components/Upload/uploadExcelDialog'
import { download, getHeadersFileName, timeNumberToDate } from '@/utils/utils'
import TransferSelectDialog from './components/transferSelectDialog'
import dayjs from 'dayjs'

// import ImportDialog from '@/components/Upload/importDialog'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  components: {
    MaterielSelectDialog,
    // UploadExcelDialog,
    TransferSelectDialog
    // ImportDialog
  },
  data() {
    return {
      batchCodes: '',
      workCenterCodes: '',
      supplierCodes: '',
      itemCodes: '',
      itemGroups: '',
      structSeqNos: '',
      searchFormModel: {
        joinMaterialCode: false,
        joinMaterialGroup: false,
        sourceMethod: '2'
      },
      beginData: null,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      transferSelectRowIdList: [], // 转交行id列表
      dispatcherListOptions: [], // 转交行id列表
      templateConfig: [
        {
          activatedRefresh: false,

          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 不使用组件中的toolbar配置
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          // toolbar: JitToolbar,
          toolbar: {
            tools: [JitToolbar, ['Setting']]
          },
          buttonQuantity: 8,
          // gridId: this.$route.path.includes('kt')
          //   ? this.$tableUUID.deliverySchedule.purchaseJitKt.list
          //   : this.$tableUUID.deliverySchedule.purchaseJit.list,
          gridId: '9e3d7e0f-30ec-4370-a250-8a6bba90d149',
          grid: {
            editSettings: EditSettings,
            allowPaging: true, // 分页
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [10, 20, 50, 100, 200, 500, 1000],
              totalRecordsCount: 0
            },
            columnData: formatTableColumnData({
              data: JitTableColumnData
            }),
            dataSource: [],
            // rowDataBound: (args) => {
            //   if (args.data.status == 4) {
            //     args.row.classList.add('bg-light-red')
            //   }
            // },
            virtualPageSize: 30,
            enableVirtualization: true,
            customSelection: true, // 使用自定义勾选列
            showSelected: false,
            selectionSettings: {
              persistSelection: true, // 这个选项很重要，影响到虚拟滚动下能不能操作全选按钮
              type: 'Multiple',
              checkboxOnly: true
            },
            asyncConfig: {
              url: `${BASE_TENANT}/jit/plan/buyer/tv/query`,
              recordsPosition: 'data.buyerDemandPlanPage.records',
              // ignoreDefaultSearch: true,

              // defaultRules: [
              //   {
              //     field: "sourceFrom",
              //     operator: "equal",
              //     value: "KT",
              //   },
              // ],
              serializeList: serializeList
            }
            // frozenColumns: 1, // 行内编辑的表格不可以冻结列，使用 frozenFistColumns 实现
          }
        }
      ],
      addId: '1',

      // componentConfig3: [
      //   {
      //     useToolTemplate: false, // 不使用预置(新增、编辑、删除)
      //     useBaseConfig: true, // 使用组件中的toolbar配置

      //     activatedRefresh: false,
      //     grid: {
      //       allowEditing: true, //开启表格编辑操作
      //       editSettings: {
      //         allowEditing: true,
      //         allowAdding: true,
      //         allowDeleting: true,
      //         mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
      //         showConfirmDialog: false,
      //         showDeleteConfirmDialog: false,
      //         newRowPosition: 'Top'
      //       },
      //       allowPaging: false, // 分页

      //       columnData: formatImportColumnData({
      //         data: JitTableColumnImportData
      //       }),
      //       dataSource: []
      //       // asyncConfig: {
      //       //   url: `${BASE_TENANT}/buyerJitInfo/query`,
      //       //   // defaultRules: [
      //       //   //   {
      //       //   //     field: "sourceFrom",
      //       //   //     operator: "equal",
      //       //   //     value: "KT",
      //       //   //   },
      //       //   // ],
      //       //   serializeList: serializeList,
      //       // },
      //     }
      //   }
      // ],
      isEditing: false, // 正在编辑数据
      dialogType: false,
      versionList: [],
      statusOptions: StatusOptions,
      receiptShipmentStatusOptions: ReceiptShipmentStatusOptions
    }
  },
  mounted() {
    this.getDispatcherList()
    this.$bus.$on('siteCodeChangeClick', () => {
      this.dialogType = true
    })
    this.$bus.$on('itemCodeUpdateBudgetUnitPrice', (e) => {
      // console.log(e);
      this.materielSelectDialogConfirm(e)
    })
  },
  methods: {
    handleChange(e, labelName) {
      this.searchFormModel[labelName] = e.checked
    },
    batchCodeChange(e) {
      if (e) {
        this.searchFormModel.batchCodes = this.batchCodes.split(' ')
      } else {
        this.searchFormModel.batchCodes = null
      }
    },
    workCenterCodeChange(e) {
      if (e) {
        this.searchFormModel.workCenterCodes = this.workCenterCodes.split(' ')
      } else {
        this.searchFormModel.workCenterCodes = null
      }
    },
    supplierChange(e) {
      if (e) {
        this.searchFormModel.supplierCodes = this.supplierCodes.split(' ')
      } else {
        this.searchFormModel.supplierCodes = null
      }
    },
    itemChange(e) {
      if (e) {
        this.searchFormModel.itemCodes = this.itemCodes.split(' ')
      } else {
        this.searchFormModel.itemCodes = null
      }
    },
    // 查询条件操作物料组切割
    itemGroupChange(e) {
      if (e) {
        this.searchFormModel.itemGroups = this.itemGroups.split(' ')
      } else {
        this.searchFormModel.itemGroups = null
      }
    },
    // 查询条件操作结构序号切割
    structSeqNoChange(e) {
      if (e) {
        this.searchFormModel.structSeqNos = this.structSeqNos.split(' ')
      } else {
        this.searchFormModel.structSeqNos = null
      }
    },
    getDispatcherList() {
      this.$API.deliverySchedule.queryDispatcher().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.dispatcherListOptions = data.map((i) => {
            return {
              ...i,
              codeAndName: `${i.dispatcherCode} - ${i.dispatcherName}`
            }
          })
        }
      })
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'From'] = dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        this.searchFormModel[field + 'To'] = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.searchFormModel[field + 'From'] = null
        this.searchFormModel[field + 'To'] = null
      }
    },
    // 重置查询条件
    handleCustomReset() {
      this.structSeqNo = ''
      this.supplierCodes = null
      this.searchFormModel.supplierCodes = null
      this.itemCodes = null
      this.searchFormModel.itemCodes = null
      this.itemGroups = null
      this.searchFormModel.itemGroups = null
      this.structSeqNos = null
      this.searchFormModel.structSeqNos = null
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.searchFormModel.joinMaterialCode = false
      this.searchFormModel.joinMaterialGroup = false
    },
    // 导入
    // importDialogImport() {
    //   this.handleCopy(this.componentConfig3[0].grid.dataSource, '1')
    // },
    handleCopy(row) {
      row.forEach(() => {
        //   e.id = null;
        //   e.serialNumber = null;
        //   e.goodsDemandPlanItemId = null;
        //   e.sourceFrom = "KT";
        //   e.item = [
        //     {
        //       timeInfoTimestamp: Number(e.timeInfoTimestamp),
        //       total: 0,
        //       buyerNum: e.buyerNum,
        //       buyerRemark: e.buyerRemark, // 采购备注
        //       released: 0, //下达
        //       limitNum: 0, //限量数量           //需要给默认值
        //       remainingNum: 0, //剩余可创建数量        //需要给默认值
        //       outstandingNum: 0, // 未清订单数量
        //       jit: e.jit,
        //     },
        //   ];
      })
      console.log(row)
      this.$API.deliverySchedule.buyerJitInfoSaveBatch(row).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })

        // this.$refs.importDialog.showStepSecond()
        // this.$refs.importDialog.handleClose()
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    siteCodeChangeRo() {
      this.dialogType = true
    },
    // toolbar 按钮点击
    handleClickToolBar(args) {
      const { toolbar, gridRef } = args
      // let selectedRecords = grid.getSelectedRecords()
      let selectedRecords = []
      gridRef.dataSource.forEach((item) => {
        if (item.customChecked) {
          selectedRecords.push(item)
        }
      })
      // if (!grid.getSelectedRecords().length) {
      //   selectedRecords = []
      // }
      // const commonToolbar = [
      //   'JitAdd',
      //   'JitImport',
      //   'JitExport',
      //   'Filter',
      //   'Refresh',
      //   'refreshDataByLocal',
      //   'filterDataByLocal',
      //   'resetDataByLocal',
      //   'Setting'
      // ]
      if (toolbar.id === 'closeEdit') {
        args.grid.closeEdit()
        this.$refs.templateRef.refreshCurrentGridData()
      }
      if (this.isEditing && toolbar.id !== 'refreshDataByLocal') {
        // 结束编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }

      // if (selectedRecords.length == 0 && !commonToolbar.includes(toolbar.id)) {
      //   this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      //   return
      // }

      const idList = []
      selectedRecords.forEach((item) => {
        const obj = {
          id: item.id,
          status: item.status,
          serialNumber: item.serialNumber,
          deliveryTime: item.deliveryTime,
          dataSetName: item.dataSetName,
          feedbackStatus: item.feedbackStatus,
          supplierTenantId: item.supplierTenantId,
          dispatcherName: item.dispatcherName,
          supplierName: item.supplierName,
          dispatcherCode: item.dispatcherCode,
          callMaterialQty: item.callMaterialQty,
          cumCallMaterialQty: item.cumCallMaterialQty,
          demandQty: item.demandQty
        }
        idList.push(obj)
      })

      if (toolbar.id === 'JitAdd') {
        // 新增
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id === 'JitDelete') {
        // 删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            this.handleJitDelete({ selectedRecords, idList })
          }
        })
      } else if (toolbar.id === 'JitPublish') {
        // 发布
        this.handleJitPublish({ selectedRecords })
      } else if (toolbar.id === 'JitUnpublish') {
        // 取消发布
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认取消发布选中的数据？')
          },
          success: () => {
            this.handleJitCancelPublish({ selectedRecords, idList })
          }
        })
      } else if (toolbar.id === 'JitConfirm') {
        // 确认
        this.handleJitConfirm({ selectedRecords, idList })
      } else if (toolbar.id === 'JitTransfer') {
        // 转交
        this.handleJitTransfer({ selectedRecords, idList })
      } else if (toolbar.id === 'JitMatchSupplier') {
        // 匹配供应商
        this.handleJitMatchSupplier({ selectedRecords, idList })
      } else if (toolbar.id === 'JitClose') {
        // 关闭
        this.handleJitClosed({ selectedRecords, idList })
      } else if (toolbar.id === 'JitCompelClose') {
        // 强制关闭
        this.handleJitCompelClose({ selectedRecords, idList })
      } else if (toolbar.id === 'JitImport') {
        // 导入
        // this.showUploadExcel(true);
        // this.$refs.importDialog.init({
        //   title: this.$t('导入')
        // })
        this.$dialog({
          modal: () => import('@/components/uploadDialog'),
          data: {
            title: this.$t('导入'),
            importApi: this.$API.deliverySchedule.buyerJitInfoImportTv,
            downloadTemplateApi: this.$API.deliverySchedule.buyerJitInfoImportTempTv,
            paramsKey: 'excel'
            // asyncParams: {
            //   // requestJson: JSON.stringify(parameter),
            // },
          },
          success: () => {
            // 导入之后刷新列表
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        return
      } else if (toolbar.id === 'JitExport') {
        // 导出
        this.buyerJitInfoExport()
      }
    },
    // 文件上传完成
    // uploadCompleted(response) {
    //   this.$refs.importDialog.showStepSecond()
    //   response.data.forEach((item) => {
    //     item.addId = this.addId++
    //     item.deliveryDate = item.deliveryDateName
    //     // item.deliveryDate = Number(item.deliveryDateName);
    //     item.jitUpdateTime = Number(new Date(item.jitUpdateTime))

    //     item.deliveryDate = Number(new Date(item.deliveryDateName))
    //   })
    //   // this.$nextTick(() => {
    //   console.log(response)
    //   if (response.data.length > 0) {
    //     this.componentConfig3[0].grid.dataSource = cloneDeep(response.data)
    //     this.$refs.templateRef3.refreshCurrentGridData()
    //   } else {
    //     this.$refs.importDialog.showStepSecond()
    //     this.$refs.importDialog.handleClose()
    //     this.$toast({
    //       content: this.$t('导入成功'),
    //       type: 'success'
    //     })
    //     this.$refs.templateRef.refreshCurrentGridData()
    //   }
    // },
    // CellTool
    handleClickCellTool(args) {
      const { tool, data } = args

      if (this.isEditing) {
        // 正在编辑时，若不是点击刷新按钮，结束编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }

      if (tool.id === 'JitPublish') {
        // 发布
        this.handleJitPublish({
          selectedRecords: [data]
        })
      } else if (tool.id === 'JitUnpublish') {
        // 取消发布
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认取消发布选中的数据？')
          },
          success: () => {
            this.handleJitCancelPublish({
              selectedRecords: [data],
              idList: [data.id]
            })
          }
        })
      } else if (tool.id === 'JitTransfer') {
        // 转交
        this.handleJitTransfer({
          selectedRecords: data,
          idList: [data.id]
        })
      } else if (tool.id === 'JitClosed') {
        // 关闭
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认关闭选中的数据？')
          },
          success: () => {
            this.handleJitClosed({
              selectedRecords: [data]
              // idList: [data.id]
            })
          }
        })
      }
    },
    // 删除
    handleJitDelete(args) {
      const { idList } = args
      let isValid = true
      // for (let i = 0; i < selectedRecords.length; i++) {
      //   const item = selectedRecords[i]
      //   if (item.status != Status.new && item.status != Status.edit) {
      //     isValid = false
      //     this.$toast({
      //       content: this.$t('请选择新建或已修改状态的数据'),
      //       type: 'warning'
      //     })
      //     break
      //   }
      // }
      if (isValid) {
        // 删除
        this.buyerJitInfoDelete(idList)
      }
    }, // 确认
    handleJitConfirm(args) {
      const { idList } = args
      let isValid = true
      // for (let i = 0; i < selectedRecords.length; i++) {
      //   const item = selectedRecords[i]
      //   if (item.status != Status.abnormal) {
      //     isValid = false
      //     this.$toast({
      //       content: this.$t('请选择反馈-不满足状态的数据'),
      //       type: 'warning'
      //     })
      //     break
      //   }
      // }
      if (isValid) {
        // 发布
        this.buyerJitInfoConfirm(idList)
      }
    },
    // 发布
    handleJitPublish(args) {
      const { selectedRecords } = args
      let isValid = true
      const idList = []
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        if (
          //   item.status != Status.new &&
          //   item.status != Status.edit &&
          //   item.status != Status.transferred &&
          //   item.status != Status.abnormal
          // ) {
          //   isValid = false
          //   this.$toast({
          //     content: this.$t('请选择新建、已修改、反馈异常状态的数据'),
          //     type: 'warning'
          //   })
          //   break
          // } else if (
          !item.supplierCode
        ) {
          isValid = false
          this.$toast({
            content: this.$t('请选择供应商不为空的数据'),
            type: 'warning'
          })
          break
        }
        // const deliveryDateTime = timeNumberToDate({
        //   formatString: 'YYYY-mm-dd HH:MM',
        //   value: item.deliveryDate
        // })
        idList.push({
          id: item.id,
          status: item.status,
          serialNumber: item.serialNumber,
          // deliveryDate: deliveryDateTime.split(' ')[0],
          // deliveryTime: deliveryDateTime.split(' ')[1],
          deliveryTime: item.deliveryTime,
          dataSetName: item.dataSetName,
          feedbackStatus: item.feedbackStatus,
          supplierTenantId: item.supplierTenantId,
          dispatcherName: item.dispatcherName,
          supplierName: item.supplierName,
          dispatcherCode: item.dispatcherCode,
          callMaterialQty: item.callMaterialQty,
          cumCallMaterialQty: item.cumCallMaterialQty,
          demandQty: item.demandQty,
          sourceMethod: item.sourceMethod
        })
      }
      if (isValid) {
        // 发布
        this.buyerJitInfoPublish(idList)
      }
    },
    // 取消发布
    handleJitCancelPublish(args) {
      const { selectedRecords } = args
      let isValid = true
      const idList = []
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        if (item.status != Status.publish) {
          isValid = false
          this.$toast({
            content: this.$t(`第{index}行请选择已发布状态的数据`, { index: item.serialNumber }),
            type: 'warning'
          })
          break
        }
        idList.push({
          id: item.id,
          status: item.status,
          serialNumber: item.serialNumber,
          // deliveryDate: deliveryDateTime.split(' ')[0],
          // deliveryTime: deliveryDateTime.split(' ')[1],
          deliveryTime: item.deliveryTime,
          dataSetName: item.dataSetName,
          feedbackStatus: item.feedbackStatus,
          supplierTenantId: item.supplierTenantId,
          dispatcherName: item.dispatcherName,
          supplierName: item.supplierName,
          dispatcherCode: item.dispatcherCode,
          callMaterialQty: item.callMaterialQty,
          cumCallMaterialQty: item.cumCallMaterialQty,
          demandQty: item.demandQty
        })
      }
      if (isValid) {
        // 取消发布
        this.buyerJitInfoCancelPublish(idList)
      }
    },
    // 转交
    handleJitTransfer(args) {
      const { selectedRecords, idList } = args
      let isValid = true
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        if (
          item.status != Status.new &&
          item.status != Status.transferred &&
          item.status != Status.abnormal
        ) {
          isValid = false
          this.$toast({
            content: this.$t('请选择新建、已转交或反馈异常状态的数据'),
            type: 'warning'
          })
          break
        }
      }
      if (isValid) {
        this.$refs.transferSelectDialog.dialogInit({
          title: this.$t('选择转交人'),
          actionType: DialogActionType.Add
        })
        this.transferSelectRowIdList = idList // 将转交行id存起来
      }
    },
    // 选择 转交人 弹框 确定 数据校验通过时
    transferSelectDialogConfirm(formData) {
      const params = {
        idList: this.transferSelectRowIdList,
        transferPlanId: formData.transferPlanId,
        transferPlannerName: formData.transferPlannerName,
        transferPlanName: formData.transferPlanName
      }
      // 转交
      this.buyerJitInfoChange(params)
    },
    // 匹配供应商
    handleJitMatchSupplier(args) {
      const { selectedRecords, idList } = args
      let isValid = true
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        if (
          item.status != Status.new &&
          item.status != Status.transferred &&
          item.status != Status.abnormal
        ) {
          isValid = false
          this.$toast({
            content: this.$t('请选择新建、已转交状态或反馈异常状态的数据'),
            type: 'warning'
          })
          break
        }
      }
      if (isValid) {
        // 采方jit-匹配供应商
        this.buyerJitInfoMatchSupplier(idList)
      }
    },
    // 关闭
    handleJitClosed(args) {
      const { selectedRecords } = args
      let isValid = true
      const idList = []
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        // if (
        //   item.status != Status.pending &&
        //   item.status != Status.abnormal &&
        //   item.status != Status.normal
        // ) {
        //   isValid = false
        //   this.$toast({
        //     content: this.$t('请选择反馈-不满足或反馈-满足状态的数据'),
        //     type: 'warning'
        //   })
        //   break
        // }
        idList.push({
          id: item.id,
          status: item.status,
          serialNumber: item.serialNumber,
          deliveryTime: item.deliveryTime,
          dataSetName: item.dataSetName,
          feedbackStatus: item.feedbackStatus,
          supplierTenantId: item.supplierTenantId,
          dispatcherName: item.dispatcherName,
          supplierName: item.supplierName,
          dispatcherCode: item.dispatcherCode,
          callMaterialQty: item.callMaterialQty,
          serialNo: item.serialNo,
          cumCallMaterialQty: item.cumCallMaterialQty,
          demandQty: item.demandQty
        })
      }
      if (isValid) {
        // 关闭
        this.buyerJitInfoClose(idList)
      }
    },
    // 强制关闭
    handleJitCompelClose(args) {
      const { selectedRecords } = args
      if (!selectedRecords.length) {
        this.$toast({
          content: this.$t('请先选择一行'),
          type: 'warning'
        })
        return
      }
      let isValid = true
      const idList = []
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        // if (item.status != Status.confirm) {
        //   isValid = false
        //   this.$toast({
        //     content: this.$t(
        //       '仅支持强制关闭已确认的单据，反馈-满足或反馈-不满足的数据可执行操作常规关闭！'
        //     ),
        //     type: 'warning'
        //   })
        //   break
        // }
        idList.push({
          id: item.id,
          status: item.status,
          serialNumber: item.serialNumber,
          deliveryTime: item.deliveryTime,
          dataSetName: item.dataSetName,
          feedbackStatus: item.feedbackStatus,
          supplierTenantId: item.supplierTenantId,
          dispatcherName: item.dispatcherName,
          supplierName: item.supplierName,
          dispatcherCode: item.dispatcherCode,
          callMaterialQty: item.callMaterialQty,
          cumCallMaterialQty: item.cumCallMaterialQty,
          demandQty: item.demandQty
        })
      }
      if (isValid) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('强制关闭可能会导致账实不符，请谨慎使用！')
          },
          success: () => {
            // 关闭
            this.buyerJitCompelClose(idList)
          }
        })
      }
    },
    // 采方jit-采方强制关闭jit
    buyerJitCompelClose(idList) {
      const params = {
        queryReq: { ...this.searchFormModel, page: { current: 1, size: 999999 } },
        chooseReqList: idList
      }
      this.apiStartLoading()
      this.$API.deliverySchedule
        .buyerJitInfoCompelCloseTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // actionBegin 表格编辑生命周期
    actionBegin(args) {
      const { requestType, action, rowData } = args
      // console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args);
      if (args.requestType === 'save') {
        if (args.data.itemCode === null && this.beginData !== null) {
          args.data.itemCode = this.beginData.itemCode
        }
      }
      if (requestType === RequestType.add) {
        // 开始行添加
        this.isEditing = true
        // 即将添加一行，赋值新增行的初始数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
        let newRowData = cloneDeep(NewRowData)
        // 调度员名称 新增时-自动填入当前登录人
        newRowData = {
          ...newRowData,
          scheduleUserName: userInfo.employeeName, // 调度员名称
          dispatcherName: userInfo.employeeCode, // 调度员编码
          scheduleUserId: userInfo.employeeId // 调度员ID
        }
        rowDataTemp.push(newRowData)
        args.rowData = newRowData
        args.data = newRowData
      } else if (requestType === RequestType.save && action === ActionType.add) {
        // 即将保存新增时，将新增中编辑的数据付给组件
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === RequestType.beginEdit) {
        this.beginData = JSON.parse(JSON.stringify(args.rowData))

        if (
          rowData.status !== Status.new &&
          rowData.status !== Status.edit &&
          rowData.status !== Status.publish &&
          rowData.status !== Status.normal &&
          rowData.status !== Status.abnormal
        ) {
          // 数据不可编辑 状态 != 新建 && 已修改 && 反馈异常
          args.cancel = true
          this.$toast({
            content: this.$t('仅新建、已修改、已发布、反馈-满足或反馈-不满足状态的数据可编辑'),
            type: 'warning'
          })
          this.$refs.templateRef.refreshCurrentGridData()
          return
        }
        // 开始行编辑
        this.isEditing = true
        // 即将编辑行，赋值当前行的数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },
    // actionComplete 表格编辑生命周期

    // actionBegin2(args) {
    //   const { requestType, action, rowData, rowIndex } = args
    //   // console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args);
    //   if (requestType === RequestType.save && action === ActionType.edit) {
    //     // 即将保存编辑，保存行编辑后的数据，使数据状态保持
    //     console.log(args, 'actionbegin2')
    //     console.log(rowDataTemp)
    //     args.data = rowData

    //     // this.componentConfig3[0].grid.dataSource[rowIndex] = args.data
    //   } else if (requestType === RequestType.beginEdit) {
    //     // 开始行编辑
    //     this.isEditing = true
    //     // 即将编辑行，赋值当前行的数据
    //     rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
    //     rowDataTemp.push(rowData)
    //   }
    // },
    actionComplete(args) {
      const { requestType, action, rowIndex, index } = args
      const rowData = rowDataTemp[rowDataTemp.length - 1]
      // console.log(
      //   `Complete,\nrequest: ${requestType}\naction: ${action}`,
      //   args
      // );
      if (requestType === RequestType.save && action == ActionType.edit) {
        // 完成编辑行
        this.isEditing = false
        // rowData, rowIndex
        if (!this.isValidSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API
          if (
            (rowData.status === Status.publish ||
              rowData.status === Status.normal ||
              rowData.status === Status.abnormal) &&
            this.beginData.demandQty !== rowData.demandQty
          ) {
            const that = this
            this.$dialog({
              data: {
                title: this.$t('警告'),
                message: this.$t('需求数量修改可能会影响供方交货历史数据，请谨慎使用！')
              },
              success: () => {
                that.buyerJitInfoSave({ rowData, rowIndex })
              },
              close: () => {
                that.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
                that.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
              }
            })
            return
          }
          this.buyerJitInfoSave({ rowData, rowIndex })
        }
      } else if (requestType === RequestType.save && action == ActionType.add) {
        // 完成新增行
        this.isEditing = false
        // rowData, index
        if (!this.isValidSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 采方在系统界面上编辑状态为“已发布”、“反馈-满足”、“反馈-不满足”和“已确认”的数据
          if (
            rowData.status === Status.publish ||
            rowData.status === Status.normal ||
            rowData.status === Status.abnormal ||
            rowData.status === Status.confirm
          ) {
            const idList = [
              {
                id: rowData.id,
                status: rowData.status,
                serialNumber: rowData.serialNumber,
                // deliveryDate: deliveryDateTime.split(' ')[0],
                // deliveryTime: deliveryDateTime.split(' ')[1],
                deliveryTime: rowData.deliveryTime,
                dataSetName: rowData.dataSetName,
                feedbackStatus: rowData.feedbackStatus,
                supplierTenantId: rowData.supplierTenantId,
                dispatcherName: rowData.dispatcherName,
                supplierName: rowData.supplierName,
                dispatcherCode: rowData.dispatcherCode,
                callMaterialQty: rowData.callMaterialQty,
                cumCallMaterialQty: rowData.cumCallMaterialQty,
                demandQty: rowData.demandQty,
                sourceMethod: rowData.sourceMethod
              }
            ]
            this.buyerJitInfoPublish(idList)
            return
          }
          // 调用API
          if (
            (rowData.status === Status.publish ||
              rowData.status === Status.normal ||
              rowData.status === Status.abnormal) &&
            this.beginData.demandQty !== rowData.demandQty
          ) {
            const that = this
            this.$dialog({
              data: {
                title: this.$t('警告'),
                message: this.$t('需求数量修改可能会影响供方交货历史数据，请谨慎使用！')
              },
              success: () => {
                that.buyerJitInfoSave({ rowData, rowIndex })
              },
              close: () => {
                that.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
                that.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
              }
            })
            return
          }
          this.buyerJitInfoSave({ rowData, rowIndex: index })
        }
      } else if (requestType === RequestType.sorting || requestType === RequestType.refresh) {
        // 丢弃编辑中状态
        this.isEditing = false
      }
    },
    // 校验数据
    // 工作中心workCenterCode有值时，项目文本批次projectTextBatch 必填
    isValidSaveData(data) {
      const {
        // deliveryMethod, // 配送方式 必填
        // outsourcedType, // 委外方式 必填
        deliveryTime, // 交货日期-时间 必填
        itemCode, // 物料编码 必填
        demandQty,
        siteCode, // 工厂编码 必填
        // warehouseCode, // 库存地点编码 必填
        // senderAddress, // 送货地址 必填
        // senderName, // 送货联系人 必填
        // senderPhone, // 送货联系电话 必填
        supplierCode
        // subSiteCode,
        // subSiteAddressCode,
        // buyerOrgCode,
        // 收货信息
        // workCenterCode, // 工作中心
        // processorCode, // 加工商编码 配送方式为 直送 时 必填
        // projectTextBatch // 工作中心 有值时，项目文本批次 必填
      } = data
      // console.log("=========================", outsourcedType);
      let valid = true
      // if (deliveryMethod === null || deliveryMethod === undefined) {
      //   this.$toast({
      //     content: this.$t("请选择配送方式"),
      //     type: "warning",
      //   });
      //   valid = false;
      // } else if (outsourcedType === null || outsourcedType === undefined) {
      //   this.$toast({
      //     content: this.$t("请选择委外方式"),
      //     type: "warning",
      //   });
      //   valid = false;
      // } else
      if (!deliveryTime) {
        this.$toast({
          content: this.$t('请选择交货日期'),
          type: 'warning'
        })
        valid = false
      } else if (!itemCode) {
        this.$toast({
          content: this.$t('请选择物料'),
          type: 'warning'
        })
        valid = false
      } else if (!demandQty) {
        this.$toast({
          content: this.$t('请输入需求数量'),
          type: 'warning'
        })
        valid = false
      } else if (!supplierCode) {
        this.$toast({
          content: this.$t('请输入供应商'),
          type: 'warning'
        })
        valid = false
        // } else if (!buyerOrgCode) {
        //   this.$toast({
        //     content: this.$t('请选择采购组'),
        //     type: 'warning'
        //   })
        //   valid = false
      } else if (!siteCode) {
        this.$toast({
          content: this.$t('请选择工厂'),
          type: 'warning'
        })
        valid = false
        // } else if (!warehouseCode) {
        //   this.$toast({
        //     content: this.$t('请选择库存地点'),
        //     type: 'warning'
        //   })
        //   valid = false
        // } else if (!subSiteCode) {
        //   this.$toast({
        //     content: this.$t('请选择分厂'),
        //     type: 'warning'
        //   })
        //   valid = false
        // } else if (!subSiteAddressCode) {
        //   this.$toast({
        //     content: this.$t('请选择分厂库存地点'),
        //     type: 'warning'
        //   })
        //   valid = false
      }
      // else if (!senderAddress && !senderName && !senderPhone) {
      //   // 收货信息
      //   this.$toast({
      //     content: this.$t("请选择收货信息"),
      //     type: "warning",
      //   });
      //   valid = false;
      //   // } else if (!processorCode && deliveryMethod == DeliveryMethod.direct) {
      //   //   // 加工商编码 配送方式为 直送 时 必填
      //   //   this.$toast({
      //   //     content: this.$t("配送方式为直送时，必须选择加工商"),
      //   //     type: "warning",
      //   //   });
      //   //   valid = false;
      // }
      // else if (workCenterCode && !projectTextBatch) {
      //   // 工作中心 有值时，项目文本批次 必填
      //   this.$toast({
      //     content: this.$t('工作中心有值时，项目文本批次必填'),
      //     type: 'warning'
      //   })
      //   valid = false
      // }

      return valid
    },
    // 表格数据绑定完成
    handleDataBound() {},
    getSaveApi(rowData) {
      // 根据数据是否存在创建人数据进行判断区别新增与编辑
      if (rowData.createUserName) {
        return this.$API.deliverySchedule.buyerJitInfoUpdateSaveTv
      }
      return this.$API.deliverySchedule.buyerJitInfoSaveTv
    },
    // 采方jit-保存jit
    buyerJitInfoSave(args) {
      const { rowData, rowIndex } = args
      const deliveryDateTime = timeNumberToDate({
        formatString: 'YYYY-mm-dd HH:MM',
        value: rowData.deliveryTime
      })
      const params = {
        ...rowData,
        thePrimaryKey: undefined,
        consignee: rowData.senderName,
        contactWay: rowData.senderPhone,
        deliverAddr: rowData.senderAddress,
        transferPlannerName: rowData.transferPlanName,
        deliveryDate: deliveryDateTime.split(' ')[0],
        deliveryTime: deliveryDateTime.split(' ')[1]
      }
      this.apiStartLoading()
      this.getSaveApi(params)(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    // 采方jit-采方删除jit
    buyerJitInfoDelete(idList) {
      const params = {
        queryReq: { ...this.searchFormModel, page: { current: 1, size: 999999 } },
        chooseReqList: idList
      }
      this.apiStartLoading()
      this.$API.deliverySchedule
        .buyerJitInfoDeleteTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    }, // 采方jit-采方确认jit
    buyerJitInfoConfirm(idList) {
      const params = {
        queryReq: { ...this.searchFormModel, page: { current: 1, size: 999999 } },
        chooseReqList: idList
      }
      this.apiStartLoading()
      this.$API.deliverySchedule
        .buyerJitInfoConfirmTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' }) // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方jit-采方发布jit
    buyerJitInfoPublish(idList) {
      const params = {
        queryReq: { ...this.searchFormModel, page: { current: 1, size: 999999 } },
        chooseReqList: idList
      }
      this.apiStartLoading()
      this.$API.deliverySchedule
        .buyerJitInfoPublishTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方jit-采方取消发布jit
    buyerJitInfoCancelPublish(idList) {
      const params = {
        queryReq: { ...this.searchFormModel, page: { current: 1, size: 999999 } },
        chooseReqList: idList
      }
      this.apiStartLoading()
      this.$API.deliverySchedule
        .buyerJitInfoCancelPublishTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方jit-jit转交
    buyerJitInfoChange(params) {
      this.apiStartLoading()
      this.$API.deliverySchedule
        .buyerJitInfoChange(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方jit-采方关闭jit
    buyerJitInfoClose(idList) {
      const params = {
        queryReq: { ...this.searchFormModel, page: { current: 1, size: 999999 } },
        chooseReqList: idList
      }
      this.apiStartLoading()
      this.$API.deliverySchedule
        .buyerJitInfoCloseTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方jit-匹配供应商
    buyerJitInfoMatchSupplier(params) {
      this.apiStartLoading()
      this.$API.deliverySchedule
        .buyerJitInfoMatchSupplier(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方jit-采方jitExcel导出
    buyerJitInfoExport() {
      let obj = JSON.parse(sessionStorage.getItem(this.templateConfig[0].gridId))?.visibleCols
      const headerMap = {}
      if (obj !== undefined && obj.length) {
        obj?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'serialNumber') {
            let field = i.field
            if (i.field === 'senderName') {
              field = 'consignee'
            }
            if (i.field === 'senderPhone') {
              field = 'contactWay'
            }
            if (i.field === 'senderAddress') {
              field = 'deliverAddr'
            }
            if (i.field === 'transferPlanName') {
              field = 'transferPlannerName'
            }
            // if (i.field === 'deliveryDate') {
            //   field = 'deliveryTime'
            // }
            headerMap[field] = i.headerText
          }
        })
      } else {
        formatTableColumnData({
          data: JitTableColumnData
        })?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'serialNumber') {
            let field = i.field
            if (i.field === 'senderName') {
              field = 'consignee'
            }
            if (i.field === 'senderPhone') {
              field = 'contactWay'
            }
            if (i.field === 'senderAddress') {
              field = 'deliverAddr'
            }
            if (i.field === 'transferPlanName') {
              field = 'transferPlannerName'
            }
            if (i.field === 'deliveryDate') {
              field = 'deliveryTime'
            }
            headerMap[field] = i.headerText
          }
        })
      }
      const params = {
        page: {
          size: 2000,
          current: 1
        },
        headerMap,
        ...this.searchFormModel
      }
      this.apiStartLoading()
      this.$API.deliverySchedule
        .buyerJitInfoExportTv(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    // 显示隐藏上传弹框
    // showUploadExcel(flag) {
    //   if (flag) {
    //     this.$refs.uploadExcelRef.uploadData = null; // 清空数据
    //     this.$refs.uploadExcelRef.fileLength = 0;
    //     this.$refs.uploadExcelRef.$refs.uploader.files = [];
    //     this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show();
    //   } else {
    //     this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide();
    //   }
    // },
    // 上传成功后
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
      this.$refs.templateRef.refreshCurrentGridData()
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
    },
    // 行编辑 点击搜索按钮
    handleSearch(args) {
      const { data, dataKey } = args
      if (dataKey === 'itemCode') {
        if (this.dialogType === true) {
          this.$refs.materielSelectDialog.dialogInit({
            title: this.$t('选择物料'),
            data
          })
        } else {
          this.$toast({ content: this.$t('请先选择工厂'), type: 'warning' })
        }
        // this.$bus.$on("siteCodeChangeClick", (e) => {
        // 物料 选择物料弹框

        // return;
        // });
        // this.$toast({ content: this.$t("请先选择工厂"), type: "warning" });
      }
    },
    // 选择 物料 弹框点击确认
    materielSelectDialogConfirm(args) {
      const { data } = args
      // this.$bus.$on("siteCodeChangeClick", (e) => {

      // }
      this.$API.masterData
        .getBasicByFacItem({
          organizationId: data[0].organizationId,
          itemCode: data[0].itemCode
        })
        .then((res) => {
          this.$bus.$emit('purchaseGroupCodeChange', {
            purchaseGroupCode: res.data.purchasingInfo.purchaseGroupCode,
            purchaseGroupId: res.data.purchasingInfo.purchaseGroupId,
            purchaseGroupName: res.data.purchasingInfo.purchaseGroupName
          })
        })
      if (data?.length > 0) {
        this.$bus.$emit('purchaseJitColumnChange', {
          requestKey: 'itemCode', // 物料编码
          changeType: ComponentChangeType.code,
          data: {
            itemCode: data[0].itemCode, // 物料编码
            itemName: data[0].itemName, // 物料名称
            itemId: data[0].id // 物料id
          },
          modifiedKeys: [
            'itemCode', // 物料编码
            'itemName', // 物料名称
            'itemId' // 物料id
          ]
        })
        this.$bus.$emit('purchaseJitColumnChange', {
          requestKey: 'itemCode', // 物料编码
          changeType: ComponentChangeType.link,
          data: {
            itemCode: data[0].itemCode, // 物料编码
            itemName: data[0].itemName, // 物料名称
            itemId: data[0].id // 物料id
          },
          modifiedKeys: [
            'siteCode', // 工厂编码
            'planGroupCode' // 计划组编码
          ]
        })
      }
    },

    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .ant-select-selection {
  background: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.42) !important;
}
/deep/ .need-jit-font-red {
  color: red;
}
// 行内 cellTool
/deep/ .column-tool {
  margin-top: 8px;

  .template-svg {
    cursor: pointer;
    font-size: 12px;
    color: #6386c1;

    &:nth-child(n + 2) {
      margin-left: 10px;
    }
  }
}

// 行编辑时包含按钮的项目
/deep/ .input-search-content {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .mt-input {
    flex: 1;
  }

  .mutliselect-container {
    width: 100%;
  }
  // 超过宽度显示省略号
  .text-ellipsis {
    width: 125px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .mt-icons {
    width: 20px;
    flex-shrink: 0;
    text-align: center;
    cursor: pointer;
  }
}

/deep/ .grid-edit-column {
  padding: 12px 0;
  .col-active,
  .col-inactive {
    width: 77px;
    box-sizing: border-box;
    display: block;
    text-align: center;
  }
}

.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  // padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
  }
}
</style>
