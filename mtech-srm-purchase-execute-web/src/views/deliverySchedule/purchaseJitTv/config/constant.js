import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
// 表格编辑 的 RequestType
export const RequestType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  sorting: 'sorting',
  refresh: 'refresh'
}

// 表格编辑 的 action
export const ActionType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  edit: 'edit'
}

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 行编辑触发的 change 事件类型
export const ComponentChangeType = {
  code: 'code', // 例：下拉框选择“供应商”，供应商编码（code)要被修改
  link: 'link' // 例：计划组：根据物料+工厂+计划组的业务组类型 code 带出
}

// 表格 EditSettings
export const EditSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
} // 编辑设置

// 弹出框类型
export const DialogActionType = {
  Add: 0, // 新增
  Edit: 1 // 编辑
}

// 状态 0-新建 1-已修改 3-反馈满足 4-反馈不满足 5-已确认 6-已关闭
export const Status = {
  new: 0, // 新建
  edit: 1, // 已修改
  publish: 2, // 已发布
  normal: 3, // 反馈满足
  abnormal: 4, // 反馈不满足
  confirm: 5, // 已确认
  closed: 6 // 已关闭
}
// 状态 text
export const StatusText = {
  [Status.new]: i18n.t('新建'),
  [Status.edit]: i18n.t('已修改'),
  [Status.publish]: i18n.t('已发布'),
  [Status.abnormal]: i18n.t('反馈-不满足'),
  [Status.normal]: i18n.t('反馈-满足'),
  [Status.confirm]: i18n.t('已确认'),
  [Status.closed]: i18n.t('已关闭')
}
// 状态 class
export const StatusClass = {
  [Status.new]: 'col-active',
  [Status.edit]: 'col-active',
  [Status.publish]: 'col-active',
  [Status.abnormal]: 'col-error',
  [Status.normal]: 'col-active',
  [Status.confirm]: 'col-active',
  [Status.closed]: 'col-inactive'
}
// 状态 对应的 Options
export const StatusOptions = [
  {
    value: Status.new, // 新建
    text: StatusText[Status.new],
    cssClass: StatusClass[Status.new]
  },
  {
    value: Status.edit, // 已修改
    text: StatusText[Status.edit],
    cssClass: StatusClass[Status.edit]
  },
  {
    value: Status.publish, // 已发布
    text: StatusText[Status.publish],
    cssClass: StatusClass[Status.publish]
  },
  {
    value: Status.abnormal, // 反馈异常
    text: StatusText[Status.abnormal],
    cssClass: StatusClass[Status.abnormal]
  },
  {
    value: Status.normal, // 反馈正常
    text: StatusText[Status.normal],
    cssClass: StatusClass[Status.normal]
  },
  {
    value: Status.confirm, // 已确认
    text: StatusText[Status.confirm],
    cssClass: StatusClass[Status.confirm]
  },
  {
    value: Status.closed, // 已关闭
    text: StatusText[Status.closed],
    cssClass: StatusClass[Status.closed]
  }
]

// 发货状态 未发货-1 全部发货-2 部分发货-3
export const DeliveryStatus = {
  notShipped: 1, // 未发货
  allShipped: 2, // 全部发货
  partiallyShipped: 3 // 部分发货
}
// 发货状态 text
export const DeliveryStatusText = {
  [DeliveryStatus.notShipped]: i18n.t('未发货'),
  [DeliveryStatus.allShipped]: i18n.t('全部发货'),
  [DeliveryStatus.partiallyShipped]: i18n.t('部分发货')
}
// 发货状态 Class
export const DeliveryStatusClass = {
  [DeliveryStatus.notShipped]: '',
  [DeliveryStatus.allShipped]: '',
  [DeliveryStatus.partiallyShipped]: ''
}
// 发货状态 对应的 Options
export const DeliveryStatusOptions = [
  {
    value: DeliveryStatus.notShipped, // 未发货
    text: DeliveryStatusText[DeliveryStatus.notShipped],
    cssClass: DeliveryStatusClass[DeliveryStatus.notShipped]
  },
  {
    value: DeliveryStatus.allShipped, // 全部发货
    text: DeliveryStatusText[DeliveryStatus.allShipped],
    cssClass: DeliveryStatusClass[DeliveryStatus.allShipped]
  },
  {
    value: DeliveryStatus.partiallyShipped, // 部分发货
    text: DeliveryStatusText[DeliveryStatus.partiallyShipped],
    cssClass: DeliveryStatusClass[DeliveryStatus.partiallyShipped]
  }
]
// 是否满足 对应的 Options
export const SatisfyOptions = [
  {
    value: '0', // 不满足
    text: i18n.t('不满足'),
    cssClass: DeliveryStatusClass[DeliveryStatus.notShipped]
  },
  {
    value: '1', // 满足
    text: i18n.t('满足'),
    cssClass: DeliveryStatusClass[DeliveryStatus.allShipped]
  }
]

// 收货状态 未收货-1 全部收货-2 部分收货-3
export const ReceiveStatus = {
  notReceived: 1, // 未收货
  allReceived: 2, // 全部收货
  partialReceipt: 3 // 部分收货
}
// 收货状态 text
export const ReceiveStatusText = {
  [ReceiveStatus.notReceived]: i18n.t('未收货'),
  [ReceiveStatus.allReceived]: i18n.t('全部收货'),
  [ReceiveStatus.partialReceipt]: i18n.t('部分收货')
}
// 收货状态 Class
export const ReceiveStatusClass = {
  [ReceiveStatus.notShipped]: '',
  [ReceiveStatus.allReceived]: '',
  [ReceiveStatus.partialReceipt]: ''
}
// 收货状态 对应的 Options
export const ReceiveStatusOptions = [
  {
    value: ReceiveStatus.notReceived, // 未收货
    text: ReceiveStatusText[ReceiveStatus.notReceived],
    cssClass: ReceiveStatusClass[ReceiveStatus.notReceived]
  },
  {
    value: ReceiveStatus.allReceived, // 全部收货
    text: ReceiveStatusText[ReceiveStatus.allReceived],
    cssClass: ReceiveStatusClass[ReceiveStatus.allReceived]
  },
  {
    value: ReceiveStatus.partialReceipt, // 部分收货
    text: ReceiveStatusText[ReceiveStatus.partialReceipt],
    cssClass: ReceiveStatusClass[ReceiveStatus.partialReceipt]
  }
]
export const ReceiptShipmentStatusOptions = [
  {
    value: 0,
    text: i18n.t('部分发货未收')
  },
  {
    value: 1,
    text: i18n.t('全部发货未收')
  },
  {
    value: 2,
    text: i18n.t('部分收货')
  },
  {
    value: 3,
    text: i18n.t('全部收货')
  },
  {
    value: 4,
    text: i18n.t('未发货')
  }
]

// 送货地址配置类型
export const DeliveryAddressConfigType = {
  inventory: 1, // 工厂+库存地点
  processor: 2, // 工厂+加工商
  branch: 3, // 工厂+库存地点+分厂+分厂库存地点
  material: 4 // 工厂+库存地点+物料
}
// 送货地址配置类型 是否默认
export const NeedDefault = {
  yes: 1, // 默认
  no: 2 // 非默认
}

// 配送方式 0直送 1非直送
export const DeliveryMethod = {
  direct: '0', // 直送
  indirect: '1' // 非直送
}
// 配送方式 text
export const DeliveryMethodText = {
  [DeliveryMethod.direct]: i18n.t('直送'),
  [DeliveryMethod.indirect]: i18n.t('非直送')
}
// 配送方式 Class
export const DeliveryMethodClass = {
  [DeliveryMethod.direct]: '',
  [DeliveryMethod.indirect]: ''
}
// 配送方式 对应的 Options
export const DeliveryMethodOptions = [
  {
    value: DeliveryMethod.direct, // 直送
    text: DeliveryMethodText[DeliveryMethod.direct],
    cssClass: DeliveryMethodClass[DeliveryMethod.direct]
  },
  {
    value: DeliveryMethod.indirect, // 非直送
    text: DeliveryMethodText[DeliveryMethod.indirect],
    cssClass: DeliveryMethodClass[DeliveryMethod.indirect]
  }
]

// 委外方式 0标准委外 1销售委外 2非委外
export const OutsourcedTypeOptions = [
  {
    value: '0', // 标准委外
    text: i18n.t('标准委外'),
    cssClass: ''
  },
  {
    value: '1', // 销售委外
    text: i18n.t('销售委外'),
    cssClass: ''
  },
  {
    value: '2', // 非委外
    text: i18n.t('非委外'),
    cssClass: ''
  }
]

// Jit表格 Toolbar
export const JitToolbar = [
  {
    id: 'JitAdd',
    icon: 'icon_table_new',
    permission: ['O_02_1610'],
    title: i18n.t('新增')
  },
  {
    id: 'closeEdit',
    icon: 'icon_table_delete',
    // permission: ["O_02_1141"],
    title: i18n.t('取消编辑')
  },
  // {
  //   id: 'JitMatchSupplier',
  //   icon: 'icon_table_matching',
  //   // permission: ["O_02_1145"],
  //   title: i18n.t('匹配供应商')
  // },
  {
    id: 'JitPublish',
    icon: 'icon_solid_Release',
    permission: ['O_02_1611'],
    title: i18n.t('发布')
  },
  {
    id: 'JitConfirm',
    icon: 'icon_solid_Pauseorder',
    permission: ['O_02_1612'],
    title: i18n.t('确认')
  },
  // {
  //   id: 'JitUnpublish',
  //   icon: '',
  //   permission: ['O_02_1613'],
  //   title: i18n.t('取消发布')
  // },
  {
    id: 'JitDelete',
    icon: 'icon_table_delete',
    permission: ['O_02_1614'],
    title: i18n.t('删除')
  },
  {
    id: 'JitImport',
    icon: 'icon_solid_Import',
    permission: ['O_02_1617'],
    title: i18n.t('导入')
  },
  {
    id: 'JitExport',
    icon: 'icon_solid_export',
    permission: ['O_02_1618'],
    title: i18n.t('导出')
  },
  {
    id: 'JitCompelClose',
    icon: 'icon_table_cancel',
    permission: ['O_02_1615'],
    title: i18n.t('强制关闭')
  },
  {
    id: 'JitClose',
    icon: 'icon_table_cancel',
    permission: ['O_02_1616'],
    title: i18n.t('关闭')
  }
  // {
  //   id: 'JitTransfer',
  //   icon: 'icon_solid_distribute',
  //   // permission: ['O_02_1144'],
  //   title: i18n.t('转交')
  // }
]
// 单元格操作按钮
export const CellTools = [
  {
    id: 'JitPublish',
    icon: '',
    permission: ['O_02_1611'],
    title: i18n.t('发布'),
    visibleCondition: (data) =>
      data.status == Status.new ||
      data.status == Status.transferred ||
      data.status == Status.abnormal // 状态：新建、已转交、反馈异常
  },
  {
    id: 'JitUnpublish',
    icon: '',
    permission: ['O_02_1613'],
    title: i18n.t('取消发布'),
    visibleCondition: (data) => data.status == Status.publish // 状态：已发布
  },
  // {
  //   id: 'JitTransfer',
  //   icon: '',
  //   // permission: ["O_02_1144"],
  //   title: i18n.t('转交'),
  //   visibleCondition: (data) => data.status == Status.new || data.status == Status.transferred // 状态：新建、已转交
  // },
  {
    id: 'JitClosed',
    icon: '',
    permission: ['O_02_1616'],
    title: i18n.t('关闭'),
    visibleCondition: (data) =>
      data.status == Status.pending ||
      data.status == Status.abnormal ||
      data.status == Status.normal // 状态：待反馈、反馈异常、反馈正常
  }
]

// 新增行固定数据
export const NewRowData = {
  id: 'add' + Math.random().toString(36).substring(3, 8),
  batchCode: null, // 批次号 不可编辑
  jitItemNo: null, // JIT 行编号 不可编辑
  status: 0, // 状态 0-新建 1-已修改 3-反馈满足 4-反馈不满足 5-已确认 6-已关闭
  deliveryStatus: 1, // 发货状态 未发货-1 全部发货-2 部分发货-3
  receiveStatus: 1, // 收货状态 未收货-1 全部收货-2 部分收货-3
  deliveryMethod: null, // 配送方式 0直送 1非直送 必填
  productLineCode: null, // 生产线
  deliveryDate: null, // 交货日期-时间 必填
  workOrder: null,
  // supplierRemark: null, // 供应商备注
  planGroupRemark: null, // 计划组备注
  dispatcherRemark: null, // 调度员备注
  plannerRemark: null, // 计划员备注
  itemId: null, // 物料ID
  itemName: null, // 物料名称
  itemCode: null, // 物料编码 必填
  siteCode: null, // 工厂编码 必填
  siteId: null, // 工厂ID
  siteName: null, // 工厂名称
  warehouseCode: null, // 库存地点编码 必填
  warehouseName: null, // 库存地点名称
  planGroupCode: null, // 计划组编码
  planGroupId: null, // 计划组ID
  planGroupName: null, // 计划组名称
  subSiteCode: null, // 分厂编码
  subSiteName: null, // 分厂名称
  subSiteAddressCode: null, // 分厂库存地点编码
  subSiteAddress: null, // 分厂库存地点名称
  senderAddress: null, // 收货地址
  senderName: null, // 送货联系人
  senderPhone: null, // 送货联系电话
  workCenter: null, // 工作中心名称
  workCenterCode: null, // 工作中心编码
  workCenterId: null, // 工作中心ID
  processorCode: null, // 加工商编码 配送方式为 直送 时 必填
  processorName: null, // 加工商名称
  companyCode: null, // 公司编码
  companyId: null, // 公司ID
  companyName: null, // 公司名称
  saleOrder: null, // 销售订单
  supplierCode: null, // 供应商编码
  supplierId: null, // 供应商ID
  supplierName: null, // 供应商名称
  projectTextBatch: null, // 项目文本批次
  batch: null, // 批量
  demandQty: null, // 需求数量
  receiveQty: null, // 收货数量
  transitQty: null, // 在途数量
  remainingDeliveryNum: null, // 剩余送货数量
  cumCallMaterialQty: null, // 累计叫料数量
  cumReceiveQty: null, // 累计收货数量
  accumulativeQuantityShipped: null, // 累计发货数量
  cumTransitQty: null, // 累积在途数量
  scheduleUserName: null, // 调度员名称
  dispatcherName: null, // 调度员编码
  scheduleUserId: null, // 调度员ID
  jitUpdateTime: null, // JIT更新时间
  transferPlannerName: null, // 转交计划员编码
  transferPlanId: null, // 转交计划员ID
  transferPlanName: null, // 转交计划员名称
  tenantId: null, // 租户id
  outsourcedType: null, // 委外方式

  // id: 0,
  buyerOrgCode: '', // 采购组code
  buyerOrgId: 0, // 采购组id
  buyerOrgName: '' // 采购组名称
  // contact: "", // 联系方式
  // itemGroupCode: "", // 品项组编码
  // itemGroupName: "", // 品项组名称
  // receiver: "", // 收货人
  // requestOrderMethod: 0, // 申请转化订单方式 1-独立；2-集中
  // tradeLocationCode: "", // 交货地点code
  // tradeLocationId: 0, // 交货地点id
  // tradeLocationName: "", // 	交货地点name
}

// 选择 物料 弹框 表格列数据
export const MaterielTableColumnData = [
  {
    fieldCode: 'itemCode', // 物料编号
    fieldName: i18n.t('物料编号'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    fieldCode: 'itemName', // 物料名称
    fieldName: i18n.t('物料名称')
  },
  {
    fieldCode: 'categoryResponse.categoryCode', // 品类
    fieldName: i18n.t('品类')
  },
  {
    fieldCode: 'itemDescription', // 规格型号
    fieldName: i18n.t('规格型号')
  },
  {
    fieldCode: 'oldItemCode', // 旧物料编号
    fieldName: i18n.t('旧物料编号')
  },
  {
    fieldCode: 'manufacturerName', // 制造商
    fieldName: i18n.t('制造商')
  }
]

// JIT表格列数据
export const JitTableColumnData = [
  // {
  //   fieldCode: 'checkBox' // 不可编辑
  // },
  {
    fieldCode: 'id'
  },
  {
    fieldCode: 'serialNumber', // 前端定义 不可编辑
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'serialNo',
    fieldName: i18n.t('序列号'),
    allowEditing: false
  },
  {
    fieldCode: 'status', // 状态
    fieldName: i18n.t('状态'),
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    }
  },
  {
    fieldCode: 'receiptShipmentStatus',
    fieldName: i18n.t('收发货状态'),
    allowEditing: false
  },
  {
    fieldCode: 'batchCode', // 批次号 不可编辑
    fieldName: i18n.t('销售订单号'),
    allowEditing: false
  },
  {
    fieldCode: 'productLineCode', // 生产线 输入文本 非必填
    fieldName: i18n.t('生产线')
  },
  {
    fieldCode: 'workCenterCode', // 工作中心 code-name 下拉选择 模糊搜索 非必填 主数据
    fieldName: i18n.t('工作中心编码')
    // workCenter
    // workCenterId
  },
  {
    fieldCode: 'workCenter',
    fieldName: i18n.t('工作中心名称'),
    allowEditing: false
  },
  {
    fieldCode: 'siteCode', // 工厂 下拉框选择 主数据 code-name显示 模糊搜索编号/名称  必填
    fieldName: i18n.t('工厂编码')
    // siteId
    // siteName
  },
  {
    fieldCode: 'siteName',
    fieldName: i18n.t('工厂名称'),
    allowEditing: false
  },
  {
    fieldCode: 'itemCode', // 物料 code-name 下拉框选择 主数据 code 显示，下拉显示code-name 模糊搜索编号/名称
    fieldName: i18n.t('物料编码'),
    searchOptions: {
      // ...MasterDataSelect.material,
      renameField: 'itemCode'
    }
    // itemName
    // itemId
  },
  {
    fieldCode: 'itemName',
    fieldName: i18n.t('物料名称'),
    allowEditing: false
  },
  {
    fieldCode: 'supplierCode', // 供应商 code-name 下拉框选择 供应商主数据 模糊搜索
    fieldName: i18n.t('供应商编码')
    // supplierId
    // supplierName
  },
  {
    fieldCode: 'supplierName',
    fieldName: i18n.t('供应商名称'),
    allowEditing: false
  },
  {
    fieldCode: 'structSeqNo',
    fieldName: i18n.t('结构序号'),
    allowEditing: false
    // itemName
    // itemId
  },
  {
    fieldCode: 'callMaterialQty', // 叫料数量 不可编辑
    fieldName: i18n.t('叫料数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    fieldCode: 'demandQty', // 需求数量 输入数字，小数位数3位 （叫料数量）
    fieldName: i18n.t('需求数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    fieldCode: 'deliveryDate', // 交货日期 选择日期-时间 必填
    fieldName: i18n.t('交货日期'),
    searchOptions: { ...MasterDataSelect.timeRange },
    allowGlobalSorting: true
  },
  {
    fieldCode: 'deliveryTime', // 交货日期 选择日期-时间 必填
    fieldName: i18n.t('交货时间'),
    ignore: true
  },
  {
    // fieldCode: 'senderAddress', // 收货信息 下拉框选择 交货地址配置表 必填
    fieldCode: 'senderAddress', // 收货信息 下拉框选择 交货地址配置表 必填
    fieldName: i18n.t('收货地址')
    // senderAddress 收货地址
    // senderName 送货联系人
    // senderPhone 送货联系电话
  },
  {
    fieldCode: 'warehouseCode', // 库存地点 下拉框选择 主数据 code-name显示 模糊搜索编号/名称  必填
    fieldName: i18n.t('库存地点')
    // warehouseName
  },
  {
    // fieldCode: 'consignee', // 收货人 下拉框选择 交货地址配置表 必填
    fieldCode: 'senderName', // 收货人 下拉框选择 交货地址配置表 必填
    fieldName: i18n.t('收货人(多个用/拼接)')
    // allowEditing: false
  },
  {
    // fieldCode: 'contactWay', // 联系方式 下拉框选择 交货地址配置表 必填
    fieldCode: 'senderPhone', // 联系方式 下拉框选择 交货地址配置表 必填
    fieldName: i18n.t('联系方式(多个用/拼接)')
    // allowEditing: false
  },
  {
    fieldCode: 'isSatisfy', // 是否满足
    fieldName: i18n.t('是否满足')
  },
  {
    fieldCode: 'supplierRemark', // 供应商备注 不可编辑
    fieldName: i18n.t('供应商备注')
  },
  {
    fieldCode: 'dispatcherRemark', // 调度员备注 输入文本 非必填
    fieldName: i18n.t('调度员备注')
  },
  // {
  //   fieldCode: 'salesOrder', // 销售订单 输入文本 必填
  //   fieldName: i18n.t('销售订单')
  // },
  {
    fieldCode: 'cumulativeQuantityShipped',
    fieldName: i18n.t('已发货数量'),
    searchOptions: { elementType: 'number' },
    allowEditing: false
  },
  {
    fieldCode: 'accumulativeQuantityShipped', // 累计发货数量 不可编辑
    fieldName: i18n.t('累计发货数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    fieldCode: 'transitQty', // 在途数量 不可编辑
    fieldName: i18n.t('在途数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    fieldCode: 'receiveQty', // 收货数量 不可编辑
    fieldName: i18n.t('收货数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    fieldCode: 'cumCallMaterialQty', // 累计叫料数量 不可编辑
    fieldName: i18n.t('累计叫料数量')
  },
  // {
  //   fieldCode: 'cumReceiveQty', // 累计收货数量 不可编辑
  //   fieldName: i18n.t('累计收货数量')
  // },
  // {
  //   fieldCode: 'cumTransitQty', // 累积在途数量 不可编辑
  //   fieldName: i18n.t('累积在途数量')
  // },
  {
    fieldCode: 'dispatcherName', // 调度员名称 code-name 新增时-自动填入当前登录人 不可修改
    fieldName: i18n.t('调度员')
    // scheduleUserId
    // dispatcherName
    // scheduleUserName
  },
  {
    fieldCode: 'dispatcherCode', // 调度员编号
    fieldName: i18n.t('调度员编号'),
    allowEditing: false
  },
  {
    fieldCode: 'dispatcherContactWay', // 调度员联系方式
    fieldName: i18n.t('调度员联系方式'),
    allowEditing: false
  },
  {
    fieldCode: 'jitUpdateTime', // JIT更新时间 不可编辑
    fieldName: i18n.t('JIT更新时间'),
    searchOptions: { ...MasterDataSelect.timeRange },
    allowGlobalSorting: true
  },
  {
    fieldCode: 'transferPlannerName', // 转交计划员 code-name 新增时选择人员（组织架构）
    fieldName: i18n.t('转交计划员')
    // transferPlanId
    // transferPlanName
  },
  // {
  //   fieldCode: 'version', // 版本 不可编辑
  //   fieldName: i18n.t('版本')
  // },
  {
    fieldCode: 'jitItemNo', // JIT 行编号 不可编辑
    fieldName: i18n.t('JIT行编号')
  },
  // {
  //   fieldCode: 'remainingDeliveryNum', // 剩余送货数量 不可编辑
  //   fieldName: i18n.t('剩余送货数量'),
  //   type: 'number',
  //   searchOptions: { elementType: 'number' }
  // },
  {
    fieldCode: 'plannerRemark', // 计划员备注 输入文本 非必填
    fieldName: i18n.t('计划员备注')
  },
  {
    fieldCode: 'projectTextBatch', // 项目文本批次 输入文本 非必填
    fieldName: i18n.t('项目文本批次')
  },
  {
    fieldCode: 'sourceMethod', //
    fieldName: i18n.t('来源方式'),
    allowFiltering: false,
    allowEditing: false,
    ignore: true,
    valueConverter: {
      type: 'map',
      map: [
        { text: i18n.t('自动'), value: 1, cssClass: '' },
        { text: i18n.t('手动'), value: 2, cssClass: '' }
      ]
    }
  },
  {
    fieldCode: 'createUserName', //
    fieldName: i18n.t('创建人'),
    allowEditing: false,
    allowFiltering: false
  },
  {
    fieldCode: 'createTime', //
    fieldName: i18n.t('创建时间'),
    searchOptions: { ...MasterDataSelect.timeRange },
    allowGlobalSorting: true
  }
  // {
  //   fieldCode: 'dispatcherPhone', // 调度员联系方式
  //   fieldName: i18n.t('调度员联系方式')
  // }
  // {
  //   fieldCode: 'plannerName',
  //   fieldName: i18n.t('计划员')
  // }
  // {
  //   fieldCode: 'workOrder', // 生产工单 不可编辑
  //   fieldName: i18n.t('生产工单'),
  //   allowEditing: false
  // },
  // {
  //   fieldCode: "deliveryStatus", // 发货状态
  //   fieldName: i18n.t("发货状态"),
  // },
  // {
  //   fieldCode: "receiveStatus", // 收货状态
  //   fieldName: i18n.t("收货状态"),
  // },
  // {
  //   fieldCode: "deliveryMethod", // 配送方式
  //   fieldName: i18n.t("配送方式"),
  // },
  // {
  //   fieldCode: "outsourcedType", // 委外方式
  //   fieldName: i18n.t("委外方式"),
  // },
  // {
  //   fieldCode: 'planGroupRemark', // 计划组备注 输入文本 非必填
  //   fieldName: i18n.t('计划组备注')
  // },
  // {
  //   fieldCode: 'dispatcherRemark', // 调度员备注 输入文本 非必填
  //   fieldName: i18n.t('调度员备注')
  // },
  // {
  //   fieldCode: 'warehouseCode', // 库存地点 下拉框选择 主数据 code-name显示 模糊搜索编号/名称  必填
  //   fieldName: i18n.t('库存地点')
  //   // warehouseName
  // },
  // {
  //   fieldCode: 'buyerOrgCode', //
  //   fieldName: i18n.t('采购组'),
  //   searchOptions: {
  //     ...MasterDataSelect.businessGroupIn,
  //     renameField: 'buyerOrgCode'
  //   }
  //   // planGroupId
  //   // planGroupName
  // },
  // {
  //   fieldCode: "planGroupCode", // 计划组 code-name显示 模糊搜索编号/名称
  //   fieldName: i18n.t("计划组"),
  //   // planGroupId
  //   // planGroupName
  // },
  // {
  //   fieldCode: 'subSiteCode', // 分厂名称 code-name 下拉框选择 交货地址配置表 非必填
  //   fieldName: i18n.t('分厂'),
  //   searchOptions: { ...MasterDataSelect.subSiteCodeBuyer }

  //   // subSiteName
  // },
  // {
  //   fieldCode: 'subSiteAddressCode', // 分厂库存地点 code-name 下拉框选择 交货地址配置表 非必填
  //   fieldName: i18n.t('分厂库存地点'),
  //   searchOptions: { ...MasterDataSelect.subSiteAddressBuyer }

  //   // subSiteAddress
  // },
  // {
  //   fieldCode: "processorCode", // 加工商 code-name 下拉选择 供应商主数据 模糊搜索，配送方式为直送时必填，选择工作中心时，根据工作中心配置表匹配带出
  //   fieldName: i18n.t("加工商"),
  //   // processorName
  // },
  // {
  //   fieldCode: 'companyCode', // 公司 code-name 不可编辑 工厂带出来
  //   fieldName: i18n.t('公司')
  //   // companyId
  //   // companyName
  // },
  // // {
  // //   fieldCode: "saleOrder", // 销售订单 输入文本 非必填
  // //   fieldName: i18n.t("销售订单"),
  // // },
  // {
  //   fieldCode: 'projectTextBatch', // 项目文本批次 输入文本 非必填
  //   fieldName: i18n.t('项目文本批次')
  // },
  // // {
  // //   fieldCode: "batch", // 批量 输入文本 非必填
  // //   fieldName: i18n.t("批量"),
  // // },
  // {
  //   fieldCode: 'createUserName', //
  //   fieldName: i18n.t('创建人'),
  //   allowFiltering: false
  // },
  // {
  //   fieldCode: 'closePersonName', //
  //   fieldName: i18n.t('关闭人'),
  //   allowFiltering: false
  // },
  // {
  //   fieldCode: 'closeTime', //
  //   fieldName: i18n.t('关闭时间'),
  //   allowFiltering: false
  // },
  // {
  //   fieldCode: 'createTime', //
  //   fieldName: i18n.t('创建时间'),
  //   searchOptions: { ...MasterDataSelect.timeRange },
  //   allowGlobalSorting: true
  // }
]
// export const JitTableColumnImportData = [
//   {
//     fieldCode: 'errorMsg',
//     fieldName: '错误原因',
//     width: '200',
//     allowEditing: false,
//     headerTemplate: () => {
//       return {
//         template: Vue.component('headers', {
//           template: `
//               <div class="headers">
//                 <span style="color: red">*</span>
//                 <span style="color: red" class="e-headertext">{{$t('错误原因')}}</span>
//               </div>
//             `
//         })
//       }
//     },
//     template: () => {
//       return {
//         template: Vue.component('headers', {
//           template: `
//               <div class="headers">
//                 <span style="color: red">{{data.errorMsg}}</span>
//               </div>
//             `,
//           data() {
//             return {
//               data: {}
//             }
//           }
//         })
//       }
//     }
//   },
//   // {
//   //   fieldCode: "batchCode", // 批次号 不可编辑
//   //   fieldName: i18n.t("版本号"),
//   // },
//   {
//     fieldCode: 'subSiteCode', // 分厂名称 code-name 下拉框选择 交货地址配置表 非必填
//     fieldName: i18n.t('分厂')
//     // subSiteName
//   },
//   {
//     fieldCode: 'siteCode', // 工厂 下拉框选择 主数据 code-name显示 模糊搜索编号/名称  必填
//     fieldName: i18n.t('工厂')
//     // siteId
//     // siteName
//   },
//   {
//     fieldCode: 'productLineCode', // 生产线 输入文本 非必填
//     fieldName: i18n.t('生产线')
//   },
//   {
//     fieldCode: 'itemCode', // 物料 code-name 下拉框选择 主数据 code 显示，下拉显示code-name 模糊搜索编号/名称
//     fieldName: i18n.t('物料')
//     // itemName
//     // itemId
//   },
//   {
//     fieldCode: 'demandQty', // 需求数量 输入数字，小数位数3位 （叫料数量）
//     fieldName: i18n.t('需求数量')
//   },
//   {
//     fieldCode: 'deliveryDate', // 交货日期 选择日期-时间 必填
//     fieldName: i18n.t('交货日期'),
//     allowGlobalSorting: true
//   },
//   {
//     fieldCode: 'supplierCode', // 供应商 code-name 下拉框选择 供应商主数据 模糊搜索
//     fieldName: i18n.t('供应商')
//     // supplierId
//     // supplierName
//   },
//   {
//     fieldCode: 'warehouseCode', // 库存地点 下拉框选择 主数据 code-name显示 模糊搜索编号/名称  必填
//     fieldName: i18n.t('库存地点')
//     // warehouseName
//   },
//   {
//     fieldCode: 'subSiteAddressCode', // 分厂库存地点 code-name 下拉框选择 交货地址配置表 非必填
//     fieldName: i18n.t('分厂库存地点')
//     // subSiteAddress
//   },
//   {
//     fieldCode: 'buyerOrgCode', //
//     fieldName: i18n.t('采购组'),
//     searchOptions: {
//       ...MasterDataSelect.businessGroupIn,
//       renameField: 'buyerOrgCode'
//     }
//     // planGroupId
//     // planGroupName
//   },
//   {
//     fieldCode: 'dispatcherName', // 调度员名称 code-name 新增时-自动填入当前登录人 不可修改
//     fieldName: i18n.t('调度员')
//     // scheduleUserId
//     // dispatcherName
//     // scheduleUserName
//   },
//   {
//     fieldCode: 'jitUpdateTime', // JIT更新时间 不可编辑
//     fieldName: i18n.t('JIT更新时间'),
//     searchOptions: { ...MasterDataSelect.timeRange },
//     allowGlobalSorting: true
//   },
//   // {
//   //   fieldCode: "planGroupCode", // 计划组 code-name显示 模糊搜索编号/名称
//   //   fieldName: i18n.t("计划组"),
//   //   // planGroupId
//   //   // planGroupName
//   // },

//   {
//     fieldCode: 'senderAddress', // 收货信息 下拉框选择 交货地址配置表 必填
//     fieldName: i18n.t('收货信息')
//     // senderAddress 收货地址
//     // senderName 送货联系人
//     // senderPhone 送货联系电话
//   },
//   {
//     fieldCode: 'senderName', // 收货信息 下拉框选择 交货地址配置表 必填
//     fieldName: i18n.t('送货联系人')
//     // senderAddress 收货地址
//     // senderName 送货联系人
//     // senderPhone 送货联系电话
//   },
//   {
//     fieldCode: 'senderPhone', // 收货信息 下拉框选择 交货地址配置表 必填
//     fieldName: i18n.t('送货联系电话')
//     // senderAddress 收货地址
//     // senderName 送货联系人
//     // senderPhone 送货联系电话
//   },
//   // {
//   //   fieldCode: "workCenterCode", // 工作中心 code-name 下拉选择 模糊搜索 非必填 主数据
//   //   fieldName: i18n.t("工作中心"),
//   //   // workCenter
//   //   // workCenterId
//   // },
//   // {
//   //   fieldCode: "processorCode", // 加工商 code-name 下拉选择 供应商主数据 模糊搜索，配送方式为直送时必填，选择工作中心时，根据工作中心配置表匹配带出
//   //   fieldName: i18n.t("加工商"),
//   //   // processorName
//   // },
//   // {
//   //   fieldCode: "batch", // 批量 输入文本 非必填
//   //   fieldName: i18n.t("批量"),
//   // },

//   {
//     fieldCode: 'transferPlannerName', // 转交计划员 code-name 新增时选择人员（组织架构）
//     fieldName: i18n.t('转交计划员')
//     // transferPlanId
//     // transferPlanName
//   }
// ]
