import Vue from 'vue'

import {
  timeNumberToDate,
  timeStringToDate,
  numberInputOnKeyDown,
  addCodeNameKeyInList
} from '@/utils/utils'
import {
  ComponentChangeType,
  ComponentType,
  DeliveryMethod,
  DeliveryAddressConfigType,
  NeedDefault,
  Status
} from './constant'
import { rowDataTemp } from './variable'
import { uniqBy } from 'lodash'

import { cloneDeep } from 'lodash'
import {} from '@/utils/utils'
import { utils } from '@mtech-common/utils'

export const ColumnComponent = {
  // 文本 可编辑、可监听关联、可带搜索按钮、可格式化显示
  inputText: (args) => {
    const {
      dataKey,
      showClearBtn,
      disabled,
      maxlength,
      isItem,
      isListenChange,
      hasSearch,
      format
    } = args
    const template = () => {
      return {
        template: Vue.component('inputComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <div :class="[hasSearch && 'input-search-content']">
              <div>
              <mt-select
              :popup-width="300"
              v-if="hasSearch===true"
              :data-source="itemList"
              :filtering="serchText"
              :disabled='disableConverter'
              @change="itemClick"
              :show-clear-button="true"
              :allow-filtering="true"
              @open="startOpen"
              :fields="{ text: 'label', value: 'itemCode' }"
              v-model="data.itemCode"
              width="100%"
            ></mt-select>
            <mt-input
            v-model="componentData"
            autocomplete="off"
            v-if='hasSearch === false'
            :show-clear-button="showClearBtn"
            :disabled="disableConverter"
            :maxlength="maxlength"
            @input="onInput"
          ></mt-input>
            <mt-icon
            v-show='!disableConverter'
            v-if="hasSearch===true"
            name="icon_input_search"
            @click.native="handleSearch"
          ></mt-icon>


              </div>
              </div>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              itemList: [],
              isItem,
              disableConverter: false,
              disabled,
              showClearBtn,
              maxlength,
              organizationCode: null,
              hasSearch,
              componentData: null // 组件内部绑定的变量
            }
          },
          //   <mt-icon
          //   v-if="hasSearch"
          //   name="icon_input_search"
          //   @click.native="handleSearch"
          // ></mt-icon>
          mounted() {
            // if (disabled) {
            //   this.disableConverter = disabled
            // }
            if (this.data.status === Status.abnormal || this.data.status === Status.normal) {
              this.disableConverter = true
            }
            if (
              (dataKey === 'batchCode' ||
                dataKey === 'itemCode' ||
                dataKey === 'productLineCode' ||
                dataKey === 'warehouseCode' ||
                dataKey === 'dispatcherRemark' ||
                dataKey === 'plannerRemark' ||
                dataKey === 'projectTextBatch') &&
              // this.data.id
              this.data.status !== 0 &&
              this.data.status !== 1
            ) {
              this.disableConverter = true
            }

            if (isListenChange) {
              // 监听变化
              this.onComponentChange()
            }
            if (this.isItem === true) {
              this.$bus.$on('itemCodeUpdateBudgetUnitPrice', (e) => {
                // this.hasSearch = false
                let itemCode = ''
                if (e && e.data && e.data[0] && e.data[0].itemCode) {
                  itemCode = e.data[0].itemCode
                }
                this.init(itemCode)
              })
            }

            // this.init(this.data.itemCode);
            if (hasSearch) {
              this.$bus.$on('siteCodeChangeClick', (e) => {
                this.organizationCode = e.siteCode
                if (this.data.itemCode === null) {
                  this.init('')
                } else {
                  this.init(this.data.itemCode)
                }
              })
              // this.$bus.$on("itemCodeConfirm", (e) => {
              //   this.hasSearch = false;
              //   this.init(e.data.data.itemCode);
              // });
            }

            this.doFormat()
            this.doDisableConverter()
          },
          beforeDestroy() {
            if (isListenChange) {
              // 移除监听
              this.$bus.$off('purchaseJitColumnChange')
            }
          },
          methods: {
            serchText(e) {
              this.init(e.text)
            },
            itemClick(e) {
              if (e && e.e !== null) {
                this.$bus.$emit('itemCodeUpdateBudgetUnitPrice', {
                  data: [e.itemData]
                })
              }

              // this.$parent.$emit("confirm", { data: [...e.itemData] });
            },
            startOpen() {
              if (this.organizationCode == null) {
                this.$toast({
                  content: this.$t('请先选择工厂'),
                  type: 'warning'
                })
              }
              // if (this.organizationCode !== null) {
              // } else {
              //   this.$toast({
              //     content: this.$t('请先选择工厂'),
              //     type: 'warning'
              //   })
              // }
            },
            init(e) {
              let itemObj = {
                page: {
                  current: 1,
                  pages: 6,
                  size: 20
                },
                defaultRules: [
                  {
                    field: 'organizationCode',
                    operator: 'equal',
                    value: this.organizationCode
                  }
                  // {
                  //   field: 'jitFlag',
                  //   operator: 'equal',
                  //   value: 1
                  // }
                ],

                rules: [
                  {
                    field: 'itemCode',
                    operator: 'contains',
                    value: e
                  }
                ]
              }
              this.$API.masterData.getItemPage(itemObj).then((res) => {
                res.data.records.forEach((item) => {
                  item.label = item.itemCode + '-' + item.itemName
                })
                const list = res?.data.records || []
                this.itemList = [...list]
                this.hasSearch = true
              })
            },
            onInput(e) {
              if (dataKey === 'batchCode') {
                e = e.replaceAll(' ', '')
                this.componentData = e.replaceAll(' ', '')
              }
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e
            },
            // 监听变化
            onComponentChange() {
              // 监听被变化
              this.$bus.$on(`purchaseJitColumnChange`, (e) => {
                const { modifiedKeys, data, changeType } = e
                if (
                  modifiedKeys.includes(this.dataKey) &&
                  changeType === ComponentChangeType.code
                ) {
                  // 发布事件的数据修改了，关联值修改
                  const newData = cloneDeep(data[this.dataKey])
                  this.data[dataKey] = newData
                  rowDataTemp[rowDataTemp.length - 1][dataKey] = newData

                  if (this.dataKey === 'itemCode') {
                    // 物料的行字段
                    this.data.itemName = data.itemName
                    this.data.itemId = data.itemId

                    rowDataTemp[rowDataTemp.length - 1]['itemName'] = data.itemName
                    rowDataTemp[rowDataTemp.length - 1]['itemId'] = data.itemId
                  }
                  this.doFormat()
                }
              })
            },
            // 点击搜索
            handleSearch() {
              this.$parent.$emit('handleSearch', {
                dataKey,
                data: rowDataTemp[rowDataTemp.length - 1]
              })
            },
            doFormat() {
              if (format && typeof format === 'function') {
                // 格式化显示
                this.componentData = format(this.data)
              } else {
                this.componentData = this.data[dataKey]
              }
            },
            doDisableConverter() {
              // if (disableConverter && typeof disableConverter === "function") {
              //   // 禁用转换
              //   if (hasSearch) {
              //     this.hasSearch = !disableConverter(this.data);
              //   } else {
              //     this.disabled = disableConverter(this.data);
              //   }
              // }
              if (
                this.data.status !== 0 &&
                this.data.status !== 1 &&
                dataKey === 'productLineCode'
              ) {
                this.disabled = true
              }
            }
          }
        })
      }
    }
    return template
  },
  // 下拉框 编辑
  select: (args) => {
    const {
      dataKey,
      selectOptions,
      fields,
      allowFiltering,
      showClearBtn,
      modifiedKeys,
      modifiedRelation,
      disabled,
      disableConverter
    } = args
    const template = () => {
      return {
        template: Vue.component('selectComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-select
                @change="selectChange"
                v-model="data[dataKey]"
                :allow-filtering="allowFiltering"
                :data-source="selectOptions"
                :show-clear-button="showClearBtn"
                :placeholder="$t('请选择')"
                :open-dispatch-change="false"
                :fields="fields"
                :disabled="disabled"
              ></mt-select>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              selectOptions,
              fields,
              dataKey,
              allowFiltering,
              showClearBtn,
              ComponentChangeType,
              disabled
            }
          },
          mounted() {
            this.doDisableConverter()
          },
          methods: {
            // 修改中的值
            selectChange(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e.value
              if (modifiedKeys?.length > 0) {
                // 触发改变的值
                this.triggerCodeChange(e.itemData)
              }
            },
            // 触发改变的值
            triggerCodeChange(selectData) {
              const args = {
                requestKey: dataKey, // 触发请求的key
                modifiedKeys: modifiedKeys || [], // 要被修改的key列表
                changeType: ComponentChangeType.code, // 修改类型
                data: selectData, // 发出的值
                modifiedRelation // 对应数据源中的 key 关系
              }
              this.$bus.$emit('purchaseJitColumnChange', args)
            },
            doDisableConverter() {
              if (disableConverter && typeof disableConverter === 'function') {
                // 禁用转换
                this.disabled = disableConverter(this.data)
              }
            }
          }
        })
      }
    }
    return template
  },
  // 数字 编辑
  number: (args) => {
    const { dataKey, showClearBtn, precision, maxValue, minValue, disabled, disableConverter } =
      args
    const template = () => {
      return {
        template: Vue.component('numberComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-input-number
                @keydown.native="numberInputOnKeyDown"
                v-model="data[dataKey]"
                :max="maxValue"
                :min="minValue"
                :precision="precision"
                :show-spin-button="false"
                :show-clear-button="showClearBtn"
                :disabled="disabled"
                @input="onInput"
              ></mt-input-number>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              maxValue,
              minValue,
              showClearBtn,
              numberInputOnKeyDown,
              precision: precision || 2,
              disabled
            }
          },
          mounted() {
            this.doDisableConverter()
          },
          methods: {
            onInput(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e
            },
            doDisableConverter() {
              if (disableConverter && typeof disableConverter === 'function') {
                // 禁用转换
                this.disabled = disableConverter(this.data)
              }
            }
          }
        })
      }
    }
    return template
  },
  // 空的显示
  empty: () => {
    return {
      template: Vue.component('emptyComponent', {
        template: `<div></div>`,
        data: function () {
          return {
            data: {}
          }
        }
      })
    }
  },
  // 不可编辑的文字显示 带 cellTools
  text: (args) => {
    const { dataKey, type, cellTools, valueConverter } = args
    const template = () => {
      return {
        template: Vue.component('textComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content"><span :class=[cssClass]>{{data[dataKey] | format}}</span></div>
            <div class="column-tool mt-flex invite-btn" v-if="haveShowCellTool && type === ComponentType.view">
              <div v-for="(cellTool, index) in cellTools"
                :key="index" :id="cellTool.id"
                class="template-svg"
                v-if="!cellTool.visibleCondition || cellTool.visibleCondition(data)"
                v-permission="cellTool.permission"
                @click.stop="clickCellTool({tool:cellTool, data})"
                ><MtIcon :name="cellTool.icon" />&nbsp<span class="icon-title">{{cellTool.title}}</span>
              </div>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              type,
              ComponentType,
              cellTools,
              haveShowCellTool: false,
              cssClass: ''
            }
          },
          mounted() {
            // 判断是否显示cellTool
            if (cellTools?.length > 0) {
              for (let i = 0; i < cellTools.length; i++) {
                const cellTool = cellTools[i]
                if (!cellTool.visibleCondition || cellTool.visibleCondition(this.data)) {
                  this.haveShowCellTool = true
                  break
                }
              }
            }
            // 设置 cssClass
            if (valueConverter?.type === 'map' && valueConverter?.map?.length) {
              const mapList = valueConverter.map
              const findItem = mapList.find((item) => item.value === this.data[dataKey])
              this.cssClass = findItem?.cssClass
            }
          },
          filters: {
            format: (value) => {
              let data = value
              if (valueConverter?.type === 'map' && valueConverter?.map?.length) {
                // 转换
                const mapList = valueConverter.map
                const findItem = mapList.find((item) => String(item.value) === String(value))
                data = findItem?.text
              }
              return data
            }
          },
          methods: {
            clickCellTool(data) {
              this.$parent.$emit('handleClickCellTool', data)
            }
          }
        })
      }
    }
    return template
  },
  // 时间显示
  timeDate: (args) => {
    const { dataKey, isDateTime, isDate, isTime, isCompare } = args
    let that = null
    const template = () => {
      return {
        template: Vue.component('timeDateComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <span v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</span>
              <span v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</span>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              isDateTime,
              isDate,
              isTime
            }
          },
          created() {
            that = this
          },
          filters: {
            dateFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({
                  formatString: 'YYYY-mm-dd',
                  value
                })
              } else {
                str = timeNumberToDate({
                  formatString: 'YYYY-mm-dd',
                  value
                })
              }

              return str
            },
            timeFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({
                  formatString: 'HH:MM:SS',
                  value
                })
              } else {
                str = timeNumberToDate({
                  formatString: 'HH:MM:SS',
                  value
                })
              }

              if (isCompare && str === '00:00:00' && value === that.data.deliveryDate) {
                str = ''
              }

              return str
            }
          }
        })
      }
    }

    return template
  },
  // 时间输入
  timeInput: (args) => {
    const {
      dataKey,
      disabled,
      showClearBtn,
      allowEdit,
      isDate,
      isTime,
      isDateTime,
      maxDate,
      disableConverter
    } = args

    const template = () => {
      return {
        template: Vue.component('timeInputComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <mt-time-picker
              v-if="isTime"
              v-model="componentData"
              :disabled="disabled"
              :show-clear-button="showClearBtn"
              :allow-edit="allowEdit"
              @change="onChange"
              placeholder=""
            ></mt-time-picker>
            <mt-date-picker
              v-if="isDate"
              v-model="componentData"
              :disabled="disabled"
              :show-clear-button="showClearBtn"
              :allow-edit="allowEdit"
              :max="maxDate"
              @change="onChange"
              placeholder=""
            ></mt-date-picker>
            <mt-date-time-picker
              v-if="isDateTime"
              v-model="componentData"
              :min="minDate"
              :disabled="disabled"
              :show-clear-button="showClearBtn"
              :allow-edit="allowEdit"
              @change="onChange"
              placeholder=""
            ></mt-date-time-picker>
          </div>`,
          data: function () {
            return {
              data: {},
              minDate: new Date(),

              dataKey,
              disabled,
              showClearBtn,
              allowEdit,
              isDate,
              isTime,
              isDateTime,
              maxDate,
              componentData: null // 组件内部绑定的变量
            }
          },
          filters: {},
          mounted() {
            this.componentData = this.formateInitData(this.data[dataKey])
            this.doDisableConverter()
          },
          methods: {
            formateInitData(value) {
              let date = null
              if (this.data[dataKey] == 0 || !this.data[dataKey]) {
                // 数据库时间戳默认值为 0，为 0 时不显示，为空时不显示
                return null
              } else if (typeof value === 'string') {
                let tmpData = null
                if (isNaN(Number(value))) {
                  // 处理时间字符串的情况 例如：YYY-MM-DD
                  tmpData = new Date(value)
                } else {
                  // 处理字符串时间戳的情况
                  tmpData = new Date(Number(value))
                }

                // 校验是否转换为了时间
                if (isNaN(tmpData.getTime())) {
                  date = null
                } else {
                  date = tmpData
                }
              } else if (typeof value === 'number') {
                // 处理数字时间戳的情况
                date = new Date(value)
              } else if (typeof value === 'object') {
                // 处理时间对象的情况
                date = value
              }

              return date
            },
            onChange(e) {
              let data = null
              if (isDate) {
                data = e
              } else if (isTime) {
                data = e.value
              } else if (isDateTime) {
                data = e.value
              }

              this.data[dataKey] = data
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = Number(data)
            },
            doDisableConverter() {
              if (disableConverter && typeof disableConverter === 'function') {
                // 禁用转换
                this.disabled = disableConverter(this.data)
              }
            }
          }
        })
      }
    }

    return template
  },
  // 带红星的表头
  requiredHeader: (args) => {
    const { headerText } = args
    const template = () => {
      return {
        template: Vue.component('requiredHeaderComponent', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{ headerText }}</span>
              </div>
            `,
          data: function () {
            return {
              data: {},
              headerText
            }
          },
          beforeDestroy() {},
          mounted() {},
          methods: {}
        })
      }
    }
    return template
  },
  // 采购组
  buyerOrgSelect: () => {
    return {
      template: Vue.component('buyerOrgSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
        <div class="field-content">
          <mt-select
            v-model="data.buyerOrgCode"
            :data-source="buyerOptions"
            :fields="{ text: 'groupName', value: 'groupCode' }"
            :show-clear-button="true"
            :filtering="serchText"
      :allow-filtering="true"

            @change="buyerOrgCodeChange"
            :open-dispatch-change="false"
            :placeholder="$t('请选择')"
            :disabled="disabled"
          ></mt-select>
        </div>
      </div>
        `,
        data: function () {
          return {
            data: {},
            buyerOptions: [], //  下拉选项
            disabled: false
          }
        },
        mounted() {
          // 监听变化
          this.init('')
          // this.doDisableConverter();
          this.doDisableConverter()
          // 监听传过来的采购组
          this.$bus.$on(`purchaseGroupCodeChange`, (e) => {
            this.buyerOrgCodeChange({
              itemData: {
                groupCode: e.purchaseGroupCode,
                groupName: e.purchaseGroupName,
                id: e.purchaseGroupId
              }
            })
            this.data.buyerOrgCode = e.purchaseGroupCode

            // this.data.buyerOrgCode = e.purchaseGroupCode
            // this.init(e.purchaseGroupCode);
          })
        },
        methods: {
          serchText(val) {
            this.init(val.text)
          },
          init(val) {
            let params = {
              fuzzyParam: val,
              groupTypeCode: 'BG001CG'
            }
            this.$API.masterData.getbussinessGroup(params).then((res) => {
              this.buyerOptions = res.data
            })
          },
          buyerOrgCodeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].buyerOrgId = itemData.id // id
              rowDataTemp[rowDataTemp.length - 1].buyerOrgCode = itemData.groupCode // code
              rowDataTemp[rowDataTemp.length - 1].buyerOrgName = itemData.groupName // name
            } else {
              rowDataTemp[rowDataTemp.length - 1].buyerOrgId = null // id
              rowDataTemp[rowDataTemp.length - 1].buyerOrgCode = null // code
              rowDataTemp[rowDataTemp.length - 1].buyerOrgName = null // name
            }
          },
          doDisableConverter() {
            if (this.data.status === Status.abnormal || this.data.status === Status.normal) {
              // 反馈异常状态下只能修改 叫料数量（需求数量）
              this.disabled = true
            }
          }
        }
      })
    }
  },
  // 工厂 code-name显示 根据物料选择带出数据源 不可模糊搜索（API不支持）
  siteCodeSelect: () => {
    return {
      template: Vue.component('siteCodeSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.siteCode"
              :data-source="siteOptions"
              :popup-width="250"
              :fields="{ text: 'theCodeName', value: 'siteCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              @change="siteCodeChange"
              :open-dispatch-change="true"
              :placeholder="$t('请选择')"
              :disabled="disabled"
            ></mt-select>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},

            siteOptions: [], // 工厂 下拉选项
            disabled: false
          }
        },
        mounted() {
          this.initGetSite()
          // 监听变化
          this.onComponentChange()
          this.doDisableConverter()
        },
        beforeDestroy() {
          this.$bus.$off('purchaseJitColumnChange')
          this.$bus.$off('siteCodeChangeClick')

          this.$bus.$off('purchaseGroupCodeChange')
        },
        methods: {
          // 主数据 根据物料 查询关联的工厂列表
          // getSite(args) {
          //   const { itemCode, updateData, setSelectData } = args;
          //   const params = {
          //     itemCode,
          //     orgLevelTypeCode: "ORG06",
          //   };
          //   this.$API.masterData.getFactoryList(params).then((res) => {
          //     if (res) {
          //       const list = res?.data || [];
          //       this.siteOptions = addCodeNameKeyInList({
          //         firstKey: "organizationCode",
          //         secondKey: "organizationName",
          //         list,
          //       });
          //       if (updateData) {
          //         this.$nextTick(() => {
          //           updateData(this.siteOptions);
          //         });
          //       }
          //       if (setSelectData) {
          //         this.$nextTick(() => {
          //           setSelectData();
          //         });
          //       }
          //     }
          //   });
          // },
          // getSourceCompany1(e) {
          //   var searchData = this.siteOptions;
          //   if (e.text == "") e.updateData(searchData);
          //   else {
          //     let query = new Query().select(["theCodeName", "theCodeName"]);
          //     // change the type of filtering
          //     query =
          //       e.text !== ""
          //         ? query.where("theCodeName", "contains", e.text, true)
          //         : query;
          //     // console.log(query);
          //     e.updateData(searchData, query);
          //   }
          // },
          getSite() {
            // const { updateData, setSelectData } = args;
            // const params = {
            //   itemCode,
            //   orgLevelTypeCode: "ORG06",
            // };
            this.$API.masterData.getSiteFindByPermission().then((res) => {
              if (res) {
                const list = res?.data || []
                this.siteOptions = addCodeNameKeyInList({
                  firstKey: 'siteCode',
                  secondKey: 'siteName',
                  list
                })
              }
            })
          },
          // 初始化检索 工厂
          initGetSite() {
            // const itemCode = this.data.itemCode;
            // if (itemCode) {
            // this.getSite({
            //   itemCode: itemCode,
            // });

            this.getSite()
            // }
            // 设置 分厂下拉、分厂库存地点下拉、收货信息下拉
            this.setShippingAddressColumn()
          },
          // 工厂 change
          siteCodeChange(args) {
            console.log(args)
            console.log(this.data)
            const { itemData } = args
            this.$bus.$emit('siteCodeChangeClick', {
              ...itemData
            })
            this.$parent.$emit('siteCodeChangeRo', {
              ...itemData
            })

            if (args.e !== null) {
              if (itemData) {
                rowDataTemp[rowDataTemp.length - 1].itemCode = '' //
                rowDataTemp[rowDataTemp.length - 1].itemName = '' //
                this.$bus.$emit('purchaseJitColumnChange', {
                  requestKey: 'itemCode', // 物料编码
                  changeType: ComponentChangeType.code,
                  data: {
                    itemCode: '', // 物料编码
                    itemName: '', // 物料名称
                    itemId: '' // 物料id
                  },
                  modifiedKeys: [
                    'itemCode', // 物料编码
                    'itemName', // 物料名称
                    'itemId' // 物料id
                  ]
                })
                rowDataTemp[rowDataTemp.length - 1].siteId = itemData.id // id
                rowDataTemp[rowDataTemp.length - 1].siteCode = itemData.organizationCode
                  ? itemData.organizationCode
                  : itemData.siteCode // code
                rowDataTemp[rowDataTemp.length - 1].siteName = itemData.organizationName
                  ? itemData.organizationName
                  : itemData.siteName // name
              } else {
                rowDataTemp[rowDataTemp.length - 1].siteId = null // id
                rowDataTemp[rowDataTemp.length - 1].siteCode = null // code
                rowDataTemp[rowDataTemp.length - 1].siteName = null // name
              } // 发布事件 库存地点数据源更新
              this.$bus.$emit('purchaseJitColumnChange', {
                requestKey: 'siteCode', // 工厂编码
                changeType: ComponentChangeType.link,
                data: {
                  siteId: rowDataTemp[rowDataTemp.length - 1].siteId, // 工厂id
                  siteCode: rowDataTemp[rowDataTemp.length - 1].siteCode, // 工厂编码
                  siteName: rowDataTemp[rowDataTemp.length - 1].siteName // 工厂名称
                },
                modifiedKeys: [
                  'warehouseCode' // 库存地点编码
                ]
              })
              // 设置 分厂下拉、分厂库存地点下拉、收货信息下拉
              this.setShippingAddressColumn()

              // 发布事件 公司 值更新
              this.$bus.$emit('purchaseJitColumnChange', {
                requestKey: 'siteCode', // 工厂编码
                changeType: ComponentChangeType.code,
                data: {
                  siteCode: rowDataTemp[rowDataTemp.length - 1].siteCode // 工厂编码
                },
                modifiedKeys: [
                  'companyCode' // 公司编码
                ]
              })
            }
          },
          // 监听变化
          onComponentChange() {
            // 监听被变化
            // this.$bus.$on(`purchaseJitColumnChange`, (e) => {
            //   const { modifiedKeys, data, changeType } = e;
            //   if (
            //     modifiedKeys.includes("siteCode") &&
            //     changeType === ComponentChangeType.link
            //   ) {
            //     // 物料修改了，重新获取工厂
            //     // this.getSite({
            //     //   itemCode: data.itemCode,
            //     // });
            //     this.data.siteId = null;
            //     this.data.siteCode = null;
            //     this.data.siteName = null;
            //     rowDataTemp[rowDataTemp.length - 1]["siteId"] = null;
            //     rowDataTemp[rowDataTemp.length - 1]["siteCode"] = null;
            //     rowDataTemp[rowDataTemp.length - 1]["siteName"] = null;
            //   }
            // });
          },
          // 设置 分厂下拉、分厂库存地点下拉、收货信息下拉
          setShippingAddressColumn() {
            if (rowDataTemp[rowDataTemp.length - 1].deliveryMethod === DeliveryMethod.indirect) {
              // 配送方式为 "非直送" 时，通过工厂+库存地点 查询 送货地址配置下拉
              const siteCode = rowDataTemp[rowDataTemp.length - 1].siteCode
              const warehouseCode = rowDataTemp[rowDataTemp.length - 1].warehouseCode
              const params = {
                siteAddress: warehouseCode, //库存地点
                siteCode: siteCode //工厂
              }
              if (warehouseCode && siteCode) {
                // 查询 送货地址配置下拉 数据源（分厂下拉、分厂库存地点下拉、收货信息下拉）
                this.getShippingAddressConfig(params)
              }
            } else if (
              rowDataTemp[rowDataTemp.length - 1].deliveryMethod === DeliveryMethod.direct
            ) {
              // 配送方式为 "直送" 时，通过工厂+加工商 查询 送货地址配置下拉
              const siteCode = rowDataTemp[rowDataTemp.length - 1].siteCode
              const processorCode = rowDataTemp[rowDataTemp.length - 1].processorCode
              const params = {
                supplierCode: processorCode, //加工商
                siteCode: siteCode //工厂
              }
              if (siteCode && processorCode) {
                // 查询 送货地址配置下拉 数据源（分厂下拉、分厂库存地点下拉、收货信息下拉）
                this.getShippingAddressConfig(params)
              }
            }
          },
          // 查询 送货地址配置下拉 数据源（分厂下拉、分厂库存地点下拉、收货信息下拉）
          getShippingAddressConfig(params) {
            this.$API.deliverySchedule.siteNewTenantExtendqueryBySite(params).then((res) => {
              console.log(res.data)
              const dataList = res?.data || []
              // 重置 分厂、分厂库存地点、收货信息 下拉数据源
              this.setDeliveryAddressOptions(dataList)
              // 重置 分厂、分厂库存地点、收货信息 选中的数据
              this.setDeliveryAddressDefaultData(dataList)
            })
          },
          // 重置 分厂、分厂库存地点、收货信息 下拉数据源
          setDeliveryAddressOptions(dataList) {
            const subSiteList = [] // 分厂下拉数据
            const subSiteAddressList = [] // 分厂库存地点下拉数据
            const senderInfoList = [] // 送货地址+联系人+电话下拉数据
            dataList.forEach((item) => {
              // configGroupType 配置方式：
              if (item.configGroupType === DeliveryAddressConfigType.branch) {
                // 工厂+库存地点+分厂+分厂库存地点
                if (item.subSiteCode) {
                  subSiteList.push({
                    configId: item.id, // 配置id
                    subSiteCode: item.subSiteCode, // 分厂code
                    subSiteName: item.subSiteName // 分厂name
                  })
                }
                if (item.subSiteAddress) {
                  subSiteAddressList.push({
                    configId: item.id, // 配置id
                    subSiteAddressCode: item.subSiteAddress, // 分厂库存地点code
                    subSiteAddress: item.subSiteAddressName // 分厂库存地点name
                  })
                }
                if (item.consigneeAddress || item.consigneeName || item.consigneePhone) {
                  senderInfoList.push({
                    configId: item.id, // 配置id
                    senderAddress: item.consigneeAddress || null, // 收货人地址
                    senderName: item.consigneeName || null, // 收货人名称
                    senderPhone: item.consigneePhone || null // 收货人联系方式
                  })
                }
              } else if (item.configGroupType === DeliveryAddressConfigType.material) {
                // 工厂+库存地点+物料
                if (item.consigneeAddress || item.consigneeName || item.consigneePhone) {
                  senderInfoList.push({
                    configId: item.id, // 配置id
                    senderAddress: item.consigneeAddress || null, // 收货人地址
                    senderName: item.consigneeName || null, // 收货人名称
                    senderPhone: item.consigneePhone || null // 收货人联系方式
                  })
                }
              } else if (item.configGroupType === DeliveryAddressConfigType.inventory) {
                // 工厂+库存地点
                if (item.consigneeAddress || item.consigneeName || item.consigneePhone) {
                  senderInfoList.push({
                    configId: item.id, // 配置id
                    senderAddress: item.consigneeAddress || null, // 收货人地址
                    senderName: item.consigneeName || null, // 收货人名称
                    senderPhone: item.consigneePhone || null // 收货人联系方式
                  })
                }
              } else if (item.configGroupType === DeliveryAddressConfigType.processor) {
                // 工厂+加工商
                if (item.consigneeAddress || item.consigneeName || item.consigneePhone) {
                  senderInfoList.push({
                    configId: item.id, // 配置id
                    senderAddress: item.consigneeAddress || null, // 收货人地址
                    senderName: item.consigneeName || null, // 收货人名称
                    senderPhone: item.consigneePhone || null // 收货人联系方式
                  })
                }
              }
            })

            // 重置 分厂、分厂库存地点、收货信息 下拉数据源
            // 工厂 发出数据源修改事件
            const args = {
              requestKey: 'siteCode', // 触发请求的key
              modifiedKeys: [
                'subSiteCode', // 分厂
                'subSiteAddressCode', // 分厂库存地点
                'senderAddress' // 送货地址+联系人+电话
              ],
              changeType: ComponentChangeType.link, // 修改类型
              data: {
                subSiteList, // 分厂
                subSiteAddressList, // 分厂库存地点
                senderInfoList // 送货地址+联系人+电话
              } // 发出的值
            }
            this.$bus.$emit('purchaseJitColumnChange', args)
          },
          // 根据优先级选中送货地址配置默认的数据
          setDeliveryAddressDefaultData(dataList) {
            const branchList = [] // 工厂+库存地点+分厂+分厂库存地点
            const materialList = [] // 工厂+库存地点+物料
            const inventoryList = [] // 工厂+库存地点
            const processorList = [] // 工厂+加工商
            let result = {} // 结果
            dataList.forEach((item) => {
              // configGroupType 配置方式：
              // 优先级:
              // 工厂+库存地点+分厂+分厂库存地点
              // 工厂+库存地点+物料
              // 工厂+库存地点
              // 工厂+加工商
              if (item.configGroupType === DeliveryAddressConfigType.branch) {
                // 工厂+库存地点+分厂+分厂库存地点
                branchList.push(item)
              } else if (item.configGroupType === DeliveryAddressConfigType.material) {
                // 工厂+库存地点+物料
                materialList.push(item)
              } else if (item.configGroupType === DeliveryAddressConfigType.inventory) {
                // 工厂+库存地点
                inventoryList.push(item)
              } else if (item.configGroupType === DeliveryAddressConfigType.processor) {
                // 工厂+加工商
                processorList.push(item)
              }
            })
            // needDefault 是否默认
            if (branchList.length > 0) {
              // 工厂+库存地点+分厂+分厂库存地点 将默认的找出来
              const theDefault = branchList.find((item) => item.needDefault === NeedDefault.yes)
              if (!theDefault) {
                // 没有默认的，取第一个
                result = branchList[0]
              } else {
                result = theDefault
              }
            } else if (materialList.length > 0) {
              //  工厂+库存地点+物料 将默认的找出来
              const theDefault = materialList.find((item) => item.needDefault === NeedDefault.yes)
              if (!theDefault) {
                // 没有默认的，取第一个
                result = materialList[0]
              } else {
                result = theDefault
              }
            } else if (inventoryList.length > 0) {
              // 工厂+库存地点 将默认的找出来
              const theDefault = inventoryList.find((item) => item.needDefault === NeedDefault.yes)
              if (!theDefault) {
                // 没有默认的，取第一个
                result = inventoryList[0]
              } else {
                result = theDefault
              }
            } else if (processorList.length > 0) {
              // 工厂+加工商 将默认的找出来
              const theDefault = processorList.find((item) => item.needDefault === NeedDefault.yes)
              if (!theDefault) {
                // 没有默认的，取第一个
                result = processorList[0]
              } else {
                result = theDefault
              }
            }

            // 工厂 发出值修改事件
            const emitResult = {
              configId: result.id, // 配置id
              subSiteCode: result.subSiteCode || null, // 分厂code
              subSiteName: result.subSiteName || null, // 分厂name
              subSiteAddressCode: result.subSiteAddress || null, // 分厂库存地点code
              subSiteAddress: result.subSiteAddressName || null, // 分厂库存地点name
              consigneeAddressCode: result.consigneeAddressCode || null, // 收货地址Code 用不到
              senderName: result.consigneeName || null, // 收货人名称
              senderPhone: result.consigneePhone || null, // 收货人联系方式
              senderAddress: result.consigneeAddress || null // 收货人地址
            }

            // 重置 分厂、分厂库存地点、收货信息 选中的数据
            const args = {
              requestKey: 'siteCode', // 触发请求的key
              modifiedKeys: [
                'subSiteCode', // 分厂
                'subSiteAddressCode', // 分厂库存地点
                'senderAddress' // 送货地址+联系人+电话
              ],
              changeType: ComponentChangeType.code, // 修改类型
              data: emitResult // 发出的值
            }
            this.$bus.$emit('purchaseJitColumnChange', args)
          },
          doDisableConverter() {
            if (this.data.status !== 0 && this.data.status !== 1) {
              // 反馈异常状态下只能修改 叫料数量（需求数量）
              this.disabled = true
            }
            // if (this.data.status === Status.abnormal || this.data.status === Status.normal) {
            //   // 反馈异常状态下只能修改 叫料数量（需求数量）
            //   this.disabled = true
            // }
          }
        }
      })
    }
  },
  // 库存地点 code-name显示 根据 工厂 选择带出数据源 可模糊搜索
  warehouseCodeSelect: () => {
    return {
      template: Vue.component('warehouseCodeSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.warehouseCode"
              :filtering="doGetDataSource"
              :data-source="warehouseOptions"
              :fields="{ text: 'theCodeName', value: 'locationCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              @change="warehouseCodeChange"

              :placeholder="$t('请选择')"
              :disabled="disabled"
            ></mt-select>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            warehouseOptions: [], // 库存地点 下拉选项
            doGetDataSource: () => {},
            disabled: false
          }
        },
        mounted() {
          this.initGetWarehouse()
          this.doDisableConverter()

          this.doGetDataSource = utils.debounce(this.getWarehouse, 1000)
          // 监听变化
          this.onComponentChange()
        },
        beforeDestroy() {
          this.$bus.$off('purchaseJitColumnChange')
        },
        methods: {
          // 主数据 获取库存地点
          getWarehouse(args) {
            const { text, updateData, setSelectData } = args
            let currentSiteCode = rowDataTemp[rowDataTemp.length - 1].siteCode
            const params = {
              commonCode: currentSiteCode || undefined, // 工厂code
              dataLimit: 50,
              fuzzyParam: text
            }
            this.$API.masterData.getLocationFuzzyQuery(params).then((res) => {
              if (res) {
                const list = res?.data || []
                // list.map((item) => {
                //   // 优先使用 externalCode（外部代码）字段（外部代码），如果没有，则使用 locationCode 字段
                //   if (item.externalCode) {
                //     item.locationCode = item.externalCode;
                //     if (item.externalName) {
                //       item.locationName = item.externalName;
                //     }
                //   }

                //   return item;
                // })
                this.warehouseOptions = addCodeNameKeyInList({
                  firstKey: 'locationCode',
                  secondKey: 'locationName',
                  list
                })
                if (updateData) {
                  this.$nextTick(() => {
                    updateData(this.warehouseOptions)
                  })
                }
                if (setSelectData) {
                  this.$nextTick(() => {
                    setSelectData()
                  })
                }
              }
            })
          },
          // 初始化检索 根据 工厂 获取 库存地点
          initGetWarehouse() {
            const warehouseCode = rowDataTemp[rowDataTemp.length - 1].warehouseCode
            this.getWarehouse({
              text: warehouseCode,
              setSelectData: () => {
                // api获取数据后重新赋值，防止没有赋上值得情况
                this.data.warehouseCode = warehouseCode
              }
            })
          },
          // 库存地点 change
          warehouseCodeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].warehouseCode = itemData.locationCode
              rowDataTemp[rowDataTemp.length - 1].warehouseName = itemData.locationName
            } else {
              rowDataTemp[rowDataTemp.length - 1].warehouseCode = null
              rowDataTemp[rowDataTemp.length - 1].warehouseName = null
            }

            console.log(rowDataTemp)

            console.log(rowDataTemp[rowDataTemp.length - 1].deliveryMethod)
            console.log(DeliveryMethod.indirect)

            // if (
            // rowDataTemp[rowDataTemp.length - 1].deliveryMethod ===
            // DeliveryMethod.indirect
            // ) {
            // 配送方式为 "非直送" 时，通过工厂+库存地点 查询 送货地址配置下拉
            const siteCode = rowDataTemp[rowDataTemp.length - 1].siteCode
            const warehouseCode = rowDataTemp[rowDataTemp.length - 1].warehouseCode
            const params = {
              siteCode: siteCode, // 工厂
              siteAddress: warehouseCode // 库存地点
            }
            if (siteCode && warehouseCode) {
              this.getShippingAddressConfig(params)
            }
            // }
          },
          // 查询 送货地址配置下拉 数据源（分厂下拉、分厂库存地点下拉、收货信息下拉）
          getShippingAddressConfig(params) {
            this.$API.deliverySchedule.siteNewTenantExtendqueryBySite(params).then((res) => {
              console.log(res.data)

              const dataList = res?.data || []
              // 重置 分厂、分厂库存地点、收货信息 下拉数据源
              this.setDeliveryAddressOptions(dataList)
              // 重置 分厂、分厂库存地点、收货信息 选中的数据
              this.setDeliveryAddressDefaultData(dataList)
            })
          },
          // 重置 分厂、分厂库存地点、收货信息 下拉数据源
          setDeliveryAddressOptions(dataList) {
            const subSiteList = [] // 分厂下拉数据
            const subSiteAddressList = [] // 分厂库存地点下拉数据
            const senderInfoList = [] // 送货地址+联系人+电话下拉数据
            dataList.forEach((item) => {
              // configGroupType 配置方式：
              if (item.configGroupType === DeliveryAddressConfigType.branch) {
                // 工厂+库存地点+分厂+分厂库存地点
                if (item.subSiteCode) {
                  subSiteList.push({
                    configId: item.id, // 配置id
                    subSiteCode: item.subSiteCode, // 分厂code
                    subSiteName: item.subSiteName // 分厂name
                  })
                }
                if (item.subSiteAddress) {
                  subSiteAddressList.push({
                    configId: item.id, // 配置id
                    subSiteAddressCode: item.subSiteAddress, // 分厂库存地点code
                    subSiteAddress: item.subSiteAddressName // 分厂库存地点name
                  })
                }
                if (item.consigneeAddress || item.consigneeName || item.consigneePhone) {
                  senderInfoList.push({
                    configId: item.id, // 配置id
                    senderAddress: item.consigneeAddress || null, // 收货人地址
                    senderName: item.consigneeName || null, // 收货人名称
                    senderPhone: item.consigneePhone || null // 收货人联系方式
                  })
                }
              } else if (item.configGroupType === DeliveryAddressConfigType.material) {
                // 工厂+库存地点+物料
                if (item.consigneeAddress || item.consigneeName || item.consigneePhone) {
                  senderInfoList.push({
                    configId: item.id, // 配置id
                    senderAddress: item.consigneeAddress || null, // 收货人地址
                    senderName: item.consigneeName || null, // 收货人名称
                    senderPhone: item.consigneePhone || null // 收货人联系方式
                  })
                }
              } else if (item.configGroupType === DeliveryAddressConfigType.inventory) {
                // 工厂+库存地点
                if (item.consigneeAddress || item.consigneeName || item.consigneePhone) {
                  senderInfoList.push({
                    configId: item.id, // 配置id
                    senderAddress: item.consigneeAddress || null, // 收货人地址
                    senderName: item.consigneeName || null, // 收货人名称
                    senderPhone: item.consigneePhone || null // 收货人联系方式
                  })
                }
              } else if (item.configGroupType === DeliveryAddressConfigType.processor) {
                // 工厂+加工商
                if (item.consigneeAddress || item.consigneeName || item.consigneePhone) {
                  senderInfoList.push({
                    configId: item.id, // 配置id
                    senderAddress: item.consigneeAddress || null, // 收货人地址
                    senderName: item.consigneeName || null, // 收货人名称
                    senderPhone: item.consigneePhone || null // 收货人联系方式
                  })
                }
              }
            })

            // 重置 分厂、分厂库存地点、收货信息 下拉$emit("purchaseJitColumnChange"数据源
            // 库存地点 发出数据源修改事件
            const args = {
              requestKey: 'warehouseCode', // 触发请求的key
              modifiedKeys: [
                'subSiteCode', // 分厂
                'subSiteAddressCode', // 分厂库存地点
                'senderAddress' // 送货地址+联系人+电话
              ],
              changeType: ComponentChangeType.link, // 修改类型
              data: {
                subSiteList, // 分厂
                subSiteAddressList, // 分厂库存地点
                senderInfoList // 送货地址+联系人+电话
              } // 发出的值
            }
            this.$bus.$emit('purchaseJitColumnChange', args)
          },
          // 根据优先级选中送货地址配置默认的数据
          setDeliveryAddressDefaultData(dataList) {
            const branchList = [] // 工厂+库存地点+分厂+分厂库存地点
            const materialList = [] // 工厂+库存地点+物料
            const inventoryList = [] // 工厂+库存地点
            const processorList = [] // 工厂+加工商
            let result = {} // 结果
            dataList.forEach((item) => {
              // configGroupType 配置方式：
              // 优先级:
              // 工厂+库存地点+分厂+分厂库存地点
              // 工厂+库存地点+物料
              // 工厂+库存地点
              // 工厂+加工商
              if (item.configGroupType === DeliveryAddressConfigType.branch) {
                // 工厂+库存地点+分厂+分厂库存地点
                branchList.push(item)
              } else if (item.configGroupType === DeliveryAddressConfigType.material) {
                // 工厂+库存地点+物料
                materialList.push(item)
              } else if (item.configGroupType === DeliveryAddressConfigType.inventory) {
                // 工厂+库存地点
                inventoryList.push(item)
              } else if (item.configGroupType === DeliveryAddressConfigType.processor) {
                // 工厂+加工商
                processorList.push(item)
              }
            })
            // needDefault 是否默认
            if (branchList.length > 0) {
              // 工厂+库存地点+分厂+分厂库存地点 将默认的找出来
              const theDefault = branchList.find((item) => item.needDefault === NeedDefault.yes)
              if (!theDefault) {
                // 没有默认的，取第一个
                result = branchList[0]
              } else {
                result = theDefault
              }
            } else if (materialList.length > 0) {
              //  工厂+库存地点+物料 将默认的找出来
              const theDefault = materialList.find((item) => item.needDefault === NeedDefault.yes)
              if (!theDefault) {
                // 没有默认的，取第一个
                result = materialList[0]
              } else {
                result = theDefault
              }
            } else if (inventoryList.length > 0) {
              // 工厂+库存地点 将默认的找出来
              const theDefault = inventoryList.find((item) => item.needDefault === NeedDefault.yes)
              if (!theDefault) {
                // 没有默认的，取第一个
                result = inventoryList[0]
              } else {
                result = theDefault
              }
            } else if (processorList.length > 0) {
              // 工厂+加工商 将默认的找出来
              const theDefault = processorList.find((item) => item.needDefault === NeedDefault.yes)
              if (!theDefault) {
                // 没有默认的，取第一个
                result = processorList[0]
              } else {
                result = theDefault
              }
            }

            // 库存地点 发出值修改事件
            const emitResult = {
              configId: result.id, // 配置id
              subSiteCode: result.subSiteCode || null, // 分厂code
              subSiteName: result.subSiteName || null, // 分厂name
              subSiteAddressCode: result.subSiteAddress || null, // 分厂库存地点code
              subSiteAddress: result.subSiteAddressName || null, // 分厂库存地点name
              consigneeAddressCode: result.consigneeAddressCode || null, // 收货地址Code 用不到
              senderName: result.consigneeName || null, // 收货人名称
              senderPhone: result.consigneePhone || null, // 收货人联系方式
              senderAddress: result.consigneeAddress || null // 收货人地址
            }

            // 重置 分厂、分厂库存地点、收货信息 选中的数据
            const args = {
              requestKey: 'warehouseCode', // 触发请求的key
              modifiedKeys: [
                'subSiteCode', // 分厂
                'subSiteAddressCode', // 分厂库存地点
                'senderAddress' // 送货地址+联系人+电话
              ],
              changeType: ComponentChangeType.code, // 修改类型
              data: emitResult // 发出的值
            }
            this.$bus.$emit('purchaseJitColumnChange', args)
          },
          // 监听变化
          onComponentChange() {
            // 监听被变化
            this.$bus.$on(`purchaseJitColumnChange`, (e) => {
              const { modifiedKeys, data, changeType } = e
              if (
                modifiedKeys.includes('warehouseCode') &&
                changeType === ComponentChangeType.link
              ) {
                // 重新获取库存地点
                this.getWarehouse({
                  siteCode: data.siteCode
                })
                this.data.warehouseCode = null
                this.data.warehouseName = null
                rowDataTemp[rowDataTemp.length - 1]['warehouseCode'] = null
                rowDataTemp[rowDataTemp.length - 1]['warehouseName'] = null
              }
            })
          },
          doDisableConverter() {
            if (this.data.status === Status.abnormal || this.data.status === Status.normal) {
              // 反馈异常状态下只能修改 叫料数量（需求数量）
              this.disabled = true
            }
          }
        }
      })
    }
  },
  // 库存地点new code-name显示 根据 工厂 选择带出数据源 可模糊搜索
  warehouseCodeSelectNew: () => {
    return {
      template: Vue.component('warehouseCodeSelectNew', {
        template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-select
                v-model="data.senderAddress"
                :data-source="warehouseOptions"
                :fields="{ text: 'theCodeName', value: 'consigneeAddress' }"
                :show-clear-button="true"
                :allow-filtering="true"
                @change="warehouseCodeChange"
                :open-dispatch-change="true"
                filter-type="Contains"
                :placeholder="$t('请选择')"
                :disabled="disabled"
              ></mt-select>
            </div>
          </div>`,
        data: function () {
          return {
            data: {},
            warehouseOptions: [], // 库存地点 下拉选项
            disabled: false,
            isFirstChange: true
          }
        },
        mounted() {
          setTimeout(() => {
            this.isFirstChange = false
          }, 800)
          this.initGetWarehouse()
          this.doDisableConverter()
          // 监听变化
          this.onComponentChange()
        },
        beforeDestroy() {
          this.$bus.$off('purchaseJitColumnChange')
        },
        methods: {
          // 主数据 获取库存地点
          getWarehouse(args) {
            const { updateData, setSelectData } = args
            let currentSiteCode = rowDataTemp[rowDataTemp.length - 1].siteCode
            const params = {
              siteCode: currentSiteCode || undefined // 工厂code
              // dataLimit: 50,
              // fuzzyParam: text
            }
            this.$API.deliverySchedule.queryBySiteAndAddress(params).then((res) => {
              if (res) {
                const list = res?.data || []
                // list.map((item) => {
                //   // 优先使用 externalCode（外部代码）字段（外部代码），如果没有，则使用 locationCode 字段
                //   if (item.externalCode) {
                //     item.locationCode = item.externalCode;
                //     if (item.externalName) {
                //       item.locationName = item.externalName;
                //     }
                //   }

                //   return item;
                // })
                this.warehouseOptions = addCodeNameKeyInList({
                  firstKey: 'consigneeAddressCode',
                  secondKey: 'consigneeAddress',
                  list
                })
                if (updateData) {
                  this.$nextTick(() => {
                    updateData(this.warehouseOptions)
                  })
                }
                if (setSelectData) {
                  this.$nextTick(() => {
                    setSelectData()
                  })
                }
              }
            })
          },
          // 初始化检索 根据 工厂 获取 库存地点
          initGetWarehouse() {
            const warehouseCode = rowDataTemp[rowDataTemp.length - 1].warehouseCode
            const senderAddress = rowDataTemp[rowDataTemp.length - 1].senderAddress
            this.getWarehouse({
              text: senderAddress,
              setSelectData: () => {
                // api获取数据后重新赋值，防止没有赋上值得情况
                this.data.warehouseCode = warehouseCode
                this.data.senderAddress = senderAddress
              }
            })
          },
          // 库存地点 change
          warehouseCodeChange(args) {
            const { itemData } = args
            if (!this.isFirstChange) {
              if (itemData) {
                // rowDataTemp[rowDataTemp.length - 1].warehouseCode = itemData.consigneeAddressCode
                // rowDataTemp[rowDataTemp.length - 1].warehouseName = itemData.consigneeAddress
                rowDataTemp[rowDataTemp.length - 1].senderAddressCode =
                  itemData.consigneeAddressCode
                rowDataTemp[rowDataTemp.length - 1].senderAddress = itemData.consigneeAddress
              } else {
                // rowDataTemp[rowDataTemp.length - 1].warehouseCode = null
                // rowDataTemp[rowDataTemp.length - 1].warehouseName = null
                rowDataTemp[rowDataTemp.length - 1].senderAddressCode = null
                rowDataTemp[rowDataTemp.length - 1].senderAddress = null
              }

              console.log(rowDataTemp)

              console.log(rowDataTemp[rowDataTemp.length - 1].deliveryMethod)
              console.log(DeliveryMethod.indirect)

              // 库存地点 发出值修改事件
              const emitResult = {
                senderName: itemData.consigneeName || null, // 收货人名称
                senderPhone: itemData.consigneePhone || null, // 收货人联系方式
                // senderAddress: itemData.consigneeAddress || null // 收货人地址
                warehouseCode: itemData.siteAddress || null // 收货人地址
              }

              // 重置 分厂、分厂库存地点、收货信息 选中的数据
              const arg = {
                requestKey: 'warehouseCode', // 触发请求的key
                modifiedKeys: [
                  'senderName', // 联系人
                  'senderPhone', // 电话
                  // 'senderAddress' // 送货地址
                  'warehouseCode' // 送货地址
                ],
                changeType: ComponentChangeType.code, // 修改类型
                data: emitResult // 发出的值
              }
              this.$bus.$emit('purchaseJitColumnChange', arg)

              // if (
              // rowDataTemp[rowDataTemp.length - 1].deliveryMethod ===
              // DeliveryMethod.indirect
              // ) {
              // 配送方式为 "非直送" 时，通过工厂+库存地点 查询 送货地址配置下拉
              // const siteCode = rowDataTemp[rowDataTemp.length - 1].siteCode
              // const warehouseCode = rowDataTemp[rowDataTemp.length - 1].warehouseCode
            }
          },
          // 监听变化
          onComponentChange() {
            // 监听被变化
            this.$bus.$on(`purchaseJitColumnChange`, (e) => {
              const { modifiedKeys, data, changeType } = e
              if (
                modifiedKeys.includes('warehouseCode') &&
                changeType === ComponentChangeType.link
              ) {
                // 重新获取库存地点
                this.getWarehouse({
                  siteCode: data.siteCode
                })
                this.data.senderAddressCode = null
                this.data.senderAddress = null
                rowDataTemp[rowDataTemp.length - 1]['senderAddressCode'] = null
                rowDataTemp[rowDataTemp.length - 1]['senderAddress'] = null
                // this.data.warehouseCode = null
                // this.data.warehouseName = null
                // rowDataTemp[rowDataTemp.length - 1]['warehouseCode'] = null
                // rowDataTemp[rowDataTemp.length - 1]['warehouseName'] = null
              }
            })
          },
          doDisableConverter() {
            // if (this.data.status === Status.abnormal || this.data.status === Status.normal) {
            if (
              this.data.status !== 0 &&
              this.data.status !== 1 &&
              this.data.status !== 2 &&
              this.data.status !== 3 &&
              this.data.status !== 4
            ) {
              // 反馈异常状态下只能修改 叫料数量（需求数量）
              this.disabled = true
            }
          }
        }
      })
    }
  },
  // 计划组 code-name显示 根据物料选择带出数据源 不可模糊搜索（API不支持）
  planGroupCodeSelect: () => {
    return {
      template: Vue.component('planGroupCodeSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.planGroupCode"
              :data-source="planGroupOptions"
              :fields="{ text: 'theCodeName', value: 'groupCode' }"
              :show-clear-button="true"
              :allow-filtering="false"
              @change="planGroupCodeChange"
              :open-dispatch-change="false"
              :placeholder="$t('请选择')"
              :disabled="disabled"
            ></mt-select>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            planGroupOptions: [], // 计划组 下拉选项
            disabled: false
          }
        },
        mounted() {
          this.initGetPlanGroup()
          // 监听变化
          this.onComponentChange()
          this.doDisableConverter()
        },
        beforeDestroy() {
          this.$bus.$off('purchaseJitColumnChange')
        },
        methods: {
          // 主数据 计划组 根据物料、业务组类型获取业务组
          getPlanGroup(args) {
            const { itemCode, updateData, setSelectData } = args
            const params = {
              itemCode, // 物料code
              businessGroupTypeCode: 'BG001JH'
            }
            this.$API.masterData.getBusinessGroup(params).then((res) => {
              if (res) {
                const list = res?.data || []
                this.planGroupOptions = addCodeNameKeyInList({
                  firstKey: 'groupCode',
                  secondKey: 'groupName',
                  list
                })
                if (updateData) {
                  this.$nextTick(() => {
                    updateData(this.planGroupOptions)
                  })
                }
                if (setSelectData) {
                  this.$nextTick(() => {
                    setSelectData()
                  })
                }
              }
            })
          },
          // 初始化检索 计划组
          initGetPlanGroup() {
            const itemCode = this.data.itemCode
            if (itemCode) {
              this.getPlanGroup({
                itemCode
              })
            }
          },
          // 计划组 change
          planGroupCodeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].planGroupId = itemData.id // id
              rowDataTemp[rowDataTemp.length - 1].planGroupCode = itemData.groupCode // code
              rowDataTemp[rowDataTemp.length - 1].planGroupName = itemData.groupName // name
            } else {
              rowDataTemp[rowDataTemp.length - 1].planGroupId = null // id
              rowDataTemp[rowDataTemp.length - 1].planGroupCode = null // code
              rowDataTemp[rowDataTemp.length - 1].planGroupName = null // name
            }
          },
          // 监听变化
          onComponentChange() {
            // 监听被变化
            this.$bus.$on(`purchaseJitColumnChange`, (e) => {
              const { modifiedKeys, data, changeType } = e
              if (
                modifiedKeys.includes('planGroupCode') &&
                changeType === ComponentChangeType.link
              ) {
                // 物料修改了，重新获取计划组
                this.getPlanGroup({
                  itemCode: data.itemCode
                })
                this.data.planGroupCode = null
                this.data.planGroupId = null
                this.data.planGroupName = null
                rowDataTemp[rowDataTemp.length - 1]['planGroupCode'] = null
                rowDataTemp[rowDataTemp.length - 1]['planGroupId'] = null
                rowDataTemp[rowDataTemp.length - 1]['planGroupName'] = null
              }
            })
          },
          doDisableConverter() {
            if (this.data.status === Status.abnormal || this.data.status === Status.normal) {
              // 反馈异常状态下只能修改 叫料数量（需求数量）
              this.disabled = true
            }
          }
        }
      })
    }
  },
  // 分厂 code-name 下拉框选择 数据来源 交货地址配置表 不可模糊搜索
  subSiteSelect: () => {
    return {
      template: Vue.component('subSiteSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.subSiteCode"
              :data-source="subSiteOptions"
              :fields="{ text: 'theCodeName', value: 'subSiteCode' }"
              :show-clear-button="true"
              :allow-filtering="false"
              @change="subSiteCodeChange"
              :placeholder="$t('请选择')"
              :open-dispatch-change="false"
              :disabled="disabled"
            ></mt-select>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            subSiteOptions: [], // 分厂 下拉选项
            disabled: false
          }
        },
        mounted() {
          // 监听变化
          this.onComponentChange()
          this.doDisableConverter()
        },
        beforeDestroy() {
          this.$bus.$off('purchaseJitColumnChange')
        },
        methods: {
          // 分厂编码 change
          subSiteCodeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].subSiteCode = itemData.subSiteCode // 分厂编码
              rowDataTemp[rowDataTemp.length - 1].subSiteName = itemData.subSiteName // 分厂名称
            } else {
              rowDataTemp[rowDataTemp.length - 1].subSiteCode = null // 分厂编码
              rowDataTemp[rowDataTemp.length - 1].subSiteName = null // 分厂名称
            }
          },
          // 监听变化
          onComponentChange() {
            // 监听被变化
            this.$bus.$on(`purchaseJitColumnChange`, (e) => {
              const { modifiedKeys, data, changeType } = e
              console.log(e)
              console.log(data)
              console.log(this.data)
              // data.subSiteList = uniqWith(
              //   filter(data?.subSiteList, (item) => item.subSiteCode), // 过滤
              //   isEqual
              // );
              data.subSiteList = uniqBy(data.subSiteList, 'subSiteCode')

              console.log(data.subSiteList)
              if (modifiedKeys.includes('subSiteCode') && changeType === ComponentChangeType.link) {
                // 修改下拉数据源
                this.subSiteOptions = addCodeNameKeyInList({
                  firstKey: 'subSiteCode',
                  secondKey: 'subSiteName',
                  list: data.subSiteList
                })
                this.data.subSiteCode = null
                this.data.subSiteName = null
                rowDataTemp[rowDataTemp.length - 1]['subSiteCode'] = null
                rowDataTemp[rowDataTemp.length - 1]['subSiteName'] = null
              } else if (
                modifiedKeys.includes('subSiteCode') &&
                changeType === ComponentChangeType.code
              ) {
                // 通过 配置行id 修改选中的数据
                const beSetData = this.subSiteOptions.find(
                  (item) => item.configId === data.configId
                )
                if (beSetData) {
                  this.data.subSiteCode = beSetData.subSiteCode // 分厂编码
                  this.data.subSiteName = beSetData.subSiteName // 分厂名称
                  rowDataTemp[rowDataTemp.length - 1].subSiteCode = beSetData.subSiteCode // 分厂编码
                  rowDataTemp[rowDataTemp.length - 1].subSiteName = beSetData.subSiteName // 分厂名称
                } else {
                  this.data.subSiteCode = null
                  this.data.subSiteName = null
                  rowDataTemp[rowDataTemp.length - 1]['subSiteCode'] = null
                  rowDataTemp[rowDataTemp.length - 1]['subSiteName'] = null
                }
              }
            })
          },
          doDisableConverter() {
            if (this.data.status === Status.abnormal || this.data.status === Status.normal) {
              // 反馈异常状态下只能修改 叫料数量（需求数量）
              this.disabled = true
            }
          }
        }
      })
    }
  },
  // 分厂库存地点 code-name 下拉框选择 数据来源 交货地址配置表 不可模糊搜索
  subSiteAddressSelect: () => {
    return {
      template: Vue.component('subSiteAddressSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.subSiteAddressCode"
              :data-source="subSiteAddressOptions"
              :fields="{ text: 'theCodeName', value: 'subSiteAddressCode' }"
              :show-clear-button="true"
              :allow-filtering="false"
              @change="subSiteAddressCodeChange"
              :placeholder="$t('请选择')"
              :open-dispatch-change="false"
              :disabled="disabled"
            ></mt-select>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            subSiteAddressOptions: [], // 分厂库存地点 下拉选项
            disabled: false
          }
        },
        mounted() {
          // 监听变化
          this.onComponentChange()
          this.doDisableConverter()
        },
        beforeDestroy() {
          this.$bus.$off('purchaseJitColumnChange')
        },
        methods: {
          // 分厂库存地点编码 change
          subSiteAddressCodeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].subSiteAddressCode = itemData.subSiteAddressCode // 分厂库存地点编码
              rowDataTemp[rowDataTemp.length - 1].subSiteAddress = itemData.subSiteAddress // 分厂库存地点名称
            } else {
              rowDataTemp[rowDataTemp.length - 1].subSiteAddressCode = null // 分厂库存地点编码
              rowDataTemp[rowDataTemp.length - 1].subSiteAddress = null // 分厂库存地点名称
            }
          },
          // 监听变化
          onComponentChange() {
            // 监听被变化
            this.$bus.$on(`purchaseJitColumnChange`, (e) => {
              const { modifiedKeys, data, changeType } = e
              if (
                modifiedKeys.includes('subSiteAddressCode') &&
                changeType === ComponentChangeType.link
              ) {
                // 修改下拉数据源
                this.subSiteAddressOptions = addCodeNameKeyInList({
                  firstKey: 'subSiteAddressCode',
                  secondKey: 'subSiteAddress',
                  list: data.subSiteAddressList
                })
                this.data.subSiteAddressCode = null
                this.data.subSiteAddress = null
                rowDataTemp[rowDataTemp.length - 1]['subSiteAddressCode'] = null
                rowDataTemp[rowDataTemp.length - 1]['subSiteAddress'] = null
              } else if (
                modifiedKeys.includes('subSiteAddressCode') &&
                changeType === ComponentChangeType.code
              ) {
                // 通过 配置行id 修改选中的数据
                const beSetData = this.subSiteAddressOptions.find(
                  (item) => item.configId === data.configId
                )
                if (beSetData) {
                  this.data.subSiteAddressCode = beSetData.subSiteAddressCode // 分厂库存地点编码
                  this.data.subSiteAddress = beSetData.subSiteAddress // 分厂库存地点名称
                  rowDataTemp[rowDataTemp.length - 1].subSiteAddressCode =
                    beSetData.subSiteAddressCode // 分厂库存地点编码
                  rowDataTemp[rowDataTemp.length - 1].subSiteAddress = beSetData.subSiteAddress // 分厂库存地点名称
                } else {
                  this.data.subSiteAddressCode = null
                  this.data.subSiteAddress = null
                  rowDataTemp[rowDataTemp.length - 1]['subSiteAddressCode'] = null
                  rowDataTemp[rowDataTemp.length - 1]['subSiteAddress'] = null
                }
              }
            })
          },
          doDisableConverter() {
            if (this.data.status === Status.abnormal || this.data.status === Status.normal) {
              // 反馈异常状态下只能修改 叫料数量（需求数量）
              this.disabled = true
            }
          }
        }
      })
    }
  },
  // 收货信息 下拉框选择 数据来源 交货地址配置表 不可模糊搜索
  senderAddressSelect: () => {
    return {
      template: Vue.component('senderAddressSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.senderAddress"
              :data-source="senderAddressOptions"
              :fields="{ text: 'theCodeName', value: 'senderAddress' }"
              :show-clear-button="true"
              :allow-filtering="false"
              @change="senderAddressChange"
              :placeholder="$t('请选择')"
              :open-dispatch-change="false"
              :disabled="disabled"
            ></mt-select>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            senderAddressOptions: [], // 收货信息 下拉选项
            disabled: false
          }
        },
        mounted() {
          // 监听变化
          this.onComponentChange()
          this.doDisableConverter()
        },
        beforeDestroy() {
          this.$bus.$off('purchaseJitColumnChange')
        },
        methods: {
          // 送货地址 change
          senderAddressChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].senderAddress = itemData.senderAddress // 送货地址
              rowDataTemp[rowDataTemp.length - 1].senderName = itemData.senderName // 送货联系人
              rowDataTemp[rowDataTemp.length - 1].senderPhone = itemData.senderPhone // 送货联系电话
            } else {
              rowDataTemp[rowDataTemp.length - 1].senderAddress = null // 送货地址
              rowDataTemp[rowDataTemp.length - 1].senderName = null // 送货联系人
              rowDataTemp[rowDataTemp.length - 1].senderPhone = null // 送货联系电话
            }
          },
          // 监听变化
          onComponentChange() {
            // 监听被变化
            this.$bus.$on(`purchaseJitColumnChange`, (e) => {
              const { modifiedKeys, data, changeType } = e
              if (
                modifiedKeys.includes('senderAddress') &&
                changeType === ComponentChangeType.link
              ) {
                // 修改下拉数据源
                this.senderAddressOptions = addCodeNameKeyInList({
                  firstKey: 'senderName',
                  secondKey: 'senderPhone',
                  thirdKey: 'senderAddress',
                  list: data.senderInfoList
                })
                this.data.senderName = null
                this.data.senderPhone = null
                this.data.senderAddress = null
                rowDataTemp[rowDataTemp.length - 1]['senderName'] = null
                rowDataTemp[rowDataTemp.length - 1]['senderPhone'] = null
                rowDataTemp[rowDataTemp.length - 1]['senderAddress'] = null
              } else if (
                modifiedKeys.includes('senderAddress') &&
                changeType === ComponentChangeType.code
              ) {
                // 通过 配置行id 修改选中的数据
                const beSetData = this.senderAddressOptions.find(
                  (item) => item.configId === data.configId
                )
                if (beSetData) {
                  this.data.senderName = beSetData.senderName // 送货联系人
                  this.data.senderPhone = beSetData.senderPhone // 送货联系电话
                  this.data.senderAddress = beSetData.senderAddress // 送货地址
                  rowDataTemp[rowDataTemp.length - 1].senderName = beSetData.senderName // 送货联系人
                  rowDataTemp[rowDataTemp.length - 1].senderPhone = beSetData.senderPhone // 送货联系电话
                  rowDataTemp[rowDataTemp.length - 1].senderAddress = beSetData.senderAddress // 送货地址
                } else {
                  this.data.senderName = null
                  this.data.senderPhone = null
                  this.data.senderAddress = null
                  rowDataTemp[rowDataTemp.length - 1]['senderName'] = null
                  rowDataTemp[rowDataTemp.length - 1]['senderPhone'] = null
                  rowDataTemp[rowDataTemp.length - 1]['senderAddress'] = null
                }
              }
            })
          },
          doDisableConverter() {
            if (this.data.status === Status.abnormal || this.data.status === Status.normal) {
              // 反馈异常状态下只能修改 叫料数量（需求数量）
              this.disabled = true
            }
          }
        }
      })
    }
  },
  // 工作中心 code-name 显示 主数据 非必填 模糊搜索编号/名称
  workCenterCodeSelect: () => {
    return {
      template: Vue.component('workCenterCodeSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.workCenterCode"
              :filtering="doGetDataSource"
              :data-source="workCenterOptions"
              :fields="{ text: 'theCodeName', value: 'workCenterCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              :popup-width="250"
              @change="workCenterCodeChange"
              :placeholder="$t('请选择')"
              :open-dispatch-change="false"
              :disabled="disabled"
            ></mt-select>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            workCenterOptions: [], // 工作中心 下拉选项
            doGetDataSource: () => {},
            disabled: false
          }
        },
        mounted() {
          this.initGetWorkCenter()
          this.doGetDataSource = utils.debounce(this.getWorkCenter, 1000)
          // 监听变化
          this.onComponentChange()
          this.doDisableConverter()
        },
        beforeDestroy() {
          this.$bus.$off('purchaseJitColumnChange')
        },
        methods: {
          // 主数据 工作中心
          getWorkCenter(args) {
            const { text, updateData, setSelectData } = args
            const params = {
              // FIXME siteCode 工厂关联？
              keyword: text
            }
            this.$API.masterData.getWorkCenter(params).then((res) => {
              if (res) {
                const list = res?.data || []
                this.workCenterOptions = addCodeNameKeyInList({
                  firstKey: 'workCenterCode',
                  secondKey: 'workCenterName',
                  list
                })
                if (updateData) {
                  this.$nextTick(() => {
                    updateData(this.workCenterOptions)
                  })
                }
                if (setSelectData) {
                  this.$nextTick(() => {
                    setSelectData()
                  })
                }
              }
            })
          },
          // 初始化检索 工作中心
          initGetWorkCenter() {
            const workCenterCode = this.data.workCenterCode
            this.getWorkCenter({
              text: workCenterCode,
              setSelectData: () => {
                // api获取数据后重新赋值，防止没有赋上值得情况
                this.data.workCenterCode = workCenterCode
              }
            })
          },
          // 工作中心 change
          workCenterCodeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].workCenterId = itemData.id // id
              rowDataTemp[rowDataTemp.length - 1].workCenterCode = itemData.workCenterCode // code
              rowDataTemp[rowDataTemp.length - 1].workCenter = itemData.workCenterName // name
              // 根据 工作中心 获取 工作中心-加工商配置表 自动选中加工商
              const args = {
                requestKey: 'workCenterCode', // 触发请求的key
                modifiedKeys: [
                  'processorCode' // 加工商编码
                ],
                changeType: ComponentChangeType.code, // 修改类型
                data: {
                  workCenterCode: itemData.workCenterCode
                } // 发出的值
              }
              this.$bus.$emit('purchaseJitColumnChange', args)
            } else {
              rowDataTemp[rowDataTemp.length - 1].workCenterId = null // id
              rowDataTemp[rowDataTemp.length - 1].workCenterCode = null // code
              rowDataTemp[rowDataTemp.length - 1].workCenter = null // name
            }
          },
          // 监听变化
          onComponentChange() {
            // 监听被变化
            this.$bus.$on(`purchaseJitColumnChange`, () => {
              // FIXME siteCode 工厂关联？
              // const { modifiedKeys, data, changeType } = e;
            })
          },
          doDisableConverter() {
            if (this.data.status !== 0 && this.data.status !== 1) {
              // 反馈异常状态下只能修改 叫料数量（需求数量）
              this.disabled = true
            }
            // if (this.data.status === Status.abnormal || this.data.status === Status.normal) {
            //   // 反馈异常状态下只能修改 叫料数量（需求数量）
            //   this.disabled = true
            // }
          }
        }
      })
    }
  },
  // 加工商 code-name 显示 主数据 非必填 模糊搜索编号/名称
  // 选择 工作中心 后，根据 工作中心-加工商配置表 自动选中加工商
  processorCodeSelect: () => {
    return {
      template: Vue.component('processorCodeSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.processorCode"
              :filtering="doGetDataSource"
              :data-source="processorOptions"
              :fields="{ text: 'theCodeName', value: 'supplierCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              @change="processorCodeChange"
              :placeholder="$t('请选择')"
              :open-dispatch-change="false"
              :disabled="disabled"
            ></mt-select>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            processorOptions: [], // 加工商 下拉选项
            doGetDataSource: () => {},
            disabled: false
          }
        },
        mounted() {
          this.initGetProcessor()
          this.doGetDataSource = utils.debounce(this.getSupplier, 1000)
          // 监听变化
          this.onComponentChange()
          this.doDisableConverter()
        },
        beforeDestroy() {
          this.$bus.$off('purchaseJitColumnChange')
        },
        methods: {
          // 主数据 加工商 = 供应商
          getSupplier(args) {
            const { text, updateData, setSelectData } = args
            const params = {
              fuzzyNameOrCode: text
            }
            this.$API.masterData.getSupplier(params).then((res) => {
              if (res) {
                const list = res?.data || []
                this.processorOptions = addCodeNameKeyInList({
                  firstKey: 'supplierCode',
                  secondKey: 'supplierName',
                  list
                })
                if (updateData) {
                  this.$nextTick(() => {
                    updateData(this.processorOptions)
                  })
                }
                if (setSelectData) {
                  this.$nextTick(() => {
                    setSelectData()
                  })
                }
              }
            })
          },
          // 初始化检索 加工商 = 供应商
          initGetProcessor() {
            const processorCode = this.data.processorCode
            this.getSupplier({
              text: processorCode,
              setSelectData: () => {
                // api获取数据后重新赋值，防止没有赋上值得情况
                this.data.processorCode = processorCode
              }
            })
          },
          // 加工商 change
          processorCodeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].processorCode = itemData.supplierCode // code
              rowDataTemp[rowDataTemp.length - 1].processorName = itemData.supplierName // name
            } else {
              rowDataTemp[rowDataTemp.length - 1].processorCode = null // code
              rowDataTemp[rowDataTemp.length - 1].processorName = null // name
            }
          },
          // 监听变化
          onComponentChange() {
            // 监听被变化
            this.$bus.$on(`purchaseJitColumnChange`, (e) => {
              const { requestKey, modifiedKeys, data, changeType } = e
              if (
                requestKey === 'workCenterCode' &&
                modifiedKeys.includes('processorCode') &&
                changeType === ComponentChangeType.code
              ) {
                // 选择 工作中心 后，根据 工作中心-加工商配置表 自动选中加工商
                const workCenterCode = data.workCenterCode
                this.getProcessorByWorkCenter({ workCenterCode })
              }
            })
          },
          // 根据 工作中心获取 工作中心-加工商配置表 自动选中加工商
          getProcessorByWorkCenter(args) {
            const { workCenterCode } = args
            const params = {
              page: {
                current: 1,
                size: 10
              },
              condition: 'and',
              rules: [
                {
                  label: '工作中心',
                  field: 'workCenter',
                  operator: 'contains',
                  value: workCenterCode
                }
              ]
            }
            this.$API.deliverySchedule.workCenterSupplierRelquery(params).then((res) => {
              const configData = res?.data?.records || []
              if (configData[0]) {
                this.getSupplier({
                  text: configData[0].supplierCode,
                  setSelectData: () => {
                    // api获取数据后重新赋值，防止没有赋上值得情况
                    this.data.processorCode = configData[0].supplierCode // 加工商编码
                    this.data.processorName = configData[0].supplierName // 加工商名称
                    rowDataTemp[rowDataTemp.length - 1].processorCode = configData[0].supplierCode // 加工商编码
                    rowDataTemp[rowDataTemp.length - 1].processorName = configData[0].supplierName // 加工商名称
                  }
                })
              }
            })
          },
          doDisableConverter() {
            if (this.data.status === Status.abnormal || this.data.status === Status.normal) {
              // 反馈异常状态下只能修改 叫料数量（需求数量）
              this.disabled = true
            }
          }
        }
      })
    }
  },
  // 公司 code-name 不可编辑 工厂带出来
  companyCodeInputText: () => {
    return {
      template: Vue.component('companyCodeInputText', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-input
              v-model="componentData"
              autocomplete="off"
              :show-clear-button="false"
              :disabled="true"
            ></mt-input>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            componentData: null // 组件内部绑定的变量
          }
        },
        mounted() {
          // 监听变化
          this.onComponentChange()
          this.componentData = this.format(this.data)
        },
        beforeDestroy() {
          this.$bus.$off('purchaseJitColumnChange')
        },
        methods: {
          // 通过工厂获取公司
          getCompany(args) {
            const { siteCode } = args
            const params = {
              fuzzyParam: siteCode
            }
            this.$API.masterData.postSiteFuzzyQuery(params).then((res) => {
              const list = res?.data || []
              const company = {
                companyId: list[0]?.parentId, // 公司ID
                companyCode: list[0]?.parentCode, // 公司编码
                companyName: list[0]?.parentName // 公司名称
              }
              rowDataTemp[rowDataTemp.length - 1].companyId = company.companyId // id
              rowDataTemp[rowDataTemp.length - 1].companyCode = company.companyCode // code
              rowDataTemp[rowDataTemp.length - 1].companyName = company.companyName // name
              this.componentData = this.format(company)
            })
          },
          // 监听变化
          onComponentChange() {
            // 监听被变化
            this.$bus.$on(`purchaseJitColumnChange`, (e) => {
              const { requestKey, modifiedKeys, data, changeType } = e
              if (
                requestKey === 'siteCode' &&
                modifiedKeys.includes('companyCode') &&
                changeType === ComponentChangeType.code
              ) {
                // 工厂带出来公司
                const siteCode = data.siteCode
                this.getCompany({ siteCode })
              }
            })
          },
          format(data) {
            let theColumnData = ''
            if (data['companyCode'] && data['companyName']) {
              // firstKey-secondKey
              theColumnData = `${data['companyCode']}-${data['companyName']}`
            } else if (data['companyCode']) {
              // firstKey
              theColumnData = data['companyCode']
            } else if (data['companyName']) {
              // secondKey
              theColumnData = data['companyName']
            }
            return theColumnData
          }
        }
      })
    }
  },
  // 供应商 code-name 下拉框选择 供应商主数据 模糊搜索
  supplierCodeSelect: () => {
    return {
      template: Vue.component('supplierCodeSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.supplierCode"
              :filtering="doGetDataSource"
              :data-source="supplierOptions"
              :fields="{ text: 'theCodeName', value: 'supplierCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              @change="supplierCodeChange"
              :open-dispatch-change="false"
              :placeholder="$t('请选择')"
              :disabled="disabled"
              :popup-width="250"
            ></mt-select>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            supplierOptions: [], // 供应商 下拉选项
            doGetDataSource: () => {},
            disabled: false
          }
        },
        mounted() {
          this.initGetSupplier()
          this.doGetDataSource = utils.debounce(this.getSupplier, 1000)
          // 监听变化
          // this.onComponentChange();
          this.doDisableConverter()
        },
        beforeDestroy() {
          this.$bus.$off('purchaseJitColumnChange')
        },
        methods: {
          // 主数据 供应商
          getSupplier(args) {
            const { text, updateData, setSelectData } = args
            const params = {
              fuzzyNameOrCode: text
            }
            this.$API.masterData.getSupplier(params).then((res) => {
              if (res) {
                const list = res?.data || []
                this.supplierOptions = addCodeNameKeyInList({
                  firstKey: 'supplierCode',
                  secondKey: 'supplierName',
                  list
                })
                if (updateData) {
                  this.$nextTick(() => {
                    updateData(this.supplierOptions)
                  })
                }
                if (setSelectData) {
                  this.$nextTick(() => {
                    setSelectData()
                  })
                }
              }
            })
          },
          // 初始化检索 供应商
          initGetSupplier() {
            const supplierCode = this.data.supplierCode
            this.getSupplier({
              text: supplierCode,
              setSelectData: () => {
                // api获取数据后重新赋值，防止没有赋上值得情况
                this.data.supplierCode = supplierCode
              }
            })
          },
          // 供应商 change
          supplierCodeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].supplierId = itemData.supplierId // id
              rowDataTemp[rowDataTemp.length - 1].supplierCode = itemData.supplierCode // code
              rowDataTemp[rowDataTemp.length - 1].supplierName = itemData.supplierName // name
            } else {
              rowDataTemp[rowDataTemp.length - 1].supplierId = null // id
              rowDataTemp[rowDataTemp.length - 1].supplierCode = null // code
              rowDataTemp[rowDataTemp.length - 1].supplierName = null // name
            }
          },
          // 监听变化
          onComponentChange() {
            // 监听被变化
            this.$bus.$on(`purchaseJitColumnChange`, () => {
              // const {
              //   requestKey,
              //   modifiedKeys,
              //   data,
              //   changeType
              // } = e;
            })
          },
          doDisableConverter() {
            // if (this.data.status === Status.abnormal || this.data.status === Status.normal) {
            //   // 反馈异常状态下只能修改 叫料数量（需求数量）
            //   this.disabled = true
            // }
            if (this.data.status !== 0 && this.data.status !== 1 && this.data.status !== 4) {
              // 反馈异常状态下只能修改 叫料数量（需求数量）
              this.disabled = true
            }
          }
        }
      })
    }
  },
  // 转交计划员 code-name 新增时选择人员（组织架构）
  transferPlanCodeSelect: () => {
    return {
      template: Vue.component('transferPlanCodeSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.transferPlanCode"
              :filtering="doGetDataSource"
              :data-source="transferPlanOptions"
              :fields="{ text: 'theCodeName', value: 'employeeName' }"
              :show-clear-button="true"
              :allow-filtering="true"
              @change="transferPlanCodeChange"
              :open-dispatch-change="false"
              :placeholder="$t('请选择')"
              :disabled="disabled"
            ></mt-select>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            transferPlanOptions: [], // 转交计划员 下拉选项
            doGetDataSource: () => {},
            disabled: false
          }
        },
        mounted() {
          this.initGetTransferPlan()
          this.doGetDataSource = utils.debounce(this.getTransferPlan, 1000)
          this.doDisableConverter()
        },
        methods: {
          // 主数据 获取 转交计划员
          getTransferPlan(args) {
            const { text, updateData, setSelectData } = args
            const params = {
              fuzzyName: text
            }
            this.$API.masterData.getCurrentTenantEmployees(params).then((res) => {
              if (res) {
                const list = res?.data || []
                this.transferPlanOptions = addCodeNameKeyInList({
                  firstKey: 'companyOrgName',
                  secondKey: 'departmentOrgName',
                  thirdKey: 'employeeCode',
                  fourthKey: 'employeeName',
                  list
                })
                if (updateData) {
                  this.$nextTick(() => {
                    updateData(this.transferPlanOptions)
                  })
                }
                if (setSelectData) {
                  this.$nextTick(() => {
                    setSelectData()
                  })
                }
              }
            })
          },
          // 初始化检索 转交计划员
          initGetTransferPlan() {
            const selectData = this.data.transferPlanCode
            this.getTransferPlan({
              text: selectData,
              setSelectData: () => {
                // api获取数据后重新赋值，防止没有赋上值得情况
                this.data.transferPlanCode = selectData
              }
            })
          },
          // 转交计划员 change
          transferPlanCodeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].transferPlanId = itemData.employeeId
              rowDataTemp[rowDataTemp.length - 1].transferPlanName = itemData.employeeName
              rowDataTemp[rowDataTemp.length - 1].transferPlanCode = itemData.employeeCode
            } else {
              rowDataTemp[rowDataTemp.length - 1].transferPlanId = null
              rowDataTemp[rowDataTemp.length - 1].transferPlanName = null
              rowDataTemp[rowDataTemp.length - 1].transferPlanCode = null
            }
          },
          doDisableConverter() {
            if (this.data.status === Status.abnormal || this.data.status === Status.normal) {
              // 反馈异常状态下只能修改 叫料数量（需求数量）
              this.disabled = true
            }
          }
        }
      })
    }
  }
}
