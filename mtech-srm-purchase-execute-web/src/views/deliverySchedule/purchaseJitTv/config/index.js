import { ColumnComponent as Component } from './columnComponent'
import {
  ComponentType,
  CellTools,
  StatusOptions,
  DeliveryStatusOptions,
  SatisfyOptions,
  ReceiveStatusOptions,
  DeliveryMethodOptions,
  OutsourcedTypeOptions,
  Status,
  ReceiptShipmentStatusOptions
} from './constant'
import { codeNameColumn } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'
import { i18n } from '@/main.js'

// 格式化表格动态数据
export const formatTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150'
    }
    if (col.fieldCode === 'id') {
      // 行主键
      defaultCol.visible = false
      defaultCol.isIdentity = true // 加上这个，新增一行的时候，主键不会变成可输入
      defaultCol.isPrimaryKey = true
      defaultCol.allowEditing = false
    } else if (col.fieldCode === 'checkBox') {
      // checkBox
      defaultCol.type = 'checkbox'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.showInColumnChooser = false
      defaultCol.width = '50'
      // 编辑时显示
      // defaultCol.editTemplate = Component.empty;
    } else if (col.fieldCode === 'serialNumber') {
      // 序号
      defaultCol.width = '66'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.ignore = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'batchCode') {
      // 批次号 不可编辑
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 250,
        isListenChange: false,
        hasSearch: false,
        disableConverter: (data) => {
          // return data.status !== Status.new
          return data.id
        }
      })
    } else if (col.fieldCode === 'jitItemNo') {
      // JIT 行编号 不可编辑
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'status') {
      defaultCol.valueConverter = {
        type: 'map',
        map: StatusOptions
      }
      // 状态 1-新建 2-已转交 3-待反馈 4-反馈异常 5-反馈正常 6-已关闭
      defaultCol.allowEditing = false

      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        cellTools: CellTools,
        valueConverter: {
          type: 'map',
          map: StatusOptions
        }
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.edit,
        dataKey: col.fieldCode,
        cellTools: CellTools,
        valueConverter: {
          type: 'map',
          map: StatusOptions
        }
      })
    } else if (col.fieldCode === 'receiptShipmentStatus') {
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        valueConverter: {
          type: 'map',
          map: ReceiptShipmentStatusOptions
        }
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.edit,
        dataKey: col.fieldCode,
        valueConverter: {
          type: 'map',
          map: ReceiptShipmentStatusOptions
        }
      })
    } else if (col.fieldCode === 'deliveryStatus') {
      // 发货状态 未发货-1 全部发货-2 部分发货-3
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: DeliveryStatusOptions
        }
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.edit,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: DeliveryStatusOptions
        }
      })
    } else if (col.fieldCode === 'isSatisfy') {
      // 发货状态 未发货-1 全部发货-2 部分发货-3
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: SatisfyOptions
        }
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.edit,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: SatisfyOptions
        }
      })
    } else if (col.fieldCode === 'receiveStatus') {
      // 收货状态 未收货-1 全部收货-2 部分收货-3
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: ReceiveStatusOptions
        }
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.edit,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: ReceiveStatusOptions
        }
      })
    } else if (col.fieldCode === 'buyerOrgCode') {
      // 采购组名称 code+name
      defaultCol.width = '300'
      defaultCol.editTemplate = Component.buyerOrgSelect
      defaultCol.template = codeNameColumn({
        firstKey: 'buyerOrgCode',
        secondKey: 'buyerOrgName'
      })
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
    } else if (col.fieldCode === 'deliveryMethod') {
      // 配送方式 0直送 1非直送 必填
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: DeliveryMethodOptions
        }
      })
      defaultCol.editTemplate = Component.select({
        dataKey: col.fieldCode,
        selectOptions: DeliveryMethodOptions,
        fields: { value: 'value', text: 'text' },
        allowFiltering: false,
        showClearBtn: true,
        modifiedKeys: [],
        modifiedRelation: null,
        disableConverter: (data) => {
          return data.status === Status.abnormal
        }
      })
    } else if (col.fieldCode === 'outsourcedType') {
      // 委外方式 0标准委外 1销售委外 2非委外 必填
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: OutsourcedTypeOptions
        }
      })
      defaultCol.editTemplate = Component.select({
        dataKey: col.fieldCode,
        selectOptions: OutsourcedTypeOptions,
        fields: { value: 'value', text: 'text' },
        allowFiltering: false,
        showClearBtn: true,
        modifiedKeys: [],
        modifiedRelation: null,
        disableConverter: (data) => {
          return data.status === Status.abnormal
        }
      })
    } else if (col.fieldCode === 'productLineCode') {
      defaultCol.allowEditing = true
      if (data.status !== Status.new) {
        defaultCol.allowEditing = false
      }
      // 生产线 输入文本 非必填 长度 250
      defaultCol.width = '108'
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 250,
        isListenChange: false,
        hasSearch: false,
        disableConverter: (data) => {
          return data.status === Status.abnormal || data.status === Status.normal
        }
      })
    } else if (col.fieldCode === 'deliveryDate') {
      // 交货日期 选择日期-日期 必填
      defaultCol.width = '200'
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.allowEditing = true
      defaultCol.template = Component.timeDate({
        dataKey: 'deliveryTime',
        isDateTime: false,
        isDate: true,
        isTime: false
      })
      defaultCol.editTemplate = Component.timeInput({
        dataKey: 'deliveryTime',
        showClearBtn: true,
        allowEdit: true,
        isDate: false,
        isTime: false,
        isDateTime: true,
        maxDate: undefined,
        disableConverter: (data) => {
          // return data.status === Status.abnormal
          return (
            data.status !== 0 &&
            data.status !== 1 &&
            data.status !== 2 &&
            data.status !== 3 &&
            data.status !== 4
          )
        }
      })
    } else if (col.fieldCode === 'deliveryTime') {
      // 交货日期 选择日期-日期 必填
      defaultCol.width = '100'
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.allowEditing = false
      defaultCol.template = Component.timeDate({
        dataKey: col.fieldCode,
        isDateTime: false,
        isDate: false,
        isTime: true,
        isCompare: true
      })
      defaultCol.editTemplate = Component.timeDate({
        dataKey: col.fieldCode,
        isDateTime: false,
        isDate: false,
        isTime: true,
        isCompare: true
      })
    } else if (col.fieldCode === 'supplierRemark') {
      // 供应商备注 不可编辑
      defaultCol.width = '108'
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'planGroupRemark') {
      // 计划组备注 输入文本 非必填 长度 250
      defaultCol.width = '115'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 250,
        isListenChange: false,
        hasSearch: false,
        disableConverter: (data) => {
          return data.status === Status.abnormal || data.status === Status.normal
        }
      })
    } else if (col.fieldCode === 'dispatcherRemark') {
      // 调度员备注 输入文本 非必填 长度 250
      defaultCol.width = '106'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: true,
        disabled: false,
        maxlength: 250,
        hasSearch: false,
        isListenChange: false,
        disableConverter: (data) => {
          return data.status !== 0 && data.status !== 1 && data.status !== 4
        }
      })
    } else if (col.fieldCode === 'itemCode') {
      // 物料 code-name 下拉框选择 主数据 code 显示，input 显示 code-name 弹框选择 必填
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '120'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      // defaultCol.template = codeNameColumn({
      //   firstKey: 'itemCode',
      //   secondKey: 'itemName'
      // })
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: false,
        maxlength: 100,
        isListenChange: true,
        hasSearch: true,
        isItem: true,
        format: (data) => {
          let theColumnData = ''
          if (data['itemCode'] && data['itemName']) {
            // firstKey-secondKey
            theColumnData = `${data['itemCode']}-${data['itemName']}`
          } else if (data['itemCode']) {
            // firstKey
            theColumnData = data['itemCode']
          } else if (data['itemName']) {
            // secondKey
            theColumnData = data['itemName']
          }
          return theColumnData
        },
        disabled: false
        // disableConverter: (data) => {
        //   return (
        //   );
        // },
      })
      // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.allowFiltering = false
      // 主数据选择器
      // defaultCol.searchOptions = {
      //   ...MasterDataSelect.material,
      //   placeholder: i18n.t("物料"),
      // };
    } else if (col.fieldCode === 'siteCode') {
      // 工厂 下拉框选择 主数据 code-name 显示 模糊搜索编号/名称  必填
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '100'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      // defaultCol.template = codeNameColumn({
      //   firstKey: 'siteCode',
      //   secondKey: 'siteName'
      // })
      defaultCol.editTemplate = Component.siteCodeSelect
      // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.allowFiltering = false
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.factoryAddress,
        placeholder: i18n.t('工厂')
      }
    } else if (col.fieldCode === 'senderAddress') {
      // 库存地点 下拉框选择 主数据 code-name 显示 模糊搜索编号/名称 必填
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '162'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      if (data.status !== Status.new) {
        defaultCol.allowEditing = false
      }
      defaultCol.template = codeNameColumn({
        firstKey: 'senderAddress'
        // secondKey: 'warehouseName'
      })
      defaultCol.editTemplate = Component.warehouseCodeSelectNew
      // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.allowFiltering = false
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.stockAddress,
        placeholder: i18n.t('库存地点')
      }
    } else if (col.fieldCode === 'planGroupCode') {
      // 计划组 code-name 显示 模糊搜索编号/名称
      defaultCol.width = '300'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'planGroupCode',
        secondKey: 'planGroupName'
      })
      defaultCol.editTemplate = Component.planGroupCodeSelect
      // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.allowFiltering = false
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessGroup
      }
    } else if (col.fieldCode === 'subSiteCode') {
      // 分厂 code-name 下拉框选择 交货地址配置表 非必填
      defaultCol.width = '138'
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'subSiteCode',
        secondKey: 'subSiteName'
      })
      defaultCol.editTemplate = Component.subSiteSelect
    } else if (col.fieldCode === 'subSiteAddressCode') {
      // 分厂库存地点 code-name 下拉框选择 交货地址配置表 非必填
      defaultCol.width = '123'
      defaultCol.allowFiltering = false
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'subSiteAddressCode',
        secondKey: 'subSiteAddress'
      })
      defaultCol.editTemplate = Component.subSiteAddressSelect
      // } else if (col.fieldCode === 'senderAddress') {
      //   // 收货信息 下拉框选择 交货地址配置表 必填
      //   // defaultCol.headerTemplate = Component.requiredHeader({
      //   //   headerText: defaultCol.headerText,
      //   // });
      //   defaultCol.width = '350'
      //   defaultCol.allowFiltering = false
      //   defaultCol.allowEditing = true
      //   defaultCol.ignore = true
      //   // defaultCol.template = codeNameColumn({
      //   //   firstKey: 'senderName',
      //   //   secondKey: 'senderPhone',
      //   //   thirdKey: 'senderAddress'
      //   // })
      //   // defaultCol.editTemplate = Component.senderAddressSelect
      //   defaultCol.editTemplate = Component.inputText({
      //     dataKey: col.fieldCode,
      //     showClearBtn: true,
      //     maxlength: 250,
      //     isListenChange: false,
      //     hasSearch: false,
      //     disableConverter: true
      //   })
      //   defaultCol.ignore = true
    } else if (
      col.fieldCode === 'warehouseCode' ||
      col.fieldCode === 'senderName' ||
      col.fieldCode === 'senderPhone'
    ) {
      // 收货信息 下拉框选择 交货地址配置表 必填
      // defaultCol.headerTemplate = Component.requiredHeader({
      //   headerText: defaultCol.headerText,
      // });
      defaultCol.width = '350'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.ignore = true

      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: true,
        disabled: col.fieldCode === 'warehouseCode' ? true : false,
        hasSearch: false,

        maxlength: 250,
        isListenChange: true
      })
      defaultCol.ignore = true
    } else if (col.fieldCode === 'workCenterCode') {
      // 工作中心 code-name 显示 主数据 非必填 模糊搜索编号/名称
      defaultCol.width = '120'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      // defaultCol.template = codeNameColumn({
      //   firstKey: 'workCenterCode',
      //   secondKey: 'workCenter'
      // })
      defaultCol.editTemplate = Component.workCenterCodeSelect
      // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.allowFiltering = false
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.workCenter
      }
    } else if (col.fieldCode === 'processorCode') {
      // 加工商 code-name 下拉选择 供应商主数据 模糊搜索，配送方式为 直送 时 必填，选择工作中心时，根据工作中心配置表匹配带出
      // 选择 工作中心 后，根据 工作中心-加工商配置表 自动选中加工商
      defaultCol.width = '400'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'processorCode',
        secondKey: 'processorName'
      })
      defaultCol.editTemplate = Component.processorCodeSelect
      // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.allowFiltering = false
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.supplier
      }
    } else if (col.fieldCode === 'companyCode') {
      // 公司 code-name 不可编辑 工厂带出来
      defaultCol.allowFiltering = false
      defaultCol.width = '100'
      defaultCol.allowEditing = true
      // defaultCol.template = codeNameColumn({
      //   firstKey: 'companyCode',
      //   secondKey: 'companyName'
      // })
      defaultCol.editTemplate = Component.companyCodeInputText
      // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.allowFiltering = false
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessCompany
      }
    } else if (col.fieldCode === 'saleOrder') {
      // 销售订单 输入文本 非必填 长度 250
      defaultCol.width = '250'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 250,
        isListenChange: false,
        hasSearch: false,
        disableConverter: (data) => {
          return data.status === Status.abnormal || data.status === Status.normal
        }
      })
    } else if (col.fieldCode === 'workOrder') {
      // 销售订单 输入文本 非必填 长度 workOrder
      defaultCol.width = '250'
      defaultCol.allowEditing = true
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 250,
        isListenChange: false,
        hasSearch: false,
        disableConverter: (data) => {
          return data.status === Status.abnormal || data.status === Status.normal
        }
      })
    } else if (col.fieldCode === 'supplierCode') {
      // 供应商 code-name 下拉框选择 供应商主数据 模糊搜索
      defaultCol.width = '100'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      // defaultCol.template = codeNameColumn({
      //   firstKey: 'supplierCode',
      //   secondKey: 'supplierName'
      // })
      defaultCol.editTemplate = Component.supplierCodeSelect
      // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.allowFiltering = false
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.supplier
      }
    } else if (
      col.fieldCode === 'projectTextBatch' ||
      col.fieldCode === 'plannerRemark' ||
      col.fieldCode === 'salesOrder'
    ) {
      // 项目文本批次 输入文本 非必填 长度 250
      defaultCol.width = '125'
      if (col.fieldCode === 'plannerRemark') {
        defaultCol.width = '250'
      }
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 250,
        isListenChange: false,
        hasSearch: false,
        disableConverter: (data) => {
          // return data.status === Status.abnormal || data.status === Status.normal
          if (
            (data.status === 2 || data.status === 3 || data.status === 4) &&
            (col.fieldCode === 'plannerRemark' || col.fieldCode === 'projectTextBatch')
          ) {
            return true
          }
          return data.id
        }
      })
    } else if (col.fieldCode === 'batch') {
      // 批量 输入数字，小数位数 3 位
      defaultCol.width = '250'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.number({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxValue: 999999999999999.999,
        minValue: 0,
        precision: 3,
        disableConverter: (data) => {
          return data.status === Status.abnormal || data.status === Status.normal
        }
      })
    } else if (col.fieldCode === 'demandQty') {
      // 需求数量（叫料数量） 输入数字 小数位数3位
      defaultCol.width = '150'
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.number({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxValue: 999999999999999.999,
        minValue: 0,
        precision: 3,
        disableConverter: (data) => {
          // return (
          //   data.status === Status.abnormal ||
          //   Number(data.remainingDeliveryNum) !== Number(data.demandQty)
          // )
          return (
            data.status !== 0 &&
            data.status !== 1 &&
            data.status !== 2 &&
            data.status !== 3 &&
            data.status !== 4
          )
        }
      })
    } else if (col.fieldCode === 'callMaterialQty') {
      // 需求数量（叫料数量） 输入数字 小数位数3位
      defaultCol.width = '150'
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.number({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxValue: 999999999999999.999,
        minValue: 0,
        precision: 3,
        disableConverter: (data) => {
          // return data.status === Status.abnormal || data.status === Status.normal
          return (
            data.status !== 0 &&
            data.status !== 1 &&
            data.status !== 2 &&
            data.status !== 3 &&
            data.status !== 4
          )
        }
      })
    } else if (
      col.fieldCode === 'receiveQty' ||
      col.fieldCode === 'closePersonName' ||
      col.fieldCode === 'accumulativeQuantityShipped'
    ) {
      // 收货数量 不可编辑
      defaultCol.width = '95'
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'transitQty') {
      // 在途数量 不可编辑
      defaultCol.width = '95'
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'remainingDeliveryNum') {
      // 剩余送货数量 不可编辑
      defaultCol.width = '125'
      defaultCol.allowEditing = false
      defaultCol.type = 'number'

      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'cumCallMaterialQty') {
      // 累计叫料数量 不可编辑
      defaultCol.width = '250'
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'cumReceiveQty') {
      // 累计收货数量 不可编辑
      defaultCol.width = '250'
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'cumTransitQty') {
      // 累积在途数量 不可编辑
      defaultCol.width = '250'
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'dispatcherName') {
      // 调度员名称 新增时-自动填入当前登录人 不可修改
      defaultCol.width = '85'
      defaultCol.allowEditing = false
      defaultCol.allowFiltering = false
      defaultCol.template = codeNameColumn({
        firstKey: 'dispatcherName',
        secondKey: 'scheduleUserName'
      })
      defaultCol.editTemplate = codeNameColumn({
        firstKey: 'dispatcherName',
        secondKey: 'scheduleUserName'
      })
    } else if (
      col.fieldCode === 'jitUpdateTime' ||
      col.fieldCode === 'createTime' ||
      col.fieldCode === 'closeTime'
    ) {
      // JIT更新时间 不可编辑
      defaultCol.width = '138'
      defaultCol.allowEditing = false
      defaultCol.template = Component.timeDate({
        dataKey: col.fieldCode,
        isDateTime: true,
        isDate: false,
        isTime: false
      })
      defaultCol.editTemplate = Component.timeDate({
        dataKey: col.fieldCode,
        isDateTime: true,
        isDate: false,
        isTime: false
      })
    } else if (col.fieldCode === 'transferPlannerName') {
      // 转交计划员 code-name 不可编辑
      defaultCol.allowFiltering = false
      defaultCol.width = '105'
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'transferPlannerName',
        secondKey: 'transferPlanName'
      })
      defaultCol.editTemplate = codeNameColumn({
        firstKey: 'transferPlannerName',
        secondKey: 'transferPlanName'
      })
      // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.allowFiltering = false
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.staff
      }
    }
    colData.push(defaultCol)
  })

  return colData
}

// 格式化表格动态数据 导入
export const formatImportColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150'
    }
    if (col.fieldCode === 'id') {
      // 行主键
      defaultCol.visible = false
      defaultCol.isIdentity = true // 加上这个，新增一行的时候，主键不会变成可输入
      defaultCol.isPrimaryKey = true
      defaultCol.allowEditing = false
    } else if (col.fieldCode === 'checkBox') {
      // checkBox
      defaultCol.type = 'checkbox'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.showInColumnChooser = false
      defaultCol.width = '50'
      // 编辑时显示
      // defaultCol.editTemplate = Component.empty;
    } else if (col.fieldCode === 'serialNumber') {
      // 序号
      defaultCol.width = '66'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.ignore = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'batchCode') {
      // 批次号 不可编辑
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 250,
        isListenChange: false,
        hasSearch: false,
        disableConverter: (data) => {
          // return data.status === Status.abnormal || data.status === Status.normal
          return data.id
        }
      })
    } else if (col.fieldCode === 'jitItemNo') {
      // JIT 行编号 不可编辑
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'isSatisfy') {
      // 发货状态 未发货-1 全部发货-2 部分发货-3
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: SatisfyOptions
        }
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.edit,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: SatisfyOptions
        }
      })
    } else if (col.fieldCode === 'buyerOrgCode') {
      // 采购组名称 code+name
      defaultCol.width = '300'
      defaultCol.editTemplate = Component.buyerOrgSelect
      defaultCol.template = codeNameColumn({
        firstKey: 'buyerOrgCode',
        secondKey: 'buyerOrgName'
      })
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
    } else if (col.fieldCode === 'productLineCode') {
      defaultCol.allowEditing = true
      if (data.status !== Status.new) {
        defaultCol.allowEditing = false
      }
      // 生产线 输入文本 非必填 长度 250
      defaultCol.width = '108'
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 250,
        isListenChange: false,
        hasSearch: false,
        disableConverter: (data) => {
          return data.status === Status.abnormal || data.status === Status.normal
        }
      })
    } else if (col.fieldCode === 'deliveryDate') {
      // 交货日期 选择日期-日期 必填
      defaultCol.width = '200'
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.allowEditing = true
      defaultCol.template = Component.timeDate({
        dataKey: col.fieldCode,
        isDateTime: true,
        isDate: false,
        isTime: false
      })
      defaultCol.editTemplate = Component.timeInput({
        dataKey: col.fieldCode,
        showClearBtn: true,
        allowEdit: false,
        isDate: false,
        isTime: false,
        isDateTime: true,
        maxDate: undefined,
        disableConverter: (data) => {
          // return data.status === Status.abnormal || data.status === Status.normal
          return (
            data.status !== 0 &&
            data.status !== 1 &&
            data.status !== 2 &&
            data.status !== 3 &&
            data.status !== 4
          )
        }
      })
    } else if (col.fieldCode === 'supplierRemark') {
      // 供应商备注 不可编辑
      defaultCol.width = '108'
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'planGroupRemark') {
      // 计划组备注 输入文本 非必填 长度 250
      defaultCol.width = '115'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 250,
        isListenChange: false,
        hasSearch: false,
        disableConverter: (data) => {
          return data.status === Status.abnormal || data.status === Status.normal
        }
      })
    } else if (col.fieldCode === 'dispatcherRemark') {
      // 调度员备注 输入文本 非必填 长度 250
      defaultCol.width = '106'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: true,
        disabled: false,
        maxlength: 250,
        hasSearch: false,
        isListenChange: false,
        disableConverter: (data) => {
          return data.status !== 0 && data.status !== 1 && data.status !== 4
        }
      })
    } else if (col.fieldCode === 'itemCode') {
      // 物料 code-name 下拉框选择 主数据 code 显示，input 显示 code-name 弹框选择 必填
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '100'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      // defaultCol.template = codeNameColumn({
      //   firstKey: 'itemCode',
      //   secondKey: 'itemName'
      // })
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: false,
        maxlength: 100,
        isListenChange: true,
        hasSearch: true,
        isItem: true,
        format: (data) => {
          let theColumnData = ''
          if (data['itemCode'] && data['itemName']) {
            // firstKey-secondKey
            theColumnData = `${data['itemCode']}-${data['itemName']}`
          } else if (data['itemCode']) {
            // firstKey
            theColumnData = data['itemCode']
          } else if (data['itemName']) {
            // secondKey
            theColumnData = data['itemName']
          }
          return theColumnData
        },
        disabled: false
        // disableConverter: (data) => {
        //   return (
        //   );
        // },
      })
      // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.allowFiltering = false
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.material,
        placeholder: i18n.t('物料')
      }
    } else if (col.fieldCode === 'siteCode') {
      // 工厂 下拉框选择 主数据 code-name 显示 模糊搜索编号/名称  必填
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '100'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      // defaultCol.template = codeNameColumn({
      //   firstKey: 'siteCode',
      //   secondKey: 'siteName'
      // })
      defaultCol.editTemplate = Component.siteCodeSelect
      // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.allowFiltering = false
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.factoryAddress,
        placeholder: i18n.t('工厂')
      }
    } else if (col.fieldCode === 'senderAddress') {
      // 库存地点 下拉框选择 主数据 code-name 显示 模糊搜索编号/名称 必填
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '162'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      if (data.status !== Status.new) {
        defaultCol.allowEditing = false
      }
      defaultCol.template = codeNameColumn({
        firstKey: 'senderAddress',
        secondKey: 'senderAddress'
      })
      defaultCol.editTemplate = Component.warehouseCodeSelectNew
      // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.allowFiltering = false
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.stockAddress,
        placeholder: i18n.t('库存地点')
      }
    } else if (col.fieldCode === 'planGroupCode') {
      // 计划组 code-name 显示 模糊搜索编号/名称
      defaultCol.width = '300'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'planGroupCode',
        secondKey: 'planGroupName'
      })
      defaultCol.editTemplate = Component.planGroupCodeSelect
      // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.allowFiltering = false
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessGroup
      }
    } else if (col.fieldCode === 'subSiteCode') {
      // 分厂 code-name 下拉框选择 交货地址配置表 非必填
      defaultCol.width = '138'
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'subSiteCode',
        secondKey: 'subSiteName'
      })
      // defaultCol.editTemplate = Component.subSiteSelect;
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: true,
        hasSearch: false,
        disabled: false,
        maxlength: 250,
        isListenChange: false
      })
    } else if (col.fieldCode === 'subSiteAddressCode') {
      // 分厂库存地点 code-name 下拉框选择 交货地址配置表 非必填
      defaultCol.width = '123'
      defaultCol.allowFiltering = false
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'subSiteAddressCode',
        secondKey: 'subSiteAddress'
      })
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: true,
        disabled: false,
        hasSearch: false,

        maxlength: 250,
        isListenChange: false
      })
    } else if (
      col.fieldCode === 'warehouseCode' ||
      col.fieldCode === 'senderName' ||
      col.fieldCode === 'senderPhone'
    ) {
      // 收货信息 下拉框选择 交货地址配置表 必填
      // defaultCol.headerTemplate = Component.requiredHeader({
      //   headerText: defaultCol.headerText,
      // });
      defaultCol.width = '350'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.ignore = true

      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: true,
        disabled: false,
        hasSearch: false,

        maxlength: 250,
        isListenChange: false
      })
      defaultCol.ignore = true
    } else if (col.fieldCode === 'workCenterCode') {
      // 工作中心 code-name 显示 主数据 非必填 模糊搜索编号/名称
      defaultCol.width = '300'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'workCenterCode',
        secondKey: 'workCenter'
      })
      defaultCol.editTemplate = Component.workCenterCodeSelect
      // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.allowFiltering = false
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.workCenter
      }
    } else if (col.fieldCode === 'processorCode') {
      // 加工商 code-name 下拉选择 供应商主数据 模糊搜索，配送方式为 直送 时 必填，选择工作中心时，根据工作中心配置表匹配带出
      // 选择 工作中心 后，根据 工作中心-加工商配置表 自动选中加工商
      defaultCol.width = '400'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'processorCode',
        secondKey: 'processorName'
      })
      defaultCol.editTemplate = Component.processorCodeSelect
      // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.allowFiltering = false
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.supplier
      }
    } else if (col.fieldCode === 'saleOrder') {
      // 销售订单 输入文本 非必填 长度 250
      defaultCol.width = '250'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 250,
        isListenChange: false,
        hasSearch: false,
        disableConverter: (data) => {
          return data.status === Status.abnormal || data.status === Status.normal
        }
      })
    } else if (col.fieldCode === 'supplierCode') {
      // 供应商 code-name 下拉框选择 供应商主数据 模糊搜索
      defaultCol.width = '100'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      // defaultCol.template = codeNameColumn({
      //   firstKey: 'supplierCode',
      //   secondKey: 'supplierName'
      // })
      defaultCol.editTemplate = Component.supplierCodeSelect
      // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.allowFiltering = false
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.supplier
      }
    } else if (
      col.fieldCode === 'projectTextBatch' ||
      col.fieldCode === 'plannerRemark' ||
      col.fieldCode === 'salesOrder'
    ) {
      // 项目文本批次 输入文本 非必填 长度 250
      defaultCol.width = '125'
      if (col.fieldCode === 'plannerRemark') {
        defaultCol.width = '250'
      }
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 250,
        isListenChange: false,
        hasSearch: false,
        disableConverter: (data) => {
          // return data.status === Status.abnormal || data.status === Status.normal
          if (
            (data.status === 2 || data.status === 3 || data.status === 4) &&
            (col.fieldCode === 'plannerRemark' || col.fieldCode === 'projectTextBatch')
          ) {
            return true
          }
          return data.id
        }
      })
    } else if (col.fieldCode === 'batch') {
      // 批量 输入数字，小数位数 3 位
      defaultCol.width = '250'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.number({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxValue: 999999999999999.999,
        minValue: 0,
        precision: 3,
        disableConverter: (data) => {
          return data.status === Status.abnormal || data.status === Status.normal
        }
      })
    } else if (col.fieldCode === 'demandQty') {
      // 需求数量（叫料数量） 输入数字 小数位数3位
      defaultCol.width = '150'
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.number({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxValue: 999999999999999.999,
        minValue: 0,
        precision: 3,
        disableConverter: (data) => {
          // return (
          //   data.status === Status.abnormal ||
          //   Number(data.remainingDeliveryNum) !== Number(data.demandQty)
          // )
          return (
            data.status !== 0 &&
            data.status !== 1 &&
            data.status !== 2 &&
            data.status !== 3 &&
            data.status !== 4
          )
        }
      })
    } else if (col.fieldCode === 'callMaterialQty') {
      // 需求数量（叫料数量） 输入数字 小数位数3位
      defaultCol.width = '150'
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.number({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxValue: 999999999999999.999,
        minValue: 0,
        precision: 3,
        disableConverter: (data) => {
          // return data.status === Status.abnormal || data.status === Status.normal
          return (
            data.status !== 0 &&
            data.status !== 1 &&
            data.status !== 2 &&
            data.status !== 3 &&
            data.status !== 4
          )
        }
      })
    } else if (col.fieldCode === 'dispatcherName') {
      // 调度员名称 新增时-自动填入当前登录人 不可修改
      defaultCol.width = '85'
      defaultCol.allowEditing = false
      defaultCol.allowFiltering = false
      defaultCol.template = codeNameColumn({
        firstKey: 'dispatcherName',
        secondKey: 'scheduleUserName'
      })
      defaultCol.editTemplate = codeNameColumn({
        firstKey: 'dispatcherName',
        secondKey: 'scheduleUserName'
      })
    } else if (col.fieldCode === 'jitUpdateTime' || col.fieldCode === 'createTime') {
      // JIT更新时间 不可编辑
      defaultCol.width = '138'
      defaultCol.allowEditing = false
      defaultCol.template = Component.timeDate({
        dataKey: col.fieldCode,
        isDateTime: true,
        isDate: false,
        isTime: false
      })
      defaultCol.editTemplate = Component.timeDate({
        dataKey: col.fieldCode,
        isDateTime: true,
        isDate: false,
        isTime: false
      })
    } else if (col.fieldCode === 'transferPlannerName') {
      // 转交计划员 code-name 不可编辑
      defaultCol.allowFiltering = false
      defaultCol.width = '105'
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'transferPlannerName',
        secondKey: 'transferPlanName'
      })
      defaultCol.editTemplate = codeNameColumn({
        firstKey: 'transferPlannerName',
        secondKey: 'transferPlanName'
      })
      // 不可是使用过滤搜索，因为是 code-name 形式
      defaultCol.allowFiltering = false
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.staff
      }
    }
    colData.push(defaultCol)
  })

  return colData
}

// 选择 物料 机弹框 格式化表格列
export const materielTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: 'auto'
    }
    colData.push(defaultCol)
  })

  return colData
}

// 表格数据序列化
export const serializeList = (list) => {
  if (list?.length > 0) {
    list.forEach((item, index) => {
      // 添加序号
      item.serialNumber = index + 1
      item.senderName = item.consignee
      item.senderPhone = item.contactWay
      item.senderAddress = item.deliverAddr
      item.transferPlanName = item.transferPlannerName
      // item.deliveryDate = item.deliveryTime
      // if (item.deliveryDate === '0' && item.deliveryTime !== '0') {
      // }
      // let deliveryDateTime = item.deliveryDate
      // if (item.deliveryTime) {
      //   deliveryDateTime = `${item.deliveryDate} ${item.deliveryTime}:00`
      // } else {
      //   deliveryDateTime = `${item.deliveryDate} 00:00:00`
      // }
      // item.deliveryDate = new Date(deliveryDateTime).getTime()
    })
  }

  return list
}
