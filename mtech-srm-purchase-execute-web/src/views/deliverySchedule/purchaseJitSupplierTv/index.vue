<template>
  <!-- JIT送货计划-供方 -->
  <div class="full-height pt20">
    <!-- 列模板 -->
    <mt-template-page
      ref="templateRef"
      class="frozenFistColumns"
      :hidden-tabs="false"
      :template-config="templateConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
      @dataBound="handleDataBound"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="factoryCode" :label="$t('工厂')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.factoryCode"
                :url="$API.masterData.getSiteAuthFuzzyUrl"
                multiple
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                params-key="fuzzyParam"
                records-position="data"
              ></RemoteAutocomplete>
            </mt-form-item>
            <!-- <mt-form-item prop="plannerName" :label="$t('计划员')" label-style="top">
              <mt-input
                v-model="searchFormModel.plannerName"
                :show-clear-button="true"
                :placeholder="$t('请输入计划员')"
              />
            </mt-form-item> -->
            <mt-form-item prop="batchCodes" :label="$t('销售订单号')" label-style="top">
              <mt-input
                v-model="batchCodes"
                :show-clear-button="true"
                :placeholder="$t('请输入')"
                @change="batchCodeChange"
              />
            </mt-form-item>
            <!-- <mt-form-item prop="structSeqNo" :label="$t('结构序号')" label-style="top">
              <mt-input
                v-model="structSeqNo"
                @change="() => (searchFormModel.structSeqNo = [structSeqNo])"
              />
            </mt-form-item> -->
            <mt-form-item prop="serialNo" :label="$t('序列号')" label-style="top">
              <mt-input v-model="searchFormModel.serialNo" />
            </mt-form-item>
            <!-- <mt-form-item prop="itemGroups" :label="$t('物料组')" label-style="top">
              <div style="display: flex">
                <mt-input
                  style="flex: 1"
                  v-model="itemGroups"
                  @change="itemGroupChange"
                  :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                />
                <mt-checkbox
                  v-model="searchFormModel.joinMaterialGroup"
                  @change="(e) => handleChange(e, 'joinMaterialGroup')"
                  :label="$t('关联查询')"
                  style="vertical-align: sub; display: inline-block; margin-left: 5px; width: 78px"
                />
              </div>
            </mt-form-item> -->
            <mt-form-item prop="itemCodes" :label="$t('物料编码')" label-style="top">
              <div style="display: flex">
                <mt-input
                  style="flex: 1"
                  v-model="itemCodes"
                  @change="itemChange"
                  :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                />
                <!-- <mt-checkbox
                  v-model="searchFormModel.joinMaterialCode"
                  @change="(e) => handleChange(e, 'joinMaterialCode')"
                  :label="$t('关联查询')"
                  style="vertical-align: sub; display: inline-block; margin-left: 5px; width: 78px"
                /> -->
              </div>
            </mt-form-item>
            <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.itemName"
                :placeholder="$t('支持模糊搜索')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <!-- <mt-form-item prop="salesOrder" :label="$t('销售订单')" label-style="top">
              <mt-input
                v-model="searchFormModel.salesOrder"
                :show-clear-button="true"
                :placeholder="$t('请输入销售订单')"
              />
            </mt-form-item> -->
            <mt-form-item prop="workCenterCodes" :label="$t('工作中心')" label-style="top">
              <mt-input
                v-model="workCenterCodes"
                :show-clear-button="true"
                :placeholder="$t('请输入工作中心')"
                @change="workCenterChange"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-multi-select
                style="flex: 1"
                v-model="searchFormModel.status"
                :show-select-all="true"
                :allow-filtering="true"
                filter-type="Contains"
                :data-source="statusOptions"
                :placeholder="$t('请选择')"
              ></mt-multi-select>
            </mt-form-item>
            <mt-form-item prop="deliveryDate" :label="$t('交货日期')" label-style="top">
              <mt-date-range-picker
                style="flex: 1"
                v-model="searchFormModel.deliveryDate"
                :allow-edit="false"
                :open-on-focus="true"
                :show-today-button="false"
                @change="(e) => handleDateTimeChange(e, 'deliveryDate')"
                :placeholder="$t('请选择交货日期')"
              />
            </mt-form-item>
            <mt-form-item prop="receiptShipmentStatus" :label="$t('收发货状态')" label-style="top">
              <mt-multi-select
                style="flex: 1"
                v-model="searchFormModel.receiptShipmentStatus"
                :show-select-all="true"
                :allow-filtering="true"
                filter-type="Contains"
                :data-source="receiptShipmentStatusOptions"
                :placeholder="$t('请选择')"
              ></mt-multi-select>
            </mt-form-item>
            <mt-form-item prop="consignee" :label="$t('收货人')" label-style="top">
              <mt-input v-model="searchFormModel.consignee" />
            </mt-form-item>
            <mt-form-item prop="contactWay" :label="$t('联系方式')" label-style="top">
              <mt-input v-model="searchFormModel.contactWay" />
            </mt-form-item>
            <mt-form-item prop="deliverAddr" :label="$t('收货地址')" label-style="top">
              <mt-input v-model="searchFormModel.deliverAddr" />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { formatTableColumnData, serializeList } from './config/index'
import {
  EditSettings,
  JitTableColumnData,
  JitToolbar,
  RequestType,
  ActionType,
  Status,
  StatusOptions,
  ReceiptShipmentStatusOptions
} from './config/constant'
import { rowDataTemp } from './config/variable'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'
// import { timeNumberToDate } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  components: {},
  data() {
    const lastTabIndex = JSON.parse(localStorage.getItem('lastTabIndex'))
    const pageType = this.$route.query.type

    return {
      batchCodes: '',
      itemCodes: '',
      workCenterCodes: '',
      itemGroups: '',
      structSeqNos: '',
      searchFormModel: {
        joinMaterialCode: false,
        joinMaterialGroup: false
      },
      apiWaitingQuantity: 0, // 调用的api正在等待数
      pageType, // 页面类型
      lastTabIndex, // 前一页面的 Tab index
      templateConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 不使用组件中的toolbar配置
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          toolbar: { tools: [JitToolbar, ['Setting']] },
          activatedRefresh: false,

          gridId: '6bc206d4-30bc-4cb5-a291-a6343a341794',
          grid: {
            editSettings: EditSettings,
            allowPaging: true, // 分页
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [10, 20, 50, 100, 200, 500, 1000],
              totalRecordsCount: 0
            },
            columnData: formatTableColumnData({
              data: JitTableColumnData
            }),
            dataSource: [],
            // rowDataBound: (args) => {
            //   if (args.data.status == 4) {
            //     args.row.classList.add('bg-light-red')
            //   }
            // },
            virtualPageSize: 30,
            enableVirtualization: true,
            showSelected: false,
            selectionSettings: {
              persistSelection: true, // 这个选项很重要，影响到虚拟滚动下能不能操作全选按钮
              type: 'Multiple',
              checkboxOnly: true
            },
            customSelection: true, // 使用自定义勾选列
            asyncConfig: {
              // ignoreDefaultSearch: true,
              recordsPosition: 'data.supplierPlanPage.records',

              url: `${BASE_TENANT}/jit/plan/supplier/tv/query`,
              serializeList: serializeList
            }
            // frozenColumns: 1, // 行内编辑的表格不可以冻结列
          }
        }
      ],
      isEditing: false, // 正在编辑数据,
      statusOptions: StatusOptions,
      receiptShipmentStatusOptions: ReceiptShipmentStatusOptions
    }
  },
  mounted() {},
  beforeDestroy() {
    localStorage.removeItem('lastTabIndex')
  },
  methods: {
    handleChange(e, labelName) {
      this.searchFormModel[labelName] = e.checked
    },
    batchCodeChange(e) {
      if (e) {
        this.searchFormModel.batchCodes = this.batchCodes.split(' ')
      } else {
        this.searchFormModel.batchCodes = null
      }
    },
    itemChange(e) {
      if (e) {
        this.searchFormModel.itemCodes = this.itemCodes.split(' ')
      } else {
        this.searchFormModel.itemCodes = null
      }
    },
    // 查询条件操作物料组切割
    itemGroupChange(e) {
      if (e) {
        this.searchFormModel.itemGroups = this.itemGroups.split(' ')
      } else {
        this.searchFormModel.itemGroups = null
      }
    },
    // 查询条件操作结构序号切割
    structSeqNoChange(e) {
      if (e) {
        this.searchFormModel.structSeqNos = this.structSeqNos.split(' ')
      } else {
        this.searchFormModel.structSeqNos = null
      }
    },
    workCenterChange(e) {
      if (e) {
        this.searchFormModel.workCenterCodes = this.workCenterCodes.split(' ')
      } else {
        this.searchFormModel.workCenterCodes = null
      }
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'From'] = dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        this.searchFormModel[field + 'To'] = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.searchFormModel[field + 'From'] = null
        this.searchFormModel[field + 'To'] = null
      }
    },
    // 重置查询条件
    handleCustomReset() {
      this.structSeqNo = ''
      this.itemCodes = null
      this.searchFormModel.itemCodes = null
      this.workCenterCode = null
      this.searchFormModel.workCenterCode = null
      this.itemGroups = null
      this.searchFormModel.itemGroups = null
      this.structSeqNos = null
      this.searchFormModel.structSeqNos = null
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
        if (key === 'deliveryDate') {
          this.searchFormModel[key] = [
            new Date(dayjs(new Date()).format('YYYY-MM-DD') + ' 00:00:00').toJSON(),
            new Date(dayjs(new Date()).format('YYYY-MM-DD') + ' 23:59:59').toJSON()
          ]
        }
      }
      this.searchFormModel.joinMaterialCode = false
      this.searchFormModel.joinMaterialGroup = false
      this.searchFormModel.deliveryDateFrom = dayjs(new Date()).format('YYYY-MM-DD') + ' 00:00:00'
      this.searchFormModel.deliveryDateTo = dayjs(new Date()).format('YYYY-MM-DD') + ' 23:59:59'
    },
    // toolbar 按钮点击
    handleClickToolBar(args) {
      const { toolbar, gridRef } = args
      const selectRows = []
      gridRef.dataSource.forEach((item) => {
        if (item.customChecked) {
          selectRows.push(item)
        }
      })
      if (toolbar.id === 'JitExport') {
        // 导出
        this.supplierJitInfoExport()
        return
      }
      if (selectRows.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (this.isEditing && toolbar.id !== 'refreshDataByLocal') {
        // 结束编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }

      if (toolbar.id === 'JitAccept') {
        // 接受
        this.handleJitAccept(selectRows)
      } else if (toolbar.id === 'JitRefuse') {
        // 拒绝
        this.handleJitRefuse(selectRows)
      }
    },
    // CellTool
    handleClickCellTool(args) {
      const { tool, data } = args

      if (this.isEditing) {
        // 正在编辑时，若不是点击刷新按钮，结束编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }

      if (tool.id === 'JitAccept') {
        // 接受
        this.handleJitAccept([data])
      } else if (tool.id === 'JitRefuse') {
        // 拒绝
        this.handleJitRefuse([data])
      }
    },
    // 接受
    handleJitAccept(selectedRecords) {
      let isValid = true
      // const today = Number(new Date())
      const idList = []
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        // const canFeedBackDay = Number(item.canFeedBackDay)
        if (item.status != Status.publish) {
          isValid = false
          this.$toast({
            content: this.$t('请选择已发布状态的数据'),
            type: 'warning'
          })
          break
          // } else if (canFeedBackDay > 0 && today > canFeedBackDay) {
          //   // 时间戳，"0" 时即为空, 超过时间限制
          //   isValid = false
          //   const canFeedBackDayStr = timeNumberToDate({
          //     formatString: 'YYYY-mm-dd HH:MM:SS',
          //     value: canFeedBackDay
          //   })
          //   let content = this.$t(`此数据只能在 ${canFeedBackDayStr} 前反馈`)
          //   if (selectedRecords.length > 1) {
          //     content = this.$t(`第 ${i + 1} 条数据只能在 ${canFeedBackDayStr} 前反馈`)
          //   }
          //   this.$toast({
          //     content,
          //     type: 'warning'
          //   })
          //   break
        }
        idList.push({
          id: item.id,
          status: item.status,
          serialNumber: item.serialNumber,
          deliveryTime: item.deliveryTime,
          dataSetName: item.dataSetName,
          feedbackStatus: 3,
          supplierTenantId: item.supplierTenantId,
          dispatcherName: item.dispatcherName,
          supplierName: item.supplierName,
          dispatcherCode: item.dispatcherCode,
          callMaterialQty: item.callMaterialQty,
          cumCallMaterialQty: item.cumCallMaterialQty,
          demandQty: item.demandQty
        })
      }
      if (isValid) {
        // 反馈
        this.supplierJitInfoFeedback(idList, 3)
      }
    },
    // 拒绝
    handleJitRefuse(selectedRecords) {
      let isValid = true
      // const today = Number(new Date())
      const idList = []
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        // const canFeedBackDay = Number(item.canFeedBackDay)
        if (item.status != Status.publish) {
          isValid = false
          this.$toast({
            content: this.$t('请选择已发布状态的数据'),
            type: 'warning'
          })
          break
          // } else if (canFeedBackDay > 0 && today > canFeedBackDay) {
          //   // 时间戳，"0" 时即为空, 超过时间限制
          //   isValid = false
          //   const canFeedBackDayStr = timeNumberToDate({
          //     formatString: 'YYYY-mm-dd HH:MM:SS',
          //     value: canFeedBackDay
          //   })
          //   let content = this.$t(`此数据只能在 ${canFeedBackDayStr} 前反馈`)
          //   if (selectedRecords.length > 1) {
          //     content = this.$t(`第 ${i + 1} 条数据只能在 ${canFeedBackDayStr} 前反馈`)
          //   }
          //   this.$toast({
          //     content,
          //     type: 'warning'
          //   })
          //   break
        }
        idList.push({
          id: item.id,
          status: item.status,
          serialNumber: item.serialNumber,
          // deliveryDate: deliveryDateTime.split(' ')[0],
          // deliveryTime: deliveryDateTime.split(' ')[1],
          deliveryTime: item.deliveryTime,
          dataSetName: item.dataSetName,
          feedbackStatus: 4,
          supplierTenantId: item.supplierTenantId,
          dispatcherName: item.dispatcherName,
          supplierName: item.supplierName,
          dispatcherCode: item.dispatcherCode,
          callMaterialQty: item.callMaterialQty,
          cumCallMaterialQty: item.cumCallMaterialQty
        })
      }
      if (isValid) {
        // 反馈
        this.supplierJitInfoFeedback(idList, 4)
      }
    },
    // actionBegin 表格编辑生命周期
    actionBegin(args) {
      const { requestType, action, rowData } = args
      // console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args);
      if (requestType === RequestType.add) {
        // 开始行添加
        this.isEditing = true
        // // 即将添加一行，赋值新增行的初始数据
        // rowDataTemp.splice(0, rowDataTemp.length); // 清空数组数据
        // const newRowData = cloneDeep(NewRowData);
        // rowDataTemp.push(newRowData);
        // args.rowData = newRowData;
        // args.data = newRowData;
      } else if (requestType === RequestType.save && action === ActionType.add) {
        // 即将保存新增时，将新增中编辑的数据付给组件
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === RequestType.beginEdit) {
        if (rowData.status !== Status.publish) {
          // 数据不可编辑 状态 != 已发布
          args.cancel = true
          this.$toast({
            content: this.$t('仅已发布状态的数据可编辑'),
            type: 'warning'
          })
          return
        }
        // 开始行编辑
        this.isEditing = true
        // 即将编辑行，赋值当前行的数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },
    // actionComplete 表格编辑生命周期
    actionComplete(args) {
      const { requestType, action, rowIndex, index } = args
      const rowData = rowDataTemp[rowDataTemp.length - 1]
      // console.log(
      //   `Complete,\nrequest: ${requestType}\naction: ${action}`,
      //   args
      // );
      if (requestType === RequestType.save && action == ActionType.edit) {
        // 完成编辑行
        this.isEditing = false
        // rowData, rowIndex
        if (!this.isValidSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API
          this.supplierJitInfoRemark({ rowData, rowIndex })
        }
      } else if (requestType === RequestType.save && action == ActionType.add) {
        // 完成新增行
        this.isEditing = false
        // rowData, index
        if (!this.isValidSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API
          this.supplierJitInfoRemark({ rowData, rowIndex: index })
        }
      } else if (requestType === RequestType.sorting || requestType === RequestType.refresh) {
        // 丢弃编辑中状态
        this.isEditing = false
      }
    },
    // 校验数据
    isValidSaveData() {
      // 没有校验
      // const {
      //   supRemark, // 供应商备注
      // } = data;
      let valid = true
      // if (supRemark === null || supRemark === undefined) {
      //   this.$toast({
      //     content: this.$t("请选择配送方式"),
      //     type: "warning",
      //   });
      //   valid = false;
      // }

      return valid
    },
    // 表格数据绑定完成
    handleDataBound() {},
    // 供方jit-修改备注
    supplierJitInfoRemark(args) {
      const { rowData, rowIndex } = args
      const params = {
        supplierRemark: rowData.supplierRemark, // 供应商备注
        id: rowData.id,
        status: rowData.status
      }
      this.apiStartLoading()
      this.$API.deliverySchedule
        .supplierJitInfoUpdate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    // 供方jit-反馈
    supplierJitInfoFeedback(idList, feedbackStatus) {
      const params = {
        queryReq: { ...this.searchFormModel, page: { current: 1, size: 999999 }, feedbackStatus },
        chooseReqList: idList
      }
      this.apiStartLoading()
      this.$API.deliverySchedule
        .supplierJitInfoFeedbackTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 列表
            this.$refs.templateRef.refreshCurrentGridData()
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 供方jit-Excel导出
    supplierJitInfoExport() {
      let obj = JSON.parse(sessionStorage.getItem(this.templateConfig[0].gridId))?.visibleCols
      const headerMap = {}
      if (obj !== undefined && obj.length) {
        obj?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'serialNumber') {
            let field = i.field
            // if (i.field === 'deliveryDate') {
            //   field = 'deliveryTime'
            // }
            headerMap[field] = i.headerText
          }
        })
      } else {
        formatTableColumnData({
          data: JitTableColumnData
        })?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'serialNumber') {
            let field = i.field
            // if (i.field === 'deliveryDate') {
            //   field = 'deliveryTime'
            // }
            headerMap[field] = i.headerText
          }
        })
      }
      const params = {
        page: {
          size: 2000,
          current: 1
        },
        headerMap,
        ...this.searchFormModel
      }
      this.apiStartLoading()
      this.$API.deliverySchedule
        .supplierJitInfoExportTv(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },

    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .need-jit-font-red {
  color: red;
}
/deep/ .ant-select-selection {
  background: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.42) !important;
}
// 行内 cellTool
/deep/ .column-tool {
  margin-top: 8px;

  .template-svg {
    cursor: pointer;
    font-size: 12px;
    color: #6386c1;

    &:nth-child(n + 2) {
      margin-left: 10px;
    }
  }
}

// 行编辑时包含按钮的项目
/deep/ .input-search-content {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .mt-input {
    flex: 1;
  }

  .mutliselect-container {
    width: 100%;
  }
  // 超过宽度显示省略号
  .text-ellipsis {
    width: 125px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .mt-icons {
    width: 20px;
    flex-shrink: 0;
    text-align: center;
    cursor: pointer;
  }
}

/deep/ .grid-edit-column {
  padding: 12px 0;
}

.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  // padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
  }
}
</style>
