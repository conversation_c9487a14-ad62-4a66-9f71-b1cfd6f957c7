import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
// 表格编辑 的 RequestType
export const RequestType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  sorting: 'sorting',
  refresh: 'refresh'
}

// 表格编辑 的 action
export const ActionType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  edit: 'edit'
}

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 行编辑触发的 change 事件类型
export const ComponentChangeType = {
  code: 'code', // 例：下拉框选择“供应商”，供应商编码（code)要被修改
  link: 'link' // 例：计划组：根据物料+工厂+计划组的业务组类型 code 带出
}

// 表格 EditSettings
export const EditSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
} // 编辑设置

// 反馈结果 3-接受,4-拒绝 接受 发 反馈满足 拒绝 发 反馈不满足状态
export const FeedbackStatus = {
  accept: 3, // 接受
  reject: 4 // 拒绝
}

// 状态 0-新建 1-已修改 3-反馈满足 4-反馈不满足 5-已确认 6-已关闭
export const Status = {
  // new: 0, // 新建
  // edit: 1, // 已修改
  publish: 2, // 已发布
  normal: 3, // 反馈满足
  abnormal: 4, // 反馈不满足
  confirm: 5, // 已确认
  closed: 6 // 已关闭
}
// 状态 text
export const StatusText = {
  // [Status.new]: i18n.t('新建'),
  // [Status.edit]: i18n.t('已修改'),
  [Status.publish]: i18n.t('已发布'),
  [Status.abnormal]: i18n.t('反馈-不满足'),
  [Status.normal]: i18n.t('反馈-满足'),
  [Status.confirm]: i18n.t('已确认'),
  [Status.closed]: i18n.t('已关闭')
}
// 状态 class
export const StatusClass = {
  // [Status.new]: 'col-active',
  // [Status.edit]: 'col-active',
  [Status.publish]: 'col-active',
  [Status.abnormal]: 'col-error',
  [Status.normal]: 'col-active',
  [Status.confirm]: 'col-active',
  [Status.closed]: 'col-inactive'
}
// 状态 对应的 Options
export const StatusOptions = [
  // {
  //   value: Status.new, // 新建
  //   text: StatusText[Status.new],
  //   cssClass: StatusClass[Status.new]
  // },
  // {
  //   value: Status.edit, // 已修改
  //   text: StatusText[Status.edit],
  //   cssClass: StatusClass[Status.edit]
  // },
  {
    value: Status.publish, // 已发布
    text: StatusText[Status.publish],
    cssClass: StatusClass[Status.publish]
  },
  {
    value: Status.abnormal, // 反馈异常
    text: StatusText[Status.abnormal],
    cssClass: StatusClass[Status.abnormal]
  },
  {
    value: Status.normal, // 反馈正常
    text: StatusText[Status.normal],
    cssClass: StatusClass[Status.normal]
  },
  {
    value: Status.confirm, // 已确认
    text: StatusText[Status.confirm],
    cssClass: StatusClass[Status.confirm]
  },
  {
    value: Status.closed, // 已关闭
    text: StatusText[Status.closed],
    cssClass: StatusClass[Status.closed]
  }
]
// 是否满足 对应的 Options
export const SatisfyOptions = [
  {
    value: '0', // 不满足
    text: i18n.t('不满足'),
    cssClass: ''
  },
  {
    value: '1', // 满足
    text: i18n.t('满足'),
    cssClass: ''
  }
]

// 发货状态 未发货-1 全部发货-2 部分发货-3
export const DeliveryStatus = {
  notShipped: 1, // 未发货
  allShipped: 2, // 全部发货
  partiallyShipped: 3 // 部分发货
}
// 发货状态 text
export const DeliveryStatusText = {
  [DeliveryStatus.notShipped]: i18n.t('未发货'),
  [DeliveryStatus.allShipped]: i18n.t('全部发货'),
  [DeliveryStatus.partiallyShipped]: i18n.t('部分发货')
}
// 发货状态 Class
export const DeliveryStatusClass = {
  [DeliveryStatus.notShipped]: '',
  [DeliveryStatus.allShipped]: '',
  [DeliveryStatus.partiallyShipped]: ''
}
// 发货状态 对应的 Options
export const DeliveryStatusOptions = [
  {
    value: DeliveryStatus.notShipped, // 未发货
    text: DeliveryStatusText[DeliveryStatus.notShipped],
    cssClass: DeliveryStatusClass[DeliveryStatus.notShipped]
  },
  {
    value: DeliveryStatus.allShipped, // 全部发货
    text: DeliveryStatusText[DeliveryStatus.allShipped],
    cssClass: DeliveryStatusClass[DeliveryStatus.allShipped]
  },
  {
    value: DeliveryStatus.partiallyShipped, // 部分发货
    text: DeliveryStatusText[DeliveryStatus.partiallyShipped],
    cssClass: DeliveryStatusClass[DeliveryStatus.partiallyShipped]
  }
]

// 收货状态 未收货-1 全部收货-2 部分收货-3
export const ReceiveStatus = {
  notReceived: 1, // 未收货
  allReceived: 2, // 全部收货
  partialReceipt: 3 // 部分收货
}
// 收货状态 text
export const ReceiveStatusText = {
  [ReceiveStatus.notReceived]: i18n.t('未收货'),
  [ReceiveStatus.allReceived]: i18n.t('全部收货'),
  [ReceiveStatus.partialReceipt]: i18n.t('部分收货')
}
// 收货状态 Class
export const ReceiveStatusClass = {
  [ReceiveStatus.notShipped]: '',
  [ReceiveStatus.allReceived]: '',
  [ReceiveStatus.partialReceipt]: ''
}
// 收货状态 对应的 Options
export const ReceiveStatusOptions = [
  {
    value: ReceiveStatus.notReceived, // 未收货
    text: ReceiveStatusText[ReceiveStatus.notReceived],
    cssClass: ReceiveStatusClass[ReceiveStatus.notReceived]
  },
  {
    value: ReceiveStatus.allReceived, // 全部收货
    text: ReceiveStatusText[ReceiveStatus.allReceived],
    cssClass: ReceiveStatusClass[ReceiveStatus.allReceived]
  },
  {
    value: ReceiveStatus.partialReceipt, // 部分收货
    text: ReceiveStatusText[ReceiveStatus.partialReceipt],
    cssClass: ReceiveStatusClass[ReceiveStatus.partialReceipt]
  }
]

export const ReceiptShipmentStatusOptions = [
  {
    value: 0,
    text: i18n.t('部分发货未收')
  },
  {
    value: 1,
    text: i18n.t('全部发货未收')
  },
  {
    value: 2,
    text: i18n.t('部分收货')
  },
  {
    value: 3,
    text: i18n.t('全部收货')
  },
  {
    value: 4,
    text: i18n.t('未发货')
  }
]

// 送货地址配置类型
export const DeliveryAddressConfigType = {
  inventory: 1, // 工厂+库存地点
  processor: 2, // 工厂+加工商
  branch: 3, // 工厂+库存地点+分厂+分厂库存地点
  material: 4 // 工厂+库存地点+物料
}
// 送货地址配置类型 是否默认
export const NeedDefault = {
  yes: 1, // 默认
  no: 2 // 非默认
}

// 配送方式 0直送 1非直送
export const DeliveryMethod = {
  direct: '0', // 直送
  indirect: '1' // 非直送
}
// 配送方式 text
export const DeliveryMethodText = {
  [DeliveryMethod.direct]: i18n.t('直送'),
  [DeliveryMethod.indirect]: i18n.t('非直送')
}
// 配送方式 Class
export const DeliveryMethodClass = {
  [DeliveryMethod.direct]: '',
  [DeliveryMethod.indirect]: ''
}
// 配送方式 对应的 Options
export const DeliveryMethodOptions = [
  {
    value: DeliveryMethod.direct, // 直送
    text: DeliveryMethodText[DeliveryMethod.direct],
    cssClass: DeliveryMethodClass[DeliveryMethod.direct]
  },
  {
    value: DeliveryMethod.indirect, // 非直送
    text: DeliveryMethodText[DeliveryMethod.indirect],
    cssClass: DeliveryMethodClass[DeliveryMethod.indirect]
  }
]

// 委外方式 0标准委外 1销售委外 2非委外
export const OutsourcedTypeOptions = [
  {
    value: '0', // 标准委外
    text: i18n.t('标准委外'),
    cssClass: ''
  },
  {
    value: '1', // 销售委外
    text: i18n.t('销售委外'),
    cssClass: ''
  },
  {
    value: '2', // 非委外
    text: i18n.t('非委外'),
    cssClass: ''
  }
]

// Jit表格 Toolbar
export const JitToolbar = [
  {
    id: 'JitAccept',
    icon: 'icon_table_accept1',
    permission: ['O_02_1637'],
    title: i18n.t('接受')
  },
  {
    id: 'JitRefuse',
    icon: 'icon_table_refuse1',
    permission: ['O_02_1638'],
    title: i18n.t('拒绝')
  },
  {
    id: 'JitExport',
    icon: 'icon_solid_export',
    permission: ['O_02_1639'],
    title: i18n.t('导出')
  }
]
// 单元格操作按钮
export const CellTools = [
  {
    id: 'JitAccept',
    icon: '',
    permission: ['O_02_1637'],
    title: i18n.t('接受'),
    visibleCondition: (data) => {
      let isShow = false
      // const canFeedBackDay = Number(data.canFeedBackDay)
      // const today = Number(new Date())
      if (data.status == Status.publish) {
        // if (data.status == Status.publish && canFeedBackDay > 0 && today < canFeedBackDay) {
        // 状态：待反馈 && 时间戳，"0" 时即为空, 没超过时间限制
        isShow = true
      }

      return isShow
    }
  },
  {
    id: 'JitRefuse',
    icon: '',
    permission: ['O_02_1638'],
    title: i18n.t('拒绝'),
    visibleCondition: (data) => {
      let isShow = false
      // const canFeedBackDay = Number(data.canFeedBackDay)
      // const today = Number(new Date())
      if (data.status == Status.publish) {
        // if (data.status == Status.publish && canFeedBackDay > 0 && today < canFeedBackDay) {
        // 状态：待反馈 && 时间戳，"0" 时即为空, 没超过时间限制
        isShow = true
      }

      return isShow
    }
  }
]

// JIT表格列数据
export const JitTableColumnData = [
  {
    fieldCode: 'id'
  },
  {
    fieldCode: 'serialNumber', // 前端定义 不可编辑
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'serialNo',
    fieldName: i18n.t('序列号'),
    allowEditing: false
  },
  {
    fieldCode: 'status', // 状态
    fieldName: i18n.t('状态'),
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    }
  },
  {
    fieldCode: 'receiptShipmentStatus',
    fieldName: i18n.t('收发货状态'),
    allowEditing: false
  },
  {
    fieldCode: 'batchCode', // 批次号 不可编辑
    fieldName: i18n.t('销售订单号'),
    allowEditing: false
  },
  {
    fieldCode: 'productLineCode', // 生产线 输入文本 非必填
    fieldName: i18n.t('生产线'),
    allowEditing: false
  },
  {
    fieldCode: 'workCenterCode', // 工作中心 code-name 下拉选择 模糊搜索 非必填 主数据
    fieldName: i18n.t('工作中心编码'),
    allowEditing: false
    // workCenter
    // workCenterId
  },
  {
    fieldCode: 'workCenter',
    fieldName: i18n.t('工作中心名称'),
    allowEditing: false
  },
  {
    fieldCode: 'siteCode', // 工厂 下拉框选择 主数据 code-name显示 模糊搜索编号/名称  必填
    fieldName: i18n.t('工厂编码'),
    allowEditing: false
    // siteId
    // siteName
  },
  {
    fieldCode: 'siteName',
    fieldName: i18n.t('工厂名称'),
    allowEditing: false
  },
  {
    fieldCode: 'itemCode', // 物料 code-name 下拉框选择 主数据 code 显示，下拉显示code-name 模糊搜索编号/名称
    fieldName: i18n.t('物料编码'),
    searchOptions: {
      // ...MasterDataSelect.material,
      renameField: 'itemCode'
    },
    allowEditing: false
    // itemName
    // itemId
  },
  {
    fieldCode: 'itemName',
    fieldName: i18n.t('物料名称'),
    allowEditing: false
  },
  // {
  //   fieldCode: 'warehouseCode', // 库存地点 下拉框选择 主数据 code-name显示 模糊搜索编号/名称  必填
  //   fieldName: i18n.t('库存地点'),
  //   allowEditing: false
  //   // warehouseName
  // },
  {
    fieldCode: 'supplierCode', // 供应商 code-name 下拉框选择 供应商主数据 模糊搜索
    fieldName: i18n.t('供应商编码'),
    allowEditing: false
    // supplierId
    // supplierName
  },
  {
    fieldCode: 'supplierName',
    fieldName: i18n.t('供应商名称'),
    allowEditing: false
  },
  {
    fieldCode: 'structSeqNo',
    fieldName: i18n.t('结构序号'),
    allowEditing: false
    // itemName
    // itemId
  },
  {
    fieldCode: 'callMaterialQty', // 叫料数量 不可编辑
    fieldName: i18n.t('叫料数量'),
    searchOptions: { elementType: 'number' },
    allowEditing: false
  },
  {
    fieldCode: 'demandQty', // 需求数量 输入数字，小数位数3位 （叫料数量）
    fieldName: i18n.t('需求数量'),
    searchOptions: { elementType: 'number' },
    allowEditing: false
  },
  {
    fieldCode: 'deliveryDate', // 交货日期 选择日期-时间 必填
    fieldName: i18n.t('交货日期'),
    searchOptions: { ...MasterDataSelect.timeRange },
    allowGlobalSorting: true,
    allowEditing: false
  },
  {
    fieldCode: 'deliveryTime',
    fieldName: i18n.t('交货时间'),
    allowEditing: false
  },
  {
    fieldCode: 'deliverAddr', // 收货信息 下拉框选择 交货地址配置表 必填
    fieldName: i18n.t('收货地址'),
    allowEditing: false
    // senderAddress 送货地址
    // senderName 送货联系人
    // senderPhone 送货联系电话
  },
  {
    fieldCode: 'consignee', // 收货人 下拉框选择 交货地址配置表 必填
    fieldName: i18n.t('收货人'),
    allowEditing: false
  },
  {
    fieldCode: 'contactWay', // 联系方式 下拉框选择 交货地址配置表 必填
    fieldName: i18n.t('联系方式'),
    allowEditing: false
  },
  {
    fieldCode: 'isSatisfy', // 是否满足
    fieldName: i18n.t('是否满足'),
    allowEditing: false
  },
  {
    fieldCode: 'supplierRemark', // 供应商备注 不可编辑
    fieldName: i18n.t('供应商备注')
  },
  {
    fieldCode: 'dispatcherRemark', // 调度员备注 输入文本 非必填
    fieldName: i18n.t('调度员备注'),
    allowEditing: false
  },
  // {
  //   fieldCode: 'salesOrder', // 销售订单 输入文本 必填
  //   fieldName: i18n.t('销售订单'),
  //   allowEditing: false
  // },
  {
    fieldCode: 'cumulativeQuantityShipped',
    fieldName: i18n.t('已发货数量'),
    searchOptions: { elementType: 'number' },
    allowEditing: false
  },
  {
    fieldCode: 'accumulativeQuantityShipped', // 累计发货数量 不可编辑
    fieldName: i18n.t('累计发货数量'),
    searchOptions: { elementType: 'number' },
    allowEditing: false
  },
  {
    fieldCode: 'transitQty', // 在途数量 不可编辑
    fieldName: i18n.t('在途数量'),
    searchOptions: { elementType: 'number' },
    allowEditing: false
  },
  {
    fieldCode: 'receiveQty', // 收货数量 不可编辑
    fieldName: i18n.t('收货数量'),
    searchOptions: { elementType: 'number' },
    allowEditing: false
  },
  {
    fieldCode: 'cumCallMaterialQty', // 累计叫料数量 不可编辑
    fieldName: i18n.t('累计叫料数量'),
    allowEditing: false
  },
  // {
  //   fieldCode: 'cumReceiveQty', // 累计收货数量 不可编辑
  //   fieldName: i18n.t('累计收货数量'),
  //   allowEditing: false
  // },
  // {
  //   fieldCode: 'cumTransitQty', // 累积在途数量 不可编辑
  //   fieldName: i18n.t('累积在途数量'),
  //   allowEditing: false
  // },
  {
    fieldCode: 'dispatcherName', // 调度员名称 code-name 新增时-自动填入当前登录人 不可修改
    fieldName: i18n.t('调度员'),
    allowEditing: false
    // scheduleUserId
    // dispatcherName
    // scheduleUserName
  },
  {
    fieldCode: 'dispatcherCode', // 调度员编号
    fieldName: i18n.t('调度员编号'),
    allowEditing: false
  },
  {
    fieldCode: 'dispatcherContactWay', // 调度员联系方式
    fieldName: i18n.t('调度员联系方式'),
    allowEditing: false
  },
  {
    fieldCode: 'jitUpdateTime', // JIT更新时间 不可编辑
    fieldName: i18n.t('JIT更新时间'),
    searchOptions: { ...MasterDataSelect.timeRange },
    allowGlobalSorting: true,
    allowEditing: false
  },
  {
    fieldCode: 'transferPlannerName', // 转交计划员 code-name 新增时选择人员（组织架构）
    fieldName: i18n.t('转交计划员'),
    allowEditing: false
    // transferPlanId
    // transferPlanName
  },
  // {
  //   fieldCode: 'version', // 版本 不可编辑
  //   fieldName: i18n.t('版本')
  // },
  {
    fieldCode: 'jitItemNo', // JIT 行编号 不可编辑
    fieldName: i18n.t('JIT 行编号'),
    allowEditing: false
  },
  // {
  //   fieldCode: 'remainingDeliveryNum', // 剩余送货数量 不可编辑
  //   fieldName: i18n.t('剩余送货数量'),
  //   type: 'number',
  //   searchOptions: { elementType: 'number' },
  //   allowEditing: false
  // },
  {
    fieldCode: 'plannerRemark', // 计划员备注 输入文本 非必填
    fieldName: i18n.t('计划员备注'),
    allowEditing: false
  },
  {
    fieldCode: 'projectTextBatch', // 项目文本批次 输入文本 非必填
    fieldName: i18n.t('项目文本批次'),
    allowEditing: false
  },
  {
    fieldCode: 'createUserName', //
    fieldName: i18n.t('创建人'),
    allowEditing: false,
    allowFiltering: false
  },
  {
    fieldCode: 'createTime', //
    fieldName: i18n.t('创建时间'),
    searchOptions: { ...MasterDataSelect.timeRange },
    allowGlobalSorting: true
  }
]
