import { ColumnComponent as Component } from './columnComponent'
import {
  ComponentType,
  CellTools,
  StatusOptions,
  DeliveryStatusOptions,
  ReceiveStatusOptions,
  DeliveryMethodOptions,
  OutsourcedTypeOptions,
  SatisfyOptions,
  ReceiptShipmentStatusOptions
} from './constant'
import { codeNameColumn } from '@/utils/utils'

// 格式化表格动态数据
export const formatTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150'
    }
    if (col.fieldCode === 'id') {
      // 行主键
      defaultCol.visible = false
      defaultCol.isIdentity = true // 加上这个，新增一行的时候，主键不会变成可输入
      defaultCol.isPrimaryKey = true
      defaultCol.allowEditing = false
    } else if (col.fieldCode === 'isSatisfy') {
      // 发货状态 未发货-1 全部发货-2 部分发货-3
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: SatisfyOptions
        }
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.edit,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: SatisfyOptions
        }
      })
    } else if (col.fieldCode === 'checkBox') {
      // checkBox
      defaultCol.type = 'checkbox'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.showInColumnChooser = false
      defaultCol.width = '50'
      // 编辑时显示
      defaultCol.editTemplate = Component.empty
    } else if (col.fieldCode === 'serialNumber') {
      // 序号 不可编辑
      defaultCol.width = '66'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.ignore = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'batchCode') {
      // 批次号 不可编辑
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'rowCode') {
      // JIT 行编号 不可编辑
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        dataKey: col.fieldCode
      })
    } else if (
      col.fieldCode === 'jitUpdateTime' ||
      col.fieldCode === 'createTime' ||
      col.fieldCode === 'closeTime'
    ) {
      // JIT更新时间 不可编辑
      defaultCol.width = '138'
      defaultCol.allowEditing = false
      defaultCol.template = Component.timeDate({
        dataKey: col.fieldCode,
        isDateTime: true,
        isDate: false,
        isTime: false
      })
      defaultCol.editTemplate = Component.timeDate({
        dataKey: col.fieldCode,
        isDateTime: true,
        isDate: false,
        isTime: false
      })
    } else if (col.fieldCode === 'status') {
      // 状态 1-新建 2-已转交 3-待反馈 4-反馈异常 5-反馈正常 6-已关闭 不可编辑
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        cellTools: CellTools,
        valueConverter: {
          type: 'map',
          map: StatusOptions
        }
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.edit,
        dataKey: col.fieldCode,
        cellTools: CellTools,
        valueConverter: {
          type: 'map',
          map: StatusOptions
        }
      })
    } else if (col.fieldCode === 'receiptShipmentStatus') {
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        valueConverter: {
          type: 'map',
          map: ReceiptShipmentStatusOptions
        }
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.edit,
        dataKey: col.fieldCode,
        valueConverter: {
          type: 'map',
          map: ReceiptShipmentStatusOptions
        }
      })
    } else if (col.fieldCode === 'deliveryStatus') {
      // 发货状态 未发货-1 全部发货-2 部分发货-3 不可编辑
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: DeliveryStatusOptions
        }
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.edit,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: DeliveryStatusOptions
        }
      })
    } else if (col.fieldCode === 'receiveStatus') {
      // 收货状态 未收货-1 全部收货-2 部分收货-3 不可编辑
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: ReceiveStatusOptions
        }
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.edit,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: ReceiveStatusOptions
        }
      })
    } else if (col.fieldCode === 'deliveryMethod') {
      // 配送方式 不可编辑
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: DeliveryMethodOptions
        }
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: DeliveryMethodOptions
        }
      })
    } else if (col.fieldCode === 'outsourcedType') {
      // 委外方式 0标准委外 1销售委外 2非委外 必填
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: OutsourcedTypeOptions
        }
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode,
        cellTools: [],
        valueConverter: {
          type: 'map',
          map: OutsourcedTypeOptions
        }
      })
    } else if (col.fieldCode === 'productLine') {
      // 生产线 不可编辑
      defaultCol.width = '108'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'deliveryDate') {
      // 交货日期 不可编辑
      defaultCol.width = '135'
      defaultCol.allowEditing = true
      defaultCol.template = Component.timeDate({
        dataKey: 'deliveryTime',
        isDateTime: false,
        isDate: true,
        isTime: false
      })
      defaultCol.editTemplate = Component.timeDate({
        dataKey: 'deliveryTime',
        isDateTime: false,
        isDate: true,
        isTime: false
      })
    } else if (col.fieldCode === 'deliveryTime') {
      defaultCol.width = '100'
      defaultCol.template = Component.timeDate({
        dataKey: col.fieldCode,
        isDateTime: false,
        isDate: false,
        isTime: true,
        isCompare: true
      })
      defaultCol.editTemplate = Component.timeDate({
        dataKey: col.fieldCode,
        isDateTime: false,
        isDate: false,
        isTime: true,
        isCompare: true
      })
    } else if (col.fieldCode === 'supplierRemark') {
      // 供应商备注 非必填 长度 250
      defaultCol.width = '250'
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.inputText({
        dataKey: col.fieldCode,
        showClearBtn: true,
        disabled: false,
        maxlength: 250,
        isListenChange: false,
        hasSearch: false
      })
    } else if (col.fieldCode === 'planGroupRemark') {
      // 计划组备注 不可编辑
      defaultCol.width = '115'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'dispatcherRemark') {
      // 调度员备注 不可编辑
      defaultCol.width = '106'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'itemCode') {
      // 物料 code-name 不可编辑
      defaultCol.width = '100'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      // defaultCol.template = codeNameColumn({
      //   firstKey: 'itemCode',
      //   secondKey: 'itemName'
      // })
      defaultCol.editTemplate = codeNameColumn({
        firstKey: 'itemCode',
        secondKey: 'itemName'
      })
    } else if (col.fieldCode === 'siteCode') {
      // 工厂 code-name 不可编辑
      defaultCol.width = '100'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      // defaultCol.template = codeNameColumn({
      //   firstKey: 'siteCode',
      //   secondKey: 'siteName'
      // })
      defaultCol.editTemplate = codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      })
    } else if (col.fieldCode === 'warehouseCode') {
      // 库存地点 code-name 不可编辑
      defaultCol.width = '162'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'warehouseCode',
        secondKey: 'warehouseName'
      })
      defaultCol.editTemplate = codeNameColumn({
        firstKey: 'warehouseCode',
        secondKey: 'warehouseName'
      })
    } else if (col.fieldCode === 'planGroupCode') {
      // 计划组 code-name 不可编辑
      defaultCol.width = '300'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'planGroupCode',
        secondKey: 'planGroupName'
      })
      defaultCol.editTemplate = codeNameColumn({
        firstKey: 'planGroupCode',
        secondKey: 'planGroupName'
      })
    } else if (col.fieldCode === 'subSiteCode') {
      // 分厂 code-name 不可编辑
      defaultCol.width = '138'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'subSiteCode',
        secondKey: 'subSiteName'
      })
      defaultCol.editTemplate = codeNameColumn({
        firstKey: 'subSiteCode',
        secondKey: 'subSiteName'
      })
    } else if (col.fieldCode === 'subSiteAddressCode') {
      // 分厂库存地点 code-name 不可编辑
      defaultCol.width = '123'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'subSiteAddressCode',
        secondKey: 'subSiteAddress'
      })
      defaultCol.editTemplate = codeNameColumn({
        firstKey: 'subSiteAddressCode',
        secondKey: 'subSiteAddress'
      })
    } else if (col.fieldCode === 'senderAddress') {
      // 收货信息 不可编辑
      defaultCol.width = '268'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'senderName',
        secondKey: 'senderPhone',
        thirdKey: 'senderAddress'
      })
      defaultCol.editTemplate = codeNameColumn({
        firstKey: 'senderName',
        secondKey: 'senderPhone',
        thirdKey: 'senderAddress'
      })
    } else if (col.fieldCode === 'workCenterCode') {
      // 工作中心 code-name 显示 不可编辑
      defaultCol.width = '120'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      // defaultCol.template = codeNameColumn({
      //   firstKey: 'workCenterCode',
      //   secondKey: 'workCenter'
      // })
      defaultCol.editTemplate = codeNameColumn({
        firstKey: 'workCenterCode',
        secondKey: 'workCenter'
      })
    } else if (col.fieldCode === 'processorCode') {
      // 加工商 code-name 不可编辑
      defaultCol.width = '400'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'processorCode',
        secondKey: 'processorName'
      })
      defaultCol.editTemplate = codeNameColumn({
        firstKey: 'processorCode',
        secondKey: 'processorName'
      })
    } else if (col.fieldCode === 'companyCode') {
      // 公司 code-name 不可编辑
      defaultCol.allowFiltering = false
      defaultCol.width = '311'
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'companyCode',
        secondKey: 'companyName'
      })
      defaultCol.editTemplate = codeNameColumn({
        firstKey: 'companyCode',
        secondKey: 'companyName'
      })
    } else if (col.fieldCode === 'saleOrder') {
      // 销售订单 不可编辑
      defaultCol.width = '250'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'supplierCode') {
      // 供应商 code-name 不可编辑
      defaultCol.width = '120'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = true
      // defaultCol.template = codeNameColumn({
      //   firstKey: 'supplierCode',
      //   secondKey: 'supplierName'
      // })
      defaultCol.editTemplate = codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      })
    } else if (col.fieldCode === 'projectTextBatch') {
      // 项目文本批次 不可编辑
      defaultCol.width = '125'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'batch') {
      // 批量 不可编辑
      defaultCol.width = '250'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'bidNum') {
      // 需求数量（叫料数量） 不可编辑
      defaultCol.width = '92'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'receiveNum') {
      // 收货数量 不可编辑
      defaultCol.width = '95'
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'deliveryNum') {
      // 在途数量 不可编辑
      defaultCol.width = '95'
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'remainingDeliveryNum') {
      // 剩余送货数量 不可编辑
      defaultCol.width = '125'
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'totalBidNum') {
      // 累计叫料数量 不可编辑
      defaultCol.width = '250'
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'totalReceiveNum') {
      // 累计收货数量 不可编辑
      defaultCol.width = '250'
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'totalDeliveryNum') {
      // 累积在途数量 不可编辑
      defaultCol.width = '250'
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        type: ComponentType.view,
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'scheduleUserName') {
      // 调度员名称 不可编辑
      defaultCol.width = '85'
      defaultCol.allowEditing = false
      defaultCol.allowFiltering = false
      defaultCol.template = codeNameColumn({
        firstKey: 'scheduleUserCode',
        secondKey: 'scheduleUserName'
      })
      defaultCol.editTemplate = codeNameColumn({
        firstKey: 'scheduleUserCode',
        secondKey: 'scheduleUserName'
      })
    } else if (col.fieldCode === 'renewTime' || col.fieldCode === 'closeTime') {
      // JIT更新时间 不可编辑
      defaultCol.width = '138'
      defaultCol.allowEditing = false
      defaultCol.template = Component.timeDate({
        dataKey: col.fieldCode,
        isDateTime: true,
        isDate: false,
        isTime: false
      })
      defaultCol.editTemplate = Component.timeDate({
        dataKey: col.fieldCode,
        isDateTime: true,
        isDate: false,
        isTime: false
      })
    } else if (col.fieldCode === 'transferPlanCode') {
      // 转交计划员 code-name 不可编辑
      defaultCol.allowFiltering = false
      defaultCol.width = '105'
      defaultCol.allowEditing = true
      defaultCol.template = codeNameColumn({
        firstKey: 'transferPlanCode',
        secondKey: 'transferPlanName'
      })
      defaultCol.editTemplate = codeNameColumn({
        firstKey: 'transferPlanCode',
        secondKey: 'transferPlanName'
      })
    }
    colData.push(defaultCol)
  })

  return colData
}

// 表格数据序列化
export const serializeList = (list) => {
  if (list?.length > 0) {
    list.forEach((item, index) => {
      // 添加序号
      item.serialNumber = index + 1
      // item.deliveryDate = item.deliveryTime
    })
  }

  return list
}
