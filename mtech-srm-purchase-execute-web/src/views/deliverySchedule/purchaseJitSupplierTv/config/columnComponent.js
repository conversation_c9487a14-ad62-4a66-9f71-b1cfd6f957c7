import Vue from 'vue'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { ComponentChangeType, ComponentType } from './constant'
import { rowDataTemp } from './variable'
import { cloneDeep } from 'lodash'
import {} from '@/utils/utils'

export const ColumnComponent = {
  // 文本 可编辑、可监听关联、可带搜索按钮、可格式化显示
  inputText: (args) => {
    const { dataKey, showClearBtn, disabled, maxlength, isListenChange, hasSearch, format } = args
    const template = () => {
      return {
        template: Vue.component('inputComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <div :class="[hasSearch && 'input-search-content']">
                <mt-input
                  v-model="componentData"
                  autocomplete="off"
                  :show-clear-button="showClearBtn"
                  :disabled="disabled"
                  :maxlength="maxlength"
                  @input="onInput"
                ></mt-input>
                <mt-icon
                  v-if="hasSearch"
                  name="icon_input_search"
                  @click.native="handleSearch"
                ></mt-icon>
              </div>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              disabled,
              showClearBtn,
              maxlength,
              hasSearch,
              componentData: null // 组件内部绑定的变量
            }
          },
          mounted() {
            if (isListenChange) {
              // 监听变化
              this.onComponentChange()
            }
            this.doFormat()
          },
          beforeDestroy() {
            if (isListenChange) {
              // 移除监听
              this.$bus.$off('purchaseJitColumnChange')
            }
          },
          methods: {
            onInput(e) {
              if (dataKey === 'batchCode') {
                e = e.replaceAll(' ', '')
                this.componentData = e.replaceAll(' ', '')
              }
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e
            },
            // 监听变化
            onComponentChange() {
              // 监听被变化
              this.$bus.$on(`purchaseJitColumnChange`, (e) => {
                const { modifiedKeys, data, changeType } = e
                if (
                  modifiedKeys.includes(this.dataKey) &&
                  changeType === ComponentChangeType.code
                ) {
                  // 发布事件的数据修改了，关联值修改
                  const newData = cloneDeep(data[this.dataKey])
                  this.data[dataKey] = newData
                  rowDataTemp[rowDataTemp.length - 1][dataKey] = newData

                  if (this.dataKey === 'itemCode') {
                    // 物料的行字段
                    this.data.itemName = data.itemName
                    this.data.itemId = data.itemId
                    rowDataTemp[rowDataTemp.length - 1]['itemName'] = data.itemName
                    rowDataTemp[rowDataTemp.length - 1]['itemId'] = data.itemId
                  }
                  this.doFormat()
                }
              })
            },
            // 点击搜索
            handleSearch() {
              this.$parent.$emit('handleSearch', {
                dataKey,
                data: rowDataTemp[rowDataTemp.length - 1]
              })
            },
            doFormat() {
              if (format && typeof format === 'function') {
                // 格式化显示
                this.componentData = format(this.data)
              } else {
                this.componentData = this.data[dataKey]
              }
            }
          }
        })
      }
    }
    return template
  },
  // 空的显示
  empty: () => {
    return {
      template: Vue.component('emptyComponent', {
        template: `<div></div>`,
        data: function () {
          return {
            data: {}
          }
        }
      })
    }
  },
  // 不可编辑的文字显示 带 cellTools
  text: (args) => {
    const { dataKey, type, cellTools, valueConverter } = args
    const template = () => {
      return {
        template: Vue.component('textComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content"><span :class=[cssClass]>{{data[dataKey] | format}}</span></div>
            <div class="column-tool mt-flex invite-btn" v-if="haveShowCellTool && type === ComponentType.view">
              <div v-for="(cellTool, index) in cellTools"
                :key="index" :id="cellTool.id"
                class="template-svg"
                v-if="!cellTool.visibleCondition || cellTool.visibleCondition(data)"
                v-permission="cellTool.permission"
                @click.stop="clickCellTool({tool:cellTool, data})"
                ><MtIcon :name="cellTool.icon" />&nbsp<span class="icon-title">{{cellTool.title}}</span>
              </div>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              type,
              ComponentType,
              cellTools,
              haveShowCellTool: false,
              cssClass: ''
            }
          },
          mounted() {
            // 判断是否显示cellTool
            if (cellTools?.length > 0) {
              for (let i = 0; i < cellTools.length; i++) {
                const cellTool = cellTools[i]
                if (!cellTool.visibleCondition || cellTool.visibleCondition(this.data)) {
                  this.haveShowCellTool = true
                  break
                }
              }
            }
            // 设置 cssClass
            if (valueConverter?.type === 'map' && valueConverter?.map?.length) {
              const mapList = valueConverter.map
              const findItem = mapList.find((item) => item.value === this.data[dataKey])
              this.cssClass = findItem?.cssClass
            }
          },
          filters: {
            format: (value) => {
              let data = value
              if (valueConverter?.type === 'map' && valueConverter?.map?.length) {
                // 转换
                const mapList = valueConverter.map
                const findItem = mapList.find((item) => String(item.value) === String(value))
                data = findItem?.text
              }
              return data
            }
          },
          methods: {
            clickCellTool(data) {
              this.$parent.$emit('handleClickCellTool', data)
            }
          }
        })
      }
    }
    return template
  },
  // 时间显示
  timeDate: (args) => {
    const { dataKey, isDateTime, isDate, isTime, isCompare } = args
    let that = null
    const template = () => {
      return {
        template: Vue.component('timeDateComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <span v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</span>
              <span v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</span>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              isDateTime,
              isDate,
              isTime
            }
          },
          created() {
            that = this
          },
          filters: {
            dateFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({
                  formatString: 'YYYY-mm-dd',
                  value
                })
              } else {
                str = timeNumberToDate({
                  formatString: 'YYYY-mm-dd',
                  value
                })
              }

              return str
            },
            timeFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({
                  formatString: 'HH:MM:SS',
                  value
                })
              } else {
                str = timeNumberToDate({
                  formatString: 'HH:MM:SS',
                  value
                })
              }
              if (isCompare && str === '00:00:00' && value === that.data.deliveryDate) {
                str = ''
              }

              return str
            }
          }
        })
      }
    }

    return template
  },
  // 带红星的表头
  requiredHeader: (args) => {
    const { headerText } = args
    const template = () => {
      return {
        template: Vue.component('requiredHeaderComponent', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{ headerText }}</span>
              </div>
            `,
          data: function () {
            return {
              data: {},
              headerText
            }
          },
          beforeDestroy() {},
          mounted() {},
          methods: {}
        })
      }
    }
    return template
  }
}
