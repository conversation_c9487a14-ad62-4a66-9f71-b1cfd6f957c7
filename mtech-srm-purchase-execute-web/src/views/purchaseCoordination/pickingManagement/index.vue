// VMI领料管理-采方
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :current-tab="currentTab"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnObj } from './config/index.js'
export default {
  components: {
    // addDialog: require("./components/addDialog.vue").default,
  },
  data() {
    return {
      currentTab: 0, //代表当前默认加载显示的Tab索引
      pageConfig: [
        {
          tab: { title: this.$t('头视图') },
          useBaseConfig: false, // 使用组件中的toolbar配置
          useToolTemplate: false,
          gridId: 'cd06cc3c-58a2-48ef-a0d7-5e0ec4883f40',
          toolbar: {
            tools: [
              [
                {
                  id: 'add',
                  icon: 'icon_solid_Createorder',
                  permission: ['O_02_1085'],
                  title: this.$t('创建VMI领料单')
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete',
                  permission: ['O_02_1086'],
                  title: this.$t('删除VMI领料单')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            // allowGrouping: true, // 拖动表格列置此以聚焦
            columnData: columnObj.headColumn,
            // dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmi-pickup-order/buyer-page-query'
              // recordsPosition: "data",
            },
            frozenColumns: 1
          }
        },
        {
          tab: { title: this.$t('明细视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          toolbar: {
            tools: [
              [
                // {
                //   id: "add",
                //   icon: "icon_solid_Createorder",
                //   title: this.$t("创建VMI领料单"),
                // },
                // {
                //   id: "delete",
                //   icon: "icon_solid_Delete",
                //   title: this.$t("删除VMI领料单"),
                // },
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData: columnObj.detailedColumn,
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmi-pickup-order/buyer-item-page-query'
              // recordsPosition: "data",
            },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    // 跳转详情
    handleClickCellTitle(e) {
      let obj = {
        id: '',
        details: 1
      }
      if (e.field === 'vmiOrderCode') {
        obj.id = e.data.vmiOrderId ?? e.data.id
        this.redirectPage('purchase-execute/coordination-picking-establish', obj)
      }
    },
    // 头部跳转
    handleClickToolBar(e) {
      if (e.toolbar.id === 'add') {
        // VMI领料单-采方-创建VMI领料单-跳转到新建页面
        this.redirectPage('purchase-execute/coordination-picking-establish', {})
      } else if (e.toolbar.id == 'Delete') {
        // VMI领料单-采方-批量删除
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('删除'),
            message: this.$t('确认删除吗？')
          },
          success: () => {
            let ids = _selectRows.map((item) => item.id)
            this.$API.purchaseCoordination.VMIPickupOrderDelete({ ids: ids }).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('删除成功'),
                  type: 'success'
                })
                // 刷新当前 Grid
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          }
        })
      } else {
        // 调用刷新方法
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'submit') {
        this.$dialog({
          data: {
            title: this.$t('提交'),
            message: this.$t('确认提交吗？')
          },
          success: () => {
            this.$API.purchaseCoordination
              .VMIPickupOrderFastSubmit({ id: e.data.id })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('提交成功'),
                    type: 'success'
                  })
                  // 刷新当前 Grid
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
          }
        })
      } else if (e.tool.id == 'cancel') {
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消吗？')
          },
          success: () => {
            this.$API.purchaseCoordination.VMIPickupOrderCancel({ id: e.data.id }).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('取消成功'),
                  type: 'success'
                })
                // 刷新当前 Grid
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          }
        })
      }
    },
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
