import { i18n } from '@/main.js'

// VMI库存行（弹窗）
export const stockLineColumns = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'siteCode',
    headerText: i18n.t('工厂编号')
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('工厂描述')
  },
  {
    width: '170',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '170',
    field: 'supplierName',
    headerText: i18n.t('供应商描述')
  },
  {
    width: '150',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓编码')
  },

  {
    width: '150',
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓描述')
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },
  {
    width: '150',
    field: 'purchaseGroupName',
    headerText: i18n.t('采购组')
  },
  {
    width: '150',
    field: 'stockStatus',
    headerText: i18n.t('库存状态')
  },
  // {
  //   width: "150",
  //   field: "batchCode",
  //   headerText: i18n.t("批次/卷号"),
  // },
  {
    width: '150',
    field: 'count',
    headerText: i18n.t('库存数量')
  },
  {
    width: '150',
    field: 'createUser',
    headerText: i18n.t('VMI仓创建人')
  }
]
