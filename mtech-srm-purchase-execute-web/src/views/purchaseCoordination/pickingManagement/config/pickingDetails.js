import { i18n } from '@/main.js'
import Vue from 'vue'
import $utils from '@/utils/utils'
// VMI配置管理
export const headColumnCreate = (that, status) => {
  return [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '100',
      field: 'rowNum',
      headerText: i18n.t('行号')
    },
    {
      width: '150',
      field: 'itemCode',
      headerText: i18n.t('物料编码')
    },
    {
      width: '150',
      field: 'itemName',
      headerText: i18n.t('物料名称')
    },
    // {
    //   width: "150",
    //   field: "stockType",
    //   headerText: i18n.t("库存状态"),
    //   valueConverter: {
    //     type: "map",
    //     map: { 0: i18n.t("合格库存"), 1: i18n.t("待检库存"), 2: "不合格库存"},
    //   },
    // },
    // {
    //   width: "150",
    //   field: "batchCode",
    //   headerText: i18n.t("批次/卷号"),
    // },
    {
      width: '150',
      field: 'countLimit',
      headerText: i18n.t('可领料数量')
    },
    {
      width: '150',
      field: 'checkCount',
      headerText: i18n.t('领料数量'),
      template: () => {
        return {
          template: Vue.component('checkCount', {
            template: `<mt-input-number
                        v-if="statusCreate"
                        v-model="data.checkCount"
                        :min="1"
                        :max="data.countLimit"
                        cssClass="e-outline"
                        :show-clear-button="false"
                        @change="handleChange"
                        :placeholder="$t('请输入领料数量')">
                      </mt-input-number>
            <span v-else>{{data.checkCount}}</span>`,
            data() {
              return { data: {} }
            },
            computed: {
              statusCreate() {
                return status == 0
              }
            },
            methods: {
              handleChange(e) {
                // if (e >= 0) {
                //   e = 1;
                // }
                that.$set(that.formObject.itemList[Number(this.data.index)], 'checkCount', e)
              }
            }
          })
        }
      }
    },
    {
      width: '150',
      field: 'requireDeliveryDate',
      headerText: i18n.t('要求交货日期'),
      template: () => {
        return {
          template: Vue.component('requireDeliveryDate', {
            template: `<mt-date-picker
                        v-model="data.requireDeliveryDate"
                        cssClass="e-outline"
                        :show-clear-button="false"
                        @change="handleChange"
                        placeholder="请输入领料数量">
                      </mt-date-picker>`,
            data() {
              return { data: {} }
            },
            mounted() {
              this.data.requireDeliveryDate = $utils.formateTime(
                new Date(+this.data.requireDeliveryDate),
                'YYYY-mm-dd'
              )
            },
            methods: {
              handleChange(e) {
                that.$set(
                  that.formObject.itemList[Number(this.data.index)],
                  'requireDeliveryDate',
                  e
                )
              }
            }
          })
        }
      }
    },
    {
      width: '150',
      field: 'itemUnit',
      headerText: i18n.t('单位编码')
    },
    {
      width: '150',
      field: 'unitName',
      headerText: i18n.t('单位名称')
    },
    {
      width: '150',
      field: 'purchaseGroupCode',
      headerText: i18n.t('采购组')
    },
    {
      width: '150',
      field: 'purchaseGroupName',
      headerText: i18n.t('采购组编码')
    },
    {
      width: '200',
      field: 'remark',
      headerText: i18n.t('行备注'),
      template: () => {
        return {
          template: Vue.component('remark', {
            template: `<mt-input v-if="statusCreate" v-model="data.remark" @change="handleChange" type="text" :placeholder="$t('请输入备注')"></mt-input>
            <span v-else>{{data.remark}}</span>`,
            data() {
              return { data: {} }
            },
            computed: {
              statusCreate() {
                return status == 0
              }
            },
            methods: {
              handleChange(e) {
                that.$set(that.formObject.itemList[Number(this.data.index)], 'remark', e)
              }
            }
          })
        }
      }
    }
    // {
    //   width: "170",
    //   field: "orderCode",
    //   headerText: i18n.t("关联采购订单号"),
    //   template: () => {
    //     return {
    //       template: Vue.component("orderCode", {
    //         template: `<mt-select :dataSource="data.orderCodeArr" v-model="data.orderCode" @change="handleChange" type="text" :placeholder="$t('请选择订单号')"></mt-select>`,
    //         data() {
    //           return { data: {} };
    //         },
    //         mounted() {
    //           if (this.data.orderCodeArr && this.data.orderCodeArr.length === 0)
    //             this.purOrderQueryOrder();
    //         },
    //         computed: {},
    //         methods: {
    //           handleChange(e) {
    //             that.$set(
    //               that.formObject.itemList[Number(this.data.index)],
    //               "orderCode",
    //               e.value
    //             );
    //             this.selectChange(e.value);
    //           },
    //           // 请求全部数据
    //           purOrderQueryOrder(value) {
    //             console.log(value, "-=");
    //             let params = {
    //               condition: "and",
    //               page: {
    //                 current: 1,
    //                 size: 50,
    //               },
    //               defaultRules: [
    //                 {
    //                   label: i18n.t("采购订单号"),
    //                   field: "orderCode",
    //                   type: "string",
    //                   operator: "contains",
    //                   value: (value && value.text) || "",
    //                 },
    //               ],
    //             };
    //             that.$API.purchaseCoordination
    //               .purOrderQueryOrder(params)
    //               .then((res) => {
    //                 let orderOptions = res.data.records
    //                   .map((item) => ({
    //                     text: item,
    //                     value: item,
    //                   }))
    //                   .filter((item) => item.value); //过滤一下空值
    //                 this.data.orderCodeArr = orderOptions;
    //               });
    //           },
    //           //  请求订单行号
    //           selectChange(value) {
    //             const code = value;
    //             if (code) {
    //               //请求采购订单行
    //               that.$API.purchaseCoordination
    //                 .getByOrder({ code })
    //                 .then((res) => {
    //                   if (res.data) {
    //                     let arrList = "";
    //                     arrList = res.data.reduce((pre, now) => {
    //                       pre.push({
    //                         text: now.itemNo,
    //                         value: now.itemNo,
    //                         ...now,
    //                       });
    //                       return pre;
    //                     }, []);
    //                     this.$bus.$emit("getOrderLineData", arrList);
    //                   }
    //                 });
    //             }
    //           },
    //         },
    //       }),
    //     };
    //   },
    // },
    // {
    //   width: "170",
    //   field: "lineNo",
    //   headerText: i18n.t("关联采购订单行号"),
    //   template: () => {
    //     return {
    //       template: Vue.component("lineNo", {
    //         template: `<mt-select :dataSource="data.lineNoArr" v-model="data.lineNo" @change="handleChange" type="text" :placeholder="$t('请选择订单号')"></mt-select>`,
    //         data() {
    //           return { data: {} };
    //         },
    //         computed: {},
    //         mounted() {
    //           this.$bus.$on("getOrderLineData", (data) => {
    //             if (data && data.length > 0) {
    //               this.data.lineNoArr = data;
    //             }
    //           });
    //         },
    //         methods: {
    //           handleChange(e) {
    //             // unitCode purchaseGroupCode根据库存行带出
    //             // that.$set(that.formObject.itemList[Number(this.data.index)], "itemUnit", e.itemData.unitCode);
    //             // that.$set(that.formObject.itemList[Number(this.data.index)], "unitName", e.itemData.unitName);
    //             // that.$set(that.formObject.itemList[Number(this.data.index)], "purchaseGroupCode", e.itemData.buyerOrgCode);
    //             // that.$set(that.formObject.itemList[Number(this.data.index)], "purchaseGroupName", e.itemData.buyerOrgName);
    //             that.$set(
    //               that.formObject.itemList[Number(this.data.index)],
    //               "lineNo",
    //               e.value
    //             );
    //           },
    //         },
    //       }),
    //     };
    //   },
    // },
  ]
}

export const headColumnDetail = (that, status) => {
  return [
    // {
    //   width: "50",
    //   type: "checkbox",
    //   showInColumnChooser: false,
    // },
    {
      width: '100',
      field: 'rowNum',
      headerText: i18n.t('行号')
    },
    {
      width: '150',
      field: 'itemCode',
      headerText: i18n.t('物料编码')
    },
    {
      width: '150',
      field: 'itemName',
      headerText: i18n.t('物料描述')
    },
    // {
    //   width: "150",
    //   field: "stockType",
    //   headerText: i18n.t("库存状态"),
    //   valueConverter: {
    //     type: "map",
    //     map: { 0: i18n.t("合格库存"), 1: i18n.t("待检库存"), 2: "不合格库存"},
    //   },
    // },
    // {
    //   width: "150",
    //   field: "batchCode",
    //   headerText: i18n.t("批次/卷号"),
    // },
    {
      width: '150',
      field: 'countLimit',
      headerText: i18n.t('可领料数量')
    },
    {
      width: '150',
      field: 'checkCount',
      headerText: i18n.t('领料数量'),
      template: () => {
        return {
          template: Vue.component('checkCount', {
            template: `<mt-input-number
                        v-if="statusCreate"
                        v-model="data.checkCount"
                        :min="1"
                        :max="data.countLimit"
                        cssClass="e-outline"
                        :show-clear-button="false"
                        @change="handleChange"
                        :placeholder="$t('请输入领料数量')">
                      </mt-input-number>
            <span v-else>{{data.checkCount}}</span>`,
            data() {
              return { data: {} }
            },
            computed: {
              statusCreate() {
                return status == 0
              }
            },
            methods: {
              handleChange(e) {
                that.$set(that.formObject.itemList[Number(this.data.index)], 'checkCount', e)
              }
            }
          })
        }
      }
    },
    {
      width: '150',
      field: 'itemUnit',
      headerText: i18n.t('单位')
    },
    {
      width: '150',
      field: 'purchaseGroupName',
      headerText: i18n.t('采购组')
    },
    {
      width: '200',
      field: 'remark',
      headerText: i18n.t('行备注'),
      template: () => {
        return {
          template: Vue.component('remark', {
            template: `<mt-input v-if="statusCreate" v-model="data.remark" @change="handleChange" type="text" :placeholder="$t('请输入备注')"></mt-input>
            <span v-else>{{data.remark}}</span>`,
            data() {
              return { data: {} }
            },
            computed: {
              statusCreate() {
                return status == 0
              }
            },
            methods: {
              handleChange(e) {
                that.$set(that.formObject.itemList[Number(this.data.index)], 'remark', e)
              }
            }
          })
        }
      }
    }
    // {
    //   width: "170",
    //   field: "orderCode",
    //   headerText: i18n.t("关联采购订单号"),
    // },
    // {
    //   width: "170",
    //   field: "lineNo",
    //   headerText: i18n.t("关联采购订单行号"),
    // },
  ]
}

// 领料-采方-创建-选择VMI库存行
// let params =  qualificationColumns[0].type === "checkbox"?
//                  qualificationColumns.unshift({width: "50",  type: "checkbox",}):
//                  qualificationColumns.shift({width: "50",  type: "checkbox",})
export const pickingDetailsPageConfigCreate = (this_, status, dataSource = []) => [
  {
    tab: { title: this_.$t('物料信息') },
    useBaseConfig: false, // 使用组件中的toolbar配置
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    toolbar: {
      // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [
        [
          {
            id: 'add',
            icon: 'icon_solid_Createorder',
            title: this_.$t('选择VMI库存行')
          },
          { id: 'delete', icon: 'icon_solid_Delete', title: this_.$t('删除') }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    gridId: '5a10ee74-bf2b-4ddf-b319-4c1961753f6d',
    grid: {
      allowPaging: false, // 单据详情页不需要分页
      columnData: headColumnCreate(this_, status),
      // dataSource: [
      //   {
      //     companyCode: "630847",
      //     companyName: "TCL空调器有限公司生产工厂",
      //     warehousingCode: "w20211227001",
      //     warehouseCode: "0100001",
      //     warehouseDescribe: "VMI红物流芜湖威灵电机",
      //     WarehouseAddress: i18n.t("深圳市福田区下梅林梅华路"),
      //     WarehouseType: i18n.t("随着后勤"),
      //     isPassword: i18n.t("是"),
      //     isScreening: i18n.t("是"),
      //     stockStatus: i18n.t("待检库存"),
      //     createPeople: "jcs",
      //     createTime: "2021-12-28",
      //     isEffective: i18n.t("是"),
      //   },
      // ],
      dataSource: dataSource,
      // asyncConfig: {
      //   url: "/srm-purchase-execute/tenant/vmi-pickup-order/detail",
      //   recordsPosition: "data.records.vmiOrderItemResponses",
      // },
      frozenColumns: 1
    }
  }
]

// 领料-详情
export const pickingDetailsPageConfigDetail = (this_, status, dataSource = []) => [
  {
    tab: { title: this_.$t('物料信息') },
    useBaseConfig: false, // 使用组件中的toolbar配置
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    toolbar: {
      // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [[], ['Filter', 'Refresh', 'Setting']]
    },
    gridId: 'b1581ae0-3a42-47bc-8eaa-748f9a741a60',
    grid: {
      allowPaging: false, // 单据详情页不需要分页
      columnData: headColumnDetail(this_, status),
      // dataSource: [
      //   {
      //     companyCode: "630847",
      //     companyName: "TCL空调器有限公司生产工厂",
      //     warehousingCode: "w20211227001",
      //     warehouseCode: "0100001",
      //     warehouseDescribe: "VMI红物流芜湖威灵电机",
      //     WarehouseAddress: i18n.t("深圳市福田区下梅林梅华路"),
      //     WarehouseType: i18n.t("随着后勤"),
      //     isPassword: i18n.t("是"),
      //     isScreening: i18n.t("是"),
      //     stockStatus: i18n.t("待检库存"),
      //     createPeople: "jcs",
      //     createTime: "2021-12-28",
      //     isEffective: i18n.t("是"),
      //   },
      // ],
      dataSource: dataSource,
      // asyncConfig: {
      //   url: "/srm-purchase-execute/tenant/vmi-pickup-order/detail",
      //   recordsPosition: "data.records.vmiOrderItemResponses",
      // },
      frozenColumns: 1
    }
  }
]

// 领料-供方-确认后-打印
export const pickingDetailsPageConfigPrint = (this_, status, dataSource = []) => [
  {
    tab: { title: this_.$t('物料信息') },
    useBaseConfig: false, // 使用组件中的toolbar配置
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    toolbar: {
      // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [
        [
          {
            id: 'print',
            icon: 'icon_solid_Createorder',
            title: this_.$t('打印')
          }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    grid: {
      allowPaging: false, // 单据详情页不需要分页
      columnData: headColumnDetail(this_, status),
      // dataSource: [
      //   {
      //     companyCode: "630847",
      //     companyName: "TCL空调器有限公司生产工厂",
      //     warehousingCode: "w20211227001",
      //     warehouseCode: "0100001",
      //     warehouseDescribe: "VMI红物流芜湖威灵电机",
      //     WarehouseAddress: i18n.t("深圳市福田区下梅林梅华路"),
      //     WarehouseType: i18n.t("随着后勤"),
      //     isPassword: i18n.t("是"),
      //     isScreening: i18n.t("是"),
      //     stockStatus: i18n.t("待检库存"),
      //     createPeople: "jcs",
      //     createTime: "2021-12-28",
      //     isEffective: i18n.t("是"),
      //   },
      // ],
      dataSource: dataSource,
      // asyncConfig: {
      //   url: "/srm-purchase-execute/tenant/vmi-pickup-order/detail",
      //   recordsPosition: "data.records.vmiOrderItemResponses",
      // },
      frozenColumns: 1
    }
  }
]
