import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'factoryCode',
    headerText: i18n.t('工厂编码'),
    cssClass: ''
    // cellTools: [
    //   {
    //     id: "config",
    //     icon: "icon_solid_Disable1",
    //     title: i18n.t("配置"),
    //     permission: ["O_02_0088"],
    //   },
    // ],
  },
  {
    width: '150',
    field: 'factoryName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '150',
    field: 'warehouseCode',
    headerText: i18n.t('VMI仓编码')
  },
  {
    width: '150',
    field: 'warehouseName',
    headerText: i18n.t('VMI仓名称')
  },
  {
    width: '150',
    field: 'materialCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',
    field: 'materialName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'purchaseGroup',
    headerText: i18n.t('采购组')
  },
  {
    width: '150',
    field: 'stockStatus',
    headerText: i18n.t('库存状态')
  },
  // {
  //   width: "150",
  //   field: "batchNumber",
  //   headerText: i18n.t("批次/卷号"),
  // },
  {
    width: '150',
    field: 'stockNumber',
    headerText: i18n.t('库存数量')
  },
  {
    width: '150',
    field: 'founder',
    headerText: i18n.t('VMI仓创建人')
  }
  // {
  //   // width: "150",
  //   field: "enableStatus",
  //   headerText: i18n.t("启用状态"),
  //   valueConverter: {
  //     type: "map",
  //     // map: { 0: i18n.t("草稿"), 1: i18n.t("启用"), 2: "停用" }
  //     map: [
  //       { value: 0, text: i18n.t("草稿"), cssClass: "col-active" },
  //       { value: 1, text: i18n.t("启用"), cssClass: "col-active" },
  //       { value: 1, text: i18n.t("停用"), cssClass: "col-inactive" },
  //     ],
  //   },
  // },
]
