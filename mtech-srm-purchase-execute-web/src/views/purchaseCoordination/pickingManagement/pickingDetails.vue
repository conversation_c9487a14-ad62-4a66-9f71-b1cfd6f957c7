<template>
  <!-- VMI领料管理，创建领料单详情页 -->
  <div class="full-height pt20">
    <!-- 头部信息 -->
    <top-info
      ref="topInfo"
      class="flex-keep"
      :header-info.sync="formObject"
      @saveBtn="saveBtn"
      @submitBtn="submitBtn"
      @goBack="goBack"
      @doExpand="doExpand"
      v-if="isInfo"
    ></top-info>
    <mt-template-page
      ref="templateRef"
      :current-tab="0"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import {
  pickingDetailsPageConfigCreate,
  pickingDetailsPageConfigDetail
} from './config/pickingDetails.js'
export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue')
  },
  data() {
    return {
      pageConfig: [],
      isEdit: false, // 是 新建 || 详情
      formObject: {
        status: 1, // status状态：0新建（待提交） 1已提交/待确认 2已接收/待质检 8已完成 9已取消
        vmiOrderCode: 'w20211227001', // VMI领料单号
        createUserName: 'jcs', // 制单人
        createTime: '2022-03-02', // 制单时间
        siteCode: '', // 工厂编码
        siteName: '',
        supplierCode: '', // 原材料供应商编码
        vmiWarehouseCode: '', // VMI仓编码
        vmiWarehouseName: '',
        processorCode: null, // 领料供应商编码（加工商）
        processorName: '', // 领料供应商名称（加工商）
        vmiWarehouseAddress: '', // 送货地址
        remark: '', // 备注
        itemList: [], // 创建VMI领料单请求明细参数
        statusCreate: false //判断是不是编辑页面
      },
      isInfo: null
    }
  },
  mounted() {
    // 新建 || 详情
    if (this.$route.query.details != 1) {
      // 渲染顶部和操作按钮toolbar
      this.renderUI(0)
      this.isInfo = true
    } else {
      this.formObject.statusCreate = false
      this.getFormDetail(this.$route.query.id)
    }
  },
  methods: {
    //详情==接口
    getFormDetail(id) {
      this.$API.purchaseCoordination
        .VMIPickupOrderDetailQuery({
          id: id
        })
        .then((res) => {
          this.formObject = { ...res.data }
          this.formObject.itemList = JSON.parse(
            JSON.stringify(this.formObject.vmiOrderItemResponses)
          )
          delete this.formObject.vmiOrderItemResponses
          this.isInfo = true
          // status状态：0新建（待提交） 1已提交/待确认 2已接收/待质检 8已完成 9已取消
          if (this.formObject.status == 0) {
            // 采方-状态是"新建（待提交）"
            // 渲染顶部和操作按钮toolbar
            this.renderUI(this.formObject.status)
          } else if (this.formObject.status == 2) {
            this.pageConfig = pickingDetailsPageConfigDetail(
              this,
              this.formObject.status,
              this.formObject.itemList
            )
          } else {
            // 渲染顶部和操作按钮toolbar
            this.renderUI(this.formObject.status)
          }
        })
        .catch(() => {
          // 渲染顶部和操作按钮toolbar
          // this.renderUI(0);
        })
    },
    // 渲染顶部和操作按钮toolbar
    renderUI(status) {
      // status状态：0新建（待提交） 1已提交/待确认 2已接收/待质检 8已完成 9已取消
      if (status == 0) {
        this.formObject.statusCreate = true
        this.pageConfig = pickingDetailsPageConfigCreate(this, status, this.formObject.itemList)
      } else {
        this.formObject.statusCreate = false
        this.pageConfig = pickingDetailsPageConfigDetail(this, status, this.formObject.itemList)
      }
    },
    // 跳转详情
    handleClickCellTitle() {},
    // 表格头部操作
    handleClickToolBar(e) {
      let fieldData = {
        siteCode: this.formObject.siteCode,
        supplierCode: this.formObject.supplierCode,
        vmiWarehouseCode: this.formObject.vmiWarehouseCode
      }
      let _selectRecords = e.grid.getSelectedRecords()
      if (e.toolbar.id === 'add') {
        this.$refs.topInfo.$refs.ruleForm.validate((valid) => {
          if (valid) {
            // 选择VMI库存行
            this.$dialog({
              modal: () => import('./components/stockLineDialog.vue'),
              data: {
                title: this.$t('选择VMI库存行'),
                info: this.formObject.itemList,
                fieldObj: fieldData
              },
              success: (data) => {
                this.formObject.itemList = data
                this.$set(this.pageConfig[0].grid, 'dataSource', this.formObject.itemList)
              }
            })
          }
        })
      } else if (e.toolbar.id == 'delete') {
        if (_selectRecords.length == 0)
          return this.$toast({
            content: this.$t('请先选择一行'),
            type: 'warning'
          })
        let itemCode = _selectRecords.map((i) => {
          return i.itemCode
        })
        itemCode.forEach((i) => {
          //删除本地。
          this.formObject.itemList.forEach((e, eIdx) => {
            if (i == e.itemCode) {
              this.formObject.itemList.splice(eIdx, 1)
            }
          })
        })
      } else if (e.toolbar.id == 'print') {
        // TODO: 领料-供方-确认后-打印
      } else {
        // 调用刷新方法
      }
    },
    // 行内操作
    handleClickCellTool() {},
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    // 展开收起的hearder
    doExpand() {
      this.$refs.templateRef?.resetGridHeight && this.$refs.templateRef.resetGridHeight()
    },
    // 保存
    saveBtn() {
      let _this = this
      // 提交时校验必填项
      this.$refs.topInfo.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (_this.formObject.itemList.length == 0) {
            return _this.$toast({
              content: _this.$t('请先选择VMI库存行'),
              type: 'warning'
            })
          }
          if (_this.formObject.itemList.some((item) => !item.checkCount)) {
            return _this.$toast({
              content: _this.$t('请输入领料数量'),
              type: 'warning'
            })
          }
          _this.VMIPickupOrderCreateInterface(true)
        }
      })
    },
    // 提交
    submitBtn() {
      let _this = this
      // 提交时校验必填项
      this.$refs.topInfo.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (_this.formObject.itemList.length == 0) {
            return _this.$toast({
              content: _this.$t('请先选择VMI库存行'),
              type: 'warning'
            })
          }
          if (_this.formObject.itemList.some((item) => !item.checkCount)) {
            return _this.$toast({
              content: _this.$t('请输入领料数量'),
              type: 'warning'
            })
          }
          _this.VMIPickupOrderCreateInterface(false)
        }
      })
    },
    // 保存||提交==接口
    VMIPickupOrderCreateInterface(isSave) {
      // TODO: 领料-采方-创建-缺参数“单位编码”、“计划组code”
      let itemList = this.formObject.itemList.map((item) => {
        let newItem = {
          // rowNum: item.rowNum, // 行号
          // id: item.id, // ID√
          // siteCode: item.siteCode, // 工厂编码
          // siteName: item.siteName, // 工厂名称
          // supplierCode: item.supplierCode, // 供应商编码
          // supplierName: item.supplierName, // 供应商名称
          // vmiWarehouseCode: item.vmiWarehouseCode, // VMI仓编码
          // vmiWarehouseName: item.vmiWarehouseName, // VMI仓名称
          itemCode: item.itemCode, // 物料编码√
          // itemName: item.itemName, // 物料名称
          itemUnit: item.itemUnit, // 单位编码√
          planGroupCode: item.planGroupCode, // 计划组code√
          purchaseGroupCode: item.purchaseGroupCode, // 采购组code√
          // stockType: 0, // 库存状态（固定死0合格库存）
          batchCode: item.batchCode, // 批次/编号√
          checkCount: item.checkCount, // 实际领料数量√（自填）
          requireDeliveryDate: new Date(item.requireDeliveryDate).valueOf(),
          count: item.count, // 库存数量√
          countLimit: item.countLimit, // 可领料数量√
          lineNo: item.lineNo, // 采购订单行号√
          orderCode: item.orderCode // 采购订单号√
          // createUser: item.createUser, // VMI仓创建人（创建时才有）
        }
        return newItem
      })

      let params = {
        siteCode: this.formObject.siteCode, // 工厂编码
        supplierCode: this.formObject.supplierCode, // 原材料供应商编码
        vmiWarehouseCode: this.formObject.vmiWarehouseCode, // VMI仓编码
        processorCode: this.formObject.processorCode, // 领料供应商编码（加工商）
        vmiWarehouseAddress: this.formObject.vmiWarehouseAddress, // 送货地址
        addressCode: this.formObject.consigneeAddressCode,
        addressContactNumber: this.formObject.consigneePhone,
        addressContactPerson: this.formObject.consigneeName,
        addressId: this.formObject.addressId,
        remark: this.formObject.remark, // 备注
        itemList: itemList // 创建VMI领料单请求明细参数
      }
      if (this.$route.query.id) {
        params['id'] = this.$route.query.id
      }
      params['operationType'] = isSave ? 1 : 2 // 操作类型，1-保存/2-提交

      this.$API.purchaseCoordination.VMIPickupOrderCreate(params).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: isSave ? this.$t('保存成功') : this.$t('提交成功'),
            type: 'success'
          })
          // delete this.formObject.statusCreate;
          // if (this.formObject.status == 0) {
          //   // 新建=>跳转详情页
          //   this.$router.replace({
          //     path: "purchase-execute/coordination-picking-details",
          //     query: {
          //       id: this.$route.query.id
          //     }
          //   });
          // } else {
          //   // 详情=>刷新
          //   this.getFormDetail(this.$route.query.id);
          // }
          this.goBack()
        } else {
          this.$toast({ content: this.$t('提交失败'), type: 'error' })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .flex-keep {
  margin-top: 10px;
}
.set-country {
  height: 100%;
}
</style>
