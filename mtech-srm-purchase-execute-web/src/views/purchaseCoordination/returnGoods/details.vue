// 供方退货管理
<template>
  <div class="full-height pt20">
    <!-- 头部信息 -->
    <top-info
      class="flex-keep"
      :header-info="headerInfo"
      @goBack="goBack"
      @doExpand="doExpand"
      @squareAccept="squareAccept"
      @squareReturn="squareReturn"
    ></top-info>
    <mt-template-page
      ref="templateRef"
      :current-tab="0"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { headColumn } from './config/details.js'
export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue')
  },
  data() {
    const { vmiWarehouseType } = this.$route.query
    return {
      isEditStatus: false, // 正处于编辑状态
      pageConfig: [
        {
          tab: { title: this.$t('物料信息') },
          useBaseConfig: false, // 使用组件中的toolbar配置
          useToolTemplate: false,
          toolbar: [],
          grid: {
            columnData: headColumn(vmiWarehouseType),
            allowPaging: false,
            editSettings: {
              allowEditing: true, // 待确认时可以编辑
              allowAdding: true,
              allowDeleting: true
            },
            dataSource: [],
            frozenColumns: 1
          }
        }
      ],
      headerInfo: {} //头部信息
    }
  },
  mounted() {
    this.getDetailData()
  },
  methods: {
    // 跳转详情
    handleClickCellTitle() {},
    // 表格头部操作
    handleClickToolBar() {},
    // 行内操作
    handleClickCellTool() {},
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    // 展开收起的hearder
    doExpand() {
      this.$refs.templateRef?.resetGridHeight && this.$refs.templateRef.resetGridHeight()
    },
    // 采方接收
    squareAccept() {
      this.$dialog({
        data: {
          title: this.$t('采方接收'),
          message: this.$t('是否确认接收')
        },
        success: () => {
          const currentRecords = this.$refs.templateRef
            .getCurrentUsefulRef()
            .gridRef.ejsRef.getCurrentViewRecords()
          const onOff = currentRecords.every((item) => item.checkCount > 0)
          const itemList = currentRecords.map((item) => {
            return {
              id: item.id,
              checkCount: item.checkCount
            }
          })
          // 数量为0的要提示
          if (onOff) {
            let params = {
              id: this.$route.query.id,
              itemList
            }
            this.$API.purchaseCoordination.postVmiReturnedOrderConfirm(params).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('接收成功'),
                  type: 'success'
                })
                this.$router.go(-1)
                // 刷新当前 Grid
                // this.$refs.templateRef.refreshCurrentGridData();
              }
            })
          } else {
            this.$toast({
              content: this.$t('每行单据的实收数量必须大于0，请检查'),
              type: 'error'
            })
          }
        }
      })
    },
    //采方退回
    squareReturn() {
      this.$dialog({
        data: {
          title: this.$t('采访退回'),
          message: this.$t('是否确认退回？')
        },
        success: () => {
          let objIds = {
            ids: [this.$route.query.id]
          }
          this.$API.purchaseCoordination.postBuyerWarehousingReturnRejected(objIds).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('接收成功'), type: 'success' })
              this.$router.go(-1)
              // 刷新当前 Grid
              // this.$refs.templateRef.refreshCurrentGridData();
            }
          })
        }
      })
    },
    // 详情数据接口
    getDetailData() {
      let obj = {
        id: this.$route.query.id
      }
      this.$API.purchaseCoordination.postBuyerWarehousingReturnDetail(obj).then((res) => {
        if (res.code === 200) {
          const data = res.data || {}
          this.headerInfo = {
            ...data,
            siteCodeAndName: `${data.siteCode}-${data.siteName}`,
            supplierCodeAndName: `${data.supplierCode}-${data.supplierName}`,
            vmiWarehouseCodeAndName: `${data.vmiWarehouseCode}-${data.vmiWarehouseName}`
          }
          if (res.data.status == 1) {
            //证明是接受或者是退回的时候   设置实收数量等于退货总数
            res.data.vmiOrderItemResponses.forEach((item) => {
              item.checkCount = item.count
            })
          }
          this.pageConfig[0].grid.dataSource = res.data.vmiOrderItemResponses
          this.logisticsData = res.data.orderLogistic[0] //订单物流信息
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .flex-keep {
  margin-top: 10px;
}
.full-height {
  height: 100%;
}
/deep/ .mt-tabs ul.tab-container li.tab-item2.tab-item2--line.active {
  border: none;
  font-weight: 100;
}
</style>
