<template>
  <div class="home">
    <!-- <div style="margin-bottom: 20px">
      <mt-button @click="startSubmit">{{ $t("提交") }}</mt-button>
    </div> -->
    <top-info
      class="flex-keep"
      :header-info="headerInfo"
      @goBack="goBack"
      @doExpand="doExpand"
      @squareAccept="squareAccept"
      @squareReturn="squareReturn"
    ></top-info>
    <mt-data-grid
      id="Grid1"
      class="pe-edit-grid edit-grid"
      :data-source="dataSource"
      :column-data="columnData"
      ref="dataGrid"
      :allow-paging="allowPaging"
      :edit-settings="editSettings"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
      :template-config="pageConfig"
    ></mt-data-grid>
  </div>
</template>

<script>
import { dataSource, checkCol, columnData, editSettings, toolbarOptions } from './config/edit.js'
export default {
  name: 'Home',
  components: {
    TopInfo: () => import('./components/topInfo.vue')
  },
  data() {
    return {
      dataSource,
      columnData: checkCol,
      editSettings,
      toolbarOptions,
      allowPaging: true, // 产品要求：新增/编辑时，不分页；查看时要分页
      isEditStatus: false, // 正处于编辑状态
      actionFlag: '', // 点击按钮，可能是 保存草稿-save; 提交-submit;...
      pageConfig: [
        {
          useBaseConfig: false, // 使用组件中的toolbar配置
          useToolTemplate: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[], ['Filter', 'Refresh', 'Setting']]
          }
        }
      ],
      headerInfo: {
        status: 1,
        createUserName: 'jcs',
        createTime: '2022-03-02',
        factory: this.$t('京东工厂'),
        supplierCode: '0001',
        companyDescribe: this.$t('描述'),
        warehouseCode: '0033333',
        warehouseDescribe: this.$t('描述'),
        remark: '',
        acceptStatus: true,
        returnBtnStatus: true
      }
    }
  },
  mounted() {
    let selectSources = {
      currencyData: [
        {
          id: '1',
          code: 'ren',
          currencyName: this.$t('人民币')
        },
        {
          id: '2',
          code: 'mei',
          currencyName: this.$t('美元')
        },
        {
          id: '3',
          code: 'ri',
          currencyName: this.$t('日元')
        },
        {
          id: '4',
          code: 'ao',
          currencyName: this.$t('澳币')
        },
        {
          id: '5',
          code: 'tai',
          currencyName: this.$t('台币')
        }
      ]
    }
    this.columnData = checkCol.concat(columnData(selectSources, this))
    this.$nextTick(() => {
      window.gridObj = this.$refs.dataGrid
    })
  },
  methods: {
    actionBegin(args) {
      console.log('actionBegin', args)
      if (args.requestType == 'add' || args.requestType == 'beginEdit') {
        this.isEditStatus = true
      }
      if (args.requestType == 'refresh') {
        this.isEditStatus = false
      }
    },

    actionComplete(args) {
      if (args.requestType == 'save') {
        this.isEditStatus = false
        if (this.actionFlag == 'submit') {
          // this.handleSubmit();
        }
      }
    },
    handleSubmit() {
      console.log(this.$t('点击了提交'), window.gridObj.ejsRef.getCurrentViewRecords())
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    doExpand() {
      // console.log(this.$refs,"09999999999999");
      this.$refs.templateRef?.resetGridHeight && this.$refs.templateRef.resetGridHeight()
    },
    // 接受: 如果处于编辑状态，需要先结束掉，再获取数据
    squareAccept() {
      if (this.isEditStatus) {
        window.gridObj.ejsRef.endEdit()
        this.handleSubmit()
      } else {
        this.handleSubmit()
      }
    },
    // 退回
    squareReturn() {}
  }
}
</script>

<style lang="scss" scoped>
$disabledBg: rgba(245, 245, 245, 1);
$requireddBg: rgba(237, 161, 51, 0.1);
.home {
  height: 100%;

  .pe-edit-grid {
    height: 80%;

    /deep/ .e-grid {
      height: 100%;
      .e-gridcontent {
        height: calc(100% - 90px);
        .e-content {
          height: 100% !important;
        }
      }
    }
  }

  .bgTransparent {
    background-color: transparent !important;
  }

  /deep/ .edit-grid {
    // 去掉 第一个单元格选中时的左边框样式，包括 列冻结后第一个单元格选中
    tr td:first-child::before {
      display: none;
    }
    .e-frozenheader,
    .e-frozenheader > .e-table,
    .e-frozencontent > .e-table {
      border-right: unset !important;
    }
    // 去掉 单元格的选中背景色
    td.e-active {
      @extend .bgTransparent;
    }
    // 去掉 行上悬浮时的单元格背景色
    tr:hover td {
      @extend .bgTransparent;
    }
    // 去掉冻结列 悬浮时，改变背景色问题
    &.e-gridhover .e-frozenhover,
    .e-detailcell,
    .e-detailindentcell,
    .e-detailrowcollapse,
    .e-detailrowexpand,
    .e-groupcaption,
    .e-indentcell,
    .e-recordpluscollapse,
    .e-recordplusexpand,
    .e-rowcell {
      @extend .bgTransparent;
    }

    // 编辑时
    .e-editedrow,
    .e-addedrow {
      .e-rowcell .e-control-wrapper {
        // 禁用的单元格背景色
        &.e-disabled,
        &.cell-disabled,
        .e-input[readonly]:not(.e-dropdownlist) {
          background: $disabledBg !important;
          cursor: not-allowed;
        }

        // 必填的单元格背景色
        &.isRequired .e-input {
          background: $requireddBg !important;
        }
      }
    }

    // 非编辑时
    tr td {
      // 禁用的单元格 样式
      &.e-rowcell.bg-grey,
      &.e-rowcell.bg-grey.e-updatedtd {
        background-color: $disabledBg !important;
        color: #9a9a9a !important;
        cursor: not-allowed;
        &.e-gridchkbox {
          @extend .bgTransparent;
          cursor: pointer;
        }
      }

      // 必填的单元格 样式
      &.e-rowcell.bg-red,
      &.e-rowcell.bg-red.e-updatedtd {
        background-color: $requireddBg !important;
      }
    }
  }
}
</style>
