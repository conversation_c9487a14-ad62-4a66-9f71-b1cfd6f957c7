// VMI退货管理
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :current-tab="0"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { columnObj } from './config/index.js'
export default {
  components: {
    // addDialog: require("./components/addDialog.vue").default,
  },
  data() {
    return {
      pageConfig: [
        {
          tab: { title: this.$t('头视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'accept',
                  icon: 'icon_table_acquire',
                  title: this.$t('采方接收')
                },
                {
                  id: 'InvoiceImport',
                  icon: 'icon_solid_Import',
                  title: this.$t('采方退回')
                },
                {
                  id: 'synchronous',
                  icon: 'icon_table_restart',
                  title: this.$t('同步WMS')
                },
                {
                  id: 'print',
                  icon: 'icon_table_print',
                  title: this.$t('打印')
                }
              ],
              ['Filter', 'Refresh', 'Setting'] //筛选  刷新   设置
            ]
          },
          gridId: '1e841de6-2e58-498a-bd39-1d516ab11947',
          grid: {
            columnData: columnObj.headColumn,
            asyncConfig: {
              url: this.$API.purchaseCoordination.postBuyerWarehousingReturnGoods, //采方退货头视图
              rules: [
                {
                  field: 'vmi_order_type',
                  // type:"string",
                  operator: 'equal',
                  value: 3
                }
              ]
              // recordsPosition: "data",   //相当于res   默认的是data.records
            },
            frozenColumns: 1 //冻结表格的一列
          }
        },
        {
          tab: { title: this.$t('明细视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'export1',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ] //筛选  刷新   设置
          },
          gridId: '0000e9f9-be2e-4b9f-8d7d-7088e39e4737',
          grid: {
            columnData: columnObj.detailedColumn,
            asyncConfig: {
              url: this.$API.purchaseCoordination.postReturnQueryDetail, //采方明细视图列表
              rules: [
                {
                  field: 'vmi_order_type',
                  // type:"string",
                  operator: 'equal',
                  value: 3
                }
              ]
            },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    // 跳转详情
    handleClickCellTitle(e) {
      let obj = {
        tabIndex: '',
        status: '',
        id: '',
        vmiWarehouseType: ''
      }
      if (e.tabIndex === 0) {
        //tabIndex:  0是头视图 ,1是明视图
        obj.tabIndex = 0
      } else {
        obj.tabIndex = 1
      }
      if (e.field === 'vmiOrderCode') {
        obj.status = e.data.status
        obj.id = e.data.vmiOrderId ?? e.data.id //明细视图ID 和 头视图ID
        obj.vmiWarehouseType = e.data.vmiWarehouseType || 2
        this.redirectPage('purchase-execute/coordination-returnGoods-details', obj)
      }
    },
    // 表格上方操作
    handleClickToolBar(e) {
      // 采方接收  批量
      if (e.toolbar.id === 'accept') {
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('采方接收'),
            message: this.$t('确认接收吗？')
          },
          success: () => {
            let ids = _selectRows.map((item) => item.id)
            let switAccept = true
            _selectRows.map((item) => {
              if (item.status != 1) {
                switAccept = false
              }
            })
            if (switAccept == false) {
              this.$toast({
                content: this.$t('请选择待确认状态'),
                type: 'warning'
              })
              return
            }
            this.$API.purchaseCoordination
              .postBuyerWarehousingReturnConfirm({ ids })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('接收成功'),
                    type: 'success'
                  })
                  // 刷新当前 Grid
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
          }
        })
      } else if (e.toolbar.id === 'InvoiceImport') {
        // 采方退回  批量
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('采方退回'),
            message: this.$t('确认退回吗？')
          },
          success: () => {
            let ids = _selectRows.map((item) => item.id)
            let switInvoiceImport = true
            _selectRows.map((item) => {
              if (item.status != 1) {
                switInvoiceImport = false
              }
            })
            if (switInvoiceImport == false) {
              this.$toast({
                content: this.$t('请选择待确认状态'),
                type: 'warning'
              })
              return
            }
            this.$API.purchaseCoordination
              .postBuyerWarehousingReturnRejected({ ids })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('退回成功'),
                    type: 'success'
                  })
                  // 刷新当前 Grid
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
              .catch(() => {
                this.$toast({ content: this.$t('退回失败'), type: 'error' })
              })
          }
        })
      } else if (e.toolbar.id === 'synchronous') {
        let _selectRecords = e.grid.getSelectedRecords()
        if (_selectRecords.length <= 0 || _selectRecords.length > 1) {
          this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
          return
        }
        this.synchronousWms(_selectRecords[0])
      } else if (e.toolbar.id === 'export1') {
        let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let obj = JSON.parse(
          sessionStorage.getItem('0000e9f9-be2e-4b9f-8d7d-7088e39e4737')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnObj.detailedColumn.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        let params = {
          page: { current: 1, size: 10000 },
          rules: rule.rules || []
        }
        params.rules = [
          ...params.rules,
          {
            field: 'vmi_order_type',
            // type:"string",
            operator: 'equal',
            value: 3
          }
        ]
        this.$store.commit('startLoading')
        this.$API.purchaseCoordination
          .postBuyerWarehousingReturnDetailExport(params, field)
          .then((res) => {
            this.$store.commit('endLoading')
            const fileName = getHeadersFileName(res)

            download({ fileName: `${fileName}`, blob: res.data })
          })
      } else if (e.toolbar.id === 'print') {
        let obj = {
          idList: []
        }

        e.gridRef.getMtechGridRecords().map((item) => {
          obj.idList.push(item.id)
        })
        let _selectRecords = e.grid.getSelectedRecords()
        if (_selectRecords.length <= 0 || _selectRecords.length > 1) {
          this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
          return
        }
        this.$API.purchaseCoordination.thirdVmiReturnedOrderPrint(obj).then((res) => {
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(
            new Blob([content], { type: 'text/html;charset=utf-8' })
          )
          // window.open(this.pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
          return
        })
      }
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    // 行内操作
    handleClickCellTool(e) {
      if (e.tool.id == 'confirm') {
        this.$dialog({
          data: {
            title: this.$t('接收'),
            message: this.$t('确认接收吗？')
          },
          success: () => {
            this.confirm(e.data.id)
          }
        })
      } else if (e.tool.id == 'rejected') {
        this.$dialog({
          data: {
            title: this.$t('退回'),
            message: this.$t('确认退回吗？')
          },
          success: () => {
            this.rejected(e.data.id)
          }
        })
      }
    },
    // 接收
    confirm(data) {
      let obj = {
        ids: [data]
      }
      this.$API.purchaseCoordination.postBuyerWarehousingReturnConfirm(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('接收成功'), type: 'success' })
          // 刷新当前 Grid
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('接收失败'), type: 'error' })
        }
      })
    },
    // 退回
    rejected(data) {
      let obj = {
        ids: [data]
      }
      this.$API.purchaseCoordination.postBuyerWarehousingReturnRejected(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('退回成功'), type: 'success' })
          // 刷新当前 Grid
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('退回失败'), type: 'error' })
        }
      })
    },
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },
    // 同步WMS
    synchronousWms(data) {
      if (data.wmsSyncStatus === 1) {
        this.$toast({
          content: this.$t('入库单已成功同步WMS，无需重复同步'),
          type: 'warning'
        })
        return
      }
      this.$API.purchaseCoordination.returnSynchronousWms({ id: data.id }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('同步成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('同步失败'), type: 'warning' })
        }
      })
    }
  }
}
</script>

<style style="scss" scoped>
.full-height {
  height: 100%;
}
</style>
