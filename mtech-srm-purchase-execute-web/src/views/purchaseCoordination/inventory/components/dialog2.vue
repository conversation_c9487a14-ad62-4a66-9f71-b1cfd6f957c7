<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules">
      <mt-form-item prop="configType" :label="$t('配置方式')">
        <mt-select
          v-model="formData.configType"
          :data-source="storageTypeOptions"
          :disabled="type"
          :show-clear-button="true"
          :multiline="false"
          :placeholder="$t('仓库类型')"
        ></mt-select>
      </mt-form-item>

      <!-- 供方本厂时，不显示，且默认值为否 -->
      <mt-form-item
        prop="siteCode"
        :label="$t('工厂')"
        v-if="formData.configType == 'SS' || formData.configType == 'SSP'"
      >
        <mt-select
          v-model="formData.siteCode"
          :fields="{ text: 'dimensionNameValue', value: 'dimensionCodeValue' }"
          :data-source="siteSource"
          :allow-filtering="true"
          @change="siteClick"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item
        prop="supplierCode"
        :label="$t('供应商')"
        v-show="formData.configType == 'SS' || formData.configType == 'SSP'"
      >
        <mt-select
          v-model="formData.supplierCode"
          :allow-filtering="true"
          :fields="{ text: 'dimensionNameValue', value: 'dimensionCodeValue' }"
          :data-source="supplierOptions"
          @change="supplierChange"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item
        prop="purchaseGroupCode"
        :label="$t('采购组')"
        v-show="formData.configType == 'SSP'"
      >
        <mt-select
          v-model="formData.purchaseGroupCode"
          :allow-filtering="true"
          @change="purchaseClick"
          :fields="{ text: 'dimensionNameValue', value: 'dimensionCodeValue' }"
          :data-source="purchaseGroupSource"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      rules: {
        configType: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        siteCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        purchaseGroup: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      storageTypeOptions: [
        { text: this.$t('工厂+供应商'), value: 'SS' },
        { text: this.$t('工厂+供应商+采购组'), value: 'SSP' }
      ],
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {
        siteCode: null,
        configType: null,
        siteName: null,
        supplierCode: null,
        supplierName: null,
        purchaseGroupName: null,
        purchaseGroupCode: null
      },
      factoryId: '',
      siteSource: [],
      supplierOptions: [],
      purchaseGroupSource: [],
      warehouseCode: '',
      type: false,

      FactoryParam: { parentId: '', tenantId: '' }
    }
  },
  computed: {},
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    // this.getCompanyOptions();
    console.log(this.modalData)
    // if (this.modalData?.row) {
    // }
    this.type = this.modalData.title === this.$t('编辑') ? true : false
    this.init()
    if (this.modalData?.row) {
      this.formData = {
        ...this.modalData.row
      }
      this.formData.supplierCode = this.modalData?.row.supplierCode
      console.log(this.modalData?.row.supplierCode)

      console.log(this.formData)
    }
  },
  methods: {
    siteClick(e) {
      this.formData.siteName = e.itemData.dimensionNameValue
    },
    supplierChange(e) {
      this.formData.supplierName = e.itemData.dimensionNameValue
    },
    purchaseClick(e) {
      this.formData.purchaseGroupName = e.itemData.dimensionNameValue
    },
    init() {
      this.$API.vmi.getQuerySupplierConfig({ type: 'site' }).then((res) => {
        this.siteSource = res.data
      })
      this.$API.vmi.getQuerySupplierConfig({ type: 'supplier' }).then((res) => {
        this.supplierOptions = res.data
      })
      this.$API.vmi.getQuerySupplierConfig({ type: 'purchase_group' }).then((res) => {
        this.purchaseGroupSource = res.data
      })
    },

    initDialog() {
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$API.vmi.VmiConfigAdd(this.formData).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('执行成功'), type: 'success' })
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .e-searcher {
  width: 100% !important;
}
</style>
