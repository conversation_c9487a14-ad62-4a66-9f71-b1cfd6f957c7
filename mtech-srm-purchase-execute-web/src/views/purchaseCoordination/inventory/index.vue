// VMI配置管理
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      current-tab="0"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
  </div>
</template>

<script>
import { headColumn } from './config/index.js'
export default {
  data() {
    return {
      // downTemplateParams: {
      //   pageFlag: false,
      // }, // 下载模板参数

      uploadParams: {}, // 明细行上传excel的

      selectId: null,
      pageConfig: [
        {
          useBaseConfig: false, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'add',
                  icon: 'icon_solid_Createorder',
                  // permission: ["O_02_1088"],
                  title: this.$t('新增仓库')
                },
                {
                  id: 'delete',
                  icon: 'icon_solid_Delete',
                  // permission: ["O_02_1089"],
                  title: this.$t('删除')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          // gridId: "cbe96617-dc44-425e-aa9c-b68c810bce85",
          grid: {
            columnData: headColumn,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmi_warehouse_supplier_config/page-query'
              // recordsPosition: "data.records",
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },

  methods: {
    // 表格头部操作
    handleClickToolBar(e) {
      const selectRows = e.grid.getSelectedRecords()
      if (e.toolbar.id === 'add') {
        // this.$refs.configListDialog.initDialog();
        this.handleAdd()
      } else if (e.toolbar.id === 'delete' && selectRows.length > 0) {
        let ids = selectRows.map((item) => item.id)
        this.handleDelete(ids)
      } else if (e.grid.getSelectedRecords().length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
    },

    handleDelete(ids) {
      // 删除弹窗
      let arr = [...ids]
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('确认删除吗？')
        },
        success: () => {
          this.$API.vmi.VmiConfigDelete(arr).then(() => {
            this.dilogCogfim()
          })
        }
      })
    },
    handleAdd(row) {
      console.log(row)
      this.$dialog({
        modal: () => import('./components/dialog2.vue'),
        data: {
          title: row ? this.$t('编辑') : this.$t('新增'),
          row: row || null
        },
        success: () => {
          this.dilogCogfim()
        }
      })
    },
    // 接受弹窗新增得数据
    dilogCogfim() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 行内操作
    handleClickCellTool(e) {
      if (e.tool.id == 'Delete') {
        this.handleDelete([e.data.id])
      } else if (e.tool.id == 'Edit') {
        this.handleAdd(e.data)
      }
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
