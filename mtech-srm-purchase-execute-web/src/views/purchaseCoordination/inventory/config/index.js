import { i18n } from '@/main.js'
// VMI配置管理
export const headColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'indexs',
    headerText: i18n.t('序号'),
    showInColumnChooser: false,
    ignore: true,
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return Number(+row.index + 1)
      }
    },
    cellTools: [
      {
        id: 'Edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑')
      },
      {
        id: 'Delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除')
      }
    ]
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商')
  },
  {
    field: 'purchaseGroupName',
    headerText: i18n.t('采购组')
  }
]
