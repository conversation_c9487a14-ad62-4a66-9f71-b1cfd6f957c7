import { i18n } from '@/main.js'
import Vue from 'vue'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false //隐藏在列选择器中的过滤
  },
  {
    field: 'buyerGroup',
    headerText: i18n.t('采购组'), // sortsObj,sortsElem;
    editTemplate: function () {
      return {
        template: Vue.component('actionSelect', {
          template: `<mt-select v-model="data.buyerGroup" :fields="fields" :dataSource="dataSource" @change="onchange"></mt-select>`,
          data() {
            return {
              dataSource: [
                {
                  text: i18n.t('采购一组'),
                  value: 'CGYZ20210907135855'
                },
                {
                  text: i18n.t('办公用品采购组'),
                  value: 'BGYPCGZ20211229140123'
                },
                {
                  text: i18n.t('生产材料采购组'),
                  value: 'SCCLCGZ20211229140203'
                }
              ]
            }
          },
          methods: {
            onchange(e) {
              this.$bus.$emit('buyerGroup', e.value)
              const index = this.data.index
              const field = this.data.column.field
              this.dataSource[index][field] = e.value
            }
          }
        })
      }
    }
  },
  {
    field: 'planGroup',
    headerText: i18n.t('计划组'), // sortsObj,sortsElem;
    editTemplate: function () {
      return {
        template: Vue.component('addressTemplate', {
          template: `<mt-select v-model="data.planGroup" :dataSource="dataSource"></mt-select>`,
          data() {
            return {
              dataSource: [
                {
                  text: i18n.t('计划一组'),
                  value: 'JHYZ20220311153901'
                }
              ]
            }
          }
        })
      }
    }
  },
  {
    field: 'materialGroup',
    headerText: i18n.t('物料组'), // sortsObj,sortsElem;
    editTemplate: function () {
      return {
        template: Vue.component('addressTemplate', {
          template: `<mt-select v-model="data.materialGroup" :dataSource="dataSource"></mt-select>`,
          data() {
            return {
              dataSource: [
                {
                  text: i18n.t('备品备件组'),
                  value: 'M001'
                },
                {
                  text: i18n.t('通用品项组'),
                  value: 'ITEM-GROUP-002'
                }
              ]
            }
          }
        })
      }
    }
  },
  {
    field: 'orderCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'warehousingType',
    headerText: i18n.t('VMI入库类型'), // sortsObj,sortsElem;
    editTemplate: function () {
      return {
        template: Vue.component('addressTemplate', {
          template: `<mt-select v-model="data.warehousingType" :dataSource="dataSource"></mt-select>`,
          data() {
            return {
              dataSource: [
                {
                  text: i18n.t('类型1'),
                  value: 1
                },
                {
                  text: i18n.t('类型2'),
                  value: 2
                }
              ]
            }
          }
        })
      }
    }
  },
  {
    field: 'dayNumber',
    headerText: i18n.t('VMI入库提前天数'),
    editType: 'numericedit' //默认编辑类型之number
  }
]
