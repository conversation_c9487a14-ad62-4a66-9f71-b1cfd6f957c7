import { i18n } from '@/main.js'
// VMI仓库与供应商关系配置
export const headColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'indexs',
    headerText: i18n.t('序号'),
    ignore: true,
    showInColumnChooser: false,
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return Number(+row.index + 1)
      }
    },
    cellTools: [
      {
        id: 'Edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑')
      },
      {
        id: 'Delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除')
      }
    ]
  },
  {
    width: '150',
    field: 'id',
    headerText: i18n.t('物料管理'),
    cellTools: [],
    valueConverter: {
      type: 'function',
      filter: () => {
        return i18n.t(`物料管理`)
      }
    }
  },
  {
    width: '120',
    field: 'companyCode',
    headerText: i18n.t('采方公司编码')
  },
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('采方公司名称')
  },
  {
    width: '120',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '180',
    field: 'thirdPartyLogisticsCode',
    headerText: i18n.t('第三方物流公司编码')
  },
  {
    width: '180',
    field: 'thirdPartyLogisticsName',
    headerText: i18n.t('第三方物流公司名称')
  },

  {
    width: '150',
    field: 'VMIManageCode',
    headerText: i18n.t('VMI管理方编码'), // 如果是供方本厂，取供应商；如果是第三方物流，取第三方物流
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return row.vmiWarehouseType == 1 ? row?.supplierCode : row?.thirdPartyLogisticsCode
      }
    }
  },
  {
    width: '150',
    field: 'VMIManageName',
    headerText: i18n.t('VMI管理方名称'),
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return row.vmiWarehouseType == 1 ? row?.supplierName : row?.thirdPartyLogisticsName
      }
    }
  },
  {
    width: '150',
    field: 'vmiWarehouseType',
    headerText: i18n.t('VMI仓类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('SRM管理库存'),
        1: i18n.t('原厂'),
        2: i18n.t('WMS管理库存')
      }
    }
  },
  {
    width: '150',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓编码')
  },
  {
    width: '150',
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓名称')
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '150',
    field: 'goodsReceiptTypeDesc',
    headerText: i18n.t('VMI入库类型')
  },
  {
    width: '150',
    field: 'advanceTime',
    headerText: i18n.t('VMI库提前期')
  },
  {
    width: '150',
    field: 'needItem',
    headerText: i18n.t('是否管理物料'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('否'),
        1: i18n.t('是')
      }
    }
  },

  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('是否有效'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('无效'),
        1: i18n.t('有效')
      }
    }
  }
]
