// VMI入库管理
<template>
  <div class="full-height">
    <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      v-show="tabIndex === 0"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <mt-template-page
      ref="templateRef1"
      :hidden-tabs="true"
      :template-config="pageConfig1"
      v-show="tabIndex === 1"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnObj } from './config/warehousingIndex.js'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    // addDialog: require("./components/addDialog.vue").default,
  },
  data() {
    return {
      pageConfig: [
        {
          tab: { title: this.$t('头视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          activatedRefresh: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'synchronous',
                  icon: 'icon_table_restart',
                  title: this.$t('同步WMS')
                },
                {
                  id: 'export1',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: 'b0ffc1c8-e915-4aa1-adde-907cc971ceea',
          grid: {
            columnData: columnObj.headColumn,
            dataSource: [
              {
                vmiOrderCode: 'w20211227001',
                status: 1,
                factoryCode: '630847',
                factoryDescribe: this.$t('TCL空调器有限公司生产工厂'),
                warehouseCode: '0100001',
                warehouseDescribe: this.$t('VMI红物流芜湖威灵电机'),
                supplierCode: 'G 89001',
                materialDescribe: this.$t('广东惠利普智能科技股份有限公司'),
                goodsAddress: this.$t('深圳市福田区下梅林梅华路'),
                createTime: '2021-12-27 13:50：33',
                receiveTime: '2021-12-30'
              }
            ],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmi-receive-order/buyer-page-query',
              recordsPosition: 'data.records',
              serializeList: this.serialize
            },
            frozenColumns: 1
          }
        }
      ],
      tabIndex: 0,
      pageConfig1: [
        {
          tab: { title: this.$t('明细视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          activatedRefresh: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'export2',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: '69874198-33bb-4f7e-928b-4a9180ffdcb1',
          grid: {
            columnData: columnObj.detailedColumn,
            dataSource: [
              {
                warehousingCode: 'w20211227001',
                status: 1,
                rowNumber: 10,
                materialCode: '630847',
                materialName: this.$t('自攻特攻GB/T'),
                procurementGroup: this.$t('采购组01'),
                number: 500,
                company: this.$t('件'),
                remarks: this.$t('备注文本字段'),
                qualityResult: this.$t('不合格'),
                unqualifiedClassification: this.$t('不良价值'),
                qualityRemarks: this.$t('质检备注内容'),
                relationCode: 'D848940',
                relationRowCode: 10,
                factoryCode: '630847',
                factoryDescribe: this.$t('TCL空调器有限公司生产工厂'),
                warehouseCode: '0100001',
                warehouseDescribe: this.$t('VMI红物流芜湖威灵电机'),
                supplierCode: 'G 89001',
                materialDescribe: this.$t('广东惠利普智能科技股份有限公司'),
                goodsAddress: this.$t('深圳市福田区下梅林梅华路'),
                createTime: '2021-12-27 13:50：33',
                receiveTime: '2021-12-30'
              }
            ],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmi-receive-order/buyer-item-page-query',
              recordsPosition: 'data.records',
              serializeList: this.serialize,
              ignoreDefaultSearch: true
            },
            frozenColumns: 1
          }
        }
      ],
      tabSource: [
        {
          title: this.$t('头视图')
        },
        {
          title: this.$t('明细视图')
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    serialize(data) {
      data.forEach((item) => {
        item.submitTime = Number(item.submitTime)
        item.confirmTime = Number(item.confirmTime)
      })
      return data
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    // 跳转详情
    handleClickCellTitle(e) {
      let obj = {
        status: '',
        id: ''
      }
      if (e.field === 'vmiOrderCode') {
        obj.status = e.data.status
        obj.id = e.data.id
        this.redirectPage('purchase-execute/coordination-warehousing-establish', obj)
      }
    },
    // 头部跳转
    handleClickToolBar(e) {
      if (e.toolbar.id === 'export1') {
        let obj = JSON.parse(
          sessionStorage.getItem('b0ffc1c8-e915-4aa1-adde-907cc971ceea')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnObj.headColumn.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        let rule =
          this.tabIndex === 0
            ? this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
            : this.$refs.templateRef1.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let params = {
          page: { current: 1, size: 1000 },
          rules: rule.rules || []
        }
        this.$store.commit('startLoading')
        this.$API.purchaseCoordination.buyerExcelAgeViewExport(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
      if (e.toolbar.id === 'export2') {
        let obj = JSON.parse(
          sessionStorage.getItem('69874198-33bb-4f7e-928b-4a9180ffdcb1')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnObj.detailedColumn.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        let rule =
          this.tabIndex === 0
            ? this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
            : this.$refs.templateRef1.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let params = {
          page: { current: 1, size: 1000 },
          rules: rule.rules || []
        }
        this.$store.commit('startLoading')
        this.$API.purchaseCoordination.buyerItemAgeViewExport(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
      let _selectRecords = e.grid.getSelectedRecords()
      if (
        (_selectRecords.length <= 0 || _selectRecords.length > 1) &&
        e.toolbar.id === 'synchronous'
      ) {
        this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
        return
      }
      this.synchronousWms(_selectRecords[0])
    },
    // 同步WMS
    synchronousWms(data) {
      if (data.wmsSyncStatus === 1) {
        this.$toast({
          content: this.$t('入库单已成功同步WMS，无需重复同步'),
          type: 'warning'
        })
        return
      }
      this.$API.purchaseCoordination.synchronousWms({ id: data.id }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('同步成功'), type: 'success' })

          this.tabIndex === 0
            ? this.$refs.templateRef.refreshCurrentGridData()
            : this.$refs.templateRef1.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('同步失败'), type: 'warning' })
        }
      })
    },
    handleClickCellTool(e) {
      console.log(e)
    },
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
