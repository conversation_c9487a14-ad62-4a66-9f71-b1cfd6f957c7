<template>
  <div class="box">
    <!-- 下面的内容 -->
    <div class="main-box">
      <mt-form
        ref="ruleForm"
        :model="logisticsData"
        :rules="rules"
        :validate-on-rule-change="false"
      >
        <!-- 发货方式 -->
        <mt-form-item prop="type" :label="$t('请选择发货方式:')">
          <br />
          <mt-radio
            :disabled="true"
            v-model.number="logisticsData.transportType"
            :data-source="ShippingTypeOptions"
          ></mt-radio>
        </mt-form-item>
        <br />
        <!-- 快递-物流公司 -->
        <mt-form-item
          v-show="logisticsData.transportType == ShippingType.express"
          prop="thirdPartyLogisticsName"
          :label="$t('物流公司')"
        >
          <mt-input
            v-model="logisticsData.thirdPartyLogisticsName"
            :disabled="true"
            :placeholder="$t('物流公司')"
          ></mt-input>
        </mt-form-item>
        <!-- 快递-物流单号 -->
        <mt-form-item
          v-show="logisticsData.transportType == ShippingType.express"
          prop="transportNum"
          :label="$t('物流单号')"
        >
          <mt-input
            v-model="logisticsData.transportNum"
            :disabled="true"
            :placeholder="$t('物流单号')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-司机名称 -->
        <mt-form-item
          v-show="logisticsData.transportType == ShippingType.logistics"
          prop="driverName"
          :label="$t('司机名称')"
        >
          <mt-input
            v-model="logisticsData.driverName"
            :disabled="true"
            :placeholder="$t('司机名称')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-司机联系方式 -->
        <mt-form-item
          v-show="logisticsData.transportType == ShippingType.logistics"
          prop="driverPhone"
          :label="$t('司机联系方式')"
        >
          <mt-input
            v-model="logisticsData.driverPhone"
            :disabled="true"
            :placeholder="$t('司机联系方式')"
          ></mt-input>
        </mt-form-item>
        <!-- 物流-车牌 -->
        <mt-form-item
          v-show="logisticsData.transportType == ShippingType.logistics"
          prop="licensePlateNumber"
          :label="$t('车牌')"
        >
          <mt-input
            v-model="logisticsData.licensePlateNumber"
            :disabled="true"
            :placeholder="$t('车牌')"
          ></mt-input>
        </mt-form-item>
        <!-- 件数 -->
        <mt-form-item prop="count" :label="$t('件数')">
          <mt-input
            v-model="logisticsData.count"
            :disabled="true"
            :placeholder="$t('件数')"
          ></mt-input>
        </mt-form-item>

        <!-- 备注 -->
        <mt-form-item class="full-width" prop="remark" :label="$t('备注')" :show-message="false">
          <mt-input
            v-model="logisticsData.remark"
            :disabled="true"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { ShippingTypeOptions, ShippingType } from '../config/constant'

export default {
  props: {
    logisticsData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      ShippingTypeOptions,
      ShippingType,
      isExpand: true,
      rules: {}
    }
  },
  mounted() {},
  filters: {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.box {
  padding: 20px 0 20px 20px;

  .main-box .mt-form-item {
    width: calc(20% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
  }
}
</style>
