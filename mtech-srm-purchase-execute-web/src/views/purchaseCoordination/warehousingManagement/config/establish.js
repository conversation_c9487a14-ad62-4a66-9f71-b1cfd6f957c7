import { i18n } from '@/main.js'
import Vue from 'vue'

export const editing = {
  allowEditing: true, //是否允许编辑
  allowDeleting: true, //是否允许删除
  allowAdding: true //是否允许新增
  //   allowFiltering: true, //是否允许过滤
  //   allowReordering: true, //是否重新排序
  //   allowResizing: true, //是否允许调整大小 列拖拽
  //   clipMode: "Ellipsis", //剪辑模式 Clip 内容溢出其区域时截断 Ellipsis显示省略号 EllipsisWithTooltip时显示省略号，当悬停在省略号应用的单元格上时，它也会显示工具提示
}

export const editingFalse = {
  allowEditing: false, //是否允许编辑
  allowDeleting: false, //是否允许删除
  allowAdding: false //是否允许新增
  //   allowFiltering: true, //是否允许过滤
  //   allowReordering: true, //是否重新排序
  //   allowResizing: true, //是否允许调整大小 列拖拽
  //   clipMode: "Ellipsis", //剪辑模式 Clip 内容溢出其区域时截断 Ellipsis显示省略号 EllipsisWithTooltip时显示省略号，当悬停在省略号应用的单元格上时，它也会显示工具提示
}
// 传递this
let _this = this
export const _thisFunction = function (vueObject) {
  _this = vueObject
}

export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false //隐藏在列选择器中的过滤
  },
  {
    width: '150',
    field: 'rowNum',
    headerText: i18n.t('行号'),
    allowEditing: false //此列不允许编辑
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    allowEditing: false //此列不允许编辑
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    allowEditing: false
  },
  // {
  //   width: "150",
  //   field: "insufficientDeliveryLimit",
  //   headerText: i18n.t("交货不足限定"),
  //   allowEditing: false,
  // },
  // {
  //   width: "150",
  //   field: "overDeliveryLimit",
  //   headerText: i18n.t("过量交货限定"),
  //   allowEditing: false,
  // },
  {
    width: '150',
    field: 'count',
    headerText: i18n.t('送货数量'),
    editType: 'numericedit', //默认编辑类型之number
    allowEditing: false
  },
  {
    width: '150',
    field: 'checkCount',
    headerText: i18n.t('收货数量'),
    editType: 'numericedit', //默认编辑类型之number
    allowEditing: false
  },
  {
    width: 0,
    field: 'itemUnit',
    headerText: i18n.t('单位'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'itemUnitDescription',
    headerText: i18n.t('单位'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'purchaseGroupName',
    headerText: i18n.t('采购组'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'reportText',
    headerText: i18n.t('质检结果'),
    editTemplate: function () {
      return {
        template: Vue.component('addressTemplate', {
          template: `<mt-radio id="reportText" v-model="data.reportText" :dataSource="[{ label: $t('合格'), value: $t('合格')},{
      label: $t('不合格'), value: $t('不合格')}]"  @input="onchange"></mt-radio>`,
          data() {
            return { data: {} }
          },
          mounted() {},
          methods: {
            onchange(value) {
              //两种方式  1 通过$bus传值
              //另一种直接改变dataSource的值 主意：这种方式只改变了值  并不会改变编辑结束后的视图   改变视图在事件里
              const index = this.data.index
              const field = this.data.column.field
              _this.dataSource[index][field] = value
              console.log(_this.dataSource[index], '-=-=')
              //选择合格的时候  要把选择的不合格的分类清空 并且置灰

              this.$bus.$emit('reportTextChange', value)
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'unqualifiedTypes',
    headerText: i18n.t('不合格分类'), // sortsObj,sortsElem;
    editTemplate: function () {
      return {
        template: Vue.component('unqualifiedTypesTemplate', {
          template: `<mt-select v-model="data.unqualifiedTypes" :dataSource="dataSource" :disabled="disabled" :placeholder="placeholder" :fields="fields" @change="onchange"></mt-select>`,
          data() {
            return {
              data: {},
              fields: { value: 'label', text: 'label' },
              placeholder: i18n.t('选择不合格的原因'),
              disabled: false,
              dataSource: [
                {
                  label: i18n.t('包装不符'),
                  value: '0'
                },
                {
                  label: i18n.t('其他不良'),
                  value: '1'
                },
                {
                  label: i18n.t('外观不符'),
                  value: '2'
                },
                {
                  label: i18n.t('内容不符'),
                  value: '3'
                },
                {
                  label: i18n.t('材质不符'),
                  value: '4'
                },
                {
                  label: i18n.t('标志不符'),
                  value: '5'
                },
                {
                  label: i18n.t('尺寸不符'),
                  value: '6'
                },
                {
                  label: i18n.t('认知不符'),
                  value: '7'
                },
                {
                  label: i18n.t('性能不符'),
                  value: '8'
                },
                {
                  label: i18n.t('错混料'),
                  value: '9'
                }
              ]
            }
          },
          mounted() {
            if (this.data.reportText === i18n.t('合格')) {
              this.disabled = true
            }

            this.$bus.$off('reportTextChange').$on('reportTextChange', (value) => {
              if (value !== i18n.t('合格')) {
                this.disabled = false
              } else {
                this.disabled = true
                this.data.unqualifiedTypes = null
                const index = this.data.index
                const field = this.data.column.field
                _this.dataSource[index][field] = null
              }
              console.log(value)
            })
          },
          methods: {
            onchange(e) {
              const index = this.data.index
              const field = this.data.column.field
              _this.dataSource[index][field] = e.itemData?.label
              // _this.dataSource[index]['unqualifiedTypes'] = e.itemData.label;
            }
          }
        })
      }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red" v-if="disabled === false">*</span>
                <span class="e-headertext">{{$t('不合格分类')}}</span>
              </div>
            `,
          data() {
            return {
              data: {},
              disabled: null
            }
          },
          mounted() {
            this.disabled =
              this.data.reportText === i18n.t('合格')
                ? true
                : this.data.reportText === i18n.t('不合格')
                ? false
                : null

            this.$bus.$on('reportTextChange', (value) => {
              this.disabled = value == i18n.t('合格') ? true : false
            })
          }
        })
      }
    }
    // edit: {
    //   create: () => {
    //     sortsElem = document.createElement("input");
    //     return sortsElem;
    //   },
    //   read: () => {
    //     return sortsObj.text;
    //   },
    //   destroy: () => {
    //     sortsObj.destroy();
    //   },
    //   write: (arg) => {
    //     console.log(arg, "-=-=");
    //     sortsObj = new DropDownList({

    //       change: (e) => {
    //         console.log(e);
    //       },
    //     });
    //     sortsObj.appendTo(sortsElem);
    //   },
    // },
  },

  {
    width: '150',
    field: 'qcRemark',
    headerText: i18n.t('质检备注'),
    editTemplate: function () {
      return {
        template: Vue.component('addressTemplate', {
          template: `<mt-input id="qcRemark" v-model="data.qcRemark" @blur="onchange"></mt-input>`,
          data() {
            return { data: {} }
          },
          methods: {
            onchange(value) {
              _this.$bus.$emit('qcRemark', value)
              const index = this.data.index
              const field = this.data.column.field
              _this.dataSource[index][field] = value
            }
          }
        })
      }
    }
  },
  // {
  //   width: "150",
  //   field: "orderCode",
  //   headerText: i18n.t("关联采购订单号"),
  //   allowEditing: false,
  // },
  // {
  //   width: "150",
  //   field: "lineNo",
  //   headerText: i18n.t("关联采购订单行号"),
  //   allowEditing: false,
  // },
  // {
  //   width: "150",
  //   field: "batchCode",
  //   headerText: i18n.t("批次/卷号"),
  //   allowEditing: false,
  // },
  // {
  //   width: "150",
  //   field: "takeNo",
  //   headerText: i18n.t("车号/船号"),
  //   allowEditing: false,
  // },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    allowEditing: false
  },
  {
    width: '150',
    field: 'updateTime', // 后端要对比质检结果是否改变
    headerText: i18n.t('接收日期'),
    allowEditing: false
    // valueConverter: {
    //   type: "function",
    //   filter: (e) => {
    //     if (e && e.length === 13) {
    //       e = Number(e);
    //       return UTILS.dateFormat(e);
    //     } else {
    //       return "";
    //     }
    //   },
    // },
  }
]
