import { i18n } from '@/main.js'
// VMI配置管理
export const headColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'indexs',
    headerText: i18n.t('序号'),
    showInColumnChooser: false,
    ignore: true,
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return Number(+row.index + 1)
      }
    },
    cellTools: [
      {
        id: 'Edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑')
      },
      {
        id: 'Delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除')
      }
    ]
  },
  {
    width: '200',
    field: 'companyCode',
    headerText: i18n.t('VMI管理方编号')
  },
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('VMI管理方名称')
  },
  {
    width: '150',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓编码')
  },
  {
    width: '150',
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓名称')
  },
  {
    width: '150',
    field: 'vmiWarehouseAddress',
    headerText: i18n.t('仓库地址')
  },
  {
    width: '150',
    field: 'vmiWarehouseType',
    headerText: i18n.t('仓库类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('SRM管理库存'),
        1: i18n.t('原厂'),
        2: i18n.t('WMS管理库存')
      }
    }
  },
  {
    width: '150',
    field: 'needBatch',
    headerText: i18n.t('是否开启批次管理'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('否'),
        1: i18n.t('是')
      }
    }
  },
  {
    width: '150',
    field: 'needIqc',
    headerText: i18n.t('是否需要IQC判定'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('否'),
        1: i18n.t('是')
      }
    }
  },
  // {
  //   width: "150",
  //   field: "status",
  //   headerText: i18n.t("库存状况"),
  //   valueConverter: {
  //     type: "map",
  //     map: {
  //       0: i18n.t("无效"),
  //       1: i18n.t("有效"),
  //     },
  //   },
  // },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建日期')
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('是否有效'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('无效'),
        1: i18n.t('有效')
      }
    }
  }
]
