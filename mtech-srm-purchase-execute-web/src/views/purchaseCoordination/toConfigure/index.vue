// VMI配置管理
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      current-tab="0"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <!-- 导入弹框 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { headColumn } from './config/index.js'
import * as UTILS from '@/utils/utils'
export default {
  components: {
    uploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    return {
      // downTemplateParams: {
      //   pageFlag: false,
      // }, // 下载模板参数
      downTemplateParams: {
        page: { current: 1, size: 10 }
      }, // 下载模板参数
      uploadParams: {}, // 明细行上传excel的
      requestUrls: {
        templateUrlPre: 'vmi',
        templateUrl: 'downloadWarehose',
        uploadUrl: 'importWarehose'
      },
      selectId: null,
      pageConfig: [
        {
          useBaseConfig: false, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'add',
                  icon: 'icon_solid_Createorder',
                  permission: ['O_02_1088'],
                  title: this.$t('新增仓库')
                },
                {
                  id: 'delete',
                  icon: 'icon_solid_Delete',
                  permission: ['O_02_1089'],
                  title: this.$t('删除')
                }
              ],
              [
                'Filter',
                {
                  id: 'import',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导入')
                },
                {
                  id: 'export',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                },
                'Refresh',
                'Setting'
              ]
            ]
          },
          gridId: 'cbe96617-dc44-425e-aa9c-b68c810bce85',
          grid: {
            columnData: headColumn,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiWarehouse/query',
              recordsPosition: 'data.records'
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },

  methods: {
    // 表格头部操作
    handleClickToolBar(e) {
      const selectRows = e.grid.getSelectedRecords()
      if (e.toolbar.id === 'add') {
        // this.$refs.configListDialog.initDialog();
        this.handleAdd()
      } else if (e.toolbar.id === 'delete' && selectRows.length > 0) {
        let ids = selectRows.map((item) => item.id)
        this.handleDelete(ids)
      } else if (e.toolbar.id === 'import') {
        // 导入
        this.showUploadExcel(true)
      } else if (e.toolbar.id === 'export') {
        // 导出
        this.exportExcel()
      } else if (e.toolbar.id === 'refreshDataByLocal') {
        // 调用刷新
      } else if (e.grid.getSelectedRecords().length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
    },
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.dilogCogfim()
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
    },
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },

    handleDelete(ids) {
      // 删除弹窗
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('确认删除吗？')
        },
        success: () => {
          this.$API.vmi.deleteWarehose({ ids }).then(() => {
            this.dilogCogfim()
          })
        }
      })
    },
    exportExcel() {
      let obj = JSON.parse(
        sessionStorage.getItem('cbe96617-dc44-425e-aa9c-b68c810bce85')
      )?.visibleCols
      let field = []
      if (obj !== undefined && obj.length) {
        obj.forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      } else {
        headColumn.forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      }
      const params = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules
      this.$store.commit('startLoading')
      this.$API.vmi
        .downloadWarehose(
          {
            page: { current: 1, size: 10000000000000000 },
            ...params
          },
          field
        )
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = UTILS.getHeadersFileName(res)
          UTILS.download({ fileName: `${fileName}`, blob: res.data })
        })
    },
    handleAdd(row) {
      if (row && row.companyCode) {
        row.supplierCode = row.companyCode
        row.supplierName = row.companyName
      }
      if (row && row.stockType) row.stockType = row.stockType.split(',')
      this.$dialog({
        modal: () => import('./components/dialog2.vue'),
        data: {
          title: row ? this.$t('编辑') : this.$t('新增'),
          row: row || null
        },
        success: () => {
          this.dilogCogfim()
        }
      })
    },
    // 接受弹窗新增得数据
    dilogCogfim() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 行内操作
    handleClickCellTool(e) {
      if (e.tool.id == 'Delete') {
        this.handleDelete([e.data.id])
      } else if (e.tool.id == 'Edit') {
        this.handleAdd(e.data)
      }
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
