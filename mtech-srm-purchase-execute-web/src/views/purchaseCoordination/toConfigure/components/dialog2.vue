<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules">
      <mt-form-item prop="supplierCode" :label="$t('VMI管理方')">
        <mt-select
          v-model="formData.supplierCode"
          :data-source="supplierOptions"
          :disabled="type"
          :show-clear-button="false"
          :allow-filtering="true"
          :filtering="getSupplier"
          :fields="{ text: 'showLabel', value: 'supplierCode' }"
          :placeholder="$t('请选择VMI管理方')"
          @change="handleCompanyChange"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="vmiWarehouseType" :label="$t('仓库类型')">
        <mt-select
          v-model="formData.vmiWarehouseType"
          :data-source="storageTypeOptions"
          :disabled="type"
          :show-clear-button="true"
          :multiline="false"
          :placeholder="$t('仓库类型')"
        ></mt-select>
      </mt-form-item>
      <!-- <mt-form-item prop="stockType" :label="$t('库存状况')">
        <mt-multi-select
          v-model="formData.stockType"
          :dataSource="stockTypeSelect"
          :showClearButton="true"
          placeholder="请选择库存状况"
        ></mt-multi-select>
      </mt-form-item> -->
      <mt-form-item prop="vmiWarehouseName" :label="$t('VMI仓名称')">
        <mt-input
          v-model="formData.vmiWarehouseName"
          :placeholder="$t('请输入VMI仓名称')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="vmiWarehouseCode" :label="$t('VMI仓编码')">
        <mt-input
          :disabled="!!modalData.row"
          v-model="formData.vmiWarehouseCode"
          :placeholder="$t('请输入VMI仓编码')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="vmiWarehouseAddress" :label="$t('仓库地址')">
        <mt-input
          v-model="formData.vmiWarehouseAddress"
          :placeholder="$t('请填写仓库地址')"
        ></mt-input>
      </mt-form-item>

      <!-- 供方本厂时，不显示，且默认值为否 -->
      <mt-form-item
        prop="needBatch"
        :label="$t('是否开启批次管理')"
        v-show="formData.vmiWarehouseType == 0 || formData.vmiWarehouseType == 2"
      >
        <mt-select
          v-model="formData.needBatch"
          :disabled="type"
          :data-source="[
            { text: $t('是'), value: 1 },
            { text: $t('否'), value: 0 }
          ]"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>

      <!-- 供方本厂时，不显示，且默认值为否 -->
      <mt-form-item
        prop="needIqc"
        :label="$t('是否需要IQC判定')"
        v-show="formData.vmiWarehouseType == 0"
      >
        <mt-select
          v-model="formData.needIqc"
          :data-source="[
            { text: $t('是'), value: 1 },
            { text: $t('否'), value: 0 }
          ]"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="status" :label="$t('是否有效')">
        <mt-select
          v-model="formData.status"
          :data-source="[
            { text: $t('是'), value: 1 },
            { text: $t('否'), value: 0 }
          ]"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { judgeFormatCodeName } from '@/utils/utils'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const reg = /^\w+$/
    const vmiWarehouseCodeValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入仓库编码')))
      } else if (!reg.test(value) && !this.type) {
        callback(new Error(this.$t('只能用数字、英文字母、下划线“_”')))
      } else {
        this.$refs.ruleForm.clearValidate(['vmiWarehouseCode'])
        callback()
      }
    }
    return {
      operateTypeOptions: [
        { text: this.$t('是'), value: '1' },
        { text: this.$t('否'), value: '2' }
      ],
      storageTypeOptions: [
        { text: this.$t('SRM管理库存'), value: 0 },
        { text: this.$t('原厂'), value: 1 },
        { text: this.$t('WMS管理库存'), value: 2 }
      ],
      // 库存状态
      // stockTypeSelect: [
      //   { text: this.$t("合格库存"), value: 0 },
      //   { text: this.$t("待检库存"), value: 1 },
      //   { text: this.$t("出库不合格库存"), value: 2 },
      //   { text: this.$t("不合格库存"), value: 3 },
      // ],
      dialogTitle: this.modalData.title,
      rules1: {
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择VMI管理方'),
            trigger: 'blur'
          }
        ],
        vmiWarehouseType: [
          {
            required: true,
            message: this.$t('请选择仓库类型'),
            trigger: 'blur'
          }
        ],
        // stockType: [
        //   {
        //     required: true,
        //     message: this.$t("请选择库存状态"),
        //     trigger: "blur",
        //   },
        // ],
        vmiWarehouseName: [
          {
            required: true,
            message: this.$t('请输入仓库名称'),
            trigger: 'blur'
          }
        ],
        vmiWarehouseCode: [
          {
            required: true,
            validator: vmiWarehouseCodeValidator,
            trigger: 'blur'
          }
        ],
        vmiWarehouseAddress: [
          {
            required: true,
            message: this.$t('请输入仓库地址'),
            trigger: 'blur'
          }
        ],
        status: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      rules2: {
        needIqc: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      rules3: {
        needBatch: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {
        supplierCode: '',
        companyName: '',
        vmiWarehouseType: '',
        vmiWarehouseName: '',
        vmiWarehouseCode: '',
        vmiWarehouseAddress: '',
        needIqc: '',
        needBatch: '',
        status: '',
        stockType: [0, 1, 2, 3]
      },
      factoryId: '',
      companyId: '',
      supplierThird: '',
      supplier: '',
      warehouseCode: '',
      companyOptions: [],
      factoryOptions: [],
      type: false,
      vmiHouseOptions: [],
      supplierThirdOptions: [],
      supplierOptions: [],
      FactoryParam: { parentId: '', tenantId: '' }
    }
  },
  computed: {
    rules() {
      // 如果是第三方物流(场外)，两个字段也要校验
      if (this.formData.vmiWarehouseType == 0) {
        return {
          ...this.rules1,
          ...this.rules2,
          ...this.rules3
        }
      } else if (this.formData.vmiWarehouseType == 2) {
        return {
          ...this.rules1,
          ...this.rules3
        }
      } else {
        return this.rules1
      }
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    // this.getCompanyOptions();
    const e = {}
    if (this.modalData?.row) {
      e.text = this.modalData?.row.supplierName
    }
    this.type = this.modalData.title === this.$t('编辑') ? true : false
    this.getSupplier(e)
    this.getVmiHouse()
    if (this.modalData?.row) {
      this.formData = {
        ...this.modalData.row
      }
    }
  },
  methods: {
    getCompanyOptions() {
      //查询业务公司下拉数据
      this.$API.masterData
        .OrgFindSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true,
          organizationIds: []
        })
        .then((res) => {
          res.data.forEach((item) => (item.nameAndCode = item.orgCode + '--' + item.orgName))
          this.companyOptions = res.data
        })
    },
    getFactoryOptions(params) {
      if (!params) return
      //查询业务公司下拉数据
      this.$API.masterData.findSiteInfoByParentId(params).then((res) => {
        // console.log(res.data, "-=-=-=");
        this.factoryOptions = res.data
      })
    },
    // 获取VMI管理方下拉
    getSupplier(e) {
      this.$API.masterData.conditionGetSupplier({ fuzzyNameOrCode: e?.text }).then((res) => {
        let data = res.data.filter((item) => item.supplierName)
        this.supplierThirdOptions = data
        let supplierOptions = data
        supplierOptions.forEach(
          (i) => (i.showLabel = judgeFormatCodeName(i.supplierCode, i.supplierName))
        )
        this.supplierOptions = supplierOptions

        this.$nextTick(() => {
          if (e.updateData && typeof e.updateData == 'function') {
            e.updateData(data)
          }
        })
      })
    },
    getVmiHouse() {
      this.$API.vmi
        .getVmiHouse({
          page: {
            current: 1,
            size: 10
          }
        })
        .then((res) => {
          this.vmiHouseOptions = res.data.records
        })
    },

    handleCompanyChange(e) {
      this.FactoryParam.parentId = e.itemData.id
      this.FactoryParam.tenantId = e.itemData.tenantId
      this.getFactoryOptions(this.FactoryParam)
    },
    initDialog() {
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      let stockString = ''
      let stockArr = JSON.parse(JSON.stringify(this.formData.stockType))
      stockArr.forEach((item, index) => {
        if (this.formData.stockType.length - 1 != index) {
          stockString += item + ','
        } else {
          stockString += item
        }
      })
      let formObj = JSON.parse(JSON.stringify(this.formData))
      formObj.stockType = stockString
      // 如果是供方本厂，那么 “是否开启批次管理”、“是否需要IQC判定”不显示，且默认值为否-0
      if (formObj.vmiWarehouseType == 1) {
        formObj.needIqc = 0
        formObj.needBatch = 0
      }
      if (formObj.vmiWarehouseType == 2) {
        formObj.needIqc = 0
      }
      // let _this = this;
      this.supplierOptions.forEach((item) => {
        // if (item.orgCode === formObj.companyCode) {
        //   formObj.companyName = item.supplierName;
        // }
        if (item.supplierCode === formObj.supplierCode) {
          formObj.companyCode = item.supplierCode
          formObj.companyName = item.supplierName
        }
      })

      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$API.vmi.addWarehose(formObj).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('执行成功'), type: 'success' })
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .e-searcher {
  width: 100% !important;
}
</style>
