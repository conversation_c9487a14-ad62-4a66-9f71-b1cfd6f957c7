<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules">
      <mt-form-item prop="companyCode" :label="$t('采方公司')">
        <mt-select
          v-model="formData.companyCode"
          :data-source="companyOptions"
          :disabled="type"
          :show-clear-button="false"
          :fields="{ text: 'showLabel', value: 'orgCode' }"
          :placeholder="$t('请选择业务公司')"
          @change="handleCompanyChange"
          :allow-filtering="true"
          filter-type="Contains"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="siteCode" :label="$t('工厂')">
        <mt-select
          v-model="formData.siteCode"
          :data-source="factoryOptions"
          :disabled="type"
          :show-clear-button="false"
          :fields="{ text: 'showLabel', value: 'siteCode' }"
          :placeholder="$t('请选择工厂')"
          :allow-filtering="true"
          filter-type="Contains"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="vmiWarehouseType" :label="$t('VMI仓库类型')">
        <mt-select
          v-model="formData.vmiWarehouseType"
          :disabled="type"
          :data-source="[
            { text: $t('SRM管理库存'), value: 0 },
            { text: $t('原厂'), value: 1 },
            { text: $t('WMS管理库存'), value: 2 }
          ]"
          :show-clear-button="false"
          :fields="{ text: 'text', value: 'value' }"
          :placeholder="$t('请选择VMI仓库类型')"
        ></mt-select>
      </mt-form-item>

      <!-- 场外 | 场内 -->
      <template v-if="formData.vmiWarehouseType === 0 || formData.vmiWarehouseType === 2">
        <mt-form-item prop="thirdPartyLogisticsCode" :label="$t('VMI管理方')">
          <mt-select
            v-model="formData.thirdPartyLogisticsCode"
            :data-source="supplierThirdOptions"
            :disabled="type && formData.vmiWarehouseType === 0"
            :show-clear-button="false"
            :allow-filtering="true"
            @change="thirdPartyLogisticsCodeChange"
            :filtering="getThirdParty"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :placeholder="$t('请选择VMI管理方')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="vmiWarehouseCode" :label="$t('VMI仓')">
          <mt-select
            v-model="formData.vmiWarehouseCode"
            :data-source="vmiHouseOptions"
            :disabled="type"
            :show-clear-button="false"
            :fields="{ text: 'label', value: 'vmiWarehouseCode' }"
            :placeholder="$t('请输入VMI编码或名称')"
            :filtering="getVmiHouse"
            :allow-filtering="true"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="supplierCode" :label="$t('供应商名称')">
          <mt-select
            v-model="formData.supplierCode"
            :data-source="supplierOptions"
            :disabled="type"
            :show-clear-button="false"
            :allow-filtering="true"
            :filtering="getSupplier"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :placeholder="$t('请选择供应商编码')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="needItem" :label="$t('是否管理物料')">
          <!-- :disabled="type && formData.vmiWarehouseType === 0" -->
          <mt-select
            v-model="formData.needItem"
            :data-source="operateTypeOptions"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="goodsReceiptType" :label="$t('VMI入库类型')">
          <mt-select
            :disabled="type && formData.vmiWarehouseType === 0"
            v-model="formData.goodsReceiptType"
            :data-source="storageTypeOptions"
            :show-clear-button="true"
            :multiline="false"
            :placeholder="$t('请输入VMI入库类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="advanceTime" :label="$t('入库提前天数')">
          <mt-input-number
            v-model="formData.advanceTime"
            :disabled="type && formData.vmiWarehouseType === 0"
            :show-clear-button="true"
            :multiline="false"
            :placeholder="$t('请输入入库提前天数')"
          ></mt-input-number>
        </mt-form-item>
      </template>

      <!-- 供方本厂 -->
      <template v-else-if="formData.vmiWarehouseType === 1">
        <mt-form-item prop="supplierCode" :label="$t('VMI管理方')">
          <mt-select
            v-model="formData.supplierCode"
            :data-source="supplierOptions"
            :show-clear-button="false"
            :disabled="type"
            :allow-filtering="true"
            :filtering="getSupplier"
            @change="thirdPartyLogisticsCodeChange"
            :fields="{ text: 'showLabel', value: 'supplierCode' }"
            :placeholder="$t('请选择VMI管理方')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="vmiWarehouseCode" :label="$t('VMI仓编码')">
          <mt-select
            v-model="formData.vmiWarehouseCode"
            :data-source="vmiHouseOptions"
            :disabled="type"
            :show-clear-button="false"
            :fields="{ text: 'vmiWarehouseCode', value: 'vmiWarehouseCode' }"
            :placeholder="$t('请输入VMI编码或名称')"
            :filtering="getVmiHouse"
            :allow-filtering="true"
          ></mt-select>
        </mt-form-item>
      </template>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
import { judgeFormatCodeName } from '@/utils/utils'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      operateTypeOptions: [
        // { text: this.$t("定时发布"), value: "0" },
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ], // 定时发布/反馈 下拉数据
      storageTypeOptions: [],
      dialogTitle: this.$t('新建'),
      defaultRules: {
        companyCode: [
          {
            required: true,
            message: this.$t('请选择采方公司'),
            trigger: 'blur'
          }
        ],
        siteCode: [{ required: true, message: this.$t('请输入工厂'), trigger: 'blur' }],
        vmiWarehouseType: [
          {
            required: true,
            message: this.$t('请选择VMI仓库类型'),
            trigger: 'blur'
          }
        ],
        thirdPartyLogisticsCode: [
          {
            required: true,
            message: this.$t('请选择第三方物流公司'),
            trigger: 'blur'
          }
        ],
        vmiWarehouseCode: [
          {
            required: true,
            message: this.$t('请输入VMI仓库名称'),
            trigger: 'blur'
          }
        ],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择供应商编码或者名称'),
            trigger: 'blur'
          }
        ],
        needItem: [
          {
            required: true,
            message: this.$t('请选择是否管理物料'),
            trigger: 'blur'
          }
        ],
        goodsReceiptType: [
          {
            required: true,
            message: this.$t('请选择VMI送货类型'),
            trigger: 'blur'
          }
        ]
      },
      advanceTimeRules: {
        advanceTime: [
          {
            required: true,
            message: this.$t('请输入提前送货天数'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {
        advanceTime: null,
        buyerTenantId: 0,
        companyCode: '',
        companyName: '',
        goodsReceiptType: '',
        goodsReceiptTypeDesc: '',
        needBatch: 0,
        needItem: 0,
        processorTenantId: 0,
        siteCode: '',
        siteName: '',
        status: 1, //永远有效 和后端约定
        supplierCode: null,
        supplierName: '',
        thirdPartyLogisticsCode: null,
        thirdPartyLogisticsName: '',
        vmiWarehouseCode: '',
        vmiWarehouseName: ''
      },
      type: false,
      factoryId: '',
      companyId: '',
      supplierThird: '',
      supplier: '',
      warehouseCode: '',
      companyOptions: [],
      factoryOptions: [],
      vmiHouseOptions: [],
      supplierThirdOptions: [],
      supplierOptions: [],
      FactoryParam: { parentId: '', tenantId: '' }
    }
  },
  computed: {
    rules() {
      return this.formData.goodsReceiptType === 'LX-03'
        ? { ...this.defaultRules }
        : { ...this.defaultRules, ...this.advanceTimeRules }
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.getCompanyOptions()
    // this.getSupplier();
    // this.getThirdParty();
    this.getVmiHouse()
    this.getStorageTypeOptions()
    this.getVmiHouse = utils.debounce(this.getVmiHouse, 300)
    if (this.modalData?.row) {
      this.formData = {}
      this.formData = this.modalData.row

      this.getVMICode()
    }
    this.type = this.modalData.title === this.$t('编辑') ? true : false
  },
  methods: {
    getVMICode() {
      this.$API.vmi.findByCode({ code: this.formData.vmiWarehouseCode }).then((res) => {
        this.formData.vmiWarehouseType = res.data.vmiWarehouseType

        this.getVmiHouse()
      })
    },
    // 获取入库类型数据
    getStorageTypeOptions() {
      this.$API.masterData
        .getDictCode({
          dictCode: 'vmiGoodsReceiptType'
        })
        .then((res) => {
          this.storageTypeOptions = res.data.map((item) => ({
            text: item.itemName,
            value: item.itemCode
          }))
        })
    },
    getCompanyOptions() {
      //查询业务公司下拉数据
      this.$API.masterData
        .OrgFindSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true,
          organizationIds: []
        })
        .then((res) => {
          console.log(res.data, 'res.datares.data')
          let companyOptions = res.data
          companyOptions.forEach((i) => (i.showLabel = judgeFormatCodeName(i.orgCode, i.orgName)))
          this.companyOptions = companyOptions
        })
    },
    getFactoryOptions(params) {
      if (!params) return
      //查询业务公司下拉数据
      this.$API.masterData.findSiteInfoByParentId(params).then((res) => {
        console.log(res.data, '-=-=-=')
        let factoryOptions = res.data
        factoryOptions.forEach((i) => (i.showLabel = judgeFormatCodeName(i.siteCode, i.siteName)))
        this.factoryOptions = factoryOptions
      })
    },
    getThirdParty(e, value) {
      this.$API.masterData
        .conditionGetSupplier({
          organizationCode: value ? value : this.formData.companyCode,

          fuzzyNameOrCode: e?.text ? e.text : this.formData.thirdPartyLogisticsCode
        })
        .then((res) => {
          let data = res.data.filter((item) => item.supplierName)
          let supplierThirdOptions = data
          supplierThirdOptions.forEach(
            (i) => (i.showLabel = judgeFormatCodeName(i.supplierCode, i.supplierName))
          )
          this.supplierThirdOptions = supplierThirdOptions
          this.$nextTick(() => {
            if (e.updateData && typeof e.updateData == 'function') {
              e.updateData(data)
            }
          })
        })
    },
    // 获取供应商下拉
    getSupplier(e, value) {
      this.$API.masterData
        .getSupplier({
          organizationCode: value ? value : this.formData.companyCode,
          fuzzyNameOrCode: e?.text ? e.text : this.formData.supplierCode
        })
        .then((res) => {
          let data = res.data.filter((item) => item.supplierName)
          let supplierOptions = data
          supplierOptions.forEach(
            (i) => (i.showLabel = judgeFormatCodeName(i.supplierCode, i.supplierName))
          )
          this.supplierOptions = supplierOptions
          this.$nextTick(() => {
            if (e.updateData && typeof e.updateData == 'function') {
              e.updateData(data)
            }
          })
        })
    },
    thirdPartyLogisticsCodeChange() {
      this.getVmiHouse()
    },
    getVmiHouse(e = { text: '' }) {
      let obj = {
        defaultRules: [
          {
            field: 'companyCode',
            operator: 'contains',
            value:
              this.formData.vmiWarehouseType === 0 || this.formData.vmiWarehouseType === 2
                ? this.formData.thirdPartyLogisticsCode
                : this.formData.supplierCode
          },
          {
            field: 'vmiWarehouseType',
            operator: 'contains',
            value: this.formData.vmiWarehouseType
          }
        ],
        rules: [
          {
            field: 'vmiWarehouseCode',
            condition: 'or',
            value: e.text,
            type: 'string',
            operator: 'contains'
          },
          {
            field: 'vmiWarehouseName',
            condition: 'or',
            value: e.text,
            type: 'string',
            operator: 'contains'
          }
        ],
        page: {
          current: 1,
          size: 100
        }
      }
      this.$API.vmi.getVmiHouse(obj).then((res) => {
        const list = res?.data?.records || []
        list.forEach((item) => {
          item.label = `${item.vmiWarehouseCode}-${item.vmiWarehouseName}`
        })
        this.vmiHouseOptions = list
      })
    },

    handleCompanyChange(e) {
      this.FactoryParam.parentId = e.itemData.id
      this.FactoryParam.tenantId = e.itemData.tenantId
      this.getFactoryOptions(this.FactoryParam)
      this.getSupplier('', e.itemData.orgCode)
      this.getThirdParty('', e.itemData.orgCode)
    },
    initDialog() {
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      let _this = this
      this.$refs.ruleForm.validate((valid) => {
        this.companyOptions.forEach((item) => {
          if (item.orgCode === _this.formData.companyCode) _this.formData.companyName = item.orgName
        })
        this.$API.vmi.saveBeforeCheck(_this.formData).then((res) => {
          if (res.code == 200 && res.data) {
            if (this.modalData?.row) {
              this.$API.vmi.updatewarehoseAndSupplier(_this.formData).then((res) => {
                if (res.code === 200) {
                  this.$emit('confirm-function')
                  this.$toast({
                    content: this.$t('执行成功'),
                    type: 'success'
                  })
                } else {
                  this.$toast({
                    content: this.$t('执行失败'),
                    type: 'error'
                  })
                }
              })
              return
            }
            if (valid) {
              this.$API.vmi.addwarehoseAndSupplier(_this.formData).then((res) => {
                if (res.code === 200) {
                  this.$emit('confirm-function')
                  this.$toast({
                    content: this.$t('执行成功'),
                    type: 'success'
                  })
                } else {
                  this.$toast({
                    content: this.$t('执行失败'),
                    type: 'error'
                  })
                }
              })
            }
          } else if (res.code == 200 && !res.data) {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('已存在相同VMI仓配置关系，是否确认。')
              },
              success: () => {
                if (this.modalData?.row) {
                  this.$API.vmi.updatewarehoseAndSupplier(_this.formData).then((res) => {
                    if (res.code === 200) {
                      this.$emit('confirm-function')
                      this.$toast({
                        content: this.$t('执行成功'),
                        type: 'success'
                      })
                    } else {
                      this.$toast({
                        content: this.$t('执行失败'),
                        type: 'error'
                      })
                    }
                  })
                  return
                }
                if (valid) {
                  this.$API.vmi.addwarehoseAndSupplier(_this.formData).then((res) => {
                    if (res.code === 200) {
                      this.$emit('confirm-function')
                      this.$toast({
                        content: this.$t('执行成功'),
                        type: 'success'
                      })
                    } else {
                      this.$toast({
                        content: this.$t('执行失败'),
                        type: 'error'
                      })
                    }
                  })
                }
              }
            })
          }
        })
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
<style lang="scss" scoped></style>
