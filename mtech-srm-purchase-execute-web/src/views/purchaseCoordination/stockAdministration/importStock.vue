// VMI供应商导入库存
<template>
  <div class="full-height pt20">
    <top-info
      class="flex-keep"
      :header-info="headerInfo"
      @goBack="goBack"
      @confirmBtn="confirmBtn"
    ></top-info>
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config/importStock.js'
import TopInfo from './components/topInfo.vue'
export default {
  components: {
    TopInfo
  },
  data() {
    return {
      headerInfo: {
        confirm: true
      },
      pageConfig: [
        {
          title: this.$t('汇总列表'),
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [],
              [
                'Filter',
                {
                  id: 'export',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                },
                'Refresh',
                'Setting'
              ]
            ]
          },
          gridId: '51fc30cf-267c-4a4b-800e-088cb89d2349',
          grid: {
            columnData: columnData,
            // lineIndex: 1,
            // autoWidthColumns: columnData.length + 1,
            dataSource: [],
            // asyncConfig: {
            //   url: "/srm-purchase-execute/tenant/pe/business/configs",
            //   recordsPosition: "data",
            // },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },
  created() {
    // 判断是不是详情页
    if (this.$route.query.details) {
      this.headerInfo.confirm = false
      this.pageConfig[0].toolbar.tools[1] = ['Filter', 'Refresh', 'Setting']
      this.pageConfig[0].useToolTemplate = false
    }
    this.getDetails()
  },
  methods: {
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    // 确认
    confirmBtn() {
      this.$dialog({
        data: {
          title: this.$t('接受'),
          message: this.$t('确认接受吗？')
        },
        success: () => {
          this.confirm()
        }
      })
    },
    handleClickToolBar(e) {
      if (e.grid.getSelectedRecords().length <= 0 && e.toolbar.id === 'export') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      } else {
        // 调用导出方法·
        let _selectRows = e.grid.getSelectedRecords()
        console.log(_selectRows, 7777)
      }
    },
    // 请求详情
    // 三方公用一个详情接口
    getDetails() {
      let _that = this
      let obj = {
        id: this.$route.query.id
      }
      this.$API.purchaseCoordination.postBuyerWarehousingReturnDetail(obj).then((res) => {
        if (res.code === 200) {
          _that.headerInfo = res.data
          _that.headerInfo.siteCode = res.data.siteCode + '-' + res.data.siteName
          _that.headerInfo.supplierCode = res.data.supplierCode + '-' + res.data.supplierName
          _that.headerInfo.vmiWarehouseCode =
            res.data.vmiWarehouseCode + '-' + res.data.vmiWarehouseName
          _that.pageConfig[0].grid.dataSource = res.data.vmiOrderItemResponses
        }
      })
    },
    // 确认
    confirm() {
      // let _that = this
      let obj = {
        ids: []
      }
      obj.ids.push(this.$route.query.id)
      this.$API.purchaseCoordination.postPurchaseStockImportConfirm(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('确认成功'), type: 'success' })
          this.goBack()
        } else {
          this.$toast({ content: this.$t('确认失败'), type: 'error' })
        }
      })
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
.titleColor {
  color: #00469c;
}
</style>
