// VMI库存调整管理
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      class="templateStyle"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnData, columnData2 } from './config/adjustmentRecord.js'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    // addDialog: require("./components/addDialog.vue").default,
  },
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false,
          useCombinationSelection: false,
          title: this.$t('汇总列表'),
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'synchronous',
                  icon: 'icon_table_restart',
                  title: this.$t('同步WMS')
                }
              ],
              [
                'Filter',
                {
                  id: 'export2',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                },
                'Refresh',
                'Setting'
              ]
            ]
          },
          gridId: 'ce042850-d99e-49d2-8b66-e0931f3d2ad0',
          grid: {
            columnData: columnData,
            dataSource: [
              {
                billCode: '01',
                status: 1,
                typeCode: 'leixing',
                factoryCode: '001',
                factoryName: this.$t('京东工厂'),
                warehouseName: this.$t('仓库名称'),
                materialCode: '0001',
                warehousedDescribe: this.$t('仓描述'),
                supplierCode: '002222',
                supplierDescribe: this.$t('供应商描述'),
                makeDate: '2022-03-04',
                makePeople: this.$t('制单人')
              }
            ],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiAllocationOrder/buyer-page-query',
              recordsPosition: 'data.records'
            }
            // frozenColumns: 1
          }
        },
        {
          useToolTemplate: false,
          useCombinationSelection: false,
          title: this.$t('明细列表'),
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [],
              [
                'Filter',
                {
                  id: 'export3',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                },
                'Refresh',
                'Setting'
              ]
            ]
          },
          gridId: '7c725496-abda-41de-83c2-fb00aead94c7',
          grid: {
            columnData: columnData2,
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiAllocationOrder/buyer-page-query-ext-detail',
              recordsPosition: 'data.records'
            }
            // frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    // 跳转详情
    handleClickCellTitle(e) {
      if (e.field === 'vmiOrderCode') {
        let obj = {
          details: '',
          orderStatus: e.data.vmiOrderType,
          id: e.data.id,
          status: e.data.status
        }
        // if(e.data.status == 0 || e.data.status == 1){
        if (e.data.status == 1) {
          obj.details = 0
        } else {
          obj.details = 1
        }
        // 根据类型判断   类型字段暂时没有返回   先用状态代替（自定义代替）
        if (e.data.vmiOrderType === 4) {
          // 跳入导入
          this.redirectPage('purchase-execute/coordination-stock-stock', obj)
        } else if (e.data.vmiOrderType === 6) {
          // 跳入物料替换
          this.redirectPage('purchase-execute/coordination-stock-replace', obj)
        } else if (e.data.vmiOrderType === 5) {
          // 跳入物料调拨
          this.redirectPage('purchase-execute/coordination-stock-allocation', obj)
        } else if (e.data.vmiOrderType === 3) {
          // 跳入退货单
          obj.id = e.data.vmiOrderId ?? e.data.id
          obj.status = e.data.status
          this.redirectPage('purchase-execute/coordination-returnGoods-details', obj)
        } else if (e.data.vmiOrderType === 1) {
          // 跳入退货单
          obj.id = e.data.id
          obj.status = e.data.status
          this.redirectPage('purchase-execute/coordination-warehousing-establish', obj)
        }
      }
    },
    handleClickToolBar(e) {
      if (e.grid.getSelectedRecords().length > 0 && e.toolbar.id === 'export') {
        // 导出
        console.log(e.grid.getSelectedRecords())
      } else if (e.toolbar.id === 'export2' || e.toolbar.id === 'export3') {
        let obj = JSON.parse(
          sessionStorage.getItem('e2ae7eab-f90b-43c7-9e16-713a0dd51ccd')
        )?.visibleCols
        if (e.toolbar.id === 'export3') {
          obj = JSON.parse(
            sessionStorage.getItem('91b28744-bc18-479d-b884-5de1abd0f629')
          )?.visibleCols
        }
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          const columnDataList = e.toolbar.id === 'export2' ? columnData : columnData2
          columnDataList.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        const queryBuilderRules =
          this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        const params = {
          page: { current: 1, size: 10000 },
          ...queryBuilderRules
        } // 筛选条件
        // 汇总导出
        this.$store.commit('startLoading')
        if (e.toolbar.id === 'export2') {
          params.sortedColumnStr = field?.length ? field.join(',') : ''
          this.$API.supplierCoordination.buyerSumExcelExport(params).then((res) => {
            this.$store.commit('endLoading')
            const fileName = getHeadersFileName(res)
            download({ fileName: `${fileName}`, blob: res.data })
          })
          return
        }
        // 明细导出
        this.$API.supplierCoordination.buyerExcelExport(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
      } else if (e.toolbar.id === 'refreshDataByLocal') {
        // 调用刷新
      } else if (e.grid.getSelectedRecords().length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      } else if (e.toolbar.id === 'synchronous') {
        let _selectRecords = e.grid.getSelectedRecords()
        if (_selectRecords.length <= 0 || _selectRecords.length > 1) {
          this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
          return
        }
        this.synchronousWms(_selectRecords[0])
      }
    },
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },
    // 删除
    delete(data) {
      let obj = {
        ids: []
      }
      obj.ids.push(data)
      this.$API.supplierCoordination.postSupplierStockImportDelete(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('删除失败'), type: 'error' })
        }
      })
    },
    // 同步WMS
    synchronousWms(data) {
      if (data.wmsSyncStatus === 1) {
        this.$toast({
          content: this.$t('入库单已成功同步WMS，无需重复同步'),
          type: 'warning'
        })
        return
      }
      this.$API.purchaseCoordination.synchronousWms({ id: data.id }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('同步成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('同步失败'), type: 'warning' })
        }
      })
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.$dialog({
          data: {
            title: this.$t('删除'),
            message: this.$t('确认删除吗？')
          },
          success: () => {
            this.delete(e.data.id)
          }
        })
      } else if (e.tool.id == 'accept') {
        this.$dialog({
          data: {
            title: this.$t('接受'),
            message: this.$t('确认接受吗？')
          },
          success: () => {
            this.accept(e.data.id, e.data.vmiOrderType)
          }
        })
      } else if (e.tool.id == 'cancel') {
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消吗？')
          },
          success: () => {
            this.cancel(e.data.id, e.data.vmiOrderType)
          }
        })
      }
    },
    // 刷新页面
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 接收 采方，供方公用一个接口
    accept(data, valType) {
      let apiInterface = ''
      if (valType == 5) {
        apiInterface = 'purchaseAllocationReceive'
      } else if (valType == 6) {
        apiInterface = 'purchaseReplaceReceive'
      }
      let obj = {
        ids: []
      }
      obj.ids.push(data)
      this.$API.purchaseCoordination[apiInterface](obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('提交失败'), type: 'error' })
        }
      })
    },
    // 取消 三方共用一个接口
    cancel(data, valType) {
      let apiInterface = ''
      if (valType == 5) {
        apiInterface = 'purchaseAllocationBackOff'
      } else if (valType == 6) {
        apiInterface = 'purchaseReplaceBackOff'
      }
      let obj = {
        ids: []
      }
      obj.ids.push(data)
      this.$API.purchaseCoordination[apiInterface](obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('取消成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('取消失败'), type: 'error' })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.set-country {
  height: 100%;
}
.titleColor {
  color: #00469c;
}
/deep/ .templateStyle {
  .template-wrap {
    .e-grid .e-table {
      & thead th:first-child {
        position: sticky;
        left: 0;
        z-index: 1;
      }
      & tbody td:first-child {
        position: sticky;
        left: 0;
        z-index: 1;
        border-right: 1px solid var(--plugin-dg-shadow-color);
        background-color: #fff;
      }
    }
  }
}
</style>
