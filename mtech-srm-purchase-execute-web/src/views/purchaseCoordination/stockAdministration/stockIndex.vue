<!--
 * @Author: zhaoriyang3 <EMAIL>
 * @Date: 2022-05-18 09:53:59
 * @LastEditors: zhaoriyang3 <EMAIL>
 * @LastEditTime: 2022-05-24 13:39:10
 * @FilePath: \mtech-srm-purchase-execute-web\src\views\purchaseCoordination\stockAdministration\stockIndex.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
//采方 VMI库存查询
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config/index.js'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  data() {
    return {
      pageConfig: [
        {
          title: this.$t('汇总列表'),
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          activatedRefresh: false,

          toolbar: {
            tools: [
              [
                {
                  id: 'checkEditInfo',
                  title: this.$t('查看变更记录'),
                  icon: 'icon_solid_Createorder'
                }
              ],
              [
                'Filter',
                {
                  id: 'export',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                },
                'Refresh',
                'Setting'
              ]
            ]
          },
          gridId: '3e214a73-6a3a-45f4-8659-bec44a1f7855',
          grid: {
            columnData: columnData,
            // lineIndex: 1,
            // autoWidthColumns: columnData.length + 1,
            dataSource: [
              {
                factoryCode: '01',
                factoryName: this.$t('测试1'),
                supplierCode: '001',
                supplierName: this.$t('京东'),
                warehouseCode: this.$t('仓库编码'),
                warehouseName: this.$t('仓库名称'),
                materialCode: '0001',
                materialName: this.$t('物料名称'),
                purchaseGroup: this.$t('采购组'),
                stockStatus: this.$t('充足'),
                batchNumber: this.$t('第一批次'),
                stockNumber: '99',
                founder: 'jcs'
              }
            ],
            asyncConfig: {
              ignoreDefaultSearch: true,
              url: '/srm-purchase-execute/tenant/vmi_stock/stock-page-query',
              recordsPosition: 'data.records'
            },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    handleClickToolBar(e) {
      console.log('exexex', e)
      const { grid } = e
      if (e.toolbar.id === 'export') {
        this.downloadTemplate()
        return
      }
      const records = grid.getSelectedRecords()
      if (!records || records.length === 0) {
        this.$toast({
          type: 'warning',
          content: this.$t('请选择一条数据')
        })
        return
      }
      if (records.length > 1) {
        this.$toast({
          type: 'warning',
          content: this.$t('只能查看单条数据的变更记录')
        })
        return
      }
      // if (![1, '1'].includes(records[0].vmiWarehouseType)) {
      //   this.$toast({
      //     type: 'warning',
      //     content: this.$t('请选择一条VMI仓类型为原厂的数据！')
      //   })
      //   return
      // }
      if (e.toolbar.id === 'checkEditInfo') {
        sessionStorage.setItem('checkEditHeaderInfo', JSON.stringify(records[0]))
        // this.$router.push({
        //   path: 'supplier-stock-administration-change-Info',
        //   query: {
        //     date: new Date().getTime()
        //   }
        // })
        this.$dialog({
          modal: () => import('@/views/supplierCoordination/stockAdministration/checkEditInfo.vue'),
          data: {
            headerTitle: this.$t('查看变更记录')
          },
          success: () => {}
        })
      }
    },
    downloadTemplate() {
      let obj = JSON.parse(
        sessionStorage.getItem('3e214a73-6a3a-45f4-8659-bec44a1f7855')
      )?.visibleCols
      let field = []
      if (obj !== undefined && obj.length) {
        obj.forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      } else {
        columnData.forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      }
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.purchaseCoordination.vmiStockExport(params, field).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
        this.$store.commit('endLoading')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.set-country {
  height: 100%;
}
/deep/ .e-grid td.e-active {
  z-index: auto;
}
</style>
