import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    // width: "200",
    field: 'rowNum',
    headerText: i18n.t('行号')
  },
  // {
  //   width: "200",
  //   field: "factoryCode",
  //   headerText: i18n.t("工厂编号"),
  // },
  // {
  //   width: "150",
  //   field: "factoryDescribe",
  //   headerText: i18n.t("工厂名称"),
  // },
  {
    // width: "150",
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    // width: "150",
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  // {
  //   width: "150",
  //   field: "supplierCode",
  //   headerText: i18n.t("供应商编码"),
  // },
  // {
  //   width: "150",
  //   field: "supplierDescribe",
  //   headerText: i18n.t("供应商描述"),
  // },
  // {
  //   width: "150",
  //   field: "positionCode",
  //   headerText: i18n.t("仓位编码"),
  // },
  // {
  //   width: "150",
  //   field: "positionDescribe",
  //   headerText: i18n.t("仓位描述"),
  // },
  {
    field: 'count',
    headerText: i18n.t('库存数量')
  },
  {
    field: 'itemUnitDescription',
    headerText: i18n.t('单位')
  },

  {
    field: 'purchaseGroupName',
    headerText: i18n.t('采购组')
  },

  {
    // width: "150",
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    // width: "150",
    field: 'createPeople',
    headerText: i18n.t('创建人')
  }
]
