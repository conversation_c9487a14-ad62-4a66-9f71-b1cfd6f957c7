import Vue from 'vue'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { ComponentChangeType, ComponentType } from './constant'
import { rowDataTemp } from './variable'
import bigDecimal from 'js-big-decimal'
import { cloneDeep } from 'lodash'
import { numberInputOnKeyDown } from '@/utils/utils'

export const ColumnComponent = {
  // 时间日期显示
  timeDate: (args) => {
    const { dataKey, hasTime } = args

    const template = () => {
      return {
        template: Vue.component('date', {
          template: `<div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div><div>{{data[dataKey] | dateFormat}}</div></div>`,
          data: function () {
            return { data: {}, dataKey, hasTime }
          },
          filters: {
            dateFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
              } else {
                str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
              }

              return str
            },
            timeFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({ formatString: 'HH:MM:SS', value })
              } else {
                str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
              }

              return str
            }
          }
        })
      }
    }

    return template
  },
  // 明细行文件信息
  fileBaseInfoList: () => {
    return {
      template: Vue.component('fileBaseInfoList', {
        template: `<div @click="showFileBaseInfo" class="cell-operable-title">{{data.fileBaseInfoList | listNumFormat}}</div>`,
        data: function () {
          return { data: {} }
        },
        filters: {
          listNumFormat(value) {
            if (value && value.length > 0) {
              return value.length
            } else {
              return ''
            }
          }
        },
        methods: {
          showFileBaseInfo() {
            this.$parent.$emit('showFileBaseInfo', {
              index: this.data.index,
              value: this.data.fileBaseInfoList
            })
          }
        }
      })
    }
  },
  // 空的显示
  empty: () => {
    return {
      template: Vue.component('emptyComponent', {
        template: `<div></div>`,
        data: function () {
          return {
            data: {}
          }
        }
      })
    }
  },
  // 不可编辑的文字显示
  text: (args) => {
    const {
      dataKey,
      cellTools,
      valueConverter,
      isAbleClick,
      componentType,
      infoMsg,
      showInfoMsgCondition,
      editDisabled
    } = args
    const template = () => {
      return {
        template: Vue.component('textComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <span
                :class="[getCssClass(data[dataKey])]"
                @click.stop="clickCellTitle({data, field: dataKey, componentType})"
                >{{data[dataKey] | format}}</span>
            </div>
            <div class="column-tool mt-flex invite-btn" v-if="haveShowCellTool">
              <div v-for="(cellTool, index) in cellTools"
                :key="index" :id="cellTool.id"
                class="template-svg"
                v-if="!cellTool.visibleCondition || cellTool.visibleCondition(data)"
                @click.stop="clickCellTool({tool:cellTool, data})"
                ><MtIcon :name="cellTool.icon" />&nbsp<span class="icon-title">{{cellTool.title}}</span>
              </div>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              cellTools,
              haveShowCellTool: false,
              componentType,
              isEditDisabled: false // 默认按照组件类型判断是否可编辑，可编辑，特殊状态的数据在编辑状态下不可编辑
            }
          },
          mounted() {
            this.dealWithHaveShowCellTool()
            if (componentType === ComponentType.edit) {
              // 监听变化
              this.onComponentChange()
              this.isEditDisabled = this.dealWithEditDisabled()
            }
          },
          filters: {
            format(value) {
              let data = value
              if (valueConverter?.type === 'map' && valueConverter?.map?.length) {
                // 转换
                const mapList = valueConverter.map
                const findItem = mapList.find((item) => item.value === value)
                data = findItem?.text
              } else if (infoMsg) {
                if (showInfoMsgCondition && showInfoMsgCondition(value)) {
                  data = infoMsg
                }
              }
              return data
            }
          },
          methods: {
            dealWithHaveShowCellTool() {
              if (cellTools?.length > 0) {
                for (let i = 0; i < cellTools.length; i++) {
                  const cellTool = cellTools[i]
                  if (!cellTool.visibleCondition || cellTool.visibleCondition(this.data)) {
                    this.haveShowCellTool = true
                    break
                  }
                }
              }
            },
            // 监听变化
            onComponentChange() {
              // 监听被变化
              this.$bus.$on(`invoiceCollaborationDetailSupplierColumnChange`, (e) => {
                const { modifiedKeys: _modifiedKeys, data, changeType } = e
                if (_modifiedKeys.includes(dataKey)) {
                  if (changeType === ComponentChangeType.change) {
                    this.data[dataKey] = cloneDeep(data[dataKey])
                    rowDataTemp[rowDataTemp.length - 1][dataKey] = data[dataKey]
                  }
                }
              })
            },
            getCssClass(value) {
              let cssClass = ''
              if (valueConverter?.type === 'map' && valueConverter?.map?.length) {
                const mapList = valueConverter.map
                const findItem = mapList.find((item) => item.value === value)
                cssClass = findItem?.cssClass || ''
              } else if (isAbleClick) {
                cssClass = 'able-click-field'
              }
              return cssClass
            },
            clickCellTool(data) {
              this.$parent.$emit('handleClickCellTool', data)
            },
            clickCellTitle(data) {
              if (isAbleClick) {
                if (this.isEditDisabled) {
                  this.$parent.$emit('handleClickCellTitle', {
                    ...data,
                    componentType: ComponentType.view
                  })
                } else {
                  this.$parent.$emit('handleClickCellTitle', data)
                }
              }
            },
            dealWithEditDisabled() {
              let isEditDisabled = false
              if (editDisabled && editDisabled(this.data)) {
                isEditDisabled = true
              }
              return isEditDisabled
            }
          }
        })
      }
    }
    return template
  },
  // 下拉框 编辑
  select: (args) => {
    const {
      dataKey,
      selectOptions,
      fields,
      allowFiltering,
      showClearBtn,
      modifiedKeys,
      modifiedRelation
    } = args
    const template = () => {
      return {
        template: Vue.component('selectComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-select
                @change="selectChange"
                v-model="data[dataKey]"
                :allow-filtering="allowFiltering"
                :data-source="selectOptions"
                :show-clear-button="showClearBtn"
                :placeholder="$t('请选择')"
                :fields="fields"
              ></mt-select>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              selectOptions,
              fields,
              dataKey,
              allowFiltering,
              showClearBtn,
              ComponentChangeType
            }
          },
          mounted() {},
          methods: {
            // 修改中的值
            selectChange(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e.value
              // 触发改变的值
              this.triggerCodeChange(e.itemData)
            },
            // 触发改变的值
            triggerCodeChange(selectData) {
              const args = {
                requestKey: dataKey, // 触发请求的key
                modifiedKeys: modifiedKeys || [], // 要被修改的key列表
                changeType: ComponentChangeType.code, // 修改类型
                data: selectData, // 发出的值
                modifiedRelation // 对应数据源中的 key 关系
              }
              this.$bus.$emit('invoiceCollaborationDetailSupplierColumnChange', args)
            }
          }
        })
      }
    }
    return template
  },
  // 文本 编辑
  input: (args) => {
    const { dataKey, showClearBtn, maxlength, disabled } = args
    const template = () => {
      return {
        template: Vue.component('inputComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
                <mt-input
                  v-model="data[dataKey]"
                  :show-clear-button="showClearBtn"
                  :disabled="disabled"
                  :maxlength="maxlength"
                  @input="onInput"
                ></mt-input>
              </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              maxlength,
              disabled,
              showClearBtn
            }
          },
          mounted() {},
          methods: {
            onInput(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e
            }
          }
        })
      }
    }
    return template
  },
  // 数字 编辑
  number: (args) => {
    const {
      dataKey,
      maxKey,
      maximumValue,
      showClearBtn,
      precision,
      disabled,
      modifiedKeys,
      componentType
    } = args
    const template = () => {
      return {
        template: Vue.component('numberComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-input-number
                v-if="!disabled"
                v-model="data[dataKey]"
                :max="max"
                :min="min"
                :disabled="disabled"
                :precision="precision"
                :show-spin-button="false"
                :show-clear-button="showClearBtn"
                @input="onInput"
              ></mt-input-number>
              <span v-else>{{ data[dataKey] }}</span>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              min: 0,
              max: undefined,
              maxKey,
              precision,
              showClearBtn,
              numberInputOnKeyDown,
              disabled
            }
          },
          mounted() {
            this.max = this.data[maxKey] || maximumValue
            this.min = this.data['companyCode'] === '0530' ? -********* : 0
            if (componentType === ComponentType.edit) {
              // 监听变化
              this.onComponentChange()
            }
          },
          beforeDestroy() {
            this.$bus.$off('invoiceCollaborationDetailSupplierColumnChange')
          },
          methods: {
            onInput(e) {
              // 校验数量
              const numberOfChecks = this.data[this.maxKey]
              if (
                !isNaN(numberOfChecks) &&
                (isNaN(Number(e)) || Number(e) < 0 || e > numberOfChecks)
              ) {
                // 非数字、小于 0、大于 校验数量
                this.data[dataKey] = numberOfChecks
              } else {
                this.data[dataKey] = e
              }
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = this.data[dataKey]
              // 发票含税金额 改变
              this.handleTaxedAmount(this.data[dataKey])
              // 发票未税金额 改变
              this.handleUntaxedAmount(this.data[dataKey])
            },
            // 发票含税金额 改变
            handleTaxedAmount(value) {
              if (this.dataKey === 'invoiceTaxedAmount') {
                const invoiceTaxedAmount = value ?? 0 // 含税
                const invoiceUntaxAmount =
                  rowDataTemp[rowDataTemp.length - 1].invoiceUntaxAmount ?? 0 // 未税
                // 税额 = 含税 - 未税
                const invoiceTaxAmount = bigDecimal.subtract(invoiceTaxedAmount, invoiceUntaxAmount)
                rowDataTemp[rowDataTemp.length - 1].invoiceTaxAmount = invoiceTaxAmount
                // 改变 发票税额
                this.triggerCodeChange({
                  data: invoiceTaxAmount,
                  changeType: ComponentChangeType.change
                })
              }
            },
            // 发票未税金额 改变
            handleUntaxedAmount(value) {
              if (this.dataKey === 'invoiceUntaxAmount') {
                const invoiceTaxedAmount =
                  rowDataTemp[rowDataTemp.length - 1].invoiceTaxedAmount ?? 0 // 含税
                const invoiceUntaxAmount = value ?? 0 // 未税
                // 税额 = 含税 - 未税
                const invoiceTaxAmount = bigDecimal.subtract(invoiceTaxedAmount, invoiceUntaxAmount)
                rowDataTemp[rowDataTemp.length - 1].invoiceTaxAmount = invoiceTaxAmount
                // 改变 发票税额
                this.triggerCodeChange({
                  data: invoiceTaxAmount,
                  changeType: ComponentChangeType.change
                })
              }
            },
            // 触发改变的值
            triggerCodeChange(args) {
              const { data, changeType } = args
              const e = {
                requestKey: dataKey, // 触发请求的key
                modifiedKeys: modifiedKeys || [], // 要被修改的key列表
                changeType, // 修改类型
                data // 发出的值
              }
              this.$bus.$emit('invoiceCollaborationDetailSupplierColumnChange', e)
            },
            // 监听变化
            onComponentChange() {
              // 监听被变化
              this.$bus.$on(`invoiceCollaborationDetailSupplierColumnChange`, (e) => {
                const { modifiedKeys: _modifiedKeys, data, changeType } = e
                if (_modifiedKeys.includes(dataKey)) {
                  if (changeType === ComponentChangeType.change) {
                    if (typeof data === 'object') {
                      this.data[dataKey] = data[dataKey]
                      rowDataTemp[rowDataTemp.length - 1][dataKey] = data[dataKey]
                    } else {
                      this.data[dataKey] = data
                      rowDataTemp[rowDataTemp.length - 1][dataKey] = data
                    }
                  }
                }
              })
            }
          }
        })
      }
    }
    return template
  },
  // 时间输入
  timeInput: (args) => {
    const { dataKey, disabled, showClearBtn, allowEdit, isDate, isTime, isDateTime, maxDate } = args

    const template = () => {
      return {
        template: Vue.component('timeInputComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <mt-time-picker
              v-if="isTime"
              v-model="componentData"
              :disabled="disabled"
              :show-clear-button="showClearBtn"
              :allow-edit="allowEdit"
              @change="onChange"
              placeholder=""
            ></mt-time-picker>
            <mt-date-picker
              v-if="isDate"
              v-model="componentData"
              :disabled="disabled"
              :show-clear-button="showClearBtn"
              :allow-edit="allowEdit"
              :max="maxDate"
              @change="onChange"
              placeholder=""
            ></mt-date-picker>
            <mt-date-time-picker
              v-if="isDateTime"
              v-model="componentData"
              :disabled="disabled"
              :show-clear-button="showClearBtn"
              :allow-edit="allowEdit"
              @change="onChange"
              placeholder=""
            ></mt-date-time-picker>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              disabled,
              showClearBtn,
              allowEdit,
              isDate,
              isTime,
              isDateTime,
              maxDate,
              componentData: null // 组件内部绑定的变量
            }
          },
          filters: {},
          mounted() {
            this.componentData = this.formateInitData(this.data[dataKey])
          },
          methods: {
            formateInitData(value) {
              let date = null
              if (this.data[dataKey] == 0 || !this.data[dataKey]) {
                // 数据库时间戳默认值为 0，为 0 时不显示，为空时不显示
                return null
              } else if (typeof value === 'string') {
                let tmpData = null
                if (isNaN(Number(value))) {
                  // 处理时间字符串的情况 例如：YYY-MM-DD
                  tmpData = new Date(value)
                } else {
                  // 处理时间戳的情况
                  tmpData = new Date(Number(value))
                }

                // 校验是否转换为了时间
                if (isNaN(tmpData.getTime())) {
                  date = null
                } else {
                  date = tmpData
                }
              }

              return date
            },
            onChange(e) {
              let data = null
              if (isDate) {
                data = e
              } else if (isTime && e.isInteracted) {
                data = e.value
              }

              this.data[dataKey] = data
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = data
            }
          }
        })
      }
    }

    return template
  },
  // 带红星的表头
  requiredHeader: (args) => {
    const { headerText } = args
    const template = () => {
      return {
        template: Vue.component('requiredHeaderComponent', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{ headerText }}</span>
              </div>
            `,
          data: function () {
            return {
              data: {},
              headerText
            }
          },
          beforeDestroy() {},
          mounted() {},
          methods: {}
        })
      }
    }
    return template
  }
}
