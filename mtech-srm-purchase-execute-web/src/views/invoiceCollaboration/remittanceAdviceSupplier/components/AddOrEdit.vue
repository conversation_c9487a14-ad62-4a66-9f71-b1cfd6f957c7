<!-- 采方-汇款通知-新增or编辑 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-form-item prop="invoiceType" :label="$t('发票类型')">
        <mt-select
          v-model="formData.invoiceType"
          :data-source="invoiceTypeOptions"
          :show-clear-button="false"
          :placeholder="$t('请选择')"
        />
      </mt-form-item>
      <mt-form-item prop="companyCode" :label="$t('公司')">
        <RemoteAutocomplete
          v-model="formData.companyCode"
          url="/masterDataManagement/tenant/organization/specified-level-paged-query"
          :placeholder="$t('请选择')"
          :fields="{ text: 'orgName', value: 'orgCode' }"
          :params="{
            organizationLevelCodes: ['ORG01', 'ORG02']
          }"
          @change="companyChange"
        />
      </mt-form-item>
      <mt-form-item prop="supplierCode" :label="$t('供应商')">
        <RemoteAutocomplete
          v-model="formData.supplierCode"
          url="/masterDataManagement/tenant/supplier/paged-query"
          :placeholder="$t('请选择')"
          :fields="{ text: 'supplierName', value: 'supplierCode' }"
          :search-fields="['supplierName', 'supplierCode']"
          @change="supplierChange"
        />
      </mt-form-item>
      <mt-form-item prop="currencyCode" :label="$t('币种')">
        <RemoteAutocomplete
          v-model="formData.currencyCode"
          url="/masterDataManagement/tenant/currency/paged-query"
          :placeholder="$t('请选择')"
          :fields="{ text: 'currencyName', value: 'currencyCode' }"
          :search-fields="['currencyName', 'currencyCode']"
          @change="currencyChange"
        />
      </mt-form-item>
      <mt-form-item prop="invoiceNo" :label="$t('发票号')">
        <mt-input
          v-model="formData.invoiceNo"
          :show-clear-button="true"
          :placeholder="$t('请输入')"
        />
      </mt-form-item>
      <mt-form-item prop="amount" :label="$t('汇款金额')">
        <mt-input
          type="number"
          v-model="formData.amount"
          :show-clear-button="true"
          :placeholder="$t('请输入')"
        />
      </mt-form-item>
      <mt-form-item prop="remark" :label="$t('备注')">
        <mt-input v-model="formData.remark" :show-clear-button="true" :placeholder="$t('请输入')" />
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { invoiceTypeOptions } from '../config'
export default {
  data() {
    return {
      dialogTitle: '',
      actionType: 'add',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {},
      rules: {
        companyCode: [
          {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          }
        ],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          }
        ],
        currencyCode: [
          {
            required: true,
            message: this.$t('请选择币种'),
            trigger: 'blur'
          }
        ],
        invoiceNo: [
          {
            required: true,
            message: this.$t('请输入发票号'),
            trigger: 'blur'
          }
        ],
        amount: [
          {
            required: true,
            message: this.$t('请输入汇款金额'),
            trigger: 'blur'
          }
        ]
      },
      invoiceTypeOptions
    }
  },
  mounted() {},
  methods: {
    companyChange(e) {
      this.formData.companyName = e.itemData?.orgName
    },
    supplierChange(e) {
      this.formData.supplierName = e.itemData?.supplierName
    },
    currencyChange(e) {
      this.formData.currencyName = e.itemData?.currencyName
    },
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, row } = args
      this.dialogTitle = title
      this.actionType = actionType
      if (actionType === 'edit') {
        this.formData = row
      }
    },
    beforeOpen() {
      this.formData = {}
      this.$refs.ruleForm.clearValidate()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.handleSave()
        }
      })
    },
    handleSave() {
      let params = { ...this.formData }
      const api = this.$API.invoiceCollaboration.saveRemittanceAdviceApi
      api(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.actionType === 'add' ? this.$t('新增成功') : this.$t('更改成功'),
            type: 'success'
          })
          this.$emit('confirm')
          this.handleClose()
        }
      })
    }
  }
}
</script>
