<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <div class="full-height">
      <mt-template-page
        v-if="isShowTable"
        ref="templateRef"
        :template-config="componentConfig"
        :hidden-tabs="true"
      />
    </div>
  </mt-dialog>
</template>

<script>
import { formatTableColumnData } from '../config/index.js'
import { ItbcTableColumnData } from '../config/constant'
import { BASE_TENANT } from '@/utils/constant'

export default {
  data() {
    return {
      dialogTitle: '',
      apiWaitingQuantity: 0, // 调用的api正在等待数
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      isShowTable: false, // 隐藏表格使用 v-if 可以仅在显示时请求数据
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 不使用组件中的toolbar配置
          toolbar: [],
          grid: {
            allowPaging: false, // 不分页
            columnData: formatTableColumnData({
              data: ItbcTableColumnData
            }),
            asyncConfig: {
              url: `${BASE_TENANT}/external/reconciliation/invoice/getInvoiceFromItbc`,
              recordsPosition: 'data',
              params: {}
            },
            lineSelection: 0,
            lineIndex: 1,
            dataSource: [],
            frozenColumns: 1
          }
        }
      ]
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      const { title, headerInfo } = entryInfo
      // 设置表格参数
      const params = {
        supplierCode: headerInfo.supplierCode,
        companyCode: headerInfo.companyCode
      }
      this.$set(this.componentConfig[0].grid.asyncConfig, 'params', params)
      this.isShowTable = true
      this.dialogTitle = title // 弹框名称
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },

    confirm() {
      const selectedRowData = this.$refs.templateRef
        .getCurrentUsefulRef()
        .gridRef.getMtechGridRecords()
      this.$emit('confirm', { data: selectedRowData })
      this.handleClose()
    },

    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .common-template-page .toolbar-container.mt-flex.invite-btn {
  display: none;
}
</style>
