<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-template-page ref="templateRef" :template-config="componentConfig" :hidden-tabs="true" />
  </mt-dialog>
</template>

<script>
import { cloneDeep } from 'lodash'
import { LogColumnData } from '../config/constant'
import { BASE_TENANT } from '@/utils/constant'
import { formatLogTableColumnData } from '../config/index'
export default {
  components: {},
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('确定') }
        }
      ],
      selectData: {},
      componentConfig: []
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      const { title, selectData } = entryInfo
      this.selectData = cloneDeep(selectData)
      const asyncConfig = {
        url: `${BASE_TENANT}/log/queryOperationLog`,
        params: selectData
      }
      this.componentConfig = [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          gridId: '102c7790-a931-4d09-a4c7-05e13114e3b2',
          grid: {
            editSettings: {
              allowEditing: false
            },
            allowPaging: true, // 分页
            lineIndex: 0,
            columnData: formatLogTableColumnData({
              data: LogColumnData
            }),
            dataSource: [],
            asyncConfig: asyncConfig
          }
        }
      ]
      this.dialogTitle = title // 弹框名称
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
