import Vue from 'vue'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

export const ColumnComponent = {
  // 时间日期显示
  timeDate: (args) => {
    const { dataKey, hasTime } = args

    const template = () => {
      return {
        template: Vue.component('date', {
          template: `<div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div><div>{{data[dataKey] | dateFormat}}</div></div>`,
          data: function () {
            return { data: {}, dataKey, hasTime }
          },
          filters: {
            dateFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
              } else {
                str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
              }

              return str
            },
            timeFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({ formatString: 'HH:MM:SS', value })
              } else {
                str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
              }

              return str
            }
          }
        })
      }
    }

    return template
  },
  // list 信息 显示 每条数据一行
  listInfoColumn: (args) => {
    const { dataKey, isShowLength, infoKey, delimiter, isAbleClick } = args

    const template = () => {
      return {
        template: Vue.component('listInfoColumnComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <div class="input-search-content">
                <span v-if="isShowLength">{{data[dataKey] && data[dataKey].length || '-' }}</span>
                <div style="padding: 12px 0;" v-else>
                  <div
                    :class="[isAbleClick && 'able-click-field']"
                    v-for="(item, index) in data[dataKey]"
                    :key="index"
                    @click.stop="clickCellField({data: data, field: dataKey, fieldValue: item})"
                  >{{infoKey ? item[infoKey] : item}}{{data[dataKey].length != index + 1 ? delimiter : ''}}</div>
                </div>
              </div>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              isShowLength,
              infoKey,
              delimiter,
              isAbleClick
            }
          },
          mounted() {},
          filters: {},
          methods: {
            clickCellField(data) {
              this.$parent.$emit('handleClickCellTitle', data)
            }
          }
        })
      }
    }

    return template
  },
  // 明细行文件信息
  fileBaseInfoList: () => {
    return {
      template: Vue.component('fileBaseInfoList', {
        template: `<div @click="showFileBaseInfo" class="cell-operable-title">{{data.fileBaseInfoList | listNumFormat}}</div>`,
        data: function () {
          return { data: {} }
        },
        filters: {
          listNumFormat(value) {
            if (value && value.length > 0) {
              return value.length
            } else {
              return ''
            }
          }
        },
        methods: {
          showFileBaseInfo() {
            this.$parent.$emit('showFileBaseInfo', {
              index: this.data.index,
              value: this.data.fileBaseInfoList
            })
          }
        }
      })
    }
  }
}
