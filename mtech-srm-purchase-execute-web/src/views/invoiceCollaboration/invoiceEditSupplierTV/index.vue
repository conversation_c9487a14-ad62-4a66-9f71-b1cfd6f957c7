<template>
  <!-- 发票协同（供方）新增、编辑发票 -->
  <!-- 新增 带入明细不可修改，编辑高低开信息 -->
  <!-- 编辑-待提交 状态下 可以修改明细和高低开信息，因为都是基于可开票单据新增的发票 -->
  <!-- 编辑-退回 状态下 不可以修改明细和高低开信息 -->
  <div class="full-height pt20 vertical-flex-box">
    <!-- 头部信息 -->
    <top-info
      class="flex-keep"
      :entry-type="entryType"
      :header-info="headerInfo"
      @doSubmit="doSubmit"
      @doUpdate="doUpdate"
      @goBack="goBack"
      @doExpand="doExpand"
    ></top-info>
    <mt-tabs
      class="flex-keep"
      :e-tab="false"
      :selected-item="activeTab"
      :data-source="tabList"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <!-- 列模板 -->
    <div
      class="flex-fit"
      v-show="
        currentTabInfo.code === TabCode.dataItem || currentTabInfo.code === TabCode.highLowInfo
      "
    >
      <mt-template-page
        ref="templateRef"
        :hidden-tabs="true"
        :current-tab="currentTab"
        :template-config="componentConfig"
        @dataBound="handleDataBound"
        @rowSelected="handleRowSelected"
        @rowDeselected="handleRowDeselected"
        @handleClickToolBar="handleClickToolBar"
        @showFileBaseInfo="showFileBaseInfo"
      >
      </mt-template-page>
    </div>
    <!-- 相关附件 -->
    <div class="flex-fit" v-show="currentTabInfo.code === TabCode.file">
      <relative-file
        ref="relativeFileRef"
        @initData="relativeFileInitData"
        :module-file-list="moduleFileList"
      ></relative-file>
    </div>
    <!-- 附件弹窗 -->
    <uploader-dialog ref="uploaderDialog"></uploader-dialog>
  </div>
</template>

<script>
import {
  ConstantType,
  TabCode,
  DataItemTabConfig,
  HighLowInfoTabConfig,
  HighLowInfoColumnData,
  TabList
} from './config/constant'
import { BASE_TENANT } from '@/utils/constant'
import { nodeType } from '@/components/businessComponents/relativeFileNoDocId/config/relative.js'
import { cloneDeep } from 'lodash'
import { InvoiceStatus } from '../summarySupplierTV/config/constant'
import bigDecimal from 'js-big-decimal'
import RelativeFile from '@/components/businessComponents/relativeFileNoDocId'
import TopInfo from './components/topInfo.vue'
import UploaderDialog from '@/components/Upload/uploaderDialog'

export default {
  components: {
    TopInfo,
    RelativeFile,
    UploaderDialog
  },
  data() {
    const lastTabIndex = JSON.parse(localStorage.getItem('lastTabIndex'))
    const invoiceEditSupplierData = JSON.parse(localStorage.getItem('invoiceEditSupplierData'))
    const {
      headerInfo, // 头部信息、行数据
      entryType, // 页面类型
      dataItemAsyncConfig, // 明细表格配置
      dataItemColumnData, // 明细表格表头
      dataItemDataSource, // 明细表格数据
      highLowInfoAsyncConfig // 高低开表格配置
    } = invoiceEditSupplierData

    return {
      lastTabIndex, // 前一页面的 Tab index
      headerInfo, // 顶部的数据
      entryType, // 页面状态
      dataItemAsyncConfig, // 明细表格配置
      dataItemColumnData, // 明细表格表头
      dataItemDataSource, // 明细表格数据
      highLowInfoAsyncConfig, // 高低开表格配置
      currentDataItemTableData: [], // 当前明细行表格数据
      currentSelectDataItems: [], // 本次选择的明细行数据 用于校验
      currentHighLowInfoTableList: [], // 当前高低开表格数据
      selectDataItemRowsIdsList: headerInfo.itemIds || [], // 选中的明细行 ID 列表
      selectHighLowInfoRowsIdsList: headerInfo.highLowInfoIds || [], // 选中的高低开行 ID 列表
      currencyName: '', // 币种名称
      tabList: TabList,
      TabCode,
      ConstantType,
      currentTabInfo: {}, // 当前tab的数据
      currentTab: 0, // 当前列模板显示的 Tab
      activeTab: 0, // 当前高亮的 Tab
      invoiceFilesInit: [], // 发票附件的初始数据
      apiWaitingQuantity: 0, // 调用的api正在等待数
      moduleFileList: [
        {
          type: nodeType.mainUpdateEdit, // 可上传、下载、删除的文件列表
          id: 'InvoiceAttachment',
          // 租户级-供方对账单发票信息-TV接口-查询附件列表
          url: `${BASE_TENANT}/tv/invoice/file/list`,
          methods: 'post',
          params: {
            id: headerInfo.id
          },
          nodeName: this.$t('发票附件')
        }
      ], // 相关附件配置
      invoiceFileListInit: [], // 发票附件 列表 初始化 用于过滤出上传、删除的文件
      componentConfig: [
        {
          ...DataItemTabConfig
        },
        {
          ...HighLowInfoTabConfig
        }
      ]
    }
  },
  mounted() {
    this.doInit()
  },
  beforeDestroy() {
    // localStorage.removeItem("invoiceEditSupplierData");
    // localStorage.removeItem("lastTabIndex");
  },
  methods: {
    doInit() {
      // 页面初始化
      this.currentTabInfo = this.tabList[0] // 选中第一个Tab
      this.currentTab = 0 // 当前tab的数据 默认为 关联明细
      this.activeTab = 0 // 当前高亮的 Tab
      // 初始化列模板组件
      this.initTemplatePage()
      // 设置组件当前勾选行 默认显示 关联明细tab
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.selectIdRecords =
        this.selectDataItemRowsIdsList.map((item) => {
          return { id: item }
        })
    },
    // 初始化列模板组件
    initTemplatePage() {
      this.componentConfig = []
      // 关联明细
      const dataItemConfig = {
        lineSelection: false,
        lineIndex: 0,
        frozenColumns: false,

        useBaseConfig: true, // 使用组件中的toolbar配置
        allowPaging: true, // 允许分页
        toolbar: DataItemTabConfig.toolbar
      }
      // 高低开
      const highLowConfig = {
        lineSelection: false,
        lineIndex: 0,
        frozenColumns: false
      }
      const dataItemAsync = {
        ...this.dataItemAsyncConfig,
        serializeList: this.dataItemSerializeList
      }
      const highLowInfoAsync = {
        ...this.highLowInfoAsyncConfig,
        serializeList: this.highLowInfoSerializeList
      }
      if (
        this.entryType === ConstantType.Edit &&
        this.headerInfo.status === InvoiceStatus.returned
      ) {
        // 编辑 && 退回 状态下 仅查看 不可以修改 明细和高低开信息
        // dataItemConfig highLowConfig 此处使用初值，不用修改配置
      } else if (
        this.entryType === ConstantType.Edit &&
        this.headerInfo.status === InvoiceStatus.pendingSubmission
      ) {
        // 编辑 && 待提交 状态下 可编辑 可以修改明细和高低开信息，因为都是基于可开票单据新增的发票
        // 关联明细
        dataItemConfig.lineSelection = 0
        dataItemConfig.lineIndex = 1
        dataItemConfig.frozenColumns = 1

        // 高低开
        highLowConfig.lineSelection = 0
        highLowConfig.lineIndex = 1
        highLowConfig.frozenColumns = 1
      } else if (this.entryType === ConstantType.Add) {
        // 新增 明细行通过明细tab勾选带入，不可编辑关联明细，高低开可编辑
        // 明细行
        dataItemConfig.useBaseConfig = false // 不使用组件中的toolbar配置
        dataItemConfig.allowPaging = false // 不分页
        dataItemConfig.toolbar = [
          // [
          //   {
          //     id: "remove",
          //     icon: "icon_table_remove",
          //     title: this.$t("移除"),
          //   },
          // ],
        ]
        // 高低开
        highLowConfig.lineSelection = 0
        highLowConfig.lineIndex = 1
        highLowConfig.frozenColumns = 1
      }
      this.$nextTick(() => {
        this.componentConfig = [
          {
            // 明细行
            ...DataItemTabConfig,
            useBaseConfig: dataItemConfig.useBaseConfig, // 组件中的toolbar配置
            toolbar: dataItemConfig.toolbar,
            grid: {
              ...DataItemTabConfig.grid,
              allowPaging: dataItemConfig.allowPaging, // 分页配置
              lineSelection: dataItemConfig.lineSelection,
              lineIndex: dataItemConfig.lineIndex,
              frozenColumns: dataItemConfig.frozenColumns,
              columnData: this.dataItemColumnData,
              asyncConfig: dataItemAsync,
              dataSource: this.dataItemDataSource || [] // 新增时勾选带入数据源
            }
          },
          {
            // 高低开
            ...HighLowInfoTabConfig,
            grid: {
              ...HighLowInfoTabConfig.grid,
              lineSelection: highLowConfig.lineSelection,
              lineIndex: highLowConfig.lineIndex,
              frozenColumns: highLowConfig.frozenColumns,
              columnData: HighLowInfoColumnData,
              asyncConfig: highLowInfoAsync
            }
          }
        ]
      })
    },
    // ToolBar
    handleClickToolBar(args) {
      const { toolbar, grid } = args
      const selectRows = grid.getSelectedRecords()
      const commonToolbar = [
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'HighLowInfoRefresh',
        'filterDataByLocal'
      ]
      if (selectRows.length === 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedId = []
      selectRows.forEach((item) => selectedId.push({ id: item.id, addId: item.addId }))
      if (toolbar.id === 'HighLowInfoRefresh') {
        // 刷新-高低开表格
        this.$refs.templateRef.refreshCurrentGridData()
      }
    },
    // 点击 tab
    handleSelectTab(e) {
      this.currentTabInfo = this.tabList[e]
      this.currentTab = e
      this.activeTab = e
    },
    // 表格数据绑定完成
    handleDataBound() {
      if (
        this.entryType === ConstantType.Edit &&
        this.headerInfo.status === InvoiceStatus.pendingSubmission
      ) {
        // 编辑 && 待提交 状态下 可编辑 可以修改明细和高低开信息，因为都是基于可开票单据新增的发票

        // 编辑状态 当前tab是关联明细
        if (this.currentTabInfo.code === TabCode.dataItem) {
          // 数据绑定完成-回显勾选的 明细表格数据
          this.doSelectDataItemRowsByIds()
        } else if (this.currentTabInfo.code === TabCode.highLowInfo) {
          // 数据绑定完成-回显勾选的 高低开表格数据
          this.doSelectHighLowInfoRowsByIds()
        }
      } else if (this.entryType === ConstantType.Add) {
        if (this.currentTabInfo.code === TabCode.highLowInfo) {
          // 数据绑定完成-回显勾选的 高低开表格数据
          this.doSelectHighLowInfoRowsByIds()
        }
      }
    },
    // 数据绑定完成-回显勾选的 明细表格数据
    doSelectDataItemRowsByIds() {
      // 设置组件当前勾选行
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.selectIdRecords =
        this.selectDataItemRowsIdsList.map((item) => {
          return { id: item }
        })
    },
    // 数据绑定完成-回显勾选的 高低开表格数据
    doSelectHighLowInfoRowsByIds() {
      const selectRowIndexList = []

      this.selectHighLowInfoRowsIdsList.forEach((item) => {
        const rowIndex = this.currentHighLowInfoTableList.findIndex((row) => {
          return row.id === item
        })
        // 历史勾选的数据的行索引
        selectRowIndexList.push(rowIndex)
      })
      // 勾选选中行
      this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRows(selectRowIndexList)
    },
    // 勾选时，计算勾选的数据的含税总金额、未税总金额、税额
    handleRowSelected(args) {
      const { isHeaderCheckboxClicked, data, isInteracted } = args
      if (!isInteracted) return
      if (this.currentTabInfo.code === TabCode.dataItem) {
        const calculateData = []

        if (isHeaderCheckboxClicked) {
          // 当前页批量勾选
          this.currentDataItemTableData.forEach((itemTableData) => {
            if (itemTableData.id && !this.selectDataItemRowsIdsList.includes(itemTableData.id)) {
              // 1. 获取勾选的数据，用于计算
              calculateData.push(itemTableData)
              // 2. 更新 关联明细 选择的id数据
              this.selectDataItemRowsIdsList.push(itemTableData.id)
              // 3. 更新 关联明细 本次选择的数据
              this.currentSelectDataItems.push(itemTableData)
            }
          })
        } else if (!isHeaderCheckboxClicked && typeof data === 'object') {
          // 当前页单个勾选
          if (data.id) {
            // 1. 获取勾选的数据，用于计算
            calculateData.push(data)
            // 2. 更新 关联明细 选择的id数据
            this.selectDataItemRowsIdsList.push(data.id)
            // 3. 更新 关联明细 本次选择的数据
            this.currentSelectDataItems.push(data)
          }
        }

        this.headerInfo.reconciliationItemCount = this.selectDataItemRowsIdsList.length // 明细行数

        // 计算 关联明细 勾选的金额 加
        this.calculateDataItemAmount({
          data: calculateData,
          method: 'add'
        })
      } else if (this.currentTabInfo.code === TabCode.highLowInfo) {
        // 高低开
        const calculateData = []

        if (isHeaderCheckboxClicked) {
          // 当前页批量勾选
          this.currentHighLowInfoTableList.forEach((itemTableData) => {
            if (itemTableData.id && !this.selectHighLowInfoRowsIdsList.includes(itemTableData.id)) {
              // 1. 获取勾选的数据，用于计算
              calculateData.push(itemTableData)
              // 2. 更新 高低开 选择的id数据
              this.selectHighLowInfoRowsIdsList.push(itemTableData.id)
            }
          })
        } else if (!isHeaderCheckboxClicked && typeof data === 'object') {
          // 当前页单个勾选
          if (data.id) {
            // 1. 获取勾选的数据，用于计算
            calculateData.push(data)
            // 2. 更新 高低开 选择的id数据
            this.selectHighLowInfoRowsIdsList.push(data.id)
          }
        }
        // 计算 高低开 勾选的金额 加
        this.calculateHighLowInfoAmount({
          data: calculateData,
          method: 'add'
        })
      }
    },
    // 取消勾选时，计算勾选的数据的含税总金额、未税总金额、税额
    handleRowDeselected(args) {
      const { data, isHeaderCheckboxClicked, isInteracted } = args
      if (!isInteracted) return
      if (this.currentTabInfo.code === TabCode.dataItem) {
        // 关联明细
        const selectIdRecords = this.$refs.templateRef.getCurrentUsefulRef().gridRef.selectIdRecords
        if (selectIdRecords.length === 0) {
          // 如果是点击了清空按钮，则清空选择的数据
          this.selectDataItemRowsIdsList = []
          this.currentSelectDataItems = []
          this.headerInfo.reconciliationTaxedAmount = 0 // 明细汇总含税金额
          this.headerInfo.reconciliationUntaxAmount = 0 // 明细汇总未税金额
          this.headerInfo.reconciliationTaxAmount = 0 // 明细汇总税额
        } else {
          const calculateData = []
          if (isHeaderCheckboxClicked) {
            // 当前页批量取消
            this.currentDataItemTableData.forEach((itemTableData) => {
              if (this.selectDataItemRowsIdsList.includes(itemTableData.id)) {
                // 1. 获取取消的数据，用于计算
                calculateData.push(itemTableData)
              }
              const deselectedIndex = this.selectDataItemRowsIdsList.findIndex(
                (itemRowId) => itemRowId === itemTableData.id
              )
              if (deselectedIndex >= 0) {
                // 2. 更新 关联明细 选择的id数据
                this.selectDataItemRowsIdsList.splice(deselectedIndex, 1)
              }
              const selectDataIndex = this.currentSelectDataItems.findIndex(
                (itemRowData) => itemRowData.id === itemTableData.id
              )
              if (selectDataIndex >= 0) {
                // 3. 更新 关联明细 本次选择的数据
                this.currentSelectDataItems.splice(selectDataIndex, 1)
              }
            })
            // 2. 更新 关联明细 选择的id数据
          } else if (!isHeaderCheckboxClicked && typeof data === 'object') {
            // 当前页单个取消
            // 1. 获取取消的数据，用于计算
            calculateData.push(data)
            const deselectedIndex = this.selectDataItemRowsIdsList.findIndex(
              (itemRowId) => itemRowId === data.id
            )
            if (deselectedIndex >= 0) {
              // 2. 更新 关联明细 选择的id数据
              this.selectDataItemRowsIdsList.splice(deselectedIndex, 1)
            }
            const selectDataIndex = this.currentSelectDataItems.findIndex(
              (itemRowData) => itemRowData.id === data.id
            )
            if (selectDataIndex >= 0) {
              // 3. 更新 关联明细 本次选择的数据
              this.currentSelectDataItems.splice(selectDataIndex, 1)
            }
          }
          // 计算 关联明细 勾选的金额 减
          this.calculateDataItemAmount({
            data: calculateData,
            method: 'subtract'
          })
        }

        this.headerInfo.reconciliationItemCount = this.selectDataItemRowsIdsList.length // 明细行数
      } else if (this.currentTabInfo.code === TabCode.highLowInfo) {
        // 高低开
        const calculateData = []
        if (isHeaderCheckboxClicked) {
          // 当前页批量取消
          this.currentHighLowInfoTableList.forEach((itemTableData) => {
            if (this.selectHighLowInfoRowsIdsList.includes(itemTableData.id)) {
              // 1. 获取取消的数据，用于计算
              calculateData.push(itemTableData)
            }
            const deselectedIndex = this.selectHighLowInfoRowsIdsList.findIndex(
              (itemRowId) => itemRowId === itemTableData.id
            )
            if (deselectedIndex >= 0) {
              // 2. 更新 高低开 选择的id数据
              this.selectHighLowInfoRowsIdsList.splice(deselectedIndex, 1)
            }
          })
        } else if (!isHeaderCheckboxClicked && typeof data === 'object') {
          // 当前页单个取消
          // 1. 获取取消的数据，用于计算
          calculateData.push(data)
          const deselectedIndex = this.selectHighLowInfoRowsIdsList.findIndex(
            (itemRowId) => itemRowId === data.id
          )
          if (deselectedIndex >= 0) {
            // 2. 更新 高低开 选择的id数据
            this.selectHighLowInfoRowsIdsList.splice(deselectedIndex, 1)
          }
        }
        // 计算 高低开 勾选的金额 减
        this.calculateHighLowInfoAmount({
          data: calculateData,
          method: 'subtract'
        })
      }
    },
    // 计算 关联明细 勾选的金额
    calculateDataItemAmount(args) {
      const { data, method } = args
      if (method === 'add') {
        data.forEach((item) => {
          // 计算 勾选执行含税总金额
          this.headerInfo.reconciliationTaxedAmount =
            bigDecimal.add(
              this.headerInfo.reconciliationTaxedAmount,
              item.executeTaxedTotalPrice
            ) || 0
          // 计算 勾选执行未税总金额
          this.headerInfo.reconciliationUntaxAmount =
            bigDecimal.add(
              this.headerInfo.reconciliationUntaxAmount,
              item.executeUntaxedTotalPrice
            ) || 0
        })
      } else if (method === 'subtract') {
        data.forEach((item) => {
          // 计算 勾选执行含税总金额
          this.headerInfo.reconciliationTaxedAmount =
            bigDecimal.subtract(
              this.headerInfo.reconciliationTaxedAmount,
              item.executeTaxedTotalPrice
            ) || 0
          // 计算 勾选执行未税总金额
          this.headerInfo.reconciliationUntaxAmount =
            bigDecimal.subtract(
              this.headerInfo.reconciliationUntaxAmount,
              item.executeUntaxedTotalPrice
            ) || 0
        })
      }

      // 计算勾选的税额
      this.headerInfo.reconciliationTaxAmount = bigDecimal.subtract(
        this.headerInfo.reconciliationTaxedAmount,
        this.headerInfo.reconciliationUntaxAmount
      )

      // 更新头部发票金额
      this.upDateInvoiceAmount()
    },
    // 计算 高低开 勾选的金额
    calculateHighLowInfoAmount(args) {
      const { data, method } = args
      if (method === 'add') {
        data.forEach((item) => {
          // 计算 高低开执行含税总金额
          this.headerInfo.taxedHighLow =
            bigDecimal.add(this.headerInfo.taxedHighLow, item.taxPrice) || 0
          // 计算 高低开执行未税总金额
          this.headerInfo.untaxedHighLow =
            bigDecimal.add(this.headerInfo.untaxedHighLow, item.freePrice) || 0
        })
      } else if (method === 'subtract') {
        data.forEach((item) => {
          // 计算 高低开执行含税总金额
          this.headerInfo.taxedHighLow =
            bigDecimal.subtract(this.headerInfo.taxedHighLow, item.taxPrice) || 0
          // 计算 高低开执行未税总金额
          this.headerInfo.untaxedHighLow =
            bigDecimal.subtract(this.headerInfo.untaxedHighLow, item.freePrice) || 0
        })
      }

      // 计算勾选的税额
      this.headerInfo.highLowInfoTaxAmount = bigDecimal.subtract(
        this.headerInfo.taxedHighLow,
        this.headerInfo.untaxedHighLow
      )

      // 更新头部发票金额
      this.upDateInvoiceAmount()
    },
    // 更新头部发票金额
    upDateInvoiceAmount() {
      // 明细含税总额
      const reconciliationTaxedAmount = this.headerInfo.reconciliationTaxedAmount
      // 明细未税总额
      const reconciliationUntaxAmount = this.headerInfo.reconciliationUntaxAmount
      // 明细汇总税额
      const reconciliationTaxAmount = this.headerInfo.reconciliationTaxAmount
      // 高低开含税总额
      const taxedHighLow = this.headerInfo.taxedHighLow
      // 高低开未税总额
      const untaxedHighLow = this.headerInfo.untaxedHighLow
      // 高低开税额
      const highLowInfoTaxAmount = this.headerInfo.highLowInfoTaxAmount

      // 使 发票未税金额，默认等于 明细未税总额 + 高低开未税总额
      this.headerInfo.invoiceUntaxAmount = Number(
        bigDecimal.add(reconciliationUntaxAmount, untaxedHighLow)
      )
      // 使 发票税额，默认等于 明细汇总税额 + 高低开税额
      this.headerInfo.invoiceTaxAmount = Number(
        bigDecimal.add(reconciliationTaxAmount, highLowInfoTaxAmount)
      )
      // 使 发票含税金额，默认等于 明细含税总额 + 高低开含税总额
      this.headerInfo.invoiceTaxedAmount = Number(
        bigDecimal.add(reconciliationTaxedAmount, taxedHighLow)
      )
    },
    dataItemSerializeList(list) {
      this.currentDataItemTableData = list
      return list
    },
    highLowInfoSerializeList(list) {
      this.currentHighLowInfoTableList = list
      return list
    },
    // 显示表格文件弹窗
    showFileBaseInfo(e) {
      const dialogParams = {
        fileData: cloneDeep(e.value),
        isView: true,
        title: this.$t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },
    // 获取发票文件初始数据
    relativeFileInitData(args) {
      const { index, data } = cloneDeep(args)
      if (this.moduleFileList[index].id === 'InvoiceAttachment') {
        // 发票附件
        this.invoiceFileListInit = data || []
      }
    },
    // 校验数据 本次勾选的数据 编辑发票明细行时，校验关联明细行税率一致
    doValidate() {
      let isValid = true
      if (
        this.entryType === ConstantType.Edit &&
        this.headerInfo.status === InvoiceStatus.pendingSubmission
      ) {
        for (let i = 0; i < this.currentSelectDataItems.length; i++) {
          const currentItem = this.currentSelectDataItems[i]
          const nextItem = this.currentSelectDataItems[i + 1]
          if (nextItem && currentItem.taxCode !== nextItem.taxCode) {
            // taxCode	税率编码
            isValid = false
            this.$toast({
              content: this.$t('勾选的明细行数据，税率不一致'),
              type: 'warning'
            })
            break
          }
        }
      }

      return isValid
    },
    // 提交 发票
    doSubmit() {
      if (this.doValidate()) {
        const invoiceFile = this.diffInvoiceFilesList()
        const params = {
          invoiceCode: this.headerInfo.invoiceCode, // 发票代码
          invoiceNum: this.headerInfo.invoiceNum, // 发票号
          invoiceTime: Number(this.headerInfo.invoiceTime), // 开票日期 时间戳
          invoiceUntaxAmount: this.headerInfo.invoiceUntaxAmount, // 发票未税金额
          invoiceTaxAmount: this.headerInfo.invoiceTaxAmount, // 发票税额
          invoiceTaxedAmount: this.headerInfo.invoiceTaxedAmount, // 发票含税金额
          supplierRemark: this.headerInfo.supplierRemark, // 供应商备注
          invoiceTypeCode: this.headerInfo.invoiceTypeCode, // 发票类型
          invoiceTypeName: this.headerInfo.invoiceTypeName, // 发票类型名称
          fileList: invoiceFile.addInvoiceFiles, // 新增的文件
          highLowIds: this.selectHighLowInfoRowsIdsList, // 高低开ids
          itemIds: this.selectDataItemRowsIdsList, // 发票明细ids
          reconciliationHeaderIds: this.headerInfo.reconciliationIds.join(','), // 对账单ids
          submit: true // 新增后触发提交操作
        }
        // 租户级-供方对账单发票信息-v2接口-新增
        this.postReconciliationInvoiceV2Add(params)
      }
    },
    // 更新 发票
    doUpdate() {
      if (this.doValidate()) {
        const invoiceFile = this.diffInvoiceFilesList()
        const params = {
          id: this.headerInfo.id,
          invoiceCode: this.headerInfo.invoiceCode, // 发票代码
          invoiceNum: this.headerInfo.invoiceNum, // 发票号
          invoiceTime: Number(this.headerInfo.invoiceTime), // 开票日期 时间戳
          invoiceUntaxAmount: this.headerInfo.invoiceUntaxAmount, // 发票未税金额
          invoiceTaxAmount: this.headerInfo.invoiceTaxAmount, // 发票税额
          invoiceTaxedAmount: this.headerInfo.invoiceTaxedAmount, // 发票含税金额
          supplierRemark: this.headerInfo.supplierRemark, // 供应商备注
          invoiceTypeCode: this.headerInfo.invoiceTypeCode, // 发票类型
          invoiceTypeName: this.headerInfo.invoiceTypeName, // 发票类型名称
          fileList: invoiceFile.addInvoiceFiles, // 新增的文件
          deleteFileIdList: invoiceFile.deleteInvoiceFiles, // 删除发票文件列表
          highLowIds: this.selectHighLowInfoRowsIdsList, // 高低开ids
          itemIds: this.selectDataItemRowsIdsList // 发票明细ids
        }
        // 租户级-供方对账单发票信息-v2接口-更新
        this.putReconciliationInvoiceV2Update(params)
      }
    },
    // 租户级-供方对账单发票信息-v2接口-新增
    postReconciliationInvoiceV2Add(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postReconciliationInvoiceV2Add(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.goBack()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-供方对账单发票信息-v2接口-更新
    putReconciliationInvoiceV2Update(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .putReconciliationInvoiceV2Update(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.goBack()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    diffInvoiceFilesList() {
      const invoiceFilesIds = [] // 发票表格 id 列表
      const invoiceFilesIdsInit = [] // 发票表格初始 id 列表
      this.invoiceFileListInit.forEach((item) => {
        invoiceFilesIdsInit.push(item.id)
      })

      // 获取发票表格数据
      let invoiceFiles = this.$refs.relativeFileRef.getUploadFlies('InvoiceAttachment')
      invoiceFiles.forEach((item) => {
        invoiceFilesIds.push(item.id)
      })

      // 被删除的发票文件 id 列表
      const deleteInvoiceFilesIds = invoiceFilesIdsInit.filter(
        (item) => !invoiceFilesIds.includes(item)
      )
      // 新增的发票文件 id 列表
      const addInvoiceFilesIds = invoiceFilesIdsInit
        .concat(invoiceFilesIds)
        .filter((item) => !invoiceFilesIdsInit.includes(item))

      // 删除的发票文件列表
      const deleteInvoiceFiles = this.invoiceFileListInit.filter((item) =>
        deleteInvoiceFilesIds.includes(item.id)
      )

      // 新增的发票文件列表
      const addInvoiceFiles = invoiceFiles
        .filter((item) => addInvoiceFilesIds.includes(item.id))
        .map((item) => {
          return {
            ...item,
            id: undefined,
            docId: this.headerInfo.id || undefined, // 文件docId 为 发票id
            parentId: 0
          }
        })

      return {
        deleteInvoiceFiles,
        addInvoiceFiles
      }
    },
    goBack() {
      // 将 tabIndex 放到 localStorage 发票协同-列表-供方 读
      localStorage.setItem('tabIndex', JSON.stringify(this.lastTabIndex))
      // 返回 发票协同-列表-供方
      this.$router.push({
        name: 'invoice-summary-supplier-tv'
      })
    },
    doExpand() {
      this.$refs.templateRef?.resetGridHeight && this.$refs.templateRef.resetGridHeight()
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.mt-tabs {
  width: 100%;
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
</style>
