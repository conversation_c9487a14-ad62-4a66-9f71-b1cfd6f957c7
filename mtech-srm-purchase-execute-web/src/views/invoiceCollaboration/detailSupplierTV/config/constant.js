import { i18n } from '@/main.js'

export const ConstantType = {
  edit: '1', // 编辑
  look: '2' // 查看
}

// 表格编辑 的 RequestType
export const RequestType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  sorting: 'sorting',
  refresh: 'refresh'
}

// 表格编辑 的 action
export const ActionType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  edit: 'edit'
}

// 行编辑触发的 change 事件类型
export const ComponentChangeType = {
  code: 'code', // 例：下拉框选择“供应商”，供应商编码（code)要被修改
  link: 'link', // 例：计划组：根据物料+工厂+计划组的业务组类型 code 带出
  change: 'change' // 值关联被改变
}

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit'
}

// 表格 EditSettings
export const EditSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
} // 编辑设置

// tab
export const Tab = {
  reconciliationDetails: 1, // 对账明细
  invoiceList: 2 // 发票清单
}

// tab code
export const TabCode = {
  highLowInfo: 'highLowInfo', // 高低开信息
  operationLog: 'operationLog', // 操作日志
  reconciliationFile: 'reconciliationFile', // 相关附件
  reconciliationField: 'reconciliationField', // 对账明细
  invoiceList: 'invoiceList', // 发票清单
  purHeaderFile: 'purHeaderFile', // 采方-整单附件
  supHeaderFile: 'supHeaderFile', // 供方-整单附件
  scanHeaderFile: 'scanHeaderFile' // 供方-整单附件
}

// 发票清单 Toolbar Look
export const InvoiceListToolbarInLook = [
  [
    {
      id: 'InvoiceExport',
      icon: 'icon_solid_export',
      title: i18n.t('导出')
    }
  ],
  [
    {
      id: 'InvoiceRefresh',
      icon: 'icon_solid_Refresh',
      title: i18n.t('刷新')
    }
  ]
]

// 发票清单 状态 status cellTools
export const StatusCellTools = [
  {
    id: 'InvoiceDelete',
    icon: 'icon_solid_Delete',
    title: i18n.t('删除'),
    visibleCondition: (data) => {
      return (
        data.status === InvoiceStatus.pendingSubmission || data.status === InvoiceStatus.returned
      ) // 发票状态 == 待提交 || 发票状态 == 已退回
    }
  }
]

// 发票清单 Toolbar Edit
export const InvoiceListToolbarInEdit = [
  [
    {
      id: 'InvoiceSave',
      icon: 'icon_solid_Createproject',
      title: i18n.t('保存')
    },
    {
      id: 'InvoiceAdd',
      icon: 'icon_solid_Createproject',
      title: i18n.t('新增')
    },
    {
      id: 'InvoiceDelete',
      icon: 'icon_table_delete',
      title: i18n.t('删除')
    },
    // {
    //   id: "InvoiceUpdate",
    //   icon: "icon_table_save",
    //   title: i18n.t("更新"),
    // },
    // {
    //   id: "GetInvoice",
    //   icon: "icon_table_acquire",
    //   title: i18n.t("税控平台获取"),
    // },
    {
      id: 'InvoiceImport',
      icon: 'icon_solid_Import',
      title: i18n.t('导入')
    }
  ],
  [
    {
      id: 'InvoiceRefresh',
      icon: 'icon_solid_Refresh',
      title: i18n.t('刷新')
    }
  ]
]

// 发票清单 表格列数据
export const InvoiceColumnData = [
  {
    code: 'checkBox' // 不可编辑
  },
  {
    code: 'reconciliationHeaderCodes',
    name: i18n.t('单据号')
  },
  {
    code: 'invoiceTime',
    name: i18n.t('开票日期')
  },
  {
    code: 'invoiceNum',
    name: i18n.t('发票号')
  },
  {
    code: 'invoiceUntaxAmount',
    name: i18n.t('发票未税金额')
  },
  {
    code: 'invoiceTaxAmount',
    name: i18n.t('税额')
  },
  {
    code: 'invoiceTaxedAmount',
    name: i18n.t('发票含税金额')
  },
  {
    code: 'arriveYear',
    name: i18n.t('到货年份')
  },
  {
    code: 'arriveMonth',
    name: i18n.t('到货月份')
  },
  {
    code: 'factoryCode',
    name: i18n.t('工厂编码')
  },
  {
    code: 'salesGoodsListQty',
    name: i18n.t('销货清单份数')
  },
  {
    code: 'salerTaxPayerIc',
    name: i18n.t('销方纳税人识别码')
  },
  {
    code: 'invoiceCode',
    name: i18n.t('发票代码')
  },
  {
    code: 'checkCode',
    name: i18n.t('后六位校验码')
  },
  {
    code: 'supplierRemark',
    name: i18n.t('备注')
  },
  {
    code: 'thePrimaryKey'
  }
]

// 税控平台 表格列数据
export const ItbcTableColumnData = [
  {
    code: 'invoiceType', // （0增值税，1普通发票）
    name: i18n.t('发票类型')
  },
  {
    code: 'invoiceCode',
    name: i18n.t('发票代码')
  },
  {
    code: 'invoiceNum',
    name: i18n.t('发票号')
  },
  {
    code: 'buyerName',
    name: i18n.t('购方名称')
  },
  {
    code: 'buyerTaxno',
    name: i18n.t('购方税号')
  },
  {
    code: 'sellerName',
    name: i18n.t('销方名称')
  },
  {
    code: 'sellerTaxno',
    name: i18n.t('销方税号')
  },
  {
    code: 'invoiceTime',
    name: i18n.t('开票日期')
  },
  {
    code: 'untaxedAmount',
    name: i18n.t('发票未税金额')
  },
  {
    code: 'taxAmount',
    name: i18n.t('税额')
  },
  {
    code: 'taxedAmount',
    name: i18n.t('发票含税金额')
  }
]

// 单据开票状态 0待开票 10部分开票 1全部开票 11已全部开票待提交
export const BillInvoiceStatus = {
  toBeBilled: 0, // 未完成 (没有提交一张发票)
  partialUpload: 10, // 部分完成 部分开票
  finishedUploadingPendingSubmission: 11, // 部分完成 已全部开票，待提交
  fullyCompleted: 1 // 全部完成 全部开票，全部提交
}
// 单据开票状态 0待开票 10部分开票 1全部开票
export const BillInvoiceStatusConst = {
  [BillInvoiceStatus.toBeBilled]: i18n.t('未完成'),
  [BillInvoiceStatus.partialUpload]: i18n.t('部分开票'),
  [BillInvoiceStatus.finishedUploadingPendingSubmission]: i18n.t('待提交'),
  [BillInvoiceStatus.fullyCompleted]: i18n.t('全部完成')
}
// 单据开票状态 对应的 css class
export const BillInvoiceStatusCssClass = {
  [BillInvoiceStatus.toBeBilled]: 'col-active',
  [BillInvoiceStatus.partialUpload]: 'col-active',
  [BillInvoiceStatus.finishedUploadingPendingSubmission]: 'col-active',
  [BillInvoiceStatus.fullyCompleted]: 'col-inactive'
}
// 单据开票状态 0待开票 10部分开票 1全部开票 11已全部开票待提交
export const BillInvoiceStatusOptions = [
  {
    // 未完成 (没有提交一张发票)
    value: BillInvoiceStatus.toBeBilled,
    text: BillInvoiceStatusConst[BillInvoiceStatus.toBeBilled],
    cssClass: BillInvoiceStatusCssClass[BillInvoiceStatus.toBeBilled]
  },
  {
    // 部分完成 部分开票
    value: BillInvoiceStatus.partialUpload,
    text: BillInvoiceStatusConst[BillInvoiceStatus.partialUpload],
    cssClass: BillInvoiceStatusCssClass[BillInvoiceStatus.partialUpload]
  },
  {
    // 部分完成 已全部开票，待提交
    value: BillInvoiceStatus.finishedUploadingPendingSubmission,
    text: BillInvoiceStatusConst[BillInvoiceStatus.finishedUploadingPendingSubmission],
    cssClass: BillInvoiceStatusCssClass[BillInvoiceStatus.finishedUploadingPendingSubmission]
  },
  {
    // 全部完成 全部开票，全部提交
    value: BillInvoiceStatus.fullyCompleted,
    text: BillInvoiceStatusConst[BillInvoiceStatus.fullyCompleted],
    cssClass: BillInvoiceStatusCssClass[BillInvoiceStatus.fullyCompleted]
  }
]

// 发票状态 0-待提交、1-待审核、2-待财务审核、3-完成、4-已退回、5-已删除
export const InvoiceStatus = {
  pendingSubmission: 0, // 待提交
  pendingReview: 1, // 待审核
  pendingFinancialReview: 2, // 待财务审核
  complete: 3, // 完成
  returned: 4, // 已退回
  deleted: 5 // 已删除
}

// 发票状态
export const InvoiceStatusConst = {
  [InvoiceStatus.pendingSubmission]: i18n.t('待提交'),
  [InvoiceStatus.pendingReview]: i18n.t('待确认'), // 待审核
  [InvoiceStatus.pendingFinancialReview]: i18n.t('采购已确认'), // 待财务审核
  [InvoiceStatus.complete]: i18n.t('财务已确认'), // 完成
  [InvoiceStatus.returned]: i18n.t('已退回'),
  [InvoiceStatus.deleted]: i18n.t('已删除')
}

// 发票状态 对应的 css class
export const InvoiceStatusCssClass = {
  [InvoiceStatus.pendingSubmission]: 'col-active',
  [InvoiceStatus.pendingReview]: 'col-active',
  [InvoiceStatus.pendingFinancialReview]: 'col-normal',
  [InvoiceStatus.complete]: 'col-normal',
  [InvoiceStatus.returned]: 'col-published',
  [InvoiceStatus.deleted]: 'col-inactive'
}

// 发票状态 对应的 Options
export const InvoiceStatusOptions = [
  {
    // 待提交
    value: InvoiceStatus.pendingSubmission,
    text: InvoiceStatusConst[InvoiceStatus.pendingSubmission],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.pendingSubmission]
  },
  {
    // 待审核
    value: InvoiceStatus.pendingReview,
    text: InvoiceStatusConst[InvoiceStatus.pendingReview],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.pendingReview]
  },
  {
    // 待财务审核
    value: InvoiceStatus.pendingFinancialReview,
    text: InvoiceStatusConst[InvoiceStatus.pendingFinancialReview],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.pendingFinancialReview]
  },
  {
    // 完成
    value: InvoiceStatus.complete,
    text: InvoiceStatusConst[InvoiceStatus.complete],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.complete]
  },
  {
    // 已退回
    value: InvoiceStatus.returned,
    text: InvoiceStatusConst[InvoiceStatus.returned],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.returned]
  },
  {
    // 已删除
    value: InvoiceStatus.deleted,
    text: InvoiceStatusConst[InvoiceStatus.deleted],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.deleted]
  }
]

// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  status: InvoiceStatus.pendingSubmission, // 发票状态 新增时，默认：待提交
  invoiceType: null, // 发票类型 *
  invoiceCode: null, // 发票代码 *
  invoiceNum: null, // 	发票号 *
  invoiceTime: null, // 开票时间 *
  invoiceTaxedAmount: null, // 发票含税金额 *
  invoiceUntaxAmount: null, // 	发票不含税金额 *
  invoiceTaxAmount: null, // 发票税额 *
  itemIds: [], // 明细行ID
  highLowIds: [], // 高低开行ID
  // reconciliationItemCount: 0, // 关联明细行数量
  fileList: [], // 发票附件列表
  attachementCount: 0, // 发票附件数量
  reconciliationTaxAmount: null, // 明细行汇总 税额
  reconciliationTaxedAmount: null, // 明细行汇总 含税金额
  reconciliationUntaxAmount: null, // 明细行汇总 不含税金额
  taxedHighLow: null, // 高低开汇总 含税金额
  untaxedHighLow: null, // 高低开汇总 未税金额
  highLowInfoTaxAmount: null, // 高低开汇总 税额
  submit: false, // 触发提交操作
  supplierRemark: null // 供方备注
}

// 来源类型（枚举） 0: 上游流入1:第三方接口
export const SourceTypeOptions = [
  {
    value: 0,
    text: i18n.t('上游流入'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('第三方接口'),
    cssClass: ''
  }
]

// 单据状态 0:待对账 1:已创建对账单
export const ReconciliationDetailsStatusOptions = [
  {
    value: 0,
    text: i18n.t('待对账'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('已创建对账单'),
    cssClass: ''
  }
]

// 同步状态 0:否 1:是
export const SyncStatusOptions = [
  {
    value: 0,
    text: i18n.t('否'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('是'),
    cssClass: ''
  }
]

// 提前开票 0:否 1:是
export const AdvanceInvoicingOptions = [
  {
    value: 0,
    text: i18n.t('否'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('是'),
    cssClass: ''
  }
]

// 冻结标记 0:否 1:是
export const FrozenStatusOptions = [
  {
    value: 0,
    text: i18n.t('否'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('是'),
    cssClass: ''
  }
]

// 是否预付 0-否；1-是
export const PrePayStatusOptions = [
  {
    value: 0,
    text: i18n.t('否'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('是'),
    cssClass: ''
  }
]

// 是否暂估价 0-否；1-是
export const ProvisionalEstimateStatusOptions = [
  {
    value: 0,
    text: i18n.t('否'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('是'),
    cssClass: ''
  }
]
// 是否暂估价 0-否；1-是
export const ProvisionalEstimateStatusOptions1 = [
  {
    value: 0,
    text: i18n.t('是'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('否'),
    cssClass: ''
  }
]

// 待对账类型:0-采购待对账；1-销售待对账
export const ReconciliationDetailsTypeOptions = [
  {
    value: 0,
    text: i18n.t('采购待对账'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('销售待对账'),
    cssClass: ''
  }
]

// 正式价标识 0-无正式价；1-有正式价
export const RealPriceStatusOptions = [
  {
    value: 0,
    text: i18n.t('无正式价'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('有正式价'),
    cssClass: ''
  }
]

// 出入库类型 0-采购入库；1-采购出库；2-销售出库；3-销售退回
export const InOutTypeOptions = [
  {
    value: 0,
    text: i18n.t('采购入库'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('采购出库'),
    cssClass: ''
  },
  {
    value: 2,
    text: i18n.t('销售出库'),
    cssClass: ''
  },
  {
    value: 3,
    text: i18n.t('销售退回'),
    cssClass: ''
  }
]
