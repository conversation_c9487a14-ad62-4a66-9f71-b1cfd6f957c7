import {
  Tab,
  SourceTypeOptions,
  ReconciliationDetailsStatusOptions,
  SyncStatusOptions,
  AdvanceInvoicingOptions,
  FrozenStatusOptions,
  PrePayStatusOptions,
  ProvisionalEstimateStatusOptions1,
  ReconciliationDetailsTypeOptions,
  RealPriceStatusOptions,
  InOutTypeOptions,
  InvoiceStatusOptions,
  ComponentType,
  StatusCellTools,
  ConstantType,
  InvoiceStatus
} from './constant'
import { ColumnComponent } from './columnComponent'
import { i18n } from '@/main.js'
import { invoiceTypeOptions } from './variable'
import bigDecimal from 'js-big-decimal'

// 格式化表格动态数据
export const formatTableColumnData = (config) => {
  const { data, tab, entryType, companyCode } = config
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.code,
      headerText: col.name,
      width: data.length > 10 ? '150' : 'auto'
    }
    if (tab === Tab.invoiceList) {
      // 发票清单
      defaultCol.allowFiltering = false // 不允许过滤
      if (defaultCol.field === 'checkBox') {
        // checkBox
        let visible = false
        if (entryType === ConstantType.edit) {
          visible = true
        }
        defaultCol.type = 'checkbox'
        defaultCol.visible = visible
        defaultCol.allowEditing = false
        defaultCol.showInColumnChooser = false
        defaultCol.width = '50'
        // 编辑时显示
        defaultCol.editTemplate = ColumnComponent.empty
      } else if (defaultCol.field === 'serialNumber') {
        // 序号
        defaultCol.width = '80'
        defaultCol.allowEditing = false
        defaultCol.ignore = true
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        defaultCol.editTemplate = ColumnComponent.text({
          dataKey: defaultCol.field
        })
      } else if (defaultCol.field === 'reconciliationHeaderCodes') {
        // 单据号
        defaultCol.width = '180'
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        defaultCol.editTemplate = ColumnComponent.text({
          dataKey: defaultCol.field
        })
      } else if (defaultCol.field === 'thePrimaryKey') {
        // thePrimaryKey
        defaultCol.visible = false
        defaultCol.isIdentity = true // 加上这个，新增一行的时候，主键不会变成可输入
        defaultCol.isPrimaryKey = true
        defaultCol.allowEditing = false
      } else if (defaultCol.field === 'status') {
        // 发票状态
        let cellTools = []
        if (entryType === ConstantType.edit) {
          cellTools = StatusCellTools
        }
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field,
          // 仅可在页面编辑状态下可显示
          cellTools,
          valueConverter: {
            type: 'map',
            map: InvoiceStatusOptions
          }
        })
        defaultCol.editTemplate = ColumnComponent.text({
          dataKey: defaultCol.field,
          valueConverter: {
            type: 'map',
            map: InvoiceStatusOptions
          }
        })
      } else if (defaultCol.field === 'invoiceType') {
        // 发票类型 必须
        defaultCol.headerTemplate = ColumnComponent.requiredHeader({
          headerText: defaultCol.headerText
        })
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field,
          valueConverter: {
            type: 'map',
            map: invoiceTypeOptions
          }
        })
        defaultCol.editTemplate = ColumnComponent.select({
          dataKey: defaultCol.field,
          fields: { text: 'text', value: 'value' },
          selectOptions: invoiceTypeOptions,
          allowFiltering: false,
          showClearBtn: true
        })
      } else if (defaultCol.field === 'invoiceNum') {
        // 发票号 必须
        defaultCol.width = '150'
        defaultCol.headerTemplate = ColumnComponent.requiredHeader({
          headerText: defaultCol.headerText
        })
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        defaultCol.editTemplate = ColumnComponent.input({
          dataKey: defaultCol.field,
          showClearBtn: true,
          maxlength: 50
        })
      } else if (defaultCol.field === 'invoiceTime') {
        // 开票日期 必须
        defaultCol.width = '150'
        defaultCol.headerTemplate = ColumnComponent.requiredHeader({
          headerText: defaultCol.headerText
        })
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: false
        })
        defaultCol.editTemplate = ColumnComponent.timeInput({
          dataKey: defaultCol.field,
          disabled: false,
          showClearBtn: true,
          allowEdit: false,
          isDate: true,
          maxDate: new Date()
        })
      } else if (defaultCol.field === 'invoiceTaxedAmount') {
        // 发票含税金额 必须
        defaultCol.headerTemplate = ColumnComponent.requiredHeader({
          headerText: defaultCol.headerText
        })
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        defaultCol.textAlign = 'Right'
        defaultCol.editTemplate = ColumnComponent.number({
          dataKey: defaultCol.field,
          maximumValue: 999999999999999.99,
          precision: 2,
          modifiedKeys: ['invoiceTaxAmount'],
          componentType: ComponentType.edit
        })
      } else if (defaultCol.field === 'invoiceUntaxAmount') {
        // 发票未税金额 必须
        defaultCol.headerTemplate = ColumnComponent.requiredHeader({
          headerText: defaultCol.headerText
        })
        defaultCol.width = '150'
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        defaultCol.textAlign = 'Right'
        defaultCol.editTemplate = ColumnComponent.number({
          dataKey: defaultCol.field,
          maximumValue: 999999999999999.99,
          precision: 2,
          modifiedKeys: ['invoiceTaxAmount'],
          componentType: ComponentType.edit
        })
      } else if (defaultCol.field === 'invoiceTaxAmount') {
        // 税额 必须
        defaultCol.headerTemplate = ColumnComponent.requiredHeader({
          headerText: defaultCol.headerText
        })
        defaultCol.width = '120'
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        defaultCol.textAlign = 'Right'
        defaultCol.editTemplate = ColumnComponent.number({
          dataKey: defaultCol.field,
          componentType: ComponentType.edit,
          disabled: true
        })
      } else if (defaultCol.field === 'factoryCode') {
        // 工厂代码 必须
        defaultCol.headerTemplate = ColumnComponent.requiredHeader({
          headerText: defaultCol.headerText
        })
        defaultCol.width = '160'
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        defaultCol.editTemplate = ColumnComponent.input({
          dataKey: defaultCol.field,
          componentType: ComponentType.edit
        })
        if (companyCode === '0602') {
          defaultCol.visible = false
        }
      } else if (defaultCol.field === 'arriveYear' || defaultCol.field === 'arriveMonth') {
        // 到货 必须
        defaultCol.headerTemplate = ColumnComponent.requiredHeader({
          headerText: defaultCol.headerText
        })
        defaultCol.width = '160'
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        if (defaultCol.field === 'arriveMonth') {
          const months = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']
          defaultCol.editTemplate = ColumnComponent.select({
            dataKey: defaultCol.field,
            // fields: { text: 'text', value: 'value' },
            selectOptions: months,
            allowFiltering: false,
            showClearBtn: true
          })
        } else {
          defaultCol.editTemplate = ColumnComponent.input({
            dataKey: defaultCol.field,
            componentType: ComponentType.edit
          })
        }
      } else if (defaultCol.field === 'salesGoodsListQty') {
        // 销货清单份数 必须
        defaultCol.headerTemplate = ColumnComponent.requiredHeader({
          headerText: defaultCol.headerText
        })
        defaultCol.width = '160'
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        defaultCol.editTemplate = ColumnComponent.number({
          dataKey: defaultCol.field,
          componentType: ComponentType.edit
        })
      } else if (defaultCol.field === 'salerTaxPayerIc') {
        // 销方纳税人识别码 必须
        defaultCol.headerTemplate = ColumnComponent.requiredHeader({
          headerText: defaultCol.headerText
        })
        defaultCol.width = '200'
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        defaultCol.editTemplate = ColumnComponent.input({
          dataKey: defaultCol.field,
          componentType: ComponentType.edit
        })
      } else if (defaultCol.field === 'reconciliationItemCount') {
        // 明细行
        defaultCol.headerTemplate = ColumnComponent.requiredHeader({
          headerText: defaultCol.headerText
        })
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field,
          isAbleClick: true,
          componentType: ComponentType.view
        })
        defaultCol.editTemplate = ColumnComponent.text({
          dataKey: defaultCol.field,
          isAbleClick: true,
          infoMsg: i18n.t('点击关联'),
          showInfoMsgCondition: (data) => data == 0 || data == null,
          // 已退回状态的发票，明细行不能修改
          editDisabled: (data) => data.status == InvoiceStatus.returned,
          componentType: ComponentType.edit
        })
      } else if (defaultCol.field === 'reconciliationTaxedAmount') {
        // 明细汇总含税金额
        defaultCol.width = '250'
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        defaultCol.editTemplate = ColumnComponent.number({
          dataKey: defaultCol.field,
          componentType: ComponentType.edit,
          disabled: true
        })
      } else if (defaultCol.field === 'reconciliationUntaxAmount') {
        // 明细汇总未税金额
        defaultCol.width = '250'
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        defaultCol.editTemplate = ColumnComponent.number({
          dataKey: defaultCol.field,
          componentType: ComponentType.edit,
          disabled: true
        })
      } else if (defaultCol.field === 'reconciliationTaxAmount') {
        // 明细汇总税额
        defaultCol.width = '250'
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        defaultCol.editTemplate = ColumnComponent.number({
          dataKey: defaultCol.field,
          componentType: ComponentType.edit,
          disabled: true
        })
      } else if (defaultCol.field === 'taxedHighLow') {
        // 高低开汇总含税金额
        defaultCol.width = '250'
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        defaultCol.editTemplate = ColumnComponent.number({
          dataKey: defaultCol.field,
          componentType: ComponentType.edit,
          disabled: true
        })
      } else if (defaultCol.field === 'untaxedHighLow') {
        // 高低开汇总未税金额
        defaultCol.width = '250'
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        defaultCol.editTemplate = ColumnComponent.number({
          dataKey: defaultCol.field,
          componentType: ComponentType.edit,
          disabled: true
        })
      } else if (defaultCol.field === 'highLowInfoTaxAmount') {
        // 高低开汇总税额
        defaultCol.width = '250'
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        defaultCol.editTemplate = ColumnComponent.number({
          dataKey: defaultCol.field,
          componentType: ComponentType.edit,
          disabled: true
        })
      } else if (defaultCol.field === 'attachementCount') {
        // 发票附件
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field,
          isAbleClick: true,
          componentType: ComponentType.view
        })
        defaultCol.editTemplate = ColumnComponent.text({
          dataKey: defaultCol.field,
          isAbleClick: true,
          infoMsg: i18n.t('点击上传'),
          showInfoMsgCondition: (data) => data == 0,
          componentType: ComponentType.edit
        })
      } else if (defaultCol.field === 'customerRemark') {
        // 采方备注
        defaultCol.width = '250'
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        defaultCol.editTemplate = ColumnComponent.text({
          dataKey: defaultCol.field
        })
      } else if (defaultCol.field === 'supplierRemark') {
        // 供方备注
        defaultCol.width = '250'
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        defaultCol.editTemplate = ColumnComponent.input({
          dataKey: defaultCol.field,
          showClearBtn: true,
          maxlength: 200
        })
      } else if (defaultCol.field === 'invoiceCode') {
        // 发票代码
        defaultCol.width = '250'
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        defaultCol.editTemplate = ColumnComponent.input({
          dataKey: defaultCol.field,
          showClearBtn: true,
          maxlength: 50
        })
      } else if (defaultCol.field === 'checkCode') {
        // 后六位校验码
        defaultCol.width = '250'
        defaultCol.template = ColumnComponent.text({
          dataKey: defaultCol.field
        })
        defaultCol.editTemplate = ColumnComponent.input({
          dataKey: defaultCol.field,
          showClearBtn: true,
          maxlength: 50
        })
      }
      // cellTools = [
      //   {
      //     id: "InvoiceDelete",
      //     icon: "icon_solid_Delete",
      //     title: i18n.t("删除"),
      //   },
      // ];
    } else if (tab === Tab.reconciliationDetails) {
      // 对账明细
      defaultCol.width = data.length > 10 ? '200' : 'auto'
      defaultCol.allowFiltering = false // 不允许过滤
      if (!col.checkStatus) {
        defaultCol.visible = false
        defaultCol.ignore = true
      } else if (defaultCol.field === 'receiveTime') {
        // 对账明细-收货时间
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
      } else if (defaultCol.field === 'fileBaseInfoList') {
        // 对账明细-文件信息
        defaultCol.template = ColumnComponent.fileBaseInfoList
      } else if (defaultCol.field === 'termsUntaxedUnitPrice') {
        // 对账明细-条件（未税）单价
        defaultCol.width = '200'
      } else if (defaultCol.field === 'sourceType') {
        // 来源类型（枚举） 0: 上游流入1:第三方接口
        defaultCol.valueConverter = {
          type: 'map',
          map: SourceTypeOptions
        }
      } else if (defaultCol.field === 'status') {
        // 单据状态 0:待对账 1:已创建对账单
        defaultCol.valueConverter = {
          type: 'map',
          map: ReconciliationDetailsStatusOptions
        }
      } else if (defaultCol.field === 'syncStatus') {
        // 同步状态 0:否 1:是
        defaultCol.valueConverter = {
          type: 'map',
          map: SyncStatusOptions
        }
      } else if (defaultCol.field === 'advanceInvoicing') {
        // 提前开票 0:否 1:是
        defaultCol.valueConverter = {
          type: 'map',
          map: AdvanceInvoicingOptions
        }
      } else if (defaultCol.field === 'frozenStatus') {
        // 冻结标记 0:否 1:是
        defaultCol.valueConverter = {
          type: 'map',
          map: FrozenStatusOptions
        }
      } else if (defaultCol.field === 'prePayStatus') {
        // 是否预付 0-否；1-是
        defaultCol.valueConverter = {
          type: 'map',
          map: PrePayStatusOptions
        }
      } else if (defaultCol.field === 'itemVoucherDate') {
        // 物料凭证日期
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
      } else if (defaultCol.field === 'provisionalEstimateStatus') {
        // 是否暂估价 0-否；1-是 修改为是否执行价 0-是 1-否
        defaultCol.valueConverter = {
          type: 'map',
          map: ProvisionalEstimateStatusOptions1
        }
      } else if (defaultCol.field === 'createTime') {
        // 创建时间
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
      } else if (defaultCol.field === 'updateTime') {
        // 最后修改时间
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
      } else if (defaultCol.field === 'type') {
        // 待对账类型:0-采购待对账；1-销售待对账
        defaultCol.valueConverter = {
          type: 'map',
          map: ReconciliationDetailsTypeOptions
        }
      } else if (defaultCol.field === 'realPriceStatus') {
        // 正式价标识 0-无正式价；1-有正式价
        defaultCol.valueConverter = {
          type: 'map',
          map: RealPriceStatusOptions
        }
      } else if (defaultCol.field === 'inOutType') {
        // 出入库类型 0-采购入库；1-采购出库；2-销售出库；3-销售退回
        defaultCol.valueConverter = {
          type: 'map',
          map: InOutTypeOptions
        }
      } else if (defaultCol.field === 'supplierName') {
        defaultCol.width = '280'
      }
    }
    colData.push(defaultCol)
  })

  return colData
}

// 序列化发票列表
export const serializeInvoiceList = (list) => {
  if (list.length > 0) {
    list.forEach((item, index) => {
      item.invoiceType = item.invoiceTypeCode // 发票类型
      item.thePrimaryKey = item.id // 主键
      item.serialNumber = index + 1 // 序号
      item.highLowInfoTaxAmount = bigDecimal.subtract(item.taxedHighLow, item.untaxedHighLow) || 0 // 高低开汇总税额
    })
  }

  return list
}
