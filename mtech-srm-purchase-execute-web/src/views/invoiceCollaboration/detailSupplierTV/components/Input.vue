<template>
  <div id="cell-changed">
    <mt-input :id="data.column.field" v-model="data[data.column.field]" @input="inputValue" />
  </div>
</template>
<script>
export default {
  data() {
    return { data: {} }
  },
  mounted() {},
  methods: {
    inputValue(e) {
      if (e) {
        const _dataSource = this.$parent?.$parent?.$parent?.$refs.gridRef.dataSource ?? []
        _dataSource.map((item) => {
          if (item.id === this.data.id) {
            item[this.data.column.field] = e
          }
        })
      }
    }
  }
}
</script>
