import { i18n } from '@/main.js'

export const invoiceStatusOptions = [
  { text: i18n.t('未过账'), value: 'OPEN' },
  { text: i18n.t('已过账'), value: 'CLEARED' }
]

export const columnData = [
  // {
  //   type: 'checkbox',
  //   width: 50,
  //   fixed: 'left',
  //   align: 'center'
  // },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'invoiceNo',
    title: i18n.t('发票号'),
    minWidth: 140
  },
  {
    field: 'invoiceItemNo',
    title: i18n.t('发票行号'),
    minWidth: 140
  },
  {
    field: 'invoiceStatus',
    title: i18n.t('发票状态'),
    formatter: ({ cellValue }) => {
      let item = invoiceStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'invoiceDate',
    title: i18n.t('发票日期'),
    minWidth: 160
  },
  {
    field: 'currencyCode',
    title: i18n.t('发票币种')
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 200,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    }
  },
  {
    field: 'companyCode',
    title: i18n.t('公司'),
    minWidth: 200,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.companyName : ''
    }
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂'),
    minWidth: 200,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.siteName : ''
    }
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码'),
    minWidth: 160
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称'),
    minWidth: 200
  },
  {
    field: 'supplierItemCode',
    title: i18n.t('供方物料编码'),
    minWidth: 200
  },
  {
    field: 'supplierItemName',
    title: i18n.t('供方物料名称'),
    minWidth: 200
  },
  {
    field: 'salesOrderNo',
    title: i18n.t('销售订单号'),
    minWidth: 120
  },
  {
    field: 'salesOrderItemNo',
    title: i18n.t('销售订单行号'),
    minWidth: 120
  },
  {
    field: 'purchaseOrderNo',
    title: i18n.t('采购订单号'),
    minWidth: 120
  },
  {
    field: 'purchaseOrderItemNo',
    title: i18n.t('采购订单行号'),
    minWidth: 120
  },
  {
    field: 'orderDate',
    title: i18n.t('订单日期'),
    minWidth: 160
  },
  {
    field: 'basicUnit',
    title: i18n.t('基本单位')
  },
  {
    field: 'purchaseUnit',
    title: i18n.t('采购单位')
  },
  {
    field: 'quantity',
    title: i18n.t('数量')
  },
  {
    field: 'taxRate',
    title: i18n.t('税率')
  },
  {
    field: 'untaxedUnitPrice',
    title: i18n.t('未税单价')
  },
  {
    field: 'untaxedTotalPrice',
    title: i18n.t('未税总价')
  },
  {
    field: 'unitPriceTaxed',
    title: i18n.t('含税单价')
  },
  {
    field: 'totalPriceTaxed',
    title: i18n.t('含税总价')
  },
  {
    field: 'supDeliveryCode',
    title: i18n.t('供方送货单号'),
    minWidth: 120
  },
  {
    field: 'supDeliveryItemNo',
    title: i18n.t('供方送货单行号'),
    minWidth: 120
  },
  {
    field: 'buyerDeliveryCode',
    title: i18n.t('采方送货单号'),
    minWidth: 120
  },
  {
    field: 'buyerDeliveryItemNo',
    title: i18n.t('采方送货单行号'),
    minWidth: 120
  },
  {
    field: 'deliverDate',
    title: i18n.t('发货日期'),
    minWidth: 160
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'remark',
    title: i18n.t('行备注'),
    minWidth: 120
  },
  {
    field: 'operate',
    title: i18n.t('操作'),
    minWidth: 120,
    slots: {
      default: 'operateDetault'
    }
  }
]
