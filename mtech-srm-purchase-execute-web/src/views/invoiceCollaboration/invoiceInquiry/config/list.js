import { i18n } from '@/main.js'

export const invoiceStatusOptions = [
  { text: i18n.t('未过账'), value: 'OPEN' },
  { text: i18n.t('已过账'), value: 'CLEARED' }
]

export const columnData = [
  // {
  //   type: 'checkbox',
  //   width: 50,
  //   fixed: 'left',
  //   align: 'center'
  // },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'invoiceNo',
    title: i18n.t('发票号'),
    minWidth: 140
  },
  {
    field: 'invoiceStatus',
    title: i18n.t('发票状态'),
    formatter: ({ cellValue }) => {
      let item = invoiceStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'invoiceDate',
    title: i18n.t('发票日期'),
    minWidth: 160
  },
  {
    field: 'untaxedTotalPrice',
    title: i18n.t('发票未税总额'),
    minWidth: 120
  },
  {
    field: 'taxAmt',
    title: i18n.t('发票税额')
  },
  {
    field: 'currencyCode',
    title: i18n.t('发票币种')
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 200,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    }
  },
  {
    field: 'companyCode',
    title: i18n.t('公司'),
    minWidth: 200,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.companyName : ''
    }
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂'),
    minWidth: 200,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.siteName : ''
    }
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'operate',
    title: i18n.t('操作'),
    minWidth: 120,
    slots: {
      default: 'operateDetault'
    }
  }
]
