<template>
  <!-- 发票协同（供方）-详情 -->
  <div class="full-height pt20 vertical-flex-box">
    <!-- 头部信息 -->
    <top-info
      class="flex-keep"
      :entry-type="entryType"
      :header-info="headerInfo"
      @doSubmit="doSubmit"
      @goBack="goBack"
      @doExpand="doExpand"
    ></top-info>
    <mt-tabs
      class="flex-keep"
      tab-id="reconcilla-tab"
      :e-tab="false"
      :data-source="tabList"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <!-- 列模板 -->
    <div class="flex-fit" v-show="currentTabInfo.code !== TabCode.reconciliationFile">
      <mt-template-page
        ref="templateRef"
        v-show="isShowTemplatePage"
        :hidden-tabs="true"
        :current-tab="currentTab"
        :template-config="componentConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
        @showFileBaseInfo="showFileBaseInfo"
        @actionBegin="actionBegin"
        @actionComplete="actionComplete"
      >
      </mt-template-page>
    </div>
    <!-- 相关附件 -->
    <div class="flex-fit" v-show="currentTabInfo.code === TabCode.reconciliationFile">
      <relative-file ref="relativeFileRef" :module-file-list="moduleFileList"></relative-file>
    </div>
    <!-- 附件弹窗 -->
    <uploader-dialog
      ref="uploaderDialog"
      @confirm="uploaderDialogOnConfirm"
      @removeOne="uploaderDialogOnRemoveOne"
      @removeAll="uploaderDialogOnRemoveAll"
    ></uploader-dialog>
    <!-- 税控平台获取发票弹框 -->
    <itbc-table-dialog ref="itbcTableDialog" @confirm="itbcTableDialogConfirm"></itbc-table-dialog>
    <!-- 配置导入弹框 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
    <!-- 发票关联单据明细行弹框 -->
    <bill-details-table-dialog
      ref="billDetailsTableDialog"
      @confirm="billDetailsTableDialogConfirm"
    ></bill-details-table-dialog>
  </div>
</template>

<script>
import { formatTableColumnData, serializeInvoiceList } from './config/index.js'
import {
  ConstantType,
  InvoiceColumnData,
  Tab,
  TabCode,
  InvoiceListToolbarInLook,
  InvoiceListToolbarInEdit,
  RequestType,
  ActionType,
  NewRowData,
  InvoiceStatus,
  EditSettings,
  ComponentType,
  ComponentChangeType
} from './config/constant'
import { BASE_TENANT } from '@/utils/constant'
import { nodeType } from '@/components/businessComponents/relativeFileNoDocId/config/relative.js'
import { cloneDeep } from 'lodash'
import { download, getHeadersFileName } from '@/utils/utils'
import TopInfo from './components/topInfo.vue'
import ItbcTableDialog from './components/itbcTableDialog.vue'
import { rowDataTemp, invoiceTypeOptions } from './config/variable'
import RelativeFile from '@/components/businessComponents/relativeFileNoDocId/index.vue'
import UploaderDialog from '@/components/Upload/uploaderDialog'
import UploadExcelDialog from '@/components/Upload/uploadExcelDialog.vue'
import BillDetailsTableDialog from '@/components/businessComponents/billDetailsTableDialog'
import { BillDetailsTableDialogActionType } from '@/components/businessComponents/billDetailsTableDialog/config/constant'
import bigDecimal from 'js-big-decimal'
import { utils } from '@mtech-common/utils'

export default {
  components: {
    TopInfo,
    ItbcTableDialog,
    RelativeFile,
    UploaderDialog,
    UploadExcelDialog,
    BillDetailsTableDialog
  },
  data() {
    const lastTabIndex = JSON.parse(localStorage.getItem('lastTabIndex'))
    let summarySupplierV2Data = JSON.parse(localStorage.getItem('summarySupplierV2Data'))
    if (this.$route.query.come === 'query') {
      summarySupplierV2Data = JSON.parse(localStorage.getItem('querySupplierV2Data'))
    }
    const { entryType, headerInfo } = summarySupplierV2Data || {}

    // 已开票含税总额
    const theTotalInvoicedIncludingTax = this.calculateTheTotalInvoicedIncludingTax({ headerInfo })

    return {
      lastTabIndex, // 前一页面的 Tab index
      TabCode,
      tabList: [],
      currentTabInfo: {}, // 当前tab的数据
      downTemplateParams: {
        pageFlag: false
      }, // 发票导入下载模板参数
      uploadParams: { headerId: headerInfo?.id }, // 发票导入文件参数 主单ID
      // 发票导入请求接口配置
      requestUrls: {
        templateUrlPre: 'invoiceCollaboration',
        templateUrl: 'postReconciliationInvoiceV2Export', // 下载模板接口方法名
        uploadUrl: 'postReconciliationInvoiceV2DataImport' // 上传接口方法名
      },
      currentTab: 0, // 当前列模板显示的 Tab
      moduleFileList: [],
      entryType, // 页面状态
      apiWaitingQuantity: 0, // 调用的api正在等待数
      headerInfo: { ...headerInfo, theTotalInvoicedIncludingTax }, // 顶部的数据
      untaxedAmountTotal: 0, // 发票总金额（未税）
      includingTaxAmountTotal: 0, // 发票总金额（含税）
      isShowTemplatePage: false, // 初始化不显示
      componentConfig: [], // 列模板配置
      currentInvoiceAttachmentList: [], // 当前编辑发票附件列表 用于比较是否有变化 构建 deleteFileIdList
      currentDeleteFileIdList: [], // 当前弹框删除的附件id
      reconciliationDetailsColumnData: [], // 当前明细表的列数据
      isAdding: false, // 是否正在新增数据
      isEditing: false, // 正在编辑数据
      isClickedUploaderDialogOnConfirm: false, // 是否点击了上传附件按钮
      actionInvoiceV2AddDebounce: () => {}, // 新增发票（防抖）
      invoiceV2AddObj: {} // 新增发票对象
    }
  },
  mounted() {
    // 根据主单查询采方的字典 发票类型
    this.postSupplierDictByHeader().then(() => {
      // 先获取发票类型，后处理列模板的配置，以免发票类型没有转换
      this.setTemplateConfig()
    })
    this.actionInvoiceV2AddDebounce = utils.debounce(this.doActionInvoiceV2Add, 1000)
  },
  beforeDestroy() {
    // localStorage.removeItem("summarySupplierV2Data");
    localStorage.removeItem('querySupplierV2Data')
    // localStorage.removeItem("lastTabIndex");
  },
  methods: {
    // 处理列模板的配置
    setTemplateConfig() {
      // 获取动态表头
      const params = {
        tenantId: this.headerInfo?.customerTenantId, // 采方租户id
        reconciliationTypeCode: this.headerInfo?.reconciliationTypeCode, // 对账类型编码
        businessTypeCode: this.headerInfo?.businessTypeCode // 业务类型编码
      }
      this.apiStartLoading()
      // 获取 获取待对账类型字段-对账单明细
      this.$API.reconciliationCollaboration
        .postReconciliationWaitSupplierGetFieldsHeaderInfo(params)
        .then((res) => {
          this.apiEndLoading()
          const dataList = res?.data || []
          const configList = []

          // 发票清单
          configList.push(this.formatInvoiceList())

          dataList.forEach((itemTab) => {
            if (itemTab.code === TabCode.reconciliationField && itemTab.checkStatus) {
              // 对账明细
              configList.push(this.formatDetail(itemTab))
            } else if (itemTab.code === TabCode.reconciliationFile && itemTab.checkStatus) {
              // 相关附件
              configList.push(this.formatFile(itemTab))
            }
          })
          this.currentTabInfo = this.tabList[0] // 选中第一个Tab

          this.componentConfig = configList // 设置列模板
          this.isShowTemplatePage = true // 显示表格
        })
        .catch(() => {
          this.isShowTemplatePage = false // 不显示表格
          this.apiEndLoading()
        })
    },
    // 整合对账明细
    formatDetail(itemTab) {
      this.tabList.push({
        title: itemTab.name,
        code: TabCode.reconciliationField
      })
      const columnData = formatTableColumnData({
        tab: Tab.reconciliationDetails,
        data: itemTab.fieldResponseList || []
      })
      this.reconciliationDetailsColumnData = columnData // // 当前明细表的列数据

      return {
        tab: { title: itemTab.name },
        useToolTemplate: false, // 不使用预置(新增、编辑、删除)
        useBaseConfig: true, // 使用组件中的toolbar配置
        toolbar: [],
        gridId: this.$tableUUID.invoiceCollaboration.detailSupplierV2.reconDetailsTab,
        grid: {
          columnData,
          dataSource: [],
          asyncConfig: {
            url: `${BASE_TENANT}/reconciliationItemSupplier/queryBuilder`, // queryBuilder查询-对账明细信息
            defaultRules: [
              {
                field: 'headerId',
                operator: 'equal',
                value: this.headerInfo?.id
              }
            ]
          }
        }
      }
    },
    // 整合 相关附件
    formatFile() {
      // 不取后端配置
      const itemTab = {
        id: '69',
        code: 'reconciliationFile',
        name: this.$t('相关附件'),
        checkStatus: true,
        hide: null,
        rateDifferent: null,
        fieldResponseList: [
          {
            id: '11',
            code: 'scanHeaderFile',
            name: this.$t('发票扫描件'),
            checkStatus: true,
            hide: null,
            rateDifferent: null,
            fieldResponseList: null,
            sort: 0
          }
        ],
        sort: null
      }
      this.tabList.push({
        title: itemTab.name,
        code: TabCode.reconciliationFile
      })

      // 动态配置的文件类型
      itemTab.fieldResponseList.forEach((item) => {
        if (item.code === TabCode.scanHeaderFile && item.checkStatus) {
          // 扫描件
          if (this.entryType === ConstantType.edit) {
            this.moduleFileList.push({
              type: nodeType.mainUpdate,
              id: 'reconciliation_header',
              // 根据待对账id获取相关文件
              url: `${BASE_TENANT}/reconciliationHeaderSupplier/queryFileByDocIdAndDocType`, // 获取附件列表的url 根据docType和docI查询所有文件信息
              methods: 'get',
              params: {
                docId: this.headerInfo?.id,
                doctype: 'scan_header'
              },
              nodeName: item.name
            })
          } else {
            this.moduleFileList.push({
              type: nodeType.mainView,
              id: 'reconciliation_header',
              // 根据待对账id获取相关文件
              url: `${BASE_TENANT}/reconciliationHeaderSupplier/queryFileByDocIdAndDocType`, // 获取附件列表的url 根据docType和docI查询所有文件信息
              methods: 'get',
              params: {
                docId: this.headerInfo?.id,
                doctype: 'scan_header'
              },
              nodeName: item.name
            })
          }
        }
      })

      return {
        tab: { title: itemTab.name }
      }
    },
    // 整合 发票清单
    formatInvoiceList() {
      this.tabList.push({
        title: this.$t('发票清单'),
        code: TabCode.invoiceList
      })

      let toolbar = [] // 发票清单表头按钮
      let editSettings = {
        allowEditing: false // 不可编辑
      }
      if (this.entryType === ConstantType.edit) {
        // 编辑状态
        toolbar = InvoiceListToolbarInEdit
        editSettings = EditSettings
      } else if (this.entryType === ConstantType.look) {
        // 查看状态
        toolbar = InvoiceListToolbarInLook
      }

      return {
        tab: { title: this.$t('发票清单') },
        useToolTemplate: false, // 不使用预置(新增、编辑、删除)
        useBaseConfig: false, // 使用组件中的toolbar配置
        toolbar,
        grid: {
          editSettings,
          columnData: formatTableColumnData({
            tab: Tab.invoiceList,
            data: InvoiceColumnData,
            entryType: this.entryType
          }),
          allowPaging: false, // 不分页
          asyncConfig: {
            // 根据主单ID查询发票列表
            url: `${BASE_TENANT}/reconciliation-invoice-v2/list-by-header`,
            recordsPosition: 'data',
            params: {
              id: this.headerInfo.id
            },
            // 序列化发票列表数据
            serializeList: serializeInvoiceList
          },
          dataSource: []
          // frozenColumns: 1, // FIXME 不可以添加冻结列，使用了后 新增行数据有误报错后，重新进入编辑状态，代码将会报错
        }
      }
    },
    // 显示表格文件弹窗
    showFileBaseInfo(e) {
      const dialogParams = {
        fileData: cloneDeep(e.value),
        isView: true,
        title: this.$t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },

    // actionBegin 表格编辑生命周期
    actionBegin(args) {
      const { requestType, action, rowData } = args
      console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args)
      if (requestType === RequestType.add) {
        // 开始行添加
        this.isEditing = true
        this.isAdding = true
        this.isClickedUploaderDialogOnConfirm = false
        // 即将添加一行，赋值新增行的初始数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        const newRowData = cloneDeep(NewRowData)
        rowDataTemp.push(newRowData)
        args.rowData = newRowData
        args.data = newRowData
      } else if (requestType === RequestType.save && action === ActionType.add) {
        // 即将保存新增时，将新增中编辑的数据付给组件
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === RequestType.beginEdit) {
        // 开始行编辑
        if (
          rowData.status == InvoiceStatus.pendingReview ||
          rowData.status == InvoiceStatus.pendingFinancialReview ||
          rowData.status == InvoiceStatus.complete ||
          rowData.status == InvoiceStatus.deleted
        ) {
          this.$toast({
            content: this.$t('待确认，采购已确认，财务已确认不能编辑'),
            type: 'warning'
          })
          args.cancel = true
          return
        }
        this.isEditing = true
        this.isClickedUploaderDialogOnConfirm = false
        // 即将编辑行，赋值当前行的数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },
    // actionComplete 表格编辑生命周期
    actionComplete(args) {
      const { requestType, action, rowIndex, index } = args
      const rowData = rowDataTemp[rowDataTemp.length - 1]
      // console.log(
      //   `Complete,\nrequest: ${requestType}\naction: ${action}`,
      //   args
      // );
      if (requestType === RequestType.save && action == ActionType.edit) {
        // 完成编辑行
        this.isEditing = false
        // rowData, rowIndex
        if (!this.isValidSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          if (this.isAdding) {
            this.invoiceV2AddObj = {
              rowData,
              rowIndex: rowIndex
            }
            this.actionInvoiceV2AddDebounce() // 新增发票（防抖）
          } else {
            // 调用API 租户级-供方对账单发票信息-v2接口-更新
            this.putReconciliationInvoiceV2Update({
              rowData,
              rowIndex
            })
          }
        }
      } else if (requestType === RequestType.save && action == ActionType.add) {
        // 完成新增行
        this.isEditing = false
        if (!this.isValidSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          this.invoiceV2AddObj = {
            rowData,
            rowIndex: rowIndex || rowIndex === 0 ? rowIndex : index
          }
          this.actionInvoiceV2AddDebounce() // 新增发票（防抖）
        }
      } else if (requestType === RequestType.sorting || requestType === RequestType.refresh) {
        // 丢弃编辑中状态
        this.isEditing = false
        this.isAdding = false
        this.isClickedUploaderDialogOnConfirm = false
      }
    },
    // 校验数据
    isValidSaveData(data) {
      const {
        invoiceType,
        invoiceNum,
        invoiceTime,
        invoiceTaxedAmount,
        invoiceUntaxAmount,
        invoiceTaxAmount,
        itemIds
      } = data
      let valid = true
      if (!itemIds || itemIds.length === 0) {
        valid = false
        this.$toast({
          content: this.$t('请关联单据明细行'),
          type: 'warning'
        })
      } else if (!invoiceType) {
        this.$toast({ content: this.$t('请选择发票类型'), type: 'warning' })
        valid = false
      } else if (!invoiceNum) {
        this.$toast({ content: this.$t('请输入发票号'), type: 'warning' })
        valid = false
      } else if (!invoiceTime) {
        this.$toast({ content: this.$t('请选择开票日期'), type: 'warning' })
        valid = false
      } else if (invoiceTaxedAmount === undefined || invoiceTaxedAmount === null) {
        this.$toast({
          content: this.$t('请输入发票含税金额'),
          type: 'warning'
        })
        valid = false
      } else if (invoiceUntaxAmount === undefined || invoiceUntaxAmount === null) {
        this.$toast({
          content: this.$t('请输入发票未税金额'),
          type: 'warning'
        })
        valid = false
      } else if (invoiceUntaxAmount && invoiceTaxedAmount < invoiceUntaxAmount) {
        this.$toast({
          content: this.$t('含税金额不能小于未税金额'),
          type: 'warning'
        })
        valid = false
      } else if (invoiceTaxedAmount && invoiceUntaxAmount > invoiceTaxedAmount) {
        this.$toast({
          content: this.$t('未税金额不能大于含税金额'),
          type: 'warning'
        })
        valid = false
      } else if (invoiceTaxAmount === undefined || invoiceTaxAmount === null) {
        this.$toast({ content: this.$t('税额不可为空'), type: 'warning' })
        valid = false
      }

      return valid
    },
    // 格式化附件信息
    formatUploadFiles(list) {
      const tmp = []
      list.forEach((item) => {
        tmp.push({
          fileName: item.fileName, //	文件上传名称
          fileSize: item.fileSize, //	文件大小
          fileType: item.fileType, //	文件类型
          sysFileId: item.id || item.sysFileId, //	文件ID(MT_WP_SYS_FILE表ID)
          url: item.url, //	文件路径
          docId: this.headerInfo.id, //	单据ID
          parentId: 0
        })
      })

      return tmp
    },
    // 对比发票附件列表
    diffInvoiceFilesList(data) {
      let fileList = []
      if (data && data.length) {
        fileList = data
      }
      const invoiceFileIdInit = [] // 发票初始 id 列表
      const invoiceFileId = [] // 当前表格所有的附件id
      const deleteFileList = [] // 被删除的发票文件

      this.currentInvoiceAttachmentList.forEach((item) => {
        invoiceFileIdInit.push(item.id) // 发票初始附件 id 列表
      })
      fileList.forEach((item) => {
        invoiceFileId.push(item.id) // 当前表格所有的附件id
      })

      // 新增的发票文件 id 列表
      const invoiceFileIdAdd = invoiceFileIdInit
        .concat(invoiceFileId)
        .filter((item) => !invoiceFileIdInit.includes(item))

      // 新增的发票文件列表
      let invoiceFilesAdd = fileList.filter((item) => invoiceFileIdAdd.includes(item.id))
      invoiceFilesAdd = this.formatUploadFiles(invoiceFilesAdd)

      // 被删除的发票文件
      if (this.currentDeleteFileIdList.length > 0) {
        this.currentInvoiceAttachmentList.forEach((item) => {
          if (this.currentDeleteFileIdList.includes(item.id)) {
            deleteFileList.push(item) // 被删除的发票文件
          }
        })
      }

      return {
        invoiceFilesAdd,
        deleteFileList
      }
    },
    // 前端计算 已开票含税总额
    calculateTheTotalInvoicedIncludingTax(args) {
      const { headerInfo } = args
      // 已开票含税总额 = 执行含税总额 - 未开票含税总额
      const executeTaxedTotalPrice = headerInfo?.executeTaxedTotalPrice || 0
      const taxInvoiceBalance = headerInfo?.taxInvoiceBalance || 0
      return bigDecimal.subtract(executeTaxedTotalPrice, taxInvoiceBalance)
    },
    doSubmit() {
      const params = {
        id: this.headerInfo.id // 对账单ID
      }
      this.$API.reconciliationCollaboration.getInvoiceListByHeader(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          if (!data || !data.length) {
            this.$toast({ content: this.$t('请先上传发票附件'), type: 'warning' })
            return
          }
          for (let i = 0; i < data.length; i++) {
            if (!data[i]['attachementCount']) {
              this.$toast({ content: this.$t('请先上传发票附件'), type: 'warning' })
              return
            }
          }
          // 租户级-供方对账单发票信息-v2接口-提交
          this.postReconciliationInvoiceV2SubmitByHeader(params)
        }
      })
    },
    goBack() {
      // 返回 发票协同（供方）列表
      // localStorage.setItem('tabIndex', JSON.stringify(this.lastTabIndex))
      // this.$router.push({
      //   name: 'invoice-summary-supplier-v2'
      // })
      this.$router.go(-1)
    },
    doExpand() {
      this.$refs.templateRef?.resetGridHeight && this.$refs.templateRef.resetGridHeight()
    },
    // ToolBar
    handleClickToolBar(args) {
      const { grid, toolbar } = args
      const selectRows = grid.getSelectedRecords()
      const commonToolbar = [
        'Filter',
        'Refresh',
        'Setting',
        'InvoiceAdd',
        'GetInvoice',
        'InvoiceImport',
        'InvoiceRefresh',
        'InvoiceExport',
        'InvoiceUpdate',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]

      if (
        this.isEditing &&
        toolbar.id !== 'refreshDataByLocal' &&
        toolbar.id !== 'InvoiceRefresh'
      ) {
        // 结束编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }

      if (selectRows.length === 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedId = []
      selectRows.forEach((item) => selectedId.push(item.id))

      if (toolbar.id === 'InvoiceAdd') {
        // 发票清单 新增
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id === 'InvoiceDelete') {
        // 发票清单 删除
        const isValid = this.isValidDeleteInvoiceData(selectRows)
        if (isValid) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除选中的数据？')
            },
            success: () => {
              this.deleteReconciliationInvoiceV2BatchDelete({
                ids: selectedId
              })
            }
          })
        } else {
          this.$toast({
            content: this.$t('请选择待提交或已退回状态的发票'),
            type: 'warning'
          })
        }
      } else if (toolbar.id === 'GetInvoice') {
        // 发票清单 税控平台获取
        this.$refs.itbcTableDialog.dialogInit({
          title: this.$t('税控平台获取'),
          headerInfo: this.headerInfo
        })
      } else if (toolbar.id === 'InvoiceImport') {
        // 导入
        this.showUploadExcel(true)
      } else if (toolbar.id === 'InvoiceExport') {
        // 导出
        this.postReconciliationInvoiceV2Export()
      } else if (toolbar.id === 'InvoiceUpdate') {
        // 更新 结束编辑
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      } else if (toolbar.id === 'InvoiceRefresh') {
        this.$refs.templateRef.refreshCurrentGridData()
      }
    },
    // CellTool
    handleClickCellTool(args) {
      const { tool, data } = args
      if (tool.id === 'InvoiceDelete') {
        // 发票清单 删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            this.deleteReconciliationInvoiceV2BatchDelete({
              ids: [data.id]
            })
          }
        })
      }
    },
    // CellTitle
    handleClickCellTitle(args) {
      const { field, data, componentType } = args
      if (field === 'fileList' || field === 'attachementCount') {
        // 点击 发票附件
        this.handleAttachementCount({ data, actionType: componentType })
      } else if (field === 'itemIds' || field === 'reconciliationItemCount') {
        // 点击 关联明细
        this.handleReconciliationItemCountClick({
          data,
          actionType: componentType
        })
      }
    },
    // 点击 发票附件
    handleAttachementCount(args) {
      const { data, actionType } = args
      let isView = true
      if (actionType === ComponentType.view) {
        isView = true
      } else if (actionType === ComponentType.edit) {
        isView = false
      }
      if (data.attachementCount > 0) {
        //  发票附件 数量大于0
        if (this.isAdding || this.isClickedUploaderDialogOnConfirm) {
          // 新增状态 || 点击过附件确认按钮 从 rowDataTemp 中获取文件列表
          const fileData = cloneDeep(rowDataTemp[rowDataTemp.length - 1].fileList)
          // 显示附件查看编辑弹框
          const dialogParams = {
            fileData,
            isView,
            fieldName: 'invoiceAttachmentList',
            title: isView ? this.$t('发票附件') : this.$t('上传发票附件')
          }
          this.$refs.uploaderDialog.dialogInit(dialogParams)
        } else {
          // 编辑状态 && 点击过附件确认按钮
          // 获取发票附件数据 后 显示 发票附件列表 弹框
          const params = {
            id: data.id
          }
          this.apiStartLoading()
          // 获取发票附件列表 租户级-供方对账单发票信息-v2接口-查询附件列表
          this.$API.invoiceCollaboration
            .postReconciliationInvoiceV2FileList(params)
            .then((res) => {
              this.apiEndLoading()
              const fileData = res?.data || []
              if (rowDataTemp[rowDataTemp.length - 1]) {
                rowDataTemp[rowDataTemp.length - 1].fileList = fileData
              }
              this.currentInvoiceAttachmentList = fileData || [] // 保留正在编辑的发票附件列表
              // 显示附件查看编辑弹框
              const dialogParams = {
                fileData,
                isView,
                fieldName: 'invoiceAttachmentList',
                title: isView ? this.$t('发票附件') : this.$t('上传发票附件')
              }
              this.currentDeleteFileIdList = []
              this.$refs.uploaderDialog.dialogInit(dialogParams)
            })
            .catch((err) => {
              console.error(err)
              this.apiEndLoading()
            })
        }
      } else {
        // 显示附件查看编辑弹框
        const dialogParams = {
          fileData: [],
          isView,
          fieldName: 'invoiceAttachmentList',
          title: isView ? this.$t('发票附件') : this.$t('上传发票附件')
        }
        this.$refs.uploaderDialog.dialogInit(dialogParams)
      }
    },
    // 点击 关联明细
    handleReconciliationItemCountClick(args) {
      const { data, actionType } = args
      let dialogActionType = BillDetailsTableDialogActionType.view

      // 明细行
      let dataItemRules = []
      let dataItemParams = {
        dataId: data.id // 发票新增时传 0，编辑、查看时传 发票id
      }
      let dataItemIds = rowDataTemp[rowDataTemp.length - 1]?.itemIds || []

      // 高低开
      let highLowInfoUrl = ''
      let highLowInfoParams = {}
      let highLowInfoIds = rowDataTemp[rowDataTemp.length - 1]?.highLowIds || []

      if (actionType === ComponentType.view) {
        // 查看
        dialogActionType = BillDetailsTableDialogActionType.view
        // 明细行
        dataItemParams = {
          dataId: data.id // 发票新增时传 0，编辑、查看时传 发票id
        }
        dataItemRules = [
          {
            field: 'invoiceId',
            operator: 'equal',
            value: data.id
          }
        ]
        // 高低开
        highLowInfoUrl = `${BASE_TENANT}/reconciliationHighLowSupplier/queryByInvoiceId`
        highLowInfoParams = {
          id: data.id // 发票id
        }
      } else if (actionType === ComponentType.edit) {
        // 编辑
        dialogActionType = BillDetailsTableDialogActionType.edit
        // 明细行
        dataItemParams = {
          dataId: data.id || 0 // 发票新增时传 0，编辑、查看时传 发票id
        }
        dataItemRules = [
          {
            field: 'headerId',
            operator: 'equal',
            value: this.headerInfo.id
          }
        ]
        // 高低开
        highLowInfoUrl = `${BASE_TENANT}/reconciliationHighLowSupplier/queryByReconciliationIdsAndInvoice`
        highLowInfoParams = {
          invoiceId: data.id || 0, // 发票新增时传 0，编辑时传 发票id
          reconciliationIds: [this.headerInfo.id]
        }
      }
      // 显示明细行列表弹框
      this.$refs.billDetailsTableDialog.dialogInit({
        title: this.$t('关联明细行列表'),
        actionType: dialogActionType,
        // 关联明细
        dataItemAsyncConfig: {
          // 新增发票时，dataId传0、defaultRule里面传headerId即可；
          // 修改发票时，dataId传发票id，defaultRule里面传headerId，能查出来关联了发票的明细行以及未关联发票的明细行
          // 查看发票明细行时，dataId传发票id，在defaultRule里面再传invoiceId对应当前发票id，只查出来当前发票关联的明细行
          url: `${BASE_TENANT}/supplier/reconciliationHeader/reconciliation-item-list`, // 发票协同-供方-查询对账明细
          condition: 'and',
          params: dataItemParams,
          defaultRules: dataItemRules
        },
        dataItemSelectRowsIds: dataItemIds,
        dataItemColumnData: this.reconciliationDetailsColumnData,
        dataItemData: {
          reconciliationTaxedAmount:
            rowDataTemp[rowDataTemp.length - 1]?.reconciliationTaxedAmount || 0, // 明细汇总含税金额
          reconciliationUntaxAmount:
            rowDataTemp[rowDataTemp.length - 1]?.reconciliationUntaxAmount || 0, // 明细汇总未税金额
          reconciliationTaxAmount: rowDataTemp[rowDataTemp.length - 1]?.reconciliationTaxAmount || 0 // 明细汇总税额
        },
        // 高低开
        highLowInfoAsyncConfig: {
          url: highLowInfoUrl,
          params: highLowInfoParams,
          recordsPosition: 'data'
        },
        highLowInfoSelectRowsIds: highLowInfoIds,
        highLowInfoData: {
          taxedHighLow: rowDataTemp[rowDataTemp.length - 1]?.taxedHighLow || 0, // 高低开汇总含税金额
          untaxedHighLow: rowDataTemp[rowDataTemp.length - 1]?.untaxedHighLow || 0, // 高低开汇总未税金额
          highLowInfoTaxAmount: rowDataTemp[rowDataTemp.length - 1]?.highLowInfoTaxAmount || 0 // 高低开汇总税额
        },
        currencyName: this.headerInfo.currencyName // 货币名称
      })
    },
    // 关联明细行列表弹框 点击确定按钮
    billDetailsTableDialogConfirm(args) {
      const {
        // 明细行
        itemIds,
        theUntaxedTotalPriceTotal, // 明细行未税
        theTaxedTotalPriceTotal, // 明细行含税
        theTaxAmountTotal, // 明细行税额
        // 高低开
        highLowIds,
        theHighLowInfoTaxed, // 高低开汇总含税金额
        theHighLowInfoUntaxed, // 高低开汇总未税金额
        theHighLowInfoTaxAmount // 高低开汇总税额
      } = args

      const reconciliationItemCountEvent = {
        requestKey: 'reconciliationItemCount', // 触发请求的key
        modifiedKeys: [
          'reconciliationItemCount',
          'itemIds',
          'reconciliationTaxedAmount', // 明细汇总含税金额
          'reconciliationUntaxAmount', // 明细汇总未税金额
          'reconciliationTaxAmount', // 明细汇总税额
          'highLowIds',
          'taxedHighLow', // 高低开汇总含税金额
          'untaxedHighLow', // 高低开汇总未税金额
          'highLowInfoTaxAmount', // 高低开汇总税额
          'invoiceTaxedAmount', // 发票含税金额
          'invoiceUntaxAmount', // 发票未税金额
          'invoiceTaxAmount' // 发票税额
        ], // 要被修改的key列表
        changeType: ComponentChangeType.change, // 修改类型
        data: {
          reconciliationItemCount: itemIds?.length || 0, // 关联明细行数量
          itemIds, // 明细行ID
          reconciliationTaxedAmount: Number(theTaxedTotalPriceTotal), // 明细汇总含税金额
          reconciliationUntaxAmount: Number(theUntaxedTotalPriceTotal), // 明细汇总未税金额
          reconciliationTaxAmount: Number(theTaxAmountTotal), // 明细汇总税额

          highLowIds, // 高低开ID
          taxedHighLow: Number(theHighLowInfoTaxed), // 高低开汇总含税金额
          untaxedHighLow: Number(theHighLowInfoUntaxed), // 高低开汇总未税金额
          highLowInfoTaxAmount: Number(theHighLowInfoTaxAmount), // 高低开汇总税额

          invoiceTaxedAmount: Number(bigDecimal.add(theTaxedTotalPriceTotal, theHighLowInfoTaxed)), // 使 发票含税金额，默认等于 明细含税总额 + 高低开含税总额
          invoiceUntaxAmount: Number(
            bigDecimal.add(theUntaxedTotalPriceTotal, theHighLowInfoUntaxed)
          ), // 使 发票未税金额，默认等于 明细未税总额 + 高低开未税总额
          invoiceTaxAmount: Number(bigDecimal.add(theTaxAmountTotal, theHighLowInfoTaxAmount)) // 使 发票税额，默认等于 明细税额 + 高低开税额
        } // 发出的值
      }
      rowDataTemp[rowDataTemp.length - 1].reconciliationItemCount = itemIds?.length || 0
      rowDataTemp[rowDataTemp.length - 1].itemIds = itemIds
      rowDataTemp[rowDataTemp.length - 1].highLowIds = highLowIds
      this.$bus.$emit(
        'invoiceCollaborationDetailSupplierColumnChange',
        reconciliationItemCountEvent
      )
    },
    // 附件弹框文件点击确认
    uploaderDialogOnConfirm(args) {
      const { invoiceAttachmentList, viewFileData } = args
      if (invoiceAttachmentList) {
        // 正在编辑发票的行附件数据
        let attachmentList = [] // 发票行附件数据
        if (
          invoiceAttachmentList.length === 0 &&
          viewFileData.length > 0 &&
          this.currentDeleteFileIdList.length === 0
        ) {
          // 没有编辑附件，但是有预览附件，行附件为预览文件
          attachmentList = viewFileData
        } else {
          attachmentList = invoiceAttachmentList
        }
        this.isClickedUploaderDialogOnConfirm = true
        // $bus 更新正在编辑的行附件数据 附件数量 attachementCount 附件列表 fileList
        const attachementCount = attachmentList.length
        const fileList = attachmentList
        const e = {
          requestKey: 'fileList', // 触发请求的key
          modifiedKeys: ['attachementCount', 'fileList'], // 要被修改的key列表
          changeType: ComponentChangeType.change, // 修改类型
          data: {
            attachementCount, // 附件数量
            fileList // 附件列表
          } // 发出的值
        }
        rowDataTemp[rowDataTemp.length - 1].attachementCount = attachementCount
        rowDataTemp[rowDataTemp.length - 1].fileList = fileList
        this.$bus.$emit('invoiceCollaborationDetailSupplierColumnChange', e)
      }
    },
    // 附件弹框文件点击删除一条
    uploaderDialogOnRemoveOne(args) {
      const { removedId } = args
      this.currentDeleteFileIdList.push(removedId)
    },
    // 附件弹框文件点击删除全部
    uploaderDialogOnRemoveAll(args) {
      const { removedIds } = args
      this.currentDeleteFileIdList.concat(removedIds)
    },
    // 税控平台获取弹框 点击确认
    itbcTableDialogConfirm(args) {
      const { data: selectedInvoices } = args
      if (selectedInvoices.length > 0) {
        const params = []
        selectedInvoices.forEach((itemData) => {
          let invoiceTypeName = ''
          invoiceTypeOptions.forEach((item) => {
            if (item.value == itemData.invoiceType) {
              invoiceTypeName = item.text
            }
          })
          const itemInvoice = {
            reconciliationHeaderIds: this.headerInfo.id,
            invoiceCode: itemData.invoiceCode, // 发票代码
            invoiceNum: itemData.invoiceNum, // 发票号
            invoiceTaxAmount: itemData.taxAmount, // 税额
            invoiceTaxedAmount: itemData.taxedAmount, // 发票含税金额
            invoiceTime: Number(new Date(itemData.invoiceTime)), // 开票日期 2022-04-15 17:48:56 -> 1555017600000
            invoiceTypeCode: itemData.invoiceType, // 发票类型
            invoiceTypeName: invoiceTypeName, // 发票类型名称
            invoiceUntaxAmount: itemData.untaxedAmount, // 发票未税金额
            submit: false
          }
          params.push(itemInvoice)
        })
        // 调用批量新增接口
        this.postReconciliationInvoiceV2BatchAdd(params)
      }
    },
    // 上传发票成功后，提示成功，刷新表格
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 显示隐藏上传弹框
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 点击 tab
    handleSelectTab(e) {
      this.currentTabInfo = this.tabList[e]
      this.currentTab = e
    },
    // 校验选择删除的发票数据
    isValidDeleteInvoiceData(data) {
      let isValid = true
      for (let i = 0; i < data.length; i++) {
        const itemStatus = data[i].status
        if (
          !(itemStatus == InvoiceStatus.pendingSubmission || itemStatus == InvoiceStatus.returned)
        ) {
          // 不是 待提交 || 已退回 状态的发票不能删除
          isValid = false
        }
      }
      return isValid
    },
    // 执行新增发票
    doActionInvoiceV2Add() {
      // 租户级-供方对账单发票信息-v2接口-新增
      this.postReconciliationInvoiceV2Add(this.invoiceV2AddObj)
    },
    // 租户级-供方对账单发票信息-v2接口-新增
    postReconciliationInvoiceV2Add(args) {
      const { rowData, rowIndex } = args
      let invoiceTypeName = ''
      invoiceTypeOptions.forEach((item) => {
        if (item.value == rowData.invoiceType) {
          invoiceTypeName = item.text
        }
      })
      const rowDataFileList = rowDataTemp[rowDataTemp.length - 1].fileList || [] // 行发票附件列表
      const fileList = this.formatUploadFiles(rowDataFileList)
      const params = {
        reconciliationHeaderIds: this.headerInfo.id,
        fileList, // 发票附件列表
        invoiceCode: rowData.invoiceCode, // 发票代码
        checkCode: rowData.checkCode, // 后六位校验码
        invoiceNum: rowData.invoiceNum, // 发票号
        invoiceTaxAmount: rowData.invoiceTaxAmount, // 税额
        invoiceTaxedAmount: rowData.invoiceTaxedAmount, // 发票含税金额
        invoiceTime: Number(rowData.invoiceTime), // 开票日期
        invoiceTypeCode: rowData.invoiceType, // 发票类型
        invoiceTypeName: invoiceTypeName, // 发票类型名称
        invoiceUntaxAmount: rowData.invoiceUntaxAmount, // 发票未税金额
        itemIds: rowData.itemIds, // 明细行ID
        highLowIds: rowData.highLowIds, // 高低开行ID
        supplierRemark: rowData.supplierRemark, // 供方备注
        submit: false,
        reconciliationBusinessTypeCode: this.$route.query?.businessTypeCode
      }
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postReconciliationInvoiceSpecialAdd(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.isAdding = false
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.upToDateTaxInvoiceBalance()
            // 发票新增成功 刷新 发票列表 tab
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    // 租户级-供方对账单发票信息-v2接口-更新
    putReconciliationInvoiceV2Update(args) {
      // 被删除的发票文件列表
      const { rowData, rowIndex } = args
      const { deleteFileList: deleteFileIdList, invoiceFilesAdd } = this.diffInvoiceFilesList(
        rowDataTemp[rowDataTemp.length - 1].fileList
      )
      let invoiceTypeName = ''
      invoiceTypeOptions.forEach((item) => {
        if (item.value == rowData.invoiceType) {
          invoiceTypeName = item.text
        }
      })
      const params = {
        deleteFileIdList, // 删除发票附件列表
        fileList: invoiceFilesAdd, // 发票附件列表
        id: rowData.id, // 发票id
        invoiceCode: rowData.invoiceCode, // 发票代码
        checkCode: rowData.checkCode, // 后六位校验码
        invoiceNum: rowData.invoiceNum, // 发票号
        invoiceTaxAmount: rowData.invoiceTaxAmount, // 税额
        invoiceTaxedAmount: rowData.invoiceTaxedAmount, // 发票含税金额
        invoiceTime: Number(rowData.invoiceTime), // 开票日期
        invoiceTypeCode: rowData.invoiceType, // 发票类型
        invoiceTypeName: invoiceTypeName, // 发票类型名称
        invoiceUntaxAmount: rowData.invoiceUntaxAmount, // 发票未税金额
        itemIds: rowData.itemIds, // 明细行ID
        highLowIds: rowData.highLowIds, // 高低开行ID
        supplierRemark: rowData.supplierRemark // 供方备注
      }
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .putReconciliationInvoiceV2Update(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.upToDateTaxInvoiceBalance()
            // 发票更新成功 刷新 发票列表 tab
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    // 租户级-供方对账单发票信息-v2接口-批量新增
    postReconciliationInvoiceV2BatchAdd(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postReconciliationInvoiceV2Add(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.isAdding = false
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.upToDateTaxInvoiceBalance()
            // 发票新增成功 刷新 发票列表 tab
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 提交对账发票信息
    postReconciliationInvoiceV2SubmitByHeader(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postReconciliationInvoiceV2SubmitByHeader(params)
        .then((res) => {
          this.apiEndLoading()
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 返回 发票协同-列表
            this.goBack()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 调用api查询主单信息 更新 头部 未开票含税总额
    upToDateTaxInvoiceBalance() {
      // 请求 api 根据单据 code、id 获取单据数据
      const params = {
        page: { current: 1, size: 10 },
        condition: 'and',
        defaultRules: [
          {
            field: 'reconciliationCode',
            type: 'string',
            operator: 'equal',
            value: this.headerInfo.reconciliationCode
          },
          {
            field: 'id',
            type: 'string',
            operator: 'equal',
            value: this.headerInfo.id
          }
        ]
      }
      // 获取单据信息 租户级-发票协同-供方主单-对账信息
      this.$API.invoiceCollaboration
        .postSupplierReconciliationHeaderQueryBuilder(params)
        .then((res) => {
          const reconciliationHeaderInfo = res?.data?.records[0] || {}
          this.headerInfo.taxInvoiceBalance = reconciliationHeaderInfo?.taxInvoiceBalance // 更新 未开票含税总额
        })
        .catch(() => {})
    },
    // 租户级-供方对账单发票信息-v2接口-批量删除
    deleteReconciliationInvoiceV2BatchDelete(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .deleteReconciliationInvoiceV2BatchDelete(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 发票删除成功 刷新 发票列表 tab
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-发票协同-供方-导出
    postReconciliationInvoiceV2Export() {
      const params = {
        pageFlag: true,
        page: { current: 1, size: 10000 },
        dataId: this.headerInfo.id,
        defaultRules: []
      } // 筛选条件
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postReconciliationInvoiceV2Export(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 根据主单查询采方的字典 发票类型
    postSupplierDictByHeader() {
      return this.$API.invoiceCollaboration
        .postSupplierDictByHeader({
          dataId: this.headerInfo.id, // 主单id
          dictCode: 'invoiceType' // 字典编码
        })
        .then((res) => {
          const data = res?.data || []
          invoiceTypeOptions.splice(0, invoiceTypeOptions.length) // 清空
          data.forEach((item) => {
            invoiceTypeOptions.push({
              text: item.itemName,
              value: item.itemCode
            })
          })
        })
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
// .amount-total {
//   text-align: right;
//   padding: 30px 15px;

//   .item {
//     margin-bottom: 10px;
//   }
// }

.mt-tabs {
  width: 100%;
  /deep/.mt-tabs-container {
    width: 100%;
  }
}

/deep/ .able-click-field {
  color: var(--plugin-ct-content-color);
  font-size: 14px;
  cursor: pointer;
  text-align: left;
  &:hover {
    font-weight: 500;
  }
}

/deep/ .column-tool {
  margin-top: 8px;
}
/deep/ .template-svg {
  cursor: pointer;
  font-size: 12px;
  color: var(--plugin-ct-cell-icon-color);

  &:nth-child(n + 2) {
    margin-left: 10px;
  }
}
.flex-fit {
  overflow: unset;
}
</style>
