<template>
  <!-- 发票协同（供方）列表 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      :hidden-tabs="false"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
    <!-- 附件弹窗 -->
    <uploader-dialog ref="uploaderDialog"></uploader-dialog>
    <!-- <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      :permission-obj="permissionObj"
      :hidden-tabs="false"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page> -->
  </div>
</template>

<script>
import { formatTableColumnData, serializeList } from './config/index.js'
import {
  InvoicesColumnData,
  Tab

  // InvoiceListDialogActionType,
} from './config/constant'
import { BASE_TENANT } from '@/utils/constant'
import { cloneDeep } from 'lodash'
import UploaderDialog from '@/components/Upload/uploaderDialog'
import { ConstantType } from '../detailSupplierV2/config/constant'

export default {
  components: { UploaderDialog },
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      slotTabList: [], // 公司列表
      slotTabTypeList: [], // 当前公司下的 对账类型列表
      currentPurTenantId: '', // 当前选择的采方租户id
      currentSlotTypeTabIndex: 0, // 对账类型序号
      asyncStatementDetailsColumnData: [], // 从 API 获取的表头
      isShowSlotTemplateRef: false, // 初始化不显示
      // permissionObj: {
      //   permissionNode: {
      //     // 当前的dom元素
      //     code: "ignore-element",
      //     type: "remove",
      //   },
      //   childNode: [
      //     { dataPermission: "BillableDocuments", permissionCode: "T_02_0010" },
      //     { dataPermission: "BillableDetails", permissionCode: "T_02_0011" },
      //     { dataPermission: "ListOfInvoices", permissionCode: "T_02_0012" },
      //   ],
      // },
      componentConfig: [
        {
          // dataPermission: "ListOfInvoices",
          // permissionCode: "T_02_0012",
          toolbar: [],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId: this.$tableUUID.invoiceCollaboration.summarySupplierV2.queryOfInvoices,
          grid: {
            lineIndex: 0,
            columnData: formatTableColumnData({
              data: InvoicesColumnData,
              tab: Tab.invoices
            }),
            dataSource: [],
            // 租户级-对账单发票信息-v2接口-分页查询
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliation-invoice-v2/paged-query`,
              defaultRules: [],
              serializeList
            }
          }
        }
      ]
    }
  },
  mounted() {
    // 获取客户对象
    this.getCustomerReconciliationType()
  },
  methods: {
    // CellTool
    handleClickCellTool(args) {
      const { tool, data } = args
      if (tool.id === 'EditInvoice') {
        // 发票列表-发票编辑
        this.handleEditInvoice({ data })
      } else if (tool.id === 'DeleteInvoice') {
        // 发票列表-发票删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            this.deleteReconciliationInvoiceV2BatchDelete({
              ids: [data.id]
            })
          }
        })
      } else if (tool.id === 'SubmitInvoice') {
        // 发票列表-发票提交
        this.postReconciliationInvoiceV2BatchSubmit({
          ids: [data.id]
        })
      } else if (tool.id === 'CloseInvoice') {
        // 发票列表-发票关闭
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认关闭选中的数据？')
          },
          success: () => {
            this.deleteReconciliationInvoiceV2BatchClose({
              ids: [data.id]
            })
          }
        })
      } else if (tool.id === 'SubmitBill') {
        // 单据提交
        const params = {
          id: data.id // 对账单ID
        }
        // 租户级-供方对账单发票信息-v2接口-提交
        this.postReconciliationInvoiceV2SubmitByHeader(params)
      }
    },
    // CellTitle
    handleClickCellTitle(args) {
      const { field, data, tabIndex, fieldValue } = args
      if (field === 'reconciliationCode') {
        // 点击 单据号
        this.handleReconciliationCode({ data, tabIndex })
      } else if (field === 'attachementCount') {
        // 点击 发票附件 数量
        this.handleAttachementCount({ data })
      } else if (field === 'reconciliationItemCount') {
        // 点击 关联明细 数量
        // this.handleReconciliationItemCountClick(data)
      } else if (field === 'reconciliationHeaderCodes') {
        // 点击 关联对账单号
        this.handleReconciliationHeaderCodes({
          data,
          fieldValue
        })
      }
    },
    // 点击 发票附件 数量
    handleAttachementCount(args) {
      const { data } = args
      // 获取发票附件数据 后 显示 发票附件列表 弹框
      const params = {
        id: data.id
      }
      this.apiStartLoading()
      // 获取发票附件列表 租户级-供方对账单发票信息-v2接口-查询附件列表
      this.$API.invoiceCollaboration
        .postReconciliationInvoiceV2FileList(params)
        .then((res) => {
          this.apiEndLoading()
          const fileData = res?.data || []
          // 显示附件查看弹框
          const dialogParams = {
            fileData: fileData,
            isView: true,
            title: this.$t('发票附件')
          }
          this.$refs.uploaderDialog.dialogInit(dialogParams)
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 点击 单据号
    handleReconciliationCode(args) {
      const { data, tabIndex } = args
      // 从 可开票单据 跳转 单据详情页
      // 将 lastTabIndex 放到 localStorage 客户对账协同（供方）-详情 读
      localStorage.setItem('lastTabIndex', JSON.stringify(tabIndex))
      this.goToInvoiceDetailSupplier({
        headerInfo: data,
        entryType: ConstantType.look
      })
    },
    // 点击 关联对账单号
    handleReconciliationHeaderCodes(args) {
      const { data, fieldValue: reconciliationCode } = args
      // 从 发票列表 跳转 单据详情页
      const reconciliationHeaderIdIndex = data.reconciliationHeaderCodes.findIndex(
        (item) => item === reconciliationCode
      )
      // 请求 api 根据单据 code、id 获取单据数据
      const params = {
        page: { current: 1, size: 10 },
        condition: 'and',
        defaultRules: [
          {
            field: 'reconciliationCode',
            type: 'string',
            operator: 'equal',
            value: reconciliationCode
          },
          {
            field: 'id',
            type: 'string',
            operator: 'equal',
            value: data.reconciliationHeaderIds[reconciliationHeaderIdIndex].supplierHeaderId
          }
        ]
      }
      this.apiStartLoading()
      // 获取单据信息 租户级-发票协同-供方主单-对账信息
      this.$API.invoiceCollaboration
        .postSupplierReconciliationHeaderQueryBuilder(params)
        .then((res) => {
          this.apiEndLoading()
          const reconciliationHeaderInfo = res?.data?.records[0] || {}
          // 从 可开票单据 跳转 单据详情页
          // 将 lastTabIndex 放到 localStorage 客户对账协同（供方）-详情 读
          localStorage.setItem('lastTabIndex', JSON.stringify(2))
          this.goToInvoiceDetailSupplier({
            headerInfo: reconciliationHeaderInfo,
            entryType: ConstantType.look
          })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 跳转到发票编辑页面
    goToInvoiceEditSupplier(data) {
      localStorage.setItem('invoiceEditSupplierData', JSON.stringify(data))
      // 跳转到发票编辑页面
      this.$router.push({
        name: 'invoice-edit-supplier',
        query: {
          timeStamp: new Date().getTime()
        }
      })
    },
    // 获取客户对象
    getCustomerReconciliationType() {
      this.$API.invoiceCollaboration
        .getCustomerReconciliationType()
        .then((res) => {
          const data = res?.data || []
          this.currentPurTenantId = data[0].purTenantId
          data.forEach((item) => {
            this.slotTabList.push({
              title: item.purTenantName,
              purTenantId: item.purTenantId,
              types: item.types
            })
          })
          this.setSlotTypeList(0)
        })
        .catch(() => {})
    },

    // 显示表格文件弹窗
    showFileBaseInfo(args) {
      const { value } = args
      const dialogParams = {
        fileData: cloneDeep(value),
        isView: true,
        title: this.$t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },

    // 跳转到 发票详情页面
    goToInvoiceDetailSupplier(data) {
      const { headerInfo, entryType } = data
      // 发票详情 页面参数
      const params = {
        reconciliationCode: headerInfo.reconciliationCode, // 对账单号
        headerInfo, // 头部信息 行数据
        entryType // 页面类型
      }
      localStorage.setItem('querySupplierV2Data', JSON.stringify(params))
      const isGeneral = headerInfo?.businessTypeCode === 'BTTCL004' ? true : false
      // 跳转到 发票详情页面
      this.$router.push({
        name: isGeneral ? 'invoice-detail-supplier-v2' : 'invoice-detail-supplier',
        query: {
          come: 'query',
          timeStamp: new Date().getTime()
        }
      })
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped>
.slot-select {
  display: flex;
  position: relative;
  align-items: center;
}
.pur-tenant-select {
  margin: 25px;
}
/deep/ .supReconTypeTabs {
  display: flex;
  width: calc(100% - 225px);
  flex: 1;
  overflow: auto;

  .mt-tabs-container {
    width: calc(100% - 225px);
  }
}
/deep/ .slot-component {
  height: calc(100% - 104px);

  .mt-data-grid {
    height: 100%;

    > .e-grid {
      height: calc(100% - 44px);

      .e-gridcontent {
        height: calc(100% - 44px);
        .e-content {
          height: calc(100% - 10px) !important;
        }
      }
    }
  }
}
</style>
