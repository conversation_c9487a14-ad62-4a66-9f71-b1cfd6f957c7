<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules">
      <mt-form-item prop="rejectReason" :label="$t('采方备注')" class="full-width">
        <mt-input
          maxlength="200"
          :disabled="false"
          v-model="formData.rejectReason"
          :show-clear-button="true"
          :placeholder="$t('采方备注')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { cloneDeep } from 'lodash'
import { InvoiceStatus } from '../config/constant'

export default {
  components: {},
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      dialogTitle: '',
      rules: {
        rejectReason: [
          {
            required: true,
            message: this.$t('请输入采方备注'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      selectData: {}, // 选中的数据
      formData: {
        rejectReason: '' // 采方备注
      }
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      const { title, selectData } = entryInfo
      this.selectData = cloneDeep(selectData)
      this.dialogTitle = title // 弹框名称
      this.initForm()
      this.$refs.dialog.ejsRef.show()
    },
    initForm() {
      this.formData = {
        rejectReason: '' // 采方备注
      }
      this.$refs.ruleForm.clearValidate()
    },
    onOpen(args) {
      args.preventFocus = true
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const params = {
            id: this.selectData.id, // 发票id
            pass: false,
            rejectReason: this.formData.rejectReason
          }
          if (this.selectData.status === InvoiceStatus.pendingReview) {
            // 待审核
            this.postCustomerReconciliationInvoiceV2purchaserConfirm(params)
          } else if (this.selectData.status === InvoiceStatus.pendingFinancialReview) {
            // 待财务审核 采购已确认
            this.postCustomerReconciliationInvoiceV2FinanceConfirm(params)
          }
        }
      })
    },
    // 采方采购确认
    postCustomerReconciliationInvoiceV2purchaserConfirm(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postCustomerReconciliationInvoiceV2purchaserConfirm(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.handleClose()
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm')
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方财务确认
    postCustomerReconciliationInvoiceV2FinanceConfirm(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postCustomerReconciliationInvoiceV2FinanceConfirm(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.handleClose()
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm')
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
