<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div :class="[billInvoiceStatusClass(headerInfo.invoiceStatus), 'mr20']">
        {{ headerInfo.invoiceStatus | billInvoiceStatusFormat }}
      </div>
      <div class="infos mr20">{{ $t('单据号：') }}{{ headerInfo.reconciliationCode }}</div>
      <div class="infos mr20">{{ $t('创建人：') }}{{ headerInfo.createUserName }}</div>
      <div class="infos">{{ $t('创建时间：') }}{{ headerInfo.createTime | dateFormat }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button
        css-class="e-flat"
        v-if="entryType == ConstantType.edit"
        :is-primary="true"
        @click="doSubmit"
        >{{ $t('提交') }}</mt-button
      >

      <div class="sort-box" @click="doExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="companyCode" :label="$t('公司编号')">
          <mt-input
            v-model="headerInfo.companyCode"
            :disabled="true"
            :placeholder="$t('公司编号')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="companyName" :label="$t('公司名称')">
          <mt-input
            v-model="headerInfo.companyName"
            :disabled="true"
            :placeholder="$t('公司名称')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplierCode" :label="$t('供应商编号')" :show-message="false">
          <mt-input
            v-model="headerInfo.supplierCode"
            :disabled="true"
            :placeholder="$t('供应商编号')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplierName" :label="$t('供应商名称')" :show-message="false">
          <mt-input
            v-model="headerInfo.supplierName"
            :disabled="true"
            :placeholder="$t('供应商名称')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="rateDifferent" :label="$t('差异率')" :show-message="false">
          <mt-input
            v-model="headerInfo.rateDifferent"
            :disabled="true"
            :placeholder="$t('差异率')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="executeUntaxedTotalPrice"
          :label="$t('对账未税总额')"
          :show-message="false"
        >
          <mt-input
            v-model="headerInfo.executeUntaxedTotalPrice"
            :disabled="true"
            :placeholder="$t('对账未税总额')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="taxAmount" :label="$t('对账税额')" :show-message="false">
          <mt-input
            v-model="headerInfo.taxAmount"
            :disabled="true"
            :placeholder="$t('对账税额')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="executeTaxedTotalPrice"
          :label="$t('对账含税总额')"
          :show-message="false"
        >
          <mt-input
            v-model="headerInfo.executeTaxedTotalPrice"
            :disabled="true"
            :placeholder="$t('对账含税总额')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="taxCode" :label="$t('税码')" :show-message="false">
          <mt-input
            v-model="headerInfo.taxCode"
            :disabled="true"
            :placeholder="$t('税码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="untaxedDifferentAmount" :label="$t('未税差异')" :show-message="false">
          <mt-input
            v-model="headerInfo.untaxedDifferentAmount"
            :disabled="true"
            :placeholder="$t('未税差异')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="invoiceUntaxAmountSum"
          :label="$t('发票未税总额')"
          :show-message="false"
        >
          <mt-input
            v-model="headerInfo.invoiceUntaxAmountSum"
            :disabled="true"
            :placeholder="$t('发票未税总额')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="invoiceTaxAmount" :label="$t('发票税额')" :show-message="false">
          <mt-input
            v-model="headerInfo.invoiceTaxAmount"
            :disabled="true"
            :placeholder="$t('发票税额')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="invoiceTaxedAmountSum"
          :label="$t('发票含税总额')"
          :show-message="false"
        >
          <mt-input
            v-model="headerInfo.invoiceTaxedAmountSum"
            :disabled="true"
            :placeholder="$t('发票含税总额')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="taxRate" :label="$t('税率')" :show-message="false">
          <mt-input
            v-model="headerInfo.taxRate"
            :disabled="true"
            :placeholder="$t('税率')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="taxAmountDifferent" :label="$t('税额差异')" :show-message="false">
          <mt-input
            v-model="headerInfo.taxAmountDifferent"
            :disabled="true"
            :placeholder="$t('税额差异')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          class="full-width"
          prop="remark"
          :label="$t('采方备注')"
          :show-message="false"
        >
          <mt-input
            v-model="headerInfo.remark"
            :disabled="true"
            :placeholder="$t('采方备注')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item
          class="full-width"
          prop="feedbackRemark"
          :label="$t('供方备注')"
          :show-message="false"
        >
          <mt-input
            v-model="headerInfo.feedbackRemark"
            :disabled="true"
            :placeholder="$t('供方备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { BillInvoiceStatusConst, BillInvoiceStatusCssClass, ConstantType } from '../config/constant'
import utils from '@/utils/utils'

export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    },
    entryType: {
      type: String,
      default: ConstantType.look
    }
  },
  data() {
    return {
      isExpand: true,
      rules: {},
      ConstantType
    }
  },
  mounted() {},
  filters: {
    dateFormat(value) {
      const date = new Date(value)
      if (isNaN(date.getTime())) {
        return value
      } else {
        return utils.formateTime(date, 'YYYY-mm-dd HH:MM:SS')
      }
    },
    billInvoiceStatusFormat(value) {
      if (!BillInvoiceStatusConst[value]) {
        return value
      } else {
        return BillInvoiceStatusConst[value]
      }
    }
  },
  methods: {
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 提交
    doSubmit() {
      this.$emit('doSubmit')
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    },
    // 发票状态转对应的 css class
    billInvoiceStatusClass(value) {
      let cssClass = ''
      if (BillInvoiceStatusCssClass[value]) {
        cssClass = BillInvoiceStatusCssClass[value]
      }
      return cssClass
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
