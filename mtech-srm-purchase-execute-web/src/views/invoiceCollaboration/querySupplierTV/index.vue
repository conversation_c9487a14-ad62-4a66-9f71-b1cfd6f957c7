<template>
  <!-- 发票协同（采方）列表 -->
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      :hidden-tabs="true"
      class="template-height"
      @handleCustomReset="handleCustomReset"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
      <template v-slot:quick-search-form>
        <mt-form ref="searchFormRef" :model="searchFormModel">
          <mt-form-item
            prop="reconciliationHeaderCodes"
            :label="$t('关联单据号')"
            label-style="top"
          >
            <mt-input
              v-model="searchFormModel.reconciliationHeaderCodes"
              :placeholder="$t('关联单据号')"
              :show-clear-button="true"
              @change="reconciliationHeaderCodesInput"
            />
          </mt-form-item>
          <mt-form-item prop="companyCodes" :label="$t('公司')" label-style="top">
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.companyCodes"
              url="/masterDataManagement/auth/company/auth-fuzzy"
              multiple
              :params="{
                organizationLevelCodes: ['ORG02', 'ORG01'],
                orgType: 'ORG001PRO',
                includeItself: true
              }"
              :placeholder="$t('请选择公司')"
              :fields="{ text: 'orgName', value: 'orgCode' }"
              records-position="data"
            ></RemoteAutocomplete>
          </mt-form-item>
          <!-- <mt-form-item prop="supplierCodes" :label="$t('供应商')" label-style="top">
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.supplierCodes"
              url="/masterDataManagement/tenant/supplier/paged-query"
              multiple
              :placeholder="$t('请选择供应商')"
              :fields="{ text: 'supplierName', value: 'supplierCode' }"
              :search-fields="['supplierName', 'supplierCode']"
            ></RemoteAutocomplete>
          </mt-form-item> -->
          <mt-form-item prop="factoryCodes" :label="$t('工厂')" class="top">
            <RemoteAutocomplete
              style="flex: 1"
              v-model="searchFormModel.factoryCodes"
              :url="$API.masterData.getSiteAuthFuzzyUrl"
              multiple
              :placeholder="$t('请选择工厂')"
              :fields="{ text: 'siteName', value: 'siteCode' }"
              params-key="fuzzyParam"
              records-position="data"
            ></RemoteAutocomplete>
          </mt-form-item>
          <mt-form-item prop="invoiceNum" :label="$t('发票号')" label-style="top">
            <mt-input
              v-model="searchFormModel.invoiceNum"
              :placeholder="$t('发票号码')"
              :show-clear-button="true"
            />
          </mt-form-item>
          <mt-form-item prop="currencyCode" :label="$t('币种')" label-style="top">
            <mt-select
              :allow-filtering="true"
              v-model="searchFormModel.currencyCode"
              :data-source="currencyOptions"
              :fields="{ text: 'label', value: 'value' }"
              :show-clear-button="true"
              :filtering="getCurrency"
              :placeholder="$t('请选择币种')"
            />
          </mt-form-item>
          <mt-form-item prop="invoiceTime" :label="$t('开票日期')" label-style="top">
            <mt-date-range-picker
              v-model="searchFormModel.invoiceTime"
              :placeholder="$t('请选择')"
              @change="(e) => dateTimeChange(e, 'invoiceTime')"
            />
          </mt-form-item>
          <mt-form-item prop="status" :label="$t('发票状态')" label-style="top">
            <mt-select
              v-model="searchFormModel.status"
              :data-source="statusOptions"
              :show-clear-button="true"
              :placeholder="$t('请选择')"
            />
          </mt-form-item>
          <mt-form-item prop="syncStatus" :label="$t('同步状态')" label-style="top">
            <mt-select
              style="flex: 1"
              v-model="searchFormModel.syncStatus"
              :data-source="[
                { value: 0, text: $t('未同步') },
                { value: 1, text: $t('已同步') }
              ]"
              :allow-filtering="true"
              :show-clear-button="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
            ></mt-select>
          </mt-form-item>
        </mt-form>
      </template>
    </mt-template-page>
    <!-- 附件弹窗 -->
    <uploader-dialog ref="uploaderDialog"></uploader-dialog>
    <!-- 发票关联单据明细行弹框 -->
    <bill-details-table-dialog ref="billDetailsTableDialog"></bill-details-table-dialog>
    <!-- 操作日志弹框 -->
    <view-logs-dialog ref="viewLogsDialog"></view-logs-dialog>
    <!-- 发票回退弹框 -->
    <send-back-dialog ref="sendBackDialog" @confirm="sendBackDialogConfirm"></send-back-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import { ColumnData, Toolbar, Table, TabCode } from './config/constant'
import { ConstantType } from '../detailV2/config/constant'
import { formatTableColumnData, serializeList } from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'
import UploaderDialog from '@/components/Upload/uploaderDialog'
import ViewLogsDialog from './components/viewLogsDialog.vue'
import BillDetailsTableDialog from '@/components/businessComponents/billDetailsTableDialog'
import { BillDetailsTableDialogActionType } from '@/components/businessComponents/billDetailsTableDialog/config/constant'
import SendBackDialog from './components/sendBackDialog.vue'
import * as UTILS from '@/utils/utils'
export default {
  components: {
    ViewLogsDialog,
    UploaderDialog,
    BillDetailsTableDialog,
    SendBackDialog
  },
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      currencyOptions: [], //订单币种
      statusOptions: [
        { text: this.$t('待提交'), value: 0 },
        { text: this.$t('待确认'), value: 1 },
        { text: this.$t('财务已确认'), value: 3 },
        { text: this.$t('已退回'), value: 4 }
      ],
      apiWaitingQuantity: 0, // 调用的api正在等待数
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: Toolbar,
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          gridId: this.$tableUUID.invoiceCollaboration.summaryV2.query,
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: formatTableColumnData({
              table: Table.summary,
              data: ColumnData
            }),
            dataSource: [],
            // 租户级-发票协同-采方-采方采购-分页查询
            asyncConfig: {
              url: `${BASE_TENANT}/tv/invoice/supplier/pagedQuery`,
              defaultRules: [],
              serializeList
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  mounted() {
    this.getCurrency()
  },
  methods: {
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    reconciliationHeaderCodesInput() {
      this.searchFormModel['recoCodes'] = []
      if (this.searchFormModel.reconciliationHeaderCodes) {
        this.searchFormModel['recoCodes'] = [this.searchFormModel.reconciliationHeaderCodes]
      }
    },
    getCurrency(val, currencyCode) {
      let params = { fuzzyParam: val?.text || '' }
      this.$API.masterData.getCurrencyByFilter(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.currencyCode}-${item.currencyName}`
          item.text = item.currencyName
          item.value = item.currencyCode
        })
        this.currencyOptions = list
        if (currencyCode) {
          this.topInfo.currency = currencyCode
        }
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.currencyOptions)
          })
        }
      })
    },
    handleClickToolBar(e) {
      const selectedRecords = e.gridRef.getMtechGridRecords()
      const commonToolbar = [
        'InvoiceAdd',
        'InvoiceExport',
        'DetailExcelExport',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]
      if (selectedRecords.length == 0 && !commonToolbar.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.reconciliationHeaderIds)
      })

      if (
        (e.toolbar.id === 'InvoiceSubmit' ||
          e.toolbar.id === 'InvoiceEdit' ||
          e.toolbar.id === 'InvoiceDelete') &&
        !selectedRecords.every(
          (i) => !i.reconciliationHeaderCodes[0] || i.reconciliationHeaderCodes[0] === '0'
        )
      ) {
        this.$toast({
          content: this.$t('月结对账单（关联单据号是0，或者空）不能操作！'),
          type: 'warning'
        })
        return
      }

      if (e.toolbar.id === 'Print') {
        this.print(idList)
      } else if (e.toolbar.id === 'InvoiceExport') {
        this.handleExport(idList)
      } else if (e.toolbar.id === 'InvoiceDelete') {
        this.handleDelete(selectedRecords)
      } else if (e.toolbar.id === 'InvoiceAdd') {
        this.handleAdd()
      } else if (e.toolbar.id === 'InvoiceSubmit') {
        this.handleSubmit(selectedRecords)
      } else if (e.toolbar.id === 'InvoiceEdit') {
        // if (selectedRecords[0]['syncStatus'] !== 0 || selectedRecords[0]['status'] !== 0) {
        if (selectedRecords.some((i) => i.syncStatus !== 0 || (i.status !== 0 && i.status !== 4))) {
          this.$toast({
            content: this.$t('只能操作发票状态为待提交、已退回且同步状态未同步的数据！'),
            type: 'warning'
          })
          return
        }
        this.handleEdit(selectedRecords)
      }
    },
    handleSubmit(selectedRecords) {
      const invoiceIdList = []
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        if (item.status !== 0 && item.status !== 4) {
          this.$toast({ content: this.$t('请选择状态为待提交或已退回的发票'), type: 'warning' })
          return
        }
        invoiceIdList.push(item.id)
      }
      this.$API.invoiceCollaboration.submitInvoiceSup({ invoiceIdList }).then((res) => {
        const { code } = res
        if (code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          // 刷新当前 Grid
          this.refreshColumns()
        }
      })
    },
    handleAdd() {
      this.$dialog({
        modal: () => import('./components/addDialog/index.vue'),
        data: {
          title: this.$t('新增')
        },
        success: () => {
          this.refreshColumns()
        }
      })
    },
    handleEdit(selectedRecords) {
      // 根据id 获取单据数据
      this.$dialog({
        modal: () => import('./components/addDialog/index.vue'),
        data: {
          title: this.$t('新增'),
          selectedRecords
        },
        success: () => {
          this.refreshColumns()
        }
      })
    },
    // CellTool
    handleClickCellTool(args) {
      const { data, tool } = args
      if (tool.id === 'SendBack' || tool.id === 'SendBack1') {
        // 退回
        this.$refs.sendBackDialog.dialogInit({
          title: this.$t('确定退回'),
          selectData: data
        })
      } else if (tool.id === 'ConfirmInvoice') {
        // 采购确认
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确定采购确认选中的数据？')
          },
          success: () => {
            const params = {
              id: data.id,
              pass: true
              // rejectReason: "",
            }
            // 采方采购确认
            this.postCustomerReconciliationInvoiceV2purchaserConfirm(params)
          }
        })
      } else if (tool.id === 'ConfirmInvoiceFinance') {
        // 财务确认
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确定财务确认选中的数据？')
          },
          success: () => {
            const params = {
              id: data.id,
              pass: true
              // rejectReason: "",
            }
            // 采方财务确认
            this.postCustomerReconciliationInvoiceV2FinanceConfirm(params)
          }
        })
      }
    },
    // 对账单外部接口-对账单推送第三方共享财务
    postCustomerReconciliationInvoiceV2PushSharedFinance(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postCustomerReconciliationInvoiceV2PushSharedFinance(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 发票推送预制发票
    postPushPreMakeInvoice(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postPushPreMadInvoice(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方采购确认
    postCustomerReconciliationInvoiceV2purchaserConfirm(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postCustomerReconciliationInvoiceV2purchaserConfirm(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方财务确认
    postCustomerReconciliationInvoiceV2FinanceConfirm(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postCustomerReconciliationInvoiceV2FinanceConfirm(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 确定退回 弹框 调用api完成
    sendBackDialogConfirm() {
      // 刷新当前 Grid
      this.refreshColumns()
    },
    // 验证 发票、同步 状态是否统一
    verifyInvoiceStatus(args) {
      const { data, checkStatus, checkSyncStatus } = args
      let valid = true
      let invoiceStatus = null
      let syncStatus = null
      data.forEach((item, index) => {
        invoiceStatus = item.status // 发票状态
        syncStatus = item.syncStatus // 同步状态

        if (checkStatus && checkSyncStatus) {
          // 校验 发票状态、同步状态
          if (
            item &&
            data[index - 1] &&
            (item.status !== data[index - 1].status ||
              item.syncStatus !== data[index - 1].syncStatus)
          ) {
            valid = false
          }
        }
      })

      return { valid, invoiceStatus, syncStatus }
    },
    // CellTitle
    handleClickCellTitle(args) {
      const { field, data, fieldValue } = args
      if (field === 'reconciliationHeaderCodes') {
        // 点击 关联对账单号
        if (!fieldValue || fieldValue === '0') {
          return
        }
        this.handleReconciliationHeaderCodes({
          data,
          fieldValue
        })
      } else if (field === 'attachementCount') {
        // 点击 发票附件 数量
        this.handleAttachementCount({ data })
      } else if (field === 'reconciliationItemCount') {
        // 点击 关联明细 数量
        this.handleReconciliationItemCountClick(data)
      } else if (field === 'operation') {
        // 点击 查看日志
        this.handleClickOption(data)
      }
    },
    // 点击 关联对账单号
    handleReconciliationHeaderCodes(args) {
      // const { data } = args
      // // 根据id 获取单据数据
      // const params = {
      //   page: { current: 1, size: 10 },
      //   recoCodes: data.reconciliationHeaderCodes
      // }
      // this.apiStartLoading()
      // // 获取单据信息 租户级-发票协同-采方主单-对账信息
      // this.$API.invoiceCollaboration
      //   .queryPurchaseDetailList(params)
      //   .then((res) => {
      //     this.apiEndLoading()
      //     const reconciliationHeaderInfo = res?.data?.records[0] || {}
      //     // 从 可开票单据 跳转 单据详情页
      //     this.goToInvoiceDetail({
      //       headerInfo: reconciliationHeaderInfo,
      //       entryType: ConstantType.look,
      //       status: data.status,
      //       id: data.id
      //     })
      //   })
      //   .catch(() => {
      //     this.apiEndLoading()
      //   })
      const { data } = args
      // 根据id 获取单据数据
      const params = {
        page: { current: 1, size: 10 },
        reconciliationCode: data.reconciliationHeaderCodes[0]
      }
      this.apiStartLoading()
      // 获取单据信息 租户级-发票协同-采方主单-对账信息
      this.$API.invoiceCollaboration
        .queryPurchaseDetailTV(params)
        .then((res) => {
          this.apiEndLoading()
          const reconciliationHeaderInfo = res?.data?.records[0] || {}
          // 从 可开票单据 跳转 单据详情页
          this.goToInvoiceDetail({
            headerInfo: reconciliationHeaderInfo,
            entryType: ConstantType.look,
            status: data.status,
            id: data.id
          })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 点击 发票附件 数量
    handleAttachementCount(args) {
      const { data } = args
      // 获取发票附件数据 后 显示 发票附件列表 弹框
      const params = {
        id: data.id
      }
      this.apiStartLoading()
      // 获取发票附件列表 查询附件列表
      this.$API.invoiceCollaboration
        .postCustomerReconciliationInvoiceV2FileList(params)
        .then((res) => {
          this.apiEndLoading()
          const fileData = res?.data || []
          // 显示附件查看弹框
          const dialogParams = {
            fileData: fileData,
            isView: true,
            title: this.$t('发票附件')
          }
          this.$refs.uploaderDialog.dialogInit(dialogParams)
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 点击查看日志
    handleClickOption(data) {
      console.log('查看日志', data)
      const params = {
        reconciliationCode: data.reconciliationHeaderCodes[0],
        reconciliationType: 5,
        tenantId: '10000'
      }
      this.$refs.viewLogsDialog.dialogInit({
        title: this.$t('操作日志'),
        selectData: params
      })
    },
    // 点击 关联明细 数量
    handleReconciliationItemCountClick(data) {
      const params = {
        businessTypeCode: data.reconciliationBusinessTypeCode,
        reconciliationTypeCode: data.reconciliationTypeCode
      }
      this.apiStartLoading()
      // 获取 明细行 表头 采购对账字段配置
      this.$API.reconciliationSettlement
        .postReconciliationConfigFieldQuery(params)
        .then((res) => {
          this.apiEndLoading()
          const fieldList = res?.data || []
          const fieldConfig = fieldList.find(
            (item) => item.code === TabCode.reconciliationField && item.checkStatus
          )
          const fields = fieldConfig?.fieldResponseList || []
          const columnData = formatTableColumnData({
            table: Table.statementDetails,
            data: fields
          })
          // 显示明细行列表弹框 查看关联 明细行、高低开
          this.$refs.billDetailsTableDialog.dialogInit({
            title: this.$t('关联明细行列表'),
            actionType: BillDetailsTableDialogActionType.view,
            dataItemAsyncConfig: {
              // 新增发票时，dataId传0、defaultRule里面传headerId即可；
              // 修改发票时，dataId传发票id，defaultRule里面传headerId，能查出来关联了发票的明细行以及未关联发票的明细行
              // 查看发票明细行时，dataId传发票id，在defaultRule里面再传invoiceId对应当前发票id，只查出来当前发票关联的明细行
              url: `${BASE_TENANT}/customer-reconciliation-invoice-v2/item-page-with-invoice`, // 发票协同-供方-查询对账明细
              condition: 'and',
              params: {
                dataId: data.id // 发票新增时传 0，编辑、查看时传 发票id
              },
              defaultRules: [
                {
                  field: 'invoiceId',
                  operator: 'equal',
                  value: data.id
                }
              ]
            },
            dataItemColumnData: columnData,
            // 高低开
            highLowInfoAsyncConfig: {
              url: `${BASE_TENANT}/reconciliationHighLow/queryByInvoiceId`,
              params: {
                id: data.id // 发票id
              },
              recordsPosition: 'data'
            }
          })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 跳转到 发票详情页面
    goToInvoiceDetail(data) {
      const { headerInfo, entryType, status, id } = data
      // 发票详情 页面参数
      const params = {
        reconciliationCode: headerInfo.reconciliationHeaderCodes, // 对账单号
        headerInfo, // 头部信息 行数据
        entryType // 页面类型
      }
      localStorage.setItem('summaryV2Data', JSON.stringify(params))
      // 跳转到 发票详情页面
      this.$router.push({
        name: 'invoice-detail-tv',
        query: { fromType: 'querySupplierTV', status, id }
      })
    },
    handleExport(idList) {
      const params = {
        ...this.searchFormModel,
        page: { current: 1, size: 9999 },
        idList
      }
      this.$store.commit('startLoading')
      this.$API.invoiceCollaboration.exportInvoiceSupplier(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleDelete(selectedRecords) {
      const idList = selectedRecords.map((i) => i.id)
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .deleteInvoiceSupplier({ idList })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    print(idList) {
      this.$API.invoiceCollaboration.printInvoiceSupplier({ idList: idList }).then((res) => {
        if (res?.data?.type === 'application/json') {
          const reader = new FileReader()
          reader.readAsText(res?.data, 'utf-8')
          reader.onload = () => {
            const readerRes = reader.result
            const resObj = JSON.parse(readerRes)
            Vue.prototype.$toast({
              content: resObj.msg,
              type: 'error'
            })
          }
          return
        }
        const content = res.data
        this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
        // this.pdfUrl = window.URL.createObjectURL(
        //   new Blob([content], { type: 'text/html;charset=utf-8' })
        // )
        // window.open(this.pdfUrl);
        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = this.pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
      }, 100)
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
