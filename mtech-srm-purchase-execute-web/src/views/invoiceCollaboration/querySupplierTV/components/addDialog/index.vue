<template>
  <mt-dialog
    ref="dialog"
    css-class="bule-bg"
    width="100%"
    height="80%"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <ScTable
        ref="xTable"
        :columns="columnsData"
        :table-data="tableData"
        :is-show-right-btn="false"
        show-overflow
        height="auto"
        header-align="left"
        align="left"
        style="padding-top: unset"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true
        }"
        show-footer
        :footer-method="this.footerMethod"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
      >
        <template slot="custom-tools">
          <!-- :icon="item.icon" -->
          <vxe-button
            v-for="item in Toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #companyCodeEdit="{ row }">
          <vxe-select
            v-model="row.companyCode"
            placeholder="请选择公司"
            :options="companyOptions"
            transfer
            filterable
            @change="(value) => companyChange(value, row)"
          ></vxe-select>
        </template>
        <template #factoryCodeEdit="{ row }">
          <vxe-select
            v-model="row.siteCode"
            placeholder="请选择工厂"
            :options="siteOptions"
            transfer
            filterable
            @change="(value) => factoryChange(value, row)"
          ></vxe-select>
        </template>
        <template #currencyCodeEdit="{ row }">
          <vxe-select
            v-model="row.currencyCode"
            placeholder="请选择币种"
            :options="currencyOptions"
            transfer
            filterable
            @change="(value) => currencyChange(value, row)"
          ></vxe-select>
        </template>
        <template #invoiceUntaxAmountEdit="{ row }">
          <vxe-input
            v-model="row.invoiceUntaxAmount"
            :placeholder="$t('请输入')"
            type="number"
            clearable
            :controls="false"
            @change="
              row.invoiceTaxedAmount = bigDecimal.add(row.invoiceUntaxAmount, row.invoiceTaxAmount)
            "
          />
        </template>
        <template #invoiceTaxAmountEdit="{ row }">
          <vxe-input
            v-model="row.invoiceTaxAmount"
            :placeholder="$t('请输入')"
            type="number"
            clearable
            :controls="false"
            @change="
              row.invoiceTaxedAmount = bigDecimal.add(row.invoiceUntaxAmount, row.invoiceTaxAmount)
            "
          />
        </template>
      </ScTable>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import debounce from 'lodash.debounce'
import { Toolbar, columnsData, NewRowData, reconciliationTypeList } from './config'
import bigDecimal from 'js-big-decimal'
import dayjs from 'dayjs'
export default {
  components: {
    ScTable
  },
  data() {
    return {
      bigDecimal,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: debounce(this.confirm, 400),
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      Toolbar,
      columnsData,
      siteOptions: [], // 工厂下拉选项
      companyOptions: [], // 公司下拉选项
      currencyOptions: [], // 币种下拉选项
      tableData: [],
      pageType: 'add'
    }
  },
  props: {
    // 所有需要传入的 props 均在 computed 下面做了拆分
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    selectedRecords: {
      handler(val) {
        if (val && val.length) {
          this.Toolbar = []
          this.pageType = 'edit'
          this.tableData = val.map((i) => {
            let arriveMonth = i.arriveMonth
            if (arriveMonth.length === 1) {
              arriveMonth = '0' + arriveMonth
            }
            return {
              ...i,
              arriveMonth
            }
          })
        }
      },
      immediate: true
    }
  },
  computed: {
    // 标题
    header() {
      return this.modalData.title
    },
    selectedRecords() {
      return this.modalData.selectedRecords
    }
  },
  mounted() {
    this.getCompany()
    this.getSite()
    this.getCurrency()
    this.show()
  },
  methods: {
    getCompany() {
      this.$API.masterData
        .getCompanyBySup({
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true,
          organizationIds: [],
          fuzzyParam: '',
          dataLimit: 9999
        })
        .then((res) => {
          res.data.forEach((item) => {
            item.label = `${item.orgCode} - ${item.orgName}` // 	客户名称
            item.value = item.orgCode // 	客户名称
          })
          this.companyOptions = res.data || []
        })
    },
    factoryChange(val, row) {
      const { value } = val
      this.siteOptions.forEach((e) => {
        if (e.siteCode === value || e.organizationCode === value) {
          row.factoryCode = e.organizationCode ? e.organizationCode : e.siteCode // code
          row.factoryName = e.organizationName ? e.organizationName : e.siteName // name
        }
      })
    },
    companyChange(val, row) {
      const { value } = val
      this.companyOptions.forEach((e) => {
        if (e.orgCode === value) {
          row.companyCode = e.orgCode // code
          row.companyName = e.orgName // name
        }
      })
    },
    currencyChange(val, row) {
      const { value } = val
      this.currencyOptions.forEach((e) => {
        if (e.currencyCode === value) {
          row.currencyCode = e.currencyCode // code
          row.currencyName = e.currencyName // name
        }
      })
    },
    // 主数据 币种列表
    getCurrency() {
      this.$API.masterData.getCurrencyByFilter({ fuzzyParam: '', dataLimit: 9999 }).then((res) => {
        const list = res?.data || []
        this.currencyOptions = list.map((i) => {
          return {
            ...i,
            label: `${i.currencyCode}-${i.currencyName}`,
            value: i.currencyCode
          }
        })
      })
    },
    // 主数据 工厂列表
    getSite() {
      this.$API.deliverySchedule.getFactoryInfo({ fuzzyParam: '', dataLimit: 9999 }).then((res) => {
        const list = res?.data || []
        this.siteOptions = list.map((i) => {
          return {
            ...i,
            label: `${i.siteCode}-${i.siteName}`,
            value: i.siteCode
          }
        })
      })
    },
    footerMethod({ columns, data }) {
      return [
        columns.map((column) => {
          if (
            ['invoiceUntaxAmount', 'invoiceTaxAmount', 'invoiceTaxedAmount'].includes(
              column.property
            )
          ) {
            return `${this.$t('总计：')}${this.sumNum(data, column.property)}`
          }
          return null
        })
      ]
    },
    sumNum(list, field) {
      let count = 0
      list.forEach((item) => {
        count = bigDecimal.add(count, Number(item[field]))
      })
      return count
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      if (code === 'Add') {
        this.tableData.push({
          ...NewRowData,
          id: 'add' + Math.random().toString(36).substring(3, 8),
          addId: 'add' + Math.random().toString(36).substring(3, 8)
        })
      } else if (code === 'Delete') {
        const selectedIds = selectedRecords.map((i) => i.id)
        const newDataSource = this.tableData.filter((i) => !selectedIds.includes(i.id))
        this.tableData = newDataSource
      } else if (code === 'Import') {
        this.handleImport()
      }
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.invoiceCollaboration.invoiceImportSup,
          downloadTemplateApi: this.$API.invoiceCollaboration.downloadInvoiceTemplateSup,
          paramsKey: 'excel'
        },
        success: (data) => {
          const importData = data.map((i) => {
            let arriveMonth = i.arriveMonth
            if (arriveMonth.length === 1) {
              arriveMonth = '0' + arriveMonth
            }
            return {
              ...i,
              id: 'add' + Math.random().toString(36).substring(3, 8),
              addId: 'add' + Math.random().toString(36).substring(3, 8),
              invoiceTime: dayjs(new Date(Number(i.invoiceTime))).format('YYYY-MM-DD'),
              arriveMonth
            }
          })
          this.tableData = [...this.tableData, ...importData]
        }
      })
    },
    show() {
      this.$refs['dialog'].ejsRef.show()
    },
    hide() {
      this.$refs['dialog'].ejsRef.hide()
    },
    validFunc(row, index) {
      const validField = [
        { field: 'companyCode', text: '公司代码' },
        // { field: 'factoryCode', text: '工厂代码' },
        { field: 'arriveYear', text: '到货年份' },
        { field: 'arriveMonth', text: '到货月份' },
        { field: 'reconciliationTypeCode', text: '标准/寄售' },
        { field: 'invoiceNum', text: '发票号' },
        { field: 'currencyCode', text: '币种' },
        { field: 'invoiceUntaxAmount', text: '不含税金额' },
        { field: 'invoiceTaxAmount', text: '税额' },
        { field: 'invoiceTaxedAmount', text: '含税金额' },
        { field: 'invoiceTime', text: '开票日期' },
        { field: 'salerTaxPayerIc', text: '销方纳税人识别码' },
        { field: 'salesGoodsListQty', text: '销货清单份数' }
      ]
      for (let i = 0; i < validField.length; i++) {
        const item = validField[i]
        if (!row[item.field] && row[item.field] !== 0) {
          this.$toast({ content: this.$t(`第${index + 1}行${item.text}不能为空`), type: 'warning' })
          return false
        }
      }
      return true
    },
    confirm() {
      const params = []
      for (let i = 0; i < this.tableData.length; i++) {
        const item = JSON.parse(JSON.stringify(this.tableData[i]))
        const valid = this.validFunc(item, i)
        if (!valid) {
          return false
        }
        if (item.errorInfo) {
          this.$toast({ content: this.$t(`第${i + 1}行${item.errorInfo}`), type: 'warning' })
          return false
        }
        if (item.addId) {
          delete item.id
        }
        if (this.pageType === 'edit') {
          delete item.reconciliationHeaderCodes
          delete item.reconciliationHeaderIds
        }
        params.push({
          ...item,
          reconciliationTypeName: reconciliationTypeList.filter(
            (itm) => itm.value === item.reconciliationTypeCode
          )[0]['label'],
          invoiceTime: new Date(`${item.invoiceTime} 00:00:00`).getTime()
        })
      }
      this.$API.invoiceCollaboration.batchSaveOrUpdateInvoiceSup(params).then((res) => {
        const { code } = res
        if (code === 200) {
          this.$emit('confirm-function')
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  height: 100%;
}
</style>
<style lang="scss">
.vxe-table--ignore-clear.vxe-select--panel.size--small.is--transfer {
  z-index: 3000 !important;
}
</style>
