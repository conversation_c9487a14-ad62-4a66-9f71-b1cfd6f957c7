import { i18n } from '@/main.js'
import {
  InvoiceStatusOptions,
  SyncStatusOptions,
  postingStatusOptions,
  Table,
  SourceTypeOptions,
  ReconciliationDetailsStatusOptions,
  AdvanceInvoicingOptions,
  FrozenStatusOptions,
  PrePayStatusOptions,
  ProvisionalEstimateStatusOptions1,
  ReconciliationDetailsTypeOptions,
  RealPriceStatusOptions,
  InOutTypeOptions
} from './constant'
import { ColumnComponent } from './columnComponent'
import { codeNameColumn } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'
import { ColumnComponent as Component } from './columnComponent'

// 格式化表格动态数据
export const formatTableColumnData = (args) => {
  const { data, table } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.code,
      headerText: col.name,
      width: '150'
    }
    if (table === Table.summary) {
      // 发票列表
      if (defaultCol.field === 'status') {
        // 开票状态
        defaultCol.width = '150'
        defaultCol.valueConverter = {
          type: 'map',
          map: InvoiceStatusOptions
        }
        defaultCol.cellTools = []
      } else if (defaultCol.field === 'reconciliationHeaderCodes') {
        // 关联单据号
        defaultCol.width = '200'
        defaultCol.template = ColumnComponent.listInfoColumn({
          dataKey: defaultCol.field,
          isAbleClick: true,
          isShowLength: false,
          delimiter: '；'
        })
      } else if (defaultCol.field === 'syncStatus') {
        // 同步状态 0:否 1:是 2:同步中
        defaultCol.width = '120'
        defaultCol.valueConverter = {
          type: 'map',
          map: SyncStatusOptions
        }
      } else if (defaultCol.field === 'postingStatus') {
        // 过账状态 0 :未过账 1 :已过账
        defaultCol.width = '120'
        defaultCol.valueConverter = {
          type: 'map',
          map: postingStatusOptions
        }
      } else if (defaultCol.field === 'codeRel') {
        // 外部关联单据号
        defaultCol.width = '200'
      } else if (defaultCol.field === 'reconciliationItemCount') {
        // 明细行
        defaultCol.valueConverter = {
          type: 'function',
          filter: (data) => {
            return `${data}` // 使 number 0 可以显示
          }
        }
        defaultCol.cellTools = [] // 使其可点击查看
      } else if (defaultCol.field === 'operation') {
        // 操作行
        defaultCol.valueConverter = {
          type: 'function',
          filter: () => {
            return `查看日志`
          }
        }
        defaultCol.cellTools = [] // 使其可点击查看
      } else if (defaultCol.field === 'attachementCount') {
        // 发票附件
        defaultCol.allowFiltering = false
        defaultCol.ignore = true
        defaultCol.valueConverter = {
          type: 'function',
          filter: (data) => {
            return `${data}` // 使 number 0 可以显示
          }
        }
        defaultCol.cellTools = [] // 使其可点击查看
      } else if (defaultCol.field === 'createTime') {
        // 创建时间
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (defaultCol.field === 'confirmTime') {
        // 采购确认时间
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (defaultCol.field === 'invoiceTime') {
        // 开票日期
        // defaultCol.template = ColumnComponent.timeDate({
        //   dataKey: defaultCol.field,
        //   hasTime: false
        // })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (defaultCol.field === 'reviewTime') {
        // 财务审核日期
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (defaultCol.field === 'companyCode') {
        // 公司
        // code-name 形式
        defaultCol.width = '300'
        defaultCol.template = codeNameColumn({
          firstKey: 'companyCode',
          secondKey: 'companyName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        // 主数据选择器
        defaultCol.searchOptions = {
          ...MasterDataSelect.businessCompany,
          placeholder: i18n.t('公司')
        }
      } else if (defaultCol.field === 'supplierCode') {
        // 供应商
        // code-name 形式
        defaultCol.width = '300'
        defaultCol.template = codeNameColumn({
          firstKey: 'supplierCode',
          secondKey: 'supplierName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        // 主数据选择器
        defaultCol.searchOptions = {
          ...MasterDataSelect.supplier,
          placeholder: i18n.t('供应商')
        }
      } else if (defaultCol.field === 'theHeaderTypeName') {
        // 关联单据类型 theHeaderTypeName FIXME: 暂时前端固定 对账单
        defaultCol.allowFiltering = false
        defaultCol.ignore = true
      }
    } else if (table === Table.statementDetails) {
      // 关联单据明细列表
      defaultCol.width = data.length > 10 ? '200' : 'auto'
      defaultCol.allowFiltering = false // 不允许过滤
      if (!col.checkStatus) {
        defaultCol.visible = false
        defaultCol.ignore = true
      } else if (defaultCol.field === 'receiveTime') {
        // 对账明细-收货时间
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
      } else if (defaultCol.field === 'fileBaseInfoList') {
        // 对账明细-文件信息
        defaultCol.template = ColumnComponent.fileBaseInfoList
      } else if (defaultCol.field === 'termsUntaxedUnitPrice') {
        // 对账明细-条件（未税）单价
        defaultCol.width = '200'
      } else if (defaultCol.field === 'sourceType') {
        // 来源类型（枚举） 0: 上游流入1:第三方接口
        defaultCol.valueConverter = {
          type: 'map',
          map: SourceTypeOptions
        }
      } else if (defaultCol.field === 'status') {
        // 单据状态 0:待对账 1:已创建对账单
        defaultCol.valueConverter = {
          type: 'map',
          map: ReconciliationDetailsStatusOptions
        }
      } else if (defaultCol.field === 'syncStatus') {
        // 同步状态 0:否 1:是 2:同步中
        defaultCol.valueConverter = {
          type: 'map',
          map: SyncStatusOptions
        }
      } else if (defaultCol.field === 'advanceInvoicing') {
        // 提前开票 0:否 1:是
        defaultCol.valueConverter = {
          type: 'map',
          map: AdvanceInvoicingOptions
        }
      } else if (defaultCol.field === 'frozenStatus') {
        // 冻结标记 0:否 1:是
        defaultCol.valueConverter = {
          type: 'map',
          map: FrozenStatusOptions
        }
      } else if (defaultCol.field === 'prePayStatus') {
        // 是否预付 0-否；1-是
        defaultCol.valueConverter = {
          type: 'map',
          map: PrePayStatusOptions
        }
      } else if (defaultCol.field === 'itemVoucherDate') {
        // 物料凭证日期
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
      } else if (defaultCol.field === 'provisionalEstimateStatus') {
        // 是否暂估价 0-否；1-是 改成是否执行价格
        defaultCol.valueConverter = {
          type: 'map',
          map: ProvisionalEstimateStatusOptions1
        }
      } else if (defaultCol.field === 'createTime') {
        // 创建时间
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
      } else if (defaultCol.field === 'updateTime') {
        // 最后修改时间
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
      } else if (defaultCol.field === 'type') {
        // 待对账类型:0-采购待对账；1-销售待对账
        defaultCol.valueConverter = {
          type: 'map',
          map: ReconciliationDetailsTypeOptions
        }
      } else if (defaultCol.field === 'realPriceStatus') {
        // 正式价标识 0-无正式价；1-有正式价
        defaultCol.valueConverter = {
          type: 'map',
          map: RealPriceStatusOptions
        }
      } else if (defaultCol.field === 'inOutType') {
        // 出入库类型 0-采购入库；1-采购出库；2-销售出库；3-销售退回
        defaultCol.valueConverter = {
          type: 'map',
          map: InOutTypeOptions
        }
      }
    }
    colData.push(defaultCol)
  })

  return colData
}

// 表格数据序列化
export const serializeList = (list) => {
  if (list?.length > 0) {
    list.forEach((item) => {
      // 关联单据号 字符串转数组
      item.reconciliationHeaderCodes = item.reconciliationHeaderCodes.split(',')
      // 关联单据id  转换：供方单据ID||采方单据ID,供方单据ID||采方单据ID => [{supplierHeaderId: 供方单据ID, buyerHeaderId: 采方单据ID},{...}]
      const headerIdsStr = item.reconciliationHeaderIds
      item.reconciliationHeaderIds = headerIdsStr.split(',').map((item) => {
        const billListItem = item.split('||') // ['供方单据ID', '采方单据ID']
        return {
          supplierHeaderId: billListItem[0],
          buyerHeaderId: billListItem[1]
        }
      })
      item.invoiceType = item.invoiceTypeCode // 发票类型
      // item.invoiceTime = new Date(Number(item.invoiceTime)) // 开票时间
      item.theHeaderTypeName = i18n.t('对账单') // theHeaderTypeName FIXME: 暂时前端固定 对账单
    })
  }

  return list
}
// 格式化 操作日志 表格动态数据
export const formatLogTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150'
    }
    if (col.fieldCode === 'createTime') {
      // 创建时间
      defaultCol.template = Component.timeDate({
        dataKey: col.fieldCode,
        hasTime: true
      })
    }
    colData.push(defaultCol)
  })

  return colData
}
