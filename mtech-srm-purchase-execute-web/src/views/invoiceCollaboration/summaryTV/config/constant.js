import { i18n } from '@/main.js'

export const ConstantType = {
  Edit: '1', // 编辑
  Look: '2' // 查看
}

export const Table = {
  summary: 'summary', // 发票列表
  statementDetails: 'statementDetails' // 关联单据明细列表
}
// tab code
export const TabCode = {
  highLowInfo: 'highLowInfo', // 高低开信息
  operationLog: 'operationLog', // 操作日志
  reconciliationFile: 'reconciliationFile', // 相关附件
  reconciliationField: 'reconciliationField', // 对账明细
  invoiceList: 'invoiceList', // 发票清单
  purHeaderFile: 'purHeaderFile', // 采方-整单附件
  supHeaderFile: 'supHeaderFile' // 供方-整单附件
}

// 发票状态 0-待提交、1-待审核、2-待财务审核、3-完成、4-已退回、5-已删除
export const InvoiceStatus = {
  pendingSubmission: 0, // 待提交
  pendingReview: 1, // 待审核
  pendingFinancialReview: 2, // 待财务审核
  complete: 3, // 完成
  returned: 4, // 已退回
  deleted: 5 // 已删除
}
// 发票状态
export const InvoiceStatusConst = {
  [InvoiceStatus.pendingSubmission]: i18n.t('待提交'),
  [InvoiceStatus.pendingReview]: i18n.t('待确认'), // 待审核
  [InvoiceStatus.pendingFinancialReview]: i18n.t('采购已确认'), // 待财务审核
  [InvoiceStatus.complete]: i18n.t('已完成'), // 完成
  [InvoiceStatus.returned]: i18n.t('已退回'),
  [InvoiceStatus.deleted]: i18n.t('已删除')
}
// 发票状态 对应的 css class
export const InvoiceStatusCssClass = {
  [InvoiceStatus.pendingSubmission]: 'col-active',
  [InvoiceStatus.pendingReview]: 'col-active',
  [InvoiceStatus.pendingFinancialReview]: 'col-normal',
  [InvoiceStatus.complete]: 'col-normal',
  [InvoiceStatus.returned]: 'col-published',
  [InvoiceStatus.deleted]: 'col-inactive'
}
// 发票状态 对应的 Options
export const InvoiceStatusOptions = [
  {
    // 待提交
    value: InvoiceStatus.pendingSubmission,
    text: InvoiceStatusConst[InvoiceStatus.pendingSubmission],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.pendingSubmission]
  },
  {
    // 待审核
    value: InvoiceStatus.pendingReview,
    text: InvoiceStatusConst[InvoiceStatus.pendingReview],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.pendingReview]
  },
  {
    // 待财务审核
    value: InvoiceStatus.pendingFinancialReview,
    text: InvoiceStatusConst[InvoiceStatus.pendingFinancialReview],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.pendingFinancialReview]
  },
  {
    // 完成
    value: InvoiceStatus.complete,
    text: InvoiceStatusConst[InvoiceStatus.complete],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.complete]
  },
  {
    // 已退回
    value: InvoiceStatus.returned,
    text: InvoiceStatusConst[InvoiceStatus.returned],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.returned]
  },
  {
    // 已删除
    value: InvoiceStatus.deleted,
    text: InvoiceStatusConst[InvoiceStatus.deleted],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.deleted]
  }
]

// 同步状态 0:否 1:是 2:同步中
export const SyncStatus = {
  synced: 1, // 是
  notSynced: 0, // 否
  synchronizing: 2 // 同步中
}
// 同步状态 对应的 Options
export const SyncStatusOptions = [
  {
    value: SyncStatus.notSynced,
    text: i18n.t('未同步'),
    cssClass: 'col-notSynced'
  },
  {
    value: SyncStatus.synced,
    text: i18n.t('已同步'),
    cssClass: 'col-synced'
  },
  {
    value: SyncStatus.synchronizing,
    text: i18n.t('同步中'),
    cssClass: 'col-synced'
  }
]

// 过账状态 0:未过账 1：已过账
export const PostingStatus = {
  posting: 0, // 未过账
  posted: 1 // 已过帐
}
// 过账状态
export const postingStatusOptions = [
  {
    value: PostingStatus.posting,
    text: i18n.t('未过账'),
    cssClass: ''
  },
  {
    value: PostingStatus.posted,
    text: i18n.t('已过帐'),
    cssClass: ''
  }
]

export const Toolbar = [
  {
    name: i18n.t('推送SAP系统'),
    icon: 'icon_table_pushSap',
    code: 'PushPreMakeInvoice'
  },
  {
    name: i18n.t('导出'),
    icon: 'icon_table_pushSap',
    code: 'InvoiceExport'
  }
  // {
  //   title: i18n.t('打印'),
  //   icon: 'icon_list_print',
  //   id: 'Print'
  // },
  // {
  //   id: 'InvoiceExport',
  //   icon: 'icon_solid_export',
  //   title: i18n.t('导出')
  // }
]

// 表格列数据
export const ColumnData = [
  {
    type: 'checkbox',
    width: 50,
    ignore: true
    // fixed: 'left'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    showOverflow: true
  },
  {
    field: 'reconciliationHeaderCodes',
    title: i18n.t('关联单据号'),
    minWidth: 140,
    slots: {
      // 使用插槽模板渲染
      default: 'reconCodeDefault'
    }
  },
  {
    field: 'status',
    title: i18n.t('发票状态'),
    formatter: ({ cellValue }) => {
      let item = InvoiceStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    },
    showOverflow: true,
    minWidth: 160,
    slots: {
      // 使用插槽模板渲染
      default: 'statusDefault'
    }
  },
  {
    field: 'theHeaderTypeName', // theHeaderTypeName FIXME: 暂时前端固定 对账单
    title: i18n.t('关联单据类型'),
    minWidth: 120
  },
  {
    field: 'syncStatus',
    title: i18n.t('同步状态'),
    type: 'html',
    formatter: ({ cellValue }) => {
      let item = SyncStatusOptions.find((item) => item.value === cellValue)
      const color = cellValue === 1 ? 'color: #6386c1' : 'color: #9baac1'
      return `<div style='${color}'><span style='
      display: inline-block;
      height: 8px;
      width: 8px;
      margin-right: 8px;
      height: 8px;
      background-${color};
      border-radius: 50%;'></span>${item ? item.text : ''}</div>`
    },
    showOverflow: true
  },
  {
    field: 'invoiceNum',
    title: i18n.t('发票号'),
    minWidth: 120,
    showOverflow: true
  },
  {
    field: 'companyCode',
    title: i18n.t('公司'),
    showOverflow: true
  },
  {
    field: 'factoryCode',
    title: i18n.t('工厂'),
    showOverflow: true
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    showOverflow: true
  },
  {
    field: 'invoiceUntaxAmount',
    title: i18n.t('发票未税金额'),
    minWidth: 140,
    align: 'right',
    showOverflow: true
  },
  {
    field: 'invoiceTaxAmount',
    title: i18n.t('发票税额'),
    minWidth: 140,
    align: 'right',
    showOverflow: true
  },
  {
    field: 'invoiceTaxedAmount',
    title: i18n.t('发票含税金额'),
    minWidth: 140,
    align: 'right',
    showOverflow: true
  },
  {
    field: 'reconciliationUntaxAmount',
    title: i18n.t('汇总未税金额'),
    minWidth: 140,
    align: 'right',
    showOverflow: true
  },
  {
    field: 'reconciliationTaxAmount',
    title: i18n.t('汇总税额'),
    minWidth: 140,
    align: 'right',
    showOverflow: true
  },
  {
    field: 'reconciliationTaxedAmount',
    title: i18n.t('汇总含税金额'),
    minWidth: 140,
    align: 'right',
    showOverflow: true
  },
  {
    field: 'invoiceTime',
    title: i18n.t('开票日期'),
    showOverflow: true
  },
  {
    field: 'currencyName',
    title: i18n.t('币种'),
    minWidth: 130,
    showOverflow: true,
    formatter: ({ cellValue, row }) => {
      return `${row.currencyCode} - ${cellValue}`
    }
  },
  {
    field: 'reconciliationTypeName',
    title: i18n.t('对账类型'),
    showOverflow: true
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 140,
    showOverflow: true
  },
  {
    field: 'invoiceCode',
    title: i18n.t('发票代码'),
    showOverflow: true
  },
  {
    field: 'checkCode',
    title: i18n.t('后六位校验码'),
    minWidth: 140,
    showOverflow: true
  },
  {
    field: 'customerRemark',
    title: i18n.t('采方备注'),
    showOverflow: true
  },
  {
    field: 'supplierRemark',
    title: i18n.t('供方备注'),
    showOverflow: true
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    showOverflow: true
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    width: 150
  },
  {
    field: 'confirmTime',
    title: i18n.t('采购审核时间'),
    width: 150
  }
]

// 来源类型（枚举） 0: 上游流入1:第三方接口
export const SourceTypeOptions = [
  {
    value: 0,
    text: i18n.t('上游流入'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('第三方接口'),
    cssClass: ''
  }
]

// 单据状态 0:待对账 1:已创建对账单
export const ReconciliationDetailsStatusOptions = [
  {
    value: 0,
    text: i18n.t('待对账'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('已创建对账单'),
    cssClass: ''
  }
]

// 提前开票 0:否 1:是
export const AdvanceInvoicingOptions = [
  {
    value: 0,
    text: i18n.t('否'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('是'),
    cssClass: ''
  }
]

// 冻结标记 0:否 1:是
export const FrozenStatusOptions = [
  {
    value: 0,
    text: i18n.t('否'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('是'),
    cssClass: ''
  }
]

// 是否预付 0-否；1-是
export const PrePayStatusOptions = [
  {
    value: 0,
    text: i18n.t('否'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('是'),
    cssClass: ''
  }
]

// 是否暂估价 0-否；1-是
export const ProvisionalEstimateStatusOptions = [
  {
    value: 0,
    text: i18n.t('否'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('是'),
    cssClass: ''
  }
]
// 是否执行价 0-是；1-否
export const ProvisionalEstimateStatusOptions1 = [
  {
    value: 0,
    text: i18n.t('是'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('否'),
    cssClass: ''
  }
]
// 待对账类型:0-采购待对账；1-销售待对账
export const ReconciliationDetailsTypeOptions = [
  {
    value: 0,
    text: i18n.t('采购待对账'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('销售待对账'),
    cssClass: ''
  }
]

// 正式价标识 0-无正式价；1-有正式价
export const RealPriceStatusOptions = [
  {
    value: 0,
    text: i18n.t('无正式价'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('有正式价'),
    cssClass: ''
  }
]

// 出入库类型 0-采购入库；1-采购出库；2-销售出库；3-销售退回
export const InOutTypeOptions = [
  {
    value: 0,
    text: i18n.t('采购入库'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('采购出库'),
    cssClass: ''
  },
  {
    value: 2,
    text: i18n.t('销售出库'),
    cssClass: ''
  },
  {
    value: 3,
    text: i18n.t('销售退回'),
    cssClass: ''
  }
]
