<template>
  <!-- 发票确认-采方 -->
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      :is-grid-display="true"
      @reset="handleSearchReset"
      @search="handleCustomSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="reconciliationHeaderCodes" :label="$t('关联单据号')" label-style="top">
          <mt-input
            v-model="searchFormModel.reconciliationHeaderCodes"
            :placeholder="$t('关联单据号')"
            :show-clear-button="true"
            @change="reconciliationHeaderCodesInput"
          />
        </mt-form-item>
        <mt-form-item prop="companyCodes" :label="$t('公司')" label-style="top">
          <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.companyCodes"
            url="/masterDataManagement/tenant/organization/specified-level-paged-query"
            :params="{
              organizationLevelCodes: ['ORG02', 'ORG01'],
              orgType: 'ORG001PRO',
              includeItself: true
            }"
            multiple
            :placeholder="$t('请选择公司')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="supplierCodes" :label="$t('供应商')" label-style="top">
          <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.supplierCodes"
            url="/masterDataManagement/tenant/supplier/paged-query"
            multiple
            :placeholder="$t('请选择供应商')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="factoryCodes" :label="$t('工厂')" class="top">
          <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.factoryCodes"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择工厂')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商代码')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierCode"
            :placeholder="$t('供应商代码')"
            :show-clear-button="true"
            @change="supplierCodeChange"
          />
        </mt-form-item>
        <mt-form-item prop="invoiceNum" :label="$t('发票号')" label-style="top">
          <mt-input
            v-model="searchFormModel.invoiceNum"
            :placeholder="$t('发票号码')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="currencyCode" :label="$t('币种')" label-style="top">
          <mt-select
            :allow-filtering="true"
            v-model="searchFormModel.currencyCode"
            :data-source="currencyOptions"
            :fields="{ text: 'label', value: 'value' }"
            :show-clear-button="true"
            :filtering="getCurrency"
            :placeholder="$t('请选择币种')"
          />
        </mt-form-item>
        <mt-form-item prop="invoiceTime" :label="$t('开票日期')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.invoiceTime"
            :placeholder="$t('请选择')"
            @change="(e) => dateTimeChange(e, 'invoiceTime')"
          />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('发票状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.status"
            :data-source="statusOptions"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="syncStatus" :label="$t('同步状态')" label-style="top">
          <mt-select
            style="flex: 1"
            v-model="searchFormModel.syncStatus"
            :data-source="[
              { value: 0, text: $t('未同步') },
              { value: 1, text: $t('已同步') }
            ]"
            :allow-filtering="true"
            :show-clear-button="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <ScTable
        ref="xTable"
        :row-config="{ height: 46 }"
        :columns="columnsData"
        :table-data="tableData"
        :is-show-right-btn="false"
        show-overflow
        height="auto"
        header-align="left"
        align="left"
        style="padding-top: unset"
        show-footer
        :footer-method="this.footerMethod"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
      >
        <template slot="custom-tools">
          <!-- :icon="item.icon" -->
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #reconCodeDefault="{ row }">
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <div class="input-search-content">
                <div style="padding: 12px 0">
                  <div
                    :class="['able-click-field']"
                    v-for="(item, index) in row['reconciliationHeaderCodes']"
                    :key="index"
                    @click.stop="
                      handleClickCellTitle({
                        field: 'reconciliationHeaderCodes',
                        data: row,
                        fieldValue: row.reconciliationHeaderCodes
                      })
                    "
                  >
                    {{ item }}{{ row['reconciliationHeaderCodes'].length != index + 1 ? ';' : '' }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <template #statusDefault="{ row }">
          <div style="color: #2783fe">
            <div style="cursor: pointer">
              <span
                :class="InvoiceStatusOptions.filter((i) => i.value === row.status)[0]['cssClass']"
                >{{ InvoiceStatusOptions.filter((i) => i.value === row.status)[0]['text'] }}</span
              >
            </div>
            <template v-for="item in statusCellTool">
              <div
                v-if="item.visibleCondition(row)"
                style="cursor: pointer; display: inline-block; margin-right: 5px"
                v-permission="item.permission"
                :key="item.id"
              >
                <mt-icon :name="item.icon" />
                <span @click="handleClickCellTool({ tool: { id: item.id }, data: row })">{{
                  item.title
                }}</span>
              </div>
            </template>
          </div>
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="forecastPageSettings"
      :total-pages="forecastPageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
    <!-- 附件弹窗 -->
    <uploader-dialog ref="uploaderDialog"></uploader-dialog>
    <!-- 发票关联单据明细行弹框 -->
    <bill-details-table-dialog ref="billDetailsTableDialog"></bill-details-table-dialog>
    <!-- 发票回退弹框 -->
    <send-back-dialog ref="sendBackDialog" @confirm="sendBackDialogConfirm"></send-back-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import { i18n } from '@/main.js'
import {
  ColumnData,
  InvoiceStatus,
  Toolbar,
  SyncStatus,
  Table,
  TabCode,
  InvoiceStatusOptions
} from './config/constant'
import { ConstantType } from '../detailV2/config/constant'
import { formatTableColumnData } from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'
import UploaderDialog from '@/components/Upload/uploaderDialog'
import BillDetailsTableDialog from '@/components/businessComponents/billDetailsTableDialog'
import { BillDetailsTableDialogActionType } from '@/components/businessComponents/billDetailsTableDialog/config/constant'
import SendBackDialog from './components/sendBackDialog.vue'
import dayjs from 'dayjs'
import * as UTILS from '@/utils/utils'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import bigDecimal from 'js-big-decimal'

export default {
  components: {
    UploaderDialog,
    BillDetailsTableDialog,
    SendBackDialog,
    ScTable,
    CollapseSearch
  },
  data() {
    return {
      InvoiceStatusOptions,
      tableData: [],
      columnsData: ColumnData,
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 50, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      forecastPageCurrent: 1,
      searchFormModel: {
        syncStatus: 0
      },
      currencyOptions: [], //订单币种
      statusOptions: [
        { text: i18n.t('待提交'), value: 0 },
        { text: i18n.t('待确认'), value: 1 },
        { text: i18n.t('采购已确认'), value: 2 },
        { text: i18n.t('已完成'), value: 3 },
        { text: i18n.t('已退回'), value: 4 }
      ],
      toolbar: Toolbar,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      statusCellTool: [
        {
          id: 'ConfirmInvoice',
          icon: 'icon_list_InvoiceConfirmation',
          title: i18n.t('采购确认'),
          visibleCondition: (data) => data.status == InvoiceStatus.pendingReview // 待确认
        },
        {
          id: 'SendBack',
          icon: 'icon_list_recall',
          title: i18n.t('采购退回'),
          visibleCondition: (data) => data.status == InvoiceStatus.pendingReview // 待确认 || 采购已确认 待财务审核
        }
      ]
    }
  },
  mounted() {
    this.getCurrency()
    this.handleCustomSearch()
  },
  methods: {
    footerMethod({ columns, data }) {
      return [
        columns.map((column) => {
          if (
            [
              'reconciliationUntaxAmount',
              'reconciliationTaxAmount',
              'reconciliationTaxedAmount'
            ].includes(column.property)
          ) {
            return `${this.$t('总计：')}${this.sumNum(data, column.property)}`
          }
          return null
        })
      ]
    },
    sumNum(list, field) {
      let count = 0
      list.forEach((item) => {
        count = bigDecimal.add(count, Number(item[field]))
      })
      return count
    },
    // 采方-获取采方信息列表
    handleCustomSearch() {
      const params = {
        ...this.searchFormModel,
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        }
      }
      params.supplierCodes =
        params.supplierCodes?.length === 0
          ? params.supplierCode?.length !== 0
            ? params.supplierCode?.split(' ')
            : []
          : params.supplierCodes
      this.apiStartLoading()
      this.tableData = []
      this.$API.invoiceCollaboration
        .queryPurchaseDetailList(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const total = res?.data?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.totalRecordsCount = Number(total)
            this.tableData =
              res?.data?.records?.map((i) => {
                const item = { ...i }
                item.reconciliationHeaderCodes = item.reconciliationHeaderCodes.split(',')
                // 关联单据id  转换：供方单据ID||采方单据ID,供方单据ID||采方单据ID => [{supplierHeaderId: 供方单据ID, buyerHeaderId: 采方单据ID},{...}]
                // const headerIdsStr = item.reconciliationHeaderIds
                // item.reconciliationHeaderIds = headerIdsStr.split(',').map((item) => {
                //   const billListItem = item.split('||') // ['供方单据ID', '采方单据ID']
                //   return {
                //     supplierHeaderId: billListItem[0],
                //     buyerHeaderId: billListItem[1]
                //   }
                // })
                item.invoiceType = item.invoiceTypeCode // 发票类型
                // item.invoiceTime = new Date(Number(item.invoiceTime)) // 开票时间
                item.theHeaderTypeName = i18n.t('对账单') // theHeaderTypeName FIXME: 暂时前端固定 对账单
                return item
              }) || [] // 表格数据
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.handleCustomSearch()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    getCurrency(val, currencyCode) {
      let params = { fuzzyParam: val?.text || '' }
      this.$API.masterData.getCurrencyByFilter(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.currencyCode}-${item.currencyName}`
          item.text = item.currencyName
          item.value = item.currencyCode
        })
        this.currencyOptions = list
        if (currencyCode) {
          this.topInfo.currency = currencyCode
        }
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.currencyOptions)
          })
        }
      })
    },
    reconciliationHeaderCodesInput() {
      this.searchFormModel['recoCodes'] = []
      if (this.searchFormModel.reconciliationHeaderCodes) {
        this.searchFormModel['recoCodes'] = [this.searchFormModel.reconciliationHeaderCodes]
      }
    },
    supplierCodeChange() {
      this.searchFormModel['supplierCodes'] = []
    },
    dateTimeChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'Start'] = this.getUnix(dayjs(e.startDate).format('YYYY-MM-DD'))
        this.searchFormModel[flag + 'End'] = this.getUnix(dayjs(e.endDate).format('YYYY-MM-DD'))
      } else {
        this.searchFormModel[flag + 'Start'] = null
        this.searchFormModel[flag + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleSearchReset() {
      for (let key in this.searchFormModel) {
        this.searchFormModel[key] = null
      }
      this.searchFormModel.syncStatus = 0
    },
    handleClickToolBar(e) {
      const { code, $grid } = e
      const selectedRecords = $grid.getCheckboxRecords()
      const commonToolbar = [
        'Add',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal',
        'InvoiceExport'
      ]
      if (selectedRecords.length == 0 && !commonToolbar.includes(code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.reconciliationHeaderIds)
      })

      if (code === 'PushReimbursement') {
        // 推送报销
        this.handlePushReimbursement({ selectedRecords, idList })
      } else if (code === 'PushPreMakeInvoice') {
        // 推送预制发票
        this.handlePushPreMakeInvoice({ selectedRecords, idList })
      } else if (code === 'Print') {
        this.print(idList)
      } else if (code === 'InvoiceExport') {
        this.handleExport(idList)
      }
    },
    handleExport(idList) {
      const params = {
        ...this.searchFormModel,
        page: { current: 1, size: 9999 },
        idList
      }
      this.$store.commit('startLoading')
      this.$API.invoiceCollaboration.exportInvoice(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    print(idList) {
      this.$API.invoiceCollaboration.printInvoiceSupplier({ idList: idList }).then((res) => {
        if (res?.data?.type === 'application/json') {
          const reader = new FileReader()
          reader.readAsText(res?.data, 'utf-8')
          reader.onload = () => {
            const readerRes = reader.result
            const resObj = JSON.parse(readerRes)
            Vue.prototype.$toast({
              content: resObj.msg,
              type: 'error'
            })
          }
          return
        }
        const content = res.data
        this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
        // this.pdfUrl = window.URL.createObjectURL(
        //   new Blob([content], { type: 'text/html;charset=utf-8' })
        // )
        // window.open(this.pdfUrl);
        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = this.pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
      }, 100)
    },
    // 推送报销
    handlePushReimbursement(args) {
      const { selectedRecords, idList } = args
      const { valid, invoiceStatus, syncStatus } = this.verifyInvoiceStatus({
        data: selectedRecords,
        checkStatus: true,
        checkSyncStatus: true
      })
      if (!valid || invoiceStatus != InvoiceStatus.complete || syncStatus != SyncStatus.notSynced) {
        this.$toast({
          content: this.$t('请选择财务已确认且未同步的数据'),
          type: 'warning'
        })
      } else {
        // 状态: 财务已确认 && 同步状态: 未同步
        this.postCustomerReconciliationInvoiceV2PushSharedFinance(idList)
      }
    },
    // 推送预制发票
    handlePushPreMakeInvoice(args) {
      const { selectedRecords } = args
      const { valid, invoiceStatus, syncStatus } = this.verifyInvoiceStatus({
        data: selectedRecords,
        checkStatus: true,
        checkSyncStatus: true
      })
      if (
        !valid ||
        invoiceStatus != InvoiceStatus.pendingFinancialReview ||
        syncStatus != SyncStatus.notSynced
      ) {
        this.$toast({
          content: this.$t('请选择采购已确认且未同步的数据'),
          type: 'warning'
        })
      } else {
        // 状态: 财务已确认 && 推送预制发票状态: 未推送
        this.postPushPreMakeInvoice(args)
      }
    },
    // CellTool
    handleClickCellTool(args) {
      const { data, tool } = args
      if (tool.id === 'SendBack' || tool.id === 'SendBack1') {
        // 外发对账采购退回
        if (['TV_EXTERNAL', 'VN_DAILY'].includes(data.reconciliationTypeCode)) {
          this.$dialog({
            data: {
              title: this.$t('退回')
            },
            modal: () => import('./components/RejectDialog.vue'),
            success: async (rejectReason) => {
              let params = {
                id: data?.id,
                pass: false,
                rejectReason,
                reconciliationType: data.reconciliationTypeCode === 'TV_EXTERNAL' ? 1 : 2
              }
              this.apiStartLoading()
              let api = this.$API.invoiceCollaboration.confirmExternalInvoiceApi
              const res = await api(params).finally(() => this.apiEndLoading())
              if (res.code === 200) {
                this.$toast({ content: this.$t('退回成功'), type: 'success' })
                this.refreshColumns()
              }
            }
          })
          return
        }
        // 退回
        this.$refs.sendBackDialog.dialogInit({
          title: this.$t('确定退回'),
          selectData: data
        })
      } else if (tool.id === 'ConfirmInvoice') {
        // 外发对账采购确认
        if (['TV_EXTERNAL', 'VN_DAILY'].includes(data.reconciliationTypeCode)) {
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('是否确认?')
            },
            success: async () => {
              let params = {
                id: data?.id,
                pass: true,
                reconciliationType: data.reconciliationTypeCode === 'TV_EXTERNAL' ? 1 : 2
              }
              this.apiStartLoading()
              let api = this.$API.invoiceCollaboration.confirmExternalInvoiceApi
              const res = await api(params).finally(() => this.apiEndLoading())
              if (res.code === 200) {
                this.$toast({ content: this.$t('确认成功'), type: 'success' })
                this.refreshColumns()
              }
            }
          })
          return
        }
        // 采购确认
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确定采购确认选中的数据？')
          },
          success: () => {
            const params = {
              id: data.id,
              pass: true
              // rejectReason: "",
            }
            // 采方采购确认
            this.postCustomerReconciliationInvoiceV2purchaserConfirm(params, data)
          }
        })
      } else if (tool.id === 'ConfirmInvoiceFinance') {
        // 财务确认
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确定财务确认选中的数据？')
          },
          success: () => {
            const params = {
              id: data.id,
              pass: true
              // rejectReason: "",
            }
            // 采方财务确认
            this.postCustomerReconciliationInvoiceV2FinanceConfirm(params)
          }
        })
      }
    },
    // 对账单外部接口-对账单推送第三方共享财务
    postCustomerReconciliationInvoiceV2PushSharedFinance(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postCustomerReconciliationInvoiceV2PushSharedFinance(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 发票推送预制发票
    postPushPreMakeInvoice(args) {
      const { selectedRecords } = args
      const idList = []
      const invoiceIdList = []
      const outIdList = []
      const vnIdList = []
      selectedRecords.forEach((i) => {
        if (i.reconciliationHeaderCodes[0] === '0') {
          invoiceIdList.push(i.id)
        } else if (['TV_EXTERNAL'].includes(i.reconciliationTypeCode)) {
          outIdList.push(i.reconciliationHeaderIds)
        } else if (['VN_DAILY'].includes(i.reconciliationTypeCode)) {
          vnIdList.push(i.reconciliationHeaderIds)
        } else {
          idList.push(i.reconciliationHeaderIds)
        }
      })
      let apiNum = 0
      let successNum = 0
      if (idList && idList.length) {
        apiNum += 1
      }
      if (invoiceIdList && invoiceIdList.length) {
        apiNum += 1
      }
      if (outIdList && outIdList.length) {
        apiNum += 1
      }
      this.apiStartLoading()
      if (idList && idList.length) {
        this.$API.invoiceCollaboration
          .pushToSapApi({ idList })
          .then((res) => {
            // this.apiEndLoading()
            if (res?.code == 200) {
              // this.$toast({ content: this.$t('操作成功'), type: 'success' })
              // 刷新当前 Grid
              successNum += 1
              this.needRefreshColumns(apiNum, successNum)
            }
          })
          .catch(() => {
            this.apiEndLoading()
          })
      }
      if (invoiceIdList && invoiceIdList.length) {
        this.$API.invoiceCollaboration
          .pushInvoiceNoOrderNumSup({ invoiceIdList })
          .then((res) => {
            // this.apiEndLoading()
            if (res?.code == 200) {
              // this.$toast({ content: this.$t('操作成功'), type: 'success' })
              // 刷新当前 Grid
              successNum += 1
              this.needRefreshColumns(apiNum, successNum)
            }
          })
          .catch(() => {
            this.apiEndLoading()
          })
      }
      if (outIdList && outIdList.length) {
        this.$API.invoiceCollaboration
          .pushSapExternalInvoiceApi({ idList: outIdList, reconciliationType: 1 })
          .then((res) => {
            if (res?.code == 200) {
              successNum += 1
              this.needRefreshColumns(apiNum, successNum)
            }
          })
          .catch(() => {
            this.apiEndLoading()
          })
      }
      if (vnIdList && vnIdList.length) {
        this.$API.invoiceCollaboration
          .pushSapExternalInvoiceApi({ idList: vnIdList, reconciliationType: 2 })
          .then((res) => {
            if (res?.code == 200) {
              successNum += 1
              this.needRefreshColumns(apiNum, successNum)
            }
          })
          .catch(() => {
            this.apiEndLoading()
          })
      }
    },
    needRefreshColumns(apiNum, successNum) {
      if (apiNum === successNum) {
        this.apiEndLoading()
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        // 刷新当前 Grid
        this.refreshColumns()
      }
    },
    // 采方采购确认
    postCustomerReconciliationInvoiceV2purchaserConfirm(params, row) {
      this.apiStartLoading()
      if (row.reconciliationHeaderCodes[0] === '0') {
        this.$API.invoiceCollaboration
          .confirmInvoiceNoOrderNumSup({ invoiceIdList: [row.id], pass: true })
          .then((res) => {
            this.apiEndLoading()
            if (res?.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              // 刷新当前 Grid
              this.refreshColumns()
            }
          })
          .catch(() => {
            this.apiEndLoading()
          })
      } else {
        this.$API.invoiceCollaboration
          .confirmInvoiceSupplier(params)
          .then((res) => {
            this.apiEndLoading()
            if (res?.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              // 刷新当前 Grid
              this.refreshColumns()
            }
          })
          .catch(() => {
            this.apiEndLoading()
          })
      }
    },
    // 采方财务确认
    postCustomerReconciliationInvoiceV2FinanceConfirm(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postCustomerReconciliationInvoiceV2FinanceConfirm(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 确定退回 弹框 调用api完成
    sendBackDialogConfirm() {
      // 刷新当前 Grid
      this.refreshColumns()
    },
    // 验证 发票、同步 状态是否统一
    verifyInvoiceStatus(args) {
      const { data, checkStatus, checkSyncStatus } = args
      let valid = true
      let invoiceStatus = null
      let syncStatus = null
      data.forEach((item, index) => {
        invoiceStatus = item.status // 发票状态
        syncStatus = item.syncStatus // 同步状态

        if (checkStatus && checkSyncStatus) {
          // 校验 发票状态、同步状态
          if (
            item &&
            data[index - 1] &&
            (item.status !== data[index - 1].status ||
              item.syncStatus !== data[index - 1].syncStatus)
          ) {
            valid = false
          }
        }
      })

      return { valid, invoiceStatus, syncStatus }
    },
    // CellTitle
    handleClickCellTitle(args) {
      const { field, data, fieldValue } = args
      if (field === 'reconciliationHeaderCodes') {
        // 点击 关联对账单号
        if (!fieldValue || fieldValue === '0' || fieldValue[0] === '0') {
          return
        }
        if (['TV_EXTERNAL', 'VN_DAILY'].includes(data.reconciliationTypeCode)) {
          this.$router.push({
            name: 'pur-invoice-detail',
            query: {
              id: data.reconciliationHeaderIds,
              code: fieldValue,
              timeStamp: new Date().getTime()
            }
          })
          return
        }
        this.handleReconciliationHeaderCodes({
          data,
          fieldValue
        })
      } else if (field === 'attachementCount') {
        // 点击 发票附件 数量
        this.handleAttachementCount({ data })
      } else if (field === 'reconciliationItemCount') {
        // 点击 关联明细 数量
        this.handleReconciliationItemCountClick(data)
      }
    },
    // 点击 关联对账单号
    handleReconciliationHeaderCodes(args) {
      const { data } = args
      // 根据id 获取单据数据
      const params = {
        page: { current: 1, size: 10 },
        reconciliationCode: data.reconciliationHeaderCodes[0]
      }
      this.apiStartLoading()
      // 获取单据信息 租户级-发票协同-采方主单-对账信息
      this.$API.invoiceCollaboration
        .queryPurchaseDetailTV(params)
        .then((res) => {
          this.apiEndLoading()
          const reconciliationHeaderInfo = res?.data?.records[0] || {}
          // 从 可开票单据 跳转 单据详情页
          this.goToInvoiceDetail({
            headerInfo: reconciliationHeaderInfo,
            entryType: ConstantType.look,
            status: data.status,
            id: data.id
          })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 点击 发票附件 数量
    handleAttachementCount(args) {
      const { data } = args
      // 获取发票附件数据 后 显示 发票附件列表 弹框
      const params = {
        id: data.id
      }
      this.apiStartLoading()
      // 获取发票附件列表 查询附件列表
      this.$API.invoiceCollaboration
        .postCustomerReconciliationInvoiceV2FileList(params)
        .then((res) => {
          this.apiEndLoading()
          const fileData = res?.data || []
          // 显示附件查看弹框
          const dialogParams = {
            fileData: fileData,
            isView: true,
            title: this.$t('发票附件')
          }
          this.$refs.uploaderDialog.dialogInit(dialogParams)
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 点击 关联明细 数量
    handleReconciliationItemCountClick(data) {
      const params = {
        businessTypeCode: data.reconciliationBusinessTypeCode,
        reconciliationTypeCode: data.reconciliationTypeCode
      }
      this.apiStartLoading()
      // 获取 明细行 表头 采购对账字段配置
      this.$API.reconciliationSettlement
        .postReconciliationConfigFieldQuery(params)
        .then((res) => {
          this.apiEndLoading()
          const fieldList = res?.data || []
          const fieldConfig = fieldList.find(
            (item) => item.code === TabCode.reconciliationField && item.checkStatus
          )
          const fields = fieldConfig?.fieldResponseList || []
          const columnData = formatTableColumnData({
            table: Table.statementDetails,
            data: fields
          })
          // 显示明细行列表弹框 查看关联 明细行、高低开
          this.$refs.billDetailsTableDialog.dialogInit({
            title: this.$t('关联明细行列表'),
            actionType: BillDetailsTableDialogActionType.view,
            dataItemAsyncConfig: {
              // 新增发票时，dataId传0、defaultRule里面传headerId即可；
              // 修改发票时，dataId传发票id，defaultRule里面传headerId，能查出来关联了发票的明细行以及未关联发票的明细行
              // 查看发票明细行时，dataId传发票id，在defaultRule里面再传invoiceId对应当前发票id，只查出来当前发票关联的明细行
              url: `${BASE_TENANT}/customer-reconciliation-invoice-v2/item-page-with-invoice`, // 发票协同-供方-查询对账明细
              condition: 'and',
              params: {
                dataId: data.id // 发票新增时传 0，编辑、查看时传 发票id
              },
              defaultRules: [
                {
                  field: 'invoiceId',
                  operator: 'equal',
                  value: data.id
                }
              ]
            },
            dataItemColumnData: columnData,
            // 高低开
            highLowInfoAsyncConfig: {
              url: `${BASE_TENANT}/reconciliationHighLow/queryByInvoiceId`,
              params: {
                id: data.id // 发票id
              },
              recordsPosition: 'data'
            }
          })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 跳转到 发票详情页面
    goToInvoiceDetail(data) {
      const { headerInfo, entryType, status, id } = data
      // 发票详情 页面参数
      const params = {
        reconciliationCode: headerInfo.reconciliationHeaderCodes, // 对账单号
        headerInfo, // 头部信息 行数据
        entryType // 页面类型
      }
      localStorage.setItem('summaryV2Data', JSON.stringify(params))
      // 跳转到 发票详情页面
      this.$router.push({
        name: 'invoice-detail-tv',
        query: { fromType: 'list', status, id }
      })
    },
    // 刷新当前 Grid
    refreshColumns() {
      // this.$refs.templateRef.refreshCurrentGridData()
      this.handleCustomSearch()
      // this.search()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
