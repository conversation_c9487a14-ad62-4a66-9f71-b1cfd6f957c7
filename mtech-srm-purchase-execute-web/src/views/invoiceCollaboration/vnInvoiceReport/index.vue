<!-- 越南发票报表 -->
<template>
  <div>
    <mt-tabs
      class="toggle-tab"
      :e-tab="false"
      :data-source="tabList"
      :selected-item="selectedItem"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <div class="toggle-content">
      <standard v-show="tabIndex === 0" />
      <consignment v-show="tabIndex === 1" />
    </div>
  </div>
</template>

<script>
export default {
  components: {
    standard: () => import('./pages/standard/index.vue'),
    consignment: () => import('./pages/consignment/index.vue')
  },
  data() {
    return {
      tabList: [{ title: this.$t('标准') }, { title: this.$t('寄售') }],
      selectedItem: this.$route.query.currentIndex ?? 0,
      tabIndex: this.$route.query.currentIndex ?? 0
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    }
  }
}
</script>
