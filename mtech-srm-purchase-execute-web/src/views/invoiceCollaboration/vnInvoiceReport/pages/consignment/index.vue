<!-- 寄售 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item prop="companyCode" :label="$t('公司')">
          <RemoteAutocomplete
            v-model="searchFormModel.companyCode"
            url="/masterDataManagement/tenant/organization/specified-level-paged-query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :params="{
              organizationLevelCodes: ['ORG02', 'ORG01'],
              orgType: 'ORG001PRO',
              includeItself: true
            }"
          />
        </mt-form-item>
        <mt-form-item prop="factoryCodeList" :label="$t('工厂')">
          <RemoteAutocomplete
            v-model="searchFormModel.factoryCodeList"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item prop="materialVoucherYear" :label="$t('对账日期')">
          <mt-date-range-picker
            style="flex: 1"
            v-model="searchFormModel.materialVoucherYear"
            @change="(e) => handleDateTimeChange(e, 'materialVoucherYear')"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="invoiceNum" :label="$t('发票号')">
          <mt-input
            v-model="searchFormModel.invoiceNum"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商编码')">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            :placeholder="$t('请选择供应商')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="recoCode" :label="$t('对账单号')">
          <mt-input
            v-model="searchFormModel.recoCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="materialCode" :label="$t('物料编码')">
          <RemoteAutocomplete
            v-model="searchFormModel.materialCode"
            :url="$API.masterData.getItemUrl"
            :placeholder="$t('请选择')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :search-fields="['itemName', 'itemCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="d501f12c-7425-4018-9700-2b05054da217"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #recoCodeDetault="{ row }">
        <div :class="['able-click-field']" @click="recoCodeClick(row)">
          {{ row.recoCode }}
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData } from './config'
export default {
  components: {
    CollapseSearch,
    ScTable
  },
  data() {
    const startDate = dayjs().subtract(3, 'month').startOf('month')
    return {
      searchFormModel: {
        materialVoucherYear: [new Date(startDate), new Date()],
        materialVoucherYearEnd: dayjs(new Date()).format('YYYYMMDD'),
        materialVoucherYearStart: dayjs(new Date(startDate)).format('YYYYMMDD')
      },
      searchFormRules: {},
      tableData: [],
      loading: false,
      columns: columnData,
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      }
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    toolbar() {
      let btns = [{ code: 'sync', name: this.$t('同步SAP'), status: 'info', loading: false }]
      return btns
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    recoCodeClick(row) {
      this.$router.push({
        name: 'pur-recon-detail-vn',
        query: {
          id: row.headerId,
          code: row.recoCode,
          type: 'invoice-report-vn',
          currentIndex: 1,
          timeStamp: new Date().getTime()
        }
      })
    },
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = dayjs(e.startDate).format('YYYYMMDD')
        this.searchFormModel[field + 'End'] = dayjs(e.endDate).format('YYYYMMDD')
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.invoiceCollaboration
        .pageVnInvoiceReportConsignmentApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['sync']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'sync':
          this.handleSync(selectedRecords)
          break
        default:
          break
      }
    },
    handleSync(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.invoiceCollaboration.pushSapVnInvoiceReportConsignmentApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('同步成功'), type: 'success' })
          this.handleSearch()
        }
      })
    }
  }
}
</script>
