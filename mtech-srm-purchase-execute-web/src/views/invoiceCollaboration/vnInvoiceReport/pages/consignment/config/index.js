import { i18n } from '@/main.js'

export const syncStatusOptions = [
  { text: i18n.t('未同步'), value: 0 },
  { text: i18n.t('同步成功'), value: 1 },
  { text: i18n.t('同步失败'), value: 2 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'companyName',
    title: i18n.t('公司'),
    minWidth: 140
  },
  {
    field: 'recoCode',
    title: i18n.t('对账单号'),
    minWidth: 140,
    slots: {
      default: 'recoCodeDetault'
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 140
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称'),
    minWidth: 140
  },
  {
    field: 'currencyName',
    title: i18n.t('币种')
  },
  {
    field: 'factoryCode',
    title: i18n.t('工厂')
  },
  {
    field: 'qty',
    title: i18n.t('对账数量')
  },
  {
    field: 'executionTotalPriceUntaxed',
    title: i18n.t('对账金额')
  },
  {
    field: 'invoiceNum',
    title: i18n.t('发票号')
  },
  {
    field: 'receiveCode',
    title: i18n.t('物料凭证号')
  },
  {
    field: 'materialVoucherItemNo',
    title: i18n.t('物料凭证行号')
  },
  {
    field: 'syncStatus',
    title: i18n.t('推送SAP状态'),
    formatter: ({ cellValue }) => {
      let item = syncStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'syncInfo',
    title: i18n.t('推送SAP信息')
  },
  {
    field: 'materialVoucherYear',
    title: i18n.t('对账日期')
  }
]
