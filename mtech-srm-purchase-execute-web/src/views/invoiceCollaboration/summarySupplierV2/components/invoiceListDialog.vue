<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules">
      <mt-form-item prop="reconciliationItemCount" :label="$t('明细行数')" class="">
        <mt-input
          v-model="formData.reconciliationItemCount"
          :disabled="true"
          :show-clear-button="true"
          placeholder=""
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="reconciliationTaxedAmount" :label="$t('明细汇总含税金额')" class="">
        <mt-input
          v-model="formData.reconciliationTaxedAmount"
          :disabled="true"
          :show-clear-button="true"
          placeholder=""
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="reconciliationUntaxAmount" :label="$t('明细汇总未税金额')" class="">
        <mt-input
          v-model="formData.reconciliationUntaxAmount"
          :disabled="true"
          :show-clear-button="true"
          placeholder=""
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="reconciliationTaxAmount" :label="$t('明细汇总税额')" class="">
        <mt-input
          v-model="formData.reconciliationTaxAmount"
          :disabled="true"
          :show-clear-button="true"
          placeholder=""
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="invoiceType" :label="$t('发票类型')" class="">
        <mt-select
          v-model="formData.invoiceType"
          :data-source="invoiceTypeOptions"
          :show-clear-button="true"
          :placeholder="$t('发票类型')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="invoiceCode" :label="$t('发票代码')" class="">
        <mt-input
          maxlength="50"
          v-model="formData.invoiceCode"
          :disabled="false"
          :show-clear-button="true"
          :placeholder="$t('发票代码')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="invoiceNum" :label="$t('发票号')" class="">
        <mt-input
          maxlength="50"
          v-model="formData.invoiceNum"
          :disabled="false"
          :show-clear-button="true"
          :placeholder="$t('发票号')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="invoiceTime" :label="$t('开票日期')" class="">
        <mt-date-picker
          :show-clear-button="true"
          :max="new Date()"
          :allow-edit="false"
          :placeholder="$t('开票日期')"
          v-model="formData.invoiceTime"
        ></mt-date-picker>
      </mt-form-item>
      <mt-form-item prop="invoiceTaxedAmount" :label="$t('发票含税金额')" class="">
        <mt-input-number
          max="999999999999999.99"
          :show-clear-button="true"
          :show-spin-button="false"
          :placeholder="$t('发票含税金额')"
          :precision="2"
          @input="handleTaxedAmount"
          v-model="formData.invoiceTaxedAmount"
        ></mt-input-number>
      </mt-form-item>
      <mt-form-item prop="invoiceUntaxAmount" :label="$t('发票未税金额')" class="">
        <mt-input-number
          max="999999999999999.99"
          :show-clear-button="true"
          :show-spin-button="false"
          :precision="2"
          @input="handleUntaxedAmount"
          :placeholder="$t('发票未税金额')"
          v-model="formData.invoiceUntaxAmount"
        ></mt-input-number>
      </mt-form-item>
      <mt-form-item prop="invoiceTaxAmount" :label="$t('税额')" class="">
        <mt-input
          :disabled="true"
          v-model="formData.invoiceTaxAmount"
          :show-clear-button="true"
          :placeholder="$t('税额')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="supplierRemark" :label="$t('供方备注')" class="full-width">
        <mt-input
          maxlength="200"
          v-model="formData.supplierRemark"
          :show-clear-button="true"
          :placeholder="$t('供方备注')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="fileList" :label="$t('附件上传')" class="full-width">
        <upload-file
          ref="uploader"
          :view-file-data="formData.fileList"
          @change="fileChange"
        ></upload-file>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { InvoiceListDialogActionType } from '../config/constant'
import bigDecimal from 'js-big-decimal'
import UploadFile from '@/components/Upload/uploader'
import { cloneDeep } from 'lodash'

export default {
  components: {
    UploadFile
  },
  data() {
    // 发票含税金额
    const taxedAmountValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入发票含税金额')))
      } else if (
        this.formData.invoiceUntaxAmount &&
        Number(value) < Number(this.formData.invoiceUntaxAmount)
      ) {
        callback(new Error(this.$t('含税金额不能小于未税金额')))
      } else {
        this.$refs.ruleForm.clearValidate(['invoiceUntaxAmount'])
        callback()
      }
    }
    // 发票未税金额
    const untaxedAmountValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入发票未税金额')))
      } else if (
        this.formData.invoiceTaxedAmount &&
        Number(value) > Number(this.formData.invoiceTaxedAmount)
      ) {
        callback(new Error(this.$t('未税金额不能大于含税金额')))
      } else {
        this.$refs.ruleForm.clearValidate(['invoiceTaxedAmount'])
        callback()
      }
    }
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      dialogTitle: '',
      selectData: null,
      selectItemInfo: {}, // 选中的行数据汇总信息
      headerInfoId: '', // 主单id/任意一个单据id
      rules: {
        invoiceType: [
          {
            required: true,
            message: this.$t('请选择发票类型'),
            trigger: 'blur'
          }
        ],
        invoiceCode: [
          {
            required: true,
            message: this.$t('请输入发票代码'),
            trigger: 'blur'
          }
        ],
        invoiceNum: [{ required: true, message: this.$t('请输入发票号'), trigger: 'blur' }],
        invoiceTime: [
          {
            required: true,
            message: this.$t('请选择开票日期'),
            trigger: 'blur'
          }
        ],
        invoiceTaxedAmount: [{ required: true, validator: taxedAmountValidator, trigger: 'blur' }],
        invoiceUntaxAmount: [
          {
            required: true,
            validator: untaxedAmountValidator,
            trigger: 'blur'
          }
        ]
      },
      invoiceTypeOptions: [], // 发票类型
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      actionType: InvoiceListDialogActionType.add, // 默认类型：新增
      formData: {
        reconciliationItemCount: 0, // 明细行数
        reconciliationTaxedAmount: 0, // 明细汇总含税金额
        reconciliationUntaxAmount: 0, // 明细汇总未税金额
        reconciliationTaxAmount: 0, // 明细汇总税额
        invoiceType: null, // 发票类型
        invoiceCode: '', // 发票代码
        invoiceNum: '', // 发票号
        invoiceTime: '', // 开票日期
        invoiceUntaxAmount: '', // 发票未税金额
        invoiceTaxAmount: '', // 税额
        invoiceTaxedAmount: '', // 发票含税金额
        // currencyName: "", //币种
        supplierRemark: '', // 供应商备注
        fileList: [] // 发票附件
      },
      fileIdListInit: [] // 发票附件id列表 初始化 用于过滤出新上传的文件
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      const { title, actionType, selectData, selectItemInfo } = entryInfo
      // 获取 发票类型
      this.getInvoiceTypeOptions({ selectData, selectItemInfo })
      // this.headerInfoId, // 主单id/任意一个单据id
      this.dialogTitle = title // 弹框名称
      this.actionType = actionType // 弹框模式
      this.selectData = selectData // 发票行数据
      this.selectItemInfo = selectItemInfo // 选择的明细行汇总信息
      this.initForm()
      this.$refs.dialog.ejsRef.show()
    },
    initForm() {
      this.formData = {
        id: undefined, // 发票id
        invoiceType: null, // 发票类型
        invoiceCode: '', // 发票代码
        invoiceNum: '', // 发票号
        invoiceTime: '', // 开票日期
        invoiceUntaxAmount: '', // 发票未税金额
        invoiceTaxAmount: '', // 税额
        invoiceTaxedAmount: '', // 发票含税金额
        currencyName: '', //币种
        supplierRemark: '', // 供应商备注
        fileList: [] // 发票附件
      }
      this.fileIdListInit = []
      this.$refs.ruleForm.clearValidate()
      if (this.actionType === InvoiceListDialogActionType.edit) {
        // 编辑
        let invoiceTime = ''
        // 日期转换
        if (this.selectData.invoiceTime) {
          const date = new Date(this.selectData.invoiceTime) // 日期类型格式 时间戳
          // 如果 invoiceTime 是一个日期，就使用它
          if (!isNaN(date.getTime())) {
            invoiceTime = date
          }
        }
        const selectData = cloneDeep(this.selectData)
        if (this.selectData.fileList && this.selectData.fileList.length > 0) {
          this.selectData.fileList.forEach((item) => {
            this.fileIdListInit.push(item.id)
          })
        }

        this.formData = {
          ...selectData,
          invoiceTime // 开票日期
        }
      } else if (this.actionType === InvoiceListDialogActionType.apply) {
        // 申请开票
        this.formData = {
          id: undefined, // 发票id
          invoiceType: null, // 发票类型
          invoiceCode: '', // 发票代码
          invoiceNum: '', // 发票号
          invoiceTime: '', // 开票日期
          invoiceUntaxAmount: this.selectItemInfo.reconciliationUntaxAmount, // 发票未税金额 默认为 汇总未税金额
          invoiceTaxAmount: this.selectItemInfo.reconciliationTaxAmount, // 税额 默认为 汇总税额
          invoiceTaxedAmount: this.selectItemInfo.reconciliationTaxedAmount, // 发票含税金额 默认为 汇总含税金额
          currencyName: '', //币种
          supplierRemark: '', // 供应商备注
          fileList: [], // 发票附件
          ...this.selectItemInfo // 展开选择的明细行汇总信息
        }
      }
    },
    fileChange(data) {
      this.formData.fileList = data
    },
    // 发票未税金额 改变
    handleUntaxedAmount(e) {
      const invoiceTaxedAmount = this.formData.invoiceTaxedAmount ?? 0 // 含税
      const invoiceUntaxAmount = e ?? 0 // 未税
      // 税额 = 含税 - 未税
      this.formData.invoiceTaxAmount = bigDecimal.subtract(invoiceTaxedAmount, invoiceUntaxAmount)
    },
    // 发票含税金额 改变
    handleTaxedAmount(e) {
      const invoiceTaxedAmount = e ?? 0 // 含税
      const invoiceUntaxAmount = this.formData.invoiceUntaxAmount ?? 0 // 未税
      // 税额 = 含税 - 未税
      this.formData.invoiceTaxAmount = bigDecimal.subtract(invoiceTaxedAmount, invoiceUntaxAmount)
    },
    onOpen(args) {
      args.preventFocus = true
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const data = {
            ...this.formData,
            invoiceUntaxAmount: Number(this.formData.invoiceUntaxAmount),
            invoiceTaxedAmount: Number(this.formData.invoiceTaxedAmount),
            invoiceTime: Number(this.formData.invoiceTime) // 开票日期 时间戳
          }
          if (this.actionType === InvoiceListDialogActionType.apply) {
            // 申请开票
            let invoiceTypeName = ''
            this.invoiceTypeOptions.forEach((item) => {
              if (item.value == data.invoiceType) {
                invoiceTypeName = item.text
              }
            })
            const params = {
              ...data,
              invoiceType: undefined,
              invoiceTypeCode: data.invoiceType, // 发票类型
              invoiceTypeName: invoiceTypeName, // 发票类型名称
              reconciliationHeaderIds: this.selectItemInfo.reconciliationHeaderIds, // 对账单ids
              submit: true // 新增后触发提交操作
            }
            // api 调用
            this.postReconciliationInvoiceV2Add(params)
          } else if (this.actionType === InvoiceListDialogActionType.edit) {
            // 编辑发票
            let fileList = []
            if (this.fileIdListInit.length > 0) {
              // 当原来存在文件时，将新增的文件过滤出来，并把id置为空
              fileList = this.formData.fileList
                .filter((item) => !this.fileIdListInit.includes(item.id))
                .map((item) => {
                  return {
                    ...item,
                    id: undefined,
                    docId: this.selectData.id // 文件docId 为 发票id
                  }
                })
              data.fileIds = this.fileIdListInit
            } else {
              // 当原来不存在发票时，直接将新上传的文件id置为空，设置上docId给后端
              fileList = this.formData.fileList.map((item) => {
                return {
                  ...item,
                  id: undefined,
                  docId: this.selectData.id // 文件docId 为 发票id
                }
              })
            }
            let invoiceTypeName = ''
            this.invoiceTypeOptions.forEach((item) => {
              if (item.value == data.invoiceType) {
                invoiceTypeName = item.text
              }
            })
            const reconciliationHeaderIds = data.reconciliationHeaderIds
              .map((item) => {
                return `${item.supplierHeaderId}||${item.buyerHeaderId}`
              })
              .join(',')
            const params = {
              ...data,
              reconciliationHeaderIds: reconciliationHeaderIds, // 对账单ids
              invoiceType: undefined,
              invoiceTypeCode: data.invoiceType, // 发票类型
              invoiceTypeName: invoiceTypeName, // 发票类型名称
              fileList, // 新增的文件
              deleteFileIdList: this.getDeleteFileList() // 删除的附件列表
            }
            this.putReconciliationInvoiceV2Update(params)
          }
        }
      })
    },
    // 对比 selectData 和 formData 的 fileList，获得 deleteFileList
    getDeleteFileList() {
      const deleteFileList = []
      const selectFileList = this.selectData.fileList
      const formFileList = this.formData.fileList
      // 如果 selectData 中的 fileList 为空，则直接返回 []
      if (!selectFileList || selectFileList.length === 0) {
        return []
      }
      // 如果 formData 中的 fileList 为空，则直接返回 selectData 中的 fileList
      if (!formFileList || formFileList.length === 0) {
        return selectFileList
      }
      // 如果 selectData 中的 fileList 与 formData 中的 fileList 都不为空，则对比
      selectFileList.forEach((item) => {
        const index = formFileList.findIndex((i) => i.id === item.id)
        if (index === -1) {
          deleteFileList.push(item)
        }
      })
      return deleteFileList
    },
    // 租户级-供方对账单发票信息-v2接口-新增
    postReconciliationInvoiceV2Add(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postReconciliationInvoiceV2Add(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.handleClose()
            this.$emit('confirm', { actionType: this.actionType })
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-供方对账单发票信息-v2接口-更新
    putReconciliationInvoiceV2Update(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .putReconciliationInvoiceV2Update(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.handleClose()
            this.$emit('confirm', { actionType: this.actionType })
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 获取发票类型
    getInvoiceTypeOptions(args) {
      const { selectData, selectItemInfo } = args
      if (selectItemInfo && selectItemInfo.reconciliationHeaderIds) {
        this.headerInfoId = selectItemInfo.reconciliationHeaderIds.split(',')[0]
      } else if (selectData && selectData.reconciliationHeaderIds) {
        this.headerInfoId = selectData.reconciliationHeaderIds[0].supplierHeaderId
      }
      // 根据主单查询采方的字典 发票类型
      this.postSupplierDictByHeader()
    },
    // 根据主单查询采方的字典 发票类型
    postSupplierDictByHeader() {
      this.$API.invoiceCollaboration
        .postSupplierDictByHeader({
          dataId: this.headerInfoId, // 主单id/任意一个单据id
          dictCode: 'invoiceType' // 字典编码
        })
        .then((res) => {
          const data = res?.data || []
          this.invoiceTypeOptions = [] // 清空发票类型
          this.invoiceTypeOptions = data.map((item) => {
            return {
              text: item.itemName,
              value: item.itemCode
            }
          })
        })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
