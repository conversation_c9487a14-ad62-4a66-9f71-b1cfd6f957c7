import { i18n } from '@/main.js'

export const statusOptions = [
  { text: i18n.t('新建'), value: 0 },
  { text: i18n.t('已发布'), value: 1 },
  { text: i18n.t('已完成'), value: 2 }
]

export const syncStatusOptions = [
  { text: i18n.t('未同步'), value: 0 },
  { text: i18n.t('同步成功'), value: 1 },
  { text: i18n.t('同步失败'), value: 2 }
]

export const invoiceTypeOptions = [
  { text: i18n.t('蓝字正常发票'), value: 'blue' },
  { text: i18n.t('蓝字补收发票'), value: 'debit' },
  { text: i18n.t('红字正常发票'), value: 'red' }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'code',
    title: i18n.t('单号'),
    minWidth: 140
  },
  {
    field: 'invoiceType',
    title: i18n.t('发票类型'),
    formatter: ({ cellValue }) => {
      let item = invoiceTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 200,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    }
  },
  {
    field: 'companyCode',
    title: i18n.t('公司'),
    minWidth: 200,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.companyName : ''
    }
  },
  {
    field: 'invoiceNo',
    title: i18n.t('发票号'),
    minWidth: 140
  },
  {
    field: 'status',
    title: i18n.t('汇款状态'),
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'syncStatus',
    title: i18n.t('同步外部系统状态'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      let item = syncStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'amount',
    title: i18n.t('汇款金额')
  },
  {
    field: 'currencyCode',
    title: i18n.t('币种')
  },
  {
    field: 'returnCode',
    title: i18n.t('同步-code'),
    minWidth: 160
  },
  {
    field: 'reason',
    title: i18n.t('同步-reason'),
    minWidth: 160
  },
  {
    field: 'msg',
    title: i18n.t('同步-message'),
    minWidth: 160
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  },
  {
    field: 'remark',
    title: i18n.t('备注'),
    minWidth: 120
  }
]
