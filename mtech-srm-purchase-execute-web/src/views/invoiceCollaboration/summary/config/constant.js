import { i18n } from '@/main.js'

export const ConstantType = {
  Add: '1', // 新增
  Edit: '2', // 编辑
  Look: '3' // 查看
}

// 开票状态 0:未开票 1:已开票 2:采购已确认 -1:反馈异常 3:财务已确认 4: 已退回
export const InvoiceStatus = {
  abnormal: -1, // 反馈异常
  notInvoiced: 0, // 未开票
  invoiced: 1, // 已开票
  confirmed: 2, // 采购已确认
  confirmedFinance: 3, // 财务已确认
  returned: 4 // 已退回
}

// 开票状态
export const InvoiceStatusConst = {
  [InvoiceStatus.abnormal]: i18n.t('反馈异常'),
  [InvoiceStatus.notInvoiced]: i18n.t('未开票'),
  [InvoiceStatus.invoiced]: i18n.t('已开票'),
  [InvoiceStatus.confirmed]: i18n.t('采购已确认'),
  [InvoiceStatus.confirmedFinance]: i18n.t('财务已确认'),
  [InvoiceStatus.returned]: i18n.t('已退回')
}

// 开票状态 对应的 css class
export const InvoiceStatusCssClass = {
  [InvoiceStatus.abnormal]: 'col-abnormal',
  [InvoiceStatus.notInvoiced]: 'col-published',
  [InvoiceStatus.invoiced]: 'col-active',
  [InvoiceStatus.confirmed]: 'col-normal',
  [InvoiceStatus.confirmedFinance]: 'col-normal',
  [InvoiceStatus.returned]: i18n.t('col-published')
}

// 开票状态 对应的 Options
export const InvoiceStatusOptions = [
  {
    // 未开票
    value: InvoiceStatus.notInvoiced,
    text: InvoiceStatusConst[InvoiceStatus.notInvoiced],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.notInvoiced]
  },
  {
    // 已开票
    value: InvoiceStatus.invoiced,
    text: InvoiceStatusConst[InvoiceStatus.invoiced],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.invoiced]
  },
  {
    // 采购已确认
    value: InvoiceStatus.confirmed,
    text: InvoiceStatusConst[InvoiceStatus.confirmed],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.confirmed]
  },
  {
    // 财务已确认
    value: InvoiceStatus.confirmedFinance,
    text: InvoiceStatusConst[InvoiceStatus.confirmedFinance],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.confirmedFinance]
  },
  {
    // 反馈异常
    value: InvoiceStatus.abnormal,
    text: InvoiceStatusConst[InvoiceStatus.abnormal],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.abnormal]
  },
  {
    // 已退回
    value: InvoiceStatus.returned,
    text: InvoiceStatusConst[InvoiceStatus.returned],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.returned]
  }
]

// 同步状态
export const SyncStatus = {
  synced: 1, // 是
  notSynced: 0 // 否
}
// 同步状态 对应的 Options
export const SyncStatusOptions = [
  {
    value: SyncStatus.notSynced,
    text: i18n.t('未同步'),
    cssClass: 'col-notSynced'
  },
  {
    value: SyncStatus.synced,
    text: i18n.t('已同步'),
    cssClass: 'col-synced'
  }
]

// 单据状态 -1:关闭 0:未发布 1:待反馈 2:反馈正常 3:反馈异常
export const ConstStatus = {
  close: -1,
  unpublished: 0,
  toBeConfirmed: 1,
  normal: 2,
  abnormal: 3
}

export const Toolbar = [
  {
    title: i18n.t('推送共享财务'),
    icon: 'icon_table_pushFinance',
    id: 'PushSharedFinance'
    // permission: ["O_02_0081"],
  },
  {
    title: i18n.t('推送 SAP'),
    icon: 'icon_table_pushSap',
    id: 'PushSAP'
    // permission: ["O_02_0082"],
  }
]

// 表格列数据
export const ColumnData = [
  {
    fieldCode: 'reconciliationCode',
    fieldName: i18n.t('对账单号')
  },
  {
    fieldCode: 'invoiceStatus',
    fieldName: i18n.t('开票状态') // 开票状态
  },
  {
    fieldCode: 'syncStatus',
    fieldName: i18n.t('同步状态') // 同步状态
  },
  {
    fieldCode: 'reconciliationTypeName',
    fieldName: i18n.t('对账类型')
  },
  {
    fieldCode: 'companyName',
    fieldName: i18n.t('客户公司')
  },
  {
    fieldCode: 'supplierName',
    fieldName: i18n.t('供应商')
  },
  {
    fieldCode: 'executeUntaxedTotalPrice',
    fieldName: i18n.t('执行未税总价')
  },
  {
    fieldCode: 'taxAmount',
    fieldName: i18n.t('税额')
  },
  {
    fieldCode: 'executeTaxedTotalPrice',
    fieldName: i18n.t('执行含税总价')
  },
  {
    fieldCode: 'taxInvoiceBalance',
    fieldName: i18n.t('含税发票差额')
  },
  {
    fieldCode: 'currencyName',
    fieldName: i18n.t('币种')
  },
  {
    fieldCode: 'invoiceQuantity',
    fieldName: i18n.t('发票张数')
  },
  {
    fieldCode: 'remark',
    fieldName: i18n.t('采方备注')
  },
  {
    fieldCode: 'feedbackRemark',
    fieldName: i18n.t('供方备注')
  },
  {
    fieldCode: 'createUserName',
    fieldName: i18n.t('创建人')
  },
  {
    fieldCode: 'createTime',
    fieldName: i18n.t('创建时间')
  }
]

export const ColumnCheckbox = {
  width: '50',
  type: 'checkbox',
  showInColumnChooser: false
}
