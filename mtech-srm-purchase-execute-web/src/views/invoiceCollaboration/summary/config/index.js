import Vue from 'vue'
import utils from '@/utils/utils'
import { i18n } from '@/main.js'
import { InvoiceStatus, InvoiceStatusOptions, SyncStatusOptions } from './constant'

// data: yyyy-mm-dd hh:mm:ss
export const timeToDate = (data) => {
  const { formatString, value } = data
  if (formatString && value) {
    const date = new Date(value)
    if (isNaN(date.getTime())) {
      return value
    } else {
      return utils.formateTime(date, formatString)
    }
  } else {
    return value
  }
}

// 格式化表格动态数据
export const formatTableColumnData = (data) => {
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150'
    }
    if (col.fieldCode === 'reconciliationCode') {
      // 对账单号
      defaultCol.width = '200'
      defaultCol.cellTools = [] // 使其可点击查看
    } else if (col.fieldCode === 'invoiceStatus') {
      // 开票状态
      defaultCol.width = '200'
      defaultCol.valueConverter = {
        type: 'map',
        map: InvoiceStatusOptions
      }
      defaultCol.cellTools = [
        {
          id: 'UploadInvoice',
          icon: 'icon_list_download rotate-180',
          title: i18n.t('上传发票'),
          // permission: ["O_02_0083"],
          visibleCondition: (data) =>
            data.invoiceStatus == InvoiceStatus.notInvoiced ||
            data.invoiceStatus == InvoiceStatus.abnormal ||
            data.invoiceStatus == InvoiceStatus.returned // 未开票 || 反馈异常 || 已退回
        },
        {
          id: 'ConfirmInvoice',
          icon: 'icon_list_InvoiceConfirmation',
          title: i18n.t('采购确认'),
          // permission: ["O_02_0084"],
          visibleCondition: (data) => data.invoiceStatus == InvoiceStatus.invoiced // 已开票
        },
        {
          id: 'ConfirmInvoiceFinance',
          icon: 'icon_list_InvoiceConfirmation',
          title: i18n.t('财务确认'), // TODO 增加的按钮，加权限
          visibleCondition: (data) => data.invoiceStatus == InvoiceStatus.confirmed // 采购已确认
        },
        {
          id: 'SendBack',
          icon: 'icon_list_recall',
          title: i18n.t('退回'),
          // permission: ["O_02_0085"],
          visibleCondition: (data) =>
            data.invoiceStatus == InvoiceStatus.invoiced ||
            data.invoiceStatus == InvoiceStatus.confirmed ||
            data.invoiceStatus == InvoiceStatus.confirmedFinance // 已开票 || 采购已确认 || 财务已确认
        }
      ]
    } else if (col.fieldCode === 'syncStatus') {
      // 同步状态
      defaultCol.valueConverter = {
        type: 'map',
        map: SyncStatusOptions
      }
    } else if (col.fieldCode === 'createTime') {
      // 创建时间
      defaultCol.template = () => {
        return {
          template: Vue.component('date', {
            template: `<div><div>{{data.createTime | timeFormat}}</div><div>{{data.createTime | dateFormat}}</div></div>`,
            data: function () {
              return { data: {} }
            },
            filters: {
              dateFormat(value) {
                return timeToDate({ formatString: 'YYYY-mm-dd', value })
              },
              timeFormat(value) {
                return timeToDate({ formatString: 'HH:MM:SS', value })
              }
            }
          })
        }
      }
    } else if (col.fieldCode === 'publishTime') {
      // 发布时间
      defaultCol.template = () => {
        return {
          template: Vue.component('date', {
            template: `<div><div>{{data.publishTime | timeFormat}}</div><div>{{data.publishTime | dateFormat}}</div></div>`,
            data: function () {
              return { data: {} }
            },
            filters: {
              dateFormat(value) {
                return timeToDate({ formatString: 'YYYY-mm-dd', value })
              },
              timeFormat(value) {
                return timeToDate({ formatString: 'HH:MM:SS', value })
              }
            }
          })
        }
      }
    }
    colData.push(defaultCol)
  })

  return colData
}
