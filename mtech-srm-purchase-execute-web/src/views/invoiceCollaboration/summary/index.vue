<template>
  <!-- 发票协同（采方）列表 -->
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      :hidden-tabs="true"
      class="template-height"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
  </div>
</template>

<script>
import {
  ColumnData,
  ConstantType,
  ConstStatus,
  InvoiceStatus,
  Toolbar,
  SyncStatus
} from './config/constant'
import { formatTableColumnData } from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'

export default {
  components: {},
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: Toolbar,
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: formatTableColumnData(ColumnData),
            dataSource: [],
            // queryBuilder查询-对账信息
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliationHeader/queryBuilder`,
              defaultRules: [
                {
                  field: 'status',
                  operator: 'equal',
                  value: ConstStatus.normal
                }
              ]
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      const selectedRecords = e.gridRef.getMtechGridRecords()
      const commonToolbar = [
        'Add',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]
      if (selectedRecords.length == 0 && !commonToolbar.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })

      if (e.toolbar.id === 'PushSharedFinance') {
        // 推送共享财务
        this.handlePushFIN({ selectedRecords, idList })
      } else if (e.toolbar.id === 'PushSAP') {
        // 推送 SAP
        this.handlePushSAP({ selectedRecords, idList })
      }
    },
    // 推送共享财务
    handlePushFIN(args) {
      const { selectedRecords, idList } = args
      const { valid, invoiceStatus, syncStatus } =
        this.verifyInvoiceStatusAndSyncStatus(selectedRecords)
      if (
        !valid ||
        invoiceStatus != InvoiceStatus.confirmedFinance ||
        syncStatus != SyncStatus.notSynced
      ) {
        this.$toast({
          content: this.$t('请选择财务已确认且未同步的数据'),
          type: 'warning'
        })
      } else {
        // 状态: 财务已确认 && 同步状态: 未同步
        this.postReconciliationPushSharedFinance(idList)
      }
    },
    // 推送 SAP
    handlePushSAP(args) {
      const { selectedRecords, idList } = args
      const { valid, invoiceStatus, syncStatus } =
        this.verifyInvoiceStatusAndSyncStatus(selectedRecords)
      if (
        !valid ||
        invoiceStatus != InvoiceStatus.confirmedFinance ||
        syncStatus != SyncStatus.notSynced
      ) {
        this.$toast({
          content: this.$t('请选择财务已确认且未同步的数据'),
          type: 'warning'
        })
      } else {
        // 状态: 财务已确认 && 同步状态: 未同步
        this.postReconciliationHeaderPushSAP(idList)
      }
    },
    // CellTool
    handleClickCellTool(e) {
      if (e.tool.id === 'UploadInvoice') {
        this.goToInvoiceCollaborationUpload({
          headerInfo: e.data,
          entryType: ConstantType.Add
        })
      } else if (e.tool.id === 'SendBack') {
        // 退回
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确定退回选中的数据？')
          },
          success: () => {
            const params = {
              reconciliationId: e.data.id
            }
            this.getReconciliationInvoiceReturnById(params)
          }
        })
      } else if (e.tool.id === 'ConfirmInvoice') {
        // 采购确认
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确定采购确认选中的数据？')
          },
          success: () => {
            const params = {
              reconciliationId: e.data.id,
              invoiceStatus: InvoiceStatus.confirmed
            }
            this.postReconciliationInvoiceConfirmById(params)
          }
        })
      } else if (e.tool.id === 'ConfirmInvoiceFinance') {
        // 财务确认
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确定财务确认选中的数据？')
          },
          success: () => {
            const params = {
              reconciliationId: e.data.id,
              invoiceStatus: InvoiceStatus.confirmedFinance
            }
            this.postReconciliationInvoiceConfirmById(params)
          }
        })
      }
    },
    // 对账单外部接口-对账单推送第三方共享财务
    postReconciliationPushSharedFinance(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postReconciliationPushSharedFinance(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 对账单主单接口-对账单推送SAP
    postReconciliationHeaderPushSAP(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postReconciliationHeaderPushSAP(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 根据对账单ID确认发票
    postReconciliationInvoiceConfirmById(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postReconciliationInvoiceConfirmById(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 根据对账单ID退回发票
    getReconciliationInvoiceReturnById(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .getReconciliationInvoiceReturnById(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 验证 发票、同步 状态是否统一
    verifyInvoiceStatusAndSyncStatus(data) {
      let valid = true
      let invoiceStatus = ''
      let syncStatus = ''
      data.forEach((item, index) => {
        invoiceStatus = item.invoiceStatus
        syncStatus = item.syncStatus
        if (
          item &&
          data[index - 1] &&
          (item.invoiceStatus !== data[index - 1].invoiceStatus ||
            item.syncStatus !== data[index - 1].syncStatus)
        ) {
          valid = false
        }
      })

      return { valid, invoiceStatus, syncStatus }
    },
    // CellTitle
    handleClickCellTitle(e) {
      if (e.field === 'reconciliationCode') {
        // 对账单号 click
        this.goToInvoiceCollaborationUpload({
          headerInfo: e.data,
          entryType: ConstantType.Look
        })
      }
    },
    // 跳转到上传发票页面
    goToInvoiceCollaborationUpload(data) {
      const { headerInfo, entryType } = data
      // 上传发票 页面参数
      const params = {
        reconciliationCode: headerInfo.reconciliationCode, // 对账单号
        headerInfo,
        entryType: entryType
      }
      localStorage.setItem('reconciliationData', JSON.stringify(params))
      // 跳转到上传发票页面
      this.$router.push({
        name: 'invoice-collaboration-upload',
        query: {}
      })
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
