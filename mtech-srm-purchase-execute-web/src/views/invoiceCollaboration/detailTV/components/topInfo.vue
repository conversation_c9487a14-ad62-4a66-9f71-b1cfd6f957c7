<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div :class="[billInvoiceStatusClass(status), 'mr20']">
        {{ getStatusLabel() }}
      </div>
      <div class="infos mr20">
        {{ $t('单据号：') }}{{ headerInfo.recoCode || headerInfo.reconciliationHeaderCodes }}
      </div>
      <div class="infos mr20">{{ $t('创建人：') }}{{ headerInfo.createUserName }}</div>
      <div class="infos">{{ $t('创建时间：') }}{{ headerInfo.createTime | dateFormat }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button
        css-class="e-flat"
        v-if="entryType == ConstantType.edit"
        :is-primary="true"
        @click="doSubmit"
        >{{ $t('提交') }}</mt-button
      >
      <div v-if="(roleType === 'ALL' || roleType === 'FIN01') && status == 1">
        <mt-button css-class="e-flat" :is-primary="true" @click="handleClick('back')">{{
          $t('采购退回')
        }}</mt-button>
        <mt-button css-class="e-flat" :is-primary="true" @click="handleClick('ConfirmInvoice')">{{
          $t('采购确认')
        }}</mt-button>
      </div>
      <!-- <div v-if="(roleType === 'ALL' || roleType === 'FINAN') && status === 1">
        <mt-button css-class="e-flat" :is-primary="true" @click="handleClick('back')">{{
          $t('财务退回')
        }}</mt-button>
        <mt-button
          css-class="e-flat"
          :is-primary="true"
          @click="handleClick('ConfirmInvoiceFinance')"
          >{{ $t('财务确认') }}</mt-button
        >
      </div> -->

      <div class="sort-box" @click="doExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="companyCode" :label="$t('公司编号')">
          <mt-input
            v-model="headerInfo.companyCode"
            :disabled="true"
            :placeholder="$t('公司编号')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="companyName" :label="$t('公司名称')">
          <mt-input
            v-model="headerInfo.companyName"
            :disabled="true"
            :placeholder="$t('公司名称')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplierCode" :label="$t('供应商编号')" :show-message="false">
          <mt-input
            v-model="headerInfo.supplierCode"
            :disabled="true"
            :placeholder="$t('供应商编号')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplierName" :label="$t('供应商名称')" :show-message="false">
          <mt-input
            v-model="headerInfo.supplierName"
            :disabled="true"
            :placeholder="$t('供应商名称')"
          ></mt-input>
        </mt-form-item>

        <!-- <mt-form-item prop="rateDifferent" :label="$t('差异率')" :show-message="false">
          <mt-input
            v-model="headerInfo.rateDifferent"
            :disabled="true"
            :placeholder="$t('差异率')"
          ></mt-input>
        </mt-form-item> -->
        <mt-form-item
          prop="executionTotalPriceUntaxed"
          :label="$t('对账未税总额')"
          :show-message="false"
        >
          <mt-input
            v-model="headerInfo.executionTotalPriceUntaxed"
            :disabled="true"
            :placeholder="$t('对账未税总额')"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="taxAmount" :label="$t('对账税额')" :show-message="false">
          <mt-input
            v-model="headerInfo.taxAmount"
            :disabled="true"
            :placeholder="$t('对账税额')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="executeTaxedTotalPrice"
          :label="$t('对账含税总额')"
          :show-message="false"
        >
          <mt-input
            v-model="headerInfo.executeTaxedTotalPrice"
            :disabled="true"
            :placeholder="$t('对账含税总额')"
          ></mt-input>
        </mt-form-item> -->

        <!-- <mt-form-item prop="taxCode" :label="$t('税码')" :show-message="false">
          <mt-input
            v-model="headerInfo.taxCode"
            :disabled="true"
            :placeholder="$t('税码')"
          ></mt-input>
        </mt-form-item> -->
        <!-- <mt-form-item prop="untaxedDifferentAmount" :label="$t('未税差异')" :show-message="false">
          <mt-input
            v-model="headerInfo.untaxedDifferentAmount"
            :disabled="true"
            :placeholder="$t('未税差异')"
          ></mt-input>
        </mt-form-item> -->
        <mt-form-item
          prop="invoiceUntaxAmountSum"
          :label="$t('发票未税总额')"
          :show-message="false"
        >
          <mt-input
            v-model="headerInfo.invoiceUntaxAmountSum"
            :disabled="true"
            :placeholder="$t('发票未税总额')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="invoiceTaxAmount" :label="$t('发票税额')" :show-message="false">
          <mt-input
            v-model="headerInfo.invoiceTaxAmount"
            :disabled="true"
            :placeholder="$t('发票税额')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="invoiceTaxedAmountSum"
          :label="$t('发票含税总额')"
          :show-message="false"
        >
          <mt-input
            v-model="headerInfo.invoiceTaxedAmountSum"
            :disabled="true"
            :placeholder="$t('发票含税总额')"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="taxRate" :label="$t('税率')" :show-message="false">
          <mt-input
            v-model="headerInfo.taxRate"
            :disabled="true"
            :placeholder="$t('税率')"
          ></mt-input>
        </mt-form-item> -->
        <!-- <mt-form-item prop="taxAmountDifferent" :label="$t('税额差异')" :show-message="false">
          <mt-input
            v-model="headerInfo.taxAmountDifferent"
            :disabled="true"
            :placeholder="$t('税额差异')"
          ></mt-input>
        </mt-form-item> -->
        <!-- class="full-width" -->
        <mt-form-item prop="customerRemark" :label="$t('采方备注')" :show-message="false">
          <mt-input
            v-model="headerInfo.customerRemark"
            :disabled="true"
            :placeholder="$t('采方备注')"
          ></mt-input>
        </mt-form-item>

        <!-- class="full-width" -->
        <mt-form-item prop="supplierRemark" :label="$t('供方备注')" :show-message="false">
          <mt-input
            v-model="headerInfo.supplierRemark"
            :disabled="true"
            :placeholder="$t('供方备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
    <!-- 发票回退弹框 -->
    <send-back-dialog ref="sendBackDialog" @confirm="sendBackDialogConfirm"></send-back-dialog>
  </div>
</template>

<script>
import { InvoiceStatusCssClass, ConstantType, InvoiceStatusConst } from '../config/constant'
import utils from '@/utils/utils'
import SendBackDialog from '@/views/invoiceCollaboration/summaryTV/components/sendBackDialog.vue'
export default {
  components: {
    SendBackDialog
  },
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    },
    entryType: {
      type: String,
      default: ConstantType.look
    },
    roleType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isExpand: true,
      rules: {},
      ConstantType,
      InvoiceStatusConst
    }
  },
  computed: {
    status() {
      return this.$route.query?.status ? String(this.$route.query?.status) : ''
    },
    invoiceId() {
      return this.$route.query?.id || ''
    }
  },
  mounted() {},
  filters: {
    dateFormat(value) {
      const date = new Date(value)
      if (isNaN(date.getTime())) {
        return value
      } else {
        return utils.formateTime(date, 'YYYY-mm-dd HH:MM:SS')
      }
    }
    // billInvoiceStatusFormat(value) {
    //   if (!InvoiceStatusConst[value]) {
    //     return value
    //   } else {
    //     return InvoiceStatusConst[value]
    //   }
    // }
  },
  methods: {
    getStatusLabel() {
      const status = this.$route.query?.status
      let statusLabel = ''
      if (status || status === 0) {
        statusLabel = InvoiceStatusConst[status]
      }
      return statusLabel
    },
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 提交
    doSubmit() {
      this.$emit('doSubmit')
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    },
    // 发票状态转对应的 css class
    billInvoiceStatusClass() {
      const status = this.$route.query?.status
      let cssClass = ''
      if (InvoiceStatusCssClass[status]) {
        cssClass = InvoiceStatusCssClass[status]
      }
      return cssClass
    },
    handleClick(id) {
      if (id === 'back') {
        // 退回
        this.$refs.sendBackDialog.dialogInit({
          title: this.$t('确定退回'),
          selectData: { id: this.invoiceId, status: this.$route.query?.status }
        })
      } else if (id === 'ConfirmInvoice') {
        // 采购确认
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确定采购确认')
          },
          success: () => {
            const params = {
              id: this.invoiceId,
              pass: true
            }
            // 采方采购确认
            this.postCustomerReconciliationInvoiceV2purchaserConfirm(params)
          }
        })
      } else if (id === 'ConfirmInvoiceFinance') {
        // 财务确认
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确定财务确认？')
          },
          success: () => {
            const params = {
              id: this.invoiceId,
              pass: true
            }
            // 采方财务确认
            this.postCustomerReconciliationInvoiceV2FinanceConfirm(params)
          }
        })
      }
    },
    // 采方采购确认
    postCustomerReconciliationInvoiceV2purchaserConfirm(params) {
      this.$API.invoiceCollaboration.confirmInvoiceSupplier(params).then((res) => {
        if (res?.code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.goBack()
        }
      })
    },
    // 采方财务确认
    postCustomerReconciliationInvoiceV2FinanceConfirm(params) {
      this.$API.invoiceCollaboration
        .postCustomerReconciliationInvoiceV2FinanceConfirm(params)
        .then((res) => {
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.goBack()
          }
        })
    },
    sendBackDialogConfirm() {
      this.goBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
