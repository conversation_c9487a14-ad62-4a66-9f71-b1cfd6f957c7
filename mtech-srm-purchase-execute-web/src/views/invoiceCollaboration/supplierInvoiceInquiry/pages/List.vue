<!-- 列表视图 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('发票号')" prop="invoiceNo">
          <mt-input
            v-model="searchFormModel.invoiceNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('发票状态')" prop="invoiceStatus">
          <mt-multi-select
            v-model="searchFormModel.invoiceStatus"
            :data-source="invoiceStatusOptions"
            :show-select-all="true"
            :show-clear-button="false"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="invoiceDate" :label="$t('发票日期')">
          <mt-date-range-picker
            v-model="searchFormModel.invoiceDate"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'invoiceDate')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('发票未税总额')" prop="untaxedTotalPrice">
          <mt-input
            v-model="searchFormModel.untaxedTotalPrice"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('发票税额')" prop="taxAmt">
          <mt-input
            v-model="searchFormModel.taxAmt"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('发票币种')" prop="currencyCode">
          <mt-input
            v-model="searchFormModel.currencyCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供应商')" prop="supplierCode">
          <mt-input
            v-model="searchFormModel.supplierCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('公司编码')" prop="companyCode">
          <mt-input
            v-model="searchFormModel.companyCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('工厂')" prop="siteCode">
          <mt-input
            v-model="searchFormModel.siteCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('创建人')" prop="createUserName">
          <mt-input
            v-model="searchFormModel.createUserName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'createTime')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="5ab4f10a-3e5c-474c-9116-f8b741caa6ed"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      keep-source
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #operateDetault="{ row }">
        <span style="cursor: pointer; color: #2783fe" @click="handleDownload(row)">{{
          row.attachFileName
        }}</span>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
    <InvoiceQuery ref="invoiceQueryRef" @confirm="handleSearch" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, invoiceStatusOptions } from '../config/list'
import { getHeadersFileName, download } from '@/utils/utils'
import InvoiceQuery from '../components/InvoiceQuery.vue'

export default {
  components: { CollapseSearch, ScTable, InvoiceQuery },
  data() {
    return {
      searchFormModel: {},
      toolbar: [
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
        { code: 'manualGet', name: this.$t('手工获取'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      invoiceStatusOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleDownload(row) {
      this.$API.fileService.fileDownload(row.attachId).then((res) => {
        let link = document.createElement('a')
        link.style.display = 'none'
        let blob = new Blob([res.data], { type: 'application/x-msdownload' })
        let url = window.URL.createObjectURL(blob)
        link.href = url
        link.setAttribute('download', `${row.attachFileName}.pdf`)
        link.click()
        window.URL.revokeObjectURL(url)
      })
    },
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.invoiceCollaboration
        .pageSupplierInvoiceInquiryListApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        case 'manualGet':
          this.handleQuery()
          break
        default:
          break
      }
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$API.invoiceCollaboration
        .exportSupplierInvoiceInquiryListApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    handleQuery() {
      this.$refs.invoiceQueryRef.dialogInit({
        title: this.$t('获取供方发票'),
        actionType: 'list'
      })
    }
  }
}
</script>
