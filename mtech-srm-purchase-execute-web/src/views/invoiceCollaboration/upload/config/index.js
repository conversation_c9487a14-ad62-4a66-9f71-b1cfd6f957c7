import Vue from 'vue'
import utils from '@/utils/utils'
import { ColumnCheckbox, InvoiceTypeOptions, Tab } from './constant'
import { i18n } from '@/main.js'
import { timeDate } from './columnComponent'

// data: yyyy-mm-dd hh:mm:ss
export const timeToDate = (data) => {
  const { formatString, value } = data
  if (formatString && value) {
    const date = new Date(value)
    if (isNaN(date.getTime())) {
      return value
    } else {
      return utils.formateTime(date, formatString)
    }
  } else {
    return value
  }
}

/**
 * 选项 code 转选项 text
 * @param data: { code, options }
 * @returns "text"
 */
export const codeToText = (data) => {
  const { code, options } = data
  let text = code
  for (let i = 0; i < options.length; i++) {
    if (options[i].value == code) {
      text = options[i].text
      break
    }
  }

  return text
}

// 格式化表格动态数据
export const formatTableColumnData = (config) => {
  const { data, hasCheckBox, hasSerialNumber, isView, tab } = config
  if (tab === Tab.invoiceList) {
    const colData = []
    data.forEach((col) => {
      const defaultCol = {
        ...col,
        field: col.fieldCode,
        headerText: col.fieldName,
        width: data.length > 10 ? '150' : 'auto' // 发票清单tab 自适应
      }
      if (defaultCol.field === 'invoiceType') {
        // 发票清单-发票类型
        defaultCol.template = () => {
          return {
            template: Vue.component('actionInput', {
              template: `<div>{{data.invoiceType | codeFilter}}</div>`,
              data: function () {
                return { InvoiceTypeOptions, data: {} }
              },
              mounted() {},
              methods: {},
              filters: {
                codeFilter(value) {
                  return codeToText({
                    code: value,
                    options: InvoiceTypeOptions
                  })
                }
              }
            })
          }
        }
      } else if (defaultCol.field === 'invoiceTime') {
        // 发票清单-开票日期
        defaultCol.template = () => {
          return {
            template: Vue.component('invoiceTime', {
              template: `<div>{{data.invoiceTime | dateFormat}}</div>`,
              data: function () {
                return { data: {} }
              },
              mounted() {},
              methods: {},
              filters: {
                dateFormat(value) {
                  return timeToDate({ formatString: 'YYYY-mm-dd', value })
                },
                timeFormat(value) {
                  return timeToDate({ formatString: 'HH:MM:SS', value })
                }
              }
            })
          }
        }
      }
      colData.push(defaultCol)
    })
    if (hasSerialNumber) {
      let cellTools = undefined
      if (!isView) {
        cellTools = [
          {
            id: 'InvoiceEdit',
            icon: 'icon_Editor',
            title: i18n.t('编辑')
          },
          {
            id: 'InvoiceDelete',
            icon: 'icon_solid_Delete',
            title: i18n.t('删除')
          }
        ]
      }
      // 表格 序号
      colData.unshift({
        field: 'serialNumber',
        headerText: i18n.t('序号'),
        width: '150',
        cssClass: '',
        cellTools
      })
    }
    if (hasCheckBox) {
      // 表格第一列 checkbox
      colData.unshift({ ...ColumnCheckbox, visible: true })
    }

    return colData
  } else if (tab === Tab.reconciliationDetails) {
    const colData = []
    data.forEach((col) => {
      if (!col.checkStatus) return
      const defaultCol = {
        ...col,
        field: col.code,
        headerText: col.name,
        width: data.length > 10 ? '150' : 'auto' // 发票清单tab 自适应
      }
      if (defaultCol.field === 'receiveCode') {
        // 对账明细-物料凭证/收货单号
        defaultCol.width = '200'
      } else if (defaultCol.field === 'receiveTime') {
        // 对账明细-收货时间
        defaultCol.template = timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
      } else if (defaultCol.field === 'fileBaseInfoList') {
        // 对账明细-文件信息
        defaultCol.template = () => {
          return {
            template: Vue.component('fileBaseInfoList', {
              template: `<div @click="showFileBaseInfo" class="cell-operable-title">{{data.fileBaseInfoList | listNumFormat}}</div>`,
              data: function () {
                return { data: {} }
              },
              filters: {
                listNumFormat(value) {
                  if (value && value.length > 0) {
                    return value.length
                  } else {
                    return ''
                  }
                }
              },
              methods: {
                showFileBaseInfo() {
                  this.$parent.$emit('showFileBaseInfo', {
                    index: this.data.index,
                    value: this.data.fileBaseInfoList
                  })
                }
              }
            })
          }
        }
      } else if (defaultCol.field === 'termsUntaxedUnitPrice') {
        // 对账明细-条件（未税）单价
        defaultCol.width = '200'
      } else if (defaultCol.field === 'sourceType') {
        // 来源类型（枚举） 0: 上游流入1:第三方接口
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('上游流入'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('第三方接口'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'status') {
        // 单据状态 0:待对账 1:已创建对账单
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('待对账'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('已创建对账单'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'syncStatus') {
        // 同步状态 0:否 1:是
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('否'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('是'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'advanceInvoicing') {
        // 提前开票 0:否 1:是
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('否'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('是'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'frozenStatus') {
        // 冻结标记 0:否 1:是
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('否'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('是'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'prePayStatus') {
        // 是否预付 0-否；1-是
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('否'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('是'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'itemVoucherDate') {
        // 物料凭证日期
        defaultCol.template = timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
      } else if (defaultCol.field === 'provisionalEstimateStatus') {
        // 是否暂估价 0-否；1-是 => 改成是否执行价 取值相反
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('是'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('否'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'createTime') {
        // 创建时间
        defaultCol.template = timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
      } else if (defaultCol.field === 'updateTime') {
        // 最后修改时间
        defaultCol.template = timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
      } else if (defaultCol.field === 'type') {
        // 待对账类型:0-采购待对账；1-销售待对账
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('采购待对账'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('销售待对账'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'realPriceStatus') {
        // 正式价标识 0-无正式价；1-有正式价
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('无正式价'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('有正式价'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'inOutType') {
        // 出入库类型 0-采购入库；1-采购出库；2-销售出库；3-销售退回
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('采购入库'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('采购出库'),
              cssClass: ''
            },
            {
              value: 2,
              text: i18n.t('销售出库'),
              cssClass: ''
            },
            {
              value: 3,
              text: i18n.t('销售退回'),
              cssClass: ''
            }
          ]
        }
      }
      colData.push(defaultCol)
    })

    return colData
  }
}
