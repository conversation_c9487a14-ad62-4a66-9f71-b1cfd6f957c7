<template>
  <!-- 发票协同（采方）上传发票 -->
  <div class="full-height pt20 vertical-flex-box">
    <!-- 头部信息 -->
    <top-info
      class="flex-keep"
      :entry-type="entryType"
      :header-info="headerInfo"
      @doSubmit="doSubmit"
      @goBack="goBack"
      @doExpand="doExpand"
    ></top-info>
    <mt-tabs :e-tab="false" :data-source="tabList" @handleSelectTab="handleSelectTab"></mt-tabs>
    <!-- 列模板 -->
    <div class="flex-fit" v-show="currentTabInfo.code !== TabCode.reconciliationFile">
      <mt-template-page
        ref="templateRef"
        v-show="isShowTemplatePage"
        :hidden-tabs="true"
        :current-tab="currentTab"
        :template-config="componentConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @showFileBaseInfo="showFileBaseInfo"
      >
      </mt-template-page>
    </div>
    <!-- 发票清单底部数据 -->
    <div class="amount-total" v-show="currentTabInfo.code === TabCode.invoiceList">
      <div class="item">
        {{ $t('发票总金额（未税）：') }}{{ untaxedAmountTotal }}
        {{ $t('人民币') }}
      </div>
      <div class="item">
        {{ $t('发票总金额（含税）：') }}{{ includingTaxAmountTotal }}
        {{ $t('人民币') }}
      </div>
    </div>
    <!-- 相关附件 -->
    <div class="flex-fit" v-show="currentTabInfo.code === TabCode.reconciliationFile">
      <relative-file
        ref="relativeFileRef"
        @initData="relativeFileInitData"
        :module-file-list="moduleFileList"
      ></relative-file>
    </div>
    <!-- 附件弹窗 -->
    <uploader-dialog ref="uploaderDialog"></uploader-dialog>
    <!-- 发票填写弹框 -->
    <invoice-list-dialog
      ref="invoiceListDialog"
      @confirm="invoiceDialogConfirm"
    ></invoice-list-dialog>
    <!-- 税控平台获取发票弹框 -->
    <invoice-table-dialog
      ref="invoiceTableDialog"
      @confirm="invoiceTableDialogConfirm"
    ></invoice-table-dialog>
    <!-- 配置导入弹框 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-name="downTemplateName"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { formatTableColumnData } from './config/index.js'
import { invoiceDataSource } from './config/variable'
import {
  ColumnCheckbox,
  InvoiceColumnData,
  DialogActionType,
  ConstantType,
  Tab,
  TabCode
} from './config/constant'
import { BASE_TENANT } from '@/utils/constant'
import { nodeType } from '@/components/businessComponents/relativeFileNoDocId/config/relative.js'
import { isEqual, cloneDeep } from 'lodash'
import { download, getHeadersFileName } from '@/utils/utils'
import bigDecimal from 'js-big-decimal'

export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue'),
    InvoiceListDialog: () => import('./components/invoiceListDialog'),
    InvoiceTableDialog: () => import('./components/invoiceTableDialog'),
    RelativeFile: () => import('@/components/businessComponents/relativeFileNoDocId/index.vue'),
    UploaderDialog: () => import('@/components/Upload/uploaderDialog'),
    UploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    const reconciliationData = JSON.parse(localStorage.getItem('reconciliationData'))
    const { entryType, headerInfo } = reconciliationData

    return {
      tabList: [],
      TabCode,
      currentTabInfo: {}, // 当前tab的数据
      downTemplateName: this.$t('发票导入模板'),
      downTemplateParams: {
        pageFlag: false
      }, // 发票导入下载模板参数
      uploadParams: { headerId: headerInfo?.id }, // 发票导入文件参数
      // 发票导入请求接口配置
      requestUrls: {
        templateUrlPre: 'invoiceCollaboration',
        templateUrl: 'postDownloadInvoice', // 下载模板接口方法名
        uploadUrl: 'postReconciliationInvoiceUpload' // 上传接口方法名
      },
      currentTab: 0, // 当前列模板显示的 Tab
      invoiceFilesInit: [], // 发票附件的初始数据
      invoicesInit: [], // 发票列表的初始数据
      moduleFileList: [],
      deleteInvoiceIdList: [], // 删除的发票id
      entryType: entryType, // 页面状态
      apiWaitingQuantity: 0, // 调用的api正在等待数
      headerInfo: headerInfo, // 顶部的数据
      untaxedAmountTotal: 0, // 发票总金额（未税）
      includingTaxAmountTotal: 0, // 发票总金额（含税）
      isShowTemplatePage: false, // 初始化不显示
      componentConfig: []
    }
  },
  mounted() {
    // 获取发票清单表格数据
    const params = {
      reconciliationId: this.headerInfo.id // 对账单id
    }
    this.getReconciliationInvoiceQueryList(params)
    // 处理列模板的配置
    this.setTemplateConfig()
  },
  beforeDestroy() {
    invoiceDataSource.length = 0 // 清空 发票表格 数据
    localStorage.removeItem('reconciliationData')
  },
  methods: {
    // 处理列模板的配置
    setTemplateConfig() {
      // 获取动态表头
      const params = {
        businessTypeCode: this.headerInfo?.businessTypeCode,
        reconciliationTypeCode: this.headerInfo?.reconciliationTypeCode // 对账类型编码
      }
      this.apiStartLoading()
      // 获取 采购对账字段配置
      this.$API.reconciliationSettlement
        .postReconciliationConfigFieldQuery(params)
        .then((res) => {
          this.apiEndLoading()
          const dataList = res?.data || []
          const configList = []

          dataList.forEach((itemTab) => {
            if (itemTab.code === TabCode.reconciliationField && itemTab.checkStatus) {
              // 对账明细
              configList.push(this.formatDetail(itemTab))
            } else if (itemTab.code === TabCode.reconciliationFile && itemTab.checkStatus) {
              // 相关附件
              configList.push(this.formatFile(itemTab))
            }
          })

          // 发票清单
          configList.push(this.formatInvoiceList())
          this.currentTabInfo = this.tabList[0] // 选中第一个Tab

          this.componentConfig = configList // 设置列模板
          this.isShowTemplatePage = true // 显示表格
        })
        .catch(() => {
          this.isShowTemplatePage = false // 不显示表格
          this.apiEndLoading()
        })
    },

    // 整合对账明细
    formatDetail(itemTab) {
      this.tabList.push({
        title: itemTab.name,
        code: TabCode.reconciliationField
      })
      const cols = formatTableColumnData({
        tab: Tab.reconciliationDetails,
        data: itemTab.fieldResponseList || []
      })

      return {
        tab: { title: itemTab.name },
        useToolTemplate: false, // 不使用预置(新增、编辑、删除)
        useBaseConfig: true, // 使用组件中的toolbar配置
        toolbar: [],
        grid: {
          columnData: [ColumnCheckbox].concat(cols),
          dataSource: [],
          asyncConfig: {
            url: `${BASE_TENANT}/reconciliationItem/queryBuilder?BU_CODE=${localStorage.getItem(
              'currentBu'
            )}`, // queryBuilder查询-对账明细信息
            defaultRules: [
              {
                field: 'headerId',
                operator: 'equal',
                value: this.headerInfo?.id
              }
            ]
          }
        }
      }
    },

    // 整合 相关附件
    formatFile(itemTab) {
      this.tabList.push({
        title: itemTab.name,
        code: TabCode.reconciliationFile
      })
      // 采方-行附件
      this.moduleFileList.push({
        type: nodeType.itemView,
        id: 'reconciliation_item', // 选中时传给api的值
        // 根据待对账id获取相关文件
        url: `${BASE_TENANT}/reconciliationHeader/queryFileByDocIdAndDocType`, // 获取附件列表的url 根据docType和docI查询所有文件信息
        methods: 'get',
        params: {
          docId: this.headerInfo?.id,
          doctype: 'reconciliation_item'
        },
        nodeName: this.$t('采方-行附件') // 侧边栏名称
      })

      // 动态配置的文件类型
      itemTab.fieldResponseList.forEach((item) => {
        if (item.code === TabCode.purHeaderFile && item.checkStatus) {
          // 采方-整单附件
          this.moduleFileList.push({
            type: nodeType.mainView,
            id: 'reconciliation_header',
            // 根据待对账id获取相关文件
            url: `${BASE_TENANT}/reconciliationHeader/queryFileByDocIdAndDocType`, // 获取附件列表的url 根据docType和docI查询所有文件信息
            methods: 'get',
            params: {
              docId: this.headerInfo?.id,
              doctype: 'reconciliation_header'
            },
            nodeName: item.name
          })
        } else if (item.code === TabCode.supHeaderFile && item.checkStatus) {
          // 供方-整单附件
          this.moduleFileList.push({
            type: nodeType.mainView,
            id: 'reconciliation_header_sup',
            // 根据待对账id获取相关文件
            url: `${BASE_TENANT}/reconciliationHeader/queryFileByDocIdAndDocType`, // 获取附件列表的url 根据docType和docI查询所有文件信息
            methods: 'get',
            params: {
              docId: this.headerInfo?.id,
              doctype: 'reconciliation_header_sup'
            },
            nodeName: this.$t('供方-整单附件')
          })
        }
      })

      // 对账单发票
      this.moduleFileList.push({
        type: this.entryType === ConstantType.Add ? nodeType.mainUpdateEdit : nodeType.mainView,
        id: 'reconciliation_invoice',
        // 根据待对账id获取相关文件
        url: `${
          this.entryType === ConstantType.Add ? BASE_TENANT : BASE_TENANT
        }/reconciliationHeader/queryFileByDocIdAndDocType`, // 获取附件列表的url 根据docType和docI查询所有文件信息
        methods: 'get',
        params: {
          docId: this.headerInfo?.id,
          doctype: 'reconciliation_invoice'
        },
        nodeName: this.$t('对账单发票')
      })

      return {
        tab: { title: itemTab.name }
      }
    },

    // 整合 发票清单
    formatInvoiceList() {
      this.tabList.push({
        title: this.$t('发票清单'),
        code: TabCode.invoiceList
      })
      let tools = [] // 发票清单表头按钮
      if (this.entryType === ConstantType.Add) {
        tools = [
          [
            {
              id: 'InvoiceAdd',
              icon: 'icon_solid_Createproject',
              title: this.$t('新增')
            },
            {
              id: 'InvoiceDelete',
              icon: 'icon_table_delete',
              title: this.$t('删除')
            },
            {
              id: 'GetInvoice',
              icon: 'icon_table_acquire',
              title: this.$t('税控平台获取')
            },
            {
              id: 'InvoiceImport',
              icon: 'icon_solid_Import',
              title: this.$t('导入')
            }
          ]
        ]
      } else if (this.entryType === ConstantType.Look) {
        // 查看状态
        tools = [
          [
            {
              id: 'InvoiceExport',
              icon: 'icon_solid_export',
              title: this.$t('导出')
            }
          ]
        ]
      }

      return {
        tab: { title: this.$t('发票清单') },
        useToolTemplate: false, // 不使用预置(新增、编辑、删除)
        useBaseConfig: false, // 使用组件中的toolbar配置
        toolbar: tools,
        grid: {
          allowPaging: false,
          columnData: formatTableColumnData({
            tab: Tab.invoiceList,
            data: InvoiceColumnData,
            hasCheckBox: this.entryType != ConstantType.Look ?? true,
            hasSerialNumber: true,
            isView: this.entryType == ConstantType.Look ?? false
          }),
          dataSource: invoiceDataSource
          // frozenColumns: this.entryType != ConstantType.Look ? 1 : 0, // 不固定列，固定列的话新增时高度会出错
        }
      }
    },

    // 显示表格文件弹窗
    showFileBaseInfo(e) {
      const dialogParams = {
        fileData: cloneDeep(e.value),
        isView: true,
        title: this.$t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },
    // 获取发票文件初始数据
    relativeFileInitData(e) {
      if (e.index == 3) {
        this.invoiceFilesInit = this.formatUploadFiles(e.data)
      }
    },
    doSubmit() {
      const file = this.diffInvoiceFilesList()
      // const invoiceList = this.diffInvoices();
      const invoiceList = invoiceDataSource

      // 格式转换
      invoiceList.forEach((item) => {
        item.invoiceType = Number(item.invoiceType) // 发票类型转换数据类型
      })

      const params = {
        deleteFileIdList: file.deleteInvoiceFilesSysFileIds, // 删除发票文件sysFileId列表
        deleteInvoiceIdList: this.deleteInvoiceIdList, // 删除发票id列表
        headerId: this.headerInfo.id, // 对账单ID
        fileList: file.addInvoiceFiles, //	文件列表
        invoiceList // 发票列表
      }
      // 提交对账发票信息
      this.putReconciliationInvoiceCommitInvoice(params)
    },
    // 对比发票清单，过滤出新增的和修改的
    diffInvoices() {
      const invoiceList = invoiceDataSource.filter((item) => {
        let isNeed = false

        if (item.addId) {
          // 新增的
          isNeed = true
        } else {
          for (let i = 0; i < this.invoicesInit.length; i++) {
            if (this.invoicesInit[i].id === item.id && !isEqual(this.invoicesInit[i], item)) {
              // 修改了的
              isNeed = true
              break
            }
          }
        }

        return isNeed
      })

      return invoiceList
    },
    diffInvoiceFilesList() {
      const invoiceFilesInitSysFileIds = [] // 发票初始 sysFileId 列表
      const invoiceFilesSysFileIds = [] // 发票表格 sysFileId 列表

      this.invoiceFilesInit.forEach((item) => {
        invoiceFilesInitSysFileIds.push(item.sysFileId)
      })
      // 获取发票表格数据
      let invoiceFiles = this.$refs.relativeFileRef.getUploadFlies('reconciliation_invoice')
      invoiceFiles = this.formatUploadFiles(invoiceFiles)
      invoiceFiles.forEach((item) => {
        invoiceFilesSysFileIds.push(item.sysFileId)
      })
      // 被删除的发票文件 sysFileId 列表
      const deleteInvoiceFilesSysFileIds = invoiceFilesInitSysFileIds.filter(
        (item) => !invoiceFilesSysFileIds.includes(item)
      )
      // 新增的发票文件 sysFileId 列表
      const addInvoiceFilesSysFileIds = invoiceFilesInitSysFileIds
        .concat(invoiceFilesSysFileIds)
        .filter((item) => !invoiceFilesInitSysFileIds.includes(item))
      // 新增的发票文件列表
      const addInvoiceFiles = invoiceFiles.filter((item) =>
        addInvoiceFilesSysFileIds.includes(item.sysFileId)
      )

      return {
        deleteInvoiceFilesSysFileIds,
        addInvoiceFiles
      }
    },
    // 格式化附件信息
    formatUploadFiles(list) {
      const tmp = []
      list.forEach((item) => {
        tmp.push({
          fileName: item.fileName, //	文件上传名称
          fileSize: item.fileSize, //	文件大小
          fileType: item.fileType, //	文件类型
          sysFileId: item.id || item.sysFileId, //	文件ID(MT_WP_SYS_FILE表ID)
          url: item.url //	文件路径
        })
      })

      return tmp
    },
    // 提交对账发票信息
    putReconciliationInvoiceCommitInvoice(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .putReconciliationInvoiceCommitInvoice(params)
        .then((res) => {
          this.apiEndLoading()
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 返回 发票协同-列表
            this.goBack()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 发票计算
    putReconciliationInvoiceInvoiceCalculation() {
      const params = []
      invoiceDataSource.forEach((item) => {
        params.push({
          taxAmount: Number(item.taxAmount), //	税额
          taxedAmount: Number(item.taxedAmount), //	发票含税金额
          untaxedAmount: Number(item.untaxedAmount) //	发票未税金额
        })
      })
      // this.apiStartLoading();
      this.$API.invoiceCollaboration
        .putReconciliationInvoiceInvoiceCalculation(params)
        .then((res) => {
          // this.apiEndLoading();
          if (res?.data) {
            this.untaxedAmountTotal = res.data.untaxedAmount // 未税
            this.includingTaxAmountTotal = res.data.taxedAmount // 含税
            // 在上传发票时计算 含税发票差额
            this.calculateTaxInvoiceBalance()
          }
        })
        .catch(() => {
          // this.apiEndLoading();
        })
    },
    // 在上传发票时计算 含税发票差额
    calculateTaxInvoiceBalance() {
      if (this.entryType === ConstantType.Add) {
        // 含税发票差额 taxInvoiceBalance = 执行含税总价 executeTaxedTotalPrice - 发票总金额（含税） includingTaxAmountTotal
        this.headerInfo.taxInvoiceBalance = bigDecimal.subtract(
          this.headerInfo.executeTaxedTotalPrice,
          this.includingTaxAmountTotal
        )
      }
    },
    // 查询对账发票信息
    getReconciliationInvoiceQueryList(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .getReconciliationInvoiceQueryList(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.data) {
            let serialNumber = 1 // 序号
            res?.data.forEach((item) => {
              invoiceDataSource.push({
                ...item,
                invoiceType: String(item.invoiceType), // 发票类型转换数据类型
                serialNumber
              }) // 表格数据
              serialNumber++
            })
            this.invoicesInit = cloneDeep(invoiceDataSource)
            if (invoiceDataSource.length > 0) {
              const params = []
              invoiceDataSource.forEach((item) => {
                params.push({
                  taxAmount: Number(item.taxAmount), //	税额
                  taxedAmount: Number(item.taxedAmount), //	发票含税金额
                  untaxedAmount: Number(item.untaxedAmount) //	发票未税金额
                })
              })
              // 更新金额
              this.putReconciliationInvoiceInvoiceCalculation(params)
            }
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    goBack() {
      // 返回 发票协同-列表
      this.$router.push({
        name: 'invoice-collaboration-summary'
      })
    },
    doExpand() {
      this.$refs.templateRef?.resetGridHeight && this.$refs.templateRef.resetGridHeight()
    },
    // ToolBar
    handleClickToolBar(e) {
      const selectRows = e.grid.getSelectedRecords()
      const commonToolbar = [
        'Add',
        'Filter',
        'Refresh',
        'Setting',
        'InvoiceAdd',
        'GetInvoice',
        'InvoiceImport',
        'InvoiceExport',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]
      if (selectRows.length === 0 && !commonToolbar.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedId = []
      selectRows.forEach((item) => selectedId.push({ id: item.id, addId: item.addId }))

      if (e.toolbar.id === 'InvoiceAdd') {
        // 发票清单 新增
        this.invoiceAdd()
      } else if (e.toolbar.id === 'InvoiceDelete') {
        // 发票清单 删除
        this.handleInvoiceDelete(selectedId)
      } else if (e.toolbar.id === 'GetInvoice') {
        // 发票清单 税控平台获取
        this.$refs.invoiceTableDialog.dialogInit({
          title: this.$t('税控平台获取'),
          headerInfo: this.headerInfo
        })
      } else if (e.toolbar.id === 'InvoiceImport') {
        // 导入
        this.showUploadExcel(true)
      } else if (e.toolbar.id === 'InvoiceExport') {
        // 导出
        this.postDownloadInvoice()
      }
    },
    // 采方发票下载（导出）（导入的文件模板）
    postDownloadInvoice() {
      const params = {
        pageFlag: true,
        page: { current: 1, size: 10000 },
        defaultRules: [
          {
            field: 'headerId',
            operator: 'contains',
            value: this.headerInfo?.id
          }
        ]
      } // 筛选条件
      this.apiStartLoading()
      this.$API.invoiceCollaboration.postDownloadInvoice(params).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 发票清单-新增
    invoiceAdd() {
      // 显示 发票清单 新增 弹框
      this.$refs.invoiceListDialog.dialogInit({
        title: this.$t('新增'),
        actionType: DialogActionType.Add
      })
    },
    // CellTool
    handleClickCellTool(e) {
      if (e.tool.id === 'InvoiceDelete') {
        // 发票清单 删除
        this.handleInvoiceDelete([{ id: e.data.id, addId: e.data.addId }])
      } else if (e.tool.id === 'InvoiceEdit') {
        // 发票清单 编辑
        this.invoiceEdit(e.data)
      }
    },
    // 发票清单 删除
    handleInvoiceDelete(idList) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          idList.forEach((itemId) => {
            for (let i = 0; i < invoiceDataSource.length; i++) {
              if (
                (itemId.id !== undefined && invoiceDataSource[i].id == itemId.id) ||
                (itemId.addId !== undefined && invoiceDataSource[i].addId == itemId.addId)
              ) {
                if (itemId.id) {
                  this.deleteInvoiceIdList.push(itemId.id)
                }
                invoiceDataSource.splice(i, 1)
                break
              }
            }
          })
        }
      })
    },
    // 发票清单-编辑
    invoiceEdit(row) {
      this.$refs.invoiceListDialog.dialogInit({
        title: this.$t('编辑'),
        selectData: row,
        actionType: DialogActionType.Edit
      })
    },
    // 发票清单弹框 点击确认
    invoiceDialogConfirm(e) {
      const { data, actionType } = e
      if (actionType === DialogActionType.Edit) {
        // 更新表格数据
        for (let i = 0; i < invoiceDataSource.length; i++) {
          if (
            (invoiceDataSource[i].id && invoiceDataSource[i].id === data.id) ||
            (invoiceDataSource[i].addId && invoiceDataSource[i].addId === data.addId)
          ) {
            invoiceDataSource.splice(i, 1, data)
            break
          }
        }
      } else if (actionType === DialogActionType.Add) {
        // 新增表格数据
        const serialNumber = invoiceDataSource.length + 1 // 序号
        invoiceDataSource.push({
          ...data,
          addId: 'add' + Math.random().toString(36).substr(3, 8),
          headerId: this.headerInfo.id, // 主单ID
          serialNumber
        })
      }
      // 更新金额
      this.putReconciliationInvoiceInvoiceCalculation()
    },
    // 税控平台获取弹框 点击确认
    invoiceTableDialogConfirm(e) {
      const { data: selectedInvoices } = e
      this.updateOrChangeInvoiceListByApiData(selectedInvoices)
    },
    // 显示隐藏上传弹框
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 上传发票成功后，获取到数据
    upExcelConfirm(res) {
      this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      let apiData = res?.data || []
      this.updateOrChangeInvoiceListByApiData(apiData)
    },
    // 更新或修改发票清单的数据，通过API获取的数据
    updateOrChangeInvoiceListByApiData(apiData) {
      if (invoiceDataSource.length === 0) {
        // 发票清单中 没有发票，新增
        apiData.forEach((itemInvoice) => {
          const serialNumber = invoiceDataSource.length + 1 // 序号
          invoiceDataSource.push({
            ...itemInvoice,
            invoiceType: String(itemInvoice.invoiceType), // 发票类型转换数据类型
            addId: 'add' + Math.random().toString(36).substr(3, 8),
            headerId: this.headerInfo.id, // 主单ID
            serialNumber
          })
        })
      } else {
        // 发票清单中 有发票，以发票号 invoiceNum 校验是新增/修改
        apiData.forEach((itemInvoice) => {
          for (let i = 0; i < invoiceDataSource.length; i++) {
            if (invoiceDataSource[i].invoiceNum == itemInvoice.invoiceNum) {
              // 发票清单中 存在 所选的发票号，修改
              const listInvoice = invoiceDataSource[i]
              invoiceDataSource.splice(i, 1, {
                ...listInvoice,
                invoiceType: String(itemInvoice.invoiceType), // 发票类型
                invoiceCode: itemInvoice.invoiceCode, // 发票代码
                invoiceNum: itemInvoice.invoiceNum, // 发票号
                invoiceTime: itemInvoice.invoiceTime, // 开票日期
                untaxedAmount: itemInvoice.untaxedAmount, // 未税金额
                taxAmount: itemInvoice.taxAmount, // 税额
                taxedAmount: itemInvoice.taxedAmount // 含税
              })
              break
            } else if (
              invoiceDataSource[i].invoiceNum != itemInvoice.invoiceNum &&
              i === invoiceDataSource.length - 1
            ) {
              // 发票清单中 不存在 所选的发票号，新增
              const serialNumber = invoiceDataSource.length + 1 // 序号
              invoiceDataSource.push({
                ...itemInvoice,
                invoiceType: String(itemInvoice.invoiceType), // 发票类型
                addId: 'add' + Math.random().toString(36).substr(3, 8),
                headerId: this.headerInfo.id, // 主单ID
                serialNumber
              })
            }
          }
        })
      }
      // 更新金额
      this.putReconciliationInvoiceInvoiceCalculation()
    },
    // 点击 tab
    handleSelectTab(e) {
      this.currentTabInfo = this.tabList[e]
      this.currentTab = e
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.amount-total {
  text-align: right;
  padding: 30px 15px;

  .item {
    margin-bottom: 10px;
  }
}

.mt-tabs {
  width: 100%;
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
</style>
