<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-template-page ref="templateRef" :template-config="componentConfig" :hidden-tabs="true" />
  </mt-dialog>
</template>

<script>
import { formatTableColumnData } from '../config/index.js'
import { InvoiceTableColumnData } from '../config/constant'
import { invoiceTableDataSource } from '../config/variable'

export default {
  data() {
    return {
      dialogTitle: '',
      apiWaitingQuantity: 0, // 调用的api正在等待数
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 不使用组件中的toolbar配置
          toolbar: [],
          grid: {
            allowPaging: false, // 不分页
            columnData: formatTableColumnData({
              hasCheckBox: true,
              data: InvoiceTableColumnData
            }),
            dataSource: invoiceTableDataSource,
            frozenColumns: 1
          }
        }
      ]
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      const { title, headerInfo } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.postInvoiceGetInvoiceFromItbc(headerInfo)
      this.$refs.dialog.ejsRef.show()
    },
    // 税控平台获取数据
    postInvoiceGetInvoiceFromItbc(headerInfo) {
      const params = {
        supplierCode: headerInfo.supplierCode,
        companyCode: headerInfo.companyCode
      }
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postInvoiceGetInvoiceFromItbc(params)
        .then((res) => {
          this.apiEndLoading()
          if (res.code == 200) {
            invoiceTableDataSource.length = 0
            const dataList = res.data || []
            dataList.forEach((item) => {
              invoiceTableDataSource.push(item)
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    onOpen(args) {
      args.preventFocus = true
    },

    confirm() {
      const selectedRowData = this.$refs.templateRef
        .getCurrentUsefulRef()
        .gridRef.getMtechGridRecords()
      this.$emit('confirm', { data: selectedRowData })
      this.handleClose()
    },

    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
