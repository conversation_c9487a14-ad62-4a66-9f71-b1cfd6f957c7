<!-- 发票清单 -->
<template>
  <div class="vertical-flex-box">
    <div class="flex-fit">
      <sc-table
        ref="detailSctableRef"
        :grid-id="gridId"
        :loading="loading"
        :columns="columns"
        :table-data="tableData"
        :edit-config="editConfig"
        :edit-rules="editRules"
        show-overflow
        keep-source
        :is-show-refresh-bth="true"
        @refresh="getTableData"
        @edit-closed="editComplete"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :loading="item.loading"
            size="small"
            @click="handleClickToolBar(item)"
          >
            {{ item.name }}
          </vxe-button>
        </template>
      </sc-table>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import ScTable from '@/components/ScTable/src/index'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  name: 'SupplierDetail',
  props: {
    isSup: {
      type: Boolean,
      default: false
    },
    canEdit: {
      type: Boolean,
      default: true
    }
  },
  components: { ScTable },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      loading: false,
      tableData: [],

      arriveMonthOptions: [
        { label: '01', value: '01' },
        { label: '02', value: '02' },
        { label: '03', value: '03' },
        { label: '04', value: '04' },
        { label: '05', value: '05' },
        { label: '06', value: '06' },
        { label: '07', value: '07' },
        { label: '08', value: '08' },
        { label: '09', value: '09' },
        { label: '10', value: '10' },
        { label: '11', value: '11' },
        { label: '12', value: '12' }
      ]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.detailSctableRef.$refs.xGrid
    },
    gridId() {
      let id = 'abb7c973-90d5-4065-beec-55eb0eeb8d82'
      if (this.isSup) {
        id = '8db0ac04-d0e1-4c6c-9949-92a75d7272ae'
      }
      return id
    },
    toolbar() {
      let btns = []
      if (this.isSup) {
        if (this.canEdit) {
          btns = [
            { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
            { code: 'save', name: this.$t('保存'), status: 'info', loading: false },
            { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
            { code: 'import', name: this.$t('导入'), status: 'info', loading: false }
          ]
        }
      } else {
        btns = [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }]
      }
      return btns
    },
    columns() {
      let column = [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'reconciliationHeaderCodes',
          title: this.$t('单据号'),
          minWidth: 140
        },
        {
          field: 'invoiceTime',
          title: this.$t('开票日期'),
          minWidth: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.invoiceTime}
                  type='date'
                  editable={false}
                  transfer
                  label-format='yyyy-MM-dd'
                  value-format='yyyy-MM-dd'
                />
              ]
            }
          }
        },
        {
          field: 'invoiceNum',
          title: this.$t('发票号'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.invoiceNum} clearable placeholder={this.$t('请输入')} />
              ]
            }
          }
        },
        {
          field: 'invoiceUntaxAmount',
          title: this.$t('发票未税金额'),
          minWidth: 130,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.invoiceUntaxAmount}
                  type='number'
                  clearable
                  placeholder={this.$t('请输入')}
                  onChange={() => {
                    row.invoiceTaxAmount =
                      0 - Number(row.invoiceUntaxAmount) + Number(row.invoiceTaxedAmount)
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'invoiceTaxAmount',
          title: this.$t('税额'),
          minWidth: 130,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.invoiceTaxAmount} disabled />]
            }
          }
        },
        {
          field: 'invoiceTaxedAmount',
          title: this.$t('发票含税金额'),
          minWidth: 145,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.invoiceTaxedAmount}
                  type='number'
                  clearable
                  placeholder={this.$t('请输入')}
                  onChange={() => {
                    row.invoiceTaxAmount =
                      0 - Number(row.invoiceUntaxAmount) + Number(row.invoiceTaxedAmount)
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'factoryCode',
          title: this.$t('工厂编码'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.factoryCode} clearable placeholder={this.$t('请输入')} />
              ]
            }
          }
        },
        {
          field: 'arriveYear',
          title: this.$t('到货年份'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.arriveYear} clearable placeholder={this.$t('请输入')} />
              ]
            }
          }
        },
        {
          field: 'arriveMonth',
          title: this.$t('到货月份'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.arriveMonth}
                  options={this.arriveMonthOptions}
                  option-props={{ label: 'label', value: 'value' }}
                  clearable
                  transfer
                  placeholder={this.$t('请选择')}
                />
              ]
            }
          }
        },
        {
          field: 'salesGoodsListQty',
          title: this.$t('销货清单份数'),
          minWidth: 145,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.salesGoodsListQty}
                  type='integer'
                  clearable
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'salerTaxPayerIc',
          title: this.$t('销方纳税人识别码'),
          minWidth: 170,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.salerTaxPayerIc}
                  clearable
                  placeholder={this.$t('请输入')}
                />
              ]
            }
          }
        },
        {
          field: 'invoiceCode',
          title: this.$t('发票代码'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.invoiceCode} clearable placeholder={this.$t('请输入')} />
              ]
            }
          }
        },
        {
          field: 'checkCode',
          title: this.$t('后六位校验码'),
          minWidth: 140,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.checkCode} clearable placeholder={this.$t('请输入')} />
              ]
            }
          }
        },
        {
          field: 'supplierRemark',
          title: this.$t('供方备注'),
          minWidth: 120,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.supplierRemark} clearable placeholder={this.$t('请输入')} />
              ]
            }
          }
        }
      ]
      return column
    },
    editConfig() {
      return {
        enabled: this.canEdit,
        trigger: 'click',
        mode: 'row',
        showStatus: true,
        beforeEditMethod: this.beforeEditMethod
      }
    },
    editRules() {
      let rules = {
        invoiceTime: [{ required: true, message: this.$t('必填') }],
        invoiceNum: [{ required: true, message: this.$t('必填') }],
        invoiceUntaxAmount: [{ required: true, message: this.$t('必填') }],
        invoiceTaxedAmount: [{ required: true, message: this.$t('必填') }],
        arriveYear: [{ required: true, message: this.$t('必填') }],
        arriveMonth: [{ required: true, message: this.$t('必填') }],
        salesGoodsListQty: [{ required: true, message: this.$t('必填') }],
        salerTaxPayerIc: [{ required: true, message: this.$t('必填') }]
      }
      if (this.$route?.query?.currentIndex === 3) {
        // 外发发票
        rules = {
          invoiceTime: [{ required: true, message: this.$t('必填') }],
          invoiceNum: [{ required: true, message: this.$t('必填') }],
          invoiceUntaxAmount: [{ required: true, message: this.$t('必填') }],
          invoiceTaxedAmount: [{ required: true, message: this.$t('必填') }],
          factoryCode: [{ required: true, message: this.$t('必填') }],
          arriveYear: [{ required: true, message: this.$t('必填') }],
          arriveMonth: [{ required: true, message: this.$t('必填') }],
          salesGoodsListQty: [{ required: true, message: this.$t('必填') }],
          salerTaxPayerIc: [{ required: true, message: this.$t('必填') }]
        }
      }
      return rules
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    beforeEditMethod() {
      if (!this.isSup) {
        return false
      }
      return true
    },
    async getTableData() {
      const params = {
        id: this.$route?.query?.id
      }
      let api = this.$API.invoiceCollaboration.listExternalInvoiceApi
      this.loading = true
      const res = await api(params).catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const records = res?.data || []
        this.tableData = records.map((item) => {
          return {
            ...item
          }
        })
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (['delete'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'save':
          break
        case 'delete':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除?')
            },
            success: () => {
              this.handleDelete(selectedRecords)
            }
          })
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleAdd() {
      const item = {}
      this.tableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.tableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }
          this.handleSave(row)
        })
      }
    },
    handleSave(row) {
      this.tableRef.validate([row]).then(async (valid) => {
        if (valid) {
          this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
          return
        }
        let params = { ...row }
        if (params.id?.includes('row_')) {
          params.id = null
        }
        let api = this.$API.invoiceCollaboration.addExternalInvoiceApi
        if (params.id) {
          api = this.$API.invoiceCollaboration.editExternalInvoiceApi
        }
        params.reconciliationHeaderIds = this.$route?.query?.id
        params.invoiceUntaxAmount = Number(params.invoiceUntaxAmount)
        params.invoiceTaxedAmount = Number(params.invoiceTaxedAmount)
        params.invoiceTime = dayjs(params.invoiceTime).valueOf()
        params.reconciliationType = this.$route?.query?.currentIndex === 3 ? 1 : 2
        const res = await api(params)
        if (res.code === 200) {
          this.$toast({ content: this.$t('保存成功'), type: 'success' })
          this.getTableData()
        }
      })
    },
    handleDelete(selectedRecords) {
      let rows = selectedRecords.filter((v) => {
        if (!v?.id?.includes('row_')) {
          return v
        }
      })
      if (rows.length !== 0) {
        let idList = rows.map((v) => v.id)
        this.$API.invoiceCollaboration.deleteExternalInvoiceApi({ idList }).then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('删除成功'), type: 'success' })
            this.getTableData()
          }
        })
      }
      this.tableRef.removeCheckboxRow()
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.invoiceCollaboration.importExternalInvoiceApi,
          downloadTemplateApi: this.$API.invoiceCollaboration.downloadExternalInvoiceApi,
          paramsKey: 'excel',
          asyncParams: {
            headerId: this.$route?.query?.id,
            reconciliationType: this.$route?.query?.currentIndex === 3 ? 1 : 2
          }
        },
        success: () => {
          this.getTableData()
        }
      })
    },
    handleExport(e) {
      const params = {
        headerId: this.$route?.query?.id,
        isDownLoad: true,
        page: {
          current: 1,
          size: 10000
        }
      }
      let api = this.$API.invoiceCollaboration.exportExternalInvoiceApi
      api(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>
