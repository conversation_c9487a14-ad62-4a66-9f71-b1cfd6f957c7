<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div v-if="isSup" :class="['status-box', 'status-box' + '-' + headerInfo.invoiceStatus]">
        {{ headerInfo.invoiceStatus | statusFormat }}
      </div>
      <div v-if="!isSup" :class="['status-box', 'status-box' + '-' + headerInfo.status]">
        {{ headerInfo.status | statusFormat }}
      </div>
      <div class="infos mr20">
        {{ $t('单据号') }}：{{ isSup ? headerInfo.recoCode : headerInfo.reconciliationHeaderCodes }}
      </div>
      <div class="infos mr20">{{ $t('创建人') }}：{{ headerInfo.createUserName }}</div>
      <div class="infos">{{ $t('创建时间') }}：{{ headerInfo.createTime | dateFormat }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button
        v-if="isSup && [0, '0', 4, '4'].includes(headerInfo.invoiceStatus)"
        css-class="e-flat"
        :is-primary="true"
        @click="handleClick('submit')"
        >{{ $t('提交') }}</mt-button
      >
      <mt-button
        v-if="!isSup && [1, '1'].includes(headerInfo.status)"
        css-class="e-flat"
        :is-primary="true"
        @click="handleClick('return')"
        >{{ $t('采购退回') }}</mt-button
      >
      <mt-button
        v-if="!isSup && [1, '1'].includes(headerInfo.status)"
        css-class="e-flat"
        :is-primary="true"
        @click="handleClick('confirm')"
        >{{ $t('采购确认') }}</mt-button
      >

      <div class="sort-box" @click="isExpand = !isExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>
    <div class="main-bottom" v-show="isExpand">
      <mt-form ref="ruleForm" :model="headerInfo">
        <mt-form-item prop="companyCode" :label="$t('公司编号')">
          <mt-input v-model="headerInfo.companyCode" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="companyName" :label="$t('公司名称')">
          <mt-input v-model="headerInfo.companyName" :disabled="true"></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplierCode" :label="$t('供应商代码')">
          <mt-input v-model="headerInfo.supplierCode" :disabled="true"></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplierName" :label="$t('供应商名称')">
          <mt-input v-model="headerInfo.supplierName" :disabled="true"></mt-input>
        </mt-form-item>

        <template v-if="isSup">
          <mt-form-item
            v-if="currentIndex === 3"
            prop="executionTotalPriceTaxed"
            :label="$t('执行含税总价')"
          >
            <mt-input v-model="headerInfo.executionTotalPriceTaxed" :disabled="true"></mt-input>
          </mt-form-item>
          <mt-form-item v-if="currentIndex === 4" prop="totalAmtUntaxed" :label="$t('未税总价')">
            <mt-input v-model="headerInfo.totalAmtUntaxed" :disabled="true"></mt-input>
          </mt-form-item>
          <mt-form-item prop="executionTotalPriceUntaxed" :label="$t('执行未税总价')">
            <mt-input v-model="headerInfo.executionTotalPriceUntaxed" :disabled="true"></mt-input>
          </mt-form-item>
          <mt-form-item prop="invoiceAmtUntaxed" :label="$t('发票未税总额')">
            <mt-input v-model="headerInfo.invoiceAmtUntaxed" :disabled="true"></mt-input>
          </mt-form-item>
          <mt-form-item prop="invoiceTaxAmt" :label="$t('发票税额')">
            <mt-input v-model="headerInfo.invoiceTaxAmt" :disabled="true"></mt-input>
          </mt-form-item>
          <mt-form-item prop="invoiceAmtTaxed" :label="$t('发票含税总额')">
            <mt-input v-model="headerInfo.invoiceAmtTaxed" :disabled="true"></mt-input>
          </mt-form-item>
        </template>

        <template v-if="!isSup">
          <mt-form-item prop="invoiceUntaxAmount" :label="$t('发票未税金额')">
            <mt-input v-model="headerInfo.invoiceUntaxAmount" :disabled="true"></mt-input>
          </mt-form-item>
          <mt-form-item prop="invoiceTaxAmount" :label="$t('发票税额')">
            <mt-input v-model="headerInfo.invoiceTaxAmount" :disabled="true"></mt-input>
          </mt-form-item>
          <mt-form-item prop="invoiceTaxedAmount" :label="$t('发票含税金额')">
            <mt-input v-model="headerInfo.invoiceTaxedAmount" :disabled="true"></mt-input>
          </mt-form-item>
          <mt-form-item prop="reconciliationUntaxAmount" :label="$t('汇总未税金额')">
            <mt-input v-model="headerInfo.reconciliationUntaxAmount" :disabled="true"></mt-input>
          </mt-form-item>
          <mt-form-item prop="reconciliationTaxAmount" :label="$t('汇总税额')">
            <mt-input v-model="headerInfo.reconciliationTaxAmount" :disabled="true"></mt-input>
          </mt-form-item>
          <mt-form-item prop="reconciliationTaxedAmount" :label="$t('汇总含税金额')">
            <mt-input v-model="headerInfo.reconciliationTaxedAmount" :disabled="true"></mt-input>
          </mt-form-item>
        </template>

        <mt-form-item prop="purRemark" :label="$t('采方备注')">
          <mt-input
            v-model="headerInfo.purRemark"
            :disabled="true"
            :multiline="true"
            maxlength="150"
          />
        </mt-form-item>
        <mt-form-item prop="supplierRemark" :label="$t('供方备注')">
          <mt-input
            v-model="headerInfo.supplierRemark"
            :disabled="true"
            :multiline="true"
            maxlength="150"
          />
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
import dayjs from 'dayjs'
export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    },
    isSup: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isExpand: true
    }
  },
  filters: {
    statusFormat(value) {
      let text = ''
      if (value === 0) {
        text = i18n.t('待提交')
      } else if (value === 1) {
        text = i18n.t('待确认')
      } else if (value === 2) {
        text = i18n.t('采购已确认')
      } else if (value === 3) {
        text = i18n.t('已完成')
      } else if (value === 4) {
        text = i18n.t('已退回')
      }
      return text
    },
    dateFormat(value) {
      let date = value ? dayjs(Number(value)).format('YYYY-MM-DD') : ''
      if (value && value.includes('-')) {
        date = value
      }
      return date
    }
  },
  computed: {
    currentIndex() {
      // 3外发发票、4越南发票
      return this.$route.query.currentIndex ?? 0
    }
  },
  methods: {
    goBack() {
      this.$emit('goBack')
    },
    handleClick(flag) {
      this.$emit('btnClick', flag)
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status-box {
      padding: 2px 6px;
      border-radius: 2px;
      margin: 0 36px 0 10px;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      &-0 {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
      &-1 {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
      &-2 {
        color: #8acc40;
        background: rgba(138, 204, 64, 0.1);
      }
      &-3 {
        color: #8acc40;
        background: rgba(138, 204, 64, 0.1);
      }
      &-4 {
        color: #ed5633;
        background: rgba(237, 86, 51, 0.1);
      }
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
