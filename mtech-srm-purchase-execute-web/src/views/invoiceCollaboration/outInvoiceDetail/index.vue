<!-- 采供-外发发票详情 -->
<template>
  <div class="full-height vertical-flex-box detail-fix-wrap">
    <div>
      <TopInfo
        class="flex-keep"
        ref="topInfoRef"
        :header-info="headerInfo"
        :is-sup="isSup"
        @goBack="goBack"
        @btnClick="btnClick"
      />
    </div>
    <mt-tabs
      class="flex-keep toggle-tab"
      :e-tab="false"
      :data-source="tabList"
      :selected-item="tabIndex"
      @handleSelectTab="handleSelectTab"
    />
    <div class="toggle-content">
      <div class="flex-fit" v-show="tabIndex === 0">
        <Detail ref="detailRef" :is-sup="isSup" :can-edit="canEdit" />
      </div>
      <div class="flex-fit" v-show="tabIndex === 1">
        <relative-file
          ref="relativeFileRef"
          :doc-id="relativeFileData.docId"
          :request-url-obj="requestUrlObj"
          :module-file-list="moduleFileList"
          :file-query-parms="fileQueryParms"
          :is-view="relativeFileData.isView"
        ></relative-file>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {
    TopInfo: () => import('./components/TopInfo.vue'),
    Detail: () => import('./components/Detail.vue'),
    RelativeFile: () => import('@/components/businessComponents/relativeFile/index.vue')
  },
  data() {
    return {
      headerInfo: {},
      tabList: [
        {
          title: this.$t('发票清单')
        },
        {
          title: this.$t('相关附件')
        }
      ],
      tabIndex: 0,
      requestUrlObj: {
        preUrl: 'reconciliationSettlementTv',
        saveUrl: 'saveFileSaleReconApi', // 将附件文件url保存到列表 保存文件信息
        fileUrl: `/contract/tenant/reconciliation/file/queryList` // 获取附件列表的url 根据docType和docID查询所有文件信息
      }
    }
  },
  computed: {
    currentIndex() {
      // 3外发发票、4越南发票
      return this.$route.query.currentIndex ?? 0
    },
    isSup() {
      return this.$route.name === 'sup-invoice-detail'
    },
    canEdit() {
      return [0, '0', 4, '4'].includes(this.headerInfo.invoiceStatus)
    },
    relativeFileData() {
      let isView = !this.isSup || (this.isSup && !this.canEdit)
      return {
        docId: this.$route.query?.id,
        isView // 查看
      }
    },
    fileQueryParms() {
      return {
        docType: this.currentIndex === 3 ? 'TV_EXTERNAL_RECONCILIATION' : 'VN_DAILY_RECONCILIATION',
        nodeCode: 'supplier_invoice'
      }
    },
    moduleFileList() {
      return [
        {
          id: this.fileQueryParms.docType, // 选中时传给api的 doctype 的值
          nodeName: this.$t('供方-发票附件'), // 侧边栏名称
          nodeCode: 0, // 即当前 index 用于选择左侧边栏时 获取当前项 配置
          btnRequired: {
            hasUpload: true,
            hasDownload: true,
            hasDelete: true,
            hasPrint: false
          }, // 表格 toolbar 配置（可选） hasUpload: Boolean、hasDownload: Boolean、 hasDelete: Boolean
          hasItem: false, // 是否显示附件行数据（可选）
          deleteFileUrl: `/contract/tenant/reconciliation/file/delete`, // 删除文件使用的 API 根据ID删除
          deleteFileRequestMethod: 'delete' // 设置删除api的请求方法
        }
      ]
    }
  },
  mounted() {
    this.getHeader()
  },
  methods: {
    async getHeader() {
      let params = null
      let api = null
      if (!this.isSup) {
        params = {
          recoCodes: this.$route.query?.code,
          page: {
            current: 1,
            size: 20
          }
        }
        api = this.$API.invoiceCollaboration.queryPurchaseDetailList
      } else {
        if (this.currentIndex === 3) {
          params = {
            recoCode: this.$route.query?.code,
            status: 2,
            page: {
              current: 1,
              size: 20
            }
          }
          api = this.$API.invoiceCollaboration.headerExternalInvoiceApi
        }
        if (this.currentIndex === 4) {
          params = {
            recoCode: this.$route.query?.code,
            page: {
              current: 1,
              size: 20
            }
          }
          api = this.$API.invoiceCollaboration.headerVnInvoiceApi
        }
      }
      const res = await api(params)
      if (res.code === 200) {
        this.headerInfo = res.data.records[0]
        if (this.currentIndex === 4) {
          this.headerInfo.purRemark = this.headerInfo.invoiceCustomerRemark
          this.headerInfo.supplierRemark = this.headerInfo.invoiceSupplierRemark
        }
      }
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    btnClick(flag) {
      switch (flag) {
        case 'submit':
          if (this.$refs.detailRef.tableData.length === 0) {
            this.$toast({ content: this.$t('请先新增发票'), type: 'warning' })
            break
          }
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认提交?')
            },
            success: () => {
              this.handleSubmit()
            }
          })
          break
        case 'return':
          this.handleReturn()
          break
        case 'confirm':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('是否确认?')
            },
            success: () => {
              this.handleConfirm()
            }
          })
          break
        default:
          break
      }
    },
    async handleSubmit() {
      let params = {
        reconciliationHeaderIds: this.$refs.detailRef.tableData[0].reconciliationHeaderIds,
        reconciliationType: this.$route?.query?.currentIndex === 3 ? 1 : 2
      }
      this.apiStartLoading()
      let api = this.$API.invoiceCollaboration.submitExternalInvoiceApi
      const res = await api(params).finally(() => this.apiEndLoading())
      if (res.code === 200) {
        this.$toast({ content: this.$t('提交成功'), type: 'success' })
        this.getHeader()
      }
    },
    handleReturn() {
      this.$dialog({
        data: {
          title: this.$t('退回')
        },
        modal: () => import('./components/RejectDialog.vue'),
        success: async (rejectReason) => {
          let params = {
            id: this.headerInfo?.id,
            pass: false,
            rejectReason,
            reconciliationType: this.$route?.query?.currentIndex === 3 ? 1 : 2
          }
          this.apiStartLoading()
          let api = this.$API.invoiceCollaboration.confirmExternalInvoiceApi
          const res = await api(params).finally(() => this.apiEndLoading())
          if (res.code === 200) {
            this.$toast({ content: this.$t('退回成功'), type: 'success' })
            this.getHeader()
          }
        }
      })
    },
    async handleConfirm() {
      let params = {
        id: this.headerInfo?.id,
        pass: true,
        reconciliationType: this.$route?.query?.currentIndex === 3 ? 1 : 2
      }
      this.apiStartLoading()
      let api = this.$API.invoiceCollaboration.confirmExternalInvoiceApi
      const res = await api(params).finally(() => this.apiEndLoading())
      if (res.code === 200) {
        this.$toast({ content: this.$t('确认成功'), type: 'success' })
        this.getHeader()
      }
    },
    goBack() {
      // this.$router.go(-1)
      this.$router.push({
        name: this.isSup ? 'invoice-summary-supplier-tv' : 'invoice-summary-tv',
        query: {
          timeStamp: new Date().getTime(),
          currentIndex: this.isSup ? this.currentIndex : 0
        }
      })
    },
    apiStartLoading() {
      this.$store.commit('startLoading')
    },
    apiEndLoading() {
      this.$store.commit('endLoading')
    }
  }
}
</script>
