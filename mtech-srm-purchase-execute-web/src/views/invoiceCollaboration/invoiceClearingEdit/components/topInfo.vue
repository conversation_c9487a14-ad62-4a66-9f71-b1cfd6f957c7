<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button
        css-class="e-flat"
        v-if="entryType == ConstantType.Edit"
        :is-primary="true"
        @click="doUpdate"
        >{{ $t('更新') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        v-if="entryType == ConstantType.Add"
        :is-primary="true"
        @click="doSubmit"
        >{{ $t('提交') }}</mt-button
      >

      <div class="sort-box" @click="doExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form
        ref="ruleForm"
        :model="headerInfo"
        :rules="rules"
        :validate-on-rule-change="false"
        autocomplete="off"
      >
        <!-- 明细行-明细行数 -->
        <mt-form-item prop="reconciliationItemCount" :label="$t('明细行数')" class="">
          <mt-input
            v-model="headerInfo.reconciliationItemCount"
            :disabled="true"
            :show-clear-button="true"
            placeholder=""
          ></mt-input>
        </mt-form-item>
        <!-- 明细行-汇总含税 -->
        <mt-form-item prop="reconciliationTaxedAmount" :label="$t('明细汇总含税金额')" class="">
          <mt-input
            v-model="headerInfo.reconciliationTaxedAmount"
            :disabled="true"
            :show-clear-button="true"
            placeholder=""
          ></mt-input>
        </mt-form-item>
        <!-- 明细行-汇总未税 -->
        <mt-form-item prop="reconciliationUntaxAmount" :label="$t('明细汇总未税金额')" class="">
          <mt-input
            v-model="headerInfo.reconciliationUntaxAmount"
            :disabled="true"
            :show-clear-button="true"
            placeholder=""
          ></mt-input>
        </mt-form-item>
        <!-- 明细行-汇总税额 -->
        <mt-form-item prop="reconciliationTaxAmount" :label="$t('明细汇总税额')" class="">
          <mt-input
            v-model="headerInfo.reconciliationTaxAmount"
            :disabled="true"
            :show-clear-button="true"
            placeholder=""
          ></mt-input>
        </mt-form-item>

        <!-- 高低开-汇总含税 -->
        <mt-form-item prop="taxedHighLow" :label="$t('高低开汇总含税金额')" class="">
          <mt-input
            v-model="headerInfo.taxedHighLow"
            :disabled="true"
            :show-clear-button="true"
            placeholder=""
          ></mt-input>
        </mt-form-item>
        <!-- 高低开-汇总未税 -->
        <mt-form-item prop="untaxedHighLow" :label="$t('高低开汇总未税金额')" class="">
          <mt-input
            v-model="headerInfo.untaxedHighLow"
            :disabled="true"
            :show-clear-button="true"
            placeholder=""
          ></mt-input>
        </mt-form-item>
        <!-- 高低开-汇总税额 -->
        <mt-form-item prop="highLowInfoTaxAmount" :label="$t('高低开汇总税额')" class="">
          <mt-input
            v-model="headerInfo.highLowInfoTaxAmount"
            :disabled="true"
            :show-clear-button="true"
            placeholder=""
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="invoiceTypeCode" :label="$t('发票类型')" class="">
          <mt-select
            v-model="headerInfo.invoiceTypeCode"
            :data-source="invoiceTypeOptions"
            :show-clear-button="true"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :placeholder="$t('发票类型')"
            @change="onChangeInvoiceTypeCode"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="invoiceCode" :label="$t('发票代码')" class="">
          <mt-input
            maxlength="50"
            v-model="headerInfo.invoiceCode"
            :disabled="false"
            :show-clear-button="true"
            :placeholder="$t('发票代码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="invoiceNum" :label="$t('发票号')" class="">
          <mt-input
            maxlength="50"
            v-model="headerInfo.invoiceNum"
            :disabled="false"
            :show-clear-button="true"
            :placeholder="$t('发票号')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="invoiceTime" :label="$t('开票日期')" class="">
          <mt-date-picker
            :show-clear-button="true"
            :max="new Date()"
            :allow-edit="false"
            :placeholder="$t('开票日期')"
            v-model="headerInfo.invoiceTime"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="invoiceTaxedAmount" :label="$t('发票含税金额')" class="">
          <mt-input-number
            max="999999999999999.99"
            :show-clear-button="true"
            :show-spin-button="false"
            :placeholder="$t('发票含税金额')"
            :precision="2"
            @keydown.native="numberInputOnKeyDown"
            @input="handleTaxedAmount"
            v-model="headerInfo.invoiceTaxedAmount"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item prop="invoiceUntaxAmount" :label="$t('发票未税金额')" class="">
          <mt-input-number
            max="999999999999999.99"
            :show-clear-button="true"
            :show-spin-button="false"
            :precision="2"
            @keydown.native="numberInputOnKeyDown"
            @input="handleUntaxedAmount"
            :placeholder="$t('发票未税金额')"
            v-model="headerInfo.invoiceUntaxAmount"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item prop="invoiceTaxAmount" :label="$t('税额')" class="">
          <mt-input
            :disabled="true"
            v-model="headerInfo.invoiceTaxAmount"
            :show-clear-button="true"
            :placeholder="$t('税额')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="supplierRemark" :label="$t('供方备注')" class="full-width">
          <mt-input
            maxlength="200"
            v-model="headerInfo.supplierRemark"
            :show-clear-button="true"
            :placeholder="$t('供方备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { ConstantType } from '../config/constant'
import bigDecimal from 'js-big-decimal'
import { numberInputOnKeyDown } from '@/utils/utils'

export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {
        return {
          reconciliationIds: [], // 关联对账单id列表

          // 关联明细行
          reconciliationItemCount: 0, // 明细行数
          reconciliationTaxedAmount: 0, // 明细汇总含税金额
          reconciliationUntaxAmount: 0, // 明细汇总未税金额
          reconciliationTaxAmount: 0, // 明细汇总税额

          // 关联高低开
          taxedHighLow: 0, // 高低开汇总含税金额
          untaxedHighLow: 0, // 高低开汇总未税金额
          highLowInfoTaxAmount: 0, // 高低开汇总税额

          // 发票信息
          id: undefined, // 发票id
          invoiceTypeCode: null, // 发票类型
          invoiceTypeName: '', // 发票类型名称
          invoiceCode: '', // 发票代码
          invoiceNum: '', // 发票号
          invoiceTime: '', // 开票日期
          invoiceTaxedAmount: null, // 发票含税金额
          invoiceUntaxAmount: null, // 发票未税金额
          invoiceTaxAmount: null, // 税额
          supplierRemark: '' // 供应商备注
        }
      }
    },
    entryType: {
      type: String,
      default: ConstantType.Add
    }
  },
  data() {
    // 发票含税金额
    const taxedAmountValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入发票含税金额')))
      } else if (
        this.headerInfo.invoiceUntaxAmount &&
        Number(value) < Number(this.headerInfo.invoiceUntaxAmount)
      ) {
        callback(new Error(this.$t('含税金额不能小于未税金额')))
      } else {
        // this.$refs.ruleForm.clearValidate(["invoiceUntaxAmount"]);
        callback()
      }
    }
    // 发票未税金额
    const untaxedAmountValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('请输入发票未税金额')))
      } else if (
        this.headerInfo.invoiceTaxedAmount &&
        Number(value) > Number(this.headerInfo.invoiceTaxedAmount)
      ) {
        callback(new Error(this.$t('未税金额不能大于含税金额')))
      } else {
        // this.$refs.ruleForm.clearValidate(["invoiceTaxedAmount"]);
        callback()
      }
    }

    return {
      isExpand: true,
      rules: {
        invoiceTypeCode: [
          {
            required: true,
            message: this.$t('请选择发票类型'),
            trigger: 'blur'
          }
        ],
        invoiceCode: [
          {
            required: true,
            message: this.$t('请输入发票代码'),
            trigger: 'blur'
          }
        ],
        invoiceNum: [{ required: true, message: this.$t('请输入发票号'), trigger: 'blur' }],
        invoiceTime: [
          {
            required: true,
            message: this.$t('请选择开票日期'),
            trigger: 'blur'
          }
        ],
        invoiceTaxedAmount: [{ required: true, validator: taxedAmountValidator, trigger: 'blur' }],
        invoiceUntaxAmount: [
          {
            required: true,
            validator: untaxedAmountValidator,
            trigger: 'blur'
          }
        ]
      },
      invoiceTypeOptions: [], // 发票类型
      ConstantType,
      numberInputOnKeyDown
    }
  },
  mounted() {
    // 根据主单查询采方的字典 发票类型
    this.postSupplierDictByHeader()
  },
  filters: {},
  methods: {
    // 根据主单查询采方的字典 发票类型
    postSupplierDictByHeader() {
      this.$API.invoiceCollaboration
        .postSupplierDictByHeader({
          dataId: this.headerInfo.reconciliationIds[0], // 主单id/任意一个单据id
          dictCode: 'invoiceType' // 字典编码
        })
        .then((res) => {
          const data = res?.data || []
          this.invoiceTypeOptions = data // 发票类型
        })
    },
    // 发票未税金额 改变
    handleUntaxedAmount(e) {
      const invoiceTaxedAmount = this.headerInfo.invoiceTaxedAmount ?? 0 // 含税
      const invoiceUntaxAmount = e ?? 0 // 未税
      // 税额 = 含税 - 未税
      this.headerInfo.invoiceTaxAmount = bigDecimal.subtract(invoiceTaxedAmount, invoiceUntaxAmount)
    },
    // 发票含税金额 改变
    handleTaxedAmount(e) {
      const invoiceTaxedAmount = e ?? 0 // 含税
      const invoiceUntaxAmount = this.headerInfo.invoiceUntaxAmount ?? 0 // 未税
      // 税额 = 含税 - 未税
      this.headerInfo.invoiceTaxAmount = bigDecimal.subtract(invoiceTaxedAmount, invoiceUntaxAmount)
    },
    // 发票类型 改变
    onChangeInvoiceTypeCode(args) {
      const { itemData } = args
      if (itemData) {
        this.headerInfo.invoiceTypeCode = itemData.itemCode
        this.headerInfo.invoiceTypeName = itemData.itemName
      } else {
        this.headerInfo.invoiceTypeCode = null
        this.headerInfo.invoiceTypeName = ''
      }
    },
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 提交
    doSubmit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('doSubmit')
        }
      })
    },
    // 更新
    doUpdate() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('doUpdate')
        }
      })
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // border-bottom: 1px solid #e6e9ed;

    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
