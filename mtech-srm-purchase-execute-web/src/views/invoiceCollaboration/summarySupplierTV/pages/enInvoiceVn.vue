<!-- 供方-越南日结对账发票 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('公司')" prop="companyCode">
          <RemoteAutocomplete
            v-model="searchFormModel.companyCode"
            url="/masterDataManagement/auth/company/auth-fuzzy"
            :params="{
              organizationLevelCodes: ['ORG02', 'ORG01'],
              orgType: 'ORG001PRO',
              includeItself: true
            }"
            :placeholder="$t('请选择公司')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            records-position="data"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item :label="$t('币种')" prop="currencyCode">
          <RemoteAutocomplete
            v-model="searchFormModel.currencyCode"
            url="/masterDataManagement/tenant/currency/paged-query"
            :placeholder="$t('请选择币种')"
            :fields="{ text: 'currencyName', value: 'currencyCode' }"
            :search-fields="['currencyName', 'currencyCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item :label="$t('对账单号')" prop="recoCode">
          <mt-input
            v-model="searchFormModel.recoCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="invoiceStatus" :label="$t('发票状态')">
          <mt-select
            v-model="searchFormModel.invoiceStatus"
            :data-source="invoiceStatusOptions"
            :allow-filtering="true"
            :show-clear-button="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="adf83cc2-0eb1-4c91-b6c5-51cc0c322d4c"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :row-config="{ height: 46 }"
      keep-source
      @refresh="handleSearch"
    >
      <template #recoNoDetault="{ row }">
        <div :class="['able-click-field']" @click="recoNoClick(row)">
          {{ row.recoCode }}
        </div>
      </template>
      <template #invoiceStatusDefault="{ row }">
        <div style="color: #2783fe">
          <div style="cursor: pointer">
            <span
              :class="
                invoiceStatusOptions.filter((i) => i.value === row.invoiceStatus)[0]['cssClass']
              "
              >{{
                invoiceStatusOptions.filter((i) => i.value === row.invoiceStatus)[0]['text']
              }}</span
            >
          </div>
          <template v-for="item in statusCellTool">
            <div
              v-if="item.visibleCondition(row)"
              style="cursor: pointer; display: inline-block; margin-right: 5px"
              v-permission="item.permission"
              :key="item.id"
            >
              <mt-icon :name="item.icon" />
              <span @click="handleClickCellTool({ tool: { id: item.id }, data: row })">{{
                item.title
              }}</span>
            </div>
          </template>
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, invoiceStatusOptions } from '../config/enInvoiceVn'
export default {
  components: { CollapseSearch, ScTable },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      invoiceStatusOptions,
      statusCellTool: [
        {
          id: 'UploadInvoice',
          icon: 'icon_list_download rotate-180',
          title: this.$t('上传发票'),
          visibleCondition: (data) => data.invoiceStatus == 0 || data.invoiceStatus == 4 // 待提交、已退回
        }
      ]
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleClickCellTool(args) {
      const { tool, data } = args
      if (tool.id === 'UploadInvoice') {
        this.$router.push({
          name: 'sup-invoice-detail',
          query: {
            id: data.id,
            code: data.recoCode,
            currentIndex: 4,
            timeStamp: new Date().getTime()
          }
        })
      }
    },
    recoNoClick(row) {
      // 跳转到 发票明细页面
      this.$router.push({
        name: 'sup-invoice-detail',
        query: {
          id: row.id,
          code: row.recoCode,
          currentIndex: 4,
          timeStamp: new Date().getTime()
        }
      })
    },
    recoTimeChange(e) {
      if (e.startDate) {
        this.searchFormModel['recoStartTime'] = dayjs(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        ).valueOf()
        this.searchFormModel['recoEndTime'] =
          dayjs(dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')).valueOf() + 999 // + 999是后端要求
      } else {
        this.searchFormModel['recoStartTime'] = null
        this.searchFormModel['recoEndTime'] = null
      }
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      let api = this.$API.invoiceCollaboration.headerVnInvoiceApi
      this.loading = true
      const res = await api(params).catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    }
  }
}
</script>
