<template>
  <!-- 发票协同（供方）列表 -->
  <div>
    <!-- 可开票明细 内容 -->
    <div class="full-height">
      <!-- 可开票明细tab表格 -->
      <mt-template-page
        class="slot-component"
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="slotComponentConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleCustomReset="handleSearchReset"
        @handleCustomSearch="search"
      >
        <template v-slot:quick-search-form>
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="companyCodes" :label="$t('公司')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.companyCodes"
                url="/masterDataManagement/auth/company/auth-fuzzy"
                multiple
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                :placeholder="$t('请选择公司')"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                records-position="data"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="factoryCodes" :label="$t('工厂')" class="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.factoryCodes"
                :url="$API.masterData.getSiteAuthFuzzyUrl"
                multiple
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                params-key="fuzzyParam"
                records-position="data"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="invoiceNum" :label="$t('发票号码')" label-style="top">
              <mt-input
                v-model="searchFormModel.invoiceNum"
                :placeholder="$t('发票号码')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="currencyCode" :label="$t('币种')" label-style="top">
              <mt-select
                :allow-filtering="true"
                v-model="searchFormModel.currencyCode"
                :data-source="currencyOptions"
                :fields="{ text: 'label', value: 'value' }"
                :show-clear-button="true"
                :filtering="getCurrency"
                :placeholder="$t('请选择币种')"
                @open="startOpen1"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="" :label="$t('到货月份')" label-style="top">
              <mt-date-range-picker
                format="yyyy-MM"
                v-model="searchFormModel.arriveMonth"
                :show-clear-button="true"
                :placeholder="$t('请选择到货月份')"
                @change="(e) => dateTimeChange(e, 'arriveMonth')"
              />
            </mt-form-item>
            <mt-form-item prop="reconciliationCode" :label="$t('对账单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.reconciliationCode"
                :placeholder="$t('对账单号')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="invoiceStatus" :label="$t('开票状态')" label-style="top">
              <mt-select
                style="flex: 1"
                v-model="searchFormModel.invoiceStatus"
                :data-source="[
                  { value: 0, text: $t('未完成') },
                  { value: 1, text: $t('已完成') }
                ]"
                :allow-filtering="true"
                :show-clear-button="true"
                filter-type="Contains"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('发票状态')" label-style="top">
              <mt-select
                style="flex: 1"
                v-model="searchFormModel.status"
                :data-source="[
                  { value: 0, text: $t('待提交') },
                  { value: 1, text: $t('待确认') },
                  { value: 2, text: $t('采购已确认') },
                  { value: 3, text: $t('财务已确认') },
                  { value: 4, text: $t('已退回') }
                ]"
                :allow-filtering="true"
                :show-clear-button="true"
                filter-type="Contains"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item>
            <!-- <mt-form-item prop="syncStatus" :label="$t('同步状态')" label-style="top">
            <mt-select
              style="flex: 1"
              v-model="searchFormModel.syncStatus"
              :data-source="[
                { value: 0, text: $t('未同步') },
                { value: 1, text: $t('已同步') }
              ]"
              :allow-filtering="true"
              :show-clear-button="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
            ></mt-select>
          </mt-form-item> -->
          </mt-form>
        </template>
      </mt-template-page>
    </div>
    <mt-page
      class="full-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totals"
      @currentChange="currentChange"
      @sizeChange="sizeChange"
    />
  </div>
</template>

<script>
import { columnData } from '../config/constant'
import { addCodeNameKeyInList } from '@/utils/utils'
import dayjs from 'dayjs'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      siteOptions: [],
      currencyOptions: [], //订单币种
      companyOptions: [], // 公司 下拉选项
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 5,
        pageSizes: [10, 50, 100, 200],
        totalRecordsCount: 0,
        totals: 0
      },
      searchFormModel: {
        isQueryAll: true
        // companyCode: null,
        // companyName: null,
        // factoryCode: null,
        // factoryName: null,
        // invoiceTypeCode: null,
        // invoiceTypeName: null,
        // invoiceCode: null,
        // invoiceNum: null,
        // invoiceTime: null,
        // reviewTime: null,
        // invoiceTaxAmount: null,
        // arriveYear: null,
        // arriveMonth: null,
        // currencyCode: null,
        // currencyName: null,
        // status: null,
        // statusDescription: null,
        // syncStatus: null
      },
      apiWaitingQuantity: 0, // 调用的api正在等待数
      // 可开票明细 表格
      slotComponentConfig: [
        {
          isUseCustomSearch: true,
          isCustomSearchHandle: true,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                {
                  id: 'InvoiceExport',
                  icon: 'icon_solid_export',
                  title: this.$t('导出')
                }
              ],
              ['Setting']
            ]
          },
          grid: {
            allowPaging: false,
            frozenColumns: 1,
            lineIndex: 1,
            lineSelection: 0,
            columnData: columnData
          }
        }
      ]
    }
  },
  mounted() {
    this.postSiteFuzzyQuery({ text: '' })
    this.getCompany({ text: '' })
    this.search()
  },
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id === 'InvoiceExport') {
        const params = {
          isDownLoad: true,
          page: { current: 1, size: 10000 },
          ...this.searchFormModel
        } // 筛选条件
        this.apiStartLoading()
        this.$API.invoiceCollaboration
          .exportInvoiceList(params)
          .then((res) => {
            this.apiEndLoading()
            const fileName = getHeadersFileName(res)
            download({ fileName: `${fileName}`, blob: res.data })
          })
          .catch(() => {
            this.apiEndLoading()
          })
      }
    },
    currencyCodeChange(e) {
      if (e.itemData) {
        this.searchFormModel.currencyCode = e.itemData.currencyCode
        this.searchFormModel.currencyName = e.itemData.currencyName
      } else {
        this.searchFormModel.currencyCode = null
        this.searchFormModel.currencyName = null
      }
    },
    dateTimeChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'S'] = this.getUnix(dayjs(e.startDate).format('YYYY-MM'))
        this.searchFormModel[flag + 'E'] = this.getUnix(dayjs(e.endDate).format('YYYY-MM'))
      } else {
        this.searchFormModel[flag + 'S'] = null
        this.searchFormModel[flag + 'E'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    startOpen1() {
      if (!this.currencyOptions.length) {
        this.getCurrency()
      }
    },
    getCurrency(val, currencyCode) {
      // 订单币种
      let params = { fuzzyParam: val?.text || '' }
      this.$API.masterData.getCurrencyByFilter(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.currencyCode}-${item.currencyName}`
          item.text = item.currencyName
          item.value = item.currencyCode
        })
        this.currencyOptions = list
        if (currencyCode) {
          this.topInfo.currency = currencyCode
        }
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.currencyOptions)
          })
        }
      })
    },
    // 分页器 - 切换分页
    currentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.handleCustomSearch()
    },
    // 分页器 - 切换页大小
    sizeChange(currentPageSize) {
      this.pageSettings.pageSize = currentPageSize
      this.pageSettings.currentPage = 1
      this.handleCustomSearch()
    },
    handleSearchReset() {
      for (let key in this.searchFormModel) {
        this.searchFormModel[key] = null
        if (key === 'isQueryAll') {
          this.searchFormModel[key] = true
        }
      }
      this.search()
    },
    // 自定义查询
    search() {
      this.pageSettings.currentPage = 1
      this.handleCustomSearch()
    },
    handleCustomSearch() {
      this.$store.commit('startLoading')
      const param = {
        page: {
          current: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      if (this.searchFormModel.reconciliationCode) {
        param.recoCodes = [this.searchFormModel.reconciliationCode]
      }
      this.$API.invoiceCollaboration
        .getInvoiceSupplierList(param)
        .then((res) => {
          if (res.code === 200) {
            this.$set(this.slotComponentConfig[0].grid, 'dataSource', res.data.records)
            this.pageSettings.totals = Math.ceil(
              Number(res.data.total) / this.pageSettings.pageSize
            )
            this.pageSettings.totalRecordsCount = res.data.total
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        page: {
          current: 1,
          pages: 0,
          size: 20
        }
      }
      this.$API.receiptAndDelivery
        .getFactoryInfo(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 公司 change
    companyCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.companyId = itemData.id
        this.searchFormModel.companyCode = itemData.customerCode
        this.searchFormModel.companyName = itemData.customerName
      } else {
        this.searchFormModel.companyId = ''
        this.searchFormModel.companyCode = ''
        this.searchFormModel.companyName = ''
      }
    },
    // 工厂 change
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.factoryId = itemData.id
        this.searchFormModel.factoryCode = itemData.factoryCode
        this.searchFormModel.factoryName = itemData.factoryName
      } else {
        this.searchFormModel.factoryId = ''
        this.searchFormModel.factoryCode = ''
        this.searchFormModel.factoryName = ''
      }
    },
    // 获取主数据-获取指定组织下指定组织层级节点列表 业务公司
    getCompany(args) {
      const { updateData, setSelectData } = args
      let obj = {
        fuzzyNameOrCode: ''
      }
      this.$API.masterData
        .getCustomer(obj)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.companyOptions = addCodeNameKeyInList({
              firstKey: 'customerCode',
              secondKey: 'customerName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.companyOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height > div.tabs-line {
  display: flex;
  align-items: center;
}
.slot-select {
  display: flex;
  position: relative;
  align-items: center;
}
.pur-tenant-select {
  margin: 25px;
}
/deep/ .supReconTypeTabs {
  display: flex;
  width: calc(100% - 225px);
  flex: 1;
  overflow: auto;

  .mt-tabs-container {
    width: calc(100% - 225px);
  }
}
</style>
