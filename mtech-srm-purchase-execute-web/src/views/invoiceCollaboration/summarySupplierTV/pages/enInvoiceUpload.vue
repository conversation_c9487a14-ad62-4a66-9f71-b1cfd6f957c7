<template>
  <!-- 发票协同（供方）列表 -->
  <div>
    <!-- 可开票明细 内容 -->
    <div class="full-height">
      <!-- 可开票明细tab表格 -->
      <mt-template-page
        class="slot-component"
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="slotComponentConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
        @showFileBaseInfo="showFileBaseInfo"
        @handleCustomReset="handleSearchReset"
        @handleCustomSearch="search"
      >
        <template v-slot:quick-search-form>
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="companyCodes" :label="$t('公司')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.companyCodes"
                url="/masterDataManagement/auth/company/auth-fuzzy"
                multiple
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                :placeholder="$t('请选择公司')"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                records-position="data"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="factoryCodes" :label="$t('工厂')" class="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.factoryCodes"
                :url="$API.masterData.getSiteAuthFuzzyUrl"
                multiple
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                params-key="fuzzyParam"
                records-position="data"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="invoiceNum" :label="$t('发票号码')" label-style="top">
              <mt-input
                v-model="searchFormModel.invoiceNum"
                :placeholder="$t('发票号码')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="currencyCode" :label="$t('币种')" label-style="top">
              <mt-select
                :allow-filtering="true"
                v-model="searchFormModel.currencyCode"
                :data-source="currencyOptions"
                :fields="{ text: 'label', value: 'value' }"
                :show-clear-button="true"
                :filtering="getCurrency"
                :placeholder="$t('请选择币种')"
                @open="startOpen1"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="" :label="$t('到货月份')" label-style="top">
              <mt-date-range-picker
                format="yyyy-MM"
                v-model="searchFormModel.arriveMonth"
                :show-clear-button="true"
                :placeholder="$t('请选择到货月份')"
                @change="(e) => dateTimeChange(e, 'arriveMonth')"
              />
            </mt-form-item>
            <mt-form-item prop="reconciliationCode" :label="$t('对账单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.reconciliationCode"
                :placeholder="$t('对账单号')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="invoiceStatus" :label="$t('开票状态')" label-style="top">
              <mt-select
                style="flex: 1"
                v-model="searchFormModel.invoiceStatus"
                :data-source="[
                  { value: 0, text: $t('未完成') },
                  { value: 1, text: $t('已完成') }
                ]"
                :allow-filtering="true"
                :show-clear-button="true"
                filter-type="Contains"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('发票状态')" label-style="top">
              <mt-select
                style="flex: 1"
                v-model="searchFormModel.status"
                :data-source="[
                  { value: 0, text: $t('待提交') },
                  { value: 1, text: $t('待确认') },
                  { value: 2, text: $t('采购已确认') },
                  { value: 3, text: $t('财务已确认') },
                  { value: 4, text: $t('已退回') }
                ]"
                :allow-filtering="true"
                :show-clear-button="true"
                filter-type="Contains"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item>
            <!-- <mt-form-item prop="syncStatus" :label="$t('同步状态')" label-style="top">
            <mt-select
              style="flex: 1"
              v-model="searchFormModel.syncStatus"
              :data-source="[
                { value: 0, text: $t('未同步') },
                { value: 1, text: $t('已同步') }
              ]"
              :allow-filtering="true"
              :show-clear-button="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
            ></mt-select>
          </mt-form-item> -->
          </mt-form>
        </template>
      </mt-template-page>
    </div>
    <mt-page
      class="full-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totals"
      @currentChange="currentChange"
      @sizeChange="sizeChange"
    />
    <!-- 附件弹窗 -->
    <uploader-dialog ref="uploaderDialog"></uploader-dialog>
    <!-- 发票填写弹框 -->
    <!-- <invoice-list-dialog
      ref="invoiceListDialog"
      @confirm="invoiceDialogConfirm"
    ></invoice-list-dialog> -->
    <!-- 发票关联单据明细行弹框 -->
    <bill-details-table-dialog ref="billDetailsTableDialog"></bill-details-table-dialog>
  </div>
</template>

<script>
import { formatTableColumnData } from '../config/index.js'
import {
  Tab,
  InvoiceStatus,
  columnData
  // InvoiceListDialogActionType,
} from '../config/constant'
import { BASE_TENANT } from '@/utils/constant'
import { cloneDeep } from 'lodash'
import UploaderDialog from '@/components/Upload/uploaderDialog'
// import InvoiceListDialog from "./components/invoiceListDialog";
import BillDetailsTableDialog from '@/components/businessComponents/billDetailsTableDialog'
import { BillDetailsTableDialogActionType } from '@/components/businessComponents/billDetailsTableDialog/config/constant'
import bigDecimal from 'js-big-decimal'
import { ConstantType } from '../../detailSupplierV2/config/constant'
import { ConstantType as InvoiceEditType } from '../../invoiceEditSupplier/config/constant'
import { download, getHeadersFileName } from '@/utils/utils'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
import dayjs from 'dayjs'
import Vue from 'vue'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  components: {
    UploaderDialog,
    BillDetailsTableDialog
  },
  data() {
    return {
      siteOptions: [],
      currencyOptions: [], //订单币种
      statusOptions: [
        { value: 0, label: '0-待提交' },
        { value: 1, label: '1-待审核' },
        { value: 2, label: '2-待财务审核' },
        { value: 3, label: '3-完成' },
        { value: 4, label: '4-已退回' },
        { value: 5, label: '5-已删除' }
      ],
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      companyCodeValueTemplate: codeNameColumn({
        firstKey: 'customerCode',
        secondKey: 'customerName'
      }),
      companyOptions: [], // 公司 下拉选项
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 5,
        pageSizes: [10, 50, 100, 200],
        totalRecordsCount: 0,
        totals: 0
      },
      searchFormModel: {
        isQueryAll: false
        // companyCode: null,
        // companyName: null,
        // factoryCode: null,
        // factoryName: null,
        // invoiceTypeCode: null,
        // invoiceTypeName: null,
        // invoiceCode: null,
        // invoiceNum: null,
        // invoiceTime: null,
        // reviewTime: null,
        // invoiceTaxAmount: null,
        // arriveYear: null,
        // arriveMonth: null,
        // currencyCode: null,
        // currencyName: null,
        // status: null,
        // statusDescription: null,
        // syncStatus: null
      },
      apiWaitingQuantity: 0, // 调用的api正在等待数
      slotTabList: [], // 公司列表
      slotTabTypeList: [], // 当前公司下的 对账类型列表
      currentPurTenantId: '', // 当前选择的采方租户id
      currentSlotTypeTabIndex: 0, // 对账类型序号
      asyncStatementDetailsColumnData: [], // 从 API 获取的表头
      isShowSlotTemplateRef: false, // 初始化不显示
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'BillableDocuments', permissionCode: 'T_02_0010' },
          { dataPermission: 'BillableDetails', permissionCode: 'T_02_0011' },
          { dataPermission: 'ListOfInvoices', permissionCode: 'T_02_0012' }
        ]
      },
      // 可开票明细 表格
      slotComponentConfig: [
        {
          isUseCustomSearch: true,
          isCustomSearchHandle: true,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                {
                  id: 'InvoiceAdd',
                  icon: 'icon_solid_export',
                  title: this.$t('新增')
                },
                {
                  id: 'InvoiceDelete',
                  icon: 'icon_solid_Delete',
                  title: this.$t('删除')
                },
                {
                  id: 'InvoiceEdit',
                  icon: 'icon_solid_Edit',
                  title: this.$t('编辑')
                },
                {
                  id: 'InvoiceSubmit',
                  icon: 'icon_solid_export',
                  title: this.$t('提交')
                },
                {
                  title: this.$t('打印'),
                  icon: 'icon_list_print',
                  id: 'Print'
                },
                {
                  id: 'InvoiceExport',
                  icon: 'icon_solid_export',
                  title: this.$t('导出')
                }
              ],
              ['Setting']
            ]
          },
          grid: {
            allowPaging: false,
            frozenColumns: 1,
            lineIndex: 1,
            lineSelection: 0,
            columnData: columnData
          }
        }
      ]
    }
  },
  mounted() {
    // 获取客户对象
    this.getCustomerReconciliationType()
    this.postSiteFuzzyQuery({ text: '' })
    this.getCompany({ text: '' })
    this.search()
  },
  methods: {
    currencyCodeChange(e) {
      if (e.itemData) {
        this.searchFormModel.currencyCode = e.itemData.currencyCode
        this.searchFormModel.currencyName = e.itemData.currencyName
      } else {
        this.searchFormModel.currencyCode = null
        this.searchFormModel.currencyName = null
      }
    },
    dateTimeChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'S'] = this.getUnix(dayjs(e.startDate).format('YYYY-MM'))
        this.searchFormModel[flag + 'E'] = this.getUnix(dayjs(e.endDate).format('YYYY-MM'))
      } else {
        this.searchFormModel[flag + 'S'] = null
        this.searchFormModel[flag + 'E'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    startOpen1() {
      if (!this.currencyOptions.length) {
        this.getCurrency()
      }
    },
    getCurrency(val, currencyCode) {
      // 订单币种
      let params = { fuzzyParam: val?.text || '' }
      this.$API.masterData.getCurrencyByFilter(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.currencyCode}-${item.currencyName}`
          item.text = item.currencyName
          item.value = item.currencyCode
        })
        this.currencyOptions = list
        if (currencyCode) {
          this.topInfo.currency = currencyCode
        }
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.currencyOptions)
          })
        }
      })
    },
    // 分页器 - 切换分页
    currentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.handleCustomSearch()
    },
    // 分页器 - 切换页大小
    sizeChange(currentPageSize) {
      this.pageSettings.pageSize = currentPageSize
      this.pageSettings.currentPage = 1
      this.handleCustomSearch()
    },
    handleSearchReset() {
      for (let key in this.searchFormModel) {
        this.searchFormModel[key] = null
        if (key === 'isQueryAll') {
          this.searchFormModel[key] = false
        }
      }
      this.search()
    },
    // 自定义查询
    search() {
      this.pageSettings.currentPage = 1
      this.handleCustomSearch()
    },
    handleCustomSearch() {
      this.$store.commit('startLoading')
      const param = {
        page: {
          current: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      if (this.searchFormModel.reconciliationCode) {
        param.recoCodes = [this.searchFormModel.reconciliationCode]
      }
      this.$API.invoiceCollaboration
        .getInvoiceSupplierList(param)
        .then((res) => {
          if (res.code === 200) {
            this.$set(this.slotComponentConfig[0].grid, 'dataSource', res.data.records)
            this.pageSettings.totals = Math.ceil(
              Number(res.data.total) / this.pageSettings.pageSize
            )
            this.pageSettings.totalRecordsCount = res.data.total
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        page: {
          current: 1,
          pages: 0,
          size: 20
        }
      }
      this.$API.receiptAndDelivery
        .getFactoryInfo(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 公司 change
    companyCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.companyId = itemData.id
        this.searchFormModel.companyCode = itemData.customerCode
        this.searchFormModel.companyName = itemData.customerName
      } else {
        this.searchFormModel.companyId = ''
        this.searchFormModel.companyCode = ''
        this.searchFormModel.companyName = ''
      }
    },
    // 工厂 change
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.searchFormModel.factoryId = itemData.id
        this.searchFormModel.factoryCode = itemData.factoryCode
        this.searchFormModel.factoryName = itemData.factoryName
      } else {
        this.searchFormModel.factoryId = ''
        this.searchFormModel.factoryCode = ''
        this.searchFormModel.factoryName = ''
      }
    },
    // 获取主数据-获取指定组织下指定组织层级节点列表 业务公司
    getCompany(args) {
      const { updateData, setSelectData } = args
      let obj = {
        fuzzyNameOrCode: ''
      }
      this.$API.masterData
        .getCustomer(obj)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.companyOptions = addCodeNameKeyInList({
              firstKey: 'customerCode',
              secondKey: 'customerName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.companyOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // ToolBar
    handleClickToolBar(args) {
      const { toolbar, grid } = args
      const selectedRecords = grid.getSelectedRecords()

      const commonToolbar = [
        'InvoiceAdd',
        'more-option-btn',
        'InvoiceExport',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'resetDataByLocal',
        'filterDataByLocal'
      ]
      if (selectedRecords.length == 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const idList = []
      const headerIdList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
        headerIdList.push(item.headerId)
      })

      if (
        (toolbar.id === 'InvoiceSubmit' ||
          toolbar.id === 'InvoiceEdit' ||
          toolbar.id === 'InvoiceDelete') &&
        !selectedRecords.every(
          (i) => !i.reconciliationHeaderCodes[0] || i.reconciliationHeaderCodes[0] === '0'
        )
      ) {
        this.$toast({
          content: this.$t('月结对账单不能操作！'),
          type: 'warning'
        })
        return
      }
      // if (toolbar.id === 'ApplyInvoice') {
      //   // 申请开票
      //   this.handleApplyInvoice({ idList, headerIdList, selectedRecords })
      // } else if (toolbar.id === 'InvoiceExport') {
      //   this.handleExport()
      // }
      if (toolbar.id === 'Print') {
        this.print(idList)
      } else if (toolbar.id === 'InvoiceExport') {
        this.handleExport()
      } else if (toolbar.id === 'InvoiceDelete') {
        this.handleDelete(selectedRecords)
      } else if (toolbar.id === 'InvoiceAdd') {
        this.handleAdd()
      } else if (toolbar.id === 'InvoiceSubmit') {
        this.handleSubmit(selectedRecords)
      } else if (toolbar.id === 'InvoiceEdit') {
        // if (selectedRecords[0]['syncStatus'] !== 0 || selectedRecords[0]['status'] !== 0) {
        if (selectedRecords.some((i) => i.syncStatus !== 0 || (i.status !== 0 && i.status !== 4))) {
          this.$toast({
            content: this.$t('只能操作发票状态为待提交、已退回且同步状态未同步的数据！'),
            type: 'warning'
          })
          return
        }
        this.handleEdit(selectedRecords)
      }
    },
    handleSubmit(selectedRecords) {
      const invoiceIdList = []
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        if (item.status !== 0 && item.status !== 4) {
          this.$toast({ content: this.$t('请选择状态为待提交或已退回的发票'), type: 'warning' })
          return
        }
        invoiceIdList.push(item.id)
      }
      this.$API.invoiceCollaboration.submitInvoiceSup({ invoiceIdList }).then((res) => {
        const { code } = res
        if (code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          // 刷新当前 Grid
          this.search()
        }
      })
    },
    handleAdd() {
      this.$dialog({
        modal: () => import('../components/addDialog/index.vue'),
        data: {
          title: this.$t('新增')
        },
        success: () => {
          this.search()
        }
      })
    },
    handleEdit(selectedRecords) {
      // 根据id 获取单据数据
      this.$dialog({
        modal: () => import('../components/addDialog/index.vue'),
        data: {
          title: this.$t('编辑'),
          selectedRecords
        },
        success: () => {
          this.search()
        }
      })
    },
    handleDelete(selectedRecords) {
      const idList = selectedRecords.map((i) => i.id)
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .deleteInvoiceSupplier({ idList })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.search()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    print(idList) {
      this.$API.invoiceCollaboration.printInvoiceSupplier({ idList: idList }).then((res) => {
        if (res?.data?.type === 'application/json') {
          const reader = new FileReader()
          reader.readAsText(res?.data, 'utf-8')
          reader.onload = () => {
            const readerRes = reader.result
            const resObj = JSON.parse(readerRes)
            Vue.prototype.$toast({
              content: resObj.msg,
              type: 'error'
            })
          }
          return
        }
        const content = res.data
        this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
        // this.pdfUrl = window.URL.createObjectURL(
        //   new Blob([content], { type: 'text/html;charset=utf-8' })
        // )
        // window.open(this.pdfUrl);
        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = this.pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
      }, 100)
    },
    // CellTool
    handleClickCellTool(args) {
      const { tool, data } = args
      if (tool.id === 'UploadInvoice') {
        // 可开票单据-上传发票
        this.goToInvoiceDetailSupplier({
          headerInfo: data,
          entryType: ConstantType.edit
        })
      } else if (tool.id === 'EditInvoice') {
        // 发票列表-发票编辑
        this.handleEditInvoice({ data })
      } else if (tool.id === 'DeleteInvoice') {
        // 发票列表-发票删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            this.deleteReconciliationInvoiceV2BatchDelete({
              ids: [data.id]
            })
          }
        })
      } else if (tool.id === 'SubmitInvoice') {
        // 发票列表-发票提交
        this.postReconciliationInvoiceV2BatchSubmit({
          ids: [data.id]
        })
      } else if (tool.id === 'CloseInvoice') {
        // 发票列表-发票关闭
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认关闭选中的数据？')
          },
          success: () => {
            this.deleteReconciliationInvoiceV2BatchClose({
              ids: [data.id]
            })
          }
        })
      } else if (tool.id === 'SubmitBill') {
        // 单据提交
        const params = {
          id: data.id // 对账单ID
        }
        // 租户级-供方对账单发票信息-v2接口-提交
        this.postReconciliationInvoiceV2SubmitByHeader(params)
      }
    },
    // CellTitle
    handleClickCellTitle(args) {
      const { field, data, tabIndex, fieldValue } = args
      if (field === 'reconciliationCode') {
        // 点击 单据号
        this.handleReconciliationCode({ data, tabIndex })
      } else if (field === 'attachementCount') {
        // 点击 发票附件 数量
        this.handleAttachementCount({ data })
      } else if (field === 'reconciliationItemCount') {
        // 点击 关联明细 数量
        this.handleReconciliationItemCountClick(data)
      } else if (field === 'reconciliationHeaderCodes') {
        // 点击 关联对账单号
        this.handleReconciliationHeaderCodes({
          data,
          fieldValue
        })
      }
    },
    // 申请开票
    handleApplyInvoice(args) {
      const { idList: itemIds, headerIdList, selectedRecords } = args
      // 校验选择的明细行数据币种是否相同
      // if (!this.checkCurrency({ selectedRecords })) {
      //   return
      // }
      let reconciliationTaxedAmount = 0 // 明细汇总含税金额
      let reconciliationUntaxAmount = 0 // 明细汇总未税金额

      selectedRecords.forEach((item) => {
        // 明细汇总 含税金额 = 执行含税总价 每行相加
        reconciliationTaxedAmount = item.reconciliationTaxedAmount
        // 明细汇总 未税金额 = 执行未税总价 每行相加
        reconciliationUntaxAmount = item.reconciliationUntaxAmount
      })

      // 明细汇总 税额 = 明细汇总 含税金额 - 明细汇总 未税金额
      const reconciliationTaxAmount = bigDecimal.subtract(
        reconciliationTaxedAmount,
        reconciliationUntaxAmount
      ) // 明细汇总 税额

      // 获取明细表头后跳转到发票编辑页面
      this.goToInvoiceEditSupplier({
        headerInfo: {
          reconciliationIds: headerIdList, // 关联对账单id列表
          status: null, // 发票状态
          itemIds, // 选中的明细行 ID 列表
          highLowInfoIds: [], // 选中的高低开行 ID 列表

          // 关联明细行
          reconciliationItemCount: itemIds.length, // 明细行数
          reconciliationTaxedAmount, // 明细汇总含税金额
          reconciliationUntaxAmount, // 明细汇总未税金额
          reconciliationTaxAmount, // 明细汇总税额

          // 关联高低开
          taxedHighLow: 0, // 高低开汇总含税金额
          untaxedHighLow: 0, // 高低开汇总未税金额
          highLowInfoTaxAmount: 0, // 高低开汇总税额

          // 发票信息
          id: 0, // 发票id
          invoiceTypeCode: null, // 发票类型
          invoiceTypeName: '', // 发票类型名称
          invoiceCode: '', // 发票代码
          invoiceNum: '', // 发票号
          invoiceTime: null, // 开票日期
          invoiceUntaxAmount: reconciliationUntaxAmount, // 发票未税金额 默认等于明细汇总未税金额
          invoiceTaxAmount: reconciliationTaxAmount, // 税额 默认等于明细汇总税额
          invoiceTaxedAmount: reconciliationTaxedAmount, // 发票含税金额 默认等于明细汇总含税金额
          supplierRemark: '' // 供应商备注
        }, // rowData
        entryType: InvoiceEditType.Add, // 页面类型 编辑
        dataItemAsyncConfig: {},
        dataItemDataSource: selectedRecords, // 勾选的明细行数据
        dataItemColumnData: formatTableColumnData({
          tab: Tab.statementDetails,
          data: this.asyncStatementDetailsColumnData,
          allowFiltering: false
        }), // 明细行表头
        highLowInfoAsyncConfig: {
          url: `${BASE_TENANT}/reconciliationHighLowSupplier/queryByReconciliationIdsAndInvoice`,
          params: {
            invoiceId: 0, // 发票新增时传 0，编辑时传 发票id
            reconciliationIds: headerIdList // 关联对账单id列表
          },
          recordsPosition: 'data'
        }
      })
    },
    // 校验选择的明细行数据币种是否相同
    checkCurrency() {
      // const { selectedRecords } = args
      let valid = true
      // 有 currencyCode 优先用 currencyCode 校验否则用 currencyId
      // const beCheckedCurrencyCode = selectedRecords[0].currencyCode
      // const beCheckedCurrencyId = selectedRecords[0].currencyId
      // if (beCheckedCurrencyCode) {
      //   if (!selectedRecords.every((i) => i.currencyCode === beCheckedCurrencyCode)) {
      //     this.$toast({
      //       content: this.$t('请选择相同币种的明细'),
      //       type: 'warning'
      //     })
      //     valid = false
      //   }
      // } else {
      //   if (!selectedRecords.every((i) => i.currencyId === beCheckedCurrencyId)) {
      //     this.$toast({
      //       content: this.$t('请选择相同币种的明细'),
      //       type: 'warning'
      //     })
      //     valid = false
      //   }
      // }
      return valid
    },
    // 点击 单据号
    handleReconciliationCode(args) {
      const { data, tabIndex } = args
      // 从 可开票单据 跳转 单据详情页
      // 将 lastTabIndex 放到 localStorage 客户对账协同（供方）-详情 读
      localStorage.setItem('lastTabIndex', JSON.stringify(tabIndex))
      this.goToInvoiceDetailSupplier({
        headerInfo: data,
        entryType: ConstantType.look
      })
    },
    // 点击 发票附件 数量
    handleAttachementCount(args) {
      const { data } = args
      // 获取发票附件数据 后 显示 发票附件列表 弹框
      const params = {
        id: data.id
      }
      this.apiStartLoading()
      // 获取发票附件列表 租户级-供方对账单发票信息-v2接口-查询附件列表
      this.$API.invoiceCollaboration
        .postReconciliationInvoiceV2FileList(params)
        .then((res) => {
          this.apiEndLoading()
          const fileData = res?.data || []
          // 显示附件查看弹框
          const dialogParams = {
            fileData: fileData,
            isView: true,
            title: this.$t('发票附件')
          }
          this.$refs.uploaderDialog.dialogInit(dialogParams)
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 点击 关联明细 数量
    handleReconciliationItemCountClick(data) {
      const params = {
        tenantId: data.tenantId,
        reconciliationTypeCode: data.reconciliationTypeCode
      }
      this.apiStartLoading()
      // 获取 明细行 表头
      this.$API.reconciliationCollaboration
        .postReconciliationWaitSupplierGetFields(params)
        .then((res) => {
          this.apiEndLoading()
          const fields = res.data || []
          const columnData = formatTableColumnData({
            tab: Tab.statementDetails,
            data: fields
          })
          // 显示明细行列表弹框 查看
          this.$refs.billDetailsTableDialog.dialogInit({
            title: this.$t('关联明细行列表'),
            actionType: BillDetailsTableDialogActionType.view,
            dataItemAsyncConfig: {
              // 新增发票时，dataId传0、defaultRule里面传headerId即可；
              // 修改发票时，dataId传发票id，defaultRule里面传headerId，能查出来关联了发票的明细行以及未关联发票的明细行
              // 查看发票明细行时，dataId传发票id，在defaultRule里面再传invoiceId对应当前发票id，只查出来当前发票关联的明细行
              url: `${BASE_TENANT}/supplier/reconciliationHeader/reconciliation-item-list`, // 发票协同-供方-查询对账明细
              condition: 'and',
              params: {
                dataId: data.id // 发票新增时传 0，编辑、查看时传 发票id
              },
              defaultRules: [
                {
                  field: 'invoiceId',
                  operator: 'equal',
                  value: data.id
                }
              ]
            },
            dataItemColumnData: columnData,
            // 高低开
            highLowInfoAsyncConfig: {
              url: `${BASE_TENANT}/reconciliationHighLowSupplier/queryByInvoiceId`,
              params: {
                id: data.id // 发票id
              },
              recordsPosition: 'data'
            }
          })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 点击 关联对账单号
    handleReconciliationHeaderCodes(args) {
      const { data, fieldValue: reconciliationCode } = args
      // 从 发票列表 跳转 单据详情页
      const reconciliationHeaderIdIndex = data.reconciliationHeaderCodes.findIndex(
        (item) => item === reconciliationCode
      )
      // 请求 api 根据单据 code、id 获取单据数据
      const params = {
        page: { current: 1, size: 10 },
        condition: 'and',
        defaultRules: [
          {
            field: 'reconciliationCode',
            type: 'string',
            operator: 'equal',
            value: reconciliationCode
          },
          {
            field: 'id',
            type: 'string',
            operator: 'equal',
            value: data.reconciliationHeaderIds[reconciliationHeaderIdIndex].supplierHeaderId
          }
        ]
      }
      this.apiStartLoading()
      // 获取单据信息 租户级-发票协同-供方主单-对账信息
      this.$API.invoiceCollaboration
        .postSupplierReconciliationHeaderQueryBuilder(params)
        .then((res) => {
          this.apiEndLoading()
          const reconciliationHeaderInfo = res?.data?.records[0] || {}
          // 从 可开票单据 跳转 单据详情页
          // 将 lastTabIndex 放到 localStorage 客户对账协同（供方）-详情 读
          localStorage.setItem('lastTabIndex', JSON.stringify(2))
          this.goToInvoiceDetailSupplier({
            headerInfo: reconciliationHeaderInfo,
            entryType: ConstantType.look
          })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 编辑发票
    handleEditInvoice(args) {
      const { data: rowData } = args
      const params = {
        tenantId: rowData.tenantId,
        reconciliationTypeCode: rowData.reconciliationTypeCode
      }
      this.apiStartLoading()
      // 获取 明细行 表头
      this.$API.reconciliationCollaboration
        .postReconciliationWaitSupplierGetFields(params)
        .then((res) => {
          this.apiEndLoading()
          const dataItemColumnData = formatTableColumnData({
            tab: Tab.statementDetails,
            data: res.data || []
          })
          if (dataItemColumnData?.length == 0) {
            this.$toast({
              content: this.$t('当前单据类型，明细行表头配置为空'),
              type: 'warning'
            })
            return
          }

          let reconciliationIds = [] // 关联对账单id列表 只会有一个，因为只有基于可开票单据新增的发票可编辑
          rowData.reconciliationHeaderIds.forEach((item) => {
            reconciliationIds.push(item.supplierHeaderId)
          })

          // 明细行
          const dataItemAsyncConfig = {
            // 待提交 状态下 可编辑 可以修改明细和高低开信息，因为都是基于可开票单据新增的发票
            [InvoiceStatus.pendingSubmission]: {
              // 新增发票时，dataId传0、defaultRule里面传headerId即可；
              // 修改发票时，dataId传发票id，defaultRule里面传headerId，能查出来关联了发票的明细行以及未关联发票的明细行
              // 查看发票明细行时，dataId传发票id，在defaultRule里面再传invoiceId对应当前发票id，只查出来当前发票关联的明细行
              url: `${BASE_TENANT}/supplier/reconciliationHeader/reconciliation-item-list`,
              condition: 'and',
              params: {
                dataId: rowData.id // 发票新增时传 0，编辑、查看时传 发票id
              },
              defaultRules: [
                {
                  field: 'headerId',
                  operator: 'equal',
                  value: reconciliationIds[0]
                }
              ]
            },
            // 退回 状态下 仅查看 不可以修改明细和高低开信息
            [InvoiceStatus.returned]: {
              // 新增发票时，dataId传0、defaultRule里面传headerId即可；
              // 修改发票时，dataId传发票id，defaultRule里面传headerId，能查出来关联了发票的明细行以及未关联发票的明细行
              // 查看发票明细行时，dataId传发票id，在defaultRule里面再传invoiceId对应当前发票id，只查出来当前发票关联的明细行
              url: `${BASE_TENANT}/supplier/reconciliationHeader/reconciliation-item-list`,
              condition: 'and',
              params: {
                dataId: rowData.id // 发票新增时传 0，编辑、查看时传 发票id
              },
              defaultRules: [
                {
                  field: 'invoiceId',
                  operator: 'equal',
                  value: rowData.id
                }
              ]
            }
          }

          // 高低开
          const highLowInfoAsyncConfig = {
            // 待提交 状态下 可编辑 可以修改明细和高低开信息，因为都是基于可开票单据新增的发票
            [InvoiceStatus.pendingSubmission]: {
              url: `${BASE_TENANT}/reconciliationHighLowSupplier/queryByReconciliationIdsAndInvoice`,
              params: {
                invoiceId: rowData.id || 0, // 发票新增时传 0，编辑时传 发票id
                reconciliationIds: reconciliationIds // 关联对账单id列表 只会有一个，因为只有基于可开票单据新增的发票可编辑
              },
              recordsPosition: 'data'
            },
            // 退回 状态下 仅查看 不可以修改明细和高低开信息
            [InvoiceStatus.returned]: {
              url: `${BASE_TENANT}/reconciliationHighLowSupplier/queryByInvoiceId`,
              params: {
                id: rowData.id // 发票id
              },
              recordsPosition: 'data'
            }
          }

          const highLowInfoTaxAmount = bigDecimal.subtract(
            rowData.taxedHighLow || 0, // 高低开含税总金额
            rowData.untaxedHighLow || 0 // 高低开未税总金额
          )
          // 获取明细表头后跳转到发票编辑页面
          this.goToInvoiceEditSupplier({
            headerInfo: {
              reconciliationIds, // 关联对账单id列表
              status: rowData.status, // 发票状态
              itemIds: rowData.itemIds, // 选中的明细行 ID 列表
              highLowInfoIds: rowData.highLowInfoIds, // 选中的高低开行 ID 列表

              // 关联明细行
              reconciliationItemCount: rowData.itemIds ? rowData.itemIds.length : 0, // 明细行数
              reconciliationTaxedAmount: rowData.reconciliationTaxedAmount, // 明细汇总含税金额
              reconciliationUntaxAmount: rowData.reconciliationUntaxAmount, // 明细汇总未税金额
              reconciliationTaxAmount: rowData.reconciliationTaxAmount, // 明细汇总税额

              // 关联高低开
              taxedHighLow: rowData.taxedHighLow, // 高低开汇总含税金额
              untaxedHighLow: rowData.untaxedHighLow, // 高低开汇总未税金额
              highLowInfoTaxAmount: Number(highLowInfoTaxAmount), // 高低开汇总税额

              // 发票信息
              id: rowData.id, // 发票id
              invoiceTypeCode: rowData.invoiceTypeCode, // 发票类型
              invoiceTypeName: rowData.invoiceTypeName, // 发票类型名称
              invoiceCode: rowData.invoiceCode, // 发票代码
              invoiceNum: rowData.invoiceNum, // 发票号
              invoiceTime: rowData.invoiceTime, // 开票日期
              invoiceUntaxAmount: rowData.invoiceUntaxAmount, // 发票未税金额
              invoiceTaxAmount: rowData.invoiceTaxAmount, // 税额
              invoiceTaxedAmount: rowData.invoiceTaxedAmount, // 发票含税金额
              supplierRemark: rowData.supplierRemark // 供应商备注
            }, // rowData
            entryType: InvoiceEditType.Edit, // 页面类型 编辑
            dataItemAsyncConfig: dataItemAsyncConfig[rowData.status],
            dataItemColumnData, // 明细行表头
            highLowInfoAsyncConfig: highLowInfoAsyncConfig[rowData.status]
          })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 跳转到发票编辑页面
    goToInvoiceEditSupplier(data) {
      localStorage.setItem('invoiceEditSupplierData', JSON.stringify(data))
      // 跳转到发票编辑页面
      this.$router.push({
        name: 'invoice-edit-supplier-tv',
        query: {}
      })
    },
    // 获取客户对象
    getCustomerReconciliationType() {
      this.$API.invoiceCollaboration
        .getCustomerReconciliationType()
        .then((res) => {
          const data = res?.data || []
          this.currentPurTenantId = data[0].purTenantId
          data.forEach((item) => {
            this.slotTabList.push({
              title: item.purTenantName,
              purTenantId: item.purTenantId,
              types: item.types
            })
          })
          this.setSlotTypeList(0)
        })
        .catch(() => {})
    },
    // 租户级-供方对账单发票信息-v2接口-批量删除
    deleteReconciliationInvoiceV2BatchDelete(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .deleteReconciliationInvoiceV2BatchDelete(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 发票删除成功 刷新 发票列表 tab
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-供方对账单发票信息-v2接口-批量关闭
    deleteReconciliationInvoiceV2BatchClose(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .deleteReconciliationInvoiceV2BatchClose(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 发票删除成功 刷新 发票列表 tab
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-供方对账单发票信息-v2接口-批量提交
    postReconciliationInvoiceV2BatchSubmit(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postReconciliationInvoiceV2BatchSubmit(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 发票删除成功 刷新 发票列表 tab
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 提交对账发票信息
    postReconciliationInvoiceV2SubmitByHeader(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postReconciliationInvoiceV2SubmitByHeader(params)
        .then((res) => {
          this.apiEndLoading()
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 单据成功 刷新 可开票单据 tab
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },

    // 切换客户列表
    handleSelectSlotTab(args) {
      const { itemData } = args
      this.currentSlotTypeTabIndex = 0
      const currentPurTenantIdIndex = this.slotTabList.findIndex(
        (item) => item.purTenantId == itemData.purTenantId
      )
      this.setSlotTypeList(currentPurTenantIdIndex)
    },

    // 设置 对账类型 列表
    setSlotTypeList(index) {
      this.slotTabTypeList = []
      this.slotTabList[index].types.forEach((item) => {
        this.slotTabTypeList.push({
          title: item.name,
          code: item.code
        })
      })
      this.setSlotTemplateConfig()
    },

    // 切换对账类型
    // handleSelectTypeSlotTab(e) {
    //   this.currentSlotTypeTabIndex = e
    //   this.setSlotTemplateConfig()
    // },

    // 处理列模板的配置 可开票明细
    // setSlotTemplateConfig() {
    //   const params = {
    //     tenantId: this.currentPurTenantId,
    //     reconciliationTypeCode: this.slotTabTypeList[this.currentSlotTypeTabIndex].code
    //   }
    //   this.$API.reconciliationCollaboration
    //     .postReconciliationWaitSupplierGetFields(params)
    //     .then((res) => {
    //       const columnData = res.data || []
    //       // 固定在第一列添加单据号字段
    //       columnData.unshift({
    //         code: 'reconciliationHeaderCodes',
    //         name: this.$t('单据号')
    //       })
    //       this.asyncStatementDetailsColumnData = columnData // 动态表头将会传递到详情页
    //       const col = formatTableColumnData({
    //         tab: Tab.statementDetails,
    //         data: columnData,
    //         allowFiltering: true
    //       })
    //       console.log('123')
    //       console.log(columnData)
    //       console.log('456')
    //       this.$set(this.slotComponentConfig[0].grid, 'columnData', [ColumnCheckbox].concat(col))

    //       // 设置表格 gridId 配置
    //       const tableUUID = '666730a4-a975-42d9-9eee-10972e2598ad' // 表格 uuid
    //       const currentTableCode = this.slotTabTypeList[this.currentSlotTypeTabIndex].code // 当前对账类型code
    //       this.$set(
    //         this.slotComponentConfig[0],
    //         'gridId',
    //         this.$md5(`${tableUUID}${currentTableCode}`)
    //       )
    //       this.search()
    //       // this.$set(this.slotComponentConfig[0].grid, 'asyncConfig', {
    //       //   url: `${BASE_TENANT}/supplier/reconciliationHeader/reconciliation-item-list`, // 发票协同-供方-查询对账明细
    //       //   params: {
    //       //     dataId: 0
    //       //   },
    //       //   defaultRules: [
    //       //     {
    //       //       field: 'reconciliationTypeCode',
    //       //       operator: 'equal',
    //       //       value: this.slotTabTypeList[this.currentSlotTypeTabIndex].code
    //       //     }
    //       //   ]
    //       // })
    //       this.isShowSlotTemplateRef = true // 显示表格
    //     })
    //     .catch(() => {
    //       this.isShowSlotTemplateRef = false // 不显示表格
    //     })
    // },

    // 显示表格文件弹窗
    showFileBaseInfo(args) {
      const { value } = args
      const dialogParams = {
        fileData: cloneDeep(value),
        isView: true,
        title: this.$t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },

    // 跳转到 发票详情页面
    goToInvoiceDetailSupplier(data) {
      const { headerInfo, entryType } = data
      // 发票详情 页面参数
      const params = {
        reconciliationCode: headerInfo.reconciliationCode, // 对账单号
        headerInfo, // 头部信息 行数据
        entryType // 页面类型
      }
      localStorage.setItem('summarySupplierV2Data', JSON.stringify(params))
      // 跳转到 发票详情页面
      this.$router.push({
        name: 'invoice-detail-supplier-v2',
        query: {}
      })
    },
    // 导出
    handleExport() {
      const params = {
        isDownLoad: true,
        page: { current: 1, size: 10000 },
        ...this.searchFormModel
      } // 筛选条件
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .exportInvoiceList(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height > div.tabs-line {
  display: flex;
  align-items: center;
}
.slot-select {
  display: flex;
  position: relative;
  align-items: center;
}
.pur-tenant-select {
  margin: 25px;
}
/deep/ .supReconTypeTabs {
  display: flex;
  width: calc(100% - 225px);
  flex: 1;
  overflow: auto;

  .mt-tabs-container {
    width: calc(100% - 225px);
  }
}
</style>
