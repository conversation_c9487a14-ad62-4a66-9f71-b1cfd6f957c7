import { i18n } from '@/main.js'
export const Toolbar = [
  {
    code: 'Add',
    name: i18n.t('新增'),
    icon: 'vxe-icon-square-plus',
    status: 'info'
  },
  {
    code: 'Delete',
    name: i18n.t('删除'),
    icon: 'vxe-icon-edit',
    status: 'info'
  },
  {
    code: 'Import',
    name: i18n.t('导入'),
    icon: 'vxe-icon-cloud-upload',
    status: 'info'
  }
]
export const reconciliationTypeList = [
  { value: 'A', label: i18n.t('标准'), cssClass: '' },
  // { value: 'C', label: i18n.t('标准追溯对账单'), cssClass: '' },
  { value: 'B', label: i18n.t('寄售'), cssClass: '' }
  // { value: 'D', label: i18n.t('寄售追溯对账单'), cssClass: '' },
  // { value: 'E', label: i18n.t('采购/外协对账单'), cssClass: '' },
  // { value: 'F', label: i18n.t('注塑申购对账单'), cssClass: '' }
]

const months = []
const monthsData = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']
for (let i = 0; i < monthsData.length; i++) {
  const month = monthsData[i]
  months.push({ label: String(month), value: String(month) })
}
export const monthList = months

export const columnsData = [
  {
    type: 'checkbox',
    minWidth: 70,
    ignore: true
    // fixed: 'left'
  },
  // {
  //   type: 'seq',
  //   title: i18n.t('序号'),
  //   minWidth: 50
  // },
  {
    field: 'companyCode',
    title: i18n.t('公司代码'),
    minWidth: 110,
    showOverflow: true,
    // editRender: { name: 'input' }
    editRender: {},
    slots: {
      edit: 'companyCodeEdit'
    }
  },
  {
    field: 'factoryCode',
    title: i18n.t('工厂代码'),
    minWidth: 110,
    showOverflow: true,
    // editRender: { name: 'input' }
    editRender: {},
    slots: {
      edit: 'factoryCodeEdit'
    }
  },
  {
    field: 'arriveYear',
    title: i18n.t('到货年份'),
    minWidth: 110,
    showOverflow: true,
    editRender: { name: 'input' }
  },
  {
    field: 'arriveMonth',
    title: i18n.t('到货月份'),
    minWidth: 110,
    showOverflow: true,
    editRender: {
      name: 'select',
      options: monthList,
      props: { placeholder: '请选择到货月份' },
      attrs: { disabled: false, style: 'background: #fff' }
    }
  },
  {
    field: 'reconciliationTypeCode',
    title: i18n.t('标准/寄售'),
    minWidth: 120,
    align: 'center',
    editRender: {
      name: 'select',
      options: reconciliationTypeList,
      props: { placeholder: '请选择标准/寄售' },
      attrs: { disabled: false, style: 'background: #fff' }
    }
  },
  {
    field: 'invoiceNum',
    title: i18n.t('发票号'),
    minWidth: 110,
    showOverflow: true,
    editRender: { name: 'input' }
  },
  {
    field: 'currencyCode',
    title: i18n.t('币种'),
    minWidth: 110,
    showOverflow: true,
    // editRender: { name: 'input' }
    editRender: {},
    slots: {
      edit: 'currencyCodeEdit'
    }
  },
  {
    field: 'invoiceUntaxAmount',
    title: i18n.t('不含税金额'),
    minWidth: 120,
    align: 'right',
    showOverflow: true,
    editRender: {},
    // editRender: { name: 'input', attrs: { type: 'number' } },
    slots: {
      edit: 'invoiceUntaxAmountEdit'
    }
  },
  {
    field: 'invoiceTaxAmount',
    title: i18n.t('税额'),
    minWidth: 100,
    align: 'right',
    showOverflow: true,
    editRender: {},
    // editRender: { name: 'input', attrs: { type: 'number' } },
    slots: {
      edit: 'invoiceTaxAmountEdit'
    }
  },
  {
    field: 'invoiceTaxedAmount',
    title: i18n.t('含税金额'),
    minWidth: 110,
    align: 'right',
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
    // slots: {
    //   edit: 'invoiceTaxedAmountEdit'
    // }
  },
  {
    field: 'invoiceTime',
    title: i18n.t('开票日期'),
    minWidth: 110,
    showOverflow: true,
    editRender: { name: 'input', attrs: { type: 'date' } }
  },
  {
    field: 'salerTaxPayerIc',
    title: i18n.t('销方纳税人识别码'),
    minWidth: 170,
    showOverflow: true,
    editRender: { name: 'input' }
  },
  {
    field: 'salesGoodsListQty',
    title: i18n.t('销货清单份数'),
    minWidth: 140,
    showOverflow: true,
    editRender: { name: 'input', attrs: { type: 'number' } }
  },
  {
    field: 'invoiceCode',
    title: i18n.t('发票代码'),
    minWidth: 150,
    showOverflow: true,
    editRender: { name: 'input' }
  },
  {
    field: 'checkCode',
    title: i18n.t('后六位校验码'),
    minWidth: 150,
    showOverflow: true,
    editRender: { name: 'input' }
  },
  {
    field: 'supplierRemark',
    title: i18n.t('供应商备注'),
    minWidth: 150,
    showOverflow: true,
    editRender: { name: 'input' }
  },
  {
    field: 'errorInfo',
    title: i18n.t('错误信息'),
    minWidth: 150,
    showOverflow: true
  }
]

export const NewRowData = {
  companyCode: null, // 公司代码
  companyName: null, // 公司名称
  factoryCode: null, // 工厂代码
  factoryName: null, // 工厂name
  arriveYear: null, // 到货年份
  arriveMonth: '01', // 到货月份
  reconciliationTypeCode: 'A', // 关联单据类型编码
  reconciliationTypeName: i18n.t('标准对账单'), // 关联单据类型名称
  invoiceNum: null, // 发票号
  currencyCode: null, // 货币编码
  currencyName: null, // 货币名称
  invoiceUntaxAmount: 0, // 发票不含税金额
  invoiceTaxAmount: 0, // 发票税额
  invoiceTaxedAmount: 0, // 发票含税金额
  invoiceTime: null, // 开票日期
  salerTaxPayerIc: null, // 销方纳税人识别码
  salesGoodsListQty: null, // 销货清单份数
  supplierRemark: null // 供应商备注
}
