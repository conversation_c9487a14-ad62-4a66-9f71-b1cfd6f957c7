import utils from '@/utils/utils'
import { i18n } from '@/main.js'
import {
  InvoiceStatusOptions,
  SyncStatusOptions,
  InvoiceStatusCellTools,
  BillInvoiceStatusCellTools,
  SourceTypeOptions,
  ReconciliationDetailsStatusOptions,
  AdvanceInvoicingOptions,
  FrozenStatusOptions,
  PrePayStatusOptions,
  ProvisionalEstimateStatusOptions,
  ProvisionalEstimateStatusOptions1,
  ReconciliationDetailsTypeOptions,
  RealPriceStatusOptions,
  InOutTypeOptions
} from './constant'
import { MasterDataSelect } from '@/utils/constant'
import { Tab } from './constant'
import { ColumnComponent } from './columnComponent'
import { strByCharacterToArray } from '@/utils/utils'

export const ColumnCheckbox = {
  width: '50',
  type: 'checkbox',
  showInColumnChooser: false
}

// data: yyyy-mm-dd hh:mm:ss
export const timeToDate = (data) => {
  const { formatString, value } = data
  if (formatString && value) {
    const date = new Date(value)
    if (isNaN(date.getTime())) {
      return value
    } else {
      return utils.formateTime(date, formatString)
    }
  } else {
    return value
  }
}

// 格式化表格动态数据
export const formatTableColumnData = (args) => {
  const { data, tab, allowFiltering } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.field,
      headerText: col.headerText,
      width: data.length > 10 ? '200' : 'auto'
    }
    if (tab === Tab.statements) {
      // 可开票单据 tab
      if (defaultCol.field === 'reconciliationCode') {
        // 单据号
        defaultCol.width = '200'
        defaultCol.cellTools = [] // 使其可点击查看
      } else if (defaultCol.field === 'invoiceStatus') {
        // 单据开票状态
        defaultCol.width = '120'
        // defaultCol.valueConverter = {
        //   type: 'map',
        //   map: BillInvoiceStatusOptions
        // }
        defaultCol.cellTools = BillInvoiceStatusCellTools
      } else if (defaultCol.field === 'createTime') {
        // 创建时间
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
      }
    } else if (tab === Tab.statementDetails) {
      // 可开票明细
      defaultCol.width = data.length > 10 ? '200' : 'auto'
      if (allowFiltering) {
        defaultCol.allowFiltering = true // 允许过滤
      } else {
        // 弹框中不可过滤
        defaultCol.allowFiltering = false // 不允许过滤
      }
      const isVisibleList = ['supplierCode', 'supplierName'] // 不显示的动态字段
      if (isVisibleList.includes(defaultCol.field)) {
        defaultCol.visible = false
      }
      if (defaultCol.field === 'receiveTime') {
        // 对账明细-收货时间
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
      } else if (defaultCol.field === 'fileBaseInfoList') {
        // 对账明细-文件信息
        defaultCol.template = ColumnComponent.fileBaseInfoList
      } else if (defaultCol.field === 'termsUntaxedUnitPrice') {
        // 对账明细-条件（未税）单价
        defaultCol.width = '200'
      } else if (defaultCol.field === 'sourceType') {
        // 来源类型（枚举） 0: 上游流入1:第三方接口
        defaultCol.valueConverter = {
          type: 'map',
          map: SourceTypeOptions
        }
      } else if (defaultCol.field === 'status') {
        // 单据状态 0:待对账 1:已创建对账单
        defaultCol.valueConverter = {
          type: 'map',
          map: ReconciliationDetailsStatusOptions
        }
      } else if (defaultCol.field === 'syncStatus') {
        // 同步状态 0:否 1:是
        defaultCol.valueConverter = {
          type: 'map',
          map: SyncStatusOptions
        }
      } else if (defaultCol.field === 'advanceInvoicing') {
        // 提前开票 0:否 1:是
        defaultCol.valueConverter = {
          type: 'map',
          map: AdvanceInvoicingOptions
        }
      } else if (defaultCol.field === 'frozenStatus') {
        // 冻结标记 0:否 1:是
        defaultCol.valueConverter = {
          type: 'map',
          map: FrozenStatusOptions
        }
      } else if (defaultCol.field === 'prePayStatus') {
        // 是否预付 0-否；1-是
        defaultCol.valueConverter = {
          type: 'map',
          map: PrePayStatusOptions
        }
      } else if (defaultCol.field === 'itemVoucherDate') {
        // 物料凭证日期
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
      } else if (defaultCol.field === 'provisionalEstimateStatus') {
        if (defaultCol.headerText == '是否执行价') {
          defaultCol.valueConverter = {
            type: 'map',
            map: ProvisionalEstimateStatusOptions1
          }
        } else {
          defaultCol.valueConverter = {
            type: 'map',
            map: ProvisionalEstimateStatusOptions
          }
        }
      } else if (defaultCol.field === 'createTime') {
        // 创建时间
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
      } else if (defaultCol.field === 'updateTime') {
        // 最后修改时间
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
      } else if (defaultCol.field === 'type') {
        // 待对账类型:0-采购待对账；1-销售待对账
        defaultCol.valueConverter = {
          type: 'map',
          map: ReconciliationDetailsTypeOptions
        }
      } else if (defaultCol.field === 'realPriceStatus') {
        // 正式价标识 0-无正式价；1-有正式价
        defaultCol.valueConverter = {
          type: 'map',
          map: RealPriceStatusOptions
        }
      } else if (defaultCol.field === 'inOutType') {
        // 出入库类型 0-采购入库；1-采购出库；2-销售出库；3-销售退回
        defaultCol.valueConverter = {
          type: 'map',
          map: InOutTypeOptions
        }
      }
    } else if (tab === Tab.invoices) {
      // 发票列表
      if (defaultCol.field === 'status') {
        // 状态 发票状态
        defaultCol.width = '160'
        defaultCol.valueConverter = {
          type: 'map',
          map: InvoiceStatusOptions
        }
        defaultCol.cellTools = InvoiceStatusCellTools
      } else if (defaultCol.field === 'syncStatus') {
        // 同步状态
        defaultCol.width = '120'
        defaultCol.valueConverter = {
          type: 'map',
          map: SyncStatusOptions
        }
      } else if (defaultCol.field === 'invoiceTime') {
        // 开票时间
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: false
        })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (defaultCol.field === 'createTime') {
        // 创建时间
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (defaultCol.field === 'reviewTime') {
        // 财务审核时间
        defaultCol.template = ColumnComponent.timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (defaultCol.field === 'reconciliationHeaderCodes') {
        // 关联单据号
        defaultCol.width = '200'
        defaultCol.template = ColumnComponent.listInfoColumn({
          dataKey: defaultCol.field,
          isAbleClick: true,
          isShowLength: false,
          delimiter: '；'
        })
      } else if (defaultCol.field === 'reconciliationItemCount') {
        // 明细行
        defaultCol.allowFiltering = false
        defaultCol.ignore = true
        defaultCol.valueConverter = {
          type: 'function',
          filter: (data) => {
            return `${data}` // 使 number 0 可以显示
          }
        }
        defaultCol.cellTools = [] // 使其可点击查看
      } else if (defaultCol.field === 'attachementCount') {
        // 发票附件
        defaultCol.allowFiltering = false
        defaultCol.ignore = true
        defaultCol.valueConverter = {
          type: 'function',
          filter: (data) => {
            return `${data}` // 使 number 0 可以显示
          }
        }
        defaultCol.cellTools = [] // 使其可点击查看
      } else if (defaultCol.field === 'theHeaderTypeName') {
        // 关联单据类型 theHeaderTypeName FIXME: 暂时前端固定 对账单
        defaultCol.allowFiltering = false
        defaultCol.ignore = true
      }
    }
    colData.push(defaultCol)
  })

  return colData
}

// 表格数据序列化
export const serializeList = (list) => {
  if (list?.length > 0) {
    list.forEach((item) => {
      // 关联单据号 字符串转数组
      item.reconciliationHeaderCodes = strByCharacterToArray({
        str: item.reconciliationHeaderCodes || '',
        character: ','
      })
      // 关联单据id  转换：供方单据ID||采方单据ID,供方单据ID||采方单据ID => [{supplierHeaderId: 供方单据ID, buyerHeaderId: 采方单据ID},{...}]
      const headerIdsStr = item.reconciliationHeaderIds
      item.reconciliationHeaderIds = headerIdsStr.split(',').map((item) => {
        const billListItem = item.split('||') // ['供方单据ID', '采方单据ID']
        return {
          supplierHeaderId: billListItem[0],
          buyerHeaderId: billListItem[1]
        }
      })
      item.invoiceType = item.invoiceTypeCode // 发票类型
      item.invoiceTime = new Date(Number(item.invoiceTime)) // 开票时间
      item.theHeaderTypeName = i18n.t('对账单') // theHeaderTypeName FIXME: 暂时前端固定 对账单
    })
  }

  return list
}
