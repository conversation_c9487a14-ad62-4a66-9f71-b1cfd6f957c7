import { i18n } from '@/main.js'
import dayjs from 'dayjs'

export const invoiceStatusOptions = [
  { text: i18n.t('待提交'), value: 0, cssClass: 'col-active' },
  { text: i18n.t('待确认'), value: 1, cssClass: 'col-active' },
  { text: i18n.t('采购已确认'), value: 2, cssClass: 'col-active' },
  { text: i18n.t('已完成'), value: 3, cssClass: 'col-active' },
  { text: i18n.t('已退回'), value: 4, cssClass: 'col-active' }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'recoCode',
    title: i18n.t('对账单号'),
    minWidth: 120,
    slots: {
      default: 'recoNoDetault'
    }
  },
  {
    field: 'invoiceStatus',
    title: i18n.t('发票状态'),
    slots: {
      default: 'statusDefault'
    }
  },
  {
    field: 'recoTypeName',
    title: i18n.t('对账类型'),
    minWidth: 120
  },
  {
    field: 'currencyCode',
    title: i18n.t('币种')
  },
  {
    field: 'companyCode',
    title: i18n.t('公司'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.companyName : ''
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    }
  },
  {
    field: 'recoYearMonth',
    title: i18n.t('对账日期'),
    minWidth: 120
  },
  {
    field: 'executionTotalPriceTaxed',
    title: i18n.t('执行含税总价'),
    minWidth: 120
  },
  {
    field: 'executionTotalPriceUntaxed',
    title: i18n.t('执行未税总价'),
    minWidth: 120
  },
  {
    field: 'invoiceTaxAmt',
    title: i18n.t('发票税额'),
    minWidth: 120
  },
  {
    field: 'invoiceAmtTaxed',
    title: i18n.t('发票含税金额'),
    minWidth: 120
  },
  {
    field: 'invoiceAmtUntaxed',
    title: i18n.t('发票不含税金额'),
    minWidth: 140
  },
  {
    field: 'invoiceCode',
    title: i18n.t('发票代码'),
    minWidth: 120
  },
  {
    field: 'checkCode',
    title: i18n.t('后六位校验码'),
    minWidth: 120
  },
  {
    field: 'invoiceCustomerRemark',
    title: i18n.t('采方备注'),
    minWidth: 160
  },
  {
    field: 'invoiceSupplierRemark',
    title: i18n.t('供方备注'),
    minWidth: 160
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建日期'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue ? dayjs(Number(cellValue)).format('YYYY-MM-DD') : ''
    }
  }
]
