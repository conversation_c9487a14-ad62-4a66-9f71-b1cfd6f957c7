<template>
  <!-- 采购 -->
  <div class="orderConfig full-height vertical-flex-box">
    <mt-tabs
      class="toggle-tab"
      :e-tab="false"
      :data-source="tabSource"
      :selected-item="selectedItem"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <div class="toggle-content">
      <enInvoice v-show="tabIndex == 0" />
      <enInvoiceUpload v-show="tabIndex == 1" :is-detail="'2'" version="2" />
      <enInvoiceAll v-show="tabIndex == 2" />
      <enInvoiceOut v-show="tabIndex == 3" />
      <enInvoiceVn v-show="tabIndex == 4" />
    </div>

    <!-- <mt-template-page
      ref="templateRef1"
      :template-config="pageConfig"
      :permission-obj="permissionObj"
    >
      <div slot="slot-0" class="full-height">
        <enInvoice v-if="!isReload"></enInvoice>
      </div>
      <div slot="slot-1" class="full-height">
        <enInvoiceUpload :is-detail="'2'" version="2"></enInvoiceUpload>
      </div>
      <div slot="slot-2" class="full-height">
        <enInvoiceAll></enInvoiceAll>
      </div>
      <div slot="slot-3" class="full-height">
        <enInvoiceOut></enInvoiceOut>
      </div>
    </mt-template-page> -->
  </div>
</template>
<script>
export default {
  components: {
    enInvoice: () => import('./pages/enInvoice.vue'),
    enInvoiceUpload: () => import('./pages/enInvoiceUpload.vue'),
    enInvoiceAll: () => import('./pages/enInvoiceAll.vue'),
    enInvoiceOut: () => import('./pages/enInvoiceOut.vue'),
    enInvoiceVn: () => import('./pages/enInvoiceVn.vue')
  },
  data() {
    return {
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        }
      },
      pageConfig: [
        {
          title: this.$t('月结对账单开票')
        },
        {
          title: this.$t('返利/其他发票上传')
        },
        {
          title: this.$t('所有发票')
        },
        {
          title: this.$t('外发发票')
        }
      ],
      isReload: false,

      tabSource: [
        {
          title: this.$t('月结对账单开票')
        },
        {
          title: this.$t('返利/其他发票上传')
        },
        {
          title: this.$t('所有发票')
        },
        {
          title: this.$t('外发发票')
        },
        {
          title: this.$t('越南日结对账发票')
        }
      ],
      tabIndex: this.$route.query.currentIndex ?? 0,
      selectedItem: this.$route.query.currentIndex ?? 0
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.name === 'invoice-detail-supplier-v2-tv') {
        vm.isReload = !vm.isReload
        setTimeout(() => {
          vm.isReload = !vm.isReload
        })
      }
    })
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    }
  }
}
</script>
