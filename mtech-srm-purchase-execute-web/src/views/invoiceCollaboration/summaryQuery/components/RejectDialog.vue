<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    :width="500"
    :height="400"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="dialog-mt-form" ref="dialogRef" :model="formObject">
        <mt-form-item prop="reconciliationStatus" :label="$t('对账单状态')">
          <mt-select
            v-model="formObject.reconciliationStatus"
            :data-source="reconciliationStatusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="reconInvoiceStatus" :label="$t('对账单据的发票状态')">
          <mt-select
            v-model="formObject.reconInvoiceStatus"
            :data-source="reconInvoiceStatusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="invoiceStatus" :label="$t('发票状态')">
          <mt-select
            v-model="formObject.invoiceStatus"
            :data-source="invoiceStatusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      //v-model获取的值
      formObject: {
        reconciliationStatus: null,
        reconInvoiceStatus: null,
        invoiceStatus: null
      },
      //必填项
      formRules: {
        reconciliationStatus: [
          {
            required: true,
            message: this.$t('请选择对账单状态'),
            trigger: 'blur'
          }
        ],
        reconInvoiceStatus: [
          {
            required: true,
            message: this.$t('请选择对账单据的发票状态'),
            trigger: 'blur'
          }
        ],
        invoiceStatus: [
          {
            required: true,
            message: this.$t('请选择发票状态'),
            trigger: 'blur'
          }
        ]
      },
      reconciliationStatusOptions: [
        { text: this.$t('关闭'), value: -1 },
        { text: this.$t('未发布'), value: 0 },
        { text: this.$t('发布待确认'), value: 1 },
        { text: this.$t('反馈正常'), value: 2 },
        { text: this.$t('反馈异常'), value: 3 }
      ],
      reconInvoiceStatusOptions: [
        { text: this.$t('待开票'), value: 0 },
        { text: this.$t('已开票'), value: 1 },
        { text: this.$t('已确认'), value: 2 },
        { text: this.$t('财务确认'), value: 3 }
      ],
      invoiceStatusOptions: [
        { text: this.$t('待提交'), value: 0 },
        { text: this.$t('待审核'), value: 1 },
        { text: this.$t('待财务审核'), value: 2 },
        { text: this.$t('完成'), value: 3 },
        { text: this.$t('已退回'), value: 4 },
        { text: this.$t('已删除'), value: 5 }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    //点击确认
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          if (
            this.formObject.reconciliationStatus !== null ||
            this.formObject.reconInvoiceStatus !== null ||
            this.formObject.invoiceStatus !== null
          ) {
            this.$emit('confirm-function', this.formObject)
          } else {
            this.$toast({ content: this.$t('至少选择一个状态'), type: 'warning' })
          }
        }
      })
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    .dialog-content {
      padding: 20px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 100%;
        }
      }
    }
  }
}
</style>
