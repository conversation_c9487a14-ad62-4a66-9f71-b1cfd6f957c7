import { i18n } from '@/main.js'
import Vue from 'vue'

export const CancelQuantityColumnData = {
  width: '150',
  field: 'cancelQuantity',
  headerText: i18n.t('撤销数量'),
  template: () => {
    return {
      template: Vue.component('actionInput', {
        template: `<mt-input-number v-model="data.cancelQuantity" :max="data.preCancelQuantity" cssClass="e-outline" :show-clear-button="false" type="text" @change="handleChange"></mt-input-number>`,
        data: function () {
          return { data: {} }
        },
        mounted() {},
        methods: {
          handleChange(e) {
            if (
              this.data.preCancelQuantity &&
              (isNaN(Number(e)) || Number(e) < 0 || e > this.data.preCancelQuantity)
            ) {
              // 非数字、小于 0、大于 可撤销数量 preCancelQuantity
              this.data.cancelQuantity = this.data.preCancelQuantity
            } else {
              this.data.cancelQuantity = e
            }
            this.$parent.$emit('cellEdit', {
              index: this.data.index,
              key: 'cancelQuantity',
              value: this.data.cancelQuantity
            })
          }
        }
      })
    }
  }
}
