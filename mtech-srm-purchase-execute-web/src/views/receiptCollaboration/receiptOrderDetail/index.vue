<template>
  <!-- 收货协同-收货单详情：打印、收货退回 -->
  <div class="full-height pt20">
    <!-- 头部信息 -->
    <top-info
      :header-info="headerInfo"
      :entry-type="entryType"
      @doSubmit="doSubmit"
      @doPrint="doPrint"
      @goBack="goBack"
    ></top-info>
    <div class="bottom-tables">
      <!-- 表格 -->
      <div class="full-height">
        <mt-data-grid
          v-if="columnData && columnData.length > 0"
          ref="dataGrid"
          class="custom-toolbar-grid"
          :allow-sorting="true"
          :allow-filtering="true"
          :allow-reordering="true"
          :allow-paging="true"
          :data-source="dataSource"
          :column-data="columnData"
          :toolbar="toolbar"
          :toolbar-click="toolbarClick"
          :filter-settings="filterOptions"
          :page-settings="pageSettings"
          @cellEdit="cellEdit"
          @currentChange="handleCurrentChange"
          @sizeChange="handleSizeChange"
        ></mt-data-grid>
      </div>
    </div>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { CancelQuantityColumnData } from './config/index.js'
export default {
  components: {
    TopInfo: require('./components/topInfo.vue').default
  },
  data() {
    return {
      pageSettings: {
        pageSize: 10, // 当前每页数据量
        totalRecordsCount: 0, // 总条数
        pageSizes: [10, 20, 30]
      },
      filterOptions: {
        type: 'Menu'
      },
      currentPage: 1, // 当前页码
      dataSource: [],
      columnData: [],
      toolbar: [
        {
          text: this.$t('清空数量'),
          tooltipText: this.$t('清空数量'),
          prefixIcon: 'mt-icons mt-icon-icon_table_empty',
          id: 'EmptyQuantity'
        },
        {
          text: this.$t('自动填充'),
          tooltipText: this.$t('自动填充'),
          prefixIcon: 'mt-icons mt-icon-icon_table_AutoFill',
          id: 'AutoFill'
        }
      ],
      headerInfo: {},
      entryType: '0', // 0: 打印 1: 收货撤销
      editData: [], // 行编辑过的数据
      apiWaitingQuantity: 0 // 调用的api正在等待数
    }
  },
  mounted() {
    this.entryType = this.$route.query.type
    // 顶部信息获取
    this.getHeaderInfo()
    // 表格列动态获取
    this.getTableCol()
    // 表格数据获取
    this.getTableData()
  },
  methods: {
    // 提交 退货
    doSubmit() {
      const cancel = []
      this.editData.forEach((item) => {
        if (item.cancelQuantity > 0) {
          cancel.push({
            itemId: item.id, // 明细ID
            deliveryBillId: item.deliveryBillId, // 运货单ID
            cancelQuantity: item.cancelQuantity
          })
        }
      })
      if (cancel.length === 0) {
        this.$toast({ content: this.$t('未填写撤销数量'), type: 'warning' })
        return
      }
      const parms = {
        billId: this.$route.query.billId,
        idQuantityParams: cancel
      }
      this.apiStartLoading()
      this.$API.receiptCollaboration
        .postBuyerReceivingBillItemCancel(parms)
        .then(() => {
          // 返回 收货协同-列表
          this.$router.push({
            name: 'receipt-collaboration-summary',
            query: {}
          })
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 打印
    doPrint() {
      console.log('打印 TODO')
    },
    // 返回
    goBack() {
      this.$router.push({
        name: 'receipt-collaboration-summary',
        query: {}
      })
    },
    toolbarClick(e) {
      if (e.item.id == 'EmptyQuantity') {
        // 清空数量
        const dataSourceCopy = cloneDeep(this.dataSource)
        dataSourceCopy.forEach((item) => {
          item.cancelQuantity = ''
          // 更新行编辑数据 撤销数量 cancelQuantity
          this.updataEditDataById({
            id: item.id,
            data: { cancelQuantity: '' }
          })
        })
        this.dataSource = dataSourceCopy
      } else if (e.item.id == 'AutoFill') {
        // 自动填充 可退货数量 preCancelQuantity
        const dataSourceCopy = cloneDeep(this.dataSource)
        dataSourceCopy.forEach((item) => {
          item.cancelQuantity = item.preCancelQuantity
          // 更新行编辑数据
          this.updataEditDataById({
            id: item.id,
            data: { cancelQuantity: item.preCancelQuantity }
          })
        })
        this.dataSource = dataSourceCopy
      }
    },
    // 通过 id 更新指定的行编辑数据
    updataEditDataById({ id, data }) {
      for (let i = 0; i < this.editData.length; i++) {
        if (this.editData[i][id]) {
          this.editData[i][data.key] = data.value
          break
        }
      }
    },
    // 行编辑
    cellEdit(e) {
      // 存行编辑的数据
      if (this.dataSource[e.index].id) {
        let currentEditIndex
        const editItem = this.editData.find((item, index) => {
          currentEditIndex = index
          return item.id == this.dataSource[e.index].id
        })
        if (editItem) {
          // 更新编辑的数据
          editItem[e.key] = e.value
          this.editData.splice(currentEditIndex, 1, editItem)
        } else {
          // 保存编辑的数据
          if (e.value) {
            this.editData.push({
              id: this.dataSource[e.index].id, // 行数据id
              [e.key]: e.value, // 编辑的字段和值
              key: e.key // 编辑的字段
            })
          }
        }
      }
      // 更新当前页 dataSource，使此修改可 this.$refs.dataGrid.dataSource 获取
      this.$set(this.dataSource[e.index], [e.key], e.value)
    },
    handleCurrentChange(pageIndex) {
      this.currentPage = pageIndex
      // 重新获取表格数据
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      // 重新获取表格数据
      this.getTableData()
    },
    // 表格数据
    getTableData() {
      this.apiStartLoading()
      this.$API.receiptCollaboration
        .postBuyerReceivingBillItemQuery({
          billId: this.$route.query.billId,
          page: { current: this.currentPage, size: this.pageSettings.pageSize }
        })
        .then((res) => {
          if (res?.data?.records) {
            // 将行编辑过的数据设置到表格中
            const rowData = []
            res.data.records.forEach((itemData) => {
              rowData.push({ ...itemData.orderDetailResponse, ...itemData })
            })
            this.pageSettings.totalRecordsCount = res.data.total // 总数
            this.dataSource = this.setEditedData(rowData)
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 将行编辑过的数据设置到表格中
    setEditedData(data) {
      this.editData.forEach((itemEdit) => {
        for (let i = 0; i < data.length; i++) {
          if (data[i].id) {
            data[i][itemEdit.key] = itemEdit.value
            break
          }
        }
      })
      return data
    },
    // 顶部信息获取 根据ID获取收货单详情
    getHeaderInfo() {
      this.apiStartLoading()
      this.$API.receiptCollaboration
        .getBuyerReceivingBillGet({ billItemId: this.$route.query.billId })
        .then((res) => {
          if (res && res.data) {
            this.headerInfo = {
              ...res.data,
              receivingDate: res.data.receivingDate ? new Date(Number(res.data.receivingDate)) : '' // 收货日期
            }
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 表格列动态获取
    getTableCol() {
      this.apiStartLoading()
      this.$API.receiptCollaboration
        .getReModuleConfig({ docId: this.$route.query.billId })
        .then((res) => {
          if (res && res.data) {
            this.columnData = this.formatTableDynamicData(res.data)
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 格式化表格动态数据
    formatTableDynamicData(data) {
      const colData = []
      if (data && data.moduleItems && data.moduleItems[0] && data.moduleItems[0].fieldDefines) {
        data.moduleItems[0].fieldDefines.forEach((col) => {
          const defaultCol = {
            ...col,
            field: col.fieldCode,
            headerText: col.fieldName,
            width: '150'
          }
          if (col.fieldCode === 'checkRequest') {
            // 检验要求
            colData.push({
              ...col,
              field: col.fieldCode,
              headerText: col.fieldName,
              width: '150',
              valueConverter: {
                type: 'map',
                map: {
                  0: this.$t('否'),
                  1: this.$t('是')
                }
              }
            })
          } else {
            colData.push(defaultCol)
          }
        })
        // 表格末尾添加列
        if (this.entryType == '0') {
          // 0: 打印，“ 收货数量 receivedQuantity ”
          colData.push({
            field: 'receivedQuantity',
            headerText: this.$t('收货数量'),
            width: '150'
          })
        } else if (this.entryType == '1') {
          // 1: 收货撤销，“ 收货数量 receivedQuantity ” “ 可撤销数量 preCancelQuantity ” “ 撤销数量 input ”
          colData.push(
            {
              field: 'receivedQuantity',
              headerText: this.$t('收货数量'),
              width: '150'
            },
            {
              field: 'preCancelQuantity',
              headerText: this.$t('可撤销数量'),
              width: '150'
            },
            CancelQuantityColumnData // 撤销数量 input
          )
        }
      }

      return colData
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style></style>
