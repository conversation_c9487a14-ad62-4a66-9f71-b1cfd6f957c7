<template>
  <!-- 收货协同-列表 -->
  <div class="full-height pt20">
    <mt-template-page
      :template-config="pageConfig"
      ref="templateRef"
      @handleClickCellTool="handleClickCellTool"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { DeliveryOrderCol, ReceiptOrderCol, ReceiptCancelOrderCol } from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'

export default {
  data() {
    return {
      pageConfig: [
        // 送货单
        {
          title: this.$t('送货单'),
          toolbar: [],
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: DeliveryOrderCol,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/buyerDeliveryBill/query` // 根据条件查询运货单
            },
            frozenColumns: 1
          }
        },
        // 收货单
        {
          title: this.$t('收货单'),
          toolbar: [],
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: ReceiptOrderCol,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/buyerReceivingBill/query` // 根据条件获取收货单
            },
            frozenColumns: 1
          }
        },
        // 收货退货单
        {
          title: this.$t('收货退货单'),
          toolbar: [],
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: ReceiptCancelOrderCol,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/buyerReceivingCancelBill/query` // 根据条件获取收货退回单
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'Tab0Receipt') {
        // 送货单 收货
        this.$router.push({
          name: 'delivery-order-detail',
          query: {
            billId: e.data.id
          }
        })
      } else if (e.tool.id == 'Tab1Print') {
        // 收货单 打印
        this.$router.push({
          name: 'receipt-order-detail',
          query: {
            type: '0',
            billId: e.data.id
          }
        })
      } else if (e.tool.id == 'Tab1ReceiptReturn') {
        // 收货单 收货退回
        this.$router.push({
          name: 'receipt-order-detail',
          query: {
            type: '1',
            billId: e.data.id
          }
        })
      } else if (e.tool.id == 'Tab2Print') {
        // 收货退回单 打印
        this.$router.push({
          name: 'receipt-cancel-order-detail',
          query: {
            billId: e.data.id
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 表格 状态列
/deep/ .status-highlight {
  padding: 2px 4px;
  font-size: 12px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(99, 134, 193, 1);
  height: 20px;
  background: #eff2f8;
  border-radius: 2px;
  opacity: 10;
}
/deep/ .status-disable {
  padding: 2px 4px;
  height: 20px;
  background: #f4f4f4;
  border-radius: 2px;
  opacity: 10;
  font-size: 12px;
  font-family: PingFangSC;
  font-weight: 500;
  color: rgba(154, 154, 154, 1);
}
</style>
