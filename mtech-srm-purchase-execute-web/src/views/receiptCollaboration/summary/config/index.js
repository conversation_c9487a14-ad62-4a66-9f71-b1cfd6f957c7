import { i18n } from '@/main.js'
import Vue from 'vue'
import utils from '@/utils/utils'

export const stringToDate = (data) => {
  const { formatString, value } = data
  if (formatString) {
    const date = new Date(Number(value))
    if (isNaN(date.getTime())) {
      return value
    } else {
      return utils.formateTime(date, formatString)
    }
  } else {
    return value
  }
}

// 送货单 Col
export const DeliveryOrderCol = [
  {
    width: 'auto',
    field: 'deliveryNo',
    headerText: i18n.t('送货单号'),
    cssClass: '',
    cellTools: [
      {
        id: 'Tab0Receipt',
        icon: 'icon_list_takeDelivery',
        title: i18n.t('收货'),
        visibleCondition: (data) => data['status'] == 1 || data['status'] == 2
      }
    ]
  },
  {
    width: 'auto',
    field: 'status',
    headerText: i18n.t('状态'),
    template: () => {
      return {
        template: Vue.component('status', {
          template: `<span :class="[isHighlight(data.status) ? 'status-highlight' : 'status-disable']">{{data.status | statusFormat}}</span>`,
          data: function () {
            return { data: {} }
          },
          filters: {
            statusFormat(value) {
              const status = {
                1: i18n.t('待收货'),
                2: i18n.t('部分收货'),
                3: i18n.t('全部收货')
              }
              if (!status[value]) {
                return value
              } else {
                return status[value]
              }
            }
          },
          methods: {
            isHighlight(value) {
              let highlight = false
              if (value == 1 || value == 2) {
                highlight = true
              }
              return highlight
            }
          }
        })
      }
    }
  },
  {
    width: 'auto',
    field: 'expectArrivalDate',
    headerText: i18n.t('预计到货日期'),
    template: () => {
      return {
        template: Vue.component('date', {
          template: `<div><div>{{data.expectArrivalDate | timeFormat}}</div><div>{{data.expectArrivalDate | dateFormat}}</div></div>`,
          data: function () {
            return { data: {} }
          },
          filters: {
            dateFormat(value) {
              return stringToDate({ formatString: 'YYYY-mm-dd', value })
            },
            timeFormat(value) {
              return stringToDate({ formatString: 'HH:MM:SS', value })
            }
          }
        })
      }
    }
  },
  {
    width: 'auto',
    field: 'delivaryDate',
    headerText: i18n.t('发货日期'),
    template: () => {
      return {
        template: Vue.component('date', {
          template: `<div><div>{{data.delivaryDate | timeFormat}}</div><div>{{data.delivaryDate | dateFormat}}</div></div>`,
          data: function () {
            return { data: {} }
          },
          filters: {
            dateFormat(value) {
              return stringToDate({ formatString: 'YYYY-mm-dd', value })
            },
            timeFormat(value) {
              return stringToDate({ formatString: 'HH:MM:SS', value })
            }
          }
        })
      }
    }
  },
  {
    width: 'auto',
    field: 'supplierCode',
    headerText: i18n.t('供应商编号')
  },
  {
    width: 'auto',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: 'auto',
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    width: 'auto',
    field: 'orderType',
    headerText: i18n.t('订单类型'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('退货订单'),
        '-2': i18n.t('换货订单'),
        '-3': i18n.t('维修订单'),
        1: i18n.t('正常订单')
      }
    }
  },
  {
    width: 'auto',
    field: 'deliveryType',
    headerText: i18n.t('发货方式'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('快递配送'),
        2: i18n.t('物流配送'),
        3: i18n.t('采方自提')
      }
    }
  },
  {
    width: 'auto',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

// 收货单 Col
export const ReceiptOrderCol = [
  {
    width: 'auto',
    field: 'receivingNo',
    headerText: i18n.t('收货单号'),
    cssClass: '',
    cellTools: [
      {
        id: 'Tab1Print',
        icon: 'icon_list_print',
        title: i18n.t('打印')
      },
      {
        id: 'Tab1ReceiptReturn',
        icon: 'icon_list_return',
        title: i18n.t('收货退回')
      }
    ]
  },
  {
    width: 'auto',
    field: 'supplierCode',
    headerText: i18n.t('供应商编号')
  },
  {
    width: 'auto',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: 'auto',
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    width: 'auto',
    field: 'orderType',
    headerText: i18n.t('订单类型'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('退货订单'),
        '-2': i18n.t('换货订单'),
        '-3': i18n.t('维修订单'),
        1: i18n.t('正常订单')
      }
    }
  },
  {
    width: 'auto',
    field: 'createUserName',
    headerText: i18n.t('收货人')
  },
  {
    width: 'auto',
    field: 'createTime',
    headerText: i18n.t('收货日期'),
    template: () => {
      return {
        template: Vue.component('date', {
          template: `<div><div>{{data.createTime | timeFormat}}</div><div>{{data.createTime | dateFormat}}</div></div>`,
          data: function () {
            return { data: {} }
          },
          filters: {
            dateFormat(value) {
              return stringToDate({ formatString: 'YYYY-mm-dd', value })
            },
            timeFormat(value) {
              return stringToDate({ formatString: 'HH:MM:SS', value })
            }
          }
        })
      }
    }
  },
  {
    width: 'auto',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

// 收货退货单 Col
export const ReceiptCancelOrderCol = [
  {
    width: 'auto',
    field: 'receivingCancelNo',
    headerText: i18n.t('收货退回单号'),
    cssClass: '',
    cellTools: [
      {
        id: 'Tab2Print',
        icon: 'icon_list_print',
        title: i18n.t('打印')
      }
    ]
  },
  {
    width: 'auto',
    field: 'supplierCode',
    headerText: i18n.t('供应商编号')
  },
  {
    width: 'auto',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: 'auto',
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    width: 'auto',
    field: 'orderType',
    headerText: i18n.t('订单类型'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('退货订单'),
        '-2': i18n.t('换货订单'),
        '-3': i18n.t('维修订单'),
        1: i18n.t('正常订单')
      }
    }
  },
  {
    width: 'auto',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: 'auto',
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    template: () => {
      return {
        template: Vue.component('date', {
          template: `<div><div>{{data.createTime | timeFormat}}</div><div>{{data.createTime | dateFormat}}</div></div>`,
          data: function () {
            return { data: {} }
          },
          filters: {
            dateFormat(value) {
              return stringToDate({ formatString: 'YYYY-mm-dd', value })
            },
            timeFormat(value) {
              return stringToDate({ formatString: 'HH:MM:SS', value })
            }
          }
        })
      }
    }
  },
  {
    width: 'auto',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
