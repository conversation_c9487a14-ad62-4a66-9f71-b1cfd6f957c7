<template>
  <!-- 收货协同-收货退回单详情：打印 -->
  <div class="full-height pt20">
    <!-- 头部信息 -->
    <top-info :header-info="headerInfo" @doPrint="doPrint" @goBack="goBack"></top-info>
    <div class="bottom-tables">
      <!-- 表格 -->
      <div class="full-height">
        <mt-data-grid
          v-if="columnData && columnData.length > 0"
          ref="dataGrid"
          :allow-sorting="true"
          :allow-filtering="true"
          :allow-reordering="true"
          :allow-paging="true"
          :data-source="dataSource"
          :column-data="columnData"
          :filter-settings="filterOptions"
          :page-settings="pageSettings"
          @currentChange="handleCurrentChange"
          @sizeChange="handleSizeChange"
        ></mt-data-grid>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {
    TopInfo: require('./components/topInfo.vue').default
  },
  data() {
    return {
      pageSettings: {
        pageSize: 10, // 当前每页数据量
        totalRecordsCount: 0, // 总条数
        pageSizes: [10, 20, 30]
      },
      filterOptions: {
        type: 'Menu'
      },
      currentPage: 1, // 当前页码
      dataSource: [],
      columnData: [],
      headerInfo: {},
      apiWaitingQuantity: 0 // 调用的api正在等待数
    }
  },
  mounted() {
    // 顶部信息获取
    this.getHeaderInfo()
    // 表格列动态获取
    this.getTableCol()
    // 表格数据获取
    this.getTableData()
  },
  methods: {
    // 打印
    doPrint() {
      console.log('打印 TODO')
    },
    // 返回
    goBack() {
      this.$router.push({
        name: 'receipt-collaboration-summary',
        query: {}
      })
    },
    handleCurrentChange(pageIndex) {
      this.currentPage = pageIndex
      // 重新获取表格数据
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      // 重新获取表格数据
      this.getTableData()
    },
    // 表格数据
    getTableData() {
      this.apiStartLoading()
      this.$API.receiptCollaboration
        .postBuyerReceivingCancelBillItemQuery({
          billId: this.$route.query.billId,
          page: { current: this.currentPage, size: this.pageSettings.pageSize }
        })
        .then((res) => {
          if (res && res.data && res.data.records) {
            // 将行编辑过的数据设置到表格中
            const rowData = []
            res.data.records.forEach((itemData) => {
              rowData.push({ ...itemData.orderDetailResponse, ...itemData })
            })
            this.pageSettings.totalRecordsCount = res.data.total // 总数
            this.dataSource = rowData
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 顶部信息获取 根据ID获取收货退回单详情
    getHeaderInfo() {
      this.apiStartLoading()
      this.$API.receiptCollaboration
        .getBuyerReceivingCancelBillGet({
          billItemId: this.$route.query.billId
        })
        .then((res) => {
          if (res && res.data) {
            this.headerInfo = {
              ...res.data
            }
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 表格列动态获取
    getTableCol() {
      this.apiStartLoading()
      this.$API.receiptCollaboration
        .getReModuleConfig({ docId: this.$route.query.billId })
        .then((res) => {
          if (res && res.data) {
            this.columnData = this.formatTableDynamicData(res.data)
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 格式化表格动态数据
    formatTableDynamicData(data) {
      const colData = []
      if (data && data.moduleItems && data.moduleItems[0] && data.moduleItems[0].fieldDefines) {
        data.moduleItems[0].fieldDefines.forEach((col) => {
          const defaultCol = {
            ...col,
            field: col.fieldCode,
            headerText: col.fieldName,
            width: '150'
          }
          if (col.fieldCode === 'checkRequest') {
            // 检验要求
            colData.push({
              ...col,
              field: col.fieldCode,
              headerText: col.fieldName,
              width: '150',
              valueConverter: {
                type: 'map',
                map: {
                  0: this.$t('否'),
                  1: this.$t('是')
                }
              }
            })
          } else {
            colData.push(defaultCol)
          }
        })
        // 表格末尾添加列 撤销数量 cancelQuantity
        colData.push({
          field: 'cancelQuantity',
          headerText: this.$t('撤销数量'),
          width: '150'
        })
      }

      return colData
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
