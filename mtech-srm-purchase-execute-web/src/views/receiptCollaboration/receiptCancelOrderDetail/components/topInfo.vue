<template>
  <!-- 收货退回单详情头部的内容：打印 -->
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="infos mr20">{{ $t('创建人：') }}{{ headerInfo.createUserName }}</div>
      <div class="infos">{{ $t('创建时间：') }}{{ headerInfo.createTime | dateFormat }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" @click="doPrint">{{ $t('打印') }}</mt-button>

      <div class="sort-box" @click="isExpand = !isExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="receivingCancelNo" :label="$t('收货退回单号')">
          <mt-input
            v-model="headerInfo.receivingCancelNo"
            :disabled="true"
            :placeholder="$t('收货退回单号')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplierCode" :label="$t('供应商编号')">
          <mt-input
            v-model="headerInfo.supplierCode"
            :disabled="true"
            :placeholder="$t('供应商编号')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplierName" :label="$t('供应商名称')">
          <mt-input
            v-model="headerInfo.supplierName"
            :disabled="true"
            :placeholder="$t('供应商名称')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="createUserName" :label="$t('收货退回人')">
          <mt-input
            v-model="headerInfo.createUserName"
            :disabled="true"
            :placeholder="$t('收货退回人')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="createTime" :label="$t('收货退回时间')" :show-message="false">
          <mt-date-time-picker
            :disabled="true"
            :show-clear-button="true"
            :allow-edit="false"
            v-model="headerInfo.createTime"
            :placeholder="$t('收货退回时间')"
          ></mt-date-time-picker>
        </mt-form-item>

        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input
            v-model="headerInfo.remark"
            :disabled="true"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import utils from '@/utils/utils'
export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    },
    entryType: {
      type: String,
      default: () => '0'
    }
  },
  data() {
    return {
      isExpand: true,
      rules: {},
      // 合同/协议类型
      contractTypeOptions: [
        {
          value: 0,
          text: this.$t('单次采购')
        },
        {
          value: 1,
          text: this.$t('框架协议')
        }
      ],
      // 订单类型
      typeOptions: [
        {
          value: 0,
          text: this.$t('正常订单')
        },
        {
          value: 1,
          text: this.$t('退货订单')
        },
        {
          value: 2,
          text: this.$t('免费订单')
        }
      ]
    }
  },
  mounted() {},
  filters: {
    dateFormat(value) {
      const date = new Date(Number(value))
      if (isNaN(date.getTime())) {
        return value
      } else {
        return utils.formateTime(date, 'YYYY-mm-dd HH:MM:SS')
      }
    }
  },
  methods: {
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 打印
    doPrint() {
      this.$emit('doPrint')
    },
    // 创建送货单并发货
    createDeliveryNoteAndShip() {
      this.$emit('ship')
    },
    // 创建送货单
    createDeliveryNote() {
      this.$emit('createDeliveryNote')
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
