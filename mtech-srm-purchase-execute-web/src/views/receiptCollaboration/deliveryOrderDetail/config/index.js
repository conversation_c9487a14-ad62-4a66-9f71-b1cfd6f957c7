import { i18n } from '@/main.js'
import Vue from 'vue'

export const ReceivedQuantityColumnData = {
  width: '150',
  field: 'receivedQuantity',
  headerText: i18n.t('收货数量'),
  template: () => {
    return {
      template: Vue.component('actionInput', {
        template: `<mt-input-number v-model="data.receivedQuantity" :max="data.preReceivedQuantity" cssClass="e-outline" :show-clear-button="false" type="text" @change="handleChange"></mt-input-number>`,
        data: function () {
          return { data: {} }
        },
        mounted() {},
        methods: {
          handleChange(e) {
            if (
              this.data.preReceivedQuantity &&
              (isNaN(Number(e)) || Number(e) < 0 || e > this.data.preReceivedQuantity)
            ) {
              // 非数字、小于 0、大于 待收货数量 preReceivedQuantity
              this.data.receivedQuantity = this.data.preReceivedQuantity
            } else {
              this.data.receivedQuantity = e
            }
            this.$parent.$emit('cellEdit', {
              index: this.data.index,
              key: 'receivedQuantity',
              value: this.data.receivedQuantity
            })
          }
        }
      })
    }
  }
}
