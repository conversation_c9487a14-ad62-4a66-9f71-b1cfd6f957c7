<template>
  <!-- 送货单详情头部的内容：收货 -->
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div
        :class="[isHighlight(headerInfo.status) ? 'status-highlight' : 'status-disable', 'mr20']"
      >
        {{ headerInfo.status | statusFormat }}
      </div>
      <div class="infos mr20">{{ $t('创建人：') }}{{ headerInfo.createUserName }}</div>
      <div class="infos">{{ $t('创建时间：') }}{{ headerInfo.createTime | dateFormat }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" @click="doSubmit">{{
        $t('提交')
      }}</mt-button>

      <div class="sort-box" @click="isExpand = !isExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="deliveryNo" :label="$t('送货单号')">
          <mt-input
            v-model="headerInfo.deliveryNo"
            :disabled="true"
            :placeholder="$t('送货单号')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="businessTypeName" :label="$t('业务类型')">
          <mt-input
            v-model="headerInfo.businessTypeName"
            :disabled="true"
            :placeholder="$t('业务类型')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplierCode" :label="$t('供应商编号')" :show-message="false">
          <mt-input
            v-model="headerInfo.supplierCode"
            :disabled="true"
            :placeholder="$t('供应商编号')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplierName" :label="$t('供应商名称')" :show-message="false">
          <mt-input
            v-model="headerInfo.supplierName"
            :disabled="true"
            :placeholder="$t('供应商名称')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="delivaryDate" :label="$t('发货时间')" :show-message="false">
          <mt-date-time-picker
            :disabled="true"
            :show-clear-button="true"
            :allow-edit="false"
            v-model="headerInfo.delivaryDate"
            :placeholder="$t('发货时间')"
          ></mt-date-time-picker>
        </mt-form-item>

        <mt-form-item prop="expectArrivalDate" :label="$t('预计到货时间')" :show-message="false">
          <mt-date-time-picker
            :disabled="true"
            :show-clear-button="true"
            :allow-edit="false"
            v-model="headerInfo.expectArrivalDate"
            :placeholder="$t('预计到货时间')"
          ></mt-date-time-picker>
        </mt-form-item>

        <mt-form-item prop="deliveryAddress" :label="$t('发货地址')" :show-message="false">
          <mt-input
            v-model="headerInfo.deliveryAddress"
            :disabled="true"
            :placeholder="$t('发货地址')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="deliveryType" :label="$t('发货方式')" :show-message="false">
          <mt-select
            :disabled="true"
            v-model="headerInfo.deliveryType"
            :data-source="deliveryTypeOptions"
            :show-clear-button="false"
            :placeholder="$t('发货方式')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="logisticsCompany" :label="$t('物流公司')" :show-message="false">
          <mt-input
            v-model="headerInfo.logisticsCompany"
            :disabled="true"
            :placeholder="$t('物流公司')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="logisticsBillCode" :label="$t('物流单号')" :show-message="false">
          <mt-input
            v-model="headerInfo.logisticsBillCode"
            :disabled="true"
            :placeholder="$t('物流单号')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input
            v-model="headerInfo.remark"
            :disabled="true"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import utils from '@/utils/utils'
export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isExpand: true,
      rules: {},
      // 发货方式
      deliveryTypeOptions: [
        {
          value: 1,
          text: this.$t('快递配送')
        },
        {
          value: 2,
          text: this.$t('物流配送')
        },
        {
          value: 3,
          text: this.$t('采方自提')
        }
      ]
    }
  },
  mounted() {},
  filters: {
    dateFormat(value) {
      const date = new Date(Number(value))
      if (isNaN(date.getTime())) {
        return value
      } else {
        return utils.formateTime(date, 'YYYY-mm-dd HH:MM:SS')
      }
    },
    statusFormat(value) {
      const status = {
        1: this.$t('待收货'),
        2: this.$t('部分收货'),
        3: this.$t('全部收货')
      }
      if (!status[value]) {
        return value
      } else {
        return status[value]
      }
    }
  },
  methods: {
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 提交
    doSubmit() {
      this.$emit('doSubmit')
    },
    isHighlight(value) {
      let highlight = false
      // 1: this.$t("待收货"),2: this.$t("部分收货"),
      if (value == 1 || value == 2) {
        highlight = true
      }
      return highlight
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
