<template>
  <!-- 收货协同-送货单详情：收货-ASN收货 -->
  <div class="full-height pt20">
    <!-- 头部信息 -->
    <top-info :header-info="headerInfo" @doSubmit="doSubmit" @goBack="goBack"></top-info>
    <div class="bottom-tables">
      <!-- 表格 -->
      <div class="full-height">
        <mt-data-grid
          v-if="columnData && columnData.length > 0"
          ref="dataGrid"
          class="custom-toolbar-grid"
          :allow-sorting="true"
          :allow-filtering="true"
          :allow-reordering="true"
          :allow-paging="true"
          :data-source="dataSource"
          :column-data="columnData"
          :toolbar="toolbar"
          :toolbar-click="toolbarClick"
          :filter-settings="filterOptions"
          :page-settings="pageSettings"
          @cellEdit="cellEdit"
          @currentChange="handleCurrentChange"
          @sizeChange="handleSizeChange"
        ></mt-data-grid>
      </div>
    </div>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { ReceivedQuantityColumnData } from './config/index.js'
export default {
  components: {
    TopInfo: require('./components/topInfo.vue').default
  },
  data() {
    return {
      pageSettings: {
        pageSize: 10, // 当前每页数据量
        totalRecordsCount: 0, // 总条数
        pageSizes: [10, 20, 30]
      },
      filterOptions: {
        type: 'Menu'
      },
      currentPage: 1, // 当前页码
      dataSource: [],
      columnData: [],
      toolbar: [
        {
          text: this.$t('清空数量'),
          tooltipText: this.$t('清空数量'),
          prefixIcon: 'mt-icons mt-icon-icon_table_empty',
          id: 'EmptyQuantity'
        },
        {
          text: this.$t('自动填充'),
          tooltipText: this.$t('自动填充'),
          prefixIcon: 'mt-icons mt-icon-icon_table_AutoFill',
          id: 'AutoFill'
        }
      ],
      headerInfo: {
        status: 2
      },
      editData: [], // 行编辑过的数据
      apiWaitingQuantity: 0 // 调用的api正在等待数
    }
  },
  mounted() {
    // 顶部信息获取
    this.getHeaderInfo()
    // 表格数据获取
    this.getTableData()
    // 表格列动态获取
    this.getTableCol()
  },
  methods: {
    // 提交
    doSubmit() {
      // 收货操作
      const receive = []
      this.editData.forEach((item) => {
        if (item.receivedQuantity > 0) {
          receive.push({
            deliveryBillId: item.deliveryBillId, // 运货单ID
            itemId: item.id, // 明细ID
            receivedQuantity: item.receivedQuantity // 收货数量
          })
        }
      })
      if (receive.length === 0) {
        this.$toast({ content: this.$t('未填写收货数量'), type: 'warning' })
        return
      }
      const parms = {
        billId: this.$route.query.billId,
        idQuantityParams: receive
      }
      this.apiStartLoading()
      this.$API.receiptCollaboration
        .postBuyerDeliveryBillItemReceive(parms)
        .then((res) => {
          // 提交后，进入收货单详情 打印
          if (res?.data) {
            this.$router.push({
              name: 'receipt-order-detail',
              query: {
                type: '0',
                billId: res?.data
              }
            })
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 返回
    goBack() {
      this.$router.push({
        name: 'receipt-collaboration-summary',
        query: {}
      })
    },
    toolbarClick(e) {
      if (e.item.id == 'EmptyQuantity') {
        // 清空数量
        const dataSourceCopy = cloneDeep(this.dataSource)
        dataSourceCopy.forEach((item) => {
          item.receivedQuantity = ''
          // 更新行编辑数据 收货数量 receivedQuantity
          this.updataEditDataById({
            id: item.id,
            data: { receivedQuantity: '' }
          })
        })
        this.dataSource = dataSourceCopy
      } else if (e.item.id == 'AutoFill') {
        // 自动填充 待收货数量 preReceivedQuantity
        const dataSourceCopy = cloneDeep(this.dataSource)
        dataSourceCopy.forEach((item) => {
          item.receivedQuantity = item.preReceivedQuantity
          // 更新行编辑数据
          this.updataEditDataById({
            id: item.id,
            data: { receivedQuantity: item.preReceivedQuantity }
          })
        })
        this.dataSource = dataSourceCopy
      }
    },
    // 通过 id 更新指定的行编辑数据
    updataEditDataById({ id, data }) {
      for (let i = 0; i < this.editData.length; i++) {
        if (this.editData[i][id]) {
          this.editData[i][data.key] = data.value
          break
        }
      }
    },
    // 行编辑
    cellEdit(e) {
      // 存行编辑的数据
      if (this.dataSource[e.index].id) {
        let currentEditIndex
        const editItem = this.editData.find((item, index) => {
          currentEditIndex = index
          return item.id == this.dataSource[e.index].id
        })
        if (editItem) {
          // 更新编辑的数据
          editItem[e.key] = e.value
          this.editData.splice(currentEditIndex, 1, editItem)
        } else {
          // 保存编辑的数据
          if (e.value) {
            this.editData.push({
              id: this.dataSource[e.index].id, // 行数据id
              [e.key]: e.value, // 编辑的字段和值
              key: e.key // 编辑的字段
            })
          }
        }
      }
      // 更新当前页 dataSource，使此修改可 this.$refs.dataGrid.dataSource 获取
      this.$set(this.dataSource[e.index], [e.key], e.value)
    },
    handleCurrentChange(pageIndex) {
      this.currentPage = pageIndex
      // 重新获取表格数据
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      // 重新获取表格数据
      this.getTableData()
    },
    // 表格数据
    getTableData() {
      this.apiStartLoading()
      this.$API.receiptCollaboration
        .postBuyerDeliveryBillItemQuery({
          billId: this.$route.query.billId,
          page: { current: this.currentPage, size: this.pageSettings.pageSize }
        })
        .then((res) => {
          if (res?.data?.records) {
            // 将行编辑过的数据设置到表格中
            const rowData = []
            res.data.records.forEach((itemData) => {
              rowData.push({ ...itemData.orderDetailResponse, ...itemData })
            })
            this.pageSettings.totalRecordsCount = res.data.total // 总数
            this.dataSource = this.setEditedData(rowData)
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 将行编辑过的数据设置到表格中
    setEditedData(data) {
      this.editData.forEach((itemEdit) => {
        for (let i = 0; i < data.length; i++) {
          if (data[i].id == itemEdit.id) {
            data[i][itemEdit.key] = itemEdit.value
            break
          }
        }
      })
      return data
    },
    // 顶部信息获取 根据单据ID获取运货单详情
    getHeaderInfo() {
      this.apiStartLoading()
      this.$API.receiptCollaboration
        .getBuyerDeliveryBillGet({ billId: this.$route.query.billId })
        .then((res) => {
          if (res && res.data) {
            this.headerInfo = {
              ...res.data,
              delivaryDate: res.data.delivaryDate ? new Date(Number(res.data.delivaryDate)) : '', // 发货日期
              expectArrivalDate: res.data.expectArrivalDate
                ? new Date(Number(res.data.expectArrivalDate))
                : '' // 预计到货日期
            }
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 表格列动态获取
    getTableCol() {
      // 获取采购申请单据模块定义
      this.apiStartLoading()
      this.$API.receiptCollaboration
        .getReModuleConfig({ docId: this.$route.query.billId })
        .then((res) => {
          if (res && res.data) {
            this.columnData = this.formatTableDynamicData(res.data)
          }
          this.apiEndLoading()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 格式化表格动态数据
    formatTableDynamicData(data) {
      const colData = []
      if (data?.moduleItems && data?.moduleItems[0] && data?.moduleItems?.[0]?.fieldDefines) {
        data.moduleItems[0].fieldDefines.forEach((col) => {
          const defaultCol = {
            ...col,
            field: col.fieldCode,
            headerText: col.fieldName,
            width: '150'
          }
          if (col.fieldCode === 'checkRequest') {
            // 检验要求
            colData.push({
              ...col,
              field: col.fieldCode,
              headerText: col.fieldName,
              width: '150',
              valueConverter: {
                type: 'map',
                map: {
                  0: this.$t('否'),
                  1: this.$t('是')
                }
              }
            })
          } else {
            colData.push(defaultCol)
          }
        })
        // 表格末尾添加列 “ 待收货数量 preReceivedQuantity ” 和 “ 收货数量 input ”
        colData.push(
          {
            field: 'preReceivedQuantity',
            headerText: this.$t('待收货数量'),
            width: '150'
          },
          ReceivedQuantityColumnData // 收货数量 input
        )
      }

      return colData
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style></style>
