import { i18n } from '@/main.js'

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'typeCode',
    title: i18n.t('类别编码'),
    minWidth: 160
  },
  {
    field: 'typeDesc',
    title: i18n.t('类别描述'),
    minWidth: 160
  },
  {
    field: 'categCode',
    title: i18n.t('品类编码'),
    minWidth: 160
  },
  {
    field: 'categName',
    title: i18n.t('品类名称'),
    minWidth: 160
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createDate',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'modifyDate',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]
