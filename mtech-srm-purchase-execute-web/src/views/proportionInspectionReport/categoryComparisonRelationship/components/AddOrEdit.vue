<!-- 采方-类别品类对照关系-新增or编辑 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-form-item prop="typeCode" :label="$t('类别')">
        <RemoteAutocomplete
          v-model="formData.typeCode"
          url="/statistics/tenant/proportionInspectCategoryConfig/pageQuery"
          :placeholder="$t('请选择')"
          :fields="{ text: 'typeDesc', value: 'typeCode' }"
          params-key="typeCode"
          @change="typeCodeChange"
        />
      </mt-form-item>
      <mt-form-item prop="categCode" :label="$t('品类')">
        <RemoteAutocomplete
          v-model="formData.categCode"
          url="/masterDataManagement/tenant/category/paged-query"
          :placeholder="$t('请选择')"
          :fields="{ text: 'categoryName', value: 'categoryCode' }"
          :search-fields="['categoryName', 'categoryCode']"
          @change="categCodeChange"
        />
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogTitle: '',
      actionType: 'add',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {},
      rules: {
        typeCode: [
          {
            required: true,
            message: this.$t('请选择类别'),
            trigger: 'blur'
          }
        ],
        categCode: [
          {
            required: true,
            message: this.$t('请选择品类'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  mounted() {},
  methods: {
    typeCodeChange(e) {
      this.formData.typeDesc = e.itemData?.typeDesc
      this.formData.typeId = e.itemData?.id
    },
    categCodeChange(e) {
      this.formData.categName = e.itemData?.categoryName
    },
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, row } = args
      this.dialogTitle = title
      this.actionType = actionType
      if (actionType === 'edit') {
        this.formData = row
      }
    },
    beforeOpen() {
      this.formData = {}
      this.$refs.ruleForm.clearValidate()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$emit('close')
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.handleSave()
        }
      })
    },
    handleSave() {
      let params = { ...this.formData }
      const api = this.$API.proportionInspectionReport.saveCategoryComparisonRelationshipApi
      api(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.actionType === 'add' ? this.$t('新增成功') : this.$t('更改成功'),
            type: 'success'
          })
          this.$emit('confirm')
          this.handleClose()
        }
      })
    }
  }
}
</script>
