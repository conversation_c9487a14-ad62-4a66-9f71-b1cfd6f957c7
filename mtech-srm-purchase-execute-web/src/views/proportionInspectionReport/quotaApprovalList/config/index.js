import { i18n } from '@/main.js'

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'approvalNo',
    title: i18n.t('报批编号'),
    minWidth: 160,
    slots: {
      default: 'approvalNoDefault'
    }
  },
  {
    field: 'approvalName',
    title: i18n.t('报批名称'),
    minWidth: 160
  },
  {
    field: 'respDeptCode',
    title: i18n.t('责任部门'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.respDeptName : ''
    }
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createDate',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'modifyDate',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]
