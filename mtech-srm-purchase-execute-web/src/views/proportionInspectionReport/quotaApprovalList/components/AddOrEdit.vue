<!-- 采方-配额报批清单-新增or编辑 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-form-item prop="approvalNo" :label="$t('报批编号')">
        <mt-input v-model="formData.approvalNo" :placeholder="$t('系统生成')" :disabled="true" />
      </mt-form-item>
      <mt-form-item prop="approvalName" :label="$t('报批名称')">
        <mt-input
          v-model="formData.approvalName"
          :show-clear-button="true"
          :placeholder="$t('请输入')"
        />
      </mt-form-item>
      <mt-form-item prop="respDeptCode" :label="$t('责任部门')">
        <RemoteAutocomplete
          v-model="formData.respDeptCode"
          url="/masterDataManagement/tenant/dict-item/item-tree"
          :params="{
            dictCode: 'RESP_DEPT'
          }"
          :placeholder="$t('请选择')"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          records-position="data"
          @change="respDeptChange"
        />
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogTitle: '',
      actionType: 'add',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {},
      rules: {
        approvalName: [
          {
            required: true,
            message: this.$t('请输入报批名称'),
            trigger: 'blur'
          }
        ],
        respDeptCode: [
          {
            required: true,
            message: this.$t('请选择责任部门'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  mounted() {},
  methods: {
    respDeptChange(e) {
      this.formData.respDeptName = e.itemData?.itemName
    },
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, row } = args
      this.dialogTitle = title
      this.actionType = actionType
      if (actionType === 'edit') {
        this.formData = row
      }
    },
    beforeOpen() {
      this.formData = {}
      this.$refs.ruleForm.clearValidate()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.handleSave()
        }
      })
    },
    handleSave() {
      let params = { ...this.formData }
      const api = this.$API.proportionInspectionReport.saveQuotaApprovalListApi
      api(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.actionType === 'add' ? this.$t('新增成功') : this.$t('更改成功'),
            type: 'success'
          })
          this.$emit('confirm')
          this.handleClose()
        }
      })
    }
  }
}
</script>
