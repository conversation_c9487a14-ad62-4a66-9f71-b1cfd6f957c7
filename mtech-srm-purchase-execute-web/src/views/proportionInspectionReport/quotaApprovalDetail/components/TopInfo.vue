<template>
  <div :class="['detail-top-info', !isExpand && 'detail-top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <span class="header-box-btn" v-waves type="info" @click="goBack">{{ $t('返回') }}</span>
      <span class="header-box-btn" v-waves type="info" @click="handleSave">{{ $t('保存') }}</span>
      <!-- <div class="sort-box" @click="doExpand">
        <span>{{ isExpand ? $t('收起') : $t('展开') }}</span>
        <i
          class="mt-icons mt-icon-MT_DownArrow"
          :class="isExpand ? 'expendIcon' : 'unExpendIcon'"
        />
      </div> -->
    </div>

    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="approvalNo" :label="$t('报批编号')">
          <mt-input v-model="headerInfo.approvalNo" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="approvalName" :label="$t('报批名称')">
          <mt-input v-model="headerInfo.approvalName" />
        </mt-form-item>
        <mt-form-item prop="respDept" :label="$t('责任部门')">
          <RemoteAutocomplete
            v-model="headerInfo.respDeptCode"
            url="/masterDataManagement/tenant/dict-item/item-tree"
            :params="{
              dictCode: 'RESP_DEPT'
            }"
            :placeholder="$t('请选择')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            records-position="data"
            :clearable="false"
            @change="respDeptChange"
          />
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')">
          <mt-input v-model="headerInfo.createUserName" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="createDate" :label="$t('创建时间')">
          <mt-input v-model="headerInfo.createDate" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="updateUserName" :label="$t('更新人')">
          <mt-input v-model="headerInfo.updateUserName" :disabled="true" />
        </mt-form-item>
        <mt-form-item prop="modifyDate" :label="$t('更新时间')">
          <mt-input v-model="headerInfo.modifyDate" :disabled="true" />
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isExpand: true,
      rules: {}
    }
  },
  methods: {
    respDeptChange(e) {
      this.headerInfo.respDeptName = e.itemData?.itemName
    },
    handleSave() {
      if (!this.headerInfo.approvalName) {
        this.$toast({ content: this.$t('报批名称不能为空'), type: 'warning' })
        return
      }
      this.$emit('save')
    },
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    }
  }
}
</script>
