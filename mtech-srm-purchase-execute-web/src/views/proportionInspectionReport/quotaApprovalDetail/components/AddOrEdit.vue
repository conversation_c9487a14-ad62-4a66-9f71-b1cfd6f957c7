<!-- 采方-配额报批清单-详情-新增or编辑 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-form-item prop="seqNo" :label="$t('序号')">
        <mt-input-number
          v-model="formData.seqNo"
          :show-clear-button="true"
          :placeholder="$t('请输入')"
          :min="1"
          :show-spin-button="false"
        />
      </mt-form-item>
      <mt-form-item prop="orgCode" :label="$t('供应组织')">
        <RemoteAutocomplete
          v-model="formData.orgCode"
          url="/statistics/tenant/proportionInspectApproval/querySupplyOrg"
          :placeholder="$t('请选择')"
          :fields="{ text: 'orgName', value: 'orgCode' }"
          records-position="data"
          @change="orgCodeChange"
        />
      </mt-form-item>
      <mt-form-item prop="typeCode" :label="$t('类别')">
        <RemoteAutocomplete
          v-model="formData.typeCode"
          url="/statistics/tenant/proportionInspectCategoryConfig/pageQuery"
          :placeholder="$t('请选择')"
          :fields="{ text: 'typeDesc', value: 'typeCode' }"
          @change="typeCodeChange"
        />
      </mt-form-item>
      <mt-form-item prop="supplierCode" :label="$t('供应商')">
        <RemoteAutocomplete
          v-model="formData.supplierCode"
          url="/masterDataManagement/tenant/supplier/paged-query"
          :placeholder="$t('请选择')"
          :fields="{ text: 'supplierName', value: 'supplierCode' }"
          :search-fields="['supplierName', 'supplierCode']"
          @change="supplierCodeChange"
        />
      </mt-form-item>
      <mt-form-item prop="quota" :label="$t('配额')">
        <mt-input v-model="formData.quota" :show-clear-button="true" :placeholder="$t('请输入')" />
      </mt-form-item>
      <mt-form-item prop="rank" :label="$t('排名')">
        <mt-input-number
          v-model="formData.rank"
          :show-clear-button="true"
          :placeholder="$t('请输入')"
          :min="1"
          :show-spin-button="false"
        />
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogTitle: '',
      actionType: 'add',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {},
      rules: {
        seqNo: [
          {
            required: true,
            message: this.$t('请输入序号'),
            trigger: 'blur'
          }
        ],
        orgCode: [
          {
            required: true,
            message: this.$t('请输入供应组织'),
            trigger: 'blur'
          }
        ],
        typeCode: [
          {
            required: true,
            message: this.$t('请选择类别'),
            trigger: 'blur'
          }
        ],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          }
        ],
        quota: [
          {
            required: true,
            message: this.$t('请输入配额'),
            trigger: 'blur'
          }
        ],
        rank: [
          {
            required: true,
            message: this.$t('请输入排名'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  mounted() {},
  methods: {
    orgCodeChange(e) {
      this.formData.orgName = e.itemData?.orgName
    },
    typeCodeChange(e) {
      this.formData.typeDesc = e.itemData?.typeDesc
    },
    supplierCodeChange(e) {
      this.formData.supplierName = e.itemData?.supplierName
    },
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, row } = args
      this.dialogTitle = title
      this.actionType = actionType
      this.formData = row
    },
    beforeOpen() {
      this.formData = {}
      this.$refs.ruleForm.clearValidate()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.handleSave()
        }
      })
    },
    handleSave() {
      let params = [{ ...this.formData }]
      const api = this.$API.proportionInspectionReport.saveQuotaApprovalDetailApi
      api(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.actionType === 'add' ? this.$t('新增成功') : this.$t('更改成功'),
            type: 'success'
          })
          this.$emit('confirm')
          this.handleClose()
        }
      })
    }
  }
}
</script>
