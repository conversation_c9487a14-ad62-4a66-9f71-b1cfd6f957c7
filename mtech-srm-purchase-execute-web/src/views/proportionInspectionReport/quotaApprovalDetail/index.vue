<!-- 采方 - 配额报批清单 - 详情 -->
<template>
  <div>
    <TopInfo :header-info="headerInfo" @goBack="goBack" @save="saveAll" />

    <div>
      <sc-table
        ref="sctableRef"
        grid-id="e13c2961-87e5-43a2-b1ad-cfaaae82ea44"
        :loading="loading"
        :is-show-refresh-bth="true"
        :columns="columns"
        :table-data="tableData"
        keep-source
        show-overflow
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true
        }"
        @refresh="handleSearch"
        @edit-actived="editBegin"
        @edit-closed="editComplete"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :loading="item.loading"
            size="small"
            @click="handleClickToolBar(item)"
          >
            {{ item.name }}
          </vxe-button>
        </template>
        <template #seqNoEdit="{ row }">
          <vxe-input
            v-model="row.seqNo"
            type="integer"
            min="1"
            clearable
            :placeholder="$t('请输入')"
          />
        </template>
        <template #orgCodeEdit="{ row }">
          <vxe-select
            v-model="row.orgCode"
            :options="orgCodeOptions"
            transfer
            :placeholder="$t('请选择')"
            @change="({ value }) => orgCodeChange(row, value)"
          />
        </template>
        <template #typeCodeEdit="{ row }">
          <vxe-pulldown ref="xDownType" transfer>
            <template #default>
              <vxe-input
                :value="row.typeCode"
                :placeholder="$t('请选择')"
                readonly
                @click="focusTypeCode"
              />
            </template>
            <template #dropdown>
              <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                @keyup="keyupTypeCode"
                style="width: 100%"
              />
              <vxe-list
                height="200"
                class="predict-vxe-dropdown"
                :data="typeCodeOptions"
                auto-resize
              >
                <template #default="{ items }">
                  <div
                    v-show="typeCodeOptions.length"
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.id"
                    @click="selectTypeCode(item, row)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                  <div v-show="!typeCodeOptions.length" class="predict-vxe-list-item">
                    <span>{{ $t('暂无数据') }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template>
        <template #supplierCodeEdit="{ row }">
          <vxe-pulldown ref="xDownSupplier" transfer>
            <template #default>
              <vxe-input
                :value="row.supplierCode"
                :placeholder="$t('请选择')"
                readonly
                @click="focusSupplierCode"
              />
            </template>
            <template #dropdown>
              <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                @keyup="keyupSupplierCode"
                style="width: 100%"
              />
              <vxe-list
                height="200"
                class="predict-vxe-dropdown"
                :data="supplierCodeOptions"
                auto-resize
              >
                <template #default="{ items }">
                  <div
                    v-show="supplierCodeOptions.length"
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.id"
                    @click="selectSupplierCode(item, row)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                  <div v-show="!supplierCodeOptions.length" class="predict-vxe-list-item">
                    <span>{{ $t('暂无数据') }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template>
        <template #quotaEdit="{ row }">
          <vxe-input
            v-model="row.quota"
            type="number"
            min="1"
            clearable
            :placeholder="$t('请输入')"
          />
        </template>
        <template #rankEdit="{ row }">
          <vxe-input
            v-model="row.rank"
            type="integer"
            min="1"
            clearable
            :placeholder="$t('请输入')"
          />
        </template>
      </sc-table>
    </div>
  </div>
</template>

<script>
import TopInfo from './components/TopInfo.vue'
import ScTable from '@/components/ScTable/src/index'
import { columnData } from './config'
import { getHeadersFileName, download } from '@/utils/utils'
import { utils } from '@mtech-common/utils'

export default {
  components: { TopInfo, ScTable },
  data() {
    return {
      columns: columnData,
      loading: false,
      tableData: [],
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
        { code: 'closeEdit', name: this.$t('取消编辑'), status: 'info' },
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
        { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
      ],

      orgCodeOptions: [],
      getTypeDataSource: () => {},
      typeCodeOptions: [],
      getSupplierDataSource: () => {},
      supplierCodeOptions: [],

      isEditing: false
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    headerInfo() {
      let data = this.$route.query
      data.respDept = data.respDeptCode ? data.respDeptCode + '-' + data.respDeptName : ''
      return data
    }
  },
  created() {
    this.getOrgCodeOptions()
    this.getTypeCodeOptions()
    this.getTypeDataSource = utils.debounce(this.getTypeCodeOptions, 1000)
    this.getSupplierCodeOptions()
    this.getSupplierDataSource = utils.debounce(this.getSupplierCodeOptions, 1000)
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    focusTypeCode() {
      this.$refs.xDownType.showPanel()
    },
    keyupTypeCode(e) {
      this.getTypeDataSource(e)
    },
    selectTypeCode(e, row) {
      row.typeId = e.id
      row.typeCode = e.typeCode
      row.typeDesc = e.typeDesc
    },
    getTypeCodeOptions(e = { value: '' }) {
      const { value } = e
      let params = {
        page: {
          current: 1,
          size: 50
        },
        typeCode: value
      }
      this.$API.proportionInspectionReport.pageCategorySettingsApi(params).then((res) => {
        const list = res.data?.records || []
        const newData = list.map((i) => {
          return {
            ...i,
            label: `${i.typeCode}-${i.typeDesc}`,
            value: i.typeCode
          }
        })
        this.typeCodeOptions = [...newData]
      })
    },
    focusSupplierCode() {
      this.$refs.xDownSupplier.showPanel()
    },
    keyupSupplierCode(e) {
      this.getSupplierDataSource(e)
    },
    selectSupplierCode(e, row) {
      row.supplierCode = e.supplierCode
      row.supplierName = e.supplierName
    },
    getSupplierCodeOptions(e = { value: '' }) {
      const { value } = e
      let params = {
        page: {
          current: 1,
          size: 50
        },
        condition: 'or',
        rules: [
          {
            field: 'supplierCode',
            operator: 'contains',
            type: 'string',
            value: value
          },
          {
            field: 'supplierName',
            operator: 'contains',
            type: 'string',
            value: value
          }
        ]
      }
      this.$API.masterData.supplierPagedQuery(params).then((res) => {
        const list = res.data?.records || []
        const newData = list.map((i) => {
          return {
            ...i,
            label: `${i.supplierCode}-${i.supplierName}`,
            value: i.supplierCode
          }
        })
        this.supplierCodeOptions = [...newData]
      })
    },
    getOrgCodeOptions() {
      this.$API.proportionInspectionReport.querySupplyOrgApi().then((res) => {
        if (res.code === 200) {
          this.orgCodeOptions = res.data.map((item) => {
            return {
              label: item.orgCode + '-' + item.orgName,
              value: item.orgCode,
              ...item
            }
          })
        }
      })
    },
    orgCodeChange(row, orgCode) {
      this.orgCodeOptions.forEach((item) => {
        if (item.value === orgCode) {
          row.orgName = item.orgName
        }
      })
    },
    editBegin(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        this.getTypeDataSource({ value: '' }, row)
        this.getSupplierDataSource({ value: '' }, row)
      }
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // if (args.$event.target.innerText === this.$t('取消编辑')) {
        //   this.tableRef.clearEdit()
        //   return
        // }
        // 1、 校验必填
        if (!this.isValidData(row)) {
          this.tableRef.setEditRow(row)
          return
        }
        this.isEditing = false
        // 2、 调保存接口
        // this.handleSave(row)
      }
    },
    isValidData(data) {
      const { seqNo, orgCode, typeCode, supplierCode, quota } = data
      let valid = false
      if (!seqNo) {
        this.$toast({ content: this.$t('请输入序号'), type: 'warning' })
      } else if (!orgCode) {
        this.$toast({ content: this.$t('请选择供应组织'), type: 'warning' })
      } else if (!typeCode) {
        this.$toast({ content: this.$t('请选择类别'), type: 'warning' })
      } else if (!supplierCode) {
        this.$toast({ content: this.$t('请选择供应商'), type: 'warning' })
      } else if (!quota) {
        this.$toast({ content: this.$t('请输入配额'), type: 'warning' })
      } else {
        valid = true
      }
      return valid
    },
    handleSearch() {
      this.isEditing = false
      this.getTableData()
    },
    async getTableData() {
      const params = {
        headId: this.$route.query.id
      }
      this.loading = true
      const res = await this.$API.proportionInspectionReport
        .pageQuotaApprovalDetailApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const records = res.data || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const ids = []
      selectedRecords.forEach((item) => {
        ids.push(item.id)
      })
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'closeEdit':
          if (this.isEditing) {
            this.tableRef.clearEdit()
            this.isEditing = false
            this.$nextTick(() => {
              const currentViewRecords = this.tableRef.getTableData().visibleData
              this.tableRef.remove(currentViewRecords[currentViewRecords.length - 1])
            })
          }
          break
        case 'delete':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除？')
            },
            success: () => {
              this.handleDelete(ids)
            }
          })
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleAdd() {
      if (!this.isEditing) {
        const item = {
          headId: this.$route.query.id,
          seqNo: this.tableRef.getTableData().visibleData.length + 1,
          orgCode: null,
          orgName: null,
          typeCode: null,
          typeDesc: null,
          supplierCode: null,
          supplierName: null,
          quota: null,
          rank: null
        }
        this.tableRef.insertAt([item], -1)
        this.$nextTick(() => {
          // 获取最新的表格视图数据
          const currentViewRecords = this.tableRef.getTableData().visibleData
          // 将新增的那一条设置为编辑状态
          this.tableRef.setEditRow(currentViewRecords[currentViewRecords.length - 1])
        })
        this.isEditing = true
      }
    },
    handleDelete(ids) {
      ids.forEach((id) => {
        this.tableData = this.tableData.filter((item) => item.id !== id)
      })
      // let params = { ids }
      // this.$API.proportionInspectionReport.deleteQuotaApprovalDetailApi(params).then((res) => {
      //   if (res.code === 200) {
      //     this.$toast({ content: this.$t('操作成功'), type: 'success' })
      //     this.handleSearch()
      //   }
      // })
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.proportionInspectionReport.importQuotaApprovalDetailApi,
          downloadTemplateApi: this.$API.proportionInspectionReport.downloadQuotaApprovalDetailApi,
          paramsKey: 'excel',
          asyncParams: {
            headId: this.$route.query.id
          }
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    handleExport(e) {
      const params = {
        id: this.$route.query.id
      }
      this.$API.proportionInspectionReport
        .exportQuotaApprovalDetailApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    goBack() {
      // this.$router.go(-1)
      this.tableRef.clearEdit()
      this.isEditing = false
      this.$router.push({
        name: 'quota-approval-list'
      })
    },
    saveAll() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认保存？')
        },
        success: () => {
          let params = {
            detailList: this.tableRef.getTableData().visibleData,
            ...this.$route.query
          }
          this.$API.proportionInspectionReport.saveQuotaApprovalListApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({
                content: this.$t('保存成功'),
                type: 'success'
              })
              this.$router.push({
                name: 'quota-approval-list',
                query: {
                  timeStamp: new Date().getTime()
                }
              })
            } else {
              this.$toast({
                content: res.msg,
                type: 'error'
              })
            }
          })
        }
      })
    }
  }
}
</script>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
