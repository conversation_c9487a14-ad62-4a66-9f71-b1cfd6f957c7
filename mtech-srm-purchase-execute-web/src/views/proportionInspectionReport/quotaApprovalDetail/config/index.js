import { i18n } from '@/main.js'

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'seqNo',
    title: i18n.t('序号'),
    editRender: {},
    slots: {
      edit: 'seqNoEdit'
    }
  },
  {
    field: 'orgCode',
    title: i18n.t('供应组织'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.orgName : ''
    },
    editRender: {},
    slots: {
      edit: 'orgCodeEdit'
    }
  },
  {
    field: 'typeCode',
    title: i18n.t('类别'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.typeDesc : ''
    },
    editRender: {},
    slots: {
      edit: 'typeCodeEdit'
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    },
    editRender: {},
    slots: {
      edit: 'supplierCodeEdit'
    }
  },
  {
    field: 'quota',
    title: i18n.t('配额'),
    editRender: {},
    slots: {
      edit: 'quotaEdit'
    }
  },
  {
    field: 'rank',
    title: i18n.t('排名'),
    editRender: {},
    slots: {
      edit: 'rankEdit'
    }
  }
]
