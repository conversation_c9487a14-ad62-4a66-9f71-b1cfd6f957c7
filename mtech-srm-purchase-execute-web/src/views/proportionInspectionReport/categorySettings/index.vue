<!-- 采方 - 类别设置 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('类别编码')" prop="typeCode">
          <mt-input
            v-model="searchFormModel.typeCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('类别描述')" prop="typeDesc">
          <mt-input
            v-model="searchFormModel.typeDesc"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('责任部门')" prop="respDeptCode">
          <RemoteAutocomplete
            v-model="searchFormModel.respDeptCode"
            url="/masterDataManagement/tenant/dict-item/item-tree"
            :params="{
              dictCode: 'RESP_DEPT'
            }"
            :placeholder="$t('请选择')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            records-position="data"
          />
        </mt-form-item>
        <mt-form-item :label="$t('责任议价员')" prop="bargainingRespUser">
          <mt-input
            v-model="searchFormModel.bargainingRespUser"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('责任采购开发')" prop="purchaseDevelop">
          <mt-input
            v-model="searchFormModel.purchaseDevelop"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('是否有效')" prop="isValid">
          <mt-multi-select
            v-model="searchFormModel.isValid"
            :data-source="statusOptions"
            :show-select-all="true"
            :show-clear-button="false"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('创建人')" prop="createUserName">
          <mt-input
            v-model="searchFormModel.createUserName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="createDate" :label="$t('创建时间')">
          <mt-date-range-picker
            v-model="searchFormModel.createDate"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'CreateDate')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('更新人')" prop="updateUserName">
          <mt-input
            v-model="searchFormModel.updateUserName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="modifyDate" :label="$t('更新时间')">
          <mt-date-range-picker
            v-model="searchFormModel.modifyDate"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'ModifyDate')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="d72cb58a-8be7-4f7a-8a68-f8d4a16bbbe3"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      keep-source
      @refresh="handleSearch"
      @cell-dblclick="cellDblclick"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />

    <AddOrEdit ref="addOrEditRef" @confirm="handleSearch" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, statusOptions } from './config'
import { getHeadersFileName, download } from '@/utils/utils'
import AddOrEdit from './components/AddOrEdit.vue'
import { cloneDeep } from 'lodash'

export default {
  components: { CollapseSearch, ScTable, AddOrEdit },
  data() {
    return {
      searchFormModel: {},
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
        // { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
        { code: 'enable', name: this.$t('生效'), status: 'info', loading: false },
        { code: 'disable', name: this.$t('失效'), status: 'info', loading: false },
        { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      statusOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    cellDblclick(args) {
      const { row } = args
      this.$refs.addOrEditRef.dialogInit({
        title: this.$t('编辑'),
        actionType: 'edit',
        row: cloneDeep(row)
      })
    },
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel['start' + field] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel['end' + field] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel['start' + field] = null
        this.searchFormModel['end' + field] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.proportionInspectionReport
        .pageCategorySettingsApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const ids = []
      selectedRecords.forEach((item) => {
        ids.push(item.id)
      })
      const commonToolbar = ['delete', 'enable', 'disable']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除？')
            },
            success: () => {
              this.handleDelete(ids)
            }
          })
          break
        case 'enable':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认生效？')
            },
            success: () => {
              this.handleEnable(ids)
            }
          })
          break
        case 'disable':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认失效？')
            },
            success: () => {
              this.handleDisable(ids)
            }
          })
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleAdd() {
      this.$refs.addOrEditRef.dialogInit({
        title: this.$t('新增'),
        actionType: 'add'
      })
    },
    handleDelete(ids) {
      let params = { ids }
      this.$API.proportionInspectionReport.deleteCategorySettingsApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleEnable(ids) {
      let params = { ids }
      this.$API.proportionInspectionReport.enableCategorySettingsApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleDisable(ids) {
      let params = { ids }
      this.$API.proportionInspectionReport.disableCategorySettingsApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.proportionInspectionReport.importCategorySettingsApi,
          downloadTemplateApi: this.$API.proportionInspectionReport.downloadCategorySettingsApi,
          paramsKey: 'excel'
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$API.proportionInspectionReport
        .exportCategorySettingsApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>
