import { i18n } from '@/main.js'

export const statusOptions = [
  { text: i18n.t('有效'), value: 0 },
  { text: i18n.t('无效'), value: 1 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'typeCode',
    title: i18n.t('类别编码'),
    minWidth: 160
  },
  {
    field: 'typeDesc',
    title: i18n.t('类别描述'),
    minWidth: 160
  },
  {
    field: 'respDeptCode',
    title: i18n.t('责任部门'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.respDeptName : ''
    }
  },
  {
    field: 'bargainingRespUser',
    title: i18n.t('责任议价员'),
    minWidth: 160
  },
  {
    field: 'purchaseDevelop',
    title: i18n.t('责任采购开发'),
    minWidth: 160
  },
  {
    field: 'isValid',
    title: i18n.t('是否有效'),
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createDate',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'modifyDate',
    title: i18n.t('更新时间'),
    minWidth: 160
  }
]
