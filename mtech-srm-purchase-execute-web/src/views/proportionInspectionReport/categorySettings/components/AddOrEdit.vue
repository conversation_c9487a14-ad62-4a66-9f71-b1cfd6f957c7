<!-- 采方-类别设置-新增or编辑 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-form-item prop="typeDesc" :label="$t('类别描述')">
        <mt-input
          v-model="formData.typeDesc"
          :show-clear-button="true"
          :placeholder="$t('请输入')"
        />
      </mt-form-item>
      <mt-form-item prop="respDeptCode" :label="$t('责任部门')">
        <RemoteAutocomplete
          v-model="formData.respDeptCode"
          url="/masterDataManagement/tenant/dict-item/item-tree"
          :params="{
            dictCode: 'RESP_DEPT'
          }"
          :placeholder="$t('请选择')"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          records-position="data"
          @change="respDeptChange"
        />
      </mt-form-item>
      <mt-form-item prop="bargainingRespUser" :label="$t('责任议价员')">
        <mt-input
          v-model="formData.bargainingRespUser"
          :show-clear-button="true"
          :placeholder="$t('请输入')"
        />
      </mt-form-item>
      <mt-form-item prop="purchaseDevelop" :label="$t('责任采购开发')">
        <mt-input
          v-model="formData.purchaseDevelop"
          :show-clear-button="true"
          :placeholder="$t('请输入')"
        />
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { statusOptions } from '../config/index'
export default {
  data() {
    return {
      dialogTitle: '',
      actionType: 'add',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {},
      rules: {
        typeDesc: [
          {
            required: true,
            message: this.$t('请输入类别描述'),
            trigger: 'blur'
          }
        ],
        respDeptCode: [
          {
            required: true,
            message: this.$t('请选择责任部门'),
            trigger: 'blur'
          }
        ],
        bargainingRespUser: [
          {
            required: true,
            message: this.$t('请输入责任议价员'),
            trigger: 'blur'
          }
        ],
        purchaseDevelop: [
          {
            required: true,
            message: this.$t('请输入责任采购开发'),
            trigger: 'blur'
          }
        ]
      },
      statusOptions
    }
  },
  mounted() {},
  methods: {
    respDeptChange(e) {
      this.formData.respDeptName = e.itemData?.itemName
    },
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, row } = args
      this.dialogTitle = title
      this.actionType = actionType
      if (actionType === 'edit') {
        this.formData = row
      }
    },
    beforeOpen() {
      this.formData = {}
      this.$refs.ruleForm.clearValidate()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.handleSave()
        }
      })
    },
    handleSave() {
      let params = { ...this.formData }
      const api = this.$API.proportionInspectionReport.saveCategorySettingsApi
      api(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.actionType === 'add' ? this.$t('新增成功') : this.$t('更改成功'),
            type: 'success'
          })
          this.$emit('confirm')
          this.handleClose()
        }
      })
    }
  }
}
</script>
