<!-- 采方 - 比例稽查报表 -->
<template>
  <div>
    <mt-tabs
      class="toggle-tab"
      :e-tab="false"
      :data-source="tabList"
      :selected-item="currentTab"
      @handleSelectTab="handleSelectTab"
    />
    <div class="toggle-content">
      <List v-show="currentTab === 0" @clickJump="handleJump" />
      <Detail v-show="currentTab === 1" :search-form="searchForm" :search-type="searchType" />
    </div>
  </div>
</template>

<script>
export default {
  components: {
    List: () => import('./pages/List.vue'),
    Detail: () => import('./pages/Detail.vue')
  },
  data() {
    return {
      tabList: [{ title: this.$t('汇总') }, { title: this.$t('明细') }],
      currentTab: 0,
      searchForm: {},
      searchType: '1'
    }
  },
  methods: {
    handleJump(searchForm) {
      this.searchForm = searchForm
      this.searchType = '2'
      this.currentTab = 1
    },
    handleSelectTab(e) {
      this.currentTab = e
      if (this.currentTab === 0) {
        this.searchType = '1'
      }
    }
  }
}
</script>
