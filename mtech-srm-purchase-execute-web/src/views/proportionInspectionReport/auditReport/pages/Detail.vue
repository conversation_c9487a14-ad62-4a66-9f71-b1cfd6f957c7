<!-- 明细 -->
<template>
  <div class="full-height vertical-flex-box">
    <collapse-search :is-grid-display="true" @reset="handleReset" @search="handleSearch">
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('报批')" prop="approvalNo">
          <RemoteAutocomplete
            v-model="searchFormModel.approvalNo"
            url="/statistics/tenant/proportionInspectApproval/pageQuery"
            :placeholder="$t('请选择')"
            :fields="{ text: 'approvalName', value: 'approvalNo' }"
            @change="approvalChange"
          />
        </mt-form-item>
        <mt-form-item prop="stockTime" :label="$t('入库时间')">
          <mt-date-range-picker
            v-model="searchFormModel.stockTime"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => stockTimeChange(e)"
          />
        </mt-form-item>
        <mt-form-item :label="$t('责任部门')" prop="respDept">
          <RemoteAutocomplete
            v-model="searchFormModel.respDept"
            url="/statistics/tenant/proportionInspectCategoryConfig/fieldPageQuery"
            :placeholder="$t('请选择')"
            :fields="{ text: 'desc', value: 'code' }"
            :multiple="true"
            params-key="fieldValue"
            :params="{
              fieldType: 1
            }"
          />
          <!-- <mt-multi-select
            v-model="searchFormModel.respDept"
            :data-source="respDeptOptions"
            :allow-filtering="true"
            filter-type="Contains"
            :filtering="getRespDeptOptions"
            :fields="{ text: 'theCodeName', value: 'id' }"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
            :multiple="true"
            params-key="respDeptCode"
          /> -->
        </mt-form-item>
        <mt-form-item :label="$t('责任议价员')" prop="bargainingRespUser">
          <RemoteAutocomplete
            v-model="searchFormModel.bargainingRespUser"
            url="/statistics/tenant/proportionInspectCategoryConfig/fieldPageQuery"
            :placeholder="$t('请选择')"
            :fields="{ text: 'desc', value: 'desc' }"
            :is-generate="false"
            :multiple="true"
            params-key="fieldValue"
            :params="{
              fieldType: 2
            }"
          />
        </mt-form-item>
        <mt-form-item prop="typeDesc" :label="$t('类别')">
          <RemoteAutocomplete
            v-model="searchFormModel.typeDesc"
            url="/statistics/tenant/proportionInspectCategoryConfig/fieldPageQuery"
            :placeholder="$t('请选择')"
            :fields="{ text: 'desc', value: 'code' }"
            :multiple="true"
            params-key="fieldValue"
            :params="{
              fieldType: 3
            }"
          />
        </mt-form-item>
        <mt-form-item prop="orgCodes" :label="$t('供应组织')">
          <RemoteAutocomplete
            v-model="searchFormModel.orgCodes"
            url="/statistics/tenant/proportionInspectApproval/querySupplyOrg"
            :placeholder="$t('请选择')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            records-position="data"
            :multiple="true"
          />
        </mt-form-item>
        <mt-form-item prop="supplierCodes" :label="$t('供应商')">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCodes"
            url="/masterDataManagement/tenant/supplier/paged-query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
            :multiple="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <div class="flex-fit">
      <sc-table
        ref="sctableRef"
        grid-id="62cadf75-e985-466a-a665-f49b7227e773"
        :loading="loading"
        :is-show-refresh-bth="true"
        :columns="columns"
        :table-data="tableData"
        keep-source
        @refresh="handleSearch"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :loading="item.loading"
            size="small"
            @click="handleClickToolBar(item)"
          >
            {{ item.name }}
          </vxe-button>
        </template>
      </sc-table>
    </div>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData } from '../config/detail'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  components: { CollapseSearch, ScTable },
  props: {
    searchType: {
      type: String,
      default: '1' // 1是默认跳转，2是点击行跳转
    },
    searchForm: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      searchFormModel: {},
      toolbar: [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      searchFormRules: {
        approvalNo: [{ required: true, message: this.$t('请选择报批'), trigger: 'blur' }],
        stockTime: [{ required: true, message: this.$t('请选择入库时间'), trigger: 'blur' }]
      },
      respDeptOptions: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    if (this.searchType === '1') {
      this.searchFormModel = {}
    } else if (this.searchType === '2') {
      this.searchFormModel = this.searchForm
    }
  },
  mounted() {
    // this.getRespDeptOptions({ text: '' })
    // this.getRespDeptOptions = utils.debounce(this.getRespDeptOptions, 500)
  },
  methods: {
    approvalChange(e) {
      this.searchFormModel.approvalId = e.itemData.id
    },
    getRespDeptOptions(e = { text: '' }) {
      let params = {
        page: {
          current: 1,
          size: 50
        },
        respDeptCode: e.text
      }
      this.$API.proportionInspectionReport.pageCategorySettingsApi(params).then((res) => {
        if (res.code === 200) {
          this.respDeptOptions =
            res.data?.records.map((item) => {
              return {
                theCodeName: item.respDeptCode + '-' + item.respDeptName,
                ...item
              }
            }) || []
          if (this.searchFormModel?.approvalId) {
            this.handleSearch()
          }
        }
      })
    },
    stockTimeChange(e) {
      if (e.startDate) {
        this.searchFormModel['stockBeginTime'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel['stockEndTime'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel['stockBeginTime'] = null
        this.searchFormModel['stockEndTime'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.currentPage = 1
      this.pageSettings.totalRecordsCount = 0
      this.tableData = []
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.proportionInspectionReport
        .pageAuditReportDetailApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$API.proportionInspectionReport
        .exportAuditReportDetailApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>
