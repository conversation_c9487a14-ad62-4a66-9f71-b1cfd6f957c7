import { i18n } from '@/main.js'

export const columnData = [
  // {
  //   type: 'checkbox',
  //   width: 50,
  //   fixed: 'left',
  //   align: 'center'
  // },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'bargainingRespUser',
    title: i18n.t('议价员')
  },
  {
    field: 'respDeptCode',
    title: i18n.t('部门'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.respDeptName : ''
    }
  },
  {
    field: 'purchaseDevelop',
    title: i18n.t('采购开发')
  },
  {
    field: 'orgCode',
    title: i18n.t('供应组织'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.orgName : ''
    }
  },
  {
    field: 'typeDesc',
    title: i18n.t('类别')
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 200,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    }
  },
  {
    field: 'rank',
    title: i18n.t('排名')
  },
  {
    title: i18n.t('入库金额'),
    headerAlign: 'center',
    children: [
      {
        field: 'warehouseAmount',
        title: i18n.t('入库金额（元）-含税'),
        minWidth: 160,
        slots: {
          default: 'warehouseAmountDefault'
        }
      },
      {
        field: 'practicalRate',
        title: i18n.t('实际执行比例'),
        formatter: ({ cellValue }) => {
          return cellValue !== null ? cellValue + '%' : ''
        }
      },
      {
        field: 'approvalQuota',
        title: i18n.t('冷年报批配额'),
        formatter: ({ cellValue }) => {
          return cellValue !== null ? cellValue + '%' : ''
        }
      },
      {
        field: 'rateDeviation',
        title: i18n.t('比例偏差'),
        formatter: ({ cellValue }) => {
          return cellValue !== null ? cellValue + '%' : ''
        }
      }
    ]
  },
  {
    title: i18n.t('入库数量'),
    headerAlign: 'center',
    children: [
      {
        field: 'warehouseAccount',
        title: i18n.t('入库数量'),
        slots: {
          default: 'warehouseAccountDefault'
        }
      },
      {
        field: 'accountPracticalRate',
        title: i18n.t('实际执行比例'),
        formatter: ({ cellValue }) => {
          return cellValue !== null ? cellValue + '%' : ''
        }
      },
      {
        field: 'accountApprovalQuota',
        title: i18n.t('冷年报批配额'),
        formatter: ({ cellValue }) => {
          return cellValue !== null ? cellValue + '%' : ''
        }
      },
      {
        field: 'accountRateDeviation',
        title: i18n.t('比例偏差'),
        formatter: ({ cellValue }) => {
          return cellValue !== null ? cellValue + '%' : ''
        }
      }
    ]
  },
  {
    field: 'highPriceAndRate',
    title: i18n.t('是否高价高比例'),
    minWidth: 160
  },
  {
    field: 'approvalSignExecuteAmount',
    title: i18n.t('是否按签批比例执行-金额'),
    minWidth: 200
  },
  {
    field: 'approvalSignExecuteAccount',
    title: i18n.t('是否按签批比例执行-数量'),
    minWidth: 200
  },
  {
    field: 'deviationRemark',
    title: i18n.t('偏差原因说明'),
    minWidth: 120
  }
]
