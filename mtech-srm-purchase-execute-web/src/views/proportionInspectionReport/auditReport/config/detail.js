import { i18n } from '@/main.js'
import dayjs from 'dayjs'

export const columnData = [
  // {
  //   type: 'checkbox',
  //   width: 50,
  //   fixed: 'left',
  //   align: 'center'
  // },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'siteCode',
    title: i18n.t('工厂'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.siteName : ''
    }
  },
  {
    field: 'companyCode',
    title: i18n.t('公司码')
  },
  {
    field: 'itemCode',
    title: i18n.t('物料'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.itemName : ''
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    }
  },
  {
    field: 'transType',
    title: i18n.t('MvT')
  },
  {
    field: 'moveType',
    title: i18n.t('移动类型')
  },
  {
    field: 'receiveCode',
    title: i18n.t('物料凭证')
  },
  {
    field: 'warehouseCode',
    title: i18n.t('库位')
  },
  {
    field: 'stockSite',
    title: i18n.t('仓储地点')
  },
  {
    field: 'itemGroupCode',
    title: i18n.t('物料组'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.itemGroupName : ''
    }
  },
  {
    field: 'quantity',
    title: i18n.t('数量')
  },
  {
    field: 'unit',
    title: i18n.t('EUn')
  },
  {
    field: 'taxedTotalPrice',
    title: i18n.t('本位币金额')
  },
  {
    field: 'receiveTime',
    title: i18n.t('过账日期'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'itemVoucherDate',
    title: i18n.t('凭证日期'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  }
]
