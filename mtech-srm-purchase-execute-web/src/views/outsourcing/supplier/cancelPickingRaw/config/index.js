import { i18n } from '@/main.js'
import { timeNumberToDate } from '@/utils/utils'
import Vue from 'vue'
const todoListToolBar = [
  {
    id: 'create',
    icon: 'icon_solid_edit',
    title: i18n.t('供应商确认'),
    permission: ['O_02_0685']
  },
  {
    id: 'back',
    icon: 'icon_solid_edit',
    title: i18n.t('供应商退回'),
    permission: ['O_02_0686']
  }
]

const todoListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: '250',

    field: 'cancelOrderCode',
    headerText: i18n.t('委外退货单号'),
    cellTools: []
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('供应商退回'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('供应商确认'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('采购退回'), cssClass: 'col-active' },
        { value: 5, text: i18n.t('采购确认'), cssClass: 'col-active' },
        { value: 6, text: i18n.t('已完结'), cssClass: 'col-active' },
        { value: 7, text: i18n.t('已取消'), cssClass: 'col-active' }
      ]
    }
    // cellTools: [
    //   {
    //     id: "delete",
    //     title: i18n.t("删除"),
    //     visibleCondition: (data) => data["status"] == 0,
    //   },
    //   {
    //     id: "cancel",
    //     title: i18n.t("取消"),
    //     visibleCondition: (data) => data["status"] == 4,
    //   },
    //   {
    //     id: "cancel",
    //     title: i18n.t("取消"),
    //     visibleCondition: (data) => data["status"] == 5,
    //   },
    // ],
  },
  {
    field: 'outsourcedType',
    headerText: i18n.t('委外方式'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('标准委外'), cssClass: '' },
        { value: 1, text: i18n.t('销售委外'), cssClass: '' },
        { value: 3, text: i18n.t('工序委外'), cssClass: '' }
      ]
    }
  },
  {
    field: 'isOutDirect',
    headerText: i18n.t('委外直退'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },
  {
    field: 'materialApproveUserName',
    headerText: i18n.t('供方确认人')
  },
  {
    field: 'materialApproveDate',
    headerText: i18n.t('供方确认时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'buyerApproveUserName',
    headerText: i18n.t('采方确认人')
  },
  {
    field: 'buyerApproveDate',
    headerText: i18n.t('采方确认时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: '0', text: i18n.t('未同步'), cssClass: '' },
        { value: '1', text: i18n.t('同步'), cssClass: '' },
        { value: '2', text: i18n.t('同步失败'), cssClass: '' }
      ]
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: 300,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerName',
    headerText: i18n.t('公司'),
    width: 300,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerCode}}-{{data.buyerName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('加工供应商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.supplierCode}}-{{data.supplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'materialSupplierCode',
    headerText: i18n.t('原材料供供应商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.materialSupplierCode}}-{{data.materialSupplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'cancelAddress',
    headerText: i18n.t('退货地址')
  },
  {
    field: 'createTime',
    headerText: i18n.t('制单日期')
  }
]

const completedListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: '250',
    field: 'cancelOrderCode',
    headerText: i18n.t('委外退货单号')
  },
  {
    field: 'cancelOrderStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('供应商退回'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('供应商确认'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('采购退回'), cssClass: 'col-active' },
        { value: 5, text: i18n.t('采购确认'), cssClass: 'col-active' },
        { value: 6, text: i18n.t('已完结'), cssClass: 'col-active' },
        { value: 7, text: i18n.t('已取消'), cssClass: 'col-active' }
      ]
    }
    // cellTools: [
    //   {
    //     id: "delete",
    //     title: i18n.t("删除"),
    //     visibleCondition: (data) => data["status"] == 0,
    //   },
    //   {
    //     id: "cancel",
    //     title: i18n.t("取消"),
    //     visibleCondition: (data) => data["status"] == 4,
    //   },
    //   {
    //     id: "cancel",
    //     title: i18n.t("取消"),
    //     visibleCondition: (data) => data["status"] == 5,
    //   },
    // ],
  },
  {
    field: 'outsourcedType',
    headerText: i18n.t('委外方式'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('标准委外'), cssClass: '' },
        { value: 1, text: i18n.t('销售委外'), cssClass: '' },
        { value: 3, text: i18n.t('工序委外'), cssClass: '' }
      ]
    }
  },
  {
    field: 'isOutDirect',
    headerText: i18n.t('委外直退'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },
  {
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'cancelQuantity',
    headerText: i18n.t('退货数量')
  },
  {
    field: 'stockUnit',
    headerText: i18n.t('单位')
  },
  {
    field: 'remark',
    headerText: i18n.t('行备注')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: 300,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerName',
    headerText: i18n.t('公司'),
    width: 300,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerCode}}-{{data.buyerName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('加工供应商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.supplierCode}}-{{data.supplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'materialSupplierCode',
    headerText: i18n.t('原材料供供应商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.materialSupplierCode}}-{{data.materialSupplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'cancelAddress',
    headerText: i18n.t('退货地址')
  },
  {
    field: 'createTime',
    headerText: i18n.t('制单日期')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('制单人')
  },
  {
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

//status  定点推荐主表状态 0待处理 3已处理 4已退回
export const pageConfig = () => [
  {
    title: i18n.t('头视图'),
    dataPermission: 'a',
    permissionCode: 'T_02_0058',
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    toolbar: todoListToolBar,
    gridId: Vue.prototype.$tableUUID.outsourcing.cancelPickingRaw.list,

    grid: {
      frozenColumns: 1,
      columnData: todoListColumnData,
      asyncConfig: {
        url: '/srm-purchase-execute/tenant/outCancelOrder/material/queryView',
        recordsPosition: 'data.records'
        // defaultRules: [
        //   {
        //     condition: "and",
        //     field: "rfx_item.rfxHeaderId",
        //     operator: "equal",
        //     rfx_bidding_item: "",
        //     rfx_item: "",
        //     value,
        //   },
        // ],
      }
    }
  },
  {
    title: i18n.t('明细视图'),
    dataPermission: 'b',
    permissionCode: 'T_02_0059',
    toolbar: todoListToolBar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    gridId: Vue.prototype.$tableUUID.outsourcing.cancelPickingRaw.details,

    grid: {
      frozenColumns: 1,

      columnData: completedListColumnData,
      asyncConfig: {
        url: '/srm-purchase-execute/tenant/outCancelOrder/material/queryDetailView'
        // defaultRules: [
        //   {
        //     condition: "and",
        //     field: "rfx_item.rfxHeaderId",
        //     operator: "equal",
        //     value,
        //   },
        // ],
      }
    }
  }
]
