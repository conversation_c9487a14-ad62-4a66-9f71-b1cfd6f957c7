<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :permission-obj="permissionObj"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
    <deliver
      @handleAddDialogShow="handleAddDialogShow"
      @resolveClick="resolveClick"
      ref="headerTop"
      v-if="deliveryShow"
      class="flex-keep"
    ></deliver>
  </div>
</template>

<script>
import { pageConfig } from './config'
export default {
  components: {
    deliver: require('./components/deliver.vue').default
  },
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0058' },
          { dataPermission: 'b', permissionCode: 'T_02_0059' }
        ]
      },
      pageConfig: pageConfig(),
      deliveryShow: false,
      chooseIds: []
    }
  },
  created() {},
  mounted() {},
  methods: {
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    resolveClick(value) {
      this.materialReject(value)
      this.deliveryShow = false
    },
    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    queryRfxConfig() {},
    //表格按钮-点击事件
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (_selectGridRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _status = _selectGridRecords.map((ele) => {
        return ele.status
      })
      console.log(_status)

      if (_status.includes(2) || _status.includes(3)) {
        this.$toast({
          content: this.$t('该状态不可以确认或退回'),
          type: 'warning'
        })
        return
      }
      if (e.toolbar.id == 'create') {
        //议价

        let arr = _selectGridRecords.map((ele) => {
          return ele.id
        })
        console.log('eeee', arr)
        this.materialConfirm(arr)
      } else if (e.toolbar.id == 'back') {
        this.chooseIds = _selectGridRecords.map((ele) => {
          return ele.id
        })
        this.deliveryShow = true
        // this.handleAddDialogShow();
        // this.accept(_selectGridRecords);
      }
    },
    materialConfirm(e) {
      this.$API.outsourcing.materialConfirm({ ids: e.join(',') }).then(() => {
        this.$toast({ content: this.$t('操作成功！'), type: 'success' })
        this.confirmSuccess()
      })
    },
    materialReject(e) {
      this.$API.outsourcing
        .materialReject({ ids: this.chooseIds.join(','), rejectReason: e })
        .then(() => {
          this.$toast({ content: this.$t('操作成功！'), type: 'success' })

          this.handleAddDialogShow()
          this.confirmSuccess()
        })
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
      console.log(e.data)
      if (e.field === 'cancelOrderCode') {
        localStorage.setItem('purchaseListScope', JSON.stringify(e.data))
        this.$router.push(
          `supplier-cancel-pickingDetail-raw?cancelOrderCode=${e.data.cancelOrderCode}`
        )
      }
    }
  }
}
</script>
<style lang="scss">
.form-design {
  padding-left: 10px;
  background: #fff;
}
.e-dlg-container > .right-wrapper {
  height: 100% !important;
  max-height: 100% !important;
  width: 95% !important;
  .dialog-panel {
    .panel-title {
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(41, 41, 41, 1);
      margin-bottom: 18px;
      &.price-title {
        margin-bottom: 0;
      }
    }
    .full-width {
      width: 100% !important;
    }
  }
}
</style>
<style lang="scss" scoped>
.full-height {
  /deep/.mt-tabs {
    background: #fafafa;
  }
  /deep/ .tab-container {
    background: #fafafa !important;
  }
}
</style>
