<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :permission-obj="permissionObj"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @handleSelectTab="handleSelectTab"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { pageConfig } from './config'
import Vue from 'vue'
export default {
  components: {},
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0056' },
          { dataPermission: 'b', permissionCode: 'T_02_0057' }
        ]
      },
      pageConfig: pageConfig(),
      rfxHeaderId: '', // 需要传参
      showDialog: false,
      rfxId: this.$route.query.rfxId, // 需要传参
      dataArr1: [],
      dataArr2: [],
      modelarr1: [],
      modelarr2: [],
      handleSelectTabShow: 0
    }
  },
  created() {
    // this.queryDistinctSupplier();
    // this.queryDistinctItem();
    this.queryRfxConfig()
  },
  mounted() {},
  methods: {
    show() {
      this.$refs.toast.ejsRef.show()
    },
    hide() {
      this.$refs.toast.ejsRef.hide()
    },
    handleSelectTab(e) {
      this.handleSelectTabShow = e
    },
    queryRfxConfig() {},
    //表格按钮-点击事件
    handleClickToolBar(e) {
      console.log(e)
      const { gridRef, toolbar } = e

      if (toolbar.id == 'create') {
        this.$router.push(`supplier-add-cancel-picking`)
        return
      }

      const selectRows = gridRef.getMtechGridRecords()

      if (selectRows.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let obj = []
      selectRows.forEach((item) => {
        obj.push(item.cancelOrderCode)
      })
      if (toolbar.id === 'print') {
        this.$API.outsourcing.outCancelOrderSupplierPrint(obj).then((res) => {
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = function () {
              console.log('======', reader)
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }

            return
          }
          const content = res.data
          let pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))

          // window.open(pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
      }
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id === 'cancel') {
        this.handleCancel(e.data.id)
      }
      if (e.tool.id === 'delete') {
        this.handleDelete(e.data.id)
      }
      if (e.tool.id === 'add') {
        this.handleAdd(e.data.id)
      }
      if (e.tool.id === 'edit') {
        // this.handleEdit(e.data.id);
        this.$router.push(`supplier-add-cancel-picking?edit=` + e.data.cancelOrderCode)
      }
    },
    handleEdit() {},
    handleAdd(e) {
      let obj = {
        ids: e
      }
      this.$API.outsourcing.outSupplierSubmit(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('提交成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    handleDelete(e) {
      this.$API.outsourcing.outCancelOrderSupplierDelete({ ids: e }).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('删除成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
        // this.$store.commit("endLoading");

        // location.reload();
      })
    },
    handleCancel(e) {
      this.$API.outsourcing
        .outCancelOrderSupplierCancel({ ids: e, cancelReason: '' })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('取消成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          }
          // this.$store.commit("endLoading");

          // location.reload();
        })
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)

      if (e.field === 'cancelOrderCode') {
        localStorage.setItem('purchaseListScope', JSON.stringify(e.data))
        this.$router.push(
          `supplier-cancel-pickingDetail-work?cancelOrderCode=${e.data.cancelOrderCode}`
        )
      }
    }
  }
}
</script>
<style lang="scss">
.form-design {
  padding-left: 10px;
  background: #fff;
}
.e-dlg-container > .right-wrapper {
  height: 100% !important;
  max-height: 100% !important;
  width: 95% !important;
  .dialog-panel {
    .panel-title {
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(41, 41, 41, 1);
      margin-bottom: 18px;
      &.price-title {
        margin-bottom: 0;
      }
    }
    .full-width {
      width: 100% !important;
    }
  }
}
</style>
<style lang="scss" scoped>
.full-height {
  /deep/.mt-tabs {
    background: #fafafa;
  }
  /deep/ .tab-container {
    background: #fafafa !important;
  }
}
</style>
