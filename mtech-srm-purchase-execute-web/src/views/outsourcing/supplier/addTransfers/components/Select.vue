<template>
  <div>
    <debounce-filter-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :request="postChange"
      :data-source="dataSource"
      :fields="{ text: 'name', value: 'value' }"
      :placeholder="placeholder"
      @change="selectChange"
      :open-dispatch-change="true"
      :disabled="isDisabled"
      :allow-filtering="true"
    ></debounce-filter-select>
  </div>
</template>
<script>
// import { utils } from "@mtech-common/utils";
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      purchase: '', //采购组code
      isDisabled: false,
      order: JSON.parse(sessionStorage.getItem('order'))
    }
  },
  mounted() {
    if (this.data.column.field === 'itemCode') {
      //物料下拉
      this.getCategoryItem()
    }
    if (this.data.column.field === 'batch') {
      this.$bus.$on('batchChange', (val) => {
        // val.stockBatchResponseList.forEach((item) => {
        //   item.name = item.batch; // 	客户名称
        //   item.value = item.batch; // 	客户名称
        // });
        this.dataSource = val || []
        if (val.stockBatchResponseList.length === 1) {
          this.$bus.$emit('stockQuantityChange', val.stockBatchResponseList[0].qty) //传给库存
          this.$bus.$emit('warehouseChange2', val)

          this.data.batch = val.stockBatchResponseList[0].batch
          this.$parent.$emit('selectedChanged', {
            fieldCode: 'batch',
            itemInfo: {
              batch: val.stockBatchResponseList[0].batch,

              basicUnit: val.stockBatchResponseList[0].unitName,
              orderItemNo: val.stockBatchResponseList[0].lineNo
              // itemId: val.itemData.id,
            }
          })
        }
        if (val.stockBatchResponseList.length === 0) {
          this.$toast({
            content: '当前物料无库存!',
            type: 'warning'
          })
        }
        val.stockBatchResponseList.forEach((item) => {
          item.name = item.batch // 	客户名称
          item.value = item.batch // 	客户名称
        })
        this.dataSource = val.stockBatchResponseList || []
        this.fields = { text: 'name', value: 'value' }
      }) //接受的单位

      //批次下拉
      // this.getCategoryItem();
    }
    if (this.data.column.field === 'warehouseName') {
      this.$bus.$on('warehouseChange', (val) => {
        console.log(val)
        val.forEach((item) => {
          item.name = item.warehouseCode + item.warehouseName // 	客户名称
          item.value = item.warehouseName == null ? '' : item.warehouseName // 	客户名称
        })
        this.dataSource = val || []
        this.data.warehouseName = val[0].warehouseName
        // this.data.warehouseCode = val[0].warehouseCode;
        this.$bus.$emit('batchChange', val[0])

        this.fields = { text: 'name', value: 'value' }
      })
    }
    if (this.data.column.field === 'siteName') {
      //工厂下拉
      this.findOrgSiteInfo()
    }
  },
  methods: {
    // 模糊
    postChange(e) {
      let obj = {
        currentPage: 1,
        keyword: '',
        pageSize: 20
      }
      obj.keyword = e.text
      this.$API.masterData.getItemByKeyword(obj).then((res) => {
        res.data.records.forEach((item) => {
          item.name = item.itemCode // 	客户名称
          item.value = item.itemCode // 	客户名称
        })
        this.dataSource = res.data.records || []
      })
    },
    findOrgSiteInfo() {
      //工厂下拉
      this.$API.masterData.findOrgSiteInfo().then((res) => {
        res.data.forEach((item) => {
          item.name = item.siteName // 	客户名称
          item.value = item.siteCode // 	客户名称
        })
        this.dataSource = res.data || []
        // this.fields = { text: "siteName", value: "siteCode" };
      })
    },
    getCategoryItem() {
      //物料下拉
      console.log(this.order)
      let obj = {
        buyerEnterpriseId: this.order.buyerEnterpriseId,
        orderCode: this.order.orderCode,
        siteCode: this.order.siteCode,
        lineNo: this.order.orderItemNo,
        itemCode: this.order.itemCode,
        itemName: this.order.itemName
      }
      this.$API.outsourcing.orderBom(obj).then((res) => {
        res.data.forEach((item) => {
          item.name = item.itemCode // 	客户名称
          item.value = item.itemCode // 	客户名称
        })
        this.dataSource = res.data || []
      })
      // this.$API.outsourcing
      //   .getOrderItemBom(this.order.orderCode)
      //   .then((res) => {
      //     res.data.forEach((item) => {
      //       item.name = item.itemCode; // 	客户名称
      //       item.value = item.itemCode; // 	客户名称
      //     });
      //     this.dataSource = res.data || [];
      //   });
      // this.fields = { text: "itemCode", value: "itemCode" };
    },
    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      if (this.data.column.field === 'itemCode') {
        this.$bus.$emit('itemNameChange', val.itemData.itemName) //传给物料名称
        this.$bus.$emit('stockUnitChange', val.itemData.unitCode) //传给单位
        this.$bus.$emit('stockUnitNameChange', val.itemData.unitName) //传给单位
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'itemCode',
          itemInfo: {
            stockUnit: val.itemData.unitCode,
            stockUnitName: val.itemData.unitName,
            itemCode: val.itemData.itemCode,
            itemName: val.itemData.itemName
          }
        })
        let params = {
          buyerEnterpriseId: '',
          itemCode: '',
          nature: '1',
          quantity: '',
          orderQuantity: '',
          siteCode: '',
          buyerOrgCode: this.order.buyerOrgCode,

          supplierCode: ''
          // warehouseCode: "",
        }
        params.itemCode = val.itemData.itemCode
        params.quantity = val.itemData.quantity
        params.orderQuantity = this.order.quantity

        params.supplierCode = this.order.outSupplierCode
        params.siteCode = this.order.siteCode
        params.buyerEnterpriseId = this.order.buyerEnterpriseId

        // this.$API.outsourcing.supplierQueryStock(params).then((r) => {
        //   this.$bus.$emit("batchChange", r.data);
        // });
        this.$API.outsourcing.queryWmsStock(params).then((r) => {
          if (r.data.length > 0) {
            this.$bus.$emit('warehouseChange', r.data)
          }
        })
      }
      if (this.data.column.field === 'batch') {
        this.$bus.$emit('stockQuantityChange', val.itemData.qty) //传给库存

        this.$bus.$emit('maxAllocationQuantityChange', val.itemData.maxCreateQty) //传给可退货数量

        this.$parent.$emit('selectedChanged', {
          fieldCode: 'batch',
          itemInfo: {
            batch: val.itemData.batch,

            basicUnit: val.itemData.unitName,
            orderItemNo: val.itemData.lineNo
            // itemId: val.itemData.id,
          }
        })
      }
      if (this.data.column.field === 'warehouseName') {
        console.log(val)
        this.$bus.$emit('batchChange', val.itemData)
      }
    }
  },
  beforeDestroy() {
    this.$bus.$off('batchChange2')
    this.$bus.$off('batchChange')
    this.$bus.$off('maxDemandQuantityChange')
    this.$bus.$off('warehouseChange')
    this.$bus.$off('itemNameChange')
    this.$bus.$off('warehouseChange2')
  }
}
</script>
