<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="middle-blank">
        <div style="display: flex">
          <div class="infos mr20">{{ $t('新建') }}</div>
          <div class="infos mr20">{{ $t('制单人：') }}{{ userInfo.username }}</div>
          <div class="infos mr20">{{ $t('制单日期：') }}{{ getCurrentTime() }}</div>
        </div>
      </div>
      <!-- 右侧各种操作按钮 -->
      <mt-button css-class="e-flat invite-btn" :is-primary="true" @click="goBack">{{
        $t('返回')
      }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" @click="submit">{{ $t('保存') }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" @click="resoveClick">{{
        $t('提交')
      }}</mt-button>
      <div class="sort-box" @click="expandChange">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="topInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="buyerEnterpriseId" :label="$t('公司')">
          <mt-select
            ref="businessRef"
            v-model="topInfo.buyerEnterpriseId"
            @change="companyClick"
            :data-source="dataArr0"
            :allow-filtering="true"
            :filtering="getSourceCompany1"
            :fields="{ text: 'name', value: 'code' }"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('委外工厂')">
          <mt-input disabled type="text" v-model="site"></mt-input>
        </mt-form-item>
        <mt-form-item prop="orderCode" :label="$t('采购订单号')">
          <debounce-filter-select
            v-model="topInfo.orderCode"
            :data-source="dataArr2"
            :fields="{ text: 'name', value: 'code' }"
            :placeholder="$t('请选择')"
            :request="filtering"
            :open-dispatch-change="false"
            @change="codeChange"
            :allow-filtering="true"
          ></debounce-filter-select>
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')">
          <mt-select
            ref="siteId"
            v-model="topInfo.orderItemNo"
            :fields="{ text: 'itemCode', value: 'itemNo' }"
            :data-source="orderList"
            @change="orderClick"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item :label="$t('物料名称')">
          <mt-input disabled type="text" v-model="topInfo.itemName"></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('采购订单行号')">
          <mt-input disabled type="text" v-model="topInfo.orderItemNo"></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('调出供应商')">
          <mt-input type="text" disabled v-model="supplier"></mt-input>
        </mt-form-item>
        <!-- <mt-form-item :label="$t('加工供应商描述')">
          <mt-input
            type="text"
            disabled
            v-model="topInfo.supplierName"
          ></mt-input>
        </mt-form-item> -->

        <mt-form-item :label="$t('调入供应商')">
          <debounce-filter-select
            :disabled="topInfo.isOutDirect == '0'"
            v-model="topInfo.inSupplierCode"
            :request="getSourceMaterialSupplier"
            :data-source="materialList"
            :value-template="supplierCodeValueTemplate"
            :fields="{ text: 'theCodeName', value: 'supplierCode' }"
            @change="materialClick"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
          ></debounce-filter-select>
        </mt-form-item>
        <mt-form-item :label="$t('领料工厂')">
          <debounce-filter-select
            v-model="topInfo.allocationSiteCode"
            :request="getSourceSitesInVendor"
            :value-template="siteCodeValueTemplate"
            :data-source="siteOptions"
            :is-active="true"
            :fields="{ text: 'theCodeName', value: 'siteCode' }"
            @change="siteOrgClick"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
          ></debounce-filter-select>
        </mt-form-item>
        <mt-form-item :label="$t('送货地址')">
          <mt-input type="text" v-model="topInfo.inSupplierAddress"> </mt-input>
        </mt-form-item>

        <mt-form-item :label="$t('是否销售委外')" prop="isOutSale">
          <mt-radio v-model="topInfo.isOutSale" :data-source="radioData"></mt-radio>
        </mt-form-item>

        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input v-model="topInfo.remark"></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { Query } from '@syncfusion/ej2-data'
import { maxPageSize } from '@/utils/constant'
import Vue from 'vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'

const buyCodeSelectTemplate = () => {
  return {
    template: Vue.component('buyCodeSelectTemplate', {
      template: `
        <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <span>{{data.customerCode}}+{{data.customerName}}</span>
            </div>
          </div>`,
      data() {
        return { data: {} }
      }
    })
  }
}

const siteCodeSelectTemplate = () => {
  return {
    template: Vue.component('siteCodeSelectTemplate', {
      template: `
    <div>
      <div>{{data.siteCode}}-{{data.siteName}}</div>
    </div>`,
      data() {
        return { data: {} }
      }
    })
  }
}
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      buyerItem: buyCodeSelectTemplate,
      supplierCodeValueTemplate: codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      }),
      siteCodeValueTemplate: siteCodeSelectTemplate, // 工厂
      radioData: [
        {
          label: this.$t('否'),
          value: '0'
        },
        {
          label: this.$t('是'),
          value: '1'
        }
      ],
      cancelData: [
        {
          label: this.$t('良品退货'),
          value: '0'
        },
        {
          label: this.$t('不良品退货'),
          value: '1'
        }
      ],
      cancelList: [],
      materialList: [],
      supplier: '',
      customerEnterpriseId: '',
      topInfo: {
        allocationOrderCode: '',
        buyerCode: '',
        buyerOrgId: '',
        allocationSiteCode: '',
        allocationSiteName: '',
        buyerEnterpriseId: '',
        buyerName: '',
        buyerOrgCode: '',
        quantity: '',
        itemName: '',
        itemCode: '',
        // buyerTenantId: 0,
        buyerTenantId: '',
        orderItemNo: '',
        orderCode: '',
        orderId: '',
        inSupplierAddress: '',

        id: '',

        inSupplierCode: '',
        inSupplierId: '',
        inSupplierName: '',
        isOutSale: '',
        remark: '',
        siteCode: '',
        siteId: '',
        siteName: '',
        outSupplierCode: '',
        outSupplierId: '',
        outSupplierName: ''
      },
      dataArr0: [],
      addForm: {},
      radioData1: [
        {
          label: this.$t('是'),
          value: '1'
        },
        {
          label: this.$t('否'),
          value: '0'
        }
      ],
      orderList: [],
      site: '',
      isExpand: true,
      rules: {
        siteCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        itemCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        orderCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        buyerEnterpriseId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        isOutDirect: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        isOutSale: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        warehouseCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        allocationSiteCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }]
      },
      expedited: '1', //待领料明细or手工创建
      siteOptions: [],
      dataArr2: [],
      userInfo: JSON.parse(sessionStorage.getItem('userInfo')),
      siteTenantList: [],
      edit: ''
    }
  },
  watch: {
    headerInfo: {
      handler(newVal) {
        console.log(newVal)
        this.edit = '1'
        this.topInfo.buyerCode = newVal.buyerCode
        this.topInfo.buyerEnterpriseId = newVal.buyerEnterpriseId
        this.topInfo.buyerName = newVal.buyerName
        this.topInfo.buyerOrgCode = newVal.buyerOrgCode

        this.topInfo.itemCode = newVal.itemCode
        this.topInfo.itemName = newVal.itemName
        this.topInfo.allocationSiteName = newVal.allocationSiteName
        this.topInfo.allocationSiteCode = newVal.allocationSiteCode
        this.topInfo.orderCode = newVal.orderCode
        this.topInfo.inSupplierAddress = newVal.inSupplierAddress
        this.topInfo.allocationOrderCode = newVal.allocationOrderCode

        this.topInfo.id = newVal.id
        this.topInfo.isOutDirect = String(newVal.isOutDirect)
        this.topInfo.isOutSale = String(newVal.isOutSale)
        this.topInfo.outSupplierCode = newVal.outSupplierCode
        this.topInfo.outSupplierId = newVal.outSupplierId
        this.topInfo.outSupplierName = newVal.outSupplierName
        this.topInfo.remark = newVal.remark
        this.topInfo.siteCode = newVal.siteCode
        this.topInfo.siteId = newVal.siteId
        this.topInfo.siteName = newVal.siteName
        this.topInfo.inSupplierCode = newVal.inSupplierCode
        this.topInfo.inSupplierId = newVal.inSupplierId
        this.topInfo.inSupplierName = newVal.inSupplierName

        this.codeChange({ value: this.topInfo.orderCode })
      },
      deep: true
    }
  },
  mounted() {
    this.getSourceCompany()

    // this.purOrderQueryOrder();
    // this.init();

    // this.topInfo.supplierCode = this.userInfo.enterpriseCode;
    // this.topInfo.supplierName = this.userInfo.enterpriseName;
    // this.topInfo.supplierId = this.userInfo.enterpriseId;
    // this.topInfo.supplierCode = this.userInfo.enterpriseCode;
    // this.topInfo.supplierName = this.userInfo.enterpriseName;

    // this.topInfo.buyerCompanyOrgCode = this.userInfo.enterpriseCode;
  },
  methods: {
    // init() {
    //   this.topInfo.remark = this.headerInfo.remark;
    // },

    resoveClick() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('resolve', this.topInfo)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 原材料供应商点击事件
    materialClick(val) {
      console.log(val)
      this.topInfo.inSupplierCode = val.itemData?.supplierCode
      this.topInfo.inSupplierId = val.itemData?.supplierId
      this.topInfo.inSupplierName = val.itemData?.supplierName
    },
    // 当前日期
    getCurrentTime() {
      let yy = new Date().getFullYear()
      let mm = new Date().getMonth() + 1
      let dd = new Date().getDate()
      let hh = new Date().getHours()
      let mf =
        new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes()
      let ss =
        new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds()
      return yy + '/' + mm + '/' + dd + ' ' + hh + ':' + mf + ':' + ss
    },
    // 工厂点击
    siteOrgClick(e) {
      console.log(e.itemData)
      this.topInfo.buyerOrgId = e.itemData?.organizationId
      this.topInfo.allocationSiteCode = e.itemData?.siteCode
      this.topInfo.allocationSiteName = e.itemData?.siteName
    },
    warehouseClick(val) {
      console.log(val)

      this.topInfo.warehouseCode = val.itemData?.siteAddress

      this.cancelList = []

      let obj = {
        enterpriseId: this.customerEnterpriseId,
        params: {
          page: {
            size: maxPageSize,
            current: 1
          },
          condition: 'and',
          defaultRules: [
            {
              // 工厂
              field: 'siteCode',
              operator: 'equal',
              value: this.topInfo.siteCode
            },
            {
              // 交货库存地点
              field: 'siteAddress',
              operator: 'equal',
              value: this.topInfo.warehouseCode
            }
          ]
        }
      }
      this.$API.receiptAndDelivery.postSiteTenantExtendQueryByEnterpriseId(obj).then((res) => {
        this.cancelList = res.data.records
      })
    },

    // 获取 原材料供应商
    getMaterialList(val) {
      let obj = {
        fuzzyNameOrCode: val,
        tenantId: this.topInfo.buyerTenantId
      }
      this.$API.masterData.authGetSupplier(obj).then((res) => {
        const list = res?.data || []
        this.materialList = addCodeNameKeyInList({
          firstKey: 'supplierCode',
          secondKey: 'supplierName',
          list
        })
      })
    },
    getSourceMaterialSupplier(e) {
      console.log(e)
      this.getMaterialList(e.text)
    },
    submit() {
      console.log(this.topInfo)
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('submitClick', this.topInfo)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    companyClick(val) {
      console.log(val)
      if (val.e !== null) {
        if (this.topInfo.buyerEnterpriseId) {
          this.$emit('deleteRe')
        }
        this.topInfo.orderCode = ''
        this.topInfo.itemName = ''
        this.topInfo.orderItemNo = ''

        this.topInfo.itemCode = ''
        this.topInfo.quantity = ''
        this.topInfo.siteCode = ''
        this.topInfo.siteName = ''
        this.topInfo.buyerOrgCode = ''
      }

      this.$API.masterData
        .findInBuyingByCustomerCode({ customerCode: val.itemData.customerCode })
        .then((res) => {
          this.topInfo.buyerOrgCode = res.data.organizationCode
          this.topInfo.outSupplierId = res.data.supplierId
          this.supplier = res.data.supplierCode + res.data.supplierName
          this.topInfo.outSupplierCode = res.data.supplierCode
          this.topInfo.outSupplierName = res.data.supplierName
        })
      this.customerEnterpriseId = val.value
      this.topInfo.buyerCode = val.itemData.customerCode
      this.topInfo.buyerTenantId = val.itemData.customerTenantId
      this.topInfo.buyerName = val.itemData.customerName
      this.getSourceSitesInVendor({})
      this.getMaterialList('') // 获取原材料
      let params = {
        enterpriseId: this.customerEnterpriseId,
        page: {
          current: 1,
          size: 10000
        },
        defaultRules: [
          {
            field: 'orderCode',
            operator: 'contains',
            // type: "number",
            value: ''
          }
        ]
        // enterpriseId: ,
      }
      this.$API.purchaseOrder.getPurOrderQueryOrder(params).then((r) => {
        this.dataArr2 = r.data.records.map((e) => {
          return { name: e, code: e }
        })
      })
    },
    getSourceCompany() {
      let obj = {
        fuzzyNameOrCode: ''
      }

      this.$API.masterData.getCustomer(obj).then((res) => {
        res.data.forEach((item) => {
          item.name = item.customerCode + item.customerName
          item.code = item.customerEnterpriseId
        })
        this.dataArr0 = res.data
        // this.topInfo.buyerCode = this.headerInfo.buyerCode;
        // this.topInfo.buyerEnterpriseId = this.headerInfo.buyerEnterpriseId;
        // this.topInfo.buyerName = this.headerInfo.buyerName;
        // console.log("----------", this.headerInfo.buyerCode, this.topInfo);
      })
    },
    getSourceCompany1(e) {
      var searchData = this.dataArr0
      // this.purOrderQueryOrder(e.text);

      // load overall data when search key empty.
      if (e.text == '') e.updateData(searchData)
      else {
        let query = new Query().select(['name', 'code', 'customerCode', 'customerName'])
        // change the type of filtering
        query = e.text !== '' ? query.where('name', 'contains', e.text, true) : query
        console.log(query)
        e.updateData(searchData, query)
        console.log(searchData)
      }
    },
    formFun(rule, value, callback) {
      console.log(rule, value, callback)
    },
    orderClick(e) {
      this.topInfo.itemName = e.itemData?.itemName
      this.topInfo.orderItemNo = e.itemData?.itemNo
      this.site = e.itemData?.siteCode + e.itemData?.siteName
      this.topInfo.itemCode = e.itemData?.itemCode
      this.topInfo.quantity = e.itemData?.quantity
      this.topInfo.siteCode = e.itemData?.siteCode
      this.topInfo.siteName = e.itemData?.siteName
      // this.topInfo.buyerOrgCode = e.itemData.buyerOrgCode;
      if (e.e !== null && this.edit !== '1') {
        this.topInfo.allocationSiteCode = e.itemData?.siteCode
        this.topInfo.allocationSiteName = e.itemData?.siteName
      }
      console.log(this.topInfo)
      sessionStorage.setItem('order', JSON.stringify(this.topInfo))

      this.$emit('orderChange', this.topInfo, {
        supplierCode: this.topInfo.outSupplierCode,
        orderQuantity: this.topInfo.quantity,
        buyerOrgCode: this.topInfo.buyerOrgCode,
        orderCode: this.topInfo.orderCode,
        buyerEnterpriseId: this.topInfo.buyerEnterpriseId,
        siteCode: this.topInfo.siteCode,
        itemCode: this.topInfo.itemCode,
        itemName: this.topInfo.itemName,
        lineNo: this.topInfo.orderItemNo
      })
    },
    whitch(e) {
      console.log(e)
      if (e === '0') {
        this.topInfo.materialSupplierCode = ''
        this.topInfo.materialSupplierName = ''
      }
    },

    filtering(e) {
      let params = {
        // siteCode: this.topInfo.siteCode,
        enterpriseId: this.customerEnterpriseId,
        page: {
          current: 1,
          size: 10000
        },
        defaultRules: [
          {
            field: 'orderCode',
            operator: 'contains',
            // type: "number",
            value: e.text
          }
        ]
      }
      this.$API.purchaseOrder.getPurOrderQueryOrder(params).then((r) => {
        this.dataArr2 = r.data.records.map((e) => {
          return { name: e, code: e }
        })
      })

      // var searchData = this.dataArr2;
      // // this.purOrderQueryOrder(e.text);
      // // load overall data when search key empty.
      // if (e.text == "") e.updateData(searchData);
      // else {
      //   let query = new Query().select(["text", "value"]);
      //   // change the type of filtering
      //   query =
      //     e.text !== "" ? query.where("text", "contains", e.text, true) : query;
      //   console.log(query);
      //   e.updateData(searchData, query);

      // }
    },
    // 获取主数据-工厂
    getSourceSitesInVendor(args) {
      const { text, updateData } = args
      const params = {
        customerCode: this.topInfo.buyerCode,
        enterpriseId: this.customerEnterpriseId,
        fuzzyParam: text,
        dataLimit: 10000
      }
      this.$API.masterData
        .siteQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
          }
        })
        .catch(() => {})
    },
    goBack() {
      this.$router.go(-1)
    },

    expandChange() {
      this.isExpand = !this.isExpand
      // this.$refs.ruleForm.clearValidate();
    },
    onchang(e) {
      console.log('onchang', e, this.expedited)
    },
    codeChange(e) {
      if (e.e !== null) {
        // this.$emit("codeChange", e.value);
        if (this.topInfo.itemCode) {
          this.$emit('deleteRe')
        }
        this.topInfo.itemName = ''
        this.topInfo.orderItemNo = ''

        this.topInfo.itemCode = ''
        this.topInfo.quantity = ''
        this.topInfo.siteCode = ''
        this.topInfo.siteName = ''
        // this.topInfo.buyerOrgCode = "";
        this.$emit('siteClick', this.topInfo)
      }
      this.$API.purchaseOrder.purOrderGetByOrder({ code: e.value }).then((res) => {
        if (res.data.length === 0) {
          this.$toast({
            content: '当前订单的物料不存在工单BOM和标准BOM!',
            type: 'warning'
          })
          this.orderList = res.data
          return
        }
        this.orderList = res.data
        this.orderClick({ itemData: this.orderList[0] })
      })
    },
    // 获取其他数据 code、name
    getOtherInfo(params) {
      // 业务类型、申请人、公司、申请部门、采购组织
      if (this.topInfo.siteId && this.$refs.businessRef) {
        let _data = this.$refs.businessRef.ejsRef.getDataByValue(this.topInfo.siteId)
        // console.log("params", _data);
        for (let key in _data) {
          params[key] = _data[key]
        }
        if (!_data) return
      }
      console.log('params', params)
      return params
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin: 20px 20px 20px 0;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .itemcon {
      display: flex;
      align-items: center;
    }
    .item {
      margin-right: 20px;
    }
    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      // &.more-width {
      //   // width: 450px;
      // }

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
