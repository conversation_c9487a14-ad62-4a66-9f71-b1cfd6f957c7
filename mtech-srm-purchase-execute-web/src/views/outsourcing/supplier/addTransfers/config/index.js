import { i18n } from '@/main.js'
import Select from '../components/Select.vue'
import InputView from '../components/InputView'
import Input from '../components/Input'
import InputNumber from '../components/InputNumber'
import Vue from 'vue'
const todoListToolBar = ['add', 'delete']

const todoListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'addId',
    headerText: 'addId',
    width: 0,
    visible: false,
    allowEditing: false
  },
  {
    field: 'itemCode',
    width: '250',
    headerText: i18n.t('物料编码'),

    allowEditing: true,
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料编号')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料描述'),
    allowEditing: false,
    editTemplate: () => {
      return { template: InputView }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('物料描述')}}</span>
              </div>
            `
        })
      }
    }
  },

  {
    field: 'warehouseName',
    headerText: i18n.t('库存地点'),
    width: 300,
    allowEditing: false,
    // editTemplate: () => {
    //   return { template: InputView };
    // },
    editTemplate: () => {
      return { template: Select }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('库存地点')}}</span>
              </div>
            `
        })
      }
    },
    valueAccessor: (field, data) => {
      return data.warehouseCode + data.warehouseName
    }
  },
  {
    field: 'warehouseCode',
    headerText: i18n.t('库存地点编号'),
    width: 200,
    allowEditing: false,
    visible: false,

    editTemplate: () => {
      return { template: InputView }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('库存地点编号')}}</span>
              </div>
            `
        })
      }
    }
  },
  {
    field: 'batch',
    headerText: '批次/卷号',
    editTemplate: () => {
      return { template: Select }
    }
    // headerTemplate: () => {
    //   return {
    //     template: Vue.component("headers", {
    //       template: `
    //           <div class="headers">
    //             <span style="color: red">*</span>
    //             <span class="e-headertext">{{$t('批次/卷号')}}</span>
    //           </div>
    //         `,
    //     }),
    //   };
    // },
    // validationRules: { required: true },
  },
  {
    field: 'allocationQuantity',
    headerText: i18n.t('调出数量'),
    editTemplate: () => {
      return { template: InputNumber }
    },
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('调出数量')}}</span>
              </div>
            `
        })
      }
    }

    // validationRules: { required: true },
  },
  {
    field: 'maxAllocationQuantity',
    headerText: i18n.t('可调拨数量'),
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'stockQuantity', // 只是界面显示
    headerText: i18n.t('库存现有量'),
    editTemplate: () => {
      return { template: InputView }
    }
  },
  {
    field: 'stockUnit',
    headerText: i18n.t('库存单位'),
    editTemplate: () => {
      return { template: InputView }
    }
  },
  // {
  //   field: "", // 只是界面显示
  //   headerText: i18n.t("最小包装量"),
  // },

  {
    field: 'remark',
    headerText: i18n.t('行备注'),
    editTemplate: () => {
      return { template: Input }
    }
  }
]

//status  定点推荐主表状态 0待处理 3已处理 4已退回
export const pageConfig = [
  {
    title: i18n.t('物料信息'),
    // useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    toolbar: todoListToolBar,

    grid: {
      // height: "auto",

      allowPaging: false,
      frozenColumns: 0,
      editSettings: {
        allowEditing: true,
        allowAdding: true,
        allowDeleting: true,
        mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
        showConfirmDialog: false,
        // showDeleteConfirmDialog: true,
        newRowPosition: 'Bottom'
      },
      columnData: todoListColumnData,
      dataSource: []

      // asyncConfig: {
      //   url: `/srm-purchase-execute/tenant/order_item_bom/${code}`,
      //   recordsPosition: "data.records",
      //   methods: "get",
      //   // defaultRules: [
      //   //   {
      //   //     condition: "and",
      //   //     field: "rfx_item.rfxHeaderId",
      //   //     operator: "equal",
      //   //     rfx_bidding_item: "",
      //   //     rfx_item: "",
      //   //     value,
      //   //   },
      //   // ],
      // },
    }
  }
]

export const editSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: true,
  newRowPosition: 'Bottom'
}
