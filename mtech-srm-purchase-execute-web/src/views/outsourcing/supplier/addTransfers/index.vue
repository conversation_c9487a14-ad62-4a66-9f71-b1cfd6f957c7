<template>
  <div class="full-height pt20 vertical-flex-box">
    <top-info
      @submitClick="submitClick"
      @codeChange="codeChange"
      @orderChange="orderChange"
      @resolve="resolve"
      @deleteRe="deleteRe"
      :header-info="headerInfo"
      ref="topInfo"
      class="flex-keep"
    ></top-info>
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :load="load"
      @actionBegin="actionBegin"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @warehouseChanged="warehouseChanged"
      @selectedChanged="selectedChanged"
    >
    </mt-template-page>
    <!-- </div> -->
  </div>
</template>
<script>
import { pageConfig, editSettings } from './config/index.js'

export default {
  components: {
    TopInfo: () => import('./components/topInfo')
  },
  data() {
    return {
      headerInfo: {
        siteCode: '',
        remark: ''
      },
      editSettings,
      pageConfig: pageConfig,
      rowList: [],
      topList: {},
      entryId: '', //编辑带入的订单id
      entryType: null, //1是新增 2是编辑 3是反馈异常
      entrySource: '', // 0采购申请 1手工创建 2商城进入 4合同进入
      entryDraft: '', //1是草稿 2不是草稿 草稿可以修改
      ids: '',
      saveList: null,
      topInfo: {
        orderCode: '',
        createType: '1',
        isOutSale: '0'
      },
      selectedOtherInfo: {},

      edit: ''
    }
  },
  mounted() {
    this.edit = this.$route.query.edit
    if (this.edit) {
      this.getOnePicking()
    }
    window.pickGrid = this.$refs.templateRef
  },
  methods: {
    load(args) {
      console.log('load-----------', args)
      this.$refs.grid.$el.addEventListener('keydown', this.keyDownHandler)
    },
    keyPressed(args) {
      console.log('keyPressed', args)
    },
    keyDownHandler(e) {
      console.log('keyDownHandler', e)
    },
    submitClick(val) {
      let currentRecords =
        this.$refs.templateRef?.getCurrentUsefulRef().gridRef?.ejsRef.getCurrentViewRecords() || []
      console.log(
        this.$refs.templateRef?.getCurrentUsefulRef().gridRef?.ejsRef.getCurrentViewRecords()
      )
      this.rowList = currentRecords
      let obj = {
        detailRequestList: this.rowList,
        ...val
      }

      for (let i of obj.detailRequestList) {
        if (Number(i.allocationQuantity) === 0) {
          this.$toast({
            content: this.$t('调出数量不能为0'),
            type: 'warning'
          })
          return
        }

        if (Number(i.allocationQuantity) > Number(i.stockQuantity)) {
          this.$toast({
            content: '调出数量不可超过库存现有量!',

            type: 'warning'
          })
          return
        }
      }
      obj.detailRequestList.forEach((item) => {
        return (item.orderCode = val.orderCode), (item.orderItemNo = val.orderItemNo)
      })
      if (this.ids.length > 0) {
        obj.id = this.ids
      }
      this.$API.outsourcing.outAllocationOrderSave(obj).then((r) => {
        if (r.code === 200) {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
          this.ids = r.data
        }
      })
    },
    resolve(val) {
      if (val.status === 2 || val.status === 4) {
        let obj = {
          detailRequestList: this.rowList,
          ...val
        }
        for (let i of obj.detailRequestList) {
          if (Number(i.allocationQuantity) === 0) {
            this.$toast({
              content: this.$t('调出数量不能为0'),
              type: 'warning'
            })
            return
          }
        }
        obj.detailRequestList.forEach((item) => {
          return (item.orderCode = val.orderCode), (item.orderItemNo = val.orderItemNo)
        })
        if (this.ids.length > 0) {
          obj.id = this.ids
        }
        this.$API.outsourcing.outAllocationOrderSave(obj).then((r) => {
          this.$API.outsourcing.outAllocationOrderSubmit({ ids: r.data }).then(() => {
            // if (res.code === 200) {
            this.$toast({
              content: this.$t('提交成功'),
              type: 'success'
            })
            this.$router.go(-1)
            // }
          })
        })
      }
      let currentRecords =
        this.$refs.templateRef?.getCurrentUsefulRef().gridRef?.ejsRef.getCurrentViewRecords() || []

      this.rowList = currentRecords
      console.log(this.ids.length)
      if (this.ids.length > 0) {
        let obj = {
          ids: this.ids
        }
        this.$API.outsourcing.outAllocationOrderSubmit(obj).then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('提交成功'),
              type: 'success'
            })
            this.$router.go(-1)
          }
        })
      } else {
        let obj = {
          detailRequestList: this.rowList,
          ...val
        }
        for (let i of obj.detailRequestList) {
          if (Number(i.allocationQuantity) === 0) {
            this.$toast({
              content: this.$t('调出数量不能为0'),
              type: 'warning'
            })
            return
          }
          if (Number(i.allocationQuantity) > Number(i.stockQuantity)) {
            this.$toast({
              content: '调出数量不可超过库存现有量!',

              type: 'warning'
            })
            return
          }
        }
        obj.detailRequestList.forEach((item) => {
          return (item.orderCode = val.orderCode), (item.orderItemNo = val.orderItemNo)
        })
        if (this.ids.length > 0) {
          obj.id = this.ids
        }
        this.$API.outsourcing.outAllocationOrderSave(obj).then((r) => {
          this.$API.outsourcing.outAllocationOrderSubmit({ ids: r.data }).then(() => {
            // if (res.code === 200) {
            this.$toast({
              content: this.$t('提交成功'),
              type: 'success'
            })
            this.$router.go(-1)
            // }
          })
        })
      }
    },
    selectedChanged(val) {
      console.log(val)
      console.log(this.selectedOtherInfo)
      Object.assign(this.selectedOtherInfo, val.itemInfo)
      console.log(this.selectedOtherInfo, '最新的额外数据')
    },
    getOnePicking() {
      this.$API.outsourcing
        .outAllocationOrderGetOne({ allocationOrderCode: this.edit })
        .then((r) => {
          // this.topInfo.siteId = r.data.siteId;
          // this.topInfo.supplierCode = r.data.supplierCode;
          // this.topInfo.orderCode = r.data.orderCode;
          // this.topInfo.isOutSale = r.data.isOutSale;
          // this.topInfo.remark = r.data.remark;
          // sessionStorage.setItem("order", JSON.stringify(r.data));

          this.headerInfo = r.data || {}
          this.topList.siteCode = r.data.siteCode
          console.log(this.headerInfo)
          this.pageConfig[0].grid.dataSource = r.data.detailResponseList
        })
    },
    orderChange(e, obj) {
      console.log(e)
      this.topList.itemCode = e.itemCode
      this.$API.outsourcing.AllocationOrderSupplierOrderBomStock(obj).then((r) => {
        let obj = r.data.map((item) => {
          return {
            ...item,
            stockUnit: item.unitCode,
            maxAllocationQuantity:
              item.wmsStockResponseList[0].stockBatchResponseList[0].maxCreateQty,
            stockQuantity: item.wmsStockResponseList[0].stockBatchResponseList[0].qty,

            // itemName: "",
            // itemCode:item.itemCode
            ...item.wmsStockResponseList[0],
            ...item.wmsStockResponseList[0].stockBatchResponseList[0]
          }
        })
        console.log(obj)
        this.pageConfig[0].grid.dataSource = obj
        console.log(this.pageConfig[0].grid.dataSource)
      })
    },
    codeChange(e) {
      this.topList.orderCode = e
    },
    deleteRe() {
      // this.$dialog({
      //   data: {
      //     title: this.$t("确认"),
      //     message: this.$t(
      //       "更换公司和采购订单将重新清空/获取领料物料数据，请确定是否继续?"
      //     ),
      //   },
      // success: () => {
      let currentRecords =
        this.$refs.templateRef?.getCurrentUsefulRef().gridRef?.ejsRef.getCurrentViewRecords() || []
      if (currentRecords.length > 0) {
        let numList = []
        for (let i = 0; i < currentRecords.length; i++) {
          numList.push(i)
        }
        console.log(numList)
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRows(numList)
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
      }
      //   },
      // });
    },
    handleClickToolBar(e) {
      console.log('handleClickToolBar', e)

      if (e.toolbar.id === 'Add') {
        if (
          this.topList.itemCode === undefined ||
          this.topList.itemCode === null ||
          this.topList.itemCode === ''
        ) {
          this.$toast({
            content: this.$t('头部带星号的为必填项'),
            type: 'warning'
          })
        } else {
          console.log(this.topList)
          console.log('ref', this.$refs.templateRef.getCurrentUsefulRef())
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
        }
      } else if (e.toolbar.id === 'Delete') {
        console.log(this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef)
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
      }
    },
    warehouseChanged(e) {
      this.saveList = e
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
    },
    handleClickCellTitle(e) {
      console.log('handleClickCellTitle', e)
    },

    actionBegin(args) {
      console.log('actionBegin', args)
      // const { index } = args;

      if (args.requestType === 'save') {
        if (this.saveList !== null) {
          console.log(this.saveList)
          args.data.warehouseCode = this.saveList.itemInfo.warehouseCode
          console.log(args.data)
        }
        if (Number(args.data.allocationQuantity) > Number(args.data.stockQuantity)) {
          this.$toast({
            content: '调出数量不可超过库存现有量!',
            type: 'warning'
          })
        }
      }

      if (args.requestType == 'add' || args.requestType == 'beginEdit') {
        args.data.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
        this.isEditStatus = true
        // this.nowEditRowFlag = args.data.addId;

        console.log(args.data)
      }
      if (args.requestType == 'refresh') {
        this.isEditStatus = false
      }
    },

    addRow(row) {
      console.log('addRow', row)
    }
  },
  beforeDestroy() {
    this.pageConfig[0].grid.dataSource = []
    sessionStorage.removeItem('order')
  }
}
</script>
<style lang="scss" scoped>
// /deep/ .top-info {
//   margin-top: 20px;
// }
.bottom-tables {
  height: 100%;
}
// .addPicking {
//   background: #fff;
//   display: flex;
//   flex-direction: column;
//   .template-height {
//     height: auto;
//     flex: 1;
//     max-height: calc(100% - 270px);
//     /deep/ .e-gridcontent {
//       height: 100%;
//     }
//     /deep/ .e-gridcontent {
//       height: 100%;
//     }
//   }
// }
</style>
