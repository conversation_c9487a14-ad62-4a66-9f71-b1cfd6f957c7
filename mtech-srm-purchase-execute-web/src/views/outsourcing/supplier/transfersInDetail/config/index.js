import { i18n } from '@/main.js'
import Vue from 'vue'
export const columnData = [
  {
    field: 'itemCode',
    width: '250',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },

  {
    field: 'warehouseName',
    headerText: i18n.t('库存地点'),
    width: 300,
    valueAccessor: (field, data) => {
      return data.warehouseCode + data.warehouseName
    }
  },
  {
    field: 'warehouseCode',
    headerText: i18n.t('库存地点编号'),
    width: 200
  },
  {
    field: 'batch',
    headerText: '批次/卷号'
  },
  {
    field: 'allocationQuantity',
    headerText: i18n.t('调出数量')

    // validationRules: { required: true },
  },
  {
    field: 'wmsAllocationQuantity',
    headerText: i18n.t('实收数量'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div><mt-input-number precision="3" :min="0" v-if='data.type === 1' v-model="data.wmsAllocationQuantity"  cssClass="e-outline" :show-clear-button="false" @blur="handleChange"></mt-input-number>
          <div v-else>{{data.wmsAllocationQuantity}}</div>
          </div> `,
          data: function () {
            return {
              data: {}
            }
          },
          mounted() {
            console.log(this.data)
            // alert(this.data.receiveQuantity);
          },
          methods: {
            handleChange() {
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'wmsAllocationQuantity',
                value: this.data
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'stockUnit',
    headerText: i18n.t('库存单位')
  },
  // {
  //   field: "", // 只是界面显示
  //   headerText: i18n.t("最小包装量"),
  // },

  {
    field: 'remark',
    headerText: i18n.t('行备注')
  }
]
