import { i18n } from '@/main.js'
import { timeNumberToDate } from '@/utils/utils'
import Vue from 'vue'
const todoListToolBar = [
  {
    id: 'create',
    icon: 'icon_solid_edit',
    title: i18n.t('供应商确认'),
    permission: ['O_02_1135']
  },
  {
    id: 'back',
    icon: 'icon_solid_edit',
    title: i18n.t('供应商退回'),
    permission: ['O_02_1136']
  }
]

const todoListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'allocationOrderCode',
    headerText: i18n.t('委外调拨单号'),
    cellTools: []
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待供应商确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('调入供应商退回'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('调入供应商确认'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('采购退回'), cssClass: 'col-active' },
        { value: 5, text: i18n.t('采购确认'), cssClass: 'col-active' },
        { value: 6, text: i18n.t('已完结'), cssClass: 'col-active' },
        { value: 7, text: i18n.t('已取消'), cssClass: 'col-active' }
      ]
    }
  },
  {
    field: 'isOutSale',
    headerText: i18n.t('销售委外'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },

  // {
  //   field: "materialApproveUserName",
  //   headerText: i18n.t("供方确认人"),
  // },
  // {
  //   field: "materialApproveDate",
  //   headerText: i18n.t("供方确认时间"),
  //   valueConverter: {
  //     type: "function",
  //     filter: (e) => {
  //       if (e && !isNaN(e) && e.length == 13) {
  //         e = Number(e);
  //         return timeNumberToDate({
  //           formatString: "YYYY-mm-dd",
  //           value: e,
  //         });
  //       } else {
  //         return "";
  //       }
  //     },
  //   },
  // },
  // {
  //   field: "buyerApproveUserName",
  //   headerText: i18n.t("采方确认人"),
  // },
  // {
  //   field: "buyerApproveDate",
  //   headerText: i18n.t("采方确认时间"),
  //   valueConverter: {
  //     type: "function",
  //     filter: (e) => {
  //       if (e && !isNaN(e) && e.length == 13) {
  //         e = Number(e);
  //         return timeNumberToDate({
  //           formatString: "YYYY-mm-dd",
  //           value: e,
  //         });
  //       } else {
  //         return "";
  //       }
  //     },
  //   },
  // },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: '0', text: i18n.t('未同步'), cssClass: '' },
        { value: '1', text: i18n.t('同步'), cssClass: '' },
        { value: '2', text: i18n.t('同步失败'), cssClass: '' }
      ]
    }
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: 300,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerCode',
    headerText: i18n.t('公司'),
    width: 300,
    allowEditing: false,
    valueAccessor: (field, data) => {
      return data.buyerCode + data.buyerName
    }
  },
  {
    field: 'outSupplierCode',
    headerText: i18n.t('调出供应商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.outSupplierCode}}-{{data.outSupplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'inSupplierCode',
    headerText: i18n.t('调入供应商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.inSupplierCode}}-{{data.inSupplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'inSupplierAddress',
    headerText: i18n.t('送货地址')
  },
  {
    field: 'inApproveUserName',
    headerText: i18n.t('供方确认人')
  },
  {
    field: 'inApproveDate',
    headerText: i18n.t('供方确认时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'buyerApproveUserName',
    headerText: i18n.t('采方确认人')
  },
  {
    field: 'buyerApproveDate',
    headerText: i18n.t('采方确认时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'createTime',
    headerText: i18n.t('制单日期')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('制单人')
  }
]

const completedListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'allocationOrderCode',
    headerText: i18n.t('委外调拨单号')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待供应商确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('调入供应商退回'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('调入供应商确认'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('采购退回'), cssClass: 'col-active' },
        { value: 5, text: i18n.t('采购确认'), cssClass: 'col-active' },
        { value: 6, text: i18n.t('已完结'), cssClass: 'col-active' },
        { value: 7, text: i18n.t('已取消'), cssClass: 'col-active' }
      ]
    }
  },
  {
    field: 'isOutSale',
    headerText: i18n.t('销售委外'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },

  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    field: 'allocationQuantity',
    headerText: i18n.t('调出数量')
  },
  {
    field: 'wmsAllocationQuantity',
    headerText: i18n.t('实收数量')
  },
  {
    field: 'stockUnit',
    headerText: i18n.t('单位')
  },
  {
    field: 'batch',
    headerText: i18n.t('批次/卷号')
  },
  {
    field: 'remark',
    headerText: i18n.t('行备注')
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    width: 300,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerCode',
    headerText: i18n.t('公司'),
    width: 300,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerCode}}-{{data.buyerName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'outSupplierCode',
    headerText: i18n.t('调出供应商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.outSupplierCode}}-{{data.outSupplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'inSupplierCode',
    headerText: i18n.t('调入供应商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.inSupplierCode}}-{{data.inSupplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'inSupplierAddress',
    headerText: i18n.t('送货地址')
  },
  {
    field: 'createTime',
    headerText: i18n.t('制单日期')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('制单人')
  }
]

//status  定点推荐主表状态 0待处理 3已处理 4已退回
export const pageConfig = () => [
  {
    title: i18n.t('头视图'),
    dataPermission: 'a',
    permissionCode: 'T_02_0119',
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    toolbar: todoListToolBar,
    grid: {
      frozenColumns: 1,
      columnData: todoListColumnData,
      asyncConfig: {
        url: '/srm-purchase-execute/tenant/outAllocationOrder/inSupplier/queryView',
        recordsPosition: 'data.records'
        // defaultRules: [
        //   {
        //     condition: "and",
        //     field: "rfx_item.rfxHeaderId",
        //     operator: "equal",
        //     rfx_bidding_item: "",
        //     rfx_item: "",
        //     value,
        //   },
        // ],
      }
    }
  },
  {
    title: i18n.t('明细视图'),
    dataPermission: 'b',
    permissionCode: 'T_02_0118',
    toolbar: todoListToolBar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      frozenColumns: 1,

      columnData: completedListColumnData,
      asyncConfig: {
        url: '/srm-purchase-execute/tenant/outAllocationOrder/inSupplier/queryDetailView'
        // defaultRules: [
        //   {
        //     condition: "and",
        //     field: "rfx_item.rfxHeaderId",
        //     operator: "equal",
        //     value,
        //   },
        // ],
      }
    }
  }
]
