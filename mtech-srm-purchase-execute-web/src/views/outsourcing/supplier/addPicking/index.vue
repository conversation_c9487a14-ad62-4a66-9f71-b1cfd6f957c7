<template>
  <!-- <div class="detail-fix-wrap full-height"> -->
  <div class="full-height pt20 vertical-flex-box">
    <top-info
      @deleteRe="deleteRe"
      :top-info-e="topInfo"
      :entry-type="entryType"
      :entry-new-rule-form="entryNewRuleForm"
      @submit="submit"
      @onchang="onchang"
      @save="save"
      @codeChange="codeChange"
      @updateDataSource="updateDataSource"
      @newSave="newSave"
      @newSubmit="newSubmit"
      class="flex-keep"
      ref="topInfo"
    ></top-info>
    <!-- <mt-data-grid
      :column-data="todoListColumnData"
      :data-source="[]"
      :edit-settings="editSettings"
      :toolbar="toolbar"
    ></mt-data-grid> -->
    <!--订单BOM-->
    <mt-template-page
      ref="templateRef23"
      :template-config="pageConfig"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
      @selectedChanged="selectedChanged"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      v-show="(topInfo.createType == '2' || entryType == 'readonly') && topInfo.createType !== '3'"
    >
    </mt-template-page>
    <!--待领料明细-->
    <mt-template-page
      class="bottom-box"
      ref="templateRef2"
      :template-config="pageConfig2"
      @actionBegin="actionBegin2"
      @selectedChanged="selectedChanged2"
      @handleClickToolBar="handleClickToolBar2"
      v-show="topInfo.createType == '1' && (entryType == 'edit' || !entryType)"
    >
    </mt-template-page>
    <!--手工创建-->
    <mt-template-page
      ref="templateRef234"
      :template-config="pageConfig3"
      @actionBegin="actionBegin3"
      @handleClickToolBar="handleClickToolBar3"
      v-show="topInfo.createType == '3'"
    >
    </mt-template-page>
  </div>
</template>
<script>
import { cloneDeep } from 'lodash'
import {
  todoListColumnData,
  todoListColumnData2,
  todoListColumnData3,
  pageConfig,
  pageConfig2,
  pageConfig3,
  editSettings
} from './config/index.js'
import { utils } from '@mtech-common/utils'
export default {
  components: {
    TopInfo: () => import('./components/topInfo')
  },
  data() {
    return {
      todoListColumnData,
      todoListColumnData2,
      saveList: null,
      toolbar: ['add', 'delete'],
      editSettings,
      pageConfig: this.$route.query.receiveOrderCode
        ? pageConfig(this.$route.query.receiveOrderCode)
        : pageConfig2(),
      entryId: '', //编辑带入的订单id
      // entryType: null, //1是新增 2是编辑 3是反馈异常
      entrySource: '', // 0采购申请 1手工创建 2商城进入 4合同进入
      entryDraft: '', //1是草稿 2不是草稿 草稿可以修改
      topInfo: {
        orderCode: '',
        createType: '2',
        customerEnterpriseId: ''
        // isOutSale: "0",
      },
      showpage: true,
      receiveOrderCode: '',
      codeArr: [],
      orderDetailRequestList: [],
      id: '',
      entryType: this.$route.query.type,
      pageConfig2: [
        {
          title: this.$t('物料信息'),
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [
            [
              {
                id: 'Delete',
                icon: 'icon_solid_Delete',
                title: this.$t('删除')
              }
            ]
          ],
          gridId: this.$tableUUID.outsourcing.addPicking.list,
          grid: {
            allowPaging: false,
            editSettings: {
              allowEditing: true,
              allowAdding: false,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              // showDeleteConfirmDialog: true,
              newRowPosition: 'Top'
            },
            columnData: todoListColumnData3,
            dataSource: []
          }
        }
      ],
      pageConfig3: pageConfig3(),
      addId: '1',
      newSaveId: ''
    }
  },
  created() {
    if (this.entryType == 'readonly') {
      this.pageConfig[0].toolbar = []
      this.pageConfig[0].grid.columnData = todoListColumnData2
      this.pageConfig[0].grid.editSettings = {}
      // this.codeChange({})
      // this.pageConfig[0].editSettings.allowEditing = false;
    }
  },
  watch: {
    showpage: {
      handler(newVal) {
        if (newVal) {
          this.$nextTick(() => {
            window.outGrid = this.$refs.templateRef23
          })
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.receiveOrderCode = this.$route.query.receiveOrderCode
    if (this.receiveOrderCode) {
      this.getOnePicking()
    }
    this.save = utils.debounce(this.save, 1000)
  },
  methods: {
    updateDataSource(e) {
      this.pageConfig2[0].grid.dataSource = e
    },
    actionBegin2() {},
    actionComplete2() {},
    selectedChanged2() {},
    handleClickToolBar2(e) {
      if (e.toolbar.id === 'Delete') {
        let selectRows = e.gridRef.getMtechGridRecords()
        let list = this.$refs.templateRef2
          .getCurrentUsefulRef()
          .gridRef.ejsRef.getCurrentViewRecords()
        let newList = []
        list.forEach((item) => {
          let hasItem = selectRows.some((item1) => {
            return item.addId == item1.addId
          })
          if (!hasItem) {
            newList.push(item)
          }
        })
        this.pageConfig2[0].grid.dataSource = newList
      }
    },
    onchang(e) {
      if (e === '1') {
        this.topInfo.createType = '1'
        // this.pageConfig = pageConfig2();
      } else if (e === '3') {
        this.topInfo.createType = '3'
        // this.pageConfig = pageConfig(this.$route.query.receiveOrderCode);
      } else {
        this.topInfo.createType = '2'
        // this.pageConfig = pageConfig(this.$route.query.receiveOrderCode);
      }
    },
    getDetailList(val) {
      let list = []
      val.forEach((item) => {
        let obj = {
          buyerOrgCode: item.buyerOrgCode,
          buyerOrgName: item.buyerOrgName,
          demandDate: item.demandDate || 0,
          itemCode: item.itemCode,
          itemName: item.itemName,
          orderCode: item.orderCode,
          orderItemNo: item.orderItemNo,
          packageMinQuantity: item.packageMinQuantity,
          receiveQuantity: item.receiveQuantity || 0,
          remark: item.remark,
          stockUnit: item.stockUnit || '',
          warehouseCode: item.warehouseCode || '',
          warehouseName: item.warehouseName || ''
        }
        if (item.id) {
          obj.id = item.id
        }
        list.push(obj)
      })
      return list
    },
    async newSave(newRuleForm, type) {
      let params = {
        buyerCode: newRuleForm.customerCode,
        buyerEnterpriseId: newRuleForm.enterpriseId,
        buyerName: newRuleForm.customerName,
        createType: this.topInfo.createType,
        isOutSale: newRuleForm.isOutSale,
        itemCode: newRuleForm.itemCode,
        itemName: newRuleForm.itemName,
        orderCode: newRuleForm.orderCode,
        orderItemNo: newRuleForm.orderItemNo,
        receiveSiteCode: newRuleForm.pickingSiteCode,
        receiveSiteName: newRuleForm.pickingSiteName,
        remark: newRuleForm.remark,
        siteCode: newRuleForm.outsourcedSiteCode,
        siteName: newRuleForm.outsourcedSiteName,
        supplierCode: newRuleForm.supplierCode,
        supplierName: newRuleForm.supplierName,
        buyerOrgCode: newRuleForm.purchaseGroupCode,
        buyerOrgId: '1', //无此字段
        demandStartDate: newRuleForm.requireDate ? newRuleForm.requireDate[0].getTime() : '',
        demandEndDate: newRuleForm.requireDate ? newRuleForm.requireDate[1].getTime() : '',
        orderDetailRequestList: []
      }
      params.id = newRuleForm.id
      let orderDetailRequestList = this.$refs.templateRef2
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      params.buyerTenantId = newRuleForm.tenantId
      if (!orderDetailRequestList.length) {
        this.$toast({
          content: this.$t('请至少添加一条委外领料明细'),
          type: 'warning'
        })
        return
      }
      params.orderDetailRequestList = this.getDetailList(orderDetailRequestList)
      await this.$API.outsourcing.supplierSaveByWaitOrder(params).then((res) => {
        this.newSaveId = res.data
        this.$toast({ content: this.$t('操作成功！'), type: 'success' })
        if (type != '1') {
          this.$router.go(-1)
        }
      })
    },
    async newSubmit(newRuleForm) {
      await this.newSave(newRuleForm, '1')
      this.$API.outsourcing.supplierSubmit({ ids: this.newSaveId }).then(() => {
        this.$router.go(-1)
      })
    },
    submit(params) {
      let arr = []
      if (params?.createType === '3') {
        arr = this.$refs.templateRef234.getCurrentUsefulRef().gridRef.ejsRef.getCurrentViewRecords()
        params.siteName = params.outsourcedSiteName
        params.siteCode = params.outsourcedSiteCode
      } else {
        arr = this.$refs.templateRef23.getCurrentUsefulRef().gridRef.ejsRef.getCurrentViewRecords()
      }

      if (params.status === 2) {
        arr.forEach((ele1) => {
          ele1.orderItemNo = params.itemNo
          ele1.orderCode = params.orderCode
        })
        this.orderDetailRequestList = arr
        params.buyerTenantId = params.tenantId
        params.orderDetailRequestList = this.orderDetailRequestList
        params.id = this.id
        console.log('buyerTenantId', params)
        params.orderDetailRequestList.every((item) => {
          console.log(item)
        })

        this.$API.outsourcing[params.createType === '3' ? 'addHandPicking' : 'addPicking'](
          params
        ).then((r) => {
          this.$API.outsourcing.supplierSubmit({ ids: r.data }).then(() => {
            this.$toast({ content: this.$t('操作成功！'), type: 'success' })
            this.$router.go(-1)
          })
        })
        return
      }
      if (this.id === '') {
        arr.forEach((ele1) => {
          ele1.orderItemNo = params.itemNo
          ele1.orderCode = params.orderCode
        })
        this.orderDetailRequestList = arr
        params.buyerTenantId = params.tenantId
        params.orderDetailRequestList = this.orderDetailRequestList
        params.id = this.id
        console.log('buyerTenantId', params)
        params.orderDetailRequestList.every((item) => {
          console.log(item)
        })

        this.$API.outsourcing[params.createType === '3' ? 'addHandPicking' : 'addPicking'](
          params
        ).then((r) => {
          this.$API.outsourcing.supplierSubmit({ ids: r.data }).then(() => {
            this.$toast({ content: this.$t('操作成功！'), type: 'success' })
            this.$router.go(-1)
          })
        })
      } else {
        this.$API.outsourcing.supplierSubmit({ ids: this.id }).then(() => {
          this.$toast({ content: this.$t('操作成功！'), type: 'success' })
          this.$router.go(-1)
        })
      }
    },
    save(params) {
      let arr = []
      if (params?.createType === '3') {
        arr = this.$refs.templateRef234.getCurrentUsefulRef().gridRef.ejsRef.getCurrentViewRecords()
        params.siteName = params.outsourcedSiteName
        params.siteCode = params.outsourcedSiteCode
      } else {
        arr = this.$refs.templateRef23.getCurrentUsefulRef().gridRef.ejsRef.getCurrentViewRecords()
      }
      arr.forEach((ele1) => {
        ele1.orderItemNo = params.itemNo
        ele1.orderCode = params.orderCode
      })
      this.orderDetailRequestList = arr
      params.buyerTenantId = params.tenantId
      params.orderDetailRequestList = this.orderDetailRequestList
      params.id = this.id
      console.log('buyerTenantId', params)
      params.orderDetailRequestList.every((item) => {
        console.log(item)
      })

      this.$API.outsourcing[params.createType === '3' ? 'addHandPicking' : 'addPicking'](
        params
      ).then((r) => {
        this.id = r.data
        this.$toast({ content: this.$t('操作成功！'), type: 'success' })
        this.$router.go(-1)
      })
    },
    getOnePicking() {
      this.$API.outsourcing.getOnePicking({ receiveOrderCode: this.receiveOrderCode }).then((r) => {
        if (r.data.createType == '1' && this.entryType == 'edit') {
          this.topInfo.createType = '1'
          this.entryNewRuleForm = r.data
          let entryList = r.data.outReceiveOrderDetailResponseList || []
          entryList.forEach((item) => {
            item.addId = this.addId++
          })
          this.pageConfig2[0].grid.dataSource = entryList
          return
        }
        this.topInfo = cloneDeep(r.data)
        this.topInfo.siteId = r.data.siteId
        this.topInfo.itemNo = r.data.orderItemNo
        this.topInfo.supplierCode = r.data.supplierCode
        this.topInfo.orderCode = r.data.orderCode
        this.topInfo.isOutSale = String(r.data.isOutSale)
        this.topInfo.outsourcedType = String(r.data.outsourcedType)

        this.topInfo.remark = r.data.remark
        this.topInfo.itemCode = r.data.itemCode
        this.topInfo.itemName = r.data.itemName
        this.topInfo.customerEnterpriseId = r.data.buyerEnterpriseId
        // this.topInfo.outReceiveOrderDetailResponseList =
        //   r.data.outReceiveOrderDetailResponseList;
        // this.pageConfig[0].grid.dataSource =
        //   this.topInfo.outReceiveOrderDetailResponseList;
        // this.topInfo.siteId = r.data.siteCode;
        this.id = r.data.id
        if (r.data.createType === '3') {
          // this.topInfo.createType = "3";
          let entryList = r.data.outReceiveOrderDetailResponseList || []
          entryList.forEach((item) => {
            item.addId = this.addId++
          })
          this.headerInfo = cloneDeep(this.topInfo)
          //处理表格渲染不显示数据问题
          setTimeout(() => {
            this.pageConfig3[0].grid.dataSource = entryList
          }, 800)
        }
        if (r.data.createType == '1' || r.data.createType == '3') return
        this.codeChange(
          {
            orderCode: r.data.orderCode,
            buyerEnterpriseId: r.data.buyerEnterpriseId,
            siteCode: r.data.siteCode,
            itemCode: r.data.itemCode,
            itemName: r.data.itemName,
            lineNo: r.data.orderItemNo
          },
          'creat'
        )
      })
    },
    deleteRe() {
      // this.$dialog({
      //   data: {
      //     title: this.$t("确认"),
      //     message: this.$t(
      //       "更换公司和采购订单将重新清空/获取领料物料数据，请确定是否继续?"
      //     ),
      //   },
      // success: () => {
      let currentRecords =
        this.$refs.templateRef23?.getCurrentUsefulRef().gridRef?.ejsRef.getCurrentViewRecords() ||
        []
      if (currentRecords.length > 0) {
        let numList = []
        for (let i = 0; i < currentRecords.length; i++) {
          numList.push(i)
        }
        console.log(numList)
        this.$refs.templateRef23.getCurrentUsefulRef().gridRef.ejsRef.selectRows(numList)
        this.$refs.templateRef23.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
      }
      // },
      // });
    },
    codeChange(e) {
      this.$API.outsourcing.supplierOutReceiveOrderBomStock(e).then((r) => {
        let obj = r.data.map((item) => {
          return {
            ...item,
            stockUnit: item.unitCode,
            stockQuantity: item.wmsStockResponseList[0].stockBatchResponseList[0].maxCreateQty,
            maxReceiveQuantity: item.wmsStockResponseList[0].stockBatchResponseList[0].qty,
            maxDemandQuantity:
              item.wmsStockResponseList[0].stockBatchResponseList[0].maxDemandQuantity,
            itemName: '22',
            // itemCode:item.itemCode
            ...item.wmsStockResponseList[0],
            ...item.wmsStockResponseList[0].stockBatchResponseList[0]
          }
        })
        console.log(obj)
        this.pageConfig[0].grid.dataSource = obj
        console.log(this.pageConfig[0].grid.dataSource)
      })
      // if (type === 'creat') {
      //   // this.showpage = false;
      //   this.$API.outsourcing
      //     .orderBom(e)
      //     .then((r) => {
      //       this.codeArr = r.data

      //       if (r.data.length === 0) {
      //         this.$toast({
      //           content: '当前订单的物料不存在工单BOM和标准BOM，无法创建领料单!',
      //           type: 'warning'
      //         })
      //         return
      //       }
      //       this.pageConfig[0].grid.columnData[2].selectOptions = cloneDeep(this.codeArr)
      //       console.log(this.pageConfig[0].grid.columnData[2].selectOptions)
      //       // this.showpage = true;
      //     })
      //     .catch(() => {
      //       this.codeArr = []
      //     })
      // } else {
      //   // this.showpage = false;
      //   // this.pageConfig = pageConfig2();
      //   this.$API.outsourcing
      //     .orderBom(e)
      //     .then((r) => {
      //       this.codeArr = r.data
      //       if (r.data.length === 0) {
      //         this.$toast({
      //           content: '当前订单的物料不存在工单BOM和标准BOM，无法创建领料单!',
      //           type: 'warning'
      //         })
      //         return
      //       }
      //       this.pageConfig[0].grid.columnData[2].selectOptions = cloneDeep(this.codeArr)
      //       // this.showpage = true;
      //     })
      //     .catch(() => {
      //       this.codeArr = []
      //     })
      // }
    },
    selectedChanged(e) {
      this.saveList = e
      console.log(this.saveList)
    },

    handleClickToolBar(e) {
      if (e.toolbar.id === 'Add') {
        this.$refs.templateRef23.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
        // if (this.codeArr && this.codeArr.length > 0) {
        //   this.$refs.templateRef23.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
        // } else {
        //   // this.$toast({
        //   //   content: "当前订单或公司无物料数据，请选择其他订单或公司!",
        //   //   type: "warning",
        //   // });
        //   // return;
        // }
      } else if (e.toolbar.id === 'Delete') {
        console.log(this.$refs.templateRef23.getCurrentUsefulRef().ejsRef)
        console.log(
          this.$refs.templateRef23.getCurrentUsefulRef().gridRef.ejsRef.getCurrentViewRecords()
        )
        console.log(this.pageConfig[0].grid.dataSource)
        this.$refs.templateRef23.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
      }
    },
    handleClickToolBar3(e) {
      if (e.toolbar.id === 'Add') {
        this.$refs.topInfo.$refs.ruleForm1.validate((valid) => {
          if (!valid) {
            this.$toast({
              content: this.$t('头部带星号的为必填项'),
              type: 'warning'
            })
          } else {
            this.$refs.templateRef234.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
          }
        })
      } else if (e.toolbar.id === 'Delete') {
        this.$refs.templateRef234.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
      }
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
    },
    handleClickCellTitle(e) {
      console.log('handleClickCellTitle', e)
    },
    actionBegin(args) {
      if (args.requestType === 'save') {
        if (this.saveList !== null) {
          console.log(this.saveList)
          args.data.warehouseCode = this.saveList.itemInfo.warehouseCode
          console.log(args.data)
        }
      }
      if (args.requestType == 'add' || args.requestType == 'beginEdit') {
        args.data.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
        if (Number(args.data.receiveQuantity) > Number(args.data.maxDemandQuantity)) {
          this.$toast({
            content: '本次领料数量不可超过最大需求可创建数量!',
            type: 'warning'
          })
          args.data.receiveQuantity = args.data.maxDemandQuantity
        }
        this.isEditStatus = true
      }
      if (args.requestType == 'refresh') {
        this.isEditStatus = false
      }
      // let arr = this.$refs.templateRef23
      //   .getCurrentUsefulRef()
      //   .gridRef.ejsRef.getCurrentViewRecords();
      // console.log(arr);
    },
    actionBegin3(args) {
      if (args.requestType === 'save') {
        if (this.saveList !== null) {
          console.log(this.saveList)
          args.data.warehouseCode = this.saveList.itemInfo.warehouseCode
          console.log(args.data)
        }
      }
      if (args.requestType == 'add' || args.requestType == 'beginEdit') {
        args.data.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
        if (Number(args.data.receiveQuantity) > Number(args.data.maxDemandQuantity)) {
          this.$toast({
            content: '本次领料数量不可超过最大需求可创建数量!',
            type: 'warning'
          })
          args.data.receiveQuantity = args.data.maxDemandQuantity
        }
        this.isEditStatus = true
      }
      if (args.requestType == 'refresh') {
        this.isEditStatus = false
      }
      // let arr = this.$refs.templateRef23
      //   .getCurrentUsefulRef()
      //   .gridRef.ejsRef.getCurrentViewRecords();
      // console.log(arr);
    },
    actionComplete() {
      //   // const { requestType, action, rowIndex, index } = args;
      //   console.log(args, "我是actionComplete");
    },
    addRow(row) {
      console.log('addRow', row)
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('entryData')
  }
}
</script>
<style lang="scss" scoped>
.repeat-template {
  .common-template-page {
    /deep/ .mt-tabs {
      display: none;
    }
  }
}
.e-content {
  height: 200px;
}

.mt-tabs {
  width: 100%;
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
// /deep/ .top-info {
// margin-top: 20px;
// }
// .bottom-tables {
//   height: 100%;
// }
// .mt-tabs {
//   width: 100%;
//   /deep/.mt-tabs-container {
//     width: 100%;
//   }
// }
// .addPicking {
//   background: #fff;
//   display: flex;
//   flex-direction: column;
//   .template-height {
//     height: auto;
//     flex: 1;
//     max-height: calc(100% - 270px);
//     /deep/ .e-gridcontent {
//       overflow-y: auto;
//       height: calc(100% - 44px);
//     }
//     /deep/ .e-gridcontent {
//       // height: 100%;
//       overflow-y: auto;
//       height: calc(100% - 44px);
//     }
//   }
// }
</style>
