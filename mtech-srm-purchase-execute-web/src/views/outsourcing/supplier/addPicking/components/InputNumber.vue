<template>
  <div class="selfType">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      style="display: none"
      on-keypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
    ></mt-input>
    <!-- <div>{{ showInfo.d }}</div> -->
    <mt-inputNumber
      v-if="show"
      v-model="num"
      precision="3"
      :min="0"
      :step="1"
      :show-clear-button="false"
      @input="inputChange"
    ></mt-inputNumber>
  </div>
</template>
<script>
export default {
  data() {
    return {
      data: {},
      showInfo: {
        d: '',
        c: '',
        g: '',
        u: ''
      },
      num: 0,
      // max: 0,
      show: true,
      maxType: true
    }
  },
  created() {
    this.num = this.data[this.data.column.field]

    this.$bus.$on('batchChange2', (val) => {
      console.log('物料描述啊啊啊2', val)
      // this.max = val.maxDemandQuantity;
      this.maxType = false
    }) //接受的物料描述
  },
  methods: {
    inputChange() {
      this.data[this.data.column.field] = Number(this.num)
    }
  }
}
</script>
<style lang="scss" scoped>
.selfType {
  > div {
    height: 36px;
    line-height: 36px;
    box-sizing: border-box;
    // padding: 12px 0;
  }
}
</style>
