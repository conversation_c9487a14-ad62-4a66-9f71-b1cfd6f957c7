<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="itemcon mr20" v-if="!dis">
        <span class="infos" style="margin-right: 10px">{{ $t('创建方式：') }}</span>
        <mt-radio v-model="topInfo.createType" :data-source="radioData" @input="onchang"></mt-radio>
      </div>
      <!-- <div v-else> -->
      <div v-if="dis" class="status mr20">
        {{ typeInfo(topInfo.status) }}
      </div>
      <div class="infos mr20" v-if="dis">{{ $t('创建人') }}：{{ topInfo.createUserName }}</div>
      <div class="infos" v-if="dis">{{ $t('创建日期：') }}{{ topInfo.createTime }}</div>
      <div class="infos mr20" v-if="dis">{{ $t('单据编号：') }}{{ topInfo.receiveOrderCode }}</div>
      <div class="infos mr20" v-if="topInfo.buyerApproveUserName && dis">
        {{ $t('采方确认人') }}：{{ topInfo.buyerApproveUserName }}
      </div>
      <div class="infos mr20" v-if="topInfo.buyerApproveUserName && dis">
        {{ $t('采方确认时间') }}：{{ dateFormat(topInfo.buyerApproveDate) }}
      </div>
      <div class="infos mr20" v-if="topInfo.materialApproveUserName && dis">
        {{ $t('供方确认人') }}：{{ topInfo.materialApproveUserName }}
      </div>
      <div class="infos mr20" v-if="topInfo.materialApproveUserName && dis">
        {{ $t('供方确认时间') }}：{{ dateFormat(topInfo.materialApproveDate) }}
      </div>
      <!-- </div> -->
      <div class="middle-blank"></div>
      <!-- 右侧各种操作按钮 -->
      <mt-button css-class="e-flat invite-btn" :is-primary="true" @click="goBack">{{
        $t('返回')
      }}</mt-button>
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        @click="getItemInfo"
        v-if="!dis && topInfo.createType === '1'"
        >{{ $t('获取物料信息') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        @click="newSave"
        v-if="!dis && topInfo.createType === '1'"
        >{{ $t('保存') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        @click="newSubmit"
        v-if="!dis && topInfo.createType === '1'"
        >{{ $t('提交') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        @click="save"
        v-if="!dis && topInfo.createType === '2'"
        >{{ $t('保存') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        @click="submit"
        v-if="!dis && topInfo.createType === '2'"
        >{{ $t('提交') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        @click="onSave"
        v-if="!dis && topInfo.createType === '3'"
        >{{ $t('保存') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        @click="onSubmit"
        v-if="!dis && topInfo.createType === '3'"
        >{{ $t('提交') }}</mt-button
      >
      <div class="sort-box" @click="expandChange">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>
    <!-- 订单BOM -->
    <div
      class="main-bottom"
      v-show="(topInfo.createType === '2' || entryType == 'readonly') && topInfo.createType !== '3'"
    >
      <mt-form ref="ruleForm" :model="topInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item :label="$t('公司')">
          <mt-select
            ref="customerEnterpriseId"
            v-model="topInfo.customerEnterpriseId"
            :open-dispatch-change="false"
            :allow-filtering="true"
            :disabled="dis"
            :filtering="getSourceCompany1"
            :fields="{ text: 'name', value: 'code' }"
            :data-source="dataArr3"
            @change="companyChange"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('委外工厂')">
          <mt-input disabled type="text" v-model="site"></mt-input>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('领料供应商编号')">
          <mt-input disabled type="text" v-model="topInfo.supplierCode"></mt-input>
        </mt-form-item>

        <mt-form-item prop="orderCode" :label="$t('采购订单号')">
          <mt-input v-if="dis" disabled type="text" v-model="topInfo.orderCode"></mt-input>
          <debounce-filter-select
            v-else
            v-model="topInfo.orderCode"
            :disabled="dis"
            :data-source="dataArr2"
            :open-dispatch-change="false"
            :fields="{ text: 'name', value: 'code' }"
            :placeholder="$t('请选择')"
            @change="codeChange"
            :allow-filtering="true"
          ></debounce-filter-select>
        </mt-form-item>
        <mt-form-item prop="orderCode" :label="$t('采购订单行号')">
          <mt-input disabled type="text" v-model="topInfo.itemNo"></mt-input>
        </mt-form-item>
        <mt-form-item prop="orderCode" :label="$t('物料编码')">
          <mt-input v-if="dis" disabled type="text" v-model="topInfo.itemCode"></mt-input>
          <mt-select
            v-else
            ref="siteId"
            :disabled="dis"
            :open-dispatch-change="false"
            v-model="topInfo.itemNo"
            :fields="{ text: 'itemCode', value: 'itemNo' }"
            :data-source="orderList"
            @change="orderClick"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="orderCode" :label="$t('物料名称')">
          <mt-input disabled type="text" v-model="topInfo.itemName"></mt-input>
        </mt-form-item>
        <mt-form-item prop="receiveSiteCode" :label="$t('领料工厂')">
          <mt-input v-if="dis" disabled type="text" v-model="receiveSite"></mt-input>
          <debounce-filter-select
            v-else
            v-model="topInfo.receiveSiteCode"
            :disabled="dis"
            :request="getSourceAvailable"
            :value-template="siteCodeValueTemplate"
            :is-active="true"
            :data-source="siteOptions"
            :fields="{ text: 'theCodeName', value: 'siteCode' }"
            @change="siteOrgClick"
            :allow-filtering="true"
          ></debounce-filter-select>
        </mt-form-item>
        <mt-form-item prop="outsourcedType" :label="$t('委外方式')">
          <mt-radio
            v-model="topInfo.outsourcedType"
            :data-source="radioData1"
            :disabled="true"
          ></mt-radio>
        </mt-form-item>
        <mt-form-item
          v-if="topInfo.buyerApproveReaseon !== null && topInfo.buyerApproveReaseon !== undefined"
          prop="businessTypeId"
          :label="$t('采方退回原因')"
        >
          <mt-input
            v-model="topInfo.buyerApproveReaseon"
            :disabled="true"
            :placeholder="$t('采方退回原因')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input v-model="topInfo.remark" :disabled="dis"></mt-input>
        </mt-form-item>
        <!-- <mt-form-item
          prop="remark"
          :label="$t('退回原因')"
          class="full-width"
          v-show="topInfo.buyerApproveReaseon"
        >
          <mt-input disabled v-model="topInfo.buyerApproveReaseon"></mt-input>
        </mt-form-item> -->
      </mt-form>
    </div>
    <!-- 待领料明细创建 -->
    <div class="main-bottom" v-show="topInfo.createType === '1' && entryType == 'edit'">
      <mt-form
        ref="newRuleForm"
        :model="newRuleForm"
        :rules="newRuleFormRules"
        :validate-on-rule-change="false"
      >
        <mt-form-item :label="$t('客户公司')" prop="customerCode">
          <mt-select
            v-model="newRuleForm.customerCode"
            :open-dispatch-change="false"
            :allow-filtering="true"
            :filtering="getSourceCompany1"
            :fields="{ text: 'label', value: 'value' }"
            :data-source="customerCodeOptions"
            @change="companyChange2"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('需求日期')">
          <mt-date-range-picker
            :width="300"
            :placeholder="$t('选择开始时间和结束时间')"
            v-model="newRuleForm.requireDate"
          ></mt-date-range-picker>
        </mt-form-item>
        <mt-form-item :label="$t('委外工厂')" prop="outsourcedSiteCode">
          <mt-select
            :show-clear-button="false"
            v-model="newRuleForm.outsourcedSiteCode"
            :open-dispatch-change="false"
            :allow-filtering="true"
            :filtering="getOutsourcedSiteCodeOptions"
            :data-source="outsourcedSiteCodeOptions"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="supplierCodeName" :label="$t('领料供应商')">
          <mt-input disabled type="text" v-model="newRuleForm.supplierCodeName"></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('采购组')">
          <mt-select
            :show-clear-button="true"
            v-model="newRuleForm.purchaseGroupCode"
            :open-dispatch-change="false"
            :allow-filtering="true"
            :filtering="getPurchaseGroupCodeOptions"
            :data-source="purchaseGroupCodeOptions"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('采购订单号')">
          <mt-select
            :show-clear-button="true"
            v-model="newRuleForm.orderCode"
            :open-dispatch-change="false"
            :allow-filtering="true"
            :filtering="getOrderCodeOptions"
            :fields="{ text: 'label', value: 'value' }"
            :data-source="orderCodeOptions"
            :placeholder="$t('请选择')"
            @change="orderCodeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('物料')">
          <mt-select
            :show-clear-button="true"
            v-model="newRuleForm.itemCode"
            :open-dispatch-change="false"
            :allow-filtering="true"
            :filtering="getItemCodeOptions"
            :data-source="itemCodeOptions"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择')"
            @change="itemCodeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('发料库存地点')">
          <mt-select
            :show-clear-button="true"
            v-model="newRuleForm.warehouseCode"
            :open-dispatch-change="false"
            :allow-filtering="true"
            :filtering="getWarehouseCodeOptions"
            :data-source="warehouseCodeOptions"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('领料工厂')" prop="pickingSiteCode">
          <mt-select
            :show-clear-button="false"
            v-model="newRuleForm.pickingSiteCode"
            :open-dispatch-change="false"
            :allow-filtering="true"
            :filtering="getPickingSiteCodeOptions"
            :data-source="pickingSiteCodeOptions"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="outsourcedType" :label="$t('委外方式')">
          <mt-radio v-model="newRuleForm.outsourcedType" :data-source="radioData1"></mt-radio>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input v-model="newRuleForm.remark"></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
    <!-- 手工创建 -->
    <div class="main-bottom" v-show="topInfo.createType === '3'">
      <mt-form
        ref="ruleForm1"
        :model="headInfo"
        :rules="handRules"
        :validate-on-rule-change="false"
      >
        <mt-form-item :label="$t('公司')">
          <mt-select
            ref="customerEnterpriseId"
            v-model="headInfo.customerEnterpriseId"
            :open-dispatch-change="false"
            :allow-filtering="true"
            :disabled="dis"
            :filtering="getSourceCompany1"
            :fields="{ text: 'name', value: 'code' }"
            :data-source="dataArr3"
            @change="companyChange3"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <!-- <mt-form-item :label="$t('委外工厂')" prop="outsourcedSiteCode">
          <mt-select
            :show-clear-button="false"
            v-model="headInfo.outsourcedSiteCode"
            :open-dispatch-change="false"
            :allow-filtering="true"
            :filtering="getOutsourcedSiteCodeOptions"
            :data-source="outsourcedSiteCodeOptions"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item> -->
        <mt-form-item prop="outsourcedSiteCode" :label="$t('委外工厂')">
          <mt-input v-if="dis" disabled type="text" v-model="outsourcedSite"></mt-input>
          <debounce-filter-select
            v-else
            v-model="headInfo.outsourcedSiteCode"
            :disabled="dis"
            :request="getSourceAvailable4"
            :value-template="siteCodeValueTemplate"
            :is-active="true"
            :data-source="siteOptions1"
            :fields="{ text: 'theCodeName', value: 'siteCode' }"
            @change="outsourcedSiteCodeChange"
          ></debounce-filter-select>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('领料供应商编号')">
          <mt-input disabled type="text" v-model="headInfo.supplierCode"></mt-input>
        </mt-form-item>
        <mt-form-item prop="receiveSiteCode" :label="$t('领料工厂')">
          <mt-input v-if="dis" disabled type="text" v-model="receiveSite"></mt-input>
          <debounce-filter-select
            v-else
            v-model="headInfo.receiveSiteCode"
            :disabled="dis"
            :request="getSourceAvailable3"
            :value-template="siteCodeValueTemplate"
            :is-active="true"
            :data-source="siteOptions"
            :fields="{ text: 'theCodeName', value: 'siteCode' }"
            @change="siteOrgClick3"
            :allow-filtering="true"
          ></debounce-filter-select>
        </mt-form-item>
        <mt-form-item prop="outsourcedType" :label="$t('委外方式')">
          <mt-radio
            :disabled="dis"
            v-model="headInfo.outsourcedType"
            :data-source="radioData2"
            @change="onselectOutsource"
          ></mt-radio>
        </mt-form-item>
        <mt-form-item
          v-if="headInfo.buyerApproveReaseon !== null && headInfo.buyerApproveReaseon !== undefined"
          prop="businessTypeId"
          :label="$t('采方退回原因')"
        >
          <mt-input
            v-model="headInfo.buyerApproveReaseon"
            :disabled="true"
            :placeholder="$t('采方退回原因')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input v-model="headInfo.remark" :disabled="dis"></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
// import UTILS from "@/utils/utils";
import { Query } from '@syncfusion/ej2-data'
import { utils } from '@mtech-common/utils'
import { cloneDeep, isEqual } from 'lodash'

import Vue from 'vue'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { addCodeNameKeyInList } from '@/utils/utils'
const siteCodeSelectTemplate = () => {
  return {
    template: Vue.component('siteCodeSelectTemplate', {
      template: `
    <div>
      <div>{{data.siteCode}}-{{data.siteName}}</div>
    </div>`,
      data() {
        return { data: {} }
      }
    })
  }
}
const buyCodeSelectTemplate = () => {
  return {
    template: Vue.component('buyCodeSelectTemplate', {
      template: `
        <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <span>{{data.customerCode}}+{{data.customerName}}</span>
            </div>
          </div>`,
      data() {
        return { data: {} }
      }
    })
  }
}
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  props: {
    entryType: {
      type: String,
      default: 'edit'
    },
    topInfoE: {
      type: Object,
      default: () => {
        return {
          // createType: "1",
          // isOutSale: 1,
        }
      }
    },
    entryNewRuleForm: {
      type: Object,
      default: () => {}
    },
    costSwitch: {
      type: String,
      default: '1'
    },
    entrySource: {
      type: String,
      default: '1'
    },
    acceptanceUse: {
      type: String,
      default: '0'
    },
    entryDraft: {
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      addId: '1',
      useingNewRuleForm: {},
      pickingSiteCodeOptions: [], //领料工厂下拉
      warehouseCodeOptions: [], //发料库存地点下拉
      itemCodeOptions: [], //物料下拉
      orderCodeOptions: [], //订单号下拉
      purchaseGroupCodeOptions: [], //采购组下拉
      outsourcedSiteCodeOptions: [], //委外工厂下拉数据
      customerCodeOptions: [], //客户公司下拉
      headInfo: {
        customerEnterpriseId: '',
        outsourcedSiteCode: '',
        outsourcedSiteName: '', // 委外工厂名称
        supplierCode: '',
        supplierCodeName: '', // 领料供应商
        receiveSiteCode: '',
        outsourcedType: '',
        remark: ''
      },
      newRuleForm: {
        customerCode: '', //客户公司
        customerName: '', //客户公司名称
        enterpriseId: '', // 企业id
        requireDate: '', // 需求日期
        outsourcedSiteCode: '', // 委外工厂
        outsourcedSiteName: '', // 委外工厂名称
        supplierCodeName: '', // 领料供应商
        purchaseGroupCode: '', //采购组
        purchaseGroupName: '', //采购组名称
        orderCode: '', // 采购订单号
        orderItemNo: '', //采购订单行号
        itemCode: '', // 物料
        itemName: '', //物料名称
        warehouseCode: '', // 发料库存地点
        pickingSiteCode: '', // 领料工厂
        pickingSiteName: '', //领料工厂名称
        outsourcedType: '0', //是否销售委外
        remark: '', // 备注
        supplierCode: '',
        supplierName: '',
        id: ''
      },
      newRuleFormRules: {
        customerCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        outsourcedSiteCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        supplierCodeName: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        pickingSiteCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      handRules: {
        customerEnterpriseId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        outsourcedSiteCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        receiveSiteCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],

        outsourcedType: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      receiveSite: '',
      outsourcedSite: '',
      siteCodeValueTemplate: siteCodeSelectTemplate, // 工厂
      orderTypeOptions: [],

      buyerItem: buyCodeSelectTemplate,
      radioData: [
        {
          label: this.$t('待领料明细'),
          value: '1'
          // disabled: true,
        },
        {
          label: this.$t('订单BOM'),
          value: '2'
        },
        {
          label: this.$t('手工创建'),
          value: '3'
        }
      ],
      orderList: [],
      addForm: {},
      radioData1: [
        {
          label: this.$t('销售委外'),
          value: '1'
        },
        {
          label: this.$t('工序委外'),
          value: '3'
        }
      ],
      radioData2: [
        {
          label: this.$t('标准委外'),
          value: '0'
        }
      ],
      isExpand: true,
      rules: {
        siteId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        receiveSiteCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        orderCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        outsourcedType: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        customerEnterpriseId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      site: '',
      expedited: '1', //待领料明细or手工创建
      siteOptions: [],
      siteOptions1: [],
      dataArr2: [],
      dataArr3: [],
      userInfo: JSON.parse(sessionStorage.getItem('userInfo')),
      topInfo: cloneDeep(this.topInfoE),
      buyerEnterpriseId: '',
      type: '',
      entryFirst: '1'
    }
  },
  computed: {
    dis() {
      return this.entryType === 'readonly'
    }
  },
  watch: {
    topInfoE: {
      handler() {
        this.topInfo = cloneDeep(this.topInfoE)
        if (this.topInfo.customerEnterpriseId) {
          this.topInfo.outsourcedType =
            this.topInfo?.outsourcedType || this.topInfo?.outsourcedType === 0
              ? String(this.topInfo?.outsourcedType)
              : ''
          if (
            (this.topInfo.createType == '2' || this.entryType == 'readonly') &&
            this.topInfo.createType !== '3'
          ) {
            this.type = 'edit'
            this.buyerEnterpriseId = this.topInfo.customerEnterpriseId
            this.site = this.topInfo.siteCode + this.topInfo.siteName
            this.getSourceAvailable({})
            this.receiveSite = this.topInfo.receiveSiteCode + this.topInfo.receiveSiteName

            this.purOrderQueryOrder()
            // this.findInBuyingByCustomerCode();
            // sessionStorage.setItem("entryData", JSON.stringify(this.topInfo));
            this.codeChange({ value: this.topInfo.orderCode, e: null })
            // this.$API.purchaseOrder
            //   .purOrderGetByOrder({ code: this.topInfo.orderCode })
            //   .then((res) => {
            //     this.orderList = res.data;
            //   });
          }
          if (this.topInfo.createType === '3') {
            this.topInfo.outsourcedSiteCode = this.topInfo.siteCode
            this.topInfo.outsourcedSiteName = this.topInfo.siteName
            this.headInfo = cloneDeep(this.topInfo)
            this.receiveSite = this.topInfo.receiveSiteCode + this.topInfo.receiveSiteName
            this.outsourcedSite = this.topInfo.siteCode + this.topInfo.siteName

            this.getSourceAvailable3({})
            this.getSourceAvailable4({})
          }
        }
      },
      immediate: true
    },
    entryNewRuleForm: {
      handler(val) {
        if (this.entryFirst != '1') return
        if (this.entryType != 'edit') return
        if (val.createType == '1') {
          this.topInfo.createType = '1'
          this.entryFirst = '2'
          this.newRuleForm.customerCode = val.buyerCode
          this.newRuleForm.customerName = val.buyerName
          this.newRuleForm.enterpriseId = val.buyerEnterpriseId // 企业id
          if (val.demandStartDate && val.demandEndDate) {
            this.newRuleForm.requireDate = [
              new Date(Number(val.demandStartDate)),
              new Date(Number(val.demandEndDate))
            ]
          }
          this.newRuleForm.outsourcedSiteCode = val.siteCode
          this.newRuleForm.outsourcedSiteName = val.siteName
          this.getOutsourcedSiteCodeOptions({ text: val.siteCode }) //获取工厂
          this.newRuleForm.supplierCode = val.supplierCode
          this.newRuleForm.supplierName = val.supplierName
          this.newRuleForm.supplierCodeName = `${val.supplierCode}-${val.supplierName}` // 领料供应商
          this.newRuleForm.purchaseGroupCode = val.buyerOrgCode //采购组
          this.getPurchaseGroupCodeOptions({ text: val.buyerOrgCode })
          this.newRuleForm.orderCode = val.orderCode //采购订单号
          this.getOrderCodeOptions({ text: val.orderCode })
          this.newRuleForm.orderItemNo = val.orderItemNo //采购订单行号
          this.newRuleForm.itemCode = val.itemCode //物料
          this.newRuleForm.itemName = val.itemName //物料名称
          this.getItemCodeOptions({ text: val.itemCode })
          this.newRuleForm.pickingSiteCode = val.receiveSiteCode //领料工厂
          this.newRuleForm.pickingSiteName = val.receiveSiteName //领料工厂名称
          this.getPickingSiteCodeOptions({ text: val.receiveSiteCode })
          this.newRuleForm.outsourcedType = String(val.outsourcedType)
          this.newRuleForm.remark = val.remark
          this.newRuleForm.id = val.id
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.fuzzyQuery('')
  },
  mounted() {
    this.topInfo.supplierName = this.userInfo.enterpriseName
    this.topInfo.createUserName = this.userInfo.username
    this.topInfo.siteId = this.userInfo.siteId
    this.purOrderQueryOrder = utils.debounce(this.purOrderQueryOrder, 1000)
    this.getPickingSiteCodeOptions = utils.debounce(this.getPickingSiteCodeOptions, 1000)
    this.getOutsourcedSiteCodeOptions = utils.debounce(this.getOutsourcedSiteCodeOptions, 1000)
  },
  methods: {
    itemCodeChange(val) {
      this.newRuleForm.itemName = val.itemData.itemName
    },
    orderCodeChange(val) {
      this.newRuleForm.orderItemNo = val.itemData.orderItemNo
    },
    onselectOutsource() {
      const _info = { ...this.topInfo, ...this.headInfo }
      sessionStorage.setItem('pickTopInfo', JSON.stringify(_info))
    },
    outsourcedSiteCodeChange(val) {
      const _info = { ...this.topInfo, ...this.headInfo }
      this.headInfo.outsourcedSiteName = val?.itemData.siteName
      sessionStorage.setItem('pickTopInfo', JSON.stringify(_info))
    },
    getPickingSiteCodeOptions(val) {
      //领料工厂
      let params = {
        customerCode: this.newRuleForm.customerCode,
        enterpriseId: this.newRuleForm.enterpriseId,
        fuzzyParam: val?.text || '',
        dataLimit: 10000
      }
      this.$API.masterData.siteQuery(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.siteName}-${item.siteCode}`
          item.value = item.siteCode
        })
        this.pickingSiteCodeOptions = list
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.pickingSiteCodeOptions)
          })
        }
      })
    },
    getWarehouseCodeOptions() {
      //发料库存地点
    },
    getItemCodeOptions(val) {
      //获取物料信息
      let params = {
        enterpriseId: this.newRuleForm.enterpriseId,
        queryField: 4,
        queryValue: val?.text || ''
      }
      this.$API.outsourcing.supplierCollectQuery(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.itemName}-${item.itemCode}`
          item.value = item.itemCode
        })
        this.itemCodeOptions = list
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.itemCodeOptions)
          })
        }
      })
    },
    getOrderCodeOptions(val) {
      //获取采购订单号
      let params = {
        enterpriseId: this.newRuleForm.enterpriseId,
        queryField: 3,
        queryValue: val?.text || ''
      }
      this.$API.outsourcing.supplierCollectQuery(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.orderCode}`
          item.value = item.orderCode
        })
        this.orderCodeOptions = list
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.orderCodeOptions)
          })
        }
      })
    },
    getPurchaseGroupCodeOptions(val) {
      //获取采购组
      let params = {
        enterpriseId: this.newRuleForm.enterpriseId,
        queryField: 2,
        queryValue: val?.text || ''
      }
      this.$API.outsourcing.supplierCollectQuery(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.purchaseGroupCode}-${item.purchaseGroupName}`
          item.value = item.purchaseGroupCode
        })
        this.purchaseGroupCodeOptions = list
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.purchaseGroupCodeOptions)
          })
        }
      })
    },
    getOutsourcedSiteCodeOptions(val) {
      //获取委外工厂
      let params = {
        enterpriseId: this.newRuleForm.enterpriseId,
        queryField: 1,
        queryValue: val?.text || ''
      }
      this.$API.outsourcing.supplierCollectQuery(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.siteName}-${item.siteCode}`
          item.value = item.siteCode
        })
        this.outsourcedSiteCodeOptions = list
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.outsourcedSiteCodeOptions)
          })
        }
      })
    },
    companyChange2(e) {
      //公司改变
      this.newRuleForm.customerName = e.itemData?.customerName
      this.newRuleForm.customerCode = e.itemData?.customerCode
      this.newRuleForm.enterpriseId = e.itemData?.customerEnterpriseId
      this.findInBuyingByCustomerCode2(e.itemData?.customerCode)
      this.getOutsourcedSiteCodeOptions() //委外工厂
      this.getPickingSiteCodeOptions() //领料工厂
      this.getItemCodeOptions() //物料
      this.getOrderCodeOptions() //采购订单号
      this.getPurchaseGroupCodeOptions() //采购组
    },
    findInBuyingByCustomerCode2(customerCode) {
      //获取供应商信息
      this.$API.masterData
        .findInBuyingByCustomerCode({ customerCode: customerCode })
        .then((res) => {
          this.newRuleForm.supplierCodeName = `${res.data.supplierCode}-${res.data.supplierName}`
          this.newRuleForm.supplierCode = res.data.supplierCode
          this.newRuleForm.supplierName = res.data.supplierName
        })
    },
    getItemInfo() {
      //获取物料信息
      if (!this.newRuleForm.customerCode) {
        this.$toast({
          content: '请选择客户公司!',
          type: 'warning'
        })
        return
      }
      if (!this.newRuleForm.outsourcedSiteCode) {
        this.$toast({
          content: '请选择委外工厂!',
          type: 'warning'
        })
        return
      }
      if (!this.newRuleForm.pickingSiteCode) {
        this.$toast({
          content: '请选择领料工厂!',
          type: 'warning'
        })
        return
      }
      this.useingNewRuleForm = cloneDeep(this.newRuleForm)
      let params = {
        enterpriseId: this.newRuleForm.enterpriseId,
        queryBuilderDTO: {
          defaultRules: [
            {
              field: 'siteCode',
              operator: 'equal',
              value: this.newRuleForm.outsourcedSiteCode
            },
            {
              field: 'supplierCode',
              operator: 'equal',
              value: this.newRuleForm.supplierCode
            }
          ],
          page: {
            current: 1,
            size: 10000000
          }
        }
      }
      if (this.newRuleForm.orderCode) {
        params.queryBuilderDTO.defaultRules.push({
          field: 'orderCode',
          operator: 'equal',
          value: this.newRuleForm.orderCode
        })
      }
      if (this.newRuleForm.itemCode) {
        params.queryBuilderDTO.defaultRules.push({
          field: 'itemCode',
          operator: 'equal',
          value: this.newRuleForm.itemCode
        })
      }
      if (this.newRuleForm.purchaseGroupCode) {
        params.queryBuilderDTO.defaultRules.push({
          field: 'purchaseGroupCode',
          operator: 'equal',
          value: this.newRuleForm.purchaseGroupCode
        })
      }
      if (this.newRuleForm.requireDate) {
        params.queryBuilderDTO.defaultRules.push({
          field: 'demandDate',
          operator: 'greaterthanorequal',
          value: this.newRuleForm.requireDate[0].getTime()
        })
        params.queryBuilderDTO.defaultRules.push({
          field: 'demandDate',
          operator: 'lessthanorequal',
          value: this.newRuleForm.requireDate[1].getTime()
        })
      }
      this.$API.outsourcing.outWaitReceiveOrderQuery(params).then((res) => {
        let list = res.data.records
        list.forEach((item) => {
          item.receiveQuantity = item.unReceiveNum
          item.maxDemandQuantity = item.unReceiveNum
          // item.maxReceiveQuantity = item.maxReceiveQuantity;
          item.buyerOrgCode = item.purchaseGroupCode
          item.buyerOrgName = item.purchaseGroupName
          item.packageMinQuantity = item.minPackNum
          item.remark = ''
          item.addId = this.addId++
          delete item.id
        })
        this.$emit('updateDataSource', list)
      })
    },
    newSave() {
      //待领料明细保存
      this.$refs.newRuleForm.validate((valid) => {
        if (valid) {
          let flag = isEqual(this.useingNewRuleForm, this.newRuleForm)
          if (!flag) {
            this.$toast({
              content: this.$t('请重新获取物料信息'),
              type: 'warning'
            })
            return
          }
          this.newRuleForm.outsourcedSiteName = this.outsourcedSiteCodeOptions.find((item) => {
            return item.siteCode == this.newRuleForm.outsourcedSiteCode
          })?.siteName
          this.newRuleForm.pickingSiteName = this.pickingSiteCodeOptions.find((item) => {
            return item.siteCode == this.newRuleForm.pickingSiteCode
          })?.siteName
          this.newRuleForm.purchaseGroupName = this.purchaseGroupCodeOptions.find((item) => {
            return item.purchaseGroupCode == this.newRuleForm.purchaseGroupCode
          })?.purchaseGroupName
          this.$emit('newSave', this.newRuleForm)
        }
      })
    },
    newSubmit() {
      //待领料明细提交
      this.$refs.newRuleForm.validate((valid) => {
        if (valid) {
          let flag = isEqual(this.useingNewRuleForm, this.newRuleForm)
          if (!flag) {
            this.$toast({
              content: this.$t('请重新获取物料信息'),
              type: 'warning'
            })
            return
          }
          this.newRuleForm.outsourcedSiteName = this.outsourcedSiteCodeOptions.find((item) => {
            return item.siteCode == this.newRuleForm.outsourcedSiteCode
          })?.siteName
          this.newRuleForm.pickingSiteName = this.pickingSiteCodeOptions.find((item) => {
            return item.siteCode == this.newRuleForm.pickingSiteCode
          })?.siteName
          this.newRuleForm.purchaseGroupName = this.purchaseGroupCodeOptions.find((item) => {
            return item.purchaseGroupCode == this.newRuleForm.purchaseGroupCode
          })?.purchaseGroupName
          this.$emit('newSubmit', this.newRuleForm)
        }
      })
    },
    getSourceCompany1(e) {
      var searchData = this.dataArr3
      // this.purOrderQueryOrder(e.text);

      // load overall data when search key empty.
      if (e.text == '') e.updateData(searchData)
      else {
        let query = new Query().select(['name', 'code', 'customerCode', 'customerName'])
        // change the type of filtering
        query = e.text !== '' ? query.where('name', 'contains', e.text, true) : query
        e.updateData(searchData, query)
      }
    },
    siteOrgClick(e) {
      this.topInfo.buyerOrgId = e.itemData.organizationId
      this.topInfo.receiveSiteCode = e.itemData.siteCode
      this.topInfo.receiveSiteName = e.itemData.siteName

      const _info = { ...this.headInfo, ...this.topInfo }
      sessionStorage.setItem('pickTopInfo', JSON.stringify(_info))
      this.$bus.$emit('receiveSiteChange')
    },
    siteOrgClick3(e) {
      this.headInfo.buyerOrgId = e.itemData.organizationId
      this.headInfo.receiveSiteCode = e.itemData.siteCode
      this.headInfo.receiveSiteName = e.itemData.siteName
    },
    dateFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }
      return str
    },
    orderClick(e) {
      if (e.e !== null) {
        this.topInfo = {
          ...this.topInfo,
          receiveSiteCode: e.itemData.siteCode,
          receiveSiteName: e.itemData.siteName
        }
        this.site = e.itemData.siteCode + e.itemData.siteName
      }
      this.topInfo.itemName = e.itemData?.itemName
      this.topInfo.itemCode = e.itemData?.itemCode
      this.topInfo.itemNo = e.itemData?.itemNo
      this.topInfo.orderItemNo = e.itemData?.itemNo
      this.topInfo.siteCode = e.itemData?.siteCode
      this.topInfo.siteName = e.itemData?.siteName
      this.topInfo.itemCode = e.itemData?.itemCode

      this.$forceUpdate()
      this.topInfo.quantity = e.itemData?.quantity
      if (e.value && this.topInfo.buyerEnterpriseId) {
        // alert(1);

        this.$emit(
          'codeChange',
          {
            supplierCode: this.topInfo.supplierCode,
            orderQuantity: this.topInfo.quantity,
            buyerOrgCode: this.topInfo.buyerOrgCode,
            orderCode: this.topInfo.orderCode,
            buyerEnterpriseId: this.topInfo.buyerEnterpriseId,
            siteCode: this.topInfo.siteCode,
            itemCode: this.topInfo.itemCode,
            itemName: this.topInfo.itemName,
            lineNo: this.topInfo.itemNo
          },
          'change'
        )
      }

      sessionStorage.setItem('entryData', JSON.stringify(this.topInfo))

      // }
    },
    typeInfo(e) {
      if (e === 0) {
        return this.$t('新建')
      } else if (e === 1) {
        return this.$t('待确认')
      } else if (e === 2) {
        return this.$t('退回')
      } else if (e === 3) {
        return this.$t('已确认')
      } else if (e === 4) {
        return this.$t('已完结')
      } else if (e === 5) {
        return this.$t('已完结')
      }
    },
    findInBuyingByCustomerCode() {
      this.$API.masterData
        .findInBuyingByCustomerCode({ customerCode: this.topInfo.buyerCode })
        .then((r) => {
          this.topInfo.supplierCode = r.data.supplierCode
          this.topInfo.buyerOrgCode = r.data.organizationCode
          this.$forceUpdate()
        })
    },
    findInBuyingByCustomerCode3() {
      this.$API.masterData
        .findInBuyingByCustomerCode({ customerCode: this.headInfo.buyerCode })
        .then((r) => {
          this.headInfo.supplierCode = r.data.supplierCode
          this.headInfo.supplierName = r.data.supplierName
          this.headInfo.buyerOrgCode = r.data.organizationCode
          this.$forceUpdate()
        })
    },

    companyChange(e) {
      this.topInfo.orderCode = ''

      this.topInfo.itemName = ''
      this.topInfo.orderItemNo = ''

      this.topInfo.itemCode = ''
      this.topInfo.quantity = ''
      this.topInfo.siteCode = ''
      this.topInfo.siteName = ''
      // this.topInfo.buyerOrgCode = "";

      if (this.topInfo.customerEnterpriseId) {
        this.$emit('deleteRe')
      }

      this.siteOptions = []
      this.siteOptions1 = []
      this.buyerEnterpriseId = e.itemData.customerEnterpriseId
      this.topInfo.buyerEnterpriseId = e.itemData.customerEnterpriseId
      this.topInfo.buyerCode = e.itemData.customerCode
      this.topInfo.buyerName = e.itemData.customerName
      this.topInfo.buyerEnterpriseId = e.itemData.customerEnterpriseId
      this.purOrderQueryOrder()

      this.getSourceAvailable({})
      this.findInBuyingByCustomerCode()
    },
    companyChange3(e) {
      this.headInfo.outsourcedSiteCode = ''
      this.headInfo.supplierCode = ''
      this.headInfo.supplierCode = ''
      this.buyerEnterpriseId = e.itemData.customerEnterpriseId
      this.headInfo.buyerEnterpriseId = e.itemData.customerEnterpriseId
      this.headInfo.buyerCode = e.itemData.customerCode
      this.headInfo.buyerName = e.itemData.customerName

      // this.topInfo.buyerOrgCode = "";
      if (this.headInfo.customerEnterpriseId) {
        this.$emit('deleteRe')
      }
      this.headInfo.buyerEnterpriseId = e.itemData.customerEnterpriseId
      this.getSourceAvailable3({})
      this.getSourceAvailable4({})
      this.findInBuyingByCustomerCode3()
    },
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.topInfo)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    onSubmit() {
      this.$refs.ruleForm1.validate((valid) => {
        if (valid) {
          this.$emit('submit', {
            ...this.headInfo,
            createType: this.topInfo.createType
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    fuzzyQuery() {
      this.$API.outsourcing.fuzzyQuery({ fuzzyNameOrCode: '' }).then((res) => {
        res.data.forEach((item) => {
          item.name = item.customerCode + '-' + item.customerName
          item.code = item.customerEnterpriseId
          item.label = item.customerCode + '-' + item.customerName
          item.value = item.customerCode
        })
        this.dataArr3 = res.data
        this.customerCodeOptions = res.data
      })
    },
    purOrderQueryOrder() {
      let params = {
        // siteCode: this.topInfo.siteCode,
        enterpriseId: this.topInfo.customerEnterpriseId,
        page: {
          current: 1,
          size: 10000
        },
        defaultRules: [
          {
            field: 'orderCode',
            operator: 'contains',
            // type: "number",
            value: ''
          }
        ]
      }
      this.$API.purchaseOrder.getPurOrderQueryOrder(params).then((r) => {
        this.dataArr2 = r.data.records.map((e) => {
          return { name: e, code: e }
        })
      })
    },

    getSourceAvailable(args) {
      const { text, updateData } = args
      let obj = {
        customerCode: this.topInfo.buyerCode,
        enterpriseId: this.topInfo.buyerEnterpriseId,
        fuzzyParam: text,
        dataLimit: 10000
      }
      this.$API.masterData.siteQuery(obj).then((res) => {
        const list = res?.data || []

        this.siteOptions = addCodeNameKeyInList({
          firstKey: 'siteCode',
          secondKey: 'siteName',
          list
        })
        if (updateData) {
          this.$nextTick(() => {
            updateData(this.siteOptions)
          })
        }
      })
    },
    // 手工创建获取领料工厂
    getSourceAvailable3(args) {
      const { text, updateData } = args
      this.getSiteList(text, updateData, 'receive')
    },
    // 手工创建获取委外工厂
    getSourceAvailable4(args) {
      const { text, updateData } = args
      this.getSiteList(text, updateData, 'outSourced')
    },
    getSiteList(text, updateData, type) {
      let obj = {
        customerCode: this.headInfo.buyerCode,
        enterpriseId: this.headInfo.buyerEnterpriseId,
        fuzzyParam: text,
        dataLimit: 10000
      }
      this.$API.masterData.siteQuery(obj).then((res) => {
        const list = res?.data || []
        const _options = addCodeNameKeyInList({
          firstKey: 'siteCode',
          secondKey: 'siteName',
          list
        })
        if (type === 'outSourced') {
          this.siteOptions1 = cloneDeep(_options)
          if (updateData) {
            this.$nextTick(() => {
              updateData(this.siteOptions1)
            })
          }
        } else {
          this.siteOptions = cloneDeep(_options)
          if (updateData) {
            this.$nextTick(() => {
              updateData(this.siteOptions)
            })
          }
        }
      })
    },
    goBack() {
      this.$router.push(`supplier-picking`)
    },
    save() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('save', this.topInfo)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    onSave() {
      this.$refs.ruleForm1.validate((valid) => {
        if (valid) {
          this.$emit('save', {
            ...this.headInfo,
            createType: this.topInfo.createType
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    expandChange() {
      this.isExpand = !this.isExpand
    },
    onchang(e) {
      this.$emit('onchang', e)
    },
    codeChange(e) {
      if (e.e !== null) {
        if (this.topInfo.itemCode) {
          this.$emit('deleteRe')
        }
        this.topInfo.itemName = ''
        this.topInfo.orderItemNo = ''

        this.topInfo.itemCode = ''
        this.topInfo.quantity = ''
        this.topInfo.siteCode = ''
        this.topInfo.siteName = ''
      }
      this.getOutsourcedType(e.value)

      // this.topInfo.buyerOrgCode = "";
      this.$API.purchaseOrder.purOrderGetByOrder({ code: e.value }).then((res) => {
        console.log('purOrderGetByOrder', res.data.length)
        if (res.data.length === 0) {
          // this.$toast({
          //   content: '当前订单的物料不存在工单BOM和标准BOM，无法创建领料单!',
          //   type: 'warning'
          // })
          this.orderList = res.data
        } else {
          this.orderList = res.data
          this.orderClick({
            value: this.orderList[0].orderCode,
            itemData: this.orderList[0],
            e: e.e
          })
        }
      })
    },
    // 获取采购订单号对应的委外方式
    getOutsourcedType(orderCode) {
      let params = {
        enterpriseId: this.topInfo.customerEnterpriseId,
        page: {
          current: 1,
          size: 10000
        },
        defaultRules: [
          {
            field: 'orderCode',
            operator: 'contains',
            value: orderCode
          }
        ]
      }
      this.$API.purchaseOrder.getOutsourcedType(params).then((res) => {
        const { code, data } = res
        let outsourcedType = ''
        if (code === 200) {
          const orderTypeCode = data.records[0]?.orderTypeCode || ''
          switch (orderTypeCode) {
            case 'T1':
            case 'T2':
              outsourcedType = '1'
              break
            case 'T6':
              outsourcedType = '3'
              break
            default:
              this.$toast({
                content: this.$t('该采购订单不是销售委外或工序委外委外类型!'),
                type: 'warning'
              })
              break
          }
        }
        this.topInfo = {
          ...this.topInfo,
          outsourcedType
        }
      })
    },
    // 获取其他数据 code、name
    getOtherInfo(params, refName, value) {
      // 业务类型、申请人、公司、申请部门、采购组织
      if (value && this.$refs[refName]) {
        let _data = this.$refs[refName].ejsRef.getDataByValue(value)
        for (let key in _data) {
          params[key] = _data[key]
        }
        if (!_data) return
      }
      return params
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin: 20px 20px 20px 0;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .itemcon {
      display: flex;
      align-items: center;
    }
    .item {
      margin-right: 20px;
    }
    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      // &.more-width {
      //   // width: 450px;
      // }

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
