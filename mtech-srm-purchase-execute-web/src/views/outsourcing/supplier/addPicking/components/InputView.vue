<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="disabled"
      @input="onInput"
    ></mt-input>
  </div>
</template>
<script>
export default {
  data() {
    return { data: {}, disabled: true }
  },
  mounted() {
    this.$bus.$on('itemNameChange', (val) => {
      this.data.itemName = val.itemName
      // this.data.lineNo = val.lineNo;
      this.data.stockUnit = val.unitCode
      // this.data.unitCode = val.unitCode;
    }) //接受的物料描述
    // 通过SelectSearch过来
    this.$bus.$on('itemNameChange1', (val) => {
      this.data.itemName = val.itemName
      this.data.stockUnit = val?.purchasingBasicInOrgResponse?.itemInfo?.baseMeasureUnitCode
    }) //接受的物料描述
    this.$bus.$on('batchChange2', (val) => {
      // this.data.lineNo = val.lineNo;
      // this.data.unitCode = val.unitCode;
      console.log(val)
      this.data.maxDemandQuantity = val.maxDemandQuantity

      this.data.maxReceiveQuantity = Number(val.maxCreateQty)
      this.data.stockQuantity = Number(val.qty)

      // this.data.maxReceiveQuantity = val.maxReceiveQuantity;
      // this.data.stockQuantity = val.stockQuantity;
    }) //接受的物料描述
    this.$bus.$on('maxDemandQuantityChange', (val) => {
      this.data.maxDemandQuantity = val
    })
    this.$bus.$on('warehouseChange2', (val) => {
      console.log(val)
      console.log(this.data)

      this.data.warehouseName = val.warehouseName
      this.data.warehouseCode = val.warehouseCode
      this.$parent.$emit('selectedChanged', {
        //传出额外数据
        fieldCode: 'selectedChange',
        itemInfo: {
          ...this.data
        }
      })
    })
  },
  methods: {
    onInput(e) {
      console.log(e)
    }
  }
}
</script>
