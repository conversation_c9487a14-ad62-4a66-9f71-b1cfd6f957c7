<template>
  <div>
    <div class="in-cell 1">
      <debounce-filter-select
        :id="data.column.field"
        v-model="data[data.column.field]"
        :request="fuzzySearch"
        :data-source="dataSource"
        :fields="{ text: 'codeAndName', value: 'value' }"
        :placeholder="placeholder"
        @change="selectChange"
        :open-dispatch-change="true"
        :disabled="isDisabled"
        :allow-filtering="true"
      ></debounce-filter-select>
    </div>
  </div>
</template>
<script>
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      purchase: '', //采购组code
      isDisabled: false
      // topInfo: JSON.parse(sessionStorage.getItem('pickTopInfo'))
    }
  },
  mounted() {
    // 请求物料接口
    if (this.data.column.field === 'itemCode') {
      this.data.itemCode === null
        ? this.getCategoryItem('')
        : this.getCategoryItem(this.data.itemCode)
    }
    this.$bus.$on('receiveSiteChange', () => {
      this.getCategoryItem('')
    })
  },
  methods: {
    //获取物料
    getCategoryItem(e) {
      let topInfo = JSON.parse(sessionStorage.getItem('pickTopInfo'))
      let obj = {
        page: {
          current: 1,
          size: 20
        },
        rules: [
          {
            field: 'itemCode',
            operator: 'likeright',
            value: e
          }
        ],
        // customerEnterpriseId: topInfo.buyerEnterpriseId,
        organizationCode: topInfo.outsourcedSiteCode || topInfo.receiveSiteCode
      }
      this.$API.masterData.getOrgRel(obj).then((res) => {
        res.data.records.forEach((item) => {
          item.name = item.itemCode // 	客户名称
          item.value = item.itemCode // 	客户名称
        })
        this.dataSource =
          res.data.records.map((i) => {
            return {
              ...i,
              codeAndName: `${i.itemCode} - ${i.itemName}`
            }
          }) || []
      })
    },
    // 模糊搜索
    fuzzySearch(e) {
      if (this.data.column.field === 'itemCode') {
        this.getCategoryItem(e.text)
      }
    },
    // 数据change监听
    selectChange(val) {
      if (this.data.column.field === 'itemCode') {
        this.data[this.data.column.field] = val.itemData?.itemCode
        this.$bus.$emit('itemNameChange1', val.itemData) //传给物料名称、单位code、单位名称
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'itemCode',
          itemInfo: {
            stockUnit: val.itemData.stockUnitCode,
            stockUnitName: val.itemData.stockUnitName,
            itemCode: val.itemData.itemCode,
            itemName: val.itemData.itemName
          }
        })
      }
    }
  },
  deactivated() {
    this.$bus.$off('itemNameChange')
  }
}
</script>

<style scoped lang="scss">
.in-cell {
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;

  /deep/ .mt-select {
    .e-input-group-icon,
    .e-ddl-icon,
    .e-search-icon {
      margin-right: 20px;
    }
  }
  > .mt-icons {
    margin-left: 5px;
    cursor: pointer;
    position: absolute;
    top: 8px;
    right: 0;
  }
}
</style>
