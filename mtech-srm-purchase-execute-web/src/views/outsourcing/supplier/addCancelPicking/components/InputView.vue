<template>
  <div id="cell-changed">
    <mt-input
      :id="data.column.field"
      v-model="data[data.column.field]"
      :disabled="disabled"
    ></mt-input>
  </div>
</template>
<script>
// var bigDecimal = require("js-big-decimal");
export default {
  data() {
    return {
      data: {},
      disabled: true,

      packingQuantity: '', //装箱数量
      printQuantity: '', //本次打印数量
      grossWeight: '', //毛重

      netWeight: '' //净重
    }
  },
  mounted() {
    this.deliveryTotalQuantity = this.data.deliveryTotalQuantity
    this.packingQuantity = this.data.packingQuantity
    this.printQuantity = this.data.printQuantity
    this.grossWeight = this.data.grossWeight

    this.netWeight = this.data.netWeight
    this.$bus.$on('itemNameChange', (val) => {
      this.data.itemName = val
    }) //接受的物料名称
    this.$bus.$on('stockUnitChange', (val) => {
      this.data.stockUnit = val
    }) //接受的单位
    this.$bus.$on('stockUnitNameChange', (val) => {
      this.data.stockUnitName = val
    })
    this.$bus.$on('stockQuantityChange', (val) => {
      this.data.stockQuantity = val
    }) //接受的单位

    this.$bus.$on('maxCancelQuantityChange', (val) => {
      // this.data.maxCancelQuantity =
      //   Number(this.data.stockQuantity) - Number(val);
      this.data.maxCancelQuantity = Number(val)
    }) //接受的可退货数量
    this.$bus.$on('maxDemandQuantityChange', (val) => {
      this.data.maxDemandQuantity = val
    })
    this.$bus.$on('warehouseNameChange', (val) => {
      this.data.warehouseName = val
    })
    this.$bus.$off('warehouseChange2')
    this.$bus.$on('warehouseChange2', (val) => {
      console.log(val)
      this.data.warehouseName = val.warehouseName
      this.data.warehouseCode = val.warehouseCode

      this.$parent.$emit('warehouseChanged', {
        //传出额外数据
        fieldCode: 'selectedChange',
        itemInfo: {
          ...this.data
        }
      })
    })
  },
  methods: {}
}
</script>
