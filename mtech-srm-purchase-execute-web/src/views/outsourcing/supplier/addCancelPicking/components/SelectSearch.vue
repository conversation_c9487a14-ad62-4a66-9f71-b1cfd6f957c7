<template>
  <div>
    <div class="in-cell">
      <debounce-filter-select
        :id="data.column.field"
        v-model="data[data.column.field]"
        :request="fuzzySearch"
        :data-source="dataSource"
        :fields="{ text: 'codeAndName', value: 'value' }"
        :placeholder="placeholder"
        @change="selectChange"
        :open-dispatch-change="true"
        :disabled="isDisabled"
        :allow-filtering="true"
      ></debounce-filter-select>
      <mt-icon
        v-show="data.column.field === 'itemCode'"
        style="width: 20px"
        name="icon_input_search"
        @click.native="showDialog"
      ></mt-icon>
    </div>
    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="title"
      :buttons="buttons"
      @close="handleClose"
    >
      <div class="full-height">
        <mt-template-page
          ref="templateRef"
          class="template-height has-page"
          :hidden-tabs="true"
          :template-config="pageConfig"
          @recordDoubleClick="recordDoubleClick"
        ></mt-template-page>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
import { PROXY_MDM_AUTH } from '@/utils/constant'
// import { utils } from "@mtech-common/utils";
import { maxPageSize } from '@/utils/constant'

export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      title: this.$t('请选择'),
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          toolbar: [],
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: '1cfd4c1a-674b-4547-8365-aff2aed9cbbc',
          grid: {
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: [
              {
                width: '150',
                field: 'itemCode',
                headerText: this.$t('物料编号')
              },
              {
                width: '150',
                field: 'itemName',
                headerText: this.$t('物料名称')
              }
            ],
            asyncConfig: {},
            dataSource: []
          }
        }
      ],
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      purchase: '', //采购组code
      isDisabled: false,
      topInfo: JSON.parse(sessionStorage.getItem('cancelTopInfo'))
    }
  },
  mounted() {
    // 请求物料接口
    if (this.data.column.field === 'itemCode') {
      this.data.itemCode === null
        ? this.getCategoryItem('')
        : this.getCategoryItem(this.data.itemCode)
      // this.getCategoryItem(this.data.itemCode);
    }
    // 请求库存地点接口
    if (this.data.column.field === 'warehouseCode') {
      this.getWarehouseCodeOptions()
    }
  },
  methods: {
    //获取物料
    getCategoryItem(e) {
      let obj = {
        page: {
          current: 1,
          size: 20
        },
        rules: [
          {
            field: 'itemCode',
            operator: 'likeright',
            value: e
          }
        ],
        customerEnterpriseId: this.topInfo.buyerEnterpriseId,
        organizationCode: this.topInfo.siteCode
      }
      this.$API.masterData.getOrgRel(obj).then((res) => {
        res.data.records.forEach((item) => {
          item.name = item.itemCode // 	客户名称
          item.value = item.itemCode // 	客户名称
        })
        this.dataSource =
          res.data.records.map((i) => {
            return {
              ...i,
              codeAndName: `${i.itemCode} - ${i.itemName}`
            }
          }) || []
      })
    },
    // 获取库存地点
    getWarehouseCodeOptions() {
      let obj = {
        enterpriseId: this.topInfo.buyerEnterpriseId,
        params: {
          page: {
            size: maxPageSize,
            current: 1
          },
          condition: 'and',
          defaultRules: [
            {
              // 工厂
              field: 'siteCode',
              operator: 'equal',
              value: this.topInfo.siteCode
            }
          ]
        }
      }
      this.$API.receiptAndDelivery.postSiteTenantExtendQueryByEnterpriseId(obj).then((res) => {
        res.data.records.forEach((item) => {
          item.name = item.siteAddressName
          item.value = item.siteAddress
        })
        this.dataSource = res.data.records.map((i) => {
          return {
            ...i,
            codeAndName: `${i.siteAddress} - ${i.siteAddressName}`
          }
        })
      })
    },

    // 模糊搜索
    fuzzySearch(e) {
      if (this.data.column.field === 'itemCode') {
        this.getCategoryItem(e.text)
      }
    },
    // 数据change监听
    selectChange(val) {
      if (this.data.column.field === 'itemCode') {
        // this.handleClose();
        this.data[this.data.column.field] = val.itemData?.itemCode
        this.$bus.$emit('itemNameChange', val.itemData.itemName) //传给物料名称
        this.$bus.$emit(
          'stockUnitChange',
          val.itemData?.purchasingBasicInOrgResponse?.itemInfo?.baseMeasureUnitCode
        ) //传给单位
        this.$bus.$emit(
          'stockUnitNameChange',
          val.itemData?.purchasingBasicInOrgResponse?.itemInfo?.baseMeasureUnitName
        ) //传给单位
        this.$parent.$emit('selectedChanged', {
          fieldCode: 'itemCode',
          itemInfo: {
            stockUnit: val.itemData?.purchasingBasicInOrgResponse?.itemInfo?.baseMeasureUnitCode,
            stockUnitName:
              val.itemData?.purchasingBasicInOrgResponse?.itemInfo?.baseMeasureUnitName,
            itemCode: val.itemData.itemCode,
            itemName: val.itemData.itemName
          }
        })
        let params = {
          buyerEnterpriseId: this.topInfo.buyerEnterpriseId,
          createType: 2,
          itemCode: val.itemData.itemCode,
          buyerOrgCode: this.topInfo.buyerOrgCode,
          isOutSale: this.topInfo.isOutSale,
          id: this.data?.id,
          siteCode: this.topInfo.siteCode,
          supplierCode: this.topInfo.supplierCode,
          outsourcedType: this.topInfo.outsourcedType
        }

        // 查询库存
        this.$API.outsourcing.outWaitQuerySapOutDemand(params).then((res) => {
          if (res.data.length > 0) {
            this.$bus.$emit('maxCancelQuantityChange', res.data[0].maxReceiveQuantity)
            this.$bus.$emit('stockQuantityChange', res.data[0].supplierStock) //传给库存现有
          }
        })
        this.handleClose()
      }
      // 暂不需要
      // if (this.data.column.field === "batch") {
      //   this.$bus.$emit("stockQuantityChange", val.itemData.qty); //传给库存
      //   this.$bus.$emit("maxCancelQuantityChange", val.itemData.maxCreateQty); //传给可退货数量
      //   this.$parent.$emit("selectedChanged", {
      //     fieldCode: "batch",
      //     itemInfo: {
      //       batch: val.itemData.batch,
      //       basicUnit: val.itemData.unitName,
      //       orderItemNo: val.itemData.lineNo,
      //     },
      //   });
      // }
      if (this.data.column.field === 'warehouseCode') {
        this.data[this.data.column.field] = val.itemData?.value
        this.data.warehouseName = val.itemData?.name
        this.$bus.$emit('warehouseNameChange', val.itemData?.name) //传给库存地点名称
      }
    },

    // ------ 物料弹框相关逻辑
    // 物料弹框 - 显示
    showDialog() {
      this.pageConfig[0].grid.asyncConfig = {
        url: `${PROXY_MDM_AUTH}/item-org-rel/paged-query?BU_CODE=${localStorage.getItem(
          'currentBu'
        )}`,
        recordsPosition: 'data.records',
        params: {
          customerEnterpriseId: this.topInfo.buyerEnterpriseId,
          organizationCode: this.topInfo.siteCode
        }
      }
      this.$refs.dialog.ejsRef.show()
    },
    // 物料弹框 - 双击选中
    recordDoubleClick(args) {
      this.selectChange({ itemData: args.rowData })
    },
    // 物料弹框 - 确认
    confirm(e, records) {
      if (!records || records.length <= 0) {
        records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      }
      if (records.length <= 0) return
      this.selectChange({ itemData: records[0] })
    },
    // 物料弹框 - 关闭
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  },
  deactivated() {
    this.$bus.$off('maxDemandQuantityChange')
    this.$bus.$off('warehouseChange')
    this.$bus.$off('warehouseNameChange')
    this.$bus.$off('itemNameChange')
  }
}
</script>

<style scoped lang="scss">
.in-cell {
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;

  /deep/ .mt-select {
    .e-input-group-icon,
    .e-ddl-icon,
    .e-search-icon {
      margin-right: 20px;
    }
  }
  > .mt-icons {
    margin-left: 5px;
    cursor: pointer;
    position: absolute;
    top: 8px;
    right: 0;
  }
}
</style>
