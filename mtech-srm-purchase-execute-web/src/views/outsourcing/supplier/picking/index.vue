<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :permission-obj="permissionObj"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
    <deliver
      @handleAddDialogShow="handleAddDialogShow"
      @resolveClick="resolveClick"
      ref="headerTop"
      v-if="deliveryShow"
      class="flex-keep"
    ></deliver>
  </div>
</template>

<script>
import { pageConfig } from './config'
import Vue from 'vue'
export default {
  components: {
    deliver: require('./components/deliver.vue').default
  },
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0060' },
          { dataPermission: 'b', permissionCode: 'T_02_0061' }
        ]
      },
      pageConfig: pageConfig(),
      deliveryShow: false,
      chooseIds: []
    }
  },
  created() {},
  mounted() {},
  methods: {
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    resolveClick(value) {
      this.materialReject(value)
      this.deliveryShow = false
    },
    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    queryRfxConfig() {},
    //表格按钮-点击事件
    handleClickToolBar(e) {
      if (e.toolbar.id == 'create') {
        this.$router.push('supplier-add-picking')
        return
      }
      const selectRows = e.gridRef.getMtechGridRecords()

      if (selectRows.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let obj = []
      selectRows.forEach((item) => {
        obj.push(item.receiveOrderCode)
      })
      if (e.toolbar.id === 'print') {
        this.$API.outsourcing.outReceiveOrderSupplierPrint(obj).then((res) => {
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = function () {
              console.log('======', reader)
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }

            return
          }
          const content = res.data
          let pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))

          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
      }
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    supplierSubmit(e) {
      this.$API.outsourcing.supplierSubmit({ ids: e }).then(() => {
        this.confirmSuccess()
      })
    },
    supplierCancel(e) {
      this.$API.outsourcing.supplierCancel({ ids: e }).then(() => {
        this.confirmSuccess()
      })
    },
    supplierDelete(e) {
      this.$API.outsourcing.supplierDelete({ ids: e }).then(() => {
        this.confirmSuccess()
      })
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'push') {
        this.supplierSubmit(e.data.id)
      } else if (e.tool.id == 'cancel') {
        this.supplierCancel(e.data.id)
      } else if (e.tool.id == 'delete') {
        this.supplierDelete(e.data.id)
      } else if (e.tool.id == 'edit') {
        this.$router.push({
          path: 'supplier-add-picking',
          query: { receiveOrderCode: e.data.receiveOrderCode, type: 'edit' }
        })
      }
    },
    //单元格标题点击操作
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
      this.$router.push({
        path: 'supplier-add-picking',
        query: { receiveOrderCode: e.data.receiveOrderCode, type: 'readonly' }
      })
    }
  }
}
</script>
<style lang="scss">
.form-design {
  padding-left: 10px;
  background: #fff;
}
.e-dlg-container > .right-wrapper {
  height: 100% !important;
  max-height: 100% !important;
  width: 95% !important;
  .dialog-panel {
    .panel-title {
      font-size: 16px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(41, 41, 41, 1);
      margin-bottom: 18px;
      &.price-title {
        margin-bottom: 0;
      }
    }
    .full-width {
      width: 100% !important;
    }
  }
}
</style>
<style lang="scss" scoped>
.full-height {
  /deep/.mt-tabs {
    background: #fafafa;
  }
  /deep/ .tab-container {
    background: #fafafa !important;
  }
}
</style>
