import { i18n } from '@/main.js'
import { timeNumberToDate } from '@/utils/utils'
import Vue from 'vue'
const todoListToolBar = [
  {
    id: 'create',
    icon: 'icon_solid_edit',
    title: i18n.t('创建委外领料单'),
    permission: ['O_02_0681']
  },
  {
    id: 'print',
    icon: 'icon_table_print',
    title: i18n.t('打印领料单'),
    permission: ['O_02_1046']
  }
]
const todoListToolBarDetail = [
  // {
  //   id: "create",
  //   icon: "icon_solid_edit",
  //   title: i18n.t("创建委外领料单"),
  //   permission: ["O_02_0681"],
  // },
  // {
  //   id: "print",
  //   icon: "icon_table_print",
  //   title: i18n.t("打印领料单"),
  //   permission: ["O_02_1046"],
  // },
]

const todoListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'receiveOrderCode',
    headerText: i18n.t('委外领料单号'),
    width: 300,
    cellTools: [
      // {
      //   id: "edit",
      //   icon: "icon_list_edit",
      //   title: i18n.t("编辑"),
      // },
      // {
      //   id: "delete",
      //   icon: "icon_Delete",
      //   title: i18n.t("删除"),
      // },
    ]
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: 200,
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('采购退回'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('已确认'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('已完结'), cssClass: 'col-active' },
        { value: 5, text: i18n.t('已取消'), cssClass: 'col-active' }
      ]
    },
    cellTools: [
      {
        id: 'push',
        title: i18n.t('提交'),
        visibleCondition: (data) => {
          return data['status'] == 0 || data['status'] == 2
        },
        permission: ['O_02_0681']
      },
      {
        id: 'delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => data['status'] == 0,
        permission: ['O_02_0681']
      },
      {
        id: 'edit',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return data['status'] == 0 || data['status'] == 2
        },
        permission: ['O_02_0681']
      },
      {
        id: 'cancel',
        title: i18n.t('取消'),
        visibleCondition: (data) => data['status'] == 2,
        permission: ['O_02_0699']
      },
      {
        id: 'cancel',
        title: i18n.t('取消'),
        visibleCondition: (data) => data['status'] == 3,
        permission: ['O_02_0699']
      }
    ]
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: 300,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerName',
    headerText: i18n.t('公司'),
    width: 300,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerCode}}-{{data.buyerName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  // {
  //   field: "materialApproveUserName",
  //   headerText: i18n.t("供方确认人"),
  // },
  // {
  //   field: "materialApproveDate",
  //   headerText: i18n.t("供方确认时间"),
  //   valueConverter: {
  //     type: "function",
  //     filter: (e) => {
  //       if (e && !isNaN(e) && e.length == 13) {
  //         e = Number(e);
  //         return timeNumberToDate({
  //           formatString: "YYYY-mm-dd",
  //           value: e,
  //         });
  //       }
  //     },
  //   },
  // },
  {
    field: 'buyerApproveUserName',
    headerText: i18n.t('采方确认人')
  },
  {
    field: 'buyerApproveDate',
    headerText: i18n.t('采方确认时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: '0', text: i18n.t('未同步'), cssClass: '' },
        { value: '1', text: i18n.t('同步'), cssClass: '' },
        { value: '2', text: i18n.t('同步失败'), cssClass: '' }
      ]
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('领料供应商'),
    width: 200,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.supplierCode}}-{{data.supplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'createTime',
    headerText: i18n.t('制单日期'),
    width: 200
  },
  {
    field: 'createUserName',
    headerText: i18n.t('制单人'),
    width: 200
  }
]

const completedListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'receiveOrderCode',
    headerText: i18n.t('委外领料单号'),
    width: 300,
    cellTools: [
      // {
      //   id: "edit",
      //   icon: "icon_list_edit",
      //   title: i18n.t("编辑"),
      // },
      // {
      //   id: "delete",
      //   icon: "icon_Delete",
      //   title: i18n.t("删除"),
      // },
    ]
  },
  {
    field: 'receiveOrderStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-inactive' },
        { value: 2, text: i18n.t('退回'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('已确认'), cssClass: 'col-inactive' },
        { value: 4, text: i18n.t('已完结'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('已取消'), cssClass: 'col-active' }
      ]
    },
    cellTools: [
      // {
      //   id: "download",
      //   icon: "icon_solid_Download",
      //   title: i18n.t("下载"),
      //   visibleCondition: () => btnRequired.hasDownload,
      // },
      // {
      //   id: "delete",
      //   icon: "icon_solid_Delete",
      //   title: i18n.t("删除"),
      //   visibleCondition: () => btnRequired.hasDelete && !isView,
      // },
    ]
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: 300,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerName',
    headerText: i18n.t('公司'),
    width: 300,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerCode}}-{{data.buyerName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    width: 200
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料描述'),
    width: 200
  },
  {
    field: 'receiveQuantity',
    headerText: i18n.t('本次领料数量'),
    width: 200
  },
  {
    field: 'receiveQuantity',
    headerText: i18n.t('实发数量'),
    width: 200
  },
  {
    field: 'stockUnit',
    headerText: i18n.t('单位'),
    width: 200
  },
  // {
  //   field: "siteCode",
  //   headerText: i18n.t("工厂编码"),
  //   width: 200,
  //   template: () => {
  //     return {
  //       template: Vue.component("actionInput", {
  //         template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
  //         data: function () {
  //           return { data: {} };
  //         },
  //         mounted() {},
  //         methods: {},
  //       }),
  //     };
  //   },
  // },

  {
    field: 'supplierCode',
    headerText: i18n.t('领料供应商'),
    width: 200,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.supplierCode}}-{{data.supplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },

  {
    field: 'warehouseCode',
    headerText: i18n.t('库存地点'),
    width: 250,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.warehouseCode}}-{{data.warehouseName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'createTime',
    headerText: i18n.t('制单日期'),
    width: 200
  },
  {
    field: 'createUserName',
    headerText: i18n.t('制单人'),
    width: 200
  }
]

//status  定点推荐主表状态 0待处理 3已处理 4已退回
export const pageConfig = () => [
  {
    title: i18n.t('头视图'),
    dataPermission: 'a',
    permissionCode: 'T_02_0060',
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    toolbar: todoListToolBar,
    gridId: Vue.prototype.$tableUUID.outsourcing.piking.list,

    grid: {
      frozenColumns: 1,
      columnData: todoListColumnData,
      asyncConfig: {
        url: '/srm-purchase-execute/tenant/outReceiveOrder/supplier/queryView',
        recordsPosition: 'data.records'
        // defaultRules: [
        //   {
        //     condition: "and",
        //     field: "rfx_item.rfxHeaderId",
        //     operator: "equal",
        //     rfx_bidding_item: "",
        //     rfx_item: "",
        //     value,
        //   },
        // ],
      }
    }
  },
  {
    title: i18n.t('明细视图'),
    dataPermission: 'b',
    permissionCode: 'T_02_0061',
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    toolbar: todoListToolBarDetail,
    gridId: Vue.prototype.$tableUUID.outsourcing.piking.details,

    grid: {
      columnData: completedListColumnData,
      frozenColumns: 1,

      asyncConfig: {
        url: '/srm-purchase-execute/tenant/outReceiveOrder/supplier/queryDetailView',
        recordsPosition: 'data.records'
        // defaultRules: [
        //   {
        //     condition: "and",
        //     field: "rfx_item.rfxHeaderId",
        //     operator: "equal",
        //     value,
        //   },
        // ],
      }
    }
  }
]
