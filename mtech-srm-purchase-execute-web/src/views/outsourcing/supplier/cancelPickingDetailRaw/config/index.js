import { i18n } from '@/main.js'
export const columnData = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },
  {
    field: 'cancelQuantity',
    headerText: i18n.t('退货数量')
  },
  {
    // width: "150",
    field: 'maxCancelQuantity',
    headerText: i18n.t('可退货数量')
  },

  {
    width: '150',
    field: 'stockQuantity',
    headerText: i18n.t('库存现有量')
  },
  {
    width: '150',
    field: 'stockUnit',
    headerText: i18n.t('库存单位')
  },
  // {
  //   width: "150",
  //   field: "buyerOrgName",
  //   headerText: i18n.t("采购组"),
  // },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('行备注')
  }
]
