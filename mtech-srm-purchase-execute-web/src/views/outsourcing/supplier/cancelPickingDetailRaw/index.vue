<template>
  <div class="full-height pt20 vertical-flex-box">
    <top-info
      ref="headerTop"
      :header-info="headerInfo"
      v-if="this.headerInfo"
      @rejectTo="rejectTo"
      @startTo="startTo"
      class="flex-keep"
    ></top-info>
    <mt-template-page ref="templateRef" :template-config="pageConfig1" :hidden-tabs="true" />
    <deliver
      @handleAddDialogShow="handleAddDialogShow"
      @resolveClick="resolveClick"
      ref="headerTop"
      v-if="deliveryShow"
      class="flex-keep"
    ></deliver>
  </div>
</template>

<script>
import { columnData } from './config/index.js'

export default {
  components: {
    topInfo: require('./components/topInfo.vue').default,
    deliver: require('./components/deliver.vue').default
  },
  data() {
    const headerInfo = JSON.parse(localStorage.getItem('purchaseListScope'))
    const id = this.$route.query.id
    const type = this.$route.query.type

    return {
      id,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      deliveryShow: false,
      userInfo: null,
      headerInfo,
      type,
      pageConfig1: [
        {
          gridId: this.$tableUUID.outsourcing.cancelPickingDetailRaw.list,

          grid: {
            columnData: columnData,
            lineIndex: 0,
            allowPaging: false, // 不分页
            // autoWidthColumns: columnData.length + 1,
            dataSource: []
            // asyncConfig: {
            //   url: "/srm-purchase-execute/tenant/_distribute_rule/list",
            // serializeList: serializeList,
            // },
            // frozenColumns: 1,
          }
        }
      ],
      currentTabIndex: 0,
      forecastRules: []
    }
  },
  mounted() {
    // this.currentInfo.title = "物料信息";
    this.init()
    // if (this.$route.query.type === "are") {
    //   this.pageConfig1[0].grid = this.grid1;
    // } else if (this.$route.query.type === "no") {
    //   this.pageConfig1[0].grid = this.grid2;
    // } else {
    //   this.pageConfig1[0].grid = {};
    // }
  },
  methods: {
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    resolveClick(value) {
      let obj = {
        ids: this.headerInfo.id,
        rejectReason: value
      }
      this.$API.outsourcing
        .outCancelOrderMaterialReject(obj)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$router.go(-1)
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef.refreshCurrentGridData()
        })
      this.deliveryShow = false
    },
    rejectTo() {
      this.deliveryShow = true
    },
    startTo() {
      this.certain(this.headerInfo.id)
    },
    // 确认
    certain(id) {
      let obj = {
        ids: id.toString()
      }
      console.log(obj)
      this.$API.outsourcing
        .outCancelOrderMaterialConfirm(obj)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$router.go(-1)
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    // 初始化
    init() {
      let obj = {
        cancelOrderCode: this.$route.query.cancelOrderCode
      }
      this.$API.outsourcing
        .materialGetOne(obj)
        .then((res) => {
          this.pageConfig1[0].grid.dataSource = res.data.outCancelOrderDetailResponseList
          console.log(this.pageConfig1[0].grid.dataSource)
        })
        .catch(() => {
          // this.apiEndLoading();
        })
    },
    handleSelectTab(index, item) {
      this.currentInfo = item
      console.log(item)
      // this.currentTabIndex = e;
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },

    //    // 将行编辑过的数据设置到表格中
    setEditedData(data) {
      if (this.editData.length > 0) {
        this.editData.forEach((itemEdit) => {
          for (let i = 0; i < data.length; i++) {
            if (data[i].id === itemEdit.id) {
              data[i][itemEdit.key] = itemEdit[itemEdit.key]
              break
            }
          }
        })
      }
      return data
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  },
  // 消失
  beforeDestroy() {
    localStorage.removeItem('purchaseListScope')
  }
}
</script>

<style lang="scss" scoped>
.repeat-template {
  .common-template-page {
    /deep/ .mt-tabs {
      display: none;
    }
  }
}
.e-content {
  height: 200px;
}

.mt-tabs {
  width: 100%;
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
</style>
