import { i18n } from '@/main.js'
export const columnData = [
  {
    field: 'itemCode',
    width: '250',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },

  {
    field: 'warehouseName',
    headerText: i18n.t('库存地点'),
    width: 300,
    valueAccessor: (field, data) => {
      return data.warehouseCode + data.warehouseName
    }
  },

  {
    field: 'batch',
    headerText: '批次/卷号'
  },
  {
    field: 'allocationQuantity',
    headerText: i18n.t('调出数量')

    // validationRules: { required: true },
  },
  {
    field: 'wmsAllocationQuantity',
    headerText: i18n.t('实收数量')
  },
  {
    field: 'stockQuantity', // 只是界面显示
    headerText: i18n.t('库存现有量')
  },
  {
    field: 'stockUnit',
    headerText: i18n.t('库存单位')
  },
  // {
  //   field: "", // 只是界面显示
  //   headerText: i18n.t("最小包装量"),
  // },

  {
    field: 'remark',
    headerText: i18n.t('行备注')
  }
]
export const serializeList = (list) => {
  if (list.length > 0) {
    let serialNumber = 1
    list.forEach((item) => {
      // 添加序号
      item.serialNumber = serialNumber++
    })
  }
  return list
}
