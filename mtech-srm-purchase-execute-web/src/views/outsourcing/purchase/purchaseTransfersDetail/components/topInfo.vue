<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 测试用字段prSubmitFlag:{{ prSubmitFlag }} -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="status mr20">
        {{ typeInfo(headerInfo.status) }}
      </div>
      <div class="infos mr20">{{ $t('创建人') }}：{{ headerInfo.createUserName }}</div>
      <div class="infos">{{ $t('创建日期：') }}{{ headerInfo.createTime }}</div>
      <div class="infos mr20">{{ $t('单据编号：') }}{{ headerInfo.allocationOrderCode }}</div>
      <div class="infos mr20" v-if="headerInfo.buyerApproveUserName">
        {{ $t('采方确认人') }}：{{ headerInfo.buyerApproveUserName }}
      </div>
      <div class="infos mr20" v-if="headerInfo.buyerApproveUserName">
        {{ $t('采方确认时间') }}：{{ dateFormat(headerInfo.buyerApproveDate) }}
      </div>
      <div class="infos mr20" v-if="headerInfo.inApproveUserName">
        {{ $t('供方确认人') }}：{{ headerInfo.inApproveUserName }}
      </div>
      <div class="infos mr20" v-if="headerInfo.inApproveDate">
        {{ $t('供方确认时间') }}：{{ dateFormat(headerInfo.inApproveDate) }}
      </div>
      <div class="middle-blank"></div>

      <!-- 右侧各种操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button
        css-class="e-flat"
        v-if="headerInfo.status === 3"
        :is-primary="true"
        v-permission="['O_02_1137']"
        @click="resolveclick"
        >{{ $t('确认') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-permission="['O_02_1138']"
        v-if="headerInfo.status === 3"
        @click="rejectclick"
        >{{ $t('退回') }}</mt-button
      >
      <!-- 草稿和审批拒绝状态可以保存草稿 -->
      <!-- <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-if="type === 'put"
        @click="startToSave(1)" -->
      <!-- >{{ $t("保存草稿") }}</mt-button -->
      <!-- > -->
      <!-- 草稿和审批拒绝状态可以提交 -->
      <!-- <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-if="type === 'add"
        @click="startToSave(2)"
        >{{ $t("提交") }}</mt-button
      > -->
      <!-- <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-if="type === 'no"
        @click="startToSave(1)"
        >{{ $t("补订单") }}</mt-button -->
      <!-- > -->
      <div class="sort-box" @click="isExpand = !isExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="addForm" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="businessTypeId" :label="$t('公司')">
          <mt-input
            v-model="company"
            :show-clear-button="false"
            :disabled="true"
            :placeholder="$t('公司')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="businessTypeId" :label="$t('委外工厂')">
          <mt-input
            v-model="site"
            :show-clear-button="false"
            :disabled="true"
            :placeholder="$t('工厂')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="orderCode" :label="$t('采购订单号')">
          <mt-input disabled type="text" v-model="headerInfo.orderCode"></mt-input>
        </mt-form-item>
        <mt-form-item prop="orderCode" :label="$t('采购订单行号')">
          <mt-input disabled type="text" v-model="headerInfo.orderItemNo"></mt-input>
        </mt-form-item>
        <mt-form-item prop="businessTypeId" :label="$t('物料编码')">
          <mt-input v-model="headerInfo.itemCode" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="businessTypeId" :label="$t('物料名称')">
          <mt-input v-model="headerInfo.itemName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="businessTypeId" :label="$t('调出供应商')">
          <mt-input
            v-model="supplier"
            :disabled="true"
            :placeholder="$t('加工供应商编码')"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="title" :label="$t('加工供应商描述')">
          <mt-input
            v-model="headerInfo.supplierName"
            :disabled="true"
            :maxlength="50"
            :placeholder="$t('加工供应商描述')"
          ></mt-input>
        </mt-form-item> -->

        <mt-form-item prop="title" :label="$t('调入供应商')">
          <mt-input
            v-model="materialSupplier"
            :disabled="true"
            :maxlength="50"
            :placeholder="$t('原材料供应商编号')"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="title" :label="$t('原材料供应商描述')">
          <mt-input
            v-model="headerInfo.materialSupplierName"
            :disabled="true"
            :maxlength="50"
            :placeholder="$t('原材料供应商描述')"
          ></mt-input>
        </mt-form-item> -->
        <mt-form-item :label="$t('领料工厂')">
          <debounce-filter-select
            v-model="headerInfo.allocationSiteCode"
            :data-source="siteOptions"
            :is-active="true"
            :value-template="siteCodeValueTemplate"
            :request="postSiteFuzzyQuery"
            :disabled="headerInfo.status !== 3"
            :fields="{ text: 'theCodeName', value: 'siteCode' }"
            @change="siteOrgClick"
            :allow-filtering="true"
          ></debounce-filter-select>
          <!-- <mt-input
            v-model="returnSite"
            :disabled="true"
            :maxlength="50"
          ></mt-input> -->
        </mt-form-item>
        <mt-form-item prop="title" :label="$t('送货地址')">
          <mt-input
            v-model="headerInfo.inSupplierAddress"
            :disabled="true"
            :maxlength="50"
            :placeholder="$t('送货地址')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="businessTypeId" :label="$t('是否销售委外')">
          <mt-input
            v-model="headerInfo.isOutSale"
            :disabled="true"
            :placeholder="$t('是否销售委外')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          v-if="
            headerInfo.buyerApproveReaseon !== null && headerInfo.buyerApproveReaseon !== undefined
          "
          prop="businessTypeId"
          :label="$t('采方退回原因')"
        >
          <mt-input
            v-model="headerInfo.buyerApproveReaseon"
            :disabled="true"
            :placeholder="$t('采方退回原因')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          v-if="headerInfo.inApproveReaseon !== null && headerInfo.inApproveReaseon !== undefined"
          prop="businessTypeId"
          :label="$t('供方退回原因')"
        >
          <mt-input
            v-model="headerInfo.inApproveReaseon"
            :disabled="true"
            :placeholder="$t('供方退回原因')"
          ></mt-input>
        </mt-form-item>
        <!-- 暂无 -->

        <!-- <mt-form-item prop="title" :label="$t('发货日期')">
          <mt-date-time-picker
            v-model="headerInfo.sendTime"
            :disabled="true"
            :placeholder="$t('选择日期和时间')"
          ></mt-date-time-picker>
        </mt-form-item>
        <mt-form-item prop="title" :label="$t('预计到货日期')">
          <mt-date-time-picker
            :disabled="true"
            v-model="headerInfo.forecastArriveTime"
            :placeholder="$t('选择日期和时间')"
          ></mt-date-time-picker>
        </mt-form-item> -->
        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input
            :multiline="true"
            v-model="headerInfo.remark"
            :placeholder="$t('备注')"
            :maxlength="200"
            :disabled="true"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { cloneDeep } from 'lodash'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  props: {
    headerInfo: {
      type: Object,
      default: () => {
        return 1212
      }
    }
  },
  data() {
    return {
      isInit: true, // 是第一次赋值
      isExpand: true,
      type: '', // add view edit
      site: '',
      company: '',
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      siteOptions: [],
      supplier: '',
      materialSupplier: '',
      dataArr1: [],
      businessTypeList: [], // 业务类型
      applyUserIdData: [], // 申请人列表
      organizationData: {},
      applyCompanyData: [], // 公司
      applyDepartData: [], // 部门
      addForm: {
        title: '', // 采购申请名称
        businessTypeId: '', // 业务类型
        // businessTypeCode: "",
        // businessTypeName: "",
        applyUserId: '', // 申请人
        // applyUserName: "",
        // applyUserCode: ""
        companyId: '', // 公司
        // companyName: "",
        // companyCode: ""
        applyDepId: '', // 申请部门
        // applyDepCode: "",
        // applyDepName: "",
        remark: '',
        projectDesc: '' // 项目名称的聚合
      },
      rules: {
        businessTypeId: [
          {
            required: true,
            message: this.$t('请选择业务类型'),
            trigger: 'blur'
          }
        ],
        applyUserId: [{ required: true, message: this.$t('请选择申请人'), trigger: 'blur' }],
        companyId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        applyDepId: [
          {
            required: true,
            message: this.$t('请选择申请部门'),
            trigger: 'blur'
          }
        ]
      },
      isClickCancel: false //手动点击了取消按钮， 为了不再次触发change事件
    }
  },

  computed: {},

  watch: {
    headerInfo() {
      this.headerInfo.isOutSale = this.headerInfo.isOutSale === 1 ? '是' : this.$t('否')
      this.company = this.headerInfo.buyerCode + this.headerInfo.buyerName

      this.site = this.headerInfo.siteCode + this.headerInfo.siteName
      this.returnSite = this.headerInfo.allocationSiteCode + this.headerInfo.allocationSiteName
      // site: "",
      this.supplier = this.headerInfo.outSupplierCode + this.headerInfo.outSupplierName
      this.materialSupplier = this.headerInfo.inSupplierCode + this.headerInfo.inSupplierName
    }
  },

  mounted() {
    // this.company = this.headerInfo.buyerCode + this.headerInfo.buyerName;

    // this.site = this.headerInfo.siteCode + this.headerInfo.siteName;
    // this.returnSite =
    //   this.headerInfo.allocationSiteCode + this.headerInfo.allocationSiteName;
    // // site: "",
    // this.supplier =
    //   this.headerInfo.outSupplierCode + this.headerInfo.outSupplierName;
    // this.materialSupplier =
    //   this.headerInfo.inSupplierCode + this.headerInfo.inSupplierName;

    this.type = this.$route.query.type
    // 获取主数据-工厂模糊查询
    this.postSiteFuzzyQuery({ text: undefined })
  },

  methods: {
    siteOrgClick(e) {
      const { itemData } = e
      if (e.e !== null) {
        if (itemData) {
          this.headerInfo.allocationSiteId = itemData.organizationId
          this.headerInfo.allocationSiteCode = itemData.siteCode
          this.headerInfo.allocationSiteName = itemData.siteName
        } else {
          this.headerInfo.allocationSiteId = ''
          this.headerInfo.allocationSiteCode = ''
          this.headerInfo.allocationSiteName = ''
        }
      }
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text,
        enterpriseId: this.headerInfo.buyerEnterpriseId
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            res.data.push({
              areaDetail: ' ',
              areaId: '0',
              areaWholeName: ' ',
              createTime: '2022-01-11 15:15:48',
              createUserName: '李长江',
              id: '1480800595226320897',
              organizationId: '1480800595184377857',
              parentCode: 'DJZKJYXGS20211228201444',
              parentId: '1475802394828308482',
              parentName: '电解智科技有限公司',
              siteCode: 'JDSYGFGC20220111-011212',
              siteDescription: '  ',
              siteGroupId: '0',
              siteName: '工厂是采方的不是供方的1212',
              siteTypeCode: '  ',
              siteTypeId: '0',
              siteTypeName: '  ',
              statusDescription: '激活',
              statusId: '1',
              tenantId: '17706479458443265'
            })
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
          }
        })
        .catch(() => {})
    },
    dateFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }
      return str
    },
    timeFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'HH:MM:SS', value })
      } else {
        str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
      }
      return str
    },
    // 获取当前状态
    typeInfo(e) {
      if (e === 0) {
        return '新建'
      } else if (e === 1) {
        return '待供应商确认'
      } else if (e === 2) {
        return '调入供应商退回'
      } else if (e === 3) {
        return '调入供应商确认'
      } else if (e === 4) {
        return '采购退回'
      } else if (e === 5) {
        return '采购确认'
      } else if (e === 6) {
        return '已完结'
      } else if (e === 7) {
        return '已取消'
      }
    },
    resolveclick() {
      let obj = cloneDeep(this.headerInfo)

      obj.isOutSale = obj.isOutSale === '是' ? 1 : 0
      console.log(obj)
      // this.$API.outsourcing.buyerConfirmAndUpdate(obj).then((res) => {
      //   this.$toast({ content: this.$t("操作成功"), type: "success" });

      //   this.goBack();
      // });
      this.$emit('startTo', obj)
    },
    rejectclick() {
      this.$emit('rejectTo')
    },

    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      &.more-width {
        width: 450px;
      }

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
