<template>
  <div class="full-height pt20">
    <mt-template-page :template-config="pageConfig" @handleSelectTab="handleSelectTab">
      <mt-template-page
        slot="slot-0"
        ref="templateRef"
        :template-config="pageConfig1"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
        @handleClickCellTool="handleClickCellTool"
      >
      </mt-template-page>

      <mt-template-page
        slot="slot-1"
        ref="template1"
        :template-config="pageConfig2"
        @rowSelecting="rowSelecting"
        @rowDeselected="rowDeselecting"
        @handleClickToolBar="handleClickToolBar2"
        @cellEdit="cellEdit"
      >
      </mt-template-page>
      <mt-template-page
        slot="slot-2"
        ref="template-2"
        @handleClickToolBar="handleClickToolBar3"
        :template-config="pageConfig3"
      >
      </mt-template-page>
    </mt-template-page>
    <deliver
      @handleAddDialogShow="handleAddDialogShow"
      @resolveClick="resolveClick"
      ref="headerTop"
      v-if="deliveryShow"
      class="flex-keep"
    ></deliver>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { BASE_TENANT } from '@/utils/constant'
import { columnData, columnData2, columnData3 } from './config/index.js'
// import deliveryDialog from "./components/deliveryDialog";
import { cloneDeep } from 'lodash'

export default {
  mounted() {},
  components: {
    deliver: require('./components/deliver.vue').default
  },
  data() {
    return {
      id: [],
      editData: [],
      deliveryShow: false,
      // permissionObj: {
      //   permissionNode: {
      //     // 当前的dom元素
      //     code: "ignore-element",
      //     type: "remove",
      //   },
      //   childNode: [
      //     { dataPermission: "a", permissionCode: "T_02_0120" },
      //     { dataPermission: "b", permissionCode: "T_02_0121" },
      //   ],
      // },
      pageConfig: [
        {
          title: this.$t('头视图')
          // dataPermission: "a",
          // permissionCode: "T_02_0148", // 需要与permissionObj中的参数和权限code对应
        },
        {
          title: this.$t('待审核')
          // dataPermission: "b",
          // permissionCode: "T_02_0149", // 需要与permissionObj中的参数和权限code对应
        },
        {
          title: this.$t('明细视图')
          // dataPermission: "b",
          // permissionCode: "T_02_0149", // 需要与permissionObj中的参数和权限code对应
        }
      ],
      userInfo: null,
      addDialogShow: false,
      currentTabIndex: 0,
      pageConfig1: [
        {
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'accept1',
                  icon: 'icon_table_accept1',
                  title: this.$t('确认')
                  // permission: ["O_02_1299"],
                },
                {
                  id: 'recall',
                  icon: 'icon_table_recall',
                  title: this.$t('退回')
                  // permission: ["O_02_1299"],
                },
                {
                  id: 'print',
                  icon: 'icon_table_print',
                  title: this.$t('打印')
                  // permission: ["O_02_1046"],
                },
                {
                  id: 'restart',
                  icon: 'icon_table_restart',
                  title: this.$t('手工同步')
                  // permission: ["O_02_1300"],
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          grid: {
            columnData: columnData,
            lineIndex: 1,

            autoWidthColumns: columnData.length + 1,
            dataSource: [],
            asyncConfig: {
              page: {
                current: 1,
                size: 10000
              },
              url: `${BASE_TENANT}/kt/outAllocationOrder/buyer/queryView`,
              defaultRules: [
                {
                  field: 'orderType',
                  operator: 'equal',
                  value: this.$route.path.includes('gc') ? 1 : 0
                }
              ]
            },
            frozenColumns: 1
          }
        }
      ],
      pageConfig2: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'detailAccept',
                  icon: 'icon_table_accept1',
                  title: this.$t('确认')
                  // permission: ["O_02_1295"],
                },
                {
                  id: 'recall',
                  icon: 'icon_table_recall',
                  title: this.$t('退回')
                  // permission: ["O_02_1299"],
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          grid: {
            columnData: columnData2,
            lineIndex: 1,
            allowPaging: false, // 不分页
            autoWidthColumns: columnData2.length + 1,
            dataSource: [],
            asyncConfig: {
              page: {
                current: 1,
                size: 10000
              },
              url: `${BASE_TENANT}/kt/outAllocationOrder/buyer/queryDetailView`,
              rules: [
                {
                  field: 'orderType',
                  operator: 'equal',
                  value: this.$route.path.includes('gc') ? 1 : 0
                },
                {
                  condition: 'and',
                  rules: [
                    {
                      condition: 'or',
                      field: 'status',
                      type: 'string',
                      operator: 'contains',
                      value: 1
                    },
                    {
                      condition: 'or',
                      field: 'status',
                      type: 'string',
                      operator: 'contains',
                      value: 2
                    },
                    {
                      condition: 'or',
                      field: 'status',
                      type: 'string',
                      operator: 'contains',
                      value: 3
                    }
                  ]
                }
              ]
            },
            frozenColumns: 1
          }
        }
      ],
      pageConfig3: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'export1',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          gridId: '0a524210-912f-4259-b21c-d7d930b71951',
          grid: {
            columnData: columnData3,
            lineIndex: 1,
            autoWidthColumns: columnData2.length + 1,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/kt/outAllocationOrder/buyer/queryDetailView`,
              rules: [
                {
                  field: 'orderType',
                  operator: 'equal',
                  value: this.$route.path.includes('gc') ? 1 : 0
                }
              ]
            },
            frozenColumns: 1
          }
        }
      ],
      dialogData: null
    }
  },
  methods: {
    resolveClick(a) {
      this.resolvechange(this.id, a)
      this.deliveryShow = false
    },
    resolvechange(id, a) {
      let obj = {
        ids: id.toString(),
        allocationReason: a
      }
      this.$API.outsourcing
        .outNewAllocationOrderBuyerReject(obj)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef?.refreshCurrentGridData()
            this.$refs.template1.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef?.refreshCurrentGridData()
          this.$refs.template1.refreshCurrentGridData()
        })
    },
    handleClickToolBar2(e) {
      console.log(e)
      // if (e.toolbar.id == "new") {
      //   this.$router.push(`create-noOrder-supplier`);
      //   return;
      // }
      const { toolbar, gridRef } = e
      let _status = [],
        _id = []
      let obj = []

      e.gridRef.getMtechGridRecords().map((item) => {
        _id.push(item.allocationOrderId), _status.push(item.status)
        // _sourceStatus.push(item.sourceStatus),
        // _remainingQuantity.push(item.remainingQuantity);
        obj.push(item.cancelOrderCode)
      })
      if (e.gridRef.getMtechGridRecords()?.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id === 'detailAccept') {
        const _selectedData = gridRef.getMtechGridRecords()
        console.log(_selectedData)
        this.handleClickToolBarSubmit(_selectedData)
      }
      if (e.toolbar.id == 'recall') {
        this.id = _id
        for (let i of _status) {
          if (i !== 3 && i !== 2 && i !== 1) {
            this.$toast({
              content: this.$t('该状态不可操作'),
              type: 'warning'
            })
            return
          }
        }
        this.deliveryShow = true
      }
    },
    //复选框事件
    rowSelecting(e) {
      if (e?.rowIndexes?.length < 1) return
      if (e.data instanceof Array) return
      // if (e?.data?.status != 2) {
      //   let mapArr = e.rowIndexes.filter((item) => {
      //     return item !== e.rowIndex;
      //   });
      //   if (!(e.data instanceof Array)) {
      //     console.log(e, mapArr);
      //     this.$nextTick(() => {
      //       this.$refs.templateRef
      //         .getCurrentUsefulRef()
      //         .ejsRef.selectRows(mapArr);
      //     });
      //   }
      //   return;
      // }
      //获取当前页所有的行
      let Obj = cloneDeep(this.$refs.template1.getCurrentUsefulRef().ejsRef.getCurrentViewRecords())
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.template1.getCurrentUsefulRef().ejsRef.getSelectedRecords()
      )
      currentSelect.push(e.data)
      currentSelect = [...new Set(currentSelect)]
      if (Obj instanceof Array && Obj.length > 0) {
        let mapArr = []
        for (let i = 0; i < currentSelect.length; i++) {
          for (let j = 0; j < Obj.length; j++) {
            if (currentSelect[i]?.allocationOrderCode === Obj[j]?.allocationOrderCode) {
              mapArr.push(Obj[j])
            }
          }
        }
        mapArr = mapArr.map((item) => item.index)
        if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0 && mapArr.length > 0) {
          this.$refs.template1.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
        }
        console.log(mapArr)
      }
    },
    //取消
    rowDeselecting(e) {
      if (e?.rowIndexes?.length < 1) return
      //获取当前页所有的行
      let Obj = cloneDeep(this.$refs.template1.getCurrentUsefulRef().ejsRef.getCurrentViewRecords())
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.template1
          .getCurrentUsefulRef()
          .ejsRef.getSelectedRecords()
          .map((e) => e.id)
      )
      let mapArr = []
      Obj.filter((item, index) => {
        if (
          item.allocationOrderCode !== e.data.allocationOrderCode &&
          currentSelect.includes(item.id)
        ) {
          console.log(item)
          mapArr.push(index)
        }
      })
      console.log(e, mapArr)
      if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0) {
        this.$refs.template1.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
      }
    },
    handleClickToolBarSubmit(selectedData) {
      let _selectedData = cloneDeep(selectedData)
      if (_selectedData.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //没有编辑但是勾选的值
      let newarr = this.editData.map((item) => item.id)
      let incluArr = _selectedData.filter((item) => !newarr.includes(item.id))
      // incluArr.map((item) => {
      //   item.receiveQuantity = item.deliveryQuantity;
      // });
      //编辑的值替换勾选的值
      this.editData.forEach((item) => {
        let index = _selectedData.findIndex((e) => e.id == item.id)
        if (index > -1) {
          _selectedData[index] = item
        }
      })
      //勾选但未编辑的值 ---替换
      incluArr.forEach((item) => {
        let index = _selectedData.findIndex((e) => e.id == item.id)
        if (index > -1) {
          _selectedData[index] = item
        }
      })
      console.log(_selectedData)
      this.doSubmit(_selectedData)
    },
    doSubmit(e) {
      console.log(e)

      let obj = e.map((item) => {
        return {
          id: item.id,
          outOrderId: item.allocationOrderId,
          wmsQuantity: item.wmsAllocationQuantity
        }
      })
      console.log(obj)
      this.$API.outsourcing
        .OrderNewDetailBuyerConfirmAndUpdateBatch(obj)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.template1.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.template1.refreshCurrentGridData()
        })
    },
    // 确认
    certain(id) {
      let obj = {
        ids: id.toString()
      }
      console.log(obj)
      this.$API.outsourcing
        .outNewAllocationOrderBuyerConfirm(obj)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef.refreshCurrentGridData()
        })
    },
    // 弹框
    handleDialogShow(flag) {
      this.deliveryShow = flag
    },
    handleSelectTab(e) {
      this.currentTabIndex = e
    },
    // 点击单元格 标题
    handleClickCellTitle(e) {
      // if (e.data.status == "1") {

      if (e.field === 'allocationOrderCode') {
        this.$router.push(
          `purchase-transfers-detail-v2?allocationOrderCode=${e.data.allocationOrderCode}`
        )
      }

      // } else if (e.data.status == "0") {
      //   this.$router.push(`deliver-detail-supplier?id=${e.data.id}&type=no`);
      // }
    },
    handleClickCellTool(e) {
      console.log(e)
      if (e.tool.title === this.$t('确认')) {
        this.certain(e.data.id)
      }
      if (e.tool.title === this.$t('退回')) {
        this.id = []
        this.id.push(e.data.id)
        this.deliveryShow = true
      }
      if (e.tool.title === this.$t('手工同步')) {
        this.id = []
        this.restartClick(e.data.id)
      }
    },
    //编辑行内
    cellEdit(e) {
      console.log(e)
      let { status, data } = e
      let flag = false
      if (status) {
        if (this.editData && this.editData.length < 1) {
          this.editData.push(data)
        } else {
          for (let i = 0; i < this.editData.length; i++) {
            if (this.editData[i].id !== data.id) {
              flag = true
              break
            }
          }
          if (flag) {
            let arr = this.editData.map((item) => item.id)
            let indexOf = arr.indexOf(data.id)
            if (indexOf == -1) {
              this.editData.push(data)
            }
          }
        }
      } else {
        if (this.editData && this.editData.length < 1) return
        this.editData.map((item, i) => {
          if (item.id === data.id) {
            this.editData.splice(i, 1)
          }
        })
      }

      console.log(this.editData)
    },
    restartClick(id) {
      console.log(id)
      let pd = id.toString()
      let obj = { ids: pd }
      this.$API.outsourcing.outNewAllocationOrderBuyerSyncWms(obj).then(() => {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    handleClickToolBar(e) {
      console.log(e)

      if (e.gridRef.getMtechGridRecords()?.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _status = [],
        _id = []
      let obj = []

      let _syncStatus = []
      e.gridRef.getMtechGridRecords().map((item) => {
        _id.push(item.id), _status.push(item.status)
        _syncStatus.push(item.syncStatus)
        obj.push(item.allocationOrderCode)

        // _sourceStatus.push(item.sourceStatus),
        // _remainingQuantity.push(item.remainingQuantity);
      })
      this.id = _id
      console.log(_status)
      if (e.toolbar.id == 'accept1') {
        for (let i of _status) {
          if (i !== 3 && i === 2 && i === 1) {
            this.$toast({
              content: this.$t('该状态不可操作'),
              type: 'warning'
            })
            return
          }
        }
        this.certain(_id)
        return
      }

      if (e.toolbar.id == 'recall') {
        for (let i of _status) {
          if (i !== 3 && i !== 2 && i !== 1) {
            this.$toast({
              content: this.$t('该状态不可操作'),
              type: 'warning'
            })
            return
          }
        }
        this.deliveryShow = true
      }
      if (e.toolbar.id == 'restart') {
        console.log(_syncStatus)
        // for (let i of _syncStatus) {
        //   if (i === "1") {
        //     this.$toast({
        //       content: this.$t("该状态不可手工同步"),
        //       type: "warning",
        //     });

        //     return;
        //   }
        // }
        this.restartClick(_id)
      }
      if (e.toolbar.id === 'print') {
        this.$API.outsourcing.outAllocationOrderBuyerPrint(obj).then((res) => {
          // if (res?.data?.type === "application/json") {
          //   const reader = new FileReader();
          //   reader.readAsText(res?.data, "utf-8");
          //   reader.onload = function () {
          //     console.log("======", reader);
          //     const readerRes = reader.result;
          //     const resObj = JSON.parse(readerRes);
          //     Vue.prototype.$toast({
          //       content: resObj.msg,
          //       type: "error",
          //     });
          //   };

          //   return;
          // }
          const content = res.data
          let pdfUrl = window.URL.createObjectURL(
            new Blob([content], { type: 'text/html;charset=utf-8' })
          )

          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
      }
    },
    handleClickToolBar3(e) {
      if (e.toolbar.id === 'export1') {
        let rule = this.$refs['template-2'].getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let obj = JSON.parse(
          sessionStorage.getItem('0a524210-912f-4259-b21c-d7d930b71951')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnData3.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        let params = {
          page: { current: 1, size: 10000 },
          rules: rule.rules || []
        }
        params.rules = [
          ...params.rules,
          {
            field: 'orderType',
            operator: 'equal',
            value: this.$route.path.includes('gc') ? 1 : 0
          }
        ]
        this.$store.commit('startLoading')
        this.$API.outsourcing.outNewAllocationOrderBuyerExport(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
    }
  }
  // 消失
  // deactivated() {
  //   localStorage.removeItem("deliverDetailSupplier");
  // },
}
</script>

<style></style>
