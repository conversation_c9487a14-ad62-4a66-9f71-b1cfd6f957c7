import { timeNumberToDate } from '@/utils/utils'
import Vue from 'vue'
import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '165',
    field: 'allocationOrderCode',
    headerText: i18n.t('委外调拨单号'),
    cellTools: []
  },
  {
    width: '95',
    field: 'isOutSale',
    headerText: i18n.t('销售委外'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },
  {
    width: '120',
    field: 'status',
    headerText: i18n.t('状态'),
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待供应商确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('调入供应商退回'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('调入供应商确认'), cssClass: 'col-inactive' },
        { value: 4, text: i18n.t('采购退回'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('采购确认'), cssClass: 'col-inactive' },
        { value: 6, text: i18n.t('已完结'), cssClass: 'col-inactive' },
        { value: 7, text: i18n.t('已取消'), cssClass: 'col-inactive' }
      ]
    }
    // cellTools: [
    //   {
    //     id: "Proper",
    //     icon: "icon_table_accept1",
    //     title: i18n.t("确认"),
    //     // permission: ["O_02_0605"],
    //     visibleCondition: (data) => data["status"] === 1,
    //   },
    //   {
    //     id: "Proper",
    //     icon: "icon_table_recall",
    //     title: i18n.t("退回"),
    //     // permission: ["O_02_0606"],
    //     visibleCondition: (data) => data["status"] === 1,
    //   },
    //   {
    //     id: "restart",
    //     icon: "icon_table_restart",
    //     title: i18n.t("手工同步"),
    //     // permission: ["O_02_0687"],
    //     visibleCondition: (data) => data["status"] === 3,
    //   },
    // ],
  },
  {
    width: '110',
    field: 'buyerApproveUserName',
    headerText: i18n.t('采方确认人')
  },
  {
    width: '125',
    field: 'buyerApproveDate',
    headerText: i18n.t('采方确认时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    width: '110',
    field: 'buyerCancelUserName',
    headerText: i18n.t('取消人')
  },
  {
    width: '125',
    field: 'buyerCancelDate',
    headerText: i18n.t('取消时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    width: '110',
    field: 'inApproveUserName',
    headerText: i18n.t('供方确认人')
  },
  {
    width: '125',
    field: 'inApproveDate',
    headerText: i18n.t('供方确认时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  // {
  //   field: "syncStatus",
  //   headerText: i18n.t("同步状态"),
  //   valueConverter: {
  //     type: "map",
  //     map: [
  //       { value: "0", text: i18n.t("未同步"), cssClass: "" },
  //       { value: "1", text: i18n.t("同步"), cssClass: "" },
  //       { value: "2", text: i18n.t("同步失败"), cssClass: "" },
  //     ],
  //   },
  // },
  {
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: '0', text: i18n.t('未同步'), cssClass: '' },
        { value: '1', text: i18n.t('同步成功'), cssClass: '' },
        { value: '2', text: i18n.t('同步失败'), cssClass: '' },
        { value: '3', text: i18n.t('同步中'), cssClass: '' }
      ]
    }
  },
  {
    field: 'syncMsg',
    headerText: i18n.t('同步消息')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '255',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerOrgCode',
    headerText: i18n.t('公司'),
    width: '230',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  // {
  //   width: "150",
  //   field: "supplierCode",
  //   headerText: i18n.t("领料供应商编码"),
  // },
  {
    width: '185',
    field: 'outSupplierCode',
    headerText: i18n.t('调出供应商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.outSupplierCode}}-{{data.outSupplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '250',
    field: 'inSupplierCode',
    headerText: i18n.t('调入供应商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.inSupplierCode}}-{{data.inSupplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'inSupplierAddressName',
    headerText: i18n.t('送货地址')
    // editTemplate: () => {
    //   return {
    //     template: "1212",
    //   };
    // },
    // template: timeDate("sendTime", true),
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('制单日期')
    // editTemplate: () => {
    //   return {
    //     template: "1212",
    //   };
    // },
    // template: timeDate("sendTime", true),
  },
  {
    width: '85',
    field: 'createUserName',
    headerText: i18n.t('制单人')
  }
]
export const columnData2 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '160',
    field: 'allocationOrderCode',
    headerText: i18n.t('委外调拨单号'),
    cellTools: []
  },
  {
    width: '95',
    field: 'isOutSale',
    headerText: i18n.t('销售委外'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },
  {
    width: '65',
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '105',
    field: 'status',
    headerText: i18n.t('状态'),
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待供应商确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('调入供应商退回'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('调入供应商确认'), cssClass: 'col-inactive' },
        { value: 4, text: i18n.t('采购退回'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('采购确认'), cssClass: 'col-inactive' },
        { value: 6, text: i18n.t('已完结'), cssClass: 'col-inactive' },
        { value: 7, text: i18n.t('已取消'), cssClass: 'col-inactive' }
      ]
    }
    // cellTools: [
    //   {
    //     id: "Proper",
    //     icon: "icon_table_accept1",
    //     title: i18n.t("确认"),
    //     // permission: ["O_02_0605"],
    //     visibleCondition: (data) => data["status"] === 1,
    //   },
    //   {
    //     id: "Proper",
    //     icon: "icon_table_recall",
    //     title: i18n.t("退回"),
    //     // permission: ["O_02_0606"],
    //     visibleCondition: (data) => data["status"] === 1,
    //   },
    //   {
    //     id: "restart",
    //     icon: "icon_table_restart",
    //     title: i18n.t("手工同步"),
    //     // permission: ["O_02_0687"],
    //     visibleCondition: (data) => data["status"] === 3,
    //   },
    // ],
  },
  {
    width: '105',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '430',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '100',
    field: 'allocationQuantity',
    headerText: i18n.t('调出数量')
  },
  {
    width: '100',
    field: 'wmsAllocationQuantity',
    headerText: i18n.t('实收数量'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `
        <div>
          <mt-input
          id="wmsAllocationQuantity"
          v-model="data.wmsAllocationQuantity"
          @change="change"
        ></mt-input>
        </div>
          `,
          data: function () {
            return {
              data: {}
            }
          },
          mounted() {},
          methods: {
            change(e) {
              this.data.wmsAllocationQuantity = e
              this.$parent.$emit(`cellEdit`, {
                data: this.data,
                type: 'text',
                status: true
              })
            }
          }
        })
      }
    }
  },

  {
    width: '65',
    field: 'basicUnitName',
    headerText: i18n.t('单位'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.basicUnitCode}}-{{data.basicUnitName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '105',
    field: 'batch',
    headerText: i18n.t('批次/卷号')
  },
  {
    width: '100',
    field: 'remark',
    headerText: i18n.t('行备注')
  },
  // {
  //   field: "buyerApproveUserName",
  //   headerText: i18n.t("采方确认人"),
  // },
  // {
  //   field: "buyerApproveDate",
  //   headerText: i18n.t("采方确认时间"),
  //   valueConverter: {
  //     type: "function",
  //     filter: (e) => {
  //       if (e && !isNaN(e) && e.length == 13) {
  //         e = Number(e);
  //         return timeNumberToDate({
  //           formatString: "YYYY-mm-dd",
  //           value: e,
  //         });
  //       } else {
  //         return "";
  //       }
  //     },
  //   },
  // },
  // {
  //   field: "syncStatus",
  //   headerText: i18n.t("同步状态"),
  //   valueConverter: {
  //     type: "map",
  //     map: [
  //       { value: "0", text: i18n.t("未同步"), cssClass: "" },
  //       { value: "1", text: i18n.t("同步"), cssClass: "" },
  //       { value: "2", text: i18n.t("同步失败"), cssClass: "" },
  //     ],
  //   },
  // },

  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '255',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerOrgName',
    headerText: i18n.t('公司'),
    width: '230',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  // {
  //   width: "150",
  //   field: "supplierCode",
  //   headerText: i18n.t("领料供应商编码"),
  // },
  {
    width: '250',
    field: 'outSupplierCode',
    headerText: i18n.t('调出供应商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.outSupplierCode}}-{{data.outSupplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '250',
    field: 'inSupplierCode',
    headerText: i18n.t('调入供应商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.inSupplierCode}}-{{data.inSupplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'inSupplierAddressName',
    headerText: i18n.t('送货地址')
    // editTemplate: () => {
    //   return {
    //     template: "1212",
    //   };
    // },
    // template: timeDate("sendTime", true),
  },
  {
    width: '145',
    field: 'createTime',
    headerText: i18n.t('制单日期')
    // editTemplate: () => {
    //   return {
    //     template: "1212",
    //   };
    // },
    // template: timeDate("sendTime", true),
  },
  {
    width: '85',
    field: 'createUserName',
    headerText: i18n.t('制单人')
  }
]
export const columnData3 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '160',
    field: 'allocationOrderCode',
    headerText: i18n.t('委外调拨单号'),
    cellTools: []
  },
  {
    width: '95',
    field: 'isOutSale',
    headerText: i18n.t('销售委外'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },
  {
    width: '65',
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '105',
    field: 'status',
    headerText: i18n.t('状态'),
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待供应商确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('调入供应商退回'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('调入供应商确认'), cssClass: 'col-inactive' },
        { value: 4, text: i18n.t('采购退回'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('采购确认'), cssClass: 'col-inactive' },
        { value: 6, text: i18n.t('已完结'), cssClass: 'col-inactive' },
        { value: 7, text: i18n.t('已取消'), cssClass: 'col-inactive' }
      ]
    }
    // cellTools: [
    //   {
    //     id: "Proper",
    //     icon: "icon_table_accept1",
    //     title: i18n.t("确认"),
    //     // permission: ["O_02_0605"],
    //     visibleCondition: (data) => data["status"] === 1,
    //   },
    //   {
    //     id: "Proper",
    //     icon: "icon_table_recall",
    //     title: i18n.t("退回"),
    //     // permission: ["O_02_0606"],
    //     visibleCondition: (data) => data["status"] === 1,
    //   },
    //   {
    //     id: "restart",
    //     icon: "icon_table_restart",
    //     title: i18n.t("手工同步"),
    //     // permission: ["O_02_0687"],
    //     visibleCondition: (data) => data["status"] === 3,
    //   },
    // ],
  },
  {
    width: '105',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '430',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '100',
    field: 'allocationQuantity',
    headerText: i18n.t('调出数量')
  },
  {
    width: '100',
    field: 'wmsAllocationQuantity',
    headerText: i18n.t('实收数量')
  },

  {
    width: '65',
    field: 'basicUnitName',
    headerText: i18n.t('单位'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.basicUnitCode}}-{{data.basicUnitName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '105',
    field: 'batch',
    headerText: i18n.t('批次/卷号')
  },
  {
    width: '100',
    field: 'remark',
    headerText: i18n.t('行备注')
  },
  // {
  //   field: "buyerApproveUserName",
  //   headerText: i18n.t("采方确认人"),
  // },
  // {
  //   field: "buyerApproveDate",
  //   headerText: i18n.t("采方确认时间"),
  //   valueConverter: {
  //     type: "function",
  //     filter: (e) => {
  //       if (e && !isNaN(e) && e.length == 13) {
  //         e = Number(e);
  //         return timeNumberToDate({
  //           formatString: "YYYY-mm-dd",
  //           value: e,
  //         });
  //       } else {
  //         return "";
  //       }
  //     },
  //   },
  // },
  // {
  //   field: "syncStatus",
  //   headerText: i18n.t("同步状态"),
  //   valueConverter: {
  //     type: "map",
  //     map: [
  //       { value: "0", text: i18n.t("未同步"), cssClass: "" },
  //       { value: "1", text: i18n.t("同步"), cssClass: "" },
  //       { value: "2", text: i18n.t("同步失败"), cssClass: "" },
  //     ],
  //   },
  // },

  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    width: '255',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerOrgName',
    headerText: i18n.t('公司'),
    width: '230',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  // {
  //   width: "150",
  //   field: "supplierCode",
  //   headerText: i18n.t("领料供应商编码"),
  // },
  {
    width: '250',
    field: 'outSupplierCode',
    headerText: i18n.t('调出供应商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.outSupplierCode}}-{{data.outSupplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '250',
    field: 'inSupplierCode',
    headerText: i18n.t('调入供应商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.inSupplierCode}}-{{data.inSupplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'inSupplierAddressName',
    headerText: i18n.t('送货地址')
    // editTemplate: () => {
    //   return {
    //     template: "1212",
    //   };
    // },
    // template: timeDate("sendTime", true),
  },
  {
    width: '145',
    field: 'createTime',
    headerText: i18n.t('制单日期')
    // editTemplate: () => {
    //   return {
    //     template: "1212",
    //   };
    // },
    // template: timeDate("sendTime", true),
  },
  {
    width: '85',
    field: 'createUserName',
    headerText: i18n.t('制单人')
  }
]
