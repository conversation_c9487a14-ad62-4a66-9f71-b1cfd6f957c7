<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="this.$t('退回原因')"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="rules" :model="addForm" :rules="rules">
      <mt-form-item prop="allocationReason" :label="this.$t('退回原因')" class="full-width">
        <mt-input
          v-model="addForm.allocationReason"
          :show-clear-button="true"
          :placeholder="this.$t('请输入退回原因')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
// import common from "@/utils/constant";
// import { formatDate, formatRules } from "@/utils/util";
export default {
  props: {
    // dialogData: {
    //   type: Object,
    //   default: () => {},
    // },
  },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        allocationReason: ''
      },
      rules: {
        allocationReason: [
          {
            required: true,
            message: this.$t('请输入退回原因'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },

  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    confirm() {
      this.$refs.rules.validate((valid) => {
        if (valid) {
          this.$emit('resolveClick', this.addForm.allocationReason)
          this.addForm.allocationReason = ''
          // console.log(this.addForm);
          // let _request = this.dialogData?.requestUrl;
          // console.log(this.dialogData, _request);
          // this.$API.baseMainData[_request](params).then((res) => {
          //   if (res.code == 200) {
          //     this.$toast({ content: this.$t("操作成功"), type: "success" });
          //     this.$emit("handleAddDialogShow", false);
          //     this.$emit("confirmSuccess");
          //   }
          // });
        }
      })
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    }
  }
}
</script>

<style></style>
