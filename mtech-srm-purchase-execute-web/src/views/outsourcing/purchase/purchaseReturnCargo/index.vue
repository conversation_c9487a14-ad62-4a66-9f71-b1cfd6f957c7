<template>
  <div class="full-height pt20">
    <mt-template-page
      :permission-obj="permissionObj"
      :template-config="pageConfig"
      @handleSelectTab="handleSelectTab"
    >
      <mt-template-page
        slot="slot-0"
        ref="templateRef"
        :template-config="pageConfig1"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTitle="handleClickCellTitle"
        @handleClickCellTool="handleClickCellTool"
      >
      </mt-template-page>

      <mt-template-page slot="slot-1" ref="template-1" :template-config="pageConfig2">
      </mt-template-page>
    </mt-template-page>
    <deliver
      @handleAddDialogShow="handleAddDialogShow"
      @resolveClick="resolveClick"
      ref="headerTop"
      v-if="deliveryShow"
      class="flex-keep"
    ></deliver>
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { columnData, columnData2 } from './config/index.js'
// import deliveryDialog from "./components/deliveryDialog";
export default {
  mounted() {},
  components: {
    deliver: require('./components/deliver.vue').default
  },
  data() {
    return {
      id: [],
      deliveryShow: false,
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0038' },
          { dataPermission: 'b', permissionCode: 'T_02_0039' }
        ]
      },
      pageConfig: [
        {
          title: this.$t('头视图'),
          dataPermission: 'a',
          permissionCode: 'T_02_0038'
        },
        {
          title: this.$t('明细视图'),
          dataPermission: 'b',
          permissionCode: 'T_02_0039'
        }
      ],
      userInfo: null,
      addDialogShow: false,
      currentTabIndex: 0,
      pageConfig1: [
        {
          useToolTemplate: false,
          toolbar: {
            tools: [
              [
                // {
                //   id: "accept1",
                //   icon: "icon_table_accept1",
                //   title: this.$t("确认"),
                //   // permission: ["O_02_0122"],
                // },
                // {
                //   id: "recall",
                //   icon: "icon_table_recall",
                //   title: this.$t("退回"),
                //   // permission: ["O_02_0122"],
                // },
                {
                  id: 'restart',
                  icon: 'icon_table_restart',
                  title: this.$t('手工同步'),
                  permission: ['O_02_0688']
                },
                {
                  id: 'accept1',
                  icon: 'icon_table_accept1',
                  title: this.$t('确认'),
                  permission: ['O_02_0607']
                },
                {
                  id: 'recall',
                  icon: 'icon_table_recall',
                  title: this.$t('退回'),
                  permission: ['O_02_0608']
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          gridId: this.$tableUUID.outsourcing.purchaseReturnCargo.list,

          grid: {
            columnData: columnData,
            lineIndex: 1,
            frozenColumns: 1,

            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/outCancelOrder/buyer/queryView`
            }
            // frozenColumns: 1,
          }
        }
      ],
      pageConfig2: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          gridId: this.$tableUUID.outsourcing.purchaseReturnCargo.details,

          grid: {
            columnData: columnData2,
            lineIndex: 1,
            frozenColumns: 1,

            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/outCancelOrder/buyer/queryDetailView`
            }
            // frozenColumns: 1,
          }
        }
      ],
      dialogData: null
    }
  },
  methods: {
    resolveClick(a) {
      this.resolvechange(this.id, a)
      this.deliveryShow = false
    },
    resolvechange(id, a) {
      let obj = {
        ids: id.toString(),
        rejectReason: a
      }
      this.$API.outsourcing
        .OrderBuyerReject(obj)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef.refreshCurrentGridData()
        })
    },
    restartClick(id) {
      console.log(id)
      let pd = id.toString()
      let obj = { ids: pd }
      this.$API.outsourcing.cancelBuyerSyncWms(obj).then(() => {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    // 确认
    certain(id) {
      let obj = {
        ids: id.toString()
      }
      this.$API.outsourcing
        .OrderBuyerConfirm(obj)
        .then((res) => {
          this.$store.commit('startLoading')
          if (res.code == 200) {
            this.$store.commit('endLoading')

            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          this.$refs.templateRef.refreshCurrentGridData()
        })
    },
    // 弹框
    handleDialogShow(flag) {
      this.deliveryShow = flag
    },
    handleSelectTab(e) {
      this.currentTabIndex = e
    },
    // 点击单元格 标题
    handleClickCellTitle(e) {
      console.log(e)
      if (e.field === 'cancelOrderCode') {
        localStorage.setItem('purchaseListScope', JSON.stringify(e.data))
        this.$router.push(`purchase-detail-returnCargo?cancelOrderCode=${e.data.cancelOrderCode}`)
      }
    },
    handleClickCellTool(e) {
      console.log(e)
      if (e.tool.title === this.$t('取消')) {
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消？')
          },
          success: () => {}
        })
      }
      if (e.tool.title === this.$t('手工同步')) {
        this.restartClick(e.data.id)
      }
    },
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    handleClickToolBar(e) {
      console.log(e)
      if (e.toolbar.id == 'new') {
        this.$router.push(`create-noOrder-supplier`)
        return
      }
      if (e.gridRef.getMtechGridRecords()?.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _status = [],
        _id = []
      e.gridRef.getMtechGridRecords().map((item) => {
        _id.push(item.id), _status.push(item.status)

        // _sourceStatus.push(item.sourceStatus),
        // _remainingQuantity.push(item.remainingQuantity);
      })
      this.id = _id
      console.log(_status)
      if (e.toolbar.id == 'accept1') {
        for (let i of _status) {
          if (i !== 1 && i !== 2 && i !== 3) {
            this.$toast({
              content: this.$t('当前状态不可操作'),
              type: 'warning'
            })
            return
          }
        }
        this.certain(_id)
        return
      }
      if (e.toolbar.id == 'restart') {
        console.log(_status)
        // for (let i of _status) {
        //   if (i !== 5 && i !== 7) {
        //     this.$toast({
        //       content: this.$t("该状态不可手工同步"),
        //       type: "warning",
        //     });
        //     return;
        //   }
        // }
        this.restartClick(_id)
      }
      if (e.toolbar.id == 'recall') {
        for (let i of _status) {
          if (i !== 1 && i !== 2 && i !== 3) {
            console.log(_status)
            this.$toast({
              content: this.$t('当前状态不可操作'),
              type: 'warning'
            })
            return
          }
        }
        this.deliveryShow = true
      }
    }
  }
  // 消失
  // beforeDestroy() {
  //   localStorage.removeItem("deliverDetailSupplier");
  // },
}
</script>

<style></style>
