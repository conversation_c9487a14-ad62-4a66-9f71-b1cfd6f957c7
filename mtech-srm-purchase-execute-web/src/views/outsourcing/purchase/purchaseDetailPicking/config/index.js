import { i18n } from '@/main.js'
import Vue from 'vue'
export const columnData = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },
  {
    field: 'receiveQuantity',
    headerText: i18n.t('本次领料数量'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div><mt-input-number precision="3" :min="0" v-if='type.status === 1' v-model="data.receiveQuantity"  cssClass="e-outline" :show-clear-button="false" @blur="handleChange"></mt-input-number>
          <div v-else>{{data.receiveQuantity}}</div>
          </div> `,
          data: function () {
            return {
              data: {},
              type: JSON.parse(localStorage.getItem('purchaseListScope'))
            }
          },
          mounted() {
            // alert(this.data.receiveQuantity);
          },
          methods: {
            handleChange() {
              this.$parent.$emit('cellEdit', {
                index: Number(this.data.index),
                key: 'receiveQuantity',
                value: this.data
              })
            }
          }
        })
      }
    }
  },
  {
    // width: "150",
    field: 'maxReceiveQuantity',
    headerText: i18n.t('创建领料单最大数量')
  },
  {
    width: '150',
    field: 'maxDemandQuantity',
    headerText: i18n.t('最大需求可创建数量')
  },
  {
    width: '150',
    field: 'packageMinQuantity',
    headerText: i18n.t('最小包装量')
  },
  // {
  //   width: "150",
  //   field: "buyerOrgName",
  //   headerText: i18n.t("采购组"),
  // },
  {
    width: '150',
    field: 'stockUnit',
    headerText: i18n.t('库存单位')
  },
  // {
  //   width: "150",
  //   field: "warehouseCode",
  //   headerText: i18n.t("发料仓位编码"),
  // },
  {
    width: '150',
    field: 'demandStartDate',
    headerText: i18n.t('需求日期')
  },
  {
    field: 'warehouseName',
    headerText: i18n.t('库存地点'),
    width: 200,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.warehouseName}}{{data.warehouseCode}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('关联采购订单号')
  },
  {
    width: '150',
    field: 'orderItemNo',
    headerText: i18n.t('关联采购订单行号') // 暂无
  },
  {
    field: 'wmsReceiveStatus',
    headerText: i18n.t('发料状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('未发货'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('全部发货'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('部分发货'), cssClass: 'col-active' }
      ]
    }
  },

  {
    field: 'wmsReceiveQuantity',
    headerText: i18n.t('实发数量')
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('行备注')
  }
]
export const serializeList = (list) => {
  if (list.length > 0) {
    let serialNumber = 1
    list.forEach((item) => {
      // 添加序号
      item.serialNumber = serialNumber++
    })
  }
  return list
}
