import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import Vue from 'vue'
import { i18n } from '@/main.js'
export const timeDate = (dataKey, hasTime) => {
  // const { dataKey, hasTime } = args;
  // console.log(args);
  const template = () => {
    return {
      template: Vue.component('date', {
        template: `<div><div>{{data[dataKey] | dateFormat}}</div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div></div>`,
        data: function () {
          return { data: {}, dataKey, hasTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '280',
    field: 'deliveryCode',
    headerText: i18n.t('送货单号'),
    cellTools: []
  },
  {
    width: '150',
    field: 'deliveryType',
    headerText: i18n.t('类型'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('关联采购订单'), cssClass: '' },
        { value: 2, text: i18n.t('无采购订单'), cssClass: '' }
      ]
    }
  },
  {
    width: '200',
    field: 'status',
    headerText: i18n.t('状态'),
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { value: 2, text: i18n.t('发货中'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('已完成'), cssClass: 'col-inactive' },
        { value: 4, text: i18n.t('已取消'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('已关闭'), cssClass: 'col-inactive' },
        { value: 99, text: i18n.t('已妥投'), cssClass: 'col-active' } // 妥投 TODO 演示后删掉
      ]
    },
    cellTools: [
      {
        id: 'active',
        icon: '',
        title: i18n.t('维护物流信息'),
        // permission: ["O_02_0125"],
        visibleCondition: (data) => data['status'] === 2
      },
      {
        id: 'inactive',
        icon: '',
        title: i18n.t('取消'),
        // permission: ["O_02_0126"],
        visibleCondition: (data) => data['status'] === 2
      },
      // 妥投 TODO 演示后删掉
      {
        id: 'Proper',
        icon: '',
        title: i18n.t('妥投'),
        // permission: ["O_02_0126"],
        visibleCondition: (data) => data['status'] === 2
      }
    ]
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商')
  },
  {
    width: '150',
    field: 'sendAddressName',
    headerText: i18n.t('发货地点')
  },
  {
    width: '150',
    field: 'warehouseName',
    headerText: i18n.t('交货库存地点')
  },
  {
    width: '150',
    field: 'sendTime',
    headerText: i18n.t('发货日期'),
    // editTemplate: () => {
    //   return {
    //     template: "1212",
    //   };
    // },
    template: timeDate('sendTime', true)
  },
  {
    width: '150',
    field: 'forecastArriveTime',
    headerText: i18n.t('预计到货日期'),
    template: timeDate('forecastArriveTime', true)
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
export const columnData2 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'deliveryType',
    headerText: i18n.t('交货方式'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('采购订单'), cssClass: '' },
        { value: 2, text: i18n.t('交货计划'), cssClass: '' },
        { value: 3, text: i18n.t('JIT'), cssClass: '' },
        { value: 4, text: i18n.t('无需求'), cssClass: '' }
      ]
    }
  },
  {
    width: '150',
    field: '121323123',
    headerText: i18n.t('版本号') //暂时没有
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('发货中'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('已完成'), cssClass: 'col-inactive' },
        { value: 4, text: i18n.t('已取消'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('已关闭'), cssClass: 'col-inactive' },
        { value: 6, text: i18n.t('部分收货'), cssClass: 'col-inactive' }
      ]
    }
    // cellTools: [
    //   {
    //     id: "active",
    //     icon: "icon_Editor",
    //     title: i18n.t("启用"),
    //     permission: ["O_02_0125"],
    //     visibleCondition: (data) => data["status"] == 0,
    //   },
    //   {
    //     id: "inactive",
    //     icon: "icon_Editor",
    //     title: i18n.t("停用"),
    //     permission: ["O_02_0126"],
    //     visibleCondition: (data) => data["status"] == 1,
    //   },
    // ],
  },
  {
    width: '150',
    field: 'transferPlanName',
    headerText: i18n.t('计划员')
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('工厂')
  },
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    width: '150',
    field: 'receiveSupplierName',
    headerText: i18n.t('供应商')
  },
  {
    width: '150',
    field: 'unitName',
    headerText: i18n.t('单位')
  },
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('采购订单号')
  },
  {
    width: '150',
    field: 'lineNo',
    headerText: i18n.t('采购订单行号')
  },
  {
    width: '150',
    field: 'workOrderNo',
    headerText: i18n.t('关联工单号')
  },
  {
    width: '150',
    field: 'saleOrderNo',
    headerText: i18n.t('关联销售订单号')
  },
  {
    width: '150',
    field: 'saleOrderLineNo',
    headerText: i18n.t('关联销售订单行号')
  },
  {
    width: '150',
    field: 'bomCode',
    headerText: i18n.t('BOM号')
  },
  {
    width: '150',
    field: 'productCode',
    headerText: i18n.t('关联产品代码')
  },
  {
    width: '150',
    field: 'workCenterName',
    headerText: i18n.t('工作中心')
  },
  {
    width: '150',
    field: 'processName',
    headerText: i18n.t('工序名称')
  },
  // {
  //   width: "150",
  //   field: "receiveSupplierName",
  //   headerText: i18n.t("送货方式"), 暂无
  // },
  // {
  //   width: "150",
  //   field: "receiveSupplierName",
  //   headerText: i18n.t("收货方名称"),
  // },
  // {
  //   width: "150",
  //   field: "receiveSupplierCode",
  //   headerText: i18n.t("收货方编号"),
  // },
  {
    width: '150',
    field: 'receiveAddressName',
    headerText: i18n.t('送货地址')
  },
  {
    width: '150',
    field: '2',
    headerText: i18n.t('批次管理')
  },
  {
    width: '150',
    field: '2',
    headerText: i18n.t('下达')
  },
  {
    width: '150',
    field: 'limitQuantity',
    headerText: i18n.t('限量数量')
  },
  {
    width: '150',
    field: 'warehouseClerkName',
    headerText: i18n.t('仓管员')
  },
  {
    width: '150',
    field: 'dispatcherName',
    headerText: i18n.t('调度员')
  },
  {
    width: '150',
    field: 'demandDate',
    headerText: i18n.t('需求日期')
    // template: timeDate("demandDate", true),
  },
  {
    width: '150',
    field: 'demandTime',
    headerText: i18n.t('需求时间')
    // template: timeDate("demandTime", false),
  },
  {
    width: '150',
    field: 'deliveryQuantity',
    headerText: i18n.t('本次送货数量')
  },
  {
    width: '150',
    field: 'receiveQuantity',
    headerText: i18n.t('收货数量')
  },
  {
    width: '150',
    field: 'rejectQuantity',
    headerText: i18n.t('拒绝数量')
  },
  {
    width: '150',
    field: 'rejectReason',
    headerText: i18n.t('拒绝原因')
  },
  // FIXME: 暂时不展示
  // {
  //   width: "150",
  //   field: "inventoryQuantity",
  //   headerText: i18n.t("入库数量"),
  // },
  // {
  //   width: "150",
  //   field: "inventoryTime",
  //   headerText: i18n.t("入库日期"),
  //   template: timeDate("inventoryTime", true),
  // },
  {
    width: '150',
    field: 'receiveSupplierName',
    headerText: i18n.t('收货供应商')
  },
  {
    width: '150',
    field: 'cancelPersonName',
    headerText: i18n.t('取消人')
  },
  {
    width: '150',
    field: 'cancelTime',
    headerText: i18n.t('取消时间'),
    template: timeDate('cancelTime', true)
  },
  {
    width: '150',
    field: 'collectorMark',
    headerText: i18n.t('代收标识')
  },
  {
    width: '150',
    field: 'collectorName',
    headerText: i18n.t('代收人')
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('行备注')
  }
]
