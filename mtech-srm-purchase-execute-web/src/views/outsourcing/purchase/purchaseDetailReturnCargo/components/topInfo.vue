<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 测试用字段prSubmitFlag:{{ prSubmitFlag }} -->
    <div class="header-box" v-if="headerInfo">
      <!-- 左侧的信息 -->
      <div class="status mr20">
        {{ typeInfo(headerInfo.status) }}
      </div>
      <div class="infos mr20">{{ $t('创建人：') }}{{ headerInfo.createUserName }}</div>
      <div class="infos">{{ $t('创建日期：') }}{{ headerInfo.createTime }}</div>
      <div class="infos mr20">{{ $t('单据编号：') }}{{ headerInfo.cancelOrderCode }}</div>
      <div class="infos mr20" v-if="headerInfo.buyerApproveUserName">
        {{ $t('采方确认人') }}：{{ headerInfo.buyerApproveUserName }}
      </div>
      <div class="infos mr20" v-if="headerInfo.buyerApproveUserName">
        {{ $t('采方确认时间') }}：{{ dateFormat(headerInfo.buyerApproveDate) }}
      </div>
      <div class="infos mr20" v-if="headerInfo.materialApproveUserName">
        {{ $t('供方确认人') }}：{{ headerInfo.materialApproveUserName }}
      </div>
      <div class="infos mr20" v-if="headerInfo.materialApproveUserName">
        {{ $t('供方确认时间') }}：{{ dateFormat(headerInfo.materialApproveDate) }}
      </div>
      <div class="middle-blank"></div>

      <!-- 右侧各种操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button
        css-class="e-flat"
        v-permission="['O_02_0607']"
        v-if="headerInfo.status === 3 || headerInfo.status === 2 || headerInfo.status === 1"
        :is-primary="true"
        @click="resolveclick"
        >{{ $t('确认') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-permission="['O_02_0608']"
        v-if="headerInfo.status === 3 || headerInfo.status === 2 || headerInfo.status === 1"
        @click="rejectclick"
        >{{ $t('退回') }}</mt-button
      >
      <div class="sort-box" @click="isExpand = !isExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="addForm" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="businessTypeId" :label="$t('公司')">
          <mt-input
            v-model="company"
            :show-clear-button="false"
            :disabled="true"
            :placeholder="$t('公司')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="outsourcedType" :label="$t('委外方式')">
          <mt-radio
            v-model="headerInfo.outsourcedType"
            :disabled="true"
            :data-source="outsourcedTypeData"
          ></mt-radio>
        </mt-form-item>
        <mt-form-item prop="businessTypeId" :label="$t('工厂')">
          <mt-input
            v-model="site"
            :show-clear-button="false"
            :disabled="true"
            :placeholder="$t('工厂')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="orderCode" :label="$t('采购订单号')">
          <mt-input disabled type="text" v-model="headerInfo.orderCode"></mt-input>
        </mt-form-item>
        <mt-form-item prop="orderCode" :label="$t('采购订单行号')">
          <mt-input disabled type="text" v-model="headerInfo.orderItemNo"></mt-input>
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')">
          <mt-input v-model="headerInfo.itemCode" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')">
          <mt-input v-model="headerInfo.itemName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="businessTypeId" :label="$t('加工供应商')">
          <mt-input v-model="supplier" :disabled="true"></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="title" :label="$t('加工供应商描述')">
          <mt-input
            v-model="headerInfo.supplierName"
            :disabled="true"
            :maxlength="50"
            :placeholder="$t('加工供应商描述')"
          ></mt-input>
        </mt-form-item> -->
        <mt-form-item prop="title" :label="$t('退货处理方式')">
          <mt-radio
            v-model="headerInfo.cancelType"
            :disabled="true"
            :data-source="cancelData"
          ></mt-radio>
        </mt-form-item>
        <mt-form-item prop="title" :label="$t('原材料供应商')">
          <mt-input v-model="materialSupplier" :disabled="true" :maxlength="50"></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="title" :label="$t('原材料供应商描述')">
          <mt-input
            v-model="headerInfo.materialSupplierName"
            :disabled="true"
            :maxlength="50"
            :placeholder="$t('原材料供应商描述')"
          ></mt-input>
        </mt-form-item> -->

        <mt-form-item prop="title" :label="$t('退货地址')">
          <mt-input
            v-model="headerInfo.cancelAddress"
            :disabled="true"
            :maxlength="50"
            :placeholder="$t('退货地址')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('退货工厂')">
          <debounce-filter-select
            v-model="headerInfo.cancelSiteCode"
            :is-active="true"
            :value-template="siteCodeValueTemplate"
            :fields="{ text: 'theCodeName', value: 'siteCode' }"
            :data-source="siteOptions"
            :request="postSiteFuzzyQuery"
            :disabled="
              headerInfo.status !== 3 && headerInfo.status !== 2 && headerInfo.status !== 1
            "
            @change="siteOrgClick"
            :allow-filtering="true"
          ></debounce-filter-select>
        </mt-form-item>

        <mt-form-item prop="businessTypeId" :label="$t('是否直退材料商')">
          <mt-input
            v-model="headerInfo.isOutDirect"
            :disabled="true"
            :placeholder="$t('是否直退材料商')"
          ></mt-input>
        </mt-form-item>

        <!-- 暂无 -->
        <!-- <mt-form-item prop="title" :label="$t('送货地址')">
          <mt-input
            v-model="headerInfo.title"
            :disabled="true"
            :maxlength="50"
            :placeholder="$t('送货地址')"
          ></mt-input>
        </mt-form-item> -->
        <!-- <mt-form-item prop="title" :label="$t('发货日期')">
          <mt-date-time-picker
            v-model="headerInfo.sendTime"
            :disabled="true"
            :placeholder="$t('选择日期和时间')"
          ></mt-date-time-picker>
        </mt-form-item>
        <mt-form-item prop="title" :label="$t('预计到货日期')">
          <mt-date-time-picker
            :disabled="true"
            v-model="headerInfo.forecastArriveTime"
            :placeholder="$t('选择日期和时间')"
          ></mt-date-time-picker>
        </mt-form-item> -->
        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input
            :multiline="true"
            v-model="headerInfo.remark"
            :placeholder="$t('备注')"
            :maxlength="200"
            :disabled="true"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { cloneDeep } from 'lodash'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'

export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName',
        cssClass: 'a'
      }), // 工厂
      cancelData: [
        {
          label: this.$t('良品退货'),
          value: '0'
        },
        {
          label: this.$t('不良品退货'),
          value: '1'
        }
      ],
      outsourcedTypeData: [
        {
          label: this.$t('标准委外'),
          value: '0'
        },
        {
          label: this.$t('销售委外'),
          value: '1'
        },
        {
          label: this.$t('工序委外'),
          value: '3'
        }
      ],
      siteOptions: [],
      isInit: true, // 是第一次赋值
      isExpand: true,
      type: '', // add view edit

      businessTypeList: [], // 业务类型
      applyUserIdData: [], // 申请人列表
      organizationData: {},
      applyCompanyData: [], // 公司
      applyDepartData: [], // 部门
      supplier: '',
      materialSupplier: '',
      company: '',
      returnSite: '',
      site: '',
      addForm: {
        title: '', // 采购申请名称
        businessTypeId: '', // 业务类型
        // businessTypeCode: "",
        // businessTypeName: "",
        applyUserId: '', // 申请人
        // applyUserName: "",
        // applyUserCode: ""
        companyId: '', // 公司
        // companyName: "",
        // companyCode: ""
        applyDepId: '', // 申请部门
        // applyDepCode: "",
        // applyDepName: "",
        remark: '',
        projectDesc: '' // 项目名称的聚合
      },
      rules: {
        businessTypeId: [
          {
            required: true,
            message: this.$t('请选择业务类型'),
            trigger: 'blur'
          }
        ],
        applyUserId: [{ required: true, message: this.$t('请选择申请人'), trigger: 'blur' }],
        companyId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        applyDepId: [
          {
            required: true,
            message: this.$t('请选择申请部门'),
            trigger: 'blur'
          }
        ]
      },
      isClickCancel: false //手动点击了取消按钮， 为了不再次触发change事件
    }
  },

  computed: {},

  watch: {
    headerInfo() {
      this.headerInfo.outsourcedType =
        this.headerInfo?.outsourcedType || this.headerInfo.outsourcedType === 0
          ? Number(this.headerInfo.outsourcedType)
          : ''
      this.headerInfo.isOutDirect =
        this.headerInfo.isOutDirect === 1 ? this.$t('是') : this.$t('否')
    }
  },

  mounted() {
    this.site = this.headerInfo.siteCode + this.headerInfo.siteName
    this.company = this.headerInfo.buyerCode + this.headerInfo.buyerName

    this.returnSite = this.headerInfo.cancelSiteCode + this.headerInfo.cancelSiteName
    // site: "",
    this.supplier = this.headerInfo.supplierCode + this.headerInfo.supplierName
    this.materialSupplier =
      this.headerInfo.materialSupplierCode + this.headerInfo.materialSupplierName
    // 获取主数据-工厂模糊查询
    this.postSiteFuzzyQuery({ text: undefined })
    this.site = this.headerInfo.siteCode + this.headerInfo.siteName
    this.company = this.headerInfo.buyerCode + this.headerInfo.buyerName

    this.returnSite = this.headerInfo.cancelSiteCode + this.headerInfo.cancelSiteName
    // site: "",
    this.supplier = this.headerInfo.supplierCode + this.headerInfo.supplierName
    this.materialSupplier =
      this.headerInfo.materialSupplierCode + this.headerInfo.materialSupplierName
    this.type = this.$route.query.type
    // this.headerInfo.isOutSale =
    //   this.headerInfo.isOutSale === 1 ? this.$t("是") : this.$t("否");
    this.headerInfo.outsourcedType =
      this.headerInfo?.outsourcedType || this.headerInfo?.outsourcedType === 0
        ? String(this.headerInfo?.outsourcedType)
        : ''
    this.headerInfo.isOutDirect = this.headerInfo.isOutDirect === 1 ? this.$t('是') : this.$t('否')
  },

  methods: {
    siteOrgClick(e) {
      const { itemData } = e
      if (itemData) {
        this.headerInfo.buyerOrgId = itemData.id
        this.headerInfo.cancelSiteCode = itemData.siteCode
        this.headerInfo.cancelSiteName = itemData.siteName
      } else {
        this.headerInfo.buyerOrgId = ''
        this.headerInfo.cancelSiteCode = ''
        this.headerInfo.cancelSiteName = ''
      }
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text,
        enterpriseId: this.headerInfo.buyerEnterpriseId
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
          }
        })
        .catch(() => {})
    },
    dateFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }
      return str
    },
    timeFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'HH:MM:SS', value })
      } else {
        str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
      }
      return str
    },
    // 获取当前状态
    typeInfo(e) {
      const _obj = {
        0: this.$t('新建'),
        1: this.$t('待确认'),
        2: this.$t('供应商退回'),
        3: this.$t('供应商确认'),
        4: this.$t('采购退回'),
        5: this.$t('采购确认'),
        6: this.$t('已完结'),
        7: this.$t('已取消')
      }
      return _obj[e]
    },

    resolveclick() {
      console.log(this.headerInfo)
      let obj = cloneDeep(this.headerInfo)

      obj.isOutSale = obj.isOutSale === this.$t('是') ? 1 : 0
      obj.isOutDirect = obj.isOutDirect === this.$t('是') ? 1 : 0

      this.$API.outsourcing.OrderBuyerConfirmAndUpdate(obj).then((res) => {
        this.$store.commit('startLoading')
        if (res.code == 200) {
          this.$store.commit('endLoading')

          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$router.go(-1)
        }
      })
      // this.$emit("startTo");
    },
    rejectclick() {
      this.$emit('rejectTo')
    },

    goBack() {
      this.$router.push(`purchase-list-returnCargo`)
      // this.$router.go(-1);
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }
  /deep/ .a {
    white-space: wrap;
  }
  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      &.more-width {
        width: 450px;
      }

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
