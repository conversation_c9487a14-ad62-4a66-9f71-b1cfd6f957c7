import { i18n } from '@/main.js'
import Vue from 'vue'
export const columnData = [
  {
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },
  {
    field: 'warehouseName',
    headerText: i18n.t('库存地点'),
    width: 200,
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.warehouseName}}{{data.warehouseCode}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'cancelQuantity',
    headerText: i18n.t('退货数量'),
    width: '150'
  },
  {
    // width: "150",
    field: 'maxCancelQuantity',
    headerText: i18n.t('可退货数量')
  },
  {
    width: '150',
    field: 'stockQuantity',
    headerText: i18n.t('库存现有量')
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('退货状态'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e === 6) {
          return i18n.t('已退货')
        } else {
          return i18n.t('未退货')
        }
      }
    }
  },
  {
    width: '150',
    field: 'wmsCancelQuantity',
    headerText: i18n.t('实退数量')
  },
  // {
  //   width: "150",
  //   field: "buyerOrgName",
  //   headerText: i18n.t("采购组"),
  // },
  {
    width: '150',
    field: 'stockUnit',
    headerText: i18n.t('库存单位')
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('行备注')
  }
]
export const serializeList = (list) => {
  if (list.length > 0) {
    let serialNumber = 1
    list.forEach((item) => {
      // 添加序号
      item.serialNumber = serialNumber++
    })
  }
  return list
}
