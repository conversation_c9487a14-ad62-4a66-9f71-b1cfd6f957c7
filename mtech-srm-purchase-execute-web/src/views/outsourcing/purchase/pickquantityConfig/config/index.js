import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '150',
    field: 'indexs',
    headerText: i18n.t('序号'),
    showInColumnChooser: false,
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return Number(+row.index + 1)
      }
    },
    cellTools: [
      {
        id: 'Edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        visibleCondition: (e) => e.status == 0,
        permission: ['O_02_0689']
      },
      {
        id: 'Delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        permission: ['O_02_0610'],

        visibleCondition: (e) => e.status == 0
      }
    ]
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        {
          value: 0,
          text: i18n.t('停用'),
          cssClass: 'col-inactive'
        },
        {
          value: 1,
          text: i18n.t('启用'),
          cssClass: 'col-active'
        }
      ]
    },
    cellTools: [
      {
        id: 'active',
        icon: 'icon_list_disable',
        title: i18n.t('启用'),
        visibleCondition: (e) => e.status == 0,
        permission: ['O_02_0612']
      },
      {
        id: 'inActive',
        icon: 'icon_list_enable',
        title: i18n.t('停用'),
        visibleCondition: (e) => e.status == 1,
        permission: ['O_02_0611']
      }
    ]
  },
  {
    width: '150',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '150',
    field: 'stockStatus', // 1开启 0关闭
    headerText: i18n.t('可用库存数量限制'),
    valueConverter: {
      type: 'map',
      map: [
        {
          value: 0,
          text: i18n.t('关闭'),
          cssClass: 'col-inactive'
        },
        {
          value: 1,
          text: i18n.t('开启'),
          cssClass: 'col-active'
        }
      ]
    }
  },
  {
    width: '150',
    field: 'demandStatus', // 1开启 0关闭
    headerText: i18n.t('最大需求数量限制'),
    valueConverter: {
      type: 'map',
      map: [
        {
          value: 0,
          text: i18n.t('关闭'),
          cssClass: 'col-inactive'
        },
        {
          value: 1,
          text: i18n.t('开启'),
          cssClass: 'col-active'
        }
      ]
    }
  },
  {
    width: '150',
    field: 'updateUserName',
    headerText: i18n.t('最后更新人'),
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return row.updateUserName || row.createUserName
      }
    }
  },
  {
    width: '150',
    field: 'updateTime',
    headerText: i18n.t('最后更新时间'),
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return row.updateTime || row.createTime
      }
    }
  }
]
