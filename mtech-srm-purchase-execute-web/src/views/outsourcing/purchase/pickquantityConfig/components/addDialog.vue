<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="headerText"
    :buttons="buttons"
    @close="handleClose"
  >
    <mt-form ref="addFormRef" :model="addForm" :rules="addRules">
      <mt-form-item prop="siteCode" :label="$t('工厂')" style="width: 100%">
        <select-filter
          :width="390"
          :fields="{ text: 'labelShow', value: 'siteCode' }"
          :request-url="requestUrl"
          :request-key="requestKey"
          :init-val.sync="addForm.siteCode"
          :other-params="otherParams"
          :label-show-obj="labelShowObj"
          @handleChange="handleChange"
        ></select-filter>
      </mt-form-item>

      <mt-form-item prop="stockStatus" :label="$t('可用库存数量限制')">
        <mt-switch v-model="addForm.stockStatus"></mt-switch>
      </mt-form-item>

      <mt-form-item prop="demandStatus" :label="$t('最大需求数量限制')">
        <mt-switch v-model="addForm.demandStatus"></mt-switch>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      headerText: this.$t('新增'),
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        siteCode: '',
        siteId: 0,
        siteName: '',
        stockStatus: 0, // 可用库存数量限制 1开启 0关闭
        demandStatus: 0 // 最大需求数量限制 1开启 0关闭
      },
      addRules: {
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        stockStatus: [
          {
            required: true,
            message: this.$t('请选择是否限制可用库存数量'),
            trigger: 'blur'
          }
        ],
        demandStatus: [
          {
            required: true,
            message: this.$t('请选择是否限制最大需求数量'),
            trigger: 'blur'
          }
        ]
      },
      requestUrl: {
        pre: 'masterData',
        url: 'postSiteFuzzyQuery'
      },
      requestKey: 'fuzzyParam',
      otherParams: { dataLimit: 100 },
      labelShowObj: { code: 'siteCode', name: 'siteName' }
    }
  },
  mounted() {
    console.log('我是弹窗')
    this.$refs.dialog.ejsRef.show()
    this.$nextTick(() => {
      this.$refs.addFormRef.resetFields()
      if (this.modalData) {
        this.headerText = this.modalData?.title
        if (this.modalData?.row) {
          this.addForm = {
            ...this.modalData.row,
            stockStatus: this.modalData.row.stockStatus ? true : false,
            demandStatus: this.modalData.row.demandStatus ? true : false
          }
        }
      }
    })
  },
  methods: {
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    cancel() {
      this.$emit('cancel-function')
    },
    confirm() {
      this.$refs.addFormRef.validate((valid) => {
        if (valid) {
          console.log(this.addForm)
          let params = {
            ...this.addForm,
            stockStatus: this.addForm.stockStatus ? 1 : 0, // 可用库存数量限制 1开启 0关闭
            demandStatus: this.addForm.demandStatus ? 1 : 0 // 最大需求数量限制 1开启 0关闭
          }
          this.$API.outsourcing.saveStockSetting(params).then(() => {
            this.$emit('confirm-function')
          })
        }
      })
    },

    handleChange(e) {
      this.addForm.siteId = e.itemData?.id
      this.addForm.siteName = e.itemData?.siteName
    }
  }
}
</script>

<style></style>
