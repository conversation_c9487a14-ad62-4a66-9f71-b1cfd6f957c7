<template>
  <div class="full-height">
    <mt-template-page
      ref="saleRecon"
      class="self-set-table"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @handleSelectTab="handleSelectTab"
    ></mt-template-page>
    <!-- 操作日志弹框 -->
    <view-logs-dialog ref="viewLogsDialog"></view-logs-dialog>
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { sumCols } from './config'
import { download, getHeadersFileName } from '@/utils/utils'
import ViewLogsDialog from './components/viewLogsDialog.vue'

export default {
  components: {
    ViewLogsDialog
  },
  data() {
    return {
      tabIndex: 0,
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'excelExport',
              icon: 'icon_solid_export',
              title: this.$t('导出')
            }
          ],
          gridId: this.$tableUUID.reconciliationSettlement.saleReconList.query,
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: sumCols,
            // 销售待对账明细接口
            asyncConfig: {
              url: `${BASE_TENANT}/sale/reconciliation/query`,
              defaultRules: []
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      let selectRows = e.gridRef.getMtechGridRecords()
      if (['close', 'repush', 'writeOff'].includes(e.toolbar.id)) {
        if (selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        let ids = selectRows.map((i) => i.id)
        if (e.toolbar.id == 'close') {
          this.handleClose(ids)
        } else if (e.toolbar.id == 'repush') {
          let finds = selectRows.find((i) => i.status != 4)
          if (finds) {
            this.$toast({
              content: this.$t('请选择已确认状态的进行操作'),
              type: 'warning'
            })
            return
          }
          this.handleRepush(ids)
        } else if (e.toolbar.id == 'writeOff') {
          let finds = selectRows.find((i) => i.status == 4 && i.syncStatus == 2)
          if (!finds) {
            this.$toast({
              content: this.$t('请选择已确认状态且非同步成功的进行操作'),
              type: 'warning'
            })
            return
          }
          this.handleWriteOff(ids)
        }
      }
      if (e.toolbar.id == 'excelExport') {
        this.handleExport()
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'close') {
        this.handleClose([e.data.id])
      } else if (e.tool.id == 'publish') {
        this.handlePublish([e.data.id])
      } else if (e.tool.id == 'confirm') {
        this.handleConfirm([e.data.id])
      } else if (e.tool.id == 'view') {
        this.getDetailById(e.data.id)
      }
    },
    handleClickCellTitle(e) {
      console.log('点击了编码', e)
      if (e.field == 'code' && this.tabIndex == 0) {
        // 根据id，拿详情数据，存到session中。详情页为 创建对账单页面
        this.getDetailById(e.data.id)
      } else if (e.field == 'operation') {
        // 点击 查看日志
        this.handleClickOption(e.data)
      }
    },
    // 点击查看日志
    handleClickOption(data) {
      console.log('查看日志', data)
      const params = {
        reconciliationCode: data.code,
        reconciliationType: 3,
        tenantId: '10000'
      }
      this.$refs.viewLogsDialog.dialogInit({
        title: this.$t('操作日志'),
        selectData: params
      })
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    // 导出
    handleExport() {
      let rule = this.$refs.saleRecon.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 10000 },
        pageFlag: true,
        rules: rule.rules || []
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement.reconciliationSaleExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 关闭
    handleClose(ids) {
      console.log('ids', ids)
      this.$dialog({
        data: {
          title: this.$t('关闭'),
          message: this.$t('确认关闭销售对账单？')
        },
        success: () => {
          this.$API.reconciliationSettlement.closeSaleRecon(ids).then(() => {
            this.$toast({ content: this.$t('关闭成功'), type: 'success' })
            this.$refs.saleRecon.refreshCurrentGridData()
          })
        }
      })
    },

    // 发布
    handlePublish(ids) {
      console.log('ids', ids)
      this.$API.reconciliationSettlement.publishSaleRecon(ids).then(() => {
        this.$toast({ content: this.$t('发布成功'), type: 'success' })
        this.$refs.saleRecon.refreshCurrentGridData()
      })
    },

    // 确认
    handleConfirm(ids) {
      console.log('ids', ids)
      this.$API.reconciliationSettlement.confirmSaleRecon(ids).then(() => {
        this.$toast({ content: this.$t('确认成功'), type: 'success' })
        this.$refs.saleRecon.refreshCurrentGridData()
      })
    },

    // 根据id，拿详情数据，存到session中。详情页为 创建对账单页面
    getDetailById(id) {
      this.$API.reconciliationSettlement.getSaleDetailById(id).then((res) => {
        let saleCreateSessionData = {
          headerInfo: res?.data.header,
          requireData: res?.data.items,
          entryType: 'view', // create view
          purFiles: res?.data?.purFiles || [],
          supFiles: res?.data?.supFiles || []
        }
        // 销售对账单 缓存数据 区分销售待对账缓存数据（saleCreateSessionData）,避免数据混乱
        sessionStorage.setItem('saleQuerySessionData', JSON.stringify(saleCreateSessionData))
        setTimeout(() => {
          this.$router.push('sale-recon-query-detail')
        }, 10)
      })
    },

    // 重新推送
    handleRepush(ids) {
      this.$API.reconciliationSettlement.rePushSaleRecon(ids).then(() => {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$refs.saleRecon.refreshCurrentGridData()
      })
    },

    // 冲销
    handleWriteOff(ids) {
      this.$API.reconciliationSettlement.writeOffSaleRecon(ids).then(() => {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$refs.saleRecon.refreshCurrentGridData()
      })
    }
  }
}
</script>

<style></style>
