// import Vue from "vue";
import { i18n } from '@/main.js'
import { timeNumberToDate, judgeFormatCodeName } from '@/utils/utils'

export const checkCol = {
  width: '50',
  type: 'checkbox',
  showInColumnChooser: false,
  allowEditing: false
}

export const detailCols = (entryType) => [
  {
    width: '150',
    field: 'saleOrderCode',
    headerText: i18n.t('订单号')
  },
  {
    width: '150',
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '150',
    field: 'receiveCode',
    headerText: i18n.t('交货单号')
  },
  {
    width: '150',
    field: 'receiveItemNo',
    headerText: i18n.t('交货单行号')
  },
  {
    visible: entryType == 'view',
    width: '150',
    field: 'syncStatus',
    headerText: i18n.t('创建同步状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('未同步'), cssClass: 'col-inactive' },
        { value: 1, text: i18n.t('同步成功'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('同步失败'), cssClass: 'col-inactive' }
      ]
    }
  },
  {
    visible: entryType == 'view',
    width: '150',
    field: 'writeOffStatus',
    headerText: i18n.t('关闭同步状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('未同步'), cssClass: 'col-inactive' },
        { value: 1, text: i18n.t('同步成功'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('同步失败'), cssClass: 'col-inactive' }
      ]
    }
  },
  {
    width: '150',
    field: 'invoiceNo',
    headerText: i18n.t('发票号'),
    valueConverter: {
      type: 'placeholder',
      placeholder: '--' //placeholder可不传，默认值为"未填"
    }
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'unitCode',
    headerText: i18n.t('单位'),
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.unitCode, data?.unitName)
    }
  },
  {
    width: '150',
    field: 'quantity',
    headerText: i18n.t('数量')
  },
  {
    width: '150',
    field: 'receiveDate',
    headerText: i18n.t('事务日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    }
  },
  {
    width: '150',
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    width: '150',
    field: 'untaxedTotalPrice',
    headerText: i18n.t('未税总价')
  },
  {
    width: '150',
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    width: '150',
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价')
  },
  {
    width: '150',
    field: 'taxedTotalPrice',
    headerText: i18n.t('含税总价')
  },
  // {
  //   width: "150",
  //   field: "currencyName",
  //   headerText: i18n.t("币种"),
  //   valueAccessor: (field, data) => {
  //     return judgeFormatCodeName(data?.currencyCode, data?.currencyName);
  //   },
  // },
  {
    width: '150',
    field: 'channelName',
    headerText: i18n.t('分销渠道')
  },
  // {
  //   width: "150",
  //   field: "channelCode",
  //   headerText: i18n.t("分销渠道编码"),
  // },

  {
    width: '150',
    field: 'typeName',
    headerText: i18n.t('条件类型')
  },
  // {
  //   width: "150",
  //   field: "typeCode",
  //   headerText: i18n.t("条件类型编码"),
  // },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  },
  {
    width: '150',
    field: 'sapBackInfo',
    headerText: i18n.t('SAP返回信息')
  }
]
