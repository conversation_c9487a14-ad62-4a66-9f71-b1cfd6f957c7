<template>
  <div class="full-height mt-flex-direction-column detail-fix-wrap">
    <!-- 头部信息 -->
    <top-info class="flex-keep" ref="topInfoRef" @doSubmit="doSubmit"></top-info>

    <div class="bottom-tables">
      <mt-tabs
        tab-id="reconci-tab"
        :e-tab="false"
        :data-source="tabList"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>

      <div class="bottom-box">
        <!-- 对账明细 -->
        <div class="grid-wrap">
          <recon-detail class="flex-fit" ref="saleReconRef" v-show="tabIndex == 0"></recon-detail>
        </div>

        <!-- 相关附件 -->
        <relative-file
          ref="relativeFileRef"
          v-show="tabIndex == 1"
          :module-file-list="moduleFileList"
        ></relative-file>

        <!-- 操作日志 -->
        <operation-log
          :entry-code="code"
          :reconciliation-type="3"
          v-if="tabIndex == 2"
        ></operation-log>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * entryType: create - 创建；view - 查看
 */
import { nodeType } from '@/components/businessComponents/relativeFileNoDocId/config/relative.js'
export default {
  components: {
    topInfo: require('./components/topInfo.vue').default,
    reconDetail: require('./components/reconDetail.vue').default,
    relativeFile: require('@/components/businessComponents/relativeFileNoDocId/index.vue').default,
    operationLog: require('@/components/businessComponents/comOperationLog/index.vue').default
  },
  data() {
    return {
      tabIndex: 0,
      tabList: [
        {
          title: this.$t('对账明细')
        },
        {
          title: this.$t('相关附件')
        }
      ],
      moduleFileList: [],
      beforeTabIndex: 0,
      id: null,
      code: null
    }
  },

  mounted() {
    this.initData()
  },

  activated() {
    this.tabList = [
      {
        title: this.$t('对账明细')
      },
      {
        title: this.$t('相关附件')
      }
    ]
    this.initData()
  },

  methods: {
    // 数据初始化
    initData() {
      let saleCreateSessionData = sessionStorage.getItem('saleCreateOrderSessionData')
      if (this.$route.name === 'sale-recon-list-detail') {
        saleCreateSessionData = sessionStorage.getItem('saleListSessionData')
      } else if (this.$route.name === 'sale-recon-query-detail') {
        saleCreateSessionData = sessionStorage.getItem('saleQuerySessionData')
      }
      saleCreateSessionData = JSON.parse(saleCreateSessionData)
      this.beforeTabIndex = saleCreateSessionData?.tabIndex
      this.id = saleCreateSessionData?.headerInfo?.id
      this.code = saleCreateSessionData?.headerInfo?.code
      if (saleCreateSessionData?.entryType == 'view') {
        this.moduleFileList = [
          {
            id: '01',
            code: 'reconciliation_settled_header_file', // 清账整单附件code
            nodeName: this.$t('采方 - 整单附件'),
            type: nodeType.mainViewData,
            dataSource: saleCreateSessionData?.purFiles || []
          },
          {
            id: '02',
            code: 'reconciliation_gong',
            nodeName: this.$t('供方 - 整单附件'),
            type: nodeType.mainViewData,
            dataSource: saleCreateSessionData?.supFiles || []
          }
        ]
        this.tabList.push({
          title: this.$t('操作日志')
        })
      } else {
        this.moduleFileList = [
          {
            id: '01',
            code: 'reconciliation_settled_header_file', // 清账整单附件code
            nodeName: this.$t('采方 - 整单附件'),
            type: nodeType.mainUpdate
          }
        ]
      }
    },
    doSubmit() {
      console.log(this.$refs.topInfoRef, this.$refs.saleReconRef, this.$refs.relativeFileRef)
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交吗？')
        },
        success: () => {
          let _dataSource =
            this.$refs.saleReconRef.$refs.detailTemplate
              .getCurrentUsefulRef()
              .gridRef?.ejsRef?.getCurrentViewRecords() || []

          let _waits = _dataSource.map((i) => {
            return {
              waitId: i.id,
              remark: i.remark
            }
          })

          let submitData = {
            fileList: this.$refs.relativeFileRef.getUploadFlies(this.moduleFileList[0].id),
            remark: this.$refs.topInfoRef?.topInfo.remark,
            waits: _waits
          }
          this.$API.reconciliationSettlement.createSateRecon(submitData).then((res) => {
            if (res?.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$router.replace(`sale-reconciliation?tabIndex=${this.beforeTabIndex}`)
            }
          })
        }
      })
    },

    handleSelectTab(e) {
      this.tabIndex = e
    }
  },

  beforeDestroy() {
    if (this.$route.name === 'sale-recon-list-detail') {
      sessionStorage.removeItem('saleListSessionData')
    } else if (this.$route.name === 'sale-recon-query-detail') {
      sessionStorage.removeItem('saleQuerySessionData')
    } else {
      sessionStorage.removeItem('saleCreateOrderSessionData')
    }
  }
}
</script>
