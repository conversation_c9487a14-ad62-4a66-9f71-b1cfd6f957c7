<template>
  <div :class="['detail-top-info', !isExpand && 'detail-top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box" v-if="topInfo">
      <div
        :class="['infos', 'mr20', 'status-box', `status-box-${topInfo.status}`]"
        v-if="entryType == 'view'"
      >
        {{ statusTxt }}
      </div>
      <div class="infos mr20" v-if="entryType == 'view'">
        {{ $t('销售对账单号：') }}{{ topInfo.code || '-' }}
      </div>
      <div class="infos mr20">{{ $t('创建人：') }}{{ topInfo.createUserName }}</div>
      <div class="infos">{{ $t('创建时间：') }}{{ topInfo.createTime | dateFormat }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-if="entryType == 'create'"
        @click="doSubmit"
        >{{ $t('提交') }}</mt-button
      >

      <div class="sort-box" @click="doExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom" v-if="topInfo">
      <mt-form ref="ruleForm" :model="topInfo">
        <mt-form-item prop="company" :label="$t('公司')">
          <mt-input v-model="topInfo.company" :disabled="true" :placeholder="$t('公司')"></mt-input>
        </mt-form-item>
        <mt-form-item prop="saleGroup" :label="$t('销售组织')" v-if="entryType == 'view'">
          <mt-input
            v-model="topInfo.saleGroup"
            :disabled="true"
            :placeholder="$t('销售组织')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="supplier" :label="$t('供应商/客户名称')">
          <mt-input
            v-model="topInfo.supplier"
            :disabled="true"
            :placeholder="$t('供应商/客户名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="currency" :label="$t('币种')">
          <mt-input
            v-model="topInfo.currency"
            :disabled="true"
            :placeholder="$t('币种')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="unTaxTotal" :label="$t('未税总金额')">
          <mt-input
            v-model="unTaxTotal"
            :disabled="true"
            :placeholder="$t('未税总金额')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="taxedTotal" :label="$t('含税总金额')">
          <mt-input
            v-model="taxedTotal"
            :disabled="true"
            :placeholder="$t('含税总金额')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="taxAmount" :label="$t('税额')">
          <mt-input v-model="taxAmount" :disabled="true" :placeholder="$t('税额')"></mt-input>
        </mt-form-item>

        <mt-form-item
          prop="remark"
          :label="$t('采方备注')"
          class="full-width"
          v-if="entryType == 'create'"
          :show-message="true"
        >
          <mt-input v-model="topInfo.remark" :placeholder="$t('采方备注')"></mt-input>
        </mt-form-item>

        <mt-form-item
          prop="purRemark"
          :label="$t('采方备注')"
          class="full-width"
          v-if="entryType == 'view'"
        >
          <mt-input
            v-model="topInfo.purRemark"
            :disabled="true"
            :placeholder="$t('采方备注')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item
          prop="supRemark"
          :label="$t('供方备注')"
          class="full-width"
          v-if="entryType == 'view'"
        >
          <mt-input
            v-model="topInfo.supRemark"
            :disabled="true"
            :placeholder="$t('供方备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
/**
 * entryType: create - 创建；view - 查看
 */
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
export default {
  data() {
    return {
      isExpand: true,
      topInfo: null,
      unTaxTotal: 0,
      taxedTotal: 0,
      taxAmount: 0,
      beforeTabIndex: 0 // 待对账列表页面的tab序号
    }
  },

  filters: {
    dateFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }

      return str
    }
  },

  watch: {
    'topInfo.status'(value) {
      let str = ''
      if (value == -1) {
        str = this.$t('关闭')
      } else if (value == 0) {
        str = this.$t('待发布')
      } else if (value == 1) {
        str = this.$t('发布待反馈')
      } else if (value == 2) {
        str = this.$t('反馈正常')
      } else if (value == 3) {
        str = this.$t('反馈异常')
      } else if (value == 4) {
        str = this.$t('已确认')
      }
      this.statusTxt = str
    }
  },

  mounted() {
    this.initData()
  },
  activated() {
    this.initData()
  },

  methods: {
    initData() {
      // 设置头部信息
      let saleCreateSessionData = sessionStorage.getItem('saleCreateOrderSessionData')
      if (this.$route.name === 'sale-recon-list-detail') {
        saleCreateSessionData = sessionStorage.getItem('saleListSessionData')
      } else if (this.$route.name === 'sale-recon-query-detail') {
        saleCreateSessionData = sessionStorage.getItem('saleQuerySessionData')
      }
      saleCreateSessionData = JSON.parse(saleCreateSessionData)
      let headerInfo = saleCreateSessionData?.headerInfo
      let topInfo = {
        ...headerInfo,
        company: headerInfo?.companyCode + '-' + headerInfo?.companyName,
        supplier: headerInfo?.supplierCode + '-' + headerInfo?.supplierName,
        saleGroup: headerInfo?.saleGroupCode + '-' + headerInfo?.saleGroupName,
        currency: headerInfo?.currencyCode + '-' + headerInfo?.currencyName
      }
      this.topInfo = topInfo
      this.entryType = saleCreateSessionData?.entryType
      this.beforeTabIndex = saleCreateSessionData?.tabIndex

      // 监听 总金额
      this.$bus.$on('changeSaleDetailRows', (e) => {
        this.unTaxTotal = e.unTaxTotal
        this.taxedTotal = e.taxedTotal
        this.taxAmount = e.taxAmount
      })
    },
    // 返回
    goBack() {
      if (this.$route.name === 'sale-recon-list-detail') {
        this.$router.replace(`sale-recon-list`)
      } else if (this.$route.name === 'sale-recon-query-detail') {
        this.$router.replace(`sale-recon-query`)
      } else {
        this.$router.replace(`sale-reconciliation?tabIndex=${this.beforeTabIndex}`)
      }
    },
    // 提交
    doSubmit() {
      this.$emit('doSubmit')
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
    }
  }
}
</script>

<style lang="scss" scoped>
.status-box {
  line-height: 20px;
  padding: 4px;
  border-radius: 2px;
  margin: 0 36px 0 10px;
  font-size: 12px;
  font-family: PingFangSC;
  font-weight: 500;
  color: #6386c1;
  background: rgba(99, 134, 193, 0.1);
  &--1 {
    color: #9baac1;
    background: rgba(155, 170, 193, 0.1);
  }
}
</style>
