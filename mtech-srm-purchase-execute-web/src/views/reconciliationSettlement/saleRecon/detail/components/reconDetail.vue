<template>
  <mt-template-page
    ref="detailTemplate"
    :template-config="componentConfig"
    @handleClickToolBar="handleClickToolBar"
  ></mt-template-page>
</template>

<script>
/**
 * entryType: create - 创建；view - 查看
 */
import { cloneDeep } from 'lodash'
var bigDecimal = require('js-big-decimal')
import { checkCol, detailCols } from '../config/index.js'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  data() {
    return {
      componentConfig: [
        {
          useToolTemplate: false,
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: [],
          gridId: this.$tableUUID.reconciliationSettlement.saleReconciliation.addList,
          grid: {
            height: 'auto',
            allowPaging: false,
            columnData: [checkCol],
            dataSource: []
          }
        }
      ],
      entryType: null,
      headerId: null,
      headerInfo: null
    }
  },

  mounted() {
    this.setData()
  },
  activated() {
    this.setData()
  },
  methods: {
    // 设置列、行、是否可编辑
    setData() {
      let saleCreateSessionData = sessionStorage.getItem('saleCreateOrderSessionData')
      if (this.$route.name === 'sale-recon-list-detail') {
        saleCreateSessionData = sessionStorage.getItem('saleListSessionData')
      } else if (this.$route.name === 'sale-recon-query-detail') {
        saleCreateSessionData = sessionStorage.getItem('saleQuerySessionData')
      }
      saleCreateSessionData = JSON.parse(saleCreateSessionData)
      this.entryType = saleCreateSessionData?.entryType
      this.headerId = saleCreateSessionData?.headerInfo?.id
      this.headerInfo = saleCreateSessionData?.headerInfo

      // 设置列
      let columns = cloneDeep(detailCols(this.entryType))
      columns.forEach((i) => {
        i.allowEditing = i.field == 'remark'
      })
      this.$set(this.componentConfig[0].grid, 'columnData', [checkCol].concat(columns))

      // 设置dataSource
      this.$set(this.componentConfig[0].grid, 'dataSource', saleCreateSessionData?.requireData)

      // 设置是否表格可编辑
      let editSettings
      if (this.entryType == 'create') {
        editSettings = {
          allowEditing: true,
          allowAdding: true,
          allowDeleting: true,
          mode: 'Normal', // Batch模式下editTemplate使用下拉的话，保存的args里获取不到，所以就回显不上。最终用重新赋值dataSource解决(币种和贸易条款的下拉)
          showConfirmDialog: false,
          showDeleteConfirmDialog: false, // 因为切换业务类型的时候，只能通过deleteRecord来删除了，但这个方法会触发这个弹窗，所以只能把弹窗去掉
          newRowPosition: 'Bottom'
        }
        this.$set(this.componentConfig[0], 'toolbar', [
          [
            {
              id: 'remove',
              icon: 'icon_table_remove',
              title: this.$t('移除')
            },
            {
              id: 'excelExport',
              icon: 'icon_solid_pushorder',
              title: this.$t('导出')
            }
          ]
        ])
      } else {
        editSettings = {
          allowEditing: false
        }
        this.$set(this.componentConfig[0], 'toolbar', [
          [
            {
              id: 'excelExport',
              icon: 'icon_solid_pushorder',
              title: this.$t('导出')
            }
          ]
        ]) // 查看状态无移除按钮
      }
      this.$set(this.componentConfig[0].grid, 'editSettings', editSettings)

      // 设置头部的 总金额
      this.countTotal(saleCreateSessionData?.requireData)
    },

    handleClickToolBar(e) {
      const selectRowIndexs = e.grid.getSelectedRowIndexes() // 选择的行的序号

      // 移除
      if (e.toolbar.id == 'remove') {
        if (selectRowIndexs.length === 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认移除选中的数据？')
          },
          success: () => {
            const tableData = this.$refs.detailTemplate
              .getCurrentUsefulRef()
              .ejsRef.getCurrentViewRecords()
            console.log('tableData', tableData)
            // 过滤掉已勾选的
            let _dataSource = tableData.filter((item, index) => !selectRowIndexs.includes(index))
            this.countTotal(_dataSource)
            this.$set(this.componentConfig[0].grid, 'dataSource', _dataSource)
          }
        })
      } else if (e.toolbar.id == 'excelExport') {
        if (this.entryType == 'create') {
          this.handleExportNoId()
        } else {
          this.handleExport()
        }
      }
    },

    // 行数据发生变化时，计算传递 总价
    countTotal(newVal) {
      let unTaxTotal = 0,
        taxedTotal = 0,
        taxAmount = 0

      for (let i of newVal) {
        // unTaxTotal = bigDecimal.round(bigDecimal.add(unTaxTotal, i.untaxedTotalPrice), 2)
        // taxedTotal = bigDecimal.round(bigDecimal.add(taxedTotal, i.taxedTotalPrice), 2)
        unTaxTotal = bigDecimal.add(unTaxTotal, i.untaxedTotalPrice)
        taxedTotal = bigDecimal.add(taxedTotal, i.taxedTotalPrice)
      }
      // taxAmount = bigDecimal.round(bigDecimal.subtract(taxedTotal, unTaxTotal), 2)
      taxAmount = bigDecimal.subtract(taxedTotal, unTaxTotal)
      this.$bus.$emit('changeSaleDetailRows', {
        unTaxTotal: Number(unTaxTotal),
        taxedTotal: Number(taxedTotal),
        taxAmount: Number(taxAmount)
      })
    },
    handleExport() {
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement.reconciliationSaleListExport(this.headerId).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 待对账明细导出
    handleExportNoId() {
      const tableData = this.$refs.detailTemplate
        .getCurrentUsefulRef()
        .ejsRef.getCurrentViewRecords()
      console.log(tableData)
      const ids = tableData.map((item) => item.id)
      const params = {
        page: { current: 1, size: 10000 },
        condition: 'and',
        rules: [
          {
            field: 'id',
            operator: 'in',
            value: ids
          }
        ]
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement.reconciliationSaleDownload(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>
