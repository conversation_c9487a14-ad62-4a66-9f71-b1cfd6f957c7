<template>
  <!-- 采方-销售待对账列表 -->
  <div class="full-height">
    <mt-template-page
      ref="saleBeRecon"
      class="self-set-table"
      :permission-obj="permissionObj"
      :template-config="componentConfig"
      :current-tab="tabIndex"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleSelectTab="handleSelectTab"
    ></mt-template-page>
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { detailCols, summaryCols } from './config'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  data() {
    return {
      tabIndex: 0,
      total: 0,
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0028' },
          { dataPermission: 'b', permissionCode: 'T_02_0029' }
        ]
      },
      componentConfig: [
        {
          title: this.$t('销售待对账明细'),
          dataPermission: 'a',
          permissionCode: 'T_02_0028', // 需要与permissionObj中的参数和权限code对应
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'createStatement',
              icon: 'icon_solid_Createorder',
              title: this.$t('创建对账单'),
              permission: ['O_02_0403']
            },
            {
              id: 'batchCreateStatement',
              icon: 'icon_solid_Createorder',
              title: this.$t('批量创建对账单'),
              permission: ['O_02_0403']
            },
            {
              id: 'excelExport',
              icon: 'icon_solid_export',
              title: this.$t('导出')
            }
          ],
          gridId: this.$tableUUID.reconciliationSettlement.saleReconciliation.detail,
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: detailCols,
            // 销售待对账明细接口
            asyncConfig: {
              url: `${BASE_TENANT}/sale/reconciliation/wait/query`,
              defaultRules: [],
              afterAsyncData: (res) => {
                this.total = res?.data?.total
              }
            },
            frozenColumns: 1
          }
        },
        {
          title: this.$t('销售待对账'),
          dataPermission: 'b',
          permissionCode: 'T_02_0029', // 需要与permissionObj中的参数和权限code对应
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'createStatementAll',
              icon: 'icon_solid_Createorder',
              title: this.$t('创建对账单'),
              permission: ['O_02_0697']
            }
          ],
          gridId: this.$tableUUID.reconciliationSettlement.saleReconciliation.list,
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            autoWidthColumns: 8,
            columnData: summaryCols,
            // 销售待对账明细接口 - 汇总列表
            asyncConfig: {
              url: `${BASE_TENANT}/sale/reconciliation/wait/package`,
              defaultRules: []
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },

  computed: {
    userInfo() {
      return this.$store.state.userInfo
    }
  },

  mounted() {
    let tabIndex = this.$route.query?.tabIndex || 0
    this.tabIndex = Number(tabIndex)
    if (tabIndex) this.$router.replace('sale-reconciliation')
  },

  methods: {
    // 切换tab
    handleSelectTab(e) {
      this.tabIndex = e
    },

    // 点击按钮
    handleClickToolBar(e) {
      let selectRows = e.gridRef.getMtechGridRecords()
      // 点击 创建
      if (
        ['createStatement', 'createStatementAll', 'batchCreateStatement'].includes(e.toolbar.id)
      ) {
        if (selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
      }
      if (e.toolbar.id == 'createStatement') {
        let valid = this.checkSelectedValid(selectRows)
        let _len = selectRows?.length
        if (!valid) {
          this.$toast({
            content: this.$t('请选择相同的公司、客户、币种的数据进行创建'),
            type: 'warning'
          })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t(
              `是否跳转创建页面提交创建？（您勾选的数据条数：${_len}，当前待对账总条数：${this.total}）`
            )
          },
          success: () => {
            this.routeToDetail('create', selectRows)
          }
        })
      } else if (e.toolbar.id == 'batchCreateStatement') {
        this.batchCreateStatement(selectRows)
      } else if (e.toolbar.id == 'createStatementAll') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t(`是否确认创建对账单`)
          },
          success: () => {
            this.$store.commit('startLoading')
            let params = selectRows.map((i) => {
              return {
                companyCode: i.companyCode,
                currencyCode: i.currencyCode,
                supplierCode: i.supplierCode,
                saleGroupCode: i.saleGroupCode
              }
            })
            this.$API.reconciliationSettlement.createSaleByPackage(params).then(() => {
              this.$store.commit('endLoading')
              this.$toast({ content: this.$t('创建成功'), type: 'success' })
              this.$refs.saleBeRecon.refreshCurrentGridData()
            })
          }
        })
      } else if (e.toolbar.id == 'excelExport') {
        this.handleExport()
      }
    },

    // 校验
    checkSelectedValid(rows) {
      if (rows.length == 1) {
        return true
      }
      let valid = true
      // 相同的公司编号 +客户编号（供应商）+币种编号 进行聚合
      for (let i = 0; i < rows.length - 1; i++) {
        if (
          rows[i].companyCode != rows[i + 1].companyCode ||
          rows[i].supplierCode != rows[i + 1].supplierCode ||
          rows[i].currencyCode != rows[i + 1].currencyCode
        ) {
          valid = false
          break
        }
      }
      return valid
    },

    // 点击标题
    handleClickCellTitle(e) {
      console.log('点击title', e)
      if (e.field != 'count') return
      if (!e.data[e.field]) {
        this.$toast({ content: this.$t('无明细行'), type: 'warning' })
        return
      }
      this.$API.reconciliationSettlement
        .getSaleRowsByPackage({
          companyCode: e.data.companyCode,
          currencyCode: e.data.currencyCode,
          supplierCode: e.data.supplierCode,
          saleGroupCode: e.data.saleGroupCode
        })
        .then((res) => {
          if (res?.code == 200) {
            this.routeToDetail('create', res.data)
          }
        })
    },

    // 跳转到创建页面
    routeToDetail(flag, selectRows) {
      this.$store.commit('startLoading')
      let saleCreateSessionData = {
        headerInfo: {
          ...selectRows[0],
          createUserName: this.userInfo.accountName,
          createTime: new Date()
        },
        requireData: selectRows,
        entryType: flag, // create view
        tabIndex: this.tabIndex
      }
      // 销售待对账数据缓存数据
      sessionStorage.setItem('saleCreateOrderSessionData', JSON.stringify(saleCreateSessionData))
      setTimeout(() => {
        this.$store.commit('endLoading')
        this.$router.push('sale-be-recon')
      }, 10)
    },
    // 批量创建对账单
    batchCreateStatement(selectRows) {
      const _len = selectRows?.length
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(
            `是否确认创建对账单？（您勾选的数据条数：${_len}，当前待对账总条数：${this.total}）`
          )
        },
        success: () => {
          this.$store.commit('startLoading')
          let params = selectRows.map((i) => {
            return {
              waitId: i.id
            }
          })
          this.$API.reconciliationSettlement.createSaleByBatch({ waits: params }).then(() => {
            this.$store.commit('endLoading')
            this.$toast({ content: this.$t('创建成功'), type: 'success' })
            this.$refs.saleBeRecon.refreshCurrentGridData()
          })
        }
      })
    },
    // 导出
    handleExport() {
      let rule = this.$refs.saleBeRecon.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 10000 },
        pageFlag: true,
        rules: rule.rules || []
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement.reconciliationSaleDownload(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>

<style></style>
