// import Vue from "vue";
import { i18n } from '@/main.js'
import { timeNumberToDate, judgeFormatCodeName } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'

export const checkCol = {
  width: '50',
  type: 'checkbox',
  showInColumnChooser: false,
  allowEditing: false
}

export const detailCols = [
  {
    width: '150',
    field: 'saleOrderCode',
    headerText: i18n.t('订单号')
  },
  {
    width: '150',
    field: 'lineNo',
    headerText: i18n.t('行号'),
    ignore: true
  },
  {
    width: '150',
    field: 'receiveCode',
    headerText: i18n.t('交货单号')
  },
  {
    width: '150',
    field: 'receiveItemNo',
    headerText: i18n.t('交货单行号')
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    searchOptions: MasterDataSelect.material
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    ignore: true
  },
  {
    width: '250',
    field: 'unitCode',
    headerText: i18n.t('单位'),
    searchOptions: MasterDataSelect.unit,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.unitCode, data?.unitName)
    }
  },
  {
    width: '150',
    field: 'quantity',
    headerText: i18n.t('数量')
  },
  {
    width: '150',
    field: 'receiveDate',
    headerText: i18n.t('事务日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    width: '150',
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    width: '150',
    field: 'untaxedTotalPrice',
    headerText: i18n.t('未税总价')
  },
  {
    width: '150',
    field: 'taxRate',
    headerText: i18n.t('税率'),
    searchOptions: MasterDataSelect.saleTaxRate
  },
  {
    width: '150',
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价')
  },
  {
    width: '150',
    field: 'taxedTotalPrice',
    headerText: i18n.t('含税总价')
  },
  {
    width: '250',
    field: 'currencyCode',
    headerText: i18n.t('币种'),
    searchOptions: MasterDataSelect.money,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.currencyCode, data?.currencyName)
    }
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  },
  {
    width: '250',
    field: 'companyCode',
    headerText: i18n.t('公司'),
    searchOptions: MasterDataSelect.businessCompany,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.companyCode, data?.companyName)
    }
  },
  {
    width: '150',
    field: 'saleGroupName',
    headerText: i18n.t('销售组织')
  },
  {
    width: '150',
    field: 'saleGroupCode',
    headerText: i18n.t('销售组织编码')
  },
  {
    width: '150',
    field: 'channelName',
    headerText: i18n.t('分销渠道')
  },
  // {
  //   width: "150",
  //   field: "channelCode",
  //   headerText: i18n.t("分销渠道编码"),
  // },
  {
    width: '150',
    field: 'typeName',
    headerText: i18n.t('条件类型')
  },
  // {
  //   width: "150",
  //   field: "typeCode",
  //   headerText: i18n.t("条件类型编码"),
  // },
  {
    width: '250',
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    searchOptions: MasterDataSelect.supplierAll,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  }
  // {
  //   width: "150",
  //   field: "supplierCode",
  //   headerText: i18n.t("供应商编码"),
  // },
]

export const summaryCols = [
  {
    width: '250',
    field: 'companyCode',
    headerText: i18n.t('公司'),
    searchOptions: MasterDataSelect.businessCompany,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.companyCode, data?.companyName)
    }
  },
  {
    // width: "150",
    field: 'saleGroupCode',
    headerText: i18n.t('销售组织'),
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return row.saleGroupCode + '-' + row.saleGroupName
      }
    }
  },
  {
    width: '250',
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    searchOptions: MasterDataSelect.supplier,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  },
  {
    width: '250',
    field: 'currencyCode',
    headerText: i18n.t('币种'),
    searchOptions: MasterDataSelect.money,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.currencyCode, data?.currencyName)
    }
  },
  {
    // width: "150",
    field: 'count',
    headerText: i18n.t('详情明细'),
    cellTools: [],
    ignore: true
  }
]
