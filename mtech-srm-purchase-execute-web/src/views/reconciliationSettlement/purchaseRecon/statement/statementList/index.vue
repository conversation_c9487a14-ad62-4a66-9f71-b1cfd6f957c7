<template>
  <!-- 采方-对账协同-采购对账单列表 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="toolbarClick"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      class="template-height"
    >
    </mt-template-page>
    <accept-dialog ref="acceptDialog" @refreshColumns="refreshColumns"></accept-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import { columnData } from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  components: {
    AcceptDialog: () => import('./components/acceptDialog')
  },
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'close',
              icon: 'icon_solid_Closeorder',
              title: this.$t('关闭'),
              permission: ['O_02_0398']
            },
            {
              id: 'publish',
              icon: 'icon_solid_pushorder',
              title: this.$t('发布'),
              permission: ['O_02_0399']
            },
            // {
            //   id: "cancelPublish",
            //   icon: "icon_table_cancel",
            //   title: this.$t("取消发布"),
            //   permission: ["O_02_0400"],
            // },
            {
              id: 'acceptBatch',
              icon: 'icon_table_accept1',
              title: this.$t('批量接受'),
              permission: ['O_02_0401']
            },
            {
              id: 'refuseBatch',
              icon: 'icon_table_refuse1',
              title: this.$t('批量拒绝')
            },
            {
              id: 'printRecon',
              icon: 'icon_table_print',
              title: this.$t('打印'),
              permission: ['O_02_1166']
            },
            {
              id: 'excelExport',
              icon: 'icon_solid_export',
              title: this.$t('导出')
            }
          ],
          gridId: this.$tableUUID.reconciliationSettlement.statementList.list,
          grid: {
            columnData: columnData,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliationHeader/queryBuilderStatus?BU_CODE=${localStorage.getItem(
                'currentBu'
              )}`,
              recordsPosition: 'data.records',
              params: {}
            },
            lineSelection: 0,
            lineIndex: 1,
            frozenColumns: 1
          }
        }
      ]
    }
  },
  created() {},
  mounted() {},
  methods: {
    toolbarClick(e) {
      if (e.toolbar.id !== 'excelExport' && e.gridRef.getMtechGridRecords().length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = []
      e.gridRef.getMtechGridRecords().map((item) => _id.push(item.id))
      if (e.toolbar.id == 'close') {
        if (
          e.gridRef.getMtechGridRecords().some((item) => item.sourcePath == 1) ||
          e.gridRef.getMtechGridRecords().some((item) => item.status !== 3)
        ) {
          this.$toast({
            content: this.$t('只能操作采方创建且状态为反馈异常的对账单'),
            type: 'warning'
          })
          return
        } else {
          this.handleClose(_id)
        }
      } else if (e.toolbar.id == 'publish') {
        if (
          e.gridRef.getMtechGridRecords().some((item) => item.sourcePath == 1) ||
          e.gridRef.getMtechGridRecords().some((item) => item.status != 0)
        ) {
          this.$toast({
            content: this.$t('只能操作采方创建且状态为未发布的对账单'),
            type: 'warning'
          })
          return
        } else {
          this.handlePublish(_id)
        }
      } else if (e.toolbar.id == 'cancelPublish') {
        if (
          e.gridRef.getMtechGridRecords().some((item) => item.sourcePath == 1) ||
          e.gridRef.getMtechGridRecords().some((item) => item.status != 1)
        ) {
          this.$toast({
            content: this.$t('只能操作采方创建且状态为待反馈的对账单'),
            type: 'warning'
          })
          return
        } else {
          this.handleCancelPublish(_id)
        }
      } else if (e.toolbar.id == 'acceptBatch') {
        // 不是供方创建且状态为待反馈的数据
        if (
          e.gridRef.getMtechGridRecords().some((item) => item.sourcePath != 1) ||
          e.gridRef.getMtechGridRecords().some((item) => item.status != 1)
        ) {
          this.$toast({
            content: this.$t('只能操作供方创建且状态为待反馈的对账单'),
            type: 'warning'
          })
          return
        } else {
          // this.handleAcceptBatch();
          this.$refs.acceptDialog.acceptInfo({
            dialogTitle: this.$t('批量接受'),
            ids: _id
          })
        }
      } else if (e.toolbar.id === 'refuseBatch') {
        // 批量拒绝
        const params = {
          idList: _id
        }
        this.handleBatchRefuse(params)
      } else if (e.toolbar.id == 'printRecon') {
        this.handlePrint(_id)
      } else if (e.toolbar.id == 'excelExport') {
        this.handleExport()
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'close') {
        this.handleClose([e.data.id])
      } else if (e.tool.id == 'publish') {
        this.handlePublish([e.data.id])
      } else if (e.tool.id == 'exceptionHandle') {
        localStorage.setItem('statementData', JSON.stringify(e.data))
        this.$router.push(`/purchase-execute/statement-detail?id=${e.data.id}&type=1&fromType=list`)
      } else if (e.tool.id == 'cancelPublish') {
        this.handleCancelPublish([e.data.id])
      } else if (e.tool.id == 'edit' || e.tool.id == 'feedback') {
        // 将信息放到 localStorage
        localStorage.setItem('statementData', JSON.stringify(e.data))
        this.$router.push(`/purchase-execute/statement-detail?id=${e.data.id}&type=1&fromType=list`)
      }
    },
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 批量拒绝
    handleBatchRefuse(params) {
      this.$API.reconciliationSettlement
        .putBatchFeedbackAbnormal(params)
        .then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 批量接受
    // handleAcceptBatch(ids) {
    //   this.$API.reconciliationSettlement
    //     .putBatchFeedbackNormal({
    //       idList: ids,
    //       remark: "",
    //     })
    //     .then((res) => {
    //       if (res.code === 200) {
    //         this.$toast({
    //           content: this.$t("操作成功"),
    //           type: "success",
    //         });
    //         this.refreshColumns();
    //       }
    //     });
    // },
    // 关闭对账单
    handleClose(ids) {
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t('确定关闭选中行?')
        },
        success: () => {
          this.$API.reconciliationSettlement
            .closeReconciliation({
              idList: ids,
              currentBu: localStorage.getItem('currentBu') || 'KT'
            })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.refreshColumns()
              }
            })
        }
      })
    },
    // 发布对账单
    handlePublish(ids) {
      this.$API.reconciliationSettlement
        .publishReconciliation({
          idList: ids
        })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.refreshColumns()
          }
        })
    },
    // 取消发布对账单
    handleCancelPublish(ids) {
      this.$API.reconciliationSettlement
        .cancelPublishReconciliation({
          idList: ids
        })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.refreshColumns()
          }
        })
    },
    //单元格title文字点击
    handleClickCellTitle(e) {
      // 将信息放到 localStorage
      localStorage.setItem('statementData', JSON.stringify(e.data))
      if (e.field == 'reconciliationCode') {
        //如果是待反馈的状态 点击数据也让在详情页提交
        const _type = e.data.sourcePath == 1 && e.data.status == 1 ? 1 : 0
        this.$router.push(
          `/purchase-execute/statement-detail?id=${e.data.id}&type=${_type}&fromType=list`
        )
      }
    },
    // 导出
    handleExport() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 10000 },
        pageFlag: true,
        rules: rule.rules || [],
        defaultRules: [
          {
            label: this.$t('状态'),
            field: 'status',
            type: 'string',
            operator: 'notequal',
            value: 2
          },
          {
            label: this.$t('状态'),
            field: 'status',
            type: 'string',
            operator: 'notequal',
            value: -1
          }
        ]
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement.reconciliationHeaderDownload(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 打印
    handlePrint(ids) {
      this.$API.reconciliationSettlement
        .printRecon({ idList: ids, currentBu: localStorage.getItem('currentBu') || 'KT' })
        .then((res) => {
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = () => {
              console.log('======', reader)
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }

            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
          // window.open(this.pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    }
  }
}
</script>

<style></style>
