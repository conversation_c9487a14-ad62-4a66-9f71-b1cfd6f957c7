<template>
  <!-- 采方-对账协同-采购对账单列表 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="toolbarClick"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      class="template-height"
    >
    </mt-template-page>
    <accept-dialog ref="acceptDialog" @refreshColumns="refreshColumns"></accept-dialog>
    <!-- 操作日志弹框 -->
    <view-logs-dialog ref="viewLogsDialog"></view-logs-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import { columnData } from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'
import ViewLogsDialog from './components/viewLogsDialog.vue'
export default {
  components: {
    AcceptDialog: () => import('./components/acceptDialog'),
    ViewLogsDialog
  },
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'excelExport',
              icon: 'icon_solid_export',
              title: this.$t('导出')
            },
            {
              id: 'printRecon',
              icon: 'icon_table_print',
              title: this.$t('打印')
            },
            {
              id: 'printHRORecon',
              icon: 'icon_table_print',
              title: this.$t('人力外包打印')
            },
            {
              id: 'syncSap',
              icon: 'icon_table_print',
              title: this.$t('重新推送SAP')
            }
          ],
          gridId: this.$tableUUID.reconciliationSettlement.statementList.query,
          grid: {
            columnData: columnData,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliationHeader/queryBuilder?BU_CODE=${localStorage.getItem(
                'currentBu'
              )}`,
              recordsPosition: 'data.records'
            },
            lineSelection: 0,
            lineIndex: 1,
            frozenColumns: 1
          }
        }
      ]
    }
  },
  created() {},
  mounted() {},
  methods: {
    toolbarClick(e) {
      if (e.toolbar.id !== 'excelExport' && e.gridRef.getMtechGridRecords().length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = []
      e.gridRef.getMtechGridRecords().map((item) => _id.push(item.id))
      if (e.toolbar.id == 'close') {
        if (
          e.gridRef.getMtechGridRecords().some((item) => item.sourcePath == 1) ||
          e.gridRef.getMtechGridRecords().some((item) => item.status == -1)
        ) {
          this.$toast({
            content: this.$t('只能操作采方创建且状态为未关闭的对账单'),
            type: 'warning'
          })
          return
        } else {
          this.handleClose(_id)
        }
      } else if (e.toolbar.id == 'publish') {
        if (
          e.gridRef.getMtechGridRecords().some((item) => item.sourcePath == 1) ||
          (e.gridRef.getMtechGridRecords().some((item) => item.status != 0) &&
            e.gridRef.getMtechGridRecords().some((item) => item.status != 3))
        ) {
          this.$toast({
            content: this.$t('只能操作采方创建且状态为未发布或反馈异常的对账单'),
            type: 'warning'
          })
          return
        } else {
          this.handlePublish(_id)
        }
      } else if (e.toolbar.id == 'cancelPublish') {
        if (
          e.gridRef.getMtechGridRecords().some((item) => item.sourcePath == 1) ||
          e.gridRef.getMtechGridRecords().some((item) => item.status != 1)
        ) {
          this.$toast({
            content: this.$t('只能操作采方创建且状态为待反馈的对账单'),
            type: 'warning'
          })
          return
        } else {
          this.handleCancelPublish(_id)
        }
      } else if (e.toolbar.id == 'acceptBatch') {
        // 不是供方创建且状态为待反馈的数据
        if (
          e.gridRef.getMtechGridRecords().some((item) => item.sourcePath != 1) ||
          e.gridRef.getMtechGridRecords().some((item) => item.status != 1)
        ) {
          this.$toast({
            content: this.$t('只能操作供方创建且状态为待反馈的对账单'),
            type: 'warning'
          })
          return
        } else {
          // this.handleAcceptBatch();
          this.$refs.acceptDialog.acceptInfo({
            dialogTitle: this.$t('批量接受'),
            ids: _id
          })
        }
      } else if (e.toolbar.id == 'printRecon') {
        this.handlePrint(_id)
      } else if (e.toolbar.id == 'printHRORecon') {
        this.handleHROPrint(e.gridRef.getMtechGridRecords())
      } else if (e.toolbar.id == 'syncSap') {
        this.handleSyncSap(e.gridRef.getMtechGridRecords())
      } else if (e.toolbar.id == 'excelExport') {
        this.handleExport()
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'close') {
        this.handleClose([e.data.id])
      } else if (e.tool.id == 'publish') {
        this.handlePublish([e.data.id])
      } else if (e.tool.id == 'cancelPublish') {
        this.handleCancelPublish([e.data.id])
      } else if (e.tool.id == 'edit' || e.tool.id == 'feedback') {
        // 将信息放到 localStorage
        localStorage.setItem('statementData', JSON.stringify(e.data))
        this.$router.push(`/purchase-execute/statement-detail?id=${e.data.id}&type=1`)
      }
    },
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 批量接受
    // handleAcceptBatch(ids) {
    //   this.$API.reconciliationSettlement
    //     .putBatchFeedbackNormal({
    //       idList: ids,
    //       remark: "",
    //     })
    //     .then((res) => {
    //       if (res.code === 200) {
    //         this.$toast({
    //           content: this.$t("操作成功"),
    //           type: "success",
    //         });
    //         this.refreshColumns();
    //       }
    //     });
    // },
    // 关闭对账单
    handleClose(ids) {
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t('确定关闭选中行?')
        },
        success: () => {
          this.$API.reconciliationSettlement
            .closeReconciliation({
              idList: ids
            })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.refreshColumns()
              }
            })
        }
      })
    },
    // 发布对账单
    handlePublish(ids) {
      this.$API.reconciliationSettlement
        .publishReconciliation({
          idList: ids
        })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.refreshColumns()
          }
        })
    },
    // 取消发布对账单
    handleCancelPublish(ids) {
      this.$API.reconciliationSettlement
        .cancelPublishReconciliation({
          idList: ids
        })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.refreshColumns()
          }
        })
    },
    //单元格title文字点击
    handleClickCellTitle(e) {
      // 将信息放到 localStorage
      localStorage.setItem('statementQueryData', JSON.stringify(e.data))
      if (e.field == 'reconciliationCode') {
        this.$router.push(
          `/purchase-execute/statement-detail?id=${e.data.id}&type=0&fromType=query`
        )
      } else if (e.field == 'operation') {
        // 点击 查看日志
        this.handleClickOption(e.data)
      }
    },
    // 点击查看日志
    handleClickOption(data) {
      console.log('查看日志', data)
      const params = {
        reconciliationCode: data.reconciliationCode,
        reconciliationType: 1,
        tenantId: '10000'
      }
      this.$refs.viewLogsDialog.dialogInit({
        title: this.$t('操作日志'),
        selectData: params
      })
    },
    // 导出
    handleExport() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 10000 },
        pageFlag: true,
        rules: rule.rules || []
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement.reconciliationHeaderQueryDownload(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 打印
    handlePrint(ids) {
      this.$API.reconciliationSettlement
        .printRecon({ idList: ids, currentBu: localStorage.getItem('currentBu') || 'KT' })
        .then((res) => {
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = () => {
              console.log('======', reader)
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'warning'
              })
            }

            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
          // window.open(this.pdfUrl);
          let date = new Date().getTime()
          let ifr = document.createElement('iframe')
          ifr.style.frameborder = 'no'
          ifr.style.display = 'none'
          ifr.style.pageBreakBefore = 'always'
          ifr.setAttribute('id', 'printPdf' + date)
          ifr.setAttribute('name', 'printPdf' + date)
          ifr.src = this.pdfUrl
          document.body.appendChild(ifr)
          this.doPrint('printPdf' + date)
          window.URL.revokeObjectURL(ifr.src)
        })
    },
    // 人力外包重新推送SAP
    handleSyncSap(selectedRecords) {
      const idList = []
      for (let i = 0; i < selectedRecords.length; i++) {
        const obj = selectedRecords[i]
        if (obj.syncSapStatus !== 0) {
          this.$toast({
            content: this.$t('仅可选择未同步的数据进行同步'),
            type: 'warning'
          })
          return
        }
        if (obj.reconciliationTypeCode !== 'HRO') {
          this.$toast({
            content: this.$t('仅可选择对账类型为人力外包的数据进行同步'),
            type: 'warning'
          })
          return
        }
        idList.push(obj.id)
      }
      this.$API.reconciliationSettlement.syncReconciliationHroInfoToSap({ idList }).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.refreshColumns()
        }
      })
    },
    // 人力外包打印
    handleHROPrint(ids) {
      if (ids.length !== 1) {
        this.$toast({
          content: this.$t('仅可选择一行进行打印'),
          type: 'warning'
        })
        return
      }
      if (ids[0]['reconciliationTypeCode'] !== 'HRO') {
        this.$toast({
          content: this.$t('仅可选择对账类型为人力外包的数据进行打印'),
          type: 'warning'
        })
        return
      }
      if (ids[0]['status'] !== 2) {
        this.$toast({
          content: this.$t('只能打印反馈正常的'),
          type: 'warning'
        })
        return
      }
      this.$API.reconciliationSettlement.printHRORecon({ id: ids[0]['id'] }).then((res) => {
        if (res?.data?.type === 'application/json') {
          const reader = new FileReader()
          reader.readAsText(res?.data, 'utf-8')
          reader.onload = () => {
            console.log('======', reader)
            const readerRes = reader.result
            const resObj = JSON.parse(readerRes)
            Vue.prototype.$toast({
              content: resObj.msg,
              type: 'warning'
            })
          }

          return
        }
        const content = res.data
        this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
        // window.open(this.pdfUrl);
        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = this.pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    }
  }
}
</script>

<style></style>
