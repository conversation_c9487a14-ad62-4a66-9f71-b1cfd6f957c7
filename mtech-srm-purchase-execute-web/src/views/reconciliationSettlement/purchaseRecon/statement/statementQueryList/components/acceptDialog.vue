<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="ruleForm">
      <mt-form-item prop="remark" :label="$t('反馈意见')" class="full-width">
        <mt-input
          v-model="ruleForm.remark"
          :show-clear-button="true"
          :placeholder="$t('请输入反馈意见')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      orderId: '',
      entryIds: [],
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {
        remark: ''
      }
    }
  },
  methods: {
    acceptInfo(entryInfo) {
      this.dialogTitle = entryInfo.dialogTitle
      this.entryIds = entryInfo.ids
      this.orderId = entryInfo.orderId
      this.$refs.dialog.ejsRef.show()
      this.ruleForm = {
        remark: ''
      }
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    confirm() {
      let params = {
        idList: this.entryIds,
        remark: this.ruleForm.remark
      }

      this.$API.reconciliationSettlement.putBatchFeedbackNormal(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
        this.handleClose()
        this.$emit('refreshColumns')
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
