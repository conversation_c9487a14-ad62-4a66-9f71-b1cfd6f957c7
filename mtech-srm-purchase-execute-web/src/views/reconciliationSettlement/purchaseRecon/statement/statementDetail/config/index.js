import Vue from 'vue'
import { timeDate, requiredHeader } from './columnComponent'
import { i18n } from '@/main.js'
import { formatMasterFilter } from '@/utils/utils'
import { Query } from '@syncfusion/ej2-data'
import dayjs from 'dayjs'
// 高开/低开
const TypeDropData = [
  {
    text: i18n.t('补差'),
    value: 0
  },
  {
    text: i18n.t('返利'),
    value: 1
  },
  {
    text: i18n.t('其他'),
    value: 2
  },
  {
    text: i18n.t('应付返工费用'),
    value: 3
  },
  {
    text: i18n.t('应扣住宿费用'),
    value: 4
  },
  {
    text: i18n.t('应扣考核费用'),
    value: 5
  },
  {
    text: i18n.t('外包借入工时费用'),
    value: 6
  },
  {
    text: i18n.t('外包借出工时费用'),
    value: 7
  },
  {
    text: i18n.t('损耗率返利'),
    value: 8
  }
]
// 表格 checkbox
export const ColumnCheckbox = {
  width: '50',
  type: 'checkbox',
  showInColumnChooser: false,
  allowEditing: false,
  visible: false // 不显示，无需使用 checkbox
}

// tab code
export const TabCode = {
  highLowInfo: 'highLowInfo', // 高低开信息
  operationLog: 'operationLog', // 操作日志
  reconciliationFile: 'reconciliationFile', // 相关附件
  reconciliationField: 'reconciliationField', // 对账明细
  purHeaderFile: 'purHeaderFile', // 采方-整单附件
  supHeaderFile: 'supHeaderFile' // 供方-整单附件
}

export const formatTableColumnData = (cols, businessTypeCode) => {
  let isFC = false
  if (
    businessTypeCode === 'BTTCL001' ||
    businessTypeCode === 'BTTCL002' ||
    businessTypeCode === 'BTTCL003'
  ) {
    isFC = true
  }
  const res = []
  cols.forEach((col) => {
    if (!col.checkStatus) return
    var defaultCol = {
      ...col,
      field: col.code,
      headerText: col.name,
      width: '150',
      allowEditing: false
    }
    defaultCol = formatMasterFilter(defaultCol)
    if (isFC) {
      // 非采情况下，字段名修改
      if (defaultCol.field === 'executeUntaxedUnitPrice') {
        // 采购未税单价
        defaultCol.headerText = i18n.t('采购未税单价')
      }
      if (defaultCol.field === 'executeUntaxedTotalPrice') {
        // 采购未税总价
        defaultCol.headerText = i18n.t('采购未税总价')
      }
      if (defaultCol.field === 'executeTaxedUnitPrice') {
        // 采购含税单价
        defaultCol.headerText = i18n.t('采购含税单价')
      }
      if (defaultCol.field === 'executeTaxedTotalPrice') {
        // 采购未税单价
        defaultCol.headerText = i18n.t('采购含税总价')
      }
    }
    if (defaultCol.field === 'sourceHeaderCode') {
      defaultCol.template = () => {
        return {
          template: Vue.component('sourceHeaderCode', {
            template: `<div @click="sourceHeaderCodeClick" class="able-click-field">{{ data.sourceHeaderCode }}</div>`,
            data() {
              return { data: {} }
            },
            methods: {
              sourceHeaderCodeClick() {
                if (localStorage.getItem('currentBu') === 'GF') {
                  this.$router.push({
                    path: '/purchase-pv/purchase-coordination-detail',
                    query: {
                      type: 'detail',
                      id: this.data.sourceHeaderId,
                      refreshId: new Date().getTime()
                    }
                  })
                  return
                }
                this.$API.purchaseOrder
                  .getTenantPurOrderCode(this.data.sourceHeaderCode)
                  .then((res) => {
                    if (res.code === 200) {
                      this.$router.push({
                        name: 'purchase-coordination-detail',
                        query: {
                          orderid: res.data.id,
                          type: '3'
                        }
                      })
                    }
                  })
              }
            }
          })
        }
      }
    }
    if (defaultCol.field === 'projectCode') {
      defaultCol.template = () => {
        return {
          template: Vue.component('projectCode', {
            template: `<div @click="projectCodeClick" class="able-click-field">{{ data.projectCode }}</div>`,
            data() {
              return { data: {} }
            },
            methods: {
              projectCodeClick() {
                this.$API.contract
                  .findByRfxCodeApi({ rfxCode: this.data.projectCode })
                  .then((res) => {
                    if (res.code === 200) {
                      this.$router.push({
                        path: '/sourcing/bid-hall/hall-detail',
                        query: {
                          source: res.data.sourcingMode,
                          rfxId: res.data.id,
                          sourceType: 'statement-detail',
                          sourceQuery: JSON.stringify(this.$route.query)
                        }
                      })
                    }
                  })
              }
            }
          })
        }
      }
    }
    if (defaultCol.field === 'contractCode') {
      defaultCol.template = () => {
        return {
          template: Vue.component('contractCode', {
            template: `<div @click="contractCodeClick" class="able-click-field">{{ data.contractCode }}</div>`,
            data() {
              return { data: {} }
            },
            methods: {
              contractCodeClick() {
                this.$API.contract
                  .getByContractCodeApi({ contractCode: this.data.contractCode })
                  .then((res) => {
                    if (res.code === 200) {
                      this.$router.push({
                        path: '/middlePlatform/contractPrivew',
                        query: {
                          contractId: res.data.id,
                          type: 'statement-detail',
                          sourceQuery: JSON.stringify(this.$route.query)
                        }
                      })
                    }
                  })
              }
            }
          })
        }
      }
    }
    if (defaultCol.field === 'remark') {
      // 采方备注
      defaultCol.allowEditing = true
    } else if (defaultCol.field === 'sourceType') {
      // 来源类型（枚举） 0: 上游流入1:第三方接口
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('上游流入'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('第三方接口'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'receiveTime') {
      // 收货时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'status') {
      // 单据状态 0:待对账 1:已创建对账单
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('待对账'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('已创建对账单'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'syncStatus') {
      // 同步状态 0:否 1:是
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('否'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('是'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'advanceInvoicing') {
      // 提前开票 0:否 1:是
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('否'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('是'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'frozenStatus') {
      // 冻结标记 0:否 1:是
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('否'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('是'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'prePayStatus') {
      // 是否预付 0-否；1-是
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('否'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('是'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'itemVoucherDate') {
      // 物料凭证日期
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'provisionalEstimateStatus') {
      // 是否暂估价 0-否；1-是 改成是否执行价
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('是'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('否'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'createTime') {
      // 创建时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'updateTime') {
      // 最后修改时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'type') {
      // 待对账类型:0-采购待对账；1-销售待对账
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('采购待对账'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('销售待对账'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'realPriceStatus') {
      // 正式价标识 0-无正式价；1-有正式价
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('无正式价'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('有正式价'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'inOutType') {
      // 出入库类型 0-采购；1-销售；
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('采购'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('销售'),
            cssClass: ''
          }
        ]
      }
    } else if (
      defaultCol.field === 'modelCode' ||
      defaultCol.field === 'recoTime' ||
      defaultCol.field === 'priceType' ||
      defaultCol.field === 'workshop' ||
      defaultCol.field === 'prodtLine' ||
      defaultCol.field === 'workProcessName' ||
      defaultCol.field === 'teamGroupCode' ||
      defaultCol.field === 'batchNo' ||
      defaultCol.field === 'settleModel' ||
      defaultCol.field === 'dailyOutput' ||
      defaultCol.field === 'materialCode' ||
      defaultCol.field === 'workOrder' ||
      defaultCol.field === 'moduleUnitPrice' ||
      defaultCol.field === 'cbuUnitPrice' ||
      defaultCol.field === 'otherUnitPrice' ||
      defaultCol.field === 'domainRentalFeeUnitPrice' ||
      defaultCol.field === 'equipmentRentalFeeUnitPrice' ||
      defaultCol.field === 'domainRentalFeeTotalPrice' ||
      defaultCol.field === 'equipmentRentalFeeTotalPrice' ||
      defaultCol.field === 'moduleDomainRentalFeeUnitPrice' ||
      defaultCol.field === 'moduleEquipmentRentalFeeUnitPrice' ||
      defaultCol.field === 'cbuDomainRentalFeeUnitPrice' ||
      defaultCol.field === 'cbuEquipmentRentalFeeUnitPrice' ||
      defaultCol.field === 'moduleDomainRentalFeeTotalPrice' ||
      defaultCol.field === 'moduleEquipmentRentalFeeTotalPrice' ||
      defaultCol.field === 'cbuDomainRentalFeeTotalPrice' ||
      defaultCol.field === 'cbuEquipmentRentalFeeTotalPrice' ||
      defaultCol.field === 'settleUnitPriceUntaxed' ||
      defaultCol.field === 'settleUnitPriceTaxed' ||
      defaultCol.field === 'settleTotalPriceUntaxed' ||
      defaultCol.field === 'settleTotalPriceTaxed' ||
      defaultCol.field === 'shareTotalPrice'
    ) {
      // 机型编码(ID)、对账时间、价格类型、车间、工序、班组、批次号、结算机型、日产量、物料编码、工单、模组单价、整机单价、其他单价、场地租赁费单价、设备租赁费单价、场地租赁费总价、设备租赁费总价、模组场地租赁费单价、模组设备租赁费单价、整机场地租赁费单价、整机设备租赁费单价、模组场地租赁费总价、模组设备租赁费总价、整机场地租赁费总价、整机设备租赁费总价、结算未税单价、结算含税单价、结算未税总价、结算含税总价、分摊总价
      defaultCol.searchOptions = {
        renameField: `reconciliationHroResponse.${defaultCol.field}`
      }
      defaultCol.valueAccessor = (field, data) => {
        if (defaultCol.field === 'recoTime') {
          return dayjs(Number(data?.reconciliationHroResponse?.[defaultCol.field])).format(
            'YYYY-MM-DD HH:mm:ss'
          )
        }
        if (defaultCol.field === 'priceType') {
          const priceType = [
            {
              value: '0',
              text: i18n.t('整机'),
              cssClass: ''
            },
            {
              value: '2',
              text: i18n.t('模组'),
              cssClass: ''
            },
            {
              value: '3',
              text: i18n.t('其他'),
              cssClass: ''
            }
          ]
          // return data?.reconciliationHroResponse?.[defaultCol.field]
          return priceType.filter(
            (i) => i.value === data?.reconciliationHroResponse?.[defaultCol.field]
          )[0]?.['text']
        }
        return data?.reconciliationHroResponse?.[defaultCol.field]
      }
      defaultCol.ignore = true
    }
    res.push(defaultCol)
  })

  res.push({
    field: 'id',
    headerText: 'id',
    ignore: true,
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true, // 主键，导入解析后，设置表格数据需要
    allowEditing: false
  })

  return res
}

export const columnData1 = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    field: 'advanceInvoicing',
    headerText: i18n.t('提前开票'),
    valueConverter: {
      type: 'map',
      //0. 是，0. 否
      map: {
        0: i18n.t('否'),
        1: i18n.t('是')
      }
    }
  },
  {
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    field: 'receiveCode',
    headerText: i18n.t('入库/验收单号')
  },
  {
    field: 'sourceHeaderCode',
    headerText: i18n.t('订单号')
  },
  {
    field: 'projectName',
    headerText: i18n.t('项目名称')
  },
  {
    field: 'projectCode',
    headerText: i18n.t('项目编号')
  },
  {
    field: 'contractCode',
    headerText: i18n.t('合同编号')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编号')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料/品项编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料/品项名称')
  },
  {
    field: 'skuCode',
    headerText: i18n.t('SKU编码')
  },
  {
    field: 'skuName',
    headerText: i18n.t('SKU名称')
  },
  {
    field: 'skuName',
    headerText: i18n.t('规格型号')
  },
  {
    field: 'categoryName',
    headerText: i18n.t('品类')
  },
  {
    field: 'requireName',
    headerText: i18n.t('需求名称')
  },
  {
    field: 'requireDescription',
    headerText: i18n.t('需求描述')
  },
  {
    field: 'assetsCategory',
    headerText: i18n.t('资产类别')
  },
  {
    field: 'assetsCode',
    headerText: i18n.t('资产编号')
  },
  {
    field: 'assetsCard',
    headerText: i18n.t('资产卡片')
  },
  {
    field: 'customName',
    headerText: i18n.t('客户')
  },
  {
    field: 'relationCustomOrder',
    headerText: i18n.t('关联客户订单')
  },
  // {
  //   field: "orderCode",
  //   headerText: i18n.t("验收项编号"),
  // },
  {
    field: 'acceptanceCheckType',
    headerText: i18n.t('验收项类型')
  },
  // {
  //   field: "orderCode",
  //   headerText: i18n.t("验收附件"),
  // },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    field: 'untaxedTotalPrice',
    headerText: i18n.t('未税总价')
  },
  {
    field: 'taxedTotalPrice',
    headerText: i18n.t('含税总价')
  },
  {
    field: 'termsUntaxedUnitPrice',
    headerText: i18n.t('（未税）单价')
  },
  {
    field: 'payUntaxedTotalPrice',
    headerText: i18n.t('应付未税总价')
  },
  {
    field: 'payTaxedTotalPrice',
    headerText: i18n.t('应付含税总价')
  },
  {
    field: 'chargebackUntaxedPrice',
    headerText: i18n.t('扣款未税金额')
  },
  {
    field: 'currencyName',
    headerText: i18n.t('金额币种')
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司编号')
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'siteName',
    headerText: i18n.t('地点/工厂')
  },
  {
    field: 'stockSite',
    headerText: i18n.t('库存地点')
  },
  {
    field: 'costCenter',
    headerText: i18n.t('成本中心')
  },
  {
    field: 'profitCenter',
    headerText: i18n.t('利润中心')
  },
  {
    field: 'paymentMode',
    headerText: i18n.t('采购方式')
  },
  {
    field: 'remark',
    headerText: i18n.t('备注说明')
  },
  {
    field: 'receiveUserName',
    headerText: i18n.t('收货人')
  },
  {
    field: 'receiveTime',
    headerText: i18n.t('收货时间')
  },
  {
    field: 'relationContractCode',
    headerText: i18n.t('关联合同/协议编号')
  },
  {
    field: 'fileBaseInfoList',
    headerText: i18n.t('附件'),
    template: function () {
      return {
        template: Vue.component('fileBaseInfoList', {
          template: `<div @click="showFileBaseInfo" class="cell-operable-title">{{data.fileBaseInfoList | listNumFormat}}</div>`,
          data: function () {
            return { data: {} }
          },
          filters: {
            listNumFormat(value) {
              if (value && value.length > 0) {
                return value.length
              } else {
                return ''
              }
            }
          },
          methods: {
            showFileBaseInfo() {
              this.$parent.$emit('showFileBaseInfo', {
                index: this.data.index,
                value: this.data.fileBaseInfoList
              })
            }
          }
        })
      }
    }
  }
]

export const openColumnData = (
  reconciliationTypeCode,
  workshopList,
  workProcessNameList,
  companyCode
) => [
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false,
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    headerText: 'addId主键',
    visible: false,
    isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。
    allowEditing: false
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    headerTemplate: requiredHeader({
      headerText: i18n.t('物料编码')
    }),
    validationRules: { required: true },
    visible: companyCode === '0530' ? true : false
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    headerTemplate: requiredHeader({
      headerText: i18n.t('物料名称')
    }),
    validationRules: { required: true },
    visible: companyCode === '0530' ? true : false
  },
  {
    width: '150',
    field: 'type',
    headerText: i18n.t('类型'), // 0-补差；1-返利；2-其他
    editType: 'dropdownedit',
    edit: {
      params: {
        allowFiltering: true,
        dataSource: TypeDropData,
        fields: { value: 'value', text: 'text' },
        query: new Query(),
        placeholder: i18n.t('请选择类型'),
        floatLabelType: 'Never',
        showClearButton: true,
        actionComplete: () => false
      }
    },
    valueAccessor: function (field, data, column) {
      let dataSource = column.edit.params.dataSource || []
      return dataSource.filter((i) => i.value == data[field])?.[0]?.text
    },
    headerTemplate: requiredHeader({
      headerText: i18n.t('类型')
    }),
    validationRules: { required: true }
  },
  {
    width: '150',
    field: 'freePrice',
    headerText: i18n.t('未税金额'),
    editType: 'numericedit',
    edit: {
      params: {
        // min: 0,
      }
    },
    headerTemplate: requiredHeader({
      headerText: i18n.t('未税金额')
    }),
    validationRules: { required: true }
  },
  {
    width: '150',
    field: 'taxPrice',
    headerText: i18n.t('含税金额'),
    editType: 'numericedit',
    edit: {
      params: {
        // min: 0,
      }
    },
    headerTemplate: requiredHeader({
      headerText: i18n.t('含税金额')
    }),
    validationRules: { required: true }
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注'),
    headerTemplate: requiredHeader({
      headerText: i18n.t('备注')
    }),
    validationRules: { required: true }
  },
  {
    width: '150',
    field: 'workshop',
    headerText: i18n.t('车间'),
    editType: 'dropdownedit',
    edit: {
      params: {
        allowFiltering: true,
        dataSource: workshopList,
        // fields: { value: 'value', text: 'text' },
        query: new Query(),
        placeholder: i18n.t('请选择车间'),
        floatLabelType: 'Never',
        showClearButton: true,
        actionComplete: () => false
      }
    },
    // valueAccessor: function (field, data, column) {
    //   let dataSource = column.edit.params.dataSource || []
    //   return dataSource.filter((i) => i.value == data[field])?.[0]?.text
    // },
    // headerTemplate: requiredHeader({
    //   headerText: i18n.t('车间')
    // }),
    // validationRules: { required: true },
    visible: reconciliationTypeCode === 'HRO' ? true : false
  },
  {
    width: '150',
    field: 'workProcessName',
    headerText: i18n.t('工序'),
    editType: 'dropdownedit',
    edit: {
      params: {
        allowFiltering: true,
        dataSource: workProcessNameList,
        // fields: { value: 'value', text: 'text' },
        query: new Query(),
        placeholder: i18n.t('请选择工序'),
        floatLabelType: 'Never',
        showClearButton: true,
        actionComplete: () => false
      }
    },
    // valueAccessor: function (field, data, column) {
    //   let dataSource = column.edit.params.dataSource || []
    //   return dataSource.filter((i) => i.value == data[field])?.[0]?.text
    // },
    // headerTemplate: requiredHeader({
    //   headerText: i18n.t('工序')
    // }),
    // validationRules: { required: true },
    visible: reconciliationTypeCode === 'HRO' ? true : false
  }
]

// export const openColumnData = [
//   {
//     width: "50",
//     type: "checkbox",
//     showInColumnChooser: false,
//   },
//   {
//     width: "150",
//     field: "type",
//     headerText: i18n.t("类型"), // 0-补差；1-返利；2-其他
//     valueConverter: {
//       type: "map",
//       //0. 是，0. 否
//       map: {
//         0: i18n.t("补差"),
//         1: i18n.t("返利"),
//         2: i18n.t("其他"),
//       },
//     },
//   },
//   {
//     width: "150",
//     field: "freePrice",
//     headerText: i18n.t("未税金额"),
//   },
//   {
//     width: "150",
//     field: "taxPrice",
//     headerText: i18n.t("含税金额"),
//   },
//   {
//     width: "150",
//     field: "remark",
//     headerText: i18n.t("备注"),
//   },
// ];
