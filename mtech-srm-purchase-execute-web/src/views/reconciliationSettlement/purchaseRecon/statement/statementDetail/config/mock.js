import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName } from '@/utils/utils'

export default [
  {
    id: '68',
    code: 'reconciliationField',
    name: '对账明细',
    checkStatus: true,
    hide: null,
    fieldResponseList: [
      {
        checkStatus: true,
        hide: null,
        fieldResponseList: null,
        sort: 0,
        width: '150',
        code: 'sourceHeaderCode',
        name: i18n.t('采购订单号'),
        allowEditing: false
      },
      {
        checkStatus: true,
        hide: null,
        fieldResponseList: null,
        sort: 0,
        width: '150',
        code: 'lineNo',
        name: i18n.t('行号'),
        allowEditing: false
      },
      {
        checkStatus: true,
        hide: null,
        fieldResponseList: null,
        sort: 0,
        width: '250',
        code: 'siteCode',
        name: i18n.t('工厂'),
        allowEditing: false,
        searchOptions: MasterDataSelect.factoryAddress,
        valueAccessor: (code, data) => {
          return judgeFormatCodeName(data?.siteCode, data?.siteName)
        }
      },
      {
        checkStatus: true,
        hide: null,
        fieldResponseList: null,
        sort: 0,
        width: '150',
        code: 'itemVoucherDate',
        name: i18n.t('凭证日期'),
        allowEditing: false
      },
      {
        checkStatus: true,
        hide: null,
        fieldResponseList: null,
        sort: 0,
        width: '150',
        code: 'itemCode',
        name: i18n.t('物料编号'),
        allowEditing: false,
        searchOptions: MasterDataSelect.material
      },
      {
        checkStatus: true,
        hide: null,
        fieldResponseList: null,
        sort: 0,
        width: '150',
        code: 'itemName',
        name: i18n.t('物料名称'),
        allowEditing: false,
        ignore: true
      },
      {
        checkStatus: true,
        hide: null,
        fieldResponseList: null,
        sort: 0,
        width: '250',
        code: 'companyCode',
        name: i18n.t('公司'),
        allowEditing: false,
        searchOptions: MasterDataSelect.businessCompany,
        valueAccessor: (code, data) => {
          return judgeFormatCodeName(data?.companyCode, data?.companyName)
        }
      },
      {
        checkStatus: true,
        hide: null,
        fieldResponseList: null,
        sort: 0,
        width: '250',
        code: 'supplierCode',
        name: i18n.t('供应商'),
        allowEditing: false,
        searchOptions: MasterDataSelect.supplier,
        valueAccessor: (code, data) => {
          return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
        }
      },
      {
        checkStatus: true,
        hide: null,
        fieldResponseList: null,
        sort: 0,
        width: '150',
        code: 'quantity',
        name: i18n.t('数量'),
        allowEditing: false
      },
      {
        checkStatus: true,
        hide: null,
        fieldResponseList: null,
        sort: 0,
        width: '150',
        code: 'untaxedUnitPrice',
        name: i18n.t('未税单价'),
        allowEditing: false
      },
      {
        checkStatus: true,
        hide: null,
        fieldResponseList: null,
        sort: 0,
        width: '150',
        code: 'untaxedTotalPrice',
        name: i18n.t('未税总价'),
        allowEditing: false
      },
      {
        checkStatus: true,
        hide: null,
        fieldResponseList: null,
        sort: 0,
        width: '150',
        code: 'taxCode',
        name: i18n.t('税率'),
        allowEditing: false,
        searchOptions: MasterDataSelect.taxRate
      },
      {
        checkStatus: true,
        hide: null,
        fieldResponseList: null,
        sort: 0,
        width: '150',
        code: 'taxedUnitPrice',
        name: i18n.t('含税单价'),
        allowEditing: false
      },
      {
        checkStatus: true,
        hide: null,
        fieldResponseList: null,
        sort: 0,
        width: '150',
        code: 'taxedTotalPrice',
        name: i18n.t('含税总价'),
        allowEditing: false
      },
      {
        checkStatus: true,
        hide: null,
        fieldResponseList: null,
        sort: 0,
        width: '250',
        code: 'currencyCode',
        name: i18n.t('币种'),
        allowEditing: false,
        searchOptions: MasterDataSelect.money,
        valueAccessor: (code, data) => {
          return judgeFormatCodeName(data?.currencyCode, data?.currencyName)
        }
      },
      {
        checkStatus: true,
        hide: null,
        fieldResponseList: null,
        sort: 0,
        width: '150',
        code: 'remark',
        name: i18n.t('采方备注'),
        allowEditing: false
      }
      // {
      //   checkStatus: true,
      //   hide: null,
      //   fieldResponseList: null,
      //   sort: 0,
      //   width: "150",
      //   code: "feedbackRemark",
      //   name: i18n.t("供方备注"),
      //   allowEditing: false,
      // },
    ],
    sort: null
  },
  {
    id: '69',
    code: 'reconciliationFile',
    name: '相关附件',
    checkStatus: true,
    hide: null,
    fieldResponseList: [
      {
        id: '1',
        code: 'purHeaderFile',
        name: '采方-整单附件',
        checkStatus: true,
        hide: null,
        fieldResponseList: null,
        sort: 0
      },
      {
        id: '2',
        code: 'supHeaderFile',
        name: '供方-整单附件',
        checkStatus: true,
        hide: null,
        fieldResponseList: null,
        sort: 0
      }
    ],
    sort: null
  },
  {
    id: '71',
    code: 'highLowInfo',
    name: '高低开信息',
    checkStatus: false,
    hide: null,
    fieldResponseList: [],
    sort: null
  }
]
