<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div :class="['status-box', 'status-box' + '-' + topInfo.status]">
        <span v-if="topInfo.status == 0">{{ $t('未发布') }}</span>
        <span v-if="topInfo.status == 1">{{ $t('待反馈') }}</span>
        <span v-if="topInfo.status == 2">{{ $t('反馈正常') }}</span>
        <span v-if="topInfo.status == 3">{{ $t('反馈异常') }}</span>
        <span v-if="topInfo.status == -1">{{ $t('已关闭') }}</span>
      </div>
      <div class="infos mr20">{{ $t('对账单号：') }}{{ topInfo.reconciliationCode }}</div>
      <div class="infos mr20">{{ $t('创建人：') }}{{ topInfo.createUserName }}</div>
      <div class="infos">{{ $t('创建时间：') }}{{ topInfo.createTime }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        @click="saveData"
        v-if="topInfo.status == 0 || topInfo.status == 3"
        >{{ $t('提交') }}</mt-button
      >

      <div v-if="$route.query.type == 1">
        <mt-button
          css-class="e-flat"
          :is-primary="true"
          @click="handleData"
          v-if="topInfo.status == 3"
          >{{ $t('异常处理') }}</mt-button
        >
        <mt-button
          css-class="e-flat"
          :is-primary="true"
          @click="handleAccept"
          v-if="topInfo.sourcePath == 1 && topInfo.status == 1"
          >{{ $t('接受') }}</mt-button
        >
        <mt-button
          css-class="e-flat"
          :is-primary="true"
          @click="handleReject"
          v-if="topInfo.sourcePath == 1 && topInfo.status == 1"
          >{{ $t('拒绝') }}</mt-button
        >
      </div>

      <div class="sort-box" @click="isExpand = !isExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>
    <div v-show="isExpand">
      <mt-form ref="ruleForm" :model="topInfo">
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="company" :label="$t('公司')" class="disabled-label">
              <mt-input v-model="topInfo.company" :disabled="true"></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="supplier" :label="$t('供应商')" class="disabled-label">
              <mt-input v-model="topInfo.supplier" :disabled="true"></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="site" :label="$t('工厂')" class="disabled-label">
              <mt-input v-model="topInfo.site" :disabled="true"></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="currencyName" :label="$t('币种')">
              <mt-input
                v-model="topInfo.currencyName"
                :disabled="true"
                :placeholder="$t('币种')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item
              prop="executeUntaxedTotalPrice"
              :label="isFC ? $t('采购未税总金额') : $t('执行未税总金额')"
            >
              <mt-input
                v-model="topInfo.executeUntaxedTotalPrice"
                :disabled="true"
                :placeholder="isFC ? $t('采购未税总金额') : $t('执行未税总金额')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="taxAmount" :label="$t('税额')">
              <mt-input
                v-model="topInfo.taxAmount"
                :disabled="true"
                :placeholder="$t('税额')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item
              prop="executeTaxedTotalPrice"
              :label="isFC ? $t('采购含税总金额') : $t('执行含税总金额')"
            >
              <mt-input
                v-model="topInfo.executeTaxedTotalPrice"
                :disabled="true"
                :placeholder="isFC ? $t('采购含税总金额') : $t('执行含税总金额')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="taxRate" :label="$t('税率')">
              <mt-input
                v-model="topInfo.taxRate"
                :disabled="true"
                :placeholder="$t('税率')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item
              prop="untaxedTotalPrice"
              :label="isFC ? $t('采购未税金额') : $t('入库未税金额')"
            >
              <mt-input
                v-model="topInfo.untaxedTotalPrice"
                :disabled="true"
                :placeholder="isFC ? $t('采购未税金额') : $t('入库未税金额')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item v-if="!isFC" prop="amountDifferentTotal" :label="$t('差异合计')">
              <mt-input
                v-model="topInfo.amountDifferentTotal"
                :disabled="true"
                :placeholder="$t('差异合计')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item v-if="!isFC" prop="rateDifferent" :label="$t('差异率')">
              <mt-input
                v-model="topInfo.rateDifferent"
                :disabled="true"
                :placeholder="$t('差异率')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item prop="reconciliationTypeName" :label="$t('对账类型')">
              <mt-input
                v-model="topInfo.reconciliationTypeName"
                :disabled="true"
                :placeholder="$t('对账类型')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item v-if="isGF" prop="totalNumber" :label="$t('对账单总数量')">
              <mt-input
                v-model="topInfo.totalNumber"
                :disabled="true"
                :placeholder="$t('对账单总数量')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item v-if="isGF" prop="contactAddress" :label="$t('联系人地址')">
              <mt-input
                v-model="topInfo.contactAddress"
                :disabled="true"
                :placeholder="$t('联系人地址')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item v-if="isGF" prop="partyAddress" :label="$t('甲方地址')">
              <mt-input
                v-model="topInfo.partyAddress"
                :disabled="true"
                :placeholder="$t('甲方地址')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item
              prop="remark"
              :label="$t('采方备注')"
              :class="['full-width', !isRemarkEditable && 'disabled-label']"
            >
              <mt-input
                ref="remarkInput"
                v-model="topInfo.remark"
                :disabled="!isRemarkEditable"
                :title="purTitle"
                max-length="200"
                @change="remarkChange('purTitle', 'remark')"
                @input="remarkChange('purTitle', 'remark')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item
              prop="feedbackRemark"
              :label="$t('供方备注')"
              :class="['full-width', !isFeedbackRemarkEditable && 'disabled-label']"
            >
              <mt-input
                v-model="topInfo.feedbackRemark"
                :disabled="!isFeedbackRemarkEditable"
                ref="remarkInput"
                :title="supTitle"
                @change="remarkChange('supTitle', 'feedbackRemark')"
                @input="remarkChange('supTitle', 'feedbackRemark')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </div>
</template>

<script>
/**
 * topInfo.status:  0-未发布，1. 待反馈，2. 反馈正常，3. 反馈异常，-1. 已关闭
 * topInfo.sourcePath(创建方):   0- 采方, 1- 供方
 * $route.query.type: 0-查看 , 1- 编辑
 */
import bigDecimal from 'js-big-decimal'
export default {
  props: {
    entryType: {
      type: String,
      default: '1'
    },
    headerInfo: {
      type: Object,
      default: () => {}
    },
    entryId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isExpand: true,
      rules: {},
      typeOptions: [
        {
          text: this.$t('退货订单'),
          value: '0'
        },
        {
          text: this.$t('换货订单'),
          value: '1'
        },
        {
          text: this.$t('维修订单'),
          value: '2'
        }
      ],
      deliveryMethodOptions: [
        {
          text: this.$t('快递配送'),
          value: '1'
        },
        {
          text: this.$t('物流配送'),
          value: '2'
        },
        {
          text: this.$t('供方自提'),
          value: '3'
        }
      ],
      purTitle: '',
      supTitle: '',
      topInfo: {},
      isFC: false,
      isGF: false
    }
  },
  computed: {
    // 采方可以编辑备注 编辑和反馈状态 && 供方数据 && 状态为3
    isRemarkEditable() {
      return (
        (this.$route.query.type == 1 && this.topInfo.status == 1 && this.topInfo.sourcePath == 1) ||
        (this.topInfo.sourcePath == 0 && this.topInfo.status == 3)
        //
      )
    },

    // 供方可以编辑备注
    isFeedbackRemarkEditable() {
      return this.$route.query.type == 1 && this.topInfo.status == 1 && this.topInfo.sourcePath == 0
    },

    reconTotal() {
      return this.$store.state.reconTotal
    }
  },
  watch: {
    headerInfo(newVal) {
      if (newVal) {
        this.topInfo = {
          ...newVal
        }
        if (newVal.companyCode) {
          this.topInfo.company = newVal?.companyCode + '-' + newVal?.companyName
        }
        if (newVal.supplierCode) {
          this.topInfo.supplier = newVal?.supplierCode + '-' + newVal?.supplierName
        }
        if (newVal.siteCode) {
          this.topInfo.site = newVal?.siteCode + '-' + newVal?.siteName
        }
        if (
          newVal.businessTypeCode === 'BTTCL001' ||
          newVal.businessTypeCode === 'BTTCL002' ||
          newVal.businessTypeCode === 'BTTCL003'
        ) {
          this.isFC = true
        }
      }
    },
    reconTotal: {
      handler(newVal) {
        // 执行未税总金额 = 高低开 未税 + 对账单 汇总 执行未税
        this.topInfo.executeUntaxedTotalPrice = bigDecimal.add(newVal.openUnTax, newVal.detailUnTax)
        // 执行含税总金额 = 高低开 含税 + 对账单 汇总 执行含税
        this.topInfo.executeTaxedTotalPrice = bigDecimal.add(newVal.openTax, newVal.detailTax)
        // 税额 = 执行含税总金额 - 执行未税总金额
        this.topInfo.taxAmount = bigDecimal.subtract(
          this.topInfo.executeTaxedTotalPrice,
          this.topInfo.executeUntaxedTotalPrice
        )

        this.topInfo = JSON.parse(JSON.stringify(this.topInfo))
      },
      deep: true
      // immediate: true,
    }
  },
  mounted() {
    const currentBu = localStorage.getItem('currentBu')
    this.isGF = currentBu === 'GF'
  },
  methods: {
    remarkChange(type, v) {
      this[type] = this.topInfo[v]
    },
    goBack() {
      if (this.$route.query.fromType === 'query') {
        this.$router.push({
          name: 'statement-query'
        })
      } else {
        this.$router.push({
          name: 'statement-list'
        })
      }
    },
    saveData() {
      if (this.topInfo.executeUntaxedTotalPrice <= 0) {
        this.$toast({
          content: this.$t('执行未税总金额小于等于0，不能提交'),
          type: 'warning'
        })
        return false
      }
      const params = {
        id: this.topInfo.id,
        remark: this.topInfo.remark
      }
      this.updateStatementStatus(params)
    },
    //异常处理
    handleData() {
      this.$emit('exceptionHandle')
    },
    handleAccept() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认接受吗？')
        },
        success: () => {
          let params = {
            id: this.topInfo.id,
            remark: this.topInfo.remark // 采方反馈
          }
          this.$emit('handleBack', 'putFeedbackNormal', params)
        }
      })
    },
    handleReject() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认拒绝吗？')
        },
        success: () => {
          let params = {
            id: this.topInfo.id,
            remark: this.topInfo.remark
          }
          this.$emit('handleBack', 'putFeedbackAbnormal', params)
        }
      })
    },
    // 保存对账单(采方)
    updateStatementStatus(params) {
      this.$API.reconciliationSettlement.updateStatementStatus(params).then((res) => {
        if (res?.code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.goBack()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 0 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .mr20 {
      margin-right: 20px;
    }
    .status-box {
      padding: 2px 6px;
      border-radius: 2px;
      margin: 0 36px 0 10px;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      &-0 {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
      &-1 {
        color: #eda133;
        background: rgba(237, 161, 51, 0.1);
      }
      &-2 {
        color: #8acc40;
        background: rgba(138, 204, 64, 0.1);
      }
      &-3 {
        color: #ed5633;
        background: rgba(237, 86, 51, 0.1);
      }
      &--1 {
        color: #9a9a9a;
        background: rgba(154, 154, 154, 0.1);
      }
    }
    .middle-blank {
      flex: 1;
    }
    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    padding-bottom: 20px;
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin: 20px 20px 0 10px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
    /deep/.disabled-label .mt-form-item-topLabel .label {
      color: #9a9a9a;
    }
  }
}
</style>
