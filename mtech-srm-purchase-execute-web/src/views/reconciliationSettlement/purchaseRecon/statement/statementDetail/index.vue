<template>
  <!-- 采方-对账协同-采购对账单详情 -->
  <div class="full-height vertical-flex-box detail-fix-wrap">
    <top-info
      ref="topInfoRef"
      class="flex-keep"
      :entry-type="entryType"
      :header-info="headerInfo"
      :entry-id="entryId"
      @handleBack="handleBack"
      @exceptionHandle="exceptionHandle"
    ></top-info>

    <div class="bottom-tables">
      <mt-tabs
        :e-tab="false"
        tab-id="state-tab"
        :data-source="tabList"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
      <div class="flex-fit bottom-box">
        <!-- 对账明细 -->
        <div class="grid-wrap grid-wrap-page" v-show="currentTabInfo.code == 'reconciliationField'">
          <mt-template-page
            ref="stateDetailRef"
            :template-config="pageConfig1"
            @dataBound="handleDataBound"
            @handleClickToolBar="handleClickToolBar"
            @showFileBaseInfo="showFileBaseInfo"
          ></mt-template-page>
        </div>

        <!-- 相关附件 -->
        <relative-file
          ref="relativeFileRef"
          :is-view="relativeFileData.isView"
          :doc-id="relativeFileData.docId"
          :request-url-obj="relativeFileData.requestUrlObj"
          :module-file-list="relativeFileData.moduleFileList"
          @fileListData="getFileListData"
          v-show="currentTabInfo.code == 'reconciliationFile'"
        ></relative-file>

        <!-- 高低开 -->
        <div class="grid-wrap" v-show="currentTabInfo.code == 'highLowInfo'">
          <mt-template-page
            ref="dataGrid"
            @actionBegin="actionBegin"
            @actionComplete="actionComplete"
            @handleClickToolBar="handleClickToolBar1"
            :template-config="componentConfig"
          >
          </mt-template-page>
        </div>

        <!-- 总对账单 -->
        <div class="grid-wrap" v-show="currentTabInfo.code === 'collectReconciliationInvoice'">
          <collectReconciliationInvoice
            class="flex-fit"
            ref="collectReconRef"
            :data-source="collectReconDataSource"
          ></collectReconciliationInvoice>
        </div>

        <!-- 租赁费对账单 -->
        <div class="grid-wrap" v-show="currentTabInfo.code === 'rentalReconciliationInvoice'">
          <rentalFeeSummary
            class="flex-fit"
            ref="rentalFeeSummaryRef"
            :data-source="rentalFeeSummaryDataSource"
          ></rentalFeeSummary>
        </div>

        <!-- 操作日志 -->
        <operation-log
          v-if="currentTabInfo.code == 'operation'"
          :entry-code="headerInfo.reconciliationCode"
          :reconciliation-type="reconciliationType"
        ></operation-log>
      </div>
    </div>
    <!-- 需求附件弹窗 -->
    <uploader-dialog ref="uploaderDialog"></uploader-dialog>
    <!-- 导入文件弹框 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :is-show-tips="false"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>
<script>
import { formatTableColumnData, ColumnCheckbox, openColumnData, TabCode } from './config'
import { detailTableData } from './config/variable.js'
import { BASE_TENANT } from '@/utils/constant'
import { cloneDeep } from 'lodash'
import topInfo from './components/topInfo.vue'
import relativeFile from '@/components/businessComponents/relativeFile/index.vue'
import uploaderDialog from '@/components/Upload/uploaderDialog.vue'
import operationLog from '@/components/businessComponents/comOperationLog/index.vue'
import uploadExcelDialog from '@/components/Upload/uploadExcelDialog.vue'
import { download, getHeadersFileName } from '@/utils/utils'
import bigDecimal from 'js-big-decimal'
import { utils } from '@mtech-common/utils'
export default {
  components: {
    topInfo,
    relativeFile,
    uploaderDialog,
    operationLog,
    uploadExcelDialog,
    collectReconciliationInvoice: () =>
      import('./../../beRecon/createStatement/pages/collectReconciliationInvoice.vue'),
    rentalFeeSummary: () => import('./../../beRecon/createStatement/pages/rentalFeeSummary.vue')
  },
  data() {
    let statementData = JSON.parse(localStorage.getItem('statementData'))
    if (this.$route.query.fromType === 'query') {
      statementData = JSON.parse(localStorage.getItem('statementQueryData'))
    }
    return {
      isInEdit: false, // 处于行内编辑中
      btnFlag: '', // 按钮点击的类型（无论是否处于编辑状态）
      initDetailUnTax: 0, // 扣除高低开 执行未税
      initDetailTax: 0, // 扣除高低开 执行含税
      apiWaitingQuantity: 0, // 调用的api正在等待数
      TabCode,
      statementData,
      codeList: [
        'reconciliationField', // 对账明细code
        'reconciliationFile', // 相关附件code
        'operation', // 操作日志code
        'highLowInfo' // 高开 低开code
      ],
      relativeFileData: {
        docId: this.$route.query.id,
        requestUrlObj: {
          preUrl: 'invoiceCollaboration',
          saveUrl: 'putReconciliationHeaderSaveFile', // 将附件文件url保存到列表
          fileUrl: `${BASE_TENANT}/reconciliationHeader/queryFileByDocIdAndDocType` // 获取附件列表的url 根据docType和docI查询所有文件信息
        },
        moduleFileList: [],
        isView: false
      },
      uploadParams: {
        headerId: this.$route.query.id
      }, // 导入文件参数
      // 导入请求接口配置
      requestUrls: {
        templateUrlPre: 'reconciliationSettlement',
        uploadUrl: 'reconImport' // 上传接口方法名
      },
      currentTabInfo: {
        code: 'reconciliationField'
      },
      docId: this.$route.query.id,
      isView: true,
      pageSettings: {
        pageSize: 1,
        pageCount: 8,
        totalRecordsCount: 0,
        enableQueryString: true,
        pageSizes: [1, 10, 20, 50, 100, 200]
      },
      entryId: null,
      reconciliationType: 1,
      entryType: null, //1是编辑 3是详情 2是历史反馈
      headerInfo: statementData, // 头部数据
      tabList: [
        { title: this.$t('操作日志'), code: 'operation' }
        // { title: this.$t("高开/低开信息"), code: "highLowOpen" },
      ],
      pageConfig1: [],
      componentConfig: [],
      relativeFileList: null,
      collectReconDataSource: [], // 总对账单数据
      rentalFeeSummaryDataSource: [] // 租赁费对账单
    }
  },

  mounted() {
    this.handleDataBound = utils.debounce(this.handleDataBound, 1000)
    this.entryId = this.$route.query.id

    this.getDetailTab()
    // if (this.statementData.reconciliationTypeCode === 'GQCGQZ_530') {
    //   this.setDetailTab(mockInfo.default)
    // } else {
    //   this.getDetailTab()
    // }
    this.setHighLowConfig()

    if (this.statementData.reconciliationTypeCode === 'HRO') {
      this.getCollectRecon()
    }
  },

  activated() {
    this.statementData = JSON.parse(localStorage.getItem('statementData'))
    this.headerInfo = JSON.parse(localStorage.getItem('statementData'))
    if (this.$route.query.fromType === 'query') {
      this.statementData = JSON.parse(localStorage.getItem('statementQueryData'))
      this.headerInfo = JSON.parse(localStorage.getItem('statementQueryData'))
    }
    this.entryId = this.$route.query.id
    this.tabList = [
      { title: this.$t('操作日志'), code: 'operation' }
      // { title: this.$t("高开/低开信息"), code: "highLowOpen" },
    ]
    this.relativeFileData = {
      docId: this.$route.query.id,
      requestUrlObj: {
        preUrl: 'invoiceCollaboration',
        saveUrl: 'putReconciliationHeaderSaveFile', // 将附件文件url保存到列表
        fileUrl: `${BASE_TENANT}/reconciliationHeader/queryFileByDocIdAndDocType` // 获取附件列表的url 根据docType和docI查询所有文件信息
      },
      moduleFileList: [],
      isView: false // TODO 控制
    }

    this.getDetailTab()
    // if (this.statementData.reconciliationTypeCode === 'GQCGQZ_530') {
    //   this.setDetailTab(mockInfo.default)
    // } else {
    //   this.getDetailTab()
    // }
    this.setHighLowConfig()

    if (this.statementData.reconciliationTypeCode === 'HRO') {
      this.getCollectRecon()
    }
  },

  beforeDestroy() {
    if (this.$route.query.fromType === 'query') {
      localStorage.removeItem('statementQueryData')
    } else {
      localStorage.removeItem('statementData')
    }
  },
  methods: {
    // 表格数据绑定完成
    handleDataBound() {
      const currentViewRecords = this.$refs.stateDetailRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let workshopList = []
      let workProcessNameList = []
      currentViewRecords?.forEach((i) => {
        if (!workshopList.includes(i?.reconciliationHroResponse?.workshop)) {
          workshopList.push(i?.reconciliationHroResponse?.workshop)
        }
        if (!workProcessNameList.includes(i?.reconciliationHroResponse?.workProcessName)) {
          workProcessNameList.push(i?.reconciliationHroResponse?.workProcessName)
        }
      })
      this.componentConfig = [
        // 高低开配置
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置

          toolbar: [],
          // gridId: this.$tableUUID.reconciliationSettlement.statementList.highLowOpen,
          grid: {
            height: 'auto',
            columnData: openColumnData(
              this.statementData?.reconciliationTypeCode,
              workshopList,
              workProcessNameList,
              this.statementData?.companyCode
            ), // formatTableColumnData(ToBeReconciledDetailColumnData)
            dataSource: [],
            editSettings: {
              allowEditing:
                this.statementData.sourcePath == 0 &&
                this.statementData.status == 3 &&
                this.$route.query.type == 1
                  ? true
                  : false,
              allowAdding: true,
              allowDeleting: true,
              showDeleteConfirmDialog: false
            },
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliationHighLow/queryReconciliationHighLow`,
              defaultRules: [
                {
                  field: 'reconciliationId',
                  operator: 'equal',
                  value: this.$route.query.id
                }
              ],
              afterAsyncData: (res) => {
                if (res.code === 200 && res.data.records) {
                  let freePriceTotal = 0 // 高低开 未税总额
                  let openTaxTotal = 0 // 高低开 含税总额
                  res.data.records.forEach((item) => {
                    freePriceTotal = bigDecimal.add(freePriceTotal, item.freePrice)
                    openTaxTotal = bigDecimal.add(openTaxTotal, item.taxPrice)
                  })
                  this.initDetailUnTax = bigDecimal.subtract(
                    this.headerInfo.executeUntaxedTotalPrice - freePriceTotal
                  ) // 对账单 汇总 执行未税
                  this.initDetailTax = bigDecimal.subtract(
                    this.headerInfo.executeTaxedTotalPrice - openTaxTotal
                  ) // 对账单 汇总 执行含税税
                }
              }
            }
          }
        }
      ]
    },
    getCollectRecon() {
      // 获取对账明细的id
      // let reconciliationWaitIdList = this.statementData.idList
      const params = {
        // highLowList: highLowOpenData,
        reconciliationId: this.statementData.id
        // reconciliationWaitIdList
      }
      // 获取总对账单数据
      this.$API.reconciliationSettlement.reconciliationSummaryStatement(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.collectReconDataSource = data.itemList
        }
      })
      // 获取租赁费数据
      this.$API.reconciliationSettlement.rentalFeeSummaryStatement(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.rentalFeeSummaryDataSource = data.itemList
        }
      })
    },
    handleClickToolBar1(e) {
      const selectRows = e.grid.getSelectedRecords()
      let includesBtns = ['delete']
      if (selectRows.length === 0 && includesBtns.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // 如果新增时，有一行正在编辑。结束编辑后，再调用新增
      if (e.toolbar.id == 'add') {
        if (this.isInEdit) {
          this.btnFlag = e.toolbar.id
          this.endEdit()
        } else {
          this.handleAdd()
        }
      }
      if (e.toolbar.id == 'delete') {
        this.handleDelete()
      }
      if (e.toolbar.id == 'updateGrid') {
        this.endEdit()
      }
    },

    // 结束编辑状态
    endEdit() {
      const ref = this.$refs.dataGrid.getCurrentUsefulRef().gridRef.ejsRef
      ref.endEdit()
    },

    // 新增
    handleAdd() {
      const ref = this.$refs.dataGrid.getCurrentUsefulRef().gridRef.ejsRef
      ref.addRecord()
    },

    // 删除
    handleDelete() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认移除选中的数据？')
        },
        success: () => {
          const ref = this.$refs.dataGrid.getCurrentUsefulRef().gridRef.ejsRef
          ref.deleteRecord()
        }
      })
    },
    actionBegin(args) {
      console.log('actionBegin', args)
      // 记录开始 处于编辑状态
      if (args.requestType == 'add' || args.requestType == 'beginEdit') {
        this.isInEdit = true
      }
      if (args.requestType == 'refresh') {
        this.isInEdit = false // 结束编辑状态
      }

      if (args.requestType === 'add') {
        args.data.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
      }
    },

    actionComplete(args) {
      setTimeout(() => {
        if (['save', 'delete'].includes(args.requestType)) {
          this.isInEdit = false // 结束编辑状态

          // 如果新增时，有一行正在编辑。结束编辑后，再调用新增
          if (this.btnFlag == 'add') {
            this.handleAdd()
          }
          this.btnFlag = null
          // 计算执行未税/含税总价
          const ref = this.$refs.dataGrid?.getCurrentUsefulRef().gridRef?.ejsRef
          const _dataSource = ref?.getCurrentViewRecords() || []
          console.log(_dataSource)
          this.updateOpenTaxByTableData(_dataSource)
        }
      }, 10)
    },

    // 通过表格数据更新头部的执行含税、未税总额
    updateOpenTaxByTableData(tableData = []) {
      let freePriceTotal = 0 // 高低开 未税总额
      let openTaxTotal = 0 // 高低开 含税总额
      tableData.forEach((item) => {
        freePriceTotal = bigDecimal.add(freePriceTotal, item.freePrice)
        openTaxTotal = bigDecimal.add(openTaxTotal, item.taxPrice)
      })

      this.$store.commit('updateReconTotal', {
        detailUnTax: this.initDetailUnTax, // 对账单 汇总 执行未税
        detailTax: this.initDetailTax, // 对账单 汇总 执行含税
        openUnTax: Number(freePriceTotal), // 高低开 未税总额
        openTax: Number(openTaxTotal) // 高低开 含税总额
      })
    },
    setHighLowConfig() {
      if (
        this.headerInfo?.sourcePath == 0 &&
        this.headerInfo?.status == 3 &&
        this.$route.query?.type == 1
      ) {
        this.$set(this.componentConfig[0], 'toolbar', [
          {
            id: 'add',
            icon: 'icon_solid_Createorder',
            title: this.$t('新增')
          },
          {
            id: 'delete',
            icon: 'icon_solid_Closeorder',
            title: this.$t('删除')
          },
          {
            id: 'updateGrid',
            icon: 'icon_table_save',
            title: this.$t('更新')
          }
        ])
      } else {
        this.$set(this.componentConfig[0], 'toolbar', [])
      }
    },
    // 获取到tab和动态列
    getDetailTab() {
      const params = {
        businessTypeCode: this.headerInfo?.businessTypeCode,
        reconciliationTypeCode: this.headerInfo?.reconciliationTypeCode
      }
      this.$API.reconciliationSettlement.postReconciliationConfigFieldQuery(params).then((res) => {
        this.setDetailTab(res?.data)
      })
    },
    setDetailTab(res) {
      const dataList = res || []
      const preTab = []
      this.tabList = [{ title: this.$t('操作日志'), code: 'operation' }]
      dataList.forEach((itemTab) => {
        if (itemTab.code == TabCode.reconciliationField && itemTab.checkStatus) {
          // 对账明细
          this.formatDetail(itemTab)
          // // 获取明细表格数据
          // this.getDetailTableData();
        }
        if (itemTab.code == TabCode.reconciliationFile && itemTab.checkStatus) {
          // 相关附件
          this.formatFile(itemTab)
        }
        if (itemTab.checkStatus) {
          preTab.push({
            title: itemTab.name,
            code: itemTab.code
          })
        }
      })
      this.tabList = preTab.concat(this.tabList)
    },
    // 整合对账明细
    formatDetail(itemTab) {
      let cols = formatTableColumnData(itemTab.fieldResponseList, this.headerInfo.businessTypeCode)
      cols = [ColumnCheckbox].concat(cols)
      const toolbar = [
        {
          id: 'ExportRecon',
          icon: 'icon_table_print',
          title: this.$t('导出')
        }
      ]
      // if (this.$route.query.type == 1 && this.headerInfo?.status == 1) {
      //   // 如果表格可以编辑，导入文件解析，通过行号、物料凭证号设置解析后获得的备注
      //   toolbar.push({
      //     id: "ImportRecon",
      //     icon: "icon_solid_Import",
      //     title: this.$t("导入"),
      //   });
      // }
      const currentBu = localStorage.getItem('currentBu') || 'KT'
      this.pageConfig1 = [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar,
          // gridId: this.$tableUUID.reconciliationSettlement.statementList.reconciliationField,
          grid: {
            height: 'auto',
            columnData: cols,
            // dataSource: detailTableData,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliationItem/queryBuilder?BU_CODE=${currentBu}`,
              defaultRules: [
                {
                  field: 'headerId',
                  operator: 'equal',
                  value: this.$route.query.id
                }
              ]
            },
            allowPaging: true, // 分页
            editSettings: {
              allowEditing: this.$route.query.type == 1 && this.headerInfo?.status == 1
            }
          }
        }
      ]
    },

    // 整合 相关附件
    formatFile(itemTab) {
      this.relativeFileData.moduleFileList = []
      itemTab.fieldResponseList.forEach((item, index) => {
        if (item.code === TabCode.purHeaderFile && item.checkStatus) {
          // 设置 采方 整单附件的配置
          let btnRequired = {
            hasUpload: false,
            hasDownload: true,
            hasDelete: false
          }
          // 如果是采方反馈，则可以编辑
          if (
            (this.headerInfo.sourcePath == 1 &&
              this.headerInfo.status == 1 &&
              this.$route.query.type == 1) ||
            (this.headerInfo.sourcePath == 0 &&
              this.headerInfo.status == 3 &&
              this.$route.query.type == 1)
          ) {
            // 设置 采方 整单附件的配置
            btnRequired = {
              hasUpload: true,
              hasDownload: true,
              hasDelete: true
            }
          }
          this.relativeFileData.moduleFileList.push({
            id: 'reconciliation_header', // 选中时传给api的值
            nodeName: item.name, //"采方-整单附件", // 侧边栏名称
            nodeCode: index, // 即当前 index 用于选择左侧边栏时 获取当前项 配置
            btnRequired: btnRequired, // 表格 toolbar 配置（可选） hasUpload: Boolean、hasDownload: Boolean、 hasDelete: Boolean
            hasItem: false, // 是否显示附件行数据（可选）
            deleteFileUrl: `${BASE_TENANT}/reconciliationHeader/deleteById` // 删除文件使用的 API 根据ID删除
          })
        } else if (item.code === TabCode.supHeaderFile && item.checkStatus) {
          this.relativeFileData.moduleFileList.push({
            id: 'reconciliation_header_sup', // 选中时传给api的值
            nodeName: item.name, // "供方-整单附件", // 侧边栏名称
            nodeCode: index, // 即当前 index 用于选择左侧边栏时 获取当前项 配置
            btnRequired: {
              hasUpload: false,
              hasDownload: true,
              hasDelete: false
            }, // 表格 toolbar 配置（可选） hasUpload: Boolean、hasDownload: Boolean、 hasDelete: Boolean
            hasItem: false // 是否显示附件行数据（可选）
          })
        }
      })
    },
    //获取附件数据
    getFileListData(data) {
      this.relativeFileList = { ...data }
    },
    // 显示行附件弹窗
    showFileBaseInfo(e) {
      this.selectedRowIndex = e.index
      const dialogParams = {
        fileData: cloneDeep(e.value),
        isView: true, //是否可上传
        title: this.$t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },
    // tab切换
    handleSelectTab(e) {
      this.currentTabInfo = this.tabList[e]
    },

    refreshColumns() {
      this.$refs[`grid0`].refreshCurrentGridData()
    },
    goBack() {
      this.$router.push({
        name: 'statement-list'
      })
    },

    // 反馈 接受、拒绝
    handleBack(flag, params) {
      // 对账明细
      let idRemarkRequestList = []
      if (this.tabList.find((item) => item.code == this.codeList[0])) {
        try {
          if (this.$refs.stateDetailRef) {
            const detailRefData = this.$refs.stateDetailRef
              .getCurrentUsefulRef()
              .gridRef?.$refs.ejsRef.getCurrentViewRecords()
            if (detailRefData && detailRefData.length > 0) {
              detailRefData.forEach((item) => {
                idRemarkRequestList.push({
                  id: item.id,
                  remark: item.remark
                })
              })
            }
          }
        } catch (error) {
          console.warn(error)
        }
      }
      this.$API.reconciliationSettlement[flag]({
        ...params,
        idRemarkRequestList
      }).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$router.push({
            name: 'statement-list'
          })
        }
      })
    },
    //异常处理
    exceptionHandle() {
      const _flag = this.isInEdit
      // 如果高低开信息没完成，禁止提交
      if (_flag) {
        this.$toast({
          content: this.$t('请完善高低开信息'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交吗？')
        },
        success: () => {
          // 头部数据
          const topInfoData = this.$refs.topInfoRef.topInfo
          // 对账明细
          let reconciliationWaitIdList = []
          console.log(this.tabList)

          if (this.tabList.find((item) => item.code === TabCode.reconciliationField)) {
            const detailRefData =
              this.$refs.stateDetailRef
                ?.getCurrentUsefulRef()
                .gridRef?.ejsRef.getCurrentViewRecords() || []
            reconciliationWaitIdList = detailRefData.map((item) => {
              return item
            })
          }

          // 相关附件
          let fileList = []
          if (this.tabList.find((item) => item.code === TabCode.reconciliationFile)) {
            fileList =
              this.relativeFileList?.tab === 'reconciliation_header'
                ? this.relativeFileList?.data
                : [] // 采方获取采方附件
          }

          // 高低开信息
          let highLowList = []
          if (this.tabList.find((item) => item.code === TabCode.highLowInfo)) {
            const tmp =
              this.$refs.dataGrid
                ?.getCurrentUsefulRef()
                .gridRef?.$refs.ejsRef?.getCurrentViewRecords() || []
            // 格式化高低开信息
            tmp.forEach((item) => {
              highLowList.push({
                freePrice: item.freePrice, // 未税金额
                remark: item.remark, // 备注
                taxPrice: item.taxPrice, // 含税金额
                type: item.type // 类型
              })
            })
          }

          const params = {
            ...topInfoData, // 头部数据
            id: topInfoData.id,
            fileList, // 附件
            highLowList, // 高低开信息
            reconciliationWaitIdList
          }

          // 保存对账单
          this.apiStartLoading()
          this.$API.reconciliationSettlement
            .putReconciliationHeaderCreate(params)
            .then((res) => {
              this.apiEndLoading()
              if (res?.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
              }
              // 返回 待对账列表
              this.goBack()
            })
            .catch(() => {
              this.apiEndLoading()
            })
        }
      })
    },

    // ToolBar
    handleClickToolBar(args) {
      const { toolbar } = args
      if (toolbar.id === 'ImportRecon') {
        // 导入
        this.showUploadExcel(true)
      } else if (toolbar.id === 'ExportRecon') {
        // 导出
        this.postReconDownload()
      }
    },
    // 采购对账-对账单明细下载
    postReconDownload() {
      const params = {
        query: { headerId: this.$route.query.id },
        body: {},
        currentBu: localStorage.getItem('currentBu') || 'KT'
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlement.postReconDownload(params).then((res) => {
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 显示隐藏上传弹框
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 上传成功后
    upExcelConfirm(res) {
      this.showUploadExcel(false)

      const dataList = res?.data || []
      let detailRefData = [] // 对账明细表格数据
      try {
        if (this.$refs.stateDetailRef) {
          detailRefData =
            this.$refs.stateDetailRef
              .getCurrentUsefulRef()
              .gridRef?.$refs.ejsRef.getCurrentViewRecords() || []
        }
      } catch (error) {
        console.warn(error)
      }

      if (detailRefData?.length > 0 && dataList?.length > 0) {
        detailRefData.forEach((itemDetail) => {
          const currentLineNo = itemDetail.lineNo // 行号
          const currentReceiveCode = itemDetail.receiveCode // 物料凭证号
          const matchData = dataList.find((itemData) => {
            const itemLineNo = itemData.lineNo // 行号
            const itemReceiveCode = itemData.receiveCode // 物料凭证号
            if (itemLineNo === currentLineNo && itemReceiveCode === currentReceiveCode) {
              // 行号 && 物料凭证号 相同
              return itemData
            }
          })

          if (matchData) {
            itemDetail.remark = matchData.remark
          }
          try {
            this.$refs.stateDetailRef
              .getCurrentUsefulRef()
              .gridRef?.$refs.ejsRef.setRowData(itemDetail.id, itemDetail)
          } catch (error) {
            console.warn(error)
          }
        })
      }
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
    },
    // 获取表格数据
    getDetailTableData() {
      const params = {
        headerId: this.headerInfo.id
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .reconItemNoPageFlag(params)
        .then((res) => {
          this.apiEndLoading()
          const data = res?.data || []
          detailTableData.length = 0
          data.forEach((item) => {
            detailTableData.push(item)
          })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped>
.flex-fit {
  /deep/ .grid-container {
    overflow: auto;
    height: auto;
    flex: 1;
    > .mt-data-grid {
      height: 100%;

      > .e-grid {
        height: calc(100% - 42px);
      }
    }
  }
}

.mt-tabs {
  width: 100%;
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
</style>
