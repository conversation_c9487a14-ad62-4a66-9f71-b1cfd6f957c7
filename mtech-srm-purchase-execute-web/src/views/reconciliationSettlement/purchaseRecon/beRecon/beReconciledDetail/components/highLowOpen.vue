<template>
  <mt-template-page
    ref="dataGrid"
    :template-config="componentConfig"
    @handleClickToolBar="handleClickToolBar"
    @actionBegin="actionBegin"
    @actionComplete="actionComplete"
  >
  </mt-template-page>
</template>

<script>
import { OpenColumnData } from '../config/index'
import bigDecimal from 'js-big-decimal'

export default {
  data() {
    return {
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [
            [
              {
                id: 'add',
                icon: 'icon_solid_Createorder',
                title: this.$t('新增')
              },
              {
                id: 'delete',
                icon: 'icon_solid_Closeorder',
                title: this.$t('删除')
              },
              {
                id: 'cancel',
                icon: 'icon_table_delete',
                title: this.$t('取消编辑')
              },
              {
                id: 'updateGrid',
                icon: 'icon_table_save',
                title: this.$t('更新')
              }
            ]
          ],
          grid: {
            // lineIndex: 0,
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              showDeleteConfirmDialog: false
            },
            height: 'auto',
            allowPaging: false, // 不分页
            columnData: OpenColumnData, // formatTableColumnData(ToBeReconciledDetailColumnData)
            dataSource: []
          }
        }
      ],
      isInEdit: false, // 处于行内编辑中
      btnFlag: '' // 按钮点击的类型（无论是否处于编辑状态）
    }
  },
  methods: {
    handleClickToolBar(e) {
      const selectRows = e.grid.getSelectedRecords()
      let includesBtns = ['delete']
      if (selectRows.length === 0 && includesBtns.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // 如果新增时，有一行正在编辑。结束编辑后，再调用新增
      if (e.toolbar.id == 'add') {
        this.handleAdd()
        // if (this.isInEdit) {
        //   this.btnFlag = e.toolbar.id;
        //   this.endEdit();
        // } else {
        //   this.handleAdd();
        // }
      }
      if (e.toolbar.id == 'delete') {
        this.handleDelete()
        this.isInEdit = true
      }
      if (e.toolbar.id == 'cancel') {
        e.grid.closeEdit()
      }
      if (e.toolbar.id == 'updateGrid') {
        this.endEdit()
      }
    },

    actionBegin(args) {
      console.log('actionBegin', args)
      // 记录开始 处于编辑状态
      if (args.requestType == 'add' || args.requestType == 'beginEdit') {
        this.isInEdit = true
      }
      if (args.requestType == 'refresh') {
        this.isInEdit = false // 结束编辑状态
      }

      if (args.requestType === 'add') {
        args.data.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
      }
    },

    actionComplete(args) {
      console.log('actionComplete', args)
      if (args.data) {
        if (
          (args.data.freePrice > 0 && args.data.taxPrice < 0) ||
          (args.data.freePrice < 0 && args.data.taxPrice > 0)
        ) {
          this.$toast({
            content: this.$t('未税金额和含税金额正负不一致'),
            type: 'warning'
          })
          // 当出现错误时，指定行进入编辑状态
          this.$refs.dataGrid.getCurrentUsefulRef().gridRef.ejsRef.selectRow(args.rowIndex)
          this.$refs.dataGrid.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          setTimeout(() => {
            if (['save', 'delete'].includes(args.requestType)) {
              this.isInEdit = false // 结束编辑状态

              // 如果新增时，有一行正在编辑。结束编辑后，再调用新增
              if (this.btnFlag == 'add') {
                this.handleAdd()
              }
              this.btnFlag = null

              // 计算执行未税/含税总价
              const ref = this.$refs.dataGrid?.getCurrentUsefulRef().gridRef?.ejsRef
              const _dataSource = ref?.getCurrentViewRecords() || []
              this.updateOpenTaxByTableData(_dataSource)
            }
          }, 10)
        }
      }
    },
    // 通过表格数据更新头部的执行含税、未税总额
    updateOpenTaxByTableData(tableData = []) {
      let freePriceTotal = 0 // 高低开 未税总额
      let openTaxTotal = 0 // 高低开 含税总额
      tableData.forEach((item) => {
        freePriceTotal = bigDecimal.add(freePriceTotal, item.freePrice)
        openTaxTotal = bigDecimal.add(openTaxTotal, item.taxPrice)
      })

      this.$store.commit('updateReconTotal', {
        ...this.$store.state.reconTotal,
        openUnTax: Number(freePriceTotal), // 高低开 未税总额
        openTax: Number(openTaxTotal) // 高低开 含税总额
      })
    },

    // 结束编辑状态
    endEdit() {
      const ref = this.$refs.dataGrid.getCurrentUsefulRef().gridRef.ejsRef

      ref.endEdit()
    },

    // 新增
    handleAdd() {
      const ref = this.$refs.dataGrid.getCurrentUsefulRef().gridRef.ejsRef
      ref.addRecord()
    },

    // 删除
    handleDelete() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认移除选中的数据？')
        },
        success: () => {
          const ref = this.$refs.dataGrid.getCurrentUsefulRef().gridRef.ejsRef
          ref.deleteRecord()
        }
      })
    }
  }
}
</script>

<style></style>
