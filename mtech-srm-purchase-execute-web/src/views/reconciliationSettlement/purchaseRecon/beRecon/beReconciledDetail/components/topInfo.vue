<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" @click="doSubmit">{{
        $t('提交')
      }}</mt-button>

      <div class="sort-box" @click="doExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="topInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="company" :label="$t('公司')">
          <mt-input v-model="topInfo.company" :disabled="true" :placeholder="$t('公司')"></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplier" :label="$t('供应商')">
          <mt-input
            v-model="topInfo.supplier"
            :disabled="true"
            :placeholder="$t('供应商')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="site" :label="$t('工厂')">
          <mt-input v-model="topInfo.site" :disabled="true" :placeholder="$t('工厂')"></mt-input>
        </mt-form-item>

        <mt-form-item prop="currencyName" :label="$t('币种')">
          <mt-input
            v-model="topInfo.currencyName"
            :disabled="true"
            :placeholder="$t('币种')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="reconciliationTypeName" :label="$t('对账类型')">
          <mt-input
            v-model="topInfo.reconciliationTypeName"
            :disabled="true"
            :placeholder="$t('对账类型')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="executeTaxedTotalPrice" :label="$t('执行含税总金额')">
          <mt-input
            v-model="topInfo.executeTaxedTotalPrice"
            :disabled="true"
            :placeholder="$t('执行含税总金额')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="executeUntaxedTotalPrice" :label="$t('执行未税总金额')">
          <mt-input
            v-model="topInfo.executeUntaxedTotalPrice"
            :disabled="true"
            :placeholder="$t('执行未税总金额')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="taxAmount" :label="$t('税额')">
          <mt-input
            v-model="topInfo.taxAmount"
            :disabled="true"
            :placeholder="$t('税额')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="remark" :label="$t('采方备注')" class="full-width">
          <mt-input
            v-model="topInfo.remark"
            :disabled="entryType == ConstantType.Look"
            :placeholder="$t('采方备注')"
            max-length="200"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { ConstantType } from '../config/index.js'
import utils from '@/utils/utils'
import bigDecimal from 'js-big-decimal'

export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    },
    entryType: {
      type: String,
      default: () => '0' // 0: 新增、编辑 1: 查看
    }
  },
  data() {
    return {
      ConstantType,
      isExpand: true,
      rules: {},
      topInfo: {}
    }
  },
  watch: {
    headerInfo: {
      handler(newVal) {
        if (newVal) {
          this.topInfo = {
            ...newVal
          }
          if (newVal.companyCode) {
            this.topInfo.company = newVal?.companyCode + '-' + newVal?.companyName
          }
          if (newVal.supplierCode) {
            this.topInfo.supplier = newVal?.supplierCode + '-' + newVal?.supplierName
          }
          if (newVal.siteCode) {
            this.topInfo.site = newVal?.siteCode + '-' + newVal?.siteName
          }
        }
      },
      immediate: true
    },

    reconTotal: {
      handler(newVal) {
        // 执行未税总金额 = 高低开 未税 + 对账单 汇总 执行未税
        this.topInfo.executeUntaxedTotalPrice = bigDecimal.add(newVal.openUnTax, newVal.detailUnTax)
        // 执行含税总金额 = 高低开 含税 + 对账单 汇总 执行含税
        this.topInfo.executeTaxedTotalPrice = bigDecimal.add(newVal.openTax, newVal.detailTax)
        // 税额 = 执行含税总金额 - 执行未税总金额
        this.topInfo.taxAmount = bigDecimal.subtract(
          this.topInfo.executeTaxedTotalPrice,
          this.topInfo.executeUntaxedTotalPrice
        )

        this.topInfo = JSON.parse(JSON.stringify(this.topInfo))
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    reconTotal() {
      return this.$store.state.reconTotal
    }
  },
  mounted() {},
  filters: {
    dateFormat(value) {
      const date = new Date(Number(value))
      if (isNaN(date.getTime())) {
        return value
      } else {
        return utils.formateTime(date, 'YYYY-mm-dd HH:MM:SS')
      }
    }
  },
  methods: {
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 提交
    doSubmit() {
      this.$emit('doSubmit')
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
