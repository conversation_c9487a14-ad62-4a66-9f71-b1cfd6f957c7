import Vue from 'vue'
import utils from '@/utils/utils'
import { timeDate, requiredHeader } from './columnComponent'
import { i18n } from '@/main.js'

export const tableData = [] // 表格数据

// tab code
export const TabCode = {
  highLowInfo: 'highLowInfo', // 高低开信息
  operationLog: 'operationLog', // 操作日志
  reconciliationFile: 'reconciliationFile', // 相关附件
  reconciliationField: 'reconciliationField', // 对账明细
  purHeaderFile: 'purHeaderFile', // 采方-整单附件
  supHeaderFile: 'supHeaderFile' // 供方-整单附件
}

export const ConstantType = {
  Add: '1', // 新增
  Edit: '2', // 编辑
  Look: '3' // 查看
}

// 冻结标记
export const FrozenStatus = {
  markFreeze: 1, // 是
  unmark: 0 // 否
}

// 同步状态
export const SyncStatus = {
  synced: 1, // 是
  notSynced: 0 // 否
}

// 表格 checkbox
export const ColumnCheckbox = {
  width: '50',
  type: 'checkbox',
  showInColumnChooser: false
}

// data: yyyy-mm-dd hh:mm:ss
export const timeToDate = (data) => {
  const { formatString, value } = data
  if (formatString && value) {
    const date = new Date(value)
    if (isNaN(date.getTime())) {
      return value
    } else {
      return utils.formateTime(date, formatString)
    }
  } else {
    return value
  }
}

export const formatTableColumnData = (data) => {
  const colData = []
  // 待对账明细
  data.forEach((col) => {
    if (!col.checkStatus) return
    var defaultCol = {
      ...col,
      field: col.code,
      headerText: col.name,
      width: '150',
      allowEditing: false
    }
    if (defaultCol.field === 'frozenStatus') {
      // 冻结标记
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: FrozenStatus.markFreeze,
            text: i18n.t('冻结'),
            cssClass: 'col-active'
          },
          {
            value: FrozenStatus.unmark,
            text: i18n.t('取消标记'),
            cssClass: 'col-inactive'
          }
        ]
      }
    } else if (defaultCol.field === 'advanceInvoicing') {
      // 提前开票
      defaultCol.valueConverter = {
        type: 'map',
        map: {
          0: i18n.t('否'),
          1: i18n.t('是')
        }
      }
    } else if (defaultCol.field === 'syncStatus') {
      // 同步状态
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: SyncStatus.notSynced,
            text: i18n.t('未同步'),
            cssClass: 'col-notSynced'
          },
          {
            value: SyncStatus.synced,
            text: i18n.t('已同步'),
            cssClass: 'col-synced'
          }
        ]
      }
    } else if (defaultCol.field === 'fileBaseInfoList') {
      // 文件信息
      defaultCol.template = () => {
        return {
          template: Vue.component('fileBaseInfoList', {
            template: `<div @click="showFileBaseInfo" class="cell-operable-title">{{data.fileBaseInfoList | listNumFormat}}</div>`,
            data: function () {
              return { data: {} }
            },
            filters: {
              listNumFormat(value) {
                if (value && value.length > 0) {
                  return value.length
                } else {
                  return ''
                }
              }
            },
            methods: {
              showFileBaseInfo() {
                this.$parent.$emit('showFileBaseInfo', {
                  index: this.data.index,
                  value: this.data.fileBaseInfoList
                })
              }
            }
          })
        }
      }
    } else if (defaultCol.field == 'remark') {
      // 备注
      defaultCol.allowEditing = true
    } else if (defaultCol.field === 'sourceType') {
      // 来源类型（枚举） 0: 上游流入1:第三方接口
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('上游流入'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('第三方接口'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'receiveTime') {
      // 收货时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'status') {
      // 单据状态 0:待对账 1:已创建对账单
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('待对账'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('已创建对账单'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'prePayStatus') {
      // 是否预付 0-否；1-是
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('否'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('是'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'itemVoucherDate') {
      // 物料凭证日期
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'provisionalEstimateStatus') {
      // 是否暂估价 0-否；1-是 改成是否执行价 取值相反
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('是'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('否'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'createTime') {
      // 创建时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'updateTime') {
      // 最后修改时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'type') {
      // 待对账类型:0-采购待对账；1-销售待对账
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('采购待对账'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('销售待对账'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'realPriceStatus') {
      // 正式价标识 0-无正式价；1-有正式价
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('无正式价'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('有正式价'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'inOutType') {
      // 出入库类型 0-采购；1-销售；
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('采购'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('销售'),
            cssClass: ''
          }
        ]
      }
    }
    colData.push(defaultCol)
  })

  return colData
}

// 高开/低开
const TypeDropData = [
  {
    text: i18n.t('补差'),
    value: 0
  },
  {
    text: i18n.t('返利'),
    value: 1
  },
  {
    text: i18n.t('其他'),
    value: 2
  }
]

import { Query } from '@syncfusion/ej2-data'
export const OpenColumnData = [
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false,
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    headerText: 'addId主键',
    visible: false,
    isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。
    allowEditing: false
  },
  {
    width: '150',
    field: 'type',
    headerText: i18n.t('类型'), // 0-补差；1-返利；2-其他
    editType: 'dropdownedit',
    edit: {
      params: {
        allowFiltering: true,
        dataSource: TypeDropData,
        fields: { value: 'value', text: 'text' },
        query: new Query(),
        placeholder: i18n.t('请选择类型'),
        floatLabelType: 'Never',
        showClearButton: true,
        actionComplete: () => false
      }
    },
    headerTemplate: requiredHeader({
      headerText: i18n.t('类型')
    }),
    valueAccessor: function (field, data, column) {
      let dataSource = column.edit.params.dataSource || []
      return dataSource.filter((i) => i.value == data[field])?.[0]?.text
    },
    validationRules: { required: true }
  },
  {
    width: '150',
    field: 'freePrice',
    headerText: i18n.t('未税金额'),
    editType: 'numericedit',
    headerTemplate: requiredHeader({
      headerText: i18n.t('未税金额')
    }),
    validationRules: { required: true },
    edit: {
      params: {
        // min: 0,
      }
    }
  },
  {
    width: '150',
    field: 'taxPrice',
    headerText: i18n.t('含税金额'),
    editType: 'numericedit',
    headerTemplate: requiredHeader({
      headerText: i18n.t('含税金额')
    }),
    validationRules: { required: true },
    edit: {
      params: {
        // min: 0,
      }
    }
  },
  {
    width: '150',
    field: 'remark',
    headerTemplate: requiredHeader({
      headerText: i18n.t('备注')
    }),
    validationRules: { required: true }
  }
]
