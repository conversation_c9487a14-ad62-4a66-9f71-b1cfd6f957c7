<template>
  <!-- 采方-待对账结算-详情 -->
  <div class="full-height pt20 vertical-flex-box detail-fix-wrap">
    <!-- 头部信息 -->
    <top-info
      ref="topInfoRef"
      class="flex-keep"
      :header-info="headerInfo"
      :entry-type="entryType"
      @doSubmit="doSubmit"
      @goBack="goBack"
      @doExpand="doExpand"
    ></top-info>

    <div class="bottom-tables">
      <mt-tabs
        tab-id="berecon-tab"
        :e-tab="false"
        :data-source="tabList"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>

      <div class="bottom-box">
        <div
          class="flex-fit grid-wrap"
          v-show="currentTabInfo.code === TabCode.reconciliationField"
        >
          <mt-template-page
            ref="templateRef"
            :template-config="componentConfig"
            @handleClickToolBar="handleClickToolBar"
            @showFileBaseInfo="showFileBaseInfo"
          />
          <mt-page
            class="flex-keep custom-page"
            :page-settings="forecastPageSettings"
            :total-pages="forecastPageSettings.totalPages"
            @currentChange="handleCurrentChange"
            @sizeChange="handleSizeChange"
          />
        </div>

        <!-- 相关附件 -->
        <div class="flex-fit" v-show="currentTabInfo.code === TabCode.reconciliationFile">
          <relative-file ref="relativeFileRef" :module-file-list="moduleFileList"></relative-file>
        </div>

        <!-- 高低开 -->
        <div class="grid-wrap" v-show="currentTabInfo.code === TabCode.highLowInfo">
          <high-low-open class="flex-fit" ref="highLowRef"></high-low-open>
        </div>
      </div>
    </div>

    <!-- 附件弹窗 -->
    <uploader-dialog ref="uploaderDialog"></uploader-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import {
  formatTableColumnData,
  tableData,
  ColumnCheckbox,
  ConstantType,
  TabCode
} from './config/index'
import { nodeType } from '@/components/businessComponents/relativeFileNoDocId/config/relative.js'
import bigDecimal from 'js-big-decimal'

export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue'),
    RelativeFile: () => import('@/components/businessComponents/relativeFileNoDocId/index.vue'),
    UploaderDialog: () => import('@/components/Upload/uploaderDialog'),
    HighLowOpen: () => import('./components/highLowOpen.vue')
  },
  data() {
    const lastTabIndex = JSON.parse(localStorage.getItem('lastTabIndex'))
    const headerInfo = JSON.parse(localStorage.getItem('toBeReconciledTopInfo')) || {}

    return {
      TabCode,
      lastTabIndex, // 前一页面的 Tab index
      isShowRequireDetail: true, // 默认 tabindex 0，显示需求明细
      isShowRelativeFiles: false, // 默认 tabindex 0，不显示相关附件
      isShowHighLowOpen: false, // 默认 tabindex 0，不显示高开/低开信息
      apiWaitingQuantity: 0, // 调用的api正在等待数
      ConstantType,
      entryType: ConstantType.Add, // 页面状态
      headerInfo,
      actionType: {
        createStatement: 1, // 创建对账单
        markFreeze: 2, // 标记冻结
        unmark: 3 // 取消标记
      },
      forecastPageSettings: {
        pageCount: 5,
        pageSize: 10, // 当前每页数据量
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [10, 50, 100, 200]
      },
      forecastPageCurrent: 1, // 预测表格 当前页码
      tabList: [],
      moduleFileList: [], // 相关附件左侧边栏
      componentConfig: [], // 对账明细的列模板配置
      currentTabInfo: {} // 当前tab的数据
    }
  },
  mounted() {
    // 获取表格数据
    this.getTableData(this.headerInfo)
  },
  beforeDestroy() {
    this.$store.commit('updateReconTotal', {
      ...this.$store.state.reconTotal,
      openUnTax: 0, // 高低开 未税
      openTax: 0, // 高低开 含税
      detailUnTax: 0, // 对账单 汇总 执行未税
      detailTax: 0 // 对账单 汇总 执行含税
    })
    localStorage.removeItem('toBeReconciledTopInfo')
    localStorage.removeItem('lastTabIndex')
  },
  methods: {
    // 显示表格文件弹窗
    showFileBaseInfo(e) {
      const dialogParams = {
        fileData: cloneDeep(e.value),
        isView: true,
        title: this.$t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },
    doSubmit() {
      // 头部数据
      const topInfoData = this.$refs.topInfoRef.topInfo

      // 对账明细
      let reconciliationWaitIdList = []
      if (this.tabList.find((item) => item.code === TabCode.reconciliationField)) {
        const detailRefData =
          this.$refs.templateRef
            ?.getCurrentUsefulRef()
            ?.gridRef?.$refs.ejsRef?.getCurrentViewRecords() || []
        reconciliationWaitIdList = detailRefData.map((item) => {
          return {
            id: item.id,
            remark: item.remark
          }
        })
      }

      // 相关附件
      const uploadFiles = this.$refs.relativeFileRef.getUploadFlies('reconciliation_header')
      const fileList = this.formatUploadFiles(uploadFiles)

      // 高低开
      let highLowList = []
      const tmp =
        this.$refs.highLowRef?.$refs.dataGrid
          ?.getCurrentUsefulRef()
          .gridRef?.$refs.ejsRef.getCurrentViewRecords() || []
      // 格式化高低开信息
      tmp.forEach((item) => {
        highLowList.push({
          freePrice: item.freePrice, // 未税金额
          remark: item.remark, // 备注
          taxPrice: item.taxPrice, // 含税金额
          type: item.type // 类型
        })
      })

      const params = {
        ...topInfoData, // 头部数据
        id: undefined, // 创建时没有id
        fileList, // 附件
        reconciliationWaitIdList: reconciliationWaitIdList, // 对账明细备注
        highLowList // 高低开信息
      }
      // 保存对账单
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .putReconciliationHeaderCreate(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
          }
          // 返回 待对账列表
          this.goBack()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    goBack() {
      // 将 待对账列表-待对账 tab index 放到 localStorage 待对账列表 读
      localStorage.setItem('tabIndex', JSON.stringify(this.lastTabIndex))
      // 返回 待对账列表
      this.$router.push({
        name: 'be-reconciled',
        query: {}
      })
    },
    doExpand() {
      this.$refs.templateRef?.resetGridHeight && this.$refs.templateRef.resetGridHeight()
    },
    // 格式化附件信息
    formatUploadFiles(list) {
      const tmp = []
      list.forEach((item) => {
        tmp.push({
          fileName: item.fileName, //	文件上传名称
          fileSize: item.fileSize, //	文件大小
          fileType: item.fileType, //	文件类型
          sysFileId: item.id || item.sysFileId, //	文件ID(MT_WP_SYS_FILE表ID)
          url: item.url //	文件路径
        })
      })

      return tmp
    },
    // 点击 tab
    handleSelectTab(e) {
      this.currentTabInfo = this.tabList[e]
      console.log('分页-----', this.currentTabInfo)
    },
    // ToolBar
    handleClickToolBar(e) {
      const selectRows = e.grid.getSelectedRecords()
      const commonToolbar = [
        'more-option-btn',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]
      console.log('--------------------------', e)
      if (selectRows.length === 0 && !commonToolbar.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedIds = []
      selectRows.forEach((item) => {
        selectedIds.push(item.id)
      })

      if (e.toolbar.id === 'remove') {
        // 移除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认移除选中的数据？')
          },
          success: () => {
            selectedIds.forEach((id) => {
              for (let i = 0; i < tableData.length; i++) {
                if (tableData[i].id === id) {
                  tableData.splice(i, 1)
                  break
                }
              }
            })

            // 通过表格数据更新头部的含税、未税总金额
            this.updateReconTotalByTableData(tableData)
          }
        })
      }
    },
    // 通过表格数据更新头部的含税、未税总金额
    updateReconTotalByTableData(tableData = []) {
      // 含税、未税的总金额
      let untaxedTotal = 0 // 执行未税总金额
      let taxTotal = 0 // 执行含税总金额
      tableData.forEach((item) => {
        untaxedTotal = bigDecimal.add(untaxedTotal, item.executeUntaxedTotalPrice)
        taxTotal = bigDecimal.add(taxTotal, item.executeTaxedTotalPrice)
      })

      this.$store.commit('updateReconTotal', {
        ...this.$store.state.reconTotal,
        detailUnTax: Number(untaxedTotal), // 汇总明细行 执行未税总金额
        detailTax: Number(taxTotal) // 汇总明细行 执行含税总金额
      })
    },
    // 获取到tab和动态列
    getDetailTab() {
      let params = {
        businessTypeCode: this.headerInfo?.businessTypeCode,
        reconciliationTypeCode: this.headerInfo?.reconciliationTypeCode
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postReconciliationConfigFieldQuery(params)
        .then((res) => {
          this.apiEndLoading()
          const dataList = res?.data || []
          const preTab = []
          dataList.forEach((itemTab) => {
            if (itemTab.code === TabCode.reconciliationField && itemTab.checkStatus) {
              // 对账明细
              this.formatDetail(itemTab)
            } else if (itemTab.code === TabCode.reconciliationFile && itemTab.checkStatus) {
              // 相关附件
              this.formatFile(itemTab)
            }
            if (itemTab.checkStatus) {
              preTab.push({
                title: itemTab.name,
                code: itemTab.code
              })
            }
          })
          // preTab.push({
          //   title: this.$t("高开低开信息"),
          //   code: TabCode.highLowInfo,
          // });
          this.tabList = preTab
          this.currentTabInfo = this.tabList[0]
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },

    // 整合 相关附件
    formatFile(itemTab) {
      itemTab.fieldResponseList.forEach((item) => {
        if (item.code === TabCode.purHeaderFile && item.checkStatus) {
          // 采方-整单附件
          this.moduleFileList.push({
            code: item.code,
            id: 'reconciliation_header',
            nodeName: item.name,
            type: nodeType.mainUpdate
          })
        } else if (item.code === TabCode.supHeaderFile && item.checkStatus) {
          // 供方-整单附件
          this.moduleFileList.push({
            code: item.code,
            id: item.id,
            nodeName: item.name,
            type: nodeType.mainView
          })
        }
      })
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.forecastPageCurrent = currentPage
      this.getTableData(this.headerInfo)
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.forecastPageSettings.pageSize = pageSize
      this.getTableData(this.headerInfo)
    },
    // 获取表格数据
    getTableData(headerInfo) {
      const params = {
        businessTypeCode: headerInfo.businessTypeCode, // 业务类型code
        companyCode: headerInfo.companyCode, // 公司code
        siteCode: headerInfo.siteCode, // 工厂code
        supplierCode: headerInfo.supplierCode, // 供应商code
        reconciliationTypeCode: headerInfo.reconciliationTypeCode, // 对账类型code
        currencyCode: headerInfo.currencyCode, // 对账类型code
        page: {
          size: this.forecastPageSettings.pageSize,
          current: this.forecastPageCurrent
        }
      }
      // queryBuilder查询-待对账明细信息-不分页
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postReconciliationwaitQueryreconciliationwaitlist(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.data) {
            const total = res?.data?.total || 0
            this.forecastPageSettings.totalPages = Math.ceil(
              Number(total) / this.forecastPageSettings.pageSize
            )
            this.forecastPageSettings.totalRecordsCount = Number(total)
            tableData.length = 0
            res.data.records.forEach((item) => {
              tableData.push(item)
            })
            // 通过表格数据更新头部的含税、未税总金额
            this.updateReconTotalByTableData(tableData)
            // 获取到tab和动态列
            this.getDetailTab()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 整合对账明细
    formatDetail(itemTab) {
      let cols = formatTableColumnData(itemTab.fieldResponseList)
      cols = [ColumnCheckbox].concat(cols)
      this.componentConfig = [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 不使用组件中的toolbar配置
          toolbar: [
            [
              {
                id: 'remove',
                icon: 'icon_table_remove',
                title: this.$t('移除')
              }
            ]
          ],
          grid: {
            height: 'auto',
            editSettings: {
              allowEditing: true
            },
            allowPaging: false, // 不分页
            columnData: cols,
            dataSource: tableData
            // frozenColumns: 1,
          }
        }
      ]
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped>
.mt-tabs {
  width: 100%;
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
</style>
