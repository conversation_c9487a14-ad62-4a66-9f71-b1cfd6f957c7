<template>
  <mt-template-page
    ref="detailTemplate"
    class="frozenFistColumns"
    :template-config="componentConfig"
    @showFileBaseInfo="showFileBaseInfo"
    @handleClickToolBar="handleClickToolBar"
    @actionBegin="actionBegin"
    @actionComplete="actionComplete"
  >
  </mt-template-page>
</template>

<script>
import { cloneDeep } from 'lodash'
import bigDecimal from 'js-big-decimal'
export default {
  props: {
    componentConfig: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },

  methods: {
    // 显示表格文件弹窗
    showFileBaseInfo(e) {
      const dialogParams = {
        fileData: cloneDeep(e.value),
        isView: true,
        title: this.$t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },

    // ToolBar
    handleClickToolBar(e) {
      console.log('handleClickToolBar', e)
      const selectRows = e.grid.getSelectedRecords()
      const commonToolbar = [
        'more-option-btn',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]
      if (selectRows.length === 0 && !commonToolbar.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      if (e.toolbar.id == 'remove') {
        // 移除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认移除选中的数据？')
          },
          success: () => {
            e.grid.deleteRecord()
          }
        })
      }
    },

    actionBegin(args) {
      console.log('actionBegin', args)
    },

    actionComplete(args) {
      console.log('actionComplete', args)
      setTimeout(() => {
        if (['save', 'delete'].includes(args.requestType)) {
          // 计算执行未税/含税总价
          const ref = this.$refs.detailTemplate?.getCurrentUsefulRef().gridRef?.ejsRef
          const _dataSource = ref?.getCurrentViewRecords() || []
          this.updateReconTotalByTableData(_dataSource)
        }
      }, 10)
    },

    // 通过表格数据更新头部的含税、未税总金额
    updateReconTotalByTableData(tableData = []) {
      // 含税、未税的总金额
      let untaxedTotal = 0 // 未税总额
      let taxTotal = 0 // 含税总额
      tableData.forEach((item) => {
        untaxedTotal = bigDecimal.add(untaxedTotal, item.untaxedTotalPrice)
        taxTotal = bigDecimal.add(taxTotal, item.taxedTotalPrice)
      })

      this.$store.commit('updateReconTotal', {
        ...this.$store.state.reconTotal,
        detailUnTax: Number(untaxedTotal), // 未税总额
        detailTax: Number(taxTotal) // 含税总额
      })
    }
  }
}
</script>

<style></style>
