<template>
  <mt-template-page
    ref="dataGrid"
    :template-config="componentConfig"
    @handleClickToolBar="handleClickToolBar"
    @actionBegin="actionBegin"
    @actionComplete="actionComplete"
  >
  </mt-template-page>
</template>

<script>
import { OpenColumnData } from '../config/index'
import bigDecimal from 'js-big-decimal'

export default {
  data() {
    let workshopList = []
    let workProcessNameList = []
    let createData = JSON.parse(localStorage.getItem('createStatementData'))
    createData?.requireData?.forEach((i) => {
      if (!workshopList.includes(i?.reconciliationHroResponse?.workshop)) {
        workshopList.push(i?.reconciliationHroResponse?.workshop)
      }
      if (!workProcessNameList.includes(i?.reconciliationHroResponse?.workProcessName)) {
        workProcessNameList.push(i?.reconciliationHroResponse?.workProcessName)
      }
    })
    let reconciliationTypeCode = createData?.headerInfo?.reconciliationTypeCode
    return {
      reconciliationTypeCode,
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [
            [
              {
                id: 'add',
                icon: 'icon_solid_Createorder',
                title: this.$t('新增')
              },
              {
                id: 'cancelEdit',
                icon: 'icon_solid_Createorder',
                title: this.$t('取消编辑')
              },
              {
                id: 'delete',
                icon: 'icon_solid_Closeorder',
                title: this.$t('删除')
              },
              {
                id: 'updateGrid',
                icon: 'icon_table_save',
                title: this.$t('更新')
              }
            ]
          ],
          grid: {
            // lineIndex: 0,
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              showDeleteConfirmDialog: false
            },
            height: 'auto',
            allowPaging: false, // 不分页
            columnData: OpenColumnData(reconciliationTypeCode, workshopList, workProcessNameList), // formatTableColumnData(ToBeReconciledDetailColumnData)
            dataSource: []
          }
        }
      ],
      isInEdit: false, // 处于行内编辑中
      btnFlag: '' // 按钮点击的类型（无论是否处于编辑状态）
    }
  },
  mounted() {
    this.getCollectRecon()
  },
  methods: {
    getCollectRecon() {
      // 获取总对账单数据
      const ref = this.$refs.dataGrid?.getCurrentUsefulRef().gridRef?.ejsRef
      const _dataSource = ref?.getCurrentViewRecords() || []
      if (this.reconciliationTypeCode === 'HRO') {
        // 对账类型为人力外包时，重新请求总对账单
        this.$parent.getCollectRecon(_dataSource)
      }
    },
    handleClickToolBar(e) {
      const selectRows = e.grid.getSelectedRecords()
      let includesBtns = ['delete']
      if (selectRows.length === 0 && includesBtns.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // 如果新增时，有一行正在编辑。结束编辑后，再调用新增
      if (e.toolbar.id == 'add') {
        if (this.isInEdit) {
          this.btnFlag = e.toolbar.id
          this.endEdit()
        } else {
          this.handleAdd()
        }
      }
      if (e.toolbar.id == 'delete') {
        this.handleDelete()
      }
      if (e.toolbar.id == 'cancelEdit') {
        if (this.isInEdit === true) {
          this.$refs.dataGrid.refreshCurrentGridData()
        }
      }
      if (e.toolbar.id == 'updateGrid') {
        this.endEdit()
        if (this.isInEdit === false) {
          this.getCollectRecon()
        }
      }
    },

    actionBegin(args) {
      console.log('actionBegin', args)
      // 记录开始 处于编辑状态
      if (args.requestType == 'add' || args.requestType == 'beginEdit') {
        this.isInEdit = true
      }
      if (args.requestType == 'refresh') {
        this.isInEdit = false // 结束编辑状态
      }

      if (args.requestType === 'add') {
        args.data.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
      }
    },

    actionComplete(args) {
      console.log('actionComplete', args)
      const { requestType, data, action, rowIndex, index } = args
      // 人力外包情况下才校验
      if (requestType === 'save' && this.reconciliationTypeCode === 'HRO') {
        // 人力外包 高低开类型不为 0-补差；1-返利；2-其他 校验车间工序字段必填
        if (data.type !== 0 && data.type !== 1 && data.type !== 2) {
          if (!data.workshop) {
            this.$toast({ content: '请选择车间', type: 'warning' })
            if (action === 'add') {
              this.startEdit(index)
            } else if (action === 'edit') {
              this.startEdit(rowIndex)
            }
            return
          }
          if (!data.workProcessName) {
            this.$toast({ content: '请选择工序', type: 'warning' })
            if (action === 'add') {
              this.startEdit(index)
            } else if (action === 'edit') {
              this.startEdit(rowIndex)
            }
            return
          }
        }
      }
      setTimeout(() => {
        if (['save', 'delete'].includes(requestType)) {
          this.isInEdit = false // 结束编辑状态

          // 如果新增时，有一行正在编辑。结束编辑后，再调用新增
          if (this.btnFlag == 'add') {
            this.handleAdd()
          }
          this.btnFlag = null

          // 计算执行未税/含税总价
          const ref = this.$refs.dataGrid?.getCurrentUsefulRef().gridRef?.ejsRef
          const _dataSource = ref?.getCurrentViewRecords() || []
          this.updateOpenTaxByTableData(_dataSource)
          this.getCollectRecon()
        }
      }, 10)
    },
    startEdit(index) {
      this.$refs.dataGrid.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
      this.$refs.dataGrid.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
    },
    // 通过表格数据更新头部的执行含税、未税总额
    updateOpenTaxByTableData(tableData = []) {
      let freePriceTotal = 0 // 高低开 未税总额
      let openTaxTotal = 0 // 高低开 含税总额
      tableData.forEach((item) => {
        freePriceTotal = bigDecimal.add(freePriceTotal, item.freePrice)
        openTaxTotal = bigDecimal.add(openTaxTotal, item.taxPrice)
      })

      this.$store.commit('updateReconTotal', {
        ...this.$store.state.reconTotal,
        openUnTax: Number(freePriceTotal), // 高低开 未税总额
        openTax: Number(openTaxTotal) // 高低开 含税总额
      })
    },

    // 结束编辑状态
    endEdit() {
      const ref = this.$refs.dataGrid.getCurrentUsefulRef().gridRef.ejsRef

      ref.endEdit()
    },

    // 新增
    handleAdd() {
      const ref = this.$refs.dataGrid.getCurrentUsefulRef().gridRef.ejsRef
      ref.addRecord()
    },

    // 删除
    handleDelete() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认移除选中的数据？')
        },
        success: () => {
          const ref = this.$refs.dataGrid.getCurrentUsefulRef().gridRef.ejsRef
          ref.deleteRecord()
          this.getCollectRecon()
        }
      })
    }
  }
}
</script>

<style></style>
