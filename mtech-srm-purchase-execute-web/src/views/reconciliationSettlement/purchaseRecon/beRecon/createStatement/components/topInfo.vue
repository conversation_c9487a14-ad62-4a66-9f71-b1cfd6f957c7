<template>
  <div :class="['detail-top-info', !isExpand && 'detail-top-info-small']">
    <!-- 头部的内容 -->
    <div class="header-box">
      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" @click="doSubmit">{{
        $t('提交')
      }}</mt-button>

      <div class="sort-box" @click="doExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom" v-if="topInfo">
      <mt-form ref="ruleForm" :model="topInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="company" :label="$t('公司')">
          <mt-input v-model="topInfo.company" :disabled="true" :placeholder="$t('公司')"></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplier" :label="$t('供应商')">
          <mt-input
            v-model="topInfo.supplier"
            :disabled="true"
            :placeholder="$t('供应商')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="site" :label="$t('工厂')">
          <mt-input v-model="topInfo.site" :disabled="true" :placeholder="$t('工厂')"></mt-input>
        </mt-form-item>

        <mt-form-item prop="currencyName" :label="$t('币种')">
          <mt-input
            v-model="topInfo.currencyName"
            :disabled="true"
            :placeholder="$t('币种')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="executeUntaxedTotalPrice"
          :label="isFC ? $t('采购未税总金额') : $t('执行未税总金额')"
        >
          <mt-input
            v-model="topInfo.executeUntaxedTotalPrice"
            :disabled="true"
            :placeholder="isFC ? $t('采购未税总金额') : $t('执行未税总金额')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="taxAmount" :label="$t('税额')">
          <mt-input
            v-model="topInfo.taxAmount"
            :disabled="true"
            :placeholder="$t('税额')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="executeTaxedTotalPrice"
          :label="isFC ? $t('采购含税总金额') : $t('执行含税总金额')"
        >
          <mt-input
            v-model="topInfo.executeTaxedTotalPrice"
            :disabled="true"
            :placeholder="isFC ? $t('采购含税总金额') : $t('执行含税总金额')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="taxRate" :label="$t('税率')">
          <mt-input v-model="topInfo.taxRate" :disabled="true" :placeholder="$t('税率')"></mt-input>
        </mt-form-item>
        <mt-form-item
          prop="untaxedTotalPrice"
          :label="isFC ? $t('采购未税金额') : $t('入库未税金额')"
        >
          <mt-input
            v-model="topInfo.untaxedTotalPrice"
            :disabled="true"
            :placeholder="isFC ? $t('采购未税金额') : $t('入库未税金额')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item v-if="!isFC" prop="amountDifferentTotal" :label="$t('差异合计')">
          <mt-input
            v-model="topInfo.amountDifferentTotal"
            :disabled="true"
            :placeholder="$t('差异合计')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item v-if="!isFC" prop="rateDifferent" :label="$t('差异率')">
          <mt-input
            v-model="topInfo.rateDifferent"
            :disabled="true"
            :placeholder="$t('差异率')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="reconciliationTypeName" :label="$t('对账类型')">
          <mt-input
            v-model="topInfo.reconciliationTypeName"
            :disabled="true"
            :placeholder="$t('对账类型')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item v-if="isGF" prop="totalNumber" :label="$t('对账单总数量')">
          <mt-input
            v-model="topInfo.totalNumber"
            :disabled="true"
            :placeholder="$t('对账单总数量')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item v-if="isGF" prop="contactAddress" :label="$t('联系人地址')">
          <mt-input
            v-model="topInfo.contactAddress"
            :disabled="entryType == ConstantType.Look"
            :placeholder="$t('联系人地址')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item v-if="isGF" prop="partyAddress" :label="$t('甲方地址')">
          <mt-input
            v-model="topInfo.partyAddress"
            :disabled="entryType == ConstantType.Look"
            :placeholder="$t('甲方地址')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('采方备注')" class="full-width">
          <mt-input
            v-model="topInfo.remark"
            :disabled="entryType == ConstantType.Look"
            :placeholder="$t('采方备注')"
            max-length="200"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { ConstantType } from '../config/index.js'
import utils from '@/utils/utils'
import bigDecimal from 'js-big-decimal'

export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    },
    entryType: {
      type: String,
      default: () => '0' // 0: 新增、编辑 1: 查看
    }
  },
  data() {
    return {
      ConstantType,
      isExpand: true,
      rules: {},
      topInfo: {},
      isFC: false, // 是否为非采
      isGF: false // 是否光伏
    }
  },
  watch: {
    headerInfo: {
      handler(newVal) {
        if (newVal) {
          this.topInfo = {
            ...newVal
          }
          if (newVal.companyCode) {
            this.topInfo.company = newVal?.companyCode + '-' + newVal?.companyName
          }
          if (newVal.supplierCode) {
            this.topInfo.supplier = newVal?.supplierCode + '-' + newVal?.supplierName
          }
          if (newVal.siteCode) {
            this.topInfo.site = newVal?.siteCode + '-' + newVal?.siteName
          }
          if (
            newVal.businessTypeCode === 'BTTCL001' ||
            newVal.businessTypeCode === 'BTTCL002' ||
            newVal.businessTypeCode === 'BTTCL003'
          ) {
            this.isFC = true
          }
        }
      },
      immediate: true,
      deep: true
    },

    reconTotal: {
      handler(newVal) {
        // 执行未税总金额 = 高低开 未税 + 对账单 汇总 执行未税
        this.topInfo.executeUntaxedTotalPrice = bigDecimal.add(newVal.openUnTax, newVal.detailUnTax)
        // 执行含税总金额 = 高低开 含税 + 对账单 汇总 执行含税
        this.topInfo.executeTaxedTotalPrice = bigDecimal.add(newVal.openTax, newVal.detailTax)
        // 税额 = 执行含税总金额 - 执行未税总金额
        this.topInfo.taxAmount = bigDecimal.subtract(
          this.topInfo.executeTaxedTotalPrice,
          this.topInfo.executeUntaxedTotalPrice
        )

        this.topInfo = JSON.parse(JSON.stringify(this.topInfo))
      },
      deep: true
      // immediate: true,
    }
  },
  computed: {
    reconTotal() {
      return this.$store.state.reconTotal
    }
  },
  mounted() {
    const currentBu = localStorage.getItem('currentBu')
    this.isGF = currentBu === 'GF'
  },

  filters: {
    dateFormat(value) {
      const date = new Date(Number(value))
      if (isNaN(date.getTime())) {
        return value
      } else {
        return utils.formateTime(date, 'YYYY-mm-dd HH:MM:SS')
      }
    }
  },
  methods: {
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 提交
    doSubmit() {
      this.$emit('doSubmit')
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    }
  }
}
</script>
<style scoped lang="scss">
.detail-top-info .main-bottom .mt-form-item {
  width: calc(23% - 20px);
  min-width: 200px;
  display: inline-flex;
  margin-right: 20px;
}
.full-height .detail-top-info .header-box .sort-box {
  flex-direction: column;
  margin: 7px 20px 0;
  height: unset;
  .mt-icons {
    font-size: 12px;
    transform: scale(0.5);
    color: #00469c;
    margin-top: -6px;
  }
}
</style>
