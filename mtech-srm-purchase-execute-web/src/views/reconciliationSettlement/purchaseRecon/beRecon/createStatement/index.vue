<template>
  <!-- 采方-待对账结算-创建对账单 -->
  <div class="full-height pt20 vertical-flex-box detail-fix-wrap">
    <!-- 头部信息 -->
    <top-info
      class="flex-keep"
      ref="topInfoRef"
      :header-info="headerInfo"
      :entry-type="entryType"
      @doSubmit="doSubmit"
      @goBack="goBack"
      @doExpand="doExpand"
    ></top-info>

    <div class="bottom-tables">
      <mt-tabs
        tab-id="reconci-tab"
        :e-tab="false"
        :data-source="tabList"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>

      <div class="bottom-box">
        <!-- 对账明细 -->
        <div class="grid-wrap" v-show="currentTabInfo.code === TabCode.reconciliationField">
          <recon-details
            class="flex-fit"
            ref="reconDetailRef"
            :component-config="componentConfig"
          ></recon-details>
        </div>

        <!-- 相关附件 -->
        <div class="flex-fit" v-show="currentTabInfo.code === TabCode.reconciliationFile">
          <relative-file ref="relativeFileRef" :module-file-list="moduleFileList"></relative-file>
        </div>

        <!-- 高低开 -->
        <div class="grid-wrap" v-show="currentTabInfo.code === TabCode.highLowInfo">
          <high-low-open class="flex-fit" ref="highLowRef"></high-low-open>
        </div>

        <!-- 总对账单 -->
        <div
          class="grid-wrap"
          v-show="currentTabInfo.code === TabCode.collectReconciliationInvoice"
        >
          <collectReconciliationInvoice
            class="flex-fit"
            ref="collectReconRef"
            :data-source="collectReconDataSource"
          ></collectReconciliationInvoice>
        </div>

        <!-- 租赁费对账单 -->
        <div class="grid-wrap" v-show="currentTabInfo.code === TabCode.rentalReconciliationInvoice">
          <rentalFeeSummary
            class="flex-fit"
            ref="rentalFeeSummaryRef"
            :data-source="rentalFeeSummaryDataSource"
          ></rentalFeeSummary>
        </div>
      </div>
    </div>

    <!-- 附件弹窗 -->
    <uploader-dialog ref="uploaderDialog"></uploader-dialog>
  </div>
</template>

<script>
import { nodeType } from '@/components/businessComponents/relativeFileNoDocId/config/relative.js'
import { ColumnCheckbox, formatTableColumnData, TabCode } from './config/index'
import bigDecimal from 'js-big-decimal'
import { BASE_TENANT } from '@/utils/constant'
export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue'),
    ReconDetails: () => import('./pages/reconDetail.vue'),
    highLowOpen: () => import('./pages/highLowOpen.vue'),
    collectReconciliationInvoice: () => import('./pages/collectReconciliationInvoice.vue'),
    rentalFeeSummary: () => import('./pages/rentalFeeSummary.vue'),
    RelativeFile: () => import('@/components/businessComponents/relativeFileNoDocId/index.vue'),
    UploaderDialog: () => import('@/components/Upload/uploaderDialog')
  },
  data() {
    let createData = null
    let currentBu = localStorage.getItem('currentBu')
    if (this.isOverDue) {
      createData = JSON.parse(localStorage.getItem('overdueClearData'))
    } else {
      createData = JSON.parse(localStorage.getItem('createStatementData'))
      if (currentBu === 'GF') {
        createData.headerInfo.contactAddress = '广东省深圳市南山区中山园路1001号TCL国际E城F4栋5楼'
        createData.headerInfo.partyAddress = '浙江省宁波市北仑区七星路88号1幢501室A055'
      }
    }
    if (this.$route.query.come == 'clear') {
      createData = JSON.parse(localStorage.getItem('createStatementData'))
    }
    // 通过表格数据更新头部的含税、未税总金额
    // this.updateReconTotalByTableData(createData?.requireData);
    return {
      createData,
      TabCode,
      currentTabInfo: {}, // 当前tab的数据
      headerInfo: createData?.headerInfo, // 头部信息
      tabList: [],
      moduleFileList: [],
      entryType: createData?.entryType, // 页面状态
      lastTabIndex: 0, // 上一页面的 tabIndex，返回时传递参数
      apiWaitingQuantity: 0, // 调用的api正在等待数

      componentConfig: [], // 对账明细的列模板配置
      asyncParams: null,
      collectReconDataSource: [], // 总对账单数据
      rentalFeeSummaryDataSource: [] // 租赁费对账单
    }
  },
  computed: {
    isOverDue() {
      return this.$route.name === 'overdue-clear-detail' ? true : false
    },
    isBatchCreate() {
      return this.$route.query?.flag === 'all'
    }
  },
  mounted() {
    if (!this.isOverDue) {
      this.lastTabIndex = JSON.parse(localStorage.getItem('lastTabIndex')) ?? 0
    }

    this.getDetailTab()
    this.initTaxData()
    this.getAmount()
  },
  activated() {
    this.getDetailTab()
    this.initTaxData()
    this.getAmount()
  },
  beforeDestroy() {
    this.$store.commit('updateReconTotal', {
      ...this.$store.state.reconTotal,
      openUnTax: 0, // 高低开 未税
      openTax: 0, // 高低开 含税
      detailUnTax: 0, // 对账单 汇总 执行未税
      detailTax: 0 // 对账单 汇总 执行含税
    })

    if (this.isOverDue) {
      localStorage.removeItem('overdueClearData')
    } else {
      localStorage.removeItem('lastTabIndex')
      localStorage.removeItem('createStatementData')
      localStorage.removeItem('createStatementParams')
    }
  },
  methods: {
    getCollectRecon(highLowOpenData = null) {
      // 获取对账明细的id
      console.log('设置~~~', highLowOpenData)
      let reconciliationWaitIdList = this.createData.idList
      if (highLowOpenData) {
        // 高低开数据 下面调接口
        const params = {
          highLowList: highLowOpenData,
          // reconciliationId: 0,
          reconciliationWaitIdList
        }
        // 获取总对账单数据
        this.$API.reconciliationSettlement.reconciliationSummaryStatement(params).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.collectReconDataSource = data.itemList
          }
        })
        // 获取租赁费数据
        this.$API.reconciliationSettlement.rentalFeeSummaryStatement(params).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.rentalFeeSummaryDataSource = data.itemList
          }
        })
      }
    },
    initTaxData() {
      this.$store.commit('updateReconTotal', {
        openUnTax: 0, // 高低开 未税
        openTax: 0, // 高低开 含税
        detailUnTax: 0, // 对账单 汇总 执行未税
        detailTax: 0 // 对账单 汇总 执行含税
      })
    },
    // 通过接口获取的数据更新高低含税、未税金额
    updateReconTotalByRequest(untaxedTotal, taxTotal) {
      this.$store.commit('updateReconTotal', {
        ...this.$store.state.reconTotal,
        detailUnTax: Number(untaxedTotal), // 汇总明细行 执行未税总金额
        detailTax: Number(taxTotal) // 汇总明细行 执行含税总金额
      })
    },
    // 通过表格数据更新头部的含税、未税总金额
    updateReconTotalByTableData(tableData = []) {
      // 含税、未税的总金额
      let untaxedTotal = 0 // 执行未税总金额
      let taxTotal = 0 // 执行含税总金额
      tableData.forEach((item) => {
        untaxedTotal = bigDecimal.add(untaxedTotal, item.executeUntaxedTotalPrice)
        taxTotal = bigDecimal.add(taxTotal, item.executeTaxedTotalPrice)
      })

      this.$store.commit('updateReconTotal', {
        ...this.$store.state.reconTotal,
        detailUnTax: Number(untaxedTotal), // 汇总明细行 执行未税总金额
        detailTax: Number(taxTotal) // 汇总明细行 执行含税总金额
      })
    },

    getAmount() {
      if (this.isBatchCreate) {
        let _asyncParams = localStorage.getItem('createStatementParams')
        this.asyncParams = _asyncParams ? JSON.parse(_asyncParams) : {}
      }
      let params = this.getParams('init')
      if (this.$route.query.come == 'clear') {
        this.$API.reconciliationSettlement.getClearAmount(params).then((res) => {
          if (res.code === 200) {
            this.updateReconTotalByRequest(
              res.data.executeUntaxedTotalPrice,
              res.data.executeTaxedTotalPrice
            )
            this.headerInfo = Object.assign({}, this.headerInfo, {
              executeUntaxedTotalPrice: res.data.executeUntaxedTotalPrice,
              taxRate: res.data.taxRate,
              executeTaxedTotalPrice: res.data.executeTaxedTotalPrice,
              untaxedTotalPrice: res.data.untaxedTotalPrice,
              amountDifferentTotal: res.data.amountDifferentTotal,
              rateDifferent: res.data.rateDifferent,
              taxAmount: res.data.taxAmount
            })
          }
        })
      } else {
        params.currentBu = localStorage.getItem('currentBu') || 'KT'
        this.$API.reconciliationSettlement.getAmount(params).then((res) => {
          if (res.code === 200) {
            this.updateReconTotalByRequest(
              res.data.executeUntaxedTotalPrice,
              res.data.executeTaxedTotalPrice
            )
            this.headerInfo = Object.assign({}, this.headerInfo, {
              executeUntaxedTotalPrice: res.data.executeUntaxedTotalPrice,
              taxRate: res.data.taxRate,
              executeTaxedTotalPrice: res.data.executeTaxedTotalPrice,
              untaxedTotalPrice: res.data.untaxedTotalPrice,
              amountDifferentTotal: res.data.amountDifferentTotal,
              rateDifferent: res.data.rateDifferent,
              taxAmount: res.data.taxAmount,
              totalNumber: res.data.totalNumber
            })
          }
        })
      }
    },
    getParams(type) {
      const topInfoData =
        type === 'init' ? this.createData.headerInfo : this.$refs.topInfoRef?.topInfo
      if (this.$route.query.come == 'clear') {
        let createData = JSON.parse(localStorage.getItem('createStatementData'))
        let reconciliationWaitIdList = createData.idList.map((item) => {
          return {
            id: item,
            remark: ''
          }
        })
        const InfoData = type === 'init' ? createData.headerInfo : this.$refs.topInfoRef?.topInfo
        return {
          ...InfoData,
          createFlag: false,
          reconciliationWaitIdList
        }
      }
      if (this.isBatchCreate) {
        return {
          ...topInfoData,
          createFlag: true,
          queryBuilderDTO: this.asyncParams
        }
      }
      let reconciliationWaitIdList = this.createData.idList.map((item) => {
        return {
          id: item,
          remark: ''
        }
      })
      return {
        ...topInfoData,
        createFlag: false,
        reconciliationWaitIdList
      }
    },
    // 获取到tab和动态列
    getDetailTab() {
      let params = {
        businessTypeCode: this.headerInfo?.businessTypeCode,
        reconciliationTypeCode: this.headerInfo?.reconciliationTypeCode
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postReconciliationConfigFieldQuery(params)
        .then((res) => {
          this.apiEndLoading()
          const dataList = res?.data || []
          const preTab = []
          dataList.forEach((itemTab) => {
            if (itemTab.code === TabCode.reconciliationField && itemTab.checkStatus) {
              // 对账明细
              this.formatDetail(itemTab)
            } else if (itemTab.code === TabCode.reconciliationFile && itemTab.checkStatus) {
              // 相关附件
              this.formatFile(itemTab)
            }
            if (itemTab.checkStatus) {
              preTab.push({
                title: itemTab.name,
                code: itemTab.code
              })
            }
          })
          // preTab.push({
          //   title: this.$t("高开低开信息"),
          //   code: TabCode.highLowInfo,
          // });
          this.tabList = preTab
          this.currentTabInfo = this.tabList[0]
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },

    // 整合对账明细
    formatDetail(itemTab) {
      let cols = formatTableColumnData(itemTab.fieldResponseList, this.headerInfo.businessTypeCode)
      cols = [ColumnCheckbox].concat(cols)
      let _dataSource = this.createData?.requireData
      if (this.isBatchCreate) {
        this.componentConfig = [
          {
            useToolTemplate: false, // 不使用预置(新增、编辑、删除)
            useBaseConfig: false, // 不使用组件中的toolbar配置
            toolbar: [],
            grid: {
              height: 'auto',
              editSettings: {
                allowEditing: true,
                allowDeleting: true,
                showDeleteConfirmDialog: false
              },
              // allowPaging: , // 不分页
              columnData: cols,
              dataSource: [],
              asyncConfig: {
                url: `${BASE_TENANT}/reconciliationWait/queryBuilderItem`,
                ...this.asyncParams
              }
            }
          }
        ]
      } else {
        this.componentConfig = [
          {
            useToolTemplate: false, // 不使用预置(新增、编辑、删除)
            useBaseConfig: false, // 不使用组件中的toolbar配置
            toolbar: [],
            grid: {
              height: 'auto',
              editSettings: {
                allowEditing: true,
                allowDeleting: true,
                showDeleteConfirmDialog: false
              },
              // allowPaging: false, // 不分页
              columnData: cols,
              dataSource: _dataSource
            }
          }
        ]
      }
      console.log('放大水电费萨芬00000---------', cols)
    },

    // 整合 相关附件
    formatFile(itemTab) {
      this.moduleFileList = []
      itemTab.fieldResponseList.forEach((item) => {
        if (item.code === TabCode.purHeaderFile && item.checkStatus) {
          // 采方-整单附件
          this.moduleFileList.push({
            code: item.code,
            id: item.id,
            nodeName: item.name,
            type: nodeType.mainUpdate
          })
        } else if (item.code === TabCode.supHeaderFile && item.checkStatus) {
          // 供方-整单附件
          this.moduleFileList.push({
            code: item.code,
            id: item.id,
            nodeName: item.name,
            type: nodeType.mainView
          })
        }
      })
    },

    doSubmit() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交吗？')
        },
        success: () => {
          const _flag = this.$refs.highLowRef.isInEdit
          // 如果高低开信息没完成，禁止提交
          if (_flag) {
            this.$toast({
              content: this.$t('请完善高低开信息'),
              type: 'warning'
            })
            return
          }
          // 头部数据
          const topInfoData = this.$refs.topInfoRef.topInfo
          // 对账明细
          let reconciliationWaitIdList = []
          if (this.tabList.find((item) => item.code === TabCode.reconciliationField)) {
            const detailRefData =
              this.$refs.reconDetailRef?.$refs.detailTemplate
                ?.getCurrentUsefulRef()
                ?.gridRef?.$refs.ejsRef?.getCurrentViewRecords() || []
            reconciliationWaitIdList = detailRefData.map((item) => {
              return {
                id: item.id,
                remark: item.remark
              }
            })
          }

          // 相关附件
          let fileList = []
          if (this.tabList.find((item) => item.code === TabCode.reconciliationFile)) {
            let _uploadRefId = this.moduleFileList.find(
              (item) => item.code == TabCode.purHeaderFile
            )?.id
            const uploadFiles = this.$refs.relativeFileRef.getUploadFlies(_uploadRefId)
            fileList = this.formatUploadFiles(uploadFiles)
          }

          // 高低开信息
          let highLowList = []
          if (this.tabList.find((item) => item.code === TabCode.highLowInfo)) {
            const tmp =
              this.$refs.highLowRef?.$refs.dataGrid
                ?.getCurrentUsefulRef()
                .gridRef?.$refs.ejsRef?.getCurrentViewRecords() || []
            // 格式化高低开信息
            tmp.forEach((item) => {
              highLowList.push({
                freePrice: item.freePrice, // 未税金额
                remark: item.remark, // 备注
                taxPrice: item.taxPrice, // 含税金额
                type: item.type, // 类型
                workProcessName: item.workProcessName, // 工序
                workshop: item.workshop // 车间
              })
            })
          }
          const params = {
            ...topInfoData, // 头部数据
            createFlag: this.isBatchCreate ? true : false,
            id: undefined, // 创建时没有id
            fileList, // 附件
            reconciliationWaitIdList: this.isBatchCreate ? [] : reconciliationWaitIdList, // 待对账数据
            highLowList // 高低开信息
          }
          if (this.isBatchCreate) {
            params.queryBuilderDTO = this.asyncParams
          }
          if (this.isOverDue) params.reconciliationTypeCode = 'GQCGQZ_530'
          params.currentBu = localStorage.getItem('currentBu') || 'KT'
          console.log('我是提交的数据', params)
          // 保存对账单
          this.apiStartLoading()
          if (this.$route.query.come == 'clear') {
            this.$API.reconciliationSettlement
              .putReconciliationHeaderCreateSettled(params)
              .then((res) => {
                this.apiEndLoading()
                if (res?.code == 200) {
                  this.$toast({
                    content: this.$t('操作成功'),
                    type: 'success'
                  })
                }
                // 返回 待对账列表
                this.goBack()
              })
              .catch(() => {
                this.apiEndLoading()
              })
          } else {
            this.$API.reconciliationSettlement
              .putReconciliationHeaderCreate(params)
              .then((res) => {
                this.apiEndLoading()
                if (res?.code == 200) {
                  this.$toast({
                    content: this.$t('操作成功'),
                    type: 'success'
                  })
                }
                // 返回 待对账列表
                this.goBack()
              })
              .catch(() => {
                this.apiEndLoading()
              })
          }
        }
      })
    },
    goBack() {
      if (this.$route.query.come == 'clear') {
        this.$router.push({
          name: 'overdue-purchase-clear',
          query: {}
        })
      } else {
        // 将 tabIndex 放到 localStorage 待对账列表 读
        localStorage.setItem('tabIndex', JSON.stringify(this.lastTabIndex))
        // 返回 待对账列表
        this.$router.push({
          name: 'be-reconciled',
          query: {}
        })
      }
    },
    doExpand() {},
    // 格式化附件信息
    formatUploadFiles(list) {
      const tmp = []
      list.forEach((item) => {
        tmp.push({
          fileName: item.fileName, //	文件上传名称
          fileSize: item.fileSize, //	文件大小
          fileType: item.fileType, //	文件类型
          sysFileId: item.id || item.sysFileId, //	文件ID(MT_WP_SYS_FILE表ID)
          url: item.url, //	文件路径
          nodeCode: 'purHeaderFile'
        })
      })

      return tmp
    },
    // 点击 tab
    handleSelectTab(e) {
      this.currentTabInfo = this.tabList[e]
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped>
.mt-tabs {
  width: 100%;
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
</style>
