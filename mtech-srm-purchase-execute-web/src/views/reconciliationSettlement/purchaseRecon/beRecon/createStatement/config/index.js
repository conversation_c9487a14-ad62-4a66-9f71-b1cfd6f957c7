import { i18n } from '@/main.js'
import Vue from 'vue'
import { timeDate, requiredHeader } from './columnComponent'
import dayjs from 'dayjs'

export const ConstantType = {
  Add: '1', // 新增
  Edit: '2', // 编辑
  Look: '3' // 查看
}

// 冻结标记
export const FrozenStatus = {
  markFreeze: 1, // 是
  unmark: 0 // 否
}

// 表格 checkbox
export const ColumnCheckbox = {
  width: '50',
  type: 'checkbox',
  showInColumnChooser: false,
  allowEditing: false
}

// 同步状态
export const SyncStatus = {
  synced: 1, // 是
  notSynced: 0 // 否
}

// tab code
export const TabCode = {
  highLowInfo: 'highLowInfo', // 高低开信息
  operationLog: 'operationLog', // 操作日志
  reconciliationFile: 'reconciliationFile', // 相关附件
  reconciliationField: 'reconciliationField', // 对账明细
  collectReconciliationInvoice: 'collectReconciliationInvoice', // 总对账单
  rentalReconciliationInvoice: 'rentalReconciliationInvoice', // 租赁费对账单
  purHeaderFile: 'purHeaderFile', // 采方-整单附件
  supHeaderFile: 'supHeaderFile' // 供方-整单附件
}

// 待对账
export const ToBeReconciledColumnData = [
  {
    fieldCode: 'serialNumber',
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'companyCode',
    fieldName: i18n.t('公司编码')
  },
  {
    fieldCode: 'companyName',
    fieldName: i18n.t('公司名称')
  },
  {
    fieldCode: 'supplierCode',
    fieldName: i18n.t('供应商编码')
  },
  {
    fieldCode: 'supplierName',
    fieldName: i18n.t('供应商名称')
  }
]

// 待对账明细
export const ToBeReconciledDetailColumnData = [
  {
    fieldCode: 'frozenStatus',
    fieldName: i18n.t('冻结标记') // 0:否 1:是
  },
  {
    fieldCode: 'advanceInvoicing',
    fieldName: i18n.t('提前开票') // 0:否 1:是
  },
  {
    fieldCode: 'syncStatus',
    fieldName: i18n.t('同步状态') // 0:否 1:是
  },
  {
    fieldCode: 'businessTypeName',
    fieldName: i18n.t('业务类型')
  },
  {
    fieldCode: 'acceptanceCheckType',
    fieldName: i18n.t('验收类型')
  },
  {
    fieldCode: 'lineNo',
    fieldName: i18n.t('行号')
  },
  {
    fieldCode: 'receiveCode',
    fieldName: i18n.t('物料凭证/收货单号')
  },
  {
    fieldCode: 'sourceHeaderCode',
    fieldName: i18n.t('订单号') // 来源主单编码（订单）
  },
  {
    fieldCode: 'projectName',
    fieldName: i18n.t('项目名称')
  },
  {
    fieldCode: 'projectCode',
    fieldName: i18n.t('项目编号')
  },
  {
    fieldCode: 'relationContractCode',
    fieldName: i18n.t('合同编号')
  },
  {
    fieldCode: 'supplierCode',
    fieldName: i18n.t('供应商编号')
  },
  {
    fieldCode: 'supplierName',
    fieldName: i18n.t('供应商名称')
  },
  {
    fieldCode: 'itemCode',
    fieldName: i18n.t('物料/品项编码')
  },
  {
    fieldCode: 'itemName',
    fieldName: i18n.t('物料/品项名称')
  },
  {
    fieldCode: 'skuCode',
    fieldName: i18n.t('SKU编码')
  },
  {
    fieldCode: 'skuName',
    fieldName: i18n.t('SKU名称')
  },
  {
    fieldCode: 'spec',
    fieldName: i18n.t('规格型号')
  },
  {
    fieldCode: 'spec',
    fieldName: i18n.t('品类')
  },
  {
    fieldCode: 'requireName',
    fieldName: i18n.t('需求名称')
  },
  {
    fieldCode: 'requireDescription',
    fieldName: i18n.t('需求描述')
  },
  {
    fieldCode: 'assetsCategory',
    fieldName: i18n.t('资产类别')
  },
  {
    fieldCode: 'assetsCode',
    fieldName: i18n.t('资产编号')
  },
  {
    fieldCode: 'assetsCard',
    fieldName: i18n.t('资产卡片')
  },
  {
    fieldCode: 'customName',
    fieldName: i18n.t('客户')
  },
  {
    fieldCode: 'relationCustomOrder',
    fieldName: i18n.t('关联客户订单')
  },
  {
    fieldCode: 'quantity',
    fieldName: i18n.t('数量')
  },
  {
    fieldCode: 'untaxedUnitPrice',
    fieldName: i18n.t('未税单价')
  },
  {
    fieldCode: 'untaxedTotalPrice',
    fieldName: i18n.t('未税总价')
  },
  {
    fieldCode: 'taxedTotalPrice',
    fieldName: i18n.t('含税总价')
  },
  {
    fieldCode: 'termsUntaxedUnitPrice',
    fieldName: i18n.t('条件（未税）单价')
  },
  {
    fieldCode: 'payUntaxedTotalPrice',
    fieldName: i18n.t('应付未税总价')
  },
  {
    fieldCode: 'payTaxedTotalPrice',
    fieldName: i18n.t('应付含税总价')
  },
  {
    fieldCode: 'chargebackUntaxedPrice',
    fieldName: i18n.t('扣款未税金额')
  },
  {
    fieldCode: 'currencyName',
    fieldName: i18n.t('币种')
  },
  {
    fieldCode: 'companyCode',
    fieldName: i18n.t('公司编号')
  },
  {
    fieldCode: 'companyName',
    fieldName: i18n.t('公司名称')
  },
  {
    fieldCode: 'siteName',
    fieldName: i18n.t('地点/工厂')
  },
  {
    fieldCode: 'stockSite',
    fieldName: i18n.t('库存地点')
  },
  {
    fieldCode: 'costCenter',
    fieldName: i18n.t('成本中心')
  },
  {
    fieldCode: 'aaprofitCenteraaa',
    fieldName: i18n.t('利润中心')
  },
  {
    fieldCode: 'paymentMode',
    fieldName: i18n.t('付款方式')
  },
  {
    fieldCode: 'remark',
    fieldName: i18n.t('备注说明')
  },
  {
    fieldCode: 'receiveUserName',
    fieldName: i18n.t('收货人')
  },
  {
    fieldCode: 'receiveTime',
    fieldName: i18n.t('收货时间')
  },
  {
    fieldCode: 'fileBaseInfoList',
    fieldName: i18n.t('收货附件')
  }
]

export const formatTableColumnData = (data, businessTypeCode) => {
  let isFC = false
  if (
    businessTypeCode === 'BTTCL001' ||
    businessTypeCode === 'BTTCL002' ||
    businessTypeCode === 'BTTCL003'
  ) {
    isFC = true
  }
  const colData = []
  // 待对账明细
  data.forEach((col) => {
    if (!col.checkStatus) return
    var defaultCol = {
      ...col,
      field: col.code,
      headerText: col.name,
      width: '150',
      allowEditing: false
    }

    if (isFC) {
      // 非采情况下，字段名修改
      if (defaultCol.field === 'executeUntaxedUnitPrice') {
        // 付款未税单价
        defaultCol.headerText = i18n.t('付款未税单价')
      }
      if (defaultCol.field === 'executeUntaxedTotalPrice') {
        // 付款未税总价
        defaultCol.headerText = i18n.t('付款未税总价')
      }
      if (defaultCol.field === 'executeTaxedUnitPrice') {
        // 付款含税单价
        defaultCol.headerText = i18n.t('付款含税单价')
      }
      if (defaultCol.field === 'executeTaxedTotalPrice') {
        // 付款未税单价
        defaultCol.headerText = i18n.t('付款含税总价')
      }
      if (defaultCol.field === 'customProjectName') {
        // 付款未税单价
        defaultCol.headerText = i18n.t('寻源项目名称')
      }
    }

    if (defaultCol.field === 'advanceInvoicing') {
      // 提前开票
      defaultCol.valueConverter = {
        type: 'map',
        map: {
          0: i18n.t('否'),
          1: i18n.t('是')
        }
      }
    } else if (defaultCol.field === 'syncStatus') {
      // 同步状态
      defaultCol.valueConverter = {
        type: 'map',
        map: {
          [SyncStatus.notSynced]: i18n.t('未同步'),
          [SyncStatus.synced]: i18n.t('已同步')
        }
        // map: [
        //   {
        //     value: SyncStatus.notSynced,
        //     text: i18n.t("未同步"),
        //     cssClass: "col-notSynced",
        //   },
        //   {
        //     value: SyncStatus.synced,
        //     text: i18n.t("已同步"),
        //     cssClass: "col-synced",
        //   },
        // ],
      }
    } else if (defaultCol.field === 'fileBaseInfoList') {
      // 文件信息
      defaultCol.template = () => {
        return {
          template: Vue.component('fileBaseInfoList', {
            template: `<div @click="showFileBaseInfo" class="cell-operable-title">{{data.fileBaseInfoList | listNumFormat}}</div>`,
            data: function () {
              return { data: {} }
            },
            filters: {
              listNumFormat(value) {
                if (value && value.length > 0) {
                  return value.length
                } else {
                  return ''
                }
              }
            },
            methods: {
              showFileBaseInfo() {
                this.$parent.$emit('showFileBaseInfo', {
                  index: this.data.index,
                  value: this.data.fileBaseInfoList
                })
              }
            }
          })
        }
      }
    } else if (defaultCol.field == 'remark') {
      // 备注
      defaultCol.allowEditing = true
    } else if (defaultCol.field === 'sourceType') {
      // 来源类型（枚举） 0: 上游流入1:第三方接口
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('上游流入'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('第三方接口'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'receiveTime') {
      // 收货时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'status') {
      // 单据状态 0:待对账 1:已创建对账单
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('待对账'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('已创建对账单'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'prePayStatus') {
      // 是否预付 0-否；1-是
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('否'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('是'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'itemVoucherDate') {
      // 物料凭证日期
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'provisionalEstimateStatus') {
      // 是否暂估价 0-否；1-是 改成是否执行价
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('是'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('否'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'createTime') {
      // 创建时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'updateTime') {
      // 最后修改时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
    } else if (defaultCol.field === 'type') {
      // 待对账类型:0-采购待对账；1-销售待对账
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('采购待对账'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('销售待对账'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'realPriceStatus') {
      // 正式价标识 0-无正式价；1-有正式价
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('无正式价'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('有正式价'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'inOutType') {
      // 出入库类型 0-采购；1-销售；
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('采购'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('销售'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'priceType') {
      defaultCol.searchOptions = {
        renameField: `reconciliationHroResponse.${defaultCol.field}`
      }
      defaultCol.field = 'reconciliationHroResponse.priceType'
      // defaultCol.valueAccessor = (field, data) => {
      //   return data?.reconciliationHroResponse?.[defaultCol.field]
      // }
      defaultCol.ignore = true
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: '0',
            text: i18n.t('整机'),
            cssClass: ''
          },
          {
            value: '2',
            text: i18n.t('模组'),
            cssClass: ''
          },
          {
            value: '3',
            text: i18n.t('其他'),
            cssClass: ''
          }
        ]
      }
    } else if (
      defaultCol.field === 'modelCode' ||
      defaultCol.field === 'recoTime' ||
      // defaultCol.field === 'priceType' ||
      defaultCol.field === 'workshop' ||
      defaultCol.field === 'prodtLine' ||
      defaultCol.field === 'workProcessName' ||
      defaultCol.field === 'teamGroupCode' ||
      defaultCol.field === 'batchNo' ||
      defaultCol.field === 'settleModel' ||
      defaultCol.field === 'dailyOutput' ||
      defaultCol.field === 'materialCode' ||
      defaultCol.field === 'workOrder' ||
      defaultCol.field === 'moduleUnitPrice' ||
      defaultCol.field === 'cbuUnitPrice' ||
      defaultCol.field === 'otherUnitPrice' ||
      defaultCol.field === 'domainRentalFeeUnitPrice' ||
      defaultCol.field === 'equipmentRentalFeeUnitPrice' ||
      defaultCol.field === 'domainRentalFeeTotalPrice' ||
      defaultCol.field === 'equipmentRentalFeeTotalPrice' ||
      defaultCol.field === 'moduleDomainRentalFeeUnitPrice' ||
      defaultCol.field === 'moduleEquipmentRentalFeeUnitPrice' ||
      defaultCol.field === 'cbuDomainRentalFeeUnitPrice' ||
      defaultCol.field === 'cbuEquipmentRentalFeeUnitPrice' ||
      defaultCol.field === 'moduleDomainRentalFeeTotalPrice' ||
      defaultCol.field === 'moduleEquipmentRentalFeeTotalPrice' ||
      defaultCol.field === 'cbuDomainRentalFeeTotalPrice' ||
      defaultCol.field === 'cbuEquipmentRentalFeeTotalPrice' ||
      defaultCol.field === 'settleUnitPriceUntaxed' ||
      defaultCol.field === 'settleUnitPriceTaxed' ||
      defaultCol.field === 'settleTotalPriceUntaxed' ||
      defaultCol.field === 'settleTotalPriceTaxed' ||
      defaultCol.field === 'shareTotalPrice'
    ) {
      // 机型编码(ID)、对账时间、价格类型、车间、工序、班组、批次号、结算机型、日产量、物料编码、工单、模组单价、整机单价、其他单价、场地租赁费单价、设备租赁费单价、场地租赁费总价、设备租赁费总价、模组场地租赁费单价、模组设备租赁费单价、整机场地租赁费单价、整机设备租赁费单价、模组场地租赁费总价、模组设备租赁费总价、整机场地租赁费总价、整机设备租赁费总价、结算未税单价、结算含税单价、结算未税总价、结算含税总价、分摊总价
      defaultCol.searchOptions = {
        renameField: `reconciliationHroResponse.${defaultCol.field}`
      }
      defaultCol.valueAccessor = (field, data) => {
        if (defaultCol.field === 'recoTime') {
          return dayjs(Number(data?.reconciliationHroResponse?.[defaultCol.field])).format(
            'YYYY-MM-DD HH:mm:ss'
          )
        }
        if (defaultCol.field === 'priceType') {
          const priceType = [
            {
              value: '0',
              text: i18n.t('整机'),
              cssClass: ''
            },
            {
              value: '2',
              text: i18n.t('模组'),
              cssClass: ''
            },
            {
              value: '3',
              text: i18n.t('其他'),
              cssClass: ''
            }
          ]
          // return data?.reconciliationHroResponse?.[defaultCol.field]
          return priceType.filter(
            (i) => i.value === data?.reconciliationHroResponse?.[defaultCol.field]
          )[0]?.['text']
        }
        return data?.reconciliationHroResponse?.[defaultCol.field]
      }
      defaultCol.ignore = true
    }
    colData.push(defaultCol)
  })

  return colData
}

// 高开/低开
const TypeDropData = [
  {
    text: i18n.t('补差'),
    value: 0
  },
  {
    text: i18n.t('返利'),
    value: 1
  },
  {
    text: i18n.t('其他'),
    value: 2
  },
  {
    text: i18n.t('应付返工费用'),
    value: 3
  },
  {
    text: i18n.t('应扣住宿费用'),
    value: 4
  },
  {
    text: i18n.t('应扣考核费用'),
    value: 5
  },
  {
    text: i18n.t('外包借入工时费用'),
    value: 6
  },
  {
    text: i18n.t('外包借出工时费用'),
    value: 7
  },
  {
    text: i18n.t('损耗率返利'),
    value: 8
  }
]
import { Query } from '@syncfusion/ej2-data'

export const OpenColumnData = (reconciliationTypeCode, workshopList, workProcessNameList) => [
  {
    width: '50',
    type: 'checkbox',
    allowEditing: false,
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
    headerText: 'addId主键',
    visible: false,
    isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。
    allowEditing: false
  },
  {
    width: '150',
    field: 'type',
    headerText: i18n.t('类型'), // 0-补差；1-返利；2-其他
    editType: 'dropdownedit',
    edit: {
      params: {
        allowFiltering: true,
        dataSource: TypeDropData,
        fields: { value: 'value', text: 'text' },
        query: new Query(),
        placeholder: i18n.t('请选择类型'),
        floatLabelType: 'Never',
        showClearButton: true,
        actionComplete: () => false
      }
    },
    valueAccessor: function (field, data, column) {
      let dataSource = column.edit.params.dataSource || []
      return dataSource.filter((i) => i.value == data[field])?.[0]?.text
    },
    headerTemplate: requiredHeader({
      headerText: i18n.t('类型')
    }),
    validationRules: { required: true }
  },
  {
    width: '150',
    field: 'freePrice',
    headerText: i18n.t('未税金额'),
    editType: 'numericedit',
    edit: {
      params: {
        // min: 0,
        decimals: 2
      }
    },
    headerTemplate: requiredHeader({
      headerText: i18n.t('未税金额')
    }),
    validationRules: { required: true }
  },
  {
    width: '150',
    field: 'taxPrice',
    headerText: i18n.t('含税金额'),
    editType: 'numericedit',
    edit: {
      params: {
        // min: 0,
        decimals: 2
      }
    },
    headerTemplate: requiredHeader({
      headerText: i18n.t('含税金额')
    }),
    validationRules: { required: true }
  },
  {
    width: '150',
    field: 'remark',
    headerTemplate: requiredHeader({
      headerText: i18n.t('备注')
    }),
    validationRules: { required: true }
  },
  {
    width: '150',
    field: 'workshop',
    headerText: i18n.t('车间'),
    editType: 'dropdownedit',
    edit: {
      params: {
        allowFiltering: true,
        dataSource: workshopList,
        // fields: { value: 'value', text: 'text' },
        query: new Query(),
        placeholder: i18n.t('请选择车间'),
        floatLabelType: 'Never',
        showClearButton: true,
        actionComplete: () => false
      }
    },
    // valueAccessor: function (field, data, column) {
    //   let dataSource = column.edit.params.dataSource || []
    //   return dataSource.filter((i) => i.value == data[field])?.[0]?.text
    // },
    headerTemplate: requiredHeader({
      headerText: i18n.t('车间')
    }),
    validationRules: { required: true },
    visible: reconciliationTypeCode === 'HRO' ? true : false
  },
  {
    width: '150',
    field: 'workProcessName',
    headerText: i18n.t('工序'),
    editType: 'dropdownedit',
    edit: {
      params: {
        allowFiltering: true,
        dataSource: workProcessNameList,
        // fields: { value: 'value', text: 'text' },
        query: new Query(),
        placeholder: i18n.t('请选择工序'),
        floatLabelType: 'Never',
        showClearButton: true,
        actionComplete: () => false
      }
    },
    // valueAccessor: function (field, data, column) {
    //   let dataSource = column.edit.params.dataSource || []
    //   return dataSource.filter((i) => i.value == data[field])?.[0]?.text
    // },
    headerTemplate: requiredHeader({
      headerText: i18n.t('工序')
    }),
    validationRules: { required: true },
    visible: reconciliationTypeCode === 'HRO' ? true : false
  }
]

export const collectReconciliationInvoiceColumnData = [
  {
    field: 'serialNumber',
    headerText: i18n.t('序号')
  },
  {
    width: '150',
    field: 'projectName',
    headerText: i18n.t('项目')
  },
  {
    width: '150',
    field: 'workshop',
    headerText: i18n.t('车间')
  },
  {
    width: '150',
    field: 'apAmtUntaxed',
    headerText: i18n.t('应付不含税金额')
  },
  {
    width: '150',
    field: 'apReworkCost',
    headerText: i18n.t('应付返工费用')
  },
  {
    width: '150',
    field: 'netDeductionStayCost',
    headerText: i18n.t('应扣住宿费用')
  },
  {
    width: '150',
    field: 'netDeductionExamineCost',
    headerText: i18n.t('应扣考核费用')
  },
  {
    width: '150',
    field: 'wastageRateRebate',
    headerText: i18n.t('损耗率返利')
  },
  {
    width: '150',
    field: 'outsourceInManHourCost',
    headerText: i18n.t('外包借入工时费用')
  },
  {
    width: '150',
    field: 'outsourceLendingWorkHourCost',
    headerText: i18n.t('外包借出工时费用')
  },
  {
    width: '150',
    field: 'otherCost',
    headerText: i18n.t('其余费用')
  },
  // {
  //   width: '150',
  //   field: 'apAmtTaxed',
  //   headerText: i18n.t('应付含税金额')
  // },
  {
    width: '150',
    field: 'taxes',
    headerText: i18n.t('税费')
  },
  {
    width: '150',
    field: 'apOutsourceTaxed',
    headerText: i18n.t('应付含税外包费')
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
export const rentalFeeSummaryColumnData = [
  {
    field: 'serialNumber',
    headerText: i18n.t('序号')
  },
  {
    width: '150',
    field: 'projectName',
    headerText: i18n.t('项目')
  },
  {
    width: '150',
    field: 'workshop',
    headerText: i18n.t('车间')
  },
  {
    width: '150',
    field: 'domainRentalFeeUntaxed',
    headerText: i18n.t('场地租赁费（不含税）')
  },
  {
    width: '150',
    field: 'equipmentRentalFeeUntaxed',
    headerText: i18n.t('设备/线体租赁费（不含税）')
  },
  {
    width: '150',
    field: 'domainRentalTax',
    headerText: i18n.t('场地租赁税费（5%）')
  },
  {
    width: '150',
    field: 'equipmentRentalTax',
    headerText: i18n.t('设备/线体租赁税费（13%）')
  },
  {
    width: '150',
    field: 'receivableRentalFeeTaxed',
    headerText: i18n.t('应收含税租赁费')
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
