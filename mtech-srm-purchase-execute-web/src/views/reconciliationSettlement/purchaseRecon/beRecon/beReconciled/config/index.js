import Vue from 'vue'
import { i18n } from '@/main.js'
import { timeDate } from './columnComponent'
import { MasterDataSelect } from '@/utils/constant'
import { formatMasterFilter, judgeFormatCodeName } from '@/utils/utils'
import dayjs from 'dayjs'

export const ConstantType = {
  Add: '1', // 新增
  Edit: '2', // 编辑
  Look: '3' // 查看
}

// 动态 Tab 的 Toolbar
export const AsyncTabToolbar = [
  {
    id: 'DetailCreateStatement',
    icon: 'icon_solid_Createorder',
    title: i18n.t('创建对账单'),
    permission: ['O_02_0396']
  },
  // {
  //   id: "BatchDetailCreateStatement",
  //   icon: "icon_solid_Createorder",
  //   title: i18n.t("批量创建对账单"),
  //   permission: ["O_02_0396"],
  // },
  {
    id: 'MarkFreeze',
    icon: 'a-icon_table_Markfreeze',
    title: i18n.t('冻结'),
    permission: ['O_02_1175']
  },
  {
    id: 'Unmark',
    icon: 'a-icon_table_Unmarkfreezing',
    title: i18n.t('取消冻结'),
    permission: ['O_02_1176']
  },
  {
    id: 'SynchronizeSuppliers',
    icon: 'icon_table_synchronize',
    title: i18n.t('同步供应商'),
    permission: ['O_02_0397']
  },
  {
    id: 'ExcelExport',
    icon: 'icon_solid_export',
    title: i18n.t('导出')
    // permission: ["O_02_0397"],
  }
]

export const Tab = {
  toBeReconciled: 0 // 待对账
}

// 冻结标记
export const FrozenStatus = {
  markFreeze: 1, // 是
  unmark: 0 // 否
}
// 冻结标记 Options
export const FrozenStatusOptions = [
  {
    value: FrozenStatus.markFreeze,
    text: i18n.t('已冻结'),
    cssClass: 'col-inactive'
  },
  {
    value: FrozenStatus.unmark,
    text: i18n.t('未冻结'),
    cssClass: 'col-active'
  }
]

// 同步状态
export const SyncStatus = {
  synced: 1, // 是
  notSynced: 0 // 否
}

// 待对账
export const ToBeReconciledColumnData = [
  {
    fieldCode: 'businessTypeName',
    fieldName: i18n.t('业务类型')
  },
  {
    fieldCode: 'reconciliationTypeName',
    fieldName: i18n.t('对账类型')
  },
  {
    width: '250',
    fieldCode: 'currencyCode',
    fieldName: i18n.t('币种'),
    searchOptions: MasterDataSelect.moneyType,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.currencyCode, data?.currencyName)
    }
  },
  {
    width: '250',
    fieldCode: 'companyCode',
    fieldName: i18n.t('公司'),
    searchOptions: MasterDataSelect.businessCompany,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.companyCode, data?.companyName)
    }
  },
  // {
  //   fieldCode: "companyName",
  //   fieldName: i18n.t("公司名称"),
  // },
  {
    width: '250',
    fieldCode: 'supplierCode',
    fieldName: i18n.t('供应商'),
    searchOptions: MasterDataSelect.supplierAll,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  },
  // {
  //   fieldCode: "supplierName",
  //   fieldName: i18n.t("供应商名称"),
  // },
  {
    width: '250',
    fieldCode: 'siteCode',
    fieldName: i18n.t('工厂'),
    searchOptions: MasterDataSelect.factoryAddress,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.siteCode, data?.siteName)
    }
  },
  // {
  //   fieldCode: "siteName",
  //   fieldName: i18n.t("工厂名称"),
  // },
  {
    fieldCode: 'dataLength',
    fieldName: i18n.t('详情明细'),
    ignore: true
  }
]

export const formatTableColumnData = (config) => {
  const { tab, data } = config
  const colData = []

  if (Tab.toBeReconciledDetail === tab) {
    // 待对账明细

    // 数组开头固定添加 "冻结状态" 列
    data.unshift({
      code: 'frozenStatus',
      name: i18n.t('冻结状态')
    })

    data.forEach((col) => {
      let defaultCol = {
        field: col.code,
        headerText: col.name,
        width: data.length > 10 ? col?.width || '150' : 'auto'
      }
      // 处理 搜索使用 主数据选择器
      defaultCol = formatMasterFilter(defaultCol)

      if (
        (defaultCol.field === 'sourceHeaderCode' ||
          defaultCol.field === 'supplierCode' ||
          defaultCol.field === 'siteCode' ||
          defaultCol.field === 'companyCode' ||
          defaultCol.field === 'receiveCode') &&
        !defaultCol.searchOptions
      ) {
        defaultCol.searchOptions = {
          operator: 'likeright'
        }
      }
      if (defaultCol.field === 'sourceHeaderCode') {
        defaultCol.template = () => {
          return {
            template: Vue.component('sourceHeaderCode', {
              template: `<div @click="sourceHeaderCodeClick" class="able-click-field">{{ data.sourceHeaderCode }}</div>`,
              data() {
                return { data: {} }
              },
              methods: {
                sourceHeaderCodeClick() {
                  if (localStorage.getItem('currentBu') === 'GF') {
                    this.$router.push({
                      path: '/purchase-pv/purchase-coordination-detail',
                      query: {
                        type: 'detail',
                        id: this.data.sourceHeaderId,
                        refreshId: new Date().getTime()
                      }
                    })
                    return
                  }
                  this.$API.purchaseOrder
                    .getTenantPurOrderCode(this.data.sourceHeaderCode)
                    .then((res) => {
                      if (res.code === 200) {
                        this.$router.push({
                          name: 'purchase-coordination-detail',
                          query: {
                            orderid: res.data.id,
                            type: '3'
                          }
                        })
                      }
                    })
                }
              }
            })
          }
        }
      }
      if (defaultCol.field === 'projectCode') {
        defaultCol.template = () => {
          return {
            template: Vue.component('projectCode', {
              template: `<div @click="projectCodeClick" class="able-click-field">{{ data.projectCode }}</div>`,
              data() {
                return { data: {} }
              },
              methods: {
                projectCodeClick() {
                  this.$API.contract
                    .findByRfxCodeApi({ rfxCode: this.data.projectCode })
                    .then((res) => {
                      if (res.code === 200) {
                        this.$router.push({
                          path: '/sourcing/bid-hall/hall-detail',
                          query: {
                            source: res.data.sourcingMode,
                            rfxId: res.data.id,
                            sourceType: 'be-reconciled'
                          }
                        })
                      }
                    })
                }
              }
            })
          }
        }
      }
      if (defaultCol.field === 'contractCode') {
        defaultCol.template = () => {
          return {
            template: Vue.component('contractCode', {
              template: `<div @click="contractCodeClick" class="able-click-field">{{ data.contractCode }}</div>`,
              data() {
                return { data: {} }
              },
              methods: {
                contractCodeClick() {
                  this.$API.contract
                    .getByContractCodeApi({ contractCode: this.data.contractCode })
                    .then((res) => {
                      if (res.code === 200) {
                        this.$router.push({
                          path: '/middlePlatform/contractPrivew',
                          query: {
                            contractId: res.data.id,
                            type: 'be-reconciled'
                          }
                        })
                      }
                    })
                }
              }
            })
          }
        }
      }
      if (defaultCol.field === 'frozenStatus') {
        // 冻结状态
        defaultCol.valueConverter = {
          type: 'map',
          map: FrozenStatusOptions
        }
      } else if (defaultCol.field === 'advanceInvoicing') {
        // 提前开票
        defaultCol.valueConverter = {
          type: 'map',
          map: {
            0: i18n.t('否'),
            1: i18n.t('是')
          }
        }
      } else if (defaultCol.field === 'syncStatus') {
        // 同步状态
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: SyncStatus.notSynced,
              text: i18n.t('未同步'),
              cssClass: 'col-notSynced'
            },
            {
              value: SyncStatus.synced,
              text: i18n.t('已同步'),
              cssClass: 'col-synced'
            }
          ]
        }
      } else if (defaultCol.field === 'receiveTime' || defaultCol.field === 'deliverDate') {
        // 收货时间
        defaultCol.template = timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (defaultCol.field === 'fileBaseInfoList') {
        // 文件信息
        defaultCol.template = () => {
          return {
            template: Vue.component('fileBaseInfoList', {
              template: `<div @click="showFileBaseInfo" class="cell-operable-title">{{data.fileBaseInfoList | listNumFormat}}</div>`,
              data: function () {
                return { data: {} }
              },
              filters: {
                listNumFormat(value) {
                  if (value && value.length > 0) {
                    return value.length
                  } else {
                    return ''
                  }
                }
              },
              methods: {
                showFileBaseInfo() {
                  this.$parent.$emit('showFileBaseInfo', {
                    index: this.data.index,
                    value: this.data.fileBaseInfoList
                  })
                }
              }
            })
          }
        }
      } else if (defaultCol.field === 'sourceType') {
        // 来源类型（枚举） 0: 上游流入1:第三方接口
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('上游流入'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('第三方接口'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'status') {
        // 单据状态 0:待对账 1:已创建对账单
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('待对账'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('已创建对账单'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'prePayStatus') {
        // 是否预付 0-否；1-是
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('否'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('是'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'itemVoucherDate') {
        // 物料凭证日期
        defaultCol.template = timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
        defaultCol.searchOptions = {
          ...MasterDataSelect.dateRange,
          default: [
            new Date(dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD 00:00:00')),
            new Date(dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD 23:59:59'))
          ]
        }
      } else if (defaultCol.field === 'provisionalEstimateStatus') {
        if (defaultCol.headerText == '是否执行价') {
          // 是否执行价 0-是；1-否
          defaultCol.valueConverter = {
            type: 'map',
            map: [
              {
                value: 0,
                text: i18n.t('是'),
                cssClass: ''
              },
              {
                value: 1,
                text: i18n.t('否'),
                cssClass: ''
              }
            ]
          }
        } else {
          // 是否暂估价 0-否；1-是
          defaultCol.valueConverter = {
            type: 'map',
            map: [
              {
                value: 0,
                text: i18n.t('否'),
                cssClass: ''
              },
              {
                value: 1,
                text: i18n.t('是'),
                cssClass: ''
              }
            ]
          }
        }
      } else if (defaultCol.field === 'createTime') {
        // 创建时间
        defaultCol.template = timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (defaultCol.field === 'updateTime') {
        // 最后修改时间
        defaultCol.template = timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (defaultCol.field === 'type') {
        // 待对账类型:0-采购待对账；1-销售待对账
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('采购待对账'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('销售待对账'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'priceDateRangeStartDate') {
        // 价格有效期开始
        defaultCol.template = timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
        defaultCol.ignore = true
        defaultCol.searchOptions = MasterDataSelect.timeOnly
      } else if (defaultCol.field === 'priceDateRangeEndDate') {
        // 价格有效期结束
        defaultCol.template = timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
        defaultCol.ignore = true
        defaultCol.searchOptions = MasterDataSelect.timeOnly
      } else if (defaultCol.field === 'realPriceStatus') {
        // 正式价标识 0-无正式价；1-有正式价
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('无正式价'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('有正式价'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'inOutType') {
        // 出入库类型 0-采购；1-销售；
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('采购'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('销售'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'supplierName') {
        defaultCol.searchOptions = {
          ...MasterDataSelect.supplierAll,
          renameField: 'supplierCode'
        }
      } else if (defaultCol.field === 'itemCode') {
        defaultCol.searchOptions = {
          ...MasterDataSelect.material,
          operator: 'in',
          renameField: 'itemCode'
        }
      } else if (
        defaultCol.field === 'modelCode' ||
        defaultCol.field === 'recoTime' ||
        defaultCol.field === 'priceType' ||
        defaultCol.field === 'workshop' ||
        defaultCol.field === 'prodtLine' ||
        defaultCol.field === 'workProcessName' ||
        defaultCol.field === 'teamGroupCode' ||
        defaultCol.field === 'batchNo' ||
        defaultCol.field === 'settleModel' ||
        defaultCol.field === 'dailyOutput' ||
        defaultCol.field === 'materialCode' ||
        defaultCol.field === 'workOrder' ||
        defaultCol.field === 'moduleUnitPrice' ||
        defaultCol.field === 'cbuUnitPrice' ||
        defaultCol.field === 'otherUnitPrice' ||
        defaultCol.field === 'domainRentalFeeUnitPrice' ||
        defaultCol.field === 'equipmentRentalFeeUnitPrice' ||
        defaultCol.field === 'domainRentalFeeTotalPrice' ||
        defaultCol.field === 'equipmentRentalFeeTotalPrice' ||
        defaultCol.field === 'moduleDomainRentalFeeUnitPrice' ||
        defaultCol.field === 'moduleEquipmentRentalFeeUnitPrice' ||
        defaultCol.field === 'cbuDomainRentalFeeUnitPrice' ||
        defaultCol.field === 'cbuEquipmentRentalFeeUnitPrice' ||
        defaultCol.field === 'moduleDomainRentalFeeTotalPrice' ||
        defaultCol.field === 'moduleEquipmentRentalFeeTotalPrice' ||
        defaultCol.field === 'cbuDomainRentalFeeTotalPrice' ||
        defaultCol.field === 'cbuEquipmentRentalFeeTotalPrice' ||
        defaultCol.field === 'settleUnitPriceUntaxed' ||
        defaultCol.field === 'settleUnitPriceTaxed' ||
        defaultCol.field === 'settleTotalPriceUntaxed' ||
        defaultCol.field === 'settleTotalPriceTaxed' ||
        defaultCol.field === 'shareTotalPrice' ||
        defaultCol.field === 'workProcessName'
      ) {
        // 机型编码(ID)、对账时间、价格类型、车间、生产线、工序、班组、批次号、结算机型、日产量、物料编码、工单、模组单价、整机单价、其他单价、场地租赁费单价、设备租赁费单价、场地租赁费总价、设备租赁费总价、模组场地租赁费单价、模组设备租赁费单价、整机场地租赁费单价、整机设备租赁费单价、模组场地租赁费总价、模组设备租赁费总价、整机场地租赁费总价、整机设备租赁费总价、结算未税单价、结算含税单价、结算未税总价、结算含税总价、分摊总价
        defaultCol.searchOptions = {
          renameField: `reconciliationHroResponse.${defaultCol.field}`
        }
        defaultCol.valueAccessor = (field, data) => {
          if (defaultCol.field === 'recoTime') {
            return dayjs(Number(data?.reconciliationHroResponse?.[defaultCol.field])).format(
              'YYYY-MM-DD HH:mm:ss'
            )
          }
          if (defaultCol.field === 'priceType') {
            const priceType = [
              {
                value: '0',
                text: i18n.t('整机'),
                cssClass: ''
              },
              {
                value: '2',
                text: i18n.t('模组'),
                cssClass: ''
              },
              {
                value: '3',
                text: i18n.t('其他'),
                cssClass: ''
              }
            ]
            // return data?.reconciliationHroResponse?.[defaultCol.field]
            return priceType.filter(
              (i) => i.value === data?.reconciliationHroResponse?.[defaultCol.field]
            )[0]?.['text']
          }
          return data?.reconciliationHroResponse?.[defaultCol.field]
        }
        if (defaultCol.field !== 'modelCode') {
          defaultCol.ignore = true
        }
        if (defaultCol.field === 'modelCode') {
          defaultCol.searchOptions = {
            ...defaultCol.searchOptions,
            renameField: `associateExtDocNo`
          }
        }
        if (defaultCol.field === 'recoTime') {
          defaultCol.searchOptions = {
            ...defaultCol.searchOptions,
            ...MasterDataSelect.dateRange,
            renameField: `itemVoucherDate`
          }
          defaultCol.ignore = false
        }
      }
      colData.push(defaultCol)
    })
  } else if (Tab.toBeReconciled === tab) {
    // 待对账
    data.forEach((col) => {
      const defaultCol = {
        ...col,
        field: col.fieldCode,
        headerText: col.fieldName,
        width: col?.width || '150'
      }
      if (defaultCol.field === 'dataLength') {
        // 详情明细 明细数据条数
        defaultCol.cellTools = []
        defaultCol.ignore = true
      }
      colData.push(defaultCol)
    })
  }

  return colData
}
