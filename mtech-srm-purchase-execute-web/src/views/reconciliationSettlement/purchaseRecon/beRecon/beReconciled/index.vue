<template>
  <!-- 采方-对账结算-待对账 -->
  <div class="full-height">
    <mt-template-page
      v-if="componentConfig.length"
      ref="templateRef"
      class="self-set-table"
      :template-config="componentConfig"
      :current-tab="currentTab"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
      @showFileBaseInfo="showFileBaseInfo"
    />
    <div v-else>{{ $t('暂未配置相关查看权限，请联系管理员') }}</div>
    <!-- 附件弹窗 -->
    <uploader-dialog ref="uploaderDialog"></uploader-dialog>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash'
import {
  formatTableColumnData,
  Tab,
  ToBeReconciledColumnData,
  FrozenStatus,
  ConstantType,
  AsyncTabToolbar
} from './config/index'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  components: {
    UploaderDialog: () => import('@/components/Upload/uploaderDialog')
  },
  data() {
    const currentTab = JSON.parse(localStorage.getItem('tabIndex')) ?? 0
    localStorage.removeItem('tabIndex')

    return {
      total: 0,
      dateRange: null,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      currentTab, // 当前加载的 Tab 默认 0
      actionType: {
        createStatement: 1, // 创建对账单
        markFreeze: 2, // 标记冻结
        unmark: 3 // 取消标记
      },
      componentConfig: [],
      componentConfigTemp: [],
      asyncOriginTabs: []
    }
  },
  mounted() {
    this.getReconTabs()
  },
  methods: {
    // 获取对账tab
    getReconTabs() {
      this.$API.reconciliationSettlement
        .getTypeTab({ BU_CODE: localStorage.getItem('currentBu') || 'KT' })
        .then((res) => {
          this.asyncOriginTabs = res.data
          this.setAsyncTabs()
        })
    },
    // 设置头部的tabs
    setAsyncTabs() {
      const asyncTabs = []
      this.asyncOriginTabs.forEach((item) => {
        let cols = null
        let oneTab = {
          tab: { title: this.$t('待对账') },
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'CreateStatement',
              icon: 'icon_solid_Createorder',
              title: this.$t('创建对账单'),
              permission: ['O_02_0396']
            }
          ],
          gridId: this.$tableUUID.reconciliationSettlement.beReconciled.list,
          saveSelectedRecordWithSearch: false,
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            frozenColumns: 1,
            columnData: formatTableColumnData({
              tab: Tab.toBeReconciled,
              data: ToBeReconciledColumnData
            }),

            dataSource: [],
            // queryBuilder查询-待对账信息
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliationWait/queryBuilderHeader`,
              // ignoreDefaultSearch: true,
              // ignoreSearchToast: true,
              defaultRules: [],
              // 序列化
              serializeList: (list) => {
                return list
              }
            }
          }
        }
        const currentBu = localStorage.getItem('currentBu')
        const _AsyncTabToolbar =
          currentBu === 'GF'
            ? AsyncTabToolbar.filter((item) => item.id !== 'SynchronizeSuppliers')
            : AsyncTabToolbar
        if (item.code !== 'RTC000') {
          let tabToolbar = _AsyncTabToolbar
          if (item.code === 'HRO') {
            tabToolbar = [
              ..._AsyncTabToolbar,
              {
                id: 'BatchDetailCreateStatement',
                icon: 'icon_solid_Createorder',
                title: this.$t('批量创建对账单'),
                permission: ['O_02_0396']
              }
            ]
          }
          cols = formatTableColumnData({
            tab: Tab.toBeReconciledDetail,
            data: item.fields
          })
          oneTab = {
            tab: { title: item.name },
            useToolTemplate: false, // 不使用预置(新增、编辑、删除)
            useBaseConfig: true, // 使用组件中的toolbar配置
            toolbar: tabToolbar,
            buttonQuantity: 6,
            gridId: this.$md5(
              this.$tableUUID.reconciliationSettlement.beReconciled.setAsyncTabs + item.code
            ),
            saveSelectedRecordWithSearch: false,
            // defaultsearchTemplates: [],
            grid: {
              lineSelection: 0,
              lineIndex: 1,
              // frozenColumns: 1,
              columnData: cols,
              dataSource: [],
              pageSettings: {
                currentPage: 1,
                pageSize: 200,
                pageSizes: [10, 20, 50, 100, 200, 1000],
                totalRecordsCount: 0
              },
              // 待对账接口 - queryBuilder查询-待对账明细信息
              asyncConfig: {
                url: `${BASE_TENANT}/reconciliationWait/queryBuilderItem?BU_CODE=${currentBu}`,
                // ignoreDefaultSearch: true,
                // ignoreSearchToast: true,
                defaultRules: [
                  {
                    label: this.$t('对账类型编码'),
                    field: 'reconciliationTypeCode',
                    type: 'string',
                    operator: 'equal',
                    value: item.code
                  },
                  {
                    label: this.$t('业务类型编码'),
                    field: 'businessTypeCode',
                    type: 'string',
                    operator: 'equal',
                    value: item.businessTypeCode
                  }
                ],
                afterAsyncData: (res) => {
                  this.total = res?.data?.total
                }
              },
              defaultSearchItem: [
                {
                  field: 'frozenStatus',
                  headerText: this.$t('冻结状态')
                },
                {
                  field: 'itemVoucherDate',
                  headerText: this.$t('物料凭证日期')
                },
                {
                  field: 'sourceHeaderCode',
                  headerText: this.$t('订单号')
                },
                {
                  field: 'supplierCode',
                  headerText: this.$t('供应商编码')
                },
                {
                  field: 'supplierName',
                  headerText: this.$t('供应商名称')
                },
                {
                  field: 'itemCode',
                  headerText: this.$t('物料编号')
                },
                {
                  field: 'provisionalEstimateStatus',
                  headerText: this.$t('是否执行价')
                },
                {
                  field: 'companyCode',
                  headerText: this.$t('公司编码')
                },
                {
                  field: 'companyName',
                  headerText: this.$t('公司名称')
                },
                {
                  field: 'siteCode',
                  headerText: this.$t('地点/工厂代码')
                },
                {
                  field: 'siteName',
                  headerText: this.$t('地点/工厂名称')
                },
                {
                  field: 'receiveTime',
                  headerText: this.$t('收货时间')
                },
                {
                  field: 'inOutType',
                  headerText: this.$t('出入库类型')
                },
                {
                  field: 'receiveCode',
                  headerText: this.$t('物料凭证')
                }
              ]
            }
          }
          if (['RTC006', 'GQCGQZ_530', 'G', 'H', 'I', 'J'].includes(item.code)) {
            oneTab.grid.asyncConfig.rules = []
            oneTab.grid.asyncConfig.rules.push({
              label: '物料凭证日期',
              field: 'itemVoucherDate',
              type: 'string',
              operator: 'between',
              value: [
                dayjs(
                  dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD 00:00:00')
                ).valueOf(),
                dayjs(
                  dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD 23:59:59')
                ).valueOf()
              ]
            })
          }
        }
        asyncTabs.push(oneTab)
      })
      this.componentConfig = this.componentConfigTemp.concat(asyncTabs)
    },

    // 显示表格文件弹窗
    showFileBaseInfo(e) {
      const dialogParams = {
        fileData: cloneDeep(e.value),
        isView: true,
        title: this.$t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },
    // CellTitle
    handleClickCellTitle(e) {
      const tabIndex = this.$refs.templateRef.getCurrentTabRef().tabIndex
      if (e.field === 'dataLength' && e.tabIndex === Tab.toBeReconciled) {
        // 详情明细 && 待对账
        // 将 lastTabIndex 放到 localStorage 创建对账页面 读
        localStorage.setItem('lastTabIndex', JSON.stringify(tabIndex))
        // 将行信息放到 localStorage 待对账明细 读
        localStorage.setItem('toBeReconciledTopInfo', JSON.stringify(e.data))
        // 跳转 待对账明细
        this.$router.push({
          name: 'be-reconciled-detail',
          query: {}
        })
      }
    },
    // ToolBar
    handleClickToolBar(e) {
      const selectRows = e.gridRef.getMtechGridRecords()
      const tabIndex = this.$refs.templateRef.getCurrentTabRef().tabIndex
      const selectOperateId = ['DetailCreateStatement', 'MarkFreeze', 'Unmark', 'CreateStatement']
      if (selectRows.length === 0 && selectOperateId.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedIds = []
      let executeTaxedTotalPrice = 0, // 计算执行含税/未税 总价
        executeUntaxedTotalPrice = 0,
        taxAmount
      selectRows.forEach((item) => {
        selectedIds.push(item.id)
        executeTaxedTotalPrice += item.taxedTotalPrice
        executeUntaxedTotalPrice += item.untaxedTotalPrice
      })
      taxAmount = executeTaxedTotalPrice - executeUntaxedTotalPrice

      if (e.toolbar.id === 'DetailCreateStatement') {
        // 待对账明细-创建对账单
        this.handleDetailCreateStatement({
          selectRows,
          selectedIds,
          executeTaxedTotalPrice,
          executeUntaxedTotalPrice,
          taxAmount,
          tabIndex
        })
      } else if (e.toolbar.id === 'BatchDetailCreateStatement') {
        // 待对账明细-创建对账单
        this.handleBatchDetailCreateStatement({
          selectRows,
          selectedIds,
          executeTaxedTotalPrice,
          executeUntaxedTotalPrice,
          taxAmount,
          tabIndex
        })
      } else if (e.toolbar.id === 'MarkFreeze') {
        // 冻结
        this.handleMarkFreeze({ selectRows, selectedIds })
      } else if (e.toolbar.id === 'Unmark') {
        // 取消冻结
        this.handleUnmark({ selectRows, selectedIds })
      } else if (e.toolbar.id === 'CreateStatement') {
        // 待对账-创建对账单
        this.handleCreateStatement({ selectRows })
      } else if (e.toolbar.id === 'SynchronizeSuppliers') {
        // 同步供应商
        const params = {}
        this.getReconciliationWaitSyncToSup(params, selectRows)
      } else if (e.toolbar.id === 'ExcelExport') {
        this.handleExport()
      }
    },
    // 待对账明细-创建对账单
    handleDetailCreateStatement(args) {
      const { selectRows } = args
      const {
        valid,
        validMarkFreeze, // 冻结状态
        validProvisional
      } = this.checkSelectedValid({
        selectedData: selectRows,
        actionType: this.actionType.createStatement
      })
      if (!valid) {
        if (!validMarkFreeze) {
          this.$toast({
            content: this.$t('冻结状态为已冻结的数据不可创建对账单'),
            type: 'warning'
          })
        } else if (!validProvisional) {
          this.$toast({
            content: this.$t('是否执行价为否的数据不可创建对账单'),
            type: 'warning'
          })
        } else {
          this.$toast({
            content: this.$t('请选择相同业务类型、公司、供应商、币种、税率、工厂、对账类型的数据'),
            type: 'warning'
          })
        }

        return
      }
      const _len = selectRows?.length
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(
            `是否创建当前勾选数据？（您勾选的数据条数：${_len}，当前待对账总条数：${this.total}）`
          )
        },
        success: () => {
          this.mergeStorageData()
        }
      })
    },
    // 批量（全量）创建对账单
    handleBatchDetailCreateStatement() {
      let { asyncParams, queryBuilderRules } =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef
      let _flag = this.checkParams(queryBuilderRules)
      let selectRows = this.$refs.templateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      if (selectRows.length <= 0) {
        this.$toast({
          content: this.$t('当前查询条件未查询对账单数据'),
          type: 'warning'
        })
        return false
      }
      if (!_flag) return false
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('是否全量创建当前查询的数据？')
        },
        success: () => {
          queryBuilderRules = queryBuilderRules || {}
          const _params = {
            ...asyncParams,
            rules: queryBuilderRules.rules || []
          }
          localStorage.setItem('createStatementParams', JSON.stringify(_params))
          this.mergeStorageData('all')
        }
      })
    },
    // 组装缓存数据
    mergeStorageData(type) {
      let selectRows = this.$refs.templateRef.getCurrentUsefulRef().gridRef.getMtechGridRecords()
      if (type === 'all') {
        selectRows = this.$refs.templateRef
          .getCurrentUsefulRef()
          .gridRef.ejsRef.getCurrentViewRecords()
      }

      const tabIndex = this.$refs.templateRef.getCurrentTabRef().tabIndex
      const selectedIds = []
      selectRows.forEach((item) => {
        selectedIds.push(item.id)
      })
      const createStatementData = {
        headerInfo: {
          reconciliationTypeCode: selectRows[0].reconciliationTypeCode, // 对账类型编码
          reconciliationTypeName: selectRows[0].reconciliationTypeName, // 对账类型名称
          companyCode: selectRows[0].companyCode, // 公司编码
          companyName: selectRows[0].companyName, // 公司名称
          supplierCode: selectRows[0].supplierCode, // 供应商编码
          supplierName: selectRows[0].supplierName, // 供应商名称
          businessTypeCode: selectRows[0].businessTypeCode, // 业务类型编码
          businessTypeId: selectRows[0].businessTypeId, // 业务类型id
          businessTypeName: selectRows[0].businessTypeName, // 业务类型名称
          companyId: selectRows[0].companyId, // 公司id
          supplierId: selectRows[0].supplierId, // 供应商id
          currencyName: selectRows[0].currencyName, // 币种
          siteName: selectRows[0]?.siteName, // 工厂
          siteCode: selectRows[0]?.siteCode,
          contactAddress: selectRows[0]?.contactAddress,
          partyAddress: selectRows[0]?.partyAddress,
          deliverDate: selectRows[0]?.deliverDate
          // executeTaxedTotalPrice,
          // executeUntaxedTotalPrice,
          // taxAmount,
        }, // 头部信息
        requireData: selectRows, // 需求明细信息
        idList: selectedIds, // idList
        entryType: ConstantType.Add
      }
      // 将信息放到 localStorage 创建对账页面 读
      localStorage.setItem('createStatementData', JSON.stringify(createStatementData))
      // 将 lastTabIndex 放到 localStorage 创建对账页面 读
      localStorage.setItem('lastTabIndex', JSON.stringify(tabIndex))
      // 跳转 创建对账单
      this.$router.push({
        name: 'create-statement',
        query: type === 'all' ? { flag: 'all' } : {}
      })
    },
    checkParams(data) {
      if (!data) {
        this.$toast({
          content: this.$t('请选择供应商名称、公司编号、地点/工厂名称'),
          type: 'warning'
        })
        return false
      }

      let _rules = data.rules
      let _codes = _rules.map((item) => item.field)
      console.log(_codes)
      if (!_codes.includes('frozenStatus')) {
        this.$toast({
          content: this.$t('请选择冻结状态'),
          type: 'warning'
        })
        return false
      }
      // if (!_codes.includes('provisionalEstimateStatus')) {
      //  this.$toast({
      //    content: this.$t('请选择是否执行价'),
      //    type: 'warning'
      //  })
      //  return false
      //}
      if (!_codes.includes('realPriceStatus')) {
        this.$toast({
          content: this.$t('请选择是否正式价'),
          type: 'warning'
        })
        return false
      }

      if (!_codes.includes('supplierCode')) {
        this.$toast({
          content: this.$t('请选择供应商名称'),
          type: 'warning'
        })
        return false
      }
      // if (!_codes.includes('companyCode')) {
      //   this.$toast({
      //     content: this.$t('请选择公司'),
      //     type: 'warning'
      //   })
      //   return false
      // }
      if (!_codes.includes('siteCode')) {
        this.$toast({
          content: this.$t('请选择地点/工厂名称'),
          type: 'warning'
        })
        return false
      }

      for (var i = 0; i < _rules.length; i++) {
        let item = _rules[i]
        if (item.field === 'frozenStatus' && item.value === 1) {
          this.$toast({
            content: this.$t('请选择冻结状态为否'),
            type: 'warning'
          })
          return false
        }
        // if (item.field === 'provisionalEstimateStatus' && item.value === 1) {
        //   this.$toast({
        //     content: this.$t('请选择是否执行价为是'),
        //     type: 'warning'
        //   })
        //   return false
        // }
        if (item.field === 'realPriceStatus' && item.value === 0) {
          this.$toast({
            content: this.$t('请选择是否正式价为有正式价'),
            type: 'warning'
          })
          return false
        }
        if (item.field === 'supplierCode' && item.operator === 'in' && item.value.length !== 1) {
          this.$toast({
            content: this.$t('请选择相同供应商'),
            type: 'warning'
          })
          return false
        }
        // if (item.field === 'companyCode' && item.value.length !== 1) {
        //   this.$toast({
        //     content: this.$t('请选择相同公司'),
        //     type: 'warning'
        //   })
        //   return false
        // }
        if (item.field === 'siteCode' && item.operator === 'in' && item.value.length !== 1) {
          this.$toast({
            content: this.$t('请选择相同地点/工厂名称'),
            type: 'warning'
          })
          return false
        }
        if (!_codes.includes('itemVoucherDate')) {
          this.$toast({
            content: this.$t('请选择对账时间'),
            type: 'warning'
          })
          return false
        }
      }
      return true
    },
    // 冻结
    handleMarkFreeze(args) {
      const { selectRows, selectedIds } = args
      const { valid } = this.checkSelectedValid({
        selectedData: selectRows,
        actionType: this.actionType.unmark
      })
      if (!valid) {
        this.$toast({
          content: this.$t('请选择相同的冻结状态'),
          type: 'warning'
        })

        return
      }
      if (selectRows[0].frozenStatus == FrozenStatus.markFreeze) {
        this.$toast({
          content: this.$t('请选择未冻结的数据'),
          type: 'warning'
        })

        return
      }
      const params = {
        idList: selectedIds
      }
      // 请求API
      this.doMarkFreeze(params)
    },
    // 取消冻结
    handleUnmark(args) {
      const { selectRows, selectedIds } = args
      const { valid } = this.checkSelectedValid({
        selectedData: selectRows,
        actionType: this.actionType.unmark
      })
      if (!valid) {
        this.$toast({
          content: this.$t('请选择相同的冻结状态'),
          type: 'warning'
        })

        return
      }
      if (selectRows[0].frozenStatus == FrozenStatus.unmark) {
        this.$toast({
          content: this.$t('请选择已冻结的数据'),
          type: 'warning'
        })

        return
      }
      const params = {
        idList: selectedIds
      }
      // 请求API
      this.doUnmark(params)
    },
    // 待对账-创建对账单
    handleCreateStatement(args) {
      const { selectRows } = args
      const params = []
      selectRows.forEach((item) =>
        params.push({
          businessTypeCode: item.businessTypeCode, // 业务类型编码
          businessTypeId: item.businessTypeId, // 业务类型id
          businessTypeName: item.businessTypeName, // 业务类型名称
          companyCode: item.companyCode, // 公司编码
          companyId: item.companyId, // 公司id
          companyName: item.companyName, // 公司名称
          currencyId: item.currencyId, // 货币id
          currencyName: item.currencyName, // 货币名称
          currencyCode: item.currencyCode, // 货币code
          supplierCode: item.supplierCode, // 供应商编码
          supplierId: item.supplierId, // 供应商id
          supplierName: item.supplierName, // 供应商名称
          reconciliationTypeCode: item.reconciliationTypeCode, // 对账类型编码
          reconciliationTypeName: item.reconciliationTypeName, // 对账类型名称
          siteCode: item.siteCode, // 工厂
          siteName: item.siteName,
          taxCode: item.taxCode,
          receiveCode: item.receiveCode,
          receiveCodeRef: item.receiveCodeRef,
          itemVoucherDate: item.itemVoucherDate,
          untaxedUnitPrice: item.untaxedUnitPrice,
          executeUntaxedTotalPrice: item.executeUntaxedTotalPrice
        })
      )
      this.doCreateStatement(params)
    },
    // 判断选择的数据是否符合要求
    checkSelectedValid(data) {
      const { selectedData: selectedList, actionType } = data
      let valid = true
      let validMarkFreeze = true // 冻结状态
      let validProvisional = true // 是否执行价
      let validBusinessType = true // 业务类型
      let validCompany = true // 公司
      let validSupplier = true // 供应商
      let validCurrency = true // 币种
      let validTax = true // 币种
      let validSite = true // 工厂
      let validReconciliationType = true // 对账类型
      if (selectedList && selectedList.length > 0) {
        if (actionType === this.actionType.createStatement) {
          // 创建对账单
          for (let i = 0; i < selectedList.length; i++) {
            // 可创建条件：
            // 冻结状态为"未冻结" &&
            // 业务类型code 相同 &&
            // 公司code 相同 &&
            // 供应商code 相同 &&
            // 币种id 相同 &&
            // 税率 相同 &&
            // 工厂code 相同 &&
            // 对账类型 code 相同
            if (selectedList[i].frozenStatus === FrozenStatus.markFreeze) {
              // 冻结状态为"已冻结"
              this.$toast({
                content: this.$t('冻结状态为"已冻结"的数据不可创建对账单'),
                type: 'warning'
              })
              validMarkFreeze = false
              valid = false
            }
            if (selectedList[i].provisionalEstimateStatus === 1) {
              // 是否执行价为否的时候不让创建对账单
              this.$toast({
                content: this.$t('是否执行价为"否"的数据不可创建对账单'),
                type: 'warning'
              })
              validProvisional = false
              valid = false
            }
            if (selectedList[i + 1]) {
              // 存在下一条数据，与下一条数据比较
              if (selectedList[i].businessTypeCode !== selectedList[i + 1].businessTypeCode) {
                // 业务类型 不相同
                validBusinessType = false // 业务类型
                valid = false
              }
              if (selectedList[i].companyCode !== selectedList[i + 1].companyCode) {
                // 公司 不相同
                validCompany = false // 公司
                valid = false
              }
              if (selectedList[i].supplierCode !== selectedList[i + 1].supplierCode) {
                // 供应商 不相同
                validSupplier = false // 供应商
                valid = false
              }
              if (selectedList[i].currencyId !== selectedList[i + 1].currencyId) {
                // 币种 不相同
                validCurrency = false // 币种
                valid = false
              }
              if (selectedList[i].taxCode !== selectedList[i + 1].taxCode) {
                // 税率 不相同
                validTax = false // 税率
                valid = false
              }
              if (selectedList[i].siteCode !== selectedList[i + 1].siteCode) {
                // 工厂 不相同
                validSite = false // 工厂
                valid = false
              }
              if (
                selectedList[i].reconciliationTypeCode !==
                selectedList[i + 1].reconciliationTypeCode
              ) {
                // 对账类型 不相同
                validReconciliationType = false // 对账类型
                valid = false
              }
            }
          }
        } else if (
          actionType === this.actionType.markFreeze ||
          actionType === this.actionType.unmark
        ) {
          // 冻结 || 取消冻结
          for (let i = 0; i < selectedList.length; i++) {
            if (
              selectedList[i + 1] &&
              selectedList[i].frozenStatus !== selectedList[i + 1].frozenStatus
            ) {
              valid = false
              break
            }
          }
        }
      }

      return {
        valid,
        validMarkFreeze, // 冻结状态
        validProvisional, // 是否执行价
        validBusinessType, // 业务类型
        validCompany, // 公司
        validSupplier, // 供应商
        validCurrency, // 币种
        validTax, // 税率
        validSite, // 工厂
        validReconciliationType // 对账类型
      }
    },
    // 同步待对账数据至供应商
    getReconciliationWaitSyncToSup(params, selectRows) {
      let hasOne = selectRows.some((item) => {
        return item.syncStatus == 1
      })
      if (hasOne) {
        this.$toast({
          content: this.$t('请选择未同步的行操作!'),
          type: 'warning'
        })
        return
      }
      params.currentBu = localStorage.getItem('currentBu') || 'KT'
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .getReconciliationWaitSyncToSup(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 导出
    handleExport() {
      let { asyncParams, queryBuilderRules } =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef
      let { tabIndex } = this.$refs.templateRef.getCurrentTabRef()
      let { gridId, grid } = this.componentConfig[tabIndex]
      let obj = JSON.parse(sessionStorage.getItem(gridId))?.visibleCols
      let field = []
      if (obj !== undefined && obj.length) {
        obj.forEach((item) => {
          if (item.field) {
            if (item.field.slice(-4) === 'Date' && item.field !== 'itemVoucherDate') {
              const fieldName = item.field.slice(0, -4)
              field.push(fieldName)
            } else {
              field.push(item.field)
            }
          }
        })
      } else {
        grid.columnData.forEach((item) => {
          if (item.field) {
            if (item.field.includes('Date')) {
              const fieldName = item.field.replace('Date', '')
              field.push(fieldName)
            } else {
              field.push(item.field)
            }
          }
        })
      }
      let params = {
        condition: queryBuilderRules?.condition || '',
        page: { current: 1, size: 10000 },
        pageFlag: true,
        rules: queryBuilderRules?.rules || [],
        defaultRules: asyncParams?.defaultRules,
        currentBu: localStorage.getItem('currentBu') || 'KT'
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement.reconciliationHeaderExport(params, field).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 根据ID取消标记冻结
    doUnmark(data) {
      data.currentBu = localStorage.getItem('currentBu') || 'KT'
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .putReconciliationWaitCancelFrozenById(data)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 根据ID标记冻结
    doMarkFreeze(data) {
      data.currentBu = localStorage.getItem('currentBu') || 'KT'
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .putReconciliationWaitFrozenById(data)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 批量创建对账单
    doCreateStatement(data) {
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .putReconciliationHeaderBatchCreate(data)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .self-set-table {
  .template-wrap {
    .e-grid .e-table {
      & thead th:first-child {
        position: sticky;
        left: 0;
        z-index: 1;
      }
      & tbody td:first-child {
        position: sticky;
        left: 0;
        z-index: 1;
        border-right: 1px solid var(--plugin-dg-shadow-color);
        background-color: #fff;
      }
    }
  }
}
</style>
