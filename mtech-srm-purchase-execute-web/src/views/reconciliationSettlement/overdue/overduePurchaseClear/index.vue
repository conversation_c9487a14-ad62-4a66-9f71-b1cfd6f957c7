<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      :current-tab="currentTab"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
    />
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { colsq } from './config'
import { ConstantType, FrozenStatus } from './config/index'
export default {
  data() {
    return {
      componentConfig: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: [
            [
              {
                id: 'createStatement',
                icon: 'icon_solid_Createorder',
                title: this.$t('创建对账单'),
                permission: ['O_02_0402']
              }
            ],
            ['Filter', 'Refresh', 'Setting']
          ],
          gridId: 'bf068135-3f8c-45fd-a5e1-95d0735d8eab',
          saveSelectedRecordWithSearch: false,
          grid: {
            selectId: 'detailId',
            lineSelection: 0,
            columnData: colsq,
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliationWait/queryBuilderItem`,
              afterAsyncData: (res) => {
                this.total = res?.data?.total
              }
            },
            frozenColumns: 1
          }
        }
      ],
      actionType: {
        createStatement: 1, // 创建对账单
        markFreeze: 2, // 标记冻结
        unmark: 3 // 取消标记
      },
      currentTab: 0,
      total: 0,
      userInfo: null
    }
  },
  mounted() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
  },

  methods: {
    handleClickCellTitle() {},

    // ToolBar
    handleClickToolBar(e) {
      const selectRows = e.gridRef.getMtechGridRecords()
      // const tabIndex = this.$refs.templateRef.getCurrentTabRef().tabIndex;
      if (selectRows.length === 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedIds = []
      let executeTaxedTotalPrice = 0, // 计算执行含税/未税 总价
        executeUntaxedTotalPrice = 0,
        taxAmount
      selectRows.forEach((item) => {
        selectedIds.push(item.id)
        executeTaxedTotalPrice += item.taxedTotalPrice
        executeUntaxedTotalPrice += item.untaxedTotalPrice
      })
      console.log('11111111111' + selectedIds)
      taxAmount = executeTaxedTotalPrice - executeUntaxedTotalPrice
      if (e.toolbar.id === 'createStatement') {
        // if (selectRows.length === 0) {
        //   this.$toast({ content: this.$t("请先选择一行"), type: "warning" });
        //   return;
        // }
        // 待对账明细-创建对账单
        this.handleDetailCreateStatement({
          selectRows,
          selectedIds,
          executeTaxedTotalPrice,
          executeUntaxedTotalPrice,
          taxAmount
          // tabIndex,
        })
      }
      // this.handleCreateStatement({ selectRows });
    },
    // 待对账明细-创建对账单
    handleDetailCreateStatement(args) {
      const { selectRows } = args
      const {
        valid,
        validMarkFreeze // 冻结状态
      } = this.checkSelectedValid({
        selectedData: selectRows,
        actionType: this.actionType.createStatement
      })
      if (!valid) {
        if (!validMarkFreeze) {
          this.$toast({
            content: this.$t('冻结状态为已冻结的数据不可创建对账单'),
            type: 'warning'
          })
        } else {
          this.$toast({
            content: this.$t('请选择相同业务类型、公司、供应商、币种、工厂、对账类型的数据'),
            type: 'warning'
          })
        }

        return
      }
      const _len = selectRows?.length
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(
            `是否创建当前勾选数据？（您勾选的数据条数：${_len}，当前待对账总条数：${this.total}）`
          )
        },
        success: () => {
          this.mergeStorageData()
        }
      })
    },
    // 组装缓存数据
    mergeStorageData(type) {
      let selectRows = this.$refs.templateRef.getCurrentUsefulRef().gridRef.getMtechGridRecords()
      if (type === 'all') {
        selectRows = this.$refs.templateRef
          .getCurrentUsefulRef()
          .gridRef.ejsRef.getCurrentViewRecords()
      }

      // const tabIndex = this.$refs.templateRef.getCurrentTabRef().tabIndex;
      const selectedIds = []
      selectRows.forEach((item) => {
        selectedIds.push(item.id)
      })
      const createStatementData = {
        headerInfo: {
          reconciliationTypeCode: selectRows[0].reconciliationTypeCode, // 对账类型编码
          reconciliationTypeName: selectRows[0].reconciliationTypeName, // 对账类型名称
          companyCode: selectRows[0].companyCode, // 公司编码
          companyName: selectRows[0].companyName, // 公司名称
          supplierCode: selectRows[0].supplierCode, // 供应商编码
          supplierName: selectRows[0].supplierName, // 供应商名称
          businessTypeCode: selectRows[0].businessTypeCode, // 业务类型编码
          businessTypeId: selectRows[0].businessTypeId, // 业务类型id
          businessTypeName: selectRows[0].businessTypeName, // 业务类型名称
          companyId: selectRows[0].companyId, // 公司id
          supplierId: selectRows[0].supplierId, // 供应商id
          currencyName: selectRows[0].currencyName, // 币种
          siteName: selectRows[0]?.siteName, // 工厂
          siteCode: selectRows[0]?.siteCode
          // executeTaxedTotalPrice,
          // executeUntaxedTotalPrice,
          // taxAmount,
        }, // 头部信息
        requireData: selectRows, // 需求明细信息
        idList: selectedIds, // idList
        entryType: ConstantType.Add
      }
      // 将信息放到 localStorage 创建对账页面 读
      localStorage.setItem('createStatementData', JSON.stringify(createStatementData))
      // 将 lastTabIndex 放到 localStorage 创建对账页面 读
      // localStorage.setItem("lastTabIndex", JSON.stringify(tabIndex));
      // 跳转 创建对账单
      this.$router.push({
        name: 'create-statement',
        query: type === 'all' ? { flag: 'all', come: 'clear' } : { come: 'clear' }
      })
    },
    // 判断选择的数据是否符合要求
    // checkSelectedValid(data) {
    //   const FrozenStatus = {
    //     markFreeze: 1, // 是
    //     unmark: 0, // 否
    //   };
    //   const { selectedData: selectedList } = data;
    //   let valid = true;
    //   let validMarkFreeze = true; // 冻结状态
    //   let validBusinessType = true; // 业务类型
    //   let validCompany = true; // 公司
    //   let validSupplier = true; // 供应商
    //   let validCurrency = true; // 币种
    //   let validSite = true; // 工厂
    //   let validReconciliationType = true; // 对账类型
    //   if (selectedList && selectedList.length > 0) {
    //     // 创建对账单
    //     for (let i = 0; i < selectedList.length; i++) {
    //       // 可创建条件：
    //       // 冻结状态为"未冻结" &&
    //       // 业务类型code 相同 &&
    //       // 公司code 相同 &&
    //       // 供应商code 相同 &&
    //       // 币种id 相同 &&
    //       // 工厂code 相同 &&
    //       // 对账类型 code 相同
    //       if (selectedList[i].frozenStatus === FrozenStatus.markFreeze) {
    //         // 冻结状态为"已冻结"
    //         this.$toast({
    //           content: this.$t('冻结状态为"已冻结"的数据不可创建对账单'),
    //           type: "warning",
    //         });
    //         validMarkFreeze = false;
    //         valid = false;
    //       }
    //       if (selectedList[i + 1]) {
    //         // 存在下一条数据，与下一条数据比较
    //         if (
    //           selectedList[i].businessTypeCode !==
    //           selectedList[i + 1].businessTypeCode
    //         ) {
    //           // 业务类型 不相同
    //           validBusinessType = false; // 业务类型
    //           valid = false;
    //         }
    //         if (
    //           selectedList[i].companyCode !== selectedList[i + 1].companyCode
    //         ) {
    //           // 公司 不相同
    //           validCompany = false; // 公司
    //           valid = false;
    //         }
    //         if (
    //           selectedList[i].supplierCode !== selectedList[i + 1].supplierCode
    //         ) {
    //           // 供应商 不相同
    //           validSupplier = false; // 供应商
    //           valid = false;
    //         }
    //         if (selectedList[i].currencyId !== selectedList[i + 1].currencyId) {
    //           // 币种 不相同
    //           validCurrency = false; // 币种
    //           valid = false;
    //         }
    //         if (selectedList[i].siteCode !== selectedList[i + 1].siteCode) {
    //           // 工厂 不相同
    //           validSite = false; // 工厂
    //           valid = false;
    //         }
    //         if (
    //           selectedList[i].reconciliationTypeCode !==
    //           selectedList[i + 1].reconciliationTypeCode
    //         ) {
    //           // 对账类型 不相同
    //           validReconciliationType = false; // 对账类型
    //           valid = false;
    //         }
    //       }
    //     }
    //   }

    //   return {
    //     valid,
    //     validMarkFreeze, // 冻结状态
    //     validBusinessType, // 业务类型
    //     validCompany, // 公司
    //     validSupplier, // 供应商
    //     validCurrency, // 币种
    //     validSite, // 工厂
    //     validReconciliationType, // 对账类型
    //   };
    // },
    // 判断选择的数据是否符合要求
    checkSelectedValid(data) {
      const { selectedData: selectedList, actionType } = data
      let valid = true
      let validMarkFreeze = true // 冻结状态
      let validProvisional = true // 是否执行价
      let validBusinessType = true // 业务类型
      let validCompany = true // 公司
      let validSupplier = true // 供应商
      let validCurrency = true // 币种
      let validSite = true // 工厂
      let validReconciliationType = true // 对账类型
      if (selectedList && selectedList.length > 0) {
        if (actionType === this.actionType.createStatement) {
          // 创建对账单
          for (let i = 0; i < selectedList.length; i++) {
            // 可创建条件：
            // 冻结状态为"未冻结" &&
            // 业务类型code 相同 &&
            // 公司code 相同 &&
            // 供应商code 相同 &&
            // 币种id 相同 &&
            // 工厂code 相同 &&
            // 对账类型 code 相同
            if (selectedList[i].frozenStatus === FrozenStatus.markFreeze) {
              // 冻结状态为"已冻结"
              this.$toast({
                content: this.$t('冻结状态为"已冻结"的数据不可创建对账单'),
                type: 'warning'
              })
              validMarkFreeze = false
              valid = false
            }
            // if (selectedList[i].provisionalEstimateStatus === 1) {
            //   // 是否执行价为否的时候不让创建对账单
            //   this.$toast({
            //     content: this.$t('是否执行价为"否"的数据不可创建对账单'),
            //     type: 'warning'
            //   })
            //   validProvisional = false
            //   valid = false
            // }
            if (selectedList[i + 1]) {
              // 存在下一条数据，与下一条数据比较
              if (selectedList[i].businessTypeCode !== selectedList[i + 1].businessTypeCode) {
                // 业务类型 不相同
                validBusinessType = false // 业务类型
                valid = false
              }
              if (selectedList[i].companyCode !== selectedList[i + 1].companyCode) {
                // 公司 不相同
                validCompany = false // 公司
                valid = false
              }
              if (selectedList[i].supplierCode !== selectedList[i + 1].supplierCode) {
                // 供应商 不相同
                validSupplier = false // 供应商
                valid = false
              }
              if (selectedList[i].currencyId !== selectedList[i + 1].currencyId) {
                // 币种 不相同
                validCurrency = false // 币种
                valid = false
              }
              if (selectedList[i].siteCode !== selectedList[i + 1].siteCode) {
                // 工厂 不相同
                validSite = false // 工厂
                valid = false
              }
              if (
                selectedList[i].reconciliationTypeCode !==
                selectedList[i + 1].reconciliationTypeCode
              ) {
                // 对账类型 不相同
                validReconciliationType = false // 对账类型
                valid = false
              }
            }
          }
        } else if (
          actionType === this.actionType.markFreeze ||
          actionType === this.actionType.unmark
        ) {
          // 冻结 || 取消冻结
          for (let i = 0; i < selectedList.length; i++) {
            if (
              selectedList[i + 1] &&
              selectedList[i].frozenStatus !== selectedList[i + 1].frozenStatus
            ) {
              valid = false
              break
            }
          }
        }
      }

      return {
        valid,
        validMarkFreeze, // 冻结状态
        validProvisional, // 是否执行价
        validBusinessType, // 业务类型
        validCompany, // 公司
        validSupplier, // 供应商
        validCurrency, // 币种
        validSite, // 工厂
        validReconciliationType // 对账类型
      }
    }
    // handleCreateStatement(args) {
    //   const constantType = {
    //     Add: "1", // 新增
    //     Edit: "2", // 编辑
    //     Look: "3", // 查看
    //   };
    //   const {
    //     selectRows,
    //     selectedIds,
    //     executeTaxedTotalPrice,
    //     executeUntaxedTotalPrice,
    //     taxAmount,
    //     // tabIndex,
    //   } = args;
    //   const {
    //     valid,
    //     validMarkFreeze, // 冻结状态
    //     validProvisional,
    //     // validBusinessType, // 业务类型
    //     // validCompany, // 公司
    //     // validSupplier, // 供应商
    //     // validCurrency, // 币种
    //     // validSite, // 工厂
    //     // validReconciliationType, // 对账类型
    //   } = this.checkSelectedValid({
    //     selectedData: selectRows,
    //     // actionType: this.actionType.createStatement,
    //   });
    //   if (!valid) {
    //     if (!validMarkFreeze) {
    //       this.$toast({
    //         content: this.$t("冻结状态为已冻结的数据不可创建对账单"),
    //         type: "warning",
    //       });
    //     } else if (!validProvisional) {
    //       this.$toast({
    //         content: this.$t("是否执行价为否的数据不可创建对账单"),
    //         type: "warning",
    //       });
    //     } else {
    //       this.$toast({
    //         content: this.$t(
    //           "请选择相同业务类型、公司、供应商、币种、工厂、对账类型的数据"
    //         ),
    //         type: "warning",
    //       });
    //     }

    //     return;
    //   }
    //   const createStatementData = {
    //     headerInfo: {
    //       reconciliationTypeCode: selectRows[0].reconciliationTypeCode, // 对账类型编码
    //       reconciliationTypeName: selectRows[0].reconciliationTypeName, // 对账类型名称
    //       companyCode: selectRows[0].companyCode, // 公司编码
    //       companyName: selectRows[0].companyName, // 公司名称
    //       supplierCode: selectRows[0].supplierCode, // 供应商编码
    //       supplierName: selectRows[0].supplierName, // 供应商名称
    //       businessTypeCode: selectRows[0].businessTypeCode, // 业务类型编码
    //       businessTypeId: selectRows[0].businessTypeId, // 业务类型id
    //       businessTypeName: selectRows[0].businessTypeName, // 业务类型名称
    //       companyId: selectRows[0].companyId, // 公司id
    //       supplierId: selectRows[0].supplierId, // 供应商id
    //       currencyName: selectRows[0].currencyName, // 币种
    //       siteName: selectRows[0]?.siteName, // 工厂
    //       siteCode: selectRows[0]?.siteCode,
    //       executeTaxedTotalPrice,
    //       executeUntaxedTotalPrice,
    //       taxAmount,
    //     }, // 头部信息
    //     requireData: selectRows, // 需求明细信息
    //     idList: selectedIds, // idList
    //     entryType: constantType.Add,
    //   };
    //   // 将信息放到 localStorage 创建对账页面 读
    //   localStorage.setItem(
    //     // "createStatementData",
    //     "overdueClearData",
    //     JSON.stringify(createStatementData)
    //   );
    //   // 将 lastTabIndex 放到 localStorage 创建对账页面 读
    //   // localStorage.setItem("lastTabIndex", JSON.stringify(tabIndex));
    //   // 跳转 创建对账单
    //   this.$router.push({
    //     // name: "create-statement",
    //     name: "overdue-clear-detail",
    //     query: { come: "clear" },
    //   });
    // },
  }
}
</script>

<style></style>
