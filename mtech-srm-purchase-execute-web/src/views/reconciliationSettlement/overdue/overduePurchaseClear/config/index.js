import { i18n } from '@/main.js'
import Vue from 'vue'
import { timeNumberToDate } from '@/utils/utils'
import { timeDate } from '../../../purchaseRecon/beRecon/beReconciled/config/columnComponent'
import { MasterDataSelect } from '@/utils/constant'
import { formatMasterFilter } from '@/utils/utils'

import { FrozenStatusOptions, ProvisionalEstimateStatusOptions } from './constant'
export const cols = [
  {
    width: '150',
    field: 'frozenStatus',
    headerText: i18n.t('冻结状态'),
    valueConverter: {
      type: 'map',
      map: FrozenStatusOptions
    }
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('凭证日期'),
    allowEditing: false,
    searchOptions: MasterDataSelect.dateRange
  },
  {
    field: 'reconciliationTypeName',
    headerText: i18n.t('对账类型名称')
  },
  {
    width: '150',
    field: 'lineNo',
    headerText: i18n.t('行号'),
    ignore: true
  },
  {
    width: '150',
    field: 'itemVoucherDate',
    headerText: i18n.t('物料凭证日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    width: '150',
    field: 'sourceHeaderCode',
    headerText: i18n.t('订单号')
  },
  {
    width: '150',
    field: 'orderTypeName',
    headerText: i18n.t('订单类型')
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    searchOptions: {
      ...MasterDataSelect.material,
      operator: 'in'
    }
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'projectName',
    headerText: i18n.t('项目名称')
  },
  {
    width: '150',
    field: 'projectCode',
    headerText: i18n.t('项目编号')
  },
  {
    width: '150',
    field: 'contractCode',
    headerText: i18n.t('合同编号')
  },
  {
    width: '150',
    field: 'spec',
    headerText: i18n.t('规格/型号')
  },
  {
    width: '150',
    field: 'categoryCode',
    headerText: i18n.t('品类')
  },
  {
    width: '150',
    field: 'customName',
    headerText: i18n.t('客户')
  },
  {
    width: '150',
    field: 'relationCustomOrder',
    headerText: i18n.t('销售订单号')
  },
  {
    width: '150',
    field: 'quantity',
    headerText: i18n.t('数量')
  },
  {
    width: '150',
    field: 'provisionalEstimateStatus',
    headerText: i18n.t('是否执行价'),
    valueConverter: {
      type: 'map',
      map: ProvisionalEstimateStatusOptions
    }
  },
  {
    width: '150',
    field: 'executeUntaxedUnitPrice',
    headerText: i18n.t('执行未税单价')
  },
  {
    width: '150',
    field: 'executeUntaxedTotalPrice',
    headerText: i18n.t('执行未税总价')
  },
  {
    width: '150',
    field: 'executeTaxedUnitPrice',
    headerText: i18n.t('执行含税单价')
  },
  {
    width: '150',
    field: 'executeTaxedTotalPrice',
    headerText: i18n.t('执行含税总价')
  },
  {
    width: '150',
    field: 'deductedTaxedPrice',
    headerText: i18n.t('扣款未税金额')
  },
  {
    width: '150',
    field: 'currencyName',
    headerText: i18n.t('币种'),
    searchOptions: MasterDataSelect.moneyType
  },
  {
    width: '150',
    field: 'companyCode',
    headerText: i18n.t('公司编号')
  },
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('公司名称'),
    searchOptions: MasterDataSelect.administrativeCompany
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('地点/工厂名称'),
    searchOptions: MasterDataSelect.factoryAddress
  },
  {
    width: '150',
    field: 'stockSite',
    headerText: i18n.t('库存地点')
  },
  {
    width: '150',
    field: 'paymentMode',
    headerText: i18n.t('付款方式')
  },
  {
    width: '150',
    field: 'receiveUserName',
    headerText: i18n.t('收货人')
  },
  {
    width: '150',
    field: 'receiveTime',
    headerText: i18n.t('收货时间'),
    searchOptions: MasterDataSelect.dateRange
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('采方备注')
  },
  {
    width: '150',
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: [
        {
          value: 0,
          text: i18n.t('未同步'),
          cssClass: 'col-notSynced'
        },
        {
          value: 1,
          text: i18n.t('已同步'),
          cssClass: 'col-synced'
        }
      ]
    }
  },
  {
    width: '150',
    field: 'receiveCode',
    headerText: i18n.t('物料凭证')
  },
  {
    width: '150',
    field: 'priceDateRangeStart',
    headerText: i18n.t('价格有效期开始'),
    searchOptions: {
      ...MasterDataSelect.dateRange
    }
  },
  {
    width: '150',
    field: 'priceDateRangeEnd',
    headerText: i18n.t('价格有效期结束'),
    searchOptions: {
      ...MasterDataSelect.dateRange
    }
  },
  {
    width: '150',
    field: 'taxRate',
    headerText: i18n.t('税率')
  }
  // {
  //   width: "250",
  //   field: "currencyCode",
  //   headerText: i18n.t("币种"),
  //   allowEditing: false,
  //   searchOptions: MasterDataSelect.money,
  //   valueAccessor: (field, data) => {
  //     return judgeFormatCodeName(data?.currencyCode, data?.currencyName);
  //   },
  // },
]
export const colsq = [
  {
    width: '150',
    field: 'frozenStatus',
    headerText: i18n.t('冻结状态'),
    valueConverter: {
      type: 'map',
      map: FrozenStatusOptions
    }
  },
  {
    field: 'reconciliationTypeName',
    headerText: i18n.t('对账类型名称'),
    width: '150'
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型名称'),
    width: '150'
  },
  {
    field: 'lineNo',
    headerText: i18n.t('行号'),
    width: '150'
  },
  {
    field: 'itemVoucherDate',
    headerText: i18n.t('物料凭证日期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    field: 'sourceHeaderCode',
    headerText: i18n.t('订单号'),
    width: '150'
  },
  {
    field: 'orderTypeName',
    headerText: i18n.t('订单类型'),
    width: '150'
  },
  {
    field: 'projectCode',
    headerText: i18n.t('项目编号'),
    width: '150'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码'),
    width: '150'
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称'),
    width: '150'
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    width: '150',
    searchOptions: {
      ...MasterDataSelect.material,
      operator: 'in'
    }
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: '150'
  },
  {
    field: 'spec',
    headerText: '规格/型号',
    width: '150'
  },
  {
    field: 'categoryCode',
    headerText: i18n.t('品类'),
    width: '150'
  },
  {
    field: 'customName',
    headerText: i18n.t('客户'),
    width: '150'
  },
  {
    field: 'relationCustomOrder',
    headerText: i18n.t('销售订单号'),
    width: '150'
  },
  {
    field: 'quantity',
    headerText: i18n.t('数量'),
    width: '150'
  },
  {
    field: 'unitName',
    headerText: i18n.t('单位'),
    width: '150'
  },
  {
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价'),
    width: '150'
  },
  {
    field: 'untaxedTotalPrice',
    headerText: i18n.t('未税总价'),
    width: '150'
  },
  {
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价'),
    width: '150'
  },
  {
    field: 'taxedTotalPrice',
    headerText: i18n.t('含税总价'),
    width: '150'
  },
  {
    field: 'provisionalEstimateStatus',
    headerText: i18n.t('是否执行价'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: ProvisionalEstimateStatusOptions
    }
  },
  {
    field: 'executeUntaxedUnitPrice',
    headerText: i18n.t('执行未税单价'),
    width: '150'
  },
  {
    field: 'executeUntaxedTotalPrice',
    headerText: i18n.t('执行未税总价'),
    width: '150'
  },
  {
    field: 'executeTaxedUnitPrice',
    headerText: i18n.t('执行含税单价'),
    width: '150'
  },
  {
    field: 'executeTaxedTotalPrice',
    headerText: i18n.t('执行含税总价'),
    width: '150'
  },
  {
    field: 'deductedTaxedPrice',
    headerText: i18n.t('扣款未税金额'),
    width: '150'
  },
  {
    field: 'currencyName',
    headerText: i18n.t('币种'),
    width: '150',
    searchOptions: MasterDataSelect.moneyType
  },
  {
    width: '150',
    field: 'companyCode',
    headerText: i18n.t('公司编号')
  },
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('公司名称'),
    searchOptions: MasterDataSelect.administrativeCompany
  },
  {
    width: '150',
    field: 'sitecode',
    headerText: i18n.t('地点/工厂名称'),
    searchOptions: MasterDataSelect.factoryAddress
  },
  {
    field: 'stockSite',
    headerText: i18n.t('库存地点'),
    width: '150'
  },
  {
    field: 'receiveUserName',
    headerText: i18n.t('收货人'),
    width: '150'
  },
  {
    field: 'receiveTime',
    headerText: i18n.t('收货时间'),
    width: '150',
    searchOptions: MasterDataSelect.dateRange
  },
  {
    field: 'remark',
    headerText: i18n.t('采方备注'),
    width: '150'
  },
  {
    field: 'inOutType',
    headerText: i18n.t('出入库类型'),
    width: '150'
  },
  {
    field: 'receiveCode',
    headerText: i18n.t('物料凭证'),
    width: '150'
  },
  {
    field: 'priceDateRangeStartDate',
    headerText: i18n.t('价格有效期开始'),
    width: '150',
    searchOptions: {
      ...MasterDataSelect.dateRange
    }
  },
  {
    field: 'priceDateRangeEndDate',
    headerText: i18n.t('价格有效期结束'),
    width: '150',
    searchOptions: {
      ...MasterDataSelect.dateRange
    }
  },
  {
    field: 'taxRate',
    headerText: i18n.t('税率'),
    width: '150'
  },
  {
    field: 'rateDifferent',
    headerText: i18n.t('差异率'),
    width: '150'
  },
  {
    field: 'amountDifferent',
    headerText: i18n.t('金额差异'),
    width: '150'
  },
  {
    field: 'taxName',
    headerText: i18n.t('税率名称'),
    width: '150'
  },
  {
    field: 'receiveItemNo',
    headerText: i18n.t('物料凭证行号'),
    width: '150'
  },
  {
    field: 'batchCode',
    headerText: i18n.t('钢材卷号'),
    width: '150'
  },
  {
    field: 'itemText',
    headerText: i18n.t('物料长文本'),
    width: '150'
  }
]
export const Tab = {
  toBeReconciled: 0 // 待对账
}
export const SyncStatus = {
  synced: 1, // 是
  notSynced: 0 // 否
}
// 冻结标记
export const FrozenStatus = {
  markFreeze: 1, // 是
  unmark: 0 // 否
}
export const formatTableColumnData = (config) => {
  const { tab, data } = config
  const colData = []

  if (Tab.toBeReconciledDetail === tab) {
    // 待对账明细

    // 数组开头固定添加 i18n.t("冻结状态") 列
    data.unshift({
      code: 'frozenStatus',
      name: i18n.t('冻结状态')
    })

    data.forEach((col) => {
      let defaultCol = {
        field: col.code,
        headerText: col.name,
        width: data.length > 10 ? col?.width || '150' : 'auto'
      }
      // 处理 搜索使用 主数据选择器
      defaultCol = formatMasterFilter(defaultCol)

      if (defaultCol.field === 'frozenStatus') {
        // 冻结状态
        defaultCol.valueConverter = {
          type: 'map',
          map: FrozenStatusOptions
        }
      } else if (defaultCol.field === 'advanceInvoicing') {
        // 提前开票
        defaultCol.valueConverter = {
          type: 'map',
          map: {
            0: i18n.t('否'),
            1: i18n.t('是')
          }
        }
      } else if (defaultCol.field === 'syncStatus') {
        // 同步状态
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: SyncStatus.notSynced,
              text: i18n.t('未同步'),
              cssClass: 'col-notSynced'
            },
            {
              value: SyncStatus.synced,
              text: i18n.t('已同步'),
              cssClass: 'col-synced'
            }
          ]
        }
      } else if (defaultCol.field === 'receiveTime') {
        // 收货时间
        defaultCol.template = timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (defaultCol.field === 'fileBaseInfoList') {
        // 文件信息
        defaultCol.template = () => {
          return {
            template: Vue.component('fileBaseInfoList', {
              template: `<div @click="showFileBaseInfo" class="cell-operable-title">{{data.fileBaseInfoList | listNumFormat}}</div>`,
              data: function () {
                return { data: {} }
              },
              filters: {
                listNumFormat(value) {
                  if (value && value.length > 0) {
                    return value.length
                  } else {
                    return ''
                  }
                }
              },
              methods: {
                showFileBaseInfo() {
                  this.$parent.$emit('showFileBaseInfo', {
                    index: this.data.index,
                    value: this.data.fileBaseInfoList
                  })
                }
              }
            })
          }
        }
      } else if (defaultCol.field === 'sourceType') {
        // 来源类型（枚举） 0: 上游流入1:第三方接口
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('上游流入'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('第三方接口'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'status') {
        // 单据状态 0:待对账 1:已创建对账单
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('待对账'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('已创建对账单'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'prePayStatus') {
        // 是否预付 0-否；1-是
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('否'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('是'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'itemVoucherDate') {
        // 物料凭证日期
        defaultCol.template = timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (defaultCol.field === 'provisionalEstimateStatus') {
        if (defaultCol.headerText == '是否执行价') {
          // 是否执行价 0-是；1-否
          defaultCol.valueConverter = {
            type: 'map',
            map: [
              {
                value: 0,
                text: i18n.t('是'),
                cssClass: ''
              },
              {
                value: 1,
                text: i18n.t('否'),
                cssClass: ''
              }
            ]
          }
        } else {
          // 是否暂估价 0-否；1-是
          defaultCol.valueConverter = {
            type: 'map',
            map: [
              {
                value: 0,
                text: i18n.t('否'),
                cssClass: ''
              },
              {
                value: 1,
                text: i18n.t('是'),
                cssClass: ''
              }
            ]
          }
        }
      } else if (defaultCol.field === 'createTime') {
        // 创建时间
        defaultCol.template = timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (defaultCol.field === 'updateTime') {
        // 最后修改时间
        defaultCol.template = timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (defaultCol.field === 'type') {
        // 待对账类型:0-采购待对账；1-销售待对账
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('采购待对账'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('销售待对账'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'priceDateRangeStartDate') {
        // 价格有效期开始
        defaultCol.template = timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
        defaultCol.ignore = true
        defaultCol.searchOptions = MasterDataSelect.timeOnly
      } else if (defaultCol.field === 'priceDateRangeEndDate') {
        // 价格有效期结束
        defaultCol.template = timeDate({
          dataKey: defaultCol.field,
          hasTime: true
        })
        defaultCol.ignore = true
        defaultCol.searchOptions = MasterDataSelect.timeOnly
      } else if (defaultCol.field === 'realPriceStatus') {
        // 正式价标识 0-无正式价；1-有正式价
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('无正式价'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('有正式价'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'inOutType') {
        // 出入库类型 0-采购；1-销售；
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('采购'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('销售'),
              cssClass: ''
            }
          ]
        }
      } else if (defaultCol.field === 'supplierName') {
        defaultCol.searchOptions = {
          ...MasterDataSelect.supplierAll,
          renameField: 'supplierCode'
        }
      }
      colData.push(defaultCol)
    })
  } else if (Tab.toBeReconciled === tab) {
    // 待对账
    data.forEach((col) => {
      const defaultCol = {
        ...col,
        field: col.fieldCode,
        headerText: col.fieldName,
        width: col?.width || '150'
      }
      if (defaultCol.field === 'dataLength') {
        // 详情明细 明细数据条数
        defaultCol.cellTools = []
        defaultCol.ignore = true
      }
      colData.push(defaultCol)
    })
  }

  return colData
}
export const ConstantType = {
  Add: '1', // 新增
  Edit: '2', // 编辑
  Look: '3' // 查看
}
