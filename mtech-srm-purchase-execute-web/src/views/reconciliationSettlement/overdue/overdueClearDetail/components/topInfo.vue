<template>
  <div :class="['top-info', !isExpand && 'top-info-small']" v-if="topInfo">
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="infos mr20">{{ $t('对账单号：') }}{{ topInfo.orderCode }}</div>
      <div class="infos mr20">{{ $t('创建人：') }}{{ topInfo.createUserName }}</div>
      <div class="infos">{{ $t('创建时间：') }}{{ topInfo.createTime }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" @click="saveData">{{
        $t('提交')
      }}</mt-button>

      <div class="sort-box" @click="isExpand = !isExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>
    <div class="main-bottom" v-show="isExpand">
      <mt-form ref="ruleForm" :model="topInfo">
        <mt-form-item prop="company" :label="$t('公司编号')">
          <mt-input v-model="topInfo.company" :disabled="true"></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplier" :label="$t('供应商编号')">
          <mt-input v-model="topInfo.supplier" :disabled="true"></mt-input>
        </mt-form-item>

        <mt-form-item prop="site" :label="$t('工厂')">
          <mt-input v-model="topInfo.site" :disabled="true"></mt-input>
        </mt-form-item>

        <mt-form-item prop="executeTaxedTotalPrice" :label="$t('执行含税总金额')">
          <mt-input
            v-model="topInfo.executeTaxedTotalPrice"
            :disabled="true"
            placeholder=""
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="executeUntaxedTotalPrice" :label="$t('执行未税总金额')">
          <mt-input
            v-model="topInfo.executeUntaxedTotalPrice"
            :disabled="true"
            placeholder=""
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="taxAmount" :label="$t('税额')">
          <mt-input v-model="topInfo.taxAmount" :disabled="true" placeholder=""></mt-input>
        </mt-form-item>

        <mt-form-item prop="currencyName" :label="$t('币种')">
          <mt-input v-model="topInfo.currencyName" :disabled="true" placeholder=""></mt-input>
        </mt-form-item>

        <mt-form-item prop="reconciliationTypeName" :label="$t('对账类型')">
          <mt-input
            v-model="topInfo.reconciliationTypeName"
            :disabled="true"
            placeholder=""
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="remark" :label="$t('采方备注')" :class="['full-width']">
          <mt-input :multiline="true" v-model="topInfo.remark" max-length="200"></mt-input>
        </mt-form-item>

        <mt-form-item prop="feedbackRemark" :class="['full-width']" :label="$t('供方备注')">
          <mt-input
            :multiline="true"
            v-model="topInfo.feedbackRemark"
            :disabled="isFeedbackRemarkEditable"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isExpand: true,
      rules: {}
    }
  },
  computed: {
    // 采方创建或状态为待反馈以外的场合不可编辑
    isFeedbackRemarkEditable() {
      return this.topInfo.sourcePath != 1 || this.topInfo.status != 1 || this.topInfo.type != 1
    },

    topInfo() {
      let newVal = this.headerInfo
      if (newVal) {
        let topInfo = {
          ...newVal
        }
        // 公司
        if (newVal.companyCode) {
          topInfo.company = newVal.companyCode + '-' + newVal.companyName
        }
        // 供应商
        if (newVal.supplierCode) {
          topInfo.supplier = newVal.supplierCode + '-' + newVal.supplierName
        }
        // 工厂
        if (newVal.siteCode) {
          topInfo.site = newVal.siteCode + '-' + newVal.siteName
        }
        return topInfo
      }
      return {}
    }
  },
  methods: {
    goBack() {
      this.$router.push({
        name: 'overdue-purchase-clear'
      })
    },
    saveData() {
      const params = {
        remark: this.topInfo.remark
      }
      this.$emit('submitOverdueClear', params)
    }
  }
}
</script>

<style lang="scss" scoped>
.top-info {
  display: flex;
  flex-direction: column;
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 0 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    // height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .mr20 {
      margin-right: 20px;
    }
    .status-box {
      line-height: 20px;
      padding: 4px;
      border-radius: 2px;
      margin: 0 36px 0 10px;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      &-0 {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
      &-1 {
        color: #eda133;
        background: rgba(237, 161, 51, 0.1);
      }
      &-2 {
        color: #8acc40;
        background: rgba(138, 204, 64, 0.1);
      }
      &-3 {
        color: #ed5633;
        background: rgba(237, 86, 51, 0.1);
      }
      &--1 {
        color: #9a9a9a;
        background: rgba(154, 154, 154, 0.1);
      }
    }
    .middle-blank {
      flex: 1;
    }
    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    padding-bottom: 20px;
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin: 20px 20px 0 10px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
    /deep/.disabled-label .mt-form-item-topLabel .label {
      color: #9a9a9a;
    }
  }
}
</style>
