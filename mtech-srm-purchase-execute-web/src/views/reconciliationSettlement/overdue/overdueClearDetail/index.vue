<template>
  <div class="full-height overdue-clear-detail detail-fix-wrap">
    <top-info :header-info="headerInfo" @submitOverdueClear="submitOverdueClear"></top-info>

    <div class="bottom-tables">
      <mt-tabs
        tab-id="reconci-tab"
        :e-tab="false"
        :data-source="tabList"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>

      <div class="bottom-box">
        <!-- 对账明细 -->
        <div class="grid-wrap" v-show="currentTabInfo.code == 'reconciliationField'">
          <mt-template-page
            ref="grid0"
            :template-config="componentConfig"
            @actionBegin="actionBegin"
            @actionComplete="actionComplete"
            @handleClickToolBar="handleClickToolBar"
          ></mt-template-page>
        </div>

        <!-- 相关附件 -->
        <relative-file
          ref="relativeFileRef"
          v-show="currentTabInfo.code == 'reconciliationFile'"
          :module-file-list="moduleFileList"
        ></relative-file>
      </div>
    </div>
  </div>
</template>

<script>
import { cols } from '@/views/reconciliationSettlement/overdue/overduePurchaseClear/config'
import { nodeType } from '@/components/businessComponents/relativeFileNoDocId/config/relative.js'
import { remarkCol } from './config'
export default {
  components: {
    topInfo: require('./components/topInfo.vue').default,
    relativeFile: require('@/components/businessComponents/relativeFileNoDocId/index.vue').default
  },
  data() {
    const overdueClearData = JSON.parse(localStorage.getItem('overdueClearData'))
    return {
      overdueClearData,
      headerInfo: overdueClearData?.headerInfo, // 头部信息
      tabList: [
        {
          title: this.$t('对账明细'),
          code: 'reconciliationField'
        },
        {
          title: this.$t('相关附件'),
          code: 'reconciliationFile'
        }
      ],
      currentTabInfo: {
        code: 'reconciliationField'
      },
      componentConfig: [
        // 对账明细
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 不使用组件中的toolbar配置
          toolbar: [
            [
              {
                id: 'Remove',
                icon: 'icon_table_remove',
                title: this.$t('移除')
              }
            ]
          ],
          gridId: this.$tableUUID.reconciliationSettlement.overduePurchaseCClear.addList,
          grid: {
            height: 'auto',
            lineSelection: 0,
            allowPaging: false, // 不分页
            columnData: cols.concat(remarkCol),
            dataSource: overdueClearData?.requireData,
            editSettings: { allowEditing: true }
          }
        }
      ],
      isInEdit: false, // 处于编辑状态
      moduleFileList: [
        {
          id: '01',
          code: 'reconciliation_settled_header_file', // 清账整单附件code
          nodeName: '采方 - 整单附件',
          type: nodeType.mainUpdate
        },
        {
          id: '02',
          code: 'reconciliation_gong',
          nodeName: '供方 - 整单附件',
          type: nodeType.mainView
        }
      ]
    }
  },

  mounted() {
    // document.body.addEventListener("click", (e) => {
    //   console.log("点击咯", e);
    //   e.cancelBubble = true;
    //   e.stopPropagation();
    //   e.preventDefault();
    //   return false;
    // });
  },

  methods: {
    // 点击 tab
    handleSelectTab(e) {
      this.currentTabInfo = this.tabList[e]
    },

    handleClickToolBar(e) {
      const selectRows = e.grid.getSelectedRecords()
      let ids = selectRows.map((i) => i.id)
      if (e.toolbar.id === 'Remove') {
        this.handleDelete(ids)
      }
    },

    actionBegin(args) {
      console.log('actionBegin', args)
      // 记录开始 处于编辑状态
      if (args.requestType == 'beginEdit') {
        this.isInEdit = true
      }
      if (args.requestType == 'refresh') {
        this.isInEdit = false // 结束编辑状态
      }
    },

    actionComplete(args) {
      console.log('actionComplete', args)

      setTimeout(() => {
        if (args.requestType == 'save') {
          this.isInEdit = false // 结束编辑状态
        }
      }, 10)
    },

    // 移除
    handleDelete(selectedIds) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认移除选中的数据？')
        },
        success: () => {
          const tableData = this.componentConfig[0].grid.dataSource
          selectedIds.forEach((id) => {
            for (let i = 0; i < tableData.length; i++) {
              if (tableData[i].id === id) {
                // 计算执行未税/含税总价
                this.headerInfo.executeTaxedTotalPrice -= tableData[i].taxTotal
                this.headerInfo.executeUntaxedTotalPrice -= tableData[i].freeTotal
                // 移除
                tableData.splice(i, 1)
                break
              }
            }
          })
        }
      })
    },

    // 提交  采方
    submitOverdueClear(topInfo) {
      // 对账明细
      const ref = this.$refs.grid0?.getCurrentUsefulRef().gridRef?.ejsRef
      let _dataSource = ref?.getCurrentViewRecords() || []
      console.log('_dataSource', _dataSource)

      // 相关附件
      let _uploadRefId = this.moduleFileList.find(
        (item) => item.code == 'reconciliation_settled_header_file'
      )?.id
      var uploadFiles = this.$refs.relativeFileRef.getUploadFlies(_uploadRefId)
      var fileList = this.formatUploadFiles(uploadFiles)

      let params = {
        ...topInfo,
        reconciliationSettledList: _dataSource,
        fileList
      }
      this.$API.reconciliationSettlement.putCreateOverdue(params).then((res) => {
        if (res?.code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$router.replace('overdue-purchase-clear')
        }
      })
    },

    // 格式化附件信息
    formatUploadFiles(list) {
      const tmp = []
      list.forEach((item) => {
        tmp.push({
          fileName: item.fileName, //	文件上传名称
          fileSize: item.fileSize, //	文件大小
          fileType: item.fileType, //	文件类型
          sysFileId: item.id || item.sysFileId, //	文件ID(MT_WP_SYS_FILE表ID)
          url: item.url, //	文件路径
          nodeCode: 'reconciliation_settled_header_file'
        })
      })

      return tmp
    }
  },

  beforeDestroy() {
    localStorage.removeItem('overdueClearData')
  }
}
</script>

<style lang="scss" scoped>
.overdue-clear-detail {
  display: flex;
  flex-direction: column;

  .mt-tabs {
    width: 100%;
    /deep/.mt-tabs-container {
      width: 100%;
    }
  }

  .bottom-tables {
    flex: 1;
    overflow: auto;

    /deep/ .repeat-template {
      .mt-data-grid,
      .mt-data-grid > .e-control {
        height: 100%;

        .e-gridcontent {
          height: calc(100% - 44px);

          .e-content {
            height: 100%;
          }
        }
      }
    }
    /deep/ .table-container {
      height: 100%;
      max-height: 100%;
    }
  }
}
</style>
