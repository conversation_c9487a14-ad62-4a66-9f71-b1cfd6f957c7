<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div :class="['status-box', 'status-box' + '-' + topInfo.status]">
        <span v-if="topInfo.status == 1">{{ $t('未确认') }}</span>
        <span v-if="topInfo.status == 2">{{ $t('已确认') }}</span>
        <span v-if="topInfo.status == 3">{{ $t('已退回') }}</span>
        <span v-if="topInfo.status == 4">{{ $t('已作废') }}</span>
      </div>
      <div class="infos mr20">{{ $t('对账单号：') }}{{ topInfo.recoCode }}</div>
      <div class="infos mr20">{{ $t('创建人：') }}{{ topInfo.createUserName }}</div>
      <div class="infos">{{ $t('创建时间：') }}{{ topInfo.createTime }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>

      <div class="sort-box" @click="isExpand = !isExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>
    <div class="main-bottom" v-show="isExpand">
      <mt-form ref="ruleForm" :model="topInfo">
        <mt-form-item prop="company" :label="$t('公司')" class="disabled-label">
          <mt-input v-model="topInfo.companyName" :disabled="true"></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplierCode" :label="$t('供应商代码')" class="disabled-label">
          <mt-input v-model="topInfo.supplierCode" :disabled="true"></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplierName" :label="$t('供应商名称')" class="disabled-label">
          <mt-input v-model="topInfo.supplierName" :disabled="true"></mt-input>
        </mt-form-item>
        <mt-form-item prop="recoTypeCode" :label="$t('对账类型')">
          <!-- <mt-input
            v-model="topInfo.recoTypeCode"
            :disabled="true"
            :placeholder="$t('对账类型')"
          ></mt-input> -->
          <mt-select
            v-model="topInfo.recoTypeCode"
            :data-source="recoTypeOptions"
            :disabled="true"
            :placeholder="$t('对账类型')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
/**
 * topInfo.status:  0-未发布，1. 待反馈，2. 反馈正常，3. 反馈异常，-1. 已关闭
 * topInfo.sourcePath(创建方):   0- 采方, 1- 供方
 * $route.query.type: 0-查看 , 1- 编辑
 */
export default {
  props: {
    entryType: {
      type: String,
      default: '1'
    },
    headerInfo: {
      type: Object,
      default: () => {}
    },
    entryId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      recoTypeOptions: [
        { value: 'A', text: this.$t('标准对账单'), cssClass: '' },
        { value: 'C', text: this.$t('标准追溯对账单'), cssClass: '' },
        { value: 'B', text: this.$t('寄售对账单'), cssClass: '' },
        { value: 'D', text: this.$t('寄售追溯对账单'), cssClass: '' },
        { value: 'E', text: this.$t('采购/外协对账单'), cssClass: '' },
        { value: 'F', text: this.$t('注塑申购对账单'), cssClass: '' }
      ],
      isExpand: true,
      rules: {},
      topInfo: {}
    }
  },
  watch: {
    headerInfo(newVal) {
      if (newVal) {
        this.topInfo = {
          ...newVal
        }
      }
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 0 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .mr20 {
      margin-right: 20px;
    }
    .status-box {
      padding: 2px 6px;
      border-radius: 2px;
      margin: 0 36px 0 10px;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      &-0 {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
      &-1 {
        color: #eda133;
        background: rgba(237, 161, 51, 0.1);
      }
      &-2 {
        color: #8acc40;
        background: rgba(138, 204, 64, 0.1);
      }
      &-3 {
        color: #ed5633;
        background: rgba(237, 86, 51, 0.1);
      }
      &-4 {
        color: #9a9a9a;
        background: rgba(154, 154, 154, 0.1);
      }
    }
    .middle-blank {
      flex: 1;
    }
    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    padding-bottom: 20px;
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin: 20px 20px 0 10px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
    /deep/.disabled-label .mt-form-item-topLabel .label {
      // color: #9a9a9a;
    }
  }
}
</style>
