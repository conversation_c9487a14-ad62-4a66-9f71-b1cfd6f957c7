import { i18n } from '@/main.js'
import { judgeFormatCodeName } from '@/utils/utils'

export const statusOptions = [
  { value: 1, text: i18n.t('未确认'), cssClass: 'col-abnormal' },
  { value: 2, text: i18n.t('已确认'), cssClass: 'col-active' },
  { value: 3, text: i18n.t('已退回'), cssClass: 'col-normal' },
  { value: 4, text: i18n.t('已作废'), cssClass: 'col-published' }
]

export const recoTypeOptions = [
  { value: 'A', text: i18n.t('标准对账单'), cssClass: '' },
  { value: 'C', text: i18n.t('标准追溯对账单'), cssClass: '' }
]

export const columnData = [
  // {
  //   width: '200',
  //   field: 'lineNo',
  //   headerText: i18n.t('行号')
  // },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    isPrimaryKey: true
  },
  {
    width: '120',
    field: 'companyCode',
    headerText: i18n.t('公司编码')
    // searchOptions: MasterDataSelect.businessCompany
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.companyCode, data?.companyName)
    // }
  },
  {
    // width: "150",
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    width: '250',
    field: 'factoryCode',
    headerText: i18n.t('工厂代码')
  },
  {
    width: '200',
    field: 'recoCode',
    headerText: i18n.t('对账单号')
  },
  {
    width: '120',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    // }
  },
  {
    // width: '230',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'sourceOrderCode',
    headerText: i18n.t('采购订单号')
  },
  {
    width: '120',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.itemCode, data?.itemName)
    // }
  },
  {
    // width: '120',
    field: 'itemName',
    headerText: i18n.t('物料名称')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.itemCode, data?.itemName)
    // }
  },
  {
    field: 'orderItemNo',
    headerText: i18n.t('行项目')
  },
  {
    field: 'location',
    headerText: i18n.t('库位')
  },
  {
    field: 'materialVoucherDate',
    headerText: i18n.t('入库时间'),
    valueAccessor: (field, data) => {
      let date = ''
      if (data?.materialVoucherDate) {
        date = data?.materialVoucherDate.split(' ')[0]
      }
      return date
    }
  },
  {
    field: 'orderDate',
    headerText: i18n.t('订单建立日期'),
    valueAccessor: (field, data) => {
      let date = ''
      if (data?.materialVoucherDate) {
        date = data?.materialVoucherDate.split(' ')[0]
      }
      return date
    }
  },
  {
    field: 'qty',
    headerText: i18n.t('入库数量')
  },
  {
    field: 'currencyCode',
    headerText: i18n.t('币种'),
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.currencyCode, data?.currencyName)
    }
  },
  {
    field: 'unitPriceUntaxed',
    headerText: i18n.t('原单价')
  },
  {
    field: 'executionUnitPriceUntaxed',
    headerText: i18n.t('条件价格')
  },
  {
    field: 'totalAmtUntaxed',
    headerText: i18n.t('原金额')
  },
  {
    field: 'executionTotalPriceUntaxed',
    headerText: i18n.t('应执行金额')
  },
  {
    field: 'deductionAmtUntaxed',
    headerText: i18n.t('扣款金额')
  },
  {
    field: 'receiveCode',
    headerText: i18n.t('入库单号')
  },
  {
    field: 'deliveryCode',
    headerText: i18n.t('送货单号')
  },
  {
    field: 'recordDate',
    headerText: i18n.t('记录日期')
  },
  {
    field: 'supplierRemark',
    headerText: i18n.t('供应商备注')
  },
  {
    field: 'buyerHistoryRemark',
    headerText: i18n.t('采购方历史备注')
  }
]
