import { i18n } from '@/main.js'
// import { judgeFormatCodeName } from '@/utils/utils'

export const monthList = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']

const currentYear = new Date().getFullYear()
const years = []
for (let i = 0; i < 19; i++) {
  const year = currentYear + 2 - i
  years.push(String(year))
}
export const yearList = years

export const columnData = [
  {
    field: 'materialVoucherYear',
    // width: "200",
    headerText: i18n.t('对账日期')
  },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    isPrimaryKey: true
  },
  {
    width: '250',
    field: 'factoryCode',
    headerText: i18n.t('工厂代码')
  },
  {
    width: '120',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    // }
  },
  {
    // width: '230',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '120',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.itemCode, data?.itemName)
    // }
  },
  {
    // width: '120',
    field: 'itemName',
    headerText: i18n.t('物料名称')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.itemCode, data?.itemName)
    // }
  },
  {
    field: 'initialStageInv',
    headerText: i18n.t('期初库存')
  },
  {
    field: 'curMonthInstockQty',
    headerText: i18n.t('本月入库')
  },
  {
    field: 'curMonthOutstockQty',
    headerText: i18n.t('本月出库')
  },
  {
    field: 'curMonthAllotQty',
    headerText: i18n.t('本月移库')
  },
  {
    field: 'curMonthBalanceQty',
    headerText: i18n.t('本月结余')
  }
]
