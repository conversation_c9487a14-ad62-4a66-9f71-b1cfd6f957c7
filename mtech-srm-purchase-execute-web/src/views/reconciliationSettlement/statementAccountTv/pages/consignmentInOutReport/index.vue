<template>
  <!-- 采方-对账协同-采购对账单列表 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="toolbarClick"
      @handleCustomReset="handleCustomReset"
      class="template-height"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
            <mt-form-item prop="factoryCodes" :label="$t('工厂')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.factoryCodes"
                :url="$API.masterData.getSiteListUrl"
                multiple
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                :search-fields="['siteName', 'siteCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.itemCode"
                :url="$API.masterData.getItemUrl"
                :placeholder="$t('请选择物料')"
                :fields="{ text: 'itemName', value: 'itemCode' }"
                :search-fields="['itemName', 'itemCode']"
              ></RemoteAutocomplete>
              <!-- <mt-input v-model="searchFormModel.itemCode" /> -->
            </mt-form-item>

            <mt-form-item prop="materialVoucherYear" :label="$t('年度')">
              <mt-select
                v-model="searchFormModel.materialVoucherYear"
                :data-source="yearList"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择年度')"
              />
            </mt-form-item>
            <mt-form-item prop="materialVoucherMonth" :label="$t('月份')">
              <mt-select
                v-model="searchFormModel.materialVoucherMonth"
                :data-source="monthList"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择月份')"
              />
            </mt-form-item>
            <mt-form-item
              v-if="!isSup"
              prop="supplierCodes"
              :label="$t('供应商编码')"
              label-style="top"
            >
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.supplierCodes"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择供应商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { columnData, yearList, monthList } from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'
import Vue from 'vue'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  mounted() {
    this.setToolbar()
  },
  data() {
    // const currentMonth = new Date().getMonth() + 1
    const lastMonth = dayjs().subtract(1, 'month').format('YYYY-MM')
    return {
      lastMonth,
      yearList,
      monthList,
      searchFormModel: {
        // materialVoucherYear: new Date().getFullYear().toString(),
        // materialVoucherMonth: currentMonth.toString().padStart(2, '0')
        materialVoucherYear: lastMonth.split('-')[0],
        materialVoucherMonth: lastMonth.split('-')[1]
      },
      searchFormRules: {
        materialVoucherYear: [{ required: true, message: this.$t('请选择年度'), trigger: 'blur' }],
        materialVoucherMonth: [{ required: true, message: this.$t('请选择月份'), trigger: 'blur' }],
        supplierCodes: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }]
      },
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          toolbar: {
            tools: [
              [
                {
                  id: 'excelExport',
                  icon: 'icon_solid_export',
                  title: this.$t('导出')
                }
              ],
              []
            ]
          },
          grid: {
            // height: 566,
            virtualPageSize: 30,
            enableVirtualization: true,
            columnData: columnData,
            dataSource: [],
            pageSettings: {
              currentPage: 1,
              pageSize: 50,
              pageSizes: [50, 100, 200, 1000],
              totalRecordsCount: 0
            },
            selectionSettings: {
              persistSelection: true, // 这个选项很重要，影响到虚拟滚动下能不能操作全选按钮
              type: 'Multiple',
              checkboxOnly: true
            },
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliation/consignmentItem/query/report`,
              ignoreDefaultSearch: true,
              recordsPosition: 'data.records',
              params: {},
              ignoreSearchToast: true
            },
            lineSelection: 0,
            lineIndex: 1
            // frozenColumns: 1
          }
        }
      ]
    }
  },
  computed: {
    isSup() {
      return this.$route.name === 'sup-query-statement-tv'
    }
  },
  methods: {
    setToolbar() {
      const toolbar = []
      if (this.$route.name === 'pur-query-statement-tv') {
        toolbar.push({
          id: 'excelExport',
          icon: 'icon_solid_export',
          title: this.$t('导出')
        })
      } else {
        toolbar.push(
          {
            id: 'excelExport', // 共用的
            icon: 'icon_solid_export',
            title: this.$t('导出')
          },
          {
            id: 'printRecon', // 共用的
            icon: 'icon_table_print',
            title: this.$t('打印')
          }
        )
      }
      this.pageConfig[0].toolbar = { tools: [toolbar, []] }
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = dayjs(e.startDate).format('YYYYMM')
        this.searchFormModel[field + 'End'] = dayjs(e.endDate).format('YYYYMM')
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    toolbarClick(e) {
      if (e.toolbar.id == 'excelExport') {
        // 导出
        this.handleExport()
      }
      if (e.toolbar.id == 'printRecon') {
        if (!this.searchFormModel.materialVoucherYear) {
          this.$toast({ content: this.$t('请选择年度'), type: 'warning' })
          return
        }
        if (!this.searchFormModel.materialVoucherMonth) {
          this.$toast({ content: this.$t('请选择月份'), type: 'warning' })
          return
        }
        // 打印
        this.handlePrint()
      }
    },
    // 打印
    handlePrint() {
      const params = {
        materialVoucherYear: this.searchFormModel.materialVoucherYear,
        materialVoucherMonth: this.searchFormModel.materialVoucherMonth
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlementTv
        .queryFactoryPrintCodes(params)
        .then((res) => {
          this.apiEndLoading()
          const { code, data } = res
          if (code === 200) {
            for (let i = 0; i < data.length; i++) {
              this.batchPrint(data[i])
            }
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    batchPrint(headerId) {
      this.apiStartLoading()
      let params = {
        materialVoucherYear: this.searchFormModel.materialVoucherYear,
        materialVoucherMonth: this.searchFormModel.materialVoucherMonth,
        headerId
      }
      this.$API.reconciliationSettlementTv
        .printConsignSaveReportRecon(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = () => {
              console.log('======', reader)
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }

            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
          window.open(this.pdfUrl)
          // let date = new Date().getTime()
          // let ifr = document.createElement('iframe')
          // ifr.style.frameborder = 'no'
          // ifr.style.display = 'none'
          // ifr.style.pageBreakBefore = 'always'
          // ifr.setAttribute('id', 'printPdf' + date)
          // ifr.setAttribute('name', 'printPdf' + date)
          // ifr.src = this.pdfUrl
          // document.body.appendChild(ifr)
          // this.doPrint('printPdf' + date)
          // window.URL.revokeObjectURL(ifr.src)
        })
        .catch(() => {
          this.apiEndLoading()
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    // 导出
    handleExport() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        page: { current: 1, size: 10000 }
        // rules: rule.rules || []
      }
      if (rule.rules && rule.rules.length) {
        rule.rules.forEach((item) => {
          params[item.field] = item.value
        })
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlementTv
        .reconConsignmentItemExport(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.searchFormModel.materialVoucherYear = this.lastMonth.split('-')[0]
      this.searchFormModel.materialVoucherMonth = this.lastMonth.split('-')[1]
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style></style>
