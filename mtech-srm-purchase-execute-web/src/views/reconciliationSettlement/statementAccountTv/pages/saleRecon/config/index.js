import { i18n } from '@/main.js'
import dayjs from 'dayjs'

export const statusOptions = [
  { text: i18n.t('未确认'), value: 1 },
  { text: i18n.t('已确认'), value: 2 },
  { text: i18n.t('已退回'), value: 3 },
  { text: i18n.t('已关闭'), value: 4 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'companyCode',
    title: i18n.t('公司'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.companyName : ''
    }
  },
  // {
  //   field: 'factoryCode',
  //   title: i18n.t('工厂'),
  //   minWidth: 160
  // },
  {
    field: 'recoCode',
    title: i18n.t('对账单号'),
    minWidth: 120,
    slots: {
      default: 'recoNoDetault'
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    }
  },
  {
    field: 'recoYearMonth',
    title: i18n.t('对账日期'),
    minWidth: 160
  },
  {
    field: 'purRemark',
    title: i18n.t('采方备注'),
    minWidth: 160
  },
  {
    field: 'supplierRemark',
    title: i18n.t('供方备注'),
    minWidth: 160
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建日期'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return dayjs(Number(cellValue)).format('YYYY-MM-DD')
    }
  }
]
