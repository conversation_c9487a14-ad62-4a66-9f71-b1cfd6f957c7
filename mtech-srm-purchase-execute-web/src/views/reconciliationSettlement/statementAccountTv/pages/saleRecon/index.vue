<!-- 查询对账单-销售对账 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('公司')" prop="companyCode">
          <mt-select
            v-model="searchFormModel.companyCode"
            :data-source="companyOptions"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('工厂')" prop="factoryList">
          <RemoteAutocomplete
            v-model="searchFormModel.factoryList"
            :url="$API.masterData.getSiteListUrl"
            :placeholder="$t('请选择工厂')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
            multiple
          />
        </mt-form-item>
        <mt-form-item :label="$t('对账单号')" prop="recoCode">
          <mt-input
            v-model="searchFormModel.recoCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供应商')" prop="supplierCode">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('状态')" prop="status">
          <mt-select
            v-model="searchFormModel.status"
            :data-source="statusOptions"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('对账日期')" prop="recoTime">
          <mt-date-range-picker
            v-model="searchFormModel.recoTime"
            @change="recoTimeChange"
            :placeholder="$t('请选择对账日期')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('客户编码')" prop="custCode">
          <mt-input
            v-model="searchFormModel.custCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="a1ed3932-b588-4903-933a-719ac1b4b386"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      keep-source
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #recoNoDetault="{ row }">
        <span style="cursor: pointer; color: #2783fe" @click="recoNoClick(row)">{{
          row.recoCode
        }}</span>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import Vue from 'vue'
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, statusOptions } from './config'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  components: { CollapseSearch, ScTable },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      statusOptions,
      companyOptions: []
    }
  },
  computed: {
    isSup() {
      return this.$route.name === 'sup-query-statement-tv'
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    toolbar() {
      let toolbarData = [
        { code: 'publish', name: this.$t('重新发布'), status: 'info', loading: false },
        { code: 'cancel', name: this.$t('作废'), status: 'info', loading: false },
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
        { code: 'print', name: this.$t('打印'), status: 'info', loading: false }
      ]
      if (this.isSup) {
        toolbarData = [
          { code: 'confirm', name: this.$t('确认'), status: 'info', loading: false },
          { code: 'return', name: this.$t('退回'), status: 'info', loading: false },
          { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
          { code: 'print', name: this.$t('打印'), status: 'info', loading: false }
        ]
      }
      return toolbarData
    }
  },
  created() {
    if (this.$store.state.saleReconSearch) {
      this.searchFormModel = this.$store.state.saleReconSearch
    }
    this.getCompanyOptions()
    this.getTableData()
  },
  beforeDestroy() {
    this.$store.commit('setSaleReconSearch', null)
  },
  methods: {
    getCompanyOptions() {
      this.$API.reconciliationReport.getCompanyList().then((res) => {
        if (res.code === 200) {
          this.companyOptions = res.data.map((item) => {
            return {
              ...item,
              text: item.orgCode + '-' + item.orgName,
              value: item.orgCode
            }
          })
        }
      })
    },
    recoTimeChange(e) {
      if (e.startDate) {
        this.searchFormModel['recoStartTime'] = dayjs(e.startDate).format('YYYYMM')
        this.searchFormModel['recoEndTime'] = dayjs(e.endDate).format('YYYYMM')
        // this.searchFormModel['recoStartTime'] = dayjs(
        //   dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        // ).valueOf()
        // this.searchFormModel['recoEndTime'] =
        //   dayjs(dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')).valueOf() + 999 // + 999是后端要求
      } else {
        this.searchFormModel['recoStartTime'] = null
        this.searchFormModel['recoEndTime'] = null
      }
    },
    dateChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = dayjs(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        ).valueOf()
        this.searchFormModel[field + 'End'] =
          dayjs(dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')).valueOf() + 999 // + 999是后端要求
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    recoNoClick(row) {
      if (this.$route.name === 'pur-query-statement-tv') {
        this.$router.push({
          name: 'pur-recon-detail-tv',
          query: {
            id: row.id,
            timeStamp: new Date().getTime(),
            type: 'saleRecon'
          }
        })
      } else if (this.$route.name === 'sup-query-statement-tv') {
        this.$router.push({
          name: 'sup-recon-detail-tv',
          query: {
            id: row.id,
            timeStamp: new Date().getTime(),
            type: 'saleRecon'
          }
        })
      }
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      this.$store.commit('setSaleReconSearch', this.searchFormModel)
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      let api = this.$API.reconciliationSettlementTv.pageSaleReconApi
      if (this.$route.name === 'sup-query-statement-tv') {
        api = this.$API.reconciliationSettlementTv.pageSaleReconSupplierApi
      }
      this.loading = true
      const res = await api(params).catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const ids = []
      selectedRecords.forEach((item) => {
        ids.push(item.id)
      })
      const commonToolbar = ['publish', 'cancel', 'delete', 'confirm', 'return', 'print']
      if (selectedRecords.length !== 1 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'publish':
          this.handlePublish(selectedRecords)
          break
        case 'cancel':
          this.handleCancel(selectedRecords)
          break
        case 'delete':
          this.handleDelete(selectedRecords)
          break
        case 'confirm':
          this.handleConfirm(selectedRecords)
          break
        case 'return':
          this.handleReturn(selectedRecords)
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        case 'print':
          this.handlePrint(selectedRecords)
          break
        default:
          break
      }
    },
    handlePublish(selectedRecords) {
      if ([3].includes(selectedRecords[0].status)) {
        this.$dialog({
          data: {
            title: this.$t('重新发布')
          },
          modal: () => import('../reconDetail/components/PublishDialog.vue'),
          success: (purRemark) => {
            let params = {
              headerId: selectedRecords[0].id,
              purRemark
            }
            this.apiStartLoading()
            this.$API.reconciliationSettlementTv
              .publichSaleReconApi(params)
              .then((res) => {
                if (res.code === 200) {
                  this.$toast({ content: this.$t('重新发布成功'), type: 'success' })
                  this.handleSearch()
                }
              })
              .finally(() => {
                this.apiEndLoading()
              })
          }
        })
      } else {
        this.$toast({ content: this.$t('请选择【已退回】的数据进行发布操作'), type: 'warning' })
      }
    },
    handleCancel(selectedRecords) {
      if ([1, 3].includes(selectedRecords[0].status)) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认作废?')
          },
          success: () => {
            let params = {
              headerId: selectedRecords[0].id
            }
            this.apiStartLoading()
            this.$API.reconciliationSettlementTv
              .refuseSaleReconApi(params)
              .then((res) => {
                if (res.code === 200) {
                  this.$toast({ content: this.$t('作废成功'), type: 'success' })
                  this.handleSearch()
                }
              })
              .finally(() => {
                this.apiEndLoading()
              })
          }
        })
      } else {
        this.$toast({
          content: this.$t('请选择【未确认】或【已退回】的数据进行作废操作'),
          type: 'warning'
        })
      }
    },
    handleDelete(selectedRecords) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除?')
        },
        success: () => {
          let params = {
            headerId: selectedRecords[0].id
          }
          this.apiStartLoading()
          this.$API.reconciliationSettlementTv
            .deleteSaleReconApi(params)
            .then((res) => {
              if (res.code === 200) {
                this.$toast({ content: this.$t('删除成功'), type: 'success' })
                this.handleSearch()
              }
            })
            .finally(() => {
              this.apiEndLoading()
            })
        }
      })
    },
    handleConfirm(selectedRecords) {
      if ([1].includes(selectedRecords[0].status)) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('是否确认?')
          },
          success: () => {
            let params = {
              headerId: selectedRecords[0].id,
              status: 2
            }
            this.apiStartLoading()
            this.$API.reconciliationSettlementTv
              .feedbackSaleReconSupplierApi(params)
              .then((res) => {
                if (res.code === 200) {
                  this.$toast({ content: this.$t('确认成功'), type: 'success' })
                  this.handleSearch()
                }
              })
              .finally(() => {
                this.apiEndLoading()
              })
          }
        })
      } else {
        this.$toast({ content: this.$t('请选择【未确认】的数据进行确认操作'), type: 'warning' })
      }
    },
    handleReturn(selectedRecords) {
      if ([1].includes(selectedRecords[0].status)) {
        this.$dialog({
          data: {
            title: this.$t('退回')
          },
          modal: () => import('../reconDetail/components/RejectDialog.vue'),
          success: (supplierRemark) => {
            // 退回
            let params = {
              headerId: selectedRecords[0].id,
              status: 3,
              supplierRemark
            }
            this.apiStartLoading()
            this.$API.reconciliationSettlementTv
              .feedbackSaleReconSupplierApi(params)
              .then((res) => {
                if (res.code === 200) {
                  this.$toast({ content: this.$t('退回成功'), type: 'success' })
                  this.handleSearch()
                }
              })
              .finally(() => {
                this.apiEndLoading()
              })
          }
        })
      } else {
        this.$toast({ content: this.$t('请选择【未确认】的数据进行退回操作'), type: 'warning' })
      }
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      let api = this.$API.reconciliationSettlementTv.exportSaleReconApi
      if (this.$route.name === 'sup-query-statement-tv') {
        api = this.$API.reconciliationSettlementTv.exportSaleReconSupplierApi
      }
      api(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    handlePrint(selectedRecords) {
      if ([2].includes(selectedRecords[0].status)) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认打印?')
          },
          success: () => {
            let params = {
              headerId: selectedRecords[0].id
            }
            this.$API.reconciliationSettlementTv.getFactoryListApi(params).then((res) => {
              const { code, data } = res
              if (code === 200) {
                for (let i = 0; i < data.length; i++) {
                  this.batchPrint(selectedRecords[0].id, data[i])
                }
              }
            })
          }
        })
      } else {
        this.$toast({ content: this.$t('请选择【已确认】的数据进行打印操作'), type: 'warning' })
      }
    },
    batchPrint(id, factoryCode) {
      let params = {
        headerId: id,
        factoryCode
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlementTv
        .printSaleReconSupplierApi(params)
        .then((res) => {
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = () => {
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }

            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
          window.open(this.pdfUrl)
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    apiStartLoading() {
      this.$store.commit('startLoading')
    },
    apiEndLoading() {
      this.$store.commit('endLoading')
    }
  }
}
</script>
