<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div :class="['status-box', 'status-box' + '-' + headerInfo.status]">
        {{ headerInfo.status | statusFormat }}
      </div>
      <div class="infos mr20">{{ $t('对账单号：') }}{{ headerInfo.recoCode }}</div>
      <div class="infos mr20">{{ $t('创建人：') }}{{ headerInfo.createUserName }}</div>
      <div class="infos">{{ $t('创建时间：') }}{{ headerInfo.createTime | dateFormat }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <mt-button
        v-if="!isSup && [1, 3].includes(headerInfo.status)"
        css-class="e-flat"
        :is-primary="true"
        @click="handleClick('cancel')"
        >{{ $t('作废') }}</mt-button
      >
      <mt-button
        v-if="!isSup && [3].includes(headerInfo.status)"
        css-class="e-flat"
        :is-primary="true"
        @click="handleClick('publish')"
        >{{ $t('重新发布') }}</mt-button
      >
      <mt-button
        v-if="isSup && [1].includes(headerInfo.status)"
        css-class="e-flat"
        :is-primary="true"
        @click="handleClick('confirm')"
        >{{ $t('确认') }}</mt-button
      >
      <mt-button
        v-if="isSup && [1].includes(headerInfo.status)"
        css-class="e-flat"
        :is-primary="true"
        @click="handleClick('return')"
        >{{ $t('退回') }}</mt-button
      >
      <mt-button
        v-if="[2].includes(headerInfo.status)"
        css-class="e-flat"
        :is-primary="true"
        @click="handleClick('print')"
        >{{ $t('打印') }}</mt-button
      >

      <div class="sort-box" @click="handleExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>
    <div class="main-bottom" v-show="isExpand">
      <mt-form ref="ruleForm" :model="headerInfo">
        <mt-form-item prop="company" :label="$t('公司')" class="disabled-label">
          <mt-input v-model="headerInfo.companyName" :disabled="true"></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplierCode" :label="$t('供应商代码')" class="disabled-label">
          <mt-input v-model="headerInfo.supplierCode" :disabled="true"></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplierName" :label="$t('供应商名称')" class="disabled-label">
          <mt-input v-model="headerInfo.supplierName" :disabled="true"></mt-input>
        </mt-form-item>

        <mt-form-item
          v-if="pageType === 'saleRecon'"
          prop="custCode"
          :label="$t('客户代码')"
          class="disabled-label"
        >
          <mt-input v-model="headerInfo.custCode" :disabled="true"></mt-input>
        </mt-form-item>

        <mt-form-item prop="supplierRemark" :label="$t('供方备注')" class="half-width">
          <mt-input
            v-model="headerInfo.supplierRemark"
            :disabled="true"
            :multiline="true"
            maxlength="150"
          />
        </mt-form-item>
        <mt-form-item prop="purRemark" :label="$t('采方备注')" class="half-width">
          <mt-input
            v-model="headerInfo.purRemark"
            :disabled="true"
            :multiline="true"
            maxlength="150"
          />
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
import dayjs from 'dayjs'
export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    },
    isSup: {
      type: Boolean,
      default: false
    },
    pageType: {
      type: String,
      default: 'saleRecon'
    }
  },
  data() {
    return {
      isExpand: true
    }
  },
  filters: {
    statusFormat(value) {
      let text = ''
      if (value === 1) {
        text = i18n.t('未确认')
      } else if (value === 2) {
        text = i18n.t('已确认')
      } else if (value === 3) {
        text = i18n.t('已退回')
      } else if (value === 4) {
        text = i18n.t('已关闭')
      }
      return text
    },
    dateFormat(value) {
      let date = value ? dayjs(Number(value)).format('YYYY-MM-DD') : ''
      return date
    }
  },
  methods: {
    handleExpand() {
      this.isExpand = !this.isExpand
      this.$emit('expand')
    },
    goBack() {
      this.$emit('goBack')
    },
    handleClick(flag) {
      this.$emit('btnClick', flag)
    }
  }
}
</script>

<style lang="scss" scoped>
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 0 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .mr20 {
      margin-right: 20px;
    }
    .status-box {
      padding: 2px 6px;
      border-radius: 2px;
      margin: 0 36px 0 10px;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      &-0 {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
      &-1 {
        color: #eda133;
        background: rgba(237, 161, 51, 0.1);
      }
      &-2 {
        color: #8acc40;
        background: rgba(138, 204, 64, 0.1);
      }
      &-3 {
        color: #ed5633;
        background: rgba(237, 86, 51, 0.1);
      }
      &-4 {
        color: #9a9a9a;
        background: rgba(154, 154, 154, 0.1);
      }
    }
    .middle-blank {
      flex: 1;
    }
    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    padding-bottom: 20px;
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin: 20px 20px 0 10px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
    /deep/ .half-width {
      width: calc(48% - 20px) !important;
    }
  }
}
</style>
