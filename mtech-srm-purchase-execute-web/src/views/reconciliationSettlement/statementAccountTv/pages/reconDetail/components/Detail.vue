<!-- 对账明细 -->
<template>
  <div class="vertical-flex-box">
    <collapse-search :is-grid-display="true" @reset="handleReset" @search="handleSearch">
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item v-if="!isSup" prop="factoryCode" :label="$t('工厂编码')">
          <RemoteAutocomplete
            v-model="searchFormModel.factoryCode"
            :url="$API.masterData.getSiteListUrl"
            :placeholder="$t('请选择工厂')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item v-if="isSup" prop="factoryCode" :label="$t('工厂编码')">
          <mt-input
            v-model="searchFormModel.factoryCode"
            :show-clear-button="true"
            :placeholder="$t('请输入工厂编码')"
          />
        </mt-form-item>
        <mt-form-item v-if="!isSup" prop="materialCode" :label="$t('物料编码')">
          <RemoteAutocomplete
            v-model="searchFormModel.materialCode"
            :url="$API.masterData.getItemUrl"
            :placeholder="$t('请选择物料编码')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :search-fields="['itemName', 'itemCode']"
          />
        </mt-form-item>
        <mt-form-item v-if="isSup" prop="materialCode" :label="$t('物料编码')">
          <RemoteAutocomplete
            v-model="searchFormModel.materialCode"
            :url="$API.masterData.getItemAuthUrl"
            :placeholder="$t('请选择物料编码')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :search-fields="['itemName', 'itemCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('对账日期')" prop="recoTime">
          <mt-date-range-picker
            v-model="searchFormModel.recoTime"
            @change="recoTimeChange"
            :placeholder="$t('请选择对账日期')"
            format="yyyy-MM"
            start="Year"
            depth="Year"
          />
        </mt-form-item>
        <mt-form-item prop="voucherNo" :label="$t('物料凭证编码')">
          <mt-input
            v-model="searchFormModel.voucherNo"
            :show-clear-button="true"
            :placeholder="$t('请输入物料凭证编码')"
          />
        </mt-form-item>
        <mt-form-item prop="purchaseVoucherNo" :label="$t('采购凭证号')">
          <mt-input
            v-model="searchFormModel.purchaseVoucherNo"
            :show-clear-button="true"
            :placeholder="$t('请输入采购凭证号')"
          />
        </mt-form-item>
        <mt-form-item prop="pcbVersionNo" :label="$t('PCB版号')">
          <mt-input
            v-model="searchFormModel.pcbVersionNo"
            :show-clear-button="true"
            :placeholder="$t('请输入PCB版号')"
          />
        </mt-form-item>
        <mt-form-item prop="orderNo" :label="$t('订单号')">
          <mt-input
            v-model="searchFormModel.orderNo"
            :show-clear-button="true"
            :placeholder="$t('请输入订单号')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <div class="flex-fit">
      <sc-table
        ref="supplierDetailSctableRef"
        :grid-id="gridId"
        :loading="loading"
        :is-show-refresh-bth="true"
        :columns="columns"
        :table-data="tableData"
        @refresh="handleSearch"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :loading="item.loading"
            size="small"
            @click="handleClickToolBar(item)"
          >
            {{ item.name }}
          </vxe-button>
        </template>
      </sc-table>
    </div>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  name: 'SupplierDetail',
  props: {
    isSup: {
      type: Boolean,
      default: false
    },
    pageType: {
      type: String,
      default: 'saleRecon'
    }
  },
  components: { CollapseSearch, ScTable },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      loading: false,
      toolbar: [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      tableData: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.supplierDetailSctableRef.$refs.xGrid
    },
    columns() {
      let column = [
        // {
        //   field: 'lineNo',
        //   title: this.$t('行号')
        // },
        {
          field: 'factoryCode',
          title: this.$t('工厂编码')
        },
        {
          field: 'factoryName',
          title: this.$t('工厂名称'),
          minWidth: 160
        },
        // {
        //   field: 'custCode',
        //   title: this.$t('客户')
        // },
        {
          field: 'materialCode',
          title: this.$t('BOM型号'),
          minWidth: 120
        },
        {
          field: 'materialName',
          title: this.$t('名称'),
          minWidth: 160
        },
        {
          field: 'purchaseOrderNo',
          title: this.$t('批次号')
        },
        {
          field: 'deliveryNo',
          title: this.$t('交货单号')
        },
        {
          field: 'saleOrder',
          title: this.$t('销售订单号')
        },
        {
          field: 'salesOrderItemNo',
          title: this.$t('销售订单行号'),
          minWidth: 120
        },
        {
          field: 'receiveQty',
          title: this.$t('批量')
        },
        {
          field: 'receiveQty2',
          title: this.$t('出货数量')
        },
        {
          field: 'receiveQty3',
          title: this.$t('结算数量')
        },
        {
          field: 'currencyCode',
          title: this.$t('币种'),
          minWidth: 80
        },
        {
          field: 'unitPriceUntaxed',
          title: this.$t('不含税单价')
        },
        {
          field: 'amtUntaxed',
          title: this.$t('未税金额')
        },
        {
          field: 'taxAmt',
          title: this.$t('税额'),
          minWidth: 80
        },
        {
          field: 'taxRate',
          title: this.$t('税率'),
          minWidth: 80,
          formatter: ({ cellValue }) => {
            return cellValue ? cellValue + '%' : ''
          }
        },
        {
          field: 'billingAmt',
          title: this.$t('合计开票金额'),
          minWidth: 120
        },
        {
          field: 'receiveTime',
          title: this.$t('出货日期')
        }
      ]
      if (this.pageType === 'outRecon') {
        column = [
          {
            field: 'factoryCode',
            title: this.$t('工厂编码')
          },
          {
            field: 'factoryName',
            title: this.$t('工厂名称'),
            minWidth: 160
          },
          {
            field: 'voucherNo',
            title: this.$t('物料凭证编码'),
            minWidth: 120
          },
          {
            field: 'materialVoucherProject',
            title: this.$t('物料凭证中的项目'),
            minWidth: 120
          },
          {
            field: 'materialVoucherYear',
            title: this.$t('物料凭证年度'),
            minWidth: 120
          },
          {
            field: 'batchNo',
            title: this.$t('批次号'),
            minWidth: 120
          },
          {
            field: 'purchaseVoucherNo',
            title: this.$t('采购凭证号'),
            minWidth: 120
          },
          {
            field: 'purchaseVoucherProjectNo',
            title: this.$t('采购凭证的项目编号'),
            minWidth: 120
          },
          {
            field: 'asbNo',
            title: this.$t('组件')
          },
          {
            field: 'componentName',
            title: this.$t('组件名称')
          },
          {
            field: 'pcbVersionNo',
            title: this.$t('PCB版号')
          },
          {
            field: 'instockDate',
            title: this.$t('入库日期')
          },
          {
            field: 'voucherDate',
            title: this.$t('订单凭证日期')
          },
          {
            field: 'orderNo',
            title: this.$t('订单号')
          },
          {
            field: 'materialCode',
            title: this.$t('物料编码')
          },
          {
            field: 'materialName',
            title: this.$t('物料名称')
          },
          {
            field: 'purchaseInfoRecordNo',
            title: this.$t('采购信息记录编号')
          },
          {
            field: 'deliveryNum',
            title: this.$t('交货数量')
          },
          {
            field: 'poQty',
            title: this.$t('采购订单数量')
          },
          {
            field: 'orderPrice',
            title: this.$t('PO单价')
          },
          {
            field: 'orderAmt',
            title: this.$t('PO金额')
          },
          {
            field: 'executionUnitPriceUntaxed',
            title: this.$t('应执行单价')
          },
          {
            field: 'executionTotalPriceUntaxed',
            title: this.$t('应执行金额')
          },
          {
            field: 'currencyCode',
            title: this.$t('币种')
          },
          {
            field: 'taxRate',
            title: this.$t('税率')
          }
        ]
      }
      return column
    },
    gridId() {
      let id = '4f653076-87d8-4960-b306-f5115116ab31'
      if (this.pageType === 'outRecon') {
        id = 'cd3f3bdc-73b5-4ddb-ac57-6dd67275b4cf'
      }
      return id
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    recoTimeChange(e) {
      if (e.startDate) {
        this.searchFormModel['startTime'] = dayjs(e.startDate).format('YYYYMM')
        this.searchFormModel['endTime'] = dayjs(e.endDate).format('YYYYMM')
      } else {
        this.searchFormModel['startTime'] = null
        this.searchFormModel['endTime'] = null
      }
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        headerId: this.$route?.query?.id,
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      let api = this.$API.reconciliationSettlementTv.pageSaleReconItemApi
      if (this.pageType === 'outRecon') {
        api = this.$API.reconciliationSettlementTv.pageOutReconItemApi
      }
      if (this.isSup) {
        api = this.$API.reconciliationSettlementTv.pageSaleReconItemSupplierApi
        if (this.pageType === 'outRecon') {
          api = this.$API.reconciliationSettlementTv.pageOutReconItemSupplierApi
        }
      }
      this.loading = true
      const res = await api(params).catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records.map((item) => {
          return {
            receiveQty2: item.receiveQty,
            receiveQty3: item.receiveQty,
            ...item
          }
        })
      }
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleExport(e) {
      const params = {
        headerId: this.$route?.query?.id,
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      let api = this.$API.reconciliationSettlementTv.exportSaleReconItemApi
      if (this.pageType === 'outRecon') {
        api = this.$API.reconciliationSettlementTv.exportOutReconItemApi
      }
      if (this.isSup) {
        api = this.$API.reconciliationSettlementTv.exportSupSaleReconItemApi
        if (this.pageType === 'outRecon') {
          api = this.$API.reconciliationSettlementTv.exportSupOutReconItemApi
        }
      }
      api(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>
