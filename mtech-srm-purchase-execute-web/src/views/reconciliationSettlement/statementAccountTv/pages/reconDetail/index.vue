<!-- 对账明细 -->
<template>
  <div class="full-height vertical-flex-box detail-fix-wrap">
    <div>
      <TopInfo
        class="flex-keep"
        ref="topInfoRef"
        :header-info="headerInfo"
        :is-sup="isSup"
        :page-type="pageType"
        @goBack="goBack"
        @btnClick="btnClick"
        @expand="handleExpand"
      />
    </div>
    <div>
      <mt-tabs
        class="flex-keep toggle-tab"
        :e-tab="false"
        :data-source="tabList"
        :selected-item="tabIndex"
        @handleSelectTab="handleSelectTab"
      />
      <div class="toggle-content">
        <div class="flex-fit" v-show="tabIndex === 0">
          <Detail ref="detailRef" :is-sup="isSup" :page-type="pageType" />
        </div>
        <div class="flex-fit" v-show="tabIndex === 1">
          <relative-file
            ref="relativeFileRef"
            :doc-id="relativeFileData.docId"
            :request-url-obj="requestUrlObj"
            :module-file-list="moduleFileList"
            :file-query-parms="fileQueryParms"
            :is-view="relativeFileData.isView"
          ></relative-file>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
export default {
  components: {
    TopInfo: () => import('./components/TopInfo.vue'),
    Detail: () => import('./components/Detail.vue'),
    RelativeFile: () => import('@/components/businessComponents/relativeFile/index.vue')
  },
  data() {
    return {
      headerInfo: {},
      tabList: [
        {
          title: this.$t('对账明细')
        },
        {
          title: this.$t('相关附件')
        }
      ],
      tabIndex: 0,
      requestUrlObj: {
        preUrl: 'reconciliationSettlementTv',
        saveUrl: 'saveFileSaleReconApi', // 将附件文件url保存到列表 保存文件信息
        fileUrl: `/contract/tenant/reconciliation/file/queryList` // 获取附件列表的url 根据docType和docID查询所有文件信息
      }
    }
  },
  computed: {
    isSup() {
      return this.$route.name === 'sup-recon-detail-tv'
    },
    relativeFileData() {
      return {
        docId: this.$route.query?.id,
        isView: !this.isSup // 查看
      }
    },
    pageType() {
      return this.$route.query?.type
    },
    fileQueryParms() {
      return {
        docType:
          this.pageType === 'outRecon' ? 'TV_EXTERNAL_RECONCILIATION' : 'TV_SALE_RECONCILIATION',
        nodeCode: 'supplier'
      }
    },
    moduleFileList() {
      return [
        {
          id: this.fileQueryParms.docType, // 选中时传给api的 doctype 的值
          nodeName: this.$t('供方-整单附件'), // 侧边栏名称
          nodeCode: 0, // 即当前 index 用于选择左侧边栏时 获取当前项 配置
          btnRequired: {
            hasUpload: true,
            hasDownload: true,
            hasDelete: true,
            hasPrint: false
          }, // 表格 toolbar 配置（可选） hasUpload: Boolean、hasDownload: Boolean、 hasDelete: Boolean
          hasItem: false, // 是否显示附件行数据（可选）
          deleteFileUrl: `/contract/tenant/reconciliation/file/delete`, // 删除文件使用的 API 根据ID删除
          deleteFileRequestMethod: 'delete' // 设置删除api的请求方法
        }
      ]
    }
  },
  mounted() {
    this.getHeader()
  },
  methods: {
    handleExpand() {
      if (this.tabIndex === 0) {
        let eleTab = document.querySelector('.toggle-tab')
        if (!eleTab) return
        let eleContent = eleTab.nextElementSibling
        if (!eleContent.classList?.contains('toggle-content')) return
        let ul = eleTab.querySelector('.tab-container')
        let li = ul.querySelector('li')
        li.click()
      }
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    async getHeader() {
      let params = {
        headerId: this.$route.query?.id
      }
      let api = this.$API.reconciliationSettlementTv.detailSaleReconApi
      if (this.pageType === 'outRecon') {
        api = this.$API.reconciliationSettlementTv.detailOutReconApi
      }
      const res = await api(params)
      if (res.code === 200) {
        this.headerInfo = res.data
      }
    },
    btnClick(flag) {
      switch (flag) {
        case 'cancel':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认作废?')
            },
            success: () => {
              this.handleCancel()
            }
          })
          break
        case 'publish':
          this.handlePublish()
          break
        case 'confirm':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('是否确认?')
            },
            success: () => {
              this.handleConfirm()
            }
          })
          break
        case 'return':
          this.handleReturn()
          break
        case 'print':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认打印?')
            },
            success: () => {
              this.handlePrint()
            }
          })
          break
        default:
          break
      }
    },
    async handleCancel() {
      let params = {
        headerId: this.$route.query?.id
      }
      this.apiStartLoading()
      let api = this.$API.reconciliationSettlementTv.refuseSaleReconApi
      if (this.pageType === 'outRecon') {
        api = this.$API.reconciliationSettlementTv.refuseOutReconApi
      }
      const res = await api(params).finally(() => this.apiEndLoading())
      if (res.code === 200) {
        this.$toast({ content: this.$t('作废成功'), type: 'success' })
        this.getHeader()
      }
    },
    handlePublish() {
      this.$dialog({
        data: {
          title: this.$t('重新发布')
        },
        modal: () => import('./components/PublishDialog.vue'),
        success: async (purRemark) => {
          let params = {
            headerId: this.$route.query?.id,
            purRemark
          }
          this.apiStartLoading()
          let api = this.$API.reconciliationSettlementTv.publichSaleReconApi
          if (this.pageType === 'outRecon') {
            api = this.$API.reconciliationSettlementTv.publichOutReconApi
          }
          const res = await api(params).finally(() => this.apiEndLoading())
          if (res.code === 200) {
            this.$toast({ content: this.$t('重新发布成功'), type: 'success' })
            this.getHeader()
          }
        }
      })
    },
    async handleConfirm() {
      let params = {
        headerId: this.$route.query?.id,
        status: 2
      }
      this.apiStartLoading()
      let api = this.$API.reconciliationSettlementTv.feedbackSaleReconSupplierApi
      if (this.pageType === 'outRecon') {
        api = this.$API.reconciliationSettlementTv.feedbackOutReconSupplierApi
      }
      const res = await api(params).finally(() => this.apiEndLoading())
      if (res.code === 200) {
        this.$toast({ content: this.$t('确认成功'), type: 'success' })
        this.getHeader()
      }
    },
    handleReturn() {
      this.$dialog({
        data: {
          title: this.$t('退回')
        },
        modal: () => import('./components/RejectDialog.vue'),
        success: async (supplierRemark) => {
          // 退回
          let params = {
            headerId: this.$route.query?.id,
            status: 3,
            supplierRemark
          }
          this.apiStartLoading()
          let api = this.$API.reconciliationSettlementTv.feedbackSaleReconSupplierApi
          if (this.pageType === 'outRecon') {
            api = this.$API.reconciliationSettlementTv.feedbackOutReconSupplierApi
          }
          const res = await api(params).finally(() => this.apiEndLoading())
          if (res.code === 200) {
            this.$toast({ content: this.$t('退回成功'), type: 'success' })
            this.getHeader()
          }
        }
      })
    },
    async handlePrint() {
      let params = {
        headerId: this.$route.query?.id
      }
      let api = this.$API.reconciliationSettlementTv.getFactoryListApi
      if (this.pageType === 'outRecon') {
        api = this.$API.reconciliationSettlementTv.getFactoryListOutReconApi
      }
      const res = await api(params)
      const { code, data } = res
      if (code === 200) {
        for (let i = 0; i < data.length; i++) {
          this.batchPrint(data[i])
        }
      }
    },
    async batchPrint(factoryCode) {
      let params = {
        headerId: this.$route.query?.id,
        factoryCode
      }
      this.apiStartLoading()
      let api = this.$API.reconciliationSettlementTv.printSaleReconSupplierApi
      if (this.pageType === 'outRecon') {
        api = this.$API.reconciliationSettlementTv.printOutReconApi
      }
      const res = await api(params).finally(() => this.apiEndLoading())
      if (res?.data?.type === 'application/json') {
        const reader = new FileReader()
        reader.readAsText(res?.data, 'utf-8')
        reader.onload = () => {
          const readerRes = reader.result
          const resObj = JSON.parse(readerRes)
          Vue.prototype.$toast({
            content: resObj.msg,
            type: 'error'
          })
        }

        return
      }
      const content = res.data
      this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
      window.open(this.pdfUrl)
    },
    handleBack() {
      this.$router.push({
        name: this.isSup ? 'sup-query-statement-tv' : 'pur-query-statement-tv',
        query: {
          currentIndex: this.pageType === 'saleRecon' ? 6 : 7,
          timeStamp: new Date().getTime()
        }
      })
    },
    goBack() {
      // this.$router.go(-1)
      this.$router.push({
        name: this.isSup ? 'sup-query-statement-tv' : 'pur-query-statement-tv',
        query: {
          currentIndex: this.pageType === 'saleRecon' ? 6 : 7,
          timeStamp: new Date().getTime()
        }
      })
    },
    apiStartLoading() {
      this.$store.commit('startLoading')
    },
    apiEndLoading() {
      this.$store.commit('endLoading')
    }
  }
}
</script>
