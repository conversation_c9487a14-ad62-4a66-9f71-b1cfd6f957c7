<template>
  <!-- 采方-对账协同-采购对账单列表 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="toolbarClick"
      @handleCustomReset="handleCustomReset"
      class="template-height"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="companyCodes" :label="$t('公司')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.companyCodes"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                multiple
                :placeholder="$t('请选择公司')"
                :fields="{ text: 'orgName', value: 'orgCode' }"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-select
                style="flex: 1"
                v-model="searchFormModel.status"
                :data-source="statusOptions"
                :show-clear-button="true"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="materialVoucherYear" :label="$t('对账日期')" label-style="top">
              <mt-date-range-picker
                style="flex: 1"
                v-model="searchFormModel.materialVoucherYear"
                @change="(e) => handleDateTimeChange(e, 'materialVoucherYear')"
                :placeholder="$t('请选择对账日期')"
              />
            </mt-form-item>
            <mt-form-item prop="factoryCodes" :label="$t('工厂')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.factoryCodes"
                :url="$API.masterData.getSiteListUrl"
                multiple
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                :search-fields="['siteName', 'siteCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="reconciliationCode" :label="$t('对账单号')" label-style="top">
              <mt-input
                v-model="reconciliationCode"
                :show-clear-button="true"
                :placeholder="$t('请输入对账单号')"
                @change="
                  reconciliationCode
                    ? (searchFormModel['recoCodes'] = [reconciliationCode])
                    : (searchFormModel['recoCodes'] = null)
                "
              />
            </mt-form-item>
            <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.itemCode"
                :url="$API.masterData.getItemUrl"
                :placeholder="$t('请选择物料')"
                :fields="{ text: 'itemName', value: 'itemCode' }"
                :search-fields="['itemName', 'itemCode']"
              ></RemoteAutocomplete>
              <!-- <mt-input v-model="searchFormModel.itemCode" /> -->
            </mt-form-item>
            <!-- <mt-form-item prop="orderCode" :label="$t('采购订单')" label-style="top">
              <mt-input
                v-model="searchFormModel.orderCode"
                :show-clear-button="true"
                :placeholder="$t('请输入采购订单')"
              />
            </mt-form-item> -->
            <mt-form-item prop="supplierCodes" :label="$t('供应商编码')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.supplierCodes"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择供应商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="recoTypeCodes" :label="$t('对账类型')" label-style="top">
              <mt-multi-select
                style="flex: 1"
                :show-clear-button="false"
                v-model="searchFormModel.recoTypeCodes"
                :data-source="recoTypeOptions"
                :placeholder="$t('请选择')"
                @change="recoTypeCodesChange"
              ></mt-multi-select>
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { columnData, statusOptions, recoTypeOptions } from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    const startDate = dayjs().subtract(2, 'month').startOf('month')
    return {
      reconciliationCode: '',
      searchFormModel: {
        materialVoucherYear: [new Date(startDate), new Date()],
        materialVoucherYearEnd: dayjs(new Date()).format('YYYYMM'),
        materialVoucherYearStart: dayjs(new Date(startDate)).format('YYYYMM'),
        recoTypeCodes: ['B', 'D']
      },
      statusOptions,
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          toolbar: {
            tools: [
              [
                {
                  id: 'excelExport',
                  icon: 'icon_solid_export',
                  title: this.$t('导出')
                }
              ],
              []
            ]
          },
          grid: {
            // height: 566,
            virtualPageSize: 30,
            enableVirtualization: true,
            columnData: columnData,
            dataSource: [],
            pageSettings: {
              currentPage: 1,
              pageSize: 50,
              pageSizes: [50, 100, 200, 1000],
              totalRecordsCount: 0
            },
            selectionSettings: {
              persistSelection: true, // 这个选项很重要，影响到虚拟滚动下能不能操作全选按钮
              type: 'Multiple',
              checkboxOnly: true
            },
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliation/consignmentItem/query`,
              ignoreDefaultSearch: true,
              recordsPosition: 'data.records',
              params: {},
              ignoreSearchToast: true
            },
            lineSelection: 0,
            lineIndex: 1
            // frozenColumns: 1
          }
        }
      ],
      recoTypeOptions
    }
  },
  methods: {
    recoTypeCodesChange(e) {
      const { value } = e
      if (!value || !value.length) {
        this.searchFormModel['recoTypeCodes'] = ['B', 'D']
      }
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = dayjs(e.startDate).format('YYYYMM')
        this.searchFormModel[field + 'End'] = dayjs(e.endDate).format('YYYYMM')
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    toolbarClick(e) {
      if (e.toolbar.id == 'excelExport') {
        // 导出
        this.handleExport()
      }
    },
    // 导出
    handleExport() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        page: { current: 1, size: 10000 }
        // rules: rule.rules || []
      }
      if (rule.rules && rule.rules.length) {
        rule.rules.forEach((item) => {
          params[item.field] = item.value
        })
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlementTv
        .exportConsignmentItem(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          this.reconciliationCode = ''
          if (key === 'recoTypeCodes') {
            this.searchFormModel[key] = ['B', 'D']
          }
        }
      }
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style></style>
