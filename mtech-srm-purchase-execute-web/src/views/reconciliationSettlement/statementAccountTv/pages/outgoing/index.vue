<template>
  <!-- 采方-对账协同-采购对账单列表 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="toolbarClick"
      @handleCustomReset="handleCustomReset"
      @handleClickCellTitle="handleClickCellTitle"
      class="template-height"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item v-if="isSup" prop="companyCodes" :label="$t('公司')" label-style="top">
              <mt-multi-select
                style="flex: 1"
                ref="companyRef1"
                :popup-width="350"
                v-model="searchFormModel.companyCodes"
                :data-source="companyOptions"
                :show-clear-button="true"
                :fields="{ text: 'labelShow', value: 'orgCode' }"
                :allow-filtering="true"
                :filtering="getCompanyOptions"
                :placeholder="$t('请选择公司')"
              ></mt-multi-select>
            </mt-form-item>
            <mt-form-item v-else prop="companyCodes" :label="$t('公司')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.companyCodes"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                multiple
                :placeholder="$t('请选择公司')"
                :fields="{ text: 'orgName', value: 'orgCode' }"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-select
                style="flex: 1"
                v-model="searchFormModel.status"
                :data-source="statusOptions"
                :show-clear-button="true"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="materialVoucherYear" :label="$t('对账日期')" label-style="top">
              <mt-date-range-picker
                style="flex: 1"
                v-model="searchFormModel.materialVoucherYear"
                @change="(e) => handleDateTimeChange(e, 'materialVoucherYear')"
                :placeholder="$t('请选择对账日期')"
              />
            </mt-form-item>
            <mt-form-item prop="factoryCodes" :label="$t('工厂')" label-style="top">
              <RemoteAutocomplete
                v-if="!isSup"
                style="flex: 1"
                v-model="searchFormModel.factoryCodes"
                :url="$API.masterData.getSiteListUrl"
                multiple
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                :search-fields="['siteName', 'siteCode']"
              ></RemoteAutocomplete>
              <RemoteAutocomplete
                v-else
                style="flex: 1"
                v-model="searchFormModel.factoryCodes"
                :url="$API.masterData.getSiteAuthFuzzyUrl"
                multiple
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                params-key="fuzzyParam"
                records-position="data"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="reconciliationCode" :label="$t('对账单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.reconciliationCode"
                :show-clear-button="true"
                :placeholder="$t('请输入对账单号')"
              />
            </mt-form-item>
            <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.itemCode"
                :url="$API.masterData.getItemUrl"
                :placeholder="$t('请选择物料')"
                :fields="{ text: 'itemName', value: 'itemCode' }"
                :search-fields="['itemName', 'itemCode']"
              ></RemoteAutocomplete>
              <!-- <mt-input v-model="searchFormModel.itemCode" /> -->
            </mt-form-item>
            <mt-form-item prop="orderCode" :label="$t('采购订单')" label-style="top">
              <mt-input
                v-model="searchFormModel.orderCode"
                :show-clear-button="true"
                :placeholder="$t('请输入采购订单')"
              />
            </mt-form-item>
            <mt-form-item
              v-if="!isSup"
              prop="supplierCodes"
              :label="$t('供应商编码')"
              label-style="top"
            >
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.supplierCodes"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择供应商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="recoTypeCodes" :label="$t('对账类型')" label-style="top">
              <mt-multi-select
                style="flex: 1"
                :show-clear-button="false"
                v-model="searchFormModel.recoTypeCodes"
                :data-source="recoTypeOptions"
                :placeholder="$t('请选择')"
                @change="recoTypeCodesChange"
              ></mt-multi-select>
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import Vue from 'vue'
import dayjs from 'dayjs'
import { columnData, statusOptions, recoTypeOptions } from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'
import { utils } from '@mtech-common/utils'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    const startDate = dayjs().subtract(3, 'month').startOf('month')
    return {
      isSup: false,
      companyOptions: [],
      searchFormModel: {
        materialVoucherYear: [new Date(startDate), new Date()],
        materialVoucherYearEnd: dayjs(new Date()).format('YYYYMM'),
        materialVoucherYearStart: dayjs(new Date(startDate)).format('YYYYMM'),
        recoTypeCodes: ['E', 'F']
      },
      statusOptions,
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          toolbar: {
            tools: [
              [
                // {
                //   id: 'Confirm', // 供方的
                //   icon: 'icon_solid_Pauseorder',
                //   title: this.$t('确认')
                // },
                // {
                //   id: 'SendBack', // 供方的
                //   icon: 'icon_list_recall',
                //   title: this.$t('退回')
                // },
                // {
                //   id: 'Publish', // 采方的
                //   icon: 'icon_solid_Activateorder',
                //   title: this.$t('重新发布')
                // },
                // {
                //   id: 'Cancel', // 采方的
                //   icon: 'icon_solid_Cancel',
                //   title: this.$t('作废')
                // },
                // {
                //   id: 'Delete', // 采方的
                //   icon: 'icon_solid_Delete',
                //   title: this.$t('删除')
                // },
                // {
                //   id: 'excelExport', // 共用的
                //   icon: 'icon_solid_export',
                //   title: this.$t('导出')
                // },
                // {
                //   id: 'printRecon', // 共用的
                //   icon: 'icon_table_print',
                //   title: this.$t('打印')
                // }
              ],
              []
            ]
          },
          grid: {
            // height: 566,
            virtualPageSize: 30,
            enableVirtualization: true,
            columnData: columnData,
            dataSource: [],
            pageSettings: {
              currentPage: 1,
              pageSize: 50,
              pageSizes: [50, 100, 200, 1000],
              totalRecordsCount: 0
            },
            selectionSettings: {
              persistSelection: true, // 这个选项很重要，影响到虚拟滚动下能不能操作全选按钮
              type: 'Multiple',
              checkboxOnly: true
            },
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliation/header/query/outgoing`,
              ignoreDefaultSearch: true,
              recordsPosition: 'data.records',
              params: {},
              ignoreSearchToast: true
            },
            lineSelection: 0
            // lineIndex: 1
            // frozenColumns: 1
          }
        }
      ],
      recoTypeOptions
    }
  },
  created() {},
  mounted() {
    this.getCompanyOptions = utils.debounce(this.getCompanyOptions, 300)
    this.setToolbar()
  },
  methods: {
    //查询业务公司下拉数据
    getCompanyOptions(e = { text: '' }) {
      this.$API.masterData
        .getCompanyBySup({
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true,
          organizationIds: [],
          fuzzyParam: e.text
        })
        .then((res) => {
          const companyOptions = res.data
          companyOptions.forEach((item) => {
            item.labelShow = item.orgCode + ' - ' + item.orgName
          })
          this.companyOptions = companyOptions

          this.$nextTick(() => {
            if (e.updateData && typeof e.updateData == 'function') {
              e.updateData(companyOptions)
            }
          })
        })
    },
    setToolbar() {
      const toolbar = []
      if (this.$route.name === 'pur-query-statement-tv') {
        toolbar.push(
          {
            id: 'Publish', // 采方的
            icon: 'icon_solid_Activateorder',
            title: this.$t('重新发布')
          },
          {
            id: 'Cancel', // 采方的
            icon: 'icon_solid_Cancel',
            title: this.$t('作废')
          },
          {
            id: 'Delete', // 采方的
            icon: 'icon_solid_Delete',
            title: this.$t('删除')
          }
        )
      } else {
        this.isSup = true
        this.getCompanyOptions({ text: '' })
        toolbar.push(
          {
            id: 'Confirm', // 供方的
            icon: 'icon_solid_Pauseorder',
            title: this.$t('确认')
          },
          {
            id: 'SendBack', // 供方的
            icon: 'icon_list_recall',
            title: this.$t('退回')
          }
        )
      }
      toolbar.push(
        {
          id: 'excelExport', // 共用的
          icon: 'icon_solid_export',
          title: this.$t('导出')
        },
        {
          id: 'printRecon', // 共用的
          icon: 'icon_table_print',
          title: this.$t('打印')
        }
      )
      this.pageConfig[0].toolbar = { tools: [toolbar, ['refresh', 'setting']] }
    },
    recoTypeCodesChange(e) {
      const { value } = e
      if (!value || !value.length) {
        this.searchFormModel['recoTypeCodes'] = ['E', 'F']
      }
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = dayjs(e.startDate).format('YYYYMM')
        this.searchFormModel[field + 'End'] = dayjs(e.endDate).format('YYYYMM')
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    //单元格title文字点击
    handleClickCellTitle(e) {
      // 将信息放到 localStorage
      localStorage.setItem('statementTvData', JSON.stringify(e.data))
      if (e.field == 'recoCode') {
        if (this.isSup) {
          this.$router.push(
            `/purchase-execute/sup-query-statement-detail-tv?id=${e.data.id}&type=2`
          )
        } else {
          this.$router.push(
            `/purchase-execute/pur-query-statement-detail-tv?id=${e.data.id}&type=2`
          )
        }
      }
    },
    toolbarClick(e) {
      const getMtechGridRecords = e.gridRef.getMtechGridRecords()
      if (getMtechGridRecords.length <= 0 && e.toolbar.id != 'excelExport') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (getMtechGridRecords.length != 1 && e.toolbar.id == 'printRecon') {
        this.$toast({ content: this.$t('仅支持单行打印'), type: 'warning' })
        return
      }
      const _id = []
      // getMtechGridRecords.map((item) => _id.push(item.id))
      // push数据id的同时做校验
      for (let i = 0; i < getMtechGridRecords.length; i++) {
        const item = getMtechGridRecords[i]
        if (e.toolbar.id == 'Confirm' && item.status != 1) {
          // 确认 未确认才能确认
          this.$toast({
            content: this.$t('未确认状态的数据才能确认'),
            type: 'warning'
          })
          return
        } else if (e.toolbar.id == 'SendBack' && item.status != 1) {
          // 退回 未确认才能退回
          this.$toast({
            content: this.$t('未确认状态的数据才能退回'),
            type: 'warning'
          })
          return
        } else if (e.toolbar.id == 'Publish' && item.status != 3) {
          // 重新发布 已退回才能重新发布
          this.$toast({
            content: this.$t('已退回状态的数据才能重新发布'),
            type: 'warning'
          })
          return
        } else if (e.toolbar.id == 'Cancel' && (item.status == 2 || item.status == 4)) {
          // 作废 已确认、已作废不能作废
          this.$toast({
            content: this.$t('已确认、已作废状态的数据不能作废'),
            type: 'warning'
          })
          return
        }
        _id.push(item.id)
      }

      if (e.toolbar.id == 'Confirm') {
        // 确认 未确认才能确认
        this.handleConfirm(_id)
      } else if (e.toolbar.id == 'SendBack') {
        // 退回 未确认才能退回
        this.handleSendBack(_id)
      } else if (e.toolbar.id == 'Delete') {
        // 删除
        this.handleDelete(_id)
      } else if (e.toolbar.id == 'Publish') {
        // 重新发布 已退回才能重新发布
        this.handlePublish(_id)
      } else if (e.toolbar.id == 'Cancel') {
        // 作废 已确认、已作废不能作废
        this.handleCancel(_id)
      } else if (e.toolbar.id == 'excelExport') {
        // 导出
        this.handleExport()
      } else if (e.toolbar.id == 'printRecon') {
        // 打印
        this.handlePrint(e.gridRef.getMtechGridRecords()[0])
      }
    },
    // 确认对账单
    handleConfirm(ids) {
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t('是否确认选中行?')
        },
        success: () => {
          this.$API.reconciliationSettlementTv
            .reconUpdateStatus(
              {
                idList: ids
              },
              2
            )
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
        }
      })
    },
    // 退回对账单
    handleSendBack(ids) {
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t('是否退回选中行?')
        },
        success: () => {
          this.$API.reconciliationSettlementTv
            .reconUpdateStatus(
              {
                idList: ids
              },
              3
            )
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
        }
      })
    },
    // 删除对账单
    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t('是否删除选中行?')
        },
        success: () => {
          this.$API.reconciliationSettlementTv
            .reconSoftDelete({
              idList: ids
            })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
        }
      })
    },
    // 重新发布对账单
    handlePublish(ids) {
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t('是否重新发布选中行?')
        },
        success: () => {
          this.$API.reconciliationSettlementTv
            .reconRePublish({
              idList: ids
            })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
        }
      })
    },
    // 作废对账单
    handleCancel(ids) {
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t('是否作废选中行?')
        },
        success: () => {
          this.$API.reconciliationSettlementTv
            .reconDiscard({
              idList: ids
            })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: this.$t('操作成功'),
                  type: 'success'
                })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
        }
      })
    },
    // 导出
    handleExport() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        page: { current: 1, size: 10000 }
        // rules: rule.rules || []
      }
      if (rule.rules && rule.rules.length) {
        rule.rules.forEach((item) => {
          params[item.field] = item.value
        })
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlementTv
        .reconOutgoingExport(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    // 打印
    handlePrint(row) {
      // this.apiStartLoading()
      // this.$API.reconciliationSettlementTv
      //   .queryFactoryCodes({ headerId: row.id })
      //   .then((res) => {
      //     this.apiEndLoading()
      //     const { code, data } = res
      //     if (code === 200) {
      //       for (let i = 0; i < data.length; i++) {
      //         this.batchPrint(row.id, data[i], row.recoTypeCode)
      //       }
      //     }
      //   })
      //   .catch(() => {
      //     this.apiEndLoading()
      //   })
      this.batchPrint({ headerId: row.id })
    },
    // getPrintApi(recoTypeCode) {
    //   if (recoTypeCode === 'F') {
    //     return this.$API.reconciliationSettlementTv.printStandardRetrospectRecon
    //   }
    //   return this.$API.reconciliationSettlementTv.printStandardRecon
    // },
    // batchPrint(headerId, factoryCode, recoTypeCode) {
    //   this.apiStartLoading()
    //   this.getPrintApi(recoTypeCode)({ headerId, factoryCode })
    //     .then((res) => {
    //       this.apiEndLoading()
    //       if (res?.data?.type === 'application/json') {
    //         const reader = new FileReader()
    //         reader.readAsText(res?.data, 'utf-8')
    //         reader.onload = () => {
    //           console.log('======', reader)
    //           const readerRes = reader.result
    //           const resObj = JSON.parse(readerRes)
    //           Vue.prototype.$toast({
    //             content: resObj.msg,
    //             type: 'error'
    //           })
    //         }

    //         return
    //       }
    //       const content = res.data
    //       this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
    //       window.open(this.pdfUrl)
    //       // let date = new Date().getTime()
    //       // let ifr = document.createElement('iframe')
    //       // ifr.style.frameborder = 'no'
    //       // ifr.style.display = 'none'
    //       // ifr.style.pageBreakBefore = 'always'
    //       // ifr.setAttribute('id', 'printPdf' + date)
    //       // ifr.setAttribute('name', 'printPdf' + date)
    //       // ifr.src = this.pdfUrl
    //       // document.body.appendChild(ifr)
    //       // this.doPrint('printPdf' + date)
    //       // window.URL.revokeObjectURL(ifr.src)
    //     })
    //     .catch(() => {
    //       this.apiEndLoading()
    //     })
    //     .finally(() => {
    //       this.apiEndLoading()
    //     })
    // },
    batchPrint(params) {
      this.apiStartLoading()
      this.$API.reconciliationSettlementTv
        .printOutGoingtRecon(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = () => {
              console.log('======', reader)
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }

            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
          window.open(this.pdfUrl)
          // let date = new Date().getTime()
          // let ifr = document.createElement('iframe')
          // ifr.style.frameborder = 'no'
          // ifr.style.display = 'none'
          // ifr.style.pageBreakBefore = 'always'
          // ifr.setAttribute('id', 'printPdf' + date)
          // ifr.setAttribute('name', 'printPdf' + date)
          // ifr.src = this.pdfUrl
          // document.body.appendChild(ifr)
          // this.doPrint('printPdf' + date)
          // window.URL.revokeObjectURL(ifr.src)
        })
        .catch(() => {
          this.apiEndLoading()
        })
        .finally(() => {
          this.apiEndLoading()
        })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'recoTypeCodes') {
            this.searchFormModel[key] = ['E', 'F']
          }
        }
      }
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style></style>
