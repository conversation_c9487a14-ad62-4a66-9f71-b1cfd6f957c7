import { i18n } from '@/main.js'
// import { judgeFormatCodeName } from '@/utils/utils'

export const statusOptions = [
  { value: 1, text: i18n.t('未确认'), cssClass: 'col-abnormal' },
  { value: 2, text: i18n.t('已确认'), cssClass: 'col-active' },
  { value: 3, text: i18n.t('已退回'), cssClass: 'col-normal' },
  { value: 4, text: i18n.t('已作废'), cssClass: 'col-published' }
]

export const recoTypeOptions = [
  { value: 'B', text: i18n.t('寄售对账单'), cssClass: '' },
  { value: 'D', text: i18n.t('寄售追溯对账单'), cssClass: '' }
]

export const columnData = [
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    isPrimaryKey: true
  },
  {
    width: '70',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      //0-已确认，1. 已作废，2. 已退回，3. 未确认，-1. 已关闭
      map: statusOptions
    }
    // cellTools: [
    //   {
    //     id: 'close',
    //     icon: 'icon_list_close',
    //     title: i18n.t('关闭'),
    //     permission: ['O_02_0398'],
    //     visibleCondition: (data) => {
    //       return (
    //         data.sourcePath == 0 && data.status >= 0 && data.status !== 1 && data.invoiceStatus == 0
    //       )
    //     }
    //   },
    //   {
    //     id: 'publish',
    //     icon: 'icon_list_issue',
    //     title: i18n.t('发布'),
    //     permission: ['O_02_0396'],
    //     visibleCondition: (data) => {
    //       return data.sourcePath == 0 && data.status == 0
    //     }
    //   },
    //   {
    //     id: 'exceptionHandle',
    //     icon: 'icon_list_issue',
    //     title: i18n.t('异常处理'),
    //     permission: ['O_02_0396'], // 异常处理
    //     visibleCondition: (data) => {
    //       return data.sourcePath == 0 && data.status != 3
    //     }
    //   }
    //   // {
    //   //   id: "cancelPublish",
    //   //   icon: "icon_table_cancel",
    //   //   title: i18n.t("取消发布"),
    //   //   permission: ["O_02_0400"],
    //   //   visibleCondition: (data) => {
    //   //     return data.sourcePath == 0 && data.status == 1;
    //   //   },
    //   // },
    // ]
  },
  // {
  //   width: '120',
  //   field: 'companyCode',
  //   headerText: i18n.t('公司编码'),
  //   searchOptions: MasterDataSelect.businessCompany
  //   // valueAccessor: (field, data) => {
  //   //   return judgeFormatCodeName(data?.companyCode, data?.companyName)
  //   // }
  // },
  {
    // width: "150",
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    width: '150',
    field: 'recoCode',
    headerText: i18n.t('对账单号'),
    cellTools: []
  },
  {
    // width: '230',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '110',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    // }
  },
  {
    width: '110',
    field: 'recoTypeCode',
    headerText: i18n.t('对账类型'),
    valueConverter: {
      type: 'map',
      map: recoTypeOptions
    }
  },
  {
    field: 'materialVoucherYear',
    width: '110',
    headerText: i18n.t('对账日期')
  },
  {
    // width: "150",
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '100',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    valueAccessor: (field, data) => {
      return data.createTime ? data.createTime.split(' ')[0] : ''
    }
  }
]
