<template>
  <!-- 采方-对账协同-采购对账单详情 -->
  <div class="full-height vertical-flex-box detail-fix-wrap">
    <top-info
      ref="topInfoRef"
      class="flex-keep"
      :entry-type="entryType"
      :header-info="headerInfo"
      :entry-id="entryId"
    ></top-info>

    <div class="bottom-tables">
      <mt-tabs
        :e-tab="false"
        tab-id="state-tab"
        :data-source="tabList"
        @handleSelectTab="handleSelectTab"
      ></mt-tabs>
      <div class="flex-fit bottom-box">
        <!-- 对账明细 -->
        <div class="grid-wrap grid-wrap-page" v-show="currentTabInfo.code == 'reconciliationField'">
          <mt-template-page
            ref="stateDetailRef"
            :template-config="pageConfig1"
            @handleClickToolBar="handleClickToolBar"
            @handleCustomReset="handleCustomReset"
          >
            <template v-slot:quick-search-form>
              <div class="custom-form-box">
                <mt-form ref="searchFormRef" :model="searchFormModel">
                  <mt-form-item prop="factoryCodes" :label="$t('工厂')" label-style="left">
                    <RemoteAutocomplete
                      v-if="!isSup"
                      style="flex: 1"
                      v-model="searchFormModel.factoryCodes"
                      :url="$API.masterData.getSiteListUrl"
                      multiple
                      :placeholder="$t('请选择工厂')"
                      :fields="{ text: 'siteName', value: 'siteCode' }"
                      :search-fields="['siteName', 'siteCode']"
                    ></RemoteAutocomplete>
                    <RemoteAutocomplete
                      v-else
                      style="flex: 1"
                      v-model="searchFormModel.factoryCodes"
                      :url="$API.masterData.getSiteAuthFuzzyUrl"
                      multiple
                      :placeholder="$t('请选择工厂')"
                      :fields="{ text: 'siteName', value: 'siteCode' }"
                      params-key="fuzzyParam"
                      records-position="data"
                    ></RemoteAutocomplete>
                  </mt-form-item>
                  <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="left">
                    <RemoteAutocomplete
                      style="flex: 1"
                      v-model="searchFormModel.itemCode"
                      :url="$API.masterData.getItemUrl"
                      :placeholder="$t('请选择物料')"
                      :fields="{ text: 'itemName', value: 'itemCode' }"
                      :search-fields="['itemName', 'itemCode']"
                    ></RemoteAutocomplete>
                    <!-- <mt-input v-model="searchFormModel.itemCode" /> -->
                  </mt-form-item>
                  <!-- <mt-form-item prop="sourceOrderCode" :label="$t('采购订单号')" label-style="left">
                    <mt-input
                      v-model="searchFormModel.sourceOrderCode"
                      :show-clear-button="true"
                      :placeholder="$t('请输入采购订单号')"
                    />
                  </mt-form-item> -->
                  <mt-form-item
                    v-if="reconciliationType == 1"
                    prop="receiveCode"
                    :label="$t('出库单号')"
                    label-style="left"
                  >
                    <mt-input
                      v-model="searchFormModel.receiveCode"
                      :show-clear-button="true"
                      :placeholder="$t('请输入出库单号')"
                    />
                  </mt-form-item>
                  <mt-form-item
                    v-if="!isSup"
                    prop="supplierCodes"
                    :label="$t('供应商编码')"
                    label-style="left"
                  >
                    <RemoteAutocomplete
                      style="flex: 1"
                      v-model="searchFormModel.supplierCodes"
                      url="/masterDataManagement/tenant/supplier/paged-query"
                      multiple
                      :placeholder="$t('请选择供应商')"
                      :fields="{ text: 'supplierName', value: 'supplierCode' }"
                      :search-fields="['supplierName', 'supplierCode']"
                    ></RemoteAutocomplete>
                  </mt-form-item>
                </mt-form>
              </div>
            </template>
          </mt-template-page>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { standardColumnData, outgoingColumnData, consignmentColumnData } from './config'
import topInfo from './components/topInfo.vue'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  components: {
    topInfo
  },
  data() {
    let statementTvData = JSON.parse(localStorage.getItem('statementTvData'))
    return {
      searchFormModel: {},
      entryId: null,
      reconciliationType: 0,
      entryType: null, //1是编辑 3是详情 2是历史反馈 后续可能有编辑，变量先留着
      headerInfo: statementTvData, // 头部数据
      tabList: [{ title: this.$t('对账明细'), code: 'reconciliationField' }],
      pageConfig1: [],
      currentTabInfo: {
        code: 'reconciliationField'
      },
      isSup: false
    }
  },
  mounted() {
    if (this.$route.name === 'sup-query-statement-detail-tv') {
      this.isSup = true
    }
    this.entryId = this.$route.query.id
    this.reconciliationType = this.$route.query.type
    this.formatDetail()
  },
  methods: {
    // 整合对账明细
    formatDetail() {
      // 默认标准对账详情
      let columnData = standardColumnData
      let url = `${BASE_TENANT}/reconciliation/standardItem/query`
      if (this.reconciliationType == 2) {
        // 注塑外发对账详情
        columnData = outgoingColumnData(this.headerInfo.recoTypeCode)
        url = `${BASE_TENANT}/reconciliation/outgoingItem/query`
      } else if (this.reconciliationType == 1) {
        // 寄售对账详情
        columnData = consignmentColumnData
        url = `${BASE_TENANT}/reconciliation/consignmentItem/query`
      }
      const toolbar = {
        tools: [
          [
            {
              id: 'ExportRecon',
              icon: 'icon_table_print',
              title: this.$t('导出')
            }
          ],
          ['Refresh']
        ]
      }
      this.pageConfig1 = [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          toolbar,
          grid: {
            height: 'auto',
            columnData,
            // dataSource: detailTableData,
            dataSource: [],
            asyncConfig: {
              url,
              // defaultRules: [
              //   {
              //     field: "headerId",
              //     operator: "equal",
              //     value: this.$route.query.id,
              //   },
              // ],
              params: {
                headerId: this.$route.query.id
              }
            },
            allowPaging: true // 分页
            // editSettings: {
            //   allowEditing:
            //     this.$route.query.type == 1 && this.headerInfo?.status == 1,
            // },
          }
        }
      ]
    },
    // ToolBar
    handleClickToolBar(args) {
      const { toolbar } = args
      if (toolbar.id === 'ExportRecon') {
        // 导出
        this.postReconDownload()
      }
    },
    // 采购对账-对账单明细下载
    postReconDownload() {
      const params = {
        headerId: this.$route.query.id
      }
      this.$store.commit('startLoading')
      this.getExportApi()(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    getExportApi() {
      if (this.headerInfo.recoTypeCode === 'B' || this.headerInfo.recoTypeCode === 'D') {
        // 寄售明细导出
        return this.$API.reconciliationSettlementTv.exportConsignmentItem
      } else if (this.headerInfo.recoTypeCode === 'E' || this.headerInfo.recoTypeCode === 'F') {
        // 注塑申购明细导出
        return this.$API.reconciliationSettlementTv.exportOutgoingItem
      } else {
        // 标准明细导出
        return this.$API.reconciliationSettlementTv.exportStandardItem
      }
    },
    // tab切换
    handleSelectTab(e) {
      this.currentTabInfo = this.tabList[e]
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    }
  },

  activated() {
    this.headerInfo = JSON.parse(localStorage.getItem('statementTvData'))
    this.entryId = this.$route.query.id
    this.tabList = [{ title: this.$t('对账明细'), code: 'reconciliationField' }]
  },

  beforeDestroy() {
    localStorage.removeItem('statementTvData')
  }
}
</script>

<style lang="scss" scoped>
.flex-fit {
  /deep/ .grid-container {
    overflow: auto;
    height: auto;
    flex: 1;
    > .mt-data-grid {
      height: 100%;

      > .e-grid {
        height: calc(100% - 42px);
      }
    }
  }
  > .grid-wrap {
    width: 100%;
  }
}

.mt-tabs {
  width: 100%;
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
</style>
