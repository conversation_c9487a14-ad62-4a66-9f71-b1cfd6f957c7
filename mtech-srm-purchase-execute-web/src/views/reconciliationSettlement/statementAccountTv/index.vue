<template>
  <div class="fields-config-page mt-flex-direction-column">
    <mt-tabs
      class="toggle-tab"
      :e-tab="false"
      :data-source="tabSource"
      :selected-item="selectedItem"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <div class="toggle-content">
      <standard v-show="tabIndex == 0" />
      <consignment v-show="tabIndex == 1" />
      <standardReport v-show="tabIndex == 2" />
      <consignmentReport v-show="tabIndex == 3" />
      <consignmentInOutReport v-show="tabIndex == 4" />
      <outgoing v-show="tabIndex == 5" />
      <saleRecon v-show="tabIndex == 6" />
      <outRecon v-show="tabIndex == 7" />
    </div>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
const purTabList = [
  {
    title: i18n.t('标准')
  },
  {
    title: i18n.t('寄售')
  },
  {
    title: i18n.t('标准对账单报表')
  },
  {
    title: i18n.t('寄售对账单报表')
  },
  {
    title: i18n.t('寄售进出存报表')
  },
  {
    title: i18n.t('注塑外发')
  },
  {
    title: i18n.t('销售对账')
  },
  {
    title: i18n.t('外发对账')
  }
]
export default {
  components: {
    standard: require('./pages/standard/index.vue').default,
    outgoing: require('./pages/outgoing/index.vue').default,
    standardReport: require('./pages/standardReport/index.vue').default,
    consignmentReport: require('./pages/consignmentReport/index.vue').default,
    consignmentInOutReport: require('./pages/consignmentInOutReport/index.vue').default,
    consignment: require('./pages/consignment/index.vue').default,
    saleRecon: require('./pages/saleRecon/index.vue').default,
    outRecon: require('./pages/outRecon/index.vue').default
  },
  data() {
    return {
      tabIndex: this.$route.query.currentIndex ?? 0,
      tabSource: purTabList,
      selectedItem: this.$route.query.currentIndex ?? 0
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    }
  }
}
</script>
