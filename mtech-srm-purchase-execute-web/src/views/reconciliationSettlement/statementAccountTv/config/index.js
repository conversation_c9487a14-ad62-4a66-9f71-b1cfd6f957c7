import { i18n } from '@/main.js'
// import { judgeFormatCodeName } from '@/utils/utils'

export const statusOptions = [
  { value: 1, text: i18n.t('未确认'), cssClass: 'col-abnormal' },
  { value: 2, text: i18n.t('已确认'), cssClass: 'col-active' },
  { value: 3, text: i18n.t('已退回'), cssClass: 'col-normal' },
  { value: 4, text: i18n.t('已作废'), cssClass: 'col-published' }
]

export const recoTypeOptions = [
  { value: 'A', text: i18n.t('标准对账单'), cssClass: '' },
  { value: 'C', text: i18n.t('标准追溯对账单'), cssClass: '' }
]

const addThousandSeparator = (num) => {
  if (!num && num !== 0) {
    return ''
  }
  var numParts = num.toString().split('.')
  numParts[0] = numParts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  return numParts.join('.')
}

export const standardColumnData = [
  // {
  //   width: '200',
  //   field: 'lineNo',
  //   headerText: i18n.t('行号')
  // },
  {
    width: '95',
    field: 'factoryCode',
    headerText: i18n.t('工厂代码')
  },
  {
    width: '105',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    // }
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    // }
  },
  {
    width: '105',
    field: 'sourceOrderCode',
    headerText: i18n.t('采购订单号')
  },
  {
    width: '140',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.itemCode, data?.itemName)
    // }
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    // }
  },
  {
    width: '95',
    field: 'materialVoucherItemNo',
    headerText: i18n.t('行项目')
  },
  {
    width: '80',
    field: 'location',
    headerText: i18n.t('库位')
  },
  {
    width: '150',
    field: 'materialVoucherDate',
    headerText: i18n.t('入库时间'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  // {
  //   width: '150',
  //   field: 'orderDate',
  //   headerText: i18n.t('订单建立日期'),
  //   type: 'date',
  //   format: 'yyyy-MM-dd'
  // },
  {
    width: '95',
    field: 'qty',
    headerText: i18n.t('入库数量')
  },
  {
    width: '80',
    field: 'currencyCode',
    headerText: i18n.t('币种')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.currencyCode, data?.currencyName)
    // }
  },
  {
    width: '95',
    field: 'unitPriceUntaxed',
    headerText: i18n.t('原单价')
  },
  {
    width: '105',
    field: 'executionUnitPriceUntaxed',
    headerText: i18n.t('应执行单价')
  },
  {
    width: '95',
    field: 'totalAmtUntaxed',
    headerText: i18n.t('原金额'),
    valueAccessor: (field, data) => {
      return addThousandSeparator(data?.totalAmtUntaxed)
    }
  },
  {
    width: '105',
    field: 'executionTotalPriceUntaxed',
    headerText: i18n.t('应执行金额'),
    valueAccessor: (field, data) => {
      return addThousandSeparator(data?.executionTotalPriceUntaxed)
    }
  },
  {
    width: '95',
    field: 'deductionAmtUntaxed',
    headerText: i18n.t('扣款金额'),
    valueAccessor: (field, data) => {
      return addThousandSeparator(data?.deductionAmtUntaxed)
    }
  },
  {
    width: '140',
    field: 'receiveCode',
    headerText: i18n.t('入库单号')
  },
  {
    width: '105',
    field: 'materialVoucherItemNo',
    headerText: i18n.t('入库单行号')
  },
  {
    width: '105',
    field: 'deliveryCode',
    headerText: i18n.t('送货单号')
  },
  // {
  //   field: 'supplierCode',
  //   headerText: i18n.t('利润中心')
  // },
  {
    width: '150',
    field: 'recordDate',
    headerText: i18n.t('记录日期')
  },
  {
    width: '150',
    field: 'supplierRemark',
    headerText: i18n.t('供应商备注')
  },
  {
    width: '150',
    field: 'buyerHistoryRemark',
    headerText: i18n.t('采购方历史备注')
  },
  {
    width: '150',
    field: 'buyerRemark',
    headerText: i18n.t('采购方备注')
  }
]
export const consignmentColumnData = [
  // {
  //   width: '200',
  //   field: 'lineNo',
  //   headerText: i18n.t('行号')
  // },
  {
    width: '95',
    field: 'factoryCode',
    headerText: i18n.t('工厂代码')
  },
  {
    width: '150',
    field: 'factoryName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '105',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    // }
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    // }
  },
  {
    width: '140',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.itemCode, data?.itemName)
    // }
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    // }
  },
  {
    width: '95',
    field: 'materialVoucherItemNo',
    headerText: i18n.t('行项目')
  },
  {
    width: '105',
    field: 'receiveCode',
    headerText: i18n.t('出库单号')
  },
  {
    width: '80',
    field: 'location',
    headerText: i18n.t('库位')
  },
  {
    width: '150',
    field: 'materialVoucherDate',
    headerText: i18n.t('出库日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    width: '95',
    field: 'qty',
    headerText: i18n.t('出库数量')
  },
  // {
  //   field: 'orderDate',
  //   headerText: i18n.t('订单建立日期')
  // },
  {
    width: '80',
    field: 'currencyCode',
    headerText: i18n.t('币种')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.currencyCode, data?.currencyName)
    // }
  },
  {
    width: '95',
    field: 'unitPriceUntaxed',
    headerText: i18n.t('原单价')
  },
  {
    width: '105',
    field: 'executionUnitPriceUntaxed',
    headerText: i18n.t('应执行单价')
  },
  {
    width: '95',
    field: 'totalAmtUntaxed',
    headerText: i18n.t('原金额'),
    valueAccessor: (field, data) => {
      return addThousandSeparator(data?.totalAmtUntaxed)
    }
  },
  {
    width: '105',
    field: 'executionTotalPriceUntaxed',
    headerText: i18n.t('应执行金额'),
    valueAccessor: (field, data) => {
      return addThousandSeparator(data?.executionTotalPriceUntaxed)
    }
  },
  {
    width: '95',
    field: 'deductionAmtUntaxed',
    headerText: i18n.t('扣款金额'),
    valueAccessor: (field, data) => {
      return addThousandSeparator(data?.deductionAmtUntaxed)
    }
  },
  {
    width: '95',
    field: 'initialStageInv',
    headerText: i18n.t('期初库存')
  },
  {
    width: '95',
    field: 'curMonthInstockQty',
    headerText: i18n.t('本月入库')
  },
  {
    width: '95',
    field: 'curMonthOutstockQty',
    headerText: i18n.t('本月出库')
  },
  {
    width: '95',
    field: 'curMonthAllotQty',
    headerText: i18n.t('本月移库')
  },
  {
    width: '95',
    field: 'curMonthBalanceQty',
    headerText: i18n.t('本月结余')
  },
  // {
  //   field: 'supplierCode',
  //   headerText: i18n.t('利润中心')
  // },
  {
    width: '150',
    field: 'recordDate',
    headerText: i18n.t('记录日期')
  },
  {
    width: '150',
    field: 'supplierRemark',
    headerText: i18n.t('供应商备注')
  },
  {
    width: '150',
    field: 'buyerHistoryRemark',
    headerText: i18n.t('采购方历史备注')
  },
  {
    width: '150',
    field: 'buyerRemark',
    headerText: i18n.t('采购方备注')
  }
]
export const outgoingColumnData = (recoTypeCode) => [
  {
    width: '95',
    field: 'factoryCode',
    headerText: i18n.t('工厂代码')
  },
  {
    width: '150',
    field: 'factoryName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '150',
    field: 'materialVoucherDate',
    headerText: i18n.t('收货过账日期'),
    type: 'date',
    format: 'yyyy-MM-dd'
  },
  {
    width: '105',
    field: 'receiveCode',
    headerText: i18n.t('入库凭证号')
  },
  {
    width: '95',
    field: 'materialVoucherItemNo',
    headerText: i18n.t('凭证项目号')
  },
  {
    width: '140',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.itemCode, data?.itemName)
    // }
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    // }
  },
  {
    width: '95',
    field: 'qty',
    headerText: i18n.t('收货数量')
  },
  {
    width: '80',
    field: 'unitCode',
    headerText: i18n.t('单位')
    // valueAccessor: (field, data) => {
    //   return judgeFormatCodeName(data?.unitCode, data?.unitName)
    // }
  },
  {
    width: '105',
    field: 'executionUnitPriceUntaxed',
    headerText: i18n.t('不含税单价'),
    visible: recoTypeCode === 'E' // 采购外协类型才显示
  },
  {
    width: '105',
    field: 'executionTotalPriceUntaxed',
    headerText: i18n.t('不含税金额'),
    visible: recoTypeCode === 'E' // 采购外协类型才显示
  },
  {
    width: '95',
    field: 'executionUnitPriceTaxed',
    headerText: i18n.t('含税单价'),
    visible: recoTypeCode === 'F' // 注塑申购类型才显示
  },
  {
    width: '95',
    field: 'executionTotalPriceTaxed',
    headerText: i18n.t('含税金额'),
    visible: recoTypeCode === 'F' // 注塑申购类型才显示
  },
  {
    width: '105',
    field: 'sourceOrderCode',
    headerText: i18n.t('采购订单编号')
  },
  {
    width: '148',
    field: 'orderItemNo',
    headerText: i18n.t('采购订单行项目号')
  },
  {
    width: '95',
    field: 'mrpBatch',
    headerText: i18n.t('MRP批次')
  },
  {
    width: '120',
    field: 'mrpContractNo',
    headerText: i18n.t('MRP合同号')
  }
]
