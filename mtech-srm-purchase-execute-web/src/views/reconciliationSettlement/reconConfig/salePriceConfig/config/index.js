// import UTILS from "@/utils/utils";
import { i18n } from '@/main.js'
import { timeNumberToDate, judgeFormatCodeName } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'
export const checkColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  }
]
export const lastColumn = [
  {
    field: 'code',
    headerText: i18n.t('单据编号'),
    width: '150',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'), //0,1,2,5,6,7
        visibleCondition: (data) => {
          return ![3, 4, 6, 7].includes(data.status)
        },
        permission: ['O_02_0434']
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'), // 0,1
        visibleCondition: (data) => {
          return [0, 1].includes(data.status)
        },
        permission: ['O_02_0435']
      }
    ]
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: [
        // 0-草稿；1-新建；2-修改；3-待审批；4-oa审批通过；5-oa审批决绝；6-sap推送成功；7-sap推送失败
        { value: 0, text: i18n.t('草稿'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('修改'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('待审批'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('oa审批通过'), cssClass: 'col-active' },
        { value: 5, text: i18n.t('oa审批拒绝'), cssClass: 'col-inactive' },
        { value: 6, text: i18n.t('sap推送成功'), cssClass: 'col-active' },
        { value: 7, text: i18n.t('sap推送失败'), cssClass: 'col-inactive' }
      ]
    }
  },
  // {
  //   field: "companyCode",
  //   headerText: i18n.t("公司编码"),
  //   width: "150",
  // },
  {
    field: 'companyCode',
    headerText: i18n.t('公司'),
    width: '250',
    searchOptions: MasterDataSelect.businessCompany,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.companyCode, data?.companyName)
    }
  },
  {
    field: 'saleGroupCode',
    headerText: i18n.t('销售组织编码'),
    width: '150'
  },
  {
    field: 'saleGroupName',
    headerText: i18n.t('销售组织'),
    width: '150'
  },
  {
    field: 'customerCode',
    headerText: i18n.t('客户编码'),
    width: '150',
    searchOptions: {
      operator: 'likeright',
      maxQueryValueLength: 20000
    }
  },
  {
    field: 'customerName',
    headerText: i18n.t('客户名称'),
    width: '150'
  },
  {
    field: 'typeName',
    headerText: i18n.t('条件类型'),
    width: '150'
  },
  {
    field: 'channelName',
    headerText: i18n.t('分销渠道'),
    width: '150'
  },
  {
    field: 'productTypeCode',
    headerText: i18n.t('产品类型编码'),
    width: '150'
  },
  {
    field: 'productTypeName',
    headerText: i18n.t('产品类型名称'),
    width: '150'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    width: '150'
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('修改人'),
    width: '150'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('修改时间'),
    width: '150',
    searchOptions: MasterDataSelect.dateRange
  },
  // {
  //   field: "updateTime",
  //   headerText: i18n.t("传SAP状态"),
  //   width: "150",
  //   valueConverter: {
  //     type: "map",
  //     map: [
  //       // 0-草稿；1-新建；2-修改；3-待审批；4-oa审批通过；5-oa审批决绝；6-sap推送成功；7-sap推送失败
  //       { value: 0, text: i18n.t("草稿"), cssClass: "col-active" },
  //       { value: 1, text: i18n.t("新建"), cssClass: "col-active" },
  //       { value: 2, text: i18n.t("修改"), cssClass: "col-active" },
  //       { value: 3, text: i18n.t("待审批"), cssClass: "col-active" },
  //       { value: 4, text: i18n.t("oa审批通过"), cssClass: "col-active" },
  //       { value: 5, text: i18n.t("oa审批拒绝"), cssClass: "col-inactive" },
  //       { value: 6, text: i18n.t("sap推送成功"), cssClass: "col-active" },
  //       { value: 7, text: i18n.t("sap推送失败"), cssClass: "col-inactive" },
  //     ],
  //   },
  // },
  {
    field: 'syncMsg',
    headerText: i18n.t('传SAP信息'),
    width: '150'
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
// 11.15 唯一区别是空调的没有配置按钮权限
export const lastColumnKt = [
  {
    field: 'code',
    headerText: i18n.t('单据编号'),
    width: '150',
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'), //0,1,2,5,6,7
        visibleCondition: (data) => {
          return ![3, 4, 6, 7].includes(data.status)
        }
        // permission: ["O_02_0434"],
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'), // 0,1
        visibleCondition: (data) => {
          return [0, 1].includes(data.status)
        }
        // permission: ["O_02_0435"],
      }
    ]
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: [
        // 0-草稿；1-新建；2-修改；3-待审批；4-oa审批通过；5-oa审批决绝；6-sap推送成功；7-sap推送失败
        { value: 0, text: i18n.t('草稿'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('修改'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('待审批'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('oa审批通过'), cssClass: 'col-active' },
        { value: 5, text: i18n.t('oa审批拒绝'), cssClass: 'col-inactive' },
        { value: 6, text: i18n.t('sap推送成功'), cssClass: 'col-active' },
        { value: 7, text: i18n.t('sap推送失败'), cssClass: 'col-inactive' }
      ]
    }
  },
  // {
  //   field: "companyCode",
  //   headerText: i18n.t("公司编码"),
  //   width: "150",
  // },
  {
    field: 'companyCode',
    headerText: i18n.t('公司'),
    width: '250',
    searchOptions: MasterDataSelect.businessCompany,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.companyCode, data?.companyName)
    }
  },
  {
    field: 'saleGroupCode',
    headerText: i18n.t('销售组织编码'),
    width: '150'
  },
  {
    field: 'saleGroupName',
    headerText: i18n.t('销售组织'),
    width: '150'
  },
  {
    field: 'customerCode',
    headerText: i18n.t('客户编码'),
    width: '150',
    searchOptions: {
      operator: 'likeright',
      maxQueryValueLength: 20000
    }
  },
  {
    field: 'customerName',
    headerText: i18n.t('客户名称'),
    width: '150'
  },
  {
    field: 'typeName',
    headerText: i18n.t('条件类型'),
    width: '150'
  },
  {
    field: 'channelName',
    headerText: i18n.t('分销渠道'),
    width: '150'
  },
  {
    field: 'productTypeCode',
    headerText: i18n.t('产品类型编码'),
    width: '150'
  },
  {
    field: 'productTypeName',
    headerText: i18n.t('产品类型名称'),
    width: '150'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    width: '150'
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('修改人'),
    width: '150'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('修改时间'),
    width: '150',
    searchOptions: MasterDataSelect.dateRange
  },
  // {
  //   field: "updateTime",
  //   headerText: i18n.t("传SAP状态"),
  //   width: "150",
  //   valueConverter: {
  //     type: "map",
  //     map: [
  //       // 0-草稿；1-新建；2-修改；3-待审批；4-oa审批通过；5-oa审批决绝；6-sap推送成功；7-sap推送失败
  //       { value: 0, text: i18n.t("草稿"), cssClass: "col-active" },
  //       { value: 1, text: i18n.t("新建"), cssClass: "col-active" },
  //       { value: 2, text: i18n.t("修改"), cssClass: "col-active" },
  //       { value: 3, text: i18n.t("待审批"), cssClass: "col-active" },
  //       { value: 4, text: i18n.t("oa审批通过"), cssClass: "col-active" },
  //       { value: 5, text: i18n.t("oa审批拒绝"), cssClass: "col-inactive" },
  //       { value: 6, text: i18n.t("sap推送成功"), cssClass: "col-active" },
  //       { value: 7, text: i18n.t("sap推送失败"), cssClass: "col-inactive" },
  //     ],
  //   },
  // },
  {
    field: 'syncMsg',
    headerText: i18n.t('传SAP信息'),
    width: '150'
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
export const lastColumnDetail = [
  {
    field: 'code',
    headerText: i18n.t('单据编号'),
    width: '150'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人'),
    width: '150'
    // template: () => {
    //   return {
    //     template: Vue.component("headers", {
    //       template: `
    //         <div class="headers">
    //           <span>{{data.createUserName}}</span>
    //         </div>
    //       `,
    //       data() {
    //         return {
    //           data: {},
    //         };
    //       },
    //     }),
    //   };
    // },
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: [
        // 0-草稿；1-新建；2-修改；3-待审批；4-oa审批通过；5-oa审批决绝；6-sap推送成功；7-sap推送失败
        { value: 0, text: i18n.t('草稿'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('修改'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('待审批'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('oa审批通过'), cssClass: 'col-active' },
        { value: 5, text: i18n.t('oa审批拒绝'), cssClass: 'col-inactive' },
        { value: 6, text: i18n.t('sap推送成功'), cssClass: 'col-active' },
        { value: 7, text: i18n.t('sap推送失败'), cssClass: 'col-inactive' }
      ]
    }
  },
  // {
  //   field: "companyCode",
  //   headerText: i18n.t("公司编码"),
  //   width: "150",
  // },
  {
    field: 'companyCode',
    headerText: i18n.t('公司'),
    width: '250',
    searchOptions: MasterDataSelect.businessCompany,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.companyCode, data?.companyName)
    }
  },
  {
    field: 'saleGroupCode',
    headerText: i18n.t('销售组织编码'),
    width: '150'
  },
  {
    field: 'saleGroupName',
    headerText: i18n.t('销售组织'),
    width: '150'
  },
  {
    field: 'customerCode',
    headerText: i18n.t('客户编码'),
    width: '150',
    searchOptions: {
      operator: 'likeright',
      maxQueryValueLength: 20000
    }
  },
  {
    field: 'customerName',
    headerText: i18n.t('客户名称'),
    width: '150'
  },
  {
    field: 'typeName',
    headerText: i18n.t('条件类型'),
    width: '150'
  },
  {
    field: 'channelName',
    headerText: i18n.t('分销渠道'),
    width: '150'
  },
  {
    field: 'productTypeCode',
    headerText: i18n.t('产品类型编码'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return row.header?.productTypeCode
      }
    }
  },
  {
    field: 'productTypeName',
    headerText: i18n.t('产品类型名称'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return row.header?.productTypeName
      }
    }
  },

  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('修改人'),
    width: '150'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('修改时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  // {
  //   field: "updateTime",
  //   headerText: i18n.t("传SAP状态"),
  //   width: "150",
  //   valueConverter: {
  //     type: "map",
  //     map: [
  //       // 0-草稿；1-新建；2-修改；3-待审批；4-oa审批通过；5-oa审批决绝；6-sap推送成功；7-sap推送失败
  //       { value: 0, text: i18n.t("草稿"), cssClass: "col-active" },
  //       { value: 1, text: i18n.t("新建"), cssClass: "col-active" },
  //       { value: 2, text: i18n.t("修改"), cssClass: "col-active" },
  //       { value: 3, text: i18n.t("待审批"), cssClass: "col-active" },
  //       { value: 4, text: i18n.t("oa审批通过"), cssClass: "col-active" },
  //       { value: 5, text: i18n.t("oa审批拒绝"), cssClass: "col-inactive" },
  //       { value: 6, text: i18n.t("sap推送成功"), cssClass: "col-active" },
  //       { value: 7, text: i18n.t("sap推送失败"), cssClass: "col-inactive" },
  //     ],
  //   },
  // },

  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  },
  {
    width: '200',
    field: 'itemCode',
    headerText: i18n.t('物料/品项编码'),
    searchOptions: {
      operator: 'likeright',
      maxQueryValueLength: 20000
    }
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料/品项名称'),
    allowEditing: false
  },
  {
    width: '300',
    field: 'buyerOrgName',
    headerText: i18n.t('采购组')

    // editType: "dropdownedit",
    // edit: {
    //   create() {
    //     wholeObj.buyerOrgNameEle = document.createElement("input");
    //     return wholeObj.buyerOrgNameEle;
    //   },
    //   read() {
    //     return wholeObj.buyerOrgNameObj?.value;
    //   },
    //   destroy() {
    //     wholeObj.buyerOrgNameObj.destroy();
    //   },
    //   write: (args) => {
    //     wholeObj.buyerOrgNameObj = new DropDownList({
    //       dataSource: editColumnParams.buyerGroupOptions,
    //       value: args.rowData?.buyerOrgName,
    //       fields: { text: "text", value: "value" },
    //       allowFiltering: true,
    //       change: (e) => {
    //         console.log("buyerOrgName", e);
    //         that.selectedChanged({
    //           fieldCode: "buyerOrgName",
    //           itemInfo: {
    //             buyerOrgCode: e.itemData.value,
    //             buyerOrgName: e.itemData.text,
    //           },
    //         });
    //       },
    //     });
    //     wholeObj.buyerOrgNameObj.appendTo(wholeObj.buyerOrgNameEle);
    //   },
    // },
    // valueAccessor: function (field, data, column) {
    //   console.log(i18n.t("采购组"), field, data, column);
    //   let dataSource = editColumnParams.buyerGroupOptions || [];
    //   return (
    //     dataSource.filter((i) => i.value == data[field])?.[0]?.text ||
    //     data[field]
    //   );
    // },
  },
  // {
  //   field: "freePrice",
  //   headerText: i18n.t("未税单价"),
  //   editType: "numericedit",
  // },
  {
    field: 'freePrice',
    headerText: i18n.t('未税单价'),
    edit: {
      params: {
        min: 0,
        format: '###.##',
        decimals: 2
      }
    }
  },
  {
    field: 'priceUnit',
    headerText: i18n.t('价格单位'),

    edit: {
      params: {
        max: 10000,
        decimals: 4,
        format: '###.####'
      }
    }
  },

  {
    width: '200',
    field: 'currencyName',
    headerText: i18n.t('币种'),
    selectWidth: 180
  },
  {
    width: '200',
    field: 'unitName',
    headerText: i18n.t('单位'),
    selectWidth: 180
  },
  {
    width: '225',
    field: 'taxRate',
    headerText: i18n.t('税率（%）'),
    type: 'string'
  },
  {
    width: '150',
    field: 'startTime',
    headerText: i18n.t('有效开始时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    width: '150',
    field: 'endTime',
    headerText: i18n.t('有效结束时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  }
]
