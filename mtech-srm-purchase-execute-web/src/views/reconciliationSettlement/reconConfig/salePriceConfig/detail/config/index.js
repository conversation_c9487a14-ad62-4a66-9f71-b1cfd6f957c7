import { i18n } from '@/main.js'
import { DropDownList } from '@syncfusion/ej2-dropdowns'
var bigDecimal = require('js-big-decimal')

import cellChanged from '@/components/normalEdit/cellChanged' // 单元格被改变（纯展示）
import cellTwoFormFilter from '@/components/normalEdit/cellTwoFormFilter' // 选不选物料，是两种显示，但为下拉时，根据输入搜索接口（品类、单位）
import buyerOrFilter from '../component/buyerOrFilter' // 采购组
import Vue from 'vue'
import selectedItemCode from '../component/selectedItemCode' // 物料、sku
import companySelector from '../component/companySelector' // 公司选择
import saleGroupSelector from '../component/saleGroupSelector' // 销售组织

export const checkCol = {
  width: '60',
  type: 'checkbox',
  allowEditing: false
}

export const detailCol = (editColumnParams, that, ktType) => {
  var wholeObj = {},
    createColKeys = ['taxRate', 'buyerOrgName']
  createColKeys.forEach((i) => {
    wholeObj[`${i}Ele`] = null
    wholeObj[`${i}Obj`] = null
  })
  const cols = [
    {
      width: '150',
      field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
      headerText: i18n.t('addId主键'),
      visible: false,
      isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
      isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。
      allowEditing: false
    }
  ]
  const ktCols = [
    {
      width: '300',
      field: 'companyName',
      headerText: i18n.t('公司'),
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('公司')}}</span>
                </div>
              `
          })
        }
      },
      editTemplate: () => {
        return {
          template: companySelector
        }
      }
    },
    {
      width: '300',
      field: 'salesOrgName',
      headerText: i18n.t('销售组织'),
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('销售组织')}}</span>
                </div>
              `
          })
        }
      },
      editTemplate: () => {
        return {
          template: saleGroupSelector
        }
      }
    }
  ]
  const defCols = [
    {
      width: '200',
      field: 'itemCode',
      headerText: i18n.t('物料/品项编码'),
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('物料/品项编码')}}</span>
                </div>
              `
          })
        }
      },
      editTemplate: () => {
        return {
          template: selectedItemCode
        }
      }
    },
    {
      width: '150',
      field: 'itemName',
      headerText: i18n.t('物料/品项名称'),
      allowEditing: false,
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('物料/品项名称')}}</span>
                </div>
              `
          })
        }
      },
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      width: '300',
      field: 'buyerOrgName',
      headerText: i18n.t('采购组'),
      editTemplate: () => {
        return {
          template: buyerOrFilter
        }
      }
      // editType: "dropdownedit",
      // edit: {
      //   create() {
      //     wholeObj.buyerOrgNameEle = document.createElement("input");
      //     return wholeObj.buyerOrgNameEle;
      //   },
      //   read() {
      //     return wholeObj.buyerOrgNameObj?.value;
      //   },
      //   destroy() {
      //     wholeObj.buyerOrgNameObj.destroy();
      //   },
      //   write: (args) => {
      //     wholeObj.buyerOrgNameObj = new DropDownList({
      //       dataSource: editColumnParams.buyerGroupOptions,
      //       value: args.rowData?.buyerOrgName,
      //       fields: { text: "text", value: "value" },
      //       allowFiltering: true,
      //       change: (e) => {
      //         console.log("buyerOrgName", e);
      //         that.selectedChanged({
      //           fieldCode: "buyerOrgName",
      //           itemInfo: {
      //             buyerOrgCode: e.itemData.value,
      //             buyerOrgName: e.itemData.text,
      //           },
      //         });
      //       },
      //     });
      //     wholeObj.buyerOrgNameObj.appendTo(wholeObj.buyerOrgNameEle);
      //   },
      // },
      // valueAccessor: function (field, data, column) {
      //   console.log(i18n.t("采购组"), field, data, column);
      //   let dataSource = editColumnParams.buyerGroupOptions || [];
      //   return (
      //     dataSource.filter((i) => i.value == data[field])?.[0]?.text ||
      //     data[field]
      //   );
      // },
    },
    // {
    //   field: "freePrice",
    //   headerText: i18n.t("未税单价"),
    //   editType: "numericedit",
    // },
    {
      field: ktType.includes('kt') ? 'freePrice' : 'taxPrice',
      headerText: ktType.includes('kt') ? i18n.t('未税单价') : i18n.t('含税单价'),
      editType: 'numericedit',
      edit: {
        params: {
          min: 0,
          format: '###.##',
          decimals: 2
        }
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span v-if='ktType.includes("kt")' class="e-headertext">{{$t('未税单价')}}</span>
                  <span v-else class="e-headertext">{{$t('含税单价')}}</span>
                </div>
              `,
            data() {
              return {
                data: {},
                ktType: ktType // 默认不全选
              }
            }
          })
        }
      }
    },
    {
      field: 'priceUnit',
      headerText: i18n.t('价格单位'),
      editType: 'numericedit',
      edit: {
        params: {
          max: 10000,
          decimals: 4,
          format: '###.####'
        }
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('价格单位')}}</span>
                </div>
              `
          })
        }
      }
    },

    {
      width: '200',
      field: 'currencyName',
      headerText: i18n.t('币种'),
      selectWidth: 180,
      editTemplate: () => {
        return {
          template: cellTwoFormFilter
        }
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('币种')}}</span>
                </div>
              `
          })
        }
      }
    },
    {
      width: '200',
      field: 'unitName',
      headerText: i18n.t('单位'),
      selectWidth: 180,
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('单位')}}</span>
                </div>
              `
          })
        }
      },
      editTemplate: () => {
        return {
          template: cellTwoFormFilter
        }
      }
    },
    {
      width: '225',
      field: 'taxRate',
      headerText: i18n.t('税率（%）'),
      type: 'string',
      editType: 'dropdownedit',
      edit: {
        create() {
          // console.log(i18n.t("税率的初始值"), args);
          wholeObj.taxRateEle = document.createElement('input')
          return wholeObj.taxRateEle
        },
        read: () => {
          return wholeObj.taxRateObj?.value
        },
        destroy() {
          wholeObj.taxRateObj.destroy()
        },
        write: (args) => {
          wholeObj.taxRateObj = new DropDownList({
            width: '200',
            dataSource: editColumnParams.taxidData,
            value: args.rowData?.taxRate,
            fields: { text: 'showLabel', value: 'id' }, // 因为编码可能会重复，因此这里用id
            allowFiltering: true,
            change: (e) => {
              console.log(i18n.t('税率改变'), e)
              that.selectedChanged({
                fieldCode: 'taxRate',
                itemInfo: {
                  taxCode: e.itemData.taxItemCode,
                  taxRate: Number(bigDecimal.divide(e.itemData.taxRate, 100))
                }
              })
            }
          })
          wholeObj.taxRateObj.appendTo(wholeObj.taxRateEle)
        }
      },
      valueAccessor: function (field, data) {
        let dataSource = editColumnParams.taxidData || []
        console.log('选择完了税率---', dataSource, data)
        let res = ''
        if (data['taxRate']) {
          res = dataSource.filter((i) => i.id == data[field])?.[0]?.showLabel
        }
        // 如果是第一次进入编辑时，taxRate还是0.05，不是id
        if (!res && data['taxRate']) {
          res = data['taxCode'] + ' - ' + data['taxRate'] * 100
        }
        return res
      }
    },
    {
      width: '150',
      field: 'startTime',
      headerText: i18n.t('有效开始时间'),
      editType: 'datepickeredit',
      type: 'date',
      format: 'yyyy-MM-dd',
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('有效开始时间')}}</span>
                </div>
              `
          })
        }
      }
    },
    {
      width: '150',
      field: 'endTime',
      headerText: i18n.t('有效结束时间'),
      editType: 'datepickeredit',
      type: 'date',
      format: 'yyyy-MM-dd',
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{$t('有效结束时间')}}</span>
                </div>
              `
          })
        }
      }
    }
  ]
  return ktType === 'kt' ? cols.concat(ktCols).concat(defCols) : cols.concat(defCols)
}
