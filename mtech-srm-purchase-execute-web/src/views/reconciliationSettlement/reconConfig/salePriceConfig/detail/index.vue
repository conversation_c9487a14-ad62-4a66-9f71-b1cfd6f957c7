<template>
  <div class="full-height mt-flex-direction-column">
    <div v-if="isKtBu">
      <top-info-kt :header="header" @handleSave="handleSave"></top-info-kt>
      <bottom-grid-kt
        :items="items"
        :files="files"
        @handleFile="handleFile"
        ref="saleBottomGrid"
      ></bottom-grid-kt>
    </div>
    <div v-else>
      <top-info-bd :header="header" @handleSave="handleSave"></top-info-bd>
      <bottom-grid-bd
        :items="items"
        :files="files"
        @handleFile="handleFile"
        ref="saleBottomGrid"
      ></bottom-grid-bd>
    </div>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
export default {
  components: {
    TopInfoBd: require('./page_BD/topInfo.vue').default,
    BottomGridBd: require('./page_BD/bottomGrid.vue').default,
    TopInfoKt: require('./page_KT/topInfo.vue').default,
    BottomGridKt: require('./page_KT/bottomGrid.vue').default
  },
  data() {
    return {
      header: null,
      items: [],
      file: [],
      files: [],
      id: null // 提交/保存草稿后，返回id
    }
  },
  computed: {
    isKtBu() {
      return this.$route.query.ktType === 'kt'
    }
  },
  mounted() {
    if (this.$route.query.id) this.getDetail()
  },

  methods: {
    getDetail() {
      this.$API.reconciliationSettlement.getSalePriceDetail(this.$route.query.id).then((res) => {
        if (!res.data) return
        this.header = res.data?.header || null
        this.items = cloneDeep(res.data?.items)
        this.files = res.data.fileBaseInfoList ?? []
      })
    },
    /**
     * flag: save-保存草稿；submit-提交
     */
    handleFile(e) {
      this.file = e
      console.log(this.file)
    },
    handleSave(flag, topData) {
      console.log('handleSave', flag, topData)

      this.$refs.saleBottomGrid.handleA()

      let gridData = this.$refs.saleBottomGrid.formatData(flag)
      console.log('获取到的gridData', gridData)
      if (!gridData) return
      this.file.forEach((item) => {
        item.parentId = 0
      })
      let params = {
        header: topData,
        items: gridData,
        // fileList: this.$refs.relativeFileRef.getUploadFlies(
        //       this.moduleFileList[0].id
        //     ),
        files: this.file
      }

      console.log('请i去数据', flag, params)
      let url = flag == 'save' ? 'saveSalePriceDraft' : 'submitSalePriceDraft'
      this.$API.reconciliationSettlement[url](params).then(() => {
        if (flag == 'save') {
          // if (!this.$route.query.id) {
          // if (this.$route.path.includes("kt")) {
          //   this.$router.replace(`sale-price-config-kt`);
          // } else {
          //   this.$router.replace(`sale-price-config`);
          // }
          this.$router.go(-1)
          // }
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
        } else {
          // if (this.$route.path.includes("kt")) {
          //   this.$router.replace("sale-price-config-kt");
          // } else {
          //   this.$router.replace("sale-price-config");
          // }
          this.$router.go(-1)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mt-flex-direction-column {
  /deep/ .bottomGrid {
    flex: 1;
    overflow: auto;

    .repeat-template,
    .template-wrap {
      height: 100%;

      .grid-container {
        flex: 1;
        overflow: auto;
        height: auto;

        .e-gridcontent {
          overflow: auto;
          display: flex;
        }
      }
    }
  }
}
</style>
