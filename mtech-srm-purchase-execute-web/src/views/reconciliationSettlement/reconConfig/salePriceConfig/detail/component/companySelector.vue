<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <select-filter
        id="companyName"
        :width="280"
        :fields="companyObj.fields"
        :request-url="companyObj.requestUrl"
        :request-key="companyObj.requestKey"
        :init-val.sync="data['companyName']"
        :other-params="companyObj.otherParams"
        :label-show-obj="companyObj.labelShowObj"
        :placeholder="$t('请选择公司')"
        @handleChange="handleCompanyChange"
      ></select-filter>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {},
      // 业务公司
      companyObj: {
        requestUrl: {
          pre: 'masterData',
          url: 'OrgFindSpecifiedChildrenLevelOrgs'
        },
        requestKey: 'fuzzyParam',
        otherParams: {
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true
        },
        fields: {
          text: 'labelShow',
          value: 'labelShow'
        },
        labelShowObj: {
          code: 'orgCode',
          name: 'orgName'
        }
      }
    }
  },
  mounted() {},
  methods: {
    // 业务公司改变
    handleCompanyChange(e) {
      this.$parent.$emit('selectedChanged', {
        fieldCode: 'companyName',
        itemInfo: {
          companyCode: e.itemData?.orgCode,
          companyName: e.itemData?.orgName
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>

<style lang="scss">
.pc-item-dialog {
  .mt-data-grid {
    height: 100%;
    > .e-grid {
      height: calc(100% - 40px) !important;
      // display: flex;

      > .e-gridcontent {
        flex: 1;
        overflow: auto;
      }
    }

    .e-rowcell.e-active {
      background: #e0e0e0 !important;
    }
  }
}
</style>
