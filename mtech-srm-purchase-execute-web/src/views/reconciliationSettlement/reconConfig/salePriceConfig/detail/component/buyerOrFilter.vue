<template>
  <!-- 采购组 -->
  <div>
    <mt-input
      :id="data.column.field"
      :value="data[data.column.field]"
      :disabled="true"
      v-show="!data.column.allowEditing"
    ></mt-input>
    <select-filter
      v-show="data.column.allowEditing"
      id="buyerOrgName"
      :width="280"
      :fields="fields"
      :request-url="requestUrl"
      :request-key="requestKey"
      :init-val.sync="data['buyerOrgName']"
      :other-params="otherParams"
      :label-show-obj="labelShowObj"
      @handleChange="handleChange"
    ></select-filter>
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {},
      fields: {
        text: 'labelShow',
        value: 'labelShow'
      },
      requestUrl: {
        pre: 'masterData',
        url: 'getbussinessGroup'
      },
      requestKey: 'fuzzyParam',
      otherParams: {
        groupTypeCode: 'BG001CG'
      },
      labelShowObj: {
        code: 'groupCode',
        name: 'groupName'
      }
    }
  },
  methods: {
    handleChange(e) {
      console.log(this.$t('接受到了变化'), e)
      this.$parent.$emit('selectedChanged', {
        fieldCode: 'buyerOrgName',
        itemInfo: {
          buyerOrgCode: e.itemData?.groupCode,
          buyerOrgName: e.itemData?.groupName
        }
      })
    }
  }
}
</script>

<style></style>
