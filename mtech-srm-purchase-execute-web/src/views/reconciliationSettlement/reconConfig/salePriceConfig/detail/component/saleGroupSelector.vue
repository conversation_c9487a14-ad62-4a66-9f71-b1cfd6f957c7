<template>
  <div class="pc-select">
    <div class="in-cell" id="in-cell">
      <mt-select
        v-model="data.salesOrgName"
        id="salesOrgName"
        :data-source="saleGroupOptions"
        :placeholder="$t('请选择')"
        :fields="{ text: 'organizationName', value: 'organizationName' }"
        @change="change"
      ></mt-select>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      data: {},
      saleGroupOptions: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      //销售组
      this.$API.masterData
        .purchaseOraginaze({
          organizationTypeCode: 'BUORG001ADM'
        })
        .then((res) => {
          this.saleGroupOptions = res.data || []
        })
    },
    // 业务公司改变
    change(e) {
      this.$parent.$emit('selectedChanged', {
        fieldCode: 'salesOrgName',
        itemInfo: {
          salesOrgCode: e.itemData?.organizationCode,
          salesOrgName: e.itemData?.organizationName
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-select {
  display: flex;

  #in-cell {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > .mt-icons {
      margin-left: 10px;
      cursor: pointer;
    }
    /deep/ .e-input.e-disabled {
      height: 33px !important; // 与行内编辑的输入框禁用样式 保持一致
      padding-left: 10px !important;
      background: #f5f5f5 !important;
    }
  }
}
</style>

<style lang="scss">
.pc-item-dialog {
  .mt-data-grid {
    height: 100%;
    > .e-grid {
      height: calc(100% - 40px) !important;
      // display: flex;

      > .e-gridcontent {
        flex: 1;
        overflow: auto;
      }
    }

    .e-rowcell.e-active {
      background: #e0e0e0 !important;
    }
  }
}
</style>
