<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <div class="header-box">
      <div class="infos mr20" v-if="header">
        {{ $t('单据编号：') }}{{ header.code ? header.code : '-' }}
      </div>
      <div class="infos mr20" style="color: #9a9a9a">
        {{ $t('创建人：') }}
        {{ ruleForm.createUserName ? ruleForm.createUserName : '-' }}
      </div>
      <div class="infos" style="color: #9a9a9a">
        {{ $t('创建时间：') }}{{ ruleForm.createTime | dateFormat }}
      </div>
      <div class="middle-blank"></div>
      <!-- 右侧各种操作按钮 -->
      <mt-button css-class="e-flat invite-btn" :is-primary="true" @click="goBack">{{
        $t('返回')
      }}</mt-button>
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-if="$route.query.type != 'view' && canDraft"
        @click="handleSave('save')"
        >{{ $t('保存草稿') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        v-if="$route.query.type != 'view'"
        @click="handleSave('submit')"
        >{{ $t('提交') }}</mt-button
      >
      <div class="sort-box" @click="expandChange">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>
    <!-- <button @click="ruleForm.customerCode = 'T01'">{{ $t("点击赋值") }}</button> -->
    <!-- 下面的内容 -->
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules" :validate-on-rule-change="false">
        <!-- <mt-form-item prop="companyCode" :label="$t('公司')" class="apply-item">
          <select-filter
            :width="350"
            ref="companyRef"
            :fields="companyObj.fields"
            :request-url="companyObj.requestUrl"
            :request-key="companyObj.requestKey"
            :init-val.sync="ruleForm.companyCode"
            :other-params="companyObj.otherParams"
            :label-show-obj="companyObj.labelShowObj"
            :placeholder="$t('请选择公司')"
            :disabled="$route.query.type == 'view'"
            @handleChange="handleCompanyChange"
          ></select-filter>
        </mt-form-item> -->
        <mt-form-item prop="customerCode" :label="$t('客户')">
          <mt-select
            allow-filtering="true"
            v-model="ruleForm.customerCode"
            :data-source="customerCodeOptions"
            :placeholder="$t('请选择')"
            :fields="{ text: 'customerName', value: 'customerCode' }"
            :filtering="getCustomer"
            :disabled="$route.query.type == 'view'"
          ></mt-select>
        </mt-form-item>
        <!-- <mt-form-item prop="saleGroupCode" :label="$t('销售组织')">
          <mt-select
            v-model="ruleForm.saleGroupCode"
            :data-source="saleGroupOptions"
            :placeholder="$t('请选择')"
            :fields="{ text: 'organizationName', value: 'organizationCode' }"
            :disabled="$route.query.type == 'view'"
          ></mt-select>
        </mt-form-item> -->
        <mt-form-item prop="typeCode" :label="$t('条件类型')">
          <mt-select
            v-model="ruleForm.typeCode"
            :data-source="typeOptions"
            :placeholder="$t('请选择')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :disabled="$route.query.type == 'view'"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="channelCode" :label="$t('分销渠道')">
          <mt-select
            v-model="ruleForm.channelCode"
            :data-source="channelOptions"
            :placeholder="$t('请选择')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :disabled="$route.query.type == 'view'"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="productTypeCode" :label="$t('产品类型')">
          <mt-select
            v-model="ruleForm.productTypeCode"
            :data-source="productList"
            :placeholder="$t('请选择')"
            :fields="{ text: 'productTypeName', value: 'productTypeCode' }"
            :disabled="$route.query.type == 'view'"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('采方备注')" class="full-width">
          <mt-input v-model="ruleForm.remark" :disabled="$route.query.type == 'view'"></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>
<script>
import { utils } from '@mtech-common/utils'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
export default {
  props: {
    header: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      isExpand: true,
      rules: {
        // companyCode: [
        //   {
        //     required: true,
        //     message: this.$t("请选择公司"),
        //     trigger: "change",
        //   },
        // ],
        customerCode: [{ required: true, message: this.$t('请选择客户'), trigger: 'blur' }],
        // saleGroupCode: [
        //   {
        //     required: true,
        //     message: this.$t("请选择销售组织"),
        //     trigger: "blur",
        //   },
        // ],
        typeCode: [
          {
            required: true,
            message: this.$t('请选择条件类型'),
            trigger: 'blur'
          }
        ],
        channelCode: [
          {
            required: true,
            message: this.$t('请选择分销渠道'),
            trigger: 'blur'
          }
        ],
        productTypeCode: [
          {
            required: true,
            message: this.$t('请选择产品类型'),
            trigger: 'blur'
          }
        ]
      },
      // companyOptions: [], //公司
      customerCodeOptions: [], //客户
      // saleGroupOptions: [], //销售组织
      typeOptions: [], //条件类型
      channelOptions: [], //分销渠道
      ruleForm: {
        companyCode: null, //公司
        customerCode: null, //客户
        saleGroupCode: null, //销售组织
        typeCode: null, //条件类型
        channelCode: null, //分销渠道
        productTypeCode: null, // 产品类型
        productTypeName: null,
        remark: ''
      },
      // 业务公司
      // companyObj: {
      //   requestUrl: {
      //     pre: 'masterData',
      //     url: 'OrgFindSpecifiedChildrenLevelOrgs'
      //   },
      //   requestKey: 'fuzzyParam',
      //   otherParams: {
      //     organizationLevelCodes: ['ORG02', 'ORG01'],
      //     orgType: 'ORG001PRO',
      //     includeItself: true
      //   },
      //   fields: {
      //     text: 'labelShow',
      //     value: 'orgCode'
      //   },
      //   labelShowObj: {
      //     code: 'orgCode',
      //     name: 'orgName'
      //   }
      // },
      productList: [
        {
          productTypeName: this.$t('冰箱'),
          productTypeCode: '01'
        },
        {
          productTypeName: this.$t('洗衣机'),
          productTypeCode: '02'
        },
        {
          productTypeName: this.$t('空调'),
          productTypeCode: '03'
        }
      ]
    }
  },
  filters: {
    dateFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }

      return str
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo
    },
    canDraft() {
      if (!this.header) {
        return true
      } else if (this.header && [0, 1].includes(this.header?.status)) {
        return true
      }
      return false
    }
  },
  watch: {
    userInfo(newVal) {
      // 如果是新增
      if (this.$route.query.type == 'add') this.ruleForm.createUserName = newVal.accountName
    },
    header: {
      handler(newVal) {
        console.log('newVal', newVal)
        this.ruleForm = { ...newVal }
        // this.ruleForm.remark = newVal.remark;
        // this.ruleForm.companyCode = newVal.companyCode;
        // this.ruleForm.customerCode = null;
        if (newVal?.customerCode) {
          this.getCustomer({ text: newVal.customerCode })
        }
        this.getOptions()
      },
      immediate: true
    }
  },

  mounted() {
    console.log('我是详情页面的mounted')
    // 如果是新增，设置 创建时间
    if (this.$route.query.type == 'add') {
      this.ruleForm.createUserName = this.userInfo.accountName
      this.ruleForm.createTime = new Date()
    }
    this.getCustomer = utils.debounce(this.getCustomer, 500)
    // 如果不是进详情，就主动获取下拉
    if (!this.$route.query.id) {
      this.getCustomer({ text: '' })
      this.getOptions()
    }
  },
  activated() {
    console.log('我是详情页面的actived')
    // // 如果是新增，设置 创建时间
    // if (this.$route.query.type == "add") {
    //   this.ruleForm.createUserName = this.userInfo.accountName;
    //   this.ruleForm.createTime = new Date();
    // }
    // this.getCustomer = utils.debounce(this.getCustomer, 500);
    // // 如果不是进详情，就主动获取下拉
    // if (!this.$route.query.id) {
    //   this.getCustomer({ text: "" });
    //   this.getOptions();
    // }
  },

  methods: {
    getCustomer(e) {
      //客户
      let str = e?.text || ''
      let params = {
        fuzzyNameOrCode: str
      }
      this.$API.masterData.getCustomer(params).then((res) => {
        this.customerCodeOptions = res.data || []
        this.$nextTick(() => {
          if (e.updateData && typeof e.updateData == 'function') {
            e.updateData(this.customerCodeOptions)
          }
        })
        if (this.header?.customerCode) this.ruleForm.customerCode = this.header?.customerCode
      })
    },

    // 业务公司改变
    handleCompanyChange(e) {
      // this.ruleForm.companyCode = e?.itemData?.orgCode;
      this.ruleForm.companyName = e?.itemData?.orgName
    },
    getOptions() {
      // //获取公司
      // this.$API.masterData
      //   .OrgFindSpecifiedChildrenLevelOrgs({
      //     organizationLevelCodes: ["ORG02", "ORG01"],
      //     orgType: "ORG001PRO",
      //     includeItself: true,
      //     organizationIds: [],
      //   })
      //   .then((res) => {
      //     this.companyOptions = res.data;
      //     if (this.header?.companyCode)
      //       this.ruleForm.companyCode = this.header?.companyCode;
      //   });
      //销售组
      // this.$API.masterData
      //   .purchaseOraginaze({
      //     organizationTypeCode: 'BUORG001ADM'
      //   })
      //   .then((res) => {
      //     this.saleGroupOptions = res.data || []
      //     if (this.header?.saleGroupCode) this.ruleForm.saleGroupCode = this.header?.saleGroupCode
      //   })
      //条件类型
      this.$API.masterData.getDictCode({ dictCode: 'conditionType' }).then((res) => {
        this.typeOptions = res.data || []
        if (this.header?.typeCode) this.ruleForm.typeCode = this.header?.typeCode
      })
      //条件类型
      this.$API.masterData.getDictCode({ dictCode: 'distributionChannel' }).then((res) => {
        this.channelOptions = res.data || []
        if (this.header?.channelCode) this.ruleForm.channelCode = this.header?.channelCode
      })
    },
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (!valid) {
          this.$toast({ content: this.$t('请保存头部信息'), type: 'error' })
          return
        }
        let params = this.getParams()
        console.log(params, '保存参数')
      })
    },
    getParams() {
      //获取参数
      let params = {
        // companyCode: this.ruleForm.companyCode, //公司
        // companyName: this.ruleForm.companyName,
        customerCode: this.ruleForm.customerCode, //客户
        customerName: this.customerCodeOptions.find((item) => {
          return item.customerCode == this.ruleForm.customerCode
        })?.customerName,
        // saleGroupCode: this.ruleForm.saleGroupCode, //销售组织
        // saleGroupName: this.saleGroupOptions.find((item) => {
        //   return item.organizationCode == this.ruleForm.saleGroupCode
        // })?.organizationName,
        typeCode: this.ruleForm.typeCode, //条件类型
        typeName: this.typeOptions.find((item) => {
          return item.itemCode == this.ruleForm.typeCode
        })?.itemName,
        channelCode: this.ruleForm.channelCode, //分销渠道
        channelName: this.channelOptions.find((item) => {
          return item.itemCode == this.ruleForm.channelCode
        })?.itemName,
        productTypeCode: this.ruleForm.productTypeCode, // 产品类型
        productTypeName: this.productList.find((item) => {
          return item.productTypeCode == this.ruleForm.productTypeCode
        })?.productTypeName
      }
      return params
    },
    handleSave(flag) {
      if (flag == 'submit') {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            this.toSave(flag)
          } else {
            this.$emit('请先将头部填写完整')
          }
        })
      } else {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            this.toSave(flag)
          } else {
            this.$emit('请先将头部填写完整')
          }
        })
      }
    },
    toSave(flag) {
      let params = {
        ...this.ruleForm,
        ...this.getParams()
      }
      if (this.$route.query.id) {
        params.id = this.$route.query.id
      }
      if (this.header?.code) {
        params.code = this.header?.code
      }
      this.$emit('handleSave', flag, params)
    },

    goBack() {
      // this.$router.push({
      //   name: "sale-price-config",
      // });
      // if (this.$route.path.includes("kt")) {
      //   this.$router.replace("sale-price-config-kt");
      // } else {
      //   this.$router.replace("sale-price-config");
      // }
      this.$router.go(-1)
    },
    expandChange() {
      this.isExpand = !this.isExpand
      this.$refs.ruleForm.clearValidate()
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  background: #fff;
  padding: 0 0 0 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }

      &.apply-item {
        width: 350px;
      }
    }
  }
}
</style>
