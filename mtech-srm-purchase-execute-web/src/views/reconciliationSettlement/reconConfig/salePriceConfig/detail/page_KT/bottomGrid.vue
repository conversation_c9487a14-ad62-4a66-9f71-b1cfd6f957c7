<template>
  <div>
    <mt-tabs
      tab-id="reconci-tab"
      :e-tab="false"
      :data-source="tabList"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <mt-template-page
      ref="salePriceRef"
      class="bottomGrid template-height"
      :template-config="templateConfig"
      v-show="tabIndex == 0"
      @handleClickToolBar="handleClickToolBar"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
      @selectedChanged="selectedChanged"
    ></mt-template-page>
    <relative-file
      ref="relativeFileRef"
      :hide-left-node="true"
      :btn-required="btnRequired"
      v-show="tabIndex == 1"
      :require-files="requireFiles"
    ></relative-file>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { checkCol, detailCol } from '../config'

export default {
  components: {
    relativeFile: require('@/components/businessComponents/relativeFileSale/index.vue').default
  },
  props: {
    items: {
      type: Array,
      default: () => []
    },
    files: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      path: this.$route.query.ktType || '',
      templateConfig: [
        {
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: [
            [
              'Add',
              'Delete',
              {
                id: 'endEdit',
                icon: 'icon_table_save',
                title: this.$t('更新数据')
              }
            ]
          ],
          gridId: this.$tableUUID.reconciliationSettlement.salePPriceConfig.detail,
          grid: {
            // height: 'auto',
            columnData: [checkCol],
            dataSource: [],
            allowPaging: false,
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // Batch模式下editTemplate使用下拉的话，保存的args里获取不到，所以就回显不上。最终用重新赋值dataSource解决(币种和贸易条款的下拉)
              showConfirmDialog: false,
              showDeleteConfirmDialog: true
            }
          }
        }
      ],
      requireFiles: [],
      btnRequired: {
        hasUpload: true,
        hasDownload: true,
        hasDelete: true
      },
      moduleFileList: [
        // {
        //   id: "152",
        //   code: "reconciliation_settled_header_file", // 清账整单附件code
        //   nodeName: this.$t("采方 -附件"),
        //   type: nodeType.mainUpdate,
        //   dataSource: [],
        // },
      ],

      tabIndex: 0,

      tabList: [
        {
          title: this.$t('价格明细')
        },
        {
          title: this.$t('相关附件')
        }
      ],
      rowsSelectedInfo: {}, //  编辑行的下拉数据，包括对应的id和code（不能用序号，得换id或addId作为唯一标识）  {row-:{categoryId:"", categoryCode:""....}}
      nowEditRowFlag: '', // 当前编辑行的id或addId，可以是编辑也可能是新增。。在actionBegin时记录，在actionComplete时清空
      taxidData: [], // 税率
      buyerGroupOptions: [] // 采购组
    }
  },
  watch: {
    files: {
      handler(newVal) {
        // this.moduleFileList = [
        //   {
        //     id: "152",
        //     code: "reconciliation_settled_header_file", // 清账整单附件code
        //     nodeName: this.$t("采方 -附件"),
        //     type: nodeType.mainEditViewData,
        //     dataSource: newVal,
        //     // methods: "get",
        //     // url: `${BASE_TENANT}/requestFile/queryFileByDocId`,
        //     // params: {
        //     //   docId: this.$route.query.id,
        //     //   parentId: "152",
        //     // },
        //   },
        // ];
        this.requireFiles = newVal
        console.log(this.requireFiles, '123123')
      }
    },
    items: {
      handler(newVal) {
        setTimeout(() => {
          let _dataSource = []
          newVal.forEach((item) => {
            _dataSource.push({
              ...item,
              startTime: item.startTime ? new Date(Number(item.startTime)) : null,
              endTime: item.endTime ? new Date(Number(item.endTime)) : null,
              buyerOrgName: item.buyerOrgCode + ' - ' + item.buyerOrgName,
              companyName: item.companyCode + ' - ' + item.companyName // ' - ' 注意横杆两边的空格要保留，不然初始化匹配不上
            })
          })

          this.templateConfig[0].grid.dataSource = cloneDeep(_dataSource)
        }, 10)

        // this.$set(this.templateConfig[0].grid, "dataSource", _dataSource);
      }
    }
  },
  mounted() {
    this.getList() // 获取下拉
    // window.salePriceGrid =
    //   this.$refs.salePriceRef.getCurrentUsefulRef().gridRef.ejsRef;
  },
  activated() {
    if (this.$route.query.type == 'view') {
      this.$set(this.templateConfig[0], 'toolbar', [])

      this.btnRequired = {
        hasUpload: false,
        hasDownload: true,
        hasDelete: false
      }
      this.$set(this.templateConfig[0].grid.editSettings, 'allowEditing', false)
    }
  },
  methods: {
    // 点击 保存 或 提交时，整合数据
    formatData(flag) {
      let viewRecords = this.$refs.salePriceRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      console.log('表格里的数据', viewRecords, this.rowsSelectedInfo)

      let _records = []
      viewRecords.forEach((item) => {
        let oneRecord = item,
          flag = item.id || item.addId
        if (this.rowsSelectedInfo[`row-${flag}`]) {
          oneRecord = {
            ...item,
            ...this.rowsSelectedInfo[`row-${flag}`]
          }
          console.log('oneRecord', oneRecord)
        }
        if (item.startTime) {
          oneRecord.startTime = new Date(item.startTime).getTime()
        }
        if (item.endTime) {
          oneRecord.endTime = new Date(item.endTime).getTime()
        }
        if (oneRecord.companyName && oneRecord.companyName.indexOf('-') > -1) {
          oneRecord.companyCode = oneRecord.companyName.split('-')[0]
          oneRecord.companyName = oneRecord.companyName.split('-')[1]
        }
        _records.push(oneRecord)
      })
      if (flag == 'save') {
        return _records
      }
      try {
        _records.forEach((el, index) => {
          // 校验必填
          // 暂时 去掉采购组必填 el.buyerOrgName &&
          if (
            !(
              el.itemCode &&
              el.currencyName &&
              el.unitName &&
              el.startTime &&
              el.endTime &&
              el.companyName &&
              el.salesOrgName
            )
          ) {
            this.$toast({
              content: this.$t(`请将第${Number(index + 1)}行的数据填写完整`),
              type: 'warning'
            })
            throw new Error('时间选择错误')
          }
          // console.log(this.$route.path.includes("kt"));
          // console.log(this.$route.path);
          if (this.$route.query.ktType === 'kt') {
            if ((!el.priceUnit && el.priceUnit != 0) || (!el.freePrice && el.freePrice != 0)) {
              this.$toast({
                content: this.$t(`请将第${Number(index + 1)}}行的数据填写完整`),
                type: 'warning'
              })
              throw new Error('时间选择错误')
            }
          } else {
            if ((!el.taxPrice && el.taxPrice != 0) || (!el.priceUnit && el.priceUnit != 0)) {
              this.$toast({
                content: this.$t(`请将第${Number(index + 1)}}行的数据填写完整`),
                type: 'warning'
              })
              throw new Error('时间选择错误')
            }
          }

          // 校验数据有效时间
          if (el.startTime && el.endTime) {
            if (el.startTime > el.endTime) {
              this.$toast({
                content: this.$t('有效开始时间应小于有效结束时间'),
                type: 'warning'
              })
              throw new Error('时间选择错误')
            }
          }
        })
        return _records
      } catch (e) {
        return null
      }
    },
    handleA() {
      // alert(1);
      // console.log(
      //   this.$refs.relativeFileRef.getUploadFlies(this.moduleFileList[0].id)
      // );

      this.$emit('handleFile', this.$refs.relativeFileRef.getDataSource())
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    actionBegin(args) {
      console.log('actionBegin', args)
      if (args.requestType === 'add') {
        args.data.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
        this.nowEditRowFlag = args.data.addId // 记录当前编辑行的唯一标识
      }
      // 记录当前编辑行的唯一标识
      if (args.requestType == 'beginEdit') {
        this.nowEditRowFlag = args.rowData.addId || args.rowData?.id
      }
    },

    actionComplete(args) {
      console.log('actionComplete', args)
      if (args.action == 'add' || args.action == 'edit') {
        this.nowEditRowFlag = ''
      }
      // if (args.requestType === 'save') {
      //   const curEditItem = this.templateConfig.grid.dataSource
      // }
    },

    selectedChanged(params) {
      let { itemInfo } = params
      // 暂时没有考虑分页，要是分页，得外层再加一层page的
      // console.log("handleSetSelectedInfo", fieldCode, params);
      let _nowRowSelectedInfo = itemInfo,
        _flag = this.nowEditRowFlag // 如果已有这一行数据
      // rowsSelectedInfo  {row-1: {name: 'sdf', age: 13}, row-2xxx: {class: '13d'}}
      if (Object.prototype.hasOwnProperty.call(this.rowsSelectedInfo, `row-${_flag}`)) {
        _nowRowSelectedInfo = {
          ...this.rowsSelectedInfo[`row-${_flag}`],
          ...itemInfo
        }
      }

      // if (fieldCode == "supplierName") {
      //   // 推荐供应商
      //   _nowRowSelectedInfo.supplier = itemInfo;
      // }
      this.rowsSelectedInfo[`row-${_flag}`] = _nowRowSelectedInfo
      console.log('下拉选择完后获取到的id、code、name', _nowRowSelectedInfo, this.rowsSelectedInfo)
    },

    async getList() {
      // 获取税率
      await this.$API.masterData.getTaxItem().then((res) => {
        this.taxidData = res.data
        this.taxidData.forEach((item) => {
          item.taxRate = item.taxRate * 100
          item.showLabel = item.taxItemCode + '-' + item.taxRate + '%'
        })
      })

      //采购组
      // await this.$API.masterData
      //   .getbussinessGroup({
      //     groupTypeCode: "BG001CG",
      //   })
      //   .then((res) => {
      //     if (res && res.data.length) {
      //       this.buyerGroupOptions = res.data.map((item) => {
      //         return {
      //           text: item.groupName,
      //           value: item.groupCode,
      //           id: item.id,
      //         };
      //       });
      //     }
      //   });
      let editParam = {
        taxidData: this.taxidData,
        buyerGroupOptions: this.buyerGroupOptions
      }

      this.templateConfig[0].grid.columnData = [checkCol].concat(
        detailCol(editParam, this, this.path)
      )
      // this.$set(
      //   this.templateConfig[0].grid,
      //   "columnData",
      //   [checkCol].concat(detailCol(editParam, this, this.path))
      // );
    },

    // 点击 新增/删除
    handleClickToolBar(e) {
      if (e.toolbar.id == 'Add') {
        console.log(this.$refs.salePriceRef)
        this.$refs.salePriceRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (e.toolbar.id == 'Delete') {
        this.$refs.salePriceRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
      } else if (e.toolbar.id == 'endEdit') {
        this.$refs.salePriceRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.bottomGrid {
  flex: 1;
  /deep/ .mt-data-grid {
    flex: 1;
    .e-grid {
      height: 100%;
      display: flex;
      flex-direction: column;

      .e-headerchkcelldiv {
        padding-left: 0;
      }

      .e-gridcontent {
        flex: 1;
        .e-content {
          // height: 100% !important;
          flex-direction: row !important;
        }
      }
    }
  }
}
</style>
