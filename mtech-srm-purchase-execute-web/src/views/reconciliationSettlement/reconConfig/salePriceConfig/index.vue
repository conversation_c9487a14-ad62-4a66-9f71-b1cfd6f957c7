<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>

    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadSalePriceRef"
      :down-template-name="downTemplateName"
      :request-urls="requestUrls"
      :down-template-params="downTemplateParams"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { checkColumn, lastColumn, lastColumnDetail, lastColumnKt } from './config/index'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  data() {
    return {
      componentConfig: this.$route.path.includes('kt')
        ? [
            {
              useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
              useBaseConfig: true,
              title: this.$t('头视图'),

              toolbar: [
                {
                  id: 'Add',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('新增')
                  // permission: ["O_02_0433"],
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete',
                  title: this.$t('删除')
                  // permission: ["O_02_0435"],
                },
                {
                  id: 'upload',
                  icon: 'icon_solid_Import',
                  title: this.$t('导入')
                  // permission: ["O_02_1039"],
                },
                {
                  id: 'push',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('重推sap')
                  // permission: ["O_02_1039"],
                },
                {
                  id: 'Submit',
                  icon: 'icon_solid_Import',
                  title: this.$t('提交')
                }
                // {
                //   id: "outport",
                //   icon: "icon_solid_export",
                //   title: this.$t("导出"),
                //   // permission: ["O_02_0691"],
                // },
              ],
              // gridId:
              //   this.$tableUUID.reconciliationSettlement.salePPriceConfig.list,
              gridId: '022b170c-55ba-409c-b637-0d72ab82a053',
              grid: {
                columnData: this.$route.path.includes('kt')
                  ? checkColumn.concat(lastColumnKt)
                  : checkColumn.concat(lastColumn),
                asyncConfig: {
                  url: this.$route.path.includes('kt')
                    ? `${BASE_TENANT}/selling_price/queryAir`
                    : `${BASE_TENANT}/selling_price/query`
                },
                frozenColumns: 1
              }
            },
            {
              useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
              useBaseConfig: true,
              title: this.$t('明细视图'),
              toolbar: {
                tools: [
                  [
                    {
                      id: 'export1',
                      // icon: "icon_solid_Createorder",
                      // permission: ["O_02_1092"],
                      title: this.$t('导出')
                    }
                  ],
                  ['Filter', 'Refresh', 'Setting']
                ]
              },
              // gridId:
              //   this.$tableUUID.reconciliationSettlement.salePPriceConfig.list,
              gridId: 'da7ad0b4-c726-4c2a-9214-dbff57635c8f',
              grid: {
                columnData: checkColumn.concat(lastColumnDetail),
                asyncConfig: {
                  url: this.$route.path.includes('kt')
                    ? `${BASE_TENANT}/selling_price/queryItems`
                    : ``,
                  serializeList: (list) => {
                    let obj = []
                    list.forEach((item) => {
                      obj.push({ ...item.header, ...item })
                    })
                    return obj
                  }
                },
                frozenColumns: 1
              }
            }
          ]
        : [
            {
              useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
              useBaseConfig: true,
              title: this.$t('头视图'),

              toolbar: [
                {
                  id: 'Add',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('新增')
                  // permission: ["O_02_0433"],
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete',
                  title: this.$t('删除')
                  // permission: ["O_02_0435"],
                },
                {
                  id: 'upload',
                  icon: 'icon_solid_Import',
                  title: this.$t('导入')
                  // permission: ["O_02_1039"],
                }
                // {
                //   id: "outport",
                //   icon: "icon_solid_export",
                //   title: this.$t("导出"),
                //   // permission: ["O_02_0691"],
                // },
              ],
              // gridId:
              //   this.$tableUUID.reconciliationSettlement.salePPriceConfig.list,

              grid: {
                columnData: checkColumn.concat(lastColumn),
                asyncConfig: {
                  url: this.$route.path.includes('kt')
                    ? `${BASE_TENANT}/selling_price/queryAir`
                    : `${BASE_TENANT}/selling_price/query`
                },
                frozenColumns: 1
              }
            }
          ],
      // 上传excel的参数
      downTemplateName: this.$t('销售价格维护模板'),
      requestUrls: {},

      downTemplateParams: {
        pageFlag: false
      }
    }
  },
  methods: {
    handleClickToolBar(e) {
      console.log('方法1', e)
      console.log(e.gridRef.getMtechGridRecords(), e.toolbar.id)
      if (e.toolbar.id === 'export1') {
        let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let obj = JSON.parse(
          sessionStorage.getItem('da7ad0b4-c726-4c2a-9214-dbff57635c8f')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          let column = checkColumn.concat(lastColumnDetail) || []
          column.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        let params = {
          page: { current: 1, size: 10000 },
          rules: rule.rules || []
        }
        this.$store.commit('startLoading')

        this.$API.reconciliationSettlement.salePriceExport(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
      if (e.toolbar.id === 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        let selectedRecords = e.gridRef.getMtechGridRecords()
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        if (selectedRecords.find((i) => ![0, 1].includes(i.status))) {
          this.$toast({
            content: this.$t('请选择草稿/新建状态数据进行删除操作'),
            type: 'warning'
          })
          return
        }
        let ids = selectedRecords.map((i) => i.id)
        this.handleDelete(ids)
      } else if (e.toolbar.id == 'upload') {
        this.handleUpload()
      } else if (e.toolbar.id === 'push') {
        let selectedRecords = e.gridRef.getMtechGridRecords()
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        if (selectedRecords.find((i) => ![7].includes(i.status))) {
          this.$toast({
            content: this.$t('请选择sap推送失败的单子'),
            type: 'warning'
          })
          return
        }
        let ids = selectedRecords.map((i) => i.id)
        this.handlePush(ids)
      } else if (e.toolbar.id == 'outport') {
        this.handleExport()
      } else if (e.toolbar.id === 'Submit') {
        let selectRecords = e.grid.getSelectedRecords()
        if (!selectRecords.length) {
          this.$toast({ content: this.$t('请至少选择一行'), type: 'warning' })
          return
        }
        const ids = []
        for (let i = 0; i < selectRecords.length; i++) {
          const element = selectRecords[i]
          if (element.status !== 0 && element.status !== 1 && element.status !== 2) {
            this.$toast({
              content: this.$t('请选择状态为草稿、新增或修改的数据'),
              type: 'warning'
            })
            return
          }
          ids.push(element.id)
        }
        this.submit(ids)
      }
    },
    submit(ids) {
      this.$API.reconciliationSettlement.batchSubmit({ ids }).then(() => {
        this.$toast({ content: this.$t('提交成功'), type: 'success' })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    //获取附件数据
    getFileListData(data) {
      this.relativeFileList = { ...data }
    },
    handlePush(ids) {
      this.$API.reconciliationSettlement.pushSAP(ids).then(() => {
        this.$toast({ content: this.$t('重推成功'), type: 'success' })
        this.$refs.templateRef.refreshCurrentGridData()
      })
    },
    handleAdd() {
      //新增
      console.log('新增数据')
      if (this.$route.path.includes('kt')) {
        this.$router.push('sale-price-detail?type=add&ktType=kt')
      } else {
        this.$router.push('sale-price-detail?type=add')
      }
    },
    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
      if (e.tool.id == 'edit') {
        this.handleEdit(e.data.id)
      } else if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      }
    },
    handleClickCellTitle(e) {
      if (e.field == 'code') {
        if (this.$route.path.includes('kt')) {
          this.$router.push(`sale-price-detail?id=${e.data.id}&type=view&ktType=kt`)
        } else {
          this.$router.push(`sale-price-detail?id=${e.data.id}&type=view`)
        }
      }
    },

    handleEdit(id) {
      if (this.$route.path.includes('kt')) {
        this.$router.push(`sale-price-detail?id=${id}&type=edit&ktType=kt`)
      } else {
        this.$router.push(`sale-price-detail?id=${id}&type=edit`)
      }
    },

    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('确认删除？')
        },
        success: () => {
          this.$API.reconciliationSettlement.delSalePrice(ids).then(() => {
            this.$toast({ content: this.$t('删除成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },

    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadSalePriceRef.uploadData = [] // 清空数据
        this.$refs.uploadSalePriceRef.$refs.uploader.files = []
        this.$refs.uploadSalePriceRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadSalePriceRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },

    // 上传（显示弹窗）
    handleUpload() {
      this.requestUrls = {
        templateUrlPre: 'reconciliationSettlement',
        templateUrl: this.$route.path.includes('kt') ? 'downloadKtTemp' : 'downloadTemp',
        uploadUrl: this.$route.path.includes('kt') ? 'uploadKtEx' : 'uploadEx'
      }
      this.showUploadExcel(true)
    },

    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
      this.$refs.templateRef.refreshCurrentGridData()
    },

    // 导出
    handleExport() {
      let rules = this.$refs.templateRef.getCurrentUsefulRef()?.pluginRef?.queryBuilderRules
      let page = {
        size: 1000,
        current: 1
      }
      let params = rules
        ? {
            ...rules,
            page,
            pageFlag: true
          }
        : {
            pageFlag: true
          }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement.downloadTemp(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>

<style></style>
