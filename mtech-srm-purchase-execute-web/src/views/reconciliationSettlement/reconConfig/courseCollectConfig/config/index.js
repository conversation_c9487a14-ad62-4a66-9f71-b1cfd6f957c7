import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    field: 'enable',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('禁用'),
        1: i18n.t('启用')
      }
    },
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑')
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除')
      }
    ]
  },
  {
    field: 'companyName',
    headerText: i18n.t('公司')
  },
  {
    field: 'acctCode',
    headerText: i18n.t('汇总科目')
  },
  {
    field: 'acctDesc',
    headerText: i18n.t('汇总科目说明')
  },
  {
    field: 'subAcctCode',
    headerText: i18n.t('归集科目')
  },
  {
    field: 'subAcctDesc',
    headerText: i18n.t('归集科目说明')
  },
  {
    field: 'comments',
    headerText: i18n.t('备注')
  }
  // {
  //   field: "createUserName",
  //   headerText: i18n.t("创建人"),
  // },
  // {
  //   field: "createTime",
  //   headerText: i18n.t("创建时间"),
  //   valueConverter: {
  //     type: "function",
  //     filter: (e) => {
  //       if (e && !isNaN(e) && e.length == 13) {
  //         e = Number(e);
  //         return timeNumberToDate({
  //           formatString: "YYYY-mm-dd",
  //           value: e,
  //         });
  //       } else {
  //         return "-";
  //       }
  //     },
  //   },
  //   searchOptions: MasterDataSelect.dateRange,
  // },
]
