<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="addForm" :model="addForm" :rules="rules">
      <mt-form-item prop="companyCode" :label="$t('公司')">
        <select-filter
          :width="390"
          ref="companyRef"
          :fields="companyObj.fields"
          :request-url="companyObj.requestUrl"
          :request-key="companyObj.requestKey"
          :init-val.sync="addForm.companyCode"
          :other-params="companyObj.otherParams"
          :label-show-obj="companyObj.labelShowObj"
          :placeholder="$t('请选择公司')"
          :disabled="$route.query.type == 'view'"
          @handleChange="handleCompanyChange"
        ></select-filter>
      </mt-form-item>

      <mt-form-item prop="acctCode" :label="$t('汇总科目')">
        <mt-input v-model="addForm.acctCode" :show-clear-button="true" :maxlength="50"></mt-input>
      </mt-form-item>

      <mt-form-item prop="acctDesc" :label="$t('科目说明')">
        <mt-input v-model="addForm.acctDesc" :show-clear-button="true" :maxlength="50"></mt-input>
      </mt-form-item>
      <mt-form-item prop="subAcctCode" :label="$t('归集科目')">
        <mt-input
          v-model="addForm.subAcctCode"
          :show-clear-button="true"
          :maxlength="50"
        ></mt-input>
      </mt-form-item>

      <mt-form-item prop="subAcctDesc" :label="$t('归集科目说明')">
        <mt-input
          v-model="addForm.subAcctDesc"
          :show-clear-button="true"
          :maxlength="50"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="enable" :label="$t('是否启用')">
        <mt-switch
          v-model="addForm.enable"
          :on-label="$t('是')"
          :off-label="$t('否')"
          :active-value="1"
          :inactive-value="0"
        ></mt-switch>
      </mt-form-item>
      <mt-form-item prop="comments" :label="$t('备注')" class="full-width">
        <mt-input
          maxlength="200"
          v-model="addForm.comments"
          :show-clear-button="true"
          :placeholder="$t('备注')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  props: {
    dialogTitle: {
      type: String,
      default: () => ''
    },
    dialogType: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      businessTypeList: [],

      addForm: {
        id: '',
        enable: 0,
        companyCode: '',
        companyName: '',
        acctCode: '',
        acctDesc: '',
        subAcctCode: '',
        subAcctDesc: '',
        comments: ''
      },
      // 业务公司
      companyObj: {
        requestUrl: {
          pre: 'masterData',
          url: 'OrgFindSpecifiedChildrenLevelOrgs'
        },
        requestKey: 'fuzzyParam',
        otherParams: {
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true
        },
        fields: {
          text: 'labelShow',
          value: 'orgCode'
        },
        labelShowObj: {
          code: 'orgCode',
          name: 'orgName'
        }
      },
      rules: {
        companyCode: [
          {
            required: true,
            message: this.$t('请选择业务类型'),
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: this.$t('请输入对账类型名称'),
            trigger: 'blur'
          }
        ]
      },
      companyOptions: []
    }
  },
  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.addForm.clearValidate()
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },
    confirm() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          let params = {
            ...this.addForm
          }
          this.$API.reconciliationSettlement[
            this.dialogType === 'edit' ? 'updateCourseCollect' : 'saveCourseCollect'
          ](params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('handleAddDialogShow', false)
              this.$emit('confirmSuccess')
            }
          })
        }
      })
    },
    // 公司下拉改变
    handleCompanyChange(e) {
      console.log('公司改变aa', e)
      this.addForm.companyCode = e.itemData?.orgCode
      this.addForm.companyName = e.itemData?.orgName
    }
  }
}
</script>

<style></style>
