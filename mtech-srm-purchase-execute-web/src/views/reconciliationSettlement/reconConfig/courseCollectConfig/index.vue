<template>
  <!-- 采方-对账类型配置 -->
  <div class="full-height reconciliation-config">
    <mt-template-page
      ref="reconTypeRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />

    <add-dialog
      ref="addDialog"
      :dialog-title="dialogTitle"
      :dialog-type="type"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></add-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { BASE_TENANT } from '@/utils/constant'
import { columnData } from './config'
export default {
  components: {
    addDialog: require('./components/addDialog.vue').default
  },
  data() {
    return {
      pageConfig: [
        {
          toolbar: [
            {
              id: 'add',
              icon: 'icon_solid_Createorder',
              title: this.$t('新增'),
              permission: ['O_02_0426']
            },
            {
              id: 'delete',
              icon: 'icon_solid_Delete',
              title: this.$t('删除'),
              permission: ['O_02_0428']
            }
          ],
          gridId: this.$tableUUID.reconciliationSettlement.reconciliationTypeConfig.list,
          grid: {
            columnData: columnData,
            dataSource: [],
            frozenColumns: 1,
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliationSummaryAccount/queryList`
            },
            autoWidthColumns: columnData.length + 1
          }
        }
      ],

      dialogTitle: null,
      type: ''
    }
  },
  methods: {
    handleAddDialogShow(flag) {
      if (flag) {
        this.$refs.addDialog.$refs.dialog.ejsRef.show()
      } else {
        this.$refs.addDialog.$refs.dialog.ejsRef.hide()
      }
    },
    confirmSuccess() {
      this.$refs.reconTypeRef.refreshCurrentGridData()
    },

    handleClickToolBar(e) {
      let multpleBtnIds = [
        'add',
        'Filter',
        'Refresh',
        'Setting',
        'more-option-btn',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]
      if (e.gridRef.getMtechGridRecords().length <= 0 && !multpleBtnIds.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = [],
        _status = []
      e.gridRef.getMtechGridRecords().map((item) => {
        _id.push(item.id)
        _status.push(item.status)
      })
      if (e.toolbar.id == 'add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'delete') {
        this.handleDelete(_id)
      }
    },

    handleClickCellTool(e) {
      if (e.tool.id == 'edit') {
        this.handleAdd(e)
      } else if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      }
    },

    handleAdd(row) {
      this.$refs.addDialog.$refs.dialog.ejsRef.show()
      this.$refs.addDialog.$refs.addForm.resetFields()
      if (!row) {
        // 是新增
        this.dialogTitle = this.$t('新增')
        this.type = 'add'
      } else {
        // 编辑
        this.dialogTitle = this.$t('编辑')
        this.type = 'edit'
        this.$refs.addDialog.addForm = cloneDeep(row?.data)
      }
    },

    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('确认删除该对账类型配置？')
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.reconciliationSettlement
            .deleteCourseCollect({ idList: ids })
            .then((res) => {
              this.$store.commit('endLoading')
              if (res.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.confirmSuccess()
              }
            })
            .catch(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    }
  }
}
</script>

<style></style>
