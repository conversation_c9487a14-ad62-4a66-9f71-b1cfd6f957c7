<template>
  <div class="detail-wrap">
    <detail-top :top-info="topInfo"></detail-top>

    <div class="actions-box">
      <detail-tab :tab-list.sync="tabList" :active-tab.sync="activeTab"></detail-tab>

      <div class="btns-wrap">
        <mt-button @click.native="saveModuleConfig">{{ $t('保存') }}</mt-button>
      </div>
    </div>

    <detail-check-grid
      v-show="activeTab == 0"
      ref="checkGridRef"
      :active-tab="activeTab"
      :grid-data="gridData"
      @changeGridData="changeGridData"
    ></detail-check-grid>

    <detail-nav-check
      v-show="activeTab == 1"
      :active-tab="activeTab"
      :left-nav-list.sync="leftNavList"
    ></detail-nav-check>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'

export default {
  components: {
    detailTop: require('./components/detailTop.vue').default,
    detailTab: require('./components/detailTab.vue').default,
    detailCheckGrid: require('./components/detailCheckGrid.vue').default,
    detailNavCheck: require('./components/detailNavCheck.vue').default
  },

  data() {
    return {
      topInfo: {}, // 头部的信息
      tabList: [],
      activeTab: 0,
      gridData: [],
      leftNavList: []
    }
  },

  mounted() {
    this.getTopInfo()
  },

  methods: {
    // 获取头部数据
    getTopInfo() {
      this.$API.reconciliationSettlement.getDetailReconType(this.$route.query.id).then((res) => {
        this.topInfo = res?.data
        this.getConfig()
      })
    },
    // 获取配置
    getConfig() {
      let params = {
        businessTypeCode: this.topInfo?.businessTypeCode,
        reconciliationTypeCode: this.topInfo?.code
      }
      this.$API.reconciliationSettlement.postReconciliationConfigFieldQuery(params).then((res) => {
        const dataList = res?.data || []
        this.tabList = dataList.map((item) => {
          let fieldResponseList = item?.fieldResponseList.map((f, i) => {
            return {
              ...f,
              indexs: Number(i + 1)
            }
          })
          return {
            ...item,
            fieldResponseList: fieldResponseList
          }
        })

        this.gridData = this.tabList[0]?.fieldResponseList
        this.leftNavList = this.tabList[1]?.fieldResponseList
      })
    },

    // 点击tab名称
    handleClickTabTitle() {},

    // 保存
    saveModuleConfig() {
      let addList = []
      this.tabList[0].fieldResponseList = this.gridData
      if (this.tabList.length > 0) {
        this.tabList[1].fieldResponseList = this.leftNavList
      }

      this.tabList.forEach((tab) => {
        if (tab.checkStatus) {
          addList.push({
            code: tab.code,
            id: tab.id,
            name: tab.name
          })
        }
        if (tab.fieldResponseList && tab.fieldResponseList.length) {
          tab.fieldResponseList.forEach((i) => {
            if (i.checkStatus) {
              addList.push({
                code: i.code,
                id: i.id,
                name: i.name
              })
            }
          })
        }
      })
      let params = {
        businessTypeCode: this.topInfo?.businessTypeCode,
        businessTypeName: this.topInfo?.businessTypeName,
        reconciliationTypeCode: this.topInfo?.code,
        reconciliationTypeName: this.topInfo?.name,
        addList: addList
      }
      this.$API.reconciliationSettlement.saveReconConfig(params).then(() => {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$router.go(-1)
      })
    },

    changeGridData(data) {
      this.gridData = cloneDeep(data)
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-wrap {
  height: 100%;
  overflow: hidden;
  padding-top: 20px;
  padding-bottom: 10px;
  display: flex;
  flex-direction: column;

  .actions-box {
    width: 100%;
    display: flex;
    background: #fafafa;
    // position: absolute;
    z-index: 1;
    border-bottom: 1px solid #e0e0e0;

    /deep/ .config-custom-tabs {
      flex: 1;
    }

    .btns-wrap {
      display: flex;
      /deep/ .mt-button {
        button {
          width: 76px;
          height: 34px;
          background: rgba(255, 255, 255, 1);
          border: 1px solid rgba(0, 70, 156, 0.1);
          border-radius: 4px;
          box-shadow: unset;
          padding: 0;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
          margin-top: 8px;
          margin-right: 10px;
        }
      }
    }
  }

  /deep/ .detail-check-grid {
    flex: 1;
    overflow: hidden;

    .mt-data-grid {
      overflow: hidden;
      height: 100%;

      .e-grid {
        height: 100%;
        padding-bottom: 44px;

        .e-gridcontent {
          overflow-y: auto;
          height: 100%;
        }
      }
    }
  }
}
</style>
