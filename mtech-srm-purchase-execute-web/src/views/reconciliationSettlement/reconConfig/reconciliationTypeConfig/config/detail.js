import { i18n } from '@/main.js'
import Vue from 'vue'
export const columnData = [
  {
    width: '70',
    field: 'indexs',
    headerText: i18n.t('序号')
  },
  {
    width: '150',
    field: 'name',
    headerText: i18n.t('字段名称')
  },
  {
    width: '150',
    field: 'checkStatus',
    headerText: i18n.t('是否需要'),
    headerTemplate: () => {
      return {
        template: Vue.component('checkAll', {
          // template: `
          //       <div class="headers">
          //         <mt-checkbox class="checkbox-item"  v-model="checkAll"  @change="handleCheckAll" ></mt-checkbox>是否需要
          //       </div>
          //     `,
          template: `
                <div class="headers">
                 {{$t("是否需要")}}
                </div>
              `,
          data() {
            return {
              data: {},
              checkAll: false // 默认不全选
            }
          },
          methods: {
            handleCheckAll(e) {
              this.checkAll = e.checked
              this.$parent.$emit('handleCheckAll', {
                key: 'checkStatus',
                value: this.checkAll
              })
            }
          }
        })
      }
    },
    template: function () {
      return {
        template: Vue.component('actionOptions', {
          template: `<div class="action-boxs">
            <mt-checkbox class="checkbox-item"  v-model="data.checkStatus"  @change="handleChange" ></mt-checkbox>
          </div>`,
          data() {
            return { data: {} }
          },
          methods: {
            handleChange(e) {
              console.log('handleChange', e, this)
              this.data.checkStatus = e.checked
              this.$parent.$emit('handleChangeCellCheckBox', {
                index: this.data.index,
                key: 'checkStatus',
                value: this.data.checkStatus
              })
            }
          }
        })
      }
    }
  }
]
