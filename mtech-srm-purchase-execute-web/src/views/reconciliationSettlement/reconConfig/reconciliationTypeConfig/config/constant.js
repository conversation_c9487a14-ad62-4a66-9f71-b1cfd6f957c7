import { i18n } from '@/main.js'

// 是否默认 0-否；1-是
export const DefaultStatus = {
  no: 0, // 否
  yes: 1 // 是
}
// 是否默认
export const DefaultStatusText = {
  [DefaultStatus.no]: i18n.t('否'),
  [DefaultStatus.yes]: i18n.t('是')
}
// 是否默认 对应的 css class
export const DefaultStatusCssClass = {
  [DefaultStatus.no]: 'col-inactive', // 否
  [DefaultStatus.yes]: 'col-active' // 是
}
// 是否默认 Options
export const DefaultStatusOptions = [
  {
    // 否
    text: DefaultStatusText[DefaultStatus.no],
    value: DefaultStatus.no,
    cssClass: DefaultStatusCssClass[DefaultStatus.no]
  },
  {
    // 是
    text: DefaultStatusText[DefaultStatus.yes],
    value: DefaultStatus.yes,
    cssClass: DefaultStatusCssClass[DefaultStatus.yes]
  }
]
