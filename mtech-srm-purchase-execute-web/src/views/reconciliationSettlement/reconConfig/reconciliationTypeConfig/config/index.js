import { i18n } from '@/main.js'
import { DefaultStatusOptions } from './constant'
import { MasterDataSelect } from '@/utils/constant'
import { timeNumberToDate } from '@/utils/utils'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    // width: "150",
    field: 'code',
    headerText: i18n.t('对账类型编码'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'), // 草稿、审批拒绝可以重新编辑
        permission: ['O_02_0427']
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'), // 草稿、审批拒绝可以重新编辑
        permission: ['O_02_0428']
      }
    ]
  },
  {
    // width: "150",
    field: 'name',
    headerText: i18n.t('对账类型名称')
  },
  {
    // width: "150",
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    // width: "150",
    field: 'defaultStatus',
    headerText: i18n.t('是否默认'),
    valueConverter: {
      type: 'map',
      map: DefaultStatusOptions
    }
  },
  {
    // width: "150",
    field: 'description',
    headerText: i18n.t('描述')
  },
  {
    // width: "150",
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    // width: "150",
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  }
]
