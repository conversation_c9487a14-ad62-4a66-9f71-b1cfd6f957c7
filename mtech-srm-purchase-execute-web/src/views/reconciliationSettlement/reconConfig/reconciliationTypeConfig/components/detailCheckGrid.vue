<template>
  <div class="detail-check-grid">
    <mt-data-grid
      :allow-selection="false"
      :column-data="columnData"
      :data-source.sync="gridData"
      @handleChangeCellCheckBox="handleChangeCellCheckBox"
      @handleCheckAll="handleCheckAll"
    ></mt-data-grid>
  </div>
</template>

<script>
import { columnData } from '../config/detail'
export default {
  props: {
    gridData: {
      type: Array,
      default: () => []
    },
    activeTab: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      columnData
    }
  },

  methods: {
    handleChangeCellCheckBox(e) {
      // console.log("handleChangeCellCheckBox", e);
      let _gridData = this.gridData
      _gridData[e.index][e.key] = e.value
      // this.$emit("update:gridData", _gridData);
      this.$emit('changeGridData', _gridData)
    },

    handleCheckAll(e) {
      let _gridData = this.gridData
      _gridData.forEach((i) => {
        i[e.key] = e.value
      })
      console.log('_gridData', e, _gridData)
      // this.$emit("update:gridData", _gridData);
      // this.gridData = cloneDeep(_gridData);
      this.$emit('changeGridData', _gridData)
    }
  }
}
</script>

<style></style>
