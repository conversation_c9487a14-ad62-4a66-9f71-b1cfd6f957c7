<template>
  <div class="top-info">
    <div class="detail-info">
      <div class="name-wrap">
        <div class="first-line">
          <span class="code">{{ topInfo.code }}</span>
          <span class="tags">{{ topInfo.name }}</span>
        </div>
        <div class="second-line">
          <div class="cai-name">{{ topInfo.description || '-' }}</div>
        </div>
      </div>
      <div class="btns-wrap">
        <!-- <mt-button>{{ $t("保存") }}</mt-button> -->
        <mt-button @click.native="backToBusinessConfig">{{ $t('返回') }}</mt-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    topInfo: {
      type: Object,
      default: () => {}
    }
  },

  methods: {
    backToBusinessConfig() {
      this.$router.push('reconciliation-type-config')
    }
  }
}
</script>
<style lang="scss">
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.top-info {
  width: 100%;
  height: 80px;
  padding: 20px;
  justify-content: space-between;
  background: rgba(245, 248, 251, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 0 8px 0 0;
  .detail-info {
    padding: 0;
    display: flex;
    line-height: 1;

    .name-wrap {
      flex: 1;

      .first-line {
        display: flex;
        align-items: center;
        .code {
          font-size: 20px;
          font-family: DINAlternate;
          font-weight: bold;
          color: #292929;
        }
        .tags {
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: 500;
          padding: 4px;
          border-radius: 2px;
          margin-left: 10px;

          &-1 {
            color: rgba(237, 161, 51, 1);
            background: rgba(237, 161, 51, 0.1);
          }
          &-2 {
            color: #6386c1;
            background: rgba(99, 134, 193, 0.1);
          }
        }
      }

      .second-line {
        display: flex;
        align-items: center;
        margin-top: 10px;
        .cai-name {
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(41, 41, 41, 1);
          @extend .text-ellipsis;
        }
        .cai-desc {
          margin-left: 60px;
        }
        ul {
          display: flex;
          li {
            margin-left: 30px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(157, 170, 191, 1);

            .mt-icons {
              font-size: 12px;
            }
            span {
              vertical-align: text-bottom;
              margin-left: 4px;
              @extend .text-ellipsis;
            }
          }
        }
      }
    }
    .btns-wrap {
      /deep/ .mt-button {
        margin-right: 20px;
        button {
          width: 76px;
          height: 34px;
          background: rgba(255, 255, 255, 1);
          border: 1px solid rgba(0, 70, 156, 0.1);
          border-radius: 4px;
          box-shadow: unset;
          padding: 0;
          font-size: 14px;
          font-family: PingFangSC;
          font-weight: 500;
          color: rgba(0, 70, 156, 1);
        }
      }
    }
  }
}
</style>
