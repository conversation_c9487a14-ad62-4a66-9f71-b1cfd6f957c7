<template>
  <div class="left-nav-tabs">
    <ul class="nav-container mt-flex-direction-column">
      <li
        :class="['nav-item', { active: index == activeNav }]"
        v-for="(nav, index) in leftNavList"
        :key="'nav-item-' + index + '-' + index"
        @click="activeNav = index"
      >
        <div class="svg-option-item">
          <mt-icon
            @click.native="handleClickNavIcon(nav)"
            class="config-checkbox"
            :name="nav.checkStatus ? 'a-icon_MultipleChoice_on' : 'a-icon_MultipleChoice_off'"
          />
          <span @click="handleClickNavTitle(nav)">{{ nav.name }}</span>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    leftNavList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeNav: 0 //左侧第三层Tab，Active序号
    }
  },

  methods: {
    //左侧Nav点击，checkbox切换
    handleClickNavIcon(e) {
      console.log('handleClickNavIcon', e)
      //当前，左侧Tab未设置关于fixed的要求
      // let _fixed = e.fixed;
      // if (_fixed > 0) {
      //   this.$toast({ content: `'${e.text}'为固定项.`, type: "warning" });
      //   return;
      // } else {
      //   e.checkStatus = !e.checkStatus;
      // }
      e.checkStatus = !e.checkStatus
    },
    //左侧Nav Title点击，Tab切换
    handleClickNavTitle(e) {
      console.log('handleClickNavTitle', e)
    }
  }
}
</script>

<style lang="scss" scoped>
.left-nav-tabs {
  margin: 20px 30px;
  .nav-item {
    margin-bottom: 16px;
  }
  .svg-option-item {
    display: flex;
    .config-checkbox {
      &.mt-icon-a-icon_MultipleChoice_on {
        color: #00469c;
      }
      &.mt-icon-a-icon_MultipleChoice_off {
        color: #9daabf;
      }
    }
  }
}
</style>
