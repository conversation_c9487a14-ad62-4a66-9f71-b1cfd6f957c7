<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="addForm" :rules="rules">
      <mt-form-item prop="businessTypeId" :label="$t('业务类型')">
        <mt-select
          ref="businessRef"
          v-model="addForm.businessTypeId"
          :data-source="businessTypeList"
          :show-clear-button="true"
          :fields="{ text: 'itemName', value: 'id' }"
          :placeholder="$t('请选择业务类型')"
        ></mt-select>
      </mt-form-item>

      <mt-form-item prop="name" :label="$t('对账类型名称')">
        <mt-input v-model="addForm.name" :show-clear-button="true" :maxlength="50"></mt-input>
      </mt-form-item>

      <mt-form-item prop="defaultStatus" :label="$t('是否默认')">
        <mt-switch
          v-model="addForm.defaultStatus"
          :on-label="$t('是')"
          :off-label="$t('否')"
          :active-value="1"
          :inactive-value="0"
        ></mt-switch>
      </mt-form-item>

      <mt-form-item prop="description" :label="$t('描述')" class="full-width">
        <mt-input
          v-model="addForm.description"
          :show-clear-button="true"
          :multiline="true"
          :maxlength="200"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  props: {
    dialogTitle: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      businessTypeList: [],

      addForm: {
        businessTypeId: null,
        name: '',
        defaultStatus: 0, // 是否默认 默认为否
        description: ''
      },

      rules: {
        businessTypeId: [
          {
            required: true,
            message: this.$t('请选择业务类型'),
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: this.$t('请输入对账类型名称'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },

    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },

    getList() {
      // 获取业务类型列表
      this.$API.masterData.getDictCode({ dictCode: 'businessType' }).then((res) => {
        if (res.data && res.data.length) {
          this.businessTypeList = res.data
        }
      })
    },

    // 获取其他数据 code、name
    getOtherInfo(params) {
      // 业务类型
      params.businessTypeCode = null
      params.businessTypeName = null
      if (this.addForm.businessTypeId && this.$refs.businessRef) {
        let _data = this.$refs.businessRef.ejsRef.getDataByValue(this.addForm.businessTypeId)
        if (_data) {
          params.businessTypeCode = _data.itemCode
          params.businessTypeName = _data.itemName
        }
      }
      return params
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            ...this.addForm
          }
          params = this.getOtherInfo(params)

          this.$API.reconciliationSettlement.saveReconType(params).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('handleAddDialogShow', false)
              this.$emit('confirmSuccess')
            }
          })
        }
      })
    }
  }
}
</script>

<style></style>
