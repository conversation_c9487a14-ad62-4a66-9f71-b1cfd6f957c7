<template>
  <!-- 自动化配置 -->
  <div class="full-height">
    <!-- 右侧各种操作按钮 -->
    <!-- <div class="operateButton">
      <mt-button
        css-class="e-flat"
        v-show="editFlag"
        @click="changeEditFlag"
        :is-primary="true"
        >{{ $t("取消") }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        v-show="!editFlag"
        @click="changeEditFlag"
        :is-primary="true"
        v-permission="['O_02_0432']"
        >{{ $t("编辑") }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        v-show="editFlag"
        @click="saveConfig"
        :is-primary="true"
        >{{ $t("保存") }}</mt-button
      >
    </div> -->
    <mt-template-page
      ref="template-6"
      :template-config="pageConfig6"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      class="ml20"
    >
    </mt-template-page>
    <!-- 自动化配置弹窗 -->
    <add-automatic-config-dialog
      v-if="isAutomaticDialogShow"
      @handleAutomaticDialogShow="handleAutomaticDialogShow"
      :dialog-data="dialogData"
      @confirmSuccess="confirmSuccess"
    ></add-automatic-config-dialog>
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import * as config from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'

export default {
  components: {
    addAutomaticConfigDialog: require('./components/addAutomaticConfigDialog.vue').default,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  data() {
    return {
      dialogData: null,
      downTemplateParams: {
        flag: 0
      }, // 下载模板参数
      requestUrls: {
        templateUrlPre: 'bgConfig',
        templateUrl: 'downloadAutomationStrategy',
        uploadUrl: 'uploadAutomationStrategy'
      }, // 上传下载接口地址
      automaticDataOrigin: [], // 源数据（用于Tab切换前对比）
      isAutomaticDialogShow: false, // 是否显示弹窗
      pageConfig6: [
        {
          useToolTemplate: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          useBaseConfig: false,
          toolbar: [
            [
              {
                id: 'Add',
                icon: 'icon_solid_Createorder',
                title: this.$t('新增'),
                permission: ['O_02_1172']
              },
              {
                id: 'Delete',
                icon: 'icon_solid_Delete',
                title: this.$t('删除'),
                permission: ['O_02_1173']
              }
            ],
            [
              {
                id: 'download',
                icon: 'icon_solid_export',
                title: this.$t('导出'),
                permission: ['O_02_0694']
              },
              {
                id: 'upload',
                icon: 'icon_solid_Import',
                title: this.$t('导入'),
                permission: ['O_02_1174']
              }
            ]
          ],
          gridId: this.$tableUUID.reconciliationSettlement.automationConfig.list,
          grid: {
            allowPaging: false,
            // autoWidthColumns: config.automaticConfigColumn.length + 1,
            lineSelection: true,
            // frozenColumns: 1,
            columnData: config.automaticConfigColumn,
            asyncConfig: {
              url: `${BASE_TENANT}/orderAutomationStrategy/query`,
              methods: 'get',
              recordsPosition: 'data'
            }
          }
        }
      ]
    }
  },

  methods: {
    // 打开编辑弹窗
    handleEdit(data) {
      this.dialogData = {
        dialogType: 'edit',
        row: data
      }
      this.isAutomaticDialogShow = true
    },

    // 打开新增弹窗
    handleAdd() {
      this.isAutomaticDialogShow = true
      this.dialogData = {
        dialogType: 'add'
      }
    },

    // 新增/编辑成功
    confirmSuccess(params) {
      console.log('获取到的数据', params)

      this.$API.bgConfig.editOrderAutomation(params).then((res) => {
        if (res.code == 200) {
          this.$refs['template-6'].refreshCurrentGridData()
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
        }
      })
    },

    // 删除行
    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t('确定删除选中行?')
        },
        success: () => {
          this.$API.bgConfig.deleteOrderAutomation({ idList: ids }).then((res) => {
            if (res.code == 200) {
              this.$refs['template-6'].refreshCurrentGridData()
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
            }
          })
        }
      })
    },

    handleClickToolBar(e) {
      if (e.grid.getSelectedRecords().length <= 0 && e.toolbar.id == 'Delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      let ids = []
      e.grid.getSelectedRecords().map((item) => {
        ids.push(item.id)
      })
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(ids)
      } else if (e.toolbar.id == 'upload') {
        this.showUploadExcel(true)
      } else if (e.toolbar.id == 'download') {
        this.handleDownload()
      }
    },

    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      } else if (e.tool.id == 'edit') {
        this.handleEdit(e.data)
      }
    },

    handleAutomaticDialogShow(flag) {
      this.isAutomaticDialogShow = flag
    },

    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },

    // 导出
    handleDownload() {
      this.$store.commit('startLoading')
      this.$API.bgConfig.downloadAutomationStrategy({ flag: 1 }).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },

    // 上传成功后，获取到的数据
    upExcelConfirm() {
      this.$refs['template-6'].refreshCurrentGridData()
      this.showUploadExcel(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
    },

    sameTypeConfigCheck(arr) {
      var tempSet = new Set(arr)
      return tempSet.size !== arr.length
    },

    saveConfig() {
      if (config.automaticData.some((item) => item.supplierId == null)) {
        this.$toast({
          content: this.$t('供应商不能为空，请确认后再提交'),
          type: 'warning'
        })
        return
      }
      let tempArr = config.automaticData.map((ele) => `${ele.businessTypeId}!!!!${ele.supplierId}`)
      if (this.sameTypeConfigCheck(tempArr)) {
        this.$toast({
          content: this.$t('存在类型重复的配置，请确认后再提交'),
          type: 'warning'
        })
        return
      }
      this.$API.bgConfig.saveOrderAutomation(config.automaticData).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$store.commit('startLoading')
          this.getConfig()
        }
      })
    }
  }
}
</script>

<style></style>
