import { i18n } from '@/main.js'

// 自动化配置 用于提交的数据
export let automaticData = []

// 自动化配置 用于页面展示的数据
export let automaticDataSource = []

// 业务类型list
export let businessTypeList = []

// 供应商list
export let supplierTypeList = []

// 页面内容是否可编辑
export let editFlag = {
  isEditable: false
}

// 自动化配置
export const automaticConfigColumn = [
  {
    width: '200',
    field: 'serialNumber',
    headerText: i18n.t('序号'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        permission: ['O_02_0432']
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        permission: ['O_02_1173']
      }
    ]
  },
  {
    width: '220',
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
    // template: function () {
    //   return {
    //     template: Vue.component("actionOption", {
    //       template: `<div style="flex-direction: row; display: inline-flex;">
    //       <mt-select
    //             :width="140"
    //             :data-source="businessTypeList"
    //             :placeholder="$t('请选择')"
    //             @change="businessTypeChange"
    //             @focus="getBusinessConfig"
    //             :disabled="!isEditable"
    //             css-class="input-select"
    //             :fields="{ text: 'businessTypeName', value: 'businessTypeId' }"
    //             v-model="data.businessTypeId"
    //           ></mt-select>
    //       </div>`,
    //       data() {
    //         return { data: {}, businessTypeList: [], isEditable: null };
    //       },
    //       mounted() {
    //         this.businessTypeList = businessTypeList;
    //         this.isEditable = editFlag.isEditable;
    //       },
    //       methods: {
    //         // 获取业务类型下拉
    //         getBusinessConfig() {
    //           this.$API.bgConfig.getLocalBusinessTypeList({}).then((res) => {
    //             this.businessTypeList.length = 0;
    //             res.data.forEach((e) => {
    //               this.businessTypeList.push(e);
    //             });
    //           });
    //         },
    //         businessTypeChange(e) {
    //           if (e != null && e?.itemData) {
    //             automaticData[this.data.index].businessTypeCode =
    //               e.itemData.businessTypeCode;
    //             automaticData[this.data.index].businessTypeId =
    //               e.itemData.businessTypeId;
    //             automaticData[this.data.index].businessTypeName =
    //               e.itemData.businessTypeName;
    //             // automaticDataSource[this.data.index].purExecutionMethod = e.value;
    //           } else {
    //             automaticData[this.data.index].businessTypeCode = null;
    //             automaticData[this.data.index].businessTypeId = null;
    //             automaticData[this.data.index].businessTypeName = null;
    //             // automaticDataSource[this.data.index].purExecutionMethod = null;
    //           }
    //         },
    //       },
    //     }),
    //   };
    // },
  },
  {
    width: '450',
    field: 'supplierName',
    headerText: i18n.t('供应商')
    // template: function () {
    //   return {
    //     template: Vue.component("actionOption", {
    //       template: `<div style="flex-direction: row; display: inline-flex;">
    //       <select-filter
    //         :width="360"
    //         :disabled="!isEditable"
    //         :fields="{ text: 'labelShow', value: 'supplierCode' }"
    //         :request-url="requestUrl"
    //         :request-key="requestKey"
    //         :labelShowObj="labelShowObj"
    //         :init-val.sync="data.supplierCode"
    //         @handleChange="handleChange"
    //       ></select-filter>
    //       </div>`,

    //       data() {
    //         return {
    //           data: {},
    //           supplierTypeList: [],
    //           getSupplierTimer: null,
    //           isEditable: null,
    //           requestUrl: {
    //             pre: "masterData",
    //             url: "getSupplier",
    //           },
    //           requestKey: "fuzzyNameOrCode",
    //           labelShowObj: {
    //             code: "supplierCode",
    //             name: "supplierName",
    //           },
    //         };
    //       },
    //       mounted() {
    //         this.isEditable = editFlag.isEditable;
    //       },
    //       methods: {
    //         // 供应商下拉修改
    //         handleChange(e) {
    //           console.log("供应商下拉修改", e);
    //           automaticData[this.data.index].supplierCode =
    //             e.itemData?.supplierCode;
    //           automaticData[this.data.index].supplierName =
    //             e.itemData?.supplierName;
    //           automaticData[this.data.index].supplierId = e.itemData?.id;
    //         },
    //       },
    //     }),
    //   };
    // },
  },
  {
    width: 'auto',
    field: 'accountReconciliationAutoConfirm',
    headerText: i18n.t('对账单自动确认'),
    valueAccessor: (field, data) => {
      return data.strategyConfigMap.accountReconciliationAutoConfirm == 1
        ? i18n.t('是')
        : i18n.t('否')
    }
    // template: function () {
    //   return {
    //     template: Vue.component("actionOption", {
    //       template: `<div style="flex-direction: row; display: inline-flex;">
    //       <mt-checkbox v-model="data.strategyConfigMap.accountReconciliationAutoConfirm  == 1 ? true : false"
    //       :disabled="!isEditable" :class="[!isEditable && 'checkbox-checkeditem']"
    //       @change="handleChangeCellCheckBox" cssClass="time-range-checkbox" ></mt-checkbox>
    //       </div>`,
    //       data() {
    //         return { data: {}, isEditable: null };
    //       },
    //       mounted() {
    //         this.isEditable = editFlag.isEditable;
    //       },
    //       methods: {
    //         handleChangeCellCheckBox(e) {
    //           // this.data.accountReconciliationAutoConfirm = e.checked;
    //           automaticData[
    //             this.data.index
    //           ].strategyConfigMap.accountReconciliationAutoConfirm = e.checked
    //             ? 1
    //             : 0;
    //           // automaticDataSource[
    //           //   this.data.index
    //           // ].accountReconciliationAutoConfirm = e.checked ? "1" : "0";
    //         },
    //       },
    //     }),
    //   };
    // },
  }
]
