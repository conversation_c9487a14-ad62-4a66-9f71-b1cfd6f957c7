import { i18n } from '@/main.js'
import Vue from 'vue'

// 定时同步 用于提交的数据
export let timingSyncData = []

// 定时同步 用于页面展示的数据
export let timingSyncDataSource = []

// 租户员工list
export let userList = []

// 定时同步配置的周数据（用于计算二进制转换）
export const weekData = [
  { text: i18n.t('周一'), value: 1 },
  { text: i18n.t('周二'), value: 10 },
  { text: i18n.t('周三'), value: 100 },
  { text: i18n.t('周四'), value: 1000 },
  { text: i18n.t('周五'), value: 10000 },
  { text: i18n.t('周六'), value: 100000 },
  { text: i18n.t('周日'), value: 1000000 }
]

// 定时同步配置
export const timingSyncColumn = [
  {
    width: '200',
    field: 'serialNumber',
    headerText: i18n.t('序号'),
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        permission: ['O_02_0430']
        // visibleCondition: () => editFlag.isEditable,
      },
      {
        id: 'delete',
        icon: 'icon_Delete',
        title: i18n.t('删除'),
        permission: ['O_02_0431']
        // visibleCondition: () => editFlag.isEditable,
      }
    ]
  },
  {
    width: '220',
    field: 'title',
    headerText: i18n.t('同步名称')
  },
  {
    width: '180',
    field: 'loopStatus',
    headerText: i18n.t('是否循环'),
    valueConverter: {
      type: 'map',
      //0-不循环，1. 循环
      map: {
        0: i18n.t('不循环'),
        1: i18n.t('循环')
      }
    }
  },
  {
    width: '180',
    field: 'loopType',
    headerText: i18n.t('循环周期'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div  v-if="this.data.loopStatus==1" style="flex-direction: row; display: inline-flex;">
            <span v-if="this.data.loopStatus==1" style="margin-right:10px;">{{loopType}}</span>
          </div>`,
          data() {
            return { data: {} }
          },
          computed: {
            loopType() {
              if (this.data?.loopType) {
                return this.data.loopType == 1 ? i18n.t('按天') : i18n.t('按周')
              } else {
                return null
              }
            }
          },
          methods: {}
        })
      }
    }
  },
  {
    width: '300',
    field: 'time',
    headerText: i18n.t('同步时间'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div  v-if="this.data.loopStatus==1" style="flex-direction: row; display: inline-flex;">
            <span v-if="this.data.loopType==2" style="margin-right:10px;">{{weekData}}</span>
            <span>{{time}}</span>
          </div>`,
          data() {
            return { data: {} }
          },
          computed: {
            time() {
              if (this.data?.time) {
                return this.data.time.split(' ')[1].substr(0, 5)
              } else {
                return null
              }
            },
            weekData() {
              if (this.data?.week) {
                let selectedDay = Number(this.data.week).toString(2).split('').reverse()
                let _tempDay = []
                selectedDay.forEach((e, i) => {
                  if (e * 10 ** i) {
                    _tempDay.push(e * 10 ** i)
                  }
                })
                let tempArr = this.data.weekData.map((item) => item.value)
                let weekData = []
                _tempDay.forEach((e) => {
                  if (tempArr.indexOf(e) > -1) {
                    weekData.push(this.data.weekData[tempArr.indexOf(e)].text)
                  }
                })
                return weekData.toString().replaceAll(',', '/')
              } else {
                return null
              }
            }
          },
          methods: {}
        })
      }
    }
  },
  {
    width: '300',
    field: 'reminder',
    headerText: i18n.t('同步失败提醒人'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: `<div style="flex-direction: row; display: inline-flex;">
            <span>{{reminderData}}</span>
          </div>`,
          data() {
            return { data: {} }
          },
          computed: {
            reminderData() {
              let tempArr = this.data.userList.map((item) => item.employeeId)
              let reminderData = []
              this.data.reminder.split(',').forEach((e) => {
                if (tempArr.indexOf(e) > -1) {
                  reminderData.push(this.data.userList[tempArr.indexOf(e)].text)
                }
              })
              return reminderData.toString().replaceAll(',', '，')
            }
          }
        })
      }
    }
  }
]
