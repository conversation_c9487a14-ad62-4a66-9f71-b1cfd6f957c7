// 定时同步配置弹窗
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <div class="dialog-content">
      <mt-form ref="ruleForm" :model="timingSyncObject" :rules="dialogRules">
        <mt-form-item prop="type" :label="$t('同步名称')">
          <mt-select
            v-model="timingSyncObject.type"
            :data-source="orderScheduleNameList"
            :placeholder="$t('请选择')"
            @change="titleChange"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="loopStatus" :label="$t('是否循环：')">
          <mt-radio
            v-model="timingSyncObject.loopStatus"
            :data-source="radioData"
            @change="loopStatusChange"
          ></mt-radio>
        </mt-form-item>

        <mt-form-item
          prop="loopType"
          :label="$t('循环周期')"
          v-if="timingSyncObject.loopStatus == '1'"
        >
          <mt-select
            v-model="timingSyncObject.loopType"
            :data-source="loopTypeList"
            @change="loopTypeChange"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item
          prop="weekLoopRule"
          :label="$t('同步时间')"
          v-if="timingSyncObject.loopStatus == '1'"
        >
          <span v-show="false">{{ this.timingSyncObject.weekLoopRule }}</span>
          <div v-if="timingSyncObject.loopType == '1'">
            <mt-time-picker
              v-model="timingSyncObject.time"
              @change="loopTimeChange"
              :placeholder="$t('请选择')"
            ></mt-time-picker>
          </div>
          <div v-if="timingSyncObject.loopType == '2'">
            <mt-multi-select
              ref="weekDayRef"
              v-model="weekDay"
              @change="weekDayChange"
              :data-source="weekData"
              :placeholder="$t('请选择')"
              :show-clear-button="true"
              width="190"
              class="loop-time"
            ></mt-multi-select>
            <mt-time-picker
              v-model="timingSyncObject.time"
              @change="loopTimeChange"
              :placeholder="$t('请选择')"
              width="190"
            ></mt-time-picker>
          </div>
        </mt-form-item>

        <mt-form-item prop="reminder" :label="$t('同步失效提醒人')">
          <mt-multi-select
            v-model="timingSyncObject.reminder"
            :data-source="userList"
            :allow-filtering="true"
            :placeholder="$t('请选择')"
            :fields="{ text: 'text', value: 'employeeId' }"
            width="820"
          ></mt-multi-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import UTILS from '@/utils/utils'
import { cloneDeep } from 'lodash'
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      orderScheduleNameList: [],
      weekDay: null,
      loopTime: null,
      userList: [],
      loopTypeList: [
        { text: this.$t('按天'), value: 1 },
        { text: this.$t('按周'), value: 2 }
      ],
      radioData: [
        {
          label: this.$t('不循环'),
          value: '0'
        },
        {
          label: this.$t('循环'),
          value: '1'
        }
      ],
      weekData: [
        { text: this.$t('周一'), value: 1 },
        { text: this.$t('周二'), value: 10 },
        { text: this.$t('周三'), value: 100 },
        { text: this.$t('周四'), value: 1000 },
        { text: this.$t('周五'), value: 10000 },
        { text: this.$t('周六'), value: 100000 },
        { text: this.$t('周日'), value: 1000000 }
      ],
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      dialogTitle: '',

      timingSyncObject: {
        title: '',
        type: null,
        loopStatus: '0',
        loopType: 1,
        week: null,
        time: null,
        reminder: null,
        weekLoopRule: null
      },
      dialogRules: {
        type: [
          {
            required: true,
            message: this.$t('请选择同步名称'),
            trigger: 'blur'
          }
        ],
        reminder: [
          {
            required: true,
            message: this.$t('请选择同步失效提醒人'),
            trigger: 'blur'
          }
        ],
        loopTime: [
          {
            required: true,
            message: this.$t('请选择同步时间'),
            trigger: 'blur'
          }
        ],
        // 同步时间必填校验
        weekLoopRule: [
          {
            required: true,
            message: this.$t('请选择同步时间'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.getInitList()
  },
  methods: {
    async getInitList() {
      await this.getUser()
      await this.getOrderScheduleName()
      this.$refs['dialog'].ejsRef.show()
      this.$refs.ruleForm.resetFields()
      if (this.dialogData?.row) {
        let _addForm = JSON.parse(JSON.stringify(this.dialogData?.row))
        _addForm.useApproval = _addForm.enableStatus == 1
        delete _addForm.serialNumber
        delete _addForm.weekData
        delete _addForm.userList

        this.timingSyncObject = {
          ...this.timingSyncObject,
          ..._addForm,
          loopStatus: _addForm.loopStatus.toString(),
          reminder: _addForm.reminder.split(','),
          type: _addForm.type.toString()
        }
        // 处理二进制形式存储的星期信息
        if (this.timingSyncObject?.week) {
          let selectedDay = this.timingSyncObject.week.toString(2).split('').reverse()
          let _tempDay = []
          selectedDay.forEach((e, i) => {
            if (e * 10 ** i) {
              _tempDay.push(e * 10 ** i)
            }
          })
          this.weekDay = _tempDay
        }
        this.dialogTitle = this.$t('编辑')
      } else {
        this.dialogTitle = this.$t('新增')
      }
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
      //状态为编辑且需要循环
      if (this.dialogData.dialogType === 'edit' && this.timingSyncObject.loopStatus == '1') {
        this.timingSyncObject.weekLoopRule = 'isRequired'
      }
    },

    // 获取同步名称
    async getOrderScheduleName() {
      this.orderScheduleNameList.length = 0
      await this.$API.bgConfig.getOrderScheduleName().then((res) => {
        for (var e in res.data) {
          this.orderScheduleNameList.push({
            text: res.data[e],
            value: e
          })
        }
      })
    },

    handleClose() {
      this.$emit('handleTimingSyncDialogShow', false)
    },

    async getUser() {
      // 获取 员工列表
      await this.$API.masterData.getCurrentTenantEmployees().then((res) => {
        res.data.forEach((item) => {
          this.userList.push({
            ...item,
            text:
              item.companyOrgName +
              '-' +
              item.departmentOrgName +
              '-' +
              item.employeeCode +
              '-' +
              item.employeeName
          })
        })
      })
    },

    // 循环周期变更
    loopTypeChange(e) {
      // 按天时清空周信息
      if (
        (e.itemData.value == '1' && this.timingSyncObject?.time) ||
        (e.itemData.value == '2' && this.timingSyncObject?.time && this.timingSyncObject?.week)
      ) {
        // 根据时间字段有无更新同步时间必填校验

        this.timingSyncObject.weekLoopRule = 'isRequired'
      } else {
        this.timingSyncObject.weekLoopRule = null
      }
    },

    // 是否循环变更
    loopStatusChange(e) {
      // 循环设定默认按天
      if (e == '1') {
        this.timingSyncObject.loopType = 1
        this.timingSyncObject.time = null
        this.timingSyncObject.week = null
        this.weekDay = null
        this.timingSyncObject.weekLoopRule = null
      }
    },
    // 选中星期变更
    weekDayChange(e) {
      let _week = 0
      e.value.forEach((e) => {
        _week = _week + e
      })
      this.timingSyncObject.week = parseInt(_week, 2)
      // 根据星期和时间有无更新同步时间必填校验
      if (this.timingSyncObject?.week && this?.timingSyncObject.time) {
        this.timingSyncObject.weekLoopRule = 'isRequired'
      } else {
        this.timingSyncObject.weekLoopRule = null
      }
    },
    // 定时时间变更
    loopTimeChange(e) {
      // 时间有无
      if (e) {
        if (
          this.timingSyncObject.loopType == 1 ||
          (this.timingSyncObject.loopType == 2 && this.timingSyncObject?.week)
        )
          this.timingSyncObject.weekLoopRule = 'isRequired'
      } else {
        this.timingSyncObject.weekLoopRule = null
      }
    },

    titleChange(e) {
      let _arr = this.orderScheduleNameList.filter((item) => item.value == e.value)
      this.timingSyncObject.title = _arr[0].text
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid && this.dialogData.dialogType === 'add') {
          let _obj = cloneDeep(this.timingSyncObject)
          delete _obj.weekLoopRule
          _obj = {
            ..._obj,
            type: Number(_obj.type),
            loopStatus: Number(_obj.loopStatus),
            reminder: _obj.reminder.toString(),
            time: UTILS.dateFormat(_obj.time).toString()
          }
          this.$API.bgConfig.addOrderScheduleStrategy(_obj).then((res) => {
            if (res.code === 200) {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.$store.commit('startLoading')
              this.$parent.getConfig()
              this.handleClose()
            }
          })
        } else if (valid && this.dialogData.dialogType === 'edit') {
          let _obj = cloneDeep(this.timingSyncObject)
          delete _obj.weekLoopRule
          _obj = {
            ..._obj,
            type: Number(_obj.type),
            loopStatus: Number(_obj.loopStatus),
            reminder: _obj.reminder.toString(),
            time: UTILS.dateFormat(_obj.time).toString()
          }
          this.$API.bgConfig.editOrderScheduleStrategy(_obj).then((res) => {
            if (res.code === 200) {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.$store.commit('startLoading')
              this.$parent.getConfig()
              this.handleClose()
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
  /deep/span.e-input-group {
    padding: 0;
  }
  /deep/.process-desc {
    width: 820px !important;
  }
  /deep/.tree-view-container {
    box-shadow: inset 0 0 0 1px rgba(232, 232, 232, 1);
    width: 100%;
    position: absolute;
    left: 0;
    top: 50px;
    z-index: 2;
    background: #fff;
    height: 200px;
    overflow: auto;
  }
  /deep/.loop-time {
    margin-right: 10px;
  }
}
</style>
