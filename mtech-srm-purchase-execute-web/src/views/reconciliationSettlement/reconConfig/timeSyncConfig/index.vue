// 定时同步配置
<template>
  <div class="full-height">
    <!-- 右侧各种操作按钮 -->
    <!-- <div class="operateButton">
      <mt-button
        css-class="e-flat"
        v-show="editFlag"
        @click="changeEditFlag(false)"
        :is-primary="true"
        >{{ $t("取消") }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        v-show="!editFlag"
        @click="changeEditFlag(true)"
        :is-primary="true"
        >{{ $t("编辑") }}</mt-button
      >
    </div> -->
    <mt-template-page
      ref="template7"
      :template-config="pageConfig7"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    >
    </mt-template-page>
    <!-- 定时同步弹窗 -->
    <add-timing-sync-dialog
      v-if="isTimingSyncDialogShow"
      @handleTimingSyncDialogShow="handleTimingSyncDialogShow"
      :dialog-data="dialogData"
    ></add-timing-sync-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import * as config from './config'
export default {
  components: {
    addTimingSyncDialog: require('./components/addTimingSyncDialog.vue').default
  },
  data() {
    return {
      editFlag: true, //是否可编辑
      dialogData: null,
      timingSyncDataOrigin: [], // 源数据（用于Tab切换前对比）
      isTimingSyncDialogShow: false, // 是否显示弹窗
      pageConfig7: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: [
            [
              {
                icon: 'icon_solid_Createorder',
                id: 'Add',
                title: this.$t('新增'),
                permission: ['O_02_0429']
              },
              {
                icon: 'icon_solid_Delete',
                id: 'Delete',
                title: this.$t('删除'),
                permission: ['O_02_0431']
              }
            ]
          ],
          gridId: this.$tableUUID.reconciliationSettlement.timeSyncConfig.list,
          grid: {
            allowPaging: false,
            // autoWidthColumns: 7,
            frozenColumns: 1,
            lineSelection: true,
            columnData: config.timingSyncColumn,
            dataSource: config.timingSyncDataSource
          }
        }
      ]
    }
  },
  mounted() {
    this.getConfig()
  },
  methods: {
    handleTimingSyncDialogShow(flag) {
      this.isTimingSyncDialogShow = flag
    },
    handleEdit(data) {
      this.dialogData = {
        dialogType: 'edit',
        row: data
      }
      this.isTimingSyncDialogShow = true
    },
    handleAdd() {
      this.isTimingSyncDialogShow = true
      this.dialogData = {
        dialogType: 'add'
      }
    },
    handleDelete(id) {
      this.$dialog({
        data: {
          title: this.$t('确认'),
          message: this.$t('确定删除选中行?')
        },
        success: () => {
          this.$API.bgConfig.delOrderScheduleStrategy({ idList: id }).then((res) => {
            if (res.code === 200) {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.getConfig()
            }
          })
        }
      })
    },

    // 获取 员工列表
    async getCurrentTenantEmployees() {
      await this.$API.masterData.getCurrentTenantEmployees().then((res) => {
        res.data.forEach((item) => {
          config.userList.push({
            ...item,
            text: `${item.companyOrgName}-${item.departmentOrgName}-${item.employeeCode}-${item.employeeName}`
          })
        })
      })
    },

    // 定时同步配置
    async getConfig() {
      await this.getCurrentTenantEmployees()
      await this.$API.bgConfig
        .getOrderScheduleStrategy({
          page: {
            current: 1,
            size: 10000
          },
          defaultRules: [
            {
              field: 'abolished',
              operator: 'equal',
              value: 0
            }
          ]
        })
        .then((res) => {
          this.$store.commit('endLoading')
          this.timingSyncDataOrigin = cloneDeep(res.data.records)
          config.timingSyncDataSource.length = 0
          config.timingSyncData.length = 0
          res.data.records.forEach((e, i) => {
            config.timingSyncData.push(cloneDeep(e))
            let tempMap = {
              ...e,
              serialNumber: i + 1,
              weekData: config.weekData,
              userList: config.userList
            }
            config.timingSyncDataSource.push(cloneDeep(tempMap))
          })
          // console.log(
          //   "获取到的timingSyncDataSource",
          //   config.timingSyncDataSource
          // );
          this.$set(this.pageConfig7[0].grid, 'dataSource', cloneDeep(config.timingSyncDataSource))
        })
    },

    handleClickToolBar(e) {
      if (e.grid.getSelectedRecords().length <= 0 && e.toolbar.id == 'Delete') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      let _id = []
      e.grid.getSelectedRecords().map((item) => {
        _id.push(item.id)
      })
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      } else if (e.toolbar.id == 'Delete') {
        this.handleDelete(_id)
      }
    },

    handleClickCellTool(e) {
      if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      } else if (e.tool.id == 'edit') {
        this.handleEdit(e.data)
      }
    }

    // changeEditFlag(flag) {
    //   console.log([config.editToolbar]);
    //   this.editFlag = flag;
    //   config.editFlag.isEditable = flag;
    //   this.$set(
    //     this.pageConfig7[0].toolbar,
    //     "tools",
    //     flag ? [config.editToolbar] : [[]]
    //   );
    // },
  }
}
</script>

<style></style>
