<template>
  <!-- 创建往来对账单-采方 -->
  <div class="full-height pt20 vertical-flex-box">
    <!-- 头部信息 -->
    <top-info
      class="flex-keep"
      ref="topInfoRef"
      :header-info="headerInfo"
      @doSubmit="doSubmit"
      @goBack="goBack"
      @doExpand="doExpand"
    ></top-info>

    <mt-tabs
      class="flex-keep"
      :e-tab="false"
      :data-source="tabList"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <!-- 金额汇总 -->
    <div class="flex-fit summary" v-show="currentTabInfo.code === Tab.summary">
      <div class="box">
        <div class="title">
          <span>{{ $t('对账科目') }}</span>
        </div>
        <div class="tips">
          <mt-icon class="icon" name="icon_card_rank_second" />
          <span>{{ $t('对账总差异（元）：') }}{{ '-' }}</span>
        </div>
        <div class="row-box">
          <div class="item-box">
            <div class="top">
              <div class="item">
                <div>{{ $t('对账单位') }}</div>
                <mt-input
                  v-model="headerInfo.companyName"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
              <div class="item">
                <div>{{ $t('期初余额') }}</div>
                <mt-input
                  v-model="headerInfo.openingBalance"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
              <div class="item">
                <div>{{ $t('期末余额') }}</div>
                <mt-input
                  v-model="headerInfo.closingBalance"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
            </div>
            <div class="table-box">
              <mt-data-grid
                :data-source="accountCategorySummary"
                :column-data="summaryColumns"
              ></mt-data-grid>
            </div>
          </div>
          <div class="dividing-line"></div>
          <div class="item-box empty">
            <svg-icon icon-class="empty-data"></svg-icon>
            <div>{{ $t('供方对账科目暂无数据') }}</div>
          </div>
        </div>
        <div class="title">
          <span>{{ $t('差异调整项') }}</span>
        </div>
        <div class="row-box">
          <div class="item-box empty">
            <svg-icon icon-class="empty-data"></svg-icon>
            <div>{{ $t('采方差异调整项暂无数据') }}</div>
          </div>
          <div class="dividing-line"></div>
          <div class="item-box empty">
            <svg-icon icon-class="empty-data"></svg-icon>
            <div>{{ $t('供方差异调整项暂无数据') }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 采方-往来明细 -->
    <div class="flex-fit" v-show="currentTabInfo.code === Tab.purchaseDetails">
      <mt-template-page
        ref="templateRef"
        class="grid-wrap-not-paging"
        :template-config="componentConfig"
        @handleClickToolBar="handleClickToolBar"
      />
    </div>
    <!-- 相关附件 -->
    <div class="flex-fit" v-show="currentTabInfo.code === Tab.file">
      <relative-file ref="relativeFileRef" :module-file-list="moduleFileList"></relative-file>
    </div>
  </div>
</template>

<script>
import {
  Tab,
  TabList,
  ToBeReconciledDetailsColumnData,
  CleanStatus,
  EntryType,
  SummaryColumns
} from './config/constant'
import { formatTableColumnData, formatUploadFiles } from './config/index'
import { purchaseDetailsDataSource } from './config/variable'
import bigDecimal from 'js-big-decimal'

export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue'),
    RelativeFile: () => import('@/components/businessComponents/relativeFileNoDocId/index.vue')
  },
  data() {
    const createData = JSON.parse(localStorage.getItem('createContactReconciledData')) || {}
    const lastTabIndex = JSON.parse(localStorage.getItem('lastTabIndex')) ?? 0
    let headerInfo = {}
    if (createData?.headerInfo) {
      const accountDate = createData.headerInfo.accountDate // 入账日期
      headerInfo = {
        ...createData.headerInfo,
        accountDate: accountDate ? new Date(Number(accountDate)) : null
      }
    }
    return {
      Tab,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      createData, // 创建参数
      headerInfo, // 头部信息
      entryType: createData?.entryType, // 页面状态 待创建明细勾选进入、待创建点击行进入
      lastTabIndex, // 上一页面的 tabIndex，返回时传递参数
      currentTabInfo: TabList[0], // 当前tab的数据，默认选中第一项
      tabList: TabList,
      accountCategorySummary: [], // 科目类别汇总
      moduleFileList: [
        // 采方-整单附件
        // {
        //   id: "purchaseMain",
        //   nodeName: this.$t("采方-整单附件"),
        //   type: nodeType.mainUpdate,
        // },
      ],
      summaryColumns: SummaryColumns,
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 使用组件中的toolbar配置
          toolbar: [
            // [
            //   // {
            //   //   id: "remove",
            //   //   icon: "icon_table_remove",
            //   //   title: this.$t("移除"),
            //   // },
            // ],
          ],
          grid: {
            editSettings: {
              allowEditing: false
            },
            allowPaging: false, // 不分页
            lineSelection: 0,
            lineIndex: 1,
            columnData: formatTableColumnData({
              tab: Tab.purchaseDetails,
              data: ToBeReconciledDetailsColumnData
            }),
            dataSource: purchaseDetailsDataSource,
            frozenColumns: 1
          }
        }
      ]
    }
  },
  mounted() {
    // 设置页面数据
    this.setPageData()
  },
  beforeDestroy() {
    // localStorage.removeItem("lastTabIndex");
    // localStorage.removeItem("createContactReconciledData");
  },
  methods: {
    // ToolBar
    handleClickToolBar(args) {
      const { grid, toolbar } = args
      const selectRows = grid.getSelectedRecords()
      const commonToolbar = [
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]

      if (selectRows.length === 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const selectedIds = []
      selectRows.forEach((item) => {
        selectedIds.push(item.id)
      })

      if (toolbar.id == 'remove') {
        // 移除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认移除选中的数据？')
          },
          success: () => {
            // 移除行
            selectedIds.forEach((id) => {
              for (let i = 0; i < purchaseDetailsDataSource.length; i++) {
                if (purchaseDetailsDataSource[i].id === id) {
                  purchaseDetailsDataSource.splice(i, 1)
                  break
                }
              }
            })
            // 更新 科目类别汇总，计算期末余额
            this.typeSummaryAndCalculateClosingBalance(purchaseDetailsDataSource)
          }
        })
      }
    },
    // 设置页面数据
    setPageData() {
      // 通过缓存数据获取期初期末余额的数据
      const _preSave = localStorage.getItem('preSaveData')
        ? JSON.parse(localStorage.getItem('preSaveData'))
        : null
      // 取期初余额
      this.headerInfo.openingBalance = _preSave?.openingBalance
      if (this.entryType === EntryType.byDetails) {
        // 当从待对账明细勾选创建跳转过来时
        const purchaseDetailsList = this.createData.purchaseDetailsList || [] // 采方明细表格数据
        this.setPurchaseDetailsListByDetails(purchaseDetailsList)
        // 科目类别汇总，计算期末余额
        this.typeSummaryAndCalculateClosingBalance(purchaseDetailsList)
      } else if (this.entryType === EntryType.byDataClick) {
        // 根据数据获取对账明细
        const params = {
          companyCode: this.headerInfo.companyCode, //	公司代码
          currencyCode: this.headerInfo.currencyCode, // 币种代码
          month: this.headerInfo.accountMonth, //	月份
          supplierCode: this.headerInfo.supplierCode, // 供应商代码
          year: this.headerInfo.accountYear // 年份
        }
        this.postTransactionReconciliationWaitFindList(params)
      }
    },
    // 当从待对账明细勾选创建跳转过来时，设置采方-对账明细表格数据
    setPurchaseDetailsListByDetails(data = []) {
      // 清空采方-对账明细表格数据
      purchaseDetailsDataSource.splice(0, purchaseDetailsDataSource.length)
      if (data.length > 0) {
        data.forEach((item) => {
          // 设置采方往来明细表格数据
          purchaseDetailsDataSource.push(item)
        })
      }
    },
    // 科目类别汇总，计算期末余额
    typeSummaryAndCalculateClosingBalance(data = []) {
      const typeSummaryList = [] // 科目类别汇总(科目类别编码、科目类别、金额)
      const accountTypeCodeList = [] // 科目类别编码容器
      if (data.length > 0) {
        // 计算各个 未清账状态的 对账科目类别
        data.forEach((item) => {
          if (
            item.cleanStatus === CleanStatus.notYet &&
            !accountTypeCodeList.includes(item.accountTypeCode)
          ) {
            // 未清 && accountTypeCode 不在科目类别编码容器 list 中
            accountTypeCodeList.push(item.accountTypeCode)
            typeSummaryList.push({
              accountTypeCode: item.accountTypeCode, // 科目类别编码
              accountTypeName: item.accountTypeName, // 科目类别
              amount: item.amount // 金额
            })
          } else if (
            item.cleanStatus === CleanStatus.notYet &&
            accountTypeCodeList.includes(item.accountTypeCode)
          ) {
            // 未清 && accountTypeCode 在科目类别编码容器 list 中
            for (let i = 0; i < typeSummaryList.length; i++) {
              const itemSummary = typeSummaryList[i]
              if (itemSummary.accountTypeCode === item.accountTypeCode) {
                // 相加
                itemSummary.amount = bigDecimal.add(itemSummary.amount, item.amount)
              }
            }
          }
        })
      }

      this.headerInfo.closingBalance = Number(this.headerInfo.openingBalance) || 0 // 期末余额=期初余额+未清账状态的往来对账明细+调整项金额
      if (typeSummaryList.length > 0) {
        // 计算并设置 期末余额
        typeSummaryList.forEach((item) => {
          this.headerInfo.closingBalance = bigDecimal.add(
            this.headerInfo.closingBalance,
            item.amount
          )
        })
      }
      // 科目类别汇总
      this.accountCategorySummary = typeSummaryList
    },
    // 租户级-往来待对账-根据公司供应商等查询列表
    postTransactionReconciliationWaitFindList(params) {
      // 清空采方-对账明细表格数据
      purchaseDetailsDataSource.splice(0, purchaseDetailsDataSource.length)
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationWaitFindList(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 根据API获取的对账明细，科目类别汇总，计算期末余额
            if (res?.data) {
              const data = res?.data || []
              data.forEach((item) => {
                // 设置采方往来明细表格数据
                purchaseDetailsDataSource.push(item)
              })
              // 科目类别汇总，计算期末余额
              this.typeSummaryAndCalculateClosingBalance(data)
            }
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-往来对账单-采方-创建往来对账单并发布
    postTransactionReconciliationSave(params) {
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationSave(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 创建成功
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            // 返回
            this.goBack()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 提交 创建往来对账单
    doSubmit() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交吗？')
        },
        success: () => {
          // 获取采方整单附件
          const uploadFiles = this.$refs.relativeFileRef.getUploadFlies('purchaseMain')
          // 附件
          const fileList = formatUploadFiles(uploadFiles)
          // 往来对账主单
          const header = {
            closingBalance: this.headerInfo?.closingBalance, // 期末余额
            companyCode: this.headerInfo?.companyCode, //	公司编号
            companyName: this.headerInfo?.companyName, //	公司名称
            currencyCode: this.headerInfo?.currencyCode, // 币种编码
            currencyName: this.headerInfo?.currencyName, // 币种名称
            deadLine: this.headerInfo?.accountDate
              ? Number(this.headerInfo?.accountDate)
              : undefined, // 截止日期 入账日期
            month: this.headerInfo?.accountMonth, //	对账月份
            openingBalance: this.headerInfo?.openingBalance, // 期初余额
            remark: this.headerInfo?.remark, // 采方备注
            supplierCode: this.headerInfo?.supplierCode, // 供应商/客户编号
            supplierName: this.headerInfo?.supplierName, // 供应商/客户名称
            year: this.headerInfo?.accountYear // 对账年份
          }
          // 往来对账明细
          const items = []
          purchaseDetailsDataSource.forEach((item) => {
            items.push({ id: item.id, remark: '' })
          })
          const params = {
            fileList, // 附件
            header, // 往来对账主单
            items // 往来对账明细
          }
          // 租户级-往来对账单-采方-创建往来对账单并发布
          this.postTransactionReconciliationSave(params)
        }
      })
    },
    goBack() {
      // 将 tabIndex 放到 localStorage 往来待对账-采方 读
      localStorage.setItem('tabIndex', JSON.stringify(this.lastTabIndex))
      // 返回 往来待对账-采方
      this.$router.push({
        name: 'contact-be-reconciled',
        query: {}
      })
    },
    // 点击 tab
    handleSelectTab(e) {
      this.currentTabInfo = this.tabList[e]
    },
    doExpand() {},
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.summary {
  background-color: var(--plugin-dg-bg-ff);
  height: 100%;
  overflow: auto;
  .box {
    flex-direction: column;
    .title {
      margin: 24px 0px 24px 24px;
      padding-left: 8px;
      border-left: 4px solid #3369ac;
      font-weight: 600;
    }
    .tips {
      margin: 24px 0px 24px 60px;
      .icon {
        padding-right: 8px;
        color: #6487bf;
      }
    }
    .row-box {
      flex: 1 1 auto;
      display: flex;
      position: relative;
      .item-box {
        width: calc(50% - 1px); // 两项 共计 2px 预留给中间的虚线
        &.empty {
          display: flex;
          justify-content: center;
          flex-direction: column;
          min-height: 200px;
          align-items: center;
          color: #a6a6a6;
        }

        .top {
          display: flex;
          justify-content: space-between;
          margin: 12px 32px 24px 32px;
          .item {
            width: 32%;
          }
        }
        .table-box {
          margin: 12px 32px 0px 32px;
        }
      }
      .dividing-line {
        border-left: 2px dashed #d9d9d9;
      }
    }
  }
}

/deep/ .grid-wrap-not-paging .e-control.e-grid .e-gridcontent {
  .e-frozenscrollbar.e-frozen-left-scrollbar {
    width: 50px !important; // 选择列宽度
  }
  .e-movablescrollbar .e-movablechild {
    width: 2030px !important; // 表格宽度
  }
}
</style>
