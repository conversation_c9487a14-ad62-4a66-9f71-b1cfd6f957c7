import { Tab, ContactsTypeOptions, CleanStatusOptions } from './constant'
import { timeDate } from './columnComponent'

// 格式化表格动态数据
export const formatTableColumnData = (args) => {
  const { data, tab } = args
  const colData = []
  if (tab === Tab.purchaseDetails) {
    // 采方-待对账明细
    data.forEach((col) => {
      const defaultCol = {
        ...col,
        field: col.fieldCode,
        headerText: col.fieldName,
        allowFiltering: false,
        ignore: true,
        width: '150'
      }
      if (col.fieldCode === 'type') {
        // // 往来待对账明细 往来类别
        defaultCol.valueConverter = {
          type: 'map',
          map: ContactsTypeOptions
        }
      } else if (col.fieldCode === 'accountDate' || col.fieldCode === 'theOperationDate') {
        // 往来待对账明细 入账日期
        // 往来待对账明细 操作日期 前端自定义 updateTime更新时间为空时显示createTime创建时间
        defaultCol.template = timeDate({
          dataKey: col.fieldCode,
          hasTime: false
        })
      } else if (col.fieldCode === 'cleanStatus') {
        // 往来待对账明细 清账状态
        defaultCol.valueConverter = {
          type: 'map',
          map: CleanStatusOptions
        }
      }
      colData.push(defaultCol)
    })
  } else if (tab === Tab.toBeReconciled) {
    // 往来待对账
    data.forEach((col) => {
      const defaultCol = {
        ...col,
        field: col.fieldCode,
        headerText: col.fieldName,
        width: 'auto'
      }
      if (col.fieldCode === 'dataLength') {
        // 往来待对账 详情明细
        defaultCol.cellTools = [] // 使其可点击查看
      } else if (col.fieldCode === 'accountDate') {
        // 往来待对账 入账日期
        defaultCol.template = timeDate({
          dataKey: col.fieldCode,
          hasTime: false
        })
      }
      colData.push(defaultCol)
    })
  }

  return colData
}

// 格式化附件信息
export const formatUploadFiles = (list) => {
  const tmp = []
  list.forEach((item) => {
    tmp.push({
      fileName: item.fileName, //	文件上传名称
      fileSize: item.fileSize, //	文件大小
      fileType: item.fileType, //	文件类型
      sysFileId: item.id || item.sysFileId, //	文件ID(MT_WP_SYS_FILE表ID)
      url: item.url //	文件路径
    })
  })

  return tmp
}
