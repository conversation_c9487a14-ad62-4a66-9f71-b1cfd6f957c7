import { i18n } from '@/main.js'

// tab
export const Tab = {
  summary: 0, // 金额汇总
  purchaseDetails: 1, // 采方-往来明细
  file: 2 // 相关附件
}

export const TabList = [
  {
    title: i18n.t('金额汇总'),
    code: Tab.summary
  },
  {
    title: i18n.t('采方-往来明细'),
    code: Tab.purchaseDetails
  },
  {
    title: i18n.t('相关附件'),
    code: Tab.file
  }
]

export const EntryType = {
  byDetails: 1, // 待创建明细勾选进入
  byDataClick: 2 // 待创建点击行进入
}

// // 往来类别 0-对账；1-索赔；2-返利
// export const ContactsType = {
//   recon: 0, // 对账
//   claim: 1, // 索赔
//   rebate: 2, // 返利
//   other: 9, // 其他
// };

export const ContactsType = {
  bzj: 3, // 保证金
  xsfp: 4, // 开发票给TCl
  kk: 5, // 扣款
  sk: 6, // 收款
  kfp: 7, // 收到TCL开具的发票
  other: 9 // 其他
}

// 往来类别 text
export const ContactsTypeText = {
  [ContactsType.bzj]: i18n.t('保证金'),
  [ContactsType.xsfp]: i18n.t('开发票给TCL'),
  [ContactsType.kk]: i18n.t('扣款'),
  [ContactsType.sk]: i18n.t('收款'),
  [ContactsType.kfp]: i18n.t('收到TCL开具的发票'),
  [ContactsType.other]: i18n.t('其他')
}

// 往来类别 对应的 css class
export const ContactsTypeCssClass = {
  [ContactsType.bzj]: '',
  [ContactsType.xsfp]: '',
  [ContactsType.kk]: '',
  [ContactsType.sk]: '',
  [ContactsType.kfp]: '',
  [ContactsType.other]: ''
}

// 往来类别 对应的 Options
export const ContactsTypeOptions = [
  {
    // 保证金
    value: ContactsType.bzj,
    text: ContactsTypeText[ContactsType.bzj],
    cssClass: ContactsTypeCssClass[ContactsType.bzj]
  },
  {
    // 开发票给TCL
    value: ContactsType.xsfp,
    text: ContactsTypeText[ContactsType.xsfp],
    cssClass: ContactsTypeCssClass[ContactsType.xsfp]
  },
  {
    // 扣款
    value: ContactsType.kk,
    text: ContactsTypeText[ContactsType.kk],
    cssClass: ContactsTypeCssClass[ContactsType.kk]
  },
  {
    // 收款
    value: ContactsType.sk,
    text: ContactsTypeText[ContactsType.sk],
    cssClass: ContactsTypeCssClass[ContactsType.sk]
  },
  {
    // 收到TCL开具的发票
    value: ContactsType.kfp,
    text: ContactsTypeText[ContactsType.kfp],
    cssClass: ContactsTypeCssClass[ContactsType.kfp]
  },
  {
    // 其他
    value: ContactsType.other,
    text: ContactsTypeText[ContactsType.other],
    cssClass: ContactsTypeCssClass[ContactsType.other]
  }
]

// 清账状态 0未清 1已清
export const CleanStatus = {
  notYet: '0', // 未清
  already: '1' // 已清
}

// 清账状态 text
export const CleanStatusText = {
  [CleanStatus.notYet]: i18n.t('未清'),
  [CleanStatus.already]: i18n.t('已清')
}

// 清账状态 对应的 css class
export const CleanStatusCssClass = {
  [CleanStatus.notYet]: '',
  [CleanStatus.already]: ''
}

// 清账状态 对应的 Options
export const CleanStatusOptions = [
  {
    // 未清
    value: CleanStatus.notYet,
    text: CleanStatusText[CleanStatus.notYet],
    cssClass: CleanStatusCssClass[CleanStatus.notYet]
  },
  {
    // 已清
    value: CleanStatus.already,
    text: CleanStatusText[CleanStatus.already],
    cssClass: CleanStatusCssClass[CleanStatus.already]
  }
]

// 金额汇总-采方对账科目
export const SummaryColumns = [
  {
    field: 'accountTypeName',
    headerText: i18n.t('采方科目名称')
  },
  {
    field: 'amount',
    headerText: i18n.t('采方科目金额')
  }
]

// 采方-往来待对账明细 表格列数据
export const ToBeReconciledDetailsColumnData = [
  {
    fieldCode: 'companyName', // 公司名称
    fieldName: i18n.t('公司名称')
  },
  {
    fieldCode: 'supplierName', // 供应商名称
    fieldName: i18n.t('供应商名称')
  },
  {
    fieldCode: 'type', // 往来类别 往来类别 0-对账；1-索赔；2-返利
    fieldName: i18n.t('往来类别')
  },
  {
    fieldCode: 'docNo', // 单据号
    fieldName: i18n.t('单据号')
  },
  {
    fieldCode: 'accountTypeName', // 科目类别
    fieldName: i18n.t('科目类别')
    // accountTypeCode	科目类别编码
  },
  {
    fieldCode: 'reconciliationAccount', // 科目编号 对账科目
    fieldName: i18n.t('科目编号')
  },
  {
    fieldCode: 'reconciliationAccountName', // 科目 对账科目名称
    fieldName: i18n.t('科目')
  },
  {
    fieldCode: 'accountDate', // 入账日期
    fieldName: i18n.t('入账日期')
  },
  {
    fieldCode: 'invoiceNo', // 发票号
    fieldName: i18n.t('发票号')
  },
  {
    fieldCode: 'amount', // 金额
    fieldName: i18n.t('金额')
  },
  {
    fieldCode: 'currencyName', // 币种
    fieldName: i18n.t('币种')
    // 币种编码 currencyCode
  },
  {
    fieldCode: 'theOperationDate', // 操作日期 前端自定义 updateTime更新时间为空时显示createTime创建时间
    fieldName: i18n.t('操作日期')
  },
  {
    fieldCode: 'cleanStatus', // 清账状态 0未清 1已清
    fieldName: i18n.t('清账状态')
  }
]
