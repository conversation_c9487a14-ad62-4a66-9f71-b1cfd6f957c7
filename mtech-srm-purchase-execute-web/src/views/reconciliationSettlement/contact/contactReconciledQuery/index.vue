<template>
  <!-- 往来对账单列表-采方 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      class="template-height"
      :template-config="componentConfig"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    />
    <!-- 操作日志弹框 -->
    <view-logs-dialog ref="viewLogsDialog"></view-logs-dialog>
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { formatTableColumnData, addTheReconDiffToList } from './config/index'
import ViewLogsDialog from './components/viewLogsDialog.vue'
import { Tab, ReconciledColumnData, Toolbar, ReconciledStatus } from './config/constant'
import { EntryType } from '../contactReconciledDetail/config/constant'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  components: { ViewLogsDialog },
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      actionType: {
        createStatement: 1 // 明细创建对账单
      },

      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: Toolbar,
          gridId: this.$tableUUID.reconciliationSettlement.contactReconciledList.query,
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: formatTableColumnData({
              tab: Tab.reconciled,
              data: ReconciledColumnData
            }),
            dataSource: [],
            // 租户级-往来对账单-采方-采方往来对账单分页查询
            asyncConfig: {
              url: `${BASE_TENANT}/transaction_reconciliation/query`,
              defaultRules: [],
              // 往来对账单列表 序列化
              serializeList: (list) => {
                let afterSerialization = addTheReconDiffToList(list)
                return afterSerialization
              }
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    // ToolBar
    handleClickToolBar(args) {
      const { gridRef, toolbar } = args
      const selectRows = gridRef.getMtechGridRecords()
      if (
        selectRows.length === 0 &&
        !(
          toolbar.id == 'more-option-btn' ||
          toolbar.id == 'Filter' ||
          toolbar.id == 'Refresh' ||
          toolbar.id == 'Setting' ||
          toolbar.id == 'refreshDataByLocal' ||
          toolbar.id == 'filterDataByLocal' ||
          toolbar.id == 'ExcelExport'
        )
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedIds = []
      selectRows.forEach((item) => {
        selectedIds.push(item.id)
      })

      if (toolbar.id === 'ConfirmStatement') {
        // 往来对账单列表-确认
        this.handleConfirmStatement({
          selectedRecords: selectRows,
          idList: selectedIds
        })
      } else if (toolbar.id == 'printRecon') {
        this.handlePrint(selectedIds)
      } else if (toolbar.id === 'ExcelExport') {
        this.handleExport()
      }
    },
    // CellTool
    handleClickCellTool(args) {
      const { tool, data, tabIndex } = args
      if (tool.id === 'ConfirmStatement') {
        // 确认
        this.handleConfirmStatement({
          selectedRecords: data,
          idList: [data.id]
        })
      } else if (tool.id === 'ConfirmStatement1') {
        // 确认
        this.postTransactionReconciliationFinace([data.id])
      } else if (tool.id === 'CloseStatement') {
        // 关闭
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认关闭选中的数据？')
          },
          success: () => {
            // 租户级-往来对账单-采方-关闭往来对账单
            this.postTransactionReconciliationClose([data.id])
          }
        })
      } else if (tool.id === 'BackStatement' || tool.id === 'BackStatement1') {
        // 采购退回 、财务退回
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认退回选中的数据？')
          },
          success: () => {
            this.postTransactionReconciliationBack([data.id])
          }
        })
      } else if (tool.id === 'ContinueStatement') {
        // 继续发布
        // 将 lastTabIndex 放到 localStorage 往来对账单详情-采方 读
        localStorage.setItem('lastTabIndex', JSON.stringify(tabIndex))
        // 跳转 往来对账单详情-采方
        this.$router.push({
          name: 'contact-reconciled-detail',
          query: {
            id: data.id,
            code: data.code,
            type: EntryType.edit // 编辑
          }
        })
      }
    },
    // CellTitle
    handleClickCellTitle(args) {
      const { field, tabIndex, data } = args
      if (field === 'code' && (tabIndex === Tab.reconciledDetails || tabIndex === Tab.reconciled)) {
        // 点击对账单号 && （往来对账明细 || 往来对账单列表）
        let id = data.id
        if (tabIndex === Tab.reconciledDetails) {
          id = data.headerId
        }
        // 将 lastTabIndex 放到 localStorage 往来对账单详情-采方 读
        localStorage.setItem('lastTabIndex', JSON.stringify(tabIndex))
        // 跳转 往来对账单详情-采方
        this.$router.push({
          name: 'contact-reconciled-query-detail',
          query: {
            id,
            type: EntryType.view, // 查看
            code: data.code
          }
        })
      } else if (field == 'operation') {
        // 点击 查看日志
        this.handleClickOption(data)
      }
    },
    // 点击查看日志
    handleClickOption(data) {
      console.log('查看日志', data)
      const params = {
        reconciliationCode: data.code,
        reconciliationType: 4,
        tenantId: '10000'
      }
      this.$refs.viewLogsDialog.dialogInit({
        title: this.$t('操作日志'),
        selectData: params
      })
    },
    // 采购确认对账单
    handleConfirmStatement(args) {
      const { selectedRecords, idList } = args
      // 校验状态是否未 反馈正常
      let valid = true
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        if (item.status != ReconciledStatus.normal) {
          valid = false
          break
        }
      }
      if (valid) {
        // 租户级-往来对账单-采方-确认往来对账单
        this.postTransactionReconciliationFinish(idList)
      } else {
        // 校验未通过
        this.$toast({
          content: this.$t('请选择反馈正常的数据'),
          type: 'warning'
        })
      }
    },
    // 导出
    handleExport() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 10000 },
        pageFlag: true,
        rules: rule.rules || []
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement.reconciliationTransactionExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 租户级-往来对账单-采方-确认往来对账单
    postTransactionReconciliationFinish(idList) {
      const params = {
        ids: idList
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationFinish(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-往来对账单-采方-财务确认往来对账单
    postTransactionReconciliationFinace(idList) {
      const params = {
        ids: idList
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationFinace(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-往来对账单-采方-关闭往来对账单
    postTransactionReconciliationClose(idList) {
      const params = {
        id: idList[0]
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationClose(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-往来对账单-采方-退回往来对账单
    postTransactionReconciliationBack(idList) {
      const params = {
        ids: idList
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationBack(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 打印
    handlePrint(ids) {
      this.$API.reconciliationSettlement.printContactRecon({ idList: ids }).then((res) => {
        if (res?.data?.type === 'application/json') {
          const reader = new FileReader()
          reader.readAsText(res?.data, 'utf-8')
          reader.onload = () => {
            console.log('======', reader)
            const readerRes = reader.result
            const resObj = JSON.parse(readerRes)
            this.$toast({
              content: resObj.msg,
              type: 'error'
            })
          }

          return
        }
        const content = res.data
        this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = this.pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
// 对账差异 theReconDiffTemplate
/deep/ .the-recon-diff-blue {
  color: #6386c1; // 这是 UI 给的颜色
}
/deep/ .the-recon-diff-red {
  color: #ed5633; // 这是 UI 给的颜色
}
</style>
