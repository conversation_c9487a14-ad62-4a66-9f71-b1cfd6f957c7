import { ContactsTypeOptions, CleanStatusOptions, AdjustTypeOptions } from './constant'
import { ColumnComponent as Component } from './columnComponent'

// 格式化 对账明细 表格动态数据
export const formatDetailsTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150'
    }
    if (col.fieldCode === 'type') {
      // // 往来待对账明细 往来类别
      defaultCol.valueConverter = {
        type: 'map',
        map: ContactsTypeOptions
      }
    } else if (col.fieldCode === 'accountDate') {
      // 往来待对账明细 入账日期
      defaultCol.template = Component.timeDate({
        dataKey: col.fieldCode,
        hasTime: false
      })
    } else if (col.fieldCode === 'cleanStatus') {
      // 往来待对账明细 清账状态
      defaultCol.valueConverter = {
        type: 'map',
        map: CleanStatusOptions
      }
    }
    colData.push(defaultCol)
  })

  return colData
}

// 格式化 操作日志 表格动态数据
export const formatLogTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150'
    }
    if (col.fieldCode === 'createTime') {
      // 创建时间
      defaultCol.template = Component.timeDate({
        dataKey: col.fieldCode,
        hasTime: true
      })
    }
    colData.push(defaultCol)
  })

  return colData
}

// 格式化 差异调整项 表格动态数据
export const formatAdjustTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150'
    }
    if (col.fieldCode === 'thePrimaryKey') {
      // 行主键
      defaultCol.visible = false
      defaultCol.isIdentity = true // 加上这个，新增一行的时候，主键不会变成可输入
      defaultCol.isPrimaryKey = true
      defaultCol.allowEditing = false
    } else if (col.fieldCode === 'checkBox') {
      // checkBox
      defaultCol.type = 'checkbox'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.showInColumnChooser = false
      defaultCol.width = '50'
      // 编辑时显示
      defaultCol.editTemplate = Component.empty
    } else if (col.fieldCode === 'serialNumber') {
      // 序号
      defaultCol.width = '80'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.ignore = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'adjustType') {
      // 差异调整型类型
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode,
        valueConverter: {
          type: 'map',
          map: AdjustTypeOptions
        }
      })
      defaultCol.editTemplate = Component.select({
        dataKey: col.fieldCode,
        fields: { text: 'text', value: 'value' },
        selectOptions: AdjustTypeOptions,
        allowFiltering: false,
        showClearBtn: true
      })
    } else if (col.fieldCode === 'adjustDescription') {
      // 调整项摘要
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 100
      })
    } else if (col.fieldCode === 'amount') {
      // 调整项金额
      defaultCol.width = '200'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.number({
        dataKey: col.fieldCode,
        showClearBtn: true,
        precision: 2,
        maxValue: 999999999999999.99,
        minValue: -999999999999999.99
      })
    } else if (col.fieldCode === 'remark') {
      // 备注
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 200
      })
    }
    colData.push(defaultCol)
  })

  return colData
}
