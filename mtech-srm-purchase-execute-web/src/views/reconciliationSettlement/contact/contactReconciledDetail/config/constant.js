import { i18n } from '@/main.js'

// tab
export const Tab = {
  summary: 0, // 金额汇总
  purchaseDetails: 1, // 采方-往来明细
  supplierDetails: 2, // 供方-往来明细
  file: 3, // 相关附件
  log: 4 // 操作日志
}

export const TabList = [
  {
    title: i18n.t('金额汇总'),
    code: Tab.summary
  },
  {
    title: i18n.t('采方-往来明细'),
    code: Tab.purchaseDetails
  },
  {
    title: i18n.t('供方-往来明细'),
    code: Tab.supplierDetails
  },
  {
    title: i18n.t('相关附件'),
    code: Tab.file
  },
  {
    title: i18n.t('操作日志'),
    code: Tab.log
  }
]

// 表格编辑 的 RequestType
export const RequestType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  sorting: 'sorting',
  refresh: 'refresh'
}

// 表格编辑 的 action
export const ActionType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  edit: 'edit'
}

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 行编辑触发的 change 事件类型
export const ComponentChangeType = {
  code: 'code', // 例：下拉框选择“供应商”，供应商编码（code)要被修改
  link: 'link' // 例：计划组：根据物料+工厂+计划组的业务组类型 code 带出
}

// 表格 EditSettings
export const EditSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
} // 编辑设置

// 采方差异调整项 新增行固定数据
export const PurchaseAdjustNewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  adjustDescription: null, //	调整项摘要
  adjustType: null, // 调整项类型
  amount: null, // 调整金额
  headerId: null, // 对账单ID
  id: null, // id
  remark: null // 备注
}

// 页面显示类型
export const EntryType = {
  view: 1, // 查看
  edit: 2 // 继续发布时就行编辑
}

// 状态 0-新建；1-待反馈；2-反馈正常；3-反馈异常；4-采购已确认；5-关闭；
export const ReconciledStatus = {
  new: 0, // 新建
  pending: 1, // 待反馈
  normal: 2, // 反馈正常
  abnormal: 3, // 反馈异常
  confirmed: 4, // 采购已确认
  close: 5, // 关闭
  finConfirmed: 6, // 财务已确认
  finished: 7 // 已完结
}

// 往来类别 text
export const ReconciledStatusText = {
  [ReconciledStatus.new]: i18n.t('新建'),
  [ReconciledStatus.pending]: i18n.t('待反馈'),
  [ReconciledStatus.normal]: i18n.t('反馈正常'),
  [ReconciledStatus.abnormal]: i18n.t('反馈异常'),
  [ReconciledStatus.confirmed]: i18n.t('采购已确认'),
  [ReconciledStatus.close]: i18n.t('关闭'),
  [ReconciledStatus.finConfirmed]: i18n.t('财务已确认'),
  [ReconciledStatus.finished]: i18n.t('已完结')
}

// 往来类别 对应的 css class
export const ReconciledStatusCssClass = {
  [ReconciledStatus.new]: 'col-active',
  [ReconciledStatus.pending]: 'col-active',
  [ReconciledStatus.normal]: 'col-active',
  [ReconciledStatus.abnormal]: 'col-active',
  [ReconciledStatus.confirmed]: 'col-inactive',
  [ReconciledStatus.close]: 'col-inactive',
  [ReconciledStatus.finConfirmed]: 'col-inactive',
  [ReconciledStatus.finished]: 'col-inactive'
}

// 往来类别 对应的 Options
export const ReconciledStatusOptions = [
  {
    // 新建
    value: ReconciledStatus.new,
    text: ReconciledStatusText[ReconciledStatus.new],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.new]
  },
  {
    // 待反馈
    value: ReconciledStatus.pending,
    text: ReconciledStatusText[ReconciledStatus.pending],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.pending]
  },
  {
    // 反馈正常
    value: ReconciledStatus.normal,
    text: ReconciledStatusText[ReconciledStatus.normal],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.normal]
  },
  {
    // 反馈异常
    value: ReconciledStatus.abnormal,
    text: ReconciledStatusText[ReconciledStatus.abnormal],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.abnormal]
  },
  {
    // 采购已确认
    value: ReconciledStatus.confirmed,
    text: ReconciledStatusText[ReconciledStatus.confirmed],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.confirmed]
  },
  {
    // 关闭
    value: ReconciledStatus.close,
    text: ReconciledStatusText[ReconciledStatus.close],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.close]
  },
  {
    // 财务已确认
    value: ReconciledStatus.finConfirmed,
    text: ReconciledStatusText[ReconciledStatus.finConfirmed],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.finConfirmed]
  },
  {
    // 已完结
    value: ReconciledStatus.finished,
    text: ReconciledStatusText[ReconciledStatus.finished],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.finished]
  }
]

// 往来类别 0-对账；1-索赔；2-返利
// export const ContactsType = {
//   recon: 0, // 对账
//   claim: 1, // 索赔
//   rebate: 2, // 返利
//   other: 9, // 其他
// };

// 往来类别
export const ContactsType = {
  bzj: 3, // 保证金
  xsfp: 4, // 开发票给TCl
  kk: 5, // 扣款
  sk: 6, // 收款
  kfp: 7, // 收到TCL开具的发票
  other: 9 // 其他
}

// 往来类别 text
export const ContactsTypeText = {
  [ContactsType.bzj]: i18n.t('保证金'),
  [ContactsType.xsfp]: i18n.t('开发票给TCL'),
  [ContactsType.kk]: i18n.t('扣款'),
  [ContactsType.sk]: i18n.t('收款'),
  [ContactsType.kfp]: i18n.t('收到TCL开具的发票'),
  [ContactsType.other]: i18n.t('其他')
}

// 往来类别 对应的 css class
export const ContactsTypeCssClass = {
  [ContactsType.bzj]: '',
  [ContactsType.xsfp]: '',
  [ContactsType.kk]: '',
  [ContactsType.sk]: '',
  [ContactsType.kfp]: '',
  [ContactsType.other]: ''
}

// 往来类别 对应的 Options
export const ContactsTypeOptions = [
  {
    // 保证金
    value: ContactsType.bzj,
    text: ContactsTypeText[ContactsType.bzj],
    cssClass: ContactsTypeCssClass[ContactsType.bzj]
  },
  {
    // 开发票给TCL
    value: ContactsType.xsfp,
    text: ContactsTypeText[ContactsType.xsfp],
    cssClass: ContactsTypeCssClass[ContactsType.xsfp]
  },
  {
    // 扣款
    value: ContactsType.kk,
    text: ContactsTypeText[ContactsType.kk],
    cssClass: ContactsTypeCssClass[ContactsType.kk]
  },
  {
    // 收款
    value: ContactsType.sk,
    text: ContactsTypeText[ContactsType.sk],
    cssClass: ContactsTypeCssClass[ContactsType.sk]
  },
  {
    // 收到TCL开具的发票
    value: ContactsType.kfp,
    text: ContactsTypeText[ContactsType.kfp],
    cssClass: ContactsTypeCssClass[ContactsType.kfp]
  },
  {
    // 其他
    value: ContactsType.other,
    text: ContactsTypeText[ContactsType.other],
    cssClass: ContactsTypeCssClass[ContactsType.other]
  }
]

// 清账状态 0未清 1已清
export const CleanStatus = {
  notYet: '0', // 未清
  already: '1' // 已清
}

// 清账状态 text
export const CleanStatusText = {
  [CleanStatus.notYet]: i18n.t('未清'),
  [CleanStatus.already]: i18n.t('已清')
}

// 清账状态 对应的 css class
export const CleanStatusCssClass = {
  [CleanStatus.notYet]: '',
  [CleanStatus.already]: ''
}

// 清账状态 对应的 Options
export const CleanStatusOptions = [
  {
    // 未清
    value: CleanStatus.notYet,
    text: CleanStatusText[CleanStatus.notYet],
    cssClass: CleanStatusCssClass[CleanStatus.notYet]
  },
  {
    // 已清
    value: CleanStatus.already,
    text: CleanStatusText[CleanStatus.already],
    cssClass: CleanStatusCssClass[CleanStatus.already]
  }
]

// 差异调整型类型
export const AdjustType = {
  plus: 1, // 新增项
  minus: -1 // 新减项
}

// 差异调整型类型 text
export const AdjustTypeText = {
  [AdjustType.plus]: i18n.t('调增项'),
  [AdjustType.minus]: i18n.t('调减项')
}

// 差异调整型类型 对应的 css class
export const AdjustTypeCssClass = {
  [AdjustType.plus]: '',
  [AdjustType.minus]: ''
}

// 差异调整型类型 对应的 Options
export const AdjustTypeOptions = [
  {
    // 新增项
    value: AdjustType.plus,
    text: AdjustTypeText[AdjustType.plus],
    cssClass: AdjustTypeCssClass[AdjustType.plus]
  },
  {
    // 新减项
    value: AdjustType.minus,
    text: AdjustTypeText[AdjustType.minus],
    cssClass: AdjustTypeCssClass[AdjustType.minus]
  }
]

// 金额汇总-采方对账科目
export const PurchaseSummaryColumns = [
  {
    field: 'accountTypeName',
    headerText: i18n.t('采方科目名称')
  },
  {
    field: 'amount',
    headerText: i18n.t('采方科目金额')
  }
]

// 金额汇总-供方对账科目
export const SupplierSummaryColumns = [
  {
    field: 'accountTypeName',
    headerText: i18n.t('供方科目名称')
  },
  {
    field: 'amount',
    headerText: i18n.t('供方科目金额')
  }
]

// 往来明细 表格列数据
export const DetailsColumnData = [
  {
    fieldCode: 'type', // 往来类别 往来类别 0-对账；1-索赔；2-返利
    fieldName: i18n.t('往来类别')
  },
  {
    fieldCode: 'docNo', // 单据号
    fieldName: i18n.t('凭证编码')
  },
  {
    fieldCode: 'reconciliationAccountName', // 科目 对账科目名称
    fieldName: i18n.t('会计科目名称')
  },
  {
    fieldCode: 'reconciliationAccount', // 科目编号 对账科目
    fieldName: i18n.t('会计科目编号')
  },
  // {
  //   fieldCode: "accountTypeName", // 科目类别
  //   fieldName: i18n.t("科目类别"),
  // },
  // {
  //   fieldCode: "accountTypeCode", // 科目类别编码
  //   fieldName: i18n.t("科目类别编码"),
  // },
  // {
  //   fieldCode: "reconciliationAccountName", // 科目 对账科目名称
  //   fieldName: i18n.t("科目"),
  // },
  // {
  //   fieldCode: "reconciliationAccount", // 科目编号 对账科目
  //   fieldName: i18n.t("科目编号"),
  // },
  {
    fieldCode: 'accountDate', // 入账日期
    fieldName: i18n.t('入账日期')
  },
  {
    fieldCode: 'invoiceNo', // 发票号
    fieldName: i18n.t('发票号')
  },
  {
    fieldCode: 'amount', // 金额
    fieldName: i18n.t('金额')
  },
  {
    fieldCode: 'currencyName', // 币种
    fieldName: i18n.t('币种')
  },
  {
    fieldCode: 'currencyCode', // 币种编码
    fieldName: i18n.t('币种编码')
  },
  {
    fieldCode: 'lineNo', // 行号
    fieldName: i18n.t('行号')
  },
  {
    fieldCode: 'digest', // 摘要
    fieldName: i18n.t('摘要')
  },
  {
    fieldCode: 'digest1', // 摘要1
    fieldName: i18n.t('摘要1')
  },
  {
    fieldCode: 'digest2', // 摘要2
    fieldName: i18n.t('摘要2')
  }
  // {
  //   fieldCode: "cleanStatus", // 清账状态 0未清 1已清
  //   fieldName: i18n.t("清账状态"),
  // },
]

export const SupplierDetailsColumnData = [
  {
    fieldCode: 'type', // 往来类别 往来类别 0-对账；1-索赔；2-返利
    fieldName: i18n.t('往来类别')
  },
  {
    fieldCode: 'docNo', // 对账单号
    fieldName: i18n.t('对账单号')
  },
  {
    fieldCode: 'accountDate', // 入账日期
    fieldName: i18n.t('入账日期')
  },
  {
    fieldCode: 'invoiceNo', // 发票号
    fieldName: i18n.t('发票号')
  },
  {
    fieldCode: 'amount', // 金额
    fieldName: i18n.t('金额')
  },
  {
    fieldCode: 'currencyName', // 币种
    fieldName: i18n.t('币种')
  },
  {
    fieldCode: 'currencyCode', // 币种编码
    fieldName: i18n.t('币种编码')
  },
  {
    fieldCode: 'claimDemageNo', // 索赔单号
    fieldName: i18n.t('索赔单号')
  },
  {
    fieldCode: 'remark', // 备注
    fieldName: i18n.t('备注')
  }
]

// 操作日志 表格列数据
export const LogColumnData = [
  {
    fieldCode: 'operTypeName',
    fieldName: i18n.t('操作类型')
  },
  {
    fieldCode: 'operDescription',
    fieldName: i18n.t('操作内容') // 操作描述 操作内容
  },
  {
    fieldCode: 'createUserName',
    fieldName: i18n.t('操作人')
  },
  {
    fieldCode: 'createTime',
    fieldName: i18n.t('操作时间')
  }
]

// 差异调整项 表格列数据
export const AdjustColumnData = [
  {
    fieldCode: 'checkBox' // 不可编辑
  },
  {
    fieldCode: 'thePrimaryKey'
  },
  {
    fieldCode: 'serialNumber', // 前端定义,不可编辑
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'adjustType', // 新增项 1，新减项 -1
    fieldName: i18n.t('类型')
  },
  {
    fieldCode: 'adjustDescription',
    fieldName: i18n.t('调整项摘要')
  },
  {
    fieldCode: 'amount',
    fieldName: i18n.t('调整项金额')
  },
  {
    fieldCode: 'remark',
    fieldName: i18n.t('备注')
  }
]
