<template>
  <!-- 往来对账-待对账-采方 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      class="template-height"
      :permission-obj="permissionObj"
      :template-config="componentConfig"
      :current-tab="currentTab"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    />
    <!-- 通知配置导入弹框 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import {
  formatTableColumnData,
  addTheOperationDateToList,
  formatNotifyTableColumnData,
  serializeNotifyList
} from './config/index'
import {
  Tab,
  ToBeReconciledColumnData,
  ToBeReconciledDetailsColumnData,
  EditSettings,
  NotifyToolbar,
  NotifyColumnData,
  RequestType,
  ActionType,
  NewRowData
} from './config/constant'
import { EntryType } from '../createContactReconciled/config/constant'
import { rowDataTemp } from './config/variable'
import { cloneDeep } from 'lodash'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    UploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    const currentTab = JSON.parse(localStorage.getItem('tabIndex')) ?? 0
    localStorage.removeItem('tabIndex')

    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      currentTab, // 当前加载的 Tab 默认 0
      actionType: {
        createStatement: 1 // 明细创建对账单
      },
      isEditing: false, // 正在编辑数据
      downTemplateParams: {
        pageFlag: false
      }, // 通知配置导入下载模板参数
      uploadParams: {}, // 导入通知配置文件参数
      // 通知配置导入请求接口配置
      requestUrls: {
        templateUrlPre: 'reconciliationSettlement',
        templateUrl: 'postTransactionReconciliationSupplierConfigDownload', // 下载模板接口方法名
        uploadUrl: 'postTransactionReconciliationSupplierConfigUpload' // 上传接口方法名
      },
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0032' },
          { dataPermission: 'b', permissionCode: 'T_02_0033' },
          { dataPermission: 'c', permissionCode: 'T_02_0065' }
        ]
      },
      componentConfig: []
    }
  },
  mounted() {
    let _config = [
      {
        title: this.$t('往来待对账明细'),
        dataPermission: 'a',
        permissionCode: 'T_02_0032',
        useToolTemplate: false, // 不使用预置(新增、编辑、删除)
        useBaseConfig: true, // 使用组件中的toolbar配置
        toolbar: [
          // {
          //   id: "CreateStatementDetails",
          //   icon: "icon_solid_Createorder",
          //   title: this.$t("创建对账单"),
          //   permission: ["O_02_0408"],
          // },
          {
            id: 'excelExport',
            icon: 'icon_solid_export',
            title: this.$t('导出')
          }
        ],
        gridId: this.$tableUUID.reconciliationSettlement.contactBeReconciled.reconDetailsTab,
        grid: {
          lineSelection: 0,
          lineIndex: 1,
          columnData: formatTableColumnData({
            tab: Tab.toBeReconciledDetails,
            data: ToBeReconciledDetailsColumnData
          }),
          dataSource: [],
          // 往来待对账-往来待对账明细信息
          asyncConfig: {
            url: `${BASE_TENANT}/transaction_reconciliation/wait/query`,
            defaultRules: [],
            // 序列化
            serializeList: (list) => {
              const afterSerialization = addTheOperationDateToList(list)
              return afterSerialization
            }
          }
          // frozenColumns: 1,
        }
      },
      {
        title: this.$t('往来待对账'),
        dataPermission: 'b',
        permissionCode: 'T_02_0033',
        useToolTemplate: false, // 不使用预置(新增、编辑、删除)
        useBaseConfig: true, // 使用组件中的toolbar配置
        toolbar: [
          {
            id: 'CreateStatement',
            icon: 'icon_solid_Createorder',
            title: this.$t('创建对账单'),
            permission: ['O_02_0409']
          }
        ],
        gridId: this.$tableUUID.reconciliationSettlement.contactBeReconciled.toBeReconTab,
        grid: {
          lineSelection: 0,
          lineIndex: 1,
          columnData: formatTableColumnData({
            tab: Tab.toBeReconciled,
            data: ToBeReconciledColumnData
          }),
          dataSource: [],
          // 往来待对账-往来待对账汇总信息
          asyncConfig: {
            url: `${BASE_TENANT}/transaction_reconciliation/wait/summary`,
            defaultRules: []
          }
          // frozenColumns: 1,
        }
      },
      {
        title: this.$t('对账通知'),
        dataPermission: 'c',
        permissionCode: 'T_02_0065',
        useToolTemplate: false, // 不使用预置(新增、编辑、删除)
        useBaseConfig: true, // 不使用组件中的toolbar配置
        toolbar: NotifyToolbar,
        gridId: this.$tableUUID.reconciliationSettlement.contactBeReconciled.reconNoticeTab,
        grid: {
          editSettings: EditSettings,
          allowPaging: true, // 分页
          columnData: formatNotifyTableColumnData({
            data: NotifyColumnData
          }),
          dataSource: [],
          // 往来对账供应商配置-查询往来对账配置列表
          asyncConfig: {
            url: `${BASE_TENANT}/transaction_reconciliation_supplier_config/query`,
            defaultRules: [],
            serializeList: serializeNotifyList
          }
          // frozenColumns: 1,
        }
      },
      {
        title: this.$t('无发生额外往来对账'),
        // dataPermission: "b",
        // permissionCode: "T_02_0033",
        useToolTemplate: false, // 不使用预置(新增、编辑、删除)
        useBaseConfig: true, // 使用组件中的toolbar配置
        toolbar: [
          {
            id: 'CreateStatementNoExtra',
            icon: 'icon_solid_Createorder',
            title: this.$t('创建对账单')
            // permission: ["O_02_0409"],
          }
        ],
        gridId: this.$tableUUID.reconciliationSettlement.contactBeReconciled.noExtraReconNoticeTab,
        grid: {
          lineSelection: 0,
          lineIndex: 1,
          columnData: formatTableColumnData({
            tab: Tab.noExtraReconciled,
            data: ToBeReconciledColumnData
          }),
          dataSource: [],
          // 往来待对账-往来待对账汇总信息
          asyncConfig: {
            url: `${BASE_TENANT}/transaction_reconciliation/wait/noAmountQueryBuilder`,
            ignoreDefaultSearch: true,
            defaultRules: [],
            serializeList: (list) => {
              list.forEach((item) => {
                item.dataLength = '0'
              })
              return list
            }
          },
          // frozenColumns: 1,
          defaultSearchItem: [
            {
              field: 'companyCode', // 公司
              headerText: this.$t('公司')
            },
            {
              field: 'accountDate',
              headerText: this.$t('入账日期')
            }
          ]
        }
      }
    ]
    this.componentConfig = _config
  },
  methods: {
    // ToolBar
    handleClickToolBar(args) {
      const { gridRef, toolbar } = args
      const selectRows = gridRef.getMtechGridRecords()
      const tabIndex = this.$refs.templateRef.getCurrentTabRef().tabIndex
      const commonToolbar = [
        'SendNotify',
        'AddNotify',
        'UpdateNotify',
        'ImportNotify',
        'ExportNotify',
        'more-option-btn',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal',
        'excelExport'
      ]
      if (selectRows.length === 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedIds = []
      selectRows.forEach((item) => {
        selectedIds.push(item.id)
      })

      if (toolbar.id === 'CreateStatementDetails') {
        // 往来待对账明细信息-创建对账单
        if (
          !this.checkSelectedValid({
            selectedData: selectRows,
            actionType: this.actionType.createStatement
          })
        ) {
          this.$toast({
            content: this.$t('请选择相同公司、供应商、入账月份的数据'),
            type: 'warning'
          })

          return
        }
        const createStatementData = {
          headerInfo: {
            companyName: selectRows[0].companyName, // 公司名称
            companyCode: selectRows[0].companyCode, // 公司代码
            supplierName: selectRows[0].supplierName, // 供应商/客户名称
            supplierCode: selectRows[0].supplierCode, // 供应商/客户代码
            currencyName: selectRows[0].currencyName, // 币种
            currencyCode: selectRows[0].currencyCode, // 币种代码
            accountMonth: selectRows[0].accountMonth, // 入账月份
            accountYear: selectRows[0].accountYear, // 入账年份
            accountDate: Number(selectRows[0].accountDate)
              ? Number(selectRows[0].accountDate)
              : null, // 入账日期
            openingBalance: selectRows[0].openingBalance, // 期初余额
            closingBalance: null, // 期末余额 创建页计算 期初余额+未清账状态的往来对账明细+调整项金额
            remark: '' // 采方备注
          }, // 头部信息
          purchaseDetailsList: selectRows, // 采方明细表格数据
          entryType: EntryType.byDetails // 区分状态 勾选明细
        }
        // 将信息放到 localStorage 创建往来对账单-采方 读
        localStorage.setItem('createContactReconciledData', JSON.stringify(createStatementData))
        // 将 lastTabIndex 放到 localStorage 创建往来对账单-采方 读
        localStorage.setItem('lastTabIndex', JSON.stringify(tabIndex))
        // 跳转 创建往来对账单-采方
        this.$router.push({
          name: 'create-contact-reconciled',
          query: {}
        })
      } else if (toolbar.id === 'CreateStatement' || toolbar.id === 'CreateStatementNoExtra') {
        // 往来待对账-创建对账单

        this.$dialog({
          modal: () => import('./components/confirmDialog.vue'),
          data: {
            title: this.$t('提示'),
            message: this.$t('是否确认创建对账单？'),
            isExtra: toolbar.id === 'CreateStatementNoExtra' ? true : false
          },
          success: (res) => {
            const params = []
            selectRows.forEach((item) =>
              params.push({
                companyCode: item.companyCode, // 公司代码
                companyName: item.companyName, // 公司名称
                currencyCode: item.currencyCode, // 币种代码
                currencyName: item.currencyName, // 币种名称
                month: item.accountMonth, // 月份
                supplierCode: item.supplierCode, // 供应商代码
                supplierName: item.supplierName, // 供应商名称
                year: item.accountYear, // 年份
                autoInitialBalance: res, // 是否转结
                noAmountFlag: toolbar.id === 'CreateStatementNoExtra' ? true : false //无发生额标识
              })
            )
            this.doCreateStatement(params)
          }
        })
      } else if (toolbar.id === 'AddNotify') {
        // 对账通知-新增
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id === 'UpdateNotify') {
        // 对账通知-更新 结束编辑
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      } else if (toolbar.id === 'DeleteNotify') {
        // 对账通知-删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            this.handleDeleteNotify({ selectRows, selectedIds })
          }
        })
      } else if (toolbar.id === 'SendNotify') {
        // 对账通知-发送通知
        this.postTransactionReconciliationSupplierConfigSendNotice({
          ids: selectedIds
        })
      } else if (toolbar.id === 'ImportNotify') {
        // 对账通知-导入
        this.showUploadExcel(true)
      } else if (toolbar.id === 'ExportNotify') {
        // 对账通知-导出
        this.postTransactionReconciliationSupplierConfigDownload()
      } else if (toolbar.id == 'excelExport') {
        this.handleExport()
      }
    },
    // CellTitle
    handleClickCellTitle(args) {
      const { field, tabIndex, data } = args
      if (
        field === 'dataLength' &&
        (tabIndex === Tab.toBeReconciled || tabIndex === Tab.noExtraReconciled)
      ) {
        // 详情明细 && 往来待对账
        const createStatementData = {
          headerInfo: {
            companyName: data.companyName, // 公司名称
            companyCode: data.companyCode, // 公司代码
            supplierName: data.supplierName, // 供应商/客户名称
            supplierCode: data.supplierCode, // 供应商代码
            currencyName: data.currencyName, // 币种
            currencyCode: data.currencyCode, // 币种代码
            accountMonth: data.accountMonth, // 入账月份
            accountYear: data.accountYear, // 入账年份
            accountDate: Number(data.accountDate) ? Number(data.accountDate) : null, // 入账日期
            openingBalance: data.openingBalance, // 期初余额
            closingBalance: null, // 期末余额 创建页计算 期初余额+未清账状态的往来对账明细+调整项金额
            remark: '' // 采方备注
          }, // 头部信息
          purchaseDetailsList: [], // 采方明细表格数据
          entryType: EntryType.byDataClick // 区分状态 行数据点击
        }
        this.$dialog({
          modal: () => import('./components/confirmDialog.vue'),
          data: {
            title: this.$t('提示'),
            message: this.$t('是否跳转创建页面提交创建?'),
            isExtra: tabIndex === Tab.noExtraReconciled ? true : false
          },
          success: (res) => {
            let _commitData = {
              companyCode: data.companyCode,
              companyName: data.companyName,
              supplierCode: data.supplierCode,
              supplierName: data.supplierName,
              currencyCode: data.currencyCode,
              currencyName: data.currencyName,
              year: data.accountYear,
              month: data.accountMonth,
              autoInitialBalance: res,
              noAmountFlag: tabIndex === Tab.noExtraReconciled ? true : false
            }
            this.getClosingBalance(_commitData, createStatementData, tabIndex)
          }
        })
      }
    },
    getClosingBalance(data, createStatementData, tabIndex) {
      this.$API.reconciliationSettlement
        .postTransactionReconciliationPreSave(data)
        .then((res) => {
          if (res?.code == 200) {
            // 将 lastTabIndex 放到 localStorage 创建往来对账单-采方 读
            localStorage.setItem('lastTabIndex', JSON.stringify(tabIndex))
            // 将行信息放到 localStorage 创建往来对账单-采方 读
            localStorage.setItem('createContactReconciledData', JSON.stringify(createStatementData))
            localStorage.setItem('preSaveData', JSON.stringify(res?.data))
            // 跳转 创建往来对账单-采方
            this.$router.push({
              name: 'create-contact-reconciled',
              query: {}
            })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 往来待对账-创建对账单
    doCreateStatement(data) {
      this.apiStartLoading()
      // 租户级-往来对账单-采方-批量快速创建往来对账单并发布
      this.$API.reconciliationSettlement
        .postTransactionReconciliationBatchSave(data)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 对账通知-删除
    handleDeleteNotify(args) {
      const { selectedIds } = args
      // 租户级-往来对账通知配置-删除往来对账配置
      this.postTransactionReconciliationSupplierConfigDelete(selectedIds)
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 判断选择的数据是否符合要求
    checkSelectedValid(data) {
      const { selectedData: selectedList, actionType } = data
      let valid = true
      if (selectedList && selectedList.length > 0) {
        if (actionType === this.actionType.createStatement) {
          // 创建对账单
          for (let i = 0; i < selectedList.length - 1; i++) {
            // 公司code && 供应商code && 入账月份 相同
            if (
              selectedList[i + 1] &&
              (selectedList[i].companyCode !== selectedList[i + 1].companyCode ||
                selectedList[i].supplierCode !== selectedList[i + 1].supplierCode ||
                selectedList[i].accountMonth !== selectedList[i + 1].accountMonth)
            ) {
              valid = false
              break
            }
          }
        }
      }

      return valid
    },
    // actionBegin 表格编辑生命周期
    actionBegin(args) {
      const { requestType, action, rowData } = args
      // console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args);
      if (requestType === RequestType.add) {
        // 开始行添加
        this.isEditing = true
        // 即将添加一行，赋值新增行的初始数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        const newRowData = cloneDeep(NewRowData)
        rowDataTemp.push(newRowData)
        args.rowData = newRowData
        args.data = newRowData
      } else if (requestType === RequestType.save && action === ActionType.add) {
        // 即将保存新增时，将新增中编辑的数据付给组件
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === RequestType.beginEdit) {
        // 开始行编辑
        this.isEditing = true
        // 即将编辑行，赋值当前行的数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },
    // actionComplete 表格编辑生命周期
    actionComplete(args) {
      const { requestType, action, rowIndex, index } = args
      const rowData = rowDataTemp[rowDataTemp.length - 1]
      // console.log(
      //   `Complete,\nrequest: ${requestType}\naction: ${action}`,
      //   args
      // );
      if (requestType === RequestType.save && action == ActionType.edit) {
        // 完成编辑行
        this.isEditing = false
        // rowData, rowIndex
        if (!this.isValidSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API
          // 租户级-往来对账通知配置-保存往来对账配置
          this.postTransactionReconciliationSupplierConfigSave({
            rowData,
            rowIndex
          })
        }
      } else if (requestType === RequestType.save && action == ActionType.add) {
        // 完成新增行
        this.isEditing = false
        if (!this.isValidSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API
          // 租户级-往来对账通知配置-保存往来对账配置
          this.postTransactionReconciliationSupplierConfigSave({
            rowData,
            rowIndex: index
          })
        }
      }
    },
    // 校验数据
    isValidSaveData(data) {
      const { companyName, supplierName, reconciliationPerson } = data
      let valid = true
      if (!companyName) {
        this.$toast({ content: this.$t('请选择公司'), type: 'warning' })
        valid = false
      } else if (!supplierName) {
        this.$toast({
          content: this.$t('请选择供应商/客户编号'),
          type: 'warning'
        })
        valid = false
      } else if (!reconciliationPerson) {
        this.$toast({ content: this.$t('请选择对账人'), type: 'warning' })
        valid = false
      }

      return valid
    },
    // 租户级-往来对账通知配置-删除往来对账配置
    postTransactionReconciliationSupplierConfigDelete(params) {
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationSupplierConfigDelete(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-往来对账通知配置-保存往来对账配置
    postTransactionReconciliationSupplierConfigSave(args) {
      const { rowData, rowIndex } = args
      const params = [
        {
          ...rowData,
          thePrimaryKey: undefined
        }
      ]
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationSupplierConfigSave(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    // 租户级-往来对账通知配置-发送往来对账通知
    postTransactionReconciliationSupplierConfigSendNotice(params) {
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationSupplierConfigSendNotice(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-往来对账通知配置-往来对账配置下载
    postTransactionReconciliationSupplierConfigDownload() {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationSupplierConfigDownload(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
    },
    // 显示隐藏上传弹框
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 上传成功后
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 导出
    handleExport() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 10000 },
        pageFlag: true,
        rules: rule.rules || []
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement.reconciliationTransactionDownload(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped></style>
