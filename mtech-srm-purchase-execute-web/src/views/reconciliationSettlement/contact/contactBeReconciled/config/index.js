// import { i18n } from "@/main.js";
import {
  Tab,
  ContactsTypeOptions,
  CleanStatusOptions,
  ReconciliationStatusOptions,
  ReconciliationStatus,
  ReconciliationStatusText
} from './constant'
import { ColumnComponent as Component } from './columnComponent'
import { codeNameColumn } from '@/utils/utils'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'

// 格式化表格动态数据
export const formatTableColumnData = (args) => {
  const { data, tab } = args
  const colData = []
  if (tab === Tab.toBeReconciledDetails) {
    // 往来待对账明细
    data.forEach((col) => {
      const defaultCol = {
        ...col,
        field: col.fieldCode,
        headerText: col.fieldName,
        width: '150'
      }
      if (col.fieldCode === 'type') {
        // 往来待对账明细 往来类别
        defaultCol.valueConverter = {
          type: 'map',
          map: ContactsTypeOptions
        }
      } else if (col.fieldCode === 'accountDate') {
        // 往来待对账明细 入账日期
        defaultCol.template = Component.timeDate({
          dataKey: col.fieldCode,
          hasTime: false
        })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (col.fieldCode === 'cleanStatus') {
        // 往来待对账明细 清账状态
        defaultCol.valueConverter = {
          type: 'map',
          map: CleanStatusOptions
        }
      } else if (col.fieldCode === 'companyCode') {
        // 公司
        // code-name 形式
        defaultCol.width = '300'
        defaultCol.template = codeNameColumn({
          firstKey: 'companyCode',
          secondKey: 'companyName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        // 主数据选择器
        defaultCol.searchOptions = {
          ...MasterDataSelect.businessCompany,
          placeholder: i18n.t('公司')
        }
      } else if (col.fieldCode === 'supplierCode') {
        // 供应商
        // code-name 形式
        defaultCol.width = '400'
        defaultCol.template = codeNameColumn({
          firstKey: 'supplierCode',
          secondKey: 'supplierName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        // 主数据选择器
        defaultCol.searchOptions = {
          ...MasterDataSelect.supplierAll,
          placeholder: i18n.t('供应商')
        }
      } else if (col.fieldCode === 'theOperationDate') {
        defaultCol.searchOptions = {
          ...MasterDataSelect.dateRange
        }
      }
      colData.push(defaultCol)
    })
  } else if (tab === Tab.toBeReconciled || tab === Tab.noExtraReconciled) {
    // 往来待对账
    data.forEach((col) => {
      const defaultCol = {
        ...col,
        field: col.fieldCode,
        headerText: col.fieldName,
        width: 'auto'
      }
      if (col.fieldCode === 'dataLength') {
        // 往来待对账 详情明细
        defaultCol.cellTools = [] // 使其可点击查看
      } else if (col.fieldCode === 'accountDate') {
        defaultCol.headerText = '入账日期'
        defaultCol.template = Component.timeDate({
          dataKey: col.fieldCode,
          hasTime: false,
          format: 'YYYY-mm'
        })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (col.fieldCode === 'companyCode') {
        // 公司
        // code-name 形式
        defaultCol.width = '300'
        defaultCol.template = codeNameColumn({
          firstKey: 'companyCode',
          secondKey: 'companyName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        // 主数据选择器
        defaultCol.searchOptions = {
          ...MasterDataSelect.businessCompany,
          placeholder: i18n.t('公司')
        }
      } else if (col.fieldCode === 'supplierCode') {
        // 供应商
        // code-name 形式
        defaultCol.width = '400'
        defaultCol.template = codeNameColumn({
          firstKey: 'supplierCode',
          secondKey: 'supplierName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        // 主数据选择器
        defaultCol.searchOptions = {
          ...MasterDataSelect.supplierAll,
          placeholder: i18n.t('供应商')
        }
      }
      colData.push(defaultCol)
    })
  }

  return colData
}

// 对账通知 表格数据
export const formatNotifyTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: 'auto'
    }
    if (col.fieldCode === 'thePrimaryKey') {
      // 行主键
      defaultCol.visible = false
      defaultCol.isIdentity = true // 加上这个，新增一行的时候，主键不会变成可输入
      defaultCol.isPrimaryKey = true
      defaultCol.allowEditing = false
    } else if (col.fieldCode === 'checkBox') {
      // checkBox
      defaultCol.type = 'checkbox'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.showInColumnChooser = false
      defaultCol.width = '50'
      // 编辑时显示
      // defaultCol.editTemplate = Component.empty;
    } else if (col.fieldCode === 'serialNumber') {
      // 序号
      defaultCol.width = '80'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.ignore = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'companyCode') {
      // 公司编码 可编辑
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '350'
      defaultCol.allowEditing = true
      defaultCol.allowFiltering = false
      defaultCol.ignore = false
      defaultCol.template = codeNameColumn({
        firstKey: 'companyCode',
        secondKey: 'companyName'
      })
      defaultCol.editTemplate = Component.companySelect
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessCompany,
        placeholder: i18n.t('公司')
      }
    } else if (col.fieldCode === 'supplierCode') {
      // 供应商/客户编号 可编辑
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '350'
      defaultCol.allowEditing = true
      defaultCol.allowFiltering = false
      defaultCol.ignore = false
      defaultCol.template = codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      })
      defaultCol.editTemplate = Component.supplierSelect
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.supplierAll,
        placeholder: i18n.t('供应商/客户')
      }
    } else if (col.fieldCode === 'reconciliationStatus') {
      // 允许本月对账 可编辑
      defaultCol.width = '150'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode,
        valueConverter: {
          type: 'map',
          map: ReconciliationStatusOptions
        }
      })
      defaultCol.editTemplate = Component.switch({
        dataKey: col.fieldCode,
        onLabel: ReconciliationStatusText[ReconciliationStatus.allow],
        offLabel: ReconciliationStatusText[ReconciliationStatus.notAllowed],
        activeValue: ReconciliationStatus.allow,
        inactiveValue: ReconciliationStatus.notAllowed
      })
    } else if (col.fieldCode === 'reconciliationPerson') {
      // 对账人 可编辑
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '350'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.reconciliationPersonSelect
    } else if (col.fieldCode === 'reconciliationPersonEmail') {
      // 对账人邮箱 不可编辑
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '350'
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'updateUserName') {
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.showInColumnChooser = false
      defaultCol.width = '150'
    } else if (col.fieldCode === 'updateTime') {
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.showInColumnChooser = false
      defaultCol.width = '150'
      defaultCol.ignore = true
      defaultCol.searchOptions = MasterDataSelect.dateRange
    }
    colData.push(defaultCol)
  })

  return colData
}

// 添加 操作日期 字段到列表中
export const addTheOperationDateToList = (list) => {
  if (list.length > 0) {
    // 操作日期 前端自定义 updateTime更新时间为空时显示createTime创建时间
    list.forEach((item) => {
      let theOperationDate = item.createTime
      if (!item.updateTime || item.updateTime != 0) {
        // 数据库时间戳默认值为 0，为 0 时即为空
        theOperationDate = item.updateTime
      }
      item.theOperationDate = theOperationDate
    })
  }
  return list
}

// 对账通知 序列化
export const serializeNotifyList = (list) => {
  if (list.length > 0) {
    list.forEach((item, index) => {
      item.serialNumber = index + 1 // 序号
      item.thePrimaryKey = item.id // 主键
    })
  }
  return list
}

// 验证 状态是否统一
export const verifyStatus = (data) => {
  let valid = true
  let status = ''
  for (let i = 0; i < data.length; i++) {
    status = data[i].status
    if (data[i] && data[i - 1] && data[i].status !== data[i - 1].status) {
      valid = false
      break
    }
  }

  return { valid, status }
}
