import Vue from 'vue'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { rowDataTemp } from './variable'
import { cloneDeep } from 'lodash'
import { ComponentChangeType } from './constant'
import { addCodeNameKeyInList } from '@/utils/utils'
import { utils } from '@mtech-common/utils'

export const ColumnComponent = {
  // 时间日期显示
  timeDate: (args) => {
    let { dataKey, hasTime, format } = args
    if (!format) format = 'YYYY-mm-dd'
    const template = () => {
      return {
        template: Vue.component('date', {
          template: `<div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div><div>{{data[dataKey] | dateFormat}}</div></div>`,
          data: function () {
            return { data: {}, dataKey, hasTime }
          },
          filters: {
            dateFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({ formatString: format, value })
              } else {
                str = timeNumberToDate({ formatString: format, value })
              }

              return str
            },
            timeFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({ formatString: 'HH:MM:SS', value })
              } else {
                str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
              }

              return str
            }
          }
        })
      }
    }

    return template
  },
  // 空的显示
  empty: () => {
    return {
      template: Vue.component('emptyComponent', {
        template: `<div></div>`,
        data: function () {
          return {
            data: {}
          }
        }
      })
    }
  },
  // 不可编辑的文字显示
  text: (args) => {
    const { dataKey, cellTools, valueConverter } = args
    const template = () => {
      return {
        template: Vue.component('textComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">{{data[dataKey] | format}}</div>
            <div class="column-tool mt-flex invite-btn" v-if="haveShowCellTool">
              <div v-for="(cellTool, index) in cellTools"
                :key="index" :id="cellTool.id"
                class="template-svg"
                v-if="!cellTool.visibleCondition || cellTool.visibleCondition(data)"
                @click.stop="clickCellTool({tool:cellTool, data})"
                ><MtIcon :name="cellTool.icon" />&nbsp<span class="icon-title">{{cellTool.title}}</span>
              </div>
            </div>
          </div>`,
          data: function () {
            return { data: {}, dataKey, cellTools, haveShowCellTool: false }
          },
          mounted() {
            if (cellTools?.length > 0) {
              for (let i = 0; i < cellTools.length; i++) {
                const cellTool = cellTools[i]
                if (!cellTool.visibleCondition || cellTool.visibleCondition(this.data)) {
                  this.haveShowCellTool = true
                  break
                }
              }
            }
          },
          filters: {
            format: (value) => {
              let data = value
              if (valueConverter?.type === 'map' && valueConverter?.map?.length) {
                // 转换
                const mapList = valueConverter.map
                const findItem = mapList.find((item) => item.value === value)
                data = findItem?.text
              }
              return data
            }
          },
          methods: {
            clickCellTool(data) {
              this.$parent.$emit('handleClickCellTool', data)
            }
          }
        })
      }
    }
    return template
  },
  // 文本 下拉选择时，带出的数据
  changedText: (args) => {
    const { dataKey, showClearBtn } = args
    const template = () => {
      return {
        template: Vue.component('changedTextComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">{{data[dataKey]}}</div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              showClearBtn
            }
          },
          mounted() {
            // 监听变化
            this.onComponentChange()
          },
          beforeDestroy() {
            this.$bus.$off('contactBeReconciledColumnChange')
          },
          methods: {
            // 监听变化
            onComponentChange() {
              // 监听被变化
              this.$bus.$on(`contactBeReconciledColumnChange`, (e) => {
                const { modifiedKeys, data, modifiedRelation } = e
                if (modifiedKeys.includes(dataKey)) {
                  // 发布事件的数据修改了，关联值修改
                  let newData = null
                  if (data && data[modifiedRelation[dataKey]]) {
                    newData = cloneDeep(data[modifiedRelation[dataKey]])
                  }
                  this.data[dataKey] = newData
                  rowDataTemp[rowDataTemp.length - 1][dataKey] = newData
                }
              })
            }
          }
        })
      }
    }
    return template
  },
  // 下拉框 编辑
  select: (args) => {
    const {
      dataKey,
      selectOptions,
      fields,
      allowFiltering,
      showClearBtn,
      modifiedKeys,
      modifiedRelation
    } = args
    const template = () => {
      return {
        template: Vue.component('selectComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-select
                @change="selectChange"
                v-model="data[dataKey]"
                :allow-filtering="allowFiltering"
                :data-source="selectOptions"
                :show-clear-button="showClearBtn"
                :placeholder="$t('请选择')"
                :fields="fields"
              ></mt-select>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              selectOptions,
              fields,
              dataKey,
              allowFiltering,
              showClearBtn,
              ComponentChangeType
            }
          },
          mounted() {},
          methods: {
            // 修改中的值
            selectChange(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e.value
              // 触发改变的值
              this.triggerCodeChange(e.itemData)
            },
            // 触发改变的值
            triggerCodeChange(selectData) {
              const args = {
                requestKey: dataKey, // 触发请求的key
                modifiedKeys: modifiedKeys || [], // 要被修改的key列表
                changeType: ComponentChangeType.code, // 修改类型
                data: selectData, // 发出的值
                modifiedRelation // 对应数据源中的 key 关系
              }
              this.$bus.$emit('contactBeReconciledColumnChange', args)
            }
          }
        })
      }
    }
    return template
  },
  // 开关按钮
  switch: (args) => {
    const { dataKey, onLabel, offLabel, activeValue, inactiveValue } = args
    const template = () => {
      return {
        template: Vue.component('selectComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-switch
                @change="switchChange"
                v-model="data[dataKey]"
                :on-label="onLabel"
                :off-label="offLabel"
                :active-value="activeValue"
                :inactive-value="inactiveValue"
              ></mt-switch>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              onLabel,
              offLabel,
              activeValue,
              inactiveValue,
              ComponentChangeType
            }
          },
          mounted() {},
          methods: {
            // 修改中的值
            switchChange(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e
            }
          }
        })
      }
    }
    return template
  },
  // 对账人
  reconciliationPersonSelect: () => {
    return {
      template: Vue.component('reconciliationPersonSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.reconciliationPerson"
              :filtering="doGetDataSource"
              :data-source="reconciliationPersonOptions"
              :fields="{ text: 'theCodeName', value: 'employeeName' }"
              :show-clear-button="true"
              :allow-filtering="true"
              @change="reconciliationPersonChange"
              :open-dispatch-change="true"
              :placeholder="$t('请选择')"
            ></mt-select>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            reconciliationPersonOptions: [], // 对账人 下拉选项
            doGetDataSource: () => {}
          }
        },
        mounted() {
          this.initGetReconciliationPerson()
          this.doGetDataSource = utils.debounce(this.getReconciliationPerson, 1000)
        },
        methods: {
          // 主数据 获取对账人
          getReconciliationPerson(args) {
            const { text, updateData, setSelectData } = args
            const params = {
              fuzzyName: text
            }
            this.$API.masterData.getCurrentTenantEmployees(params).then((res) => {
              if (res) {
                const list = res?.data || []
                this.reconciliationPersonOptions = addCodeNameKeyInList({
                  firstKey: 'companyOrgName',
                  secondKey: 'departmentOrgName',
                  thirdKey: 'employeeCode',
                  fourthKey: 'employeeName',
                  list
                })
                if (updateData) {
                  this.$nextTick(() => {
                    updateData(this.reconciliationPersonOptions)
                  })
                }
                if (setSelectData) {
                  this.$nextTick(() => {
                    setSelectData()
                  })
                }
              }
            })
          },
          // 初始化检索 对账人
          initGetReconciliationPerson() {
            const selectData = this.data.reconciliationPerson
            this.getReconciliationPerson({
              text: selectData,
              setSelectData: () => {
                // api获取数据后重新赋值，防止没有赋上值得情况
                this.data.reconciliationPerson = selectData
              }
            })
          },
          // 对账人 change
          reconciliationPersonChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].reconciliationPerson = itemData.employeeName
              rowDataTemp[rowDataTemp.length - 1].reconciliationPersonCode = itemData.employeeCode
              rowDataTemp[rowDataTemp.length - 1].reconciliationPersonEmail = itemData.email
            } else {
              rowDataTemp[rowDataTemp.length - 1].reconciliationPerson = null
              rowDataTemp[rowDataTemp.length - 1].reconciliationPersonCode = null
              rowDataTemp[rowDataTemp.length - 1].reconciliationPersonEmail = null
            }
          }
        }
      })
    }
  },
  // 供应商
  supplierSelect: () => {
    return {
      template: Vue.component('supplierSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.supplierCode"
              :filtering="doGetDataSource"
              :data-source="supplierOptions"
              :fields="{ text: 'theCodeName', value: 'supplierCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              @change="supplierCodeChange"
              :open-dispatch-change="true"
              :placeholder="$t('请选择')"
            ></mt-select>
          </div>
        </div>`,
        data() {
          return {
            data: {},
            supplierOptions: [], // 供应商 下拉选项
            doGetDataSource: () => {}
          }
        },
        mounted() {
          this.initGetSupplier()
          this.doGetDataSource = utils.debounce(this.getSupplier, 1000)
        },
        methods: {
          // 主数据 获取供应商
          getSupplier(args) {
            const { text, updateData, setSelectData } = args
            const params = {
              fuzzyNameOrCode: text
            }
            this.$API.masterData
              .getSupplier(params)
              .then((res) => {
                if (res) {
                  const list = res?.data || []
                  this.supplierOptions = addCodeNameKeyInList({
                    firstKey: 'supplierCode',
                    secondKey: 'supplierName',
                    list
                  })
                  if (updateData) {
                    this.$nextTick(() => {
                      updateData(this.supplierOptions)
                    })
                  }
                  if (setSelectData) {
                    this.$nextTick(() => {
                      setSelectData()
                    })
                  }
                }
              })
              .catch(() => {})
          },
          // 初始化检索 供应商
          initGetSupplier() {
            const selectData = this.data.supplierCode
            this.getSupplier({
              text: selectData,
              setSelectData: () => {
                // api获取数据后重新赋值，防止没有赋上值得情况
                this.data.supplierCode = selectData
              }
            })
          },
          // 供应商 change
          supplierCodeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].supplierCode = itemData.supplierCode
              rowDataTemp[rowDataTemp.length - 1].supplierName = itemData.supplierName
            } else {
              rowDataTemp[rowDataTemp.length - 1].supplierCode = null
              rowDataTemp[rowDataTemp.length - 1].supplierName = null
            }
          }
        }
      })
    }
  },
  // 公司
  companySelect: () => {
    return {
      template: Vue.component('companySelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.companyCode"
              :filtering="doGetDataSource"
              :data-source="companyOptions"
              :fields="{ text: 'theCodeName', value: 'orgCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              @change="companyCodeChange"
              :open-dispatch-change="true"
              :placeholder="$t('请选择')"
            ></mt-select>
          </div>
        </div>`,

        data() {
          return {
            data: {},
            companyOptions: [], // 公司 下拉选项
            doGetDataSource: () => {}
          }
        },
        mounted() {
          this.initGetCompany()
          this.doGetDataSource = utils.debounce(this.getCompany, 1000)
        },
        methods: {
          // 主数据 获取公司
          getCompany(args) {
            const { text, updateData, setSelectData } = args
            this.$API.masterData
              .OrgFindSpecifiedChildrenLevelOrgs({
                fuzzyParam: text || '',
                organizationLevelCodes: ['ORG02', 'ORG01'],
                orgType: 'ORG001PRO',
                includeItself: true,
                organizationIds: []
              })
              .then((res) => {
                const list = res?.data || []
                this.companyOptions = addCodeNameKeyInList({
                  firstKey: 'orgCode',
                  secondKey: 'orgName',
                  list
                })
                if (updateData) {
                  this.$nextTick(() => {
                    updateData(this.companyOptions)
                  })
                }
                if (setSelectData) {
                  this.$nextTick(() => {
                    setSelectData()
                  })
                }
              })
          },
          // 初始化检索 公司
          initGetCompany() {
            const selectData = this.data.companyCode
            this.getCompany({
              text: selectData,
              setSelectData: () => {
                // api获取数据后重新赋值，防止没有赋上值得情况
                this.data.companyCode = selectData
              }
            })
          },
          // 公司 change
          companyCodeChange(e) {
            const { itemData } = e
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].companyCode = itemData.orgCode
              rowDataTemp[rowDataTemp.length - 1].companyName = itemData.orgName
            } else {
              rowDataTemp[rowDataTemp.length - 1].companyCode = null
              rowDataTemp[rowDataTemp.length - 1].companyName = null
            }
          }
        }
      })
    }
  },
  // 带红星的表头
  requiredHeader: (args) => {
    const { headerText } = args
    const template = () => {
      return {
        template: Vue.component('requiredHeaderComponent', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{ headerText }}</span>
              </div>
            `,
          data: function () {
            return {
              data: {},
              headerText
            }
          },
          beforeDestroy() {},
          mounted() {},
          methods: {}
        })
      }
    }
    return template
  }
}
