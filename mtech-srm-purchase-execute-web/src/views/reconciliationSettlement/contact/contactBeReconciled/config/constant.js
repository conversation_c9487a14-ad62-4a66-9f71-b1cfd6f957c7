import { i18n } from '@/main.js'

// tab
export const Tab = {
  toBeReconciledDetails: 0, // 往来待对账明细
  toBeReconciled: 1, // 往来待对账
  notify: 2, // 对账通知
  noExtraReconciled: 3 // 无发生额外往来对账单
}

// 表格编辑 的 RequestType
export const RequestType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  sorting: 'sorting',
  refresh: 'refresh'
}

// 表格编辑 的 action
export const ActionType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  edit: 'edit'
}

// 行编辑触发的 change 事件类型
export const ComponentChangeType = {
  code: 'code', // 例：下拉框选择“供应商”，供应商编码（code)要被修改
  link: 'link' // 例：计划组：根据物料+工厂+计划组的业务组类型 code 带出
}

// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  companyCode: null, // 公司编号
  companyName: null, // 	公司名称
  reconciliationPerson: null, // 对账人
  reconciliationPersonCode: null, // 对账人代码
  reconciliationStatus: 0, // 允许本月对账 0-不允许；1-允许
  supplierCode: null, // 供应商/客户编号
  supplierName: null // 	供应商/客户名称
}

// 表格 EditSettings
export const EditSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
} // 编辑设置

// 对账通知 toolbar
export const NotifyToolbar = [
  {
    id: 'AddNotify',
    icon: 'icon_table_new',
    title: i18n.t('新建'),
    permission: ['O_02_0410']
  },
  {
    id: 'DeleteNotify',
    icon: 'icon_table_delete',
    title: i18n.t('删除'),
    permission: ['O_02_0411']
  },
  {
    id: 'UpdateNotify',
    icon: 'icon_table_save',
    title: i18n.t('更新'),
    permission: ['O_02_0410']
  },
  {
    id: 'SendNotify',
    icon: 'icon_table_UpgradeAppointment',
    title: i18n.t('发送通知'),
    permission: ['O_02_0412']
  },
  {
    id: 'ImportNotify',
    icon: 'icon_solid_Import',
    title: i18n.t('导入'),
    permission: ['O_02_0413']
  },
  {
    id: 'ExportNotify',
    icon: 'icon_solid_export',
    title: i18n.t('导出'),
    permission: ['O_02_0414']
  }
]

// 允许本月对账 0-不允许；1-允许
export const ReconciliationStatus = {
  notAllowed: 0, // 不允许
  allow: 1 // 允许
}

// 允许本月对账 text
export const ReconciliationStatusText = {
  [ReconciliationStatus.notAllowed]: i18n.t('不允许'),
  [ReconciliationStatus.allow]: i18n.t('允许')
}

// 允许本月对账 对应的 css class
export const ReconciliationStatusCssClass = {
  [ReconciliationStatus.notAllowed]: '',
  [ReconciliationStatus.allow]: ''
}

// 允许本月对账 对应的 Options
export const ReconciliationStatusOptions = [
  {
    // 不允许
    value: ReconciliationStatus.notAllowed,
    text: ReconciliationStatusText[ReconciliationStatus.notAllowed],
    cssClass: ReconciliationStatusCssClass[ReconciliationStatus.notAllowed]
  },
  {
    // 允许
    value: ReconciliationStatus.allow,
    text: ReconciliationStatusText[ReconciliationStatus.allow],
    cssClass: ReconciliationStatusCssClass[ReconciliationStatus.allow]
  }
]

// 往来类别 0-对账；1-索赔；2-返利
// export const ContactsType = {
//   recon: 0, // 对账
//   claim: 1, // 索赔
//   rebate: 2, // 返利
//   other: 9, // 其他
// };

// 往来类别
export const ContactsType = {
  bzj: 3, // 保证金
  xsfp: 4, // 开发票给TCl
  kk: 5, // 扣款
  sk: 6, // 收款
  kfp: 7, // 收到TCL开具的发票
  other: 9 // 其他
}

// 往来类别 text
export const ContactsTypeText = {
  [ContactsType.bzj]: i18n.t('保证金'),
  [ContactsType.xsfp]: i18n.t('开发票给TCL'),
  [ContactsType.kk]: i18n.t('扣款'),
  [ContactsType.sk]: i18n.t('收款'),
  [ContactsType.kfp]: i18n.t('收到TCL开具的发票'),
  [ContactsType.other]: i18n.t('其他')
}

// 往来类别 对应的 css class
export const ContactsTypeCssClass = {
  [ContactsType.bzj]: '',
  [ContactsType.xsfp]: '',
  [ContactsType.kk]: '',
  [ContactsType.sk]: '',
  [ContactsType.kfp]: '',
  [ContactsType.other]: ''
}

// 往来类别 对应的 Options
export const ContactsTypeOptions = [
  {
    // 保证金
    value: ContactsType.bzj,
    text: ContactsTypeText[ContactsType.bzj],
    cssClass: ContactsTypeCssClass[ContactsType.bzj]
  },
  {
    // 开发票给TCL
    value: ContactsType.xsfp,
    text: ContactsTypeText[ContactsType.xsfp],
    cssClass: ContactsTypeCssClass[ContactsType.xsfp]
  },
  {
    // 扣款
    value: ContactsType.kk,
    text: ContactsTypeText[ContactsType.kk],
    cssClass: ContactsTypeCssClass[ContactsType.kk]
  },
  {
    // 收款
    value: ContactsType.sk,
    text: ContactsTypeText[ContactsType.sk],
    cssClass: ContactsTypeCssClass[ContactsType.sk]
  },
  {
    // 收到TCL开具的发票
    value: ContactsType.kfp,
    text: ContactsTypeText[ContactsType.kfp],
    cssClass: ContactsTypeCssClass[ContactsType.kfp]
  },
  {
    // 其他
    value: ContactsType.other,
    text: ContactsTypeText[ContactsType.other],
    cssClass: ContactsTypeCssClass[ContactsType.other]
  }
]

// 清账状态 0未清 1已清
export const CleanStatus = {
  notYet: '0', // 未清
  already: '1' // 已清
}

// 清账状态 text
export const CleanStatusText = {
  [CleanStatus.notYet]: i18n.t('未清'),
  [CleanStatus.already]: i18n.t('已清')
}

// 清账状态 对应的 css class
export const CleanStatusCssClass = {
  [CleanStatus.notYet]: '',
  [CleanStatus.already]: ''
}

// 清账状态 对应的 Options
export const CleanStatusOptions = [
  {
    // 未清
    value: CleanStatus.notYet,
    text: CleanStatusText[CleanStatus.notYet],
    cssClass: CleanStatusCssClass[CleanStatus.notYet]
  },
  {
    // 已清
    value: CleanStatus.already,
    text: CleanStatusText[CleanStatus.already],
    cssClass: CleanStatusCssClass[CleanStatus.already]
  }
]

// 往来待对账明细 表格列数据
export const ToBeReconciledDetailsColumnData = [
  {
    fieldCode: 'companyCode', // 公司
    fieldName: i18n.t('公司')
    // companyName
  },
  {
    fieldCode: 'supplierCode', // 供应商
    fieldName: i18n.t('供应商')
    // supplierName
  },
  {
    fieldCode: 'type', // 往来类别 往来类别 0-对账；1-索赔；2-返利
    fieldName: i18n.t('往来类别')
  },
  {
    fieldCode: 'docNo', // 单据号
    fieldName: i18n.t('单据号')
  },
  {
    fieldCode: 'accountTypeName', // 科目类别
    fieldName: i18n.t('科目类别')
    // accountTypeCode	科目类别编码
  },
  {
    fieldCode: 'reconciliationAccount', // 科目编号 对账科目
    fieldName: i18n.t('科目编号')
  },
  {
    fieldCode: 'reconciliationAccountName', // 科目 对账科目名称
    fieldName: i18n.t('科目')
  },
  {
    fieldCode: 'accountDate', // 入账日期
    fieldName: i18n.t('入账日期')
  },
  {
    fieldCode: 'invoiceNo', // 发票号
    fieldName: i18n.t('发票号')
  },
  {
    fieldCode: 'amount', // 金额
    fieldName: i18n.t('金额')
  },
  {
    fieldCode: 'currencyName', // 币种
    fieldName: i18n.t('币种')
    // 币种编码 currencyCode
  },
  {
    fieldCode: 'theOperationDate', // 操作日期 前端自定义 updateTime更新时间为空时显示createTime创建时间
    fieldName: i18n.t('操作日期')
  }
  // {
  //   fieldCode: "cleanStatus", // 清账状态 0未清 1已清
  //   fieldName: i18n.t("清账状态"),
  // },
]

// 往来待对账 表格列数据
export const ToBeReconciledColumnData = [
  {
    fieldCode: 'companyCode', // 公司
    fieldName: i18n.t('公司')
    // companyName
  },
  {
    fieldCode: 'supplierCode', // 供应商
    fieldName: i18n.t('供应商')
    // supplierName
  },
  {
    fieldCode: 'accountDate', // 入账截止日期 accountDate入账日期 accountMonth入账月份 accountYear入账年份
    fieldName: i18n.t('入账截止日期')
  },
  {
    fieldCode: 'dataLength', // 详情明细
    fieldName: i18n.t('详情明细'),
    ignore: true
  },
  {
    fieldCode: 'reconciliationPerson', // 对账人
    fieldName: i18n.t('对账人')
  },
  {
    fieldCode: 'reconciliationFlag', // 对账人
    fieldName: i18n.t('是否可对账'),
    valueConverter: {
      type: 'map', //(map为数组对象)：此时，fields默认值为{ text: 'text', value: 'value' }
      map: [
        { id: 0, title: '是', cssClass: 'status-open' }, //通过cssClass控制单项的样式
        { id: 1, title: '否', cssClass: ['status-close'] }
      ],
      fields: { text: 'title', value: 'id' }
    }
  }
]

// 对账通知 表格列数据
export const NotifyColumnData = [
  {
    fieldCode: 'checkBox' // 不可编辑
  },
  {
    fieldCode: 'thePrimaryKey'
  },
  {
    fieldCode: 'serialNumber', // 前端定义,不可编辑
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'companyCode', // 公司 可编辑
    fieldName: i18n.t('公司')
  },
  // {
  //   fieldCode: "companyName", // 公司名称 可编辑
  //   fieldName: i18n.t("公司名称"),
  // },
  {
    fieldCode: 'supplierCode', // 供应商/客户 可编辑
    fieldName: i18n.t('供应商/客户')
  },
  // {
  //   fieldCode: "supplierName", // 供应商/客户名称 可编辑
  //   fieldName: i18n.t("供应商/客户名称"),
  // },
  {
    fieldCode: 'reconciliationStatus', // 允许本月对账 可编辑
    fieldName: i18n.t('允许本月对账')
    // 允许本月对账 0-不允许；1-允许
  },
  {
    fieldCode: 'reconciliationPerson', // 对账人 可编辑
    fieldName: i18n.t('对账人')
    // reconciliationPersonCode	对账人代码
  },
  {
    fieldCode: 'reconciliationPersonEmail', // 对账人邮箱 可编辑
    fieldName: i18n.t('对账人邮箱')
  },
  {
    fieldCode: 'updateUserName', // 更新人 不可可编辑
    fieldName: i18n.t('更新人')
  },
  {
    fieldCode: 'updateTime', // 更新时间 不可可编辑
    fieldName: i18n.t('更新时间')
  }
]
