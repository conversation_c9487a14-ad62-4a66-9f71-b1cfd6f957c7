import { i18n } from '@/main.js'

// tab
export const Tab = {
  reconciled: 0, // 往来对账单列表
  reconciledDetails: 1 // 往来对账单明细
}
// toolbar 操作按钮
export const Toolbar = [
  {
    id: 'ConfirmStatement',
    icon: 'icon_solid_Createorder',
    title: i18n.t('采购批量确认'),
    permission: ['O_02_0415'] //采购批量确认
  },
  {
    id: 'BackStatement',
    icon: 'icon_solid_Createorder',
    title: i18n.t('采购批量退回'),
    permission: ['O_02_0415'] //采购批量退回
  },
  {
    id: 'ConfirmStatement1',
    icon: 'icon_solid_Createorder',
    title: i18n.t('财务批量确认'),
    permission: ['O_02_1312'] //财务确认
  },
  // {
  //   id: "printRecon",
  //   icon: "icon_table_print",
  //   title: i18n.t("打印"),
  // },
  {
    id: 'ExcelExport',
    icon: 'icon_solid_export',
    title: i18n.t('导出')
  },
  {
    id: 'PatchDelete',
    title: i18n.t('关闭')
  }
]
// 单元格操作按钮
export const CellTools = [
  {
    id: 'ConfirmStatement',
    icon: '',
    title: i18n.t('采购确认'),
    permission: ['O_02_0415'],
    visibleCondition: (data) => data.status == ReconciledStatus.normal // 反馈正常
  },
  {
    id: 'ConfirmStatement1',
    icon: '',
    title: i18n.t('财务确认'),
    permission: ['O_02_1312'],
    visibleCondition: (data) => data.status == ReconciledStatus.confirmed // 采购已确认
  },
  {
    id: 'ContinueStatement',
    icon: '',
    title: i18n.t('继续发布'),
    permission: ['O_02_0417'],
    visibleCondition: (data) => data.status == ReconciledStatus.abnormal // 反馈异常
  },
  {
    id: 'CloseStatement',
    icon: '',
    title: i18n.t('关闭'),
    permission: ['O_02_0416'],
    visibleCondition: (data) =>
      data.status == ReconciledStatus.new ||
      data.status == ReconciledStatus.abnormal ||
      data.status == ReconciledStatus.pending // 新建 反馈异常 待反馈
  },
  {
    id: 'BackStatement',
    icon: '',
    title: i18n.t('采购退回'),
    permission: ['O_02_1318'],
    visibleCondition: (data) =>
      data.status == ReconciledStatus.normal || data.status == ReconciledStatus.abnormal // 反馈正常 反馈异常
  },
  {
    id: 'BackStatement1',
    icon: '',
    title: i18n.t('财务退回'),
    permission: ['O_02_1319'],
    visibleCondition: (data) =>
      data.status == ReconciledStatus.confirmed || data.status == ReconciledStatus.finished // 采购已确认 || 已完结
  }
]

// 明细列表：单元格操作按钮
export const CellTools2 = [
  {
    id: 'ConfirmStatement',
    icon: '',
    title: i18n.t('确认'),
    permission: ['O_02_0419'],
    visibleCondition: (data) => data.status == ReconciledStatus.normal // 反馈正常
  },
  {
    id: 'ContinueStatement',
    icon: '',
    title: i18n.t('继续发布'),
    permission: ['O_02_0421'],
    visibleCondition: (data) => data.status == ReconciledStatus.abnormal // 反馈异常
  },
  {
    id: 'CloseStatement',
    icon: '',
    title: i18n.t('关闭'),
    permission: ['O_02_0420'],
    visibleCondition: (data) =>
      data.status == ReconciledStatus.new ||
      data.status == ReconciledStatus.pending ||
      data.status == ReconciledStatus.normal ||
      data.status == ReconciledStatus.abnormal // 新建 待反馈 反馈正常 反馈异常
  }
]

// 往来类别 0-对账；1-索赔；2-返利
// export const ContactsType = {
//   recon: 0, // 对账
//   claim: 1, // 索赔
//   rebate: 2, // 返利
// };

// 往来类别
export const ContactsType = {
  bzj: 3, // 保证金
  xsfp: 4, // 开发票给TCl
  kk: 5, // 扣款
  sk: 6, // 收款
  kfp: 7, // 收到TCL开具的发票
  other: 9 // 其他
}

// 往来类别 text
export const ContactsTypeText = {
  [ContactsType.bzj]: i18n.t('保证金'),
  [ContactsType.xsfp]: i18n.t('开发票给TCL'),
  [ContactsType.kk]: i18n.t('扣款'),
  [ContactsType.sk]: i18n.t('收款'),
  [ContactsType.kfp]: i18n.t('收到TCL开具的发票'),
  [ContactsType.other]: i18n.t('其他')
}

// 往来类别 对应的 css class
export const ContactsTypeCssClass = {
  [ContactsType.bzj]: '',
  [ContactsType.xsfp]: '',
  [ContactsType.kk]: '',
  [ContactsType.sk]: '',
  [ContactsType.kfp]: '',
  [ContactsType.other]: ''
}

// 往来类别 对应的 Options
export const ContactsTypeOptions = [
  {
    // 保证金
    value: ContactsType.bzj,
    text: ContactsTypeText[ContactsType.bzj],
    cssClass: ContactsTypeCssClass[ContactsType.bzj]
  },
  {
    // 开发票给TCL
    value: ContactsType.xsfp,
    text: ContactsTypeText[ContactsType.xsfp],
    cssClass: ContactsTypeCssClass[ContactsType.xsfp]
  },
  {
    // 扣款
    value: ContactsType.kk,
    text: ContactsTypeText[ContactsType.kk],
    cssClass: ContactsTypeCssClass[ContactsType.kk]
  },
  {
    // 收款
    value: ContactsType.sk,
    text: ContactsTypeText[ContactsType.sk],
    cssClass: ContactsTypeCssClass[ContactsType.sk]
  },
  {
    // 收到TCL开具的发票
    value: ContactsType.kfp,
    text: ContactsTypeText[ContactsType.kfp],
    cssClass: ContactsTypeCssClass[ContactsType.kfp]
  },
  {
    // 其他
    value: ContactsType.other,
    text: ContactsTypeText[ContactsType.other],
    cssClass: ContactsTypeCssClass[ContactsType.other]
  }
]

// 清账状态 0未清 1已清
export const CleanStatus = {
  notYet: '0', // 未清
  already: '1' // 已清
}

// 清账状态 text
export const CleanStatusText = {
  [CleanStatus.notYet]: i18n.t('未清'),
  [CleanStatus.already]: i18n.t('已清')
}

// 清账状态 对应的 css class
export const CleanStatusCssClass = {
  [CleanStatus.notYet]: '',
  [CleanStatus.already]: ''
}

// 清账状态 对应的 Options
export const CleanStatusOptions = [
  {
    // 未清
    value: CleanStatus.notYet,
    text: CleanStatusText[CleanStatus.notYet],
    cssClass: CleanStatusCssClass[CleanStatus.notYet]
  },
  {
    // 已清
    value: CleanStatus.already,
    text: CleanStatusText[CleanStatus.already],
    cssClass: CleanStatusCssClass[CleanStatus.already]
  }
]

// 状态 0-新建；1-待反馈；2-反馈正常；3-反馈异常；4-采购已确认；5-关闭；6-财务已确认
export const ReconciledStatus = {
  new: 0, // 新建
  pending: 1, // 待反馈
  normal: 2, // 反馈正常
  abnormal: 3, // 反馈异常
  confirmed: 4, // 采购已确认
  close: 5, // 关闭
  finConfirmed: 6, // 财务已确认
  finished: 7 // 已完结
}

// 往来类别 text
export const ReconciledStatusText = {
  [ReconciledStatus.new]: i18n.t('新建'),
  [ReconciledStatus.pending]: i18n.t('待反馈'),
  [ReconciledStatus.normal]: i18n.t('反馈正常'),
  [ReconciledStatus.abnormal]: i18n.t('反馈异常'),
  [ReconciledStatus.confirmed]: i18n.t('采购已确认'),
  [ReconciledStatus.close]: i18n.t('关闭'),
  [ReconciledStatus.finConfirmed]: i18n.t('财务已确认'),
  [ReconciledStatus.finished]: i18n.t('已完结')
}

// 往来类别 对应的 css class
export const ReconciledStatusCssClass = {
  [ReconciledStatus.new]: 'col-active',
  [ReconciledStatus.pending]: 'col-active',
  [ReconciledStatus.normal]: 'col-active',
  [ReconciledStatus.abnormal]: 'col-active',
  [ReconciledStatus.confirmed]: 'col-inactive',
  [ReconciledStatus.close]: 'col-inactive',
  [ReconciledStatus.finConfirmed]: 'col-inactive',
  [ReconciledStatus.finished]: 'col-inactive'
}

// 往来类别 对应的 Options
export const ReconciledStatusOptions = [
  {
    // 新建
    value: ReconciledStatus.new,
    text: ReconciledStatusText[ReconciledStatus.new],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.new]
  },
  {
    // 待反馈
    value: ReconciledStatus.pending,
    text: ReconciledStatusText[ReconciledStatus.pending],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.pending]
  },
  {
    // 反馈正常
    value: ReconciledStatus.normal,
    text: ReconciledStatusText[ReconciledStatus.normal],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.normal]
  },
  {
    // 反馈异常
    value: ReconciledStatus.abnormal,
    text: ReconciledStatusText[ReconciledStatus.abnormal],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.abnormal]
  },
  {
    // 采购已确认
    value: ReconciledStatus.confirmed,
    text: ReconciledStatusText[ReconciledStatus.confirmed],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.confirmed]
  },
  // { // 不查询关闭额数据
  //   // 关闭
  //   value: ReconciledStatus.close,
  //   text: ReconciledStatusText[ReconciledStatus.close],
  //   cssClass: ReconciledStatusCssClass[ReconciledStatus.close],
  // },
  {
    // 财务已确认
    value: ReconciledStatus.finConfirmed,
    text: ReconciledStatusText[ReconciledStatus.finConfirmed],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.finConfirmed]
  },
  {
    // 已完结
    value: ReconciledStatus.finished,
    text: ReconciledStatusText[ReconciledStatus.finished],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.finished]
  }
]

// 往来对账单列表 表格列数据
export const ReconciledColumnData = [
  {
    fieldCode: 'code', // 往来对账单号
    fieldName: i18n.t('往来对账单号')
  },
  {
    fieldCode: 'status', // 状态 状态 0-新建；1-待反馈；2-反馈正常；3-反馈异常；4-采购已确认；5-关闭；
    fieldName: i18n.t('状态')
  },
  {
    fieldCode: 'companyCode', // 公司
    fieldName: i18n.t('公司')
    // companyName
  },
  {
    fieldCode: 'supplierCode', // 供应商
    fieldName: i18n.t('供应商')
    // supplierName
  },
  {
    fieldCode: 'deadLine', // 对账月份
    fieldName: i18n.t('对账月份')
  },
  {
    fieldCode: 'openingBalance', // 期初余额
    fieldName: i18n.t('期初余额')
  },
  {
    fieldCode: 'closingBalance', // 期末余额 采方期末余额
    fieldName: i18n.t('期末余额')
  },
  {
    fieldCode: 'theReconDiff', // 对账差异 前端定义、计算 = 采方期末余额-供方期末余额
    fieldName: i18n.t('对账差异')
    // supplierClosingBalance	供方期末余额
  },
  {
    fieldCode: 'currencyName', // 币种
    fieldName: i18n.t('币种')
  },
  {
    fieldCode: 'remark', // 采方备注
    fieldName: i18n.t('采方备注')
  },
  {
    fieldCode: 'supRemark', // 供方备注
    fieldName: i18n.t('供方备注')
  },
  {
    fieldCode: 'financeRemark', // 财务备注
    fieldName: i18n.t('财务备注')
  },
  {
    fieldCode: 'createUserName', // 创建人
    fieldName: i18n.t('创建人')
  },
  {
    fieldCode: 'createTime', // 创建时间
    fieldName: i18n.t('创建时间')
  }
]

// 往来对账单明细 表格列数据
export const ReconciledDetailsColumnData = [
  {
    fieldCode: 'code', // 往来对账单号
    fieldName: i18n.t('往来对账单号')
  },
  {
    fieldCode: 'status', // 状态
    fieldName: i18n.t('状态')
  },
  {
    fieldCode: 'companyCode', // 公司
    fieldName: i18n.t('公司')
    // companyName
  },
  {
    fieldCode: 'supplierCode', // 供应商
    fieldName: i18n.t('供应商')
    // supplierName
  },
  {
    fieldCode: 'openingBalance', // 期初余额
    fieldName: i18n.t('期初余额')
  },
  {
    fieldCode: 'closingBalance', // 期末余额
    fieldName: i18n.t('期末余额')
  },
  {
    fieldCode: 'theReconDiff', // 对账差异 对账差异 前端定义、计算 = 采方期末-供方期末
    fieldName: i18n.t('对账差异')
  },
  {
    fieldCode: 'currencyName', // 币种
    fieldName: i18n.t('币种')
  },
  {
    fieldCode: 'remark', // 采方备注
    fieldName: i18n.t('采方备注')
  },
  {
    fieldCode: 'supRemark', // 供方备注
    fieldName: i18n.t('供方备注')
  },
  {
    fieldCode: 'docNo', // 单据号
    fieldName: i18n.t('单据号')
  },
  {
    fieldCode: 'type', // 往来类别
    fieldName: i18n.t('往来类别')
  },
  {
    fieldCode: 'reconciliationAccountName', // 对账科目
    fieldName: i18n.t('对账科目')
  },
  {
    fieldCode: 'accountDate', // 入账日期
    fieldName: i18n.t('入账日期')
  },
  {
    fieldCode: 'invoiceNo', // 发票号
    fieldName: i18n.t('发票号')
  },
  {
    fieldCode: 'amount', // 金额
    fieldName: i18n.t('金额')
  },
  {
    fieldCode: 'cleanStatus', // 清账状态
    fieldName: i18n.t('清账状态')
  },
  {
    fieldCode: 'createUserName', // 创建人
    fieldName: i18n.t('创建人')
  },
  {
    fieldCode: 'createTime', // 创建时间
    fieldName: i18n.t('创建时间')
  }
]
