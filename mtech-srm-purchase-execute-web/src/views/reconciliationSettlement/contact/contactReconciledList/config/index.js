import {
  Tab,
  ContactsTypeOptions,
  CleanStatusOptions,
  ReconciledStatusOptions,
  CellTools,
  CellTools2
} from './constant'
import { timeDate, theReconDiffTemplate } from './columnComponent'
import bigDecimal from 'js-big-decimal'
import { codeNameColumn } from '@/utils/utils'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'

// 格式化表格动态数据
export const formatTableColumnData = (args) => {
  const { data, tab } = args
  const colData = []
  if (tab === Tab.reconciled) {
    // 往来对账单
    data.forEach((col) => {
      const defaultCol = {
        ...col,
        field: col.fieldCode,
        headerText: col.fieldName,
        width: '150'
      }
      if (col.fieldCode === 'code') {
        // 往来对账单 往来对账单号
        defaultCol.cellTools = [] // 使其可点击查看
      } else if (col.fieldCode === 'createTime') {
        // 往来对账单 创建时间
        defaultCol.template = timeDate({
          dataKey: col.fieldCode,
          hasTime: true
        })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (col.fieldCode === 'deadLine') {
        // 往来对账单 对账月份
        defaultCol.template = timeDate({
          dataKey: col.fieldCode,
          hasTime: false,
          format: 'YYYY-mm'
        })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (col.fieldCode === 'theReconDiff') {
        // 往来对账单 对账差异 前端定义、计算 = 采方期末-供方期末
        defaultCol.template = theReconDiffTemplate
      } else if (col.fieldCode === 'status') {
        // 状态
        defaultCol.valueConverter = {
          type: 'map',
          map: ReconciledStatusOptions
        }
        defaultCol.cellTools = CellTools
      } else if (col.fieldCode === 'companyCode') {
        // 公司
        // code-name 形式
        defaultCol.width = '300'
        defaultCol.template = codeNameColumn({
          firstKey: 'companyCode',
          secondKey: 'companyName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        // 主数据选择器
        defaultCol.searchOptions = {
          ...MasterDataSelect.businessCompany,
          placeholder: i18n.t('公司')
        }
      } else if (col.fieldCode === 'supplierCode') {
        // 供应商
        // code-name 形式
        defaultCol.width = '400'
        defaultCol.template = codeNameColumn({
          firstKey: 'supplierCode',
          secondKey: 'supplierName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        // 主数据选择器
        defaultCol.searchOptions = {
          ...MasterDataSelect.supplierAll,
          placeholder: i18n.t('供应商')
        }
      }
      colData.push(defaultCol)
    })
  } else if (tab === Tab.reconciledDetails) {
    // 往来对账单明细
    data.forEach((col) => {
      const defaultCol = {
        ...col,
        field: col.fieldCode,
        headerText: col.fieldName,
        width: '150'
      }
      if (col.fieldCode === 'code') {
        // 往来对账单 往来对账单号
        defaultCol.cellTools = [] // 使其可点击查看
      } else if (col.fieldCode === 'type') {
        // 往来对账单明细 往来类别
        defaultCol.valueConverter = {
          type: 'map',
          map: ContactsTypeOptions
        }
      } else if (col.fieldCode === 'accountDate') {
        // 往来对账单明细 入账日期
        defaultCol.template = timeDate({
          dataKey: col.fieldCode,
          hasTime: false
        })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (col.fieldCode === 'createTime') {
        // 往来对账单明细 创建时间
        defaultCol.template = timeDate({
          dataKey: col.fieldCode,
          hasTime: true
        })
        defaultCol.searchOptions = MasterDataSelect.dateRange
      } else if (col.fieldCode === 'cleanStatus') {
        // 往来对账单明细 清账状态
        defaultCol.valueConverter = {
          type: 'map',
          map: CleanStatusOptions
        }
      } else if (col.fieldCode === 'theReconDiff') {
        // 往来对账单 对账差异 前端定义、计算 = 采方期末-供方期末
        defaultCol.template = theReconDiffTemplate
        defaultCol.ignore = true
      } else if (col.fieldCode === 'status') {
        // 状态
        defaultCol.valueConverter = {
          type: 'map',
          map: ReconciledStatusOptions
        }
        defaultCol.cellTools = CellTools2
      } else if (col.fieldCode === 'companyCode') {
        // 公司
        // code-name 形式
        defaultCol.width = '300'
        defaultCol.template = codeNameColumn({
          firstKey: 'companyCode',
          secondKey: 'companyName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        // 主数据选择器
        defaultCol.searchOptions = {
          ...MasterDataSelect.businessCompany,
          placeholder: i18n.t('公司')
        }
      } else if (col.fieldCode === 'supplierCode') {
        // 供应商
        // code-name 形式
        defaultCol.width = '400'
        defaultCol.template = codeNameColumn({
          firstKey: 'supplierCode',
          secondKey: 'supplierName'
        })
        defaultCol.allowFiltering = false // 不可是使用过滤搜索，因为是 code-name 形式
        // 主数据选择器
        defaultCol.searchOptions = {
          ...MasterDataSelect.supplierAll,
          placeholder: i18n.t('供应商')
        }
      }
      colData.push(defaultCol)
    })
  }

  return colData
}

// 添加 对账差异 字段到列表中
export const addTheReconDiffToList = (list) => {
  if (list.length > 0) {
    // 往来对账单 对账差异 前端定义、计算 = 采方期末+供方期末
    list.forEach((item) => {
      const closingBalance = item.closingBalance || 0 // 采方期末
      const supplierClosingBalance = item.supplierClosingBalance || 0 // 供方期末
      // 对账差异
      item.theReconDiff = bigDecimal.add(closingBalance, supplierClosingBalance)
    })
  }
  return list
}

// 验证 状态是否统一
export const verifyStatus = (data) => {
  let valid = true
  let status = ''
  for (let i = 0; i < data.length; i++) {
    status = data[i].status
    if (data[i] && data[i - 1] && data[i].status !== data[i - 1].status) {
      valid = false
      break
    }
  }

  return { valid, status }
}
