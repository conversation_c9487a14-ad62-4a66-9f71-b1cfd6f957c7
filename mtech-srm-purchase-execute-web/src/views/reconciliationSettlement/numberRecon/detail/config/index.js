// import Vue from "vue";
import { i18n } from '@/main.js'
import { timeNumberToDate, judgeFormatCodeName } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'

export const checkCol = {
  width: '50',
  type: 'checkbox',
  showInColumnChooser: false,
  allowEditing: false
}

export const detailCols = () => [
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('采购订单号')
  },
  {
    width: '150',
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '150',
    field: 'receiveItemNo',
    headerText: i18n.t('物料凭证行号')
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    searchOptions: MasterDataSelect.material
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    ignore: true
  },
  {
    width: '150',
    field: 'unitCode',
    headerText: i18n.t('单位'),
    searchOptions: MasterDataSelect.unit,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.unitCode, data?.unitName)
    }
  },
  {
    width: '150',
    field: 'warehousingCount',
    headerText: i18n.t('入库数量')
  },
  {
    width: '150',
    field: 'orderTypeName',
    headerText: i18n.t('订单类型')
  },
  {
    width: '250',
    field: 'locationCode',
    headerText: i18n.t('库存地点'),
    searchOptions: MasterDataSelect.stockAddress,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.locationCode, data?.locationName)
    }
  },
  {
    width: '150',
    field: 'certificateTime',
    headerText: i18n.t('凭证日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    }
  },
  {
    width: '150',
    field: 'transType',
    headerText: i18n.t('事务类型')
  },
  {
    width: '150',
    field: 'itemCertificateNo',
    headerText: i18n.t('物料凭证号')
  },
  {
    width: '150',
    field: 'itemCertificateTime',
    headerText: i18n.t('物料凭证日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    }
  },
  {
    width: '150',
    field: 'deliveryNo',
    headerText: i18n.t('送货单号')
  },
  {
    width: '150',
    field: 'receiveTime',
    headerText: i18n.t('过账日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    }
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('采方备注')
  },
  {
    width: '150',
    field: 'supRemark',
    headerText: i18n.t('供方备注')
  }
]

export const summaryCols = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'unitName',
    headerText: i18n.t('单位')
  },
  {
    width: '150',
    field: 'warehousingCount',
    headerText: i18n.t('入库数量')
  }
]
