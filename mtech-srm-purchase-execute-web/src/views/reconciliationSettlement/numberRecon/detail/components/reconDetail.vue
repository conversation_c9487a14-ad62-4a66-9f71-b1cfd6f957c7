<template>
  <div>
    <sc-table
      ref="sctableRef"
      grid-id="d92494d6-d78a-4fc0-a614-ff7defbd501c"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :is-show-refresh-bth="false"
      :scroll-x="{ gt: 0, oSize: 20 }"
      :scroll-y="{ gt: 0, oSize: 100 }"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <div style="margin-top: 10px">{{ $t('总条数') }}：{{ total }}</div>
  </div>
</template>

<script>
/**
 * entryType: create - 创建；view - 查看
 */
import dayjs from 'dayjs'
import ScTable from '@/components/ScTable/src/index'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  components: { ScTable },
  props: {
    entryType: {
      type: String,
      default: 'view'
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          width: 50,
          type: 'checkbox'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          align: 'center'
        },
        {
          field: 'orderCode',
          title: this.$t('采购订单号'),
          minWidth: 120
        },
        {
          field: 'lineNo',
          title: this.$t('行号')
        },
        {
          field: 'receiveItemNo',
          title: this.$t('物料凭证行号'),
          minWidth: 120
        },
        {
          field: 'itemCode',
          title: this.$t('物料编号'),
          minWidth: 140
        },
        {
          field: 'itemName',
          title: this.$t('物料名称'),
          minWidth: 160
        },
        {
          field: 'unitCode',
          title: this.$t('单位'),
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.unitName : ''
          }
        },
        {
          field: 'warehousingCount',
          title: this.$t('入库数量'),
          minWidth: 120
        },
        {
          field: 'orderTypeName',
          title: this.$t('订单类型'),
          minWidth: 120
        },
        {
          field: 'locationCode',
          title: this.$t('库存地点'),
          minWidth: 160,
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.locationName : ''
          }
        },
        {
          field: 'certificateTime',
          title: this.$t('凭证日期'),
          minWidth: 120,
          formatter: ({ cellValue }) => {
            let text = ''
            if (cellValue && cellValue !== '0') {
              text = dayjs(Number(cellValue)).format('YYYY-MM-DD')
            }
            return text
          }
        },
        {
          field: 'transType',
          title: this.$t('事务类型'),
          minWidth: 120
        },
        {
          field: 'itemCertificateNo',
          title: this.$t('物料凭证号'),
          minWidth: 120
        },
        {
          field: 'itemCertificateTime',
          title: this.$t('物料凭证日期'),
          minWidth: 140,
          formatter: ({ cellValue }) => {
            let text = ''
            if (cellValue && cellValue !== '0') {
              text = dayjs(Number(cellValue)).format('YYYY-MM-DD')
            }
            return text
          }
        },
        {
          field: 'deliveryNo',
          title: this.$t('送货单号'),
          minWidth: 120
        },
        {
          field: 'receiveTime',
          title: this.$t('过账日期'),
          minWidth: 140,
          formatter: ({ cellValue }) => {
            let text = ''
            if (cellValue && cellValue !== '0') {
              text = dayjs(Number(cellValue)).format('YYYY-MM-DD')
            }
            return text
          }
        },
        {
          field: 'remark',
          title: this.$t('采方备注'),
          minWidth: 160
        },
        {
          field: 'supRemark',
          title: this.$t('供方备注'),
          minWidth: 160
        }
      ]
    },
    toolbar() {
      let arr = [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }]
      if (this.entryType === 'create') {
        arr = [
          { code: 'remove', name: this.$t('移除'), status: 'info', loading: false },
          { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
        ]
      }
      return arr
    },
    companyCode() {
      return this.$route.query?.companyCode
    },
    factoryCode() {
      return this.$route.query?.factoryCode
    },
    supplierCode() {
      return this.$route.query?.supplierCode
    },
    itemCertificateMonth() {
      return this.$route.query?.itemCertificateMonth
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    getTableData() {
      if (this.entryType === 'view') {
        this.loading = true
        this.$API.reconciliationSettlement
          .getDetailReconciliationApi({
            id: this.$route.query?.id
          })
          .then((res) => {
            this.loading = false
            if (res.code === 200) {
              this.tableData = res.data
              this.total = this.tableData.length
            }
          })
      } else if (this.entryType === 'create') {
        this.loading = true
        this.$API.reconciliationSettlement
          .getNumberByPackage({
            companyCode: this.$route.query?.companyCode,
            factoryCode: this.$route.query?.factoryCode,
            itemCertificateMonth: this.$route.query?.itemCertificateMonth,
            supplierCode: this.$route.query?.supplierCode
          })
          .then((res) => {
            this.loading = false
            if (res?.code == 200) {
              this.tableData = res.data
              this.total = this.tableData.length
            }
          })
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const ids = []
      selectedRecords.forEach((item) => {
        ids.push(item.id)
      })
      const commonToolbar = ['remove']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'remove':
          this.handleRemove(ids)
          break
        case 'export':
          if (this.entryType === 'view') {
            this.handleExport()
          } else if (this.entryType === 'create') {
            this.handleExportNoId()
          }
          break
        default:
          break
      }
    },
    handleRemove(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认移除选中的数据？')
        },
        success: () => {
          this.tableRef.removeCheckboxRow()
          this.total = this.tableRef.getTableData().visibleData.length
          this.$emit('remove', ids)
        }
      })
    },
    handleExport() {
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement
        .reconciliationQuantityListExport(this.$route.query?.id)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
    },
    // 待对账明细导出
    handleExportNoId() {
      const params = {
        page: { current: 1, size: 10000 },
        condition: 'and',
        rules: [
          {
            label: this.$t('公司'),
            field: 'companyCode',
            type: 'select',
            operator: 'in',
            value: [this.companyCode]
          },
          {
            label: this.$t('工厂'),
            field: 'factoryCode',
            type: 'select',
            operator: 'in',
            value: [this.factoryCode]
          },
          {
            label: this.$t('供应商'),
            field: 'supplierCode',
            type: 'select',
            operator: 'in',
            value: [this.supplierCode]
          },
          {
            label: this.$t('出入库月份'),
            field: 'itemCertificateMonth',
            type: 'string',
            operator: 'contains',
            value: this.itemCertificateMonth
          }
        ]
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement.numberReconExcelExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>
