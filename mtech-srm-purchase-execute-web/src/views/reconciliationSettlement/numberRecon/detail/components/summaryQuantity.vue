<template>
  <div>
    <sc-table
      ref="sctableRef"
      grid-id="466e4415-dbcb-4948-b7f0-e224b2dddd16"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :is-show-refresh-bth="false"
      :scroll-x="{ gt: 0, oSize: 20 }"
      :scroll-y="{ gt: 0, oSize: 100 }"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <div style="margin-top: 10px">{{ $t('总条数') }}：{{ total }}</div>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  components: { ScTable },
  props: {
    entryType: {
      type: String,
      default: 'view'
    },
    tableData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false
    }
  },
  computed: {
    columns() {
      return [
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          align: 'center'
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码'),
          minWidth: 140
        },
        {
          field: 'itemName',
          title: this.$t('物料名称'),
          minWidth: 160
        },
        {
          field: 'unitName',
          title: this.$t('单位')
        },
        {
          field: 'warehousingCount',
          title: this.$t('入库数量'),
          minWidth: 120
        }
      ]
    },
    toolbar() {
      let arr = [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }]
      return arr
    },
    total() {
      return this.tableData.length
    },
    companyCode() {
      return this.$route.query?.companyCode
    },
    factoryCode() {
      return this.$route.query?.factoryCode
    },
    supplierCode() {
      return this.$route.query?.supplierCode
    },
    itemCertificateMonth() {
      return this.$route.query?.itemCertificateMonth
    }
  },
  methods: {
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          if (this.entryType === 'view') {
            this.handleExport()
          } else if (this.entryType === 'create') {
            this.handleExportNoId()
          }
          break
        default:
          break
      }
    },
    handleExport() {
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement
        .reconciliationQuantitySummaryExport(this.$route.query?.id)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
    },
    handleExportNoId() {}
  }
}
</script>
