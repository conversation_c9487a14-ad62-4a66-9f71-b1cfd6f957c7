<template>
  <div class="full-height pt20 vertical-flex-box">
    <!-- 头部信息 -->
    <top-info
      class="flex-keep"
      ref="topInfoRef"
      :header-info="headerInfo"
      @doSubmit="doSubmit"
      @abnormalConfirm="abnormalConfirm"
      @abnormalHandle="abnormalHandle"
    ></top-info>

    <mt-tabs
      tab-id="reconci-tab"
      :e-tab="false"
      :data-source="tabList"
      @handleSelectTab="handleSelectTab"
      class="flex-keep toggle-tab"
    ></mt-tabs>

    <div class="toggle-content">
      <!-- 对账明细 -->
      <div v-if="headerInfo.id">
        <recon-detail
          ref="numberReconRef"
          v-show="tabIndex == 0"
          :entry-type="entryType"
          @remove="handleRemove"
        ></recon-detail>
      </div>
      <div>
        <summary-quantity
          ref="summaryQuantityRef"
          v-show="tabIndex == 1"
          :entry-type="entryType"
          :table-data="summaryData"
        ></summary-quantity>
      </div>

      <!-- 相关附件 -->
      <relative-file
        ref="relativeFileRef"
        v-show="tabIndex == 2"
        :module-file-list="moduleFileList"
      ></relative-file>

      <!-- 操作日志 -->
      <operation-log
        :entry-code="headerInfo.code"
        log-operation-type=""
        v-if="tabIndex == 3"
      ></operation-log>
    </div>
  </div>
</template>

<script>
/**
 * entryType: create - 创建；view - 查看
 */
import { nodeType } from '@/components/businessComponents/relativeFileNoDocId/config/relative.js'
export default {
  components: {
    topInfo: require('./components/topInfo.vue').default,
    reconDetail: require('./components/reconDetail.vue').default,
    summaryQuantity: require('./components/summaryQuantity.vue').default,
    relativeFile: require('@/components/businessComponents/relativeFileNoDocId/index.vue').default,
    operationLog: require('./components/operationLog/index.vue').default
  },
  data() {
    return {
      tabIndex: 0,
      tabList: [
        {
          title: this.$t('对账明细')
        },
        {
          title: this.$t('数量汇总')
        },
        {
          title: this.$t('相关附件')
        }
      ],
      moduleFileList: [],
      headerInfo: null,
      summaryData: []
    }
  },
  computed: {
    entryType() {
      return this.$route.query.type
    }
  },
  mounted() {
    this.getHeader()
  },
  activated() {
    this.tabList = [
      {
        title: this.$t('对账明细')
      },
      {
        title: this.$t('数量汇总')
      },
      {
        title: this.$t('相关附件')
      }
    ]
  },
  methods: {
    getHeader() {
      if (this.entryType === 'view') {
        this.$API.reconciliationSettlement
          .detailReconciliationApi({
            id: this.$route.query?.id
          })
          .then((res) => {
            if (res.code === 200) {
              let headerInfo = {
                ...res.data,
                company: res.data?.companyCode + '-' + res.data?.companyName,
                supplier: res.data?.supplierCode + '-' + res.data?.supplierName,
                factory: res.data?.factoryCode + '-' + res.data?.factoryName
              }
              this.headerInfo = headerInfo
              this.getSummaryData()
              this.getFiles()
            }
          })
      } else if (this.entryType === 'create') {
        let headerInfo = {
          ...this.$route.query,
          company: this.$route.query?.companyCode + '-' + this.$route.query?.companyName,
          supplier: this.$route.query?.supplierCode + '-' + this.$route.query?.supplierName,
          factory: this.$route.query?.factoryCode + '-' + this.$route.query?.factoryName
        }
        this.headerInfo = headerInfo

        this.$API.reconciliationSettlement
          .getSummaryQuantity({
            companyCode: this.headerInfo.companyCode,
            factoryCode: this.headerInfo.factoryCode,
            itemCertificateMonth: this.headerInfo.itemCertificateMonth,
            supplierCode: this.headerInfo.supplierCode
          })
          .then((res) => {
            if (res?.code == 200) {
              this.summaryData = res.data
            }
          })

        this.getFiles()
      }
    },
    getSummaryData() {
      this.$API.reconciliationSettlement
        .summaryQuantityApi({
          id: this.$route.query?.id
        })
        .then((res) => {
          if (res.code === 200) {
            this.summaryData = res.data
          }
        })
    },
    getFiles() {
      this.$API.reconciliationSettlement
        .filesDetailReconciliationApi({
          id: this.$route.query?.id
        })
        .then((res) => {
          if (res.code === 200) {
            //
            if (this.entryType === 'view') {
              this.moduleFileList = [
                {
                  id: '01',
                  code: 'reconciliation_number_header_file', // 清账整单附件code
                  nodeName: this.$t('采方 - 整单附件'),
                  type: nodeType.mainViewData,
                  dataSource: res.data?.purFiles || []
                },
                {
                  id: '02',
                  code: 'reconciliation_gong',
                  nodeName: this.$t('供方 - 整单附件'),
                  type: nodeType.mainViewData,
                  dataSource: res.data?.supFiles || []
                }
              ]
              if (this.headerInfo.status === 6) {
                this.moduleFileList.push({
                  id: '03',
                  code: 'reconciliation_abnormal',
                  nodeName: this.$t('采方 - 异常处理附件'),
                  type: nodeType.mainUpdateEdit,
                  dataSource: res.data?.abnormalFiles || []
                })
              } else {
                this.moduleFileList.push({
                  id: '03',
                  code: 'reconciliation_abnormal',
                  nodeName: this.$t('采方 - 异常处理附件'),
                  type: nodeType.mainViewData,
                  dataSource: res.data?.abnormalFiles || []
                })
              }
              this.tabList.push({
                title: this.$t('操作日志')
              })
            } else {
              this.moduleFileList = [
                {
                  id: '01',
                  code: 'reconciliation_number_header_file',
                  nodeName: this.$t('采方 - 整单附件'),
                  type: nodeType.mainUpdate
                }
              ]
            }
          }
        })
    },
    handleRemove(ids) {
      this.$API.reconciliationSettlement
        .getSummaryQuantity({
          companyCode: this.headerInfo.companyCode,
          factoryCode: this.headerInfo.factoryCode,
          itemCertificateMonth: this.headerInfo.itemCertificateMonth,
          supplierCode: this.headerInfo.supplierCode,
          waitIds: ids
        })
        .then((res) => {
          if (res?.code == 200) {
            this.summaryData = res.data
          }
        })
    },
    doSubmit() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交吗？')
        },
        success: () => {
          this.$store.commit('startLoading')
          let _dataSource =
            this.$refs.numberReconRef.$refs.sctableRef.$refs.xGrid.getTableData().fullData || []

          let _items = _dataSource.map((i) => {
            return {
              id: i.id,
              remark: i.remark
            }
          })

          let submitData = {
            fileList: this.$refs.relativeFileRef.getUploadFlies(this.moduleFileList[0].id),
            header: {
              ...this.headerInfo,
              month: this.headerInfo?.itemCertificateMonth
            },
            items: _items
          }
          delete submitData?.header?.id

          this.$API.reconciliationSettlement
            .createNumberDetail(submitData)
            .then((res) => {
              if (res?.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$router.replace(`number-reconciliation`)
              }
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },
    //异常确认
    abnormalConfirm() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('异常确认？')
        },
        success: () => {
          this.$store.commit('startLoading')
          const { id, remark } = this.headerInfo
          const params = {
            id,
            remark
          }
          this.$API.reconciliationSettlement
            .abnormalConfirm(params)
            .then((res) => {
              if (res?.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                if (this.$route.name == 'number-recon-detail') {
                  this.$router.replace(`number-recon-list`)
                  return
                } else if (this.$route.name === 'number-recon-query-detail') {
                  this.$router.replace(`number-recon-query-list`)
                  return
                }
                this.$router.replace(`number-reconciliation`)
              }
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },
    //异常处理
    abnormalHandle() {
      let _dataSource =
        this.$refs.numberReconRef.$refs.sctableRef.$refs.xGrid.getTableData().fullData || []

      let _items = _dataSource.map((i) => {
        return {
          id: i.id,
          remark: i.remark
        }
      })
      let fileList = this.$refs.relativeFileRef.getUploadFlies(this.moduleFileList[2].id)
      if (!fileList || fileList.length <= 0) {
        this.$toast({
          content: this.$t('请上传异常处理附件'),
          type: 'warning'
        })
        return
      }
      let submitData = {
        fileList,
        header: {
          ...this.headerInfo,
          month: this.headerInfo?.itemCertificateMonth
        },
        items: _items
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('异常处理？')
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.reconciliationSettlement
            .abnormalHandle(submitData)
            .then((res) => {
              if (res?.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })

                if (this.$route.name == 'number-recon-detail') {
                  this.$router.replace(`number-recon-list`)
                  return
                }
              }
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },
    handleSelectTab(e) {
      this.tabIndex = e
    }
  }
}
</script>
