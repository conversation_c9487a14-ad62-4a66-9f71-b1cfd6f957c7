import { i18n } from '@/main.js'
import { timeNumberToDate, judgeFormatCodeName } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'
// 数量对账单列表
export const sumCols = [
  {
    width: '150',
    field: 'code',
    headerText: i18n.t('对账单号'),
    valueConverter: {
      type: 'placeholder',
      placeholder: '--' //placeholder可不传，默认值为"未填"
    },
    cellTools: [
      {
        id: 'view',
        icon: 'icon_outline_Preview',
        title: i18n.t('查看')
      }
    ]
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        // 0-新建；1-待反馈；2-反馈正常；3-反馈异常；4-已确认；5-关闭； 6-异常确认 ,操作页面隐藏不可操作的数据
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待反馈'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('反馈正常'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('反馈异常'), cssClass: 'col-active' },
        // { value: 4, text: i18n.t("已完成"), cssClass: "col-active" },
        // { value: 5, text: i18n.t("关闭"), cssClass: "col-inactive" },
        { value: 6, text: i18n.t('异常确认'), cssClass: 'col-inactive' }
        // { value: 7, text: i18n.t("异常已处理"), cssClass: "col-inactive" },
      ]
    },
    cellTools: [
      {
        id: 'publish',
        icon: 'icon_list_issue',
        title: i18n.t('发布'),
        visibleCondition: (data) => {
          return data['status'] == 0 || data['status'] == 3
        },
        permission: ['O_02_1156']
      },
      // {
      //   id: "cancelPublish",
      //   icon: "icon_table_cancel",
      //   title: i18n.t("取消发布"),
      //   visibleCondition: (data) => {
      //     return data["status"] == 1;
      //   },
      //   permission: ["O_02_1158"],
      // },
      {
        id: 'close',
        icon: 'icon_list_close',
        title: i18n.t('关闭'),
        visibleCondition: (data) => {
          return data['status'] == 0 || data['status'] == 1
        },
        permission: ['O_02_1155']
      },
      {
        id: 'confirm',
        icon: 'icon_table_accept1',
        title: i18n.t('确认'),
        visibleCondition: (data) => {
          return data['status'] == 2
        },
        permission: ['O_02_1157']
      }
    ]
  },
  {
    width: '150',
    field: 'year',
    headerText: i18n.t('年份')
  },
  {
    width: '150',
    field: 'month',
    headerText: i18n.t('月份')
  },
  {
    width: '150',
    field: 'itemCertificateMonth',
    headerText: i18n.t('出入库月份'),
    ignore: true
  },
  {
    width: '250',
    field: 'factoryCode',
    headerText: i18n.t('工厂'),
    searchOptions: MasterDataSelect.factoryAddress,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.factoryCode, data?.factoryName)
    }
  },
  {
    width: '250',
    field: 'companyCode',
    headerText: i18n.t('公司'),
    searchOptions: MasterDataSelect.businessCompany,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.companyCode, data?.companyName)
    }
  },
  {
    width: '250',
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    searchOptions: MasterDataSelect.supplierAll,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    searchOptions: MasterDataSelect.dateRange
  },
  {
    width: '150',
    field: 'publishTime',
    headerText: i18n.t('发布时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  }
]

export const detailCols = [
  {
    width: '150',
    field: 'code',
    headerText: i18n.t('对账单号'),
    valueConverter: {
      type: 'placeholder',
      placeholder: '--' //placeholder可不传，默认值为"未填"
    }
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        // 0-新建；1-待反馈；2-反馈正常；3-反馈异常；4-已确认；5-关闭
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待反馈'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('反馈正常'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('反馈异常'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('已确认'), cssClass: 'col-active' },
        { value: 5, text: i18n.t('关闭'), cssClass: 'col-inactive' }
      ]
    }
  },
  {
    width: '150',
    field: 'month',
    headerText: i18n.t('出入库月份'),
    valueAccessor: (field, data) => {
      let _month = data?.month
      _month = _month < 10 ? '0' + _month : _month
      return data?.year + String(_month)
    }
  },
  {
    width: '250',
    field: 'factoryCode',
    headerText: i18n.t('工厂'),
    searchOptions: MasterDataSelect.factoryAddress,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.factoryCode, data?.factoryName)
    }
  },
  {
    width: '250',
    field: 'companyCode',
    headerText: i18n.t('公司'),
    searchOptions: MasterDataSelect.businessCompany,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.companyCode, data?.companyName)
    }
  },
  {
    width: '250',
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    searchOptions: MasterDataSelect.supplierAll,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    width: '150',
    field: 'publishTime',
    headerText: i18n.t('发布时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },

  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('采购订单号')
  },
  {
    width: '150',
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    searchOptions: MasterDataSelect.material
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    ignore: true
  },
  {
    width: '150',
    field: 'unitCode',
    headerText: i18n.t('单位'),
    searchOptions: MasterDataSelect.unit,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.unitCode, data?.unitName)
    }
  },
  {
    width: '150',
    field: 'warehousingCount',
    headerText: i18n.t('入库数量')
  },
  {
    width: '150',
    field: 'orderTypeName',
    headerText: i18n.t('订单类型')
  },
  {
    width: '250',
    field: 'locationCode',
    headerText: i18n.t('库存地点'),
    searchOptions: MasterDataSelect.stockAddress,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.locationCode, data?.locationName)
    }
  },
  {
    width: '150',
    field: 'certificateTime',
    headerText: i18n.t('凭证日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    width: '150',
    field: 'transType',
    headerText: i18n.t('事务类型')
  },
  {
    width: '150',
    field: 'itemCertificateNo',
    headerText: i18n.t('物料凭证号')
  },
  {
    width: '150',
    field: 'itemCertificateTime',
    headerText: i18n.t('物料凭证日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    width: '150',
    field: 'deliveryNo',
    headerText: i18n.t('送货单号')
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('采方备注')
  },
  {
    width: '150',
    field: 'supRemark',
    headerText: i18n.t('供方备注')
  }
]
