<template>
  <div class="full-height">
    <mt-template-page
      ref="numberRecon"
      class="self-set-table"
      :permission-obj="permissionObj"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @handleSelectTab="handleSelectTab"
    ></mt-template-page>
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { sumCols, detailCols } from './config'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  data() {
    return {
      tabIndex: 0,
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0111' },
          { dataPermission: 'b', permissionCode: 'T_02_0112' }
        ]
      },
      componentConfig: [
        {
          title: this.$t('数量对账单列表'),
          dataPermission: 'a',
          permissionCode: 'T_02_0111', // 需要与permissionObj中的参数和权限code对应
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'close',
              icon: 'icon_solid_Closeorder',
              title: this.$t('关闭'),
              permission: ['O_02_1155']
            },
            {
              id: 'publish',
              icon: 'icon_list_issue',
              title: this.$t('发布'),
              permission: ['O_02_1156']
            },
            // {
            //   id: "cancelPublish",
            //   icon: "icon_table_cancel",
            //   title: this.$t("取消发布"),
            //   permission: ["O_02_1158"],
            // },
            {
              id: 'confirm',
              icon: 'icon_table_accept1',
              title: this.$t('确认'),
              permission: ['O_02_1157']
            },
            {
              id: 'excelExport',
              icon: 'icon_solid_export',
              title: this.$t('导出')
            }
            // {
            //   id: "printRecon",
            //   icon: "icon_table_print",
            //   title: this.$t("打印"),
            // },
          ],
          gridId: this.$tableUUID.reconciliationSettlement.numberReconList.list,
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: sumCols,
            // 数量待对账明细接口
            asyncConfig: {
              url: `${BASE_TENANT}/quantity/reconciliation/query`,
              defaultRules: [
                {
                  // 状态
                  field: 'status',
                  operator: 'in',
                  value: [0, 1, 2, 3, 6] //查询可操作的数据
                }
              ],
              rules: []
            },
            frozenColumns: 1
          }
        },
        {
          title: this.$t('数量对账单明细列表'),
          dataPermission: 'b',
          permissionCode: 'T_02_0112', // 需要与permissionObj中的参数和权限code对应
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          gridId: this.$tableUUID.reconciliationSettlement.numberReconList.details,
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: detailCols,
            // 数量待对账明细接口
            asyncConfig: {
              url: `${BASE_TENANT}/quantity/reconciliation/item/query`,
              rules: []
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      let selectRows = e.gridRef.getMtechGridRecords()
      if (['close', 'publish', 'cancelPublish', 'confirm', 'printRecon'].includes(e.toolbar.id)) {
        if (selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        let ids = selectRows.map((i) => i.id)
        if (e.toolbar.id == 'close') {
          this.handleClose(ids)
        } else if (e.toolbar.id == 'publish') {
          this.handlePublish(ids)
        } else if (e.toolbar.id == 'cancelPublish') {
          this.handleCancelPublish(ids)
        } else if (e.toolbar.id == 'confirm') {
          this.handleConfirm(ids)
        } else if (e.toolbar.id == 'printRecon') {
          this.handlePrint(ids)
        }
      }
      if (e.toolbar.id == 'excelExport') {
        this.handleExport()
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'close') {
        this.handleClose([e.data.id])
      } else if (e.tool.id == 'publish') {
        this.handlePublish([e.data.id])
      } else if (e.tool.id == 'cancelPublish') {
        this.handleCancelPublish([e.data.id])
      } else if (e.tool.id == 'confirm') {
        this.handleConfirm([e.data.id])
      } else if (e.tool.id == 'view') {
        this.$router.push({
          name: 'number-recon-detail',
          query: {
            id: e.data.id,
            type: 'view',
            timeStamp: new Date().getTime()
          }
        })
        // this.getDetailById(e.data.id)
      }
    },
    handleClickCellTitle(e) {
      console.log('点击了编码', e)
      if (e.field == 'code' && this.tabIndex == 0) {
        this.$router.push({
          name: 'number-recon-detail',
          query: {
            id: e.data.id,
            type: 'view',
            timeStamp: new Date().getTime()
          }
        })
        // 根据id，拿详情数据，存到session中。详情页为 创建对账单页面
        // this.getDetailById(e.data.id)
      }
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    // 打印
    handlePrint(ids) {
      this.$API.reconciliationSettlement.printNumRecon({ ids: ids }).then((res) => {
        if (res?.data?.type === 'application/json') {
          const reader = new FileReader()
          reader.readAsText(res?.data, 'utf-8')
          reader.onload = () => {
            console.log('======', reader)
            const readerRes = reader.result
            const resObj = JSON.parse(readerRes)
            this.$toast({
              content: resObj.msg,
              type: 'error'
            })
          }

          return
        }
        const content = res.data
        this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = this.pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    // 导出
    handleExport() {
      let rule = this.$refs.numberRecon.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 10000 },
        pageFlag: true,
        rules: rule.rules || [],
        defaultRules: [
          {
            // 状态
            field: 'status',
            operator: 'in',
            value: [0, 1, 2, 3] //查询可操作的数据
          }
        ]
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement.reconciliationQuantityExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 关闭
    handleClose(ids) {
      this.$dialog({
        data: {
          title: this.$t('关闭'),
          message: this.$t('确认关闭数量对账单？')
        },
        success: () => {
          this.$API.reconciliationSettlement.closeNumberRecon({ ids: ids }).then(() => {
            this.$toast({ content: this.$t('关闭成功'), type: 'success' })
            this.$refs.numberRecon.refreshCurrentGridData()
          })
        }
      })
    },

    // 发布
    handlePublish(ids) {
      console.log('ids', ids)
      this.$API.reconciliationSettlement.publishNumberRecon({ ids: ids }).then(() => {
        this.$toast({ content: this.$t('发布成功'), type: 'success' })
        this.$refs.numberRecon.refreshCurrentGridData()
      })
    },

    // 取消发布
    handleCancelPublish(ids) {
      this.$API.reconciliationSettlement.cancelPublishNumberRecon({ ids: ids }).then(() => {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.$refs.numberRecon.refreshCurrentGridData()
      })
    },

    // 确认
    handleConfirm(ids) {
      console.log('ids', ids)
      this.$API.reconciliationSettlement.confirmNumberRecon({ ids: ids }).then(() => {
        this.$toast({ content: this.$t('确认成功'), type: 'success' })
        this.$refs.numberRecon.refreshCurrentGridData()
      })
    },

    // 根据id，拿详情数据，存到session中。详情页为 创建对账单页面
    getDetailById(id) {
      this.$API.reconciliationSettlement.getNumberReconDetail(id).then((res) => {
        let numberCreateSessionData = {
          headerInfo: res?.data.header,
          requireData: res?.data.items,
          summaryQuantity: res?.data.summaryQuantity,
          entryType: 'view', // create view
          purFiles: res?.data?.purFiles || [],
          supFiles: res?.data?.supFiles || [],
          abnormalFiles: res?.data?.abnormalFiles || []
        }
        // 数量对账单详情数据缓存（区分数据对账单详情数据）
        sessionStorage.setItem(
          'numberListCreateSessionData',
          JSON.stringify(numberCreateSessionData)
        )
        setTimeout(() => {
          this.$router.push('number-recon-detail')
        }, 10)
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
      }, 100)
    }
  }
}
</script>

<style></style>
