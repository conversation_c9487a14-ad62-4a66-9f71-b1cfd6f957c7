<template>
  <!-- 采方-数量待对账列表 -->
  <div class="full-height">
    <mt-template-page
      ref="saleBeRecon"
      class="self-set-table"
      :permission-obj="permissionObj"
      :template-config="componentConfig"
      :current-tab="tabIndex"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleSelectTab="handleSelectTab"
    ></mt-template-page>
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { detailCols, summaryCols } from './config'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      tabIndex: 0,
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0109' },
          { dataPermission: 'b', permissionCode: 'T_02_0110' }
        ]
      },
      componentConfig: [
        {
          title: this.$t('数量待对账明细'),
          dataPermission: 'a',
          permissionCode: 'T_02_0109', // 需要与permissionObj中的参数和权限code对应
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'excelExport',
              icon: 'icon_solid_export',
              title: this.$t('导出')
            }
          ],
          gridId: this.$tableUUID.reconciliationSettlement.numberReconciliation.detail,
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: detailCols,
            asyncConfig: {
              url: `${BASE_TENANT}/quantity/reconciliation/wait/query`,
              rules: []
            },
            frozenColumns: 1
          }
        },
        {
          title: this.$t('数量待对账'),
          dataPermission: 'b',
          permissionCode: 'T_02_0110', // 需要与permissionObj中的参数和权限code对应
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [
            {
              id: 'createStatementAll',
              icon: 'icon_solid_Createorder',
              title: this.$t('创建对账单'),
              permission: ['O_02_1153']
            }
          ],
          gridId: this.$tableUUID.reconciliationSettlement.numberReconciliation.list,
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            autoWidthColumns: 6,
            columnData: summaryCols,
            asyncConfig: {
              url: `${BASE_TENANT}/quantity/reconciliation/wait/summary`,
              rules: []
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },

  computed: {
    userInfo() {
      return this.$store.state.userInfo
    }
  },

  methods: {
    // 切换tab
    handleSelectTab(e) {
      this.tabIndex = e
    },

    // 点击按钮
    handleClickToolBar(e) {
      let selectRows = e.gridRef.getMtechGridRecords()
      // 点击 创建
      if (['createStatement', 'createStatementAll'].includes(e.toolbar.id)) {
        if (selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
      }
      if (e.toolbar.id == 'createStatement') {
        let valid = this.checkSelectedValid(selectRows)
        if (!valid) {
          this.$toast({
            content: this.$t('请选择相同的供应商、工厂、出入库月份的数据进行创建'),
            type: 'warning'
          })
          return
        }
        this.routeToDetail('create', selectRows)
      } else if (e.toolbar.id == 'excelExport') {
        this.excelExport()
      } else if (e.toolbar.id == 'createStatementAll') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('是否确认创建对账单')
          },
          success: () => {
            let params = selectRows.map((i) => {
              return {
                companyCode: i.companyCode,
                factoryCode: i.factoryCode,
                supplierCode: i.supplierCode,
                month: i.itemCertificateMonth
              }
            })
            this.apiStartLoading()
            this.$API.reconciliationSettlement.createNumberAll(params).then(() => {
              this.apiEndLoading()
              this.$toast({ content: this.$t('创建成功'), type: 'success' })
              this.$refs.saleBeRecon.refreshCurrentGridData()
            })
          }
        })
      }
    },

    // 校验
    checkSelectedValid(rows) {
      if (rows.length == 1) {
        return true
      }
      let valid = true
      // 相同的 供应商、工厂、出入库月份 进行聚合
      for (let i = 0; i < rows.length - 1; i++) {
        if (
          rows[i].factoryCode != rows[i + 1].factoryCode ||
          rows[i].supplierCode != rows[i + 1].supplierCode ||
          rows[i].itemCertificateMonth != rows[i + 1].itemCertificateMonth
        ) {
          valid = false
          break
        }
      }
      return valid
    },

    // 点击标题
    handleClickCellTitle(e) {
      console.log('点击title', e)
      if (e.field != 'dataCount') return
      if (!e.data[e.field]) {
        this.$toast({ content: this.$t('无明细行'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('是否跳转创建页面提交创建')
        },
        success: () => {
          this.$API.reconciliationSettlement
            .getNumberByPackage({
              companyCode: e.data.companyCode,
              factoryCode: e.data.factoryCode,
              itemCertificateMonth: e.data.itemCertificateMonth,
              supplierCode: e.data.supplierCode
            })
            .then((res) => {
              if (res?.code == 200) {
                this.routeToDetail('create', res.data, e)
              }
            })
        }
      })
    },

    // 跳转到创建页面
    routeToDetail(flag, selectRows) {
      this.$router.push({
        name: 'number-be-recon',
        query: {
          type: 'create',
          timeStamp: new Date().getTime(),
          ...selectRows[0],
          createUserName: this.userInfo.accountName,
          createTime: new Date().getTime()
        }
      })
      // this.$API.reconciliationSettlement
      //   .getSummaryQuantity({
      //     companyCode: e.data.companyCode,
      //     factoryCode: e.data.factoryCode,
      //     itemCertificateMonth: e.data.itemCertificateMonth,
      //     supplierCode: e.data.supplierCode
      //   })
      //   .then((res) => {
      //     if (res?.code == 200) {
      //       let numberCreateSessionData = {
      //         headerInfo: {
      //           ...selectRows[0],
      //           createUserName: this.userInfo.accountName,
      //           createTime: new Date()
      //         },
      //         requireData: selectRows,
      //         summaryQuantity: res.data,
      //         entryType: flag // create view
      //       }
      //       sessionStorage.setItem(
      //         'numberCreateSessionData',
      //         JSON.stringify(numberCreateSessionData)
      //       )
      //       setTimeout(() => {
      //         this.$router.push('number-be-recon')
      //       }, 10)
      //     }
      //   })
    },
    // 数量待对账明细下载
    excelExport() {
      const queryBuilderRules =
        this.$refs.saleBeRecon.getCurrentUsefulRef().pluginRef.queryBuilderRules
      if (!queryBuilderRules || queryBuilderRules?.rules.length <= 0) {
        this.$toast({ content: this.$t('请先查询数据'), type: 'warning' })
        return
      }
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.$store.commit('startLoading')

      this.apiStartLoading()
      this.$API.reconciliationSettlement.numberReconExcelExport(params).then((res) => {
        this.$store.commit('endLoading')
        this.apiEndLoading()
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style></style>
