<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog dialog-payment"
    :header="headerTxt"
    :buttons="buttons"
    @close="handleClose"
  >
    <div class="dialog-content">
      <mt-form ref="addFormRef" :model="addForm" :rules="addRules">
        <mt-form-item prop="companyCode" :label="$t('公司')">
          <!-- <mt-select
            :data-source="companyList"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            v-model="addForm.companyCode"
          ></mt-select> -->
          <select-filter
            :width="390"
            ref="companyRef"
            :fields="companyObj.fields"
            :request-url="companyObj.requestUrl"
            :request-key="companyObj.requestKey"
            :init-val.sync="addForm.companyCode"
            :other-params="companyObj.otherParams"
            :label-show-obj="companyObj.labelShowObj"
            :placeholder="$t('请选择公司')"
            @handleChange="handleCompanyChange"
          ></select-filter>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商')">
          <mt-select
            :data-source="supplierList"
            v-model="addForm.supplierCode"
            @handleChange="handleChange"
            :filtering="getSupplierList"
            :allow-filtering="true"
            :fields="{
              text: 'supplierName',
              value: 'supplierCode'
            }"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="reconciliationAccountCode" :label="$t('对账科目')">
          <mt-select
            :data-source="reconList"
            :fields="{
              text: 'codeAndName',
              value: 'accountSubjectCode'
            }"
            v-model="addForm.reconciliationAccountCode"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="openingBalance" :label="$t('期初余额')">
          <mt-input-number v-model="addForm.openingBalance" :precision="2"></mt-input-number>
        </mt-form-item>
        <mt-form-item prop="currencyCode" :label="$t('币种')">
          <!-- <select-filter
            :width="390"
            :fields="currencyObj.fields"
            :request-url="currencyObj.requestUrl"
            :request-key="currencyObj.requestKey"
            :init-val.sync="addForm.currencyCode"
            :label-show-obj="currencyObj.labelShowObj"
            @handleChange="handleCurrencyChange"
          ></select-filter> -->
          <mt-select
            :data-source="currencyList"
            v-model="addForm.currencyCode"
            :show-clear-button="true"
            :allow-filtering="true"
            :filtering="getCurrency"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="year" :label="$t('年月')">
          <mt-date-picker
            start="Year"
            depth="Year"
            format="y MMMM"
            :max="maxDate"
            v-model="addForm.year"
            :placeholder="$t('选择年月')"
          ></mt-date-picker>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  props: {
    dialogType: {
      type: String,
      default: 'add'
    }
  },

  data() {
    return {
      maxDate: new Date(),
      headerTxt: this.dialogType == 'add' ? this.$t('新增') : this.$t('编辑'),
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      addForm: {
        companyCode: null,
        companyName: null,
        supplierCode: null,
        year: '',
        month: null,
        reconciliationAccountCode: null,
        openingBalance: null,
        currencyCode: null,
        currencyName: null
      },
      addRules: {
        companyCode: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }],
        reconciliationAccountCode: [
          {
            required: true,
            message: this.$t('请选择对账科目'),
            trigger: 'blur'
          }
        ],
        openingBalance: [
          {
            required: true,
            message: this.$t('请输入期初余额'),
            trigger: 'blur'
          }
        ],
        currencyCode: [{ required: true, message: this.$t('请选择币种'), trigger: 'blur' }],
        year: [{ required: true, message: this.$t('请选择年月'), trigger: 'blur' }]
      },
      companyList: [],
      supplierList: [],
      reconList: [], // 对账科目
      currencyList: [],
      supplierChoseObj: {
        requestUrl: {
          pre: 'masterData',
          url: 'getSupplier'
        },
        requestKey: 'fuzzyNameOrCode',
        fields: {
          text: 'labelShow',
          value: 'supplierCode'
        },
        labelShowObj: {
          code: 'supplierCode',
          name: 'supplierName'
        }
        // otherParams: {},
      },
      // 业务公司
      companyObj: {
        requestUrl: {
          pre: 'masterData',
          url: 'OrgFindSpecifiedChildrenLevelBalance'
        },
        requestKey: 'fuzzyParam',
        otherParams: {
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true
        },
        fields: {
          text: 'labelShow',
          value: 'orgCode'
        },
        labelShowObj: {
          code: 'orgCode',
          name: 'orgName'
        }
      },
      currencyObj: {
        requestUrl: {
          pre: 'masterData',
          url: 'getCurrencyByFilter'
        },
        requestKey: 'fuzzyParam',
        otherParams: {},
        fields: {
          text: 'labelShow',
          value: 'currencyCode'
        },
        labelShowObj: {
          code: 'currencyCode',
          name: 'currencyName'
        }
      }
    }
  },

  computed: {
    otherParams() {
      let _otherParams = {}
      if (this.addForm?.companyCode) {
        _otherParams.organizationCode = this.addForm?.companyCode
      }
      if (this.addForm?.supplierCode) {
        _otherParams.supplierCode = this.addForm.supplierCode
      }
      return _otherParams
    }
  },

  watch: {
    'addForm.companyCode': {
      handler(newVal) {
        if (newVal) {
          console.log('有公司', newVal)
          this.getAccountSubject() // 获取对账科目
          this.getSupplierList()
        } else {
          console.log('没有公司')
          this.supplierList = []
          this.addForm.supplierCode = null
          this.reconList = []
          this.addForm.reconciliationAccountCode = null
        }
      },
      immediate: true
    }
  },

  async mounted() {
    await this.getData()
    // this.getCurrency = utils.debounce(this.getCurrency, 1000);
  },
  methods: {
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.addFormRef.validate((valid) => {
        if (valid) {
          let params = { ...this.addForm }
          params.year = new Date(this.addForm.year).getFullYear()
          params.month = Number(new Date(this.addForm.year).getMonth()) + 1
          console.log('提交的数据', params)
          let requestUrl = 'addInitBalance'
          if (this.addForm.id) {
            requestUrl = 'editInitBalance'
          }
          this.$API.reconciliationSettlement[requestUrl](params).then(() => {
            this.$toast({
              content: this.addForm?.id ? this.$t('修改成功') : this.$t('新增成功'),
              type: 'success'
            })
            this.$refs.dialog.ejsRef.hide()
            this.$emit('addSuccess')
          })
        }
      })
    },
    async getData(row) {
      //查询业务公司下拉数据
      if (sessionStorage.getItem('initBalanceCompamy')) {
        this.companyList = JSON.parse(sessionStorage.getItem('initBalanceCompamy'))
      } else {
        await this.$API.masterData
          .OrgFindSpecifiedChildrenLevelOrgs({
            organizationLevelCodes: ['ORG02', 'ORG01'],
            orgType: 'ORG001PRO',
            includeItself: true,
            organizationIds: []
          })
          .then((res) => {
            console.log('显示4')
            this.companyList = res.data
            sessionStorage.setItem('initBalanceCompamy', JSON.stringify(res.data))
          })
      }

      // 币种
      await this.getCurrency({
        text: row?.currencyCode || this.addForm.currencyCode
      })
    },

    // 公司改变
    handleCompanyChange(e) {
      this.addForm.companyCode = e.itemData?.orgCode
      this.addForm.companyName = e.itemData?.orgName
    },

    // 供应商下拉修改
    handleChange(e) {
      console.log('供应商下拉修改', e)
      this.addForm.supplierCode = e.itemData?.supplierCode
      this.addForm.supplierName = e.itemData?.supplierName
    },

    // 币种下拉修改
    handleCurrencyChange(e) {
      this.addForm.currencyCode = e.itemData?.currencyCode
      this.addForm.currencyName = e.itemData?.currencyName
    },

    // 币种 可搜索
    async getCurrency(e = { text: '' }) {
      console.log('搜索币种', e)
      await this.$API.masterData.getCurrencyByFilter({ fuzzyParam: e.text }).then((res) => {
        if (res && res.data.length) {
          this.currencyList = res.data.map((item) => {
            return {
              text: item.currencyName,
              value: item.currencyCode,
              id: item.id
            }
          })
        }
      })
    },
    // 供应商数据
    async getSupplierList(e = { text: '' }) {
      this.$API.masterData
        .getSupplier({
          fuzzyNameOrCode: e.text,
          organizationCode: this.addForm.companyCode
        })
        .then((res) => {
          this.supplierList = res.data || []
        })
    },

    // 对账科目
    getAccountSubject() {
      this.$API.masterData
        .getAccountSubject({
          orgCode: this.addForm.companyCode,
          orgTypeCode: 'ORG001PRO'
        })
        .then((res) => {
          this.reconList =
            res.data.map((i) => {
              return {
                ...i,
                codeAndName: `${i.accountSubjectCode} - ${i.accountSubjectName}`
              }
            }) || []
        })
    },

    // 编辑时，给addForm 设置数据
    async setAddFormData(row) {
      this.$refs.dialog.ejsRef.show()
      await this.getData(row)
      let year = new Date(row.year + '-' + row.month)
      this.addForm = {
        ...row,
        year
      }
    }
  }
}
</script>

<style></style>
