import { i18n } from '@/main.js'
import { judgeFormatCodeName } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'
export const columnData = [
  {
    width: '150',
    headerText: i18n.t('序号'),
    field: 'index',
    ignore: true,
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        permission: ['O_02_0424']
      },
      {
        id: 'delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        permission: ['O_02_0425']
      }
    ],
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return Number(+row.index + 1) * 10
      }
    }
  },
  {
    width: '250',
    field: 'companyCode',
    headerText: i18n.t('公司'),
    searchOptions: MasterDataSelect.businessCompany,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.companyCode, data?.companyName)
    }
  },
  {
    width: '250',
    field: 'supplierCode',
    headerText: i18n.t('供应商/客户'),
    searchOptions: MasterDataSelect.supplierAll,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  },
  {
    width: '100',
    field: 'year',
    headerText: i18n.t('年')
  },
  {
    width: '100',
    field: 'month',
    headerText: i18n.t('月')
  },
  {
    width: '150',
    field: 'reconciliationAccountCode',
    headerText: i18n.t('对账科目编号')
  },
  {
    width: '150',
    field: 'reconciliationAccount',
    headerText: i18n.t('对账科目')
  },
  {
    width: '150',
    field: 'openingBalance',
    headerText: i18n.t('期初余额')
  },
  {
    width: '250',
    field: 'currencyCode',
    headerText: i18n.t('币种'),
    searchOptions: MasterDataSelect.money,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.currencyCode, data?.currencyName)
    }
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    searchOptions: MasterDataSelect.dateRange
  }
]
