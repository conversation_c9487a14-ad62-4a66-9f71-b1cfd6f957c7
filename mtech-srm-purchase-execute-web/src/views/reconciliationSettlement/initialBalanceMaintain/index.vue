<template>
  <!-- 期初余额维护-采方 -->
  <div class="full-height pt20">
    <mt-template-page
      ref="initBalanceRef"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>

    <!-- 新增/编辑弹窗 -->
    <add-dialog
      v-if="dialogType"
      ref="addDailogRef"
      :dialog-type="dialogType"
      @addSuccess="reloadGrid"
    ></add-dialog>

    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadInitBalanceRef"
      :down-template-name="downTemplateName"
      :request-urls="requestUrls"
      :down-template-params="downTemplateParams"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { columnData } from './config'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  components: {
    addDialog: require('./components/addDialog.vue').default,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      componentConfig: [
        {
          useToolTemplate: false,
          useBaseConfig: true, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          toolbar: [
            {
              id: 'Add',
              icon: 'icon_solid_Createorder',
              title: this.$t('新增'),
              permission: ['O_02_0423']
            },
            {
              id: 'Delete',
              icon: 'icon_solid_Delete',
              title: this.$t('删除'),
              permission: ['O_02_0425']
            },
            {
              id: 'upload',
              icon: 'icon_solid_Import',
              title: this.$t('导入'),
              permission: ['O_02_0690']
            },
            {
              id: 'outport',
              icon: 'icon_solid_export',
              title: this.$t('导出'),
              permission: ['O_02_0691']
            }
          ],
          gridId: 'f6a929dd-2e50-49b6-b6c5-8c701475eeca',
          grid: {
            lineSelection: true,
            columnData: columnData,
            asyncConfig: {
              url: `${BASE_TENANT}/transaction_reconciliation/balance/pageQuery`
            },
            frozenColumns: 1
          }
        }
      ],
      dialogType: null, // 新增、编辑
      // 上传excel的参数
      downTemplateName: this.$t('期初余额维护模板'),
      requestUrls: {},
      downTemplateParams: {
        pageFlag: false
      }
    }
  },
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id == 'Add') {
        this.dialogType = 'add'
        setTimeout(() => {
          this.$refs.addDailogRef.$refs.addFormRef.resetFields()
          this.$refs.addDailogRef.$refs.dialog.ejsRef.show()
        }, 10)
      } else if (e.toolbar.id == 'Delete') {
        let selectedRows = e.gridRef.getMtechGridRecords()
        if (!selectedRows.length) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        let ids = selectedRows.map((item) => item.id)
        this.handleDelete(ids)
      } else if (e.toolbar.id == 'upload') {
        this.handleUpload()
      } else if (e.toolbar.id == 'outport') {
        this.handleExport()
      }
    },

    handleClickCellTool(e) {
      if (e.tool.id == 'edit') {
        this.dialogType = 'edit'
        setTimeout(() => {
          this.$refs.addDailogRef.setAddFormData(e.data)
        }, 10)
      } else if (e.tool.id == 'delete') {
        this.handleDelete([e.data.id])
      }
    },

    // 删除
    handleDelete(ids) {
      console.log(this.$refs.initBalanceRef)
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('确认删除该条数据？')
        },
        success: () => {
          this.$API.reconciliationSettlement.deleteInitBalance({ ids }).then(() => {
            this.$toast({ content: this.$t('删除成功'), type: 'success' })
            this.$refs.initBalanceRef.refreshCurrentGridData()
          })
        }
      })
    },

    // 导出
    handleExport() {
      let rules = this.$refs.initBalanceRef.getCurrentUsefulRef()?.pluginRef?.queryBuilderRules
      let page = {
        size: 1000,
        current: 1
      }
      let params = rules
        ? {
            ...rules,
            page,
            pageFlag: true
          }
        : {
            pageFlag: true
          }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement.exportInitBalance(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },

    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadInitBalanceRef.uploadData = [] // 清空数据
        this.$refs.uploadInitBalanceRef.$refs.uploader.files = []
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },

    // 上传（显示弹窗）
    handleUpload() {
      this.requestUrls = {
        templateUrlPre: 'reconciliationSettlement',
        templateUrl: 'exportInitBalance',
        uploadUrl: 'importInitBalance'
      }
      this.showUploadExcel(true)
    },

    upExcelConfirm() {
      this.showUploadExcel(false)
      this.reloadGrid()
    },

    // 重新加载表格
    reloadGrid() {
      this.$refs.initBalanceRef.refreshCurrentGridData()
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  },

  beforeDestroy() {
    sessionStorage.removeItem('initBalanceCompamy')
  }
}
</script>

<style lang="scss" scoped></style>
