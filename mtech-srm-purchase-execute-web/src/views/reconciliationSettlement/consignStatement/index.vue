<template>
  <!-- 采方-寄售报表 -->
  <div class="full-height pt20">
    <mt-template-page ref="templateRef" :template-config="componentConfig" />
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { consignmentCol } from './config'
export default {
  data() {
    return {
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          gridId: this.$tableUUID.reconciliationSettlement.consignStatement.list,
          grid: {
            lineIndex: 0,
            columnData: consignmentCol,
            dataSource: [],
            // 寄售报表汇总查询
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliation/consignment/queryConsignmentSum`
            }
          }
        }
      ],
      asyncOriginTabs: []
    }
  },

  mounted() {},

  methods: {}
}
</script>

<style></style>
