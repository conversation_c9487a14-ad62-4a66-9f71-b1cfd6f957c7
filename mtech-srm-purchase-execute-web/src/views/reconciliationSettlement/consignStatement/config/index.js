import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName } from '@/utils/utils'
export const consignmentCol = [
  {
    width: '250',
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    searchOptions: MasterDataSelect.factoryAddress,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.siteCode, data?.siteName)
    }
  },
  {
    width: '150',
    field: 'itemVoucherDate',
    headerText: i18n.t('对账日期'),
    searchOptions: MasterDataSelect.dateRange
  },
  {
    width: '250',
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    searchOptions: MasterDataSelect.supplier,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    searchOptions: MasterDataSelect.material
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    ignore: true
  },
  {
    width: '150',
    field: 'monthBeginQty',
    headerText: i18n.t('期初库存')
  },
  {
    width: '150',
    field: 'monthWarehouseQty',
    headerText: i18n.t('本月入库')
  },
  {
    width: '150',
    field: 'monthIssueQty',
    headerText: i18n.t('本月出库')
  },
  {
    width: '150',
    field: 'monthAllocationQty',
    headerText: i18n.t('本月移库')
  },
  {
    width: '150',
    field: 'monthBalanceQty',
    headerText: i18n.t('本月结余')
  }
]
