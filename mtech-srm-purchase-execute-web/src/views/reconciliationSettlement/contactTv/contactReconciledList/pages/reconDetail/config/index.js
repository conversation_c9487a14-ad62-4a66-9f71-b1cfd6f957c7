import Vue from 'vue'
import { i18n } from '@/main.js'
import UTILS from '@/utils/utils'
import { codeNameColumn } from '@/utils/utils'
import bigDecimal from 'js-big-decimal'

// 页面显示类型
export const EntryType = {
  view: 1, // 查看
  edit: 2 // 继续发布时就行编辑
}
// 添加 对账差异 字段到列表中
export const addTheReconDiffToList = (list) => {
  if (list.length > 0) {
    // 往来对账单 对账差异 前端定义、计算 = 采方期末+供方期末
    list.forEach((item) => {
      const closingBalance = item.closingBalance || 0 // 采方期末
      const supplierClosingBalance = item.supplierClosingBalance || 0 // 供方期末
      // 对账差异
      item.theReconDiff = bigDecimal.add(closingBalance, supplierClosingBalance)
    })
  }
  return list
}

// 状态 0-新建；1-待反馈；2-反馈正常；3-反馈异常；4-采购已确认；5-关闭；6-财务已确认
export const ReconciledStatus = {
  new: 0, // 新建
  pending: 1, // 待反馈
  normal: 2, // 反馈正常
  abnormal: 3, // 反馈异常
  confirmed: 4, // 采购已确认
  close: 5, // 关闭
  finConfirmed: 6, // 财务已确认
  finished: 7 // 已完结
}

// 往来类别 text
export const ReconciledStatusText = {
  [ReconciledStatus.new]: i18n.t('新建'),
  [ReconciledStatus.pending]: i18n.t('待反馈'),
  [ReconciledStatus.normal]: i18n.t('反馈正常'),
  [ReconciledStatus.abnormal]: i18n.t('反馈异常'),
  [ReconciledStatus.confirmed]: i18n.t('采购已确认'),
  [ReconciledStatus.close]: i18n.t('关闭'),
  [ReconciledStatus.finConfirmed]: i18n.t('财务已确认'),
  [ReconciledStatus.finished]: i18n.t('已完结')
}

// 往来类别 对应的 css class
export const ReconciledStatusCssClass = {
  [ReconciledStatus.new]: 'col-active',
  [ReconciledStatus.pending]: 'col-active',
  [ReconciledStatus.normal]: 'col-active',
  [ReconciledStatus.abnormal]: 'col-active',
  [ReconciledStatus.confirmed]: 'col-inactive',
  [ReconciledStatus.close]: 'col-inactive',
  [ReconciledStatus.finConfirmed]: 'col-inactive',
  [ReconciledStatus.finished]: 'col-inactive'
}

// 往来类别 对应的 Options
export const ReconciledStatusOptions = [
  {
    // 新建
    value: ReconciledStatus.new,
    text: ReconciledStatusText[ReconciledStatus.new],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.new]
  },
  {
    // 待反馈
    value: ReconciledStatus.pending,
    text: ReconciledStatusText[ReconciledStatus.pending],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.pending]
  },
  {
    // 反馈正常
    value: ReconciledStatus.normal,
    text: ReconciledStatusText[ReconciledStatus.normal],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.normal]
  },
  {
    // 反馈异常
    value: ReconciledStatus.abnormal,
    text: ReconciledStatusText[ReconciledStatus.abnormal],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.abnormal]
  },
  {
    // 采购已确认
    value: ReconciledStatus.confirmed,
    text: ReconciledStatusText[ReconciledStatus.confirmed],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.confirmed]
  },
  {
    // 不查询关闭额数据
    // 关闭
    value: ReconciledStatus.close,
    text: ReconciledStatusText[ReconciledStatus.close],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.close]
  },
  {
    // 财务已确认
    value: ReconciledStatus.finConfirmed,
    text: ReconciledStatusText[ReconciledStatus.finConfirmed],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.finConfirmed]
  },
  {
    // 已完结
    value: ReconciledStatus.finished,
    text: ReconciledStatusText[ReconciledStatus.finished],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.finished]
  }
]

// 清账状态 0未清 1已清
export const CleanStatus = {
  notYet: '0', // 未清
  already: '1' // 已清
}

// 清账状态 text
export const CleanStatusText = {
  [CleanStatus.notYet]: i18n.t('未清'),
  [CleanStatus.already]: i18n.t('已清')
}

// 清账状态 对应的 css class
export const CleanStatusCssClass = {
  [CleanStatus.notYet]: '',
  [CleanStatus.already]: ''
}

// 清账状态 对应的 Options
export const CleanStatusOptions = [
  {
    // 未清
    value: CleanStatus.notYet,
    text: CleanStatusText[CleanStatus.notYet],
    cssClass: CleanStatusCssClass[CleanStatus.notYet]
  },
  {
    // 已清
    value: CleanStatus.already,
    text: CleanStatusText[CleanStatus.already],
    cssClass: CleanStatusCssClass[CleanStatus.already]
  }
]
// 往来待对账明细 表格列数据
export const columnData = [
  {
    field: 'code', // 往来对账单号
    headerText: i18n.t('往来对账单号'),
    cellTools: [],
    width: '150'
  },
  {
    field: 'status', // 状态 状态 0-新建；1-待反馈；2-反馈正常；3-反馈异常；4-采购已确认；5-关闭；
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: ReconciledStatusOptions
    },
    cellTools: [
      {
        id: 'ConfirmStatement',
        icon: '',
        title: i18n.t('确认'),
        // permission: ["O_02_0419"],
        visibleCondition: (data) => data.status == ReconciledStatus.normal // 反馈正常
      },
      {
        id: 'ContinueStatement',
        icon: '',
        title: i18n.t('继续发布'),
        // permission: ["O_02_0421"],
        visibleCondition: (data) => data.status == ReconciledStatus.abnormal // 反馈异常
      },
      {
        id: 'CloseStatement',
        icon: '',
        title: i18n.t('关闭'),
        // permission: ["O_02_0420"],
        visibleCondition: (data) =>
          data.status == ReconciledStatus.new ||
          data.status == ReconciledStatus.pending ||
          data.status == ReconciledStatus.normal ||
          data.status == ReconciledStatus.abnormal // 新建 待反馈 反馈正常 反馈异常
      }
    ]
  },
  {
    field: 'companyCode', // 公司
    headerText: i18n.t('公司'),
    template: codeNameColumn({
      firstKey: 'companyCode',
      secondKey: 'companyName'
    })
    // companyName
  },
  {
    field: 'supplierCode', // 供应商
    headerText: i18n.t('供应商'),
    template: codeNameColumn({
      firstKey: 'supplierCode',
      secondKey: 'supplierName'
    })
    // supplierName
  },
  {
    field: 'openingBalance', // 期初余额
    headerText: i18n.t('期初余额'),
    width: '150'
  },
  {
    field: 'closingBalance', // 期末余额 采方期末余额
    headerText: i18n.t('期末余额'),
    width: '150'
  },
  {
    field: 'theReconDiff', // 对账差异 前端定义、计算 = 采方期末余额-供方期末余额
    headerText: i18n.t('对账差异'),
    width: '150',
    template: () => {
      return {
        template: Vue.component('theReconDiffComponent', {
          template: `<div :class="setClassNameByData()">{{data.theReconDiff}}</div>`,
          data: function () {
            return { data: {} }
          },
          methods: {
            setClassNameByData() {
              let cssClass = ''
              if (this.data.theReconDiff > 0) {
                // 对账差异 > 0 蓝色
                cssClass = 'the-recon-diff-blue'
              } else if (this.data.theReconDiff < 0) {
                // 对账差异 < 0 红色
                cssClass = 'the-recon-diff-red'
              }

              return cssClass
            }
          }
        })
      }
    }
    // supplierClosingBalance	供方期末余额
  },
  {
    field: 'currencyName', // 币种
    headerText: i18n.t('币种'),
    width: '150'
  },
  {
    field: 'remark', // 采方备注
    headerText: i18n.t('采方备注'),
    width: '150'
  },
  {
    field: 'supRemark', // 供方备注
    headerText: i18n.t('供方备注'),
    width: '150'
  },
  {
    fieldCode: 'docNo', // 单据号
    fieldName: i18n.t('单据号')
  },
  {
    fieldCode: 'type', // 往来类别
    fieldName: i18n.t('往来类别')
  },
  {
    fieldCode: 'reconciliationAccountName', // 对账科目
    fieldName: i18n.t('对账科目')
  },
  {
    fieldCode: 'accountDate', // 入账日期
    fieldName: i18n.t('入账日期')
  },
  {
    fieldCode: 'invoiceNo', // 发票号
    fieldName: i18n.t('发票号')
  },
  {
    fieldCode: 'amount', // 金额
    fieldName: i18n.t('金额')
  },
  {
    fieldCode: 'cleanStatus', // 清账状态
    fieldName: i18n.t('清账状态')
  },
  {
    field: 'createUserName', // 创建人
    headerText: i18n.t('创建人'),
    width: '150'
  },
  {
    field: 'createTime', // 创建时间
    headerText: i18n.t('创建时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return e
        }
      }
    }
  }
]
