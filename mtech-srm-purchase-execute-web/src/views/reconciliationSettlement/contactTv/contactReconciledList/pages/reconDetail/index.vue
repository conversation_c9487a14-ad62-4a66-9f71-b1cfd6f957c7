<template>
  <!-- 采方-对账协同-采购对账单列表 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickCellTool="handleClickCellTool"
      @handleCustomReset="handleCustomReset"
      class="template-height"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="code" :label="$t('往来对账单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.code"
                :show-clear-button="true"
                :placeholder="$t('请输入往来对账单号')"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-select
                style="flex: 1"
                v-model="searchFormModel.status"
                :data-source="ReconciledStatusOptions"
                :show-clear-button="true"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="companyCodes" :label="$t('公司')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.companyCodes"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                multiple
                :placeholder="$t('请选择公司')"
                :fields="{ text: 'orgName', value: 'orgCode' }"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="supplierCodes" :label="$t('供应商编码')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.supplierCodes"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择供应商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="deadLine" :label="$t('对账月份')" label-style="top">
              <mt-date-range-picker
                style="flex: 1"
                v-model="searchFormModel.deadLine"
                @change="(e) => handleDateTimeChange(e, 'deadLine')"
                :placeholder="$t('请选择对账月份')"
              />
            </mt-form-item>
            <mt-form-item prop="openingBalance" :label="$t('期初余额')" label-style="top">
              <mt-input
                v-model="searchFormModel.openingBalance"
                :show-clear-button="true"
                :placeholder="$t('请输入期初余额')"
              />
            </mt-form-item>
            <mt-form-item prop="closingBalance" :label="$t('期末余额')" label-style="top">
              <mt-input
                v-model="searchFormModel.closingBalance"
                :show-clear-button="true"
                :placeholder="$t('请输入期末余额')"
              />
            </mt-form-item>
            <mt-form-item prop="theReconDiff" :label="$t('对账差异')" label-style="top">
              <mt-input
                v-model="searchFormModel.theReconDiff"
                :show-clear-button="true"
                :placeholder="$t('请输入对账差异')"
              />
            </mt-form-item>
            <mt-form-item prop="remark" :label="$t('采方备注')" label-style="top">
              <mt-input
                v-model="searchFormModel.remark"
                :show-clear-button="true"
                :placeholder="$t('请输入采方备注')"
              />
            </mt-form-item>
            <mt-form-item prop="supRemark" :label="$t('供方备注')" label-style="top">
              <mt-input
                v-model="searchFormModel.supRemark"
                :show-clear-button="true"
                :placeholder="$t('请输入供方备注')"
              />
            </mt-form-item>
            <mt-form-item prop="financeRemark" :label="$t('财务备注')" label-style="top">
              <mt-input
                v-model="searchFormModel.financeRemark"
                :show-clear-button="true"
                :placeholder="$t('请输入财务备注')"
              />
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input
                v-model="searchFormModel.createUserName"
                :show-clear-button="true"
                :placeholder="$t('请输入创建人')"
              />
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
              <mt-date-range-picker
                style="flex: 1"
                v-model="searchFormModel.createTime"
                @change="(e) => handleDateTimeChange(e, 'createTime')"
                :placeholder="$t('请选择创建时间')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import {
  columnData,
  addTheReconDiffToList,
  ReconciledStatusOptions,
  ReconciledStatus,
  EntryType
} from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      ReconciledStatusOptions,
      searchFormModel: {},
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          // isUseCustomSearch: true,
          // isCustomSearchRules: true,
          toolbar: [],
          grid: {
            lineIndex: 0,
            columnData,
            dataSource: [],
            // 租户级-采方往来对账明细-分页查询特定对账单的明细，包含主单信息
            asyncConfig: {
              url: `${BASE_TENANT}/tv/transactionItem/page/query`,
              defaultRules: [],
              params: {
                belong: 0 // 数据归属，0-采方,1-供方
              },
              // 往来对账单列表 序列化
              serializeList: (list) => {
                const afterSerialization = addTheReconDiffToList(list)
                return afterSerialization
              }
            }
          }
        }
      ]
    }
  },
  created() {},
  methods: {
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = dayjs(e.startDate).format('YYYYMM')
        this.searchFormModel[field + 'End'] = dayjs(e.endDate).format('YYYYMM')
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    handleClickToolBar(args) {
      const { gridRef, toolbar } = args
      const selectRows = gridRef.getMtechGridRecords()
      if (
        selectRows.length === 0 &&
        (toolbar.id == 'ConfirmStatement' ||
          toolbar.id == 'BackStatement' ||
          toolbar.id == 'ConfirmStatementFinance')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedIds = []
      selectRows.forEach((item) => {
        selectedIds.push(item.id)
      })

      if (toolbar.id === 'ConfirmStatement') {
        // 往来对账单列表-采购批量确认
        this.handleConfirmStatement({
          selectedRecords: selectRows,
          idList: selectedIds
        })
      } else if (toolbar.id === 'BackStatement') {
        // 往来对账单列表-采购批量退回
        this.handleBackStatement({
          selectedRecords: selectRows,
          idList: selectedIds
        })
      } else if (toolbar.id === 'ConfirmStatementFinance') {
        // 往来对账单列表-财务批量确认
        this.handleConfirmStatementFinance({
          selectedRecords: selectRows,
          idList: selectedIds
        })
        // } else if (toolbar.id == "printRecon") {
        //   this.handlePrint(selectedIds);
      } else if (toolbar.id === 'ExcelExport') {
        this.handleExport()
      }
    },
    // CellTool
    handleClickCellTool(args) {
      const { tool, data } = args
      if (tool.id === 'ConfirmStatement') {
        // 确认
        this.handleConfirmStatement({
          selectedRecords: data,
          idList: [data.id]
        })
      } else if (tool.id === 'ConfirmStatement1') {
        // 确认
        this.postTransactionReconciliationFinace([data.id])
      } else if (tool.id === 'CloseStatement') {
        // 关闭
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认关闭选中的数据？')
          },
          success: () => {
            // 租户级-往来对账单-采方-关闭往来对账单
            this.postTransactionReconciliationClose([data.id])
          }
        })
      } else if (tool.id === 'BackStatement') {
        // 采购退回
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认退回选中的数据？')
          },
          success: () => {
            this.postTransactionReconciliationBack([data.id])
          }
        })
      } else if (tool.id === 'BackStatement' || tool.id === 'BackStatement1') {
        // 财务退回
        const dialogConfig = {
          data: {
            title: this.$t('财务退回'),
            message: this.$t('确认退回选中的数据？')
          },
          success: (financeRemark) => {
            // 已完结状态财务驳回
            if (data.status === 7) {
              this.postTransactionReconciliationFinanceReject(data.id)
              return
            }
            // 退回
            this.postTransactionReconciliationFinanceBack([data.id], financeRemark)
          }
        }
        if (data.status !== 7) {
          dialogConfig.modal = () =>
            import(
              /* webpackChunkName: "./components/rejectDialog.vue" */ './components/rejectDialog.vue'
            )
        }
        this.$dialog(dialogConfig)
      } else if (tool.id === 'ContinueStatement') {
        // 继续发布
        // 将 lastTabIndex 放到 localStorage 往来对账单详情-采方 读
        localStorage.setItem('lastTabIndex', JSON.stringify(1))
        // 跳转 往来对账单详情-采方
        this.$router.push({
          name: 'contact-reconciled-detail-tv',
          query: {
            id: data.id,
            code: data.code,
            type: EntryType.edit // 编辑
          }
        })
      }
    },
    // CellTitle
    handleClickCellTitle(args) {
      const { field, data } = args
      if (field === 'code') {
        // 将 lastTabIndex 放到 localStorage 往来对账单详情-采方 读
        localStorage.setItem('lastTabIndex', JSON.stringify(1))
        // 跳转 往来对账单详情-采方
        // 如果反馈异常状态 可以edit操作
        this.$router.push({
          name: 'contact-reconciled-detail-tv',
          query: {
            id: data.headerId,
            type: data.status === ReconciledStatus.abnormal ? EntryType.edit : EntryType.view, // 查看
            code: data.code
          }
        })
      }
    },
    // 采购确认对账单
    handleConfirmStatement(args) {
      const { selectedRecords, idList } = args
      // 校验状态是否未 反馈正常
      let valid = true
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        if (item.status != ReconciledStatus.normal) {
          valid = false
          break
        }
      }
      if (valid) {
        // 租户级-往来对账单-采方-确认往来对账单
        this.postTransactionReconciliationFinish(idList)
      } else {
        // 校验未通过
        this.$toast({
          content: this.$t('请选择反馈正常的数据'),
          type: 'warning'
        })
      }
    },
    // 采购批量退回
    handleBackStatement(args) {
      const { selectedRecords, idList } = args
      // 校验状态是否为 反馈异常
      let valid = true
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        if (item.status != ReconciledStatus.abnormal) {
          valid = false
          break
        }
      }
      if (valid) {
        // 采购批量退回
        this.postTransactionReconciliationBack(idList)
      } else {
        // 校验未通过
        this.$toast({
          content: this.$t('请选择反馈异常的数据'),
          type: 'warning'
        })
      }
    },
    // 财务确认对账单
    handleConfirmStatementFinance(args) {
      const { selectedRecords, idList } = args
      // 校验状态是否未 采购已确认
      let valid = true
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        if (item.status != ReconciledStatus.confirmed) {
          valid = false
          break
        }
      }
      if (valid) {
        // 租户级-往来对账单-采方-确认往来对账单
        this.postTransactionReconciliationFinace(idList)
      } else {
        // 校验未通过
        this.$toast({
          content: this.$t('请选择采购已确认的数据'),
          type: 'warning'
        })
      }
    },
    // 租户级-往来对账单-采方-确认往来对账单
    postTransactionReconciliationFinish(idList) {
      const params = {
        ids: idList
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationFinish(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-往来对账单-采方-财务确认往来对账单
    postTransactionReconciliationFinace(idList) {
      const params = {
        ids: idList
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationFinace(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-往来对账单-采方-关闭往来对账单
    postTransactionReconciliationClose(idList) {
      const params = {
        id: idList[0]
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationClose(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-往来对账单-采方-采购退回往来对账单
    postTransactionReconciliationBack(idList) {
      const params = {
        ids: idList
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationBack(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-往来对账单-采方-财务退回往来对账单
    postTransactionReconciliationFinanceBack(idList, financeRemark) {
      const params = {
        ids: idList,
        financeRemark
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationFinanceBack(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-往来对账单-采方-财务退回往来对账单
    postTransactionReconciliationFinanceReject(id) {
      const params = {
        id: id
      }
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationFinanceReject(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 导出
    handleExport() {
      let params = {
        ...this.searchFormModel,
        page: { current: 1, size: 10000 }
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement.reconciliationTransactionExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 对账差异 theReconDiffTemplate
/deep/ .the-recon-diff-blue {
  color: #6386c1; // 这是 UI 给的颜色
}
/deep/ .the-recon-diff-red {
  color: #ed5633; // 这是 UI 给的颜色
}
</style>
