<template>
  <div class="fields-config-page mt-flex-direction-column">
    <div class="top-info mt-flex-direction-column">
      <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
    </div>
    <div class="config-container">
      <recon v-if="tabIndex == 0" />
      <reconDetail v-if="tabIndex == 1" />
    </div>
  </div>
</template>

<script>
export default {
  components: {
    recon: require('./pages/recon/index.vue').default,
    reconDetail: require('./pages/reconDetail/index.vue').default
  },
  data() {
    // const purTabList = [
    //   {
    //     title: this.$t('往来对账单列表')
    //   },
    //   {
    //     title: this.$t('往来对账单明细列表')
    //   }
    // ]
    return {
      tabIndex: 0,
      tabSource: [
        {
          title: this.$t('往来对账单列表')
        },
        {
          title: this.$t('往来对账单明细列表')
        }
      ]
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    }
  }
}
</script>

<style></style>
