<template>
  <div>
    <mt-template-page
      ref="purchaseDetailsRef"
      class="template-height"
      :template-config="purchaseComponentConfig"
      @handleClickToolBar="purchaseDetailClickToolBar"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <!-- <mt-form-item prop="type" :label="$t('往来类别')" label-style="top">
              <mt-select
                v-model="searchFormModel.type"
                :data-source="ContactsTypeOptions"
                :show-clear-button="true"
                :placeholder="$t('请选择往来类别')"
              />
            </mt-form-item> -->
            <mt-form-item
              prop="reconciliationAccountName"
              :label="$t('会计科目名称')"
              label-style="top"
            >
              <mt-input
                v-model="searchFormModel.reconciliationAccountName"
                :show-clear-button="true"
                :placeholder="$t('请输入会计科目名称')"
              />
            </mt-form-item>
            <mt-form-item
              prop="reconciliationAccount"
              :label="$t('会计科目代码')"
              label-style="top"
            >
              <mt-input
                v-model="searchFormModel.reconciliationAccount"
                :show-clear-button="true"
                :placeholder="$t('请输入会计科目代码')"
              />
            </mt-form-item>
            <!-- <mt-form-item prop="invoiceNo" :label="$t('发票号')" label-style="top">
              <mt-input
                v-model="searchFormModel.invoiceNo"
                :show-clear-button="true"
                :placeholder="$t('请输入发票号')"
              />
            </mt-form-item> -->
            <mt-form-item prop="amount" :label="$t('金额')" label-style="top">
              <mt-input
                v-model="searchFormModel.amount"
                :show-clear-button="true"
                :placeholder="$t('请输入金额')"
              />
            </mt-form-item>
            <mt-form-item prop="currencyCode" :label="$t('币种')" label-style="top">
              <mt-input
                v-model="searchFormModel.currencyCode"
                :show-clear-button="true"
                :placeholder="$t('请输入币种')"
              />
            </mt-form-item>
            <!-- <mt-form-item prop="currencyCode" :label="$t('币种编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.currencyCode"
                :show-clear-button="true"
                :placeholder="$t('请输入币种编码')"
              />
            </mt-form-item> -->
            <mt-form-item prop="docNo" :label="$t('入账凭证编号')" label-style="top">
              <mt-input
                v-model="searchFormModel.docNo"
                :show-clear-button="true"
                :placeholder="$t('请输入入账凭证编号')"
              />
            </mt-form-item>
            <!-- <mt-form-item prop="lineNo" :label="$t('行号')" label-style="top">
              <mt-input
                v-model="searchFormModel.lineNo"
                :show-clear-button="true"
                :placeholder="$t('请输入行号')"
              />
            </mt-form-item> -->
            <mt-form-item prop="digest" :label="$t('摘要')" label-style="top">
              <mt-input
                v-model="searchFormModel.digest"
                :show-clear-button="true"
                :placeholder="$t('请输入摘要')"
              />
            </mt-form-item>
            <!-- <mt-form-item prop="digest1" :label="$t('摘要1')" label-style="top">
              <mt-input
                v-model="searchFormModel.digest1"
                :show-clear-button="true"
                :placeholder="$t('请输入摘要1')"
              />
            </mt-form-item>
            <mt-form-item prop="digest2" :label="$t('摘要2')" label-style="top">
              <mt-input
                v-model="searchFormModel.digest2"
                :show-clear-button="true"
                :placeholder="$t('请输入摘要2')"
              />
            </mt-form-item> -->
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { DetailsColumnData, ContactsTypeOptions } from '../config/constant'
import { formatDetailsTableColumnData } from '../config/index'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  name: 'PurchaseDetail',
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    const statementId = this.$route?.query?.id // 从URL获取对账单id
    // const code = this.$route?.query?.code // 从URL获取对账单code
    let purchaseDetailToolbar = [
      {
        id: 'PurchaseDetailExport',
        icon: 'icon_solid_Import',
        title: this.$t('导出')
      }
    ]
    return {
      searchFormModel: {},
      apiWaitingQuantity: 0, // 调用的api正在等待数
      ContactsTypeOptions,
      // 采方-往来明细
      purchaseComponentConfig: [
        {
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          isUseCustomEditor: true,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: purchaseDetailToolbar, // 供方-往来明细 操作按钮
          gridId:
            this.$tableUUID.reconciliationCollaboration.contactDetailSupplier.purchaserDetailsTab,
          grid: {
            editSettings: {
              allowEditing: false
            },
            allowPaging: true, // 分页
            // lineIndex: 0,
            columnData: formatDetailsTableColumnData({
              data: DetailsColumnData.filter((item) => item.fieldCode !== 'checkBox')
            }),
            dataSource: [],
            // 分页查询特定对账单的明细
            asyncConfig: {
              url: `${BASE_TENANT}/tv/transactionItem/page/query`,
              params: {
                belong: 0, // 数据归属，0-采方,1-供方
                headerId: statementId
              },
              // defaultRules: [],
              serializeList: this.serializeDetailList
            }
          }
        }
      ]
    }
  },
  methods: {
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    //采方往来明细 ToolBar
    purchaseDetailClickToolBar(args) {
      if (args.toolbar.id === 'PurchaseDetailExport') {
        // 供方往来明细-导出
        this.purchaseDetailExport()
      }
    },
    // 采方-导出
    purchaseDetailExport() {
      const params = {
        page: { current: 1, size: 10000 },
        headerId: this.headerInfo.id,
        isDownLoad: true
      } // 筛选条件
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .PuPuchaseDetailExportTv(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 往来明细 数据序列化
    serializeDetailList(list) {
      if (list.length > 0) {
        list.forEach((item, index) => {
          item.serialNumber = index + 1 // 序号
          item.thePrimaryKey = item.id // 主键
        })
      }
      return list
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped></style>
