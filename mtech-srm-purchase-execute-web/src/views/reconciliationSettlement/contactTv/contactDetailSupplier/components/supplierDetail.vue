<template>
  <div>
    <mt-template-page
      ref="supplierDetailsRef"
      class="template-height"
      :template-config="supplierComponentConfig"
      @handleClickToolBar="supplierDetailClickToolBar"
      @actionBegin="supplierDetailActionBegin"
      @actionComplete="supplierDetailActionComplete"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <!-- <mt-form-item prop="type" :label="$t('往来类别')" label-style="top">
              <mt-select
                v-model="searchFormModel.type"
                :data-source="ContactsTypeOptions"
                :show-clear-button="true"
                :placeholder="$t('请选择往来类别')"
              />
            </mt-form-item> -->
            <mt-form-item prop="code" :label="$t('对账单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.code"
                :show-clear-button="true"
                :placeholder="$t('请输入对账单号')"
              />
            </mt-form-item>
            <mt-form-item prop="invoiceNo" :label="$t('入账发票号')" label-style="top">
              <mt-input
                v-model="searchFormModel.invoiceNo"
                :show-clear-button="true"
                :placeholder="$t('请输入入账发票号')"
              />
            </mt-form-item>
            <mt-form-item prop="amount" :label="$t('金额')" label-style="top">
              <mt-input
                v-model="searchFormModel.amount"
                :show-clear-button="true"
                :placeholder="$t('请输入金额')"
              />
            </mt-form-item>
            <mt-form-item prop="currencyCode" :label="$t('币种')" label-style="top">
              <mt-input
                v-model="searchFormModel.currencyCode"
                :show-clear-button="true"
                :placeholder="$t('请输入币种')"
              />
            </mt-form-item>
            <!-- <mt-form-item prop="currencyCode" :label="$t('币种编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.currencyCode"
                :show-clear-button="true"
                :placeholder="$t('请输入币种编码')"
              />
            </mt-form-item> -->
            <!-- <mt-form-item prop="claimDemageNo" :label="$t('索赔单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.claimDemageNo"
                :show-clear-button="true"
                :placeholder="$t('请输入索赔单号')"
              />
            </mt-form-item> -->
            <!-- <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
              <mt-input
                v-model="searchFormModel.remark"
                :show-clear-button="true"
                :placeholder="$t('请输入备注')"
              />
            </mt-form-item> -->
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import {
  SupplierDetailsColumnData,
  EntryType,
  EditSettings,
  RequestType,
  ActionType,
  SupplierDetailNewRowData,
  ContactsTypeOptions
} from '../config/constant'
import { formatDetailsTableColumnData } from '../config/index'
import { rowDataTemp } from '../config/variable'
import { BASE_TENANT } from '@/utils/constant'
import { cloneDeep } from 'lodash'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  name: 'SupplierDetail',
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const entryType = this.$route?.query?.type // 从URL获取页面状态
    const statementId = this.$route?.query?.id // 从URL获取对账单id
    let supplierDetailToolbar = [
      {
        id: 'SupplierDetailExport',
        icon: 'icon_solid_Import',
        title: this.$t('导出')
      }
    ]
    // 供方-往来明细 操作按钮
    if (entryType == EntryType.edit) {
      // 页面显示类型 = 编辑
      supplierDetailToolbar = [
        {
          id: 'SupplierDetailSave',
          icon: 'icon_table_save',
          title: this.$t('保存')
        },
        {
          id: 'SupplierDetailAdd',
          icon: 'icon_table_new',
          title: this.$t('新增')
        },
        {
          id: 'SupplierDetailDelete',
          icon: 'icon_table_delete',
          title: this.$t('删除')
        },
        {
          id: 'SupplierDetailUpdate',
          icon: 'icon_table_save',
          title: this.$t('更新')
        },
        {
          id: 'SupplierDetailImport',
          icon: 'icon_solid_Import',
          title: this.$t('导入')
        },
        {
          id: 'SupplierDetailExport',
          icon: 'icon_solid_Import',
          title: this.$t('导出')
        }
      ]
    }
    return {
      searchFormModel: {},
      apiWaitingQuantity: 0, // 调用的api正在等待数
      ContactsTypeOptions,
      // 供方-往来明细
      supplierComponentConfig: [
        {
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          isUseCustomEditor: true,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: supplierDetailToolbar, // 供方-往来明细 操作按钮
          gridId:
            this.$tableUUID.reconciliationCollaboration.contactDetailSupplier.supplierDetailsTab,
          grid: {
            editSettings: {
              ...EditSettings,
              allowEditing: entryType == EntryType.edit
            },
            allowPaging: true, // 分页
            // lineIndex: 0,
            columnData: formatDetailsTableColumnData({
              data: SupplierDetailsColumnData
            }),
            dataSource: [],
            // 分页查询特定对账单的明细
            asyncConfig: {
              url: `${BASE_TENANT}/tv/transactionItem/page/query`,
              params: {
                belong: 1, // 数据归属，0-采方,1-供方
                headerId: statementId
              },
              // defaultRules: [],
              serializeList: this.serializeDetailList
            }
          }
        }
      ]
    }
  },
  methods: {
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 供方往来明细 actionBegin 表格编辑生命周期
    supplierDetailActionBegin(args) {
      const { requestType, action, rowData } = args
      if (requestType === RequestType.add) {
        // 开始行添加
        this.isEditing = true
        // 即将添加一行，赋值新增行的初始数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        const newRowData = cloneDeep(SupplierDetailNewRowData)
        newRowData.headerId = this.statementId // 主单id
        rowDataTemp.push(newRowData)
        args.rowData = newRowData
        args.data = newRowData
      } else if (requestType === RequestType.save && action === ActionType.add) {
        // 即将保存新增时，将新增中编辑的数据付给组件
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === RequestType.beginEdit) {
        // 开始行编辑
        this.isEditing = true
        // 即将编辑行，赋值当前行的数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },
    // 供方往来明细 actionComplete 表格编辑生命周期
    supplierDetailActionComplete(args) {
      const { requestType, action, rowIndex, index } = args
      const rowData = rowDataTemp[rowDataTemp.length - 1]

      if (requestType === RequestType.save && action == ActionType.edit) {
        // 完成编辑行
        this.isEditing = false
        // rowData, rowIndex
        if (!this.isValidDetailSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.supplierDetailsRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.supplierDetailsRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API 租户级-往来对账明细供应商控制器-新增或修改
          this.postTransactionReconciliationItemSupplierSave({
            rowData,
            rowIndex
          })
        }
      } else if (requestType === RequestType.save && action == ActionType.add) {
        // 完成新增行
        this.isEditing = false
        if (!this.isValidDetailSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.supplierDetailsRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
          this.$refs.supplierDetailsRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API 租户级-往来对账明细供应商控制器-新增或修改
          this.postTransactionReconciliationItemSupplierSave({
            rowData,
            rowIndex: index
          })
        }
      }
    },
    // 校验 往来明细 数据
    isValidDetailSaveData(data) {
      const { accountDate, amount, currencyCode } = data
      let valid = true
      if (!accountDate) {
        this.$toast({ content: this.$t('请选择入账日期'), type: 'warning' })
        valid = false
      } else if (!amount && amount !== 0) {
        this.$toast({ content: this.$t('请填写金额'), type: 'warning' })
        valid = false
      } else if (!currencyCode) {
        this.$toast({ content: this.$t('请选择币种'), type: 'warning' })
        valid = false
      } else if (amount || amount === 0) {
        const _message = this.validAmount(data)
        if (_message) {
          this.$toast({ content: _message, type: 'warning' })
          valid = false
        }
      }
      // if (type == '9') {
      //   if (!remark) {
      //     this.$toast({
      //       content: this.$t('类别选择其它时，备注必填'),
      //       type: 'warning'
      //     })
      //     valid = false
      //   }
      // }
      // if (type == '5') {
      //   if (!claimDemageNo) {
      //     this.$toast({
      //       content: this.$t('类型选择扣款时，索赔单号必填'),
      //       type: 'warning'
      //     })
      //     valid = false
      //   }
      // }
      // if (type == '4' || type == '7') {
      //   if (!invoiceNo) {
      //     this.$toast({
      //       content: this.$t('类型选择开发票给TCL或收到TCL开具的发票,发票号必填'),
      //       type: 'warning'
      //     })
      //     valid = false
      //   }
      // }

      return valid
    },
    // 根据对账类型校验金额
    validAmount(data) {
      const { type, amount } = data
      let _message = ''

      if ((type === 3 || type === 4) && amount <= 0) {
        //保证金、开发票给TCL金额需要大于0
        _message = this.$t('往来类别为保证金或开发票给TCL时，金额需大于0')
      }
      if ((type === 5 || type === 6 || type === 7) && amount >= 0) {
        //扣款、收款、收到TCL开具的发票金额需要小于0
        _message = this.$t('往来类别为扣款、收款或收到TCL开具的发票时，金额需小于0')
      }
      return _message
    },
    // 供方往来明细 ToolBar
    supplierDetailClickToolBar(args) {
      const { grid, toolbar } = args
      const selectRows = grid.getSelectedRecords()
      const commonToolbar = [
        'SupplierDetailAdd',
        'SupplierDetailUpdate',
        'SupplierDetailImport',
        'SupplierDetailExport',
        'more-option-btn',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal',
        'SupplierDetailSave'
      ]
      if (this.isEditing && toolbar.id === 'SupplierDetailSave') {
        // 结束编辑状态
        this.$refs.supplierDetailsRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }
      if (selectRows.length === 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedIds = []
      selectRows.forEach((item) => {
        selectedIds.push(item.id)
      })

      if (toolbar.id === 'SupplierDetailAdd') {
        // 供方往来明细-新增
        // const item = {
        //   code: this.headerInfo.code,
        //   companyName: this.headerInfo.companyName,
        //   docNo: null,
        //   accountDate: new Date(),
        //   invoiceNo: null,
        //   reconciliationAccount: null,
        //   reconciliationAccountName: null,
        //   accountTypeName: null,
        //   digest: null,
        //   amount: null,
        //   currencyCode: null,
        //   thePrimaryKey: 'add' + Math.random().toString(36).substr(3, 8),
        //   addId: 'add' + Math.random().toString(36).substr(3, 8),
        //   id: 'add' + Math.random().toString(36).substr(3, 8)
        // }
        this.$refs.supplierDetailsRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id === 'SupplierDetailUpdate') {
        // 供方往来明细-更新 结束编辑
        this.$refs.supplierDetailsRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      } else if (toolbar.id === 'SupplierDetailDelete') {
        // 供方往来明细-删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            // 供方往来明细-删除
            this.handleSupplierDetailDelete({ selectRows, selectedIds })
          }
        })
      } else if (toolbar.id === 'SupplierDetailImport') {
        // 供方往来明细-导入
        // this.showSupplierDetailsUploadExcel(true)
        this.$dialog({
          modal: () => import('@/components/uploadDialog'),
          data: {
            title: this.$t('导入'),
            importApi: this.$API.deliverySchedule.supplierGoodsDemandPlanInfoImportTv,
            downloadTemplateApi:
              this.$API.reconciliationCollaboration.PuPuchaseDetailSupplierExportTv,
            paramsKey: 'excel',
            asyncParams: {
              // requestJson: JSON.stringify(parameter),
              headerId: this.$route?.query?.id
            },
            downloadTemplateParams: {
              isDownLoad: false
            }
          },
          success: () => {
            // 导入之后刷新列表
            this.$refs.supplierDetailsRef.refreshCurrentGridData()
          }
        })
      } else if (toolbar.id === 'SupplierDetailExport') {
        // 供方往来明细-导出
        this.supplierDetailExport()
      } else if (toolbar.id === 'SupplierDetailSave') {
        // 此保存只作失焦处理，不作实际保存操作
        if (!this.isEditing) {
          this.$toast({
            content: this.$t('数据未更新，无需保存'),
            type: 'warning'
          })
        }
      }
    },
    // 供方往来明细-删除
    handleSupplierDetailDelete(args) {
      const { selectedIds } = args
      // 租户级-往来对账明细供应商控制器-批量删除
      this.deleteTransactionReconciliationItemSupplierBatchDelete({
        ids: selectedIds
      })
    },
    // 租户级-往来对账明细供应商控制器-批量删除
    deleteTransactionReconciliationItemSupplierBatchDelete(params) {
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .postTransactionReconciliationHeaderSupplierDeleteTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.hasChangeSupplierDetails = true // 修改了供方往来明细，回到金额汇总tab时重新获取主单明细数据
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 Grid
            this.$refs.supplierDetailsRef.refreshCurrentGridData()
            this.updateBalance()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-往来对账明细供应商控制器-新增或修改
    postTransactionReconciliationItemSupplierSave(args) {
      const { rowData, rowIndex } = args
      const params = {
        ...rowData,
        accountDate: Number(rowData.accountDate), // 入账日期 转时间戳
        id: rowData.id || undefined, // 没有时即为新增
        thePrimaryKey: undefined,
        headerId: this.$route?.query?.id
      }
      this.apiStartLoading()
      this.getSaveApi(params.id)(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.hasChangeSupplierDetails = true // 修改了供方往来明细，回到金额汇总tab时重新获取主单明细数据
            this.$toast({ content: this.$t('操作成功'), type: 'success' })

            // 刷新 Grid
            this.$refs.supplierDetailsRef.refreshCurrentGridData()
            this.updateBalance()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.supplierDetailsRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.supplierDetailsRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    getSaveApi(id) {
      if (id) {
        return this.$API.reconciliationCollaboration
          .postTransactionReconciliationHeaderSupplierUpdateTv
      }
      return this.$API.reconciliationCollaboration.postTransactionReconciliationHeaderSupplierAddTv
    },
    // 更新期初期末余额
    updateBalance() {
      this.$API.reconciliationCollaboration
        .queryBalanceById({ id: this.headerInfo.id })
        .then((res) => {
          if (res?.code == 200) {
            this.headerInfo.supplierClosingBalance = res.data.supplierClosingBalance
          }
        })
    },
    // 供方-导出
    supplierDetailExport() {
      const params = {
        page: { current: 1, size: 10000 },
        headerId: this.headerInfo.id,
        isDownLoad: true
      } // 筛选条件
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .PuPuchaseDetailExportTv(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 往来明细 数据序列化
    serializeDetailList(list) {
      if (list.length > 0) {
        list.forEach((item, index) => {
          item.serialNumber = index + 1 // 序号
          item.thePrimaryKey = item.id // 主键
        })
      }
      return list
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped></style>
