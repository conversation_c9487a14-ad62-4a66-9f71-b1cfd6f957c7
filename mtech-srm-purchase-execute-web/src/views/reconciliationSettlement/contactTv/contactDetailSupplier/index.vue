<template>
  <!-- 往来对账单详情-供方 -->
  <div class="full-height pt20 vertical-flex-box">
    <!-- 头部信息 -->
    <top-info
      class="flex-keep"
      ref="topInfoRef"
      :header-info="headerInfo"
      :entry-type="entryType"
      @doSubmit="doSubmit"
      @goBack="goBack"
      @doExpand="doExpand"
    ></top-info>

    <mt-tabs
      class="flex-keep"
      :e-tab="false"
      :data-source="tabList"
      :selected-item="selectedItem"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <!-- 金额汇总 -->
    <div class="flex-fit summary" v-show="currentTabInfo.code === Tab.summary">
      <div class="box">
        <div class="title">
          <span>{{ $t('对账科目') }}</span>
        </div>
        <!-- <div class="tips">
          <mt-icon class="icon" name="icon_card_rank_second" />
          <span
            >{{ $t('对账总差异（元）：') }}<span v-if="statementId">{{ totalDiff }}</span
            ><span v-else>{{ '-' }}</span></span
          >
        </div> -->
        <div class="row-box">
          <!-- 采方-金额汇总 -->
          <div class="item-box">
            <div class="top">
              <div class="item">
                <div>{{ $t('编制单位') }}</div>
                <mt-input
                  v-model="headerInfo.companyName"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
              <div class="item">
                <div>{{ $t('当前余额') }}</div>
                <mt-input
                  v-model="headerInfo.buyerCurrentBalance"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
              <div class="item">
                <div>{{ $t('应收账款') }}</div>
                <mt-input
                  v-model="headerInfo.accountsReceivable"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
            </div>
            <div class="top">
              <div class="item">
                <div>{{ $t('其他应收款') }}</div>
                <mt-input
                  v-model="headerInfo.otherReceivables"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
              <div class="item">
                <div>{{ $t('预付账款') }}</div>
                <mt-input
                  v-model="headerInfo.prePayments"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
              <div class="item">
                <div>{{ $t('应付账款') }}</div>
                <mt-input
                  v-model="headerInfo.accountsPayable"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
            </div>
            <div class="top">
              <div class="item">
                <div>{{ $t('其他应付款') }}</div>
                <mt-input
                  v-model="headerInfo.otherPayAbles"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
            </div>
            <!-- <div class="table-box">
              <mt-data-grid
                :data-source="buyerSummary"
                :column-data="PurchaseSummaryColumns"
              ></mt-data-grid>
            </div> -->
          </div>
          <div class="dividing-line"></div>
          <!-- 供方-金额汇总 -->
          <div class="item-box">
            <div class="top">
              <div class="item">
                <div>{{ $t('对账单位') }}</div>
                <mt-input
                  v-model="headerInfo.supplierName"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
              <div class="item">
                <div>{{ $t('当前余额') }}</div>
                <mt-input
                  v-model="headerInfo.supplierCurrentBalance"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
              <div class="item">
                <div>{{ $t('应收账款') }}</div>
                <mt-input
                  v-model="headerInfo.supAccountsReceivable"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
            </div>
            <div class="top">
              <!-- <div class="item">
                <div><span style="color: red">*</span>{{ $t('制表') }}</div>
                <mt-input
                  v-model="headerInfo.supplierContact"
                  :disabled="true"
                  placeholder="请输入制表人"
                ></mt-input>
              </div>
              <div class="item">
                <div><span style="color: red">*</span>{{ $t('联系电话') }}</div>
                <mt-input
                  v-model="headerInfo.supplierPhone"
                  :disabled="true"
                  placeholder="请输入联系电话"
                ></mt-input>
              </div>
              <div class="item">
                <div><span style="color: red">*</span>{{ $t('email') }}</div>
                <mt-input
                  v-model="headerInfo.supplierEmail"
                  :disabled="true"
                  placeholder="请输入email"
                ></mt-input>
              </div> -->
              <div class="item">
                <div>{{ $t('应收票据') }}</div>
                <mt-input
                  v-model="headerInfo.billReceivable"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
              <div class="item">
                <div>{{ $t('其他应收款') }}</div>
                <mt-input
                  v-model="headerInfo.supOtherReceivables"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
              <div class="item">
                <div>{{ $t('预付账款') }}</div>
                <mt-input
                  v-model="headerInfo.supArePayments"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
            </div>
            <div class="top">
              <div class="item">
                <div>{{ $t('应付账款') }}</div>
                <mt-input
                  v-model="headerInfo.supAccountsPayable"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
              <div class="item">
                <div>{{ $t('其他应付款') }}</div>
                <mt-input
                  v-model="headerInfo.supOtherPayAbles"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
              <div class="item">
                <div>{{ $t('预收账款') }}</div>
                <mt-input
                  v-model="headerInfo.depositReceived"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
            </div>
            <!-- <div class="table-box">
              <mt-data-grid
                :data-source="supplierSummary"
                :column-data="SupplierSummaryColumns"
              ></mt-data-grid>
            </div> -->
          </div>
        </div>
        <div class="title">
          <span>{{ $t('差异调整项') }}</span>
        </div>
        <div class="row-box">
          <!-- 采方差异调整项 -->
          <div class="item-box">
            <div class="table-box">
              <span class="table-title">{{ $t('调增项：') }}</span>
              <div class="table-area">
                <PurchaseAdjust table-type="plus" />
              </div>
            </div>
            <div class="top">
              <div class="item">
                <div>{{ $t('加项合计') }}</div>
                <mt-input
                  v-model="headerInfo.buyerSumAdd"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
            </div>
            <div class="table-box">
              <span class="table-title">{{ $t('调减项：') }}</span>
              <div class="table-area">
                <PurchaseAdjust table-type="reduce" />
              </div>
            </div>
            <div class="top">
              <div class="item">
                <div>{{ $t('减项合计') }}</div>
                <mt-input
                  v-model="headerInfo.buyerSumSubtract"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
            </div>
            <div class="top">
              <div class="item">
                <div>{{ $t('调整后余额') }}</div>
                <mt-input
                  v-model="headerInfo.buyerAmtAfter"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
            </div>
            <!-- <div class="top">
              <div class="item">
                <div>{{ $t('制表备注') }}</div>
                <mt-input v-model="headerInfo.contact" :disabled="true" placeholder=""></mt-input>
              </div>
            </div>
            <div class="top">
              <div class="item">
                <div>{{ $t('备注') }}</div>
                <mt-input v-model="headerInfo.remark" :disabled="true" placeholder=""></mt-input>
              </div>
            </div>
            <div class="top">
              <div class="item">
                <div>{{ $t('联系电话') }}</div>
                <mt-input v-model="headerInfo.phone" :disabled="true" placeholder=""></mt-input>
              </div>
            </div>
            <div class="top">
              <div class="item">
                <div>{{ $t('mail') }}</div>
                <mt-input
                  v-model="headerInfo.supplierClosingBalance"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
            </div> -->
          </div>
          <div class="dividing-line"></div>
          <!-- 供方差异调整项 -->
          <!-- <div class="item-box" v-if="isShowSupAdj"> -->
          <div class="item-box">
            <div class="table-box">
              <span class="table-title">{{ $t('调增项：') }}</span>
              <div class="table-area">
                <SupplierAdjust
                  table-type="plus"
                  @upDateTotalDiffByResponse="upDateTotalDiffByResponse"
                />
              </div>
            </div>
            <div class="top">
              <div class="item">
                <div>{{ $t('加项合计') }}</div>
                <mt-input
                  v-model="headerInfo.supplierSumAdd"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
            </div>
            <div class="table-box">
              <span class="table-title">{{ $t('调减项：') }}</span>
              <div class="table-area">
                <SupplierAdjust
                  table-type="reduce"
                  @upDateTotalDiffByResponse="upDateTotalDiffByResponse"
                />
              </div>
            </div>
            <div class="top">
              <div class="item">
                <div>{{ $t('减项合计') }}</div>
                <mt-input
                  v-model="headerInfo.supplierSumSubtract"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
            </div>
            <div class="top">
              <div class="item">
                <div>{{ $t('调整后余额') }}</div>
                <mt-input
                  v-model="headerInfo.supplierAmtAfter"
                  :disabled="true"
                  placeholder=""
                ></mt-input>
              </div>
            </div>
            <!-- <div class="top">
              <div class="item">
                <div>{{ $t('调整差异') }}</div>
                <mt-input
                  v-model="headerInfo.buyerAmtDiff"
                  :disabled="entryType != EntryType.edit && entryType != EntryType.upload"
                  placeholder=""
                ></mt-input>
              </div>
            </div> -->
            <div class="top">
              <div class="item">
                <div><span style="color: red">*</span>{{ $t('制表') }}</div>
                <mt-input
                  v-model="headerInfo.supplierContact"
                  :disabled="entryType != EntryType.edit && entryType != EntryType.upload"
                  placeholder=""
                ></mt-input>
              </div>
            </div>
            <div class="top">
              <div class="item">
                <div>{{ $t('备注') }}</div>
                <mt-input
                  v-model="headerInfo.supRemark"
                  :disabled="entryType != EntryType.edit && entryType != EntryType.upload"
                  placeholder=""
                ></mt-input>
              </div>
            </div>
            <div class="top">
              <div class="item">
                <div><span style="color: red">*</span>{{ $t('联系电话') }}</div>
                <mt-input
                  v-model="headerInfo.supplierPhone"
                  :disabled="entryType != EntryType.edit && entryType != EntryType.upload"
                  placeholder=""
                ></mt-input>
              </div>
            </div>
            <div class="top">
              <div class="item">
                <div><span style="color: red">*</span>{{ $t('mail') }}</div>
                <mt-input
                  v-model="headerInfo.supplierEmail"
                  :disabled="entryType != EntryType.edit && entryType != EntryType.upload"
                  placeholder=""
                ></mt-input>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 采方-往来明细 -->
    <div class="flex-fit" v-show="currentTabInfo.code === Tab.purchaseDetails">
      <!-- <div style="background: white; display: flex; flex-direction: row">
        <div style="margin-left: 20px; padding-top: 20px">
          期初余额：{{ headerInfo.openingBalance }}
        </div>
        <div style="margin-left: 20px; padding-top: 20px">
          期末余额：{{ headerInfo.closingBalance }}
        </div>
      </div> -->
      <PurchaseDetail />
    </div>
    <!-- 供方-往来明细 -->
    <div class="flex-fit" v-show="currentTabInfo.code === Tab.supplierDetails">
      <!-- <div style="background: white; display: flex; flex-direction: row">
        <div style="margin-left: 20px; padding-top: 20px">
          期初余额：{{ headerInfo.supplierOpeningBalance }}
        </div>
        <div style="margin-left: 20px; padding-top: 20px">
          期末余额：{{ headerInfo.supplierClosingBalance }}
        </div>
      </div> -->
      <SupplierDetail :header-info="headerInfo" />
    </div>
    <!-- 相关附件 -->
    <div class="flex-fit" v-show="currentTabInfo.code === Tab.file">
      <relative-file
        ref="relativeFileRef"
        :doc-id="relativeFileData.docId"
        :request-url-obj="relativeFileData.requestUrlObj"
        :module-file-list="moduleFileList"
        :is-view="relativeFileData.isView"
      ></relative-file>
    </div>
    <!-- 操作日志 -->
    <!-- <div class="flex-fit" v-show="currentTabInfo.code === Tab.log">
      <mt-template-page
        ref="logRef"
        class="template-height"
        :template-config="logComponentConfig"
      />
    </div> -->
    <!-- 供方-往来明细导入弹框 -->
    <!-- <upload-excel-dialog
      ref="supplierDetailsUploadExcelRef"
      :down-template-params="supplierDetailsDownTemplateParams"
      :upload-params="supplierDetailsUploadParams"
      :request-urls="supplierDetailsRequestUrls"
      @closeUploadExcel="showSupplierDetailsUploadExcel(false)"
      @upExcelConfirm="supplierDetailsUpExcelConfirm"
    ></upload-excel-dialog> -->
  </div>
</template>

<script>
import {
  Tab,
  TabList,
  EntryType,
  PurchaseSummaryColumns,
  SupplierSummaryColumns
} from './config/constant'
import bigDecimal from 'js-big-decimal'
import { BASE_TENANT } from '@/utils/constant'

export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue'),
    PurchaseAdjust: () => import('./components/purchaseAdjust.vue'),
    SupplierAdjust: () => import('./components/supplierAdjust.vue'),
    PurchaseDetail: () => import('./components/purchaseDetail.vue'),
    SupplierDetail: () => import('./components/supplierDetail.vue'),
    RelativeFile: () => import('@/components/businessComponents/relativeFile/index.vue')
    // UploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    const lastTabIndex = JSON.parse(localStorage.getItem('lastTabIndex')) ?? 0
    const entryType = this.$route?.query?.type // 从URL获取页面状态
    const statementId = this.$route?.query?.id // 从URL获取对账单id

    return {
      EntryType,
      Tab,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      headerInfo: {}, // 主单信息
      entryType: entryType || EntryType.view, // 页面状态，默认：查看
      selectedItem: entryType === 3 ? 3 : 0,
      statementId, // 对账单id
      totalDiff: 0, // 对账总差异
      lastTabIndex, // 上一页面的 tabIndex，返回时传递参数
      currentTabInfo: entryType === 3 ? TabList[3] : TabList[0], // 当前tab的数据，默认选中第一项
      tabList: TabList,
      buyerSummary: [], // 采方科目类别汇总
      supplierSummary: [], // 供方科目类别汇总
      hasChangeSupplierDetails: false, // 修改了供方往来明细，回到金额汇总tab时重新获取主单明细数据
      moduleFileList: [
        // {
        //   id: "trans_reconciliation_header_buyer", // 选中时传给api的 doctype 的值
        //   nodeName: this.$t("采方-整单附件"), // 侧边栏名称
        //   nodeCode: 0, // 即当前 index 用于选择左侧边栏时 获取当前项 配置
        //   btnRequired: {
        //     hasUpload: false,
        //     hasDownload: true,
        //     hasDelete: false,
        //   }, // 表格 toolbar 配置（可选） hasUpload: Boolean、hasDownload: Boolean、 hasDelete: Boolean
        //   hasItem: false, // 是否显示附件行数据（可选）
        // },
        {
          id: 'trans_reconciliation_header_supplier', // 选中时传给api的 doctype 的值
          nodeName: this.$t('供方-整单附件'), // 侧边栏名称
          nodeCode: 0, // 即当前 index 用于选择左侧边栏时 获取当前项 配置
          btnRequired: {
            hasUpload: true,
            hasDownload: true,
            hasDelete: true,
            hasPrint: false
          }, // 表格 toolbar 配置（可选） hasUpload: Boolean、hasDownload: Boolean、 hasDelete: Boolean
          hasItem: false, // 是否显示附件行数据（可选）
          deleteFileUrl: `${BASE_TENANT}/transaction_reconciliation/files/deleteSupplierFile`, // 删除文件使用的 API 根据ID删除
          deleteFileRequestMethod: 'delete' // 设置删除api的请求方法
        }
      ], // 相关附件 左侧菜单
      // 相关附件配置
      relativeFileData: {
        docId: statementId,
        requestUrlObj: {
          preUrl: 'reconciliationSettlement',
          saveUrl: 'postTransactionReconciliationFilesSupplierSave', // 将附件文件url保存到列表 保存文件信息
          fileUrl: `${BASE_TENANT}/transaction_reconciliation/files/queryList` // 获取附件列表的url 根据docType和docID查询所有文件信息
        },
        isView: entryType != EntryType.edit // 查看的状态 || 待反馈状态，没有上传和删除
      },
      PurchaseSummaryColumns, // 金额汇总-采方对账科目 表格
      SupplierSummaryColumns, // 金额汇总-供方对账科目 表格
      supplierDetailsDownTemplateParams: {
        pageFlag: false,
        belong: 1, // 数据归属，0-采方,1-供方
        headerId: statementId,
        page: {}
      }, // 通知配置导入下载模板参数
      supplierDetailsUploadParams: {
        headerId: statementId
      },
      companyCode: '',
      companyCodeList: []
    }
  },
  computed: {
    isShowSupAdj() {
      return !this.companyCode.includes(this.companyCodeList)
    }
  },
  mounted() {
    // 获取对账单详情数据
    this.getDetailsData()
  },
  beforeDestroy() {
    localStorage.removeItem('lastTabIndex')
  },
  methods: {
    // 获取对账单详情数据
    getDetailsData() {
      if (this.statementId) {
        // 如果URL存在对账单ID
        this.apiStartLoading()
        this.$API.reconciliationCollaboration
          .postTransactionContactDetailSupplierApi({
            id: this.statementId
          })
          .then((res) => {
            this.apiEndLoading()
            this.hasChangeSupplierDetails = false
            const data = res?.data || {}
            this.headerInfo = data
            this.headerInfo.supplierOpeningBalance = 0 - data.openingBalance
            // 截止时间 格式转换
            let deadLine = null
            if (data.deadLine) {
              deadLine = new Date(Number(data.deadLine))
            }
            this.headerInfo.deadLine = deadLine // 截止时间
            // 采方金额汇总
            this.buyerSummary = data.buyerSummary || []
            // 供方金额汇总
            this.supplierSummary = data.supplierSummary || []
            // 计算对账总差异
            const closingBalance = this.headerInfo.closingBalance || 0 // 采方期末金额
            const supplierClosingBalance = this.headerInfo.supplierClosingBalance || 0 // 供方期末金额
            // 对账总差异 = 采方期末金额 + 供方期末金额
            this.totalDiff = bigDecimal.add(closingBalance, supplierClosingBalance)
            this.companyCode = data.companyCode
            // this.getAdjDisplay(data.companyCode)
          })
          .catch(() => {
            this.apiEndLoading()
          })
      }
    },
    upDateTotalDiffByResponse() {
      if (this.statementId) {
        // 如果URL存在对账单ID
        this.$API.reconciliationCollaboration
          .postTransactionContactDetailSupplierApi({
            id: this.statementId
          })
          .then((res) => {
            this.hasChangeSupplierDetails = false
            const data = res?.data || {}
            this.headerInfo = data
            this.headerInfo.supplierOpeningBalance = 0 - data.openingBalance
            // 截止时间 格式转换
            let deadLine = null
            if (data.deadLine) {
              deadLine = new Date(Number(data.deadLine))
            }
            this.headerInfo.deadLine = deadLine // 截止时间
            // 采方金额汇总
            this.buyerSummary = data.buyerSummary || []
            // 供方金额汇总
            this.supplierSummary = data.supplierSummary || []
            // 计算对账总差异
            const closingBalance = this.headerInfo.closingBalance || 0 // 采方期末金额
            const supplierClosingBalance = this.headerInfo.supplierClosingBalance || 0 // 供方期末金额
            // 对账总差异 = 采方期末金额 + 供方期末金额
            this.totalDiff = bigDecimal.add(closingBalance, supplierClosingBalance)
            this.companyCode = data.companyCode
            // this.getAdjDisplay(data.companyCode)
          })
          .catch(() => {})
      }
    },
    // 提交反馈
    doSubmit() {
      if (!this.headerInfo.supplierContact) {
        this.$toast({ content: this.$t('请填写制表人'), type: 'warning' })
        return
      }
      if (!this.headerInfo.supplierPhone) {
        this.$toast({ content: this.$t('请填写电话'), type: 'warning' })
        return
      }
      if (!this.headerInfo.supplierEmail) {
        this.$toast({ content: this.$t('请填写email'), type: 'warning' })
        return
      }
      const params = {
        supplierContact: this.headerInfo.supplierContact,
        supplierPhone: this.headerInfo.supplierPhone,
        supplierEmail: this.headerInfo.supplierEmail,
        id: this.statementId, // 对账单Id
        supRemark: this.headerInfo.supRemark // 供方备注
      }
      // 租户级-往来对账单供应商控制器-提交反馈
      this.postTransactionReconciliationHeaderSupplierFeedback(params)
    },
    // 租户级-往来对账单供应商控制器-提交反馈
    postTransactionReconciliationHeaderSupplierFeedback(params) {
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .postTransactionReconciliationHeaderSupplierFeedbackTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 返回
            this.goBack()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 返回
    goBack() {
      //  // 将 tabIndex 放到 localStorage 往来对账单列表-供方 读
      //  localStorage.setItem("tabIndex", JSON.stringify(this.lastTabIndex));
      //  // 返回 往来对账单列表-供方
      //  if (this.$route.query.come) {
      //    this.$router.push({
      //      name: "contact-reconciled-supplier-query",
      //      query: {},
      //    });
      //  } else {
      //    this.$router.push({
      //      name: "contact-reconciled-supplier",
      //      query: {},
      //    });
      //  }
      this.$router.go(-1)
    },
    // 点击 tab
    handleSelectTab(e) {
      this.currentTabInfo = this.tabList[e]
      if (this.currentTabInfo.code === Tab.summary && this.hasChangeSupplierDetails) {
        // 修改了供方往来明细，回到金额汇总tab时重新获取主单明细数据
        this.getDetailsData()
      }
    },
    doExpand() {},
    // 获取字典项
    // getAdjDisplay(customerCode) {
    //   this.$API.masterData
    //     .getAuthFindDictItemByCustomerCodeAndDictCode({
    //       customerCode,
    //       dictCode: 'ACCT_CELAR_ADJ_UNDISPLAY'
    //     })
    //     .then((res) => {
    //       this.companyCodeList = res.data.map((item) => item.itemCode) || []
    //     })
    // },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.summary {
  background-color: var(--plugin-dg-bg-ff);
  height: 100%;
  overflow: auto;
  .box {
    flex-direction: column;
    .title {
      margin: 24px 0px 24px 24px;
      padding-left: 8px;
      border-left: 4px solid #3369ac;
      font-weight: 600;
    }
    .tips {
      margin: 24px 0px 24px 60px;
      .icon {
        padding-right: 8px;
        color: #6487bf;
      }
    }
    .row-box {
      flex: 1 1 auto;
      display: flex;
      position: relative;
      .item-box {
        width: calc(50% - 1px); // 两项 共计 2px 预留给中间的虚线
        &.empty {
          display: flex;
          justify-content: center;
          flex-direction: column;
          min-height: 200px;
          align-items: center;
          color: #a6a6a6;
        }

        .top {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          margin: 12px 32px 24px 32px;
          .item {
            display: flex;
            align-items: center;
            width: 100%;
            div {
              width: 16%;
              padding-right: 5px;
              box-sizing: border-box;
              text-align: right;
            }
            .mt-input {
              flex: 1;
            }
          }
        }
        .table-box {
          display: flex;
          align-items: center;
          margin: 12px 32px 0px 32px;
          width: 77%;
          .table-title {
            white-space: nowrap;
          }
          .table-area {
            flex: 1;
            width: 100%;
          }
        }
      }
      .dividing-line {
        border-left: 2px dashed #d9d9d9;
      }
    }
  }
}
</style>
