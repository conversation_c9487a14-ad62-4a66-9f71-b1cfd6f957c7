import {
  ContactsTypeOptions,
  CleanStatusOptions,
  AdjustTypeOptions,
  AdjustTypePlusOptions,
  AdjustTypeReduceOptions,
  accountTypeNameOptions
} from './constant'
import { ColumnComponent as Component } from './columnComponent'

// 格式化 对账明细 表格动态数据
export const formatDetailsTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150'
    }
    if (col.fieldCode === 'thePrimaryKey') {
      // 行主键
      defaultCol.visible = false
      defaultCol.isIdentity = true // 加上这个，新增一行的时候，主键不会变成可输入
      defaultCol.isPrimaryKey = true
      defaultCol.allowEditing = false
    } else if (col.fieldCode === 'checkBox') {
      // checkBox
      defaultCol.type = 'checkbox'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.showInColumnChooser = false
      defaultCol.width = '50'
      // 编辑时显示
      defaultCol.editTemplate = Component.empty
    } else if (col.fieldCode === 'serialNumber') {
      // 序号
      defaultCol.width = '80'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.ignore = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'type') {
      // 往来类别
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode,
        valueConverter: {
          type: 'map',
          map: ContactsTypeOptions
        }
      })
      defaultCol.editTemplate = Component.select({
        dataKey: col.fieldCode,
        fields: { text: 'text', value: 'value' },
        selectOptions: ContactsTypeOptions,
        allowFiltering: false,
        showClearBtn: true
      })
    } else if (col.fieldCode === 'docNo') {
      // 单据号
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 50
      })
    } else if (col.fieldCode === 'accountTypeName') {
      // 科目类别
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.select({
        dataKey: col.fieldCode,
        fields: { text: 'text', value: 'text' },
        selectOptions: accountTypeNameOptions,
        allowFiltering: false,
        showClearBtn: true
      })
    } else if (col.fieldCode === 'accountTypeCode') {
      // 科目类别编码
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 50
      })
    } else if (col.fieldCode === 'reconciliationAccountName') {
      // 科目
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 50
      })
    } else if (col.fieldCode === 'reconciliationAccount') {
      // 科目编号
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 50
      })
    } else if (col.fieldCode === 'accountDate') {
      // 入账日期
      defaultCol.template = Component.timeDate({
        dataKey: col.fieldCode,
        hasTime: false
      })
      defaultCol.editTemplate = Component.timeInput({
        dataKey: col.fieldCode,
        disabled: false,
        showClearBtn: true,
        allowEdit: false,
        isDate: true,
        maxDate: new Date()
      })
    } else if (col.fieldCode === 'invoiceNo') {
      // 发票号
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 50
      })
    } else if (col.fieldCode === 'amount') {
      // 金额
      defaultCol.width = '200'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.number({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxValue: 999999999999999.99,
        minValue: -999999999999999.99,
        precision: 2
      })
    } else if (col.fieldCode === 'currencyName') {
      // 币种
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 50
      })
    } else if (col.fieldCode === 'currencyCode') {
      // 币种编码
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.currencyCodeSelect
      // defaultCol.editTemplate = Component.input({
      //   dataKey: col.fieldCode,
      //   showClearBtn: true,
      //   maxlength: 50
      // })
    } else if (col.fieldCode === 'claimDemageNo') {
      // 索赔单号
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 50
      })
    } else if (col.fieldCode === 'remark') {
      // 备注
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 200
      })
    } else if (col.fieldCode === 'cleanStatus') {
      // 清账状态
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode,
        valueConverter: {
          type: 'map',
          map: CleanStatusOptions
        }
      })
      defaultCol.editTemplate = Component.select({
        dataKey: col.fieldCode,
        fields: { text: 'text', value: 'value' },
        selectOptions: CleanStatusOptions,
        allowFiltering: false,
        showClearBtn: true
      })
    }
    colData.push(defaultCol)
  })

  return colData
}

// 格式化 操作日志 表格动态数据
export const formatLogTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150'
    }
    if (col.fieldCode === 'createTime') {
      // 创建时间
      defaultCol.template = Component.timeDate({
        dataKey: col.fieldCode,
        hasTime: true
      })
    }
    colData.push(defaultCol)
  })

  return colData
}

// 格式化 差异调整项 表格动态数据
export const formatAdjustTableColumnData = (args) => {
  const { data, tableType } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: '150'
    }
    if (col.fieldCode === 'thePrimaryKey') {
      // 行主键
      defaultCol.visible = false
      defaultCol.isIdentity = true // 加上这个，新增一行的时候，主键不会变成可输入
      defaultCol.isPrimaryKey = true
      defaultCol.allowEditing = false
    } else if (col.fieldCode === 'checkBox') {
      // checkBox
      defaultCol.type = 'checkbox'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.showInColumnChooser = false
      defaultCol.width = '50'
      // 编辑时显示
      defaultCol.editTemplate = Component.empty
    } else if (col.fieldCode === 'serialNumber') {
      // 序号
      defaultCol.width = '80'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.ignore = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'adjustType') {
      // 差异调整型类型
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode,
        valueConverter: {
          type: 'map',
          map: AdjustTypeOptions
        }
      })
      defaultCol.editTemplate = Component.select({
        dataKey: col.fieldCode,
        fields: { text: 'text', value: 'value' },
        selectOptions: tableType === 'plus' ? AdjustTypePlusOptions : AdjustTypeReduceOptions,
        allowFiltering: false,
        showClearBtn: true
      })
    } else if (col.fieldCode === 'adjustDescription') {
      // 调整项摘要
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 100
      })
    } else if (col.fieldCode === 'amount') {
      // 调整项金额
      defaultCol.width = '200'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.number({
        dataKey: col.fieldCode,
        showClearBtn: true,
        precision: 2,
        maxValue: 999999999999999.99,
        minValue: -999999999999999.99
      })
    } else if (col.fieldCode === 'remark') {
      // 备注
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.input({
        dataKey: col.fieldCode,
        showClearBtn: true,
        maxlength: 200
      })
    }
    colData.push(defaultCol)
  })

  return colData
}
