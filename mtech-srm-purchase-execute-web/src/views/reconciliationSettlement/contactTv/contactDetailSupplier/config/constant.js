import { i18n } from '@/main.js'

// tab
export const Tab = {
  summary: 0, // 金额汇总
  purchaseDetails: 1, // 采方-往来明细
  supplierDetails: 2, // 供方-往来明细
  file: 3 // 相关附件
  // log: 4 // 操作日志
}

export const TabList = [
  {
    title: i18n.t('金额汇总'),
    code: Tab.summary
  },
  {
    title: i18n.t('采方-往来明细'),
    code: Tab.purchaseDetails
  },
  {
    title: i18n.t('供方-往来明细'),
    code: Tab.supplierDetails
  },
  {
    title: i18n.t('相关附件'),
    code: Tab.file
  }
  // {
  //   title: i18n.t("操作日志"),
  //   code: Tab.log,
  // },
]

// 表格编辑 的 RequestType
export const RequestType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  sorting: 'sorting',
  refresh: 'refresh'
}

// 表格编辑 的 action
export const ActionType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  edit: 'edit'
}

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 行编辑触发的 change 事件类型
export const ComponentChangeType = {
  code: 'code', // 例：下拉框选择“供应商”，供应商编码（code)要被修改
  link: 'link' // 例：计划组：根据物料+工厂+计划组的业务组类型 code 带出
}

// 表格 EditSettings
export const EditSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
} // 编辑设置

// 供方差异调整项 新增行固定数据
export const SupplierAdjustNewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  adjustDescription: null, //	调整项摘要
  adjustType: null, // 调整项类型
  amount: null, // 调整金额
  headerId: null, // 对账单ID
  id: null, // id
  remark: null // 备注
}

export const SupplierDetailNewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  id: null, // id
  accountDate: null, // 入账日期
  amount: null, // 金额
  cleanStatus: null, // 清账状态 0未清 1已清
  docNo: null, // 单据号
  headerId: null, // 往来对账单id
  invoiceNo: null, // 发票号
  reconciliationAccount: null, // 对账科目
  reconciliationAccountName: null, // 对账科目名称
  remark: null, // 备注
  type: null, // 往来类别 0-对账；1-索赔；2-返利
  typeName: null, // 往来类别名称
  currencyCode: null // 币种
}

// 页面显示类型
export const EntryType = {
  view: 1, // 查看
  edit: 2, // 继续发布时就行编辑
  upload: 3 // 上传附件
}

// 状态 1-待反馈；2-已提交；3-已确认
export const ReconciledStatus = {
  pending: 1, // 待反馈
  submited: 2, // 已提交
  confirmed: 3 // 已确认
}

// 往来类别 text
export const ReconciledStatusText = {
  [ReconciledStatus.pending]: i18n.t('待反馈'),
  [ReconciledStatus.submited]: i18n.t('已提交'),
  [ReconciledStatus.confirmed]: i18n.t('已确认')
}

// 往来类别 对应的 css class
export const ReconciledStatusCssClass = {
  [ReconciledStatus.pending]: 'col-active',
  [ReconciledStatus.submited]: 'col-active',
  [ReconciledStatus.confirmed]: 'col-inactive'
}

// 往来类别 对应的 Options
export const ReconciledStatusOptions = [
  {
    // 待反馈
    value: ReconciledStatus.pending,
    text: ReconciledStatusText[ReconciledStatus.pending],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.pending]
  },
  {
    // 已提交
    value: ReconciledStatus.submited,
    text: ReconciledStatusText[ReconciledStatus.submited],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.submited]
  },
  {
    // 采购已确认
    value: ReconciledStatus.confirmed,
    text: ReconciledStatusText[ReconciledStatus.confirmed],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.confirmed]
  }
]

// 往来类别 0-对账；1-索赔；2-返利
// export const ContactsType = {
//   recon: 0, // 对账
//   claim: 1, // 索赔
//   rebate: 2, // 返利
//   other: 9, // 其他
// };

// 往来类别
export const ContactsType = {
  bzj: 3, // 保证金
  xsfp: 4, // 开发票给TCl
  kk: 5, // 扣款
  sk: 6, // 收款
  kfp: 7, // 收到TCL开具的发票
  other: 9 // 其他
}

// 往来类别 text
export const ContactsTypeText = {
  [ContactsType.bzj]: i18n.t('保证金'),
  [ContactsType.xsfp]: i18n.t('开发票给TCL'),
  [ContactsType.kk]: i18n.t('扣款'),
  [ContactsType.sk]: i18n.t('收款'),
  [ContactsType.kfp]: i18n.t('收到TCL开具的发票'),
  [ContactsType.other]: i18n.t('其他')
}

// 往来类别 对应的 css class
export const ContactsTypeCssClass = {
  [ContactsType.bzj]: '',
  [ContactsType.xsfp]: '',
  [ContactsType.kk]: '',
  [ContactsType.sk]: '',
  [ContactsType.kfp]: '',
  [ContactsType.other]: ''
}

// 往来类别 对应的 Options
export const ContactsTypeOptions = [
  {
    // 保证金
    value: ContactsType.bzj,
    text: ContactsTypeText[ContactsType.bzj],
    cssClass: ContactsTypeCssClass[ContactsType.bzj]
  },
  {
    // 开发票给TCL
    value: ContactsType.xsfp,
    text: ContactsTypeText[ContactsType.xsfp],
    cssClass: ContactsTypeCssClass[ContactsType.xsfp]
  },
  {
    // 扣款
    value: ContactsType.kk,
    text: ContactsTypeText[ContactsType.kk],
    cssClass: ContactsTypeCssClass[ContactsType.kk]
  },
  {
    // 收款
    value: ContactsType.sk,
    text: ContactsTypeText[ContactsType.sk],
    cssClass: ContactsTypeCssClass[ContactsType.sk]
  },
  {
    // 收到TCL开具的发票
    value: ContactsType.kfp,
    text: ContactsTypeText[ContactsType.kfp],
    cssClass: ContactsTypeCssClass[ContactsType.kfp]
  },
  {
    // 其他
    value: ContactsType.other,
    text: ContactsTypeText[ContactsType.other],
    cssClass: ContactsTypeCssClass[ContactsType.other]
  }
]

// 清账状态 0未清 1已清
export const CleanStatus = {
  notYet: '0', // 未清
  already: '1' // 已清
}

// 清账状态 text
export const CleanStatusText = {
  [CleanStatus.notYet]: i18n.t('未清'),
  [CleanStatus.already]: i18n.t('已清')
}

// 清账状态 对应的 css class
export const CleanStatusCssClass = {
  [CleanStatus.notYet]: '',
  [CleanStatus.already]: ''
}

// 清账状态 对应的 Options
export const CleanStatusOptions = [
  {
    // 未清
    value: CleanStatus.notYet,
    text: CleanStatusText[CleanStatus.notYet],
    cssClass: CleanStatusCssClass[CleanStatus.notYet]
  },
  {
    // 已清
    value: CleanStatus.already,
    text: CleanStatusText[CleanStatus.already],
    cssClass: CleanStatusCssClass[CleanStatus.already]
  }
]

// 清账状态 对应的 Options
export const accountTypeNameOptions = [
  {
    value: 0,
    text: i18n.t('应收账款')
  },
  {
    value: 1,
    text: i18n.t('应收票据')
  },
  {
    value: 2,
    text: i18n.t('其他应收款')
  },
  {
    value: 3,
    text: i18n.t('预收账款')
  },
  {
    value: 4,
    text: i18n.t('应付账款')
  },
  {
    value: 5,
    text: i18n.t('其他应付款')
  },
  {
    value: 6,
    text: i18n.t('预付账款')
  }
]

// 差异调整型类型
export const AdjustType = {
  plus: 1, // 新增项
  minus: -1 // 新减项
}

// 差异调整型类型 text
export const AdjustTypeText = {
  [AdjustType.plus]: i18n.t('调增项'),
  [AdjustType.minus]: i18n.t('调减项')
}

// 差异调整型类型 对应的 css class
export const AdjustTypeCssClass = {
  [AdjustType.plus]: '',
  [AdjustType.minus]: ''
}

// 差异调整型类型 对应的 Options
export const AdjustTypeOptions = [
  {
    // 新增项
    value: AdjustType.plus,
    text: AdjustTypeText[AdjustType.plus],
    cssClass: AdjustTypeCssClass[AdjustType.plus]
  },
  {
    // 新减项
    value: AdjustType.minus,
    text: AdjustTypeText[AdjustType.minus],
    cssClass: AdjustTypeCssClass[AdjustType.minus]
  }
]

// 差异调整型类型 对应的 Options
export const AdjustTypePlusOptions = [
  {
    // 新增项
    value: AdjustType.plus,
    text: AdjustTypeText[AdjustType.plus],
    cssClass: AdjustTypeCssClass[AdjustType.plus]
  }
  // {
  //   // 新减项
  //   value: AdjustType.minus,
  //   text: AdjustTypeText[AdjustType.minus],
  //   cssClass: AdjustTypeCssClass[AdjustType.minus]
  // }
]

// 差异调整型类型 对应的 Options
export const AdjustTypeReduceOptions = [
  // {
  //   // 新增项
  //   value: AdjustType.plus,
  //   text: AdjustTypeText[AdjustType.plus],
  //   cssClass: AdjustTypeCssClass[AdjustType.plus]
  // },
  {
    // 新减项
    value: AdjustType.minus,
    text: AdjustTypeText[AdjustType.minus],
    cssClass: AdjustTypeCssClass[AdjustType.minus]
  }
]

// 金额汇总-采方对账科目
export const PurchaseSummaryColumns = [
  {
    field: 'accountTypeName',
    headerText: i18n.t('采方科目名称')
  },
  {
    field: 'amount',
    headerText: i18n.t('采方科目金额')
  }
]

// 金额汇总-供方对账科目
export const SupplierSummaryColumns = [
  {
    field: 'accountTypeName',
    headerText: i18n.t('供方科目名称')
  },
  {
    field: 'amount',
    headerText: i18n.t('供方科目金额')
  }
]

// 往来明细 表格列数据 - 采方
export const DetailsColumnData = [
  {
    fieldCode: 'checkBox' // 不可编辑
  },
  {
    fieldCode: 'serialNumber', // 前端定义,不可编辑
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'thePrimaryKey'
  },
  {
    fieldCode: 'code', // 前端定义,不可编辑
    fieldName: i18n.t('对账单号')
  },
  {
    fieldCode: 'companyName', // 前端定义,不可编辑
    fieldName: i18n.t('编制单位名称')
  },
  // {
  //   fieldCode: 'type', // 往来类别 往来类别 0-对账；1-索赔；2-返利
  //   fieldName: i18n.t('往来类别')
  // },
  {
    fieldCode: 'docNo', // 单据号
    fieldName: i18n.t('入账凭证编号')
  },
  {
    fieldCode: 'accountDate', // 入账日期
    fieldName: i18n.t('入账日期')
  },
  {
    fieldCode: 'reconciliationAccount', // 科目编号 对账科目
    fieldName: i18n.t('会计科目代码')
  },
  {
    fieldCode: 'reconciliationAccountName', // 科目 对账科目名称
    fieldName: i18n.t('会计科目名称')
  },
  {
    fieldCode: 'digest', // 摘要
    fieldName: i18n.t('摘要')
  },
  {
    fieldCode: 'amount', // 金额
    fieldName: i18n.t('金额')
  },
  {
    fieldCode: 'currencyCode', // 币种
    fieldName: i18n.t('币种')
  },
  {
    fieldCode: 'cleanStatus', // 清账状态 0未清 1已清
    fieldName: i18n.t('清账状态')
  }
  // {
  //   fieldCode: "accountTypeName", // 科目类别
  //   fieldName: i18n.t("科目类别"),
  // },
  // {
  //   fieldCode: "accountTypeCode", // 科目类别编码
  //   fieldName: i18n.t("科目类别编码"),
  // },
  // {
  //   fieldCode: "reconciliationAccountName", // 科目 对账科目名称
  //   fieldName: i18n.t("科目"),
  // },
  // {
  //   fieldCode: "reconciliationAccount", // 科目编号 对账科目
  //   fieldName: i18n.t("科目编号"),
  // },
  // {
  //   fieldCode: 'invoiceNo', // 发票号
  //   fieldName: i18n.t('发票号')
  // },
  // {
  //   fieldCode: 'currencyCode', // 币种编码
  //   fieldName: i18n.t('币种编码')
  // },
  // {
  //   fieldCode: 'lineNo', // 行号
  //   fieldName: i18n.t('行号')
  // },
  // {
  //   fieldCode: 'digest1', // 摘要1
  //   fieldName: i18n.t('摘要1')
  // },
  // {
  //   fieldCode: 'digest2', // 摘要2
  //   fieldName: i18n.t('摘要2')
  // }
  // {
  //   fieldCode: "cleanStatus", // 清账状态 0未清 1已清
  //   fieldName: i18n.t("清账状态"),
  // },
]

export const SupplierDetailsColumnData = [
  {
    fieldCode: 'checkBox' // 不可编辑
  },
  {
    fieldCode: 'serialNumber', // 前端定义,不可编辑
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'thePrimaryKey'
  },
  {
    fieldCode: 'code', // 前端定义,不可编辑
    fieldName: i18n.t('对账单号'),
    allowEditing: false
  },
  {
    fieldCode: 'supplierName', // 前端定义,不可编辑
    fieldName: i18n.t('对账单位名称'),
    allowEditing: false
  },
  // {
  //   fieldCode: 'type', // 往来类别 往来类别 0-对账；1-索赔；2-返利
  //   fieldName: i18n.t('往来类别')
  // },
  {
    fieldCode: 'docNo', // 单据号
    fieldName: i18n.t('入账凭证编号')
  },
  {
    fieldCode: 'accountDate', // 入账日期
    fieldName: i18n.t('入账日期')
  },
  {
    fieldCode: 'invoiceNo', // 发票号
    fieldName: i18n.t('入账发票号')
  },
  {
    fieldCode: 'reconciliationAccount', // 科目编号 对账科目
    fieldName: i18n.t('会计科目代码')
  },
  {
    fieldCode: 'reconciliationAccountName', // 科目 对账科目名称
    fieldName: i18n.t('会计科目名称')
  },
  {
    fieldCode: 'accountTypeName', // 科目类别
    fieldName: i18n.t('科目类别')
  },
  // {
  //   fieldCode: 'accountTypeCode', // 科目类别编码
  //   fieldName: i18n.t('科目类别编码')
  // },
  {
    fieldCode: 'digest', // 摘要
    fieldName: i18n.t('摘要')
  },
  {
    fieldCode: 'amount', // 金额
    fieldName: i18n.t('金额')
  },
  {
    fieldCode: 'currencyCode', // 币种
    fieldName: i18n.t('币种')
  }
]
// 操作日志 表格列数据
export const LogColumnData = [
  {
    fieldCode: 'operTypeName',
    fieldName: i18n.t('操作类型')
  },
  {
    fieldCode: 'operDescription',
    fieldName: i18n.t('操作内容') // 操作描述 操作内容
  },
  {
    fieldCode: 'createUserName',
    fieldName: i18n.t('操作人')
  },
  {
    fieldCode: 'createTime',
    fieldName: i18n.t('操作时间')
  }
]

// 差异调整项 表格列数据
export const AdjustColumnData = [
  {
    fieldCode: 'checkBox' // 不可编辑
  },
  {
    fieldCode: 'serialNumber', // 前端定义,不可编辑
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'thePrimaryKey'
  },
  {
    fieldCode: 'adjustType', // 新增项 1，新减项 -1
    fieldName: i18n.t('类型')
  },
  {
    fieldCode: 'adjustDescription',
    fieldName: i18n.t('调整项摘要')
  },
  {
    fieldCode: 'amount',
    fieldName: i18n.t('调整项金额')
  },
  {
    fieldCode: 'remark',
    fieldName: i18n.t('备注')
  }
]
