<template>
  <div class="vertical-flex-box">
    <collapse-search :is-grid-display="true" @reset="handleReset" @search="handleSearch">
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item prop="docNo" :label="$t('入账凭证编号')">
          <mt-input
            v-model="searchFormModel.docNo"
            :show-clear-button="true"
            :placeholder="$t('请输入入账凭证编号')"
          />
        </mt-form-item>
        <mt-form-item prop="invoiceNo" :label="$t('入账发票号')">
          <mt-input
            v-model="searchFormModel.invoiceNo"
            :show-clear-button="true"
            :placeholder="$t('请输入入账发票号')"
          />
        </mt-form-item>
        <mt-form-item prop="reconciliationAccount" :label="$t('会计科目代码')">
          <mt-input
            v-model="searchFormModel.reconciliationAccount"
            :show-clear-button="true"
            :placeholder="$t('请输入会计科目代码')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <div class="flex-fit">
      <sc-table
        ref="supplierDetailSctableRef"
        grid-id="c45a6028-749b-47f5-b80e-79612f34602c"
        :loading="loading"
        :is-show-refresh-bth="true"
        :columns="columns"
        :table-data="tableData"
        keep-source
        :edit-config="{
          enabled: canEdit,
          trigger: 'click',
          mode: 'row',
          showStatus: true
        }"
        :edit-rules="validRules"
        @edit-actived="editBegin"
        @edit-closed="editComplete"
        @refresh="handleSearch"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :loading="item.loading"
            size="small"
            @click="handleClickToolBar(item)"
          >
            {{ item.name }}
          </vxe-button>
        </template>
        <template #docNoEdit="{ row }">
          <vxe-input v-model.trim="row.docNo" :placeholder="$t('请输入')" />
        </template>
        <template #accountDateEdit="{ row }">
          <vxe-input
            v-model="row.accountDate"
            type="datetime"
            transfer
            :placeholder="$t('请选择')"
          />
        </template>
        <template #invoiceNoEdit="{ row }">
          <vxe-input v-model="row.invoiceNo" :placeholder="$t('请输入')" />
        </template>
        <template #accountTypeCodeEdit="{ row }">
          <vxe-select
            v-model="row.accountTypeCode"
            placeholder="请选择"
            :options="accountTypeOptions"
            transfer
            filterable
            @change="({ value }) => accountTypeChange(row, value)"
          />
        </template>
        <template #reconciliationAccountEdit="{ row }">
          <vxe-select
            v-model="row.reconciliationAccount"
            placeholder="请选择"
            :options="reconciliationAccountOptions"
            transfer
            filterable
            @change="({ value }) => reconciliationAccountChange(row, value)"
          />
        </template>
        <template #reconciliationAccountNameEdit="{ row }">
          <vxe-input v-model="row.reconciliationAccountName" disabled />
        </template>
        <template #digestEdit="{ row }">
          <vxe-input v-model="row.digest" :placeholder="$t('请输入')" />
        </template>
        <template #amountEdit="{ row }">
          <vxe-input
            v-model="row.amount"
            type="number"
            min="-************.99"
            max="************.99"
            :placeholder="$t('请输入')"
          />
        </template>
        <template #currencyCodeEdit="{ row }">
          <vxe-select
            v-model="row.currencyCode"
            placeholder="请选择"
            :options="currencyOptions"
            transfer
            filterable
            @change="({ value }) => currencyCodeChange(row, value)"
          />
        </template>
      </sc-table>
    </div>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { accountTypeOptions, columnData } from '../config/supplierDetail'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  name: 'SupplierDetail',
  components: { CollapseSearch, ScTable },
  props: {
    canEdit: {
      type: Boolean,
      default: false
    },
    isTv: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      loading: false,
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      tableData: [],
      validRules: {
        accountDate: [{ required: true, message: this.$t('请选择入账日期') }],
        accountTypeCode: [{ required: true, message: this.$t('请选择科目类别') }],
        amount: [{ required: true, message: this.$t('请填写金额') }],
        currencyCode: [{ required: true, message: this.$t('请选择币种') }]
      },

      currencyOptions: [],
      accountTypeOptions,
      reconciliationAccountOptions: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.supplierDetailSctableRef.$refs.xGrid
    },
    toolbar() {
      let arr = [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }]
      if (this.canEdit) {
        arr = [
          { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
          { code: 'save', name: this.$t('保存'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
          { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
          { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
        ]
      }
      return arr
    }
  },
  mounted() {
    this.getTableData()
    this.getCurrency({ text: '' })
  },
  methods: {
    accountTypeChange(row, accountTypeCode) {
      this.accountTypeOptions.forEach((item) => {
        if (item.value === accountTypeCode) {
          row.accountTypeName = item.label
        }
      })
      this.getReconciliationAccountOptions(row.accountTypeName)
    },
    reconciliationAccountChange(row, reconciliationAccount) {
      this.reconciliationAccountOptions.forEach((item) => {
        if (item.value === reconciliationAccount) {
          row.reconciliationAccountName = item.reconciliationAccountName
        }
      })
    },
    currencyCodeChange(row, currencyCode) {
      this.currencyOptions.forEach((item) => {
        if (item.value === currencyCode) {
          row.currencyName = item.currencyName
        }
      })
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.isEditing = false
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        headerId: this.$route?.query?.id,
        belong: 1,
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.reconciliationCollaboration
        .pageReconciliationItemApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      let ids = []
      selectedRecords.forEach((item) => {
        if (!item.id?.includes('row_')) {
          ids.push(item.id)
        }
      })
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'save':
          break
        case 'delete':
          if (ids.length !== 0) {
            this.handleDelete(ids)
          } else {
            this.handleSearch()
          }
          break
        case 'import':
          this.handleImport()
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleAdd() {
      if (!this.isEditing) {
        const item = {
          id: null,
          docNo: null,
          accountDate: new Date(),
          invoiceNo: null,
          accountTypeCode: null,
          accountTypeName: null,
          reconciliationAccount: null,
          reconciliationAccountName: null,
          digest: null,
          amount: null,
          currencyCode: null,
          currencyName: null
        }
        this.tableRef.insert([item])
        this.$nextTick(() => {
          // 获取最新的表格视图数据
          const currentViewRecords = this.tableRef.getTableData().visibleData
          // 将新增的那一条设置为编辑状态
          this.tableRef.setEditRow(currentViewRecords[0])
        })
        this.isEditing = true
      }
    },
    editBegin(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        this.getReconciliationAccountOptions({ value: row.accountTypeName })
      }
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        if (args.$event.target.innerText === this.$t('刷新')) {
          this.$emit('updateStatus', 'supplierDetail', false)
          this.tableRef.clearEdit()
          this.handleSearch()
          return
        }
        // 远程数据才有$event属性
        if (!this.isValidData(row)) {
          this.$emit('updateStatus', 'supplierDetail', true)
          // 当出现错误时，指定行进入编辑状态
          this.tableRef.setEditRow(row)
          return
        }
        this.handleSave(row)
      }
    },
    isValidData(data) {
      const { accountDate, accountTypeCode, amount, currencyCode } = data
      let valid = false
      if (!accountDate) {
        this.$toast({ content: this.$t('请选择入账日期'), type: 'warning' })
      } else if (!accountTypeCode) {
        this.$toast({ content: this.$t('请选择科目类别'), type: 'warning' })
      } else if (!amount) {
        this.$toast({ content: this.$t('请填写金额'), type: 'warning' })
      } else if (!currencyCode) {
        this.$toast({ content: this.$t('请选择币种'), type: 'warning' })
      } else {
        valid = true
      }

      return valid
    },
    handleSave(row) {
      let obj = {
        headerId: this.$route?.query?.id,
        belong: 1,
        ...row
      }
      if (obj.id?.includes('row_')) {
        obj.id = null
      }
      const params = {
        id: this.$route?.query?.id,
        supItemReqList: [obj]
      }
      this.$API.reconciliationCollaboration
        .saveReconciliationItemApi(params)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('保存成功'),
              type: 'success'
            })
            this.tableRef.clearEdit()
            this.updateData()
          } else {
            this.tableRef.setEditRow(row)
          }
        })
        .catch(() => {
          this.tableRef.setEditRow(row)
        })
    },
    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据?')
        },
        success: () => {
          let params = { ids: ids.join() }
          this.$API.reconciliationCollaboration.deleteReconciliationItemApi(params).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.updateData()
          })
        }
      })
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.reconciliationCollaboration.importReconciliationItemApi,
          downloadTemplateApi: this.$API.reconciliationCollaboration.downloadReconciliationItemApi,
          paramsKey: 'excel',
          asyncParams: {
            headerId: this.$route?.query?.id
          }
        },
        success: () => {
          // 导入之后刷新
          this.updateData()
        }
      })
    },
    handleExport(e) {
      const params = {
        headerId: this.$route?.query?.id,
        belong: 1,
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$API.reconciliationCollaboration
        .exportReconciliationItemApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    getReconciliationAccountOptions(val) {
      let params = {
        accountChartCode: this.isTv ? 'TV_ACCT_PAYABLE' : 'BD_ACCT_PAYABLE',
        accountSubjectTypeName: val
      }
      this.$API.masterData.getAccountSubjectApi(params).then((res) => {
        if (res.code === 200) {
          const list = res?.data || []
          this.reconciliationAccountOptions = list.map((item) => {
            return {
              label: item.accountSubjectCode + '-' + item.accountSubjectName,
              value: item.accountSubjectCode,
              reconciliationAccountName: item.accountSubjectName
            }
          })
        }
      })
    },
    getCurrency(args) {
      const { text } = args
      const params = {
        fuzzyParam: text
      }
      this.$API.masterData.getCurrencyFuzzyQuery(params).then((res) => {
        if (res.code === 200) {
          const list = res?.data || []
          this.currencyOptions = list.map((item) => {
            return {
              label: item.currencyCode + '-' + item.currencyName,
              value: item.currencyCode,
              currencyName: item.currencyName
            }
          })
        }
      })
    },
    updateData() {
      this.$emit('updateStatus', 'supplierDetail', false)
      this.$emit('saveSuccess')
      this.handleSearch()
    }
  }
}
</script>
