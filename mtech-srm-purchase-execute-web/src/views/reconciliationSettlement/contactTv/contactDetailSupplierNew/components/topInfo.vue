<template>
  <div :class="['detail-top-info', !isExpand && 'detail-top-info-small']" style="padding-bottom: 0">
    <!-- 头部的内容 -->
    <div class="header-box" style="margin-bottom: 20px">
      <!-- 左侧的信息 -->
      <div :class="[statusClass(headerInfo.status), 'mr20']">
        {{ headerInfo.status | statusFormat }}
      </div>
      <div class="infos mr20">{{ $t('对账单号：') }}{{ headerInfo.recoNo }}</div>
      <div class="infos mr20">{{ $t('创建人：') }}{{ headerInfo.createUserName }}</div>
      <div class="infos">{{ $t('创建时间：') }}{{ headerInfo.createTime | dateFormat }}</div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <span
        v-if="headerInfo.status === 1 || headerInfo.status === 5"
        class="header-box-btn"
        v-waves
        type="info"
        @click="doSave"
        >{{ $t('保存') }}</span
      >
      <span
        v-if="headerInfo.status === 1 || headerInfo.status === 5"
        class="header-box-btn"
        v-waves
        type="info"
        @click="doSubmit"
        >{{ $t('提交') }}</span
      >
      <span
        v-if="headerInfo.status === 4"
        class="header-box-btn"
        v-waves
        type="info"
        :class="headerInfo.status !== 4 ? 'disable-btn' : ''"
        @click="handlePrint"
        >{{ $t('打印') }}</span
      >
      <span class="header-box-btn" v-waves type="info" @click="goBack">{{ $t('返回') }}</span>

      <div class="sort-box" @click="doExpand">
        <span>{{ isExpand ? $t('收起') : $t('展开') }}</span>
        <i
          class="mt-icons mt-icon-MT_DownArrow"
          :class="isExpand ? 'expendIcon' : 'unExpendIcon'"
        />
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom" v-if="headerInfo">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="companyName" :label="$t('公司')">
          <mt-input v-model="headerInfo.companyName" :disabled="true" />
        </mt-form-item>

        <mt-form-item prop="supplierName" :label="$t('供应商/客户名称')">
          <mt-input v-model="headerInfo.supplierName" :disabled="true" />
        </mt-form-item>

        <mt-form-item prop="currencyCode" :label="$t('币种')">
          <mt-select
            v-model="headerInfo.currencyCode"
            :data-source="currencyOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            :disabled="headerInfo.status !== 1 && headerInfo.status !== 5"
            filter-type="Contains"
            :placeholder="$t('请选择币种')"
            @change="currencyChange"
          />
        </mt-form-item>

        <mt-form-item prop="deadlineDate" :label="$t('截止日期')">
          <mt-date-picker
            v-model="deadlineDate"
            :disabled="true"
            :show-clear-button="true"
            :allow-edit="false"
          />
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('采方备注')">
          <mt-input v-model="headerInfo.remark" :disabled="true" maxlength="500" />
        </mt-form-item>
        <mt-form-item prop="supRemark" :label="$t('供方备注')" class="full-width">
          <mt-input
            v-model="headerInfo.supRemark"
            :disabled="headerInfo.status !== 1 && headerInfo.status !== 5"
            :multiline="true"
            maxlength="500"
          />
        </mt-form-item>
        <mt-form-item prop="opinionDesc" :label="$t('驳回原因')" class="full-width">
          <mt-input
            v-model="headerInfo.opinionDesc"
            :disabled="true"
            :multiline="true"
            maxlength="500"
          />
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { ReconciledStatusCssClass, ReconciledStatusText } from '../config/constant'

export default {
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isExpand: true,
      rules: {
        currencyCode: [{ required: true, message: this.$t('请选择币种'), trigger: 'blur' }]
      },
      topInfo: {},
      currencyOptions: []
    }
  },
  computed: {
    deadlineDate() {
      let value = this.headerInfo.deadlineDate
      return timeStringToDate({ formatString: 'YYYY-mm', value })
    }
  },
  filters: {
    dateFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd HH:MM:SS', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd HH:MM:SS', value })
      }

      return str
    },
    statusFormat(value) {
      if (!ReconciledStatusText[value]) {
        return value
      } else {
        return ReconciledStatusText[value]
      }
    }
  },
  mounted() {
    this.getCurrency({ text: '' })
  },
  methods: {
    getCurrency(args) {
      const { text } = args
      const params = {
        fuzzyParam: text
      }
      this.$API.masterData.getCurrencyFuzzyQuery(params).then((res) => {
        if (res.code === 200) {
          const list = res?.data || []
          this.currencyOptions = list.map((item) => {
            return {
              text: item.currencyCode + '-' + item.currencyName,
              value: item.currencyCode,
              currencyName: item.currencyName
            }
          })
        }
      })
    },
    currencyChange(e) {
      const { currencyName } = e.itemData
      this.headerInfo.currencyName = currencyName
    },
    statusClass(value) {
      let cssClass = ''
      if (ReconciledStatusCssClass[value]) {
        cssClass = ReconciledStatusCssClass[value]
      }
      return cssClass
    },
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 保存
    doSave() {
      this.$emit('doSave')
    },
    // 提交
    doSubmit() {
      this.$emit('doSubmit')
    },
    handlePrint() {
      this.$emit('print')
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    }
  }
}
</script>

<style>
.disable-btn {
  background: #cccccc !important;
  pointer-events: none;
}
</style>
<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(25% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
