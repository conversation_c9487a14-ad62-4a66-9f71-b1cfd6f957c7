import { i18n } from '@/main.js'

export const accountTypeOptions = [
  {
    value: '1122',
    text: i18n.t('应收账款'),
    label: i18n.t('应收账款')
  },
  {
    value: '150301',
    text: i18n.t('应收票据'),
    label: i18n.t('应收票据')
  },
  {
    value: '1221',
    text: i18n.t('其他应收款'),
    label: i18n.t('其他应收款')
  },
  {
    value: '150302',
    text: i18n.t('预收账款'),
    label: i18n.t('预收账款')
  },
  {
    value: '2202',
    text: i18n.t('应付账款'),
    label: i18n.t('应付账款')
  },
  {
    value: '2241',
    text: i18n.t('其他应付款'),
    label: i18n.t('其他应付款')
  },
  {
    value: '1123',
    text: i18n.t('预付账款'),
    label: i18n.t('预付账款')
  }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'supplierName',
    title: i18n.t('对账单位名称')
  },
  {
    field: 'docNo',
    title: i18n.t('入账凭证编号'),
    editRender: {},
    slots: {
      edit: 'docNoEdit'
    }
  },
  {
    field: 'accountDate',
    title: i18n.t('入账日期'),
    editRender: {},
    slots: {
      edit: 'accountDateEdit'
    }
  },
  {
    field: 'invoiceNo',
    title: i18n.t('入账发票号'),
    editRender: {},
    slots: {
      edit: 'invoiceNoEdit'
    }
  },
  {
    field: 'accountTypeCode',
    title: i18n.t('科目类别'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.accountTypeName : ''
    },
    editRender: {},
    slots: {
      edit: 'accountTypeCodeEdit'
    }
  },
  {
    field: 'reconciliationAccount',
    title: i18n.t('会计科目代码'),
    editRender: {},
    slots: {
      edit: 'reconciliationAccountEdit'
    }
  },
  {
    field: 'reconciliationAccountName',
    title: i18n.t('会计科目名称'),
    editRender: {},
    slots: {
      edit: 'reconciliationAccountNameEdit'
    }
  },
  {
    field: 'digest',
    title: i18n.t('摘要'),
    editRender: {},
    slots: {
      edit: 'digestEdit'
    }
  },
  {
    field: 'amount',
    title: i18n.t('金额'),
    editRender: {},
    slots: {
      edit: 'amountEdit'
    }
  },
  {
    field: 'currencyCode',
    title: i18n.t('币种'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.currencyName : ''
    },
    editRender: {},
    slots: {
      edit: 'currencyCodeEdit'
    }
  }
]
