import { i18n } from '@/main.js'

export const Tab = {
  summary: 0, // 金额汇总
  purchaseDetails: 1, // 采方-往来明细
  supplierDetails: 2, // 供方-往来明细
  file: 3 // 相关附件
}

export const TabList = [
  {
    title: i18n.t('金额汇总'),
    code: Tab.summary
  },
  {
    title: i18n.t('采方-往来明细'),
    code: Tab.purchaseDetails
  },
  {
    title: i18n.t('供方-往来明细'),
    code: Tab.supplierDetails
  },
  {
    title: i18n.t('相关附件'),
    code: Tab.file
  }
]

// 表格编辑 的 RequestType
export const RequestType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  sorting: 'sorting',
  refresh: 'refresh'
}

// 表格编辑 的 action
export const ActionType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  edit: 'edit'
}

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 行编辑触发的 change 事件类型
export const ComponentChangeType = {
  code: 'code', // 例：下拉框选择“供应商”，供应商编码（code)要被修改
  link: 'link' // 例：计划组：根据物料+工厂+计划组的业务组类型 code 带出
}

// 表格 EditSettings
export const EditSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
} // 编辑设置

// 供方差异调整项 新增行固定数据
export const SupplierAdjustNewRowData = {
  thePrimaryKey: 'add' + new Date().getTime().toString(36).slice(3, 8),
  type: null, // 调整项类型
  digest: null, //	调整项摘要
  amount: null, // 调整金额
  headerId: null, // 对账单ID
  id: null // id
}

export const SupplierDetailNewRowData = {
  thePrimaryKey: 'add' + new Date().getTime().toString(36).slice(3, 8),
  id: null, // id
  accountDate: null, // 入账日期
  amount: null, // 金额
  cleanStatus: null, // 清账状态 0未清 1已清
  docNo: null, // 单据号
  headerId: null, // 往来对账单id
  invoiceNo: null, // 发票号
  reconciliationAccount: null, // 对账科目
  reconciliationAccountName: null, // 对账科目名称
  remark: null, // 备注
  type: null, // 往来类别 0-对账；1-索赔；2-返利
  typeName: null, // 往来类别名称
  currencyCode: null // 币种
}

// 页面显示类型
export const EntryType = {
  view: 1, // 查看
  edit: 2, // 继续发布时就行编辑
  upload: 3 // 上传附件
}

// 状态
export const ReconciledStatus = {
  pending: 1, // 已通知
  submited: 2, // 已提交
  generated: 3, // 已生成
  confirmed: 4, // 已确认
  returned: 5 // 已退回
}

// 往来类别 text
export const ReconciledStatusText = {
  [ReconciledStatus.pending]: i18n.t('已通知'),
  [ReconciledStatus.submited]: i18n.t('已提交'),
  [ReconciledStatus.generated]: i18n.t('已生成'),
  [ReconciledStatus.confirmed]: i18n.t('已确认'),
  [ReconciledStatus.returned]: i18n.t('已退回')
}

// 往来类别 对应的 css class
export const ReconciledStatusCssClass = {
  [ReconciledStatus.pending]: 'col-active',
  [ReconciledStatus.submited]: 'col-active',
  [ReconciledStatus.generated]: 'col-active',
  [ReconciledStatus.confirmed]: 'col-active',
  [ReconciledStatus.returned]: 'col-inactive'
}

// 往来类别 对应的 Options
export const ReconciledStatusOptions = [
  {
    // 已通知
    value: ReconciledStatus.pending,
    text: ReconciledStatusText[ReconciledStatus.pending],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.pending]
  },
  {
    // 已提交
    value: ReconciledStatus.submited,
    text: ReconciledStatusText[ReconciledStatus.submited],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.submited]
  },
  {
    // 已生成
    value: ReconciledStatus.generated,
    text: ReconciledStatusText[ReconciledStatus.generated],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.generated]
  },
  {
    // 已确认
    value: ReconciledStatus.confirmed,
    text: ReconciledStatusText[ReconciledStatus.confirmed],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.confirmed]
  },
  {
    // 已退回
    value: ReconciledStatus.returned,
    text: ReconciledStatusText[ReconciledStatus.returned],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.returned]
  }
]

// 清账状态 0未清 1已清
export const CleanStatus = {
  notYet: 0, // 未清
  already: 1 // 已清
}

// 清账状态 text
export const CleanStatusText = {
  [CleanStatus.notYet]: i18n.t('未清'),
  [CleanStatus.already]: i18n.t('已清')
}

// 清账状态 对应的 css class
export const CleanStatusCssClass = {
  [CleanStatus.notYet]: '',
  [CleanStatus.already]: ''
}

// 清账状态 对应的 Options
export const CleanStatusOptions = [
  {
    // 未清
    value: CleanStatus.notYet,
    text: CleanStatusText[CleanStatus.notYet],
    cssClass: CleanStatusCssClass[CleanStatus.notYet]
  },
  {
    // 已清
    value: CleanStatus.already,
    text: CleanStatusText[CleanStatus.already],
    cssClass: CleanStatusCssClass[CleanStatus.already]
  }
]

// 差异调整型类型
export const AdjustType = {
  plus: 1, // 新增项
  minus: -1 // 新减项
}

// 差异调整型类型 text
export const AdjustTypeText = {
  [AdjustType.plus]: i18n.t('调增项'),
  [AdjustType.minus]: i18n.t('调减项')
}

// 差异调整型类型 对应的 css class
export const AdjustTypeCssClass = {
  [AdjustType.plus]: '',
  [AdjustType.minus]: ''
}

// 差异调整型类型 对应的 Options
export const AdjustTypeOptions = [
  {
    // 新增项
    value: AdjustType.plus,
    text: AdjustTypeText[AdjustType.plus],
    cssClass: AdjustTypeCssClass[AdjustType.plus]
  },
  {
    // 新减项
    value: AdjustType.minus,
    text: AdjustTypeText[AdjustType.minus],
    cssClass: AdjustTypeCssClass[AdjustType.minus]
  }
]

// 差异调整型类型 对应的 Options
export const AdjustTypePlusOptions = [
  {
    // 新增项
    value: AdjustType.plus,
    text: AdjustTypeText[AdjustType.plus],
    cssClass: AdjustTypeCssClass[AdjustType.plus]
  }
  // {
  //   // 新减项
  //   value: AdjustType.minus,
  //   text: AdjustTypeText[AdjustType.minus],
  //   cssClass: AdjustTypeCssClass[AdjustType.minus]
  // }
]

// 差异调整型类型 对应的 Options
export const AdjustTypeReduceOptions = [
  // {
  //   // 新增项
  //   value: AdjustType.plus,
  //   text: AdjustTypeText[AdjustType.plus],
  //   cssClass: AdjustTypeCssClass[AdjustType.plus]
  // },
  {
    // 新减项
    value: AdjustType.minus,
    text: AdjustTypeText[AdjustType.minus],
    cssClass: AdjustTypeCssClass[AdjustType.minus]
  }
]

// 金额汇总-采方对账科目
export const PurchaseSummaryColumns = [
  {
    field: 'accountTypeName',
    headerText: i18n.t('采方科目名称')
  },
  {
    field: 'amount',
    headerText: i18n.t('采方科目金额')
  }
]

// 金额汇总-供方对账科目
export const SupplierSummaryColumns = [
  {
    field: 'accountTypeName',
    headerText: i18n.t('供方科目名称')
  },
  {
    field: 'amount',
    headerText: i18n.t('供方科目金额')
  }
]

// 往来明细 表格列数据 - 采方
export const DetailsColumnData = [
  {
    fieldCode: 'checkBox' // 不可编辑
  },
  {
    fieldCode: 'serialNumber', // 前端定义,不可编辑
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'thePrimaryKey'
  },
  {
    fieldCode: 'companyName',
    fieldName: i18n.t('编制单位名称')
  },
  {
    fieldCode: 'docNo',
    fieldName: i18n.t('入账凭证编号')
  },
  {
    fieldCode: 'accountDate',
    fieldName: i18n.t('入账日期')
  },
  {
    fieldCode: 'reconciliationAccount',
    fieldName: i18n.t('会计科目代码')
  },
  {
    fieldCode: 'reconciliationAccountName',
    fieldName: i18n.t('会计科目名称')
  },
  {
    fieldCode: 'accountTypeName',
    fieldName: i18n.t('科目类别')
  },
  {
    fieldCode: 'digest',
    fieldName: i18n.t('摘要')
  },
  {
    fieldCode: 'amount',
    fieldName: i18n.t('金额')
  },
  {
    fieldCode: 'currencyCode',
    fieldName: i18n.t('币种')
  },
  {
    fieldCode: 'settleAccountsStatus', // 清账状态 0未清 1已清
    fieldName: i18n.t('清账状态')
  }
]

export const SupplierDetailsColumnData = [
  {
    fieldCode: 'checkBox' // 不可编辑
  },
  {
    fieldCode: 'serialNumber', // 前端定义,不可编辑
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'thePrimaryKey'
  },
  {
    fieldCode: 'supplierName',
    fieldName: i18n.t('对账单位名称'),
    allowEditing: false
  },
  {
    fieldCode: 'docNo',
    fieldName: i18n.t('入账凭证编号')
  },
  {
    fieldCode: 'accountDate',
    fieldName: i18n.t('入账日期')
  },
  {
    fieldCode: 'invoiceNo',
    fieldName: i18n.t('入账发票号')
  },
  {
    fieldCode: 'accountTypeCode',
    fieldName: i18n.t('会计科目代码')
  },
  {
    fieldCode: 'accountTypeName',
    fieldName: i18n.t('会计科目名称')
  },
  {
    fieldCode: 'reconciliationAccount',
    fieldName: i18n.t('科目类别')
  },
  {
    fieldCode: 'digest',
    fieldName: i18n.t('摘要')
  },
  {
    fieldCode: 'amount',
    fieldName: i18n.t('金额')
  },
  {
    fieldCode: 'currencyCode',
    fieldName: i18n.t('币种')
  }
]
// 操作日志 表格列数据
export const LogColumnData = [
  {
    fieldCode: 'operTypeName',
    fieldName: i18n.t('操作类型')
  },
  {
    fieldCode: 'operDescription',
    fieldName: i18n.t('操作内容') // 操作描述 操作内容
  },
  {
    fieldCode: 'createUserName',
    fieldName: i18n.t('操作人')
  },
  {
    fieldCode: 'createTime',
    fieldName: i18n.t('操作时间')
  }
]

// 差异调整项 表格列数据
export const AdjustColumnData = [
  {
    fieldCode: 'checkBox' // 不可编辑
  },
  {
    fieldCode: 'serialNumber', // 前端定义,不可编辑
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'thePrimaryKey'
  },
  {
    fieldCode: 'digest',
    fieldName: i18n.t('调整项摘要')
  },
  {
    fieldCode: 'amount',
    fieldName: i18n.t('调整项金额')
  }
]
