<!-- 往来对账汇总tab -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item :label="$t('往来对账单号')" prop="recoNo">
          <mt-input
            v-model="searchFormModel.recoNo"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供应商')" prop="supplierCode">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('状态')" prop="status">
          <mt-select
            v-model="searchFormModel.status"
            :data-source="statusOptions"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('截止日期')" prop="deadlineDate">
          <mt-date-range-picker
            v-model="searchFormModel.deadlineDate"
            @change="(e) => dateChange(e, 'deadlineDate')"
            :placeholder="$t('请选择截止日期')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('核查人')" prop="recoUserName">
          <mt-input
            v-model="searchFormModel.recoUserName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('公司')" prop="companyCode">
          <!-- <RemoteAutocomplete
            v-model="searchFormModel.companyCode"
            url="/masterDataManagement/tenant/organization/specified-level-paged-query"
            :placeholder="$t('请选择')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :params="{
              organizationLevelCodes: ['ORG01', 'ORG02']
            }"
          /> -->
          <!-- <RemoteAutocomplete
            v-model="searchFormModel.companyCode"
            url="/masterDataManagement/auth/company/auth-fuzzy"
            :params="{
              organizationLevelCodes: ['ORG02', 'ORG01'],
              orgType: 'ORG001PRO',
              includeItself: true
            }"
            :placeholder="$t('请选择')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            records-position="data"
          /> -->
          <mt-select
            v-model="searchFormModel.companyCode"
            :data-source="companyOptions"
            :show-clear-button="false"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="871d3613-5f1f-406a-ac0e-bb7a520e0009"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      keep-source
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #recoNoDetault="{ row }">
        <span style="cursor: pointer; color: #2783fe" @click="recoNoClick(row)">{{
          row.recoNo
        }}</span>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />

    <RejectDialog ref="rejectDialogRef" @confirm="handleSearch" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, statusOptions } from './config'
import { getHeadersFileName, download } from '@/utils/utils'
import RejectDialog from './components/RejectDialog.vue'

export default {
  components: { CollapseSearch, ScTable, RejectDialog },
  data() {
    const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))

    return {
      userInfo,
      searchFormModel: {
        recoUserName: userInfo.username
      },
      toolbar: [
        { code: 'create', name: this.$t('生成'), status: 'info', loading: false },
        { code: 'confirm', name: this.$t('确认'), status: 'info', loading: false },
        { code: 'reject', name: this.$t('驳回'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      statusOptions,
      companyOptions: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    if (this.$store.state.reconSummaryTvSearch) {
      this.searchFormModel = this.$store.state.reconSummaryTvSearch
    }
    this.getCompanyOptions()
    this.getTableData()
  },
  beforeDestroy() {
    this.$store.commit('setReconSummaryTvSearch', null)
  },
  methods: {
    getCompanyOptions() {
      this.$API.reconciliationReport.getCompanyList().then((res) => {
        if (res.code === 200) {
          this.companyOptions = res.data.map((item) => {
            return {
              ...item,
              text: item.orgCode + '-' + item.orgName,
              value: item.orgCode
            }
          })
        }
      })
    },
    dateChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = dayjs(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        ).valueOf()
        this.searchFormModel[field + 'End'] =
          dayjs(dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')).valueOf() + 999 // + 999是后端要求
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    recoNoClick(row) {
      this.$router.push({
        name: 'recon-summary-detail',
        query: {
          id: row.id,
          // type: row.status === 3 ? 2 : 1, // 已生成才可编辑  1查看，2编辑
          timeStamp: new Date().getTime()
        }
      })
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.searchFormModel.recoUserName = this.userInfo.username
      this.handleSearch()
    },
    handleSearch() {
      this.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      this.$store.commit('setReconSummaryTvSearch', this.searchFormModel)
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.reconciliationSettlement
        .pageReconSummaryApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const ids = []
      selectedRecords.forEach((item) => {
        ids.push(item.id)
      })
      const commonToolbar = ['create', 'confirm', 'reject']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'create':
          if (selectedRecords.length > 1) {
            this.$toast({
              content: this.$t('仅能选择一条状态为已提交或已生成的数据进行操作！'),
              type: 'warning'
            })
          } else if (selectedRecords[0].status !== 2 && selectedRecords[0].status !== 3) {
            this.$toast({
              content: this.$t('请选择一条状态为已提交或已生成的数据进行操作！'),
              type: 'warning'
            })
          } else {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('确认生成?')
              },
              success: () => {
                this.handleCreate(ids[0])
              }
            })
          }
          break
        case 'confirm':
          if (selectedRecords.length > 1) {
            this.$toast({
              content: this.$t('仅能选择一条状态为已生成的数据进行操作！'),
              type: 'warning'
            })
          } else if (selectedRecords[0].status !== 3) {
            this.$toast({
              content: this.$t('请选择一条状态为已生成的数据进行操作！'),
              type: 'warning'
            })
          } else {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('是否确认?')
              },
              success: () => {
                this.handleConfirm(ids[0])
              }
            })
          }
          break
        case 'reject':
          if (selectedRecords.length > 1) {
            this.$toast({
              content: this.$t('仅能选择一条状态为已提交或已生成的数据进行操作！'),
              type: 'warning'
            })
          } else if (selectedRecords[0].status !== 2 && selectedRecords[0].status !== 3) {
            this.$toast({
              content: this.$t('请选择一条状态为已提交或已生成的数据进行操作！'),
              type: 'warning'
            })
          } else {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('确认驳回?')
              },
              success: () => {
                this.handleReject(ids[0])
              }
            })
          }
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$API.reconciliationSettlement
        .exportReconSummaryApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    handleCreate(id) {
      let params = { id, status: 0 }
      this.$API.reconciliationSettlement.createReconSummaryApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleConfirm(id) {
      let params = { id, status: 4 }
      this.$API.reconciliationSettlement.feedbackReconSummaryApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleReject(id) {
      let params = { id, status: 5 }
      this.$refs.rejectDialogRef.dialogInit({
        title: this.$t('驳回'),
        row: params
      })
    }
  }
}
</script>
