<!-- 驳回 -->
<template>
  <mt-dialog
    ref="dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    @beforeOpen="beforeOpen"
    @close="close"
    width="650px"
    height="400px"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" style="margin-top: 16px">
      <mt-row :gutter="24">
        <mt-col :span="24">
          <mt-form-item prop="opinionDesc" :label="$t('驳回原因')">
            <mt-input
              v-model="formData.opinionDesc"
              :show-clear-button="true"
              :placeholder="$t('请输入')"
            />
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {},
      rules: {
        opinionDesc: [
          {
            required: true,
            message: this.$t('请输入驳回原因'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    dialogInit(args) {
      this.$refs.dialog.ejsRef.show()
      const { title, row } = args
      this.dialogTitle = title
      this.formData = row
    },
    beforeOpen() {
      this.formData = {}
      this.$refs.ruleForm.clearValidate()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    close() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate()
      this.formData = {}
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.handleSave()
        }
      })
    },
    handleSave() {
      let params = { ...this.formData }
      const api = this.$API.reconciliationSettlement.feedbackReconSummaryApi
      api(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$emit('confirm')
          this.handleClose()
        }
      })
    }
  }
}
</script>
