import { i18n } from '@/main.js'

export const columnData = [
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'supplierName',
    title: i18n.t('对账单位名称')
  },
  {
    field: 'docNo',
    title: i18n.t('入账凭证编号')
  },
  {
    field: 'accountDate',
    title: i18n.t('入账日期')
  },
  {
    field: 'invoiceNo',
    title: i18n.t('入账发票号')
  },
  {
    field: 'accountTypeName',
    title: i18n.t('科目类别')
  },
  {
    field: 'reconciliationAccount',
    title: i18n.t('会计科目代码')
  },
  {
    field: 'reconciliationAccountName',
    title: i18n.t('会计科目名称')
  },
  {
    field: 'digest',
    title: i18n.t('摘要')
  },
  {
    field: 'amount',
    title: i18n.t('金额')
  },
  {
    field: 'currencyCode',
    title: i18n.t('币种'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.currencyName : ''
    }
  }
]
