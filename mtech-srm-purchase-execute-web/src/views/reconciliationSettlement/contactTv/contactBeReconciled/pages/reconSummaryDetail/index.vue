<template>
  <!-- 往来对账单详情-采方 -->
  <div
    class="full-height pt20 vertical-flex-box"
    :class="{ 'sticky-height': currentTabInfo.code === Tab.summary }"
  >
    <div class="sticky-nav">
      <!-- 头部信息 -->
      <top-info
        class="flex-keep"
        ref="topInfoRef"
        :header-info="headerInfo"
        @doSave="doSave"
        @doSubmit="doSubmit"
        @goBack="goBack"
        @doExpand="doExpand"
        @btnClick="btnClick"
      />
    </div>

    <mt-tabs
      class="flex-keep toggle-tab"
      :e-tab="false"
      :data-source="tabList"
      :selected-item="selectedItem"
      @handleSelectTab="handleSelectTab"
    />
    <div class="toggle-content">
      <!-- 金额汇总 -->
      <div class="flex-fit summary" v-show="currentTabInfo.code === Tab.summary">
        <div class="box">
          <div class="title">
            <span>{{ $t('对账科目') }}</span>
          </div>
          <div class="row-box">
            <!-- 采方-金额汇总 -->
            <div class="item-box">
              <div class="top">
                <div class="item">
                  <div>{{ $t('编制单位') }}</div>
                  <mt-input v-model="headerInfo.companyName" :disabled="true" />
                </div>
                <div class="item">
                  <div>{{ $t('当前余额') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.purCurrentBalance) }}
                  </span>
                </div>
                <div class="item">
                  <div>{{ $t('应收账款') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.purReceivableAccounts) }}
                  </span>
                </div>
              </div>
              <div class="top">
                <div class="item">
                  <div>{{ $t('其他应收款') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.purOtherReceivableAccounts) }}
                  </span>
                </div>
                <div class="item">
                  <div>{{ $t('预付账款') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.purPrepayments) }}
                  </span>
                </div>
                <div class="item">
                  <div>{{ $t('应付账款') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.purPayableAccounts) }}
                  </span>
                </div>
              </div>
              <div class="top">
                <div class="item">
                  <div>{{ $t('其他应付款') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.purOtherPayableAccounts) }}
                  </span>
                </div>
              </div>
            </div>
            <div class="dividing-line"></div>
            <!-- 供方-金额汇总 -->
            <div class="item-box">
              <div class="top">
                <div class="item">
                  <div>{{ $t('对账单位') }}</div>
                  <mt-input v-model="headerInfo.supplierName" :disabled="true" />
                </div>
                <div class="item">
                  <div>{{ $t('当前余额') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.supCurrentBalance) }}
                  </span>
                </div>
                <div class="item">
                  <div>{{ $t('应收账款') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.supReceivableAccounts) }}
                  </span>
                </div>
              </div>
              <div class="top">
                <div class="item">
                  <div>{{ $t('应收票据') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.supReceivableInvoiceAccounts) }}
                  </span>
                </div>
                <div class="item">
                  <div>{{ $t('其他应收款') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.supOtherReceivableAccounts) }}
                  </span>
                </div>
                <div class="item">
                  <div>{{ $t('预付账款') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.supPrepayments) }}
                  </span>
                </div>
              </div>
              <div class="top">
                <div class="item">
                  <div>{{ $t('应付账款') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.supPayableAccounts) }}
                  </span>
                </div>
                <div class="item">
                  <div>{{ $t('其他应付款') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.supOtherPayableAccounts) }}
                  </span>
                </div>
                <div class="item">
                  <div>{{ $t('预收账款') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.depositReceived) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="title">
            <span>{{ $t('差异调整项') }}</span>
          </div>
          <div class="row-box">
            <!-- 采方差异调整项 -->
            <div class="item-box">
              <div class="table-box">
                <span class="table-title">{{ $t('调增项：') }}</span>
                <div class="table-area">
                  <PurchaseAdjust
                    table-type="plus"
                    :can-edit="headerInfo.status === 3"
                    @updateStatus="updateStatus"
                    @updateData="getDetailList"
                  />
                </div>
              </div>
              <div class="top">
                <div class="item">
                  <div>{{ $t('加项合计') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.purAddAdjust) }}
                  </span>
                </div>
              </div>
              <div class="table-box">
                <span class="table-title">{{ $t('调减项：') }}</span>
                <div class="table-area">
                  <PurchaseAdjust
                    table-type="reduce"
                    :can-edit="headerInfo.status === 3"
                    @updateStatus="updateStatus"
                    @updateData="getDetailList"
                  />
                </div>
              </div>
              <div class="top">
                <div class="item">
                  <div>{{ $t('减项合计') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.purSubAdjust) }}
                  </span>
                </div>
              </div>
              <div class="top">
                <div class="item">
                  <div>{{ $t('调整后余额') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.purAdjustedBalance) }}
                  </span>
                </div>
              </div>
              <div class="top">
                <div class="item">
                  <div>{{ $t('调整后差异') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.purAdjustedDiff) }}
                  </span>
                </div>
              </div>
              <div class="top">
                <div class="item">
                  <div><span style="color: red">*</span>{{ $t('制表') }}</div>
                  <mt-input v-model="headerInfo.preparerName" :disabled="headerInfo.status !== 3" />
                </div>
              </div>
              <div class="top">
                <div class="item">
                  <div><span style="color: red">*</span>{{ $t('联系电话') }}</div>
                  <mt-input v-model="headerInfo.contactPhone" :disabled="headerInfo.status !== 3" />
                </div>
              </div>
              <div class="top">
                <div class="item">
                  <div><span style="color: red">*</span>{{ $t('mail') }}</div>
                  <mt-input
                    v-model="headerInfo.emailReceiver"
                    :disabled="headerInfo.status !== 3"
                  />
                </div>
              </div>
            </div>
            <div class="dividing-line"></div>
            <!-- 供方差异调整项 -->
            <div class="item-box">
              <div class="table-box">
                <span class="table-title">{{ $t('调增项：') }}</span>
                <div class="table-area">
                  <SupplierAdjust table-type="plus" />
                </div>
              </div>
              <div class="top">
                <div class="item">
                  <div>{{ $t('加项合计') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.supAddAdjust) }}
                  </span>
                </div>
              </div>
              <div class="table-box">
                <span class="table-title">{{ $t('调减项：') }}</span>
                <div class="table-area">
                  <SupplierAdjust table-type="reduce" />
                </div>
              </div>
              <div class="top">
                <div class="item">
                  <div>{{ $t('减项合计') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.supSubAdjust) }}
                  </span>
                </div>
              </div>
              <div class="top">
                <div class="item">
                  <div>{{ $t('调整后余额') }}</div>
                  <span class="item-value">
                    {{ formatNumber(headerInfo.supAdjustedBalance) }}
                  </span>
                </div>
              </div>
              <div class="top">
                <div class="item">
                  <div>{{ $t('制表') }}</div>
                  <mt-input v-model="headerInfo.receiverName" :disabled="true" />
                </div>
              </div>
              <div class="top">
                <div class="item">
                  <div>{{ $t('联系电话') }}</div>
                  <mt-input v-model="headerInfo.custContactTel" :disabled="true" />
                </div>
              </div>
              <div class="top">
                <div class="item">
                  <div>{{ $t('mail') }}</div>
                  <mt-input v-model="headerInfo.supplierEmail" :disabled="true" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 采方-往来明细 -->
      <div class="flex-fit" v-show="currentTabInfo.code === Tab.purchaseDetails">
        <PurchaseDetail />
      </div>
      <!-- 供方-往来明细 -->
      <div class="flex-fit" v-show="currentTabInfo.code === Tab.supplierDetails">
        <SupplierDetail :header-info="headerInfo" />
      </div>
      <!-- 相关附件 -->
      <div class="flex-fit" v-show="currentTabInfo.code === Tab.file">
        <relative-file
          ref="relativeFileRef"
          :doc-id="relativeFileData.docId"
          :request-url-obj="relativeFileData.requestUrlObj"
          :module-file-list="moduleFileList"
          :is-view="relativeFileData.isView"
        ></relative-file>
      </div>
    </div>
    <RejectDialog ref="rejectDialogRef" @confirm="rejcetConfirm" />
  </div>
</template>

<script>
import { Tab, TabList } from './config/constant'
import { BASE_TENANT } from '@/utils/constant'

export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue'),
    PurchaseAdjust: () => import('./components/purchaseAdjust.vue'),
    SupplierAdjust: () => import('./components/supplierAdjust.vue'),
    PurchaseDetail: () => import('./components/purchaseDetail.vue'),
    SupplierDetail: () => import('./components/supplierDetail.vue'),
    RelativeFile: () => import('@/components/businessComponents/relativeFile/index.vue'),
    RejectDialog: () =>
      import(
        '@/views/reconciliationSettlement/contactTv/contactBeReconciled/pages/reconSummary/components/RejectDialog.vue'
      )
  },
  data() {
    const statementId = this.$route?.query?.id // 从URL获取对账单id

    return {
      Tab,
      headerInfo: {}, // 主单信息
      selectedItem: 0,
      statementId, // 对账单id
      currentTabInfo: TabList[0], // 当前tab的数据，默认选中第一项
      tabList: TabList,
      hasChangeSupplierDetails: false,
      moduleFileList: [
        {
          id: 'trans_reconciliation_header_supplier', // 选中时传给api的 doctype 的值
          nodeName: this.$t('供方-整单附件'), // 侧边栏名称
          nodeCode: 0, // 即当前 index 用于选择左侧边栏时 获取当前项 配置
          btnRequired: {
            hasUpload: true,
            hasDownload: true,
            hasDelete: true,
            hasPrint: false
          }, // 表格 toolbar 配置（可选） hasUpload: Boolean、hasDownload: Boolean、 hasDelete: Boolean
          hasItem: false, // 是否显示附件行数据（可选）
          deleteFileUrl: `${BASE_TENANT}/transaction_reconciliation/files/deleteSupplierFile`, // 删除文件使用的 API 根据ID删除
          deleteFileRequestMethod: 'delete' // 设置删除api的请求方法
        }
      ], // 相关附件 左侧菜单
      // 相关附件配置
      relativeFileData: {
        docId: statementId,
        requestUrlObj: {
          preUrl: 'reconciliationSettlement',
          saveUrl: 'postTransactionReconciliationFilesSupplierSave', // 将附件文件url保存到列表 保存文件信息
          fileUrl: `${BASE_TENANT}/transaction_reconciliation/files/queryList` // 获取附件列表的url 根据docType和docID查询所有文件信息
        },
        isView: true // 查看的状态 || 待反馈状态，没有上传和删除
      },

      edittingPlus: false,
      edittingReduce: false
    }
  },
  mounted() {
    this.getDetailList()
  },
  methods: {
    updateStatus(type, value) {
      if (type === 'plus') {
        this.edittingPlus = value
      } else if (type === 'reduce') {
        this.edittingReduce = value
      }
    },
    formatNumber(number) {
      return number.toLocaleString()
    },
    getDetailList() {
      this.$API.reconciliationCollaboration
        .getContactDetailSupplierNewApi({
          id: this.statementId
        })
        .then((res) => {
          if (res.code === 200) {
            this.hasChangeSupplierDetails = false
            const data = res?.data || {}
            this.headerInfo = data
          }
        })
    },
    btnClick(flag) {
      switch (flag) {
        case 'create':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认生成?')
            },
            success: () => {
              this.handleCreate()
            }
          })
          break
        case 'confirm':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('是否确认?')
            },
            success: () => {
              this.handleConfirm()
            }
          })
          break
        case 'reject':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认驳回?')
            },
            success: () => {
              this.handleReject()
            }
          })
          break
      }
    },
    handleCreate() {
      let params = { id: this.$route?.query?.id, status: 0 }
      this.$API.reconciliationSettlement.createReconSummaryApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleJump()
        }
      })
    },
    handleConfirm() {
      let params = { id: this.$route?.query?.id, status: 4 }
      this.$API.reconciliationSettlement.feedbackReconSummaryApi(params).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleJump()
        }
      })
    },
    handleReject() {
      let params = { id: this.$route?.query?.id, status: 5 }
      this.$refs.rejectDialogRef.dialogInit({
        title: this.$t('驳回'),
        row: params
      })
    },
    rejcetConfirm() {
      this.handleJump()
    },
    handleJump() {
      this.getDetailList()
      // this.$router.push({
      //   name: 'contact-be-reconciled-tv',
      //   query: {
      //     timeStamp: new Date().getTime(),
      //     currentIndex: 2
      //   }
      // })
    },
    doSave() {
      if (this.edittingPlus) {
        return
      }
      if (this.edittingReduce) {
        return
      }
      const emailReg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
      const params = {
        preparerName: this.headerInfo.preparerName,
        contactPhone: this.headerInfo.contactPhone,
        emailReceiver: this.headerInfo.emailReceiver,
        id: this.statementId, // 对账单Id
        remark: this.headerInfo.remark // 采方备注
      }
      if (params.emailReceiver && !emailReg.test(params.emailReceiver)) {
        this.$toast({
          content: this.$t('请输入格式正确的邮箱'),
          type: 'error'
        })
        return
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationCollaboration
        .saveReconciliationApi(params)
        .then((res) => {
          this.$store.commit('endLoading')
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    doSubmit() {
      if (!this.headerInfo.preparerName) {
        this.$toast({ content: this.$t('请填写制表人'), type: 'warning' })
        return
      }
      if (!this.headerInfo.contactPhone) {
        this.$toast({ content: this.$t('请填写电话'), type: 'warning' })
        return
      }
      if (!this.headerInfo.emailReceiver) {
        this.$toast({ content: this.$t('请填写email'), type: 'warning' })
        return
      }
      const params = {
        preparerName: this.headerInfo.preparerName,
        contactPhone: this.headerInfo.contactPhone,
        emailReceiver: this.headerInfo.emailReceiver,
        id: this.statementId, // 对账单Id
        remark: this.headerInfo.remark // 采方备注
      }
      this.handleSubmit(params)
    },
    handleSubmit(params) {
      this.$store.commit('startLoading')
      this.$API.reconciliationCollaboration
        .submitReconciliationApi(params)
        .then((res) => {
          this.$store.commit('endLoading')
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleJump()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    // 返回
    goBack() {
      // this.$router.go(-1)
      this.$router.push({
        name: 'contact-be-reconciled-tv',
        query: {
          timeStamp: new Date().getTime(),
          currentIndex: 2
        }
      })
    },
    supplierDetailChange() {
      this.hasChangeSupplierDetails = true
    },
    // 点击 tab
    handleSelectTab(e) {
      this.currentTabInfo = this.tabList[e]
      if (this.currentTabInfo.code === Tab.summary && this.hasChangeSupplierDetails) {
        // 修改了供方往来明细，回到金额汇总tab时重新获取主单明细数据
        this.getDetailList()
        this.hasChangeSupplierDetails = false
      }
    },
    doExpand() {}
  }
}
</script>

<style lang="scss" scoped>
.sticky-height {
  height: 190vh;
}
.sticky-nav {
  position: sticky;
  top: 0;
  z-index: 999;
}
.summary {
  background-color: var(--plugin-dg-bg-ff);
  height: 100%;
  overflow: auto;
  .box {
    flex-direction: column;
    .title {
      margin: 24px 0px 24px 24px;
      padding-left: 8px;
      border-left: 4px solid #3369ac;
      font-weight: 600;
    }
    .tips {
      margin: 24px 0px 24px 60px;
      .icon {
        padding-right: 8px;
        color: #6487bf;
      }
    }
    .row-box {
      flex: 1 1 auto;
      display: flex;
      position: relative;
      .item-box {
        width: calc(50% - 1px); // 两项 共计 2px 预留给中间的虚线
        &.empty {
          display: flex;
          justify-content: center;
          flex-direction: column;
          min-height: 200px;
          align-items: center;
          color: #a6a6a6;
        }

        .top {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          margin: 12px 32px 24px 32px;
          .item {
            display: flex;
            align-items: center;
            width: 100%;
            div {
              width: 16%;
              padding-right: 8px;
              box-sizing: border-box;
              text-align: right;
            }
            .mt-input {
              flex: 1;
            }
            .item-value {
              flex: 1;
              background: #fafafa;
              padding: 8px 0px;
              margin: 0 8px 4px 0;
              color: #000000;
              font-size: 13px;
            }
          }
        }
        .table-box {
          display: flex;
          align-items: center;
          margin: 12px 32px 0px 32px;
          width: 77%;
          .table-title {
            white-space: nowrap;
          }
          .table-area {
            flex: 1;
            width: 100%;
          }
        }
      }
      .dividing-line {
        border-left: 2px dashed #d9d9d9;
      }
    }
  }
}
</style>
