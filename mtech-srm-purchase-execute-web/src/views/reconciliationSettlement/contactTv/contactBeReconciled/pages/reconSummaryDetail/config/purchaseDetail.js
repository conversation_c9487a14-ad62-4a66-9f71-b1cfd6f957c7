import { i18n } from '@/main.js'

export const settleAccountsStatusOptions = [
  { text: i18n.t('未清'), value: 0 },
  { text: i18n.t('已清'), value: 1 }
]

export const columnData = [
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'companyName',
    title: i18n.t('编制单位名称')
  },
  {
    field: 'docNo',
    title: i18n.t('入账凭证编号')
  },
  {
    field: 'lineNo',
    title: i18n.t('入账凭证行号')
  },
  {
    field: 'accountDate',
    title: i18n.t('入账日期')
  },
  {
    field: 'accountTypeName',
    title: i18n.t('科目类别')
  },
  {
    field: 'reconciliationAccount',
    title: i18n.t('会计科目代码')
  },
  {
    field: 'reconciliationAccountName',
    title: i18n.t('会计科目名称')
  },
  {
    field: 'digest',
    title: i18n.t('摘要')
  },
  {
    field: 'amount',
    title: i18n.t('金额')
  },
  {
    field: 'currencyCode',
    title: i18n.t('币种'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.currencyName : ''
    }
  },
  {
    field: 'settleAccountsStatus',
    title: i18n.t('清账状态'),
    formatter: ({ cellValue }) => {
      let item = settleAccountsStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'settleAccountsDate',
    title: i18n.t('清账日期')
  },
  {
    field: 'liquidationDocNo',
    title: i18n.t('清账凭证')
  },
  {
    field: 'liquidationYear',
    title: i18n.t('清算会计年度')
  }
]
