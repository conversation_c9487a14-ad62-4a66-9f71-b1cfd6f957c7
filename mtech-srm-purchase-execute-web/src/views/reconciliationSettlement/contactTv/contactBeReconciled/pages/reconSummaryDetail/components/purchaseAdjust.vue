<template>
  <div>
    <sc-table
      ref="sctableRef"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      :fix-height="300"
      :edit-rules="validRules"
      keep-source
      :edit-config="{
        enabled: canEdit,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }"
      @edit-closed="editComplete"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #digestEdit="{ row }">
        <vxe-input v-model.trim="row.digest" :placeholder="$t('请输入')" />
      </template>
      <template #amountEdit="{ row }">
        <vxe-input
          v-model.trim="row.amount"
          type="number"
          min="-999999999999.99"
          max="999999999999.99"
          :placeholder="$t('请输入')"
        />
      </template>
      <template #amountDefault="{ row }">
        {{ formatNumber(row.amount) }}
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { columnData } from '../config/purchaseAdjust'

export default {
  name: 'PurchaseAdjust',
  components: { ScTable },
  props: {
    tableType: {
      type: String,
      default: 'plus'
    },
    canEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      columns: columnData,
      tableData: [],
      validRules: {
        digest: [{ required: true, message: this.$t('请输入调整项摘要') }],
        amount: [{ required: true, message: this.$t('请输入调整项金额') }]
      },
      isEditing: false
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    toolbar() {
      let arr = []
      if (this.canEdit) {
        arr = [
          { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
          { code: 'save', name: this.$t('保存'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
        ]
      }
      return arr
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    formatNumber(number) {
      return number?.toLocaleString()
    },
    handleSearch() {
      this.isEditing = false
      this.getTableData()
    },
    async getTableData() {
      const params = {
        headerId: this.$route?.query?.id,
        belong: 2,
        type: this.tableType === 'plus' ? 1 : -1
      }
      this.loading = true
      const res = await this.$API.reconciliationCollaboration
        .pageAdjustApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const records = res.data || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      let ids = []
      selectedRecords.forEach((item) => {
        if (!item.id.includes('row_')) {
          ids.push(item.id)
        }
      })
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'save':
          break
        case 'delete':
          if (ids.length !== 0) {
            this.$dialog({
              data: {
                title: this.$t('提示'),
                message: this.$t('确认删除？')
              },
              success: () => {
                this.handleDelete(ids)
              }
            })
          } else {
            this.handleSearch()
          }
          break
        default:
          break
      }
    },
    handleAdd() {
      if (!this.isEditing) {
        const item = {
          id: null,
          digest: null,
          amount: null
        }
        this.tableRef.insert([item])
        this.$nextTick(() => {
          // 获取最新的表格视图数据
          const currentViewRecords = this.tableRef.getTableData().visibleData
          // 将新增的那一条设置为编辑状态
          this.tableRef.setEditRow(currentViewRecords[0])
        })
        this.isEditing = true
      }
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        if (args.$event.target.innerText === this.$t('刷新')) {
          this.$emit('updateStatus', this.tableType, false)
          this.tableRef.clearEdit()
          this.handleSearch()
          return
        }
        if (!this.isValidData(row)) {
          this.$emit('updateStatus', this.tableType, true)

          // 当出现错误时，指定行进入编辑状态
          this.tableRef.setEditRow(row)
          return
        }
        this.handleSave(row)
      }
    },
    isValidData(data) {
      const { digest, amount } = data
      let valid = false
      if (!digest) {
        this.$toast({ content: this.$t('请输入调整项摘要'), type: 'warning' })
      } else if (!amount) {
        this.$toast({ content: this.$t('请输入调整项金额'), type: 'warning' })
      } else {
        valid = true
      }

      return valid
    },
    handleSave(row) {
      let data = {
        headerId: this.$route?.query?.id,
        belong: 2,
        type: this.tableType === 'plus' ? 1 : -1,
        ...row
      }
      if (row.id.includes('row_')) {
        data.id = null
      }
      const params = {
        id: this.$route?.query?.id,
        adjustReqList: [data]
      }
      this.$API.reconciliationCollaboration
        .saveOrUpdataBatchAdjustApi(params)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('保存成功'),
              type: 'success'
            })
            this.updateData()
            this.handleSearch()
          } else {
            this.tableRef.setEditRow(row)
          }
        })
        .catch(() => {
          this.tableRef.setEditRow(row)
        })
    },
    handleDelete(ids) {
      let params = {
        ids: ids.join(),
        headerId: this.$route?.query?.id
      }
      this.$API.reconciliationCollaboration.deleteAdjustApi(params).then(() => {
        this.$toast({
          content: this.$t('删除成功'),
          type: 'success'
        })
        this.updateData()
        this.handleSearch()
      })
    },
    updateData() {
      this.$emit('updateStatus', this.tableType, false)
      this.$emit('updateData')
    }
  }
}
</script>
