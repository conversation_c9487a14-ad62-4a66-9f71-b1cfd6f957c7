<template>
  <div>
    <sc-table
      ref="sctableRef"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :fix-height="300"
      @refresh="handleSearch"
    >
      <template #amountDefault="{ row }">
        {{ formatNumber(row.amount) }}
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { columnData } from '../config/supplierAdjust'
export default {
  name: 'SupplierAdjust',
  components: { ScTable },
  props: {
    tableType: {
      type: String,
      default: 'plus'
    }
  },
  data() {
    return {
      loading: false,
      columns: columnData,
      tableData: []
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    formatNumber(number) {
      return number?.toLocaleString()
    },
    handleSearch() {
      this.getTableData()
    },
    async getTableData() {
      const params = {
        headerId: this.$route?.query?.id,
        belong: 1,
        type: this.tableType === 'plus' ? 1 : -1
      }
      this.loading = true
      const res = await this.$API.reconciliationCollaboration
        .pageAdjustApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const records = res.data || []
        this.tableData = records
      }
    }
  }
}
</script>
