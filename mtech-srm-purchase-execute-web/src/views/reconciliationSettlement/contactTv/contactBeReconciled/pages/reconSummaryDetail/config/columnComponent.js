import Vue from 'vue'
import { timeNumberToDate, timeStringToDate, addCodeNameKeyInList } from '@/utils/utils'
import { ComponentChangeType } from './constant'
import { rowDataTemp } from './variable'
// import { cloneDeep } from "lodash";
import { utils } from '@mtech-common/utils'

export const ColumnComponent = {
  // 时间日期显示
  timeDate: (args) => {
    const { dataKey, hasTime } = args

    const template = () => {
      return {
        template: Vue.component('date', {
          template: `<div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div><div>{{data[dataKey] | dateFormat}}</div></div>`,
          data: function () {
            return { data: {}, dataKey, hasTime }
          },
          filters: {
            dateFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
              } else {
                str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
              }

              return str
            },
            timeFormat(value) {
              let str = ''
              // 数据库时间戳默认值为 0，为 0 时不显示
              if (value == 0) {
                return str
              }
              if (isNaN(Number(value))) {
                str = timeStringToDate({ formatString: 'HH:MM:SS', value })
              } else {
                str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
              }

              return str
            }
          }
        })
      }
    }

    return template
  },
  // 时间输入
  timeInput: (args) => {
    const { dataKey, disabled, showClearBtn, allowEdit, isDate, isTime, isDateTime, maxDate } = args

    const template = () => {
      return {
        template: Vue.component('timeInputComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <mt-time-picker
              v-if="isTime"
              v-model="componentData"
              :disabled="disabled"
              :show-clear-button="showClearBtn"
              :allow-edit="allowEdit"
              @change="onChange"
              placeholder=""
            ></mt-time-picker>
            <mt-date-picker
              v-if="isDate"
              v-model="componentData"
              :disabled="disabled"
              :show-clear-button="showClearBtn"
              :allow-edit="allowEdit"
              :max="maxDate"
              @change="onChange"
              placeholder=""
            ></mt-date-picker>
            <mt-date-time-picker
              v-if="isDateTime"
              v-model="componentData"
              :disabled="disabled"
              :show-clear-button="showClearBtn"
              :allow-edit="allowEdit"
              @change="onChange"
              placeholder=""
            ></mt-date-time-picker>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              disabled,
              showClearBtn,
              allowEdit,
              isDate,
              isTime,
              isDateTime,
              maxDate,
              componentData: null // 组件内部绑定的变量
            }
          },
          filters: {},
          mounted() {
            this.componentData = this.formateInitData(this.data[dataKey])
          },
          methods: {
            formateInitData(value) {
              let date = null
              if (this.data[dataKey] == 0 || !this.data[dataKey]) {
                // 数据库时间戳默认值为 0，为 0 时不显示，为空时不显示
                return null
              } else if (typeof value === 'string') {
                let tmpData = null
                if (isNaN(Number(value))) {
                  // 处理时间字符串的情况 例如：YYY-MM-DD
                  tmpData = new Date(value)
                } else {
                  // 处理时间戳的情况
                  tmpData = new Date(Number(value))
                }

                // 校验是否转换为了时间
                if (isNaN(tmpData.getTime())) {
                  date = null
                } else {
                  date = tmpData
                }
              } else {
                date = new Date(Number(value))
              }
              return date
            },
            onChange(e) {
              let data = null
              if (isDate) {
                data = e
              } else if (isTime && e.isInteracted) {
                data = e.value
              }

              this.data[dataKey] = data
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = data
            }
          }
        })
      }
    }

    return template
  },
  // 空的显示
  empty: () => {
    return {
      template: Vue.component('emptyComponent', {
        template: `<div></div>`,
        data: function () {
          return {
            data: {}
          }
        }
      })
    }
  },
  // 不可编辑的文字显示
  text: (args) => {
    const { dataKey, cellTools, valueConverter } = args
    const template = () => {
      return {
        template: Vue.component('textComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">{{data[dataKey] | format}}</div>
            <div class="column-tool mt-flex invite-btn" v-if="haveShowCellTool">
              <div v-for="(cellTool, index) in cellTools"
                :key="index" :id="cellTool.id"
                class="template-svg"
                v-if="!cellTool.visibleCondition || cellTool.visibleCondition(data)"
                @click.stop="clickCellTool({tool:cellTool, data})"
                ><MtIcon :name="cellTool.icon" />&nbsp<span class="icon-title">{{cellTool.title}}</span>
              </div>
            </div>
          </div>`,
          data: function () {
            return { data: {}, dataKey, cellTools, haveShowCellTool: false }
          },
          mounted() {
            if (cellTools?.length > 0) {
              for (let i = 0; i < cellTools.length; i++) {
                const cellTool = cellTools[i]
                if (!cellTool.visibleCondition || cellTool.visibleCondition(this.data)) {
                  this.haveShowCellTool = true
                  break
                }
              }
            }
          },
          filters: {
            format: (value) => {
              let data = value
              if (valueConverter?.type === 'map' && valueConverter?.map?.length) {
                // 转换
                const mapList = valueConverter.map
                const findItem = mapList.find((item) => item.value === value)
                data = findItem?.text
              }
              return data
            }
          },
          methods: {
            clickCellTool(data) {
              this.$parent.$emit('handleClickCellTool', data)
            }
          }
        })
      }
    }
    return template
  },
  // 下拉框 编辑
  select: (args) => {
    const {
      dataKey,
      selectOptions,
      fields,
      allowFiltering,
      showClearBtn,
      modifiedKeys,
      modifiedRelation
    } = args
    const template = () => {
      return {
        template: Vue.component('selectComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-select
                @change="selectChange"
                v-model="data[dataKey]"
                :allow-filtering="allowFiltering"
                :data-source="selectOptions"
                :show-clear-button="showClearBtn"
                :disabled="disabled"
                :placeholder="$t('请选择')"
                :fields="fields"
              ></mt-select>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              selectOptions,
              fields,
              dataKey,
              allowFiltering,
              showClearBtn,
              ComponentChangeType,
              disabled: false
            }
          },
          mounted() {
            this.disabled = dataKey === 'adjustType' && this.data.id ? true : false
          },
          methods: {
            // 修改中的值
            selectChange(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e.value
              if (modifiedKeys?.length > 0) {
                // 触发改变的值
                this.triggerCodeChange(e.itemData)
              }
            },
            // 触发改变的值
            triggerCodeChange(selectData) {
              const args = {
                requestKey: dataKey, // 触发请求的key
                modifiedKeys: modifiedKeys || [], // 要被修改的key列表
                changeType: ComponentChangeType.code, // 修改类型
                data: selectData, // 发出的值
                modifiedRelation // 对应数据源中的 key 关系
              }
              this.$bus.$emit('contactReconciledDetailColumnChange', args)
            }
          }
        })
      }
    }
    return template
  },
  // 数字 编辑
  number: (args) => {
    const { dataKey, showClearBtn, precision, maxValue, minValue } = args
    const template = () => {
      return {
        template: Vue.component('numberComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-input-number
                v-model="data[dataKey]"
                :max="maxValue"
                :min="minValue"
                :precision="precision"
                :show-spin-button="false"
                :show-clear-button="showClearBtn"
                @input="onChange"
                @keydown.native="numberInputOnKeyDown"
              ></mt-input-number>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              maxValue,
              minValue,
              showClearBtn,
              precision: precision || 2
            }
          },
          mounted() {},
          methods: {
            numberInputOnKeyDown(e) {
              if (['e', 'E', '+'].includes(e.key)) {
                e.returnValue = false
              } else {
                e.returnValue = true
              }
            },
            onChange(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e
            }
          }
        })
      }
    }
    return template
  },
  // 编辑行时的 文本数据
  input: (args) => {
    const { dataKey, showClearBtn, maxlength } = args
    const template = () => {
      return {
        template: Vue.component('inputComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-input
                v-model="data[dataKey]"
                :show-clear-button="showClearBtn"
                :maxlength="maxlength"
                @input="onInput"
              ></mt-input>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              showClearBtn,
              maxlength
            }
          },
          mounted() {},
          methods: {
            onInput(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e
            }
          }
        })
      }
    }
    return template
  },
  // 币种 code-name 下拉框选择 模糊搜索
  currencyCodeSelect: () => {
    return {
      template: Vue.component('currencyCodeSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.currencyCode"
              :data-source="currencyOptions"
              :fields="{ text: 'theCodeName', value: 'currencyCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              @change="currencyCodeChange"
              :open-dispatch-change="false"
              :placeholder="$t('请选择')"
              :disabled="disabled"
              :popup-width="250"
            ></mt-select>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            currencyOptions: [], // 供应商 下拉选项
            doGetDataSource: () => {},
            disabled: false
          }
        },
        mounted() {
          this.initGetCurrency()
          this.doGetDataSource = utils.debounce(this.getCurrency, 1000)
        },
        beforeDestroy() {},
        methods: {
          // 主数据 供应商
          getCurrency(args) {
            const { text, updateData, setSelectData } = args
            const params = {
              fuzzyParam: text
            }
            this.$API.masterData.getCurrencyFuzzyQuery(params).then((res) => {
              if (res) {
                const list = res?.data || []
                this.currencyOptions = addCodeNameKeyInList({
                  firstKey: 'currencyCode',
                  secondKey: 'currencyName',
                  list
                })
                if (updateData) {
                  this.$nextTick(() => {
                    updateData(this.currencyOptions)
                  })
                }
                if (setSelectData) {
                  this.$nextTick(() => {
                    setSelectData()
                  })
                }
              }
            })
          },
          // 初始化检索
          initGetCurrency() {
            const currencyCode = this.data.currencyCode
            this.getCurrency({
              text: currencyCode,
              setSelectData: () => {
                // api获取数据后重新赋值，防止没有赋上值得情况
                this.data.currencyCode = currencyCode
              }
            })
          },
          // change
          currencyCodeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].currencyCode = itemData.currencyCode // code
              rowDataTemp[rowDataTemp.length - 1].currencyName = itemData.currencyName // name
            } else {
              rowDataTemp[rowDataTemp.length - 1].currencyCode = null // code
              rowDataTemp[rowDataTemp.length - 1].currencyName = null // name
            }
          },
          // 监听变化
          onComponentChange() {}
        }
      })
    }
  }
}
