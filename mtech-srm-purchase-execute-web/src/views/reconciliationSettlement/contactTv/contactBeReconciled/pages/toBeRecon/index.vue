<template>
  <!-- 采方-对账协同-采购对账单列表 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      class="template-height"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="companyCodes" :label="$t('公司')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.companyCodes"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                multiple
                :placeholder="$t('请选择公司')"
                :fields="{ text: 'orgName', value: 'orgCode' }"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="supplierCodes" :label="$t('供应商编码')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.supplierCodes"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择供应商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="accountDate" :label="$t('入账截止日期')" label-style="top">
              <mt-date-range-picker
                style="flex: 1"
                v-model="searchFormModel.accountDate"
                @change="(e) => handleDateTimeChange(e, 'accountDate')"
                :placeholder="$t('请选择入账截止日期')"
              />
            </mt-form-item>
            <!-- <mt-form-item prop="reconciliationPerson" :label="$t('对账人')" label-style="top">
              <mt-input
                v-model="searchFormModel.reconciliationPerson"
                :show-clear-button="true"
                :placeholder="$t('请输入对账人')"
              />
            </mt-form-item>
            <mt-form-item prop="reconciliationFlag" :label="$t('是否可对账')" label-style="top">
              <mt-select
                style="flex: 1"
                v-model="searchFormModel.reconciliationFlag"
                :data-source="[
                  {
                    text: '是',
                    value: 0
                  },
                  {
                    text: '否',
                    value: 1
                  }
                ]"
                :show-clear-button="true"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item> -->
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      searchFormModel: {},
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          toolbar: [
            {
              id: 'CreateStatement',
              icon: 'icon_solid_Createorder',
              title: this.$t('创建对账单')
            }
          ],
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData,
            dataSource: [],
            // 往来待对账-往来待对账汇总信息
            asyncConfig: {
              url: `${BASE_TENANT}/tv/transactionWait/summary`
              // defaultRules: []
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  created() {},
  methods: {
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        // this.searchFormModel[field + 'Start'] = dayjs(e.startDate).format('YYYYMM')
        // this.searchFormModel[field + 'End'] = dayjs(e.endDate).format('YYYYMM')
        this.searchFormModel[field + 'Start'] = new Date(e.startDate).getTime()
        this.searchFormModel[field + 'End'] = new Date(e.endDate).getTime()
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    handleClickToolBar(args) {
      const { toolbar } = args
      if (toolbar.id == 'CreateStatement') {
        this.$dialog({
          modal: () => import('./../../components/createDialog.vue'),
          data: {
            title: this.$t('创建对账单'),
            message: this.$t('是否确认创建对账单？')
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    // 往来待对账-创建对账单
    doCreateStatement(data) {
      this.apiStartLoading()
      // 租户级-往来对账单-采方-批量快速创建往来对账单并发布
      this.$API.reconciliationSettlement
        .postTransactionReconciliationBatchSave(data)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            // this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style></style>
