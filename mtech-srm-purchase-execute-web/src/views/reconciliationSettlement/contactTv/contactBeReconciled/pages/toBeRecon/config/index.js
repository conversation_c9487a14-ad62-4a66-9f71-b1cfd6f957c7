import { i18n } from '@/main.js'
import UTILS from '@/utils/utils'
import { codeNameColumn } from '@/utils/utils'

// 往来待对账明细 表格列数据
export const columnData = [
  {
    field: 'companyCode', // 公司
    headerText: i18n.t('公司'),
    width: '300',
    template: codeNameColumn({
      firstKey: 'companyCode',
      secondKey: 'companyName'
    })
    // companyName
  },
  {
    field: 'supplierCode', // 供应商
    headerText: i18n.t('供应商'),
    width: '400',
    template: codeNameColumn({
      firstKey: 'supplierCode',
      secondKey: 'supplierName'
    })
    // supplierName
  },
  {
    field: 'accountDate', // 入账截止日期 accountDate入账日期 accountMonth入账月份 accountYear入账年份
    headerText: i18n.t('入账截止日期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return e
        }
      }
    }
  },
  {
    field: 'dataLength', // 详情明细
    width: '150',
    headerText: i18n.t('详情明细')
  }
  // {
  //   field: 'reconciliationPerson', // 对账人
  //   width: '150',
  //   headerText: i18n.t('对账人')
  // }
]
