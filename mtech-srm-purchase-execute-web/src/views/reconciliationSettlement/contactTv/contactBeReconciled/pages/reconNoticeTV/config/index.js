import { i18n } from '@/main.js'

export const monthOptions = [
  { text: '01', value: 1 },
  { text: '02', value: 2 },
  { text: '03', value: 3 },
  { text: '04', value: 4 },
  { text: '05', value: 5 },
  { text: '06', value: 6 },
  { text: '07', value: 7 },
  { text: '08', value: 8 },
  { text: '09', value: 9 },
  { text: '10', value: 10 },
  { text: '11', value: 11 },
  { text: '12', value: 12 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商代码')
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称')
  },
  {
    field: 'recoUserCode',
    title: i18n.t('对账人代码')
  },
  {
    field: 'recoUserName',
    title: i18n.t('对账人名称')
  },
  {
    field: 'recoUserMailbox',
    title: i18n.t('对账人邮件')
  },
  {
    field: 'errorInfo',
    title: i18n.t('错误消息')
  }
]
