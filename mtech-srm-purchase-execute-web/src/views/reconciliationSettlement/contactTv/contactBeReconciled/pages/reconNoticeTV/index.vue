<!-- 往来对账通知tab -->
<template>
  <div>
    <mt-form
      ref="ruleForm"
      :model="formData"
      :rules="rules"
      style="margin-top: 16px; padding: 0 10px"
    >
      <mt-row :gutter="24">
        <mt-col :span="4">
          <mt-form-item prop="companyCode" :label="$t('公司')">
            <!-- <RemoteAutocomplete
              v-model="formData.companyCode"
              url="/masterDataManagement/tenant/organization/specified-level-paged-query"
              :placeholder="$t('请选择')"
              :fields="{ text: 'orgName', value: 'orgCode' }"
              :params="{
                organizationLevelCodes: ['ORG01', 'ORG02']
              }"
              @change="companyChange"
            /> -->
            <!-- <RemoteAutocomplete
              v-model="formData.companyCode"
              url="/masterDataManagement/auth/company/auth-fuzzy"
              :params="{
                organizationLevelCodes: ['ORG02', 'ORG01'],
                orgType: 'ORG001PRO',
                includeItself: true
              }"
              :placeholder="$t('请选择')"
              :fields="{ text: 'orgName', value: 'orgCode' }"
              records-position="data"
              @change="companyChange"
            /> -->
            <mt-select
              v-model="formData.companyCode"
              :data-source="companyOptions"
              :show-clear-button="false"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
              @change="companyChange"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="4">
          <mt-form-item prop="deadlineYear" :label="$t('截止年份')">
            <mt-input
              v-model="formData.deadlineYear"
              :placeholder="$t('请输入')"
              :show-clear-button="true"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="4">
          <mt-form-item prop="deadlineMonth" :label="$t('截止月份')">
            <mt-select
              v-model="formData.deadlineMonth"
              :data-source="monthOptions"
              :show-clear-button="false"
              :placeholder="$t('请选择')"
            />
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
    <sc-table
      ref="sctableRef"
      grid-id="aa6d9d53-6f25-4577-9506-13ecefb66048"
      :loading="loading"
      :is-show-refresh-bth="false"
      :columns="columns"
      :table-data="tableData"
      keep-source
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { columnData, monthOptions } from './config'
export default {
  components: { ScTable },
  data() {
    return {
      toolbar: [
        { code: 'import', name: this.$t('导入'), status: 'info', loading: false },
        { code: 'create', name: this.$t('生成通知单'), status: 'info', loading: false }
      ],
      columns: columnData,
      loading: false,
      tableData: [],

      formData: {},
      rules: {
        companyCode: [
          {
            required: true,
            message: this.$t('请选择公司'),
            trigger: 'blur'
          }
        ],
        deadlineYear: [
          {
            required: true,
            message: this.$t('请输入截止年份'),
            trigger: 'blur'
          }
        ],
        deadlineMonth: [
          {
            required: true,
            message: this.$t('请选择截止月份'),
            trigger: 'blur'
          }
        ]
      },
      monthOptions,
      companyOptions: []
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getCompanyOptions()
  },
  methods: {
    getCompanyOptions() {
      this.$API.reconciliationReport.getCompanyList().then((res) => {
        if (res.code === 200) {
          this.companyOptions = res.data.map((item) => {
            return {
              ...item,
              text: item.orgCode + '-' + item.orgName,
              value: item.orgCode
            }
          })
        }
      })
    },
    companyChange(e) {
      this.formData.companyName = e.itemData?.orgName
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['create']
      if (this.tableData.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先导入数据'), type: 'warning' })
        return
      }
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'import':
          this.handleImport()
          break
        case 'create':
          this.handleCreate(selectedRecords)
          break
        default:
          break
      }
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$API.reconciliationSettlement.importReconNoticeTVApi,
          downloadTemplateApi: this.$API.reconciliationSettlement.downloadReconNoticeTVApi,
          paramsKey: 'excel'
        },
        success: (data) => {
          console.log(data)
          this.loading = true
          this.setTableData(data)
        }
      })
    },
    setTableData(data) {
      this.tableData = data
      this.loading = false
    },
    handleCreate(selectedRecords) {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = {
            list: selectedRecords,
            ...this.formData
          }
          this.$API.reconciliationSettlement.createReconNoticeTVApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
            }
          })
        } else {
          this.$toast({ content: this.$t('请完善条件'), type: 'warning' })
        }
      })
    }
  }
}
</script>
