import { i18n } from '@/main.js'
import { codeNameColumn } from '@/utils/utils'
import { ColumnComponent as Component } from './columnComponent'

// 对账通知 序列化
export const serializeNotifyList = (list) => {
  if (list.length > 0) {
    list.forEach((item, index) => {
      item.serialNumber = index + 1 // 序号
      item.thePrimaryKey = item.id // 主键
    })
  }
  return list
}
// 表格编辑 的 RequestType
export const RequestType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  sorting: 'sorting',
  refresh: 'refresh'
}

// 表格编辑 的 action
export const ActionType = {
  save: 'save',
  beginEdit: 'beginEdit',
  add: 'add',
  edit: 'edit'
}
// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  companyCode: null, // 公司编号
  companyName: null, // 	公司名称
  reconciliationPerson: null, // 对账人
  reconciliationPersonCode: null, // 对账人代码
  reconciliationStatus: 0, // 允许本月对账 0-不允许；1-允许
  supplierCode: null, // 供应商/客户编号
  supplierName: null // 	供应商/客户名称
}

// 表格 EditSettings
export const EditSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Top'
} // 编辑设置

// 允许本月对账 0-不允许；1-允许
export const ReconciliationStatus = {
  notAllowed: 0, // 不允许
  allow: 1 // 允许
}

// 允许本月对账 text
export const ReconciliationStatusText = {
  [ReconciliationStatus.notAllowed]: i18n.t('不允许'),
  [ReconciliationStatus.allow]: i18n.t('允许')
}

// 允许本月对账 对应的 css class
export const ReconciliationStatusCssClass = {
  [ReconciliationStatus.notAllowed]: '',
  [ReconciliationStatus.allow]: ''
}

// 允许本月对账 对应的 Options
export const ReconciliationStatusOptions = [
  {
    // 不允许
    value: ReconciliationStatus.notAllowed,
    text: ReconciliationStatusText[ReconciliationStatus.notAllowed],
    cssClass: ReconciliationStatusCssClass[ReconciliationStatus.notAllowed]
  },
  {
    // 允许
    value: ReconciliationStatus.allow,
    text: ReconciliationStatusText[ReconciliationStatus.allow],
    cssClass: ReconciliationStatusCssClass[ReconciliationStatus.allow]
  }
]

// 允许本月对账 对应的 Options
export const AssociationSupplierOptions = [
  { value: 0, text: i18n.t('非关联供应商') },
  { value: 1, text: i18n.t('关联供应商') }
]

// 对账通知 toolbar
export const NotifyToolbar = [
  {
    id: 'AddNotify',
    icon: 'icon_table_new',
    title: i18n.t('新建')
  },
  {
    id: 'DeleteNotify',
    icon: 'icon_table_delete',
    title: i18n.t('删除')
  },
  {
    id: 'UpdateNotify',
    icon: 'icon_table_save',
    title: i18n.t('更新')
  },
  // {
  //   id: 'SendNotify',
  //   icon: 'icon_table_UpgradeAppointment',
  //   title: i18n.t('发送通知')
  // },
  {
    id: 'ImportNotify',
    icon: 'icon_solid_Import',
    title: i18n.t('导入')
  },
  {
    id: 'ExportNotify',
    icon: 'icon_solid_export',
    title: i18n.t('导出')
  }
]

// 往来待对账明细 表格列数据
export const columnData = [
  {
    field: 'checkBox', // 不可编辑
    type: 'checkbox',
    allowFiltering: false,
    allowEditing: false,
    showInColumnChooser: false,
    width: '50'
  },
  {
    field: 'thePrimaryKey',
    visible: false,
    isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
    isPrimaryKey: true,
    allowEditing: false
  },
  {
    field: 'serialNumber', // 前端定义,不可编辑
    headerText: i18n.t('序号'),

    width: '80',
    allowFiltering: false,
    allowEditing: false,
    ignore: true,
    template: Component.text({
      dataKey: 'serialNumber'
    }),
    editTemplate: Component.text({
      dataKey: 'serialNumber'
    })
  },
  {
    field: 'companyCode', // 公司 可编辑
    headerText: i18n.t('公司'),
    width: '300',
    template: codeNameColumn({
      firstKey: 'companyCode',
      secondKey: 'companyName'
    }),
    editTemplate: Component.companySelect,
    headerTemplate: Component.requiredHeader({
      headerText: i18n.t('公司')
    }),

    allowEditing: true,
    allowFiltering: false
  },
  // {
  //   field: "companyName", // 公司名称 可编辑
  //   headerText: i18n.t("公司名称"),
  // },
  {
    field: 'supplierCode', // 供应商/客户 可编辑
    headerText: i18n.t('供应商/客户'),
    width: '400',
    template: codeNameColumn({
      firstKey: 'supplierCode',
      secondKey: 'supplierName'
    }),
    editTemplate: Component.supplierSelect,
    headerTemplate: Component.requiredHeader({
      headerText: i18n.t('供应商/客户')
    }),

    allowEditing: true,
    allowFiltering: false
  },
  // {
  //   field: "supplierName", // 供应商/客户名称 可编辑
  //   headerText: i18n.t("供应商/客户名称"),
  // },
  {
    field: 'reconciliationStatus', // 允许本月对账 可编辑
    headerText: i18n.t('是否本次对账'),
    width: '150',
    allowEditing: true,
    template: Component.text({
      dataKey: 'reconciliationStatus',
      valueConverter: {
        type: 'map',
        map: ReconciliationStatusOptions
      }
    }),
    editTemplate: Component.switch({
      dataKey: 'reconciliationStatus',
      onLabel: ReconciliationStatusText[ReconciliationStatus.allow],
      offLabel: ReconciliationStatusText[ReconciliationStatus.notAllowed],
      activeValue: ReconciliationStatus.allow,
      inactiveValue: ReconciliationStatus.notAllowed
    })
    // 允许本月对账 0-不允许；1-允许
  },
  {
    field: 'reconciliationPerson', // 对账人 可编辑
    headerText: i18n.t('对账人'),
    width: '350',
    headerTemplate: Component.requiredHeader({
      headerText: i18n.t('对账人')
    }),
    allowEditing: true,
    template: Component.text({
      dataKey: 'reconciliationPerson'
    }),
    editTemplate: Component.reconciliationPersonSelect
    // reconciliationPersonCode	对账人代码
  },
  {
    field: 'reconciliationPersonEmail', // 对账人邮箱 可编辑
    headerText: i18n.t('对账人邮箱'),
    width: '350',
    headerTemplate: Component.requiredHeader({
      headerText: i18n.t('对账人邮箱')
    }),
    allowEditing: false,
    template: Component.text({
      dataKey: 'reconciliationPersonEmail'
    })
  },
  {
    field: 'isAssociationSupplier', // 是否关联供应商 不可可编辑
    headerText: i18n.t('是否关联供应商'),
    width: '150',
    allowEditing: false,
    template: Component.text({
      dataKey: 'isAssociationSupplier',
      valueConverter: {
        type: 'map',
        map: AssociationSupplierOptions
      }
    }),
    editTemplate: Component.text({
      dataKey: 'isAssociationSupplier',
      valueConverter: {
        type: 'map',
        map: AssociationSupplierOptions
      }
    })
  },
  {
    field: 'currencyCode', // 币种 不可可编辑
    headerText: i18n.t('币种'),
    width: '150',
    allowFiltering: false,
    allowEditing: false,
    showInColumnChooser: false
  },
  {
    field: 'amountSum', // 金额 不可可编辑
    headerText: i18n.t('金额'),
    width: '150',
    allowFiltering: false,
    allowEditing: false,
    showInColumnChooser: false
  },
  {
    field: 'updateUserName', // 更新人 不可可编辑
    headerText: i18n.t('更新人'),
    width: '150',
    allowFiltering: false,
    allowEditing: false,
    showInColumnChooser: false
  },
  {
    field: 'updateTime', // 更新时间 不可可编辑
    headerText: i18n.t('更新时间'),
    width: '150',
    allowFiltering: false,
    allowEditing: false,
    showInColumnChooser: false
  }
]
