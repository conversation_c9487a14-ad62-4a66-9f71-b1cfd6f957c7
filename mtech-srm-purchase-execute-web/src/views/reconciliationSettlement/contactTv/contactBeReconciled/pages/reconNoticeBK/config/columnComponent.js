import Vue from 'vue'
import { utils } from '@mtech-common/utils'
import { addCodeNameKeyInList } from '@/utils/utils'
import { rowDataTemp } from './variable'

// 行编辑触发的 change 事件类型
export const ComponentChangeType = {
  code: 'code', // 例：下拉框选择“供应商”，供应商编码（code)要被修改
  link: 'link' // 例：计划组：根据物料+工厂+计划组的业务组类型 code 带出
}

export const ColumnComponent = {
  // 公司
  companySelect: () => {
    return {
      template: Vue.component('companySelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.companyCode"
              :filtering="doGetDataSource"
              :data-source="companyOptions"
              :fields="{ text: 'theCodeName', value: 'orgCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              @change="companyCodeChange"
              :open-dispatch-change="true"
              :placeholder="$t('请选择')"
            ></mt-select>
          </div>
        </div>`,

        data() {
          return {
            data: {},
            companyOptions: [], // 公司 下拉选项
            doGetDataSource: () => {}
          }
        },
        mounted() {
          this.initGetCompany()
          this.doGetDataSource = utils.debounce(this.getCompany, 1000)
        },
        methods: {
          // 主数据 获取公司
          getCompany(args) {
            const { text, updateData, setSelectData } = args
            this.$API.masterData
              .OrgFindSpecifiedChildrenLevelOrgs({
                fuzzyParam: text || '',
                organizationLevelCodes: ['ORG02', 'ORG01'],
                orgType: 'ORG001PRO',
                includeItself: true,
                organizationIds: []
              })
              .then((res) => {
                const list = res?.data || []
                this.companyOptions = addCodeNameKeyInList({
                  firstKey: 'orgCode',
                  secondKey: 'orgName',
                  list
                })
                if (updateData) {
                  this.$nextTick(() => {
                    updateData(this.companyOptions)
                  })
                }
                if (setSelectData) {
                  this.$nextTick(() => {
                    setSelectData()
                  })
                }
              })
          },
          // 初始化检索 公司
          initGetCompany() {
            const selectData = this.data.companyCode
            this.getCompany({
              text: selectData,
              setSelectData: () => {
                // api获取数据后重新赋值，防止没有赋上值得情况
                this.data.companyCode = selectData
              }
            })
          },
          // 公司 change
          companyCodeChange(e) {
            const { itemData } = e
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].companyCode = itemData.orgCode
              rowDataTemp[rowDataTemp.length - 1].companyName = itemData.orgName
            } else {
              rowDataTemp[rowDataTemp.length - 1].companyCode = null
              rowDataTemp[rowDataTemp.length - 1].companyName = null
            }
          }
        }
      })
    }
  },
  // 带红星的表头
  requiredHeader: (args) => {
    const { headerText } = args
    const template = () => {
      return {
        template: Vue.component('requiredHeaderComponent', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{ headerText }}</span>
              </div>
            `,
          data: function () {
            return {
              data: {},
              headerText
            }
          },
          beforeDestroy() {},
          mounted() {},
          methods: {}
        })
      }
    }
    return template
  },
  // 开关按钮
  switch: (args) => {
    const { dataKey, onLabel, offLabel, activeValue, inactiveValue } = args
    const template = () => {
      return {
        template: Vue.component('selectComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <mt-switch
                @change="switchChange"
                v-model="data[dataKey]"
                :on-label="onLabel"
                :off-label="offLabel"
                :active-value="activeValue"
                :inactive-value="inactiveValue"
              ></mt-switch>
            </div>
          </div>`,
          data: function () {
            return {
              data: {},
              dataKey,
              onLabel,
              offLabel,
              activeValue,
              inactiveValue,
              ComponentChangeType
            }
          },
          mounted() {},
          methods: {
            // 修改中的值
            switchChange(e) {
              // 编辑行时改变rowDataTemp中当前的值
              rowDataTemp[rowDataTemp.length - 1][dataKey] = e
            }
          }
        })
      }
    }
    return template
  },
  // 不可编辑的文字显示
  text: (args) => {
    const { dataKey, cellTools, valueConverter } = args
    const template = () => {
      return {
        template: Vue.component('textComponent', {
          template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">{{data[dataKey] | format}}</div>
            <div class="column-tool mt-flex invite-btn" v-if="haveShowCellTool">
              <div v-for="(cellTool, index) in cellTools"
                :key="index" :id="cellTool.id"
                class="template-svg"
                v-if="!cellTool.visibleCondition || cellTool.visibleCondition(data)"
                @click.stop="clickCellTool({tool:cellTool, data})"
                ><MtIcon :name="cellTool.icon" />&nbsp<span class="icon-title">{{cellTool.title}}</span>
              </div>
            </div>
          </div>`,
          data: function () {
            return { data: {}, dataKey, cellTools, haveShowCellTool: false }
          },
          mounted() {
            if (cellTools?.length > 0) {
              for (let i = 0; i < cellTools.length; i++) {
                const cellTool = cellTools[i]
                if (!cellTool.visibleCondition || cellTool.visibleCondition(this.data)) {
                  this.haveShowCellTool = true
                  break
                }
              }
            }
          },
          filters: {
            format: (value) => {
              let data = value
              if (valueConverter?.type === 'map' && valueConverter?.map?.length) {
                // 转换
                const mapList = valueConverter.map
                const findItem = mapList.find((item) => item.value === value)
                data = findItem?.text
              }
              return data
            }
          },
          methods: {
            clickCellTool(data) {
              this.$parent.$emit('handleClickCellTool', data)
            }
          }
        })
      }
    }
    return template
  },
  // 对账人
  reconciliationPersonSelect: () => {
    return {
      template: Vue.component('reconciliationPersonSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.reconciliationPerson"
              :filtering="doGetDataSource"
              :data-source="reconciliationPersonOptions"
              :fields="{ text: 'theCodeName', value: 'employeeName' }"
              :show-clear-button="true"
              :allow-filtering="true"
              @change="reconciliationPersonChange"
              :open-dispatch-change="true"
              :placeholder="$t('请选择')"
            ></mt-select>
          </div>
        </div>`,
        data: function () {
          return {
            data: {},
            reconciliationPersonOptions: [], // 对账人 下拉选项
            doGetDataSource: () => {}
          }
        },
        mounted() {
          this.initGetReconciliationPerson()
          this.doGetDataSource = utils.debounce(this.getReconciliationPerson, 1000)
        },
        methods: {
          // 主数据 获取对账人
          getReconciliationPerson(args) {
            const { text, updateData, setSelectData } = args
            const params = {
              fuzzyName: text
            }
            this.$API.masterData.getCurrentTenantEmployees(params).then((res) => {
              if (res) {
                const list = res?.data || []
                this.reconciliationPersonOptions = addCodeNameKeyInList({
                  // firstKey: 'companyOrgName',
                  // secondKey: 'departmentOrgName',
                  // thirdKey: 'employeeCode',
                  // fourthKey: 'employeeName',
                  firstKey: 'employeeCode',
                  secondKey: 'employeeName',
                  list
                })
                if (updateData) {
                  this.$nextTick(() => {
                    updateData(this.reconciliationPersonOptions)
                  })
                }
                if (setSelectData) {
                  this.$nextTick(() => {
                    setSelectData()
                  })
                }
              }
            })
          },
          // 初始化检索 对账人
          initGetReconciliationPerson() {
            const selectData = this.data.reconciliationPerson
            this.getReconciliationPerson({
              text: selectData,
              setSelectData: () => {
                // api获取数据后重新赋值，防止没有赋上值得情况
                this.data.reconciliationPerson = selectData
              }
            })
          },
          // 对账人 change
          reconciliationPersonChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].reconciliationPerson = itemData.employeeName
              rowDataTemp[rowDataTemp.length - 1].reconciliationPersonCode = itemData.employeeCode
              rowDataTemp[rowDataTemp.length - 1].reconciliationPersonEmail = itemData.email
            } else {
              rowDataTemp[rowDataTemp.length - 1].reconciliationPerson = null
              rowDataTemp[rowDataTemp.length - 1].reconciliationPersonCode = null
              rowDataTemp[rowDataTemp.length - 1].reconciliationPersonEmail = null
            }
          }
        }
      })
    }
  },
  // 供应商
  supplierSelect: () => {
    return {
      template: Vue.component('supplierSelect', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <mt-select
              v-model="data.supplierCode"
              :filtering="doGetDataSource"
              :data-source="supplierOptions"
              :fields="{ text: 'theCodeName', value: 'supplierCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              @change="supplierCodeChange"
              :open-dispatch-change="true"
              :placeholder="$t('请选择')"
            ></mt-select>
          </div>
        </div>`,
        data() {
          return {
            data: {},
            supplierOptions: [], // 供应商 下拉选项
            doGetDataSource: () => {}
          }
        },
        mounted() {
          this.initGetSupplier()
          this.doGetDataSource = utils.debounce(this.getSupplier, 1000)
        },
        methods: {
          // 主数据 获取供应商
          getSupplier(args) {
            const { text, updateData, setSelectData } = args
            const params = {
              fuzzyNameOrCode: text
            }
            this.$API.masterData
              .getSupplier(params)
              .then((res) => {
                if (res) {
                  const list = res?.data || []
                  this.supplierOptions = addCodeNameKeyInList({
                    firstKey: 'supplierCode',
                    secondKey: 'supplierName',
                    list
                  })
                  if (updateData) {
                    this.$nextTick(() => {
                      updateData(this.supplierOptions)
                    })
                  }
                  if (setSelectData) {
                    this.$nextTick(() => {
                      setSelectData()
                    })
                  }
                }
              })
              .catch(() => {})
          },
          // 初始化检索 供应商
          initGetSupplier() {
            const selectData = this.data.supplierCode
            this.getSupplier({
              text: selectData,
              setSelectData: () => {
                // api获取数据后重新赋值，防止没有赋上值得情况
                this.data.supplierCode = selectData
              }
            })
          },
          // 供应商 change
          supplierCodeChange(args) {
            const { itemData } = args
            if (itemData) {
              rowDataTemp[rowDataTemp.length - 1].supplierCode = itemData.supplierCode
              rowDataTemp[rowDataTemp.length - 1].supplierName = itemData.supplierName
            } else {
              rowDataTemp[rowDataTemp.length - 1].supplierCode = null
              rowDataTemp[rowDataTemp.length - 1].supplierName = null
            }
          }
        }
      })
    }
  }
}
