<template>
  <!-- 采方-对账协同-采购对账单列表 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      class="template-height"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.companyCode"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                :placeholder="$t('请选择公司')"
                :fields="{ text: 'orgName', value: 'orgCode' }"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="supplierCodes" :label="$t('供应商/客户')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.supplierCodes"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择供应商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="reconciliationStatus" :label="$t('是否本次对账')" label-style="top">
              <mt-select
                style="flex: 1"
                v-model="searchFormModel.reconciliationStatus"
                :data-source="ReconciliationStatusOptions"
                :show-clear-button="true"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item
              prop="isAssociationSupplier"
              :label="$t('是否关联供应商')"
              label-style="top"
            >
              <mt-select
                style="flex: 1"
                v-model="searchFormModel.isAssociationSupplier"
                :data-source="AssociationSupplierOptions"
                :show-clear-button="true"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="currencyCode" :label="$t('币种编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.currencyCode"
                :show-clear-button="true"
                :placeholder="$t('请输入币种编码')"
              />
            </mt-form-item>
            <mt-form-item
              prop="reconciliationPersonCode"
              :label="$t('对账人编码')"
              label-style="top"
            >
              <mt-input
                v-model="searchFormModel.reconciliationPersonCode"
                :show-clear-button="true"
                :placeholder="$t('请输入对账人编码')"
              />
            </mt-form-item>
            <mt-form-item
              prop="reconciliationPersonEmail"
              :label="$t('对账人邮箱')"
              label-style="top"
            >
              <mt-input
                v-model="searchFormModel.reconciliationPersonEmail"
                :show-clear-button="true"
                :placeholder="$t('请输入对账人邮箱')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import {
  columnData,
  serializeNotifyList,
  EditSettings,
  NotifyToolbar,
  ReconciliationStatusOptions,
  AssociationSupplierOptions,
  RequestType,
  ActionType,
  NewRowData
} from './config/index.js'
import { rowDataTemp } from './config/variable'
import { BASE_TENANT } from '@/utils/constant'
import { cloneDeep } from 'lodash'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      searchFormModel: {},
      ReconciliationStatusOptions,
      AssociationSupplierOptions,
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          toolbar: NotifyToolbar,
          grid: {
            editSettings: EditSettings,
            allowPaging: true, // 分页
            columnData: columnData,
            dataSource: [],
            // 往来对账供应商配置-查询往来对账配置列表
            asyncConfig: {
              url: `${BASE_TENANT}/tv/transactionWait/payableSum`,
              defaultRules: [],
              serializeList: serializeNotifyList
            }
            // frozenColumns: 1,
          }
        }
      ]
    }
  },
  created() {},
  methods: {
    // actionBegin 表格编辑生命周期
    actionBegin(args) {
      const { requestType, action, rowData } = args
      // console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args);
      if (requestType === RequestType.add) {
        // 开始行添加
        this.isEditing = true
        // 即将添加一行，赋值新增行的初始数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        const newRowData = cloneDeep(NewRowData)
        rowDataTemp.push(newRowData)
        args.rowData = newRowData
        args.data = newRowData
      } else if (requestType === RequestType.save && action === ActionType.add) {
        // 即将保存新增时，将新增中编辑的数据付给组件
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === RequestType.beginEdit) {
        // 开始行编辑
        this.isEditing = true
        // 即将编辑行，赋值当前行的数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },
    // actionComplete 表格编辑生命周期
    actionComplete(args) {
      const { requestType, action, rowIndex, index } = args
      const rowData = rowDataTemp[rowDataTemp.length - 1]
      // console.log(
      //   `Complete,\nrequest: ${requestType}\naction: ${action}`,
      //   args
      // );
      if (requestType === RequestType.save && action == ActionType.edit) {
        // 完成编辑行
        this.isEditing = false
        // rowData, rowIndex
        if (!this.isValidSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API
          // 租户级-往来对账通知配置-保存往来对账配置
          this.postTransactionReconciliationSupplierConfigSave({
            rowData,
            rowIndex
          })
        }
      } else if (requestType === RequestType.save && action == ActionType.add) {
        // 完成新增行
        this.isEditing = false
        if (!this.isValidSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API
          // 租户级-往来对账通知配置-保存往来对账配置
          this.postTransactionReconciliationSupplierConfigSave({
            rowData,
            rowIndex: index
          })
        }
      }
    },
    // 校验数据
    isValidSaveData(data) {
      const { companyName, supplierName, reconciliationPerson } = data
      let valid = true
      if (!companyName) {
        this.$toast({ content: this.$t('请选择公司'), type: 'warning' })
        valid = false
      } else if (!supplierName) {
        this.$toast({
          content: this.$t('请选择供应商/客户编号'),
          type: 'warning'
        })
        valid = false
      } else if (!reconciliationPerson) {
        this.$toast({ content: this.$t('请选择对账人'), type: 'warning' })
        valid = false
      }

      return valid
    },
    handleClickToolBar(args) {
      const { toolbar, gridRef } = args
      const selectRows = gridRef.getMtechGridRecords()
      if (selectRows.length === 0 && toolbar.id === 'DeleteNotify') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedIds = []
      selectRows.forEach((item) => {
        selectedIds.push(item.id)
      })
      if (toolbar.id === 'AddNotify') {
        // 对账通知-新增
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id === 'UpdateNotify') {
        // 对账通知-更新 结束编辑
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      } else if (toolbar.id === 'DeleteNotify') {
        // 对账通知-删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            this.handleDeleteNotify(selectedIds)
          }
        })
      } else if (toolbar.id === 'SendNotify') {
        // 对账通知-发送通知
        this.handleSendNotify({
          ids: selectedIds
        })
      } else if (toolbar.id === 'ImportNotify') {
        // 对账通知-导入
        this.handleUploadNotify()
      } else if (toolbar.id === 'ExportNotify') {
        // 对账通知-导出
        this.handleExport()
      }
    },
    // 租户级-往来对账通知配置-删除往来对账通知
    handleDeleteNotify(params) {
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationSupplierConfigDelete(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-往来对账通知配置-发送往来对账通知
    handleSendNotify(params) {
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationSupplierConfigSendNotice(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleUploadNotify() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi:
            this.$API.reconciliationSettlement.postTransactionReconciliationSupplierConfigUpload,
          downloadTemplateApi:
            this.$API.reconciliationSettlement.postTransactionReconciliationSupplierConfigDownload,
          paramsKey: 'excel',
          // asyncParams: {
          //   // requestJson: JSON.stringify(parameter),
          // },
          downloadTemplateParams: {
            pageFlag: false
          }
        },
        success: () => {
          // 导入之后刷新列表
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 导出
    handleExport() {
      let params = {
        ...this.searchFormModel,
        page: { current: 1, size: 10000 }
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement.reconciliationTransactionDownload(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 租户级-往来对账通知配置-保存往来对账配置
    postTransactionReconciliationSupplierConfigSave(args) {
      const { rowData, rowIndex } = args
      const params = [
        {
          ...rowData,
          thePrimaryKey: undefined
        }
      ]
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationSupplierConfigSave(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style></style>
