import { i18n } from '@/main.js'

export const settleAccountsStatusOptions = [
  { text: i18n.t('未清'), value: '0' },
  { text: i18n.t('已清'), value: '1' }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'companyCode',
    title: i18n.t('公司'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.companyName : ''
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    }
  },
  {
    field: 'docNo',
    title: i18n.t('单据号')
  },
  {
    field: 'lineNo',
    title: i18n.t('行号')
  },
  {
    field: 'accountTypeName',
    title: i18n.t('科目类别')
  },
  {
    field: 'reconciliationAccount',
    title: i18n.t('科目编号')
  },
  {
    field: 'reconciliationAccountName',
    title: i18n.t('科目')
  },
  {
    field: 'accountDate',
    title: i18n.t('入账日期'),
    minWidth: 160
  },
  {
    field: 'amount',
    title: i18n.t('金额')
  },
  {
    field: 'currencyCode',
    title: i18n.t('币种'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.currencyName : ''
    }
  },
  {
    field: 'profitCenter',
    title: i18n.t('利润中心')
  },
  {
    field: 'settleAccountsStatus',
    title: i18n.t('清账状态'),
    formatter: ({ cellValue }) => {
      let item = settleAccountsStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'settleAccountsDate',
    title: i18n.t('清账日期')
  },
  {
    field: 'liquidationDocNo',
    title: i18n.t('清账凭证')
  },
  {
    field: 'liquidationYear',
    title: i18n.t('清算会计年度')
  },
  {
    field: 'invoiceNo',
    title: i18n.t('发票号')
  }
]
