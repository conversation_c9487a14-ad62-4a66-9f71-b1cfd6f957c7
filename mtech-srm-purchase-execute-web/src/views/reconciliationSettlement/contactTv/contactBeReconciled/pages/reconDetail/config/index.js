import { i18n } from '@/main.js'
import UTILS from '@/utils/utils'
import { codeNameColumn } from '@/utils/utils'

// 添加 操作日期 字段到列表中
export const addTheOperationDateToList = (list) => {
  if (list.length > 0) {
    // 操作日期 前端自定义 updateTime更新时间为空时显示createTime创建时间
    list.forEach((item) => {
      let theOperationDate = item.createTime
      if (!item.updateTime || item.updateTime != 0) {
        // 数据库时间戳默认值为 0，为 0 时即为空
        theOperationDate = item.updateTime
      }
      item.theOperationDate = theOperationDate
    })
  }
  return list
}

export const statusOptions = [
  { value: 1, text: i18n.t('待确认'), cssClass: 'col-abnormal' },
  { value: 2, text: i18n.t('已确认'), cssClass: 'col-active' },
  { value: 3, text: i18n.t('已退回'), cssClass: 'col-normal' },
  { value: 4, text: i18n.t('已作废'), cssClass: 'col-published' }
]

// 往来类别
export const ContactsType = {
  bzj: 3, // 保证金
  xsfp: 4, // 开发票给TCl
  kk: 5, // 扣款
  sk: 6, // 收款
  kfp: 7, // 收到TCL开具的发票
  other: 9 // 其他
}

// 往来类别 text
export const ContactsTypeText = {
  [ContactsType.bzj]: i18n.t('保证金'),
  [ContactsType.xsfp]: i18n.t('开发票给TCL'),
  [ContactsType.kk]: i18n.t('扣款'),
  [ContactsType.sk]: i18n.t('收款'),
  [ContactsType.kfp]: i18n.t('收到TCL开具的发票'),
  [ContactsType.other]: i18n.t('其他')
}

// 往来类别 对应的 css class
export const ContactsTypeCssClass = {
  [ContactsType.bzj]: '',
  [ContactsType.xsfp]: '',
  [ContactsType.kk]: '',
  [ContactsType.sk]: '',
  [ContactsType.kfp]: '',
  [ContactsType.other]: ''
}

// 往来类别 对应的 Options
export const ContactsTypeOptions = [
  {
    // 保证金
    value: ContactsType.bzj,
    text: ContactsTypeText[ContactsType.bzj],
    cssClass: ContactsTypeCssClass[ContactsType.bzj]
  },
  {
    // 开发票给TCL
    value: ContactsType.xsfp,
    text: ContactsTypeText[ContactsType.xsfp],
    cssClass: ContactsTypeCssClass[ContactsType.xsfp]
  },
  {
    // 扣款
    value: ContactsType.kk,
    text: ContactsTypeText[ContactsType.kk],
    cssClass: ContactsTypeCssClass[ContactsType.kk]
  },
  {
    // 收款
    value: ContactsType.sk,
    text: ContactsTypeText[ContactsType.sk],
    cssClass: ContactsTypeCssClass[ContactsType.sk]
  },
  {
    // 收到TCL开具的发票
    value: ContactsType.kfp,
    text: ContactsTypeText[ContactsType.kfp],
    cssClass: ContactsTypeCssClass[ContactsType.kfp]
  },
  {
    // 其他
    value: ContactsType.other,
    text: ContactsTypeText[ContactsType.other],
    cssClass: ContactsTypeCssClass[ContactsType.other]
  }
]

// 往来待对账明细 表格列数据
export const columnData = [
  {
    field: 'companyCode', // 公司
    headerText: i18n.t('公司'),
    width: '300',
    template: codeNameColumn({
      firstKey: 'companyCode',
      secondKey: 'companyName'
    })
    // companyName
  },
  {
    field: 'supplierCode', // 供应商
    headerText: i18n.t('供应商'),
    width: '400',
    template: codeNameColumn({
      firstKey: 'supplierCode',
      secondKey: 'supplierName'
    })
    // supplierName
  },
  {
    field: 'type', // 往来类别 往来类别 0-对账；1-索赔；2-返利
    headerText: i18n.t('往来类别'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: ContactsTypeOptions
    }
  },
  {
    field: 'docNo', // 单据号
    headerText: i18n.t('单据号'),
    width: '150'
  },
  {
    field: 'accountTypeName', // 科目类别
    headerText: i18n.t('科目类别'),
    width: '150'
    // accountTypeCode	科目类别编码
  },
  {
    field: 'reconciliationAccount', // 科目编号 对账科目
    headerText: i18n.t('科目编号'),
    width: '150'
  },
  {
    field: 'reconciliationAccountName', // 科目 对账科目名称
    headerText: i18n.t('科目'),
    width: '150'
  },
  {
    field: 'accountDate', // 入账日期
    headerText: i18n.t('入账日期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return e
        }
      }
    }
  },
  {
    field: 'invoiceNo', // 发票号
    headerText: i18n.t('发票号'),
    width: '150'
  },
  {
    field: 'amount', // 金额
    headerText: i18n.t('金额'),
    width: '150'
  },
  {
    field: 'currencyName', // 币种
    headerText: i18n.t('币种'),
    width: '150'
    // 币种编码 currencyCode
  },
  {
    field: 'theOperationDate', // 操作日期 前端自定义 updateTime更新时间为空时显示createTime创建时间
    headerText: i18n.t('操作日期'),
    width: '150'
  }
  // {
  //   field: "cleanStatus", // 清账状态 0未清 1已清
  //   headerText: i18n.t("清账状态"),
  // },
]
